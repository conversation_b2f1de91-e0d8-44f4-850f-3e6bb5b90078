//参考书http://www.w3school.com.cn/jquery/jquery_hide_show.asp

//根据相对url获取绝对url地址
var getAbsoluteUrl = (function () {
    var a;
    return function (url) {
        if (!a) a = document.createElement("a");
        a.href = url;
        return a.href;
    }
})();
//usage
getAbsoluteUrl("/something");

//获取当前js文件引用的url地址
var getFilePath = function () {
    var e = document.scripts,
        t = e[e.length - 1],
        n = t.src;
    if (!t.getAttribute("merge")) return n.substring(0, n.lastIndexOf("/") + 1)
};
//usage
getFilePath();

//js关闭当前页面
window.opener = null;
window.open("", "_self");
window.close();

//S获取前一个访问页面的URL地址
//返回上一页，可以使用history.go(-1)或者history.back()
//location.reload()刷新（location.href或者location.replace()刷新有信息）；
document.referrer;
if (document.referrer === '') {
    // 没有来源页面信息的时候，改成首页URL地址
    $('.jsBack').attr('href', '/');
}

//$.ajax提交表单
$.ajax({
    type: 'post',
    url: "/plus/diy.php",
    dataType: "html",
    data: {
        "action": "post",
        "diyid": "1",
        "do": "2",
        "dede_fields": "txtname,text;txtnum,text;txtphone,text",
        "dede_fieldshash": "22ed4c5a35db93fc48b65d20eaf917d3",
        "txtname": order_name,
        "txtnum": order_num,
        "txtphone": order_phone,
    },
    success: function (data) {
        layer.alert("报名成功！", {
            skin: skin_1
            , title: "提示"
            , shade: 0.8
            , closeBtn: 0
            , anim: 0,
            yes: function () {
                layer.closeAll();
            }
        });
    }
});

//$.get(URL,data,function(data,status,xhr),dataType)方式获取页面内容
$.get("/plus/view.php?aid=1", function (data) {
    alert(data)
});
//或
$.get("test_get.aspx", { name: "John", time: "2pm" }, function (data, status) {
    alert("数据：" + data + "\n状态：" + status);
});

//$.post获取页面内容
$.post("test_post.asp", { name: "Donald Duck", city: "Duckburg" }, function (data, status) {
    alert("数据：" + data + "\n状态：" + status);
});

//jQuery AJAX 方法列表 http://www.runoob.com/jquery/jquery-ref-ajax.html
$.ajax()	        //执行异步 AJAX 请求
$.ajaxPrefilter()	//在每个请求发送之前且被 $.ajax() 处理之前，处理自定义 Ajax 选项或修改已存在选项
$.ajaxSetup()	    //为将来的 AJAX 请求设置默认值
$.ajaxTransport()	//创建处理 Ajax 数据实际传送的对象
$.get()	            //使用 AJAX 的 HTTP GET 请求从服务器加载数据
$.getJSON()	        //使用 HTTP GET 请求从服务器加载 JSON 编码的数据
$.getScript()	    //使用 AJAX 的 HTTP GET 请求从服务器加载并执行 JavaScript
$.param()	        //创建数组或对象的序列化表示形式（可用于 AJAX 请求的 URL 查询字符串）
$.post()	        //使用 AJAX 的 HTTP POST 请求从服务器加载数据
ajaxComplete()	    //规定 AJAX 请求完成时运行的函数
ajaxError()	        //规定 AJAX 请求失败时运行的函数
ajaxSend()	        //规定 AJAX 请求发送之前运行的函数
ajaxStart()	        //规定第一个 AJAX 请求开始时运行的函数
ajaxStop()	        //规定所有的 AJAX 请求完成时运行的函数
ajaxSuccess()	    //规定 AJAX 请求成功完成时运行的函数
load()	            //从服务器加载数据，并把返回的数据放置到指定的元素中 $("#div1").load("demo_test.txt");
serialize()	        //输出序列化表单元素集为字符串以便提交
serializeArray()	//输出序列化表单元素集为 names 和 values 的数组


//jquery获取当前页面的URL信息
//例：http://localhost:8086/topic/index?topicId=361#text
window.location.pathname        //则输出：/topic/index
window.location.href            //则输出：http://localhost:8086/topic/index?topicId=361#text
window.location.port            //则输出：8086
window.location.protocol        //则输出：http:
window.location.hash            //则输出：text
window.location.host            //则输出：http:localhost:8086 
window.location.search          //则输出：?topicId =361#text