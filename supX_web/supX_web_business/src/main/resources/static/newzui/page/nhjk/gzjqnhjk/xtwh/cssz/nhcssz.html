<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>参数设置</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link href="ybcssz.css" rel="stylesheet">
</head>

<body class="skin-default flex-container flex-dir-c flex-one">
<div class="ybhzgl-box background-f flex-container flex-dir-c flex-one padd-l-10 padd-r-10" >
    <div class="jbxx  margin-t-30">
        <div class="jbxx-size">
            <div class="jbxx-position">
                <span class="jbxx-top"></span>
                <span class="jbxx-text">参数设置</span>
                <span class="jbxx-bottom"></span>
            </div>
            <div class="jbxx-box flex-start padd-t-10 padd-b-10 padd-l-10 padd-r-10">
                <div class="top-form margin-b-10 flex-start margin-r-20">
                    <label class="top-label ">服&ensp;务&ensp;器IP&ensp;地&ensp;址</label>
                    <div class="top-zinle flex-start">
                        <input type="text" class="zui-input wh182 background-f" v-model="popContent.host"/>
                    </div>

                </div>
                <div class="top-form margin-b-10 flex-start margin-r-20">
                    <label class="top-label ">端&ensp;&ensp;&ensp;&ensp;口</label>
                    <div class="top-zinle">
                        <input type="number" class="zui-input wh182 background-f" v-model="popContent.port"/>
                    </div>
                </div>
                <div class="top-form margin-b-10 flex-start margin-r-20">
                    <label class="top-label ">用户名</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182 background-f" v-model="popContent.userName" />
                    </div>
                </div>
                <div class="top-form margin-b-10 flex-start margin-r-20">
                    <label class="top-label ">用户密码</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182 background-f" v-model="popContent.userPwd"/>
                    </div>
                </div>
                <div class="top-form margin-b-10 flex-start margin-r-20">
                    <label class="top-label ">农&ensp;合&ensp;中心&ensp;编&ensp;码</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182 background-f" v-model="popContent.centerNo" />
                    </div>
                </div>
                <div class="top-form margin-b-10 flex-start margin-r-20">
                    <label class="top-label ">农&ensp;合&ensp;机构&ensp;编&ensp;码</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182 background-f" v-model="popContent.hospCode"/>
                    </div>
                </div>
                <div class="top-form margin-b-10 flex-start margin-r-20">
                    <label class="top-label ">四舍五入上传项目:</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182 background-f" v-model="popContent.sswrxm"
                               @keydown="changeDown($event,'sswrxm','csszContent','searchCon')"
                               @input="change(false,'sswrxm',$event.target.value)" />
                        <search-table :message="searchCon" :selected="selSearch"
                                          :them="them" :page="page"
                                          @click-one="checkedOneOut" @click-two="selectOne">
                        </search-table>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="tong-top background-f border-none flex-end">
        <button class="tong-btn btn-parmary" @click="save">保存</button>
        <button class="tong-btn btn-parmary-b" @click="cancel">取消</button>
    </div>

</div>
<script src="nhcssz.js"></script>
<style type="text/css">
    .ybhzgl-box{
        width: 100%;
    }
    .top-form .top-label{
        width: 60px;
    }
    .border-none{
        border-bottom: none;
    }
</style>
</body>

</html>