(function(){
    //病理诊断
    var tableInfo = new Vue({
        el: '#jbZy',
        mixins: [dic_transform, baseFunc, tableBase, mformat,checkData],
        data:{
        	popContent: {},
            jsonList: [],
        },
        methods: {
        	//进入页面加载列表信息
            getData: function () {
            	this.param.sort='jbbm';
            	if($("#jbZyjsvalue").val()!=null&&$("#jbZyjsvalue").val()!=''){
			        this.param.parm=$("#jbZyjsvalue").val();
			    }else{
			        this.param.parm='';
			    }
                tableInfo.jsonList=[];
        		$.getJSON("/actionDispatcher.do?reqUrl=BaglBmwhZy&types=queryBaglJbbmZy&parm="+JSON.stringify(this.param),function (json) {
        			tableInfo.totlePage = Math.ceil(json.d.total/tableInfo.param.rows);
        			tableInfo.jsonList = json.d.list;
        		});
            },
            //检索查询回车键
		    searchHc: function() {
		        if(window.event.keyCode == 13) {
                    tableInfo.getData();
		        }
		    },


            //修改值域类别
            edit: function (num) {
                wap.open();
                wap.title='编辑';
                //这里要拷贝值到popContent中，不能直接=
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            },

            //删除值域类别
            remove: function () {
                var jbZyList = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                    	var jbZy={};
                    	var removeUrl="/actionDispatcher.do?reqUrl=BaglBmwhZy&types=delete&";
                    	jbZy.jbbm = this.jsonList[i].jbbm
                    	jbZyList.push(jbZy);
                    }
                }
                if(jbZyList.length == 0){
                    malert("请选中您要删除的数据");
                    return false;
                }
                var json='{"list":'+JSON.stringify(jbZyList)+'}';
                this.$http.post(removeUrl,json).then( function (data) {
                    this.getData();
                    if(data.body.a == 0){
                        malert("删除成功")
                    } else {
                        malert("删除失败")
                    }
                }, function (error) {
                    console.log(error);
                });
            },
            //新增清空编辑区
            addData: function(){
                wap.open();
                wap.title='新增';
            	wap.popContent={};
            }
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            nums:1,
            title:'',
            popContent:{}

        },
        methods:{
            //关闭
            closes:function () {
                // brzcList.hzShow=false;
                wap.nums=1;
            },
            open: function () {
                wap.nums=0;
            },

            //保存值域类别
            saveData: function () {
                // 提交前验证数据（主要是非空）
                if(wap.popContent.jbbm=='' || wap.popContent.jbbm==null || wap.popContent.jbbm==undefined){
                    malert('疾病编码不能为空','top','defeadted');
                    return false;
                }if(wap.popContent.jbmc=='' || wap.popContent.jbmc==null || wap.popContent.jbmc==undefined){
                    malert('疾病名称不能为空','top','defeadted');
                    return false;
                }
                var json=JSON.stringify(wap.popContent);
                this.$http.post("/actionDispatcher.do?reqUrl=BaglBmwhZy&types=save&",json).then(function (data) {
                    if(data.body.a == 0){
                        tableInfo.getData();
                        if(wap.title=='编辑'){
                            wap.closes();
                            malert("保存成功");
                            return;
                        }if(wap.title=='新增'){
                            malert("新增成功");
                        }
                        this.popContent = {};
                    } else {
                        malert("上传数据失败");
                    }
                },function (error) {
                    console.log(error);
                });
            },
        }


    });
    tableInfo.getData();


    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function () {
        if ($(this).val() == '' || $(this).val() == null) {
            $(this).addClass("emptyError");
        } else {
            $(this).removeClass("emptyError");
        }
    });

    //为table循环添加拖拉的div
    var drawWidthNumJbzy = $(".patientTableJbzy tr").eq(0).find("th").length;
    for(var i=0;i<drawWidthNumJbzy;i++){
        if(i>=2){
            $(".patientTableJbzy th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
        }
    }
})();
