@color1a:#1abc9c;
@colorff:#fff;
@color7f8:#7f8fa4;
@color75:#757c83;
@colore7:#e7eaee;
@font14:14px;

*{
  box-sizing: border-box;
}
body{
}
.body-box{
  width: 100%;
  border:1px solid #eeeeee;
  margin: 10px 0;
  height: 100%;
  padding: 0 10px;
}
.tong-top{
  width: 100%;
  height:52px;
  background:#fafafa;
  border:1px solid #eeeeee;
  border-right: none;
  border-left: none;
  padding: 10px 0 10px 12px;
  box-sizing: border-box;
}
.tong-btn{
  width: auto;
  min-width: 72px;
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border:none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right:8px;
}
.btn-parmary{
  background:@color1a;
  color:@colorff;
  position: relative;
}
.btn-parmary-b{
  border: 1px solid @color1a;
  color: @color1a;
  position: relative;
}
.paddr-r5:before{
  padding-right: 5px;
}

.tong-search{
  width: 100%;
   padding: 12px 0 4px 19px;
  background: #fff;
  min-width: 1366px;
  .zui-form-label{
    width: auto;
  }
  .zui-form .zui-inline{
    margin: 0 18px 0 0;
    padding:0 0 8px 65px;
  }
  .zui-date .datenox{
    left:10px;
    color:#c5d0de;
  }
  .zui-input{
    border-radius: 4px;
    height: 36px;
  }
  .zui-select-group{
    border: none;
    box-shadow:0 0 8px 0 rgba(0,0,0,0.19);
    border-radius:4px;
  }
  .zui-select-group ul.inner li{
    line-height: 40px;
    padding: 0 12px;
    color:@color75;
    font-size: @font14;
    border-bottom: 1px solid @colore7;
    &:nth-child(2n){
      background:rgba(26,188,156,0.08);
    }
  }
  .zui-select-group ul.inner li.active{
    background: @color1a;
    color: @colorff;
  }
  .padd-l24{
    padding-left: 24px;
  }
  .padd-l33{
    padding-left: 33px;
  }
  .margin-l13{
    margin-left: 13px;
  }
 .zui-form .margin-l14{
    margin-left: 14px !important;
  }
  .zui-form-label{
    color: @color7f8;
    padding: 8px 0;
  }
  .padd-l14{
    padding-left: 14px;
  }
}

.wh240{
  width: 240px;
}
.wh182{
  width: 182px;
}

/**下拉*/
.zui-select-inline {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.zui-select-group {
   display: none;
 }
::placeholder{
  color: rgba(53,64,82,.3) !important;
}

/****/
.box-size{
  padding: 0 10px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 107px;
  background: #fff;

}
.box-fixed{
  position: fixed;
  top:6px;
  z-index: 999;
  right: 10px;
  left: 10px;
}
.zui-select-group ul.inner li:hover{
  background:#1abc9c !important;
  color: #fff !important;
}


.zui-table-view .zui-table-body{
  height:71vh;
  border-bottom: 1px solid #e7eaee;
  .zui-select-group{
    border: none;
  }
  .zui-select-group ul.inner li{
    line-height: 28px;
    color:#757c83;
    font-size: 14px;
    border-bottom: 1px solid #e7eaee;
    &:nth-last-child{
      border-bottom: none;
    }
  }
  .zui-select-group ul.inner li.active{
    color: #fff;
  }
  .zui-select-group ul.inner{
    border-radius: 4px;
    box-shadow: 0 0 8px 0 rgba(0,0,0,0.19);
    background: #fff;
    &:before,&:after{
      content: "";
      display: block;
      position: absolute;
      top: -30px;
      left: 10px;
      font-size: 0;
      line-height: 0;
      transform: rotate(180deg);
      border-width: 15px;
      border-style: solid dashed dashed;
      border-color: #fff transparent transparent;
    }
  }
  .group-down{
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right:10px solid transparent;
    border-bottom:20px solid #fff;
    position: absolute;
    top: -7px;
    left: 11px;
    z-index: -1;
    box-shadow: 0 0 8px 0 rgba(0,0,0,0.19);
  }
}
.zui-table-view .zui-table-body .zui-table{
 float: left;

}

.zui-table-view .zui-table-body::-webkit-scrollbar-thumb{
  background: none ;
}
.zui-table-view .zui-table-body::-webkit-scrollbar-thumb:hover{
  background: rgba(0,0,0,.5);
}
.zui-table-view table th,.zui-table-view table td{
  border-left: none;
}
.zui-table-view .zui-table-header,.zui-table-view .zui-table-body{
//display: flex;
//  justify-content: space-between;
//  align-items: center;
  width: 100%;
}
.zui-table{
  width: 100%;
}
.zui-table-view .zui-table-header th,.zui-table-view .zui-table-body td{
  width: auto;

}
.zui-table-view .zui-table-header table{
  background:#edf2f1;
}
.zui-table-view .zui-table-body tr td {
  padding: 12px 0;
  font-size: 14px;
  color:#757c83;
}
.zui-table-view table tr{
  //border-left: 1px solid #eee;
  position: relative;
}
.zui-table-view{
  height: 100% !important;
  //border: none !important;
}
.zui-table-view .zui-table-body tr:first-child{
  border-top: 1px solid #eee;
  &:hover{
    //border-top: 1px solid #1abc9c;
  }
}
.zui-table-body table tr{
  position: relative;
}
.zui-table-body table tr:hover{
  background:rgba(26,188,156,0.08) !important;
  //border: 1px solid #1abc9c;
  //box-shadow:0 0 6px 0 rgba(26,188,156,0.45);
}
.md-more-vert:before{
  content: "\e93d";
  color: #818883;
}
.zui-table-view .fieldlist .field_btn:before{
  content: "\e93d";
}
.color-1a{
  color: @color1a !important;
}
.zui-table-body table tr:hover .zui-table-view .zui-table-fixed table tr{
  background:rgba(26,188,156,0.08) !important;
  //border-top: 1px solid #1abc9c;
  //border-bottom: 1px solid #1abc9c;
  //border-left: 1px solid #1abc9c;
  box-shadow:0 0 6px 0 rgba(26,188,156,0.45);
  &.zui-table-view .zui-table-fixed table tr{
    background:rgba(26,188,156,0.08) !important;
    //border-top: 1px solid #1abc9c;
    //border-bottom: 1px solid #1abc9c;
    //border-left: 1px solid #1abc9c;
    //box-shadow:0 0 6px 0 rgba(26,188,156,0.45);
  }
}
.table-hover{
  background:rgba(26,188,156,0.08) !important;
}

.tong-inp{
  background: none;
  border: none;
  color:#757c83;
  width: auto;
  max-width: 90px;
  text-align: center;
}
.disableds{
  width: auto;
  background:#ffffff;
  border:1px solid #1abc9c;
  box-shadow:0 0 6px 0 rgba(26,188,156,0.43);
  border-radius:4px;
  height: 34px;
  max-width: 90px;
}
.zui-table-view .zui-table-cell{
  text-align: center;
  padding: 0;
}
.dot-position{
  position: absolute;
  width: 10px;
  top:0;
  display: none;
  height:36px;
  right: 12px;
}

.dot-top {
  font-size: 0;
  line-height: 0;
  border-width:4px;
  border-color:#757c83;
  border-top-width: 0;
  border-style: dashed;
  border-bottom-style: solid;
  border-left-color: transparent;
  border-right-color: transparent;
  top:-20px;
}
/* 向下的箭头 */
.dot-bottom {
  font-size: 0;
  line-height: 0;
  border-width:4px;
  border-color:#757c83;
  border-bottom-width: 0;
  border-style: dashed;
  border-top-style: solid;
  border-left-color: transparent;
  border-right-color: transparent;
  top:19px;
}
.zui-table-view .zui-table-tool{
  background: #fff !important;
  height: 87px !important;
  line-height: 87px;
  border-top: none !important;
  width: inherit !important;
  .page-count{
    font-size:14px;
    color:#8a94a0;

  }
  .page-limits{
    width: 72px;
    position: relative;
    margin-top: -5px;
  select{
    width: 72px;
    height: 34px;
    border: 1px solid #e9eee6;
    -webkit-appearance: none;
    border-radius: 4px;
    padding: 0;
    text-indent: 5px;
    color:#354052;
  }
    .dot-bottom{
      right: 2px;
      position: absolute;
      top: 16px;
    }
  }
}
.zui-table-page{
  width: 100%;
  padding: 0 28px;
  box-sizing: border-box;
}
.zui-table-view .zui-table-tool .zui-table-page{
  margin: 0 !important;
}
.zui-table-page{
  height: auto !important;
}
.zui-table-page .disabled,.zui-table-page .dis-next{
  background-image:linear-gradient(0deg, #f2f4f7 0%, #ffffff 100%);
  border-right:1px solid #dfe3e9;
  color: #313131 !important;

}
.zui-table-page .border-r{
  border-right: none !important;
}
.zui-table-page a{
  height: 34px;
  line-height: 34px;
  margin: 0 !important;
  width: 36px;
}
.page-right{
  max-width: 600px;
  float: right;
  border-radius:4px;
  border: 1px solid #dfe3e9;
  display: flex;
  align-items: center;
  margin-right: 2px;
  a{
    height: 36px;
    width: 36px;
    line-height: 36px;
    text-align: center;
    border-right: 1px solid #dfe3e9;
    &:hover{
      background: #1abc9c;
      color: #fff;
    }
  }
  .page-prev:hover{
background: none;
  }
  .page-next:hover{
    background: none;
  }
}
.zui-table-page .page-curr{
  width: 36px;
  height: 36px;
  line-height: 36px;
  display: block;
  text-align: center;
  margin: 0 !important;
}
.zui-table-page .jump{
  width: 80px;
  height: 37px;
  border: 1px solid #dfe3e9;
  text-align: center;
  background: none;
  float: right;
  margin-right:2px;
}
.zui-table-page .jump-btn{
  background:#1abc9c;
  border-radius:4px;
  width:88px;
  height:37px;
  color: #fff;
  float: right;
}

.select-after:after{
  top: 37%;
  right: 7px;
}

.table-hovers{
  background:rgba(26,188,156,0.08) !important;
  //background: #000;
  border: 1px solid #1abc9c !important;
  box-shadow:0 0 6px 0 rgba(26,188,156,0.45);
}
.zui-table-tool{
  position: fixed;
  right: 10px;
  left: 10px;
  background: #fff;
  bottom: 10px;
  z-index: 9999;
}
.color-green{
  color: #1abc9c !important;
  font-style: normal;
}
.zui-table-view .zui-table-header .zui-table-cell span{
  color: #333;
}

.zui-table-view .zui-table-body::-webkit-scrollbar{
  width: 0;

}
.zui-table-view .zui-table-fixed.table-fixed-r{
  right: 0px !important;
}
.zui-table-view .zui-table-body::-webkit-scrollbar-track{
  background: #fff;
}
.zui-table-view table tr:nth-child(2n){
  background:#fdfdfd;
}