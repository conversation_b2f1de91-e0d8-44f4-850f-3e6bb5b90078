.brjz{
    height: 100%;
}

.searchLeft_tab1 > div {
    padding: 4px;
}

.personList input[type = checkbox] {
    width: 30px;
    float: left;
}

.personList > div:nth-child(n + 2) {
    height: 50px;
}

.right{
    height: 100%;
}

#brxxList {

}

.contextDiv {
    height: 100%;
    margin: 10px 10px 0 10px;
}

.nh-menu > span {
    display: block;
    float: left;
    background-color: #FFFFFF;
    width: 100px;
    padding: 10px 0;
    border: 1px solid #aaaaaa;
    border-right: 0;
    text-align: center;
    margin-bottom: -1px;
    cursor: pointer;
}

.context{
    background-color: #FFFFFF;
    width: 100%;
    height: 100%;
    border: 1px solid #aaaaaa;
}

.nh-menu{
    display: inline-block;
    margin-bottom: -3px;
    width: 100%;
}

.nh-screen{
    float: right;
    margin-right: 20px;
    margin-top: 10px;
}

.nh-screen div{
    float: left;
    margin-left: 10px;
}

.selected{
    border-bottom: 1px solid #FFFFFF !important;
}

.infoIpt p{
    width: 116px;
}

.infoIpt input{
    width: calc(100% - 124px);
}

.conTit{
    margin-bottom: 20px;
}

.place input{
    border: 1px solid #CCCCCC;
}

.page_div{
    height: calc(100% - 8px);
    overflow: scroll;
}

#scnh{
    height: calc(100% - 40px);
}

.patientTable tbody tr:first-child th{
    height: 22px;
}

#cwxx tbody tr:first-child th{
    height: 20px;
}

.patientTable th:nth-child(n+3){
    min-width: 110px;
}

.tableDiv{
    width: 98%;
    height: calc(100% - 52px) !important;
}

.patientTable th{
    padding: 6px;
}

.nh-total{
    padding: 4px 10px;
}

.nh-total > div{
    float: left;
    font-size: 16px;
    margin-right: 20px;
}

.pageDiv{
    width: calc(100% - 260px);
}

.ys{
    display: inline-block;
    background-color: #EEEEEE;
    padding: 20px 0;
    margin-left: 10px;
    margin-top: 20px;
    width: 95%;
    margin-bottom: 10px;
}

.ys input{
    width: 41%;
}

#wscjl, #yjs, #cwxx{
    width: calc(100% - 10px);
    height: 100%;
}

.patientTable th:nth-child(2){
    min-width: 20px;
}

.toolMenu{
    display: block;
}

.hc_input{
    height: 24px;
}

#hyjl_pop tbody tr:first-child th{
    height: 20px;
}

#hyjl_pop .tableDiv{
    margin-top: 10px;
    height: calc(100% - 150px);
}

.infoIpt input[type=checkbox]{
    width: 14px;
    margin-left: 0;
}

.selectInput{
    width: 43% !important;
}

.selectInput input{
    width: 100%;
    height: 28px;
}

.selectInput ul{
    top: 30px;
}

.selectGroup{
    top: 36px;
}

.personInfo, .ryInfo{
    display: inline-block;
}

.djxx button{
    margin: 20px 0;
}