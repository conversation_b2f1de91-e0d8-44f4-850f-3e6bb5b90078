<div id="jbbm">
    <div class="flex-container  padd-b-10">
        <button class="tong-btn btn-parmary" @click="getData"><span class="fa fa-refresh"></span>读取</button>
        <button class="tong-btn btn-parmary" @click="downData"><span class="fa fa-plus"></span>下载编码</button>
    </div>
    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m">序号</div></th>
                    <th><div class="zui-table-cell cell-s">疾病编码</div></th>
                    <th><div class="zui-table-cell cell-s">名称</div></th>
                    <th><div class="zui-table-cell cell-s">代码</div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one over-auto"   @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                    <td class="cell-m"><div  class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.bm}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.mc}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.dm}}</div></td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
<script type="application/javascript" src="jbbm.js"></script>