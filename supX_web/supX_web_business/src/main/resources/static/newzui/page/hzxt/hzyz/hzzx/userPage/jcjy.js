var bgdList = new Vue({
    el: '#bgdList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        djList: [],
    },
    mounted: function () {
        this.getData()
    },
    updated:function(){
        changeWin()
    },
    methods: {
        getData() {
            this.djList = [];
            this.getJyData();
            this.getJcData();
        },
        // 查询检验列表
        getJyData: function () {
            //后台查询数据
            var parm = {
                bah: userNameBg.Brxx_List.zyh,
            };
            this.$http.get("/actionDispatcher.do",{params:{reqUrl:'YzPacsNew',types:'yzpacs_bgcx',parm:JSON.stringify(parm)}}).then(function (data) {
                if (data.body.a == 0) {
                    if (data.body.d.list != null && data.body.d.list.length > 0) {
                        bgdList.djList = bgdList.djList.concat(data.body.d.list);
                    }
                } else {
                    malert("检验报告单列表查询失败" + data.body.c, "top", "defeadted");
                }
            });
        },
        // 查询检查列表
        getJcData: function () {
            var pram = {
                INPATIENTNO: userNameBg.Brxx_List.zyh
            },
                _url = "/actionDispatcher.do";
            this.$http.get(_url,{params:{reqUrl:'YzPacsNew',types:'yzpacs_queryReport',parm:JSON.stringify(pram)}}).then(function (data) {
                if (data.body.a == 0) {
                    if (data.body.d != null && data.body.d.list.length > 0) {
                        var _list = data.body.d.list;
                        for (var i=0;i<_list.length;i++) {
                            _list[i].fymc = _list[i].convertHtml;//"检查报告单" + (Number(i) + 1);
                            _list[i].ifJcbgd = true;
                            bgdList.djList.push(_list[i]);
                        }
                    }
                } else {
                    malert("检查报告单列表查询失败" + data.body.c, "top", "defeadted");
                }
            });
        },
        showDj: function (index) {
            if (this.djList[index].ifJcbgd) { // 检查
                var param = {
                    src: this.djList[index].html,
                    ptnid: this.djList[index].ptnid,
                };
                jybgd.close();
                jcbgd.open(param);
            } else { // 检验
                jcbgd.close();
                jybgd.open(this.djList[index]);
            }
        }
    }
});

var jcbgd = new Vue({
    el: '#jcbgd',
    data: {
        ifShow: false,
        serveIp: '***********',
        ptnid: "",
        iframeSrc: "",
    },
    computed: {
        bgdImageHref: function () {
            //return "http://" + this.serveIp + "/DicomWeb/DicomWeb.dll/OpenImage?User=1&Password=1&PTNID=" + this.ptnid;
            return "http://" + this.serveIp + "/DicomWeb";
        }
    },
    methods: {
        open: function (param) {
            if (!param.ptnid) console.error("检查报告单的pinid错误！请检查。");
            else this.ptnid = param.ptnid;

            if (!param.src) console.error("检查报告单的src错误！请检查。");
            else this.iframeSrc = param.src;

            this.ifShow = true;
        },
        close: function () {
            this.ifShow = false;
            this.ptnid = "";
            this.iframeSrc = "";
        }
    }
});

var jybgd = new Vue({
    el: '#jybgd',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifShow: false,
        jybgd: {},
        jybgdmx: [],
        yljgmc:userNameBg.Brxx_List.yljgmc
    },
    methods: {
        print:function(){
            window.print()
        },
        open: function (jybgd) {
            this.jybgd = jybgd;
            this.getJymx(jybgd.jyxh);
            this.ifShow = true;
        },
        close: function(){
            this.ifShow = false;
            this.jybgd = {};
            this.jybgdmx = [];
        },
        getJymx: function (jyxh) {
            var parm = {
                jyxh: jyxh,
            };
			jybgd.jybgdmx=[]
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_bgmxcx&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    jybgd.jybgdmx = data.d.list;
                } else {
                    malert("检验明细查询失败！" + data.c, "top", "defeadted");
                }
            });
        },
    }
});