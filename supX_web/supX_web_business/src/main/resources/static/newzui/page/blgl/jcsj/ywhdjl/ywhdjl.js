    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            title:'',
            LcList:{
                lcsjzid:''
            },
            cdajcmb:null,
            zYList:[],
            totle:'',
            num:0,
            param: {
                page: '',
                rows: '',
                total: ''
            }
        },
        watch:{
            cdajcmb:function () {
                var cdajcmb = '';
                for(var i = 0; i < wrapper.LcList.length; i++) {
                    if(this.LcList[i].cdajcmbid == this.cdajcmbid) {
                        cdajcmb = this.LcList[i].cdajcmb;

                    }

                }

            }
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增电子病历临床文档基础模板';
                wap.open();
                wap.popContent={};

            },
            sx:function () {
              yjkmtableInfo.getData();
            },
            //删除
            del:function () {
                yjkmtableInfo.remove();
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    yjkmtableInfo.getData();
                }

            },
            LcGetData:function () {
                $.getJSON('/actionDispatcher.do?reqUrl=EmrXtwhJcmb&types=query',function (json) {
                    if(json.a==0){
                        wrapper.LcList=json.d.list;
                        wap.LcList=json.d.list;
                        // console.log(wrapper.LcList)

                    }

                });
            },
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            iShow:false,
            isTabelShow:false,
            flag:false,
            jsShow:false,
            ksList:[],
            hszList:[],
            LcList:[],
            ywckList:[],
            centent:'',
            sjzmc:null,
            isFold: false,
            title:'',
            ifClick:true,
            num:0,
            csContent: {},
            jsonList: [],
            popContent: {
                'tybz': '',
            },
            hisjkfl:{
                "0":"病历",
                "1":"病程记录",
                "2":"护理记录",
                "3":"会诊申请",
                "4":"手术申请",
                "5":"检查申请",
                "6":"检验申请",
            },
            //0-病例 1-护理 2-检查 3-检验 4-手术 5-会诊
            hlws:{
                "0":"病例",
                "1":"护理",
                "2":"检查",
                "3":"检验",
                "4":"手术",
                "5":"会诊",
            },
            //0-按每次就诊号分开书写 1-追加到首次就诊号上
            sxfs:{
                "0":"按每次就诊号分开书写",
                "1":"追加到首次就诊号上"
            },
            //0-住院 1-门诊
            syfw:{
                "0":"住院",
                "1":"门诊"
            },
            //0-一条记录 1-多条记录
            ywhdjls:{
                "0":"一条记录",
                "1":"多条记录"
            },
            sfqxkz:{
                "0":"是",
                "1":"否"
            }

        },
        watch:{
            sjzmc:function () {
                var sjzmc = '';
                for(var i = 0; i < wap.LcList.length; i++) {
                    if(this.LcList[i].lcsjzid == this.lcsjzid) {
                        sjzmc = this.LcList[i].lcsjzid;

                    }

                }

            }
        },
        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //保存
            saveData: function() {
                if(wap.popContent.scbz){
                    wap.popContent.scbz = '0'
                }else{
                    wap.popContent.scbz = '1'
                }

                var json=JSON.stringify(wap.popContent);
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhYwhd&types=save',
                    json).then(function (data) {
                    if(data.body.a == 0){
                        yjkmtableInfo.getData();
                        wap.closes();
                        malert("保存成功","top","success");
                    } else {
                        malert("上传数据失败","top","defeadted");
                    }
                },function (error) {
                    console.log(error);
                });
            },

        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            popContent: {},
            jsonList: [],//
            iShow:false,
            isShowpopL:false,
            totlePage:0,
            total:'',
            page:'',
            kmbm:'',
            LcList:[],
            kmmc:'',
            rows:10,
            param: {
                page:1,
                rows:10,
                sort: '',
                order: 'asc',
                parm:'',
            },
            hisjkfl:{
                "0":"病历",
                "1":"病程记录",
                "2":"护理记录",
                "3":"会诊申请",
                "4":"手术申请",
                "5":"检查申请",
                "6":"检验申请",
            },
            //0-病例 1-护理 2-检查 3-检验 4-手术 5-会诊
            hlws:{
                "0":"病例",
                "1":"护理",
                "2":"检查",
                "3":"检验",
                "4":"手术",
                "5":"会诊",
            },
            //0-按每次就诊号分开书写 1-追加到首次就诊号上
            sxfs:{
                "0":"按每次就诊号分开书写",
                "1":"追加到首次就诊号上"
            },
            //0-住院 1-门诊
            syfw:{
                "0":"住院",
                "1":"门诊"
            },
            //0-一条记录 1-多条记录
            ywhdjls:{
                "0":"一条记录",
                "1":"多条记录"
            },
            sfqxkz:{
                "0":"是",
                "1":"否"
            }
        },
        methods: {

            getData: function () {
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }
                this.param.cdajcmbid=$("#lcsjzid").val();
                $.getJSON("/actionDispatcher.do?reqUrl=EmrXtwhYwhd&types=query&parm="+JSON.stringify(this.param),function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if(json.a==0){
                        yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                        yjkmtableInfo.jsonList = json.d.list;
                    }

                });
            },



            //删除
            remove: function() {
                var list = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                        var ywhdjlid={};
                        ywhdjlid.ywhdjlid=this.jsonList[i].ywhdjlid
                        list.push(ywhdjlid);
                    }
                }
                if(list.length == 0){
                    malert("请选中您要删除的数据","top","defeadted");
                    return false;
                }
                if(!confirm("请确认是否删除")){
                    return false;
                }
                var json='{"list":'+JSON.stringify(list)+'}'
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhYwhd&types=updateBz',
                    json).then(function (data) {
                    if(data.body.a == 0){
                        malert("删除成功","top","success")
                        yjkmtableInfo.getData();
                    } else {
                        malert("删除失败","top","defeadted")
                    }
                }, function (error) {
                    console.log(error);
                });

            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑新增电子病历临床文档基础模板'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },


        },


    });
    yjkmtableInfo.getData();
    wrapper.LcGetData();



