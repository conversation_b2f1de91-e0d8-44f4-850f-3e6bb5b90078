<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>管理小组</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link type="text/css" href="glxz.css" rel="stylesheet"/>
</head>

<body class="skin-default flex-container flex-dir-c flex-one">

    <div class="wrapper background-box" id="jyxm_icon" >
        <div class="panel" v-cloak>
            <div class="tong-top">
                <button class="tong-btn btn-parmary" @click="AddModel"><i class="iconfont icon-iocn42 icon-cf"></i>新增小组</button>
                <button class="tong-btn btn-parmary-b" @click="getData"><i class="iconfont icon-iocn56 icon-c1"></i>刷新</button>

            </div>
            <div class="tong-search">
                <div class="top-form">
                    <label class="top-label">检索</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-r-10 padd-l-10" v-cloak  >
            <div class="zui-table-header">
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-l text-left"><span>组名</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>所属科室</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>组员数量</span></div></th>
                        <th><div class="zui-table-cell cell-xxl text-left"><span>工作描述</span></div></th>
                        <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body "   @scroll="scrollTable($event)" v-cloak>
                <table class="zui-table zui-collapse">
                    <tbody>
                    <tr v-for="(item,$index) in 10" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @dblclick="Listconfirm($index)"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l text-left">医疗指控小组</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">消化内科</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s color-cf3">12个</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xxl text-left">工作描述工作描述工作描述工作描述工作描述</div>
                        </td>
                        <td class="cell-l">
                            <div class="zui-table-cell cell-l">
                                <span class="flex-center padd-t-2">
                                    <!--ui操作状态-->
                                    <em class="width30"><i class="iconfont icon-iocn46 icon-font20 icon-hover" data-title="编辑" @click="Edit($index)"></i></em>
                                    <em class="width30"><i class="iconfont icon-icon71 icon-font25 icon-hover" data-title="变更记录" @click="ChangeRecord($index)"></i></em>
                                    <em class="width30"><i class="iconfont icon-iocn52 icon-font20 icon-hover" data-title="删除" @click="remove($index)"></i></em>
                                </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--暂无数据提示,绑数据放开-->
                <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
            </div>

            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l background-f">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
                        <tr v-for="(item, $index) in 10"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()">
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r background-f">
                <div class="zui-table-header">
                    <table class="zui-table zui-collapse">
                        <thead>
                        <tr>
                            <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <tr v-for="(item, $index) in 10"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()">
                            <td class="cell-l">
                                <div class="zui-table-cell cell-l" >
                                    <span class="flex-center padd-t-2">
                                     <!--ui操作状态-->
                                    <em class="width30"><i class="iconfont icon-iocn46 icon-font20 icon-hover" data-title="编辑" @click="Edit($index)"></i></em>
                                    <em class="width30"><i class="iconfont icon-icon71 icon-font25 icon-hover" data-title="变更记录" @click="ChangeRecord($index)"></i></em>
                                    <em class="width30"><i class="iconfont icon-iocn52 icon-font20 icon-hover" data-title="删除" @click="remove($index)"></i></em>
                                    </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>


            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

    </div>
<div class="side-form pop-548" :class="{'ng-hide':num==0}"  id="brzcList" role="form" v-cloak>
    <div class="fyxm-side-top flex-between">
        <span v-text="title"></span>
        <span class="fr iconfont icon-iocn55 icon-cf056 icon-font20" @click="close"></span>
    </div>
    <div class="ksys-side" v-show="changeShow">
            <div class="top-form fl">
                <label class="top-label">小组名称</label>
                <div class="top-zinle">
                    <input type="text" class="zui-input wh182"/>
                </div>
            </div>
            <div class="top-form fr margin-r-0">
                <label class="top-label">科室</label>
                <div class="top-zinle">
                    <select-input class="wh182" @change-data="resultChange"
                                  :child="bxzt_tran" :index="'zt'" :val="zt"
                                  :name="'zt'"  :disable="true">
                    </select-input>
                </div>
            </div>
             <div class="top-form fl Absolutely padd-t-13">
                 <label class="top-label glxz-ms">工作描述</label>
                 <div class="top-zinle Absolutely">
                    <textarea class="zui-input height66" placeholder="请输入小组工作职责"></textarea>
                 </div>
            </div>
           <div class="Grouping">
            <div class="group-left">
                <div class="group-left-top">组长人选</div>
                <vue-scroll :ops="pageScrollOps">
                <ul class="group-content">
                    <li v-for="(item,$index) in 20" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <span class="group-span">
                       <input-checkbox @result="reCheckBox" :list="'20'"  :type="'some'" :which="$index"  :val="isChecked[$index]">
                        </input-checkbox>
                            </span>
                        <span class="group-span">周丽君</span>
                        <span class="group-span">全科</span>
                        <span class="group-span">科主任</span>
                    </li>

                </ul>
                </vue-scroll>
            </div>
            <div class="group-left">
                <div class="group-left-top group-right-top">组员人选</div>
                <vue-scroll :ops="pageScrollOps">
                <ul class="group-content">
                    <li v-for="(item,$index) in 20" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <span class="group-span">
                        <input-checkbox @result="reCheckBox" :list="'20'"  :type="'some'" :which="$index"  :val="isChecked[$index]">
                        </input-checkbox>
                        </span>
                        <span class="group-span">周丽君</span>
                        <span class="group-span">全科</span>
                        <span class="group-span">科主任</span>
                    </li>

                </ul>
                </vue-scroll>
            </div>

           </div>
    </div>
    <div class="ksys-side" v-show="changeShow==false">
        <vue-scroll :ops="pageScrollOps">
            <ul class="glxz-list">
                <li>
                    <div class="glxz-dot"></div>
                    <div class="glxz-title">2018/1/1 10:55:22</div>
                    <div class="glxz-content">
                        <h2>医疗质控检查小组 <span class="color-dlr font14 padd-l-20">消化内科</span></h2>
                        <div class="glxz-sub">医疗质量管理小组在科主任的领导下进行工作，负责完成门诊的医疗质量管理，对门诊医疗质量进行综合评估，对门诊的业务发展提出切实可行的划。</div>
                            <div class="flex-container margin-b-15 padd-b-15 swtl-text-content" style="position: relative">
                                <div @mouseleave="hoverName()"  class="Headimg" :class="!editText?'':'HeadImg'" v-for="(list,$index) in 4" :id="list">
                                    <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(/newzui/pub/image/wtg.png)">
                                    <p class="color-757c83 font12">刘医生</p>
                                </div>
                                <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                    <span class="djzt">科主任</span>
                                    <p class="headImg margin-t-10" style="background-image: url(/newzui/pub/image/wtg.png)">
                                    <p class="username text-center margin-t-10 margin-b-5 font-16 padd-t-10">刘医生</p>
                                    <div class="flex-container flex-jus-c padd-t-5">
                                        <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                        <span class="color-green font12 padd-r-10">外科</span>
                                        <span class="color-757c83 font12">主任医师</span>
                                    </div>
                                </div>
                            </div>
                    </div>
                </li>
                <li>
                    <div class="glxz-dot glxz-dot1"></div>
                    <div class="glxz-title">2018/1/1 10:55:22</div>
                    <div class="glxz-content">
                        <h2>医疗质控检查小组 <span class="color-dlr font14 padd-l-20">消化内科</span></h2>
                        <div class="glxz-sub">医疗质量管理小组在科主任的领导下进行工作，负责完成门诊的医疗质量管理，对门诊医疗质量进行综合评估，对门诊的业务发展提出切实可行的划。</div>
                        <div class="flex-container margin-b-15 padd-b-15 swtl-text-content" style="position: relative">
                            <div @mouseleave="hoverName()"  class="Headimg" :class="!editText?'':'HeadImg'" v-for="(list,$index) in 4" :id="list">
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="color-757c83 font12 padd-t-3">刘医生</p>
                            </div>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="username text-center margin-t-10 margin-b-5 font-16 padd-t-10">刘医生</p>
                                <div class="flex-container flex-jus-c padd-t-5">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="glxz-dot glxz-dot1"></div>
                    <div class="glxz-title">2018/1/1 10:55:22</div>
                    <div class="glxz-content">
                        <h2>医疗质控检查小组 <span class="color-dlr font14 padd-l-20">消化内科</span></h2>
                        <div class="glxz-sub">医疗质量管理小组在科主任的领导下进行工作，负责完成门诊的医疗质量管理，对门诊医疗质量进行综合评估，对门诊的业务发展提出切实可行的划。</div>
                        <div class="flex-container margin-b-15 padd-b-15 swtl-text-content" style="position: relative">
                            <div @mouseleave="hoverName()"  class="Headimg" :class="!editText?'':'HeadImg'" v-for="(list,$index) in 4" :id="list">
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="color-757c83 font12 padd-t-3">刘医生</p>
                            </div>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="username text-center margin-t-10 margin-b-5 font-16 padd-t-10">刘医生</p>
                                <div class="flex-container flex-jus-c padd-t-5">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="glxz-dot glxz-dot1"></div>
                    <div class="glxz-title">2018/1/1 10:55:22</div>
                    <div class="glxz-content">
                        <h2>医疗质控检查小组 <span class="color-dlr font14 padd-l-20">消化内科</span></h2>
                        <div class="glxz-sub">医疗质量管理小组在科主任的领导下进行工作，负责完成门诊的医疗质量管理，对门诊医疗质量进行综合评估，对门诊的业务发展提出切实可行的划。</div>
                        <div class="flex-container margin-b-15 padd-b-15 swtl-text-content" style="position: relative">
                            <div @mouseleave="hoverName()"  class="Headimg" :class="!editText?'':'HeadImg'" v-for="(list,$index) in 4" :id="list">
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="color-757c83 font12 padd-t-3">刘医生</p>
                            </div>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="username text-center margin-t-10 margin-b-5 font-16 padd-t-10">刘医生</p>
                                <div class="flex-container flex-jus-c padd-t-5">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="glxz-dot glxz-dot1"></div>
                    <div class="glxz-title">2018/1/1 10:55:22</div>
                    <div class="glxz-content">
                        <h2>医疗质控检查小组 <span class="color-dlr font14 padd-l-20">消化内科</span></h2>
                        <div class="glxz-sub">医疗质量管理小组在科主任的领导下进行工作，负责完成门诊的医疗质量管理，对门诊医疗质量进行综合评估，对门诊的业务发展提出切实可行的划。</div>
                        <div class="flex-container margin-b-15 padd-b-15 swtl-text-content" style="position: relative">
                            <div @mouseleave="hoverName()"  class="Headimg" :class="!editText?'':'HeadImg'" v-for="(list,$index) in 4" :id="list">
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="color-757c83 font12 padd-t-3">刘医生</p>
                            </div>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="username text-center margin-t-10 margin-b-5 font-16 padd-t-10">刘医生</p>
                                <div class="flex-container flex-jus-c padd-t-5">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="glxz-dot glxz-dot1"></div>
                    <div class="glxz-title">2018/1/1 10:55:22</div>
                    <div class="glxz-content">
                        <h2>医疗质控检查小组 <span class="color-dlr font14 padd-l-20">消化内科</span></h2>
                        <div class="glxz-sub">医疗质量管理小组在科主任的领导下进行工作，负责完成门诊的医疗质量管理，对门诊医疗质量进行综合评估，对门诊的业务发展提出切实可行的划。</div>
                        <div class="flex-container margin-b-15 padd-b-15 swtl-text-content" style="position: relative">
                            <div @mouseleave="hoverName()"  class="Headimg" :class="!editText?'':'HeadImg'" v-for="(list,$index) in 4" :id="list">
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="color-757c83 font12 padd-t-3">刘医生</p>
                            </div>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(/newzui/pub/image/wtg.png)">
                                <p class="username text-center margin-t-10 margin-b-5 font-16 padd-t-10">刘医生</p>
                                <div class="flex-container flex-jus-c padd-t-5">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </vue-scroll>
    </div>
    <div class="ksys-btn padd-r-10">
        <button class="root-btn btn-parmary-d9" @click="close">取消</button>
        <button class="root-btn btn-parmary" @click="confirm">确定</button>
    </div>
</div>

<script src="glxz.js"></script>
</body>

</html>