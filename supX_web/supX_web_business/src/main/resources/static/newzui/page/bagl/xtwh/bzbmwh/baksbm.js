
(function () {

	/********************顶部工具栏操作***********************/
    var moneyTool = new Vue({
        el: '.panel',
        data: {

        },
        methods: {
            //刷新
            sx:function () {
                baksList.getData();
            },
            //删除病案科室
            deleteBaks: function () {
                var ksList = [];
                for(var i=0;i<baksList.isChecked.length;i++){
                    if(baksList.isChecked[i] == true){
                    	var baks={};
                    	var removeUrl="/actionDispatcher.do?reqUrl=BaglBmwhBaks&types=delete&";
                    	baks.ksbm = baksList.baksList[i].ksbm
                    	ksList.push(baks);
                    }
                }
                if(ksList.length == 0){
                    malert("请选中您要删除的数据");
                    return false;
                }
                var json='{"list":'+JSON.stringify(ksList)+'}';
                this.$http.post(removeUrl,json).then( function (data) {
                    baksList.getData();
                    if(data.body.a == 0){
                        malert("删除成功");
                         baksList.getData();
                    } else {
                        malert("删除失败："+data.body.c)
                    }
                }, function (error) {
                    console.log(error);
                });
            },

            //保存病案科室
            saveBaks: function () {
                if(baksList.baksList.length== 0){
                    malert("保存的对象不能为空！");
                    return;
                }
                //请求后台进行保存
                	var json='{"list":'+JSON.stringify(baksList.baksList)+'}';
	                this.$http.post('/actionDispatcher.do?reqUrl=BaglBmwhBaks&types=updateBetch&',
	                		json).then(function (data) {
	                            if(data.body.a == 0){
	                                malert("保存成功");
	                                baksList.getData();
	                                baksList.edit = [];
                					baksList.isChecked = [];
	                            } else {
	                                malert("保存失败："+data.body.c);
	                            }
	                        }, function (error) {
	                            console.log(error);
	                        });
            },
        }
    });

	/********************左边科室列表信息***********************/
    var ksList = new Vue({
        el: '.baksbm-left',
        mixins: [dic_transform,tableBase],
        data: {
            ksList: [],
            baksContent:{}, //病案科室对象
        },
        methods: {
			getData: function(){
				//请求后台查询所有科室
				var parm={
					tybz:"0"
				};
				this.param.sort='ksbm';
        		$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryKsbm&types=query&dg="+JSON.stringify(this.param)+"&parm="+JSON.stringify(parm),
        				function(json) {
	                        if(json.a==0){
	                        	if(json.d.list.length>0){
	                        		ksList.totlePage = Math.ceil(json.d.total/ksList.param.rows);
	                        		ksList.ksList=json.d.list;
	                        	}
	                        }else {
	    						malert("查询失败："+json.c);
	    					}
                        });
			},

			//双击选中科室
			editThis: function (index) {
	            //将当前选中的科室保存到病案科室中
	            for(var i=0; i<baksList.baksList.length;i++){
	            	if(this.ksList[index]['ksbm']==baksList.baksList[i].ksbm){
	            		malert("该科室已作为病案科室请重新选择");
	            		return;
	            	}
	            }
	            this.baksContent['ksbm']=this.ksList[index]['ksbm'];
	            this.baksContent['ksmc']=this.ksList[index]['ksmc'];
	            var json=JSON.stringify(this.baksContent);
	            this.$http.post("/actionDispatcher.do?reqUrl=BaglBmwhBaks&types=insert&",json).then(function (data) {
                    if(data.body.a == 0){
                        baksList.getData();
                        this.baksContent = {};
                    } else {
                        malert("上传数据失败:"+data.body.c);
                    }
                });
	        },
        }
    });

	/********************右边病案科室列表信息***********************/
    var baksList = new Vue({
        el: '.baksbm-right',
        mixins: [dic_transform,baseFunc,tableBase,mformat],
        data: {
            baksList: [],
            edit: [],
        },
        methods: {
        	//请求后台查询查询病案科室信息
        	getData: function(){
        		this.param.rows=20000;
        		this.param.sort="ksbm";
        		$.getJSON("/actionDispatcher.do?reqUrl=BaglBmwhBaks&types=queryBaks&parm="+JSON.stringify(this.param),
        				function(json) {
	                        if(json.a==0){
	                        	if(json.d.list.length>0){
	                        		baksList.baksList=json.d.list;

	                        	}
	                        }else {
	    						malert("查询失败："+json.c);
	    					}
                        });
        	},

        	//双击修改当前行
            editThis: function (index) {
                Vue.set(this.edit, index, true);
            },
        }
    });



    /********************初始化頁面需要加載的***********************/
	ksList.getData();//加载科室列表信息
	baksList.getData(); //加载病案科室列表信息


})();
