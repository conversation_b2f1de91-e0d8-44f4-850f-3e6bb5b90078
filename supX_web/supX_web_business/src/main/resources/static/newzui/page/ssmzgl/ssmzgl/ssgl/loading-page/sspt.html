<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>手术排台</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../../newcss/main.css" rel="stylesheet">
    <link href="sspt.css" rel="stylesheet">
</head>
<body class="body skin-default padd-l-10 padd-t-10 padd-r-10 height padd-b-20">
<div class="header-item">
    <header class="userNameBg printHide" v-cloak>
        <div class="flex-container flex-align-c">
            <div class="text-color">
                <p class="userHeader userCwh padd-l-30">
                    <span class="userName">病人姓名：{{Brxx_List.brxm}}</span>
                    <span class="sex text">住院号：{{Brxx_List.zyh}}</span>
                    <span class="sex text">性别：{{brxb_tran[Brxx_List.brxb]}}</span>
                    <span class="sex text">年龄：{{Brxx_List.nl}}{{nldw_tran[Brxx_List.nldw]}}</span>
                    <span class="sex text">科室床位：{{Brxx_List.rycwbh}}</span>
                    <span class="sex text">住院医生：{{Brxx_List.ysxm}}</span>
                </p>
            </div>
        </div>
    </header>
</div>
<div class="content background-f contextInfo over-auto padd-b-20" id="content" style="height: calc(100% - 66px)">
    <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">手术编号</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" readonly @keydown="nextFocus($event)" v-model="pageState.ssbh" type="text"/>
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c">
            <span class="padd-r-5">申请单号</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" readonly  @keydown="nextFocus($event)" v-model="pageState.sssqdh"  type="text"/>
            </div>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-l-20">
            <span class="padd-r-5">手术科室</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.ssksmc"  type="text"/>
            </div>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-l-20 padd-r-20">
            <span class="padd-r-5">当前状态</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input color-wtg" readonly @keydown="nextFocus($event)" v-model="pageState.dqzt" type="text"/>
            </div>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c ">
            <span class="padd-r-5">婴儿标识</span>
            <div class="zui-table-cell text-center">
                <input class="green" v-model="pageState.ye"  :true-value="1" :false-value="0" type="checkbox">
                <label @click="doCheck('ye')" @dblclick.stop></label>
            </div>
        </div>
    </div>
    <div class="flex-container  padd-l-10 padd-r-10 ">
        <div class=" padd-b-20 padd-t-10 flex-container flex-align-c padd-r-20">
            <span class="padd-r-5">安排日期</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input"  data-notEmpty="true" @keydown="nextFocus($event)" :value="fDate(pageState.aprq,'datetime')" id="apri" type="text"/>
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5">手&ensp;术&ensp;间</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange"  :not_empty="true" :child="ssjList"
                              :index="'mc'" :index_val="'bm'" :val="pageState.ssj" :name="'pageState.ssj'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c ">
            <span class="padd-r-5">&emsp;&emsp;台次</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.tc" type="number"/>
            </div>
        </div>
    </div>
    <div class="flex-container padd-t-10  padd-l-10 padd-r-10 padd-b-20">
        <div class="flex-container padd-r-20" style="width: 100%">
            <span class="padd-r-5 whiteSpace">术前诊断</span>
            <textarea class="zui-input" v-model="pageState.sqzd" style="height: 100px"></textarea>
        </div>
    </div>
    <div class="flex-container flex-wrap-w padd-l-10 padd-r-10 ">
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">主刀医师</span>
                <select-input class="wh180" @change-data="resultChange22" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :index_mc="'ryxm'" :val="pageState.ssys" :name="'pageState.ssys'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">手术等级</span>
                <select-input class="wh180" @change-data="resultChange"  disable :not_empty="true" :child="ssldj_tran"
                              :index="pageState.ssdj" :val="pageState.ssdj"  :name="'pageState.ssdj'"
                              :search="true"></select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">手术类型</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'" :child="ssllx_tran"
                              :index="pageState.sslx" :val="pageState.sslx" :name="'pageState.sslx'"
                              :search="true"></select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">上&emsp;&emsp;级<br/>手术医生</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :index_mc="'ssysSjysxm'" :val="pageState.ssysSjys" :name="'pageState.ssysSjys'"
                              :search="true" :phd="''">
                </select-input>
        </div>
    </div>
    <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">&emsp;&emsp;主术</span>
            <div class="zui-input-inline wh180">
                <input  class="zui-input" :data-notEmpty="csqx.N03012200102== '0' ?true :'removeFalse'" v-model="pageState.zsbm"
                       @keydown="changeDown($event,'searchCon','Content','selSearch','zsbm','zsmc'),nextFocus($event)" @input="change(false,'zsbm',$event.target.value,'searchCon','selSearch')"
                       placeholder="手術編碼">
                <search-table :message="searchCon" :selected="selSearch" :page="page" :them="them" :them_tran="them_tran"
                              @click-one="checkedOneOut" @click-two="selectOne">
                </search-table>
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">主术名称</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.zsmc"  />
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">第一手术</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" v-model="pageState.ssbm1" @keydown="changeDown($event,'searchCon1','Content1','selSearch1','ssbm1','ssmc1'),nextFocus($event)"
                       @input="change(false, 'ssbm1', $event.target.value,'searchCon1','selSearch1')" />
                <search-table :message="searchCon1" :selected="selSearch1" :page="page" :them="them" :them_tran="them_tran"
                              @click-one="checkedOneOut" @click-two="selectOne1">
                </search-table>
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">第&emsp;&emsp;一<br/>手术名称</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.ssmc1" />
            </div>
        </div>
    </div>
    <div class="flex-container flex-wrap-w padd-l-10 padd-r-10 ">
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">第二手术</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" v-model="pageState.ssbm2" @keydown="changeDown($event,'searchCon2','Content2','selSearch2','ssbm2','ssmc2'),nextFocus($event)"
                       @input="change(false, 'ssbm2', $event.target.value,'searchCon2','selSearch2')" />
                <search-table :message="searchCon2" :selected="selSearch2" :page="page" :them="them" :them_tran="them_tran"
                              @click-one="checkedOneOut" @click-two="selectOne2">
                </search-table>
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">第&emsp;&emsp;二<br/>手术名称</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.ssmc2"  />
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">第三手术</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" v-model="pageState.ssbm3" @keydown="changeDown($event,'searchCon3','Content3','selSearch3','ssbm3','ssmc3'),nextFocus($event)"
                       @input="change(false, 'ssbm3', $event.target.value,'searchCon3','selSearch3')" />
                <search-table :message="searchCon3" :selected="selSearch3" :page="page" :them="them" :them_tran="them_tran"
                              @click-one="checkedOneOut" @click-two="selectOne3">
                </search-table>
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">第&emsp;&emsp;三<br/>手术名称</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.ssmc3" />
            </div>
        </div>
    </div>
    <div class="flex-container flex-wrap-w padd-l-10 padd-r-10 ">
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">第四手术</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" v-model="pageState.ssbm4" @keydown="changeDown($event,'searchCon4','Content4','selSearch4','ssbm4','ssmc4'),nextFocus($event)"
                       @input="change(false,'ssbm4', $event.target.value,'searchCon4','selSearch4')" />
                <search-table :message="searchCon4" :selected="selSearch4" :page="page" :them="them" :them_tran="them_tran"
                              @click-one="checkedOneOut" @click-two="selectOne4">
                </search-table>
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">第&emsp;&emsp;四<br/>手术名称</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.ssmc4"/>
            </div>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">助&emsp;&ensp;手1</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'"  :child="ysData"
                              :index="'ryxm'" :index_mc="'zs1xm'" :index_val="'rybm'" :val="pageState.zs1" :name="'pageState.zs1'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">助&emsp;&ensp;手2</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="false" :child="ysData"
                              :index="'ryxm'" :index_mc="'zs2xm'" :index_val="'rybm'" :val="pageState.zs2" :name="'pageState.zs2'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">助&emsp;&ensp;手3</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="false" :child="ysData"
                              :index="'ryxm'" :index_mc="'zs3xm'" :index_val="'rybm'" :val="pageState.zs3" :name="'pageState.zs3'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">输&emsp;&emsp;液<br/>及补液者</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'" :child="hsData"
                              :index="'ryxm'" :index_mc="'sybyzxm'" :index_val="'rybm'" :val="pageState.sybyz" :name="'pageState.sybyz'"
                              :search="true" :phd="''">
                </select-input>
        </div>
    </div>
    <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 ">
            <span class="padd-r-5">台上护士<br/>1</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'" :child="hsData"
                              :index="'ryxm'" :index_mc="'tshs1xm'" :index_val="'rybm'" :val="pageState.tshs1" :name="'pageState.tshs1'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c" v-if="cs03004200246=='0'">
            <span class="padd-r-5">台上护士<br/>2</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="false" :child="hsData"
                              :index="'ryxm'" :index_mc="'tshs2xm'" :index_val="'rybm'" :val="pageState.tshs2" :name="'pageState.tshs2'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-l-20" v-if="cs03004200246=='0'">
            <span class="padd-r-5">洗手护士</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'" :child="hsData"
                              :index="'ryxm'" :index_mc="'xshsxm'" :index_val="'rybm'" :val="pageState.xshs" :name="'pageState.xshs'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-l-20 padd-r-20" >
            <span class="padd-r-5">巡回护士<br/>1</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="true" :child="hsData"
                              :index="'ryxm'"  :index_mc="'xhhs1xm'"  :index_val="'rybm'" :val="pageState.xhhs1" :name="'pageState.xhhs1'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20" v-if="cs03004200246=='0'">
            <span class="padd-r-5">巡回护士<br/>2</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="false" :child="hsData"
                              :index="'ryxm'"  :index_mc="'xhhs2xm'" :index_val="'rybm'" :val="pageState.xhhs2" :name="'pageState.xhhs2'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c ">
            <span class="padd-r-5">器械护士</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="true" :child="hsData"
                              :index="'ryxm'" :index_mc="'qxsxm'" :index_val="'rybm'" :val="pageState.qxs" :name="'pageState.qxs'"
                              :search="true" :phd="''">
                </select-input>
        </div>
    </div>
    <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
        <div class="flex-container   padd-b-20 padd-t-10 flex-align-c">
            <span class="padd-r-5">麻醉分级</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'" :child="mzfj_tran"
                              :index="pageState.asajb" :val="pageState.asajb" :name="'pageState.asajb'"
                              :search="true"></select-input>
        </div>
        <div class="flex-container padd-l-20 padd-b-20 padd-t-10 flex-align-c">
            <span class="padd-r-5">麻醉医生</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_mc="'mzysxm'" :index_val="'rybm'" :val="pageState.mzys" :name="'pageState.mzys'"
                              :search="true" :phd="''">
                </select-input>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 padd-r-20 flex-align-c padd-l-20">
            <span class="padd-r-5">steward</br>术前评分</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" :data-notEmpty="csqx.N03012200102== '0' ?true :'removeFalse'" placeholder="术前steward评分" v-model="pageState.stewardSq" type="number"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">麻醉处理标志</span>
            <div class="zui-table-cell text-center">
                <input class="green" v-model.number="pageState.mzclbz" :true-value="1" :false-value="0" type="checkbox">
                <label @click="doCheck('mzclbz')" @dblclick.stop></label>
            </div>
        </div>
    </div>
    <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
        <div class="flex-container padd-b-20 padd-t-10 flex-align-c">
            <span class="padd-r-5">麻醉方式</span>
                <select-input class="wh180" @change-data="resultChange" :child="zybmList" :index="'zymc'"
                              :index_val="'zybm'" :index_mc="'mzfsmc'" :val="pageState.mzfs" :name="'pageState.mzfs'" :index_mc="'mzfsmc'" :search="true"
                              :phd="'麻醉方式'">
                </select-input>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-l-20">
            <span class="padd-r-5">麻醉科室</span>
                <select-input class="wh180" @change-data="resultChangeOd" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'"
                              :child="allKs"  :index="'ksmc'" :index_val="'ksbm'" :val="pageState.mzks"
                              :name="'pageState.mzks'">
                </select-input>
        </div>
        <div class="flex-container padd-b-20 padd-t-10 padd-l-20 flex-align-c">
            <span class="padd-r-5">上&emsp;&emsp;级<br/>麻醉医生</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="csqx.N03012200102== '0' ?true :'removeFalse'" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.mzysSjys" :name="'pageState.mzysSjys'"
                              :search="true" :phd="''">
                </select-input>
        </div>
    </div>
    <div class="zui-table-tool padd-r-10 flex-jus-e flex-align-c zui-border-bottom flex-container font-14-654">
        <button class="root-btn btn-parmary" @click="SaveSssq()">保存</button>
    </div>
</div>
<script type="text/javascript" src="sspt.js"></script>
</body>
</html>
