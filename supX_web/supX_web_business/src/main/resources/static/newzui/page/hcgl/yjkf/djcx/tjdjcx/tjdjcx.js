var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],

    data: {
        isCheck: null,
        isCheckMx: null,
        djList: [], //单据列表
        mxList: {}, //单据明细
        dateBegin: null,
        dateEnd: null,
        shzfbz: {
            '0': '未审核',
            '1': '已审核',
            '2': '已作废',
        },
        popContent: {},
        yfkf: 0, //药房库房信息
        csParm: {},
        tkdContent: {
            rkfs: "02",  //01-入库，02-退库，03-盘点入库
        },
        rkdList: [], //入库单集合
        KFList: [], //库房
        yfkfList: [],
        rkd: {}, //入库单对象
        num: 0,
        param: {
            kfbm:'',
            qrzfbz: 1,
        },

    },
    updated: function () {
        changeWin();
    },
    mounted: function () {
        this.getKf();
    },
    methods: {
        getKf: function () {
            //库房列表
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf',
                function (data) {
                    if (data.a == 0) {
                        wrapper.yfkfList = data.d.list;
                        wrapper.param.kfbm = data.d.list[0].kfbm;
                        wrapper.getData();
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },

        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            Vue.set(this.param, 'kfbm', val[0]);
            Vue.set(this.param, 'kfmc', val[4]);
            this.getData();
        },
        //查询单据
        getData: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=ykglKfywTjd&types=queryTjdForPass&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == 0) {
                        wrapper.djList = data.d;
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }

                });
        },
    }
});

laydate.render({
    elem: '.todate'
    , trigger: 'click'
    , theme: '#1ab394',
    range: true
    , done: function (value, data) {
        wrapper.param.beginrq = value.slice(0, 10);
        wrapper.param.endrq = value.slice(13, 23);
    }
});




