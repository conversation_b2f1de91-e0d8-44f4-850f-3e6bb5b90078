<div id="sjsc" class="contextInfo">
    <div class="sjsc-left" style="width: 22%">
        <div class="sjsc-top">
            <label class="sjsc-label ">科室</label>
            <select-input @change-data="commonResultChange" :not_empty="false"
                          :child="zyksList" :index="'ksmc'" :index_val="'ksbm'" :val="rydjBrxx.ksbm"
                          :name="'rydjBrxx.ksbm'" :search="true" :index_mc="'ksmc'" id="ksbm">
            </select-input>
        </div>
        <div class="sjsc-top margin-t-10">
            <label class="sjsc-label ">搜索</label>
            <input type="text" class="zui-input" placeholder="住院号/姓名" v-model="page.parm"
                   @input="searching($event.target.value)"
            />
        </div>
        <div class="sjsc-top margin-t-10">
            <div class="sjsc-table-name">
                <i>
                    <div class="zui-table-cell cell-m">
                        <span>
                            <input-checkbox @result="reCheckBox" :list="'rydjBrList'" :type="'all'" :val="isCheckAll"></input-checkbox>
                        </span>
                    </div>
                </i>
                <i>住院号</i>
                <i>姓名</i>
                <i>科室</i>
            </div>
            <vue-scroll :ops="pageScrollOps">
                <ul class="sjsc-table-list">
                    <li v-for="(item,$index) in rydjBrList">
                        <i>
                            <div class="zui-table-cell cell-m" @click="selectOne($index)">
                                <input-checkbox @result="reCheckBox"
                                                :list="'rydjBrList'"
                                                :type="'some'" :which="$index"
                                                 :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </i>
                        <i v-text="item.zyh">zhy0002002</i>
                        <i v-text="item.brxm">李欢欢</i>
                        <i v-text="item.ryksmc" class="overflowHide">lhh</i>
                    </li>
                </ul>
            </vue-scroll>

        </div>
    </div>
    <div class="sjsc-right" style="width: 78%">
        <div class="contextDiv">
            <div class="nh-menu">
                <span @click="loadCon('wscjl'),which=1" :class="{selected: which==1}">未上传记录</span>
                <span @click="loadCon('cwxx'),which=2" :class="{selected: which==2}">错误信息</span>
                <div class="nh-screen">
                    <div>
                        <label class="zui-form-label float-left">时间</label>
                        <div class="float-left zui-input-inline zui-date">
                            <i class="datenox fa-calendar"></i>
                            <input id="cronDate" class="zui-input" v-model="popContent.cronDate" readonly="readonly">
                        </div>
                    </div>
                    <div>
                        <label><input id="zdsc" type="checkbox" v-model="ifClick" @click="zdsc"/><span>自动上传<span></label>
                    </div>
                    <div>
                        <label><input type="checkbox" />出院病人</label>
                    </div>
                    <div>
                        <label><input type="checkbox" />未对码</label>
                    </div>
                    <div>
                        <label><input type="checkbox" />已对码</label>
                    </div>
                </div>
            </div>
            <div class="context">
                <div id="scnh">
                    <div class="page_div hyjl"></div>
                    <div class="page_div wscjl"></div>
                    <div class="page_div cwxx"></div>
                    <div class="page_div yjs"></div>
                </div>
            </div>
        </div>
    </div>

</div>
<link href="sjsc.css" rel="stylesheet">
<style type="text/css">
.contextInfo{
    width: 100%;
    display: flex;
    justify-content: space-between;
}
    .sjsc-left{
        width: 270px;
        padding:10px 0;
    }
    .sjsc-right{
        width: calc(100% - 270px);
    }
.sjsc-top{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    box-sizing: border-box;
    flex-wrap: wrap;
    padding: 0 10px;
}
.sjsc-label{
    width: 80px;
    font-size: 14px;

}
.sjsc-table-name{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    background: #f9fbfc;
    height: 34px;
    align-items: center;
    border: 1px solid #eee;
    box-sizing: border-box;
}
.sjsc-table-name i{
    width: calc((100% - 130px )/2);
    text-align: center;
    display: flex;
    justify-content: center;
}

.sjsc-table-list{
    width: 100%;
    display: block;
    height:70vmin;
}
.sjsc-table-list li{
     width: 100%;
    display: flex;
    justify-content: flex-start;
    line-height: 40px;
    border: 1px solid #eee;
    border-top: none;
    cursor: pointer;
}
.sjsc-table-list li:hover{
    background: rgba(237, 250, 247,1);
}
.sjsc-table-list li i{
    width: calc((100% - 130px )/2);
    text-align: center;
    display: flex;
    justify-content: center;
    flex-wrap: nowrap;
    overflow: hidden;
}
.sjsc-table-name i:first-child,.sjsc-table-list li i:first-child{
    width: 30px;
}
.sjsc-table-name i:nth-child(2),.sjsc-table-list li i:nth-child(2){
    width: 100px;
}
</style>
<script type="text/javascript" src="sjsc.js"></script>