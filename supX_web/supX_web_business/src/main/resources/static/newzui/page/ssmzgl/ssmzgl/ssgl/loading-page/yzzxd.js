//(function () {
//当前时间11
var dateend = getTodayDateEnd();
var datestart = getTodayDateBegin();
/********************************华丽分割线***************************************/
var bodyMenu = new Vue({
    el: '#bodyMenu',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        yzzxdList: [], //医嘱执行单集合
        yzlx: 'qb', //医嘱类型默认全部
        zyhs: [], //住院号集合
        pcs: [], //频次集合
        sxlx: 'all',
        picked:'0',
        num:null,
        isShowPslr: false,
        sytNumberOfColumns: 1,
        pcList: {
            "a": "20~80"
        },
        pc: "a",
        jsrq:'',
        ksrq:'',
        searchContent:{
        	sysd:'3',
        },
    },
    mounted: function () {
        //默认加载当前时间
        //默认加载当前时间
        this.ksrq = datestart
        this.jsrq = dateend
        laydate.render({
            elem: '#dbegin',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                bodyMenu.ksrq = value;
                bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
            }
        });
        laydate.render({
            elem: '#dEnd',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                bodyMenu.jsrq = value;
                bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
            }
        });
    },
    methods: {
    	initData:function(){
    		var getZyhs=JSON.parse(sessionStorage.getItem("yzshly"));
    		this.zyhs.push(getZyhs.zyh);
    		bodyMenu.clickMenu('all',0);
    	},
        showLsYz: function () {
            pslr.getPsData();
            pslr.isShow = true;
        },
        // 弹出打印格式
        printFormat: function () {
            printFormat.setData();
            printFormat.isShow = true;
        },
        searchListHc: function () {
            if (window.event.keyCode == 13) {
                bodyMenu.clickMenu(yzlx); //根据科室过滤病人
            }
        },
        YzlxChange: function (type) {
            this.yzlx = type;
            bodyMenu.clickMenu(bodyMenu.sxlx);
        },
        resultChangeSysd:function(val){
        	  console.log(val);
        	  if (val[2].length > 1) {
                  if (Array.isArray(this[val[2][0]])) {
                      Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                      bodyMenu.clickMenu(bodyMenu.sxlx,bodyMenu.num);
                  } else {
                      Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                      if (val[3] != null) {
                          Vue.set(this[val[2][0]], val[3], val[4]);
                      }
                      bodyMenu.clickMenu(bodyMenu.sxlx,bodyMenu.num);
                  }
              } else {
                  this[val[2][0]] = val[0];
                  
              }
              if (val[1] != null) {
                  this.nextFocus(val[1]);
              }
        	
        },
        //筛选按钮
        clickMenu: function (isClick,index) {
            bodyMenu.sxlx = isClick;
            this.num=index;
            //取住院号 查看查询
            if (this.zyhs.length <= 0) {//住院号为空则直接返回不请求后台
                return;
            }
            var yzlxx = "";
            if (this.yzlx == 'ls') {
                //临时医嘱执行单
                printFormat.title = "临时";
                yzlxx = "0";
            } else if (this.yzlx == 'cq') {
                //长期医嘱执行单
                printFormat.title = "长期";
                yzlxx = "1";
            } else {
                yzlxx = null;
            }
            var parm = {
                beginrq: this.ksrq,
                endrq: this.jsrq,
                yzlx: yzlxx,
                searchzyh: this.zyhs
            };
            this.pcs = [];
            for (var i = 0; i < rightPcxx.pcList.length; i++) {
                if (rightPcxx.isChecked[i]) {
                    var obj = {};
                    this.pcs.push(rightPcxx.pcList[i].pcbm);
                }
            }
            if(this.pcs.length>0){
            	parm.searchpcbm = this.pcs;
            }
            $(".InfoMenu div").removeClass('InfoMenuSelected');
            if (isClick == 'all') {//*******************全部
                this.yzzxdList = [];
                //请求后台查询全部执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryAll&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(0).addClass('InfoMenuSelected');

            } else if (isClick == 'kf') {//********************口服
                this.yzzxdList = [];
                //请求后台查询口服执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryKf&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(1).addClass('InfoMenuSelected');

            } else if (isClick == 'zs') {//**********************************注射
                this.yzzxdList = [];
                //请求后台查询注射的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryZs&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(2).addClass('InfoMenuSelected');

            } else if (isClick == 'sy') {//********************输液
                this.yzzxdList = [];
                if(bodyMenu.searchContent.sysd=='0'){
                	parm.sysdsx1='1';
                }
                if(bodyMenu.searchContent.sysd=='1'){
                	parm.sysdsx2='1';
                }
                if(bodyMenu.searchContent.sysd=='2'){
                	parm.sysdsx3='1';
                }
                //请求后台查询输液的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=querySy&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(3).addClass('InfoMenuSelected');

            } else if (isClick == 'hl') {//**********************护理
                this.yzzxdList = [];
                //请求后台查询护理的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryHl&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(4).addClass('InfoMenuSelected');

            } else if (isClick == 'zl') {//******************************治疗
                this.yzzxdList = [];
                //请求后台查询治疗的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryZl&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(5).addClass('InfoMenuSelected');

            } else if (isClick == 'syt') {//输液贴
                this.yzzxdList = [];
                //取频次 查看查询
                this.pcs = [];
                for (var i = 0; i < rightPcxx.pcList.length; i++) {
                    if (rightPcxx.isChecked[i]) {
                        var obj = {};
                        this.pcs.push(rightPcxx.pcList[i].pcbm);
                    }
                }
                if (this.pcs.length <= 0) {//频次为空则直接返回不请求后台
                	malert("请先选择频次之后再打印输液贴！",'top','defeadted');
                    return;
                }
                parm.searchpcbm = this.pcs;
                //请求后台查询输液贴的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=querySyt&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                    	bodyMenu.dealSytData(json.d.list);
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(6).addClass('InfoMenuSelected');
            }
        },
        //对输液贴查询出来的数据做处理
        dealSytData:function(oldList){
          var newlist=[];
          for(var i=0;i<oldList.length;i++){
        	//执行记录数据从新根据分组号分组
      		var map = {};
      		var fzhobjList=[];
      		for(var j = 0; j < oldList[i].yzxx.length; j++){
      			var obj = oldList[i].yzxx[j];
      			if(!map[obj.fzh]){
      				fzhobjList.push({
      					fzh:obj.fzh,
      					yzmx:[obj],
      				});
      				map[obj.fzh] =obj;
      			}else{
      				for(var k = 0; k < fzhobjList.length; k++){
      					var fzcz = fzhobjList[k];
      					if(fzcz.fzh == obj.fzh){
      						fzcz.yzmx.push(obj);
      						break;
      					}
      				}
      			}
      		}
      		oldList[i].yzxx=fzhobjList;
      		if(oldList[i].yzxx.length>1){
      			for(var num=0;num<oldList[i].yzxx.length;num++){
      				var newobj=JSON.parse(JSON.stringify(oldList[i]));
      				newobj.yzxx=oldList[i].yzxx[num].yzmx;
      				newlist.push(newobj);
      			}
      		}else{
      			var yzxx=oldList[i].yzxx[0].yzmx;
      			oldList[i].yzxx=yzxx;
      			newlist.push(oldList[i]);
      		}
      		//----------
          }
          console.log(newlist);
          for(var dex=0;dex<newlist.length;dex++){
        	  if(newlist[dex].yzxx[0].yzlx=="1"){
        		  newlist[dex].yzlxmc="长期";
        	  }else{
        		  newlist[dex].yzlxmc="临时";
        	  }
        	  newlist[dex].yyffmc=newlist[dex].yzxx[0].yyffmc;
        	  newlist[dex].pcmc=newlist[dex].yzxx[0].pcmc;
        	  newlist[dex].sysd=newlist[dex].yzxx[0].sysd+newlist[dex].yzxx[0].sysddw;
        	  if(newlist[dex].nldw=="1"){
        		  newlist[dex].brnl=newlist[dex].nl+"岁";
        	  }else{
        		  newlist[dex].brnl=newlist[dex].nl+"月";
        	  }
          }
          bodyMenu.yzzxdList = newlist;
        },
    }
});

/********************************华丽分割线***************************************/

/********************************华丽分割线***************************************/
var rightPcxx = new Vue({
    el: '#rightPcxx',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        pcList: [] //频次集合
    },
    methods: {
        //请求后台查询所有频次
        getDate: function () {
            this.param.rows = 200000;
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yzpc'
                + '&dg=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == 0) {
                        if (data.d.list.length > 0) {
                            rightPcxx.pcList = data.d.list;
                        }
                    }
                });
        }
    }
});

var pslr = new Vue({
    el: '.pslr',
    mixins: [baseFunc, tableBase, dic_transform, mformat],
    data: {
        isShow: false,
        psYzList: [],
        index: 0
    },
    methods: {
        getPsData: function () {
            var yzlxx = "";
            var endrq = $("#dEnd").val();
            var beginrq = $("#dbegin").val();
            if (bodyMenu.yzlx == 'ls') {
                yzlxx = "0";
            } else if (bodyMenu.yzlx == 'cq') {
                yzlxx = "1";
            } else {
                yzlxx = null;
            }
            var parm = {
                beginrq: beginrq,
                endrq: endrq,
                yzlx: yzlxx,
                searchzyh: bodyMenu.zyhs
            };
            $.getJSON('/actionDispatcher.do?reqUrl=HszHlywYzzxdCx&types=queryPs&parm=' + JSON.stringify(parm), function (json) {
                if(json.a==0){
                    pslr.psYzList = json.d.list;
                }else {
                    malert(json.c,'top','defeadted')
                }
                }, function (error) {
                    console.log(error);
                });
        },
        resultChange_save: function (val) {
            Vue.set(this.psYzList[val[2][0]], [val[2][1]], val[0]);
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
            var parm = {
                psjg: val[0],
                ypyzxh: pslr.psYzList[pslr.index].yzxh,
                mxxh: pslr.psYzList[pslr.index].yzmxxh,
                ryypmc: pslr.psYzList[pslr.index].xmmc,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=savePs&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == '0') {
                        pslr.getPsData();
                        malert("皮试结果保存成功",'top','success');
                    } else {
                        malert("皮试结果保存失败",'top','defeadted');
                    }
                });
        },
        isfzcfMxChecked: function () {

        },
        fzCheckOne: function (index) {
            pslr.index = index;
        }
    }
});
//执行打印
var printFormat = new Vue({
    el: '.printFormat',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        jsonList: [],
        BrxxJson: [],
        rowsVal: null,
        blankList: [],
        title: '', //标题（长期和临时）
        isGoPrint: false
    },
    methods: {
        setList: function () {
            var newList = [];
            for (var i = 0; i < this.jsonList.length; i++) {
                for (var j = 0; j < this.jsonList[i]['yzxx'].length; j++) {
                    if (this.jsonList[i]['yzxx'][j].sysd == null || this.jsonList[i]['yzxx'][j].sysd == undefined) {
                        this.jsonList[i]['yzxx'][j].sysd = " ";
                    }
                    if (this.jsonList[i]['yzxx'][j].sysddw == null || this.jsonList[i]['yzxx'][j].sysddw == undefined) {
                        this.jsonList[i]['yzxx'][j].sysddw = " ";
                    }
                    if (this.jsonList[i]['yzxx'][j].yyffmc == null) {
                        this.jsonList[i]['yzxx'][j].yyffmc = " ";
                    }
                    if (this.jsonList[i]['yzxx'][j]['ypbz'] != null) {
                        if (this.tzqm(j, i)) {
                            newList.push(this.jsonList[i]['yzxx'][j]);
                            newList.push({'fzh': null, 'ksrq': this.jsonList[i]['yzxx'][j]['ksrq']});
                        } else {
                            newList.push(this.jsonList[i]['yzxx'][j]);
                        }
                    }
                }
                this.jsonList[i]['yzxx'] = newList;
                // 加上空格子
                var sa = 0;
                if (this.jsonList[i]['yzxx'].length > 20) {
                    sa = 24 - ((this.jsonList[i]['yzxx'].length - 22 + 25) % 25);
                } else {
                    sa = 18 - this.jsonList[i]['yzxx'].length;
                }
                this.blankList = [];
                for (var a = 0; a < sa; a++) {
                    this.blankList.push({});
                    // this.jsonList[i]['yzxx'].push({'yyffmc': '', 'sysd': '', 'sysddw': '', 'isBlank': true});
                }
                // for (var a = 0; a < this.jsonList[i]['yzxx'].length; a++) {
                //     if (this.jsonList[i]['yzxx'][a]['yyffmc'] == null) {
                //         this.jsonList[i]['yzxx'][a]['yyffmc'] = '';
                //     }
                //     if (this.jsonList[i]['yzxx'][a]['sysd'] == null) {
                //         this.jsonList[i]['yzxx'][a]['sysd'] = '';
                //         this.jsonList[i]['yzxx'][a]['sysddw'] = '';
                //     }
                // }
            }
        },
        setData: function () {
            for (var i = 0; i < bodyMenu.yzzxdList.length; i++) {
                this.jsonList[i] = JSON.parse(JSON.stringify(bodyMenu.yzzxdList[i]));
            }
            this.setList();
        },
        sameDate: function (name, index, type) {
            for (var i = 0; i < bodyMenu.yzzxdList.length; i++) {
                var val = this.jsonList[i]['yzxx'][index][name];
                var prvVal = null, nextVal = null;
                if (index != this.jsonList[i]['yzxx'].length - 1 && index != 0) {
                    prvVal = this.jsonList[i]['yzxx'][index - 1][name];
                    nextVal = this.jsonList[i]['yzxx'][index + 1][name]
                }
            }

            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return (reDate.getMonth() + 1) + '-' + reDate.getDate();
            } else if (type == 'sj') {
                return reDate.getHours() + ':' + reDate.getMinutes();
            }
        },
        sameSE: function (index, num) {
            var fzh = this.jsonList[num]['yzxx'][index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[num]['yzxx'][index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList[num]['yzxx'].length - 1) {
                var nextFzh = this.jsonList[num]['yzxx'][index + 1]['fzh'];
                var prvFzh = this.jsonList[num]['yzxx'][index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        toTime: function (dateVal) {
            var reDate = new Date(dateVal);
            var str= (reDate.getMonth() + 1) + '-' + reDate.getDate() + '  ' + reDate.getHours() + ':' + reDate.getMinutes();
            var strs=str.split('-');
            var strs2=strs[1].split(' ');
            var strs3=strs2[2].split(':');
            if(parseInt(strs[0])<10){
            	strs[0]='0'+strs[0];
            }
            if(parseInt(strs2[0])<10){
            	strs2[0]='0'+strs2[0];
            }
            if(parseInt(strs3[0])<10){
            	strs3[0]='0'+strs3[0];
            }
            if(parseInt(strs3[1])<10){
            	strs3[1]='0'+strs3[1];
            }
            var rtstr=strs[0]+'-'+strs2[0]+' '+strs3[0]+":"+strs3[1];
            return rtstr;
        },
        tzqm: function (index, num) {
            if (this.sameSE(index, num) != 'start' && this.sameSE(index, num) != 'all') {
                return true;
            } else {
                return false;
            }
        },
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.yzd-table td').css('border', '1px solid transparent');
            $('.yzd-table-blank span:first-child').css('border-bottom', '1px solid transparent');
            $('.blank-list').css('display','none');
            $('.ysDiv').css('display', 'none');
            setTimeout(function () {
                window.print();
                $('.yzd-table td').css('border', '1px solid #999');
                $('.yzd-table-blank span:first-child').css('border-bottom', '1px solid #999');
                $('.blank-list').removeAttr('style');
                $('.ysDiv').removeAttr('style');
            }, 100);
        },
        isShowItem: function (num, index) {
            if (this.jsonList[num]['yzxx'][index + 1] == null) {
                return true;
            }
            if (this.jsonList[num]['yzxx'][index]['fzh'] == this.jsonList[num]['yzxx'][index + 1]['fzh']) {
                if (this.jsonList[num]['yzxx'][index]['yyffmc'] == this.jsonList[num]['yzxx'][index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});


/*********************************华丽分割线**************************************/
rightPcxx.getDate();//初始化页面加载所有频次信息
bodyMenu.initData();

/*********************************华丽分割线**************************************/
// 为table循环添加拖拉的div（病人基本信息列表）
var drawWidthNumBr = $(".patientTableBr tr").eq(0).find("th").length;
for (var i = 0; i < drawWidthNumBr; i++) {
    if (i >= 2) {
        $(".patientTableBr th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
    }
}

//})();

