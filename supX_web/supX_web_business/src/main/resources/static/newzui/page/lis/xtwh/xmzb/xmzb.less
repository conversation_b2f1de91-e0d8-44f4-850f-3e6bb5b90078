.xmzb-top{
  width: 100%;
  padding: 15px 34px;
  overflow: hidden;
}
.xmzb-top-left{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  i{
    margin-right: 5px;
    &:nth-child(2){
      margin-right: 19px;
    }
  }
}

.xmzb-content{
  width: 100%;
  padding:10px 10px 0;
  box-sizing: border-box;
  //float: left;
  //min-width: 1366px;
  //overflow-x: auto;
}
.xmzb-content-left{
  width: 35%;
  float: left;
  .content-left-top{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    border: 1px solid #e9eee6;
    background:#edf2f1;
    height: 36px;
    line-height: 36px;
    align-items: center;
    i{
      width: calc(~"(100% - 50px)/2");
      text-align: center;
      &:first-child{
        width: 50px;
      }
    }

  }
  .content-left-list{
    width: 100%;
    //height:75vh;
    overflow: auto;
    border-top: none;
    border-right: none;
    li{
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      //line-height: 54px;
      cursor: pointer;
      line-height: 40px;
      border: 1px solid #e9eee6;
      border-top: 1px solid #fff;
      i{
        width: calc(~"(100% / 3)");
        text-align: center;
      }
      &:nth-child(2n){
        background: #fdfdfd;
      }
      &:first-child{
        border-top: none;
      }
      &:hover{
        background:rgba(26,188,156,0.08);
        //border:1px solid #1abc9c;
      }
    }

  }
}
.xmzb-content-right{
  width: 63%;
  float: right;
  .content-right-top{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    border: 1px solid #e9eee6;
    background:#edf2f1;
    height: 36px;
    //line-height: 36px;
    align-items: center;
    i{
      width: calc(~"(100% / 6)");
      text-align: center;
    }
  }
  .content-right-list{
    width: 100%;
    //height:75vh;
    overflow: auto;
    border-top: none;
    li{
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      line-height: 40px;
      border: 1px solid #e9eee6;
      //border-top: 1px solid #fff;
      border-top:none;
      cursor: pointer;
      i{
        width: calc(~"(100% / 6)");
        text-align: center;
      }
      &:nth-child(2n){
        background: #fdfdfd;
      }
    &:hover{
      background:rgba(26,188,156,0.08);
      //border:1px solid #1abc9c;
    }
    }
  }

}
.xmzb-title{
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background:#edf2f1;
  border:1px solid #e9eee6;
  height:34px;
  i{
    width: calc(~"(100% / 5)");
    text-align: center;
  }
}
.xmzb-list{
  width: 100%;
  max-height: 320px;
  overflow: auto;
  li{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-top: 1px solid #e9eee6;
    height: 52px;
    &:nth-child(2n){
      background: #fdfdfd;
    }
    &:first-child{
      border-top: none;
    }
  }
  i{
    width: calc(~"(100% / 5)");
    text-align: center;
  }
}
.font16{
  font-size: 16px !important;
}
.xmzb-ok{
  width: 100%;
  height: 70px;
  border-top: 1px solid #e9eee6;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  button{
    margin-right: 15px;
  }
}
.font-icon{
  position: absolute;
  right:90px;
  top:3px;
  color: rgba(255,255,255,.8);
}
.min-chuangkou{
  right: 11px;
}