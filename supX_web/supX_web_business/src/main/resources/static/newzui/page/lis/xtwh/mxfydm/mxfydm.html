<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>明细费用对码</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../xmzb/xmzb.css"/>
    <link rel="stylesheet" href="mxfydm.css"/>
</head>
<style>
    .zui-table-view .fieldlist{
        display: none !important;
    }
    .xmzb-content-right .content-right-list li i{
        width: 80%;
        line-height: initial;
        height: initial;
    }
    .font14{
    font-size: 14px;}
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary " @click="savedata"><i class="icon-baocunb paddr-r5"></i>保存</button>
                <button class="tong-btn btn-parmary-b" @click="getData"><i class="icon-sx paddr-r5"></i>刷新</button>
                <button class="tong-btn btn-parmary-b " @click="guolu"><i class="icon-gl paddr-r5"></i>过滤</button>
                <button class="tong-btn btn-parmary-b "  @click="Print"><i class="icon-yl paddr-r5"></i>预览</button>
                <button class="tong-btn btn-parmary-b "><i class="icon-dysq paddr-r5"></i>打印</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline">
                        <label class="zui-form-label">检索码</label>
                        <div class="zui-input-inline margin-f-l15">
                            <input class="zui-input wh274" v-model="allSearch" placeholder="请输入关键字" type="text"/>
                        </div>
                    </div>
                    <!--<button class="zui-btn btn-primary xmzb-db">查询</button>-->
                </div>
            </div>
        </div>

    <div class="zui-table-view  padd-r-10 padd-l-10" >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m">
                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                           :type="'all'" :val="isCheckAll">
                    </input-checkbox></div></th>
                    <th><div class="zui-table-cell cell-s"><span>费用编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>费用名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>检验项目编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>检验项目名称</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body"  @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr @dblclick="dbEdit($index)" v-for="(item, $index) in jsonList" :key="item.fybm"
                    ref="list"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'some','jsonList'],$event)">
                    <td class="cell-m"><div class="zui-table-cell cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fybm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fymc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fydj"></div></td>
                    <td><div class="zui-table-cell cell-s"><input class="zui-input wh200 tool-center" v-model="item.jyxmbm" type="text"/></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jyxmmc"></div></td>

                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
<div id="pop">
    <!--<transition name="pop-fade">-->
    <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
    <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
        <div class="layui-layer-content" >
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="colse()">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
        </div>
    </div>
    <!--</transition>-->
</div>
<div class="side-form ng-hide" style="width:485px;padding-top: 0;" id="brzcList" role="form">
    <div class="tab-message">
        <a v-text="sideTitle"></a>
        <a href="javascript:;" class="fr closex ti-close" style="color:rgba(255,255,255,.56) !important;"
           @click="closes"></a>
    </div>
    <div class="ksys-side">
        <div class="zui-row">
            <div class="col-fm-12">
                <div class="col-fm-7 jiansuo">
                   <span>
                <i>检索码</i>
                   <input v-model="jyparam.parm" type="text" class="zui-input border-r4" placeholder="请输入"/>
                 </span>
                </div>
                <div class="xmzb-content-right pop-content sjks-content-right">
                    <div class="content-right-top">
                        <i>序号</i>
                        <i>项目名称</i>
                        <i>代码</i>
                    </div>
                    <ul class="content-right-list">
                        <li @dblclick="doEdit($index)" v-for="(item, $index) in jyList" :key="item.jyxmbm" >
                        	<span v-text="item.jyxmbm">001</span>
                        	<span v-text="item.jyxmmc">白蛋白</span>
                        	<span v-text="item.jyxmpy">FGHJ</span>
                        </li>
                    </ul>
                </div>
            </div>


        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
        </div>
    </div>

</div>
<!--<div class="filter">-->
    <!--<filter-select v-on:close="guanbi" v-on:save="baocun" v-if="isShow"></filter-select>-->
<!--</div>-->
<div id="isTabel">
    <div class="pophide" :class="{'show':isShow}"></div>
    <div class="zui-form podrag  bcsz-layer zui-800 " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title ">过滤查询</div>
        <div class="guolv-xinzeng">
            <span class="layui-txt" @click="append()">新增一项</span>
            <i class="color-btn" @click="isShow=false"
               style="margin-top:-17px;width: 16px;height: 16px;display: inline-block;margin-left: 10px;float: right">×</i>
        </div>
        <div class="layui-layer-content">
            <div class=" layui-mad">
                <ul class="guolv-header guolv-style">
                    <li class="line">项目</li>
                    <li class="line">条件</li>
                    <li class="line">结果</li>
                    <li class="line">连接条件</li>
                    <li class="line">操作</li>
                </ul>
                <ui class="guolv-content" id="guo_append">
                    <div class="guolv-style guolv-bottom" v-for="(list,index) in appNum" :key="list.num">
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext"  name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <!--<div class="zui-select-inline">-->
                            <!--<input type="text" class="zui-input lsittext" name="input1" check="required" />-->
                            <!--<div class="zui-select-group" role="listbox">-->
                            <!--<ul class="inner">-->
                            <!--<li value="0">中国</li>-->
                            <!--<li value="1">印度</li>-->
                            <!--<li value="2">安道尔</li>-->
                            <!--<li value="3">老挝</li>-->
                            <!--</ul>-->
                            <!--</div>-->
                            <input  class="zui-input"/>

                            <!--</div>-->
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <span class="icon-sc" @click="sc(index)"></span>
                        </li>
                    </div>

                </ui>
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="save()">保存</button>
        </div>
    </div>
</div>
<style>
    body{
        padding: 0;
    }

    .background{
        width: 98.9%;
    }
</style>
    <script src="mxfydm.js"></script>
</body>
</html>
