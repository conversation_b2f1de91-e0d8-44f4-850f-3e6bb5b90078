<style type="text/css">
    @media print {
        @page {
            size: 115mm 139mm;
            /*margin: 0 0.5mm 0;*/
        }
    }

    .bcd {
        width: 115mm;
        border-bottom: 1px solid #b3afaf;
        /*height: 130mm;*/
    }

    .title-bcd {
        font-size: 21px;
        font-weight: bold;
    }

    .bcd-content {
        font-size: 13px;
    }

    .wh50 {
        width: 50% !important;
        display: flex;
        align-items: center;
    }

    .text-line {
        width: 96px;
        border-left: 1px solid #b3afaf;
        border-right: 1px solid #b3afaf;
        justify-content: center;
    }

    .text-line, .line-value {
        height: 100%;
        color: #000 !important;
        display: flex;
        font-weight: bolder;
        align-items: center;
        padding: 4px 8px;
    }

    .line-value {
    }
    .bcd-content .wh100MAx{
        border: 1px solid #b3afaf;
        border-bottom: none;
        border-left: none;
    }
    .bcxq{
        width: 100%;
        height: 100%;
        text-align: center;
        border-left: 1px solid #b3afaf;
    }
</style>
<div class="bcd" id="bcd-box">
    <div class="title-bcd text-center padd-b-10">剑河县新型农村合作医疗门诊补偿单</div>
    <div class="  bcd-content">
        <div class="wh100MAx flex-container">
            <div class="wh50"><span class="text-line">就诊机构:</span><span class="line-value">{{yljgmc}}</span></div>
            <div class="wh50"><span class="text-line">补偿日期:</span><span class="line-value">{{fDate(d2.bcrq,'date')}}</span></div>
        </div>
        <div class="wh100MAx flex-container">
            <div class="wh50"><span class="text-line">补偿类型:</span><span class="line-value">{{d2.mzlx}}</span></div>
            <div class="wh50"><span class="text-line">人员属性:</span><span class="line-value">{{d2.rylx}}</span></div>
        </div>
        <div class="wh100MAx flex-container">
            <div class="wh50"><span class="text-line">患者姓名:</span><span class="line-value">{{d1.brxm}}</span></div>
            <div class="wh50"><span class="text-line">就诊号:</span><span class="line-value">{{d1.outpid}}</span></div>
        </div>
        <div class="wh100MAx flex-container">
            <div class="wh50"><span class="text-line">就诊日期:</span><span class="line-value">{{fDate(d1.inpatientdate,'date')}}</span></div>
            <div class="wh50"><span class="text-line">联系电话:</span><span class="line-value">{{d2.lxdh}}</span></div>
        </div>
        <div class="wh100MAx flex-container"><span class="text-line">疾病名称:</span><span
                class="line-value">{{d2.jbmc}}</span></div>
        <div class="wh100MAx flex-container"><span class="text-line">家庭地址:</span><span
                class="line-value">{{d2.jtdz}}</span></div>
        <div class="wh100MAx flex-container"><span class="text-line">医疗证号:</span><span
                class="line-value">{{d1.ylzh}}</span></div>
        <div class="wh100MAx flex-container"><span class="text-line">个人编号:</span><span
                class="line-value">{{d2.mzmberid}}</span></div>
        <div class="wh100MAx flex-container">
            <div class="wh50"><span class="text-line">医疗总费用:</span><span class="line-value">{{d2.fyje}}</span></div>
            <div class="wh50"><span class="text-line">自费费用:</span><span class="line-value">{{d2.zfje}}</span></div>
        </div>
        <div class="wh100MAx">
            <div class="wh50 "><span class="text-line">补偿金额:</span><span class="line-value">{{d2.bcfy}}</span></div>
        </div>
        <div class=" wh100MAx flex-container flex-jus-c flex-align-c  "><span class="bcxq padd-t-5 padd-b-5">补偿详情</span></div>
        <div class="wh100MAx flex-container">
            <div class="wh50"><span class="text-line">农合:</span><span class="line-value">{{d1.compensatecost}}</span></div>
            <div class="wh50"><span class="text-line">民政:</span><span class="line-value">{{d1.civilcost}}</span></div>
        </div>
        <div class="wh100MAx flex-container">
            <div class="wh50"><span class="text-line">兜底补偿:</span><span class="line-value">{{d1.bottomredeem}}</span></div>
            <div class="wh50"><span class="text-line">大商:</span><span class="line-value">{{d1.insurecost}}</span></div>
        </div>
        <div class="wh100MAx flex-container  ">
            <span class="text-line">补偿合计大写:</span><span class="line-value">{{d2.bcfy}}</span>
        </div>
        <div class="wh100MAx flex-container"><span class="text-line">打印时间:</span><span
                class="line-value">{{fDate(d2.dysj,'date')}}</span></div>
        <div class="wh100MAx flex-container">
            <div class="wh50"><span class="text-line">病人或家属签字:</span><span class="line-value"></span></div>
            <div class="wh50"><span class="text-line">操作员:</span><span class="line-value">{{d2.czyxm}}</span></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var printPageBox = new Vue({
        el: '#bcd-box',
        mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
        data: {
            d2: {},
            d1: {},
            yljgmc:sessionStorage.getItem('yljgOrUser') &&  JSON.parse(sessionStorage.getItem('yljgOrUser')).yljgmc,
        },
        mounted: function () {

            this.getPrintData();
        },
        methods: {
            getPrintData: function () {
                var that = this;
                //  325209395||
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryBcdxx&parm="+JSON.stringify({'outpid':gz_001.bcdxxContent.outpid}), function (json) {
                    if (json.a == 0) {
                        that.d1=json.d.d1 || {}
                        that.d2=json.d.d2 || {};
                        that.$nextTick(function () {
                            window.print();
                             $('#printPage').html('')
                             $('#printArea').html('')
                        })
                    }else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });
            },

        },
    })
</script>
