
var hyjl = new Vue({
    el: '.tong-top',
    mixins: [dic_transform, baseFunc, tableBase, mformat],

    data: {
        popConent:{},
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},

    },
    methods: {
        addDj(){
            wap.popContent = {};
            wap.open();
        }

    
    }
});
var hyjl1 = new Vue({
    el: '.hyjls',
    mixins: [dic_transform, baseFunc, tableBase, mformat],

    data: {
        popConent:{},
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},

    },
    methods: {



    }
});


var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],
    components: {
        'search-table': searchTable
    },
    data: {
        nums:0,//弹窗
        title: '入院信息',
        popContent:{
            zyh:"22222"
        },
    },

    methods: {
        closes: function () {
            this.nums=0;

        },
        open: function () {
            this.nums=1;
        },
        //保存
        save : function(){

        }
    }


});
// 出生日期
laydate.render({
    elem: '.sctimes'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {
        wap.popContent.csrq = value;
    }
});
laydate.render({
    elem: '.sctimes1'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {
        wap.popContent.ryrq = value;
    }
});

$(window).resize(function () {
    changHeight();
})
setTimeout(function () {
    changHeight();
}, 150)

