<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta charset="UTF-8" />
    <title>单规则质控</title>
    <script type="application/javascript" src="/newzui/pub/tops.js" charset="utf-8"></script>
    <script type="application/javascript" src="/newzui/pub/js/highcharts.js" charset="utf-8"></script>
    <link rel="stylesheet" href="zk.css" charset="utf-8"/>
	<style type="text/css">
		.l-right-tx .l-time-bg span{
			line-height: 24px;
			border: 1px solid #757c83;
			border-right: 1px solid #333333;
		}
		.l-right-tx .l-time-bg span .ii {
    		border-right: 1px solid #333333;
		}
		.inp{
		width: 60%;
    padding: 0 10px;
    height: 36px;
    line-height: 36px;
    border: 1px solid #f9f9f9;
    background-color: #fff;
    color: #767d85;
    font-size: 14px;
    border-radius: 4px;}
	</style>
</head>
<body>
<div class="printHide">
<div class="body-box">
    <div class="box-size background-none">
        <div class="l-zk-left">
            <div class="l-zk-top">
                <!--@click="miaoshu(list)"-->
                <button class="tong-btn btn-parmary icon-zkms padd-l28" >质控描述</button>
            </div>
            <div class="l-zk-jysb background-ff">
                <div class="zui-form">
                    <div class="zui-inline padd-l64 margin-l15">
                        <label class="zui-form-label">检验设备</label>
                        <div class="zui-select-inline">
                            <select-input @change-data="resultChange"
                                          :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="jysbObj.sbbm"
                                          :search="true" :name="'jysbObj.sbbm'">
                            </select-input>
                        </div>
                    </div>
                    <div class="zui-inline padd-l78 margin-l10">
                        <label class="zui-form-label">质控物批号</label>
                        <div class="zui-input-inline">
                            <input type="text" class="zui-input" name="input1" placeholder="请输入质控物批号" v-model="jysbObj.zkwph" @input="getSbbm"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zui-table-view zui-scroll-left"  z-height="full">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th z-field="id" z-fixed="left" z-width="40px">
                                <div class="zui-table-cell"><span>序号</span></div>
                            </th>
                            <th  z-field="xiangmu" z-width="80px" z-style="text-align:center;">
                                <div class="zui-table-cell"><span>项目名称</span></div>
                            </th>
                            <th z-field="jiancheng" z-width="60px">
                                <div class="zui-table-cell"><span>简称</span></div>
                            </th>
                            <th z-field="pihao" z-width="80px" >
                                <div class="zui-table-cell"><span>质控批号</span></div>
                            </th>
                            <th z-field="shebei" z-width="80px">
                                <div class="zui-table-cell"><span>执行设备</span></div>
                            </th>
                            <th z-field="cankao" z-width="60px">
                                <div class="zui-table-cell"><span>参考靶子</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="slimscroll zui-table-body"  role="treeitem">
                    <table class="zui-table" id="ice">
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,$index),choices(list)"
                            :tabindex="$index" v-for="(list, $index) in zklist">
                            <td width="50px"><div class="zui-table-cell" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell" v-text="list.zbmc"></div></td>
                            <td><div class="zui-table-cell" v-text="list.zbjc"></div></td>
                            <td><div class="zui-table-cell" v-text="list.zkwph"></div></td>
                            <td><div class="zui-table-cell">
                                <div class="title" data-title="list.sbmc" v-text="list.sbmc"></div>
                            </div></td>

                            <td><div class="zui-table-cell" v-text="list.x"></div></td>
                        </tr>
                       </tbody>
                    </table>
                </div>
            </div>

        </div>
        <div class="l-zk-right">
            <div class="l-zk-top">
                <button class="tong-btn btn-parmary icon-zkms padd-l28" @click="jglr('','','top')">结果录入</button>
                <button class="tong-btn btn-parmary-b icon-yl paddr-r5"  @click="hqjg">获取结果</button>
                <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="scall()">删除</button>
                <button class="tong-btn btn-parmary-b icon-dysq paddr-r5"  @click="print">打印</button>
                <div class="l-bgtx">
                        <span class="l-span-line" :class="{'active':num==0}" @click="tabBg(0)"><i class="icon-bg" :class="{' icon-bg-hover':num==0}"></i><em>表格</em></span>
                        <span :class="{'active':num==1}"><i class="icon-tx" @click="tabBg(1)" :class="{'icon-tx-hover':num==1}"></i><em>图形</em></span>
                </div>
            </div>
            <div class="l-zk-jysb background-ff">
                <div class="zui-form">
                    <div class="zui-inline padd-l64 margin-l15">
                        <label class="zui-form-label">日期</label>
                        <div class="zui-input-inline zui-select-inline zui-date">
                            <i class="datenox icon-rl"></i>
                            <input type="text" name="phone" class="zui-input todate padd-l33 text-indent20" not_empty="false"  placeholder="请选择申请日期" v-model="param.time">
                        </div>
                    </div>
                    <div class="zui-inline padd-l78 margin-l10">
                        <label class="zui-form-label">质控类型</label>
                        <div class="zui-input-inline">
                            <select-input @change-data="resultChange" :not_empty="false" :child="jydjzklx_tran"
                                          :index="'jysbObj.zklx'"  :val="jysbObj.zklx"
                                          :name="'jysbObj.zklx'" :search="true">
                            </select-input>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zui-table-view zui-scroll-right" id="utable1" z-height="full" >
                <div class="zui-table-header"  v-show="num==0">
                    <table class="zui-table dang-tell">
                        <thead>
                        <tr>
                            <!-- <th z-field="xz" z-fixed="left" z-width="40px" style="width: 50px !important;">
                                <input-checkbox @result="reCheckBox" :list="'getchilddata'"
                                                                            :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th> -->
                            <td class="i" style="width: 50px"><i>
                            <input-checkbox @result="reCheckBox" :list="'getchilddata'" :type="'all'" :val="isCheckAll">
                            </input-checkbox></i></td>
                    
                            <th  z-field="xh" z-width="80px" z-style="text-align:center;">
                                <div class="zui-table-cell"><span>序号</span></div>
                            </th>
                            <th z-field="cs" z-width="120px">
                                <div class="zui-table-cell"><span>测试日期</span></div>
                            </th>
                            <th z-field="zm" z-width="80px" >
                                <div class="zui-table-cell"><span>质控项目</span></div>
                            </th>
                            <th z-field="ph" z-width="80px">
                                <div class="zui-table-cell"><span>批号</span></div>
                            </th>
                            <th z-field="sb" z-width="100px">
                                <div class="zui-table-cell"><span>执行设备</span></div>
                            </th>
                            <th z-field="jieguo" z-width="50px">
                                <div class="zui-table-cell"><span>结果</span></div>
                            </th>
                            <th z-field="jx" z-width="50px">
                                <div class="zui-table-cell"><span>X</span></div>
                            </th>
                            <th z-field="sd" z-width="50px">
                                <div class="zui-table-cell"><span>sd</span></div>
                            </th>
                            <th z-field="cv" z-width="50px">
                                <div class="zui-table-cell"><span>Cv</span></div>
                            </th>

                            <th z-field="zf" z-width="50px">
                                <div class="zui-table-cell"><span>作废</span></div>
                            </th>
                            <th z-field="sfjs" z-width="50px">
                                <div class="zui-table-cell"><span>是否计算</span></div>
                            </th>
                            <th z-fixed="right" z-field="caozuo" z-width="56px">
                                <!-- <div class="zui-table-cell"><span>操作</span></div> -->
                                <i class="title overflow1">操作<em class="icon-bgzdgl"></em></i>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="slimscroll"  role="treeitem">
                    <div class="zui-table-body">
                    <table class="zui-table ice"   v-show="num==0">
                        <tbody>
                        <tr @dblclick="slideEdit(item,item.id,'center')" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,$index)"  v-for="(item,index) in getchilddata">
                           <!--  <td class="ls-td">
                                    <input-checkbox @result="reCheckBox" :list="'getchilddata'"
                                                    :type="'some'" :which="index"
                                                    :val="isChecked[index]">
                                    </input-checkbox>
                            </td> -->
                            <td class="i" style="width: 50px"><i>
                            <input-checkbox @result="reCheckBox" :list="'getchilddata'" :type="'some'" 
                            				:which="index" :val="isChecked[index]">
                            </input-checkbox></i></td>
                            
                            <td style="width: 87px;"><div class="zui-table-cell" v-text="index+1"></div></td>
                            <td style="width: 130px"><div class="zui-table-cell">{{item.rq | formDate}}</div></td>
                            <td style="width: 87px"><div class="zui-table-cell" v-text="item.zbmc">门诊收费</div></td>
                            <td style="width: 87px"><div class="zui-table-cell" v-text="item.zkwph">常规</div></td>
                            <td  style="width:109px"><div class="zui-table-cell" v-text="item.sbmc">执行设备</div></td>
                            <!--<td><div class="zui-table-cell" v-text="item.sbmc">执行设备</div></td>-->
                            <td  style="width:54px"><div class="zui-table-cell" v-text="item.jg">女</div></td>
                            <td style="width:54px"><div class="zui-table-cell" v-text="item.x">24</div></td>
                            <td style="width:54px"><div class="zui-table-cell">
                                <div class="title" data-title="item.sd" v-text="item.sd">ARO+RH血</div>
                            </div></td>
                            <td style="width:54px"><div class="zui-table-cell" v-text="item.cv">1.2</div></td>
                            <td style="width:55px"><div class="zui-table-cell" :class="item.zfbz==1?'color-red':''" v-text="zf_tran[item.zfbz]">已作废</div></td>
                            <!--<td><div class="zui-table-cell color-red"  v-text="item.zfbz?"作废":"未作废"">已作废</div></td>-->
                            <td style="width:56px"><div class="zui-table-cell"  v-text="item.bjsjg==1?'是':'否'">是</div></td>
                            <td style="width:56px">
                                <div class="zui-table-cell">
                                    <!-- <i class="icon-ms" @click="slideEdit(item,item.id)"></i>
                                    <i class="icon-sc"  @click="sc(index,item.id,item.zbmc)"></i> -->
                                    <i><span class="icon-bj" @click="slideEdit(item,item.id)"></span>
                                    <span @click="sc(index,item.id,item.sbmc)" class="icon-sc"></span></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    </div>
                    <div class="printDiv">
                    	<div class="l-right-tx" style="z-index: 1" :class="{'disShow':num==1}"  role="treeitem" v-show="num==1">
                        <h2>临床检验Levey Jennings 质控图</h2>
                        <div class="l-tx-title">
                            <span class="l-tx-col4">单位：{{jgmc}}</span>
                            <span class="l-tx-col5">起止日期：{{start|formDatehanzi}} 至 {{end|formDatehanzi}}</span>
                            <span class="l-tx-col3">试验项目：{{xbmc}}</span>
                            <span class="l-tx-col4">有效期：{{yxrq |formDatehanzi}}</span>
                            <span class="l-tx-col5">仪器型号：{{jysb.hostname}} {{jysb.machinetype}}</span>
                            <span class="l-tx-col3">样本批号：{{zkwph}}</span>
                            <span class="l-tx-col4">质控靶值：X{{ckzObj.xCkz}} SD={{ckzObj.sdCkz}} CV={{ckzObj.cvCkz}}%</span>
                            <span class="l-tx-col5">本月靶值：x={{sum}} SD={{stddev}} CV={{byxs}}%</span>
                            <!-- <span class="l-tx-col12">
                            	<span>质控靶值：X{{ckzObj.xCkz}} SD={{ckzObj.sdCkz}} CV={{ckzObj.cvCkz}}%</span>
                            	<span style="margin-left:10%">本月靶值：x={{sum}} SD={{stddev}} CV={{byxs}}%</span>
                            </span> -->
                        </div>
                        <div style="clear: both"></div>
                        <!--<div class="l-zktu">-->
                            <!--<canvas id="chart" style="width: 100%;height: 100%" > 你的浏览器不支持HTML5 canvas </canvas>-->
                        <div class="apphtml" style="width: 95%;height: 300px;margin-left: 5%;position: relative" >
                            <p style="position: absolute;z-index: 11;
                            left: -25px" v-for="(item,index) in hiclist"
                             :style="{top:item==1?0:(item-1)*30+'px',color: (index==1 || index==7) ? '#ff4532' :  (index==4 || index==8)? '#030303' : '#2885e2'}">
                             {{datatestarr[index]}}
                             </p>
                            <div id="container" style="width: 100%;height:100%;" ></div>
                        </div>
                        <!--</div>-->
                        <div class="l-zk-cdjg">
                            <span></span>
                        </div>
                        
                        <div class="l-zk-time">
                            <div class="l-time-top">
                                <i>日期</i>
                                <i>结果</i>
                                <i>检验人</i>
                                
                                <i>日期</i>
                                <i>结果</i>
                                <i>检验人</i>
                                
                                <i>日期</i>
                                <i>结果</i>
                                <i>检验人</i>
                            </div>
                            <div class="l-time-bg">
                                <span v-for="(list,index) in newLielist">
                                	<i v-if="list.rq != ''" style="border-left: 1px solid #333333">{{list.rq|formDate}}</i>
                                	<i v-if="list.rq == ''" style="border-left: 1px solid #333333"></i>
                                	<i>{{list.jg}}</i>
                                	<i class="ii">{{list.czyxm}}</i>
                                	
                                	<i v-if="list.rq1 != ''">{{list.rq1|formDate}}</i>
                                	<i v-if="list.rq1 == ''"></i>
                                	<i>{{list.jg1}}</i>
                                	<i class="ii">{{list.czyxm1}}</i>
                                	
                                	<i v-if="list.rq2 != ''">{{list.rq2|formDate}}</i>
                                	<i v-if="list.rq2 == ''"></i>
                                	<i>{{list.jg2}}</i>
                                	<i class="ii">{{list.czyxm2}}</i>
                                </span>
                            </div>
                        </div>
                        <div class="l-zk-cdjg">
                            <span class="l-zk"></span>
                        </div>
                        <div class="l-zk-popel"></p>
                        	<textarea v-model="zkms"  rows="" cols="" style="width:98%;height: 220px">{{zkms}}</textarea>
						</div>
                        <div class="l-zk-bgr">
                            <span style="margin-right: 5%">报告人：</span>
                            <span style="margin-right: 10%">审核人：</span>
                        </div>

                    </div>
                    </div>

                </div>
            </div>
            <div></div>
            <div class="zk-right-fixed">
                <!-- <span>靶值：<i v-if="values.length" :id="redure">{{ckzObj.xCkz}}</i></span>
                <span>标准差：<i v-if="values.length" :bz="bz">{{ckzObj.sdCkz}}</i></span>
                <span>变异系数：<i  v-if="values.length" :byxs="byxs">{{ckzObj.cvCkz}}%</i></span> -->
                <span>靶值：<i v-if="values.length" >{{ckzObj.xCkz}}</i></span>
                <span>标准差：<i v-if="values.length" >{{ckzObj.sdCkz}}</i></span>
                <span>变异系数：<i  v-if="values.length">{{ckzObj.cvCkz}}%</i></span>
                <span>本月靶值：<i v-if="values.length">{{sum}}</i></span>
                <span>本月标准差：<i  v-if="values.length">{{stddev}}</i></span>
                <span  >本月变异系数：<i v-if="values.length">{{byxs}}%</i></span>
            </div>
        </div>
    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide" style="width:548px;padding-top: 0;"  id="brzcList" role="form">
    <div class="tab-message">
        <a v-text="title"></a>
        <a href="javascript:;" class="icon-cha" @click="AddClose"></a>
    </div>
   <div class="l-width100"  v-show="msShow=='0'">
    <div class="l-zk-ms">
        <div class="l-zk-col-6">
            <label><i>检验设备</i>
                <input type="text" class="zui-input wh180 margin-l5 l-bgColor" value="" disabled/>
            </label>
        </div>
        <div class="l-zk-col-6">
            <label><i>质控项目</i>
                <input type="text" class="zui-input wh180 margin-l5 l-bgColor" value="" disabled/>
            </label>
        </div>
        <div class="l-zk-col-6">
            <label><i>质控月份</i>
                <input type="text" class="zui-input wh180 margin-l5 l-bgColor" value="" disabled/>
            </label>
        </div>
    </div>
    <div class="l-zk-ms margin-t20">
        <label>质控描述</label>
        <textarea placeholder="请输入描述内容" class="l-textarea"></textarea>
    </div>
        <div class="l-bottom-fixed">
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default btn-width" @click="AddClose">取消</button>
                <button class="zui-btn btn-primary btn-width margin-r15" @click="lightOk">确定</button>
            </div>
        </div>
    </div>
    <!--录入-->
    <div class="l-width100" v-show="msShow=='1'">
        <div class="l-zk-ms" style="padding-bottom:40px;">
            <div class="l-zk-col-6">
                <label><i>中文名称</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-bgColor" disabled v-model="datalist.zbmc"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>简称</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-bgColor" disabled v-model="datalist.zbjc"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>批号</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-bgColor"  disabled v-model="datalist.zkwph"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>产地</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-bgColor" disabled v-model="datalist.cd"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>检测设备</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-bgColor"  disabled v-model="datalist.sbmc"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>检验方法</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-bgColor"  disabled v-model="datalist.jyff"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label class="zui-select-inline"><i>有效期</i>
                        <em class="l-data icon-rl"></em>
                    <input type="text" class="zui-input wh180 margin-l5 l-time " placeholder="请选择日期" v-model="datalist.yxrq"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label class="zui-select-inline"><i>检测日期</i>
                    <em class="l-data icon-rl"></em>
                    <input type="text" class="zui-input wh180 margin-l5 l-times "  placeholder="请选择日期" v-model="datalist.rq"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>检测结果</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35 padd-r50"  v-model="datalist.jg"/>
                    <em class="l-dw-after"></em>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label  class="zui-select-inline"><i>质控类型</i>
                    <!--<input type="text" class="zui-input wh180 margin-l5 l-color35" value="" placeholder="请选择类型"/>-->
                        <!--<select-input @change-data="resultChange" :not_empty="true"-->
                        <!--:child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="item.zxsb"-->
                        <!--:search="true" :name="'item.zxsb'">-->
                        <!--</select-input>-->
                    <div class="wh180 margin-l5 l-color35"><select-input @change-data="resultChange" :not_empty="true" :child="jydjzklx_tran"
                                  :index="'datalist.zklx'"  :val="datalist.zklx"
                                  :name="'datalist.zklx'" :search="true">
                    </select-input></div>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>标准X</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35"  placeholder="请输入标准x" v-model="datalist.x"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>标准SD</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35"   placeholder="请输入标准SD" v-model="datalist.sd"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <label><i>标准CV</i>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35"  placeholder="请输入标准标准CV" v-model="datalist.cv"/>
                </label>
            </div>
            <div class="l-zk-col-6">
                <span class="l-label-left">
                    <!--<input type="checkbox" class="margin-r5">作废-->
                <input type="checkbox" id="items" class="green" v-model="datalist.zfbz"/><label
                  for="items" class="margin-r5"></label>作废

                </span>
                <span class="l-label-right">
                    <!--<input type="checkbox" class="margin-r5">-->
                <input type="checkbox" id="items1" v-model="datalist.bjsjg" class="green"  /><label
                            for="items1" class="margin-r5"></label>不参与计算
                </span>
            </div>
        </div>
        <div class="l-bottom-fixed">
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default btn-width" @click="AddClose">取消</button>
                <button class="zui-btn btn-primary btn-width margin-r15" @click="jgOk">确定</button>
            </div>
        </div>
    </div>
    <!--编辑-->

</div>

<div id="pop">
    <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
    <!--<div class="zui-form podrag pop-width bcsz-layer " :class="[{show:isShow},flag ?'pop-850':'pop-width']" style="height: max-content;padding-bottom: 20px">-->
    <div class="zui-form podrag pop-width bcsz-layer" :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin"><i class="icon-cha" @click="isShowpopL=false,isShow=false"></i></span>
        <div class="layui-layer-content">
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default btn-width" @click="isShowpopL=false,isShow=false">取消</button>
            <button class="zui-btn btn-primary btn-width margin-r15" @click="delAll">确定</button>
        </div>
    </div>
    <!--</transition>-->
</div>
</div>
</body>
<script type="text/javascript" src="zk.js"></script>
<script type="text/javascript">
    $(function () {
        $(".zui-form input").uicomplete();
        $(".zui-table-body input").uicomplete();
        $(".zui-table-view").uitable();
        $(".zui-scroll-left,.zui-scroll-right,.l-scroll-right").uiscroll({ height: '70%', size: '6px', opacity: .3 ,disableFadeOut:true,position:'right',color:'#000'});
    })
</script>
</html>