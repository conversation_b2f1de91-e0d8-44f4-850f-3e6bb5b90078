var qxksbm = '';
    var rksh = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
        data: {
            //打印数据
            printData: {},
            isShowkd: true,
            isShow: false,
            rkdList: [], //入库单集合
            jsonList: [],
            yfList: [],
            json: {},
            csParm: {},
            ryList: [],
            zdy: userId,
            sldmxList: [],
            bsdContent: {
                ckfs: "03", //报损方式
                lyr: userId //操作人
            },
            bsdList: [], //入库单集合
            tkdDetail: [],
            rkd: {}, //入库单对象
            objItem: {},
            mxShShow: true,
            thdList: [], //退货单集合
            param: {
                page: 1,
                rows: 20,
                sort: "",
                order: "asc",
                parm: ""
            },
            isCheck: null,
            thdDetail: [], //退货单明细集合
            dateBegin: null,//getTodayDateBegin(),
            dateEnd: null,//getTodayDateEnd(),
            time: {
                test: 'hello!'
            },
            t: {},
            zhuangtai: {
                "0": "待审核",
                "1": "已审核",
                "2": "作废",
            },
            totlePage: 0,
			tysl:0,
			isCheckAll:true,
        },
        updated:function(){
            changeWin()
        },
        methods: {
			checkSelectSon:function(index){
				var yzStatus = !this.sldmxList[index].isChecked;
				if(!yzStatus){
					yzStatus = false;
				}
				Vue.set(this.sldmxList[index], 'isChecked', yzStatus);
				
				if (yzStatus) {
				    var yzIsOverCk = true;
				    for (var x = 0; x < this.sldmxList.length; x++) {
				        if (!this.sldmxList[x].isChecked) {
				            yzIsOverCk = false;
				            break;
				        }
				    }
				    this.isCheckAll = yzIsOverCk;
				} else {
				    this.isCheckAll = false;
				}
				this.$forceUpdate()
				
			},
			reCheckBoxSon:function(){
				var isCheckAll = this.isCheckAll ? false : true;
				this.isCheckAll = isCheckAll;
				for (var x = 0; x < this.sldmxList.length; x++) {
				    this.sldmxList[x].isChecked = isCheckAll;
				}
			},
            loadNum: function () {
                this.num = wrapper.num;
            },
            //打印
            print: function () {
                //更新打印次数
                var parm  = {
                    'sldh' : wrapper.ckdh
                };
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=updateDycs&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            var reportlets = "[{reportlet: 'fpdy%2Fejkf%2Fyfgl_lyckd.cpt',yljgbm:'" + jgbm + "',yfbm:'" + wrapper.yfbm + "',ckdh:'" + wrapper.ckdh + "'}]";
                            if (!FrPrint(reportlets, null)) {
                                window.print();
                            }
                        } else {
                            malert(data.c, 'top', 'defeadted');
                        }
                });


            },
            //判断是否有操作权限
            hasCx: function (cx) {
                if (!cx) {
                    malert("用户没有操作权限！", 'top', 'defeadted');
                    return true;
                }
            },
            //进入页面加载单据列表信息
            getData: function () {
                common.openloading('.zui-table-view');
                this.dateBegin = rksh.param.beginrq;
                this.dateEnd = rksh.param.endrq;

                var parm = {
                    beginrq: this.dateBegin,
                    endrq: this.dateEnd,
                    yfbm: wrapper.yfbm,
                    parm: wrapper.search,
                    page: rksh.param.page,
                    rows: rksh.param.rows
                };
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=Sldcx&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            rksh.bsdList = data.d.list;
                            rksh.totlePage = Math.ceil(data.d.total / rksh.param.rows);
                        } else {
                            malert(data.c, 'top', 'defeadted');
                        }
                    });
                common.closeLoading()
            },
            //审核传值
            passData: function () {
                var json = this.bsdList[this.isCheck];
                json.parm = '1';
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=ZfShSld&yfbm=',
                    JSON.stringify(json)).then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.isShow = false;
                        wrapper.isShowkd = true;
                        rksh.isShow = false;
                        rksh.isShowkd = true;
                        rksh.getData();
                        malert("审核成功！", 'top', 'success')

                        var kfksbm = rksh.listGetName(wrapper.KFList, json.kfbm, 'kfbm', 'ksbm');
                        var sendmsg = {
                            msgtype: '503',
                            ksbm: kfksbm,
                            yljgbm: jgbm,
                            yqbm: yqbm,
                            msg: '有新的领药申请,请悉知!',
                            toaddress: 'page/ykgl/kfyw/ckgl/ckgl.html',
                            pagename: '出库管理',
                            sbid: json.sldh + "_" + rksh.fDate(new Date(), 'YY'),
                            ylbm: 'N040100021004',
                        };
                        console.log(sendmsg);
                        window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
            },
            //选中单据信息加载出相对应的单据内容明细
            showDetail: function (index, item) {
                wrapper.kfbm = this.bsdList[index].kfbm;//库房编码
                wrapper.ckdh = this.bsdList[index].sldh;//申领单号
                wrapper.objItem = this.bsdList[index];
                this.objItem = this.bsdList[index];
                this.isCheck = index;
                wrapper.isShow = true;
                wrapper.isShowkd = false;
                rksh.isShow = true;
                rksh.isShowkd = false;
                //根据状态判断
                if (this.bsdList[index].shzfbz == 0) {
                    this.mxShShow = true;
                } else {
                    this.mxShShow = false;
                }
                wrapper.zdyxm = this.bsdList[index].zdrmc;
                wrapper.zdrq = rksh.fDate(this.bsdList[index].zdrq, 'date');
                wrapper.bzms = this.bsdList[index].bzms;
                wrapper.yfbm = this.bsdList[index].yfbm;
                wrapper.kfbm = this.bsdList[index].kfbm;
                //发送请求，查询明细
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=sldmxcx' +
                    '&parm=' + JSON.stringify(item),
                    function (data) {
                        if (data.a == "0") {
							for (let i = 0; i < data.d.list.length; i++) {
								data.d.list[i].isChecked = true;
							}
                            rksh.sldmxList = data.d.list;
                        } else {
                            malert(data.c,'top','defeadted');
                        }

                    });
            },

            //编辑
            bianji: function (index, item) {
                wrapper.addShow = true;
                wrapper.isShowkd = false;
                wrapper.isShow = true;
                rksh.isShowkd = true;
                rksh.isShow = true;
                rksh.isShowkd = false;
                rksh.addShow = true;
                this.isCheck = index;
                this.isCheckSld = item;
                wrapper.kfbm = this.bsdList[index].kfbm;//库房编码
                rksh.mxShShow = true;
                this.ShShow = false;
                wrapper.zdyxm = this.bsdList[index].zdrmc;
                wrapper.zdrq = rksh.fDate(this.bsdList[index].zdrq, 'date');
                wrapper.bzms = this.bsdList[index].bzms;
                wrapper.yfbm = this.bsdList[index].yfbm;
                wrapper.kfbm = this.bsdList[index].kfbm;
                //发送请求，查询明细
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=sldmxcx' +
                    '&parm=' + JSON.stringify(item),
                    function (data) {
                        if (data.a == "0") {
							for (let i = 0; i < data.d.list.length; i++) {
								data.d.list[i].isChecked = true;
							}
                            rksh.sldmxList = data.d.list;
                        } else {
                            malert(data.c,'top','defeadted');
                        }

                    });
            },

            //拒绝
            jujue: function () {
                // //出库单非空判断
                // if(rksh.tkdContent.length == 0) {
                //     return;
                // }
                if (wrapper.kfbm == null || wrapper.kfbm == undefined || wrapper.kfbm == "") {
                    malert('请选择库房', 'top', 'defeadted');
                    return;
                }
                var json = {
                    "ckdh": this.bsdList[this.isCheck]['sldh'],
                    "kfbm": wrapper.kfbm,
                    "shzfbz": '-3'
                    // "qxksbm": qxksbm
                };

                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywtkd&types=passTkd', JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == "0") {
                            //打印数据
                            // this.print();
                            wrapper.isShow = false;
                            wrapper.isShowkd = true;
                            rksh.isShow = false;
                            rksh.isShowkd = true;
                            rksh.getData();
                            malert("已拒绝！",'top','defeadted')
                        } else {
                            rksh.getData();
                            malert(data.body.c,'top','defeadted');
                        }
                    });
            },

            //作废退库单2018/07/09二次弹窗作废提示
            invalidData: function (num) {
                if (num != null && num != undefined) {
                    this.isCheck = num;
                }
                if (common.openConfirm("确认作废该条信息吗？", function () {
                    var json = {
                        "sldh": rksh.bsdList[rksh.isCheck]['sldh'],
                        "kfbm": wrapper.kfbm,
                        "parm": '2'
                    };
                    rksh.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=ZfShSld', JSON.stringify(json)).then(function (data) {
                        if (data.body.a == "0") {
                            rksh.getData();
                            malert('作废成功', 'top', 'success');
                            rksh.cancel();
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    });
                })) {
                    return false;

                }

                // var son = {
                //     "ckdh": this.bsdList[this.isCheck]['sldh'],
                //     "kfbm": wrapper.kfbm
                // };
                // this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywBsd&types=zfBsd',
                //     JSON.stringify(json))
                //     .then(function (data) {
                //         if (data.body.a == "0") {
                //             rksh.getData();
                //             malert("作废成功！",'top','success')
                //         } else {
                //             malert(data.body.c);
                //         }
                //     });
            },


            //提交所有设备
            submitAll: function () {
                //是否禁止提交
                if (this.isSubmited) {
                    malert("数据提交中，请稍候！", 'top', 'defeadted');
                    return false;
                }
                //判断提交数据正确性
                if (this.sldmxList.length <= 0) {
                    malert("没有可提交的数据", 'top', 'defeadted');
                    return false;
                }
				let tpTc = false;
				for (let i = 0; i < this.sldmxList.length; i++) {
					if(this.sldmxList[i].sjkc =='0'){
						tpTc = true;
						break;
					}
					
				}
				/*if(tpTc){
					malert("申领设备中有库存为0", 'top', 'defeadted');
				}*/
				
				
                //是否禁止提交
                this.isSubmited = true;
                //备注描述
                var bzms = wrapper.bzms;
                if (bzms == undefined || bzms == null || bzms == "") {
                    bzms = "领用出库";
                }
                wap.bsdContent.kfbm = wrapper.kfbm;
                //准备数据，包括退货单对象和退货单明细对象
                // var json = {
                //     "list": {
                //         "sld": wap.bsdContent,
                //         "sldmx": this.sldmxList
                //     }
                // };
                var sld = {
                    "yfbm": wrapper.yfbm,
                    "kfbm": wrapper.kfbm,
                    "bzms": bzms,
                    "zbr": wrapper.bsdContent.zbr,
                };
				
				let tjlist = [];
				
				for (let i = 0; i < this.sldmxList.length; i++) {
					if(this.sldmxList[i].isChecked){
						tjlist.push(this.sldmxList[i]);
					}
				}
				if(tjlist.length==0){
					malert('请勾选需要领用的设备', 'top', 'defeadted');
					return false;
				}
                var json = {
                    "list": {
                        "sld": sld,
                        "sldmx": tjlist
                    }
                };
                if (this.bsdList[this.isCheck]) {
                    if (this.bsdList[this.isCheck]['sldh']) {
                        sld.sldh = this.bsdList[this.isCheck]['sldh'];
                    }
                }
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=saveSld',
                    JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            malert("数据更新成功", 'top', 'success');
                            this.sldmxList = [];
                            this.isShow = false;
                            this.isShowkd = true;
                            wrapper.isShowkd = true;
                            wrapper.isShow = false;
							rksh.isCheckAll =true;
                            rksh.getData();
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                        //是否禁止提交
                        rksh.isSubmited = false;
                    }, function (error) {
                        console.log(error);
                        //是否禁止提交
                        rksh.isSubmited = false;
                    });
            },
            //删除
            clearAll: function (num) {
                //this.jsonList = [];
                if (num == null) {
                    if (this.isCheckAll) {
                        this.sldmxList = [];
                        this.isCheckAll = false;
                    }
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            num = i;
                            this.sldmxList.splice(i, 1);
                            this.isChecked[i] = false;
                        }
                    }
                    if (num == null) {
                        malert("请选中你要删除的数据", 'top', 'defeadted');
                        return false;
                    }
                }
            },
            //双击修改
            edit: function (num) {
                if (num == null) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if (num == null) {
                        malert("请选中你要修改的数据", 'top', 'defeadted');
                        return false;
                    }
                }
                wrapper.modifyIndex = num;
                wrapper.isUpdate = 1;
                wap.title = '编辑设备';
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.sldmxList[num]));
            },
            //删除2018/07/09二次弹窗删除提示
            scmx: function (index) {
                if (common.openConfirm("确认删除该条信息吗？", function () {
                    rksh.sldmxList.splice(index, 1);
                })) {
                    return false;
                }
                // this.sldmxList.splice(index,1);
            },
            //取消
            cancel: function () {
                rksh.getData();
                wrapper.isShow = false;
                wrapper.isShowkd = true;
                rksh.isShow = false;
                rksh.isShowkd = true;
            }


        }
    });


    var wrapper = new Vue({
        el: '.panel',
        mixins: [dic_transform, baseFunc, tableBase, mformat],

        data: {
            isTabelShow: false,
            isShowkd: true,
            isShow: false,
            objItem:{},
            keyWord: '',
            yfList: [],
            csParm: {},
            zdy: userId,
            kfbm: null,
            yfbm: null,
            bzms: null,
            zdyxm: '',
            ckdh: '',
            zdrq: getTodayDateTime(), //获取制单日期
            bsdContent: {
                bzms: "领用出库",
                // yfbm: "01", //报损方式
                zbr: userId //操作人
            },
            jsonList: [],
            bsdList: [], //入库单集合
            KFList: [], //库房
            title: '',
            totle: '',
            rkd: {}, //入库单对象
            num: 0,
            param: {
                page: '',
                rows: '',
                total: ''
            },
            isUpdate: 0,
            modifyIndex: '',//修改的对象下标
            search: '',//模糊查询
			tysl:0,
        },
        mounted: function () {
            var myDate = new Date();
            rksh.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
            rksh.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
			rksh.param.startBysj = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
			rksh.param.endBysj = this.fDate(new Date(), 'date') + ' 23:59:59';
            laydate.render({
                elem: '#timeVal',
                eventElem: '.zui-date',
                value: rksh.param.beginrq,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    rksh.param.beginrq = value;
                    rksh.getData();
                }
            });
            laydate.render({
                elem: '#timeVal1',
                eventElem: '.zui-date',
                value: rksh.param.endrq,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    rksh.param.endrq = value;
                    rksh.getData();
                }
            });
			laydate.render({
			    elem: '#timeVal2',
			    eventElem: '.zui-date',
			    value: rksh.param.startBysj,
			    type: 'datetime',
			    trigger: 'click',
			    theme: '#1ab394',
			    done: function (value, data) {
			        rksh.param.startBysj = value;
			    }
			});
			laydate.render({
			    elem: '#timeVal3',
			    eventElem: '.zui-date',
			    value: rksh.param.endBysj,
			    type: 'datetime',
			    trigger: 'click',
			    theme: '#1ab394',
			    done: function (value, data) {
			        rksh.param.endBysj = value;
			    }
			});
        },
        watch: {
			
            //二级库房变化获取参数权限
            kfbm: function () {
                var kfbm = '';
                for (var i = 0; i < wrapper.KFList.length; i++) {
                    if (wrapper.KFList[i].kfbm == this.kfbm) {
                        kfbm = wrapper.KFList[i].kfbm;
                    }
                }
            },
            yfbm: function () {
                for (var i = 0; i < wrapper.yfList.length; i++) {
                    if (wrapper.yfList[i].yfbm == this.yfbm) {
                        qxksbm = wrapper.yfList[i].ksbm;
                    }
                }
                wap.getCsqx();
            },
        },
        methods: {
			specifiName: function(){
				if(this.tysl){
					if(this.tysl.indexOf('.')!=-1){
						let tp = this.tysl;
						tp = tp.substr(0,this.tysl.indexOf('.'))
						this.tysl = tp;
						
					}else if(this.tysl<0){
						this.tysl = 0;
					}
					for (let i = 0; i < rksh.sldmxList.length; i++) {
						rksh.sldmxList[i].slsl = wrapper.tysl;
					}
					this.$forceUpdate()
				}
			},
			zdsc:function(){
				var sld = {
				    "yfbm": wrapper.yfbm,
				    "kfbm": wrapper.kfbm,
				    "endBysj": rksh.param.endBysj,
					"startBysj":rksh.param.startBysj
				};
				var json = {
				        "sld": sld
				};
				rksh.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=getZdhcscLyd', JSON.stringify(json)).then(function (data) {
				    if (data.body.a == "0") {
						
						for (let i = 0; i < data.body.d.list.length; i++) {
							data.body.d.list[i].slsl = wrapper.tysl;
							data.body.d.list[i].isChecked = true;
						}
						rksh.sldmxList = data.body.d.list;
				    } else {
				        malert(data.body.c, 'top', 'defeadted');
				    }
				});
				
			},
            kd: function (index) {
                this.num = index;
                rksh.loadNum();
                switch (this.num) {
                    case 0:
                        this.isUpdate = 0;
                        this.objItem = {};
                        rksh.objItem = {};
                        rksh.sldmxList = [];
                        rksh.isCheck = null;
                        this.isShowkd = false;
                        this.isShow = true;
                        rksh.isShow = true;
                        rksh.isShowkd = false;
                        rksh.mxShShow = true;
                        var reg = /^[\'\"]+|[\'\"]+$/g;
                        this.zdyxm = sessionStorage.getItem("userName" + userId).replace(reg, '');
                        break;
                    case 1:
                        this.isUpdate = 0;
                        wap.open();
                        wap.title = '添加设备';
                        wap.popContent = {};
                        break;
                }

            },
            //删除
            del: function () {
                rksh.clearAll();
            },
            //检索查询回车键
            searchHc: function () {
                rksh.param.page = 1
                rksh.getData();
            },
            resultChangeYf: function (val) {
                console.log(val);
                if (val[2].length > 1) {
                    if (Array.isArray(this[val[2][0]])) {
                        Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    } else {
                        Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                        if (val[3] != null) {
                            Vue.set(this[val[2][0]], val[3], val[4]);
                        }
                    }
                } else {
                    this[val[2][0]] = val[0];
                }
                if (val[1] != null) {
                    this.nextFocus(val[1]);
                }
            },
            //获取二级库房
            getYFData: function () {
                //获取二级库房列表
                var parm = {
                    "ylbm": 'N040100021004'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            wrapper.yfList = data.d.list;
                            if (data.d.list.length > 0) {
                                qxksbm = data.d.list[0].ksbm;
                                Vue.set(wrapper, 'yfbm', data.d.list[0].yfbm);//二级库房默认
//                                wrapper.kfbm=data.d.list[0].kfbm;
                                wap.getCsqx(); //加载完库房再次加载参数权限
                                //rksh.getData();
                            }
                        } else {
                            malert("二级库房获取失败");
                        }
                    });
            },
            getKFData: function () {
                //下拉框获取库房
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf',
                    function (data) {
                        if (data.a == 0) {
                            wrapper.KFList = data.d.list;
                            if (data.d.length > 0) {
                                wrapper.kfbm = data.d.list[1].kfbm;
                            }
                        } else {
                            malert("一级库房获取失败", 'top', 'defeadted');
                        }
                    });

            },
            //库房改变
            kfChange: function (event, kfbm) {
                var obj = event.currentTarget;
                qxksbm = kfbm; //获取科室编码
                wap.getCsqx(); //选中库房之后再次请求获取参数权限
                //输入框跳转
                document.getElementsByTagName('input')[0].focus();

            },
            yfChange: function (event, kfbm) {
                var obj = event.currentTarget;
                qxksbm = kfbm; //获取科室编码
                wap.getCsqx(); //选中库房之后再次请求获取参数权限
                //输入框跳转
                document.getElementsByTagName('input')[0].focus();

            },

        }
    });


    var wap = new Vue({
        el: '#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat,checkData],
        components: {
            'search-table': searchTable
        },
        data: {
            iShow: false,
            isTabelShow: false,
            flag: false,
            title: '',
            zdy: userId,
            ryList: [],
            added: false,
            zdrq: getTodayDateTime(), //获取制单日期
            bsdContent: {
                bzms: "领用出库",
                // yfbm: "02", //
                zbr: userId //操作人
            },
            cgryList: [], //退库人员
            KSList: [],
            KFList: [],
            lyr: null,
            bsdList: [], //入库单集合
            ghdwList: [],
            //设备信息对象
            popContent: {},
            //参数权限对象
            csParm: {},
            dg: {
                page: 1,
                rows: 20,
                sort: "",
                order: "asc",
                parm: ""
            },
            them_tran: {},
            them: {
                '生产批号': 'scph',
                '设备编号': 'ypbm',
                '设备名称': 'ypmc',
                '库存数量': 'kcsl',
                '有效期至': 'yxqz',
                '规格': 'ypgg',
                '分装比例': 'fzbl',
                '进价': 'ypjj',
                '零价': 'yplj',
                '库房单位': 'kfdwmc',
                '二级库房单位': 'yfdwmc',
                '效期': 'yxqz',
                '设备剂型': 'jxmc'
            }
        },
        methods: {
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg');
                $(".side-form").removeClass('ng-hide');
            },

            //获取参数权限
            getCsqx: function () {
                //获取参数权限
                $.ajaxSetup({
                    async: false
                });

                var parm = {
                    "ksbm": qxksbm,
                    "ylbm": "N040100021004"
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        if (json.d.length > 0) {
                            //退库开单打印单据，1-开单保存后打印单据，0-开单保存后不打印单据
                            for (var i = 0; i < json.d.length; i++) {
                                if (json.d[i].csqxbm == 'N04010002100401') {
                                    if (json.d[i].csz == '1') {
                                        wap.csParm.kddy = true;
                                    } else {
                                        wap.csParm.kddy = false;
                                    }
                                }
                                //报损开单审核，0-有开单审核  1-有开单 2-有审核
                                if (json.d[i].csqxbm == 'N04010002100402') {
                                    if (json.d[i].csz == '0') {
                                        wap.csParm.kd = true;
                                        wap.csParm.sh = true;
                                    } else if (json.d[i].csz == '1') {
                                        wap.csParm.kd = true;
                                        wap.csParm.sh = false;
                                    } else {
                                        wap.csParm.sh = true;
                                        wap.csParm.kd = false;
                                    }
                                }

                                if (json.d[i].csqxbm == 'N04010002100403') {
                                	wap.csParm.N04003002002200403 = json.d[i].csz;
                                }
                            }
                        }
                        rksh.getData();
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                    //设置ajax为异步
                    $.ajaxSetup({
                        async: true
                    });
                });
            },
            //

            //设备名称下拉table检索数据
            changeDown: function (event, type) {
                if (this['searchCon'][this.selSearch] == undefined)return;
                this.keyCodeFunction(event, 'popContent', 'searchCon');
                //选中之后的回调操作
                if (event.keyCode == 13) {
                    if (type == "ypmc" && wap.popContent.ypmc != undefined) {
                        this.nextFocus(event);
                    }
                }


                // if(type == "cksl" && event.keyCode == 13) {
                //     //添加数据到提交区
                //     this.addData();
                //     // 清空数据录入区
                //     wap.clearData();
                // }
                // var _searchEvent = $(event.target.nextElementSibling).eq(0);
                // // var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
                // if(this.searchCon.length!=0){
                //     this.inputUpDown(event,'searchCon','selSearch')
                //     //选中之后的回调操作
                //     if(event.keyCode == 13) {
                //         this.popContent=this.searchCon[this.selSearch]
                //         if(type == "ypmc") {
                //             this.nextFocus(event);
                //             $(".selectGroup").hide();
                //         }
                //     }
                // }
            },
            //当输入值后才触发
            change: function (add, type, val) {
                this.popContent['ypmc'] = val;
                if (wrapper.kfbm == undefined || wrapper.kfbm == null | wrapper.kfbm == "") {
                    malert("请先择库房!", 'top', 'defeadted');
                    return;
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                
				var sld = {
				    "yfbm": wrapper.yfbm,
				    "kfbm": wrapper.kfbm,
				    "parm": val,
					"yxqz":wrapper.fDate(new Date(),'AllDate')
				};
				var json = {
				        "sld": sld
				};
                var page= {
                    page: 1,
                    rows: 200,
                    sort: "",
                    order: "asc",
                    parm: val
                };

                this.popContent.slsl='';
                this.popContent.ypgg='';
                this.popContent.kfdwmc='';
                this.popContent.kcsl='';
                this.popContent.ypjj='';
                this.popContent.yplj='';
                this.popContent.scph='';
                this.popContent.cdmc='';
                this.popContent.zdghdwmc='';
                this.popContent.fzbl='';
                this.popContent.cpbzh='';
                this.popContent.pzwh='';

				//rksh.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=zdscLydsj', JSON.stringify(json)).then(function (data) {
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp&dg=' + JSON.stringify(page) + '&json=' + JSON.stringify(sld), function (data) {
                if (data.a == "0") {
                    wap.searchCon = data.d.list;
                    console.log(wap.searchCon);
                    wap.selSearch = 0;
                    if (data.d.list.length != 0) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                    // else {
                    //     malert('未搜索到设备', 'top', 'defeadted');
                    //     $(".selectGroup").hide();
                    // }
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
                //分页参数
            /*$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp&dg=' + JSON.stringify(this.page) + '&json=' + JSON.stringify(sld), function (data) {
                if (data.a == '0' && data.d) {
                    for (var i = 0; i < data.d.list.length; i++) {
                        data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                        data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                    }
                    if (add) {
                        wap.searchCon = wap.searchCon.concat(data.d.list)
                    } else {
                        wap.searchCon = data.d.list;
                    }
                    wap.searchCon = data.d.list;
                    wap.page.total = data.d.total;
                    wap.selSearch = 0;
                    if (!add && data.d.list.length != 0) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                }
            });*/
                    
            },
            //物资库房
            getwzData: function () {
                $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxwzkf&parm={"ylbm":"N040030020022004"}',
                    function (data) {
                        if (data.a == 0) {
                            console.log(data.d);

                        } else {
                            malert("物资库房获取失败", 'top', 'defeadted');
                        }
                    });
            },
            //设备库房
            getsbData: function () {
                $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxsbkf&parm={"ylbm":"N040100021004"}',
                    function (data) {
                        if (data.a == 0) {
                            console.log(data.d);

                        } else {
                            malert("设备库房获取失败", 'top', 'defeadted');
                        }
                    });
            },

            //双击选中下拉table
            selectOne: function (item) {
                //查询下页
                if (item == null) {
                    //分页操作
                    var parm = {
                        "kfbm": wrapper.kfbm,
                        "pdbz": '1',
                    };
                    wap.dg.page++;

                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp' +
                        '&dg=' + JSON.stringify(wap.dg) + '&json=' + JSON.stringify(parm),
                        function (data) {
                            if (data.a == 0) {
                                for (var i = 0; i < data.d.list.length; i++) {
                                    wap.searchCon.push(data.d.list[i]);
                                }
                                wap.total = data.d.total;
                                wap.selSearch = 0;
                            } else {
                                malert('分页信息获取失败', 'top', 'defeadted')
                            }

                        });
                    return;
                }
                wap.popContent = item;
                $(".selectGroup").hide();
            },
            //添加入库信息
            addData: function () {

                //判断权限
                if (rksh.hasCx(wap.csParm.kd)) {
                    return;
                }
                // 提交前验证数据
                if (!this.empty_sub('contextInfo')) {
                    return false;
                }
                this.popContent['zdy'] = userId;
                this.popContent['yxqz'] = $('#yxqz').val();
                this.popContent['scrq'] = $('#scrq').val();

                sessionStorage.setItem('kcsl', wap.popContent['kcsl'])
                console.log(sessionStorage.setItem('kcsl', wap.popContent['kcsl']))
                var haveError = false;

                // if(this.popContent.slsl==undefined || this.popContent.slsl==null || this.popContent.slsl<=0){
                // 	 malert("领用数量输入有误",'top','defeadted');
                //     return false;
                // }
                //判断可用库存数量是否大于0（同时判断未审核库存数）--暂不做库存判断 （勿删）
                /* if(this.popContent.kykc <= 0 || (this.popContent.kykc) < this.popContent.slsl) {
                     var msg = "可用库存不足" + (this.popContent['kykc'] == 0 ? "!" : "，请检查未审核的出库！")
                     malert(msg,'top','defeadted');
                     return false;
                 }*/

                /**
                 * 判断零库存和当前库存不够领用情况
                 
                if (this.popContent.kcsl <= 0 || (this.popContent.kcsl) < this.popContent.slsl) {
                    var msg = "可用库存不足" + (this.popContent['kykc'] == 0 ? "!" : "，请检查未审核的出库！")
                    malert(msg, 'top', 'defeadted');
                    return false;
                }
                */
                //判断有效期是否已过期
                if (this.popContent.yxqz && new Date(this.popContent.yxqz) < new Date()){
                    malert('效期已过，不允许领用！', 'top', 'defeadted');
                    return false;
                }
				wap.popContent.isChecked = true;
                if (wrapper.isUpdate == 0) {
                    //添加
                    for (var i = 0; i < rksh.sldmxList.length; i++) {
                        if (rksh.sldmxList[i].ypbm == this.popContent.ypbm && rksh.sldmxList[i].scph == this.popContent.scph) {
                            malert("设备【" + this.popContent.ypmc + "】已存在,请修改已有数据!", 'top', 'defeadted');
                            return;
                        }
                    }
                    //将数据加入列表
                    rksh.sldmxList.push(wap.popContent);
                    wap.popContent = {};
                }
                if (wrapper.isUpdate == 1) {
                    var sldh = rksh.sldmxList[wrapper.modifyIndex].sldh;
                    rksh.$set(rksh.sldmxList, wrapper.modifyIndex, wap.popContent);
                    rksh.$set(rksh.sldmxList[wrapper.modifyIndex], 'sldh', sldh);
                    wap.closes();
                }


                //判断数据是否重复
                if (rksh.jsonList.length > 0) {
                    for (var i = 0; i < rksh.jsonList.length - 1; i++) {
                        if (rksh.jsonList[i].ypbm == this.popContent.ypbm && rksh.jsonList[i].xtph == this.popContent.xtph) {
                            rksh.jsonList[i].cksl = this.popContent.cksl;
                            malert("设备【" + this.popContent.ypmc + "】已添加，请双击修改！");
                            rksh.jsonList.pop();
                            return false;
                        } else {
                            rksh.jsonList[i].pop;
                        }
                    }
                }

                setTimeout(function () {
                    $("#ypmc").focus();
                })
                this.added = true;
            },
            zdlb: function () {
                var parm = {
                    "ksbm": ksbm,
                };
                //下拉框获取科室编码
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                    function (data) {
                        if (data.a == 0) {
                            wap.KSList = data.d.list;
                        } else {
                            malert("科室获取失败!", 'top', 'defeadted');
                        }
                    });
                //获取制单人列表  调整为提取本科室人员信息，原来是提取的所有人员信息里面的前20个人员信息
                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json='+ JSON.stringify(parm) + '&dg=' + JSON.stringify(this.dg), function (data) {
                    wap.ryList = data.d.list;
                });
            }

        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;
    wrapper.getKFData();
    wrapper.getYFData();
    // rksh.getData();
    wap.zdlb();
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }
});




