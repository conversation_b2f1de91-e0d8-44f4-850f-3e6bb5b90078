var bm = ""; //下拉table选中项的编码
var allBm = ""; //需要展示的编码
var mc = ""; //下拉table选中项的名称
var jb = ""; //手术级别
var jbbm = {'疾病编码': 'jbmb', '疾病名称': 'jbmc', '拼音代码': 'pydm'};
var selSearch = ""; //下拉table选中项的索引

var rightVue = new Vue({
    el: '#mtgl_bzrz',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable,
		'jbsearch-table': searchTable,
    },
    data: {
        isShow: false,
        searchCon: [],
        selSearch: -1,
        them: {
            '门诊号': 'ghxh',
            '病人姓名': 'brxm',
            '年龄': 'brnl',
            '费别': 'bxlbmc',
            '科室': 'ghksmc',
            '挂号日期': 'ghrq',
            '疾病名称': 'jbmc',
            '身份证': 'sfzjhm',
            '医生': 'jzysxm',
        },
        param: {
            page: 1,
            rows: 10,
            sort: '',
            order: 'desc',
            parm: '',
        },
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        title: '取消结算',
        beginrq: null,
        endrq: null,
        jsonList:[],
        bxlbbm:'',
        bxurl:'',
        gzyhybContent:{},
        popContent:{},
        userInfo:{},
        yhybinit:false,
        yhybBzrdContent:{},
        yhybBzrdList:[],
        mtzt_tran:{
            '1':'已作废',
            '0':'已登记',
        },
        ksList:[],
        isadd:false,
		jbContent: {},
		jbsearchCon: [],
		jbthem: jbbm,
		loadName:'/newzui/page/mtgl/jzyy/014cdyhyb/014cdyhyb',
		
    },
    mounted: function () {
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#timeVal',
            format: 'yyyy-MM-dd',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    rightVue.param.beginTime = value;
                } else {
                    rightVue.param.beginTime = '';
                }
                rightVue.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            format: 'yyyy-MM-dd',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    rightVue.param.endTime = value;
                } else {
                    rightVue.param.endTime = '';
                }
                rightVue.getData();
            }
        });
        laydate.render({
            elem: '#beginrq'
            , eventElem: '.zui-date i.datenox'
            , trigger: 'click'
            , theme: '#1ab394'
            ,format:'yyyy-MM-dd'
            ,done:function (value,data) {
                rightVue.gzyhybContent.yae170 = value
            }
        });
        laydate.render({
            elem: '#endrq'
            , eventElem: '.zui-date i.datenox'
            , trigger: 'click'
            , theme: '#1ab394'
            ,format:'yyyy-MM-dd'
            ,done:function (value,data) {
                rightVue.gzyhybContent.yae171 = value
            }
        });
        this.getKsData();
    },
    updated:function(){
        changeWin();
    },
    created: function(){
        this.getbxlb();
        this.getUserInfo();
    },
    methods: {
		
		changeDown1: function (event, type, content, searchCon, modelBm, showBm, modelMc,selSearch) {
		    //全局变量
		    bm = modelBm;
		    allBm = showBm;
		    selSearch = selSearch;
		    mc = modelMc;
		    this.selectType = type;
		    if (this[searchCon][this[selSearch]] == undefined) return;
		    this.inputUpDown(event,this[searchCon],selSearch)
		    this[content]=this[searchCon][this[selSearch]]
		    //选中之后的回调操作
		    if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
		        //************************************疾病（西医）
		        if (type.indexOf("xy") >= 0) {
		            var str = rightVue.jbContent['opspDiseCode'];
		            
		            Vue.set(this.gzyhybContent, 'opspDiseCode', this.jbContent['jbmb']);
		            Vue.set(this.gzyhybContent, 'opspDiseName', this.jbContent['jbmc']);
		        }
		        this.$forceUpdate()
		        this.nextFocus(event);
		        $(".selectGroup").hide();
		        this[selSearch]=-1
		    }
		},
		//当输入值后才触发
		change1: function (add, type, val,selSearch,mc,bm) {
			
		    this.selectType = type;
		    if (!add) this.page.page = 1;
		    var _searchEvent = $(event.target.nextElementSibling).eq(0);
		    this.gzyhybContent['opspDiseName'] = val;
		     this.page.parm = val;
		     this.gzyhybContent['opspDiseCode']='';
		    //西医
		    if (type.indexOf("xy") >= 0) {
		        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm'
		            + '&json=' + JSON.stringify(this.page),
		            function (data) {
		                if (add) {
		                    for (var i = 0; i < data.d.list.length; i++) {
		                        rightVue.jbsearchCon.push(data.d.list[i]);
		                    }
		                } else {
		                    rightVue.jbsearchCon = data.d.list;
		                }
		                rightVue.page.total = data.d.total;
		                rightVue[selSearch] = 0;
						
		                if (data.d.list.length > 0 && !add) {
		                    $(".selectGroup").hide();
		                    _searchEvent.show();
		                    return false;
		                }
		            });
		    }
		    
		
		},
		//鼠标双击（西医）
		jbselectOne: function (item) {
		    if (item == null) {
		        this.page.page++;
		        this.change1(true, 'xy',this.gzyhybContent['xy'],selSearch);
		    } else {
		        var str = item.jbmb;
		        
		        this.jbContent = item;
				console.log(item)
		        Vue.set(this.gzyhybContent, 'opspDiseCode', this.jbContent['jbmb']);
		        Vue.set(this.gzyhybContent, 'opspDiseName', this.jbContent['jbmc']);
		this.$forceUpdate()
		        $(".selectGroup").hide();
		    }
		},
		
        getUserInfo: function () {
			
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    rightVue.userInfo = json.body.d;
					console.log(rightVue.userInfo)
                });
        },
        getbxlb: function () {
            var param = { bxjk : "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="+ JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            rightVue.bxlbbm = json.d.list[0].bxlbbm;
                            rightVue.bxurl = json.d.list[0].url;
							// rightVue.bxurl='http://172.20.103.68:10015/interface/cdyhyb/post'
                        }
                    } else {
                        malert("保险类别查询失败!" + json.c,'right','defeadted');
                    }
                });
            },


        getKsData: function () {
            common.openloading('#mtgl_bzrz');
            var str_param = {
                ylbm: "N050010012002"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(str_param), function (data) {
                if (data.a == 0 && data.d) {
                    if (data.d.length > 0){
                        rightVue.ksList = data.d;
                        rightVue.param.ksbm = rightVue.ksList[0].ksbm;
                        rightVue.ksList.push({ksbm:'all','ksmc':'全部'});
                        setTimeout(function(){
                            rightVue.getData();
                        },100)
                    }
                } else {
                    malert('获取科室失败','right','defeadted')
                }
            });
        },

        commonResultChange:function(val){
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "ksbm":
                    Vue.set(this.param, 'ksbm', val[0]);
                    rightVue.getData();
                    break;
            }
        },

        addDj:function(){
            this.gzyhybContent = {};
            this.isShow = true;
            this.isadd = true;
            this.yhybBzrdList = [];
            
        },
        save:function(){
			window.insuranceGbUtils.init();
			window.insuranceGbUtils.qd();
			let param_2503 = {
				data:{
					"psn_no":rightVue.gzyhybContent.aac001, //人员编号
					"insutype":rightVue.gzyhybContent.ykc303, //参保类型
					"opsp_dise_code":rightVue.gzyhybContent.opspDiseCode, //就诊id
					"opsp_dise_name":rightVue.gzyhybContent.opspDiseName, //就诊id
					"tel":rightVue.gzyhybContent.tel, //就诊id
					"addr":rightVue.gzyhybContent.addr, //就诊id
					"insu_optins":rightVue.gzyhybContent.insuplc_admdvs, //就诊id
					"ide_fixmedins_no":window.insuranceGbUtils.fixmedins_code, //就诊id
					"ide_fixmedins_name":window.insuranceGbUtils.fixmedins_name, //就诊id
					"hosp_ide_date":this.fDate(new Date(), 'datetime'), //就诊id
					"diag_dr_codg":rightVue.userInfo.czybm, //就诊id
					"diag_dr_name":rightVue.userInfo.czyxm, //就诊id
					"begndate":rightVue.gzyhybContent.yae170, //就诊id
					"enddate":rightVue.gzyhybContent.yae171, //就诊id
				}
			}
			let data1 = window.insuranceGbUtils.call1("2503",param_2503,rightVue.gzyhybContent.insuplc_admdvs)
			if(data1){
				
				rightVue.hisSave(data1.result.trt_dcla_detl_sn);
			}
			
        },

        hisSave:function(trt_dcla_detl_sn){
            
            var saveObj = {
                "psnNo":rightVue.gzyhybContent.aac001, //人员编号
                "insutype":rightVue.gzyhybContent.ykc303, //参保类型
                "opspDiseCode":rightVue.gzyhybContent.opspDiseCode, //就诊id
                "opspDiseName":rightVue.gzyhybContent.opspDiseName, //就诊id
                "tel":rightVue.gzyhybContent.tel, //就诊id
                "addr":rightVue.gzyhybContent.addr, //就诊id
                "insuOptins":rightVue.gzyhybContent.insuplc_admdvs, //就诊id
                "ideFixmedinsNo":window.insuranceGbUtils.fixmedins_code, //就诊id
                "ideFixmedinsName":window.insuranceGbUtils.fixmedins_name, //就诊id
                "hospIdeDate":this.fDate(new Date(), 'datetime'), //就诊id
                "diagDrCodg":rightVue.userInfo.czybm, //就诊id
                "diagDrName":rightVue.userInfo.czyxm, //就诊id
                "begindate":rightVue.gzyhybContent.yae170, //就诊id
                "enddate":rightVue.gzyhybContent.yae171, //就诊id
				"trtDclaDetlSn":trt_dcla_detl_sn,
				"zjhm":rightVue.gzyhybContent.aac002,
				"ryxm":rightVue.gzyhybContent.aac003,
				"zfbz":'0',
            };
            var params = '{"list":' + JSON.stringify(saveObj) + '}';
            rightVue.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + rightVue.bxurl + "&bxlbbm=" + rightVue.bxlbbm + "&types=mzjy&method=bzrz120",
                params,function (json) {
                    if(json.a == '0'){
                        rightVue.isadd = false;
						rightVue.isShow = false;
                        rightVue.getData();
                        malert(json.c,'right','success');
                        
                    }else{
                        malert(json.c,'right','defeadted');
                        return false;
                    }
                });
        },
		invalidData:function(index){
			popWin.cxindex = index;
			popWin.open();
		},
        getData:function () {
            var begin = rightVue.param.beginTime?(rightVue.param.beginTime + ' 00:00:00'):"";
            var end = rightVue.param.endTime?(rightVue.param.endTime + ' 23:59:59'):"";
            var param = {beginTime: begin,endTime: end, ksbm : rightVue.param.ksbm, parm: rightVue.param.parm, page: this.page.page, rows: this.page.rows,};
            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + rightVue.bxurl + "&bxlbbm=" + rightVue.bxlbbm + "&types=mzjy&method=bzrzlist&parm=" + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        var res = JSON.parse(json.d);
                        rightVue.totlePage = Math.ceil(res.total/rightVue.param.rows);
                        rightVue.jsonList = res.records;
                    } else {
                        malert(json.c,'right','defeadted');
                    }
                });
        },
        cancel:function () {
            this.isShow = false;
            this.brxxContent = {};
            this.popContent = {};
        },
		loadYbk:function(){
			popTable.isShow = true;
			
			loadPage(this.loadName);
			popTable.$nextTick(function () {
			    loadPage(rightVue.loadName);
			})
		},
    },
});

var popTable = new Vue({
    el: '#popCenter',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        isShow: false,
        jsonList: []
    },
});

var popWin = new Vue({
    el: '#pop',
    data: {
        ifClick: true,
        isShow: false,
        title: '确认对选中记录进行撤销？',
		cxindex:null,
    },
    methods: {
        saveData: function () {
            //获取到退费的原因进行赋值操作
            $.ajaxSettings.async = false;
            if (!popWin.ifClick) return;// 已经点击过就不能再点
            popWin.ifClick = false;
			window.insuranceGbUtils.init();
			window.insuranceGbUtils.qd();
			let param_2504 = {
				data:{
					"psn_no":rightVue.jsonList[popWin.cxindex].psnNo, //人员编号
					"trt_dcla_detl_sn":rightVue.jsonList[popWin.cxindex].trtDclaDetlSn, //参保类型
					"memo":$("#tfyy").val()
				}
			}
			let data1 = window.insuranceGbUtils.call1("2504",param_2504,rightVue.gzyhybContent.insuplc_admdvs);
			// let data1 = true;
			if(data1){
				var saveObj = {
					"id":rightVue.jsonList[popWin.cxindex].id,
					"zfbz":'1',
					"memo":$("#tfyy").val()
				};
				 var params = '{"list":' + JSON.stringify(saveObj) + '}';
				rightVue.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + rightVue.bxurl + "&bxlbbm=" + rightVue.bxlbbm + "&types=mzjy&method=bzrzUpdate",
				    params,function (json) {
				        if(json.a == '0'){
							$(this.$refs.tspop).hide();
				            rightVue.isadd = false;
							rightVue.isShow = false;
				            rightVue.getData();
				            malert(json.c,'right','success');
				            
				        }else{
				            malert(json.c,'right','defeadted');
				            return false;
				        }
				    });
			}
			popWin.ifClick = true;
        },
        closes: function () {
            $(this.$refs.tspop).hide();
            
        },
        open: function () {
            $(this.$refs.tspop).show();
        },
    }
});
function loadPage(name) {
    $('#loadPage').load(name + '.html');
}