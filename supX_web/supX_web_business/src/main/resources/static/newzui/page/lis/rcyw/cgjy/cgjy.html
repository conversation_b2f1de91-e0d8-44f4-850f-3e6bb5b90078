<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>危急值管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link type="text/css" href="cgjy.css" rel="stylesheet"/>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="printArea printShow"></div>
<div class=" printHide background-box">
    <div class="wrapper background-f" id="jyxm_icon" >
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary " id="but_regyl"  @click="AddMdel"><i class="icon-xz1 paddr-r5"></i>新增登记</button>
                <button class="tong-btn btn-parmary "><i class="icon-hqsq paddr-r5"></i><i  @click="hqsq">获取申请</i></button>
                <button class="tong-btn btn-parmary-b " @click="sx"><i class="icon-sx paddr-r5"></i>刷新</button>
                <button class="tong-btn btn-parmary-b " @click="topbc"><i class="icon-baocun paddr-r5"></i>保存</button>
                <button class="tong-btn btn-parmary-b " @click="sh"><i class="icon-width icon-sh-l padd-l-15"></i>审核</button>
                <button class="tong-btn btn-parmary-b  " ><i class="icon-ckbl paddr-r5"></i>查看病历</button>
                <button class="tong-btn btn-parmary-b  " @click="piliang"><i class="icon-pllr paddr-r5"></i>批量录入</button>
                <button class="tong-btn btn-parmary-b  " @click="pici"><i class="icon-pctz paddr-r5"></i>批次调整</button>
                <div class="dvmenu"><button class="tong-btn btn-parmary-b  paddr-r5" style="width: 102px !important;" ><i class="icon-pctz paddr-r5"></i>更多功能</button>
                    <div class="cgjy-menu">
                        <a @click="zf">作废检验申请</a>
                        <a @click="feiyong">查看费用情况</a>
                        <a @click="daying">打印检验申请</a>
                        <a>打印申请列表</a>
                        <a>取消保存标志</a>
                        <a @click="shebei">设备原始数据</a>
                    </div>
                </div>
            </div>
            <div class="tong-search">
                <div class="top-form">
                    <label class="top-label">申请日期</label>
                    <div class="top-zinle">
                        <i class="icon-position icon-rl"></i>
                        <input type="text" name="phone" class="zui-input todate text-indent-20" v-model="param.time" placeholder="请选择申请日期">
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">病员检索</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <input class="zui-input wh180" v-model="param.bah" placeholder="FAX" type="text"/>
                        </div>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">执行设备</label>
                    <div class="top-zinle">
                        <select-input @change-data="resultChange"
                                      :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="param.zxsb"
                                      :search="true" :name="'param.zxsb'">
                        </select-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-l-10 padd-r-10" v-cloak>
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jydjList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>病员姓名</span></div></th>
                        <th><div class="zui-table-cell cell-xl text-left text-indent-9"><span>检验项目</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>样本编号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>住院号/门诊号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>类型</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>急诊</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>性别</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>送检科室</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>费用名称</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>送检医师</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>床位号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>申请日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>扣费金额</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>临床诊断</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>检验序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>登记人员</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>登记日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>申请单号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>执行设备</span></div></th>
                        <th><div class="zui-table-cell cell-s" ><span>核查结果</span></div></th>
                        <th><div class="zui-table-cell cell-xl text-left" ><span>备注</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                        <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body "   @scroll="scrollTable($event)">
                <table class="zui-table zui-collapse">
                    <tbody>
                    <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
                    <tr v-for="(item,$index) in jydjList" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                        @dblclick="edit($index)"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jydjList'],$event)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jydjList'"  :type="'some'" :which="$index"  :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.brxm"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.jyxmmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.bbbh"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.bah"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="jydjlx_tran[item.lx]"></div></td>
                        <td><div class="zui-table-cell cell-s" :class="item.yblx=='Q' ? 'jizheng' : ''"  v-text="jydjyblx_tran[item.yblx]"></div></td>
                        <td><div class="zui-table-cell cell-s"  v-text="brxb_tran[item.xb]"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ksmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.fymc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.sqysxm"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.cwh"></div></td>
                        <td><div class="zui-table-cell cell-s">{{item.sqrq|formDate}}</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.fyje"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.lczd"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.jyxh"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.czyxm"></div></td>
                        <td><div class="zui-table-cell cell-s">{{item.djrq|formDate}}</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.fyje"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.lczd"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.jyxh"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.czyxm"></div></td>
                        <td><div class="zui-table-cell cell-s">{{item.djrq|formDate}}</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.sqdxh"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.zxsbmc"></div></td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s">
                           <span class="flex-center ">
                            <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                               <em class="width30"><i class="iconfont icon-iocn52 icon-hover icon-font20" data-title="删除"></i></em>
                                 <em class="width30"><i class="iconfont icon-iocn45 icon-hover icon-font20" data-title="审核" @click="xxxq($index)"></i></em>
                                 <em class="width30"><i class="iconfont icon-icon65 icon-hover icon-font25" data-title="发起" @click="Launch($index)"></i></em>
                                 <em class="width30"><i class="iconfont icon-iocn44 icon-hover icon-font20" data-title="核查" @click="check($index)"></i></em>
                            </span>
                            </div>
                        </td>
                        <p v-if="jydjList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>

            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l background-f">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jydjList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
                        <tr v-for="(item, $index) in jydjList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jydjList'],$event)">
                            <td class="cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'10'"  :type="'some'" :which="$index"  :val="isChecked[$index]">
                                    </input-checkbox>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r background-f">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                            <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
                        <tr v-for="(item, $index) in jydjList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jydjList'],$event)">
                            <td class="cell-s"><div class="zui-table-cell cell-s" :class="'待审核' ? 'color-cff5':'' ">
                                待审核
                                <!--待审核：颜色状态值color-cff5，已审核：color-008，待核查：color-f2，已核查color-c04-->
                            </div></td>
                            <td class="cell-l">
                                <div class="zui-table-cell cell-l">
                                <span class="flex-center ">
                                <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                    <em class="width30"><i class="iconfont icon-iocn52 icon-hover icon-font20" data-title="删除"></i></em>
                                    <em class="width30"><i class="iconfont icon-iocn45 icon-hover icon-font20" data-title="审核" @click="xxxq($index)"></i></em>
                                    <em class="width30"><i class="iconfont icon-icon65 icon-hover icon-font25" data-title="发起" @click="Launch($index)"></i></em>
                                 <em class="width30"><i class="iconfont icon-iocn44 icon-hover icon-font20" data-title="核查" @click="check($index)"></i></em>
                                </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>


            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

    </div>

<div class="side-form" :class="{'ng-hide':num==0,'pop-850':xzShow==true,'pop-width':fqShow==true}"  id="brzcList" role="form" v-cloak>
    <div v-show="fqShow==false">
    <div class="tab-message" style="padding: 0 0 0 20px">
        <a v-show="xzShow==true">新增登记</a>
        <div class="tab-a fyxm-tab"  v-show="xzShow==false">
            <a :class="{'active':number==0}" @click="tabBg(0)">申请人信息</a>
            <a :class="{'active':number==1}" @click="tabBg(1)">图形信息</a>
        </div>

        <a href="javascript:;" class="fr closex ti-close" @click="close"></a>
        <a class="tab-right padd-r-20" @click="AddMdels" v-show="xzShow==true"><i class="icon-ad"></i>新增项目</a>
    </div>
    <div class="ksys-side" v-show="number==0">
        <div class="fyxm-hide" :class="{'fyxm-show':number==0}">
            <ul class="tab-edit-list">
                <li>
                    <i>病员类型</i>
                    <select-input data-notempty="true" @change-data="resultChange" :not_empty="true" :child="jydjlx_tran"
                                  :index="'pdxz.lx'"  :val="pdxz.lx"
                                  :name="'pdxz.lx'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>患者姓名</i>
                    <input data-notempty="true" type="text" class="label-input"  v-model="pdxz.brxm" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>患者性别</i>
                    <select-input data-notempty="true" @change-data="resultChange" :not_empty="true" :child="brxb_tran"
                                  :index="'pdxz.xb'"  :val="pdxz.xb"
                                  :name="'pdxz.xb'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>患者年龄</i>
                    <input data-notempty="true" type="text" class="label-input"  v-model="pdxz.nl" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>年龄单位</i>
                    <select-input data-notempty="true" @change-data="resultChange" :not_empty="true" :child="nldw_tran"
                                  :index="'pdxz.nldw'"  :val="pdxz.nldw"
                                  :name="'pdxz.nldw'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>送检科室</i>
                    <select-input data-notempty="true" @change-data="resultChange" :not_empty="true" :child="util.sjks"
                                  :index="'ksmc'" :index_val="'ksbm'" :val="pdxz.ksbm"
                                  :name="'pdxz.ksbm'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>送检医师</i>
                    <select-input @change-data="resultChange" :not_empty="true" :child="util.sjys"
                                  :index="'ysmc'" :index_val="'ysbm'" :val="pdxz.sqys"
                                  :name="'pdxz.sqys'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>检验项目</i>
                    <select-input @change-data="resultChange" :not_empty="true" :child="util.jyxm"
                                  :index="'mc'" :index_val="'bm'" :val="pdxz.jyxm"
                                  :name="'pdxz.jyxm'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>执行设备</i>
                    <select-input @change-data="resultChange"  :child="util.jysb"
                                  :index="'hostname'" :index_val="'sbbm'" :val="pdxz.zxsb"
                                  :name="'pdxz.zxsb'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>检验类型</i>
                    <select-input @change-data="resultChange"  :child="jydjyblx_tran"
                                  :index="'pdxz.yblx'"  :val="pdxz.yblx"
                                  :name="'pdxz.yblx'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>质控类型</i>
                    <!-- disabled="disabled"   -->
                    <select-input id="zklx_xz"  @change-data="resultChange"  :child="jydjzklx_tran"
                                  :index="'pdxz.zklx'"  :val="pdxz.zklx"
                                  :name="'pdxz.zklx'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i><span id="lxmc_xz">门诊号</span></i>
                    <input type="text" class="label-input" v-model="pdxz.bah" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>床位号</i>
                    <input type="text" class="label-input"  v-model="pdxz.cwh" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>临床诊断</i>
                    <select-input @change-data="resultChange"  :child="util.lczd"
                                  :index="'zdmc'" :index_val="'zdmc'" :val="pdxz.lczd"
                                  :name="'pdxz.lczd'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>样本类型</i>
                    <select-input @change-data="resultChange"  :child="util.ybbm"
                                  :index="'ybmc'" :index_val="'ybbm'" :val="pdxz.ybbm"
                                  :name="'pdxz.ybbm'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>样本编号</i>
                    <input type="text" class="label-input"  v-model="pdxz.bbbh" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>申请时间</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" name="datetime" class="label-input  datetim"  v-model="pdxz.sqrq" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>扣费金额</i>
                    <input type="text" class="label-input"  v-model="pdxz.fyje" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>条码号</i>
                    <input type="text" class="label-input"  v-model="pdxz.jyxh" @keydown="nextFocus($event)"/>
                </li>
                <!--<li>-->
                <!--<i>体检单位</i>-->
                <!--<select-input @change-data="resultChange"  :child="util.ybbm"-->
                <!--:index="'ybmc'" :index_val="'ybbm'" :val="pdxz.ybbm"-->
                <!--:name="'pdxz.ybbm'" :search="true">-->
                <!--</select-input>-->
                <!--</li>-->
                <li>
                    <i>采样时间</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" class="label-input datetim1"  v-model="pdxz.cyrq" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>核收时间</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" class="label-input datetim2" v-model="pdxz.ybhsrq" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>上机时间</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" class="label-input datetim3"  v-model="pdxz.sjsj" @keydown="nextFocus($event)"/>
                </li>
                <li style="width:65%;margin-right: 0;">
                    <i>备注</i>
                    <input type="text" class="label-input"  v-model="pdxz.bz" style="width: 100%" @keydown="nextFocus($event)" placeholder="请输入备注"/>
                </li>
            </ul>
        </div>
      <div class="cgjy-wjz" v-show="xzShow==false">
            <i>序号</i>
            <i>英文缩写</i>
            <i class="text-indent-30">检测项目</i>
            <i>检测结果</i>
            <i>状态</i>
            <i>参考区间</i>
        </div>
        <vue-scroll :ops="pageScrollOps">
            <ul class="wjz-list" v-show="xzShow==false">
                <li v-for="(item,$index) in datas" :class="{'table-active':item.wxbz==0}">
                    <span v-text="$index+1"></span>
                    <span v-text="item.pydm">英文缩写</span>
                    <span class="wjz-span">
                    <em class="crisis-danger crisis-dangers" v-show="item.wxbz==0"><small>危</small></em>
                    <em class="wjz-zw" v-show="item.wxbz!=0"></em>
                    <em v-text="item.name">检验项目</em>
                    </span>
                    <span :class=" item.result=='0' ? 'color-dsh':item.result=='1' ? 'color-cff5' :item.result=='2' ? 'color-c04' : '' " v-text="is_result[item.result]">检测结果</span>
                    <span :class=" item.state=='0' ? ' ':item.state=='1' ? 'wj_s':item.state=='2' ? 'wj_x': '' " v-text="is_state[item.state]">状态</span>
                    <span v-text="item.ckqj"></span>
                </li>
            </ul>
        </vue-scroll>
    </div>
    <div class="fyxm-hide" :class="{'fyxm-show':number==1}" v-show="xzShow==false">
        图形信息
    </div>
    <div class="ksys-btn padd-r-10">
        <button class="root-btn btn-parmary-d9" @click="close">取消</button>
        <button class="root-btn btn-parmary" >确定</button>
    </div>
    </div>
    <div v-show="fqShow">
        <div class="fyxm-side-top flex-between">
            <span>发起危机值</span>
            <span class="fr iconfont icon-iocn55 icon-cf056 icon-font20" @click="close"></span>
        </div>
        <div class="ksys-side">
            <ul class="tab-edit-list1 flex-start">
                <li>
                    <i>报告时间</i>
                    <em class="iconfont icon-icon61 icon-c5 icon-position icon-positions icon-font20"></em>
                    <input type="text" class="zui-input times text-indent-25" name="disable"/>
                </li>
                <li class="patient">
                    <i>患者姓名</i>
                    <!--组件-->
                    <select-div-input @change-data="resultChange" :styles="'nz'" :not_empty="false" :child="ryxmList"
                                      :rymc="'jszgmc'" :position="'pydm'"   :index_val="'jszgbm'" :val="popContent.jszgmc"
                                      :name="'popContent.jszgmc'" :search="true">
                    </select-div-input>
                </li>
                <li>
                    <i>报告形式</i>
                    <select-input @change-data="resultChanges"
                                  :child="XsList" :index="'bgxs'" :index_val="'ksbm'" :val="popContent.ksbm"
                                  :name="'popContent.ksbm'" :search="true" :index_mc="'bgxs'" >
                    </select-input>

                </li>
                <li>
                    <i>接收科室</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="BgList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                  :name="'popContent.wxks'" :search="true">
                    </select-input>
                </li>
                <li>
                    <i>接收人</i>
                    <!--组件-->
                    <select-div-input @change-data="resultChange" :styles="'nzp'" :not_empty="false" :child="ryxmList"
                                      :rymc="'jszgmc'" :position="'pydm'" :phone="'jszgbm'"  :index_val="'jszgbm'" :val="popContent.jszgmc"
                                      :name="'popContent.jszgmc'" :search="true">
                    </select-div-input>
                </li>
                <li id="phone">
                    <i>电话接收人</i>
                    <!--组件-->
                    <select-div-input @change-data="resultChange" :styles="'nzp'" :not_empty="false" :child="ryxmList"
                                      :rymc="'jszgmc'" :position="'pydm'" :phone="'jszgbm'"  :index_val="'jszgbm'" :val="popContent.jszgmc"
                                      :name="'popContent.jszgmc'" :search="true">
                    </select-div-input>
                </li>
            </ul>
            <div class="crunch">
                <div class="crunch-name">危机值数据</div>
                <div class="crisis-title">
                    <span class="crisis-span text-left text-indent-28">检查项目</span>
                    <span class="crisis-span">检查结果</span>
                    <span class="crisis-span">状态</span>
                </div>
                <vue-scroll :ops="pageScrollOps">
                    <ul class="crisis-list">
                        <li v-for="(item,$index) in datas1">
                    <span class="crisis-span text-left">
                        <em class="crisis-danger crisis-dangers"><small>危</small></em>
                        <em v-text="item.name" data-title="阿斯蒂芬"></em>
                    </span>
                            <span class="crisis-span" :class=" item.result=='0' ? 'color-c04':item.result=='1' ? 'color-cff5' : '' " v-text="is_result[item.result]">检测结果</span>
                            <span class="crisis-span" :class=" item.state=='0' ? 'wj_x':item.state=='1' ? 'wj_s': '' " v-text="is_state[item.state]">状态</span>
                        </li>

                    </ul>
                </vue-scroll>
            </div>
        </div>

        <div class="ksys-btn padd-r-10">
            <button class="root-btn btn-parmary-d9" @click="close">取消</button>
            <button class="root-btn btn-parmary" @click="lanchOk">发起</button>
        </div>
    </div>
</div>



<div id="pop" v-cloak>
    <!--<transition name="pop-fade">-->
    <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
    <div class="zui-form podrag pop-width bcsz-layer " :class="[{show:isShow},flag ?'pop-850':'pop-width']" v-show="wjzShow==false" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title" v-if="dyShow"></div>
        <div class="layui-layer-title" v-else>{{brxm}} 交费查询</div>
        <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
        <div class="layui-layer-content" v-if="dyShow">
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="layui-layer-content" style="padding:10px 10px 0 10px;" v-else>
            <ul class="pop_jiaofeo bgcx-list">
                <li>费用名称</li>
                <li>单价</li>
                <li>数量</li>
                <li>金额</li>
                <li>收费日期</li>
                <li>医师</li>
                <li>收费员</li>
            </ul>
            <vue-scroll :ops="pageScrollOps">
            <ul v-for="(item, $index) in fylb" class="pop_jiaofeo bgcx-list pop-jiaofei-height">
                <li v-text="item.mxfyxmmc"></li>
                <li v-text="item.fydj"></li>
                <li v-text="item.fysl"></li>
                <li v-text="item.fyje"></li>
                <li></li>
                <li v-text="item.mzysxm"></li>
                <li v-text="item.mzysxm"></li>
            </ul>
            </vue-scroll>
            <div class="bgcx-money">共计<i>0</i>笔，<i>0</i>元</div>

        </div>
        <div class="zui-row buttonbox" v-if="dyShow">
            <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
            <!-- <button class="zui-btn btn-primary table_db_save" @click="delZbxm">确定删除</button> -->
        </div>
    </div>
    <!--</transition>-->
    <div class="wjz-width" v-show="wjzShow">
        <div class="wjz-top">
            <span class="top-title">核查原因</span>
            <span class="iconfont icon-iocn55 icon-cf056 icon-font20" @click="Popclose"></span>
        </div>
        <div class="wjz-content">
            <!--组件components.js调用说明方法-->
            <select-input @change-data="resultChange"
                          :child="XsList" :index="'bgxs'" :index_val="'ksbm'" :val="popContent.ksbm"
                          :name="'popContent.ksbm'" :search="true" :index_mc="'bgxs'" >
            </select-input>
        </div>

        <div class="wjz-bz">
            <textarea class="wjz-textarea" placeholder="请输入备注"></textarea>
        </div>
        <div class="wjz-btn">
            <button class="root-btn btn-parmary-d9" @click="Popclose">取消</button>
            <button class="root-btn btn-parmary" @click="popConfirm">确定</button>
        </div>
    </div>

</div>
    <div class="isTabels" :class="{'show':isTabelShow}">
    <div class="table" style="height:610px;">
        <div class="poptable">
            <div class="pop-cig">
                <i v-text="titles"></i>
                <a href="javascript:;" class="fr closex ti-close" @click="isTabelShow=false"></a>
            </div>
            <div class="pop-search">
                <i v-text="sj"  v-show="jsShowtime"></i>
                <i  v-show="jsShowtime"><input type="text" v-model="sjsj" class="pop-input label-time5" placeholder="请输入时间"/></i>
                <i v-text="jsm"></i>
                <i><input type="text" v-model="pydm" v-show="isXzxmShow" placeholder="请输入搜索内容" class="pop-input"/></i>
                <i><input type="text" v-model="ybbm" v-show="isPllrShow" placeholder="请输入样本号" @keyup.enter="bbbh4jydj"  class="pop-input"/></i>

                <i><input type="text" v-model="bbbh_tz" v-show="isPltzShow" placeholder="请输入样本号"  class="pop-input" style="margin-left: -23px;"/></i>

                <i><button class="zui-btn btn-primary table_db_save" v-show="isPllrShow" style="width: 80px;">查询</button></i>
                <i><button class="zui-btn btn-primary table_db_save" @click="query_tz" v-show="isPltzShow" style="width: 80px;">查询</button></i>
                <i v-if="isXmShow">姓名：{{brxm}}  </i>
            </div>
            <div class="pc-zhi" v-show="pcShow">
                <i>指标名称</i>
                <i>
                    <em><input type="text" v-model="zwmc" placeholder="请输入搜索内容" class="pop-input" @click="zhibiao" id="zbText"/></em>
                    <div class="pc-select" :class="{'show':zbShow}">
                        <span><b>编号</b><b>中文名称</b></span>
                        <div class="pc-option">
                            <a v-for="(item, $index) in zbbmList"  @click="dangqian(item)">
                                <b v-text="item.zbbm"></b>
                                <b v-text="item.zwmc"></b>
                            </a>
                        </div>
                    </div>

                </i>
                <i>调整值</i>
                <i><input type="text" v-model="tzcs" placeholder="" class="pop-input"/></i>
                <i><button class="zui-btn btn-primary table_db_save" @click="tzbc">调整结果</button></i>
            </div>
        </div>
        <div class="layui-layer-content content-padding" >
            <!--批量录入-->
            <div class="jieguo" v-show="jsShow">
                <div class="jieguo-left">
                    <div class="jieguo-top"><span>选择</span><span>项目名称</span></div>
                    <ul>
                        <li   v-for="(item, $index) in xmzb" @click="detail(item)" >
                            <div class="zui-table-cell fl">
                                <input type="checkbox" />
                            </div>
                            <div class="jieguo-r" v-text="item.mc"></div>
                        </li>
                    </ul>
                </div>
                <div class="jieguo-right">
                    <div class="jieguo-top">
                        <i>序号</i>
                        <i>检测项目</i>
                        <i>英文缩写</i>
                        <i>检测结果</i>
                    </div>
                    <ul class="jieguo-list">
                        <li v-for="(item, $index) in zbxq">
                            <i v-text="$index+1"></i>
                            <i v-text="item.zwmc"></i>
                            <i v-text="item.ywmc"></i>
                            <i><input type="text" v-model="item.jg" class="border-none" @keydown="nextFocus($event)"/><em v-text="item.dw"></em></i>
                        </li>
                    </ul>
                </div>
            </div>
            <!--原始设备数据-->
            <ul class="pop_jiaofeo pop-cg" v-show="isShow">
                <li>指标名称</li>
                <li>文英名称</li>
                <li>类型</li>
                <li>代码</li>
            </ul>
            <ul class="pop_jiaofeo pop-jiaofei-height pop-cg" v-show="isShow">
                <li><i class="cgjy-color">糖化血红蛋白检测</i></li>
                <li><i class="cgjy-color">XXBFX</i></li>
                <li><i class="cgjy-color">组合</i></li>
                <li><i class="cgjy-color">XZQX</i></li>
            </ul>
            <ul class="pop_jiaofeo pop-jiaofei-height pop-cg" v-show="isShow">
                <li>糖化血红蛋白检测</li>
                <li>XXBFX</li>
                <li>组合</li>
                <li>XZQX</li>
            </ul>
            <ul class="pop_jiaofeo pop-jiaofei-height pop-cg" v-show="isShow">
                <li>糖化血红蛋白检测</li>
                <li>XXBFX</li>
                <li>组合</li>
                <li>XZQX</li>
            </ul>
            <!--新增项目指标-->
            <ul class="pop_jiaofeo pop-cg" v-show="lsShow">
                <li>选择</li>
                <li>指标名称</li>
                <li>文英名称</li>
                <li>类型</li>
                <li>代码</li>
            </ul>
            <div style="width: 100%;height:415px;overflow-y: scroll"  v-show="lsShow">
                <ul v-for="(item, $index) in xmzb" class="pop_jiaofeo pop-jiaofei-height pop-cg" v-show="lsShow"   @dblclick="zbAdd(item,'zh')">
                    <li><div class="zui-table-cell"><input type="checkbox" /></div></li>
                    <li><i class="cgjy-color" v-text="item.mc"></i></li>
                    <li><i class="cgjy-color" v-text="item.pydm"></i></li>
                    <li><i class="cgjy-color">组合</i></li>
                    <li><i class="cgjy-color" v-text="item.pydm"></i></li>
                </ul>
                <ul v-for="(item, $index) in xmzbDx" class="pop_jiaofeo pop-jiaofei-height pop-cg" v-show="lsShow " @dblclick="zbAdd(item,'dx')" >
                    <li><div class="zui-table-cell"><input type="checkbox" /></div></li>
                    <li v-text="item.zwmc"></li>
                    <li v-text="item.ywmc"></li>
                    <li>单项</li>
                    <li v-text="item.pydm"></li>
                </ul>
            </div>
            <!--end-->
            <div class="pici" v-show="pcShow">
                <ul class="pop_jiaofeo pop-cg">
                    <li>序号</li>
                    <li>样本号</li>
                    <li>指标</li>
                    <li>调整前</li>
                    <li>调整后</li>
                </ul>
                <vue-scroll :ops="pageScrollOps">
                <ul class="pop_jiaofeo pop-jiaofei-height pop-ci">
                    <li v-for="(item, $index) in mxList">
                        <i v-text="$index+1"></i>
                        <i v-text="item.bbbh"></i>
                        <i v-text="item.zbxmmc"></i>
                        <i>{{item.valueTzh}}{{item.dw}}</i>
                        <i>{{item.valueN}}{{item.dw}}</i>

                    </li>
                </ul>
                </vue-scroll>
            </div>

            <div class="dbclick">
                <div class="addfl icon-gthh"  v-text="addCs" :class="{'addline':pcShow}"></div>
                <div class="addfr" v-show="qsShow">
                    <button class="zui-btn btn-default xmzb-db" @click="quxiao">取消</button>
                    <button class="zui-btn btn-primary table_db_save xmzb-db" v-if="isPlxzBc" @click="plxzBc">保存</button>
                </div>
            </div>
        </div>
    </div>

    </div>

</div>
<script src="cgjy.js"></script>
</body>

</html>
