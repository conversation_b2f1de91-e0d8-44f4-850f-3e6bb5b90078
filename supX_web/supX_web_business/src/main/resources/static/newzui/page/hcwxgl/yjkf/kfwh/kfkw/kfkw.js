var opType = 'save';
    var wrapper= new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
        data:{
            param: {
                kfbm:'',
                parm:''
            }, //查询参数

            kfList: [], //库房列表
            kf: 0, //库房
            jsonList: [],
            totlePage: 0,
            isChecked: [],
            isCheckAll: false,
            isShowListDbfa: true,
            isShowListBcfa: false,
            popContent:{},
        },
        updated:function () {
            changeWin()
        },
        mounted:function (){
            this.getKf();
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增库位';
                opType = 'save';
                wap.open();
                wap.popContent={};
            },
            //删除
            del:function () {
                wrapper.remove(-1);
            },
          //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                Vue.set(this.param, 'kfbm', val[0]);
                Vue.set(this.param, 'kfmc', val[4]);
                this.getData();
            },
            //启动加载
            getKf: function() {
                $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100014005"}', function (data) {
                    if(data.a == 0) {
                        wrapper.kfList = data.d;
                        Vue.set(wrapper.param,'kfbm',data.d[0].kfbm);
                        wrapper.getData();
                    }else{
                        malert("获取库房列表失败",'top','defeadted');
                    }
                    /* for (var i = 0; i < data.d.length; i++)
                         Vue.set(wrapper.kfList, i, data.d[i])*/
                });
                //加载库房列表
                /*$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf',
                    function(data) {
                        if(data.a == 0) {
                            wrapper.kfList = data.d.list;
                            Vue.set(wrapper.param,'kfbm',data.d.list[0].kfbm);
                            //初始化列表
                            wrapper.getData();
                        } else {
                            malert(data.c,'top','defeadted')
                        }
                    });*/

            },
            getData: function() {
                common.openloading('.zui-table-view')
                //加载库位信息列表
                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfwhKfkw&types=query&json=' + JSON.stringify(this.param),
                    function(data) {
                        if(data.a == 0) {
                            wrapper.jsonList = data.d;
                            common.closeLoading()
                        } else {
                            malert(data.c,'top','defeadted')
                            common.closeLoading()
                        }
                    });

            },
            //2018/07/04二次弹窗删除提示
            remove: function(num) {
                var kwglList = [];
                if(num == -1){
                    for(var i = 0; i < this.isChecked.length; i++) {
                        if(this.isChecked[i] == true) {
                            kwglList.push({kwbm:this.jsonList[i].kwbm});
                        }
                    }
                }else{
                    //单个删除
                    var obj = {
                        kwbm: this.jsonList[num].kwbm
                    }
                    kwglList.push(obj);
                }

                if(kwglList.length == 0) {
                    malert("请选中您要删除的数据",'top','defeadted');
                    return false;
                }
                if (common.openConfirm("确认删除该条信息吗？",function () {
                    var json = '{"json":' + JSON.stringify(kwglList) + '}';
                    wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfwhKfkw&types=delete&',
                        json).then(function (data) {
                        if(data.body.a == 0) {
                            malert("删除成功",'top','success')
                            wrapper.getData();
                        } else {
                            malert("删除失败",'top','defeadted')
                        }
                    }, function(error) {
                        console.log(error);
                    });
                })) {
                    return false;
                }
            },
            edit: function (num) {
                opType = 'modify';
                wap.title='编辑库位'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },
        },
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            dg: {
                page: 1,
                rows: 2000,
                sort: '',
                order: 'asc'
            },
            //药品列表
            ypList: [],
            //药品信息
            yp: 0,
            //医疗机构列表
            jgList: [],
            //医疗机构
            yljg: 0,
            popContent: {},
            isKeyDown: null,
            title: '',
            time: null,
            tybz: 0
        },
        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open:function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },
            saveData: function () {
            	if(!wap.popContent.tybz){
            		Vue.set(wap.popContent,'tybz','0');
            	}
            	//判断
            	if(!wrapper.param.kfbm){
            		malert("请选择库房！",'top','defeadted');
            	}
                //准备参数
                wap.popContent.kfbm = wrapper.param.kfbm;
                var json = wap.popContent;
                var url = '/actionDispatcher.do?reqUrl=New1YkglKfwhKfkw&types=save&json=';
                if(opType == "modify") {
                    url = '/actionDispatcher.do?reqUrl=New1YkglKfwhKfkw&types=update&json=';
                }
                //保存数据
                this.$http.post(url, JSON.stringify(json)).then(function(data) {
                    if(data.body.a == 0) {
                        wrapper.getData();
                        wap.closes();
                        wap.popContent = {};
                        malert("保存数据成功",'top','success');
                    } else {
                        malert("保存数据失败",'top','defeadted');
                    }
                }, function(error) {
                    console.log(error);
                });
            },

        }
    });
