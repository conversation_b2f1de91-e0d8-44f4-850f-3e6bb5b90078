body{
    background-color: #F3F3F5;
}

.cfhj_popTable th {
    width: 80px;
    font-size: 11px;
    text-align: right;
    padding-right: 5px;
}

.cfhj_popTable td {
    height: 35px;
    width: 100px;
    font-size: 16px;
}

.popTable {
    width: 95%;
    max-width: 900px;
}

.tablePfDiv {
    margin-left: 3px;
}

.tablePfListDiv {
    overflow-x: auto;
    margin-left: 8px;
}

.popInfo {
    width: 900px;
    height: 380px;
}

.part_w {
    border: 1px solid #bbb;
    display: table-caption;
    width: 100%;
    border-radius: 10px;
    padding: 2px 10px;
    margin-bottom: 20px;
}

.slide-fade-enter {
    opacity: 0;
}

.slide-fade-leave-to {
    opacity: 0;
}

.slide-fade-enter-active {
    transition: all .3s ease;
}

.slide-fade-leave-active {
    transition: all .3s;
}

.slide-fade-enter {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
}

.toolMenu select {
    padding: 4px 16px;
    margin: 3px 3px 3px 0;
    width: 100px;
}

.editArea{
    width: 100%;
    padding: 0 10px 0 0;
    background-color: #fff;
    box-shadow: 0 2px 5px #bbbbbb;
}

#cfhjList {
    /*width: 50%;*/
    padding: 14px 0 0 10px;
    background-color: #fff;
    box-shadow: 0 2px 5px #bbbbbb;
}

.infoIpt input, .infoIpt select {
    /*width: calc(100% - 110px);*/
    border: 1px solid #dddddd;
    color: #666666;
    height: 30px;
}

.age input{
    width: 60px;
}

.age .selectInput{
    width: 50px;
    height: 30px;
}
.addList{
    font-size: 20px;
    left: 0;
    padding: 20px 40px;
    box-sizing: border-box;
    background: #fff ;
    border: 1px solid #eee;
    height: 66px;
    bottom: 0;
}
#pfhjList .addList{
    /*position: absolute;*/
    width: 100%;
    padding: 0;
    height: 20px;
}
#cfhjList .addList{
    position: absolute;
    width: 99%;
    height: 30px;
    padding: 0;
}
#cfhjEdit{
    /*height: 70%;*/
    overflow: auto;
}
.zui-table-view .zui-table-body tr td{
    padding: 0;
}
.zui-table-view .zui-table-header .zui-table-cell{
    height: 29px;
    line-height: 29px;
}
.zui-table-view .zui-table-header .zui-table-cell span{
    height: 29px;
    line-height: 29px;
}
.pop-805 {
    width: 805px ;
}
