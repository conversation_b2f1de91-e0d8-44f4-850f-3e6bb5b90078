var qxksbm = ""; //选中库房后获取的科室编码
    var rksh = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, baseFunc, printer, mformat],
        data: {
            //打印数据
            printData: {},
            isShowkd:true,
            isShow: false,
            ckdList: [], //出库单集合
            jsonList:[],
            yfList:[],
            KSList:[],
            rkd: {}, //入库单对象
			ckd:{},
            TjShow:true,
            ShShow:false,
            mxShShow:true,
            zfShow: true,
            dyShow:false,
            ckdContent: {},
            popContent:{},
            isChecked_ly: [],
            param:{
            	 page: 1,
                 rows: 500,
                 sort: "",
                 order: "asc",
                 parm: ""
            },

            isCheck: null,
            rkdDetail: [], //入库明细集合
            dateBegin: null, //getTodayDateBegin(),
            dateEnd: null, //getTodayDateEnd(),
            zfIfHide: true, //作废按钮是否显示
            num:0,
            ckdDetail: [], //出库单明细集合
            json: {
                jjzj: 0.0,
                ljzj: 0.0,
            },
            zhuangtai: {
                "0": "待审核",
                "1": "已审核",
                "2":"作废",
            },
            modifyIndex:'',
            ckd:null, //修改的出库单,
            totlePage: 0,
			djkz:false,
        },
        updated:function(){
            changeWin()
          },
        computed:{
			
            money:function () {
                var reducers = {
                    totalInEuros: function(state, item) {
                        var jj1 = rksh.MathFun(rksh.accMul(item.ypjj,item.cksl));
                        //return state.jjzj +=rksh.Mul(item.ypjj, item.cksl);
                        return state.jjzj =rksh.dcmAdd(state.jjzj,jj1);
                        //return state.jjzj +=item.ypjj*item.cksl;
                    },
                    totalInYen: function(state, item) {
                        var lj1 = rksh.MathFun(rksh.accMul(item.yplj,item.cksl));
                        //return state.ljzj +=rksh.Mul(item.yplj, item.cksl);
                        return state.ljzj =rksh.dcmAdd(state.ljzj,lj1);
                        //return state.ljzj +=item.yplj*item.cksl;
                    }
                };
                var manageReducers = function(reducers){
                    return function(state, item){
                        return Object.keys(reducers).reduce(function(nextState, key){
                            reducers[key](state, item);
                            return state;
                        },{})
                    }
                }
                var bigTotalPriceReducer = manageReducers(reducers);
                this.jsonList.reduce(bigTotalPriceReducer, this.json={
                    jjzj:0,
                    ljzj:0,
                });
                //this.json.jjzj=this.formatDecimal(this.json.jjzj,2)
                //this.json.ljzj=this.formatDecimal(this.json.ljzj,2)
                this.json.jjzj=this.json.jjzj.toFixed(2)
                this.json.ljzj=this.json.ljzj.toFixed(2)
                console.log(this.json)
            }
        },
        methods: {
			getCxsl:function (rksl,cxsl,ycxsl,index){
			    if(rksl-ycxsl<parseFloat(cxsl)){
			        malert('冲销数量不得大于入库数量', 'top', 'defeadted');
			        this.ckdDetail[index]['cxsl']=''
			    }else {
			        return true
			    }
			},
			isCx:function (){
				var item = this.ckd;
			    return JSON.stringify(item) != '{}' ? item.totalypjj ? item.totalypjj>0 ? true:false:true : true;
			},
			isShFun:function (){
				var item = this.ckd;
			    return JSON.stringify(item) != '{}' && item.shzfbz == 0 ? true:false;
			},
			cxClick:function (){
			    this.cxShow=!this.cxShow;
			    this.ShShow=!this.ShShow;
			    this.dyShow=!this.dyShow;
			},
			Mul: function (arg1, arg2) {
			            var r1 = arg1.toString(), r2 = arg2.toString(), m, resultVal, d = arguments[2];
			            m = (r1.split(".")[1] ? r1.split(".")[1].length : 0) + (r2.split(".")[1] ? r2.split(".")[1].length : 0);
			            resultVal = Number(r1.replace(".", "")) * Number(r2.replace(".", "")) / Math.pow(10, m);
						if(d){
							return rksh.fDec(resultVal, d);
						}else{
							return Number(resultVal)	
						}
			 },
            formatDecimal(num, decimal) {
                num = num.toString()
                let index = num.indexOf('.')
                if (index !== -1) {
                    num = num.substring(0, decimal + index + 1)
                } else {
                    num = num.substring(0)
                }
                return parseFloat(num);
            },
        	//打印
    		printDJ: function() {
                var ckdh = rksh.ckdList[this.isCheck]['ckdh'];
                var yfbm = wrapper.ckdContent.yfbm;
    			var json = {
    				ckdh: ckdh,
    				yfbm: yfbm
    			};
    			$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglCkgl&types=print&parm=' + JSON.stringify(json),
    				function(data) {
    					rksh.printData = data.d;
    					var zdrq = data.d.dj.zdrq;
    					var djmx = data.d.djmx;
    					rksh.printData.dj.zdrq = rksh.fDate(zdrq, 'date');
    					for(var i = 0; i < djmx.length; i++) {
    						var yxqz = djmx[i].yxqz;
    						rksh.printData.djmx[i]['yxqz'] = rksh.fDate(yxqz, 'date');
    						djmx[i].ypjjje = rksh.fDec(djmx[i].ypjjje, 2);
    						djmx[i].ypljje = rksh.fDec(djmx[i].ypljje, 2);
    					}
    				});
    		},
            loadNum: function () {
                this.num = wrapper.num;
            },
            //进入页面加载单据列表信息
            getData: function () {
                Vue.set(this.param,'parm',wrapper.search);
                common.openloading('.zui-table-view');
                rksh.ckdDetail = [];
                this.dateBegin = rksh.param.beginrq;
                this.dateEnd =rksh.param.endrq;

                var parm = {
                    'yfbm': wrapper.ckdContent.yfbm,
                    // 'shzfbz': '0',
                    'cklx': '05',
                    "beginrq": this.dateBegin,
                    "endrq": this.dateEnd,
                    "parm" : rksh.param.parm,
                };
                if (wrapper.popContent.zt != '9') {
                    parm.shzfbz = wrapper.popContent.zt;
                } else {
                    parm.shzfbz = null;
                }
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglCkgl&types=ckdcx' +
                    '&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == "0") {
                            rksh.ckdList = data.d.list;
                            rksh.totlePage = Math.ceil(data.d.total / rksh.param.rows);
							if(data.d.count){
								wrapper.totalyplj=data.d.count.TOTALYPLJ
								wrapper.totalypjj=data.d.count.TOTALYPJJ
							}
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
                common.closeLoading()
            },
            //打印
            print: function() {
                //帆软打印
				let ckdh='';
				if(this.ckdList[this.isCheck].iscx =='1'){
					ckdh = this.ckdList[this.isCheck]['cxdh'];
				}else{
					ckdh = this.ckdList[this.isCheck]['ckdh'];
				}
				
                // var ckdh = this.ckdList[this.isCheck]['ckdh'];
                var reportlets ="[{reportlet:'fpdy%2Fyfgl%2Fyfgl_ckd.cpt',yljgbm:'"+jgbm+"',ckdh:'"+ckdh+"'}]";
                if(!window.top.J_tabLeft.obj.FRorWindow){
                    if (FrPrint(reportlets,null)){
                        return;
                    }
                }

    			// 查询打印模板
    			var json = {
    				repname: '药房出库单'
    			};
    			var rows = rksh.printData['dj']['rows'];
    			$.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function(json) {
    				// 根据每页行数循环打印
    				var total = Math.ceil(rksh.printData['djmx'].length / rows);
    				for(var i = 0; i < total; i++) {
    					// 清除打印区域
    					rksh.clearArea(json.d[0]);
    					// 绘制模板的canvas
    					rksh.drawList = JSON.parse(json.d[0]['canvas']);
    					rksh.creatCanvas();
    					rksh.reDraw();
                        var list = [];
                        var jjhz = 0;
                        var ljhz = 0;
                        for(var j = 0; j < rows; j++) {
                            if(rksh.printData['djmx'][i * rows + j] == null) break;
                            list.push(rksh.printData['djmx'][i * rows + j]);
                            jjhz += rksh.printData['djmx'][i * rows + j]['ypjjje'];
                            ljhz += rksh.printData['djmx'][i * rows + j]['ypljje'];
                        }
                        // 为打印前生成数据
                        rksh.printData['dj']['total'] = total;
                        rksh.printData['dj']['page'] = i + 1;
                        rksh.printData['dj']['jjxj'] = parseFloat(jjhz).toFixed(2);
                        rksh.printData['dj']['ljxj'] = parseFloat(ljhz).toFixed(2);
                        rksh.printContent(rksh.printData['dj']);
                        rksh.printTrend(list);
    					// 开始打印
    					window.print();
    				}
    			});
    		},
            //入库审核
            passData: function () {
                if (wrapper.ckdContent.yfbm == undefined || wrapper.ckdContent.yfbm == null | wrapper.ckdContent.yfbm == "") {
                    malert("请先择药房!",'top','defeadted');
                    return;
                }
				if(rksh.djkz){
					return false;
				}
				rksh.djkz = true;
                var json = {
                    ckdh: this.ckdList[this.isCheck]['ckdh']
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglCkgl&types=shckd&yfbm=' + wrapper.ckdContent.yfbm,
                    JSON.stringify(json))
                    .then(function(data) {
						rksh.djkz = false;
                        if(data.body.a == "0") {
                            //打印数据
                        	rksh.printDJ();
                            rksh.getData();
                            malert("出库单审核成功！",'top','success');
                            rksh.jsonList=[];
                            this.cancel();
                        } else {
                            malert(data.body.c,'top','defeadted');
                        }
                    });
            },

            //作废2018/07/07
            invalidData: function (num) {
            	if(num!=null&& num!=undefined){
            		this.isCheck=num;
            	}
                if (wrapper.ckdContent.yfbm == undefined || wrapper.ckdContent.yfbm == null || wrapper.ckdContent.yfbm == "") {
                    malert("请选择药房!",'top','defeadted');
                    return;
                }
                var json= {"ckdh": rksh.ckdList[rksh.isCheck]['ckdh']};
                if (common.openConfirm("确认作废该条信息吗？", function () {

                        rksh.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglCkgl&types=zfckd&yfbm=' + wrapper.ckdContent.yfbm, JSON.stringify(json)).then(function (data) {
                            if (data.body.a == "0") {
                                rksh.getData();
                                malert("作废成功！", 'top', 'success')
                                rksh.cancel();
                            } else {
                                malert(data.body.c);
                            }
                        });
                    })) {
                    return false;
                }




            },

            //选中单据信息加载出相对应的单据内容明细
            showDetail: function (index,item) {
                this.isCheck = index;
				
                wrapper.isShow=true;
                wrapper.isShowkd=false;
                wrapper.isShowpopL=true;
                rksh.isShow=true;
                rksh.isShowkd=false;
                this.TjShow=true;
                this.ShShow= false;
                wrapper.TjShow=false;
                rksh.zfShow=true;
                rksh.mxShShow=false;
                wrapper.jyinput=false;
				this.cxShow  = false;
                //根据状态判断
                if(this.ckdList[index].shzfbz==0){
                	this.ShShow=true;
					this.dyShow = false;
					this.TjShow = false;
                	wrapper.jyinput=false;
                }else{
                	this.ShShow=false;
                	wrapper.jyinput=true;
					this.zfShow = false;
					rksh.zfShow = false;
                    if(this.ckdList[index].shzfbz == 1){
                        rksh.dyShow=true;
						wrapper.isShowpopL=false;
						if(this.ckdList[index].totalypjj< 0){
							this.TjShow   = false;
						}
                    }
                }
                this.printDJ()
                wrapper.zdyxm=this.ckdList[index].zdrxm;
                wrapper.zdrq=rksh.fDate(this.ckdList[index].zdrq,'date');
                wrapper.ckdContent.yfbm=this.ckdList[index].yfbm;
                wrapper.ckdContent.lyks=this.ckdList[index].lyks;//领用科室
                wrapper.ckdContent.bzms=this.ckdList[index].bzms;
				this.ckd = this.ckdList[index];
				
				let ckdh='';
				if(this.ckdList[index].iscx =='1'){
					ckdh = this.ckdList[index]['cxdh'];
				}else{
					ckdh = this.ckdList[index]['ckdh'];
				}
                var parm = {
                    "ckdh": ckdh,
					uptimestamp:this.ckd.uptimestamp
                };

                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglCkgl&types=ckdmxcx' +
                    '&parm=' + JSON.stringify(parm),
                    function(data) {
                        if(data.a == 0) {
                            rksh.jsonList = data.d.list;
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
            },

            //提交所有药品
            submitAll: function() {
                if(this.isSubmited) {
                    malert("数据提交中，请稍候！",'top','defeadted');
                    return;
                }
                if(this.jsonList.length <= 0) {
                    malert("没有可提交的数据",'top','defeadted');
                    
                }
                if(wrapper.ckdContent.yfbm == null) {
                    $(".YFSelect").addClass("emptyError");
                    malert("药房为必选项",'top','defeadted');
					return false;
                }
                //是否禁止提交
                //wap.ckdContent.yfbm=wrapper.ckdContent.yfbm;
                

                var bzms = wrapper.ckdContent.bzms;
                if(bzms == undefined || bzms == null || bzms == "") {
                    bzms = "药品出库";
                }
                var ckdJosn = null;
                if(rksh.ckd){
                	ckdJosn = rksh.ckd;
                	Vue.set(ckdJosn,'yfbm',wrapper.ckdContent.yfbm);//药房编码
                	Vue.set(ckdJosn,'jbr',wap.ckdContent.jbr);//经办人
                	Vue.set(ckdJosn,'lyks',wrapper.ckdContent.lyks);
                	Vue.set(ckdJosn,'bzms',bzms);
                }else{
                	ckdJosn = {
                            yfbm: wrapper.ckdContent.yfbm,
                            bzms: bzms,
                            jbr: wap.ckdContent.jbr,
                            lyks: wrapper.ckdContent.lyks,
                            bzms: wrapper.ckdContent.bzms
                    };
                }
								var detailList=[];
                if(this.cxShow){
                    for (var i = 0; i <this.jsonList.length ; i++) {
                        if(this.jsonList[i].cxsl){
                            var item=JSON.parse(JSON.stringify(this.jsonList[i]));
                            item.mxxh= ++this.jsonList[this.jsonList.length-1]['mxxh']
                            item.cxsl='-'+item.cxsl;
							
							if(this.jsonList[i].yxqz =='-' || wrapper.fDate(this.jsonList[i].yxqz,'date').indexOf("9999-01-01")!=-1){
							    item.yxqz='9999-01-01 00:00:00';
							    detailList.push(item);
							}else{
								detailList.push(item);
							}   
                        }
                    }
                }else {
					for (var i = 0; i <this.jsonList.length ; i++) {
						var item=JSON.parse(JSON.stringify(this.jsonList[i]));
					    if(this.jsonList[i].yxqz =='-' || wrapper.fDate(this.jsonList[i].yxqz,'date').indexOf("9999-01-01")!=-1){
					        item.yxqz='9999-01-01 00:00:00';
					        detailList.push(item);
					    }else{
							detailList.push(item);
						}
					}
                }
				
                var json = {
                        list:{
                            ckd: ckdJosn,
                            ckdmx: detailList
                        }
                };
				this.isSubmited = true;
				var url=!this.cxShow ? 'modify' :'ckcx'
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglCkgl&types='+url,
                    JSON.stringify(json))
                    .then(function(data) {
                        console.log(data);
                        if(data.body.a == 0) {
                            malert("数据更新成功",'top','success');
                            this.jsonList = [];
                            this.isShow=false;
                            this.isShowkd=true;
                            wrapper.isShowkd=true;
                            wrapper.isShow=false;
                            wrapper.isShowpopL=false;
                            rksh.ckd = null;
                            rksh.getData();
                        } else {
                            malert("数据提交失败",'top','defeadted');
                        }
                        //是否禁止提交
                        rksh.isSubmited = false;
                    }, function(error) {
                        console.log(error);
                        //是否禁止提交
                        rksh.isSubmited = false;
                    });
            },
            //删除2018/07/06二次弹窗删除提示
            scmx: function(index){
                if (common.openConfirm("确认删除该条信息吗？", function () {
                        rksh.jsonList.splice(index,1);
                    })) {
                    return false;
                }
            	// this.jsonList.splice(index,1);
            },

            //双击修改
            edit: function(num) {
            	rksh.modifyIndex = num;
            	wrapper.isUpdate = 1;
            	console.log();
            	//Vue.set()
                if(num == null) {
                    for(var i = 0; i < this.isChecked.length; i++) {
                        if(this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if(num == null) {
                        malert("请选中你要修改的数据",'top','defeadted');
                        return false;
                    }
                }
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                this.getPckc(wap.popContent.ypbm,wap.popContent.xtph);
                wap.title = "编辑药品";
                wap.open();
            },
            //批次库存
            getPckc:function(ypbm,xtph){
            	var parm = {
            		yfbm : wrapper.ckdContent.yfbm,
                    ypbm : ypbm,
                    xtph : xtph,
					sfpdkcsl:'1',
            	};
            	 $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc' + '&json=' + JSON.stringify(parm),
                         function(data) {
                             if(data.a == 0) {
                            	 Vue.set(wap.popContent,'kcsl',data.d.list[0].sjkc);
                             } else {
                                 malert(data.c,'top','defeadted');
                             }
                         });
            },

            //编辑
            editIndex : function(index){
            	this.ckd = this.ckdList[index];
            	Vue.set(wrapper.ckdContent,'lyks',this.ckdList[index].lyks);//领用科室
            	Vue.set(wap.ckdContent,'jbr',this.ckdList[index].jbr);//经办人
                this.isCheck = index;
                wrapper.isShow=true;
                wrapper.isShowkd=false;
                wrapper.isShowpopL=true;
                rksh.isShow=true;
                rksh.isShowkd=false;
                this.TjShow=true;
                this.ShShow= false;
                wrapper.TjShow=false;
                rksh.zfShow=true;
                rksh.mxShShow=true;
                wrapper.jyinput=false;
                wrapper.zdyxm=this.ckdList[index].zdrxm;
                wrapper.zdrq=rksh.fDate(this.ckdList[index].zdrq,'date');
                wrapper.ckdContent.yfbm=this.ckdList[index].yfbm;
                wrapper.ckdContent.bzms=this.ckdList[index].bzms;
				this.cxShow  = false;
				//根据状态判断
				if(this.ckdList[index].shzfbz==0){
					this.ShShow=false;
					this.mxShShow=true;
					this.dyShow = false;
					this.TjShow = true;
					wrapper.jyinput=false;
				}else{
					this.ShShow=false;
					wrapper.jyinput=true;
					this.mxShShow=false;
					this.zfShow = false;
					rksh.zfShow = false;
				    if(this.ckdList[index].shzfbz == 1){
				        rksh.dyShow=true;
						wrapper.isShowpopL=false;
						if(this.ckdList[index].totalypjj< 0){
							this.TjShow   = false;
						}
				    }
				}
                this.ckd = this.ckdList[index];
				
				let ckdh='';
				if(this.ckdList[index].iscx =='1'){
					ckdh = this.ckdList[index]['cxdh'];
				}else{
					ckdh = this.ckdList[index]['ckdh'];
				}
				
                var parm = {
                    "ckdh": ckdh,
                	uptimestamp:this.ckd.uptimestamp
                };

                    $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglCkgl&types=ckdmxcx' +
                        '&parm=' + JSON.stringify(parm),
                        function(data) {
                            if(data.a == 0) {
                                rksh.jsonList = data.d.list;
                            } else {
                                malert(data.c,'top','defeadted');
                            }
                        });
            },

            //取消
            cancel:function () {
                wrapper.isShow=false;
                wrapper.isShowkd=true;
                wrapper.isShowpopL=false;
                rksh.isShow=false;
                rksh.isShowkd=true;
				rksh.dyShow=true;
				
                rksh.ckd = {};
                rksh.getData();
            },
            totalPrice: function(type) {
                var total = 0;
                for(var i = 0; i < this.jsonList.length; i++) {
                    total = total + this.jsonList[i][type]
                }
                return this.fDec(total, 2);
            }



        }
    });


    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],

        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShowkd:true,
            isShow:false,
            keyWord:'',
            TjShow:true,
            zdyxm:'',
            zdrq: getTodayDateTime(), //获取制单日期
            jyinput: false, //禁用输入框
            ckdContent: {ckfs: '01'},
            popContent:{zt: '9'},
            jsonList:[],
            rkdList: [], //入库单集合
            KFList: [], //库房
            yfList:[],
            KSList:[],
            ryList:[],
            title:'',
            totle:'',
            dg: {
                page: 1,
                rows: 500,
                sort: "",
                order: "asc",
                parm: ""
            },
            rkd: {}, //入库单对象
            num:0,
            param: {
                page: '',
                rows: '',
                total: ''
            },
            isUpdate : 0,
            search:'',
			totalypjj:0,
			totalyplj:0,
        },
        watch:{
        	'ckdContent.yfbm' : function(){
                rksh.getData();
        	},
            'popContent.zt': function (newval, oldval) {
                if (newval != oldval) {
                    rksh.getData();
                }
        },
        },
        mounted: function () {
            this.getYFData();
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
			rksh.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
			rksh.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
        	laydate.render({
                elem: '#timeVal',
                eventElem: '.zui-date',
                value: this.param.beginrq,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                	rksh.param.beginrq = value;
                    rksh.getData();
                }
            });
        	laydate.render({
                elem: '#timeVal1',
                eventElem: '.zui-date',
                value: this.param.endrq,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                	rksh.param.endrq = value;
                    rksh.getData();
                }
            });
        },

        methods:{
			getData:function(){
				rksh.param.page = 1;
				rksh.getData();
			},
            kd:function (index) {
                this.num=index;
                rksh.loadNum();
                setTimeout(function () {
                    wap.$refs.autofocus.$refs.inputFu.focus();
                    wap.$refs.autofocus.setLiShow(wap.$refs.autofocus.$refs.inputFu)
                },1000);
                switch (wrapper.num){
                    case 0:
                    	wrapper.isUpdate = 0;
                    	//默认开单领用科室 1014
						let tp = true;
						for (let i = 0; i < wrapper.KSList.length; i++) {
							if(wrapper.KSList[i].ksmc=='无'){
								Vue.set(wrapper.ckdContent,'lyks',wrapper.KSList[i].ksbm);
								tp = false;
								break;
							}
						}
						if(tp){
							Vue.set(wrapper.ckdContent,'lyks',wrapper.KSList[0].ksbm);
						}
						Vue.set(wap.ckdContent,'jbr',userId);
						
                    	rksh.jsonList = [];
                    	rksh.ckd = {};
                        this.isShowkd=false;
                        this.isShow=true;
                        this.isShowpopL=true;
                        rksh.isShow=true;
                        rksh.isShowkd=false;
                        rksh.TjShow=true;
                        rksh.ShShow=false;
						rksh.dyShow=false; 
                        $('#bzms').attr('disabled',false);
						rksh.zfShow=false;
                        rksh.mxShShow=true;
                        wrapper.jyinput=false;
                        var reg = /^[\'\"]+|[\'\"]+$/g;
                        wrapper.zdyxm=sessionStorage.getItem("userName"+userId).replace(reg,'');
                        break;
                    case 1:
                        wap.open();
                        wrapper.isUpdate = 0;
                        wap.title='添加药品';
                        wap.popContent={};
                        break;
                }

            },
            getYFData:function () {
                //下拉框获取库房
                $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm={"ylbm":"N040030020022016"}', function(data) {
                        if(data.a == 0) {
                            wrapper.yfList = data.d.list;
                            Vue.set(wrapper.ckdContent,'yfbm',wrapper.yfList[0].yfbm);//初始化库房
                            rksh.getData();
                        } else {
                            malert("药库获取失败",'top','defeadted');
                        }
                    });
var str_parm = {"ksbm": ksbm,};
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(str_parm), function (data) {
                
                        if(data.a == 0) {
                            wap.ryList = data.d.list;
                        } else {
                            malert("科室获取失败!",'top','defeadted');
                        }
                    });
                //下拉框获取科室编码
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                    function(data) {
                        if(data.a == 0) {
                            wap.KSList = data.d.list;
                            wrapper.KSList = data.d.list;
                        } else {
                            malert("科室获取失败!",'top','defeadted');
                        }
                    });

                //初始化页面加载领用人
                var str_parm = {"ksbm": ksbm,};
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(str_parm), function (json) {
                    if(json.a == 0) {
                        wap.glryList = json.d.list;
                    } else {
                        malert("领用人员列表查询失败!"+json.c,'top','defeadted');

                    }
                });
            },

        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
            isShowpopL:false,
            iShow:false,
            isTabelShow:false,
            flag:false,
            jsShow:false,
            centent:'',
            yfList:[],
            isFold: false,
            ksList:[],
            KSList:[],
            ckdContent: {
                "zdrq": getTodayDateTime()
            },
            hszList:[],
            ywckList:[],
            title:'',
            ifClick:true,
            num:0,
            csContent: {},
            jsonList: [],
            cgryList:[],//采购员
            csqxContent: {}, //参数权限对象
            popContents:{},
            KSList: [], //领用科室
            zbfsList:[],//招标方式
            ghdwList:[],//供货单位
            KFList: [], //库房
            ryList: [], //领用人
            glryList: [], //过滤领用人
            ypcdList: [], //药品产地
            rkd: {}, //入库单对象
            popContent: {},
            dg: {
                page: 1,
                rows: 500,
                sort: "",
                order: "asc",
                parm: ""
            },
            them_tran: {},
            them: {
                '药品编号': 'ypbm',
                '药品名称': 'ypmc',
                '规格': 'ypgg',
                '分装比例': 'fzbl',
                '进价': 'ypjj',
                '零价': 'yplj',
                '库存数量': 'kcsl',
                '库房单位': 'kfdwmc',
                '药房单位': 'yfdwmc',
                '效期': 'yxqz',
                '药品剂型': 'jxmc',
                '药品国家码': 'ypgjm',
            },

        },
        mounted:function(){
            // this.getJzData();
        },
        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg');
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                wap.addData();
                //this.popContent={};
                // this.closes();
            },
            // getJzData:function () {
            //     //初始化页面记载供货单位
            //     this.dg.rows = 200;
            //     this.dg.sort = 'dwbm';
            //     this.dg.tybz = '0';
            //     $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(this.dg),
            //         function (json) {
            //             if (json.a == 0) {
            //                 wap.ghdwList = json.d.list;
            //             } else {
            //                 malert("供货单位获取失败",'top','defeadted');
            //             }
            //         });
            // },


            //获取参数权限
            /*getCsqx: function () {
                // if(wrapper.ckdContent.kfbm == undefined && qxksbm == undefined) {
                //     return;
                // }
                //获取参数权限
                var parm = {
                    "ylbm": '002001002',
                    "ksbm": qxksbm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function(json) {
                    if(json.a == 0) {
                        if(json.d.length > 0) {
                            for(var i = 0; i < json.d.length; i++) {
                                var csjson = json.d[i];
                                switch(csjson.csqxbm) {
                                    case "00200100201": //出库开单打印单据1-开单保存后打印单据，0-开单保存后不打印单据
                                        if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            wap.csqxContent.cs00200100201 = csjson.csz;
                                        }
                                        break;
                                    case "00200100202": //是否判断药房库存上限1-开单判断药房库存上限，出库数量加药房当前库不能大于药房库存上限；0-不判断，不限制
                                        if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            wap.csqxContent.cs00200100202 = csjson.csz;
                                        }
                                        break;
                                    case "00200100203": //药房申领是否需要审核0=不(出库后药房自动增加库存)，1=是(出库后药房审核后增加库存)
                                        if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            wap.csqxContent.cs00200100203 = csjson.csz;
                                        }
                                        break;
                                    case "00200100204": //药库出库数量是否允许为小数1、允许；0、不允许
                                        if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            wap.csqxContent.cs00200100204 = csjson.csz;
                                        }
                                        break;
                                    case "00200100205": //出库是否只允许选取当前科室人员作为领用人1、是,0、否
                                        if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            wap.csqxContent.cs00200100205 = csjson.csz;
                                        }
                                        break;
                                    case "00200100206": //出库开单审核权限0-有开单审核  1-有开单 2-有审核
                                        if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            wap.csqxContent.cs00200100206 = csjson.csz;
                                        }
                                        break;
                                    case "00200100207": //出库单作废权限1、是,0、否
                                        if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            wap.csqxContent.cs00200100207 = csjson.csz;
                                        }
                                        break;
                                }
                            }
                        }
                    } else {
                    }
                });
            },*/

            //药品名称下拉table检索数据
            changeDown: function (event, type) {
               this.keyCodeFunction(event, 'popContent', 'searchCon');
                //选中之后的回调操作
                if(window.event.keyCode == 13) {
                    if(type == "ypmc" && wap.popContent.ypmc != undefined) {
                        document.getElementById("cksl").focus();
                    } else if(type == "cksl") {
                        var cksl = this.popContent["cksl"];
                        var kcsl = this.popContent["kcsl"];
                        if(!cksl || cksl<=0) {
                            malert("出库数量不能为空!",'top','defeadted');
                            return false;
                        }

                        if(cksl > kcsl) {
                            malert("药品出库数量[" + cksl + "]大于库存[" + kcsl + "]数量",'top','defeadted');
                            return false;
                        }
                        this.addData();
                    } /*else if(type == "bzsm") {
                        this.addData();
                    }*/
                }
            },
            //当输入值后才触发
            change: function (add,type,val) {
            	this.popContent['ypmc']=val;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if(type == "ypmc") { //药品下拉框
                    wap.dg.page = 1;
                    wap.dg.rows = 500;
                    wap.dg.parm = this.popContent[type];
                    wap.dg.sort = "ypbm";
                    var json = {};
                    json.yfbm = wrapper.ckdContent.yfbm;
                    json.ksbm = wrapper.ckdContent.ksbm;
                    json.ckbz = "bs"; //出库不过虑过期药品
                    json.sfpdkcsl = 1; //只查询库存>0的药品
                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc' +
                        '&dg=' + JSON.stringify(wap.dg) + '&json=' + JSON.stringify(json),
                        function(data) {
                            if(data.a == 0) {
                                wap.searchCon = data.d.list;
                                wap.total = data.d.total;
                                wap.selSearch = 0;
                                if(data.d.list.length != 0) {
                                    $(".selectGroup").hide();
                                    _searchEvent.show()
                                } else {
                                    $(".selectGroup").hide();
                                }
                            } else {
                                malert(data.c,'top','defeadted');
                            }

                        });
                }
            },

            //双击选中下拉table
            selectOne: function (item) {
                //查询下页
                if(item == null) {
                    wap.dg.page++;
                    var json = {};
                    json.yfbm = wrapper.ckdContent.yfbm;
                    json.ksbm = wrapper.ckdContent.ksbm;
                    json.ckbz = "bs"; //出库不过虑过期药品
                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc' +
                        '&dg=' + JSON.stringify(wap.dg) + '&json=' + JSON.stringify(json),
                        function(data) {
                            if(data.a == 0) {
                                wap.total = data.d.total;
                                wap.selSearch = 0;
                                if(data.d.list.length != 0) {
                                    for(var i = 0; i < data.d.list.length; i++) {
                                        wap.searchCon.push(data.d.list[i]);
                                    }
                                }
                            } else {
                                malert(data.c,'top','defeadted');
                            }

                        });

                    return;
                }
                this.popContent = item;
                    $(".selectGroup").hide();
                    $("#cksl").focus();
            },
            //添加药品出库信息
            addData: function () {

                if(wrapper.ckdContent.yfbm == undefined || wrapper.ckdContent.yfbm == null) {
                    malert('请选择药房','top','defeadted');
                    return;
                }
                if(this.ckdContent.jbr == null || this.ckdContent.jbr == undefined) {
                    malert('请选择经办人','top','defeadted');
                    return;
                }
                if(this.popContent.ypmc == null || this.popContent.ypmc == undefined) {
                    malert('请输入药品名称','top','defeadted');
                    return;
                }
                if(this.popContent.cksl == null || this.popContent.cksl == undefined) {
                    malert('出库数量不正确','top','defeadted');
                    return;
                }

                if(this.popContent.cksl > this.popContent.kcsl){
                	malert('出库数量大于库存数量','top','defeadted');
                	return ;
                }
                this.popContent['yxqz'] = $('#_yxqz').val();
                this.popContent['scrq'] = $('#_scrq').val();

                //验证数据完整性
                var _input = $("#rkgl_enter input,select");
                var haveError = false;
                for(var i = 0; i < _input.length; i++) {
                    if(_input.eq(i).attr("data-notempty") && (_input.eq(i).val() == null || _input.eq(i).val() == '')) {
                        haveError = true;
                        _input.eq(i).addClass("emptyError");
                    }
                }
                if(haveError) {
                    malert("录入区数据不完整",'top','defeadted');
                    return;
                }

                if(wrapper.isUpdate == 0){
                	//添加
                	var ypbm = wap.popContent.ypbm;
                    var xtph = wap.popContent.xtph;
                    for(var i = 0; i < rksh.jsonList.length; i++) {
                        if(rksh.jsonList[i].ypbm == ypbm && rksh.jsonList[i].xtph == xtph) {
                            malert("药品【" + this.popContent.ypmc + "】已存在,请修改已有数据!",'top','defeadted');
                            return;
                        }
                    }
                    rksh.jsonList.push(this.popContent);
                    this.popContent = {};
                    $("#ypmc").focus();
                }

                if(wrapper.isUpdate == 1){
                	var ckdh = rksh.jsonList[rksh.modifyIndex].ckdh;
                	rksh.jsonList[rksh.modifyIndex] = wap.popContent;
                    Vue.set(rksh.jsonList[rksh.modifyIndex],'ckdh',ckdh);
                    wap.closes();
                }
            },
            selectLyr: function() {
                if(this.bsList.length == 0) {
                    $("#lyks").addClass("emptyError");
                    malert("请先选择领用科室！",'top','defeadted');
                }
            },
            //领用科室加载下拉框回调
            resultKsChange: function(val) {
                if(val[1] != null) {
                    this.nextFocus(val[1]);
                }
               var types = val[2][val[2].length - 1]; //方便获取到是什么类型
                if(types == 'lyks') {
                    Vue.set(this.ckdContent, 'lyks', val[0]);

                    //请求后台获取到出库药房
                    this.dg.parm = this.ckdContent.lyks;
                    if(this.dg.parm == null || this.dg.parm == '') {
                        return false;
                    }
                    this.dg.rows = 20000;
                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=findYFByKS' +
                        '&dg=' + JSON.stringify(this.dg),
                        function(data) {
                            console.log(data.d);
                            if(data.a == 0) {
                                if(data.d == null) {
                                    Vue.set(wap.ckdContent, "lyyfmc", "科室出库");
                                } else {
                                    Vue.set(wap.ckdContent, "lyks", data.d.KSBM);
                                    Vue.set(wap.ckdContent, "lyyf", data.d.YFBM);
                                    Vue.set(wap.ckdContent, "lyyfmc", data.d.YFMC);
                                }

                            } else {
                                malert("出库药房查询失败")
                            }
                        });

                    //出库是否只允许选取当前科室人员作为领用人
                    if(wap.csqxContent.cs00200100205 == '0') {
                        wap.ryList = wap.glryList;
                    } else if(wap.csqxContent.cs00200100205 == '1') {
                        //根据科室过滤领用人
                        wap.ryList = jsonFilter(wap.glryList, "ksbm", this.ckdContent.lyks);
                    }
                }
                //回车跳转
                if(val[1] != null) {
                    this.keyCodeFunction(val[1], val[2]);
                }
            },


        }


    });

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol) {
        $(".selectGroup").hide();
    }

});
