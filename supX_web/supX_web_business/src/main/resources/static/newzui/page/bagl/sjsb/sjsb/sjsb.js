    //取开始时间
    var tableInfo = new Vue({
        el: '#wrapper',
        //混合js字典庫
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data: {
            scShow:false,
            totlePage:0,
            jsonList: [],
            scJsonList: [],
            xyOrZy: '1',      //判断是中医还是西医
            htmlData:[
                {text:'医疗机构名称',className:'cell-l'},
                {text:'医疗付款方式',className:''},
                {text:'健康卡号',className:''},
                {text:'住院次数',className:''},
                {text:'病案号',className:''},
                {text:'姓名',className:''},
                {text:'性别',className:''},
                {text:'出生日期',className:''},
                {text:'年龄',className:''},
                {text:'国籍',className:''},
                {text:'月龄',className:''},
                {text:'新生儿出生体重',className:'cell-l'},
                {text:'新生儿入院体重',className:'cell-l'},
                {text:'出生地',className:'cell-l'},
                {text:'籍贯',className:''},
                {text:'民族',className:''},
                {text:'身份证号码',className:'cell-xl'},
                {text:'职业',className:'cell-xl'},
                {text:'婚姻',className:''},
                {text:'现住址',className:'cell-l'},
                {text:'电话',className:''},
                {text:'邮编',className:''},
                {text:'户口地址',className:'cell-l'},
                {text:'邮编',className:''},
                {text:'工作单位及地址',className:'cell-l'},
                {text:'邮编',className:''},
                {text:'联系人姓名',className:''},
                {text:'关系',className:''},
                {text:'地址',className:''},
                {text:'电话',className:''},
                {text:'入院途径',className:''},
                {text:'其他医疗机构转入名称',className:'cell-l'},
                {text:'入院时间',className:''},
                {text:'入院科别',className:''},
                {text:'入院病房',className:''},
                {text:'专科科别',className:''},
                {text:'出院时间',className:''},
                {text:'出院科别',className:''},
                {text:'出院病房',className:''},
                {text:'实际住院',className:''},
                {text:'门诊急诊',className:''},
                {text:'疾病编码',className:''},
                {text:'主要诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'其他诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'入院病情',className:''},
                {text:'中毒的外部原因',className:''},
                {text:'疾病编码',className:''},
                {text:'病理诊断',className:''},
                {text:'疾病编码',className:''},
                {text:'病理号',className:''},
                {text:'药物过敏',className:''},
                {text:'过敏药物疾病',className:''},
                {text:'死亡患者尸检',className:''},
                {text:'血型',className:''},
                {text:'RH',className:''},
                {text:'科主任',className:''},
                {text:'主任医师',className:''},
                {text:'主治医师',className:''},
                {text:'住院医师',className:''},
                {text:'责任护士',className:''},
                {text:'进修医师',className:''},
                {text:'责任医师',className:''},
                {text:'实习医师',className:''},
                {text:'编目员',className:''},
                {text:'病案质量',className:''},
                {text:'质控医师',className:''},
                {text:'质控护士',className:''},
                {text:'质控日期',className:''},
                {text:'手术编码',className:''},
                {text:'手术日期',className:''},
                {text:'手术级别',className:''},
                {text:'手术名称',className:''},
                {text:'术者',className:''},
                {text:'Ι助',className:''},
                {text:'ΙΙ助',className:''},
                {text:'切口等级',className:''},
                {text:'切口愈合类别',className:''},
                {text:'麻醉方式',className:''},
                {text:'麻醉医师',className:''},
                {text:'手术编码',className:''},
                {text:'手术日期',className:''},
                {text:'手术级别',className:''},
                {text:'手术名称',className:''},
                {text:'术者',className:''},
                {text:'Ι助',className:''},
                {text:'ΙΙ助',className:''},
                {text:'切口等级',className:''},
                {text:'切口愈合类别',className:''},
                {text:'麻醉方式',className:''},
                {text:'麻醉医师',className:''},
                {text:'手术编码',className:''},
                {text:'手术日期',className:''},
                {text:'手术级别',className:''},
                {text:'手术名称',className:''},
                {text:'术者',className:''},
                {text:'Ι助',className:''},
                {text:'ΙΙ助',className:''},
                {text:'切口等级',className:''},
                {text:'切口愈合类别',className:''},
                {text:'麻醉方式',className:''},
                {text:'麻醉医师',className:''},
                {text:'手术编码',className:''},
                {text:'手术日期',className:''},
                {text:'手术级别',className:''},
                {text:'手术名称',className:''},
                {text:'术者',className:''},
                {text:'Ι助',className:''},
                {text:'ΙΙ助',className:''},
                {text:'切口等级',className:''},
                {text:'切口愈合类别',className:''},
                {text:'麻醉方式',className:''},
                {text:'麻醉医师',className:''},
                {text:'手术编码',className:''},
                {text:'手术日期',className:''},
                {text:'手术级别',className:''},
                {text:'手术名称',className:''},
                {text:'术者',className:''},
                {text:'Ι助',className:''},
                {text:'ΙΙ助',className:''},
                {text:'切口等级',className:''},
                {text:'切口愈合类别',className:''},
                {text:'麻醉方式',className:''},
                {text:'麻醉医师',className:''},
                {text:'手术编码',className:''},
                {text:'手术日期',className:''},
                {text:'手术级别',className:''},
                {text:'手术名称',className:''},
                {text:'术者',className:''},
                {text:'Ι助',className:''},
                {text:'ΙΙ助',className:''},
                {text:'切口等级',className:''},
                {text:'切口愈合类别',className:''},
                {text:'麻醉方式',className:''},
                {text:'麻醉医师',className:''},
                {text:'手术编码',className:''},
                {text:'手术日期',className:''},
                {text:'手术级别',className:''},
                {text:'手术名称',className:''},
                {text:'术者',className:''},
                {text:'Ι助',className:''},
                {text:'ΙΙ助',className:''},
                {text:'切口等级',className:''},
                {text:'切口愈合类别',className:''},
                {text:'麻醉方式',className:''},
                {text:'麻醉医师',className:''},
                {text:'离院方式',className:''},
                {text:'医嘱转院机构名称',className:'cell-l'},
                {text:'医嘱转社区机构名称',className:'cell-l'},
                {text:'31天内再住院',className:''},
                {text:'颅脑损伤入院前时间',className:'cell-l'},
                {text:'天',className:''},
                {text:'小时',className:''},
                {text:'颅脑损伤入院后时间',className:'cell-l'},
                {text:'天',className:''},
                {text:'小时',className:''},
                {text:'总费用',className:''},
                {text:'自付金额',className:''},
                {text:'一般医疗服务费',className:'cell-l'},
                {text:'一般治疗操作费',className:'cell-l'},
                {text:'护理费',className:''},
                {text:'其他费',className:''},
                {text:'病理诊断',className:''},
                {text:'实验室诊断',className:''},
                {text:'影像学诊断费',className:''},
                {text:'临床诊断项目费',className:'cell-l'},
                {text:'非手术治疗费',className:''},
                {text:'物理治疗费',className:''},
                {text:'手术治疗费',className:''},
                {text:'麻醉费',className:''},
                {text:'手术费',className:''},
                {text:'康复费',className:''},
                {text:'中医治疗费',className:''},
                {text:'西药费',className:''},
                {text:'抗菌药物费',className:''},
                {text:'中成药费',className:''},
                {text:'中草药费',className:''},
                {text:'血费',className:''},
                {text:'白蛋白类制品费',className:'cell-l'},
                {text:'球蛋白类制品费',className:'cell-l'},
                {text:'凝血因子类制品费',className:'cell-l'},
                {text:'细胞因子类制品费',className:'cell-l'},
                {text:'检查用材料费',className:''},
                {text:'治疗用材料费',className:''},
                {text:'手术用材料费',className:''},
                {text:'其他费',className:''},
            ],
            attchment_sc:[
                {text:'住院次数',className:''},
                {text:'病案号',className:''},
                {text:'手术及操作名称1_附加',className:'cell-xl'},
                {text:'手术及操作名称2_附加',className:'cell-xl'},
                {text:'手术及操作名称3_附加',className:'cell-xl'},
                {text:'手术及操作名称4_附加',className:'cell-xl'},
                {text:'手术及操作编码1_附加',className:'cell-l'},
                {text:'手术及操作编码2_附加',className:'cell-l'},
                {text:'手术及操作编码3_附加',className:'cell-l'},
                {text:'手术及操作编码4_附加',className:'cell-l'},
                {text:'择期手术1',className:''},
                {text:'择期手术2',className:''},
                {text:'择期手术3',className:''},
                {text:'择期手术4',className:''},
                {text:'术前准备时间（天）1',className:''},
                {text:'手术开始时间(年月日时分秒)1',className:''},
                {text:'手术开始时间(年月日时分秒)2',className:''},
                {text:'手术开始时间(年月日时分秒)3',className:''},
                {text:'手术开始时间(年月日时分秒)4',className:''},
                {text:'手术结束时间(年月日时分秒)1',className:''},
                {text:'手术结束时间(年月日时分秒)2',className:''},
                {text:'手术结束时间(年月日时分秒)3',className:''},
                {text:'手术结束时间(年月日时分秒)4',className:''},
                {text:'术前预防性抗菌药物给药时间(年月日时分秒)1',className:''},
                {text:'术前预防性抗菌药物给药时间(年月日时分秒)2',className:''},
                {text:'术前预防性抗菌药物给药时间(年月日时分秒)3',className:''},
                {text:'术前预防性抗菌药物给药时间(年月日时分秒)4',className:''},
                {text:'麻醉开始时间(年月日时分秒)1',className:''},
                {text:'麻醉开始时间(年月日时分秒)2',className:''},
                {text:'麻醉开始时间(年月日时分秒)3',className:''},
                {text:'麻醉开始时间(年月日时分秒)4',className:''},
                {text:'麻醉方式1',className:''},
                {text:'麻醉方式2',className:''},
                {text:'麻醉方式3',className:''},
                {text:'麻醉方式4',className:''},
                {text:'ASA麻醉分级1',className:''},
                {text:'ASA麻醉分级2',className:''},
                {text:'ASA麻醉分级3',className:''},
                {text:'ASA麻醉分级4',className:''},
                {text:'切口部位1',className:''},
                {text:'切口部位2',className:''},
                {text:'切口部位3',className:''},
                {text:'切口部位4',className:''},
                {text:'手术切口感染1',className:''},
                {text:'手术切口感染2',className:''},
                {text:'手术切口感染3',className:''},
                {text:'手术切口感染4',className:''},
                {text:'手术并发症1',className:'cell-l'},
                {text:'手术并发症2',className:'cell-l'},
                {text:'手术并发症3',className:'cell-l'},
                {text:'手术并发症4',className:'cell-l'},
                {text:'手术并发症名称1',className:'cell-l'},
                {text:'手术并发症名称2',className:'cell-l'},
                {text:'手术并发症名称3',className:'cell-l'},
                {text:'手术并发症名称4',className:'cell-l'},
                {text:'填写人员--术者或I助1',className:''},
                {text:'填写人员--术者或I助2',className:''},
                {text:'填写人员--术者或I助3',className:''},
                {text:'填写人员--术者或I助4',className:''},
                {text:'麻醉医师1',className:''},
                {text:'麻醉医师2',className:''},
                {text:'麻醉医师3',className:''},
                {text:'麻醉医师4',className:''},
                {text:'医院感染情况',className:''},
                {text:'医院感染是否与手术相关',className:''},
                {text:'医院感染是否与侵入性操作性相关',className:''},
                {text:'抗菌药物使用情况',className:''},
                {text:'抗菌药物名称1',className:''},
                {text:'抗菌药物名称2',className:''},
                {text:'抗菌药物名称3',className:''},
                {text:'抗菌药物名称4',className:''},
                {text:'抗菌药物名称5',className:''},
                {text:'抗菌药物名称6',className:''},
                {text:'是否发生压疮',className:''},
                {text:'是否住院期间发生',className:''},
                {text:'压疮分期',className:''},
                {text:'输液反应',className:''},
                {text:'引发反应的药物',className:''},
                {text:'输液临床表现',className:''},
                {text:'住院期间是否发生跌倒或坠床',className:''},
                {text:'住院期间跌倒或坠床的伤害程度',className:''},
                {text:'跌倒或坠床的原因',className:''},
                {text:'住院期间身体约束',className:''},
                {text:'离院时透析（血透、腹透）尿素氮值',className:''},
                {text:'单位负责人',className:''},
                {text:'统计负责人',className:''},
                {text:'填报人',className:''},
                {text:'联系电话',className:''},
                {text:'手机',className:''},
            ],
            csqxContent:{},
        },
        //页面渲染完成之后加载数据
        mounted: function () {
            //默认加载当前时间
            //初始化检索日期！为今天0点到今天24点
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
            laydate.render({
                elem: '#timeVal',
                 type: 'datetime',
                value:this.param.beginrq
                , eventElem: '.zui-date'
                , trigger: 'click'
                , theme: '#1ab394'
                , done: function (value, data) {
                    tableInfo.param.beginrq = value;
                }
            });
            laydate.render({
                elem: '#timeVal1',
                value:this.param.endrq,
                    type: 'datetime'
                , eventElem: '.zui-date'
                , trigger: 'click'
                , theme: '#1ab394'
                , done: function (value, data) {
                    tableInfo.param.endrq =value;
                }
            });
        },
        created: function () {
            this.$nextTick(function () {
                tableInfo.getCsqx();
            });
        },
        methods: {
            getCsqx: function () {
                var parm = {
                    "ylbm": 'N010064001',
                    "ksbm":  ksbm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0 && json.d) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N01006400144":
                                    if (csjson.csz) { //卫统四表生成直接导出 0-否,1-是
                                        tableInfo.csqxContent.N01006400144 = csjson.csz;
                                    }
                                    break;
                                case "N01006400145":
                                    if (csjson.csz) { //病案附页机构
                                        tableInfo.csqxContent.N01006400145 = csjson.csz;
                                    }
                                    break;
                            }
                        }
                    } else {
                        malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                    }
                });
            },
            //查询
            getData: function () {
                if(this.xyOrZy == 'sc'){
                    tableInfo.attachment_sc_query();
                }else{
                    common.openloading('.zui-table-view');
                    $.getJSON("/actionDispatcher.do?reqUrl=New1BaglBasbXy&types=queryBasbMsg&parm="
                        +JSON.stringify(this.param), function (json) {
                        if(json.a == 0 && json.d){
                            tableInfo.jsonList = json.d.list;
                            tableInfo.totlePage=Math.ceil(json.d.total / tableInfo.param.rows)

                        }else{
                            tableInfo.scShow=false;
                            malert('暂无数据','top','defeadted')
                        }
                    });
                    common.closeLoading();
                }
            },
            attachment_sc_query:function(){
                common.openloading('.zui-table-view');
                tableInfo.scJsonList = [];
                $.getJSON("/actionDispatcher.do?reqUrl=New1BaglBasbXy&types=attachmentsListQuerySc&parm="
                    +JSON.stringify(this.param), function (json) {
                    if(json.a == 0 && json.d){
                        tableInfo.scJsonList = json.d.list;
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                    }else{
                        tableInfo.scShow=false;
                        malert('暂无数据','top','defeadted')
                    }
                });
                common.closeLoading();
            },
            //生成
            saveDate: function(){
                common.openloading('.zui-table-view');
                this.param.xyOrZy=this.xyOrZy
                this.$http.post("/actionDispatcher.do?reqUrl=New1BaglBasbXy&types=insert&",
                    JSON.stringify(this.param)).then(function (data) {
                    common.closeLoading();
                    if(data.body.a == 0){
                        malert(data.body.c,'top','success');
                        //下载CSV文件
                        if (tableInfo.csqxContent.N01006400144 == '1' && data.body.d){
                            window.location.href = data.body.d;
                        }
                    } else {
                        malert(data.body.c,'top','defeadted');
                    }
                },function (error) {
                    common.closeLoading();
                    console.log(error);
                });
            },


            exportCSV: function(){
                common.openloading('.zui-table-view');
                this.param.xyOrZy = this.xyOrZy;
                this.$http.post("/actionDispatcher.do?reqUrl=New1BaglBasbXy&types=export&",
                    JSON.stringify(this.param)).then(function (data) {
                    common.closeLoading();
                    if(data.body.a == 0){
                        malert(data.body.c,'top','success');
                        window.location.href = data.body.d;
                    } else {
                        malert(data.body.c,'top','defeadted');
                    }
                },function (error) {
                    common.closeLoading();
                    console.log(error);
                });
            },

            //点击单选框
            zxyClick: function(val){
                this.xyOrZy = val;
                tableInfo.getData();
            }
        }
    });
    //初始化页面需要加载的数据
    tableInfo.getData();
