var chlb = new Vue({
    el: '#chlb',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        pageState: {
            jsonList: [],
            ztType: '0',
            beginrq: '',
            endrq: '',
            //jssx:'',
            cxrq: 'ryrq',
            ryks:''
        },
        ZyyjPrint: '',
        popContent: {},
        jsjlContent: {},
        jjlInfo: {},
        isShow: false,
        sxzzan: false,
        askJson: {},
        allKs:[],//所有科室
        param: {
            page: 1,
            rows: 10,
            sort: '',
            order: 'desc',
            parm: '',
        },
        title: '取消结算',
        jsjl: {},
        zt_tran: {
            '0': '在院',
            '1': '出院',
            '2': '门诊入院',
            '3': '待结算'
        },
        pxContent: {
            'sort': '0',
            'order': '0',
        },
        caqxContent: {}, //参数权限对象
        ylkh: '', //医疗卡号
        ifClick: true, // 用于判断是否点击保存按钮
        bxlbList: [],//保险类别
        bxJsxx: {}, //保险结算信息
    },
    //页面渲染完成之后加载数据
    mounted: function () {
        // this.Wf_getKs();
        this.getKsbm();
		this.GetBrfbData();
        if (loadPage.stateMap.chlb !== undefined) {
            this.setData();
        } else {
            var myDate = new Date();
            this.pageState.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
            this.pageState.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        }
        this.pageState.ztType = '0';
        this.pageState.cxrq = 'ryrq';
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#timeVal',
            value: this.pageState.beginrq,
            format: 'yyyy-MM-dd HH:mm:ss',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    chlb.pageState.beginrq = value;
                } else {
                    chlb.pageState.beginrq = '';
                }
                //获取一次列表
                chlb.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            value: this.pageState.endrq,
            format: 'yyyy-MM-dd HH:mm:ss',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    chlb.pageState.endrq = value + ' 23:59:59';
                } else {
                    chlb.pageState.endrq = '';
                }
                //获取一次列表
                chlb.getData();
            }
        });


        // 监听护士站点击费用记账和退费过来的东西
        this.hszToZygl();
        window.addEventListener("storage", function (e) {
            if (e.key === "hszToZygl" + userId && e.oldValue !== e.newValue) {
                chlb.hszToZygl();
            }
        });
    },
    updated: function () {
        changeWin();
    },
    methods: {
		//页面加载时自动获取保险类别Dddw数据
		openDjxx:function(zyh){
			
			var obj = {};
			this.updatedAjax("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryByZyh&zyh=" + zyh, function (json) {
			    if (json.a == '0' && json.d) {
					djxg.zyhdjxx = json.d
			        djxg.open();
			    } else {
			        malert(json.c, 'top', 'defeadted')
			    }
			}, function (XMLHttpRequest, textStatus, errorThrown) {
			    malert(textStatus, 'top', 'defeadted')
			});
			
			
		},
		
		//页面加载时自动获取病人费别Dddw数据
		GetBrfbData: function () {
		    $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=brfb", function (json) {
		        if (json.a == 0) {
		            djxg.brfbList = json.d.list;
		        } else {
		            malert('病人费别列表查询失败', 'top', 'defeadted');
		
		        }
		    });
		},
				
        setNl: function (item, index) {
            if (parseInt(item.nldw) != NaN) {
                return this.toAge(item.csrq || item.brjbxxModel.csrq).age + this.nldw_tran[this.toAge(item.csrq || item.brjbxxModel.csrq).unitNum]
            } else {
                return item.nl + item.nldw
            }
        },
        wdbd: function () {
            if (this.activeIndex != undefined) {
                zyh = this.pageState.jsonList[this.activeIndex].zyh
                if (this.pageState.jsonList[this.activeIndex].sfcr == '0') {
                    var bm = "N010024009", strPrint = [{reportlet: 'fpdy%2Fzygl%2Fzygl_xsrwddy.cpt', zyh: zyh}]
                } else {
                    var bm = "N010024008", strPrint = [{reportlet: 'fpdy%2Fzygl%2Fzygl_wddy.cpt', zyh: zyh}]
                }
                this.frPrint(strPrint, bm);
            } else {
                malert('请选择需要补打的病人', 'top', 'defeadted')
            }
        },
        //获取科室
        Wf_getKs: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"N050022001"}', function (json) {
                if (json.a == '0' && json.d) {
                    var rlksList = [];
                    for (var i = 0; i < json.d.length; i++) {
                        if (json.d[i].bqbm != null) {
                            json.d[i].ksmc = "【"+json.d[i].ksbm+"】"+json.d[i].ksmc
                            rlksList.push(json.d[i]);
                        }
                    }
                    chlb.allKs = rlksList;
                    chlb.popContent.ksbm = rlksList[0].ksbm;
//                	panel.allKs = json.d;
//                	panel.popContent.ksbm =json.d[0].ksbm;
                    if (sessionStorage.getItem('ksbm') != null) {
                        chlb.popContent.ksbm = sessionStorage.getItem('ksbm')
                    }
                    //科室获取成功后再查询患者信息
                    /*chlb.Wf_GetBrList(panel.popContent.brgl);
                    chlb.getCsqx();*/
                }
            });
        },

        getKsbm: function () {
            var str_param = {
                zyks:'1',
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(str_param), function (json) {
                if (json.a == '0' && json.d != null) {
                    console.log("进来了-------->"+json.d.list.length);
                    var rlksList = [];
                    if (json.d.list.length > 0 ){
						djxg.Kslist = JSON.parse(JSON.stringify(json.d.list)) ;
                        for (var i = 0; i < json.d.list.length; i++) {
                            if (json.d.list[i].bqbm != null) {
                                json.d.list[i].ksmc = "【"+json.d.list[i].ksbm+"】"+json.d.list[i].ksmc
                                rlksList.push(json.d.list[i]);
                            }
                        }
                    }
                    chlb.allKs = rlksList;
                    chlb.popContent.ksbm = rlksList[0].ksbm;
					
                    if (sessionStorage.getItem('ksbm') != null) {
                        chlb.popContent.ksbm = sessionStorage.getItem('ksbm')
                    }
                } else {
                    malert('获取科室失败','top','defeadted')
                }
            });
        },

        //科室改变事件
        Wf_KsChange: function (val) {
            //先获取住院医生的值
            chlb.pageState.ryks = val[0];
            console.log("-------->"+val[0]);
            chlb.getKsbm();
            this.goToPage(1);

        },
        frPrint: function (printData, bm) {
            window.top.J_tabLeft.csqxparm.csbm = bm;
            //打印方法调整致取参数之后
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0) {
                    if (json.d != null) {
                        chlb.ZyyjPrint = json.d[0].csz;
                    }
                    FrPrint(printData, chlb.ZyyjPrint)
                }
            });
        },
        //一卡通检索
        loadYkt: function () {
            var str = "card&" + chlb.ylkh;
            window.top.J_tabLeft.socketCd.send(str);
            var cs = 0;
            var interval = setInterval(function () {
                cs += 1;
                if (window.top.J_tabLeft.rs) {
                    if (window.top.J_tabLeft.ifok) {
                        clearInterval(interval);
                        var kh = window.top.J_tabLeft.rslmsg;
                        chlb.ylkh = kh;
                        chlb.param.parm = kh;
                        chlb.getData();
                        window.top.J_tabLeft.rs = false;
                        window.top.J_tabLeft.ifok = false;
                    } else {
                        malert("读卡失败！", 'top', 'defeadted');
                        clearInterval(interval);
                        window.top.J_tabLeft.rs = false;
                        window.top.J_tabLeft.ifok = false;
                    }
                }
                if (cs >= 5) {
                    // malert(rslmsg,'top','defeadted');
                    window.top.J_tabLeft.rs = false;
                    window.top.J_tabLeft.ifok = false;
                    clearInterval(interval);
                    malert("读卡超时！请重试", 'top', 'defeadted');
                }
            }, 800);
        },


        hszToZygl: function () {
            var hszToZygl = sessionStorage.getItem('hszToZygl' + userId);
            // sessionStorage.removeItem("hszToZygl");
            if (hszToZygl) {
                var _hszToZygl = JSON.parse(hszToZygl);
                if (_hszToZygl.type == "jztf") {
                    this.jztfButtClick(_hszToZygl.zyh, _hszToZygl.brxx);
                }
            }
        },
        mzdjs: function (zyh) {
            this.topNewPage('住院结算', 'page/zygl/rcygl/zyjs/zyjs.html');
            sessionStorage.setItem('hzglDjs', zyh);
        },
        resultchangeOd: function (val) {
            if (val[2].length > 1) {
                if (Array.isArray(this[val[2][0]])) {
                    Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    chlb.goToPage(1);
                } else {
                    Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                    if (val[3] != null) {
                        Vue.set(this[val[2][0]], val[3], val[4]);
                    }
                    chlb.goToPage(1);
                }
            } else {
                this[val[2][0]] = val[0];
                chlb.goToPage(1);
            }
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
        },
        //门诊接诊
        mzjr: function (item) {
            loadPage.saveData('chlb', {
                pageState: this.pageState,
                param: this.param,
                pageParam: {
                    brid: item.brid,
                    ghxh: item.ghxh,
                    ismzjr: 1
                }
            });
            loadPage.show('loading-page/rydj.html');
        },
        edit: function (index, item) {
                        var parm = {
                zyh: this.pageState.jsonList[index].zyh
            };
            var obj = {};
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=querrydj&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == '0' && json.d && json.d.list) {
                    obj = json.d.list[0];
                } else {
                    malert(json.c, 'top', 'defeadted')
                }
            }, function (XMLHttpRequest, textStatus, errorThrown) {
                malert(textStatus, 'top', 'defeadted')
            });
            if (this.pageState.ztType != '1' && this.pageState.ztType != '2') { // 判断是否出院
                loadPage.saveData('chlb', {
                    pageState: chlb.pageState,
                    param: chlb.param,
                    pageParam: {
                        obj: obj,
                        importFlag: "hzlbEdit"
                    }
                });
                loadPage.show("loading-page/rydj.html");
                if (chlb.pageState.ztType == '3') {
                    setTimeout(function () {
                        $("#zydb").attr('disabled', true);
                        $("#zyyj").attr('disabled', true);
                    }, 300);
                }


            } else if (this.pageState.ztType == '2') { //门诊接入
                loadPage.saveData('chlb', {
                    pageState: this.pageState,
                    param: this.param,
                    pageParam: {
                        obj: item,
                        ismzjr: '1',
                        brid: item.brid
                    }
                });
                loadPage.show("loading-page/rydj.html");
            }
        },
        setData: function () { // 设置data
            this.pageState = loadPage.stateMap.chlb.pageState;
            this.param = loadPage.stateMap.chlb.param;
        },
        ztTypeCB: function (val) {//状态下拉框选中回调
            this.pageState.ztType = val[0];
            if (this.pageState.ztType == 1) {
                //出院
                this.pageState.cxrq = 'cyrq'
            }
            if (this.pageState.ztType == 0) {
                //入院
                this.pageState.cxrq = 'ryrq'
            }

            if (this.pageState.ztType == 2) {
                //门诊接入
            }

            if (this.pageState.ztType == 3) {
                //结算
            }
            this.goToPage(1);
        },
        addData: function () { //跳转到添加入院信息视图
            loadPage.saveData('chlb', {
                pageState: this.pageState,
                param: this.param,
				pageParam:null
            });
            loadPage.show('loading-page/rydj.html');
        },
        getData: function () {
            if (this.pageState.ztType == 2) {
                if (this.param.parm != null && this.param.parm != '') {
                    this.param.page = 1;
                }
                //门诊接入
                chlb.pageState.jsonList = [];
                var json = {
                    ismzry: 1,
                    ryks: this.pageState.ryks
                };


                var parm = {
                    ismzry: '1',
                    page: this.param.page,
                    rows: this.param.rows,
                    ryks: this.pageState.ryks,
                    parm: this.param.parm,

                };

                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=hzxx&json=' + JSON.stringify(json) + '&dg=' + JSON.stringify(parm), function (json) {
                    console.log(json);
                    chlb.totlePage = Math.ceil(json.d.total / chlb.param.rows);
                    if (json.d != null && json.d.list != null) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            Vue.set(json.d.list[i], 'brjbxxModel', JSON.parse(JSON.stringify(json.d.list[i])));
                            json.d.list[i].brjbxxModel.ylkh = json.d.list[i].brjbxxModel.ylkh || json.d.list[i].brjbxxModel.brid
                            json.d.list[i].ylkh = json.d.list[i].brjbxxModel.ylkh || json.d.list[i].brjbxxModel.brid
                        }
                    }
                    chlb.pageState.jsonList = json.d.list;
                });
            } else if (this.pageState.ztType == 3) {
                if (this.param.parm != null && this.param.parm != '') {
                    this.param.page = 1;
                }
                //                if(chlb.pxContent.sort=='0'){
                //                	this.param.sort1='1';
                //                }else{
                //                	this.param.sort2='1'
                //                }
                //
                //                if(chlb.pxContent.order=='0'){
                //                	this.param.order1='1';
                //                }else{
                //                	this.param.order2='1'
                //                }

                var json = {
                    ryks: this.pageState.ryks,
                    bqcybz: 1,
                    zyzt: 0
                }
                //结算
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDownYw&types=rydjZyBqcy&dg=" + JSON.stringify(this.param)
                    + "&json=" +JSON.stringify(json)
                    , function (json) {
                    console.log(json);
                    chlb.totlePage = Math.ceil(json.d.total / chlb.param.rows);
                    for (var i = 0; i < json.d.list.length; i++) {
                        if (!json.d.list[i].brjbxxModel) {
                            json.d.list[i].brjbxxModel = {};
                        }
                        //赋值
						if(json.d.list[i].brxm){
							Vue.set(json.d.list[i].brjbxxModel, 'brxm', json.d.list[i].brxm);
						}
                        if(json.d.list[i].brxb){
                        	Vue.set(json.d.list[i].brjbxxModel, 'brxb', json.d.list[i].brxb);
                        }
                        //Vue.set(json.d.list[i].brjbxxModel,'csrq',json.d.list[i].csrq);
                    }
                    chlb.pageState.jsonList = json.d.list;
                });
            } else if (this.pageState.ztType == 1) {
                if (this.param.parm != null && this.param.parm != '') {
                    this.param.page = 1;
                }
                //出院
                var parm = {
                    parm: this.param.parm,
                    endrq: this.pageState.endrq,
                    beginrq: this.pageState.beginrq,
                    ryks: this.pageState.ryks,
                    page: this.param.page,
                    rows: this.param.rows,
                    bqcybz: 1,
                    zyzt: 1
                };
                var json = {
                    ryks: this.pageState.ryks,
                    bqcybz: 1,
                    zyzt: 1
                }
                /* if(chlb.pxContent.sort=='0'){
                     parm.order='1';
                 }else{
                     parm.order='1'
                 }*/
                //                if(chlb.pxContent.order=='0'){
                //                	parm.order1='1';
                //                }else{
                //                	parm.order2='1'
                //                }

                chlb.pageState.jsonList = [];
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDownYw&types=rydjCy&dg=" + JSON.stringify(parm)
                    + "&json=" +JSON.stringify(json)
                    , function (json) {
                    // for (var i = 0; i < json.d.list.length; i++) {
                    //     if (!json.d.list[i].brjbxxModel) {
                    //         json.d.list[i].brjbxxModel = {};
                    //     }
                    //     //赋值
                    //     Vue.set(json.d.list[i].brjbxxModel, 'brxm', json.d.list[i].brxm);
                    //     Vue.set(json.d.list[i].brjbxxModel, 'brxb', json.d.list[i].brxb);
                    // }
                    console.log(json.d.list);
                    chlb.totlePage = Math.ceil(json.d.total / chlb.param.rows);
                    chlb.pageState.jsonList = json.d.list;
                    console.log(json.d.list);
                });
            } else {
                if (this.param.parm != null && this.param.parm != '') {
                    this.param.page = 1;
                }
                //获取检索参数
                var parm = {
                    zyzt: this.pageState.ztType,
                    parm: this.param.parm,
                    cxrq: this.pageState.cxrq,
                    ryks: this.pageState.ryks,
                    page: this.param.page,
                    rows: this.param.rows,
                    bqcybz: 0,
                    gbxtbz: "0"
                };

                if (chlb.pxContent.sort == '0') {
                    parm.sort1 = '1';
                } else {
                    parm.sort2 = '1'
                }

                if (chlb.pxContent.order == '0') {
                    parm.order1 = '1';
                } else {
                    parm.order2 = '1'
                }


                chlb.pageState.jsonList = [];
                $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=querrydj&dg=" + JSON.stringify(parm) + "&parm=" + JSON.stringify(parm), function (json) {
                    chlb.totlePage = Math.ceil(json.d.total / chlb.param.rows);
                    chlb.pageState.jsonList = json.d.list;
                    console.log(json.d.list);
                });
            }
        },
        sxzz: function (zyh, item) {
            var param = {
                brid: item.brid,
                xh: zyh,
                bz: '2',
                yljgbm: item.yljgbm
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryZcHzxx&&parm=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    chlb.askJson = JSON.parse(json.d);
                    if (!chlb.askJson.YWSJ.JBXX.ZJHM) {
                        malert('身份证不能为空', 'top', 'defeadted')
                        return;
                    }
                    console.log(chlb.askJson)
                    chlb.callByRestful();
                }


            });

        },
        callByRestful() {
            var jsonPara = {
                'YWJSON': chlb.askJson
            }
            $.ajax({
                type: 'post',
                url: 'http://127.0.0.1:8081/open',
                data: JSON.stringify(jsonPara),
                contentType: 'application/x-www-form-urlencoded',
                beforeSend: function (xhr) {
                },
                dataType: "json",
                success: function (data) {
                    malert(data.YWXT.MSG)
                }
            });
        },
        yjfButtClick: function (zyh, item, isShouYJJ) { // 预交/退预交
            sessionStorage.setItem("hszToZygl" + userId, JSON.stringify({
                zyh: zyh,
                brxx: item,
                csqxContent: chlb.caqxContent,
                isShouYJJ: isShouYJJ
            }));
            this.topNewPage('收预交款', 'page/zygl/rcygl/syjk/syjk.html', 'N050022010');
        },
        jscyButtClick: function (zyh) { //结算出院
            loadPage.saveData('chlb', {
                pageState: this.pageState,
                param: this.param,
                pageParam: {
                    zyh: zyh
                }
            });
            loadPage.show("loading-page/jscy.html");
        },
        jzButtClick: function (zyh, item) {
            if (item.rycwbh == null) {
                malert('请先接科安床，在记账！', 'top', 'defeadted');
                return
            }
            sessionStorage.setItem("hszToZygl" + userId, JSON.stringify({
                zyh: zyh,
                brxx: item
            }));
            // this.topNewPage('费用记账', 'page/zygl/rcygl/fyjz/fyjz.html','N050022011');
        },
        jztfButtClick: function (zyh, item) { //
            if (item.rycwbh == null) {
                malert('请先接科安床', 'top', 'defeadted');
                return
            }
            loadPage.saveData('chlb', {
                pageState: this.pageState,
                param: this.param,
                pageParam: {
                    zyh: zyh
                }
            });
            loadPage.show("loading-page/jztf.html");
        },
        cancelJs: function (str, cb) {
            this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglCryglJsjl&types=remove', str)
                .then(function (data) {
                                        chlb.ifClick = true;
                    if (data.body.a == 0) {
                        malert('取消结算成功', 'top', 'success');
                        cb && cb();
                        var json2 = {
                            jsjlid: chlb.jsjl.jsjl.jsjlid,
                            ipAddress: window.top.navli.yourIp,
                            userName: chlb.jsjl.jsjl.jsczyxm,
                            operator: chlb.jsjl.jsjl.jsczyxm,
                            chlb: 'invEBillHospitalized',
                            yljgbm: jgbm
                        };
                        if (chlb.caqxContent.cs05002200166) {
                            chlb.$http.post(chlb.caqxContent.cs05002200166 + "/eb/writeOffEBill", JSON.stringify(json2))
                                .then(function (data) {
                                    if (data.body.code == "S0000" && data.body.result.result == "S0000") {
                                        malert("住院冲红开票成功");
                                    } else {
                                        malert("住院冲红开票失败" + data.body.msg, "bottom", "defeadted");
                                    }
                                });
                        }
                    } else {
                        malert('取消结算失败', 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
        },
        //取消结算
        qxjs: function (zyh, item) {
                        if (chlb.caqxContent.cs05002200157 == "0") {
                if (item.jsczy != userId) {
                    malert('不允许取消他人结算记录,请联系【' + item.jsczyxm + '】', 'top', 'defeadted');
                    return;
                }
            }
			let param = {
				zyh:zyh
			}
            //根据住院号查询结算记录
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglJsjl&types=queryJsjl&parm="+JSON.stringify(param), function (json) {
                chlb.jsjl = json.d;
                if (chlb.jsjl.jsjl.sfjk == '0') {
                    chlb.title = "请确认是否取消结算";
                } else {
                    chlb.title = "结算记录已交款，请确认是否取消结算";
                }
                common.openConfirm(chlb.title, function () {
                    chlb.isShow = true
                    chlb.popContent = item
                    chlb.jsjlContent = json.d.jsjl;
                });
            });
        },

        success: function () {
            if (!chlb.ifClick) return; // 已经点击过就不能再点
            chlb.ifClick = false;
            // 退第三方支付
            if (chlb.jsjl.jsjl.innerorderno && chlb.caqxContent.cs05002200168 == "001") {//贵州统一支付退费
                var parameter = {
                    innerorderno: chlb.jsjl.jsjl.innerorderno,
                    yljgbm: "000001",
                    czyh: userId,
                    subject: '住院结算退费',
                    tfje: chlb.jsjl.jsjl.xjzf
                }
                common.openloading("#chlb", "退款进行中。。。。。");
                var tfurl = chlb.caqxContent.cs05002200169;
                this.postAjax(tfurl, JSON.stringify(parameter), function (json) {
                    if (json.returnCode == 0) {//统一支付退费成功
                        malert('退款成功', 'top', 'success');
                        chlb.jsjl.jsjl.bzsm = json.subject + ",tkje:" + chlb.jsjl.jsjl.xjzf;
                        chlb.jsjl.jsjl.innerorderno = json.payno;
                    } else {//统一退费失败
                        common.closeLoading();
                        malert('退款失败!', 'top', 'defeadted');
                        return;
                    }
                });
            }
            //判断是否保险病人，保险则先取消保险结算
            var bxjk = chlb.listGetName(chlb.bxlbList, chlb.jsjl.bxlbbm, 'bxlbbm', 'bxjk');
            if (bxjk == '008') {
                this.cancelYbjs_008();
            } else {
                chlb.cancelJs(JSON.stringify(chlb.jsjl), function () {
                    chlb.getData();
                    chlb.isShow = false
                });
            }
        },

        //云南东软医保取消结算
        cancelYbjs_008: function () {

            chlb.cancelJs(JSON.stringify(chlb.jsjl), function () {
                var bxjkUrl = 'http://localhost:9001';
                chlb.bxJsxx = null;

                // 先查询保险结算记录
                var queryParm = {
                    hisywbh: chlb.jsjl.zyh
                };
                $.ajaxSettings.async = false;
                $.post(bxjkUrl + "/zyyw/queryJsxx", {'parm': JSON.stringify(queryParm)}, function (json) {
                    if (json.code == 0) {
                        chlb.bxJsxx = json.data;
                    } else {
                        malert(json.msg, 'top', 'defeadted');
                    }
                });

                //未查到保险结算信息，直接return
                if (chlb.bxJsxx == null) {
                    chlb.getData();
                    chlb.isShow = false
                    return;
                }

                // 取消保险结算
                $.post(bxjkUrl + "/zyyw/qxjs", {'parm': JSON.stringify(chlb.bxJsxx)}, function (json) {
                    if (json.code == 0) {
                        chlb.bxJsxx = {};
                        malert('医保出院办理成功！');
                    } else {
                        malert(json.msg, 'top', 'defeadted');
                    }
                });

                chlb.getData();
                chlb.isShow = false
            });
        },

        //页面加载时自动获取保险类别数据
        GetBxlbData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=bxlb", function (json) {
                if (json.a == 0) {
                    chlb.bxlbList = json.d.list;
					djxg.bxlbList = json.d.list;
					
					
                }
            });
        },

        //获取参数权限
        getCsqx: function () {
            //先获取到科室编码
            var ksparm = {"ylbm": 'N050022001'};
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                if (json.a == 0) {
                    if (json.d != null && json.d.length > 0) {
                        //获取参数权限
                        var parm = {"ylbm": 'N050022001', "ksbm": json.d[0].ksbm};
                        $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0) {
                                if (json.d.length > 0) {
                                    for (var i = 0; i < json.d.length; i++) {
                                        var csjson = json.d[i];
                                        switch (csjson.csqxbm) {
                                            case "N05002200149" :    //收费室是否允许费用记账  0-否，1-是
                                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                    chlb.caqxContent.cs05002200149 = csjson.csz;
                                                }
                                                break;
                                            case "N05002200150" :   //收费室是否允许记账退费0-否，1-是
                                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                    chlb.caqxContent.cs05002200150 = csjson.csz;
                                                }
                                                break;
                                            case "N05002200120" :    //是否允许退其它人收预交  0-不允许；1-允许
                                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                    chlb.caqxContent.cs05002200120 = csjson.csz;
                                                } else {
                                                    chlb.caqxContent.cs05002200120 = "1";
                                                }
                                                break;
                                            case "N05002200157" :    //是否允许取消他人结算记录  0-不允许；1-允许
                                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                    chlb.caqxContent.cs05002200157 = csjson.csz;
                                                } else {
                                                    chlb.caqxContent.cs05002200157 = "1";
                                                }
                                                break;
                                            case "N05002200166" :   //住院冲红电子发票 默认null url值
                                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                    chlb.caqxContent.cs05002200166 = csjson.csz;
                                                }
                                                break;
                                            case "N05002200168" :   //004 临夏州中医院POS接口 判断是甘肃还是贵州
                                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                    chlb.caqxContent.cs05002200168 = csjson.csz;
                                                }
                                                break;
                                            case "N05002200169" :   //004 临夏州中医院POS接口 判断是甘肃还是贵州
                                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                    chlb.caqxContent.cs05002200169 = csjson.csz;
                                                }
                                                break;
                                        }
                                    }
                                }
                                chlb.getData();
                            } else {
                                malert('参数权限获取失败', 'top', 'defeadted');
                            }
                        });
                    }
                } else {
                    malert('权限科室获取失败', 'top', 'defeadted');
                }
            });
        }

    },
    watch: {
        'param.parm': function () {
            chlb.getData();
        }
    },

    /*//科室改变事件
    Wf_KsChange: function (val) {
        //先获取住院医生的值
        Vue.set(this.popContent, 'ksbm', val[0]);
        chlb.Wf_GetBrList(chlb.popContent.brgl);
        chlb.getCsqx();
    },*/
    Wf_GetBrList: function (type) {
        if(!this.param.page || this.param.page <= 1){
            kp.noticeContent.zyrs = 0;
            kp.noticeContent.jrry = 0;
            kp.noticeContent.qfrs = 0;
            kp.noticeContent.yjhl = 0;
            kp.noticeContent.ejhl = 0;
            kp.noticeContent.sjhl = 0;
            kp.noticeContent.tjhl = 0;
            kp.noticeContent.bzrs = 0;
            kp.noticeContent.bwrs = 0;
            kp.noticeContent.lclj = 0;
            kp.lsBrList = [];
        }
        var zyrs = 0;
        var jrry = 0;
        var qfrs = 0;
        var yjhl = 0;
        var ejhl = 0;
        var sjhl = 0;
        var tjhl = 0;
        var bwrs = 0;
        var bzrs = 0;
        var lclj = 0;
        var start = new Date(new Date(new Date().toLocaleDateString()).getTime());
        if (this.isflag) {
            common.openloading('#jyxm_icon');
        }
        if (!this.isflag) {
            kp.isDoneCb = true;
        }
        //当前病人类型
        if (typeof type == 'object') {
            Vue.set(this[type[2][0]], type[2][1], type[0]);
            type = type[0];
            this.param.page = 1;
            kp.Brxx_List = [];
            zuiItem.Brxx_List = [];
        } else {
            type = type;
        }
        var zgzyys = null;
        if (this.popContent.brgl == 'zgbr') {
            zgzyys = userId;
        }
        // 门特患者
        var mtbr = null;
        if (this.popContent.mtbr == '0') {
            mtbr = null;
        } else if (this.popContent.mtbr == '1') {
            mtbr = '1';
        } else {
            mtbr = '0';
        }
        var queryparm = {
            ryks: this.popContent.ksbm,
            rows: this.index ? zuiItem.param.rows : 10000,
            page: this.index ? zuiItem.param.page :this.param.page,
            zyys: zgzyys,
            mtbbz: mtbr,
            parm: panel.cwbh,
        };
        if (panel.is_csqx.N03003200124 == '1' && this.popContent.zyty == 'qbhzxx' || this.popContent.zyty == 'Rcyhzxx' || this.popContent.zyty == 'bqcyhzxx' || this.popContent.zyty == 'zkhzxx') {
            queryparm.beginrq = panel.ksrq;
            queryparm.endrq = panel.jsrq;
            queryparm.cxrq="cyrq";
        }
        if (this.popContent.zyty == 'zkhzxx') {
            queryparm.zcks = this.popContent.ksbm
        }
        if(this.popContent.zyty == 'Rcyhzxx')queryparm.cxrq="cyrq";
        if(this.popContent.zyty == 'bqcyhzxx')queryparm.cxrq="bqcyrq";
        // switch (type){
        //case 'zy'://在院
        //panel.Type = type;
        queryparm.sort = panel.pxContent.sort;
        if(this.popContent.zyty == 'bqcyhzxx' && panel.pxContent.sort == 'cyrq'){
            queryparm.sort = 'bqcyrq'
        }

        if (panel.pxContent.order == '0') {
            queryparm.order = 'desc';
        } else {
            queryparm.order = 'asc'
        }
        var popContentZyzy = this.popContent.zyty;
        $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=' + this.popContent.zyty + '&parm=' + JSON.stringify(queryparm), function (json) {
            if (json.a == '0') {
                kp.scollType = true
                common.closeLoading();
                for (var i = 0; i <json.d.list.length ; i++) {
                    //json.d.list[i].brxb=panel.brxb_tran[json.d.list[i].brxb];
                    //json.d.list[i].nldw=panel.nldw_tran[json.d.list[i].nldw];
                    if((popContentZyzy == 'Rcyhzxx' || popContentZyzy == 'bqcyhzxx') && json.d.list[i].bqcyrq){//出院病人住院天数计算
                        json.d.list[i].zyts = dateDiff(kp.fDate(json.d.list[i].ryrq,'date'),kp.fDate(json.d.list[i].bqcyrq,'date'));
                    }
                    if (popContentZyzy == 'zkhzxx' && json.d.list[i].zyzt == '0') {
                        json.d.list[i].zyzt = '2'
                        json.d.list[i].ryks = queryparm.zcks;
                    }
                    //判断是否今日入院
                    if (json.d.list[i].ryrq >= start) {
                        json.d.list[i].jrrybz = '1';
                    } else {
                        json.d.list[i].jrrybz = '0';
                    }
                    zyrs++;
                    if (json.d.list[i].jrrybz == '1') {
                        jrry++;
                    }
                    if (json.d.list[i].yjhj+json.d.list[i].dbje - json.d.list[i].fyhj < 0) {
                        qfrs++;
                    }
                    if (json.d.list[i].hldj == '1') {
                        tjhl++;
                    }
                    if (json.d.list[i].hldj == '2') {
                        yjhl++;
                    }
                    if (json.d.list[i].hldj == '3') {
                        ejhl++;
                    }
                    if (json.d.list[i].hldj == '4') {
                        sjhl++;
                    }
                    if (json.d.list[i].bqdj == '1') {
                        bzrs++;
                    }
                    if (json.d.list[i].bqdj == '2') {
                        bwrs++;
                    }
                    if (json.d.list[i].icdbs == '1') {
                        lclj++;
                    }
                }
                kp.noticeContent.zyrs = (kp.noticeContent.zyrs?kp.noticeContent.zyrs:0) + zyrs;
                kp.noticeContent.jrry = (kp.noticeContent.jrry?kp.noticeContent.jrry:0) + jrry;
                kp.noticeContent.qfrs = (kp.noticeContent.qfrs?kp.noticeContent.qfrs:0) + qfrs;
                kp.noticeContent.yjhl = (kp.noticeContent.yjhl?kp.noticeContent.yjhl:0) + yjhl;
                kp.noticeContent.ejhl = (kp.noticeContent.ejhl?kp.noticeContent.ejhl:0) + ejhl;
                kp.noticeContent.sjhl = (kp.noticeContent.sjhl?kp.noticeContent.sjhl:0) + sjhl;
                kp.noticeContent.tjhl = (kp.noticeContent.tjhl?kp.noticeContent.tjhl:0) + tjhl;
                kp.noticeContent.bzrs = (kp.noticeContent.bzrs?kp.noticeContent.bzrs:0) + bzrs;
                kp.noticeContent.bwrs = (kp.noticeContent.bwrs?kp.noticeContent.bwrs:0) + bwrs;
                kp.noticeContent.lclj = (kp.noticeContent.lclj?kp.noticeContent.lclj:0) + lclj;
                zuiItem.Brxx_List = json.d.list;
                kp.lsBrList = kp.lsBrList?kp.lsBrList.concat(json.d.list):json.d.list;
                kp.Brxx_List = kp.Brxx_List.concat(json.d.list);
                panel.total = json.d.total;
                kp.isDoneCb = false;
                zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
                panel.getnljd();
                kp.brk_list(json.d.list.length);
            } else {
                kp.isDoneCb = false;
                common.closeLoading();
            }
        });
        // break;
        // case 'bqcy'://出院
        // 	//panel.Type = type;
        // 	queryparm;
        //     $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=bqcyhzxx&parm='+JSON.stringify(queryparm), function (json) {
        //         if (json.a == '0') {
        //             kp.scollType=true
        //             common.closeLoading();
        //             zuiItem.Brxx_List=json.d.list;
        //             kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //             panel.total=json.d.total;
        //             kp.isDoneCb=false;
        //             zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //             kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //             panel.getnljd();
        //             kp.brk_list(json.d.list.length);
        //         }else {
        //             kp.isDoneCb=false;
        //             common.closeLoading();
        //         }
        //         //zuiItem.total = json.d.total;
        //         //kp.total = json.d.total;
        //     });
        //     break;
        // case 'cy'://出院
        //     //panel.Type = type;
        // 	queryparm.beginrq=panel.ksrq;
        // 	queryparm.endrq=panel.jsrq;
        // 	queryparm.cxrq="cyrq";
        //     $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=Rcyhzxx&parm=" + JSON.stringify(queryparm), function (json) {
        //         if (json.a == '0') {
        //             kp.scollType=true
        //             common.closeLoading();
        //             zuiItem.Brxx_List=json.d.list;
        //             kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //             panel.total=json.d.total;
        //             kp.isDoneCb=false;
        //             zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //             kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //             panel.getnljd();
        //             kp.brk_list(json.d.list.length);
        //         }else {
        //             kp.isDoneCb=false;
        //             common.closeLoading();
        //         }
        //         //zuiItem.total = json.d.total;
        //         //kp.total = json.d.total;
        //
        //     });
        //     break;
        // case 'zk'://转科
        // 	//panel.Type = type;
        //     $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zkhzxx&parm=' + JSON.stringify(queryparm), function (json) {
        //         if (json.a == '0') {
        //             kp.scollType=true
        //             common.closeLoading();
        //             zuiItem.Brxx_List=json.d.list;
        //             kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //             panel.total=json.d.total;
        //             kp.isDoneCb=false;
        //             zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //             kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //             panel.getnljd();
        //             kp.brk_list(json.d.list.length);
        //         }else {
        //             kp.isDoneCb=false;
        //             common.closeLoading();
        //         }
        //         //zuiItem.total = json.d.total;
        //         //kp.total = json.d.total;
        //
        //     });
        //     break;
        // case 'cxbyParm':
        //     if (panel.popContent.zyty == 'zy') {
        //         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm=' + JSON.stringify(queryparm), function (json) {
        //             if (json.a == '0') {
        //                 kp.scollType=true
        //                 common.closeLoading();
        //                 zuiItem.Brxx_List=json.d.list;
        //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //                 panel.total=json.d.total;
        //                 kp.isDoneCb=false;
        //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //                 panel.getnljd();
        //                 kp.brk_list(json.d.list.length);
        //             }else {
        //                 kp.isDoneCb=false;
        //                 common.closeLoading();
        //             }
        //             //zuiItem.total = json.d.total;
        //             //kp.total = json.d.total;
        //
        //         });
        //     } else {
        //       	queryparm.beginrq=panel.ksrq;
        //     	queryparm.endrq=panel.jsrq;
        //     	$.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=cyhzxx&parm="  + JSON.stringify(queryparm), function (json) {
        //             if (json.a == '0') {
        //                 kp.scollType=true
        //                 common.closeLoading();
        //                 zuiItem.Brxx_List=json.d.list;
        //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //                 panel.total=json.d.total;
        //                 kp.isDoneCb=false;
        //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //                 panel.getnljd();
        //                 kp.brk_list(json.d.list.length);
        //             }else {
        //                 kp.isDoneCb=false;
        //                 common.closeLoading();
        //             }
        //             //zuiItem.total = json.d.total;
        //             //kp.total = json.d.total;
        //
        //         });
        //     }
        //     break;
        // case 'zgbr':
        //     if (panel.popContent.zyty == 'zy') {
        //
        //            var parm={
        //         		 ryks:this.popContent.ksbm,
        //         		 zyys:userId,
        //         		 rows:rows,
        //         		 page:page,
        //         		 parm:cxp,
        //            }
        //     	   if(panel.pxContent.sort=='cw'){
        //     		   parm.sort1='1';
        //               }else if(panel.pxContent.sort=='xm'){
        //             	  parm.sort2='1'
        //               }else{
        //             	  parm.sort3='1'
        //               }
        //               if(panel.pxContent.order=='0'){
        //             	  parm.order1='1';
        //               }else{
        //             	  parm.order2='1'
        //               }
        //         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm='+JSON.stringify(parm), function (json) {
        //             if (json.a == '0') {
        //                 kp.scollType=true
        //                 common.closeLoading();
        //                 zuiItem.Brxx_List=json.d.list;
        //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //                 panel.total=json.d.total;
        //                 kp.isDoneCb=false;
        //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //                 panel.getnljd();
        //                 kp.brk_list(json.d.list.length);
        //             }else {
        //                 kp.isDoneCb=false;
        //                 common.closeLoading();
        //             }
        //             //zuiItem.total = json.d.total;
        //             //kp.total = json.d.total;
        //
        //         });
        //     } else {
        //       	queryparm.beginrq=panel.ksrq;
        //     	queryparm.endrq=panel.jsrq;
        //
        //         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=cyhzxx&parm={"cyks":"' + this.popContent.ksbm + '","zyys":"'+userId+'","rows":"'+rows+'","page":'+page+',sort:"'+this.popContent.sort+'"}', function (json) {
        //             if (json.a == '0') {
        //                 kp.scollType=true
        //                 common.closeLoading();
        //                 zuiItem.Brxx_List=json.d.list;
        //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //                 panel.total=json.d.total;
        //                 kp.isDoneCb=false;
        //                 kp.brk_list(json.d.list.length);
        //                 panel.getnljd();
        //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //             }else {
        //                 kp.isDoneCb=false;
        //                 common.closeLoading();
        //             }
        //             //zuiItem.total = json.d.total;
        //             //kp.total = json.d.total;
        //         });
        //     }
        //     break;
        // case 'qk':
        //     if (panel.popContent.zyty == 'zy') {
        //         var parm={
        //        		 ryks:this.popContent.ksbm,
        //        		 rows:rows,
        //        		 page:page,
        //        		 parm:cxp,
        //           }
        //    	   if(panel.pxContent.sort=='cw'){
        //    		   parm.sort1='1';
        //              }else if(panel.pxContent.sort=='xm'){
        //            	  parm.sort2='1'
        //              }else{
        //            	  parm.sort3='1'
        //              }
        //              if(panel.pxContent.order=='0'){
        //            	  parm.order1='1';
        //              }else{
        //            	  parm.order2='1'
        //              }
        //         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm='+JSON.stringify(parm), function (json) {
        //             if (json.a == '0') {
        //                 kp.scollType=true
        //                 common.closeLoading();
        //                 zuiItem.Brxx_List=json.d.list;
        //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //                 panel.total=json.d.total;
        //                 kp.isDoneCb=false;
        //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //                 panel.getnljd();
        //                 kp.brk_list(json.d.list.length);
        //             }else {
        //                 kp.isDoneCb=false;
        //                 common.closeLoading();
        //             }
        //             //zuiItem.total = json.d.total;
        //             //kp.total = json.d.total;
        //         });
        //     } else {
        //       	queryparm.beginrq=panel.ksrq;
        //     	queryparm.endrq=panel.jsrq;
        //         $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=cyhzxx&parm=" + JSON.stringify(model), function (json) {
        //             if (json.a == '0') {
        //                 kp.scollType=true
        //                 common.closeLoading();
        //                 zuiItem.Brxx_List=json.d.list;
        //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
        //                 panel.total=json.d.total;
        //                 kp.isDoneCb=false;
        //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
        //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
        //                 panel.getnljd();
        //                 kp.brk_list(json.d.list.length);
        //             }else{
        //                 kp.isDoneCb=false;
        //                 common.closeLoading();
        //             }
        //             //zuiItem.total = json.d.total;
        //             //kp.total = json.d.total;
        //
        //         });
        //     }
        //     break;
        // }
    },
});
//转科按床pop
var djxg = new Vue({
    el: '#djxg',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        title: '登记信息修改',
        zgysList: [],
        cwList: [],
        Kslist: [],
        ifClick: true,//重复点击判断
        Class: true,
        bxlbList: [],//
		brfbList: [],//
		zyhdjxx:{},
		djparam:{
			zyh:'',
			ryks:'',
			ryksmc:'',
            ryrq:null,
			brfb:'',
			brfbmc:'',
			bxlbbm:'',
			bxlbmc:'',
		},
    },
    mounted:function(){
        // 入院日期
        laydate.render({
            elem: '#xgtimeRyrq',
            trigger: 'click',
            theme: '#1ab394',
            type: 'datetime',
            done: function (value, data) {
                //应该在这个储存这个值
                djxg.djparam.ryrq = value;
            }
        });
    },
    methods: {
        //关闭
        closes: function () {
            djxg.Class = true
            djxg.zyhdjxx = {};
        },
		
		commonResultChange:function(val){
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "brfb"://病人费别
                    Vue.set(this.djparam, 'brfb', val[0]);
                    Vue.set(this.djparam, 'brfbmc', val[4]);
                    this.$forceUpdate();
                    break;
				case "bxlbbm"://病人费别
				    Vue.set(this.djparam, 'bxlbbm', val[0]);
				    Vue.set(this.djparam, 'bxlbmc', val[4]);
				    this.$forceUpdate();
				    break;
				case "ryks"://病人费别
				    Vue.set(this.djparam, 'ryks', val[0]);
				    Vue.set(this.djparam, 'ryksmc', val[4]);
				    this.$forceUpdate();
				    break;		
            }
        },
        //打开
        open: function () {
			            djxg.Class = false;
			this.djparam = {
                zyh:'',
                ryks:'',
                ryksmc:'',
                ryrq:null,
                brfb:'',
                brfbmc:'',
                bxlbbm:'',
                bxlbmc:'',
            }
        },

        //转科换床
        saveDjxx: function () {
            $.ajaxSettings.async = false;
            if (!this.ifClick) {
                return;
            }//保存提交限制只允许一次
            
            
			this.djparam.zyh = this.zyhdjxx.zyh;
			
			if(!this.djparam.ryks && !this.djparam.brfb && !this.djparam.bxlbbm && !this.djparam.ryrq){
				malert("请选择需要修改的内容", 'top', 'defeadted')
				return false;
			}
			this.ifClick = false;
			
			var obj = {};
			this.updatedAjax("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=sfsupdaye&parm=" + JSON.stringify(this.djparam), function (json) {
			    if (json.a == '0' && json.d ) {
			        malert("修改成功！");
			        djxg.Class = true;
			        $("#djxg.side-form").removeClass('side-form-bg');
			        $("#djxg.side-form").addClass('ng-hide');
			        djxg.closes;
					chlb.getData();
			    } else {
			        malert('失败', 'top', 'defeadted')
			    }
				this.ifClick = true;
			}, function (XMLHttpRequest, textStatus, errorThrown) {
			    malert(textStatus, 'top', 'defeadted')
			});
			
        },
    }
});
chlb.getCsqx();
chlb.GetBxlbData();
