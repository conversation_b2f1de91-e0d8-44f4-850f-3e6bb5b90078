<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>单病种维护</title>
	<script type="application/javascript" src="/newzui/pub/top.js"></script>
	<link href="../../../../css/main.css" rel="stylesheet">
	<link type="text/css" href="dbzwh.css" rel="stylesheet"/>
</head>
<style>
	.table-hovers-filexd-l{
		border-right: none !important;
	}
	.table-hovers-filexd-r{
		border-left: none!important;
	}
	.table-hovers-filexd-r-child{
		border-right: 1px solid #1abc9c !important;
	}
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
	<div class="panel box-fixed">
		<div class="tong-top">
			<button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addData">新增</button>
			<button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
			<button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="remove">删除</button>
			<button class="tong-btn btn-parmary-b " @click="Tbsm"><i class="icon-width icon-tbsm padd-l-20"></i>说明</button>
		</div>
		<div class="tong-search">
			<div class="zui-form">
				<div class="zui-inline">
					<label class="zui-form-label padd-l-20">检索</label>
					<div class="zui-input-inline">
						<input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="zui-table-view ybglTable" id="utable1" z-height="full" style="margin-top: 108px; padding: 0 10px;border: none;width: 100%;float: left;">
		<div class="zui-table-header">
			<table class="font-14 zui-table table-width50">
				<thead>
				<tr>
					<th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'jsonList'"
																									   :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
					<th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>统计码</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>病种名称</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>疾病范围</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>手术标志</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>科室</span></div></th>
					<th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
				</tr>
				</thead>
			</table>
		</div>
		<div class="zui-table-body" @scroll="scrollTable($event)">
			<table class="zui-table table-width50">
				<tbody>
				<tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
					<td  class="cell-m"><div class="zui-table-cell cell-m">
						<input-checkbox @result="reCheckBox" :list="'jsonList'"
										:type="'some'" :which="$index"
										:val="isChecked[$index]">
						</input-checkbox>
					</div>
					</td>
					<td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.tjm"></div></td>
					<td>
						<div class="zui-table-cell cell-s " v-text="item.bzmc">
						</div>
					</td>
					<td><div class="zui-table-cell cell-s" v-text="item.fw"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="istrue_tran[item.ssbz]"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.ksmc"></div></td>
					<td class="cell-s"><div class="zui-table-cell cell-s">
						<span class="flex-center padd-t-3">
								<em class="width30">
									<i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>
								</em>
						</span>

					</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
		<div class="zui-table-fixed table-fixed-l">
			<div class="zui-table-header">
				<table class="zui-table">
					<thead>
					<tr>
						<th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox  @result="reCheckBox" :list="'jsonList'"
																					  :type="'all'" :val="isCheckAll">
                            </input-checkbox></span> <em></em></div></th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="zui-table-body" @scroll="scrollTableFixed($event)">
				<table class="zui-table">
					<tbody>
					<tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
						<td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

</div>
<!--侧边窗口-->
<div class="side-form  pop-width" :class="{'ng-hide':nums==1}"  style="padding-top: 0;"  id="brzcList" role="form">
	<div class="fyxm-side-top">
		<span v-text="title"></span>
		<span class="fr closex ti-close" @click="closes"></span>
	</div>
	<!--值域类别-->
	<div class="ksys-side" v-if="dbzwhShow">
        <span class="span0">
            <i>统计码</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
		<span class="span0">
            <i>病种名称</i>
            <input class="zui-input border-r4" v-model="popContent.bzmc" :data-notEmpty="true" @keydown="nextFocus($event)"/>
        </span>
		<span class="span0">
            <i>疾病范围</i>
            <input class="zui-input border-r4 " v-model="popContent.fw" >
        </span>
		<span class="span0">
            <i>手术标志</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" @keydown="nextFocus($event)">
        </span>
		<span class="span0">
            <i>疾病级别</i>
            <select-input @change-data="resultChange" :data-notEmpty="false"
						  :child="istrue_tran" :index="popContent.ssbz" :val="popContent.ssbz"
						  :name="'popContent.ssbz'">
			</select-input>
        </span>
		<span class="span0">
            <i>科室编码</i>
            <select-input @change-data="resultChange" :not_empty="false"
						  :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.ksbm"
						  :name="'popContent.ksbm'" :search="true">
						        </select-input>
			<input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
	</div>
	<div class="ksys-side line-height30" v-if="dbzwhShow==false">
		<p>1、ICD＿10(或ICD_9_CM3)编码范围均以'> '作结束标志:所含分隔符如：'>'、','、'-'均为半角;</p>
		<p>2、如为单个病种，则直接录入疾病统计编码，如‘感染性腹泻’编码范围为‘A04.903>’；如为单个编码段，则直接录入段所属编码，如'胃恶性肿瘤'编码范围为'C16＞'；如为一个编码区间，则在起止编码间用‘－’联接，如'病毒性肝炎'编码范围为'B15-B19>'；如为多个不连续段，则各段间用‘，’号分隔，如'浸润性肺结核'编码范围为'A15.0-A15.2,A16.0-A16.2>','急性白血病'编码范围为'C91.0,C92.0,C93.0,C94.0,C95.0>';当用'－'分隔时分隔符两边的编码长度一致!</p>
		<p>3、上列表中顺序为报表中顺序.</p>
	</div>
	<div class="ksys-btn" v-show="dbzwhShow">
		<button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
		<button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
	</div>
</div>


<script src="dbzwh.js"></script>

</body>
</html>