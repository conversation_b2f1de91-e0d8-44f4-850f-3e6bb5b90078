<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>数据元值域代码</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="sjyzy.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">添加</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
            <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">原值域分类</label>
                    <div class="zui-input-inline margin-l13 wh100">
                        <!--<select class="zui-input">-->
                            <!--<option></option>-->
                        <!--</select>-->
                        <select-input @change-data="resultChange"
                                      :not_empty="false" :child="zYList"
                                      :index="'sjyzymc'" :index_val="'sjyzymc'"
                                      :val="sjyzymc" :search="true" :name="'sjyzymc'"
                                      id="sjyzymc" :index_mc="'sjyzymc'">
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view ybglTable padd-l-10 padd-r-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="sexs" z-width="60px">
                        <div class="zui-table-cell">元值域id</div>
                    </th>
                    <th z-field="sex" z-width="80px">
                        <div class="zui-table-cell">元值域代码</div>
                    </th>
                    <th z-field="city" z-width="80px">
                        <div class="zui-table-cell">元值域简码</div>
                    </th>
                    <th z-field="sysx" z-width="100px">
                        <div class="zui-table-cell">元值域名称</div>
                    </th>
                    <th z-field="jm1" z-width="100px">
                        <div class="zui-table-cell">元值域值编码</div>
                    </th>
                    <th z-field="jm2" z-width="100px">
                        <div class="zui-table-cell">元值域值名称</div>
                    </th>
                    <th z-field="jm3" z-width="100px">
                        <div class="zui-table-cell">元值域值简码</div>
                    </th>
                    <th z-field="jm4" z-width="100px">
                        <div class="zui-table-cell">元值域值默认值</div>
                    </th>
                    <th z-field="jm5" z-width="100px">
                        <div class="zui-table-cell">元值域值序号</div>
                    </th>
                    <th z-field="jm6" z-width="100px">
                        <div class="zui-table-cell">元值域值说明</div>
                    </th>
                    <th z-field="jm7" z-width="100px">
                        <div class="zui-table-cell">状态</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-height" id="zui-table">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  @dblclick="edit($index)" :tabindex="$index" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.sjyzyid"></div></td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sjyzydm" :data-title="item.sjyzydm"></i>
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sjyzyjm" :data-title="item.sjyzyjm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sjyzymc" :data-title="item.sjyzymc"></i>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.sjyzyz"></div></td>
                    <td><div class="zui-table-cell" v-text="item.sjyzyzhy"></div></td>
                    <td><div class="zui-table-cell" v-text="item.sjyzyzhyjm"></div></td>
                    <td><div class="zui-table-cell" v-text="item.sjyzyzmrz"></div></td>
                    <td><div class="zui-table-cell" v-text="item.sjyzyzplsx"></div></td>
                    <td>
                        <div class="zui-table-cell relative" >
                            <i class="title title-width" v-text="item.sjyzyzsm" :data-title="item.sjyzyzsm"></i>

                        </div>

                    </td>
                    <td><div class="zui-table-cell">
                        <div class="switch">
                            <input  type="checkbox" :checked="item.scbz==0?true:false" disabled/>
                            <label></label>
                        </div>
                    </div>
                    </td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>元值域代码</i>
                    <input type="text" class="label-input" v-model="popContent.sjyzydm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>元值域id</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.sjyzyid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>元值域简码</i>
                    <input type="text" class="label-input"  v-model="popContent.sjyzyjm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>元值域名称</i>
                    <input type="text" class="label-input"  v-model="popContent.sjyzymc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>元值域值编码</i>
                    <input type="text" class="label-input"   v-model="popContent.sjyzyz" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>元值域值名称</i>
                    <input type="text" class="label-input"  v-model="popContent.sjyzyzhy" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>元值域值简码</i>
                    <input type="text" class="label-input"  v-model="popContent.sjyzyzhyjm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>元值域值默认值</i>
                    <input type="text" class="label-input"  v-model="popContent.sjyzyzmrz" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>元值域值序号</i>
                    <input type="text" class="label-input"  v-model="popContent.sjyzyzplsx" @keydown="nextFocus($event)"/>
            </li>
            <li style="width:100%">
                    <i>说明</i>
                    <input type="text" class="label-input" style="width: 76%;"  v-model="popContent.sjyzyzsm" @keydown="nextFocus($event)"/>
            </li>
            <li id="jyxm_icon">
                    <i>状态</i>
                    <div class="switch sy-switch">
                        <input  type="checkbox" :checked="popContent.scbz==0?true:false"/>
                        <label></label>
                    </div>
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="sjyzy.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>