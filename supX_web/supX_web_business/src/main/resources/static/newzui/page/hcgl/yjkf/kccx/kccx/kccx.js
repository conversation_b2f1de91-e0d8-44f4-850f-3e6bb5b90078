var qjindex = '';
var zlxmbm = "";
var wrapper = new Vue({
    el: '#jyxm_icon',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
    data: {
        jsonList: [],
        yfkfList: [
            {
                kfbm:'03',
                kfmc:'材料库房',
                ksbm:'042'
            },
            {
                kfbm:'06',
                kfmc:'体外诊断试剂库',
                ksbm:'0952'
            },
            {
                kfbm:'07',
                kfmc:'设备综合库',
                ksbm:'0952'
            },
            {
                kfbm:'08',
                kfmc:'卫消库',
                ksbm:'0952'
            },
            {
                kfbm:'11',
                kfmc:'零成本库房',
                ksbm:'0952'
            }
        ],
        totalAmount:0,
        param: {
            'page': 1,
            'rows': 10,
            'sort': '',
            'order': 'desc',
            'shzfbz': 1,
            'kfbm': '',
            'beginrq': null,
            'endrq': null,
            'parm': '',
            'cxfs':'0'
        },
        search: '',
        options: {
            '0': '全部',
            '1': '明细',
            '2': '非零库存'
        }
    },
    mounted:function(){
        this.getKfData();
    },
    updated:function () {
        changeWin()
    },
    methods: {
        showTime: function (elem, key) {
            laydate.render({
                elem: '#' + elem
                , show: true, //直接显示
                theme: '#1ab394',
                done: function (value, data) {
                    wrapper.param[key] = value;
                }
            });
        },
        getKfData: function () {
            //库房列表
            // $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf', function (data) {
            //         if (data.a == 0) {
            //             wrapper.yfkfList = data.d.list;
            //             wrapper.param.kfbm=data.d.list[0]['kfbm']
            //             wrapper.getData();
            //             wrapper.$forceUpdate()
            //         } else {
            //             malert(data.c,'top','defeadted');
            //         }
            //     });
            //获取列表
            // 修改成从权限中获取库房列表 用例:N040100011009
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100011009"}',
                function (data) {
                    if (data.a == 0 ) {
                        // wrapper.yfkfList = data.d;
                        wrapper.param.kfbm=wrapper.yfkfList[0]['kfbm']
                        wrapper.getData();
                        wrapper.$forceUpdate()
                    } else {
                        malert('库房列表获取:' + data.c + ',请查看权限配置', 'top', 'defeadted');
                    }
                });

        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "kfbm":
                    Vue.set(this.param, 'kfbm', val[0]);
                    Vue.set(this.param, 'kfmc', val[4]);
                    this.getData()
                    break;
                case "cxfs":
                    Vue.set(this.param, 'zeroKc', null);
                    if (val[0] == 0) {
                        this.qShow = true;
                    } else if (val[0] == 1) {
                        this.qShow = false;
                    } else {
                        this.qShow = false;
                        Vue.set(this.param, 'zeroKc', '1');
                    }
                    Vue.set(this.param, 'cxfs', val[0]);
                    Vue.set(this.param, 'fsmc', val[4]);
                    this.getData()
                    break;
                default:
                    break;
            }
        },
        getData: function () {
            if (this.param.cxfs != 0) {
                this.qShow = false;
            } else {
                this.qShow = true;
            }

            this.param.sort = this.param.cxfs == 0 ? null : 'sum'
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=kc&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                }
            });
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=kcze&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0" && json.d && json.d.totalAmount) {
                    wrapper.totalAmount = json.d.totalAmount;
                }else{
                    wrapper.totalAmount = 0;
                }
            });
        },
        //单击选中
        checkOne: function (index, item) {
            this.ypSelected = item;
            this.isCheckAll = false;
            this.isChecked = [];
            this.isChecked[index] = true;
        },
        //双击修改有效期和批次停用
        edit: function () {
            wap.open();
            Vue.set(this.popContent, 'xxq', this.fDate(this.ypSelected.yxqz, 'date'));
            Vue.set(this.popContent, 'xpcty', this.ypSelected.tybz);
            Vue.set(this.popContent, 'kfbm', this.ypSelected.kfbm);
            Vue.set(this.popContent, 'xtph', this.ypSelected.xtph);
            Vue.set(this.popContent, 'ypbm', this.ypSelected.ypbm);
        },
    },
});

var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: false,
        popContent: {},
        isKeyDown: null,
        title: '有效期、批次停用修改'
    },

    methods: {
        //关闭
        closes: function () {
            $(".side-form").removeClass('side-form-bg')
            $(".side-form").addClass('ng-hide');

        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg')
            $(".side-form").removeClass('ng-hide');
        },
        saveData: function () {
            if (this.popContent.xxq == null) {
                alert("请输入有效期", 'top', 'defeadted')
                return;
            }
            if (this.popContent.xpcty == null) {
                alert("请输入停用标志", 'top', 'defeadted')
                return;
            }
            wap.param.xxq = $('#_xxq').val();
            $.getJSON("/actionDispatcher.do?reqUrl=YkglKfcxCrcx&types=update&parm=" +
                JSON.stringify(this.param),
                function (data) {
                    if (data.a == 0) {
                        malert("数据保存成功", 'top', 'success');
                        //刷新页面
                        wap.closes();
                        wrapper.getData();
                    } else {
                        malert("上传数据失败", 'top', 'defeadted');
                    }
                })
        }

    }


});

laydate.render({
    elem: '.ytimes'
    , trigger: 'click'
    , theme: '#1ab394', done: function (value, data) {
        wap.param.xxq = value;
    }
});



