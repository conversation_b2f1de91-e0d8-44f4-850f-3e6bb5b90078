(function() {
    $(".zui-table-view").uitable();
	var tableInfo = new Vue({
		el: '#kswh',
		//混合js字典庫
		mixins: [dic_transform, tableBase, mConfirm, baseFunc],
		data: {
			jsonList: [],
			ksVal: null,
			param: {},
            isCheckedall:false,
			type: null
		},
		methods: {
            // 选中单条
            checkOne: function (event,index) {
                if(event.srcElement.checked==true){
                    this.isChecked[index] = false;

                }else{
                    this.isChecked[index] = true;
                }
            },

            // 选中全部
            checkAll: function (event) {
                if (event.srcElement.checked==true) {
                    for (var i = 0; i < this.jsonList.length; i++) {
                        Vue.set(this.isChecked,i,true)
                        this.isCheckedall=true
                        // this.isChecked[i] = true;
                    }
                } else {
                    this.isChecked = [];
                    this.isCheckedall=false
                }
            },
			getData: function() {
				this.param = {
					'page': 1,
					'rows': 10,
					'sort': '',
					'order': 'asc',
					'ksrq': tableInfo.param.ksrq,
					'jsrq': tableInfo.param.jsrq
				};
				$.getJSON("/actionDispatcher.do?reqUrl=MzysCxtjYsgzl&types=query&parm=" + JSON.stringify(this.param), function(json) {
					if(json.a == "0") {
						tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
						tableInfo.totlePage = tableInfo.totlePage == 0 ? 1 : tableInfo.totlePage;
						var list = json.d.list;
						for(var i = 0; i < list.length; i++) {
							if(list[i].mzysxm == null) {
								list[i].mzysxm = "无";
							}
						}
						tableInfo.jsonList = list;
						tableInfo.ksVal = '0005';
					}
				});
			}
		}
	});
	//初始化页面需要加载的数据
	tableInfo.getData();

	//为table循环添加拖拉的div
	var drawWidthNum = $(".patientTable tr").eq(0).find("th").length;
	for(var i = 0; i < drawWidthNum; i++) {
		if(i >= 2) {
			$(".patientTable th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
		}
	}
    laydate.render({
        elem: '#dbegin'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,format:'yyyy-MM-dd HH:mm:ss'
        ,done:function (value,data) {
            tableInfo.param.ksrq=value
        }
    });
    laydate.render({
        elem: '#dEnd'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,format:'yyyy-MM-dd HH:mm:ss'
        ,done:function (value,data) {
            tableInfo.param.jsrq=value
        }
    });
})();