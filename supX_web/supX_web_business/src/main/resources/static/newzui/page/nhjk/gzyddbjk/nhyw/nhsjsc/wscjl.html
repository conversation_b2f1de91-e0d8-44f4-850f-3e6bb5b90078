<div id="wscjl" class="padd-l-10 margin-top-10">
    <div class=" flex-container padd-b-10">
        <button class="tong-btn btn-parmary" @click="getData">刷新</button>
        <button class="tong-btn btn-parmary" @click="upload">上传费用</button>
    </div>
    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">项目名称</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">类别</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl text-left">农保类别</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">单价</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">数量</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">金额</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">项目编码</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">记录ID</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">收费日期</div>
                    </th>
                    <th><div class="zui-table-cell cell-s">操作员</div></th>
                    <th><div class="zui-table-cell cell-s">住院医生</div></th>
                    <th><div class="zui-table-cell cell-s">医嘱序号</div></th>
                    <th><div class="zui-table-cell cell-s">退费ID</div></th>
                    <th><div class="zui-table-cell cell-s">保内外</div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one over-auto"   @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                    <td  class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td><div  class="zui-table-cell cell-s">{{item.mxfyxmmc}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.fylbmc}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.mc}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.fydj}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.fysl}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.fyje}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.mxfyxmbm}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.fyid}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{fDate(item.sfrq,'yyyy-MM-dd')}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.czyxm}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.zyysxm}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.yzxh}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.tfid}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{nhtclb_tran[item.fw]}}</div></td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
    <div class="tableDiv addList">
        <div class="nh-total flex-container">
            <div>合计：<span>{{totalContent.bs}}</span>笔</div>
            <div>累计：<span>{{fDec(totalContent.account,2)}}</span>元</div>
        </div>
    </div>
</div>
<script type="application/javascript" src="wscjl.js"></script>