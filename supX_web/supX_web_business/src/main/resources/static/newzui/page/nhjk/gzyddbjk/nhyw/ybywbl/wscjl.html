<div id="wscjl">
    <div class="toolMenu">
        <button @click="getData">刷新</button>
        <button id="scfyButton" @click="upload">上传费用</button>
        <!-- <button @click="showall">全部未上传记录</button> -->
       	<span style="color:blue;">每次最多上传100条费用信息，如果超过，则分次上传！  </span>
    </div>
    <div>
     <span v-text= "error"></span>
    </div>
    <div class="tableDiv" style="margin: 10px;">
        <table class="patientTable" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;">
            <tr>
                <th class="tableNo"></th>
                <th><input type="checkbox" v-model="fyisCheckAll" @click="fyCheckAll('jsonList')" /></th>
                <th>项目编码</th>
                <th>项目名称</th>
                <th>医保编码</th>
                <th>医保项目名称</th>
                <th>类别</th>
                <!-- <th>支付类别</th> -->
                <th>单价</th>
                <th>数量</th>
                <th>金额</th>
                <th>记录ID</th>
                <th>收费日期</th>
                <th>操作员</th>
                <th>住院医生</th>
                <th>医嘱序号</th>
            </tr>
            </thead>
            <tbody>
            <tr><th v-for="item in 16"></th></tr>
            <tr v-for="(item, $index) in jsonList" @click="fyCheckOne($index)"
                :class="[{'tableTrSelect':isfyChecked[$index]},{'tableTr': $index%2 == 0},{'yqq':'yqq'}]">
                <th class="tableNo" v-text="$index+1"></th>
                <th><input type="checkbox" name="checkNo" v-model="isfyChecked[$index]" @click.stop="fyCheckSome($index)"/></th>
                <td v-text="item.MXFYXMBM"></td>
                <td v-text="item.MXFYXMMC"></td>
                <td v-text="item.ITEM_CODE"></td>
                <td v-text="item.HIS_ITEM_NAME"></td>
                <td v-text="item.FYLBMC"></td>
               <!--  <td v-text="item.mc"></td> -->
                <td v-text="item.PRICE"></td>
                <td v-text="item.DOSAGE"></td>
                <td v-text="item.MONEY"></td>
                <td v-text="item.FYID"></td>
                <td v-text="fDate(item.FEE_DATE,'yyyy-MM-dd')"></td>
                <td v-text="item.CZYXM"></td>
                <td v-text="item.ZYYSXM"></td>
                <td v-text="item.YZXH"></td>
            </tr>
            </tbody>
        </table>
        <div class="nh-total">
            <div>
                合计：<span>{{totalContent.bs}}</span>笔
            </div>
            <div>
                累计：<span>{{fDec(totalContent.account,2)}}</span>元
            </div>
        </div>
    </div>
</div>
<script type="application/javascript" src="wscjl.js"></script>