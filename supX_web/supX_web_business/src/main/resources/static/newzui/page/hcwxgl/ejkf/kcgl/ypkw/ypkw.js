var opType = 'save';
var func = 'New1YfbKcglYpkw';
    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data:{
            //库房列表
            kfList: [],
            //库房
            yf: 0,
            jsonList: [],
            popContent:{},
            YfkwList: [],
            totlePage: 0,
            isChecked: [],
            isCheckAll: false,
            testVal: {},
            isShowYfkw: true,
            isShowYpkw: false,
            search : '' ,
        },
        updated:function () {
            changeWin()
        },
        mounted:function(){
            this.getYf();
        },
        methods:{
            AddMdel:function () {
                wap.title='新增药品库位';
                //校验库房
                if(!this.param.yfbm ) {
                    malert('请先选择药房','top','defeadted');
                    return;
                }else{

                    wap.open();
                    wap.popContent={};
                }
            },

            getYf: function() {
                //加载药房列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
                    function(data) {
                        if(data.a == 0) {
                            wrapper.kfList = data.d.list;
                            Vue.set(wrapper.param,'yfbm',wrapper.kfList[0].yfbm);
                            wrapper.getData();
                            wap.getYp()
                        } else {
                            malert('失败','top','defeadted')
                        }

                    });
            },
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                Vue.set(this.param, 'yfbm', val[0]);
                Vue.set(this.param, 'yfmc', val[4]);
                this.getData();
            },
            getData: function() {
                common.openloading('.zui-table-view')
                $.getJSON('/actionDispatcher.do?reqUrl=' + func + '&types=query&json=' + JSON.stringify(this.param), function(json) {
                    if(json.a == "0") {
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.YfkwList = json.d.list;
                        console.log(wrapper.YfkwList);
                    }
                });
                common.closeLoading()
            },
			remove:function(num){
				//准备参数
				//			this.popContent.ypbm = this.yp.ypbm;
				//保存药品库位
				if('YfbKcglYpkw' == func) {
				    //				this.popContent.kwbm = this.kw.kwbm;
				}

				var json = this.YfkwList[num];
				var url = '/actionDispatcher.do?reqUrl=' + func + '&types=delete';
				//console.log(json)
				//return
				//保存数据
				this.$http.post(url, JSON.stringify(json)).then(function(data) {
				    if(data.body.a == 0) {
				        malert("删除数据成功",'top','success');
				        // wap.closes();
				        wrapper.getData(wrapper.popContent);
				        this.popContent={};
				    } else {
				        malert("删除数据失败",'top','defeadted');
				    }
				}, function(error) {
				    console.log(error);
				});
			},
            edit: function(num) {
                wap.open();
                wap.title='编辑'
                opType = "modify";
                if(num == null) {
                    for(var i = 0; i < this.isChecked.length; i++) {
                        if(this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if(num == null) {
                        malert("请选中你要修改的数据",'top','defeadted');
                        return false;
                    }
                }

                wap.popContent = this.YfkwList[num];
            },
        }
    });

    //右侧
    var wap=new Vue({
        el: '#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
		components: {
		    'search-table': searchTable
		},
        data: {
            dg: {
                page: 1,
                rows: 2000,
                sort: '',
                order: 'asc'
            },
            //药品列表
            ypList: [],
            //医疗机构列表
            jgList: [],
            //医疗机构
            yljg: 0,
            //库位列表
            kwList: [],
            //药品
            yp: 0,
            //库位
            kw: 0,
            isShow: false,
            isShowAddYfkw: false,
            isShowAddYpkw: false,
            popContent: {},
			popContentTp: {},
            isKeyDown: null,
            title: null,
            time: null,
            param: {
                page: 1,
                rows: 20,
                sort: '',
                order: 'asc'
            },
			searchCon: [],
			searchFy:[],
			them: {
			    '药品编号': 'ypbm',
			    '药品名称': 'ypmc',
			    '规格': 'ypgg',
			    '分装比例': 'fzbl',
			    '药品剂型': 'jxmc'
			},
        },
        mounted:function(){
        },
        methods: {
        //药品名称下拉table检索数据
        changeDown: function (event, type, searchCon) {
             this.keyCodeFunction(event, 'popContentTp', 'searchCon');
            //选中之后的回调操作
            if (event.keyCode == 13) {
                this.nextFocus(event);
                $(".selectGroup").hide();
                this.selSearch=-1;
				Object.assign(this.popContent,this.popContentTp)
            }
        },
        //当输入值后才触发
        change: function (add, val) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            this.popContent['ypmc'] = val;
            this.param.parm = val;
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc&dg=' +
                    JSON.stringify(this.param) + '&json=' + JSON.stringify(this.searchFy), function (data) {
                    if (data.a == 0) {
                        if (add) {
                            wap.searchCon = wap.searchCon.concat(data.d.list)
                        } else {
                            wap.searchCon = data.d.list;
                        }
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                        if (data.d.list.length != 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show()
                        } else {
                            $(".selectGroup").hide();
                        }
                    } else {
                        malert("药品检索失败!",'right','defeadted');
                    }

                });
        },
        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                this.dg.page++;
                this.change(true, this.popContent['ypmc'])
                return false;
            }
			Object.assign(this.popContent,item)
            $(".selectGroup").hide();
            this.selSearch=-1;

        },
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            getYp:function(){
                var yf = {};
                for(var i = 0 ; i < wrapper.kfList.length ; i++){
                    if(wrapper.kfList[i].yfbm == wrapper.param.yfbm){
                        yf = wrapper.kfList[i];
						wap.searchFy = wrapper.kfList[i];
                        break;
                    }
                }

                // $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc&dg=' +
                //     JSON.stringify(this.param) + '&json=' + JSON.stringify(yf),
                //     function(data) {
                //         if(data.a == 0) {
                //             wap.ypList = data.d.list;
                //         } else {
                //             malert(data.c,'top','defeadted')
                //         }
                //     });

                //加载库位信息列表
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglYfkw&types=query&json=' + JSON.stringify(yf),
                    function(data) {
                        if(data.a == 0) {
                            wap.kwList = data.d;
                        } else {
                            malert(data.c,'top','defeadted')
                        }
                    });
            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },
            //保存库位信息
            saveData: function() {
                //准备参数
                this.popContent.yfbm = wrapper.param.yfbm;
                //			this.popContent.ypbm = this.yp.ypbm;
                //保存药品库位
                if('YfbKcglYpkw' == func) {
                    //				this.popContent.kwbm = this.kw.kwbm;
                }

                var json = this.popContent;
                var url = '/actionDispatcher.do?reqUrl=' + func + '&types=save';
                if(opType == "modify") {
                    url = '/actionDispatcher.do?reqUrl=' + func + '&types=update';
                }
                //console.log(json)
                //return
                //保存数据
                this.$http.post(url, JSON.stringify(json)).then(function(data) {
                    if(data.body.a == 0) {
                        malert("上传数据成功",'top','success');
                        // wap.closes();
                        wrapper.getData(wrapper.popContent);
                        this.popContent={};
                    } else {
                        malert("上传数据失败",'top','defeadted');
                    }
                }, function(error) {
                    console.log(error);
                });
            }
        }
    });
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            // wrapper.param.time = value
            wrapper.param.beginrq = value.slice(0,10);
            wrapper.param.endrq =value.slice(13,23);
            wrapper.getData();
        }
    });



