
.cgjy-menu{
  position: absolute;
  top:44px;
  left: 572px;
  display: none;
  background: #fff;
  border: 1px solid #00cccc;
  border-top: none;
  width: 100px;
  z-index: 9999;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  text-align: center;
  a{
    display: block;
    color:#1abc9c;
    line-height:30px;
    cursor: pointer;

  }
}
.cgjy-btn:hover .cgjy-menu{
  display: block;
}

.tab-message{
  width: 100%;
  height:46px;
  background: #1abc9c;
  .hidden{
    display: none;
    color: #fff;
    font-size: 16px;
    float: left;
    line-height: 46px;
    padding-left: 30px;
  }
  .tab-a{
    width: 530px;
    float: left;
    display: inline-block;
    a{
      width: 120px;
      text-align: center;
      font-size: 16px;
      height: 44px;
      display: inline-block;
      line-height: 44px;
      margin-top: 2px;
      color: #fff;
      cursor: pointer;

    }

    .active{
      color: #1abc9c;
      background: #fff;
    }

  }
  .tab-right{
    width:auto;
    line-height: 46px;
    float: right;
    color: rgba(255,255,255,.56) !important;
    background: #1abc9c !important;
    font-size: 16px;
    padding-right: 30px;
  }
}

.tab-box-list{
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.fr{
  float: right !important;
  line-height: 46px;
  color: rgba(255,255,255,.56);
  padding-right: 30px;
  font-size: 20px;
}
.tab-edit-list{
  width: 100%;
  padding:20px 18px;
  box-sizing: border-box;
  li{
    width: calc(~"(100% - 38px)/3");
    float: left;
    margin-right: 19px;
    margin-bottom: 20px;
    label{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color:#7f8fa4;
      font-size: 14px;
      position: relative;
      letter-spacing: 1px;
      i{
        width: 100px;
        display: block;
        text-align: justify;
        font-style: normal;
      }
      .edit-select{
        width:178px;
        height: 36px;
        background: none;
        border: 1px solid #dfe3e9;
        margin-left:5px;
        border-radius: 4px;
        text-indent: 20px;
      }
      .label-input{
        width: 152px;
        height: 36px;
        margin-left:5px;
        background: none;
        border: 1px solid #dfe3e9;
        border-radius: 4px;
        float: right;
        text-indent: 20px;
        position: relative;
      }
    }
    &:nth-child(3n){
      margin-right: 0;
    }
  }
  .label-time,.label-time1,.label-time2,.label-time3{
    background: #00B83F;

  }
}
i,em{
  font-style: normal;
}
.tab-table{
  width: 100%;
  float: left;
  height: 300px;
  padding-bottom: 30px;
  border-bottom: 1px solid #dfe3e9;
  .tab-suoxie{
    li{
      width:calc(~"(100% / 7)") ;
      .tab-gl{
        width: 120px;
        padding: 0 10px;
        border: 1px solid  #1abc9c;
        border-radius: 4px;
        font-style: normal;
        height: 36px;
        line-height: 36px;
        margin-top: 9px;
        display: flex;
        justify-content: space-between;
        em{
          color: #1abc9c;
        }
      }
      .tab-blue{
        color: #15aa52;
      }
      .tab-red{
        color: #ff6151;
      }
    }
  }
  ::-webkit-scrollbar
  {
    width:5px;
    height: 0;
  }
  /* 垂直滚动条的滑动块 */
  ::-webkit-scrollbar-thumb:vertical {
    border-radius:5px;
    -webkit-box-shadow: inset 0 0 0 rgba(0,0,0,.3);
    background-color:#f9f9f9;
  }

}
.tab-tables{
  height: 260px;
  overflow: auto;
}
.tab-confirm{
  width: 100%;
  position: absolute;
  bottom: 20px;
  display: flex;
  justify-content: flex-end;
  right: 0;
  button{
    margin-right: 20px;
  }
}
.tab-kuai{
  width: 100%;
  padding: 20px 18px;
  p{
    font-size:14px;
    color:#1abc9c;
    line-height:28px;
    text-align:left;
  }
}
.isTabels{
  width: 100%;
  height: 100%;
  display: none;
  position: fixed;
  z-index: 9999;
  background: rgba(0,0,0,.5);
  top: 0;
  .table{
    left: 50%;
    top:50%;
    position: fixed;
    z-index: 9999;
    transform: translate(-50%,-50%);
  }
}
.pop-cig{
  width:100%;
  height: 46px;
  line-height: 46px;
  background:#1abc9c;
  color: #fff;
  font-size: 16px;
  padding-left: 20px;
  a{
    font-size: 36px;
  }
}
.pop-search{
  padding: 14px 20px;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  i{
    margin-right: 10px;
  }

}
.pop-input{
  width: 182px;
  height: 32px;
  text-indent: 10px;
  border: 1px solid #dfe3e9;
  border-radius: 4px;
}
.content-padding{
  width: 100%;
  padding:0 10px;
}
.pop-cg{
  width: 100%;
  border-top: none;
  li{
    width: calc(~"(100% / 5)");
  }
  .cgjy-color{
    color:#2885e2;
  }
}
.pop-ci{
  width: 100%;
  border-top: none;
  height:288px;
  overflow: auto;
  display: inherit;
  border-right: none;
  li{
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid #dfe3e9;
    i{
      display: block;
      float: left;
      width: calc(~"(100% / 5)");
    }
    &:first-child{
      border-top: none;
    }
  }
}
.dbclick{
  width: 100%;
  font-size:14px;
  color:#f2a654;
  line-height:54px;
  float: left;
  .addfl{
    float: left;
    width: auto;
    max-width: 500px;
  }
  .addfr{
    float: right;
    padding-top:7px;
    .zui-btn{
      padding:5px 30px;
    }
  }

}
.jieguo{
  width: 100%;
  height: 410px;

}
.jieguo-left{
  width:310px;
  height:410px;
  float: left;
  ul{
    width: 100%;
    overflow: auto;
    height: 381px;
    border-bottom: 1px solid #e9eee6;
    border-left: 1px solid #e9eee6;
    li{
      border-top:1px solid #e9eee6;
      overflow: hidden;
      line-height: 54px;
      cursor: pointer;
      &:first-child{
        border-top: none;
      }
    }
  }
}
.jieguo-top{
  width: 100%;
  background:#edf2f1;
  height: 34px;
  line-height: 34px;
  border: 1px solid #e9eee6;
  span{
    text-indent: 30px;
    display: block;
    float: left;
    width: 40%;
    &:nth-child(2){
      width: 60%;
      text-align: center;
    }
  }
  i{
    width: calc(~"(100% / 4)");
    display: block;
    text-align: center;
    float: left;
  }
}
.fl{
  float: left !important;
  width: 40%;
  text-indent: 30px;
}
.jieguo-r{
  width: 60%;
  text-align: center;
  float: right;
}
.jieguo-right{
  width:509px;
  float: right;
  height:410px;
  .jieguo-list{
    width: 100%;
    overflow: auto;
    height: 381px;
    border-bottom: 1px solid #e9eee6;
    border-left: 1px solid #e9eee6;
    border-right: 1px solid #e9eee6;
    li{
      width: 100%;
      line-height: 54px;
      border-top:1px solid #e9eee6;
      overflow: hidden;
      &:first-child{
        border-top: none;
      }
      i{
        width: calc(~"((100%-39px) / 4)");
        display: block;
        text-align: center;
        float: left;
        &:nth-child(4){
          border:1px solid #1abc9c;
          border-radius: 4px;
          height: 34px;
          line-height: 34px;
          margin: 10px 0 0 15px;
          width: 100px;
          padding: 0 10px;
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          em{
            color:#1abc9c;
          }
        }
      }
    }
  }
}
.pc-zhi{
  width:96%;
  border-top: 1px dashed #dfe3e9;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 0 auto;
  padding-top: 15px;
  i{
    margin-right: 10px;
    position: relative;
  }
  select{
    width: 182px;
    height: 34px;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
  }
  .pc-select{
    width: 280px;
    position: absolute;
    top: 36px;
    left: 0;
    display: none;
    z-index: 9999;
    height:260px;
    background: #fff;
    border: 1px solid #dfe3e9;
    box-shadow: 0 0 5px #dfe3e9;
    span{
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      text-align: center;
      color: #333333;
      font-size: 14px;
      height: 34px;
      border-bottom: 1px solid #dfe3e9;
      b{
        width: 40%;
        display: block;
        &:nth-child(2){
          width: 60%;
        }
      }
    }
    .pc-option{
      height: 210px;
      overflow: auto;
      width: 100%;
      background: #fff;
    }
    a{
      width: 100%;
      display:flex;
      justify-content: flex-start;
      align-items: center;
      text-align: center;
      line-height: 52px;
      border-top: 1px solid #dfe3e9;
      cursor: pointer;
      b{
        width: 40%;
        display: block;
        &:nth-child(2){
          width: 60%;
        }
      }
      &:hover{
        background:rgba(26,188,156,0.08);
        border:1px solid #1abc9c;
        line-height: 52px;
      }
      &:first-child{
        border-top: none;
      }
    }
  }
}

b{
  font-weight: normal;
}
.addline{
  line-height: 24px !important;
}


.pici{
  height:350px;
  width: 100%;
  padding-top: 20px;
}
.readonly{
  background:#f9f9f9 !important;
}
.color-gg{
  color:#ff6151 !important;
}
.color-blue{
  color:#2885e2;
}
.color-zy{
  color: #13a950;
}
.tab-gls{
  width: auto;
  padding: 0 10px;
  border: 1px solid #1abc9c;
  border-radius: 4px;
  font-style: normal;
  height: 36px;
  line-height: 36px;
  margin-top: 9px;
  display: flex;
  justify-content:center;
}
.bgcxlabel{
  padding: 0 15px;
  width: 100%;
  label{
    width: 50%;
    float: left;
    line-height:60px !important;
    position: relative;
  }
  span{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    line-height: 40px !important;
    overflow: hidden;
    float: left;
    i{
      width:25%;
      margin-right: 10px;
      display: block;
      float: left;
      &:nth-child(2){
        width:70%;
      }
    }
  }
}
.bgcxdiv{
  width: 100%;
  line-height: 20px;
  color:#f2a654;
  padding: 20px 0 10px 0;
  text-align: left;
}
.ztdb-top{
  width:100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background:#edf2f1;
  height: 34px;
  color:#333333;
  margin-top: 15px;
  font-size: 14px;
  i{
    width: 50%;
    display: block;
    float: left;
    text-align: center;
  }
}
.ztdb-list{
  height:85vh;
  overflow: auto;
  width: 100%;
  li{
    width: 100%;
    display: flex;
    justify-content: center;
    height: 52px;
    line-height: 52px;
    border-bottom: 1px solid #e9eee6;
    i{
     width: 50%;
      display: block;
      float: left;
      text-align: center;
    }
    :nth-child(2n){
      background:#fdfdfd;
    }
  }
}
.tab-gba{
  width: 280px;
  margin: 20px auto;
  background:#ffffff;
  border:1px solid #dfe3e9;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  line-height: 36px;
  font-size: 14px;
  a{
    width: 140px;
    height: 36px;
    text-align: center;
    display: block;
    float: left;
    cursor: pointer;
    &.active{
      background:#1abc9c;
      border-radius:4px 0 0 4px;
      color: #fff;
    }
  }
}
.tab-bg-box{
width: 100%;
  padding:0 10px;
  box-sizing: border-box;
}
.gb-box{
  position: relative;
}
.gb-top{
  width: 100%;
  background:#edf2f1;
  height: 34px;
  border: 1px solid #dfe3e9;
  line-height: 34px;
  color:#333333;
  display: flex;
  justify-content: center;
  align-items: center;
  i{
    width: calc(~"(100% / 4)");
    text-align: center;
    display: block;
  }
}
.gb-list{
  width: 100%;
  max-height:300px;
  overflow: auto;
  border-bottom: 1px solid #dfe3e9;
  li{
    width: 100%;
    display: flex;
    line-height: 52px;
    border: 1px solid #dfe3e9;
    border-top:none;
    cursor: pointer;
    i{
      width: calc(~"(100% / 4)");
      text-align: center;
    }
    &:hover{
      background:rgba(26,188,156,0.06);
      border:1px solid #1abc9c;
    }
  }
}
.gb-bottom{
 margin-top: 20px;
  i{
    width: calc(~"(100% / 3)");
    text-align: center;
    display: block;
  }
}
.bg-blist{
  li{
   i{
     width: calc(~"(100% /3)");
     text-align: center;
   }
  }
}
.jeguo{
  width: 10px;
  height: 100px;
  position: absolute;
  left:32px;
  top: 300px;
  color: #000;
}
.bgcx-list{
  li{
    width: calc(~"(100% / 7)");

  }
}
.pop-jiaofei-height{
  li{
    &:nth-child(2n){
      background:#fdfdfd;
    }
  }
}
.bgcx-money{
  font-size: 14px;
  width:100%;
  padding-top: 10px;
  text-indent: 30px;
  i{
    color:#1abc9c;
  }
}


.label-content:before{
  content: '岁';
  position: absolute;
  right:5px;
  top: 0;
  width: 20px;
  height: 36px;
  line-height: 36px;
  z-index: 999;
  color: #1abc9c;
  font-size: 14px;
}
.wh250{
  margin-right: 15px;
}
.tong-search{
  padding: 13px 0 5px 20px;
}