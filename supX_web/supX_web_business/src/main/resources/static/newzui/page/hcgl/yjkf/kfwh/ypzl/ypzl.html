<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>材料种类</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ypzl.css" rel="stylesheet"/>
</head>
<style>

</style>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5 icon-font15" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b"><i class=" icon-width icon-dc padd-l-25"></i>导出</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label ">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="search"/>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="zui-table-view padd-l-10 padd-r-10"  z-height="full">
        <div class="flex-container flex-one" key="g" >

        <div class="margin-r-10">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                 :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>种类编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>种类名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>拼音简码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>加成比例</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>加成方式</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料类型</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>对应收费项目</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    :tabindex="$index" @dblclick="edit($index)" @click="checkSelect([$index,'some']),clickOne($index)" >
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'one'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>

                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlbm"></div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" ><i v-text="item.ypzlmc" style="width: 84px;display: block;overflow: hidden;"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" ><i v-text="item.pyjm" style="width: 84px;display: block;overflow: hidden;"></i></div>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jcbl"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="jcfs_tran[item.jcfs]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="yplx_tran[item.yplx]"></div></td>
                    <td><div class="zui-table-cell cell-s" >
                        <div class=" relative title title-width" ><i v-text="item.dyfyxmmc" :data-title="item.dyfyxmmc" ></i></div>
                    </div></td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <div class="switch">
                                <input  type="checkbox" true-value="0" false-value="1" v-model="item.tybz" disabled/>
                                <label></label>
                            </div>
                        </div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell  cell-s">
                            <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>

                                <em class="width30"><i class="icon-width icon-kz" data-title="新增区间"
                                                       @click="addJcqj($index)"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        </div>
        <div class="margin-r-10">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s"><span>起始值(包含)</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>结束值(不包含)</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>类型</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>加成值</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>最高限价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)" >
                <table class="zui-table" v-if="jcqjList.length!=0">
                    <tbody>
                    <tr @click="switchIndex('activeIndex1',true,$index)"
                        :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                        @mouseenter="switchIndex('hoverIndex1',true,$index)"
                        @mouseleave="switchIndex()"
                        :tabindex="$index"
                        v-for="(item, $index) in jcqjList" @dblclick="editJcqj($index)">
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.qsz">起始值</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jsz">结束值</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="jclx_tran[item.jclx]">类型</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jcz">加成值</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zgxj">最高限价</div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell  cell-m">
                            <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-sc" data-title="作废" @click="delQj($index)"></i></em>
                            </span>
                            </div>
                        </td>

                    </tr>
                    </tbody>
                </table>
                <p v-if="jcqjList.length==0" class="  noData text-center zan-border">暂无数据...</p>
            </div>
        </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<div class="side-form ng-hide pop-width" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side" v-if="isYpzl">
            <span class="span0">
                <i>种类编码</i>
                <input type="text" class="zui-input border-r4" placeholder="自动生成" v-model="popContent.ypzlbm"
 @keydown="nextFocus($event)" disabled="disabled"/>
            </span>
        <span class="span0">
                <i>种类名称</i>
                <input type="text" class="zui-input border-r4" @blur="setPYDM(popContent.ypzlmc, 'popContent', 'pyjm')"  v-model="popContent.ypzlmc" @keydown="nextFocus($event)"/>
            </span>
        <span class="span0">
                <i>对应收费项目</i>
                <input id="ypmc" class="zui-input border-r4" @input="change($event,'text')"
                       @keydown.enter.up.down="changeDown($event,'text'),nextFocus($event)" v-model="popContent.dyfyxmmc"/>
                                <search-table :message="searchCon" :selected="selSearch" :current="dg.page"
                                              @click-one="checkedOneOut" @click-two="selectOne"
                                              :rows="dg.rows" :total="total" :them="them" :them_tran="them_tran">
                                </search-table>
            </span>
        <span class="span0">
                <i>加成方式</i>
            <select-input @change-data="resultChange" :child="jcfs_tran"
                          :index="popContent.jcfs" :val="popContent.jcfs" :search="true" :name="'popContent.jcfs'">
                                </select-input>
            </span>
        <span class="span0">
                <i>加成比例</i>
                <input  @keydown.up.prevent @keydown.down.prevent class="zui-input border-r4" type="number" v-model="popContent.jcbl"  @keydown="nextFocus($event)"/>
            </span>
        <span class="span0" v-if="false">
                <i>材料类型</i>
                <select-input @change-data="resultChange" :child="yplx_tran"
                              :index="popContent.yplx" :val="popContent.yplx" :search="true" :name="'popContent.yplx'">
                                </select-input>
            </span>
        <span class="span0">
                <i>拼音简码</i>
                <input  class="zui-input border-r4" type="text" v-model="popContent.pyjm"  @keydown="nextFocus($event)" disabled="disabled"/>
            </span>
        <span  class="margin-top-10 span0">
                <i style="float:left;">状态</i>
                <div class="switch">
                    <input type="checkbox" true-value="0" false-value="1" v-model="popContent.tybz" id="tybz"/>
                    <label for="tybz"></label>
                </div>
            </span>
    </div>
    <div class="ksys-side" v-if="isJcqj">
            <span class="span0">
                <i>种类编码</i>
                <input type="text" class="zui-input border-r4" placeholder="自动生成" v-model="popContent.ypzlbm"
                       @keydown="nextFocus($event)" disabled="disabled"/>
            </span>
        <span class="span0">
                <i>种类名称</i>
                <input type="text" class="zui-input border-r4"  v-model="popContent.ypzlmc" @keydown="nextFocus($event)" disabled="disabled"/>
            </span>
        <span class="span0">
                <i>起始值(包含)</i>
                <input  @keydown.up.prevent @keydown.down.prevent class="zui-input border-r4" type="number" v-model="popContent.qsz"  @keydown="nextFocus($event)"/>
            </span>
        <span class="span0">
                <i>结束值(不包含)</i>
                <input  @keydown.up.prevent @keydown.down.prevent class="zui-input border-r4" type="number" v-model="popContent.jsz"  @keydown="nextFocus($event)"/>
            </span>

        <span class="span0">
                <i>加成类型</i>
            <select-input @change-data="resultChange" :child="jclx_tran"
                          :index="popContent.jclx" :val="popContent.jclx" :search="true" :name="'popContent.jclx'">
                                </select-input>
            </span>
        <span class="span0">
                <i>加成值(加成50%请输1.50)</i>
                <input  @keydown.up.prevent @keydown.down.prevent class="zui-input border-r4" type="number" v-model="popContent.jcz"  @keydown="nextFocus($event)"/>
            </span>
        <span class="span0">
                <i>最高限价(0为不限定)</i>
                <input  @keydown.up.prevent @keydown.down.prevent class="zui-input border-r4" type="number" v-model="popContent.zgxj"  @keydown="nextFocus($event)"/>
            </span>
    </div>

    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ypzl.js"></script>
</body>

</html>
