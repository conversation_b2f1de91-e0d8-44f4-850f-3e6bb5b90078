<link href="/page/ybjk/gzwxhy/ybyw/sjsc/sjsc.css" rel="stylesheet" type="text/css">
<div class="gl_013">
    <div class="ksys-side">
        <ul class="tab-edit-list flex-start">
            <li>
                <i>个人编号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac001" disabled="disabled"/>
            </li>
            <li>
                <i>姓名</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac003" disabled="disabled"/>
            </li>
            <li>
                <i>性别</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac004" disabled="disabled"/>
            </li>
            <li>
                <i>身份证号码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac002" disabled="disabled"/>
            </li>
            <li>
                <i>出生日期</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac006" disabled="disabled"/>
            </li>
            <li>
                <i>异地标志</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ydbz" disabled="disabled"/>
            </li>
            <li>
                <i>卡号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aaz500" disabled="disabled"/>
            </li>
            <li>
                <i>门诊诊断信&emsp;&emsp;息</i>
                <input class="zui-input" v-model="jbContent.jbmc" @input="searching(false,'jbmc')"
                       @keyDown="changeDown($event,'text')">
                <search-table :message="searchCon" :selected="selSearch"
                              :them="them" :them_tran="them_tran" :page="page"
                              @click-one="checkedOneOut" @click-two="selectOne" :not_empty="true">
                </search-table>
            </li>
            <li class="zflb">
                <i style="color:red;font-weight: 700">支付类别</i>
                <select-input @change-data="resultChange" id="aka130"
                              :child="gzybzflb_tran" :index="zdxxJson.aka130" :val="zdxxJson.aka130"
                              :search="true" :name="'zdxxJson.aka130'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>个人账户余额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aae240" disabled="disabled"/>
            </li>
        </ul>
    </div>
    <div class="zui-row buttonbox">
        <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="load()">读卡</button>
        <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="enter()">引入</button>
    </div>
</div>
<script type="application/javascript" src="insurancePort/013glyhyb/013glyhyb.js"></script>