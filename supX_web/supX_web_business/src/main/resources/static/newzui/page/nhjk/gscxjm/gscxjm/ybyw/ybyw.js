
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    components: {
        'search-table': searchTable,
    },
    data: {
        bz:0,
        num: 0,
        textnum:1,
        childNum: 0,
        json: {
            kh: undefined
        },
        color:{
            '0':'',
            '1':'bg-red',
        },
        fbJson: {},
        bqcy: true,
        searchCon: [],
        selSearch: 0,
        popContent: {},
        cyztCon: {},
        pageSelect: {
            page: 1,
            rows: 20,
            total: null
        },
        ghKsList: [],
        brFbList: [],
        bxLbList: [],
        fylbList: [],
        bxlbbm: null,
        bxurl: null,
        zygrxxJson: {},
        jtcyxxJson: {},
        brList: [],
        brJson: [],
        zhlbList: [],
        cyztList: [],
        zybclbList: [],
        cyjson: {},
        zhjson: {},
        bcjson: {},
        search: '',
        djsearch: '',
        bnbw: {'1': '保内', '2': '保外'},
        sfywsh_tran: {'0': '不是', '1': '是'},
        sfyydf_tran: {'0': '不是', '1': '是'},
        lplx_tran: {'01': '自动理赔', '02': '人工理赔', '03': '人工理赔进行中'},
        ifztjs_tran: {'1': '是', '2': '否'},
        cyzt_tran: {'1': '治愈', '2': '好转', '3': '未愈', '4': '死亡', '9': '其他'},
        xb_tran:{'1':'男','2':'女'},
        ztjs: {
            sfztjs:'2'
        },
        them_tran: {},
        zfb_tran: {},
        them: {'疾病编码': 'jbbm', '疾病名称': 'zdmc'},
        bfthem: {'疾病编码': 'jbbm', '疾病名称': 'zdmc'},
        lyjson: {},
        jzjson: {},
        ryztList:[],
        jzlxList:[]

    },
    created: function () {
        // this.readyData({"ghks": "1"}, "ksbm", "ghKsList");
        // this.readyData(false, "brfb", "brFbList");
        // this.readyData(false, "bxlb", "bxLbList");
        this.getbxlb();
    },
    mounted: function () {

        laydate.render({
            elem: '#ryqq'
            , trigger: 'click'
            , theme: '#1ab394'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , done: function (value, data) {
            }
        });
        laydate.render({
            elem: '#csrq'
            , trigger: 'click'
            , theme: '#1ab394'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , done: function (value, data) {
            }
        });
        laydate.render({
            elem: '#ryqq'
            , trigger: 'click'
            , theme: '#1ab394'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , done: function (value, data) {
            }
        });
        laydate.render({
            elem: '#csrq'
            , trigger: 'click'
            , theme: '#1ab394'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , done: function (value, data) {
            }
        });
        laydate.render({
            elem: '#ryqq1'
            , trigger: 'click'
            , theme: '#1ab394'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , done: function (value, data) {
            }
        });
        laydate.render({
            elem: '#csrq1'
            , trigger: 'click'
            , theme: '#1ab394'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , done: function (value, data) {
            }
        });
        laydate.render({
            elem: '#ryrq1'
            , trigger: 'click'
            , theme: '#1ab394'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , done: function (value, data) {
            }
        });
    },
    updated: function () {
        changeWin();
    },
    computed: {
        a: function () {
            this.bcjson.bm = this.zygrxxJson.bclb
            this.zhjson.zhbcbm = this.zygrxxJson.bczhlb
            this.cyjson.cyzt = this.zygrxxJson.cyzt||'2'
            this.ztjs.sfztjs = this.zygrxxJson.sfztjs || '2'
                console.log(this.zygrxxJson.ryzt,"---",this.zygrxxJson.jzlx)
                this.lyjson.rybm=this.zygrxxJson.ryzt
                this.jzjson.jzbm=this.zygrxxJson.jzlx

        },
    },
    methods: {
        hqxx: function (val) {
                $.getJSON('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=getMessage&zyh=' + val, function (json) {
                    if (json.a == 0) {
                        //********费用信息处理
                        wrapper.zygrxxJson.money = json.d.fyxxModel.zfy.toFixed(2);
                    } else {
                        malert("获取信息失败：" + json.c, 'top', 'defeadted');
                        return;
                    }
                });
        },
        fycz:function(){
            if(wrapper.zygrxxJson.zfy=='0'){
                malert("请先预结算！")
                return;
            }
            var param={
                list:wrapper.zygrxxJson
            }
            var url="/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&&types=costjs&method=fycz&time="+new Date().getTime()
            wrapper.$http.post(url, JSON.stringify(param)).then(function (data) {
                console.log(">>>>>" + data.body.a);
                if (data.body.a == 0) {
                    malert("冲正成功！")
                } else {
                    malert(data.body.c, 'top', 'defeadted')
                }
            });
        },
        bcdPrint:function(){
            var reportlets ="[{reportlet: 'fpdy%2Fzygl%2Fbxbcd.cpt',zyh:'"+this.zygrxxJson.zyh+"',zydjlsh:'"+this.zygrxxJson.zydjlsh+"'}]";
            if (!FrPrint(reportlets,null)){
                window.print();
            }
        },
        checkedOneOut0: function (index) {
            console.log("------->", index);
            var item = wrapper.searchCon[index];
            console.log("------->item", item);
            if (index == null) {
                this.pageSelect.page++
                this.searching(true, this.zygrxxJson.zdmc);
            } else {
                console.log(arguments);
                this.zygrxxJson.jbdm = item.jbbm;
                this.zygrxxJson.jbmc = item.zdmc;
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },
        changeRydj:function(){
            var param = {
                brdqbm: wrapper.zygrxxJson.brdqbm,
                zydjlsh: wrapper.zygrxxJson.zydjlsh,
                zyh: wrapper.zygrxxJson.zyh,
                jbdm: wrapper.zygrxxJson.jbdm,
                jbmc: wrapper.zygrxxJson.jbmc,
                ryqk:this.lyjson.rybm,
                jzlx:this.jzjson.jzbm,
                ryks:wrapper.zygrxxJson.ryks,
                ryrq:wrapper.zygrxxJson.ryrq,
                sjhm:wrapper.zygrxxJson.sjhm
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=rydj&method=changeRydj&parm=" + encodeURI(JSON.stringify(param))+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    malert("修改入院登记成功！")
                    wrapper.getdjbr();
                } else {
                    malert("修改入院登记失败！", 'top', 'defeadted')
                }
            });
        },
        comfirmJs: function () {
            if(this.zygrxxJson.bqcybz==0){
                malert("该病人未出院不能进行预结算！", 'top', 'defeadted')
                return;
            }
            if(parseFloat(this.zygrxxJson.money)!=parseFloat(this.zygrxxJson.zfy)){
                malert("预结算总费用与his总费用不符合，请冲正或者核实费用！", 'top', 'defeadted')
                return;
            }

            var param={
                list:wrapper.zygrxxJson
            }
            common.openloading(".tab-card");
            var url="/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&&types=costjs&method=zsjs&time="+new Date().getTime()
            wrapper.$http.post(url, JSON.stringify(param)).then(function (data) {
                console.log(">>>>>" + data.body.a);
                if (data.body.a == 0) {
                    malert("结算成功！")
                    wrapper.getdjbr();
                    common.closeLoading()
                } else {
                    malert(data.body.c, 'top', 'defeadted')
                    common.closeLoading()
                }
            });


        },
        cancenlJs: function () {
            var param = {
                brdqbm: this.zygrxxJson.brdqbm,
                zydjlsh: this.zygrxxJson.zydjlsh,
                zyh: this.zygrxxJson.zyh,
            }
            common.openloading(".tab-card");
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=costjs&method=qxjs&parm=" + encodeURI(JSON.stringify(param))+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    malert("取消结算成功！")
                    wrapper.zygrxxJson={}
                    wrapper.getdjbr();
                    common.closeLoading()
                } else {
                    malert(json.c, 'top', 'defeadted')
                    common.closeLoading()
                }
            });

        },
        checkedOneOut: function (index) {
            console.log("------->", index);
            var item = wrapper.searchCon[index];
            console.log("------->item", item);
            if (index == null) {
                this.pageSelect.page++
                this.searching(true, this.zygrxxJson.zdmc);
            } else {
                console.log(arguments);
                this.zygrxxJson.cyzd = item.jbbm;
                this.zygrxxJson.cyzdmc = item.zdmc;
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },

        checkedOneOut1: function (index) {
            console.log("------->", index);
            var item = wrapper.searchCon[index];
            console.log("------->item", item);
            if (index == null) {
                this.pageSelect.page++
                this.searching(true, this.zygrxxJson.zdmc);
            } else {
                console.log(arguments);
                this.zygrxxJson.bfz = item.jbbm;
                this.zygrxxJson.bfzmc = item.zdmc;
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },
        searching: function (add, val) {
            if (val == '') {
                return;
            }
            var param = {
                page: this.pageSelect.page,
                rows: 10,
                sort: "yljgbm",
                order: "asc",
                parm: val,
            }
            if (!add) this.pageSelect.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var dim = [];
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=basic&method=ypQuery&parm=" + encodeURI(JSON.stringify(param))+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    console.log("--->toltal", JSON.parse(json.d).total)
                    wrapper.pageSelect.total = JSON.parse(json.d).total;
                    console.log("--->sum", wrapper.page.total)
                    wrapper.selSearch = 0;
                    if (add) {
                        for (var i = 0; i < JSON.parse(json.d).list.length; i++) {
                            wrapper.searchCon.push(JSON.parse(json.d).list[i]);
                        }
                    } else {
                        wrapper.searchCon = JSON.parse(json.d).list;
                    }
                    if (JSON.parse(json.d).list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                } else {
                    malert(json.c)
                }
            });
        },
        changeDown: function (event, index) {
            if (this.searchCon[this.selSearch] == undefined) return;
            this.inputUpDown(event, this.searchCon, 'selSearch')
            this.popContent = this.searchCon[this.selSearch]
            if (event.keyCode == 13) {
                console.log(this.popContent)
                if (index == 1) {
                    this.zygrxxJson.cyzdmc = this.popContent.zdmc
                    this.zygrxxJson.cyzd = this.popContent.jbbm
                } else if (index == 2) {
                    this.zygrxxJson.bfzmc = this.popContent.zdmc
                    this.zygrxxJson.bfz = this.popContent.jbbm
                }
                this.$forceUpdate()
                this.nextFocus(event)
                $(".selectGroup").hide();
            }
        },
        doCheck: function (val) {
            this.zygrxxJson[val] = this.zygrxxJson[val] == 0 ? 1 : 0
            this.$forceUpdate()
        }
        ,
        resultChangeData: function (val) {
            console.log("----val", val);
            this.zygrxxJson.bxlbmc = wrapper.brFbList[val[5]].bxlbmc
            this.zygrxxJson.brfb = val[0];
            this.zygrxxJson.brfbmc = val[4]
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            console.log("----zygrxxJson", this.zygrxxJson);
            this.$forceUpdate()
        },
        dim: function (item) {
            this.hqxx(item.zyh);
            wrapper.zygrxxJson = {};
            wrapper.zygrxxJson = item;
            this.search = item.zyh
            var val = [item.brfb, undefined, ["fbJson", "fbbm"], "fbmc", item.brfbmc, 1]
            this.resultChangeData(val)
            if (this.num == 0) {
                this.textnum=0
                wrapper.readyData("ryzt", "ryztList");
                wrapper.readyData("jzlx", "jzlxList");
            } else if (this.num == 1 && wrapper.zygrxxJson != null) {
                this.fymx();
            } else if (this.num == 2 && wrapper.zygrxxJson != null) {
                wrapper.readyData("bczhlb", "zhlbList");
                wrapper.readyData("zybclb", "zybclbList");
            }
        },
        zyhquery: function () {

        },
        sfrz: function () {
            if(this.zygrxxJson.brxm==null){
                malert("请先搜索病人!", 'top', 'defeadted');
                return;
            }
            this.loadName = 'insurancePort/009gsnhjk/009gsnhjk';
            popTable.isShow = true;
            loadPage(this.loadName);
        },
        getbxlb: function () {
            var param = {bxjk: "009"};
            common.openloading();
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            wrapper.bxlbbm = json.d.list[0].bxlbbm;
                            wrapper.bxurl = json.d.list[0].url;

                        }
                        wrapper.fb();
                        common.closeLoading();

                    } else {
                        malert("保险类别查询失败!" + json.c, 'top', 'defeadted');
                        common.closeLoading();
                    }
                });
        },
        addData: function () {
        },
        cancelDj: function () {
            var param = {
                zydjlsh:this.zygrxxJson.zydjlsh,
                brdqbm:this.zygrxxJson.brdqbm,
                zyh:this.zygrxxJson.zyh,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=rydj&method=cancelZybRydj&parm=" + encodeURI(JSON.stringify(param))+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    wrapper.zygrxxJson = {}
                    wrapper.brList=[]
                    wrapper.textnum=1
                    wrapper.djsearch=''
                    wrapper.search=''
                    malert("取消登记成功！")
                } else {
                    malert("取消登记异常！", 'top', 'defeadted')
                }
            });
        },
        rydjData: function () {
            console.log("----->zygrxxJson", wrapper.zygrxxJson)
            console.log("----->jtcyxxJson", wrapper.jtcyxxJson)
            var cyxh = wrapper.jtcyxxJson.cyxh;
            if (cyxh == '' || cyxh == undefined) {
                malert("请先进行身份认证！", 'top', 'defeadted')
                return
            }
            if (wrapper.zygrxxJson.brxm != wrapper.jtcyxxJson.brxm.trim()) {
                malert("请选择正确的病人成员！")
                return
            }
            this.zygrxxJson.ybkh = this.jtcyxxJson.ybkh
            this.zygrxxJson.brdqbm = this.jtcyxxJson.brdqbm
            this.zygrxxJson.cyxh = this.jtcyxxJson.cyxh
            this.zygrxxJson.jbdm = this.jtcyxxJson.jbdm
            this.zygrxxJson.jzlx = this.jtcyxxJson.jzlx
            this.zygrxxJson.ryqk = this.jtcyxxJson.ryqk
            this.zygrxxJson.sfzjhm = this.jtcyxxJson.sfzjhm
            this.zygrxxJson.jbmc = this.jtcyxxJson.zdmc
            this.zygrxxJson.jtdz = this.jtcyxxJson.jtdz
            this.zygrxxJson.bclb = this.jtcyxxJson.bclb
            this.rydj()
        },
        tabBg: function (index) {
            this.num = index;
            this.zygrxxJson = {};
            this.search = '';
            this.fylbList = [];
            // this.ztjs.sfztjs='2';
        },
        childTabBg: function (index) {
            this.childNum = index
            if (wrapper.zygrxxJson.zyh != null) {
                this.fymx();
            }
        },
        close: function () {
        },
        save: function () {
        },

        searchHc: function () {
            if (event.keyCode == 13) {
                if(this.djsearch.length<7&&this.djsearch.length>0){
                    var oDate = new Date();
                    wrapper.djsearch = oDate.getFullYear()+(Array(7-this.djsearch.length).join(0) +   this.djsearch);
                }
                this.getdjbr();
            }
        },


        sschangeDown: function (event) {
            console.log(event, event.keyCode);
            if (event.keyCode == 13) {
                if (this.num == 0) {
                    if (this.search == '') {
                        malert("请输入住院号！", 'top', 'defeadted')
                        return;
                    }
                    if(this.search.length<7&&this.search.length>0){
                        var oDate = new Date();
                        wrapper.search = oDate.getFullYear()+(Array(7-this.search.length).join(0) +   this.search);
                    }
                    this.getData();
                }
            }
        },
        getData: function () {
            var param = {
                parm: this.search,
            };

            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=rydj&method=qetbrxx&parm=" + encodeURI(JSON.stringify(param))+"&time="+new Date().getTime(), function (json) {

                    if (json.a == "0") {
                        console.log(">>>>>", JSON.parse(json.d));
                        if (JSON.parse(json.d) == null) {
                            wrapper.zygrxxJson = {}
                            wrapper.brFbList = []
                        } else {
                            wrapper.zygrxxJson = JSON.parse(json.d);
                            console.log("--->brfb", wrapper.zygrxxJson.brfb, "---->", wrapper.zygrxxJson.brfbmc);
                            var val = [wrapper.zygrxxJson.brfb, undefined, ["fbJson", "fbbm"], "fbmc", wrapper.zygrxxJson.brfbmc, 1]
                            wrapper.resultChangeData(val)
                        }

                    } else {
                        malert("数据查询异常！", 'top', 'defeadted')
                    }
                }
            );
        },

        getdjbr: function () {
            var param = {
                parm: this.djsearch,
            };

            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=rydj&method=querRydj&parm=" + JSON.stringify(param)+"&time="+new Date().getTime(), function (json) {
                    if (json.a == "0") {
                        console.log(">>>>>", JSON.parse(json.d));
                        wrapper.brList = JSON.parse(json.d).list
                    } else {
                        malert("数据查询异常！", 'top', 'defeadted')
                    }
                }
            );
        },
        fymx: function () {
            var ifsc = '';
            if (this.childNum != 0) {
                ifsc = '1';
            }
            var param = {
                zyh: wrapper.zygrxxJson.zyh,
                ifsc: ifsc,
            };
            common.openloading(".zui-table-body");
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=CostUpload&method=queryCost&parm=" +JSON.stringify(param)+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    wrapper.fylbList = JSON.parse(json.d).list;
                    common.closeLoading();
                } else {
                    malert("查询费用明细异常！", 'top', 'defeadted')
                    common.closeLoading();
                }
            });


        },
        uploadFy: function () {
            var param = {
                ifsc: '',
                zyh: wrapper.zygrxxJson.zyh
            }
            common.openloading(".zui-table-body");
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=CostUpload&method=costUpload&parm=" +JSON.stringify(param)+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    malert("上传成功！")
                    wrapper.fylbList=[]
                    wrapper.childNum=1;
                    wrapper.fymx();
                    common.closeLoading();
                } else {
                    malert(json.c, 'top', 'defeadted')
                    common.closeLoading();
                }
            });
        },
        cancelUpload: function () {
            common.openloading(".zui-table-body");
            console.log("---》zydjlsh", wrapper.zygrxxJson.zydjlsh)
            var param = {
                brdqbm: wrapper.zygrxxJson.brdqbm,
                zydjlsh: wrapper.zygrxxJson.zydjlsh,
                zyh: wrapper.zygrxxJson.zyh,
            };
            common.openloading(".zui-table-body");
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=CostUpload&method=cancelUpload&parm=" + JSON.stringify(param)+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    malert("取消上传成功！")
                    wrapper.fylbList=[]
                    wrapper.fymx();
                    common.closeLoading();
                } else {
                    malert("取消上传异常！", 'top', 'defeadted')
                    common.closeLoading();
                }
            });
        },

        firstJs: function () {
            console.log("--sfztjs", wrapper.ztjs.sfztjs, "------>")
            if(this.zygrxxJson.bqcybz==0){
                malert("该病人未出院不能进行预结算！", 'top', 'defeadted')
                return;
            }
            console.log(this.cyjson.cyzt)
            console.log(this.bcjson.bm)
            console.log(this.zhjson.zhbcbm)
            console.log(this.zygrxxJson.cyzdmc)
            console.log(this.zygrxxJson.cyzd)
            console.log(this.zygrxxJson.bfz)
            console.log(this.zygrxxJson.bfzmc)
            this.zygrxxJson.cyzt = this.cyjson.cyzt;
            this.zygrxxJson.bclb = this.bcjson.bm;
            this.zygrxxJson.bczhlb = this.zhjson.zhbcbm;
            this.zygrxxJson.sfztjs = this.ztjs.sfztjs;
            this.fstJs();
        },


        fstJs: function () {
            // param={
            //     zydjlsh:this.zygrxxJson.zydjlsh,
            //     brdqbm:this.zygrxxJson.brdqbm,
            //     cyxh:this.zygrxxJson.cyxh,
            //     jbdm:this.zygrxxJson.jbdm,
            //     ryrq:this.zygrxxJson.ryrq,
            //     bclb:this.zygrxxJson.bclb,
            //     nl:this.zygrxxJson.nl,
            //     jzlx:this.zygrxxJson.jzlx,
            //     ryks:this.zygrxxJson.ryks,
            //     zyh:this.zygrxxJson.zyh,
            //     sfztjs:this.zygrxxJson.sfztjs,
            //     ybkh:this.zygrxxJson.ybkh,
            //     cyzd:this.zygrxxJson.cyzd,
            // }.
            var param={
                list:wrapper.zygrxxJson
            }
            common.openloading(".tab-card");
            var url="/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=costjs&method=firstjs&time="+new Date().getTime()
            wrapper.$http.post(url, JSON.stringify(param)).then(function (data) {
                console.log(">>>>>" + data.body.a);
                if (data.body.a == 0) {
                    wrapper.zygrxxJson = JSON.parse(data.body.d);
                    malert("预结算成功！")
                    common.closeLoading();
                } else {
                    malert(data.body.c, 'top', 'defeadted')
                    common.closeLoading();
                }
            });


            // $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=costjs&method=firstjs&parm=" + JSON.stringify(wrapper.zygrxxJson)+"&time="+new Date().getTime(), function (json) {
            //     console.log(">>>>>" + json.a);
            //     if (json.a == "0") {
            //         wrapper.zygrxxJson = JSON.parse(json.d);
            //         malert("预结算成功！")
            //     } else {
            //         malert("预结算异常！", 'top', 'defeadted')
            //     }
            // });
        },
        rydj: function () {
            var param = {
                brdqbm: this.zygrxxJson.brdqbm,
                ybkh: this.zygrxxJson.ybkh,
                cyxh: this.zygrxxJson.cyxh,
                brxm: this.zygrxxJson.brxm,
                brxb: this.zygrxxJson.brxb,
                sfzjhm: this.zygrxxJson.sfzjhm,
                nl: this.zygrxxJson.nl,
                jbdm: this.zygrxxJson.jbdm,
                zyh: this.zygrxxJson.zyh.trim(),
                jzlx: this.zygrxxJson.jzlx.trim(),
                ryrq: this.zygrxxJson.ryrq,
                ryqk: this.zygrxxJson.ryqk.trim(),
                ryks: this.zygrxxJson.ryks,
                sjhm: this.zygrxxJson.sjhm,
                bxbr: this.zygrxxJson.bxbr,
                brfb: this.zygrxxJson.brfb,
                brfbmc: this.zygrxxJson.brfbmc,
                bclb: this.zygrxxJson.bclb,
                jtdz: this.zygrxxJson.jtdz,
                jbmc: this.zygrxxJson.jbmc

            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=rydj&method=zybRydj&parm=" + encodeURIComponent(JSON.stringify(param))+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    wrapper.zygrxxJson = {}
                    wrapper.search = ''
                    wrapper.jtcyxxJson={}
                    wrapper.getdjbr();
                    malert("入院登记成功！")
                } else {
                    malert(json.c, 'top', 'defeadted')
                }
            });


        },
        fb: function () {
            console.log("----1111>", this.bxurl)
            var param = {};
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=config&method=fb&parm=" + JSON.stringify(param)+"&time="+new Date().getTime(), function (json) {
                if (json.a == "0") {
                    console.log(">>>>>", JSON.parse(json.d).list);
                    wrapper.brFbList = JSON.parse(json.d).list
                } else {
                    malert("获取费别异常！", 'top', 'defeadted')
                }
            });


        },
        readyData: function (types, listName) {
            // console.log(this, yndryb_009_inta);
            var param = {
                brdqbm: this.zygrxxJson.brdqbm,
                bxlbbm: this.bxlbbm
            };

            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + wrapper.bxurl + "&bxlbbm=" + wrapper.bxlbbm + "&types=config&method=" + types + "&parm=" + encodeURI(JSON.stringify(param))+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    wrapper[listName] = JSON.parse(json.d);
                    console.log(">>>>>", listName, wrapper[listName]);
                } else {
                    malert(json.c)
                }
            });
        },
    },
})
var popTable = new Vue({
    el: '#popCenter',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        isShow: false,
        jsonList: []
    },
    methods: {
        success: function () {

        }
    }
});

function loadPage(name) {
    $('#loadPage').load(name + '.html');
}
