<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>待会诊管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link type="text/css" href="blsjbg.css" rel="stylesheet"/>
</head>

<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="background-box">
    <div class="wrapper" id="jyxm_icon" >
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary" @click="AddModel('',0)"><i class="iconfont icon-iocn42 icon-cf"></i>不良事件上报</button>
                <button class="tong-btn btn-parmary-b" @click="getData"><i class="iconfont icon-iocn56 icon-c1"></i>刷新</button>
            </div>
            <div class="tong-search">
                <div class="top-form">
                    <label class="top-label">状态</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChange"
                                          :child="bxzt_tran" :index="'zt'" :val="zt"
                                          :name="'zt'" >
                            </select-input>
                        </div>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">科室</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChange"
                                          :child="bxzt_tran" :index="'zt'" :val="zt"
                                          :name="'zt'" >
                            </select-input>
                        </div>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">时间段</label>
                    <div class="top-zinle">
                        <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                        <input type="text" class="zui-input wh122 times text-indent-20" v-model="dg.beginrq"/>
                    </div>
                    <div class="top-zinle padd-r-5 padd-l-5">
                        至
                    </div>
                    <div class="top-zinle">
                        <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                        <input type="text" class="zui-input wh122 times1 text-indent-20" v-model="dg.endrq"/>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">检索</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <input class="zui-input todate wh182 text-indent-10" placeholder="请输入关键字" id="timeVal" v-model="dg.parm"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view" v-cloak  >
            <div class="zui-table-header">
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>时间段</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>事件类型</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>发生科室</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>发生时间</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>事件名称</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>患者姓名</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>年龄</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>性别</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>身份证号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body "   @scroll="scrollTable($event)">
                <table class="zui-table zui-collapse">
                    <tbody>
                    <tr v-for="(item,$index) in 30" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td>
                            <div class="zui-table-cell cell-m">
                                <span v-text="$index+1"></span></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                上午
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">事件类型</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">发生部分</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                发生部分
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">年龄</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <i class="text-line color-dsh" @click="Patient">患者姓名</i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">身份证号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">受邀科室</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">受邀医生</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s color-cff5">
                                已上报
                            </div>
                        </td>

                    </tr>
                    </tbody>
                </table>
                <!--暂无数据提示,绑数据放开-->
                <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

    </div>
</div>


<script src="blsjbg.js"></script>
</body>

</html>
