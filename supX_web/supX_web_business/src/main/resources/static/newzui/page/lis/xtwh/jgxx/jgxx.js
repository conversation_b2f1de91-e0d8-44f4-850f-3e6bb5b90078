(function () {
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:'',
            param: {
                page: 1,
                rows: 20,
                total: 0,
                mc:''
            }
            
            
        },
        methods:{
          
            //addJg
            addJg:function () {
            	wapse.isXzShow=true;
            	wapse.isBjShow=false;
                wapse.isFold = true;
                wapse.sideTitle='新增结果选项';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
            },
            queryAll:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJgxx&types=selectJg&param="+JSON.stringify(this.param) , function (json) {
                    if (json.a == 0) {
                    	waps.totlePage =Math.ceil(json.d.total / wrapper.param.rows);
                    	waps.jgxxList=json.d.list;
                    } else {
                        malert("查询失败" + json.c);
                        return;
                    }
                });
            },
            bc: function(){
            	var json = '{"list":' + JSON.stringify(waps.jgxxList) + '}';
           	 	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJgxx&types=updateBatch',json).then(
                  		function(json) {
                  			console.log(json);
                  			if(json.body.a==0){
                  				malert('保存成功','top','success');
                  				wrapper.queryAll();
                	        }
                          }, 
                          function(error) {
                          	malert(error,'top','success');
                          });
            }
        },
        watch:{
        	'param.mc':function(){
        		this.queryAll();
        	}
        }
    });
  
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                this.isShowpopL=false;
                this.isShow=false;
                malert('删除成功','top','success');
            }

        }
    });
    var waps=new Vue({
        el:'.zui-table-view',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isFold:false,
            title:'',
            sideTitle:'',
            centent:'',
            jgxxList:[],
            totlePage:0,
            param:{
            	rows:10
            }
            
            
        },
        methods:{
            DelLine:function () {
                pop.isShow=true;
                pop.isShowpopL=true;
                pop.title='系统提示';
                pop.centent='确定删除该项内容吗？'
            },
            //双击编辑
            toDetail:function (data) {
            	wapse.isXzShow=false;
                wapse.isBjShow=true;
                wapse.isFold = true;
                wapse.sideTitle='编辑结果选项';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                wapse.pd.bm=data.bm;
                wapse.pd.mc=data.mc;
                wapse.pd.fw=data.fw;
                wapse.pd.tybz=data.tybz;
            },
            
            getData:function(){
            	wrapper.param.rows=this.param.rows;
        		wrapper.queryAll();
        	},
        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            
            isXzShow:false,
            isBjShow:false,
            
            title:'',
            sideTitle:'',
            centent:'',
            isFold: false,
            checked:'0',
            
            pd:{
            	bm:'',
            	mc:'',
            	fw:'',
            	tybz:''
            }	
            
        },
        methods:{
            // 取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // 确定
            confirms:function () {
                this.isXzShow=true;
                this.isBjShow=false;
            	this.pd.tybz=this.checked;
                this.$http.post('/actionDispatcher.do?reqUrl=XtwhJgxx&types=saveJg',JSON.stringify(this.pd)).then(
                  		function(json) {
                  			if(json.body.a=='0'){
                  				malert('保存成功','top','success');
                  				wapse.pd.bm='';
                   				wapse.pd.mc='';
                   				wapse.pd.fw='';
                  				// $(".side-form-bg").removeClass('side-form-bg')
                                // $(".side-form").addClass('ng-hide');
                  				wrapper.queryAll();
                	        }
                          }, 
                          function(error) {
                          	malert(error,'top','success');
                          });
                
            },
            qh:function(){
            	if(this.checked=='0'){
            		this.checked='1';
            	}else{
            		this.checked='0';
            	}
            },
            changeBj:function(){
        		if(wapse.pd.tybz=='0'){
        			wapse.pd.tybz='1'
        		}else{
        			wapse.pd.tybz='0'
        		}
        		
        	},
        	bj:function(){
                this.isXzShow=false;
                this.isBjShow=true;
        		console.log(wapse.pd);
        		 var a=[wapse.pd];
                 
                var json = '{"list":' + JSON.stringify(a) + '}';
            	 	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJgxx&types=updateBatch',json).then(
                   		function(json) {
                   			console.log(json);
                   			if(json.body.a==0){
                   				malert('保存成功','top','success');
                   				wrapper.queryAll();
                                $(".side-form-bg").removeClass('side-form-bg')
                                $(".side-form").addClass('ng-hide');
                 	        }else{
                 	        	malert('保存失败','top','defeadted');
                 	        }
                           }, 
                           function(error) {
                           	malert(error,'top','success');
                           });
        	}

        }
    });
    
    
    //初始化方法
    wrapper.queryAll();
    
})()