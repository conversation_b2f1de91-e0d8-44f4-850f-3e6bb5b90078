<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>手术完成</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../../newcss/main.css" rel="stylesheet">
    <link href="sswc.css" rel="stylesheet">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10 background-f">
<div class="header-item" style="height:100%">
    <header class="userNameBg printHide" v-cloak>
        <div class="flex-container flex-align-c">
            <div class="text-color">
                <p class="userHeader userCwh padd-l-30">
                    <span class="userName">病人姓名：{{Brxx_List.brxm}}</span>
                    <span class="sex text">住院号：{{Brxx_List.zyh}}</span>
                    <span class="sex text">性别：{{brxb_tran[Brxx_List.brxb]}}</span>
                    <span class="sex text">年龄：{{Brxx_List.nl}}{{nldw_tran[Brxx_List.nldw]}}</span>
                    <span class="sex text">科室床位：{{Brxx_List.rycwbh}}</span>
                    <span class="sex text">住院医生：{{Brxx_List.ysxm}}</span>
                </p>
            </div>
        </div>
    </header>
    <div class="content  over-auto padd-b-20" style="height: calc(100% - 76px)">
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手术科室</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.ssksmc" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手术分类</span>
                <div class="zui-input-inline wh180">
                    <select-input class="wh180" @change-data="resultChange" :data-notEmpty="true" :child="ssldj_tran"
                                  :index="pageState.ssfl" :val="pageState.ssfl" :name="'pageState.ssfl'"
                                  :search="true"></select-input>
                </div>
            </div>
            <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-l-20">
                <span class="padd-r-5">加班标志</span>
                <div class="zui-table-cell text-center">
                    <input class="green" :true-value="1" :false-value="0" v-model="pageState.jbbz" type="checkbox">
                    <label @click="doCheck('jbbz')" @dblclick.stop></label>
                </div>
            </div>
            <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-l-20">
                <span class="padd-r-5">手术处理标志</span>
                <span class="padd-r-5">是</span>
                <div class="position padd-r-5">
                    <input type="radio" id="four" name="one" v-model="pageState.qrbz" value="1" class="zui-radio">
                    <label for="four" class="padd-r-5"></label>
                </div>
                <span class="padd-r-5">否</span>
                <div class="position padd-r-5">
                    <input type="radio" id="four1" name="one" v-model="pageState.qrbz" value="0" class="zui-radio">
                    <label for="four1" class="padd-r-5"></label>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手&emsp;&emsp;术<br/>开始日期</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" id="ksTime" :value="fDate(pageState.ssksrq,'datetime')" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手&emsp;&emsp;术<br/>结束日期</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input"  id="jsTime" :value="fDate(pageState.ssjsrq,'datetime')" type="text"/>
                </div>
            </div>
            <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-l-20">
                <span class="padd-r-5">手术过程</span>
                <span class="padd-r-5">顺利</span>
                <div class="position padd-r-5">
                    <input type="radio" id="four2" name="two" v-model="pageState.ssgc" value="1" class="zui-radio">
                    <label for="four2" class="padd-r-5"></label>
                </div>
                <span class="padd-r-5">不顺利</span>
                <div class="position padd-r-5">
                    <input type="radio" id="four3" name="two" v-model="pageState.ssgc" value="0" class="zui-radio">
                    <label for="four3" class="padd-r-5"></label>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手术切口</span>
<!--                <div class="zui-input-inline wh180">-->
<!--                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.qk"  type="text"/>-->
<!--                </div>-->
                <select-input :search="true" class="wh180" @change-data="resultChange"  :search="true"
                              :child="ssqklb_tran"
                              :index="pageState.qk"
                              :val="pageState.qk" :name="'pageState.qk'">
                </select-input>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手&emsp;&emsp;术<br/>登记日期</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" id="ssdjrq"  :value="fDate(pageState.djrq,'datetime')" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手&emsp;&emsp;术<br/>登&ensp;记&ensp;人</span>
                <div class="zui-input-inline wh180">
                    <select-input  @change-data="resultChange" :not_empty="true" :child="ysData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.czy" :name="'pageState.czy'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手术部位</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.ssbw" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">术后诊断</span>
                <input @keydown="changeDown($event,'1')" class="zui-input position" v-model="pageState.shzdmc" @input="searching(false,$event.target.value)">
                <search-table :message="con" :selected="selSearch"
                              :them="them" :them_tran="them_tran" :page="pageSelect"
                              @click-one="checkedOneOut"  @click-two="checkedOneOut">
                </search-table>

<!--                <div class="zui-input-inline wh180">-->
<!--                    <input class="zui-input" v-model="pageState.shzd" @keydown="nextFocus($event)"  type="text"/>-->
<!--                </div>-->
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">术&emsp;&emsp;后<br/>并&ensp;发&ensp;症</span>
                <input @keydown="changeDown($event,'2')" class="zui-input position" v-model="pageState.ssbfzmc" @input="searching(false,$event.target.value)">
                <search-table :message="con" :selected="selSearch"
                              :them="them" :them_tran="them_tran" :page="pageSelect"
                              @click-one="checkedOneOut1"  @click-two="checkedOneOut1">
                </search-table>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">术后手术等级</span>
                <div class="zui-input-inline wh180">
                    <select-input  @change-data="resultChange" :data-notEmpty="true" :child="ssldj_tran"
                                  :index="pageState.ssdjSh" :val="pageState.ssdjSh" :name="'pageState.ssdjSh'"
                                  :search="true"></select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">术后死亡</span>
                <div class="zui-table-cell text-center">
                    <input class="green" v-model="pageState.shsw" :true-value="1" :false-value="0" type="checkbox"><label @click="doCheck('shsw')" @dblclick.stop></label>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">主刀医师</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="ysData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.ssys" :name="'pageState.ssys'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手&emsp;&emsp;术<br/>上级医生</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="ysData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.ssysSjys" :name="'pageState.ssysSjys'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手术等级</span>
                <div class="zui-input-inline wh180">
                    <select-input  @change-data="resultChange" :data-notEmpty="true" :child="ssldj_tran"
                                  :index="pageState.ssdj" :val="pageState.ssdj" :name="'pageState.ssdj'"
                                  :search="true"></select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">手术类型</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :data-notEmpty="true" :child="ssllx_tran"
                                  :index="pageState.sslx" :val="pageState.sslx" :name="'pageState.sslx'"
                                  :search="true"></select-input>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">主术</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.zsbm" disabled type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">主术名称</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.zsmc" disabled type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">第一手术</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.ssbm1" disabled type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">第&emsp;&emsp;一<br/>手术名称</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.ssmc1" disabled type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">第二手术</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.ssbm2" disabled type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">第&emsp;&emsp;二<br/>手术名称</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.ssmc2" disabled type="text"/>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">第三手术</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.ssbm3" disabled type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">第&emsp;&emsp;三<br/>手术名称</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.ssmc3" disabled type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">第四手术</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.ssbm4" disabled type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">第&emsp;&emsp;四<br/>手术名称</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.ssmc4" disabled type="text"/>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">助&emsp;&nbsp;手1</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.zs1" :name="'pageState.zs1'"
                              :search="true" :phd="''">
                </select-input>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">助&emsp;&nbsp;手2</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.zs2" :name="'pageState.zs2'"
                              :search="true" :phd="''">
                </select-input>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">助&emsp;&ensp;手3</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.zs3" :name="'pageState.zs3'"
                              :search="true" :phd="''">
                </select-input>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">助&emsp;&ensp;手4</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.zs4" disabled  type="text"/>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">台上护士<br/>1</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="hsData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.tshs1" :name="'pageState.tshs1'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">台上护士<br/>2</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="hsData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.tshs2" :name="'pageState.tshs2'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">洗手护士</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="hsData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.xshs" :name="'pageState.xshs'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">巡回护士<br/>1</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="hsData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.xhhs1" :name="'pageState.xhhs1'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">巡回护士<br/>2</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="hsData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.xhhs2" :name="'pageState.xhhs2'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">器械师</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="hsData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.qxs" :name="'pageState.qxs'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">心肺复苏治疗</span>
                <div class="zui-table-cell text-center"><input class="green" v-model="pageState.xffszl"  type="checkbox"><label @click="doCheck('xffszl')" @dblclick.stop></label></div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">心肺复苏成功</span>
                <div class="zui-table-cell text-center"><input class="green" v-model="pageState.xffscg"  type="checkbox"><label @click="doCheck('xffscg')" @dblclick.stop></label></div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">进入麻醉复苏</span>
                <div class="zui-table-cell text-center"><input class="green" v-model="pageState.jrmzfs"  type="checkbox"><label @click="doCheck('jrmzfs')" @dblclick.stop></label></div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">离室时steward评分</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.stewardSh" type="number"/>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻&ensp;醉&ensp;非<br/>预期事件</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.mzfyqxgsj" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻&emsp;&emsp;醉<br/>并&ensp;发&ensp;症</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.mzbfz" type="text"/>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉处理标志</span>
                <div class="zui-table-cell text-center"><input class="green" v-model="pageState.mzclbz" disabled :true-value="1" :false-value="0"  type="checkbox">
                    <label @click="doCheck('mzclbz')"  @dblclick.stop></label></div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉方式</span>
                <select-input class="wh180" @change-data="resultChange" :child="zybmList" :index="'zymc'"
                              :index_val="'zybm'" :val="pageState.mzfs" :name="'pageState.mzfs'" :index_mc="'mzfsmc'" :search="true"
                              :phd="'麻醉方式'">
                </select-input>

            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉情况</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" v-model="pageState.mzqk" @keydown="nextFocus($event)" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉科室</span>
                <select-input :cs="true" @change-data="resultChangeOd" :not_empty="false"
                              :child="allKs" :index="'ksmc'" :index_val="'ksbm'" :val="pageState.mzks"
                              :name="'pageState.mzks'">
                </select-input>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉分级</span>
                <div class="zui-input-inline wh180">
                    <select-input @change-data="resultChange" disable :data-notEmpty="true" :child="mzfj_tran"
                                  :index="pageState.asajb" :val="pageState.asajb" :name="'pageState.asajb'"
                                  :search="true"></select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉助手</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.mzzs" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉指导</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)"  v-model="pageState.mzzd" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">术前steward评分</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.stewardSq"  type="text"/>
                </div>
            </div>
        </div>
        <div class="flex-container flex-wrap-w  padd-l-10 padd-r-10 ">
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉医生</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="ysData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.mzys" :name="'pageState.mzys'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">上&emsp;&emsp;级<br/>麻醉医生</span>
                <div class="zui-input-inline wh180">
                    <select-input disable @change-data="resultChange" :not_empty="true" :child="ysData"
                                  :index="'ryxm'" :index_val="'rybm'" :val="pageState.mzysSjys" :name="'pageState.mzysSjys'"
                                  :search="true" :phd="''">
                    </select-input>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻醉登记</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.mzdjczy" type="text"/>
                </div>
            </div>
            <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
                <span class="padd-r-5">麻&emsp;&emsp;醉<br/>登记日期</span>
                <div class="zui-input-inline wh180">
                    <input class="zui-input" id="mzdjrq" :value="pageState.mzdjrq |initDate2" type="text"/>
                </div>
            </div>
        </div>
        <div class="zui-table-tool padd-r-10 flex-jus-e flex-align-c zui-border-bottom flex-container font-14-654">
            <button class="root-btn btn-parmary"  :disabled="disable" @click="SaveSssq()">保存</button>
        </div>
    </div>
</div>
<script src="sswc.js" type="text/javascript"></script>
</body>
</html>