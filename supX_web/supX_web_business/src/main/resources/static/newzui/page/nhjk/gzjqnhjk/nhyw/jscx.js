
(function () {
    var wrapper = new Vue({
        el: '.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            title: '',
            param : {
                parm : '',
            },

        },
        methods: {
            //刷新
            sx: function () {
            },
            //删除
            del: function () {
            },
        }
    });

    var wzList = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data: {
            jsonList : [] ,
            totlePage : 0 ,
            //isCkecked : []
            param:{
                page: 1,
                rows: 10,
                parm :'',
            }
        },
        updated:function () {
            changeWin()
        },
        mounted(){
            changeWin()
        },
        methods: {
            getData: function () {

            },
        },


    });
})()
$(window).resize(function () {
    changHeight();
})
setTimeout(function () {
    changHeight();
}, 150)





