.tabs-link .tabs-link-list.tabs-link-active{
    color:#1abc9c
}
.ivu-tabs-ink-bar-active{
    background-color:#1abc9c
}
.bqcydj_model{
    width: auto;
    padding: 14px 15px 33px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.totalPrice {
    position: fixed;
    right: 300px;
    top: 95px;
}

.setredbackground {
    background-color: red!important;
}

.popshow{
    position: fixed;
    width: 100%;
    height: 100%;
    text-align: center;
    top: 0;
    left: 0;
    background-color: hsla(170, 100%, 4%, .5);
    -webkit-transition: opacity 0.3s ease;
    transition: opacity 0.3s ease;
    z-index: 111111;
}
.bg-fiexd{
    background-image: url(/newzui/pub/image/hzxx.png);
    position: fixed;
    top: 46%;
    right: 0;
    width: 34px;
    cursor: pointer;
    height: 128px;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
}
.wrapper .zui-table-body .bg-red{
    background: rgba(255, 0, 0, 0.5) !important;
}