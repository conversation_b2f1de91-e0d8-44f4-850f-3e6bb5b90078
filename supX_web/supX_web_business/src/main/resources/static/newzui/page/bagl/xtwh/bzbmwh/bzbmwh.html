<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>费用项目</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="bzbmwh.css" rel="stylesheet"/>

</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10" >
<div class="wrapper background-f" v-cloak id="wrapper">
    <div class="panel " >
        <div class="tong-top">
            <button class="tong-btn btn-parmary" @click="addData" v-if="num!=6"><span class="icon-xz1 padd-r-5"></span>新增</button>
            <button class="tong-btn btn-parmary-b" @click="goToPage(1)"><span class="icon-sx paddr-r5 icon-font14"></span>刷新</button>
            <button class="tong-btn btn-parmary-b" @click="del"><span class="icon-sc-header paddr-r5 icon-font15"></span>删除</button>
            <button class="tong-btn btn-parmary-b" @click="saveBaks" v-if="num==6"><span class="icon-baocun paddr-r5 icon-font15"></span>保存</button>
        </div>
        <div class="tong-search">
                <div class="flex-container flex-align-c ">
                    <span class="padd-r-5 ft-14">检索</span>
                    <div class="zui-input-inline width162" >
                        <input type="text" class="zui-input" v-model="param.parm" id="jbbmjsvalue" @keydown.enter="goToPage(1)"/>
                    </div>
                </div>
        </div>
    </div>
        <tabs :num="num" :tab-child="[{text:'疾病编码'},{text:'手术编码'},{text:'肿瘤形态学编码'},{text:'病理诊断编码'},{text:'中医症候编码'},{text:'病原学名称编码'},{text:'病案科室维护'}]" @tab-active="tabBg"></tabs>
        <div class="zui-table-view" >
            <div  v-if="num==0">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">

                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>疾病编码</span></div></th>
                            <th><div class="zui-table-cell cell-l text-left"><span>疾病名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>统计码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>疾病级别</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>五笔码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>传染病标志</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>附加编码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>产妇分娩标志</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>停用标志</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table" v-if="jsonList.length!=0">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)" @dblclick="edit($index)">
                            <td class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.jbmb"></div></td>
                            <td>
                                <div class="zui-table-cell cell-l text-left" v-text="item.jbmc">
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.pydm">
                                </div>
                            </td>
                            <td><div class="zui-table-cell cell-s" v-text="item.tjm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.jbjb"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.wbbm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="istrue_tran[item.crbbz]"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.fjbm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="istrue_tran[item.cffmbz]"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div></td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span  class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>
                                </em>
                               </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </div>
            </div>
            <!--疾病编码end-->
            <div  v-if="num==1">
                <div class="zui-table-header">
                    <table class="font-14 zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox  style="display: flex;justify-content: center;align-items: center;width: 50px;" @result="reCheckBox" :list="'jsonList'"
                                                                                                         :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>手术编码</span></div></th>
                            <th><div class="zui-table-cell cell-l text-left"><span>手术名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>统计码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>手术级别</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>五笔码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>医感检测标志</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>停用标志</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m"><div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.ssbm"></div></td>
                            <td>
                                <div class="zui-table-cell cell-l text-left " v-text="item.ssmc">
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.pydm">
                                </div>
                            </td>
                            <td><div class="zui-table-cell cell-s" v-text="item.tjm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="ssjb_tran[item.jb]"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.wbbm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.ygjcbz"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div></td>
                            <td class="cell-s"><div class="zui-table-cell cell-s">
                              <span  class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>
                                </em>
                               </span>
                            </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <!--手术编码end-->
            <div  v-if="num==2">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox   @result="reCheckBox" :list="'jsonList'"
                                                                                                          :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>形态学编码</span></div></th>
                            <th><div class="zui-table-cell cell-l text-left"><span>形态学名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>统计码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>级别</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>五笔码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>停用标志</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m"><div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.jbbm"></div></td>
                            <td><div class="zui-table-cell cell-l text-left" v-text="item.jbmc">
                            </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"  v-text="item.pydm">
                                </div>
                            </td>
                            <td><div class="zui-table-cell cell-s" v-text="item.tjm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.jb"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.wbbm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div></td>
                            <td class="cell-s"><div class="zui-table-cell cell-s">
						<span class="flex-center padd-t-5">
							<em class="width30">
							<i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>

							</em>
						</span>

                            </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--肿瘤形态学编码end-->
            <div  v-if="num==3">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                                         :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>诊断编码</span></div></th>
                            <th><div class="zui-table-cell cell-xl text-left"><span>诊断名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>统计码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>级别</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>五笔码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>停用标志</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m"><div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.zdbm"></div></td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left"  v-text="item.zdmc">
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.pydm">
                                </div>
                            </td>
                            <td><div class="zui-table-cell cell-s" v-text="item.tjm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.jb"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.wbbm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div></td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                    <span class="flex-center padd-t-5">
                                        <em class="width30">
                                            <i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>
                                        </em>
                                    </span>
                                 </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--病理诊断编码end-->
            <div  v-if="num==4">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox  @result="reCheckBox" :list="'jsonList'"
                                                                                                         :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>疾病编码</span></div></th>
                            <th><div class="zui-table-cell cell-l text-left"><span>疾病名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>统计码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>级别</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>五笔码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>类型</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>停用标志</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>

                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                            <td class="cell-m"><div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.jbbm"></div></td>
                            <td>
                                <div class="zui-table-cell cell-l text-left" v-text="item.jbmc">
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.pydm">
                                </div>
                            </td>
                            <td><div class="zui-table-cell cell-s" v-text="item.tjm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.jb"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.wbbm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="bazyjblx_tran[item.lx]"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div></td>
                            <td class="cell-s"><div class="zui-table-cell cell-s">
							<span class="flex-center padd-t-5">
								<em class="width30">
									<i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>
								</em>
							</span>

                            </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <!--中医症候编码end-->
            <div  v-if="num==5">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox  @result="reCheckBox" :list="'jsonList'"
                                                                                                         :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>病原学编码</span></div></th>
                            <th><div class="zui-table-cell cell-l text-left"><span>病原学名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>停用标志</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m"><div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.byxbm"></div></td>
                            <td><div class="zui-table-cell cell-l text-left" v-text="item.byxmc"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.pydm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div></td>
                            <td class="cell-s"><div class="zui-table-cell cell-s">
						<span class="flex-center padd-t-5">
							<em class="width30">
								<i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>
							</em>
						</span>
                            </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--病原学名称编码end-->
            <div  v-if="num==6">
                <!--<div class="baksbm-left">
                    <div class="zui-table-view " z-height="full" style="border: none;">
                        <div class="zui-table-header">
                            <table class="font-14 zui-table">
                                <thead>
                                <tr>
                                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>科室编码</span></div></th>
                                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>科室名称</span></div></th>
                                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event)">
                            <table class="zui-table ">
                                <tbody>
                                <tr v-for="(item, $index) in ksList" :tabindex="$index" class="tableTr2" @click="checkOne($index)" @dblclick="editThis($index)">
                                    <td><div class="zui-table-cell cell-s" v-text="item.ksbm"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.ksmc"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.pydm"></div></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>-->
                <!--<div class="baksbm-right">-->
                    <div class="zui-table-view " z-height="full" style="border: none;">
                        <div class="zui-table-header">
                            <table class="zui-table">
                                <thead>
                                <tr>
                                    <th><div class="zui-table-cell cell-m" ><span>
                            <input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>科室编码</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>科室名称</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>病案统计码</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>病案科室</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>编制床位</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>加床床位</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>产科标志</span></div></th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in baksList" :tabindex="$index" class="tableTr2" @click="checkOne($index)"
                                    :class="[{'yellowBack': isChecked[$index]},{'newAddBg': edit[$index]}]"
                                    @dblclick="editThis($index)">
                                    <td><div class="zui-table-cell cell-m" >
                                        <input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'" :type="'some'" :which="$index" :val="isChecked[$index]">
                                        </input-checkbox></div>
                                    </td>

                                    <td><div class="zui-table-cell cell-s"><span v-text="item.ksbm"></span></div></td>
                                    <td><div class="zui-table-cell cell-s"><span v-text="item.ksmc"></span></div></td>
                                    <td><div class="zui-table-cell cell-s">
                                        <span v-if="!edit[$index]" v-text="item.batjm"></span>
                                        <span  v-if="edit[$index]"><input v-model="item.batjm" class="zui-input height27" @keydown="nextFocus($event)"></span>
                                    </div>
                                    <td><div class="zui-table-cell cell-s">
                                        <span v-if="!edit[$index]" v-text="item.baksmc"></span>
                                        <span  v-if="edit[$index]"><input v-model="item.baksbm" class="zui-input height27" @keydown="nextFocus($event)"></span>
                                    </div>
                                    <td><div class="zui-table-cell cell-s">
                                        <span v-if="!edit[$index]" v-text="item.bzcw"></span>
                                        <span  v-if="edit[$index]"><input v-model="item.bzcw" class="zui-input height27" @keydown="nextFocus($event)"></span>
                                    </div>
                                    <td><div class="zui-table-cell cell-s">
                                        <span v-if="!edit[$index]" v-text="item.jccw"></span>
                                        <span  v-if="edit[$index]"><input v-model="item.jccw" class="zui-input height27" @keydown="nextFocus($event)"></span>
                                    </div>
                                    </td>
                                    <td><div class="zui-table-cell cell-s">
                                        <span v-if="!edit[$index]" v-text="istrue_tran[item.ckbz]"></span>
                                        <span  v-if="edit[$index]" class="baks-span">
                                <select-input  @change-data="resultChange" :not_empty="false"
                                               :child="istrue_tran" :index="item.ckbz" :val="item.ckbz"
                                               :name="'baksList.'+$index+'.ckbz'">
	            			</select-input>
                            </span>
                                    </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <!--</div>-->
            </div>
            <!--病案科室维护end-->
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

</div>
<!--侧边窗口-->
<div class="side-form  pop-width" :class="[{'ng-hide':index==1},{'pop-850':num==3}]"  v-cloak   id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side" v-if="num==0">
        <span class="span0">
            <i>疾病编码</i>
            <input class="zui-input border-r4" v-model="popContent.jbmb" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>疾病名称</i>
            <input class="zui-input border-r4" v-model="popContent.jbmc" :data-notEmpty="true" @keydown="nextFocus($event)"
                   @blur="setPYDM(popContent.jbmc,'popContent','pydm')">
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input class="zui-input border-r4 background-h" v-model="popContent.pydm" disabled="disabled">
        </span>
        <span class="span0">
            <i>统计码</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>疾病级别</i>
            <input class="zui-input border-r4" v-model="popContent.jbjb" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>五笔码</i>
            <input class="zui-input border-r4" v-model="popContent.wbbm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>附加编码</i>
            <input class="zui-input border-r4" v-model="popContent.fjbm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>传染病标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="istrue_tran" :index="popContent.crbbz" :val="popContent.crbbz"
                              :name="'popContent.crbbz'">
               </select-input>
        </span>
        <span class="span0">
            <i>产妇分娩标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="istrue_tran" :index="popContent.cffmbz" :val="popContent.cffmbz"
                              :name="'popContent.cffmbz'">
                </select-input>
        </span>
        <span class="span0">
            <i>停用标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
                              :name="'popContent.tybz'">
              </select-input>
            <input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
    </div>
    <div class="ksys-side" v-if="num==1">
        <span class="span0">
            <i>手术编码</i>
            <input class="zui-input border-r4" v-model="popContent.ssbm" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>手术名称</i>
            <input class="zui-input border-r4" v-model="popContent.ssmc" :data-notEmpty="true" @keydown="nextFocus($event)"
                   @blur="setPYDM(popContent.ssmc,'popContent','pydm')">
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input class="zui-input border-r4 background-h" v-model="popContent.pydm" disabled="disabled">
        </span>
        <span class="span0">
            <i>统计码</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>手术级别</i>
            <select-input @change-data="resultChange" :data-notEmpty="false"
                          :child="ssjb_tran" :index="popContent.jb" :val="popContent.jb"
                          :name="'popContent.jb'">
            </select-input>
        </span>
        <span class="span0">
            <i>五笔码</i>
            <input class="zui-input border-r4" v-model="popContent.wbbm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>院感监试标志</i>
            <select-input @change-data="resultChange" :data-notEmpty="false"
                          :child="istrue_tran" :index="popContent.ygjcbz" :val="popContent.ygjcbz"
                          :name="'popContent.ygjcbz'">
            </select-input>
        </span>
        <span class="span0">
            <i>停用标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
                              :name="'popContent.tybz'">
              </select-input>
            <input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
    </div>
    <div class="ksys-side" v-if="num==2">
        <span class="span0">
            <i>形态学编码</i>
            <input class="zui-input border-r4" v-model="popContent.jbbm" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>形态学名称</i>
            <input class="zui-input border-r4" v-model="popContent.jbmc" :data-notEmpty="true" @keydown="nextFocus($event)"
                   @blur="setPYDM(popContent.jbmc,'popContent','pydm')">
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input class="zui-input border-r4 background-h" v-model="popContent.pydm" disabled="disabled">
        </span>
        <span class="span0">
            <i>统计码</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>级别</i>
            <input class="zui-input border-r4" v-model="popContent.jb" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>五笔码</i>
            <input class="zui-input border-r4" v-model="popContent.wbbm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>停用标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
                              :name="'popContent.tybz'">
              </select-input>
			<input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
    </div>
    <div class="ksys-side" v-if="num==3">
        <span class="span0">
            <i>诊断编码</i>
            <input class="zui-input border-r4" v-model="popContent.zdbm" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>诊断名称</i>
            <input class="zui-input border-r4" v-model="popContent.zdmc" :data-notEmpty="true" @keydown="nextFocus($event)"
                   @blur="setPYDM(popContent.zdmc,'popContent','pydm')">
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input class="zui-input border-r4 background-h" v-model="popContent.pydm" disabled="disabled">
        </span>
        <span class="span0">
            <i>统计码</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>级别</i>
            <input class="zui-input border-r4" v-model="popContent.jb" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>五笔码</i>
            <input class="zui-input border-r4" v-model="popContent.wbbm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>停用标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
                              :name="'popContent.tybz'">
              </select-input>
			<input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
    </div>
    <div class="ksys-side" v-if="num==4">
        <span class="span0">
            <i>疾病编码</i>
            <input class="zui-input border-r4" v-model="popContent.jbbm" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>疾病名称</i>
            <input class="zui-input border-r4" v-model="popContent.jbmc" :data-notEmpty="true" @keydown="nextFocus($event)"
                   @blur="setPYDM(popContent.jbmc,'popContent','pydm')">
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input class="zui-input border-r4 background-h" v-model="popContent.pydm" disabled="disabled">
        </span>
        <span class="span0">
            <i>统计码</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>级别</i>
            <input class="zui-input border-r4" v-model="popContent.jb" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>五笔码</i>
            <input class="zui-input border-r4" v-model="popContent.wbbm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>类型</i>
            <select-input @change-data="resultChange" :data-notEmpty="false"
                          :child="bazyjblx_tran" :index="popContent.lx" :val="popContent.lx"
                          :name="'popContent.lx'">
            </select-input>
        </span>
        <span class="span0">
            <i>停用标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
                              :name="'popContent.tybz'">
              </select-input>
			<input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
    </div>
    <div class="ksys-side" v-if="num==5">
        <span class="span0">
            <i>病原学编码</i>
            <input class="zui-input border-r4" v-model="popContent.byxbm" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>病原学名称</i>
            <input class="zui-input border-r4" v-model="popContent.byxmc" :data-notEmpty="true" @keydown="nextFocus($event)"
                   @blur="setPYDM(popContent.byxmc,'popContent','pydm')">
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input class="zui-input border-r4 background-h" v-model="popContent.pydm" disabled="disabled">
        </span>
        <span class="span0">
            <i>停用标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
                              :name="'popContent.tybz'">
              </select-input>
			<input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
    </div>
    <div class="ksys-side" v-if="num==6"></div>
   <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<script src="bzbmwh.js"></script>
</body>
</html>
