	  $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:'',
            data:{
            	hostname:''
            }
            
        },
        methods:{
          bg:function (type) {
               pop.isShow=true
               pop.typeShow=true
          },
            //addJg
            addJg:function () {
            	wapse.isDetail=false;
            	wapse.isAdd=true;
                wapse.isFold = true;
                wapse.sideTitle='新增检验设备';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                wapse.jysbObj={};
            },
            queryAll:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJysb&types=selectJysb&param=" + JSON.stringify(this.data), function (json) {
                    if (json.a == 0) {
                    	waps.jysbList = json.d.list;
                    	waps.totlePage =Math.ceil(json.d.total / wrapper.param.rows);
                    	malert('查询成功','top','success');
                    } else {
                        malert("查询失败" + json.c,'top','defeadted');
                        return;
                    }
                });
            },
            getutil : function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=gltjutil&yq=", function (json) {
            		wapse.util=json.d;
                });
            },
            sx:function(){
            	this.queryAll();
            },
            deleteSb:function(){
            	
            	//判断是否选择
  		  		var yq=0;
  		  		for (var i = 0; i < this.isChecked.length; i++) {
                  if (this.isChecked[i] == true) {
                      yq++;
                  }
  		  		}
  		  		if(yq==0){
  		  			malert('请选择需要删除的设备','top','defeadted');
                	return;
  		  		}
            	
            	
            	 	var List = [];
 	                for (var i = 0; i < this.isChecked.length; i++) {
 	                    if (this.isChecked[i] == true) {
 	                        List.push(waps.jysbList[i]);
 	                    }
 	                }
 	               var json = '{"list":' + JSON.stringify(List) + '}';	
            	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJysb&types=deleteSb',json).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				//成功回调提示
                                malert('删除成功','top','success');
                                wrapper.queryAll();
                			}
                        }, 
                        function(error) {
                        	malert(error,'top','defeadted');
                        });
            }
        },
        watch:{
        	'data.hostname':function(){
        		this.queryAll();
        	}
        }
    });
  
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShow:false,
            title:'',
            typeShow:undefined,
            centent:'',
            jysbObj:{}
        },
        methods:{
            //确定删除
            delOk:function () {
            		var List=[];
            		List.push(this.jysbObj);
            	   var json = '{"list":' + JSON.stringify(List) + '}';	
               	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJysb&types=deleteSb',json).then(
                   		function(data) {
                   			console.log(data);
                   			if(data.body.a==0){
                   				//成功回调提示
                   			 this.isShow=false;
                             malert('删除成功','top','success');
                            wrapper.queryAll();
                   			}
                           }, 
                           function(error) {
                           	malert(error,'top','defeadted');
                           });
            },
            clsoe:function () {
                this.isShow=false;
                this.typeShow=undefined;
            }
        }
    });
    var waps=new Vue({
        el:'.zui-table-view',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isFold:false,
            title:'',
            sideTitle:'',
            centent:'',
            jysbList:[],
            totlePage:0
            
        },
        methods:{
            DelLine:function (data) {
                //pop.typeShow=type=='sc'?false:true
                pop.isShow=true;
                pop.title='系统提示';
                pop.centent='确定删除【'+data.hostname+'】设备吗？'
                pop.jysbObj=data;
            },
            //双击编辑
            dbEdit:function (data) {
            	wapse.isAdd=false;
            	wapse.isDetail=true;
                wapse.isFold = true;
                wapse.sideTitle='编辑检验设备';
               // console.log($(window).height());//浏览器当前窗口可视区域高度
               // console.log($('.tab-message').outerHeight());
               // console.log($('.ksys-btn').outerHeight());
                changeElement();
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                if(data.sgsb=='0'){
                	data.sgsb=true;
                }else{
                	data.sgsb=false;
                }
                
                wapse.jysbObj=data;

            },
            getData:function(){
            	wrapper.queryAll();
            }

        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
        	isDetail:false,
        	isAdd:false,
        	
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            title:'',
            sideTitle:'',
            popContent:{},
            centent:'',
            isFold: false,
            jysbObj:{'isfile': null},
            jysbList_detail:[],
            util:'',
            
            
        },
        methods:{
            // 取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // 确定
            confirms:function () {
                
                //malert('222','top','success');
            	if(wapse.jysbObj.sgsb==true){
            		wapse.jysbObj.sgsb='0';
            	}else{
            		wapse.jysbObj.sgsb='1';
            	}
                this.$http.post('/actionDispatcher.do?reqUrl=XtwhJysb&types=saveSb',JSON.stringify(wapse.jysbObj)).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				//成功回调提示
                                malert('修改成功','top','success');
                                wrapper.queryAll();
                                $(".side-form-bg").removeClass('side-form-bg')
                                $(".side-form").addClass('ng-hide');
                			}
                        }, 
                        function(error) {
                        	malert(error,'top','defeadted');
                        });
                
            },
            confirms_Add : function () {
            	if(wapse.jysbObj.sgsb==true){
            		wapse.jysbObj.sgsb='0';
            	}else{
            		wapse.jysbObj.sgsb='1';
            	}
            	
            	
            	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJysb&types=saveSb',JSON.stringify(wapse.jysbObj)).then(
                		function(data) {
                			if(data.body.a==0){
                				//成功回调提示
                                malert('修改成功','top','success');
                                wapse.jysbObj={}
                                wrapper.queryAll();
                			}else{
                			    //2018/07/19接收参数错误
                                // malert(json.body.c,'top','defeadted');
                                malert(data.body.c,'top','defeadted');
                            }
                        }, 
                        function(error) {
                        	malert(error,'top','defeadted');
                        });
            	}

        }
    });
    
    wrapper.queryAll();
    wrapper.getutil();
