.userNameBg{
    background:#708f89;
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    -webkit-user-select: none;
    /*margin-top: -125.58px;*/
    -moz-user-select: none;
    user-select: none;
    background-image: url("/newzui/pub/image/userImg.png");
}
.flex{
    display: flex;
    align-items: center;
}
.userNameImg{
    /*width: 100px;*/
    /*height: 100px;*/
}
.header-item{
    height: 100%;
    /*padding-top: 125.58px;*/
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}
.text-color{
    width: 100%;
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.userNameImg img{
    width: 100%;
    height: 100%;
}


.userCwh .text,.userFooter .text{
    display: inline-block;
    white-space: nowrap;
    margin-right: 20px;
}
.userCwh{
    margin-bottom: 4px;
}
.dyblImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    height:80px;
    background-image: url("/newzui/pub/image/yuanquan.png");
}
.dyblImg:after{
    content: '';
    background-image: url("/newzui/pub/image/bl.png");
    width: 60%;
    height: 60%;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
}
.userFooter{
margin-bottom: 13px;
}
.heaf{

    color: #B0BFBB;
    text-decoration: underline;
}
.content{
    /*-webkit-user-select: none;*/
    /*-moz-user-select: none;*/
    /*user-select: none;*/
    width: 100%;
    overflow: hidden;
    height: calc(100% + 100px);
    padding-top: 36px;
    background: #fff;
}
.blImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/xbl.png");
}
.xyzImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/xyz.png");
}
.fzImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/fz.png");
}
.dy-bgImg{
     width:86px;
     height: 86px;
     background: url("/newzui/pub/image/dy-bl.png") center no-repeat;
     background-size: 86px 86px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}
.dy-img{
    width: 48px;
    height: 41px;
    background: url("/newzui/pub/image/dy-img.png") center no-repeat;
    background-size: 48px 41px;
    cursor: pointer;
    display: block;
}
.blRight{
    position: absolute;
    right: 7px;
    display: flex;
    bottom: -40px;
}



.loadPage{
    position: relative;
    padding-top: 10px !important;
    background: #fff;
    overflow: auto;
    height: 100%;
}
.fyxm-tab{
    margin-top: -36px;
}
.pop-width{
    width: 380px;
}
.color-f2a654{
color:#f2a654;
}
.sdrj-pop{
    border:1px solid #dfe3e9;
    border-radius:4px;
    height:74px;
    width: 284px;
}
