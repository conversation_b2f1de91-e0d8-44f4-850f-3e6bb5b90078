input, label, img, th, textarea {
     vertical-align: initial;
}
html, body {
    height: 100%;
}

* {
    margin: 0;
    padding: 0;
}

.max-html {
    width: 100%;
    height: 100%;
    position: relative;
    min-height: 100%;
    background: #fff;
}

.left {
    /*float: left;*/
    width: 12%;
    display: inline-table;
    margin-right: 9px;
    height: 100%;
    min-height: 100%;
    background-color: #ffffff;
    box-shadow: 0 3px 6px 5px rgba(0,0,0,.08);
}

.left .main {
    width: 100%;
    height: 95%;
}

.left .header {
    background-color: #ffffff;
    height: 42px;
    line-height: 42px;
}

.left .header .text {
    display: inline-table;
    width: 48%;
    text-align: center;
    font-size: 14px;
    cursor: pointer;
    position: relative;
}

.left .header .active {
    color: #1abc9c;
}

.left .header .active:after {
    position: absolute;
    content: '';
    left: 50%;
    bottom: 1px;
    background-color: #1abc9c;
    height: 2px;
    width: 60%;
    transform: translate(-50%, 0);
}

.left .main .jczj {
    background: #f6f8f9;
    border: 1px solid #eaecf1;
    width: 99%;
    cursor: pointer;
    height: 34px;
    font-size: 12px;
    color: #767f8d;
    text-indent: 15px;
    line-height: 34px;
    position: relative;
}

.left .main .jczj:after {
    position: absolute;
    content: '';
    right: 16px;
    top: 50%;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 6px solid #7f8fa4;
    transform: translate(0, -50%);
}

.left .content {
    width: 100%;
    height: 89.9%;
}

.left .content .itemzj .list {
    list-style: none;
    display: inline-table;
    width: 30%;
    cursor: pointer;
    text-align: center;
    margin-bottom: 20px;
}

.left .content .itemzj {
    margin-top: 16px;
}

.left .content .itemzj .list img {
    width: 30px;
    height: 30px;
}

.left .content .itemzj .list p {
    font-size: 12px;
    color: #7f8fa4;
    margin-top: 6px;
}

.left .main .footer .itemzj {
    height: 42px;
    line-height: 42px;
    background: #1abc9c1c;
}

.left .main .footer .itemzj .list {
    list-style: none;
    display: inline-table;
    text-align: center;
    height: 100%;
    width: calc(100% / 6);
    cursor: pointer;
}

.left .main .footer .itemzj .list img {
    width: 100%;
    height: 70%;
    margin-top: 15%;
}

.cler {
    clear: both;
}

/*左边css样式*/
.right {
    float: right;
    width: 87%;
    padding-right: 10px;
}
.right .content{
    background: #fff;
    position: relative;
    border: 1px solid #eee;
}
.right .header .left-text {
    font-size: 18px;
    color: #354052;
    float: left;
    font-weight: bold;
    text-indent: 5px;
    padding-left: 28px;
    position: relative;
}

.right .header .left-text:after {
    position: absolute;
    left: 17px;
    top: 50%;
    transform: translate(0, -50%);
    content: '';
    width: 4px;
    height: 60%;
    background: #1abc9c;
}

.right .header .right-icon {
    float: right;
    padding-right: 28px;
}

.right .header {
    background: #ffffff;
    border: 1px solid #eeeeee;
    border-radius: 4px;
    height: 46px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.right .header .right-icon .icon-sava, .right .header .right-icon .icon-lc, .right .header .right-icon .icon-rest, .right .header .right-icon .icon-szmb, .right .header .right-icon .icon-close {
    position: relative;
    padding-left: 20px;
    font-size: 14px;
    cursor: pointer;
    margin-left: 25px;
}

.right .header .right-icon .icon-sava:before {
    background-image: url("images/<EMAIL>");
}

.right .header .right-icon .icon-lc:before {
    background-image: url("images/<EMAIL>");
}

.right .header .right-icon .icon-rest:before {
    background-image: url("images/<EMAIL>");
}

.right .header .right-icon .icon-szmb:before {
    background-image: url("images/<EMAIL>");
}

.right .header .right-icon .icon-close:before {
    background-image: url("images/<EMAIL>");
}

.right .header .right-icon .icon-sava:before, .right .header .right-icon .icon-lc:before, .right .header .right-icon .icon-rest:before, .right .header .right-icon .icon-szmb:before, .right .header .right-icon .icon-close:before {
    position: absolute;
    left: 0;
    top: 50%;
    content: '';
    transform: translate(0, -50%);
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
}

/*侧边弹窗*/
.sideForm {
    position: fixed;
    right: 9px;
    top: 58px;

}

.sideForm .tab-message {
    height: 50px;
    line-height: 50px;
    text-align: center;
    position: relative;
    background-color: #1abc9c;
}

.sideForm .tab-message .text {
    font-size: 18px;
    color: #fbfdff;
}

 .closex {
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translate(0, -50%);
    font-size: 18px;
    width: 50px;
    cursor: pointer;
     text-align: center;
    color: #fff;
}

.sideForm {
    width: 320px;
    background: #fff;
    height: 100%;
    box-shadow: 0 3px 6px 5px rgba(0,0,0,.08);
}

.sideForm .ksys-side {
    width: 100%;
    /*height: 50px;*/
    position: relative;
    line-height: 50px;
}

.sideForm .ksys-side .active {
    position: relative;
    color: #1abc9c;
}

.sideForm .ksys-side .active:before {
    position: absolute;
    background-color: #1abc9c;
    content: '';
    left: 50%;
    bottom: -2px;
    height: 2px;
    width: 70%;
    transform: translate(-50%, 0);
}
.tab{
    width: 90%;
    margin: 0 auto;
    border-bottom: 2px solid #ddd;
    margin-bottom: 7px;
}
.sideForm .ksys-side:after {
    position: absolute;
    content: '';
    left: 50%;
    bottom: 0;
    width: 90%;
    transform: translate(-50%, 0);
    height: 1px;
    background-color: #e5eaf2;
}

.sideForm .ksys-side .line {
    width: 49%;
    text-align: center;
    display: inline-table;
    font-size: 16px;
    cursor: pointer;
    color: #8594a8;
    /*border-bottom: 2px solid #ddd;*/
}

.sideForm .ksys-side .kjsx {
    position: relative;
}

.sideForm .ksys-side .kjsx:after {
    position: absolute;
    content: '';
    right: 0;
    top: 50%;
    height: 50%;
    width: 1px;
    transform: translate(0, -50%);
    background-color: #e5eaf2;
}

.sideForm .no-xiugai {
    background: #f6f8f9;
    border: 1px solid #e5eaf2;
    height: 38px;
    position: relative;
    padding-left: 43px;
    line-height: 38px;
    color:#6b737e;
}

.sideForm .no-xiugai:after {
    position: absolute;
    content: '';
    right: 16px;
    top: 50%;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 6px solid #7f8fa4;
    transform: translate(0, -50%);

}

.sideForm .no-xiugai:before {
    position: absolute;
    content: '';
    left: 13px;
    top: 50%;
    transform: translate(0, -50%);
    background-image: url("images/<EMAIL>");
    width: 33px;
    height: 33px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
}
.sideForm .no-xiugai:nth-child(5):before{
    position: absolute;
    content: '';
    left: 13px;
    top: 50%;
    transform: translate(0, -50%);
    background-image: url("images/<EMAIL>");
    width: 33px;
    height: 33px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
}
.sideForm .id-list {
    height: 49px;
    line-height: 49px;
}

.sideForm .id-list .value-line {
    width: 49%;
    display: inline-table;
}
.sideForm .id-list .left-id{
    font-size:14px;
    color:#aeb6c4;
    text-align:center;
}
.sideForm .id-list .right-value{
    font-size:14px;
    color:#8f9cb0;
    text-align:center;
}
.sideForm .id-list  .color-yello{
    color:#f89160;
}
.sideForm .id-list .border-sr{
    overflow: hidden;
    white-space: nowrap;
    border:1px solid #dadde9;
    border-radius:2px;
    height:28px;
    width: 40%;
    line-height: 28px;
}
.sideForm .id-list .border-sr:focus{
    outline: #1abc9c auto 5px;;
}
.sideForm .id-list .value-select{
    width: 83%;
    color:#8f9cb0;
}
li{
    list-style: none;
}
.sideForm .color .color-item{
    text-align: center;
}
.sideForm .color .color-item .color-list{
    width: 20px;
    height: 20px;
    margin-right: 10px;
    cursor: pointer;
    display: inline-table;
    border-radius:2px;
}
.boxColor{
    box-shadow: 0 0 6px 0 rgb(255, 38, 26);
}
.sideForm .id-list  .select{
text-align: left;
}
.Shape{
background-image: url("images/<EMAIL>");
    width: 20px;
    height: 20px;
    background-position: center center;
    background-repeat: no-repeat;
    display: inline-block;
    border: 1px solid #1abc9c;
    position: relative;
    cursor: pointer;
}
.Shape:after{
    position: absolute;
    left: 0;
    content: '';
    top: 0;
    background-color: #1abc9c36;
    width: 20px;
    height: 20px;
}
.js-content{
    width: 212px;
    height: 223px;
    margin: 0 auto;
    box-shadow: 0 3px 6px 5px rgba(0,0,0,.08);
    position: absolute;
    bottom: 22%;
    left: 13%;
    /*transform: translate(-50%,0);*/
    padding: 5px;
    z-index: 1;

}
.ng-hide{
    display: none;
}
.right .content .txt{
    cursor: pointer;
    display: inline-block;
    position: absolute;
    /*height: 38px;*/
    z-index: 1;
    /*line-height: 38px;*/
}
.editing{
    border:1px solid #1abc9c;
}
.editingdiv{
    position: relative;
    border: 1px solid #1abc9c;
    width: 140px;
    display: inline-table;
}
.editingdiv .editing{
    border:1px solid #dfe3e9;
    height:36px;
    font-size:14px;
    color:#354052;
}
 .icon{
    position: absolute;
}
 .icon:after{
    position: absolute;
    content: '';
    width: 6px;
    height: 6px;
    background-color: #1abc9c;
}
 .lineIcon{
     width: 100%;
     height: 100%;
     border: 1px solid #1abc9c;
 }
 .lineIcon:after{
     position: absolute;
     content: '';
     width: 6px;
     height: 6px;
     background-color: #1abc9c;
     right: -2px;
     top:-3px;
 }
 .sline:after{
     position: absolute;
     content: '';
     width: 6px;
     height: 6px;
     background-color: #1abc9c;
     right: -3px;
     bottom: -3px;
     top: initial;
 }
 .lineIcon:before{
     position: absolute;
     content: '';
     width: 6px;
     height: 6px;
     background-color: #1abc9c;
     left:-3px;
     top:-3px;
 }
 .icon-left-top{
    left: -3px;
    top: -3px;
}
 .icon-left-center{
    left:-2px;
    top:50%;
    transform: translate(0,-50%);
}
 .icon-left-bottom{
    left: -3px;
    bottom: 2px;
}
  .icon-center-top{
     left: 50%;
     top: -4px;
     transform: translate(0,-50%);
 }
  .icon-center-bottom{
     left: 50%;
     bottom: 2px;
     transform: translate(0,-50%);
 }
  .icon-right-top{
     right: 3px;
     top: -4px;
 }
  .icon-right-center{
     right: 3px;
     top: 50%;
     transform: translate(-50%,0);
 }
  .icon-right-bottom{
     right: 3px;
     bottom: 2px;
 }
  .editingdiv .sc{
      opacity: 0.6;
      background: #000000;
      width: 26px;
      height: 26px;
      border-radius: 100%;
      display: inline-block;
      text-align: center;
      cursor: pointer;
      line-height: 29px;
  }
.editingdiv .sc img{
    width: 13px;
    height: 14px;
}
.editingdiv .sx{
    opacity: 0.6;
    background: #000000;
    width: 26px;
    height: 26px;
    cursor: pointer;
    border-radius: 100%;
    display: inline-block;
    text-align: center;
    line-height: 29px;
}
.editingdiv .sx img{
    width: 13px;
    height: 14px;
}
.editingdiv .bottom-right{
    position: absolute;
    bottom:-35px;
    right: 0;
}
.trendDiv {
    position: relative;
    width: calc(100% - 2px);
}
.drawWidth {
    width: 4px;
    height: 15px;
    cursor: col-resize;
    float: right;
}
.main .temTable td{
    width: 30px;
    height: 30px;
}
.main .line{
    width: 100px;
    display: block;
    height: 1px;
    background-color: #dfe3e9;
}
.verticalline{
    width: 1px;
    display: block;
    height: 100px;
    background-color: #dfe3e9;
}
.hide{
    display: none;
}
.cursor{
    cursor: pointer;
}
.biaodashi .value-textarea{
    width: 100%;
    height: 321px;
    background:#ffffff;
    border:1px solid #dadde9;
    border-radius:2px;
    outline: none;
    padding-left: 14px;
    padding-top: 9px;
    font-size:14px;
    color:#354052;
}
.biaodashi{
    width: 90%;
    margin: 0 auto;
}
.bold{
    font-weight:bold
}
.oblique{
    font-style: oblique
}
.underline{
    text-decoration:underline
}
.through{
    text-decoration:line-through
}




[hidden] {
    display: none
}

html {
    font-family: sans-serif;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%
}

body {
    margin: 0
}

a:focus {
    outline: thin dotted
}

a:active, a:hover {
    outline: 0
}

h1 {
    font-size: 2em
}

b, strong {
    font-weight: bold
}

dfn {
    font-style: italic
}

mark {
    background: #ff0;
    color: #000
}

code, kbd, pre, samp {
    font-family: monospace, serif;
    font-size: 1em
}

pre {
    white-space: pre;
    white-space: pre-wrap;
    word-wrap: break-word
}

q {
    quotes: "\201C" "\201D" "\2018" "\2019"
}

small {
    font-size: 80%
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sup {
    top: -0.5em
}

sub {
    bottom: -0.25em
}

img {
    border: 0
}

svg:not(:root) {
    overflow: hidden
}

figure {
    margin: 0
}

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em
}

legend {
    border: 0;
    padding: 0
}

button, input, select, textarea {
    font-family: inherit;
    font-size: 100%;
    margin: 0
}

button, input {
    line-height: normal
}

button, html input[type="button"], input[type="reset"], input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer
}

button[disabled], input[disabled] {
    cursor: default
}

input[type="checkbox"], input[type="radio"] {
    box-sizing: border-box;
    padding: 0
}

input[type="search"] {
    -webkit-appearance: textfield;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none
}

button::-moz-focus-inner, input::-moz-focus-inner {
    border: 0;
    padding: 0
}

textarea {
    overflow: auto;
    vertical-align: top
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

html, body {
    margin: 0;
    padding: 0;
    font-size: 100%;
    -webkit-tap-highlight-color: transparent
}

body {
    background: white;
    color: #3f3844;
    font-family: "Lato", "Helvetica", Helvetica, sans-serif
}

main {
    display: block
}

ol, ul, li {
    list-style: none;
    margin: 0;
    padding: 0
}

input {
    outline: none;
    border-radius: 3px;
    border: 1px solid #c3c3c3;
    padding: 12px
}

::-webkit-input-placeholder {
    color: #eaeaea;
    -webkit-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1)
}

:focus::-webkit-input-placeholder {
    opacity: 0.5
}

:-ms-input-placeholder {
    color: #eaeaea;
    -webkit-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1)
}

:focus:-ms-input-placeholder {
    opacity: 0.5
}

:-moz-placeholder {
    color: #eaeaea;
    opacity: 1;
    -webkit-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1)
}

:focus:-moz-placeholder {
    opacity: 0.5
}

::-moz-placeholder {
    color: #eaeaea;
    opacity: 1;
    -webkit-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1)
}

:focus::-moz-placeholder {
    opacity: 0.5
}

p {
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.025em;
    font-size: 22px;
    font-size: 1.375rem
}

p a {
    color: #b0b0b0;
    border-bottom: 2px solid #c3c3c3
}

p a:hover {
    color: #3f3844;
    border-bottom: 2px solid #3f3844
}

a {
    font-family: inherit;
    font-weight: inherit;
    font-style: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    text-decoration: none;
    cursor: pointer;
    color: inherit
}

code {
    font-family: "Courier", Courier, monospace;
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.0375em;
    line-height: 1.4;
    font-size: 14px;
    font-size: 0.875rem
}

.colorPicker {
    width: 100%
}

.colorPicker .twod {
    position: relative;
    width: 86%;
    height: 396px;
    float: left;
    border-radius: 3px
}

.colorPicker .twod .bg {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 3px
}

.colorPicker .twod .bg1 {
    z-index: 0;
    background: -moz-linear-gradient(left, #fff 0%, rgba(255, 255, 255, 0) 100%);
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, #fff), color-stop(100%, rgba(255, 255, 255, 0)));
    background: -webkit-linear-gradient(left, #fff 0%, rgba(255, 255, 255, 0) 100%);
    background: -o-linear-gradient(left, #fff 0%, rgba(255, 255, 255, 0) 100%);
    background: linear-gradient(to right, #fff 0%, rgba(255, 255, 255, 0) 100%)
}

.colorPicker .twod .bg2 {
    z-index: 1;
    background: -moz-linear-gradient(top, transparent 0%, #000 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, transparent), color-stop(100%, #000));
    background: -webkit-linear-gradient(top, transparent 0%, #000 100%);
    background: -o-linear-gradient(top, transparent 0%, #000 100%);
    background: linear-gradient(to bottom, transparent 0%, #000 100%)
}

.colorPicker .twod .pointer {
    position: relative;
    z-index: 2;
    width: 8px
}

.colorPicker .twod .pointer .shape {
    position: absolute
}

.colorPicker .twod .pointer .shape1 {
    margin-left: -9px;
    margin-top: -9px;
    width: 18px;
    height: 18px;
    border: 3px solid #3f3844;
    -moz-border-radius: 9px;
    border-radius: 9px
}

.colorPicker .twod .pointer .shape2 {
    margin-left: -6px;
    margin-top: -6px;
    width: 12px;
    height: 12px;
    border: 3px solid white;
    -moz-border-radius: 6px;
    border-radius: 6px
}

.colorPicker .oned {
    width: 12.5%;
    height: 100px;
    /*padding: 0 1.5em;*/
    float: left;
    border-radius: 3px
}

.colorPicker .oned .bg {
    width: 100%;
    height: 100%;
    background: -moz-linear-gradient(top, red 0%, #ff0 17%, lime 33%, cyan 50%, blue 66%, #f0f 83%, red 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, red), color-stop(17%, #ff0), color-stop(33%, lime), color-stop(50%, cyan), color-stop(66%, blue), color-stop(83%, #f0f), color-stop(100%, red));
    background: -webkit-linear-gradient(top, red 0%, #ff0 17%, lime 33%, cyan 50%, blue 66%, #f0f 83%, red 100%);
    background: -o-linear-gradient(top, red 0%, #ff0 17%, lime 33%, cyan 50%, blue 66%, #f0f 83%, red 100%);
    background: linear-gradient(to bottom, red 0%, #ff0 17%, lime 33%, cyan 50%, blue 66%, #f0f 83%, red 100%);
    border-radius: 3px
}

.colorPicker .oned .pointer {
    position: relative;
    z-index: 2
}

.colorPicker .oned .pointer .shape {
    position: absolute;
    width: calc(100% + 6px);
    margin-left: -3px;
    margin-top: -4px;
    height: 9px;
    border: 3px solid #3f3844;
    -moz-border-radius: 3px;
    border-radius: 3px
}

.colorPicker .extras {
    position: relative;
    /*width: 159px;*/
    /*height: 396px;*/
    float: left;
    width: 100%;
    /*padding: 1.5em;*/
    line-height: initial;
    border-radius: 3px;
    border-right: 1px solid #eaeaea;
    border-left: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea
}

.colorPicker .extras .currentColorContainer {
    /*margin: -1.5em -1.5em 1em*/
    margin: 0.2em 0;
}

.colorPicker .extras .currentColor {
    cursor: pointer;
    width: calc(100% + 2px);
    height: 36px;
    margin-left: -1px;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px
}

.colorPicker .extras label, .colorPicker .extras input {
    font-family: "proxima-nova-soft", "Proxima Nova Soft", Helvetica, Arial, sans-serif;
    font-style: normal;
    letter-spacing: 0.01em;
    text-transform: uppercase;
    font-size: 14px;
    font-size: 0.875rem
}

.colorPicker .extras label {
    padding-right: .7em;
    font-weight: 600;
    color: #c3c3c3;
    margin-left: 0.7em;
}

.colorPicker .extras input {
    width: 80px;
    border: none;
    font-weight: 700;
    padding: 0.1875em 0.75em 0.125em;
    border-radius: 3px;
    border: 1px solid white;
    background-color: transparent;
    -webkit-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all 0.375s cubic-bezier(0.4, 0, 0.2, 1)
}

.colorPicker .extras input:focus {
    outline: none;
    border: 1px solid #eaeaea;
    background-color: transparent
}

.colorPicker .extras input[type=text] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

@media only screen and (max-width: 40em) {
    article#picker section.color-harmonies figure, article#picker section.color-harmonies figure:nth-child(odd), article#picker section.color-harmonies figure:nth-child(even) {
        width: 100%;
        float: none;
        margin: 3em 0 1.5em;
        text-align: center
    }

    article#picker section.color-harmonies figure img {
        padding: 1.5em 2.25em
    }

    .colorPicker .twod {
        width: 85%;
        height: 240px
    }

    .colorPicker .oned {
        width: 15%;
        height: 240px;
        padding: 0 0 0 1.5em
    }

    .colorPicker .extras {
        width: 100%;
        height: 100%;
        margin-top: 1.5em;
        padding: 1em 1.5em;
        border-top: 1px solid #eaeaea
    }

    .colorPicker .extras .currentColorContainer .currentColor {
        height: 88px
    }

    .colorPicker .extras div.hex {
        width: 100%
    }

    .colorPicker .extras div.colorFields {
        width: calc(100% / 3);
        float: left;
        padding: 0
    }

    .colorPicker .extras label {
        width: 40%;
        padding-right: 1.125em;
        font-weight: 600;
        color: #c3c3c3
    }

    .colorPicker .extras input {
        width: 60%
    }
}

@media only screen and (min-width: 40.063em) and (max-width: 48em) {
    .colorPicker .twod {
        width: calc(87.5% - 162px)
    }

    .colorPicker .extras {
        width: 162px
    }
}

table td {
    /*width: 100%;*/
    border-right: 1px solid #eaeaea;
    border-left: 1px solid #eaeaea;
    border-top: 1px solid #eaeaea;
    background-color: white
}

table > tbody tr:nth-child(1) {
    border-top-right-radius: 3px;
    border-top-left-radius: 3px
}

table > tbody tr:nth-last-child(1) {
    border-bottom: 1px solid #eaeaea;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px
}


*, *:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

.row {
    width: 100%;
    max-width: 75em;
    margin: 0 auto
}

.row:before, .row:after {
    content: " ";
    display: table
}

.row:after {
    clear: both
}
.pop{
    display: table;
    position: fixed;
    width: 100%;
    height: 100%;
    text-align: center;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.498039);
    transition: opacity 0.3s ease;
    z-index: 100;
}
.popCenter {
    display: table-cell;
    vertical-align: middle;
}
#pop .save {
    background-color: #FFFFFF;
    width: 400px;
    height: 200px;
    margin: 0 auto;
}
#pop .title{
    background: #1ABC9C;
    height: 40px;
    line-height: 40px;
    color: #ffffff;
    margin-bottom: 50px;
    position: relative;
    text-align: left;
    padding-left: 18px;
}
#pop  input{
    background:#ffffff;
    border:1px solid #dfe3e9;
    border-radius:4px;
    width:280px;
    height:34px;
}
/** btn按钮样式 **/
.footerSave .zui-btn {
    font-size: 14px;
    padding: 0px 12px 0px 12px;
    font-size: 14px;
    height: 32px;
    border: 1px solid transparent;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
    white-space: nowrap;
    display: initial;
    float: inherit;
    vertical-align: middle;
    text-align: center;
    font-weight: 400;
    touch-action: manipulation;
    cursor: pointer;
    user-select: none;
    background-color: #fff;
    color: #767d85;
    position: relative;
}
.footerSave .zui-btn.btn-primary {
    color: #fff;
    background-color: #1ABC9C;
}
.footerSave{
    width: 100%;
    margin: 0 auto;
    margin-top: 20px;
}
.tem {
    position: relative;
    float: left;
    /*width: 300px;*/
    /*height: 450px;*/
    width: 800px;
    height: 500px;
    border: 1px solid green;
    /*margin-left: 20px;*/
    margin-top: 20px;
}
.bgbs{
    background-color: #ffffff;
}
.item {
    position: absolute;
    display: inline-block;
    top: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    cursor: default;
    z-index: 100;
}

.item span {
    float: left;
    /*height: 38px;*/
    z-index: 1;
    /*line-height: 38px;*/
}

/*.item input {*/
    /*float: left;*/
    /*width: 80px;*/
    /*border: 1px solid #aaaaaa;*/
    /*padding: 4px;*/
/*}*/

/*.item div {*/
    /*float: left;*/
    /*width: 100px;*/
    /*height: 20px;*/
    /*border: 0;*/
    /*border-bottom: 1px solid #000000;*/
/*}*/
.zui-table-view{
    border: none !important;
}
.bb-view .list{
    height: calc(100% - 486px);
}
.viewPop{
    position: relative;
}
.viewPop .contentView{
    position: absolute;
    width: 600px;
    background-color: #ffffff;
    left: 50%;
    padding-bottom: 20px;
    transform: translate(-50%,25%);
    top: 50%;
    z-index: 11111;
    box-shadow: 0 3px 6px 5px rgba(0,0,0,.08);
}
.viewPop .popview{
    width: 100%;
    height: 100%;
    opacity:0.5;
    top: 0;
    z-index: 1111;
    background:#00120f;
    position: fixed;
}
.viewPop .contentView .viewHeadder{
    background:#1abc9c;
    height:46px;
    line-height: 46px;
    margin-bottom: 28px;
}
.viewPop .viewHeadder .leftText{
    float: left;
    margin-left: 18px;
}
.viewPop .viewHeadder .rightText{
    float: right;
    cursor: pointer;
    margin-right: 11px;
}
.viewPop .viewHeadder span{
    font-size:16px;
    color:#ffffff;
}
.viewPop  .contentView .contentItem .viewList{
    margin: 0 22px 19px 31px;
}
.viewPop  .contentView .contentItem .viewList input{
    display: inline-block;
    width: 88%;
}
.excel{
    width: 62px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    align-self: center;
    vertical-align: bottom;
}
.viewPop  .contentView  .ksys-btn{
    float: right;
    margin-top: 30px;
    position: sticky;
    display: inherit;
}
.ksys-btn button{
    display: initial;
    float: inherit;
}
.zui-table-body{
    position: relative;
    display: block;
    overflow: scroll;
    max-width: 100%;
    margin: 0 ;
    height: 79vh;
}
.fieldlist{
    display: none !important;
}
.demoonstrate{
    width: 100%;
    height: 100%;
    position: relative;
    float: left;
}
.bb-view .icon-hf:before{
    width: 20px;
    height: 15px;
    position: absolute;
    left: 5px;
    content: '';
    z-index: 11;
    top: 11px;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url("images/hf.png");
}
.bb-view .icon-sj:before{
    width: 16px;
    height: 16px;
    position: absolute;
    left: 5px;
    content: '';
    z-index: 11;
    top: 11px;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url("images/sj.png");
}
.paddr-r25{
    padding-left: 30px!important;
}
.paddr-r20{
    padding-left: 20px!important;
}