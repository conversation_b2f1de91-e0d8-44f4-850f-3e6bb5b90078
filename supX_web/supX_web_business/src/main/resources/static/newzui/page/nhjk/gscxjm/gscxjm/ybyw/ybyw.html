<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="ybyw.css" rel="stylesheet">
</head>
<body class="skin-default padd-b-10 padd-r-10 padd-l-10 padd-t-10">
<OBJECT style="display: none" classid="clsid:D9532F10-603B-4BF7-87AE-F4130EF43553"  width="0" height="0" align="center"  id="Ts" HSPACE="0" VSPACE="0"></OBJECT>
<div class="wrapper background-f flex-container flex-dir-c" id="wrapper" v-cloak>
    <div class="panel" :id="a">
        <div class="flex-container  tong-top flex-align-c">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" v-if="num==0" @click="sfrz">身份认证</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==0" @click="rydjData">确认登记</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==0" @click="changeRydj">修改登记</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==0" @click="cancelDj">取消登记</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" v-if="num==1" @click="uploadFy">上传费用</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==1" @click="cancelUpload">取消上传</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" v-if="num==2" @click="firstJs">预结算</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==2" @click="comfirmJs">正式结算</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==2" @click="cancenlJs">取消结算</button>
            <button class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5" @click="getData">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5" @click="bcdPrint">补偿单打印</button>
            <button class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5" @click="fycz">费用冲正</button>
        </div>
    </div>
    <div class="flex-container skin-default">
            <div class="tab-card" style="width: 20%">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">已登记病人</div>
                </div>
                <div class="tab-card-body  ">
                    <div class="flex-container flex-align-c padd-b-10 padd-t-10 padd-l-5">
                        <label class="whiteSpace  ft-14 margin-r-1 padd-r-5">住院号</label>
                        <div class="zui-input-inline">
                            <input class="zui-input " placeholder="已登记住院号" v-model="djsearch" type="text"
                                   @keydown="searchHc()"/>
                        </div>
                    </div>
                    <div class="zui-table-view ">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m" >序号</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>住院号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event)">
                            <table class="zui-table table-width50" v-if="brList.length!=0">
<!--                            <table class="zui-table table-width50">-->
                                <tbody>
                                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex},color[item.jsbz]]"
                                    @mouseenter="switchIndex('hoverIndex',true,$index)"
                                    @mouseleave="switchIndex()"
                                    @click="switchIndex('activeIndex',true,$index),dim(item)"
                                    :tabindex="$index"
                                    v-for="(item, $index) in brList">
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.zyh"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <p v-if="brList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        <div class="hzList  padd-r-10 padd-l-10" :class="{'flex-container flex-dir-c':num==2}" style="width: 80%">
            <tabs :num="num" @tab-active="tabBg" :tab-child="[{text:'城乡居民入院登记'},{text:'城乡居民数据上传'},{text:'城乡居民结算'}]"></tabs>
            <div v-if="num==2" class="totalPrice" style="font-size: 16px;color: #ff4735">His总费用:{{zygrxxJson.money}}</div>
            <div key="a" v-if="num==0">
                <div class="tab-card">
                    <div class="tab-card-header">
                        <div class="tab-card-header-title">入院信息</div>
                    </div>
                    <div class="grid-box tab-card-body">
                        <div class="flex-container flex-wrap-w flex-align-c padd-t-10">
                            <div class="flex-container flex-align-c margin-l-15 margin-b-15">
                                <span class="whiteSpace  ft-14 margin-r-5">住&ensp;院&ensp;号</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text"
                                           v-model="search" placeholder="未登记住院号" @keyDown="sschangeDown" id="search"/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-l-15 margin-b-15">
                                <span class="whiteSpace  ft-14 margin-r-5">病人姓名</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text" v-model="zygrxxJson.brxm" disabled/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">性&emsp;&emsp;别</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text" :value="brxb_tran[zygrxxJson.brxb]" disabled/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">床&ensp;位&ensp;号</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text" v-model="zygrxxJson.rycwbh" disabled/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">人员类别</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text" v-model="zygrxxJson.grxz" disabled/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">出生日期</span>
                                <div class=" wh180">
                                    <input class="zui-input "  type="text" v-model="zygrxxJson.csrq" disabled/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">病人年龄</span>
                                <div class="wh180 flex-container flex-align-c">
                                    <input type="number" class="wh100 zui-input" v-model="zygrxxJson.nl" disabled="disabled">
                                    <select-input class="wh80" @change-data="resultChange" :not_empty="false"
                                                  :child="nldw_tran" :index="zygrxxJson.nldw" :val="zygrxxJson.nldw"
                                                  :name="'zygrxxJson.nldw'" :search="false" :disable="false" :phd="''">
                                    </select-input>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">入院科室</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text" v-model="zygrxxJson.ryksmc" disabled/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">住院医师</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text" v-model="zygrxxJson.zyysxm" disabled/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">入院日期</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text" v-model="zygrxxJson.ryrq" disabled/>
                                </div>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">费&emsp;&emsp;别</span>
                                <select-input class="wh180" @change-data="resultChangeData" :not_empty="true"
                                              :child="brFbList"
                                              :index="'fbmc'" :index_mc="'fbmc'"
                                              :index_val="'fbbm'" :val="fbJson.fbbm" :name="'fbJson.fbbm'"
                                              :search="true"
                                              :disable="false"
                                              :phd="''">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">保险病人</span>
                                <div class="zui-table-cell text-left  wh180">
                                    <input class="green" :true-value="1" :false-value="0" v-model="zygrxxJson.bxbr" type="checkbox">
                                    <label @click="doCheck('bxbr')" @dblclick.stop></label>
                                </div>
                            </div>
                        </div>
                        <div class="flex-container flex-wrap-w flex-align-c margin-t-10">
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15"  v-if="textnum==0">
                                <span class="whiteSpace  ft-14 margin-r-5">疾病编码</span>
                                <input @keydown="changeDown($event)" class="zui-input position" v-model="zygrxxJson.jbmc" @input ="searching(false,$event.target.value)">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="pageSelect"
                                              @click-one="checkedOneOut0"  @click-two="checkedOneOut0">
                                </search-table>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                <span class="whiteSpace  ft-14 margin-r-5">保险类别</span>
                                <div class=" wh180">
                                    <input class="zui-input " type="text" v-model="zygrxxJson.bxlbmc" disabled/>
                                </div>
                            </div>

                            <div class="flex-container flex-align-c margin-b-15 margin-l-15" v-if="textnum==0">
                                <span class="whiteSpace  ft-14 margin-r-5">来院状态</span>
                                <select-input class="wh180" @change-data="resultChange"
                                              :child="ryztList"
                                              :index="'rymc'"
                                              :index_val="'rybm'" :val="lyjson.rybm" :name="'lyjson.rybm'" :search="true"
                                              :disable="false"
                                              :phd="''">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-b-15 margin-l-15" v-if="textnum==0">
                                <span class="whiteSpace  ft-14 margin-r-5">就诊类型</span>
                                <select-input class="wh180" @change-data="resultChange"
                                              :child="jzlxList"
                                              :index="'jzmc'"
                                              :index_val="'jzbm'" :val="jzjson.jzbm" :name="'jzjson.jzbm'" :search="true"
                                              :disable="false"
                                              :phd="''">
                                </select-input>
                            </div>
                            <!--<div class="flex-container flex-align-c margin-b-15 margin-l-15">-->
                            <!--<span class="whiteSpace  ft-14 margin-r-5">保险机构</span>-->
                            <!--<select-input @change-data="resultChange" class="wh180" :not_empty="false" :child="bxLbList"-->
                            <!--:index="'bxlbmc'"-->
                            <!--:index_val="'bxlbbm'" :val="json.bxjgbm" :name="'json.bxjgbm'" :search="true"-->
                            <!--:disable="false"-->
                            <!--:phd="''">-->
                            <!--</select-input>-->
                            <!--</div>-->
                            <!--<div class="flex-container flex-align-c margin-b-15 margin-l-15">-->
                            <!--<span class="whiteSpace  ft-14 margin-r-5">医疗统筹<br/>类&emsp;&emsp;别</span>-->
                            <!--<select-input class="wh180" @change-data="resultChange" :not_empty="true" :child="nldw_tran"-->
                            <!--:index="json.tclb"-->
                            <!--:val="json.tclb" :name="'json.tclb'" :search="false" :disable="false"-->
                            <!--:phd="''">-->
                            <!--</select-input>-->
                            <!--</div>-->
                        </div>
                    </div>
                </div>
            </div>
            <div key="b" v-if="num==1">
                <div class="flex-container flex-align-c">
                    <tabs :num="childNum" @tab-active="childTabBg" :tab-child="[{text:'未上传'},{text:'已上传'}]"></tabs>
                    <div> <p class="color-wtg"></p></div>
                </div>
                <div class="zui-table-view">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>记录ID</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>项目ID</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>项目名称</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>保险编码</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>保险项目</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>保内保外</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>数量</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>单价</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>金额</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>规格</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>单位日期</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>操作员</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>类型</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>来源</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>医生</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>处方号</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)">
                        <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                        <table class="zui-table table-width50">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex},{'setredbackground': (item.wydm==null||item.wydm=='')}]"
                                @mouseenter="switchIndex('hoverIndex',true,$index)"
                                @mouseleave="switchIndex()"
                                @click="switchIndex('activeIndex',true,$index)"
                                :tabindex="$index"
                                v-for="(item, $index) in fylbList">
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fyid"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="item.mxfyxmbm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="item.mxfyxmmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="item.wydm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="item.ypmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="bnbw[item.bnbw]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fysl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fydj"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fyje"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fygg"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.sfrq"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.czyxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.type"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.ly"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.zyysxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.cfh"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                    </div>
                </div>
            </div>
            <div class="flex-one over-auto" key="c" v-if="num==2">

                <div class="tab-card">
                    <div class="tab-card-header">
                        <div class="tab-card-header-title">结算信息</div>
                    </div>
                    <div class="grid-box tab-card-body">
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title">病人基本信息</div>
                            </div>
                            <div class="grid-box tab-card-body">
                                <div class="flex-container flex-wrap-w flex-align-c">
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">住&ensp;院&ensp;号</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text"  v-model="zygrxxJson.zyh" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">姓&emsp;&emsp;名</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.brxm" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">性&emsp;&emsp;别</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="xb_tran[zygrxxJson.brxb]" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">出生日期</span>
                                        <div class=" wh180">
                                            <input class="zui-input "  type="text" v-model="zygrxxJson.csrq" disabled/>
                                        </div>
                                    </div>

                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">病人年龄</span>
                                        <div class="wh180 flex-container flex-align-c">
                                            <input type="number" class="wh100 zui-input" v-model="zygrxxJson.nl" disabled="disabled">
                                            <select-input class="wh80" @change-data="resultChange" :not_empty="false"
                                                          :child="nldw_tran" :index="zygrxxJson.nldw" :val="zygrxxJson.nldw"
                                                          :name="'zygrxxJson.nldw'" :search="false" :disable="false" :phd="''">
                                            </select-input>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">床&ensp;位&ensp;号</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.rycwbh" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">人员类别</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.grxz" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">入院科室</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.ryksmc" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">住院医师</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.zyysxm" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">入院日期</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.ryrq" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">费&emsp;&emsp;别</span>
                                        <select-input class="wh180" @change-data="resultChangeData" :not_empty="true"
                                                      :child="brFbList"
                                                      :index="'fbmc'" :index_mc="'fbmc'"
                                                      :index_val="'fbbm'" :val="fbJson.fbbm" :name="'fbJson.fbbm'"
                                                      :search="true"
                                                      :disable="false"
                                                      :phd="''">
                                        </select-input>
                                    </div>
                                </div>
                                <div class="flex-container flex-wrap-w flex-align-c margin-t-10">
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">保险病人</span>
                                        <div class="zui-table-cell text-left  wh180">
                                            <input class="green" :true-value="1" :false-value="0" v-model="zygrxxJson.bxbr"
                                                   type="checkbox">
                                            <label @click="doCheck('bxbr')" @dblclick.stop></label>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">保险类别</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.bxlbmc" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">出院诊断</span>
                                        <input @keydown="changeDown($event,'1')" class="zui-input position" v-model="zygrxxJson.cyzdmc" @input="searching(false,$event.target.value)">
                                        <search-table :message="searchCon" :selected="selSearch"
                                                      :them="them" :them_tran="them_tran" :page="pageSelect"
                                                      @click-one="checkedOneOut"  @click-two="checkedOneOut">
                                        </search-table>
                                    </div>

                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">并发症&emsp;</span>
                                        <input @keydown="changeDown($event,'2')" class="zui-input position" v-model="zygrxxJson.bfzmc" @input ="searching(false,$event.target.value)">
                                        <search-table :message="searchCon" :selected="selSearch"
                                                      :them="bfthem" :them_tran="zfb_tran" :page="pageSelect"
                                                      @click-one="checkedOneOut1"  @click-two="checkedOneOut1">
                                        </search-table>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="padd-r-5">是否中途</br>结&emsp;&emsp;算</span>
                                    <select-input class="wh180" @change-data="resultChange"
                                                  :child="ifztjs_tran" :index="'ztjs.sfztjs'" :val="ztjs.sfztjs"
                                                  :name="'ztjs.sfztjs'"  :not_empty="true" >
                                    </select-input>
                                    </div>


                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">出院状态</span>
                                        <select-input class="wh180" @change-data="resultChange"
                                                      :child="cyzt_tran" :index="'cyjson.cyzt'" :val="cyjson.cyzt"
                                                      :name="'cyjson.cyzt'"  :not_empty="true" >
                                        </select-input>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">补偿账户</br>类&emsp;&emsp;别</span>
                                        <select-input class="wh180" @change-data="resultChange"
                                                      :child="zhlbList"
                                                      :index="'zhbcmc'"
                                                      :index_val="'zhbcbm'" :val="zhjson.zhbcbm" :name="'zhjson.zhbcbm'" :search="true"
                                                      :disable="false"
                                                      :phd="''">
                                        </select-input>
                                    </div>
                                    <div  class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">补偿类别</span>
                                        <select-input class="wh180" @change-data="resultChange"
                                                      :child="zybclbList"
                                                      :index="'mc'"
                                                      :index_val="'bm'" :val="bcjson.bm" :name="'bcjson.bm'" :search="true"
                                                      :disable="false"
                                                      :phd="''">
                                        </select-input>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">医疗证号</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.ybkh" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">成员
                                            序号</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.cyxh" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">身份证号</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text"  v-model="zygrxxJson.sfzjhm" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">家庭地址</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text"  v-model="zygrxxJson.jtdz" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">联系电话</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text"  v-model="zygrxxJson.sjhm" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">住院登记<br/>流&ensp;水&ensp;号</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.zydjlsh" disabled/>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title">结算信息</div>
                            </div>
                            <div class="grid-box tab-card-body">
                                <div class="flex-container flex-wrap-w flex-align-c">
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">总&ensp;费&ensp;用</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.zfy" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">自费费用</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.zffy" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">合理费用</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.hlfy" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">实际补偿<br/>费&emsp;&emsp;用</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.sjbcje" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">本&ensp;次&ensp;起<br/>付&emsp;&emsp;线</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.qfx" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">报销比例</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.bxbl" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">大&ensp;病&ensp;支<br/>付&emsp;&emsp;额</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.dbzfe" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">单次补偿<br/>合&emsp;&emsp;计</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.dcbchj" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">大病合规<br/>费&emsp;&emsp;用</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.dbbxhgfy" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">大病理赔<br/>次&emsp;&emsp;数</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.dblpcs" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">是否意外<br/>伤&emsp;&emsp;害</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="sfywsh_tran[zygrxxJson.sfywsh]" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">是否医院<br/>垫&emsp;&emsp;付</span>
                                        <div class=" wh180">
                                            <div class=" wh180">
                                                <input class="zui-input " type="text" v-model="sfyydf_tran[zygrxxJson.sfyydf]" disabled/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">住&ensp;院&ensp;次</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.zycs" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">理赔类型</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="lplx_tran[zygrxxJson.lplx]" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">理赔金额</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.lpje" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">民政救助</br>金&emsp;&emsp;额</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.mzjzje" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">核算补偿<br/>金&emsp;&emsp;额</span>
                                        <div class=" wh180">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.hsbcje" disabled/>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                                        <span class="whiteSpace  ft-14 margin-r-5">计算公式</span>
                                        <div class=" wh400">
                                            <input class="zui-input " type="text" v-model="zygrxxJson.jsgs" disabled/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="popCenter" v-show="isShow" v-cloak>
    <div class="popshow"></div>
    <div class="zui-form podrag bcsz-layer  flex-container flex-dir-c"
         style="width: auto;overflow: hidden;top: 80px;bottom: 50px; height: auto; left: 100px; right: 100px">
        <div class="layui-layer-title">请输入参合人员信息</div>
        <span class="layui-layer-setwin">
                <a @click="isShow=false" class="closex ti-close" href="javascript:;"></a>
            </span>
        <div class="layui-layer-content flex-container flex-dir-c flex-one">
            <div class="layui-height flex-container flex-dir-c flex-one " id="loadPage">

            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="ybyw.js"></script>
</body>
</html>
