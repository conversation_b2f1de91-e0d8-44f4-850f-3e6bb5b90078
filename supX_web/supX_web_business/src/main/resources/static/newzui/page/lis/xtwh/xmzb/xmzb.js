(function () {
    $(".zui-table-view").uitable();
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,done:function (value,data) {
        }
    });

   var pd=new Vue({
        el:'.xmzb-top',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
        	parm:{
            	pydm:''
            },
            pydm:''
        },
        methods:{
        	queryData:function(){
        		 $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryXmzb&parm="+JSON.stringify(pd.parm), function (json) {
                     if (json.a == 0) {
                     	wapse.jyxmList=json.d.list;
                     } else {
                         malert("查询失败" + json.c);
                         return;
                     }
                 });
        	}
        },
        watch:{
        	'parm.pydm':function(){
        		pd.parm.pydm=pd.parm.pydm.toUpperCase();
        		this.queryData();
        	}, 
        	'pydm':function(){
    		   var json={
        			   bm:isTabel.jyxmMx.bm,
        			   pydm:this.pydm.toUpperCase()
        	   };
        	   
        	   $.getJSON("/actionDispatcher.do?reqUrl=XtwhXmzb&types=queryOtherZbbm&param="+JSON.stringify(json), function (json) {
                   if (json.a == 0) {
                   	wapse.zbbmList=json.d.list;
                   } else {
                       malert("查询失败" + json.c);
                       return;
                   }
               });
    	   }
        }
    });
    
   var wapse=new Vue({
       el:'.xmzb-content',
       mixins: [dic_transform, tableBase, mConfirm, baseFunc],
       data:{
           isShowpopL:false,
           isShow:false,
           title:'',
           centent:'',
           isFold: false,
           jyxmList:[],
           zbbmList:[]
       },
       methods:{
           // 点击增加对应标签
           AddList: function (data) {
        	   isTabel.sumNum=data.zbbmList.length;
        	   isTabel.jyxmMx=data;
        	   
        	   var json={
        			   bm:data.bm
        	   };
        	   
        	   $.getJSON("/actionDispatcher.do?reqUrl=XtwhXmzb&types=queryOtherZbbm&param="+JSON.stringify(json), function (json) {
                   if (json.a == 0) {
                   	wapse.zbbmList=json.d.list;
                   } else {
                       malert("查询失败" + json.c);
                       return;
                   }
               });
        	   
        	   
        	   
           },
           //AddDown右边双击
           AddDown:function (data,index) {
               wapse.zbbmList.splice(index,1);
               
               isTabel.jyxmMx.zbbmList.push(data);
               isTabel.sumNum=isTabel.jyxmMx.zbbmList.length;
           }


       }
   }); 
   
   var isTabel=new Vue({
       el:'.isTabel',
       mixins: [dic_transform, baseFunc, tableBase],
       data:{
           isTabelShow:false,
           minishow:true,
           title:'',
           centent:'',
           isShowpopL:false,
           isShow:false,
           sumNum:0,
           jyxmMx:'',
           
           delIndex:'',
           delData:''
       },
       methods:{
              //dbDel双击删除
           dbDel:function (data,index) {
               pop.isShowpopL=true;
               pop.isShow=true;
               pop.title='项目指标';
               pop.centent='确定删除该项目指标吗？';
               
               isTabel.delIndex=index;
               isTabel.delData=data;
               
           },
           bcjg:function(){
        	   isTabel.isTabelShow=false;
        	   isTabel.minishow=true;
        	   var List=[];
        	   for (var int = 0; int < isTabel.jyxmMx.zbbmList.length; int++) {
        		   //拼装为需要的数据格式
        		   var d={
        				  xmbm:isTabel.jyxmMx.bm,
        				  zbbm:isTabel.jyxmMx.zbbmList[int].zbbm,
        				  xh:isTabel.jyxmMx.zbbmList[int].xh,
        				  tdh:''
        		   };
        		   if(d.zbbm != '' && d.zbbm != null){
        			   List.push(d);
        		   }
        		   
        	   }
        	   if(List.length==0){
     			  var d={
     					 xmbm:isTabel.jyxmMx.bm
         		   };
     			 List.push(d);
     		   }
        	   var json='{"list":' + JSON.stringify(List) + '}';
        	  this.$http.post('/actionDispatcher.do?reqUrl=XtwhXmzb&types=addXmzb',json).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                                malert('保存结果成功','top','success');
                			}
                        }, 
                        function(error) {
                        	malert(error,'top','success');
                        });
        	   
        	   
           }
       },
   });
   
   
   
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                isTabel.jyxmMx.zbbmList.splice(isTabel.delIndex,1);
                wapse.zbbmList.push(isTabel.delData);
                isTabel.sumNum=isTabel.jyxmMx.zbbmList.length;
                isTabel.delIndex='';
                isTabel.delData='';
                
            }
        }
    });
    
  
    
    //初始化方法
    pd.queryData();
    
})()
