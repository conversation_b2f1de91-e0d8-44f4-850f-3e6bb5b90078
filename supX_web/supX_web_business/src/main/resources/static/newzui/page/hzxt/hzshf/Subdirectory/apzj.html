<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>会诊申请管理审核方</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link type="text/css" href="apzj.css" rel="stylesheet"/>
    <style>
        .hz_span{
            color: red;
            font-size:18px;
            font-weight: bold;
        }
        .input_color{
            border: 1px solid red;
        }
    </style>
</head>

<body class="skin-default flex-container  padd-l-10 padd-r-10 padd-b-10 ">
<div class=" padd-l-10 background-f padd-r-10 over-auto" id="wrapper" v-cloak>
    <div class="apzj-title flex-container flex-jus-c">会诊申请单</div>
    <div class="jbxx-size  position">
        <div class="jbxx-position padd-b-10">
            <span class="jbxx-top"></span>
            <span class="jbxx-text">患者基本信息</span>
            <span class="jbxx-bottom"></span>
        </div>
        <div class="flex-container flex-align-c  padd-t-10">
            <div class="top-form margin-b-10">
                <span>患者姓名： <span class="hz_span">{{popContent.brxm}}</span></span>
            </div>
            <div class="top-form margin-b-10">
                <span>性别：<span class="hz_span">{{brxb_tran[popContent.brxb]}}</span></span>
            </div>
            <div class="top-form margin-b-10">
                <span>身份证号：<span class="hz_span">{{popContent.sfzjhm}}</span></span>
            </div>
            <div class="top-form margin-b-10">
                <span>年龄：<span class="hz_span">{{popContent.nl}} {{nldw_tran[popContent.nldw]}}</span></span>
            </div>
            <div class="top-form margin-b-10">
                <span>住院号：<span class="hz_span">{{popContent.zyh}}</span></span>
            </div>
        </div>
    </div>
    <div class="jbxx-size margin-t-15 position padd-b-10">
        <div class="jbxx-position">
            <span class="jbxx-top"></span>
            <span class="jbxx-text">会诊病历</span>
            <span class="jbxx-bottom"></span>
        </div>
        <div class="flex-container flex-dir-cr">
            <div class="flex-container flex-align-c margin-b-15 margin-t-15">
                <span class="ft-14   whiteSpace">既&nbsp;往&nbsp;史</span>
                <textarea style="width: 100%;height: 45px" v-model="popContent.jws"></textarea>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-t-15">
                <span class=" ft-14   whiteSpace">过&nbsp;敏&nbsp;史</span>
                <textarea style="width: 100%;height: 45px" v-model="popContent.gms"></textarea>
        </div>
            <div class="flex-container flex-align-c margin-b-15  margin-t-15">
                <span class="ft-14   whiteSpace">现&nbsp;病&nbsp;史</span>
                <textarea style="width: 100%;height: 80px" v-model="popContent.xbs"></textarea>
            </div>
        </div>
    </div>
    <div class="jbxx-size position margin-t-15 padd-b-10">
        <div class="jbxx-position">
            <span class="jbxx-top"></span>
            <span class="jbxx-text">病情资料</span>
            <span class="jbxx-bottom"></span>
        </div>
        <div class="flex-container flex-dir-cr">
            <div class="flex-container flex-align-c  margin-t-10">
                <div class="flex-container flex-align-c">
                    <span class="padd-r-5 ft-14 whiteSpace">申请时间</span>
                    <input type="text" v-model="popContent.sqrq" class="zui-input background-f wh200 sqrq text-indent-20" readonly="readonly"/>
                </div>
                <div class="flex-container flex-align-c">
                    <span class="padd-r-5 ft-14 whiteSpace">&emsp;&emsp;会诊时间</span>
                    <input type="text" v-model="popContent.hzkssj" class="zui-input background-f wh200 times text-indent-20" readonly="readonly"/>
                </div>
                <div class="flex-container flex-align-c margin-l-15">
                    <span class="padd-r-5 ft-14 whiteSpace">会诊地点</span>
                    <input type="text" v-model="popContent.hzdd" class="zui-input wh302  " placeholder="请输入请求会诊地点"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-t-15">
                <span class="ft-14 padd-r-5  whiteSpace ">会诊原因</span>
                <input type="text" v-model="popContent.hzyy" class="zui-input background-f input_color" placeholder="请输入会诊原因"/>
            </div>
            <div class="flex-container flex-align-c  position margin-t-20">
                <span class="padd-r-5 ft-14 whiteSpace">会诊目的</span>
                <input type="text" v-model="popContent.hzmd" class="zui-input background-f input_color" placeholder="请输入会诊目的"/>
            </div>
            <div class="flex-container flex-align-c margin-t-15 margin-b-15">
                <span class="ft-14 padd-r-5 whiteSpace ">病情概述</span>
                <textarea style="width: 100%;height: 80px" class="input_color" v-model="popContent.bqgs"></textarea>
            </div>
            <div class="flex-container flex-align-c margin-t-15">
                <span class="ft-14 padd-r-5  whiteSpace ">初步诊断</span>
                <input class="zui-input input_color" v-model="popContent.cbzd" @keydown="changeDown($event,'jbbm','jbbmContent','jbsearchCon')"
                       @input="change(false,'jbbm',$event.target.value)">
                <jbsearch-table :message="jbsearchCon" :selected="selSearch"
                                :them="jbthem" :page="page"
                                @click-one="checkedOneOut" @click-two="selectJbbm">
                </jbsearch-table>
            </div>
        </div>
    </div>
    <div class="jbxx-size position margin-t-15 padd-b-10">
        <div class="jbxx-position">
            <span class="jbxx-top"></span>
            <span class="jbxx-text">会诊要求</span>
            <span class="jbxx-bottom"></span>
        </div>
        <div class="flex-container flex-dir-c padd-b-15">
            <div class="flex-container flex-align-c">
                <div class="flex-container flex-align-c">
                    <span class="padd-r-5 ft-14 whiteSpace ">会诊类型</span>
                    <select-input class="wh120" class="background-f" @change-data="resultChange"
                                  :child="group_type" :index="'type'" :index_val="'number'"
                                  :val="popContent.hzlx"
                                  :name="'popContent.hzlx'" :search="true" :index_mc="'type'">
                    </select-input>
                    <!--<select-input :search="true"  class="wh120" class="background-f"  @change-data="resultChange" :not_empty="false"-->
                                  <!--:child="group_type" :index="popContent.hzlx" :val="popContent.hzlx"-->
                                  <!--:name="'popContent.hzlx'">-->
                    <!--</select-input>-->
                </div>
                <div class="flex-container padd-l-10 flex-align-c">
                    <span class="padd-r-5 ft-14 whiteSpace">会诊方式</span>
                    <div class="top-zinle padd-l-10">
                        <div class="c_radio padd-t-3">
                            <input type="checkbox" v-model="popContent.hzfs" id="1" >
                            <label for="1"></label>
                            <label for="1" class="lb_text">点名会诊</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class=" flex-container flex-align-b margin-t-10">
                <span class="padd-r-5 ft-14 whiteSpace">会诊信息</span>
                <div class="top-zinle">
                    <div class="apzj-top">
                        <i>会诊科室</i>
                        <i>会诊医生</i>
                        <i>操作</i>
                    </div>
                    <ul class="apzj-content">
                        <li>
                            <div class="apzj-detail">
                                <select-input class="wh122 height32 input_color" @change-data="commonResultChange"
                                              :child="ksList" :index="'ksmc'" :index_val="'ksbm'"
                                              :val="popContent.yqysks"
                                              :name="'popContent.yqysks'" :search="true" :index_mc="'ksmc'">
                                </select-input>
                            </div>
                            <div class="apzj-detail">
                                <select-input class="wh122 height32" @change-data="commonResultChange"
                                              :child="ryList" :index="'ryxm'" :index_val="'rybm'"
                                              :val="popContent.yqys"
                                              :name="'popContent.yqys'" :search="true" :index_mc="'ryxm'">
                                </select-input>
                            </div>
                            <div class="apzj-detail">
                                <i class="iconfont icon-iocn52 icon-font20 cursor" data-title="删除"></i>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="jbxx-size position margin-t-15 ">
        <div class="jbxx-position">
            <span class="jbxx-top"></span>
            <span class="jbxx-text">申请人信息</span>
            <span class="jbxx-bottom"></span>
        </div>
        <div class="flex-container flex-align-c padd-b-10">
            <div class="flex-container flex-align-c">
                <span class="ft-14 padd-r-5  whiteSpace">申请科室</span>
                <input type="text" v-model="popContent.sqysksmc" class="zui-input background-f" disabled/>
            </div>
            <div class="apzj-detail">
                <span class="ft-14 padd-r-5  whiteSpace">申请人</span>
                <select-input class="wh122 height32" @change-data="commonResult"
                              :child="sqysList" :index="'ryxm'" :index_val="'rybm'"
                              :val="popContent.sqys"
                              :name="'popContent.sqys'" :search="true" :index_mc="'ryxm'">
                </select-input>
            </div>
        </div>
    </div>
    <div class="zui-table-tool flex-end">
        <!--新建会诊跳转来显示对应的按钮操作-->
        <button v-waves class="root-btn btn-parmary" @click="submit">保存并提交</button>
        <!--编辑下的状态按钮操作-->
        <!-- button v-waves class="root-btn btn-parmary" @click="save">保存</button>-->
         <button v-waves class="root-btn btn-parmary-f2a1" @click="printHzsqd">打印</button>
         <button v-waves class="root-btn btn-parmary-d9" @click="Return">取消</button>
     </div>
 </div>

 </body>
 <script src="apzj.js" type="text/javascript"></script>

 </html>
