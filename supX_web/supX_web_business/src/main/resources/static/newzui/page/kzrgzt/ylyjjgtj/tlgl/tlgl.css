.tong-top{
    padding: 0;
    min-height: 45px;
}
.tabItem{
    width:126px;
    height:44px;
    cursor: pointer;
}
.tabItem.active{
    color:#1abc9c;
    position: relative;
    border-radius:4px 0 0 0;
    background:#ffffff;
}
.tabItem.active:before{
    width: 80%;
    content: '';
    height: 2px;
    position: absolute;
    bottom: 0;
    background: #1abc9c;
}
.color-bf71ec{
    color: #bf71ec;
}
.canvas1{
    width: 67.7% !important;
}
.border-error:before{
    border: 6px solid #fa6969 !important;
}
.border-error:after{
    border:1px solid #f3ebeb !important;
    background-color: #f8f3f3 !important;
}
.text-num-error{
    color:#fa6969 !important;
}
.text-decoration {
    color: #1abc9c;
    cursor: pointer;
    text-decoration: underline;
}
.errorBg{
    background-color:rgba(255,92,99,0.10);
}
.zui-table-view .zui-table-fixed{
    top: auto;
}