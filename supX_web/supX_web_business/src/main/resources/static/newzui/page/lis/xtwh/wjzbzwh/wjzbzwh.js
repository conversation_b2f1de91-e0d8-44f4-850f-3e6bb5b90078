    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.background',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            centent:'',
            data:{
            	zbbm:''
            }
        },
        methods:{
            guolu:function () {
                filter.isShow=true;
            },
            Print:function () {
                window.print()
            },
            delOK:function () {
                pop.isShowpopL=true
                pop.isShow=true
                pop.title='系统提示';
                pop.centent='确定删除该科室吗？';
            },
            queryAll:function(){
            	 $.getJSON("/actionDispatcher.do?reqUrl=LisXtwhWjz&types=queryAllWjz&yq=" + JSON.stringify(this.data), function (json) {
                     if (json.a == 0) {
                    	 wapses.wjzList = json.d;
                         console.log(wapses.wjzList);
                     } else {
                         malert("获取申请失败" + json.c,'top','success');
                         return;
                     }
                 });
            },
            getYbbm:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=gltjutil&yq=", function (json) {
            		brzcList.ybbmList=json.d.ybbm;
                });
            }
        },
        watch:{
        	'data.zbbm': function(){
        		wrapper.queryAll();
        	}
        }
    });
    var wapses=new Vue({
        el:'.xmzb-content',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            isFold: false,
            wjzList:''
        },
        methods:{
            //删除当前
            delNow:function () {
                pop.title='系统提示';
                pop.centent='确定删除该行内容吗？';
                pop.isShowpopL=true;
                pop.isShow=true;
            },
            //dbAdd
            dbAdd:function () {
                alert('双击添加');
            },
            queryWjzYblx : function(data){
            	var json={
            		zbbm:data.zbbmModel.zbbm,
            	};
            	 $.getJSON("/actionDispatcher.do?reqUrl=LisXtwhWjz&types=queryWjzYblx&yq=" + JSON.stringify(json), function (json) {
            		 console.log(json);
                     if (json.a == 0) {
                    	 brzcList.wjzYblxList=json.d;
                     } else {
                         malert("查询失败" + json.c,'top','defeadted');
                         return;
                     }
                 });
            },
            //双击编辑
            dbEdit:function (pd) {
            	
            	console.log(pd);
                
            	
            	this.isFold = true;
                this.sideTitle='危机值设置';
                /*$("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');*/
                
                
                brzcList.wjzObj=pd;
                
                var cklx=pd.zbbmModel.wjzpdfs;
            	var sjlx=pd.zbbmModel.sjlx;
            	
            	console.log("参考类型："+cklx +"___数据类型："+sjlx)
            	
            	//未明确
            	if(cklx=='0'){
            		 malert("未明确的参考值类型不需要设置",'top','defeadted');
                     return;
            	}
            	//通用
            	if(cklx=='1'){
            		//数值
            		if(sjlx=='1' || sjlx=='4'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao-1").show();
            			$(".cankao-1").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2' || sjlx=='3'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao-2").show();
            			$(".cankao-2").siblings('div').hide();
            		}
            		
            	}
            	//性别
            	if(cklx=='2'){
            		//数值
            		if(sjlx=='1' || sjlx=='4'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao1").show();
            			$(".cankao1").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2' || sjlx=='3'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao").show();
            			$(".cankao").siblings('div').hide();
            		}
            		
            	}
            	//年龄 性别
            	if(cklx=='3'){
            		//数值
            		if(sjlx=='1' || sjlx=='4'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao4").show();
            			$(".cankao4").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2' || sjlx=='3'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao3").show();
            			$(".cankao3").siblings('div').hide();
            		}
            		
            	}
            	
            	//样本类型
            	if(cklx=='4'){
            		this.queryWjzYblx(pd);
            		//数值
            		if(sjlx=='1' || sjlx=='4'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao6").show();
            			$(".cankao6").siblings('div').hide();
            			$(".cankao6").siblings('a').hide();
            		}
            		//文本
            		if(sjlx=='2' || sjlx=='3'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao5").show();
            			$(".cankao5").siblings('div').hide();
            			$(".cankao5").siblings('a').hide();
            		}
            	}
                
            	if(cklx=='5'){
            		this.queryWjzYblx(pd);
            		//数值
            		if(sjlx=='1' || sjlx=='4'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao9").show();
            			$(".cankao9").siblings('div').hide();
            			$(".cankao9").siblings('a').hide();
            		}
            		//文本
            		if(sjlx=='2' || sjlx=='3'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao8").show();
            			$(".cankao8").siblings('div').hide();
            			$(".cankao8").siblings('a').hide();
            		}
            	}
            	
            	if(cklx=='6'){
            		this.queryWjzYblx(pd);
            		//数值
            		if(sjlx=='1' || sjlx=='4'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao10").show();
            			$(".cankao10").siblings('div').hide();
            			$(".cankao10").siblings('a').hide();
            		}
            		//文本
            		if(sjlx=='2' || sjlx=='3'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao11").show();
            			$(".cankao11").siblings('div').hide();
            			$(".cankao11").siblings('a').hide();
            		}
            	}
                
                 
                
            }

        },
    });
    
   
    var brzcList=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
        	wjzObj:'',
        	wjzYblxList:[],
        	ybbmList:[],
        	
        	
            isTabelShow:false,
            sideTitle:'',
            isFold: false,
            isShowpopL:false,
            isShow:false,
            title:'',
            popContent:{},
            centent:'',
            num:'',
            num5:0,
            num6:0,
            num9:0,
            num8:0,
            num10:0,
            dom:[],
            dom5:[],
            dom6:[],
            dom8:[],
            dom9:[],
            numDom:2,
            domlist:1,
            arrDom:'<input class="zui-input"/>',
            pop10:['年龄段','参考值',],

        },
        created:function () {
            this.xinzeng4()
            this.xinzeng5()
            this.xinzeng6()
            this.xinzeng8()
            this.xinzeng9()
            var clone=$('.clone')
            var absol=$('.absol')
            var dom='<div  style="min-width: 25%;max-width: 25%" class="text-calc dbremove" >'+this.arrDom+'</div>'
            for(var i=0;i<this.pop10.length;i++){
                $(clone).find($(absol)).before(dom)
            }
        },
        computed:{

        },
        methods:{
        	Wf_YppfChange: function (val) {
        	    var index = "";
        	    //先获取到操作的哪一个
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        var types = val[2][val[2].length - 1];
        	        index = val[2][1];
        	        Vue.set(this.wjzYblxList[index], 'ybbm', val[0]);
        	    }
        	    
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        if (window.event.keyCode == 13) {
        	            this.nextFocus(event);
        	        }
        	    }
        	},
        	
        	
        	
            Guid:function () {
              return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g,function (e) {
                  var r=Math.random()*16|10,v=e=='x'?r:(r&0x3|0x8);
                  return v.toString(16);
              })
            },
            xinzeng4:function () {
               /* var cankaoO={}
                this.num=this.Guid()
                 cankaoO.cankao4='<div class="text-calc"><div><input type="number" style="width: 70px;" class="zui-input value_'+this.num+'"></div><div ><select  style="width: 70px;" class="zui-input "><option>年</option><option>月</option><option>日</option><option selected>小时</option> <option>分</option></select></div></div><span class="left-right">至</span><div class="text-calc"><div ><input type="number" style="width: 70px;" class="zui-input "> </div><div ><select  style="width: 70px;" class="zui-input "><option>年</option><option>月</option><option>日</option><option selected>小时</option> <option>分</option></select></div></div><div class="text-calc"><div ><input type="number" style="width: 70px;"  class="zui-input "> </div></div><div class="text-calc"><div ><input type="number" style="width: 70px;"  class="zui-input "> </div></div><div class="text-calc"><div><select  style="width: 70px;" class="zui-input "><option>年</option><option>月</option> <option>日</option><option selected>小时</option><option>分</option></select> </div></div><div class="text-calc"><div><span onclick="delok(this)" data-target="num" data-num="'+this.num+'" class="icon-sc"></span></div></div>';
                cankaoO.num=this.num
                this.dom.push(cankaoO)*/
            },
            xinzeng5:function () {
                /*this.num5=this.Guid()
                var cankao5='<div class="text-calc"><select class="zui-input"><option>xxxx</option><option>xxxx</option><option>xxxx</option><option>xxxx</option><option>xxxx</option></select></div><div class="text-calc"><input class="zui-input"/></div><div class="text-calc"><span onclick="delok(this)" data-target="num5" data-num="'+this.num5+'"  class="icon-sc"></span></div>';
                this.dom5.push(cankao5)*/
            	var yblxobj={
                		pddz:0,
                		pdgz:0
                		
                	};
                	this.wjzYblxList.push(yblxobj);
            },
            xinzeng6:function () {
                /*this.num6=this.Guid()
                var cankao6='<div class="text-calc"><select class="zui-input"><option>xxxx</option><option>xxxx</option><option>xxxx</option><option>xxxx</option><option>xxxx</option></select></div><div class="text-calc"><input class="zui-input"/></div><div class="text-calc"><input class="zui-input"/></div><div class="text-calc"><span onclick="delok(this)" data-num="'+this.num6+'" data-target="num6"  class="icon-sc"></span></div>'
                this.dom6.push(cankao6)*/
            	var yblxobj={
            		pddz:0,
            		pdgz:0
            		
            	};
            	this.wjzYblxList.push(yblxobj);
            },
            xinzeng8:function () {
            	var yblxobj={
                		pddz:0,
                		pdgz:0
                		
                	};
                	this.wjzYblxList.push(yblxobj);
            },
            xinzeng9:function () {
            	var yblxobj={
                		pddz:0,
                		pdgz:0
                		
                	};
                	this.wjzYblxList.push(yblxobj);
            },
            xinzeng10:function () {
            	var yblxobj={
                		pddz:0,
                		pdgz:0
                		
                	};
                	this.wjzYblxList.push(yblxobj);
            },
            xinzeng11:function () {
            	var yblxobj={
            			pddz:0,
            			pdgz:0
            			
            	};
            	this.wjzYblxList.push(yblxobj);
            },
            xinzengheader:function () {
                var nl='年龄段';
                this.pop10.push(nl);
                var clone=$('.clone')
                var dom='<div  style="min-width: 25%;max-width: 25%" class="text-calc dbremove" >'+this.arrDom+'</div>'
                for(var i=0;i<clone.length;i++){
                    $(clone).eq(i).append(dom)
                }
            },
            delHeader:function (n) {
                if(this.pop10.length<=2){
                    malert('最少保留两个','bottom','defeadted')
                }else{
                    this.pop10.splice(n,1)
                    var clone=$('.clone')
                    var dbremove=$('.dbremove')
                    for(var i=0;i<clone.length;i++){
                        $(clone).eq(i).find($(dbremove)).eq(n).remove()

                    }
                }

            },
            delOK:function (index,type,event) {
                switch (type){
                    case 'num':
                        $(event).parents('li').remove()
                        // for(var i=0;i<this.dom.length;i++){
                        //     if(index==this.dom[i].num){
                        //         this.dom.splice(i,1)
                        //     }
                        // }
                        break;
                    case 'num5':
                        $(event).parents('li').remove()
                        // this.dom5.splice(index,1)
                        // this.num5=this.num5-1;
                        break;
                    case 'num6':
                        $(event).parents('li').remove()
                        // this.dom6.splice(index,1)
                        // this.num6=this.num6-1;
                        break;
                    case 'num8':
                        $(event).parents('li').remove()
                        // this.dom8.splice(index,1)
                        // this.num8=this.num8-1
                        break;
                    case 'num9':
                        $(event).parents('li').remove()
                        // this.dom9.splice(index,1)
                        // this.num9=this.num9-1
                        break;
                    case 'num10':
                        if(($('.clone').length==1)){
                            malert('你不能全部删除','top','defeadted');
                        }else {
                            $(event).parent().parent().remove()
                            this.num10=this.num10-1
                        }
                        break;
                }

            },
            // 取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            changeChecked:function(data,type){
            	if('pddz'==type){
            		if(data=='0'){
            			brzcList.wjzObj.pddz='1';
            		}else{
            			brzcList.wjzObj.pddz='0';
            		}
            	}
            	if('pdgz'==type){
            		if(data=='0'){
            			brzcList.wjzObj.pdgz='1';
            		}else{
            			brzcList.wjzObj.pdgz='0';
            		}
            	}
            	
            },
            yblxChangeChecked:function(index,type){
            	if('pddz'==type){
            		var x=  this.wjzYblxList[index].pddz;
            		if(x=='0'){
            			this.wjzYblxList[index].pddz='1';
            		}else{
            			this.wjzYblxList[index].pddz='0';
            		}
            	}
            	if('pdgz'==type){
            		var y=  this.wjzYblxList[index].pdgz;
            		if(y=='0'){
            			this.wjzYblxList[index].pdgz='1';
            		}else{
            			this.wjzYblxList[index].pdgz='0';
            		}
            	}
            	
            },
            // 确定
            confirms:function () {
                var cklx=brzcList.wjzObj.zbbmModel.wjzpdfs;
                console.log("参考值："+cklx);
                if(cklx != '4' && cklx != '5' && cklx != '6'){
                	 $.getJSON("/actionDispatcher.do?reqUrl=LisXtwhWjz&types=update&yq=" + JSON.stringify(brzcList.wjzObj), function (json) {
                         if (json.a == 0) {
                         	$(".side-form-bg").removeClass('side-form-bg')
                             $(".side-form").addClass('ng-hide');
                         	malert("修改成功",'top','success');
                         } else {
                             malert("修改失败" + json.c,'top','defeadted');
                             return;
                         }
                     });
                }else{
                	for (var int = 0; int < brzcList.wjzYblxList.length; int++) {
						if(brzcList.wjzYblxList[int].ybbm==''){
							 malert("请选择样本类型",'top','defeadted');
                             return;
						}
						if(brzcList.wjzYblxList[int].pddz==''){
							brzcList.wjzYblxList[int].pddz='0';
						}
						if(brzcList.wjzYblxList[int].pdgz==''){
							brzcList.wjzYblxList[int].pdgz='0';
						}
						brzcList.wjzYblxList[int].zbbm=brzcList.wjzObj.zbbmModel.zbbm;
					}
                	console.log(brzcList.wjzYblxList);
                	if(brzcList.wjzYblxList.length==0){
                		var json={
                			zbbm:brzcList.wjzObj.zbbmModel.zbbm
                		};
                		brzcList.wjzYblxList.push(json);
                	}
                	var json='{"list":' + JSON.stringify(brzcList.wjzYblxList) + '}';
               	  	this.$http.post('/actionDispatcher.do?reqUrl=LisXtwhWjz&types=saveWjzYblx',json).then(
	                   function(data) {
	                	   console.log(json)
	                   		if(data.body.a==0){
	                            malert('保存结果成功','top','success');
	                   		}else{
	                   			malert('样本类型不能重复','top','defeadted');
	                   		}
	                    }, 
	                    function(error) {
	                    	malert(error,'top','success');
	                    });
                }
                
               
            },
            delYblx:function(data,type){
            	data.splice(type,1);
            }
        }
    });
    
    
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                malert('删除成功','top','success');
            },
            colse:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                malert('取消删除成功','top','defeadted');
            }

        }
    });
   
    var filter=new  Vue({
        el:'.filter',
        data:{
            isShow:false
        },
        methods:{
            baocun:function () {
                this.isShow=false;
                malert('保存成功','top','success');
            },
            guanbi:function () {
                this.isShow=false;
                malert('取消保存','top','defeadted ');
            }
        },
    })
function delok(event) {
    var n=$(event).attr('data-num')
    var type=$(event).attr('data-target')
    brzcList.delOK(n,type,event)

}


wrapper.queryAll();
wrapper.getYbbm();
