.iconfont.font-14{
    font-size: 14px;
}
.wrapper  {
    width: 100%;
}
.icon-iocn40:before{
    color: #ffffff;
}
.icon-iocn56:before{
    color: #1abc9c;
}
.left-user{
    display: flex;
    align-items: center;
    height: 36px;
}
.color-354052{
    font-size:14px;
    color:#354052;
}
.color-f2a654{
    color:#f2a654;
}
.left-user div{
    color:#7f8fa4;
}
.zui-table-view .zui-input{
    text-align: left;
    height: 28px;
    text-indent: 0;
}
.zui-input:focus{
    color: #1abc9c;
}
.pop-box{
    position: static;
}
.box-border{
    background:#ffffff;
    box-shadow:0 0 9px 0 rgba(1,23,18,0.40);
    border-radius:4px;
    padding-bottom: 8px;
    width:347px;
    margin-bottom: 8px;
}
.box-border .box-header{
    background:#fbfbfb;
    border:1px solid #eeeeee;
    border-radius:4px 4px 0 0;
    height:28px;
    font-size:14px;
    color:#333333;
    font-weight: bolder;
    padding-left: 13px;
    line-height: 28px;
}
.box-border .box-contnet{
    padding: 9px 13px 0 ;
}
.tabs-link .tabs-link-list.tabs-link-active{
    color:#1abc9c;
}
.ivu-tabs-ink-bar-active{
    background-color:#1abc9c
}
.color-1ab{
    color: #1abc9c;;
}
.color-fe3f3f{
    color: #fe3f3f;
}
.color-1024{
color:#1c2024;
}
.cm{
    background: #ffffff;
    height: 95%;
    color:#e96509 !important;
    width: 20px;
    right: 1px;
    line-height: 36px;
}
.mzsf-fyje {
    height: 60px;
    bottom: 16px;
    background: rgb(255, 255, 255);
    margin: 0px 10px;
    border-width: 1px;
    border-style: solid;
    border-color: rgb(233, 238, 230);
    border-image: initial;
}
.zui-table-view .zui-table-body::-webkit-scrollbar{
    width: 15px;
}
.height300{
    height: 350px;
    padding: 0;
    width: 840px;
    overflow: hidden;
    background-color: #fff;
}
#hyjl{
    height: 100%;
}
#wscjl{
height: 97%;
}
#yjs{
    height: 100%;
}
.zui-input{
    text-indent: 0;
}
.wscjl{
    height: 97%;
}
.tem{
    position: relative;
}
.item{
    position: absolute;
}
.bqcydj_model1{
    width: auto;
    padding: 14px 15px 33px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.yjs {
    overflow: scroll;
}
.hzgl-shanchu {
    width: 20px;
    height: 20px;
    cursor: pointer;
}
.hzgl-shanchu {
    width: 20px;
    height: 20px;
    cursor: pointer;
}
.hzgl-shanchu:before {
    position: absolute;
    top: -5px;
    content: '';
    background: #dfe3e9;
    width: 20px;
    left: -5px;
    height: 20px;
    z-index: 1;
    border-radius: 100%;
}

.hzgl-shanchu:after {
    position: absolute;
    top: -5px;
    content: '';
    background: #ffffff;
    width: 10px;
    left: -5px;
    height: 3px;
    z-index: 1;
    text-align: center;
    margin: 8px 5px;
}
.add-yp-hzgl {
    background: rgba(26, 188, 156, 0.07);
    border: 1px solid #1abc9c;
    border-radius: 4px;
    width: 28px;
    margin-top: -4px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    position: relative;
}
.add-yp-hzgl:before {
    position: absolute;
    content: '+';
    left: 17%;
    margin-top: -1px;
    color: #1abc9c;;
    font-size: 24px;

}
.block{
    display: inline-block;
}