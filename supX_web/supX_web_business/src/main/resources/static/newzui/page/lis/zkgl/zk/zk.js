(function () {
    var s = new Date().getTime();
    var pop = new Vue({
        el: '#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            isShowpopL: false,
            isShow: false,
            flag: false,
            msShow: '',
            list: {},
            detarr: [],
            title: '',
            centent: ''

        },
        methods: {
            delAll: function () {
                var jsondata = '';
                if (times.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            // right.getchilddata[i].id
                            this.list.id = times.getchilddata[i].id;
                            this.detarr.push(this.list);
                            jsondata = '{"list":' + JSON.stringify(this.detarr) + '}';
                        }
                    }
                } else {
                    jsondata = '{"list":' + JSON.stringify([this.list]) + '}';
                }
                var json = jsondata;
                this.$http.post('/actionDispatcher.do?reqUrl=ZkxtZkglDld&types=deleteZkjl', json).then(function (data) {
                        if (data.body.a == 0) {
                            times.getchilddata.splice(i, 1);
                            malert('删除成功', 'top', 'success');
                            pop.detarr = [];
                            pop.list = {};
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                            pop.detarr = [];
                            pop.list = {};
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            }
        }
    });
    var times = new Vue({
        el: '.l-zk-right',
        mixins: [dic_transform, baseFunc, tableBase, printer],
        data: {
            msShow: '',
            title: '',
            start: new Date().setDate(1),
            end: '',
            sqsj: '',
            zklx: '',
            xbmc: '',
            values:[],
            echart:{},
            lechart:[],
            sdtArr:[],//散点图
            spotArr:[],//折线图
            initSpotArr:[
                     [0, null,null],
                     [1, null,null],
                     [2, null,null],
                     [3, null,null],
                     [4, null,null],
                     [5, null,null],
                     [6, null,null],
                     [7, null,null],
                     [8, null,null],
                     [9, null,null],
                     [10, null,null],
                     [11, null,null],
                     [12, null,null],
                     [13, null,null],
                     [14, null,null],
                     [15, null,null],
                     [16, null,null],
                     [17, null,null],
                     [18, null,null],
                     [19, null,null],
                     [20, null,null],
                     [21, null,null],
                     [22, null,null],
                     [23, null,null],
                     [24, null,null],
                     [25, null,null],
                     [26, null,null],
                     [27, null,null],
                     [28, null,null],
                     [29, null,null],
                     [30, null,null],
                     [31, null,null]
                 ],
            yxrq: '',
            zkwph: '',
            zbdw: '',
            lielist: {},
            lielistArr: [],
            newLielist:[],
            jysbObj: {
                sbbm: '',
                zklx: '0',
                hostname: '',
            },
            jysb:{
            	
            },
            jgmc:'',
            ckzObj:{
            	xCkz:'',
            	sdCkz:'',
            	cvCkz:''
            },
            getchilddata: [],
            param: {
                time: '',
            },
            num: 0,
            arr: [],
            curMonthDays:'',
            arr1: [],
            arr001: [],
            sum:0,//本月靶值
            stddev:0,//标准差
            byxs:0,//变异系数
            hiclist:'',
            zkms:'',
            datatestarr:'',
            objlsit:[0,2,5,9,9,11,13],
        },
        created: function () {
            this.$nextTick(function () {
                // this.goChart(this.arr1);
                this.param.time = times.$options.filters['formDateMonth'](s);
                this.end = this.getCurrentMonthLast();

            })
        },
        filters: {
            formdatat:function (value) {
                var d = new Date(value);
                return d.getDate()
            },
            formDateMonth: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1);
            },
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            },
            formDatehanzi: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '年' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '月' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate()) + '日'
            },
        },
        methods: {
        	hqjg:function(){
        		var res={
        			sbbm:light.jysbObj.sbbm,
        			rq:times.param.time+'-01'
        		};
                 this.$http.post("/actionDispatcher.do?reqUrl=ZkxtZkglDld&types=queryAllZkjl", JSON.stringify(res)).then(function (data) {
                         console.log(data);
                         if (data.body.a == 0) {
                        	 malert('获取成功！', 'top', 'success');
                         }else{
                        	 malert('获取失败！', 'top', 'defeadted');
                         }
                 },function (error) {
                       malert(error, 'top', 'defeadted');
                });
        	},
            print:function () {
            	var headhtml = "<div>";
                var foothtml = "</div>";
                // 获取div中的html内容
            	$('.printHide').hide();
            	$('.yyyy').show();
                var newhtml = $('.printDiv')[0].innerHTML;
                
                // 给窗口界面重新赋值，赋自己拼接起来的html内容
                var div=document.createElement("div");
                div.setAttribute("class", "yyyy");
                div.innerHTML=headhtml + newhtml + foothtml;
                
                document.body.appendChild( div);
                window.print();
                
                $('.yyyy').remove();
                $('.printHide').show();
            	
                return false;
            },
            getCurrentMonthLast: function () {
                var date = new Date();
                var currentMonth = date.getMonth();
                var nextMonth = ++currentMonth;
                var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
                var oneDay = 1000 * 60 * 60 * 24;
                return new Date(nextMonthFirstDay - oneDay);
            },
            getechart:function () {
            	console.log("-------------------------");
            	console.log(times.spotArr)
                var arr1=this.arr001;
                var testarr=[];
                //var testdataarr=[];
                // var echart=this.echart;
                var sum=this.sum;
                var stddev=this.stddev;
                var num=0;
                var numarr=[];
               
             /* var stddev=times.stddev==0?1:times.stddev;
                   for(var i=0;i<this.arr1.length;i++){
                    for(var j=0;j<this.lechart.length;j++){
                        if(parseInt(times.$options.filters['formdatat'](this.lechart[j].rq))==this.arr1[i][0]){
                            this.arr1[i].splice(1,1,(this.lechart[j].jg-times.sum)/stddev);
                        }
                    }
                    }
                    if(this.lechart.length==0){
                        for(var i=0;i<this.arr1.length;i++){
                            this.arr1[i].splice(1,1,null);
                        }
                    }*/
                    //testarr=this.arr1;,#ff4532'#2885e2','#999ea7'
                var bordercolor=['#2885e2'];
                var index = Math.floor((Math.random()*bordercolor.length));
                
                var xCkz=times.ckzObj.xCkz;
            	var sdCkz=times.ckzObj.sdCkz;
            	var cvCkz=times.ckzObj.cvCkz;
            	//y轴共9根线  计算出其从下往上y坐标
            	var yArr=[]
            	//1  -4
            	yArr.push(parseFloat((xCkz+sdCkz*(-4)).toFixed(2)));
            	yArr.push(parseFloat((xCkz+sdCkz*(-3)).toFixed(2)));
            	yArr.push(parseFloat((xCkz+sdCkz*(-2)).toFixed(2)));
            	yArr.push(parseFloat((xCkz+sdCkz*(-1)).toFixed(2)));
            	yArr.push(parseFloat((xCkz+sdCkz*(0)).toFixed(2)));
            	yArr.push(parseFloat((xCkz+sdCkz*(1)).toFixed(2)));
            	yArr.push(parseFloat((xCkz+sdCkz*(2)).toFixed(2)));
            	yArr.push(parseFloat((xCkz+sdCkz*(3)).toFixed(2)));
            	yArr.push(parseFloat((xCkz+sdCkz*(4)).toFixed(2)));
            	this.datatestarr=JSON.parse(JSON.stringify(yArr)).reverse();
            	this.datatestarr[0]='';
            	this.datatestarr[8]='日期';
            	
                $('#container').highcharts({
                    chart: {
                        borderColor: '#030303',
                        borderRadius: 0,
                        borderWidth: 0.7,
                        type: 'line'
                    },
                    title: {
                        text: ''
                    },
                    legend: {
                        enabled: false
                    },
                    plotOptions: {
                    	line: {
                            // 配置在  line 里，对直线图有效，所以图中直线图没有鼠标跟踪行为
                           // enableMouseTracking: false
                        },
                        series: {
                            color: '#030303',
                            connectNulls: true
                        }
                    },
                    credits: {
                        enabled: false
                    },
                    /*tooltip: {
                        enabled: false
                    },*/
                    tooltip: {
                    	headerFormat: '<small>{point.key}日</small><br/>',
                    	pointFormat: '<b>值:{point.y}</b><br/>'
                    },
                    xAxis: {
                        tickPositions:arr1,
                        minorGridLineColor:'#666666',
                        minorGridLineDashStyle: 'longdash',
                        minorTickInterval: 'auto',
                        title: {
                            enabled: false,
                            text: '',
                        }
                    },
                    yAxis: {
                    	tickPositions:yArr,
                        gridLineColor:bordercolor[index],
                        title: {
                            enabled: false,
                            text: '',
                        },
                        labels: {
                            formatter:function (value,idnex) {
                                num=num+1;
                                numarr.push(num);
                               
                                var i = parseFloat(((this.value - xCkz)/sdCkz).toFixed(2)); 
                                if (i==0) {
                                	return 'X';
								}else if(i==-4 || i==4){
									return '';
								}else{
									return i+'SD';
								}

                            }
                        }
                    },
                    series: [{
                    	data: times.spotArr,
                        lineWidth: 1.5,
                        marker: {
                            radius: 3
                        }
                    },{
                        type: 'scatter',
                        data: times.sdtArr,
                        id: 's1',
                        connectEnds: false,
                        color:'#ff4532',
                        //enableMouseTracking:false,
                        marker: {
                        	fillColor: 'red',
                            radius: 4
                        }
                    }]
                }, function (chart) { // on complete
                	console.log("→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→")
                	var yArr=[];
                	var pds=[];//装连续升降的点 长度5
                	var type='UP'; //状态 UP为升  DOWN为降
                    var points = chart.series[0].points;
                    console.log(points);
                    Highcharts.each(points, function(pd,index) {
                    	if(pd.y != null ){
                    		var ty='';
                        	//将连续升降大于等于5个点的数据选出来
                        	//当前参考靶值
                        	var xCkz=times.ckzObj.xCkz;
                        	if(pd.y>xCkz){
                        		ty='UP';
                        	}
                        	if(pd.y<xCkz){
                        		ty='DOWN';
                        	}
                        	
                        	if(pds.length > 0 && pds.length <5 && type != ty){
                        		pds=[];
                        	}
                        	
                        	if(pds.length==0 || pds.length==5){
                        		if(pds.length==5){
                            		yArr.push(pds);
                            	}
                        		pds=[];
                        		if(pd.y != null){
                        			pds.push(pd);
                        		}
                        		type=ty;
                        	}else{
                        		if(pds.length<5 && type==ty){
                        			if(pd.y != null){
                            			pds.push(pd);
                            		}
                            	}else{
                            		pds=[];
                            	}
                        	}
                        	
                        	
                    	}
                    });
                    console.log(yArr);
                    //画框框,计算方形四点坐标
                    
                    //偏移值
                    var offsetLeft=parseFloat(chart.yAxis[0].left-8);
                    var offsetRight=parseFloat(chart.yAxis[0].right+4);
                    console.log(offsetLeft+"---"+offsetRight)
                    if(yArr.length>0){
                    	for (var i = 0; i < yArr.length; i++) {
							var leftX=parseFloat((yArr[i][0].plotX+offsetLeft).toFixed(2));
							var rightX=parseFloat((yArr[i][4].plotX+offsetLeft+14).toFixed(2));
							//计算最高点和最低点
							var yL=[];
							for (var j = 0; j < yArr[i].length; j++) {
								yL.push(parseFloat((yArr[i][j].plotY+offsetRight).toFixed(2)));
							}
							yL.sort(function(a, b){
							    return a - b;
							});
							
							var topY=yL[0]-12;
							var downY=yL[4]+4;
							chart.renderer.rect(leftX, topY, rightX-leftX, downY-topY, 5)
				            .attr({
				            'stroke-width': 1,
				            stroke: 'red',
				            //fill: '',
				            zIndex: 3
				            }).add();
							
							//var att="M "+leftX+" "+topY+" L "+leftX+" "+downY+" "+rightX+" "+downY+" "+rightX+" "+topY+" z";
							//console.log(att);
							//画框框
							/*var axis = chart.xAxis[0];
							axis.addPlotBand({
							   id: 'ydg'+i,     // id 用于后续删除用
							   color: '#FCFFC5',
		                       borderColor:'#FF0000',
		                       borderWidth:1,
							   from: yArr[i][0].x,
							  to: yArr[i][4].x,
							   // ...
							});
							
							//$('.highcharts-plot-band').setattribute("d",att);
							console.log("============")
							console.log($('.highcharts-plot-band')[i].attributes.d.nodeValue);
							$('.highcharts-plot-band')[i].attributes.d.nodeValue=att;*/
						}
                    }
                    console.log("→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→→")
                 console.log(  chart)
                });
             this.hiclist=numarr;
            //this.datatestarr=testdataarr;
            console.log(this.datatestarr);
            console.log($('.highcharts-plot-band'));
            },

            getData: function () {
                var d = new Date();
                this.arr1=[]
                this.arr001=[]
                this.curMonthDays = new Date(d.getFullYear(), (d.getMonth() + 1), 0).getDate();
                // var curMonthDays = 10;
                for (var i = 1; i < this.curMonthDays+1; i++) {
                    this.arr.push(i,null);
                    this.arr001.push(i);
                    this.arr1.push(this.arr);
                    this.arr = []
                }
            },
            //确定删除
            sc: function (i, id, zbmc) {
                pop.title = '删除质控记录';
                pop.centent = '确定删除质控记录：' + zbmc + ' 吗？';
                pop.isShow = true;
                pop.isShowpopL = true;
                pop.list.id = id;

            },
            scall: function () {
                if (this.isChecked.length > 0) {
                    pop.isShow = true;
                    pop.centent = '确定删除多条质控记录吗？';
                    pop.title = '删除质控记录';
                } else {
                    malert('请选择需要删除的质控记录', 'top', 'defeadted');
                }
            },

            // //编辑
            // bj:function (item,type,id) {
            //     closees.num=1;
            //     closees.type=type;
            //     $("#isFold").addClass('side-form-bg');
            //     closees.datalist=item==''?this.getchilddata[0]:item;
            //     if(item==''){
            //         closees.datalist.zfbz=this.getchilddata[0].zfbz=0
            //     }
            //     closees.datalist.zfbz=closees.datalist.zfbz==0?false:true
            // },
            //结果录入
            jglr: function (i, l, y) {
                closees.type = y
                closees.datalist = light.zklist[0]
                closees.datalist.yxrq = this.$options.filters['formDate'](closees.datalist.yxrq)
                closees.msShow = 1;
                closees.title = '单规则结果录入';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');


            },
            //图形表格切换
            tabBg: function (i) {
                this.num = i;
                if (i == 1) {
                    $(".fieldlist").hide();
                }
            },
            //双击编辑
            slideEdit: function (item, id, t) {
            	console.log(item)
                closees.type = t;
                closees.datalist = item
                closees.datalist.yxrq = this.$options.filters['formDate'](closees.datalist.yxrq)
                closees.datalist.rq = this.$options.filters['formDate'](closees.datalist.rq)
                closees.msShow = 1;
                closees.title = '单规则结果编辑';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                closees.datalist = item == '' ? this.getchilddata[0] : item;
                if (item == '') {
                    closees.datalist.zfbz = this.getchilddata[0].zfbz = 0
                }
                closees.datalist.zfbz = closees.datalist.zfbz == 0 ? false : true
            },
            //


        },
        watch: {
            "jysbObj.zklx": function (old, newvalue) {
                light.choices(light.list == '' ? light.jysbList[0] : light.list);
            },
            "param.time":function(){
            	light.choices(light.list == '' ? light.jysbList[0] : light.list);
            }
        }
    });
    var light = new Vue({
        el: '.l-zk-left',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            msShow: '',
            title: '',
            jysbList: '',
            zklist: '',
            zklx: '',
            sqsj: '',
            time: '',
            getchilddata: [],
            jysbObj: {
                sbbm: '',
                zklx: '',
                zkwph:''
            },

        },
        created: function () {
            this.jysb();

        },
        methods: {
            selectdata: function (value) {
                times.jysbObj.sbbm = value[0];
                times.jysbObj.text = value[4];
            },
            miaoshu: function (list) {
                closees.msShow = 0;
                closees.title = '质控描述';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
            },

            jysb: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    if (json.a == 0) {
                        light.jysbList = json.d.list;
                        light.jysbObj.sbbm = json.d.list[0].sbbm;
                        times.jysb=json.d.list[0];
                        light.getSbbm();
                        //light.choices(json.d.list[0]);
                    } else {
                        malert("获取申请检验设备失败" + json.c);
                        return false;
                    }

                });
            },
            getSbbm: function () {
                // console.log(JSON.stringify(this.jysbObj));
                $.getJSON("/actionDispatcher.do?reqUrl=ZkxtZkglDld&types=queryXmyb&parm=" + JSON.stringify(light.jysbObj), function (json) {
                    //  console.log(json);
                    if (json.a == 0) {
                        light.zklist = json.d.list;
                        // console.log(JSON.stringify(json)+"获取");
                    }
                    else {
                        malert("检验设备获取失败" + JSON.stringify(json).c);
                        return false;
                    }
                });
            },
            //质控物批号改变
            // show: function (a) {
            //     $.getJSON("/actionDispatcher.do?reqUrl=ZkxtZkglDld&types=queryXmyb&parm=" + JSON.stringify({zkwph: a}), function (json) {
            //         if (json.a == 0) {
            //             light.zklist = json.d.list;
            //             // console.log(JSON.stringify(json)+"获取");
            //         }
            //         else {
            //             malert("获取失败" + JSON.stringify(json).c);
            //             return false;
            //         }
            //     })
            // },
            //获取当前
            choices: function (list) {
            	if(list== undefined){
            		return;
            	}
                closees.datalist = list
                this.list = list
                times.xbmc = list.zbmc
                times.zbdw = list.zbdw
                times.zkwph = list.zkwph
                times.yxrq = list.yxrq
                console.log(times.param.time);
                list.rq = times.param.time+'-01';
                list.zklx = times.jysbObj.zklx;
                if(times.jysbObj.zklx=='0'){
                	times.ckzObj.xCkz=list.x;
                    times.ckzObj.sdCkz=list.sd;
                    times.ckzObj.cvCkz=list.cv;
                }
                if(times.jysbObj.zklx=='1'){
                	times.ckzObj.xCkz=list.x1;
                    times.ckzObj.sdCkz=list.sd1;
                    times.ckzObj.cvCkz=list.cv1;
                }
                if(times.jysbObj.zklx=='2'){
                	times.ckzObj.xCkz=list.x2;
                    times.ckzObj.sdCkz=list.sd2;
                    times.ckzObj.cvCkz=list.cv2;
                }
                // list.zklx="0";
                this.$http.post("/actionDispatcher.do?reqUrl=ZkxtZkglDld&types=queryAllZkjl", JSON.stringify(list)).then(function (data) {
                        console.log(data);
                        if (data.body.a == 0) {
                            times.lechart=[];
                            times.lielistArr=[];
                            times.newLielist=[];
                            
                          //装点的数组
                            var str=JSON.stringify(times.initSpotArr);
                            times.spotArr=JSON.parse(str);
                            
                            times.getchilddata = data.body.d.allList;
                            
                            if(data.body.d.allList.length>0){
                            	times.jgmc=data.body.d.allList[0].jgmc;
                            }
                            
                            //计算平均数据  start
                            var sum=0;//本月靶值
                            var stddev=0;//标准差
                            var byxs=0;//变异系数
                            
                            for (var i = 0; i < times.getchilddata.length; i++) {
                            	//结果列表
                                times.lielist.rq = times.getchilddata[i].rq;
                                times.lielist.jg = times.getchilddata[i].jg;
                                times.lielist.czyxm = times.getchilddata[i].czyxm;
                                times.lielistArr.push(JSON.parse(JSON.stringify(times.lielist)));
                                
                                times.values.push( times.getchilddata[i].x);
                                
                                times.echart.jg=times.getchilddata[i].jg;
                                times.echart.rq=times.getchilddata[i].rq;
                                times.lechart.push(times.echart);
                                
                                sum+=times.getchilddata[i].jg;
                            }
                            //重新组装测定结果数组
                            console.log("----------------------------")
                            console.log(times.lielistArr);
                            for (var x = 0; x < times.lielistArr.length; x++) {
								var pd={
										rq:'',
										jg:'',
										czyxm:'',
										rq1:'',
										jg1:'',
										czyxm1:'',
										rq2:'',
										jg2:'',
										czyxm2:'',
								};
								//向下取整
								var dow=Math.floor(times.lielistArr.length/3);
								var ys=times.lielistArr.length%3;
								console.log(dow + "::" + ys);
								if (dow<1) {
									if(ys==1){
										pd.rq=times.lielistArr[x].rq;
										pd.jg=times.lielistArr[x].jg;
										pd.czyxm=times.lielistArr[x].czyxm;
									}
									if(ys==2){
										pd.rq=times.lielistArr[x].rq;
										pd.jg=times.lielistArr[x].jg;
										pd.czyxm=times.lielistArr[x].czyxm;
										pd.rq1=times.lielistArr[x+1].rq;
										pd.jg1=times.lielistArr[x+1].jg;
										pd.czyxm1=times.lielistArr[x+1].czyxm;
									}
								}else{
									if(times.lielistArr[x] != undefined){
										pd.rq=times.lielistArr[x].rq;
										pd.jg=times.lielistArr[x].jg;
										pd.czyxm=times.lielistArr[x].czyxm;
									}
									if(times.lielistArr[x+1] != undefined){
										pd.rq1=times.lielistArr[x+1].rq;
										pd.jg1=times.lielistArr[x+1].jg;
										pd.czyxm1=times.lielistArr[x+1].czyxm;
									}
									if(times.lielistArr[x+2] != undefined){
										pd.rq2=times.lielistArr[x+2].rq;
										pd.jg2=times.lielistArr[x+2].jg;
										pd.czyxm2=times.lielistArr[x+2].czyxm;
									}
								}
								times.newLielist.push(pd);
								x+=2;
								
							}
                            console.log(times.newLielist);
                            
                            times.sum=parseFloat((sum/times.getchilddata.length).toFixed(2));//本月靶值
                            //计算标准差
                            for (var i = 0; i < times.getchilddata.length; i++) {
                            	stddev+=(times.getchilddata[i].jg-times.sum)*(times.getchilddata[i].jg-times.sum)
                            }
                            times.stddev=parseFloat((Math.sqrt(stddev/(times.getchilddata.length-1))).toFixed(2));
                            times.byxs=parseFloat((times.stddev/times.sum).toFixed(2));
                          //计算平均数据  end_____________
                            
                            //折线图数据
                            var zxtD=data.body.d.zxtList;
                            for (var int = 0; int < zxtD.length; int++) {
                            	//组装表中点数组
                            	var d=[];
                            	var dat = new Date(zxtD[int].rq);
                            	var t=parseInt(dat.getDate() < 10 ? '0' + dat.getDate() : dat.getDate());
                            	d.push(t);
                            	d.push(zxtD[int].jg);
                            	d.push(zxtD[int].czyxm);
                            	times.spotArr[t]=d;
							}
                            //散点图数据
                            times.sdtArr=[];
                            var sdtD=data.body.d.sdtList;
                            for (var j = 0; j < sdtD.length; j++) {
                            	//组装表中点数组
                            	var d=[];
                            	var dat = new Date(sdtD[j].rq);
                            	var t=parseInt(dat.getDate() < 10 ? '0' + dat.getDate() : dat.getDate());
                            	d.push(t);
                            	d.push(sdtD[j].jg);
                            	d.push(sdtD[j].czyxm);
                            	times.sdtArr.push(d);
							}
                            
                            light.$nextTick(function () {
                                times.getData();
                              times.getechart();
                          })
                        } else {
                            malert(error, 'top', 'defeadted');
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });

            },

        },
        watch: {
            "jysbObj.sbbm": function () {
            	for (var int = 0; int < light.jysbList.length; int++) {
            		if(light.jysbObj.sbbm==light.jysbList[int].sbbm){
            			times.jysb=light.jysbList[int];
            		}
				}
                light.getSbbm();
            },

        }
    });
    var closees = new Vue({
        el: '#brzcList',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            msShow: '',
            title: '',
            type: '',
            datalist: {},
            jysbObj: {
                sbbm: '',
                zklx: '0',
            },
        },
        created: function () {
            this.$nextTick(function () {
                this.getEdit()
            })
        },
        methods: {
            AddClose: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            //侧滑确定按钮
            lightOk: function () {
                this.datalist.zfbz = this.datalist.zfbz == false ? 0 : 1
                // var json='{"list":' + JSON.stringify(list) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=ZkxtZkglDld&types=updateZkjl', JSON.stringify(this.datalist)).then(
                    function (data) {
                        console.log(data);
                        if (data.body.a == 0) {
                            malert(data.body.c, 'top', 'success');
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            //获取信息
            getEdit: function () {


            },
            //录入
            jgOk: function () {
                if (this.datalist.rq == null) {
                    malert('检测日期不能为空', 'top', 'defeadted');
                } else {
                    var url = this.type == 'top' ? '/actionDispatcher.do?reqUrl=ZkxtZkglDld&types=insertZkjl' : '/actionDispatcher.do?reqUrl=ZkxtZkglDld&types=updateZkjl';
                    this.datalist.zfbz = this.datalist.zfbz == true ? 1 : 0;
                    this.datalist.bjsjg = this.datalist.bjsjg == true ? 1 : 0;
                    this.datalist.yxrq = new Date(this.datalist.yxrq).getTime()
                    this.datalist.rq = new Date(this.datalist.rq).getTime()
                    this.$http.post(url, JSON.stringify(this.datalist)).then(
                        function (data) {
                            console.log(data);
                            if (data.body.a == 0) {
                                malert(data.body.c, 'top', 'success');
                            } else {
                                malert(data.body.c, 'top', 'defeadted');
                            }
                        })
                    $(".side-form-bg").removeClass('side-form-bg')
                    $(".side-form").addClass('ng-hide');
                }
            }
        }
    });

    laydate.render({
        elem: '.todate',
        eventElem: '.zui-date i.datenox',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM',
        type:'month',
        done: function (value, data) {
            times.param.time = value
        }
    });
    laydate.render({
        elem: '.l-time',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
    laydate.render({
        elem: '.l-times',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
            closees.datalist.rq = value
        }
    });
    laydate.render({
        elem: '.l-time1',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
            times.datalist.yxrq = value
        }
    });
    laydate.render({
        elem: '.l-times2',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
            times.datalist.djrq = value
        }
    });


})()