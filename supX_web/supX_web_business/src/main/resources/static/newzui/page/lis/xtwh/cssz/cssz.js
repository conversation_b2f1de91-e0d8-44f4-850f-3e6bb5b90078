(function () {
    $("#jyxm_icon").uitable();
    var background = new Vue({
        el: '#jyxm_icon',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            num: 0,
            popContent:{},
            objList: [
                {
                    id: 001,
                    name: '周丽君',
                    tpye: '消化科',
                    aduser: '周丽君',
                    adtype: '高值',
                    adtypeO: '高值',
                    adyb: '高值',
                    adnum: '88888'
                },
                {
                    id: 001,
                    name: '周丽君',
                    tpye: '消化科',
                    aduser: '周丽君',
                    adtype: '高值',
                    adtypeO: '高值',
                    adyb: '高值',
                    adnum: '88888'
                },
                {
                    id: 001,
                    name: '周丽君',
                    tpye: '消化科',
                    aduser: '周丽君',
                    adtype: '高值',
                    adtypeO: '高值',
                    adyb: '高值',
                    adnum: '88888'
                },
                {
                    id: 001,
                    name: '周丽君',
                    tpye: '消化科',
                    aduser: '周丽君',
                    adtype: '高值',
                    adtypeO: '高值',
                    adyb: '高值',
                    adnum: '88888'
                },
                {
                    id: 001,
                    name: '周丽君',
                    tpye: '消化科',
                    aduser: '周丽君',
                    adtype: '高值',
                    adtypeO: '高值',
                    adyb: '高值',
                    adnum: '88888'
                }
            ]
        },
        methods: {
            silde: function (i) {
                this.num = i
            }
        }
    })
})()