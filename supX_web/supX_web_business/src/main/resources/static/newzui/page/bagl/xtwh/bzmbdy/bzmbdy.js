    var wrapper = new Vue( {
        el: '.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            isShowpopL: false,
            isTabelShow: false,
            isShow: false,
            title: '',
            totle: '',
            num: 0,
            mxShow:false,
            ypShow:false,
            popContent:{},
            fylbList:[],//费用类别下拉框
            balbList: [], //病案类别
            ypzlList: [], //药品种类
            fylb: null, //费用类别值
            balb: null,   //病案类别值
            param: {
                page: '',
                rows: '',
                total: ''
            }
        },
        methods: {
            //刷新
            sx: function () {
                yjkmtableInfo.getData();


            },
            //保存病案科室
            save: function () {
                yjkmtableInfo.saveData();
            },
            //下拉框费用类别加载
            fylbSelect: function(){
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=fybm&dg=",function (json) {
                    wrapper.fylbList = json.d.list;
                    yjkmtableInfo.fylbList = json.d.list;
                });
            },
            //下拉框病案类别加载
            balbSelect: function(){
                var json={
                    zylb: "08"
                }
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyzbm&dg="+"&json="+JSON.stringify(json),function (json) {
                    wrapper.balbList = json.d.list;
                    yjkmtableInfo.balbList = json.d.list;
                });
            },
            //下拉框药品种类加载
          	ypzlSelect: function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl&dg=",function (json) {
       			 	wrapper.ypzlList = json.d.list;
                });
          	},
            resultChangeBg:function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "lbbm":
                        Vue.set(this.popContent, 'lbbm', val[0]);
                        Vue.set(this.popContent, 'lbmc', val[4]);
                        break;
                    case "zybm":
                        Vue.set(this.popContent, 'zybm', val[0]);
                        Vue.set(this.popContent, 'zymc', val[4]);
                        break;
                    case "ypzlbm":
                        Vue.set(this.popContent, 'ypzlbm', val[0]);
                        Vue.set(this.popContent, 'ypzlmc', val[4]);
                        break;
                    default:
                        break;
                }
                yjkmtableInfo.getData();
            }

        }
    });

    //科目
    var yjkmtableInfo = new Vue({
        el: '.fyxm-table',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            num:0,
            popContent: {},
            jsonList: [],
            ksList: [],
            baksList: [],
            balbList:[],
            edits: [],
            baksContent:{}, //病案科室对象
            totlePage:''
        },
        updated:function () {
            changeWin()
        },
        methods: {
            checkSelectZz: function (val, event, type) {
                var _lebal = $('label');
                for (var i = 0; i < _lebal.length; i++) {
                    if (event.toElement == _lebal[i]) {
                        return
                    }
                }
                if (val[1] == 'some') {
                } else if (val[1] == 'one') {

                } else if (val[1] == 'all') {

                    if (type == undefined) {

                    }
                }
            },
            //进入页面加载列表信息
            //切换
            tabBg: function (index) {
                this.num = index;
                if(index==1){
                    wrapper.mxShow=true;
                    wrapper.ypShow=false;
                }else if(index==2){
                    wrapper.mxShow=false;
                    wrapper.ypShow=true;
                }else{
                	wrapper.mxShow=false;
                    wrapper.ypShow=false;
                }
                $("#jsvalue").val('');
                this.param.page = 1;
                this.getData();
                this.isChecked = [];
                this.isCheckedall = false;
                setTimeout(function () {
                    changeWin();
                    changHeight()
                },30)
            },
            getData: function () {
                common.openloading('.fyxm-size')
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }
                switch (this.num) {
                    case 0:
                        //this.param.rows=10;
                        var json={
                            tybz:0
                        };
                        $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmFylb&types=query&dg="+JSON.stringify(this.param)+"&json="+JSON.stringify(json),function (json) {
                            if(json.d!=null){
                                yjkmtableInfo.jsonList = json.d.list;
                                yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                                yjkmtableInfo.isCheckAll = false;
                                yjkmtableInfo.checkAll();//调用全选
                            }
                        });
                        break;
                    case 1:
                        var json={
                            tybz:0,
                            lbbm:wrapper.popContent.lbbm,
                            balb:wrapper.popContent.zybm
                        };
                        $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmMxfyxm&types=query&dg="+JSON.stringify(this.param)+"&json="+JSON.stringify(json),function (json) {
                            if(json.d!=null){
                                yjkmtableInfo.jsonList = json.d.list;
                                yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                                yjkmtableInfo.isCheckAll = false;
                                yjkmtableInfo.checkAll();//调用全选
                            }
                        });
                        break;
                    case 2:
                        var json={
                            tybz:0,
                            zlbm:wrapper.popContent.ypzlbm,
                            balb:wrapper.popContent.zybm
                        };
                        $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=query&dg="+JSON.stringify(this.param)+"&json="+JSON.stringify(json),function (json) {
                            if(json.d!=null){
                                yjkmtableInfo.jsonList = json.d.list;
                                yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                                yjkmtableInfo.isCheckAll = false;
                                yjkmtableInfo.checkAll();//调用全选
                            }
                        });
                        break;
                }
                common.closeLoading()
            },

            //保存
            saveData: function () {
                switch (yjkmtableInfo.num){
                    case 0:
                        var fylbList = [];
                        for (var i = 0; i < this.isChecked.length; i++) {
                            if (this.isChecked[i] == true) {
                                fylbList.push(this.jsonList[i]);
                            }
                        }
                        var json = '{"list":'+JSON.stringify(fylbList)+'}';
                        this.$http.post('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmFylb&types=updateBetch&',
                            json).then(function (data) {
                            if(data.body.a == 0){
                                malert("保存成功");
                                yjkmtableInfo.getData();
                            } else {
                                malert("保存失败");
                            }
                        }, function (error) {
                            console.log(error);
                        });
                        break;
                    case 1:
                    	if(wrapper.popContent.zybm&&wrapper.popContent.lbbm){
                    		var parm={
                    			lbbm:wrapper.popContent.lbbm,
                    			balb:wrapper.popContent.zybm
                    		}
                    		var json = JSON.stringify(parm);
	                        this.$http.post('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmMxfyxm&types=updateByFylb&',
	                            json).then(function (data) {
	                            if(data.body.a == 0){
	                                malert("保存成功");
	                                wrapper.popContent={};
	                                yjkmtableInfo.getData();
	                            } else {
	                                malert("保存失败");
	                            }
	                        }, function (error) {
	                            console.log(error);
	                        });
                    	}else{
                    		var mxfyList = [];
                        for (var i = 0; i < this.isChecked.length; i++) {
	                            if (this.isChecked[i] == true) {
	                                mxfyList.push(this.jsonList[i]);
	                            }
	                        }
	                        var json = '{"list":'+JSON.stringify(mxfyList)+'}';
	                        this.$http.post('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmMxfyxm&types=updateBetch&',
	                            json).then(function (data) {
	                            if(data.body.a == 0){
	                                malert("保存成功");
	                                yjkmtableInfo.getData();
	                            } else {
	                                malert("保存失败");
	                            }
	                        }, function (error) {
	                            console.log(error);
	                        });
                    	}
                        break;
                    case 2:
                    	if(wrapper.popContent.zybm&&wrapper.popContent.ypzlbm){
                    		var parm={
                    			zlbm:wrapper.popContent.ypzlbm,
                    			balb:wrapper.popContent.zybm
                    		}
                    		var json = JSON.stringify(parm);
	                        this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=updateByYpzl&',
	                            json).then(function (data) {
	                            if(data.body.a == 0){
	                                malert("保存成功");
	                                yjkmtableInfo.getData();
	                                wrapper.popContent={};
	                            } else {
	                                malert("保存失败");
	                            }
	                        }, function (error) {
	                            console.log(error);
	                        });
                    	}else{
                    		var ypList = [];
	                        for (var i = 0; i < this.isChecked.length; i++) {
	                            if (this.isChecked[i] == true) {
	                                ypList.push(this.jsonList[i]);
	                            }
	                        }
	                        var json = '{"list":'+JSON.stringify(ypList)+'}';
	                        this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=updateBetch&',
	                            json).then(function (data) {
	                            if(data.body.a == 0){
	                                malert("保存成功");
	                                yjkmtableInfo.getData();
	                            } else {
	                                malert("保存失败");
	                            }
	                        }, function (error) {
	                            console.log(error);
	                        });
                    	}
                        break;
                }
            },

            //设置框
            remove: function(){
                this.balb=null;
                this.fylb=null;
            },

            //设置按钮
            szDate: function(){
                var json = {
                    lbbm : this.fylb,
                    balb : this.balb
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmMxfyxm&types=updateByFylb&',
                    JSON.stringify(json)).then(function (data) {
                    if(data.body.a == 0){
                        malert("设置成功");
                        yjkmtableInfo.getData();
                    } else {
                        malert("设置失败");
                    }
                }, function (error) {
                    console.log(error);
                });
            },
        },


    });

    yjkmtableInfo.getData();
    wrapper.fylbSelect();
    wrapper.balbSelect();
    wrapper.ypzlSelect();




