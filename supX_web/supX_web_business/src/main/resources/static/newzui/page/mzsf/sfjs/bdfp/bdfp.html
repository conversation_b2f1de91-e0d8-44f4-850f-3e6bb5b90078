<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>补打发票</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="rydj.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="skin-default">
<div id="printPage"></div>
<div class="printArea printShow"></div>
<div class="wrapper  printHide">
    <div class="panel" id="panel" v-cloak>
        <div class="toolMenu">
                <div class="tong-top">
                    <button class="tong-btn btn-parmary icon-zhixing-b" @click="goToPage(1)">查询</button>
                    <button class="tong-btn btn-parmary-b bdfp paddr-r5" @click="somePrint">补打发票</button>
                    <button class="tong-btn btn-parmary-b bdfp paddr-r5" @click="someEBill">补开电子发票</button>
                    <button class="tong-btn btn-parmary-b bdfp paddr-r5" @click="printEBill">打印电子发票</button>
					<button class="tong-btn btn-parmary-b bdfp paddr-r5" @click="bdxp">补打小票</button>
                    <!--
					<button class="tong-btn btn-parmary-b bdfp paddr-r5" @click="somePrintNh">补打农合补偿单</button>
					<button class="tong-btn btn-parmary-b bdfp paddr-r5" @click="someDbPrintNh">补打大病补偿单</button>
                    <button class="tong-btn btn-parmary-b bdfp paddr-r5" @click="print55_014">医保详单打印</button>
                    <button class="tong-btn btn-parmary-b bdfp paddr-r5" @click="print44_014">明细分割信息打印</button>
					-->
                </div>
            <!--检索字段begin-->
            <div class="flex-container padd-b-10 padd-t-10" >
                    <div class="flex-container flex-align-c  margin-l-20">
                        <span class="whiteSpace margin-r-5 ft-14">时间段</span>
                        <div class="position margin-l13 flex-container flex-align-c">
                            <input class="zui-input todate wh182 " placeholder="不限定时间范围" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input todate wh182 " placeholder="请选择处方结束时间" id="timeVal1" />
                        </div>
                    </div>
                    <div class="flex-container flex-align-c   margin-l-20">
                        <span class="whiteSpace margin-r-5 ft-14">发票号</span>
                            <input class="zui-input wh150" placeholder="发票号" type="text"
                                   @keydown.enter="goToPage(1)" v-model="param.fphm" id="jsfph"/>
                    </div>
                    <div class="flex-container flex-align-c  margin-l-20">
                        <span class="whiteSpace margin-r-5 ft-14">姓名</span>
                            <input class="zui-input wh120" placeholder="姓名" type="text"
                                   @keydown.enter="goToPage(1)"  v-model="param.brxm" id="jsname"/>
                    </div>
                <div class="flex-container flex-align-c  margin-l-20">
                        <span class="whiteSpace margin-r-5 ft-14">操作员</span>
                    <select-input class="wh122" @change-data="selectCzyBz" :not_empty="false"
                                  :child="czy_tran" :index="czyBz" :val="czyBz"
                                  :name="'czyBz'">
                    </select-input>
                    </div>
            </div>
        </div>

    <!--表格区-->
        <div class="zui-table-view padd-l-10 padd-r-10">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                                <input-checkbox @result="reCheckBoxBd" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l">挂号序号</div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s">病人姓名</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">保险类别</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l">发票号码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">组合费用</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l text-left">费用项目</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">单价</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">数量</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">金额</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">收费日期</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">医嘱序号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">收费窗口</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">收费员</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">门诊医生</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">门诊科室</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">优惠比例</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">优惠金额</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">门诊诊断</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">备注</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">收费类型</div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-l">结算id</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">保险结算号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">银行卡支付</div>
                        </th>
                    </tr>

                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr :data-index="$index" v-for="(item, $index) in jsonList"  @click="checkSelectBd([$index,'some','jsonList'],$event)" @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex},{'tableTr': $index%2 == 0}]">
                        <td class="cell-m">
                                <input-checkbox @result="reCheckBoxBd" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                        </td>
                        <td class="cell-l">
                            <div class="zui-table-cell cell-l" v-text="item.ryghxh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.rybxlbmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell title cell-l" v-text="item.fphm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s title" style="overflow: hidden" v-text="item.zhfymc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l title text-left text-over-2"  v-text="item.mxfyxmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fydj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fysl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fyje"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="fDate(item.sfsj,'datetime')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yzxh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ywckmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.czyxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.mzysxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.mzksmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yhbl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yhje"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.rymzzd"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.bzsm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fylbmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.ryjsjlid"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.bxjsh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.bankzfls"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div  class="zui-table-fixed table-fixed-l padd-l-10" v-if="jsonList.length!=0"> <!-- 有浮动就加 table-fixed-r -->
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th  class="cell-l">
                                <div class="zui-table-cell cell-l"><span>挂号序号</span>
                                    <em></em></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body " @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @click="checkSelectBd([$index,'some','jsonList'],$event)" @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            class="tableTr2 table-hovers-filexd-l">
                            <td class="cell-m text-center">
                                <input-checkbox @result="reCheckBoxBd" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td >
                                <div  class="zui-table-cell cell-l">{{item.ryghxh}}</div>
                            </td>
                        </tr>
                        </tbody>

                    </table>
                </div>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
        <div id="pop">
            <div class="pophide" :class="{'show':isShowpopL}"></div>
            <div class="zui-form podrag bcsz-layer " style="height: max-content;padding-bottom: 20px"
                 :class="{'show':isShow}">
                <div class="layui-layer-title ">{{title}}</div>
                <span class="layui-layer-setwin" @click="isShow = false,isShowpopL=false"><a
                        class="layui-layer-ico layui-layer-close layui-layer-close1" href="javascript:"></a></span>
                <div class="layui-layer-content">
                    <div class="">
                        <div class="zui-inline " style="margin: 20px 20px 20px 110px;padding-left: 117px">
                            <label class="zui-form-label" style="width: auto">是否占用新的票号</label>
                            <select-input @change-data="resultChangeItem"
                                          :child="cardList" :index="'bdfp'" :index_val="'bdfpnum'"
                                          :val="popContent.bdfpnum"
                                          :name="'popContent.bdfp'" :search="false">
                            </select-input>
                        </div>
                    </div>
                    <div class="zui-row buttonbox">
                        <button class="zui-btn btn-primary table_db_save" @click="saveBdfp">确定</button>
                        <button class="zui-btn table_db_esc" @click="isShow = false,isShowpopL=false">取消</button>
                    </div>
                </div>
            </div>
        </div>
</div>
<script type="text/javascript" src="bdfp.js"></script>
</body>
</html>
