<!DOCTYPE html>
<html lang="en">
<head>
    <title>受限药品</title>
    <link href="userPage/user.css" rel="stylesheet">
</head>
<style>
    .icon-icon, .icon-icon:before {
        width: 24px;
        height: 24px;
        display: inline-block;
    }
    .hzList input {
        height: 28px;
    }
</style>
<body>
<div class="panel">
    <div class="grid-box zui-form">
        <div class=" flex margin-b-10 margin-top-5 margin-l-20">
            <label class="whiteSpace margin-r-5 ft-14">审批过滤</label>
            <select-input :cs="true" @change-data="resultChange_type" :not_empty="false"
                          :child="sfsp_tran" :index="jsValue" :val="jsValue"
                          :name="'jsValue'">
            </select-input>
        </div>
    </div>
</div>
<div class="zui-table-view hzList printHide margin-l-10 margin-r-10">
    <div class="zui-table-header">
        <table class="zui-table table-width50">
            <thead>
            <tr>
                <th class="cell-m">
                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-xl text-left"><span>药品名称</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-s"><span>剂量</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-s"><span>单位</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-s"><span>次/日</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-s"><span>途径</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-l"><span>申请日期</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-l"><span>用药时限（天）</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-l"><span>抗生素级别</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-s"><span>状态</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-m"><span>操作</span></div>
                </th>
            </tr>
            </thead>
        </table>
    </div>
    <div class="zui-table-body">
        <table class="zui-table table-width50" v-if="jsonList.length!=0">
            <tbody>
            <tr v-for="(item, $index) in jsonList" :class="{'tableTr': $index%2 == 0}">
                <td class="cell-m">
                    <div class="zui-table-cell  cell-m" v-text="$index+1"></div>
                </td>
                <td>
                    <div class="zui-table-cell  cell-xl text-over-2 text-left" v-text="item.ypmc"></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-s ">
                        <input type="text" class="zui-input startdate" v-model="item.jl"
                               :lay-key="$index+1" :disabled="item.readonly" @keydown="nextFocus($event)" />
                    </div>
                </td>
                <td>
                    <div class="zui-table-cell cell-s " v-text="item.jldwmc"></div>
                </td>
                <td>
                    <div class=" yellow cell-s" style="margin: 0 auto;">
                        <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                      :child="PcData" :index="'pcmc'" :index_val="'pcbm'" :val="item.pcbm"
                                      :name="'jsonList.'+$index+'.pcbm'" :index_mc="'pcmcmc'" :search="true">
                        </select-input>
                    </div>
                </td>
                <td>
                    <div class=" yellow cell-s" style="margin: 0 auto;">
                        <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                      :child="YyffData" :index="'yyffmc'" :index_val="'yyffbm'" :val="item.yyff"
                                      :name="'jsonList.'+$index+'.yyff'" :index_mc="'yyffmc'" :search="true" >
                        </select-input>

                    </div>
                </td>
                <td>
                    <div class="zui-table-cell  cell-l" v-text="item.sqsj"></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-l " >
                        <input @mousewheel.prevent type="number" class="zui-input startdate" v-model="item.yysx"
                               :lay-key="$index+1" :disabled="item.readonly"/>

                    </div>
                </td>
                <td>
                    <div class="zui-table-cell cell-l " v-text="kssjb_tran[item.kssdj]"></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-s yellow" v-text="item.spbz==undefined ? '草稿': sfsp_tran[item.spbz]"></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-m" v-if="item.spbz==0||item.spbz==undefined">
                        <i   class="icon-icon icon-qxjs" @click="delsq($index)"></i>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
        <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
    </div>
    <div class="zui-table-tool hzgl-flex">
        <!--<button class="tong-btn btn-parmary-b icon-zfh paddr-r5">作废</button>-->
        <button v-waves class="tong-btn btn-parmary btn-parmary-not xmzb-db paddr-r5 yellow-bg" @click="showDael">选择药品</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="addsq">提交申请</button>

    </div>
</div>
<div class="side-form  pop-width pop-805" v-cloak :class="{'ng-hide':num==1}" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span>选择抗生素</span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="col-xxl-4 flex margin-l-10">
        <div class="flex margin-b-10 margin-top-10 margin-l-20">
            <label class="whiteSpace margin-r-5 ft-14">检索</label>
            <div class="zui-input-inline margin-l13">
                <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" v-model="jsValue" @keydown="Wf_YpChange()"/>
            </div>
        </div>
    </div>
    <div class="ksys-side hzgl-height">
        <div class="col-x-12 margin-l-10 ">
            <div class="zui-table-view" id="zuiTable">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>

                            <th class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'hssList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s"><span>药品编码</span></div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s text-left"><span>药品名称</span></div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-m"><span>库存</span></div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s"><span>规格</span></div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-m"><span>单价</span></div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-l text-left"><span>抗生素级别</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" data-no-attr="true">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr :tabindex="$index" :class="[{'table-hovers':isChecked[$index]}]"
                            @click="checkSelect([$index,'some','hssList'],$event)"
                            v-for="(item, $index) in hssList">
                            <td class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'hssList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ypbm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s text-left" v-text="item.ypmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-m" v-text="item.kcsl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ypgg"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-m" v-text="item.yplj"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l text-left" v-text="kssjb_tran[item.kssjb]"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>

</body>
<script type="text/javascript" src="userPage/sxyp.js"></script>
</html>
