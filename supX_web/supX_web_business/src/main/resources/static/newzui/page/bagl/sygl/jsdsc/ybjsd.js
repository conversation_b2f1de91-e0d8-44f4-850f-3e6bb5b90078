
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data: {
        sczt_tran:{
            '0':'未上传',
            '1':'已上传'
        },
        shzt_tran:{
            '0':'未审核',
            '1':'已审核'
        },
        param:{
            page: 1,
            rows: 500,
            sort: "",
            order: "asc",
            parm: "",
            sczt:'0',
            shzt:'0',

        },
        isShowkd:true,
        ybjsdList:[],
        totlePage: 0,
        tqsjType:false,
        tqparam:{

        },
        clickTq:false,
        lbxs:true,
        mxdaxx:{},
        userInfo:{},
    },
    mounted: function () {
        var myDate=new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: this.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: this.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#tqksTime',
            eventElem: '.zui-date',
            value: this.tqparam.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.tqparam.beginrq = value;
            }
        });
        laydate.render({
            elem: '#tqjsTime',
            eventElem: '.zui-date',
            value: this.tqparam.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.tqparam.endrq = value;
            }
        });

        this.getUserInfo();
        this.getData();
    },
    updated: function () {
        changeWin();
    },
    computed:{

    },
    methods: {
        plqxsc:function(){
            let that = this;
            if(that.clickTq){
                return false;
            }
            that.clickTq = true;
            common.openloading('#wrapper');

            for (let i = 0; i < that.ybjsdList.length; i++) {
                let param = {
                    parm: {
                        zyh: that.ybjsdList[i].zyh
                    }
                }
                that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=queryYbBaScZyhMx', JSON.stringify(param)).then(function (data) {
                    if (data.body.a == "0") {
                        let mxdaxx = data.body.d;
                        if (mxdaxx.shzt == '1') {
                            window.insuranceGbUtils.qd();
                            window.insuranceGbUtils.init();
                            if (window.insuranceGbUtils.initStatus) {
                                let sfyd = '0';
                                if (mxdaxx.jzid.indexOf('519900G') != -1) {
                                    sfyd = '1';
                                } else {
                                    sfyd = '0';
                                }
                                let param4102 = window.insuranceGbUtils.sbfparam_4102(mxdaxx, '0');
                                let data4102 = window.insuranceGbUtils.call1("4102", param4102, mxdaxx.cbdqybqh, sfyd);
                                console.log(data4102)
                                let gxztparam = {
                                    parm: {
                                        zyh: mxdaxx.zyh,
                                        sczt: '0',
                                        scsj: window.insuranceGbUtils.fDate('AllDate'),
                                        sclsh: '',
                                    }
                                }
                                if (data4102) {
                                    gxztparam.parm.shzt = '0';
                                    gxztparam.parm.shsj = window.insuranceGbUtils.fDate('AllDate');
                                }

                                that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateYbBaScZyhMx', JSON.stringify(gxztparam)).then(function (fhdata) {
                                    if (fhdata.body.a == "0") {
                                        that.getData();
                                    } else {
                                    }
                                });
                            }
                        }
                    } else {
                        malert(data.body.c);
                    }
                });
            }
            that.clickTq = false;
            common.closeLoading()
        },
        chYbBaScZyh:function(index,item){
            let that = this;
            let param = {
                parm :{
                    zyh:item.zyh
                }
            }
            that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=queryYbBaScZyhMx', JSON.stringify(param)).then(function (data) {
                if (data.body.a == "0") {
                    let mxdaxx = data.body.d;
                    if (mxdaxx.shzt == '1') {
                        window.insuranceGbUtils.qd();
                        window.insuranceGbUtils.init();
                        if (window.insuranceGbUtils.initStatus) {
                            let sfyd = '0';
                            if (mxdaxx.jzid.indexOf('519900G') != -1) {
                                sfyd = '1';
                            } else {
                                sfyd = '0';
                            }
                            let param4102 = window.insuranceGbUtils.sbfparam_4102(mxdaxx, '0');
                            let data4102 = window.insuranceGbUtils.call1("4102", param4102, mxdaxx.cbdqybqh, sfyd);
                            console.log(data4102)
                            let gxztparam = {
                                parm: {
                                    zyh: mxdaxx.zyh,
                                    sczt: '0',
                                    scsj: window.insuranceGbUtils.fDate('AllDate'),
                                    sclsh: '',
                                }
                            }
                            if (data4102) {
                                gxztparam.parm.shzt = '0';
                                gxztparam.parm.shsj = window.insuranceGbUtils.fDate('AllDate');
                            }

                            that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateYbBaScZyhMx', JSON.stringify(gxztparam)).then(function (fhdata) {
                                if (fhdata.body.a == "0") {

                                } else {
                                }
                            });
                            if(that.ybjsdList.length-1 == i){
                                that.getData();
                            }
                        }
                    }
                } else {
                    malert(data.body.c);
                }
            });

        },
        resultRydjChange: function (val) {
            //先获取到操作的哪一个
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);

            this.goToPage(1);
        },
        getUserInfo: function () {
            let that = this;
            that.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    let userInfo = json.body.d;
                    that.userInfo = userInfo;
                });
        },
        plsc:function(){
            let that = this;
            if(that.clickTq){
                return false;
            }
            that.clickTq = true;
            common.openloading('#wrapper');

            for (let i = 0; i < that.ybjsdList.length; i++) {
                let param = {
                    parm :{
                        zyh:that.ybjsdList[i].zyh
                    }
                }
                that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=queryYbBaScZyhMx', JSON.stringify(param)).then(function (data) {
                    if (data.body.a == "0") {
                        let mxdaxx = data.body.d;
                        if(mxdaxx.sczt =='0'){
                            window.insuranceGbUtils.qd();
                            window.insuranceGbUtils.init();
                            if (window.insuranceGbUtils.initStatus) {
                                let param4101a = window.insuranceGbUtils.sbfparam_4101a(mxdaxx, that.userInfo);
                                console.log(param4101a)

                                let sfyd = '0';
                                if (mxdaxx.jzid.indexOf('519900G') != -1) {
                                    sfyd = '1';
                                } else {
                                    sfyd = '0';
                                }


                                let data2 = window.insuranceGbUtils.call1("4101A", param4101a, mxdaxx.cbdqybqh, sfyd);
                                console.log(data2)
                                if(data2){
                                    let param4102 = window.insuranceGbUtils.sbfparam_4102(mxdaxx,'1');
                                    let data4102 = window.insuranceGbUtils.call1("4102", param4102, mxdaxx.cbdqybqh, sfyd);
                                    console.log(data4102)
                                    let gxztparam = {
                                        parm:{
                                            zyh:mxdaxx.zyh,
                                            sczt:'1',
                                            scsj:window.insuranceGbUtils.fDate('AllDate'),
                                            sclsh:data2.setl_list_id,
                                        }
                                    }
                                    if(data4102){
                                        gxztparam.parm.shzt = '1';
                                        gxztparam.parm.shsj = window.insuranceGbUtils.fDate('AllDate');
                                    }

                                    that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateYbBaScZyhMx', JSON.stringify(gxztparam)).then(function (fhdata) {
                                        if (fhdata.body.a == "0") {

                                        } else {
                                        }
                                    });
                                }
                                if(that.ybjsdList.length-1 == i){
                                    that.getData();
                                }
                            }


                        }


                    } else {
                        malert(data.body.c);
                    }
                });


            }
            that.clickTq = false;
            common.closeLoading()

        },



        saveSjtq:function(){
            let that = this;
            let param = {
                parm :that.tqparam
            }
            if(that.clickTq){
                return false;
            }
            that.clickTq = true;
            common.openloading('#wrapper');
            that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=queryYbjsc', JSON.stringify(param)).then(function (data) {
                common.closeLoading()
                that.clickTq = false;
                if (data.body.a == "0") {
                    that.tqsjType = false;
                    that.getData();
                } else {
                    malert(data.body.c);
                }
            });
        },
        showTq:function () {
            this.tqsjType = true;
        },

        showlb:function(){
            let that = this;
            that.lbxs = true;
            that.mxdaxx = {};
            that.$forceUpdate()
        },
        deleteYbBaScZyh:function(index,item){
            let that = this;
            let param = {
                parm :{
                    zyh:item.zyh
                }
            }
            that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=deleteYbBaScZyh', JSON.stringify(param)).then(function (data) {
                if (data.body.a == "0") {
                    that.getData();
                } else {
                    malert(data.body.c);
                }
            });

        },
        savemx:function(){
            let that = this;
            let param = {
                parm :that.mxdaxx
            }
            that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateYbBaScZyhMx', JSON.stringify(param)).then(function (data) {
                if (data.body.a == "0") {
                    that.getData();
                    that.lbxs = true;
                    that.$forceUpdate()
                } else {
                    malert(data.body.c);
                }
            });
        },
        showDetail:function(index,item){
            let that = this;
            let param = {
                parm :{
                    zyh:item.zyh
                }
            }
            that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=queryYbBaScZyhMx', JSON.stringify(param)).then(function (data) {
                if (data.body.a == "0") {
                    that.mxdaxx = data.body.d;
                    that.lbxs = false;
                    that.$forceUpdate()
                } else {
                    malert(data.body.c);
                }
            });

        },
        getData:function(){
            let that = this;
            let param = {
                parm :that.param
            }
            that.$http.post('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=queryYbBaSc', JSON.stringify(param)).then(function (data) {
                if (data.body.a == "0") {
                    that.ybjsdList = data.body.d.list
                    that.totlePage = Math.ceil(data.body.d.total / that.param.rows);
                } else {
                    malert(data.body.c);
                }
            });


        },

    }
});


//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

});




