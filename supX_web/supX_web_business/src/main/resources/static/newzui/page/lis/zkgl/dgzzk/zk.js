(function () {
    $(".zui-table-view").uitable();
    $(".zui-input").uicomplete();
    var s = new Date().getTime()
    var left = new Vue({
        el: '.xmzb-content-left',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            jysbList: {},
            getobjlist: '',
            params: {
                zxsb: '',
                parm: '',
            },
            time: '',
        },
        created: function () {
            this.jysb()
        },
        methods: {
            getchild: function (list) {
            	if(list== undefined){
            		return;
            	}
                var litst={}
                litst=list
                right.list = litst
                console.log(right.list)
                right.xbmc = litst.zbmc
                right.jyff = litst.jyff
                right.zhonglistarr = []
                right.datalistarr = []
                right.gaolistarr = []
                // this.$options.filters['testFilter2']
                var rq = new Date(right.time).getTime()
                litst.rq = rq;
                // var json='{"list":' + JSON.stringify(list) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=ZkxtZkglDlm&types=queryAllZkjg', JSON.stringify(list)).then(
                    function (data) {
                        console.log(data);
                        if (data.body.a == 0) {
                            right.getchilddata = data.body.d.list;
                            this.$nextTick(function () {
                                right.getData()
                                right.getechart()
                            })
                            console.log("------")
                            console.log(right.getchilddata);
                            for (var i = 0; i < right.getchilddata.length; i++) {
                                Vue.set(right.zkph, right.getchilddata[i].zkwph, right.getchilddata[i].zkwph)

                                if (right.getchilddata[i].zklx == 0) {
                                    right.dilist.zklx = right.getchilddata[i].zkjg
                                    right.dilist.rq = right.getchilddata[i].rq
                                    right.dilist.zbxm = right.getchilddata[i].zbxm
                                    right.datalistarr.push(JSON.parse(JSON.stringify(right.dilist)));
                                } else if (right.getchilddata[i].zklx == 1) {
                                    right.zhonglist.zklx = right.getchilddata[i].zkjg
                                    right.zhonglist.rq = right.getchilddata[i].rq
                                    right.zhonglist.zbxm = right.getchilddata[i].zbxm
                                    right.zhonglistarr.push(JSON.parse(JSON.stringify(right.zhonglist)))
                                } else {
                                    right.gaolist.zklx = right.getchilddata[i].zkjg
                                    right.gaolist.rq = right.getchilddata[i].rq
                                    right.gaolist.zbxm = right.getchilddata[i].zbxm
                                    right.gaolistarr.push(JSON.parse(JSON.stringify(right.gaolist)))
                                }
                            }
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            },
            jysb: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    if (json.a == 0) {
                        left.jysbList = json.d.list;
                        left.params.zxsb = json.d.list[0].sbbm;
                        left.getsbbm()
                    } else {
                        malert("获取数据失败" + json.c, 'top', 'defeadted');
                        return false;
                    }
                });
            },
            getsbbm: function () {
            	this.params.sbbm=this.params.zxsb;
                $.getJSON("/actionDispatcher.do?reqUrl=ZkxtZkglDlm&types=queryXmyb&parm=" + JSON.stringify(this.params), function (json) {
                    if (json.a == 0) {
                        // if (json.d.list.length!=0) {
                    	//去重高中低值 留一条数据
                            left.getobjlist = json.d.list;
                             wapse.getobjlist = json.d.list;
                            right.time = right.$options.filters['formDate'](s)
                            left.getchild(left.getobjlist[0])
                        // }

                    } else {
                        malert("获取数据失败" + json.c, 'top', 'defeadted');
                        return false;
                    }
                });
            }
        },
        watch:{
            'params.zxsb':function () {
                this.getsbbm()
            },
            'time':function () {
               this.$nextTick(function () {
                   this.wzkxm=this.zkxm==''?this.getsblist[0].zkxm:this.zkxm
                   this.getlist(this.wzkxm)
               })
            }
        }
    })
    var right = new Vue({
        el: '.xmzb-content-right',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            num: 0,
            arr: [],
            arr03: [],
            arr1: [],
            arr01: [],
            arr02: [],
            arr2: [],
            arr3: [],
            xbmc: '',
            jyff: '',
            list: {},
            zkph: [],
            gaolist: {},
            zhonglist: {},
            dilist: {},
            datalistarr: [],
            gaolistarr: [],
            zhonglistarr: [],
            getchilddata: [],
            obj: [],
            arr04: [],
            arr004: [],
            time: '',
            start: new Date().setDate(1),
            end: '',
        },
        filters: {
            formdatat:function (value) {
                var d = new Date(value);
                return d.getDate()
            },
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            },
            formDatehanzi: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '年' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '月' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate()) + '日'
            },
            formAverage: function (list, value) {
                var num = 0
                for (var i = 0; i < list.length; i++) {
                    num = num + list[i][value]
                }
                return num / 2
            },
        },
        created: function () {

            this.end = this.getCurrentMonthLast()
        },
        methods: {
            print:function () {
                window.print()
            },
            getCurrentMonthLast: function () {
                var date = new Date();
                var currentMonth = date.getMonth();
                var nextMonth = ++currentMonth;
                var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
                var oneDay = 1000 * 60 * 60 * 24;
                return new Date(nextMonthFirstDay - oneDay);
            },
            getData: function () {
                var d = new Date();
                this.arr=[];
                this.arr001=[];
                this.arr04=[];
                this.arr01=[];
                this.arr02=[];
                var curMonthDays = new Date(d.getFullYear(), (d.getMonth() + 1), 0).getDate();
                // var curMonthDays = 10;
                for (var i = 1; i < curMonthDays; i++) {
                    this.arr.push(i,null);
                    this.arr001.push(i);
                    // this.arr1.push(this.arr)
                    this.arr04.push(JSON.parse(JSON.stringify(this.arr)));
                    this.arr01.push(JSON.parse(JSON.stringify(this.arr)));
                    this.arr02.push(JSON.parse(JSON.stringify(this.arr)));
                    this.arr=[]
                }
                for(var i=0;i<this.arr04.length;i++){
                for (var j = 0; j < this.getchilddata.length; j++) {
                    switch (parseInt(this.getchilddata[j].zklx)) {
                        case 0:
                            if(parseInt(this.$options.filters['formdatat'](this.getchilddata[j].rq))==this.arr04[i][0]){
                                this.arr04[i].splice(1,1,(this.getchilddata[j].zkjg-right.list.x)/right.list.sd)
                            }
                            // this.arr04.push((this.getchilddata[i].zkjg-right.list.x)/right.list.sd);
                            break;
                        case 1:
                            if(parseInt(this.$options.filters['formdatat'](this.getchilddata[j].rq))==this.arr04[i][0]){
                                // Vue.set(this.arr01[i][i],'1',(this.getchilddata[j].zkjg-right.list.x)/right.list.sd)
                                this.arr01[i].splice(1,1,(this.getchilddata[j].zkjg-right.list.x)/right.list.sd)
                            }
                            // this.arr01.push((this.getchilddata[i].zkjg-right.list.x1)/right.list.sd1);
                            break;
                        case 2:
                            if(parseInt(this.$options.filters['formdatat'](this.getchilddata[j].rq))==this.arr04[i][0]){
                                this.arr02[i].splice(1,1,(this.getchilddata[j].zkjg-right.list.x)/right.list.sd)
                            }
                            break;
                            // this.arr02.push((this.getchilddata[i].zkjg-right.list.x2)/right.list.sd2);
                    }
                }
                }
            },
            showlist: function (i) {
                this.num = i
            },
            sc: function (i, id, sbmc) {
                pop.title = '删除质控记录';
                pop.centent = '确定删除质控记录：' + sbmc + ' 吗？';
                pop.isShow = true;
                pop.list.id = id;

            },
            scall: function () {
                if (this.isChecked.length > 0) {
                    pop.isShow = true;
                    pop.centent = '确定删除多条质控记录吗？';
                    pop.title = '删除质控记录';
                } else {
                    malert('请选择需要删除的质控记录', 'top', 'defeadted');
                }
            },
            bj: function (item, type, id) {
                wapse.num = 1;
                wapse.type = type;
                if(type=='top'){
                    wapse.title='多规则结果新增'
                }else{
                    wapse.title='多规则结果编辑'
                }
                $("#isFold").addClass('side-form-bg');
                    wapse.datalist = item == '' ? left.getobjlist[0] : item;
                    wapse.datalist.zfbz =  wapse.datalist.zfbz = 0
                    wapse.datalist.rq=right.$options.filters['formDate'](wapse.datalist.rq)
                    wapse.datalist.zfbz = wapse.datalist.zfbz == 0 ? true : false
            },
            getechart: function () {
                var arr='';
                var bordercolor = ['#ff4532', '#2885e2', '#999ea7'];
                var arr04 = this.arr04
                var arr = this.arr001
                var arr01 = this.arr01
                var arr02 = this.arr02
                /*console.log(arr02)
                console.log(arr01)
                console.log(arr04)*/
                if(this.getchilddata.length==0){
                    for(var i=0;i<this.arr04.length;i++){
                        this.arr04[i].splice(1,1,null)
                        this.arr01[i].splice(1,1,null)
                        this.arr02[i].splice(1,1,null)
                    }
                }
                var index = Math.floor((Math.random() * bordercolor.length));
                $('#container').highcharts({
                    title: {
                        text: ''
                    },
                    legend: {
                        enabled: false
                    },
                    plotOptions: {
                        series: {
                            connectNulls: true,
                            marker: {
                                radius: 4,  //曲线点半径，默认是4
                                symbol: 'circle' //曲线点类型："circle", "square", "diamond", "triangle","triangle-down"，默认是"circle"
                            }
                        }
                    },
                    credits: {
                        enabled: false
                    },
                    tooltip: {
                        enabled: true
                    },
                    xAxis: {
                        tickAmount:30,
                        tickPositions:arr,
                        minorTickInterval: 'auto',
                        categories: this.arr,
                        title: {
                            enabled: false,
                            text: '',
                        },
                    },
                    yAxis: {
                        // tickPositions:[-4,-3,-2,-1,0,1,2,3,4],
                        gridLineColor: bordercolor[index],
                        title: {
                            enabled: false,
                            text: '',
                        },
                        labels: {
                            formatter: function (value, idnex) {
                                if (this.value == 0) {
                                    return '0'
                                } else if (this.value < 0) {
                                    return '-' + this.value
                                } else {
                                    return this.value
                                }
                            }
                        }
                    },
                    series: [
                        {data: arr04},
                        {data: arr01},
                        {data: arr02}
                    ]
                }, function (chart) { // on complete
                    var list = 0;
                    var cX = ''
                    var cY = ''
                    var points = chart.series[0].points;
                    Highcharts.each(points, function (value, index) {
                        if (value.y > 5) {
                            list = list + 1
                            if (list == 1) {
                                cX = value.plotX
                                cY = value.plotY
                            }
                        } else if(value.y<-2){
                            are(value.plotX, value.plotY, 113.6875, 30)
                            if (list > 5) {
                                are(cX + 30, cY / 2, points[index - 1].plotX, value.plotY)
                            }
                        }
                        if (list > 4) {
                            if (points[index + 1]) {
                                if (points[index + 1].y > 5) {
                                } else {
                                }
                            } else {
                            }
                        }

                        function are(x, y, X, Y) {
                            chart.renderer.rect(x, y, X, Y, 5).attr({
                                'stroke-width': 2,
                                stroke: 'rgba(255,69,50,0.10)',
                                fill: 'rgba(255,69,50,0.10)',
                                zIndex: 3
                            }).add();
                        }
                    });
                });
            },
        },
    })
    var pop = new Vue({
        el: '#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            title: '',
            centent: '',
            list: {},
            detarr: [],
            isShow: false,
        },
        methods: {
            delOk: function () {
                var jsondata = '';
                if (right.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            // right.getchilddata[i].id
                            this.list.id = right.getchilddata[i].id
                            this.detarr.push(this.list)
                            jsondata = '{"list":' + JSON.stringify(this.detarr) + '}';
                        }
                    }
                } else {
                    jsondata = '{"list":' + JSON.stringify([this.list]) + '}';
                }
                var json = jsondata;
                this.$http.post('/actionDispatcher.do?reqUrl=ZkxtZkglDlm&types=deleteZkjg', json).then(function (data) {
                        if (data.body.a == 0) {
                            right.getchilddata.splice(i, 1)
                            malert('删除成功', 'top', 'success');
                            pop.detarr = []
                            pop.list = {}
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                            pop.detarr = []
                            pop.list = {}
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            }
        },
    })
    var wapse = new Vue({
        el: '#brzcList',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            isShowpopL: false,
            isShow: false,
            params:{
                zbmc:'',
            },
            getobjlist:{},
            getzkxm:'',
            title: '',
            centent: '',
            type: '',
            num: 0,
            datalist: {},
            isFold: false,
        },
        created: function () {

            this.getzhxm()
        },
        methods: {
            ck:function (index) {
                this.datalist=this.getzkxm[index]
                
                wapse.datalist.zfbz =true
            },
            getzhxm: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=ZkxtZkglDlm&types=queryZkxm", function (json) {
                    if (json.a == 0) {
                        wapse.getzkxm = json.d.list;
                    } else {
                        malert("获取数据失败" + json.c, 'top', 'defeadted');
                        return false;
                    }
                });
            },
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                wapse.num = 0;
            },
            save: function () {
                 delete this.datalist.id
                var url = this.type == 'top' ? '/actionDispatcher.do?reqUrl=ZkxtZkglDlm&types=insertZkjg' : '/actionDispatcher.do?reqUrl=ZkxtZkglDlm&types=updateZkjg';
                this.datalist.zfbz = this.datalist.zfbz == false ? 1 : 0
                // var json='{"list":' + JSON.stringify(list) + '}';
                this.$http.post(url, JSON.stringify(this.datalist)).then(
                    function (data) {
                        console.log(data);
                        if (data.body.a == 0) {
                            malert(data.body.c, 'top', 'success');
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
                $(".side-form-bg").removeClass('side-form-bg')
                wapse.num = 0;
            }
        },
    });
    var wapse1 = new Vue({
        el: '#brzcList1',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            isShowpopL: false,
            isShow: false,
            title: '多规则质控评价',
            centent: '',
            isFold: false,
        },

        methods: {
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            save: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            }
        },
    });
    laydate.render({
        elem: '.date',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
            right.time = value
        }
    });
    laydate.render({
        elem: '.JCRQ',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
            wapse.datalist.rq = value
        }
    });
})()
