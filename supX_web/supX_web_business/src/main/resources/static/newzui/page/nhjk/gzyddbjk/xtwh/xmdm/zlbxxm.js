/**
 * Created by mash on 2017/10/8.
 */
(function () {
    var zl_toolMenu = new Vue({
        el: '.zl_toolMenu',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            bxlbbm: null,
            bxurl: null,
            searchtext: null,
            type:'qb',
        },
        methods: {
            sschangeDown: function () {
                if (window.event.keyCode == 13) {
                    zl_toolMenu.searchtext = $('#search').val();
                    zl_toolMenu.getData();
                }
            },
            changeType:function(xType){
            	zl_toolMenu.type=xType;
            	zl_toolMenu.getData();
            },

            getbxlb: function () {
                var param = {bxjk:"004"};
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            if (json.d.list.length > 0) {
                                zl_toolMenu.bxlbbm = json.d.list[0].bxlbbm;
                                zl_toolMenu.bxurl = json.d.list[0].url;
                            }
                        } else {
                            malert("保险类别查询失败!" + json.c)
                        }
                    });
            },
            // 请求保险类别
            getData: function () {
                var param = {
                    'page': 1,
                    'rows': 500,
                    'parm': zl_toolMenu.searchtext,
                    'fpfs':null,
                    'fpks':null,
                };
                if(zl_toolMenu.type=='yd'){
                	param.fpks='1';
                }
                if(zl_toolMenu.type=='wd'){
                	param.fpfs='1';
                }
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=queryZl&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            zlXmMx.jsonList = res.list;
                        } else {
                            malert(json.c);
                        }
                    });
            },
            // 保存项目详情
            save: function (bxlbbm, fyxmbm, bxxmbm, bxxmmc) {
                var param = {
                    'page': 1,
                    'rows': 30,
                    'bxlbbm': bxlbbm,
                    'fyxmbm': fyxmbm,
                    'bxxmbm': bxxmbm,
                    'bxxmmc': bxxmmc,
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=saveZl&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            malert("保存诊疗项目成功！");
                        } else {
                            malert(json.c);
                        }
                    });
            },
            // 删除项目详情
            remove: function () {

            },
            //获取诊疗项目
            loadXm: function () {
                var param = {
                    page: 1,
                    rows: 30,
                    bxlbbm: zl_toolMenu.bxlbbm
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=getZl&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            malert("获取诊疗项目成功！");
                            zl_toolMenu.getData();
                        } else {
                            malert(json.c);
                        }
                    });

            },
            //自动对码（项目名称）
            autoDm: function () {
                var param = {
                    page: 1,
                    rows: 30
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=autoDmZl&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            malert("自动对码（项目名称）成功！");
                            zl_toolMenu.getData();
                        } else {
                            malert(json.c);
                        }
                    });
            }
        }
    });
    zl_toolMenu.getbxlb();

    var zlBxXm = new Vue({
        el: '.zlBxXm',
        mixins: [tableBase, baseFunc],
        data: {
            jsonList: [],
            searchCon: [],
            param: {}
        },
        methods: {
            getData: function () {
                var param = {
                    bxjk: '004'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                    zlBxXm.totlePage = Math.ceil(json.d.total / zlBxXm.param.rows);
                    zlBxXm.jsonList = json.d.list;
                });
            },
            getMx: function () {
                zl_toolMenu.getData();
            }
        }
    });
    zlBxXm.getData();

    var zlXmMx = new Vue({
        el: '.zlXmMx',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data: {
        	qjIndex:null,
            jsonList: [],
            isEdit: null,
            text: null,
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            popContent: {},
            searchCon: {},
            selSearch: -1,
            dg: {page: 1, rows: 5, sort: "", order: "asc", parm: ""},
            them: {'项目编码': 'itemCode', '项目名称': 'itemName', '项目分类': 'classCode', '自付比例': 'selfScale'}
        },
        methods: {
            edit: function (index) {
                this.isEdit = index;
            },
            // 点击进行赋值的操作
            selectOne: function (item, index) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching(zlXmMx.qjIndex, true,'bxxmmc',zlXmMx.jsonList[zlXmMx.qjIndex].bxxmmc); // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    zlXmMx.popContent = item;
                    Vue.set(zlXmMx.jsonList[zlXmMx.qjIndex], 'bxxmmc', zlXmMx.popContent['itemName']);
                    zlXmMx.jsonList[zlXmMx.qjIndex].bxxmbm=this.popContent.itemCode;
                    /*zlXmMx.jsonList[zlXmMx.qjIndex].bxxmlb=this.popContent.yka001;
                    zlXmMx.jsonList[zlXmMx.qjIndex].zfbl=this.popContent.yka096;*/
                    zl_toolMenu.save(zl_toolMenu.bxlbbm, zlXmMx.jsonList[zlXmMx.qjIndex]['xmbm'], zlXmMx.jsonList[zlXmMx.qjIndex]['bxxmbm'], zlXmMx.jsonList[zlXmMx.qjIndex]['bxxmmc']);
                    $(".selectGroup").hide();
                }
            },
            changeDown: function (index, event, type) {
                if (this['searchCon'][this.selSearch] == undefined) return;
                this.keyCodeFunction(event, 'popContent', 'searchCon');
                if (event.code == 'Enter' || event.code == 13) {
                    Vue.set(zlXmMx.jsonList[index], 'bxxmmc', zlXmMx.popContent['itemName']);
                    Vue.set(zlXmMx.jsonList[index], 'bxxmbm', zlXmMx.popContent['itemCode']);
                    /*Vue.set(zlXmMx.jsonList[index], 'bxxmlb', zlXmMx.popContent['yka001']);
                    Vue.set(zlXmMx.jsonList[index], 'zfbl', zlXmMx.popContent['yka096']);*/
                    zl_toolMenu.save(zl_toolMenu.bxlbbm, zlXmMx.jsonList[index]['xmbm'], zlXmMx.jsonList[index]['bxxmbm'], zlXmMx.jsonList[index]['bxxmmc']);
                    this.nextFocus(event);
                }
            },
            // 输入内容进行检索
            searching: function (index, add, type,val) {
            	zlXmMx.qjIndex=index;
            	this.jsonList[index]['bxxmmc'] = val;
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                zlXmMx.popContent = {};
                if (zlXmMx.jsonList[index]['bxxmmc'] == undefined || zlXmMx.jsonList[index]['bxxmmc'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = zlXmMx.jsonList[index]['bxxmmc'];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=zlxx&method=query&parm=" + JSON.stringify(str_param), function (json) {
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            if (add) {
                                for (var i = 0; i < res.list.length; i++) {
                                    zlXmMx.searchCon.push(res.list[i]);
                                }
                            } else {
                                zlXmMx.searchCon = res.list;
                            }
                            zlXmMx.page.total = res.total;
                            zlXmMx.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert(json.c);
                        }
                    });
            }
        }
    });

    $('body').click(function () {
        $(".selectGroup").hide();
    });

    $(".selectGroup").click(function (e) {
        e.stopPropagation();
    });
})();
