<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室会议记录</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link type="text/css" href="kshyjl.css" rel="stylesheet"/>
</head>

<body class="skin-default flex-container flex-dir-c flex-one">

    <div class="wrapper background-f percent100 printHide" id="jyxm_icon" >
        <div class="panel" v-cloak>
            <div class="tong-top">
                <button class="tong-btn btn-parmary" @click="AddModel"><i class="iconfont icon-iocn42 icon-cf"></i>会议记录填写</button>
                <button class="tong-btn btn-parmary-b" @click="getData"><i class="iconfont icon-iocn56 icon-c1"></i>刷新</button>

            </div>
            <div class="tong-search">
                <div class="top-form">
                    <label class="top-label">时间段</label>
                    <div class="top-zinle">
                    <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                        <input type="text" class="zui-input wh240 todate text-indent-20"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-r-10 padd-l-10" v-cloak >
            <div class="zui-table-header">
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>科室</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>会议时间</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>主持人</span></div></th>
                        <th><div class="zui-table-cell cell-xxl text-left"><span>参加人员</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>记录人</span></div></th>
                        <th><div class="zui-table-cell cell-xxl text-left"><span>会议内容</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body "   @scroll="scrollTable($event)" v-cloak>
                <table class="zui-table zui-collapse">
                    <tbody>
                    <tr v-for="(item,$index) in 10" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @dblclick="Listconfirm($index)"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                       >
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">消化内科</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">2018/12/12</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">李浩然</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xxl text-left">李浩然，李浩然，李浩然，李浩然</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">李浩然</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xxl text-left">会议内容会议内容会议内容会议内容会议内容会议内容</div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" :class="'未确认' ? 'color-cff5':'' ">
                                未确认 <!--未确认：颜色状态值color-cff5，返回处理:color-f2 已确认：color-008-->
                            </div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-2">
                                    <!--ui状态-->
                                    <em class="width30"><i class="iconfont icon-iocn12 icon-font20 icon-hover" data-title="未确认" @click="unconfirmed($index)"></i></em>
                                    <em class="width30"><i class="iconfont icon-iocn46 icon-font20 icon-hover" data-title="返回处理" @click="handle($index)"></i></em>
                                </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--暂无数据提示,绑数据放开-->
                <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
            </div>

            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l background-f">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
                        <tr v-for="(item, $index) in 10"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()">
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r background-f">
                <div class="zui-table-header">
                    <table class="zui-table zui-collapse">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <tr v-for="(item, $index) in 10"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                           >
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" :class="'未接收' ? 'color-cff5':'' ">
                                    未接收<!--未接收：颜色状态值color-cff5，已接收：color-008-->
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" >
                                    <span class="flex-center padd-t-2">
                                    <!--根据状态做对应显示-->
                                    <em class="width30"><i class="iconfont icon-iocn12 icon-font20 icon-hover" data-title="未确认" @click="unconfirmed($index)"></i></em>
                                    <em class="width30"><i class="iconfont icon-iocn46 icon-font20 icon-hover" data-title="返回处理" @click="handle($index)"></i></em>
                                    </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>


            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

    </div>
<!--科室会议记录-->
<div id="pop" class="pophide printHide"  :class="{'show':popShow}" v-cloak>
    <div class="kshyjl-width" v-show="MeetShow" >
        <div class="kshyjl-close " @click="popClose"><i class="iconfont icon-iocn55 icon-font25 icon-cf08"></i></div>
        <div class="kshyjl-title">科室会议记录</div>
        <div class="kshyjl-table">
            <div class="table-Department">
                <span class="padd-r-15">科室:<i class="color-c1 padd-l-5">消化内科</i></span>
                <span>会议时间:2020年12月12日10：00</span>
            </div>
            <div class="table-Department">
                <span class="padd-r-15">记录者:Henry</span>
                <span class="padd-r-15">主持人:Henry</span>
                <span>参会人员:李浩然，周丽君，boss</span>
            </div>
            <div class="table-Department"  v-show="wtyShow">
                <span class="padd-r-15">审核人:Henry</span>
            </div>
        </div>
        <div class="kshyjl-theme printShow">
            <vue-scroll :ops="pageScrollOps">
                <h2>会议主题今日会议主题</h2>
                <h3>一、传达护士长会议精神</h3>
                <p>1、关于综合绩效考核检查点评：</p>
                <p>1）、人力资源，床护比不够，新老搭配不合理；</p>
                <p>2）、技能考核，15项操作严格培训；</p>
                <p>3）、理论考的不理想、理论题库15000题；</p>
                <p>4）、操作平时要严抓，按时考核，心肺复苏95分及格医学|教育网|搜集整理，每人必过，不按时考试的扣钱，考核才允许进室内训练中心，注意按要求着装；</p>
                <p>2、人员在职在位，严格管控，按级请假；</p>
                <p>3、护士长、安全员进行科室安全排查（人员管理、毒麻、消防等），与每位护士谈一次话；</p>
                <p>4、等级医院评审抓紧准备，从今日开始，从一点一滴做起，从每个人做起，从平时做起，从常规做起，从每一个病种开始；</p>
                <p>5、被服、床单管理好，办公护士要看，工作人员不准带回家；</p>
                <p>6、护士长要公平对待护士，要懂法，知底线；</p>
                <p>7、12月考核内容：规章制度、科室护理常规、所有应急预案、应知应会、工作流程等医|学教育|网搜集整理；</p>
                <p>8、年终总结，科室总结，明年计划；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>9、论文上交（12月12日前）；</p>
                <p>10、新门诊12月8日开业典礼；</p>
            </vue-scroll>

        </div>
        <div class="kshyjl-btn">
            <button class="root-btn btn-parmary-f2a" @click="Disagree" v-show="prShow==false">不同意</button>
            <button class="root-btn btn-parmary-f2a" @click="print" v-show="prShow">打印</button>
            <button class="root-btn btn-parmary" @click="Agree" v-show="prShow==false">同意</button>
        </div>
    </div>
    <div class="sjys-width" v-show="MeetShow==false">
        <div class="sjys-top">
            <span v-text="topTitle"></span>
            <span class="iconfont icon-iocn55 icon-cf056 icon-font20" @click="popClose"></span>
        </div>
        <div class="sjys-textarea">
            <textarea placeholder="请输入不同意建议"></textarea>
        </div>
        <div class="sjys-pop-btn sjys-pop-btn-t">
            <button class="root-btn btn-parmary-d9" @click="popClose">取消</button>
            <button class="root-btn btn-parmary" @click="popConfirm">确定</button>
        </div>
    </div>
</div>

<!--打印记录-->
<div class="dyjl printShow" v-show="dyShow" v-cloak>
    <h2>科室会议记录</h2>
    <div class="dyjl-center">
        <div class="table-Department">
            <span class="padd-r-15">科室:<i class="color-c1 padd-l-5">消化内科</i></span>
            <span>会议时间:2020年12月12日10：00</span>
        </div>
        <div class="table-Department">
            <span class="padd-r-15">记录者:Henry</span>
            <span class="padd-r-15">主持人:Henry</span>
            <span>参会人员:李浩然，周丽君，boss</span>
        </div>
        <div class="table-Department">
            <span class="padd-r-15">审核人:Henry</span>
        </div>
    </div>
    <div class="kshyjl-theme">
            <h3 class="font16">会议主题今日会议主题</h3>
            <h4>一、传达护士长会议精神</h4>
            <p>1、关于综合绩效考核检查点评：</p>
            <p>1）、人力资源，床护比不够，新老搭配不合理；</p>
            <p>2）、技能考核，15项操作严格培训；</p>
            <p>3）、理论考的不理想、理论题库15000题；</p>
            <p>4）、操作平时要严抓，按时考核，心肺复苏95分及格医学|教育网|搜集整理，每人必过，不按时考试的扣钱，考核才允许进室内训练中心，注意按要求着装；</p>
            <p>2、人员在职在位，严格管控，按级请假；</p>
            <p>3、护士长、安全员进行科室安全排查（人员管理、毒麻、消防等），与每位护士谈一次话；</p>
            <p>4、等级医院评审抓紧准备，从今日开始，从一点一滴做起，从每个人做起，从平时做起，从常规做起，从每一个病种开始；</p>
            <p>5、被服、床单管理好，办公护士要看，工作人员不准带回家；</p>
            <p>6、护士长要公平对待护士，要懂法，知底线；</p>
            <p>7、12月考核内容：规章制度、科室护理常规、所有应急预案、应知应会、工作流程等医|学教育|网搜集整理；</p>
            <p>8、年终总结，科室总结，明年计划；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>10、新门诊12月8日开业典礼；</p>
    </div>


</div>


<script src="kshyjl.js"></script>
</body>

</html>
