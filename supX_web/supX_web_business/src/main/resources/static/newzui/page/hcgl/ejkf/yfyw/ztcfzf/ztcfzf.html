<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title></title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="ztcfzf.css">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
    <!--患者列表-->
    <div class="wrapper" id="page">
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getData()">刷新</button>
                <button @click="deleteMore()" class="tong-btn btn-parmary-b"><i class="icon-ff icon-sc-header paddr-r5"></i>删除</button>
            </div>
        </div>

        <div class="tong-search">
                <div class="flex-container flex-align-c ">
                    <span class="font-14 padd-r-5 ">检索</span>
                        <input class="zui-input wh180" placeholder="请输入检索关键字" v-model="jsValue" type="text" @keyUp.enter="getData()"/>
                </div>
        </div>

        <div class="zui-table-view">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox
                                @result="reCheckBox"
                                :type="'all'"
                                :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>处方号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>临床诊断</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>处方日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>处方金额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>处方医生姓名</span></div>
                        </th>
                          <th>
                            <div class="zui-table-cell cell-s"><span>病人科室</span></div>
                        </th>
                          <th>
                            <div class="zui-table-cell cell-s"><span>处方类型</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>备注说明</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody v-if="jsonList.length">
                    <tr v-for="(item, $index) in jsonList"
                        :tabindex="$index"
                        @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" @dblclick="Ckdetail(item.cfh)"
                        class="tableTr2">
                        <td class="cell-m">
                            <input-checkbox
                                @result="reCheckBox"
                                :type="'some'"
                                :which="$index"
                                :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.cfh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.lczd"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.cfrq,'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s text-right" v-text="fDec(item.cfje,2)"></div>
                        </td>
                       <td>
                            <div class="zui-table-cell cell-s" v-text="item.cfysxm"></div>
                        </td>
                         <td>
                            <div class="zui-table-cell cell-s" v-text="item.brksmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cflx"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.bzsm"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="!jsonList.length" class="noData  text-center zan-border">暂无数据...</p>
            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                            <tr>
                                <th class="cell-m">
                                    <input-checkbox
                                        @result="reCheckBox"
                                        :type="'all'"
                                        :val="isCheckAll">
                                    </input-checkbox>
                                </th>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            class="tableTr2">
                            <td class="cell-m">
                                <input-checkbox
                                    @result="reCheckBox"
                                    :type="'some'"
                                    :which="$index"
                                    :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
    <div class="side-form  pop-850 printHide" v-cloak  id="brzcList" :class="Show ?'' :'ng-hide'" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--编辑药品-->
        <div class="ksys-side">
            <div class="jbxx flex-container flex-dir-c" style="height:100%;">
                <div class="zui-table-view flex-one flex-container flex-dir-c">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell text-left cell-xl"><span>药品名称</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell text-left cell-s"><span>药品规格</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>单次剂量</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>频次名称</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>用药用量</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>用药方法</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>用药说明</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>进价</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>零价</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>药品批号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>药品产地</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body " @scroll="scrollTable($event)" >
                        <table class="zui-table">
                            <tbody>
                            <tr  v-for="(item,$index) in jsonList" :tabindex="$index" @click="switchIndex('activeIndex',true,$index)"
                                :class="[{'table-hover':$index===hoverIndex},{'table-hovers':$index===activeIndex}]"
                                @mouseenter="switchIndex('hoverIndex',true,$index)"
                                @mouseleave="switchIndex()" class="tableTr2">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m"><span v-text="$index+1">序号</span></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell text-left cell-xl" v-text="item.ypmc">药品名称</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell text-left cell-s" v-text="item.ypgg">药品规格</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">{{item.yyjl || ''}} {{item.yyjl ? item.jldwmc :''}}</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.yypcmc">频次名称</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.cfyl+item.yfdwmc">用药用量</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.yyffmc">用药方法</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.yysm">用药说明</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s text-right" v-text="fDec(item.ypjj,2)">进价</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s text-right" v-text="fDec(item.yplj,2)">零价</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.scph">药品批号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.cdmc">药品产地</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>

        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">关闭</button>
        </div>
    </div>
<script src="ztcfzf.js"></script>
</body>
</html>
