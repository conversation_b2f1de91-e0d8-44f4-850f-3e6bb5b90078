<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检验分组</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="jyfz.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary " id="but_regyl" data-toggle="sideform" data-target="#brzcList" @click="add"><i class="icon-xz1 paddr-r5"></i>新增分组</button>
            <button class="tong-btn btn-parmary-b" @click="refresh"><i class=" icon-sx paddr-r5"></i>刷新</button>
            <button class="tong-btn btn-parmary-b " @click="save"><i class="icon-baocun paddr-r5"></i>保存</button>
            <button class="tong-btn btn-parmary-b " @click="del"><i class="icon-sc-header paddr-r5"></i>删除</button>
            <button class="tong-btn btn-parmary-b"><i class=" icon-yl paddr-r5"></i>预览</button>
            <button class="tong-btn btn-parmary-b " @click="piliang"><i class="icon-dysq paddr-r5"></i>打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">搜&nbsp;索</label>
                    <div class="zui-input-inline ">
                        <input type="text" name="phone" class="zui-input  padd-l33" v-model="searchAll" placeholder="请输入关键字">
                    </div>
                </div>
                <button class="zui-btn btn-primary xmzb-db" @click="queryAll">查询</button>
            </div>
        </div>
    </div>

    <div class="zui-table-view ybglTable padd-l-10 padd-r-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                          :type="'all'" :val="isCheckAll">
                    </input-checkbox></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分组编码</span></div></th>
                    <th><div class="zui-table-cell cell-l text-left"><span>分组名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>所属科室</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table">
            <table class="zui-table" >
                <tbody>
                <tr :tabindex="$index" data-index="0"  v-for="(item, $index) in jsonList" :key="item.fzbm" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbm"></div></td>
                    <td><div class="zui-table-cell cell-l text-left" v-text="item.fzmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ksmc"></div></td>
                    <td><div class="zui-table-cell cell-s">
                        <div class="switch cell-s" >
                            <input :id="'checked'+$index" type="checkbox" v-model="item.tybz" true-value="0" false-value="1"  />
                            <label :for="'checked'+$index"></label>
                        </div>
                    </div></td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

    <!--侧边窗口-->
<div class="side-form ng-hide" style="width:320px;padding-top: 0;"  id="brzcList" role="form">
        <div class="tab-message">
            <a>新增检验分组</a>
            <a href="javascript:;" class="fr closex ti-close" style="color:rgba(255,255,255,.56) !important;" @click="AddClose"></a>
        </div>
        <div class="ksys-side">
        <span class="span0">
            <i>分组编码</i>
            <input type="text" class="zui-input border-r4" disabled  placeholder="请输入分组编码"/>
        </span>
            <span class="span0">
            <i>分组名称</i>
            <input type="text" class="zui-input border-r4"  v-model="fz.fzmc" placeholder="请输入分组名称"/>
        </span>
            <span class="span0">
            <i>所属科室</i>
            <select-input @change-data="resultChange" :not_empty="false"
                          :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="fz.ksbm" :name="'fz.ksbm'"
                          :search="true">
            </select-input>
        </span>
            <span class="span0">
            <i>状态</i>
            <div class="switch" >
                  <input  type="checkbox" v-model="fz.tybz" true-value="0" false-value="1" />
                <label></label>
            </div>
        </span>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="confirms">确定</button>
        </div>
    </div>

</div>

<style>
    .side-form-bg{
        background: none;
        position: inherit;
    }
</style>
<script src="jyfz.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
