<div id="ypbxxm">
    <div class="yp_toolMenu">
        <button @click="getData"><span class="fa fa-refresh"></span>刷新</button>
        <button @click="save"><span class="fa fa-save"></span>保存</button>
        <button @click="remove"><span class="fa fa-trash-o"></span>删除</button>
        <button @click="loadXm"><span class="fa fa-trash-o"></span>获取药品项目</button>
        <button @click="autoDm"><span class="fa fa-save"></span>自动对码（项目名称）</button>
        <span style="margin-left: 20px">检索项目：</span>
        <input type="text" style="width: 200px;" @keyDown="sschangeDown2" id="search2"/>
        <label><input type="radio" @click="changeType('qb')" id="yzgl_qb" name="yzgl" checked="checked">全部</label>
        <label><input type="radio" @click="changeType('yd')" id="yzgl_wt" name="yzgl">已对码</label>
        <label><input type="radio" @click="changeType('wd')" id="yzgl_yt" name="yzgl">未对码</label>
        <div class="pageDiv">
	        <div class="page">
	            <div @click="goPage(page, 'prev', 'getData')" class="fa fa-angle-left num"></div>
	            <div class="num" :class="{currentPage: param.page == 1}" @click="goPage(1, null, null)">1</div>
	            <div v-show="prevMore">...</div>
	            <div class="num" v-for="(item, $index) in totlePage" v-text="item"
	                 :class="{currentPage: param.page == $index + 1}" @click="goPage($index + 1, null, null)"
	                 v-show="showPageList.indexOf(item) != -1 && item != 1 && item != totlePage"></div>
	            <div v-show="nextMore">...</div>
	            <div v-show="nextMore" class="num" :class="{currentPage: param.page == totlePage}"
	                 @click="goPage(totlePage, null, null)" v-text="totlePage"></div>
	            <div @click="goPage(page, 'next', 'getData')" class="fa fa-angle-right num next"></div>
	            <div>
	                第<input type="number" v-model="page"/>页
	                <div class="divBtu" @click="goPage(page, null, null)">跳转</div>
	            </div>
	            <div>
	                共<span v-text="totlePage"></span>页
	                <select v-model="param.rows" @change="getData()">
	                    <option value="10">10</option>
	                    <option value="20">20</option>
	                    <option value="30">30</option>
	                </select>条/页
	            </div>
	        </div>
	    </div>
    </div>
    <div class="tableDiv ypBxXm">
        <table class="patientTable patientTableCzy" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;">
            <tr>
                <th style="min-width: 84px">保险类别编码</th>
                <th style="min-width: 120px">保险类别名称</th>
            </tr>
            </thead>
            <tr>
                <th style="min-width: 84px"></th>
                <th style="min-width: 120px"></th>
            </tr>
            <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                :class="[{'tableTrSelect': isChecked[$index]},{'tableTr': $index%2 == 0}]">
                <td v-text="item.bxlbbm"></td>
                <td v-text="item.bxlbmc"></td>
            </tr>
        </table>
    </div>

    <div class="tableDiv ypXmMx">
        <table class="patientTable patientTableCzy" cellspacing="0" cellpadding="0">
            <tr>
                <th style="min-width: 30px">序号</th>
                <th>药品编码</th>
                <th style="min-width: 249px">药品名称</th>
                <th style="min-width: 50px">药品规格</th>
                <!--<th style="min-width: 120px">发药单位</th>-->
                <th style="min-width: 60px">零价</th>
                <th>保险编码</th>
                <th style="min-width: 150px">保险名称</th>
                <th style="min-width: 60px">项目类别</th>
                <th style="min-width: 50px">收费项目类别</th>
                <th style="min-width: 50px">收费项目等级</th>
                <!--<th style="min-width: 50px">审批信息</th>
                <th style="min-width: 50px">药品产地</th>
                <th style="min-width: 50px">农合统筹</th>
                <th style="min-width: 50px">字典类别</th>
                <th style="min-width: 50px">剂量单位</th>
                <th style="min-width: 50px">拼音代码</th>
                <th style="min-width: 50px">种类</th>
                <th style="min-width: 50px">剂型</th>
                <th style="min-width: 50px">类型</th>-->
                <th style="min-width: 50px">保险类别</th>
            </tr>
            <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                :class="[{'tableTrSelect': isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
                <td v-text="$index+1"></td>
                <td v-text="item.xmbm">药品编码</td>
                <td v-text="item.xmmc">药品名称</td>
                <td v-text="item.fygg">药品规格</td>
                <!--<td>发药单位</td>-->
                <td v-text="item.fydj">零价</td>
                <td v-text="item.bxxmbm">保险编码</td>
                <td style="overflow: inherit;min-width: 185px;">
                    <span v-show="isEdit != $index" v-text="item.bxxmmc"></span>
                    <input :id="'mc_'+$index" v-show="isEdit == $index" v-model="item.bxxmmc" @input="searching($index,false,'bxxmmc',$event.target.value)" @keyDown="changeDown($index,$event,'text')">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </td>
                <td v-text="item.ybmllb">项目类别</td>
                <td v-text="item.ybsflb">收费项目类别</td>
                <td v-text="item.ybsfdj">收费项目等级</td>
                <!--<td>审批信息</td>
                <td>药品产地</td>
                <td>农合统筹</td>
                <td>字典类别</td>
                <td>剂量单位</td>
                <td>拼音代码</td>
                <td>种类</td>
                <td =>剂型</td>
                <td>类型</td>-->
                <td v-text="item.bxlbbm">保险类别</td>
            </tr>
        </table>
    </div>
</div>
<script type="application/javascript" src="ypbxxm.js"></script>