<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>申领管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="slgl.css" rel="stylesheet"/>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel box-fixed">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="sx">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药房</label>
                    <div class="zui-input-inline wh122 margin-l-7">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfList" :index="'yfmc'" :index_val="'yfbm'" :val="barContent.yfbm"
                                      :name="'barContent.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">时间段</label>
                    <div class="zui-input-inline flex-container flex-align-c  margin-f-l5">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh120 text-indent20" placeholder="请选择申请开始日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh120 " placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="search"/>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="zui-table-view " id="utable1" z-height="full" style="margin-top: 108px; padding: 0 10px;border: none;">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>申领单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>申领时间</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>申领病区</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>申领人员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr  :tabindex="$index"  :class="[{'table-hovers':$index===activeIndex}]" @dblclick="getPerson($index)" v-for="(item,$index) in jsonList">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-l" v-text="item.sldh"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.slsj,'date')"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ksmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.slhsxm"></div></td>
                    <!-- <td><div class="zui-table-cell" v-text="zhuangtai[item.fybz]" :class="item.fybz=='0' ? 'color-dlr':item.fybz=='1' ? 'color-wtg' : '' "></div></td> -->
                    <td><div class="zui-table-cell cell-s">待摆药</div>
                    <td class="cell-l">
                       <div class="zui-table-cell cell-l">
                           <span style="display: flex;justify-content:center;align-items: center;">
                                <em class="width30" >
                                  <i class="icon-width icon-py-h" data-title="摆药" @click="getPerson($index)"></i>
                                </em>
                               </span>
                         </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
</div>
    <div class="side-form ng-hide pop-850" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="slgl-by">
        <i style="width: 30%"><em>申领单号:</em><em v-text="times.sldh"></em></i>
        <i style="width: 25%"><em>申领时间:</em><em v-text="fDate(times.slsj,'date')"></em></i>
        <i><em>申领病区:</em><em v-text="times.ysksmc"></em></i>
        <i><em>申领人:</em><em v-text="times.zxhsxm"></em></i>
        <!-- <i style="width: 10%" v-show=""><em>全选</em>
            <input-checkbox @result="reCheckBox" :list="'mxfyList'"
                            :type="'all'" :val="isCheckAll">
            </input-checkbox>
        </i> -->
    </div>
        <div class="zui-form margin-top-10">
            <div class="zui-inline" style="margin-left: -60px;width: 80%;">
                <label class="zui-form-label">检索</label>
                <div class="zui-input-inline">
                    <input class="zui-input" v-model="search" placeholder="请输入病人姓名/住院号" type="text"/>
                </div>
            </div>
        </div>
    <div class="ksys-side">

        <div class="jbxx margin-b-20" v-for="(item,$index) in mxfyList">
            <div class="jbxx-size">
                <!-- <div class="all-check">
                    <input-checkbox @result="reCheckBox" :list="'mxfyList'"
                                    :type="'some'" :which="$index"
                                    :val="isChecked[$index]">
                    </input-checkbox>
                </div> -->
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">患者信息</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <ul class="cffy-list">
                    <li><i>患者:<em v-text="item[0].brxm"></em></i><i>性别:<em></em></i><i>年龄:<em></em></i><i>电话:<em></em></i></li>
                    <li><i>挂号序号:<em></em></i><i>住院号:<em v-text="item[0].zyh"></em></i><i>主管医生:<em v-text="item[0].ysxm"></em></i><i>统筹类别:<em v-text="item[0].tclbmc"></em></i></li>
                    <li v-show="moreShow">保险</li>
                    <li v-show="moreShow">过敏史</li>
                    <li v-show="moreShow">遗传史</li>
                    <li v-show="moreShow">手术史</li>
                    <li @click="zkMore(0)" v-show="zkShow" class="zkmore text-center "><small class="color-75">展开更多</small><small class="icon-zkm"></small></li>
                    <li @click="zkMore(1)" v-show="sqShow" class="zkmore text-center"><small class="color-green">收起信息</small><small class="icon-zkm-h"></small></li>
                </ul>

            </div>
            <ul class="cfhj-top all-height margin-top-10">
                <li>
                    <i>序号</i>
                    <i>药品名称</i>
                    <i>剂量（带单位）</i>
                    <i>用法</i>
                    <i>频次</i>
                    <i>规格</i>
                    <i>单位</i></li>
	        </ul>
            <div v-for="(it,$in) in item">
	            <ul class="cfhj-content all-height">
	                <li>
	                    <i v-text="$in+1">1</i>
	                    <i class="relative">
	                        <em class="title title-width" v-text="it.ryypmc" ></em>
	                    </i>
	                    <i v-text="it.jl+it.jldwmc">剂量</i>
	                    <i v-text="it.yyffmc">用法</i>
	                    <i v-text="it.pcmc">频次</i>
	                    <i v-text="it.ypgg">规格</i>
	                    <i v-text="it.yfdwmc">单位</i>
	                </li>
	            </ul>
            </div>
            

        </div>



    </div>
    <div class="ksys-btn" style="right: 5px;">
        <div class="slgl-fl"><i class="icon-width icon-zy"></i><em style="padding-left: 32px;">请注意药品使用合理性！</em></div>
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="bqby" v-text="contents"></button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="bqby.js"></script>

</body>

</html>