<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="libssj.css" rel="stylesheet">
</head>
<style>
    .loadPage{
        background-color: #ffffff;
    }
    .icon-sh:before{
        color: #1abc9c;
    }
    .icon-width:before{
        top: 6px;
    }
    .icon-width1:before{
        top: -13px;
        left:-13px;
    }
    .icon-yl-b:before{
        color: #fff;

    }
    .icon-yl:before{
        font-size: 14px;
    }
</style>
<body class="skin-default" style="overflow: auto">
<div class="wrapper" id="jyxm_icon">
    <div class="panel" id="ljbswhhl">
        <div class="tong-top" v-if="num==0">
            <button @click="addData" class="tong-btn btn-parmary icon-xz1 paddr-r5">新增</button>
            <button @click="save()" class="tong-btn btn-parmary-b"><i class="icon-baocun paddr-r5"></i>保存</button>
            <button @click="remove" class="tong-btn btn-parmary-b paddr-r5 icon-sc-header">删除</button>
            <button @click="sh" class="tong-btn btn-parmary-b"><i class=" icon-width icon-sh  "></i>审核</button>
            <button @click="dr" class="tong-btn btn-parmary-b "><i class=" icon-width icon-dr "></i>导入</button>
            <button @click="dc" class="tong-btn btn-parmary-b"><i class="  icon-width icon-width1 icon-dc "></i>导出</button>
        </div>
        <div class="tong-top" v-if="num==1" style="padding: 7px 0 7px 13px;">
            <button @click="yl" class="tong-btn btn-parmary"><i class=" paddr-r5 icon-yl icon-yl-b"></i>预览</button>
            <button @click="dy" class="tong-btn btn-parmary-b"><i class=" paddr-r5  icon-dysq"></i>打印</button>
            <div class="col-x-1">
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="jsonList" :index="'ljmc'" :index_val="'ljbm'" :val="popContent.ljbm"
                              :name="'popContent.ljbm'" :search="true">
                </select-input>
            </div>
        </div>
        <div class="tong-top" v-if="num==2" style="padding: 7px 0 7px 13px;">
            <button @click="bc" class="tong-btn btn-parmary"><i class=" paddr-r5 icon-baocunb"></i>保存数量</button>
            <button @click="yltwo" class="tong-btn btn-parmary-b"><i class=" paddr-r5 icon-yl"></i>预览</button>
            <button @click="dytwo" class="tong-btn btn-parmary-b"><i class=" paddr-r5 icon-dysq"></i>打印</button>
            <div class="col-x-1">
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="jsonList" :index="'ljmc'" :index_val="'ljbm'" :val="popContent.ljbm"
                              :name="'popContent.ljbm'" :search="true">
                </select-input>
            </div>
        </div>
        <div class="tong-search">
            <div class="zui-form">
            <div class="zui-inline">
                <label class="zui-form-label ">检索</label>
                <div class="zui-input-inline" style="margin-left: -27px;">
                    <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()" />
                </div>
            </div>
            </div>
        </div>
    </div>
    <div  style="background-color: #fff;">
        <div class="fyxm-tab ybglTable">
            <div><span :class="{'active':num==0}" class="isative" onclick="tabBg('lj',0,this)" >路径</span></div>
            <div><span :class="{'active':num==1}" class="isative" onclick="tabBg('bd',1,this)" >表单</span></div>
            <div><span :class="{'active':num==2}" class="isative" onclick="tabBg('yz',2,this)" >医嘱</span></div>
        </div>
        <div class="clear"></div>
        <div class="loadPage"></div>
    </div>
</div>
<script type="text/javascript" src="ljbswhys.js"></script>
</body>
</html>