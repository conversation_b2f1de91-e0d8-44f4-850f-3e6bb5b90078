var cwxx = new Vue({
    el: '#cwxx',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        errorList: [],
        page: {
            page: 1,
            rows: 20,
            total: null
        },
        param:{
            page: 1,
            rows: 100,
            total: null
        },
    },
    methods: {
        getData:function(){
            common.openloading("#cwxx");
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + fyxmTab.bxurl + "&bxlbbm=" + fyxmTab.bxlbbm + "&types=error&method=query&parm=" + JSON.stringify(cwxx.param),
                function(json){
                    if(json.a == '0'){
                        var res = JSON.parse(json.d);
                        cwxx.totlePage = Math.ceil(res.total / cwxx.param.rows);
                        cwxx.errorList = res.list;
                        common.closeLoading();
                    }else{
                        malert("获取错误信息列表出错！","top","defeadted");
                        common.closeLoading();
                    }
                });
        },
        // 请求保险类别
        // getbxlb: function () {
        //     var param = {bxjk: "005"};
        //     $.getJSON(
        //         "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
        //         + JSON.stringify(param), function (json) {
        //             if (json.a == 0) {
        //                 if (json.d.list.length > 0) {
        //                     cwxx.bxlbbm = json.d.list[0].bxlbbm;
        //                     cwxx.bxurl = json.d.list[0].url;
        //                     cwxx.getData();
        //                 }
        //             } else {
        //                 malert("保险类别查询失败!" + json.c,"top","defeadted")
        //             }
        //         });
        // },
    }
});
// cwxx.getbxlb();
