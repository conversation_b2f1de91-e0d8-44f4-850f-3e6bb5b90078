<div id="hyjl" v-cloak class="padd-b-10 padd-l-10 padd-t-10">
    <div class="flex-container">
        <button class="tong-btn btn-parmary" @click="edit">登记/修改</button>
        <button class="tong-btn btn-parmary" @click="cancel">取消入院</button>
        <div  class="djzt" style="font-size: 16px;color: #ff2828">登记状态:{{brxxList.inpid?"已登记":"未登记"}}</div>
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font14">入院信息</div>
        </div>
        <div class="tab-card-body padd-t-10">
            <div class="grid-box ">
                <div class="flex-container flex-align-c flex-wrap-w">
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 ">
                    <p class="padd-r-5">住&nbsp;&nbsp;院&nbsp;&nbsp;号</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.zyh" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">病人姓名</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.brxm" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">性&emsp;&emsp;别</p>
                    <input  class="wh180 zui-input" type="text" v-model="brxb_tran[brxxList.brxb]" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">床&nbsp;&nbsp;位&nbsp;号</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.rycwbh" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">人员类别</p>
                    <input class="wh180 zui-input" type="text"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">出生日期</p>
                    <input  class="wh180 zui-input" type="text" v-model="fDate(brxxList.csrq,'yyyy-MM-dd')" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">年&emsp;&emsp;龄</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.nl" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">入院科室</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.ryksmc" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">住院医师</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.zyysxm" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">入院日期</p>
                    <input class="wh180 zui-input" type="text" v-model="fDate(brxxList.ryrq,'yyyy-MM-dd')" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">费&emsp;&emsp;别</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.brfbmc" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c  padd-r-20 padd-b-10 padd-r-20" style="width: 260px">
                    <p class="padd-r-5">保险病人</p>
                    <input class=" zui-input" type="checkbox" v-model="brxxList.bxbr" disabled="disabled" />
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">保险类别</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.bxlbbm" v-text="brxxList.bxlbmc" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 text-red">保险卡号</p>
                    <input  class="wh180 zui-input border-color-red" type="text" v-model="brxxList.ybkh" data-notEmpty="true" @keydown="nextFocus($event)"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">个人代码</p>
                    <input  class="wh180 zui-input" type="text" v-model="brxxList.memberid" disabled="disabled"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 text-red">联系电话</p>
                    <input class="wh180 zui-input border-color-red" type="text" v-model="brxxList.lxdh"  @keydown="nextFocus($event)" :not_empty="true" />
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 text-red">入院类型</p>
                    <select-input @change-data="resultChange" :not_empty="true" class="wh180 border-color-red"
                                  :child="gznhry_tran" :index="brxxList.rylx" :val="brxxList.rylx"
                                  :search="true" :name="'brxxList.rylx'":not_empty="true">
                    </select-input>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 text-red">疾病名称</p>
                    <input class="wh180 zui-input border-color-red" v-model="jbContent.jbmc" @input="searching(false,'jbmc',$event.target.value)" @keyDown="changeDown($event,'text')">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne":not_empty="true">
                    </search-table>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 text-red">入院情况</p>
                    <select-input @change-data="resultChange" class="wh180 border-color-red"
                                  :child="gznhryqk_tran" :index="brxxList.ryqk" :val="brxxList.ryqk"
                                  :search="true" :name="'brxxList.ryqk'":not_empty="true">
                    </select-input>
                </div>
                <div class="infoIpt flex-container flex-align-c  padd-r-20 padd-b-10 padd-r-20" style="width: 260px">
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'是否重大疾病'" :val="'brxxList.zdjb'"  :new-value="brxxList.zdjb"></vue-checkbox>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">重大疾病</p>
                    <input  class="wh180 zui-input" v-model="jbContentzdjb.zdjbmc" @input="searchingzdjb(false,'zdjbmc')" @keyDown="changeDownzdjb($event,'text')">
                    <search-table4 :message="searchConzdjb" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOnezdjb">
                    </search-table4>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">重大疾病</br>申请序号</p>
                    <input placeholder="请输入疾病申请序号" class="zui-input wh120"  v-model="brxxList.zdjbsqxh" type="text" @keydown="nextFocus($event)"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 ">治疗方式</p>
                    <select-input @change-data="resultChange" class="wh180 border-color-red"
                                  :child="gznhzlfs_tran" :index="brxxList.zlfs" :val="brxxList.zlfs"
                                  :search="true" :name="'brxxList.zlfs'":not_empty="true">
                    </select-input>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 text-red">行政区划</p>
                    <input class="wh180 zui-input" type="text"  v-model="brxxList.xzqh"  @keydown="nextFocus($event)" :not_empty="true" />
                </div>
                    <!--<div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 text-red">经办医院</p>
                    <input class="wh180 zui-input" type="text"  v-model="brxxList.registerID"  @keydown="nextFocus($event)" :not_empty="true" />
                </div>-->
                <div class="infoIpt flex-container flex-align-c  padd-r-20 padd-b-10 padd-r-20" style="width: 260px">
                    <p class="padd-r-5">跨省就医</p>
                    <input class=" zui-input" true-value="1" false-value="0" type="checkbox" v-model="brxxList.ksjy":not_empty="true"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20" style="width: 260px">
                    <p class="padd-r-5">证件是否<br/>齐&emsp;&emsp;全</p>
                    <input class=" zui-input"  type="checkbox" v-model="brxxList.zsqq":not_empty="true"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">手术名称<br/>代&emsp;&emsp;码</p>
                    <input class="wh180 zui-input" v-model="ssContent.ssmc" @input="searching2(false,'ssmc')" @keyDown="changeDown2($event,'text')">
                    <search-table2 :message="searchCon2" :selected="selSearch"
                                   :them="them2" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOne2">
                    </search-table2>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">保&nbsp;&nbsp;险&nbsp; 费</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.bxf" data-notEmpty="false" @keydown="nextFocus($event)"/>
                </div>
                <!--<div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20" style="width: 260px">
                    <p class="padd-r-5">转诊类型</p>
                    <input class=" zui-input" type="checkbox"   v-model="brxxList.zzbz"  data-notEmpty="false" @keydown="nextFocus($event)"/>
                </div>-->
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5 text-red">转诊类型</p>
                    <select-input @change-data="resultChange" class="wh180 border-color-red"
                                  :child="gznhzzlx_tran" :index="brxxList.zzlx" :val="brxxList.zzlx"
                                  :search="true" :name="'brxxList.zzlx'":not_empty="true">
                    </select-input>
                </div>
                <div :class="brxxList.zzlx !='0' ? 'font-bolder red' :''" class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">转&nbsp;&nbsp;诊&nbsp;号</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.turnCode"  data-notEmpty="false" @keydown="nextFocus($event)"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">银行卡号</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.yhkh"  data-notEmpty="false" @keydown="nextFocus($event)"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">银行卡号<br>开户人姓</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.yhkhrx"  data-notEmpty="false" @keydown="nextFocus($event)"/>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">患者与开<br>户人关系</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.khgx"  data-notEmpty="false" @keydown="nextFocus($event)">
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20">
                    <p class="padd-r-5">出院时间</p>
                    <input class="wh180 zui-input" type="text" v-model="fDate(brxxList.bqcyrq,'yyyy-MM-dd')" disabled="disabled"/>
                </div>
                <!--<div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20" style="width: 100%;height: 0;margin: 0;"></div>-->
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(一)</p>
                    <input class="wh180 zui-input" v-model="jbContent2.qtzd1mc" @input="searching3(false,'qtzd1mc')" @keyDown="changeDown3($event,'text')">
                    <search-table3 :message="searchCon3" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOne3">
                    </search-table3>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(二)</p>
                    <input  class="wh180 zui-input" v-model="jbContent3.qtzd2mc" @input="searching4(false,'qtzd2mc')" @keyDown="changeDown4($event,'text')">
                    <search-table4 :message="searchCon4" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOne4">
                    </search-table4>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(三)</p>
                    <input class="wh180 zui-input" v-model="jbContent4.qtzd3mc" @input="searching5(false,'qtzd3mc')" @keyDown="changeDown5($event,'text')">
                    <search-table5 :message="searchCon5" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOne5">
                    </search-table5>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(四)</p>
                    <input class="wh180 zui-input" v-model="jbContent5.qtzd4mc" @input="searching6(false,'qtzd4mc')" @keyDown="changeDown6($event,'text')">
                    <search-table6 :message="searchCon6" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOne6">
                    </search-table6>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(五)</p>
                    <input class="wh180 zui-input" v-model="jbContent6.qtzd5mc" @input="searching7(false,'qtzd5mc')" @keyDown="changeDown7($event,'text')">
                    <search-table7 :message="searchCon7" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOne7">
                    </search-table7>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(六)</p>
                    <input class="wh180 zui-input" v-model="jbContent7.qtzd6mc" @input="searching8(false,'qtzd6mc')" @keyDown="changeDown8($event,'text')">
                    <search-table8 :message="searchCon8" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOne8">
                    </search-table8>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(七)</p>
                    <input class="wh180 zui-input" v-model="jbContent8.qtzd7mc" @input="searching9(false,'qtzd7mc')" @keyDown="changeDown9($event,'text')">
                    <search-table9 :message="searchCon9" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOne9">
                    </search-table9>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(八)&emsp;</p>
                    <input class="wh180 zui-input" v-model="jbContent9.qtzd8mc" @input="searching10(false,'qtzd8mc')" @keyDown="changeDown10($event,'text')">
                    <search-table10 :message="searchCon10" :selected="selSearch"
                                    :them="them" :them_tran="them_tran" :page="page"
                                    @click-one="checkedOneOut" @click-two="selectOne10">
                    </search-table10>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(九)</p>
                    <input class="wh180 zui-input" v-model="jbContent10.qtzd9mc" @input="searching11(false,'qtzd9mc')" @keyDown="changeDown11($event,'text')">
                    <search-table11 :message="searchCon11" :selected="selSearch"
                                    :them="them" :them_tran="them_tran" :page="page"
                                    @click-one="checkedOneOut" @click-two="selectOne11">
                    </search-table11>
                </div>
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">其他诊断<br>&nbsp;&emsp;(十)</p>
                    <input  class="wh180 zui-input" v-model="jbContent11.qtzd10mc" @input="searching12(false,'qtzd10mc')"
                           @keyDown="changeDown12($event,'text')">
                    <search-table12 :message="searchCon12" :selected="selSearch"
                                    :them="them" :them_tran="them_tran" :page="page"
                                    @click-one="checkedOneOut" @click-two="selectOne12">
                    </search-table12>
                </div>
                <!-- <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place">
                    <p class="padd-r-5">重大疾病<br>申请序号</p>
                    <input class="wh180 zui-input" type="text" v-model="brxxList.zdjbxh"  data-notEmpty="false" @keydown="nextFocus($event)"/>
                </div> -->
                <div class="infoIpt flex-container flex-align-c padd-r-20 padd-b-10 padd-r-20 place" style="width: 100% !important;">
                    <p class="padd-r-5">备&emsp;&emsp;注</p>
                    <textarea class="wh180 zui-input" style="width: 59%;height: 60px" v-model="brxxList.bz"  data-notEmpty="false" @keydown="nextFocus($event)"></textarea>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
<div id="hyjl_pop"  v-if="isShow" class="side-form  pop-850" v-cloak :class="{'ng-hide':num==1}"
  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">

<div class="tab-card padd-b-10 padd-l-10 padd-t-10" >
    <div class="tab-card-header" v-show="isShow">
        <div class="tab-card-header-title font14" v-show="isShow">参合人员信息</div>
    </div>
    <div class="tab-card-body padd-t-10">
        <div >
            <transition name="pop-fade">
                <div class="pop" v-show="isShow"  ><!-- style="display: none" -->
                    <div class="popCenter zui-table-view hzList hzList-border flex-container flex-dir-c">
                        <div id="popCon" class="popInfo">
                            <div class="flex">
                                <div class="flex year">
                                    <span>年份：</span>
                                    <input type="number" class="hc_input width-81 zui-input" v-model="year.year"/>
                                </div>
                                <span class="margin-l-5">参合号：</span>
                                <input type="text" class="hc_input wh180 zui-input" v-model="brxxList.ybkh2"/>
                                <button @click="getPerson" class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5 margin-l-5">获取家庭成员</button>
                                <button @click="saveData" class="tong-btn btn-parmary margin-l-5"><span class="fa fa-save"></span>确定</button>
                            </div>
                            <div class="tableDiv zui-table-header">
                                <table class="zui-table table-width50" cellspacing="0" cellpadding="0">
                                    <thead>
                                    <tr>
                                        <th class="cell-m"><div class="zui-table-cell cell-m">序号</div></th>
                                        <th><div class="zui-table-cell cell-s">个人编号</div></th>
                                        <th><div class="zui-table-cell cell-s">姓名</div></th>
                                        <th><div class="zui-table-cell cell-s">性别</div></th>
                                        <th><div class="zui-table-cell cell-s">出生日期</div></th>
                                        <th><div class="zui-table-cell cell-xl">身份证号</div></th>
                                        <th><div class="zui-table-cell cell-xl">医疗证号</div></th>
                                        <th><div class="zui-table-cell cell-s">人员属性</div></th>
                                        <th><div class="zui-table-cell cell-xxl">家庭住址</div></th>
                                        <th><div class="zui-table-cell cell-s">账户余额</div></th>
                                        <th><div class="zui-table-cell cell-s">参保状态</div></th>
                                        <th><div class="zui-table-cell cell-s">住院总费用</div></th>
                                        <th><div class="zui-table-cell cell-s">住院保内费用</div></th>
                                        <th><div class="zui-table-cell cell-s">住院补偿费用</div></th>
                                        <th><div class="zui-table-cell cell-s">单病种总费用</div></th>
                                        <th><div class="zui-table-cell cell-s">单病种保内费用</div></th>
                                        <th><div class="zui-table-cell cell-s">单病种补偿费用</div></th>
                                        <th><div class="zui-table-cell cell-s">门诊总费用</div></th>
                                        <th><div class="zui-table-cell cell-s">门诊保内费用</div></th>
                                        <th><div class="zui-table-cell cell-s">门诊补偿费用</div></th>
                                        <th><div class="zui-table-cell cell-s">慢性病总费用</div></th>
                                        <th><div class="zui-table-cell cell-s">慢性病保内费用</div></th>
                                        <th><div class="zui-table-cell cell-s">慢性病补偿费用</div></th>
                                        <th><div class="zui-table-cell cell-s">人员属性</div></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                                        :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                                        @dblclick="edit($index)" style="cursor: pointer">
                                        <td class="cell-m"><div  class="zui-table-cell cell-m">{{$index+1}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.memberId}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.memberName}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{brxb_tran[item.memberSex]}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.birthday}}</div></td>
                                        <td><div  class="zui-table-cell cell-xl">{{item.idcard}}</div></td>
                                        <td><div  class="zui-table-cell cell-xl">{{item.medicalNo}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.memberPro}}</div></td>
                                        <td><div  class="zui-table-cell cell-xxl">{{item.areaName}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.account}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.memberStatus}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.hosTotalCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.hosInsuranceCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.hosCompensateCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.sigTotalCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.sigInsuranceCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.sigCompensateCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.outpTotalCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.outpInsuranceCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.outpCompensateCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.chroTotalCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.chroInsuranceCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.chroCompensateCost}}</div></td>
                                        <td><div  class="zui-table-cell cell-s">{{item.ideName}}</div></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>
        </div>
    </div>
</div>
 <div style="color: red;margin-left: 20px;font-size: 16px">双击选中参保人员！</div>
</div>
</div>
<script type="application/javascript" src="hyjl.js"></script>
