<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>限量设置</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="xlsz.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary" @click="editSave" ><i class=" icon-baocunb paddr-r5"></i>保存</button>
            <button class="tong-btn btn-parmary-b  icon-sx paddr-r5" @click="searchHc">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药库</label>
                    <div class="zui-input-inline wh122 margin-l-5">
                        <select-input @change-data="resultRydjChange"
                                      :child="YFList" :index="'yfmc'" :index_val="'yfbm'" :val="popContent.yfbm"
                                      :name="'popContent.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="searchHc()" type="text" v-model="search"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10" id="utable1" z-height="full" >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                 :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>药品名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品规格</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>下量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>上量</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-heights" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index" @dblclick="edit($index)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>

                    <td><div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc"></div></td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.ypgg"></div></td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.ypdw"></div></td>
                    <td><div class="zui-table-cell cell-s"><input class="zui-input border-r4 box-disabled" style="height:28px;"  type="number" v-model="item.zdkc" @change="modify($index)" ></div></td>
                    <td><div class="zui-table-cell cell-s"><input class="zui-input border-r4 box-disabled" style="height:28px;"   type="number" v-model="item.zgkc" @change="modify($index)" ></div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="xlsz.js"></script>
</body>

</html>
