(function () {

    //顶部工具栏
    var tool=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
            zt: '9',
        },
        methods:{
            //会议记录填写
            AddModel:function () {
                this.topNewPage('科室会议记录','page/kzrgzt/kzrgzt/kshyjl/hyjl.html');
            },
            //刷新和检索
            getData:function () {
            wxdjList.getData();
            },

        }
    });

    //危急值管理列表
    var wxdjList = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data: {

        },

        updated:function () {
            changeWin()
        },
        methods: {
            //请求后台查询列表信息
            getData : function(){
                //加载动画效果
                common.openloading('.zui-table-view')
                //数据请求结束时关闭


                common.closeLoading()
            },
            //未确认弹窗
            unconfirmed:function (num) {
                pop.popShow=true;
                pop.MeetShow=true;
            },
            //返回处理事件操作
            handle:function () {
                this.topNewPage('科室会议记录','page/kzrgzt/kzrgzt/kshyjl/hyjl.html');
            },
            //已确认弹窗打印
            Listconfirm:function () {
                pop.popShow=true;
                pop.MeetShow=true;
                pop.wtyShow=true;
                pop.prShow=true;
            },

        }
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase, mformat,scrollOps],
        data:{
            popShow:false,
            wjzShow:true,
            prShow:false,
            wtyShow:false,
            topTitle:'',
            ryxmList:[],
            MeetShow:false,
            popContent:{},
        },
        methods:{
        //不同意操作
            Disagree:function () {
                this.MeetShow=false;
                this.wtyShow=false;
                this.prShow=false;
                this.topTitle='不同意建议'
            },
            Agree:function () {
                malert('同意','top','success')
                setTimeout(function () {
                    pop.popClose();
                },1000)
            },
            //关闭取消操作
            popClose:function () {
                this.popShow=false;
                this.prShow=false;
                this.MeetShow=false;
            },
            //确定
            popConfirm:function () {
                this.prShow=false;
                malert('确定','top','success')
            },
            //打印
            print:function () {
                window.print();
            }
        }
    })
var dyjl=new Vue({
    el:'.dyjl',
    data:{
        dyShow:false,
    },
    methods:{

    }
});



    laydate.render({
        elem: '.todate',
        type: 'datetime',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
})();






