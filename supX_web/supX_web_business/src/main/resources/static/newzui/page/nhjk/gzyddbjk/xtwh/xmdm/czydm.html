<div id="kswh">
    <div class="toolMenu2" style="display: block;">
        <button @click="getData"><span class="fa fa-refresh"></span>刷新</button>
        <button @click="addData"><span class="fa fa-plus"></span>新增</button>
        <button @click="edit()"><span class="fa fa-edit"></span>修改</button>
        <button @click="remove"><span class="fa fa-trash-o"></span>删除</button>
    </div>
    <div class="tableDiv">
        <table class="patientTable" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;z-index: 100">
            <tr>
                <th class="tableNo"></th>
                <th style="min-width: 26px"><input type="checkbox" v-model="isCheckAll" @click="checkAll('jsonList')"/></th>
                <th>操作员编码</th>
             	<th>操作员名称</th>
                <th>农合账号</th>
                <th>农合密码</th>
                <th>保险类别名称</th>
                <th>保险类别编码</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <th class="tableNo"></th>
                <th style="min-width: 26px"></th>
                <th>操作员编码</th>
                <th>操作员名称</th>
                <th>农合账号</th>
                <th>农合密码</th>
                <th>保险类别名称</th>
                <th>保险类别编码</th>
            </tr>
            <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                @dblclick="edit($index)">
                <th class="tableNo" v-text="$index+1"></th>
                <th style="min-width: 26px"><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)"/>
                </th>
                <td v-text="item.czybm"></td>
                <td v-text="item.czymc"></td>
                <td v-text="item.nhczy"></td>
                <td v-text="item.password"></td>
                <td v-text="item.bxlbmc"></td>
                <td v-text="item.bxlbbm"></td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="pageDiv">
        <div class="page">
            <div @click="goPage(page, 'prev')" class="fa fa-angle-left num"></div>
            <div class="num" v-for="(item, $index) in totlePage" v-text="item"
                 :class="{currentPage: param.page == $index + 1}"
                 @click="goPage($index + 1)"></div>
            <div @click="goPage(page, 'next')" class="fa fa-angle-right num next"></div>
            <div>
                第<input type="number" v-model="page"/>页
                <div class="divBtu" @click="goPage(page)">跳转</div>
            </div>
            <div>
                共<span v-text="totlePage"></span>页
                <select v-model="param.rows" @change="getData()">
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="30">30</option>
                </select>条/页
            </div>
        </div>
    </div>
</div>

<!--弹出框-->
<div id="pop">
    <transition name="pop-fade">
        <div class="pop" v-show="isShow" style="display: none">
            <div class="popCenter">
                <div id="popCon" class="popInfo" style="height: 470px;">
                    <div class="popTitle dragCSS" v-text="title"
                         onmousedown="drag(event,'popCon')" onmouseup="stopDrag()"></div>
                    <table class="popTable" cellspacing="0" cellpadding="0">
                        <tr>
                            <th>人员名称:</th>
                            <td>
                            <select-input @change-data="resultChange" :not_empty="true"
                                              :child="rybmList" :index="'ryxm'" :index_val="'rybm'" :val="popContent.czybm"
                                              :name="'popContent.czybm'" :search="true">
                            </select-input>
                            </td>
                            <th>农合账号:</th>
                            <td><input v-model="popContent.nhczy" type="text" data-notEmpty="true" @keydown="nextFocus($event)"></td>
                        </tr>
                        <tr>
                            <th>农合密码:</th>
                            <td>
                               <input v-model="popContent.password" type="text" data-notEmpty="true" @keydown="nextFocus($event)"></td>
                            </td>
                            <th>保险类别名称:</th>
                            <td>
                            <select-input @change-data="resultChange" :not_empty="false"
                          		:child="jkList" :index="'zymc'" :index_val="'zybm'" :val="bxContent.bxbm"
                         		 :name="'bxContent.bxbm'" >
            				</select-input>
                            </td>
                        </tr>
                    </table>
                    <div class="popDoBtu">
                        <button @click="saveData" class=""><span class="fa fa-save"></span>保存</button>
                        <button @click="isShow = false" class="cancel"><span class="fa fa-close"></span>取消</button>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</div>

</body>
<script type="text/javascript" src="czydm.js"></script>
</html>
