.fyxm-size {
  width: 100%;
}
.fyxm-hide {
  display: none;
}
.fyxm-show {
  display: table;
}
#jyxm_icon .switch {
  top: 15%;
  left: 30%;
}
.fieldlist,
.zui-table-view .zui-table-fixed {
  top: 36px !important;
}
.pop-width {
  width: 320px !important;
}
.pop-805 {
  width: 805px;
}
.ksys-de {
  width: 100%;
  padding: 6px 14px;
  float: left;
}
.ksys-de span{
  padding-bottom: 15px;
  display: block;
}
.ksys-de span i {
  display: block;
  width: 100%;
  padding-bottom:7px;
}
.icon-width:before{
  left: -4px;
  top: -13px;
}
.tab-message{
  padding: 0 19px;
}
.pop-width .ksys-de{
  padding: 14px !important;
}