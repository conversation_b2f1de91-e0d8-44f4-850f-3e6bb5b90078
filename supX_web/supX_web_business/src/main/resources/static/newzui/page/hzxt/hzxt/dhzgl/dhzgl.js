//顶部工具栏
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        zt: '9',
        ksList: [],
        hzsqList: [],
        csqxContent: {},
        userInfo: {},
        is_csqx: {},
        sfcy: false,
        hzzt_tran: {
            '1': '进行中',
            '2': '已完成',
            '3': '作废',
            '9': '全部',
        },
        param: {},
        param_data:{}
    },
    mounted: function () {
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 3), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        this.param.cybeginrq = this.fDate(myDate.setDate(myDate.getDate() - 3), 'date') + ' 00:00:00';
        this.param.cyendrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        this.getKsbm();
        window.addEventListener("storage", function (e) {
            if (e.key == "apzj_saveHzbg" && e.newValue !== e.oldValue) {
                wrapper.getData();
            }
        });
        this.getUserInfo();
    },
    updated: function () {
        changeWin()
    },
    methods: {

        // 获取操作员用户信息
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    wrapper.userInfo = json.body.d;
                    wrapper.getCsqx();
                    wrapper.getCsqx1();
                });
        },
        //获取参数权限
        getCsqx1: function () {
            //获取参数权限
            parm = {
                "ylbm": 'N030032001',
                "ksbm": wrapper.userInfo.ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03003200101": //医嘱默认西药房  --下医嘱和申领药品时默认，为空则取本地配置文件药房编码
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200101 = csjson.csz;
                                    }
                                    break;
                                case "N03003200102": //中药医嘱默认药房 --输入药房编码
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200102 = csjson.csz;
                                    }
                                    break;
                                case "N03003200103": //医生作废医嘱权限	 0-提示是否打印;1-直接打印;2-不打印
                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                        wrapper.is_csqx.cs003003200103 = csjson.csz;
                                    }
                                    break;
                                case "N03003200104": //中药医嘱默认用药方法   --中药医嘱默认用药方法
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200104 = csjson.csz;
                                    }
                                    break;
                                case "N03003200105": //临时医嘱默认频次编码 --请录入频次编码
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200105 = csjson.csz;
                                    }
                                    break;
                                case "N03003200106": //录入医嘱时指定药品统筹类别  --0、不指定，1、指定
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200106 = csjson.csz;
                                    }
                                    break;
                                case "N03003200107": //保存出院医嘱时是否停嘱 --0、否，1、是
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200107 = csjson.csz;
                                    }
                                    break;
                                case "N03003200108": //下转科医嘱是否自动停嘱参数 0、否，1、是
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200108 = csjson.csz;
                                    }
                                    break;
                                case "N03003200109": //医嘱单打印行数 --医嘱单打印行数
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200109 = csjson.csz;
                                    }
                                    break;
                                case "N03003200110": //自动失效长期医嘱是否签停嘱医生和时间  --0=否，1=是
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200110 = csjson.csz;
                                    }
                                    break;
                                case "N03003200111": //输液速度单位  --0、滴/分 1、gtt/min 2、毫升/小时
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200111 = csjson.csz;
                                    }
                                    break;
                                case "N03003200112": //检查医嘱填写诊断、申请信息 --0-否，1-有
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200112 = csjson.csz;
                                    }
                                    break;
                                case "N03003200113": //检验医嘱填写诊断、申请信息 0-否，1-有
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200113 = csjson.csz;
                                    }
                                    break;
                                case "N03003200114": //是否允许修改医嘱排序功能 0-无 1-有
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200114 = csjson.csz;
                                    }
                                    break;
                                case "N03003200115": //医嘱保存是否需要密码0-不需要,1-需要
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200115 = csjson.csz;
                                    }
                                    break;
                                case "N03003200116": //医嘱保存是否需要密码0-不需要,1-需要
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200116 = csjson.csz;
                                    }
                                    break;
                                case "N03003200117": //审核之后是否允许作废医嘱0-允许，1-不允许
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200117 = csjson.csz;
                                    }
                                    break;
                                case "N03003200118": //是否对抗生素药品使用限制0-否，1-是
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200118 = csjson.csz;
                                    }
                                    break;
                                case "N03003200126": //是否对抗肿瘤药品使用限制0-否，1-是
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200126 = csjson.csz;
                                    }
                                    break;
                                case "N03003200127": //是否对药品种类中成药使用限制0-否，1-是
                                    if (csjson.csz) {
                                        wrapper.is_csqx.cs003003200127 = csjson.csz;
                                    }
                                    break;
                                case "N03003200124": //是否使用住院状态全部选项  0-否，1-是
                                    if (csjson.csz) {
                                        wrapper.is_csqx.N03003200124 = csjson.csz;
                                        if (wrapper.is_csqx.N03003200124 == '1') {
                                            wrapper.zyTypeBq_tran['qbhzxx'] = '全部';
                                            console.log(wrapper.zyTypeBq_tran)
                                        }
                                    }
                                    break;
                                case "N03003200130" :    //是否显示双向转诊按钮 ，（临夏）
                                    if (csjson.csz && csjson.csz == '1') {
                                        kp.sxzzan = true;
                                    }
                                    break;
                                case "N03003200151" :    //是否打开多个页面
                                    wrapper.is_csqx.N03003200151 = csjson.csz;
                                    break;
                                case "N03003200147" :    //患者信息是否可编辑
                                    wrapper.is_csqx.N03003200147 = csjson.csz;
                                    break;
                                case "N03003200176" :    //患者信息性别是否允许修改
                                    wrapper.is_csqx.N03003200176 = csjson.csz;
                                    break;
                            }
                        }

                    }
                } else {
                    malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                }
            });

        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            parm = {
                "ylbm": 'N030102002',
                "ksbm": wrapper.userInfo.ksbm
            };
            this.$http.get("/actionDispatcher.do", {
                params: {
                    reqUrl: 'CsqxAction',
                    types: 'csqx',
                    parm: JSON.stringify(parm)
                }
            }).then(function (json, status, xhr) {
                if (json.body.a == 0) {
                    if (json.body.d) {
                        for (var i = 0; i < json.body.d.length; i++) {
                            var csjson = json.body.d[i];
                            switch (csjson.csqxbm) {
                                case "N03010200201"://会诊开医嘱方式 0-默认（直接开）1-会诊建议医嘱(宁蒗)
                                    if (csjson.csz) {
                                        wrapper.csqxContent.N03010200201 = csjson.csz;
                                    }
                                    break;
                                case "N03010200202"://会诊结论打印方式 0-默认 1-帆软打印
                                    if (csjson.csz) {
                                        wrapper.csqxContent.N03010200202 = csjson.csz;
                                    }
                                    break;
                            }
                        }
                    }
                } else {
                    malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                }
            })
        },

        //获取当前操作员的拥有科室权限
        getKsbm: function () {
            this.param.ysks = "1";
            this.param.rows = 99999;
            this.param.page = 1;
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify(this.param) + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == 0 && json.d != null) {
                    if (json.d.list.length > 0) {
                        wrapper.ksList = json.d.list;
                        var qbks = {
                            'ksbm': 'all',
                            'ksmc': '全部'
                        };
                        wrapper.ksList.push(qbks);
                        wrapper.param.hzzt = '9';
                        wrapper.param.sqysks = 'all';
                        wrapper.param.dhz = '1';
                        wrapper.getData();
                    }
                } else {
                    malert('获取科室失败', 'top', 'defeadted')
                }
            });
        },
        //请求后台查询列表信息
        getData: function () {
            //加载动画效果
            let shdy = "";
            if (wrapper.sfcy) {
                shdy = 1
            }
            this.param.sfcy = shdy;
            this.param_data=this.param;
            if (!wrapper.sfcy){
                this.param_data.cybeginrq=null;
                this.param_data.cyendrq=null;
            }
            common.openloading('.zui-table-view');
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=queryHzsqList&parm=" + JSON.stringify(this.param_data),
                function (json) {
                    if (json.a == '0' && json.d && json.d.list) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            json.d.list[i].brxb = wrapper.brxb_tran[json.d.list[i].brxb];
                            json.d.list[i].nldw = wrapper.nldw_tran[json.d.list[i].nldw];
                        }
                        wrapper.hzsqList = json.d.list;
                    } else {
                        malert(json.c, 'top', 'defeadted')
                    }
                });

            //数据请求结束时关闭
            common.closeLoading()
        },
        //取消完成
        cancelSave: function (item) {
            var parm = {
                hzbh: item.hzbh,
                hzzt: '1'
            }
            wrapper.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=saveHzgl', JSON.stringify(parm)).then(function (data) {
                if (data.body.a == 0) {
                    malert(data.body.c);
                    wrapper.getData();
                } else {
                    malert(data.body.c, 'top', 'defeadted');
                    return;
                }
            }, function (error) {
                console.log(error);
            });
        },
        searching: function () {
            wrapper.getData();
        },
        commonResultChange: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "hzzt":
                    wrapper.param.hzzt = val[0];
                    this.$forceUpdate();
                    break;
                case "sqysks":
                    wrapper.param.sqysks = val[0];
                    this.$forceUpdate();
                    break;
            }
            wrapper.getData();
        },
        //查看详情
        checkDetail: function (index) {
            wrapper.hzsqList[index].zdsj = this.fDate(wrapper.hzsqList[index].zdsj, 'datetime');
            wrapper.hzsqList[index].ryxm = wrapper.hzsqList[index].yqysxm;
            wrapper.hzsqList[index].hzkssj = this.fDate(wrapper.hzsqList[index].hzkssj, 'datetime');
            wrapper.hzsqList[index].hzjssj = this.fDate(wrapper.hzsqList[index].hzjssj, 'datetime');
            sessionStorage.setItem('hzsqglitem', JSON.stringify(wrapper.hzsqList[index]));
            sessionStorage.setItem('hzcsqxList', JSON.stringify(wrapper.csqxContent));
            this.topNewPage('会诊申请管理', 'page/hzxt/hzxt/hzshf/Subdirectory/apzj.html')
        },
        //患者姓名跳转到电子病历
        Patient: function () {
            // this.topNewPage('患者中心','page/zyysz/zyysz/hzgl/hzzx/hzzx.html')
        },
        //填写会诊报告
        group: function (index) {
            sessionStorage.setItem('dhzglitem_change', new Date().getTime());
            sessionStorage.setItem('dhzglitem_ifEdit', "2");//填写会诊报告
            sessionStorage.setItem('hzbg_targetPatPath', "page/hzxt/hzxt/dhzgl/dhzgl.html");//返回目标地址
            sessionStorage.setItem('dhzglitem', JSON.stringify(wrapper.hzsqList[index]));
            sessionStorage.setItem('hzcsqxList', JSON.stringify(wrapper.csqxContent));
            this.topNewPage('填写会诊报告', 'page/hzxt/hzxt/dhzgl/sub/hzbg.html')
        },
        //诊断报告
        Diagnosis: function () {
            this.topNewPage('诊断报告', 'page/hzxt/hzxt/dhzgl/sub/hzbg.html')
        },

        yzCheck: function (list, val) {
            if (wrapper.csqxContent.N03010200201 == '1') {//建议医嘱模式，目前宁蒗在用
                this.topNewPage("会诊医嘱", 'page/hzxt/hzxt/hzyz/hzzx/hzzx.html?zyh=' + 1);
            } else {
                this.topNewPage(list.brxm, 'page/zyysz/zyysz/hzgl/hzzx/hzzx.html?zyh=' + 1);
            }
            val.push(wrapper.userInfo.ksbm);
            val[2].ksbm = list.sqysks;
            val[2].ksmc = list.sqysksmc;
            val[2].sqysksmc = list.sqysksmc;
            val[2].nldw = this.findKey(this.nldw_tran,list.nldw);
            val[2].ryks = list.ryks;
            val[2].ryksmc = list.ryksmc;
            val[4] = wrapper.is_csqx;
            sessionStorage.setItem('userPage' + 1, JSON.stringify(val))
        },
        fyjz: function (item) {
            sessionStorage.setItem("hszToZygl" + userId, JSON.stringify({
                zyh: item.zyh,
                brxx: item,
                ksbm: item.ryks,
                ksmc: item.ryksmc,
            }));
            this.topNewPage('费用记账', 'page/zygl/rcygl/fyjz/fyjz.html', 'N050022011');
        },
        findKey:function (obj,value, compare = (a, b) => a === b) {
            return Object.keys(obj).find(k => compare(obj[k], value))
        }
    }
});

laydate.render({
    elem: '.times',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        wrapper.param.beginrq = value;
        wrapper.goToPage(1)
    }
});
laydate.render({
    elem: '.times1',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        wrapper.param.endrq = value;
        wrapper.goToPage(1)
    }
});
laydate.render({
    elem: '.times2',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        wrapper.param.cybeginrq = value;
        wrapper.goToPage(1)
    }
});
laydate.render({
    elem: '.times3',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        wrapper.param.cyendrq = value;
        wrapper.goToPage(1)
    }
});





