
var ljbswhhl=new Vue({
    el:'#ljbswhhl',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data:{
        num:0,
        jsonList:[],
        popContent:{
            ljbm:'0001'
        },
    },
    created:function () {
      this.getDataOne()
    },
    watch:{
        'popContent.ljbm':function (newValue,oldValue) {
            sessionStorage.ryljbm=newValue
            getDatayz()
        },
    },
    methods:{
        getDataOne: function () {//路径病重种
            $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhys&types=queryall", function (json) {
                if (json.a == '0') {
                    ljbswhhl.jsonList = json.d.list;
                    // ybglTablelist.jsonList = json.d.list;
                }
            });
        },
        addData:function () {
            addListds()
        },
        save:function () {
        },
        remove:function () {
            removeListData()
        },
        sh:function () {
            shbzData()
        },
        fz:function () {

        },
        dr:function () {

        },
        dc:function () {

        },
        searchHc:function () {

        },
        yl:function () {

        },
        dy:function () {

        },
        yltwo:function () {

        },
        dytwo:function () {

        },
        bc:function () {

        }
    },
})
var ybglTable=new Vue({
    el:'.ybglTable',
    data:{
        num:0,
    },
    created:function () {
        this.$nextTick(function () {
            this.tabBg('lj',0)
        })
    },
    methods:{
        tabBg:function (page,index) {
            var pageDiv = $("."+page);
            if(pageDiv.length == 0){
                $(".loadPage").load(page+".html").fadeIn(300);
            } else {
                $("."+page).fadeIn(300);
            }
        }
    },
})
function  tabBg(page,index,event) {
    $('.isative').removeClass('active')
    ljbswhhl.num=index;
    $(event).addClass('active')
    var pageDiv = $("."+page);
    if(pageDiv.length == 0){
        $(".loadPage").load(page+".html").fadeIn(300);
    } else {
        $("."+page).fadeIn(300);
    }
}
sessionStorage.ryljbm=ljbswhhl.popContent.ljbm