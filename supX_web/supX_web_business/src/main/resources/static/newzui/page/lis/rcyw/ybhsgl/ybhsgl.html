<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>样本审核管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ybhsgl.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper">
    <div class="panel box-fixed">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-hsh paddr-r5" @click="heshou">核收</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-jsh paddr-r5" data-toggle="sideform" data-target="#brzcList" >拒收</button>
            <button class="tong-btn btn-parmary-b icon-zfh paddr-r5" @click="zuofei">作废</button>
            <button class="tong-btn btn-parmary-b icon-dysq paddr-r5" @click="Btnprint">打印</button>
            <!-- <button class="tong-btn btn-parmary-b icon-scybh paddr-r5">生成样本号</button> -->
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">申请日期</label>
                    <div class="zui-input-inline zui-select-inline zui-date">
                        <i class="datenox icon-rl"></i>
                        <input type="text" name="phone" class="zui-input todate padd-l33" v-model="param.time" placeholder="请选择申请日期">
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">条码打印机</label>
                    <div class="zui-input-inline margin-l13">
                        <input class="zui-input wh180" v-model="param.jyxh" placeholder="FAX" type="text"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label padd-l14">检索码</label>
                    <div class="zui-input-inline">
                        <input type="text" class="zui-input" name="input1" placeholder="请输入检索码" v-model="param.bah"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <button class="zui-btn btn-primary  xmzb-db" @click="cx">查询</button>
                </div>
            </div>
        </div>
    </div>

 <div class="zui-table-view ybglTable" id="utable1 zui-table-body" z-height="full" style="border:none;margin-top: 108px;padding: 0 10px; background: #fff">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" z-width="50px">
                        <input-checkbox @result="reCheckBox" :list="'jydjList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="username"   z-width="80px" z-style="text-align:center;">
                        <div class="zui-table-cell">类型</div>
                    </th>
                    <th z-field="sex" z-width="80px">
                        <div class="zui-table-cell">来源</div>
                    </th>
                    <th z-field="sexs" z-width="80px">
                        <div class="zui-table-cell">类别</div>
                    </th>
                    <th z-field="city" z-width="80px">
                        <div class="zui-table-cell">病员姓名</div>
                    </th>
                    <th z-field="sign" z-width="60px">
                        <div class="zui-table-cell">性别</div>
                    </th>
                    <th z-field="experience" z-width="60px">
                        <div class="zui-table-cell">年龄</div>
                    </th>
                    <th z-field="score" z-width="100px">
                        <div class="zui-table-cell">住院号/门诊号</div>
                    </th>
                    <th z-field="classify" z-width="100px">
                        <div class="zui-table-cell">床位号</div>
                    </th>
                    <th z-field="classifyr" z-width="100px">
                        <div class="zui-table-cell">送检科室</div>
                    </th>
                    <th z-field="classifys" z-width="100px">
                        <div class="zui-table-cell">送检医师</div>
                    </th>
                    <th z-field="wealth" z-width="100px">
                        <div class="zui-table-cell">申请项目名称</div>
                    </th>
                    <th z-field="wealthq" z-width="100px">
                        <div class="zui-table-cell">执行设备</div>
                    </th>
                    <th z-field="wealthqw" z-width="100px">
                        <div class="zui-table-cell">申请日期</div>
                    </th>
                    <th z-field="wealtheq" z-width="100px">
                        <div class="zui-table-cell">临床诊断</div>
                    </th>
                    <th z-field="wealthqr" z-width="100px">
                        <div class="zui-table-cell">检验序号</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table">
            <table class="zui-table" >
                <tbody>
                <tr v-for="(item, $index) in jydjList" :tabindex="$index" @click="checkSelect([$index,'some','jydjList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                <td width="50px">
                <div class="zui-table-cell">
                <input-checkbox @result="reCheckBox" :list="'jydjList'"
                :type="'some'" :which="$index"
                :val="isChecked[$index]">
                </input-checkbox>
                </div>
                </td>
                <td width="80px"><div class="zui-table-cell" v-text="jydjlx_tran[item.lx]"></div></td>
                <td width="90px"><div class="zui-table-cell" v-text="jydjly_tran[item.ly]"></div></td>
                <td width="80px"><div class="zui-table-cell" v-text="jydjyblx_tran[item.yblx]"></div></td>
                <td width="80px"><div class="zui-table-cell" v-text="item.brxm"></div></td>
                <td width="60px"><div class="zui-table-cell" v-text="brxb_tran[item.xb]"></div></td>
                <td width="60px"><div class="zui-table-cell title" :data-title="item.nl" v-text="item.nl"></div></td>
                <td width="100px"><div class="zui-table-cell" v-text="item.bah"></div></td>
                <td width="90px"><div class="zui-table-cell" v-text="item.cwh"></div></td>
                <td width="90px"><div class="zui-table-cell" v-text="item.ksmc"></div></td>
                <td width="90px"><div class="zui-table-cell" v-text="item.sqys"></div></td>
                <td width="90px"><div class="zui-table-cell" v-text="item.jyxmmc"></div></td>
                <td width="100px"><div class="zui-table-cell"style="margin-top: -8px;">
                    <select-input @change-data="resultChange"  :not_empty="true"
                                  :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="item.zxsb"
                                  :search="true" :name="'item.zxsb'">
                    </select-input>
                </div></td>
                <td width="100px"><div class="zui-table-cell" >{{item.sqrq|formDate}}</div></td>
                <td width="100px"><div class="zui-table-cell" v-text="item.lczd"></div></td>
                <td width="100px"><div class="zui-table-cell" v-text="item.jyxh"></div></td>
                <td width="100px"><div class="zui-table-cell">
                    <i class="icon-hs icon-font"></i>
                    <i class="icon-js icon-font"></i>
                    <i class="icon-zf icon-font"></i></div>
                </td>
                </tr>
                </tbody>
            </table>
        </div>
     <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>


</div>
<div class="popus-right side-form ng-hide" style="width: 320px; padding-top: 0; z-index: 9999; top:0px;bottom: 0px;height: auto;"  id="brzcList" role="form" >
    <h2><span>拒收理由</span>
        <div class="setwin">
            <a href="javascript:;" class="fr closex ti-close"></a>
        </div>


    </h2>
    <div class="sample">样本拒收原因</div>
    <div class="sample-select">
        <select v-model="jsyy">
            <option value="样本量过少">样本量过少</option>
            <option value="样本污染">样本污染</option>
            <option value="样本不合格">样本不合格</option>
            <option value="样本疑稀释">样本疑稀释</option>
            <option value="样本量过多">样本量过多</option>
            <option value="样本有凝块">样本有凝块</option>
            <option value="样本抗凝剂选择错误">样本抗凝剂选择错误</option>
            <option value="样本类型错误">样本类型错误</option>
            <option value="样本容器错误">样本容器错误</option>
        </select>
    </div>
    <div class="sample">拒收处理</div>
    <div class=" sample-texarea">
        <textarea v-model="jscl" placeholder="请输入拒收处理意见"></textarea>
    </div>
    <div class="pop-ok sample-btn">
        <button class="pop-btn" @click="close">取消</button>
        <button class="pop-btn pop-confirm" @click="saveOk">确定</button>
    </div>

</div>
<div class="isTabel">
    <div class="table " style="height: 496px;" v-show="isTabelShow">
        <div class="poptable">
            <div class="header">
                <h3 class="titel"><span>
                    已核收样本（{{hsjlListLength}}份）
                </span>
                    <i class="icon-shanchu" @click="showDom"></i>
                </h3>
            </div>
            <div   style="border:none;">
                <div class="zui-table-header" style="width: 100%!important;">
                    <table class="zui-table" style="width: 100%;">
                        <thead>
                        <tr class="edf2f1" style="height: 34px">
                            <th z-field="id" z-width="90px" z-sort="true">
                                <div class="zui-table-cell"><span>类型</span></div>
                            </th>
                            <th z-field="username"   z-width="100px" z-style="text-align:center;">
                                <div class="zui-table-cell"  style="height: 36px;line-height: 36px;"><span>来源</span></div>
                            </th>
                            <th z-field="city" z-width="80px">
                                <div class="zui-table-cell"  style="height: 36px;line-height: 36px;"><span>类别</span></div>
                            </th>
                            <th z-field="sign" z-width="120px" >
                                <div class="zui-table-cell"  style="height: 36px;line-height: 36px;"><span>病员姓名</span></div>
                            </th>
                            <th z-field="experience" z-width="100px">
                                <div class="zui-table-cell"  style="height: 36px;line-height: 36px;"><span>病床号</span></div>
                            </th>
                            <th z-field="score" z-width="100px">
                                <div class="zui-table-cell"  style="height: 36px;line-height: 36px;"><span>年龄</span></div>
                            </th>
                            <th z-field="classify" z-width="150px">
                                <div class="zui-table-cell"  style="height: 36px;line-height: 36px;"><span>住院号/门诊号</span></div>
                            </th>
                            <th z-field="leixing" z-width="100px">
                                <div class="zui-table-cell"  style="height: 36px;line-height: 36px;"><span>申请项目名称</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body nowData">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in hsjlList">
                            <td><div class="zui-table-cell" v-text="jydjlx_tran[item.lx]"></div></td>
                            <td><div class="zui-table-cell" v-text="jydjly_tran[item.ly]"></div></td>
                            <td><div class="zui-table-cell" v-text="jydjyblx_tran[item.yblx]"></div></td>
                            <td><div class="zui-table-cell" v-text="item.brxm"></div></td>
                            <td><div class="zui-table-cell title" v-text="item.cwh"></div></td>
                            <td><div class="zui-table-cell" v-text="item.nl"></div></td>
                            <td><div class="zui-table-cell" v-text="item.zyh"></div></td>
                            <td><div class="zui-table-cell" v-text="item.jyxmmc"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
    <div class="min-chuangkou" v-show="minishow">
        <p><span class="text-min">已核收样本（{{hsjlListLength}}份）</span> <span class="icon-min" @click="tabshow"></span></p>
    </div>
</div>
    <div id="pop">
        <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
        <div class="zui-form podrag  bcsz-layer " :class="[{show:isShow},flag ?'pop-850':'pop-width']" style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title"></div>
            <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
            <div class="layui-layer-content">
                <div class=" layui-mad layui-height" v-text="ts" >

                </div>
            </div>
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="isShowpopL=false,isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save xmzb-db"   v-if="zfShow" @click="zuofeiok">确定</button>
                <button class="zui-btn btn-primary table_db_save xmzb-db" v-else @click="heshouok">核收</button>
            </div>
        </div>
    </div>

<script src="ybhsgl.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
