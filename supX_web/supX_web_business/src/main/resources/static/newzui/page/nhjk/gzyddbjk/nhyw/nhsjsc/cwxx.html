<div id="cwxx" class="padd-l-10 margin-top-10">
    <div class=" flex-container padd-b-10">
        <button class="tong-btn btn-parmary" @click="getData">刷新</button>
    </div>
    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th><div class="zui-table-cell cell-s">序号</div></th>
                    <th><div class="zui-table-cell cell-s">住院号</div></th>
                    <th><div class="zui-table-cell cell-s">患者姓名</div></th>
                    <th><div class="zui-table-cell cell-s">错误信息</div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one over-auto"   @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                    <td><div  class="zui-table-cell cell-s">{{$index+1}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.ksbm}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.ksmc}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.pydm}}</div></td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<script type="application/javascript" src="cwxx.js"></script>