<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>基础数据</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="jcsj.css" rel="stylesheet">
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10" style="overflow: auto">
<div class="wrapper" id="jyxm_icon">
    <div class="panel" id="jcsj">
        <div class="tong-top">
            <button @click="addData" :disabled="num==0?false:num==1?false:true" :class="{bgOpct:num==0?false:num==1?false:true}" class="tong-btn btn-parmary icon-xz1 paddr-r5">新增</button>
            <button @click="getData()" class="tong-btn btn-parmary-b icon-sx paddr-r5">刷新</button>
            <button @click="save()" class="tong-btn btn-parmary-b icon-baocun paddr-r5">保存</button>
            <button @click="remove()" :disabled="num==0?false:num==1?false:true" :class="{bgOpct:num==0?false:num==1?false:true}" class="tong-btn btn-parmary-b paddr-r5 icon-sc-header">删除</button>
            <button @click="gl" class="tong-btn btn-parmary-b paddr-r5 icon-gl">过滤</button>
            <button @click="yl" class="tong-btn btn-parmary-b paddr-r5 icon-dc icon-yl">预览</button>
            <button @click="dy" class="tong-btn btn-parmary-b paddr-r5 icon-dc icon-dysq">打印</button>
        </div>
        <div class="tong-search" v-show="searchMessage">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">路径检索</label>
                    <div class="zui-input-inline margin-l13">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">疾病检索</label>
                    <div class="zui-input-inline margin-l13">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">疾病检索</label>
                    <div class="zui-input-inline margin-l13">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full">
        <div class="fyxm-tab">
            <div><span :class="{'active':num==0}" @click="tabBg(0)">科室对应</span></div>
            <div><span :class="{'active':num==1}" @click="tabBg(1)">病种对应</span></div>
            <div><span :class="{'active':num==2}" @click="tabBg(2)">疾病编码对应</span></div>
            <div><span :class="{'active':num==3}" @click="tabBg(3)">手术编码对应</span></div>
            <div><span :class="{'active':num==4}" @click="tabBg(4)">报表对应病种</span></div>
        </div>
        <!--
        	作者：offline
        	时间：2018-07-30
        	描述：科室对应
        -->
        <div class="fyxm-size" v-if="num==0">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                            <input-checkbox @result="reCheckBox" :list="'ksdyJsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th z-field="a1" z-width="60px" z-style="text-align:center;">
                            <div class="zui-table-cell">序号</div>
                        </th>
                        <th z-field="a2" z-width="100px">
                            <div class="zui-table-cell">路径疾病名称</div>
                        </th>
                        <th z-field="a3" z-width="100px">
                            <div class="zui-table-cell">科室名称</div>
                        </th>
                        <th z-field="a4" z-fixed="right" z-width="100px">
                            <div class="zui-table-cell">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body body-height">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in ksdyJsonList" @click="checkSelect([$index,'some','ksdyJsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index">
                        <td width="50px">
                            <div class="zui-table-cell">
                                <input-checkbox @result="reCheckBox" :list="'ksdyJsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell" v-text="$index+1"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell" v-text="item.ryljmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell" v-text="item.ryksmc"></div>
                        </td>
                        <td width="100px">
                            <div class="zui-table-cell">
                                <i class="icon-bj" @click="bj(item)"></i>
                                <i class="icon-sc icon-font" @click="remove(item)"></i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!--
        	作者：offline
        	时间：2018-07-30
        	描述：病种对应
        -->
        <div class="fyxm-size" v-if="num==1">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                            <input-checkbox @result="reCheckBox" :list="'bzdyJsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th z-field="a1" z-width="60px" z-style="text-align:center;">
                            <div class="zui-table-cell">序号</div>
                        </th>
                        <th z-field="a2" z-width="100px">
                            <div class="zui-table-cell">路径名称</div>
                        </th>
                        <th z-field="a3" z-width="100px">
                            <div class="zui-table-cell">疾病编码</div>
                        </th>
                        <th z-field="a3" z-width="100px">
                            <div class="zui-table-cell">疾病名称</div>
                        </th>
                        <th z-field="a5" z-fixed="right" z-width="100px">
                            <div class="zui-table-cell">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body body-height" id="zui-table">
                <table class="zui-table table-width50">
                    <tbody>
	                    <tr v-for="(item, $index) in bzdyJsonList">
		                    <td width="50px">
			                    <div class="zui-table-cell">
				                    <input-checkbox @result="reCheckBox" :list="'bzdyJsonList'"
				                    :type="'some'" :which="$index"
				                    :val="isChecked[$index]">
				                    </input-checkbox>
			                    </div>
		                    </td>
		                    <td>
		                    	<div class="zui-table-cell" v-text="$index+1">1</div>
		                    </td>
		                    <td>
		                    	<div class="zui-table-cell" v-text="item.ryljmc">001</div>
		                    </td>
		                    <td>
		                    	<div class="zui-table-cell" v-text="item.ryjbbm">001</div>
		                    </td>
		                    <td>
		                    	<div class="zui-table-cell" v-text="item.ryjbmc">001</div>
		                    </td>
		                    <td width="100px">
			                    <div class="zui-table-cell">
				                    <i class="icon-bj" @click="bj(item)"></i>
				                    <i class="icon-sc icon-font" @click="remove(item)"></i>
			                    </div>
		                    </td>
	                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--
        	作者：offline
        	时间：2018-07-30
        	描述：疾病编码对应
        -->
        <div class="fyxm-size" v-if="num==2">
            <div class="col-x-12">
                <div class="col-4">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th z-field="a1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">编码</div>
                                </th>
                                <th z-field="a2" z-width="100px">
                                    <div class="zui-table-cell">名称</div>
                                </th>
                                <th z-field="a3" z-width="100px">
                                    <div class="zui-table-cell">路径分类</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body ">
                        <table class="zui-table table-width50">
                            <tbody>
                            <tr v-for="(item, $index) in jbdyLjJsonList" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index"  @click="getDatathree(item),getDataChild(item),checkSelect([$index,'one','jbdyLjJsonList'],$event)" >
                                <td>
                                    <div class="zui-table-cell" v-text="item.ljbm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell" v-text="item.ljmc">001</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell" v-text="ljbzfl_tran[item.fl]">001</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-4">
                    <div class="zui-table-header">
                        <table class="zui-table ">
                            <thead>
                            <tr>
                                <th z-field="b1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">疾病编码</div>
                                </th>
                                <th z-field="b2" z-width="100px">
                                    <div class="zui-table-cell">临床表现</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body">
                        <table class="zui-table ">
                            <tbody>
                            <tr v-for="(item, $index) in jbdyJbJsonList" tabindex="$index" @click="checkSelect([$index,'one','jbdyJbJsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                            <td>
                            <div class="zui-table-cell" v-text="item.jbmb">001</div>
                            </td>
                            <td>
                            <div class="zui-table-cell" v-text="item.lcbx">001</div>
                            </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-x-4">
                    <div class="zui-table-header">
                        <table class="zui-table ">
                            <thead>
                            <tr>
                                <th z-field="c1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">疾病编码</div>
                                </th>
                                <th z-field="c2" z-width="100px">
                                    <div class="zui-table-cell">疾病名称</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body">
                        <table class="zui-table ">
                            <tbody>
                            <tr v-for="(item, $index) in jbdyJgJsonList" @dblclick="remove(item)" :tabindex="$index" @click="checkSelect([$index,'one','jbdyJgJsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                            <td>
                            <div class="zui-table-cell" v-text="item.ryjbbm">1</div>
                            </td>
                            <td>
                            <div class="zui-table-cell" v-text="item.ryjbmc">001</div>
                            </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!--
        	作者：offline
        	时间：2018-07-30
        	描述：手术编码对应
        -->
        <div class="fyxm-size" v-if="num==3">
            <div class="col-x-12">
                <div class="col-4">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th z-field="a1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">编码</div>
                                </th>
                                <th z-field="a2" z-width="100px">
                                    <div class="zui-table-cell">名称</div>
                                </th>
                                <th z-field="a2" z-width="100px">
                                    <div class="zui-table-cell">路径分类</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body body-height" id="zui-table">
                        <table class="zui-table table-width50">
                            <tbody>
                            <!--<tr v-for="(item, $index) in sjljsonList"  -->
                            <!-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="$index+1">1</div>-->
                            <!--</td>-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--</tr>-->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-4">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th z-field="b1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">编码</div>
                                </th>
                                <th z-field="b2" z-width="100px">
                                    <div class="zui-table-cell">名称</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body body-height" id="zui-table">
                        <table class="zui-table table-width50">
                            <tbody>
                            <!--<tr v-for="(item, $index) in sjcjsonList" -->
                            <!-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--</tr>-->

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-x-4">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th z-field="c1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">手术编码</div>
                                </th>
                                <th z-field="c2" z-width="100px">
                                    <div class="zui-table-cell">手术名称</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body body-height" id="zui-table">
                        <table class="zui-table table-width50">
                            <tbody>
                            <!--<tr v-for="(item, $index) in sjlrjsonList" -->
                            <!-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="$index+1">1</div>-->
                            <!--</td>-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--</tr>-->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!--
        	作者：offline
        	时间：2018-07-30
        	描述：报表对应病种
        -->
        <div class="fyxm-size" v-if="num==4">
            <div class="col-x-12">
                <div class="col-4">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th z-field="a1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">序号</div>
                                </th>
                                <th z-field="a2" z-width="100px">
                                    <div class="zui-table-cell">报表名称</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body body-height" id="zui-table">
                        <table class="zui-table table-width50">
                            <tbody>
                            <!--<tr v-for="(item, $index) in bbljsonList" -->
                            <!-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="$index+1">1</div>-->
                            <!--</td>-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--</tr>-->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-4">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th z-field="b1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">编码</div>
                                </th>
                                <th z-field="b2" z-width="100px">
                                    <div class="zui-table-cell">名称</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body body-height" id="zui-table">
                        <table class="zui-table table-width50">
                            <tbody>
                            <!--<tr v-for="(item, $index) in bbcjsonList"  -->
                            <!-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--</tr>-->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-x-4">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th z-field="c1" z-width="60px" z-style="text-align:center;">
                                    <div class="zui-table-cell">序号</div>
                                </th>
                                <th z-field="c2" z-width="100px">
                                    <div class="zui-table-cell">显示序号</div>
                                </th>
                                <th z-field="c2" z-width="100px">
                                    <div class="zui-table-cell">病种</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body body-height" id="zui-table">
                        <table class="zui-table table-width50">
                            <tbody>
                            <!--<tr v-for="(item, $index) in bbrjsonList"  -->
                            <!-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="$index+1">1</div>-->
                            <!--</td>-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell" v-text="item.ejkmbm">001</div>-->
                            <!--</td>-->
                            <!--</tr>-->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

    <!-- 右侧弹窗 -->
    <div class="side-form  pop-width" :class="{'ng-hide':index==1}" style="padding-top: 0;" id="brzcList" role="form">
        <div class="fyxm-side-top"><span>{{title}}</span> <span class="fr closex ti-close" @click="closes"></span></div>

        <div class="ksys-side" v-if="num==0">
	        <span class="span0">
	            <i>临床路径</i>
               <select-input @change-data="resultChange" :not_empty="false"
                 :child="ljJsonList" :index="'ljmc'" :index_val="'ljbm'" :val="ksdyPopContent.ryljbm"
                 :name="'ksdyPopContent.ryljbm'" :search="true">
               </select-input>
	        </span>
	        <span class="span0">
	                <!--:child为要循环展示的list，:index为传入要展示内容的索引，:index_val为要修改的值的索引，-->
	                <!--:val为要修改的值，:search为是否需要检索，:name为要赋值的参数名, :disable默认为false-->
	        	<i>适用科室</i>
	            <select-input @change-data="resultChange" :not_empty="false"
	              :child="ksJsonList" :index="'ksmc'" :index_val="'ksbm'" :val="ksdyPopContent.ryksbm"
	              :name="'ksdyPopContent.ryksbm'" :search="true">
	            </select-input>
	        </span>
        </div>

        <div class="ksys-side" v-if="num==1">
	        <span class="span0">
	            <i>临床路径</i>
	            <select-input @change-data="resultChange" :not_empty="true"
                  :child="ljJsonList" :index="'ljmc'" :index_val="'ljbm'" :val="bzdyPopContent.ryljbm"
                  :name="'bzdyPopContent.ryljbm'" :search="true">
                </select-input>
	        </span>
	            <span class="span0">
	            <i>疾病编码</i>
	            <input class="zui-input" v-model="bzdyPopContent.ryjbmc" data-notEmpty="true"
                                   @input="searching(null,$event.target.value)"
                                   @keyDown="changeDown($event,'ryjbmc','ryjbbm')">
                <search-table :message="searchCon" :selected="selSearch" :them="them" :page="page"
                              @click-one="checkedOneOut" @click-two="selectOne">
                </search-table>
	        </span>
	            <!-- <span class="span0">
	            <i>结束疾病编码</i>
	            <input class="zui-input" v-model="bzdyPopContent.ryjsjbmc" data-notEmpty="false"
                                   @input="searching(null,$event.target.value)"
                                   @keyDown="changeDown($event,'ryjsjbmc','ryjsjbbm')">
                <search-table1 :message="searchCon" :selected="selSearch" :them="them" :page="page"
                              @click-one="checkedOneOut" @click-two="selectOne1">
                </search-table1>
	        </span> -->
        </div>

        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
        </div>
    </div>

    <!-- 过滤弹窗 -->
    <div id="isTabel">
        <div class="pophide" :class="{'show':isShow}"></div>
        <div class="zui-form podrag  bcsz-layer zui-800 " :class="{'show':isShow}"
             style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title ">过滤查询</div>
            <div class="guolv-xinzeng">
                <span class="layui-txt" @click="append()">新增一项</span>
                <i class="color-btn" @click="isShow=false"
                   style="margin-top:-17px;width: 16px;height: 16px;display: inline-block;margin-left: 10px;float: right">×</i>
            </div>
            <div class="layui-layer-content">
                <div class=" layui-mad">
                    <ul class="guolv-header guolv-style">
                        <li class="line">关系符</li>
                        <li class="line">条件</li>
                        <li class="line">结果</li>
                        <li class="line">输入值</li>
                        <li class="line">操作</li>
                    </ul>
                    <ui class="guolv-content" id="guo_append">
                        <div class="guolv-style guolv-bottom" v-for="(item, $index) in cxtjList">
                            <li class="line">
                                <div class="zui-select-inline">
                                    <select-input :id="'ljtj_' + $index"
                                                  @change-data="resultChangeLjtj_item" :not_empty="true"
                                                  :child="ljtjybgl_tran" :index="'item.ljtj'" :val="item.ljtj"
                                                  :name="$index + '.ljtj.' + 1" :search="true"
                                                  @keydown="nextFocus($event)"
                                                  data-notEmpty="false">
                                    </select-input>
                                </div>
                            </li>
                            <li class="line">
                                <div class="zui-select-inline">
                                    <select-input :id="'xm_' + $index"
                                                  @change-data="resultChange_item" :not_empty="true"
                                                  :child="xmybgl_tran" :index="'item.xm'" :val="item.xm"
                                                  :name="$index + '.xm.' + 1" :search="true"
                                                  @keydown="nextFocus($event)"
                                                  data-notEmpty="false">
                                    </select-input>
                                </div>
                            </li>
                            <li class="line">
                                <div class="zui-select-inline">
                                    <select-input :id="'tj_' + $index"
                                                  @change-data="resultChangeTj_item" :not_empty="true"
                                                  :child="tjybgl_tran" :index="'item.tj'" :val="item.tj"
                                                  :name="$index + '.tj.' + 1" :search="true"
                                                  @keydown="nextFocus($event)"
                                                  data-notEmpty="false">
                                    </select-input>
                                </div>
                            </li>
                            <li class="line">
                                <div class="zui-select-inline">
                                    <select-input :id="'tj_' + $index"
                                                  @change-data="resultChangeTj_item" :not_empty="true"
                                                  :child="tjybgl_tran" :index="'item.tj'" :val="item.tj"
                                                  :name="$index + '.tj.' + 1" :search="true"
                                                  @keydown="nextFocus($event)"
                                                  data-notEmpty="false">
                                    </select-input>
                                </div>
                            </li>
                            <!--<li class="line">-->
                            <!--&lt;!&ndash; 类型 &ndash;&gt;-->
                            <!--<div class="zui-select-inline" v-if="isLxNum.indexOf(''+$index)>=0">-->
                            <!--<select-input :id="'LX_' + $index"-->
                            <!--@change-data="resultChangeTj_item" :not_empty="true"-->
                            <!--:child="jydjlx_tran" :index="'item.jg'" :val="item.jg"-->
                            <!--:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"-->
                            <!--data-notEmpty="false">-->
                            <!--</select-input>-->
                            <!--</div>-->
                            <!--&lt;!&ndash; 输入框通用 &ndash;&gt;-->
                            <!--<div class="zui-select-inline" v-if="isTyNum.indexOf(''+$index)>=0">-->
                            <!--<input type="text" class="zui-input" v-model="item.jg"/>-->
                            <!--</div>-->
                            <!--&lt;!&ndash; 性别 &ndash;&gt;-->
                            <!--<div class="zui-select-inline" v-if="isXbNum.indexOf(''+$index)>=0">-->
                            <!--<select-input :id="'XB_' + $index"-->
                            <!--@change-data="resultChangeTj_item" :not_empty="true"-->
                            <!--:child="xtwhxb_tran" :index="'item.jg'" :val="item.jg"-->
                            <!--:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"-->
                            <!--data-notEmpty="false">-->
                            <!--</select-input>-->
                            <!--</div>-->
                            <!--&lt;!&ndash; 科室 &ndash;&gt;-->
                            <!--<div class="zui-select-inline" v-if="isKsNum.indexOf(''+$index)>=0">-->
                            <!--<select-input @change-data="Wf_YppfChange" :not_empty="false"-->
                            <!--:child="util.sjks" :index="'ksmc'" :index_val="'ksbm'"-->
                            <!--:val="item.jg" :name="'PfxxJson.'+$index+'.ksbm'" :index_mc="'ksmc'" :search="true">-->
                            <!--</select-input>-->
                            <!--</div>-->
                            <!--&lt;!&ndash; 医生 &ndash;&gt;-->
                            <!--<div class="zui-select-inline" v-if="isYsNum.indexOf(''+$index)>=0">-->
                            <!--<select-input @change-data="Wf_YsChange" :not_empty="false"-->
                            <!--:child="util.sjys" :index="'ysmc'" :index_val="'ysbm'"-->
                            <!--:val="item.jg" :name="'PfxxJsonx.'+$index+'.ysbm'" :index_mc="'ysmc'" :search="true">-->
                            <!--</select-input>-->
                            <!--</div>-->
                            <!--&lt;!&ndash; 样本类型 &ndash;&gt;-->
                            <!--<div class="zui-select-inline" v-if="isYblxNum.indexOf(''+$index)>=0">-->
                            <!--<select-input :id="'XB_' + $index"-->
                            <!--@change-data="resultChangeTj_item" :not_empty="true"-->
                            <!--:child="jydjyblx_tran" :index="'item.jg'" :val="item.jg"-->
                            <!--:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"-->
                            <!--data-notEmpty="false">-->
                            <!--</select-input>-->
                            <!--</div>-->

                            <!--</li>-->
                            <li class="line">
                                <span class="icon-sc" @click="sc($index)"></span>
                            </li>
                        </div>

                    </ui>
                </div>
            </div>
            <div class="zui-row buttonbox">
                <div class="left-radio">
                    <p class="text-left"><label class="green-radio"><input class="green-radius" type="radio" name="a"
                                                                           checked><i></i><span>重新过滤</span></label></p>
                    <p class="text-left"><label class="green-radio"><input type="radio"
                                                                           name="a"><i></i><span>在上次过滤结果中查找</span></label>
                    </p>
                    <p class="text-left"><label class="green-radio"><input type="radio" name="a"><i></i><span>本次过滤上次过滤的交集</span></label>
                    </p>
                </div>
                <div class="right">
                    <button class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
                    <button class="zui-btn btn-primary table_db_save" @click="save">保存</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="jcsj.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
