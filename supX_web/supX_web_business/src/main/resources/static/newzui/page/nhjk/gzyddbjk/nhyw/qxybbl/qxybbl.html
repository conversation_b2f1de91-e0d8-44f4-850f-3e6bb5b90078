<html>
<head>
    <script type="application/javascript" src="/pub/top.js"></script>
    <title>取消上传</title>
    <link rel="stylesheet" href="/page/xtwh/xtpz/mzpjgs/mzpjgs.css"/>
    <link href="qxsc.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div class="toolMenu">
    <button @click="getData"><span class="fa fa-refresh"></span>刷新</button>
    <button @click="remove"><span class="fa fa-trash-o"></span>取消上传</button>
</div>
<!-- 入院信息列表展示 -->
<div id="mztf">
    <!-- 检索 -->
    <div style="padding: 0 0 5px 3px;">
        <span>当前科室：</span>
        <select id="ksList" v-model="ksbm" @change="ksChange">
             <option v-for="option in ksList" :value="option.ksbm" v-text="option.ksmc"></option>
        </select>
        <!-- 
        <span>入院日期：</span>
        <input id="startRq" onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" readonly="readonly"
               style="width: 120px;height: 25px;margin-left: 10px" >
        至<input id="endtRq" onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" readonly="readonly"
                style="width: 120px;height: 25px;margin-left: 10px" >
        <button @click="searchListDj()">检索</button>
         -->
    </div>

    <div class="tableDiv brList">
        <table class="patientTable" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;">
            <tr>
                <th class="tableNo"></th>
                <th>病人姓名</th>
                <th>住院号</th>
                <th>保险类别</th>
            </tr>
            </thead>
            <tr>
                <th v-for="item in 4"></th>
            </tr>
            <tr v-for="(item, $index) in brList" @click="checkOne($index)"
                :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" >
                <th class="tableNo" v-text="$index+1"></th>
                <td v-text="item.brxm"></td>
                <td v-text="item.brxm"></td>
                <td v-text="item.bxlbmc"></td>
            </tr>
        </table>
    </div>

    <div class="tableDiv xqList">
        <table class="patientTable" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;">
            <tr>
                <th class="tableNo"></th>
                <th>项目名称</th>
                <th>类别</th>
                <th>农保类别</th>
                <th>单价</th>
                <th>数量</th>
                <th>金额</th>
                <th>项目编码</th>
                <th>记录ID</th>
                <th>收费日期</th>
                <th>操作员</th>
                <th>住院医生</th>
                <th>医嘱序号</th>
                <th>退费ID</th>
                <th>保内外</th>
            </tr>
            </thead>
            <tr>
                <th v-for="item in 16"></th>
            </tr>
            <tr v-for="(item, $index) in fyList" @click="fycheckOne($index)"
                :class="[{'tableTrSelect':fyisChecked[$index]},{'tableTr': $index%2 == 0}]">
                <th class="tableNo" v-text="$index+1"></th>
                <td v-text="item.mxfyxmmc"></td>
                <td v-text="item.fylbmc"></td>
                <td v-text="item.mc"></td>
                <td v-text="item.fydj"></td>
                <td v-text="item.fysl"></td>
                <td v-text="item.fyje"></td>
                <td v-text="item.mxfyxmbm"></td>
                <td v-text="item.fyid"></td>
                <td v-text="fDate(item.sfrq,'yyyy-MM-dd')"></td>
                <td v-text="item.czyxm"></td>
                <td v-text="item.zyysxm"></td>
                <td v-text="item.yzxh"></td>
                <td v-text="item.tfid"></td>
                <td v-text="nhtclb_tran[item.fw]">
            </tr>
        </table>
    </div>
</div>
</body>
<script type="text/javascript" src="qxsc.js"></script>
</html>
