
.fydm-content-left{
  width:49%;
  float: left;
  .content-left-top{
    width: 100%;
    i{
      width: calc(~"(100% / 6)");
      text-align: center;
    }
  }
  .sjks-content-left-list{
    li{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      i{
        width: calc(~"(100% / 6)");
        text-align: center;
      }
      &:hover{
        background:rgba(26,188,156,0.08);
        border:1px solid #1abc9c;
      }
    }
  }
}
.pop-content{
  width:100%;
  float: right;
  .content-right-top,.content-right-list{
    width: 100%;
    i,span{
      width: calc(~"(100% / 3)");
      text-align: center;
      em{
        margin-top: 10px;
      }
    }
  }
  li{
    cursor: pointer;
    width: 100%;
    &:hover{
      background:rgba(26,188,156,0.08) !important;
      border:1px solid #1abc9c;
    }
  }
}
.ksys-side{
  width: 100%;
  padding: 26px 17px;
  float: left;
  .jiansuo{
    margin-bottom: 20px;
  }
  .abs{
    color: #1abc9c;
  }
  .absolate{
    color: #1abc9c;
    right: 48px;
    //display: flex;
    //width: 100%;
    //justify-content: center;
    //position: relative;
    i{
      width: 56px;
      float: left;
      align-items: center;
      align-self: center;
    }
  }
  .left-right{
    min-width: auto;
    max-width: none;
    padding: 0;
    margin: 0px 5px;
  }
  #jyxm_icon .switch{
    top:0;
    left:17px;
  }

}
.border-r4{
  border-radius: 4px !important;
}
.ksys-btn{
  position: absolute;
  bottom: 20px;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 40px;
  button{
    margin-right: 20px;
  }
}
.xmzb-top{
  width: 100%;
  padding: 15px 34px;
}
.xmzb-top-left{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  i{
    margin-right: 5px;
    &:nth-child(2){
      margin-right: 19px;
    }
  }
}

.xmzb-content{
  width: 100%;
  padding:15px 10px;
  box-sizing: border-box;
  float: left;
}
.xmzb-content-left{
  width: 35%;
  float: left;
  .content-left-top{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    border: 1px solid #e9eee6;
    background:#edf2f1;
    height: 36px;
    line-height: 36px;
    align-items: center;
    i{
      width: calc(~"(100% / 3)");
      text-align: center;
    }
  }
  .content-left-list{
    width: 100%;
    height:80vh;
    overflow: auto;
    border: 1px solid #e9eee6;
    border-top: none;
    border-right: none;
    li{
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      //line-height: 54px;
      height: 54px;
      border-top: 1px solid #e9eee6;
      i{
        width: calc(~"(100% / 3)");
        text-align: center;
      }
      &:nth-child(2n){
        background: #fdfdfd;
      }
      &:first-child{
        border-top: none;
      }
    }

  }
}
.xmzb-content-right{
  width: 100%;
  float: right;
  .content-right-top{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    border: 1px solid #e9eee6;
    background:#edf2f1;
    height: 36px;
    //line-height: 36px;
    align-items: center;
    i{
      width: calc(~"(100% - 50px)/6");
      text-align: center;
    }
  }
  .content-right-list{
    width: 100%;
    height:80vh;
    overflow: auto;
    li{
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      line-height: 54px;
      border: 1px solid #e9eee6;
      border-top: none;
      i{
        width: calc(~"(100% -50px)/6");
        text-align: center;
      }
      &:nth-child(2n){
        background: #fdfdfd;
      }
      &:first-child{
        border-top: none;
      }
    }
  }

}
.xmzb-title{
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background:#edf2f1;
  border:1px solid #e9eee6;
  height:34px;
  i{
    width: calc(~"(100% / 5)");
    text-align: center;
  }
}
.xmzb-list{
  width: 100%;
  max-height: 320px;
  overflow: auto;
  li{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 54px;
    border-top: 1px solid #e9eee6;
    &:nth-child(2n){
      background: #fdfdfd;
    }
    &:first-child{
      border-top: none;
    }
  }
  i{
    width: calc(~"(100% / 5)");
    text-align: center;
  }
}
.font16{
  font-size: 16px !important;
}
.xmzb-ok{
  width: 100%;
  height: 70px;
  border-top: 1px solid #e9eee6;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  button{
    margin-right: 15px;
  }
}
.font-icon{
  position: absolute;
  right:90px;
  top:3px;
  color: rgba(255,255,255,.8);
}
.icon-jia{
  position: relative;
  opacity: 0.56;
}
.icon-jia:before{
  position: absolute;
  left: -30px;
  top: -3px;
  width: 22px;
  height: 22px;
  content: '';
  border-radius: 100%;
  border: 1px solid #ffffff;
}
.icon-jia:after{
  position: absolute;
  content: '+';
  font-size: 22px;
  color: #fff5e6;
  left: -26px;
  top: -5px;
}