body{
    font-family:PingFangSC-Semibold;
}
.xmzb-top {
    width: 100%;
    /*overflow: hidden;*/
}
.xmzb-top-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 27px;
    height: 64px;
}
.xmzb-top-left i {
    margin-right: 5px;
}
.xmzb-top-left i:nth-child(2) {
    margin-right: 18px;
}
.xmzb-content {
    width: 100%;
    box-sizing: border-box;
    float: left;
    background: #fff;
    min-width: 1024px;
    overflow-x: auto;
}
.xmzb-content-left {
    width: 36.7%;
    float: left;
}
.xmzb-content-left .content-left-top {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    border: 1px solid #e9eee6;
    background: #edf2f1;
    height: 36px;
    line-height: 36px;
    align-items: center;
}
.xmzb-content-left .content-left-top .i {
    width: calc((100% - 50px)/ 3);
    text-align: center;
    font-size:14px;
    color:#333333;
}
.xmzb-content-left .content-left-list {
    width: 100%;
    height: 75vh;
    overflow: auto;
    border-top: none;
    border-right: none;
}
.xmzb-content-left .content-left-list li {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    height: 40px;
    border: 1px solid #e9eee6;
    border-top: 1px solid #fff;
}
.xmzb-content-left .content-left-list li .i {
    width: calc((100% - 50px)/ 3);
    text-align: center;
    font-size:14px;
    color:#757c83;
}
.xmzb-content-left .content-left-list li:nth-child(2n) {
    background: #fdfdfd;
}
.xmzb-content-left .content-left-list li:first-child {
    border-top: none;
}
.xmzb-content-left .content-left-list li:hover {
    background: rgba(26, 188, 156, 0.08);
}
.xmzb-content-right {
    width: 63%;
    float: right;
}
.xmzb-content-right .content-right-top {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    border: 1px solid #e9eee6;
    background: #edf2f1;
    height: 36px;
    align-items: center;
}
.xmzb-content-right .content-right-top .i {
    width:calc((100% - 50px)/8);
    text-align: center;
    font-size:14px;
    color:#333333;
}
.xmzb-content-right .content-right-list {
    width: 100%;
    height: 75vh;
    overflow: auto;
    border-top: none;
}
.xmzb-content-right .content-right-list li {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 40px;
    border: 1px solid #e9eee6;
    border-top: none;
    cursor: pointer;
}
.xmzb-content-right .content-right-list li .i {
    width: calc((100% - 50px)/8);
    text-align: center;
    font-size:14px;
    color:#757c83;
}
.xmzb-content-right .content-right-list li:nth-child(2n) {
    background: #fdfdfd;
}
.xmzb-content-right .content-right-list li:hover {
    background: rgba(26, 188, 156, 0.08);
}
.xmzb-title {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #edf2f1;
    border: 1px solid #e9eee6;
    height: 34px;
}
.xmzb-title i {
    width: calc((100% / 5));
    text-align: center;
}
.xmzb-list {
    width: 100%;
    max-height: 320px;
    overflow: auto;
}
.xmzb-list li {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-top: 1px solid #e9eee6;
    height: 52px;
}
.xmzb-list li:nth-child(2n) {
    background: #fdfdfd;
}
.xmzb-list li:first-child {
    border-top: none;
}
.xmzb-list i {
    width: calc((100% / 5));
    text-align: center;
}
.font16 {
    font-size: 16px !important;
}
.xmzb-ok {
    width: 100%;
    height: 70px;
    border-top: 1px solid #e9eee6;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.xmzb-ok button {
    margin-right: 15px;
}
.font-icon {
    position: absolute;
    right: 90px;
    top: 3px;
    color: rgba(255, 255, 255, 0.8);
}

.ysb_list-50{
    background: #fafafa;
    height: 50px;
    display: flex;
    padding: 12px;
    align-items: center;
}
.Y-bg{
    width: 46px;
    margin-right: 28px;
    display: inline-block;
}
.Y-bg:after{
    width: 18px;
    height: 8px;
    content: '';
    position: absolute;

    margin-left: 10px;
    right: 0;
    top: 50%;
    transform: translate(0,-50%);
}
.Y-bg-lan:after{
    background-color: #2885e2;
}
.Y-bg-lv:after{
    background-color: #1abc9c;
}
.Y-bg-huang:after{
    background-color: #f2a654;
}
.y-size-text{

    font-size:22px;
    color:#354052;
    margin-bottom: 22px;
}
.y-margin-bottom-7{
    margin-bottom: 7px;
    font-size:14px;
    color:#7f8fa4;
}
.y-margin-bottom-24{
    margin-bottom: 24px;
    font-size:14px;
    color:#7f8fa4;
}
.y-canvas-line{
    font-size:16px;
    color:#1abc9c;

    position: relative;
}
.y-padding-left-64{
    padding-left: 64px;
}
.y-canvas-line:before{
    position: absolute;
    content: '';
    opacity: 0.3;
    right: 0;
    width: 89%;
    top: 50%;
    border-top: 1px dashed #1abc9c;
}
.content-right-list-bottom{
    margin-bottom: 20px;
    padding: 0 11px;
}
.content-right-list-bottom  .y-line{
    width: calc((100% / 10));
    text-align: center;
    display: block;
    border:1px solid #e9eee6;
    float: left;
}
.content-right-list-bottom .header .header-color,.content-right-list-bottom .header{
    background:#edf2f1;
    height:34px;
    line-height: 34px;
}
.content-right-list-bottom .content .content-color{
    height:38px;
    line-height: 38px;
    border-top: none;
}
.content-right-list-bottom .content{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    height: 38px;
    border: 1px solid #e9eee6;
    border-top: 1px solid #fff;
    border-left: none;
}
.content-right-list-bottom .content .content-color:nth-child(even){
    border-right: none;
    border-left: none;
}
.zui-text-flex{
    float: left;
    display: flex;
    align-items: center;
    height: 36px;
    margin-right: 10px;
}
.ysb-zui-wid{
    width: 68%!important;
}
.y-textarea{
    background:#ffffff;
    border:1px solid #dfe3e9;
    border-radius:4px;
    height:600px;
    width: 100%;
    margin-top: 6px;
    padding: 6px;
}
.zui-scroll-left .slimScrollDiv{
    height: 79%!important;
}
.zui-scroll-right .slimScrollDiv{
    height: 69%!important;
}
.y-right{
    position: absolute;
    right: 34px;
    font-size: 14px;
    color: #1abc9c;
    top: 25%;
}
.y-input-width{
    width: 80.9%;
    margin: 0 auto;
}
input{
    font-size:14px;
    color:#354052;
}
.content-right-list input:focus{
    color:#1abc9c;
}

.zui-select-group ul.inner li:hover {
    background: #1abc9c !important;
    color: #fff !important;
}
.icon-sc:before{
    color:#757c83 !important;
}