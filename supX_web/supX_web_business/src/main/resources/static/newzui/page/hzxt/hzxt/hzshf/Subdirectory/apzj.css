.apzj-title{
    font-size:24px;
    color:#1f2a34;
    font-weight: bold;
    width: 100%;
    padding: 15px 0;
}

.jbxx-size{
    padding: 10px 0 0 20px;
}
.jbxx-pl{
    padding: 10px 0 0 10px;
}
.bgNone:after{
    width: 10px;
    height: 10px;
    display: block;
    position: absolute;
    top:9px;
    right: 10px;
    content: "\f0d7";
    font-family: 'FontAwesome';
    z-index: 20;
    zoom: 1;
    text-align: center;
    color: #a7b1c2;
}
.wh106{
width: 106px !important;
}
.padd-b-15{
    padding-bottom: 15px ;
}
.color-1c{
    color:#1c2024
}
.line-height24{
line-height: 20px;
}

.position-top{
position: absolute;
    top: 0;
}
.apzj-top{
 width: 600px;
 background:#edf2f1;
 border:1px solid #e9eee6;
 height: 34px;
 display: flex;
 justify-content: center;
 align-items: center;
}
.apzj-top i{
    width: calc(600px / 3);
    display: flex;
    justify-content: center;
    align-items: center;
    color:#354052;
}
.apzj-content{
    border:1px solid #e9eee6;
    width: 600px;
    background: #fff;
}
.apzj-content li{
 height: 40px;
 width: 100%;
 display: flex;
 justify-content: center;
 align-items: center;
 border-top: 1px solid #e9eee6;
}
.apzj-content li .apzj-detail{
    width: calc(600px / 3);
    display: flex;
    justify-content: center;
    align-items: center;
}
.apzj-content li:nth-child(2n){
background:#fdfdfd;
}
.height32 > .zui-input{
height: 32px !important;
}

.wh302{
width: 320px;
}

.wh152{
width: 152px ;
}
#wrapper{
    height: calc(100% - 66px);
}