.ksys-content {
  padding: 0 10px;
}
.xmzb-content-left {
  width: calc((100% - 20px) /2);
}
.sjks-content-left {
  float: left;
}
.sjks-content-left .content-left-top {
  width: 100%;
}
.sjks-content-left .content-left-top i {
  width: calc((100% - 50px)/3);
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sjks-content-left .sjks-content-left-list li {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
}
.sjks-content-left .sjks-content-left-list li i {
  width: calc((100% - 50px)/8);
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sjks-content-right {
  float: right;
}
.sjks-content-right .content-right-top {
  width: 100%;
}
.sjks-content-right .content-right-top i {
  width: calc((100% - 50px)/7);
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sjks-content-right li {
  cursor: pointer;
  width: 100%;
}
.ksys-side {
  width: 100%;
  padding: 15px 14px;
  float: left;
}
.ksys-side .span0 {
  display: block;
  width: 100%;
  position: relative;
}
.ksys-side .span0 i {
  display: block;
  width: 100%;
}
.ksys-side #jyxm_icon .switch {
  top: 0;
  left: 17px;
}
.border-r4 {
  border-radius: 4px !important;
}
.ksys-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 60px;
}
.ksys-btn button {
  margin-right: 20px;
}
.ybhsgl-height {
  padding: 10px;
  min-height: 35px;
}
