<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检验分类</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="jyfl.css" rel="stylesheet"/>
</head>
<style>
 .tong-search{
     padding: 13px 0 5px 20px;
 }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" id="but_regyl" data-toggle="sideform" data-target="#brzcList">新增分类</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="refresh">刷新</button>
            <button class="tong-btn btn-parmary-b icon-baocun paddr-r5" @click="save">保存</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-yl paddr-r5" >预览</button>
            <button class="tong-btn btn-parmary-b icon-dysq paddr-r5">打印</button>


        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">搜索</label>
                    <div class="zui-input-inline margin-f-l30">
                        <input class="zui-input wh180" v-model="searchAll" placeholder="请输入关键字" type="text"/>
                    </div>
                </div>
                <button class="zui-btn btn-primary xmzb-db margin-f-l15" @click="queryAll">查询</button>

            </div>
        </div>
    </div>

    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full" >
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"  :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </div>
                    </th>
                    <th><div class="zui-table-cell cell-s"><span>分类编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分类名称</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>报告单打印联系电话</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table" >
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jsonList" :key="item.flbm" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.flbm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.flmc"></div></td>
                    <td><div class="zui-table-cell cell-l" v-text="item.lxdh"></div></td>
                    <td>
                        <div class="switch cell-s" >
                            <input :id="'checked'+$index" type="checkbox" v-model="item.tybz" true-value="0" false-value="1"  />
                            <label :for="'checked'+$index"></label>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
<div class="side-form ng-hide" style="width:320px;padding-top: 0;"  id="brzcList" role="form">
        <div class="tab-message">
            <!--<a v-text="title"></a>-->
            <a>新增分类</a>
            <a href="javascript:;" class="fr closex ti-close" style="color:rgba(255,255,255,.56) !important;" @click="AddClose"></a>
        </div>
        <div class="ksys-side">
        <span class="span0">
            <i>分类编码</i>
            <input type="text" class="zui-input border-r4" disabled  placeholder="请输入分类编码"/>
        </span>
            <span class="span0">
            <i>分类名称</i>
            <input type="text" class="zui-input border-r4" v-model="reqParams.flmc" placeholder="请输入分类名称"/>
        </span>
            <span class="span0">
            <i>报告单打印联系电话</i>
             <input  class="zui-input " v-model="reqParams.lxdh" placeholder="请输入联系电话"  autocomplete="auto" maxlength="11">
        </span>
            <span class="span0">
            <i>状态</i>
            <div class="switch">
                  <input  id="xinzeng"  type="checkbox" v-model="reqParams.tybz" true-value="0" false-value="1" />
                <label></label>
            </div>
        </span>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="confirms">确定</button>
        </div>
    </div>


</div>
<style>
    .side-form-bg{
        background: none;
        position: inherit;
    }
</style>

<script src="jyfl.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
