
    // 工具栏
    var InfoMenu = new Vue({
        el: '#toolMenu',
        data: {
            csqxContent: {}
        },
        methods: {

            getData: function () {
            	tableInfo.getFyData();
            },
            //
            remove: function () {
            	if(!tableInfo.inpid){
            		malert("请选择病人后再进行该操作！","top","defeadted");
            		return
            	};
      	      	var head = {
                    	operCode:"S15",
                    	billCode:tableInfo.billCode,
                    	rsa:""
                    };
                    var body = {
                    	inpId:tableInfo.inpid
//                    		inpId:'967553314'
                    };
                    var footer={
                    		czy:userId,
							zyh:tableInfo.zyh

                    };
                    var param = {
                    	head:head,
                    	body:body,
                    	footer:footer
                    };
                    var str_param = JSON.stringify(param);
        	            $.getJSON(
        	                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + tableInfo.bxurl + "&bxlbbm=" + tableInfo.bxlbbm + "&types=S&parm=" + str_param, function (json) {
        	                    console.log(json);
        	                    if (json.a == 0) {
        	                    	malert("取消上传成功！，请从新办理入院");
        	                    	InfoMenu.getData();
        	                    } else {
        	                        malert(json.c,"top","defeadted");
        	                    }
        	                });
            },
        }
    });

    // 列表和搜索
    var tableInfo = new Vue({
        el: '#mztf',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            brList: [],
            fyList:[],
            bxlbbm:null,
            bxurl:null,
            ksList:[],
            ksbm:null,
            ksmc:null,
            billCode:null,
            zyh:"",
            inpid:null,
            fyisChecked:[],
            isChecked:[]
        },
        updated:function(){
            changeWin()
        },
        methods: {
        	  getKsData: function () {
              	 $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"009001001"}', function (json) {
                       if (json.a == 0) {
                           if (json.d.length > 0) {
                        	   tableInfo.ksList = json.d;
                        	   tableInfo.ksbm = json.d[0].ksbm;
                        	   tableInfo.ksmc = json.d[0].ksmc;
                              setTimeout(function () {
                            	  tableInfo.getBrData(); //加载完成后自动获取就诊病人列表
                               }, 100);

                           }
                       } else {
                           malert("获取科室失败", 'top', 'defeadted');
                       }
                   });
        	   },
        	 ksChange: function (index) {
        		   tableInfo.getBrData();
                   var obj = event.currentTarget;
                   var selected = $(obj).find("option:selected");
                   var ks = $(obj).val();
                   var mc = selected.text();
                   tableInfo.ksmc = selected.text();
             },
             // 获取病人的API
             getBrData: function () {
             	 var ksbm = $("#ksList").val();
                  var ksmc = $('#ksList option:selected').text();
                  if (!ksbm) {
                      malert("请选择就诊科室！","top","defeadted");
                      return;
                  }
                  $.getJSON('/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=gznhhzxx&parm={"ryks":"' + tableInfo.ksbm + '","zyh":"'+tableInfo.zyh+'","bxlbbm":"' + tableInfo.bxlbbm + '"}', function (json) {
                      if (json.a == '0') {
                    	  tableInfo.brList = json.d.list;
                          tableInfo.total = json.d.total;
                      }else{
                          malert("未查到相关信息！","top","defeadted");
                      }
                  });
             },
        	 getbxlb: function () {
                 var param = {bxjk: "001"};
                 $.getJSON(
                     "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                     + JSON.stringify(param), function (json) {
                         if (json.a == 0) {
                             if (json.d.list.length > 0) {
                            	 tableInfo.bxlbbm = json.d.list[0].bxlbbm;
                            	 tableInfo.bxurl = json.d.list[0].url;
                            	 tableInfo.getS02();
                             }
                         } else {
                             malert("保险类别查询失败!" + json.c,"top","defeadted");
                         }
                     });
             },
             getS02: function () {
                 var head = {
                     operCode: "S02",
                     rsa: ""
                 };
                 var body = {
                     userName: "",
                     passWord: ""
                 };
                 var param = {
                     head: head,
                     body: body
                 };
                 var str_param = JSON.stringify(param);

                 $.getJSON(
                     "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + tableInfo.bxurl + "&bxlbbm=" + tableInfo.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                         if (json.a == 0) {
                        	 tableInfo.billCode = json.d;
                         } else {
                             malert(json.c,"top","defeadted");
                         }
                     });
             },

            getFyData: function () {
            	if(!tableInfo.zyh){
        			malert("请选择病人后再进行操作！","top","defeadted");
        			return
        		}
        		if(!tableInfo.inpid){
        			malert("该病人未办理农合入院！","top","defeadted");
        			return
        		}
        		this.param.zyh = tableInfo.zyh;
        		$.getJSON(
                        "/actionDispatcher.do?reqUrl=New1BxInterface&url="+tableInfo.bxurl+"&bxlbbm="+tableInfo.bxlbbm+"&types=wscjl&method=queryYsc&parm="+JSON.stringify(this.param), function (json) {
                        	if (json.a == 0){
                        		var res=eval('('+json.d+')');
                        		tableInfo.totlePage = Math.ceil(res.total/tableInfo.param.rows);
                        		tableInfo.fyList = res.list;
                        	}else{
                        		malert(json.c,"top","defeadted");
                        	}
                        });
            },
            // 单击选中病人，获取病人信息
            checkOne: function (index) {
            	  tableInfo.zyh = this.brList[index].zyh;
            	  tableInfo.inpid = this.brList[index].inpid;
            	  tableInfo.getFyData();
            },
        }
    });
    tableInfo.getbxlb();
    tableInfo.getKsData();

