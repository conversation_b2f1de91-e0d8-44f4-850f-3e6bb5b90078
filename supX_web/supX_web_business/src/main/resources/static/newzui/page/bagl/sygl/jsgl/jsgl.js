var menuTool=new Vue({
    el:'#jyxm_icon',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    components: {
        'search-table': searchTable
    },
    data:{
        zyh: null, //选中的住院号
        jsbz: null, //选中的接收标志
        brxxContent: {},//下拉table检索选中对象
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        jsonList:[{}],
        them_tran: {
        },
        them: {
            '住院号': 'zyh',
            '病人姓名': 'brxm',
            '病人性别': 'brxbmc',
            '年龄': 'nl',
            '入院日期': 'ryrq',
            '入院科室': 'ryksmc',
            '住院医生': 'zyysxm',
            '床位编号': 'rycwbh'
        }
    },
    //页面渲染完成之后加载数据
    mounted: function () {
        
    },
    methods:{
        sx:function () {
            
        },
        //病人基本信息下拉检索
        changeDown: function (event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'brxxContent', 'searchCon');
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'text') {
                    Vue.set(menuTool.brxxContent, 'text', this.brxxContent['zyh']);
                    menuTool.getData();
                }
            }
        },
       

        //当输入值后才触发
        change:function(add,type){
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.brxxContent[type] == undefined || this.brxxContent[type] == null){
                this.page.parm = "";
            }else{
                this.page.parm = this.brxxContent[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
            var json={shbz: 1};
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=getBaJbxxList'
                +'&dg='+JSON.stringify(str_param)+"&json="+JSON.stringify(json),
                function (data) {
                    if(data.d.list.length>0){
                        //如果查询结果只有1条则直接请求后台查询门诊费用信息
                        if(data.d.list.length==1){
                            menuTool.getData(data.d.list[0].zyh);
                        }
                        if(add){
                            for(var i=0;i<data.d.list.length;i++){
                                var ryrq = menuTool.fDate(data.d.list[i].ryrq, "date");
                                Vue.set(data.d.list[i], 'ryrq', ryrq);
                                menuTool.searchCon.push(data.d.list[i]);
                            }
                        }else{
                            for(var i=0;i<data.d.list.length;i++){
                                var ryrq = menuTool.fDate(data.d.list[i].ryrq, "date");
                                Vue.set(data.d.list[i], 'ryrq', ryrq);
                            }
                            menuTool.searchCon = data.d.list;
                        }
                    }
                    menuTool.page.total = data.d.total;
                    menuTool.selSearch = 0;
                    if(data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                });
        },
        getData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=BaglSyglSydj&types=queryIsSh&parm="
                + JSON.stringify({zyh : this.brxxContent['zyh']}), function (json) {
                if(json.a == 0){
                    menuTool.jsonList = json.d.list;
                    menuTool.zyh = json.d.list[0]['zyh'];
                    menuTool.jsbz = json.d.list[0]['jsbz'];
                }
            });
        },
        //鼠标双击（病人挂号信息）
        selectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.searching(true, 'text');
            } else {
                this.brxxContent = item;
                Vue.set(menuTool.brxxContent, 'text', this.brxxContent['zyh']);
                menuTool.getData(0);
                $(".selectGroup").hide();
            }
        },
        //登记
        saveDate: function () {
            if (this.jsbz == 1) {
                malert("该记录已经登记！");
                return false;
            } else if (this.jsbz == 0) {
                var parm = {
                    zyh: this.zyh
                };
                this.$http.post("/actionDispatcher.do?reqUrl=BaglSyglSydj&types=updateJs&",
                    JSON.stringify(parm)).then(function (data) {
                        if (data.body.a == 0) {
                            malert("登记成功");
                            menuTool.getData();
                        } else {
                            malert("登记失败" + data.body.c,top,defeadted);
                        }
                    }, function (error) {
                        console.log(error);
                    });
            }
        },

        //取消登记
        qxdjDate: function () {
            if (this.jsbz == 0) {
                malert("该记录还未登记！");
                return false;
            } else if (this.jsbz == 1) {
                var parm = {
                    zyh: this.zyh
                };
                this.$http.post("/actionDispatcher.do?reqUrl=BaglSyglSydj&types=updateQxjs&",
                    JSON.stringify(parm)).then(function (data) {
                        if (data.body.a == 0) {
                            malert("取消登记成功");
                            menuTool.getData();
                        } else {
                            malert("取消登记失败" + data.body.c, top, defeadted);
                        }
                    }, function (error) {
                        console.log(error);
                    });
            }
        },
    }
});






