<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>常规检验</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="jyxm.css" rel="stylesheet"/>
</head>
<style>

    .tong-search{
        padding: 13px 0 5px 20px;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary " id="but_regyl"  @click="add"><i class="icon-xz1 paddr-r5"></i>新增项目</button>
            <button class="tong-btn btn-parmary-b " @click="refresh"><i class="icon-sx paddr-r5"></i>刷新</button>
            <button class="tong-btn btn-parmary-b " @click="save"><i class="icon-baocun paddr-r5"></i>保存</button>
            <button class="tong-btn btn-parmary-b " @click="deleteList"><i class="icon-sc-header paddr-r5"></i>删除</button>
            <button class="tong-btn btn-parmary-b " ><i class="icon-yl paddr-r5"></i>预览</button>
            <button class="tong-btn btn-parmary-b "><i class="icon-dysq paddr-r5"></i>打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">搜&nbsp;索</label>
                    <div class="zui-input-inline margin-f-l25">
                        <input class="zui-input wh180" v-model="searchAll" placeholder="请输入关键字" type="text"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full" >
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m">
                     <input-checkbox @result="reCheckBox" :list="'jsonList'"
                     :type="'all'" :val="isCheckAll">
                    </input-checkbox></th>
                    <th><div class="zui-table-cell cell-s"><span>项目编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>项目名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>所属分组</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>所属分类</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>默认标本</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>报告类型</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>打印行数</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>样本号位数</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>报告名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
<!--                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>-->
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table"  @scroll="scrollTable($event)">
            <table class="zui-table" >
                <tbody>
                <tr :tabindex="$index"  @dblclick="show($index)" v-for="(item, $index) in jsonList" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.mc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbmmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.flbmmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bbbmmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="bglx_tran[item.bglx]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.dyhs"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ybhws"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bgmc"></div></td>
                    <td>
                        <div class="switch cell-s" >
                            <input :id="'checked'+$index" type="checkbox" disabled v-model="item.tybz" true-value="0" false-value="1"  />
                            <label :for="'checked'+$index"></label>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

    <div id="pop" class="jyxm">
        <transition name="left-fade">
            <div class="zui-form side-form ng-hide pop-width  bcsz-layer " id="brzcList" style="height: 100%;padding-top: 0px;width: 600px">
                <!--<div class="layui-layer-title " v-text="title"></div>-->
                <!--<span class="layui-layer-setwin" style="top: 0;">-->
                <!--<i class="color-btn" @click="close">&times;</i></span>-->
                <div class="tab-message">
                    <!--<a v-text="title"></a>-->
                    <a v-text="title"></a>
                    <a href="javascript:;" class="fr closex ti-close" style="color:rgba(255,255,255,.56) !important;" @click="close"></a>
                </div>
                <div class="ksys-side">
                    <ul class="tab-edit-list tab-edit2-list">
                        <li>
                            <i>项目编码</i>
                            <input class="zui-input ysb-zui-wid" type="text" placeholder="请输入编号" v-model="xm.bm" autocomplete="auto" disabled>
                        </li>
                        <li>
                            <i>项目名称</i>
                            <input class="zui-input ysb-zui-wid"type="text"
                                   @blur="setPYDM(xm.mc, 'xm', 'pydm')"
                                   placeholder="请输入项目名称" v-model="xm.mc"
                                   data-notempty="true" autocomplete="auto">
                        </li>
                        <li>
                            <i>所属分组</i>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="cs.fz" :index="'fzmc'" :index_val="'fzbm'" :val="xm.fzbm"
                                          :name="'xm.fzbm'" :search="true">
                            </select-input>
                        </li>
                        <li>
                            <i>检验方法</i>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="cs.ff" :index="'ffmc'" :index_val="'ffbm'" :val="xm.ffbm"
                                          :name="'xm.ffbm'" :search="true">
                            </select-input>
                        </li>
                        <li>
                            <i>所属分类</i>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="cs.fl" :index="'flmc'" :index_val="'flbm'" :val="xm.flbm"
                                          :name="'xm.flbm'" :search="true">
                            </select-input>
                        </li>
                        <li>
                            <i>默认标本</i>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="cs.yb" :index="'ybmc'" :index_val="'ybbm'" :val="xm.bbbm"
                                          :name="'xm.bbbm'" :search="true">
                            </select-input>
                        </li>
                        <li>
                            <i>报告类型</i>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="lx" :index="'text'" :index_val="'num'" :val="xm.bglx"
                                          :name="'xm.bglx'" :search="true">
                            </select-input>
                        </li>
                        <li>
                            <i>报告名称</i>
                            <input class="zui-input ysb-zui-wid" v-model="xm.bgmc" placeholder="" autocomplete="auto">
                        </li>
                        <li>
                            <i>检验设备</i>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="cs.sb" :index="'hostname'" :index_val="'sbbm'" :val="xm.jysb"
                                          :name="'xm.jysb'" :search="true">
                            </select-input>
                        </li>
                        <li>
                            <i>打印行数</i>
                            <input class="zui-input ysb-zui-wid" data-notempty="true" type="number" v-model="xm.dyhs" placeholder="请输入打印行数" autocomplete="auto">

                        </li>
                        <li>
                            <i>结果方式</i>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="fs" :index="'text'" :index_val="'num'" :val="xm.scfs"
                                          :name="'xm.scfs'" :search="true">
                            </select-input>
                        </li>
                        <li>
                            <i>项目单价</i>
                            <input class="zui-input ysb-zui-wid" type="number" data-notempty="true" placeholder="" v-model="xm.dj" autocomplete="auto">
                        </li>
                        <li><i>显示序号</i><input class="zui-input ysb-zui-wid" type="number" data-notempty="true" placeholder="请输入显示序号" v-model="xm.xssx" autocomplete="auto"></li>
                        <li style="width: 100%"><em>打印检验方法</em>
                            <div class="switch" >
                            <input type="checkbox" v-model="xm.dyjyff" true-value="0" false-value="1"  />
                            <label></label>
                             </div>
                        </li>
                        <li style="width: 100%"><em>语音叫号取报告</em><div class="switch" >
                            <input type="checkbox" v-model="xm.sfjh" true-value="0" false-value="1" />
                            <label></label>
                        </div></li>
                        <li style="width: 100%"><em class="text-left">状态</em><div class="switch" >
                            <input type="checkbox" v-model="xm.tybz" true-value="0" false-value="1" />
                            <label></label>
                        </div></li>
                    </ul>
                </div>
               <div class="ksys-btn">
                    <button class="zui-btn table_db_esc btn-default xmzb-db" @click="close">取消</button>
                    <button class="zui-btn btn-primary xmzb-db" @click="save">保存</button>
                </div>
            </div>
        </transition>
    </div>

    <div id="popCenter">
        <!--<transition name="pop-fade">-->
        <div class="pophide" :class="{'show':isShowpopL}"></div>
        <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title"></div>
            <span class="layui-layer-setwin">
                <a @click="isShowpopL=false,isShow=false" class="layui-layer-ico layui-layer-close layui-layer-close1" href="javascript:;"></a>
            </span>
            <div class="layui-layer-content" >
                <div class=" layui-mad layui-height">
                    确定删除：{{centent}}项吗？
                </div>
            </div>
            <div class="zui-row buttonbox buttond">
                <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save" @click="success()">确定</button>
            </div>
        </div>
        <!--</transition>-->
    </div>

</div>



<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="jyxm.js"></script>
</body>
</html>
