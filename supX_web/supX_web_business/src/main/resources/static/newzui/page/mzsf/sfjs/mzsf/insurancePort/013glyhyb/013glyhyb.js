var activeX = document.getElementById("csharpActiveX");

function CreatXmlDoc(obj){
	this.tagName=obj.tagName;
	var children=obj.children.map(function(item){
		if(typeof item =="object")
		{
			item=new CreatXmlDoc(item)
		}
		return item
	})
	this.children=children;
}


CreatXmlDoc.prototype.render=function(){
	var el=document.createElement(this.tagName);
	var children=this.children || [];
	children.forEach(function(child){
		var childEl=(child instanceof CreatXmlDoc)
			? child.render()
			:document.createTextNode(child)
		el.appendChild(childEl);
	})
	return el
}
var gl_013 = new Vue({
	el: '.gl_013',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data: {
		isShow: false,
		bxlbbm: null,
		bxurl: null,
		birthday: null,
		text: null,
		jbContent: {},
		searchCon: [],
		selSearch: -1,
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		them_tran: {},
		them: {
			'疾病编码': 'yke120',
			'疾病名称': 'yke121',
			'副编码': 'yke223'
		},
		brzt_tran:{
			'1':'在院',
			'2':'未在院'
		},
		zdxxJson:{
			aka130 : "11",
		},
		grxxJson:{},
		sfsbContent:{},
		cssg:false,
		userInfo:{},
		ifclick:true,
	},
	mounted: function () {
		this.isShow = true;
		this.init();
	},
	methods: {
		init: function () {
			$.post("http://localhost:10013/init", {}, function (json) {
				if (json.aint_appcode > 0) {
					rightVue.gzyhybInit = true;
					malert("初始化成功!");
				} else {
					malert("医保控件初始化失败！请从新打开页面!");
				}
			});
		},

		closeGz_002: function () {
			this.isShow = false;
			$("#hyjl").html("");
		},
		getUserInfo: function () {
			this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
				.then(function (json) {
					this.userInfo = json.body.d;
				});
		},
		getbxlb: function () {
			var param = {bxjk: "013"};
			$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
				+ JSON.stringify(param), function (json) {
				if (json.a == 0) {
					if (json.d.list.length > 0) {
						gl_013.bxlbbm = json.d.list[0].bxlbbm;
						gl_013.bxurl = json.d.list[0].url;
					}
				} else {
					malert("保险类别查询失败!" + json.c,'top','defeadted')
				}
			});
		},
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'jbContent', 'searchCon');
			if (event.code == 'Enter' || event.code == 13) {
				if (type == "text") {
					Vue.set(this.jbContent, 'jbmc', this.jbContent['aka121']);
					gl_013.zdxxJson.jbbm = this.jbContent.aka120;
					gl_013.zdxxJson.jbmc = this.jbContent.aka121;
					this.selSearch=0;
					this.nextFocus(event);
				}
			}
		},

		searching: function (add, type,val) {
			this.jbContent['jbmc']=val;
			if (!add) this.page.page = 1;
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			if (this.jbContent[type] == undefined || this.jbContent[type] == null) {
				this.page.parm = "";
			} else {
				this.page.parm = this.jbContent[type];
			}
			var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gl_013.bxurl + "&bxlbbm=" + gl_013.bxlbbm + "&types=ICD10&method=query&parm="
				+ JSON.stringify(str_param),
				function (json) {
					if (json.a == 0) {
						var date = null;
						var res = eval('(' + json.d + ')');
						if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
							for (var i = 0; i < res.list.length; i++) {
								gl_013.searchCon.push(res.list[i]);
							}
						} else {
							gl_013.searchCon = res.list;
						}
						gl_013.page.total = res.total;
						gl_013.selSearch = 0;
						if (res.list.length > 0 && !add) {
							$(".selectGroup").hide();
							_searchEvent.show();
						}
					} else {
						malert("查询失败  " + json.c,'top','defeadted');
					}
				});
		},
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;               // 设置当前页号
				this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
			} else {   // 否则就是选中事件,为json赋值
				this.jbContent = item;
				Vue.set(this.jbContent, 'jbmc', this.jbContent['aka121']);
				gl_013.zdxxJson.jbbm = this.jbContent.aka120;
				gl_013.zdxxJson.jbmc = this.jbContent.aka121;
				$(".selectGroup").hide();
			}
		},
		//读卡
		load:function(){
			if(!gl_013.ifclick){
				malert("请勿重复点击！","top","defeadted");
				return;
			}
			gl_013.ifclick = false;
			if(rightVue.gzyhybInit){
				var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai></input>';
				$.post("http://localhost:10013/call", {
					jybh:"03",
					jysr_xml:jysr
				}, function (json) {
					gl_013.ifclick = true;
					if (json.aint_appcode > 0) {
						gl_013.sfsbContent = JSON.parse(json.astr_jysc_xml);
						gl_013.grxxJson = gl_013.sfsbContent;
						malert("读卡成功!");
					} else {
						malert(json.astr_appmasg);
					}
				});
			}else{
				gl_013.ifclick = true;
				malert("医保控件未初始化,请重新打开页面！",'top','defeadted');
			}
		},

		//引入
		enter:function(){
			if (Object.keys(gl_013.grxxJson).length === 0) {
				malert("请先读卡","top","defeadted");
				return;
			}
			if (!gl_013.zdxxJson.aka130) {
				malert("请选择支付类型","top","defeadted");
				return;
			}
			if(rightVue.fzContent.brxm != gl_013.grxxJson.aac003){
				malert("医保报销，禁止使用非本人医保卡，请核对后再试！","top","defeadted");
				return;
			}
			//个人信息
			rightVue.gzyhybContent = gl_013.grxxJson;
			//门诊诊断信息
			rightVue.gzyhybContent.jbbm = this.zdxxJson.jbbm;
			//支付类别
			rightVue.gzyhybContent.aka130 = this.zdxxJson.aka130;
			//备注
			rightVue.gzyhybContent.bzsm = this.zdxxJson.bzsm;
			//个人编号,用于结算各种判断
			rightVue.gzyhybContent.grbh = gl_013.grxxJson.aac001;

			popTable.isShow = false;
			malert("引入成功！");
		},

		//门诊预结算方法
		mzyjs:function(ghrq){
			var result = "0";
			//同步操作
			$.ajaxSettings.async = false;
			//处理费用
			var yhybBrfyList = [];
			var fylist=[];
			var brfyList=[];
			var fyze = 0.00;
			for (var i=0;i<rightVue.brfyjsonList.length;i++) {
				if(rightVue.brfyjsonList[i].fydj <= 0){
					continue;
				}
				var fyparam = {};
				fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
				fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
				fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
				fyparam.fysl = rightVue.brfyjsonList[i].fysl;

				fyparam.yka091 = rightVue.brfyjsonList[i].mzks;
				fyparam.yka092 = rightVue.brfyjsonList[i].mzksmc;
				fyparam.yka093 = rightVue.brfyjsonList[i].mzys;
				fyparam.yka094 = rightVue.brfyjsonList[i].mzysxm;
				fyparam.ake007 = rightVue.brfyjsonList[i].sfsj;

				brfyList.push(fyparam);
				fyze+=rightVue.brfyjsonList[i].fyje;
			}
			this.updatedAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gl_013.bxurl + "&bxlbbm=" + gl_013.bxlbbm + "&types=mzjy&method=queryMzfy&parm=" + JSON.stringify(brfyList), function (json) {
					if(json.a == '0'){
						yhybBrfyList = eval('(' + json.d + ')');
						for (let i = 0; i < yhybBrfyList.length; i++) {
							fylist.push(yhybBrfyList[i]);
						}
					}else{
						malert(json.c,'right','defeadted');
						return false;
					}
				});
			if(!fylist || fylist.length<=0){
				malert("没有可结算费用！",'right','defeadted');
				return false;
			}
			var rowlist=[];
			for(var i=0;i<fylist.length;i++){
				if(fylist[i] && fylist[i].ake001
						// 过滤金额为0的项目
						&& fylist[i].aae019 && fylist[i].aae019 != 0 && fylist[i].aae019 != '0'){
					var taglist=[];
					var ykc120={
						tagName:'ykc120',//记账流水号
						children:[(new Date()).getTime()+i]
					};
					taglist.push(ykc120);
					var ake005={
						tagName:'ake005',//医院项目编码
						children:[fylist[i].ake005]
					};
					taglist.push(ake005);
					var ake006={
						tagName:'ake006',//医院项目名称
						children:[fylist[i].ake006]
					};
					taglist.push(ake006);
					var aka067={
						tagName:'aka067',//单位
						children:[fylist[i].aka067]
					};
					taglist.push(aka067);
					var aka074={
						tagName:'aka074',//规格
						children:[fylist[i].aka074]
					};
					taglist.push(aka074);
					var akc225={
						tagName:'akc225',//单价
						children:[fylist[i].akc225]
					};
					taglist.push(akc225);
					var akc226={
						tagName:'akc226',//数量
						children:[fylist[i].akc226]
					};
					taglist.push(akc226);
					var aae019={
						tagName:'aae019',//金额
						children:[fylist[i].aae019]
					};
					taglist.push(aae019);
					var yka091={
						tagName:'yka091',//开单科室编码
						children:[fylist[i].yka091]
					};
					taglist.push(yka091);
					var yka092={
						tagName:'yka092',//开单科室名称
						children:[fylist[i].yka092]
					};
					taglist.push(yka092);
					var yka093={
						tagName:'yka093',//开单医生编码
						children:[fylist[i].yka093]
					};
					taglist.push(yka093);
					var yka094={
						tagName:'yka094',//开单医生姓名
						children:[fylist[i].yka094]
					};
					taglist.push(yka094);
					var ake007={
						tagName:'ake007',//费用发生时间
						children:[gl_013.fDate(fylist[i].ake007,'datetime')]
					};
					taglist.push(ake007);
					var aae011={
						tagName:'aae011',//经办人
						children:[userId]
					};
					taglist.push(aae011);
					var yke018={
						tagName:'yke018',//经办人姓名
						children:[gl_013.userInfo.czyxm]
					};
					taglist.push(yke018);
					var yaz301={
						tagName:'yaz301',//限制用药
						children:['0']
					};
					taglist.push(yaz301);
					var aka190={
						tagName:'aka190',//用法用量
						children:[fylist[i].aka190]
					};
					taglist.push(aka190);
					var aka191={
						tagName:'aka191',//处方天数
						children:[fylist[i].aka191]
					};
					taglist.push(aka191);
					var ake001={
						tagName:'ake001',//医保目录编码
						children:[fylist[i].ake001]
					};
					taglist.push(ake001);
					var row={
						tagName:'row',
						children:taglist
					};
					rowlist.push(row);
				}else{
					malert("第【" + (i+1) + "】行，【"+ fylist[i].ake006 +"】【" + fylist[i].ake005 + "】未对码，请先对码！","top","defeadted");
					return false;
				}
			}
			fyze = fyze.toFixed(2);
			var obj={
				tagName:'dataset',
				children:rowlist
			};
			doc=new CreatXmlDoc(obj);
			SetupSerial=(new XMLSerializer()).serializeToString(doc.render());
			var reg = new RegExp(' xmlns="http://www.w3.org/1999/xhtml"',"g");
			SetupSerial=SetupSerial.replace(reg,"");
			var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var aka130="<aka130>"+rightVue.gzyhybContent.aka130+"</aka130>";
			var yke214="<yke214>"+rightVue.brxxContent.ghxh+"</yke214>";
			var aae030="<aae030>" + rightVue.fDate(ghrq,'datetime') + "</aae030>";
			var aae031="<aae031>"+rightVue.fDate(new Date(),'datetime')+"</aae031>";
			var aae140="<aae140>"+rightVue.gzyhybContent.aae140+"</aae140>";
			var aac001="<aac001>" + rightVue.gzyhybContent.aac001 + "</aac001>";
			var aae013="<aae013></aae013>";
			var ake034="<ake034>" + fyze + "</ake034>";
			var aae011="<aae011>" + gl_013.userInfo.czyxm + "</aae011>";
			var ydbz="<ydbz>" + (rightVue.gzyhybContent.ydbz?rightVue.gzyhybContent.ydbz:'0') + "</ydbz>";

			jysr=jysr+aka130+yke214+aae030+aae031+aae140+aac001+aae013+
				ake034+aae011+ydbz+SetupSerial+"</input>";
			//去掉所有不合法字符
			jysr = jysr.replace(/undefined/g,"");
			jysr = jysr.replace(/NaN/g,"");
			jysr = jysr.replace(/null/g,"");
			//调用结算方法
			$.ajaxSettings.async = false;
			$.post("http://localhost:10013/call",{'jybh':'48','jysr_xml':jysr},
				function (json) {
				if (json.aint_appcode > 0) {
					rightVue.yjsContentGzyhyb = JSON.parse(json.astr_jysc_xml);
					rightVue.jylsh = json.astr_jylsh;
					rightVue.jyyzm = json.astr_jjyyzm;
					//结算成功后，进行HIS门诊登记
					//这里转换一次，将字段下划线去掉，并修改为驼峰命名
					var enterContent = {
						ghxh : rightVue.brxxContent.ghxh,//取挂号序号
						jylsh : rightVue.jylsh,//就医流水号
						jyyzm : rightVue.jyyzm,//交易验证码
						akc190 : typeof rightVue.yjsContentGzyhyb.akc190 == 'string'?rightVue.yjsContentGzyhyb.akc190:'',//就诊编号	NOT NULL	VARCHAR2(15)
						aaz216 : typeof rightVue.yjsContentGzyhyb.aaz216 == 'string'?rightVue.yjsContentGzyhyb.aaz216:'',//结算编号	NOT NULL	VARCHAR2(15)
						aka130 : typeof gl_013.grxxJson.aka130 == 'string'?gl_013.grxxJson.aka130:'',//个人编号	NOT NULL	VARCHAR2(15)
						aac001 : typeof gl_013.grxxJson.aac001 == 'string'?gl_013.grxxJson.aac001:'',//个人编号	NOT NULL	VARCHAR2(15)
						aab001 : typeof gl_013.grxxJson.aab001 == 'string'?gl_013.grxxJson.aab001:'',//单位ID	NOT NULL	VARCHAR2(20)
						aab003 : typeof gl_013.grxxJson.aab003 == 'string'?gl_013.grxxJson.aab003:'',//单位名称	NOT NULL	VARCHAR2(100)
						aab999 : typeof gl_013.grxxJson.aab999 == 'string'?gl_013.grxxJson.aab999:'',//单位管理码	NOT NULL	VARCHAR2(20)
						aab019 : typeof gl_013.grxxJson.aab019 == 'string'?gl_013.grxxJson.aab019:'',//单位类型	NOT NULL	VARCHAR2(6)
						aab020 : typeof gl_013.grxxJson.aab020 == 'string'?gl_013.grxxJson.aab020:'',//经济类型	NOT NULL	VARCHAR2(6)
						aab021 : typeof gl_013.grxxJson.aab021 == 'string'?gl_013.grxxJson.aab021:'',//隶属关系	NOT NULL	VARCHAR2(6)
						aab022 : typeof gl_013.grxxJson.aab022 == 'string'?gl_013.grxxJson.aab022:'',//行业代码	NOT NULL	VARCHAR2(6)
						yab019 : typeof gl_013.grxxJson.yab019 == 'string'?gl_013.grxxJson.yab019:'',//单位管理类型	NOT NULL	VARCHAR2(6)
						aac002 : typeof gl_013.grxxJson.aac002 == 'string'?gl_013.grxxJson.aac002:'',//公民身份号码	NOT NULL	VARCHAR2(20)
						aac003 : typeof gl_013.grxxJson.aac003 == 'string'?gl_013.grxxJson.aac003:'',//姓名	NOT NULL	VARCHAR2(60)
						aac004 : typeof gl_013.grxxJson.aac004 == 'string'?gl_013.grxxJson.aac004:'',//性别	NOT NULL	VARCHAR2(6)
						aac005 : typeof gl_013.grxxJson.aac005 == 'string'?gl_013.grxxJson.aac005:'',//民族	NOT NULL	VARCHAR2(6)
						aac006 : typeof gl_013.grxxJson.aac006 == 'string'?gl_013.grxxJson.aac006:'',//出生日期	NOT NULL	VARCHAR2(21)
						aja004 : typeof gl_013.grxxJson.aja004 == 'string'?gl_013.grxxJson.aja004:'',//农民工标志	NOT NULL	VARCHAR2(6)
						aac012 : typeof gl_013.grxxJson.aac012 == 'string'?gl_013.grxxJson.aac012:'',//个人身份	NOT NULL	VARCHAR2(6)
						yac084 : typeof gl_013.grxxJson.yac084 == 'string'?gl_013.grxxJson.yac084:'',//离退休标志	NOT NULL	VARCHAR2(6)
						aac020 : typeof gl_013.grxxJson.aac020 == 'string'?gl_013.grxxJson.aac020:'',//行政职务	NOT NULL	VARCHAR2(6)
						aac066 : typeof gl_013.grxxJson.aac066 == 'string'?gl_013.grxxJson.aac066:'',//参保身份	NOT NULL	VARCHAR2(6)
						aab034 : typeof gl_013.grxxJson.aab034 == 'string'?gl_013.grxxJson.aab034:'',//参保人员所属分中心	NOT NULL	VARCHAR2(20)
						aae139 : typeof gl_013.grxxJson.aae139 == 'string'?gl_013.grxxJson.aae139:'',//异地安置标志	NOT NULL	VARCHAR2(6)
						yke112 : typeof gl_013.grxxJson.yke112 == 'string'?gl_013.grxxJson.yke112:'',//实足年龄	NOT NULL	VARCHAR2(6)
						aac031 : typeof gl_013.grxxJson.aac031 == 'string'?gl_013.grxxJson.aac031:'',//参保状态	NOT NULL	VARCHAR2(6)
						mtinfo : typeof gl_013.grxxJson.mtinfo == 'string'?gl_013.grxxJson.mtinfo:'',//慢特病余额信息	NULL	VARCHAR2(1000)
						akb020 : typeof gl_013.grxxJson.akb020 == 'string'?gl_013.grxxJson.akb020:'',//居民门诊统筹定点机构	NULL	VARCHAR2(20)
						aae140 : typeof gl_013.grxxJson.aae140 == 'string'?gl_013.grxxJson.aae140:'',//险种类型	NOT NULL	VARCHAR2(6)
						ykb065 : typeof gl_013.grxxJson.ykb065 == 'string'?gl_013.grxxJson.ykb065:'',//险种类型名称	NOT NULL	VARCHAR2(20)
						aae240 : typeof gl_013.grxxJson.aae240 == 'string'?gl_013.grxxJson.aae240:'',//个人账户余额	NULL	VARCHAR2(14)
						ydbz : typeof gl_013.grxxJson.ydbz == 'string'?(gl_013.grxxJson.ydbz?gl_013.grxxJson.ydbz:'0'):'0',//异地标志	NOT NULL	VARCHAR2(6)
						aaz500 : typeof gl_013.grxxJson.aaz500 == 'string'?gl_013.grxxJson.aaz500:'',//卡号	NOT NULL	VARCHAR2(20)
						ykc006 : typeof gl_013.grxxJson.ykc006 == 'string'?gl_013.grxxJson.ykc006:'',//建档立卡标识	NULL	VARCHAR2(6)
						akb001 : typeof gl_013.grxxJson.akb001 == 'string'?gl_013.grxxJson.akb001:'',//民政救助类型	NULL	VARCHAR2(20)
						mzarea : typeof gl_013.grxxJson.mzarea == 'string'?gl_013.grxxJson.mzarea:'',//民政救助区县	NULL	VARCHAR2(6)
					};
					$.ajaxSettings.async = false;
					gl_013.updatedAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gl_013.bxurl + "&bxlbbm=" + gl_013.bxlbbm + "&types=mzjy&method=mzdj&parm="
						+ JSON.stringify(enterContent),
						function (json) {
							if (json.a == '0') {
								rightVue.yjsContentGzyhyb.ifok = true;
							}else{
								malert(json.astr_appmasg,"top","defeadted");
							}
						});
				}else{
					malert(json.astr_appmasg,"top","defeadted");
				}
			});
		},

		//取消
		gz013_quxiao:function(){
			var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var akc190 = "<akc190>" + rightVue.yjsContentGzyhyb.akc190 + "</akc190>";//就诊编号
			var aaz216 = "<aaz216>" + rightVue.yjsContentGzyhyb.aaz216 + "</aaz216>";//结算编号
			var aac001 = "<aac001>" + rightVue.yjsContentGzyhyb.aac001 + "</aac001>";//个人编号
			var aka130 = "<aka130>" + rightVue.yjsContentGzyhyb.aka130 + "</aka130>";//支付类别
			var aae011 = "<aae011>" + gl_013.userInfo.czyxm + "</aae011>";//经办人
			var ydbz = "<ydbz>" + gl_013.zdxxJson.ydbz + "</ydbz>";//异地标志
			jysr += akc190 + aaz216 + aaz216 + aac001 + aka130 + aae011 + ydbz+"</input>";
			$.post("http://localhost:10013/call",{
				'jybh':"42",
				'jysr_xml':jysr,
			},function (json) {
				//成功后调一次 confirm
				if (json.aint_appcode > 0) {
					$.post("http://localhost:10013/confirm",{
						'jylsh':json.astr_jylsh,
						'jyyzm':json.astr_jyyzm,
					},function (data) {
					});
				}else {
					malert(data.astr_appmasg);
				}
			});
		},

		//门诊结算方法
		mzjs:function(){

		},
	}
});
gl_013.getbxlb();
gl_013.getUserInfo();

$(document).click(function () {
	if (this.className != 'selectGroup') {
		$(".selectGroup").hide();
	}
	$(".popInfo ul").hide();
});
