<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link  rel="stylesheet" href="mtyj.css">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<!--收/退预交金begin-->
<div class="printArea printShow" id="printArea"></div>
<div id="syjjInfo" class="printHide" v-cloak style="height: 100%;overflow: hidden;padding-bottom: 52px;">
    <!--收/退预交金 功能按钮begin-->
    <div class="panel">
        <div class="tong-top flex-container">
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="refresh">刷新</button>
            <input class="zui-input wh182 margin-r-10"  data-notEmpty="false" :value="brxxContent.zyh"
                   @keydown="changeDown($event,'zyh','brxxContent','rysearchCon')"
                   @input="change(false,'zyh',$event.target.value)">
            <search-table :message="rysearchCon" :selected="selSearch"
                          :them="them" :them_tran="them_tran" :page="page"
                          @click-one="checkedOneOut" @click-two="ryselectOne">
            </search-table>
            <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click=""><i class="icon-width icon-dsfz"></i>读身份证</button>
            <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click=""><i class="icon-width icon-dylk"></i>读医疗卡</button>
            <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click=""><i class="icon-width icon-dybk"></i>读医保卡</button>
            <button class="tong-btn btn-parmary-b icon-ff paddig-left"  @click="openModel"><i class="icon-width icon-dybk"></i>历史收预交</button>
            <input type="password" autocomplete="off" class="zui-input wh182 margin-r-10" :data-code="codeContentFun" v-model="codeContent" placeholder="扫码支付..." id="codeContent">
        </div>
    </div>
    <!--收/退预交金 功能按钮end-->

    <div class="syjj-info-box" style="height: 100%;overflow-y: auto;">
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">患者信息</div>
            </div>
            <div class="tab-card-body">
                <div class="flex-container flex-wrap-w flex-align-c">
                    <div class="padd-r-10">
                        患者姓名 <span class="color-wtg"> {{pageData.brxxInfo.brxm}}</span>
                    </div>
                    <div class="padd-r-10">
                        年龄 <span class="color-wtg"> {{pageData.brxxInfo.brnl}}{{nldw_tran[pageData.brxxInfo.brnldw]}}</span>
                    </div>
                    <div class="padd-r-10">
                        性别 <span class="color-wtg"> {{brxb_tran[pageData.brxxInfo.brxb]}}</span>
                    </div>
                    <div class="padd-r-10">
                        挂号序号 <span class="color-wtg"> {{pageData.brxxInfo.ghxh}}</span>
                    </div>
                    <div class="padd-r-10">
                        身份证件<span class="color-wtg"> {{pageData.brxxInfo.ghxh}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card" v-show="!isShouYJJ">
            <div class="tab-card-header">
                <div class="tab-card-header-title">预交记录详情</div>
            </div>
            <div class="tab-card-body">
                <div class="zui-form grid-box">
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">支付类型</label>
                        <div class="zui-input-inline zui-select-inline">
                            <input class="zui-input" disabled
                                   :title="pageData.jjlInfo.zflxmc"
                                   v-model="pageData.jjlInfo.zflxmc">
                            <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label" style="line-height: 1.5">原&ensp;预&ensp;交金&emsp;额</label>
                        <div class="zui-input-inline danwei-box">
                            <input class="zui-input" disabled
                                   :title="fDec(pageData.jjlInfo.yyjje,2)"
                                   v-model="fDec(pageData.jjlInfo.yyjje,2)">
                            <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            <span class="danwei">元</span>
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">已退金额</label>
                        <div class="zui-input-inline danwei-box">
                            <input placeholder="" data-notEmpty="false" class="zui-input" disabled
                                   :title="fDec(pageData.jjlInfo.ytje,2)"
                                   v-model="fDec(pageData.jjlInfo.ytje,2)">
                            <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            <span class="danwei">元</span>
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">可退金额</label>
                        <div class="zui-input-inline danwei-box">
                            <input placeholder="" data-notEmpty="false" class="zui-input" disabled
                                   :title="fDec(pageData.jjlInfo.ktje,2)"
                                   v-model="fDec(pageData.jjlInfo.ktje,2)">
                            <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            <span class="danwei">元</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">{{ isShouYJJ ? '预交编辑' : '退款编辑' }}</div>
            </div>
            <div class="tab-card-body">
                <div class="zui-form grid-box">
                    <div class="zui-inline col-xxl-3" v-show='isShouYJJ'>
                        <label class="zui-form-label">支付类型</label>
                        <select-input class="wh150" @change-data="commonResultChange" :search="true"
                                      :child="zflxList" :index="'zflxmc'" :index_val="'zflxbm'"
                                      :val="pageData.payType" :search="true"
                                      :name="'pageData.payType'" :index_mc="'zflxmc'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">{{ isShouYJJ ? '预交金额' : '退款金额' }}</label>
                        <div class="zui-input-inline danwei-box">
                            <input :disabled="!isShouYJJ && pageData.payType !='1' ? true : false" type="number" id="advanceOrRefund" placeholder="请输入金额" class="zui-input" data-notEmpty
                                   :title="pageData.brxxInfo.yjje" min="1"
                                   v-model.number="pageData.brxxInfo.yjje"
                                   @keydown.enter="nextFocus($event)">
                            <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            <span class="danwei">元</span>
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-6">
                        <label class="zui-form-label">备注说明</label>
                        <div class="zui-input-inline">
                            <input placeholder="请输入备注说明" class="zui-input" data-notEmpty
                                   :title="pageData.note"
                                   v-model="pageData.note"
                                   @keydown.enter="submitFun">
                            <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--v-show="!isShouYJJ"-->
        <div class="zui-table-view" id="yjjlTable" >
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>身份证</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>支付类型</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>业务窗口</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>操作姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>科室名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>预交金额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>预交日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>预交流水号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>退预交流水号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>备注</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)"  >
                <table v-if="pageData.yjjlList.length" class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in pageData.yjjlList"
                        @dblclick="showYjjlInfo(item)"
                        @click="checkSelect([$index,'one','pageData.yjjlList'],$event)"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'bg-color':item.yjje<=0}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"><!--@click="单击回调" @dblclick="edit($index)"双击回调-->
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"><!--序号--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.brxm"><!--住院号--></div></td>
                        <td ><div class="zui-table-cell cell-l" v-text="item.sfzjhm"><!--住院号--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.zflxmc"><!--支付类型--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.ywckmc"><!--业务窗口--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.czyxm"><!--操作员姓名--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.ksmc"><!--科室名称--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="fDec(item.yjje,2)"><!--预交金额--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="fDate(item.yjrq,'date')"><!--预交日期--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.fphm"><!--预交流水号--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.tyjjlid" style="overflow: hidden;"><!--退预交流水号--></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.bzsm"><!--备注--></div></td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="!pageData.yjjlList.length" class=" flex noData  text-center zan-border">暂无数据...</p>
            </div>
        </div>
    </div>
    <div class="action-bar fixed" style="bottom: 10px;">
        <button class="zui-btn xmzb-db btn-primary xmzb-db" @click="submitFun()"> {{ isShouYJJ ? '交预交' : '退预交' }}</button>
        <button class="zui-btn xmzb-db btn-primary xmzb-db" @click="goBack()" v-if="!isShouYJJ">返回</button>
    </div>
    <model style="top:10%" :s="'确定'" :c="'取消'" @default-click="resultFun" @result-clear="resultFun"
           :model-show="false" @result-close="resultFun" v-if="bxShow" :title="'历史预交'">
        <div class="chxx_model">
            <div class="flex-container flex-align-c padd-l-10 padd-b-10">
                <span class="ft-14 padd-r-5 whiteSpace">查询</span>
                <div class="  flex-container flex-align-c">
                    <input class="zui-input todate wh120 " @click="showTime('timeVal','beginrq')" v-model="beginrq" placeholder="请选择开始时间" id="timeVal"/><span
                        class="padd-l-5 padd-r-5">~</span>
                    <input class="zui-input todate wh120 " @click="showTime('timeVal1','endrq')" v-model="endrq" placeholder="请选择结束时间" id="timeVal1"/>
                </div>
                <input class="zui-input wh182 margin-l-10"  data-notEmpty="false" v-model="brxxContent.zyh"
                       @keyup.13="getBryjjlList($event.target.value,'yjjlList1')"/>
            </div>
            <div class="flex-container flex-dir-c flex-one">
                <div class="zui-table-view"  >
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>身份证</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>支付类型</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>业务窗口</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>操作姓名</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>科室名称</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>预交金额</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>预交日期</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>预交流水号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>退预交流水号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>备注</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)" data-no-change >
                        <table v-if="pageData.yjjlList1.length" class="zui-table table-width50">
                            <tbody>
                            <tr v-for="(item, $index) in pageData.yjjlList1"
                                @dblclick="showYjjlInfo(item)"
                                @click="checkSelect([$index,'one','pageData.yjjlList1'],$event)"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'bg-color':item.yjje<=0}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"><!--@click="单击回调" @dblclick="edit($index)"双击回调-->
                                <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"><!--序号--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="item.brxm"><!--住院号--></div></td>
                                <td ><div class="zui-table-cell cell-l" v-text="item.sfzjhm"><!--住院号--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="item.zflxmc"><!--支付类型--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="item.ywckmc"><!--业务窗口--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="item.czyxm"><!--操作员姓名--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="item.ksmc"><!--科室名称--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="fDec(item.yjje,2)"><!--预交金额--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="fDate(item.yjrq,'date')"><!--预交日期--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="item.fphm"><!--预交流水号--></div></td>
                                <td ><div class="zui-table-cell cell-l" v-text="item.tyjjlid" style="overflow: hidden;"><!--退预交流水号--></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="item.bzsm"><!--备注--></div></td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="!pageData.yjjlList1.length" class=" flex noData  text-center zan-border">暂无数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </model>
</div>
<!--收/退预交金end-->
<script type="text/javascript" src="backFun.js"></script>
<script type="text/javascript" src="mtyj.js"></script>
