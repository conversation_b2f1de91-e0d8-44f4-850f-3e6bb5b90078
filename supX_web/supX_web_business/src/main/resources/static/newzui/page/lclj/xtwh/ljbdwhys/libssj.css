.left-tree{

}
.left-tree .item{

}
.left-tree .item .list{
    margin-left: 20px;
}
.toggleIMg{
    vertical-align: baseline;
}
.yzblsj.zui-table-view .zui-table-body tr td{
    border-right: 1px solid #eee;
}
.yzblsj.zui-table-view .zui-table-header th{
    border-right: 1px solid #e9eee6;
}
.pop-805 {
    width: 805px !important;
}
.yzDataList .title,.yzDataList input,.yzDataList .titleText{
    display: inline-block;
    background:#ffffff;
    border:1px solid #e9eee6;
    height:45px;
    text-align: center;
    padding: 11px 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    border-top: none;
    border-right: none;
}
.yzDataList .titleText:last-child{
    border-right: 1px solid #e9eee6;
}
.yzDataList .leftData{
    background:#fdfdfd;
    border:1px solid #e9eee6;
    width:138px;
    font-size:14px;
    text-align: center;
    border-right: none;
    border-top: none;
    color:#1abc9c;
    display: inline-block;
}
.topData{
    width:138px;
    padding: 9px 15px 7px 16px;
    font-size:14px;
    color:#333333;
    text-align:center;
}
.yzHeader li{
    padding: 9px 15px 7px 16px;
    font-size:14px;
    color:#333333;
    border:1px solid #e9eee6;
    text-align:center;
}
.maxContent{
    width: max-content;
    min-width: 100%;
}
.yzHeader{
    background:#edf2f1;
    border:1px solid #e9eee6;
    display: flex;

    justify-content: flex-start;
    align-items: center;
}
body, html{
    background-color: #ffffff;
}
.cssz-list li{
    align-items: end;
}
.yzContent{
    height: 45px;
}
.headerText{
    font-family:PingFangSC-Semibold;
    font-size:22px;
    color:#3a3a3a;
    margin-bottom: 10px;
    text-align:center;
}
.headerList{
    font-size:14px;
    color:#7f8fa4;
    text-align:center;
}
.tjsj{
    font-size:14px;
    color:#1abc9c;
    text-align:center;
    position: relative;
    height: 38px;
    display: flex;
    padding-left: 23px;
    border-top: 1px solid #e7eaee;
    justify-content: center;
    align-items: center;
}
.tjsj:before{
    position: absolute;
    content: '';
    left: 22px;
    top: 50%;
    transform: translate(0,-50%);
    width: 15px;
    height: 15px;
    color: #1abc9c;
    border: 1px solid #1abc9c;
    border-radius: 100%;
}
.tjsj:after{
    position: absolute;
    content: '+';
    left: 23px;
    top: 43%;
    width: 15px;
    height: 15px;
    transform: translate(0,-50%);
}