var jbxx = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat, scrollOps],
    data: {
        popContent: {},
        csqxContent: {},
        mxfyxmContent: {},
        yljgmc: null,
        sfsf: 1
    },
    mounted: function () {
        this.initVal();
        window.addEventListener("storage", function (e) {
            if (e.key == "dhzglitem_change" && e.newValue !== e.oldValue) {
                jbxx.initVal();
            }
        });
        laydate.render({
            elem: '#sqTime',
            type: 'datetime',
            theme: '#1ab394'
            , done: function (value, data, a, b, c, d) {
                sssq.pageState.sqrq = value;
                sssq.nextFocus($('#sqTime')[0])
            }
        });
    },
    methods: {
        //初始加载
        initVal: function () {
            this.popContent = JSON.parse(sessionStorage.getItem('dhzglitem'));
            this.csqxContent = JSON.parse(sessionStorage.getItem('hzcsqxList'));
            this.popContent.ifEdit = sessionStorage.getItem('dhzglitem_ifEdit');
            if (!this.popContent.hzkssj) {
                this.popContent.hzkssj = new Date().getTime();
            }
            if (!this.popContent.hzjlsj) {
                this.sfsf = 0;
                this.popContent.hzjlsj = new Date().getTime();
            }
            if (this.popContent.hzzt == '2') {
                $('.contenteditable').attr('contenteditable', false);
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmMxfyxm&types=queryOne&mxfybm=111000002", function (data) {
                if (data.d != null) {
                    mxfyxmContent = data.d;
                    console.log("会诊费："+mxfyxmContent.fydj)
                } else {
                    malert(data.c, 'top', 'defeadted');
                    return;
                }
            });

            // console.l
            // this.popContent.brxb = this.brxb_tran[this.popContent.brxb];
            // this.popContent.nldw = this.nldw_tran[this.popContent.nldw];

            this.yljgmc = JSON.parse(sessionStorage.userPage)[2].yljgmc;
            this.yljgmc = this.yljgmc ? this.yljgmc : "";
            sessionStorage.removeItem('dhzglitem');
            sessionStorage.removeItem('hzcsqxList');
        },
        //取消
        Cancel: function () {
            var hzbg_targetPatPath = sessionStorage.getItem("hzbg_targetPatPath");
            this.topClosePage('page/hzxt/hzxt/dhzgl/sub/hzbg.html', hzbg_targetPatPath);
        },
        //保存
        save: function () {
            if (!jbxx.popContent.hzjl) {
                malert("请先填写会诊结论与建议！", "top", "defeadted");
                return;
            }
            if (jbxx.popContent.hzzt == '2') {
                jbxx.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=saveHzgl', JSON.stringify(jbxx.popContent)).then(function (data) {
                    if (data.body.a == 0) {
                        sessionStorage.setItem("apzj_saveHzbg", new Date().getTime());
                        // this.topClosePage('page/hzxt/hzxt/dhzgl/sub/hzbg.html', 'page/hzxt/hzxt/dhzgl/dhzgl.html');
                        malert("保存成功", 'top', 'success')
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                        return;
                    }
                }, function (error) {
                    console.log(error);
                    malert(error, 'top', 'defeadted');
                    return;
                });
            } else {
                jbxx.popContent.hzzt = '2';
                jbxx.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=saveHzgl', JSON.stringify(jbxx.popContent)).then(function (data) {
                    if (data.body.a == 0 && this.sfsf == 0) {
                        let obj = {
                            balb: mxfyxmContent.balb,
                            balbmc: mxfyxmContent.balbmc,
                            beginrq: null,
                            brid: this.popContent.brid,
                            bzbm: mxfyxmContent.bzbm,
                            djrq: this.popContent.hzjlsj,
                            endrq: null,
                            ffylb: mxfyxmContent.ffylb,
                            fpfs: mxfyxmContent.fpfs,
                            fpks: mxfyxmContent.fpks,
                            fydj: mxfyxmContent.fydj,
                            fygg: mxfyxmContent.fygg,
                            fyje: mxfyxmContent.fydj,
                            fylb: mxfyxmContent.lbbm,
                            fylbmc: "会诊费",//gyb_mxfyxm关联gyb_fylb
                            fylx: mxfyxmContent.fylx,
                            fysl: 1,
                            fytclb: null,
                            hsks: null,
                            ifqj: null,
                            ksbm: this.popContent.ryks,
                            ksmc: this.popContent.ryksmc,
                            lbbm: mxfyxmContent.lbbm,
                            mxfybm: mxfyxmContent.mxfybm,
                            mxfymc: mxfyxmContent.mxfymc,
                            mxfyxmbm: mxfyxmContent.mxfybm,
                            mxfyxmmc: mxfyxmContent.mxfymc,
                            nbtclb: mxfyxmContent.nbtclb,
                            order: "asc",
                            page: 1,
                            parm: null,
                            pydm: mxfyxmContent.pydm,
                            rows: 20000,
                            sfgd: mxfyxmContent.sfgd,
                            sfrq: this.popContent.hzjlsj,
                            sftf: "0",
                            sfzy: mxfyxmContent.sfzy,
                            sl: 0,
                            sort: "",
                            tclbmc: "全自费",
                            text: mxfyxmContent.mxfymc,
                            tybz: mxfyxmContent.tybz,
                            wbjm: mxfyxmContent.wbjm,
                            xjjzbz: "0",
                            xssx: mxfyxmContent.xssx,
                            yebh: "",
                            yhbl: mxfyxmContent.yhbl,
                            yhje: 0,
                            yjhz: mxfyxmContent.yjhz,
                            yljgbm: mxfyxmContent.yljgbm,
                            ynhz: mxfyxmContent.ynhz,
                            ypfy: mxfyxmContent.ypfy,
                            yqbm: mxfyxmContent.yqbm,
                            ysbm: this.popContent.sqys,
                            ysks: this.popContent.ryks,
                            ysksmc: this.popContent.ryksmc,
                            ysxm: this.popContent.sqysxm,
                            zbm: mxfyxmContent.zbm,
                            zhfy: "0",
                            zhfybm: null,
                            zxks: this.popContent.yqysks,
                            zxksmc: this.popContent.yqysksmc,
                            zyh: this.popContent.zyh,
                            zyks: this.popContent.ryks,
                            zyksmc: this.popContent.ryksmc,
                            zyys: this.popContent.zyys,
                            zyysxm: this.popContent.zyysxm
                        };
                        let fyxx = [];
                        fyxx.push(obj)
                        var json = '{"list":' + JSON.stringify(fyxx) + '}';
                        // console.log("ksbm:" + fyjz.ksbm)
                        this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglFyglBrfy&types=save&ksbm=' + this.popContent.ryks,
                            json).then(function (data) {
                            if (data.body.a == 0) {
                                malert('会诊费计费成功！', 'right', 'success');
                                // fyjz.clear();
                                // fyjz.ifClick = true;
                                // fyjz.add()
                                //fyjz.quxiao();
                            } else {
                                malert(data.body.c, 'right', 'defeadted');
                                // fyjz.ifClick = true;
                            }
                        }, function (error) {
                            console.log(error);
                        });
                        malert(data.body.c);
                        sessionStorage.setItem("apzj_saveHzbg", new Date().getTime())
                        // this.topClosePage('page/hzxt/hzxt/dhzgl/sub/hzbg.html', 'page/hzxt/hzxt/dhzgl/dhzgl.html');
                    } else {
                        sessionStorage.setItem("apzj_saveHzbg", new Date().getTime())
                        // this.topClosePage('page/hzxt/hzxt/dhzgl/sub/hzbg.html', 'page/hzxt/hzxt/dhzgl/dhzgl.html');
                        malert(data.body.c, 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                    malert(error, 'top', 'defeadted');
                });
            }

        },
        print: function () {
            // if (jbxx.csqxContent.N03010200202 == '1'){
            var reportlets = "[{reportlet: 'fpdy%2Fzyys%2Fzyys_hzsq.cpt',hzbh:'" + this.popContent.hzbh + "'}]";
            if (!FrPrint(reportlets, null)) {
                return;
            }
            // } else {
            //     $("#aaaa").hide();
            //     window.print();
            //     $("#aaaa").show();
            // }
        },
    }

});

