<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>执行细类</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <style>
        .ksys-side .span0{
            width: 28%;
            display: inline-flex;
            position: relative;
            float: left;
            margin-right: 5%;
            margin-bottom: 19px;
            align-self: flex-end;
            align-items: center;
            justify-content: space-between;
        }
        .ksys-side .span0 input,.ksys-side .span0 .zui-select-inline {
            width: 70%;
            float: right;
        }
        .ksys-side .span0 .zui-select-inline{
            margin-right:0
        }
        .ksys-side .span0 .zui-select-inline input{
            width: 100%;
        }
        .ksys-side .span0 i {
            /*display: block;*/
            /*width: 100%;*/
            width: 62px;
            line-height:initial;
            text-indent: initial;
            display: inline-block;
        }
        #jyxm_icon .switch {
            top: 15%;
            left: 35%;
        }

    </style>
</head>
<body class="skin-default" style="overflow: auto">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addData">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="edit()">保存</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="remove">删除</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">科室检索</label>
                    <div class="zui-input-inline margin-l13">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="param.parm" @input="getData()"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view zui-table-h padd-l-10 padd-r-10">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th  z-fixed="left" z-style="text-align:center;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th  z-field="xh" z-width="50px">
                        <div class="zui-table-cell">
                            <span>编号</span>
                        </div>
                    </th>
                    <th z-field="ry" z-width="100px">
                        <div class="zui-table-cell"><span>名称</span></div>
                    </th>
                    <th z-field="username" z-width="100px">
                        <div class="zui-table-cell"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body">
            <table class="zui-table table-width50">
                <tbody>
                <!--style="position: absolute"  :style="{marginTop:heightL[$index]+'px'}"-->
                <tr v-for="(item, $index) in jsonList" class="tableTr2" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index" @dblclick="edit($index)" ref="list">
                    <td  style="text-align:center;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td fixed="left">
                        <div class="zui-table-cell" v-text="item.xsxh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell">{{item.mc}}</div>
                    </td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove(item)"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

</div>
<div class="side-form " :class="{'ng-hide':index=='0'}" style="width:720px;padding-top: 0;"  id="brzcList" role="form">
    <div class="tab-message">
        <a>{{title}}</a>
        <a href="javascript:;" class="fr closex ti-close"  @click="closes"></a>
    </div>
    <div class="ksys-side">
        <span class="span0">
            <i>编号</i>
           <input type="text" class="zui-input" v-model="popContent.xsxh" data-notEmpty="false" @keydown="nextFocus($event)" />
        </span>
        <span class="span0">
            <i>执行大类名称</i>
            <input type="text" class="zui-input" name="input1" v-model="popContent.mc" @keydown="nextFocus($event)" data-notEmpty="false"/>
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">确定</button>
    </div>
</div>

<script type="text/javascript" src="zxxl.js"></script>
<script type="text/javascript" >
    $(".zui-table-view").uitable();
</script>
</body>
</html>