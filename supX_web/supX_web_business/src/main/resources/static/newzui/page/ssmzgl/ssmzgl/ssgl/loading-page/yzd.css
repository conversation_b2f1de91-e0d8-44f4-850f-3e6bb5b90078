.yzd_select {
    border-bottom: 2px solid #1AB394;
}

.yzdTitle {
    font-size: 22px;
    text-align: center;
}

.yzd-brInfo, .yzd-ysInfo {
    display: flex;
    justify-content: center;
    color:#7f8fa4;
    /*width: 905px;*/
    margin: 10px 0;
}


.yzd-brInfo > div, .yzd-ysInfo > div {
    font-size: 14px;
    margin-right: 20px;
}

.tablePage span{
    display: inherit;
}

.yzd-table table {
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
}

.yzd-table td, .yzd-table th {
    border-left: 1px solid #999;
    font-weight: 500;
    height: 39px;
    /*width: 30px;*/
    white-space: nowrap;
    text-align: center;
}
.yzd-table tr{
    border: 1px solid #999;
}
.yzd-table td .yzd-name {
    display: block;
    width: 60%;
    font-size: 13px;
    text-align: left;
    margin-left: 2px;
    word-break:normal;
    white-space:pre-wrap;
    word-wrap : break-word ;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
}

.cqyzd, .lsyzd {
    text-align: center;
    overflow: scroll;
    height: 100%;
    /*height: calc(100% - 36px);*/
}

.ysDiv {
    width: 100%;
    text-align: center;
    /*position: absolute;*/
    bottom: 0;
}

.same {
    border-right: 1px solid #000000;
}
.yzd-sm {
    float: right !important;
}

.sameStart{
    position: absolute;
    border-top: 1px solid #000000;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 50%;
    right: 50%;
    bottom: 0;
}
.sameEnd{
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 1px solid #000000;
    width: 10px !important;
    height: 50%;
    right: 50%;
}
.same{
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 100%;
    right: 50%;
}

.yzd-way{
    width: auto !important;
    margin-left: 18px;
}

.yzd-table td, .yzd-table th{
    position: relative;
}

@media print {
    .goPrintHide{
        visibility: hidden;
    }
}
.height-34 {
    height: 34px;
}
#toolMenu_yzd{
    min-height: 34px;
}