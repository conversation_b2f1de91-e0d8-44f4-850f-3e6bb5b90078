var qxksbm = ""; //选中库房后获取的科室编码
var userName = sessionStorage.getItem("userName" + userId) && sessionStorage.getItem("userName" + userId).replace(/^[\'\"]+|[\'\"]+$/g, '')
//列表
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, baseFunc, printer, mformat],
    data: {
        ifClick: true,
        ckdh: '',
        isShowpopL: false,
        kfmcc: '',
        zdyxm: '',
        rkdList: [], //入库单集合
        KFList: [], //库房
        kfShow: true,
        //*********
        isUpdate: 0,
        search: '',
        yfList: [],
        sfly: false,//是否领药标志
        jyinput: false,
        //打印数据
        activeIndex1: undefined,
        hoverIndex1: undefined,
        printData: {},
        ckd: {},
        popContent: {
            ckfs: '05'
        },
        ckfsContent: {
            ckfsContent: '05'
        },
        isShowkd: true,
        isShow: false,
        cxShow: false,
        ckdList: [], //出库单集合
        rkd: {}, //入库单对象
        TjShow: true,
        dyShow: false,
        ShShow: false,
        kcShow: false,
        zfShow: true,
        mxShShow: true,
        zdrq: getTodayDateTime(), //获取制单日期
        isChecked_ly: [],
        ckdContent: {}, //出库单对象
        isCheck: null,
        rkdDetail: [], //入库明细集合
        dateBegin: null, //getTodayDateBegin(),
        dateEnd: null, //getTodayDateEnd(),
        zfIfHide: true, //作废按钮是否显示
        num: 0,
        jsonList: [],
        json: {
            jjzj: 0.0,
            ljzj: 0.0,
        },
        zhuangtai: {
            "0": "未审核",
            "1": "已审核",
            "2": "已作废",
            "3": "未通过",
        },

        param: {
            rows: 100,
            page: 1,
            parm: '',
            zt: '9',
            beginrq: null,
            endrq: null
        },
        ckdDetail: [], //出库单明细集合//药品详细列表信息
        totlePage: 0,
        /*出库方式*/
        ckfs_tran1: {
            "05": "其他出库",
        },
		totalypjj:0,
		totalyplj:0,
    },
    mounted: function () {
        this.getKFData();
        this.param.beginrq = this.fDate(new Date(), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        // laydate.render({
        //     elem: '#beginTimeVal',
        //     value: this.param.beginrq,
        //     type: 'datetime',
        //     // trigger: 'click',
        //     theme: '#1ab394',
        //     done: function (value, data) {
        //         wrapper.param.beginrq = value;
        //         wrapper.getData();
        //     }
        // });
        // laydate.render({
        //     elem: '#endTimeVal',
        //     value: this.param.endrq,
        //     type: 'datetime',
        //     // trigger: 'click',
        //     theme: '#1ab394',
        //     done: function (value, data) {
        //         wrapper.param.endrq = value;
        //         wrapper.getData();
        //     }
        // });
    },
    updated: function () {
        changeWin()
    },
    computed: {
        money: function () {
            var reducers = {
                totalInEuros: function (state, item) {
                    return state.jjzj += item.ypjj * parseFloat(item.cksl);
                },
                totalInYen: function (state, item) {
                    return state.ljzj += item.yplj * parseFloat(item.cksl);
                }
            };
            var manageReducers = function (reducers) {
                return function (state, item) {
                    return Object.keys(reducers).reduce(function (nextState, key) {
                        reducers[key](state, item);
                        return state;
                    }, {})
                }
            }
            var bigTotalPriceReducer = manageReducers(reducers);
            var totals = this.ckdDetail.reduce(bigTotalPriceReducer, this.json = {
                jjzj: 0,
                ljzj: 0,
            });
        }
    },
    methods: {
        getCxsl: function (rksl, cxsl, ycxsl, index) {
            if (rksl - ycxsl < parseFloat(cxsl)) {
                malert('冲销数量不得大于入库数量', 'top', 'defeadted');
                this.ckdDetail[index]['cxsl'] = ''
            } else {
                return true
            }
        },
        isCx: function (item) {
            return JSON.stringify(item) != '{}' ? item.totalYpjj ? item.totalYpjj.indexOf('-') == -1 ? true : false : true : true;
        },
        isShFun: function (item) {
            return JSON.stringify(item) != '{}' && item.shzfbz == 0 ? true : false;
        },
        cxClick: function () {
            this.cxShow = !this.cxShow;
            this.ShShow = !this.ShShow;
            this.dyShow = !this.dyShow;
        },
        hoverMouse1: function (type, index) {
            this.hoverIndex1 = type ? index : undefined;
        },
        checkSelect: function (val) {
            this.activeIndex1 = val[0];
        },
        openDetail: function (index) {
            wap.title = '查看';
            this.isCheck = index;
            this.isShowpopL = false;
            this.TjShow = false;
            this.zfShow = false;
            this.TjShow = false;
            this.dyShow = false;
            this.ShShow = false;
            this.mxShShow = false;
            this.isShowkd = false;
            let ckdh = '';
            if(this.ckdList[index].iscx =='1'){
            	ckdh = this.ckdList[index]['cxdh'];
            }else{
            	ckdh = this.ckdList[index]['ckdh'];
            }
            
            
            this.getMx(ckdh,this.ckdList[index]['uptimestamp']);
        },
        loadNum: function () {
            this.num = wrapper.num;
        },
        //进入页面加载单据列表信息
        getData: function () {
            common.openloading('.zui-table-view');
            //清空进价和零件总计
            this.json.jjzj = 0;
            this.json.ljzj = 0;
            //清空入库明细信息
            this.ckdList = [];
            if (this.param.zt != '9') {
                this.param.shzfbz = this.param.zt;
            } else {
                this.param.shzfbz = null;
            }
this.param.ckfs ='05'
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=queryCkd&parm=' + JSON.stringify(this.param), function (data) {
                if (data.a == 0 && data.d.list) {
                    wrapper.ckdList = data.d.list;
                    wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
					if(data.d.count){
						wrapper.totalyplj=data.d.count.TOTALYPLJ
						wrapper.totalypjj=data.d.count.TOTALYPJJ
					}
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
            common.closeLoading()
        },
        //出库审核
        passData: function () {
            if (!this.param.kfbm) {
                malert("请选择库房!", 'top', 'defeadted');
                return;
            }
            if (!this.ifClick) return;
            this.ifClick = false;
            var updateYp = "0";
            if (wap.csqxContent.cs00200100203 == '1') {
                updateYp = "1";
            }
            var json = {
                ckdh: this.ckdList[this.isCheck]['ckdh'],
                kfbm: this.param.kfbm,
                qxksbm: qxksbm,
                sldh: this.ckdList[this.isCheck]['sldh'],
                lyyf: wap.ckdContent.lyyf,
                updateYp: updateYp,
            };

            this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=shrkd', JSON.stringify(json)).then(function (data) {
                if (data.body.a == "0") {
                    wrapper.ifClick = true;
                    //打印数据
                    var mes = confirm('是否打印出库单');
                    if (mes == true) {
                        wrapper.print();
                    } else {

                    }
                    //  console.log(wrapper.printData);
                    malert("审核成功！", 'top', 'success');
                    wrapper.cancel();
                    wrapper.getData();
                } else {
                    wrapper.ifClick = true;
                    malert(data.body.c, 'top', 'defeadted');
                }
            });
        },
        //打印
        printDJ: function () {
            //是否有打印权限
//              if (wap.csqxContent.cs00200100201 == '0') {
//                  this.printData = {};
//                  return;
//              }

            var json = {
                "ckdh": this.ckdList[this.isCheck]['ckdh'],
                "kfbm": this.param.kfbm,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=print&parm=' + JSON.stringify(json),
                function (data) {
                    wrapper.printData = data.d;
                    var zdrq = data.d.dj.zdrq;
                    var djmx = data.d.djmx;
                    var jjzj = 0;
                    var ljzj = 0;
                    wrapper.printData.dj.zdrq = wrapper.fDate(zdrq, 'date');
                    for (var i = 0; i < djmx.length; i++) {
                        var yxqz = djmx[i].yxqz;
                        wrapper.printData.djmx[i]['yxqz'] = wrapper.fDate(yxqz, 'date');
                        jjzj += djmx[i].ypjjje;
                        ljzj += djmx[i].ypljje;
                        djmx[i].ypjjje = wrapper.fDec(djmx[i].ypjjje, 2);
                        djmx[i].ypljje = wrapper.fDec(djmx[i].ypljje, 2);
                    }
                    wrapper.printData.dj.jjjehz = wrapper.fDec(jjzj, 2);
                    wrapper.printData.dj.ljjehz = wrapper.fDec(ljzj, 2);
                    wrapper.print();
                });


        },

        updateDycsAndPrint: function (kfbm, ckdh) {
            var parm = {
                'ckdh': ckdh
            };
            //更新打印次数
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=updateDycs' + '&parm=' + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        //调用帆软打印
                        console.log(kfbm + "||" + ckdh);
                        var frpath = "";
                        if (window.top.J_tabLeft.obj.frprintver == "3") {
                            frpath = "%2F";
                        } else {
                            frpath = "/";
                        }
                        var reportlets = "[{reportlet: 'fpdy" + frpath + "ykgl" + frpath + "ykgl_ckd.cpt',yljgbm:'" + jgbm + "',kfbm:'" + kfbm + "',ckdh:'" + ckdh + "',dysj:'" + wrapper.fDate(wrapper.ckd.uptimestamp, 'AllDate') + "'}]";
                        console.log(reportlets);
                        if (FrPrint(reportlets, null)) {
                            return;
                        }
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },

        print: function () {
            //是否有打印权限
            /*if (this.printData.dj == undefined) {
                console.log('无打印权限');
                malert('无打印权限','top','defeadted')
                return;
            }*/
            //帆软打印
			let ckdh='';
			if(this.ckdList[this.isCheck].iscx =='1'){
				ckdh = this.ckdList[this.isCheck]['cxdh'];
			}else{
				ckdh = this.ckdList[this.isCheck]['ckdh'];
			}
			
            // var ckdh = this.ckdList[this.isCheck]['ckdh'];
            var kfbm = this.param.kfbm;
            if (!window.top.J_tabLeft.obj.FRorWindow) {
                this.updateDycsAndPrint(kfbm, ckdh);
            } else {
                // 查询打印模板
                var json = {
                    repname: '药库出库单'
                };
                var rows = this.printData['dj']['rows'];
                $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    // 根据每页行数循环打印
                    var total = Math.ceil(wrapper.printData['djmx'].length / rows);
                    for (var i = 0; i < total; i++) {
                        // 清除打印区域
                        wrapper.clearArea(json.d[0]);
                        // 绘制模板的canvas
                        wrapper.drawList = JSON.parse(json.d[0]['canvas']);
                        wrapper.creatCanvas();
                        wrapper.reDraw();
                        var list = [];
                        var jjhz = 0;
                        var ljhz = 0;
                        for (var j = 0; j < rows; j++) {
                            if (wrapper.printData['djmx'][i * rows + j] == null) break;
                            list.push(wrapper.printData['djmx'][i * rows + j]);
                            jjhz += parseFloat(wrapper.printData['djmx'][i * rows + j]['ypjjje']);
                            ljhz += parseFloat(wrapper.printData['djmx'][i * rows + j]['ypljje']);
                        }
                        // 为打印前生成数据
                        wrapper.printData['dj']['total'] = total;
                        wrapper.printData['dj']['page'] = i + 1;
                        wrapper.printData['dj']['jjxj'] = parseFloat(jjhz).toFixed(2);
                        wrapper.printData['dj']['ljxj'] = parseFloat(ljhz).toFixed(2);
                        wrapper.printContent(wrapper.printData['dj']);
                        wrapper.printTrend(list);
                        // 开始打印
                        window.print();
                    }
                });
            }
        },
        //作废2018/07/04二次弹窗提示作废
        invalidData: function (num) {
            if (!this.param.kfbm) {
                malert("请选择库房!", 'top', 'defeadted');
                return;
            }
            if (num != null && num != undefined) {
                this.isCheck = num;
            }
            if (common.openConfirm("确认作废该条信息吗？", function () {
                var json = {
                    "ckdh": wrapper.ckdList[wrapper.isCheck]['ckdh'],
                    "kfbm": wrapper.param.kfbm,
                    'sldh': wrapper.ckdList[wrapper.isCheck]['sldh'],
                    'lyyf': wrapper.popContent.lyyf,
                };
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=zfrkd', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.getData();
                        malert("作废成功！", 'top', 'success');
                        wrapper.cancel();
                    } else {
                        malert(data.body.c);
                    }
                });
            })) {
                return false;
            }
        },
        //编辑
        editIndex: function (index, vals) {
            //编辑标志
            this.isCheck = index;
            this.isShowpopL = true;
            this.isShowkd = false;
            this.dyShow = false;
            this.TjShow = true;
            this.zfShow = false;
            this.ShShow = false;
            this.jyinput = false;
            this.mxShShow = true;
            $("#zfbtn").hide();
            $("#divlink").remove();
            if (this.ckdList[index].shzfbz == 0) {
                this.ShShow = true;
                this.mxShShow = true;
                this.jyinput = false;
            } else {
                this.ShShow = false;
                this.jyinput = true;
                this.mxShShow = false;
                if (this.ckdList[index].shzfbz == 1) {
                    this.dyShow = true;
                    this.TjShow = true;
                    this.isShowpopL = false;
                }
            }
            this.zdyxm = this.ckdList[index].zdyxm;
            this.zdrq = this.ckdList[index].zdrq;
            this.ckd = this.ckdList[index];
            wap.shzfbz = this.ckdList[index].shzfbz;
            let ckdh = '';
            if(this.ckdList[index].iscx =='1'){
            	ckdh = this.ckdList[index]['cxdh'];
            }else{
            	ckdh = this.ckdList[index]['ckdh'];
            }
            
            
            this.getMx(ckdh,this.ckdList[index]['uptimestamp']);
        },
        //加载明细
        getMx: function (ckdh) {
            this.ckdDetail = [];
            var parm = {
                "ckdh": ckdh,
                uptimestamp: this.ckd.uptimestamp
            };

            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=queryCkdMxByRkdh&parm=' + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        wrapper.ckdDetail = data.d;
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },

        //选中单据信息加载出相对应的单据内容明细
        showDetail: function (index, item) {
            this.isSh = 1;//审核标志
            wap.title = '审核';
            $("#divlink").remove();
            this.isCheck = index;
            this.isShowpopL = false;
            this.isShowkd = false;
            $('#bzms').attr('disabled', true);
            this.TjShow = false;
            this.zfShow = false;
            this.TjShow = true;
            this.dyShow = false;
            this.ShShow = true;
            this.ckd = this.ckdList[index];//赋值
            wap.ckd = this.ckdList[index];
            this.zdyxm = this.ckdList[index].zdyxm;
            this.zdrq = this.ckdList[index].zdrq;
            wap.shzfbz = this.ckdList[index].shzfbz;
            this.getMx(this.ckd.ckdh);
        },
        //提交所有药品
        submitAll: function () {
            if (Object.keys(wap.shparam).length != 0) {
                // 先审核申领出库
                this.postAjax('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=updateCkbz', JSON.stringify(wap.shparam)).then(function (data) {
                    if (data.body.a == "0") {
                    } else {
                        malert(data.body.c);
                    }
                });
            }
            Vue.set(wap.ckd, 'bzms', this.popContent.bzsm);
            wap.ckd = Object.assign(wap.ckd, wrapper.ckd)
            if (this.sfly) {
                Vue.set(wap.ckd, 'kfbm', this.param.kfbm);
                Vue.set(wap.ckd, 'lyks', wap.sld.lyks);
                Vue.set(wap.ckd, 'lyksmc', wap.sld.lyksmc);
                Vue.set(wap.ckd, 'lyr', wap.sld.lyr);
                Vue.set(wap.ckd, 'lyrxm', wap.sld.lyrxm);
                Vue.set(wap.ckd, 'lyyf', wap.sld.yfbm);
                Vue.set(wap.ckd, 'sldh', wap.sld.sldh);
                Vue.set(wap.ckd, 'ckfs', "05");
                wap.ckdContent.kfbm = this.param.kfbm;
            } else {
                Vue.set(wap.ckd, 'kfbm', this.popContent.kfbm || wap.ckd.kfbm);
                Vue.set(wap.ckd, 'lyks', wap.ckdContent.lyks || wap.ckd.lyks);
                Vue.set(wap.ckd, 'lyr', wap.ckdContent.lyr || wap.ckd.lyr);
                if (wap.ckdContent.lyyf != null) {
                    Vue.set(wap.ckd, 'lyyf', wap.ckdContent.lyyf);
                }
                Vue.set(wap.ckd, 'ckfs', "05")
            }

            var jsonList = [];
            if (this.cxShow) {
                for (var i = 0; i < this.ckdDetail.length; i++) {
                    if (this.ckdDetail[i].cxsl) {
                        var item = JSON.parse(JSON.stringify(this.ckdDetail[i]));
                        item.mxxh = ++this.ckdDetail[this.ckdDetail.length - 1]['mxxh']
                        item.cxsl = '-' + item.cxsl;
                        jsonList.push(item);
                    }
                }
            } else {
                jsonList = this.ckdDetail
            }
            var json = {
                "list": {
                    "ckd": wap.ckd,
                    "ckdmx": jsonList
                }
            };
            if (wrapper.sfly) {
                //申领单
                var url = !this.cxShow ? 'save' : 'ckcx'
                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=' + url, JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        malert("申领出库已审核，提交所有材料成功！", 'top', 'success');
                        wrapper.cancel()
                        wrapper.getData();
                    } else {
                        var msg = '';
                        if (data.body.d != null && data.body.d.length != 0) {
                            for (var i = 0; i < data.body.d.length; i++) {
                                msg += data.body.d[i];
                            }
                            malert(msg, 'top', 'defeadted');

                        } else {
                            malert("保存失败:" + data.body.c, 'top', 'defeadted');
                        }
                        wrapper.kcShow = true;
                    }
                });

            } else {
                if (this.isSubmited) {
                    malert("数据提交中，请稍候！", 'top', 'defeadted');
                    return;
                }
                if (this.ckdDetail.length <= 0) {
                    malert("没有可提交的数据", 'top', 'defeadted');
                    return false;
                }
                if (this.param.kfbm == null) {
                    $(".KFSelect").addClass("emptyError");
                    malert("库房为必选项", 'top', 'defeadted');
                }
                //是否禁止提交
                this.isSubmited = true;
                var url = !this.cxShow ? 'modify' : 'ckcx'
                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=' + url, JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        malert("数据更新成功", 'top', 'success');
                        wrapper.cancel()
                        wrapper.getData();
                    } else {
                        var msg = '';
                        if (data.body.d != null && data.body.d.length != 0) {
                            for (var i = 0; i < data.body.d.length; i++) {
                                msg += data.body.d[i];
                            }
                            malert(msg, 'top', 'defeadted');

                        } else {
                            malert("保存失败:" + data.body.c, 'top', 'defeadted');
                        }
                    }
                    //是否禁止提交
                    wrapper.isSubmited = false;
                }, function (error) {
                    console.log(error);
                    //是否禁止提交
                    wrapper.isSubmited = false;
                });
            }
        },

        //删除2018/07/04 二次弹窗删除提示
        scmx: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.ckdDetail.splice(index, 1);
            })) {
                return false;
            }
        },
        //双击修改
        edit: function (num) {
            this.isUpdate = 1;
            wap.isedit = num
            wap.popContent = JSON.parse(JSON.stringify(this.ckdDetail[num]));//药品详情
            //时间格式化
            Vue.set(wap.popContent, 'scrq', formatTime(wap.popContent.scrq, 'date'));
            Vue.set(wap.popContent, 'yxqz', formatTime(wap.popContent.yxqz, 'date'));

            if (this.sfly) {
                wap.ckdContent = wap.ckd;
            }

            if (this.isUpdate == 1) {
                wap.ckdContent.ckd = wrapper.ckd == null ? '' : wrapper.ckd;//出库单
            }
            var lyks = wap.ckdContent.lyks;
            var parm = {
                rows: 100,
                page: 1,
                parm: lyks,
                sort: 'asc',
            };

            //出库药房
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=findYFByKS&dg=' + JSON.stringify(parm), function (data) {
                console.log(data.d);
                if (data.a == 0) {
                    if (data.d == null) {
                        Vue.set(wap.ckdContent, "lyyfmc", "科室出库");
                    } else {
                        Vue.set(wap.ckdContent, "lyks", data.d.KSBM);
                        Vue.set(wap.ckdContent, "lyyf", data.d.YFBM);
                        Vue.set(wap.ckdContent, "lyyfmc", data.d.YFMC);
                    }
                } else {
                    malert("出库药房查询失败", 'top', 'defeadted')
                }
            });

            Vue.set(wap.popContent, 'text', this.ckdDetail[num].ypmc);//药品名称
            //库存数量
            wap.getKc(this.ckdDetail[num].ypbm);


            wap.title = "编辑材料";
            this.lYShow = false;
            wap.lYShow = false;
            $('.side-form').css({'width': '548px'});
            wap.open();
        },

        //取消
        cancel: function () {

            this.cxShow = false;
            this.dyShow = false;
            this.ckdDetail = [];
            this.isShowpopL = false;
            this.isShow = false;
            this.isShowkd = true;
            wap.ckdContent = {};
            wap.popContent = {};
        },
        kd: function (index) {
            this.num = index;
            setTimeout(function () {
                wap.$refs.autofocus.$refs.inputFu.focus();
                wap.$refs.autofocus.setLiShow(wap.$refs.autofocus.$refs.inputFu)
            }, 1000);
            this.loadNum();
            switch (this.num) {
                case 0:
                    wap.ckd = {};
                    this.ckd = {};
                    this.isShowkd = false;
                    this.isShow = true;
                    this.isShowpopL = true;
                    this.TjShow = true;
                    this.zfShow = false;
                    this.ShShow = false;
                    this.kfShow = true;
                    this.mxShShow = true;
                    $('#bzms').attr('disabled', false);
                    this.zdyxm = userName;
                    this.isUpdate = 0;
                    this.sfly = false;
                    this.ckdDetail = [];
                    break;
                case 1:
                    wap.open();
                    wap.title = '添加材料';
                    $('.side-form').css({'width': '548px'});
                    wap.popContent = {
                        ckfs: "05"
                    };
                    this.lYShow = false;
                    wap.lYShow = false;
                    this.kfShow = true;
                    this.isUpdate = 0;
                    break;
            }

        },
        getKFData: function () {
            //下拉框获取库房
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100012012"}',
                function (data) {
                    if (data.a == 0 && data.d) {
                        wrapper.KFList = data.d;
                        qxksbm = data.d[0].ksbm;
                        //默认选择库房
                        Vue.set(wrapper.param, 'kfbm', data.d[0].kfbm);
                        Vue.set(wrapper.popContent, 'kfbm', data.d[0].kfbm);
                        wap.getCsqx(); //加载完库房再次加载参数权限
                    } else {
                        malert("药库获取失败", 'top', 'defeadted');
                    }
                    //wrapper.getData();
                });
            //下拉框获取科室编码
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                function (data) {
                    if (data.a == 0) {
                        wap.KSList = data.d.list;
                    } else {
                        malert("科室获取失败!", 'top', 'defeadted');
                    }
                });

            //初始化页面加载领用人
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm", function (json) {
                if (json.a == 0) {
                    wap.glryList = json.d.list;
                } else {
                    malert("领用人员列表查询失败!" + json.c, 'top', 'defeadted');

                }
            });
            //初始化页面加科室药房
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf", function (json) {
                if (json.a == 0) {
                    wrapper.yfList = json.d.list;
                } else {
                    malert("领用人员列表查询失败!" + json.c, 'top', 'defeadted');

                }
            });
        },
        //库房改变
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            if (val[3] != null) {
                Vue.set(this[val[2][0]], val[3], val[4]);
                sessionStorage.setItem('kfmc', this.param.kfmc);
                for (var i = 0; i < wrapper.KFList.length; i++) {
                    if (wrapper.KFList[i].kfbm == val[0]) {
                        qxksbm = wrapper.KFList[i].ksbm;
                        break;
                    }
                }
                wap.getCsqx();
            }
        },
        //领药单
        LingYaoD: function () {
            wap.isChecked = [];
            wap.open();
            wap.title = '领药单';
            $('.side-form').css({'width': '805px'});
            wap.popContent = {};
            this.lYShow = true;
            wap.lYShow = true;

            var parm = {
                shzfbz: '1',
                ckbz: '0',
                page: 1,
                slkfbm: this.param.kfbm,
                rows: 2000
            };
            //获取申领单
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=Sldcx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == "0") {
                    wap.lydList = json.d.list;
                } else {
                    malert("获取领药单失败", "top", "defeadted")
                }
            });


        },
        laydate: function (e) {
            let $id = e.target.id;
            laydate.render({
                elem: e.target,
                value: $id == 'beginTimeVal' ? this.param.beginrq : this.param.endrq,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    if ($id == 'beginTimeVal') {
                        wrapper.param.beginrq = value;
                    } else {
                        wrapper.param.endrq = value;
                    }
                    wrapper.getData();
                }
            });
        }
    }
});
var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        shparam: {},// 灵药单审核参数
        isShowpopL: false,
        iShow: false,
        isTabelShow: false,
        flag: false,
        jsShow: false,
        centent: '',
        isFold: false,
        lYShow: false,
        ksList: [],
        hszList: [],
        ywckList: [],
        title: '',
        num: 0,
        selSearch: -1,
        csContent: {},
        sld: {},
        jsonList: [],
        cgryList: [],//采购员
        csqxContent: {}, //参数权限对象
        ckd: {}, //
        ckdContent: {}, //出库单对象
        KSList: [], //领用科室
        zbfsList: [],//招标方式
        ghdwList: [],//供货单位
        KFList: [], //库房
        ryList: [], //领用人
        glryList: [], //过滤领用人
        ypcdList: [], //药品产地
        rkd: {}, //入库单对象
        lyyfList: [],

        page: {
            page: 1,
            rows: 20,
            sort: "",
            order: "asc",
            parm: "",
            total: null
        },
        dg: {
            page: 1,
            rows: 20,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '材料名称': 'ypmc',
            '商品名': 'ypspm',
            '规格': 'ypgg',
            '可用库存': 'sjkc',
            '库房单位': 'kfdwmc',
            '进价': 'ypjj',
            '零价': 'yplj',
            '分装比例': 'fzbl',
            '基药': 'jy',
            '生产批号': 'scph',
            '产地': 'cdmc',
            '供货单位': 'ghdwmc',
            '材料编号': 'ypbm',
            '库存数量': 'kcsl',
            '有效期至': 'yxqz',
            '材料单位': 'yfdwmc',
            '效期': 'yxqz',

        },
        //药品信息
        popContent: {
            ckfs: "05"
        },
        popContents: {},
        sjkc: 0,//实际库存
        str: '',
        lydList: [],//领药单List
        lymxList: [],//领药明细list
        them_tran1: {},
        them1: {
            '产地编号': 'cdbm',
            '产地名称': 'cdmc',
        },
        cdEnter: false,
        queryStr: {
            page: 1,
            rows: 20,
            sort: "cdbm",
            order: "asc",
            total: null
        },
        selSearch1: -1,
        searchCon1: [],
    },
    mounted: function () {
            },
    methods: {
        //当输入值后才触发
        change1: function (add, val) {
            // //库房非空判断
            if (!add) this.queryStr.page = 1;       // 设置当前页号为第一页
            this.queryStr.parm = val
            var _searchEvent = $(event.target.nextElementSibling)[0];
            //初始化页面加载产地编码
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=query&dg=" + JSON.stringify(this.queryStr), function (data) {
                if (data.a == 0) {
                    if (add) {
                        wap.searchCon1 = wap.searchCon1.concat(data.d.list)
                    } else {
                        wap.searchCon1 = data.d.list;
                    }
                    //药品产地
                    wap.cdEnter = true;
                    wap.queryStr.total = data.d.total;
                    wap.selSearch1 = 0;
                    if (data.d.list.length != 0) {
                        // $(".selectGroup").hide();
                        $(_searchEvent).show()
                    } else {
                        $(".selectGroup").show();
                    }
                } else {
                    malert("材料产地获取失败!", 'top', 'defeadted');
                }

            });
        },
        //双击选中下拉table
        selectOne1: function (item) {
            //查询下页
            if (item == null) {
                this.queryStr.page++;
                this.change1(true, this.popContent['cdmc'])
                return;
            }
            this.popContent['cdbm'] = item['cdbm'];
            this.popContent['cdmc'] = item['cdmc'];
            $(".selectGroup").hide();
        },
        //药品名称下拉table检索数据
        changeDown1: function (event, value) {
            this.popContent1 = this.searchCon1[this.selSearch1]
            this.inputUpDown(event, this.searchCon1, "selSearch1");
            //选中之后的回调操作
            if (event.code == 'Enter' || event.keyCode == 13 || event.code == 'NumpadEnter') {
                if (this.cdEnter) {
                    this.popContent['cdmc'] = this.popContent1['cdmc'];
                    this.popContent['cdbm'] = this.popContent1['cdbm'];
                    $(".selectGroup").hide();
                }
                this.cdEnter = false;
                this.addData()
                //保留两位小数
            }
        },
        //关闭
        closes: function () {
            $(".side-form").removeClass('side-form-bg');
            $(".side-form").addClass('ng-hide');
            this.lydList = [];
        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg');
            $(".side-form").removeClass('ng-hide');
            this.lymxList = [];
        },

        //提交
        tjCom: function () {
            if (this.isChecked.length == 0) {
                malert('请选择申领单！', 'top', 'defeadted');
                return;
            }
            var index = -1;
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i]) {
                    index = i;
                    break;
                }
            }
            if (index == -1) {
                malert('请选择申领单！', 'top', 'defeadted');
                return;
            }

            var lyd = this.lydList[index];

            var yfbm = lyd.yfbm;//药房编码


            var num = 0;
            for (var i = 0; i < wrapper.KFList.length; i++) {
                if (lyd.kfbm == wrapper.KFList[i].kfbm) {
                    num++;
                    break;
                }
            }
            if (num == 0) {
                malert("未有该库房的用例权限", 'top', 'defeadted');
                return
            }
            Vue.set(wap.ckd, 'lyr', lyd.zdr);//领药人
            Vue.set(wap.ckd, 'lyrxm', lyd.zdrmc);//领药人
            Vue.set(wap.ckd, 'yfbm', yfbm);
            Vue.set(wap.ckd, 'lyyf', yfbm);

            for (var i = 0; i < wrapper.yfList.length; i++) {
                if (yfbm == wrapper.yfList[i].yfbm) {
                    Vue.set(wap.ckd, 'lyks', wrapper.yfList[i].ksbm);//领药科室
                    Vue.set(wap.ckd, 'lyksmc', wrapper.yfList[i].ksmc);//领药科室
                    break;
                }
            }

            Vue.set(wap.ckd, 'sldh', lyd.sldh);//申领单号

            for (var i = 0; i < wap.lymxList.length; i++) {
                Vue.set(wap.lymxList[i], 'cksl', wap.lymxList[i].slsl);//出库数量
                //产地编码
                for (var j = 0; j < wap.ypcdList.length; j++) {
                    if (wap.lymxList[i].ghdw == wap.ypcdList[j].cdbm) {
                        Vue.set(wap.lymxList[i], 'cdmc', wap.ypcdList[j].cdmc);
                        break;
                    }
                }
            }


            this.closes();//关闭当前领药单
            wrapper.kd(0);//开单
            wrapper.sfly = true;
            wrapper.ckdDetail = wap.lymxList;

            //修改出库标志
            var parm = {
                'ckbz': '1',
                'parm': '1', //0-未审核 1-审核 2-作废
                'yfbm': yfbm,
                'sldh': lyd.sldh
            };
            wap.shparam = parm;
        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            var parm = {
                "ylbm": 'N040030012012',
                "ksbm": qxksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N04003001201201": //出库开单打印单据1-开单保存后打印单据，0-开单保存后不打印单据
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100201 = csjson.csz;
                                }
                                break;
                            case "N04003001201202": //是否判断药房库存上限1-开单判断药房库存上限，出库数量加药房当前库不能大于药房库存上限；0-不判断，不限制
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100202 = csjson.csz;
                                }
                                break;
                            case "N04003001201203": //药房申领是否需要审核0=不(出库后药房自动增加库存)，1=是(出库后药房审核后增加库存)
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100203 = csjson.csz;
                                }
                                break;
                            case "N04003001201204": //药库出库数量是否允许为小数1、允许；0、不允许
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100204 = csjson.csz;
                                }
                                break;
                            case "N04003001201205": //出库是否只允许选取当前科室人员作为领用人1、是,0、否
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100205 = csjson.csz;
                                }
                                break;
                            case "N04003001201206": //出库开单审核权限0-有开单审核  1-有开单 2-有审核
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100206 = csjson.csz;
                                }
                                break;
                            case "N04003001201207": //出库单作废权限1、是,0、否
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100207 = csjson.csz;
                                }
                                break;
                            case "N04003001201208": //出库药房是否允许出到科室库存 0-否 1-是
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100208 = csjson.csz;
                                }
                                break;
                        }
                    }
                    wrapper.getData();
                } else {
                    malert('参数权限获取失败:' + json.c, 'top', 'defeadted');
                }
            });
        },

        //药品名称下拉table检索数据
        changeDown: function (event, type) {
            if (type == 'cksl') {
                if (wap.popContent.cksl > wap.popContent.kcsl) {
                    malert('库存不足！', 'top', 'defeadted');
                    return;
                } else {
                    this.nextFocus(event);
                    return;
                }
            }
            this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                if (type == 'text') {
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                    this.selSearch = -1;
                    this.$forceUpdate();
                } else if (type == 'xtph') {
                    this.addData();
                }
            }
        },
        //当输入值后才触发
        change: function (add, type, val) {
            this.popContent[type] = val;
            if (!wrapper.param.kfbm) {
                malert("请先择库房", 'top', 'defeadted');
                return false;
            }
            if (!add) this.page.page = 1;       //  设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.page.parm = encodeURIComponent(val);
            var bean = {
                "kfbm": wrapper.param.kfbm
            };
            //分页参数
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp&dg=' + JSON.stringify(this.page) + '&json=' + JSON.stringify(bean), function (data) {
                if (data.a == '0' && data.d) {
                    for (var i = 0; i < data.d.list.length; i++) {
                        data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                        data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                    }
                    if (add) {
                        wap.searchCon = wap.searchCon.concat(data.d.list)
                    } else {
                        wap.searchCon = data.d.list;
                    }
                    wap.searchCon = data.d.list;
                    wap.page.total = data.d.total;
                    wap.selSearch = 0;
                    if (!add && data.d.list.length != 0) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                }
            });
        },
        //库存
        getKc: function (ypbm) {
            var json = {
                ypbm: ypbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp' + '&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    wap.sjkc = data.d.list[0].sjkc;
                    Vue.set(wap.popContent, 'kcsl', data.d.list[0].sjkc);
                }
            });
        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                this.page.page++;
                this.change(true, 'ypmc', this.popContent.ypmc)
            } else {
                item.ckfs = "05";//先记录下出库方式，避免赋值后丢失
                this.popContent = item;
                this.selSearch = -1;
                this.popContent['text'] = this.popContent['ypmc'];
                $(".selectGroup").hide();
                $("#cksl").focus();
            }
        },
        //添加药品出库信息
        addData: function () {

            if (!wrapper.param.kfbm && wrapper.popContent.ckfs != '05') {
                malert('库房不能为空', 'top', 'defeadted');
                return false;
            }
            if (!this.ckdContent.lyks && wrapper.popContent.ckfs != '05') {
                malert('领用科室不能为空', 'top', 'defeadted');
                return false;
            }
            if (!this.ckdContent.lyr && wrapper.popContent.ckfs != '05') {
                malert('领用人不能为空', 'top', 'defeadted');
                return false;
            }
            // if (!this.popContent.cdbm  && this.popContent.ckfs!='05') {
            //     malert('产地不能为空', 'top', 'defeadted');
            //     return false;
            // }
            //2020-12-17添加 this.popContent.kcsl  this.popContent.cksl+
            //
            var ypbm = wap.popContent.ypbm;
            var xtph = wap.popContent.xtph;
            var cksl = wap.popContent.cksl;
            var tempCount = 0;
            for (var i = 0; i < wrapper.ckdDetail.length; i++) {
                if (wrapper.isUpdate != 1 && wap.isedit != i) {
                    if (wrapper.ckdDetail[i].ypbm == ypbm && wrapper.ckdDetail[i].xtph == xtph && parseFloat(wrapper.ckdDetail[i].cksl) + parseFloat(cksl) > this.popContent.kcsl) {
                        malert("当前已领该材料，剩余库存不足!", 'top', 'defeadted');
                        return false;
                    }
                }
            }


            if (parseFloat(this.popContent.cksl) > parseFloat(this.popContent.kcsl)) {
                malert('出库数量不能大于当前库存数量！', 'top', 'defeadted');
                return false;
            }
            // this.popContent['yxqz'] = $('#_yxqz').val();
            // this.popContent['scrq'] = $('#_scrq').val();
            if (this.popContent.yplx == '0' && !this.popContent['yxqz']) {
                malert("有效期限不能为空", 'top', 'defeadted');
                return false;
            }
            //验证数据完整性
            var _input = $("#rkgl_enter input,select");
            var haveError = false;
            for (var i = 0; i < _input.length; i++) {
                if (_input.eq(i).attr("data-notempty") && (_input.eq(i).val() == null || _input.eq(i).val() == '')) {
                    haveError = true;
                    _input.eq(i).addClass("emptyError");
                }
            }
            if (haveError) {
                malert("录入区数据不完整", 'top', 'defeadted');
                return false;
            }

            //先判断权限设置是否需要限制出库数量
            if (wap.csqxContent.cs00200100202 == '1') { //1-开单判断药房库存上限，出库数量加药房当前库不能大于药房库存上限；0-不判断，不限制
                //判断出库数量是否超过了库存数量
                if (this.popContent['cksl'] > this.popContent['sjkc']) {
                    malert("出库太多了，库存数量不够了", 'top', 'defeadted');
                    return false;
                }
            }
            //根据参数权限判断是都允许为小数
            var xs = /^(\d|[1-9]\d+)(\.\d+)?$/; //大于0的整数小数
            var zzs = /^[1-9]*[1-9][0-9]*$/; //正整数的表达式
            if (wap.csqxContent.cs00200100204 == '0') { //不允许
                if (this.popContent['cksl'] == null || this.popContent['cksl'] == '' || this.popContent['cksl'] == 'undefined') {
                    return false;
                }
                if (!zzs.test(this.popContent['cksl'])) {
                    malert("出库数量为大于0的数", 'top', 'defeadted');
                    return false;
                }
            } else { //允许
                if (!xs.test(this.popContent['cksl'])) {
                    malert("出库数量为大于0的数", 'top', 'defeadted');
                    return false;
                }
            }
            //相同药品不能重复出库
            var ypbm = wap.popContent.ypbm;
            var xtph = wap.popContent.xtph;
            this.popContent.scrq = new Date(this.popContent.scrq).getTime()
            this.popContent.yxqz = new Date(this.popContent.yxqz).getTime()
            if (wrapper.isUpdate == 0) {
                //添加
                for (var i = 0; i < wrapper.ckdDetail.length; i++) {
                    if (wrapper.ckdDetail[i].ypbm == ypbm && wrapper.ckdDetail[i].xtph == xtph) {
                        malert("材料【" + this.popContent.ypmc + "】已存在,请修改已有数据!", 'top', 'defeadted');
                        return false;
                    }
                }
                malert("添加成功！", 'top', 'success');
                wrapper.ckdDetail.push(this.popContent);
            } else {
                //修改
                // var json = this.popContent;
                Vue.set(this.popContent, 'ckdh', wrapper.ckd == null ? '' : wrapper.ckd.ckdh);
                wrapper.ckdDetail[this.isedit] = JSON.parse(JSON.stringify(this.popContent))
                wrapper.$forceUpdate()
                wrapper.isUpdate = 0;
                this.closes();
            }
            this.popContent = {};
            this.str = !this.str;
            $("#ypmc").focus();

        },
        //领用科室加载下拉框回调
        resultKsChange: function (val) {
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
            var types = val[2][val[2].length - 1]; //方便获取到是什么类型
            if (types == 'lyks') {
                Vue.set(this.ckdContent, 'lyks', val[0]);

                //请求后台获取到出库药房
                this.dg.parm = this.ckdContent.lyks;
                if (this.dg.parm == null || this.dg.parm == '') {
                    return false;
                }
                this.dg.rows = 20000;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=findYFByKS' + '&dg=' + JSON.stringify(this.dg), function (data) {
                    if (data.a == 0) {
                        if (data.d == null) {
                            Vue.set(wap.ckdContent, "lyyfmc", "科室出库");
                            wap.lyyfList = [{'lyyf': null, 'lyyfmc': '科室出库'}];
                        } else {
                            wap.lyyfList = [{'lyyf': data.d.YFBM, 'lyyfmc': data.d.YFMC}, {
                                'lyyf': '9',
                                'lyyfmc': '科室出库'
                            }];
                            wrapper.$forceUpdate();
                            Vue.set(wap.ckdContent, "lyks", data.d.KSBM);
                            Vue.set(wap.ckdContent, "lyyf", data.d.YFBM);
                            Vue.set(wap.ckdContent, "lyyfmc", data.d.YFMC);
                        }
                    } else {
                        malert("出库药房查询失败", 'top', 'defeadted')
                    }
                });

                //出库是否只允许选取当前科室人员作为领用人
                if (wap.csqxContent.cs00200100205 == '0') {
                    wap.ryList = wap.glryList;
                } else if (wap.csqxContent.cs00200100205 == '1') {
                    //根据科室过滤领用人
                    wap.ryList = jsonFilter(wap.glryList, "ksbm", this.ckdContent.lyks);
                }
            }
            //回车跳转
            if (val[1] != null) {
                this.nextFocus(val[1], val[2]);
            }
        },
        //产地获取
        resultRydjChange_cd: function (val) {
            Vue.set(this.popContent, 'cdbm', val[0]);
            Vue.set(this.popContent, 'cdmc', val[4]);
        },
        lymx: function (index) {
            wap.lymxList = [];
            var parm = this.lydList[index];
            Vue.set(parm, 'rows', '500');
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglLygl&types=sldmxcx&parm=' + JSON.stringify(parm), function (data) {
                if (data.a == "0") {
                    wap.lymxList = data.d.list
                } else {
                    malert("领药单明细查询失败！", "top", "defeadted");
                }
            });

        },


    }


});

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

});




