var blsjsb=new Vue({
    el:'.blsjsb',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        checked:false,
        nbtclb:'',
        fsks:[],
        hzList:[],
        blsjlb:[],
        hzzl:{
            brxm:'',
            nl:'',
            nldw:'',
            brxb:'',
            ksmc:'',
            rycwbh:'',
            bah:'',
            lczd:'',
            zcry:''
        },
        popContent:{
            sfnm:'2',
            bgrxm:'',
        },
        df:{
            title:'纳入持续改进项目'
        },
    },
    beforeCreate:function(){
    },
    created:function(){
        this.initPage();
        this.getPulldownBrxx();
    },
    mounted:function(){
        this.popContent.txsj = this.fDate(new Date(),'date');
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    blsjsb.popContent.fssj = value;
                } else {
                    blsjsb.popContent.fssj = '';
                }
            }
        });
    },
    methods:{
        gjxm:function () {
            console.log("======" + JSON.stringify(blsjsb.popContent));
            console.log("------" + JSON.stringify(blsjsb.blsjlb));
            this.$http.post("/actionDispatcher.do?reqUrl=New1Blsjbg&types=save",JSON.stringify(blsjsb.popContent)).then(function(data){
                if(data.body.a == '0')
                {
                    malert("保存成功!","","success");
                }
                else {
                    malert("保存失败!","","defeadted");
                }
            })
        },
        initPage:function(){
            common.delayOpenloading('blsjsb')
            //下拉框科室加载
            this.param.rows=20000;
            this.param.sort='';
            this.param.order='';
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg="+JSON.stringify(this.param),function (json) {
                blsjsb.fsks = json.d.list;
            });

            //获取当前登录人信息
            $.getJSON("/actionDispatcher.do?reqUrl=New1CommonUtil&types=queryCurrentUser",function (json) {
                console.log(JSON.stringify(json));
                blsjsb.currentUser = json.d;
                blsjsb.popContent.bgrxm = json.d.ryxm;
                blsjsb.popContent.sjhm = json.d.sjhm;
            });
        },
        //下拉加载患者信息
        getPulldownBrxx:function(){
            this.param.rows=20000;
            this.param.sort='';
            this.param.order='';
            this.param.parm='';
            $.getJSON("/actionDispatcher.do?reqUrl=New1CommonUtil&types=queryBrxxList&dg="+JSON.stringify(this.param),function (json) {
                blsjsb.hzList = json.d.list;
                common.closeLoading();
            });
        },
        setBrjbxx:function(e){
            for(var i=0;i<blsjsb.hzList.length;i++)
            {
                if(e[0] == blsjsb.hzList[i].brid)
                {
                    blsjsb.hzzl = blsjsb.hzList[i];
                    blsjsb.popContent.brid = blsjsb.hzList[i].brid;
                    blsjsb.popContent.nl = blsjsb.hzList[i].nl;
                }
            }
        },
        sfnm:function(){
            if(blsjsb.popContent.sfnm=='2') {
                blsjsb.popContent.bgrxm = blsjsb.currentUser.ryxm;
                blsjsb.popContent.sjhm = blsjsb.currentUser.sjhm;
            }
            else {
                blsjsb.popContent.bgrxm = "***";
                blsjsb.popContent.sjhm = "***********";
            }
        },
    },
});
