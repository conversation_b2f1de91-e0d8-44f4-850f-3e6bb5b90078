<link href="/page/ybjk/gzwxhy/ybyw/sjsc/sjsc.css" rel="stylesheet" type="text/css">

<div class="cd_014gjlist" id="cd_014gjlist" style="height: 90%;width: 90%;">
    <div class="zui-table-view">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class=" ">
                        <div class="zui-table-cell cell-xxl text-left"><span>处方/医嘱号</span></div>
                    </th>
                    <th class="">
                        <div class="zui-table-cell cell-m"><span>总金额</span></div>
                    </th>
    				<th class="">
    				    <div class="zui-table-cell cell-m"><span>个人账户</span></div>
    				</th>
    				<th class="">
    				    <div class="zui-table-cell cell-m"><span>医保基金</span></div>
    				</th>
					<th class="">
					    <div class="zui-table-cell cell-m"><span>现金</span></div>
					</th>
					<th class="">
					    <div class="zui-table-cell cell-xl"><span>操作</span></div>
					</th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body  over-auto" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr :id="'tr'+$index" v-for="(item, $index) in lsjsxx" >
                    
                    <td class="">
                        <div class="zui-table-cell cell-xxl text-left">
                            {{item.sfpch}}
                        </div>
                    </td>
    				<td class="">
    				    <div class="zui-table-cell cell-m" >
    				        {{item.ylfze}}
    				    </div>
    				</td>
    				<td class="">
    				    <div class="zui-table-cell cell-m" >
    				        {{item.grzhzc}}
    				    </div>
    				</td>
					<td class="">
					    <div class="zui-table-cell cell-m" >
					        {{item.jjzfze}}
					    </div>
					</td>
					<td class="">
					    <div class="zui-table-cell cell-m" >
					        {{item.zrxjzc}}
					    </div>
					</td>
                    <td class="">
                        <div class="zui-table-cell cell-xl">
                            <input type="button" name="" id="jxgj" value="共济" onclick="jxgj($index)" />
							<input type="button" name="" id="qxjxgj" value="取消共济" onclick="jxgj($index)" />
                        </div>
                    </td>
    				
                </tr>
                </tbody>
            </table>
        </div>
	
</div>




<script type="application/javascript" src="/newzui/pub/js/insuranceGbUtils.js"></script>
<script type="application/javascript" src="/newzui/page/mzsf/sfjs/mzsf/insurancePort/014cdyhybgb/uuid.js"></script>
<script type="application/javascript" src="/newzui/page/mzsf/sfjs/mzsf/insurancePort/014cdyhybgb/xmltojson.js"></script>
<script type="application/javascript" src="/newzui/page/mzsf/sfjs/mzsf/insurancePort/014cdyhybgb/014cdyhybgjlist.js?v=20210121"></script>

