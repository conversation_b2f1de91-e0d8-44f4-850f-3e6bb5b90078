<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>参数设置</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="cssz.css" rel="stylesheet">
</head>
<body class="">
<div class="over-auto background-f" id="wrapper" v-cloak>
    <div class="panel">
        <div class="flex-container  tong-top flex-align-c">
            <button class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5" @click="save">保存</button>
        </div>
    </div>
    <div class="flex-container  flex-wrap-w flex-align-c padd-r-10 padd-l-10">
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">接口地址</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text" v-model="popContent.jkdz"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">登&emsp;&emsp;陆<br/>用&ensp;户&ensp;名</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text" v-model="popContent.dlyhm"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">登录密码</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"  v-model="popContent.dlmm"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">读&emsp;&emsp;卡<br/>器&ensp;类&ensp;型</span>
            <div class="zui-input-inline wh280">
                <!--<select-input @change-data="resultChange" :not_empty="true" :child="nh_dkq"-->
                              <!--:index="popContent.dkqlx" :val="popContent.dkqlx" :name="'popContent.dkqlx'">-->
                <!--</select-input>-->
                <select-input class="wh180" @change-data="resultChange"
                              :child="nh_dkq" :index="'popContent.dkqlx'" :val="popContent.dkqlx"
                              :name="'popContent.dkqlx'"  :not_empty="true" >
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">端&ensp;口&ensp;号</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text" v-model="popContent.port"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">卡&emsp;&emsp;座</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text" v-model="popContent.kz"/>
            </div>
        </div>

        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">门诊补偿凭证打印联数</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text" v-model="popContent.bcdyls"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">住院补偿凭证打印联数</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text" v-model="popContent.zybcdyls"/>
            </div>
        </div>
        <!--<div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">-->
        <!--<span class="padd-r-5">读卡器类型</span>-->
        <!--<div class="zui-input-inline wh280">-->
        <!--<select-input @change-data="resultChange" @keydown.native="saveBc($event,'saveData')" :data-notEmpty="false"-->
        <!--:child="yqList" :index="'yqmc'" :index_val="'yqbm'":val="popContent.yqbm"-->
        <!--:name="'popContent.yqbm'" :search="true" id="tz">-->
        <!--</select-input>-->
        <!--</div>-->
        <!--</div>-->

    </div>
    <div class="flex-container  flex-wrap-w flex-align-c padd-r-10 padd-l-10">
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户&ensp;I&ensp;D</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">补偿年度</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">本县保障<br/>结束时间</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户所在<br/>机构类型</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">打印明细<br/>是否汇总</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户所在<br/>机构级别</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">单次刷卡<br/>时&emsp;&emsp;间</span>
            <div class="zui-input-inline position wh280">
                <input class="zui-input" type="text"/>
                <span class="cm">天</span>
            </div>
        </div>

        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">本县芯片<br/>刷卡授权</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户密码</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户姓名</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户电话</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户所在<br/>单位名称</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户所在<br/>单位ID</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">本县保障<br/>起始时间</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用&ensp;户&ensp;登<br/>录&emsp;&emsp;名</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">刷卡标志<br/>是否开启</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>

        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">此医疗机构刷卡权限是否开启</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">是否开启<br/>药品限价</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>

        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">日记账药品删除期限</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>

        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户所在机构详细地址编码</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">是否启用单次刷卡时间限制</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">用户所在机构地区编码</span>
            <div class="zui-input-inline wh280">
                <input class="zui-input" type="text"/>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="cssz.js"></script>
</body>
</html>