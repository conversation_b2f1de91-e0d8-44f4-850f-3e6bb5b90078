var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        kfList: [{
            kfbm:'08',
            kfmc:'卫消库',
            ksbm:'0952'
        }],
        ypgx: [],
        jsonList: [],
        param: {
            page: 1,
            rows: 100,
            sort: 'djrq',
            order: 'desc',
            tybz:'0',
            //kfbm: null,
            kfbm: "1",
            kfmc: '',
        },
        json:{
            tybz:'0',
            kfbm: null,
        },
        isChecked: []
    },
    mounted: function () {
        this.getKfbm();

    },
    methods: {
        fDate: function (value, types) {
            if (value == null) {
                return "";
            }
            var date = new Date(value);
            Y = date.getFullYear(),
                m = date.getMonth() + 1,
                d = date.getDate(),
                H = date.getHours(),
                i = date.getMinutes(),
                s = date.getSeconds();
            if (m < 10) {
                m = '0' + m;
            }
            if (d < 10) {
                d = '0' + d;
            }
            if (H < 10) {
                H = '0' + H;
            }
            if (i < 10) {
                i = '0' + i;
            }
            if (s < 10) {
                s = '0' + s;
            }
            var t = null;
            if (types == "date") {
                t = Y + '-' + m + '-' + d;
            } else if (types == "time") {
                t = H + ':' + i;
            } else if (types == "times") {
                t = H + ':' + i + ':' + s;
            } else if (types == "year") {
                t = Y;
            } else if (types == "month") {
                t = m;
            } else if (types == "day") {
                t = d;
            }
            else if (types == "shortY") {
                t = m + '-' + d + ' ' + H + ':' + i;
            }
            else if (types == "shortY2") {
                t = m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            else if (types == "YY") {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            else if (types == "AllDate") {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            else {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            return t;
        },

        //新增
        AddMdel: function () {
            wap.title = '新增卫生字典';
            wap.open();
            wap.fzblNot = false;
            wap.disabled = false;
            wap.popContent = {};
            Vue.set(wap.popContent, 'gxbm', wrapper.json.ypflbm);

        },
        getKfbm: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100014001"}', function (data) {
                if(data.a=='0' && data.d){
                    // wrapper.kfList = data.d;
                    wrapper.param.kfbm = wrapper.kfList[0]['kfbm'];
                    wrapper.json.kfbm = wrapper.kfList[0]['kfbm'];
                    wrapper.getData();
                }
            });
        },
        haveKc: function (num) {
            var ypbm = wrapper.jsonList[num]['ypbm'];
            // 查询卫生在库房和药房的库存
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=allKc&ypbm=' + ypbm + '&kfbm=' + wrapper.kfbm, function (json) {
                if (json.d) {
                    var total = json.d.KFKC + json.d.YFKC;
                    if (total > 0) {
                        wap.fzblNot = true
                    } else {
                        wap.fzblNot = false
                    }
                }else {
                    wap.fzblNot = false;
                }
            });
        },

        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            //先获取到操作的哪一个
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            if(val[2][val[2].length - 1] =='tybz'){
                Vue.set(this.json, val[2][val[2].length - 1], val[0]);
            }

            this.$forceUpdate();
            this.goToPage(1);
        },
        //进入页面加载列表信息
        getData: function () {
            common.openloading('.zui-table-body');
            // 查询字典数据
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=query&kfbm=" +
                this.param.kfbm + "&dg=" + JSON.stringify(this.param)+"&json=" + JSON.stringify(this.json),
                function (json) {
                    console.log(JSON.stringify(json));
                    if (json.a == "0") {
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.jsonList = json.d.list;
                    } else {
                        malert(json.c, 'top', 'defeadted')
                    }
                });
            common.closeLoading()
        },
        edit: function (num) {
                        wap.title = '编辑卫生字典'
            wap.open();
            wap.disabled = true;

            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            let tpzlbm = wap.popContent.hczlbm.toString();
            if(tpzlbm && tpzlbm.indexOf("[")<0){
                let tpzlbms = tpzlbm.split(",");
                let zhi = "";
                for (let i = 0; i < tpzlbms.length; i++){
                    if(i == tpzlbms.length-1){
                        zhi+='"'+tpzlbms[i]+'"';
                    }else{
                        zhi+='"'+tpzlbms[i]+'",';
                    }
                }

                tpzlbm = '['+zhi+']';
            }
            if(tpzlbm){
                wap.popContent.hczlbm  =JSON.parse(tpzlbm);
            }

            //显示DDD数小数位
            wap.popContent.ddds = wap.popContent.ddds ? wap.popContent.ddds.toFixed(2):wap.popContent.ddds;
            console.log(wap.popContent);
            wrapper.haveKc(num);

        },
        //删除
        remove: function (num) {
            var ypbmList = [];
            //num -1 批量删除
            if (num == -1) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        var ypbm = {};
                        ypbm.ypbm = this.jsonList[i].ypbm;
                        ypbmList.push(ypbm);
                    }
                }
            } else {
                //单个删除
                var obj = {
                    ypbm: this.jsonList[num].ypbm
                }
                ypbmList.push(obj);
            }


            if (ypbmList.length == 0) {
                malert("请选中您要删除的数据", 'top', 'defeadted');
                return false;
            }
            if (common.openConfirm("确认删除该条信息吗？", function () {
                //'{"list":'+ JSON.stringify(wrapper.saveList) +'}'
                var json = "{'list':" + JSON.stringify(ypbmList) + "}";
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=delete',
                    json).then(function (data) {
                    if (data.body.a == 0) {
                        malert("删除成功", 'top', 'success')
                        wrapper.getData();
                    } else {
                        malert("删除失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }
        },

    },
});

var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,checkData],
    components: {
        'search-table': searchTable
    },
    data: {
        isShow: false,
        disabled: false,
        popContent: {
			ypbm:''
		},
        queryPage: {
            page: 1,
            rows: 50,
            sort: "to_number(cdbm)",
            tybz: "0",
            total: null,
			order :'desc'
        },
        them_tran: {},
        them: {
            '产地编码': 'cdbm', '产地名称': 'cdmc',
        },
        selSearch:-1,
        item: {},
        isKeyDown: null,
        searchCon: [],
        num: 1,
        ypjx: [],
        ypgx: [],
        ypzl: {'04':'卫生材料'
        ,'05':'高值卫生'
        ,'06':'无菌卫生'
        , '07':'植入卫生'
        ,'08':'一次性卫生'
        },
        ypzlList:[{
            zlbm:'04',
            zlbmmc:'卫生材料'
        },{
            zlbm:'05',
            zlbmmc:'高值卫生'
        }],
        hcbafl_tran:[{
            balb:'01',
            balbmc:'手术'
        },{
            balb:'02',
            balbmc:'治疗'
        },{
            balb:'03',
            balbmc:'检查'
        }],
        cwlxfl_tran:[{
            cwlx:'01',
            cwlxmc:'医疗材料'
        },{
            cwlx:'02',
            cwlxmc:'化学试剂'
        },{
            cwlx:'03',
            cwlxmc:'X光胶片'
        },{
            cwlx:'04',
            cwlxmc:'医疗低值'
        },{
            cwlx:'05',
            cwlxmc:'办公用品'
        },{
            cwlx:'06',
            cwlxmc:'维修'
        },{
            cwlx:'07',
            cwlxmc:'固定资产'
        },{
            cwlx:'08',
            cwlxmc:'低固'
        }],
        ypcd: [],
        ypcd1:[],
        kjyzl: [],
        ghdw: [],
        ypdw: [],
        ybtclb: [],
        yyff: [],
        title: '',
        selectBm: '',
        selectMc: '',
        ifClick: true, //判断是否点击了按钮
        kfbm: null,
        fzblNot: false,
		isxzcd :false,
		cdpopContent:{},
		isxzcj:false,
		cjpopContent:{},
		cjlxlb: {
			"2": "物资",
		    "1": "卫生",
		    "0": "全部"
		},
    },
    computed:{
        filterSsbfzmc: function () {
                        return function (index) {
                return this.popContent['zlbm'];
            }
        }
    },
    mounted: function () {
        this.getObjData();
        this.getFl();

        laydate.render({
            elem: '.times4'
            , theme: '#1ab394',
            done: function (value, data) {
                wap.popContent.zczyxq = value
            }
        });
    },
    methods: {
		//保存
		confirms: function() {
			if(!wap.cdpopContent.tybz){
				Vue.set(wap.cdpopContent,'tybz','0');
			}
		    if(wap.cdpopContent.cdmc == null) {
		        malert("请输入产地名称",'top','defeadted')
		        return;
		    }
		    this.$http.post('/actionDispatcher.do?reqUrl=YkglKfwhCdbm&types=save',
		        JSON.stringify(wap.cdpopContent))
		        .then(function(data) {
		            if(data.body.a == 0) {
		                malert("数据更新成功",'top','success');
		                wap.isxzcd = false;
						wap.cdpopContent = {};
						wap.getCdData(false);
		            } else {
		                malert("数据失败",'top','defeadted');
		            }
		        }, function(error) {
		            console.log(error);
		        });
		},
        fDate: function (value, types) {
            if (value == null) {
                return "";
            }
            var date = new Date(value);
            Y = date.getFullYear(),
                m = date.getMonth() + 1,
                d = date.getDate(),
                H = date.getHours(),
                i = date.getMinutes(),
                s = date.getSeconds();
            if (m < 10) {
                m = '0' + m;
            }
            if (d < 10) {
                d = '0' + d;
            }
            if (H < 10) {
                H = '0' + H;
            }
            if (i < 10) {
                i = '0' + i;
            }
            if (s < 10) {
                s = '0' + s;
            }
            var t = null;
            if (types == "date") {
                t = Y + '-' + m + '-' + d;
            } else if (types == "time") {
                t = H + ':' + i;
            } else if (types == "times") {
                t = H + ':' + i + ':' + s;
            } else if (types == "year") {
                t = Y;
            } else if (types == "month") {
                t = m;
            } else if (types == "day") {
                t = d;
            }
            else if (types == "shortY") {
                t = m + '-' + d + ' ' + H + ':' + i;
            }
            else if (types == "shortY2") {
                t = m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            else if (types == "YY") {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            else if (types == "AllDate") {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            else {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            return t;
        },
		cjconfirms:function(){
			if(!wap.cjpopContent.tybz){
				Vue.set(wap.cjpopContent,'tybz','0');
			}
			if(wap.cjpopContent.cdmc == null) {
			    malert("请输入卫生厂家名称",'top','defeadted')
			    return;
			}
			this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=save',
			    JSON.stringify(wap.cjpopContent))
			    .then(function(data) {
			        if(data.body.a == 0) {
			            malert("数据更新成功",'top','success');
			            wap.isxzcj = false;
						wap.cjpopContent = {};
						wap.getCdData(false);
			        } else {
			            malert("数据失败",'top','defeadted');
			        }
			    }, function(error) {
			        console.log(error);
			    });
		},
		getCdData:function(add){
			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=query&dg=' + JSON.stringify(this.queryPage), function (data) {
			    if (data.a==0) {
			        if (add) {//不是第一页则需要追加
			            wap.searchCon = wap.searchCon.concat(data.d.list)
			        } else {
			            wap.searchCon = data.d.list;
			        }
			        wap.page.total = data.d.total;
			        wap.selSearch = 0;
			    }
			});
		},
        Wf_change: function (add, index, val,event,bm,mc) {
			if(!val){
				$(".selectGroup").hide();
				return;
			}
			console.log(val)
			this.popContent[mc] = val;
            this.item = {};
            dqindex = index;
			
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.queryPage.parm = trimStr(encodeURIComponent(val));
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=query&dg=' + JSON.stringify(this.queryPage), function (data) {
                if (data.a==0) {
                    if (add) {//不是第一页则需要追加
                        wap.searchCon = wap.searchCon.concat(data.d.list)
                    } else {
                        wap.searchCon = data.d.list;
                    }
                    wap.page.total = data.d.total;
                    wap.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }else {
                        $(".selectGroup").hide();
                    }
                }
            });
        },
        changeDown: function (index, event, searchCon,bm,mc) {
            ypIndex = index;
            this.selectBm=bm;
            this.selectMc=mc;
            this.inputUpDown(event, this[searchCon], 'selSearch');
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                this.item = this[searchCon][this.selSearch]
                this.popContent[bm] = this.item.cdbm;
                this.popContent[mc] = this.item.cdmc;
                this.$forceUpdate();
                this.selSearch = -1;
                $(".selectGroup").hide();
                this.$nextTick(function (){
                    this.nextFocus(event);
                })
            }
        },
        //双击选中下拉table
        selectOne1: function (item) {
            //查询下页
            if (item == null) {
                this.queryStr.page++;
                this.Wf_change(true, dqindex,this.item[this.selectMc],this.selectBm)
                return;
            }
            this.popContent[this.selectBm] = item.cdbm;
            this.popContent[this.selectMc] = item.cdmc;
            this.selSearch = -1;
            $(".selectGroup").hide();
        },
        selectFun:function (type){
            
            if(!this.popContent.ypbm)return  false;
            this.param=JSON.parse(JSON.stringify(wrapper.param));
            this.param.parm=this.popContent.ypbm;
			var that = this;
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=query&kfbm=" +
                wrapper.param.kfbm + "&dg=" + JSON.stringify(this.param)+"&json=" + JSON.stringify(wrapper.json), function (json) {
                    if (json.a == "0" && json.d.list.length) {
						Vue.set(that.popContent,'ypbm','');
						that.$forceUpdate()
                        malert('请注意，编码已经存在', 'top', 'defeadted')
						return false;
                    } else {
						if(type){
							wap.savebaocun();
						}
						return true;
                    }
                });
        },
        //进入页面加载列表信息
        getFl: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=queryYpfl&dg=" + JSON.stringify({"page":1,"rows":1000000,"sort":"","parm":"","order":"asc"}), function(json) {
                if(json.a == '0') {
                    wap.flList = json.d.list;
                } else {
                    malert(json.c,'top','defeadted')
                }
            });
        },
    	changeFzbl: function (add, val) {
            if(!this.popContent.ypjj || this.popContent.ypjj==0){
                wap.popContent.yplj=0.00;
            }else{
                wap.popContent.yplj = (this.popContent.ypjj/val).toFixed(2);
            }
            this.$forceUpdate()
        },
        searching: function (add, val) {
        	if(!this.popContent.fzbl || this.popContent.fzbl==0){
        		wap.popContent.yplj=0.00;
        	}else{
        		wap.popContent.yplj = (val/this.popContent.fzbl).toFixed(2);
        	}
        	this.$forceUpdate()
        },
        getObjData: function () {
            //分页信息
            pageParam = {
                'page': 1,
                'rows': 500,
                'lx':'4'
            }

            //分页信息
            pageParamZl = {
                'yplx':3,
                'page': 1,
                'rows': 500
            }
            // 查询卫生剂型
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpjx&types=query&dg=" + JSON.stringify(pageParam), function (json) {
                wap.ypjx = json.d.list
            });

            // 查询卫生功效
            $.getJSON("/actionDispatcher.do?reqUrl=YkglKfwhHcgx&types=query&dg=" + JSON.stringify(pageParam)+'&lx=4', function (json) {
                wap.ypgx = json.d.list
                let obj={
                    'gxbm':'',
                    'gxmc':'全部'
                };
                wap.ypgx.push(obj);
                wrapper.ypgx = json.d.list
            });
            // 查询卫生种类
            // $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl&dg=' + JSON.stringify(pageParamZl), function (json) {
            //     wap.ypzl = json.d.list
            // });
            // 查询抗菌药种类
            pageParam = {
                'page': 1,
                'rows': 500
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=querykjy&dg=" + JSON.stringify(pageParam), function (json) {
                wap.kjyzl = json.d.list
            });


            //供货单位
            var pageGhdw = {
                'page': 1,
                'rows': 20000,
                'sort': 'dwbm',
                'tybz': '0',
                'lx': '3'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(pageGhdw),
                function (json) {
                    if (json.a == 0) {
                        wap.ghdw = json.d.list
                    } else {
                        malert("供货单位获取失败", 'top', 'defeadted');
                    }
                });

            // 卫生单位
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypdw&dg=' + JSON.stringify(pageParam), function (json) {
                wap.ypdw = json.d.list
            });

            // 医保统筹类别
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yptclb&dg=' + JSON.stringify(pageParam), function (json) {
                wap.ybtclb = json.d.list
            });

            // 用药方法
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yyff&dg=' + JSON.stringify(pageParam), function (json) {
                wap.yyff = json.d.list
            });
        },
        //关闭
        closes: function () {
            this.num = 1
        },
        open: function () {
            this.num = 0
        },
        loadNum: function () {
            this.num = wrapper.num;
        },
        // 保存数据前的验证
        checkData: function () {
            var isError = false;

            if (this.popContent.kfdw == this.popContent.yfdw && this.popContent.fzbl != 1) {
                malert("药房单位和库房单位相同，分装比例必须为1", 'top', 'defeadted');
                isError = true
            }

            if (this.popContent.kfdw != this.popContent.yfdw && this.popContent.fzbl == 1) {
                malert("药房和库房单位不同，分装比例不能为1", 'top', 'defeadted');
                isError = true
            }
            
            // if (this.popContent.fydw && this.popContent.yfdw == this.popContent.fydw && this.popContent.fyjl != 1) {
            //     malert("药房单位和药房销售单位相同，药房销售剂量必须为1", 'top', 'defeadted');
            //     isError = true
            // }
            if (!wrapper.param.kfbm) {
                malert("请选择库房", 'top', 'defeadted');
                isError = true;
            }
            // if (this.popContent.yfdw != this.popContent.jldw && this.popContent.fzbl == 1) {
            //     malert("药房单位和剂量单位不同，分装比例不能为1",'top','defeadted');
            //     isError = true
            // }
            if (this.popContent.ypjj < 0 || this.popContent.yplj < 0) {
                malert("金额不正确！", 'top', 'defeadted');
                isError = true
            }

            if (isError) {
                // $("#fzbl").addClass("emptyError");
                return false;
            }
            return true;
        },
        setDM:function(ypmc){
            this.setPYDM(ypmc, 'popContent', 'pydm')
            this.popContent.wbjm = this.setWBJM(ypmc)
        },
        setYfypjg:function(){
            Vue.set(this.popContent,'yfyplj',wap.MathFun6(this.popContent.yplj / this.popContent.fzbl));
            Vue.set(this.popContent,'yfypjj',wap.MathFun6(this.popContent.ypjj / this.popContent.fzbl));
        },
       toArray:function (list, start) {
            start = start || 0;
            var i = list.length - start;
            var ret = new Array(i);
            while (i--) {
                ret[i] = list[i + start];
            }
            return ret
        },
        ksSelectCB: function (keyList, itemList) {
            console.log(keyList, itemList)


            this.popContent.hczlbm = keyList;
        },
        bfzSelectCB: function (keyList, itemList,parentIndex) {
		                if(!keyList || keyList.length <= 0){
                this.popContent[parentIndex] = null;
            }else{
                var bms = "";
                for (var i = 0; i < keyList.length; i++) {
                    bms += keyList[i] + ';';
                }
                bms = bms.substring(0,bms.length - 1);
                this.popContent[parentIndex] = bms;
            }
        },
		savebaocun:function(){
			// 提交前验证数据
			//if (!this.ifClick) return; //如果为false表示已经点击了不能再点
			this.ifClick = false;
            if(!this.popContent.ypbm){
                this.ifClick = true;
                malert("卫生编码为空", 'top', 'defeadted');
                return false;
            }

            this.ifClick = false;
            // if(!this.popContent.zczyxq){
            //     this.ifClick = true;
            //     malert("注册证有效期为空", 'top', 'defeadted');
            //     return false;
            // }
                        let tpzlbm = wap.popContent.hczlbm
            //药品种类编码默认，实际卫生用hczlbm为了不影响药品
            wap.popContent.zlbm='04';
            var bms = "";
            if(tpzlbm.length){
                for (var i = 0; i < tpzlbm.length; i++) {
                    bms += tpzlbm[i] + ',';
                }
                bms = bms.substring(0,bms.length - 1);
                wap.popContent.hczlbm =bms;
            }
			if (!this.empty_sub('contextInfo')) {
			    this.ifClick = true;
			    return false;
			}
			if (!this.checkData()) {
			    this.ifClick = true;
			    return false
			}
			console.log(3321);
			wap.popContent['kfbm'] = wrapper.param.kfbm;
			wap.popContent['ghdwmc'] = wap.listGetName(wap.ghdw, wap.popContent['ghdw'], 'dwbm', 'dwmc');
			//wap.popContent['ybtclb'] = '-1';
			if (!wap.popContent['tybz']){
                wap.popContent['tybz'] = "0"
            }
            if (!wap.popContent['ybtclb']){
                wap.popContent['ybtclb'] = "-1"
            }
			this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=save', JSON.stringify(this.popContent)).then(function (data) {
			        if (data.body.a == 0) {
			            malert("数据更新成功", 'top', 'success');
			            wrapper.getData();
			            wap.closes();
			            wap.ifClick = true;
			            wap.popContent = {};
			        } else {
			            wap.ifClick = true;
			            malert("数据提交失败", 'top', 'defeadted');
			        }
			    }, function (error) {
			        wap.ifClick = true;
			        console.log(error);
			    });
		},


        saveData: function () {
		    console.log("==========================================");
            this.disabled;
			if(!this.disabled){
			      wap.selectFun(1);
			}else{
				wap.savebaocun();
			}
            
        },
    }
});



