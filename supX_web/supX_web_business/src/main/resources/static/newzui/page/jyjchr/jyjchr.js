let brxx = sessionStorage.getItem('brxx') && JSON.parse(sessionStorage.getItem('brxx'));
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        jsonList: [],
        isShowpopL: false,
        isTabelShow: false,
        isShow: false,
        keyWord: '',
        popContent: {},
        brxx: brxx,
        title: '',
        totle: '',
        num: 0,
        param: {
            page: 1,
            brid: brxx.brid,
            yymc: null,
            rows: 100,
            total: '',
        },
    },
    mounted: function () {
        this.getData();
    },
    methods: {
        //新增
        AddMdel: function () {
            wap.title = '新增检验检查互认记录';
            wap.open();
            wap.popContent.jlid = null;
        },
        //进入页面加载列表信息
        getData: function () {
            common.openloading('.zui-table-view')
            $.getJSON("/actionDispatcher.do?reqUrl=New1JyjcHrjl&types=pageJyjcHrjl&json=" + JSON.stringify(this.param), function (json) {
                wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                wrapper.jsonList = json.d.list;
            });
            common.closeLoading()
        },
        //编辑修改根据num判断
        edit: function (num) {
            wap.title = '编辑检验检查互认记录'
            wap.open();
            //清空所有选项
            for (let i = 0; i < wap.hrList.length; i++) {
                Vue.set(wap.isCheckedHr, i, false);
            }
            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            if (wap.hrList.length > 0 && wap.popContent.jyjcHrjlXmmxList.length > 0) {
                for (let i = 0; i < wap.hrList.length; i++) {
                    for (let j = 0; j < wap.popContent.jyjcHrjlXmmxList.length; j++) {
                        if (wap.hrList[i].xmbm == wap.popContent.jyjcHrjlXmmxList[j].xmbm) {
                            Vue.set(wap.isCheckedHr, i, true);
                        }
                    }
                }
            }
            wap.$forceUpdate();
        },
        remove: function (num) {
            var jlidList = [];
            if (num == -1) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        let jlid = this.jsonList[i].jlid;
                        jlidList.push(jlid);
                    }
                }
            } else {
                //单个删除
                var obj = {
                    jlid: this.jsonList[num].jlid
                }
                jlidList.push(obj);
            }
            if (jlidList.length == 0) {
                malert("请选中您要删除的数据", 'top', 'defeadted');
                return false;
            }
            if (common.openConfirm("确认删除该条信息吗？", function () {
                var json = '{"json":' + JSON.stringify(jlidList) + '}';
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1JyjcHrjl&types=delJyjcHrjl&',
                    json).then(function (data) {
                    if (data.body.a == 0) {
                        malert("删除成功", 'top', 'success')
                        this.getData();
                    } else {
                        malert("删除失败", 'top', 'defeadted')
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }

        },
        fDate: function (value, types) {
            if (value == null) {
                return "";
            }
            var date = new Date(value);
            Y = date.getFullYear(),
                m = date.getMonth() + 1,
                d = date.getDate(),
                H = date.getHours(),
                i = date.getMinutes(),
                s = date.getSeconds();
            if (m < 10) {
                m = '0' + m;
            }
            if (d < 10) {
                d = '0' + d;
            }
            if (H < 10) {
                H = '0' + H;
            }
            if (i < 10) {
                i = '0' + i;
            }
            if (s < 10) {
                s = '0' + s;
            }
            var t = null;
            if (types == "date") {
                t = Y + '-' + m + '-' + d;
            } else if (types == "time") {
                t = H + ':' + i;
            } else if (types == "times") {
                t = H + ':' + i + ':' + s;
            } else if (types == "year") {
                t = Y;
            } else if (types == "month") {
                t = m;
            } else if (types == "day") {
                t = d;
            } else if (types == "shortY") {
                t = m + '-' + d + ' ' + H + ':' + i;
            } else if (types == "shortYe") {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i;
            } else if (types == "AllDate") {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            } else {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            return t;
        }
    },
});

var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: false,
        popContent: {},
        hrList: {},
        brxx: brxx,
        isKeyDown: null,
        title: '',
        isCheckedHr: [],
        isCheckAllHr: false,
        hrIndex: null,
        json: {},
        yydjList:{
            '三级甲等':'三级甲等',
            '三级乙等':'三级乙等',
            '二级甲等':'二级甲等',
            '二级乙等':'二级乙等',
        }
    },
    methods: {
        getData: function () {
            let hrParam = {};
            $.getJSON("/actionDispatcher.do?reqUrl=New1JyjcXmbz&types=queryJyjcXmbz&json=" + JSON.stringify(hrParam), function (json) {
                wap.hrList = json.d.list;
            });
        },
        //关闭
        closes: function () {
            $(".side-form").removeClass('side-form-bg')
            $(".side-form").addClass('ng-hide');

        },
        open: function () {
            this.getData();
            $(".side-form-bg").addClass('side-form-bg')
            $(".side-form").removeClass('ng-hide');
            wap.$forceUpdate();
        },
        showTime: function (el, code) {
            laydate.render({
                elem: '#' + el,
                type: 'date',
                show: true,//直接显示
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) { //回调方法
                    wap.popContent[code] = value;
                }
            });
        },
        //确定
        confirms: function () {
            if (this.popContent.yymc == null) {
                malert("请输入医院名称", 'top', 'defeadted')
                return;
            }
            if (this.popContent.yydj == null) {
                malert("请输入医院等级", 'top', 'defeadted')
                return;
            }
            if (this.popContent.jyjcsj == null) {
                malert("请输入检验检查时间", 'top', 'defeadted')
                return;
            }
            var selectList = [];
            for (let i = 0; i < wap.hrList.length; i++) {
                if (this.isCheckedHr[i]) {
                    selectList.push(wap.hrList[i])
                }
            }
            wap.json.popContent = wap.popContent;
            wap.json.popContent.brid = this.brxx.brid;
            wap.json.popContent.brmc = this.brxx.brxm;
            wap.json.popContent.brksbm = this.brxx.ryks;
            wap.json.popContent.brks = this.brxx.ryksmc;
            wap.json.selectList = selectList;
            this.$http.post('/actionDispatcher.do?reqUrl=New1JyjcHrjl&types=saveJyjcHrjl', JSON.stringify(wap.json))
                .then(function (data) {
                    if (data.body.a == 0) {
                        wrapper.getData();
                        wap.closes();
                        malert("数据更新成功", 'top', 'success');
                    } else {
                        malert("数据失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
        },
        reCheckBoxHr: function (val) {
            var that = this
            if (val[1] !== 'all') this.hrIndex = val[0];
            if (val[0] == 'some') {
                Vue.set(this.isCheckedHr, val[1], val[2]);
                if (that.notempty(this.isCheckedHr).length == this[val[3]].length) {
                    this.isChecked.every(function (el) {
                        if (el === true) {
                            return that.isCheckAllHr = true
                        } else {
                            return that.isCheckAllHr = false
                        }
                    })
                }
                console.log(this.isCheckedHr)
            } else if (val[0] == 'one') {
                this.isCheckAllHr = false;
                this.isCheckedHr = [];
                Vue.set(this.isCheckedHr, val[1], val[2]);
            } else if (val[0] == 'all') {
                this.isCheckAllHr = val[2];
                if (val[1] == null) val[1] = "hrList";
                if (this.isCheckAllHr) {
                    for (var i = 0; i < this[val[1]].length; i++) {
                        Vue.set(this.isCheckedHr, i, true);
                    }
                } else {
                    this.isCheckedHr = [];
                }
            }
        },
        checkSelectHr: function (val, event, type) {
            if (val[1] !== 'all') {
                this.hrIndex = val[0];
                if (this.isCheckedHr[this.hrIndex]) {
                    Vue.set(this.isCheckedHr, this.hrIndex, false);
                } else {
                    Vue.set(this.isCheckedHr, this.hrIndex, true);
                }
            }
        }
    }
});