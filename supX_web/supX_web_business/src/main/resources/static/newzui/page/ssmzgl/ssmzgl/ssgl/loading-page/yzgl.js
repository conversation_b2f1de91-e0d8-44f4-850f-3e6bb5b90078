//医嘱处理
var qjIndex = 0;
var yyffcz = false;
var pccz = false;
var sfzt = false;
var index2 = "";
var zdtj = false;//指定添加
var tjIndex = 0;//添加位置
var change = false;
//***************************************展示医嘱单start
var toolMenu_yzd = new Vue({
    el: '.toolMenu_yzd',
    data: {
        which: 0,
        pageList: [],
        pageH: 860
    },
    methods: {
        long: function (num) {
            this.which = num;
            cqyzd.which = num;
            lsyzd.isShow = false;
            cqyzd.isShow = true;
            cqyzd.getData();
        },
        short: function (num) {
            this.which = num;
            cqyzd.which = num;
            cqyzd.isShow = false;
            lsyzd.isShow = true;
            lsyzd.getData();
        },
    }
});
var cqyzd = new Vue({
    el: '.cqyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        list: [],
        jsonList: [],
        isShow: true,
        param: {},
        BrxxJson: [],
        isGoPrint: false,
        which: 0,
        pageList: [],
        pageH: 1150
    },
    methods: {
        doPrint: function (isGoOn) {
            var cqTr;
            cqPrint.list = [];
            lsPrint.list = [];
            if (this.which == 0) {
                cqTr = $(".cqyzd tr");
            } else {
                cqTr = $(".lsyzd tr");
            }
            var _height = 0;
            var a = 0, b = -1;
            for (var i = 1; i < cqTr.length - 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height >= cqyzd.pageH) {
                    b++;
                    var as = [];
                    for (var f = a; f < i; f++) {
                        if (this.which == 0) {
                            as.push(cqyzd.jsonList[f]);
                        } else {
                            as.push(lsyzd.jsonList[f]);
                        }
                    }
                    if (this.which == 0) cqPrint.list[b] = as;
                    else lsPrint.list[b] = as;
                    a = i;
                    _height = 0;
                    this.pageList.push(as.length);
                }
            }
            var pp = [];
            if (this.which == 0) {
                for (var p = a; p < cqyzd.jsonList.length; p++) {
                    pp.push(cqyzd.jsonList[p]);
                }
            } else {
                for (var ls = a; ls < lsyzd.jsonList.length; ls++) pp.push(lsyzd.jsonList[ls]);
            }
            for (var l = 0; l < 21; l++) {
                _height += 43;
                if (_height >= cqyzd.pageH) {
                    break;
                }
                pp.push({'psjg': '无'});
            }
            if (this.which == 0) {
                cqPrint.list[b + 1] = pp;
                cqPrint.isShow = true;
                lsPrint.isShow = false;
            } else {
                lsPrint.list[b + 1] = pp;
                lsPrint.isShow = true;
                cqPrint.isShow = false;
            }
            if (isGoOn) {
                cqPrint.isGoPrint = true;
                lsPrint.isGoPrint = true;
                setTimeout(function () {
                    cqyzd.hideTable(cqyzd.which);
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid transparent');
                }, 50);
                setTimeout(function () {
                    window.print();
                    cqPrint.isGoPrint = false;
                    lsPrint.isGoPrint = false;
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid #999');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid #999');
                }, 100);
            } else {
                setTimeout(function () {
                    window.print();
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                }, 100);
            }
        },
        hideTable: function (type) {
            var num = 0;
            if (type == 0) {
                for (var i = 0; i < cqPrint.pagePrint; i++) {
                    $('.cqPrint .popCenter').eq(i).hide();
                    num += this.pageList[i];
                }
                cqPrint.isChecked = cqPrint.isChecked - num;
            } else {
                for (var j = 0; j < lsPrint.pagePrint; j++) {
                    $('.lsPrint .popCenter').eq(j).hide();
                    num += this.pageList[j];
                }
                lsPrint.isChecked = lsPrint.isChecked - num;
            }
        },
        goPrint: function (index) {
            cqyzd.isChecked = index;
            cqPrint.isChecked = index;
            var cqTr = $(".cqyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > cqyzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            cqPrint.pagePrint = b;
        },
        getData: function () {
            cqyzd.BrxxJson = userNameBg.Brxx_List;
            cqPrint.BrxxJson = userNameBg.Brxx_List;
            if (userNameBg.Brxx_List.zyh == null || userNameBg.Brxx_List.zyh == '' || userNameBg.Brxx_List.zyh == undefined) {
                malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                return
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                zyh: userNameBg.Brxx_List.zyh,
                yzlx: '1'
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    cqyzd.jsonList = json.d.list;
                    for (var i = 0; i < cqyzd.jsonList.length; i++) {
                        cqyzd.jsonList[i]['xmmc'] = cqyzd.jsonList[i]['xmmc'].replace('null', '');
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc'].replace('null', '');
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc'].replace('null', '');

                    }
                } else {
                    malert("病人医嘱单信息查询失败！", 'top', 'defeadted');
                }
            });
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var ksrq = this.jsonList[index]['ksrq'];
            var prvVal = null, nextVal = null, prvKsrq = null, nextKsrq = null;
            if (index != this.jsonList.length - 1) {
                nextKsrq = this.jsonList[index + 1]['ksrq'];
                nextVal = this.jsonList[index + 1][name]
            }
            if (index != 0) {
                prvKsrq = this.jsonList[index - 1]['ksrq'];
                prvVal = this.jsonList[index - 1][name];
            } else {
                prvKsrq = this.jsonList[index]['ksrq'];
                prvVal = this.jsonList[index][name];
            }
            if (!val) {
                if (val == prvVal && val == nextVal) {
                    return '';
                } else {
                    return ''
                }
            }
            if (index != 0) {
                if (ksrq == prvKsrq && ksrq == nextKsrq && val == prvVal && val == nextVal) {
                    return '""';
                }
            }

            var reDate = new Date(val);
            if (type == 'ry') {
                return this.Appendzero((reDate.getMonth() + 1)) + '-' + this.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return this.Appendzero(reDate.getHours()) + ':' + this.Appendzero(reDate.getMinutes());
            } else if (type == 'name') {
                return this.jsonList[index][name]
            }
        },
        sameSE: function (index) {
            console.log(11);
            var fzh = this.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList.length - 1) {
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }

            }
            if (index == this.jsonList.length - 1) {
                return 'end'
            }
            return 'null'
        },
        isShowItem: function (index) {
            if (this.jsonList[index + 1] == null) {
                return true;
            }
            if (this.jsonList[index]['fzh'] == this.jsonList[index + 1]['fzh'] && this.jsonList[index]['fzh'] != 0) {
                if (this.jsonList[index]['yyffmc'] == this.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var cqPrint = new Vue({
    el: '.cqPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: 0,
        BrxxJson: cqyzd.BrxxJson,
        isGoPrint: false
    },
    methods: {
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                cqPrint.isGoPrint = false;
                $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var val = cqyzd.jsonList[index][name];
            var prvVal = cqyzd, nextVal = null;
            if (index != cqyzd.jsonList.length - 1 && index != 0) {
                prvVal = cqyzd.jsonList[index - 1][name];
                nextVal = cqyzd.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqPrint.Appendzero((reDate.getMonth() + 1)) + '-' + cqPrint.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqPrint.Appendzero(reDate.getHours()) + ':' + cqPrint.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var fzh = cqyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == cqyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != cqyzd.jsonList.length - 1) {
                var nextFzh = cqyzd.jsonList[index + 1]['fzh'];
                var prvFzh = cqyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }

            }
            if (index == cqyzd.jsonList.length - 1) {
                return 'end'
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var lsyzd = new Vue({
    el: '.lsyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        jsonList: [],
        isShow: false,
        param: {},
        BrxxJson: [],
        isGoPrint: false
    },
    methods: {
        goPrint: function (index) {
            lsyzd.isChecked = index;
            lsPrint.isChecked = index;
            var cqTr = $(".lsyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > cqyzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            lsPrint.pagePrint = b;
        },
        getData: function () {
            lsyzd.BrxxJson = userNameBg.Brxx_List;
            lsPrint.BrxxJson = userNameBg.Brxx_List;
            if (userNameBg.Brxx_List.zyh == null || userNameBg.Brxx_List.zyh == '' || userNameBg.Brxx_List.zyh == undefined) {
                malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                return
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                zyh: userNameBg.Brxx_List.zyh,
                yzlx: '0'
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    lsyzd.jsonList = json.d.list;
                    for (var i = 0; i < lsyzd.jsonList.length; i++) {
                        lsyzd.jsonList[i]['xmmc'] = lsyzd.jsonList[i]['xmmc'].replace('null', '');
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc'].replace('null', '');
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc'].replace('null', '');
                    }
                } else {
                    malert("查询临时医嘱失败！", 'top', 'defeadted');
                }
            });
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var ksrq = this.jsonList[index]['ksrq'];
            var prvVal, nextVal, prvKsrq, nextKsrq;
            if (index != this.jsonList.length - 1) {
                nextKsrq = this.jsonList[index + 1]['ksrq'];
                nextVal = this.jsonList[index + 1][name]
            }
            if (index != 0) {
                prvKsrq = this.jsonList[index - 1]['ksrq'];
                prvVal = this.jsonList[index - 1][name];
            } else {
                prvKsrq = this.jsonList[index]['ksrq'];
                prvVal = this.jsonList[index][name];
            }
            if (!val) {
                if (val == prvVal && val == nextVal) {
                    return '';
                } else {
                    return ''
                }
            }
            if (index != 0) {
                if (ksrq == prvKsrq && ksrq == nextKsrq && val == prvVal && val == nextVal) {
                    return '""';
                }
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            } else if (type == 'name') {
                return lsyzd.jsonList[index][name]
            } else if (type == 'dTime') {
                return this.fDate(reDate, 'shortY')
            }
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList.length - 1) {
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index) {
            if (this.jsonList[index + 1] == null) {
                return true;
            }
            if (this.jsonList[index]['fzh'] == this.jsonList[index + 1]['fzh']) {
                if (this.jsonList[index]['yyffmc'] == this.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var lsPrint = new Vue({
    el: '.lsPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: null,
        BrxxJson: lsyzd.BrxxJson,
        isGoPrint: false
    },
    methods: {
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                lsPrint.isGoPrint = false;
                $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = lsyzd.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            if (val == prvVal && index != 0) {
                return '"';
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) {
                return null;
            }
            var fzh = lsyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == lsyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != lsyzd.jsonList.length - 1) {
                var nextFzh = lsyzd.jsonList[index + 1]['fzh'];
                var prvFzh = lsyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            if (lsyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (lsyzd.jsonList[index]['fzh'] == lsyzd.jsonList[index + 1]['fzh']) {
                if (lsyzd.jsonList[index]['yyffmc'] == lsyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});
cqyzd.getData();

