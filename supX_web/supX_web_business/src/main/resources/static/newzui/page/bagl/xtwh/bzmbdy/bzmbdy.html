<html>
<head>
	<meta charset="UTF-8">
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <title>病案编码对应</title>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="bzmbdy.css" rel="stylesheet" type="text/css" />
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10" >
<div class="wrapper background-f" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary" @click="sx"><i class="icon-sx1  padd-r-5"></i>刷新</button>
            <button class="tong-btn btn-parmary-b" @click="save"><i class="icon-baocun padd-r-5"></i>保存</button>
        </div>
        <div class="tong-search" v-show="mxShow">
            <div class="zui-form">
                <div class="zui-inline ">
                    <label class="zui-form-label">费用类别</label>
                    <div class="zui-input-inline wh120 ">
                        <!--<select-input class="height36" @change-data="resultChange" :not_empty="false"-->
                                      <!--:child="fylbList" :index="'lbmc'" :index_val="'lbbm'"-->
                                      <!--:val="fylb" :search="true" :name="'fylb'">-->
                        <!--</select-input>-->
                        <select-input class="height36" @change-data="resultChangeBg" :not_empty="false"
                                      :child="fylbList" :index="'lbmc'" :index_val="'lbbm'" :val="popContent.lbbm"
                                      :name="'popContent.lbbm'" :search="true" :index_mc="'lbmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline ">
                    <label class="zui-form-label">病案类别</label>
                    <div class="zui-input-inline wh120">
                        <!--<select-input  class="height36"  @change-data="resultChange" :not_empty="false"-->
                                       <!--:child="balbList" :index="'zymc'" :index_val="'zybm'" :val="balb"-->
                                       <!--:name="'balb'" :search="true">-->
                        <!--</select-input>-->
                        <select-input class="height36" @change-data="resultChangeBg " :not_empty="false"
                                      :child="balbList" :index="'zymc'" :index_val="'zybm'" :val="popContent.zybm"
                                      :name="'popContent.zybm'" :search="true" :index_mc="'zymc'" >
                        </select-input>

                    </div>
                </div>
            </div>
        </div>
        <div class="tong-search" v-show="ypShow">
            <div class="zui-form">
                <div class="zui-inline ">
                    <label class="zui-form-label">药品种类</label>
                    <div class="zui-input-inline wh120 ">
                        <select-input class="height36" @change-data="resultChangeBg" :not_empty="false"
                                      :child="ypzlList" :index="'ypzlmc'" :index_val="'ypzlbm'" :val="popContent.ypzlbm"
                                      :name="'popContent.ypzlbm'" :search="true" :index_mc="'ypzlmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline ">
                    <label class="zui-form-label">病案类别</label>
                    <div class="zui-input-inline wh120">
                        <select-input class="height36" @change-data="resultChangeBg " :not_empty="false"
                                      :child="balbList" :index="'zymc'" :index_val="'zybm'" :val="popContent.zybm"
                                      :name="'popContent.zybm'" :search="true" :index_mc="'zymc'" >
                        </select-input>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="fyxm-table" v-cloak>
        <div class="fyxm-tab">
            <div><span :class="{'active':num==0}" @click="tabBg(0)">费用类别对应</span></div>
            <div><span :class="{'active':num==1}" @click="tabBg(1)">明细费用对应</span></div>
            <div><span :class="{'active':num==2}" @click="tabBg(2)">药品对应</span></div>
        </div>
        <div class="zui-table-view" style="width: 100%;">
        	<!--费用类别-->
            <div class="fyxm-size" v-if="num==0">
                <div class="zui-table-header">
                    <table class="font-14 zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>费用类别编码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>费用类别名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>CT</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>PETCT</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>双源CT</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>X片</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>B超</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>心电图</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>MRI</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>多普勒</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"  @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                            <td class="cell-m">
                                <input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>

                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.lbbm"></div></td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.lbmc">
                                </div>
                            </td>
                            <td>
                                <div class=" cell-s" style="margin: 0 auto">
                                    <select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                                  :child="istrue_tran" :index="item.ct" :val="item.ct"
                                                  :name="'jsonList.'+$index+'.ct'">
                                    </select-input>
                                </div>
                            </td>
                            <td><div class=" cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.petct" :val="item.petct"
                                              :name="'jsonList.'+$index+'.petct'">
                                </select-input>
                            </div>
                            </td>
                            <td><div class=" cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height" @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.syct" :val="item.syct"
                                              :name="'jsonList.'+$index+'.syct'">
                                </select-input>
                            </div>
                            </td>
                            <td><div class=" cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.xp" :val="item.xp"
                                              :name="'jsonList.'+$index+'.xp'">
                                </select-input>
                            </div>
                            </td>
                            <td><div class=" cell-s" style="margin: 0 auto">
                                <select-input  class="fyl-height" @change-data="resultChange" :not_empty="false"
                                               :child="istrue_tran" :index="item.bc" :val="item.bc"
                                               :name="'jsonList.'+$index+'.bc'">
                                </select-input>
                            </div>
                            </td>
                            <td><div class=" cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.csxdt" :val="item.csxdt"
                                              :name="'jsonList.'+$index+'.csxdt'">
                                </select-input>
                            </div></td>
                            <td><div class=" cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height" @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.mri" :val="item.mri"
                                              :name="'jsonList.'+$index+'.mri'">
                                </select-input>
                            </div></td>
                            <td><div class=" cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height" @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.dpl" :val="item.dpl"
                                              :name="'jsonList.'+$index+'.dpl'">
                                </select-input>
                            </div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--费用类别对应end-->
            <!--明细费用对应-->
            <div class="fyxm-size" v-if="num==1">
                <div class="zui-table-header">
                    <table class="font-14 zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox style="display: flex;justify-content: center;align-items: center;"  @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>费用项目编码</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>费用项目名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>费用类别</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>费用规格</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>费用单价</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>病案细类</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>是否抢救</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>院内会诊</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>院际会诊</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"  @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                            <td class="cell-m">
                                <input-checkbox style="display: flex;justify-content: center;align-items: center;"  @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.mxfybm"></div></td>
                            <td>
                                <div class="zui-table-cell cell-l title" v-text="item.mxfymc">
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="item.pydm">
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.fylbmc">
                                </div>

                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.fygg">
                                </div>
                            </td>
                            <td><div class="zui-table-cell cell-s " v-text="item.fydj">
                            </div>
                            </td>
                            <td><div class=" cell-l" style="margin: 0 auto">
                                <select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                              :child="balbList" :index="'zymc'" :index_val="'zybm'" :val="item.balb"
                                              :name="'jsonList.'+$index+'.balb'" :search="true">
                                </select-input>
                            </div>
                            </td>
                            <td><div class="cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.ifqj" :val="item.ifqj"
                                              :name="'jsonList.'+$index+'.ifqj'">
                                </select-input>
                            </div></td>
                            <td><div class="cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.ynhz" :val="item.ynhz"
                                              :name="'jsonList.'+$index+'.ynhz'">
                                </select-input>
                            </div></td>
                            <td><div class="cell-s" style="margin: 0 auto">
                                <select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.yjhz" :val="item.yjhz"
                                              :name="'jsonList.'+$index+'.yjhz'">
                                </select-input>
                            </div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <!--明细费用对应end-->
            <!--药品对应-->
            <div class="fyxm-size" v-if="num==2">
                <div class="zui-table-header">
                    <table class="zui-table font-14">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>药品编码</span></div></th>
                            <th><div class="zui-table-cell cell-xl"><span>药品名称</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>药品种类</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>药品规格</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>药房单价</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>病案类别</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody> 	
                        <tr v-for="(item, $index) in jsonList"  @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                            <td class="cell-m">
                                <input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                            <td>
                                <div class="zui-table-cell cell-xl text-over-2" v-text="item.ypmc">
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.pydm">
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.zlmc">
                                </div>

                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.ypgg">
                                </div>
                            </td>
                            <td><div class="zui-table-cell cell-s " v-text="item.kfdw">
                            </div>
                            <td><div class="zui-table-cell cell-s " v-text="item.yfdw">
                            </div>
                            </td>
                            <td><div class="zui-table-cell cell-s " v-text="item.yplj">
                            </div>
                            </td>
                            <td><div class=" cell-s" style="margin: 0 auto">
                                <select-input  class="fyl-height"  @change-data="resultChange" :not_empty="false"
                                               :child="balbList" :index="'zymc'" :index_val="'zybm'" :val="item.balb"
                                               :name="'jsonList.'+$index+'.balb'" :search="true">
                                </select-input>
                            </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <!--药品对应end-->
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>

</div>
</body>
<script type="text/javascript" src="bzmbdy.js"></script>
</html>
