<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>医生工作量</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10" style="overflow: auto;position: relative">
<div class="wrapper" style="height: auto; min-height: calc(100% - 90px); ">
    <div class="panel" id="kswh">
        <div class="col-x-12 rysx_bottom_list">
            <div class="panel-head border-bottom background toolMenu ybhsgl-height">
                <div class="zui-row">
                    <div class="col-x-8">
                        <div class="zui-input-inline zui-date wh150">
                            <i class="datenox fa-calendar"></i>
                            <input type="text" name="phone" class="zui-input todats" id="dbegin" placeholder="开始时间"/>
                        </div>
                        <div class="zui-input-inline zui-date wh150">
                            <i class="datenox fa-calendar"></i>
                            <input type="text" name="phone" class="zui-input todate" id="dEnd" placeholder="结束时间"/>
                        </div>
                        <button class="zui-btn btn-primary" @click="getData">查询</button>
                        <button class="zui-btn btn-primary">报表</button>
                    </div>
                    <div class="col-x-4" style="height: 32px;line-height: 32px;">
                    </div>
                </div>
            </div>
            <div class="zui-table-view hzList " id="utable1" style="border:none; ">
                <div class="zui-table-header" style="overflow-x: scroll;">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th z-fixed="left" z-field="xb" z-width="100px">
                                <div class="zui-table-cell"><span>下标</span></div>
                            </th>
                            <th z-fixed="left" z-field="ck" z-width="100px" z-style="text-align:center;">
                                <div class="zui-table-cell">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </div>
                            </th>
                            <th z-field="ysxm" z-width="100px">
                                <div class="zui-table-cell"><span>医生姓名</span></div>
                            </th>
                            <th z-field="ksmc" z-width="100px">
                                <div class="zui-table-cell"><span>科室名称</span></div>
                            </th>
                            <th z-field="fyje" z-width="100px">
                                <div class="zui-table-cell"><span>费用金额</span></div>
                            </th>
                            <th z-field="gzl" z-width="100px">
                                <div class="zui-table-cell"><span>工作量</span></div>
                            </th>
                        </tr>
                        <!--@click="checkOne($index)"-->

                        </thead>
                    </table>
                </div>
                <div class="zui-table-body">
                    <table class="zui-table">
                        <tbody  id="ice">
                        <tr v-for="(item, $index) in jsonList"
                            :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                            @dblclick="edit($index)">
                            <td>
                                <div class="zui-table-cell" v-text="$index+1"></div>
                            </td>

                            <td>
                                <div class="zui-table-cell">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.mzysxm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.ghksmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.fyje"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.gzl"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
            </div>
        </div>
        <div style="clear: both"></div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
</div>
<script type="text/javascript" src="ysgzl.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>