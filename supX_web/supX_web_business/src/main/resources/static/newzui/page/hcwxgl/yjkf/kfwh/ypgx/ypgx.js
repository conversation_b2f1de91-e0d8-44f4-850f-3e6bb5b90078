
var qjindex = '';
var zlxmbm = "";
(function () {
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            popContent:{},
            title:'',
            totle:'',
            json:{
            },
            num:0,
            param: {
                page: '',
                rows: 100,
                total: '',
                lx:'1',
            },
           gxfl_tran:{
               "1": "卫生材料",
               "2": "体外诊断试剂",
               "3":"设备综合库",
               "4":"卫消类材料"
            },
            search:''
        },
        watch:{
        	'search' : function(){
        		yjkmtableInfo.getData();
        	}
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增分类';
                wap.open();
                wap.popContent={};

            },
            sx:function () {
                yjkmtableInfo.getData();
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    yjkmtableInfo.getData();
                }

            },
            //组件选择下拉框之后的回调
            resultChange: function (val) {
                //先获取到操作的哪一个
                Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                Vue.set(this.param.lx, val[2][val[2].length - 1], val[0]);
                this.$forceUpdate();
                yjkmtableInfo.getData();
            },
        }
    });


    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
            isShow: false,
            popContent: {},
            fyContent: {},
            isKeyDown: null,
            title: '',
        },

        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                yjkmtableInfo.saveData();
            },

        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],
            dyFyList: {},
            title:''

        },
        methods: {
            //进入页面加载列表信息
            getData: function () {
                common.openloading('.zui-table-view')
            	Vue.set(this.param,'parm',wrapper.search);
                $.getJSON("/actionDispatcher.do?reqUrl=YkglKfwhHcgx&types=query&dg=" + JSON.stringify(this.param)+'&lx='+wrapper.param.lx , function (json) {
                    yjkmtableInfo.totlePage = Math.ceil(json.d.total / yjkmtableInfo.param.rows);
                    yjkmtableInfo.jsonList = json.d.list;
                });
                common.closeLoading()
            },

            //保存
            saveData: function() {
                            	if(!wap.popContent.tybz){
            		Vue.set(wap.popContent,'tybz','0');
            	}
                if (wap.popContent.gxmc == null) {
                    malert("请输入分类名称",'top','defeadeted')
                    return;
                }
                Vue.set(wap.popContent,'lx',wrapper.param.lx);
                // if (wap.popContent.kssbz == null) {
                //     malert("请输入抗生素标志",'top','defeadeted')
                //     return;
                // }
                // if (wap.popContent.mzyp == null) {
                //     malert("请输入麻醉药品",'top','defeadeted')
                //     return;
                // }
                $.getJSON("/actionDispatcher.do?reqUrl=YkglKfwhHcgx&types=save&json="
                    + JSON.stringify(wap.popContent), function (data) {
                    if (data.a == 0) {
                        yjkmtableInfo.getData();
                        wap.closes();
                        malert("数据保存成功",'top','success');
                    } else {
                        malert("上传数据失败",'top','defeadeted');
                    }
                })
            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑分类'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },
            remove: function() {
                var ypgxList = [];
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        var gxbm = {'gxbm': this.jsonList[i].gxbm};
                        ypgxList.push(JSON.stringify(gxbm));
                    }
                }
                if (ypgxList.length == 0) {
                    malert("请选中您要删除的数据",'top','defeadted');
                    return false;
                }
                if (!confirm("请确认是否删除")) {
                    return false;
                }
                $.getJSON("/actionDispatcher.do?reqUrl=YkglKfwhHcgx&types=delete&json=["
                    + ypgxList + "]", function (data) {
                    yjkmtableInfo.getData();
                    if (data.a == 0) {
                        malert("删除成功",'top','success')
                    } else {
                        malert(data.c,'top','defeadted');
                    }
                })
            }


        },


    });
    yjkmtableInfo.getData();
})()
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol) {
        $(".selectGroup").hide();
    }
});
$(window).resize(function () {
    changHeight();
})
function changHeight() {

    var height_b=$(window).height()-$(".zui-table-tool").outerHeight();
    $('.background-box').css({
        'height':height_b,
        'overflow':'auto'
    })
}

setTimeout(function () {
    changHeight();
},150)





