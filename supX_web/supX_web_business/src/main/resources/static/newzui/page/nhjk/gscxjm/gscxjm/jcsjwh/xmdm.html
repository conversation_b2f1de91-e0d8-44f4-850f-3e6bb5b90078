<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="xmdm.css" rel="stylesheet">
</head>
<body class="skin-default">
<div class="wrapper background-f" id="wrapper" v-cloak>
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-sx icon-font14 paddr-r5" @click="getData(page)">刷新</button>
            <button ref="ypbxxm" class="tong-btn btn-parmary-b  icon-font14 paddr-r5" @click="save">保存</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==0" @click="getZlxm">获取诊疗项目</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==1" @click="getYpXm">获取药品项目</button>
            <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="num==2" @click="getKsXm">获取科室项目</button>
        </div>
        <div class=" flex-container margin-l-10 padd-t-10">
            <div class="flex-container flex-align-c margin-l-10">
                <label class="whiteSpace  ft-14 margin-r-5">检索项目</label>
                <div class="zui-input-inline">
                    <input class="zui-input wh180" placeholder="请输入关键字" @keyDown="sschangeDown($event)" v-model="search" type="text" id="search" />
                </div>
            </div>
            <div class="flex-container flex-align-c margin-l-10">
                <label class="whiteSpace  ft-14 margin-r-5">对码</label>
                <select-input @change-data="commonResultChange" :not_empty="true" :child="dm_tran"
                              :index="dmStatus" :val="dmStatus" :search="true"
                              :name="'dmStatus'">
                </select-input>
            </div>
        </div>
    </div>
    <div class="zui-table-view over-auto hzList padd-r-10 padd-l-10">
        <tabs :num="num" @tab-active="tabBg" key="a" :tab-child="[{text:'诊疗保险项目'},{text:'药品保险项目'},{text:'科室'}]"></tabs>
        <div key="b" class="flex-container">
            <div class="padd-r-10">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-s"><span>保险类别编码</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>保险类别名称</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                    <table class="zui-table table-width50" >
                        <tbody>
                        <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index),dim(item)"
                            @mouseleave="hoverMouse()" v-for="(item, $index) in jsonLeftList"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                            <td><div class="zui-table-cell cell-s" v-text="item.bxlbbm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.bxlbmc"></div></td>
                        </tr>
                        </tbody>
                    </table>
                    <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                </div>
            </div>
            <div class="fyxm-size " key="a" v-if="num==0">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-s" ><span>类型</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>状态</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell  cell-l"><span>项目编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl"><span>项目名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>保险编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">保险项目</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">保内保外</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">报销比例</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">费用规格</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">费用单价</div>
                            </th>

                            <th>
                                <div class="zui-table-cell cell-s">统筹类别</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">项目代码</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">是否药品</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">保险类别</div>
                            </th>
                        </tr>
                        <!--@click="checkOne($index)"-->
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,$index)"
                            :tabindex="$index"
                            v-for="(item, $index) in jsonList">
                            <td>
                                <div class="zui-table-cell cell-s" v-text="xmlx_tran[item.fylx]"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="tybz_tran[item.tybz]"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l  " v-text="item.mxfybm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl  " v-text="item.mxfymc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.wydm"></div>
                            </td>

                            <td><div class="zui-table-cell cell-s">
                                <input class="zui-input position" :id="'mc_'+$index"  :disabled="isEdit == $index" :value="item.ypmc"
                                       @input="searching($index,false,'ypmc',$event.target.value)"
                                       @keyDown="changeDown($index,$event,'text')">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="pageSelect"
                                              @click-one="checkedOneOut" @click-two="selectOne">
                                </search-table>
                            </div></td>


                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.bnbw"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.bxbl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.fygg"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.fydj"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="tclb_tran[item.fytclb]"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.pydm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="ypfy_tran[item.ypfy]"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.bxlbmc"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                </div>

            </div>
            <div class="fyxm-size " key="b" v-if="num==1">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-s"><span>保险类别</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell  cell-l"><span>药品名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>保险编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>保险名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">保内保外</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">报销比例</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">药品规格</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">剂量单位</div>
                            </th>

                            <th>
                                <div class="zui-table-cell cell-s">发药单位</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">拼音简码</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">种类</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">剂型</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">类型</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">统筹</div>
                            </th>
                        </tr>
                        <!--@click="checkOne($index)"-->
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,$index)"
                            :tabindex="$index"
                            v-for="(item, $index) in jsonList">
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.bxlbmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ypbm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l  " v-text="item.ypmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s  " v-text="item.wydm"></div>
                            </td>

                            <td><div class="zui-table-cell cell-s">
                                <input class="zui-input position" :id="'mc_'+$index"  :disabled="isEdit == $index" :value="item.bxypmc"
                                       @input="searching($index,false,'bxypmc',$event.target.value)"
                                       @keyDown="changeDown($index,$event,'text')">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="pageSelect"
                                              @click-one="checkedOneOut" @click-two="selectOne">
                                </search-table>
                            </div></td>

                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.bnbw"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.bxbl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.ypgg"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.jldwmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.jldwmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.pydm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.ypzlmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.ypzlmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="item.jxmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text=""></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s " v-text="tclb_tran[item.fytclb]"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                </div>

            </div>
            <div class="fyxm-size " key="c" v-if="num==2">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-s"><span>科室</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>科室名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell  cell-l"><span>科室代码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>医保科室</span></div>
                            </th>
                        </tr>
                        <!--@click="checkOne($index)"-->
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,$index)"
                            :tabindex="$index"
                            v-for="(item, $index) in jsonList">
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yyksbm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yyksmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l  " v-text="item.nhksbm"></div>
                            </td>
                            <td><div class="zui-table-cell cell-s">
                                <input class="zui-input position" :id="'mc_'+$index"  :disabled="isEdit == $index" :value="item.nhksmc"
                                       @input="searching($index,false,'nhksmc',$event.target.value)"
                                       @keyDown="changeDown($index,$event,'text')">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="ksthem" :them_tran="them_tran" :page="pageSelect"
                                              @click-one="checkedOneOut" @click-two="selectOne">
                                </search-table>
                            </div></td>
                        </tr>
                        </tbody>
                    </table>
                    <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                </div>

            </div>
        </div>
            <div class="zui-table-tool">
                <div class="zui-table-page">
                    <button  class="page-btn jump-btn" type="button"
                             @click="goPage(page, null, null)">跳转</button>
                    <input type="number" min="1" value="1" v-model="page" class="page-input jump"/>
                    <span class="page-count">共 <i class="color-green">{{totlePage}}</i> 页,每页显示多少条</span>
                    <span class="page-limits">
                        <select lay-ignore="" v-model="param.rows" @change="getData()">
                            <option value="10">10 条</option>
                            <option value="20">20 条</option>
                            <option value="30">30 条</option>
                            <option value="40">40 条</option>
                            <option value="50">50 条</option>
                            <option value="60">60 条</option>
                            <option value="70">70 条</option>
                            <option value="80">80 条</option>
                            <option value="90">90 条</option>
                        </select>
                    <em class="dot-bottom"></em>
                    </span>
                    <div class="page-right">
                        <a href="javascript:;" class="page-prev" @click="goPage(1, null, null)"
                           :class="page<=1?'disabled':''">
                            <i class="page-more"></i>
                        </a>
                        <a href="javascript:;" class="page-prev"
                           :class="page<=1?'disabled':''"
                           @click="goPage(page, 'prev','getData')">
                            <i class="page-prev"></i>
                        </a>
                        <a :class="{'page-curr': param.page == 1}"
                           @click="goPage(1, null, null)">
                            <em>1</em>
                        </a>
                        <a class="page-spr" v-show="prevMore">···</a>
                        <a href="javascript:;" data-page=""
                           v-for="(item, $index) in totlePage"
                           v-text="item"
                           :class="{'page-curr': param.page == item}"
                           @click="goPage(item, null, null)"
                           v-show="showLittle(item)"></a>
                        <a class="page-spr" v-show="nextMore">···</a>
                        <a class=""
                           :class="{'page-curr': param.page == totlePage}"
                           @click="goPage(totlePage, null, null)"
                           v-text="totlePage"
                           v-show="totlePage > 1"></a>
                        <a href="javascript:;" class="page-next"
                           :class="param.page >= totlePage?'disabled':''"
                           @click="goPage(page, 'next','getData')">
                            <i class="page-next"></i>
                        </a>
                        <a href="javascript:;" class="page-next" @click="goPage(totlePage, null, null)"
                           :class="param.page >= totlePage?'disabled':''">
                            <i class="page-nextMore"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
<script type="text/javascript" src="xmdm.js"></script>
</body>
</html>