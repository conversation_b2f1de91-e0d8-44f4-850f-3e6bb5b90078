var panel = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popContent: {
            qdType: '0',
            time: '',
        },
    },
    created: function () {
    },
    methods: {
        //清单类型切换
        resultChange_type: function (val) {
            Vue.set(this[val[2][0]], val[2][1], val[0]);
            if (val[0] == '0') {
                this.qdType = 0;
                hzList.gethzData()
            } else {
                this.qdType = 1;
                hzList.getMxData()
            }
        },
    },
});

//汇总明细内容显示
var hzList = new Vue({
    el: '.hzList',
    //混合js字典庫
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        jsonMxList: [],
        jsonHzList: [],
        qdmx: false,
        param: {},
        type: null,
        ksrq: null,
        jsrq: null,
        brxxContent: {},
        fyqdContent: {},
        qdType: 0,              // 当前的清单类型
        fyhj: 0,
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var beginTime = new Date(userNameBg.Brxx_List.ryrq).getFullYear() + "-" + (new Date(userNameBg.Brxx_List.ryrq).getMonth() + 1) + "-" + new Date(userNameBg.Brxx_List.ryrq).getDate() + " " + new Date(userNameBg.Brxx_List.ryrq).getHours() + ":" + new Date(userNameBg.Brxx_List.ryrq).getMinutes() + ":" + new Date(userNameBg.Brxx_List.ryrq).getSeconds();
        var endTime = new Date().getFullYear() + "-" + (new Date().getMonth() + 1) + "-" + new Date().getDate() + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: beginTime + ' - ' + endTime,
            rigger: 'click',
            theme: '#1ab394',
            range: true,
            done: function (value, data) { //回调方法
                if (value != '') {
                    var A_rq = value.split(' - ');
                    hzList.ksrq = A_rq[0];
                    hzList.jsrq = A_rq[1];
                } else {
                    hzList.ksrq = '';
                    hzList.jsrq = '';
                }
                //获取一次列表
                hzList.gethzData();
            }
        });
    },
    watch: {},
    methods: {
        // 获取汇总清单
        gethzData: function () {
            /*var ksrp = panel.popContent.time.split(' - ');
            this.param.ksrq = ksrp[0];
            this.param.jsrq = ksrp[1];*/
            this.param.ksrq = hzList.ksrq;
            this.param.jsrq = hzList.jsrq;
            this.param.zyh = userNameBg.Brxx_List.zyh;
            $.getJSON("/actionDispatcher.do?reqUrl=HszCxtjFyqd&types=queryMx&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    var fyhj = 0.00;
                    hzList.jsonHzList = json.d.list;
                    hzList.qdmx = false;
                    for (var i = 0; i < hzList.jsonHzList.length; i++) {
                        var fylistbak = hzList.jsonHzList[i].fymx;
                        var length = 0;
                        for (var j = 0; j < fylistbak.length; j++) {
                            if (fylistbak[j].fysl == 0 || fylistbak[j].fysl == '0') {
                                hzList.jsonHzList[i].fymx.splice(j - length, 1);
                                length = length + 1;
                            }
                        }
                    }
                    for (var i = 0; i < hzList.jsonHzList.length; i++) {
                        fyhj += hzList.jsonHzList[i].fyze;
                    }
                    hzList.fyqdContent.fyhj = fyhj;
                }
            });
        },
        // 获取明细清单
        getMxData: function () {
            /*var ksrp = panel.popContent.time.split(' - ');
            this.param.ksrq = ksrp[0];
            this.param.jsrq = ksrp[1];*/
            this.param.ksrq = hzList.ksrq;
            this.param.jsrq = hzList.jsrq;
            this.param.zyh = userNameBg.Brxx_List.zyh;
            //请求后台查询
            $.getJSON("/actionDispatcher.do?reqUrl=HszCxtjFyqd&types=queryFyqdMxxm&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    var length = 0;
                    var fylistbak = [];
                    var fyhj = 0.00;
                    hzList.jsonMxList = json.d.list;
                    $(".ovscrll1").uiscroll({height: '100%', size: '3px', opacity: 1});
                    fylistbak = json.d.list;
                    hzList.qdmx = true;
                    for (var i = 0; i < fylistbak.length; i++) {
                        if (fylistbak[i].fysl == 0 || fylistbak[i].fysl == '0') {
                            hzList.jsonMxList.splice(i - length, 1);
                            length = length + 1;
                        }
                    }
                    for (var i = 0; i < hzList.jsonMxList.length; i++) {
                        fyhj += hzList.jsonMxList[i].ftje;
                    }
                    hzList.fyhj = fyhj;
                }
            });
        },
        print: function () {
            sessionStorage.setItem('fyqd', JSON.stringify([userNameBg.Brxx_List, hzList.ksrq, hzList.jsrq, panel.popContent.qdType]));
            var text = panel.popContent.qdType == 1 ? '费用清单' : '汇总清单';
            this.topNewPage(text, 'page/zyysz/zyysz/hzgl/hzzx/userPage/fyqd01.html')
        },
    }
});
//初始化页面查询汇总单
hzList.gethzData();