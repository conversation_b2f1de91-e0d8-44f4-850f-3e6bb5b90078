<link href="/page/ybjk/gzwxhy/ybyw/sjsc/sjsc.css" rel="stylesheet" type="text/css">
<div class="my_002">
    <div class="ksys-side">
        <ul class="tab-edit-list flex-start" style="height: 280px;overflow-y: scroll">
            <li>
                <i>编号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac001" disabled="disabled"/>
            </li>
            <li>
                <i>姓名</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac003" disabled="disabled"/>
            </li>
            <li>
                <i>性别</i>
                <input type="text" class="zui-input  background-h" v-model="brxb_tran[grxxJson.aac004]" disabled="disabled"/>
            </li>
            <li>
                <i>年龄</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.akc023" disabled="disabled"/>
            </li>
            <li>
                <i>出生日期</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac006" disabled="disabled"/>
            </li>
            <li>
                <i>单位名称</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aab004" disabled="disabled"/>
            </li>
            <li>
                <i>磁卡卡号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.akc020_out" disabled="disabled"/>
            </li>
            <li>
                <i>职工医疗<br/>待遇类别</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.akc021" disabled="disabled"/>
            </li>
            <li>
                <i>职工医疗<br/>待遇名称</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.akc021_cn" disabled="disabled"/>
            </li>
            <li>
                <i>公务员<br/>类&nbsp;&nbsp;别</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yac103" disabled="disabled"/>
            </li>
            <li>
                <i>公务员<br/>类别名称</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yac103_cn" disabled="disabled"/>
            </li>
            <li>
                <i>账户余额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ykc194" disabled="disabled"/>
            </li>
            <li>
                <i>个人账户<br/>当年余额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ykc194_dn" disabled="disabled"/>
            </li>
            <li>
                <i>个人账户<br/>往年余额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ykc194_wn" disabled="disabled"/>
            </li>
            <li>
                <i>账户当年<br/>上账金额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.akc081" disabled="disabled"/>
            </li>
            <li>
                <i>统筹累计</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yka140" disabled="disabled"/>
            </li>
            <li>
                <i>医保代码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yac122" disabled="disabled"/>
            </li>
            <li>
                <i>医保机构</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yab003" disabled="disabled"/>
            </li>
            <li>
                <i>慢特病<br/>编&nbsp;&nbsp;码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yka026" disabled="disabled"/>
            </li>
            <li>
                <i>慢特病<br/>名&nbsp;&nbsp;称</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yka026_cn" disabled="disabled"/>
            </li>
            <li>
                <i>门诊慢性<br/>已报销</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yka141" disabled="disabled"/>
            </li>
            <li>
                <i>险种</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aae140" disabled="disabled"/>
            </li>
            <li>
                <i>门诊诊断信&emsp;&emsp;息</i>
                <input class="zui-input" v-model="jbContent.jbmc" @input="searching(false,'jbmc')"
                       @keyDown="changeDown($event,'text')">
                <search-table :message="searchCon" :selected="selSearch"
                              :them="them" :them_tran="them_tran" :page="page"
                              @click-one="checkedOneOut" @click-two="selectOne" :not_empty="true">
                </search-table>
            </li>
            <li class="zflb">
                <i>支付类别</i>
                <select-input @change-data="resultChange" id="prm_aka130"
                              :child="gzybzflb_tran" :index="zdxxJson.prm_aka130" :val="zdxxJson.prm_aka130"
                              :search="true" :name="'zdxxJson.prm_aka130'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>其他说明</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aae013" disabled="disabled"/>
            </li>
            <li>
                <i>备注</i>
                <input class="zui-input" type="text" v-model="zdxxJson.bzsm"/>
            </li>
        </ul>
    </div>
    <div class="zui-row buttonbox">
        <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="load()">读卡</button>
        <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="enter()">引入</button>
    </div>
</div>
<script type="application/javascript" src="insurancePort/015myyhyb/015myyhyb.js"></script>