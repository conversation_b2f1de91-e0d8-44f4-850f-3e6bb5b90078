var qjindex = '';
var zlxmbm = "";
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        jsonList: [],
        csqxContent:{},
        totlePage: 0,
        ksbm:'',
        options: {
            "0": '门诊',
            "1": '住院',
        },
        param: {
            page: 1,
            rows: 10,
            sort: '',
            type: '0',
            order: 'asc'
        },
    },
    mounted: function () {
        this.getCsqx()
    },
    updated:function(){
        changeWin();
    },
    methods: {
        getCsqx: function () {
            // 先获取到科室编码
            var ksparm = {
                "ylbm": 'N050012002'
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                if (json.a == 0 && json.d) {
                    wrapper.ksbm = json.d[0].ksbm;
                    var parm = {
                        "ylbm": 'N050012002',
                        "ksbm": json.d[0].ksbm
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                        if (json.a == 0 && json.d) {
                            for (var i = 0; i < json.d.length; i++) {
                                var csjson = json.d[i];
                                switch (csjson.csqxbm) {
                                    case "N05001200241": // 统一支付请求地址
                                        wrapper.csqxContent.N05001200241 = csjson.csz;
                                        break;
                                }
                            }
                            wrapper.getData()
                        }
                    })
                }
            })
        },
        resultcxfsChange: function (val) {
            Vue.set(this.param, 'type', val[0]);
            this.goToPage(1)
        },
        getData: function () {
            $.post(this.csqxContent.N05001200241.trim(), JSON.stringify(this.param), function (json) {
                if (json.code == "0") {
                    wrapper.totlePage = Math.ceil(json.pageInfo.total / wrapper.param.rows);
                    wrapper.jsonList = json;
                    common.closeLoading()
                }
            });
        },
    }
});






