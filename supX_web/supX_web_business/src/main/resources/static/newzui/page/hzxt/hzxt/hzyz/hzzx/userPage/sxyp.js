//过滤
var panel=new Vue({
    el:'.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        isChecked:[],
        jsValue: '1'
    },
    methods:{
        //过滤下拉框
        resultChange_type:function (val) {
            Vue.set(panel,'jsValue',val[0]);
            hzList.getData();

        }
    },
});
//列表
var hzList=new Vue({
    el:'.hzList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        jsonList:[],
        PcData:[],
        YyffData: [],
        ifClick: true,

    },
    updated: function () {
        changeWin();
    },
    methods:{
        //获取频次编码
        getPcData: function () {
            var pc_dg = {page: 1, rows: 100, sort: "sor", order: "asc", parm: ""};
            $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmpc&types=query&dg=" + JSON.stringify(pc_dg), function (json) {
                if (json.a == 0) {
                    hzList.PcData = json.d.list;  //频次下拉窗口绑定数据
                } else {

                    malert('频次列表查询失败'+json.c,'top','defeadted')
                    return;
                }
            });
        },
        //获取西药用药方法
        getYyffData: function () {
            var json = {
                sfcy: 0
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yyff" +
                '&json=' + JSON.stringify(json),
                function (json) {
                    if (json.a == 0) {
                        hzList.YyffData = json.d.list;
                    } else {
                        malert('用药方法列表查询失败'+json.c,'top','defeadted')
                        return;
                    }
                });
        },

        //查询申请的抗生素
        getData: function(){
            this.param.rows=50000;
            this.param.spbz=panel.jsValue;
            this.param.zyh=userNameBg.Brxx_List.zyh;
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywKsssyba&types=query&parm=' + JSON.stringify(this.param), function (data) {
                if (data.a == 0) {
                    hzList.jsonList = [];  //先清空
                    if (data.d.list.length > 0) {
                        hzList.jsonList = data.d.list;
                        for(var i=0; i<hzList.jsonList.length;i++){
                            hzList.jsonList[i].readonly=true; //默认加载列表为只读
                            hzList.jsonList[i].sqsj = formatTime(hzList.jsonList[i].sqsj, "datetime");
                            changeWin();
                        }
                    }
                } else {
                    malert("获取项目列表失败！", data.c,'top','defeadted');
                }
            });
        },
        //点击选中药品弹框
        showDael:function () {
            brzcList.num=0;
            this.jsonList=[];
            //查询所有
            brzcList.getAllKss();
        },

        //取消申请
        delsq: function (index) {
            if(hzList.jsonList[index].kssid==undefined||hzList.jsonList[index].kssid==null){
                hzList.jsonList.splice(index, 1);
                return false;
            }
            var json={
                kssid: hzList.jsonList[index].kssid
            }
            hzList.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywKsssyba&types=zfsq', JSON.stringify(json)).then(function (data) {
                if (data.body.a == 0) {
                    malert("取消申请成功 ！",'top','success');
                    hzList.getData();
                } else {
                    malert("取消申请失败："+data.body.c,'top','defeadted');
                }
            });
        },

        //频次和用药方法改变
        Wf_YppfChange: function (val) {
            var index = val[2][1];
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            if (types == "yyff") {
                Vue.set(hzList.jsonList[index], 'yyff', val[0]);
                Vue.set(hzList.jsonList[index], 'yyffmc', val[4]);
            }else if(types=="pcbm"){
                Vue.set(hzList.jsonList[index], 'pcbm', val[0]);
                Vue.set(hzList.jsonList[index], 'pcmc', val[4]);
            }
            this.nextFocus(event);
        },

        //保存医嘱
        addsq: function () {
            // console.log("==========" + hzList.jsonList);
            // alert(123123123);
            // var sendmsg={
            //     msgtype:'0',
            //     ksbm:'0013',
            //     yljgbm:'000014',
            //     yqbm:'014',
            //     msg:'病人【1111】有抗生素药物待审核!',
            //     toaddress:'/',
            //     sbid:'2018060023_2018-10-10 23:06:06',
            //     ylbm:'N030042002',
            //     pagename:'抗生素'
            // };
            // console.log(sendmsg);
            // window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));

            if (userNameBg.Brxx_List.zyzt == '1'||userNameBg.Brxx_List.zyzt == '2') {
                malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                return false;
            }

            if (!this.ifClick) return; //如果为false表示已经点击了不能再点
            this.ifClick = false;

            if (this.jsonList.length <= 0) {
                this.ifClick = true;
                return;
            }
            //循环判断每条医嘱是否正常
            for (var i = 0; i < this.jsonList.length; i++) {
                if (this.jsonList[i].kssid) {
                    malert("此申请已保存，无法执行此操作！", 'top', 'defeadted');
                   return false;
                }
                //药品编码为空的直接删除
                if (this.jsonList[i].ypbm == undefined || this.jsonList[i].ypbm == null || this.jsonList[i].ypbm == "") {
                    this.jsonList.splice(i, 1);  //直接删除
                    continue;
                }
                if (this.jsonList[i].yysx == null || this.jsonList[i].yysx == undefined || this.jsonList[i].yysx == 0 || this.jsonList[i].yysx>1) {
                    malert("第【" + (i + 1) + "】行【" + this.jsonList[i].ypmc + "】的【用药时限】不能为0或大于1！", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }

                if (this.jsonList[i].yyff == null || this.jsonList[i].yyff == undefined || this.jsonList[i].yyff == "") {
                    malert("第【" + (i + 1) + "】行【" + this.jsonList[i].ypmc + "】的【用途】不能为空！", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }

                if (this.jsonList[i].pcbm == null || this.jsonList[i].pcbm == undefined || this.jsonList[i].pcbm == "") {
                    malert("第【" + (i + 1) + "】行【" + this.jsonList[i].ypmc + "】的【次数】不能为空！", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }
            }  //循环完
            if (this.jsonList.length <= 0) {
                malert("没有可保存的申请项目 ！", 'top', 'defeadted');
                this.ifClick = true;
                return;
            }
            var json = '{"list":' + JSON.stringify(this.jsonList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywKsssyba&types=insert', json).then(function (data) {
                if (data.body.a == '0') {
                    hzList.getData();  //查询医嘱
                    malert("抗生素申请保存成功", 'top', 'success');

                	var user_info = JSON.parse(this.getcookie("user_info"+userId));
                    var yqbmCookie = user_info.yqbm;
                	var yljgbmCookie = user_info.yljgbm;
                	var userCookie = user_info.userName;
                    var ksbmCookie=userNameBg.Brxx_List.ryks;

                    // TODO 调用websocket推送 医嘱审核消息 处理
                    var sendmsg={
                        msgtype:'0',
                        ksbm:ksbmCookie,
                        yljgbm:yljgbmCookie,
                        yqbm:yqbmCookie,
                        msg:'病人【'+ userNameBg.Brxx_List.brxm +'】有抗生素药物待审核!',
                        toaddress:'page/zyysz/zyysz/sxyqgl/sxyqgl.html',
                        sbid: userNameBg.Brxx_List.zyh + "_" + this.jsonList[0].sqsj,
                        ylbm:'N030032001',
                        pagename:'受限药品管理'

                    };

                    console.log(sendmsg);
                    window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                    
                    hzList.ifClick = true;
                } else {
                    malert("抗生素申请保存失败" + data.body.c, 'top', 'defeadted');
                    hzList.ifClick = true;
                }
            });

        },
    },
});

//右侧弹框
var brzcList=new Vue({
    el:'#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        num:1,
        hssList:[],
        isChecked:[],
        selSearch: 0,
        jsValue: '',
        ifClick: true,
    },
    methods:{
        //关闭
        closes:function () {
            this.num=1;
        },
        //提交
        confirms:function () {
            this.num=0;
            if (!this.ifClick) return
            this.ifClick = false
            this.getMessage();
            this.isChecked = [];
        },

        //处理药品赋值问题
        getMessage: function(){
            var nowtime = getTodayDateTime();
            //此次为添加药品信息
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    var popContent={};
                    popContent.zyh = userNameBg.Brxx_List.zyh;
                    popContent.sqks = userNameBg.Brxx_List.ryks;
                    popContent.sqksmc = userNameBg.Brxx_List.ryksmc;
                    popContent.ypbm = brzcList.hssList[i].ypbm;
                    popContent.ypmc = brzcList.hssList[i].ypmc;
                    popContent.kssdj = brzcList.hssList[i].kssjb;
                    popContent.sqsj = nowtime;
                    popContent.jldw = brzcList.hssList[i].jldw;
                    popContent.jldwmc = brzcList.hssList[i].jldwmc;
                    popContent.yysx=1;
                    hzList.jsonList.push(JSON.parse(JSON.stringify(popContent)));
                    this.ifClick = true;
                }
            }
        },

        //检索药品/明细费用
        Wf_YpChange: function () {
            //回车选择药品
            if (window.event.keyCode == 13) {
                this.getMessage();
                this.isChecked = [];
            }
            //方面键向下
            else if (window.event.keyCode == 40) {
                if ((this.hssList.length - 1) == this.selSearch) {
                    this.selSearch = 0;
                } else {
                    this.selSearch++;
                }
                this.isCheckAll = false;
                this.isChecked = [];
                this.isChecked[this.selSearch] = true;
            }
            //方面键向上
            else if (window.event.keyCode == 38) {
                if (this.selSearch == 0) {
                    this.selSearch = this.hssList.length - 1;
                } else {
                    this.selSearch--;
                }
                this.isCheckAll = false;
                this.isChecked = [];
                this.isChecked[this.selSearch] = true;
            }
            else {
                this.getAllKss(); //检索药品
                this.selSearch = -1;
            }
        },
        //查询所有抗生素药品
        getAllKss: function(){
            this.param.rows=50;
            this.param.parm=this.jsValue;
            var json = {
                kssjb: '1'
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc&dg=' + JSON.stringify(this.param) + '&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    brzcList.hssList = [];  //先清空
                    if (data.d.list.length > 0) {
                        brzcList.hssList = data.d.list;
                    }
                } else {
                    malert("获取项目列表失败！", data.c,'top','defeadted');
                }
            });
        },
    },
});

hzList.getData();
hzList.getYyffData();
hzList.getPcData();
