<link href="/page/ybjk/gzwxhy/ybyw/sjsc/sjsc.css" rel="stylesheet" type="text/css">
<div class="cd_014" id="cd_014" style="height: 90%;">
    <div class="ksys-side">
        <ul class="tab-edit-list flex-start">
            <li>
                <i>个人编号</i>
                <input type="text" class="zui-input  background-h" @keydown.13="searchFun()" v-model="grxxJson.aac001" />
            </li>
            <li>
                <i>姓&emsp;&emsp;名</i>
                <input type="text" class="zui-input  background-h" @keydown.13="searchFun()" v-model="grxxJson.aac003" />
            </li>
            <li>
                <i>性&emsp;&emsp;别</i>
                <select-input @change-data="resultChange" id="xb" disable
                              :child="brxb_tran" :index="grxxJson.aac004" :val="grxxJson.aac004"
                              :name="'grxxJson.aac004'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>年&emsp;&emsp;龄</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.akc023" disabled="disabled"/>
            </li>
            <li>
                <i>身&nbsp;&nbsp;份&nbsp;证号&emsp;&emsp;码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac002" disabled="disabled"/>
            </li>
            <li>
                <i>出生日期</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac006" disabled="disabled"/>
            </li>
            <li>
                <i>个人帐户<br>种&emsp;&emsp;类</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ykc303" disabled="disabled"/>
            </li>
            <li>
                <i>个人账户余&emsp;&emsp;额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ykc194" disabled="disabled"/>
            </li>
            <li class="zflb">
                <i style="color:red;font-weight: 700">支付类别</i>
                <select-input @change-data="resultChange" id="aka130"
                              :child="cd_aka130_tran" :index="grxxJson.aka130" :val="grxxJson.aka130"
                              :search="true" :name="'grxxJson.aka130'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>异地标志</i>
                <select-input @change-data="resultChange" id="ydbz" disable
                              :child="cdydbz_tran" :index="grxxJson.ydbz" :val="grxxJson.ydbz"
                              :name="'grxxJson.ydbz'" :not_empty="true">
                </select-input>
            </li>
        </ul>
    </div>
    <div class="zui-row buttonbox">
        <button class="tong-btn btn-parmary-f2a xmzb-db paddr-r5" @click="load()">读卡</button>
        <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="enter()">引入</button>
    </div>
</div>
<script type="application/javascript" src="insurancePort/014cdyhyb/014cdyhyb.js"></script>
