
<div id="abyhyb_007">
	<div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary" @click="load()" >读卡</button>
            <button class="tong-btn btn-parmary-b" @click="enter()">引入</button>
            
        </div>
    </div>
    <div>
        <ul class="tab-edit-list flex-start">
        	<li>
                <i>门诊诊断信&emsp;&emsp;息</i>
                <input class="zui-input" v-model="jbContent.jbmc" @input="searching(false,'jbmc', $event.target.value)"
                       @keyDown="changeDown($event,'text')">
                <search-table :message="searchCon" :selected="selSearch"
                              :them="them" :them_tran="them_tran" :page="page"
                              @click-two="selectOne" :not_empty="true">
                </search-table>
            </li>
            <li class="zflb">
                <i>支付类别</i>
                <select-input @change-data="resultChange"
                              :child="abzyb_zflb_tran" :val="zdxxJson.aka130"
                              :search="true" :name="'zdxxJson.aka130'"  :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>个人编号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac001" disabled="disabled"/>
            </li>
            <li>
                <i>参保身份</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac066" disabled="disabled"/>
            </li>
            <li>
                <i>参保分中心</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aab034" disabled="disabled"/>
            </li>
            <li>
                <i>姓名</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac003" disabled="disabled"/>
            </li>
            <li>
                <i>性别</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac004" disabled="disabled"/>
            </li>
            <li>
                <i>身份证号码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac002" disabled="disabled"/>
            </li>
            <li>
                <i>出生日期</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac006" disabled="disabled"/>
            </li>
            <li>
                <i>单位编码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aab001" disabled="disabled"/>
            </li>
            <li>
                <i>单位名称</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aab004" disabled="disabled"/>
            </li>
            <li>
                <i>个人缴费状态</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac031" disabled="disabled"/>
            </li>
            <li>
                <i>个人参保状&emsp;&emsp;态</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac008" disabled="disabled"/>
            </li>
            <li>
                <i>人员类别</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac084" disabled="disabled"/>
            </li>
            <li>
                <i>伤残级别</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yac014" disabled="disabled"/>
            </li>
            <li>
                <i>个人账户余额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aae240" disabled="disabled"/>
            </li>
            <li>
                <i>参保险种</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aae140" disabled="disabled"/>
            </li>
            <li>
                <i>居保人员类型</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yac201" disabled="disabled"/>
            </li>
            <li>
                <i>居保人员类别</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.yac203" disabled="disabled"/>
            </li>
            <li>
                <i>人员身份类别</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.rysflb" disabled="disabled"/>
            </li>
            <li>
                <i>备注</i>
                <input class="zui-input" type="text" v-model="zdxxJson.bzsm"/>
            </li>
        </ul>
    </div>
</div>
<script type="application/javascript" src="insurancePort/007abyhyb/007abyhyb.js"></script>