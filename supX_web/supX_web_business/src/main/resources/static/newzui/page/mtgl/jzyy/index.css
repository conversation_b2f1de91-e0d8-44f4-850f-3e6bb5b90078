.pop-80 {
    width: 100%
}
.zui-input{
    height: 28px;
}
.hzlistHeight{
    height: calc(100% - 29px);
}
.zuiTableBodyHzlist{
    height: 100px;
    overflow: auto;
}
.mtdjTable{
    overflow: auto;
    height: calc(100% - 393px);
}
.mtdjBody{
    height: 100%;
}
.待审核{
    color: #16ef16;
    font-weight: bold;
}
.已审核{
    color: #01abff;
    font-weight: bold;
}
.已变更{
    color: #ffa700;
    font-weight: bold;
}
.已回退{
    color: #ff0101;
    font-weight: bold;
}
.未制定方案{
    color: #ff3227;
    font-weight: bold;
    font-size: 18px;
}
.zui-table-view .zui-table-body tr td{
    padding: 0;
}
.fyxm-side-top{
    height: 26px;
    line-height: 26px;
}
.zui-table-view .zui-table-header .zui-table-cell,.zui-table-view .zui-table-header .zui-table-cell span{
    height: 28px;
    line-height: 28px;
}
.ksys-btn{
    height: 40px;
}
