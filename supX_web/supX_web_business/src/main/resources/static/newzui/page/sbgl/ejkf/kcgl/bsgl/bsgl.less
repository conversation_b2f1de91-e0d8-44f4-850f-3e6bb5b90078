
@import "../../../../css/baseColor";
.border-d{
  border-bottom: 1px dashed @color1a;
}
.icon-bj{
  margin: 0 !important;
}

.icon-bj:before{
  top:4px;
  left: 4px;
}
.icon-bj-t:before{
  top:-7px;
  left: 4px;
}

.tong-padded{
  padding: 20px 10px 10px;
}

.tong-search .zui-form .padd-l-40{
  padding-left: 40px !important;
}
.rkgl-kd{
  width: 100%;
  padding:5px 0 0;
  box-sizing: border-box;
  color: @color8a;
  font-size: @font14;
  span{
    padding-left: 22px;
  }
}
.tab-edit-list .inner{
  li{
    width: 100% !important;
    margin-bottom: 0 !important;
  }
}
.ksys-side{
  .zui-select-inline{
    margin-right: 0px !important;
  }
  .tab-edit-list li label .label-input{
    width: 148px;
  }
}

.rkgl-position{
  position: fixed;
  bottom:10px;
  display: flex;
  justify-content: flex-end;
  left:10px;
  right:10px;
  width: auto;
  z-index: 11;
  height:70px;
  background: @colorff;
  align-items: center;
  color: @color81;
  .rkgl-fl{
    width: auto;
    float: left;
    i{
      float: left;
      padding-left: 20px;
    }
  }
  span{
    display: block;
  }
  .rkgl-fr{
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
.mr-t108{
  margin-top: 108px !important;
}
.mr-t168{
  margin-top: 178px !important;
}
.tab-edit-list li label .label-input{
  text-indent: 10px;
}
.zui-form .zui-form-label{
  left: 5px;
}