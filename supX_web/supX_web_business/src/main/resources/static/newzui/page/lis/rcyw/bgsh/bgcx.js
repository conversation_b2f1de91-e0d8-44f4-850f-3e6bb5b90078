    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
            sqShow:false,
            isFold:false
        },
        mounted:function () {
          changeWin()
        },
        methods:{
            feiyong:function(){

            },
            //打印
            daying:function () {
                window.print();
            },
            guolu:function () {
                filter.isShow=true;
            },
            piliangdaying:function () {
                pop.isShowpopL=true;
                pop.dyShow=true;
                pop.flag=false;
                pop.isShow=true;
                pop.title='报告批量打印';

            },
            //变化趋势
            qushi:function () {
                wapse.isFold = true;
                wapse.sqShow=false;
                // change('.tab-a','.tab-box');
                // change('.tab-gba','.tab-bg-box');
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                this.chart();
            },
            chart:function () {
                var myChart = echarts.init(document.getElementById('main'));
                // 指定图表的配置项和数据
                var option = {
                    title: {


                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: '#ddd'
                            }
                        },
                        backgroundColor: 'rgba(255,255,255,1)',
                        padding: [5, 10],
                        textStyle: {
                            color: '#666',
                        },

                    },
                    // legend: {
                    //     // right: 20,
                    //     orient: 'vertical',
                    //     // data: ['结果']
                    // },
                    xAxis: {
                        type: 'category',
                        data: ['2014-04-06','2014-04-06','2014-04-06','2014-04-06','2014-04-06','2014-04-06'],
                        boundaryGap: false,
                        splitLine: {
                            interval: 'auto',
                            lineStyle: {
                                color: ['#1abc9c']
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#1abc9c'
                            }
                        },
                        axisLabel: {
                            // margin: 10,
                            textStyle: {
                                fontSize: 12,
                                color:'#757c83'
                            }

                        }
                    },
                    yAxis: {
                        type: 'value',
                        color:'#666',
                        splitLine: {
                            lineStyle: {
                                color: ['#e6eaee']
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#ddd'
                            }


                        },
                        axisLabel: {
                            margin: 10,
                            textStyle: {
                                fontSize: 12,
                                color:'#757c83'
                            }
                        }
                    },
                    series: [{
                        name: '结果(mmol/L)',
                        type: 'line',
//      smooth: true,
//      showSymbol: false,
//      symbol: 'circle',
//      symbolSize: 6,
                        data: ['0', '2', '5', '1', '4', '3', '9', '6', '7', '1', '0'],
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(199, 237, 250,0.5)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(199, 237, 250,0.2)'
                                }], false)
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#1abc9'
                            }
                        },
                        lineStyle: {
                            normal: {
                                width:2
                            }
                        }
                    }]
                };
                // 使用刚指定的配置项和数据显示图表。
                myChart.setOption(option);
            }

        }
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShow:false,
            flag:true,
            dyShow:true,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除

            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                // event.currentTarget.remove();
            }
        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            sqShow:false,
            title:'',
            centent:'',
            num:0,
            childNum:0,
            isFold: false,
        },
        methods:{
            childSild:function (i) {
                this.childNum=i
            },
            side:function(i){
                this.num=i
            },
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                //成功回调提示
                // malert('111','top','defeadted');
            },
            AddClose:function () {
                this.num=0;
                this.childNum=0;
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            // //确定
            confirms:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('222','top','success');
            }

        }
    });
    var ztable=new Vue({
        el:'.zui-table-view',
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShow:false,
            title:'',
            centent:'',
        },
        methods:{
            dblclick:function () {
                // 展示注册记录
                wapse.isFold = true;
                wapse.sqShow=true;
                // change('.tab-a','.tab-box');
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                this.echart();
            },
            echart:function () {
                var myChart = echarts.init(document.getElementById('message'));
                // 指定图表的配置项和数据
                var option = {
                    title: {
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: '#ddd'
                            }
                        },
                        backgroundColor: 'rgba(255,255,255,1)',
                        padding: [5, 10],
                        textStyle: {
                            color: '#666',
                        },

                    },
                    // legend: {
                    //     // right: 20,
                    //     orient: 'vertical',
                    //     // data: ['结果']
                    // },
                    xAxis: {
                        type: 'category',
                        data: ['2014-04-06','2014-04-06','2014-04-06','2014-04-06','2014-04-06','2014-04-06'],
                        boundaryGap: false,
                        splitLine: {
                            interval: 'auto',
                            lineStyle: {
                                color: ['#1abc9c']
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#1abc9c'
                            }
                        },
                        axisLabel: {
                            // margin: 10,
                            textStyle: {
                                fontSize: 12,
                                color:'#757c83'
                            }

                        }
                    },
                    yAxis: {
                        type: 'value',
                        color:'#666',
                        splitLine: {
                            lineStyle: {
                                color: ['#e6eaee']
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#ddd'
                            }


                        },
                        axisLabel: {
                            margin: 10,
                            textStyle: {
                                fontSize: 12,
                                color:'#757c83'
                            }
                        }
                    },
                    series: [{
                        name: '结果(mmol/L)',
                        type: 'line',
//      smooth: true,
//      showSymbol: false,
//      symbol: 'circle',
//      symbolSize: 6,
                        data: ['0', '2', '5', '1', '4', '3', '9', '6', '7', '1', '0'],
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(199, 237, 250,0.5)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(199, 237, 250,0.2)'
                                }], false)
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#1abc9'
                            }
                        },
                        lineStyle: {
                            normal: {
                                width:2
                            }
                        }
                    }]
                };
                // 使用刚指定的配置项和数据显示图表。
                myChart.setOption(option);
            }
        }
    });
    //表格图形转换tab-gba
    laydate.render({
        elem: '.todate',
        eventElem: '.zui-date i.datenox',
        trigger: 'click',
        theme: '#1ab394'
        ,done:function (value,data) {
        }
    });
    var filter=new  Vue({
        el:'.filter',
        data:{
            isShow:false
        },
        methods:{
            baocun:function () {
                this.isShow=false;
                malert('保存成功','top','success');
            },
            guanbi:function () {
                this.isShow=false;
                malert('取消保存','top','defeadted ');
            }
        },
    })

