<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检验检查互认</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
    <div class="wrapper" id="wrapper">
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="goToPage(1)">刷新</button>
                <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5 icon-font15" @click="remove(-1)">删除
                </button>
                <!--                <button class="tong-btn btn-parmary-b icon-width icon-dc ">导出</button>-->
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline">
                        <label class="zui-form-label">检索</label>
                        <div class="zui-input-inline margin-f-l25">
                            <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="param.yymc"
                                   id="jsvalue" @keydown.13="goToPage(1)"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view " z-height="full">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left"><span>医院名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>医院等级</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>检验检查编号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>检验检查时间</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body " @scroll="scrollTable($event)">
                <table class="zui-table table-width50" id="table1">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" @dblclick="edit($index)"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left" v-text="item.yymc"
                                 :data-title="item.yymc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yydj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jyjcbh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="fDate(item.jyjcsj,'datetime')"></div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s">
                        <span class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-bj" @click="edit($index)" data-title="编辑"></i>
                                </em>
                                <em class="width30">
                                    <i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i>
                                </em>
                        </span>
                            </div>
                        </td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>

            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table" id="table2">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                            <span class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-bj" @click="edit($index)" data-title="编辑"></i>
                                </em>
                                <em class="width30">
                                    <i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i>
                                </em>
                        </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>

        </div>

    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-700" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--诊疗类别-->
    <div class="ksys-side ">
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">病人信息</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="flex-container flex-align-c margin-top-5">
                    <span class="margin-r-20">病人姓名：{{brxx.brxm}}</span>
                    <span class="margin-r-20">性别：{{brxb_tran[brxx.brxb]}}</span>
                    <span class="margin-r-20">年龄：<span v-text="brxx.nl"></span><span
                            v-text="nldw_tran[brxx.nldw]"></span></span>
                    <span class="margin-r-20">科室：{{brxx.ryksmc}}</span>
                    <span class="margin-r-20">床位号：{{brxx.rycwbh}}</span>
                    <span class="margin-r-20">住院号：{{brxx.zyh}}</span>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">医院信息</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="wrapper">
                    <div class="flex-align-c">
                        <i>医院名称</i>
                        <input type="text" class="zui-input border-r4" v-model="popContent.yymc"
                               @keyup="nextFocus($event)" data-notEmpty="true"/>
                    </div>
                    <div class="flex-container" style="flex-wrap: wrap">
                        <div class="flex-container flex-align-c padd-r-10">
                            <i class="zui-form-label">医院等级</i>
                            <select-input class="wh122" @change-data="resultChange" :child="yydjList" :index="yydj"
                                   :index_val="'yydj'" :val="popContent.yydj" :name="'popContent.yydj'"
                                   @keydown="nextFocus($event)" data-notEmpty="true"/>
                        </div>
                        <div class="flex-container flex-align-c span1">
                            <i class="zui-form-label">检验检查时间</i>
                            <input @click="showTime('jyjcsj','jyjcsj')" id="jyjcsj" class="zui-input border-r4"
                                   :value="fDate(popContent.jyjcsj,'datetime')" @keydown="nextFocus($event)" readonly/>
                        </div>
                        <div class="flex-container flex-align-c span1">
                            <i class="zui-form-label">检验检查编号</i>
                            <input type="email" class="zui-input border-r4" v-model="popContent.jyjcbh"
                                   @keydown="nextFocus($event)"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">互认项目</div>
            </div>
            <div class="zui-table-header margin-top-10">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBoxHr" :list="'hrList'"
                                            :type="'all'" :val="isCheckAllHr">
                            </input-checkbox>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left"><span>项目名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>项目金额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s text-left"><span>检查分类</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s text-left"><span>检查部位</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="zui-table-body " @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in hrList" :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelectHr([$index,'some','hrList'],$event)">
                            <td class="cell-m">
                                <input-checkbox :key="item.xmmb" @result="reCheckBoxHr" :list="'hrList'"
                                                :type="'some'" :which="$index"
                                                :val="isCheckedHr[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.xmmc"
                                     :data-title="item.xmmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.xmje"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s text-left" v-text="item.yxyxjcfl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s text-left" v-text="item.yxyxjcbw"></div>
                            </td>
                            <p v-if="hrList.length==0" class="  noData  text-center zan-border">
                                暂无数据...</p>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
</div>
<script src="jyjchr.js"></script>

</body>

</html>
