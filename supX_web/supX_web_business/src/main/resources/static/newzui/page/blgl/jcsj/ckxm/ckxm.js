var keSeq='';
    $(".zui-table-view").uitable();
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            flag:false,
            ejShow:false,
            treeData:{},
            treeList: [],
            ksList:[],//科室
            yhList:[],
            hszList:[],
            LcList:[],
            rList:[],
            ywckList:[],
            centent:'',
            title:'',
            ksmc:null,
            yhmc:null,
            jsonList: [],
            popContent: {
                'tybz': '',
            },
            mbfw:{
                "0":"全院",
                "1":"科室",
                "2":"个人"
            },
            sffy:{
                "0":"否",
                "1":"是"
            },
            shzt:{
                "0":"未审核",
                "1":"审核"
            },
            bcjlbz:{
                "0":"否",
                "1":"是"
            },
            //0=带域名插入 1-不带域名插入
            bcjlcrfs:{
                "0":"带域名插入",
                "1":"不带域名插入"
            },
            //0-允许 1-不允许
            celldrag:{
                "0":"允许",
                "1":"不允许"
            },
            scbz:{
                "0":"否",
                "1":"是"
            },
            //ckkjfw	0-全院可见 1-科室可见 2-医生私有
            ckkjfw:{
                "0":"全院可见",
                "1":"科室可见" ,
                "2":"医生私有"
            }


        },
        watch:{
        },
        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //保存
            saveData: function() {
                wap.popContent.kbSeq=keSeq;
                var json = JSON.stringify(wap.popContent);
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhCkxm&types=save',json).then(function (data) {
                    if(data.body.a == 0){
                        yjkmtableInfo.getR();
                        wap.closes();
                        malert("保存成功","top","success");
                    } else {
                        malert("上传数据失败","top","defeadted");
                    }
                },function (error) {
                    console.log(error);
                });
            },

        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '#jyxm_icon',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            popContent: {},
            jsonList: [],//
            iShow:false,
            isShowpopL:false,
            totlePage:0,
            total:'',
            leftList:[],
            page:'',
            ksList:[],//科室
            hszList:[],
            kmbm:'',
            ejShow:false,
            ejMore:true,
            LcList:[],
            kmmc:'',
            rows:10,
            param: {
                page:1,
                rows:10,
                sort: '',
                order: 'asc',
                parm:'',
                seq:''
            },
            mbfw:{
                "0":"全院",
                "1":"科室",
                "2":"个人"
            },
            sffy:{
                "0":"否",
                "1":"是"
            },
            shzt:{
                "0":"未审核",
                "1":"审核"
            },
            bcjlbz:{
                "0":"否",
                "1":"是"
            },
            //0=带域名插入 1-不带域名插入
            bcjlcrfs:{
                "0":"带域名插入",
                "1":"不带域名插入"
            },
            //0-允许 1-不允许
            celldrag:{
                "0":"允许",
                "1":"不允许"
            }

        },
        methods: {
            searchRc:function () {
                yjkmtableInfo.getR();
            },
            Add:function () {
                wap.title='新增标准数据集';
                wap.open();
                wap.popContent={};
            },
            sx:function () {
                yjkmtableInfo.getR();
            },
            del:function () {
                yjkmtableInfo.remove();
            },
            //展开二级
            ejUp:function(){
                this.ejShow=true;
                this.ejMore=false;

            },
            ejDown:function () {
                this.ejShow=false;
                this.ejMore=true;
            },

            leftGata:function () {
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }
                $.getJSON("/actionDispatcher.do?reqUrl=EmrXtwhCk&types=queryTree",function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if(json.a==0){
                        yjkmtableInfo.leftList = json.d.list;
                        console.log(yjkmtableInfo.leftList);
                    }

                });

            },




            getR: function (kbSeq) {
                if ($("#jsvalues").val() != null && $("#jsvalues").val() != '') {
                    this.param.parm = $("#jsvalues").val();
                } else {
                    this.param.parm = '';
                }

                this.param.kbSeq=kbSeq;
                keSeq=kbSeq;
                $.getJSON("/actionDispatcher.do?reqUrl=EmrXtwhCkxm&types=query&parm="+JSON.stringify(this.param),function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if(json.a==0){
                        yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                        yjkmtableInfo.jsonList = json.d.list;
                    }

                });
            },



            //删除
            remove: function() {
                var list = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                        var ckxmid={};
                        ckxmid.ckxmid=this.jsonList[i].ckxmid
                        list.push(ckxmid);
                    }
                }
                if(list.length == 0){
                    malert("请选中您要删除的数据","top","defeadted");
                    return false;
                }
                if(!confirm("请确认是否删除")){
                    return false;
                }
                var json='{"list":'+JSON.stringify(list)+'}'
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhCkxm&types=delete',
                    json).then(function (data) {
                    if(data.body.a == 0){
                        malert("删除成功","top","success")
                        yjkmtableInfo.getData();
                    } else {
                        malert("删除失败","top","defeadted")
                    }
                }, function (error) {
                    console.log(error);
                });

            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑词库项目'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },


        },


    });




yjkmtableInfo.leftGata();
function yjClick(obj) {
    console.log($(obj).parent().next());
   $(obj).parent().next().toggle();
}
yjClick();
