<div id="sszd">
    <div class="flex-container flex-align-c padd-b-10" >
        <button class="tong-btn btn-parmary" @click="getData"><span class="fa fa-refresh"></span>读取</button>
        <button class="tong-btn btn-parmary" @click="downData"><span class="fa fa-plus"></span>下载编码</button>
    </div>
    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th><div class="zui-table-cell cell-s">疾病编码</div></th>
                    <th><div class="zui-table-cell cell-xxl text-left">名称</div></th>
                    <th><div class="zui-table-cell cell-s">拼音代码</div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one over-auto"   @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList" @click="checkOne($index)"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                    <td  class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td><div  class="zui-table-cell cell-s">{{item.ssbm}}</div></td>
                    <td><div  class="zui-table-cell cell-xxl text-left">{{item.ssmc}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.pydm}}</div></td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
<script type="application/javascript" src="sszd.js"></script>