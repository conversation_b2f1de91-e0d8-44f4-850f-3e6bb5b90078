(function(){
    //统筹类别
    var tableInfo = new Vue({
        el: '#ypdy',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
        	param: {
	            page: 1,
	            rows: 10,
	            sort: '',
	            order: 'asc'
	        },
            jsonList: [], //列表集合
            ypzlList: [], //药品种类下拉框
            ypzl: null, //药品种类
            balbList: [], //病案类别
            balb: null   //病案类别值
        },
        methods : {
        	//初始化页面加载列表
            getData: function () {
            	$.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpzd&types=query&dg="+JSON.stringify(this.param),function (json) {
                   if(json.d!=null){
                    	tableInfo.jsonList = json.d.list;
                    	tableInfo.totlePage = Math.ceil(json.d.total/tableInfo.param.rows);
                    	tableInfo.isCheckAll = false;
           				tableInfo.checkAll();//调用全选
                   }
        		});
            },

          	//下拉框药品种类加载
          	ypzlSelect: function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl&dg=",function (json) {
       			 	tableInfo.ypzlList = json.d.list;
                });
          	},
          	//下拉框病案类别加载
          	balbSelect: function(){
          		var json={
          			zylb: "08"
          		}
            	$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyzbm&dg="+"&json="+JSON.stringify(json),function (json) {
       			 	tableInfo.balbList = json.d.list;
                });
          	},

            //保存
          	saveData: function(){
          		var ypList = [];
          		for (var i = 0; i < this.isChecked.length; i++) {
                	if (this.isChecked[i] == true) {
                		ypList.push(this.jsonList[i]);
                	}
                }
          		var json = '{"list":'+JSON.stringify(ypList)+'}';
                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglkfwhYpzd&types=updateBetch&',
                		json).then(function (data) {
                            if(data.body.a == 0){
                                malert("保存成功");
                                tableInfo.getData();
                            } else {
                                malert("保存失败");
                            }
                        }, function (error) {
                            console.log(error);
                });
          	},
          	//设置框
          	remove: function(){
          		this.balb=null;
          		this.ypzl=null;
          	},

          	//设置按钮
          	szDate: function(){
          		var json = {
          			zlbm : this.ypzl,
          			balb : this.balb
          		};
                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglkfwhYpzd&types=updateByYpzl&',
                		JSON.stringify(json)).then(function (data) {
                            if(data.body.a == 0){
                                malert("设置成功");
                                tableInfo.getData();
                            } else {
                                malert("设置失败");
                            }
                        }, function (error) {
                            console.log(error);
                });
          	},
        }
    });

    //列表
    tableInfo.getData();
    tableInfo.ypzlSelect();
    tableInfo.balbSelect();


    //为table循环添加拖拉的div
    var drawWidthNumYp = $(".patientTableYp tr").eq(0).find("th").length;
    for(var i=0;i<drawWidthNumYp;i++){
        if(i>=2){
            $(".patientTableYp th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
        }
    }

})();
