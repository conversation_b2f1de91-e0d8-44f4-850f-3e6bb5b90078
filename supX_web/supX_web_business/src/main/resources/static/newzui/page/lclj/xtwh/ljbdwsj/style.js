$(".zui-table-view").uitable();
//表单查看数据
var bbView = new Vue({
    el: '#bbView',
    mixins: [tableBase, baseFunc],
    data: {
        bbViewIS: true,
        jsonList: [],
        isChecked:null,
        parms: {},
    },
    created: function () {
        this.query()
    },
    methods: {
        getContent:function (item) {
            this.parms.ryljbm = item.ryljbm
            $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysYz&types=queryLjbd&parm=" + JSON.stringify(this.parms), function (json) {
                if (json.a == '0') {
                    if (json.d != null) {
                        yzglTable.jsonList = json.d;
                    } else {
                        malert('暂无最新数据', 'top', 'defeadted');
                    }
                } else {
                    malert(json.c, 'top', 'defeadted');
                }
            });
        },
        checkOne: function (index) {
            this.isChecked = index;
        },
        query: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhys&types=queryall", function (json) {
                bbView.jsonList = json.d.list;
            });
        },
        getData: function () {
            this.query()
        },
        copySave: function () {
            if (this.isChecked != null) {
                pop.isShow = true;
                pop.json.repname = null;
                pop.json.repid = null;
                pop.autoFocus = true;
            }
        },
        addData: function () {
            viewPop.isview = true;
        },
        exportRy: function () {
            if (this.isChecked != null) {
                for (var i = 0; i < printTemplets.templet.length; i++) {
                    if (this.jsonList[this.isChecked]['repname'] == printTemplets.templet[i]['repname']) {
                        pop.json.canvas = printTemplets.templet[i].canvas;
                        pop.json.content = printTemplets.templet[i].content;
                        pop.json.repname = printTemplets.templet[i].repname;
                        pop.json.setType = 'printTemplets'
                        pop.save()
                    }

                }
            }else{
                malert('老板！你要选择一个在还原吗', 'top', 'defeadted');
            }
        },
        yl: function () {
        },
        print: function () {
        },
        edit: function (index, tpye) {
            right.$nextTick(function () {
                $("." + tpye).html('');
                $("." + tpye).append(bbView.jsonList[index]['content']);
            })
        },
    },
})

//新建自定义报表
var viewPop = new Vue({
    mixins: [baseFunc, tableBase, mformat],
    el: '.viewPop',
    data: {
        isview: false,
        sessData: {
            excel: '',
            yw: '',
            k: '',
            g: '',
            repid: null,
            content: null,
            repName: null,
            repname: null,
            types: '票据',
            canvas: null
        },
    },
    created: function () {

    },
    methods: {
        closes: function () {
            this.isview = false;
        },
        saveData: function () {
            if (this.sessData.repname == null) {
                malert('模板名称不能为空', 'top', 'defeadted');
            }else if(this.sessData.k==''){
                malert('模板宽度不能为空', 'top', 'defeadted');
            }else if(this.sessData.g==''){
                malert('模板高度不能为空', 'top', 'defeadted');
            } else {
                pop.json = this.sessData
                right.json = this.sessData
                this.isview = false
                bbView.bbViewIS = false;
                right.istagShow = true
                $('.sheji').show()
            }
        },
    },
})


var editContext = null;
var left = new Vue({
    el: '.left',
    mixins: [tableBase, baseFunc],
    data: {
        tem_x: null,
        isChecked: null,
        tem_y: null,
        item_x: null,
        item_y: null,
        drag_tem: null,
        index: '',
        tem_event: null,
        jsonList: [],
        objList: [
            {'name': '文本', 'src': 'images/<EMAIL>', type: 'txt', title: '字体属性设置'},
            {'name': '动态文本', 'src': 'images/<EMAIL>', type: 'Dynamictext', title: '动态文本属性设置'},
            {'name': '日期控件', 'src': 'images/<EMAIL>', type: 'date'},
            {'name': '表格', 'src': 'images/<EMAIL>', type: 'form', title: '表格属性设置'},
            {'name': '直线', 'src': 'images/<EMAIL>', type: 'straightline', title: '线条属性设置'},
            {'name': '竖线', 'src': 'images/<EMAIL>', type: 'verticalline', title: '线条属性设置'},
            {'name': '金额', 'src': 'images/<EMAIL>', type: 'money'},
        ],
        footerlist: [
            {'name': '左对齐', 'src': 'images/<EMAIL>'},
            {'name': '居中对齐', 'src': 'images/<EMAIL>'},
            {'name': '居右对齐', 'src': 'images/<EMAIL>'},
            {'name': '居上对齐', 'src': 'images/<EMAIL>'},
            {'name': '水平对齐', 'src': 'images/<EMAIL>'},
            {'name': '居下对齐', 'src': 'images/<EMAIL>'},
        ]
    },
    methods: {
        edit: function (index) {
            right.tagShow = false
            bbView.edit(index, 'showTem');
            right.json = bbView.jsonList[index]
        },
        checkOne: function (index) {
            this.isChecked = index;
        },
        getmb: function (num) {
            this.index = num
        },

        addEdit: function (isTrend, istype, title) {
            if (isTrend) {
                if ($(".trendDiv").length == 0) {
                    $(".trendAll").append("<div class='trendDiv'></div>");
                    $(".trendDiv").append('<div class=" txt mousedown " data-title="' + title + '" data-type="' + istype + '"><span id="' + right.guid() + '" class="t-values mouitem">编辑动态字段</span></div>')
                } else {
                    $(".trendDiv").append('<div class=" txt mousedown" data-title="' + title + '" data-type="' + istype + '"><span id="' + right.guid() + '" class="t-values mouitem">编辑动态字段</span></div>')
                }
            } else {
                $(".showTemList").append(' <div class="txt item mousedown" id="txt " data-title="' + title + '" data-type="' + istype + '"><span id="' + right.guid() + '"  class="values mouitem">双击编辑字段</span> </div>')
            }
        },
        line: function (isTrend, istype, title) {
            if (isTrend) {
                $(".showTemList").append('<div><span id="' + right.guid() + '" style="top: 25%;left: 50%;position: absolute" data-title="' + title + '" data-type="' + istype + '"  class=" clickline line mousedown"></span> </div>')
            } else {
                $(".showTemList").append('<div><span  data-title="' + title + '" id="' + right.guid() + '" data-type="' + istype + '" style="top: 25%;left: 50%;position: absolute" class=" clickline verticalline mousedown"></span> </div>')
            }
        },
        drag: function (type, istype, title) {
            brzclist.istype = istype
            switch (type) {
                case '文本':
                    this.addEdit(false, istype, title);
                    break;
                case '动态文本':
                    if ($('.showTem').text() != "") {
                        this.addEdit(true, istype, title)      // 为修改的模板增加动态字段
                        return false;
                    } else {
                        right.trend = true;
                        right.trendList.push({'name': '编辑动态字段', 'type': istype, title: title});
                    }
                    break;
                case '表格':
                    right.isTableShow = true;
                    break;
                case '直线':
                    this.line(true, istype, title)
                    break;
                case '竖线':
                    this.line(false, istype)
                    break;
                case '金额':
                    break;
            }
        }
    },
    created: function () {
    },
})
realTh = null;
var el;
var num;
var right = new Vue({
    el: '.right',
    mixins: [tableBase, baseFunc],
    data: {
        trendList: [],
        isTrend: null,
        trend: false,
        isTableShow: false,
        tagShow: true,
        trJson: [{}],
        tdJson: [{}],
        json: {},
        realTh: null,
        setData: '',
        el: null,
        num: null,
    },
    methods: {

        guid: function () {
            function S4() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
            }

            return (S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4());
        },
        deleteevent: function (event) {
            var ev = event || window.event; //获取event对象
            if (event.keyCode == 8) {
                this.isTableShow = false
            }
        },
        setbg: function (type, event) {
            brzclist.type = true;
            newtitle.title = '表格属性设置';
            brzclist.TextControl = event.target.id;
            editContext = null;
            brzclist.istypeindex = type;
            brzclist.event = event
            closeX(true)
        },
        editTrend: function (index, type, event) {
            this.isTrend = index;
            brzclist.type = true;
            editContext = ''
            brzclist.TextControl = event.target.id;
            brzclist.event = event
            brzclist.sjy = '';
            closeX(true)
            brzclist.istypeindex = type
            this.trendList[index]['name'] = '';
        },
        beginListen: function (event, type) {
            if (event.target.nodeName === 'P') {
                realTh = event.target.parentElement;
                console.log(realTh)
                el = event.clientX - event.target.parentElement.offsetWidth;
                num = event.clientY - event.target.parentElement.offsetHeight;
            } else {
                var panel = event.currentTarget;
                //光标按下时，光标和面板的相对距离
                event = event || window.event;
                var reX = event.clientX - panel.offsetLeft;
                var reY = event.clientY - panel.offsetTop;
                //当按下鼠标就应该加上在元素内部移动的事件了也就是move,因为是在整个页面移动所以是document
                //这里的event是面板移动时候的事件
                //获得left和top的最大值
                var MX = (document.documentElement.clientWidth || document.body.clientWidth) - panel.offsetWidth;
                var MY = (document.documentElement.clientHeight || document.body.clientHeight) - panel.offsetHeight;
                console.log(MX);
            }
            document.onmousemove = function (event) {
                if (realTh != null) {
                    console.log(event.clientX)
                    event = event || window.event;
                    realTh.style.width = (event.clientX - el) + 'px';
                    realTh.style.height = (event.clientY - num) + 'px';
                } else {
                    event = event || window.event;
                    var X = event.clientX - reX;
                    var Y = event.clientY - reY;
                    if (X < 0) {
                        X = 0;
                    } else if (X > MX) {
                        X = MX;
                    }
                    if (Y < 0) {
                        Y = 0;
                    } else if (Y > MY) {
                        Y = MY;
                    }
                    panel.style.position = 'absolute';
                    panel.style.cursor = 'pointer';
                    panel.style.left = X + 'px';
                    panel.style.top = Y + 'px';
                }
            }
            // 释放鼠标
            document.onmouseup = function () {
                document.onmousemove = null;
                realTh = null
            }
        },
        xz: function (event) {
            this.tagShow = true;
            this.isChecked = null;
            $(".showTem").html('');
        },
        Setting: function (type) {
            brzclist.istypeindex = type;
            newtitle.title = '设置模板属性'
            editContext=null
            $(".demoonstrate").bind("myEvent", function (event) {
                brzclist.event =event.target;
            });
            $(".demoonstrate").trigger("myEvent");
            closeX(true)
        },
        savaList: function () {
            pop.isShow = true;
            if (left.isChecked != null) {
                pop.json.repname = left.jsonList[left.isChecked]['repname'];
                pop.json.repid = left.jsonList[left.isChecked]['repid'];
            }
            pop.autoFocus = true;
            pop.save()
        },
        savaAs: function () {
            if (left.isChecked != null)
                bbView.copySave()
        },
        restore: function () {
            if (this.isChecked != null) {
                for (var i = 0; i < printTemplets.templet.length; i++) {
                    if (this.jsonList[left.isChecked]['repname'] == printTemplets.templet[i]['repname']) {
                        pop.json.canvas = printTemplets.templet[i].canvas;
                        pop.json.content = printTemplets.templet[i].content;
                        pop.json.repname = printTemplets.templet[i].repname;
                        pop.json.setType = 'printTemplets'
                        pop.save()
                    }

                }
            }else{
                malert('老板！你要选择一个在还原吗', 'top', 'defeadted');
            }
        },
        padlock: function () {
            viewPop.isview = false
            bbView.bbViewIS = true
            $('.sheji').hide()
        },
    },
})
var newtitle = new Vue({
    el: '.title',
    data: {
        title: ''
    },
})
var brzclist = new Vue({
    el: '#brzcList',
    data: {
        istypeindex: '',
        type: false,
        headline: '',
        textForm: '',
        istype: null,
        event: null,
        showRgb: false,
        index: 0,
        obj: {
            mbtxt: '',
            sjy: '',
            expression: '',
            widthbg: '',
            heightbg: '',
            Typeface: '',
            fontSize: '12',
            align: '居中',
            fontColor: '',
            TextControl: '',
            headline:'',
            Amountmoney: '',
        },

        num: '',
        setcolor: '',
        color: ['#354052', '#c2c5cb', '#00ad44', '#f89160', '#ff261a', '#f5a623', '#0086e9']
    },
    created: function () {

    },
    methods: {
        tab: function (num) {
            if (num == 1) {
                if (brzclist.istypeindex == 'Dynamictext') {
                    this.index = num;
                } else {
                    malert('此按钮为动态文本专用', 'top', 'defeadted');
                }
            } else {
                this.index = num;
            }
        },
        addRow: function () {
            right.trJson.push({});
        },
        addCol: function () {
            right.tdJson.push({});
        },
        deleteRow: function () {
            right.trJson.splice(right.trJson.length - 1, 1);
        },
        deleteCol: function () {
            right.tdJson.splice(right.tdJson.length - 1, 1);
        },
        changeData: function (event, type) {
            this[type] = event.srcElement.innerHTML
        },
        clickRgb: function () {
            this.showRgb = !this.showRgb
            if (this.showRgb == true) {
                $('.js-content').removeClass('hide')
                this.$nextTick(function () {
                    pickerHeight()
                })
            } else {
                $('.js-content').addClass('hide')
            }
        },
        getColor: function (color, event, index) {
            this.num = index
            this.setcolor = '0 0 6px 0' + color
            this.obj.fontColor = color
        }
    },
    watch: {
        'obj.headline': function (curVal, oldVal) {
            right.json.repname = curVal
        },
        'obj.mbtxt': function (curVal, oldVal) {
            var edit = editContext || brzclist.event.srcElement
            $(edit).text(curVal);
            $(edit).parent().find('.editingdiv input').val(curVal);
        },
        'obj.sjy': function (curVal, oldVal) {
            var edit = editContext || brzclist.event.srcElement
            if (curVal != '') {
                $(edit).text(curVal);
                $(edit).parent().find('.editingdiv input').val(curVal);
            }
        },
        'obj.fontSize': function (curVal, oldVal) {
            var edit = editContext || brzclist.event.srcElement
            if (brzclist.istypeindex == 'txt') {
                $(edit).css('fontSize', curVal + 'px');
                $(edit).parent().find('.editingdiv input').css('fontSize', curVal + 'px');
            } else {
                $(edit).css('fontSize', curVal + 'px');
                $(edit).parent().find('.editingdiv input').css('fontSize', curVal + 'px');
                // brzclist.event.srcElement.previousElementSibling.getElementsByClassName('editing')[0].style.fontSize=curVal+'px'
            }
        },
        'obj.Typeface':function(curVal, oldVal){
            if(brzclist.istypeindex=='headline'){
                var edit = editContext || brzclist.event
                edit.style.fontFamily = curVal
            }else{
                var edit = editContext || brzclist.event.srcElement
                edit.style.fontFamily = curVal
            }
        },
        'obj.textForm': function (curVal, oldVal) {
            var edit = editContext || brzclist.event.srcElement;
            $(edit).removeClass(oldVal);
            $(edit).addClass(curVal);
            $(edit).parent().find('.editingdiv input').removeClass(oldVal);
            $(edit).parent().find('.editingdiv input').addClass(curVal);
        },
        'obj.align': function (curVal, oldVal) {
            var edit = editContext || brzclist.event.srcElement
            edit.style.textAlign = curVal
            $(edit).parent().find('.editingdiv input').css('textAlign', curVal)
        },
        'obj.widthbg': function (curVal, oldVal) {
            if(brzclist.istypeindex=='headline'){
                var edit = editContext || brzclist.event
                edit.style.width = curVal + 'px'
            }else{
                var edit = editContext || brzclist.event.srcElement
                edit.style.width = curVal + 'px'
            }

        },
        'obj.heightbg': function (curVal, oldVal) {
            if(brzclist.istypeindex=='headline'){
                var edit = editContext || brzclist.event
                edit.style.height = curVal + 'px'
            }else{
                var edit = editContext || brzclist.event.srcElement
                edit.style.height = curVal + 'px'
            }
        },
        'obj.fontColor': function (curVal, oldVal) {
            var edit = editContext || brzclist.event.srcElement
            if (curVal.indexOf('#') == 0) {
                curVal = curVal.split("#").join("")
            }
            if (this.istypeindex == 'txt' || this.istypeindex == 'Dynamictext') {
                edit.style.color = '#' + curVal
                $(edit).parent().find('.editingdiv input').css('color', '#' + curVal)
            } else if (this.istypeindex == 'form') {
                edit.style.border = '1px solid #' + curVal + ''
            } else if (this.istypeindex == 'straightline' || this.istypeindex == 'verticalline') {
                edit.style.backgroundColor = '#' + curVal
            }
        },
        'obj.expression': function (curVal, oldVal) {
            var edit = editContext || brzclist.event.srcElement;
            $(edit).text(curVal);
            $(edit).parent().find('.editingdiv input').val(curVal);
        }
    },
})
var pop = new Vue({
    el: '#pop',
    data: {
        isShow: false,
        autoFocus: false,
        name: null,
        json: {
            excel: '',
            yw: '',
            k: '',
            g: '',
            repid: null,
            content: null,
            repname: null,
            types: '票据',
            canvas: null
        }
    },
    methods: {
        cancel: function () {
            this.isShow = false
        },
        save: function () {
            if (this.json.repname == null || this.json.repname == '') {
                malert('请填写模板名字', 'top', 'defeadted');
                return false;
            }
            this.isShow = false;
            if (this.json.setType == undefined) {
                if(right.tagShow==false) {
                    this.json.content = $(".showTemList").prop("outerHTML");
                }
                else{
                        this.json.content = $(".showTemList").prop("outerHTML");
                }
            }
            this.$http.post('/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=save',
                JSON.stringify(this.json)).then(function (data) {
                if (data.body.a == 0) {
                    right.istagShow = true
                    brzclist.obj = {
                        mbtxt: '',
                        sjy: '',
                        expression: '',
                        widthbg: '',
                        heightbg: '',
                        Typeface: '',
                        fontSize: '',
                        align: '',
                        fontColor: '',
                        TextControl: '',
                        Amountmoney: '',
                    }
                    malert('模板保存成功', 'top', 'success');
                    bbView.query();
                    pop.json = {
                        excel: '',
                        yw: '',
                        k: '',
                        g: '',
                        repid: null,
                        content: null,
                        repname: null,
                        types: '票据',
                        canvas: null
                    }
                } else {
                    malert('模板保存失败', 'top', 'defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        }
    }
});


//获取鼠标被按住就能被拖动的区域：标题
//给标题区域加上按下鼠标事件
$(document).on('mousedown', ".main .mousedown", function (event) {
    right.beginListen(event, 'moveItem');
})

$(document).on('dblclick', ".main .mouitem", function () {
    var type = $(this).parent().attr('data-type')
    var title = $(this).parent().attr('data-title');
    newtitle.title = title
    editContext = this;
    brzclist.index = 0;
    $(editContext).css('display', 'none');
    var text = '<div  class="editingdiv insertBefore"> <span class="icon-left-top icon"></span> <span class="icon-left-center icon"></span> <span class="icon-left-bottom icon"></span> <span class="icon-center-top icon"></span> <input type="text" placeholder="点击此处输入文本" autofocus="true" class="editing" id=' + testreg($(this).text()) + ' value=' + testreg($(this).text()) + '> <span class="icon-center-bottom icon"></span> <span class="icon-right-top icon"></span> <span class="icon-right-center icon"></span> <span class="icon-right-bottom icon"></span> <p class="bottom-right"><span class="sc"><img src="images/<EMAIL>"></span><span class="sx"><img src="images/<EMAIL>"></span></p></div>'
    $(this).parent().append(text);
    brzclist.mbtxt = testreg($(this).text())
    brzclist.type = true;
    closeX(true)
    brzclist.istypeindex = type;
});
$(document).on('input propertychange', '.editing', function () {
    if (brzclist.istypeindex == 'txt') {
        brzclist.mbtxt = $(this).val()
    } else {
        brzclist.sjy = $(this).val()
    }
})

var getColor = document.getElementsByClassName('getColor')[0]
valcahnge(getColor, function () {
    brzclist.obj.fontColor = getColor.value  //这里是监听插件修改的值
});

function valcahnge(dom, fn) {
    Object.defineProperty(getColorobj, 'val', {
        get: function () {
            return this._val;
        },
        set: function (val) {
            dom.value = val;
            fn();
            this._value = val;
        }
    });
}

$(document).on('dblclick', '.main .clickline', function () {
    editContext = this;
    var type = $(this).attr('data-type');
    var title = $(this).attr('data-title');
    brzclist.TextControl = $(this).attr('id');
    newtitle.title = title
    $(this).addClass('lineIcon');
    if (type == 'verticalline') {
        $(this).addClass('sline')
    }
    // var text = '<div  style="top: ' + $(this).css('top') + ';left: ' + $(this).css('left') + ';position: absolute" ' + '  class="item "><span style="height: ' + $(this).css('height') + ';width:' + $(this).css('width') + '" class="clickline verticalline mousedown"></span> </div>'
    // $(this).parent().append(text);
    // $(editContext).css('display', 'none');
    brzclist.type = true;
    brzclist.istypeindex = type;
    closeX(true)
})
$(document).click(function () {
    $('.clickline').removeClass('lineIcon')
    $('.clickline').removeClass('sline')
})
$(document).on('blur', '.main .editing', function () {
    $(editContext).css('display', 'block');
    $(editContext).text($(this).val());
    $(editContext).addClass($(this).val());
    $(this).parent().remove();
});
$(document).on('click', '.main .sc', function () {
    editContext.remove()
    $(this).parents('.editingdiv').remove()
})

function closeX(type) {
    if (type) {
        $('.sideForm').removeClass('ng-hide')
    } else {
        if (brzclist.istypeindex == 'straightline' || brzclist.istypeindex == 'verticalline') {
            $(editContext).removeClass('lineIcon')
            $(editContext).removeClass('sline')
        }
        $('.sideForm').addClass('ng-hide')
    }
}

function testreg(val) {
    var str = new RegExp('\\{', 'g');
    var str01 = new RegExp('\\}', 'g');
    var str02 = new RegExp('setData.', 'g')
    return val.replace(str, '').replace(str01, '').replace(str02, '')
}

