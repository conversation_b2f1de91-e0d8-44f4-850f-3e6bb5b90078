<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>病人医保信息</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="index.css">
</head>
<body>
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top flex-container flex-align-c">
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">上传明细</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">医保清单</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">结算单</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">读取清单</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">批量清除已上传费用</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">批量清除已上传费用及审核标志</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">打印基本医疗结算单</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">打印补充医疗结算单</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">打印结算单</button>
        </div>
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font14">病人基本信息</div>
        </div>
        <div class="tab-card-body padd-t-10">
            <div class="grid-box">
                <div class="">
                    <div class="flex-container flex-align-c flex-wrap-w ">
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">姓名</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm" @keydown.13="resultMzsfChange('brxm')"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">性别</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">治疗开始日期</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">治疗结束日期</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">挂号序号</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">就诊编号</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">费用总额</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">结算编号</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">个人账户</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">社保机构</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">补充基金</span>
                            <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm"  placeholder="姓名"/>
                        </div>
                    </div>
                    <div  class="zui-table-view wh25MAx">
                        <div class="zui-table-header " >
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th><div class="zui-table-cell cell-s"><span>疾病编码</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>疾病名称</span></div></th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div  data-no-change class="zui-table-body  zuiTableBodyHzlist flex-one flex-align-c" ref="body" v-if="isShow" @scroll="scrollTable($event)">
                            <table class="zui-table " v-if="rdbzContent.length!=0">
                                <tbody>
                                <tr v-for="(item, $index) in rdbzContent">
                                    <td><div class="zui-table-cell cell-s" v-text="item.yka026"></div></td>
                                    <td><div class="zui-table-cell cell-s " v-text="item.yka027"></div></td>
                                </tr>
                                </tbody>
                            </table>
                            <p v-if="rdbzContent.length==0" class="  noData text-center zan-border">暂无数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div  class="zui-table-view">
        <div class="zui-table-header " >
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>项目名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>项目编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>医保名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>限价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>比列</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>自费</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>挂钩</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>符合</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>审核</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>限制原因</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>上传</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>发生时间</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div  data-no-change class="zui-table-body  zuiTableBodyHzlist flex-one flex-align-c" ref="body" v-if="isShow" @scroll="scrollTable($event)">
            <table class="zui-table " v-if="rdbzContent.length!=0">
                <tbody>
                <tr v-for="(item, $index) in rdbzContent">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="item.yka026"></div></td>
                    <td><div class="zui-table-cell cell-s " v-text="item.yka027"></div></td>
                    <td><div class="zui-table-cell cell-s " >{{item.sum &&parseInt(item.sum)}}</div></td>
                    <td><div class="zui-table-cell cell-s" >{{item.ypfyje && parseInt(item.ypfyje)}}</div></td>
                    <td><div class="zui-table-cell cell-s">{{item.fypfyje && parseInt(item.fypfyje)}}</div></td>
                </tr>
                </tbody>
            </table>
            <p v-if="rdbzContent.length==0" class="  noData text-center zan-border">暂无数据...</p>
        </div>
    </div>
</div>
<script type="application/javascript" src="index.js"></script>
</body>
</html>
