/**
 * Created by mash on 2017/9/30.
 */
var wscjl = new Vue({
    el: '#wscjl',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        jsonList: [],
        totalContent: {},
        zyh: null,
        totlePage: null,
        ifClick: true,
        json: {},
        param: {
            page: 1,
            rows: 50,
            sort: '',
            parm: '',
            order: 'asc'
        },
    },
    mounted: function () {
        this.getData();
    },
    updated: function () {
        changeWin()
    },
    methods: {
        getData: function () {
            if (!this.all) {
				this.zyh = hyjl.brxxList.zyh;
            }
            var param={};
			this.inpid = hyjl.brxxList.inpid;
			this.json.doctor = hyjl.brxxList.zyysxm;
			this.json.bookNo = hyjl.brxxList.ybkh;
			this.json.familyNo = hyjl.brxxList.familyNo;//
			this.json.memberNo = hyjl.brxxList.memberid; //
			this.json.areaCode = hyjl.brxxList.xzqh;
			this.json.zdjb = hyjl.brxxList.zdjb;
            this.isChecked = [];
            param['zyh'] = this.zyh;
            param['ksbm'] = hyjl.brxxList.ryks;
            common.openloading('#wscjl');
             this.updatedAjax("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=wscjl&method=query&parm=" + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        common.closeLoading()
                        var res = eval('(' + json.d + ')');
                        wscjl.totlePage = Math.ceil(res.total / wscjl.param.rows);
                        wscjl.jsonList = res.list;
                        console.log(wscjl.jsonList);
                        var bs = 0;
                        var account = 0;
                        for (var i = 0; i < wscjl.jsonList.length; i++) {
                            bs++;
                            account += wscjl.jsonList[i].fyje;
                        }
                        wscjl.totalContent.bs = bs;
                        wscjl.totalContent.account = account;
                        wscjl.all = false;
                    } else {
                        common.closeLoading()
                        malert(json.c, "bottom", "defeadted");
                    }
                });

        },

        upload: function () {
            if (!this.ifClick) return; //如果为false表示已经点击了不能再点
            this.ifClick = false;
            $.ajaxSettings.async = false;
            if (!this.jsonList || this.jsonList.length <= 0) {
                this.ifClick = true;
                malert("无可上传的费用！", "top", "defeadted");
                return;
            }
                var bodylist = [];
                var footerList = [];
                var wdmxm = [];
                var ts = 0;
                for (var i = 0; i < this.jsonList.length; i++) {
                    if (this.jsonList[i].bxxmbm) {
                        ts++;
                        var detail = {
                            detailName: this.jsonList[i].bxxmmc,
                            detailCode: this.jsonList[i].bxxmbm=="qdnz15080024"?"ZF0000002":this.jsonList[i].bxxmbm,
                            hisDetailCode: this.jsonList[i].fyid,
                            detailHosCode: this.jsonList[i].yljgbm,
                            typeCode: this.jsonList[i].xmlb,
                            num: this.jsonList[i].fysl,
                            price: this.jsonList[i].fydj,
                            totalCost: this.jsonList[i].fyje,
                            date: this.fDate(this.jsonList[i].sfrq, 'date'),
                            unit: null,
                            standard: null,
                            formulations: null,
                        };
                        var bm = {
                            mxfyxmbm: this.jsonList[i].mxfyxmbm,
                        };
                        bodylist.push(detail);
                        footerList.push(bm);
                    } else if (this.isChecked[i] == true && !this.jsonList[i].bxxmbm) {
                        wdmxm.push(this.jsonList[i]);
                    }
                }
                if (wdmxm.length > 0) {
                    this.ifClick = false;
                    malert("其中" + wdmxm.length + "条数据未成功上传，请对码后再上传", "top", "defeadted");
                }
                if (bodylist.length <= 0) {
                    this.ifClick = false;
                    malert("无可上传的费用！", "top", "defeadted");
                    return;
                }
                var list = {
                    detail: bodylist
                }
                var footList = {
                    detail: footerList
                }
                var head = {
                    operCode: "S10",
                    billCode: left_tab1.billCode,
                    rsa: ""
                };

                var body = {
                    inpId: this.inpid,
                    doctor: this.json.doctor,
                    bookNo: this.json.bookNo,//"5204210201010612"
                    name: hyjl.brxxList.brxm,//"测试1",
                    familyNo: this.json.familyNo,//"59846890",
                    memberNo: this.json.memberNo,//"214144193",  //memberId
                    areaCode: this.json.areaCode,//"520421",//
                    isTransProvincial: "0",
                    list: list
                };



                var footer = {
                    bxlbbm: left_tab1.bxlbbm,
                    list: footList
                };
                var param = {
                    head: head,
                    body: body,
                    footer: footer
                };
                var str_param = JSON.stringify(param);
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=S&parm=" + str_param,
                    function (json) {
                        if (json.a == 0) {
                            wscjl.ifClick = false;
                            wscjl.getData();
                            wscjl.upload();
                            malert("费用上传成功！共上传" + ts + "条费用记录！", "bottom", "success");
                            if (wdmxm.length > 0) {
                                malert("其中" + wdmxm.length + "条数据未成功上传，请对码后再上传", "bottom", "defeadted");
                            }
                        } else {
                            malert(json.c, "bottom", "defeadted");
                        }
                    });

        },
    }
})
