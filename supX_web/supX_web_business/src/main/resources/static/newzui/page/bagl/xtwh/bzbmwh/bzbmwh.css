.InfoMenu {
  color: #7f8fa4;
}
.jbbm-js {
  color: #7f8fa4;
  font-size: 14px;
}
.baksbm-box {
  width: 100%;
  background: #fff;
  overflow: auto;
  min-height: 768px;
}
.baksbm-left {
  width: 27%;
  float: left;
}
.baksbm-right {
  width: 70%;
  float: right;
}
.height27 {
  height: 27px !important;
}
.baks-span input[type='text'] {
  height: 27px !important;
}
.tong-search {
  padding: 13px 0px 5px 20px;
}
.zui-table-view {
  max-width: 100%;
  color: #767d85;
  position: relative;
  overflow: hidden;
}
.fyxm-table {
  width: 100%;
  position: relative;
  padding: 0 10px;
  box-sizing: border-box;
}
.pop-width .ksys-side {
  padding: 10px 10px 10px 13px;
}
