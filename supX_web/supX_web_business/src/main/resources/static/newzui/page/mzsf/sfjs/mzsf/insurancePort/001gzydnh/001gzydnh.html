<div class="gz_001">
    <div class="tab-card-header">
        <div class="tab-card-header-title"></div>
    </div>
    <div class="tab-card-body">
        <div class="zui-form grid-box">
            <div class=" flex-container flex-align-c padd-l-10 padd-t-10">
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="padd-r-5 whiteSpace ft-14">参合号&emsp;</span>
                    <input type="text" class="zui-input wh120" id="chh" name="input1" autofocus @keyup.13="getPerson()" v-model="chh"/>
                </div>
                <div class="flex-container flex-align-c padd-l-10 padd-r-20">
                    <span class="padd-r-5 ft-14 whiteSpace">病人类型</span>
                    <select-input class="wh120" placeholder="请输入备注说明"  @change-data="resultChange" :not_empty="true"
                                  :child="mznh_tran" :index="popContent.rylx" :val="popContent.rylx"
                                  :search="true" :name="'popContent.rylx'" :not_empty="true">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-r-20">
                    <span class="padd-r-5 ft-14 whiteSpace">联系电话</span>
                    <div class=" position">
                        <input placeholder="请输入联系电话"  class="zui-input wh120"  v-model="popContent.lxdh" type="text" data-notEmpty="true"
                               @keydown="nextFocus($event)"/>
                    </div>
                </div>
                <div class="flex-container flex-align-c">
                    <span class="padd-r-5 ft-14 whiteSpace">门诊诊断</span>
                    <div class="zui-input-inline danwei-box">
                        <input class="zui-input wh120" v-model="jbContent.jbmc" @input="searching(false,'jbmc', $event.target.value)"
                               @keyDown="changeDown($event,'text')" id="jbmc">
                        <search-table :message="searchCon" :selected="selSearch"
                                      :them="them" :them_tran="them_tran" :page="page"
                                      @click-one="checkedOneOut" @click-two="selectOne" :not_empty="true">
                        </search-table>
                    </div>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-l-10">
                <div class="flex-container flex-align-c padd-r-20">
                    <span class="padd-r-5 ft-14 whiteSpace">诊断编码</span>
                    <div class=" position">
                        <input   class="zui-input wh120"  v-model="popContent.jbbm" type="text" data-notEmpty="true"
                                 @keydown="nextFocus($event)"/>
                    </div>
                </div>
                <div class="flex-container  flex-align-c  padd-r-20">
                    <span class="ft-14 padd-r-5 whiteSpace">重大疾病</br>申请序号</span>
                    <input placeholder="请输入疾病申请序号" class="zui-input wh120"  v-model="popContent.zdjbsqxh" type="text" @keydown="nextFocus($event)"/>
                </div>
                <div class="flex-container  flex-align-c  padd-r-20">
                    <span class="ft-14 padd-r-5 whiteSpace">重大疾病</span>
                    <input class="zui-input wh120" v-model="jbContentZdjb.zdjbmc" @input="searchingzdjb(false,'zdjbmc')"
                           @keyDown="changeDownzdjb($event,'text')">
                    <search-table5 :message="searchConZdjb" :selected="selSearch"
                                   :them="them" :them_tran="them_tran" :page="page"
                                   @click-one="checkedOneOut" @click-two="selectOnezdjb">
                    </search-table5>
                </div>
                <div class="  hqjtcy color-wtg" @click="getPerson()">获取家庭成员</div>
            </div>
            <div class="col-xxl-12">
                <div class="zui-table-view" id="yjjlTable00" style="overflow: hidden !important;">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th >
                                    <div class="zui-table-cell cell-s"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>个人编号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>人员类型</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>性别</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>出生日期</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>身份证号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>医疗证号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>账户余额</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>参保状态</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>住院总费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>住院保内费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>住院补偿费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>单病种总费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>单病种保内费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>单病种补偿费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>门诊总费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>门诊保内费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>门诊补偿费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>慢性病总费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>慢性病保内费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>慢性病补偿费用</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>人员属性</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"  data-no-change="undefined" style="max-height: 154px" @scroll="scrollTable">
                        <table class="zui-table table-width50">
                            <tbody>
                            <tr v-for="(item, $index) in InfoList"
                                @dblclick="edit($index)"
                                :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                                <!--@click="单击回调" @dblclick="edit($index)"双击回调-->
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.memberId"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="nhrysx_tran[item.memberPro]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.memberName"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="brxb_tran[item.memberSex]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.birthday"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.idcard"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.medicalNo"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.account"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.memberStatus"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.hosTotalCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.hosInsuranceCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.hosCompensateCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.sigTotalCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.sigInsuranceCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.sigCompensateCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.outpTotalCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.outpInsuranceCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.outpCompensateCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.chroTotalCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.chroInsuranceCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.chroCompensateCost"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.ideName"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-xxl-12 padd-l-10 margin-t-10">
                <div class="flex-container col-xxl-3">
                    <vue-checkbox class="padd-r-20" @result="reCheckOne" :new-text="'是否提高'" :val="'popContent.sftg'"   :new-value="popContent.sftg"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'是否转诊'" :val="'popContent.sfzz'"   :new-value="popContent.sfzz"></vue-checkbox>
                </div>
                <div class="flex-container flex-align-c padd-l-10 col-xxl-3">
                    <span class="ft-14 whiteSpace padd-r-5">输液次数</span>
                    <input type="number" class="zui-input" v-model="popContent.sfcs">
                </div>
                <div class="col-xxl-3 pad-top-bot padd-l-10">
                    <vue-checkbox class="padd-r-20" @result="reCheckOne" :new-text="'是否扣家庭账户'" :val="'popContent.sfkjtzh'"   :new-value="popContent.sfkjtzh"></vue-checkbox>
                </div>
            </div>
        </div>
        <div class="zui-form padd-l-10 grid-box">
            <div class="flex-container flex-align-c position col-xxl-3">
                <span class="ft-14 whiteSpace padd-r-5">病人姓名</span>
                <input placeholder="请输入姓名" class="zui-input " v-model="popContent.brxm" type="text" data-notEmpty="true" @keydown="nextFocus($event)" />
            </div>
            <div class=" flex-container flex-align-c col-xxl-3 padd-l-10">
                <span class="ft-14 whiteSpace padd-r-5">银行卡号</span>
                <input placeholder="请输入银行卡" class="zui-input"v-model="popContent.card" type="number" data-notEmpty="false" @keydown="nextFocus($event)"/>
            </div>

            <div class="flex-container flex-align-c col-xxl-3 padd-l-10">
                <span class="ft-14 whiteSpace padd-r-5">银行卡开</br>户人姓名</span>
                <input class="zui-input" type="text" v-model="popContent.khrxm" data-notEmpty="false" @keydown="nextFocus($event)"/>
            </div>

            <div class="flex-container flex-align-c padd-l-10 col-xxl-3">
                <span class="ft-14 whiteSpace padd-r-5">患者与开</br>户人关系</span>
                <input class="zui-input" type="text" v-model="popContent.khrgx" data-notEmpty="false"
                       @keydown="nextFocus($event)"/>
            </div>
            <div class="flex-container flex-align-c col-xxl-3">
                <span class="ft-14 whiteSpace padd-r-5 text-left">其他诊断</br>1</span>
                <input class="zui-input" v-model="jbContent2.qtzd1mc" @input="searching2(false,'qtzd1mc')"
                       @keyDown="changeDown2($event,'text')">
                <search-table2 :message="searchCon2" :selected="selSearch"
                               :them="them" :them_tran="them_tran" :page="page"
                               @click-one="checkedOneOut" @click-two="selectOne2">
                </search-table2>
            </div>
            <div class="flex-container flex-align-c padd-l-10 col-xxl-3">
                <span class="ft-14 whiteSpace padd-r-5 text-left">其他诊断</br>2</span>
                <input class="zui-input"  v-model="jbContent3.qtzd2mc" @input="searching3(false,'qtzd2mc')" @keyDown="changeDown3($event,'text')">
                <search-table3 :message="searchCon3" :selected="selSearch"
                               :them="them" :them_tran="them_tran" :page="page"
                               @click-one="checkedOneOut" @click-two="selectOne3">
                </search-table3>
            </div>
            <div class="flex-container flex-align-c padd-l-10 col-xxl-3">
                <span class="ft-14 whiteSpace padd-r-5 text-left">其他诊断</br>3</span>
                <input class="zui-input" v-model="jbContent4.qtzd3mc" @input="searching4(false,'qtzd3mc')"
                       @keyDown="changeDown4($event,'text')">
                <search-table4 :message="searchCon4" :selected="selSearch"
                               :them="them" :them_tran="them_tran" :page="page"
                               @click-one="checkedOneOut" @click-two="selectOne4">
                </search-table4>
            </div>
            <div class="flex-container flex-align-c padd-l-10 col-xxl-3">
                <span class="ft-14 whiteSpace padd-r-5 text-left">其他诊断</br>4</span>
                <input class="zui-input" v-model="jbContent5.qtzd4mc" @input="searching5(false,'qtzd4mc')"
                       @keyDown="changeDown5($event,'text')">
                <search-table5 :message="searchCon5" :selected="selSearch"
                               :them="them" :them_tran="them_tran" :page="page"
                               @click-one="checkedOneOut" @click-two="selectOne5">
                </search-table5>
            </div>
            <div class="flex-container flex-align-c col-xxl-3">
                <span class="ft-14 whiteSpace padd-r-5 text-left">其他诊断</br>5</span>
                <input class="zui-input" v-model="jbContent6.qtzd5mc" @input="searching6(false,'qtzd5mc')"
                       @keyDown="changeDown6($event,'text')">
                <search-table6 :message="searchCon6" :selected="selSearch"
                               :them="them" :them_tran="them_tran" :page="page"
                               @click-one="checkedOneOut" @click-two="selectOne6">
                </search-table6>
            </div>
            <div class="flex-container flex-align-c padd-l-10 position col-xxl-3">
                <span class="ft-14 whiteSpace padd-r-5">保险金额</br>&emsp;</span>
                <input class="zui-input" type="number" v-model="popContent.bxje" data-notEmpty="false" @keydown="nextFocus($event)"/>
                <span class="cm">元</span>
            </div>
            <div class="flex-container flex-align-c  padd-l-10 col-xxl-6">
                <span class="ft-14 whiteSpace padd-r-5">备&emsp;&emsp;注</span>
                <input class="zui-input" type="text" v-model="popContent.bz" data-notEmpty="false" @keydown="nextFocus($event)"/>
            </div>
        </div>
        <div class="flex-container flex-wrap-w grid-box padd-b-10 padd-l-10 zdjb">
            <p class="zdjb "><span>重大疾病</span></p>
            <div class="flex-container flex-wrap-w">
                <div class="flex-container   flex-align-c  padd-r-20">
                    <span class="ft-14 padd-r-5 whiteSpace">转诊类型</span>
                    <select-input class="wh150" placeholder="请先择转诊类型"  @change-data="resultChange" :not_empty="true"
                                  :child="gznhzzlx_tran" :index="popContent.zzlx" :val="popContent.zzlx"
                                  :search="true" :name="'popContent.zzlx'" :not_empty="true">
                    </select-input>
                </div>
                <div class="flex-container  padd-r-20 flex-align-c ">
                    <span class="ft-14 padd-r-5 whiteSpace">转诊号</span>
                    <input class="zui-input wh150" v-model="popContent.turnCode">
                </div>
                <div class="flex-container  padd-r-20 flex-align-c ">
                    <span class="ft-14 padd-r-5 whiteSpace">行政区划</span>
                    <input class="zui-input wh150" v-model="popContent.xzqh">
                </div>
                <div class="flex-container   flex-align-c  padd-r-20">
                    <span class="ft-14 padd-r-5 whiteSpace">治疗方式</span>
                    <select-input class="wh150" placeholder="请先择治疗方式"  @change-data="resultChange" :not_empty="true"
                                  :child="gznhzlfs_tran" :index="popContent.zlfs" :val="popContent.zlfs"
                                  :search="true" :name="'popContent.zlfs'" :not_empty="true">
                    </select-input>
                </div>
                <div class="flex-container   flex-align-c  padd-r-20">
                    <span class="ft-14 padd-r-5 whiteSpace">就诊类型</span>
                    <select-input class="wh150" placeholder="请先择就诊类型"  @change-data="resultChange" :not_empty="true"
                                  :child="mznhjzlx_tran" :index="popContent.jzlx" :val="popContent.jzlx"
                                  :search="true" :name="'popContent.jzlx'" :not_empty="true">
                    </select-input>
                </div>
                <div class="flex-container  flex-align-c  padd-r-20">
                    <span class="ft-14 padd-r-5 whiteSpace">补偿类型</span>
                    <select-input class="wh150"  placeholder="请先择补偿类型"  @change-data="resultChange" :not_empty="true"
                                  :child="mznhbclx_tran" :index="popContent.bclx" :val="popContent.bclx"
                                  :search="true" :name="'popContent.bclx'" :not_empty="true">
                    </select-input>
                </div>

                <!--
                <div class="zui-inline flex-container flex-align-c col-xxl-6">
                    <div class=" col-xxl-5">
                        <label class="zui-form-label">协议时间</label>
                        <div class="zui-input-inline danwei-box">
                            <input placeholder="" class="zui-input">
                        </div>
                    </div>
                    <span class="col-xxl-1 pad-top-bot text-center">至</span>
                    <div class=" col-xxl-5">
                        <div class="zui-input-inline danwei-box">
                            <input placeholder="" class="zui-input">
                        </div>
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">协议文件</label>
                    <div class="zui-input-inline danwei-box">
                        <label class="file" for="file">
                            <input id="file" type="file">
                        </label>
                        <span>还没有选择文件</span>
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">申请类型</label>
                     <div class="zui-input-inline danwei-box">
                        <input class="zui-input">
                    </div>
                </div>-->
                <div class=" flex-container  flex-align-c  padd-r-20" >
                    <vue-checkbox class="padd-r-20" @result="reCheckOne" :new-text="'重大疾病'" :val="'popContent.zdjb'"   :new-value="popContent.zdjb"></vue-checkbox>
                </div>
                <div class=" flex-container  flex-align-c  padd-r-20" >
                    <span class="ft-14 padd-r-5 whiteSpace">联&ensp;系&ensp;人</br>姓&emsp;&emsp;名</span>
                    <input class="zui-input wh150" v-model="popContent.lxrxm">
                </div>
                <div class="flex-container  padd-r-20 flex-align-c ">
                    <span class="ft-14 padd-r-5 whiteSpace">身份证号</span>
                    <input class="zui-input wh150" v-model="popContent.sfzjhm">
                </div>
            </div>
            <div class="col-xxl-12 ">
                <div class="flex-align-c flex-container col-xxl-6">
                    <span class="ft-14 padd-r-5 whiteSpace">机构意见</span>
                    <input class="zui-input ">
                </div>
            </div>
        </div>
        <div class="flex-container flex-align-c padd-l-10">
            <!--<div class="zui-btn btn-primary col-xxl-2" @click="saveBx()">确认保存</div>-->
            <div class="zui-btn btn-parmary-b zdjbtj zdjbtj-bg col-xxl-2">重大疾病协议添加</div>
            <div class="zui-btn btn-parmary-b zdjbtj-bg col-xxl-2">重大疾病治疗申请</div>
            <div class=" col-xxl- ckbxqd pad-top-bot padd-l-20">查看保险清单</div>
        </div>
    </div>
</div>
<script type="application/javascript" src="insurancePort/001gzydnh/001gzydnh.js"></script>
