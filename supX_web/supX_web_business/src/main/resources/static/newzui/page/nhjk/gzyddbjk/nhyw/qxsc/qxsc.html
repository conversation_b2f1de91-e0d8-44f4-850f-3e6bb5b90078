<html>
<head>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <title>取消上传</title>
    <link href="qxsc.css" rel="stylesheet" type="text/css"/>
</head>
<body class="skin-default background-f  padd-l-10 padd-r-10 padd-t-10 padd-b-10">
<div class="flex-container flex-align-c  padd-b-10" id="toolMenu">
    <button class="tong-btn btn-parmary" @click="getData"><span class="fa fa-refresh"></span>刷新</button>
    <button class="tong-btn btn-parmary" @click="remove"><span class="fa fa-trash-o"></span>取消上传</button>
</div>
<!-- 入院信息列表展示 -->
<div id="mztf">
    <!-- 检索 -->
    <div style="padding: 0 0 5px 3px;">
        <span>当前科室：</span>
        <select id="ksList" v-model="ksbm" @change="ksChange">
            <option v-for="option in ksList" :value="option.ksbm" v-text="option.ksmc"></option>
        </select>
    </div>
    <div style="height: auto" class="zui-table-view hzList hzList-border flex-container  padd-r-10">
        <div class="padd-r-10">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s">病人姓名</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">住院号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">保险类别</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body flex-one over-auto"  @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in brList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td>
                            <div class="zui-table-cell cell-s">{{item.brxm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.zyh}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.bxlbmc}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="skin-default">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-l">项目名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">类别</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">农保类别</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">单价</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">数量</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">金额</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">项目编码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">记录ID</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">收费日期</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">操作员</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">住院医生</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">医嘱序号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">退费ID</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">保内外</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body flex-one over-auto"  @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in fyList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td>
                            <div class="zui-table-cell cell-l">{{item.mxfyxmmc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.fylbmc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.mc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.fydj}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.fysl}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.fyje}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.mxfyxmbm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.fyid}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{fDate(item.sfrq,'yyyy-MM-dd')}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.czyxm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.zyysxm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.yzxh}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.tfid}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{nhtclb_tran[item.fw]}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

</body>
<script type="text/javascript" src="qxsc.js"></script>
</html>
