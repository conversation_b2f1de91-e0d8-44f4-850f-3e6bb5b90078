
var qjindex = '';
var zlxmbm = "";
(function () {
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            popContent:{},
            title:'',
            totle:'',
            num:0,
            param: {
                page: '',
                rows: 100,
                total: ''
            },
            search :''
        },
        watch:{
        	'search' : function(){
        		yjkmtableInfo.getData();
        	}
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增药品计量单位';
                wap.open();
                wap.popContent={};

            },
            sx:function () {
                yjkmtableInfo.getData();
            },
            del:function () {
              yjkmtableInfo.remove();
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    yjkmtableInfo.getData();
                }

            },
        }
    });


    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
            isShow: false,
            popContent: {},
            fyContent: {},
            isKeyDown: null,
            title: '',
        },

        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                yjkmtableInfo.saveData();
            },

        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],
            dyFyList: {},
            title:''

        },
        methods: {
            //进入页面加载列表信息
            getData: function () {
                common.openloading('.zui-table-view')
                Vue.set(this.param,'parm',wrapper.search);
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYptclb&types=query&dg=" + JSON.stringify(this.param), function (json) {
                    yjkmtableInfo.totlePage = Math.ceil(json.d.total / yjkmtableInfo.param.rows);
                    yjkmtableInfo.jsonList = json.d.list;
                    common.closeLoading()
                });
            },

            //保存
            saveData: function() {
            	if(!wap.popContent.tybz){
            		Vue.set(wap.popContent,'tybz','0');
            	}
                if (wap.popContent.tclb == null) {
                    alert("请输入统筹类别",'top','defeadted')
                    return;
                }
                if (wap.popContent.tclbmc == null) {
                    alert("请输入统筹类别名称",'top','defeadted')
                    return;
                }

                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYptclb&types=save&json="
                    + JSON.stringify(wap.popContent), function (data) {
                    if (data.a == 0) {
                        yjkmtableInfo.getData();
                        wap.closes();
                        malert("数据保存成功",'top','success')
                    } else {
                        malert("上传数据失败",'top','defeadted');
                    }
                })
            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑药品统筹类别'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },
            remove: function(index) {
                var yptclbList = [];
                var tclbbm={};
                if (index!=null) {

                    tclbbm = {'tclbbm': this.jsonList[index].tclbbm};
                    yptclbList.push(JSON.stringify(tclbbm));
                } else {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            tclbbm = {'tclbbm': this.jsonList[i].tclbbm};
                            yptclbList.push(JSON.stringify(tclbbm));
                        }
                    }
                }
                //
                // if (yptclbList.length == 0) {
                //     malert("请选中您要删除的数据",'top','defeadted');
                //     return false;
                // }

                if (common.openConfirm("确认删除该条信息吗？",function () {
                        //'{"list":'+ JSON.stringify(wrapper.saveList) +'}'
                        $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYptclb&types=delete&json=["
                            + yptclbList + "]", function (data) {
                            yjkmtableInfo.getData();
                            if (data.a == 0) {
                                malert("删除成功",'top','success')
                            } else {
                                malert(data.c,'top','defeadted');
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                    return false;
                }
                //
                // if (!confirm("请确认是否删除")) {
                //     return false;
                // }
                // $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYptclb&types=delete&json=["
                //     + yptclbList + "]", function (data) {
                //     yjkmtableInfo.getData();
                //     if (data.a == 0) {
                //         malert("删除成功",'top','success')
                //     } else {
                //         malert(data.c,'top','defeadted');
                //     }
                // })
            }


        },


    });
    yjkmtableInfo.getData();
})()

$(window).resize(function () {
    changHeight();
})
function changHeight() {


    var height_b=$(window).height()-$(".zui-table-tool").outerHeight();
    $('.background-box').css({
        'height':height_b,
        'overflow':'auto'
    })
}

setTimeout(function () {
    changHeight();
},150)





