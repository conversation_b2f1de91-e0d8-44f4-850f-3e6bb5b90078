(function () {
    var dateend = getTodayDateEnd();
    var datestart = getTodayDateBegin();
    var zd_enter = new Vue({
        el: '.skindefault',
        mixins: [dic_transform, baseFunc, tableBase, mformat,],
        data: {
            ifClick: true, //用于判断是否有点击交款按钮
            isCheck: null,
            isShow: true,
            mzjk: true, //门诊交款按钮
            popContent: {},//总体费用信息
            yljgmc: null,
            fphmPopContent: {},//发票号码信息
            brfbList: [],//病人费别
            brfbLis: 20,//病人费别
            fylbList: [],//费用项目
            isCheckedall:[],
            indexs:0,
            fylbLis: 20,//费用项目
            isChecked: [],
            MyisChecked: [],
            mxfyList: [],//门诊费用明细
            toolMenu: true, //工具栏,
            showList:0,
        },
        //页面渲染完成之后加载数据
        mounted: function () {
            $("#jkrq").val(dateend);
        },
        methods: {
            // 选中单条
            checkOne: function (event,index) {
                if(event.srcElement.checked==true){
                    this.isChecked[index] = false;
                }else{
                    this.isChecked[index] = true;
                }
            },

            // 选中全部
            checkAll: function (event,type,i) {

                if(type==='fylbList'){
                    if (event.srcElement.checked==true) {
                        this.isCheckedall[i]=true
                        for (var i = 0; i < this.fylbList.length; i++) {
                            Vue.set(this.isChecked,i,true)
                            // this.isChecked[i] = true;
                        }
                    } else {
                        this.isCheckedall[i]=false
                        this.isChecked = [];
                    }
                }else if(type==='mxfyList'){
                    if (event.srcElement.checked==true) {
                        this.isCheckedall[i]=true
                        for (var i = 0; i < this.brfbList.length; i++) {
                            Vue.set(this.MyisChecked,i,true)
                            // this.isChecked[i] = true;
                        }
                    } else {
                        this.isCheckedall[i]=false
                        this.MyisChecked = [];
                    }
                }
                else{
                    if (event.srcElement.checked==true) {
                        this.isCheckedall[i]=true
                        for (var i = 0; i < this.brfbList.length; i++) {
                            Vue.set(this.isChecked,i,true)
                            // this.isChecked[i] = true;
                        }
                    } else {
                        this.isCheckedall[i]=false
                        this.isChecked = [];
                    }
                }
            },
            showClick:function (one) {
                this.showList=one;
                this.indexs=one
                this.isChecked = [];
                this.isCheckedall=[]
            },
            //检索获取交款基本信息
            getData: function () {
                var jkrq = $("#jkrq").val();//获取到检索时间
                //检索内容为就清空所有数据
                if (jkrq == "") {
                    zd_enter.popContent = {};//总体费用信息
                    zd_enter.brfbList = [];//病人费别
                    zd_enter.fylbList = [];//费用项目
                    zd_enter.mxfyList = []; //门诊费用明细
                    return;
                }
                //请求后台查询基本信息
                var parm = {
                    jkrq: jkrq
                };
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=queryCwjkMsg&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        zd_enter.popContent = json.d;//总的费用情况
                        zd_enter.popContent.jkje = zd_enter.fDec(zd_enter.popContent.jkje, 2);
                        Vue.set(zd_enter.popContent, 'jkjedx', numToCn(zd_enter.popContent['jkje'])); //转换成大写金额
                        Vue.set(zd_enter.popContent, 'fyhj', zd_enter.fDec(zd_enter.popContent['fyhj'], 2));
                        zd_enter.brfbList = json.d.ryfbList; //病人费别
                        zd_enter.fylbList = json.d.fylbList; //费用项目
                    } else {
                        malert("费用查询失败"+json.c,'top','defeadted');
                    }
                });

                //请求后台查询发票信息
                zd_enter.getFphm(jkrq);
                //请求后台获取医疗机构名称
                zd_enter.getYljg();
            },

            //请求后台获取到发票号码段
            getFphm: function (jkrq) {
                var parm = {
                    cxsj: jkrq
                };
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjkFphm&types=queryFphm&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        zd_enter.fphmPopContent = json.d;//发票信息
                    } else {
                        malert("发票查询失败"+json.c,'top','defeadted');
                    }
                });
            },

            //请求后台查询医疗机构名称
            getYljg: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryYljg&types=queryOne&jgbm=" + jgbm, function (json) {
                    if (json.a == 0) {
                        zd_enter.yljgmc = json.d.jgmc;//发票信息
                    } else {
                        malert("医疗机构名称查询失败"+json.c,'top','defeadted');
                    }
                });
            },

            //编辑页面查询查询回车键
            searchListHc: function () {
                if (window.event.keyCode == 13) {
                    zd_enter.getData();
                }
            },

            //选中费用项目查询费用项目明细信息
            showDetail: function (index) {
                this.isCheck = index;
                //判断是否交款凭证号是否存在来决定查询哪个病人费用信息
                if (this.popContent['jkpzh'] == undefined || this.popContent['jkpzh'] == null || this.popContent['jkpzh'] == '') {
                    var parm = {
                        fylb: this.fylbList[index]['fylb'],
                        sfsj: $("#jkrq").val()
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=MzsfSfjsBrfy&types=queryByFylb'
                        + '&parm=' + JSON.stringify(parm),
                        function (data) {
                            if (data.a == 0) {
                                zd_enter.mxfyList = data.d.list;
                            } else {
                                malert("查询失败"+json.c,'top','defeadted');
                            }
                        });
                } else {
                    var parm = {
                        fylb: this.fylbList[index]['fylb'],
                        ryjkpzh: this.popContent['jkpzh']
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=MzsfSfjsBrfy&types=queryByFylbAndRyjkpzh'
                        + '&parm=' + JSON.stringify(parm),
                        function (data) {
                            if (data.a == 0) {
                                zd_enter.mxfyList = data.d.list;
                            } else {
                                malert("查询失败"+json.c,'top','defeadted');
                            }
                        });
                }
            },
            //门诊交款
            saveData: function () {
                if (!zd_enter.ifClick) return; //如果点击过则直接返回
                zd_enter.ifClick = false;
                if (zd_enter.popContent.jkje == undefined || zd_enter.popContent.jkje == null || zd_enter.popContent.jkje <= 0) {
                    malert("没有可交款的款项",'top','defeadted');
                    zd_enter.ifClick = true;
                    return;
                }
                var json = {
                    jkrq: $("#jkrq").val()
                };
                this.$http.post('/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=save&',
                    JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        malert("交款成功",'top','success');
                        zd_enter.popContent = {};//总体费用信息
                        zd_enter.brfbList = [];//病人费别
                        zd_enter.fylbList = [];//费用项目
                        zd_enter.mxfyList = []; //门诊费用明细
                        zd_enter.fphmPopContent = {}; //发票信息
                        zd_enter.ifClick = true;
                    } else {
                        malert("交款失败"+data.body.c,'top','defeadted');
                        zd_enter.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
            },
            print: function () {
                window.print();
            }
        }

});
    zd_enter.getData();//初始化页面加载交款信息
    laydate.render({
        elem: '.zui-date .todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,done:function (value,data) {
            search.jsrq = value
        }
    });
    laydate.render({
        elem: '.zui-date .todatD'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,done:function (value,data) {
            search.jsrq = value
        }
    });
})()