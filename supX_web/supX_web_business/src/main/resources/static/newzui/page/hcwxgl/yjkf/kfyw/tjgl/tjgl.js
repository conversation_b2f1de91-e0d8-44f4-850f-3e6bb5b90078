var qxksbm = '0952';
var rksh = new Vue({
    el: '.zui-table-view',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data: {
        //打印数据
        printData: {},
        isShowkd: true,
        isShow: false,
        rkdList: [], //入库单集合
        json: {},
        csParm: {},
        tjdList: [],
        zdrq: getTodayDateTime(), //设置制单时间
        tkdDetail: [],
        rkd: {}, //入库单对象
        tjdContent: {}, //退货单对象
        TjShow: true,
        ShShow: false,
        mxShShow: true,
        zfShow: true,
        thdList: [], //退货单集合
        dg: {
            page: 1,
            rows: 20,
            sort: "",
            order: "asc",
            parm: ""
        },
        isCheck: null,
        thdDetail: [], //退货单明细集合
        dateBegin: getTodayDateBegin(),
        dateEnd: getTodayDateEnd(),
        tjdDetail: [],
        time: {
            test: 'hello!'
        },
        zhuangtai: {
            "0": "未审核",
            "1": "已审核",
            "2": "已作废",
            "3": "未通过",
        },


        tjd: null,
        jsonList: [],
        param: {
            page: 1,
            rows: 10,
            beginrq: null,
            endrq: null,
            parm: ''
        },
        isUpdate: 0,
        modifyIndex: null,
        totlePage: 0
    },
    watch: {
        //进价总计和零价总计
        jsonList: function () {
            var temJj = 0;
            var temLj = 0;

            for (var i = 0; i < this.jsonList.length; i++) {
                temJj += parseFloat(this.jsonList[i].ypjj * this.jsonList[i].rksl);
                temLj += parseFloat(this.jsonList[i].yplj * this.jsonList[i].rksl);
                this.json.jjzj = temJj.toFixed(2);
                this.json.ljzj = temLj.toFixed(2);
            }

            if (this.jsonList.length == 0) {
                this.json.jjzj = 0;
                this.json.ljzj = 0;
            }

        }

    },
    updated: function () {
        changeWin();
    },
    methods: {
        loadNum: function () {
            this.num = wrapper.num;
        },
        //判断是否有操作权限
        hasCx: function (cx) {
            if (!cx) {
                malert("用户没有操作权限！", 'top', 'defeadted');
                return true;
            }
        },
        //进入页面加载单据列表信息
        getData: function () {
            common.openloading('.zui-table-view');
            //清空退货明细信息
            rksh.tjdDetail = [];
            rksh.tjd = null;
            rksh.isUpdate = 0;
            rksh.modifyIndex = null;
            //是否选择库房
            if (wrapper.popContent.kfbm == undefined || wrapper.popContent.kfbm == null | wrapper.popContent.kfbm == "") {
                malert("请先择库房!");
                return;
            }

            Vue.set(rksh.param, 'kfbm', wrapper.popContent.kfbm);
            Vue.set(rksh.param, 'parm', wrapper.search);
            $.getJSON('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=queryTjdForPass' +
                '&parm=' + JSON.stringify(rksh.param),
                function (data) {
                    if (data.a == 0) {
                        rksh.tjdList = data.d.list;
                        rksh.totlePage = Math.ceil(data.d.total / rksh.param.rows);
                        common.closeLoading()
                    } else {
                        malert(data.c);
                        common.closeLoading()
                    }
                });
        },

        //入库审核
        passData: function () {
            var json = {
                list: {
                    tjdmx: this.jsonList,
                }

            };

            this.$http.post('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=shtjd', JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.isShow = false;
                        wrapper.isShowkd = true;
                        rksh.isShow = false;
                        rksh.isShowkd = true;
                        this.TjShow = false;
                        wrapper.TjShow = false;
                        rksh.zfShow = false;
                        rksh.getData();
                        malert("审核成功！")
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
        //作废退库单
        invalidData: function (num) {
            if (num != null && num != undefined) {
                this.isCheck = num;
            }
            //库房非空
            if (wap.isSelKf()) {
                return;
            }
            //2018/07/04二次弹窗作废提示
            if (common.openConfirm("确认作废该条信息吗？", function () {

                var json = {
                    'kfbm': rksh.tjdList[rksh.isCheck].kfbm,
                    'tjdjh': rksh.tjdList[rksh.isCheck].tjdjh
                };
                rksh.$http.post('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=zftjd', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == "0") {
                        rksh.getData();
                        malert("作废成功！", 'top', 'success');
                        rksh.cancel();
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
            })) {
                return false;
            }
            // var json = {
            //     'kfbm': this.tjdList[this.isCheck].kfbm,
            //     'tjdjh': this.tjdList[this.isCheck].tjdjh
            // };
            // this.$http.post('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=zftjd',
            //     JSON.stringify(json))
            //     .then(function (data) {
            //         if (data.body.a == "0") {
            //             rksh.getData();
            //             malert("作废成功！",'top','success')
            //         } else {
            //             malert(data.body.c);
            //         }
            //     });


        },
        //显示调价单细节
        showDetail: function (index) {
            this.isCheck = index;
            wrapper.isShow = true;
            wrapper.isShowkd = false;
            wrapper.isShowpopL = false;
            rksh.isShow = true;
            rksh.isShowkd = false;
            this.TjShow = false;
            wrapper.TjShow = false;
            rksh.zfShow = false;
            if (this.tjdList[index].qrzfbz == '1' || this.tjdList[index].qrzfbz == '2') {
                this.mxShShow = false;
                wrapper.jyinput = true;
                this.ShShow = false;
            } else {
                this.mxShShow = true;
                wrapper.jyinput = false;
                this.ShShow = true;
            }
            wrapper.zdyxm = this.tjdList[index].tjrxm;
            wrapper.zdrq = rksh.fDate(this.tjdList[index].zdrq, 'date');
            wrapper.popContent.kfbm = this.tjdList[index].kfbm;
            wrapper.popContent.bzms = this.tjdList[index].bzms;
			
			wrapper.sfjsz = this.tjdList[index].sfjsvalue
			wrapper.sfjsvalue = wrapper.sfjs[wrapper.sfjsz]
			
			wrapper.tjlxz = this.tjdList[index].tjlx
			wrapper.tjlxvalue = wrapper.tjlx[wrapper.tjlxz]
			
			
			$('#tzsjVal').val(rksh.fDate(this.tjdList[index].tzsjval, 'AllDate'));
            rksh.getMx(this.tjdList[index]['tjdjh']);
        },
        //明细
        getMx: function (tjdjh) {
            rksh.jsonList = [];
            var parm = {
                tjdjh: tjdjh
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=queryTjdMxByTjdh' +
                '&parm=' + JSON.stringify(parm),
                function (data) {
                    rksh.jsonList = data.d;
                });
        },

        editIndex: function (index) {
            rksh.isUpdate = 1;
            this.isCheck = index;
            wrapper.isShowkd = false;
            wrapper.isShow = true;
            wrapper.isShowpopL = true;
            rksh.isShow = true;
            rksh.isShowkd = false;
            rksh.TjShow = true;
            rksh.ShShow = false;
            wrapper.TjShow = false;
            rksh.zfShow = true;
            this.mxShShow = true;
            wrapper.jyinput = false;
            wrapper.zdyxm = this.tjdList[index].tjrxm;
            wrapper.zdrq = rksh.fDate(this.tjdList[index].zdrq, 'date');
            wrapper.popContent.kfbm = this.tjdList[index].kfbm;
            wrapper.popContent.bzms = this.tjdList[index].bzms;
			
			wrapper.sfjsz = this.tjdList[index].sfjsvalue
			wrapper.sfjsvalue = wrapper.sfjs[wrapper.sfjsz]
			
			wrapper.tjlxz = this.tjdList[index].tjlx
			wrapper.tjlxvalue = wrapper.tjlx[wrapper.tjlxz]
			
            this.tjd = this.tjdList[index];
            rksh.getMx(this.tjd.tjdjh);
        },
		checkDate : function (dateStr){
			var reg = /^(\d+)-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;
			if(!reg.test(dateStr)){
				var a = /^(\d{4})-(\d{2})-(\d{2})$/
				if (!a.test(dateStr)) {
				    return "9999-01-01 00:00:00";
				}else{
				    return dateStr+" 00:00:00";
				}
			}else{
				return dateStr;
			}
		},
        //提交所有材料
        submitAll: function () {
            //是否禁止提交
            if (this.isSubmited) {
                malert("数据提交中，请稍候！");
                return;
            }
            //判断提交数据正确性
            if (this.jsonList.length <= 0) {
                malert("没有可提交的数据");
                return;
            }
            //是否禁止提交
            this.isSubmited = true;
            var tjd = null;
            if (rksh.tjd) {
                tjd = rksh.tjd;
            } else {
                tjd = wap.tjdContent;
            }
            tjd.kfbm = wrapper.popContent.kfbm;
            tjd.bzms = wrapper.popContent.bzms;
			tjd.sfjsvalue = wrapper.sfjsz
			tjd.tzsjval = wrapper.tjsjValue
			tjd.tjlx = wap.wtjlxz
			
			for (let i = 0; i < this.jsonList.length; i++) {
				this.jsonList[i].yxqz = this.checkDate(this.jsonList[i].yxqz);
			}
			
            //准备数据，包括退货单对象和退货单明细对象
            var json = {
                "list": {
                    "tjd": tjd,
                    "tjdmx": this.jsonList
                }
            };

            this.$http.post('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=modify',
                JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == 0) {
                        malert("数据更新成功");
                        this.jsonList = [];
                        this.isShow = false;
                        this.isShowkd = true;
                        wrapper.isShowkd = true;
                        wrapper.isShow = false;
                        wrapper.isShowpopL = false;
                        rksh.getData();
                    } else {
                        malert("数据提交失败" + data.body.c);
                    }
                    //是否禁止提交
                    rksh.isSubmited = false;
                }, function (error) {
                    console.log(error);
                    //是否禁止提交
                    rksh.isSubmited = false;
                });
        },
        //删除 2018/07/04二次弹窗删除提示
        scmx: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                rksh.jsonList.splice(index, 1);
            })) {
                return false;
            }
            // this.jsonList.splice(index,1);
        },
        //双击修改
        edit: function (num) {
            rksh.modifyIndex = num;
            //编辑
            if (num == null) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if (num == null) {
                    malert("请选中你要修改的数据");
                    return false;
                }
            }
            rksh.isUpdate = 1;
            wap.wisUpdate = 1;
            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            wap.open();
        },
        //取消
        cancel: function () {
            wrapper.isShow = false;
            wrapper.isShowkd = true;
            wrapper.isShowpopL = false;
            rksh.isShow = false;
            rksh.isShowkd = true;
        }
    }
});
var wrapper = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],

    data: {
        isShowpopL: false,
        isTabelShow: false,
        isShowkd: true,
        isShow: false,
        keyWord: '',
        csParm: {},
        zdrq: getTodayDateTime(), //获取制单日期
        zdyxm: '',
        jyinput: false, //禁用输入框
        TjShow: true,
        tjdContent: {}, //退货单对象
        jsonList: [],
        rkdList: [], //入库单集合
        KFList: [{
            kfbm:'08',
            kfmc:'卫消库',
            ksbm:'0952'
        }], //库房
        tjlx: {
            "3": "调进价及调零价",
            "1": "调进价",
            "2": "调零价",
        },
        sfjs: {
            "1": "是",
            "0": "否",
        },
        title: '',
        totle: '',
        rkd: {}, //入库单对象
        num: 0,
        param: {
            page: '',
            rows: '',
            total: ''
        },
        tjlxz: null,
        tjlxvalue: null,
        sfjsz: null,
        sfjsvalue: null,
        tjsjValue: null,
        //****
        popContent: {},
        search: ''
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date');
        this.param.endrq = this.fDate(new Date(), 'date');
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: this.param.beginrq,
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                rksh.param.beginrq = value;
                rksh.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: this.param.endrq,
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                rksh.param.endrq = value;
                rksh.getData();
            }
        });
		var myDate1 = new Date();
        this.tjsjValue = this.fDate(myDate1.setDate(myDate1.getDate()), 'AllDate');
        laydate.render({
            elem: '#tzsjVal',
            eventElem: '.zui-date',
            type: 'datetime',
            value: this.tjsjValue,
            min: this.fDate(myDate1.setDate(myDate1.getDate()), 'AllDate'),
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.tjsjValue = value;
            }
        });
        this.tjlxz = 3
        this.tjlxvalue = this.tjlx[3]
        this.sfjsz = 1
        this.sfjsvalue = this.sfjs[1]
    },
    updated: function () {
        changeWin();
    },
    methods: {
        kd: function (index) {
            this.num = index;
            rksh.loadNum();
            switch (wrapper.num) {
                case 0:
                    rksh.isUpdate = 0;
                    this.isShowkd = false;
                    this.isShow = true;
                    this.isShowpopL = true;
                    rksh.isShow = true;
                    rksh.isShowkd = false;
                    rksh.TjShow = true;
                    rksh.ShShow = false;
					wrapper.jyinput = false;
					this.sfjsz = 1
					this.sfjsvalue = this.sfjs[1]
                    $('#bzms').attr('disabled', false);
                    rksh.jsonList = [];
                    rksh.zfShow = false;
                    rksh.mxShShow = true;
                    if (sessionStorage.getItem("userName" + userId)) {
                        var reg = /^[\'\"]+|[\'\"]+$/g;
                        wrapper.zdyxm = sessionStorage.getItem("userName" + userId).replace(reg, '');
                    }
                    break;
                case 1:
                    wap.open();
                    rksh.isUpdate = 0;
                    wap.wisUpdate = 0;
                    wap.title = '添加材料';
                    wap.popContent = {};
                    break;
            }

        },
        //新增
        AddMdel: function () {


        },
        //删除
        del: function () {
            rksh.clearAll();
        },
        //检索查询回车键
        searchHc: function () {
            rksh.param.page = 1
            rksh.getData();
        },
        getKFData: function () {
            //下拉框获取库房
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100011007"}',
                function (data) {
                    if (data.a == 0) {
                        // wrapper.KFList = data.d;
                        Vue.set(wrapper.popContent, 'kfbm', wrapper.KFList[0].kfbm);
                        // qxksbm = data.d[0].ksbm;
                        wap.getCsqx(); //加载完库房再次加载参数权限
                    } else {
                        malert("一级库房获取失败", 'top', 'defeadted');
                    }
                });

        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "kfbm":
                    Vue.set(this.popContent, 'kfbm', val[0]);
                    Vue.set(this.popContent, 'kfmc', val[4]);
                    rksh.getData();
                    break;
                default:
                    break;
            }
        },
//组件选择下拉框之后的回调
        resultLexChange: function (val) {
            //先获取到操作的哪一个
            wrapper.tjlxz = val[0]
            wrapper.tjlxvalue = wrapper.tjlx[val[0]]
            wap.wtjlxz = wrapper.tjlxz
        },
        resultJsChange: function (val) {
            //先获取到操作的哪一个
            wrapper.sfjsz = val[0]
            wrapper.sfjsvalue = wrapper.sfjs[val[0]]

        },
    }
});


var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        isShowpopL: false,
        iShow: false,
        isTabelShow: false,
        flag: false,
        jsShow: false,
        title: '',
        added: false,
        lyks: null,
        tkyf: null,
        tkyfBM: null,
        kfbm: null,

        zdrq: getTodayDateTime(), //设置制单时间
        tjdContent: {}, //退货单对象
        cgryList: [], //采购人员
        csParm: {}, //参数权限
        csContent: [], //参数权限

        //			KSList: [],
        KFList: [{
            kfbm:'08',
            kfmc:'卫消库',
            ksbm:'0952'
        }],
        //			lyr: null,
        //			ryList: [],
        //			ghdwList: [],
        //材料信息对象
        popContent: {},
        wtjlxz: 3,
        wisUpdate: 0,
        dg: {
            page: 1,
            rows: 20,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {},
        thembm: {
            '材料名称': 'ypmc',
            '材料编号': 'ypbm',
            '商品名': 'ypspm',
            '分装比例': 'fzbl',

        },
        thempc: {
            '材料名称': 'ypmc',
            '材料编号': 'ypbm',
            '商品名': 'ypspm',
            '规格': 'ypgg',
            '价格': 'yjj',
            '库存数量': 'kcsl',
            '分装比例': 'fzbl',
            '产地': 'cdbm',
        },
		kclist:[]
    },
    methods: {

        //关闭
        closes: function () {
            $(".side-form").removeClass('side-form-bg');
            $(".side-form").addClass('ng-hide');
			wap.kclist = [];
        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg');
            $(".side-form").removeClass('ng-hide');
        },

        //确定开单
        confirms: function () {
            wap.addData();
            //this.closes();
        },
        //库房非空判断
        isSelKf: function () {
            if (wrapper.popContent.kfbm == undefined || wrapper.popContent.kfbm == null || wrapper.popContent.kfbm == '') {
                malert("请先选择库房!");
                return true;
            }
        },
        //获取参数权限
        getCsqx: function () {
            //设置ajax为同步
            $.ajaxSetup({
                async: false
            });
            var parm = {
                "ksbm": qxksbm,
                "ylbm": "N040100011007"
            };
            //获取科室权限 002001007  qxksbm
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {

                            if (json.d[i].csqxbm == 'N04010001100701') {
                                //1-是 0-否 ，允许调进价
                                if (json.d[i].csz == '0') {
                                    Vue.set(wrapper.csParm, 'ypjj', true);
                                } else {
                                    Vue.set(wrapper.csParm, 'ypjj', false);

                                }
                            }
                            if (json.d[i].csqxbm == 'N04010001100702') {
                                //1-是 0-否 ，允许调零价
                                if (json.d[i].csz == '0') {
                                    Vue.set(wrapper.csParm, 'yplj', true);
                                } else {
                                    Vue.set(wrapper.csParm, 'yplj', true);
                                }
                            }
                            if (json.d[i].csqxbm == 'N04010001100703') {
                                //1-是 0-否 ，材料调价是否自动按加成方式生成价格
                                if (json.d[i].csz == '0') {
                                    Vue.set(wrapper.csParm, 'jcfs', false);

                                } else {
                                    Vue.set(wrapper.csParm, 'jcfs', true);
                                }
                            }
                            if (json.d[i].csqxbm == 'N04010001100704') {
                                //1-是 0-否 ，是否判断处方发药
                            }
                            if (json.d[i].csqxbm == 'N04010001100705') {
                                //1-是 0-否 ，一级库房调价开单权限
                                if (json.d[i].csz == '1') {
                                    wrapper.isShow = true; //入库开单页面显示
                                } else {
                                    wrapper.isShow = true; //开单隐藏
                                }
                            }
                            if (json.d[i].csqxbm == 'N04010001100706') {
                                //1-是 0-否 ，一级库房调价审核权限
                                if (json.d[i].csz == '1') {
                                    rksh.ShShow = true; //审核页面可用
                                } else {
                                    //审核页面不可用
                                    rksh.ShShow = false;
                                }
                            }
                            if (json.d[i].csqxbm == 'N04010001100707') {
                                //1-按材料批次调价（同时判断材料编码）  0-按材料编码调价 ，调价方案
                                if (json.d[i].csz == '0') {
                                    wrapper.csParm.bmtj = true;
                                } else {
                                    wrapper.csParm.bmtj = false;
                                }
                            }
                        }
                        rksh.getData();
                    }
                }
            })
        },
        //材料名称下拉table检索数据
        changeDown: function (event, type) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                if (wap.popContent.ypmc == undefined || wap.popContent.ypmc == null) {
                    malert("请选择材料！");
                    $("#ypmc").focus();
                    return;
                }
                if (type == 'xjj') {
                    $("#xlj").focus();
                } else if (type == "xlj") {
                    wap.addData();
                } else if (type == 'ypmc') {
                    this.nextFocus(event);
					
					this.getsjYpkc(wap.popContent.ypbm);
                }
            }
        },
        //当输入值后才触发
        change: function (event, type,val) {
            // //库房非空判断
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
							this.popContent[type] = val
							this.dg.parm = encodeURIComponent(val);
			                this.dg.sort = "ypbm";
			                var bean = {
			                    'kfbm': wrapper.popContent.kfbm,
			                    'ypmc': this.dg.parm,
			                };
            
            //分页参数
            wap.dg.page = 1;
            wap.dg.rows = 200;

            $.getJSON('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=queryTjdForSel' +
                '&dg=' + JSON.stringify(wap.dg) + "&ksbm=" + qxksbm + "&parm=" + JSON.stringify(bean),
                function (data) {
                    if (data.d.list) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                            data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                            //按分装比例换算二级库房价格
                            data.d.list[i]['yyfjj'] = data.d.list[i]['yjj'] / data.d.list[i]['fzbl'];
                            data.d.list[i]['yyflj'] = data.d.list[i]['ylj'] / data.d.list[i]['fzbl'];
                            //保留四位小数
                            data.d.list[i]['yyfjj'] = Math.round(data.d.list[i]['yyfjj'] * 10000) / 10000;
                            data.d.list[i]['yyflj'] = Math.round(data.d.list[i]['yyflj'] * 10000) / 10000;
                        }
                        wap.searchCon = data.d.list;
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                    }
                    //根据材料编码调价和批次调价设置表头
                    if (wap.csParm.bmtj) {
                        wap.them = wap.thembm;
                    } else {
                        wap.them = wap.thempc;
                    }
                    if (data.d.list.length != 0) {
                        $(".selectGroup").hide();
                        _searchEvent.show()
                    } else {
                        $(".selectGroup").show();
                    }
                });
        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                var bean = {
                    'kfbm': wrapper.tjdContent.kfbm,
                    'ypmc': this.dg.parm,
                };
                wap.dg.page++;

                $.getJSON('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=queryTjdForSel' +
                    '&dg=' + JSON.stringify(wap.dg) + "&ksbm=" + qxksbm + "&parm=" + JSON.stringify(bean),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                wap.searchCon.push(data.d.list[i]);
                            }
                            wap.total = data.d.total;
                            wap.selSearch = 0;
                        } else {
                            malert('分页信息获取失败', 'top', 'defeadted')
                        }

                    });
                return;
            }
            
				this.getsjYpkc(item.ypbm);
            //todo
            this.popContent = item;
            $(".selectGroup").hide();
        },
		getsjYpkc:function(ypbm){
			let para = {ypbm: ypbm};
			wap.kclist = [];
			let $url = '/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=newqueryYpkc' +
			    '&dg=&ksbm=' + qxksbm + "&parm=" + JSON.stringify(para)
			$.ajaxSettings.async = false;
			$.getJSON($url,
			    function (data) {
			        if (data.a == '0') {
			            wap.kclist = data.d;
			        }
			    });
		},
        //是否使用加成方式调价
        getXlj: function () {
            if (this.wtjlxz != 3) {
                return false;
            }
            if (wrapper.csParm.jcfs && this.popContent.jcbl) {
                malert("自动按加成方式生成零价！");
                //按加成比例四舍五入获取零价
                this.popContent.xlj = this.popContent.jcbl * this.popContent.xjj
            }
        },
        getYp: function () {

            var bean = {
                'kfbm': wrapper.popContent.kfbm,
                'ypbm': this.popContent.ypbm,
            };
            //分页参数
            wap.dg.page = 1;
            wap.dg.rows = 200;

            $.getJSON('/actionDispatcher.do?reqUrl=New1ykglKfywTjd&types=newqueryTjdForSave' +
                '&dg=' + JSON.stringify(wap.dg) + "&ksbm=" + qxksbm + "&parm=" + JSON.stringify(bean),
                function (data) {
                    if (data.e == '-0500010100') {
                        malert("该材料有未摆情况！不允许调价！");

                        return false;
                        // if (common.openConfirm("是否继续进行调价？", function () {
                        //     wap.clsj(data);
                        // })) {
                        //     return false;
                        // }
                    } else if (data.d.list) {
                        wap.clsj(data);
                    }

                });
        },
        clsj: function (data) {
            for (var i = 0; i < data.d.list.length; i++) {
                data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                //按分装比例换算二级库房价格
                data.d.list[i]['yyfjj'] = data.d.list[i]['yjj'] / data.d.list[i]['fzbl'];
                data.d.list[i]['yyflj'] = data.d.list[i]['ylj'] / data.d.list[i]['fzbl'];
                //保留四位小数
                data.d.list[i]['yyfjj'] = Math.round(data.d.list[i]['yyfjj'] * 10000) / 10000;
                data.d.list[i]['yyflj'] = Math.round(data.d.list[i]['yyflj'] * 10000) / 10000;
                data.d.list[i]['yklj'] = wap.popContent.xlj
                data.d.list[i]['ykjj'] = wap.popContent.xjj
                if (wrapper.tjlxz == 1) {
                    if (data.d.list[i]['yply'] == 2) {
                        var yyfjj = wap.popContent.xjj / data.d.list[i]['fzbl'];
                        data.d.list[i]['xjj'] = Math.round(yyfjj * 10000) / 10000;
                    } else {
                        data.d.list[i]['xjj'] = wap.popContent.xjj
                    }

                } else if (wrapper.tjlxz == 2) {
                    if (data.d.list[i]['yply'] == 2) {
                        var yyflj = wap.popContent.xlj / data.d.list[i]['fzbl'];
                        data.d.list[i]['xlj'] = Math.round(yyflj * 10000) / 10000;
                    } else {
                        data.d.list[i]['xlj'] = wap.popContent.xlj
                    }

                } else {
                    if (data.d.list[i]['yply'] == 2) {
                        var yyfjj = wap.popContent.xjj / data.d.list[i]['fzbl'];
                        data.d.list[i]['xjj'] = Math.round(yyfjj * 10000) / 10000;
                        var yyflj = wap.popContent.xlj / data.d.list[i]['fzbl'];
                        data.d.list[i]['xlj'] = Math.round(yyflj * 10000) / 10000;
                    } else {
                        data.d.list[i]['xjj'] = wap.popContent.xjj
                        data.d.list[i]['xlj'] = wap.popContent.xlj
                    }

                }


                data.d.list[i]['kfbm'] = wrapper.popContent.kfbm;
                rksh.jsonList.push(data.d.list[i]);
            }
            wap.popContent = {};
        },
        //添加调价材料信息
        addData: function () {
            if (wrapper.popContent.kfbm == undefined || wrapper.popContent.kfbm == null || wrapper.popContent.kfbm == '') {
                malert("请先选择库房!", 'top', 'defeadted');
                return true;
            }
            if (wap.popContent.ypmc == undefined || wap.popContent.ypmc == null || wap.popContent.ypmc == '') {
                malert("请输入材料!", 'top', 'defeadted');
                return true;
            }
            if (wap.wtjlxz == 1 || wap.wtjlxz == 3) {
                if (wap.popContent.xjj == undefined || wap.popContent.xjj == null || wap.popContent.xjj == '' || wap.popContent.xjj < 0) {
                    malert("请输入材料现进价或者进价不正确!", 'top', 'defeadted');
                    return true;
                }
            }

            if (wap.wtjlxz == 2 || wap.wtjlxz == 3) {
                if (wap.popContent.xlj == undefined || wap.popContent.xlj == null || wap.popContent.xlj == '' || wap.popContent.xlj < 0) {
                    malert("请输入材料现零价或者现零价不正确!", 'top', 'defeadted');
                    return true;
                }
            }


            var haveError = false;
            if (haveError) {
                malert("录入区数据不完整", 'top', 'defeadted');
                return false;
            }
            this.popContent.kfbm = wrapper.popContent.kfbm;
            if (rksh.isUpdate == 0) {

                wap.getYp();

                $("#ypmc").focus();
            } else {
                //修改
                rksh.$set(rksh.jsonList, rksh.modifyIndex, wap.popContent);
                wap.popContent = {};
                wap.closes();
            }
        },

    }
});

//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
var saves = null;
wrapper.getKFData();
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

});




