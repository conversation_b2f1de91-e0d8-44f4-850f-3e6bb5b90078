var socket;
var rslmsg;
var ifok = false;
var rs = false;
var wsImpl = window.WebSocket || window.MozWebSocket;
//var response=false;
var qhtfsn = new Vue({
	el: '#qhtfsn',
	mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
	components: {
		'search-table': searchTable,
		'jbsearch-table': searchTable,
	},
	data: {
		bxcw: false, //保险是否有错
		klxList: {
			'1': '磁卡',
			'2': 'IC卡',
			'3': 'CPU卡'
		},
		klxIndex: '3',
		brxb: '01',
		mblist: [],
		ybkxxList: [],
		cbryxxList: [],
		cbryxxReturn: "",
		ybkxxStr: null,
		ybcsContent: {},
		bxlbbm: null,
		bxurl: null,
		ybkxxContent: {},
		cbryxxContent: {
			mxbbz: "0",
			mxbbzmc: "否",
			yllbMz:'11'
		},
		ybkpassword: null,
		mxbstr: null,
		mxblist: [],
		mxbpd: [{
			mxbbz: "0",
			mxbbzmc: "否"
		}, {
			mxbbz: "1",
			mxbbzmc: "是"
		}],
		jbContent: {},
		searchCon: [],
		selSearch: -1,
		them_tran: {},
		jbbm: null,
		them: {
			'疾病编码': 'jbbm',
			'疾病名称': 'jbmc',
			'英文名称': 'dm',
			'拼音代码': 'pyzjm',
		},
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		ybksbm: null,
		zyyszh: null,
		fycfh: [],
		fybm: [],
		scfylist: [],
		jsxxList: [],
		yjsContent: {},
	},
	mounted: function () {
		this.bulidConn();
	},
	methods: {
		qxjstest: function () {
			rs = false;
			ifok = false;
			var testparm = {
				SIN1: "H0428",
				SIN2: "41B",
				SIN3: "28262196||" + JSON.parse(sessionStorage.getItem("userName"+userId)),
				SIN4: "系统管理员",
			}
			socket.send(JSON.stringify(testparm));
			//			while(!response){
			var cs = 0;
			var interval = setInterval(function () {
				cs += 1;
				console.log(cs);
				if (rs) {
					if (ifok) {
						clearInterval(interval);
						qhtfsn.bxcw = false;
						malert("取消成功", 'right', 'defeadted');
						rs = false;
						ifok = false;
					} else {
						malert("医保调用取消结算失败！请手动取消！", 'right', 'defeadted');
						clearInterval(interval);
						qhtfsn.bxcw = true;
						rs = false;
						ifok = false;
						malert("医保错误！", 'right', 'defeadted');
					}
				}
				if (cs >= 10) {
					rs = false;
					ifok = false;
					clearInterval(interval);
				}
			}, 3000)
		},


		//取消结算
		qxJs: function (obj, cs) {
			rs = false;
			ifok = false;
			var testparm = {
				SIN1: cs.jgbm,
				SIN2: "41B",
				SIN3: obj.zxlsh + "||" + + JSON.parse(sessionStorage.getItem("userName"+userId)),
				SIN4: tableInfo.userName,
			}
			socket.send(JSON.stringify(testparm));
			//    			while(!response){
			var cs = 0;
			var interval = setInterval(function () {
				cs += 1;
				if (rs) {
					if (ifok) {
						clearInterval(interval);
						qhtfsn.bxcw = false;
						malert("取消成功", 'right', 'success');
						rs = false;
						ifok = false;
					} else {
						malert("医保调用取消结算失败！请手动取消！", 'right', 'defeadted');
						clearInterval(interval);
						qhtfsn.bxcw = true;
						rs = false;
						ifok = false;
						malert("医保错误！", 'right', 'defeadted');
					}
				}
				if (cs >= 10) {
					rs = false;
					ifok = false;
					clearInterval(interval);
				}
			}, 3000)
			//    			}
			//    			response=false;
		},
		//保存结算
		saveTest:function(){
			var obj = {};

			obj.ybkxx = "2341242421421423";
			obj.grbh = "2018113045555";
			obj.sfzh = "51092415687569";
			obj.xm = "wang er xiao";
			obj.xb = "1";
			obj.cgrq = "2018-12-13";
			obj.csrq = "2010-01-05";
			obj.grzhye = 124.50;
			obj.jfnx = "201812";
			obj.cbjgmc = "本地";
			obj.cbdwglm = "12323";
			obj.dwmc = null;
			obj.pkbz = "0";
			obj.mxtsjbkbje = null;
			obj.tqrq = "2018-10-12";
			obj.ybkh = "2018556565656488";
			obj.yllbMz = "01";
			//不显示的
			obj.nnjbylybje = 0.00;
			obj.zybcdyzt = "";
			obj.dedyzt = "";
			obj.cbdtcqbh = "510000";
			obj.cbdfzx = "51030";
			obj.shbzkkh = "21332323232";
			obj.xzlx = "01";
			obj.qtsm = "";
			obj.scbsfbm = "";
			obj.jbylzt = "";
			obj.jbbm =  "x01.9830";
			obj.zdmc = "上呼吸道感染";
			obj.jbbm1 = "x01.9830";
			obj.jbmc1 = "上呼吸道感染1";
			obj.jbbm2 = "x01.9830";
			obj.jbmc2 = "上呼吸道感染2";
			obj.jbbm3 = "x01.9830";
			obj.jbmc3 = "上呼吸道感染3";
			obj.jbbm4 = "x01.9830";
			obj.jbmc4 = "上呼吸道感染4";
			obj.zxlsh = "65564564132568512155";//中心消费流水号
			obj.jylsh = "65564564132568512155";//中心消费流水号
			obj.xfqye = 152.00//消费前账户余额
			obj.grzhzf = 202;//个人账户支付金额
			obj.xfhye = 0;//消费后账户余额
			obj.grxjzf = 55;//个人现金支付

			obj.bxje = 555;
			obj.zfy = 1000;
			obj.ybkzhye = 565;
			obj.ybkzf = 100;//医保卡支付
			obj.zxjzdjh = "65564564132568512155";//中心单据号

			obj.mzjzh   = "";//2门诊就诊号
			obj.jysj   = "2018-12-30 12:30:40";//6交易时间
			obj.cbdjgbm = "";//8参保地机构编码
			obj.cbsf = "";//9参保身份
			obj.cbxz = "";//10参保险种
			obj.qslb = "";//11清算类别
			obj.xzlb = "";//14险种类别
			obj.tsrysf = "";//15特殊人员身份
			obj.qzfje = 0.00;//18全自付部分
			obj.xzfbf = 0.00;//19先自付部分
			obj.cxjzfbf = 0.00;//20超限价自付部分
			obj.jrbxfwbf = 0.00;//21进入报销范围部分
			obj.bcqfx = "";//22本次起付线
			obj.byljqfx = "";//23本年累计起付线
			obj.jbylbxbx = "";//24基本医疗保险报销
			obj.gwyylbxbx = "";//25公务员医疗保险报销
			obj.sybxbx = "";//26生育保险报销
			obj.cxjmdbxpf = "";//27城乡居民大病险赔付
			obj.xnh = "";//28新农合
			obj.zgjjbx = "";//29照顾基金报销
			obj.qtxzbx = "";//30其他险种报销(含贫困人口倾斜支付金额)
			obj.gezfhj = 0.00;//31个人自付合计
			obj.ybzfhj = 4000;//32医保支付合计
			obj.bxqksm = "";//33报销情况说明
			obj.czzgbcbx = "";//34无 34城镇职工补充保险
			obj.yljgbm = "";//36医疗机构编码
			obj.mzzdkzfje = 0.00;//37门诊最多可支付金额
			obj.jmjbylbxbx = 0.00;//38居民基本医疗保险报销
			obj.ryyljzjssjid = "";//39年龄
			obj.jydtcqbh = "40";//40就医地统筹区编号
			obj.cbdjsyllbdm = "";//41参保地结算医疗类别代码
			obj.jyrq = "2018-12-30 12:30:40";
			obj.yxbz = '0'; //1 有效
			obj.jbr = "xitongguanliyuan";
			obj.ryghxh = "20185545111451";
			var url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=mzjs&parm=" + JSON.stringify(obj);
			$.getJSON(
				url,
				function (json) {
					if (json.a == 0) {
						malert("医保调用结算信息保存成功！", 'right', 'defeadted');
						rightVue.zxlshSn = obj.zxlsh;
					} else {
						malert(json.c, 'right', 'defeadted');
					}
				});
		},
		saveJs: function (obj) {
			$.ajaxSettings.async = false;
			if (obj.ydmbbz) {
				//异地正式结算
				rs = false;
				ifok = false;
				var testparm = {
					SIN1: obj.SIN1,
					SIN2: obj.SIN2,
					SIN3: obj.SIN3,
					SIN4: obj.SIN4,
				}
				socket.send(JSON.stringify(testparm));
				var cs = 0;
				var interval = setInterval(function () {
					if (rs) {
						cs += 1;
						if (ifok) {
							var rl = rslmsg.split("@$");
							qhtfsn.jsxxList = rl[1].split("&&");
							qhtfsn.cbryxxContent.zxlsh = qhtfsn.jsxxList[0];//中心消费流水号
							qhtfsn.cbryxxContent.mzjzh   = qhtfsn.jsxxList[1];//2门诊就诊号

							obj.zxlsh = qhtfsn.jsxxList[0];//中心消费流水号
							obj.mzjzh = qhtfsn.jsxxList[1];//2门诊就诊号

							qhtfsn.bxcw = false;
							rs = false;
							ifok = false;
							clearInterval(interval);

							if (!qhtfsn.bxcw) {

								rightVue.bxcw = qhtfsn.bxcw;

								$.getJSON(
									"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=mzjs&parm=" + JSON.stringify(obj),
									function (json) {
										if (json.a == 0) {
											//malert("医保调用结算信息保存成功！", 'top', 'defeadted');
											rightVue.zxlshSn = obj.zxlsh;
											rightVue.successQhtf();
										} else {
											malert(json.c, 'right', 'defeadted');
										}
									});

							} else {
								malert("医保业务存在错误！", 'right', 'defeadted');
								return
							}
						} else {
							malert("失败！请重试", 'right', 'defeadted');
							rs = false;
							ifok = false;
							qhtfsn.bxcw = true;
						}
						if (cs >= 10) {
							clearInterval(interval);
							rs = false;
							ifok = false;
							qhtfsn.bxcw = true;
						}
					}
				}, 3000);
			}
			else
			{
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=mzjs&parm=" + JSON.stringify(obj),
					function (json) {
						if (json.a == 0) {
							//malert("医保调用结算信息保存成功！", 'top', 'defeadted');
							rightVue.zxlshSn = obj.zxlsh;
							rightVue.mzjsQhtf();
						} else {
							malert(json.c, 'right', 'defeadted');
						}
					});
			}

		},
		selectOne: function (item) {
			if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++; // 设置当前页号
				this.searching(true, 'jbmc', this.jbContent['jbmc']); // 传参表示请求下一页,不传就表示请求第一页
			} else { // 否则就是选中事件,为json赋值
				this.jbContent = item;
				Vue.set(this.jbContent, 'jbmc', this.jbContent['jbmc']);
				this.jbContent.jbbm = this.jbContent.jbbm.replace(/\+/g, "%2B"); // 将+号替换为十六进制
				qhtfsn.jbbm = this.jbContent.jbbm;
				qhtfsn.cbryxxContent.jbbm = this.jbContent.jbbm;
				qhtfsn.cbryxxContent.zdmc = this.jbContent.jbmc;
				$(".selectGroup").hide();
			}
		},
		searching: function (add, type, val) {

			this.jbContent['jbmc'] = val;
			if (!add) this.page.page = 1;
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			if (this.jbContent['jbmc'] == undefined || this.jbContent['jbmc'] == null) {
				this.page.parm = "";
			} else {
				this.page.parm = this.jbContent['jbmc'];
			}
			var str_param = {
				parm: this.page.parm,
				page: this.page.page,
				rows: this.page.rows,
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=jbbm&method=query&parm=" +
				JSON.stringify(str_param),
				function (json) {
					if (json.a == 0) {
						var date = null;
						var res = eval('(' + json.d + ')');
						if (add) { // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
							for (var i = 0; i < res.list.length; i++) {
								qhtfsn.searchCon.push(res.list[i]);
							}
						} else {
							qhtfsn.searchCon = res.list;
						}
						qhtfsn.page.total = res.total;
						qhtfsn.selSearch = 0;
						if (res.list.length > 0 && !add) {
							$(".selectGroup").hide();
							_searchEvent.show();
						}
					} else {
						malert("查询失败  " + json.c, 'right', 'defeadted');
					}
				});
		},
		changeDown: function (event, type) {

			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'jbContent', 'searchCon');
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
				if (type == "text") {

					Vue.set(this.jbContent, 'jbmc', this.jbContent['jbmc']);
					this.jbContent.jbbm = this.jbContent.jbbm.replace(/\+/g, "%2B"); // 将+号替换为十六进制
					qhtfsn.jbbm = this.jbContent.jbbm;
					qhtfsn.cbryxxContent.jbbm = this.jbContent.jbbm;
					qhtfsn.cbryxxContent.zdmc = this.jbContent.jbmc;
					$(".selectGroup").hide();
					this.selSearch = 0;
					this.nextFocus(event);
				}
			}
		},
		//        saveJs:function(){
		//
		//        },
		//引入
		loadBx: function () {
			qhtfsn.cbryxxContent.jbbm = '';
			qhtfsn.cbryxxContent.zdmc = '';
			if (qhtfsn.cbryxxContent.jbbm == null) {
				malert("疾病编码不能为空！", 'right', 'defeadted');
				return
			}

			if (qhtfsn.cbryxxContent.zdmc == null) {
				malert("诊断名称不能为空！", 'right', 'defeadted');
				return
			}
			if (qhtfsn.cbryxxContent.grbh != null) {
				rightVue.snybryContent = qhtfsn.cbryxxContent;
			} else {
				malert("请先读卡才能引入！", 'right', 'defeadted');
				return
			}
			popTable.isShow = false;
		},
		load: function () {
			$.ajaxSettings.async = false;
			qhtfsn.bxcw = false;
			//参数完整性判断
			if (qhtfsn.cbryxxContent.jbbm == null) {
				malert("疾病编码不能为空！", 'right', 'defeadted');
				return
			}
			if (qhtfsn.cbryxxContent.zdmc == null) {
				malert("诊断名称不能为空！", 'right', 'defeadted');
				return
			}
			//开始医保交易
			var fyze = 0.00;
			for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
				fyze += rightVue.brfyjsonList[i].fyje;
			}
			var mdate = qhtfsn.fDate(new Date(), 'date');
			var datelist = mdate.split('-');
			var jzrq = datelist[0] + datelist[1] + datelist[2];
			var sin1 = qhtfsn.ybcsContent.jgbm;
			var sin2 = "11";
			//查询对应科室编码及执业医生证号
			var parm = {
				ysbm: rightVue.mzjbxxContent.jzys,
				ksbm: rightVue.mzjbxxContent.ghks,
			}
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=queryKsAndYs&parm=" + JSON.stringify(parm), function (json) {
				if (json.a == "0") {
					var res = eval('(' + json.d + ')');
					var bmlist = res.list;
					qhtfsn.ybksbm = bmlist[0].ybksbm;
					qhtfsn.zyyszh = bmlist[0].zyyszh;
					if(qhtfsn.ybksbm == null || qhtfsn.ybksbm == '' || qhtfsn.ybksbm == undefined){
						malert("该科室科室未对码！", 'right', 'defeadted');
						return
					}
					if(qhtfsn.zyyszh == null || qhtfsn.zyyszh == '' || qhtfsn.zyyszh == undefined){
						malert("该执业医生证号未对码！", 'right', 'defeadted');
						return
					}
				} else {
					malert("医生或科室为对码！", 'right', 'defeadted');
					return
				}
			});


			var sin3 = qhtfsn.cbryxxContent.grbh + "||" + rightVue.fzContent['ryghxh'] +
				"||" + jzrq + "||" + fyze + "||" + qhtfsn.ybksbm + "||" +
				rightVue.mzjbxxContent.jzysxm + "||" + qhtfsn.jbbm + "||" + qhtfsn.cbryxxContent.zdmc +
				"||" + jzrq + "||||" + tableInfo.userName;
			var sin4 = "";

			//查询门诊费用
			for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
				if (rightVue.brfyjsonList[i].yzlx == '2' || rightVue.brfyjsonList[i].yzlx == null) {
					qhtfsn.fycfh.push(rightVue.brfyjsonList[i].yzhm);
				} else {
					qhtfsn.fybm.push(rightVue.brfyjsonList[i].mxfyxmbm);
				}
			}
			if (qhtfsn.fycfh.length < 1) {
				qhtfsn.fycfh = null;
			} else if (qhtfsn.fycfh[0] == null || qhtfsn.fycfh[0] == undefined) {
				qhtfsn.fycfh = null;
			}
			if (qhtfsn.fybm.length < 1) {
				qhtfsn.fybm = null;
			}
			if (qhtfsn.fybm == null && qhtfsn.fycfh == null) {
				malert("无费用信息！", 'right', 'defeadted');
				return
			}
			var parm2 = {
				page: 1,
				rows: 10,
				fycfh: qhtfsn.fycfh,
				fybm: qhtfsn.fybm,
			}
			$.getJSON(
				"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=queryMzfy&parm=" + JSON.stringify(parm2),
				function (json) {
					if (json.a == 0) {
						var res = eval('(' + json.d + ')');
						console.log("+++++++++++++++++++++++++");
						qhtfsn.scfylist = res.list;
						console.log(qhtfsn.scfylist);
						for (var i = 0; i < qhtfsn.scfylist.length; i++) {
							if (qhtfsn.scfylist[i].bxxmbm != null) {
								var cfh = "";
								var fymxbh = "";
								var fysl = 0;
								var fydj = 0.00;
								var fyje = 0.00;
								var jx = "";
								var yl = "";
								var yf = "";
								var ts = 1;
								var djlx = qhtfsn.scfylist[i].ybdjbm;
								var sflb = qhtfsn.scfylist[i].sflb;
								var cfys = rightVue.mzjbxxContent.jzysxm;
								var jbr = tableInfo.userName;
								var kjrq = jzrq;
								var ks = qhtfsn.ybksbm;
								var zyyszh = qhtfsn.zyyszh;
								var xmbm = qhtfsn.scfylist[i].bxxmbm;
								var xmmc = qhtfsn.scfylist[i].bxxmmc;
								var yyjjdw = qhtfsn.scfylist[i].yyjjdw;
								var sfjk = "";
								var ypcd = "";

								if (qhtfsn.scfylist[i].fymxbh == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											if (rightVue.brfyjsonList[j].fyid != null) {
												fymxbh = rightVue.brfyjsonList[j].fyid;
											} else {
												fymxbh = rightVue.brfyjsonList[j].mxfybm + j;
											}
										}
									}
								} else {
									fymxbh = qhtfsn.scfylist[i].fymxbh;
								}
								if (qhtfsn.scfylist[i].jx != null) {
									jx = qhtfsn.scfylist[i].jx;
								}
								if (qhtfsn.scfylist[i].cfyl != null) {
									yl = qhtfsn.scfylist[i].cfyl;
								}
								if (qhtfsn.scfylist[i].yyts != null) {
									ts = qhtfsn.scfylist[i].yyts;
								} else {
									ts = 1;
								}
								if (qhtfsn.scfylist[i].yyffmc != null) {
									yf = qhtfsn.scfylist[i].yyffmc;
								}
								if (qhtfsn.scfylist[i].sfjk != null) {
									sfjk = qhtfsn.scfylist[i].sfjk;
								} else {
									sfjk = "0";
								}
								if (qhtfsn.scfylist[i].ypcd != null) {
									ypcd = qhtfsn.scfylist[i].ypcd;
								} else {
									ypcd = "诊疗";
								}
								if (qhtfsn.scfylist[i].cfh == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											if (rightVue.brfyjsonList[j].yzhm != null) {
												cfh = rightVue.brfyjsonList[j].yzhm;
											} else {
												cfh = "";
											}
										}
									}
								} else {
									cfh = qhtfsn.scfylist[i].cfh;
								}
								if (qhtfsn.scfylist[i].fysl == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											fysl += parseInt(rightVue.brfyjsonList[j].fysl);
										}
									}
								} else {
									fysl = qhtfsn.scfylist[i].fysl;
								}
								if (qhtfsn.scfylist[i].fydj == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											fydj = parseFloat(rightVue.brfyjsonList[j].fydj).toFixed(2);
										}
									}
								} else {
									fydj = parseFloat(qhtfsn.scfylist[i].fydj).toFixed(2);
								}
								if (qhtfsn.scfylist[i].fyje == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											fyje += rightVue.brfyjsonList[j].fyje;
										}
									}
									fyje = parseFloat(fyje).toFixed(2);
								} else {
									fyje = parseFloat(qhtfsn.scfylist[i].fyje).toFixed(2);
								}
								if (fysl == 0) {
									fysl = null;
								}

								if (fyje == 0.00) {
									fyje = null;
								}
								sin4 += cfh + "||" + djlx + "||" + kjrq + "||" + ks + "||" + cfys + "||" + jbr + "||" + zyyszh + "||" + sflb + "||" + xmbm +
									"||" + xmmc + "||" + jx + "||" + fydj + "||" + fysl + "||" + fyje + "||" + yl + "||" + yf + "||" + ts + "||" + fymxbh +
									"||" + yyjjdw + "||" + sfjk + "||" + ypcd;
								if (qhtfsn.scfylist.length > 1 && i != qhtfsn.scfylist.length - 1) {
									sin4 += "@$"
								}
							} else {
								malert("第" + i + "条数据未对码！", 'right', 'defeadted');
							}
						}
					} else {
						malert("费用查询失败！", 'right', 'defeadted');
						return
					}
				});
			var testparm = {
				SIN1: sin1,
				SIN2: sin2,
				SIN3: sin3,
				SIN4: sin4,
			}
			rs = false;
			ifok = false;
			socket.send(JSON.stringify(testparm));
			//      	while(!response){
			var cs = 0;
			var interval = setInterval(function () {
				if (rs) {
					//          	 		response=true;
					cs += 1;
					if (ifok) {
						var rl = rslmsg.split("$");
						qhtfsn.jsxxList = rl[1].split("&&");
						qhtfsn.yjsContent.bxje = qhtfsn.jsxxList[4];
						qhtfsn.cbryxxContent.ryyljzjssjid = qhtfsn.jsxxList[0];
						qhtfsn.cbryxxContent.jylsh = qhtfsn.jsxxList[1];
						qhtfsn.cbryxxContent.zfy = qhtfsn.jsxxList[2];
						qhtfsn.cbryxxContent.bxbl = qhtfsn.jsxxList[3];
						qhtfsn.cbryxxContent.bxje = qhtfsn.jsxxList[4];
						qhtfsn.cbryxxContent.nnptmfbx = qhtfsn.jsxxList[5];
						qhtfsn.cbryxxContent.nnptmfjtbx = qhtfsn.jsxxList[6];
						qhtfsn.cbryxxContent.ryghxh = rightVue.fzContent['ryghxh'];
						qhtfsn.bxcw = false;
						rs = false;
						ifok = false;
						clearInterval(interval);



						rightVue.yjsContentSnqhtf = qhtfsn.yjsContent;
						rightVue.snybryContent = qhtfsn.cbryxxContent;
						rightVue.snybcsContent = qhtfsn.ybcsContent;

						console.log("++++++++++++++++++++++++++++");
						console.log(rightVue.yjsContentSnqhtf);
						console.log("++++++++++++++++++++++++++++");
						console.log(rightVue.snybryContent);

						if (!qhtfsn.bxcw) {
							popTable.isShow = false;
							rightVue.bxcw = qhtfsn.bxcw;
						} else {
							malert("医保业务存在错误！", 'right', 'defeadted');
							return
						}
					} else {
						malert("失败！请重试", 'right', 'defeadted');
						rs = false;
						ifok = false;
						qhtfsn.bxcw = true;
					}
					if (cs >= 10) {
						clearInterval(interval);
						rs = false;
						ifok = false;
						qhtfsn.bxcw = true;
					}
				}
			}, 3000);
			//      	}
			//      	response=false;
			//-----------------------------------------------------------医保业务在此结束---------------------------------------------

		},
		//预結算
		loadYjs: function () {

			$.ajaxSettings.async = false;
			qhtfsn.bxcw = false;
			//参数完整性判断
			if (qhtfsn.cbryxxContent.jbbm == null) {
				malert("疾病编码不能为空！", 'right', 'defeadted');
				return
			}
			if (qhtfsn.cbryxxContent.zdmc == null) {
				malert("诊断名称不能为空！", 'right', 'defeadted');
				return
			}


			//异地病人判断
			qhtfsn.cbryxxContent.ydmbbz = false;

			if (qhtfsn.ybkxxList[14] != qhtfsn.ybcsContent.tcqbh || qhtfsn.cbryxxContent.mxbbz == "1") {
				qhtfsn.cbryxxContent.ydmbbz = true;
				var ghxh ="";
				ghxh = rightVue.fzContent['ryghxh'];
				if (ghxh == null || ghxh == "") {
					ghxh = new Date().Format("yyyyMMddhhmmssS");
				}
				var sin1_1 = qhtfsn.ybcsContent.jgbm;
				var sin2_1 = "51E";
				var sin3_1 = ghxh + '||' + qhtfsn.cbryxxContent.grbh + '||' + qhtfsn.ybkxxList[14] + '||' + qhtfsn.ybcsContent.tcqbh
				var sin4_1 = ""
				var testparm = {
					SIN1: sin1_1,
					SIN2: sin2_1,
					SIN3: sin3_1,
					SIN4: sin4_1,
				}
				rs = false;
				ifok = false;
				socket.send(JSON.stringify(testparm));
				var cs = 0;
				var interval = setInterval(function () {
					if (rs) {

						cs += 1;
						if (ifok) {
							qhtfsn.cbryxxContent.jylsh51 = rslmsg;
							qhtfsn.bxcw = false;
							rs = false;
							ifok = false;
							clearInterval(interval);

							console.log("++++++++++++获取51E++++++++++++++++");
							console.log(qhtfsn.cbryxxContent.jylsh51);
							console.log("++++++++++++结束51E++++++++++++++++");

							if (!qhtfsn.bxcw) {
								rightVue.bxcw = qhtfsn.bxcw;
								qhtfsn.loadYjsYd();

							} else {
								malert("医保业务获取异地交易流水号错误！", 'right', 'defeadted');
								return
							}
						} else {
							malert("医保业务获取异地交易流水号错误！", 'right', 'defeadted');
							rs = false;
							ifok = false;
							qhtfsn.bxcw = true;
						}
						if (cs >= 10) {
							clearInterval(interval);
							rs = false;
							ifok = false;
							qhtfsn.bxcw = true;
						}
					}
				}, 3000);
			}
			else
			{
				//开始医保交易
				var fyze = 0.00;
				for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
					fyze += rightVue.brfyjsonList[i].fyje;
				}
				var mdate = qhtfsn.fDate(new Date(), 'date');
				var datelist = mdate.split('-');
				var jzrq = datelist[0] + datelist[1] + datelist[2];
				//查询对应科室编码及执业医生证号
				var parm = {
					ysbm: rightVue.mzjbxxContent.jzys,
					ksbm: rightVue.mzjbxxContent.ghks,
				}
				$.ajaxSettings.async = false;
				$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=queryKsAndYs&parm=" + JSON.stringify(parm), function (json) {
					if (json.a == "0") {
						var res = eval('(' + json.d + ')');
						var bmlist = res.list;
						qhtfsn.ybksbm = bmlist[0].ybksbm;
						qhtfsn.zyyszh = bmlist[0].zyyszh;
					} else {
						malert("医生或科室为对码！", 'right', 'defeadted');
						return
					}
				});
				var ghxh ="";
				ghxh = rightVue.fzContent['ryghxh'];

				var sin1 = qhtfsn.ybcsContent.jgbm;
				var sin2 = "41A";
				var sin3 = qhtfsn.cbryxxReturn + "||" + qhtfsn.ybkpassword + "||" + parseFloat(fyze).toFixed(2) + "||" + tableInfo.userName;
				var sin4 = "";


				//查询门诊费用
				for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
					if (rightVue.brfyjsonList[i].yzlx == '2' || rightVue.brfyjsonList[i].yzlx == null) {
						qhtfsn.fycfh.push(rightVue.brfyjsonList[i].yzhm);
					} else {
						qhtfsn.fybm.push(rightVue.brfyjsonList[i].mxfyxmbm);
					}
				}
				if (qhtfsn.fycfh != null){
					if (qhtfsn.fycfh.length < 1) {
						qhtfsn.fycfh = null;
					} else if (qhtfsn.fycfh[0] == null || qhtfsn.fycfh[0] == undefined) {
						qhtfsn.fycfh = null;
					}
				}

				if (qhtfsn.fybm != null && qhtfsn.fybm.length < 1) {
					qhtfsn.fybm = null;
				}
				if (qhtfsn.fybm == null && qhtfsn.fycfh == null) {
					malert("无费用信息！", 'right', 'defeadted');
					return
				}
				var parm2 = {
					page: 1,
					rows: 10,
					fycfh: qhtfsn.fycfh,
					fybm: qhtfsn.fybm,
				}
				var fyfssj =  jzrq;
				var mxsl = 0
				$.ajaxSettings.async = false;
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=queryMzfy&parm=" + JSON.stringify(parm2),
					function (json) {
						if (json.a == 0) {
							var res = eval('(' + json.d + ')');
							console.log("+++++++++++++++++++++++++");
							qhtfsn.scfylist = res.list;
							console.log(qhtfsn.scfylist);
							for (var i = 0; i < qhtfsn.scfylist.length; i++) {
								if (qhtfsn.scfylist[i].bxxmbm != null) {
									var cfh = "";
									var fymxbh = "";
									var fysl = 0;
									var fydj = 0.00;
									var fyje = 0.00;
									var jx = "";
									var yl = "";
									var yf = "";
									var ts = 1;
									var djlx = qhtfsn.scfylist[i].ybdjbm;
									var sflb = qhtfsn.scfylist[i].sflb;
									var cfysbm = rightVue.mzjbxxContent.jzys;
									var kdks = rightVue.mzjbxxContent.ghks;
									var cfys = rightVue.mzjbxxContent.jzysxm;
									var jbr = tableInfo.userName;
									var kjrq = mdate;
									var ks = qhtfsn.ybksbm;
									var zyyszh = qhtfsn.zyyszh;
									var xmbm = qhtfsn.scfylist[i].bxxmbm;
									var xmmc = qhtfsn.scfylist[i].bxxmmc;
									var mxfybm = qhtfsn.scfylist[i].mxfybm;
									var hisxmmc = qhtfsn.scfylist[i].hisxmmc;
									var yyjjdw = qhtfsn.scfylist[i].yyjjdw;
									var sfxmdj = qhtfsn.scfylist[i].sfxmdj;
									var mllb = qhtfsn.scfylist[i].mllb;
									var ybdjbm = qhtfsn.scfylist[i].ybdjbm;

									var gg = "";
									if (qhtfsn.scfylist[i].gg != null) {
										gg = qhtfsn.scfylist[i].gg;
									}
									var yfdw="";
									if (qhtfsn.scfylist[i].yfdw != null) {
										yfdw = qhtfsn.scfylist[i].yfdw;
									}
									var ybxzyybz = '1';
									var sfjkyp = "1";
									var sfjk = "";
									var ypcd = "";


									if (qhtfsn.scfylist[i].fymxbh == null) {
										for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
											if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
												if (rightVue.brfyjsonList[j].fyid != null) {
													fymxbh = rightVue.brfyjsonList[j].fyid;
												} else {
													fymxbh = rightVue.brfyjsonList[j].mxfybm + j;
												}
											}
										}
									} else {
										fymxbh = qhtfsn.scfylist[i].fymxbh;
									}
									if (qhtfsn.scfylist[i].jx != null) {
										jx = qhtfsn.scfylist[i].jx;
									}
									if (qhtfsn.scfylist[i].cfyl != null) {
										yl = qhtfsn.scfylist[i].cfyl;
									}
									if (qhtfsn.scfylist[i].yyts != null) {
										ts = qhtfsn.scfylist[i].yyts;
									} else {
										ts = 1;
									}
									if (qhtfsn.scfylist[i].yyffmc != null) {
										yf = qhtfsn.scfylist[i].yyffmc;
									}
									if (qhtfsn.scfylist[i].sfjk != null) {
										sfjk = qhtfsn.scfylist[i].sfjk;
									} else {
										sfjk = "0";
									}
									if (qhtfsn.scfylist[i].ypcd != null) {
										ypcd = qhtfsn.scfylist[i].ypcd;
									} else {
										ypcd = "诊疗";
									}
									if (qhtfsn.scfylist[i].cfh == null) {
										for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
											if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
												if (rightVue.brfyjsonList[j].yzhm != null) {
													cfh = rightVue.brfyjsonList[j].yzhm;
												} else {
													cfh = "";
												}
											}
										}
									} else {
										cfh = qhtfsn.scfylist[i].cfh;
									}
									if (qhtfsn.scfylist[i].fysl == null) {
										for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
											if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
												fysl += parseInt(rightVue.brfyjsonList[j].fysl);
											}
										}
									} else {
										fysl = qhtfsn.scfylist[i].fysl;
									}
									if (qhtfsn.scfylist[i].fydj == null) {
										for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
											if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
												fydj = parseFloat(rightVue.brfyjsonList[j].fydj).toFixed(2);
											}
										}
									} else {
										fydj = parseFloat(qhtfsn.scfylist[i].fydj).toFixed(2);
									}
									if (qhtfsn.scfylist[i].fyje == null) {
										for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
											if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
												fyje += rightVue.brfyjsonList[j].fyje;
											}
										}
										fyje = parseFloat(fyje).toFixed(2);
									} else {
										fyje = parseFloat(qhtfsn.scfylist[i].fyje).toFixed(2);
									}

									sin4 += xmbm + '||' + xmmc+ '||'+ hisxmmc +'||'+ fydj + '||' + fysl;
									if (qhtfsn.scfylist.length > 1 && i != qhtfsn.scfylist.length - 1) {
										sin4 += "@$"
									}

									mxsl += 1;
								} else {
									malert("第" + i + "条数据未对码！", 'top', 'defeadted');
								}
							}
						} else {
							malert("费用查询失败！", 'top', 'defeadted');
							return
						}
					});


				var testparm = {};
				testparm.SIN1 = sin1;
				testparm.SIN2 = sin2;
				testparm.SIN3 = sin3;
				testparm.SIN4 = sin4;

				rs = false;
				ifok = false;
				socket.send(JSON.stringify(testparm));
				//      	while(!response){
				var cs = 0;
				var interval = setInterval(function () {
					if (rs) {
						//          	 		response=true;
						cs += 1;
						if (ifok) {
							qhtfsn.cbryxxContent.ybkxx = qhtfsn.cbryxxReturn;
							var rl = rslmsg.split("$");
							qhtfsn.jsxxList = rl[1].split("&&");
							qhtfsn.cbryxxContent.zxlsh = qhtfsn.jsxxList[0];//中心消费流水号
							qhtfsn.cbryxxContent.jylsh = qhtfsn.jsxxList[0];//中心消费流水号
							qhtfsn.cbryxxContent.xfqye = parseFloat(qhtfsn.jsxxList[1]).toFixed(2);//消费前账户余额
							qhtfsn.cbryxxContent.grzhzf = parseFloat(qhtfsn.jsxxList[2]).toFixed(2);//个人账户支付金额
							qhtfsn.cbryxxContent.xfhye = parseFloat(qhtfsn.jsxxList[3]).toFixed(2);//消费后账户余额
							qhtfsn.cbryxxContent.grxjzf = parseFloat(qhtfsn.jsxxList[4]).toFixed(2);//个人现金支付
							qhtfsn.yjsContent.bxje = parseFloat(qhtfsn.jsxxList[2]).toFixed(2);
							qhtfsn.cbryxxContent.bxje = parseFloat(qhtfsn.jsxxList[2]).toFixed(2);
							qhtfsn.cbryxxContent.zfy = parseFloat(fyze).toFixed(2);
							qhtfsn.cbryxxContent.ybkzhye = parseFloat(qhtfsn.jsxxList[3]).toFixed(2);
							qhtfsn.cbryxxContent.ybkzf = parseFloat(qhtfsn.jsxxList[2]).toFixed(2);//医保卡支付
							qhtfsn.cbryxxContent.zxjzdjh = qhtfsn.jsxxList[0];//中心单据号

							qhtfsn.cbryxxContent.mzjzh   = "";//2门诊就诊号
							qhtfsn.cbryxxContent.jysj   = mdate;//6交易时间
							qhtfsn.cbryxxContent.cbdjgbm = "";//8参保地机构编码
							qhtfsn.cbryxxContent.cbsf = "";//9参保身份
							qhtfsn.cbryxxContent.cbxz = "";//10参保险种
							qhtfsn.cbryxxContent.qslb = "";//11清算类别
							qhtfsn.cbryxxContent.xzlb = "";//14险种类别
							qhtfsn.cbryxxContent.tsrysf = "";//15特殊人员身份
							qhtfsn.cbryxxContent.qzfje = 0.00;//18全自付部分
							qhtfsn.cbryxxContent.xzfbf = 0.00;//19先自付部分
							qhtfsn.cbryxxContent.cxjzfbf = 0.00;//20超限价自付部分
							qhtfsn.cbryxxContent.jrbxfwbf = 0.00;//21进入报销范围部分
							qhtfsn.cbryxxContent.bcqfx = "";//22本次起付线
							qhtfsn.cbryxxContent.byljqfx = "";//23本年累计起付线
							qhtfsn.cbryxxContent.jbylbxbx = "";//24基本医疗保险报销
							qhtfsn.cbryxxContent.gwyylbxbx = "";//25公务员医疗保险报销
							qhtfsn.cbryxxContent.sybxbx = "";//26生育保险报销
							qhtfsn.cbryxxContent.cxjmdbxpf = "";//27城乡居民大病险赔付
							qhtfsn.cbryxxContent.xnh = "";//28新农合
							qhtfsn.cbryxxContent.zgjjbx = "";//29照顾基金报销
							qhtfsn.cbryxxContent.qtxzbx = "";//30其他险种报销(含贫困人口倾斜支付金额)
							qhtfsn.cbryxxContent.gezfhj = 0.00;//31个人自付合计
							qhtfsn.cbryxxContent.ybzfhj = parseFloat(qhtfsn.jsxxList[2]).toFixed(2);//32医保支付合计
							qhtfsn.cbryxxContent.bxqksm = "";//33报销情况说明
							qhtfsn.cbryxxContent.czzgbcbx = "";//34无 34城镇职工补充保险
							qhtfsn.cbryxxContent.yljgbm = "";//36医疗机构编码
							qhtfsn.cbryxxContent.mzzdkzfje = 0.00;//37门诊最多可支付金额
							qhtfsn.cbryxxContent.jmjbylbxbx = 0.00;//38居民基本医疗保险报销
							qhtfsn.cbryxxContent.ryyljzjssjid = "";//39年龄
							qhtfsn.cbryxxContent.jydtcqbh = qhtfsn.ybcsContent.tcqbh;//40就医地统筹区编号
							qhtfsn.cbryxxContent.cbdjsyllbdm = "";//41参保地结算医疗类别代码
							qhtfsn.cbryxxContent.jyrq = mdate;
							qhtfsn.cbryxxContent.yxbz = '0'; //1 有效
							qhtfsn.cbryxxContent.jbr = tableInfo.userName;
							qhtfsn.cbryxxContent.ryghxh = rightVue.fzContent['ryghxh'];



							qhtfsn.bxcw = false;
							rs = false;
							ifok = false;
							clearInterval(interval);


							rightVue.yjsContentSnqhtf = qhtfsn.yjsContent;
							rightVue.snybryContent = qhtfsn.cbryxxContent;
							rightVue.snybcsContent = qhtfsn.ybcsContent;

							console.log("++++++++++++++++++++++++++++");
							console.log(rightVue.yjsContentSnqhtf);
							console.log("++++++++++++++++++++++++++++");
							console.log(rightVue.snybryContent);

							if (!qhtfsn.bxcw) {
								rightVue.bxcw = qhtfsn.bxcw;
								//本地医保无预结算。
								qhtfsn.saveJs(qhtfsn.cbryxxContent);

							} else {
								malert("医保业务存在错误！", 'top', 'defeadted');
								return
							}
						} else {
							malert("失败！请重试", 'top', 'defeadted');
							rs = false;
							ifok = false;
							qhtfsn.bxcw = true;
						}
						if (cs >= 60) {
							clearInterval(interval);
							rs = false;
							ifok = false;
							qhtfsn.bxcw = true;
						}
					}
				}, 500);

			}
			//-----------------------------------------------------------医保业务在此结束---------------------------------------------

		},
		//预结算异地
		loadYjsYd:function(){

			qhtfsn.bxcw = false;
			//开始医保交易
			var fyze = 0.00;
			for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
				fyze += rightVue.brfyjsonList[i].fyje;
			}
			var mdate = qhtfsn.fDate(new Date(), 'date');
			var datelist = mdate.split('-');
			var jzrq = datelist[0] + datelist[1] + datelist[2];
			//查询对应科室编码及执业医生证号
			var parm = {
				ysbm: rightVue.mzjbxxContent.jzys,
				ksbm: rightVue.mzjbxxContent.ghks,
			}
			$.ajaxSettings.async = false;
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=queryKsAndYs&parm=" + JSON.stringify(parm), function (json) {
				if (json.a == "0") {
					var res = eval('(' + json.d + ')');
					var bmlist = res.list;
					qhtfsn.ybksbm = bmlist[0].ybksbm;
					qhtfsn.zyyszh = bmlist[0].zyyszh;
				} else {
					malert("医生或科室为对码！", 'top', 'defeadted');
					return
				}
			});

			var ghxh ="";
			ghxh = rightVue.fzContent['ryghxh'];
			if (ghxh == null || ghxh == "") {
				ghxh = qhtfsn.fDate(new Date(), 'datetime');
			}
			var sin4_yd = "";

			//查询门诊费用
			for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
				if (rightVue.brfyjsonList[i].yzlx == '2' || rightVue.brfyjsonList[i].yzlx == null) {
					qhtfsn.fycfh.push(rightVue.brfyjsonList[i].yzhm);
				} else {
					qhtfsn.fybm.push(rightVue.brfyjsonList[i].mxfyxmbm);
				}
			}
			if (qhtfsn.fycfh.length < 1) {
				qhtfsn.fycfh = null;
			} else if (qhtfsn.fycfh[0] == null || qhtfsn.fycfh[0] == undefined) {
				qhtfsn.fycfh = null;
			}
			if (qhtfsn.fybm.length < 1) {
				qhtfsn.fybm = null;
			}
			if (qhtfsn.fybm == null && qhtfsn.fycfh == null) {
				malert("无费用信息！", 'top', 'defeadted');
				return
			}
			var parm2 = {
				page: 1,
				rows: 10,
				fycfh: qhtfsn.fycfh,
				fybm: qhtfsn.fybm,
			}
			var fyfssj =  jzrq;
			var mxsl = 0
			$.ajaxSettings.async = false;
			$.getJSON(
				"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=mzjy&method=queryMzfy&parm=" + JSON.stringify(parm2),
				function (json) {
					if (json.a == 0) {
						var res = eval('(' + json.d + ')');
						console.log("+++++++++++++++++++++++++");
						qhtfsn.scfylist = res.list;
						console.log(qhtfsn.scfylist);
						for (var i = 0; i < qhtfsn.scfylist.length; i++) {
							if (qhtfsn.scfylist[i].bxxmbm != null) {
								var cfh = "";
								var fymxbh = "";
								var fysl = 0;
								var fydj = 0.00;
								var fyje = 0.00;
								var jx = "";
								var yl = "";
								var yf = "";
								var ts = 1;
								var djlx = qhtfsn.scfylist[i].ybdjbm;
								var sflb = qhtfsn.scfylist[i].sflb;
								var cfysbm = rightVue.mzjbxxContent.jzys;
								var kdks = rightVue.mzjbxxContent.ghks;
								var cfys = rightVue.mzjbxxContent.jzysxm;
								var jbr = tableInfo.userName;
								var kjrq = mdate;
								var ks = qhtfsn.ybksbm;
								var zyyszh = qhtfsn.zyyszh;
								var xmbm = qhtfsn.scfylist[i].bxxmbm;
								var xmmc = qhtfsn.scfylist[i].bxxmmc;
								var mxfybm = qhtfsn.scfylist[i].mxfybm;
								var hisxmmc = qhtfsn.scfylist[i].hisxmmc;
								var yyjjdw = qhtfsn.scfylist[i].yyjjdw;
								var sfxmdj = qhtfsn.scfylist[i].sfxmdj;
								var mllb = qhtfsn.scfylist[i].mllb;
								var ybdjbm = qhtfsn.scfylist[i].ybdjbm;

								var gg = "";
								if (qhtfsn.scfylist[i].gg != null) {
									gg = qhtfsn.scfylist[i].gg;
								}
								var yfdw="";
								if (qhtfsn.scfylist[i].yfdw != null) {
									yfdw = qhtfsn.scfylist[i].yfdw;
								}
								var ybxzyybz = '1';
								var sfjkyp = "1";
								var sfjk = "";
								var ypcd = "";


								if (qhtfsn.scfylist[i].fymxbh == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											if (rightVue.brfyjsonList[j].fyid != null) {
												fymxbh = rightVue.brfyjsonList[j].fyid;
											} else {
												fymxbh = rightVue.brfyjsonList[j].mxfybm + j;
											}
										}
									}
								} else {
									fymxbh = qhtfsn.scfylist[i].fymxbh;
								}
								if (qhtfsn.scfylist[i].jx != null) {
									jx = qhtfsn.scfylist[i].jx;
								}
								if (qhtfsn.scfylist[i].cfyl != null) {
									yl = qhtfsn.scfylist[i].cfyl;
								}
								if (qhtfsn.scfylist[i].yyts != null) {
									ts = qhtfsn.scfylist[i].yyts;
								} else {
									ts = 1;
								}
								if (qhtfsn.scfylist[i].yyffmc != null) {
									yf = qhtfsn.scfylist[i].yyffmc;
								}
								if (qhtfsn.scfylist[i].sfjk != null) {
									sfjk = qhtfsn.scfylist[i].sfjk;
								} else {
									sfjk = "0";
								}
								if (qhtfsn.scfylist[i].ypcd != null) {
									ypcd = qhtfsn.scfylist[i].ypcd;
								} else {
									ypcd = "诊疗";
								}
								if (qhtfsn.scfylist[i].cfh == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											if (rightVue.brfyjsonList[j].yzhm != null) {
												cfh = rightVue.brfyjsonList[j].yzhm;
											} else {
												cfh = "";
											}
										}
									}
								} else {
									cfh = qhtfsn.scfylist[i].cfh;
								}
								if (qhtfsn.scfylist[i].fysl == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											fysl += parseInt(rightVue.brfyjsonList[j].fysl);
										}
									}
								} else {
									fysl = qhtfsn.scfylist[i].fysl;
								}
								if (qhtfsn.scfylist[i].fydj == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											fydj = parseFloat(rightVue.brfyjsonList[j].fydj).toFixed(2);
										}
									}
								} else {
									fydj = parseFloat(qhtfsn.scfylist[i].fydj).toFixed(2);
								}
								if (qhtfsn.scfylist[i].fyje == null) {
									for (var j = 0; j < rightVue.brfyjsonList.length; j++) {
										if (rightVue.brfyjsonList[j].mxfyxmbm == qhtfsn.scfylist[i].mxfybm) {
											fyje += rightVue.brfyjsonList[j].fyje;
										}
									}
									fyje = parseFloat(fyje).toFixed(2);
								} else {
									fyje = parseFloat(qhtfsn.scfylist[i].fyje).toFixed(2);
								}

								sin4_yd += cfh + '||' + cfh + '||'+ (i +1) +'||'+xmbm + '||' + mxfybm + '||' + hisxmmc + '||'+
											fysl + '||' + fydj + '||' + fyje + '||'+ yfdw +'||' + gg + '||' + jx + '||' + fysl + '||' + yf +'||'+
											ypcd + '||' + qhtfsn.zyyszh + '||' + cfys + '||' + kdks + '||' + fyfssj + '||'+
											+ '||' + sfjkyp + '||' + ts + '||' + yyjjdw + '||' + sfxmdj + '||' + ybdjbm + '||' + mllb ;
								if (qhtfsn.scfylist.length > 1 && i != qhtfsn.scfylist.length - 1) {
									sin4_yd += "@$"
								}

								mxsl += 1;
							} else {
								malert("第" + i + "条数据未对码！", 'top', 'defeadted');
							}
						}
					} else {
						malert("费用查询失败！", 'top', 'defeadted');
						return
					}
				});

			var mtbbm = "";
			if (qhtfsn.cbryxxContent.mxbbz == "1") {
				if (qhtfsn.cbryxxContent.jbbm1.length > 0 ) {
					mtbbm = qhtfsn.cbryxxContent.jbbm1;
				}
				if (qhtfsn.cbryxxContent.jbbm2.length > 0 ) {
					mtbbm = mtbbm + "@$"+ qhtfsn.cbryxxContent.jbbm2;
				}
				if (qhtfsn.cbryxxContent.jbbm3.length > 0 ) {
					mtbbm = mtbbm + "@$"+ qhtfsn.cbryxxContent.jbbm3;
				}
				if (qhtfsn.cbryxxContent.jbbm4.length > 0 ) {
					mtbbm = mtbbm + "@$"+ qhtfsn.cbryxxContent.jbbm4;
				}
			}
			var qslb = ''//23清算类别
			if (qhtfsn.cbryxxContent.jbbm1.length > 0 ) {
				qslb = "YD13";
			}
			else
			{
				qslb = "YD02";
			}
			var qsfzx =  qhtfsn.ybcsContent.tcqbh;//22清算分中心（510900）

			var sin1_yd = qhtfsn.ybcsContent.jgbm;
			var sin2_yd = "51D";
			var sin3_yd = qhtfsn.ybcsContent.ver + "||" + qhtfsn.ybkxxList[14] + "||" + qhtfsn.ybcsContent.tcqbh + "||" + qhtfsn.cbryxxContent.grbh + "||" + qhtfsn.cbryxxContent.sfzh + "||" + rightVue.fzContent['brxm'] + "||" +
				qhtfsn.cbryxxContent.ybkh + "||" +parseFloat(fyze).toFixed(2) + "||" + tableInfo.userName + "||" + mxsl + "||" + "0" + "||" + qhtfsn.jbbm + "||"+
				qhtfsn.cbryxxContent.jbbm1 + '||' + qhtfsn.cbryxxContent.jbbm2 + '||' + qhtfsn.cbryxxContent.zdmc  + '||' + '0' + '||' + fyfssj + '||' + ghxh + '||'+
				qhtfsn.zyyszh + '||' + rightVue.mzjbxxContent.jzysxm + '||' + qhtfsn.cbryxxContent.yllbMz + '||' + qhtfsn.cbryxxContent.jylsh51 + '||' + qsfzx + '||' + qslb + '@$' + mtbbm

			var testparm = {};
			testparm.SIN1 = sin1_yd;
			testparm.SIN2 = sin2_yd;
			testparm.SIN3 = sin3_yd;
			testparm.SIN4 = sin4_yd;
			qhtfsn.cbryxxContent.SIN1 = sin1_yd;
			qhtfsn.cbryxxContent.SIN2 = "51A";
			qhtfsn.cbryxxContent.SIN3 = sin3_yd;
			qhtfsn.cbryxxContent.SIN4 = sin4_yd;

			rs = false;
			ifok = false;
			socket.send(JSON.stringify(testparm));
			//      	while(!response){
			var cs = 0;
			var interval = setInterval(function () {
				if (rs) {
					//          	 		response=true;
					cs += 1;
					if (ifok) {
						qhtfsn.cbryxxContent.ybkxx = qhtfsn.cbryxxReturn;
						var rl = rslmsg.split("@$");
						qhtfsn.jsxxList = rl[1].split("&&");
						qhtfsn.cbryxxContent.zxlsh = qhtfsn.jsxxList[0];//中心消费流水号
						qhtfsn.cbryxxContent.jylsh = qhtfsn.jsxxList[0];//中心消费流水号
						qhtfsn.cbryxxContent.xfqye = parseFloat(qhtfsn.jsxxList[2]).toFixed(2);//消费前账户余额
						qhtfsn.cbryxxContent.grzhzf = parseFloat(qhtfsn.jsxxList[3]).toFixed(2);//个人账户支付金额
						qhtfsn.cbryxxContent.xfhye = parseFloat(qhtfsn.jsxxList[4]).toFixed(2);//消费后账户余额
						qhtfsn.cbryxxContent.grxjzf = parseFloat(qhtfsn.jsxxList[5]).toFixed(2);//个人现金支付
						qhtfsn.yjsContent.bxje = parseFloat(qhtfsn.jsxxList[3]).toFixed(2) + parseFloat(qhtfsn.jsxxList[17]).toFixed(2);//17报销金额
						qhtfsn.cbryxxContent.bxje = parseFloat(qhtfsn.jsxxList[17]).toFixed(2);//17报销金额
						qhtfsn.cbryxxContent.zfy = parseFloat(qhtfsn.jsxxList[16]).toFixed(2);//16总费用
						qhtfsn.cbryxxContent.ybkzhye = parseFloat(qhtfsn.jsxxList[4]).toFixed(2);//消费后账户余额
						qhtfsn.cbryxxContent.ybkzf = parseFloat(qhtfsn.jsxxList[3]).toFixed(2) + parseFloat(qhtfsn.jsxxList[17]).toFixed(2);//医保卡支付
						qhtfsn.cbryxxContent.zxjzdjh = qhtfsn.jsxxList[0];//中心单据号

						qhtfsn.cbryxxContent.mzjzh   = qhtfsn.jsxxList[1];//2门诊就诊号
						qhtfsn.cbryxxContent.jysj   = qhtfsn.jsxxList[6];//6交易时间
						qhtfsn.cbryxxContent.cbdjgbm = qhtfsn.jsxxList[8];//8参保地机构编码
						qhtfsn.cbryxxContent.cbsf = qhtfsn.jsxxList[9];//9参保身份
						qhtfsn.cbryxxContent.cbxz = qhtfsn.jsxxList[10];//10参保险种
						qhtfsn.cbryxxContent.qslb = qhtfsn.jsxxList[11];//11清算类别
						qhtfsn.cbryxxContent.xzlb = qhtfsn.jsxxList[14];//14险种类别
						qhtfsn.cbryxxContent.tsrysf = qhtfsn.jsxxList[15];//15特殊人员身份
						qhtfsn.cbryxxContent.qzfje = parseFloat(qhtfsn.jsxxList[18]).toFixed(2);//18全自付部分
						qhtfsn.cbryxxContent.xzfbf = parseFloat(qhtfsn.jsxxList[19]).toFixed(2);//19先自付部分
						qhtfsn.cbryxxContent.cxjzfbf = parseFloat(qhtfsn.jsxxList[20]).toFixed(2);//20超限价自付部分
						qhtfsn.cbryxxContent.jrbxfwbf = parseFloat(qhtfsn.jsxxList[21]).toFixed(2);//21进入报销范围部分
						qhtfsn.cbryxxContent.bcqfx = qhtfsn.jsxxList[22];//22本次起付线
						qhtfsn.cbryxxContent.byljqfx = qhtfsn.jsxxList[23];//23本年累计起付线
						qhtfsn.cbryxxContent.jbylbxbx = qhtfsn.jsxxList[24];//24基本医疗保险报销
						qhtfsn.cbryxxContent.gwyylbxbx = qhtfsn.jsxxList[25];//25公务员医疗保险报销
						qhtfsn.cbryxxContent.sybxbx = qhtfsn.jsxxList[26];//26生育保险报销
						qhtfsn.cbryxxContent.cxjmdbxpf = qhtfsn.jsxxList[27];//27城乡居民大病险赔付
						qhtfsn.cbryxxContent.xnh = qhtfsn.jsxxList[28];//28新农合
						qhtfsn.cbryxxContent.zgjjbx = qhtfsn.jsxxList[29];//29照顾基金报销
						qhtfsn.cbryxxContent.qtxzbx = qhtfsn.jsxxList[30];//30其他险种报销(含贫困人口倾斜支付金额)
						qhtfsn.cbryxxContent.gezfhj = parseFloat(qhtfsn.jsxxList[31]).toFixed(2);//31个人自付合计
						qhtfsn.cbryxxContent.ybzfhj = parseFloat(qhtfsn.jsxxList[32]).toFixed(2);//32医保支付合计
						qhtfsn.cbryxxContent.bxqksm = qhtfsn.jsxxList[33];//33报销情况说明
						qhtfsn.cbryxxContent.czzgbcbx = qhtfsn.jsxxList[34];//34无 34城镇职工补充保险
						qhtfsn.cbryxxContent.yljgbm = qhtfsn.jsxxList[36];//36医疗机构编码
						qhtfsn.cbryxxContent.mzzdkzfje = parseFloat(qhtfsn.jsxxList[37]).toFixed(2);//37门诊最多可支付金额
						qhtfsn.cbryxxContent.jmjbylbxbx = parseFloat(qhtfsn.jsxxList[38]).toFixed(2);//38居民基本医疗保险报销
						qhtfsn.cbryxxContent.ryyljzjssjid = qhtfsn.jsxxList[39];//39年龄
						qhtfsn.cbryxxContent.jydtcqbh = qhtfsn.jsxxList[40];//40就医地统筹区编号
						qhtfsn.cbryxxContent.cbdjsyllbdm = qhtfsn.jsxxList[41];//41参保地结算医疗类别代码
						qhtfsn.cbryxxContent.jyrq = mdate;
						qhtfsn.cbryxxContent.yxbz = '0'; //1 有效
						qhtfsn.cbryxxContent.jbr = tableInfo.userName;
						qhtfsn.cbryxxContent.ryghxh = rightVue.fzContent['ryghxh'];


						qhtfsn.bxcw = false;
						rs = false;
						ifok = false;
						clearInterval(interval);


						rightVue.yjsContentSnqhtf = qhtfsn.yjsContent;
						rightVue.snybryContent = qhtfsn.cbryxxContent;
						rightVue.snybcsContent = qhtfsn.ybcsContent;

						console.log("++++++++++++++++++++++++++++");
						console.log(rightVue.yjsContentSnqhtf);
						console.log("++++++++++++++++++++++++++++");
						console.log(rightVue.snybryContent);

						if (!qhtfsn.bxcw) {
							rightVue.bxcw = qhtfsn.bxcw;
							rightVue.mzjsQhtf();
						} else {
							malert("医保业务存在错误！", 'top', 'defeadted');
							return
						}
					} else {
						malert("失败！请重试", 'top', 'defeadted');
						rs = false;
						ifok = false;
						qhtfsn.bxcw = true;
					}
					if (cs >= 10) {
						clearInterval(interval);
						rs = false;
						ifok = false;
						qhtfsn.bxcw = true;
					}
				}
			}, 3000);


		},
		//读卡
		loadCard: function () {
//			var testparm = {
//					SIN1: qhtfsn.ybcsContent.jgbm,
//					SIN2: "11H",
//					SIN3: qhtfsn.klxIndex,
//					SIN4: "",
//				}
//				rs = false;
//				ifok = false;
//				socket.send(JSON.stringify(testparm));
//				//    		while(!response){
//				var cs1 = 0;
//				var interval1 = setInterval(function () {
//					cs1 += 1;
//					console.log(cs1);
//					if (rs) {
//						//        				response=true;
//						if (ifok) {
//							clearInterval(interval1);
//							console.log(rslmsg);
//							var rl = rslmsg.split("$");
//							var rel = rl[1].split("&&");
//							if (rel[0] == "1") {
//								qhtfsn.ybkpassword = rel[1];
//								console.log(qhtfsn.ybkpassword);
//								ifok = false;
//								rs = false;



							var testparm2 = {
								SIN1: qhtfsn.ybcsContent.jgbm,
								SIN2: "11F",
								SIN3: qhtfsn.klxIndex,
								SIN4: "",
							}
							socket.send(JSON.stringify(testparm2));
							var cs2 = 0;
							var interval2 = setInterval(function () {
								cs2 += 1;
								console.log(cs2);
								if (rs) {
									//                    					response=true;
									if (ifok) {
										console.log("=============2")
										clearInterval(interval2);
										var rl = rslmsg.split("$");
										qhtfsn.ybkxxStr = rl[1];
										qhtfsn.ybkxxList = rl[1].split("&&");
										//                    	    				qhtfsn.ybkpassword=qhtfsn.ybkxxList[0];
										rs = false;
										ifok = false;
										//                    	    				response=false;
										if (qhtfsn.klxIndex != "3") {
											qhtfsn.ybkxxList[14] = qhtfsn.ybcsContent.tcqbh;
										}
										var sin3 = qhtfsn.ybkpassword + "||" + qhtfsn.ybcsContent.ver + "||" +
											qhtfsn.ybkxxList[14] + "||" + qhtfsn.ybcsContent.tcqbh;
										console.log(qhtfsn.ybkxxStr);
										var sin4 = qhtfsn.ybkxxStr.replace(/&/g, '|') + "||" + qhtfsn.klxIndex;
										console.log(sin4);
										var testparm2 = {
											SIN1: qhtfsn.ybcsContent.jgbm,
											SIN2: "03B",
											SIN3: sin3,
											SIN4: sin4,
										}
										socket.send(JSON.stringify(testparm2));
										//                    	    				while(!response){
										var cs3 = 0;
										var interval3 = setInterval(function () {
											cs3 += 1;
											console.log(cs3);
											if (rs) {
												//                        	    	    				response=true;
												if (ifok) {
													console.log("=============3")
													clearInterval(interval3);
													qhtfsn.cbryxxReturn = rslmsg.replace(/&&/g, "||");
													qhtfsn.cbryxxContent.ybkxx = rslmsg.replace(/&&/g, "||");
													console.log(qhtfsn.cbryxxContent.ybkxx);
													var rl = rslmsg.split("$");
													qhtfsn.cbryxxList = rl[1].split("&&");
													var rllist = rslmsg.split("@$");
													var newstr = rslmsg.split("@$")[1];
													//显示的
													qhtfsn.cbryxxContent.grbh = qhtfsn.cbryxxList[0];
													qhtfsn.cbryxxContent.sfzh = qhtfsn.cbryxxList[1];
													qhtfsn.cbryxxContent.xm = qhtfsn.cbryxxList[2];
													qhtfsn.cbryxxContent.xb = qhtfsn.cbryxxList[3];
													qhtfsn.cbryxxContent.cgrq = qhtfsn.cbryxxList[4];
													qhtfsn.cbryxxContent.csrq = qhtfsn.cbryxxList[5];
													qhtfsn.cbryxxContent.grzhye = qhtfsn.cbryxxList[7];
													qhtfsn.cbryxxContent.jfnx = qhtfsn.cbryxxList[8];
													qhtfsn.cbryxxContent.cbjgmc = qhtfsn.cbryxxList[9];
													qhtfsn.cbryxxContent.cbdwglm = qhtfsn.cbryxxList[10];
													qhtfsn.cbryxxContent.dwmc = qhtfsn.cbryxxList[11];
													qhtfsn.cbryxxContent.pkbz = qhtfsn.cbryxxList[12];
													qhtfsn.cbryxxContent.mxtsjbkbje = qhtfsn.cbryxxList[14];
													qhtfsn.cbryxxContent.tqrq = qhtfsn.cbryxxList[25];
													qhtfsn.cbryxxContent.ybkh = qhtfsn.ybkxxList[0];
													qhtfsn.cbryxxContent.yllbMz = qhtfsn.ybkxxList[24];
													//不显示的
													qhtfsn.cbryxxContent.nnjbylybje = qhtfsn.cbryxxList[13];
													qhtfsn.cbryxxContent.zybcdyzt = qhtfsn.cbryxxList[15];
													qhtfsn.cbryxxContent.dedyzt = qhtfsn.cbryxxList[16];
													qhtfsn.cbryxxContent.cbdtcqbh = qhtfsn.cbryxxList[17];
													qhtfsn.cbryxxContent.cbdfzx = qhtfsn.cbryxxList[18];
													qhtfsn.cbryxxContent.shbzkkh = qhtfsn.cbryxxList[19];
													qhtfsn.cbryxxContent.xzlx = qhtfsn.cbryxxList[20];
													qhtfsn.cbryxxContent.qtsm = qhtfsn.cbryxxList[21];
													qhtfsn.cbryxxContent.scbsfbm = qhtfsn.cbryxxList[22];
													qhtfsn.cbryxxContent.tqrq = qhtfsn.cbryxxList[25];
													qhtfsn.cbryxxContent.jbylzt = newstr.split("&&")[26];
													qhtfsn.cbryxxContent.jbbm1 = "";
													qhtfsn.cbryxxContent.jbmc1 = "";
													qhtfsn.cbryxxContent.jbbm2 = "";
													qhtfsn.cbryxxContent.jbmc2 = "";
													qhtfsn.cbryxxContent.jbbm3 = "";
													qhtfsn.cbryxxContent.jbmc3 = "";
													qhtfsn.cbryxxContent.jbbm4 = "";
													qhtfsn.cbryxxContent.jbmc4 = "";
													if (rllist.length > 2) {
														qhtfsn.mxbstr = rslmsg.split("@$")[2];
														var list = qhtfsn.mxbstr.split("&&");
														for (var i = 0; i < list.length; i + 3) {
															var mxb = {
																bzlb: list[i],
																bxbm: list[i + 1],
																bxmc: list[i + 2],
															}
															if (i == 3) {
																qhtfsn.cbryxxContent.jbbm1 = list[i + 1];
																qhtfsn.cbryxxContent.jbmc1 = list[i + 2];
															}
															if (i == 6) {
																qhtfsn.cbryxxContent.jbbm2 = list[i + 1];
																qhtfsn.cbryxxContent.jbmc2 = list[i + 2];
															}
															if (i == 9) {
																qhtfsn.cbryxxContent.jbbm3 = list[i + 1];
																qhtfsn.cbryxxContent.jbmc3 = list[i + 2];
															}
															if (i == 12) {
																qhtfsn.cbryxxContent.jbbm4 = list[i + 1];
																qhtfsn.cbryxxContent.jbmc4 = list[i + 2];
															}
															qhtfsn.mxblist.push(mxb);
														}
													}
													qhtfsn.cbryxxContent.yllbMz = '11';
													qhtfsn.cbryxxContent = Object.assign({}, qhtfsn.cbryxxContent);
													console.log(qhtfsn.cbryxxContent);
													rs = false;
													ifok = false;
												} else {
													clearInterval(interval3);
													malert(rslmsg, 'top', 'defeadted');
													rs = false;
													ifok = false;
													malert("医保错误！", 'top', 'defeadted');
													return
												}
											}
											if (cs3 >= 60) {
												malert("省平台连接失败！请重试", 'top', 'defeadted');
												rs = false;
												ifok = false;
												clearInterval(interval3);
											}
										}, 500);
									} else {
										rs = false;
										ifok = false;
										clearInterval(interval2);
										malert("医保错误！", 'top', 'defeadted');
									}
								}
								if (cs2 >= 60) {
									malert("省平台连接失败！请重试", 'top', 'defeadted');
									rs = false;
									ifok = false;
									clearInterval(interval2);
								}
							}, 500);

//							} else {
//								clearInterval(interval1);
//								malert(qhtfsn.ybkpassword = rel[1], 'top', 'defeadted');
//								ifok = false;
//								rs = false;
//								malert("省平台连接失败！请重试", 'top', 'defeadted');
//							}
//						} else {
//							rs = false;
//							ifok = false;
//							clearInterval(interval1);
//							malert("医保错误！", 'top', 'defeadted');
//						}
//					}
//					if (cs1 >= 60) {
//						malert("失败！请重试", 'top', 'defeadted');
//						clearInterval(interval1);
//						rs = false;
//						ifok = false;
//					}
//				}, 500);

		},
		/*loadCard: function () {
			var testparm = {
				SIN1: qhtfsn.ybcsContent.jgbm,
				SIN2: "11H",
				SIN3: qhtfsn.klxIndex,
				SIN4: "",
			}
			rs = false;
			ifok = false;
			socket.send(JSON.stringify(testparm));
			//    		while(!response){
			var cs1 = 0;
			var interval1 = setInterval(function () {
				cs1 += 1;
				console.log(cs1);
				if (rs) {
					//        				response=true;
					if (ifok) {
						clearInterval(interval1);
						console.log(rslmsg);
						var rl = rslmsg.split("$");
						var rel = rl[1].split("&&");
						if (rel[0] == "1") {
							qhtfsn.ybkpassword = rel[1];
							console.log(qhtfsn.ybkpassword);
							ifok = false;
							rs = false;
							var testparm2 = {
								SIN1: qhtfsn.ybcsContent.jgbm,
								SIN2: "11F",
								SIN3: qhtfsn.klxIndex,
								SIN4: "",
							}
							socket.send(JSON.stringify(testparm2));
							//            					response=false;
							//            					while(!response){
							var cs2 = 0;
							var interval2 = setInterval(function () {
								cs2 += 1;
								console.log(cs2);
								if (rs) {
									//                    					response=true;
									if (ifok) {
										clearInterval(interval2);
										var rl = rslmsg.split("$");
										qhtfsn.ybkxxStr = rl[1];
										qhtfsn.ybkxxList = rl[1].split("&&");
										//                    	    				qhtfsn.ybkpassword=qhtfsn.ybkxxList[0];
										rs = false;
										ifok = false;
										//                    	    				response=false;
										if (qhtfsn.klxIndex != "3") {
											qhtfsn.ybkxxList[14] = qhtfsn.ybcsContent.tcqbh;
										}
										var sin3 = qhtfsn.ybkpassword + "||" + qhtfsn.ybcsContent.ver + "||" +
											qhtfsn.ybkxxList[14] + "||" + qhtfsn.ybcsContent.tcqbh;
										console.log(qhtfsn.ybkxxStr);
										var sin4 = qhtfsn.ybkxxStr.replace(/&/g, '|') + "||" + qhtfsn.klxIndex;
										console.log(sin4);
										var testparm2 = {
											SIN1: qhtfsn.ybcsContent.jgbm,
											SIN2: "03B",
											SIN3: sin3,
											SIN4: sin4,
										}
										socket.send(JSON.stringify(testparm2));
										//                    	    				while(!response){
										var cs3 = 0;
										var interval3 = setInterval(function () {
											cs3 += 1;
											console.log(cs3);
											if (rs) {
												//                        	    	    				response=true;
												if (ifok) {
													clearInterval(interval3);
													qhtfsn.cbryxxReturn = rslmsg.replace(/&&/g, "||");
													qhtfsn.cbryxxContent.ybkxx = rslmsg.replace(/&&/g, "||");
													console.log(qhtfsn.cbryxxContent.ybkxx);
													var rl = rslmsg.split("$");
													qhtfsn.cbryxxList = rl[1].split("&&");
													var rllist = rslmsg.split("@$");
													var newstr = rslmsg.split("@$")[1];
													//显示的
													qhtfsn.cbryxxContent.grbh = qhtfsn.cbryxxList[0];
													qhtfsn.cbryxxContent.sfzh = qhtfsn.cbryxxList[1];
													qhtfsn.cbryxxContent.xm = qhtfsn.cbryxxList[2];
													qhtfsn.cbryxxContent.xb = qhtfsn.cbryxxList[3];
													qhtfsn.cbryxxContent.cgrq = qhtfsn.cbryxxList[4];
													qhtfsn.cbryxxContent.csrq = qhtfsn.cbryxxList[5];
													qhtfsn.cbryxxContent.grzhye = qhtfsn.cbryxxList[7];
													qhtfsn.cbryxxContent.jfnx = qhtfsn.cbryxxList[8];
													qhtfsn.cbryxxContent.cbjgmc = qhtfsn.cbryxxList[9];
													qhtfsn.cbryxxContent.cbdwglm = qhtfsn.cbryxxList[10];
													qhtfsn.cbryxxContent.dwmc = qhtfsn.cbryxxList[11];
													qhtfsn.cbryxxContent.pkbz = qhtfsn.cbryxxList[12];
													qhtfsn.cbryxxContent.mxtsjbkbje = qhtfsn.cbryxxList[14];
													qhtfsn.cbryxxContent.tqrq = qhtfsn.cbryxxList[25];
													qhtfsn.cbryxxContent.ybkh = qhtfsn.ybkxxList[0];
													qhtfsn.cbryxxContent.yllbMz = qhtfsn.ybkxxList[24];
													//不显示的
													qhtfsn.cbryxxContent.nnjbylybje = qhtfsn.cbryxxList[13];
													qhtfsn.cbryxxContent.zybcdyzt = qhtfsn.cbryxxList[15];
													qhtfsn.cbryxxContent.dedyzt = qhtfsn.cbryxxList[16];
													qhtfsn.cbryxxContent.cbdtcqbh = qhtfsn.cbryxxList[17];
													qhtfsn.cbryxxContent.cbdfzx = qhtfsn.cbryxxList[18];
													qhtfsn.cbryxxContent.shbzkkh = qhtfsn.cbryxxList[19];
													qhtfsn.cbryxxContent.xzlx = qhtfsn.cbryxxList[20];
													qhtfsn.cbryxxContent.qtsm = qhtfsn.cbryxxList[21];
													qhtfsn.cbryxxContent.scbsfbm = qhtfsn.cbryxxList[22];
													qhtfsn.cbryxxContent.tqrq = qhtfsn.cbryxxList[25];
													qhtfsn.cbryxxContent.jbylzt = newstr.split("&&")[26];
													qhtfsn.cbryxxContent.jbbm1 = "";
													qhtfsn.cbryxxContent.jbmc1 = "";
													qhtfsn.cbryxxContent.jbbm2 = "";
													qhtfsn.cbryxxContent.jbmc2 = "";
													qhtfsn.cbryxxContent.jbbm3 = "";
													qhtfsn.cbryxxContent.jbmc3 = "";
													qhtfsn.cbryxxContent.jbbm4 = "";
													qhtfsn.cbryxxContent.jbmc4 = "";
													if (rllist.length > 2) {
														qhtfsn.mxbstr = rslmsg.split("@$")[2];
														var list = qhtfsn.mxbstr.split("&&");
														for (var i = 0; i < list.length; i + 3) {
															var mxb = {
																bzlb: list[i],
																bxbm: list[i + 1],
																bxmc: list[i + 2],
															}
															if (i == 3) {
																qhtfsn.cbryxxContent.jbbm1 = list[i + 1];
																qhtfsn.cbryxxContent.jbmc1 = list[i + 2];
															}
															if (i == 6) {
																qhtfsn.cbryxxContent.jbbm2 = list[i + 1];
																qhtfsn.cbryxxContent.jbmc2 = list[i + 2];
															}
															if (i == 9) {
																qhtfsn.cbryxxContent.jbbm3 = list[i + 1];
																qhtfsn.cbryxxContent.jbmc3 = list[i + 2];
															}
															if (i == 12) {
																qhtfsn.cbryxxContent.jbbm4 = list[i + 1];
																qhtfsn.cbryxxContent.jbmc4 = list[i + 2];
															}
															qhtfsn.mxblist.push(mxb);
														}
													}
													qhtfsn.cbryxxContent = Object.assign({}, qhtfsn.cbryxxContent);
													console.log(qhtfsn.cbryxxContent);
													rs = false;
													ifok = false;
												} else {
													clearInterval(interval3);
													malert(rslmsg, 'top', 'defeadted');
													rs = false;
													ifok = false;
													malert("医保错误！", 'top', 'defeadted');
													return
												}
											}
											if (cs3 >= 10) {
												malert("省平台连接失败！请重试", 'top', 'defeadted');
												rs = false;
												ifok = false;
												clearInterval(interval3);
											}
										}, 3000);
									} else {
										rs = false;
										ifok = false;
										clearInterval(interval2);
										malert("医保错误！", 'top', 'defeadted');
									}
								}
								if (cs2 >= 10) {
									malert("省平台连接失败！请重试", 'top', 'defeadted');
									rs = false;
									ifok = false;
									clearInterval(interval2);
								}
							}, 3000);
						} else {
							clearInterval(interval1);
							malert(qhtfsn.ybkpassword = rel[1], 'top', 'defeadted');
							ifok = false;
							rs = false;
							malert("省平台连接失败！请重试", 'top', 'defeadted');
						}
					} else {
						rs = false;
						ifok = false;
						clearInterval(interval1);
						malert("医保错误！", 'top', 'defeadted');
					}
				}
				if (cs1 >= 10) {
					malert("失败！请重试", 'top', 'defeadted');
					clearInterval(interval1);
					rs = false;
					ifok = false;
				}
			}, 3000);
			//    		}
			//    		response=false;

		},*/
		testYb: function () {
			var testparm = {
				SIN1: 'H0428',
				SIN2: "100",
				SIN3: "",
				SIN4: "",
			}
			socket.send(JSON.stringify(testparm));
		},
		bulidConn: function () {
			console.log("初始化");
			//var host = "ws://**************:13003/";
			//    		var host="ws://*************:13003/";
			var host = "ws://localhost:13003/";
			if (socket == null || socket == undefined) {
				socket = new wsImpl(host);
			} else {

			}
			try {
				socket.onerror = function () {
					socket.close();
					console.log("发生错误！");
				};
				socket.onopen = function () {
					console.log("连接开启！");
				};
				socket.onclose = function () {
					socket.close();
					console.log("连接关闭！");
				};
				socket.onmessage = function (evt) {
					rs = true;
					console.log("消息传送正常！");
					var msg = eval('(' + evt.data + ')');
					console.log(msg);
					rslmsg = msg.mess;
					if (rslmsg.indexOf("OK") > -1 || !isNaN(rslmsg)) {
						ifok = true;
						qhtfsn.bxcw = false;
					} else {
						ifok = false;
						qhtfsn.bxcw = true;
					}
					console.log(rs);
					console.log(ifok);
				};
			} catch (ex) {
				console.log("异常！");
				socket.close();
			}
		},
		getbxlb: function () {
			var param = {
				bxlbbm: "04"
			};
			$.getJSON(
				"/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" +
				JSON.stringify(param),
				function (json) {
					if (json.a == 0) {
						if (json.d.list.length > 0) {
							qhtfsn.bxlbbm = json.d.list[0].bxlbbm;
							qhtfsn.bxurl = json.d.list[0].url;
						}
					} else {
						malert("保险类别查询失败!" + json.c, 'top', 'defeadted')
					}
				});
		},
		getYbcs: function () {
			var param = {
				page: 1,
				rows: 30,
				sort: "yljgbm",
				order: "asc"
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + qhtfsn.bxurl + "&bxlbbm=" + qhtfsn.bxlbbm + "&types=cssz&method=query&parm=" + JSON.stringify(param), function (json) {
				if (json.a == "0") {
					var res = eval('(' + json.d + ')');
					var jkcslist = res.list;
					qhtfsn.ybcsContent = jkcslist[0];
				}
			});
		},
		mzbbzResultChange: function (val) {
			qhtfsn.cbryxxContent.mxbbz = val[0];
			qhtfsn.cbryxxContent.mxbbzmc = val[4];

		}
	}
});
qhtfsn.getbxlb();
//setTimeout(function(){
//	qhtfsn.testYb();
//},1000)
setTimeout(function () {
	qhtfsn.getYbcs()
}, 500)
