<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检验结果合并</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="jyjghb.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-shh paddr-r5" @click="selCheck">审核</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="refresh">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">申请日期</label>
                    <div class="zui-input-inline zui-select-inline zui-date">
                        <i class="datenox icon-rl"></i>
                        <input type="text" name="phone" class="zui-input todate padd-l33" v-model="apply" placeholder="请选择申请日期">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full" style="background: #fff">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <!--<th z-fixed="left" z-style="text-align:center; width:50px;" z-width="50px">-->
                        <!--<div class="zui-table-cell">  <input-checkbox @result="reCheckBox" :list="'jsonList'"-->
                                        <!--:type="'all'" :val="isCheckAll">-->
                        <!--</input-checkbox></div>-->
                    <!--</th>-->
                    <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                           :type="'all'" :val="isCheckAll">
                    </input-checkbox></div></th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>病员姓名</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>检验项目</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>样本号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>样本序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>类型</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>性别</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>送检科室</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>送检医师</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>床位号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>申请日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>执行设备</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    <!--<th z-field="username"   z-width="60px" z-style="text-align:center;">-->
                        <!--<div class="zui-table-cell">序号</div>-->
                    <!--</th>-->
                    <!--<th z-field="sex" z-width="100px">-->
                        <!--<div class="zui-table-cell">病员姓名</div>-->
                    <!--</th>-->
                    <!--<th z-field="sexs" z-width="100px">-->
                        <!--<div class="zui-table-cell">检验项目</div>-->
                    <!--</th>-->
                    <!--<th z-field="city" z-width="80px">-->
                        <!--<div class="zui-table-cell">样本号</div>-->
                    <!--</th>-->
                    <!--<th z-field="sign" z-width="100px">-->
                        <!--<div class="zui-table-cell">样本序号</div>-->
                    <!--</th>-->
                    <!--<th z-field="experience" z-width="100px">-->
                        <!--<div class="zui-table-cell">类型</div>-->
                    <!--</th>-->

                    <!--<th z-field="classify" z-width="60px">-->
                        <!--<div class="zui-table-cell">性别</div>-->
                    <!--</th>-->
                    <!--<th z-field="classifyr" z-width="100px">-->
                        <!--<div class="zui-table-cell">送检科室</div>-->
                    <!--</th>-->
                    <!--<th z-field="classifys" z-width="100px">-->
                        <!--<div class="zui-table-cell">送检医师</div>-->
                    <!--</th>-->
                    <!--<th z-field="wealth" z-width="100px">-->
                        <!--<div class="zui-table-cell">床位号</div>-->
                    <!--</th>-->
                    <!--<th z-field="wealthq" z-width="100px">-->
                        <!--<div class="zui-table-cell">申请日期</div>-->
                    <!--</th>-->
                    <!--<th z-field="wealthqw" z-width="100px">-->
                        <!--<div class="zui-table-cell">执行设备</div>-->
                    <!--</th>-->
                    <!--<th z-width="100px" z-fixed="right" z-style="text-align:center;">-->
                        <!--<div class="zui-table-cell">操作</div>-->
                    <!--</th>-->
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table">
            <table class="zui-table" >
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jsonList" @click="dblclick($index)"  @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <!--<td width="50px">-->
                        <!--<div class="zui-table-cell">-->
                            <!--<input-checkbox @result="reCheckBox" :list="'jydjList'"-->
                            <!--:type="'some'" :which="$index"-->
                            <!--:val="isChecked[$index]">-->
                            <!--</input-checkbox>-->
                        <!--</div>-->
                    <!--</td>-->
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jydjList'" :type="'some'" :which="$index"  :val="isChecked[$index]">
                         </input-checkbox>
                        </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.brxm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jyxmmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bbbh"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jyxh"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="jydjyblx_tran[item.yblx]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="brxb_tran[item.xb]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ksmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.sqysxm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cwh"></div></td>
                    <td><div class="zui-table-cell cell-s">{{item.sqrq|formDate}}</div></td>
                    <td><div class="zui-table-cell cell-s"><select class="zui-input" style="height: 24px;">
                        <option v-text="item.zxsbmc">执行设备</option>
                    </select></div></td>
                    <td class="cell-s"><div class="zui-table-cell cell-s"><span class="flex-center padd-t-5"><i class="icon-shy icon-font"></i></span></div></td>
                    <!--<td width="60px"  @click="dblclick($index)"><div class="zui-table-cell" v-text="$index+1"></div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell" v-text="item.brxm"></div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell" v-text="item.jyxmmc"></div></td>-->
                    <!--<td width="80px"><div class="zui-table-cell" v-text="item.bbbh"></div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell title" v-text="item.jyxh" data-title="item.jyxh"></div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell title" v-text="jydjyblx_tran[item.yblx]" data-title="jydjyblx_tran[item.yblx]"></div></td>-->
                    <!--<td width="60px"  @click="dblclick($index)"><div class="zui-table-cell" v-text="brxb_tran[item.xb]"></div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell" v-text="item.ksmc"></div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell" v-text="item.sqysxm"></div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell jizheng" v-text="item.cwh"></div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell" >{{item.sqrq|formDate}}</div></td>-->
                    <!--<td width="100px"  @click="dblclick($index)"><div class="zui-table-cell">-->
                        <!--<select class="zui-input" style="height: 24px;">-->
                        <!--<option v-text="item.zxsbmc">执行设备</option>-->
                    <!--</select></div></td>-->
                    <!--<td width="100px"><div class="zui-table-cell">-->
                        <!--<i class="icon-shy icon-font"></i>-->
                    <!--</div></td>-->
                </tr>



                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>


</div>
<!--侧边窗口-->
<div class="side-form ng-hide" style="width:805px;padding-top: 0;"  id="brzcList" role="form">
    <div class="tab-message" style="padding: 0;">
        <div class="tab-a">
            <a class="active">申请人信息</a>
            <a>图形信息</a>
        </div>
        <a href="javascript:;" class="fr closex ti-close Closeshow" @click="AddClose"></a>
    </div>
    <!--信息编辑-->
    <div class="tab-box">
        <div class="tab-box-list" style="display: block;overflow-y: scroll;height:87vh">
            <ul class="tab-edit-list">
                <li>
                        <i>病员类型</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="jydjlx_tran[listOne.lx]">门诊</option>
                        </select>
                </li>
                <li>
                        <i>门诊号</i>
                        <input type="text" class="label-input readonly"  v-model="listOne.bah" disabled/>
                </li>
                <li>
                        <i>患者姓名</i>
                        <input type="text" class="label-input readonly"  v-model="listOne.brxm" disabled/>
                </li>
                <li>
                        <i>患者性别</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="brxb_tran[listOne.xb]">男</option>
                        </select>
                </li>
                <li>
                        <i>患者年龄</i>
                        <input type="text" class="label-input readonly"  v-model="listOne.nl" disabled/>
                </li>
                <li>
                        <i>送检科室</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="listOne.ksmc">信息科</option>
                        </select>
                </li>
                <li>
                        <i>送检医师</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="listOne.sqysxm">信息科</option>
                        </select>
                </li>
                <li>
                        <i>床位号</i>
                        <input type="text" class="label-input readonly"  v-model="listOne.cwh" disabled/>
                </li>
                <li>
                        <i>临床诊断</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="listOne.lczd">腹痛待查</option>
                        </select>
                </li>
                <li>
                        <i>检验项目</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="listOne.jyxmmc">生化全项</option>
                        </select>
                </li>
                <li>
                        <i>样本类型</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="listOne.ybmc">生化全项</option>
                        </select>
                </li>
                <li>
                        <i>检验类型</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="jydjyblx_tran[listOne.yblx]">生化全项</option>
                        </select>
                </li>
                <li>
                        <i>质控类型</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="jydjzklx_tran[listOne.zklx]">生化全项</option>
                        </select>
                </li>
                <li>
                        <i>样本编号</i>
                        <input type="text" class="label-input readonly"  v-model="listOne.bbbh" disabled/>
                </li>
                <li>
                        <i>申请时间</i>
                        <em class="label-em fa-calendar"></em>
                        <input type="text" class="label-input readonly"  :value="listOne.sqrq|formDate" disabled/>
                </li>
                <li>
                        <i>扣费金额</i>
                        <input type="text" class="label-input readonly"  v-model="listOne.fyje" disabled/>
                </li>
                <li>
                        <i>条码号</i>
                        <input type="text" class="label-input readonly" v-model="listOne.jyxh" disabled/>
                </li>
                <li>
                        <i>执行设备</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="listOne.zxsbmc">执行设备</option>
                        </select>
                </li>
                <li>
                        <i>体检单位</i>
                        <select class="edit-select readonly" disabled>
                            <option v-text="listOne.tjdw">体检单位</option>
                        </select>
                </li>
                <li>
                        <i>采样时间</i>
                        <em class="label-em fa-calendar"></em>
                        <input type="text" class="label-input readonly"  :value="listOne.cyrq|formDate" disabled/>
                </li>
                <li>
                        <i>核收时间</i>
                        <em class="label-em fa-calendar"></em>
                        <input type="text" class="label-input readonly" :value="listOne.ybhsrq|formDate" disabled/>
                </li>
                <li>
                        <i>上机时间</i>
                        <em class="label-em fa-calendar"></em>
                        <input type="text" class="label-input readonly"  :value="listOne.sjsj|formDate" disabled/>
                </li>
                <li style="width:65%;margin-right: 0;">
                        <i>备注</i>
                        <input type="text" class="label-input readonly"  :value="listOne.bz" style="width: 100%" disabled/>
                </li>
            </ul>
            <div class="tab-table">
                <div class="layui-layer-content" >
                    <ul class="pop_jiaofeo tab-suoxie">
                        <li>序号</li>
                        <li>英文缩写</li>
                        <li>检测项目</li>
                        <li>检测结果</li>
                        <li>状态</li>
                        <li>参考区间</li>
                        <li>样本号</li>
                    </ul>
                    <div class="tab-tables">
                        <ul v-for="(item, $index) in zbList" class="pop_jiaofeo pop-jiaofei-height tab-suoxie" style="border-top: none">
                            <li v-text="$index+1"></li>
                            <li v-text="item.zbxmpy"></li>
                            <li v-text="item.zbxmmc"></li>
                            <li><i class="tab-gl" :class="item.zt==1?'color-gg':item.zt==-1?'color-blue':''" v-text="item.jcjg"><em v-text="item.dw"></em></i></li>
                            <li><i class="tab-red" :class="item.zt==1?'color-gg':item.zt==-1?'color-blue':''" v-text="item.zt==1?'↑':item.zt==-1?'↓':''"></i></li>
                            <li v-text="item.ckz_t"></li>
                            <li><input class="tab-gls" value=""  @change="zbchange($index,$event)" ></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="tab-confirm" id="confirm">

                <button class="zui-btn table_db_esc btn-default" @click="closes">取消</button>
                <button class="zui-btn btn-primary table_db_save" @click="confirms">合并</button>

            </div>

        </div>
        <div class="tab-box-list">2</div>
    </div>

</div>
<div id="pop">
    <!--<transition name="pop-fade">-->
    <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
    <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
        <div class="layui-layer-content" >
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
        </div>
    </div>
    <!--</transition>-->
</div>


<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="jyjghb.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>