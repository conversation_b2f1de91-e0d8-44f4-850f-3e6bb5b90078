.xmzb-top {
  width: 100%;
  padding: 0 34px 15px 0;
  overflow: hidden;
}
.xmzb-top-left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.xmzb-top-left i {
  margin-right: 5px;
}
.xmzb-top-left i:nth-child(2) {
  margin-right: 19px;
}
.xmzb-content {
  width: 100%;
  padding: 10px 0;
  box-sizing: border-box;
  float: left;
  min-width: 1024px;
}
.xmzb-content-left {
  width: 25%;
  float: left;
  margin-left: 10px;
}
.xmzb-content-left .content-left-top {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  border: 1px solid #e9eee6;
  background: #edf2f1;
  height: 36px;
  line-height: 36px;
  align-items: center;
  text-indent: 30px;
}
.xmzb-content-left .content-left-list {
  width: 100%;
  height: 89vh;
  overflow: auto;
  border-top: none;
  border-right: none;
}
.xmzb-content-left .content-left-list li {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
  height: 53px;
  border: 1px solid #e9eee6;
  border-top: none;
  text-indent: 30px;
}
.xmzb-content-left .content-left-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-content-left .content-left-list li:hover {
  background: rgba(26, 188, 156, 0.08);
}
.xmzb-content-right {
  width: 73%;
  float: right;
  margin-right: 10px;
}
.xmzb-content-right .content-right-top {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  border: 1px solid #e9eee6;
  background: #edf2f1;
  height: 36px;
  align-items: center;
}
.xmzb-content-right .content-right-top i {
  width: calc((100% / 9));
  text-align: center;
}
.xmzb-content-right .content-right-list {
  width: 100%;
  height: 83vh;
  overflow: auto;
  border-top: none;
}
.xmzb-content-right .content-right-list li {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 53px;
  border: 1px solid #e9eee6;
  cursor: pointer;
  position: relative;
  line-height: 53px;
}
.xmzb-content-right .content-right-list li i {
  width: calc((100% -145px)/8);
  text-align: center;
  line-height: 53px;
}
.xmzb-content-right .content-right-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-content-right .content-right-list li:hover {
  background: rgba(26, 188, 156, 0.08);
}
.xmzb-title {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: #edf2f1;
  border: 1px solid #e9eee6;
  height: 34px;
}
.xmzb-title i {
  width: calc((100% / 8));
  text-align: center;
}
.xmzb-list {
  width: 100%;
  max-height: 320px;
  overflow: auto;
}
.xmzb-list li {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  line-height: 54px;
  border-top: 1px solid #e9eee6;
}
.xmzb-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-list li:first-child {
  border-top: none;
}
.xmzb-list i {
  width: calc((100% /8));
  text-align: center;
}
.xmzb-list .sbtdh-width {
  width: auto;
  max-width: 83px;
  border: 1px solid #1abc9c;
  border-radius: 4px;
  height: 34px;
  margin: 0 auto;
}
.xmzb-list .sbtdh-input {
  width: 100%;
  border: none;
  background: none;
  height: 30px;
  margin-top: -24px;
  text-align: center;
}
.font16 {
  font-size: 16px !important;
}
.xmzb-ok {
  width: 100%;
  height: 70px;
  border-top: 1px solid #e9eee6;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.xmzb-ok button {
  margin-right: 15px;
}
.font-icon {
  position: absolute;
  right: 90px;
  top: 3px;
  color: rgba(255, 255, 255, 0.8);
}
#jyxm_icon .switch {
  top: -6px;
  left: 58px;
}
