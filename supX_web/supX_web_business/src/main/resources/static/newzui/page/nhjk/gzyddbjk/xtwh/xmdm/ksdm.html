<div id="ksdm">
    <div class="ks_toolMenu">
        <button @click="getData()"><span class="fa fa-refresh"></span>刷新</button>
        <!--  
        <button @click="save"><span class="fa fa-save"></span>保存</button>
        -->
        <button @click="remove()"><span class="fa fa-trash-o"></span>删除</button>
        <button @click="autoDm()"><span class="fa fa-save"></span>自动对码（项目名称）</button>
        <span style="margin-left: 20px">检索项目：</span>
        <input type="text" style="width: 200px;" @keyDown="sschangeDown" id="search"/>
    </div>
    <div class="tableDiv ksbmXm">
        <table class="patientTable patientTableCzy" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;z-index: 200">
            <tr>
                <th style="min-width: 84px">保险类别编码</th>
                <th style="min-width: 120px">保险类别名称</th>
            </tr>
            </thead>
            <tr>
                <th style="min-width: 84px"></th>
                <th style="min-width: 120px"></th>
            </tr>
            <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                :class="[{'tableTrSelect': isChecked[$index]},{'tableTr': $index%2 == 0}]">
                <td v-text="item.bxlbbm"></td>
                <td v-text="item.bxlbmc"></td>
            </tr>
        </table>
    </div>

    <div class="tableDiv ksbmMx">
        <table class="patientTable patientTableCzy" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;z-index: 200">
            <tr>
                <th style="min-width: 100px">科室名称</th>
                <th style="min-width: 100px">科室编码</th>
                <th style="color: #1FD273;min-width: 100px">农合科室名称</th>
                <th style="color: #1FD273;min-width: 100px">农合科室编码</th>
            </tr>
            </thead>
            <tr>
                <th style="min-width: 100px">科室名称</th>
                <th style="min-width: 100px">科室编码</th>
                <th style="min-width: 100px">农合科室名称</th>
                <th style="min-width: 100px">农合科室编码</th>
            
            </tr>
            <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                :class="[{'tableTrSelect': isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
                <td v-text="item.ksmc"></td>
                <td v-text="item.ksbm"></td>
                 <td>
                    <span v-show="isEdit != $index" v-text="item.nhksmc"></span>
                    <input :id="'mc_'+$index" v-show="isEdit == $index" v-model="item.nhksmc" @input="searching($index,false,'nhksmc')" @keyDown="changeDown($index,$event,'text')">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </td>
                <td v-text="item.nhksbm"></td>
            </tr>
        </table>
    </div>
</div>
<script type="application/javascript" src="ksdm.js"></script>