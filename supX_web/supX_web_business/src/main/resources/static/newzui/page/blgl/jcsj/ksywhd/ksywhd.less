@import "../../../../css/baseColor";
.icon-bj:before{
  top:4px;
  width: 20px;
  height: 16px;
}
#jyxm_icon .switch{
  left:12% !important;
  top:-23% !important;
}
.tab-edit-list li label .label-input{
  text-indent: 10px !important;
}
.ksys-side{
  span{
    i{
      line-height: 30px;
    }
    margin-bottom: 15px !important;
  }
}
.border-dotted-t{
  border-top: 1px dashed @color1a;
}
.fl{
  float: left;
}
.zui-select-inline{
  margin-right: 0 !important;
}
.ksywhd-left{
  width: 220px;
  float: left;
  position: relative;
 .ksywhd-top{
   float: left;
   width: 100%;
   height: 31px;
   display: flex;
   justify-content: flex-start;
   align-items: center;
   position: absolute;
   left: 0;
   padding: 0 0 0 20px;
   top: 48px;
   z-index: 11;
   background: @colorff;
   border: 1px solid @coloree;
  .ksywhd-title{
    padding-left:10px;
    cursor: pointer;

  }
   &.active{
     background: @colorRgb01;
     color: @color1a;
   }
 }
  .ksywhd-ej{
    width: 100%;
    float: left;
    margin-top:35px;
    padding-top: 10px;
    border: 1px solid @coloree;
    overflow: auto;
    border-top: none;
    li{
      padding-top: 10px;
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 42px;
      box-sizing: border-box;

    }
  }

}
.ksywhd-right{
  width: 539px;
  float: right;
  position: relative;
  .right-top{
    width: 100%;
    background:@colored;
    height: 36px;
    line-height: 36px;
    position: absolute;
    top: 48px;
    left: 0;
    i{
      display: block;
      float: left;
      text-align: center;
      width:calc(~"(100% - 50px) / 5");
      &:first-child{
        width: 50px !important;
      }
    }
  }
  .right-list{
    margin-top:35px;
    width: 100%;
    overflow: auto;
    li{
      width: 100%;
      line-height: 40px;
      height: 40px;
      overflow: hidden;
      border: 1px solid @coloree;
      border-top: none;
      i{
        display: block;
        float: left;
        text-align: center;
        width:calc(~"(100% - 50px) / 5");
        &:first-child{
          width: 50px !important;
        }
      }
    }
  }
}
.zui-form-label{
  width: auto !important;
}
.zui-form .zui-inline{
  padding: 0 20px 0 37px;
}
