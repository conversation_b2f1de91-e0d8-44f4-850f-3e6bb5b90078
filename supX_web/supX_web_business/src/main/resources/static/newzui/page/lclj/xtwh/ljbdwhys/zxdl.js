var panel = new Vue({
    el: '.panel',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        param: {
            page: '',
            rows: '',
            total: '',
            parm:'',
        }
    },
    methods: {
        addData: function () {
            brzcList.title = '新增执行大类'
            brzcList.index = 1
        },
        edit: function () {

        },
        removeItem: function () {
            if(zuiTable.isChecked.length==0){
                malert("请选择需要删除的项目",'top','defeadted');
            }else{
                zuiTable.remove()
            }
        },
        getData: function () {

        }
    },
})
var zuiTable = new Vue({
    el: ".zui-table-h",
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        jsonList: [],

    },
    created:function () {
        this.getData()
    },
    methods: {
        edit: function (index) {
            brzcList.title = '编辑执行大类',
                brzcList.index = 1
            brzcList.popContent=this.jsonList[index]
        },
        remove: function (item) {
            var zhfymxList=[]
            if(this.isChecked.length>0){
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i]==true){
                        zhfymxList.push(this.jsonList[i])
                    }
                }
            }else{
                zhfymxList.push(item)
            }
            var json = '{"list":' + JSON.stringify(zhfymxList) + '}';
            this.$http.post("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysZxdl&types=deletezxdl&", json).then(function(data) {
                if(data.body.a == 0) {
                    zuiTable.getData();
                    this.popContent = {};
                } else {
                    malert("数据提交失败",'top','defeadted');
                }
            }, function(error) {
                console.log(error);
            });
        },
        getData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysZxdl&types=queryzxdl"+JSON.stringify(panel.param), function (json) {
                if (json.a == '0') {
                    if(json.d!=null){
                        zuiTable.zlFajsonList = json.d.list;
                    }else{
                        malert("暂无最新数据 ",'top','defeadted');
                    }
                }else{
                    malert(json.c,'top','defeadted');
                }
            });
        }
    },
})
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        popContent: {},
        index:'0',
        title: ''
    },
    methods: {
        closes:function () {

        },
        saveData:function () {
            this.$http.post("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysZxdl&types=savezxdl&", this.popContent).then(function(data) {
                if(data.body.a == 0) {
                    zuiTable.getData();
                    this.popContent = {};
                } else {
                    malert("数据提交失败",'top','defeadted');
                }
            }, function(error) {
                console.log(error);
            });
        }
    },
})