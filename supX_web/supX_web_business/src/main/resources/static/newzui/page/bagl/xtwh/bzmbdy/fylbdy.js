(function(){
    //改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
    //统筹类别
    var tableInfo = new Vue({
        el: '#fylbdy',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            jsonList: []
        },
        methods : {
        	//初始化页面加载列表
            getData: function () {
            	this.param.rows=20000;
            	var json={
            		tybz:0
            	};
            	$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmFylb&types=query&dg="+JSON.stringify(this.param)+"&json"+JSON.stringify(json),function (json) {
                   if(json.d!=null){
                    	tableInfo.jsonList = json.d.list;
                    	tableInfo.isCheckAll = false;
           				tableInfo.checkAll();//调用全选
                   }
        		});
            },
            
          	//保存
          	saveData: function(){
          		var fylbList = [];
          		for (var i = 0; i < this.isChecked.length; i++) {
                	if (this.isChecked[i] == true) {
                		fylbList.push(this.jsonList[i]);
                	}
                }
          		var json = '{"list":'+JSON.stringify(fylbList)+'}';
                this.$http.post('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmFylb&types=updateBetch&',
                		json).then(function (data) {
                            if(data.body.a == 0){
                                malert("保存成功");
                                tableInfo.getData();
                            } else {
                                malert("保存失败");
                            }
                        }, function (error) {
                            console.log(error);
                });
          	},
        }
    });
    
    //列表
    tableInfo.getData();
  

    //为table循环添加拖拉的div
    var drawWidthNumFylb = $(".patientTableFylb tr").eq(0).find("th").length;
    for(var i=0;i<drawWidthNumFylb;i++){
        if(i>=2){
            $(".patientTableFylb th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
        }
    }

})();