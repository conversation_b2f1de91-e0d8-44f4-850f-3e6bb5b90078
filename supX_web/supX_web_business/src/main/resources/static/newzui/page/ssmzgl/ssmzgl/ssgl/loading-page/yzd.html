    <link href="yzd.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
<div id="yzd" class="flex-container flex-one  flex-dir-c margin-t-10" >
    <div id="toolMenu_yzd" class=" flex-align-c printHide flex-container">
        <div @click="long(0)" class="cursor height-34 " :class="{'yzd_select': which==0}">长&nbsp;&nbsp;期</div>
        <div @click="short(1)" class="cursor height-34 padd-l-10 padd-r-10" :class="{'yzd_select': which==1}">临&nbsp;&nbsp;时</div>
        <button class="tong-btn btn-parmary padd-l-10"  @click="doPrint(false)">打印</button>
        <button class="tong-btn btn-parmary-f2a" @click="doPrint(true)">续打</button>
<!--        <select-input class="wh120" @change-data="resultChange"-->
<!--                      :child="qsxzList" :index="'yexm'" :index_val="'yebh'" :val="popContent.yebh"-->
<!--                      :name="'popContent.yebh'" :index_mc="'yexm'" search="true" id="yebh">-->
<!--        </select-input>-->
    </div>

    <div class="over-auto">
        <div v-cloak class="cqyzd printHide" v-if="isShow">
            <div class="yzdTitle" :class="{'goPrintHide': isGoPrint}">长期医嘱单</div>
            <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
                <div>
                    <span>科别:</span>
                    <span>{{BrxxJson.ryksmc}}</span>
                </div>
                <div>
                    <span>床号:</span>
                    <span>{{BrxxJson.rycwbh}}</span>
                </div>
                <div>
                    <span>姓名:</span>
                    <span>{{BrxxJson.brxm}}</span>
                </div>
                <div>
                    <span>性别:</span>
                    <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                </div>
                <div>
                    <span>年龄:</span>
                    <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                </div>
                <div>
                    <span>住院号:</span>
                    <span>{{BrxxJson.zyh}}</span>
                </div>
            </div>

            <div class="yzd-table">
                <table cellspacing="0" cellpadding="0">
                    <tr :class="{'goPrintHide': isGoPrint}">
                        <th colspan="2">开始</th>
                        <th rowspan="2">执行<br>时间</th>
                        <th rowspan="2" style="width: 320px">长期医嘱</th>
                        <th colspan="2">签名</th>
                        <th colspan="2">停止</th>
                        <th rowspan="2">停止<br>执行<br>时间</th>
                        <th colspan="2">签名</th>
                    </tr>
                    <tr :class="{'goPrintHide': isGoPrint}">
                        <th>日<br>月</th>
                        <th>时<br>间</th>
                        <th>医师</th>
                        <th>护士</th>
                        <th>日<br>月</th>
                        <th>时<br>间</th>
                        <th>医师</th>
                        <th>护士</th>
                    </tr>
                    <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                        @click="goPrint($index)">
                        <td v-text="sameDate('ksrq', $index, 'ry')"></td>
                        <td v-text="sameDate('ksrq', $index, 'sj')"></td>
                        <td v-text="sameDate('zxsj', $index, 'sj')"></td>
                        <td class="flex-container">
                            <span class="yzd-name" v-text="item.xmmc"></span>
                            <span :class="[{'sameStart': sameSE($index) == 'start'},  {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                            <p>
                                <span class="yzd-way" v-show="isShowItem($index)" v-text="item.yyffmc"></span>
                                <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                            </p>
                        </td>
                        <td  v-text="item.ysqmxm"></td>
                        <td  v-text="item.zxhsxm"></td>
                        <td v-text="sameDate('ystzsj', $index, 'ry')"></td>
                        <td v-text="sameDate('ystzsj', $index, 'sj')"></td>
                        <td v-text="sameDate('hstzsj', $index, 'sj')"></td>
                        <td v-text="item.tzysxm"></td>
                        <td v-text="item.tzhsxm"></td>
                    </tr>
                </table>
            </div>

            <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                <div class="yzd-ysInfo">
                    <div>
                        <span>主管医生:</span>
                        <span>{{BrxxJson.zyysxm}}</span>
                    </div>
                    <div>
                        <span>护士:</span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="cqPrint">
            <!--<transition name="pop-fade">-->
            <!--:style="index|compuGd()"-->
            <div v-show="isShow"  class="background-f">
                <div  :style="index|compuGd()"  v-for="(itemList, index) in list" >
                    <div class="yzdTitle" >长期医嘱单</div>
                    <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                        <div>
                            <span>科别:</span>
                            <span>{{BrxxJson.ryksmc}}</span>
                        </div>
                        <div>
                            <span>床号:</span>
                            <span>{{BrxxJson.rycwbh}}</span>
                        </div>
                        <div>
                            <span>姓名:</span>
                            <span>{{BrxxJson.brxm}}</span>
                        </div>
                        <div>
                            <span>性别:</span>
                            <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                        </div>
                        <div>
                            <span>年龄:</span>
                            <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                        </div>
                        <div>
                            <span>住院号:</span>
                            <span>{{BrxxJson.zyh}}</span>
                        </div>
                    </div>

                    <div class="yzd-table">
                        <table cellspacing="0" cellpadding="0">
                            <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                <th colspan="2">开始</th>
                                <th rowspan="2">执行<br>时间</th>
                                <th rowspan="2" style="width: 320px">长期医嘱</th>
                                <th colspan="2">签名</th>
                                <th colspan="2">停止</th>
                                <th rowspan="2">停止<br>执行<br>时间</th>
                                <th colspan="2">签名</th>
                            </tr>
                            <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                <th>日<br>月</th>
                                <th>时<br>间</th>
                                <th>医师</th>
                                <th>护士</th>
                                <th>日<br>月</th>
                                <th>时<br>间</th>
                                <th>医师</th>
                                <th>护士</th>
                            </tr>
                            <tr v-for="(item, $index) in itemList" :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]">
                                <td v-text="sameDate('ksrq', $index, index, 'ry')"></td>
                                <td v-text="sameDate('ksrq', $index, index, 'sj')"></td>
                                <td v-text="sameDate('zxsj', $index, index, 'sj')"></td>
                                <td  class="flex-container">
                                    <span class="yzd-name" v-text="item.xmmc"></span>
                                    <span :class="[{'sameStart': sameSE($index, index) == 'start'},{'sameEnd': sameSE($index, index) == 'end'},{'same': sameSE($index, index) == 'all'}]"></span>
                                    <p>
                                        <span class="yzd-way" v-show="isShowItem($index)" v-text="item.yyffmc"></span>
                                        <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                    </p>
                                </td>
                                <td  v-text="item.ysqmxm"></td>
                                <td  v-text="item.zxhsxm"></td>
                                <td v-text="sameDate('ystzsj', $index, index, 'ry')"></td>
                                <td v-text="sameDate('ystzsj', $index, index, 'sj')"></td>
                                <td v-text="sameDate('hstzsj', $index, index, 'sj')"></td>
                                <td v-text="item.tzysxm"></td>
                                <td v-text="item.tzhsxm"></td>
                            </tr>
                        </table>
                    </div>

                    <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                        <div class="yzd-ysInfo">
                            <div>
                                <span>主管医生:</span>
                                <span>{{BrxxJson.zyysxm}}</span>
                            </div>
                            <div>
                                <span>护士:</span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                    <div class="text-center" v-text="'第  ' + (index + 1) + '  页'"></div>
                </div>
            </div>
            <!--</transition>-->
        </div>

        <div v-cloak class="lsyzd printHide" v-if="isShow">
            <div class="yzdTitle" :class="{'goPrintHide': isGoPrint}">临时医嘱单</div>
            <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
                <div>
                    <span>科别:</span>
                    <span>{{BrxxJson.ryksmc}}</span>
                </div>
                <div>
                    <span>床号:</span>
                    <span>{{BrxxJson.rycwbh}}</span>
                </div>
                <div>
                    <span>姓名:</span>
                    <span>{{BrxxJson.brxm}}</span>
                </div>
                <div>
                    <span>性别:</span>
                    <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                </div>
                <div>
                    <span>年龄:</span>
                    <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                </div>
                <div>
                    <span>住院号:</span>
                    <span>{{BrxxJson.zyh}}</span>
                </div>
            </div>

            <div class="yzd-table">
                <table cellspacing="0" cellpadding="0">
                    <tr :class="{'goPrintHide': isGoPrint}">
                        <th colspan="2">吩咐时间</th>
                        <th rowspan="2" style="width: 445px">临时医嘱</th>
                        <th rowspan="2">医师签名</th>
                        <th rowspan="2">执行<br>时间</th>
                        <th rowspan="2">执行者签名</th>
                    </tr>
                    <tr :class="{'goPrintHide': isGoPrint}">
                        <th>日<br>月</th>
                        <th>时<br>间</th>
                    </tr>
                    <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                        @click="goPrint($index)">
                        <td v-text="sameDate('ksrq', $index, 'ry')"></td>
                        <td v-text="sameDate('ksrq', $index, 'sj')"></td>
                        <td class="flex-container">
                            <span class="yzd-name" v-text="item.xmmc"></span>
                            <span :class="[{'sameStart': sameSE($index) == 'start'}, {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                            <p>
                                <span class="yzd-way" v-show="isShowItem($index)" v-text="item.yyffmc"></span>
                                <span style="margin-left: 20px;width: auto" v-show="item.psjg != '无'">&nbsp;&nbsp;(&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;)</span>
                                <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                <span class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}</span>
                            </p>
                        </td>
                        <td v-text="item.ysqmxm"></td>
                        <td v-text="fDate(item.zxsj,'shortY')"></td>
                        <td v-text="item.zxhsxm"></td>
                    </tr>
                </table>
            </div>

            <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                <div class="yzd-ysInfo">
                    <div class="padd-r-40" style="margin-right: 40px">
                        <span>主管医生:</span>
                        <span></span>
                    </div>
                    <div>
                        <span>护士:</span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="lsPrint">
            <transition name="pop-fade">
                <div v-show="isShow" class="background-f">
                    <div class="popCenter " :style="index|compuGd()"  v-for="(itemList, index) in list" >
                        <div class="yzdTitle" >临时医嘱单</div>
                        <!--<button @click="print">打印</button>-->
                        <!--<button @click="goOnPrint">续打</button>-->
                        <!--<button @click="isShow = false">取消</button>-->
                        <!--<div class="yzdTitle" :class="{'goPrintHide': isGoPrint && pagePrint == index}">临时医嘱单</div>-->
                        <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                            <div>
                                <span>科别:</span>
                                <span>{{BrxxJson.ryksmc}}</span>
                            </div>
                            <div>
                                <span>床号:</span>
                                <span>{{BrxxJson.rycwbh}}</span>
                            </div>
                            <div>
                                <span>姓名:</span>
                                <span>{{BrxxJson.brxm}}</span>
                            </div>
                            <div>
                                <span>性别:</span>
                                <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                            </div>
                            <div>
                                <span>年龄:</span>
                                <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                            </div>
                            <div>
                                <span>住院号:</span>
                                <span>{{BrxxJson.zyh}}</span>
                            </div>
                        </div>

                        <div class="yzd-table">
                            <table cellspacing="0" cellpadding="0">
                                <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                    <th colspan="2">吩咐时间</th>
                                    <th rowspan="2" style="width: 445px">临时医嘱</th>
                                    <th rowspan="2">医师签名</th>
                                    <th rowspan="2">执行<br>时间</th>
                                    <th rowspan="2">执行者签名</th>
                                </tr>
                                <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                    <th>日<br>月</th>
                                    <th>时<br>间</th>
                                </tr>
                                <tr v-for="(item, $index) in itemList" :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]">
                                    <td v-text="sameDate('ksrq', $index, index, 'ry')"></td>
                                    <td v-text="sameDate('ksrq', $index, index, 'sj')"></td>
                                    <td class="flex-container">
                                        <span class="yzd-name" v-text="item.xmmc"></span>
                                        <span :class="[{'sameStart': sameSE($index, index) == 'start'},{'sameEnd': sameSE($index, index) == 'end'},{'same': sameSE($index, index) == 'all'}]"></span>
                                        <p>
                                            <span class="yzd-way" v-show="isShowItem($index)" v-text="item.yyffmc"></span>
                                            <span style="margin-left: 20px;width: auto" v-show="item.psjg != '无'">&nbsp;&nbsp;(&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;)</span>
                                            <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                            <span class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}</span>
                                        </p>
                                    </td>
                                    <td v-text="item.ysqmxm"></td>
                                    <td v-text="fDate(item.zxsj,'shortY')"></td>
                                    <td v-text="item.zxhsxm"></td>
                                </tr>
                            </table>
                        </div>

                        <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                            <div class="yzd-ysInfo">
                                <div>
                                    <span>主管医生:</span>
                                    <span></span>
                                </div>
                                <div>
                                    <span>护士:</span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                        <div class="text-center" v-text="'第  ' + (index + 1) + '  页'"></div>
                    </div>
                </div>
            </transition>
        </div>
    </div>
</div>
<script type="application/javascript" src="yzd.js"></script>
