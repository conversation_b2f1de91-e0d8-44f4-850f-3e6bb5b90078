var cd_014 = new Vue({
    el: '.cd_014',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        skr_tran: {
            '本人': '本人',
            '父母': '父母',
            '子女': '子女',
            '配偶': '配偶',
            '同胞兄弟姐妹': '同胞兄弟姐妹',
            '外祖父母': '外祖父母',
        },
        isShow: false,
        bxlbbm: null,
        bxurl: null,
        birthday: null,
        text: null,
        jbContent: {},
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'yke120',
            '疾病名称': 'yke121',
            '副编码': 'yke223'
        },
        brzt_tran: {
            '1': '在院',
            '2': '未在院'
        },
        zdxxJson: {},
        grxxJson: {
            aka130: "0201",
			rysfgz:'1',
        },
        cssg: false,
        userInfo: {},
        ifclick: true,
        cd_ykc117_tran: {
            '1': '公务员',
            '0': '非公务员 ',
        },
        cdydbz_tran: {
            '1': '本地',
            '2': '省内异地 ',
            '3': '省外异地'
        },
        cd_aka130_tran: {
            '0101': '药店购药',
            '0102': '城居药店支付',
            '0201': '普通门诊',
            '0202': '特殊疾病门诊',
            '0203': '城居普通门诊',
            '0204': '城居门诊特殊病',
            '0205': '城职门诊统筹',
            '0206': '城乡门诊统筹',
            '0207': '酪氨酸城职',
            '0208': '酪氨酸城乡',
            '0209': '大学生门诊',
            '0301': '普通住院',
            '0302': '家庭病床',
            '0304': '市外转诊住院(异地急救参照市外转诊)',
            '0305': '统筹区内转院',
            '0306': '外伤住院',
            '0307': '异地抢救住院(手工报销使用)',
            '0308': '本市非定点抢救住院(手工报销使用)',
            '0309': '城居普通住院',
            '0310': '城居外伤住院',
            '0311': '城居市外转诊',
            '0401': '机关事业单位生育报销',
            '0501': '工伤门诊',
            '0502': '工伤住院',
            '0504': '工伤康复治疗住院',
            '0602': '城居生育分娩住院',
            '0603': '城居产前检查',
            '0604': '城职生育住院',
            '13': '异地门特（就医地）',
        },
		insutype_tran:{
			'310':'职工基本医疗保险',
			'320':'公务员医疗补助',
			'330':'大额医疗费用补助',
			'340':'离休人员医疗保障',
			'390':'城乡居民基本医疗保险',
			'392':'城乡居民大病医疗保险',
			'510':'生育保险',
		},
        gsdataset_tran: [],
        fylist: [],
        datasetyka026_tran: [],
        gsdataset_show: false,
        datasetyka026_show: false,
        lxxx_show: false,
        ryfbmc: '',
        isdzps: false,//是否电子医保凭证
        pushResult: false,//单子医保凭证推送结果
        yinHaiRequest: {//用于声明银海请求对象，与字段对应关系
            //门诊交易（11）
            
        },
		dtxx:false,
		idetinfo:[],
		isZxChecked:[],
		isZxCheckAll:false,
		syxx:null,
		sfgzList:{
			'0':'否',
			'1':'是'
		},
		sfydList:{
			'0':'否',
			'1':'是'
		},
		sfyd:'0',
		mtbzlist:[],
    },
    created: function () {
        this.init();
    },
    mounted: function () {
        this.isShow = true;
        this.getbxlb();
        this.getUserInfo();
    },
    methods: {
        mtbzhq:function(){
            let ryxx = JSON.parse(sessionStorage.getItem('hzybxx'));
            let param5301 = {
                "data" :{
                    "psn_no":cd_014.grxxJson.aac001, //人员编号
                }
            }
            let data5301 = window.insuranceGbUtils.call1("5301",param5301,cd_014.grxxJson.insuplc_admdvs,cd_014.sfyd)
            if(!data5301){
                malert("获取备案信息失败", 'right', 'defeadted');
                return false;
            }else{
                if(typeof(data5301) =='boolean'){
                    malert("该人员未进行门特备案", 'right', 'defeadted');
                    return false;
                }else if(data5301.feedetail && data5301.feedetail.length>0){

                    this.mtbzlist = data5301.feedetail;
                    malert("获取成功", 'right', 'success');
                }else{
                    malert("该人员未进行门特备案", 'right', 'defeadted');
                    return false;
                }

            }
        },
        init: function () {
            window.insuranceGbUtils.init();
        },
        closeGz_002: function () {
            this.isShow = false;
            $("#hyjl").html("");
        },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    cd_014.userInfo = json.body.d;
                });
        },
        getbxlb: function () {
            var that = this;
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    that.bxlbbm = json.d.list[0].bxlbbm;
                    that.bxurl = json.d.list[0].url;
					// that.bxurl='http://172.20.103.68:10015/interface/cdyhyb/post'
                } else {
                    malert("保险类别查询失败!" + json.c, 'right', 'defeadted')
                }
            });
        },
        commonResultChange: function (val) {
            var type = val[2][1];
            switch (type) {
                case "yke109":
                    Vue.set(this.grxxJson, type, val[0]);
                    Vue.set(this.grxxJson, "alc022", val[4]);
                    Vue.set(this.grxxJson, "aka130", cd_014.listGetName(cd_014.gsdataset_tran, val[0], 'yke109', 'aka130'));
                    break;
                case "bkc014":
                    Vue.set(this.grxxJson, type, val[0]);
                    Vue.set(this.grxxJson, "bkc117", val[4]);
                    break;
            }
        },
        qdFun: function () {
            //签到
            window.insuranceGbUtils.qd();
        },
        loadqd: function () {
            window.insuranceGbUtils.qd();
        },
        //读卡
        load: function () {

            /*if(!cd_014.ifclick){
                malert("请勿重复点击！","right","defeadted");
                return;
            }*/
            cd_014.ifclick = false;
			window.insuranceGbUtils.qd();
			window.insuranceGbUtils.init();
            if (window.insuranceGbUtils.initStatus) {
				
				
				let ret1 =  window.insuranceGbUtils.call('1101','',cd_014.sfyd,contextInfo.userInfo,"01101");
				if(ret1){
					let ryxx =  window.insuranceGbUtils.ryxx(cd_014.sfyd);
					
					this.syxx = ryxx;
					this.insuinfo = ryxx.insuinfo
					if(ryxx.insuinfo.length>=1){
						this.dtxx = true;
					}else{
						this.dtxx = false;
					}
					
				}
            } else {
                cd_014.ifclick = true;
                malert("医保控件未初始化,请重新打开页面！", 'right', 'defeadted');
            }
        },
		//确定使用人员信息
		saveZxXx:function(){
					let param = [];
					for (var i = 0; i < this.isZxChecked.length; i++) {
							if(this.isZxChecked[i]){
								param.push(this.insuinfo[i])
							}
						}
					console.log(param)
					if(param){
						cd_014.grxxJson.aac001 = cd_014.syxx.baseinfo.psn_no
						cd_014.grxxJson.aac003 = cd_014.syxx.baseinfo.psn_name
						cd_014.grxxJson.aac004 = cd_014.syxx.baseinfo.gend
						cd_014.grxxJson.akc023 = cd_014.syxx.baseinfo.age
						cd_014.grxxJson.aac002 = cd_014.syxx.baseinfo.certno
						
						cd_014.grxxJson.aac006 = cd_014.syxx.baseinfo.brdy
						cd_014.grxxJson.ykc303 = param[0].insutype
						console.log(cd_014.insutype_tran[param[0].insutype]);
						cd_014.grxxJson.ykc194 = param[0].balc
						cd_014.grxxJson.ykc177 = param[0].cvlserv_flag
						cd_014.grxxJson.psn_type = param[0].psn_type
						cd_014.grxxJson.psn_insu_stas = param[0].psn_insu_stas
						cd_014.grxxJson.psn_insu_date = param[0].psn_insu_date
						cd_014.grxxJson.paus_insu_date = param[0].paus_insu_date
						cd_014.grxxJson.insuplc_admdvs = param[0].insuplc_admdvs
						cd_014.grxxJson.emp_name = param[0].emp_name
						this.dtxx = false;
					}
					
				},
				
		reZxCheckChange: function (val) {
			this.isZxChecked = []
					        for (var i = 0; i < this.insuinfo.length; i++) {
		            if (!val[2]) {
		                Vue.set(this.isZxChecked, val[1], val[2]);
		            } else {
		                Vue.set(this.isZxChecked, val[1], val[2]);
		            }
		        }
		},
        //引入
        enter: function () {
            if (Object.keys(cd_014.grxxJson).length === 0) {
                malert("请先读卡", "right", "defeadted");
                return;
            }
            if (!cd_014.grxxJson.aka130) {
                malert("请选择支付类型", "right", "defeadted");
                return;
            }
            			//个人信息
				contextInfo.type='06';
				contextInfo.json=Object.assign(contextInfo.json,cd_014.grxxJson);
				contextInfo.json.brxm=cd_014.grxxJson.aac003;
				contextInfo.json.brxb=cd_014.grxxJson.aac004;
				contextInfo.json.brnl=cd_014.grxxJson.akc023;
				contextInfo.json.nldw='1';
				contextInfo.json.sfzjhm=cd_014.grxxJson.aac002;
				contextInfo.json.gzdw=cd_014.grxxJson.aab004;
				contextInfo.ybbx='B07';
				if(cd_014.grxxJson.ykc303 =='340'){
					contextInfo.json.lxgh='1';
					contextInfo.json.fbbm='37';
				}else{
					contextInfo.json.lxgh='0';
					contextInfo.json.fbbm='01';
				}
				contextInfo.json.bxlbbm='02';
			   contextInfo.bxShow = false;
			   malert("引入成功！","right","success");
			   contextInfo.setCsrq()
			  contextInfo.setAge()
        },
        getFy: function () {
            //处理费用
            var fylist = [];
            var brfyList = [];
            var fyze = 0.00;
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                if (rightVue.brfyjsonList[i].fydj > 0) {
                    var fyparam = {};
                    fyparam.fyid = new Date().getTime() + i;
                    fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                    fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                    fyparam.fysl = String(rightVue.brfyjsonList[i].fysl);

                    fyparam.yka097 = rightVue.brfyjsonList[i].mzks;
                    fyparam.yka098 = rightVue.brfyjsonList[i].mzksmc;
                    fyparam.ykf008 = rightVue.brfyjsonList[i].mzys;
                    fyparam.yka099 = rightVue.brfyjsonList[i].mzysxm;
                    fyparam.yke123 = rightVue.brfyjsonList[i].sfsj || new Date();
                    fyparam.akc225 = String(rightVue.brfyjsonList[i].fydj);

                    brfyList.push(fyparam);
                    fyze += rightVue.brfyjsonList[i].fyje;
                }
            }
            var requestParameters = '{"list":' + JSON.stringify(brfyList) + '}';
            this.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=queryMzfy", requestParameters, function (json) {
                if (json.a == '0') {
                    fylist = eval('(' + json.d + ')');
                } else {
                    malert(json.c, 'right', 'defeadted');
                    return false;
                }
            });
            
        },
        mzyjs014V3: function (brfyjsonList) {
                        var fylist = [];
            var brfyList = [];
            for (var i = 0; i < brfyjsonList.length; i++) {
                if (brfyjsonList[i].fydj > 0) {
                    var fyparam = {};
                    fyparam.fyid = new Date().getTime() + i;
                    fyparam.mxfyxmbm = brfyjsonList[i].mxfyxmbm;
                    fyparam.yzlx = brfyjsonList[i].yzlx;
                    fyparam.yzhm = brfyjsonList[i].yzhm;
                    fyparam.fysl = String(brfyjsonList[i].fysl);
                    fyparam.yka097 = brfyjsonList[i].mzks;
                    fyparam.yka098 = brfyjsonList[i].mzksmc;
                    fyparam.ykf008 = brfyjsonList[i].mzys;
                    fyparam.yka099 = brfyjsonList[i].mzysxm;
                    fyparam.yke123 = brfyjsonList[i].sfsj || new Date();
                    fyparam.akc225 = String(brfyjsonList[i].fydj);
                    if (typeof (brfyjsonList[i].identity) != "undefined") {
                        fyparam.identity = brfyjsonList[i].identity;
                    }
                    brfyList.push(fyparam);
                }
            }
            var requestParameters = '{"list":' + JSON.stringify(brfyList) + '}';
            this.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=queryMzfy", requestParameters, function (json) {
                if (json.a == '0') {
                    fylist = eval('(' + json.d + ')');
                } else {
                    malert(json.c, 'top', 'defeadted');
                    return false;
                }
            });


            if (!fylist || fylist.length <= 0) {
                malert("没有可结算费用！", 'top', 'defeadted');
                return false;
            }
            this.ryfbmc = '全费';
            
            //去除数量为0 的
            fylist = fylist.filter(function (x) {
                return x.akc226 != 0;
            })
            //查找有医嘱号的
            let yke134List = [];
            fylist.forEach(x => {
                if (typeof (x.yke134) != "undefined") {
                    yke134List.push(x);
                }
            });

            //查找有医嘱号的
            let identityList = [];
            fylist.forEach(x => {
                if (typeof (x.identity) != "undefined") {
                    identityList.push(x);
                }
            });
            //通过医嘱号分组
            let groupYke134 = this.toGroupBy(yke134List, function (item) {
                return [item.yke134];
            });

            //通过identity分组
            let groupIdentity = this.toGroupBy(identityList, function (item) {
                return [item.identity];
            });
            let groupList = groupYke134.concat(groupIdentity);
			return groupList;
            
        },
        
        /**
         * 是否离休患者
         * @param baseInfo 患者基本信息（03）
         * @returns {boolean}
         */
        isLxPatient: function (baseInfo) {
            try {
                if (typeof (baseInfo.lxdataset.yke109) != "undefined") {
                    return baseInfo.lxdataset.yke109 == '1';
                }
            } catch (e) {
                return false
            }
            return false;
        },
        /**
         *
         * @param c 控制对象
         * @param item 费用明细
         * @param jybh 机构编码
         * @returns {{jybh, jysr_xml: string, jykz_xml: string}}
         */
        toYinHaiRequestModel: function (c, item, jybh) {
                        //判读是否是市医疗保险
            let isCityInsurance = (typeof (cd_014.grxxJson.yab003) != "undefined" && cd_014.grxxJson.yab003 != '0090' && cd_014.grxxJson.yab003.length == 4);
            let header = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            let controlXml = '', mxRow = '', yzXml = '', ykd018Xml = '',
                yka065Xml = '';
            //control 控制参数|mx 费用明细|yz 医嘱|ykd018 疾病列表|yka065 共济相关
            let control = null, mx = null, yz = null, ykd018 = null, yka065 = null;
            if (isCityInsurance) {
                control = this.yinHaiRequest.code11.control;
                mx = this.yinHaiRequest.code11.data.dataSetMx;
                ykd018 = this.yinHaiRequest.code11.data.datalxzd;
                yz = this.yinHaiRequest.code11.data.dataSetYz;

            } else {
                control = this.yinHaiRequest.provinceCode11.control;
                mx = this.yinHaiRequest.provinceCode11.data.row;
                ykd018 = this.yinHaiRequest.provinceCode11.ykd018dataset;
                // yka065 = this.yinHaiRequest.provinceCode11.data.datayka065; //共济账户信息
            }

            // 控制
            for (let property in control) {
                var tempXml = '<' + property + '>' + this.emptyReplace(c[control[property]]) + '</' + property + '>';
                controlXml += tempXml;
            }

            //费用明细
            for (let i = 0; i < item.length; i++) {
                let rows = "<row "
                for (let property in mx) {
                    let value = this.emptyReplace(item[i][mx[property]]);
                    //yka094 ,yke134最单长度不可超过15
                    if (property == 'yke134' && value.length > 15) {
                        value = value.substr(value.length - 15);
                    }
                    //特殊处理日期格式
                    if (property == 'aae036' || property == 'yke123') {
                        value = cd_014.fDate(value, 'datetime');
                    }
                    //成都市医保且为离休病人 ，以下字段如果为null   给默认值
                    if (this.isLxPatient(cd_014.grxxJson) && isCityInsurance) {

                        if (property == 'yke352' || property == 'yke654' || property == 'yke350' || property == 'yke446') {
                            value = (value || 1);
                        }
                        if (property == 'yke351' || property == 'yke655') {
                            value = (value || '次');
                        }
                    }

                    // if (property == 'aae013') {
                    //     value = "建筑医院测试用例";
                    // }
                    rows += property + '="' + value + '" '
                }
                rows += "/> "
                mxRow += rows;
            }


            if (rightVue.isNotNullOrEmpty(yz)) {
                //医嘱,直接取item第一个对象即可，因为是按照医嘱做的结算
                for (let property in yz) {
                    let value = this.emptyReplace(item[0][yz[property]]);
                    //yka094 ,yke134最单长度不可超过15
                    if (property == 'yke134' && value.length > 15) {
                        value = value.substr(value.length - 15);
                    }
                    let tempXml = '<' + property + '>' + value + '</' + property + '>';
                    yzXml += tempXml;
                }
                yzXml = '<row>' + yzXml + '</row>';
            }
            if (rightVue.isNotNullOrEmpty(ykd018)) {
                ykd018Xml = "<row>" +
                    "<ykd018>" + this.emptyReplace(rightVue.mzjbxxContent.jbbm) + "</ykd018>" +
                    "<yke122>" + this.emptyReplace(rightVue.mzjbxxContent.jbmc) + "</yke122>" +
                    "</row>";

                var fjzd = rightVue.mzjbxxContent.fjzd;
                if (fjzd && Array.isArray(JSON.parse(fjzd))) {
                    let arr = JSON.parse(fjzd)
                    for (let i = 0; i < arr.length; i++) {
                        ykd018Xml += "<row>" +
                            "<ykd018>" + this.emptyReplace(arr.jbmb) + "</ykd018>" +
                            "<yke122>" + this.emptyReplace(arr.jbmc) + "</yke122>" +
                            "</row>";
                    }
                }
            }
            if (rightVue.isNotNullOrEmpty(yka065)) {
                for (let property in yka065) {
                    var tempXml = '<' + property + '>' + this.emptyReplace(c[control[property]]) + '</' + property + '>';
                    yka065Xml += tempXml;
                }
            }
                        let reuslt = null;
            if (isCityInsurance) {
                reuslt = {
                    'jybh': jybh,
                    'jykz_xml': header + '<control>' + controlXml + '</control>',
                    'jysr_xml': header + '<data>' + '<datasetmx>' + mxRow + '</datasetmx>' + '<datasetyz>' + yzXml + '</datasetyz>' + '<datalxzd>' + ykd018Xml + '</datalxzd>' + '</data>'
                }
            } else {
                reuslt = {
                    'jybh': jybh,
                    'jykz_xml': header + '<control>' + controlXml + '<ykd018dataset>' + ykd018Xml + '</ykd018dataset>' + '</control>',
                    // 'jysr_xml': header + '<data>' + mxRow + '<datayka065>' + yka065Xml + '</datayka065>' + '</data>'
                    'jysr_xml': header + '<data>' + mxRow + '</data>'
                }
            }

            return reuslt;
        },
        /**
         * 医保结算
         * @param control
         * @param item
         * @returns {{success: boolean}}
         */
        ybjs: function (control, item) {
                        let result = {
                isCancel: false,
                request: item
            };
            $.ajaxSettings.async = false;

            let data = this.toYinHaiRequestModel(control, item, '11');
            if (cd_014.isdzps == true) {
                var parame = {
                    user: this.userInfo.czyxm,
                    jgbm: "0000",
                    mzjs_kz_xml: data.jykz_xml,
                    mzjs_data_xml: data.jysr_xml,
                    uid: uuid()
                }
                result.response = this.pushClinetV2(parame)
            } else {
                                $.post("http://localhost:10014/call", data, function (json) {
                    console.log("json" + JSON.stringify(json));
                    result.response = json;
                });
            }
                        return result;
        },
        /**
         * 异地医保结算
         * @param fylist
         * @param fyzh
         */
        ydybjs: function (fylist, fyzh) {
            let result = {
                success: false
            };
            let mzyjsResponseList = [];
            for (let i = 0; i < fylist.length; i++) {
                let fy = fylist[i];
                fy.edition = '5.0';
                fy.yke550 = '1';
                fy.fyzh = cd_014.MathFun(fyzh);
                fy.nums = fylist.length;
                fy.akc193 = rightVue.mzjbxxContent.jbbm;
                fy.bkc020 = rightVue.mzjbxxContent.jbmc;
                fy.aae011 = cd_014.userInfo.czyxm;
                let data = this.toYinHaiRequestModel(fylist[i], 'YD11');
                //调用结算方法
                $.ajaxSettings.async = false;
                $.post("http://localhost:10014/call", data, function (json) {
                    if (json.aint_appcode > 0) {
                        mzyjsResponseList.push(json);
                    }
                });
                rightVue.yjsContentGzyhyb.ifok = true;
            }
            result.success = true;
            result.msg = 'success';
            result.data = mzyjsResponseList;
            return result;
        },
        emptyReplace: function (para) {
            if (para == null || typeof (para) == "undefined") {
                return '';
            }
            return para;
        },
        toGroupBy: function (array, fn) {
                        const groups = {};
            array.forEach(function (item) {
                const group = JSON.stringify(fn(item));
                //这里利用对象的key值唯一性的，创建数组
                groups[group] = groups[group] || [];
                groups[group].push(item);
            });


            //最后再利用map循环处理分组出来
            return Object.keys(groups).map(function (group) {
                return groups[group];
            });
        },
        
        sleep: function (delay) {
                        var start = (new Date()).getTime();
            let end = (new Date()).getTime();
            while ((end - start) < delay) {
                end = (new Date()).getTime()
            }
            return true;
        },
        test: function () {
            this.ybjs();
        },
        test1: function () {
                        var url = "http://localhost:10014/getYbPay?uid=1";
            $.post(url, {}, function (res) {
                if (res.aint_appcode > 0) {
                    var output = JSON.parse(res.astr_jysc_xml);
                    rightVue.yjsContentGzyhyb = {
                        ybfyhj: cd_014.MathFun(0),
                        ghxh: rightVue.mzjbxxContent.ghxh,
                        akc190: output.akc190,    //'就诊编码';
                        aka130: output.aka130,    //'支付类别';
                        ykd007: output.ykd007,    //'报销类型';
                        aac001: output.aac001,    //'个人编码';
                        akb020: output.akb020,    //'医院编码';
                        yka103: output.yka103,    //'结算编号';
                        yka055: output.yka055,    //'费用总额';
                        yka056: output.yka056,    //'全自费';
                        grzhye: output.grzhye.row,    //'个人账户余额';
                        ykc303: output.grzhye.row.ykc303,    //'个人帐户种类';
                        ykc194: output.grzhye.row.ykc194,    //'个人帐户余额';
                        yka065: output.yka065,    //'个人帐户支付总额';
                        yka107: output.yka107,    //'社保基金支付总额';
                        ykh012: output.ykh012,    //'现金及其他自付';
                        yab003: output.yab003,    //'医保经办机构编号';
                        aae011: output.aae011, //经办人姓名
                        aae036: output.aae036, //经办时间
                        yka111: output.yka111, //符合范围
                        yka057: output.yka057, //挂钩自付
                        jslx: output.aka130,//结算类型
                        ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
                        qslbmx: output.dataset.row
                    },
                        rightVue.jylsh = res.astrJylsh;
                    rightVue.jyyzm = res.astrJyyzm;
                    rightVue.yjsContentGzyhyb.ifok = true;
                }
            });
        },
        decodeUnicode: function (str) {
            //先把十六进制unicode编码/u替换为%u
            str = str.replace(/\\u/gi, '%u');
            //再把页面中反斜杠替换为空
            str = str.replace(/\\/gi, '');
            return unescape(str);
        }
    }
});


$(document).click(function () {
    if (this.className != 'selectGroup') {
        $(".selectGroup").hide();
    }
    $(".popInfo ul").hide();
});
