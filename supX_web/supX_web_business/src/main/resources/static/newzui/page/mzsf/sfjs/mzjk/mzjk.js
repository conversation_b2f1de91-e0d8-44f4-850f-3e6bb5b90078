
var dqsyh = null;
var ryrq=getTodayDateTime();
/********************************华丽分割线***************************************/
    //列表展示
var tableInfo = new Vue({
        el: '#tableInfo',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
        	fyqkContent:{}, //费用情况对象
			csqxContent:{
                N05001200609:"0",
                N05001200610:"0",
            },
            jsonList: [],
            isShow: true,
            jkzt: true,
            ztType: 'ryrq',
            ksbm:'',
            gxhj:{
        	    fyhj:0,
                ybkzf:0,
                xjzf:0,
            },
            param: {
                jkbm:'2',
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc'
            },
        },
        //页面渲染完成之后加载数据
        mounted: function () {
            changeWin();
            //初始化检索日期！为今天0点到今天24点
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()),'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(),'date')+' 23:59:59';
            //入院登记查询列表 时间段选择器
            laydate.render({
                elem: '#timeVal',
                type: 'datetime',
                value: this.param.beginrq,
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                        tableInfo.param.beginrq = value;
                    //获取一次列表
                    tableInfo.getData();
                }
            });
            laydate.render({
                elem: '#timeVal1',
                value: this.param.endrq,
                type: 'datetime',
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                        tableInfo.param.endrq = value;//+' 23:59:59';
                    //获取一次列表
                    tableInfo.getData();
                }
            });
//            this.getData();
        },
        computed:{
            gxhjFun:function () {
                this.gxhj={
                    fyhj:0,
                    ybkzf:0,
                    xjzf:0,
                }
                for (var i = 0; i <this.isChecked.length ; i++) {
                    if(this.isChecked[i]){
                        this.gxhj.fyhj=this.jsonList[i].fyhj+this.gxhj.fyhj
                        this.gxhj.ybkzf=this.jsonList[i].ybkzf+this.gxhj.ybkzf
                        this.gxhj.xjzf=this.jsonList[i].xjzf+this.gxhj.xjzf
                    }
                }
                return this.gxhj
            }
        },
        methods: {
            resultChangeData:function(val){
                Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                if (val[3] != null) {
                    Vue.set(this[val[2][0]], val[3], val[4]);
                }
                this.goToPage(1)
            },
            reCheckBoxPrint:function(val){
                if(this.jsonList[val[1]].jkpzh==undefined){
                    malert('请先门诊交款，再勾选打印框','top','defeadted');
                    return false;
                }else{
                    Vue.set(this.isChecked, val[1], val[2]);
                }
            },
            rejkCheckBox: function (val) {
                var that = this
                if (val[1] !== 'all') this.activeIndex = val[0];
                if (val[0] == 'some') {
                    Vue.set(this.isChecked, val[1], val[2]);
                    if (that.notempty(this.isChecked).length == this[val[3]].length) {
                        this.isChecked.every(function (el) {
                            if (el === true) {
                                return that.isCheckAll = true
                            } else {
                                return that.isCheckAll = false
                            }
                        })
                    }
                    console.log(this.isChecked)
                } else if (val[0] == 'one') {
                    this.isCheckAll = false;
                    this.isChecked = [];
                    Vue.set(this.isChecked, val[1], val[2]);
                } else if (val[0] == 'all') {
                    this.isCheckAll = val[2];
                    console.log(this.isCheckAll);
                    if (val[1] == null) val[1] = "jsonList";
                    if (this.isCheckAll) {
                        for (var i = 0; i < this[val[1]].length; i++) {
                            //正常交款单才允许选中
                            if (this[val[1]][i].jkzt == "0") {
                                Vue.set(this.isChecked, i, true);
                            }
                        }
                    } else {
                        this.isChecked = [];
                    }
                }
            },
            print:function(){
                syjjInfo.print()
            },
			getCsqx:function(){
            // 先获取到科室编码
            var ksparm = {"ylbm": 'N050012006'};
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                if (json.a == 0 && json.d) {
                        // 获取参数权限
                        var parm = {"ylbm": 'N050012006', "ksbm": json.d[0].ksbm};
                        tableInfo.ksbm = json.d[0].ksbm;
                        $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0) {
                                if (json.d.length > 0) {
                                    for (var i = 0; i < json.d.length; i++) {
                                        var csjson = json.d[i];
                                        switch (csjson.csqxbm) {
                                            //收费员交款权限
                                            case "N05001200601"://收费员交款权限
                                                if (csjson.csz) {
                                                    tableInfo.csqxContent.N05001200601 = parseInt(csjson.csz);
                                                    syjjInfo.csqxContent.N05001200601 = parseInt(csjson.csz);
                                                }
                                                break;
                                            case"N05001200602"://收费员取消交款
                                                if (csjson.csz) {
                                                    tableInfo.csqxContent.N05001200602 = csjson.csz;
                                                    syjjInfo.csqxContent.N05001200602 = csjson.csz;
                                                }
                                                break;
                                            //财务室财务上交权限
                                            case "N05001200603" :    // 财务:财务上交权限
                                                if (csjson.csz) {
                                                    tableInfo.csqxContent.N05001200603 = csjson.csz;
                                                    syjjInfo.csqxContent.N05001200603 = csjson.csz;
                                                }
                                                break;
                                            case "N05001200604" :    // 财务:取消财务上交权限
                                                if (csjson.csz) {
                                                    tableInfo.csqxContent.N05001200604 = csjson.csz;
                                                    syjjInfo.csqxContent.N05001200604 = csjson.csz;
                                                }
                                                break;
                                            case "N05001200605" :    // 是否允许退其他人记录
                                                if (csjson.csz) {
                                                    tableInfo.csqxContent.N05001200605 = csjson.csz;
                                                    syjjInfo.csqxContent.N05001200605 = csjson.csz;
                                                }
                                                break;
                                            case "N05001200607" :    // 默认交款截至时间 0-当前时间；1-当天24点
                                                if (csjson.csz) {
                                                    if (csjson.csz == '1'){
                                                        syjjInfo.sfsj = getTodayDateEnd();
                                                    }else {
                                                        syjjInfo.sfsj =getTodayDateCurrent();
                                                    }
                                                }
                                                break;
                                            case "N05001200608" :    // 是否有查其它人的交款单权限
                                                if (csjson.csz !=undefined) {
                                                    tableInfo.csqxContent.N05001200608 = csjson.csz;
                                                }
                                                break;
                                            case "N05001200609" :    // 交款单打印方式
                                                if (csjson.csz !=undefined) {
                                                    tableInfo.csqxContent.N05001200609 = csjson.csz;
                                                }
                                                break;
                                             case "N05001200611" :    // 门诊交款单汇总打印方式权限
                                                if (csjson.csz !=undefined) {
                                                    tableInfo.csqxContent.N05001200610 = csjson.csz;
                                                }
                                                break;
                                            case "N05001200612" :    // 门诊交款单汇总打印方式权限
                                                if (csjson.csz !=undefined) {
                                                    tableInfo.csqxContent.N05001200612 = csjson.csz;
                                                }
                                                break;
                                            default:
                                                break;
                                        }
                                    }
                                }

								tableInfo.getData();//列表信息
                            } else {
                                malert("参数权限获取失败!"+json.c,'top','defeadted');
                            }
                        });
                } else {
                    malert("权限科室获取失败!"+json.c,'top','defeadted');
                }
            });
        },
            ShowMz:function () {
                tableInfo.getMzjk();
                tableInfo.isShow = false;
                syjjInfo.isShow = true;
                syjjInfo.jkshow = true;
                syjjInfo.$nextTick(function () {
                    syjjInfo.DateMouted()
                })
            },
            //检索获取交款基本信息
            getMzjk: function () {
                var jkrq = syjjInfo.sfsj;//获取到检索时间
                //检索内容为就清空所有数据
                if (jkrq == "") {
                    syjjInfo.popContent = {};//总体费用信息
                    syjjInfo.brfbList = [];//病人费别
                    syjjInfo.fylbList = [];//费用项目
                    zd_enter.mxfyList = []; //门诊费用明细
                    return;
                }
                //请求后台查询基本信息
                var parm = {
                    jkrq: jkrq
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=queryCwjkMsg&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        syjjInfo.popContent = json.d;//总的费用情况
                        syjjInfo.popContent.jkje = tableInfo.fDec(json.d.jkje, 2);
                        Vue.set(syjjInfo.popContent, 'jkjedx', numToCn(syjjInfo.popContent['jkje'])); //转换成大写金额
                        Vue.set(syjjInfo.popContent, 'fyhj', zd_enter.fDec(syjjInfo.popContent['fyhj'], 2,''));
                        var kssj = syjjInfo.fDate(syjjInfo.popContent.kssj, "datetime");
                        Vue.set(syjjInfo.popContent, 'kssj', kssj);
                        syjjInfo.brfbList = json.d.ryfbList; //病人费别
                        syjjInfo.fylbList = json.d.fylbList; //费用项目
                    } else {
                        malert('费用查询失败:'+json.c,'top','defeadted');
                    }
                });

                //请求后台查询发票信息
                this.getFphm(jkrq);
                //请求后台获取医疗机构名称
                this.getYljg();
            },
            //交款前根据日期请求后台获取到发票号码段
            getFphm: function (jkrq) {
                var parm = {
                    cxsj: jkrq
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjkFphm&types=queryFphm&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                    	syjjInfo.fphmPopContent = json.d;//发票信息
                    } else {
                        malert('发票查询失败:'+json.c,'top','defeadted');
                    }
                });
            },
            //请求后台获取到发票号码段
            getFphmByJkpzh: function (jkpzh) {
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjkFphm&types=queryFphmByjkpzh&jkpzh=" + jkpzh, function (json) {
                    if (json.a == 0) {
                        syjjInfo.fphmPopContent = json.d;//发票信息
                    } else {
                        malert('发票查询失败:'+json.c,'top','defeadted');
                    }
                });
            },
            //请求后台查询医疗机构名称
            getYljg: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryYljg&types=queryOne&jgbm=" + jgbm, function (json) {
                    if (json.a == 0) {
                        syjjInfo.yljgmc = json.d.jgmc;//发票信息
                    } else {
                        malert('医疗机构名称查询失败:'+json.c,'top','defeadted');
                    }
                });
            },
            js:function (zyh,isShouYJJ,num) {
			    if(this.jsonList[num].czybm != userId && tableInfo.csqxContent.N05001200608 !='1'){
                    malert('你没有权限查看其他人的交款单','top','defeadted')
                    return false;
                }
                if (this.jsonList[num].jkzt == 1) {
                    malert('此记录已作废不能进行操作','top','defeadted');
                    return false;
                }
                //查询后台
                var jkpzh = tableInfo.jsonList[num].jkpzh;
                var parm={
                	jkpzh:jkpzh
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=selectByPrimaryKey&parm=" + JSON.stringify(parm), function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (json.a == 0) {
                        if (json.d != null && json.d != "") {
                        	syjjInfo.popContent = {};//总体费用信息
                        	syjjInfo.brfbList = [];//病人费别
                        	syjjInfo.fylbList = [];//费用项目
                        	zd_enter.mxfyList = []; //门诊费用明细
                        	syjjInfo.popContent = json.d;//总的费用情况
                            Vue.set(syjjInfo.popContent, 'jkjedx', numToCn(syjjInfo.popContent['jkje'])); //转换成大写金额
                            var jkrq = syjjInfo.fDate(syjjInfo.popContent.jkrq, "date");
                            Vue.set(syjjInfo.popContent, 'jkrq', jkrq);
                            syjjInfo.brfbList = json.d.ryfbList; //病人费别
                            syjjInfo.fylbList = json.d.fylbList; //费用项目
                            //查询票据信息
                            tableInfo.getFphmByJkpzh(jkpzh);
                        }
                    } else {
                        malert('查询失败:'+json.c,'top','defeadted')
                    }
                });
                this.isShow = false;
                syjjInfo.isShow = true;
                syjjInfo.jkshow = false;
                syjjInfo.isShouYJJ = isShouYJJ;
                //syjjInfo.getData(zyh);
            },
            //取消交款
            qxjkData: function (i) {
                var jkpzh = '';
                        if (tableInfo.jsonList[i]['jkzt'] == '1') {//交款单作废时不可以取消交款
                            malert('此记录已作废不能进行取消交款','top','defeadted');
                            return;
                        }
                        if (tableInfo.jsonList[i]['sjbz'] == '1') {//交款单已上交时不可以取消交款
                            malert('此记录已上交不能进行取消交款','top','defeadted');
                            return;
                        }
                        jkpzh = tableInfo.jsonList[i]['jkpzh'];
                        if (tableInfo.csqxContent.N05001200612=='1' && tableInfo.jsonList[i]['czybm'] !=userId){
                            malert('对不起,您不能作废非本人交款记录!','top','defeadted');
                            return;
                        }
                var json = {
                    jkpzh: jkpzh
                };
                if (common.openConfirm("确认取消交款吗？", function () {
                        tableInfo.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=update&', JSON.stringify(json)).then(function (data) {
                            if (data.body.a == "0") {
                                tableInfo.getData();
                                malert("取消交款成功！",'top','success')
                            } else {
                                malert('取消交款失败'+data.body.c,'top','defeadted');
                            }
                        });
                    })) {
                    return false;

                }
        //2018/07/10注释当前
                // this.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=update&', JSON.stringify(json)).then(function (data) {
                //     if (data.body.a == 0) {
                //         malert('取消交款成功','top','success');
                //         tableInfo.getData();
                //     } else {
                //         malert('取消交款失败'+data.body.c,'top','defeadted');
                //     }
                // }, function (error) {
                //     console.log(error);
                // });
            },
            /*ztTypeCB:function(val){//状态下拉框选中回调
                this.ztType = val[0];
                this.getData();
            },*/

            operateCwjk: function (i,type) {
                var message = "";
                var sjbz = "";
                if(type){//财务上交
                   message = "财务上交!";
                    sjbz = "1";
                }else{//财务上交取消
                    message = "取消上交!";
                    sjbz = "0";
                }
                if (tableInfo.jsonList[i]['jkzt'] == '1') {//交款状态为作废,不能操作
                    malert('此记录已作废不能进行' + message,'top','defeadted');
                    return;
                }
                if (tableInfo.csqxContent.N05001200612=='1' && tableInfo.jsonList[i]['czybm'] !=userId){
                    malert('对不起,您不能上交非本人交款记录!','top','defeadted');
                    return;
                }
                var jkpzh = tableInfo.jsonList[i]['jkpzh'];
                var json = {
                    jkpzh: jkpzh,
                    sjbz : sjbz,
                };
                if (common.openConfirm("确认要" + message + "吗？", function () {
                    tableInfo.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=operateCwjk&', JSON.stringify(json)).then(function (data) {
                        if (data.body.a == "0") {
                            tableInfo.getData();
                            malert(message + "成功！",'top','success')
                        } else {
                            malert(message + '失败'+ data.body.c,'top','defeadted');
                        }
                    });
                })) {
                    return false;

                }
            },
           //查询交款列表信息
            getData: function () {
                var jkzt=null;
                if(this.param.jkbm=='1'){
                    jkzt='1';
                }else if(this.param.jkbm=='2'){
                    jkzt='0';
                }
                this.param.jkzt=jkzt;
                this.param.sort = "jkrq";
                this.param.order = "desc";
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=queryCwjk&parm=" + JSON.stringify(this.param) + "", function (json) {
                    tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                    for(var i=0;i<json.d.list.length;i++){
                    	if(json.d.list[i].jkzt=='1'){
                    		json.d.list[i].jkkzt='已作废';
                    	}else if(json.d.list[i].sjbz=='1'){
                    		json.d.list[i].jkkzt='已上交';
                    	}else {
                    		json.d.list[i].jkkzt='已交款（待上交）';
                    	}
                    }
                    tableInfo.jsonList = json.d.list;
                });
                tableInfo.getJkjezh();
            },

			//获取门诊交款各金额汇总
	        getJkjezh: function () {
	            var jkzt=null;
	            if(this.param.jkbm=='1'){
	            	jkzt='1';
	            }else if(this.param.jkbm=='2'){
	            	jkzt='0';
	            }
	            this.param.jkzt=jkzt;
	            $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=queryCwjkSumJe&parm=" + JSON.stringify(this.param) + "", function (json) {
	                if(json.d){
	                	tableInfo.fyqkContent= json.d;
                        tableInfo.fyqkContent.gxhj=0;
	                }
	            });
	        },

           /* yjfButtClick: function(zyh,isShouYJJ){
                this.isShow = false;
                syjjInfo.isShow = true;
                syjjInfo.isShouYJJ = isShouYJJ;
                syjjInfo.getData(zyh);
            }*/
        },



    });
	tableInfo.getCsqx();
//请求后台获取医疗机构名称
tableInfo.getYljg();
//tableInfo.getData();

//双击费用明细列表弹框
var zd_enter = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifClick: true, //用于判断是否有点击交款按钮
        isCheck: null,

        isShow: true,
        mzjk: true, //门诊交款按钮
        popContent: {},//总体费用信息
        yljgmc: null,
        fphmPopContent: {},//发票号码信息
        brfbList: [],//病人费别
        fylbList: [],//费用项目
        mxfyList: [],//门诊费用明细
        toolMenu: true, //工具栏
        num:1,
    },
    //页面渲染完成之后加载数据
    mounted: function () {
    },
    methods: {
        closes:function () {
            this.num=1
        },
        //选中费用项目查询费用项目明细信息
        showDetail: function (index) {
            this.isCheck = index;
            //判断是否交款凭证号是否存在来决定查询哪个病人费用信息
            if (syjjInfo.popContent.jkpzh == undefined || syjjInfo.popContent.jkpzh == null || syjjInfo.popContent.jkpzh == '') {
                var parm = {
                    fylb: syjjInfo.fylbList[index]['fylb'],
                    sfsj:syjjInfo.sfsj
                };
                $.getJSON('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryByFylb' + '&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            zd_enter.mxfyList = data.d.list;
                        } else {
                            malert('查询失败' + data.c,'top','defeadted')
                        }
                    });
            } else {
                var parm = {
                    fylb:  syjjInfo.fylbList[index]['fylb'],
                    ryjkpzh: syjjInfo.popContent.jkpzh
                };
                $.getJSON('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryByFylbAndRyjkpzh' + '&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            zd_enter.mxfyList = data.d.list;
                        } else {
                            malert('查询失败' + data.c,'top','defeadted')
                        }
                    });
            }
        },
       /* print: function () {
            window.print();
        }*/
    }
});

/****************编辑页面 初始化页面加载事件 ******************/

//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});

/*********************快捷键********************************/
$(document).keydown(function (e) {
    //F2门诊收费保存
    if (e.keyCode == 113) {
        zd_enter.saveData();//保存
    }
});

//收预交金
var syjjInfo = new Vue({
    el: '#syjjInfo',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data:{
        zyh: '',
        isShow: false,
        isMzDis: true,
        sfsj:'',
        kssj:'',
        isShouYJJ: '',
        yljgmc:'',
        pageData:{},
        zflxServerList: [],
        zflxList:{},
        ifClick:true,
        PzhbrxxList:[],
        fphmPopContent:{},//发票号码对象
        popContent:{},//交款对象
        brfbList: [],//病人费别
        fylbList: [],//费用项目
        jkshow:true,
        csqxContent: {}
    },
    mounted: function(){
        //this.getZflxList();
        this.$nextTick(function () {
            syjjInfo.sfsj =getTodayDateCurrent();//this.fDate(new Date(),'date')+' 23:59:59';
        })
    },
    methods:{
        DateMouted:function () {
        	 laydate.render({
                 elem: '#timeValTwo',
                 type: 'datetime',
                 rigger: 'click',
                 max: this.fDate(new Date(),'datetime'),
                 theme: '#1ab394',
                 show:true,
                 done: function (value, data) { //回调方法
                 	if (value != '') {
                 		console.log("value:"+value);
                 		syjjInfo.sfsj = value;//+'23:59:59';
                     } else {
                     	syjjInfo.sfsj = '';
                     }
                     //获取一次列表
                     tableInfo.getMzjk();
                 }
             });
        },
        /*refresh: function(){
            this.getZflxList();
            this.getData(this.zyh);
        },
        //页面加载时自动获取支付类型Dddw数据
        getZflxList: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx", function (json) {
                if (json.a == 0) {
                    var list = json.d.list,
                        listLength = json.d.list.length;
                    for(var i = 0;i < listLength;i++){
                        syjjInfo.zflxList[list[i].zflxbm] = list[i].zflxmc;
                    }
                    syjjInfo.pageData.payType = list[0].zflxbm;
                    syjjInfo.zflxServerList = list; // 为之后的切换用户 清空操作做缓存
                } else {
                    malert(json.c,'top','defeadted');
                    return false;
                }
            });
        },
        getData: function (zyh) {
            this.zyh = zyh.jkpzh; //为之后的刷新页面操作做缓存病人的住院号
            this.getBrxxInfo(zyh.jkpzh,function(brxxInfo){
                syjjInfo.pageData.patientName = brxxInfo.brxm;
                syjjInfo.pageData.hospitalAD = brxxInfo.zyh;
                syjjInfo.pageData = brxxInfo; //缓存一下病人的基本信息   在新增预交记录的时候使用
                syjjInfo.getBryjjlList(zyh,function(yjjlList){
                    syjjInfo.$set( syjjInfo.pageData, "yjjlList", yjjlList );
                });
            });
        },*/
        /*showYjjlInfo:function (index) {
            zd_enter.showDetail(index);
            zd_enter.num=0
        },
        //查询病人基本信息
        getBrxxInfo: function(zyh,cb){
            var parm = {
                jkpzh: zyh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=selectByPrimaryKey&parm=" + JSON.stringify(parm), function (brxx) {
                if (brxx.a == 0) {
                    syjjInfo.PzhbrxxList=brxx.d
                    if(brxx.d.kssj){
                        syjjInfo.kssj =syjjInfo.fDate(brxx.d.kssj,'date')+' 00:00:00';
                    }
                    if (brxx.d != null && brxx.d != "") {
                        cb(brxx.d);
                    } else {
                        malert('未查到病人相关记录','top','defeadted');
                    }
                } else {
                    malert('未查到病人相关记录','top','defeadted');
                }
            });
        },
        //查询病人的费用项目
        getBryjjlList: function(zyh,cb){
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjkFphm&types=queryFphmByjkpzh&jkpzh=" +zyh.jkpzh, function (yjjlList) {
                if (yjjlList.a == 0) {
                    cb(yjjlList.d);
                } else {
                    malert(yjjlList.c,'top','defeadted');
                }
            });
        },
        //查询预交记录详细信息
        getYjjlInfo: function(yjjlId,cb){
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglFyglYjjl&types=queryById&yjjlid=" + yjjlId, function (yjjlInfo) {
                if (yjjlInfo.a == 0) {
                    if (yjjlInfo.d != null && yjjlInfo.d != "") {
                        cb(yjjlInfo.d);
                    } else {
                        malert('未查到相关记录','top','defeadted');
                    }
                } else {
                    malert(yjjlInfo.c,'top','defeadted');
                }
            });
        },*/
        quxiao: function(){
            //this.zflx = this.zflxServerList[0].zflxbm;
            this.PzhbrxxList=[]
            this.popContent={}
            this.isShow = false;
            tableInfo.isShow = true;
        },
        print: function () {
            if(this.isShow) {
                if (this.popContent.jkpzh == undefined && syjjInfo.isMzDis) {
                    malert('请先门诊交款，再打印', 'top', 'defeadted');
                    return false;
                }
            }
                if (tableInfo.csqxContent.N05001200609 == "0"){
                    window.print();
                }else {
                    //帆软打印
                    var frpath = "", reportlets = "";
                    if (window.top.J_tabLeft.obj.frprintver == "3") {
                        frpath = "%2F";
                    } else {
                        frpath = "/";
                    }
                    var jkpzh=String(),url='';
                    if(JSON.stringify(this.popContent) !="{}"){
                        url='mzsf_cwjkb'
                         jkpzh = this.popContent.jkpzh.substr(0, this.popContent.jkpzh.length - 1);
                    }else{
                        for (var i = 0; i < tableInfo.isChecked.length; i++) {
                            if(tableInfo.isChecked[i]==true){
                                jkpzh+=tableInfo.jsonList[i].jkpzh+','
                            }
                        }
                        // jkpzh=jkpzh.substring(0,jkpzh.length-1)
                        url='mzsf_cwjkb_hzb'
                    }

                    reportlets = "[{reportlet: 'mzsf" + frpath + ""+url+".cpt',yljgbm:'" + jgbm + "',jkpzh:'" + jkpzh + "',jksj:'"+this.fDate(this.param.beginrq,'date')+'至'+this.fDate(this.param.endrq,'date')+"'}]";
                    if (!FrPrint(reportlets)) {
                        window.print();
                    }
                }
        },

        printtfmx: function () {
            if(this.isShow) {
                if (this.popContent.jkpzh == undefined && syjjInfo.isMzDis) {
                    malert('请先门诊交款，再打印', 'top', 'defeadted');
                    return false;
                }
            }
            if (tableInfo.csqxContent.N05001200609 == "0"){
                window.print();
            }else {
                //帆软打印
                var frpath = "", reportlets = "";
                if (window.top.J_tabLeft.obj.frprintver == "3") {
                    frpath = "%2F";
                } else {
                    frpath = "/";
                }
                var jkpzh=String(),url='';
                if(JSON.stringify(this.popContent) !="{}"){
                    url='mzsf_cwjkbtfmx'
                    jkpzh = this.popContent.jkpzh.substr(0, this.popContent.jkpzh.length - 1);

                    reportlets = "[{reportlet: 'mzsf" + frpath + ""+url+".cpt',yljgbm:'" + jgbm + "',jkpzh:'" + jkpzh + "',jksj:'"+this.fDate(this.param.beginrq,'date')+'至'+this.fDate(this.param.endrq,'date')+"'}]";
                    if (!FrPrint(reportlets)) {
                        window.print();
                    }
                }
            }
        },
        checkJe:function(){
        	var res = false;
        	// @yqq 门诊交款判断金额是否一致
            var fbzh = 0;
            var fylbzh = 0;
            for (var i = 0; i < syjjInfo.brfbList.length; i++) {
            	var item = syjjInfo.brfbList[i];
            	if("小计"  == item.ryfbmc){
            		var je = parseFloat(this.fDec(item.sfyje+item.tfyje,2)  ? this.fDec(item.sfyje+item.tfyje,2) : 0);
                	fbzh += je;
            	}
			}
            for (var i = 0; i < syjjInfo.fylbList.length; i++) {
            	var item = syjjInfo.fylbList[i];
            	var je = parseFloat(this.fDec(item.sfyje+item.tfyje,2)  ? this.fDec(item.sfyje+item.tfyje,2) : 0);
            	fylbzh += je;
			}
            if(fbzh != fylbzh){
            	res = true;
            }
            return res;
        },
        //门诊交款
        saveData: function () {
            if (!this.ifClick) return; //如果点击过则直接返回
            this.ifClick = false;
            if (this.popContent.jkpzh) {
                malert('你已经交过款了，不能再交款','top','defeadted');
                this.ifClick = true;
                return;
            }

            if(this.popContent.jkje=='0.00' && this.popContent.ryfbList.length == 0){
                malert('交款金额不能为0.00元!','top','defeadted');
                this.ifClick = true;
                return;
            }


//            if(this.checkJe()){
//            	malert('结算记录与收费记录总金额不一致，请核对后交款！','top','defeadted');
//                this.ifClick = true;
//                return;
//            }




            var json = {
                jkrq: syjjInfo.sfsj,
                kssj: this.popContent.kssj
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsCwjk&types=save&ksbm='+tableInfo.ksbm,
                JSON.stringify(json)).then(function (data) {
                if (data.body.a == 0) {
                	malert('交款成功！','top','success');
                	syjjInfo.popContent = {};//总体费用信息
                	syjjInfo.brfbList = [];//病人费别
                    syjjInfo.fylbList = [];//费用项目
                    syjjInfo.mxfyList = []; //门诊费用明细
                    syjjInfo.fphmPopContent = {}; //发票信息
                    syjjInfo.ifClick = true;
                    tableInfo.getData();
                    tableInfo.isShow = true;
                    syjjInfo.isMzDis = false;
                    syjjInfo.isShow = false;
                    syjjInfo.isShouYJJ = false;
                } else {
                    malert('交款失败:'+data.body.c,'top','defeadted');
                    syjjInfo.ifClick = true;
                }
            }, function (error) {
                console.log(error);
            });
        },

    }
});
