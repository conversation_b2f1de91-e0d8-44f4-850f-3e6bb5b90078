<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>挂号管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<style>
    .tong-top{
        width: 100%;
        background: #fafafa;
        border: 1px solid #eeeeee;
        border-right: none;
        border-left: none;
        padding: 10px 0 10px 12px;
        box-sizing: border-box;
        display: flex;
        height: auto;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
    <div class="wrapper">
            <div class=" toolMenu">
                <div class="panel">
                    <div class="tong-top">
                        <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getData">查询</button>
                    </div>
                </div>
                <!--检索字段begin-->

                <div class="grid-box bg-fff" style="padding: 13px 0;">
                    <div class="col-xxl-4 flex margin-l-10">
                        <div class="flex-container  flex-align-c margin-l-10">
                            <label class="whiteSpace  padd-r-5 ft-14">时间</label>
                            <div class="flex-container flex-align-c ">
                                <input class="zui-input todate wh120 " placeholder="开始时间" id="timeVal"/>
                                <span class="padd-r-5 padd-l-5">至</span>
                                <input class="zui-input todate wh120 " placeholder="结束时间" id="timeVal1"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <!--表格区-->
        <div class="zui-table-view ghList padd-l-10 padd-r-10" id="table1" style="border:none;" >
            <div class="zui-table-header" >
                <table class="zui-table">
                    <tbody>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'list'"
                                                                               :type="'all'" :val="isCheckAll">
                        </input-checkbox></div></th>
                        <th class="cell-l"><div class="zui-table-cell cell-l"><span>挂号序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>患者姓名</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>挂号时间</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>性别</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>年龄</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>联系电话</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>证件号码</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>挂号种类</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>挂号科室</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>接诊医生</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr  v-for="(item, $index) in list" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                         @mouseenter="hoverMouse(true,$index)"
                         @mouseleave="hoverMouse()"
                         @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'list'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="item.ghxh"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.brxm"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="fDate(item.ghrq, 'date')"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.brnl"></div></td>
                        <td ><div class="zui-table-cell cell-l" v-text="item.sjhm"></div></td>
                        <td ><div class="zui-table-cell cell-xl"  v-text="item.sfzh"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.ghzlmc"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.ghksmc"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.jzysxm"></div></td>
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s" >
                                <i @click="zuofei()" class="icon-sc" data-title="删除"></i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>

            </div>

            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'list'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                             </div>
                            </th>
                            <th class="cell-l"><div class="zui-table-cell cell-l"><span>挂号序号</span> </div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in list"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','list'],$event)">
                            <td class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'list'"
                                                                                                  :type="'some'" :which="$index"
                                                                                                  :val="isChecked[$index]">
                            </input-checkbox></div></td>
                            <td class="cell-l"><div class="zui-table-cell cell-l" v-text="item.ghxh"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in list"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','list'],$event)">
                            <td class="cell-s"><div class="zui-table-cell cell-s" ><i @click="zuofei()" class="icon-sc" data-title="删除"></i></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
    <script src="ghgl.js" type="application/javascript"></script>
</body>
</html>
