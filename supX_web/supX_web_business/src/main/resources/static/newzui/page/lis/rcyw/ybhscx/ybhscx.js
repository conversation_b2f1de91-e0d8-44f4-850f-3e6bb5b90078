	$(".zui-table-view").uitable();
    var s=new Date().getTime()
    var l=new Date()
    var e=l.setDate(l.getDate()+1)
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            index:1,
            old:'原始数据',
            isShowpopL:false,
            isShow:false,
            flag:false,
            title:'',
            centent:'',
            pop:{},
            param: {
                page: 1,
                rows: 10,
                total: '',
                time: '',
                bah: '',
                zxsb:''
            },
            jydjList: [],
            jysbList:''

        },
        created:function(){
        	this.param.time=this.formDate(s)+' - '+this.formDate(e)
        },
        methods:{
        	 formDate: function (value) {
                 var d = new Date(value);
                 return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
             },
            //取消核收
            qxhs:function () {
                pop.flag=false;
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.flag=false;
                pop.title='取消核收';
                pop.dyShow=true;

                // $('#popus').fadeIn();
            },
            js:function () {
        	     if(wrapper.isChecked.length==0){
                     malert('请选择拒收样本','top','defeadted')
                 }
                else {
                    $("#isFold").addClass('side-form-bg');
                    $("#brzcList").removeClass('ng-hide');
                }

            },
            //过滤
            guolu:function () {
                isTabel.isShow=true;
            },
            //打印
            Btnprint:function () {
                window.print();
            },
            //刷新获取数据
            sx : function () {
                //解析时间
                if (wrapper.param.time != null) {
                    var times = wrapper.param.time.split(" - ");
                    wrapper.param.ybhsrq = times[0];
                    wrapper.param.ybhsrqEnd = times[1];
                }
                this.param.lx = '1';//住院
                console.log( JSON.stringify(this.param));
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryShcg_zy&yq=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                    	wap.totlePage =Math.ceil(json.d.total / wrapper.param.rows);
                        wap.jydjList = json.d.list;
                    } else {
                        malert("查询失败" + json.c);
                        return;
                    }
                });
            },
            jysb: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    if (json.a == 0) {
                    	wrapper.jysbList = json.d.list;
                    } else {
                        malert("获取申请检验设备失败" + json.c);
                        return false;
                    }
                });
            },
            util:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=gltjutil&yq=", function (json) {
            		isTabel.util=json.d;
                });
            },
        	cx: function(){
        		wrapper.sx();
        	}

        },
        watch:{
        	'param.time':function(){
        		wrapper.sx();
        	},
        	'param.bah':function(){
        		wrapper.sx();
        	},
        	'param.zxsb' : function(){
        		wrapper.sx();
        	}
        }
    });
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394',
        range:true
        ,done:function (value,data) {
        	 wrapper.param.time = value
        }
    });
    var pop=new Vue({
        el:'#pop',
        data:{
            isShowpopL:false,
            isShow:false,
            dyShow:false,
            flag:false,
        },
        methods:{

            //确定
            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                var List = [];
                if(wrapper.isChecked.length>0){
	                for (var i = 0; i < wrapper.isChecked.length; i++) {
	                    if (wrapper.isChecked[i] == true) {
	                        var jydj = {};
	                        List.push(wap.jydjList[i]);
	                    }
	                }
	                var json = '{"list":' + JSON.stringify(List) + '}';
                }
                this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updateqxhs_zy',json).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				//成功回调提示
                                malert('取消核收成功','top','success');
                                wrapper.isChecked=[];
                                wrapper.sx();
                			}

                        },
                        function(error) {
                        	malert(error,'top','defeadted');
                        });

            }

        },
    });
    var wap=new Vue({
        el:'.zui-table-view',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
        	totlePage:'',
            title:'',
            centent:'',
            //isChecked: [],
            jydjList:[],
            page:'',
            rows:10
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods:{
            jsdydj : function(jydj){
                this.jydj = jydj;
                console.log(jydj);
            },
            getData : function (){
            	wrapper.param.rows=this.rows;
            	wrapper.param.page=this.page;
            	wrapper.sx();
            }

        }
    })
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            flag:false,
            popContent:{jsyy:''},
            isFold: false,
            param:null,
            jsyy:'',
            jscl:''
        },
        methods:{
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

                var List = [];
                if(wrapper.isChecked.length>0){
	                for (var i = 0; i < wrapper.isChecked.length; i++) {
	                    if (wrapper.isChecked[i] == true) {
	                        var jydj = {};
	                        wap.jydjList[i].jsyy=this.popContent.jsyy;
	                        wap.jydjList[i].jscl=this.jscl;
	                        List.push(wap.jydjList[i]);
	                    }
	                }
	                var json = '{"list":' + JSON.stringify(List) + '}';
                }
                this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updatejs_zy',json).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				//成功回调提示
                                malert('拒收成功','top','success');
                                $(".side-form").addClass('ng-hide');
                                this.popContent.jsyy='';
                                this.jscl='';
                                wrapper.sx();
                			}

                        },
                        function(error) {
                        	malert(error,'top','defeadted');
                        });


            },

        },
    });
    var isTabel=new Vue({
        el:'#isTabel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isTabelShow:false,
            isShow:false,
            minishow:true,
            isShowpopL:false,
            popContent:{},
            item: 0,
            appNum:[],
            appObj:{},
            
            cxtjList:[],
            util:{},
            isLxNum:'',
            isTyNum:'',
            isXbNum:'',
            isKsNum:'',
            isYsNum:'',
            isYblxNum:'',
        },
        created:function () {
          this.append()
        },
        methods:{
        	resultChange_item: function (val) {
        		console.log(val);
        		var index=val[2][0];
        		if(val[0]=='LX'){//类型
        			var pd=index+',';
        			this.isLxNum+=pd;
        		}
        		if(val[0]=='BAH' || val[0]=='BRXM' || val[0]=='NL' || val[0]=='FYMC' || val[0]=='XZ'){//通用
        			var pd=index+',';
        			this.isTyNum+=pd;
        		}
        		if(val[0]=='XB'){//性别
        			var pd=index+',';
        			this.isXbNum+=pd;
        		}
        		if(val[0]=='KSBM'){//科室
        			var pd=index+',';
        			this.isKsNum+=pd;
        		}
        		if(val[0]=='SQYS'){//医生
        			var pd=index+',';
        			this.isYsNum+=pd;
        		}
        		if(val[0]=='YBLX'){//样本类型
        			var pd=index+',';
        			this.isYblxNum+=pd;
        		}
        		
        	    Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
        	    if(event.keyCode != null){
        	    	this.nextFocus(val[1], parseInt(val[2][2]));
        	    }
        	},
        	resultChangeTj_item: function (val) {
        	    Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
        	    if(event.keyCode != null){
        	    	this.nextFocus(val[1], parseInt(val[2][2]));
        	    }
        	},
        	resultChangeLjtj_item: function (val) {
        		Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
        		if(event.keyCode != null){
        			this.nextFocus(val[1], parseInt(val[2][2]));
        		}
        	},
        	Wf_YppfChange: function (val) {
        	    var index = "";
        	    //先获取到操作的哪一个
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        var types = val[2][val[2].length - 1];
        	        index = val[2][1];
        	        Vue.set(this.cxtjList[index], 'jg', val[0]);
        	    }
        	    
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        if (window.event.keyCode == 13) {
        	            this.nextFocus(event);
        	        }
        	    }
        	},
        	Wf_YsChange: function (val) {
        	    var index = "";
        	    //先获取到操作的哪一个
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        var types = val[2][val[2].length - 1];
        	        index = val[2][1];
        	        Vue.set(this.cxtjList[index], 'jg', val[0]);
        	    }
        	    
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        if (window.event.keyCode == 13) {
        	            this.nextFocus(event);
        	        }
        	    }
        	},
        	
        	save:function(){
        		console.log(this.cxtjList);
        		for (var int = 0; int < this.cxtjList.length; int++) {
        			if (wrapper.param.time != null) {
                        var times = wrapper.param.time.split(" - ");
                        this.cxtjList[int].starttime = times[0];
                        this.cxtjList[int].endtime = times[1];
                    }
        			this.cxtjList[int].type='1'
				}
        		var json = '{"list":' + JSON.stringify(this.cxtjList) + '}';
        		this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=gjcx',json).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				 wap.jydjList = data.body.d.list;
                				 malert('查询成功','top','success');
                			}else{
                				malert('查询失败，请检查参数是否正确','top','defeadted');
                			}

                        },
                        function(error) {
                        	malert(error,'top','defeadted');
                        });
        	},
        	
        	
            sc: function (index) {
                this.cxtjList.splice(index,1)
                // for(var i=0;i<this.appNum.length;i++){
                //     if(this.appNum[i].num==index){
                //
                //     }
                // }

            },
            append: function () {
            	this.cxtjList.push({});
               /* this.item = this.item + 1
                this.appObj.num=this.item
                this.appNum.push(JSON.parse(JSON.stringify(this.appObj)))
               this.$nextTick(function () {
                   $(".zui-form .lsittext").uicomplete({
                       iskeyup: true
                   });
               })*/
            },
            tabshow:function () {
                this.isTabelShow=true;
                this.minishow=false;
                pop.dyShow=false;
            },
            showDom:function () {
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.dyShow=false;
                pop.flag=true;
            }
        },
    })

    wrapper.jysb();
    wrapper.util();
