var left = new Vue({
    el: '.left_tab1',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        caqxContent: {}, //参数权限对象
        allKs: [],
        jsonList: [],//左边患者列表
        total: null,
        ksId: null,
        zyhs: [],
        zyh: null,
        Type: 'zy',
        cwbh: '',
        num:0,
        objBrQuery:{
            '0':'wrzhz',
            '1':'zyhzxx',
            '2':'cyhzxx',
            '3':'zchz',
        },
        reqUrl:{
        	'0':'New1HszHlywYzclCx',
            '1':'ZyysYsywYzcl',
            '2':'ZyysYsywYzcl',
            '3':'New1HszHlywYzclCx',
        }
    },
    mounted:function(){
        this.initData();
    },
    methods: {
        tabBg:function(index){
            this.num=index;
            this.getData()
        },
        //选择病人标签后查询病人
        getData: function () {
            var parm={}
            if(this.num!=3){
                parm.rycwbh=left.cwbh;
            }else{
                parm.rycwbh=''
            }
            this.jsonList=[]
            this.total = 0;
            parm.ksbm=left.ksId;
            parm.ryks=left.ksId;

            $.getJSON('/actionDispatcher.do?reqUrl='+this.reqUrl[this.num]+'&types='+this.objBrQuery[this.num]+'&parm=' + JSON.stringify(parm), function (json) {
               if(json.a=='0' && json.d){
                   left.jsonList = json.d.list;
                   left.total = json.d.total;
               }else{
                   malert("获取患者列表失败!" + json.c, 'top', 'defeadted');
               }
                });
        },
        KsChange:function(val){
            this[val[2][0]]=val[0]
            this.getData()
        },
        //获取参数权限
        getCsqx: function () {
            var ksId = left.ksId;
            if (ksId == null) {
                ksId = left.allKs[0].ksbm;
            }
            var parm = {
                "ylbm": 'N030042007',
                "ksbm": ksId
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i]
                            switch (csjson.csqxbm) {
                                case "N03004200701": //是否允许修改执行时间 0-允许 1-不允许
                                    if (csjson.csz) {
                                        left.caqxContent.cs00900100101 = csjson.csz;
                                    }
                                    break;
                                case "N03004200702": //是否允许修改审核时间 0-允许 1-不允许
                                    if (csjson.csz) {
                                        left.caqxContent.cs00900100102 = csjson.csz;
                                    }
                                    break;
                                case "N03004200703": //是否允许修改病区出院时间 0-允许 1-不允许
                                    if (csjson.csz) {
                                        left.caqxContent.cs00900100103 = csjson.csz;
                                    }
                                    break;
                                case "N03004200704": //是否允许修改医生停嘱时间 0-允许 1-不允许
                                    if (csjson.csz) {
                                        left.caqxContent.cs00900100104 = csjson.csz;
                                    }
                                    break;
                                case "N03004200705": //是否允许修改护士停着时间 0-允许 1-不允许
                                    if (csjson.csz) {
                                        left.caqxContent.cs00900100105 = csjson.csz;
                                    }
                                    break;
                            }
                        }
                } else {
                    malert("参数权限获取失败" + json.c,'top','defeadted');
                }
            });
        },
        //h获取科室集合
        initData: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"N030042002"}', function (json) {
                if (json.a == '0' && json.d) {
                    left.allKs = json.d;
                    if (json.d.length > 0) {
                        left.ksId = json.d[0].ksbm;
                    }
                    left.getCsqx(); //科室加载完成之后再进行参数权限的获取
                    //科室获取成功后再查询患者信息
                    left.getData()
                }else {
                    malert('查询科室失败','top','defeadted')
                }
            });
        },
        //右边菜单赋值
        queryPatient: function (index) {
            pop.baseInfo = this.jsonList[index];
        },
        //单击病人查询医嘱
        queryYZ: function (zyh) {
            this.zyh = zyh;
            toolMenu.queryyz();
        },
        querySome: function () {
            toolMenu.queryyz();
        },
        //病区出院
        bqcy: function () {
            var parm = {
                zyh: this.zyh,
                ksbm: left.ksId
            };
            if (this.zyh != null) {
                if (!confirm("你确定要出院【" + (this.zyh) + "】住院号的病人吗？")) {
                    return false;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=HszByglCwgl&types=bqcy',
                    JSON.stringify(parm))
                    .then(function (data) {
                        if (data.body.a == "0") {
                            malert("病区出院成功！",'top','success')
                           left.getData()
                        } else {
                            console.log("error:" + data.body.c);
                            malert(data.body.c,'top','defeadted');
                        }
                    });
            } else {
                malert("请选择需要出院的病人！",'top','defeadted');
            }
        },
        //取消病区出院
        qxbqcy: function () {
            var parm = {
                zyh: this.zyh,
                cyks: left.ksId
            };
            if (this.zyh != null) {
                if (!confirm("你确定要取消出院【" + (this.zyh) + "】住院号的病人吗？")) {
                    return false;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=HszByglCwgl&types=qxbqcy',
                    JSON.stringify(parm))
                    .then(function (data) {
                        if (data.body.a == "0") {
                            malert("取消病区出院成功！",'top','success')
                           left.getData()
                        } else {
                            malert("取消病区出院成功！",'top','success')
                         left.getData()
                        }
                    });
            } else {
                malert("请选择需要取消病区出院的病人！",'top','defeadted');
            }
        },

    }
});
//
var InfoMenu = new Vue({
    el: '.InfoMenu',
    data: {
        which: 1
    },
    mounted:function(){
        this.loadCon('yzcl');
    },
    methods: {
        loadCon: function (page) {
            var pageDiv = $("#" + page);
            $(".page_div").hide();
            if (pageDiv.length == 0) {
                //$("."+page).load(page + "/" + page + ".html").fadeIn(300);
                $("." + page).load(page + ".html").fadeIn(300);
            } else {
                $("." + page).fadeIn(300);
            }
            console.log("which:" + this.which);
        }
    }
});
