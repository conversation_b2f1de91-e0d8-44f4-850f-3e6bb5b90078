@image:"../../../../pub/image/";
.icon-plyr:before{
  content: '';
  background: url("@{image}<EMAIL>") center left no-repeat;
  background-size: 20px 20px;
  position: absolute;
  width: 20px;
  height: 20px;
  left: 6px;
  top:6px;
}
.icon-plyc:before{
  content: '';
  background: url("@{image}<EMAIL>") center left no-repeat;
  background-size: 20px 20px;
  position: absolute;
  width: 20px;
  height: 20px;
  left: 6px;
  top: 6px;
}
.background-zksj{
  background: #fff;
  overflow: hidden;
  padding: 0 0 0 13px;
}
.l-zksj-left{
  width:30%;
  float: left;
  .l-zksj-top{
    width: 100%;
    display: flex;
    align-items: center;
    background:#edf2f1;
    height: 36px;
    i{
     display: block;
      float: left;
      color:#333333;
      &:nth-child(1){
        width: 50px;
        text-align: center;
      }
      &:nth-child(2){
        width: 150px;
        text-align: center;
      }
      &:nth-child(3){
        width: 150px;
        text-align: left;
      }
      &:nth-child(4){
        width: 120px;
        text-align: center;
      }
    }
  }
  .l-zksj-list{
    width: 100%;
    li{
      display: flex;
      align-items: center;
      height: 54px;
      border:1px solid #e9eee6;
      border-top:none;
      i{
        display: block;
        float: left;
        color:#757c83;
        &:nth-child(1){
          width: 50px;
          text-align: center;
        }
        &:nth-child(2){
          width: 150px;
          text-align: center;
        }
        &:nth-child(3){
          width: 150px;
          text-align: left;
        }
        &:nth-child(4){
          width: 120px;
          text-align: center;
        }
      }
      &:nth-child(2n){
        background:#fdfdfd;
      }
      &:hover{
        border:1px solid #1abc9c;
        background:rgba(26,188,156,0.08);
        box-shadow:0 0 6px 0 rgba(26,188,156,0.45);
      }
    }
  }
  .fieldlist{
    display: none;
  }

}
.zui-table-view{
  border: none;
 border-bottom:1px solid #eee;
  overflow: hidden !important;
}
.zui-table-view .zui-table-body tr .l-zksj-td{
  width: 50px !important;
  text-align: center;
}
.zui-table-view table tr:last-child{
  border-bottom: 1px solid #eee;
}
.zui-table-view table tr{
  border-left: 1px solid #eee;
}
.zui-table-view .zui-table-body{
  border-bottom: none;
}
.l-zksj-right{
  width: 69%;
  float:right;
  .l-right-top{
    width: 100%;
    float: left;
    height: 400px;
    max-height: 400px;
    overflow: auto;
    background: #1abc9c;
  }
.l-scroll-bottom{
  margin-top:11px;
.icon-ms:before{
  left:35px;
}
  .margin-l15{
    margin-left: 15px;
  }

}
  .l-scroll-top{
    border-bottom: 1px solid #eee;
  }
  .icon-sc:before{
    color: #757c83;
  }
  .zui-table-view .zui-table-body{
    height:260px !important;
    max-height:260px;
  }
  .zui-table-view{
    min-height:260px !important;
  }
  .zui-table-view{
    border-bottom: 1px solid #eee !important;
  }
}
.tab-message{
  width: 100%;
  height:46px;
  background: #1abc9c;
  line-height: 46px;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  a{
    color: #fff;
  }
  .icon-cha:before{
    top: 0;
  }
}

.l-zk-ms{
  width: 100%;
  padding:0 20px;
  box-sizing: border-box;
  float: left;
  overflow: hidden;
  position: relative;
  .l-zk-col-6{
    width:48%;
    float: left;
    margin-top: 20px;
    label{
      color:#7f8fa4;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      i{
        width:60px;
        display: block;
        text-align: right;
      }
      .l-data{
        position: absolute;
        width: 20px;
        height: 20px;
        left:72px;
        top:11px;
        display: block;
      }
      .l-color35{
        color:#354052 !important;
      }
    }
    &:nth-child(2n){
      margin-left: 5px;
      float: right !important;
    }
    .l-time,.l-times{
      text-indent: 17px;
    }
    .l-dw-after{
      position: absolute;
      right:9px;
      width: auto;
      height: 20px;
      top: 9px;
      color: #1abc9c;
    }
    .l-label-left{
      float: left;
      color:#354052;
      width: 103px;
      line-height:36px;
      margin-left:15px;

    }
    .l-label-right{
      float: left;
      line-height: 36px;
    }
  }
  .fr{
    float: right !important;
  }
  label{
    color:#7f8fa4;
  }
}
.l-bottom-fixed{
  position: absolute;
  bottom: 0;
  height: 70px;
  right: 0;
  left: 0;
  border-top: 1px solid  #dfe3e9;
  display: flex;
  justify-content: flex-end;
  align-items: center;

}
.l-width100{
  width: 100%;
}
.l-zksj-gz{
  width: 100%;
  height:135px;
  border: 1px dashed rgba(26,188,156,.3);
  background:rgba(26,188,156,0.06);
  margin-top: 20px;
  padding-top: 1px;
  padding: 1px 10px 0 0;
  .l-zksj-g{
    position: absolute;
    z-index: 1;
    top:0px;
    left: 32px;
    text-align: center;
    width: 54px;
    font-size:16px;
    color:#1abc9c;
    height: 40px;
    .l-g-top{
      width: 100%;
      height: 21px;
      background: #fff;
      display: block;
    }
    .l-g-bottom{
      width: 100%;
      height: 20px;
      background:rgba(26,188,156,0.06);
    }
    .l-g-center{
      position: absolute;
      z-index: 11;
      width:54px;
      text-align: center;
      top: 9px;
      left: 0;
    }
  }
}
.l-zksj-switch{
  height: 50px;
  width: 100%;
  position: relative;
  .l-zksj-fixed{
    color:#7f8fa4;
    width:86px;
    text-align: right;
    float: left;
    line-height: 56px;
  }
}
.padd10{
  padding: 0 10px 0 18px;
  margin-top: 10px;
}