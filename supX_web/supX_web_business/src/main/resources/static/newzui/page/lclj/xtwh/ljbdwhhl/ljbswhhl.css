

.wsh{
    background: url("/newzui/css/images/wsh.png") right -8px/72px auto no-repeat;
}
.ysh{
    background: url("/newzui/css/images/ysh.png") right -8px/72px auto no-repeat;
}

.lj-title{
    padding: 8px;
}
.lj-title h1{
    text-align: center;
    font-size: 22px;
    line-height: 42px;
    color:#3a3a3a;
}
.lj-title p{
    font-size:14px;
    color:#7f8fa4;
    line-height:19px;
}

.add-or-edit-jd .strat-hours-box .strat-hours,
.add-or-edit-jd .end-day-box .end-day{
    padding: 0 10px;
    font-size:14px;
    color:#1abc9c;
    position: absolute;
    right: 0;
    height: 36px;
    line-height: 36px;
    top: 0;
}

.fyxm-tab div{
    padding: 0 15px;
    border: none;
}
.fyxm-tab div:first-child{
    border: none;
}
.fyxm-tab div span{
    padding: 0 5px;
    height: 36px;
}

.add-or-edit-content-addyz{
    cursor: pointer;
}
.add-or-edit-content-addyz::before{
    margin-right: 4px;
}


.ksys-btn{
    position: static;
}
.bqcydj_model{
    width: auto;
    padding: 14px 18px 14px 18px;
    background: rgba(255, 255, 255, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
#addOrEditContent input {
    height: 28px;
}
.pop-805 {
    width: 82% !important;
}
.header-hzgl {
    height: 100%;
}
.header-hzgl .tab-card-body {
    height: calc(100% - 163px);
}

.ksys-side .dzcf-rps{
    position: absolute;
    top: 0;
    text-align: center;
    right: 0;
    justify-content: flex-end;
}
.ksys-side .dzcf-rps img{
    width:53px;
    height: 52px;
}
.zvyHeight{
    max-height: 552px;
    height: 100%;
}
.header-hzgl .tab-card-body {
    height: calc(100% - 163px);
}
.header-hzgl {
    height: 100%;
}

#addOrEditContent .zui-table-view .zui-table-body tr td{
    padding: 0;
}
#addOrEditContent .zui-table-view .zui-table-header .zui-table-cell, #addOrEditContent .zui-table-view .zui-table-header .zui-table-cell span {
    height: 25px;
    line-height: 25px;
}
#addOrEditContent .zui-select-inline, .hzlistHeight  .zui-table-cell {
    height: unset;
    vertical-align: top;
}
.hzgl-shanchu {
    width: 20px;
    /*height: 20px;*/
    cursor: pointer;
}

.hzgl-shanchu:before {
    position: absolute;
    top: -5px;
    content: '';
    background: #dfe3e9;
    width: 20px;
    right: -5px;
    height: 20px;
    z-index: 1;
    border-radius: 100%;
}

.hzgl-shanchu:after {
    position: absolute;
    top: -5px;
    content: '';
    background: #ffffff;
    width: 10px;
    right: -5px;
    height: 3px;
    z-index: 1;
    text-align: center;
    margin: 8px 5px;
}
.add-yp-hzgl {
    background: rgba(26, 188, 156, 0.07);
    border: 1px solid #1abc9c;
    border-radius: 4px;
    margin: 0 10px 15px 0;
    line-height: 34px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    position: relative;
    width: 34px;
    height: 34px;
}

.add-yp-hzgl:before {
    position: absolute;
    content: '+';
    left: 22%;
    margin-top: -1px;
    color: #1abc9c;;
    font-size: 24px;

}
.bz-wh{
    width: 860px !important;
    /*height: 570px !important;*/
}
.w30{
    width: 25%;
}
.w70{
    width: 75%;
}
.bz_model{
    height: 470px ;
    min-width: 500px;
    overflow: hidden;
}
.bzList{
    height: calc(100% - 36px);
    overflow: auto;
}

.titles{
    height: 36px;
    line-height: 34px;
    padding: 0 10px;
    text-align: center;
    background-color: #edf2f1;
    border: 1px solid #e9eee6;
    border-bottom-color: transparent;
    color: #333333;
}
.w70 .titles{
    height: 25px;
    line-height: 25px;
}
.bzItem{
    height: 40px;
    line-height: 38px;
    text-align: center;
    color: #757c83;
    padding: 0 10px;
    border: 1px solid #e9eee6;
    background-color: #fdfdfd;
}

.zuiTableBodyHeight{
    height: 150px;
}
.bz_model .zui-table-view .zui-table-body tr td{
    padding: 0;
}
.bzList::-webkit-scrollbar-thumb{
    background: rgba(0, 0, 0, 0.5);
}

.bzSelected-active{
    background: #a5e5d8
}
