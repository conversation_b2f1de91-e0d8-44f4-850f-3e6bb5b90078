.right {
    width: calc(100% - 250px);
    height: 100%;
    float: left;
}

.yzclSearch {
    border-bottom: 4px solid #eeeeee;
    border-right: 4px solid #eeeeee;
    margin-top: 0;
}

.YPTable {
    padding-left: 4%;
}

.YPTable td {
    border: 1px solid #bbbbbb;
    height: 30px;
    font-size: 14px;
}

.YPTable th {
    font-size: 14px;
}

.YPTable tr:nth-child(n+2):hover {
    background-color: #EAF2FF;
}

.CFTabTable {
    float: left;
    padding: 0;
    width: calc(100% - 10px);
    height: calc(100% - 282px);
    border-right: 4px solid #EEEEEE;
    overflow: auto;
    margin-left: 6px;
}

.CFTabTable table {
    /*border-left: 1px solid #bbbbbb;*/
    /*border-right: 1px solid #bbbbbb;*/
    /*border-top: 1px solid #bbbbbb;*/
}

.CFTabTable tr {
    border-bottom: 1px solid #bbbbbb;
}

.CFTabTable td {
    border: 1px solid #CCCCCC;
    padding: 6px;
}

.yzcl_context {
    background-color: #FFFFFF;
    width: 99%;
    height: 100%;
}

.YZInfo {
    width: 100%;
    margin-left: 8px;
    height: 95%;
}

.YZChoice {
    display: inline-block;
}

.YZChoice > div {
    float: left;
    position: relative;
    margin: 5px 10px 5px 4px;
}

.YZChoice > div > input {
    width: 90px;
}

.YZChoice > div > span {
    position: absolute;
    top: 3px;
    font-size: 14px;
}

.personInfo {
    display: inline-block;
    margin-bottom: 6px;
}

.personInfo > div {
    float: left;
    margin-right: 30px;
}

.personInfo > div > b{
    font-size: 14px;
}

.yzdItem {
    /*margin-bottom: 60px;*/
    text-align: left;
}

.table_tab1 tr td:first-child {
    text-align: center;
}

.table_tab1 {
    height: auto;
}

.table_tab1 th {
    background-color: #eeeeee;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
}

.AllYZ {
    height: calc(100% - 260px);
    overflow: scroll;
}

.AllYZSH {
    height: calc(100% - 270px);
    overflow: scroll;
}

.AllYZCX {
    height: calc(100% - 110px);
    overflow: scroll;
}

.YZInfo textarea {
    /* margin-top: 10px; */
    height: 75px;
    width: calc(100% - 160px);
    background-color: #eeeeee;
}

.patientBaseInfo {
    float: left;
    margin-left: 6px;
    height: 100%;
    margin-top: 20px;
}

.baseTitle {
    padding: 6px 0 6px 12px;
}

.baseInfo div {
    width: 100%;
    margin: 1px;
    display: inline-block;
    position: relative;
}

.baseInfo span {
    display: inline-block;
    float: left;
    padding: 5px 4px;
    height: 16px;
    border: 1px solid #aaa;
}

.baseInfo span:first-child {
    width: 80px;
    margin-right: 2px;
    padding-left: 20px;
}

.baseInfo span:nth-child(2) {
    width: calc(100% - 132px);
    min-height: 15px;
}

.baseInfo span:nth-child(3) {
    position: absolute;
    left: 8px;
    color: #F7D063;
    top: 2px;
    border: 0;
}

.ChildtablePage {
    border-top: 4px solid #EEEEEE;
    border-right: 4px solid #EEEEEE;
    background: linear-gradient(to bottom, #ffffff 0, #ffffff 100%);
    width: 230px;
    padding: 4px 0;
}

.testBtu {
    width: 100%;
    text-align: left;
    margin-top: 8px;
}

.testBtu button {
    width: 100%;
}

.InfoMenu {
    padding: 10px 0;
}

.toolMenu button {
    padding: 6px;
}

.popup-right {
    /*width: 360px;*/
}


.showInfo {
    position: absolute;
    bottom: 10px;
    right: 12px;
}

.popInfo {
    width: 300px;
    height: 330px;
}

.popInfo2 {
    width: 400px;
    height: 430px;
}

.brCon {
    text-align: left;
}

.brCon > div {
    margin-bottom: 20px;
    display: inline-block;
    width: 88%;
    text-align: right;
}

.popInfo input {
    height: 30px;
    width: 180px;
    margin-left: 10px;
}

.selectInput {
    float: right;
    width: 190px;
}

.popDoBtu {
    padding: 30px 20px 0 20px;
    width: 86%;
}

.popCenter{
    display: inline-block;
    overflow: scroll;
    height: 100%;
    width: 100%;
    background-color: white;
}

.ypsl-table table{
    width: 760px;
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
}

.ypsl-table th, .ypsl-table td{
    border: 1px solid #000;
    padding: 10px;
}

.ypsl-table td{
    padding: 4px 2px;
}

.lydTitle{
    font-size: 22px;
    text-align: center;
}

.printBtu{
    right: 0;
    position: absolute;
}

.yzd-brInfo{
    width: 758px;
    padding: 14px 0;
    display: inline-block;
    border: 1px solid #000;
    margin: 10px 0 -1px 0;
}

.yzd-brInfo > div{
    float: left;
}

.yzd-brInfo > div > span{
    font-size: 14px;
    margin: 6px;
}

.yzd-brInfo > div > span:last-child{
    margin: 10px;
}

.cqyzd{
    margin-bottom: 60px;
}
.height100{
    height: 100%;
}
#yzcl_hs{
    width: 80%;

}
.yzcl_div{
    width: 100%;
}
.font-14-654 {
    font-size: 14px;
    color: rgb(242, 166, 84);
}
.jbxx-box {
    padding: 7px 0px;
}
.jbxx-size {
    padding: 0px;
}

.padd-l-12 {
    padding-left: 12px;
}
.hzList{
    height: calc(100% - 77px);
}
.useritem .userlist .userKey {
    border-top: 1px solid #eeeeee;
    width: 155px;
    min-height: 30px;
    font-size: 14px;
    color: #7f8fa4;
    text-align: center;
    padding-right: 20px;
}
.userlist {
    display: flex;
    padding-bottom: 0;
}
.useritem .userlist:nth-child(odd) {
    background: #ffffff;
}
 .useritem .userlist {
    display: flex;
    padding-bottom: 0;
}
.useritem .userlist .userValue {
    width: 298px;
    padding: 6px 0;
    padding-left: 19px;
    font-size: 14px;
    border-left: 1px solid #eeeeee;
    color: #354052;
    text-align: center;
    border-top: 1px solid #eeeeee;
}
