.l-zk-left {
  width: 36.5%;
  float: left;
}
.l-zk-top {
  width: 100%;
  background: #fafafa;
  border: 1px solid #eeeeee;
  height: 50px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 12px;
  position: relative;
}
.l-zk-top .l-bgtx {
  width: 111px;
  position: absolute;
  right: 10px;
  top: 5px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.l-zk-top .l-bgtx span {
  display: block;
  position: relative;
  cursor: pointer;
}
.l-zk-top .l-bgtx span i {
  position: relative;
  width: 100%;
  height: 20px;
  text-align: center;
  display: block;
}
.l-zk-top .l-bgtx span em {
  width: 100%;
  text-align: center;
  display: block;
  color: #9fa9ba;
}
.l-zk-top .l-bgtx .active {
  color: #1abc9c;
}
.l-zk-top .l-bgtx .active em {
  color: #1abc9c;
}
.l-zk-top .l-bgtx .l-span-line:before {
  content: '';
  position: absolute;
  width: 1px;
  height: 15px;
  right: -12px;
  top: 14px;
  background: rgba(159, 169, 186, 0.5);
}
.l-zk-jysb {
  width: 100%;
  padding: 14px 7px 10px 7px;
  box-sizing: border-box;
}
.l-zk-jysb .zui-form .padd-l78 {
  padding: 0 0 0 78px;
}
.l-zk-jysb .zui-form .padd-l64 {
  padding: 0 0 0 64px;
}
.l-zk-jysb .zui-form .margin-l15 {
  margin-left: 15px;
}
.l-zk-jysb .zui-form .margin-l10 {
  margin-left: 10px;
}
.l-zk-jysb .zui-form-label {
  width: auto;
  padding: 0;
  line-height: 36px;
  color: #7f8fa4;
}
.zui-table-view .zui-table-body {
  border: none !important;
}
.zui-table-view table tr:last-child {
  border-bottom: 1px solid #eee;
}
.zui-table-view {
  min-height: 85vh;
}
.zui-table-view .zui-table-body {
  height: 70vh;
}
#divlink {
  margin-left: 86%;
}
.l-zk-right {
  width: 63%;
  float: right;
  position: relative;
}
.l-zk-right .zui-table-view {
  border-top: none;
}
.zui-date .datenox {
  left: 6px;
  color: #c5d0de;
}
.text-indent20 {
  text-indent: 20px;
}
.zklx-select:after {
  right: 13px;
}
.zk-right-fixed {
  position: absolute;
  bottom: 0px;
  right: 0;
  width: 100%;
  left: 0;
  z-index: 10000;
  height: 80px;
  background: #fefaf6;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.zk-right-fixed span {
  font-size: 14px;
  color: #7f8fa4;
}
.zk-right-fixed span i {
  font-size: 18px;
  color: #f2a654;
}
.tab-message {
  width: 100%;
  height: 46px;
  background: #1abc9c;
  line-height: 46px;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tab-message a {
  color: #fff;
}
.tab-message .icon-cha:before {
  top: 0;
}
.l-zk-ms {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  float: left;
  overflow: hidden;
  position: relative;
}
.l-zk-ms .l-zk-col-6 {
  width: 48%;
  float: left;
  margin-top: 20px;
}
.l-zk-ms .l-zk-col-6 label {
  color: #7f8fa4;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
}
.l-zk-ms .l-zk-col-6 label i {
  width: 60px;
  display: block;
  text-align: right;
}
.l-zk-ms .l-zk-col-6 label .l-data {
  position: absolute;
  width: 20px;
  height: 20px;
  left: 72px;
  top: 11px;
  display: block;
}
.l-zk-ms .l-zk-col-6 label .l-color35 {
  color: #354052 !important;
}
.l-zk-ms .l-zk-col-6:nth-child(2n) {
  margin-left: 5px;
  float: right !important;
}
.l-zk-ms .l-zk-col-6 .l-time,
.l-zk-ms .l-zk-col-6 .l-times,
.l-zk-ms .l-zk-col-6 .l-times2,
.l-zk-ms .l-zk-col-6 .l-time1 {
  text-indent: 17px;
}
.l-zk-ms .l-zk-col-6 .l-dw-after {
  position: absolute;
  right: 9px;
  width: auto;
  height: 20px;
  top: 9px;
  color: #1abc9c;
}
.l-zk-ms .l-zk-col-6 .l-label-left {
  float: left;
  color: #354052;
  width: 103px;
  line-height: 36px;
  margin-left: 15px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.l-zk-ms .l-zk-col-6 .l-label-right {
  float: left;
  line-height: 36px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.l-zk-ms .fr {
  float: right !important;
}
.l-zk-ms label {
  color: #7f8fa4;
}
.l-bottom-fixed {
  position: absolute;
  bottom: 0;
  height: 70px;
  right: 0;
  left: 0;
  border-top: 1px solid  #dfe3e9;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.l-width100 {
  width: 100%;
  height: 100%;
}
.l-textarea {
  width: 100%;
  border: 1px solid #dfe3e9;
  max-height: 600px;
  height: 600px;
  margin-top: 8px;
  float: left;
  text-indent: 10px;
  padding: 10px 10px;
  border-radius: 4px;
}
.l-right-tx {
  width: 100%;
  background: #fff;
  padding: 0 24px 90px;
  box-sizing: border-box;
  display: none;
  position: absolute;
  top: 0;
  z-index: 9999;
}
.l-right-tx h2 {
  font-size: 22px;
  color: #354052;
  width: 100%;
  text-align: center;
}
.l-right-tx .l-tx-title {
  width: 100%;
  padding: 20px 0;
  float: left;
}
.l-right-tx .l-tx-title span {
  display: block;
  color: #7f8fa4;
  float: left;
  line-height: 30px;
}
.l-right-tx .l-tx-title span:nth-child(3n) {
  text-indent: 70px;
}
.l-right-tx .l-tx-title .l-tx-col4 {
  width: 33.3333%;
}
.l-right-tx .l-tx-title .l-tx-col12 {
  width: 100%;
}
.l-right-tx .l-zktu {
  width: 100%;
  height: 500px;
  border: 1px solid #1abc9c;
  float: left;
}
.l-right-tx .l-zk-cdjg {
  width: 100%;
  padding: 0 10px 0 28px;
  box-sizing: border-box;
  float: left;
  position: relative;
}
.l-right-tx .l-zk-cdjg span {
  width: 100%;
  border-bottom: 1px dashed #1abc9c;
  display: block;
  height: 20px;
  position: relative;
}
.l-right-tx .l-zk-cdjg span:after {
  background: #fff;
  content: '测定结果';
  width: auto;
  padding-right: 5px;
  height: 30px;
  color: #1abc9c;
  position: absolute;
  left: 0;
  top: 4px;
  text-align: center;
  line-height: 30px;
}
.l-right-tx .l-zk-cdjg span.l-zk:after {
  content: '质控描述';
}
.l-right-tx .l-zk-time {
  width: 100%;
  float: left;
  margin: 25px 0;
}
.l-right-tx .l-time-top {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  background: #edf2f1;
}
.l-right-tx .l-time-top i {
  width: 33.333%;
  display: block;
  text-align: center;
  border-right: 1px solid #e9eee6;
}
.l-right-tx .l-time-top i:last-child {
  border-right: none;
}
.l-right-tx .l-time-bg {
  width: 100%;
  min-height: 312px;
}
.l-right-tx .l-time-bg span {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #e9eee6;
  border-top: none;
  line-height: 40px;
}
.l-right-tx .l-time-bg span i {
  width: 33.333%;
  display: block;
  text-align: center;
  border-right: 1px solid #e9eee6;
}
.l-right-tx .l-time-bg span i:last-child {
  border-right: none;
}
.l-right-tx .l-time-bg span:nth-child(2n) {
  background: #fdfdfd;
}
.l-right-tx .l-zk-popel {
  width: 100%;
  color: #7f8fa4;
  line-height: 24px;
  padding: 0 0 20px 26px;
  float: left;
  margin-top: 20px;
}
.l-right-tx .l-zk-bgr {
  width: 100%;
  padding-top: 60px;
  float: left;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.l-right-tx .l-zk-bgr span {
  font-size: 14px;
  color: #333333;
  width: auto;
  display: block;
  padding: 20px 30px 20px 30px;
}
.l-right-tx .l-zk-bgr span i {
  color: #f2a654;
}
.padd-r50 {
  padding-right: 50px !important;
}
.dishide {
  display: none;
}
.disShow {
  display: block;
}
.zui-table-view .zui-table-fixed.table-fixed-r {
  border-left: none;
}
.icon-ms:before {
  left: 19px;
}
.dang-tell .cell-2-0 {
  width: 50px !important;
}
.ls-td {
  width: 50px !important;
}
.ls-td .zui-table-cell {
  width: 50px !important;
}
