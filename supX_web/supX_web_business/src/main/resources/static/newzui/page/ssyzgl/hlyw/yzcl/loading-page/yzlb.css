body{
    padding: 10px!important;
}
.wrapper{
    background-color: #fff;
    min-height: 100%;
}
.userNameBg{
    background:#708f89;
    /*height:180px;*/
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    background-image: url("/newzui/pub/image/userImg.png");
    padding: 10px;
}
.flex{
    display: flex;
    align-items: center;
}
.userNameImg img{
    width: 100px;
}
.text-color{
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}
.zyh,.bq,.ys,.brzt,.bz,.cwh{
    margin-right: 60px;
}
.userCwh{
    margin-bottom: 4px;
}
.fyhj {
    margin-right: 39px;
}
.yjhj {
    margin-right: 104px;
}
.zyts {
    margin-right: 32px;
}
.phone {
    margin-right: 53px;
}
.hl {
    margin-right: 52px;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{
    color: #B0BFBB;
    text-decoration: underline;
}
.bryz-list{
    margin-bottom: 10px;
}
.ksys-btn{
    background-color: #fff;
}
.zui-form-label{
    width: 60px;
}
.zui-form .zui-inline {
    padding: 0 0px 0 60px;
}
.zui-inline, .zui-input-inline, .zui-select-inline{
    margin-right: 0px;
}
.todate{
    width: 448px;
}
.tisi{
    color:#f2a654;
    margin-top: 10px;
}
.icon-width{
    width: 24px;
    display: inline-block;
    height: 24px;
    position: relative;
}
.icon-width::before {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 0px;
    top: 0px;
}
.zt .zt-dsh{
    color:#f2a654;
}
.zt .zt-dzx{
    color:#ff4532;
}
.zt .zt-dly{
    color:#2885e2;
}
.zt .zt-yly{
    color:#13a950;
}
.zt .zt-dtz{
    color:#9e28e2;
}
.zt .zt-ytz{
    color:#757c83;
}

.zui-table-view .zui-table-body tr.huise td{
    color: rgb(167, 167, 167)!important;
}

.zui-table-view .zui-table-body tr.red td{
    color: rgb(189, 54, 47)!important;
}

.blRight{
    position: absolute;
    right: 7px;
    bottom: -40px;
}
.yzdImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/yzd.png");
}
.psjglrImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/psjglr.png");
}
.yzzxjlImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/yzzxjl.png");
}
.yzlb-height{
    height: calc(100% - 107px);
}
