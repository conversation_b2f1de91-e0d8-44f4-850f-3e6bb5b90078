    var s = new Date().getTime()
    var l = new Date()
    var e = l.setDate(l.getDate() + 1)
    var isTabel = new Vue({
        el: '.isTabels',
        mixins: [dic_transform, baseFunc, tableBase,scrollOps],
        data: {
            isTabelShow: false,
            minishow: true,
            isShowpopL: false,
            isShow: false,
            jsShow: false,
            qsShow: false,
            fShow: false,
            jsShowtime: false,
            zbShow: false,
            lsShow: false,
            pcShow: false,

            isXzxmShow: false,
            isPllrShow: false,
            isPltzShow: false,

            isXmShow: false,
            isPlxzBc: false,
            sjsj: '',
            ybbm: '',
            brxm: '',

            title: '',
            sj: '',
            titles: '',
            jsm: '',
            ybh: '',
            addCs: '',
            centent: '',
            cents: '',
            xmzb: [],
            xmzbDx: [],
            pydm: '',
            //指标详情
            zbxq: '',
            //根据标本编号查询的检验登记详情
            jydjxq: '',

            kaiguan: '',

            //指标列表
            zbbmList: [],
            bbbh_tz: '',
            zwmc: '',
            zbbm: '',
            mxList: [],
            tzcs: ''
        },
        //2018/07/18添加表格及时更新
        mounted:function () {
            changeWin()
        },
        methods: {
            //yq_根据标本编号和检验设备查询指定时间的检验登记列表
            bbbh4jydj: function () {

                if (this.ybbm == '') {
                    malert('请输入样本号', 'top', 'defeadted');
                    return;
                }

                var p = {
                    'sjsj': this.sjsj,
                    'bbbh': this.ybbm,
                    'zxsb': wrapper.param.zxsb
                };


                $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryAllJydj&parm=" + JSON.stringify(p), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length == 0) {
                            isTabel.ybbm = '';
                            isTabel.brxm = '';
                            malert('无此样本号的登记信息', 'top', 'defeadted');
                            isTabel.isXmShow = false;
                            isTabel.kaiguan = 'no';
                            return;
                        } else {
                            isTabel.jydjxq = json.d.list[0];
                            isTabel.isXmShow = true;
                            isTabel.brxm = json.d.list[0].brxm;
                            isTabel.kaiguan = 'yes';
                        }
                    } else {
                        malert("获取申请失败" + json.c);
                        return;
                    }
                });


            },
            //批量新增 确认按钮
            plxzBc: function () {
                if (this.ybbm == '') {
                    malert('请输入样本号', 'top', 'defeadted');
                    return;
                }

                var data = {
                    'jydj': this.jydjxq,
                    'jyxm': [{
                        'zbbmList': isTabel.zbxq
                    }]
                };
                this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=qllr_bc', JSON.stringify(data)).then(
                    function (data) {
                        console.log(data);
                        if (data.body.a == 0) {
                            //成功回调提示
                            malert('保存结果成功', 'top', 'success');
                            var p = parseInt(isTabel.ybbm);
                            isTabel.ybbm = p + 1;
                            wrapper.sx();

                            isTabel.bbbh4jydj();

                            if (isTabel.kaiguan == 'no') {
                                isTabel.isXmShow = false;
                                this.jydjxq = '';
                                isTabel.zbxq = '';
                                return;
                            }

                        }

                    },
                    function (error) {
                        malert(error, 'top', 'success');
                    });


            },
            //指标名称
            zhibiao: function () {
                this.zbShow = true;
            },
            //选中当前值
            dangqian: function (pd) {
                /*var inp_text=event.target.innerHTML;
                $("#zbText").val(inp_text);*/
                this.zwmc = pd.zwmc;
                this.zbbm = pd.zbbm;
                console.log(this.zbbm);

                this.zbShow = false;

            },
            //取消
            quxiao: function () {
                this.isTabelShow = false;
            },
            //确定
            queding: function () {
                this.isTabelShow = false;

            },
            //结果批量录入双击删除
            deletes: function (event) {
                event.currentTarget.remove();
            },
            detail: function (data) {
                this.zbxq = data.zbbmList;
            },
            //新增指标项目
            zbAdd: function (pd, type) {
                //双击时将此指标转换为检验登记明细 wap.pd
                var data = {
                    'jydj': wap.pd,
                    'jyxm': [pd]
                };
                if (type == 'zh') {//组合
                    this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=jyxm4jydjmx', JSON.stringify(data)).then(
                        function (json) {
                            console.log(json);
                            if (json.body.a == 0) {
                                var d = json.body.d;
                                var List = [];
                                for (var i = 0; i < wap.pd.jydjmxList.length; i++) {//先找出相同的元素
                                    for (var j = 0; j < d.length; j++) {
                                        if (wap.pd.jydjmxList[i].zbxm == d[j].zbxm) {
                                            List.push(d[j]);
                                        }
                                    }
                                }
                                var xzList = [];
                                //d减去List为需要新增的项目
                                var str = List.join("&quot;&quot;")
                                for (var e in d) {
                                    if (str.indexOf(d[e]) == -1) {
                                    	if(d[e].sjlx=='1' && d[e].valueN== null){
                                    		d[e].valueN=d[e].xzqsz;
                                    	}
                                    	if(d[e].sjlx=='2' || d[e].sjlx=='3'){
                                    		d[e].valueT=d[e].xzqsz;
                                    	}

                                        xzList.push(d[e]);
                                    }
                                }

                                Array.prototype.push.apply(xzList, wap.pd.jydjmxList);
                                console.log("新增的list:" + xzList);
                                wap.pd.jydjmxList = xzList;

                                this.isTabelShow = false;
                            }
                        },
                        function (error) {
                            malert(error, 'top', 'success');
                        });


                } else {//单项
                    var a = {
                        'jydj': wap.pd,
                        'jyxm': [{
                            'zbbmList': [pd]
                        }]
                    };
                    this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=jyxm4jydjmx', JSON.stringify(a)).then(
                        function (json) {
                            console.log(json);
                            if (json.body.a == 0) {
                                var d = json.body.d;
                                var List = [];
                                for (var i = 0; i < wap.pd.jydjmxList.length; i++) {//先找出相同的元素
                                    for (var j = 0; j < d.length; j++) {
                                        if (wap.pd.jydjmxList[i].zbxm == d[j].zbxm) {
                                            List.push(d[j]);
                                        }
                                    }
                                }
                                var xzList = [];
                                //d减去List为需要新增的项目
                                var str = List.join("&quot;&quot;")
                                for (var e in d) {
                                    if (str.indexOf(d[e]) == -1) {
                                        xzList.push(d[e]);
                                    }
                                }
                                console.log(xzList)

                                Array.prototype.push.apply(xzList, wap.pd.jydjmxList);
                                console.log("新增的list:" + xzList);
                                wap.pd.jydjmxList = xzList;
                                this.isTabelShow = false;
                            }
                        },
                        function (error) {
                            malert(error, 'top', 'success');
                        });
                }

            },

            //批量调整---------
            query_tz: function () {

                var bbbh = isTabel.bbbh_tz;

                var a, b;
                if (bbbh.indexOf("-") >= 0) {
                    var x = bbbh.split("-");
                    a = x[0];
                    b = x[1];
                } else {
                    malert("样本号之间需要用-号隔开！", 'top', 'defeadted');
                    return;
                }

                var zbxm = isTabel.zbbm;

                if (zbxm == '') {
                    malert("请选择需要调整的指标项目！", 'top', 'defeadted');
                    return;
                }

                var sjsjMx = isTabel.sjsj;

                var data = {
                    'bbbh': a,
                    'bbbhEnd': b,
                    'zbxm': zbxm,
                    'sjsjMx': sjsjMx
                };

                $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryPltz_mx&parm=" + JSON.stringify(data), function (json) {
                    if (json.a == 0) {
                        isTabel.mxList = json.d.list;
                    } else {
                        malert("查询失败" + json.c);
                        return;
                    }
                });

            },
            tzjg: function () {
                var p = this.tzcs;
                if (p.length > 2) {
                    if (p.indexOf("R*") >= 0) {//乘
                        for (var int = 0; int < isTabel.mxList.length; int++) {
                            isTabel.mxList[int].valueN = isTabel.mxList[int].valueN * parseFloat(p.substring(2));
                        }
                    }
                    if (p.indexOf("R/") >= 0) {//除
                        for (var int = 0; int < isTabel.mxList.length; int++) {
                            isTabel.mxList[int].valueN = isTabel.mxList[int].valueN / parseFloat(p.substring(2));
                        }
                    }
                    if (p.indexOf("R+") >= 0) {//加
                        for (var int = 0; int < isTabel.mxList.length; int++) {
                            isTabel.mxList[int].valueN = isTabel.mxList[int].valueN + parseFloat(p.substring(2));
                        }
                    }
                    if (p.indexOf("R-") >= 0) {//减
                        for (var int = 0; int < isTabel.mxList.length; int++) {
                            isTabel.mxList[int].valueN = isTabel.mxList[int].valueN - parseFloat(p.substring(2));
                        }
                    }
                }

            },
            tzbc: function () {
                var json = '{"list":' + JSON.stringify(isTabel.mxList) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=updatePltzMx', json).then(
                    function (json) {
                        console.log(json);
                        if (json.body.a == 0) {
                            malert('调整成功', 'top', 'success');
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'success');
                    });
            }

        },
        watch: {
            'pydm': function () {
                wap.xmzblb();
            },
            'zwmc': function () {
                var data = {
                    'zwmc': isTabel.zwmc
                };
                wrapper.queryZbbm(data);
            },
            'tzcs': function () {
                isTabel.tzjg();
            }
        }
    })
    var wrapper = new Vue({
        el: '.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, printer,mformat,scrollOps],
        data: {
            isShowpopL: false,
            isTabelShow: false,
            isShow: false,
            fShow: false,
            jsShowtime: false,
            pcShow: false,
            lsShow: false,
            qsShow: false,
            ckShow: false,
            title: '',
            sj: '',
            titles: '',
            jsm: '',
            ybh: '',
            addCs: '',
            centent: '',
            cents: '',
            //yq
            printList: [],
            jysbList: [],
            param: {
                page: 1,
                rows: 10,
                total: '',
                time: '',
                zxsb: '',
                bah: ''
            }
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate()) + ' '
                    + (d.getHours() < 10 ? '0' + d.getHours() : d.getHours()) + ':' + (d.getMinutes() < 10 ? '0' + d.getMinutes() : d.getMinutes()) + ':' + (d.getSeconds() < 10 ? '0' + d.getSeconds() : d.getSeconds())
            }
        },
        created: function () {
            this.param.time = this.formDate(s) + ' - ' + this.formDate(e);
            isTabel.sjsj = this.formDate(s)
        },
        methods: {
            print: function (cfList) {
            	 //特殊设备数据处理
                if(wrapper.bglx_tran[wrapper.printList[0]['bglx']]=='通用报告1'){//陆坪尿液数据处理
                	var index=11;
                	var list=[];
                	for (var int = 0; int <= 10; int++) {
                		var i=int+index;
						if(i<=18 && cfList[i] != undefined){
							cfList[int].zbxmmcjj=cfList[i].zbxmmc;
							cfList[int].valueNjj=cfList[i].valueN;
							cfList[int].ckzTjj=cfList[i].ckzT;
						}
						list.push(cfList[int])
					}
                	cfList=list;
                }


                // 查询打印模板
                var json = {repname:wrapper.bglx_tran[wrapper.printList[0]['bglx']]};
               // var json = {repname: '通用报告'};//这个名字去匹配后台模板名字
                $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    if (json.d.length == 0) {
                        //json.d[0] = printTemplets.getTempletByName('陆坪中心卫生院生化检验报告单');
                    	json.d[0] = printTemplets.getTempletByName(wrapper.bglx_tran[wrapper.printList[0]['bglx']]);
                    }
                    // 清除打印区域
                    wrapper.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    // 为打印前生成数据
                    wrapper.printContentNew(wrapper.printList[0]);
                    wrapper.printTrend(cfList);
                    // 开始打印
                    //wrapper.setPageSetup()
                    window.print();
                });
            },
            getPrintData: function (list) {
                var json = '{"list":' + JSON.stringify(list) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=queryJydjPrintData', json).then(function (data) {
                        if (data.body.d != null) {
                            wrapper.printList = data.body.d.list;
                            // 性别转换
                            wrapper.printList[0]['xb'] = wrapper.brxb_tran[wrapper.printList[0]['xb']];
                            // 年龄转换
                            wrapper.printList[0]['nldw'] = wrapper.nldw_tran[wrapper.printList[0]['nldw']];
                            //打印行数
                            var hs=wrapper.printList[0]['dyhs']==null ? 20 : wrapper.printList[0]['dyhs'];
                            //打印数据分页

                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            },

            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            },
            newAdd: function () {
                pop.isShowpopL = true
                pop.isShow = true;
            },
            AddMdel: function () {
                wap.ckShow = true;
                wap.xzShow=true;
                wap.fqShow=false;
                wap.open();
                wap.pdxz.sqrq = wrapper.$options.filters.formDate(new Date());
                wap.pdxz.cyrq = wrapper.$options.filters.formDate(new Date());
                wap.pdxz.ybhsrq = wrapper.$options.filters.formDate(new Date());
                wap.pdxz.sjsj = wrapper.$options.filters.formDate(new Date());


                /*$.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryMaxYbh&parm=" + JSON.stringify(wrapper.param), function (json) {
                    if (json.a == 0) {
                        wap.pdxz.bbbh = json.d;

                    } else {
                        malert("获取样本号失败" + json.c);
                        return;
                    }
                });*/

            },

            //作废
            zuofei: function () {
                pop.isShowpopL = true
                pop.isShow = true;
                pop.flag = false;
                pop.dyShow = true;
                pop.title = '作废检验申请';
                pop.centent = '确定作废选中的项目吗？';
                var List = [];
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            List.push(bodyPd.jydjList[i]);
                        }
                    }
                    var json = '{"list":' + JSON.stringify(List) + '}';
                    this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updatezf_zy', json).then(
                        function (data) {
                            console.log(data);
                            if (data.body.a == 0) {
                                //成功回调提示
                                malert('作废成功', 'top', 'success');
                                wrapper.sx();
                            }

                        },
                        function (error) {
                            malert(error, 'top', 'success');
                        });
                }
            },
            //取消保存标志
            cancelBc: function () {
                // if (common.openConfirm("确认取消保存标志吗？", function () {
                //
                //     })) {
                //     return false;
                // }
                // pop.isShowpopL = true
                // pop.dyShow = true;
                // pop.isShow = true;
                // pop.flag = false;
                // pop.title = '常规检验';
                // pop.centent = '你确定要取消保存标志吗？';
            },
            //打印
            daying: function () {
                window.print();
            },
            //查看设备原始数据
            shebei: function () {
                isTabel.isTabelShow = true;
                isTabel.isShow = true;
                isTabel.lsShow = false;
                isTabel.pcShow = false;
                isTabel.jsShow = false;
                isTabel.jsShowtime = false;
                isTabel.qsShow = false;
                $('.pop-cg> li').css({'width': '25%'});
                isTabel.titles = "查看设备原始数据";
                isTabel.jsm = "样本号/项目";
                isTabel.addCs = "蓝色为未登记申请的样本结果";
            },
            //费用情况
            feiyong: function () {
                pop.isShowpopL = true;
                pop.isShow = true;
                pop.flag = true;
                pop.dyShow = false;
                pop.dyShow = false;
                var List=[]
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            List.push(bodyPd.jydjList[i]);
                        }
                    }
                    $.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryFyDetail&param=" + JSON.stringify(List[0])).then(
                        function (data) {
                            if (data.a == 0) {
                                pop.fylb = data.d.list;
                                pop.brxm = List[0].brxm;
                                malert('作废成功', 'top', 'success');
                            }

                        },
                        function (error) {
                            malert(error, 'top', 'success');
                        });
                }


            },
            //批量录入
            piliang: function () {
                isTabel.isPltzShow = false;
                if (wrapper.param.zxsb != '') {
                    isTabel.isPllrShow = true;
                    isTabel.isTabelShow = true;
                    isTabel.isShow = false;
                    isTabel.jsShow = true;
                    isTabel.jsShowtime = true;
                    isTabel.pcShow = false;
                    isTabel.lsShow = false;
                    isTabel.qsShow = true;
                    isTabel.titles = "结果批量录入";
                    isTabel.sj = "样本时间";
                    isTabel.jsm = "样本号";
                    isTabel.addCs = "双击删除";
                    isTabel.isPlxzBc = true;
                    var data = {
                        pydm: ''
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryXmzb&parm=" + JSON.stringify(data), function (json) {
                        if (json.a == 0) {
                            isTabel.xmzb = json.d.list;
                        } else {
                            malert("查询失败" + json.c);
                            return;
                        }
                    });
                } else {
                    malert("请选择检验设备", 'top', 'defeadted');
                }


            },
            //批次调整
            pici: function () {
                isTabel.isPllrShow = false;
                isTabel.isPltzShow = true;
                isTabel.isTabelShow = true;
                isTabel.isShow = false;
                isTabel.pcShow = true;
                isTabel.jsShow = false;
                isTabel.jsShowtime = true;
                isTabel.lsShow = false;
                isTabel.qsShow = true;
                isTabel.titles = "检验结果批次调整";
                isTabel.sj = "样本时间";
                isTabel.jsm = "样本号";
                isTabel.addCs = "说明:调整值输入R*1.5既原来结果的1.5倍，输入R／0.8即原来结果的0.8次调整只针对已保存结果，未审核的样本，如果未保存结果或是已审核不能调整。格式为：R*1.5 前面R不变后面加运行算+-*／后面加值";

                var data = {
                    'zwmc': ''
                };
                this.queryZbbm(data);
            },

            queryZbbm: function (data) {
                $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryJyzb&parm=" + JSON.stringify(data), function (json) {
                    if (json.a == 0) {
                        isTabel.zbbmList = json.d.list;
                        console.log(isTabel.zbbmList);
                    } else {
                        malert("查询失败" + json.c);
                        return;
                    }
                });
            },

            //yq
            getybh: function () {
                //TODO 目前默认取设备的最大样本号 后续判断
                var obj = {
                    zxsb: wap.pd.zxsb
                };
                $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryMaxYbh&parm=" + JSON.stringify(obj), function (json) {
                    if (json.a == 0) {
                        wap.pd.bbbh = json.d;

                    } else {
                        malert("获取样本号失败" + json.c);
                        return;
                    }
                });
            },
            jysb: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    console.log(json);
                    if (json.a == 0) {
                        wrapper.jysbList = json.d.list;
                    } else {
                        malert("获取申请检验设备失败" + json.c);
                        return false;
                    }
                });
            },
            getutil: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=gltjutil&yq=", function (json) {
                    wap.util = json.d;
                });
                var d={
                	parm:'',
                };
                $.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=xzjgQuery&parm="+JSON.stringify(d) , function(json) {
	        		console.log(json);
	        		if(json.a == "0"){
	        			wap.xlxz = json.d.list;
	        		}
	        	});

            },
            sx: function () {
                //解析时间
            	 if (this.param.time != null) {
                     var times = this.param.time.split(" - ");
                     this.param.sqrq = times[0];
                     this.param.sqrqEnd = times[1];
                 }
                $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryAllJydj&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                        bodyPd.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        bodyPd.jydjList = json.d.list;
                    } else {
                        malert("获取申请失败" + json.c);
                        return;
                    }
                });
            },
            topbc: function () {
                if (wap.pdxz.brxm == '') {
                    malert("请输入病人姓名", 'top', 'defeadted');
                    return;
                }
                if (wap.pdxz.ksbm == '') {
                    malert("请选择申请科室", 'top', 'defeadted');
                    return;
                }
                if (wap.pdxz.sqys == '') {
                    malert("请选择申请医师", 'top', 'defeadted');
                    return;
                }
                if (wap.pdxz.jyxm == '') {
                    malert("请选择检验项目", 'top', 'defeadted');
                    return;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=xz', JSON.stringify(wap.pdxz)).then(
                    function (data) {
                        console.log(data);
                        if (data.body.a == 0) {
                            //成功回调提示
                            malert('保存登记成功', 'top', 'success');
                            wrapper.sx();

                        }

                    },
                    function (error) {
                        malert(error, 'top', 'success');
                    });
            },
            sh: function () {//审核
                var List = [];//选中的数据列
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            List.push(bodyPd.jydjList[i]);
                        }
                    }
                    var json = '{"list":' + JSON.stringify(List) + '}';
                    this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updatecgjy_sh', json).then(
                        function (data) {
                            if (data.body.a == 0) {
                                //成功回调提示
                                malert('审核成功', 'top', 'success');
                                wrapper.sx();

                                //打印审核成功的数据
                               /* setTimeout(function () {
                                    wrapper.getPrintData(List);
                                }, 2000)*/
                            }

                        },
                        function (error) {
                            malert(error, 'top', 'defeadted');
                        });
                }
            },
            zf: function () {
                var List = [];
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            List.push(bodyPd.jydjList[i]);
                        }
                    }
                    var json = '{"list":' + JSON.stringify(List) + '}';
                    this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updatezf_zy', json).then(
                        function (data) {
                            console.log(data);
                            if (data.body.a == 0) {
                                //成功回调提示
                                malert('作废成功', 'top', 'success');
                                this.jsyy = '';
                                this.jscl = '';
                                wrapper.sx();
                            }

                        },
                        function (error) {
                            malert(error, 'top', 'defeadted');
                        });
                }
            },
            hqsq : function(event){
            	event.target.disabled=true;
            	event.target.innerText='获取中....';

            	if (this.param.time != null) {
                    var times = this.param.time.split(" - ");
                    this.param.sqrq = times[0];
                    this.param.sqrqEnd = times[1];
                }
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysq4Cgjy&yq=" + JSON.stringify(this.param), function (json) {
                	console.log(json);
                	if (json.a == 0) {
                		malert(json.d);
                		wrapper.sx();
                    } else {
                        malert("获取申请失败" + json.c,'top', 'defeadted');
                        return false;
                    }
                	event.target.disabled=false;
                	event.target.innerText='获取申请';
                });
            }

        },
        watch: {
            'param.zxsb': function () {
                wrapper.sx();
            },
            'param.bah': function () {
                wrapper.sx();
            },
            'param.time': function () {
                wrapper.sx();
            }
        }
    });
    var pop = new Vue({
        el: '#pop',
        mixins: [dic_transform, baseFunc, tableBase,scrollOps],
        data: {
            isShowpopL: false,
            isTabelShow: false,
            isShow: false,
            jsShow: false,
            pcShow: false,
            flag: false,
            fShow: false,
            wjzShow:false,
            dyShow: false,
            jsShowtime: false,
            qsShow: false,
            popContent:{},
            title: '',
            sj: '',
            titles: '',
            jsm: '',
            ybh: '',
            addCs: '',
            centent: '',
            cents: '',
            fylb: '',
            brxm: '',
            XsList:[

                {
                    bgxs:'确认为危机值',
                    ksbm:'01',
                },
                {
                    bgxs:'危机值信息有误',
                    ksbm:'02',
                },
            ],
        },
        methods: {
        //关闭核查原因弹窗
            Popclose:function () {
                pop.isShowpopL=false;
                pop.wjzShow=false;
            },
          //核查原因弹窗确定按钮操作
            popConfirm:function () {
                pop.isShowpopL=false;
                pop.wjzShow=false;
            },
            //确定删除
            delOk: function () {
                var List = [];
                List.push(wap.pd.jydjmxList[wap.delZbxmIndex]);
                var json = '{"list":' + JSON.stringify(List) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=deleteMx', json).then(
                    function (data) {
                        if (data.body.a == 0) {
                            //成功回调提示
                            malert('删除成功', 'top', 'success');
                            wap.pd.jydjmxList.splice(wap.delZbxmIndex, 1);
                        }

                    },
                    function (error) {
                        malert(error, 'top', 'success');
                    });

                pop.isShowpopL = false;
                pop.isShow = false;
            }
        }
    });
    var bodyPd = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            isShowpopL: false,
            isTabelShow: false,
            pcShow: false,
            isShow: false,
            qsShow: false,
            jsShowtime: false,
            fShow: false,
            jsShow: false,
            ckShow: false,
            title: '',
            sj: '',
            titles: '',
            jsm: '',
            ybh: '',
            addCs: '',
            centent: '',
            cents: '',
            //yq
            totlePage: 0,
            jydjList: [],
            page: '',

            rows: 10
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        mounted:function () {
          changeWin()
        },
        methods: {

            edit: function (pds) {
                // wap.ckShow=false;
                // wap.title='打开详细信息>>';
                $('.tab-a').show();
                wap.xzShow=false;
                wap.fqShow=false;
                // console.log($('.tab-table').offset().top);
                /*$('.tab-box-list').scroll(function () {
                    var scrTop = $('.tab-table').offset().top;
                    if (scrTop < 90) {
                        console.log(scrTop);
                        $('.tab-table').css({
                            'position': 'absolute',
                            'top': '90px',
                            'z-index': '999'
                        })
                    } else {
                        $('.tab-table').css({
                            'position': 'relative',
                            'top': '0'
                        })
                    }
                })*/

                // 展示注册记录
                wap.isFold = true;
                // $("#isFold").addClass('side-form-bg');
                // $("#brzcList").removeClass('ng-hide');
                wap.open();

                $(".gbxz").focus();
                bodyPd.xxxq(pds);


            },

            xxxq:function(pds){
                // var sendmsg={
                //     msgtype:'402',
                //     ksbm:'0013',
                //     yljgbm:'000014',
                //     yqbm:'014',
                //     msg:'病人【某人】的检验报告!',
                //     toaddress:'/',
                //     sbid:'2018060023_2018-10-10 22:52:24',
                //     ylbm:'N030042002',
                //     pagename:'检验报告'
                // };
                // console.log(sendmsg);
                // window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                 $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryJydjDetails&parm=" + JSON.stringify(pds), function (json) {
                     if (json.a == 0) {
                         if (json.d.list.length > 0) {
                             //时间转换
                             json.d.list[0].sqrq = wrapper.$options.filters.formDate(json.d.list[0].sqrq);
                             json.d.list[0].cyrq = wrapper.$options.filters.formDate(json.d.list[0].cyrq);
                             json.d.list[0].ybhsrq = wrapper.$options.filters.formDate(json.d.list[0].ybhsrq);
                             json.d.list[0].sjsj = wrapper.$options.filters.formDate(json.d.list[0].sjsj);
                             json.d.list[0].bbsjscsj = wrapper.formDate(s);
                             wap.pd = json.d.list[0];


                             //判断危机值
                             var wjz=false;
                             for (var int = 0; int < wap.pd.jydjmxList.length; int++) {
								if(wap.pd.jydjmxList.wjzType == 'yes'){
									wjz=true;
								}
							}

                             // TODO 调用websocket推送消息处理

                             var user_info = JSON.parse(this.getcookie("user_info"));
                             var yqbmCookie = user_info.yqbm;
                             var yljgbmCookie = user_info.yljgbm;
                             var userCookie = user_info.userName;

                             if(wjz){//危机值
                                 var sendmsg={
                            		msgtype:'101',
                            		ksbm:'0013',
                            		yljgbm:yljgbmCookie,
                            		yqbm:yqbmCookie,
                            		msg:'有病人某检查达到危机值!请注意查看',
                            		toaddress:'page/lis/bqgl/wjzgl/wjzgl.html',
                            		sbid:userCookie +"_"+jgbm+"_" + bodyPd.fDate(new Date(),'YY'),
                            		ylbm:'N030060022001',
                            		pagename:'危急值'
                            	 };
                            	window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                             }else{//检验报告通知
                                 var sendmsg={
                                     msgtype:'402',
                                     ksbm:'0013',
                                     yljgbm:yljgbmCookie,
                                     yqbm:yqbmCookie,
                                     msg:'【检验报告】有病人出了检验报告！',
                                     toaddress:'page/lis/rcyw/bgcx/bgcx.html',
                                     sbid:userCookie +"_"+jgbm+"_" + bodyPd.fDate(new Date(),'YY'),
                                     ylbm:'N030060022001',
                                     pagename:'检验报告'
                                 };
                                 window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                             }
                         }

                         if (wap.pd.bbbh == null || wap.pd.bbbh == 'undefined') {
                             wrapper.getybh();
                         }
                     } else {
                         malert("查询失败" + json.c);
                         return;
                     }
                 });

            },
            getData: function () {
                common.openloading('.zui-table-view')
                wrapper.param.rows = this.rows;
                wrapper.param.page = this.page;
                wrapper.sx();
                common.closeLoading()
            },
            //核查
            check:function () {
             pop.isShowpopL=true;
             pop.wjzShow=true;
            },
            //发起危急值
            Launch:function () {
                wap.xzShow=false;
                wap.fqShow=true;
                wap.open();
            }

        }
    });

    var wap = new Vue({
        el: '#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],
        data: {
            isShowpopL: false,
            isShow: false,
            isTabelShow: false,
            xzShow:false,
            num:0,
            BgList:[],
            ryxmList:[],
            popContent:{},
            number:0,
            fqShow:false,
            pcShow: false,
            jsShowtime: false,
            flag: false,
            jsShow: false,
            qsShow: false,
            fShow: false,
            ckShow: true,
            title: '',
            sj: '',
            titles: '',
            jsm: '',
            ybh: '',
            addCs: '',
            centent: '',
            cents: '',
            isFold: false,
            pd: {},
            util: '',
            xlxz:[],
            pdxz: {
                lx: '',
                bah: '',
                brxm: '',
                xb: '',
                nl: '',
                ksbm: '',
                ksmc: '',
                sqys: '',
                sqysxm: '',
                cwh: '',
                lczd: '',
                jyxm: '',
                jyxmmc: '',
                ybmc: '',
                yblx: '',
                zklx: '',
                ybbm: '',
                sqrq: '',
                fyje: '',
                jyxh: '',
                zxsb: '',
                zxsbmc: '',
                cyrq: '',
                ybhsrq: '',
                sjsj: '',
                bz: '',
                bbbh: '',
                jzbz: ''
            },
            pdxz_non: {
                lx: '',
                bah: '',
                brxm: '',
                xb: '',
                nl: '',
                ksbm: '',
                ksmc: '',
                sqys: '',
                sqysxm: '',
                cwh: '',
                lczd: '',
                jyxm: '',
                jyxmmc: '',
                ybmc: '',
                yblx: '',
                zklx: '',
                ybbm: '',
                sqrq: '',
                fyje: '',
                jyxh: '',
                zxsb: '',
                zxsbmc: '',
                cyrq: '',
                ybhsrq: '',
                sjsj: '',
                bz: '',
                bbbh: '',
                jzbz: ''
            },
            edShow:true,
            zkShow:true,
            shShow:false,
            delZbxmIndex: '',
            XsList:[

                {
                    bgxs:'系统',
                    ksbm:'01',
                },
                {
                    bgxs:'系统+电话',
                    ksbm:'02',
                },
            ],
            is_state:{
                '0':' ',
                '1':' ',
                '2':' '
            },
            is_result:{
                '0':'25.00g/L',
                '1':'阴性',
                '2':'25.00mmol/L'
            },
            datas: [
                {
                    pydm:'pydm',
                    name: '白蛋白白蛋白白蛋白白蛋白白蛋白白蛋白',
                    result: '0',
                    wxbz:'1',
                    state: '0',
                    ckqj:'38.0-50.0'
                },
                {
                    pydm:'pydm',
                    name: '白蛋白',
                    result: '1',
                    wxbz:'0',
                    state: '1',
                    ckqj:'23-300'
                },
                {
                    pydm:'pydm',
                    name: '白蛋白',
                    result: '2',
                    wxbz:'1',
                    state: '2',
                    ckqj:'38.0-50.0'
                }
            ],
            datas1: [
                {
                    pydm:'pydm',
                    name: '白蛋白白蛋白白蛋白白蛋白白蛋白白蛋白',
                    result: '0',
                    wxbz:'1',
                    state: '0',
                    ckqj:'38.0-50.0'
                },
                {
                    pydm:'pydm',
                    name: '白蛋白',
                    result: '1',
                    wxbz:'0',
                    state: '1',
                    ckqj:'23-300'
                },
                {
                    pydm:'pydm',
                    name: '白蛋白',
                    result: '1',
                    wxbz:'1',
                    state: '1',
                    ckqj:'38.0-50.0'
                }
            ]

        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods: {
        //侧窗关闭
        close:function () {
            this.num=0;
        },
        //侧窗打开
        open:function () {
          this.num=1;
        },
        //tab切换
            tabBg: function (index) {
                this.number = index;
            },

            //报告科室
            getKsData: function(){
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm", function(json) {
                    wap.BgList = json.d.list;
                });
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyjszgdm&dg=" + JSON.stringify(this.param), function(json) {
                    wap.ryxmList = json.d.list;
                    wap.ryxmList = json.d.list;
                });
            },
            //发起确定
            lanchOk:function () {
            malert('发起','top','success');
            },
            //报告形式选择
            resultChanges:function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "ksbm":
                        Vue.set(this.popContent, 'ksbm', val[0]);
                        Vue.set(this.popContent, 'bgxs', val[4]);
                        //报告形式：下拉选择系统+电话或系统（如选择系统则下⽅方电话接收⼈人下拉框不不显示）
                        if(val[0]=='01'){
                            $('#phone').hide();
                        }else{
                            $('#phone').show();
                        }
                        break;
                    default:
                        break;
                }
            },



            queryJydjDetailsByInterfaceData: function (data) {
                //data.jydjmxList=[];//清空，减少请求数据量
                var obj = JSON.stringify(data);
                var d = JSON.parse(obj);
                d.jydjmxList = [];
                this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=queryJydjDetailsByInterfaceData', JSON.stringify(d)).then(
                        function (json) {
                        	json=json.body;
                        	if (json.a == 0) {
                                if (json.d.list.length > 0) {
                                    if (json.d.list[0].jydjmxList.length > 0) {
                                        wap.pd.jydjmxList = json.d.list[0].jydjmxList;
                                        if(json.d.list[0].scfs=='1'){
                                        	bodyPd.xxxq(d);
                                        }
                                        malert("数据获取成功！");
                                    } else {
                                        malert("该样本号数据未生成，请稍后再试！", 'top', 'defeadted');
                                    }
                                } else {
                                    malert("该样本号数据未生成，请稍后再试！", 'top', 'defeadted');
                                }
                            } else {
                                malert("数据获取失败！" + json.c, 'top', 'defeadted');
                                return;
                            }

                        },
                        function (error) {
                            malert(error, 'top', 'defeadted');
                        });

            },
            xmzblb: function () {
                //弹出新增项目指标列表
                var data = {
                    pydm: isTabel.pydm.toUpperCase()
                };
                $.getJSON("/actionDispatcher.do?reqUrl=LisCgjy&types=queryXmzb&parm=" + JSON.stringify(data), function (json) {
                    if (json.a == 0) {
                        isTabel.xmzb = json.d.list;
                        var List = [];
                        if (json.d.list.length > 0) {
                            for (var int = 0; int < json.d.list.length; int++) {
                                if (json.d.list[int].zbbmList.length > 0) {
                                    Array.prototype.push.apply(List, json.d.list[int].zbbmList);
                                }
                            }
                        }
                        isTabel.xmzbDx = List;
                    } else {
                        malert("查询失败" + json.c);
                        return;
                    }
                });


            },
            //查看更多信息
            // ckMore: function () {
            //     this.ckShow = !this.ckShow
            //     if (this.ckShow) {
            //         this.title = "关闭申请人信息>>"
            //     } else {
            //         this.title = "查看申请人信息>>"
            //     }
            // },
            AddMdels: function () {
                isTabel.isXzxmShow = true;
                isTabel.isTabelShow = true;
                isTabel.isShow = false;
                isTabel.jsShow = false;
                isTabel.pcShow = false;
                isTabel.lsShow = true;
                isTabel.jsShowtime = false;
                isTabel.qsShow = false;
                isTabel.titles = "新增指标项目";
                isTabel.jsm = "检索码";
                isTabel.addCs = "双击增加，蓝色为组合";

                this.xmzblb();


            },
            // //确定
            confirms: function () {
                if (this.pd.brxm == '') {
                    malert("请输入病人姓名", 'top', 'defeadted');
                    return;
                }
                if (this.pd.ksbm == '') {
                    malert("请选择申请科室", 'top', 'defeadted');
                    return;
                }
                if (this.pd.sqys == '') {
                    malert("请选择申请医师", 'top', 'defeadted');
                    return;
                }
                if (this.pd.jyxm == '') {
                    malert("请选择检验项目", 'top', 'defeadted');
                    return;
                }
                //数据判断 当valueN为文本时，将valueN的值转到valueT中
                var mx = this.pd.jydjmxList;
                for (var int = 0; int < mx.length; int++) {
                    var valueN = mx[int].valueN;
                    if (isNaN(Number(valueN))) {
                        this.pd.jydjmxList[int].valueT = valueN;
                        this.pd.jydjmxList[int].valueN = null;
                    }
                }
                this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=xzdj', JSON.stringify(this.pd)).then(
                    function (data) {
                        if (data.body.a == 0) {
                            //成功回调提示
                            malert('保存结果成功', 'top', 'success');
                            //wrapper.sx();
                            var x=JSON.stringify(this.pd);
                            var j=JSON.parse(x);
                            j.jydjmxList=[];
                            bodyPd.xxxq(j);

                        }

                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });

            },
            //审核打印
            shdy:function(event){
            	 if (this.pd.brxm == '') {
                     malert("请输入病人姓名", 'top', 'defeadted');
                     return;
                 }
                 if (this.pd.ksbm == '') {
                     malert("请选择申请科室", 'top', 'defeadted');
                     return;
                 }
                 if (this.pd.sqys == '') {
                     malert("请选择申请医师", 'top', 'defeadted');
                     return;
                 }
                 if (this.pd.jyxm == '') {
                     malert("请选择检验项目", 'top', 'defeadted');
                     return;
                 }

                 event.target.disabled=true;
             	 event.target.innerText='提交中....';
                 //数据判断 当valueN为文本时，将valueN的值转到valueT中
                 var mx = this.pd.jydjmxList;
                 for (var int = 0; int < mx.length; int++) {
                     var valueN = mx[int].valueN;
                     if (isNaN(Number(valueN))) {
                         this.pd.jydjmxList[int].valueT = valueN;
                         this.pd.jydjmxList[int].valueN = null;
                     }
                 }
                 this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=xzdj', JSON.stringify(this.pd)).then(
                     function (data) {
                         if (data.body.a == 0) {
                             //成功回调提示
                             malert('保存结果成功', 'top', 'success');
                             var x=JSON.stringify(this.pd);
                             var j=JSON.parse(x);
                             j.jydjmxList=[];
                             bodyPd.xxxq(j);
                             //审核打印
                             var List=[];
                             List.push(j);
                             if(List.length>0){
                            	 var json = '{"list":' + JSON.stringify(List) + '}';
                            	 this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updatecgjy_sh', json).then(
                                         function (data) {
                                             if (data.body.a == 0) {
                                                 //成功回调提示
                                                 malert('审核成功', 'top', 'success');
                                                 wrapper.sx();

                                                 //打印审核成功的数据
                                                 setTimeout(function () {
                                                     wrapper.getPrintData(List);
                                                 }, 2000)
                                             }

                                         },
                                         function (error) {
                                             malert(error, 'top', 'defeadted');
                                         });
                             }
                         }

                     },
                     function (error) {
                         malert(error, 'top', 'defeadted');
                     });
                event.target.disabled=false;
             	event.target.innerText='审核打印';
             	$(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            //双击删除
            dbDel: function (data, index) {
                this.delZbxmIndex = index;
                pop.isShowpopL = true
                pop.isShow = true;
                pop.dyShow = true;
                pop.flag = false;
                pop.title = '删除指标项目';
                pop.centent = '确定删除指标项目： ' + data.zbxmmc + ' 吗？';

            },
            xz: function () {
            	if(this.pdxz.yblx==''){
            		this.pdxz.yblx='N';
            	}
               if(this.pdxz.yblx != 'F'){
            	   if (this.pdxz.brxm == '') {
                       malert("请输入病人姓名", 'top', 'defeadted');
                       return;
                   }
                   if (this.pdxz.ksbm == '') {
                       malert("请选择申请科室", 'top', 'defeadted');
                       return;
                   }
                   if (this.pdxz.sqys == '') {
                       malert("请选择申请医师", 'top', 'defeadted');
                       return;
                   }
                   if (this.pdxz.jyxm == '') {
                       malert("请选择检验项目", 'top', 'defeadted');
                       return;
                   }
               }else{//质控业务处理
            	   if (this.pdxz.jyxm == '') {
                       malert("请选择检验项目", 'top', 'defeadted');
                       return;
                   }
            	   if (this.pdxz.zxsb == '') {
                       malert("请选择执行设备", 'top', 'defeadted');
                       return;
                   }

            	   // TODO 设置默认值 ，目前没有参数功能 ，取固定值
            	   this.pdxz.nl=23;
            	   this.pdxz.nldw='1';
            	   this.pdxz.brxm='质控';
               }



                this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=xz', JSON.stringify(this.pdxz)).then(
                    function (data) {
                        console.log(data);
                        if (data.body.a == 0) {
                            //成功回调提示
                            malert('新增登记成功', 'top', 'success');
                            // wap.close();

                            this.pdxz=this.pdxz_non;
                            wrapper.sx();
                        }

                    },
                    function (error) {
                        malert(error, 'top', 'success');
                    });

            }

        },
        watch: {
            'pd.yblx': function () {
                if (this.pd.yblx == 'F') {
                    $('#zklx input').attr("disabled", false);
                } else {
                    $('#zklx input').attr("disabled", "disabled");
                }
            },
            'pd.lx': function () {
                if (this.pd.lx == '0') {
                    $('#lxmc').html('门诊号');
                }
                if (this.pd.lx == '1') {
                    $('#lxmc').html('住院号');
                }
            },
            'pd.jyxm': function () {
                for (var int = 0; int < this.util.jyxm.length; int++) {
                    if (this.util.jyxm[int].bm == this.pd.jyxm) {
                        this.pd.jyxmmc = this.util.jyxm[int].mc;
                    }
                }
            },
            'pd.ksbm': function () {
                for (var int = 0; int < this.util.sjks.length; int++) {
                    if (this.util.sjks[int].ksbm == this.pd.ksbm) {
                        this.pd.ksmc = this.util.sjks[int].ksmc;
                    }
                }
            },
            'pd.sqys': function () {
                for (var int = 0; int < this.util.sjys.length; int++) {
                    if (this.util.sjys[int].ysbm == this.pd.sqys) {
                        this.pd.sqysxm = this.util.sjys[int].ysmc;
                    }
                }
            },
            'pd.zxsb': function () {
                for (var int = 0; int < this.util.jysb.length; int++) {
                    if (this.util.jysb[int].sbbm == this.pd.zxsb) {
                        this.pd.zxsbmc = this.util.jysb[int].hostname;
                    }
                }
            },
            'pd.ybbm': function () {
                for (var int = 0; int < this.util.ybbm.length; int++) {
                    if (this.util.ybbm[int].ybbm == this.pd.ybbm) {
                        this.pd.ybmc = this.util.ybbm[int].ybmc;
                    }
                }
            },


            'pdxz.yblx': function () {
                if (this.pdxz.yblx == 'F') {
                    $('#zklx_xz input').attr("disabled", false);
                } else {
                    $('#zklx_xz input').attr("disabled", "disabled");
                }
            },
            'pdxz.lx': function () {
                if (this.pdxz.lx == '0') {
                    $('#lxmc_xz').html('门诊号');
                }
                if (this.pdxz.lx == '1') {
                    $('#lxmc_xz').html('住院号');
                }
            },
            'pdxz.jyxm': function () {
                for (var int = 0; int < this.util.jyxm.length; int++) {
                    if (this.util.jyxm[int].bm == this.pdxz.jyxm) {
                        this.pdxz.jyxmmc = this.util.jyxm[int].mc;
                    }
                }
            },
            'pdxz.ksbm': function () {
                for (var int = 0; int < this.util.sjks.length; int++) {
                    if (this.util.sjks[int].ksbm == this.pdxz.ksbm) {
                        this.pdxz.ksmc = this.util.sjks[int].ksmc;
                    }
                }
            },
            'pdxz.sqys': function () {
                for (var int = 0; int < this.util.sjys.length; int++) {
                    if (this.util.sjys[int].ysbm == this.pdxz.sqys) {
                        this.pdxz.sqysxm = this.util.sjys[int].ysmc;
                    }
                }
            },
            'pdxz.zxsb': function () {
                for (var int = 0; int < this.util.jysb.length; int++) {
                    if (this.util.jysb[int].sbbm == this.pdxz.zxsb) {
                        this.pdxz.zxsbmc = this.util.jysb[int].hostname;
                    }
                }
            },
            'pdxz.ybbm': function () {
                for (var int = 0; int < this.util.ybbm.length; int++) {
                    if (this.util.ybbm[int].ybbm == this.pdxz.ybbm) {
                        this.pdxz.ybmc = this.util.ybbm[int].ybmc;
                    }
                }
            },

        }
    });
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            wrapper.param.time = value
        }
    });
    laydate.render({
        elem: '.label-time',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pd.sqrq = value
        }
    });
    laydate.render({
        elem: '.label-time1',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pd.cyrq = value

        }
    });
    laydate.render({
        elem: '.label-time2',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pd.ybhsrq = value
        }
    });
    laydate.render({
        elem: '.label-time3',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pd.sjsj = value
        }
    });
    laydate.render({
        elem: '.label-time5',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd'
        , done: function (value, data) {
            isTabel.sjsj = value
        }
    });
    laydate.render({
        elem: '.label-time10',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd'
        , done: function (value, data) {
            wap.pd.bbsjscsj = value  //这个里面的变量value是回调变量，里面会携带修改的值，需要将修改的复制给当前input的v-mode的变量以此来达到修改值，
        }
    });
    laydate.render({
        elem: '.label-time11',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pd.dysj = value
            }
    });
    laydate.render({
        elem: '.datetim',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pdxz.sqrq = value
        }
    });
    laydate.render({
        elem: '.datetim1',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pdxz.cyrq = value
        }
    });
    laydate.render({
        elem: '.datetim2',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pdxz.ybhsrq = value
        }
    });
    laydate.render({
        elem: '.datetim3',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd HH:mm:ss'
        , done: function (value, data) {
            wap.pdxz.sjsj = value
        }
    })

    //初始化加载方法
    wrapper.jysb();
    wrapper.getutil();
    wrapper.sx();
    wap.getKsData();



