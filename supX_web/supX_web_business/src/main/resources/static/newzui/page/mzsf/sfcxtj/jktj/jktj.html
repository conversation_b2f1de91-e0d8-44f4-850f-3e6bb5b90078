<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>交款统计</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
</head>
<style>
    .tong-top{
        height: auto;
        display: flex;
    }
    .flex{
        display: flex;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10" style="overflow: auto">
<div class="wrapper" id="jktj">
            <div class="panel">
                <div class="tong-top">
                    <button class="tong-btn  btn-parmary icon-sx1 paddr-r5" @click="getData">刷新</button>
                    <button class="tong-btn  btn-parmary-b icon-xz1 paddr-r5" >报表</button>
                </div>
                <div class="grid-box" style="padding: 13px 0;">
                    <div class="col-xxl-6 flex margin-l-10">
                        <div class="flex alignItems margin-l-10">
                            <label class="whiteSpace margin-r-5 ft-14">时间段</label>
                            <input type="text" name="phone" class="zui-input todats" id="startTime" placeholder="开始时间"/><span class="padd-l-10 padd-r-10">至</span>
                            <input type="text" name="phone" class="zui-input todate" id="endTime" placeholder="结束时间"/>
                        </div>
                        <div class="flex alignItems  margin-l-10">
                            <span>合计：{{fDec(money, 2)}}元</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zui-table-view hzList padd-l-10 padd-r-10" style="border:none; ">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th  class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th  class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th><div class="zui-table-cell cell-l"><span>交款凭证号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>操作员</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>预交金</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>住院预交</span></div></th>
                            <th> <div class="zui-table-cell cell-s"><span>费用合计</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>医保卡支付</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>医疗卡支付</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>现金支付</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>交款日期</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>上交标志</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>上交人员</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>上交日期</span></div></th>
                            <th><div class="zui-table-cell cell-l text-left"><span>交款区间</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>业务窗口</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody >
                        <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
                            <td class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :val="isCheckAll">
                                </input-checkbox>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                            <td class=""><div class="zui-table-cell cell-l" v-text="item.jkpzh"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="item.czyxm"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="fDec(item.yjje,2)"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="fDec(item.zyyj,2)"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="fDec(item.fyhj,2)"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="fDec(item.ybkzf,2)"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="fDec(item.ylkzf,2)"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="fDec(item.xjzf,2)"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="fDate(item.jkrq,'date')"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="item.sjbz==0 ? '未上交':'上交'"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="item.sjryxm"></div></td>
                            <td class=""><div class="zui-table-cell cell-s" v-text="fDate(item.sjrq,'date')"></div></td>
                            <td class=""><div class="zui-table-cell cell-l text-left" v-text="item.jkqj"></div></td>
                            <td ><div class="zui-table-cell cell-s" v-text="item.ywckmc"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="zui-table-fixed table-fixed-l">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                                      :type="'all'" :val="isCheckAll">
                                </input-checkbox></div></th>
                                <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <!-- data-no-change -->
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in jsonList"
                                :tabindex="$index"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                        <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'some'" :val="isCheckAll">
                                        </input-checkbox>
                                    </div>
                                </td>
                                <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--右侧固定-->


            </div>
        </div>
<script type="text/javascript" src="jktj.js"></script>
</body>
</html>
