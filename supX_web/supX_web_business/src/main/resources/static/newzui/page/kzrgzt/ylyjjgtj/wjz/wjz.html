<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>危机值</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script src="/newzui/pub/js/highcharts.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link href="wjz.css" rel="stylesheet" type="text/css">
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
        <div class="count-box">
            <div class="count-left">
                <div class="count-title">危机值类型统计</div>
                <div class="count-time">
                    <div class="top-form">
                        <label class="top-label">时间段</label>
                        <div class="top-zinle">
                            <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                            <input autocomplete="off" type="text" class="zui-input wh122 times text-indent-20" id="timeVal"/>
                        </div>
                        <div class="top-zinle padd-r-5 padd-l-5">
                            至
                        </div>
                        <div class="top-zinle">
                            <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                            <input autocomplete="off" type="text" class="zui-input wh122 times1 text-indent-20" id="timeVal1"/>
                        </div>
                    </div>
                </div>
                <div class="count-content">
                    <vue-scroll :ops="pageScrollOps">
                    <div class="canvas-height">
                        <div id="container" style="min-width:420px;"></div>
                    </div>

                    </vue-scroll>
                </div>
                <div style="background: rgb(255, 255, 255);position: fixed;height: 30px;top: 344px;width: calc(100% - 18% - 30px);"><span v-for="(list,index) in label" style="position: fixed;font-size: 12px;" :style="{top:list.y,left:list.x}">{{index}}</span></div>
                <div class="count-bottom">
                    <span>总人数 <i class="color-c1 font30 padd-l-5">22<small class="font14">人</small></i></span>
                    <span>未处理 <i class="color-cff5 font30 padd-l-5">18<small class="font14">人</small></i></span>
                    <span>及时处理 <i class="color-dsh font30 padd-l-5">4<small class="font14">人</small></i></span>
                </div>
            </div>
            <div class="count-right">
               <div class="right-top">
                   <div class="count-title">危机值报告率</div>
                   <div class="bzt-box" id="jxwd"></div>
                   <!--<div class="count-Pointer"></div>-->
                    <div class="percent">80%</div>
               </div>
                <div class="right-top">
                    <div class="count-title">危机值处理及时率</div>
                    <div class="bzt-box" id="jscl"></div>
                    <div class="percent">50%</div>
                </div>

            </div>

        </div>
        <div class="bottom-box">
            <div class="bottom-left">
                <div class="count-title">
                危机值处理
                <span class="color-c1 fr font12">更多>></span>
                </div>
                <div class="bottom-table">
                    <div class="zui-table-view ">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>患者姓名</span></div></th>
                                    <th><div class="zui-table-cell cell-xl text-left text-indent-9"><span>检验项目</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>危急值</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>报告时间</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>报告时间</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>报告时间</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>报告时间</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>报告时间</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>报告时间</span></div></th>
                                    <th><div class="zui-table-cell cell-s"><span>报告时间</span></div></th>
                                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <!-- data-no-change -->
                        <div class="zui-table-body"  @scroll="scrollTable($event)" data-no-change style="height: 247px !important;">
                            <table class="zui-table zui-collapse">
                                <tbody>
                                <tr v-for="(item, $index) in 6"
                                    :tabindex="$index"
                                    @dblclick="edit(item)"
                                    ref="list"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                                    @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()"
                                    @click="checkSelect([$index,'one','6'],$event)">
                                    <!-- 有复选框的时候传 some  没有复选框的时候传one  -->
                                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                                    <td><div class="zui-table-cell cell-s">
                                        <i class="text-line color-dsh">患者姓名</i>
                                    </div></td>
                                    <td>
                                        <div class="zui-table-cell cell-xl text-left">
                                            <i class="crisis-danger"><small>危</small></i>
                                            <em>检验项目</em>
                                        </div>
                                    </td>
                                    <td><div class="zui-table-cell cell-s">危急值</div></td>
                                    <td><div class="zui-table-cell cell-s">报告时间</div></td>
                                    <td><div class="zui-table-cell cell-s">报告时间</div></td>
                                    <td><div class="zui-table-cell cell-s">报告时间</div></td>
                                    <td><div class="zui-table-cell cell-s">报告时间</div></td>
                                    <td><div class="zui-table-cell cell-s">报告时间</div></td>
                                    <td><div class="zui-table-cell cell-s">报告时间</div></td>
                                    <td><div class="zui-table-cell cell-s">报告时间</div></td>
                                    <td class="cell-s">
                                        <div class="zui-table-cell cell-s" :class="'未接收' ? 'color-cff5':'' ">
                                            未接收<!--未接收：颜色状态值color-cff5，未处理：color-ca3-->
                                        </div>
                                    </td>
                                    <td class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                         <span class="flex-center padd-t-2">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                        <em class="width30"><i class="iconfont icon-iocn12 icon-font20 icon-hover" data-title="未接收" @click="unabsorbed"></i></em>
                                         </span>
                                    </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--左侧固定-->
                        <div class="zui-table-fixed table-fixed-l background-f" style="height: 247px !important;">
                            <div class="zui-table-header">
                                <table class="zui-table">
                                    <thead>
                                    <tr>
                                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <!-- data-no-change -->

                            <div class="zui-table-body" @scroll="scrollTableFixed($event)" data-no-change style="height: 247px !important;">
                                <table class="zui-table zui-collapse">
                                    <tbody>
                                    <tr v-for="(item, $index) in 6"
                                        :tabindex="$index"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                                        @mouseenter="hoverMouse(true,$index)"
                                        @mouseleave="hoverMouse()"
                                        @click="checkSelect([$index,'one','6'],$event)">
                                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                        </div>
                        <!--右侧固定-->
                        <div class="zui-table-fixed table-fixed-r background-f" style="height: 247px !important;">
                            <div class="zui-table-header">
                                <table class="zui-table">
                                    <thead>
                                    <tr>
                                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTableFixed($event)" data-no-change style="height: 247px !important;">
                                <table class="zui-table zui-collapse">
                                    <tbody>
                                    <tr v-for="(item, $index) in 6"
                                        :tabindex="$index"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                                        @mouseenter="hoverMouse(true,$index)"
                                        @mouseleave="hoverMouse()"
                                        @click="checkSelect([$index,'one','6'],$event)">
                                        <td class="cell-s">
                                            <div class="zui-table-cell cell-s" :class="'未接收' ? 'color-cff5':'' ">
                                                未接收<!--未接收：颜色状态值color-cff5，未处理：color-ca3-->
                                            </div>
                                        </td>
                                        <td class="cell-m">
                                        <div class="zui-table-cell cell-m" >
                                            <span class="flex-center padd-t-2">
                                        <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                            <em class="width30"><i class="iconfont icon-iocn12 icon-font20 icon-hover" data-title="未接收" @click="unabsorbed"></i></em>
                                             </span>
                                         </div></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
            <div class="bottom-right">
                <div class="count-title">
                    危机值处理流程监管
                </div>
                <div class="count-name">患者:李浩然</div>
                <div class="count-process">
                    <div class="count-start1 fl">
                        <!--已经进行的流程状态样式start-ys1 color-cf0,未进行start-ws1 color-c42-->
                        <span class="start-ys1 color-cf0">检验科发起</span>
                        <span class="start-text">2018/12/12 12:55</span>
                    </div>
                    <div class="count-start2 fl">
                        <!--已经进行的流程状态样式start-ys2 color-cf0,未进行start-ws2 color-c42-->
                        <span class="start-ys2 color-cf0 text-indent-f40">办公护士接收</span>
                        <span class="start-text start-text-position">2018/12/12 12:55</span>
                    </div>
                    <div class="count-start3 fl">
                        <!--已经进行的流程状态样式start-ys3 color-cf0,未进行start-ws3 color-c42-->
                        <span class="start-ys3 color-cf0 text-indent-f40">办公护士接收</span>
                        <span class="start-text start-text-position1">2018/12/12 12:55</span>
                    </div>
                    <div class="count-start4 fl">
                        <!--已经进行的流程状态样式start-ys4 color-cf0,未进行start-ws4 color-c42-->
                        <span class="start-ys4 color-cf0 text-indent-f40">医生接收</span>
                        <span class="start-text start-text-position2">2018/12/12 12:55</span>
                    </div>
                    <div class="count-start5 fl">
                    <!--已经进行的流程状态样式start-ys5 color-cf0,未进行start-ws5 color-c42-->
                        <!--<span class="start-ys5 color-cf0 text-indent-30">下医嘱30分钟内</span>-->
                        <span class="start-overtime">超时</span>
                        <span class="start-ws5 color-c42 text-indent-30">下医嘱30分钟内</span>
                        <span class="start-text start-text-position3"></span>
                    </div>
                    <div class="count-start6 fl">
                        <!--已经进行的流程状态样式start-ys6 color-cf0,未进行start-ws6 color-c42-->
                        <span class="start-ws6 color-c42 text-indent-0">编写病程6小时内</span>
                        <span class="start-text start-text-position4"></span>
                    </div>
                    <div class="count-start7 fl">
                        <!--已经进行的流程状态样式start-ys7 color-cf0,未进行start-ws7 color-c42-->
                        <span class="start-ws7 color-c42 text-indent-0">结束</span>
                        <span class="start-text start-text-position4"></span>
                    </div>
                </div>
            </div>

        </div>
        <!--危急值接收复述-->
        <div id="pop" class="pophide"  :class="{'show':popShow}" v-cloak>
            <div class="wjz-width">
                <div class="wjz-top">
                    <span class="top-title">危机值接收复述</span>
                    <span class="iconfont icon-iocn55 icon-cf056 icon-font20" @click="Popclose"></span>
                </div>
                <div class="wjz-content">
                    <span class="wjz-jsr">接受人</span>
                    <!--组件components.js调用说明方法-->
                    <select-div-input @change-data="resultChange" :styles="'nzp'" :not_empty="false" :child="ryxmList"
                                      :rymc="'jszgmc'" :position="'pydm'" :phone="'jszgbm'"  :index_val="'jszgbm'" :val="popContent.jszgmc"
                                      :name="'popContent.jszgmc'" :search="true">
                    </select-div-input>
                </div>
                <div class="wjz-radio">
                    <div class="c_radio">
                        <input type="radio" id="1" name="radio11" checked @click="getRadio(0)">
                        <label for="1"></label>
                        <label for="1" class="lb_text">信息无误</label>
                    </div>
                    <div class="c_radio">
                        <input type="radio" id="2" name="radio11" @click="getRadio(1)">
                        <label for="2"></label>
                        <label for="2" class="lb_text">转科</label>
                    </div>
                    <div class="c_radio">
                        <input type="radio" id="3" name="radio11" @click="getRadio(2)">
                        <label for="3"></label>
                        <label for="3" class="lb_text ">出院</label>
                    </div>
                    <div class="c_radio">
                        <input type="radio" id="4" name="radio11" @click="getRadio(3)">
                        <label for="4"></label>
                        <label for="4" class="lb_text ">死亡</label>
                    </div>
                </div>
                <div class="wjz-bz">
                    <span class="wjz-jsr">备注</span>
                    <textarea class="wjz-textarea"></textarea>
                </div>
                <div class="wjz-btn">
                    <button class="root-btn btn-parmary-d9" @click="Popclose">取消</button>
                    <button class="root-btn btn-parmary" @click="popConfirm">确定</button>
                </div>
            </div>
        </div>

</body>
<script type="text/javascript" src="wjz.js"></script>
</html>
