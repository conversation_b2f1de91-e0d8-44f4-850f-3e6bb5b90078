(function () {
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            saveList:[],
            title:'',
            centent:'',
            allSearch:'',
        },
        methods:{
            guolu:function () {
                isTabel.isShow=true;
            },
            Print:function () {
                window.print()
            },
            delOK:function () {
                pop.isShowpopL=true
                pop.isShow=true
                pop.title='系统提示';
                pop.centent='确定删除该对码吗？';
            },
            //保存
            savedata:function(){
            	console.log(this.saveList);
	        	 if(this.saveList.length == 0 ){
	        		 malert('请填写','center','success');
	        	 }else{
	        		 var data = '{"list":' + JSON.stringify(wrapper.saveList) + '}';
		       		 this.$http.post('/actionDispatcher.do?reqUrl=LisXtwhFydm&types=updateFydm',data).then(function(json) {
		       			 console.log(json.body);
		       			 if(json.body.a == 0){
		       				 malert('保存成功！','center','success');
	        				 wapses.getData();
	        				 wrapper.saveList=[];
		       			 }
		       		 });
	        	 }
            },
            getData:function(){
            	wapses.getData();
            }
        },
        watch:{
        	'allSearch':function(){
        		console.log(this.allSearch);
        		wapses.param.parm = this.allSearch;
        		wapses.getData();
        	}
        }
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                malert('删除成功','top','success');
            },
            colse:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                malert('取消删除成功','top','defeadted');
            }

        }
    });
    var wapses=new Vue({
        el:'.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            isFold: false,
            isChecked:[],
            totlePage:'',
            jsonList:'',
            popIndex:'',
            param: {
    			page: 1,
    			rows: 10,
    			parm:'',
    		},
    		str:'',
        },
        mounted:function () {
          changeWin()
        },
        methods:{
            //删除当前
            delNow:function () {
                pop.title='系统提示';
                pop.centent='确定删除该行内容吗？';
                pop.isShowpopL=true;
                pop.isShow=true;
            },
            //dbAdd
            dbAdd:function () {
                alert('双击添加');
            },
            //双击编辑
            dbEdit:function (index) {
            	this.popIndex = index;
                wapse.getJyxm();
                wapse.isFold = true;
                wapse.sideTitle='选择项目';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');

            },
            getData:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhFydm&types=queryMxdm&parm=" + JSON.stringify(wapses.param), function(json) {
  					console.log(json);
  					//2018/07/20添加条件判断
  					if(json.a=="0"){
                        wapses.jsonList = json.d.list;
                        wapses.totlePage = Math.ceil(json.d.total / wapses.param.rows);
                        console.log(wapses.totlePage);
  					}else{
  					    malert(json.c,'top','defeadted')
  					}

  				});
            },
        },
        watch:{
      	  'str':function(){
      		  //saveList
      		if(wrapper.saveList.length != 0 ){
       			for(i = 0 ; i < wrapper.saveList.length ; i++){
           			//待保存的数组
           			if(wrapper.saveList[i].fybm == wapses.jsonList[wapses.popIndex].fybm){
           				wrapper.saveList[i].jyxmbm = wapses.jsonList[wapses.popIndex].jyxmbm;
           				//明细费用编码标志0组合 1明细
           				wrapper.saveList[i].fylb = 1;
           				return;
           			}
           		}
       			//明细费用编码标志0组合 1明细
       			wapses.jsonList[wapses.popIndex].fylb = 1;
       			//明细停用标志
       			wapses.jsonList[wapses.popIndex].fydmtybz = 0 ;
       			wrapper.saveList.push(wapses.jsonList[wapses.popIndex]);
   			}else{
   				//明细费用编码标志0组合 1明细
   				wapses.jsonList[wapses.popIndex].fylb = 1;
   				//明细停用标志
       			wapses.jsonList[wapses.popIndex].fydmtybz = 0 ;
   				wrapper.saveList.push(wapses.jsonList[wapses.popIndex]);
   			}
      	  }
        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            title:'',
            sideTitle:'',
            centent:'',
            isFold: false,
            jyList:'',
            jyparam:{
            	parm:'',
            },
        },
        methods:{
            // 取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // 确定
            confirms:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('222','top','success');
            },
            //双击编辑
            doEdit:function(data){
            	wapses.jsonList[wapses.popIndex].jyxmbm = wapse.jyList[data].jyxmbm;
            	wapses.jsonList[wapses.popIndex].jyxmmc = wapse.jyList[data].jyxmmc;
            	wapses.str = !wapses.str;
            	 $(".side-form-bg").removeClass('side-form-bg');
                 $(".side-form").addClass('ng-hide');
            },
            getJyxm:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhFydm&types=queryJyxm&parm=" + JSON.stringify(wapse.jyparam), function(json) {
            		console.log(json);
            		//检验详情
            		wapse.jyList = json.d.list;
            		console.log(wapse.jyList);
            	});
            }
        },
        watch:{
        	'jyparam.parm':function(){
        		wapse.getJyxm();
        	}
        }
    });
    // var filter=new  Vue({
    //     el:'.filter',
    //     data:{
    //         isShow:false
    //     },
    //     methods:{
    //         baocun:function () {
    //             this.isShow=false;
    //             malert('保存成功','top','success');
    //         },
    //         guanbi:function () {
    //             this.isShow=false;
    //             malert('取消保存','top','defeadted ');
    //         }
    //     },
    // })
    var isTabel=new Vue({
        el:'#isTabel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isTabelShow:false,
            isShow:false,
            minishow:true,
            isShowpopL:false,
            popContent:{},
            item: 0,
            appNum:[],
            appObj:{},
        },
        created:function () {
            this.append()
        },
        methods:{
            sc: function (index) {
                this.appNum.splice(index,1)
                // for(var i=0;i<this.appNum.length;i++){
                //     if(this.appNum[i].num==index){
                //
                //     }
                // }

            },
            append: function () {
                this.item = this.item + 1
                this.appObj.num=this.item
                this.appNum.push(JSON.parse(JSON.stringify(this.appObj)))
                this.$nextTick(function () {
                    $(".zui-form .lsittext").uicomplete({
                        iskeyup: true
                    });
                })
            },
            tabshow:function () {
                this.isTabelShow=true;
                this.minishow=false;
                pop.dyShow=false;
            },
            showDom:function () {
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.dyShow=false;
                pop.flag=true;
            }
        },
    })
    wapses.getData();

})()