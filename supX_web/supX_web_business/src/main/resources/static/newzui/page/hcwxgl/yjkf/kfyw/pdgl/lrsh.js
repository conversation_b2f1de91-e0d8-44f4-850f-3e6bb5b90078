(function () {
    var toolMenu_3 = new Vue({
        el: '.toolMenu_3',
        data: {
            pdlrpz: 0,
            ypjs: null,
            //盘点录入列表
            pdblrList: [],
        },
        //获取盘点录入凭证号
        mounted: function () {
            //启动加载
            this.getPdbList();
        },
        methods: {
            //清空页面
            clearAll: function () {
                enter_djList_sh.jsonList = [];
                enter_djDetail_sh.jsonList = [];
            },
            //审核盘点录入
            shPdlr: function () {
                if (enter_djList_sh.pdlrSelected == {}) {
                    malert('请选择盘点录入单号！','top','defeadted');
                    return;
                }
                //凭证号非空判断
                if (this.pdlrpz == 0) {
                    malert('请先选择凭证号！','top','defeadted');
                    return;
                };
                //准备参数
                var json = {
                    list: {
                        'pdblr': enter_djList_sh.pdlrSelected,
                        'pdblrmx': enter_djDetail_sh.jsonList
                    }
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=shPdblr', JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            malert("审核成功",'top','success');
                            //凭证号下拉框清空
                            toolMenu_3.pdlrpz = 0;
                        } else {
                            if (data.body.d == 0) {
                                malert(data.body.c,'top','success');
                            } else {
                                malert("审核失败",'top','defeadted');
                            }
                        }
                        //清空页面
                        toolMenu_3.clearAll();
                    }, function (error) {
                        console.log(error);
                    });
            },
            //作废盘点录入
            zfPdlr: function () {
                //凭证号非空判断
                if (this.pdlrpz == 0) {
                    malert('请先选择凭证号！','top','defeadted');
                    return;
                }
                //准备作废参数
                var json = enter_djList_sh.pdlrSelected;

                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=zfPdblr', JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            malert("作废成功",'top','success');
                        } else {
                            if (data.body.d == 0) {
                                malert(data.body.c,'top','success');
                            } else {
                                malert("作废失败",'top','defeadted');
                            }
                        }
                        //清空页面
                        toolMenu_3.clearAll();
                        toolMenu_3.pdlrpz = 0;
                    }, function (error) {
                        console.log(error);
                    });
            },
            //获取盘点录入列表
            getPdbList: function () {
                var kfbm = document.getElementById('_kfbm').value;
                if (kfbm == '') {
                    malert('请选择库房','top','defeadted');
                    return;
                }
                var parm = {
                    'kfbm': kfbm,
                    'qrzfbz': 0
                };
                //查询未审核盘点表,获取凭证号
                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=pdbList&parm=' + JSON.stringify(parm), function (json) {
                    if (json != null && json.a == 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            json.d[i]['zdrq'] = formatTime(json.d[i]['zdrq'], 'date');
                        }
                        //凭证号下拉框
                        toolMenu_3.pdblrList = json.d;
                    } else {
                        malert('数据获取失败！','top','defeadted')
                    }
                });
            },

            //获取盘点表凭证号列表
            getPdlrList: function () {
                if (this.pdlrpz == 0) return;
                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=pdblrList&parm=' + JSON.stringify(this.pdlrpz), function (json) {
                    if (json != null && json.a == 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            json.d[i]['zdrq'] = formatTime(json.d[i]['zdrq'], 'date');
                        }
                        //凭证号下拉框
                        enter_djList_sh.jsonList = json.d;
                    } else {
                        malert('数据获取失败！','top','defeadted')
                    }
                });
            },
        }
    });

    var enter_djList_sh = new Vue({
        el: '.enter_djList_sh',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            //已选择的记录
            pdlrSelected: {},
            //盘点表录入明细列表
            jsonList: []
        },
        updated:function(){
            changeWin()
        },
        methods: {
            //获取盘点表录入明细
            getPdblrmx: function (item) {
                //保存已选择的盘点表录入
                this.pdlrSelected = item;
                //获取盘点表录入明细
                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=pdblrmx&parm=' + JSON.stringify(item), function (json) {
                    if (json != null && json.a == 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            json.d[i]['scrq'] = formatTime(json.d[i]['scrq'], 'date');
                            json.d[i]['yxqz'] = formatTime(json.d[i]['yxqz'], 'date');
                            json.d[i]['ljje'] = Math.round(json.d[i]['yplj'] * json.d[i]['kcsl'] * 100) / 100;
                        }
                        //明细列表
                        enter_djDetail_sh.jsonList = json.d;
                    } else {
                        malert('数据获取失败！','top','defeadted')
                    }
                });
            }
        }
    });

    var enter_djDetail_sh = new Vue({
        el: '.enter_djDetail_sh',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            jsonList: []
        },
        updated:function(){
            changeWin()
        },
        methods: {}
    });
})();
