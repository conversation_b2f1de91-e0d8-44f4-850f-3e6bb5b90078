(function () {
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            centent:'',
            saveList:[],
            deleteList:[],
            searchAll:'',
        },
        methods:{
        	//刷新
        	getData:function(){
        		wrapper.saveList=[];
        		wrapper.deleteList=[];
        		leftwap.isChecked=[];
        		rightwap.isChecked=[];
        		leftwap.getKsData();
        		rightwap.getLisData();
        	},
        	//保存
        	saveData:function(){
        		if(wrapper.saveList.length == 0){
        			malert('无保存内容！','top','defeadted');
        			return;
        		}
        		console.log(wrapper.saveList);
        		/*$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=batchKsOp&json=" + JSON.stringify(wrapper.saveList), function(json) {
            		if(json.a=="0"){
            			rightwap.getLisData();
            			malert('保存成功！！','top','success');
            		}else{
            			malert('保存失败！！','top','defeadted');
            		}
  				});*/
        		
        		var data = '{"list":' + JSON.stringify(wrapper.saveList) + '}';
        		console.log(data);
	       		this.$http.post('/actionDispatcher.do?reqUrl=LisXtwhKsys&types=batchKsOp',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				rightwap.getLisData();
            			malert('保存成功！！','top','success');
	       			 }else{
	            			malert('保存失败！！','top','defeadted');
	            	 }
	       		 });
        	},
        	//删除
        	deleteData:function(){
        		if(rightwap.isChecked.length == 0){
        			malert('请选择要删除的科室！！','top','defeadted');
        			return;
        		}else{
        			for(i=0;i<rightwap.isChecked.length;i++){
        				if(rightwap.isChecked[i]){
        					wrapper.deleteList.push(rightwap.jsonList[i]);
        				}
        			}
        		}
        		
        		if(wrapper.deleteList.length == 0){
        			malert('请选择要删除的科室！！','top','defeadted');
        			return;
        		}
        		
        		var data = '{"list":' + JSON.stringify(wrapper.deleteList) + '}';
	       		 this.$http.post('/actionDispatcher.do?reqUrl=LisXtwhKsys&types=deleteKs',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				leftwap.getKsData();
            			rightwap.getLisData();
            			malert('删除成功！！','top','success');
	       			 }else{
	       				malert('删除失败！！','top','defeadted');
	       			 }
	       		 });

        	}
        },
        watch:{
        	'searchAll':function(){
        		console.log(wrapper.searchAll);
        		leftwap.parm.parm = wrapper.searchAll;
        		console.log(leftwap.parm.parm);
        		leftwap.getKsData();
        	}
        }
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
            popIndex:'',
            delList:'',
            ksmc:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
            	console.log(pop.popIndex);
            	/*if(!rightwap.jsonList[pop.popIndex].ly){
            		leftwap.jsonList.push(rightwap.jsonList[pop.popIndex]);
            		rightwap.jsonList.splice(pop.popIndex,1);
            	}else{*/
        		var data = '{"list":[' + JSON.stringify(rightwap.jsonList[pop.popIndex]) + ']}';
   	       		 this.$http.post('/actionDispatcher.do?reqUrl=LisXtwhKsys&types=deleteKs',data).then(function(json) {
   	       			 console.log(json.body);
   	       			 if(json.body.a == 0){
                			malert('删除成功！','top','success');
                			//获取His科室
                		    leftwap.getKsData();
                		    //获取lis科室
                		    rightwap.getLisData();
                			pop.delList='';
   	       			 }else{
   	       				   malert('删除失败！','top','defeadted');
   	       			 }
   	       		 });
            	/*}*/
            }

        }
    });
    
    //左边HIS科室
    var leftwap = new Vue({
        el:'.xmzb-content-left',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            isFold: false,
            jsonList:'',
            isChecked:[],
            parm:{
            	parm:'',
            }
        },
        methods:{
            //删除当前
            /*delNow:function () {
                pop.title='系统提示';
                pop.centent='确定删除该科室吗？';
                pop.isShowpopL=true;
                pop.isShow=true;
            },*/
            //dbAdd
            dbAdd:function (index) {
            	console.log(leftwap.jsonList[index]);
            	wrapper.saveList.push(leftwap.jsonList[index]);
            	rightwap.jsonList.push(leftwap.jsonList[index]);
            	rightwap.jsonList[rightwap.jsonList.length - 1].stop = "0";
            	leftwap.jsonList.splice(index,1);
            },
            //获取His科室信息
            getKsData:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=queryHisKs&param=" + JSON.stringify(leftwap.parm), function(json) {
            		leftwap.jsonList = json.d.list;
  					console.log(leftwap.jsonList);
  				});
            }

        },
    });
    
    //右边lis科室xmzb-content-right
    var rightwap = new Vue({
        el:'.xmzb-content-right',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            isFold: false,
            jsonList:'',
            isChecked:[],
            parm:{
            	parm:'',
            }
        },
        methods:{
            //删除当前
            delNow:function (data) {
            	pop.popIndex = data;
                pop.title='系统提示';
                pop.centent='确定删除['+rightwap.jsonList[data].ksmc+']科室吗？';
                pop.isShowpopL=true;
                pop.isShow=true;
            },
            //dbAdd
            dbAdd:function () {
                alert('双击添加');
            },
            //获取His科室信息
            getLisData:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=queryLisKs&param=" + JSON.stringify(rightwap.parm), function(json) {
            		rightwap.jsonList = json.d.list;
  					console.log(rightwap.jsonList);
  					wrapper.saveList=[];
  					wrapper.deleteList=[];
  					rightwap.isChecked=[];
  				});
            },
            //状态改变
            checkChange:function(cindex){
            	if(rightwap.jsonList[cindex].stop == null){
            		rightwap.jsonList[cindex].stop = "0";
            	}
            	rightwap.jsonList[cindex].stop = rightwap.jsonList[cindex].stop == "0" ? "1":"0";
            	console.log(rightwap.jsonList[cindex].stop);
            	//在saveList
            	if(wrapper.saveList.length != 0){
            		//查询是否存在
            		for(i=0 ; i < wrapper.saveList.length;i++){
            			if(wrapper.saveList[i].ksbm == rightwap.jsonList[cindex].ksbm){
            				//替换
            				wrapper.saveList[i] = rightwap.jsonList[cindex];
            				return;
            			}
            		}
            		//添加
            		wrapper.saveList.push(rightwap.jsonList[cindex]);
            	}else{
            		//添加
            		wrapper.saveList.push(rightwap.jsonList[cindex]);
            	}
            }

        },
    });
    
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            pcShow:false,
            jsShowtime:false,
            jsShow:false,
            qsShow:false,
            title:'',
            centent:'',
            isFold: false,
            parm:{
            	ksdm:'',
            	ksmc:'',
            	ly:'1',
            	stop:'0',
            }
        },
        methods:{
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
            	
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                //成功回调提示
                // malert('111','top','defeadted');
            },
            AddClose:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            } ,
            // //取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // //确定
            confirms:function () {
            	//验证
            	if(wapse.parm.ksmc == ''){
            		malert('请填写科室名称','top','defeadted');
            		return;
            	}
            	if(this.parm.stop){
            		this.parm.stop = '0'
            	}else{
            		this.parm.stop = '1'
            	}
            	//添加接口
            	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=insertKs&param=" + JSON.stringify(wapse.parm)+"&ly=" +wapse.parm.ly , function(json) {
  					console.log(json);
  					if(json.a == "0"){
  						// $(".side-form-bg").removeClass('side-form-bg')
                          // $(".side-form").addClass('ng-hide');
  						rightwap.getLisData();
  		                malert(json.c,'top','success');
  					}
  					//waps.getData();
  					wapse.parm.ksdm = '';
  					wapse.parm.ksmc='';
  					wapse.parm.stop=''
  				});
               
            }

        },
    });
    //获取His科室
    leftwap.getKsData();
    //获取lis科室
    rightwap.getLisData();
    
    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
    	if($(this).val() == '' || $(this).val() == null) {
    		$(this).addClass("emptyError");
    	} else {
    		$(this).removeClass("emptyError");
    	}
    });
})()