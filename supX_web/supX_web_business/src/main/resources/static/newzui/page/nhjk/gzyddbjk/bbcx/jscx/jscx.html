<html>
<head>
    <meta charset="UTF-8">
    <script type="application/javascript" src="/pub/top.js"></script>
    <!--
 	<script src="/lib/store.js" type="text/javascript"></script>
	<script src="/lib/jquery.resizableColumns.js" type="text/javascript"></script>
	-->
    <title>门诊交款</title>
    <link href="sfygzl.css" rel="stylesheet" type="text/css"/>
</head>

<body>
<div class="waper">
    <div class="InfoMenu printHide">
        <div @click="loadCon(0)" :class="{InfoMenuSelected: isindex==0}">门诊</div>
        <div @click="loadCon(1)" :class="{InfoMenuSelected: isindex==1}">住院</div>
    </div>
    <div class="  " style="">
        <button @click="getData" ><span class="fa fa-refresh"></span>刷新</button>

    </div>
    <div id="brfycl" v-if="isindex==0" class="menuTop printHide">
        <div class="tableDiv" style="height: calc(100% - 143px);margin-top: 10px;">
            <table class="patientTable patientTableCwjk" cellspacing="0" cellpadding="0" id="jkTable">
                <thead style="position: absolute;">
                <tr>
                    <th class="tableNo"></th>
                    <th>交款凭证号</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                </tr>
                </thead>
                <tr>
                    <th class="tableNo"></th>
                    <th>交款凭证号</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>
                    <th>操作员</th>

                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                    @dblclick="edit($index)">
                    <th class="tableNo" v-text="$index+1"></th>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                    <td v-text="$index+1"></td>
                </tr>
            </table>
        </div>

    </div>
    <!-- 病人费用信息列表展示 -->
    <div id="cwjkList" v-if="isindex==1" class="menuTop printHide">
        <div class="tableDiv" style="height: calc(100% - 143px);margin-top: 10px;">
            <table class="patientTable patientTableCwjk" cellspacing="0" cellpadding="0" id="jkTable">
                <thead style="position: absolute;">
                <tr>
                    <th class="tableNo"></th>
                    <th>业务窗口</th>
                    <th>备注说明</th>
                </tr>
                </thead>
                <tr>
                    <th class="tableNo"></th>
                    <th>业务窗口</th>
                    <th>备注说明</th>
                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                    @dblclick="edit($index)">
                    <th class="tableNo" v-text="$index+1"></th>
                    <td v-text="item.ywckmc"></td>
                    <td v-text="item.bzsm"></td>
                </tr>
            </table>
        </div>
    </div>
    <div class="pageDiv">
        <div class="page">
            <div @click="goPage(page, 'prev', 'getData')" class="fa fa-angle-left num"></div>
            <div class="num" v-show="totlePage > 0" :class="{currentPage: page == 1}" @click="goPage(1, null, null)">1
            </div>
            <div v-show="prevMore">...</div>
            <div class="num" v-for="item in totlePage" v-text="item" :class="{currentPage: param.page == item}"
                 @click="goPage(item, null, null)" v-show="showLittle(item)"></div>
            <div v-show="nextMore">...</div>
            <div class="num" :class="{currentPage: param.page == totlePage}" @click="goPage(totlePage, null, null)"
                 v-text="totlePage" v-show="totlePage > 1"></div>
            <div @click="goPage(page, 'next', 'getData')" class="fa fa-angle-right num next"></div>
            <div>
                第<input type="number" v-model="page"/>页
                <div class="divBtu" @click="goPage(page, null, null)">跳转</div>
            </div>
            <div>
                共<span v-text="totlePage"></span>页
                <select v-model="param.rows" @change="getData()">
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="30">30</option>
                </select>条/页
            </div>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="index.js"></script>
</html>
