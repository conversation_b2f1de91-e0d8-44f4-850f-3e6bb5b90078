    //顶部工具栏
    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
            zt: '9',
            ksbm:'',
            ksList:[],
            hzsqList:[],
            userInfo:{},
            csqxContent:{},
            defaltObj:{
                title:'拒绝申请',
                cs:'btn-parmary fr-right',
                cm:'btn-parmary-f2a1 fr-right',
                bm:'flex-none',
                cb:'取消',
                sb:'拒绝'
            },
            hzzt_tran:{
                '0':'未提交',
                '1':'进行中',
                '2':'已完成',
                '3':'作废',
                '9':'全部',
            },
            param:{},
        },
        mounted:function(){
            var myDate = new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 3), 'date') + ' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
            this.getKsbm();
            window.addEventListener("storage", function (e) {
                if (e.key == "apzj_saveHzsq" && e.newValue !== e.oldValue) {
                    wrapper.getData();
                }
            });
             this.getUserInfo();
        },
        updated:function () {
            changeWin()
        },
        methods:{
            // 获取操作员用户信息
            getUserInfo: function () {
                this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                    .then(function (json) {
                        wrapper.userInfo = json.body.d;
                        wrapper.getCsqx();
                    });
            },

            //获取当前操作员的拥有科室权限
            getKsbm: function () {
                this.param.ysks = "1";
                this.param.rows = 99999;
                this.param.page = 1;
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify(this.param) + "&json=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0 && json.d!=null) {
                        if (json.d.list.length > 0) {
                            wrapper.ksList = json.d.list;
                            var qbks = {
                                'ksbm':'all',
                                'ksmc':'全部'
                            };
                            wrapper.ksList.push(qbks);
                            wrapper.param.hzzt = '9';
                            wrapper.param.sqysks = 'all';

                            wrapper.getData();
                        }
                    } else {
                        malert('获取科室失败','top','defeadted')
                    }
                });
            },

            //获取参数权限
            getCsqx: function () {
                var csParm = {"ylbm": 'N030102002',"ksbm": wrapper.userInfo.ksbm};
                this.$http.get("/actionDispatcher.do", {
                    params: {
                        reqUrl: 'CsqxAction',
                        types: 'csqx',
                        parm: JSON.stringify(csParm)
                    }
                }).then(function (json, status, xhr) {
                    if (json.body.a == 0) {
                        if(json.body.d){
                            for (var i = 0; i < json.body.d.length; i++) {
                                var csjson = json.body.d[i];
                                switch (csjson.csqxbm) {
                                    case "N03010200204"://已提交会诊是否允许作废 0-否 1-是
                                        if(csjson.csz){
                                            wrapper.csqxContent.N03010200204 = csjson.csz;
                                        }
                                        break;
                                }
                            }
                        }
                    } else {
                        malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                    }
                })
            },

            //请求后台查询列表信息
            getData: function(){
                //加载动画效果
                common.openloading('.loadingPage');
                $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=queryHzsqList&parm=" + JSON.stringify(this.param),
                    function (json) {
                        if (json.a == '0') {
                            if (json.d && json.d.list) {
                                malert("操作成功！");
                                // for (var i = 0; i <json.d.list.length ; i++) {
                                //     json.d.list[i].brxb=wrapper.brxb_tran[json.d.list[i].brxb];
                                //     json.d.list[i].nldw=wrapper.nldw_tran[json.d.list[i].nldw];
                                // }
                                wrapper.hzsqList = json.d.list;
                            }
                        } else {
                            malert(json.c,'top','defeadted')
                        }
                });
                //数据请求结束时关闭
                common.closeLoading()
            },

            //作废会诊申请
            zfHzsq:function(index){
                if (!confirm("会诊"+wrapper.hzzt_tran[wrapper.hzsqList[index].hzzt]+"，你确定要作废【" + (wrapper.hzsqList[index].hzbh) + "】会诊编号的申请吗？")) {
                    return false;
                }
                var parm = {
                    hzbh:wrapper.hzsqList[index].hzbh,
                    zfbz:'1',
                    zfrq:new Date(),
                    zfczy:wrapper.userInfo.czybm,
                    zfczyxm:wrapper.userInfo.czyxm,
                    hzzt:'3'
                }
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=saveHzgl', JSON.stringify(parm)).then(function(data) {
                    if (data.body.a == 0) {
                        malert(data.body.c);
                        wrapper.getData();
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                        return;
                    }
                }, function(error) {
                    console.log(error);
                });
            },
            deleteHzsq:function(index){
                if (!confirm("你确定要删除【" + (wrapper.hzsqList[index].hzbh) + "】会诊编号的申请吗？")) {
                    return false;
                }
                var deleteParm ={
                    hzbh:wrapper.hzsqList[index].hzbh
                }
                //加载动画效果
                common.openloading('.zui-table-body');
                $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=deleteHzsq&parm=" + JSON.stringify(deleteParm),
                    function (json) {
                        if (json.a == '0') {
                            wrapper.getData();
                            malert(json.c);
                        } else {
                            malert(json.c,'top','defeadted')
                        }
                    });
                //数据请求结束时关闭
                common.closeLoading()
            },
            commonResultChange:function(val){
                var type = val[2][val[2].length - 1];
                switch (type) {
                    case "hzzt":
                        wrapper.param.hzzt = val[0];
                        this.$forceUpdate();
                        break;
                    case "sqysks":
                        wrapper.param.sqysks = val[0];
                        this.$forceUpdate();
                        break;
                }
                wrapper.getData();
            },
            searching:function(){
                wrapper.getData();
            },
            //患者姓名跳转到电子病历
            Patient:function () {
                // this.topNewPage('患者中心','page/zyysz/zyysz/hzgl/hzzx/hzzx.html')
            },
            //编辑
            Edit:function (num,val) {
                sessionStorage.setItem('hzsqglitem',JSON.stringify(val));
                this.topNewPage('会诊申请管理','page/hzxt/hzxt/hzshf/Subdirectory/apzj.html')
            },
            //拒绝申请
            refuse(){
                common.openConfirm('您确定要拒绝该会诊申请吗',function () {
                    malert('拒绝','top','defeadted');
                },function () {
                },this.defaltObj)
            },
            //查看报告
            Medical:function (index) {
                sessionStorage.setItem('dhzglitem_change',new Date().getTime());
                sessionStorage.setItem('dhzglitem_ifEdit',"1");//查看会诊报告
                sessionStorage.setItem('hzbg_targetPatPath',"page/hzxt/hzxt/hzsqgl/hzsqgl.html");//返回时的目标地址
                sessionStorage.setItem('dhzglitem',JSON.stringify(wrapper.hzsqList[index]));
                this.topNewPage('会诊诊断报告','page/hzxt/hzxt/dhzgl/sub/hzbg.html')
            },
            //双击查看
            checkDetail:function (index) {
                wrapper.hzsqList[index].zdsj = this.fDate(wrapper.hzsqList[index].zdsj,'datetime');
                wrapper.hzsqList[index].ryxm = wrapper.hzsqList[index].yqysxm;
                wrapper.hzsqList[index].editHz = true;
                wrapper.hzsqList[index].hzkssj = this.fDate(wrapper.hzsqList[index].hzkssj,'datetime');
                wrapper.hzsqList[index].hzjssj = this.fDate(wrapper.hzsqList[index].hzjssj,'datetime');
                sessionStorage.setItem('hzsqglitem',JSON.stringify(wrapper.hzsqList[index]));
                this.topClosePage('page/hzxt/hzxt/hzshf/Subdirectory/apzj.html');
                setTimeout(function () {
                }, 500);
                this.topNewPage('会诊申请管理','page/hzxt/hzxt/hzshf/Subdirectory/apzj.html');
                // this.topCloseAndOpenThisPage('page/hzxt/hzxt/hzshf/Subdirectory/apzj.html','会诊申请管理');
               // this.topRefreshd,'page/hzxt/hzxt/hzshf/Subdirectory/apzj.html')   ,'page/hzxt/hzxt/hzshf/Subdirectory/apzj.html','会诊申请管理' ;
               //
            }
        }
    });
    laydate.render({
        elem: '.times',
        type: 'datetime',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
            wrapper.param.beginrq = value;
            wrapper.goToPage(1);
        }
    });
    laydate.render({
        elem: '.times1',
        type: 'datetime',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
            wrapper.param.endrq = value;
            wrapper.goToPage(1);
        }
    });





