<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>患者管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" type="text/css" href="/newzui/css/icon.css">
    <link href="./hzgl.css" rel="stylesheet">
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper bg-fff flex-container flex-dir-c" id="jyxm_icon" style="height: 100%">
    <div class="panel ">
        <div class="tong-top flex-jus-sb">
            <button v-waves class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getData">刷新</button>
            <!-- <div class="text-right margin-r-35">
                <span style="margin-right: 15px;vertical-align: text-bottom" class="fa fa-th-large shuxian font-20" :style="{'color':index==0?'#1abc9c':''}" @click="tab(0)"></span>
                <span  class="fa fa-bars font-20" @click="tab(1)"  :style="{'color':index==1?'#1abc9c':''}"></span>
            </div> -->
            <div class="text-right menu-right flex-container flex-align-c flex-jus-e">
                <span class="fa butt-hover fa-th-large" :class="{'active':index==0}" @click="tab(0)"></span>
                <span class="relative fenge"></span>
                <span class="fa butt-hover fa-bars" :class="{'active':index==1}" @click="tab(1)"></span>
            </div>
        </div>
        <div class="flex-container" style="padding: 13px 0;flex-wrap: wrap;">
                <div class="flex-container padd-t-5">
                    <div class="flex flex-align-c  margin-l-20">
                    <label class="whiteSpace margin-r-5 ft-14">检索科室</label>
                    <select-input :cs="true" @change-data="resultChangeOd" :not_empty="false"
                                  :child="allKs" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.ksbm"
                                  :name="'popContent.ksbm'">
                    </select-input>
                    </div>
                </div>
                <div class="flex-container padd-t-5">
                    <div class="flex flex-align-c margin-l-20">
                    <label class="whiteSpace margin-r-5 ft-14">住院状态</label>
                    <select-input :cs="true" @change-data="Wf_GetBrList" :not_empty="false"
                                  :child="zyTypeBq_tran" :index="popContent.zyty" :val="popContent.zyty"
                                  :name="'popContent.zyty'">
                    </select-input>
                    </div>
                </div>
                <div class="flex-container padd-t-5" v-show="popContent.zyty=='Rcyhzxx'">
                    <div class="flex flex-align-c  margin-l-20">
                    <label class="whiteSpace margin-r-5 ft-14">出院时间</label>
                    <div class="flex  flex-align-c">
                        <input type="text" name="phone" class="zui-input wh180 todate" placeholder="请选择出院开始日期" id="timeVal"><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh180 " placeholder="请选择出院结束日期" id="timeVal1" />
                    </div>
                    </div>
                </div>
                <div class="flex-container padd-t-5">
                    <div class="flex flex_items  margin-l-20">
                    <label class="whiteSpace margin-r-5 ft-14">病人过滤</label>
                    <select-input :cs="true" @change-data="Wf_GetBrList" :not_empty="false"
                                  :child="brgl_tran" :index="popContent.brgl" :val="popContent.brgl"
                                  :name="'popContent.brgl'">
                    </select-input>
                </div>
                </div>
                <div class="flex-container padd-t-5">
                    <div class="flex flex-align-c  margin-l-20">
                        <label class="whiteSpace margin-r-5 ft-14">排序类型</label>
                        <select-input :cs="true" @change-data="resultChangeOd" :not_empty="false"
                                      :child="sort_tran" :index="pxContent.sort" :val="pxContent.sort"
                                      :name="'pxContent.sort'">
                        </select-input>
                    </div>
                </div>
                                <div class="flex-container padd-t-5">
                    <div class="flex-container flex-align-c  margin-l-20">
                        <label class="whiteSpace margin-r-5 ft-14">排序方式</label>
                        <select-input :cs="true" @change-data="resultChangeOd" :not_empty="false"
                                      :child="pxfs_tran" :index="pxContent.order" :val="pxContent.order"
                                      :name="'pxContent.order'">
                        </select-input>
                    </div>
                </div>
                <!--预留-->
                <!--<div class="zui-inline col-xxl-2">
                    <label class="zui-form-label">病况状态</label>
                    <select-input @change-data="resultChange" :not_empty="false"
                                  :child="bkType_tran" :index="popContent.bkgl" :val="popContent.bkgl"
                                  :name="'popContent.bkgl'">
                    </select-input>
                </div>-->
                <div class=" col-xxl-2 flex-container padd-t-5">
                    <div class="flex-container flex-align-c margin-l-20">
                    <label class="whiteSpace margin-r-5 ft-14">检&emsp;&emsp;索</label>
                    <div class="zui-input-inline ">
                        <input class="zui-input wh180" placeholder="床号、姓名" type="text" v-model="cwbh" @keydown="searchHc('cxbyParm')"/>
                    </div>
                    </div>
                </div>
        </div>
    </div>
    <div class="grid-box  kp zui-table-view  flex-one bg-fff flex-container flex-dir-c" ref="kp" v-cloak align="center"  v-if="index==0">
        <div class="kpFlex" ref="kp" @mousewheel="loadingData">
            <div class="" style="padding: 0 9px;width: 307px;display: inline-block;" @click="activeClick(index)" :data-id="index" v-for="(list,index) in Brxx_List" v-if="Brxx_List.length!=0">
                <div class="userWidth position"  :class="{'active':index==activeIndex}">
                    <div class="header-text">
                    <span class="userNameImg left">
                    	<span v-if="list.nljd==1"><!-- 1、男儿童 (0-6)-->
                    		<img src="/newzui/pub/image/maleBaby.png">
                    	</span>
                    	<span v-if="list.nljd==2"><!-- 2、女儿童(0-6); -->
                    		<img src="/newzui/pub/image/femalebaby.png">
                    	</span>
                    	<span v-if="list.nljd==3"><!-- 3、男少年(7-17) -->
                    		<img src="/newzui/pub/image/Group <EMAIL>">
                    	</span>
                    	<span v-if="list.nljd==4"><!-- 4、女少年(7-17) -->
                    		<img src="/newzui/pub/image/Group <EMAIL>">
                    	</span>
                    	<span v-if="list.nljd==5"><!-- 1、男儿童 -->
                    		<img src="/newzui/pub/image/juvenile.png">
                    	</span>
                    	<span v-if="list.nljd==6"><!-- 1、男儿童 -->
                    		<img src="/newzui/pub/image/maid.png">
                    	</span>
                    	<span v-if="list.nljd==7"><!-- 1、男儿童 -->
                    		<img src="/newzui/pub/image/youth.png">
                    	</span>
                    	<span v-if="list.nljd==8"><!-- 1、男儿童 -->
                    		<img src="/newzui/pub/image/woman.png">
                    	</span>
                    	<span v-if="list.nljd==9"><!-- 1、男儿童 -->
                    		<img src="/newzui/pub/image/grandpa.png">
                    	</span>
                    	<span v-if="list.nljd==10"><!-- 1、男儿童 -->
                    		<img src="/newzui/pub/image/grandma.png">
                    	</span>
                        <span v-if="list.nljd==11"><!-- 1、男儿童 -->
                            <img src="/newzui/pub/image/<EMAIL>">
                        </span>
                    </span>
                        <div class="padd-t-10 flex  text-left">
                            <p class="position"><span class="userName title "  @click="userGet(list,['userPage/yzgl',1,list])">{{list.brxm}}</span></p>
                            <!--<p class="position"><span class="userName title "  @click="userGet(list,['userPage/hzzx',1,list])">{{list.brxm}}</span></p>-->
                            <span class="userName-pin" v-show="list.pkhbz==1"><img src="/newzui/pub/image/pin.png"></span>
                            <span class="userName-lc" style="display: none"><img src="/newzui/pub/image/cp.png"></span>
                        </div>
                        <p class="text-left">
                            <span class="sex" :class="list.brxb==2? 'color-woman':'color-man'">{{list.brxb}}</span>
                            <span class="username-nl">{{list.nl}}{{list.nldw}}</span>
                            <span class="username-nl">{{list.rycwbh}}床</span>
                            <!--<span class="username-nl">{{list.zyts}}</span>-->
                        </p>
                        <span class="djzt"  v-if="list.hldj!=0&&list.hldj" :class="hldj_css[list.hldj]">{{hldj_tran[list.hldj]}}</span>
                    </div>
                    <div class="main-content">
                        <p>住&ensp;院&ensp;号：{{list.zyh}}&emsp; <span class="color-woman">{{list.brfbmc}}</span></p>
                        <p>诊断名称：{{list.ryzdmc}}</p>
                        <!--<p>
                        <span>报&emsp;&emsp;告：
                            <span class="text-decoration">3/5</span>
                        </span>
                            &emsp;&emsp;病&emsp;&emsp;况：<span :class="list.bqdj==0? 'color-man':'color-woman'">{{bqdj_tran[list.bqdj]}}</span>
                        </p>-->
                        <p>
                            <span>担保金额：<span>{{list.dbje}}</span></span>
                                                                          费用合计：<span>{{list.fyhj}}</span>
                        </p>
                        <p>
                        	<span>预交金额：<span>{{list.yjhj}}</span></span>
                            <span>费用余额：<span>{{(list.yjhj - list.fyhj).toFixed(2)}}</span></span>
                        </p>
                        <p>住院医生：{{list.zyysxm}}</p>
                        <p>科&emsp;&emsp;室：{{list.ryksmc}}
                            <!--<span class="sex hzbr">（会诊）</span>-->
                        </p>
                    </div>
                    <div class="footer-text margin-b-10 grid-box flex justifyAround margin-top-10">
                        <div class=" text-center"><span class="user-footer-img ljImg  " @click="userGet(list,['userPage/lclj',4,list])" data-title="路径"></span></div>
                        <div class=" text-center"><span class="user-footer-img yzImg  " @click="userGet(list,['userPage/yzgl',1,list])" data-title="医嘱"></span></div>
                        <div class=" text-center"><span class="user-footer-img blImg  " @click="dzbl(list)" data-title="病历"></span></div>
                        <div class=" text-center"><span class="user-footer-img sbImg " @click="topNew(list)" data-title="病案"></span></div>
                        <div class=" text-center"><span class="user-footer-img sbImg " @click="ygjc_pcsb(list)" data-title="院感上报"></span></div>
                        <div class=" text-center"><span class="user-footer-img sbImg " @click="openUrl(list)" data-title="检查报告单"></span></div>
                        <div class=" text-center"><span v-show="sxzzan" class="user-footer-img sbImg " @click="sxzz(list)" data-title="转出"></span></div>
                    </div>
                </div>
            </div>
            <div class="" style="padding: 0 9px;width: 307px;display: inline-block;" v-for="list in brk_listD" v-if="Brxx_List.length!=0"></div>
            <div v-if="Brxx_List.length!=0 && !isDoneCb " class=" ysb-green text-center" style="width: 100%;margin:30px 0">{{loadData}}</div>
            <p v-if="Brxx_List.length==0" class=" noData text-center">暂无数据...</p>
            <load v-show="isDoneCb"></load>
        </div>
    </div>
    <div class="zui-table-view over-auto zui-item " v-cloak v-if="index==1">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <span>序号</span>
                        </div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>住院天数</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>标识</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>床位</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl text-left"><span>诊断名称</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>住院号</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>入院日期</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>入院科室</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>住院医生</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>费别</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>护理等级</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>报告</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-l"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body"  @scroll="scrollTable($event)">
            <table class="zui-table table-width50" v-if="Brxx_List.length!=0">
                <tbody>
                <!--style="position: absolute"  :style="{marginTop:heightL[$index]+'px'}"-->
                <tr :tabindex="$index" v-for="(item, $index) in Brxx_List"
                    @click="checkSelect([$index,'one','Brxx_List'],$event)"
                    :class="[{'table-hovers':isChecked[$index]}]" class="tableTr2" ref="list">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-decoration"  @dblclick="userGet(item,['userPage/hzzx',0,item])">{{item.brxm}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.zyts}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">标识</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.rycwbh}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-left">{{item.ryzdmc}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.zyh}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.ryrq|formDate}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.ryksmc}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.zyysxm}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell title text-over-2 cell-s">{{item.brfbmc}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{hldj_tran[item.hldj]}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-decoration">报告</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l grid-box">
                            <div class="col-xxl-3 text-center"><span class="user-footer-img ljImg  "
                                                                     @click="userGet(item,['userPage/lclj',4,item])" data-title="路径"></span></div>
                            <div class="col-xxl-3 text-center"><span class="user-footer-img yzImg  "
                                                                     @click="userGet(item,['userPage/yzgl',1,item])" data-title="医嘱"></span></div>
                            <div class="col-xxl-3 text-center"><span class="user-footer-img blImg  "
                                                                     @click="userGet(item,['userPage/yzgl',3,item])" data-title="病历"></span></div>
                            <div class="col-xxl-3 text-center"><span class="user-footer-img sbImg " @click="userGet(item,['userPage/yzgl',0,item])" data-title="病案"></span></div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <p v-if="Brxx_List.length==0" class="  noData text-center zan-border">暂无数据...</p>
        </div>
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in Brxx_List"
                        :tabindex="$index"
                        ref="list"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','Brxx_List'],$event)">
                        <td><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in Brxx_List"
                        :tabindex="$index"
                        ref="list"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','Brxx_List'],$event)">
                        <td>
                            <div class="zui-table-cell cell-l grid-box">
                                <div class="col-xxl-3 text-center"><span class="user-footer-img ljImg  "
                                                                         @click="userGet(item,['userPage/lclj',4,item])" data-title="路径"></span></div>
                                <div class="col-xxl-3 text-center"><span class="user-footer-img yzImg  "
                                                                         @click="userGet(item,['userPage/yzgl',1,item])" data-title="医嘱"></span></div>
                                <div class="col-xxl-3 text-center"><span class="user-footer-img blImg  "
                                                                         @click="userGet(item,['userPage/yzgl',3,item])" data-title="病历"></span></div>
                                <div class="col-xxl-3 text-center"><span class="user-footer-img sbImg " @click="userGet(item,['userPage/yzgl',0,item])" data-title="病案"></span></div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>
<script type="text/javascript" src="hzgl.js"></script>
</body>
</html>
