var dzbl=new Vue({
    el:'#dzbl',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data:{
    	csContent:{},
    },
    methods:{
    	schz:function(){
    		   $.getJSON(
    	                "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + userNameBg.Brxx_List.brid + "&json="
    	                + JSON.stringify(this.param), function (json) {
    	                    if (json.a == "0") {
    	                        malert('信息上传成功！','top','success')
    	                    } else {
                                malert('信息上传失败失败：'+data.body.c,'top','defeadted')
    	                    }
    	                });
    	},
    	scjz:function(){
    		  $.getJSON(
    	                "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=" + userNameBg.Brxx_List.zyh + "&json="
    	                + JSON.stringify(this.param), function (json) {
    	                    if (json.a == "0") {
                                malert('信息上传成功！','top','success')
    	                    } else {
                                malert('信息上传失败失败：'+json.c,'top','defeadted')
    	                    }
    	                });
    	},
    	xbl:function(){
    		$.ajaxSettings.async = false;
            var sxdz = "";
            var user = "";
            var password = "";
            $.getJSON("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                	dzbl.csContent = JSON.parse(JSON.stringify(json.d.list[0]));
                    sxdz = dzbl.csContent.blSxdz;
                    user = userId;
                    $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                        if (json.a == "0") {
                        	password=json.d.password;
                        }
                    });
                    if (sxdz == "") {
                        malert("书写地址为空，打开病历失败！",'top','defeadted');
                        return
                    }
                    if (user == "") {
                        malert("用户名为空，打开病历失败！！",'top','defeadted');
                        return
                    }
                    if (password == "") {
                        malert("用户密码为空，打开病历失败！",'top','defeadted');
                        return
                    }
                    var zyh = userNameBg.Brxx_List.zyh;
                    if (zyh == '' || zyh == null || zyh == undefined) {
                        malert("请先选择病人后再书写病历！",'top','defeadted');
                        return
                    }
                    var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=" + zyh + ",userid=" + user + ",password=" + password + ",lyzyhmz=0,blhhl=0";
                    window.open(url);
                }
            });
    	},
    },
});
