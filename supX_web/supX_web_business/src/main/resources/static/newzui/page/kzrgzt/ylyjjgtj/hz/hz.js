var content=new Vue({
    el:'.content',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        beginrq:'',
        endrq:'',
        ypContent:{},
    },
    created:function(){
    },
    mounted:function(){
        this.ajaxChart();
        this.AjaxChartPie();
        //初始化检索日期！为今天0点到今天24点
        var myDate=new Date();
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date');
        this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24, 'date');
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#dateStart',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.beginrq = value;
                } else {
                    content.beginrq = '';
                }
                //获取一次列表
            }
        });
        laydate.render({
            elem: '#dateEnd',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.endrq = value;
                } else {
                    content.endrq = '';
                }
                //获取一次列表
            }
        });
    },
    updated:function(){
        this.ajaxChart();
        this.AjaxChartPie()
    },
    methods:{
        AjaxChartPie:function(){
            var arrObj=[];
            var textArr=['急性会诊','多科会诊','一般会诊'];
            var myChart = echarts.init(document.getElementById('pieCanvas'));
            for(var i=0;i<3;i++){
                var num=parseInt(Math.random()*20);

                arrObj.push({value:num, name:num+'次\n'+textArr[i],selected:true})
            }
            option = {
                color:['#ff5c62','#02a9f5','#c4cccb'],
                title : {
                    text: '接收会诊统计',
                    x:'center',
                    bottom:'0%'
                },
                tooltip : {
                    trigger: 'item',
                    formatter: "{a} <br/>{b} : {c} ({d}%)"
                },
                series : [
                    {
                        name: '接收会诊统计',
                        type: 'pie',
                        selectedOffset:2,
                        radius: [0, '70%'],
                        center: ['50%', '48%'],
                        data:arrObj,
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            if (option && typeof option === 'object') {
                myChart.setOption(option,true);
            }
        },
        edit:function(){

        },
        ajaxChart:function (view) {
            var dataArrName=[];
            var dataArr=[];
            for(var i=0;i<40;i++){
                dataArrName.push('李\n浩');
                dataArr.push(parseInt(10*Math.random()))
            }
            var myChart = echarts.init(document.getElementById('canvas'));
            option = {
                title: {
                    text: '科室医生会议次数统计',
                    x:'center',
                    y:'top',
                },
                color: ['#02a9f5'],
                tooltip : {
                    trigger: 'axis',
                    axisPointer : {
                        type : 'shadow'
                    },
                    formatter:function (params,ticket,callback ) {
                        return '<span>'+params[0].value+'次</span>'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis : [
                    {
                        type : 'category',
                        data : dataArrName,

                        axisTick: {
                            alignWithLabel: true
                        },
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92',
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#fff',
                            },
                        },
                        splitLine:{
                            show:true,
                            lineStyle:{
                            },
                        },
                    },
                ],
                yAxis : [
                    {
                        splitNumber:10,
                        type : 'value',
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92'
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine:{
                            show:false,
                            lineStyle:{
                                color:'#fff'
                            },
                        },
                        axisTick:{

                        },
                    },
                ],
                series : [
                    {
                        name:'',
                        type:'bar',
                        barWidth: '11',
                        animationDuration: function (idx) {
                            return idx * 100;
                        },
                        data:dataArr,
                        label:{
                            color:'#b6babb'
                        },

                    },

                ]
            };
            if (option && typeof option === 'object') {
                myChart.setOption(option,true);
            }

        }
    },
});
var brRyList=new Vue({
    el:'#brRyList01',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{

    },
    methods:{

    },
    mounted:function () {

    },
});