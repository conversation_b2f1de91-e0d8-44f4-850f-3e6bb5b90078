
//取当天开始时间，结束时间
var datebegin = getTodayDate();
var dateend = getTodayDate();
//左上角 检索区
var qtzdbm = {'疾病编码': 'jbmb', '疾病名称': 'jbmc'};
var searchLeft = new Vue({
    el: "#searchLeft",
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
    	caqxContent: {}, //参数对象
        ksList: [],
        jsonList: [],
        pxfs: "",//排序方式
        popContent:{ksbm:'',ksmc:''},
        gl: "", //过滤
        text: "", //检索框赋值
        searchCon: [],
        json: [],
        card: null,
        page: {
            page: 1,
            rows: 20,
            total: null
        },
        them_tran: {}, //  需要读取字典库的字段
        them: { // 检索的标题字段
            '姓名': 'brxm',
            '性别': 'brxbmc',
            '年龄': 'brnl',
            '挂号序号': 'ghxh',
            '挂号种类': 'ghzl',
            '挂号日期': 'ghrq',
            '接诊医生': 'jzysxm',
            '挂号科室': 'ghksmc'
        }
    },
    //页面渲染完成之后加载数据
    mounted: function () {
        //默认加载当前时间
        $("#startRq").val(datebegin);
        $("#endtRq").val(dateend);
    },
    methods: {
        resultChangeItem:function (event) {
            this.popContent.ksbm=event[0]
            this.popContent.ksmc=event[4]
        },
    	//获取参数权限
    	getCsqx: function() {
    		//获取参数权限
    		var parm = {
    			"ylbm": '006001001',
    			"ksbm": this.popContent.ksbm
    		};
    		$.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function(json) {
    			if(json.a == 0) {
    				if(json.d.length > 0) {
    					for(var i = 0; i < json.d.length; i++) {
    						var csjson = json.d[i]
    						switch(csjson.csqxbm) {
    							case "00600100101": //是否强行接诊  0=不允许,1=允许
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100101 = csjson.csz;
    								}
    								break;
    							case "00600100102": //自动获取就诊病人刷新时间0=手动刷新,大于零时为刷新间隔秒数
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100102 = csjson.csz;
    								}
    								break;
    							case "00600100103": //门诊医生站默认药房 （选择默认药房）
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100103 = csjson.csz;
    								}
    								break;
    							case "00600100104": //是否必须录入门诊诊断0=否，1＝是
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100104 = csjson.csz;
    								}
    								break;
    							case "00600100105": //门诊医生接诊范围0=本人，1=全院，2=全科
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100105 = csjson.csz;
    								}
    								break;
    							case "00600100106": //电子处方默认处方类型（输入药品处方类型编码）
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100106 = csjson.csz;
    								}
    								break;
    							case "00600100107": //是否强行限定药品处方位数0=否,1=是
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100107 = csjson.csz;
    								}
    								break;
    							case "00600100108": //病人信息修改权限0=否,1=是
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100108 = csjson.csz;
    								}
    								break;
    							case "00600100109": //甘肃省疾病普排序接口0=否 1=是(接诊按接口规范录入病人信息和门诊诊断)  2=是(接诊时不强制输入，以后再补)
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100109 = csjson.csz;
    								}
    								break;
    							case "00600100110": //保存电子处方时是否打印0=无，1=有
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100110 = csjson.csz;
    								}
    								break;
    							case "00600100111": //是否限制一个挂号序号只能开一张处方0-不限定,1限定
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100111 = csjson.csz;
    								}
    								break;
    							case "00600100112": //申请单的打印方式0-不打印，1-提示打印，2-直接打印
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100112 = csjson.csz;
    								}
    								break;
    							case "00600100113": //保存时是否签名0=否，1=是
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100113 = csjson.csz;
    								}
    								break;
    							case "00600100114": //开电子申请单方式1-医嘱,2-项目格式
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100114 = csjson.csz;
    								}
    								break;
    							case "00600100115": //中医处方是否必须填写主症、治法	0-否，1-是
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100115 = csjson.csz;
    								}
    								break;
    							case "00600100116": //处方金额是否允许为零0-不允许，1-允许
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100116 = csjson.csz;
    								}
    								break;
    							case "00600100117": //急诊挂号对应急诊处方（急诊挂号对应急诊处方）
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100117 = csjson.csz;
    								}
    								break;
    							case "00600100118": //中药处方数量允许输入小数0=不允许 1=允许
    								if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
    									searchLeft.caqxContent.cs00600100118 = csjson.csz;
    								}
    								break;
                                case "00600100119": //35岁以上血压是否必填 0-否 1-是
                                    if(csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                        searchLeft.caqxContent.cs00600100119 = csjson.csz;
                                    }
                                    break;
                            }
    					}
    					//门诊医生接诊范围0=本人，1=全院，2=全科
	    				if(searchLeft.caqxContent.cs00600100105=="1"){//如果查询全院病人则不给科室赋值
	    					$("#ksList").val("");
	    				}
    				}
    			} else {
                    malert('参数权限获取失败'+json.c,'top','defeadted');
    			}
    		});
    	},
        //获取当前操作员的拥有科室权限
        getKsbm: function () {
            var str_param = {
                ylbm: "006001001"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(str_param), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        searchLeft.ksList = json.d;
                        searchLeft.popContent.ksbm=searchLeft.ksList[0].ksbm
                        searchLeft.popContent.ksmc=searchLeft.ksList[0].ksmc
                        setTimeout(function () {
                            searchLeft.getCsqx();//科室记载完成之后再加载参数权限
                            // $("#ksList").val(searchLeft.ksList[0].ksbm);
                            setTimeout(function () {
                            	brxx.getData(); //加载完成后自动获取就诊病人列表
                            }, 100);
                        }, 100);
                        
                    }
                } else {
                    malert('获取科室失败'+json.c,'top','defeadted');
                }
            });
        },

        //科室改变时
        ksChange: function () {
            brxx.getData(); //根据科室过滤病人
            searchLeft.getCsqx();//科室改变之后再加载参数权限
        },
        //时间回车时查询
        searchListHc: function () {
            if (window.event.keyCode == 13) {
                brxx.getData(); //根据科室过滤病人
            }
        },

        //获取排序方式单选框的值
        pxfsClick: function (val) {
            this.pxfs = val;
            brxx.getData(); //根据科室过滤病人
        },
        //获取是否就诊单选框的值
        glClick: function (val) {
            this.gl = val;
            brxx.getData(); //根据科室过滤病人
        },

        // 这里是下拉框查询的
        searching: function (add,val) {
        	this.text=val;
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var str_param = {
                parm: this.text,
                page: this.page.page,
                rows: this.page.rows,
            };
            var json = {
            	jzbz: '0'
            };
            //门诊医生接诊范围0=本人，1=全院，2=全科
            if(searchLeft.caqxContent.cs00600100105=='0'){
            	json.jzys=userId;
            	json.ghks = this.popContent.ksbm;
            }else if(searchLeft.caqxContent.cs00600100105=='2'){
            	json.ghks = this.popContent.ksbm;
            }
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDownYw&types=brgh&dg=" +
                JSON.stringify(str_param) + "&json=" + JSON.stringify(json),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        if (add) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                date = searchLeft.fDate(json.d.list[i]['ghrq'], "date");
                                json.d.list[i]['ghrq'] = date;
                                searchLeft.searchCon.push(json.d.list[i]);
                            }
                        } else {
                            for (var i = 0; i < json.d.list.length; i++) {
                                date = searchLeft.fDate(json.d.list[i]['ghrq'], "date");
                                json.d.list[i]['ghrq'] = date;
                                searchLeft.searchCon.push(json.d.list[i]);
                            }
                            searchLeft.searchCon = json.d.list;
                        }
                        searchLeft.page.total = json.d.total;
                        if (json.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert('查询失败'+json.c,'top','defeadted');
                    }
                })
        },
        //鼠标双击（病人挂号信息）
        selectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.searching(true,this.text);
            } else {
                $(".selectGroup").hide();
                this.text = item.ghxh;
                brxx.getData(); //根据科室过滤病人
            }
        },
        //回车
        changeDown: function (event) {
            this.keyCodeFunction(event, 'json', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 ||event.code== 'NumpadEnter') {
                this.text = this.json.ghxh;
                brxx.getData(); //根据科室过滤病人
            }
        },
    }
});

//就诊病人列表
var brxx = new Vue({
    el: "#brxxList",
    //混合js字典庫
    mixins: [dic_transform,baseFunc],
    components: {
        'search-table': searchTable,
        'jbsearch-table': searchTable,
        'ybsearch-table': searchTable
    },
    data: {
        jsonList: [],
        totlePage: 0,
        isChecked: null,
        isCheckAll: false,
        brid: null,
        ghxx:null,
        indexs:0,
        tkxx:false,
        jbmc: null,  //获取门诊入院诊断信息的疾病名称的，用于临床诊断
        jbbm: null,
            lxrgxList: [], //联系人关系
            zyList: [], //职业下拉框
            zdxx: {sfcrb: '0'}, //诊断信息
            zcxx: {}, //注册信息
            selSearch: -1,
            page: {
                page: 1,
                rows: 10,
                total: null
            },
            //下拉table初步诊断信息检索选中对象
            jbbmContent: {},
            searchCon: [],
            them: {
                '疾病编码': 'jbmb',
                '疾病名称': 'jbmc'
                // '拼音代码': 'pydm'
            },
            //疾病编码下拉table
            qtzdbmContent: {},
            jbsearchCon: [],
            jbthem: qtzdbm,
            ybjbbmContent:{},
            ybsearchCon:[],
            ybthem: {
                '疾病编码': 'bm',
                '疾病名称': 'mc',
                '代码': 'dm'
            },
            bxurl:null,
            bxlbbm:null,
            billCode:null
    },
    methods: {
        go: function (index) {
            this.indexs = index;
        },
        //单击
        checkOne: function (num) {
            this.isChecked = num;
        },
        //双击
        edit: function (event) {
        	if(InfoMenu.which!=0){
        		InfoMenu.which=0;
        		InfoMenu.loadCon('yz');
        	}else{
        		// 判断当前浏览那个标签页和是否当前病人填写诊断信息
	            if(InfoMenu.which == 0){
	                diagnose.loadDiagnose();
	            }
        	}
        	this.brid = ($(event.currentTarget).attr("brid"));
            this.ghxh = ($(event.currentTarget).attr("ghxh"));
        	CFInfo.lczd=null;  //清空临床诊断
            $("#addYz").show();
            CFInfo.Wf_getYyffData();  //获取用药方法列表
            CFInfo.Wf_getZyYyffData(); //获取中药用药方法列表
            CFInfo.Wf_getHsksData();   //获取执行科室方法列表
            CFInfo.Wf_getPcData();    //获取频次列表
            CFInfo.Wf_getFjfy();      //获取附加费用
            treatment.getRyxx(this.ghxh, this.brid);  //病人信息检索
            CFBar.Wf_selectCF(this.ghxh);   //处方信息检索
            CFBar.Wf_selectCFJcxm(this.ghxh);//根据挂号序号再次查询检查项目处方信息
            CFBar.getTemData();         //处方模板查询处方
            CFBar.getLsTemData(this.ghxh);    //根据处方号查询该病人的历史处方
            CFBar.getLczdDate();        //临床诊断集合信息
            treatment.getMzry(this.ghxh);	//出门诊入院信息
            //initTem.getDate(this.ghxh, this.brid);      //病人信息检索
            brxx.getDate();      //病人信息检索
        },
        //获取就诊病人列表
        getData: function () {
            var ksbm = searchLeft.popContent.ksbm;
            var ksmc = searchLeft.popContent.ksmc;
            /*if (ksbm == null || ksbm == undefined || ksbm == "") {
                malert("请选择就诊科室！");
                return;
            }
*/          var beginrq = $("#startRq").val();
            var endrq = $("#endtRq").val();
            //结束时间加上一天再查询
            endrq = new Date(endrq);
            endrq=new Date(endrq.getTime()+(1000*60*60*24));
            var parm = {
                page: 1,
                rows: 2000,
                sort: searchLeft.pxfs,
                order: 'desc',
                ghxh: searchLeft.text,
                beginrq: beginrq,
                endrq: endrq,
                jzbz: searchLeft.gl
            };
            //门诊医生接诊范围0=本人，1=全院，2=全科
            if(searchLeft.caqxContent.cs00600100105=='0'){
            	parm.jzys=userId;
            	parm.ghks=ksbm;
            	parm.ghksmc=ksmc;
            }else if(searchLeft.caqxContent.cs00600100105=='2'){
            	parm.ghks=ksbm;
            	parm.ghksmc=ksmc;
            }
            $.getJSON("/actionDispatcher.do?reqUrl=GhglGhywBrgh&types=queryJzlb&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        //把时间戳改改时间
                        for (var i = 0; i < json.d.list.length; i++) {
                            json.d.list[i].ghrq = formatTime(json.d.list[i].ghrq, "datetime");
                            json.d.list[i].qhrq = formatTime(json.d.list[i].qhrq, "datetime");
                        }
                    }
                    brxx.jsonList = json.d.list;
                } else {
                    malert('获取挂号列表失败'+json.c,'top','defeadted');
                }
            });
        },
        getbxlb: function () {
            var param = {bxjk: "001"};
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            brxx.bxlbbm = json.d.list[0].bxlbbm;
                            brxx.bxurl = json.d.list[0].url;
                            brxx.getS02();
                        }
                    } else {
                        malert("保险类别查询失败!" + json.c,'top','defeadted')
                    }
                });
        },
        getS02: function () {
            var head = {
                operCode: "S02",
                rsa: ""
            };
            var body = {
                userName: "",
                passWord: ""
            };
            var param = {
                head: head,
                body: body
            };
            var str_param = JSON.stringify(param);

            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + brxx.bxurl + "&bxlbbm=" + brxx.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    if (json.a == 0) {
                        brxx.billCode = json.d;
                    } else {
                        malert("认证鉴权失败，请从新操作",'top','defeadted');
                    }
                });
        },
        // 页面加载时自动获取联系人关系Dddw数据
        GetlxrgxData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=lxrgx", function (json) {
                if (json.a == 0) {
                    brxx.lxrgxList = json.d.list;
                } else {
                    malert("联系人下拉列表查询失败：" + json.c,'top','defeadted');
                    return false;
                }
            });
        },
        //页面加载时自动获职业编码Dddw数据
        GetZybmData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zybm", function (json) {
                if (json.a == 0) {
                    brxx.zyList = json.d.list;
                } else {
                    malert('职业列表查询失败'+json.c,'top','defeadted');
                    return false;
                }
            });
        },

        //下拉检索
        changeDown: function (event, type, content, searchCon) {
            this.nextFocus(event);
            if (this[searchCon][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, content, searchCon);
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == "jbbm") {
                    this.zdxx['jbbm'] = this.jbbmContent['jbmb'];
                    this.zdxx['jbmc'] = this.jbbmContent['jbmc'];
                }
                if (type == "qtzd") {
                    this.zdxx['qtzdbm'] = this.qtzdbmContent['jbmb'];
                    this.zdxx['qtzdmc'] = this.qtzdbmContent['jbmc'];
                }
                if(type=="ybjb"){
                    this.zdxx['ybjbbm'] = this.ybjbbmContent['bm'];
                    this.zdxx['ybjbmc'] = this.ybjbbmContent['mc'];
                }
            }
        },

        //当输入值后才触发
        change: function (add, type, val) {
            console.log(add);
            console.log(type);
            console.log(val);
            if (!add) this.page.page = 1; // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (type == "jbbm") {
                console.log(type);
                this.jbbmContent['jbmc'] = val;
                if (this.jbbmContent['jbmc'] == undefined || this.jbbmContent['jbmc'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbbmContent['jbmc'];
                }
                var str_param = {
                    parm: this.page.parm,
                    page: this.page.page,
                    rows: this.page.rows
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm' +
                    '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) { //不是第一页则需要追加
                            for (var i = 0; i < data.d.list.length; i++) {
                                brxx.searchCon.push(data.d.list[i]);
                            }
                        } else {
                            brxx.searchCon = data.d.list;
                        }
                        brxx.page.total = data.d.total;
                        brxx.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            console.log('追加');
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }
            if (type == "qtzd") {
                this.qtzdbmContent['jbmc'] = val;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.qtzdbmContent['jbmc'] == undefined || this.qtzdbmContent['jbmc'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.qtzdbmContent['jbmc'];
                }
                var str_param = {
                    parm: this.page.parm,
                    page: this.page.page,
                    rows: this.page.rows
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm' +
                    '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) { //不是第一页则需要追加
                            for (var i = 0; i < data.d.list.length; i++) {
                                brxx.jbsearchCon.push(data.d.list[i]);
                            }
                        } else {
                            brxx.jbsearchCon = data.d.list;
                        }
                        brxx.page.total = data.d.total;
                        brxx.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }
            if (type == "ybjb") {
                this.ybjbbmContent['mc'] = val;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.ybjbbmContent['mc'] == undefined || this.ybjbbmContent['mc'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.ybjbbmContent['mc'];
                }
                var str_param = {
                    parm: this.page.parm,
                    page: this.page.page,
                    rows: this.page.rows
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + brxx.bxurl + "&bxlbbm=" + brxx.bxlbbm + "&types=jbbm&method=query&parm="
                    + JSON.stringify(str_param),
                    function (data) {
                        var res=eval('('+data.d+')');
                        if (add) { //不是第一页则需要追加
                            for (var i = 0; i < res.list.length; i++) {
                                brxx.ybsearchCon.push(res.list[i]);
                            }
                        } else {
                            brxx.ybsearchCon = res.list;
                        }
                        console.log(brxx.ybsearchCon);
                        brxx.page.total = res.total;
                        brxx.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }
        },

        selectYbjbbm:function(item){
            if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++; // 设置当前页号
                this.change(true, 'ybjb', this.ybjbbmContent['mc']); // 传参表示请求下一页,不传就表示请求第一页
            } else { // 否则就是选中事件,为json赋值
                this.ybjbbmContent = item;
                this.zdxx['ybjbbm'] = this.ybjbbmContent['bm'];
                this.zdxx['ybjbmc'] = this.ybjbbmContent['mc'];
                $(".selectGroup ").hide();
            }
        },
        //鼠标双击（疾病）
        selectJbbm: function (item) {
            console.log('asxsa');
            if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++; // 设置当前页号
                this.change(true, 'jbbm', this.jbbmContent['jbmc']); // 传参表示请求下一页,不传就表示请求第一页
            } else { // 否则就是选中事件,为json赋值
                this.jbbmContent = item;
                this.zdxx['jbbm'] = this.jbbmContent['jbmb'];
                this.zdxx['jbmc'] = this.jbbmContent['jbmc'];
                $(".selectGroup ").hide();
            }
        },

        //鼠标双击（其他诊断）
        selectQtzd: function (item) {
            if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++; // 设置当前页号
                this.change(true, 'qtzd', this.qtzdbmContent['jbmc']); // 传参表示请求下一页,不传就表示请求第一页
            } else { // 否则就是选中事件,为json赋值
                this.qtzdbmContent = item;
                this.zdxx['qtzdbm'] = this.qtzdbmContent['jbmb'];
                this.zdxx['qtzdmc'] = this.qtzdbmContent['jbmc'];
                $(".selectGroup ").hide();
            }
        },
        //查询诊断信息
        getDate: function () {
            if (!brxx.ghxh || !brxx.brid) {
                malert("请先选择病人",'top','defeadted');
                return false;
            }
            // 根据挂号序号再从服务器上查询当前挂号序号的信息
            var str_param = {
                ghxh: brxx.ghxh,
                brid: brxx.brid
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GhglGhywBrgh&types=queryOne&parm=" + JSON.stringify(str_param), function (json) {
                // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        brxx.zdxx = json.d.list[0].ghxx;
                        brxx.zcxx = json.d.list[0].zcxx;
                        Vue.set(brxx.zdxx, 'fbrq', formatTime(brxx.zdxx.fbrq, "date"));
                        Vue.set(brxx.jbbmContent, 'jbmc', brxx.zdxx.jbmc);
                        Vue.set(brxx.qtzdbmContent, 'jbmc', brxx.zdxx.qtzdmc);
                        Vue.set(brxx.ybjbbmContent, 'mc', brxx.zdxx.ybjbmc);
                        //brxx.zdxx.fbrq = formatTime(brxx.zdxx.fbrq, "date");
                        //brxx.jbbmContent.jbmc=brxx.zdxx.jbmc;
                    }
                } else {
                    malert("获取病人诊断信息失败",'top','defeadted');
                }
            });
        },
        //保存按钮
        save: function () {
        	/*
            if (brxx.zdxx.brnl < 14) {
                if (brxx.zcxx.lxrxm == null) {
                    malert("病人年龄小于14，家长姓名必填",'top','defeadted');
                    return false;
                }
            }
            */
            var parm = {
                Ghxx: brxx.zdxx,
                Zcxx: brxx.zcxx
            };
            var json = JSON.stringify(parm);
            this.$http.post('/actionDispatcher.do?reqUrl=MzysZlglBrjz&types=updateBrjbxxAndGhxx&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert("上传数据成功",'top','success');
                    this.isShow = false;
                } else {
                    malert("上传数据失败" + data.body.c,'top','defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        }
    }
});
brxx.getbxlb();
brxx.GetZybmData();//病人职业下拉框
brxx.GetlxrgxData(); //联系人关系下拉框
brxx.getDate(); //初始化页面加载病人信息
searchLeft.getKsbm();  //加载科室  科室加载完成后自动加载就诊病人列表

$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});
$(".zui-date input").uicomplete({
    iskeyup: false
});
laydate.render({
    elem: '#startRq'
    , eventElem: '.zui-date i.datenox'
    , trigger: 'click'
    , theme: '#1ab394'
    ,done:function (value,data) {
        brxx.getData();
    }
});
laydate.render({
    elem: '#endtRq'
    , eventElem: '.zui-date i.datenox'
    , trigger: 'click'
    , theme: '#1ab394'
    ,done:function (value,data) {
        brxx.getData();
    }
});
$(".zui-form input").uicomplete({
    iskeyup: false
});