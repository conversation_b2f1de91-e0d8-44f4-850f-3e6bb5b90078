.tableDiv table {
    table-layout: fixed; /*只有定义了table下面的td才起作用*/
}

.tableDiv table td {
    max-width: 88px;
    overflow: hidden; /*内容超出宽度是隐藏超出部分的内容*/
    text-overflow: ellipsis; /*这两个配合使用（超出部分用...代替）*/
}
.InfoMenu{
    position: sticky;
}
.menuTop{
    margin-top: 0;
}
.left {
    float: left;
    width: 780px;
    font-size: 12px;
    border-right: 3px solid #dfdfdf;
    overflow-y: auto;
    min-height: calc(100% - 76px);
}

.right {
    float: left;
    width: calc(100% - 800px);
}

.popTable th {
    /*font-size: 12px;*/
    width: 45px;
}

.popTable td {
    min-width: 80px;
    height: 26px;
}

.enter_tem1 {
    margin-bottom: 20px;
}

.table_tab1 {
    height: auto;
    max-height: none;
    overflow: visible;
}

.table_tab1 th {
    font-size: 14px;
}

.patientTable th:nth-child(n+2) {
    min-width: 97px;
}

.jkLog {
    display: block;
    margin-top: 40px;
    border-bottom: 3px solid #dfdfdf;
    margin-bottom: 0;
}

@media print {
    @page {
        margin: 1cm 0.8cm;
    }

    .left {
        border: 0;
    }
}