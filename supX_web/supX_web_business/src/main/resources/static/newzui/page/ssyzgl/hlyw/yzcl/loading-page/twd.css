.wrapper{
    background-color: #fff;
    min-height: 100%;
    height: calc(100% - 66px);
}
.box{
    margin-bottom: 66px;
}
#lr{
    overflow: auto;
}
.userNameBg{
    background:#708f89;
    /*height:180px;*/
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    background-image: url("/newzui/pub/image/userImg.png");
    padding: 10px;
}
.flex{
    display: flex;
    align-items: center;
}
.userNameImg img{
    width: 100px;
}
.text-color{
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}
.zyh,.bq,.ys,.brzt,.bz,.cwh{
    margin-right: 60px;
}
.userCwh{
    margin-bottom: 4px;
}
.fyhj {
    margin-right: 39px;
}
.yjhj {
    margin-right: 104px;
}
.zyts {
    margin-right: 32px;
}
.phone {
    margin-right: 53px;
}
.hl {
    margin-right: 52px;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{
    color: #B0BFBB;
    text-decoration: underline;
}
.menu {
    padding: 0px 15px;
    height: 50px;
}
.menu span{
    padding: 0 5px;
    display: inline-block;
}
.menu .fenge{
    font-size: 0;
    width: 1px;
    height: 17px;
    background:#646f82;
    padding: 0;
}
.fa-th-large:before {
    font-size: 18px;
    padding-top: 3px;
}
.fa-th-large.active::before {
    color: #1abc9c;
}
.tong-search{
    padding-top: 0;
}
.table-box {
    border-top: 1px solid #d1d4da;
    border-left: 1px solid #d1d4da;
    margin: 0 10px;
}
.table-box .col{
    border-right: 1px solid #d1d4da;
    border-bottom: 1px solid #d1d4da;
    height: 37px;
    line-height: 37px;
    border-radius: 0px;
}
.table-box .bg{
    background:#edf2f1;
    text-align: center;
}
.table-box .zui-input{
    border: 0;
}
.table-box .row{
    padding-left: 120px;
}
.table-box .row .col:first-of-type{
    width: 120px;
    float: left;
    margin-left: -120px;
}

.card-box{
    border: 1px dashed rgba(26, 188, 156, 0.3);
    padding: 10px 25px;
    background: rgba(26, 188, 156, 0.018);
    margin: 10px;
}
.action-bar.fixed{
     width: auto;
    right: 10px;
    left: 10px;
}
.card-item{
    padding-left: 50px;
    position: relative;
}
.card-item .bt{
    font-size:18px;
    color:#333333;
    -webkit-writing-mode: vertical-lr;
    writing-mode: vertical-lr;
    position: absolute;
    left: 30px;
    text-align: right;
    margin-top: -15px;
}
.input-box{
    padding-left: 100px;
    padding-bottom: 10px;
}
.input-box:last-of-type{
    padding-bottom: 0px;
}
.input-box label{
    float: left;
    margin-left: -100px;
    width: 100px;
    display: inline-block;
    text-align: right;
    line-height: 36px;
}
.pop-input-box .danwei{
    color:#1abc9c;
    line-height:36px;
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 11px;
}
.pop-input-box .fenge{
    position: absolute;
    right: -15px;
    top: 9px;
}



/*打印体温单*/
@CHARSET "UTF-8";
#twd *{
    box-sizing: content-box;
}
.twd-title{
    font-size:24px;
    font-weight:bold;
    text-align: center;
    width: 676px;
    padding: 10px 0;
}

canvas{
    cursor: default;
    display: block;
}

table{
    border-collapse: collapse;
    /*overflow: hidden;*/
    font-size: 12px;
    border: 0;
}

table tr td:first-child{
    padding-left: 4px;
    border-left: 0;
}

td{
    height: 14px;
    margin: 0;
    padding: 0;
    border-left: 1px solid #555;
    border-bottom: 1px solid #555;
}

#table_1 tr td:first-child{
    width: 81px;
    border-right: 2px solid #888;
}

#table_1 td{
    width: 84px;
}

#table_2{
    margin-top: -1px;
}

#table_2 tr:first-child{
    border-top: 1px solid #888;
}

#table_2 td{
    height: 28px;
    text-align: center;
    min-width: 13px;
    max-width: 13px;
}

#table_2 tr td:first-child{
    width: 83px;
    border-right: 2px solid #888;
    min-width: 83px;
    max-width: 83px;
    padding: 0;
    margin: 0;
}

#table_3 tr td:first-child{
    width: 80px;
    border-right: 2px solid #888;
}

#table_3 td{
    height: 19px;
    width: 41px;
    max-width: 41px;
}

#table_4 td{
    height: 19px;
    width: 84px;
}

#table_4 tr td:first-child{
    width: 80px;
    border-right: 2px solid #888;
}

.td_top{
    vertical-align: top;
    color: red;
}

.td_bottom{
    vertical-align: bottom;
    color: red;
}

@media print {
    .enter_tem1{
        border: 0;
    }
}

.zui-select-inline{
    display: block;
    margin-right: 0;
}
.layui-laydate td{
    border: none;
}
#twd{
    color: #000!important;
}
.personInfo {
    width: 676px;
    height: 30px;
}

.personInfo span{
    display: block;
    float: left;
}

.personInfo p{
    margin: 0 10px 0 0;
    float: left;
    height: 22px;
}