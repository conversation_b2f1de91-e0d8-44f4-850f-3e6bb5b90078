<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>协定处方</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10" style="overflow: auto">
<div class="wrapper">
    <div class="panel" id="kswh">
        <div class="panel-head border-bottom background reset ybhsgl-height" id="reset">
            <div class="zui-row">
                <div class="col-x-6">
                    <button @click="go(0)" class="zui-btn zui-no-border btn-default" :class="{'btn-primary':indexs==0}">组合医嘱</button><button @click="go(1)" class="zui-no-border zui-btn btn-default" :class="{'btn-primary':indexs==1}">组合药品医嘱</button>
                </div>
            </div>
        </div>
        <div class="col-x-12 rysx_bottom_list" >
            <div class="zui-input-inline" v-if="indexs==0">
                <input type="text" class="zui-input" name="input1" placeholder="姓名/拼音/编码" id="jsvalue"  @keydown="searchHc()"/>
            </div>
            <label @click="getData" class="zui-form-label" style="display: initial;cursor: pointer">刷新</label>
            <label @click="indexs==0?addData():''" class="zui-form-label" style="display: initial;cursor: pointer">新增</label>
            <label @click="saveData" class="zui-form-label" style="display: initial;cursor: pointer">保存</label>
            <label @click="remove" class="zui-form-label" style="display: initial;cursor: pointer">删除</label>
        </div>
        <div class="qiehuan">
            <div class="gj_all hideList" :class="{'showList':indexs==0}">
                <div id="gj" class="rysx_bottom">
                    <div style="clear: both"></div>
                    <div class="zui-table-view hzList " id="utable1" style="border:none; ">
                        <div class="zui-table-header">
                            <table class="zui-table">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell"><span>下标</span></div>
                                    </th>
                                    <th fixed="left" style="text-align:center;">
                                        <div class="zui-table-cell"><input type="checkbox" :checked="isCheckedall" @click="checkAll($event)" id="check0_0" class="zui-checkbox"/><label
                                                for="check0_0"></label></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell"><span>组合医嘱编码</span></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell"><span>组合医嘱名称</span></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell"><span>拼音代码</span></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell"><span>医嘱类型</span></div>
                                    </th>
                                    <th ><div class="zui-table-cell"><span>拥有者</span></div></th>
                                    <th ><div class="zui-table-cell"><span>拥有科室</span></div></th>
                                    <th ><div class="zui-table-cell"><span>处方类型</span></div></th>
                                    <th ><div class="zui-table-cell"><span>类型</span></div></th>
                                    <th ><div class="zui-table-cell"><span>治法</span></div></th>
                                    <th ><div class="zui-table-cell"><span>主治</span></div></th>
                                    <th ><div class="zui-table-cell"><span>是否草药</span></div></th>
                                    <th ><div class="zui-table-cell"><span>药房</span></div></th>
                                </tr>
                                <!--@click="checkOne($index)"-->
                                <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
                                    <td>
                                        <div class="zui-table-cell" v-text="$index+1"></div>
                                    </td>
                                    <th fixed="left" style="text-align:center;">
                                        <div class="zui-table-cell"><input type="checkbox" @click="checkOne($event,$index)" :checked="isChecked[$index]" :id='"check0_1"+$index+1' class="zui-checkbox"/><label :for='"check0_1"+$index+1'></label></div>
                                    </th>
                                    <td>
                                        <div class="zui-table-cell" v-text="item.zhyzbm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell" v-text="item.zhyzmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell" v-text="item.pydm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell" v-text="zhyzlx_tran[item.zhyzlx]"></div>
                                    </td>
                                    <td><div class="zui-table-cell" v-text="item.yyzxm"></div></td>
                                    <td><div class="zui-table-cell" v-text="item.yyksmc"></div></td>
                                    <td><div class="zui-table-cell" v-text="item.cflxmc"></div></td>
                                    <td><div class="zui-table-cell" v-text="zhyzBylx_tran[item.lx]"></div></td>
                                    <td><div class="zui-table-cell" v-text="item.zf"></div></td>
                                    <td><div class="zui-table-cell" v-text="item.zz"></div></td>
                                    <td><div class="zui-table-cell" v-text="istrue_tran[item.sfcy]"></div></td>
                                    <td><div class="zui-table-cell" v-text="item.yfmc"></div></td>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
                <div id="pop_gj">
                    <div class="pophide" :class="{'show':isShowpopL}"></div>
                    <div class="zui-form podrag bcsz-layer " style="height: max-content;padding-bottom: 20px"
                         :class="{'show':isShow}">
                        <div class="layui-layer-title ">{{title}}</div>
                        <!--<span class="layui-layer-setwin" @click="isShow = false"><a class="layui-layer-ico layui-layer-close layui-layer-close1" href="javascript:;"></a></span>-->
                        <div class="layui-layer-content">
                            <div class=" layui-mad">
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">组合医嘱名称</label>
                                    <div class="zui-input-inline">
                                        <input type="text" class="zui-input" v-model="popContent.zhyzmc" data-notEmpty="false" @keydown="nextFocus($event)" @blur="setPYDM(popContent.zhyzmc,'popContent','pydm')"/></div>
                                </div>
                                <div class="zui-inline  col-x-6">
                                    <label class="zui-form-label">拼音代码</label>
                                    <div class="zui-input-inline">
                                        <input type="text" class="zui-input" name="input1" v-model="popContent.pydm" @keydown="nextFocus($event)" data-notEmpty="false"/>
                                    </div>
                                </div>
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">医嘱类型</label>
                                    <select-input @change-data="resultChange" :not_empty="false"
                                                  :child="zhyzlx_tran" :index="popContent.zhyzlx" :val="popContent.zhyzlx"
                                                  :name="'popContent.zhyzlx'">
                                    </select-input>
                                </div>
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">拥有者</label>
                                    <select-input @change-data="resultChange" :not_empty="false"
                                                  :child="rybmList" :index="'ryxm'" :index_val="'rybm'" :val="popContent.yyz"
                                                  :name="'popContent.yyz'" :search="true">
                                    </select-input>
                                </div>

                                <div class="zui-inline  col-x-6"><label class="zui-form-label">拥有科室</label>
                                    <select-input @change-data="resultChange" :not_empty="false"
                                                  :child="ksbmList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.yyks"
                                                  :name="'popContent.yyks'" :search="true">
                                    </select-input>
                                </div>
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">处方类型</label>
                                    <select-input @change-data="resultChange" :not_empty="false"
                                                  :child="cflxList" :index="'cflxmc'" :index_val="'cflxbm'" :val="popContent.cflxbm"
                                                  :name="'popContent.cflxbm'" :search="true">
                                    </select-input>
                                </div>
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">类型</label>
                                    <select-input @change-data="resultChange" :not_empty="true"
                                                  :child="zhyzBylx_tran" :index="popContent.lx" :val="popContent.lx"
                                                  :name="'popContent.lx'">
                                    </select-input>
                                </div>
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">治法</label>
                                    <div class="zui-input-inline">
                                        <input type="text" class="zui-input" name="input1"  v-model="popContent.zf" @keydown="nextFocus($event)" data-notEmpty="false"/>
                                    </div>
                                </div>
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">是否草药</label>
                                    <select-input @change-data="resultChange" :not_empty="false"
                                                  :child="istrue_tran" :index="popContent.sfcy" :val="popContent.sfcy"
                                                  :name="'popContent.sfcy'">
                                    </select-input>
                                </div>
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">药房</label>
                                    <select-input @change-data="resultChange" :not_empty="true"
                                                  :child="yfList" :index="'yfmc'" :index_val="'yfbm'" :val="popContent.yfbm"
                                                  :name="'popContent.yfbm'" :search="true">
                                    </select-input>
                                </div>
                                <div class="zui-inline  col-x-6"><label class="zui-form-label">主治</label>
                                    <div class="zui-input-inline">
                                        <input type="text" class="zui-input" name="input1"  v-model="popContent.zz" @keydown="nextFocus($event)" data-notEmpty="false"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="zui-row buttonbox">
                            <button class="zui-btn btn-primary table_db_save" @click="saveData">保存</button>
                            <button class="zui-btn table_db_esc" @click="isShow = false,isShowpopL=false">取消</button>
                        </div>
                    </div>
                </div>
            </div>
            <!--<div class="mz_all hideList" :class="{'showList':indexs==1}">-->
                <!--<div id="mz" class="rysx_bottom">-->
                    <!--<div style="clear: both"></div>-->
                    <!--<div class="zui-table-view hzList " id="" style="border:none; ">-->
                        <!--<div class="zui-table-header">-->
                            <!--<table class="zui-table">-->
                                <!--<thead>-->
                                <!--<tr>-->
                                    <!--<th>-->
                                        <!--<div class="zui-table-cell"><span>下标</span></div>-->
                                    <!--</th>-->
                                    <!--<th fixed="left" style="text-align:center;">-->
                                        <!--<div class="zui-table-cell"><input type="checkbox" :checked="isCheckedall" @click="checkAll($event)" id="check0_1" class="zui-checkbox"/><label-->
                                                <!--for="check0_1"></label></div>-->
                                    <!--</th>-->
                                    <!--<th style="width: 20%;">-->
                                        <!--<div class="zui-table-cell"><span>民族编码</span></div>-->
                                    <!--</th>-->
                                    <!--<th style="width: 20%;">-->
                                        <!--<div class="zui-table-cell"><span>民族名称</span></div>-->
                                    <!--</th>-->
                                    <!--<th style="width: 20%;">-->
                                        <!--<div class="zui-table-cell"><span>拼音代码</span></div>-->
                                    <!--</th>-->
                                    <!--<th style="width: 20%;">-->
                                        <!--<div class="zui-table-cell"><span>停用标志</span></div>-->
                                    <!--</th>-->
                                    <!--<th style="width: 20%;">-->
                                        <!--<div class="zui-table-cell"><span>序号</span></div>-->
                                    <!--</th>-->
                                <!--</tr>-->
                                <!--&lt;!&ndash;@click="checkOne($index)"&ndash;&gt;-->
                                <!--<tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">-->
                                    <!--<td>-->
                                        <!--<div class="zui-table-cell" v-text="$index+1"></div>-->
                                    <!--</td>-->
                                    <!--<th fixed="left" style="text-align:center;">-->
                                        <!--<div class="zui-table-cell"><input type="checkbox" @click="checkOne($event,$index)" :checked="isChecked[$index]" :id='"check02_"+$index+1' class="zui-checkbox"/><label-->
                                                <!--:for='"check02_"+$index+1'></label></div>-->
                                    <!--</th>-->
                                    <!--<td>-->
                                        <!--<div class="zui-table-cell" >{{item.mzbm}}</div>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                        <!--<div class="zui-table-cell" v-text="item.mzmc"></div>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                        <!--<div class="zui-table-cell" v-text="item.pydm"></div>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                        <!--<div class="zui-table-cell" v-text="item.tybz==0 ? '启用' : '停用'"></div>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                        <!--<div class="zui-table-cell" v-text="item.sor"></div>-->
                                    <!--</td>-->
                                <!--</tr>-->
                                <!--</thead>-->
                            <!--</table>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div id="pop_mj">-->
                    <!--<div class="pophide" :class="{'show':isShowpopL}"></div>-->
                    <!--<div class="zui-form podrag bcsz-layer " style="height: max-content;padding-bottom: 20px"-->
                         <!--:class="{'show':isShow}">-->
                        <!--<div class="layui-layer-title ">修改民族信息</div>-->
                        <!--&lt;!&ndash;<span class="layui-layer-setwin" @click="isShow = false"><a class="layui-layer-ico layui-layer-close layui-layer-close1" href="javascript:;"></a></span>&ndash;&gt;-->
                        <!--<div class="layui-layer-content">-->
                            <!--<div class=" layui-mad">-->
                                <!--<div class="zui-inline  col-x-6"><label class="zui-form-label">民族编码</label>-->
                                    <!--<div class="zui-input-inline"><input type="text" class="zui-input"-->
                                                                         <!--v-model="popContent.mzbm" data-notEmpty="true" :disabled="readonly"/></div>-->
                                <!--</div>-->
                                <!--<div class="zui-inline  col-x-6">-->
                                    <!--<label class="zui-form-label">民族名称</label>-->
                                    <!--<div class="zui-input-inline">-->
                                        <!--<input type="text" class="zui-input" name="input1" v-model="popContent.mzmc"-->
                                               <!--data-notEmpty="true"-->
                                               <!--@blur="setPYDM(popContent.mzmc,'popContent','pydm')"/>-->
                                    <!--</div>-->
                                <!--</div>-->
                                <!--<div class="zui-inline  col-x-6"><label class="zui-form-label">拼音代码</label>-->
                                    <!--<div class="zui-input-inline">-->
                                        <!--<input type="text" class="zui-input" name="input1" v-model="popContent.pydm"-->
                                               <!--disabled="disabled"/>-->
                                    <!--</div>-->
                                <!--</div>-->
                                <!--<div class="zui-inline  col-x-6"><label class="zui-form-label">序号</label>-->
                                    <!--<div class="zui-input-inline">-->
                                        <!--<input type="text" class="zui-input" v-model="popContent.sor"-->
                                               <!--data-notEmpty="false" @keydown="changeDown($event)"/>-->
                                    <!--</div>-->
                                <!--</div>-->

                                <!--<div class="zui-inline  col-x-6"><label class="zui-form-label">停用标志</label>-->
                                    <!--<select-input @change-data="resultChange" :data-notEmpty="true"-->
                                                  <!--:child="stopSign" :index="popContent.tybz" :val="popContent.tybz"-->
                                                  <!--:name="'popContent.tybz'">-->
                                    <!--</select-input>-->
                                <!--</div>-->
                            <!--</div>-->
                        <!--</div>-->
                        <!--<div class="zui-row buttonbox">-->
                            <!--<button class="zui-btn btn-primary table_db_save" @click="saveData">保存</button>-->
                            <!--<button class="zui-btn table_db_esc" @click="isShow = false,isShowpopL=false">取消</button>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</div>-->
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<script type="text/javascript" src="xdcf.js"></script>
</body>
</html>