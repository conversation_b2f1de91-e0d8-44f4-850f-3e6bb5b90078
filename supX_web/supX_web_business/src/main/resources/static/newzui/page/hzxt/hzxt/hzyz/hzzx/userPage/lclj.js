var panel = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        setText: {'text':'混合痔临床路径表单-子路径1',zx:true,wcjd:'90%',id:123,parentid:111},
        istype:true,
        isck:false,
        index: 0,
        isckShow:true,
        isShow:false,
        picked:'0',
        popText:'备注',
        isactiveClass:null,
        isactiveheaderClass:null,
        popPlaceholder:'',
        popModel:'正常',
        popIshow:false,
        dayIndex:null,
        dayActiveIndex:null,
        popContent: {},
        flag:false,
        jsonList:[],
        ljlist:[
            {
                text:'混合痔临床路径表单',
                zx:false,
                id:111,
                wcjd:'89%',
                list:[
                    {'text':'混合痔临床路径表单-子路径1',zx:true,wcjd:'90%',id:123,parentid:111},
                    {'text':'混合痔临床路径表单-子路径2',zx:false,wcjd:'91%',id:124,parentid:111},
                    {'text':'混合痔临床路径表单-子路径3',zx:false,wcjd:'93%',id:125,parentid:111},
                ],
            },
            {
                text:'混合痔临床路径表单121212',
                zx:false,
                wcjd:'79%',
                id:222,
                list:[
                    {'text':'混合痔临床路径表单-子路径11',zx:false,wcjd:'94%',id:223,parentid:222},
                    {'text':'混合痔临床路径表单-子路径22',zx:false,wcjd:'95%',id:224,parentid:222},
                    {'text':'混合痔临床路径表单-子路径33',zx:false,wcjd:'96%',id:225,parentid:222},
                ],
            },
        ],
        day: [
            {'day':'住院第一天',date:new Date(),'num':1,'zxwb':true,'done':true,'zxjl':false},
            {'day':'住院第二天',date:new Date(),'num':2,'zxwb':true ,'done':true,'zxjl':false},
            {'day':'住院第三天',date:new Date(),'num':3,'zxwb':true,'done':true,'zxjl':false},
            {'day':'住院第四天',date:'','num':4,'zxwb':true,'done':false,'zxjl':true},
            {'day':'住院第五天',date:'','num':5,'zxwb':false,'done':false,'zxjl':false}
        ],
        ischecked:{
            gz:['','',true],
            zdyz:[],
            hlgz:[],
            bqby:[],
            ischeckedAll:'',
        },
        contextmenuone: [
            {name: '左方新增阶段', icon: '/newzui/pub/image/zj.png', type: 'left'},
            {name: '右方新增阶段', icon: '/newzui/pub/image/zj.png', type: 'right'},
            {name: '编辑', icon: '/newzui/pub/image/bj.png', type: 'edit'},
            {name: '删除', icon: '/newzui/pub/image/sc.png', type: 'delete'},
        ],
        contextmenutwo: [
            {name: '上方新增项目', icon: '/newzui/pub/image/zj.png', type: 'top'},
            {name: '下方新增项目', icon: '/newzui/pub/image/zj.png', type: 'bottom'},
            {name: '编辑', icon: '/newzui/pub/image/bj.png', type: 'edit'},
            {name: '删除', icon: '/newzui/pub/image/sc.png', type: 'delete'},
        ],
        optionList: [
            {
                name: '住院第一天',
                csactive:false,
                gz: [
                    {type: false, title: '书写病历',csactive:false},
                    {type: true, title: '书写病历122',csactive:false},
                    {type: true, title: '书写病历1',csactive:false},
                    {type: true, title: '住院第一天',csactive:false},
                    {type: true, title: '住院第一dsdsd天',csactive:false},
                ],
                zdyz: [
                    {type: true, title: '术后长期',csactive:false},
                    {type: true, title: '术后长期',csactive:false}
                ],
                hlgz: [
                    {type: false, title: '普外科护理常规',csactive:false},
                ],
                bqby: [
                    {type: true, title: '一级护理',csactive:false},
                    {type: true, title: '一级qqqq护理',csactive:false},
                    {type: true, title: '一级qqqq一级护一级护理住院第二天理住院第二天护理',csactive:false},
                ],
            },

        ],
    },
    created:function () {
      // this.getDataOne()
    },
    watch:{
        'picked':function (n,o) {
            if(n!=0){
                this.popText='原因';
                this.popPlaceholder='请输入变异退出原因';
                this.popModel=''
            }else{

                this.popText='备注';
                this.popModel='正常';
                this.popPlaceholder=''
            }
            if(n==3){
                this.popIshow=true;
            }else {
                this.popIshow=false;
            }
        }
    },
    methods: {
        menuActive:function (item) {
                if(this.isactiveClass!=null){
                    this.optionList[this.isactiveClass[2]][this.isactiveClass[3]][this.isactiveClass[1]].csactive=!this.optionList[this.isactiveClass[2]][this.isactiveClass[3]][this.isactiveClass[1]].csactive;
                    this.isactiveClass=null;
                }
                this.optionList[item[2]][item[3]][item[1]].csactive=!this.optionList[item[2]][item[3]][item[1]].csactive;

            if(item[0]==false){
                this.isactiveClass=item;
            }

        },
        gettext:function (item,index,$index,child) {
            // if(child!=undefined){
            //      this.ljlist[index][child][$index].zx=true
            // }else {
            //     this.ljlist[index].zx=true
            // }
            for(var i=0;i<this.ljlist.length;i++){
                if(this.setText.parentid){
                    if(this.ljlist[i].id==this.setText.parentid){
                        for(var j=0;j<this.ljlist[i].list.length;j++){
                            if(this.ljlist[i].list[j].id==this.setText.id){
                                this.ljlist[i].list[j].zx=false
                            }
                        }
                    }
                }
                if(item.parentid){
                    if(this.ljlist[i].id==item.parentid){
                        for(var j=0;j<this.ljlist[i].list.length;j++){
                            if(this.ljlist[i].list[j].id==item.id){
                                this.ljlist[i].list[j].zx=true
                            }
                        }
                    }
                }
                    if(this.ljlist[i].id==this.setText.id){
                        this.ljlist[i].zx=false
                    }
                    if(this.ljlist[i].id==item.id){
                        this.ljlist[i].zx=true
                    }
            }
            this.setText = item;
            this.$forceUpdate()
        },
        showlist: function () {
            this.show = !this.show
        },
        changeTimer:function (item) {
            this.dayActiveIndex=item[0];
            if(this.flag==false){
                for(var i=0;i<this.day.length;i++){
                    if(this.day[i].done==false){
                        this.day[i].done=true;
                        this.dayIndex=i;
                        this.flag=true;
                        return true
                    }
                }
            }

        },
        rj:function () {

        },
        topNew:function () {
            this.topNewPage('新增分支','page/zyysz/zyysz/hzgl/hzzx/userPage/fzpage.html');
        },
        pg:function () {
            this.isShow=true
        },
        doZjjl:function () {
          brzcList.ishow=true;
        },
        doclick:function (n) {
        },
        Wf_save:function(){},
        doshow:function () {

        },
        menu:function () {

        },
        checked:function (hsindex,type,data) {
            var that=this;
            this.isckShow=false;
            var yzIsOverCk=true;
            if(type=='all'){
                this.ischecked.ischeckedAll=!this.ischecked.ischeckedAll;
                for(var index=0;index<data.gz;index++){
                    that.ischecked.gz[index] = this.ischecked.ischeckedAll;
                }
                for(var index=0;index<data.zdyz;index++){
                    that.ischecked.zdyz[index] = this.ischecked.ischeckedAll;
                }
                for(var index=0;index<data.hlgz;index++){
                    that.ischecked.hlgz[index] =this.ischecked.ischeckedAll;
                }
                for(var index=0;index<data.bqby;index++){
                    that.ischecked.bqby[index] = this.ischecked.ischeckedAll;
                }
            }else{
                Vue.set(this.ischecked[type],hsindex,!this.ischecked[type][hsindex]);
                for(var index=0;index<data.gz;index++){
                    if(!that.ischecked.gz[index]){
                        yzIsOverCk=false
                    }
                }
                for(var j=0;j<data.zdyz.j;index++){
                    if(!that.ischecked.zdyz[j]){
                        yzIsOverCk=false
                    }
                }
                for(var t=0;t<data.hlgz;t++){
                    if(!that.ischecked.hlgz[t]){
                        yzIsOverCk=false
                    }
                }
                for(var u=0;u<data.bqby;u++){
                    if(!that.ischecked.bqby[u]){
                        yzIsOverCk=false
                    }
                }
                console.log(that.ischecked);
                this.ischecked.ischeckedAll=yzIsOverCk
            }
             this.$forceUpdate();
            // setTimeout(function () {
                this.$nextTick(function () {
                    panel.isckShow=true
                })
            // },0)
        },
    }
});
var brzcList = new Vue({
    el: '.pop-table',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 1,
        zxfa: 1,
        popContent:{},
        jsonList: [],
        ishow:false,
    },
    methods: {
        Wf_save:function () {

        },
        confirms: function () {
            this.num = 1
        },
        closes: function () {
            this.num = 1
        },
    },
});
var popRj=new Vue({
    el:'.popRj',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        isShow:false,
        popContent:{},
    },
    created:function () {

    },
    methods:function () {

    },
});
laydate.render({
    elem: '.todate',
    eventElem: '.zui-date i.datenox',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
    }
});
