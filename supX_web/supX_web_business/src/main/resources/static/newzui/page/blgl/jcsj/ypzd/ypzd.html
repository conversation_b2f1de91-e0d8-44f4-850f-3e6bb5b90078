<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药品字典</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ypzd.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">添加</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
            <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view ybglTable padd-l-10 padd-r-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50-1">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="jm20" z-width="80px">
                        <div class="zui-table-cell">药品编码</div>
                    </th>
                    <th z-field="jm21" z-width="80px">
                        <div class="zui-table-cell cell-xl">药品名称</div>
                    </th>
                    <th z-field="jm22" z-width="100px">
                        <div class="zui-table-cell">拼音简码</div>
                    </th>
                    <th z-field="jm23" z-width="100px">
                        <div class="zui-table-cell">药品规格</div>
                    </th>
                    <th z-field="jm24" z-width="100px">
                        <div class="zui-table-cell">分类编码</div>
                    </th>
                    <th z-field="jm25" z-width="100px">
                        <div class="zui-table-cell">手工编码</div>
                    </th>
                    <th z-field="jm26" z-width="100px">
                        <div class="zui-table-cell">药品他名</div>
                    </th>
                    <th z-field="jm27" z-width="100px">
                        <div class="zui-table-cell">他名代码</div>
                    </th>
                    <th z-field="jm28" z-width="100px">
                        <div class="zui-table-cell">化学名称</div>
                    </th>
                    <th z-field="jm29" z-width="100px">
                        <div class="zui-table-cell">化学名称简码</div>
                    </th>
                    <th z-field="jm30" z-width="100px">
                        <div class="zui-table-cell">条形码</div>
                    </th>
                    <th z-field="jm37" z-width="100px">
                        <div class="zui-table-cell">删除标志</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-height" id="zui-table">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :tabindex="$index" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.ypbm"></div></td>
                    <td>
                        <div class="zui-table-cell relative cell-xl text-over-2">
                            <i class="title title-width" v-text="item.ypmc" :data-title="item.ypmc"></i>
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.pyjm" :data-title="item.pyjm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ypgg" :data-title="item.ypgg"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.flbm" :data-title="item.flbm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sgbm" :data-title="item.sgbm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.yptm" :data-title="item.yptm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.tmdm" :data-title="item.tmdm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.hxmc" :data-title="item.hxmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.hxmcjm" :data-title="item.hxmcjm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.txm" :data-title="item.txm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell">
                            <div class="switch">
                                <input  type="checkbox" :checked="item.scbz==0?true:false" disabled/>
                                <label></label>
                            </div>
                        </div>
                    </td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<div class="side-form ng-hide pop-850" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list">
            <li>
                    <i>药品编码</i>
                    <input type="text" class="label-input background-h" disabled v-model="popContent.ypbm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>药品名称</i>
                    <input type="text" class="zui-input border-r4"  v-model="popContent.ypmc"  @keydown="nextFocus($event)"
                           @blur="setPYDM(popContent.ypmc,'popContent','pyjm')" />
            </li>
            <li>
                    <i>拼音简码</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.pyjm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>药品规格</i>
                    <input type="text" class="label-input"  v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>分类编码</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.flbm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>手工编码</i>
                    <input type="text" class="label-input"  v-model="popContent.sgbm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>药品他名</i>
                    <input type="text" class="label-input"  v-model="popContent.yptm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>他名代码</i>
                    <input type="text" class="label-input"  v-model="popContent.tmdm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>化学名称</i>
                    <input type="text" class="label-input"  v-model="popContent.hxmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>化学名称简码</i>
                    <input type="text" class="label-input"  v-model="popContent.hxmcjm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>条形码</i>
                    <input type="text" class="label-input"   v-model="popContent.txm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	皮试药品</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="psyp" :index="popContent.psyp" :val="popContent.psyp"
                                  :name="'popContent.psyp'">
                    </select-input>
            </li>
            <li>
                    <i>皮试类别</i>
                    <input type="text" class="label-input"   v-model="popContent.pslb" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>统筹类别</i>
                    <input type="text" class="label-input"  v-model="popContent.tclb" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>农保统筹类别</i>
                    <input type="text" class="label-input"   v-model="popContent.nbtclb" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>药品种类</i>
                    <input type="text" class="label-input"  v-model="popContent.ypzl" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	招标类型</i>
                    <input type="text" class="label-input "   v-model="popContent.zblx" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品大类</i>
                    <input type="text" class="label-input "   v-model="popContent.ypdl" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品剂型</i>
                    <input type="text" class="label-input "   v-model="popContent.ypjx" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品产地</i>
                    <input type="text" class="label-input "   v-model="popContent.ypcd" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品功效</i>
                    <input type="text" class="label-input "   v-model="popContent.ypgx" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	给药途径</i>
                    <input type="text" class="label-input "   v-model="popContent.gytj" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品包装单位</i>
                    <input type="text" class="label-input "   v-model="popContent.ypbzdw" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品分装单位</i>
                    <input type="text" class="label-input "   v-model="popContent.ypfzdw" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	分装比例</i>
                    <input type="text" class="label-input "   v-model="popContent.fzbl" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	剂量单位</i>
                    <input type="text" class="label-input"   v-model="popContent.jldw" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	基本剂量</i>
                    <input type="text" class="label-input "   v-model="popContent.jbjl" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	国家基本药物</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="gjjbyw" :index="popContent.gjjbyw" :val="popContent.gjjbyw"
                                  :name="'popContent.gjjbyw'">
                    </select-input>
            </li>
            <li>
                    <i>	产品标准号</i>
                    <input type="text" class="label-input "   v-model="popContent.cpbzh" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	批准文号</i>
                    <input type="text" class="label-input "   v-model="popContent.pcwh" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	GMP限价</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="gmpxj" :index="popContent.gmpxj" :val="popContent.gmpxj"
                                  :name="'popContent.gmpxj'">
                    </select-input>
            </li>
            <li>
                    <i>	建议最小剂量</i>
                    <input type="text" class="label-input "   v-model="popContent.jyzxjl" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	建议最大剂量</i>
                    <input type="text" class="label-input "   v-model="popContent.jyzdjl" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品进价</i>
                    <input type="text" class="label-input "   v-model="popContent.ypjj" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品批价</i>
                    <input type="text" class="label-input "   v-model="popContent.yppj" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	药品零价</i>
                    <input type="text" class="label-input "   v-model="popContent.yplj" @keydown="nextFocus($event)"/>
            </li>
            <li >
                    <i style="padding-top: 13px">删除标准</i>
                    <div class="switch" >
                        <input  type="checkbox" :checked="popContent.scbz==0?true:false"/>
                        <label></label>
                    </div>
            </li>

        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ypzd.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>