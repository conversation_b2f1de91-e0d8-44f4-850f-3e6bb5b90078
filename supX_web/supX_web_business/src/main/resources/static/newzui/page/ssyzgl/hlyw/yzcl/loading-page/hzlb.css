
.zui-table-view {
    margin: 0 10px;
}

.zt .zt-dsh {
    color: #f2a654;
}

.zt .zt-dzx {
    color: #ff4532;
}

.zt .zt-dly {
    color: #2885e2;
}

.zt .zt-yly {
    color: #13a950;
}

.zt .zt-dtz {
    color: #9e28e2;
}

.zt .zt-ytz {
    color: #757c83;
}
.kpFlex {
    padding-top: 15px;
    border: none;
    overflow: auto;
    width: 100%;
    display: flex;
    height: 100%;
    flex-wrap: wrap;
    align-self: center;
    /*align-items: center;*/
    align-content: flex-start;
    margin: 0 auto;
    justify-content: center;
}
.caozuo i::before {
    width: 24px;
    height: 24px;
    display: inline-block;
}

.caozuo .fa {
    font-size: 20px;
}

.caozuo .more:hover {
    color: #1abc9c;
}

.caozuo .more .content {
    position: absolute;
    display: none;
    background-color: #1abc9c;
    padding: 5px 10px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    top: 20px;
    right: -12px;
    z-index: 1;
    font-size: 0;
    -webkit-box-shadow: rgba(0, 0, 0, 0.4) 0px 0px 10px 0px;
    box-shadow: rgba(0, 0, 0, 0.4) 0px 0px 10px 0px;
}

.caozuo .more .content.top {
    top: -40px !important;
}

.caozuo .more .content::before {
    position: absolute;
    content: '';
    display: none;
    top: -5px;
    right: 18px;
    z-index: 1;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #1abc9c;
}

.caozuo .more .content.top::before {
    top: 35px;
    border-top: 5px solid #1abc9c;
    border-bottom: none;
}

.caozuo .more:hover .content::before {
    display: inline-block;
}

.caozuo .more:hover .content {
    display: inline-block;
}

.caozuo .relative > .icon-icon:hover {
    opacity: 0.6;
}


.tong-search .zui-form .padd-r-20 {
    padding-left: 45px;
}

.text-decoration {
    color: #1abc9c;
    cursor: pointer;
    text-decoration: underline;
}

.userNameImg {
    border-radius: 50px;
    width: 60px;
    display: inline-block;
    height: 60px;
    margin: 0 0 0 2px;
    text-align: center;
}

.userNameImg img {
    width: 100%;
    height: 100%;
    margin: auto;
    cursor: pointer;
    display: inline-block;
}

.userName {
    font-size: 18px;
    color: #1abc9c;
    cursor: pointer;
    font-weight: 600;
    font-family: 'PingFangSC-Semibold', '幼圆';
    line-height: 25px;
}

.userName-pin {
    box-sizing: border-box;
    width: 18px;
    height: 18px;
    display: inline-block;
    border-radius: 5px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
    color: #fff;
    vertical-align: middle;
}

.userName-pin.xrybr {
    border: 1px solid rgb(26, 188, 156);
    background-color: rgba(26, 188, 156, .5);
}

.userName-pin.hz {
    background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);
    border: 1px solid #f46161;
    border-radius: 100%;
}

.userName-pin.w {
    background-image: linear-gradient(-180deg, #7ad5ff 0%, #3d9aeb 100%);
    border: 1px solid #3196ed;
    border-radius: 100%;
}

.userName-pin.j {
    background-image: linear-gradient(-180deg, #d3d8d7 0%, #acb8b6 100%);
    border: 1px solid #a4b0ae;
    border-radius: 100%;
}

.userName-pin.r {
    background-image: linear-gradient(-180deg, #f96868 0%, #f96868 100%);
    border: 1px solid #f96868;
    border-radius: 100%;
}

.userName-pin.d {
    background-image: linear-gradient(-180deg, #55ffff 0%, #55ffff 100%);
    border: 1px solid #55ffff;
    border-radius: 100%;
}

.zyys {
    font-size: 12px;
    color: #e96509;
    line-height: 18px;
    max-width: 50px;
}

.userName-pin img {
    /*background-image: url("/newzui/pub/image/pin.png");*/
    /*background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);*/
    /*border: 1px solid #f46161;*/
    border-radius: 5px;
    background-color: #f46161;
    width: 18px;
    height: 18px;
    display: block;
    /*background-position: center center;*/
    /*background-repeat: no-repeat;*/
}

.userName-lc img {
    /*background-image: linear-gradient(-180deg, #ffb456 3%, #ed8805 100%);*/
    border: 1px solid #ed8705;
    border-radius: 4px;
    width: 20px;
    background-color: #ed8705;
    height: 20px;
    font-size: 10px;
    color: #ffffff;
}

.text-left {
    color: #000;
    line-height: 20px;
}

.username-cwh {
    color: #4b8ad4;
}

.djzt {
    position: absolute;
        right: -89px;
        top: -1px;
        cursor: pointer;
        box-shadow: 0 6px 24px 0 rgb(0 0 0 / 30%);
        transform: rotate(36deg);
        font-size: 15px;
        color: #000000;
        text-align: -webkit-center;
        overflow: hidden;
        width: 195px;
        height: 40px;
        line-height: 40px;
        padding-right: 20px;
}

.red {
    background-color: #EF6263;
}
.blues{
background-color: #123456;
}
.blue {
    background-color: #4B9BFD;
}

.redCj {
    background-color: #F23E3E;
}
.yellow{
	background-color: #ffff00;
}
.green{
	background-color: #00ff00;
}
.header-text {
    background: #F6FDFA;
    width: 100%;
    min-height: 60px;
    border-bottom: 1px solid #E9F0EE;
    overflow: hidden;
    position: relative;
}

.userWidth {
    /*padding: 0 9px;*/
    /*margin: 0 9px;*/
    background: #ffffff;
    box-shadow: 0 0 9px 0 rgba(1, 23, 18, 0.40);
    /*width: 307px;*/
    margin-bottom: 15px;
}



.flex {
    display: flex;
}

.flex_items {
    align-items: center;
}

.userName {
    margin-right: 14px;
    max-width: 72px;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    /*text-overflow: ellipsis;*/

}

.userName-pin {
    margin-right: 5px;

}

.main-content {
    margin: 5px 10px;
}

.main-content p {
    font-size: 13px;
    color: #222;
    text-align: left;
    height: 18px;
}

.margin-l13 {
    margin-left: 56px;
}

.zui-item .user-footer-img {
    background-color: transparent;
    width: 24px;
    height: 24px;
    background-size: cover;
    vertical-align: text-top;
}

.user-footer-img {
    width: 35px;
    height: 35px;
    display: inline-block;
    cursor: pointer;
    border-radius: 100%;
    /*margin-right: 25px;*/
    text-align: center;
    background: #E9EAEB center/24px 24px no-repeat;
}

.ljImg {
    background-image: url("/newzui/css/images/shenhe.png");
}

.yzImg {
    background-image: url("/newzui/css/images/huli.png");
}

.blImg {
    background-image: url("/newzui/css/images/sance.png");
}

.footer-text {
    text-align: center;
    padding-right: 5px;
    padding-left: 10px;
}

.cz-butt {
    background: #e5e7ea;
    border-radius: 11px;
    width: 46px;
    height: 22px;
    font-size: 12px;
    color: #48494c;
    line-height: 22px;
}

.cz-butt.active {
    background: #1abc9c;
}

.cz-butt .butt {
    border-radius: 11px;
    width: 46px;
    height: 22px;
    display: inline-block;
    cursor: pointer;
}

.cz-butt.active .butt {
    color: #fff;
}

.alignItems {
    align-items: center;
}

.hzbr {
    color: #F17676;
}
.menu-right {
    padding: 0px 15px;
    height: 32px;
}
.menu-right {
    padding: 0px 15px;
    height: 32px;
}

.menu-right span {
    padding: 0 5px;
    display: inline-block;
    /*height: 24px;*/
}

.menu-right .fenge {
    font-size: 0;
    width: 1px;
    height: 17px;
    background: #646f82;
    padding: 0;
}

.user-footer-img.more:hover {
    opacity: 1;
}

.user-footer-img.more::before,
.user-footer-img.more.hover:hover::before {
    width: 35px;
    height: 35px;
    background-position: center;
}

.user-footer-img .content {
    width: 300px;
    top: 34px !important;
    right: -7px !important;
}

.tong-search {
    padding: 13px 0px 5px 20px;
}

.fa-th-large:before {
    font-size: 18px;
    padding-top: 3px;
}

.fa-th-large.active::before {
    color: #1abc9c;
}

.grid-box .zt-xrybr .userWidth {
    border: 1px solid rgb(26, 188, 156);
    box-shadow: 1px 0 13px 0 rgba(26, 188, 156, 1);
}

.hzts-message {
    line-height: 34px;
    font-size: 14px;
    color: #1c2024;
}

.hzts-message .box {
    cursor: pointer;
    display: inline-block;
    margin-right: 30px;
}

.hzts-message .box .font-22 {
    font-size: 22px;
}

.hzts-message .box .color-1abc9c {
    color: #1abc9c;
}

.hzts-message .box .color-fe3f3f {
    color: #fe3f3f;
}

.hzts-message .box .color-fa6969 {
    color: #fa6969;
}

.hzts-message .box .color-3ba4ff {
    color: #3ba4ff;
}

.hzts-message .box .color-494d50 {
    color: #494d50;
}

.hzts-message .box .color-ef904d {
    color: #ef904d;
}

.action-bar.fixed {
    padding-top: 8px;
    padding-bottom: 8px;
    height: 50px;
}

.cz-butt {
    position: relative;
}

.cz-butt .content {
    box-shadow: 0 0 8px 0 rgba(0, 21, 17, 0.40);
    border-radius: 4px;
    width: 360px;
    min-height: 282px;
    font-size: 13px;
    color: #1c2024;
    background-color: #fff;
    overflow: hidden;
    z-index: 999;
    display: none;
    position: absolute;
    top: -80px;
    left: 35px;
}

.cz-butt .content.left {
    left: auto;
    right: 35px;
}

.cz-butt .content.top {
    top: auto;
    bottom: -65px;
}

.cz-butt .content.top.bottom {
    bottom: -0px;
}

.cz-butt.active .content {
    display: flex;
}

.cz-butt .content .cont-butt {
    width: 120px;
    height: 35px;
    text-align: center;
    line-height: 35px;
    box-sizing: border-box;
    border-right: 1px solid #dfe3e9;
    border-bottom: 1px solid #dfe3e9;
    user-select: none;
    cursor: pointer;
}

.cz-butt .content .cont-butt:nth-child(4),
.cz-butt .content .cont-butt:nth-child(5),
.cz-butt .content .cont-butt:nth-child(6),
.cz-butt .content .cont-butt:nth-child(10),
.cz-butt .content .cont-butt:nth-child(11),
.cz-butt .content .cont-butt:nth-child(12),
.cz-butt .content .cont-butt:nth-child(16),
.cz-butt .content .cont-butt:nth-child(17),
.cz-butt .content .cont-butt:nth-child(18),
.cz-butt .content .cont-butt:nth-child(22),
.cz-butt .content .cont-butt:nth-child(23),
.cz-butt .content .cont-butt:nth-child(24) {
    background: #fafafa;
}

.cz-butt .content .cont-butt:hover {
    background: #1abc9c;
    color: #fff;
}

.useritem .userlist {
    border-left: 1px solid #eeeeee;
    border-right: 1px solid #eeeeee;
}

.useritem .userlist:nth-child(even) {
    background: #fdfdfd;
}

.useritem .userlist:nth-child(odd) {
    background: #ffffff;
}

.useritem .userlist .userKey {
    border-top: 1px solid #eeeeee;
    width: 155px;
    min-height: 30px;
    font-size: 14px;
    color: #7f8fa4;
    text-align: center;
    padding-right: 20px;
}

.useritem .userlist .userValue {
    width: 298px;
    padding: 6px 0;
    padding-left: 19px;
    font-size: 14px;
    border-left: 1px solid #eeeeee;
    color: #354052;
    text-align: center;
    border-top: 1px solid #eeeeee;
}

.height-100 {
    height: 100%;
}

.useritem .userlist .font-14-654 {
    font-size: 14px;
    color: #f2a654;
}

.ksys-side .useritem .userlist {
    display: flex;
    padding-bottom: 0;
}

.kongchuang {
    background: url("/newzui/css/images/kongchuang.png") center 42px/ 127px 64px no-repeat;
}

.kongchuang .chuangweihao {
    color: #4B8AD4;
    padding: 13px 15px 9px;
}

.bqcydj_model {
    width: auto;
    padding: 14px 15px 33px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.grid-box  .userWidth.active {
    border: solid 1px #f96868;
    box-shadow: 1px 0 13px 0 #f96868;
}
