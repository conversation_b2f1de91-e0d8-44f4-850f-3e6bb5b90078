<!DOCTYPE html>
<html lang="en">
<head>
    <link href="user.css" rel="stylesheet">
</head>
<style>

    canvas{
        cursor: default;
    }

    table{
        border-collapse: collapse;
        /*overflow: hidden;*/
        font-size: 12px;
        border: 0;
    }

   .twd table tr td:first-child{
        padding-left: 4px;
        border-left: 0;
    }

  .twd  td{
        height: 14px;
        border-left: 1px solid #555;
        border-bottom: 1px solid #555;
    }

</style>
<body>
<div class="wrapper">
    <div class="panel ">

        <div class="grid-box">
            <div class="col-xxl-4 flex margin-l-10 margin-top-5">
                <div class="flex margin-b-15">
                    <label class="whiteSpace margin-r-5 ft-14">时间段</label>
                    <div class="zui-input-inline margin-l13 zui-date">
                        <i class="datenox icon-rl"></i>
                        <input class="zui-input wh200  todate" v-model="time" id="time"  type="text"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="flex twd-center">
        <div class="paev fa-angle-left" onclick="prev()"></div>
        <div class="twd-scroll" >
            <div class="twd">
                <section style="width:676px;margin:0;border: 2px solid #000000">
                    <table id="table_1" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>日期</td>
                            <td v-for="item in currentDay" v-text="item"></td>
                        </tr>
                        <tr>
                            <td>住院天数</td>
                            <td v-for="item in zyDay" v-text="item"></td>
                        </tr>
                        <tr>
                            <td>手术后天数</td>
                            <td v-for="item in 7"></td>
                        </tr>
                    </table>
                    <canvas id="twd_title"></canvas>
                    <canvas id="twd_cvs" style="margin-top: -1px"></canvas>
                    <table id="table_2" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>呼吸(次/分)</td>
                            <td style="width: 14px" v-for="item in hx_list" v-text="item"></td>
                        </tr>
                    </table>
                    <table id="table_3" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>血压(mmHg)</td>
                            <td v-for="item in xy_list" v-text="item"></td>
                        </tr>
                    </table>
                    <table id="table_4" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>入量(ml)</td>
                            <td v-for="item in rlList" v-text="item"></td>
                        </tr>
                        <tr>
                            <td>出量(ml)</td>
                            <td v-for="item in clList" v-text="item"></td>
                        </tr>
                        <tr>
                            <td>大便(次/日)</td>
                            <td v-for="item in dbList" v-text="item"></td>
                        </tr>
                        <tr>
                            <td>体重(kg)</td>
                            <td v-for="item in tzList" v-text="item"></td>
                        </tr>
                        <tr>
                            <td>身高(cm)</td>
                            <td v-for="item in sgList" v-text="item"></td>
                        </tr>
                        <tr v-for="item in 5">
                            <td></td>
                            <td v-for="item in 7"></td>
                        </tr>
                    </table>
                </section>
            </div>
        </div>
        <div class="next fa-angle-right" onclick="next()"></div>
    </div>
</div>
<script type="text/javascript" src="twd.js"></script>
</body>
</html>