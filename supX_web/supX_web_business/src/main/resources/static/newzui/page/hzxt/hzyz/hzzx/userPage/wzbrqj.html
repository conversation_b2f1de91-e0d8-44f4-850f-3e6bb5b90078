<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>危重病人抢救</title>
    <link href="userPage/wzbrqj.css" rel="stylesheet">
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css"/>
    <link rel="stylesheet" href="/newzui/pub/css/print.css" media="print"/>
</head>
<body>
<main id="content" class="padd-l-10 padd-r-10 printHide" v-if="pageShow">
    <div v-for="(list,index) in 2" :style="{'marginTop':index>=1?'35px':''}">
        <div >
            <p class="swtl font-bolder">危重抢救{{index+1}}</p>
            <p class="swtl-hr"></p>
        </div>
        <div class="flex-container" >
            <div class="width973" >
                <ul class="swtl-item">
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-date swtl-icon"></span>
                            <span class="icon-text color-green">起止时间</span>
                        </div>
                        <div class="padd-b-15">
                            <p class="date swtl-text-content color-333" v-if="editText">2020年12月12日 12:55:12</p>
                            <div class="zui-date relative swtl-text-content" v-show="!editText"  style="width: 360px">
                                <i class="datenox icon-rl"></i>
                                <input class="zui-input  color-333 text-indent-0" id="timeVal" style="padding-top: 0" value="2020年12月12日 12:55:12">
                            </div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c margin-b-5">
                            <span class="icon-img-ch swtl-icon"></span>
                            <span class="icon-text color-f4b26b">参加人员</span>
                        </div>
                        <div  class="flex-container margin-b-15 padd-b-15 swtl-text-content">
                            <div @mouseleave="hoverName()" class="Headimg " :class="!editText?'HeadPortrait':'HeadImg'" v-for="(list,$index) in 1" :id="list">
                                <i @click="deleteImg()" :class="!editText?'HeadStart':''"></i>
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="color-757c83 font12">刘医生</p>
                            </div>
                            <span @click="tjys" class="iconfont  icon-upload col6_span1" v-if="!editText"   @mouseenter="tipsShowList(true,index,'tipsShow')"
                                  @mouseleave="tipsShowList('',index,'tipsShow')">
                                    <i v-show="tipsShow[index]"  class="tips">添加主持人</i>
                                </span>
                            <div @mouseleave="hoverName()" class="Headimg" :class="!editText?'HeadPortrait':''" v-for="(list,$index) in 6" :id="list">
                                <i :class="!editText?'HeadStart':''"></i>
                                <p @mouseenter="hoverName(true,$index,$event)"  class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="color-757c83 font12">刘医生</p>
                            </div>
                            <span @click="tjys" class="iconfont  icon-upload col6_span1" v-if="!editText"    @mouseenter="tipsShowList(true,index,'tipsShow1')"
                                   @mouseleave="tipsShowList('',index,'tipsShow1')">
                                    <i v-show="tipsShow1[index]" class="tips">添加参与人</i>
                                </span>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="username text-center margin-t-5 margin-b-5 font-16">刘医生</p>
                                <div class="flex-container flex-jus-c">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-zj swtl-icon"></span>
                            <span class="icon-text color-72bc1a">抢救过程</span>
                        </div>
                        <div class="padd-b-15">
                            <p v-if="editText" class="swtl-text-content color-333">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</p>
                            <textarea v-if="!editText" rows="1"  class="zui-input swtl-text-content text-indent-0 color-333" @keydown="rowsJ($event)">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</textarea>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-qz swtl-icon"></span>
                            <span class="icon-text color-00a7ff">参与人签字</span>
                        </div>
                        <div class="padd-b-15 margin-t-5">
                            <ul class="Photo swtl-text-content flex-container" >
                                <li class="PhotoImg" v-for="(list,index) in canvasImgArr"  :id="canvasImgArr">
                                    <i :class="!editText?'HeadStart':''"></i>
                                    <p class="PhotoImg" @click="preview"  :id="canvasImgArr" :style="{backgroundImage: 'url('+list+')'}"></p>
                                </li>

                                <span @click="cyrqz($event)" class="iconfont  icon-upload col6_span1" v-if="!editText"    @mouseenter="tipsShowList(true,index,'tipsShow2')"
                                      @mouseleave="tipsShowList('',index,'tipsShow2')">
                                    <i v-show="tipsShow2[index]" class="tips">添加签名</i>
                                </span>
                            </ul>
                        </div>
                    </li>
                    <preview :preview-update="previewshow" :src="'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532503254326&di=a2175d25c9f4ccc582e3ae37531ec0a4&imgtype=0&src=http%3A%2F%2Fwww.wallcoo.com%2Fcartoon%2FKitsunenoir_Design_Illustration_V%2Fwallpapers%2F2560x1440%2Fkim-holtermand-reflections.jpg'" @preview-hide="previewHide"></preview>
                    <li class="list">
                        <div class="flex-container flex-align-c margin-b-5">
                            <span class="icon-img-jl swtl-icon"></span>
                            <span class="icon-text color-f4b26b">记录者</span>
                        </div>
                        <div  class="flex-container margin-b-15 padd-b-15 swtl-text-content">
                            <div @mouseleave="hoverName()" class="Headimg " :class="!editText?'HeadPortrait':''" v-for="(list,$index) in 1" :id="list">
                                <i :class="!editText?'HeadStart':''"></i>
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="color-757c83 font12">刘医生</p>
                            </div>
                            <span @click="tjys" class="iconfont  icon-upload col6_span1" v-if="!editText"   @mouseenter="tipsShowList(true,index,'tipsShow3')"
                                  @mouseleave="tipsShowList('',index,'tipsShow3')">
                                    <i v-show="tipsShow3[index]"  class="tips">添加记录者</i>
                                </span>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="username text-center margin-t-5 margin-b-5 font-16">刘医生</p>
                                <div class="flex-container flex-jus-c">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </li>
                    <div class="flex-container flex-jus-e" style="width: 100%">
                        <button v-waves class="root-btn btn-parmary" style="margin-right: 0" @click="submit()" v-if="!editText">确定</button>
                    </div>
                </ul>
            </div>
            <div class="flex-container relative" style="width: 2.7%" >
                <i class="iconfont icon-iocn46"></i>
                <span class="font14 cursor" @click="editShow" style="position: absolute;right: 0;z-index: 11111;">编辑</span>
            </div>
        </div>
    </div>
</main>
<div class="flex-container flex-jus-c flex-one flex-align-c" v-if="pageShow" id="rescue">
    <div>
        <div class="fqtlbg" >
            <div v-if="fqqjShow">
                <div class="point point1"></div>
                <div class="point point2"></div>
                <div class="point point3"></div>
            </div>
            <div class="bg-center " :class="success?'Success':!success?'start':success==undefined?'fail':''" @click="!success?rescueClick():''"></div>
            <p class="color-1abc9c margin-t-5 text-center">{{fqqj}}</p>
        </div>
        <div class="flex-container" v-if="fqqjShow" style="margin-top: -29px">
            <div class="HeadPortrait" :class="fail?'startFail':!success?'startFail':''" v-for="list in objAvatar" :id="list">
                <p class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)"></p>
                <p class="color-f2a654 font12">刘医生</p>
            </div>
            <span class="iconfont  icon-upload col6_span1"></span>
        </div>
        <div class="flex-container flex-jus-c margin-t-10">
            <button v-waves v-if="fqqjShow && fqsucc" class="tong-btn btn-parmary" @click="successShow">确定</button>
            <button v-waves v-if="fqqjShow" class="tong-btn btn-parmary-f2a" @click="failShow">发起失败测试</button>
            <button v-waves v-if="fail" class="tong-btn btn-parmary-f2a" @click="clear">取消</button>
            <button v-waves v-if="fail" class="tong-btn btn-parmary" @click="successShow">再次发起</button>
        </div>
    </div>
</div>

<div class="side-form  pop-width"  :class="{'ng-hide':type}" v-cloak id="brzcList" role="form">
    <div class="fyxm-side-top flex-between">
        <span>添加抢救医生</span>
        <span class="fr closex ti-close"></span>
    </div>
    <div class="tong-search">
        <div class="top-form">
            <label class="top-label font14">科室</label>
            <select-input style="width: 122px;" @change-data="resultChange" :not_empty="false"
                          :child="jzType_tran" :index="popContent.jzbz" :val="popContent.jzbz"
                          :name="'popContent.jzbz'">
            </select-input>
        </div>
    </div>
    <div class="ksys-side" style="padding:0 8px 0 9px">
        <div class="zui-table-view">
            <div class="zui-table-header">
                <table class="zui-table" >
                    <thead>
                    <tr>
                        <th>
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>医生姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m"><span>科室</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>职称</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index" v-for="(item, $index) in 25"  @dblclick="edit($index)">
                        <td>
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td ><div class="zui-table-cell cell-s"><span>周立军</span></div></td>
                        <td ><div class="zui-table-cell cell-m"><span>全科</span></div></td>
                        <td ><div class="zui-table-cell cell-s"><span>科主任</span></div></td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class=" noData text-center zan-border">暂无数据...</p>-->
            </div>
        </div>
    </div>
    <div class="ksys-btn zui-table-tool" style="height: 66px">
        <button v-waves class="root-btn btn-parmary-d9" @click="Sure">确定</button>
        <button v-waves class="root-btn btn-parmary" @click="cancel">取消</button>
    </div>
</div>
<div class="popPrint" v-if="PrintShow">
    <div class="pophide printHide show"></div>
    <div class="zui-form confirm-height podrag show wh630" style="background:#ffffff;width:630px;height:600px;">
       <div style="width: 100%;float: left">
           <i class="iconfont icon-iocn55 printHide color-1abc9c icon-font18" @click="close" style="float: right;padding: 15px;cursor: pointer"></i>
       </div>
        <div style="font-size:22px;color:#000000;" class="text-center padd-b-10">病重（危）通知单（存根）</div>
        <div style="padding: 0 31px 0 28px;color:#000000;">
        <div class="flex-container padd-b-10">
            <div style="width: 32%" class="flex-container">
                <span class="padd-r-10">患者姓名</span>
                <span>Henry</span>
            </div>
            <div style="width: 45%">
                <span class="padd-r-10">性别</span>
                <span class="padd-r-25">女</span>
                <span class="padd-r-10">年龄</span>
                <span>88</span>
            </div>
            <div style="width: 40%" class="flex-container">
                <span class="padd-r-10">住院（门诊号）</span>
                <span>8888888888</span>
            </div>
        </div>
        <div class="padd-b-10">
            <span class="padd-r-10">入院时间</span>
            <span>2008年8月22日59分</span>
        </div>
        <div class="padd-b-10">
            <span class="padd-r-10">诊断</span>
            <span>诊断诊断诊断诊断诊断诊断</span>
        </div>
        <div class="padd-b-10">
            患者因病情危重，住院诊疗过程中随时可能进一步恶化，随时有什么危险及相关并发症，特此
            通知家属（或单位），多互相联系，告知情况。
        </div>
        <div class="padd-b-10">
            <span style="padding-right: 83px">病危通知时间</span>
            <span style="padding-right: 23px">年</span>
            <span style="padding-right: 21px">月</span>
            <span style="padding-right: 15px">日</span>
            <span style="padding-right: 23px">时</span>
            <span style="padding-right: 33px">分</span>
            <span>家属（或单位）签名：</span>
        </div>
        <div class="padd-b-10">
            <span style="padding-left: 70px;padding-right: 83px">科</span>
            <span style="padding-right: 115px">病室</span>
            <span>医师签名</span>
        </div>
        </div>
        <button v-waves class="root-btn btn-parmary printHide" style="position: absolute;bottom: 23px;right: 12px" @click="print">打印</button>
    </div>
</div>
<div class="autograph" id="autograph"v-show="autographShow" style="top: 60%;"   :style="[{left:objSetLT.left+'px'}]">
    <div style="width: 100%;float: left">
        <i class="iconfont icon-iocn55 color-1abc9c icon-font18" @click="close" style="float: right;padding: 15px;cursor: pointer"></i>
    </div>
    <canvas id="canvas" ref="canvas"  @mouseleave="mouseleaves($event)"  @mousedown="mousedowns($event)" @mouseup="mouseups($event)" @mousemove="mousemoves($event)"  height="174" width="598"></canvas>
    <div class="flex-container flex-jus-e">
        <button v-waves class="root-btn btn-parmary margin-r-15" @click="clear">清除</button>
        <button  v-waves class="root-btn btn-parmary margin-r-15" @click="submit">确定</button>
    </div>
</div>
<script type="text/javascript" src="userPage/wzbrqj.js"></script>
</body>
</html>
