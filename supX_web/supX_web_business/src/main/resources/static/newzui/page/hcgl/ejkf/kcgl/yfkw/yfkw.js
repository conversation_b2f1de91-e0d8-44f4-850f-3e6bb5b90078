    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data:{
            //库房列表
            kfList: [],
            //库房
            yf: 0,
            jsonList: [],
            YfkwList: [],
            popContent:{},
            totlePage: 0,
            isChecked: [],
            isCheckAll: false,
            testVal: {},
            isShowYfkw: true,
            isShowYpkw: false,
            isUpdate : 0,
        },
        updated:function (){
            changeWin();
        },
        mounted:function(){
            this.getYf();
        },
        methods:{
            //获取数据
            getData: function() {
                common.openloading('.zui-table-view')
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglYfkw&types=query&json=' + JSON.stringify(this.param), function(json) {
                    if(json.a == "0") {
                        // wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.YfkwList = json.d;
                        console.log(wrapper.YfkwList);
                    }
                });
                common.closeLoading()
            },
            edit: function(num) {
                this.isUpdate = 1;
                if(num == null) {
                    for(var i = 0; i < this.isChecked.length; i++) {
                        if(this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if(num == null) {
                        malert("请选中你要修改的数据");
                        return false;
                    }
                }

                wap.popContent = this.YfkwList[num];
                wap.title='编辑药房库位';
                wap.open();

            },
            AddMdel:function () {
            	wrapper.isUpdate = 0;
                wap.title='新增药房库位';
                    wap.open();
                    wap.popContent={};
            },
            getYf: function() {
                //加载药房列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
                    function(data) {
                        if(data.a == 0) {
                            wrapper.kfList = data.d.list;
                            Vue.set(wrapper.param,'yfbm',wrapper.kfList[0].yfbm);//默认
                            wrapper.getData();
                        } else {
                            malert('失败','top','defeadted')
                        }

                    });
            },
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                Vue.set(this.param, 'yfbm', val[0]);
                Vue.set(this.param, 'kwmc', val[4]);
                this.getData();
            },

        }
    });

    //右侧
    var wap=new Vue({
        el: '#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            dg: {
                page: 1,
                rows: 2000,
                sort: '',
                order: 'asc'
            },
            //药品列表
            ypList: [],
            //医疗机构列表
            jgList: [],
            //医疗机构
            yljg: 0,
            //库位列表
            popContent:{},
            kwList: [],
            //药品
            yp: 0,
            //库位
            kw: 0,
            isShow: false,
            isShowAddYfkw: false,
            isShowAddYpkw: false,
            isKeyDown: null,
            title: '',
            time: null

        },
        methods: {
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },
            //保存库位信息
            saveData: function() {
                //准备参数
                this.popContent.yfbm = wrapper.param.yfbm;
                
                var url = '';
                if(wrapper.isUpdate == 0){
                	//添加
                	//保存药品库位
                	url = '/actionDispatcher.do?reqUrl=New1YfbKcglYfkw&types=save';
                    var json = this.popContent;
                  //保存数据
                    this.$http.post(url, JSON.stringify(json)).then(function(data) {
                        if(data.body.a == 0) {
                            malert("上传数据成功",'top','success');
                            // wap.closes();
                            wrapper.getData();
                            this.popContent={};
                        } else {
                            malert("上传数据失败",'top','defeadted');
                        }
                    }, function(error) {
                        console.log(error);
                    });
                }else{
                	//修改
                	url= "/actionDispatcher.do?reqUrl=New1YfbKcglYfkw&types=update";
                	//保存药品库位
                    var json = this.popContent;
                  //保存数据
                    this.$http.post(url, JSON.stringify(json)).then(function(data) {
                        if(data.body.a == 0) {
                            malert("上传数据成功",'top','success');
                            // wap.closes();
                            wrapper.getData();
                            this.popContent={};
                            wap.closes();
                        } else {
                            malert("上传数据失败",'top','defeadted');
                        }
                    }, function(error) {
                        console.log(error);
                    });
                }

            }
        }
    });
    laydate.render({
        elem: '.todate'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            wrapper.param.beginrq = value.slice(0,10);
            wrapper.param.endrq =value.slice(13,23);
            wrapper.getData();
        }
    });


