    var formDate = function (value) {
        var d = new Date(value);
        return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
    }
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase ,mformat, printer],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
            dyShow:false,
            sqShow:false,
            isFold:false,
            bg:'0',
            apply:'',
            jysbList:'',
            zxsb:'',
            brxm:'',
            bah:'',
            sendbg:[],
            updateList:[]
        },
        created:function(){
        	//获取检验设备
        	$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryJysb", function(json) {
        		wrapper.jysbList = json.d.list;
         		console.log(json);
         	});
        	var s=new Date().getTime()
        	var l=new Date()
        	var e=l.setDate(l.getDate()+1)
            this.apply=formDate(s)+' - '+formDate(e);
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods:{
        	formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            },
            print: function (cfList) {
                //特殊设备数据处理
                if(wrapper.bglx_tran[wrapper.printList[0]['bglx']]=='通用报告1'){//陆坪尿液数据处理
                	var index=11;
                	var list=[];
                	for (var int = 0; int <= 10; int++) {
                		var i=int+index;
						if(i<=18 && cfList[i] != undefined){
							cfList[int].zbxmmcjj=cfList[i].zbxmmc;
							cfList[int].valueNjj=cfList[i].valueN;
							cfList[int].ckzTjj=cfList[i].ckzT;
						}
						list.push(cfList[int])	
					}
                	cfList=list;
                }
                

            	
                // 查询打印模板
                var json = {repname:wrapper.bglx_tran[wrapper.printList[0]['bglx']]};
               // var json = {repname: '通用报告'};//这个名字去匹配后台模板名字
                $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    if (json.d.length == 0) {
                        //json.d[0] = printTemplets.getTempletByName('陆坪中心卫生院生化检验报告单');
                    	json.d[0] = printTemplets.getTempletByName(wrapper.bglx_tran[wrapper.printList[0]['bglx']]);
                    }
                    // 清除打印区域
                    wrapper.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    // 为打印前生成数据
                    wrapper.printContentNew(wrapper.printList[0]);
                    wrapper.printTrend(cfList);
                    // 开始打印
                    window.print();
                });
            },
            getPrintData: function (list) {
                var json = '{"list":' + JSON.stringify(list) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=LisCgjy&types=queryJydjPrintData', json).then(function (data) {
                        if (data.body.d != null) {
                            wrapper.printList = data.body.d.list;
                            // 性别转换
                            wrapper.printList[0]['xb'] = wrapper.brxb_tran[wrapper.printList[0]['xb']];
                            // 年龄转换
                            wrapper.printList[0]['nldw'] = wrapper.nldw_tran[wrapper.printList[0]['nldw']];
                            //打印行数
                            var hs=wrapper.printList[0]['dyhs']==null ? 20 : wrapper.printList[0]['dyhs'];
                            //打印数据分页
                            var num = Math.ceil(wrapper.printList[0]['jydjmxList'].length / hs);
                            for (var i = 0; i < num; i++) {
                                var cfList = [];
                                for (var j = 0; j < hs; j++) {
                                    if (wrapper.printList[0]['jydjmxList'][i * hs + j] != null) {
                                        cfList.push(wrapper.printList[0]['jydjmxList'][i * hs + j]);
                                        if (wrapper.printList[0]['jydjmxList'][i * hs + j].valueT != null) {
                                            wrapper.printList[0]['jydjmxList'][i * hs + j].valueN = wrapper.printList[0]['jydjmxList'][i * hs + j].valueT
                                        }
                                    }
                                }
                                wrapper.print(cfList);
                            }
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            },
            //打印
            daying:function () {
            	console.log(ztable.isChecked);
            	if(ztable.isChecked.length == 0){
        			malert('请选择要打印的报告单','top','defeadted');
        			return;
        		}
            	var List=[];
        		for (var i = 0; i < ztable.isChecked.length; i++) {
					if(ztable.isChecked[i]){
						List.push(ztable.jsonList[i]);
					}
				}
        		
        		if(List.length == 0){
        			malert('请选择要打印报告单','top','defeadted');
        			return;
        		}
        		
        		
        		//打印审核成功的数据
                setTimeout(function () {
                    wrapper.getPrintData(List);
                }, 2000)
        		
        		
            },
            guolu:function () {
                filter.isShow=true;
            },
            piliangdaying:function () {
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.dyShow=true;
                pop.flag=false
                pop.title='报告批量打印';

            },
            //取消审核
            cancleCheck:function(){
            	//取消批量选择审核
        		if(ztable.isChecked.length == 0){
        			malert('请选择要取消审核的报告单','top','defeadted');
        			return;
        		}
        		wrapper.updateList=[];
        		for (var i = 0; i < ztable.isChecked.length; i++) {
					if(ztable.isChecked[i]){
						wrapper.updateList.push(ztable.jsonList[i]);
					}
				}
        		
        		if(wrapper.updateList.length == 0){
        			malert('请选择要审核的报告单','top','defeadted');
        			return;
        		}
        		
        		var data = '{"list":' + JSON.stringify(wrapper.updateList) + '}';
	       		this.$http.post('/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=cancelCheck',data).then(function(json) {
	       			 console.log(json);
	            		if(json.body.a == "0"){
	            			malert('审核成功！','top','success');
	            		}else{
	            			malert('审核失败！','top','defeadted');
	            		}
	           })
	           ztable.getData();
            },
            //刷新
            refresh:function(){
            	ztable.param.shbz = '1';
            	ztable.param.zxsb = '';
            	ztable.getData();
            },
            //所有报告
            allbg:function(){
            	ztable.param.shbz = '';
            	ztable.getData();
            },
            //变化趋势
            qushi:function () {
            	if(ztable.isChecked == 0){
            		malert('请选择条目','top','defeadted');
            		return;
            	}else{
            		for (var j = 0; j < ztable.isChecked.length; j++) {
						if(ztable.isChecked[j]){
							wrapper.bg = ztable.jsonList[j];
						}
					}
            	}
            	if(wrapper.bg == "0"){
            		malert('请选择条目','top','defeadted');
            		return;
            	}
            	$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryHistory&param=" + JSON.stringify(wrapper.bg), function(json) {
        			console.log(json);
        			if(json.a=="0"){
        				if(json.d){
        					wapse.qushidate = json.d.date;
            				wapse.qushiList = json.d.line;
        				}else{
        					malert('无数据','top','success');
        				}
        				console.log(wapse.qushiList);
        				console.log(wapse.qushidate);
        			}else{
        				malert('指标趋势查询失败！','top','defeadted');
        				return;
        			}
        		})
                wapse.isFold = true;
                wapse.sqShow=false;
                // change('.tab-a','.tab-box');
                // change('.tab-gba','.tab-bg-box');
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                this.chart();
            },
            //报告发放
            ffbg:function(){
            	alert(1);
            	if(ztable.isChecked == 0){
            		malert('请选择发放报告记录','top','defeadted');
            		return;
            	}else{
            		for (var j = 0; j < ztable.isChecked.length; j++) {
						if(ztable.isChecked[j]){
							//报告发放标志
							ztable.jsonList[j].ffbz = "1";
							wrapper.sendbg.push(ztable.jsonList[j]);
						}
					}
            	}
            	if(wrapper.sendbg.length == 0 ){
            		malert('请选择发放报告记录','top','defeadted');
            		return;
            	}else{
            		//发放报告
            		
            		var data = '{"list":' + JSON.stringify(wrapper.sendbg) + '}';
	   	       		 this.$http.post('/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=modifyBgRq',data).then(function(json) {
	   	       			 console.log(json.body);
	   	       			 if(json.body.a == 0){
	   	       				 malert('报告发放成功！','top','success');
	   	       				 wrapper.sendbg = [];
	   	       				 ztable.isChecked = [];
	   	       			 }else{
	   	       				 malert('报告发放失败！','top','defeadted');
	   	       			 }
	   	       		 });
            		/*$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=modifyBgRq&jsonList=" + JSON.stringify(wrapper.sendbg), function(json) {
            			console.log(json);
            			if(json.a=="0"){
            				malert('报告发放成功！','top','success');
            			}else{
            				
            			}
            		})*/
            	}
            },
            //费用情况
            feiyong:function () {
            	if(ztable.isChecked == 0){
            		malert('请选择条目','top','defeadted');
            		return;
            	}else{
            		for (var j = 0; j < ztable.isChecked.length; j++) {
						if(ztable.isChecked[j]){
							wrapper.bg = ztable.jsonList[j];
							pop.bg = ztable.jsonList[j];
						}
					}
            	}
            	if(wrapper.bg == "0"){
            		malert('请选择条目','top','defeadted');
            		return;
            	}
            	
            	//费用详情
            	$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryFyDetail&param=" + JSON.stringify(wrapper.bg), function(json) {
             		console.log(json);
             		console.log(wrapper.bg);
             		if(json.a == 0){
             			if(json.d.list.length == 0){
             				wrapper.bg ='0';
             				malert("无数据",'top','success');
             				return;
             			}else{
             				pop.fyList = json.d.list;
             			}
             		}
             	});
            	
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.flag=true
                pop.dyShow=false;
            },
            chart:function () {
                var myChart = echarts.init(document.getElementById('main'));
                // 指定图表的配置项和数据
                var option = {
                    title: {


                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: '#ddd'
                            }
                        },
                        backgroundColor: 'rgba(255,255,255,1)',
                        padding: [5, 10],
                        textStyle: {
                            color: '#666',
                        },

                    },
                    // legend: {
                    //     // right: 20,
                    //     orient: 'vertical',
                    //     // data: ['结果']
                    // },
                    xAxis: {
                        type: 'category',
                        data: ['2014-04-06','2014-04-06','2014-04-06','2014-04-06','2014-04-06','2014-04-06'],
                        boundaryGap: false,
                        splitLine: {
                            interval: 'auto',
                            lineStyle: {
                                color: ['#1abc9c']
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#1abc9c'
                            }
                        },
                        axisLabel: {
                            // margin: 10,
                            textStyle: {
                                fontSize: 12,
                                color:'#757c83'
                            }

                        }
                    },
                    yAxis: {
                        type: 'value',
                        color:'#666',
                        splitLine: {
                            lineStyle: {
                                color: ['#e6eaee']
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#ddd'
                            }


                        },
                        axisLabel: {
                            margin: 10,
                            textStyle: {
                                fontSize: 12,
                                color:'#757c83'
                            }
                        }
                    },
                    series: [{
                        name: '结果(mmol/L)',
                        type: 'line',
//      smooth: true,
//      showSymbol: false,
//      symbol: 'circle',
//      symbolSize: 6,
                        data: ['0', '2', '5', '1', '4', '3', '9', '6', '7', '1', '0'],
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(199, 237, 250,0.5)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(199, 237, 250,0.2)'
                                }], false)
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#1abc9'
                            }
                        },
                        lineStyle: {
                            normal: {
                                width:2
                            }
                        }
                    }]
                };
                // 使用刚指定的配置项和数据显示图表。
                myChart.setOption(option);
            }
        },
        watch:{
        	'apply':function(){
        		//JSON.stringify(wapse.listOne)
        		console.log(wrapper.apply);
        		var times = wrapper.apply.split(" - ");
        		console.log(times);
        		ztable.param.beginrq = times[0];
        		ztable.param.endrq = times[1];
        		console.log(JSON.stringify(ztable.param));
        		ztable.getData();
        	},
        	'zxsb':function(){
        		ztable.param.zxsb = wrapper.zxsb;
        		console.log(JSON.stringify(ztable.param));
        		ztable.getData();
        	},
        	'brxm':function(){
        		ztable.param.brxm = wrapper.brxm;
        		console.log(JSON.stringify(ztable.param));
        		ztable.getData();
        	},
        	'bah':function(){
        		ztable.param.bah = wrapper.bah;
        		console.log(JSON.stringify(ztable.param));
        		ztable.getData();
        	}
        	
        }
        
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            flag:false,
            jsShow:false,
            dyShow:false,
            title:'',
            centent:'',
            fyList:'',
            bg:''
        },
        methods:{
            //确定删除
            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                // event.currentTarget.remove();
            }
        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            sqShow:false,
            title:'',
            centent:'',
            num:0,
            qushiList:'',
            qushidate:'',
            childNum:0,
            isFold: false,
            listOne:'',
            zbList:[],
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods:{
            childSild:function (i) {
                this.childNum=i
            },
            side:function(i){
                this.num=i
            },
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                //成功回调提示
                // malert('111','top','defeadted');
            },
            AddClose:function () {
                this.num=0;
                this.childNum=0;
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            // //确定
            confirms:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('222','top','success');
            }

        }
    });
    var ztable=new Vue({
        el:'.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShow:false,
            title:'',
            centent:'',
            jsonList:[],
            isChecked:[],
            totalPage:'',
            param:{
            	parm:'',
            	page: 1,
    			rows: 10,
    			shbz:'1',
    			beginrq:'',
    			endrq:'',
    			zxsb:'',
    			brxm:'',
    			bah:''
            },
            checkList:[],
            total:0,
        },
        //2018/07/19添加动态监听表格浮动区域
        mounted:function () {
          changeWin()
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods:{
            dblclick:function (index) {
            	console.log(ztable.jsonList[index]);
            	wapse.listOne = ztable.jsonList[index];
            	console.log(wapse.listOne);
            	
            	$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryBgmx&param=" + JSON.stringify(wapse.listOne), function(json) {
             		console.log("指标list:");
             		console.log(json);
             		if(json.a == "0"){
             			wapse.zbList = json.d.list;
             			console.log(wapse.zbList);
             		}
             	});
                // 展示注册记录
                wapse.isFold = true;
                wapse.sqShow=true;
                // change('.tab-a','.tab-box');
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                this.echart();
            },
            getData:function(){
            	ztable.checkList = [];
            	$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryBg&param=" + JSON.stringify(ztable.param), function(json) {
            		ztable.isChecked = [];
            		console.log(json);
            		if(json.a == '0'){
            			if(json.d){
            				ztable.jsonList = json.d.list;
            				ztable.total = json.d.total;
        					ztable.totlePage = Math.ceil(json.d.total / ztable.param.rows);
            			}
            		}
					console.log(ztable.totlePage);
					// wrapper.$nextTick(function () {
	                 //        $(".zui-table-view").uitable();
					//  });
            		/*if(json.a == 0)
						contextInfo[listName] = json.d.list;
					else
						malert(types + "查询失败");*/
				});
            },
            cancelC:function(index){
            	ztable.checkList.push(ztable.jsonList[index]);
            	var data = '{"list":' + JSON.stringify(ztable.checkList) + '}';
	       		this.$http.post('/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=cancelCheck',data).then(function(json) {
	       			 console.log(json);
	            		if(json.body.a == "0"){
	            			malert('取消审核成功！','top','success');
	            			ztable.getData();
	            		}else{
	            			malert('取消审核失败！','top','defeadted');
	            		}
	           })
            },
            echart:function () {
                var myChart = echarts.init(document.getElementById('message'));
                // 指定图表的配置项和数据
                var option = {
                    title: {


                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: '#ddd'
                            }
                        },
                        backgroundColor: 'rgba(255,255,255,1)',
                        padding: [5, 10],
                        textStyle: {
                            color: '#666',
                        },

                    },
                    // legend: {
                    //     // right: 20,
                    //     orient: 'vertical',
                    //     // data: ['结果']
                    // },
                    xAxis: {
                        type: 'category',
                        data: ['2014-04-06','2014-04-06','2014-04-06','2014-04-06','2014-04-06','2014-04-06'],
                        boundaryGap: false,
                        splitLine: {
                            interval: 'auto',
                            lineStyle: {
                                color: ['#1abc9c']
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#1abc9c'
                            }
                        },
                        axisLabel: {
                            // margin: 10,
                            textStyle: {
                                fontSize: 12,
                                color:'#757c83'
                            }

                        }
                    },
                    yAxis: {
                        type: 'value',
                        color:'#666',
                        splitLine: {
                            lineStyle: {
                                color: ['#e6eaee']
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#ddd'
                            }


                        },
                        axisLabel: {
                            margin: 10,
                            textStyle: {
                                fontSize: 12,
                                color:'#757c83'
                            }
                        }
                    },
                    series: [{
                        name: '结果(mmol/L)',
                        type: 'line',
//      smooth: true,
//      showSymbol: false,
//      symbol: 'circle',
//      symbolSize: 6,
                        data: ['0', '2', '5', '1', '4', '3', '9', '6', '7', '1', '0'],
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(199, 237, 250,0.5)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(199, 237, 250,0.2)'
                                }], false)
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#1abc9'
                            }
                        },
                        lineStyle: {
                            normal: {
                                width:2
                            }
                        }
                    }]
                };
                // 使用刚指定的配置项和数据显示图表。
                myChart.setOption(option);
            }
        }
    });
    //表格图形转换tab-gba


    //变化趋势


    laydate.render({
        elem: '.todate',
        eventElem: '.zui-date i.datenox',
        trigger: 'click',
        theme: '#1ab394',
        range:true,
        done:function (value,data) {
        	 wrapper.apply = value
        }
    });

    var filter=new  Vue({
        el:'.filter',
        data:{
            isShow:false
        },
        methods:{
            baocun:function () {
                this.isShow=false;
                malert('保存成功','top','success');
            },
            guanbi:function () {
                this.isShow=false;
                malert('取消保存','top','defeadted ');
            }
        },
    })
