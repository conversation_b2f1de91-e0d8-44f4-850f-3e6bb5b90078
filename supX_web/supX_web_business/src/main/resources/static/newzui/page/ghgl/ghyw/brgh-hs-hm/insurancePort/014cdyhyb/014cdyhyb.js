var cd_014 = new Vue({
	el: '.cd_014',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	data: {
		gzyhybInit:false,
		bxlbbm: null,
		bxurl: null,
		brzt_tran:{
			'1':'在院',
			'2':'未在院'
		},
		zdxxJson:{
		},
		grxxJson:{
			aka130 : "0201",
		},
		userInfo:{},
		ifclick:true,
		cdydbz_tran: {
			'1':'本地',
			'2':'省内异地 ',
			'3':'省外异地'
		},
		cd_aka130_tran:{
			'0101' : '药店购药',
			'0102' : '城居药店支付',
			'0201' : '普通门诊',
			'0202' : '特殊疾病门诊',
			'0203' : '城居普通门诊',
			'0204' : '城居门诊特殊病',
			'0205' : '城职门诊统筹',
			'0206' : '城乡门诊统筹',
			'0207' : '酪氨酸城职',
			'0208' : '酪氨酸城乡',
			'0209' : '大学生门诊',
			'0301' : '普通住院',
			'0302' : '家庭病床',
			'0304' : '市外转诊住院(异地急救参照市外转诊)',
			'0305' : '统筹区内转院',
			'0306' : '外伤住院',
			'0307' : '异地抢救住院(手工报销使用)',
			'0308' : '本市非定点抢救住院(手工报销使用)',
			'0309' : '城居普通住院',
			'0310' : '城居外伤住院',
			'0311' : '城居市外转诊',
			'0401' : '机关事业单位生育报销',
			'0501' : '工伤门诊',
			'0502' : '工伤住院',
			'0504' : '工伤康复治疗住院',
			'0602' : '城居生育分娩住院',
			'0603' : '城居产前检查',
			'0604' : '城职生育住院',
			'13' : '异地门特（就医地）',
		},
		gsdataset_tran:[],
	},
	mounted: function () {
		this.init();
		this.getbxlb();
	},
	methods: {
		init: function () {
			this.postFormAjax("http://localhost:10014/init", {}, function (json) {
				if (json.aint_appcode > 0) {
					this.gzyhybInit = true;
					malert("初始化成功!","right","success");
				} else {
					malert("医保控件初始化失败！请从新打开页面!","right","defeadted");
				}
			});
		},
		getbxlb: function () {
			var param = {bxjk: "B07"};
			$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
				+ JSON.stringify(param), function (json) {
				if (json.a == 0) {
					if (json.d.list.length > 0) {
						cd_014.bxlbbm = json.d.list[0].bxlbbm;
						cd_014.bxurl = json.d.list[0].url;
					}
				} else {
					malert("保险类别查询失败!" + json.c,'right','defeadted')
				}
			});
		},

		commonResultChange:function(val){
			var type = val[2][1];
			switch (type) {
				case "yke109":
					Vue.set(this.grxxJson,type,val[0]);
					Vue.set(this.grxxJson,"alc022",val[4]);
					Vue.set(this.grxxJson,"aka130",cd_014.listGetName(cd_014.gsdataset_tran, val[0], 'yke109', 'aka130'));
					break;
				case "bkc014":
					Vue.set(this.grxxJson,type,val[0]);
					Vue.set(this.grxxJson,"bkc117",val[4]);
					break;
			}
		},


		//读卡
		load:function(){
			if(!this.ifclick){
				malert("请勿重复点击！","right","defeadted");
				return;
			}
			this.ifclick = false;
			if(this.gzyhybInit){
				var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>' +
							'<control>' +
								'<edition>3</edition>' +
								'<system>1</system>' +
							'</control>';

				var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><data></data>';

				$.post("http://localhost:10014/call", {
					jybh:"03",
					jykz_xml:jykz,
					jysr_xml:jysr,
				}, function (json) {
					cd_014.ifclick = true;
					if (json.aint_appcode > 0) {
						Vue.set(cd_014,'grxxJson',JSON.parse(json.astr_jysc_xml));
					} else {
						malert(json.astr_appmasg,"right","defeadted");
					}
				});
			}else{
				cd_014.ifclick = true;
				malert("医保控件未初始化,请重新打开页面！",'right','defeadted');
			}
		},

		//引入
		enter:function(){
			if (Object.keys(cd_014.grxxJson).length === 0) {
				malert("请先读卡","right","defeadted");
				return;
			}
			//个人信息
				contextInfo.type='06';
				contextInfo.json=Object.assign(contextInfo.json,cd_014.grxxJson);
				contextInfo.json.brxm=cd_014.grxxJson.aac003;
				contextInfo.json.brxb=cd_014.grxxJson.aac004;
				contextInfo.json.brnl=cd_014.grxxJson.akc023;
				contextInfo.json.nldw='1';
				contextInfo.json.sfzjhm=cd_014.grxxJson.aac002;
				contextInfo.json.gzdw=cd_014.grxxJson.aab004;
				contextInfo.json.fbbm='37';
				contextInfo.json.lxgh='1';
				contextInfo.json.bxlbbm='02';
			   contextInfo.bxShow = false;
			   malert("引入成功！","right","success");
			   contextInfo.setCsrq()
			  contextInfo.setAge()
		},
	}
});

$(document).click(function () {
	if (this.className != 'selectGroup') {
		$(".selectGroup").hide();
	}
});
