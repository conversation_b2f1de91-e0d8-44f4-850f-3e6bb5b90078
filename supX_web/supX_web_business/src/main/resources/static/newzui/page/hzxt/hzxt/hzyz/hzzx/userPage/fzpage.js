var fzpage = new Vue({
    el: '.fzpage',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ischeck: false,
        num:0,
        isactiveClass: null,
        isactiveheaderClass: null,
        ismenu: true,
        ischecked: [
            {
                gz: [],
                zdyz: [],
                hlgz: [],
                bqby: [],
                ischeckedAll: '',
            }
        ],
        contextmenuone: [
            {name: '左方新增阶段', icon: '/newzui/pub/image/zj.png', type: 'left'},
            {name: '右方新增阶段', icon: '/newzui/pub/image/zj.png', type: 'right'},
            {name: '编辑', icon: '/newzui/pub/image/bj.png', type: 'edit'},
            {name: '删除', icon: '/newzui/pub/image/sc.png', type: 'delete'},
        ],
        contextmenutwo: [
            {name: '上方新增项目', icon: '/newzui/pub/image/zj.png', type: 'top'},
            {name: '下方新增项目', icon: '/newzui/pub/image/zj.png', type: 'bottom'},
            {name: '编辑', icon: '/newzui/pub/image/bj.png', type: 'editChild'},
            {name: '删除整行', icon: '/newzui/pub/image/sc.png', type: 'deleteChild'},
        ],
        optionList: [
            {
                name: '住院第一天',
                csactive: false,
                gz: [
                    {type: false, title: '书写病历', csactive: false},
                    {type: true, title: '书写病历122', csactive: false},
                    {type: true, title: '书写病历1', csactive: false},
                    {type: true, title: '住院第一天', csactive: false},
                    {type: true, title: '住院第一dsdsd天', csactive: false},
                ],
                zdyz: [
                    {type: true, title: '术后长期', csactive: false},
                    {type: true, title: '术后长期', csactive: false}
                ],
                hlgz: [
                    {type: false, title: '普外科护理常规', csactive: false},
                ],
                bqby: [
                    {type: true, title: '一级护理', csactive: false},
                    {type: true, title: '一级qqqq护理', csactive: false},
                    {type: true, title: '一级qqqq一级护一级护理住院第二天理住院第二天护理', csactive: false},
                ],
            },
            {
                name: '住院第二天',
                gz: [
                    {type: true, title: '书写病历住院第二天', csactive: false},
                    {type: true, title: '书写病历住院第二天实打实打算书写病历住院第二天实打实打算', csactive: false},
                ],
                zdyz: [
                    {type: true, title: '术后长期住院第二天', csactive: false},
                ],
                hlgz: [
                    {type: false, title: '普外科护理常规住院第二天', csactive: false},
                ],
                bqby: [
                    {type: true, title: '一级护理住院第一级护理住院第二天一级护理住院第二天二天', csactive: false},
                ],
            },

        ],
    },
    mounted: function () {

    },
    methods: {
        closeAndOpenPage:function () {
            this.topClosePage('page/zyysz/zyysz/hzgl/hzzx/userPage/fzpage.html','page/zyysz/zyysz/hzgl/hzzx/hzzx.html');
            var str=JSON.parse( sessionStorage.getItem('userPage'));
               str[1]=4;
               str[0]='userPage/lclj';
               str[3]=new Date();
            sessionStorage.setItem('userPage',JSON.stringify(str))
        },
        menuActive: function (item) {
            if (item[0] == true) {
                $(item[4].currentTarget).parents('.jieduan-box').find('.item-header').removeClass('active');
                $(item[4].currentTarget).parents('.jieduan-box').find('.item-border').removeClass('active');
                $(item[4].currentTarget).addClass('active')
                // if(this.isactiveheaderClass!=null){
                //     this.optionList[this.isactiveheaderClass[2]].csactive=!this.optionList[this.isactiveheaderClass[2]].csactive;
                //     this.isactiveheaderClass=null;
                // }
                //  this.optionList[item[2]].csactive=!this.optionList[item[2]].csactive;
            } else if (item[0] == false) {
                $(item[4].currentTarget).parents('.jieduan-box').find('.item-header').removeClass('active');
                $(item[4].currentTarget).parents('.jieduan-box').find('.item-border').removeClass('active');
                $(item[4].currentTarget).addClass('active')
            }

        },
        docheck: function () {
            this.ischeck = !this.ischeck;
            this.num=this.num+1;
            this.optionList.forEach(function (br) {
                Vue.set(br, 'isCheckAll', fzpage.ischeck);
                br.gz.forEach(function (yz) {
                    Vue.set(yz, 'isChecked', fzpage.ischeck)
                });
                br.zdyz.forEach(function (yz) {
                    Vue.set(yz, 'isChecked', fzpage.ischeck)
                });
                br.hlgz.forEach(function (yz) {
                    Vue.set(yz, 'isChecked', fzpage.ischeck)
                });
                br.bqby.forEach(function (yz) {
                    Vue.set(yz, 'isChecked', fzpage.ischeck)
                });
            });
            this.optionList.forEach(function (yz) {
                Vue.set(yz, 'num', fzpage.num)
            });
            this.$forceUpdate();
        },
        checkSelectSh:function (xmIndex, dayIndex, type, childText) {
            this.num=this.num+1;
            if (type) {
                var isCheckAll = this.optionList[xmIndex].isCheckAll ? false : true;
                yzshInfo = this.optionList[xmIndex];
                this.ischeck=isCheckAll;
                // this.optionList[parentIndex].isCheckAll = isCheckAll;
                Vue.set(this.optionList[xmIndex], 'isCheckAll', isCheckAll);
                // this.ischeck = isCheckAll;
                for (var i = 0; i < yzshInfo.gz.length; i++) {
                    this.optionList[xmIndex].gz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.zdyz.length; i++) {
                    this.optionList[xmIndex].zdyz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.hlgz.length; i++) {
                    this.optionList[xmIndex].hlgz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.bqby.length; i++) {
                    this.optionList[xmIndex].bqby[i].isChecked = isCheckAll;
                }
            } else {
                var yzStatus = !this.optionList[dayIndex][childText][xmIndex].isChecked;
                // this.optionList[dayIndex][childText][xmIndex].isChecked = yzStatus;
                this.$set(this.optionList[dayIndex][childText][xmIndex],'isChecked',yzStatus);
                if (yzStatus) {
                    var yzIsOverCk = true;
                    for (var t = 0; t < this.optionList[dayIndex].gz.length; t++) {
                        if (!this.optionList[dayIndex]['gz'][t].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var u = 0; u < this.optionList[dayIndex].zdyz.length; u++) {
                        if (!this.optionList[dayIndex]['zdyz'][u].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var y = 0; y < this.optionList[dayIndex].hlgz.length; y++) {
                        if (!this.optionList[dayIndex]['hlgz'][y].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var r = 0; r < this.optionList[dayIndex].bqby.length; r++) {
                        if (!this.optionList[dayIndex]['bqby'][r].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    // this.optionList[dayIndex].isCheckAll = yzIsOverCk;
                    this.$set(this.optionList[dayIndex],'isCheckAll',yzIsOverCk);
                    this.ischeck = yzIsOverCk;
                } else {
                    this.optionList[dayIndex].isCheckAll = false;
                    this.ischeck = false;
                }
            }
            this.optionList.forEach(function (yz) {
                Vue.set(yz, 'num', fzpage.num)
            });
            this.$forceUpdate();
        },
        checked: function (hsindex, type, data) {
            var that = this;
            this.isckShow = false;
            var yzIsOverCk = true;
            if (type == 'all') {
                this.ischecked.ischeckedAll = !this.ischecked.ischeckedAll;
                for (var index = 0; index < data.gz; index++) {
                    that.ischecked.gz[index] = this.ischecked.ischeckedAll;
                }
                for (var index = 0; index < data.zdyz; index++) {
                    that.ischecked.zdyz[index] = this.ischecked.ischeckedAll;
                }
                for (var index = 0; index < data.hlgz; index++) {
                    that.ischecked.hlgz[index] = this.ischecked.ischeckedAll;
                }
                for (var index = 0; index < data.bqby; index++) {
                    that.ischecked.bqby[index] = this.ischecked.ischeckedAll;
                }
            } else {
                Vue.set(this.ischecked[type], hsindex, !this.ischecked[type][hsindex]);
                for (var index = 0; index < data.gz; index++) {
                    if (!that.ischecked.gz[index]) {
                        yzIsOverCk = false
                    }
                }
                for (var j = 0; j < data.zdyz.j; index++) {
                    if (!that.ischecked.zdyz[j]) {
                        yzIsOverCk = false
                    }
                }
                for (var t = 0; t < data.hlgz; t++) {
                    if (!that.ischecked.hlgz[t]) {
                        yzIsOverCk = false
                    }
                }
                for (var u = 0; u < data.bqby; u++) {
                    if (!that.ischecked.bqby[u]) {
                        yzIsOverCk = false
                    }
                }
                console.log(that.ischecked);
                this.ischecked.ischeckedAll = yzIsOverCk;
                this.ischeck=yzIsOverCk
            }
            this.$forceUpdate();
            // setTimeout(function () {
            this.$nextTick(function () {
                panel.isckShow = true
            })
            // },0)
        },
        menu: function (item) {
            this.ismenu = false;
            var str = {
                date: '住院第4天',
                gz: [
                    {type: true, title: ''}
                ],
                zdyz: [
                    {type: true, title: ''}
                ],
                hlgz: [
                    {type: false, title: ''}
                ],
                bqby: [
                    {type: true, title: ''}
                ],
            };
            switch (item[0].type) {
                case 'left':
                    this.optionList.splice(item[3] + 1, 0, this.optionList[item[3]]);
                    break;
                case 'right':
                    this.optionList.splice(item[3] == 0 ? 0 : item[3] - 1, 0, this.optionList[item[3]]);
                    break;
                case 'delete':
                    this.optionList.splice(item[3], 1, this.optionList[item[3]]);
                    break;
                case 'edit':
                    this.optionList.splice(item[3], 1, this.optionList[item[3]]);
                    break;
                case 'top':
                    this.optionList[item[3]][item[4]].splice(item[2] == 0 ? 0 : item[2], 0, this.optionList[item[3]][item[4]][item[2]]);
                    break;
                case 'bottom':
                    this.optionList[item[3]][item[4]].splice(item[2] + 1, 0, this.optionList[item[3]][item[4]][item[2]]);
                    break;
                case 'editChild':
                    this.optionList[item[3]][item[4]].splice(this.optionList[item[3]][item[4]][item[2]], 1, this.optionList[item[3]][item[4]]);
                    break;
                case 'deleteChild':
                    this.optionList[item[3]][item[4]].splice(this.optionList[item[3]][item[4]][item[2]], 1, this.optionList[item[3]][item[4]][item[2]]);
                    break;
            }
            this.$nextTick(function () {
                this.ismenu = true
            })
        },
        qtflj: function () {
            brzcList.index = 0
        },
        doZjjl: function () {

        }
    },
});
var brzcList = new Vue({
    el: '#brzcList',
    data: {
        index: 1,
        treeData: [
            {
                id: "001",
                name: "混合痔临床路径",
                zljList: [
                    {id: "001001", name: "路径表单分路径"},
                    {id: "001002", name: "路径表单分路径2"},
                    {id: "001003", name: "路径表单分路径3"}
                ]
            },
            {
                id: "002",
                name: "药库管理",
                zljList: [
                    {id: "002001", name: "库房业务"},
                    {id: "002002", name: "库房业务1"},
                    {id: "002003", name: "库房业务2"},
                ]
            },
            {
                id: "003",
                name: "药房管理",
                zljList: [
                    {id: "003001", name: "药房业务"},
                    {id: "003002", name: "药房业务2"},
                    {id: "003003", name: "药房业务3"}
                ]
            },
            {
                id: "004",
                name: "门诊挂号",
                zljList: [{
                    id: "004001",
                    name: "医院业务"
                }]
            },
            {
                id: "005",
                name: "门诊收费",
                zljList: [
                    {id: "005001", name: "收费结算"},
                    {id: "005002", name: "收费结算2"},
                    {id: "005003", name: "收费结算3"}
                ]
            },
            {
                id: "006",
                name: "门诊医生站",
                zljList: [
                    {id: "006001", name: "诊疗管理"},
                    {id: "006002", name: "诊疗管理2"},
                    {id: "006003", name: "诊疗管理3"},
                ]
            },
            {
                id: "007",
                name: "住院管理",
                zljList: [
                    {id: "007001", name: "入出院管理"},
                    {id: "007002", name: "入出院管理2"},
                    {id: "007003", name: "入出院管理3"},
                ]
            }
        ]
    },
    methods: {

        checkedVal: function (val) {
        },
        closes: function () {
            this.index = 1
        },
        popShow: function () {
            pop.isShow = true
        }
    },
    created: function () {

    }
});
var pop = new Vue({
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    el: '.pop',
    data: {
        isShow: false,
        popContent: {},
    },
    methods: {
        Wf_save: function () {

        }
    },
});
laydate.render({
    elem: '#time'
    , trigger: 'click'
    , theme: '#1ab394'
    , done: function (value, data) {
        pop.popContent.time = value;
    }
});