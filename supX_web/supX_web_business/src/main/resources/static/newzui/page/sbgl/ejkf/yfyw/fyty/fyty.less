@import "../../../../css/baseColor";
.icon-fy:before{
  left:inherit;
}
.icon-dy:before{
  left:17px !important;
}
.icon-ty:before{
left:77px !important;
}
.slgl-by{
  width: 100%;
  height: 40px;
  display: flex;
  line-height: 40px;
  background:@colorRgbf2a;
  justify-content: space-between;
  i{
    width: 20%;
    display: block;
    text-align: center;
    color: @color75;
    &:nth-child(5){
      padding-right: 15px;
    }
    em{
      color: @color35;
      padding-left: 5px;
      float: left;
    }
  }
}
.fyxm-tab div{
  border: none !important;
  height: auto;
}
.fyxm-tab div:first-child{
  border: none !important;
}
.actives{
  border-bottom: 2px solid @color1a;
  color: @color1a;
}
.fyty-fr{
  position: absolute;
  right: 10px;
  top:103px;
  width: 120px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0;
  margin-right: -45px;
  i{
    float: left;
  }
.fyty-select{
  width:50px;
  border: none !important;
  text-align: right;
  -webkit-appearance: none;
  position: relative;
  height: 26px;
  line-height: 26px;

}

  .fyty-dsj{
    width: 10px;
    position: absolute;
    top:29px;
    right: 0;
    height: 10px;
    background: url("../../../../css/@{image}<EMAIL>") center right no-repeat;
    transform: rotate(270deg);
  }
  .iconClass{
    right:28px;
    top: -5px;
  }
}

.cfhj-top{
  width: 100%;
  height: 36px;
  background:@colored;
  line-height: 36px;
  li{
    width: 100%;
    display: flex;
    justify-content: center;
    i{
      width: calc(~"(100% - 250px)/7");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
      &:nth-child(4){
        width: 200px;
      }
    }
  }
}

.cfhj-content{
  width: 100.7%;
  overflow: auto;
  max-height:500px;
  li{
    width: 100%;
    display: flex;
    border:1px solid @coloree;
    border-top: none;
    justify-content: center;
    i{
      width: calc(~"(100% - 250px)/7");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
      &:nth-child(4){
        width: 200px;
      }
    }
    &:hover{
      background:rgba(26,188,156,0.08);
    }
  }
}
.mx-top{
  li{
    i{
      width: calc(~"(100% - 250px)/7");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
      &:nth-child(2){
        width: 200px;
      }
    }
  }
}
.title-width{
  width: 70px !important;
}
.title-width2{
  //width:200px !important;
  white-space: nowrap;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
}
.wh182 {
  padding: 0px !important;
  input[type='text']{
    border: none !important;
    height:24px;
  }
  .zui-select-group{
    padding: 0;
  }

}

.zui-form .zui-form-label{
  left: 5px !important;
}
.h2title{
  display: none;
}
#zhcx,#zhcx1{
  padding: 0 3px 0 0;
}