<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>交款报表</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <!--<link href="../css/page/cwjf.min.css" rel="stylesheet" />-->

</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="skindefault">
    <div class="" style="padding: 15px">
        <ul class="zui-tabs tabs-jkbb"><li :class="{active:indexs==0}" @click="showClick(0)"><a href="javascript:;">费用项目</a></li><li  @click="showClick(1)" :class="{active:indexs==1}"><a href="javascript:;">病人费别</a></li></ul>
        <div class="panel">
            <!--费用项目-->
            <input type="hidden" id="jkrq" />
            <div id="table_fyxm" class="zui-table-view" v-show="showList==0" style="border:none;">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                            <tr>
                                <th fixed="left" field="ids" width="80px" style="text-align:center;">
                                    <div class="zui-table-cell"><span>序号</span></div>
                                </th>
                                <th fixed="left" style="text-align:center;">
                                    <div class="zui-table-cell"><input type="checkbox" :checked="isCheckedall[0]" @click="checkAll($event,'fylbList',0)"  id="check0_0" class="zui-checkbox"/><label for="check0_0"></label></div>
                                </th>
                                <th field="fyname" width="120px" :sort="true">
                                    <div class="zui-table-cell"><span>费用名称</span></div>
                                </th>
                                <th field="sfje" width="120px" :sort="true">
                                    <div class="zui-table-cell"><span>收费金额</span></div>
                                </th>
                                <th field="sfbs" width="120px" :sort="true">
                                    <div class="zui-table-cell"><span>收费笔数</span></div>
                                </th>
                                <th field="tfje" width="120px">
                                    <div class="zui-table-cell"><span>退费金额</span></div>
                                </th>
                                <th field="tfbs" width="120px">
                                    <div class="zui-table-cell"><span>退费笔数</span></div>
                                </th>
                                <th field="yhje" width="100px">
                                    <div class="zui-table-cell"><span>优惠金额</span></div>
                                </th>
                                <th field="hjbs" width="100px">
                                    <div class="zui-table-cell"><span>合计笔数</span></div>
                                </th>
                                <th field="hjje" width="100px">
                                    <div class="zui-table-cell"><span>合计金额</span></div>
                                </th>
                                <!--<th fixed="right" width="100px" style="text-align:center;">-->
                                    <!--<div class="zui-table-cell"></div>-->
                                <!--</th>-->
                            </tr>
                            <!--<tr v-for="(item,$index) in 20" data-toggle="sideform" data-target="#brzcLis">-->
                            <!--<th fixed="left" style="text-align:center;">-->
                            <!--<div class="zui-table-cell"><input type="checkbox" @click="checkOne($event,$index+1)"  :checked="isChecked[$index+1]" :id='"check0_"+$index+1' class="zui-checkbox"/><label :for='"check0_"+$index+1'></label></div>-->
                            <!--</th>-->
                            <!--<td ><div class="zui-table-cell" v-text="item"></div></td>-->
                            <!--</tr>-->
                            <tr data-toggle="sideform" data-target="#brzcLis" v-for="(item,$index) in fylbList" @click="showDetail($index)"
                                :class="{'tableTrSelect':isCheck == $index}">
                                <td><div class="zui-table-cell">{{$index+1}}</div></td>
                                <!--<td v-text="$index+1" style="text-align: center"><div class="zui-table-cell"></div></td>-->
                                <th fixed="left" style="text-align:center;">
                                    <div class="zui-table-cell"><input type="checkbox" @click="checkOne($event,$index+1)"  :checked="isChecked[$index]" :id='"check0_"+$index+1' class="zui-checkbox"/><label :for='"check0_"+$index+1'></label></div>
                                </th>
                                <td ><div class="zui-table-cell" v-text="item.fylbmc"></div></td>
                                <td ><div class="zui-table-cell" v-text="fDec(item.sfyje,2)"></div></td>
                                <td ><div class="zui-table-cell" v-text="item.snumber"></div></td>
                                <td ><div class="zui-table-cell" v-text="fDec(item.tfyje,2)"></div></td>
                                <td ><div class="zui-table-cell" v-text="item.tnumber"></div></td>
                                <td ><div class="zui-table-cell" v-text="fDec(item.yhje)"></div></td>
                                <td ><div class="zui-table-cell" v-text="item.allNumber"></div></td>
                                <td ><div class="zui-table-cell" v-text="fDec(item.sfyje+item.tfyje,2)"></div></td>
                                <!--<td>-->
                                    <!--<div class="zui-table-cell">-->
                                        <!--<div class="cell_dropdown">-->
                                            <!--<button class="zui-btn btn-blue btn-xs">操作<i class="ion-android-arrow-dropdown"></i></button>-->
                                            <!--<ul>-->
                                                <!--<li><a href="javascript:;"><i class="fa-search success"></i>操作1</a></li>-->
                                                <!--<li><a href="javascript:;"><i class="fa-minus-square-o blue"></i>操作2</a></li>-->
                                                <!--<li><a href="javascript:;"><i class="fa-trash danger"></i>操作3</a></li>-->
                                            <!--</ul>-->
                                        <!--</div>-->
                                    <!--</div>-->
                                <!--</td>-->
                            </tr>
                        </thead>
                    </table>
                </div>

<!--                <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>-->
            </div>

            <!--病人费别-->
            <div id="table_brfb" class="zui-table-view" v-show="showList==1" style="border:none;">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                            <tr>
                                <th fixed="left" width="80px" field="ids" style="text-align:center;">
                                    <div class="zui-table-cell"><span>序号</span></div>
                                </th>
                                <th fixed="left" style="text-align:center;">
                                    <div class="zui-table-cell"><input type="checkbox" :checked="isCheckedall[1]" @click="checkAll($event,'brfbList',1)"  id="check0_1" class="zui-checkbox"/><label for="check0_1"></label></div>
                                </th>
                                <th field="fyname" width="120px" :sort="true">
                                    <div class="zui-table-cell"><span>费用名称</span></div>
                                </th>
                                <th field="sfje" width="120px" sort="true">
                                    <div class="zui-table-cell"><span>收费金额</span></div>
                                </th>
                                <th field="sfbs" width="120px" :sort="true">
                                    <div class="zui-table-cell"><span>收费笔数</span></div>
                                </th>
                                <th field="tfje" width="120px">
                                    <div class="zui-table-cell"><span>退费金额</span></div>
                                </th>
                                <th field="tfbs" width="120px">
                                    <div class="zui-table-cell"><span>退费笔数</span></div>
                                </th>
                                <th field="yhje" width="100px">
                                    <div class="zui-table-cell"><span>优惠金额</span></div>
                                </th>
                                <th field="hjbs" width="100px">
                                    <div class="zui-table-cell"><span>合计笔数</span></div>
                                </th>
                                <th field="hjje" width="100px">
                                    <div class="zui-table-cell"><span>合计金额</span></div>
                                </th>
                                <th fixed="right"  width="100px"  style="text-align:center;">
                                    <div class="zui-table-cell"></div>
                                </th>
                            </tr>
                            <!--<tr v-for="(item,$index) in 20" data-toggle="sideform" data-target="#brzcLis">-->
                            <!--<th fixed="left" style="text-align:center;">-->
                            <!--<div class="zui-table-cell"><input type="checkbox" @click="checkOne($event,$index+1)"  :checked="isChecked[$index+1]" :id='"check0_"+$index+1' class="zui-checkbox"/><label :for='"check0_"+$index+1'></label></div>-->
                            <!--</th>-->
                            <!--<td ><div class="zui-table-cell" v-text="item"></div></td>-->
                            <!--</tr>-->
                            <tr data-toggle="sideform" data-target="#brzcLis" v-for="(item,$index) in brfbList" @click="showDetail($index)"
                                :class="{'tableTrSelect':isCheck == $index}">
                                <td><div class="zui-table-cell">{{$index+1}}</div></td>
                                <th fixed="left" style="text-align:center;">
                                    <div class="zui-table-cell"><input type="checkbox" @click="checkOne($event,$index+1)"  :checked="isChecked[$index]" :id='"check0_"+$index+1' class="zui-checkbox"/><label :for='"check0_"+$index+1'></label></div>
                                </th>
                                <td ><div class="zui-table-cell" v-text="item.fylbmc"></div></td>
                                <td ><div class="zui-table-cell" v-text="fDec(item.sfyje,2)"></div></td>
                                <td ><div class="zui-table-cell" v-text="item.snumber"></div></td>
                                <td ><div class="zui-table-cell" v-text="fDec(item.tfyje,2)"></div></td>
                                <td ><div class="zui-table-cell" v-text="item.tnumber"></div></td>
                                <td ><div class="zui-table-cell" v-text="fDec(item.yhje)"></div></td>
                                <td ><div class="zui-table-cell" v-text="item.allNumber"></div></td>
                                <td ><div class="zui-table-cell" v-text="fDec(item.sfyje+item.tfyje,2)"></div></td>
                                <!--<td>-->
                                    <!--<div class="zui-table-cell">-->
                                        <!--<div class="cell_dropdown">-->
                                            <!--<button class="zui-btn btn-blue btn-xs">操作<i class="ion-android-arrow-dropdown"></i></button>-->
                                            <!--<ul>-->
                                                <!--<li><a href="javascript:;"><i class="fa-search success"></i>操作1</a></li>-->
                                                <!--<li><a href="javascript:;"><i class="fa-minus-square-o blue"></i>操作2</a></li>-->
                                                <!--<li><a href="javascript:;"><i class="fa-trash danger"></i>操作3</a></li>-->
                                            <!--</ul>-->
                                        <!--</div>-->
                                    <!--</div>-->
                                <!--</td>-->
                            </tr>
                        </thead>
                    </table>
                </div>
<!--                <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>-->
            </div>
        </div>
    </div>
    <!--弹窗-->
    <div class="side-form ng-hide" style="width: 1000px;"  id="brzcLis" role="form">
        <div class="side_head">
            <div class="">
                <div class="col-x-10">
                    <div class="zui-input-inline zui-date wh150">
                        <i class="datenox fa-calendar" style="line-height: 18px"></i>
                        <input type="text" name="phone" class="zui-input todate" placeholder="请选择时间" />
                    </div>
                    <div class="zui-input-inline zui-date wh150">
                        <i class="datenox fa-calendar" style="line-height: 18px"></i>
                        <input type="text" name="phone" class="zui-input todatD" placeholder="结束时间" />
                    </div>
                    <button class="zui-btn btn-primary">查询</button>
                </div>
            </div>
            <div class="setwin">
                <a href="javascript:;" class="closex ti-close"></a>
            </div>
        </div>
        <div class="side_main" style=" overflow-y: initial;">
            <div class="zui-table-view" style="border:none;overflow-x: scroll">
                <div class="zui-table-header" style="overflow: initial">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th fixed="left" style="text-align:center;">
                                <!--@click="checkAll($event,'brfbList',1)"-->
                                <!--:checked="isCheckedall[1]"-->
                                <div class="zui-table-cell"><input type="checkbox"  @click="checkAll($event,'mxfyList',1)"   id="check0_2" class="zui-checkbox"/><label for="check0_2"></label></div>
                            </th>
                            <th field="id" width="140px" fixed="left" style="text-align:center;">
                                <div class="zui-table-cell"><span>挂号序号</span></div>
                            </th>
                            <th field="brname" width="100px">
                                <div class="zui-table-cell"><span>病人姓名</span></div>
                            </th>
                            <th field="brfb" width="120px">
                                <div class="zui-table-cell"><span>病人费别</span></div>
                            </th>
                            <th field="bxlb" width="80px">
                                <div class="zui-table-cell"><span>保险类别</span></div>
                            </th>
                            <th field="jxfy" width="100px">
                                <div class="zui-table-cell"><span>明细费用</span></div>
                            </th>
                            <th field="fysl" width="200px">
                                <div class="zui-table-cell"><span>费用数量</span></div>
                            </th>
                            <th field="fydj" width="130px">
                                <div class="zui-table-cell"><span>费用单价</span></div>
                            </th>
                            <th field="fyje" width="140px" :sort="true">
                                <div class="zui-table-cell"><span>费用金额</span></div>
                            </th>
                            <th field="yzlb" width="100px">
                                <div class="zui-table-cell"><span>医嘱类型</span></div>
                            </th>
                            <th field="yzhm" width="100px">
                                <div class="zui-table-cell"><span>医嘱号码</span></div>
                            </th>
                            <th field="fphm" width="100px">
                                <div class="zui-table-cell"><span>发票号码</span></div>
                            </th>
                            <th field="mzys" width="100px">
                                <div class="zui-table-cell"><span>门诊医生</span></div>
                            </th>
                            <th field="mzks" width="100px">
                                <div class="zui-table-cell"><span>门诊科室</span></div>
                            </th>
                            <th field="zxks" width="100px">
                                <div class="zui-table-cell"><span>执行科室</span></div>
                            </th>
                            <th field="yhbl" width="100px">
                                <div class="zui-table-cell"><span>优惠比例</span></div>
                            </th>
                            <th field="yhje" width="100px">
                                <div class="zui-table-cell"><span>优惠金额</span></div>
                            </th>
                            <th field="bzxm" width="100px">
                                <div class="zui-table-cell"><span>备注说明</span></div>
                            </th>
                            <th  fixed="right" width="100px"  style="text-align:center;">
                                <div class="zui-table-cell"></div>
                            </th>
                        </tr>
                        <tr v-for="(item, $index) in mxfyList">
                            <th fixed="left" style="text-align:center;">
                                <div class="zui-table-cell"><input type="checkbox" id="check0_3"  :id='"check0_"+$index+1'  :checked="MyisChecked[$index]"  @click="checkOne($event,$index+1)"   class="zui-checkbox"/><label   :for='"check0_"+$index+1'></label></div>
                            </th>
                            <!--<th class="tableNo" v-text="$index+1"></th>-->
                            <td ><div class="zui-table-cell" v-text="item.ryghxh"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.brxm"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.ryfbmc"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.rybxlbmc"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.mxfyxmmc"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.fysl"></div></td>
                            <td ><div class="zui-table-cell" v-text="fDec(item.fydj,2)"></div></td>
                            <td ><div class="zui-table-cell" v-text="fDec(item.fyje,2)"></div></td>
                            <td ><div class="zui-table-cell" v-text="yzlx_tran[item.yzlx]"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.yzhm"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.fphm"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.mzysxm"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.mzksmc"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.zxksmc"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.yhbl"></div></td>
                            <td ><div class="zui-table-cell" v-text="fDec(item.yhje,2)"></div></td>
                            <td ><div class="zui-table-cell" v-text="item.bzsm"></div></td>
                            <!--<td>-->
                                <!--<div class="zui-table-cell">-->
                                    <!--<div class="cell_dropdown">-->
                                        <!--<button class="zui-btn btn-blue btn-xs">操作<i class="ion-android-arrow-dropdown"></i></button>-->
                                        <!--<ul>-->
                                            <!--<li><a href="javascript:;"><i class="fa-search success"></i>查看明细</a></li>-->
                                            <!--<li><a href="javascript:;"><i class="fa-minus-square-o blue"></i>取消交款</a></li>-->
                                            <!--<li><a href="javascript:;"><i class="fa-trash danger"></i>作废交款</a></li>-->
                                        <!--</ul>-->
                                    <!--</div>-->
                                <!--</div>-->
                            <!--</td>-->
                        </tr>
                        </thead>
                    </table>
                </div>
<!--                <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>-->
            </div>
        </div>
    </div>

</div>
<script src="/newzui/js/page/jhth.js"></script>
<script src="jkbb.js"></script>
</body>
</html>
