.selectInput {
    width: 90%;
    height: 30px;
}

.toolMenu > div {
    width: 150px;
    height: 30px;
}

.toolMenu input {
    height: 30px;
    width: 150px;
    margin: 0 10px;
}

.tableDiv {
    width: calc(100% - 4px);
    height: calc(100% - 140px);
}

.patientTable tbody tr:first-child th {
    height: 32px;
}

.patientTable th:nth-child(n+2) {
    min-width: 100px;
}

.tableDiv table {
    overflow-x: hidden;
}

.brSearch {
    width: 100%;
    padding-bottom: 10px;
    padding-top: 8px;
}


.brSearch > div > input{
    /*height: 30px;*/
    width: 120px;
}

.fyqdTime{
    width: 100%;
    text-align: center;
}

.fyqdContext{
    width: 780px;
    margin: 0 auto;
    padding-bottom: 30px;
}

.fyqdContext h2{
    width: 100%;
    text-align: center;
    margin: 5px 0;
}

.infoIpt{
    width: 25%;
    margin: 0;
    position: relative;
    float: left;
    height: 32px;
    display: flex;
}
.patientTable {
    min-width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}
.infoIpt span{
    display: block;
    float: left;
    font-size: 12px;
    padding: 8px 0 0 0;
}
.patientTable td, th {
    height: 24px;
    padding: 6px;
    font-size: 14px;
    cursor: default;
    white-space: nowrap;
}
.fyqdTable td{
    border: 1px solid #000000;
}
.infoIpt p {
    float: left;
    text-align: right;
    width: 90px;
    padding: 7px 8px 7px 0;
    font-size: 14px;
    margin: 0;
    color: #333333;
}
.fyqdTable tr:first-child{
    text-align: center;
}

.total{
    margin-top: -1px;
    padding: 4px 10px;
    border: 1px solid #000000;
}

.fyqdTable td span:first-child{
    float: left;
}

.fyqdTable td span:last-child{
    float: right;
    margin-right: 176px;
}

.infoIpt p{
    width: 120px;
}
.height100{
    height: 100%;
}
