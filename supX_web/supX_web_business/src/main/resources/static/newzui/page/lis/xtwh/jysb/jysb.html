<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检验设备</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="jysb.css" rel="stylesheet"/>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary " @click="addJg"><i class="icon-xz1 paddr-r5"></i>新增设备</button>
            <button class="tong-btn btn-parmary-b" @click="sx"><i class=" icon-sx paddr-r5"></i>刷新</button>
            <button class="tong-btn btn-parmary-b " @click="deleteSb"><i class="icon-sc-header paddr-r5"></i>删除</button>
            <button class="tong-btn btn-parmary-b " ><i class="icon-yl paddr-r5"></i>预览</button>
            <button class="tong-btn btn-parmary-b "><i class="icon-dysq paddr-r5"></i>打印</button>
            <button class="tong-btn btn-parmary-b " @click="bg()"><i class="icon-bz paddr-r5"></i>帮助</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">搜&nbsp;索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" v-model="data.hostname" placeholder="请输入关键字" type="text"/>
                    </div>
                </div>
                <button class="zui-btn btn-primary xmzb-db">查询</button>
            </div>
        </div>
    </div>

    <div class="zui-table-view ybglTable padd-l-10 padd-r-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jysbList'"
                                                                                          :type="'all'" :val="isCheckAll">
                    </input-checkbox></div></th>
                    <th><div class="zui-table-cell cell-s"><span>设备编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>设备名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>设备型号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>端口</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>主机ip</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>波特率</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>停止位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>流控制</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>输出缓冲区长</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>会话开始字符</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table" >
                <tbody>
                <tr  v-for="(item, $index) in jysbList" :tabindex="$index"  @dblclick="dbEdit(item)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <input-checkbox @result="reCheckBox" :list="'jysbList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" ><input-checkbox @result="reCheckBox" :list="'jysbList'"
                                                                                           :type="'some'" :which="$index"
                                                                                           :val="isChecked[$index]">
                    </input-checkbox></div></td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.sbbm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s"  v-text="item.hostname"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.machinetype"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.port"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.hostip"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.baudrate"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.stopbit"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.flowcontrol"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.outbuffer"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.inbuffer"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.startchar"></div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s" @click="DelLine(item)"><i class="icon-sc"></i></div>
                    </td>
                </tr>



                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

    <div id="pop" :class="{'popShow':isShow}">
        <!--<transition name="pop-fade">-->
        <div class="pophide" :class="{'show':isShow}" style="z-index: 9999"></div>
        <div class="bg-img" :class="{'show':isShow && typeShow}">
            <h2 class="header">帮助中心</h2>
            <p class="content">设备编码维护说明：
                内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容</p>
            <p class="nobottom" @click="clsoe()">关闭</p>
        </div>
        <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow && !typeShow}"
             style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title"></div>
            <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShow=false">&times;</i></span>
            <div class="layui-layer-content">
                <div class=" layui-mad layui-height" v-text="centent">
                </div>
            </div>
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
            </div>
        </div>
        <!--</transition>-->
    </div>

</div>
<!--侧边窗口-->
<div class="side-form ng-hide" style="padding-top: 0;" id="brzcList" role="form">
    <div class="tab-message">
        <a v-text="sideTitle"></a>
        <a href="javascript:;" class="fr closex ti-close"
           @click="closes"></a>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                <i>设备编码</i>
                <input type="text" class="zui-input border-r4" placeholder="" v-model="jysbObj.sbbm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>设备名称</i>
                <input type="text" class="zui-input border-r4" placeholder="" v-model="jysbObj.hostname" @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>设备型号</i>
                <input type="text" class="zui-input border-r4" placeholder="" v-model="jysbObj.machinetype" @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>使用端口</i>
                <input type="text" class="zui-input border-r4" placeholder="" v-model="jysbObj.port" @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>主&nbsp;机&nbsp;I&nbsp;P</i>
                <input type="text" class="zui-input border-r4" placeholder="" v-model="jysbObj.hostip" @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>接口方式</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbisfile_tran"
                              :index="'jysbObj.isfile'"  :val="jysbObj.isfile"
                              :name="'jysbObj.isfile'" :search="true">
                </select-input>
            </li>
            <li>
                <i>网络类型</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbisbin_tran"
                              :index="'jysbObj.isbin'"  :val="jysbObj.isbin"
                              :name="'jysbObj.isbin'" :search="true">
                </select-input>
            </li>
            <li>
                <i>波&nbsp;&nbsp;特&nbsp;率</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbbaudrate_tran"
                              :index="'jysbObj.baudrate'"  :val="jysbObj.baudrate"
                              :name="'jysbObj.baudrate'" :search="true">
                </select-input>
            </li>
            <li>
                <i>停&nbsp;&nbsp;止&nbsp;位</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbstopbit_tran"
                              :index="'jysbObj.stopbit'"  :val="jysbObj.stopbit"
                              :name="'jysbObj.stopbit'" :search="true">
                </select-input>
            </li>
            <li>
                <i>数&nbsp;&nbsp;据&nbsp;位</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbdatabit_tran"
                              :index="'jysbObj.databit'"  :val="jysbObj.databit"
                              :name="'jysbObj.databit'" :search="true">
                </select-input>
            </li>
            <li>
                <i>流&nbsp;&nbsp;控&nbsp;制</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbflowcontrol_tran"
                              :index="'jysbObj.flowcontrol'"  :val="jysbObj.flowcontrol"
                              :name="'jysbObj.flowcontrol'" :search="true">
                </select-input>
            </li>
            <li>
                <i>输&emsp;&nbsp;&nbsp;出缓&nbsp;冲&nbsp;区</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysboutbuffer_tran"
                              :index="'jysbObj.inbuffer'"  :val="jysbObj.inbuffer"
                              :name="'jysbObj.inbuffer'" :search="true">
                </select-input>
            </li>
            <li>
                <i>效验方式</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbverify_tran"
                              :index="'jysbObj.verify'"  :val="jysbObj.verify"
                              :name="'jysbObj.verify'" :search="true">
                </select-input>
            </li>
            <li>
                <i>会&emsp;&nbsp;&nbsp;话开&nbsp;始&nbsp;符</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbstartchar_tran"
                              :index="'jysbObj.stopchar'"  :val="jysbObj.stopchar"
                              :name="'jysbObj.stopchar'" :search="true">
                </select-input>
            </li>
            <li>
                <i>会&emsp;&nbsp;&nbsp;话结&nbsp;束&nbsp;符</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="jysbstartchar_tran"
                              :index="'jysbObj.stopchar'"  :val="jysbObj.stopchar"
                              :name="'jysbObj.stopchar'" :search="true">
                </select-input>
            </li>
            <li>
                <i>应&nbsp;&nbsp;答&nbsp;符</i>
                <input type="text" class="zui-input border-r4" placeholder="" v-model="jysbObj.hfzf"  @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>样本号 生成方式</i>
                <select-input @change-data="resultChange" :not_empty="true" :child="jysbybhscfs_tran"
                              :index="'jysbObj.ybhscfs'"  :val="jysbObj.ybhscfs"
                              :name="'jysbObj.ybhscfs'" :search="true">
                </select-input>
            </li>
            <li>
                <i>样&emsp;&nbsp;&nbsp;本号&nbsp;位&nbsp;数</i>
                <select-input @change-data="resultChange" :not_empty="true" :child="jysbybhws_tran"
                              :index="'jysbObj.ybhws'"  :val="jysbObj.ybhws"
                              :name="'jysbObj.ybhws'" :search="true">
                </select-input>
            </li>
            <li>
                <i>报告格式</i>
                <select-input @change-data="resultChange" :not_empty="true" :child="jysbbgdygsfs_tran"
                              :index="'jysbObj.bgdygsfs'"  :val="jysbObj.bgdygsfs"
                              :name="'jysbObj.bgdygsfs'" :search="true">
                </select-input>
            </li>
            <li>
                <i>接&emsp;&nbsp;&nbsp;口设&nbsp;备&nbsp;名</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="util.jksb"
                              :index="'sbmc'" :index_val="'sbbm'" :val="jysbObj.bzbm"
                              :name="'jysbObj.bzbm'" :search="true">
                </select-input>
            </li>
            <li>
                <i>附属设备</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="util.jysb"
                              :index="'hostname'" :index_val="'sbbm'" :val="jysbObj.fssb"
                              :name="'jysbObj.fssb'" :search="true">
                </select-input>
            </li>
            <li>
                <i>附属设备样本号是否与主设备一致</i>
                <select-input @change-data="resultChange" :not_empty="true" :child="jysbfssbybhyz_tran"
                              :index="'jysbObj.fssbybhyz'"  :val="jysbObj.fssbybhyz"
                              :name="'jysbObj.fssbybhyz'" :search="true">
                </select-input>
            </li>
            <li>
                <i>检验科室</i>
                <select-input @change-data="resultChange" :not_empty="false" :child="util.sjks"
                              :index="'ksmc'" :index_val="'ksbm'" :val="jysbObj.jyks"
                              :name="'jysbObj.jyks'" :search="true">
                </select-input>
            </li>
            <li>
                <i>手工设备</i>
                <input type="checkbox" id="sb" v-model="jysbObj.sgsb" class="green"  @keydown="nextFocus($event)"/>
                <label for="sb"></label>
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" v-if="isDetail" @click="confirms">保存</button>
        <button class="zui-btn btn-primary xmzb-db" v-if="isAdd" @click="confirms_Add">保存</button>
    </div>
</div>


<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="jysb.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
