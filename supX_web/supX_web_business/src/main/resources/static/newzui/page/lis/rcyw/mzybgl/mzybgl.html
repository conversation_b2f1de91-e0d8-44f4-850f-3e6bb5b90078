<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>门诊样本管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="mzybgl.css" rel="stylesheet">
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper">
    <div class="panel box-fixed">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-hqsq paddr-r5" @click="hqsq">获取申请</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
            <button class="tong-btn btn-parmary-b icon-cydj paddr-r5" @click="cydj">采样登记</button>
            <button class="tong-btn btn-parmary-b icon-dysq paddr-r5" @click="dayin()">打印申请</button>
            <!-- <button class="tong-btn btn-parmary-b icon-scybh paddr-r5">生成样本号</button> -->
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">申请日期</label>
                    <div class="zui-input-inline zui-select-inline zui-date">
                        <i class="datenox icon-rl"></i>
                        <input type="text" name="phone" class="zui-input todate padd-l33" v-model="param.time" placeholder="请选择申请日期">
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">条码打印机</label>
                    <div class="zui-input-inline margin-l13">
                    <!-- v-model="param.jyxh" @click.enter="cydj" -->
                        <input class="zui-input wh180"  placeholder="FAX" type="text"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label padd-l14">检索码</label>
                    <div class="zui-input-inline">
                        <input type="text" class="zui-input" name="input1" placeholder="请输入检索码" v-model="param.bah"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <button class="zui-btn btn-primary  xmzb-db" @click="cxjydj">查询</button>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable" id="utable1 zui-table-body" z-height="full" style="border:none;margin-top: 108px;padding: 0 10px; background: #fff">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px">
                        <input-checkbox @result="reCheckBox" :list="'jydjList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="id" z-width="50px">
                        <div class="zui-table-cell">序号</div>
                    </th>
                    <th z-field="username" z-width="80px" z-style="text-align:center;">
                        <div class="zui-table-cell">类型</div>
                    </th>
                    <th z-field="sex" z-width="90px">
                        <div class="zui-table-cell">来源</div>
                    </th>
                    <th z-field="city" z-width="80px">
                        <div class="zui-table-cell">样本号</div>
                    </th>
                    <th z-field="sign" z-width="80px">
                        <div class="zui-table-cell">病员姓名</div>
                    </th>
                    <th z-field="experience" z-width="60px">
                        <div class="zui-table-cell">性别</div>
                    </th>
                    <th z-field="score" z-width="60px">
                        <div class="zui-table-cell">年龄</div>
                    </th>
                    <th z-field="classify" z-width="110px">
                        <div class="zui-table-cell">住院号/门诊号</div>
                    </th>
                    <th z-field="wealth" z-width="90px">
                        <div class="zui-table-cell">检验项目</div>
                    </th>
                    <th z-field="wealth1" z-width="90px">
                        <div class="zui-table-cell">扣费名称</div>
                    </th>
                    <th z-field="wealth2" z-width="90px">
                        <div class="zui-table-cell">扣费金额</div>
                    </th>
                    <th z-field="wealth3" z-width="100px">
                        <div class="zui-table-cell">送检科室</div>
                    </th>
                    <th z-field="wealth4" z-width="100px">
                        <div class="zui-table-cell">送检医师</div>
                    </th>
                    <th z-field="wealth5" z-width="100px">
                        <div class="zui-table-cell">执行设备</div>
                    </th>
                    <th z-field="wealth6" z-width="100px">
                        <div class="zui-table-cell">申请日期</div>
                    </th>
                    <th z-field="wealth7" z-width="100px">
                        <div class="zui-table-cell">临床诊断</div>
                    </th>
                    <th z-field="wealth" z-width="100px">
                        <div class="zui-table-cell">检验序号</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body">
            <table class="zui-table">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jydjList" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">

                        <input-checkbox @result="reCheckBox" :list="'jydjList'"
                        :type="'some'" :which="$index"
                        :val="isChecked[$index]">
                        </input-checkbox>
                        </div>
                    </td>
                    <td width="50px"><div class="zui-table-cell" v-text="$index+1"></div></td>
                    <td width="80px"><div class="zui-table-cell" v-text="jydjlx_tran[item.lx]"></div></td>
                    <td width="90px"><div class="zui-table-cell" v-text="jydjly_tran[item.ly]"></div></td>
                    <td width="80px"><div class="zui-table-cell" v-text="item.bbbh"></div></td>
                    <td width="80px"><div class="zui-table-cell" v-text="item.brxm"></div></td>
                    <td width="60px"><div class="zui-table-cell" v-text="brxb_tran[item.xb]"></div></td>
                    <td width="60px"><div class="zui-table-cell" v-text="item.nl"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.bah"></div></td>
                    <td width="90px"><div class="zui-table-cell" v-text="item.jyxmmc"></div></td>
                    <td width="90px"><div class="zui-table-cell" v-text="item.fymc"></div></td>
                    <td width="90px"><div class="zui-table-cell" v-text="item.fyje"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.ksmc"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.sqysxm"></div></td>
                    <td width="100px"><div class="zui-table-cell">
                        <select-input @change-data="resultChange" :not_empty="true"
                        :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="item.zxsb"
                        :search="true" :name="'item.zxsb'">
                        </select-input>
                    </div></td>
                    <td width="100px"><div class="zui-table-cell">{{item.sqrq | formDate}}</div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.lczd"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.jyxh"></div></td>
                </tr>
                <!--<tr>-->
                    <!--<td width="50px">-->
                        <!--<div class="zui-table-cell">-->

                            <!--&lt;!&ndash;<input-checkbox @result="reCheckBox" :list="'jydjList'"&ndash;&gt;-->
                                            <!--&lt;!&ndash;:type="'some'" :which="$index"&ndash;&gt;-->
                                            <!--&lt;!&ndash;:val="isChecked[$index]">&ndash;&gt;-->
                            <!--&lt;!&ndash;</input-checkbox>&ndash;&gt;-->
                        <!--</div>-->
                    <!--</td>-->
                    <!--<td width="50px"><div class="zui-table-cell">序号</div></td>-->
                    <!--<td width="80px"><div class="zui-table-cell" >类型</div></td>-->
                    <!--<td width="90px"><div class="zui-table-cell">来源</div></td>-->
                    <!--<td width="80px"><div class="zui-table-cell">样本号</div></td>-->
                    <!--<td width="80px"><div class="zui-table-cell">病员姓名</div></td>-->
                    <!--<td width="60px"><div class="zui-table-cell">年龄</div></td>-->
                    <!--<td width="60px"><div class="zui-table-cell">住院号/门诊号</div></td>-->
                    <!--<td width="100px"><div class="zui-table-cell">检验项目</div></td>-->
                    <!--<td width="90px"><div class="zui-table-cell">扣费名称</div></td>-->
                    <!--<td width="90px"><div class="zui-table-cell">扣费名称</div></td>-->
                    <!--<td width="90px"><div class="zui-table-cell">送检科室</div></td>-->
                    <!--<td width="100px"><div class="zui-table-cell">送检医师</div></td>-->
                    <!--<td width="100px"><div class="zui-table-cell">执行设备</div></td>-->
                    <!--<td width="100px"><div class="zui-table-cell">申请日期</div></td>-->
                    <!--<td width="100px"><div class="zui-table-cell">临床诊断</div></td>-->
                    <!--<td width="100px"><div class="zui-table-cell">检验序号</div></td>-->
                    <!--<td width="100px"><div class="zui-table-cell">检验序号</div></td>-->
                <!--</tr>-->
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
    <div id="pop">
        <!--<transition name="pop-fade">-->
        <div class="pophide" :class="{'show':isShowpopL}"></div>
        <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}"
             style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title"></div>
            <span class="layui-layer-setwin">
                <a href="javascript:;" style="color:rgba(255,255,255,0.5);font-size: 26px; margin-top: -15px;"
                   @click="isShowpopL=false,isShow=false">&times;</a>
            </span>
            <div class="layui-layer-content">
                <div class=" layui-mad layui-height" v-text="centent"></div>
            </div>
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save" @click="cydjOk">保存</button>
            </div>
        </div>
        <!--</transition>-->
    </div>
</div>
<script src="mzybgl.js"></script>
<script>
    $(function () {
        $(".zui-input").uicomplete();
        $(".zui-table-view").uitable();
        lay('.zui-date .zui-input').each(function () {
            laydate.render({
                elem: this
                , eventElem: '.zui-date i.datenox'
                , trigger: 'click'
            });
        });
        $(".f-hzgl").uitab();

        //测试JS
        $("#utable1 .zui-table-body tr").dblclick(function () {
            $(".side-form").removeClass("ng-hide").after("<div class='side-form-bg'></div>");
            $("#tab-info input").attr("disabled", true)
            $("body").css("overflow", "hidden");
            $("button#info_edit").text("修改");
            $(".info_name").text($(this).children("[field=username] ").children(".zui-table-cell").html());
        });

        $("button#info_edit").click(function () {
            $("#tab-info input").attr("disabled", false);
            $(this).text("提交数据");
        });
    });
</script>
</body>
</html>
