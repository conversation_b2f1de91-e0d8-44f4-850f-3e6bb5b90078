(function(){
  //单页面菜单的加载
  var InfoMenu = new Vue({
      el: '.fyxm-tab',
      data: {
          which: 0
      },
      methods: {
          loadCon: function (page) {
              var pageDiv = $("#"+page);
              $(".page_div").hide();
              if(pageDiv.length == 0){
                  $("."+page).load(page+".html").fadeIn(300);
              } else {
                  $("."+page).fadeIn(300);
              }
          }
      }
  });
  InfoMenu.loadCon('zlbxxm');
})();