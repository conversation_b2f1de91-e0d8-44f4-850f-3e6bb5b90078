

.icon-bj-t:before {
  top: -7px;
  left: 4px;
}
.tong-padded {
  padding: 20px 10px 10px;
}
.tong-search .zui-form .padd-l-40 {
  padding-left: 40px !important;
}
.rkgl-kd {
  width: 100%;
  padding: 5px 0 0;
  box-sizing: border-box;
  color: #333333;
  font-size: 14px;
}
.rkgl-kd span {
  padding-left: 27px;
}
.tab-edit-list .inner li {
  width: 100% !important;
  margin-bottom: 0 !important;
}
.ksys-side .zui-select-inline {
  margin-right: 0px !important;
}
.tab-edit-list {
  padding: 5px 5px;
}
.rkgl-position {
  position: fixed;
  bottom: 10px;
  left: 10px;
  right: 10px;
  width: auto;
  z-index: 11;
  height: 70px;
  background: #fff;
  align-items: center;
  color: #81878e;
  border-top: 1px solid #eee;
}


.tem {
  position: relative;
  float: left;
  /*width: 300px;*/
  /*height: 450px;*/
  width: 800px;
  height: 500px;
  border: 1px solid green;
  /*margin-left: 20px;*/
  margin-top: 20px;
}
.bgbs {
  background-color: #ffffff;
}
.item {
  position: absolute;
  display: inline-block;
  top: 10px;
  margin-bottom: 20px;
  font-size: 14px;
  cursor: default;
  z-index: 100;
}
.item span {
  float: left;
  /*height: 38px;*/
  z-index: 1;
  /*line-height: 38px;*/
}
.tem {
  position: relative;
  float: left;
  /*width: 300px;*/
  /*height: 450px;*/
  width: 800px;
  height: 500px;
  border: 1px solid green;
  margin-left: 20px;
  margin-top: 20px;
}

.item {
  position: absolute;
  display: inline-block;
  top: 10px;
  margin-bottom: 20px;
  font-size: 14px;
  cursor: default;
  z-index: 100;
}

.item span {
  float: left;
}

.item input {
  float: left;
  width: 80px;
  border: 1px solid #aaaaaa;
  padding: 4px;
}

.item div {
  float: left;
  width: 100px;
  height: 20px;
  border: 0;
  border-bottom: 1px solid #000000;
}
.temTable{
  position: absolute;
  display: inline-block;
}

.printTable{
  border: 0;
  border-collapse: collapse;
}

.printTable td{
  border: 1px solid #444444;
  width: 30px;
  height: 30px;
}
.zui-table-body table .background-red,.zui-table-body table .background-red:hover{
  background: #ff0000c7 !important;
}
.colorRed{
  color: red;
}
