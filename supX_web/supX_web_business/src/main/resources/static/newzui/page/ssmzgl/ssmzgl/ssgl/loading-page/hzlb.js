$(window).resize(function () {
    hzlb.brk_list()
});
var hzlb = new Vue({
    el: '#hzlb',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        pt_tran: {'0': '全部', '1': '已排台', '2': '未排台'},
        ss_tran: {'0': '未手术', '1': '已手术'},
        sslx_tran1: {'0': '急诊', '1': '择期'},
        sslx_tran: {'0': '急诊', '1': '择期', '2': '抢救', '3': '日间'},
        ptStatus: '0',
        ssbz: '0',
        kcList: [],
        pageState: {
            ksList: {},
            ks: '',
            sort: 'cw',
            zyType: '',
            hldj: '0',
            bkzt: '0',
            jiansuoVal: '',
            brList: []
        },
        yzztlist: [],
        caqxContent: {
            N03012200102: '0',
            N03012200103: '8',
            // N03012200104:'04',
        },//参数权限对象
        popContent: {},
        param: {
            page: 1,
            rows: 10,
            sort: '',
            order: 'asc',
            parm: '',
            timeStart: '',
            timeEnd: ''
        },
        brListCheckBox: [],
        allKs: [],
        isChecked: [],
        isCheckAll: false,
        val: false,
        ztShow: [],
        oldActiveIndex: 0,
        num: 0,
        lsBrList: [],
        brk_listD: 0,
        noticeContent: {},
        czList: [{
            name: "科室诊疗信息查询",
            clickBc: function (brIndex, list) {
                var json = {
                    userName: list,
                    csqx: hzlb.caqxContent,
                    ssyz: '0',
                }
                sessionStorage.setItem('ssmzUser', JSON.stringify(json))
                hzlb.topNewPage('科室诊疗信息查询', 'page/ssmzgl/ssmzgl/ssgl/loading-page/hzzx.html');
            }
        }, {
            name: "手术排台",
            clickBc: function (brIndex, list) {
                var json = {
                    sspt: list,
                    csqx: hzlb.caqxContent,
                    allKs: hzlb.allKs,
                }
                sessionStorage.setItem('sspt', JSON.stringify(json))
                hzlb.topNewPage('手术排台', 'page/ssmzgl/ssmzgl/ssgl/loading-page/sspt.html');
            }
        }, {
            name: "取消排台",
            clickBc: function (brIndex, list) {
                hzlb.caozuo('qxpt', list)
            }
        },
            //todo：给手术管理添加快速访问普通医嘱的按钮
            // {
            //     name: "医嘱",
            //     clickBc: function (brIndex, list) {
            //         sessionStorage.setItem("toBagl", JSON.stringify({"entry": "zyys-hzglList"}));
            //         sessionStorage.setItem("bagljbxx", list.zyh);
            //         var val = ['userPage/yzgl', 1, list];
            //         val.push(hzlb.popContent.ksbm);
            //         val[2].ksbm = hzlb.popContent.ksbm;
            //         sessionStorage.setItem('userPage', JSON.stringify(val))
            //         var json = {
            //             userName: list,
            //             csqx: hzlb.caqxContent,
            //             ssyz: '1',
            //         }
            //         $.getJSON("/actionDispatcher.do?reqUrl=GetUserInfoAction", function (json) {
            //             if (json.a == 0) {
            //                 var user = json.d;
            //                 if (user.ysbz == '0' || user.hsbz == '1') {
            //                     malert('对不起,您无权进行该操作!', 'right', 'defeadted');
            //                     return;
            //                 }
            //                 hzlb.topNewPage(list.brxm, 'page/zyysz/zyysz/hzgl/hzzx/hzzx.html');
            //             } else {
            //                 malert("查询失败", 'top', 'defeadted');
            //             }
            //         });
            //
            //
            //     }
            // },
            {
                name: "手术医嘱",
                clickBc: function (brIndex, list) {
                    var json = {
                        userName: list,
                        csqx: hzlb.caqxContent,
                        ssyz: '1',
                    }
                    sessionStorage.setItem('ssmzUser', JSON.stringify(json))
                    // sessionStorage.setItem('userPage', JSON.stringify(json))

                    $.getJSON("/actionDispatcher.do?reqUrl=GetUserInfoAction", function (json) {
                        if (json.a == 0) {
                            var user = json.d;
                            if (user.ysbz == '0' || user.hsbz == '1') {
                                malert('对不起,您无权进行该操作!', 'right', 'defeadted');
                                return;
                            }
                            hzlb.topNewPage('手术医嘱', 'page/ssmzgl/ssmzgl/ssgl/loading-page/hzzx.html');
                        } else {
                            malert("查询失败", 'top', 'defeadted');
                        }
                    });


                }
            },
            // {
            //           name: "医嘱审核领药",
            //           clickBc: function (brIndex, list) {
            //               sessionStorage.setItem('yzshly', JSON.stringify(list))
            //               hzlb.topNewPage('医嘱审核领药', 'page/ssmzgl/ssmzgl/ssgl/loading-page/yzshly.html');
            //           }
            //       },
            {
                name: "退药申请",
                clickBc: function (brIndex, list) {
                    var brjson = {
                        brlist: [list],
                        ksid: list.ssks,
                        csqx: hzlb.caqxContent,
                    };
                    sessionStorage.setItem('tysq', JSON.stringify(brjson));
                    hzlb.topNewPage('退药申请', 'page/hsz/hlyw/yzcl/loading-page/tysq.html');
                }
            }, {
                name: "费用记帐",
                clickBc: function (brIndex) {
                    sessionStorage.setItem("hszToZygl" + userId, JSON.stringify({
                        zyh: hzlb.pageState.brList[brIndex].zyh,
                        brxx: hzlb.pageState.brList[brIndex],
                        ksbm: hzlb.pageState.brList[brIndex].ssks,
                        ksmc: hzlb.pageState.brList[brIndex].ssksmc,
                    }));
                    hzlb.topNewPage('费用记帐', 'page/zygl/rcygl/fyjz/fyjz.html', 'N050022011');
                }
            }, {
                name: "退费申请",
                clickBc: function (brIndex) {
                    sessionStorage.setItem("hszToZygl" + userId, JSON.stringify({
                        type: "jztf",
                        zyh: hzlb.pageState.brList[brIndex].zyh,
                        brxx: hzlb.pageState.brList[brIndex]
                    }));
                    hzlb.topNewPage('住院管理', 'page/zygl/rcygl/rydj/rydj.html');
                }
            }, {
                name: "费用查询",
                clickBc: function (brIndex) {
                    sessionStorage.setItem("jztf", JSON.stringify({
                        returnType: '1',
                        zyh: hzlb.pageState.brList[brIndex].zyh
                    }));
                    hzlb.topNewPage('记账退费', 'page/zygl/rcygl/fyjz/jztf.html');
                }
            }, {
                name: "手术护理文书",
                clickBc: function (brIndex, br) {
                    hzlb.xbl(br)
                }
            },
            {
                name: "PACS",
                clickBc: function (brIndex, br) {
                    hzlb.openPacsTZ(brIndex)
                }
            },
            {
                name: "LIS结果",
                clickBc: function (brIndex, br) {
                    hzlb.openLisBG(brIndex)
                }
            },
            {
                name: "手术完成",
                clickBc: function (brIndex, list) {
                    sessionStorage.setItem('sswc', JSON.stringify(list))
                    hzlb.topNewPage('手术完成', 'page/ssmzgl/ssmzgl/ssgl/loading-page/sswc.html');
                }
            }, {
                name: "取消完成",
                clickBc: function (brIndex, list) {
                    hzlb.caozuo('qxwc', list)
                }
            }, {
                name: "手术作废",
                clickBc: function (brIndex, list) {
                    hzlb.caozuo('sszf', list)
                }
            }],
        ssjList: [],
    },
    watch: {
        'pageState.brList': function (val, oldVal) {
            if (val !== oldVal) {
                this.brListCheckBox = this.pageState.brList;
                this.isChecked = [];
            }
        },
        'isChecked': function (val, oldVal) {
            var length = this.brListCheckBox.length,
                isTrueAll = false;
            for (var i = 0; i < length; i++) {
                if (!val[i]) {
                    this.isCheckAll = false;
                    return false;
                } else isTrueAll = true;
            }
            if (isTrueAll) {
                this.isCheckAll = true;
            }
        }
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {
        var myDate = new Date();
        this.param.timeStart = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.timeEnd = getTodayDateEnd();

        laydate.render({
            elem: '#timeStart',
            value: this.param.timeStart,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                hzlb.param.timeStart = value;
                hzlb.getHzlb();
            }
        });
        laydate.render({
            elem: '#timeEnd',
            value: this.param.timeEnd,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                hzlb.param.timeEnd = value;
                hzlb.getHzlb();
            }
        });
        this.Wf_getKs();
        this.getPtxx();
        this.getSsjList();
    },
    methods: {
        openPacsTZ: function (brIndex) {
            debugger
            window.open("http://172.20.103.12:8802/?hisid=" + hzlb.pageState.brList[brIndex].brid);
        },
        openLisBG: function (brIndex) {
            var url = 'http://220.220.220.9/ZhiFang.ReportFormQueryPrint/ui_new/doctor.html?PATNO=' + hzlb.pageState.brList[brIndex].zyh;
            window.open(url);
        },
        getSsjList: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1SsSjwhSsj&types=query&parm=" + JSON.stringify({'stop': '0'}), function (json) {
                if (json.a == 0)
                    hzlb.ssjList = json.d.list;
                else {
                    malert("查询失败", 'top', 'defeadted');
                }
            });
        },
        getPtxx: function () {
//            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=QueryPtMsg', function (json) {
//                if (json.a == '0') {
//                    hzlb.noticeContent.zyrs=json.d.ptTotal;
//                    hzlb.noticeContent.jrry=json.d.dayTotal;
//                    hzlb.noticeContent.tjhl=json.d.wpt;
//                    hzlb.noticeContent.yjhl=json.d.ypt;
//                }
//            });
        },
        commonResultChange: function (val) {
            this.ptStatus = val[0];
            this.getHzlb();
        },
        commonResultChange1: function (val) {
            this.sslx = val[0];
            this.getHzlb();
        },
        filterFun: function (index, zt) {
            this.pageState.brList = [];
            for (var i = 0; i < hzlb.lsBrList.length; i++) {
                if (index == hzlb.lsBrList[i][zt]) {
                    this.pageState.brList.push(hzlb.lsBrList[i]);
                }
            }
        },
        filterFunz: function () {
            this.pageState.brList = hzlb.lsBrList
        },
        doCheck: function () {
            this.val = !this.val
            if (this.val) {
                hzlb.param.timeStart = getTodayDateBegin();
                hzlb.param.timeEnd = getTodayDateEnd();
            } else {
                hzlb.param.timeStart = null;
                hzlb.param.timeEnd = null;
            }
        },
        //获取科室
        Wf_getKs: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"N030122001"}', function (json) {
                if (json.a == '0') {
                    var rlksList = [];
                    for (var i = 0; i < json.d.length; i++) {
                        if (json.d[i].bqbm != null) {
                            rlksList.push(json.d[i]);
                        }
                    }
                    hzlb.allKs = rlksList;
                    //hzlb.allKs.unshift({ksbm:'%','ksmc':'全院'});
                    hzlb.popContent.ksbm = rlksList[0].ksbm;
                    hzlb.getCsqx()
//                	panel.allKs = json.d;
//                	panel.popContent.ksbm =json.d[0].ksbm;
                    //科室获取成功后再查询患者信息
                    hzlb.getHzlb();
                }
            });
        },
        resultChangeOd: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.$forceUpdate()
            hzlb.getHzlb();
        },
        brk_list: function () {
            if (this.pageState.brList.length >= 1) {
                this.brk_listD = parseInt(this.$refs.kp.offsetWidth / 270) - (this.pageState.brList.length % parseInt(this.$refs.kp.offsetWidth / 270))
            }
        },
        show: function (index) {
            if (index != this.num) {
                this.num = index;
                if (index == 0) {
                    $('body').css({
                        'padding-bottom': '10px',
                    });
                    $('.wrapper').css({
                        'padding-bottom': '28px!important',
                    });
                } else if (index == 1) {
                    $('body').css({
                        'padding-bottom': '10px',
                    });
                    $('.wrapper').css({
                        'padding-bottom': '0px!important',
                    });
                }
            }
        },
        duoxuan: function (index) {
            if (event.shiftKey) {
                var min = this.oldActiveIndex < index ? this.oldActiveIndex : index,
                    max = this.oldActiveIndex < index ? index : this.oldActiveIndex;
                for (var i = min; i <= max; i++) {
                    this.isChecked[i] = this.isChecked[index];
                }
            }
            this.oldActiveIndex = index;
        },
        reCheckBoxCB: function (arg) {
            this.reCheckBox(arg);
            this.duoxuan(arg[1]);
        },
        ssbzResultChange: function (val) {
            hzlb.ssbz = val[0];
            this.getHzlb();
        },
        getHzlb: function () {
            common.openBar('#brCard');
            var str = {
                zyh: hzlb.pageState.jiansuoVal,
                bengdate: hzlb.param.timeStart,
                enddate: hzlb.param.timeEnd,
                //wwcss: this.val ? 1 : 0,
                ssbz: hzlb.ssbz,
                sslx: this.popContent.sslx == '%' ? '' : this.popContent.sslx,
                ssks: this.popContent.ksbm == '%' ? '' : this.popContent.ksbm,
                ptStatus: this.ptStatus,
                parm: hzlb.pageState.jiansuoVal
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=QuerySssq&parm=' + JSON.stringify(str) + '', function (json) {
                if (json.a == '0' && json.d != null) {

                    hzlb.noticeContent.zyrs = json.d.list.length;
                    hzlb.noticeContent.jrry = 0;
                    hzlb.noticeContent.tjhl = 0;
                    hzlb.noticeContent.yjhl = 0;
                    for (var i = 0; i < json.d.list.length; i++) {
                        if (json.d.list[i].ssj) {
                            json.d.list[i].ssjmc = hzlb.listGetName(hzlb.ssjList, json.d.list[i].ssj, 'bm', 'mc');
                        }

                        if (json.d.list[i].ssptbz == '0') {
                            hzlb.noticeContent.tjhl += 1;
                        } else {
                            hzlb.noticeContent.yjhl += 1;
                        }
                        if (json.d.list[i].aprq && hzlb.fDate(new Date(), 'date') == json.d.list[i].aprq.substr(0, 10)) {
                            hzlb.noticeContent.jrry += 1;
                        }
                    }
                    hzlb.pageState.brList = json.d.list;
                    hzlb.brk_list();
                    hzlb.getPtxx();
                    common.closeLoading();
                    console.log(hzlb.pageState.brList[0].total)

                } else {
                    common.closeLoading()
                    malert("获取患者列表失败!" + json.c, 'top', 'defeadted')
                }
            });
        },
        //获取参数权限
        getCsqx: function () {
            var parm = {
                "ylbm": 'N030122001',
                "ksbm": hzlb.popContent.ksbm == '%' ? ksbm : hzlb.popContent.ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d && json.d.length > 0) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N03012200102": //手术排台模式 0-完整模式，1-简单模式
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    hzlb.caqxContent.N03012200102 = csjson.csz;
                                }
                                break;
                            case "N03012200103": //手术医嘱默认分类 2-术中，3-术后，8-领药
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    hzlb.caqxContent.N03012200103 = csjson.csz;
                                }
                                break;
                            case "N03012200104": //手术医嘱默认药房 录入默认药房编码
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    hzlb.caqxContent.N03012200104 = csjson.csz;
                                }
                                break;
                        }
                    }
                } else {
                    malert("参数权限获取失败" + json.c, 'top', 'defeadted');
                }
            });
        },
        openYz: function (brInfo) {
            var brlist = [];
            brlist.push(brInfo);
            var brjson = {
                brlist: brlist,
                ksid: hzlb.pageState.ks,
            };
            sessionStorage.setItem("yzlb", JSON.stringify(brjson));
            this.topNewPage('医嘱列表', 'page/hsz/hlyw/yzcl/loading-page/yzlb.html');
        },
        //    操作按钮点击事件
        caozuo: function (type, list) {
            if (type == 'tysq') {
                if (list.length > 1) { //退药申请
                    var brjson = {
                        brlist: list,
                        ksid: hzlb.pageState.ks,
                        csqx: hzlb.caqxContent,
                    };
                    sessionStorage.setItem('tysq', JSON.stringify(brjson));
                    this.topNewPage('退药申请', 'page/hsz/hlyw/yzcl/loading-page/tysq.html');
                }
            } else if (type == 'qxwc') {
                if (list.zyh != null) {
                    common.openConfirm("你确定要取消完成【" + (list.zyh) + "】住院号的病人吗？", function () {
                        var param = {
                            ssbz: '0',
                            czy: userId,
                            sqdh: list.sssqdh,
                            zfbz: list.zfbz
                        };
                        hzlb.$http.post('/actionDispatcher.do?reqUrl=New1SsRcywSsap&types=update', JSON.stringify(param)).then(function (json) {
                            if (json.body.a == '0') {
                                malert("取消完成成功！", 'top', 'success');
                                hzlb.getHzlb();
                            } else if (json.body.a == '-1') {
                                malert(json.body.c, 'top', 'defeadted');
                            }
                        });
                    }, function () {
                        malert("取消完成！", 'top', 'defeadted');
                    })
                }
            } else if (type == 'sszf') {
                if (list.zyh != null) {
                    common.openConfirm("你确定要作废【" + (list.zyh) + "】住院号的病人吗？", function () {
                        malert("作废成功！", 'top', 'success');
                    }, function () {
                        malert("取消作废！", 'top', 'defeadted');
                    })
                }
            } else {
                if (list.zyh != null) {
                    common.openConfirm("你确定要取消排台【" + (list.zyh) + "】住院号的病人吗？", function () {
                        hzlb.qxpt(list)
                    }, function () {
                        malert("取消排台成功！", 'top', 'defeadted');
                    })
                }
            }
        },
        qxpt: function (val) {
            var str = {
                zyh: val.zyh,
                sqdh: val.sssqdh,
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1SsRcywSsap&types=cancel&parm=' + JSON.stringify(str) + '', function (json) {
                if (json.a == '0' && json.d != null) {
                    malert(json.c, 'top', 'success')
                    hzlb.getHzlb()
                } else {
                    malert(json.c, 'top', 'defeadted')
                }
            });
        },
        czClick: function (index, $event) {
            removeActiveCzButt();
            var el = $("#cz-" + index),
                conEl = $('.content', el),
                height = $(window).height(),
                width = $(window).width(),
                x = $event.clientX,
                y = $event.clientY;
            if (x > width / 2) {
                conEl.addClass('left');
            }
            if (y > height / 2) {
                conEl.addClass('top');
            }
            if (y > height / 3 * 2) {
                conEl.addClass('bottom');
            }
            el.addClass("active");
        },
        xbl: function (br) {
            $.ajaxSettings.async = false;
            var sxdz = "";
            var user = "";
            var password = "";
            $.getJSON("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    var csContent = JSON.parse(JSON.stringify(json.d.list[0]));
                    sxdz = csContent.blSxdz;
                    user = userId;
                    $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                        if (json.a == "0") {
                            password = json.d.password;
                        }
                    });
                    if (sxdz == "") {
                        malert("书写地址为空，打开病历失败！", 'top', 'defeadted');
                        return
                    }
                    if (user == "") {
                        malert("用户名为空，打开病历失败！！", 'top', 'defeadted');
                        return
                    }
                    if (password == "") {
                        malert("用户密码为空，打开病历失败！", 'top', 'defeadted');
                        return
                    }
                    var zyh = br.zyh;
                    if (!zyh) {
                        malert("请先选择病人后再书写病历！", 'top', 'defeadted');
                        return
                    }
                    var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=" + zyh + ",userid=" + user + ",password=" + password + ",lyzyhmz=0,blhhl=0";
                    window.open(url);
                }
            });
        },
    }
});

$(window).click(function () {
    removeActiveCzButt();
});

function removeActiveCzButt() {
    var box = $('.cz-butt.active');
    if (box.length > 0) {
        $('.content', box).removeClass("left top bottom");
        box.removeClass("active");
    }
}


/**
 * 更多操作按钮区域鼠标移入移出事件处理函数
 * @param  {[布尔类型]} type [true表示移入，false表示移除，默认为false]
 * @return 无
 */
function moreMouse(dom, type) {
    var $thisDom = $(dom),
        $contentDom = $('.content', $thisDom);
    if (type) {
        var height = $(window).height() / 2,
            moreY = event.clientY;
        if (moreY > height) {
            $contentDom.addClass('top');
        }
    } else {
        $contentDom.removeClass("top,left,bottom");
    }
}
