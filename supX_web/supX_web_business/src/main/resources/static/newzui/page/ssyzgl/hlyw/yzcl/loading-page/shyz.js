var shyz = new Vue({
    el: '#loadingPage',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
    	brlistjson:{},//只用于接受请求LIST对象
        brList:[],
        yzList: [],
        jkList:[],
        yzshInfoList:[],//真正的列表
        zyhs:[],
        ksid:null,//科室编码
        isOver: false,//是否全选
        hoverBrListIndex: undefined,
    },
    mounted: function () {
        this.moun();
        window.addEventListener('storage',function (e) {
            if( e.key == 'shyz' && e.oldValue !== e.newValue ){
                shyz.zyhs = [];
                shyz.moun();
            }
        });
    },
    updated: function () {
        changeWin();
    },
    methods: {
        closePage: function () { //关闭本页面
            var x = parseInt( sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem( 'hszHzlbUpdate' , x );
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/shyz.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        },
        isOverClick: function (isOver) {
          this.isOver = isOver || !this.isOver;
            this.yzshInfoList.forEach(function (br) {
                br.isCheckAll = shyz.isOver;
                br.yzxx.forEach(function (yz) {
                    yz.isChecked = shyz.isOver;
                });
            });
        },
        moun: function () {
            this.brlistjson=JSON.parse( sessionStorage.getItem( 'shyz' ) );
            this.brList =this.brlistjson.brlist;
            this.ksid=this.brlistjson.ksid;
            this.caqxContent=this.brlistjson.csqx;
            for(var i=0;i<this.brList.length;i++){
                var zyh={
                    zyh:this.brList[i].zyh
                };
                this.zyhs.push(zyh);
            }
            this.initShData();
     	
        },
    	  checkSelectSh:function (brIndex,yzIndex) {
              var yzStatus = !this.yzshInfoList[ brIndex ].yzxx[ yzIndex ].isChecked;
              this.yzshInfoList[ brIndex ].yzxx[ yzIndex ].isChecked = yzStatus;
              if( yzStatus ){

                  var yzIsOverCk = true;
                  for ( var x = 0; x < this.yzshInfoList[ brIndex ].yzxx.length; x++ ){
                      if( !this.yzshInfoList[ brIndex ].yzxx[ x ].isChecked ){
                          yzIsOverCk = false;
                          break;
                      }
                  }
                  this.yzshInfoList[ brIndex ].isCheckAll = yzIsOverCk;

                  var isOverCk = true;
                  for ( var x = 0; x < this.yzshInfoList.length; x++ ){
                      if( !this.yzshInfoList[x].isCheckAll ){
                          isOverCk = false;
                          break;
                      }
                  }
                  this.isOver = isOverCk;
              }else {
                  this.yzshInfoList[ brIndex ].isCheckAll = false;
                  this.isOver = false;
              }
          },
    	  reCheckBoxSh: function () {
    		  if( arguments.length == 1 ){
    		      var isCheckAll = this.yzshInfoList[arguments[0]].isCheckAll? false:true,
                      yzshInfo = this.yzshInfoList[arguments[0]],
                      yzxxList = yzshInfo.yzxx;

                  this.yzshInfoList[arguments[0]].isCheckAll = isCheckAll;
                  for ( var i = 0; i < yzxxList.length; i++ ){
                      this.yzshInfoList[arguments[0]].yzxx[i].isChecked = isCheckAll;
                  }
              }else if(arguments.length == 2){
                  this.activeBrListIndex = arguments[0];
                  this.activeIndex = arguments[1];
                  // var isChecked = this.yzshInfoList[arguments[0]].yzxx[arguments[1]].isChecked? false:true,
                  //     yzshInfo = this.yzshInfoList[arguments[0]],
                  //     yzxxList = yzshInfo.yzxx,
                  //     isCheckAll = true;
                  //
                  // this.yzshInfoList[arguments[0]].yzxx[arguments[1]].isChecked = isChecked;
                  // for ( var y = 0; y < yzxxList.length; y++ ){
                  //     if( !yzxxList[y].isChecked ){
                  //         this.yzshInfoList[arguments[0]].isCheckAll = false;
                  //         isCheckAll = false;
                  //         break;
                  //     }
                  // }
                  // if( isCheckAll ) this.yzshInfoList[arguments[0]].isCheckAll = true;
              }

              var isOverCk = true;
              for ( var x = 0; x < this.yzshInfoList.length; x++ ){
                  if( !this.yzshInfoList[x].isCheckAll ){
                      isOverCk = false;
                      break;
                  }
              }
              this.isOver = isOverCk;

              this.$forceUpdate();
          },
          
          //单个医嘱全选
//          checkYZAll: function (types, object, numb) {
//              if (this.isCheckAll) {
//                  var obj = object[numb].yzxx;
//                  for (var i = 0; i < obj.length; i++) {
//                      var check = types + "_" + numb + "_" + i;
//                      this.isChecked[check] = true;
//                  }
//              } else {
//                  this.isChecked = [];
//              }
//          },
        saveOne:function(){
        	
        },
        shenhe: function () {
        	  for (var i = 0; i < shyz.yzshInfoList.length; i++) {
            	  if(shyz.yzshInfoList[i].rycwbh==null){
            		  malert("病人："+shyz.yzshInfoList[i].brxm+",住院号："+shyz.yzshInfoList[i].zyh+"未接科安床，无法完成操作！", 'top', 'defeadted');
            		  return
            	  }
              }
            tspop.open();
        },
        showIndex:function(index){
        	console.log(index);
        },
        //获取审核医嘱信息
        initShData:function(){
        	  this.yzshInfoList = [];
              if (this.zyhs.length == 0) {
                  malert("请选择病人后再进行此操作！", 'top', 'defeadted');
                  return
              }
              if(this.ksid==null){
            	  malert("科室编码不能为空！", 'top', 'defeadted');
            	  return
              }
              var zyh = JSON.stringify(this.zyhs);
              $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=yzsh&ksbm=' + this.ksid + '&zyh=' + zyh,
                  function (json) {
                      if (json.d.list.length > 0) {
                          for (var i = 0; i < json.d.list.length; i++) {
                              for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                  json.d.list[i].yzxx[int].no = i;
                              }
                          }
                      }
                      var list = json.d.list;
                      list.forEach(function (item,index) {
                          var yzList = item.yzxx,
                              length = yzList.length;
                          if( length > 1 ){
                              for( var i = 0; i < length; i++ ){
                                  if( yzList[i].fzh != 0 ){
                                      if( i == 0 ){ // 第一个
                                          if( yzList[i].fzh == yzList[i+1].fzh && yzList[i].yzxh == yzList[i+1].yzxh ){
                                              yzList[i]['tzbj'] = 'tz-start';
                                          }
                                      }else if( i == length - 1 ){ // 最后一个
                                          if( yzList[i].fzh == yzList[i-1].fzh && yzList[i].yzxh == yzList[i-1].yzxh ){
                                              yzList[i]['tzbj'] = 'tz-stop';
                                          }
                                      }else{
                                          if( (yzList[i].fzh != yzList[i-1].fzh || yzList[i].yzxh != yzList[i-1].yzxh) && ( yzList[i].fzh == yzList[i+1].fzh && yzList[i].yzxh == yzList[i+1].yzxh ) ){
                                              yzList[i]['tzbj'] = 'tz-start';
                                          }else if( yzList[i].fzh == yzList[i-1].fzh && yzList[i].yzxh == yzList[i-1].yzxh && yzList[i].fzh == yzList[i+1].fzh && yzList[i].yzxh == yzList[i+1].yzxh ){
                                              yzList[i]['tzbj'] = 'tz-center';
                                          }else if( (yzList[i].fzh == yzList[i-1].fzh && yzList[i].yzxh == yzList[i-1].yzxh) && ( yzList[i].fzh != yzList[i+1].fzh || yzList[i].yzxh != yzList[i+1].yzxh ) ){
                                              yzList[i]['tzbj'] = 'tz-stop';
                                          }
                                      }
                                  }
                              }
                          }
                      });
                      shyz.yzshInfoList = list;
                  	for (var k = 0; k < shyz.yzshInfoList.length; k++) {
                    	//判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                    	if(shyz.yzshInfoList[k].nl<7&&this.brList[k].brxb=='1'){
                    		shyz.yzshInfoList[k].nljd='1';
                    	}else if(shyz.yzshInfoList[k].nl<7&&shyz.yzshInfoList[k].brxb=='2'){
                    		shyz.yzshInfoList[k].nljd='2';
                    	}else if(shyz.yzshInfoList[k].nl<18&&shyz.yzshInfoList[k].nl>6&&shyz.yzshInfoList[k].brxb=='1'){
                    		shyz.yzshInfoList[k].nljd='3';
                    	}else if(shyz.yzshInfoList[k].nl<18&&shyz.yzshInfoList[k].nl>6&&shyz.yzshInfoList[k].brxb=='2'){
                    		shyz.yzshInfoList[k].nljd='4';
                    	}else if(shyz.yzshInfoList[k].nl<41&&shyz.yzshInfoList[k].nl>17&&shyz.yzshInfoList[k].brxb=='1'){
                    		shyz.yzshInfoList[k].nljd='5';
                    	}else if(shyz.yzshInfoList[k].nl<41&&shyz.yzshInfoList[k].nl>17&&shyz.yzshInfoList[k].brxb=='2'){
                    		shyz.yzshInfoList[k].nljd='6';
                    	}else if(shyz.yzshInfoList[k].nl<66&&shyz.yzshInfoList[k].nl>40&&shyz.yzshInfoList[k].brxb=='1'){
                    		shyz.yzshInfoList[k].nljd='7';
                    	}else if(shyz.yzshInfoList[k].nl<66&&shyz.yzshInfoList[k].nl>40&&shyz.yzshInfoList[k].brxb=='2'){
                    		shyz.yzshInfoList[k].nljd='8';
                    	}else if(shyz.yzshInfoList[k].nl>65&&shyz.yzshInfoList[k].brxb=='1'){
                    		shyz.yzshInfoList[k].nljd='9';
                    	}else if(shyz.yzshInfoList[k].nl>65&&shyz.yzshInfoList[k].brxb=='2'){
                    		shyz.yzshInfoList[k].nljd='10';
                    	}else{
                            shyz.yzshInfoList[k].nljd='11';
                        }
                    }
//                      YZInfo.jsonList = YZInfo.yzshInfoList;
                      if( shyz.yzshInfoList.length > 1 ){
                  	    shyz.isOverClick(true);
                      }
                  }, function (error) {
                      console.log(error);
                  });
        },
    },
});

var tspop = new Vue({
    el: '#tspop',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        ifClick: true, //判断是否点击了结算按钮
        shsj:'',//审核时间
    },
    mounted: function(){
    	 this.shsj =this.fDate(new Date(),'date')+' '+this.fDate(new Date(),'times');
    	 console.log(this.shsj);
       	 laydate.render({
             elem: '#timeVal',
             type: 'datetime',
             rigger: 'click',
             theme: '#1ab394',
             done: function (value, data) { //回调方法
             	if (value != '') {
             		tspop.shsj = value;
                 } else {
                	tspop.shsj = '';
                 }
             }
        });
    },
    methods: {
        //关闭
        closes: function () {
            $(this.$refs.pophide).animate({
                opacity: 0,
            }, function () {
                $('#tspop').hide()
            })
        },
        open: function () {
            $(this.$refs.pophide).animate({
                opacity: 1,
            }, function () {
                $('#tspop').show()
            })
        },
        //确认审核
        oksh: function () {
            if (!tspop.ifClick) return; //如果为false表示已经点击了不能再点
            tspop.ifClick = false;
        	  var shyzData=[];
              for (var i = 0; i < shyz.yzshInfoList.length; i++) {
            	  if(shyz.yzshInfoList[i].rycwbh==null){
            		  malert("病人："+shyz.yzshInfoList[i].brxm+",住院号："+shyz.yzshInfoList[i].zyh+"未接科安床，无法完成操作！");
            		  return
            	  }
                  for (var j = 0; j < shyz.yzshInfoList[i]['yzxx'].length; j++) {
                      if (shyz.yzshInfoList[i]['yzxx'][j].isChecked) {
                    	  shyzData.push({"xhid": shyz.yzshInfoList[i]['yzxx'][j].xhid});
                      }
                  }
              }
              if (shyzData.length <= 0) {
                  malert("无医嘱审核！", 'top', 'defeadted');
                  tspop.ifClick = true;
                  return;
              }
              //console.log("yzsh:"+JSON.stringify({list:this.auditingData}));
              this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzsh&ksbm=' + shyz.ksid,
                  JSON.stringify({list: shyzData})).then(function (data) {
                  if (data.body.a == 0) {
                      malert("审核成功", 'top', 'success');
                      tspop.jrcy();
                      tspop.ifClick = true;
                      $(this.$refs.tspop).fadeOut(600);
                      shyz.initShData();//刷新
                      var x = parseInt( sessionStorage.getItem('hszHzlbUpdate'));
                      x++;
                      sessionStorage.setItem( 'hszHzlbUpdate' , x );
                      // if( this.yzshInfoList.length > 1 ){ //单个执行
                      //     var brjson={
                      //         brlist:this.yzshInfoList[0],
                      //         ksid:hzlb.pageState.ks,
                      //         csqx:hzlb.caqxContent,
                      //     }
                      //     sessionStorage.setItem( 'zxyz', JSON.stringify(brjson) );
                      //     this.topNewPage('执行医嘱','page/hsz/hlyw/yzcl/loading-page/zxyz.html');
                      // }else { // 批量执行
                              var brjson= {
                                  brlist: shyz.brList,
                                  ksid: shyz.ksid,
                                  csqx: shyz.caqxContent,
                              };
                      sessionStorage.setItem( 'zxyz', JSON.stringify(brjson) );
                              this.topNewPage('批量执行医嘱','page/hsz/hlyw/yzcl/loading-page/zxyz.html');

                      // }
                  } else {
                      malert("审核失败", 'top', 'defeadted');
                      tspop.ifClick = true;
                  }
              }, function (error) {
                  console.log(error);
              });
        },

        //针对今日出院审核之后进行批量更新医嘱操作
        jrcy: function(){
             //判断是否需要停掉之前所有未停医嘱
            for (var i = 0; i < shyz.yzshInfoList.length; i++) {
                var tzyz = false;//针对医嘱类型是否停嘱
                for (var j = 0; j < shyz.yzshInfoList[i]['yzxx'].length; j++) {
                    if (shyz.yzshInfoList[i]['yzxx'][j].isChecked) {
                        if (shyz.yzshInfoList[i]['yzxx'][j].yzfl == '3' || shyz.yzshInfoList[i]['yzxx'][j].yzfl == '4' || shyz.yzshInfoList[i]['yzxx'][j].yzfl == '5') {
                            tzyz = true;

                        }
                    }
                }
                //执行停止所有未停医嘱
                if (tzyz) {
                    var wtyzList = [];
                    var ksbm = shyz.yzshInfoList[i].ryks;   //科室
                    var zyh = shyz.yzshInfoList[i].zyh;  //住院号
                    var yzlx = '';
                    var ystzbz = '0';
                    var parm_ksbm = {
                        ksbm: ksbm,
                        yzlx: yzlx,
                        ystzbz: ystzbz,
                    };
                    var parm_zyh = [{
                        zyh: zyh
                    }];
                    $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzxx&parm=' + JSON.stringify(parm_ksbm) + '&zyh=' + JSON.stringify(parm_zyh)
                        , function (json) {
                            if (json.a == '0') {
                                wtyzList = json.d.list;
                                if (wtyzList.length < 1) {
//                    	            break;
                                } else {
                                    var fhwtyzList=[];
                                    for(var k=0;k<wtyzList.length;k++){
                                        if (wtyzList[k].shbz == '1') {
                                            fhwtyzList.push(wtyzList[k]);
                                        } else {
                                            malert(wtyzList[k].xmmc+"医嘱未审核，请直接作废或进行审核！");
                                        }
                                    }
                                    if(fhwtyzList.length>0) {
                                        var tzjson = {
                                            list: [
                                                {
                                                    yzxx: fhwtyzList,          //医嘱信息
                                                    lczd: []              //诊断信息（检查，检验的诊断）
                                                }
                                            ]
                                        };
                                        tspop.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzstop', JSON.stringify(tzjson)).then(function (data) {
                                            if (data.body.a == 0) {
                                                malert("医嘱停止申请成功", 'top', 'success');
                                            } else {
                                                malert("医嘱停止申请失败：" + data.body.c, 'top', 'defeadted');
                                            }
                                        });
                                    }
                                }
                            }
                        });
                }

            }

        },
    }
});