var qxksbm = '';
var rksh = new Vue({
    el: '.zui-table-view',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data: {
        //打印数据
        printData: {},
        isShowkd: true,
        isShow: false,
        rkdList: [], //入库单集合
        jsonList: [],
        json: {},
        csParm: {},
        ryList: [],
        zdy: userId,
        bsdContent: {
            ckfs: "03", //报损方式
            lyr: userId //操作人
        },
        bsdList: [], //入库单集合
        tkdDetail: [],
        rkd: null, //入库单对象
        TjShow: true,
        ShShow: false,
        mxShShow: true,
        zfShow: true,
        dyShow: false,
        thdList: [], //退货单集合
        param: {
            page: 1,
            rows: 10,
            sort: "",
            order: "asc",
            parm: "",
            beginrq: null,
            endrq: null
        },
        isCheck: null,
        thdDetail: [], //退货单明细集合
        dateBegin: null,//getTodayDateBegin(),
        dateEnd: null,//getTodayDateEnd(),
        time: {
            test: 'hello!'
        },
        t: {},
        zhuangtai: {
            "0": "待审核",
            "1": "已审核",
            "2": "作废",
        },
        totlePage: '',
    },
    updated: function () {
        changeWin()
    },
    methods: {
        loadNum: function () {
            this.num = wrapper.num;
        },
        //判断是否有操作权限
        hasCx: function (cx) {
            if (!cx) {
                malert("用户没有操作权限！", 'top', 'defeadted');
                return true;
            }
        },
        //进入页面加载单据列表信息
        getData: function () {
            common.openloading('.zui-table-view');
            rksh.rkd = null;
            //清空退货明细信息
            rksh.thdDetail = [];
            //拼接参数对象
            this.dateBegin = rksh.param.beginrq;
            this.dateEnd = rksh.param.endrq;

            var parm = {
                yfbm: wrapper.yfbm,
                cklx: '02',
                beginrq: this.dateBegin,
                endrq: this.dateEnd,
                parm: wrapper.search,
                page: this.param.page,
                rows: this.param.rows
            };

            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglBsgl&types=bsdcx' +
                '&parm=' + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        rksh.bsdList = data.d.list;
                        rksh.totlePage = Math.ceil(data.d.total / rksh.param.rows);
                    } else {
                        malert(data.c,'top','defeadted');
                    }
                });
            common.closeLoading()
        },

        //审核传值
        passData: function () {
            if (!wrapper.yfbm) {
                malert("请先择二级库房!",'top','defeadted');
                return;
            }
            var json = {
                "ckdh": this.bsdList[this.isCheck]['ckdh']
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglBsgl&types=shbsd&yfbm=' + wrapper.yfbm,
                JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == "0") {
                        //打印数据
                        rksh.cancel();
                        malert("报损单审核成功！",'top','success');
                        this.printDJ();
                    } else {
                        malert(data.body.c,'top','defeadted');
                    }
                    this.savebz = true;
                });
        },
        //作废退库单2018/07/06二次弹窗作废提示
        invalidData: function (num) {
            if (num != null && num != undefined) {
                this.isCheck = num;
            }

            //出库单非空判断
            if (this.bsdList.length == 0) {
                return;
            }
            if (common.openConfirm("确认作废该条信息吗？", function () {
                var json = {
                    "ckdh": rksh.bsdList[rksh.isCheck]['ckdh'],
                    "yfbm": wrapper.yfbm
                };
                rksh.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglBsgl&types=zfbsd', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == "0") {
                        rksh.getData();
                        malert("作废成功！", 'top', 'success');
                        rksh.cancel();
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
            })) {
            }
        },
        editIndex: function (index) {
            this.isCheck = index;
            wrapper.isShow = true;
            wrapper.isShowkd = false;
            wrapper.isShowpopL = true;
            rksh.isShow = true;
            rksh.isShowkd = false;
            rksh.ShShow = false;
            rksh.TjShow = true;
            rksh.rkd = rksh.bsdList[index];
            rksh.zfShow = true;
            rksh.mxShShow = true;
            wrapper.jyinput = false;
            wrapper.zdyxm = this.bsdList[index].zdrxm;
            wrapper.zdrq = rksh.fDate(this.bsdList[index].zdrq, 'date');
            wrapper.yfbm = this.bsdList[index].yfbm;
            wrapper.bzms = this.bsdList[index].bzms;
            var ckdh = {
                ckdh: rksh.bsdList[index]['ckdh']
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglBsgl&types=bsdmxcx' +
                '&parm=' + JSON.stringify(ckdh),
                function (data) {
                    if (data.a = "0") {
                        rksh.jsonList = data.d.list;
                    } else {
                        malert(data.c,'top','defeadted');
                    }

                });
        },
        //显示退库单细节
        showDetail: function (index, item) {
            wrapper.ckdh = this.bsdList[index].ckdh;
            this.isCheck = index;
            wrapper.isShow = true;
            wrapper.isShowkd = false;
            wrapper.isShowpopL = false;
            rksh.isShow = true;
            rksh.isShowkd = false;
            this.TjShow = false;
            wrapper.TjShow = false;
            rksh.zfShow = false;
            //根据状态判断
            if (this.bsdList[index].shzfbz == 0) {
                this.ShShow = true;
                this.mxShShow = true;
                wrapper.jyinput = false;
            } else {
                this.ShShow = false;
                wrapper.jyinput = true;
                this.mxShShow = false;
                rksh.dyShow = true;
            }
            wrapper.zdyxm = this.bsdList[index].zdrxm;
            wrapper.zdrq = rksh.fDate(this.bsdList[index].zdrq, 'date');
            wrapper.yfbm = this.bsdList[index].yfbm;
            wrapper.bzms = this.bsdList[index].bzms;
            var ckdh = {
                ckdh: rksh.bsdList[index]['ckdh']
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglBsgl&types=bsdmxcx' +
                '&parm=' + JSON.stringify(ckdh),
                function (data) {
                    if (data.a = "0") {
                        rksh.jsonList = data.d.list;
                    } else {
                        malert(data.c,'top','defeadted');
                    }

                });
        },


        //提交所有材料
        submitAll: function () {
            //是否禁止提交
            if (this.isSubmited) {
                malert("数据提交中，请稍候!",'top','success');
                return;
            }
            if (wrapper.yfbm == null) {
                malert("二级库房为必选项",'top','defeadted');
                return
            }
            if (this.jsonList.length <= 0) {
                malert("请录入报损出库材料明细!",'top','defeadted');
                return;
            }
            //是否禁止提交
            this.isSubmited = true;
            var json = {
                "list": this.jsonList
            };

            //备注描述
            var bzms = wrapper.bzms;
            if (bzms == undefined || bzms == null || bzms == "") {
                bzms = "报损出库";
            }
            var bsd = null;
            if (!rksh.rkd) {
                bsd = {
                    "yfbm": wrapper.yfbm,
                    "bzms": bzms,
                    "jbr": wap.bsdContent.jbr,
                };
            } else {
                bsd = rksh.rkd;
                Vue.set(bsd, 'yfbm', wrapper.yfbm);
                Vue.set(bsd, 'bzms', bzms);
                Vue.set(bsd, 'jbr', wap.bsdContent.jbr);
            }
            var json = {
                "list": {
                    "ckd": bsd,
                    "ckdmx": this.jsonList
                }
            };

            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglBsgl&types=modify',
                JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == 0) {
                        wap.jsonList = [];
                        malert("报损单保存成功",'top','success');
                        rksh.cancel();
                        rksh.getData();
                    } else {
                        malert(data.c,'top','defeadted');
                    }
                    //是否禁止提交
                    this.isSubmited = false;
                }, function (error) {
                    console.log(error);
                    //是否禁止提交
                    this.isSubmited = false;
                });
        },
        //双击修改
        edit: function (num) {

            if (num == null) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if (num == null) {
                    malert("请选中你要修改的数据",'top','defeadted');
                    return false;
                }
            }
            wrapper.isUpdate = 1;
            wap.modifyIndex = num;
            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            Vue.set(wap.bsdContent, 'jbr', rksh.rkd.zdr);
            rksh.getPckc(wap.popContent.ypbm);
            wap.title = "编辑材料";
            wap.open();
            console.log(rksh.jsonList[num]);
        },
        //删除2018/07/06二次弹窗删除提示
        scmx: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                this.jsonList.splice(index, 1);
            })) {
                return false;
            }
            // this.jsonList.splice(index,1);
        },
        //库存管理
        getPckc: function (ypbm) {
            var parm = {
                yfbm: wrapper.yfbm,
                ypbm: ypbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc' + '&json=' + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        Vue.set(wap.popContent, 'kcsl', data.d.list[0].sjkc);
                    } else {
                        malert(data.c,'top','defeadted');
                    }
                });
        },
        //取消
        cancel: function () {
            rksh.getData();
            wrapper.isShow = false;
            wrapper.isShowkd = true;
            wrapper.isShowpopL = false;
            rksh.isShow = false;
            rksh.isShowkd = true;
        },
        print: function () {
            // 查询打印模板
            var json = {
                repname: '二级库房出库单'
            };
            var rows = rksh.printData['dj']['rows'];
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                var total = Math.ceil(rksh.printData['djmx'].length / rows);
                // 根据每页行数循环打印
                for (var i = 0; i < Math.ceil(rksh.printData['djmx'].length / rksh.printData['dj']['rows']); i++) {
                    // 清除打印区域
                    rksh.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    rksh.drawList = JSON.parse(json.d[0]['canvas']);
                    rksh.creatCanvas();
                    rksh.reDraw();
                    var list = [];
                    var jjhz = 0;
                    var ljhz = 0;
                    for (var j = 0; j < rows; j++) {
                        if (rksh.printData['djmx'][i * rows + j] == null) break;
                        list.push(rksh.printData['djmx'][i * rows + j]);
                        jjhz += rksh.printData['djmx'][i * rows + j]['ypjjje'];
                        ljhz += rksh.printData['djmx'][i * rows + j]['ypljje'];
                    }
                    // 为打印前生成数据
                    rksh.printData['dj']['total'] = total;
                    rksh.printData['dj']['page'] = i + 1;
                    rksh.printData['dj']['jjxj'] = parseFloat(jjhz).toFixed(2);
                    rksh.printData['dj']['ljxj'] = parseFloat(ljhz).toFixed(2);
                    rksh.printContent(rksh.printData['dj']);
                    rksh.printTrend(list);
                    // 开始打印
                    window.print();
                }
            });
        },
        //打印
        printDJ: function () {
            var ckdh = rksh.bsdList[this.isCheck]['ckdh'];
            var yfbm = wrapper.yfbm;
            var json = {
                ckdh: ckdh,
                yfbm: yfbm,
            };

            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglBsgl&types=print' +
                '&parm=' + JSON.stringify(json),
                function (data) {
                    rksh.printData = data.d;
                    var zdrq = data.d.dj.zdrq;
                    var djmx = data.d.djmx;
                    rksh.printData.dj.zdrq = rksh.fDate(zdrq, 'date');
                    for (var i = 0; i < djmx.length; i++) {
                        var yxqz = djmx[i].yxqz;
                        rksh.printData.djmx[i]['yxqz'] = rksh.fDate(yxqz, 'date');
                        djmx[i].ypjjje = parseFloat(rksh.fDec(djmx[i].ypjjje, 2));
                        djmx[i].ypljje = parseFloat(rksh.fDec(djmx[i].ypljje, 2));
                    }
                    console.log(rksh.printData);
                    // 开始打印
                    var reportlets = "[{reportlet: 'fpdy%2Fejkf%2Fyfgl_ckd.cpt',yljgbm:'" + jgbm + "',yfbm:'" + yfbm + "',ckdh:'" + ckdh + "'}]";
                    if (!FrPrint(reportlets, null)) {
                        rksh.print();
                    }
                });
        },

    }
});

var wrapper = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        isShowpopL: false,
        isTabelShow: false,
        isShowkd: true,
        isShow: false,
        keyWord: '',
        csParm: {},
        zdy: userId,
        TjShow: true,
        zdyxm: '',
        zdrq: getTodayDateTime(), //获取制单日期
        jyinput: false, //禁用输入框
        bsdContent: {
            ckfs: "03", //报损方式
            lyr: userId,//操作人
        },
        jsonList: [],
        yfbm: null,
        rybm: null,
        bzms: null,
        ckdh: '',
        yfList: [], //二级库房
        title: '',
        totle: '',
        num: 0,
        param: {
            page: '',
            rows: '',
            total: ''
        },
        rkd: {},
        isUpdate: 0,
        search: '',
    },
    mounted: function () {
        var myDate = new Date();
        rksh.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        rksh.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: rksh.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                rksh.param.beginrq = value;
                rksh.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: rksh.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                rksh.param.endrq = value;
                rksh.getData();
            }
        });
    },
    watch: {
        //二级库房变化获取参数权限
        yfbm: function () {
            for (var i = 0; i < wrapper.yfList.length; i++) {
                if (wrapper.yfList[i].yfbm == this.yfbm) {
                    qxksbm = wrapper.yfList[i].ksbm;
                    wap.getCsqx();
                }
            }


        },
    },
    methods: {
        kd: function (index) {
            this.num = index;
            rksh.loadNum();
            setTimeout(function () {
                wap.$refs.autofocus.$refs.inputFu.focus();
            }, 40);
            switch (wrapper.num) {
                case 0:
                    wrapper.isUpdate = 0;
                    rksh.jsonList = [];
                    this.isShowkd = false;
                    this.isShow = true;
                    this.isShowpopL = true;
                    rksh.isShow = true;
                    rksh.isShowkd = false;
                    rksh.TjShow = true;
                    rksh.ShShow = false;
                    rksh.zfShow = false;
                    rksh.mxShShow = true;
                    wrapper.jyinput = false;
                    var reg = /^[\'\"]+|[\'\"]+$/g;
                    wrapper.zdyxm = sessionStorage.getItem("userName" + userId).replace(reg, '');

                    break;
                case 1:
                    wrapper.isUpdate = 0;
                    wap.open();
                    wap.title = '添加材料';
                    wap.popContent = {};
                    break;
            }

        },
        searchHc: function () {
            rksh.param.page = 1
            rksh.getData();
        },
        getKFData: function () {
            //下拉框获取库房
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm={"ylbm":"N040100021002"}',
                function (data) {
                    if (data.a == 0) {
                        wrapper.yfList = data.d.list;
                        Vue.set(wrapper, 'yfbm', wrapper.yfList[0].yfbm);
                        qxksbm = wrapper.yfList[0].ksbm;
                        wap.getCsqx();
                    } else {
                        malert("二级库房获取失败", 'top', 'defeadted');
                    }
                });
        },


    }
});


var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        rybm: '',
        isShowpopL: false,
        iShow: false,
        isTabelShow: false,
        flag: false,
        jsShow: false,
        title: '',
        zdy: userId,
        ryList: [],
        added: false,
        lyks: null,
        tkyf: null,
        tkyfBM: null,
        zdrq: getTodayDateTime(), //获取制单日期
        bsdContent: {
            ckfs: "03", //报损方式
            lyr: userId //操作人
        },
        cgryList: [], //退库人员
        KSList: [],
        KFList: [],
        lyr: null,
        bsdList: [], //入库单集合
        ghdwList: [],
        popContents: {},
        //材料信息对象
        popContent: {},
        //参数权限对象
        csParm: {},
        dg: {
            page: 1,
            rows: 1,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '生产批号': 'scph',
            '材料编号': 'ypbm',
            '材料名称': 'ypmc',
            '库存数量': 'ykkc',
            '有效期至': 'yxqz',
            '规格': 'ypgg',
            '分装比例': 'fzbl',
            '进价': 'ykjj',
            '零价': 'yklj',
            '库房单位': 'kfdwmc',
            '二级库房单位': 'yfdwmc',
            '效期': 'yxqz',
            '材料剂型': 'jxmc'
        },
        modifyIndex: '',//待修改详情index
    },
    mounted: function () {
        // this.getJzData();
    },
    methods: {
        nextF: function () {
            this.addData();
        },
        result: function (val) {
            Vue.set(this.bsdContent, 'jbr', val[0])
        },
        //关闭
        closes: function () {
            $(".side-form").removeClass('side-form-bg');
            $(".side-form").addClass('ng-hide');

        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg');
            $(".side-form").removeClass('ng-hide');
        },

        //确定
        confirms: function () {
            wap.addData();
            this.closes();
        },
        // getJzData:function () {
        //     //初始化页面记载供货单位
        //     this.dg.rows = 200;
        //     this.dg.sort = 'dwbm';
        //     this.dg.tybz = '0';
        //     $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(this.dg),
        //         function (json) {
        //             if (json.a == 0) {
        //                 wap.ghdwList = json.d.list;
        //             } else {
        //                 malert("供货单位获取失败",'top','defeadted');
        //             }
        //         });
        // },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            $.ajaxSetup({
                async: false
            });

            var parm = {
                "ksbm": qxksbm,
                "ylbm": "N040030020022005"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        //二级库房报损开单权限
                        for (var i = 0; i < json.d.length; i++) {
                            if (json.d[i].csqxbm == 'N04003002002200501') {
                                if (json.d[i].csz == '1') {
                                    wap.csParm.kd = true;
                                } else {
                                    wap.csParm.kd = false;
                                }
                            }
                            //二级库房报损审核权限
                            if (json.d[i].csqxbm == 'N04003002002200502') {
                                if (json.d[i].csz == '1') {
                                    wap.csParm.sh = true;
                                } else {
                                    wap.csParm.sh = false;
                                }

                            }
                        }
                    }
                    rksh.getData();
                } else {
                    malert(json.c+ "参数权限获取失败",'top','defeadted');
                }
                //设置ajax为异步
                $.ajaxSetup({
                    async: true
                });
            });
        },
        //

        //材料名称下拉table检索数据
        changeDown: function (event, type) {
            if (type == "cksl" && window.event.keyCode == 13) {
                //添加数据到提交区
                this.addData();
                //清空数据录入区
                wap.popContent = {};
                //wap.clearData();
            }
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13 && isReq) {
                if (type == "ypmc") {
                    this.nextFocus(event);
                }
            }
        },
        //当输入值后才触发
        change: function (event, type, val) {
            this.popContent[type] = val;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);

            if (type == "ypmc") { //材料下拉框
                wap.dg.page = 1;
                wap.dg.rows = 5;
                wap.dg.parm = this.popContent[type];
                wap.dg.sort = "ypbm";
                var json = {};
                json.yfbm = wrapper.yfbm;
                // json.ksbm = wrapper.ksbm;
                json.ckbz = "bs"; //出库不过虑过期材料
                json.sfpdkcsl = 1; //只查询库存>0的材料
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc' +
                    '&dg=' + JSON.stringify(wap.dg) + '&json=' + JSON.stringify(json),
                    function (data) {
                        if (data.a == 0) {
                            wap.searchCon = data.d.list;
                            wap.total = data.d.total;
                            wap.selSearch = 0;
                            if (data.d.list.length != 0) {
                                $(".selectGroup").hide();
                                _searchEvent.show()
                            } else {
                                $(".selectGroup").hide();
                            }
                        } else {
                            malert(data.c,'top','defeadted');
                        }

                    });
            }

        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                var parm = {};
                parm.yfbm = wrapper.yfbm;
                parm.ckbz = "bs"; //出库不过虑过期材料
                parm.sfpdkcsl = 1; //只查询库存>0的材料
                wap.dg.page++;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc' +
                    '&dg=' + JSON.stringify(wap.dg) + '&json=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                wap.searchCon.push(data.d.list[i]);
                            }
                            wap.total = data.d.total;
                            wap.selSearch = 0;
                        } else {
                            malert('分页信息获取失败', 'top', 'defeadted')
                        }

                    });
                return;
            }

            this.popContent = item;
            $(".selectGroup").hide();
        },

        //添加入库信息
        addData: function () {
            //非空判断
            //判断权限
            if (wrapper.yfbm == null || wrapper.yfbm == undefined) {
                malert('请选择二级库房', 'top', 'defeadted');
                return;
            }
            if (wap.bsdContent.jbr == null || wap.bsdContent.jbr == undefined) {
                malert('请选择经办人', 'top', 'defeadted');
                return;
            }

            if (wap.popContent.ypmc == null || wap.popContent.ypmc == undefined) {
                malert('请输入材料名称', 'top', 'defeadted');
                return;
            }
            if (wap.popContent.cksl == null || wap.popContent.cksl == undefined || wap.popContent.cksl <= 0) {
                malert('出库数量不正确', 'top', 'defeadted');
                return;
            }
            this.popContent['yxqz'] = $('#yxqz').val();
            this.popContent['scrq'] = $('#scrq').val();
            var haveError = false;

            if (haveError) {
                malert("录入区数据不完整", 'top', 'defeadted');
                return false;
            }
            //判断可用库存数量是否大于0（同时判断未审核库存数）
            if (this.popContent.kcsl <= 0 || (this.popContent.kcsl) < this.popContent.cksl) {
                var msg = "可用库存不足" + (this.popContent['kcsl'] == 0 ? "!" : "，请检查未审核的出库！");
                malert(msg, 'top', 'defeadted');
                return false;
            }
            if (wrapper.isUpdate == 0) {
                //添加
                var ypbm = wap.popContent.ypbm;
                var xtph = wap.popContent.xtph;
                //判断数据是否重复
                if (rksh.jsonList.length > 0) {
                    for (var i = 0; i < rksh.jsonList.length; i++) {
                        if (rksh.jsonList[i].ypbm == this.popContent.ypbm && rksh.jsonList[i].xtph == this.popContent.xtph) {
                            malert('材料【' + this.popContent.ypmc + '】已添加，请双击修改！', 'top', 'defeadted');
                            return false;
                        }
                    }
                }
                //将数据加入列表
                rksh.jsonList.push(this.popContent);
                $("#ypmc").focus();
            }
            /*if(wap.title=='编辑材料'){
                wap.closes();
            }else{
            }*/
            if (wrapper.isUpdate == 1) {
                //修改
                var ckdh = rksh.jsonList[wap.modifyIndex].ckdh;
                rksh.$set(rksh.jsonList, wap.modifyIndex, wap.popContent);
                //rksh.$set(rksh.sldmxList[wrapper.modifyIndex],'sldh',sldh);
                malert("修改成功！", 'top', 'success');
                wap.closes();
            }

            var ypbm = wap.popContent.ypbm;
            var xtph = wap.popContent.xtph;
            //判断数据是否重复
            if (rksh.jsonList.length > 0) {
                for (var i = 0; i < rksh.jsonList.length - 1; i++) {
                    if (rksh.jsonList[i].ypbm == this.popContent.ypbm && rksh.jsonList[i].xtph == this.popContent.xtph) {
                        malert("材料【" + this.popContent.ypmc + "】已添加，请双击修改！", 'top', 'defeadted');
                        return false;
                    }

                }
            }
            $("#ypmc").focus();
            this.added = true;
            this.popContent = {};
        },
        zdlb: function () {
            //下拉框获取科室编码
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                function (data) {
                    if (data.a == 0) {
                        wap.KSList = data.d.list;
                    } else {
                        malert("科室获取失败!", 'top', 'defeadted');
                    }
                });
            //获取制单人列表
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                function (data) {
                    wap.ryList = data.d.list;
                });
        }

    }


});

//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
var saves = null;
wrapper.getKFData();
//rksh.getData();
wap.zdlb();
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

});





