<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>通用表格</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="zksj.css" charset="utf-8"/>
</head>
<body>
<div class=" background-f" >
    <div id="penal">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddNumber('top')">新增批号</button>
            <!--<button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="sc">删除</button>-->
            <button class="tong-btn btn-parmary-b icon-plyr padd-l28" @click="yr()">批量移入</button>
            <button class="tong-btn btn-parmary-b icon-plyc padd-l28" @click="yc()">批量移除</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">检验设备</label>
                    <div class="zui-select-inline margin-l13">
                            <select-input @change-data="selectdata" :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="jysbObj.sbbm" :search="true" :name="'jysbObj.sbbm'"></select-input>
                    </div>
                </div>
                <div class="zui-inline  margin-l14">
                    <label class="zui-form-label padd-l14">检索码</label>
                    <div class="zui-input-inline">
                        <input type="text" class="zui-input" @keydown.enter="getSbbm()" v-model="jysbObj.zbxm" name="input1" placeholder="请输入检索码" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="flex-container">
        <div id="left">
            <div class="zui-table-view padd-l-10 padd-r-10" >
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'zklistwx'" :type="'all'" :val="isCheckAll"></input-checkbox>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s">指标编码</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-l">指标名称</div>
                            </th>
                            <th  >
                                <div class="zui-table-cell cell-s">简称</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>

                    <div class=" zui-table-body" @scroll="scrollTable($event)"  role="treeitem">
                        <table class="zui-table" id="ice">
                            <tbody>
                            <tr v-if="zklist.wx" :class="[{'table-hovers':index===activeIndex,'table-hover':index === hoverIndex}]"
                                @mouseenter="switchIndex('hoverIndex',true,index)"
                                @mouseleave="switchIndex()"
                                @click="switchIndex('activeIndex',true,index)"  v-for="(list, index) in zklist.wx">
                                <td class="l-zksj-td cell-m"><input-checkbox @result="reCheckBox" :list="'zklist.wx'" :type="'some'" :which="index" :val="isChecked[index]"></input-checkbox></td>
                                <td><div class="zui-table-cell cell-s" v-text="list.zbxm">78</div></td>
                                <td><div class="zui-table-cell cell-l" v-text="list.zbmc">高密度脂蛋白</div></td>
                                <td><div class="zui-table-cell cell-s" v-text="list.ywmc">GMDZDB</div></td>
                            </tr>
                            </tbody>
                        </table>

                    </div>

            </div>
        </div>
        <div id="right" class="  flex-container">
            <div  class="padd-r-10 zui-table-view " >
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th >
                                <input-checkbox @result="reCheckBoxs" :list="'zklistyx'" :type="'all'" :val="isCheckAll"></input-checkbox>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-l">指标名称</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cells-s">默认质控批号</div>
                            </th>
                            <th  >
                                <div class="zui-table-cell cell-s">简称</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s">执行设备</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s">操作</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class=" zui-table-body" @scroll="scrollTable($event)" role="treeitem">
                    <table class="zui-table">
                        <tbody>
                        <tr v-if="zklist.yx"  :class="[{'table-hovers':index===activeIndex1,'table-hover':index === hoverIndex1}]"
                            @mouseenter="switchIndex('hoverIndex1',true,index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex1',true,index),ck(item)" v-for="(item, index) in zklist.yx"  >
                            <td><input-checkbox @result="reCheckBoxs" :list="'zklist.yx'" :type="'some'" :which="index" :val="isCheckeds[index]"></input-checkbox>
                            </td>
                            <td><div class="zui-table-cell cell-l" v-text="item.zbmc">78</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.zkwph">高密度脂蛋白</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.ywmc">高密度脂蛋白</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.sbbm">高密度脂蛋白</div></td>
                            <td><div class="zui-table-cell cell-s"><i class="icon-sc" @click="yc(item)"></i></div></td>
                        </tr>

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="zui-table-view "  >
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>

                            <th >
                                <div class="zui-table-cell cell-s">项目编码</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s">质控批号</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s">质控物产地</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s">有效期</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s">启用状态</div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s">操作</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class=" zui-table-body"  @scroll="scrollTable($event)" role="treeitem">
                    <table class="zui-table">
                        <tbody>
                        <tr @dblclick="edit(list,'center')" :class="[{'table-hovers':$index===activeIndex2,'table-hover':$index === hoverIndex2}]"
                            @mouseenter="switchIndex('hoverIndex2',true,$index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex2',true,$index)" v-for="list in childelist">
                            <td><div class="zui-table-cell cell-s" v-text="list.sbbm"></div></td>
                            <td><div class="zui-table-cell cell-s" v-text="list.zkwph">高密度脂蛋白</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="list.cd">高密度脂蛋白</div></td>
                            <td><div class="zui-table-cell cell-s">{{list.yxrq | formDate}}</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="list.qyzt == '1' ? '启用' : '未使用' ">高密度脂蛋白</div></td>
                            <!--<i class="icon-sc margin-l15"></i>-->
                            <td><div class="zui-table-cell cell-s"><i class="icon-bj" @click="edit(list,'center')"></i></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>


        </div>

    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-548"   id="brzcList" role="form">
    <div class="tab-message">
        <a v-text="title"></a>
        <a href="javascript:;" class="icon-cha" @click="AddClose"></a>
    </div>
    <div class="POP100 padd-t-10">
        <div class="flex-container flex-jus-sb padd-l-10  padd-r-10 flex-wrap-w">
            <div class="flex-container flex-align-c padd-b-15">
                <span class="ft-14 padd-r-5">批&emsp;&emsp;号</span>
                <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.zkwph" placeholder="请输入批号"/>
            </div>
            <div class="flex-container flex-align-c padd-b-15">
                <span class="ft-14 padd-r-5">产&emsp;&emsp;地</span>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35"  v-model="zk.cd" placeholder="请输入产地"/>
            </div>
            <div class="flex-container flex-align-c padd-b-15">
                <span class="ft-14 padd-r-5">检测设备</span>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35" disabled v-model="zk.sb" placeholder="请选择检测设备"/>
            </div>
            <div class="flex-container flex-align-c padd-b-15">
               <span class="ft-14 padd-r-5">检验方法</span>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.jyff" placeholder="请选择检验方法"/>
            </div>
            <div class="flex-container flex-align-c padd-b-15">
               <span class="ft-14 padd-r-5">单&emsp;&emsp;位</span>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35"  v-model="zk.dw" placeholder="请选择单位"/>
            </div>
            <div class="flex-container flex-align-c padd-b-15">
                <span class="ft-14 padd-r-5">有效日期</span>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35 l-time" id="ltime" v-model="zk.yxrq" placeholder="请选择日期"/>
            </div>
            <div class="flex-container flex-align-c padd-b-15">
                <span class="ft-14 padd-r-5">质&ensp;控&ensp;位</span>
                    <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.ybbh" placeholder="请输入质控位"/>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">高值</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="flex-container flex-jus-sb padd-l-10  padd-r-10 flex-wrap-w">
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考靶值</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.x2" placeholder="请输入参考靶值"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考SD</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.sd2" placeholder="请输入参考SD"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考CV</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.cv2" placeholder="请输入参考CV"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">中值</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="flex-container flex-jus-sb padd-l-10  padd-r-10 flex-wrap-w">
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考靶值</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.x1" placeholder="请输入参考靶值"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考SD</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.sd1" placeholder="请输入参考SD"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考CV</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.cv1" placeholder="请输入参考CV"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">低值</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="flex-container flex-jus-sb padd-l-10  padd-r-10 flex-wrap-w">
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考靶值</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.x" placeholder="请输入参考靶值"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考SD</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.sd" placeholder="请输入参考SD"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-15">
                        <span class="ft-14 padd-r-5">参考CV</span>
                        <input type="text" class="zui-input wh180 margin-l5 l-color35" v-model="zk.cv" placeholder="请输入参考CV"/>
                    </div>
                </div>
            </div>
        </div>
        <div  class="switch padd-l-10">
            <div class="l-zksj-fixed">状态</div>
                <input type="checkbox" v-model="zk.qyzt"/>
                <label></label>
        </div>
        <div class="l-bottom-fixed">
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default btn-width" @click="AddClose">取消</button>
                <button class="zui-btn btn-primary btn-width margin-r15" @click="addOk">确定</button>
            </div>
        </div>

    </div>
</div>
<div id="pop">
    <div class="pophide" :class="{'show':isShow}" style="z-index: 9999"></div>
    <div class="zui-form podrag pop-width bcsz-layer" :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin"><i class="icon-cha" @click="isShow=false"></i></span>
        <div class="layui-layer-content">
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default btn-width" @click="isShow=false">取消</button>
            <button class="zui-btn btn-primary btn-width margin-r15" @click="delAll">确定</button>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="zksj.js"></script>
</html>
