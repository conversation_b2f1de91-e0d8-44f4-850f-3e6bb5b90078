<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>项目指标</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="sbtdh.css" rel="stylesheet"/>
</head>
<style>
    .zui-table-view .fieldlist {
        display: none !important;
    }

    .zui-table-view {
        height: auto !important;
        min-height: auto !important;
    }

    .xmzb-content-right .content-right-list li i {
        width: 80%;
        line-height: initial;
        height: initial;
    }

    .xmzb-content-right .content-right-list li .i {
        width: calc((100% - 145px) / 8) !important;
        text-align: center;
    }
    .min-chuangkou,.table{
        right: 10px;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper">

    <div class="xmzb-content" style="padding-bottom: 40px; background: #fff">
        <div class="xmzb-content-left">
            <div class="content-left-top">
                设备名称
            </div>
            <ul class="content-left-list">
                <li v-for="(item, $index) in jysbList" @click="AddList(item)" class="overflow1"
                    v-text="item.hostname"></li>
            </ul>

        </div>
        <div class="xmzb-content-right" id="jyxm_icon">
            <div class="xmzb-top">
                <div class="col-x-6 xmzb-top-left">
                    <i>搜索</i>
                    <i><input type="text" v-model="zbbmParam.hostname" placeholder="请输入关键字" class="zui-input"/></i>
                    <i>
                        <button class="zui-btn btn-primary xmzb-db">查询</button>
                    </i>
                </div>
            </div>
            <div class="content-right-top">
                <i>指标编码</i>
                <i>中文名称</i>
                <i>英文名称</i>
                <i>代码</i>
                <i>单位</i>
                <i>数据类型</i>
                <i>参考类型</i>
                <i>小数位数</i>
                <i>状态</i>
            </div>
            <ul class="content-right-list">
                <li v-for="(item, $index) in zbbmList" @dblclick="zbdj(item,$index)">
                    <div class="i position"><i v-text="item.zbbm" class="overflow1 title" :data-title="item.zbbm"></i>
                    </div>
                    <div class="i position"><i v-text="item.zwmc" class="overflow1 title" :data-title="item.zwmc"></i>
                    </div>
                    <div class="i position"><i v-text="item.ywmc" class="overflow1 title" :data-title="item.ywmc"></i>
                    </div>
                    <div class="i position"><i v-text="item.pydm" class="overflow1 title" :data-title="item.pydm"></i>
                    </div>
                    <div class="i position"><i v-text="item.dw" class="overflow1 title" :data-title="item.dw"></i></div>
                    <div class="i position"><i v-text="zbbmsjlx_tran[item.sjlx]" class="overflow1 title"
                                               :data-title="zbbmsjlx_tran[item.sjlx]"></i></div>
                    <div class="i position"><i v-text="zbbmcklx_tran[item.cklx]" class="overflow1 title"
                                               :data-title="zbbmcklx_tran[item.cklx]"></i></div>
                    <div class="i position"><i v-text="item.xsws" class="overflow1 title" :data-title="item.xsws"></i>
                    </div>
                    <div class="i ">
                        <i>
                            <div class="switch">
                                <input type="checkbox"/>
                                <label></label>
                            </div>
                        </i>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="isTabel">
        <div class="table" v-show="isTabelShow">
            <div class="poptable">
                <div class="header">
                    <h3 class="titel">
                        <span class="font16">
                             已选项目（{{sumNum}}）
                         </span>
                        <i class="icon-shanchu" @click="isTabelShow=false,minishow=true"></i>
                    </h3>
                </div>
                <div style="border:none; width: 100%">
                    <div class="xmzb-title">
                        <i>检验设备</i>
                        <i>项目编码</i>
                        <i>项目简称</i>
                        <i>项目名称</i>
                        <i>通道号</i>
                        <i>调整系数</i>
                        <i>调整值</i>
                        <i>附属设备序号</i>
                    </div>
                    <ul class="xmzb-list">
                        <li v-for="(item, $index) in jysbTdhList" @dblclick="dbDel(item,$index)">
                            <i v-text="jysbObj.hostname"></i>
                            <i v-text="item.zbbm"></i>
                            <i v-text="item.ywmc"></i>
                            <i v-text="item.zwmc"></i>
                            <i class="sbtdh-width">
                                <input type="text" v-model="item.tdh" class="sbtdh-input" @keydown="nextFocus($event)"/>
                            </i>
                            <i class="sbtdh-width">
                                <input type="text" v-model="item.tzxs" class="sbtdh-input"
                                       @keydown="nextFocus($event)"/>
                            </i>
                            <i class="sbtdh-width">
                                <input type="text" v-model="item.tzz" class="sbtdh-input" @keydown="nextFocus($event)"/>
                            </i>
                            <i class="sbtdh-width">
                                <input type="text" v-model="item.xh" class="sbtdh-input" @keydown="nextFocus($event)"/>
                            </i>
                        </li>
                    </ul>
                </div>
                <div class="xmzb-ok">
                    <button class="zui-btn table_db_esc btn-default xmzb-db" @click="isTabelShow=false,minishow=true">
                        取消
                    </button>
                    <button class="zui-btn btn-primary xmzb-db" @click="bc">确定</button>
                </div>

            </div>
        </div>
        <div class="min-chuangkou" v-show="minishow">
            <p><span class="text-min">已选设备（{{sumNum}}）</span> <span class="icon-min"
                                                                    @click="isTabelShow=true,minishow=false"></span></p>
        </div>

    </div>

</div>


<script src="sbtdh.js"></script>
</body>
</html>
