<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>出库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ckgl.css" rel="stylesheet"/>
    <link href="print.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<style>

</style>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="printArea printShow"></div>
<div class="background-box">
    <div class="wrapper printHide" id="wrapper" v-cloak>
        <div class="panel">
            <!-- 顶部按钮区域 begin -->
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-if="isShowkd">开单</button>
                <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" v-if="isShowkd" @click="LingYaoD">申领单</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kd(1)" v-if="isShowpopL">添加材料</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)" v-if="isShowkd">刷新</button>
            </div>
            <!-- 顶部按钮区域 end -->

            <!-- 检索区域 begin -->
            <div class="tong-search" :class="{'tong-padded':isShow}">
                <div class="zui-form" v-if="isShowkd">
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label ">一级库房</label>
                        <div class="zui-input-inline wh122" style="margin-left: 20px">
                            <select-input @change-data="resultRydjChange"
                                          :child="KFList"
                                          :index="'kfmc'"
                                          :index_val="'kfbm'"
                                          :val="param.kfbm"
                                          :name="'param.kfbm'"
                                          :search="true"
                                          :index_mc="'kfmc'" @keydown="nextFocus($event)">
                            </select-input>

                        </div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">领用科室</label>
                        <select-input @change-data="commonResultChange" :child="KSList"
                                      :index="'ksmc'" :index_val="'ksbm'" :val="param.lyks" :search="true"
                                      :name="'param.lyks'" ref="autofocus" :search="true" >
                        </select-input>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">状态</label>
                        <select-input @change-data="resultChangezt"
                                      :child="ckglzt_tran"
                                      class="wh122"
                                      :index="param.zt"
                                      :val="param.zt"
                                      :name="'param.zt'">
                        </select-input>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">时间段</label>
                        <div class="zui-input-inline margin-f-l5 flex-container flex-align-c">
                            <input class="zui-input todate wh200 " v-model="param.beginrq" @click="showTime('timeVal','beginrq')" placeholder="请选择申请开始日期" id="beginTimeVal"/>
                            <span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input todate wh200 " v-model="param.endrq" @click="showTime('timeVal','endrq')" placeholder="请选择申请结束时间" id="endTimeVal" />
                        </div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">检索</label>
                        <div class="zui-input-inline margin-f-l20">
                            <input class="zui-input wh200"
                                   placeholder="请输入关键字"
                                   type="text" @keydown.enter="goToPage(1)"
                                   v-model="param.parm"/>
                        </div>
                    </div>
                    <!--<div class="zui-inline" style="padding: 0 0 8px 0px;">-->
                        <!--<span class="color-wtg font-18">零价总额： {{totalyplj}}元&ensp;</span>-->
                    <!--</div>-->
                    <!--<div class="zui-inline" style="padding: 0 0 8px 0px;">-->
                        <!--<span class="color-wtg font-18">进价总额： {{totalypjj}}元&ensp;</span>-->
                    <!--</div>-->
                </div>
                <div class="jbxx" v-if="!isShowkd">
                    <div class="jbxx-size">
                        <div class="jbxx-position">
                            <span class="jbxx-top"></span>
                            <span class="jbxx-text">基本信息</span>
                            <span class="jbxx-bottom"></span>
                        </div>
                        <div class="zui-form padd-l24 padd-t-20">
                            <div class="zui-inline">
                                <label class="zui-form-label ">库房</label>
                                <div class="zui-input-inline"
                                     v-if="kfShow">
                                    <select-input @change-data="resultChange"
                                                  :child="KFList"
                                                  :index="'kfmc'"
                                                  :index_val="'kfbm'"
                                                  :val="popContent.kfbm"
                                                  :name="'popContent.kfbm'"
                                                  :search="true"
                                                  :index_mc="'kfmc'"
                                                  :disable="jyinput" @keydown="nextFocus($event)">
                                    </select-input>
                                </div>
                                <div class="zui-input-inline  "
                                     v-if="kfShow==false">
                                    <div class="background-h zui-input wh122"
                                         disabled
                                         v-text="kfmcc"></div>
                                    <span class="iconClass"></span>
                                </div>
                            </div>

                            <div class="zui-inline">
                                <!--
                                <label class="zui-form-label ">出库方式</label>
                                <div class="zui-input-inline wh122 margin-l-5"
                                     v-if="kfShow">
                                    <select-input :cs="true"
                                                  @change-data="resultChange"
                                                  :not_empty="false"
                                                  :child="ckfs_tran1"
                                                  :search="true"
                                                  :index="popContent.ckfs"
                                                  :val="popContent.ckfs"
                                                  :name="'popContent.ckfs'" @keydown="nextFocus($event)">
                                    </select-input>
                                </div>-->
                                <div class="zui-input-inline  "
                                     v-if="kfShow==false">
                                    <div class="background-h zui-input "
                                         disabled
                                         v-text="kfmcc"></div>
                                    <span class="iconClass"></span>
                                </div>
                            </div>
                            <div class="zui-inline ">
                                <label class="zui-form-label ">领用科室</label>
                                <div class="zui-input-inline" v-if="kfShow">
                                    <select-input @change-data="resultKsChange" :child="KSList"
                                                  :index="'ksmc'" :index_val="'ksbm'" :val="ckdContent.lyks" :search="true"
                                                  :name="'ckdContent.lyks'" ref="autofocus" :search="true" >
                                    </select-input>
                                </div>
                            </div>
                            <div class="zui-inline ">
                                <label class="zui-form-label ">领用库房</label>
                                <input type="text"
                                       class="zui-input"
                                       v-model="ckdContent.lyyfmc"
                                       @keydown="nextFocus($event)"
                                       disabled="disabled" v-if="false"/>
                                <select-input @keydown="nextFocus($event)"
                                              @change-data="resultChangecz"
                                              :data-notEmpty="true"
                                              :child="lyyfList"
                                              :index="'lyyfmc'"
                                              :index_val="'lyyf'"
                                              :val="ckdContent.lyyf"
                                              :name="'ckdContent.lyyf'" :search="true"
                                              :disable="csqxContent.cs00200100208!='1'">
                                </select-input>
                            </div>
                            <div class="zui-inline ">
                                <label class="zui-form-label ">领1用人</label>
                                <select-input @keydown="nextFocus($event)"
                                              @change-data="resultChangecz"
                                              :data-notEmpty="true"
                                              :child="glryList"
                                              :index="'ryxm'"
                                              :index_val="'rybm'"
                                              :val="ckdContent.lyr"
                                              :name="'ckdContent.lyr'" :search="true">
                                </select-input>
                            </div>
                            <div class="zui-inline "
                                 style="width:30%;">
                                <label class="zui-form-label ">备注</label>
                                <div class="zui-input-inline margin-f-l20" v-if="kfShow">
                                    <input class="zui-input"
                                           placeholder="请输入备注"
                                           type="text"
                                           id="bzms"
                                           v-model="popContent.bzsm" @keydown="nextFocus($event)" @keydown.enter="kd(1)"
                                           :disabled="jyinput"/>
                                </div>
                                <div class="zui-input-inline margin-f-l20" v-if="kfShow==false">
                                    <input class="zui-input"
                                           placeholder="请输入备注"
                                           type="text"
                                           v-model="popContent.bzsm" disabled/>
                                </div>
                            </div>

                        </div>
                        <div class="rkgl-kd">
                            <span>开单日期:<i v-text="zdrq"></i></span>
                            <span>开单人：<i class="color-wtg"
                                         v-text="zdyxm"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 检索区域 end -->
        </div>

        <div class="zui-table-view ">
            <div class="zui-table-header" key="a" v-show="isShowkd">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class=" cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl "><span>出库单号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>领用科室</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>出库方式</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>领用人</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>制单员</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>总零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>总进价</span></div>
                        </th>

                        <th>
                            <div class="zui-table-cell cell-xl"><span>制单日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>申领单号</span></div>
                        </th>
                        <!--                                <th>-->
                        <!--                                    <div class="zui-table-cell cell-l"><span>SPD单号</span></div>-->
                        <!--                                </th>-->
                        <th>
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" key="a" v-show="isShowkd" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in ckdList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'background-red':item.totalYpjj.indexOf('-') == 0}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','ckdList'],$event)"
                        :tabindex="$index"
                        @dblclick="openDetail($index)"
                    >
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m"
                                 v-text="$index+1">序号
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl"
                                 v-text="item.ckdh">出库单号
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"
                                 v-text="item.lyksmc">序号
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"
                                 v-text="ckfs_tran[item.ckfs]">序号
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"
                                 v-text="item.lyrxm">序号
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"
                                 v-text="item.zdyxm">序号
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.totalYplj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.totalYpjj"></div>
                        </td>

                        <td>
                            <div class="zui-table-cell cell-xl"
                                 v-text="fDate(item.zdrq,'datetime')">状态
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l"
                                 v-text="item.sldh">状态
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <i v-text="zhuangtai[item.shzfbz]"
                                   :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : item.shzfbz=='3' ? 'color-wtg':'' "></i>
                            </div>
                        </td>
                        <td class=" cell-s">
                            <div class="zui-table-cell cell-s flex-container flex-align-c padd-t-5">
                                <span class="width30 icon-sh title" v-if="item.shzfbz == '0'" data-gettitle="审核"
                                      @click="showDetail($index,item)"></span>
                                <span class="width30 icon-js title" v-if="item.shzfbz == '0'" data-gettitle="作废"
                                      @click="invalidData($index)"></span>
                                <span class="width30 icon-bj title" v-if="item.shzfbz == '0' || item.shzfbz == '1'"
                                      data-gettitle="编辑" @click="editIndex($index,item.zdyxm)"></span>
                            </div>
                        </td>
                        <p v-show="ckdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>

                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="zui-table-header" key="b" v-show="!isShowkd">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell"><span>序号</span></div>
                        </th>
						<th>
						    <div class="zui-table-cell cell-xl text-left"><span>材料编码</span></div>
						</th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left"><span>商品名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>产地</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s text-left"><span>材料规格</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>材料批号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>出库数量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>已冲销数量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>材料进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>材料总进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>材料零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>材料总零价</span></div>
                        </th>
                        <th v-show="cxShow">
                            <div class="zui-table-cell cell-s"><span>冲销数量</span></div>
                        </th>

                        <th>
                            <div class="zui-table-cell cell-s"><span>供货单位</span></div>
                        </th>

                        <!--                                <th>-->
                        <!--                                    <div class="zui-table-cell cell-s"><span>分装比例</span></div>-->
                        <!--                                </th>-->
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body " key="b" @scroll="scrollTable($event)" v-show="!isShowkd">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in ckdDetail"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','ckdDetail'],$event)"
                        :tabindex="$index">
                        
                        <td class="cell-m">
                            <div class="zui-table-cell" v-text="$index+1">序号</div>
                        </td>
						<td>
						    <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypbm">序号</div>
						</td>
                        <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypspm">商品名</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s text-over-2" v-text="item.cdmc">产地</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s text-over-2 text-left" v-text="item.ypgg"
                                 :data-title="item.ypgg">规格
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.scph">材料批号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{getYearFun(item.yxqz) ? fDate(item.yxqz,'date') :
                                ''}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.cksl}}{{item.kfdwmc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.ycxsl}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ypjj">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj* item.cksl,2)">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,4)">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.yplj * item.cksl, 4)">序号</div>
                        </td>
                        <td v-show="cxShow">
                            <div class="zui-table-cell cell-s ">
                                <input class="zui-input height-28"
                                       @input="getCxsl(item.cksl,item.cxsl,item.ycxsl,$index)"
                                       :disabled="item.ycxsl >= item.cksl " v-model="item.cxsl"/>
                            </div>
                        </td>

                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ghdwmc">状态</div>
                        </td>

                        <!--                                <td>-->
                        <!--                                    <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>-->
                        <!--                                </td>-->
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s">
                                        <span class="flex-center padd-t-5">
                                            <em v-show="mxShShow" class="width30"><i class="icon-bj" data-title="编辑"
                                                                                   @click="edit($index)"></i></em>
                                            <em v-show="mxShShow && !rkd.shzfbz" class="width30"><i class="icon-sc"
                                                                                                  data-title="删除"
                                                                                                  @click="scmx($index)"></i></em>
                                        </span>
                            </div>
                        </td>
                        <p v-show="ckdDetail.length==0"
                           class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="zui-table-fixed table-fixed-l" key="b" v-show="!isShowkd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body"
                     @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in ckdDetail"
                            :tabindex="$index"
                            class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','ckdDetail'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m"
                                     v-text="$index+1"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="zui-table-fixed table-fixed-r" key="b" v-show="!isShowkd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body"
                     @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in ckdDetail"
                            :tabindex="$index"
                            class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','ckdDetail'],$event)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s ">
                                            <span class="flex-center padd-t-5">
                                                <em v-if="mxShShow"
                                                    class="width30"><i class="icon-bj"
                                                                       data-title="编辑"
                                                                       @click="edit($index)"></i></em>
                                                <em v-if="mxShShow && (rkd.shzfbz == '0' || !rkd.shzfbz) "
                                                    class="width30"><i class="icon-sc"
                                                                       data-title="删除"
                                                                       @click="scmx($index)"></i></em>
                                            </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage" v-show="isShowkd" :totle-page="totlePage" :page="page" :param="param"
                  :prev-more="prevMore" :next-more="nextMore"></page>
            <div class="rkgl-position flex-container padd-l-10 padd-r-10 flex-jus-sb" v-show="!isShowkd">
                    <span class="flex-container" :id="money">
                        <i class="padd-r-10">材料进价总价: <em class="color-wtg">{{fDec(json.jjzj,2)}}元</em></i>
                        <i>材料零价总价: <em class="color-wtg">{{fDec(json.ljzj,2)}}元</em></i>
                    </span>
                <span class="flex-container">
                        <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                         <button class="tong-btn btn-parmary-f2a xmzb-db" v-show="dyShow" @click="printDJ()">打印</button>
                         <button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick"
                                 v-if="isCx(ckd) && dyShow">冲销</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick"
                                v-if="!dyShow && cxShow && isCx(ckd)">取消冲销</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()"
                                v-show="zfShow">作废</button>
                        <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow && isCx(ckd)">提交</button>
                        <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow && isShFun(ckd)">审核</button>
                    </span>
            </div>
        </div>
    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide"
     id="brzcList"
     role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--诊疗类别-->
    <div class="ksys-side pop-805"  v-if="!lYShow">
        <ul class="tab-edit-list tab-edit2-list">
            <li v-if="popContent.ckfs != '05'">
                <i>领用科室</i>
                <select-input @change-data="resultKsChange" :child="KSList"
                              :index="'ksmc'" :index_val="'ksbm'" :val="ckdContent.lyks" :search="true"
                              :name="'ckdContent.lyks'" ref="autofocus" :search="true" >
                </select-input>
            </li>
			<li v-if="popContent.ckfs != '05'">
			    <i>领用二级库房</i>
			    <input type="text"
			           class="zui-input"
			           v-model="ckdContent.lyyfmc"
			           @keydown="nextFocus($event)"
			           disabled="disabled" v-if="false"/>
			    <select-input @keydown="nextFocus($event)"
			                  @change-data="resultChange"
			                  :data-notEmpty="true"
			                  :child="lyyfList"
			                  :index="'lyyfmc'"
			                  :index_val="'lyyf'"
			                  :val="ckdContent.lyyf"
			                  :name="'ckdContent.lyyf'" :search="true"
			                  :disable="csqxContent.cs00200100208!='1'">
			    </select-input>
			</li>
            <li v-if="popContent.ckfs  != '05'">
                <i>领用人</i>
                <select-input @keydown="nextFocus($event)"
                              @change-data="resultChange"
                              :data-notEmpty="true"
                              :child="glryList"
                              :index="'ryxm'"
                              :index_val="'rybm'"
                              :val="ckdContent.lyr"
                              :name="'ckdContent.lyr'" :search="true">
                </select-input>
            </li>
            <li>
                <i>材料名称</i>
                <input class="zui-input" id="ypmc"
                       autocomplete="off"
                       v-model="popContent.ypmc"
                       @keydown="changeDown($event,'text')"
                       data-notEmpty="true"
                       @input="change(false,'ypmc', $event.target.value)"/>
                <search-table :message="searchCon"
                              :selected="selSearch"
                              :page="page"
                              :them="them"
                              :them_tran="them_tran"
                              @click-one="checkedOneOut"
                              @click-two="selectOne">
                </search-table>
            </li>
            <li class="position">
                <i>商品名</i>
                <input type="text"
                       disabled
                       class="zui-input"
                       id="ypspm"
                       v-model="popContent.ypspm"
                       @keydown.enter="nextFocus($event)"/>

            </li>
            <li>
                <i>产地</i>
                <input disabled id="ypcd" ref="cdmc" class="zui-input" v-model="popContent.cdmc"
                       @keydown="changeDown1($event,$event.target.value),nextFocus($event)"
                       @input="change1(false, $event.target.value)">
                <search-table :page="queryStr" :message="searchCon1" :selected="selSearch1" :them="them1"
                              :them_tran="them_tran1" @click-one="checkedOneOut"
                              @click-two="selectOne1">
                </search-table>
            </li>
            <li>
                <i>供货单位</i>
                <input type="text"
                       disabled="disabled"
                       class="zui-input"
                       v-model="popContent.ghdwmc"
                       @keydown="nextFocus($event)"/>
            </li>
            <li class="position">
                <i>出库数量</i>
                <input type="number"
                       class="zui-input"
                       id="cksl"
                       v-model="popContent.cksl"
                       @keydown.enter="addData($event)"/>
                <span v-text="popContent.kfdwmc"></span>

            </li>
            <li>
                <i>材料规格</i>
                <input type="text"
                       disabled="disabled"
                       class="zui-input"
                       v-model="popContent.ypgg"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>库存数量</i>
                <input type="number"
                       class="zui-input"
                       v-model="popContent.kcsl"
                       @keydown="nextFocus($event)"
                       disabled="disabled"/>
            </li>
            <li>
                <i>材料进价</i>
                <input type="number"
                       class="zui-input"
                       disabled

                       v-model="popContent.ypjj"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>材料零价</i>
                <input type="number"
                       class="zui-input"
                       disabled
                       v-model="popContent.yplj"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>科室进价</i>
                <input type="number"
                       class="zui-input"
                       disabled
                       v-model="popContent.yfypjj"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>科室零价</i>
                <input type="number"
                       class="zui-input"
                       disabled
                       v-model="popContent.yfyplj"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>材料批号</i>
                <input type="text"
                       class="zui-input"
                       v-model="popContent.scph"
                       @keydown="nextFocus($event)"
                       disabled/>
            </li>
            <li>
                <i>生产日期</i>
                <input type="text"
                       class="zui-input  times1"
                       disabled
                       id="_scrq"
                       @keyup="setTime($event,'popContent.scrq')"
                       :value="fDate(popContent.scrq,'datetime')"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>有效期至</i>
                <input type="text"
                       class="zui-input  times2"
                       disabled
                       @keyup="setTime($event,'popContent.yxqz')"
                       id="_yxqz"
                       :value="getYearFun(popContent.yxqz) ? popContent.yxqz : ''"
                       @keydown="nextFocus($event)"/>
            </li>

            <li>
                <i>分装比例</i>
                <input type="text" disabled="disabled"
                       class="zui-input"
                       v-model="popContent.fzbl"
                       @keydown.enter="addData()">
            </li>
            <li>
                <i>产品标准&ensp;&ensp;号</i>
                <input class="zui-input"
                       v-model="popContent.cpbzh"
                       disabled="disabled"
                       @keydown="nextFocus($event)">
            </li>
            <li>
                <i>批准文号</i>
                <input v-model="popContent.pzwh"
                       class="zui-input"
                       disabled="disabled"
                       @keydown="nextFocus($event)">
            </li>
            <li>
                <i>系统批号</i>
                <input type="text"
                       class="zui-input"
                       v-model="popContent.xtph"
                       disabled
                       @keydown="changeDown($event,'xtph')">
            </li>
        </ul>
    </div>
    <!--领药单-->
    <div class="ksys-side1" v-if="lYShow" style="padding:15px 10px 15px 10px;">
        <div class="ly-left">
            <div class="ly-left-top">
                <i>申领单号</i>
                <i>制单时间</i>
                <i>制单员</i>
            </div>
            <ul class="ly-left-content">
                <li v-for="(item,$index) in lydList"
                    @click="checkSelect([$index,'one','lydList'],$event),lymx($index)"
                    :class="[{'table-hovers':isChecked[$index]}]"
                    :tabindex="$index">
                    <i>
                        <input-checkbox @result="reCheckBox"
                                        :list="'lydList'"
                                        :type="'one'"
                                        :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </i>
                    <i class="relative"><em class="title title-width"
                                            :data-title="item.sldh"
                                            v-text="item.sldh"></em></i>
                    <i class="relative"><em class="title title-width"
                                            :data-title="fDate(item.zdrq,'datetime')"
                                            v-text="fDate(item.zdrq,'datetime')"></em></i>
                    <i v-text="item.zdrmc">李浩然</i>
                </li>
            </ul>
        </div>
        <div class="ly-right">
            <div class="ly-right-top">
                <i>序号</i>
                <i class="text-left">材料名称</i>
                <i>编码</i>
                <i>领取数量</i>
                <i>库存数量</i>
            </div>
            <ul class="ly-right-content">
                <li v-for="(item,$index) in lymxList">
                    <i v-text="$index+1">1</i>
                    <i class="text-left relative">
                        <em class="title title-width"
                            :data-title="item.ypmc"
                            v-text="item.ypmc">材料名称</em>
                    </i>
                    <i v-text="item.ypbm">编码</i>
                    <i v-text="item.slsl">2</i>
                    <i v-text="item.kcsl">123</i>
                </li>
            </ul>
        </div>

    </div>
    <div class="ksys-btn" style="position:absolute">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="addData" v-if="!lYShow">保存</button>
        <button class="zui-btn btn-primary xmzb-db" @click="tjCom" v-if="lYShow">提交</button>
    </div>
</div>

<script src="ckgl.js?v=20210125"></script>

</body>

</html>
