<div id="baksbm">
    <!--<div class="panel box-fixed" style="top:65px;">-->
        <!--<div class="tong-top">-->
            <!--<button class="tong-btn btn-parmary" @click="sx"><span class="fa fa-refresh padd-r-5"></span>刷新</button>-->
            <!--<button class="tong-btn btn-parmary-b" @click="saveBaks"><span class="fa fa-save padd-r-5"></span>保存</button>-->
            <!--<button class="tong-btn btn-parmary-b" @click="deleteBaks"><span class="fa fa-trash-o paddr-r5"></span>删除</button>-->
        <!--</div>-->
    <!--</div>-->
    <div class="baksbm-box">
    <div class="baksbm-left">
        <div class="zui-table-view " z-height="full" style="border: none;">
            <div class="zui-table-header">
                <table class="font-14 zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>科室编码</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>科室名称</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr v-for="(item, $index) in ksList" :tabindex="$index" class="tableTr2" @click="checkOne($index)" @dblclick="editThis($index)">
                        <td><div class="zui-table-cell " v-text="item.ksbm"></div></td>
                        <td><div class="zui-table-cell " v-text="item.ksmc"></div></td>
                        <td><div class="zui-table-cell " v-text="item.pydm"></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>

        </div>

    </div>
    <div class="baksbm-right">
        <div class="zui-table-view " z-height="full" style="border: none;">
            <div class="zui-table-header">
                <table class="font-14 zui-table">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m" ><span>
                            <input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'"
                                                                                      :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>科室编码</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>科室名称</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>病案统计码</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>病案科室</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>编制床位</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>加床床位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>产科标志</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in baksList" :tabindex="$index" class="tableTr2" @click="checkOne($index)"
                        :class="[{'yellowBack': isChecked[$index]},{'newAddBg': edit[$index]}]"
                        @dblclick="editThis($index)">
                        <td><div class="zui-table-cell cell-m" >
                            <input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'" :type="'some'" :which="$index" :val="isChecked[$index]">
                        </input-checkbox></div>
                        </td>

                        <td><div class="zui-table-cell cell-m"><span v-text="item.ksbm"></span></div></td>
                        <td><div class="zui-table-cell cell-m"><span v-text="item.ksmc"></span></div></td>
                        <td><div class="zui-table-cell cell-m">
                            <span v-if="!edit[$index]" v-text="item.batjm"></span>
                            <span  v-if="edit[$index]"><input v-model="item.batjm" class="zui-input height27" @keydown="nextFocus($event)"></span>
                        </div>
                        <td><div class="zui-table-cell cell-m">
                            <span v-if="!edit[$index]" v-text="item.baksmc"></span>
                            <span  v-if="edit[$index]"><input v-model="item.baksbm" class="zui-input height27" @keydown="nextFocus($event)"></span>
                        </div>
                        <td><div class="zui-table-cell cell-m">
                            <span v-if="!edit[$index]" v-text="item.bzcw"></span>
                            <span  v-if="edit[$index]"><input v-model="item.bzcw" class="zui-input height27" @keydown="nextFocus($event)"></span>
                        </div>
                        <td><div class="zui-table-cell cell-m">
                            <span v-if="!edit[$index]" v-text="item.jccw"></span>
                            <span  v-if="edit[$index]"><input v-model="item.jccw" class="zui-input height27" @keydown="nextFocus($event)"></span>
                        </div>
                        </td>
                        <td><div class="zui-table-cell cell-s">
                            <span v-if="!edit[$index]" v-text="istrue_tran[item.ckbz]"></span>
                            <span  v-if="edit[$index]" class="baks-span">
                                <!--<select class="zui-input height27"  v-model="item.ckbz">-->
            				    <!--<option value="0">否</option>-->
            				    <!--<option value="1">是</option>-->
            			    <!--</select>-->
                                <select-input  @change-data="resultChange" :not_empty="false"
                                              :child="istrue_tran" :index="item.ckbz" :val="item.ckbz"
                                              :name="'baksList.'+$index+'.ckbz'">
	            			</select-input>
                            </span>
                        </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>

        </div>

    </div>
    </div>


</div>

<script type="application/javascript" src="baksbm.js"></script>
