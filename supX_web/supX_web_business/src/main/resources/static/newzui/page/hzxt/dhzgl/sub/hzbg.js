var jbxx = new Vue({
    el:'#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat,scrollOps],
    data:{
        popContent:{},
        csqxContent:{},
        yljgmc:null
    },
    mounted:function () {
        this.initVal();
        window.addEventListener("storage", function (e) {
            if (e.key == "dhzglitem_change" && e.newValue !== e.oldValue) {
                jbxx.initVal();
            }
        });
        laydate.render({
            elem: '#sqTime',
            type: 'datetime',
            theme: '#1ab394'
            , done: function (value, data, a, b, c, d) {
                sssq.pageState.sqrq = value;
                sssq.nextFocus($('#sqTime')[0])
            }
        });
    },
    methods:{
    //初始加载
        initVal:function () {
            this.popContent = JSON.parse(sessionStorage.getItem('dhzglitem'));
            this.csqxContent = JSON.parse(sessionStorage.getItem('hzcsqxList'));
            this.popContent.ifEdit = sessionStorage.getItem('dhzglitem_ifEdit');
            if(!this.popContent.hzkssj){
                this.popContent.hzkssj = new Date().getTime();
            }
            if(!this.popContent.hzjlsj){
                this.popContent.hzjlsj = new Date().getTime();
            }
            if(this.popContent.hzzt == '2'){
                $('.contenteditable').attr('contenteditable',false);
            }

            // console.l
			// this.popContent.brxb = this.brxb_tran[this.popContent.brxb];
			// this.popContent.nldw = this.nldw_tran[this.popContent.nldw];
			
            this.yljgmc = JSON.parse(sessionStorage.userPage)[2].yljgmc;
            this.yljgmc = this.yljgmc?this.yljgmc:"";
            sessionStorage.removeItem('dhzglitem');
            sessionStorage.removeItem('hzcsqxList');
        },
    //取消
        Cancel:function () {
            var hzbg_targetPatPath = sessionStorage.getItem("hzbg_targetPatPath");
            this.topClosePage('page/hzxt/hzxt/dhzgl/sub/hzbg.html',hzbg_targetPatPath);
        },
        //保存
        save:function () {
            if(!jbxx.popContent.hzjl){
                malert("请先填写会诊结论与建议！","top","defeadted");
                return;
            }
            jbxx.popContent.hzzt = '2';
            jbxx.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=saveHzgl', JSON.stringify(jbxx.popContent)).then(function(data) {
                if (data.body.a == 0) {
                    malert(data.body.c);
                    sessionStorage.setItem("apzj_saveHzbg",new Date().getTime())
                } else {
                    malert(data.body.c, 'top', 'defeadted');
                    return;
                }
            }, function(error) {
                console.log(error);
            });
        },
        print:function(){
            // if (jbxx.csqxContent.N03010200202 == '1'){
                var reportlets = "[{reportlet: 'fpdy%2Fzyys%2Fzyys_hzsq.cpt',hzbh:'" + this.popContent.hzbh + "'}]";
                if (!FrPrint(reportlets, null)) {
                    return;
                }
            // } else {
            //     $("#aaaa").hide();
            //     window.print();
            //     $("#aaaa").show();
            // }
        },
    }

});

