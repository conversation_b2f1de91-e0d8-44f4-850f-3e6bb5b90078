    var wrapper = new Vue({
        el: '#wrapper',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data: {
            zt: '9',
            ksList:[],
            defaltObj:{
                title:'拒绝申请',
                cs:'btn-parmary fr-right',
                cm:'btn-parmary-f2a1 fr-right',
                bm:'flex-none',
                cb:'取消',
                sb:'拒绝'
            },
        },

        updated:function () {
            changeWin()
        },
        mounted:function(){
            this.getKsbm()
        },
        methods: {
            //获取当前操作员的拥有科室权限
            getKsbm: function () {
                var str_param = {
                    zyks:'1',
                };
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(str_param), function (json) {
                    if (json.a == 0 && json.d!=null) {
                        if (json.d.list.length > 0) {
                            wrapper.ksList = json.d.list;
                            wrapper.param.ryks = json.d.list[0].ksbm;
                            wrapper.getData();
                        }
                    } else {
                        malert('获取科室失败','top','defeadted')
                    }
                });
            },
            //请求后台查询列表信息
            getData : function(){
                //加载动画效果
                common.openloading('.zui-table-view')
                //数据请求结束时关闭
                common.closeLoading()
            },
            //查看详情
            checkDetail:function (num,val) {
                sessionStorage.setItem('hzsqglitem',JSON.stringify(val));
                this.topNewPage('会诊申请管理','page/hzxt/hzxt/hzshf/Subdirectory/apzj.html')
            },
              //患者姓名跳转到电子病历
            Patient:function () {
                // this.topNewPage('患者中心','page/zyysz/zyysz/hzgl/hzzx/hzzx.html')
            },
            //安排会诊页面跳转
            arrange:function (num,val) {
                sessionStorage.setItem('hzsqglitem',JSON.stringify(val));
                this.topNewPage('安排专家','page/hzxt/hzxt/hzshf/Subdirectory/apzj.html')
            },
            //拒绝
            refuse(){
                common.openConfirm('您确定要拒绝该会诊申请吗',function () {
                    malert('拒绝','top','defeadted');
                },function () {
                },this.defaltObj)
            },
            //填写会诊报告
            Group:function () {
                this.topNewPage('安排专家','page/hzxt/hzxt/hzshf/hzbg.html')
            },
            //查看诊断报告
            Diagnosis:function () {

            }
        }
    });

    laydate.render({
        elem: '.times',
        type: 'date',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
    laydate.render({
        elem: '.times1',
        type: 'date',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });







