<div id="chlb" v-show="!edit">
    <!--入院登记功能按钮begin-->
    <div class="panel">
        <div class="tong-top flex-container flex-align-c">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addData">入院登记</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
        </div>
    </div>
    <!--入院登记功能按钮end-->

    <!--检索字段begin-->
    <div class="tong-search">
        <div class="zui-form">
            <div class="zui-inline padding-left40">
                <label class="zui-form-label">状态</label>
                <div class="zui-input-inline wh120">
                    <select-input @change-data="ztChange"
                                  :data-notEmpty="true"
                                  :child="zt_tran"
                                  :index="param.zyzt"
                                  :val="param.zyzt"
                                  :name="'param.zyzt'"
                                  :search="true"></select-input>
                </div>
            </div>
            <div class="zui-inline padding-left60" v-show="zyzt=='1'">
                <label class="zui-form-label ">时间段</label>
                <div class="zui-input-inline flex-container flex-align-c">
                    <i class="icon-position icon-rl"></i>
                    <input class="zui-input todate wh180 text-indent20" placeholder="不限定时间范围" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                    <input class="zui-input todate wh180 " placeholder="请选择处方结束时间" id="timeVal1" />
                </div>
            </div>
            <div class="zui-inline padding-left40">
                <label class="zui-form-label">检索</label>
                <div class="zui-input-inline">
                    <input class="zui-input wh180" placeholder="姓名/住院号" type="text" @input="searching($event.target.value)" v-model="param.parm" id="jsvalue"/>
                </div>
            </div>
        </div>
    </div>

    <!--循环列表begin-->
    <div class="zui-table-view" id="brRyList">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                    </th>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s"><span>住院号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>姓名</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m"><span>性别</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>出生日期</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m"><span>年龄</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>入院科室</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l"><span>入院日期</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l"><span>出院日期</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>病人费别</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>账户余额</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table v-if="jsonList.length" class="zui-table table-width50">
                <tbody>
                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                    :tabindex="$index"
                    @click="checkSelect([$index,'one','jsonList'],$event)"
                    class="tableTr2"
                    @dblclick="editRydj($index)">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"><!--序号--></div></td>
                    <td class="cell-s"><div class="zui-table-cell cell-s" v-text="item.zyh"><!--住院号--></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.brxm"><!--姓名--></div></td>
                    <td><div class="zui-table-cell cell-m" v-text="brxb_tran[item.brxb]"><!--性别--></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.csrq"><!--出生日期--></div></td>
                    <td><div class="zui-table-cell cell-m" v-text="item.nl==null ? '': item.nl+nldw_tran[item.nldw]"><!--年龄--></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ryksmc"><!--入院科室--></div></td>
                    <td><div class="zui-table-cell cell-l" v-text="item.ryrq"><!--入院时间--></div></td>
                    <td><div class="zui-table-cell cell-l" v-text="item.cyrq"><!--出院时间--></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.brfbmc"><!--病人费别--></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.zhye"><!--账户余额--></div></td>
                </tr>
                </tbody>
            </table>
            <p v-if="!jsonList.length" class="flex noData  text-center zan-border">暂无数据...</p>
        </div>
        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"
                        :tabindex="$index"
                        @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        class="tableTr2"
                        @dblclick="edit($index)">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
<div id="rydj_edit" v-show="edit"></div>
<script type="application/javascript" src="rydj.js"></script>