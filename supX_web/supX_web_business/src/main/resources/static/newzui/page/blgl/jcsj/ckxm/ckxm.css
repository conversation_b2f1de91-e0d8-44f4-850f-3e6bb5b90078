.tong-btn {
  width: auto;
  min-width: 72px;
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border: none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right: 10px;
}
.font12 {
  font-size: 12px !important;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}
.wh240 {
  width: 240px!important;
}
.wh182 {
  width: 182px !important;
}
.wh100 {
  width: 100px !important;
}
.wh66 {
  width: 66px !important;
}
.wh112 {
  width: 112px !important;
}
.wh120 {
  width: 120px !important;
}
.wh122 {
  width: 122px !important;
}
.wh138 {
  width: 138px !important;
}
.wh200 {
  width: 200px !important;
}
.wh220 {
  width: 220px !important;
}
.wh150 {
  width: 150px !important;
}
.wh1000 {
  width: 80% !important;
}
.wh50 {
  width: 50px !important;
}
.wh70 {
  width: 70px !important;
}
.width162 {
  width: 162px !important;
}
.wh160 {
  width: 160px !important;
}
.wh453 {
  width: 453px !important;
}
.wh247 {
  width: 243px !important;
  display: flex;
  justify-content: start;
  align-items: center;
}
.wh179 {
  width: 179px !important;
}
.wh59 {
  width: 59px !important;
}
.padd {
  padding: 0 !important;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-ed {
  background: #edf2f1 !important;
}
.color-green {
  color: #1abc9c !important;
  font-style: normal;
}
.color-dsh {
  color: #f3b169;
}
.color-ysh {
  color: #45e135;
}
.color-wtg {
  color: #ff4735;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.emrmb-left {
  width: 10%;
  float: left;
  max-width: 250px;
  min-width: 250px;
  overflow: auto;
  padding-top: 20px;
  min-height: 700px;
  background: #fff;
  position: relative;
  border: 1px solid #eee;
}
.emrmb-left .ksywhd-top {
  float: left;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 0 0 15px;
  flex-wrap: wrap;
  z-index: 11;
  background: #fff;
}
.emrmb-left .ksywhd-top .ksywhd-title {
  padding-left: 10px;
  cursor: pointer;
}
.emrmb-left .ksywhd-top.active {
  background: rgba(26, 188, 156, 0.1) !important;
  color: #1abc9c;
}
.emrmb-left .ksywhd-top .ckxm-list {
  display: block;
  width: 100%;
  float: left;
}
.emrmb-left .emrmb-list {
  width: 100%;
  float: left;
  margin-top: 10px;
}
.emrmb-left .emrmb-list .emrmb-yj {
  width: 100%;
  padding-left: 35px;
  box-sizing: border-box;
}
.emrmb-left .emrmb-list .emrmb-ej-detail {
  display: none;
}
.emrmb-left .emrmb-list .emrmb-ej-detail li {
  padding: 5px 0 0 10px;
  display: flex;
  cursor: pointer;
  justify-content: flex-start;
  align-items: center;
}
.emrmb-left .emrmb-list .emrmb-ej-detail li:last-child {
  margin-bottom: 10px;
}
.emrmb-left .emrmb-yj-title {
  display: block;
  cursor: pointer;
  width: 100%;
  margin-bottom: 10px;
  overflow: hidden;
}
.emrmb-right {
  width: 84%;
  float: right;
  min-width: 840px;
}
.zui-form .zui-inline {
  padding: 0 20px 0 60px;
}
.zui-form-label {
  width: 55px !important;
  line-height: 28px;
}
.tab-edit-list li label .dz-height {
  height: 70px !important;
  width: 76% !important;
  overflow: auto;
}
.width100 {
  width: 100% !important;
  float: left;
  height: 70px !important;
}
.width150 {
  width: 70px !important;
}
.ksys-side span {
  position: inherit;
}
.ksys-side .zui-input {
  width: 162px !important;
}
.validate {
  top: 57%;
  left: -54px;
}
@media screen and (max-width: 1366px) {
  .emrmb-right {
    width: auto;
    max-width: 77%;
    min-width: 840px;
  }
}
