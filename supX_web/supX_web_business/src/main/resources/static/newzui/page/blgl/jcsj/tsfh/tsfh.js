    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            title:'',
            LcList:{
                lcsjzid:''
            },
            zYList:[],
            totle:'',
            num:0,
            param: {
                page: '',
                rows: '',
                total: ''
            }
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增特殊符号';
                wap.open();
                wap.popContent={};

            },
            sx:function () {
              yjkmtableInfo.getData();
            },
            //删除
            del:function () {
                yjkmtableInfo.remove();
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    yjkmtableInfo.getData();
                }

            },
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            iShow:false,
            isTabelShow:false,
            flag:false,
            jsShow:false,
            ksList:[],
            hszList:[],
            LcList:[],
            ywckList:[],
            centent:'',
            isFold: false,
            title:'',
            ifClick:true,
            kbParent:null,
            num:0,
            csContent: {},
            jsonList: [],
            popContent: {
                'tybz': '',
            },
            //0-全院可见 1-科室可见 2-医生私有
            ckkjfw:{
                "0":"全院可见",
                "1":"科室可见",
                "2":"医生私有"
            },


        },
        watch:{
            kbParent:function () {
                var kbParent = '';
                for(var i = 0; i < wap.LcList.length; i++) {
                    if(this.LcList[i].kbParent == this.kbParent) {
                        kbParent = this.LcList[i].kbParent;

                    }

                }
            },
        },
        methods: {
            LcGetData:function () {
                $.getJSON('/actionDispatcher.do?reqUrl=EmrXtwhTsfh&types=query',function (json) {
                    if(json.a==0){
                        wap.LcList=json.d.list;
                        console.log(wap.LcList)

                    }

                });
            },
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //保存
            saveData: function () {
                if (wap.popContent.scbz) {
                    wap.popContent.scbz = '0'
                } else {
                    wap.popContent.scbz = '1'
                }
                if (wap.popContent.ckkjfw == null || wap.popContent.ckkjfw == '' || wap.popContent.ckkjfw == undefined) {
                    malert('可见范围不能为空')
                    return;
                }
                if (wap.popContent.kbName == null || wap.popContent.kbName == '' || wap.popContent.kbName == undefined) {
                    malert('节点名称不能为空')
                    return;
                }if (wap.kbParent == null || wap.kbParent == '' || wap.kbParent == undefined) {
                    malert('节点名称不能为空')
                    return;
                }if (wap.popContent.kbSeq == null || wap.popContent.kbSeq == '' || wap.popContent.kbSeq == undefined) {
                    malert('节点序号不能为空')
                    return;
                }
                wap.popContent.kbParent=wap.kbParent;
                    var json = JSON.stringify(wap.popContent);
                    this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhTsfh&types=save',
                        json).then(function (data) {
                        if (data.body.a == 0) {
                            yjkmtableInfo.getData();
                            wap.closes();
                            malert("保存成功", "top", "success");
                        } else {
                            malert("上传数据失败", "top", "defeadted");
                        }
                    }, function (error) {
                        console.log(error);
                    });

            }
        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            popContent: {},
            jsonList: [],//
            iShow:false,
            isShowpopL:false,
            totlePage:0,
            total:'',
            page:'',
            kmbm:'',
            LcList:[],
            kmmc:'',
            rows:10,
            param: {
                page:1,
                rows:10,
                sort: '',
                order: 'asc',
                parm:'',
            },
            //0-全院可见 1-科室可见 2-医生私有
            ckkjfw:{
                "0":"全院可见",
                "1":"科室可见",
                "2":"医生私有"
            },

        },
        methods: {

            getData: function () {
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }
                $.getJSON("/actionDispatcher.do?reqUrl=EmrXtwhTsfh&types=query&parm="+JSON.stringify(this.param),function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if(json.a==0){
                        yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                        yjkmtableInfo.jsonList = json.d.list;
                    }

                });
            },



            //删除
            remove: function() {
                var list = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                        var tsfhid={};
                        tsfhid.tsfhid=this.jsonList[i].tsfhid
                        list.push(tsfhid);
                    }
                }
                if(list.length == 0){
                    malert("请选中您要删除的数据","top","defeadted");
                    return false;
                }
                if(!confirm("请确认是否删除")){
                    return false;
                }
                var json='{"list":'+JSON.stringify(list)+'}'
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhTsfh&types=delete',
                    json).then(function (data) {
                    if(data.body.a == 0){
                        malert("删除成功","top","success")
                        yjkmtableInfo.getData();
                    } else {
                        malert("删除失败","top","defeadted")
                    }
                }, function (error) {
                    console.log(error);
                });

            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑词库模板'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },


        },


    });
    yjkmtableInfo.getData();
    wap.LcGetData();






