<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>组合费用对码</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../xmzb/xmzb.css"/>
    <link rel="stylesheet" href="fydm.css"/>
</head>
<style>


</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" >
    <div id="jyxm_icon">
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-baocunb paddr-r5" @click="savedata">保存</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
                <button class="tong-btn btn-parmary-b icon-gl paddr-r5" @click="guolu">过滤</button>
                <button class="tong-btn btn-parmary-b icon-yl paddr-r5"  @click="Print">预览</button>
                <button class="tong-btn btn-parmary-b icon-dysq paddr-r5">打印</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline">
                        <label class="zui-form-label">检索码</label>
                        <div class="zui-input-inline margin-f-l15">
                            <input class="zui-input wh180" v-model="allSearch" placeholder="请输入关键字" type="text"/>
                        </div>
                    </div>
                    <!--<button class="zui-btn btn-primary xmzb-db">查询</button>-->
                </div>
            </div>
        </div>
        <div class="xmzb-content padd-r-10 padd-l-10" style="background: #fff;">
            <div class="xmzb-content-right sjks-content-right bg-fff">
                <div class="content-right-top">
                    <i style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </i>
                    <i>组合费用编码</i>
                    <i>组合费用名称</i>
                    <i>检验项目编码</i>
                    <i>检验项目名称</i>
                    <i>拼音代码</i>
                </div>
                <ul class="content-right-list">
                    <li :tabindex="$index" @dblclick="dbEdit($index)" v-for="(item, $index) in jsonList" :key="item.fybm" :class="[{'table-hovers':isChecked[$index]}]" @click="checkSelect([$index,'one','jsonList'],$event)">
                        <!--@dblclick="delNow($event)"-->
                        <i style="width: 50px !important;">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                        </i>
                        <i v-text="item.fybm">001</i>
                        <i v-text="item.fymc">淋巴细胞比率</i>
                        <i ><input v-model="item.jyxmbm" class="zui-input wh200 tool-center" value="00111" type="text"/></i>
                        <i v-text="item.jyxmmc">24小时蛋白检测</i>
                        <i v-text="item.jyxmpy">FGHJ</i>
                    </li>
                </ul>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>

</div>
<div id="pop">
    <!--<transition name="pop-fade">-->
    <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
    <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
        <div class="layui-layer-content" >
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="colse()">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
        </div>
    </div>
    <!--</transition>-->
</div>
<div class="side-form ng-hide" style="width:485px;padding-top: 0;" id="brzcList" role="form">
    <div class="tab-message">
        <a v-text="sideTitle"></a>
        <a href="javascript:;" class="fr closex ti-close"
           @click="closes"></a>
    </div>
    <div class="ksys-side">
        <div class="zui-row">
            <div class="col-fm-12">
                <div class="col-fm-7 jiansuo">
                   <span>
                <i>检索码</i>
                   <input type="text" v-model="jyparam.parm" class="zui-input border-r4" placeholder="请输入"/>
                 </span>
                </div>
                <div class="xmzb-content-right pop-content sjks-content-right">
                    <div class="content-right-top">
                        <i>序号</i>
                        <i>项目名称</i>
                        <i>代码</i>
                    </div>
                    <ul class="content-right-list" >
                        <li @dblclick="doEdit($index)" v-for="(item, $index) in jyList" :key="item">
                            <span v-text="item.jyxmbm">001</span>
                            <span v-text="item.jyxmmc">白蛋白</span>
                            <span v-text="item.jyxmpy">FGHJ</span>
                           <!--  <span v-text="item">FGHJ</span>
                            <span v-text="item">FGHJ</span>
                            <span v-text="item">FGHJ</span> -->
                        </li>
                    </ul>
                </div>
            </div>


        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
        </div>
    </div>
    <!--<div class="filter">-->
    <!--<filter-select v-on:close="guanbi" v-on:save="baocun" v-if="isShow"></filter-select>-->
    <!--</div>-->

</div>
<div id="isTabel">
    <div class="pophide" :class="{'show':isShow}"></div>
    <div class="zui-form podrag  bcsz-layer zui-800 " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title ">过滤查询</div>
        <div class="guolv-xinzeng">
            <span class="layui-txt" @click="append()">新增一项</span>
            <i class="color-btn" @click="isShow=false"
               style="margin-top:-17px;width: 16px;height: 16px;display: inline-block;margin-left: 10px;float: right">×</i>
        </div>
        <div class="layui-layer-content">
            <div class=" layui-mad">
                <ul class="guolv-header guolv-style">
                    <li class="line">项目</li>
                    <li class="line">条件</li>
                    <li class="line">结果</li>
                    <li class="line">连接条件</li>
                    <li class="line">操作</li>
                </ul>
                <ui class="guolv-content" id="guo_append">
                    <div class="guolv-style guolv-bottom" v-for="(list,index) in appNum" :key="list.num">
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext"  name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <!--<div class="zui-select-inline">-->
                            <!--<input type="text" class="zui-input lsittext" name="input1" check="required" />-->
                            <!--<div class="zui-select-group" role="listbox">-->
                            <!--<ul class="inner">-->
                            <!--<li value="0">中国</li>-->
                            <!--<li value="1">印度</li>-->
                            <!--<li value="2">安道尔</li>-->
                            <!--<li value="3">老挝</li>-->
                            <!--</ul>-->
                            <!--</div>-->
                            <input  class="zui-input"/>

                            <!--</div>-->
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <span class="icon-sc" @click="sc(index)"></span>
                        </li>
                    </div>

                </ui>
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="save()">保存</button>
        </div>
    </div>
</div>

<script src="fydm.js"></script>
</body>
</html>
