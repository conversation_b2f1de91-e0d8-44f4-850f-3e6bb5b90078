<link href="/page/ybjk/gzwxhy/ybyw/sjsc/sjsc.css" rel="stylesheet" type="text/css">

<div class="cd_014" id="cd_014" style="height: 90%;">
    <div class="ksys-side">
        <ul class="tab-edit-list flex-start">
            <li>
                <i>个人编号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac001" disabled="disabled"/>
            </li>
            <li>
                <i>姓&emsp;&emsp;名</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac003" disabled="disabled"/>
            </li>
            <li>
                <i>性&emsp;&emsp;别</i>
                <select-input @change-data="resultChange" id="xb" disable
                              :child="brxb_tran" :index="grxxJson.aac004" :val="grxxJson.aac004"
                              :name="'grxxJson.aac004'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>年&emsp;&emsp;龄</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.akc023" disabled="disabled"/>
            </li>
            <li>
                <i>身&nbsp;&nbsp;份&nbsp;证号&emsp;&emsp;码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac002" disabled="disabled"/>
            </li>
            <li>
                <i>出生日期</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac006" disabled="disabled"/>
            </li>
            <li>
                <i>个人帐户<br>种&emsp;&emsp;类</i>
                <input type="text" class="zui-input  background-h" v-model="insutype_tran[grxxJson.ykc303]" disabled="disabled"/>
            </li>
            <li>
                <i>个人账户余&emsp;&emsp;额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ykc194" disabled="disabled"/>
            </li>
            <li class="zflb" v-if="gsdataset_show">
                <i class="color-wtg">工伤诊断</i>
                <select-input @change-data="commonResultChange" :not_empty="true"
                              :child="gsdataset_tran" :index="'alc022'" :index_val="'yke109'" :val="grxxJson.yke109"
                              :name="'grxxJson.yke109'" :search="true">
                </select-input>
            </li>
            <li class="zflb" v-if="datasetyka026_show">
                <i class="color-wtg">特慢病</i>
                <select-input @change-data="commonResultChange" :not_empty="true"
                              :child="datasetyka026_tran" :index="'bkc117'" :index_val="'bkc014'" :val="grxxJson.bkc014"
                              :name="'grxxJson.bkc014'" :search="true">
                </select-input>
            </li>
            <li class="zflb">
                <i class="color-wtg">支付类别</i>
                <select-input @change-data="resultChange" id="aka130"
                              :child="cd_aka130_tran" :index="grxxJson.aka130" :val="grxxJson.aka130"
                              :search="true" :name="'grxxJson.aka130'" :not_empty="true">
                </select-input>
            </li>
            <li class="zflb">
                <i class="color-wtg">公务员类别</i>
                <select-input disable @change-data="resultChange" id="ykc177"
                              :child="cd_ykc117_tran" :index="grxxJson.ykc177" :val="grxxJson.ykc177"
                              :search="true" :name="'grxxJson.ykc177'" :not_empty="true">
                </select-input>
            </li>
            <li class="zflb">
                <i class="color-wtg">是否异地</i>
                <select-input  @change-data="resultChange" id="sfyd"
                              :child="sfydList" :index="sfyd" :val="sfyd"
                              :search="true" :name="'sfyd'" :not_empty="true">
                </select-input>
            </li>
        </ul>
		
    </div>
	
    <div class="zui-row buttonbox">
<!-- 		<button class="tong-btn btn-parmary-f2a xmzb-db paddr-r5" @click="loadDzpz()">电子凭证</button>
       <button class="tong-btn btn-parmary-f2a xmzb-db paddr-r5" @click="loadqd()">签到</button>-->
        <button class="tong-btn btn-parmary-f2a xmzb-db paddr-r5" @click="load()">读卡</button>
        <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="enter()">引入</button>
    </div>
	
	<model  :model-show="false" @result-close="dtxx=false" v-if="dtxx" style="width: 700px;max-width: 700px;" :title="'身份信息列表'">
	            <div class="bqcydj_model">
	                <div class="zui-table-header">
	                    <table class="zui-table table-width50">
	                        <thead>
	                        <tr>
	                            <th class="wh50 text-center">
	                                <input-checkbox>
	                                </input-checkbox>
	                            </th>
	                            <th class=" wh80 text-center">
	                                <div class="zui-table-cell  wh80"><span>余额</span></div>
	                            </th>
	                            <th class=" wh179 text-center">
	                                <div class="zui-table-cell  wh179"><span>单位名称</span></div>
	                            </th>
								<th class=" wh179 text-center">
								    <div class="zui-table-cell  wh179"><span>保险类别</span></div>
								</th>
								<th class=" wh50 text-center">
								    <div class="zui-table-cell  wh50"><span>公务员标志</span></div>
								</th>
	                        </tr>
	                        </thead>
	                    </table>
	                </div>
	                <div class="zui-table-body zuiTableBodyHzlist" @scroll="scrollTable($event)">
	                    <table class="zui-table">
	                        <tbody>
	                        <tr :id="'tr'+$index" v-for="(item, $index) in insuinfo" >
	                            <td class="wh50 text-center">
	                                <input-checkbox @result="reZxCheckChange" :list="'insuinfo'" :type="'some'" :which="$index" :val="isZxChecked[$index]">
	                                </input-checkbox>
	                            </td>
	                            <td class="wh80 text-center">
	                                <div class=" zui-table-cell  wh80">
	                                    {{item.balc}}
	                                </div>
	                            </td>
								<td class="wh179 text-center">
								    <div class=" zui-table-cell  wh179" style="overflow:hidden;">
								        {{item.emp_name}}
								    </div>
								</td>
								
	                            <td class="wh179 text-center">
	                                <div class=" zui-table-cell  wh179">
	                                    {{insutype_tran[item.insutype]}}
	                                </div>
	                            </td>
								<td class="wh50 text-center">
								    <div class=" zui-table-cell  wh50" v-if="item.cvlserv_flag == '1'">
								        是
								    </div>
									<div class=" zui-table-cell  wh50" v-else>
									    否
									</div>
								</td>
	                        </tr>
	                        </tbody>
	                    </table>
	                </div>
	                <div class="flex-container flex-wrap-w">
	                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
	                        <div class="zui-input-inline wh180">
	                            <button v-waves style="margin-top: 5px;" class="tong-btn btn-parmary btn-parmary-not
			                    	yellow-bg" @click="saveZxXx">确定</button>
	                        </div>
	                    </div>
	                </div>
	            </div>
	        </model>
	
</div>




<script type="application/javascript" src="/newzui/pub/js/insuranceGbUtils.js"></script>
<script type="application/javascript" src="/newzui/page/mzsf/sfjs/mzsf/insurancePort/014cdyhybgb/uuid.js"></script>
<script type="application/javascript" src="/newzui/page/mzsf/sfjs/mzsf/insurancePort/014cdyhybgb/xmltojson.js"></script>
<script type="application/javascript" src="/newzui/page/mzsf/sfjs/mzsf/insurancePort/014cdyhybgb/014cdyhyb.js?v=20210121"></script>

