<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>病人接诊</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <style>
        .zui-form .select-right .zui-select-inline{
            min-width: 48px;
        }
    </style>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10" style="overflow: auto;">
<div class="wrapper" style="height: auto;">
    <div class="panel">
        <div class="col-x-12 rysx_bottom_list" id="searchLeft">
            <div class="panel-head border-bottom background toolMenu">
                <div class="zui-row">
                    <div class="col-x-7">
                        <div class="zui-input-inline zui-date wh150">
                            <select-input @change-data="resultChangeItem"
                                          :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.ksbm"
                                          :name="'popContent.ksmc'" :search="false">
                            </select-input>
                        </div>
                        <div class="zui-input-inline zui-date wh150">
                            <i class="datenox fa-calendar"></i>
                            <input type="text" name="phone" class="zui-input todats" id="startRq" placeholder="就诊日期"/>
                        </div>
                        <div class="zui-input-inline zui-date wh150">
                            <i class="datenox fa-calendar"></i>
                            <input type="text" name="phone" class="zui-input todate" id="endtRq" placeholder="结束日期"/>
                        </div>
                        <div class="zui-input-inline zui-date wh150">
                            <i class="datenox fa-search"></i>
                            <input :value="text" class="zui-input" @input="searching(null,$event.target.value)" @keyDown="changeDown($event)" placeholder="姓名，挂号序号">
                            <search-table :message="searchCon" :selected="selSearch"
                                          :them="them" :them_tran="them_tran" :page="page"
                                          @click-one="checkedOneOut" @click-two="selectOne">
                            </search-table>
                        </div>
                    </div>
                    <div class="col-x-4" style="margin: -7px 0 0 21px">
                        <div class=" zui-date wh250">排序方式
                            <div style="display:inline-block;position: relative" class="zui-table-cell"><input class="zui-radio"  type="radio" id="sort" name="sort" @click="pxfsClick('ghxh')"><label for="sort" style="vertical-align: unset"><span style="margin: 0 5px;">挂号</span></label></div>
                            <div style="display:inline-block;position: relative" class="zui-table-cell"><input class="zui-radio"  type="radio" id="xsort" name="sort" @click="pxfsClick('brxm')"><label for="xsort" style="vertical-align: unset"><span style="margin: 0 5px;">姓名</span></label></div>
                            <div style="display:inline-block;position: relative" class="zui-table-cell"><input class="zui-radio"  type="radio" id="jsort" name="sort" @click="pxfsClick('jzbz')"><label for="jsort" style="vertical-align: unset"><span style="margin: 0 5px;">就诊</span></label></div>
                        </div>
                        <div class=" zui-date wh250">过&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;滤
                            <div style="display:inline-block;position: relative" class="zui-table-cell"><input class="zui-radio"  type="radio" id="parm" name="parm" @click="glClick()"><label for="parm" style="vertical-align: unset"><span style="margin: 0 5px;">全部</span></label></div>
                            <div style="display:inline-block;position: relative" class="zui-table-cell"><input class="zui-radio"  type="radio" id="yparm" name="parm" @click="glClick()"><label for="yparm" style="vertical-align: unset"><span style="margin: 0 5px;">已读</span></label></div>
                            <div style="display:inline-block;position: relative" class="zui-table-cell"><input class="zui-radio"  type="radio" id="wparm" name="parm" @click="glClick()"><label for="wparm" style="vertical-align: unset"><span style="margin: 0 5px;">未读</span></label></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="brxxList" class="col-x-12 panel-head  background ">
            <div class="col-x-4" style="min-height:200px;">
                <div style="margin-bottom: 10px" v-for="(item, $index) in jsonList" @click="checkOne($index),edit($event)" :class="{'tableTrSelect':isChecked == $index}"
                     :brid="item.brid" :ghxh="item.ghxh">
                    <img src="/pub/image/man.png">
                    <div>
                        <span v-text="item.brxm"></span>
                        <span>( <i v-text="item.brnl"></i>&nbsp;&nbsp;{{nldw_tran[item.nldw]}} )</span>
                        <span v-text="item.ghxh"></span>
                    </div>
                </div>
            </div>
            <div class="col-x-8">
                <div class="zui-row">
                        <button @click="go(0)" class="zui-btn zui-no-border btn-default" :class="{'btn-primary':indexs==0}">电子处方</button><button @click="go(1)" class="zui-no-border zui-btn btn-default" :class="{'btn-primary':indexs==1}">报告单</button><button @click="go(2)" class="zui-no-border zui-btn btn-default" :class="{'btn-primary':indexs==2}">病人信息</button><button @click="go(3)" class="zui-no-border zui-btn btn-default" :class="{'btn-primary':indexs==3}">门诊病历</button>
                </div>
                <div class="qiehuan">
                    <div class="zui-rom" v-show="indexs==0">

                    </div>
                    <div class="zui-row" v-show="indexs==2">
                        <div class="zui-field-title"><label>基本信息</label> </div>
                        <div class="zui-form">
                            <div class="zui-inline  col-fm-4">
                                <label class="zui-form-label">姓名</label>
                                <div class="zui-input-inline">
                                    <input type="number" class="zui-input" v-model="zcxx.brxm" @keydown="nextFocus($event)" data-notEmpty="false">
                                </div>
                            </div>
                            <div class="zui-inline select-right col-fm-4">
                                <label class="zui-form-label">年龄</label>
                                <div class="zui-input-inline">
                                    <input type="text" type="number" v-model="zdxx.brnl"  @keydown="nextFocus($event)"
                                           placeholder="" class="zui-input" name="input1" />
                                </div>
                                <div class="zui-select-none" style="min-width: 48px;">
                                    <select-input @change-data="resultChange" :not_empty="false" :child="nldw_tran" :index="zdxx.nldw" :val="zdxx.nldw" :name="'zdxx.nldw'"></select-input>
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">联系人姓名</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone"class="zui-input" v-model="zcxx.lxrxm" data-notEmpty="false" @keydown="nextFocus($event)" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">联系人关系</label>
                                <select-input @change-data="resultChange" :not_empty="false" :child="lxrgxList" :index="'lxrgxmc'" :index_val="'lxrgxbm'" :val="zcxx.lxrgx" :search="true" :name="'zcxx.lxrgx'"></select-input>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">联系人电话</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zcxx.lxrdh" data-notEmpty="false" @keydown="nextFocus($event)" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">性别</label>
                                <select-input @change-data="resultChange" :not_empty="true" :child="brxb_tran" :index="zcxx.brxb" :val="zcxx.brxb" :name="'zcxx.brxb'"></select-input>
                            </div>

                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">职业</label>
                                <select-input @change-data="resultChange" :not_empty="true" :child="zyList" :index="'zymc'" :index_val="'zybm'" :val="zcxx.zybm" :name="'zcxx.zybm'" :index_mc="'zybmmc'" :search="true"></select-input>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">现住址</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zcxx.jzdmc" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">联系电话</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zcxx.sjhm" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">身高(cm)</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zcxx.sg" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">体重(Kg)</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zcxx.tz" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                        </div>
                        <div class="zui-field-title"><label>诊断信息</label> </div>
                        <div class="zui-form">
                            <div class="zui-inline  col-fm-4">
                                <label class="zui-form-label">初诊/复诊</label>
                                <select-input @change-data="resultChange" :not_empty="true" :child="cfz_tran" :index="zdxx.sffz" :val="zdxx.sffz" :name="'zdxx.sffz'"></select-input>
                            </div>
                            <div class="zui-inline  col-fm-4">
                                <label class="zui-form-label">血糖</label>
                                <div class="zui-input-inline">
                                    <input style="width:33.5%;display: inline-block;" type="text" type="number" v-model="zdxx.xtms" @keydown="nextFocus($event)" data-notEmpty="false"  placeholder="" class="zui-input" name="input1" />
                                    <span >/</span>
                                    <input style="width:33.5%;display: inline-block;" type="text" type="number" v-model="zdxx.xt" @keydown="nextFocus($event)" data-notEmpty="false" placeholder="" class="zui-input" name="input1" />
                                    <span>mmol/L</span>
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">血压</label>
                                <div class="zui-input-inline">
                                    <input style="width:34.5%;display: inline-block;" type="text" name="phone"class="zui-input" v-model="zdxx.xySsy" @keydown="nextFocus($event)" data-notEmpty="false" />
                                    <span >/</span>
                                    <input style="width:34.5%;display: inline-block;" type="text" name="phone"class="zui-input" v-model="zdxx.xySzy" @keydown="nextFocus($event)" data-notEmpty="false" />
                                    <span>mmHg</span>
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">体温</label>
                                <div class="zui-input-inline">
                                    <input style="width: 93%;display: inline-block;" type="text" name="phone" class="zui-input" v-model="zdxx.tw" @keydown="nextFocus($event)" data-notEmpty="false" />℃
                                </div>
                                </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">发病时间</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input fbsj" v-model="zdxx.fbrq" @blur="dateForVal($event, 'zdxx.fbrq')" data-notEmpty="false" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">主要症状、体征</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input fbsj" v-model="zdxx.zyzztz" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                                   </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">疾病</label>
                                <input data-notEmpty="false"  class="zui-input" :value="jbbmContent.jbmc"
                                       @keydown="changeDown($event,'jbbm','jbbmContent','searchCon')"
                                       @input="change(null,'jbbm',$event.target.value)" data-notEmpty="false">
                                <search-table :message="searchCon" :selected="selSearch" :them="them"
                                              :page="page" @click-one="checkedOneOut" @click-two="selectJbbm">
                                </search-table>
                                </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">其他诊断</label>
                                <input data-notEmpty="false"  class="zui-input" :value="qtzdbmContent.jbmc"
                                       @keydown="changeDown($event,'qtzd','qtzdbmContent','jbsearchCon')"
                                       @input="change(false,'qtzd',$event.target.value)">
                                <jbsearch-table :message="jbsearchCon" :selected="selSearch"
                                                :them="jbthem" :page="page"
                                                @click-one="checkedOneOut" @click-two="selectQtzd">
                                </jbsearch-table>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">医保诊断</label>
                                <input data-notEmpty="true"  class="zui-input" :value="ybjbbmContent.mc"
                                       @keydown="changeDown($event,'ybjb','ybjbbmContent','ybsearchCon')"
                                       @input="change(false,'ybjb',$event.target.value)">
                                <ybsearch-table :message="ybsearchCon" :selected="selSearch"
                                                :them="ybthem" :page="page"
                                                @click-one="checkedOneOut" @click-two="selectYbjbbm">
                                </ybsearch-table>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">对应处理</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zdxx.dycl" @keydown="nextFocus($event)" data-notEmpty="false"/>
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">是否传染病</label>
                                <select-input @change-data="resultChange" :not_empty="true"
                                              :child="istrue_tran" :index="zdxx.sfcrb" :val="zdxx.sfcrb" :name="'zdxx.sfcrb'">
                                </select-input>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">是否过敏</label>
                                <select-input @change-data="resultChange" :not_empty="true" :child="istrue_tran" :index="zdxx.sfgm" :val="zdxx.sfgm" :name="'zdxx.sfgm'">
                                </select-input>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">过敏史</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zdxx.gms" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">脉搏</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zdxx.mb" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">呼吸</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zdxx.hx" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">心率</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zdxx.xl" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                            <div class="zui-inline col-fm-4">
                                <label class="zui-form-label">备注说明</label>
                                <div class="zui-input-inline">
                                    <input type="text" name="phone" class="zui-input" v-model="zdxx.bzsm" @keydown="nextFocus($event)" data-notEmpty="false" />
                                </div>
                            </div>
                        </div>
                        <div class="brxxBtu" style="text-align: right">
                            <button class="zui-btn zui-no-border btn-default btn-primary" @click="save">保存修改</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="brjz.js"></script>
</body>
</html>
