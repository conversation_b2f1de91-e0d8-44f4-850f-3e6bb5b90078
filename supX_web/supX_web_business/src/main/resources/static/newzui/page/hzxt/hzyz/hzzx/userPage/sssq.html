<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link href="userPage/sssq.css" rel="stylesheet">
</head>
<body class="body skin-default height  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class=" padd-r-10 contextInfo  padd-l-10" id="sssq">
  <div class="flex-container flex-wrap-w">
      <div class="box-content" :class="{'active':index==num}" @click="getSssqData(item.sssqdh,'pageState',index)" v-for="(item,index) in lsYzList">
          <div class="flex-container padd-b-5 flex-jus-sb">
              <p class="text-000 padd-r-20">{{item.sssqdh}}</p>
              <p class="text-000">{{item.sqrq | initDate}}</p>
          </div>
          <div class="flex-container padd-b-5 flex-jus-sb">
              <p class="text-000 padd-r-20">{{item.ksmc}}</p>
              <p class="text-000">{{item.jhrq | initDate}}</p>
          </div>
          <div class="flex-container padd-b-5 text-000">
              {{item.zsmc}}
          </div>
          <div class="flex-container">
              <p class="text-FF0000" v-if="item.aprq!=null">排：{{item.aprq | initDate1}} {{item.ssj}}</p>
          </div>
      </div>
  </div>
    <div class="flex-container flex-wrap-w">
        <div :id="listenSsksList" class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5 whiteSpace">手术科室</span>
            <select-input :data-notEmpty="true" class="wh180"  @change-data="resultChangeMc" :not_empty="true" :child="ssksList"
                          :index="'ksmc'" :index_mc="'ssksmc'" :index_val="'ksbm'" :val="pageState.ssks"  :name="'pageState.ssks'"
                          :search="true" :phd="''">
            </select-input>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5 whiteSpace">手术类型</span>
            <span class="padd-r-5 padd-l-10 whiteSpace">急诊</span>
            <div class="position padd-r-5">
            <input type="radio" id="four" name="two" v-model="pageState.sslx" value="0" class="zui-radio">
            <label for="four" class="padd-r-5"></label>
            </div>
            <span class="padd-r-5 padd-l-10 whiteSpace">择期</span>
            <div class="position padd-r-5">
            <input type="radio" id="four1" name="two" v-model="pageState.sslx" value="1" class="zui-radio">
            <label for="four1" class="padd-r-5"></label>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5 whiteSpace">申&emsp;&emsp;请<br/>手术日期</span>
            <div class="zui-input-inline wh180">
                <input   class="zui-input" :value="pageState.sqrq |initDate2" id="sqTime" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5 whiteSpace">计&emsp;&emsp;划<br/>手术日期</span>
            <div class="zui-input-inline wh180">
                <input   class="zui-input" :value="pageState.jhrq | initDate2"  id="jhTime" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5 whiteSpace">术前诊断</span>
            <div class="zui-input-inline wh180">
                <input :data-notEmpty="true"  class="zui-input" @keydown="nextFocus($event)" v-model="pageState.sqzd" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5 whiteSpace">手术名称</span>
            <div class="zui-input-inline wh180">
                <input data-notEmpty="true"  class="zui-input" id="zsmc" type="text" v-model="pageState.zsmc"
                       @keyup="changeDown($event,'searchCon')" @input="change1(false,$event.target.value)"
                       placeholder="手术编码">
                <search-table :message="searchCon" :selected="selSearch" :page="page" :them="them" :them_tran="them_tran"
                              @click-one="checkedOneOut" @click-two="selectOne">
                </search-table>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5 whiteSpace">血压</span>
            <div class="flex-container flex-align-c">
                <input   class="zui-input wh66" @keydown="nextFocus($event)" v-model="pageState.ssxyg" type="text"/>
                <span class="padd-r-5 padd-l-5">/</span>
                <input   class="zui-input wh66" @keydown="nextFocus($event)" v-model="pageState.ssxyd" type="text"/>
                <span class="padd-l-5">mmHg</span>
            </div>
        </div>
    </div>
    <div class="flex-container flex-wrap-w">
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">术&emsp;&emsp;者</span>
            <div class="zui-input-inline wh180">
                <select-input :data-notEmpty="true"  @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.ssz" :name="'pageState.ssz'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">一&emsp;&emsp;助</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange"  :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.sszs1" :name="'pageState.sszs1'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">二&emsp;&emsp;助</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange"  :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.sszs2" :name="'pageState.sszs2'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">麻醉方式</span>
            <div class="zui-input-inline wh180">
                <select-input :data-notEmpty="true"  @change-data="resultChange" :not_empty="true" :child="zybmList"
                              :index="'zymc'" :index_val="'zybm'" :val="pageState.mzfs" :name="'pageState.mzfs'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
    </div>
    <div class="flex-container flex-wrap-w flex-wrap-w">
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">麻醉医生</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.mzys" :name="'pageState.mzys'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">申请医生</span>
            <div class="zui-input-inline wh180">
                <select-input :data-notEmpty="true"  @change-data="resultChangeMc" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.sqys" :name="'pageState.sqys'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">审核医生</span>
            <div class="zui-input-inline wh180">
                <select-input :data-notEmpty="true"  @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.shys" :name="'pageState.shys'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">感染类别</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.grlb" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">合 并 症</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown="nextFocus($event)" v-model="pageState.hbz" type="text"/>
            </div>
        </div>
        <div class=" flex-container padd-b-20 padd-t-10 padd-r-20 flex-align-c padd-l-20">
            <span class="padd-r-5">术前steward评分</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" @keydown.13="SaveSssq()" data-notEmpty="true" placeholder="术前steward评分" v-model="pageState.stewardSq" type="number"/>
            </div>
        </div>
    </div>
    <div class="flex-container flex-wrap-w flex-wrap-w">
        <div class=" flex-container  padd-b-20 padd-t-10 padd-r-20" style="width: 100%">
            <span class="padd-r-5 whiteSpace">备&emsp;&emsp;注</span>
                <textarea class="zui-input" @keydown.13="SaveSssq()" v-model="pageState.bz" style="height: 100px"></textarea>
        </div>
    </div>
    <div class="zui-table-tool padd-r-10 flex-jus-e flex-align-c zui-border-bottom flex-container font-14-654">
        <button v-waves class="root-btn btn-parmary"  @click="add()">新增</button>
        <button v-waves class="root-btn btn-parmary"  :disabled="disable" @click="SaveSssq()">保存</button>
        <button v-waves class="root-btn  btn-parmary-d2" @click="zf()">作废</button>
        <button v-waves class="root-btn  btn-parmary-f2a1" @click="printData()">打印</button>
    </div>
</div>

<script type="text/javascript" src="userPage/sssq.js"></script>
</body>
</html>
