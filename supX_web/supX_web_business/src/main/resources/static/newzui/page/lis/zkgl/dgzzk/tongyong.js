
var currentLine = -1;
document.onkeydown = function(e)
{
    e = window.event || e;
    switch(e.keyCode)
    {
        case 38:
            currentLine=currentLine-1;
            changeItem();
            break;
        case 40:
            currentLine=currentLine+1;
            changeItem();
            break;
        default:
            break;
    }
}
function selectTR()
{
    // currentLine=window.event.srcElement.parentElement.rowIndex;
    //alert(currentLine);
    // changeItem()

}
//改变选择项目
function changeItem(obj)
{

    if(document.all)
        var it = document.getElementById("ice").children[0];
    else
        var it = document.getElementById("ice");

    for(i=0;i<it.rows.length;i++)
    {
        it.rows[i].className = " ";
    }
    if(currentLine>=0){
        // currentLine = it.rows.length - 1;
        if(currentLine == it.rows.length)
            currentLine = 0;
        it.rows[currentLine].className = "table-hovers";
    }else{
        it.rows[$(obj).index()].className = "table-hovers";
    }
    // currentLine=window.event.srcElement.parentElement.rowIndex;

}

function shenhe(obj) {
     var that=$(obj).find("input[type='hidden']").val();
    var inputs= $(obj).parents('tr').find($("input[name='edit']"));

     alert(that);
     if(that==0){
         inputs.each(function(){
             $(this).attr("disabled",false)
             $(this).addClass("disableds");
         });
         $(obj).find("input[type='hidden']").val(1);
        $(obj).addClass('icon-hs-hover');
     }else{
         inputs.each(function(){
             $(this).attr("disabled",false)
             $(this).removeClass("disableds");
         });
         $(obj).find("input[type='hidden']").val(0);
         $(obj).removeClass('icon-hs-hover');
     }



}
function baocun() {
    // alert(23565);
    var inputs = $("input[name='edit']");
    inputs.each(function(){
        $(this).attr("disabled",true)
        $(this).removeClass("disableds");

    });
}

function conhover(obj) {
    currentLine=$(obj).index()
    changeItem(obj);
    $(obj).addClass('table-hovers');
    $(obj).siblings().removeClass('table-hovers');
}
