<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script type="application/javascript" src="/pub/top.js"></script>
    <title>数据上传</title>
    <link rel="stylesheet" href="/page/xtwh/xtpz/mzpjgs/mzpjgs.css" media="print"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="/page/mzys/zlgl/brjz/brjz.css">
    <link rel="stylesheet" href="sjsc.css">
</head>
<body style="overflow: hidden">

<!-- <object id="csharpActiveX" classid="clsid:0F1B2491-0867-4B7F-A300-76826541DAD6" 
    	style="width:0px;height: 0px;" ></object> -->
    
<div class="printArea printShow"></div>
<div class="brjz ">
    <div class="left_tab1 printHide">
        <div class="searchLeft_tab1" id="searchLeft">
            <div>
                <span>当前科室：</span>
                <div>
                    <select id="ksList" v-model="ksbm" @change="ksChange">
                        <option v-for="option in ksList" :value="option.ksbm" v-text="option.ksmc"></option>
                    </select>
                </div>
            </div>
            <div>
                <span>搜&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;索：</span>
                <div>
                <input v-model="text" @keyDown="ssBrList($event)" placeholder="姓名，挂号序号">
                   <!--  <input v-model="text" @input="searching()" @keyDown="changeDown($event)" placeholder="姓名，挂号序号">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table> -->
                    <span class="fa fa-search leftSearchIcon"></span>
                </div>
            </div>
            <!-- <div>
                <span>查询方式：</span>
                <div>
                <select  v-model="qhyb_cxfs" >
                        <option v-for="option in qhyb_cxfs_Arr" :value="option.key" v-text="option.value"></option>
                    </select>
                </div>
            </div> -->
            <div>
                <span>业务类型：</span>
                <div>
                <select  v-model="qhyb_ywlx" >
                        <option v-for="option in qhyb_ywlx_Arr" :value="option.key" v-text="option.value"></option>
                    </select>
                </div>
            </div>
            
            <div>
                <span>是否结算：</span>
                <div>
                <select  v-model="qhyb_isjs">
                        <option v-for="option in qhyb_isjs_Arr" :value="option.key" v-text="option.value"></option>
                    </select>
                </div>
            </div>
            <div>
          <!--   <button @click="uploadSbk">读取社保卡信息</button> -->
            <button @click="uploadRyxx">刷新</button>
            <!-- <button @click="updatePassword">updatePassword</button> -->
            	<!-- 
        		<button @click="socketSend">socketSend</button> -->
            </div>
        </div>

        <!-- 病人列表 -->
        <div class="personList" id="brxxList" style="overflow:auto;height:calc(100% - 80px);">
            <div style="font-size: 14px">患者信息</div>
            <div v-for="(item, $index) in jsonList" @click="checkOne($index)" @dblclick="edit($index)" :brid="item.brid" :ghxh="item.ghxh">
                <img src="/pub/image/man.png">
                <div>
                    <span v-text="item.BRXM"></span>
                    <span>（<i v-text="item.NL"></i>&nbsp;&nbsp;{{nldw_tran[item.NLDW]}} )</span>
                     <span style="color:red;" v-if= "item.SERIAL_NO  != undefined">已登记</span>
                    <span v-text="item.ZYH"></span>
                   
                </div>
            </div>
        </div>
    </div>

    <div class="right">
        <div class="contextDiv">
            <div class="nh-menu printHide">
                <span v-if="yb_isjs == '0'" @click="loadCon('ybdj'),which=0" :class="{selected: which==0}">医保登记</span>
                <span v-if="yb_isjs == '0'" @click="loadCon('wscjl'),which=1" :class="{selected: which==1}">未上传记录</span>
                <span v-if="yb_isjs == '0'" @click="loadCon('ybqd'),which=2" :class="{selected: which==2}">上传到医保清单</span>
                <span @click="loadCon('yjs'),which=3" :class="{selected: which==3}" style="border-right: 1px solid #aaaaaa">预结算</span>
               <div class="nh-screen">
               		<span style="color:green;margin-right:20px;font-weight: 800;font-size: 15px;" v-text="selectedHzxx"></span>
               		<span v-if="qhyb_conn=='onopen'" style="color:green">医保连接成功！</span>
               		<span v-if="qhyb_conn=='onclose'" style="color:red">医保未连接,请重新开启本页面！</span>
               		<span v-if="qhyb_conn=='onerror'" style="color:red">医保未连接错误,请重新开启本页面！</span>
               		
               		<span v-if="qhyb_login_type=='success'" style="color:green">成功登录到医保！</span>
               		<span v-if="qhyb_login_type=='error'" style="color:red">登录到医保失败！</span>
               		<span v-text=""></span>
                    <!-- <div>
                        <label><input type="checkbox" />自动上传</label>
                    </div>
                    <div>
                        <label><input type="checkbox" />出院病人</label>
                    </div>
                    <div>
                        <label><input type="checkbox" />未对码</label>
                    </div>
                    <div>
                        <label><input type="checkbox" />已对码</label>
                    </div> -->
                </div>
            </div>
            <div class="context printShow">
                <div id="scnh">
                    <div class="page_div ybdj"></div>
                    <div class="page_div wscjl"></div>
                    <div class="page_div ybqd"></div>
                    <div class="page_div yjs"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script type="application/javascript" src="sjsc.js"></script>
</html>