<div id="mxfydy" class="contextInfo">
	<div class="panel box-fixed" style="top:45px;">
		<div class="tong-top">
			<button class="tong-btn btn-parmary" @click="getData"><span class="fa fa-refresh padd-r-5"></span>刷新</button>
			<button class="tong-btn btn-parmary-b" @click="saveData"><span class="fa fa-save padd-r-5"></span>保存</button>
			<button class="tong-btn btn-parmary-b" @click="remove"><span class="fa fa-refresh padd-r-5"></span>清空</button>
			<button class="tong-btn btn-parmary-b" @click="szDate"><span class="fa fa-plus padd-r-5"></span>设置</button>
		</div>
		<div class="tong-search">
			<div class="zui-form">
				<div class="zui-inline ">
					<label class="zui-form-label">费用类别</label>
					<div class="zui-input-inline wh120 ">
						<select-input class="height36" @change-data="resultChange" :not_empty="false"
									  :child="fylbList" :index="'lbmc'" :index_val="'lbbm'"
									  :val="fylb" :search="true" :name="'fylb'">
						</select-input>
					</div>
				</div>
				<div class="zui-inline ">
					<label class="zui-form-label">病案类别</label>
					<div class="zui-input-inline wh120">
						<select-input  class="height36"  @change-data="resultChange" :not_empty="false"
									  :child="balbList" :index="'zymc'" :index_val="'zybm'" :val="balb"
									  :name="'balb'" :search="true">
						</select-input>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="zui-table-view ybglTable" id="utable1" z-height="full" style="margin-top:147px; padding: 0 10px;border: none;width: 100%;float: left;">
		<div class="zui-table-header">
			<table class="font-14 zui-table table-width50">
				<thead>
				<tr>
					<th class="cell-m">
						<input-checkbox  @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                            </input-checkbox>
					</th>
					<th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>费用项目编码</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>费用项目名称</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>费用类别</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>费用规格</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>费用单价</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>病案细类</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>是否抢救</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>院内会诊</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>院际会诊</span></div></th>
				</tr>
				</thead>
			</table>
		</div>
		<div class="zui-table-body" @scroll="scrollTable($event)">
			<table class="zui-table table-width50">
				<tbody>
				<tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
					<td class="cell-m">
						<input-checkbox @result="reCheckBox" :list="'jsonList'"
										:type="'some'" :which="$index"
										:val="isChecked[$index]">
						</input-checkbox>
					</td>
					<td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.mxfybm"></div></td>
					<td>
						<div class="zui-table-cell cell-s" v-text="item.mxfymc">
						</div>
					</td>
					<td>
						<div class="zui-table-cell cell-s" v-text="item.pydm">
						</div>
					</td>
					<td>
						<div class="zui-table-cell cell-s" v-text="item.fylbmc">
						</div>

					</td>
					<td>
						<div class="zui-table-cell cell-s " v-text="item.fygg">
					</div>
					</td>
					<td><div class="zui-table-cell cell-s " v-text="item.fydj">
					</div>
					</td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
									  :child="balbList" :index="'zymc'" :index_val="'zybm'" :val="item.balb"
									  :name="'jsonList.'+$index+'.balb'" :search="true">
						</select-input>
					</div>
					</td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.ifqj" :val="item.ifqj"
									  :name="'jsonList.'+$index+'.ifqj'">
						</select-input>
					</div></td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.ynhz" :val="item.ynhz"
									  :name="'jsonList.'+$index+'.ynhz'">
						</select-input>
					</div></td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.yjhz" :val="item.yjhz"
									  :name="'jsonList.'+$index+'.yjhz'">
						</select-input>
					</div></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div class="zui-table-fixed table-fixed-l">
			<div class="zui-table-header">
				<table class="zui-table">
					<thead>
					<tr>
						<th class="cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
																					  :type="'all'" :val="isCheckAll">
                            </input-checkbox></th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="zui-table-body" @scroll="scrollTableFixed($event)">
				<table class="zui-table">
					<tbody>
					<tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
						<td class="cell-m"><input-checkbox  @result="reCheckBox" :list="'jsonList'"
																				:type="'all'" :val="isCheckAll">
						</input-checkbox></td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>


	</div>
	<page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

</div>



<script type="text/javascript" src="mxfydy.js"></script>


