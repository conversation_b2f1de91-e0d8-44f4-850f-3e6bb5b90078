<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>医保结算单</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ybjsd.css" rel="stylesheet" />
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
    <div class="printArea printShow"></div>
    <div class="background-box" >
        <div class="wrapper printHide" id="wrapper">
            <div v-show="lbxs">
                <div class="panel">
                    <div class="tong-top">
                        <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="showTq()" >提取数据</button>
                        <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="goToPage(1)">刷新</button>
                        <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="plsc()" >结算单批量上传</button>

                        <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="plqxsc()" >结算单批量取消上传</button>
                    </div>
                    <div class="tong-search" :class="{'tong-padded':isShow}">
                        <div class="flex-container flex-align-c padd-b-10" v-show="isShowkd">

                            <div class="flex-container flex-align-c padd-r-20">
                                <span class="ft-14 padd-r-5 whiteSpace ">上传标志</span>
                                <select-input @change-data="resultRydjChange"
                                              :child="sczt_tran"
                                              class="wh122"
                                              :index="param.sczt"
                                              :val="param.sczt"
                                              :name="'param.sczt'"  >
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c padd-r-20">
                                <span class="ft-14 padd-r-5 whiteSpace ">审核标志</span>
                                <select-input @change-data="resultRydjChange"
                                              :child="shzt_tran"
                                              class="wh122"
                                              :index="param.shzt"
                                              :val="param.shzt"
                                              :name="'param.shzt'"  >
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c padd-r-20">
                                <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                                <div class=" flex-container flex-align-c ">
                                    <input class="zui-input todate wh200 " placeholder="请选择申请开始日期" id="timeVal" /><span
                                        class="padd-l-5 padd-r-5">~</span>
                                    <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal1" />
                                </div>
                            </div>
                            <div class="flex-container flex-align-c">
                                <span class="ft-14 padd-r-5 whiteSpace ">检索</span>
                                    <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="goToPage(1)" type="text" v-model="param.parm" />
                            </div>

                        </div>


                    </div>
                </div>
                <div class="zui-table-view  padd-r-10 padd-l-10">
                    <!--入库列表-->
                    <div class="zui-table-header"  v-if="isShowkd">
                        <table class="zui-table table-width50">
                            <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>住院号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>入院时间</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>出院时间</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>就诊ID</span></div>
                                    </th>

                                    <th>
                                        <div class="zui-table-cell cell-s"><span>状态</span></div>
                                    </th>
                                    <th class=" cell-s">
                                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" key="b" v-if="isShowkd" @scroll="scrollTable($event)">
                        <table class="zui-table ">
                            <tbody>
                                <tr v-for="(item,$index) in ybjsdList" @click="checkSelect([$index,'one','ybjsdList'],$event)"
                                    :class="[{'table-hovers':$index===activeIndex,}]" :tabindex="$index" @dblclick="showDetail($index,item)">
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.zyh">序号</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" v-text="item.admtime">序号</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" v-text="item.dscgtime">序号</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l " v-text="item.jzid"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s " v-text="item.sczt=='0'?'待上传':item.shzt=='0'?'待审核':'已审核'"></div>
                                    </td>
                                    <td class=" cell-s">
                                        <div class="zui-table-cell cell-s flex-container flex-align-c padd-t-5">
                                            <span class="width30 icon-js title" v-if="item.sczt == '0'" data-gettitle="作废"
                                                  @click="deleteYbBaScZyh($index,item)"></span>
                                            <span class="width30 icon-js title" v-if="item.shzt == '1'" data-gettitle="撤回"
                                                  @click="chYbBaScZyh($index,item)"></span>
                                        </div>
                                    </td>

                                    <p v-show="ybjsdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                                </tr>
                            </tbody>
                        </table>
                    </div>




                    <page @go-page="goPage" v-if="isShowkd" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

                </div>
            </div>

            <model  :model-show="false" @result-close="tqsjType=false"
                    v-show="tqsjType" :title="'提取数据'">
                <div class="bqcydj_model">
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">开始时间</span>
                            <div class="zui-input-inline wh180">
                                <input readonly="true"  v-model="tqparam.beginrq" id="tqksTime" class="zui-input wh180" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">结束时间</span>
                            <div class="zui-input-inline wh180">
                                <input readonly="true"  v-model="tqparam.endrq" id="tqjsTime" class="zui-input wh180" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <div class="zui-input-inline wh180">
                                <button v-waves style="margin-left: 200px;" class="tong-btn btn-parmary btn-parmary-not
		                    	yellow-bg" @click="saveSjtq">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </model>

            <div v-show="!lbxs">
                <div class="zui-table-view  padd-r-10 padd-l-10">
                <div class="panel">
                    <div class="tong-top">
                        <button class="tong-btn btn-parmary  paddr-r5" @click="showlb()" >返回</button>
                        <button class="tong-btn btn-parmary  paddr-r5" @click="savemx()" >保存</button>
                    </div>
                </div>

                <div class="basyzt" style="">结算单信息</div>
                    <div class=" margin-t-10" style="height: 87.7vh;overflow: auto;">
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">医保编号</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.rybh"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">病案号</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.medcasno"></div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">姓名</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.brxm"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">性别</div>
                                <div class="yljgxhx yqq-xhx">医保生成</div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">出生日期</div>
                                <div class="yljgxhx yqq-xhx">医保生成</div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">年龄</div>
                                <div class="yljgxhx yqq-xhx" >医保生成</div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">国籍</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ntly"></div>
                            </div>
                        </div>
                        <div class="line1">

                            <div class="yljgzl">
                                <div class="yqq-lab">职业</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.prfs"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">现住址</div>
                                <div class="yljgxhx yqq-xhxc" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.curraddr"/>
                                </div>
                            </div>

                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">单位名称</div>
                                <div class="yljgxhx yqq-xhxc" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.empname"/>
                                </div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">单位地址</div>
                                <div class="yljgxhx yqq-xhxc">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.empaddr"/>
                                </div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">单位电话</div>
                                <div class="yljgxhx yqq-xhxd" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.emptel"/>
                                </div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">邮编</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.poscode"/>
                                </div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">联系人姓名</div>
                                <div class="yljgxhx yqq-xhxd" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.conername"/>
                                </div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">关系</div>
                                <div class="yljgxhx yqq-xhxd" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.patnrlts"/>
                                </div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">地址</div>
                                <div class="yljgxhx yqq-xhxc" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.coneraddr"/>
                                </div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">电话</div>
                                <div class="yljgxhx yqq-xhxc" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.conertel"/>
                                </div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">医保类型</div>
                                <div class="yljgxhx yqq-xhx" v-text="">医保生成</div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">特殊人员类型</div>
                                <div class="yljgxhx yqq-xhx" v-text="">医保生成</div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">参保地</div>
                                <div class="yljgxhx yqq-xhx" v-text="">医保生成</div>
                            </div>
                        </div>
                        <div class="line1">
                            住院诊疗信息
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">住院医疗类型</div>
                                <div class="yljgxhx yqq-xhxd" >1</div>
                                <div>1.住院 2.日间手术</div>
                            </div>

                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">入院途径</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.admway"/>
                                </div>
                                <div>1.急诊 2.门诊 3.其它医疗机构转入 9.其它</div>
                            </div>

                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">治疗类别</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.trttype"/>
                                </div>
                                <div>1.西医 2.中医（2.1 中医 2.2 民族医） 3.中西医</div>
                            </div>

                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">入院时间</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.admtime"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院科别</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.admdeptname"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">转科科别</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.refldeptdept"></div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院时间</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.dscgtime"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">出院科别</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.admdeptname"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">实际住院</div>
                                <div class="yljgxhx yqq-xhx" v-text="">医保生成</div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">门（急）诊诊断（西医诊断）</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.otpwmdise"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.wmdisecode"></div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">门（急）诊诊断（中医诊断）</div>
                                <div class="yljgxhx yqq-xhx" v-text=""></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text=""></div>
                            </div>
                        </div>
                        <div class="line1">
                            诊断信息 入院病情（1 有 2 临床未确定 3 情况不明 4 无 ）
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医主要诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc1"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm1"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq1"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm2">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc2"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm2"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq2"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm3">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc3"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm3"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq3"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm4">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc4"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm4"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq4"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm5">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc5"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm5"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq5"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm6">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc6"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm6"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq6"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm7">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc7"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm7"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq7"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm8">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc8"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm8"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq8"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm9">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc9"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm9"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq9"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm10">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc10"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm10"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq10"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm11">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc11"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm11"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq11"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm12">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc12"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm12"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq12"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm13">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc13"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm13"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq13"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm14">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc14"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm14"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq14"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm15">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc15"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm15"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq15"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm16">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc16"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm16"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq16"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm17">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc17"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm17"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq17"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm18">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc18"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm18"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq18"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm19">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc19"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm19"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq19"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm20">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc20"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm20"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq20"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm21">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc21"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm21"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq21"></div>
                            </div>
                        </div>
                        <div class="line1" v-if="mxdaxx.ficd10bm22">
                            <div class="yljgzl">
                                <div class="yqq-lab">出院西医其他诊断</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.fzdmc22"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">疾病代码</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.ficd10bm22"></div>
                            </div>
                            <div class="yljgzl">
                                <div class="yqq-lab">入院病情</div>
                                <div class="yljgxhx yqq-xhx" v-text="mxdaxx.frybq22"></div>
                            </div>
                        </div>
                        <div class="line1" >
                            <div class="yljgzl">
                                <div class="yqq-lab">诊断代码计数</div>
                                <div class="yljgxhx yqq-xhx" >医保生成</div>
                            </div>
                        </div>

                        <div class="line1" v-if="mxdaxx.fssbm1">
                            <table>
                                <tr>
                                    <th>主要手术及操作名称</th>
                                    <th>主要手术及操作代码</th>
                                    <th>麻醉方式</th>
                                    <th>术者医师姓名</th>
                                    <th>术者医师代码</th>
                                    <th>麻醉医师姓名</th>
                                    <th>麻醉医师代码</th>
                                </tr>
                                <tr>
                                    <td v-text="mxdaxx.fssmc1"></td>
                                    <td v-text="mxdaxx.fssbm1"></td>
                                    <td v-text="mxdaxx.fmz1"></td>
                                    <td v-text="mxdaxx.fys1"></td>
                                    <td v-text="mxdaxx.fys1code"></td>
                                    <td v-text="mxdaxx.fmzys1"></td>
                                    <td v-text="mxdaxx.fmzys1code"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm1">
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作起止时间</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                            <div class="yljgzl">
                                <div class="yqq-lab">麻醉起止时间:</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                        </div>


                        <div class="line1" v-if="mxdaxx.fssbm2">
                            <table>
                                <tr>
                                    <th>其他手术及操作名称</th>
                                    <th>其他手术及操作代码</th>
                                    <th>麻醉方式</th>
                                    <th>术者医师姓名</th>
                                    <th>术者医师代码</th>
                                    <th>麻醉医师姓名</th>
                                    <th>麻醉医师代码</th>
                                </tr>
                                <tr>
                                    <td v-text="mxdaxx.fssmc2"></td>
                                    <td v-text="mxdaxx.fssbm2"></td>
                                    <td v-text="mxdaxx.fmz2"></td>
                                    <td v-text="mxdaxx.fys2"></td>
                                    <td v-text="mxdaxx.fys2code"></td>
                                    <td v-text="mxdaxx.fmzys2"></td>
                                    <td v-text="mxdaxx.fmzys2code"></td>
                                </tr>
                            </table>
                        </div>

                        <div class="line1" v-if="mxdaxx.fssbm2">
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作起止时间</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                            <div class="yljgzl">
                                <div class="yqq-lab">麻醉起止时间:</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                        </div>

                        <div class="line1" v-if="mxdaxx.fssbm3">
                            <table>
                                <tr>
                                    <th>其他手术及操作名称</th>
                                    <th>其他手术及操作代码</th>
                                    <th>麻醉方式</th>
                                    <th>术者医师姓名</th>
                                    <th>术者医师代码</th>
                                    <th>麻醉医师姓名</th>
                                    <th>麻醉医师代码</th>
                                </tr>
                                <tr>
                                    <td v-text="mxdaxx.fssmc3"></td>
                                    <td v-text="mxdaxx.fssbm3"></td>
                                    <td v-text="mxdaxx.fmz3"></td>
                                    <td v-text="mxdaxx.fys3"></td>
                                    <td v-text="mxdaxx.fys3code"></td>
                                    <td v-text="mxdaxx.fmzys3"></td>
                                    <td v-text="mxdaxx.fmzys3code"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm3">
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作起止时间</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                            <div class="yljgzl">
                                <div class="yqq-lab">麻醉起止时间:</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                        </div>

                        <div class="line1" v-if="mxdaxx.fssbm4">
                            <table>
                                <tr>
                                    <th>其他手术及操作名称</th>
                                    <th>其他手术及操作代码</th>
                                    <th>麻醉方式</th>
                                    <th>术者医师姓名</th>
                                    <th>术者医师代码</th>
                                    <th>麻醉医师姓名</th>
                                    <th>麻醉医师代码</th>
                                </tr>
                                <tr>
                                    <td v-text="mxdaxx.fssmc4"></td>
                                    <td v-text="mxdaxx.fssbm4"></td>
                                    <td v-text="mxdaxx.fmz4"></td>
                                    <td v-text="mxdaxx.fys4"></td>
                                    <td v-text="mxdaxx.fys4code"></td>
                                    <td v-text="mxdaxx.fmzys4"></td>
                                    <td v-text="mxdaxx.fmzys4code"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm4">
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作起止时间</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                            <div class="yljgzl">
                                <div class="yqq-lab">麻醉起止时间:</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm5">
                            <table>
                                <tr>
                                    <th>其他手术及操作名称</th>
                                    <th>其他手术及操作代码</th>
                                    <th>麻醉方式</th>
                                    <th>术者医师姓名</th>
                                    <th>术者医师代码</th>
                                    <th>麻醉医师姓名</th>
                                    <th>麻醉医师代码</th>
                                </tr>
                                <tr>
                                    <td v-text="mxdaxx.fssmc5"></td>
                                    <td v-text="mxdaxx.fssbm5"></td>
                                    <td v-text="mxdaxx.fmz5"></td>
                                    <td v-text="mxdaxx.fys5"></td>
                                    <td v-text="mxdaxx.fys5code"></td>
                                    <td v-text="mxdaxx.fmzys5"></td>
                                    <td v-text="mxdaxx.fmzys5code"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm5">
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作起止时间</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                            <div class="yljgzl">
                                <div class="yqq-lab">麻醉起止时间:</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm6">
                            <table>
                                <tr>
                                    <th>其他手术及操作名称</th>
                                    <th>其他手术及操作代码</th>
                                    <th>麻醉方式</th>
                                    <th>术者医师姓名</th>
                                    <th>术者医师代码</th>
                                    <th>麻醉医师姓名</th>
                                    <th>麻醉医师代码</th>
                                </tr>
                                <tr>
                                    <td v-text="mxdaxx.fssmc6"></td>
                                    <td v-text="mxdaxx.fssbm6"></td>
                                    <td v-text="mxdaxx.fmz6"></td>
                                    <td v-text="mxdaxx.fys6"></td>
                                    <td v-text="mxdaxx.fys6code"></td>
                                    <td v-text="mxdaxx.fmzys6"></td>
                                    <td v-text="mxdaxx.fmzys6code"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm6">
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作起止时间</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                            <div class="yljgzl">
                                <div class="yqq-lab">麻醉起止时间:</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm7">
                            <table>
                                <tr>
                                    <th>其他手术及操作名称</th>
                                    <th>其他手术及操作代码</th>
                                    <th>麻醉方式</th>
                                    <th>术者医师姓名</th>
                                    <th>术者医师代码</th>
                                    <th>麻醉医师姓名</th>
                                    <th>麻醉医师代码</th>
                                </tr>
                                <tr>
                                    <td v-text="mxdaxx.fssmc7"></td>
                                    <td v-text="mxdaxx.fssbm7"></td>
                                    <td v-text="mxdaxx.fmz7"></td>
                                    <td v-text="mxdaxx.fys7"></td>
                                    <td v-text="mxdaxx.fys7code"></td>
                                    <td v-text="mxdaxx.fmzys7"></td>
                                    <td v-text="mxdaxx.fmzys7code"></td>
                                </tr>
                            </table>
                        </div>

                        <div class="line1" v-if="mxdaxx.fssbm7">
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作起止时间</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                            <div class="yljgzl">
                                <div class="yqq-lab">麻醉起止时间:</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                        </div>

                        <div class="line1" v-if="mxdaxx.fssbm8">
                            <table>
                                <tr>
                                    <th>其他手术及操作名称</th>
                                    <th>其他手术及操作代码</th>
                                    <th>麻醉方式</th>
                                    <th>术者医师姓名</th>
                                    <th>术者医师代码</th>
                                    <th>麻醉医师姓名</th>
                                    <th>麻醉医师代码</th>
                                </tr>
                                <tr>
                                    <td v-text="mxdaxx.fssmc8"></td>
                                    <td v-text="mxdaxx.fssbm8"></td>
                                    <td v-text="mxdaxx.fmz8"></td>
                                    <td v-text="mxdaxx.fys8"></td>
                                    <td v-text="mxdaxx.fys8code"></td>
                                    <td v-text="mxdaxx.fmzys8"></td>
                                    <td v-text="mxdaxx.fmzys8code"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="line1" v-if="mxdaxx.fssbm8">
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作起止时间</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                            <div class="yljgzl">
                                <div class="yqq-lab">麻醉起止时间:</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                                <div>至</div>
                                <div class="yljgxhx yqq-xhx" ></div>
                            </div>

                        </div>
                        <div class="line1" >
                            <div class="yljgzl">
                                <div class="yqq-lab">手术及操作代码计数 </div>
                                <div class="yljgxhx yqq-xhx" >医保生成</div>
                            </div>
                        </div>

                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">呼吸机使用时间 </div>
                                <div class="yljgxhx yqq-xhxd" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.ventuseddurat"/>
                                </div>
                                <div class="yqq-lab" >天</div>
                                <div class="yljgxhx yqq-xhxd" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.ventuseddurax"/>
                                </div>
                                <div class="yqq-lab" >小时</div>
                                <div class="yljgxhx yqq-xhxd" >
                                    <input type="text" class="xhxinput" v-model="mxdaxx.ventusedduraf"/>
                                </div>
                                <div class="yqq-lab" >分钟</div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">颅脑损伤患者昏迷时间：入院前</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.pwcrybfadmcomadura"/>
                                </div>
                                <div class="yqq-lab" >天</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.pwcrybfadmcomadura"/>
                                </div>
                                <div class="yqq-lab" >小时</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.pwcrybfadmcomadura"/>
                                </div>
                                <div class="yqq-lab" >分钟</div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">颅脑损伤患者昏迷时间：入院后</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.pwcryafadmcomadura"/>
                                </div>
                                <div class="yqq-lab" >天</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.pwcryafadmcomadura"/>
                                </div>
                                <div class="yqq-lab" >小时</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.pwcryafadmcomadura"/>
                                </div>
                                <div class="yqq-lab" >分钟</div>
                            </div>
                        </div>
                        <div class="line1">
                            <table>
                                <tr>
                                    <th>重症监护病房类型
                                        （CCU、NICU、ECU、SICU、PICU、
                                        RICU、ICU(综合)、其他）</th>
                                    <th>进重症监护室时间
                                        （_年_月_日_时_分）</th>
                                    <th>出重症监护室时间
                                        （_年_月_日_时_分） </th>
                                    <th>合计(_时_分)
                                    </th>
                                </tr>
                                <tr v-for="(item,$index) in mxdaxx.icuxx">
                                    <td v-model="item.iculx"></td>
                                    <td v-model="fDate(item.inpooltime,'datetime')"></td>
                                    <td v-model="fDate(item.exittime,'datetime')"></td>
                                    <td v-model="item.scscutdsumdura"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="line1">
                            <table>
                                <tr>
                                    <th>输血品种</th>
                                    <th>输血量</th>
                                    <th>剂量单位</th>
                                </tr>
                                <tr v-for="(item,$index) in mxdaxx.icuxx">
                                    <td v-model="item.iculx"></td>
                                    <td v-model="fDate(item.inpooltime,'datetime')"></td>
                                    <td v-model="fDate(item.exittime,'datetime')"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">特级护理天数</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.tjhlts"/>
                                </div>
                                <div class="yqq-lab">一级护理天数</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.yjhlts"/>
                                </div>
                                <div class="yqq-lab" >二级护理天数</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.ejhlts"/>
                                </div>
                                <div class="yqq-lab" >三级护理天数</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.sjhlts"/>
                                </div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">离院方式</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.dscgway"/>
                                </div>
                                <div class="yqq-lab">1.医嘱离院 2.医嘱转院 3.医嘱转社区卫生服务 4.非医嘱离院 5.死亡 9.其他</div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">是否有出院31天内再住院计划</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.daysrinpflag31"/>
                                </div>
                                <div class="yqq-lab">1.无 2.有，目的</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.daysrinppup31"/>
                                </div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">主诊医师姓名</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.zyysxm"/>
                                </div>
                                <div class="yqq-lab">主诊医师代码</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.chfpdrcode"/>
                                </div>
                            </div>
                        </div>
                        <div class="line1">
                            <div class="yljgzl">
                                <div class="yqq-lab">责任护士姓名</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.zrhsxm"/>
                                </div>
                                <div class="yqq-lab">责任护士代码</div>
                                <div class="yljgxhx yqq-xhxd">
                                    <input type="text" class="xhxinput" v-model="mxdaxx.respnurscode"/>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
            </div>

        </div>
    </div>
    <!--侧边窗口-->

    <script type="application/javascript" src="/newzui/pub/js/insuranceGbUtils.js"></script>
    <script src="ybjsd.js"></script>

</body>

</html>
