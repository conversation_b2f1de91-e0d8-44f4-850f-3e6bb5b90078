(function () {
    //顶部工具栏
    var tool=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
            zt: '9',
            qxksList:[],
            param:{
                ksbm:'',
                ksmc:'',
            }
        },
        mounted:function(){
            this.getKsList();
        },
        methods:{

            //获取科室编码
            getKsList:function () {
                var ksparm = {
                    "ylbm": 'N030092001'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                    if (json.a == 0) {
                        if (json.d.length > 0) {
                            tool.qxksList = json.d;
                            if(json.d && json.d[0]){
                                tool.param.ksbm = json.d[0].ksbm;
                                wxdjList.ksContent.ksmc = json.d[0].ksmc;
                                wxdjList.getData();
                            }
                        }
                    }
                });
            },
            ksResultChange:function(val){
                this.param.ksbm = val[0];
                this.param.ksmc = val[4];
                wxdjList.ksContent.ksmc = val[4];
                wxdjList.getData();
            },
            //刷新和检索
            getData:function () {
                wxdjList.getData();
            },
        }
    });

    //危急值管理列表
    var wxdjList = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data: {
            param:{
                rows:20,
                page:1,
                parm:""
            },
            byshList:[],
            ksContent:{}
        },
        updated:function () {
            changeWin()
        },
        methods: {
            //请求后台查询列表信息
            getData: function(){
                var param = {
                    rows:this.param.rows,
                    page:this.param.page,
                    parm:tool.param.parm,
                    t:{
                        rjks:tool.param.ksbm,
                        yljgbm:jgbm,
                    },
                };
                //加载动画效果
                common.openloading('.zui-table-view');
                //数据请求结束时关闭
                wxdjList.byshList = [];
                this.postAjaxT("/cpw-api/LcljYwBysh/query",JSON.stringify(param),function (result) {
                    if(result.status == 200){
                        wxdjList.totlePage = Math.ceil(result.data.total/wxdjList.param.rows);
                        wxdjList.byshList = result.data.list;
                    }else{
                        malert(result.msg,"top","defeadted");
                    }
                },function () {
                    malert("临床路径服务错误，请联系管理员！","top","defeadted");
                });
                common.closeLoading()
            },
            //
            agreeFun:function (item) {
                var param = JSON.parse(JSON.stringify(item));
                if(confirm("病人将【退出】路径，是否确定？")){
                    param.yljgbm = jgbm;
                    param.shzt = '2';
                    this.postAjaxT("/cpw-api/LcljYwBysh/bysh",JSON.stringify(param),function (result) {
                        if(result.status == 200){
                            malert(result.msg);
                            wxdjList.getData();
                        }else{
                            malert(result.msg,"top","defeadted");
                        }
                    },function () {
                        malert("临床路径服务错误，请联系管理员！","top","defeadted");
                    });
                }
            },
            refuseFun:function (item) {
                var param = JSON.parse(JSON.stringify(item));
                if(confirm("病人将【继续】路径，是否确定？")){
                    param.yljgbm = jgbm;
                    param.shzt = '3';
                    this.postAjaxT("/cpw-api/LcljYwBysh/bysh",JSON.stringify(param),function (result) {
                        if(result.status == 200){
                            malert(result.msg);
                            wxdjList.getData();
                        }else{
                            malert(result.msg,"top","defeadted");
                        }
                    },function () {
                        malert("临床路径服务错误，请联系管理员！","top","defeadted");
                    });
                }
            },

            cancelFun:function (item) {
                var param = JSON.parse(JSON.stringify(item));
                if(confirm("取消审核，是否确定？")){
                    param.yljgbm = jgbm;
                    this.postAjaxT("/cpw-api/LcljYwBysh/qxbysh",JSON.stringify(param),function (result) {
                        if(result.status == 200){
                            malert(result.msg);
                            wxdjList.getData();
                        }else{
                            malert(result.msg,"top","defeadted");
                        }
                    },function () {
                        malert("临床路径服务错误，请联系管理员！","top","defeadted");
                    });
                }
            },
        }
    });
    laydate.render({
        elem: '.times',
        type: 'date',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
    laydate.render({
        elem: '.times1',
        type: 'date',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
})();





