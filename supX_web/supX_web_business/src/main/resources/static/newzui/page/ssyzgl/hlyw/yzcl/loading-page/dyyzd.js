var printBox = new Vue({
    el: '#printBox',
    data:{},
    methods: {}
});

var yzblcl_Left = {
    BrxxJson: {},
};

window.addEventListener('storage',function (e) {
    if( e.key == 'dyyzd' && e.oldValue !== e.newValue ){
        setTimeout(function () {
            pageInit();
        },100);
    }
});

function pageInit() {
    yzblcl_Left.BrxxJson = JSON.parse( sessionStorage.getItem( 'dyyzd' ) );
    cqyzd.getData();
    lsyzd.getData();
}

setTimeout(function () {
    pageInit();
},100);




var toolMenu_yzd = new Vue({
    el: '#toolMenu_yzd',
    data: {
        which: 0,
        pageList: [],
        pageH: 860
    },
    methods: {
        long: function (num) {
            this.which = num;
            lsyzd.isShow = false;
            cqyzd.isShow = true;
            cqyzd.getData();
        },
        short: function (num) {
            this.which = num;
            cqyzd.isShow = false;
            lsyzd.isShow = true;
            lsyzd.getData();
        },
        doPrint: function (isGoOn) {
            var cqTr;
            cqPrint.list = [];
            lsPrint.list = [];
            if (this.which == 0) {
                cqTr = $(".cqyzd tr");
            } else {
                cqTr = $(".lsyzd tr");
            }
            var _height = 0;
            var a = 0, b = -1;
            for (var i = 1; i < cqTr.length - 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height >= toolMenu_yzd.pageH) {
                    b++;
                    var as = [];
                    for (var f = a; f < i; f++) {
                        if (this.which == 0){
                            as.push(cqyzd.jsonList[f]);
                        } else {
                            as.push(lsyzd.jsonList[f]);
                        }
                    }
                    if (this.which == 0) cqPrint.list[b] = as;
                    else lsPrint.list[b] = as;
                    a = i;
                    _height = 0;
                    this.pageList.push(as.length);
                }
            }
            var pp = [];
            if (this.which == 0) {
                for (var p = a; p < cqyzd.jsonList.length; p++){
                    pp.push(cqyzd.jsonList[p]);
                }
            } else {
                for (var ls = a; ls < lsyzd.jsonList.length; ls++) pp.push(lsyzd.jsonList[ls]);
            }
            for (var l = 0; l < 21; l++) {
                _height += 43;
                if (_height >= toolMenu_yzd.pageH) {
                    break;
                }
                pp.push({});
            }
            if (this.which == 0) {
                cqPrint.list[b + 1] = pp;
                cqPrint.isShow = true;
                lsPrint.isShow = false;
            } else {
                lsPrint.list[b + 1] = pp;
                lsPrint.isShow = true;
                cqPrint.isShow = false;
            }
            console.log(lsPrint.list);
            if(isGoOn){
                cqPrint.isGoPrint = true;
                lsPrint.isGoPrint = true;
                setTimeout(function () {
                    toolMenu_yzd.hideTable(toolMenu_yzd.which);
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid transparent');
                }, 50);
                setTimeout(function () {
                    window.print();
                    cqPrint.isGoPrint = false;
                    lsPrint.isGoPrint = false;
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid #999');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid #999');
                }, 100);
            } else {
                setTimeout(function () {
                    window.print();
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                }, 100);
            }
        },

        hideTable: function (type) {
            var num = 0;
            if(type == 0){
                for(var i = 0; i < cqPrint.pagePrint; i++){
                    $('.cqPrint .popCenter').eq(i).hide();
                    num += this.pageList[i];
                }
                cqPrint.isChecked = cqPrint.isChecked - num;
            } else {
                for(var j = 0; j < lsPrint.pagePrint; j++){
                    $('.lsPrint .popCenter').eq(j).hide();
                    num += this.pageList[j];
                }
                lsPrint.isChecked = lsPrint.isChecked - num;
            }

        }
    }
});

//长期医嘱单
var cqyzd = new Vue({
    el: '.cqyzd',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        jsonList: [],
        isShow: true,
        param: {},
        BrxxJson: [],
        isGoPrint: false            // 是否续打
    },
    methods: {
        goPrint: function (index) {
            cqyzd.isChecked = index;
            cqPrint.isChecked = index;
            var cqTr = $(".cqyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > toolMenu_yzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            cqPrint.pagePrint = b;
        },
        getData: function () {
            this.BrxxJson = yzblcl_Left.BrxxJson;
            cqPrint.BrxxJson = yzblcl_Left.BrxxJson;
            if (this.BrxxJson.zyh == null || this.BrxxJson.zyh == '') {
                malert("请选择病人后再查看医嘱单！");
                return false;
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                zyh: this.BrxxJson.zyh,
                yzlx: '1'
            };
            $.getJSON('/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=hzyzd&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    cqyzd.jsonList = json.d.list;
                    for(var i = 0; i < cqyzd.jsonList.length; i++){
                        cqyzd.jsonList[i]['xmmc'] = cqyzd.jsonList[i]['xmmc'].replace('null', '');
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc'].replace('null', '');
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc'].replace('null', '');
                    }
                } else {
                    malert("病人医嘱单信息查询失败！");
                }
            });
        },
        Appendzero:function (obj) {
            if(obj<10) return "0" +""+ obj;
            else return obj;
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var prvVal = null, nextVal = null;
            if (index != this.jsonList.length - 1 && index != 0) {
                prvVal = this.jsonList[index - 1][name];
                nextVal = this.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if(fzh == 0) return false;
            if(index == 0 && fzh == this.jsonList[index + 1]['fzh']){
                return 'start';
            }
            if(index != 0 && index != this.jsonList.length - 1){
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if(fzh == null || fzh != nextFzh && fzh != prvFzh){
                    return 'null';
                }
                if(fzh == nextFzh && fzh != prvFzh){
                    return 'start';
                }
                if(fzh != nextFzh && fzh == prvFzh){
                    return 'end';
                }
                if(fzh == nextFzh && fzh == prvFzh){
                    return 'all';
                }
            }
            return 'null'
        }
    }
});

var cqPrint = new Vue({
    el: '.cqPrint',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: 0,
        BrxxJson: cqyzd.BrxxJson,
        isGoPrint: false
    },
    methods: {
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                cqPrint.isGoPrint = false;
                $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var val = cqyzd.jsonList[index][name];
            var prvVal = cqyzd, nextVal = null;
            if (index != cqyzd.jsonList.length - 1 && index != 0) {
                prvVal = cqyzd.jsonList[index - 1][name];
                nextVal = cqyzd.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqPrint.Appendzero((reDate.getMonth() + 1)) + '-' + cqPrint.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqPrint.Appendzero(reDate.getHours()) + ':' + cqPrint.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var fzh = cqyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == cqyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != cqyzd.jsonList.length - 1) {
                var nextFzh = cqyzd.jsonList[index + 1]['fzh'];
                var prvFzh = cqyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

//临时医嘱单
var lsyzd = new Vue({
    el: '.lsyzd',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        jsonList: [],
        isShow: false,
        param: {},
        BrxxJson: [],
        isGoPrint: false            // 是否续打
    },
    methods: {
        goPrint: function (index) {
            lsyzd.isChecked = index;
            lsPrint.isChecked = index;
            var cqTr = $(".lsyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > toolMenu_yzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            lsPrint.pagePrint = b;
        },
        getData: function () {
            this.BrxxJson = yzblcl_Left.BrxxJson;
            lsPrint.BrxxJson = yzblcl_Left.BrxxJson;
            if (this.BrxxJson.zyh == null || this.BrxxJson.zyh == '') {
                malert("请选择病人后再查看医嘱单！");
                return
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                zyh: this.BrxxJson.zyh,
                yzlx: '0'
            };
            $.getJSON('/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=hzyzd&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    lsyzd.jsonList = json.d.list;
                    for(var i = 0; i < lsyzd.jsonList.length; i++){
                        lsyzd.jsonList[i]['xmmc'] = lsyzd.jsonList[i]['xmmc'].replace('null', '');
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc'].replace('null', '');
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc'].replace('null', '');
                    }
                } else {
                    malert("查询临时医嘱失败！");
                }
            });
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = this.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            if (val == prvVal && index != 0) {
                return '"';
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if(fzh == 0) return false;
            if(index == 0 && fzh ==  this.jsonList[index + 1]['fzh']){
                return 'start';
            }
            if(index != 0 && index != this.jsonList.length - 1){
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if(fzh == null || fzh != nextFzh && fzh != prvFzh){
                    return 'null';
                }
                if(fzh == nextFzh && fzh != prvFzh){
                    return 'start';
                }
                if(fzh != nextFzh && fzh == prvFzh){
                    return 'end';
                }
                if(fzh == nextFzh && fzh == prvFzh){
                    return 'all';
                }
            }
            return 'null'
        }
    }
});

var lsPrint = new Vue({
    el: '.lsPrint',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: null,
        BrxxJson: lsyzd.BrxxJson,
        isGoPrint: false
    },
    methods: {
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                lsPrint.isGoPrint = false;
                $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = lsyzd.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            if (val == prvVal && index != 0) {
                return '"';
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) {
                return null;
            }
            var fzh = lsyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == lsyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != lsyzd.jsonList.length - 1) {
                var nextFzh = lsyzd.jsonList[index + 1]['fzh'];
                var prvFzh = lsyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            if (lsyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (lsyzd.jsonList[index]['fzh'] == lsyzd.jsonList[index + 1]['fzh']) {
                if (lsyzd.jsonList[index]['yyffmc'] == lsyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});