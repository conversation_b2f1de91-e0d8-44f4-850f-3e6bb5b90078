window.loadPage = new common.loadDebrisHTML('.loadPage');

loadPage.show('rydj.html');

var fyxmTab = new Vue({
    el: '.fyxm-tab',
    data: {
        num: 0,
        bxlbbm:'',
        bxurl:''
    },
    created:function(){
        this.getbxlb();
        this.GetZyksData();
    },
    methods: {
        tabBg: function (page) {
            $(".loadPage").load(page + ".html").fadeIn(300);
        },
        getbxlb: function () {
            var param = {bxjk: "005"};
            common.openloading("#sjsc");
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            fyxmTab.bxlbbm = json.d.list[0].bxlbbm;
                            fyxmTab.bxurl = json.d.list[0].url;
                        }
                        common.closeLoading();
                    } else {
                        malert("保险类别查询失败!" + json.c,"top","defeadted");
                        common.closeLoading();
                    }
                });
        },
        //页面加载时自动获取住院科室数据
        GetZyksData: function () {
            var bean = {"zyks": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    fyxmTab.zyksList = [];
                    fyxmTab.zyksList = json.d.list;
                    fyxmTab.ksbm = (json.d.list[1]).ksbm;
                } else{
                    malert('住院科室列表查询失败','top','defeadted');
                }
            });
        },
    },
});
function tabBg(page, index, event,num) {
    if(num){
        this.page=page
    }
    if( this.page==page){
    }else{
        this.page=''
        $('.isative').removeClass('active')
        fyxmTab.num = index;
        $(event).addClass('active')
        $(".loadPage").load(page + ".html",'',function () {
            if(num==1){
                setTimeout(function () {
                },1000)
            }

        }).fadeIn(300);
    }
}