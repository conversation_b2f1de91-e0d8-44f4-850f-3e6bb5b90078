.zui-form .zui-form-label {
  left: 5px;
}
.ksys-side {
  padding: 15px;
}
.zui-table-view .zui-table-fixed.table-fixed-l{
  left: 10px;
}
.table-hovers-filexd-l{
  border-right: none !important;
}
.table-hovers-filexd-r{
  border-left: none!important;
}
.table-hovers-filexd-r-child{
  border-right: 1px solid #1abc9c !important;
}
.flex_9 li{
  height: 39px;
  text-align: center;
  line-height: 39px;
  color: #2F2F2F;
  background: #edf2f1;
  min-width: 100px;
}
.flex_9 p{
  padding: 10px 0;
  cursor: pointer;
  text-align: center;
  color: #7C8189;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 100px;
}
.fyqd{
  padding: 0 10px;
  height: calc(100% - 198px);
  overflow: hidden;
}
.qdmx_item .list{
  min-width: 100px;
  padding: 10px 0;
  text-align: center;
}
.qdmx_title .notBorder{
  border-top: none;
  font-size: 14px;
}
.qdmx_title .list{
  min-width: 100px;
  padding: 10px 0;
  text-align: center;
  /*border-top: 1px #eee solid;*/
}
.qdmx_item .list span{
  cursor: pointer;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
  color: #7C8189;
  text-overflow: ellipsis;

}
.qdmx_item,.qdmx_title{
  border: 1px solid #eee ;
  border-top: none;
  width: max-content;
}