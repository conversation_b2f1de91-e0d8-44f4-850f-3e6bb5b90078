<div id="jscx" class="contextInfo">
    <div class="wrapper background-f" id="jyxm_icon" >
        <div class="panel">
            <div class="tong-top background-f">
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline">
                        <label class="zui-form-label">检索</label>
                        <div class="zui-input-inline margin-f-l25">
                            <input class="zui-input wh180" placeholder="请输入关键字" v-model="param.parm"/>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view padd-r-10 padd-l-10"    v-cloak>
            <!--入院登记start-->
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>住院号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>登记号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>结算日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>是否作废</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>总费用</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>统筹支付</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>账户支付</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>家庭账户</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>大病互助基金</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>离休基金</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>公务员补助</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>大病补助基金</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>二次补助</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>工伤保险</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生育保险</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>现金支付</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>居民统筹基金</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>城乡大病互助</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>农合统筹</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>医院支付</span></div></th>

                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body"  @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in 10"
                        :tabindex="$index"
                        @dblclick="edit(item)"
                        ref="list"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-xl">zyh20180912001</div></td>
                        <td><div class="zui-table-cell cell-s">djh20181693212</div></td>
                        <td><div class="zui-table-cell cell-s">2018-09-11</div></td>
                        <td><div class="zui-table-cell cell-s">是</div></td>
                        <td><div class="zui-table-cell cell-s">20157</div></td>
                        <td><div class="zui-table-cell cell-s">1056</div></td>
                        <td><div class="zui-table-cell cell-s">565</div></td>
                        <td><div class="zui-table-cell cell-s">5666</div></td>
                        <td><div class="zui-table-cell cell-s">234</div></td>
                        <td><div class="zui-table-cell cell-s">588</div></td>
                        <td><div class="zui-table-cell cell-s">345</div></td>
                        <td><div class="zui-table-cell cell-s">345</div></td>
                        <td><div class="zui-table-cell cell-s">56</div></td>
                        <td><div class="zui-table-cell cell-s">345</div></td>
                        <td><div class="zui-table-cell cell-s">345</div></td>
                        <td><div class="zui-table-cell cell-s">345</div></td>
                        <td><div class="zui-table-cell cell-xl">345</div></td>
                        <td><div class="zui-table-cell cell-xl">855</div></td>
                        <td><div class="zui-table-cell cell-s">555</div></td>
                        <td><div class="zui-table-cell cell-s">666</div></td>
                        <!--绑定数据放开 数据为空的时候占位-->
                        <!--<p v-if="bsdList.length==0" class=" noData  text-center zan-border">暂无数据...</p>-->
                    </tr>
                    </tbody>
                </table>


            </div>
            <!--左侧固定start-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in 10" :tabindex="$index" class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()">

                            <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--左侧固定end-->
            <!--右侧固定start-->
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>医院支付</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in 10" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()">
                            <td  class="cell-s">
                                <div class="zui-table-cell cell-s">236</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定end-->
            <!--分页start-->
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
            <!--分页end-->
        </div>


    </div>

    <script type="text/javascript" src="jscx.js"></script>

</div>