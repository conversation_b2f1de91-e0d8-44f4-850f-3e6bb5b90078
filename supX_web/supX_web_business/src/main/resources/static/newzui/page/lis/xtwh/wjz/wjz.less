.xmzb-top-left{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  i{
    margin-right: 5px;
    &:nth-child(2){
      margin-right: 19px;
    }
  }
}



.ksys-side{
  width: 100%;
  padding: 26px 17px;
  float: left;
  span{
    display: block;
    width: 100%;
    position: relative;
    i{
      display: block;
      width: 100%;
      line-height: 36px;
    }
  }
  #jyxm_icon .switch{
    top:0;
    left:23px;
  }

}
.border-r4{
  border-radius: 4px !important;
}
.ksys-btn{
  position: absolute;
  bottom: 20px;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 40px;
  button{
    margin-right: 20px;
  }
}