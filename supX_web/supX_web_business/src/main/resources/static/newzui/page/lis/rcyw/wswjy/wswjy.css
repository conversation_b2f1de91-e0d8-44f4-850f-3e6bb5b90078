.xmzb-top {
  width: 100%;
  padding: 15px 34px;
}
.xmzb-top-left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
}
.xmzb-top-left i {
  margin-right: 5px;
}
.xmzb-top-left i:nth-child(2) {
  margin-right: 19px;
}
.xmzb-content {
  width: 100%;
  padding: 15px 10px;
  box-sizing: border-box;
  float: left;
}
.xmzb-content-left {
  width: 22%;
  float: left;
}
.xmzb-content-left .content-left-top {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  border: 1px solid #e9eee6;
  background: #edf2f1;
  height: 36px;
  line-height: 36px;
  align-items: center;
}
.xmzb-content-left .content-left-top i {
  width: calc((100% / 3));
  text-align: center;
}
.xmzb-content-left .content-left-list {
  width: 100%;
  height: 80vh;
  overflow: auto;
  border: 1px solid #e9eee6;
  border-top: none;
  border-right: none;
}
.xmzb-content-left .content-left-list li {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
  height: 54px;
  border-top: 1px solid #e9eee6;
}
.xmzb-content-left .content-left-list li i {
  width: calc((100% / 3));
  text-align: center;
}
.xmzb-content-left .content-left-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-content-left .content-left-list li:first-child {
  border-top: none;
}
.xmzb-content-right {
  width: 77.5%;
  float: right;
}
.xmzb-content-right .content-right-top {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  border: 1px solid #e9eee6;
  background: #edf2f1;
  height: 36px;
  align-items: center;
}
.xmzb-content-right .content-right-top i {
  width: calc((100% / 6));
  text-align: center;
}
.xmzb-content-right .content-right-list {
  width: 100%;
  height: 80vh;
  overflow: auto;
  border: 1px solid #e9eee6;
  border-top: none;
  border-right: none;
}
.xmzb-content-right .content-right-list li {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  line-height: 54px;
  cursor: pointer;
  border-top: 1px solid #e9eee6;
}
.xmzb-content-right .content-right-list li i {
  width: calc((100% / 6));
  text-align: center;
}
.xmzb-content-right .content-right-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-content-right .content-right-list li:first-child {
  border-top: none;
}
.xmzb-title {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: #edf2f1;
  border: 1px solid #e9eee6;
  height: 34px;
}
.xmzb-title i {
  width: calc((100% / 5));
  text-align: center;
}
.xmzb-list {
  width: 100%;
  max-height: 320px;
  overflow: auto;
}
.xmzb-list li {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  line-height: 54px;
  border-top: 1px solid #e9eee6;
}
.xmzb-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-list li:first-child {
  border-top: none;
}
.xmzb-list i {
  width: calc((100% / 8));
  text-align: center;
}
.xmzb-list i input {
  width: 102px;
}
.font16 {
  font-size: 16px !important;
}
.xmzb-ok {
  width: 100%;
  height: 70px;
  border-top: 1px solid #e9eee6;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.xmzb-ok button {
  margin-right: 15px;
}
.font-icon {
  position: absolute;
  right: 90px;
  top: 3px;
  color: rgba(255, 255, 255, 0.8);
}
