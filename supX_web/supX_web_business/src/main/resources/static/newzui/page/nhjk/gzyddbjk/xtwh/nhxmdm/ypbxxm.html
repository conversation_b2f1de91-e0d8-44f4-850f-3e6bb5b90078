<div id="ypbxxm">
    <div class="yp_toolMenu flex-container flex-align-c padd-l-10 padd-r-10">
        <button class="tong-btn btn-parmary" @click="getData"><span class="fa fa-refresh"></span>刷新</button>
        <!--<button class="tong-btn btn-parmary" @click="save"><span class="fa fa-save"></span>保存</button>-->
        <!--<button class="tong-btn btn-parmary" @click="remove"><span class="fa fa-trash-o"></span>删除</button>-->
        <button class="tong-btn btn-parmary" @click="loadXm"><span class="fa fa-trash-o"></span>获取药品项目</button>
        <!--<button class="tong-btn btn-parmary" @click="autoDm"><span class="fa fa-save"></span>自动对码（项目名称）</button>-->
        <div class="flex-container flex-align-c padd-r-10">
            <span>检索项目：</span>
            <input type="text" class="zui-input wh180" @keyDown.13="goToPage(1)" v-model="searchtext2" id="search2"/>
        </div>
        <div class="flex-container flex-align-c padd-r-10">
            <span class="ft-14 padd-r-5">对码选择</span>
            <select-input class="wh120" @change-data="changeType" :not_empty="false"
                          :child="dmzt_tran" :index="type" :val="type"
                          :name="'type'">
            </select-input>
        </div>
        <div class="flex-container padd-r-10 flex-align-c">
            <span class="ft-14 padd-r-5">库房</span>
            <select-input class="wh120" @change-data="resultRydjChange"
                          :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                          :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
            </select-input>
        </div>
    </div>

    <div class="flex-container padd-t-10">
        <div class="zui-table-view margin-r-10 ypBxXm hzList-border flex-container flex-dir-c">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s">保险类别编码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">保险类别名称</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body  over-auto" @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td>
                            <div class="zui-table-cell cell-s">{{item.bxlbbm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.bxlbmc}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="zui-table-view ypXmMx hzList-border flex-container flex-dir-c">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s">状态</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">项目编码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xxl text-left">项目名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">费用单价</div>
                        </th>
                        <th >
                            <div style="color: rgb(31, 210, 115)" class="zui-table-cell cell-xxl ">农合名称</div>
                        </th>
                        <th>
                            <div style="color: rgb(31, 210, 115)" class="zui-table-cell cell-s">保险类型</div>
                        </th>
                        <th>
                            <div style="color: rgb(31, 210, 115)" class="zui-table-cell cell-xxl">农合编码</div>
                        </th>
                        <th>
                            <div style="color: rgb(31, 210, 115)" class="zui-table-cell cell-s">农合统筹</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">费用规格</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">是否药品</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l">手工编码</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body  over-auto" @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr @mouseenter="switchIndex('hoverIndex1',true,$index)" @click="checkOne($index),switchIndex('activeIndex1',true,$index)" @dblclick="edit($index)"
                        @mouseleave="switchIndex()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]">
                        <td>
                            <div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.xmbm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xxl text-left">{{item.xmmc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.fydj}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xxl">
                                <span v-show="isEdit != $index" v-text="item.bxxmmc"></span>
                                <input :id="'mc_'+$index" v-show="isEdit == $index" v-model="item.bxxmmc"
                                       @input="searching($index,false,'bxxmmc',$event.target.value)"
                                       @keyDown="changeDown($index,$event,'text')">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="page"
                                              @click-one="checkedOneOut" @click-two="selectOne">
                                </search-table>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.mc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xxl">{{item.bxxmbm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{nhtclb_tran[item.fw]}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.fygg}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{istrue_tran[item.ypfy]}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l">{{item.zbm}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
</div>
<script type="application/javascript" src="ypbxxm.js"></script>
