<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>设备参考值</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="../xmzb/xmzb.css"/>
    <link rel="stylesheet" href="sbckz.css"/>
</head>
<style>
    .fieldlist{
        display: none !important;
    }
</style>
<body class="skin-default">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">检索码</label>
                    <div class="zui-input-inline margin-l13">
                        <select-input @change-data="resultChange" :not_empty="true"
                                      :child="jysbList" :index="'hostname'" :index_val="'sbbm'"
                                      :val="jysbObj.sbbm" :search="true" :name="'jysbObj.sbbm'">
                        </select-input>
                    </div>
                </div>
                <!--<button class="zui-btn btn-primary xmzb-db">查询</button>-->
            </div>
        </div>
        <!--<div class="panel-head   background bg-fff">-->
            <!--<div class="zui-row">-->
                <!--<div class="col-x-12 bg-fff top-xia">-->
                    <!--<div class="col-x-6 xmzb-top-left">-->
                        <!--<i>检索码</i>-->
                        <!--<i>-->
                       		<!--<select-input @change-data="resultChange" :not_empty="true"-->
									<!--:child="jysbList" :index="'hostname'" :index_val="'sbbm'"-->
									<!--:val="jysbObj.sbbm" :search="true" :name="'jysbObj.sbbm'">-->
							<!--</select-input>-->
                        <!--</i>-->
                    <!--</div>-->
                <!--</div>-->
                <!--&lt;!&ndash;<input type="checkbox" />打印取报告条码000000000000&ndash;&gt;-->
                <!--&lt;!&ndash;<input type="checkbox" />登记后打印条码&ndash;&gt;-->

            <!--</div>-->
        <!--</div>-->
    </div>
    <div class="zui-table-view" style="border:none; margin-top:-7px">
        <div class="xmzb-content">
            <div class="xmzb-content-right sjks-content-right" style="width: 100%">
                <div class="content-right-top">
                    <!-- <i style="width: 50px !important;"><div class="zui-table-cell"><input type="checkbox"/></div></i> -->
                    <i>项目编码</i> 
                    <i>项目简称</i> 
                    <i>项目名称</i> 
                    <i>参考类型</i> 
                    <i>数据类型</i> 
                    <i>通道号</i>
                    <!--<i><em class="icon-bgzdgl"></em></i>-->

                </div>
                <ul class="content-right-list">
						<li v-for="(item, $index) in jysbCkz" @click="dbEdit(item)">
							<!-- <i style="width: 50px !important;"><div class="zui-table-cell"><input type="checkbox"/></div></i> -->
							<i v-text="item.zbbm"></i> <i v-text="item.ywmc"></i> <i
							v-text="item.zwmc"></i> <i
							v-text="zbbmcklx_tran[item.cklx]"></i> <i
							v-text="zbbmsjlx_tran[item.sjlx]"></i> <i
							v-text="item.tdh"></i> <em class="icon-glbgsx"></em>
						</li>
				</ul>
            </div>
        </div>
    </div>
</div>
<div id="pop">
    <!--<transition name="pop-fade">-->
    <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
    <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
        <div class="layui-layer-content" >
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="colse()">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
        </div>
    </div>
    <!--</transition>-->
</div>
<div class="side-form ng-hide jszb" style="padding-top: 0;" id="brzcList" role="form">
    <div class="tab-message">
        <a >设备参考值</a>
        <span class="toolBtu">
            <a class="icon-jia" style="margin-right: 47px" @click="xinzeng4">新增参考值</a>
            <!--xinzeng5-->
            <!--xinzeng6-->
            <!--xinzeng8-->
            <!--xinzeng9-->
            <!--xinzeng10-->
            <a class="icon-jia" style="margin-right: 30px" @click="xinzengheader">新增年龄段</a>

        <a href="javascript:;" class="fr closex ti-close" style="color:rgba(255,255,255,.56) !important;" @click="closes"></a>
        </span>
    </div>
    <div class="ksys-side">
        <div class="zui-row">
            <div class="col-fm-12">
            	<!-- 通用 -->
            	<div class="cankao-1" style="display: none">
                    <div class="  col-fm-2 col-width-3">
                        <div class="zui-input-inline">
                            <span class="span-input">最低值</span>
                            <input type="number" v-model="lisSbckzModel.allN" class="zui-input width-170">
                        </div>
                    </div>
                    <div class="  col-fm-2 col-width-3">
                        <div class="zui-input-inline">
                            <span class="span-input">最高值</span>
                            <input type="number" v-model="lisSbckzModel.allNH" class="zui-input width-170">
                        </div>
                    </div>
                </div>
                
                <div class="cankao-2" style="display: none">
                    <div class="  col-fm-2 col-width-3">
                        <div class="zui-input-inline">
                            <span class="span-input">参考值</span>
                            <input type="text" v-model="lisSbckzModel.allT" class="zui-input width-170">
                        </div>
                    </div>
                </div>
                
                
                <!-- 性别 -->
                <div class="cankao" style="display: none">
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3 left-20">
                            <div class="zui-input-inline">
                                <span class="span-input">男性</span>
                                <input type="text" v-model="lisSbckzModel.manT" class="zui-input ">
                            </div>
                        </div>
                    </div>
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3 left-20">
                            <div class="zui-input-inline">
                                <span class="span-input">女性</span>
                                <input type="text" v-model="lisSbckzModel.womanT" class="zui-input ">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="cankao1" style="display: none">
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3 left-20">
                            <p class="center bottom-4">男性</p>
                            <div class="zui-input-inline">
                                <span class="span-input">最低值</span>
                                <input type="number" v-model="lisSbckzModel.manN" class="zui-input ">
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <p class="center bottom-4">女性</p>
                            <div class="zui-input-inline">
                                <input type="number" v-model="lisSbckzModel.womanN" class="zui-input ">

                            </div>
                        </div>
                    </div>
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3 left-20">
                            <div class="zui-input-inline">
                                <span class="span-input">最高值</span>
                                <input type="number" v-model="lisSbckzModel.manNH" class="zui-input ">
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <input type="number" v-model="lisSbckzModel.womanNH" class="zui-input ">

                            </div>
                        </div>
                    </div>
                </div>
                
                
                <!-- <div class="cankao2" style="display: none">
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">年龄段1</span>
                                <input type="number" style="padding-right: 55px;"
                                       class="zui-input width-170">
                                <span class="absolate">岁以下</span>
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">男&emsp;&emsp;性</span>
                                <input type="number" class="zui-input width-170">
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">女&emsp;&emsp;性</span>
                                <input type="number" class="zui-input width-170">
                            </div>
                        </div>
                    </div>
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <div style="position: relative;display: flex;align-items: center;">
                                    <span class="span-input">年龄段2</span>
                                    <input type="number" style="width: 70px;padding-right: 26px;"
                                           class="zui-input ">
                                    <span class="abs">岁</span>
                                </div>
                                <span class="">至</span>
                                <div style="position: relative;display: flex;align-items: center;">
                                    <input type="number" style="width: 70px;padding-right: 26px;"
                                           class="zui-input ">
                                    <span class="abs">岁</span>
                                </div>

                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">男&emsp;&emsp;性</span>
                                <input type="number" class="zui-input width-170">
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">女&emsp;&emsp;性</span>
                                <input type="number" class="zui-input width-170">
                            </div>
                        </div>
                    </div>
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">年龄段1</span>
                                <input type="number" style="padding-right: 55px;"
                                       class="zui-input width-170">
                                <span class="absolate" style="right: 48px;">岁以上</span>
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">男&emsp;&emsp;性</span>
                                <input type="number" class="zui-input width-170">
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">女&emsp;&emsp;性</span>
                                <input type="number" class="zui-input width-170">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="cankao3" style="display: none">
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">年龄段1</span>
                                <input type="number" style="padding-right: 55px;"
                                       class="zui-input width-170">
                                <span class="absolate">岁以下</span>
                            </div>
                        </div>
                        <div class=" col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <div style="position: relative;display: flex;align-items: center;">
                                    <span class="span-input">男&emsp;&emsp;性</span>
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>
                                <span class="left-right"
                                      style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                <div style="position: relative;display: flex;align-items: center;">
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>

                            </div>
                        </div>
                        <div class=" col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <div style="position: relative;display: flex;align-items: center;">
                                    <span class="span-input">女&emsp;&emsp;性</span>
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>
                                <span class="left-right"
                                      style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                <div style="position: relative;display: flex;align-items: center;">
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <div style="position: relative;display: flex;align-items: center;">
                                    <span class="span-input">年龄段2</span>
                                    <input type="number" style="width: 70px;padding-right: 26px;"
                                           class="zui-input ">
                                    <span class="abs">岁</span>
                                </div>
                                <span class="left-right"
                                      style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                <div style="position: relative;display: flex;align-items: center;">
                                    <input type="number" style="width: 70px;padding-right: 26px;"
                                           class="zui-input ">
                                    <span class="abs">岁</span>
                                </div>

                            </div>
                        </div>
                        <div class=" col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <div style="position: relative;display: flex;align-items: center;">
                                    <span class="span-input">男&emsp;&emsp;性</span>
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>
                                <span class="left-right"
                                      style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                <div style="position: relative;display: flex;align-items: center;">
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>

                            </div>
                        </div>
                        <div class=" col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <div style="position: relative;display: flex;align-items: center;">
                                    <span class="span-input">女&emsp;&emsp;性</span>
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>
                                <span class="left-right"
                                      style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                <div style="position: relative;display: flex;align-items: center;">
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="col-fm-12">
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">年龄段1</span>
                                <input type="number" style="padding-right: 55px;"
                                       class="zui-input width-170">
                                <span class="absolate">岁以上</span>
                            </div>
                        </div>
                        <div class=" col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <div style="position: relative;display: flex;align-items: center;">
                                    <span class="span-input">男&emsp;&emsp;性</span>
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>
                                <span class="left-right"
                                      style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                <div style="position: relative;display: flex;align-items: center;">
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>

                            </div>
                        </div>
                        <div class=" col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <div style="position: relative;display: flex;align-items: center;">
                                    <span class="span-input">女&emsp;&emsp;性</span>
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>
                                <span class="left-right"
                                      style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                <div style="position: relative;display: flex;align-items: center;">
                                    <input type="number" style="width: 70px;" class="zui-input ">
                                </div>

                            </div>
                        </div>
                    </div>
                </div> -->
                <div style="display: none" class="cankao4">
                    <p class="buttom" @click="xinzeng4">新增参考值</p>
                    <ul class="ck-header" id="nl0">
                        <li class="line">
                            <span class="text" style="width: 175px;">年龄</span>
                            <span class="text" style="width: 157px;">年龄</span>
                            <span class="text">最小值</span>
                            <span class="text">最大值</span>
                            <span class="text">性别</span>
                            <span class="text">操作</span>
                        </li>
                        <li class="line bottom-border" v-for="(item, $index) in lisSbckzNlList">
                            <div class="text-calc">
                                <div><input type="number" style="width: 70px;" v-model="item.nl1"
                                            class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                </div>
                                <div class="zui-select-inline">
									<select-input :id="'nldw1_' + $index"
              											@change-data="resultChange_item" :not_empty="true"
              											:child="xtwhnldw_tran" :index="'item.nldw1'" :val="item.nldw1"
              											:name="$index + '.nldw1.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>			
                                </div>
                            </div>
                            <span class="left-right"
                                  style="min-width: auto;max-width: initial">至</span>
                            <div class="text-calc">
                                <div><input type="number" style="width: 70px;" class="zui-input " v-model="item.nl2" @keydown="nextFocus($event)">
                                </div>
                                <div class="zui-select-inline">
                                    <select-input :id="'nldw2_' + $index"
              											@change-data="resultChange_item" :not_empty="true"
              											:child="xtwhnldw_tran" :index="'item.nldw2'" :val="item.nldw2"
              											:name="$index + '.nldw2.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>	
                                </div>
                            </div>
                            <div class="text-calc">
                                <div><input type="number" style="width: 70px;" class="zui-input " v-model="item.min" @keydown="nextFocus($event)">
                                </div>
                            </div>
                            <div class="text-calc">
                                <div><input type="number" style="width: 70px;" class="zui-input " v-model="item.max" @keydown="nextFocus($event)">
                                </div>
                            </div>
                            <div class="text-calc">
                                <div class="zui-select-inline" style="width: 70px;margin: 0 auto;">
                                    <select-input :id="'xb_' + $index"
              											@change-data="resultChange_item" :not_empty="true"
              											:child="xtwhxb_tran" :index="'item.xb'" :val="item.xb"
              											:name="$index + '.xb.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>	
                                </div>
                            </div>
                            <div class="text-calc">
                                <div><span @click="sc($index)" class="icon-sc"></span></div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div style="display: none" class="cankao5">
                    <p class="buttom" @click="xinzeng5">新增参考值</p>
                    <ul class="ck-header" id="yb0">
                        <li class="line">
                            <span class="text">样本类型</span>
                            <span class="text">参考值</span>
                            <span class="text">操作</span>
                        </li>
                        <li class="line bottom-border"  v-for="(item,$index) in lisSbckzYblxList">
                            <div class="zui-select-inline text-calc">
                               <select-input @change-data="Wf_YppfChange" :not_empty="false"
			 									 	:child="ybbmList" :index="'ybmc'" :index_val="'ybbm'" 
			  										:val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
								</select-input>
                            </div>
                            <div class="text-calc"><input v-model="item.allT" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><span onclick="delok($index)" data-target="num5" data-num="aeaaaaee-beaf-4eee-aabf-bbefeabebfef" class="icon-sc"></span></div>
                        </li>
                    </ul>
                </div>
                <div style="display: none" class="cankao6">
                    <p class="buttom" @click="xinzeng6">新增参考值</p>
                    <ul class="ck-header" id="yb1">
                        <li class="line">
                            <span class="text">样本类型</span>
                            <span class="text">最低值</span>
                            <span class="text">最低值</span>
                            <span class="text">操作</span>
                        </li>
                        <li class="line bottom-border" v-for="(item,$index) in lisSbckzYblxList">
                            <div class="zui-select-inline text-calc">
									<select-input @change-data="Wf_YppfChange" :not_empty="false"
			 									 	:child="ybbmList" :index="'ybmc'" :index_val="'ybbm'" 
			  										:val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
									</select-input>
                            </div>
                            <div class="text-calc"><input v-model="item.allN" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><input v-model="item.allNH" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><span onclick="delok($index)"
                                                         data-num="efffbfaf-eeae-4fbb-bbfe-aefbaeafebef"
                                                         data-target="num6" class="icon-sc"></span>
                            </div>
                        </li>
                    </ul>
                </div>
                <!-- <div class="cankao7" >
                    <div class="  col-fm-12 " style="margin-bottom: 20px;">
                        <div class="zui-input-inline">
                            <span class="span-input" style="width: 65px">临床意义</span>
                            <textarea class="zui-input height-72 " value="临床意义结果精度">
                                        </textarea>
                        </div>
                    </div>
                </div> -->
                <div style="display: none" class="cankao8" >
                    <p class="buttom" @click="xinzeng8">新增参考值</p>
                    <ul class="ck-header" id="yb2">
                        <li class="line">
                            <span class="text">样本类型</span>
                            <span class="text">男性</span>
                            <span class="text">女性</span>
                            <span class="text">操作</span>
                        </li>
                        <li class="line bottom-border" v-for="(item,$index) in lisSbckzYblxList" >
                            <div class="zui-select-inline text-calc">
                                <select-input @change-data="Wf_YppfChange" :not_empty="false"
			 									 	:child="ybbmList" :index="'ybmc'" :index_val="'ybbm'" 
			  										:val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
									</select-input>
                            </div>
                            <div class="text-calc"><input v-model="item.manT" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><input v-model="item.womanT" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><span onclick="delok($index)"
                                                         data-num="aabaabff-afbe-4fbf-aeba-eebbeebfbbfa"
                                                         data-target="num8" class="icon-sc"></span>
                            </div>
                        </li>
                    </ul>
                </div>
                <div style="display: none" class="cankao9" >
                    <p class="buttom" @click="xinzeng9">新增参考值</p>
                    <ul class="ck-header" id="yb3">
                        <li class="line">
                            <span class="text">样本类型</span>
                            <span class="text">男性高值</span>
                            <span class="text">男性低值</span>
                            <span class="text">女性高值</span>
                            <span class="text">女性低值</span>
                            <span class="text">操作</span>
                        </li>
                        <li class="line bottom-border" v-for="(item,$index) in lisSbckzYblxList">
                            <div class="zui-select-inline text-calc">
                                <select-input @change-data="Wf_YppfChange" :not_empty="false"
			 									 	:child="ybbmList" :index="'ybmc'" :index_val="'ybbm'" 
			  										:val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
									</select-input>
                            </div>
                            <div class="text-calc"><input v-model="item.manNH" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><input v-model="item.manN" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><input v-model="item.womanNH" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><input v-model="item.womanN" class="zui-input" @keydown="nextFocus($event)"></div>
                            <div class="text-calc"><span onclick="delok($index)"
                                                         data-num="efeebebf-ebaf-4ebe-baaf-eeebbbeeffef"
                                                         data-target="num9" class="icon-sc"></span>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="cankao10">
                    <span class="buttom" @click="xinzeng10">新增参考值</span>
                     <ul class="ck-header" id="yb3">
                        <li class="line" style="margin-bottom: 20px;">
                        <!-- @dblclick="delHeader(index)" -->
                         <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="txt">样本类型</span></p>
                         <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">样本类型</span></p>
                         <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段1</span></p>
                          <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">低值</span></p>
                          <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"> <span class="text">高值</span></p>
                          <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段2</span></p>
                           <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">低值</span></p>
                           <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">高值</span></p>
                          <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段3</span></p>
                            <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">低值</span></p>
                            <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">高值</span></p>
                            <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段4</span></p>
                            <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">低值</span></p>
                            <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">高值</span></p>
                            <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">操作</span></p>
                        </li>
                        <li class="line bottom-border" v-for="(item,$index) in lisSbckzYblxList">
                                <select-input style="min-width: 14%; width: 14%;" @change-data="Wf_YppfChange" :not_empty="false"
			 									 	:child="ybbmList" :index="'ybmc'" :index_val="'ybbm'" 
			  										:val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
									</select-input>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1N" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1NH" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2N" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2NH" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3N" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3NH" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4N" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4NH" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><span onclick="delok($index)"
                                                         data-num="efeebebf-ebaf-4ebe-baaf-eeebbbeeffef"
                                                         data-target="num9" class="icon-sc"></span>
                            </div>
                        </li>
                    </ul>
                </div>
                <div style="display: none" class="cankao11">
                    <span class="buttom" @click="xinzeng10">新增参考值</span>
                     <ul class="ck-header" id="yb3">
                        <li class="line" style="margin-bottom: 20px;">
                        <!-- @dblclick="delHeader(index)" -->
                         <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="txt">样本类型</span></p>
                         <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">样本类型</span></p>
                         <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段1</span></p>
                          <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">参考值</span></p>
                          <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"> <span class="text">高值</span></p>
                          <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段2</span></p>
                           <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">参考值</span></p>
                          <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段3</span></p>
                           <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">参考值</span></p>
                            <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段4</span></p>
                             <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">参考值</span></p>
                            <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">操作</span></p>
                        </li>
                        <li class="line bottom-border" v-for="(item,$index) in lisSbckzYblxList">
                                <select-input style="min-width: 14%; width: 14%;" @change-data="Wf_YppfChange" :not_empty="false"
			 									 	:child="ybbmList" :index="'ybmc'" :index_val="'ybbm'" 
			  										:val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
									</select-input>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1T" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2T" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3T" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4T" class="zui-input"></div>
                            <div class="text-calc" style="min-width: 14%; width: 14%;"><span onclick="delok($index)"
                                                         data-num="efeebebf-ebaf-4ebe-baaf-eeebbbeeffef"
                                                         data-target="num9" class="icon-sc"></span>
                            </div>
                        </li>
                    </ul>
                </div>
               <!--  <div  class="cankao10">
                    <span class="buttom" @click="xinzeng10">新增参考值</span>
                    <span class="buttom" @click="xinzengheader">新增年龄段</span>
                    <div style="width: 100%;">
                        <ul class="ck-header" id="yb4">
                            <li class="line bg-f2" style="margin-bottom: 18px;">
                                <span class="text" style="min-width: 14%;width: 14%">样本类型</span>
                                <p @dblclick="delHeader(index)" class="text "
                                   style="min-width: 25%;background: #edf2f1;max-width: 25%"
                                   v-for="(list,index) in pop10"><span class="txt">{{list}}</span>
                                </p>
                                <span class="text absol" style="width:80px;">操作</span>
                            </li>
                            <li class="line bottom-border clone" id="clone">
                                <div class="zui-select-inline text-calc" style="min-width: 14%;width: 14%">
                                    <input type="text" class="zui-input" name="input1"
                                           placeholder="请选择" readonly/>
                                    <div class="zui-select-group" role="listbox">
                                        <ul class="inner">
                                            <li value="0">年</li>
                                            <li value="1">月</li>
                                            <li value="2">日</li>
                                            <li value="3">小时</li>
                                            <li value="3">分</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="text-calc absol" style="width:80px;background: #fff"><span onclick="delok(this)"  class="icon-sc"></span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div> -->
            </div>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
        </div>
    </div>
    <div class="filter">
        <filter-select v-on:close="guanbi" v-on:save="baocun" v-if="isShow"></filter-select>
    </div>
</div>
    <script src="sbckz.js"></script>
<script type="text/javascript">
    $(".zui-input").uicomplete();
</script>
</body>
</html>