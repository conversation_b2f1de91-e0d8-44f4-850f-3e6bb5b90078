.fydm-content-left {
  width: 49%;
  float: left;
}
.fydm-content-left .content-left-top {
  width: 100%;
}
.fydm-content-left .content-left-top i {
  width: calc((100% / 6));
  text-align: center;
}
.fydm-content-left .sjks-content-left-list li {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
}
.fydm-content-left .sjks-content-left-list li i {
  width: calc((100% / 6));
  text-align: center;
}
.fydm-content-left .sjks-content-left-list li:hover {
  background: rgba(26, 188, 156, 0.08);
  border: 1px solid #1abc9c;
}
.pop-content {
  width: 100%;
  float: right;
}
.pop-content .content-right-top,
.pop-content .content-right-list {
  width: 100%;
}
.pop-content .content-right-top i,
.pop-content .content-right-list i,
.pop-content .content-right-top span,
.pop-content .content-right-list span {
  width: calc((100% / 3));
  text-align: center;
}
.pop-content .content-right-top i em,
.pop-content .content-right-list i em,
.pop-content .content-right-top span em,
.pop-content .content-right-list span em {
  margin-top: 10px;
}
.pop-content li {
  cursor: pointer;
  width: 100%;
}
.pop-content li:hover {
  background: rgba(26, 188, 156, 0.08) !important;
  border: 1px solid #1abc9c;
}
.ksys-side {
  width: 100%;
  padding: 26px 17px;
  float: left;
}
.ksys-side .jiansuo {
  margin-bottom: 20px;
}
.ksys-side .abs {
  color: #1abc9c;
}
.ksys-side .absolate {
  color: #1abc9c;
  right: 48px;
}
.ksys-side .absolate i {
  width: 56px;
  float: left;
  align-items: center;
  align-self: center;
}
.ksys-side .left-right {
  min-width: auto;
  max-width: none;
  padding: 0;
  margin: 0px 5px;
}
.ksys-side #jyxm_icon .switch {
  top: 0;
  left: 17px;
}
.border-r4 {
  border-radius: 4px !important;
}
.ksys-btn {
  position: absolute;
  bottom: 20px;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 40px;
}
.ksys-btn button {
  margin-right: 20px;
}
.xmzb-top {
  width: 100%;
  padding: 15px 34px;
}
.xmzb-top-left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.xmzb-top-left i {
  margin-right: 5px;
}
.xmzb-top-left i:nth-child(2) {
  margin-right: 19px;
}
.xmzb-content {
  width: 100%;
  padding: 15px 10px;
  box-sizing: border-box;
  float: left;
}
.xmzb-content-left {
  width: 35%;
  float: left;
}
.xmzb-content-left .content-left-top {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  border: 1px solid #e9eee6;
  background: #edf2f1;
  height: 36px;
  line-height: 36px;
  align-items: center;
}
.xmzb-content-left .content-left-top i {
  width: calc((100% / 3));
  text-align: center;
}
.xmzb-content-left .content-left-list {
  width: 100%;
  height: 80vh;
  overflow: auto;
  border: 1px solid #e9eee6;
  border-top: none;
  border-right: none;
}
.xmzb-content-left .content-left-list li {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 54px;
  border-top: 1px solid #e9eee6;
}
.xmzb-content-left .content-left-list li i {
  width: calc((100% / 3));
  text-align: center;
}
.xmzb-content-left .content-left-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-content-left .content-left-list li:first-child {
  border-top: none;
}
.xmzb-content-right {
  width: 100%;
  float: right;
}
.xmzb-content-right .content-right-top {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  border: 1px solid #e9eee6;
  background: #edf2f1;
  height: 36px;
  align-items: center;
}
.xmzb-content-right .content-right-top i {
  width: calc((100% - 50px)/6);
  text-align: center;
}
.xmzb-content-right .content-right-list {
  width: 100%;
  height: 80vh;
  overflow: auto;
}
.xmzb-content-right .content-right-list li {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  line-height: 54px;
  border: 1px solid #e9eee6;
  border-top: none;
}
.xmzb-content-right .content-right-list li i {
  width: calc((100% -50px)/6);
  text-align: center;
}
.xmzb-content-right .content-right-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-content-right .content-right-list li:first-child {
  border-top: none;
}
.xmzb-title {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: #edf2f1;
  border: 1px solid #e9eee6;
  height: 34px;
}
.xmzb-title i {
  width: calc((100% / 5));
  text-align: center;
}
.xmzb-list {
  width: 100%;
  max-height: 320px;
  overflow: auto;
}
.xmzb-list li {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  line-height: 54px;
  border-top: 1px solid #e9eee6;
}
.xmzb-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-list li:first-child {
  border-top: none;
}
.xmzb-list i {
  width: calc((100% / 5));
  text-align: center;
}
.font16 {
  font-size: 16px !important;
}
.xmzb-ok {
  width: 100%;
  height: 70px;
  border-top: 1px solid #e9eee6;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.xmzb-ok button {
  margin-right: 15px;
}
.font-icon {
  position: absolute;
  right: 90px;
  top: 3px;
  color: rgba(255, 255, 255, 0.8);
}
.icon-jia {
  position: relative;
  opacity: 0.56;
}
.icon-jia:before {
  position: absolute;
  left: -30px;
  top: -3px;
  width: 22px;
  height: 22px;
  content: '';
  border-radius: 100%;
  border: 1px solid #ffffff;
}
.icon-jia:after {
  position: absolute;
  content: '+';
  font-size: 22px;
  color: #fff5e6;
  left: -26px;
  top: -5px;
}
