<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="renderer" content="webkit">
	<title>住院管理</title>
	<script type="application/javascript" src="/newzui/pub/top.js"></script>
	<link rel="stylesheet" href="/newzui/css/icon.css">
	<link rel="stylesheet" href="/pub/css/print.css" media="print" />
	<link rel="stylesheet" href="cydj.css">
</head>

<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 ">
<div id="loadingPrint"></div>
<div class="wrapper  flex-container flex-dir-c">
	<div id="menu-butt" v-cloak class="panel printHide">
		<div class="tong-top">
			<button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getData()">刷新</button>
			<button class="tong-btn btn-parmary-b icon-ff" @click="getCyData()">获取申请</button>
			<button class="tong-btn btn-parmary-b icon-ff" v-if="show" @click="insert()">登记</button>
<!--			<button class="tong-btn btn-parmary-b icon-ff" @click="xmhb()">项目合并</button>-->
			<button class="tong-btn btn-parmary-b icon-ff" v-if="!show" @click="printInit()">打印</button>
			<button class="tong-btn btn-parmary-b icon-ff" v-if="!show" @click="printList()">打印表单</button>
		</div>
	</div>
	<div id="table-switch" v-cloak class="printHide">
		<tabs :num="num"  class="margin-l-10 padd-r-10" :tab-child="[{text:'采样登记'},{text:'登记查询'}]" @tab-active="tabActive"></tabs>
	</div>
	<!-- 采样登记 begin -->
	<div id="cydj" v-cloak class=" flex-one flex-container flex-dir-c" v-show="show">
		<div class="tong-search">
			<div class="zui-form">
				<div class="zui-inline">
					<span class="zui-form-label">申请时间</span>
					<div class="flex-container flex-align-c relative">
						<i class="icon-position icon-rl"></i>
						<input class="zui-input todate wh200 text-indent20" placeholder="请选择开始时间" id="cydj-time-begin" /><span class="padd-l-5 padd-r-5">~</span>
						<input class="zui-input todate wh200 " placeholder="请选择结束时间" id="cydj-time-end" />
					</div>
				</div>
				<div class="zui-inline">
					<span class="zui-form-label">是否打印</span>
					<select-input class="wh70" @change-data="resultChangeData" :not_empty="false"
								  :child="IsPrint_tran" :index="popContent.PrintType" :val="popContent.PrintType"
								  :name="'popContent.PrintType'">
					</select-input>
				</div>

			</div>
		</div>

		<div class="zui-table-view flex-one flex-container flex-dir-c" >
			<div class="zui-table-header">
				<table class="zui-table table-width50">
					<thead>
					<tr>
						<th class="cell-m">
							<input-checkbox @result="reCheckBoxAll" :list="'jsonList'"
											:type="'all'" :val="isCheckAll">
							</input-checkbox>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m"><span>打印</span></div>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m"><span>采样</span></div>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m"><span>急诊</span></div>
						</th>
						<th class="cell-s">
							<div class="zui-table-cell cell-s"><span>住院号</span></div>
						</th>
						<th class="cell-s">
							<div class="zui-table-cell cell-s"><span>病人姓名</span></div>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m"><span>性别</span></div>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m"><span>年龄</span></div>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-s"><span>床号</span></div>
						</th>
						<th class="cell-xl">
							<div class="zui-table-cell cell-xl"><span>检验申请项目</span></div>
						</th>
						<th class="cell-l">
							<div class="zui-table-cell cell-l"><span>申请日期</span></div>
						</th>
						<th class="cell-l">
							<div class="zui-table-cell cell-l"><span>采样日期</span></div>
						</th>
						<th class="cell-s">
							<div class="zui-table-cell cell-s"><span>送检科室</span></div>
						</th>
						<th class="cell-s">
							<div class="zui-table-cell cell-s"><span>送检医师</span></div>
						</th>
						<th class="cell-xl">
							<div class="zui-table-cell cell-xl"><span>临床诊断</span></div>
						</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
				<table class="zui-table table-width50" v-if="jsonList.length!=0">
					<tbody>
					<tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]" @mouseenter="hoverMouse(true,$index)"
						@mouseleave="hoverMouse()" :tabindex="$index" v-for="(item, $index) in jsonList">
						<td class="cell-m">
							<input-checkbox @result="checkedSelectFn" :list="'jsonList'" :type="'some'" :which="$index" :val="checkedSelect[$index]">
							</input-checkbox>
						</td>
						<td class="cell-m">
							<input-checkbox :list="'jsonList'" :type="'some'" :which="$index" :val="printChecked[$index]">
							</input-checkbox>
						</td>
						<td class="cell-m">
							<input-checkbox :list="'jsonList'" :type="'some'" :which="$index" :val="cyChecked[$index]">
							</input-checkbox>
						</td>
						<td class="cell-m">
							<input-checkbox  :list="'jsonList'" :type="'some'" :which="$index" :val="jzChecked[item.jzbz]">
							</input-checkbox>
						</td>
						<td class="cell-s">
							<div class="zui-table-cell cell-s"><span v-text="item.bah"></span></div>
						</td>
						<td class="cell-s">
							<div class="zui-table-cell cell-s"><span v-text="item.brxm"></span></div>
						</td>
						<td class="cell-m">
							<div class="zui-table-cell cell-m"><span v-text="brxb_tran[item.xb]">性别</span></div>
						</td>
						<td class="cell-m">
							<div class="zui-table-cell cell-m"><span v-text="item.nl"></span></div>
						</td>
						<td class="cell-m">
							<div class="zui-table-cell cell-s"><span v-text="item.cwh">床号</span></div>
						</td>
						<td class="cell-xl">
							<div class="zui-table-cell cell-xl text-over-2 text-left"><span v-text="item.fymc">检验申请项目</span></div>
						</td>
						<td class="cell-l">
							<div class="zui-table-cell cell-l"><span v-text="fDate(item.sqrq,'date')">申请日期</span></div>
						</td>
						<td class="cell-l">
							<div class="zui-table-cell cell-l"><span v-text="fDate(item.cyrq,'date')">采样日期</span></div>
						</td>
						<td class="cell-s">
							<div class="zui-table-cell cell-s"><span v-text="item.sjksmc">送检科室</span></div>
						</td>
						<td class="cell-s">
							<div class="zui-table-cell cell-s"><span v-text="item.sjysxm">送检医师</span></div>
						</td>
						<td class="cell-xl">
							<div class="zui-table-cell cell-xl"><span v-text="item.lczd">临床诊断</span></div>
						</td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>

	</div>
	<!-- 采样登记 end -->

	<!-- 登记查询 begin -->
	<div id="cxdj" v-cloak class=" flex-one flex-container flex-dir-c" v-show="show">
		<div class="tong-search printHide">
			<div class="zui-form">
				<div class="zui-inline">
					<span class="zui-form-label ">申请时间</span>
					<div class="flex-container flex-align-c relative">
						<i class="icon-position icon-rl"></i>
						<input class="zui-input todate wh200 text-indent20" placeholder="请选择开始时间" id="cxdj-time-begin" /><span class="padd-l-5 padd-r-5">~</span>
						<input class="zui-input todate wh200 " placeholder="请选择结束时间" id="cxdj-time-end" />
					</div>
				</div>
				<div class="zui-inline">
					<span class="zui-form-label">打印份数</span>
					<div class="flex-container flex-align-c relative">
						<input class="zui-input todate wh50" v-model.number="dyfs" placeholder="份数"  />
					</div>
				</div>
				<div class="zui-inline">
					<span class="zui-form-label">是否打印</span>
					<select-input class="wh70" @change-data="resultChangeData" :not_empty="false"
								  :child="IsPrint_tran" :index="popContent.PrintType" :val="popContent.PrintType"
								  :name="'popContent.PrintType'">
					</select-input>
				</div>
				<div class="zui-inline">
					<span class="zui-form-label">核收状态</span>
					<select-input class="wh70" @change-data="resultChangeData" :not_empty="false"
								  :child="istrue_tran" :index="popContent.ybhsbz" :val="popContent.ybhsbz"
								  :name="'popContent.ybhsbz'">
					</select-input>
				</div>
				<div class="flex-container flex-align-c relative">
					<span class="ft-14 padd-r-5">检索</span>
					<input class="zui-input  wh122" @keydown.enter="getData()" v-model="popContent.parm"   />
				</div>
			</div>
		</div>

		<div class="zui-table-view flex-one flex-container flex-dir-c" >
			<div class="zui-table-header">
				<table class="zui-table table-width50">
					<thead>
					<tr>
						<th class="cell-m printHide">
							<input-checkbox @result="reCheckBoxAll" :list="'jsonList'"
											:type="'all'" :val="isCheckAll">
							</input-checkbox>
						</th>
						<th class="cell-m printHide">
							<div class="zui-table-cell cell-m"><span>打印</span></div>
						</th>
						<th class="cell-m printHide">
							<div class="zui-table-cell cell-m"><span>采样</span></div>
						</th>
						<th >
							<div class="zui-table-cell cell-s"><span>住院号</span></div>
						</th>
						<th>
							<div class="zui-table-cell cell-s"><span>病人姓名</span></div>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m"><span>性别</span></div>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m"><span>年龄</span></div>
						</th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m"><span>床号</span></div>
						</th>
						<th>
							<div class="zui-table-cell cell-l  text-left"><span>检验申请项目</span></div>
						</th>
						<th class="printHide">
							<div class="zui-table-cell cell-s"><span>核收状态</span></div>
						</th>
						<th class="printHide">
							<div class="zui-table-cell cell-s"><span>审核状态</span></div>
						</th>
						<th>
							<div class="zui-table-cell cell-s"><span>申请日期</span></div>
						</th>
						<th class="printHide">
							<div class="zui-table-cell cell-s"><span>采样日期</span></div>
						</th>
						<th class="printHide">
							<div class="zui-table-cell cell-s"><span>送检科室</span></div>
						</th>
						<th>
							<div class="zui-table-cell cell-s"><span>送检医师</span></div>
						</th>
						<th class="printHide">
							<div class="zui-table-cell cell-xl"><span>临床诊断</span></div>
						</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
				<table class="zui-table table-width50" v-if="jsonList.length!=0">
					<tbody>
					<tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]" @mouseenter="hoverMouse(true,$index)"
						@mouseleave="hoverMouse()" :tabindex="$index" v-for="(item, $index) in jsonList">
						<td class="cell-m printHide">
							<input-checkbox @result="checkedSelectFn" :list="'jsonList'" :type="'some'" :which="$index" :val="checkedSelect[$index]">
							</input-checkbox>
						</td>
						<td class="cell-m printHide">
							<input-checkbox @result="printCheckedFn" :list="'jsonList'" :type="'some'" :which="$index" :val="printChecked[$index]">
							</input-checkbox>
						</td>
						<td class="cell-m printHide">
							<input-checkbox @result="cyCheckedFn" :list="'jsonList'" :type="'some'" :which="$index" :val="cyChecked[$index]">
							</input-checkbox>
						</td>
						<td>
							<div class="zui-table-cell cell-s"><span v-text="item.bah">住院号</span></div>
						</td>
						<td>
							<div class="zui-table-cell cell-s"><span v-text="item.brxm">病人姓名</span></div>
						</td>
						<td class="cell-m">
							<div class="zui-table-cell cell-m"><span v-text="brxb_tran[item.xb]">性别</span></div>
						</td>
						<td class="cell-m">
							<div class="zui-table-cell cell-m"><span v-text="item.nl">年龄</span></div>
						</td>
						<td class="cell-m">
							<div class="zui-table-cell cell-m"><span v-text="item.cwh">床号</span></div>
						</td>
						<td>
							<div class="zui-table-cell cell-l text-over-2 text-left"><span v-text="item.fymc">检验申请项目</span></div>
						</td>
						<td class="printHide">
							<div class="zui-table-cell cell-s"><span v-text="istrue_tran[item.ybhsbz]">核收状态</span></div>
						</td>
						<td class="printHide">
							<div class="zui-table-cell cell-s"><span v-text="istrue_tran[item.shbz]">核收状态</span></div>
						</td>
						<td>
							<div class="zui-table-cell cell-s"><span v-text="fDate(item.sqrq,'date')">申请日期</span></div>
						</td>
						<td class="printHide">
							<div class="zui-table-cell cell-s"><span v-text="fDate(item.cyrq,'date')">采样日期</span></div>
						</td>
						<td class="printHide">
							<div class="zui-table-cell cell-s"><span v-text="item.sjksmc">送检科室</span></div>
						</td>
						<td>
							<div class="zui-table-cell cell-s"><span v-text="item.sjysxm">送检医师</span></div>
						</td>
						<td class="printHide">
							<div class="zui-table-cell cell-xl"><span v-text="item.lczd">临床诊断</span></div>
						</td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
<!-- 登记查询 end -->
<script src="cydj.js"></script>
<script src="/newzui/js/plugins/JsBarcode.all.min.js"></script>
</body>

</html>
