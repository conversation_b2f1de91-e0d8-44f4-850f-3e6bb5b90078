<!DOCTYPE html>
<html lang="en">
<head>
    <link href="userPage/user.css" rel="stylesheet">
</head>
<style>
    .template{
        height: 100%;
    }
    .wapheight{
        height: calc(100% - 66px);
    }
    .pg-pop-input{
        border:1px solid #dfe3e9;
        border-radius:4px;
        width: 284px;
        height:52px;
    }
    .pop-width{
        width: 380px;
    }
    .layui-height{
        line-height: initial;
    }
    #pop .layui-height input,#pop .layui-height .zui-select-inline{
        width: 284px;
    }
    .pop-table .ksys-btn{
        height: 70px;
    }
    .lj-pop-text{
        font-size: 14px;
        color: #354052;
    }
    .ypmc_Icon{
        position: relative;
    }
    .ypmc_Icon{
        background-image: url(/newzui/pub/image/que.png);
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
        width: 20px;
        height: 20px;
        float: left;
        margin-top: 4px;
    }
    .height{
        height: 100%
    }
    .mar-auto{
        margin: 0 auto;
    }
    .template-over{
        overflow: auto;
    }
    .template-over::-webkit-scrollbar-thumb{
        background: none;
    }
    .template-over:hover::-webkit-scrollbar-thumb{
        background-color: rgba(0, 0, 0, 0.5);
    }
    .template-over::-webkit-scrollbar-button {
        display: none;
    }
    .template-over::-webkit-scrollbar {
        width: 0;
    }
    .zui-table-32 input{
        height: 28px;
    }
    .cell-m{
        width: 50px;
    }
    .col-l-8{
    width: 67.5%;
    }
    .col-l-4{
        height: 100%;overflow: hidden;width: 32.5%
    }
    .jieduan-box .item{
        width: 100%;
    }
    .flex-day{
        width: 100%;
    }
    .fontWeight{
        font-weight:normal;
    }
    .lj-zx-active{
        /*background:rgba(26,188,156,0.08);*/
        background:rgba(191, 199, 195, .2);
    }
    .hz-width .cell-s{
        min-width: 100px !important;
        width: auto !important;
    }
    .layui-mad{
        padding: 17px 13px 0;
    }
    .hzList-border{
    border:1px solid #eee
    }
    .hzList-border .zui-table{
        border-left: none;
    }
    .yw-wh-100{
        width: 100%;
    }
</style>
<body>
<div class=" flex-container flex-dir-c wapheight position">
    <div class="panel padd-l-10 padd-r-10 flex-one flex-container flex-dir-c ">
     <div  class="flex-one flex-container flex-dir-c">
         <lujing-show :ljlist="ljlist" :child="'list'" :xsmc="'text'" :settext="setText" @doshow="doshow" @gettext="gettext"></lujing-show>
        <p class="lclj-title">适用对象：第一诊断为急性（. HHICD-10:K35.902）标准住院日：大于7-10天 标准费用：10000-12000元&emsp;&emsp;说明：无无无无无无无无无</p>
        <timer-shaft @timer="changeTimer" v-if="isckShow" :optionlist="optionList" :ischecked="ischecked"   :daylist="day"></timer-shaft>
        <div class="grid-box margin-top-5 flex-one" style="overflow: hidden;">
            <div class="col-l-4 flex-container flex-dir-c padd-b-10" >
                <!--template-->
                <td-template @menu-active="menuActive" :ischecked="ischecked" @checked="checked" :istype="istype" :isck="isck" :iscon="false" @menu-click="menu"
                             :contextmenuone="contextmenuone" :contextmenutwo="contextmenutwo"
                             :optionlist="optionList"></td-template>
            </div>
            <div class="col-l-8 flex-dir-c height hz-width padd-b-10 padd-l-10 ">
                <div class="zui-table-view hzList flex-container flex-dir-c">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50" style="table-layout:fixed">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </th>
                                <th class="cell-m">
                                    <div class="zui-table-cell">序号</div>
                                </th>
                                <th class="cell-s">
                                    <div class="zui-table-cell ">医嘱类型</div>
                                </th>
                                <th style="overflow: hidden;" class="cell-xl">
                                    <div class="zui-table-cell text-over-2  text-left">药品名称</div>
                                </th>
                                <th class="cell-s">
                                    <div class="zui-table-cell ">单次剂量</div>
                                </th>
                                <th class="cell-s">
                                    <div class="zui-table-cell ">剂量单位</div>
                                </th>
                                <th class="cell-s">
                                    <div class="zui-table-cell ">剂量单位</div>
                                </th>
                                <th class="cell-s">
                                    <div class="zui-table-cell ">剂量单位</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body flex-one flex-align-c" data-no-change style="overflow: auto" @scroll="scrollTable($event)">
                        <table class="zui-table " style="table-layout:fixed">
                            <!--v-if="jsonList.length!=0"-->
                            <tbody>
                            <tr @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()" v-for="(item, $index) in 15"
                                @click="checkSelect([$index,'one','jsonList'],$event)"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                                <td class="cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </td>
                                <td class="cell-m">
                                    <div  class="zui-table-cell cell-m " v-text="$index"></div>
                                </td>
                                <td class="cell-s">
                                    <div  class="zui-table-cell " v-text="$index"></div>
                                </td>
                                <td class="flex-container text-left cell-xl" style="overflow: hidden;">
                                    <!--<i class="ypmc_Icon"></i>-->
                                    <div class="zui-table-cell text-over-2">
                                    混合痔临床路径表单混合痔临床路径表单混合痔临床路径表单</div>
                                </td>
                                <td class="cell-s">
                                    <div  class="zui-table-cell " v-text="$index"></div>
                                </td>
                                <td class="cell-s">
                                    <div  class="zui-table-cell " v-text="$index"></div>
                                </td>
                                <td class="cell-s">
                                    <div  class=" zui-table-32 padd-r-10 text-center" style="margin: 0 auto">
                                        <select-input  @change-data="resultChange" :not_empty="false"
                                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                                       :name="'popContent.yzxx'">
                                        </select-input>
                                    </div>
                                </td>
                                    <td class="cell-s ">
                                    <div  class="padd-r-20  zui-table-32   text-center" style="margin: 0 auto">
                                        <select-input  @change-data="resultChange" :not_empty="false"
                                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                                       :name="'popContent.yzxx'">
                                        </select-input>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <!--<p v-if="jsonList.length==0" class="flex-container flex-jus-c height flex-align-c">暂无医嘱</p>-->
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-tool hzgl-flex" v-if="dayActiveIndex==dayIndex">
            <button class="tong-btn xmzb-db   feise-bg btn-parmary paddr-r5 " @click="topNew">预览表单</button>
            <button class="tong-btn btn-parmary-f2a   xmzb-db " @click="pg">评估</button>
            <button class="tong-btn btn-parmary  xmzb-db" @click="doZjjl">执行</button>
        </div>


        <!--弹出层-->

        <div id="pop" v-if="isShow">
            <!--<transition name="pop-fade">-->
            <div class="pophide" :class="{'show':isShow}"></div>
            <div class="zui-form podrag pop-width bcsz-layer padd-b-15" :class="{'show':isShow}" style="height: max-content">
                <div class="layui-layer-title ">住院第一天-评估</div>
                <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close" @click="isShow=false"></a></span>
                <div class="layui-layer-content">
                    <div class=" layui-mad layui-height">
                        <div class="flex-container margin-b-15 flex-jus-sp">
                            <div class="flex-container">
                                <div class="position padd-r-5">
                                    <input type="radio" id="one" name="one" v-model="picked" value="0" class="zui-radio">
                                    <label for="one" class="padd-r-5" ></label>
                                </div>
                                <div class="lj-pop-text">正常</div>
                            </div>
                            <div class="flex-container">
                                <div class="position padd-r-5">
                                    <input type="radio" id="two" name="one" v-model="picked" value="1" class="zui-radio">
                                    <label for="two" class="padd-r-5"></label>
                                </div>
                                <div class="lj-pop-text">变异退出</div>
                            </div>
                            <div class="flex-container">
                                <div class="position padd-r-5">
                                    <input type="radio" id="three" name="one" v-model="picked" value="2" class="zui-radio">
                                    <label for="three" class="padd-r-5" ></label>
                                </div>
                                <div class="lj-pop-text">变异继续</div>
                            </div>
                            <div class="flex-container">
                                <div class="position padd-r-5">
                                    <input type="radio" id="four" name="one" v-model="picked" value="3" class="zui-radio">
                                    <label for="four" class="padd-r-5"></label>
                                </div>
                                <div class="lj-pop-text">变异转径</div>
                            </div>
                        </div>
                    <div class="flex-container  flex-jus-c margin-b-15" style="line-height: 34px">
                        <p class="whiteSpace margin-r-5 ft-14">&emsp;&emsp;{{popText}}</p>
                        <textarea :placeholder="popPlaceholder" v-model="popModel"  type="text" class="zui-input pg-pop-input"></textarea>
                    </div>
                        <div v-show="popIshow" class="flex-container flex-align-c flex-jus-c margin-b-20">
                            <label class="whiteSpace margin-r-5 ft-14">病种路径</label>
                            <select-input  @change-data="resultChange" :not_empty="false"
                                          :child="yzlx_tran01" :phd="'请选择病种路径'" :index="popContent.yzxx" :val="popContent.yzxx"
                                          :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                    </div>
                </div>
                <div class="zui-row buttonbox">
                    <button class="zui-btn xmzb-db table_db_esc margin-r-10 btn-default" @click="isShow=false">取消</button>
                    <button class="zui-btn xmzb-db btn-primary table_db_save" @click="Wf_save">确定</button>
                </div>
            </div>
            <!--</transition>-->
        </div>
     </div>
        <div class="flex-one flex-container flex-dir-c flex-jus-c mar-auto" style="display:none;">
            <p class="padd-b-10">患者还未入径，是否入径？</p>
            <button class="zui-btn xmzb-db btn-primary table_db_save mar-auto " @click="rj">入径</button>
        </div>
    </div>
    <div class="pop pop-table" v-if="ishow">
        <div class="pophide" :class="{'show':ishow}"></div>
        <div class="zui-form podrag pop-850 bcsz-layer padd-b-15  flex-dir-c" :class="{'flex-container':ishow}"  style="height: 500px">
            <div class="layui-layer-title text-left">医嘱执行单</div>
            <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close" @click="ishow=false"></a></span>
            <div class="layui-layer-content flex-container flex-dir-c flex-one" style="margin-bottom: 56px;">
                <div class=" layui-mad layui-height  flex-container flex-dir-c flex-one">
                    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-m">序号</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">医嘱类型</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl text-left">药品名称</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">单次剂量</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">使用人</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">剂量单位</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">剂量单位</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">剂量单位</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">剂量单位</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">剂量单位</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">剂量单位</div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one over-auto" data-no-change  @scroll="scrollTable($event)">
                            <table class="zui-table ">
                                <tbody>
                                <tr @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()" v-for="(item, $index) in 15"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                                    <td>
                                        <div  class="zui-table-cell cell-m">12</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s">长期</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-xl text-over-2 text-left">他把戳(抗甲亢要)</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s">一次两篇</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s">片</div>
                                    </td>
                                    <td>
                                        <select-input :cs="true" @change-data="resultChange_text" :not_empty="false"
                                                      :child="yzgl_tran" :index="'yfmc'" :index_val="'yfbm'" :val="popContent.yfbm"
                                                      :name="'popContent.yfbm'">
                                        </select-input>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s">片</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s">片</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s">片</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s">片</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s">片</div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class=" ksys-btn flex-container flex-align-c flex-jus-e">
                <button class="zui-btn xmzb-db table_db_esc btn-default" @click="ishow=false">取消</button>
                <button class="zui-btn xmzb-db btn-primary table_db_save" @click="Wf_save">确定执行</button>
            </div>
        </div>
    </div>
     <div class="pop popRj" v-if="isShow">
         <!--v-if="isshow"-->
         <!--:class="{'show':isShow}"-->
         <div class="pophide show" :class="{'show':isShow}" ></div>
         <div class="zui-form podrag pop-width  bcsz-layer padd-b-15" :class="{'show':isShow}">
             <div class="layui-layer-title ">临床路径入径</div>
             <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close" @click="isShow=false"></a></span>
             <div class="layui-layer-content">
                 <div class=" layui-mad layui-height">
                 <div class="flex-container flex-align-c flex-jus-c margin-b-15 margin-top-10">
                     <label class="whiteSpace margin-r-5 ft-14">&emsp;&emsp;病种</label>
                     <select-input  @change-data="resultChange" :not_empty="false"
                                    :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                    :name="'popContent.yzxx'">
                     </select-input>
                 </div>
                 <div  class="flex-container flex-align-c flex-jus-c margin-b-10">
                     <label class="whiteSpace margin-r-5 ft-14">开始阶段</label>
                     <select-input  @change-data="resultChange" :not_empty="false"
                                    :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                    :name="'popContent.yzxx'">
                     </select-input>
                 </div>

                 </div>
             </div>
             <div class="zui-row buttonbox">
                 <button class="zui-btn xmzb-db table_db_esc btn-default" @click="isShow=false">否</button>
                 <button class="zui-btn xmzb-db btn-primary table_db_save" @click="Wf_save">是</button>
             </div>
         </div>
     </div>
</div>
    <script type="text/javascript" src="userPage/lclj.js"></script>
</body>
</html>