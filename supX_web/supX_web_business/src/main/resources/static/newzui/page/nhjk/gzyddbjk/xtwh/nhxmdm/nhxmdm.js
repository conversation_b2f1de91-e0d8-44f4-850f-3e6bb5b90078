  //单页面菜单的加载
  var InfoMenu = new Vue({
      el: '#InfoMenu',
      data: {
          which: 0,
          pageIndex:['zlbxxm','ypbxxm','czydm','ksdm'],
      },
      created:function(){
          this.loadCon(0);
      },
      methods: {
          loadCon: function (index) {
              this.which=index;
              var pageDiv = $("#"+this.pageIndex[this.which]);
              $(".page_div").hide();
              if(pageDiv.length == 0){
                  $("."+this.pageIndex[this.which]).load(this.pageIndex[this.which]+".html").fadeIn(300);
              } else {
                  $("."+this.pageIndex[this.which]).fadeIn(300);
              }
          }
      }
  });
