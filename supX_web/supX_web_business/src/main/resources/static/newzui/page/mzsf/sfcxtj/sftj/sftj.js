    var datebegin = getTodayDateBegin();
    var dateend = getTodayDateEnd();
    var tableInfo=new Vue({
        el:'#kswh',
        mixins: [dic_transform, tableBase, baseFunc,mformat],
        data: {
            indexs: 0,
            jsonList: [],
            title: null,
            isShow: false,
            isShowpopL: false,
            isCheckedall: false,
            isChecked: [],
            cardList: [{bdfp:'全院',bdfpnum:1},{bdfp:'个人',bdfpnum:2}],

            readonly: false,
            qyOrGr: '1',   //判断是个人或者全院
            param: {
                page: 1,
                bdfpnum:'1',
                beginrq:'',
                endrq:'',
                sort: 'sfsj',
                order: 'desc'
            },
            money: 0,        //费用汇总金额
            jeContent: {}        //费用汇总金额
        },
        //页面渲染完成之后加载数据
        mounted: function () {
            //默认加载当前时间
            this.getData();
            this.param.beginrq=datebegin
            this.param.endrq=dateend
            //初始化页面需要加载的数据

            laydate.render({
                elem: '#startTime'
                , trigger: 'click'
                , theme: '#1ab394'
                ,format:'yyyy-MM-dd HH:mm:ss'
                ,done:function (value,data) {
                    tableInfo.param.beginrq=value
                    tableInfo.getData();
                }
            });
            laydate.render({
                elem: '#endTime'
                , trigger: 'click'
                , theme: '#1ab394'
                ,format:'yyyy-MM-dd HH:mm:ss'
                ,done:function (value,data) {
                    tableInfo.param.endrq=value
                    tableInfo.getData();
                }
            });
        },
        updated:function(){
            changeWin();
        },
        methods:{
            // 选中单条
            checkOne: function (event,index) {
                if(event.srcElement.checked==true){
                    this.isChecked[index] = false;

                }else{
                    this.isChecked[index] = true;
                }
            },

            // 选中全部
            checkAll: function (event) {
                if (event.srcElement.checked==true) {
                    for (var i = 0; i < this.jsonList.length; i++) {
                        Vue.set(this.isChecked,i,true);
                        this.isCheckedall=true
                        // this.isChecked[i] = true;
                    }
                } else {
                    this.isChecked = [];
                    this.isCheckedall=false
                }
            },
            resultChangeItem:function (event) {
                this.param.bdfpnum=event[0];
                this.$forceUpdate()
                this.getData();
            },
            go: function (index) {
                this.indexs = index;
                this.getData();
                this.isChecked = [];
                this.isCheckedall = false;
                switch (index){
                    case 0:
                        this.cardList=[{bdfp:'全院',bdfpnum:1},{bdfp:'个人',bdfpnum:2}];
                        break;
                    case 1:
                        this.cardList=[{bdfp:'核算科室',bdfpnum:1},{bdfp:'开单科室',bdfpnum:2}];
                        tableInfo.param.parm = "";
                        break;
                    case 2:
                        this.cardList=[{bdfp:'全院',bdfpnum:1},{bdfp:'个人',bdfpnum:2}];
                        tableInfo.param.parm = "";
                        break;
                }
            },
            getData: function () {
                this.param.sfjs=1;
                this.param.czybm='';
                switch (this.indexs){
                    case 0:
                        switch (this.param.bdfpnum){
                            case 2:
                                this.param.czybm=userId;
                        }
                        break;
                    case 2:
                        switch (this.param.bdfpnum){
                            case 2:
                                this.param.czybm=userId;
                        }
                        break;
                }
                switch (this.indexs){
                   case 0:
                       $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=query&parm="+JSON.stringify(this.param),function (json) {
                           if(json.a == 0){
                               tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                               tableInfo.jsonList = json.d.list;
                           }
                       });
                       this.fyhz();
                       break;
                   case 1:
                       this.param.zxks = "zxks";
                       this.param.sfjs='';
                       this.param.zxksmc = "zxksmc";
                       $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryKshz&parm=" + JSON.stringify(this.param), function (json) {
                           if (json.a == 0) {
                               tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                               tableInfo.jsonList = json.d.list;
                           }
                       });
                       break;
                   case 2:
                       $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=selectJsjlMx&parm="+JSON.stringify(this.param),function (json) {
                           if(json.a == 0){
                               tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                               tableInfo.jsonList = json.d.list;
                           }
                       });
                       this.fyhzSfcx();
                       break;
               }
            },
            //获取费用合计
            fyhz: function(){
                var str_param = {
                    beginrq:this.param.beginrq,
                    endrq:this.param.endrq,
                    parm : this.param.parm
                };
                //console.log(tableInfo.popContent.bdfp);
                if(this.param.bdfpnum=='2'){
                    str_param.czybm=userId;
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryByCzybm&parm="+JSON.stringify(str_param),function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if(json.a == 0 ){
                        tableInfo.money = json.d;//赋值操作
                    }else{
                        malert("获取汇总金额失败！"+json.c ,'top','defeadted');
                    }
                });
            },
            fyhzSfcx: function(){
                var str_param = {
                    beginrq : this.param.beginrq,
                    endrq : this.param.endrq
                };
                if(this.param.bdfpnum == '2'){
                    str_param.czybm = userId;
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryMzsfTjcxMap&parm="+JSON.stringify(str_param),function (json) {
                    if(json.a == 0 ){
                        tableInfo.jeContent = json.d;//赋值操作
                    }else{
                        malert("获取汇总金额失败！"+json.c ,'top','defeadted');
                    }
                });
            },
        }
    });

