<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>不良事件上报</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link href="hzbg.css" rel="stylesheet">
</head>
<body class="">
<div class="background-f blsjsb" style="overflow: auto; min-height: 92vmin;">
    <div class="font-24 padd-tb-24 font-weight color-c3 text-center">
        不良事件上报
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font-weight">基本信息</div>
        </div>
        <div class="tab-card-body">
            <div class="grid-box">
                <div class="col-xxl-12 col-x-12 col-s-12 col-l-12 col-xx-12 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">事件名称</label>
                        <input  class="zui-input background-f" type="text" v-model="popContent.sjmc"/>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">发生科室</label>
                        <select-input class="wh182 background-f" @change-data="resultChange"
                                      :child="fsks" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.ksbm"
                                      :name="'popContent.ksbm'" :search="true" :index_mc="'ksmc'">
                        </select-input>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">发生时间</label>
                        <div class="zui-date relative">
                            <i class="datenox icon-rl"></i>
                            <input id="timeVal"  readonly="readonly" v-model="popContent.fssj" class="zui-input wh182 background-f" type="text"/>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">事件类型</label>
                        <select-input @change-data="resultChange" :not_empty="true" :child="blsjlx_tran"
                                      :index="popContent.sjlx" :val="popContent.sjlx" :search="true"
                                      :name="'popContent.sjlx'">
                        </select-input>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">事件级别</label>
                        <select-input @change-data="resultChange" :not_empty="true" :child="blsjjb_tran"
                                      :index="popContent.sjjb" :val="popContent.sjjb" :search="true"
                                      :name="'popContent.sjjb'">
                        </select-input>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">填写时间</label>
                        <div class="zui-date relative">
                            <i class="datenox icon-rl"></i>
                            <input  id="timeVal1" readonly="readonly" v-model="popContent.txsj" class="zui-input wh182 background-f" type="text"/>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">报&nbsp;&nbsp;告&nbsp;人</label>
                        <input  class="zui-input wh182 background-f" type="text" v-model="popContent.bgrxm" readonly="readonly"/>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 ">
                    <div class="top-form">
                        <label class="top-label whiteSpace">联系电话</label>
                        <input  class="zui-input wh182 background-f" type="text" v-model="popContent.sjhm" readonly="readonly"/>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3" style="height: 36px;line-height: 36px">
                    <div class="top-form">
                        <label class="top-label whiteSpace padd-r-5">匿名上报</label>
                        <input type="radio" id="radio-1" name="sfnm" value="1" @click="sfnm" v-model="popContent.sfnm" class="h-radio">
                        <label for="radio-1" class="padd-r-20 ">是</label>
                        <input type="radio" id="radio-2" name="sfnm" value="2" @click="sfnm" v-model="popContent.sfnm"  class="h-radio">
                        <label for="radio-2">否</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font-weight">患者资料</div>
        </div>
        <div class="tab-card-body">
            <div class="grid-box">
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">患者姓名</label>
                        <select-input class="wh182 background-f" @change-data="setBrjbxx"
                                      :child="hzList" :index="'brxm'" :index_val="'brid'" :val="popContent.brid"
                                      :name="'popContent.brid'" :search="true" :index_mc="'brxm'">
                        </select-input>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">年&emsp;&emsp;龄</label>
                        <input  type="text" class="zui-input margin-r-5 background-f" v-model="hzzl.nl" style="width: 106px" readonly="readonly"/>
                        <select-input style="width: 72px" class="background-f" @change-data="resultChange"
                                      :child="xtwhnldw_tran" :index="'nldw'" :index_val="'nldw'" :val="hzzl.nldw"
                                      :name="'hzzl.nldw'" :search="true" :index_mc="'nldw'">
                        </select-input>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3" style="height: 36px;line-height: 36px">
                    <div class="top-form">
                        <label class="top-label whiteSpace padd-r-5">性&emsp;&emsp;别</label>
                        <input type="radio" id="radio-3" name="xb"  value="1" class="h-radio" v-model="hzzl.brxb" readonly="readonly">
                        <label for="radio-3" class="padd-r-20 ">男</label>
                        <input type="radio" id="radio-4" name="xb" value="2" class="h-radio" v-model="hzzl.brxb" readonly="readonly">
                        <label for="radio-4" class="padd-r-20">女</label>
                        <input type="radio" id="radio-5" name="xb" value="3" class="h-radio" v-model="hzzl.brxb" readonly="readonly">
                        <label for="radio-5">未知</label>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">所在科室</label>
                        <input type="text" class="wh182 zui-input background-f" v-model="hzzl.ksmc"  readonly="readonly"/>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">床&emsp;&emsp;号</label>
                        <input type="text" class="wh182 zui-input background-f" v-model="hzzl.rycwbh"  readonly="readonly"/>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">病&nbsp;&nbsp;案&nbsp;号</label>
                        <input type="text" class="wh182 zui-input background-f" v-model="hzzl.bah"  readonly="readonly"/>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 ">
                    <div class="top-form">
                        <label class="top-label whiteSpace">临床诊断</label>
                        <input type="text" class="wh182 zui-input background-f" v-model="hzzl.lczd"  readonly="readonly"/>
                    </div>
                </div>
                <div class="col-xxl-2 col-x-3 col-s-3 col-l-3 col-xx-3 ">
                    <div class="top-form">
                        <label class="top-label ">在&emsp;&emsp;场<br/>相关人员</label>
                        <input type="text" class="wh182 zui-input background-f" v-model="popContent.zcry"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font-weight">不良事件情况</div>
        </div>
        <div class="tab-card-body">
            <div class="grid-box">
                <div class="col-xxl-12 col-x-12 col-s-12 col-l-12 col-xx-12 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">事件发生<br/>主要经过</label>
                       <input  class="zui-input background-f" type="text" v-model="popContent.sjfszyjg"/>
                    </div>
                </div>
                <div class="col-xxl-12 col-x-12 col-s-12 col-l-12 col-xx-12 padd-b-10">
                    <div class="top-form">
                        <label class="top-label whiteSpace">事&emsp;&emsp;件<br/>发生场所</label>
                            <input type="radio" id="radio-6"  name="fscs" value="1" class="h-radio" v-model="popContent.sjfscs">
                        <label for="radio-6" class="padd-r-20 ">急诊</label>
                        <input type="radio" id="radio-7"  name="fscs" value="2" class="h-radio" v-model="popContent.sjfscs">
                        <label for="radio-7" class="padd-r-20">门诊</label>
                        <input type="radio" id="radio-8"  name="fscs" value="3" class="h-radio" v-model="popContent.sjfscs">
                        <label for="radio-8" class="padd-r-20 ">病区</label>
                        <input type="radio" id="radio-9"  name="fscs" value="4" class="h-radio" v-model="popContent.sjfscs">
                        <label for="radio-9" class="padd-r-20 ">医技部门</label>
                        <input type="radio" id="radio-10"  name="fscs" value="5" class="h-radio" v-model="popContent.sjfscs">
                        <label for="radio-10" class="padd-r-10">其它</label>
                        <input  class="zui-input background-f wh122" type="text" v-model="popContent.sjfscsqtxx"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font-weight">不良事件类别</div>
        </div>
        <div class="tab-card-body flex-container flex-align-c">
            <div v-if="popContent.sjlx == '1'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">信息传递与接受</p>
                <input type="radio" id="radio-11" name="blsjlb"  value="1" class="h-radio">
                <label for="radio-11" class="padd-r-15 margin-b-10">正确信息，传递与接受错误</label>
                <input type="radio" id="radio-12" name="blsjlb" value="2" class="h-radio">
                <label for="radio-12" class="padd-r-15 margin-b-10">正确信息，信息传递与接受延迟</label>
                <input type="radio" id="radio-13" name="blsjlb" value="3" class="h-radio">
                <label for="radio-13" class="padd-r-15 margin-b-10">正确信息，信息传递与接受不准确</label>
                <input type="radio" id="radio-14" name="blsjlb" value="4" class="h-radio">
                <label for="radio-14" class="padd-r-15 margin-b-10">错误信息/或传递错误</label><br/>
                <input type="radio" id="radio-15" name="blsjlb" value="5" class="h-radio">
                <label for="radio-15" class="padd-r-15">信息传递与接受其它错误形式</label>
            </div>
            <div v-if="popContent.sjlx == '2'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">治疗</p>
                <input type="radio" id="radio-110" name="zl" value="1" class="h-radio">
                <label for="radio-110" class="padd-r-15 margin-b-10">患者选择错误</label>
                <input type="radio" id="radio-120" name="zl" value="2" class="h-radio">
                <label for="radio-120" class="padd-r-15 margin-b-10">部位选择错误 </label>
                <input type="radio" id="radio-130" name="zl" value="3" class="h-radio">
                <label for="radio-130" class="padd-r-15 margin-b-10">器材选择错误 </label>
                <input type="radio" id="radio-140" name="zl" value="4" class="h-radio">
                <label for="radio-140" class="padd-r-15 margin-b-10">其它选择错误 </label><br/>
            </div>
            <div v-if="popContent.sjlx == '3'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">方法/技术</p>
                <input type="radio" id="radio-112" name="ffjs" value="1" class="h-radio">
                <label for="radio-112" class="padd-r-15 margin-b-10">遗忘，未治疗</label>
                <input type="radio" id="radio-1202" name="ffjs" value="2" class="h-radio">
                <label for="radio-1202" class="padd-r-15 margin-b-10">中止</label>
                <input type="radio" id="radio-1302" name="ffjs" value="3" class="h-radio">
                <label for="radio-1302" class="padd-r-15 margin-b-10">延期 </label>
                <input type="radio" id="radio-1402" name="ffjs" value="4" class="h-radio">
                <label for="radio-1402" class="padd-r-15 margin-b-10">时间错误  </label>
                <input type="radio" id="radio-1403" name="ffjs" value="5" class="h-radio">
                <label for="radio-1403" class="padd-r-15 margin-b-10">程顺序错误</label>
                <input type="radio" id="radio-1404" name="ffjs" value="6" class="h-radio">
                <label for="radio-1404" class="padd-r-15 margin-b-10">不必要的治疗</label>
                <input type="radio" id="radio-1405" name="ffjs" value="7" class="h-radio">
                <label for="radio-1405" class="padd-r-15 margin-b-10">灭菌/消毒错误</label>
                <input type="radio" id="radio-1406" name="ffjs" value="8" class="h-radio">
                <label for="radio-1406" class="padd-r-15 margin-b-10">体位错误</label>
                <input type="radio" id="radio-1407" name="ffjs" value="9" class="h-radio">
                <label for="radio-1407" class="padd-r-15 margin-b-10">其它诊疗错误</label>
                <input type="radio" id="radio-1408" name="ffjs" value="10" class="h-radio">
                <label for="radio-1408" class="padd-r-15 margin-b-10">误吸</label>
                <input type="radio" id="radio-1409" name="ffjs" value="11" class="h-radio">
                <label for="radio-1409" class="padd-r-15 margin-b-10">误咽</label>
                <input type="radio" id="radio-14010" name="ffjs" value="12" class="h-radio">
                <label for="radio-14010" class="padd-r-15 margin-b-10">其它</label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div v-if="popContent.sjlx == '4'">
                <p class="ysb-black font14 padd-l-15 ">药品调剂分发</p>
                <div class="padd-l-nb-25">
                    <div class="grid-box flex-container flex-wrap-w">
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox021" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox021" class="padd-l-nb-25">多给药</label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox034" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox034" class="padd-l-nb-25">少给药 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox044" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox044" class=" padd-l-nb-25">重复发药 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox054" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox054" class="padd-l-nb-25 ">配伍禁忌 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox064" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox064" class="padd-l-nb-25 ">发药时机错误 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox074" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox074" class=" padd-l-nb-25">拿错处方 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox084" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox084" class="padd-l-nb-25 ">其它 </label>
                            <input  class="zui-input background-f wh122" type="text"/>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox094" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox094" class=" padd-l-nb-25">用药速度过快 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91014" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91014" class="padd-l-nb-25 ">用药速度过慢 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91024" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91024" class="padd-l-nb-25 ">其它用药速度错误 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91034" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91034" class=" padd-l-nb-25">用法/途经错误</label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91044" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91044" class=" padd-l-nb-25">取药对象错误 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox9584" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox9584" class="padd-l-nb-25 ">用药剂量错误 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox9384" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox9384" class=" padd-l-nb-25">未核对药品 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93845" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93845" class=" padd-l-nb-25">其它  </label>
                            <input  class="zui-input background-f wh122" type="text"/>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938459" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938459" class=" padd-l-nb-25">调剂管理  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93889" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93889" class=" padd-l-nb-25">重量错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93898" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93898" class=" padd-l-nb-25">规格错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93867" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93867" class=" padd-l-nb-25">包装错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93833" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93833" class=" padd-l-nb-25">数量错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93844" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93844" class=" padd-l-nb-25">违规调剂 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93855" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93855" class=" padd-l-nb-25">其它  </label>
                            <input  class="zui-input background-f wh122" type="text"/>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93811" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93811" class=" padd-l-nb-25">与说明书不一致  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93812" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93812" class=" padd-l-nb-25">发药时错误告知患者  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93822" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93822" class=" padd-l-nb-25">过期药品  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93823" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93823" class=" padd-l-nb-25">血液制剂ABO不符合 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93834" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93834" class=" padd-l-nb-25">其它错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938457" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938457" class=" padd-l-nb-25">异物混入  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93800" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93800" class=" padd-l-nb-25">细菌污染  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93809" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93809" class=" padd-l-nb-25">混合错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93808" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93808" class=" padd-l-nb-25">包装破损  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93807" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93807" class=" padd-l-nb-25">其它  </label>
                            <input  class="zui-input background-f wh122" type="text"/>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93850" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93850" class=" padd-l-nb-25">装错药袋  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938590" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938590" class=" padd-l-nb-25">药袋破损  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938333" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938333" class=" padd-l-nb-25">药袋说明错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938444" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938444" class=" padd-l-nb-25">药袋无说明  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938555" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938555" class=" padd-l-nb-25">药品丢失  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938666" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938666" class=" padd-l-nb-25">其它调剂错误 </label>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="popContent.sjlx == '5'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">输血</p>
                <input type="radio" id="radio-1120" name="sx" value="1" class="h-radio">
                <label for="radio-1120" class="padd-r-15 margin-b-10">输血前检验项目未执行 </label>
                <input type="radio" id="radio-120201" name="sx" value="2" class="h-radio">
                <label for="radio-120201" class="padd-r-15 margin-b-10">未输入 </label>
                <input type="radio" id="radio-13021101" name="sx" value="3" class="h-radio">
                <label for="radio-13021101" class="padd-r-15 margin-b-10">血型错误  </label>
                <input type="radio" id="radio-1402111" name="sx" value="4" class="h-radio">
                <label for="radio-1402111" class="padd-r-15 margin-b-10">配型错误  </label>
                <input type="radio" id="radio-1403112" name="sx" value="5" class="h-radio">
                <label for="radio-1403112" class="padd-r-15 margin-b-10">输错患者</label>
                <input type="radio" id="radio-1405115" name="sx" value="6" class="h-radio">
                <label for="radio-1405115" class="padd-r-15 margin-b-10">记录错误 </label>
                <input type="radio" id="radio-14010117" name="sx" value="7" class="h-radio">
                <label for="radio-14010117" class="padd-r-15 margin-b-10">其它</label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div v-if="popContent.sjlx == '6'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">器械使用  </p>
                <input type="radio" id="radio-112077" name="qxsy" value="1" class="h-radio">
                <label for="radio-112077" class="padd-r-15 margin-b-10">设置错误 </label>
                <input type="radio" id="radio-12020177" name="qxsy" value="2" class="h-radio">
                <label for="radio-12020177" class="padd-r-15 margin-b-10">无电源  </label>
                <input type="radio" id="radio-1302110177" name="qxsy" value="3" class="h-radio">
                <label for="radio-1302110177" class="padd-r-15 margin-b-10">条件设置错误   </label>
                <input type="radio" id="radio-140211177" name="qxsy" value="4" class="h-radio">
                <label for="radio-140211177" class="padd-r-15 margin-b-10">故障   </label>
                <input type="radio" id="radio-140311277" name="qxsy" value="5" class="h-radio">
                <label for="radio-140311277" class="padd-r-15 margin-b-10">修理状态 </label>
                <input type="radio" id="radio-140411477" name="qxsy" value="6" class="h-radio">
                <label for="radio-140411477" class="padd-r-15 margin-b-10">停止运行</label>
                <input type="radio" id="radio-140511577" name="qxsy" value="7" class="h-radio">
                <label for="radio-140511577" class="padd-r-15 margin-b-10">操作失控  </label>
                <input type="radio" id="radio-14010117778" name="qxsy" value="8" class="h-radio">
                <label for="radio-14010117778" class="padd-r-15 margin-b-10">漏电/触电</label>
                <input type="radio" id="radio-1401011777" name="qxsy" value="9" class="h-radio">
                <label for="radio-1401011777" class="padd-r-15 margin-b-10">未接地 </label>
                <input type="radio" id="radio-14010117779" name="qxsy" value="10" class="h-radio">
                <label for="radio-14010117779" class="padd-r-15 margin-b-10">未定期检修  </label>
                <input type="radio" id="radio-14010117770" name="qxsy" value="11" class="h-radio">
                <label for="radio-14010117770" class="padd-r-15 margin-b-10">未行剂量检测  </label>
                <input type="radio" id="radio-14010117775" name="qxsy" value="12" class="h-radio">
                <label for="radio-14010117775" class="padd-r-15 margin-b-10">违反操作规程  </label>
                <input type="radio" id="radio-14010117774" name="qxsy" value="13" class="h-radio">
                <label for="radio-14010117774" class="padd-r-15 margin-b-10">其它 </label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div v-if="popContent.sjlx == '7'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">导管操作   </p>
                <input type="radio" id="radio-112077t" name="dgcz"  value="1" class="h-radio">
                <label for="radio-112077t" class="padd-r-15 margin-b-10">静点滴漏/渗 </label>
                <input type="radio" id="radio-12020177y" name="dgcz" value="2" class="h-radio">
                <label for="radio-12020177y" class="padd-r-15 margin-b-10">导管脱落   </label>
                <input type="radio" id="radio-1302110177u" name="dgcz" value="3" class="h-radio">
                <label for="radio-1302110177u" class="padd-r-15 margin-b-10">导管断裂    </label>
                <input type="radio" id="radio-140211177i" name="dgcz" value="4" class="h-radio">
                <label for="radio-140211177i" class="padd-r-15 margin-b-10">连接错误    </label>
                <input type="radio" id="radio-140311277k" name="dgcz" value="5" class="h-radio">
                <label for="radio-140311277k" class="padd-r-15 margin-b-10">未连接  </label>
                <input type="radio" id="radio-140411477l" name="dgcz" value="6" class="h-radio">
                <label for="radio-140411477l" class="padd-r-15 margin-b-10">错误速度</label>
                <input type="radio" id="radio-140511577g" name="dgcz" value="7" class="h-radio">
                <label for="radio-140511577g" class="padd-r-15 margin-b-10">三通方向错误   </label>
                <input type="radio" id="radio-14010117778h" name="dgcz" value="8" class="h-radio">
                <label for="radio-14010117778h" class="padd-r-15 margin-b-10">导管闭塞 </label>
                <input type="radio" id="radio-1401011777n" name="dgcz" value="9" class="h-radio">
                <label for="radio-1401011777n" class="padd-r-15 margin-b-10">导管内异物  </label>
                <input type="radio" id="radio-14010117779m" name="dgcz" value="10" class="h-radio">
                <label for="radio-14010117779m" class="padd-r-15 margin-b-10">混入空气   </label>
                <input type="radio" id="radio-14010117774f" name="dgcz" value="11" class="h-radio">
                <label for="radio-14010117774f" class="padd-r-15 margin-b-10">其它 </label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div  v-if="popContent.sjlx == '8'">
                <p class="ysb-black font14 padd-l-15 ">医学技术检查</p>
                <div class="padd-l-nb-25">
                    <div class="grid-box flex-container flex-wrap-w">
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox021q" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox021q" class="padd-l-nb-25">检查人员无资质 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox034w" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox034w" class="padd-l-nb-25">患者识别错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox044e" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox044e" class=" padd-l-nb-25">方法/技巧错误 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox054e" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox054e" class="padd-l-nb-25 ">技术不熟练 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox064e" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox064e" class="padd-l-nb-25 ">有禁忌症  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox074r" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox074r" class=" padd-l-nb-25">无质量控制（室间质评、室内质控) </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox084r" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox084r" class="padd-l-nb-25 ">使用“计量”检测不合格设备 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox094t" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox094t" class=" padd-l-nb-25">标本采集时机错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91014i" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91014i" class="padd-l-nb-25 ">标本采集储存错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91024o" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91024o" class="padd-l-nb-25 ">采集标本破损  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91034o" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91034o" class=" padd-l-nb-25">采集标本丢失 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91044o" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91044o" class=" padd-l-nb-25">采集标本不合格  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox9584y" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox9584y" class="padd-l-nb-25 ">未抗凝  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox9384y" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox9384y" class=" padd-l-nb-25">标识错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93845y" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93845y" class=" padd-l-nb-25">部位识别错误   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938459y" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938459y" class=" padd-l-nb-25">非医师检查申请单所要求的检查内容   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93889t" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93889t" class=" padd-l-nb-25">试剂管理   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93898r" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93898r" class=" padd-l-nb-25">分析仪器/准备  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93867r" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93867r" class=" padd-l-nb-25">检查仪表/准备  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93833e" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93833e" class=" padd-l-nb-25">图像编码错误   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93844e" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93844e" class=" padd-l-nb-25">信息记录错误  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93855e" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93855e" class=" padd-l-nb-25">记录信息丢失   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93811w" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93811w" class=" padd-l-nb-25">计算机系统故障  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93812w" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93812w" class=" padd-l-nb-25">结果传递错误   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93822q" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93822q" class=" padd-l-nb-25">结果报告丢失   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93823a" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93823a" class=" padd-l-nb-25">结果未报告  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93834d" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93834d" class=" padd-l-nb-25">造影剂过敏反应  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938457f" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938457f" class=" padd-l-nb-25">患者病情意外变化   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93800g" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93800g" class=" padd-l-nb-25">无应急抢救药械   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93809c" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93809c" class=" padd-l-nb-25">需有医师随同监护而执行  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93808c" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93808c" class=" padd-l-nb-25">未执行“危急值”报告制  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93807v" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93807v" class=" padd-l-nb-25">其它  </label>
                            <input  class="zui-input background-f wh122" type="text"/>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="popContent.sjlx == '9'">
                <p class="ysb-black font14 padd-l-15 ">基础护理  </p>
                <div class="padd-l-nb-25">
                    <div class="grid-box flex-container flex-wrap-w">
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox021qt" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox021qt" class="padd-l-nb-25">摔倒  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox034wy" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox034wy" class="padd-l-nb-25">坠床   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox044ey" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox044ey" class=" padd-l-nb-25">误吸  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox054ey" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox054ey" class="padd-l-nb-25 ">误咽  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox064ey" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox064ey" class="padd-l-nb-25 ">误食   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox074ru" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox074ru" class=" padd-l-nb-25">其它  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox084ru" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox084ru" class="padd-l-nb-25 ">禁食/禁水医嘱不执行 </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox094tu" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox094tu" class=" padd-l-nb-25">行动限制医嘱不执行   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91014iu" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91014iu" class="padd-l-nb-25 ">其它控制医嘱不执行   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91024oi" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91024oi" class="padd-l-nb-25 ">约束固定无医嘱   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91034ow" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91034ow" class=" padd-l-nb-25">约束固定未告知  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox91044oe" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox91044oe" class=" padd-l-nb-25">约束固定后未做到观察病情   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox9584ye" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox9584ye" class="padd-l-nb-25 ">其它   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox9384yw" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox9384yw" class=" padd-l-nb-25">错误获取  </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93845yq" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93845yq" class=" padd-l-nb-25">延迟    </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox938459yq" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox938459yq" class=" padd-l-nb-25">遗忘    </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93889tw" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93889tw" class=" padd-l-nb-25">行动在先，未通告   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93898re" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93898re" class=" padd-l-nb-25">其它错误行动   </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93867rr" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93867rr" class=" padd-l-nb-25">检查仪表/准备  </label>
                        </div>
                        <div class="wc8 padd-b-10"></div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93855er" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93855er" class=" padd-l-nb-25">患者自行留宿院外    </label>
                        </div>
                        <div class="wc8 padd-b-10">
                            <input id="checkbBox93811wt" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93811wt" class=" padd-l-nb-25">未告知院方的其它行动  </label>
                        </div>
                        <div class=" padd-b-10">
                            <input id="checkbBox93833et" class="green" v-model="checked" type="checkbox" >
                            <label for="checkbBox93833et" class=" padd-l-nb-25">患者自带药品 Ø 忘服 Ø 忘注射 Ø 自带药品用完 Ø 带药未告知医师 Ø 其它   </label>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="popContent.sjlx == '10'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">营养与饮食 </p>
                <input type="radio" id="radio-112077tw"  value="zlj" class="h-radio">
                <label for="radio-112077tw" class="padd-r-15 margin-b-10">饮食类别错误  </label>
                <input type="radio" id="radio-12020177yw"  value="zlj" class="h-radio">
                <label for="radio-12020177yw" class="padd-r-15 margin-b-10">未按医嘱用餐    </label>
                <input type="radio" id="radio-1302110177uw"  value="zlj" class="h-radio">
                <label for="radio-1302110177uw" class="padd-r-15 margin-b-10">数量错误     </label>
                <input type="radio" id="radio-140211177iw"  value="zlj" class="h-radio">
                <label for="radio-140211177iw" class="padd-r-15 margin-b-10">未按医嘱禁食     </label>
                <input type="radio" id="radio-14031127w7k"  value="zlj" class="h-radio">
                <label for="radio-14031127w7k" class="padd-r-15 margin-b-10">未按医嘱禁水   </label>
                <input type="radio" id="radio-140411w477l"  value="zlj" class="h-radio">
                <label for="radio-140411w477l" class="padd-r-15 margin-b-10">未按治疗饮食医嘱执行 </label>
                <input type="radio" id="radio-140511w577g"  value="zlj" class="h-radio">
                <label for="radio-140511w577g" class="padd-r-15 margin-b-10">肠道内灌注给食错误    </label>
                <input type="radio" id="radio-14010w117774f"  value="zlj" class="h-radio">
                <label for="radio-14010w117774f" class="padd-r-15 margin-b-10">其它 </label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div v-if="popContent.sjlx == '11'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">物品运送  </p>
                <input type="radio" id="radio-1120d77tw"  value="zlj" class="h-radio">
                <label for="radio-1120d77tw" class="padd-r-15 margin-b-10">延迟   </label>
                <input type="radio" id="radio-120d20177yw"  value="zlj" class="h-radio">
                <label for="radio-120d20177yw" class="padd-r-15 margin-b-10">遗忘     </label>
                <input type="radio" id="radio-1302d110177uw"  value="zlj" class="h-radio">
                <label for="radio-1302d110177uw" class="padd-r-15 margin-b-10">丢失      </label>
                <input type="radio" id="radio-14021d1177iw"  value="zlj" class="h-radio">
                <label for="radio-14021d1177iw" class="padd-r-15 margin-b-10">破损      </label>
                <input type="radio" id="radio-14031d127w7k"  value="zlj" class="h-radio">
                <label for="radio-14031d127w7k" class="padd-r-15 margin-b-10">未按急需急送    </label>
                <input type="radio" id="radio-14041d1w477l"  value="zlj" class="h-radio">
                <label for="radio-14041d1w477l" class="padd-r-15 margin-b-10">品种规格错误  </label>
                <input type="radio" id="radio-14010dw117774f"  value="zlj" class="h-radio">
                <label for="radio-14010dw117774f" class="padd-r-15 margin-b-10">其它 </label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div v-if="popContent.sjlx == '12'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">放射安全  </p>
                <input type="radio" id="radio-1120dv77tw"  value="zlj" class="h-radio">
                <label for="radio-1120dv77tw" class="padd-r-15 margin-b-10">放射线泄漏    </label>
                <input type="radio" id="radio-1404114" name="sx" value="6" class="h-radio">
                <label for="radio-1404114" class="padd-r-15 margin-b-10">放射线照射错误</label>
                <input type="radio" id="radio-120vd20177yw"  value="zlj" class="h-radio">
                <label for="radio-120vd20177yw" class="padd-r-15 margin-b-10">放射性物品丢失      </label>
                <input type="radio" id="radio-1302dv110177uw"  value="zlj" class="h-radio">
                <label for="radio-1302dv110177uw" class="padd-r-15 margin-b-10">未行防护       </label>
                <input type="radio" id="radio-14021vd1177iw"  value="zlj" class="h-radio">
                <label for="radio-14021vd1177iw" class="padd-r-15 margin-b-10">误照射       </label>
                <input type="radio" id="radio-14010dvw117774f"  value="zlj" class="h-radio">
                <label for="radio-14010dvw117774f" class="padd-r-15 margin-b-10">其它 </label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div v-if="popContent.sjlx == '13'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">诊疗记录  </p>
                <input type="radio" id="radio-1120bdv77tw"  value="zlj" class="h-radio">
                <label for="radio-1120bdv77tw" class="padd-r-15 margin-b-10">诊疗记录丢失     </label>
                <input type="radio" id="radio-12b0vd20177yw"  value="zlj" class="h-radio">
                <label for="radio-12b0vd20177yw" class="padd-r-15 margin-b-10">应记录而未记录       </label>
                <input type="radio" id="radio-1302bdv110177uw"  value="zlj" class="h-radio">
                <label for="radio-1302bdv110177uw" class="padd-r-15 margin-b-10">记录内容失实        </label>
                <input type="radio" id="radio-140b21vd1177iw"  value="zlj" class="h-radio">
                <label for="radio-140b21vd1177iw" class="padd-r-15 margin-b-10">涂改记录内容        </label>
                <input type="radio" id="radio-14010dbvw117774f"  value="zlj" class="h-radio">
                <label for="radio-14010dbvw117774f" class="padd-r-15 margin-b-10">无资质人员书写记录  </label>
                <input type="radio" id="radio-14010bdvw117774f"  value="zlj" class="h-radio">
                <label for="radio-14010bdvw117774f" class="padd-r-15 margin-b-10">其它 </label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div v-if="popContent.sjlx == '14'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">知情同意   </p>
                <input type="radio" id="radio-1120bdvz77tw"  value="zlj" class="h-radio">
                <label for="radio-1120bdvz77tw" class="padd-r-15 margin-b-10">知情告知不准确      </label>
                <input type="radio" id="radio-12b0zvd20177yw"  value="zlj" class="h-radio">
                <label for="radio-12b0zvd20177yw" class="padd-r-15 margin-b-10">未行知情告知        </label>
                <input type="radio" id="radio-1302zbdv110177uw"  value="zlj" class="h-radio">
                <label for="radio-1302zbdv110177uw" class="padd-r-15 margin-b-10">未告知先签字同意         </label>
                <input type="radio" id="radio-140bz21vd1177iw"  value="zlj" class="h-radio">
                <label for="radio-140bz21vd1177iw" class="padd-r-15 margin-b-10">告知与书面记录不一致        </label>
                <input type="radio" id="radio-14010zdbvw117774f"  value="zlj" class="h-radio">
                <label for="radio-14010zdbvw117774f" class="padd-r-15 margin-b-10">未行签字同意   </label>
                <input type="radio" id="radio-14010zbdvw117774f"  value="zlj" class="h-radio">
                <label for="radio-14010zbdvw117774f" class="padd-r-15 margin-b-10">其它 </label>
                <input  class="zui-input background-f wh122" type="text"/>
            </div>
            <div v-if="popContent.sjlx == '15'">
                <p class="ysb-black font14 padd-l-15 padd-b-10">设备设施     </p>
                <input type="radio" id="radio-1120bdvz77tnw" name="sbss" value="1" class="h-radio">
                <label for="radio-1120bdvz77tnw" class="padd-r-15 margin-b-10">停止运行       </label>
                <input type="radio" id="radio-12b0zvdn20177yw" name="sbss" value="2" class="h-radio">
                <label for="radio-12b0zvdn20177yw" class="padd-r-15 margin-b-10">故障         </label>
                <input type="radio" id="radio-1302zbdnv110177uw" name="sbss" value="3" class="h-radio">
                <label for="radio-1302zbdnv110177uw" class="padd-r-15 margin-b-10">损坏          </label>
                <input type="radio" id="radio-140bz21vnd1177iw" name="sbss" value="4" class="h-radio">
                <label for="radio-140bz21vnd1177iw" class="padd-r-15 margin-b-10">违规操作         </label>
                <input type="radio" id="radio-14010zbdnvw117774f" name="sbss" value="5" class="h-radio">
                <label for="radio-14010zbdnvw117774f" class="padd-r-15 margin-b-10">其它 </label>
                <input  class="zui-input background-f wh122" type="text" style="margin-top: 18px;"/>
            </div>
        </div>
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font-weight">事件发生对病人或家属的影响</div>
        </div>
        <div class="tab-card-body">
            <input type="radio" id="radio-16" name="sjyx" value="1" class="h-radio" v-model="popContent.sjyx">
            <label for="radio-16" class="padd-r-15">潜在不良事件</label>
            <input type="radio" id="radio-17" name="sjyx" value="2" class="h-radio" v-model="popContent.sjyx">
            <label for="radio-17" class="padd-r-15">无伤害</label>
            <input type="radio" id="radio-18" name="sjyx" value="3" class="h-radio" v-model="popContent.sjyx">
            <label for="radio-18" class="padd-r-15">轻度伤害</label>
            <input type="radio" id="radio-19" name="sjyx" value="4" class="h-radio" v-model="popContent.sjyx">
            <label for="radio-19" class="padd-r-15">中度伤害</label>
            <input type="radio" id="radio-20" name="sjyx" value="5" class="h-radio" v-model="popContent.sjyx" >
            <label for="radio-20" class="padd-r-15">重度伤害</label>
            <input type="radio" id="radio-21" name="sjyx"  value="6" class="h-radio" v-model="popContent.sjyx">
            <label for="radio-21" class="padd-r-15">极重度伤害</label>
        </div>
    </div>
    <div class="zui-table-tool flex-container flex-jus-e flex-align-c">
        <button class="tong-btn btn-parmary-d9 xmzb-db ">取消</button>
        <button class="tong-btn btn-parmary xmzb-db" @click="gjxm">确定</button>
    </div>
</div>
<script src="hzbg.js" type="text/javascript"></script>
</body>
</html>