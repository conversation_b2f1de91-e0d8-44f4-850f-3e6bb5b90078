<div id="fylbdy" class="contextInfo">
	<div class="panel">
		<div class="tong-top">
			<button class="tong-btn btn-parmary" @click="getData"><span class="fa fa-refresh padd-r-5"></span>刷新</button>
			<button class="tong-btn btn-parmary-b" @click="saveBaks"><span class="fa fa-save padd-r-5"></span>保存</button>
		</div>
	</div>
	<div class="zui-table-view ybglTable" id="utable1" z-height="full">
		<div class="zui-table-header">
			<table class="font-14 zui-table table-width50">
				<thead>
				<tr>
					<th class="cell-m">
						<input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                            </input-checkbox>
					</th>
					<th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>费用类别编码</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>费用类别名称</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>CT</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>PETCT</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>双源CT</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>X片</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>B超</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>心电图</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>MRI</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>多普勒</span></div></th>
				</tr>
				</thead>
			</table>
		</div>
		<div class="zui-table-body" @scroll="scrollTable($event)">
			<table class="zui-table table-width50">
				<tbody>
				<tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
					<td class="cell-m">
						<input-checkbox style="display: flex;justify-content: center;align-items: center;" @result="reCheckBox" :list="'jsonList'"
										:type="'some'" :which="$index"
										:val="isChecked[$index]">
						</input-checkbox>

					</td>
					<td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.lbbm"></div></td>
					<td>
						<div class="zui-table-cell cell-s" v-text="item.lbmc">
						</div>
					</td>
					<td>
						<div class="zui-table-cell cell-s">
							<select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
										  :child="istrue_tran" :index="item.ct" :val="item.ct"
										  :name="'jsonList.'+$index+'.ct'">
							</select-input>
						</div>
					</td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.petct" :val="item.petct"
									  :name="'jsonList.'+$index+'.petct'">
						</select-input>
					</div>
					</td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height" @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.syct" :val="item.syct"
									  :name="'jsonList.'+$index+'.syct'">
						</select-input>
					</div>
					</td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.xp" :val="item.xp"
									  :name="'jsonList.'+$index+'.xp'">
						</select-input>
					</div>
					</td>
					<td><div class="zui-table-cell cell-s">
						<select-input  class="fyl-height" @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.bc" :val="item.bc"
									  :name="'jsonList.'+$index+'.bc'">
						</select-input>
					</div>
					</td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height"  @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.csxdt" :val="item.csxdt"
									  :name="'jsonList.'+$index+'.csxdt'">
						</select-input>
					</div></td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height" @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.mri" :val="item.mri"
									  :name="'jsonList.'+$index+'.mri'">
						</select-input>
					</div></td>
					<td><div class="zui-table-cell cell-s">
						<select-input class="fyl-height" @change-data="resultChange" :not_empty="false"
									  :child="istrue_tran" :index="item.dpl" :val="item.dpl"
									  :name="'jsonList.'+$index+'.dpl'">
						</select-input>
					</div></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div class="zui-table-fixed table-fixed-l">
			<div class="zui-table-header">
				<table class="zui-table">
					<thead>
					<tr>
						<th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
																					  :type="'all'" :val="isCheckAll">
                            </input-checkbox></div></th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="zui-table-body" @scroll="scrollTableFixed($event)">
				<table class="zui-table">
					<tbody>
					<tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
						<td class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
																							  :type="'some'" :which="$index"
																							  :val="isChecked[$index]">
						</input-checkbox></div></td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>



<script type="text/javascript" src="fylbdy.js"></script>


