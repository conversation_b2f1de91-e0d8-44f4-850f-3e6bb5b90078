     <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>参数设置</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="cssz.css">
</head>
<style>
    .fieldlist {
        display: none !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="panel-head border-bottom   bg-fff" style="padding-top: 0">
            <div class="zui-row" style="padding-bottom: 40px;">
                <div class="col-x-12 botom-29">
                    <div class=" bg-1abc9c">
                        <button class="zui-btn bg-1abc9c" :class="{'zui-active':num==0}" @click="silde(0)">参数设置一
                        </button>
                        <button class="zui-btn bg-1abc9c" :class="{'zui-active':num==1}" @click="silde(1)">参数设置二
                        </button>
                        <button class="zui-btn bg-1abc9c" @click="silde(2)" :class="{'zui-active':num==2}">参数设置三
                        </button>
                        <button class="zui-btn bg-1abc9c" @click="silde(3)" :class="{'zui-active':num==3}">参数设置四
                        </button>
                        <button class="zui-btn bg-1abc9c" @click="silde(4)" :class="{'zui-active':num==4}">参数设置五
                        </button>
                    </div>
                </div>
                <div>
                    <div>
                        <div class="zui-form" v-show="num==0">
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">执行科室</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">检验分组</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">接口文件存放路径</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">接口图片文件路径</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-4">
                                    <label class="left-right">自动删除</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input"
                                               style="width:80px;display: inherit;margin-right: 10px;"><span>天以前借口文件</span>
                                    </div>
                                </div>
                                <div class=" col-fm-4" style="margin-left:-271px;">
                                    <label class="left-right" style="width: auto">自动删除</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input"
                                               style="width:80px;display: inherit;margin-right: 10px;"><span>天以前图片文件</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">微生物所属检验分类</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                        <!--:child="ryhzlx_tran" :index="popContent.hzlx"-->
                                        <!--:val="popContent.hzlx"-->
                                        <!--:name="'popContent.hzlx'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">阳性结果</label>
                                    <div class="zui-input-inline width-50">
                                        <select-input @change-data="resultChange" :not_empty="false"
                                        :child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"
                                        :name="'popContent.brxb'">
                                        </select-input>
                                        <!--<input class="zui-input">-->
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">阴性结果</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <span class="left-right">急诊样本和普通样本连续编号</span>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">报告单打印检验者扫描签名</label>
                                    <div class="zui-input-inline width-50">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input id="qm" type="checkbox" class="green" style="margin-top: 12px;"/>
                                                <label for="qm"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">当前设备</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">审核成功打印方式</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">登录时允许修改分组</label>
                                    <div class="zui-input-inline width-50">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="fz" class="green" style="margin-top: 12px;"/>
                                                <label for="fz"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">报告单图片保存路径</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="action-bar fixed">
                                <button class="zui-btn btn-primary onsubmit zui-88">确定</button>
                                <button class="zui-btn btn-default onreset zui-88">取消</button>
                            </div>
                        </div>
                        <div class="zui-form" v-show="num==1">
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">报告单命名方式</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                        <!--:child="ryhzlx_tran" :index="popContent.hzlx"-->
                                        <!--:val="popContent.hzlx"-->
                                        <!--:name="'popContent.hzlx'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">报告单缺省名称</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">体检中心科室</label>
                                    <div class="zui-input-inline width-50">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">体检申请医师</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                        <!--:child="ryhzlx_tran" :index="popContent.hzlx"-->
                                        <!--:val="popContent.hzlx"-->
                                        <!--:name="'popContent.hzlx'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">检验登记业务窗口</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                        <!--:child="ryhzlx_tran" :index="popContent.hzlx"-->
                                        <!--:val="popContent.hzlx"-->
                                        <!--:name="'popContent.hzlx'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">报告发放业务窗口</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                        <!--:child="ryhzlx_tran" :index="popContent.hzlx"-->
                                        <!--:val="popContent.hzlx"-->
                                        <!--:name="'popContent.hzlx'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">住院申请扣费方式</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">获取检验申请范围</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <span class="left-right">病区样本和手范围</span>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">门诊采样登自动核收</label>
                                    <div class="zui-input-inline width-50">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">报告单日期格式</label>
                                    <div class="zui-input-inline width-50">

                                        <input class="zui-input todate">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226"
                                           style="height: 22px;">生化、免疫项目按姓名和收费日期自动合并</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" class="green" id="hb"/>
                                                <label for="hb"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">报告发放成功是否同步打印报告单</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="bg" class="green"/>
                                                <label for="bg"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">使用体检接口</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="jk" class="green"/>
                                               <label for="jk"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">是否使用大屏显示报告发放信息</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="dp" class="green"/>
                                                <label for="dp"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">住院扣费判断余额</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="ye" class="green"/>
                                                <label for="ye"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">报告单打印前预览</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="yl" class="green"/>
                                                <label for="yl"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">自动生成样本号</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="yb" class="green"/>
                                                <label for="yb"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">登录成功后自动启动串口接受程序</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="cx" class="green"/>
                                                <label for="cx"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">退出关闭串口接受程序</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="ck" class="green"/>
                                                <label for="ck"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">登录成功后自动启动网口接受程序</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="wk" class="green"/>
                                                <label for="wk"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">退出关闭网口接受程序</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="gbwk" class="green"/>
                                                <label for="gbwk"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">登记时样本核收日期默认为当天</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="rq" class="green"/>
                                                <label for="rq"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">重复项目不允许扣费</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="kf" class="green"/>
                                                <label for="kf"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">登记时采样日期默认为当天</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="dj" class="green"/>
                                                <label for="dj"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">登记时允许修改患者姓名</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="us" class="green"/>
                                                <label for="us"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">审核报告时是否签名</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="sh" class="green"/>
                                                <label for="sh"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">设置审核人是否需要输入密码</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="sz" class="green"/>
                                                <label for="sz"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">保存后样本排在最后</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="bc" class="green"/>
                                                <label for="bc"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226" style="height: 22px">显示报告领取信息</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="lq" class="green"/>
                                                <label for="lq"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-7">
                                    <label class="left-right">设置审核人输入密码方式</label>
                                    <div class="zui-input-inline " style="width: 55%;margin-bottom: 20px">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="action-bar fixed">
                                <button class="zui-btn btn-primary onsubmit zui-88">确定</button>
                                <button class="zui-btn btn-default onreset zui-88">取消</button>
                            </div>
                        </div>
                        <div class="zui-form" v-show="num==2">
                            <div class="col-fm-12">
                                <div class="  col-fm-5 pa-l-20">
                                    <label class="left-right">执行检验申请显示范围</label>
                                    <div class="zui-input-inline width-59">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <!--<div class="lansexuxian">-->
                            <!--<p class="t">条码配置</p>-->
                            <!--</div>-->
                            <div class="col-fm-12  lansexuxiantxt pa-l-20">
                                <div class="t">
                                    <div class="t-div"></div>
                                    <div class="t-text">条码配置</div>
                                    <div class="t-div-color"></div>
                                </div>
                                <div class=" col-fm-5 lansexuxianfist bgcolor">
                                    <label class="left-right">条码类型</label>
                                    <div class="zui-input-inline width-56">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class=" col-fm-5 lansexuxian bgcolor">
                                    <label class="left-right">条码纸张大小取值方式</label>
                                    <div class="zui-input-inline width-56">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>

                            <div class="col-fm-12 pa-l-20">
                                <div class="col-fm-5 lansexuxian bgcolor">
                                    <div class=" col-fm-6">
                                        <label class="left-right">条码纸张宽度</label>
                                        <div class="zui-input-inline width-botton" style="width: 36.5%;">
                                            <input class="zui-input"
                                                   style="display: inherit;margin-right: 10px;padding-right: 30px;"><span
                                                class="cm">cm</span>
                                        </div>
                                    </div>
                                    <div class=" col-fm-6">
                                        <label class="left-right" style="width: auto;min-width: auto">条码纸张高度</label>
                                        <div class="zui-input-inline width-botton" style="width: 47.5%;">
                                            <input class="zui-input"
                                                   style="display: inherit;margin-right: 10px;padding-right: 30px"><span
                                                class="cm">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class="lansexuxianlast col-fm-5 bgcolor">
                                    <div class=" col-fm-6">
                                        <label class="left-right">条码宽度</label>
                                        <div class="zui-input-inline width-botton" style="width: 36.5%;">
                                            <input class="zui-input"
                                                   style="display: inherit;margin-right: 10px;padding-right: 30px"><span
                                                class="cm">cm</span>
                                        </div>
                                    </div>
                                    <div class=" col-fm-6">
                                        <label class="left-right" style="width: auto;min-width: 84px">条码高度</label>
                                        <div class="zui-input-inline width-botton" style="width: 47.5%;">
                                            <input class="zui-input"
                                                   style="display: inherit;margin-right: 10px;padding-right: 30px"><span
                                                class="cm">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226"
                                           style="height: 22px;width: auto;min-width: auto;margin-right: 30px;">新增登记默认上次检验项目</label>
                                    <div class="zui-input-inline width-50"
                                         style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="jy" class="green"/>
                                                <label for="jy"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226"
                                           style="height: 22px;width: auto;min-width: auto">提取结果后是否自动保存</label>
                                    <div class="zui-input-inline width-50"
                                         style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="jg" class="green"/>
                                                <label for="jg"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226"
                                           style="height: 22px;width: auto;min-width: auto;margin-right: 30px;">报告单打印检验设备名称&emsp;</label>
                                    <div class="zui-input-inline width-50"
                                         style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="bgd" class="green"/>
                                                <label for="bgd"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                                <div class=" col-fm-3">
                                    <label class="left-right width-226"
                                           style="height: 22px;width: auto;min-width: auto">报告单打印审核人签章&emsp;</label>
                                    <div class="zui-input-inline width-50"
                                         style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" id="shr" class="green"/>
                                                <label for="shr"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 ">
                                <div class="  col-fm-5 pa-l-20">
                                    <label class="left-right">样本号生成方式</label>
                                    <div class="zui-input-inline width-59">
                                        <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                        <!--:child="ryhzlx_tran" :index="popContent.hzlx"-->
                                        <!--:val="popContent.hzlx"-->
                                        <!--:name="'popContent.hzlx'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class="  col-fm-5 pa-l-20">
                                    <label class="left-right">报告打印时间取数方式</label>
                                    <div class="zui-input-inline width-59">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 lansexuxiantxt pa-l-20">
                                <div class="t">
                                    <div class="t-div"></div>
                                    <div class="t-text">程序更新设置</div>
                                    <div class="t-div-color"></div>
                                </div>
                                <div class=" col-fm-5 lansexuxianfist bgcolor">
                                    <label class="left-right">ftp服务器</label>
                                    <div class="zui-input-inline width-59">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class=" col-fm-5 lansexuxian bgcolor">
                                    <span class="left-right">用户名</span>
                                    <div class="zui-input-inline width-59">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class=" col-fm-5 lansexuxian bgcolor">
                                    <label class="left-right">用户密码</label>
                                    <div class="zui-input-inline width-59">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class=" col-fm-5 lansexuxian bgcolor">
                                    <label class="left-right">链接端口</label>
                                    <div class="zui-input-inline width-59">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class=" col-fm-5 lansexuxianlast bgcolor">
                                    <label class="left-right">ftp名称</label>
                                    <div class="zui-input-inline width-59">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">ftp服务器</label>
                                    <div class="zui-input-inline width-59">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">用户名</label>
                                    <div class="zui-input-inline width-59">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">用户密码</label>
                                    <div class="zui-input-inline width-59">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="action-bar fixed">
                                <button class="zui-btn btn-primary onsubmit zui-88">确定</button>
                                <button class="zui-btn btn-default onreset zui-88">取消</button>
                            </div>
                        </div>
                        <div class="zui-form" v-show="num==3">
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">微生物接口方式</label>
                                    <div class="zui-input-inline width-59">
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12 pa-l-20">
                                <div class=" col-fm-3">
                                    <label class="left-right width-226"
                                           style="height: 22px;width: auto;min-width: auto">报告审核时判断结果是否为空</label>
                                    <div class="zui-input-inline width-50" style="width: max-content !important;">
                                        <td>
                                            <div class="zui-table-cell">
                                                <input type="checkbox" class="green" id="pd"/>
                                                <label for="pd"></label>
                                            </div>
                                        </td>
                                    </div>
                                </div>

                            </div>

                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">条码打印机</label>
                                    <div class="zui-input-inline width-59">
                                        <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                        <!--:child="ryhzlx_tran" :index="popContent.hzlx"-->
                                        <!--:val="popContent.hzlx"-->
                                        <!--:name="'popContent.hzlx'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5">
                                    <label class="left-right">通用打印机</label>
                                    <div class="zui-input-inline width-59">
                                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                                        <!--</select-input>-->
                                        <input class="zui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="action-bar fixed">
                                <button class="zui-btn btn-primary onsubmit zui-88">确定</button>
                                <button class="zui-btn btn-default onreset zui-88">取消</button>
                            </div>
                        </div>
                        <div class="zui-form" v-show="num==4">
                                <div class="col-fm-12 lansexuxiantxt pa-l-20">
                                    <div class="t">
                                        <div class="t-div"></div>
                                        <div class="t-text">默认质控维护</div>
                                        <div class="t-div-color"></div>
                                    </div>
                                    <div class=" col-fm-5 lansexuxianfist bgcolor">
                                        <label class="left-right">默认质控编码</label>
                                        <div class="zui-input-inline width-59">
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxian bgcolor">
                                        <label class="left-right">默认质控姓名</label>
                                        <div class="zui-input-inline width-59">
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxian bgcolor">
                                        <label class="left-right">默认质控科室</label>
                                        <div class="zui-input-inline width-59">
                                            <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                            <!--:child="ryhzlx_tran" :index="popContent.hzlx"-->
                                            <!--:val="popContent.hzlx"-->
                                            <!--:name="'popContent.hzlx'">-->
                                            <!--</select-input>-->
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxian bgcolor">
                                        <label class="left-right">默认质控医师</label>
                                        <div class="zui-input-inline width-59">
                                            <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                            <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                            <!--:name="'popContent.brxb'">-->
                                            <!--</select-input>-->
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxian bgcolor">
                                        <label class="left-right">默认质控类型</label>
                                        <div class="zui-input-inline width-59">
                                            <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                            <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                            <!--:name="'popContent.brxb'">-->
                                            <!--</select-input>-->
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class="lansexuxian col-fm-5 bgcolor">
                                        <div class=" col-fm-6">
                                            <label class="left-right">样本</label>
                                            <div class="zui-input-inline width-20">
                                                <input class="zui-input"
                                                       style="display: inherit;margin-right: 10px;padding-right: 30px;"><span
                                                    class="cm">cm</span>
                                            </div>
                                        </div>
                                        <div class=" col-fm-6">
                                            <label class="left-right" style="width: auto;min-width: auto">样本编号</label>
                                            <div class="zui-input-inline width-20">
                                                <input class="zui-input"
                                                       style="display: inherit;margin-right: 10px;padding-right: 30px"><span
                                                    class="cm">cm</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxian bgcolor">
                                        <label class="left-right">默认质控分组</label>
                                        <div class="zui-input-inline width-59">
                                            <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                            <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                            <!--:name="'popContent.brxb'">-->
                                            <!--</select-input>-->
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxian bgcolor">
                                        <label class="left-right">默认质控项目</label>
                                        <div class="zui-input-inline width-59">
                                            <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                            <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                            <!--:name="'popContent.brxb'">-->
                                            <!--</select-input>-->
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxian bgcolor">
                                        <label class="left-right">默认质控设备</label>
                                        <div class="zui-input-inline width-59">
                                            <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                            <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                            <!--:name="'popContent.brxb'">-->
                                            <!--</select-input>-->
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxian bgcolor">
                                        <label class="left-right">默认质控备注</label>
                                        <div class="zui-input-inline width-59">
                                            <!--<select-input @change-data="resultChange" :not_empty="false"-->
                                            <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                            <!--:name="'popContent.brxb'">-->
                                            <!--</select-input>-->
                                            <input class="zui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-fm-12 pa-l-20">
                                    <div class=" col-fm-5 lansexuxianlast bgcolor" style="padding-bottom: 20px;">
                                        <label class="left-right">停用</label>
                                        <div class="zui-input-inline width-59">
                                            <div class="switch">
                                                <input type="checkbox"/>
                                                <label></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <div class="col-fm-12">
                                <div class=" col-fm-5 text-right" style="display: flex;justify-content: flex-end;margin-left: 20px;">
                                    <button class="btn-primary zui-88 zui-btn">保存</button>
                                    <button class="zui-88 btn-f2a654 zui-btn">新增</button>
                                </div>
                            </div>
                            <div class="col-fm-12" style="padding: 0 20px 40px;">
                                <ul class="dwbm">
                                    <li class="line bg-edf2f1">
                                        <span class="text">id</span>
                                        <span class="text">默认质控姓名</span>
                                        <span class="text">默认质控科室</span>
                                        <span class="text">默认质控医师</span>
                                        <span class="text">默认质控类型</span>
                                        <span class="text">默认质控类型</span>
                                        <span class="text">默认质控样本</span>
                                        <span class="text">默认质控样本编号</span>
                                        <span class="text">状态</span>
                                        <span class="text">操作</span>
                                    </li>
                                    <li class="line" v-for="list in objList">
                                        <span class="text" v-text="list.id"></span>
                                        <span class="text" v-text="list.name"></span>
                                        <span class="text" v-text="list.tpye"></span>
                                        <span class="text" v-text="list.aduser"></span>
                                        <span class="text" v-text="list.adtype"></span>
                                        <span class="text" v-text="list.adtypeO"></span>
                                        <span class="text" v-text="list.adyb"></span>
                                        <span class="text" v-text="list.adnum"></span>
                                        <span class="text">
                                            <div class="switch">
                                                <input type="checkbox"/>
                                                <label></label>
                                            </div>
                                        </span>
                                        <span class="text icon-sc"></span>
                                    </li>
                                </ul>
                            </div>
                            <div class="action-bar fixed">
                                <button class="zui-btn btn-primary onsubmit zui-88">确定</button>
                                <button class="zui-btn btn-default onreset zui-88">取消</button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<script src="cssz.js"></script>
</body>
</html>
