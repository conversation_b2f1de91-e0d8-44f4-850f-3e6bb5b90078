<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>门特管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="bzrz.css">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper printHide" id="loadingPage">
    <div id="mtgl_bzrz" >
        <div class="panel">
            <div class="tong-top flex-container flex-align-c">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addDj">新增</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
            </div>
        </div>

        <div class="tong-search">
            <div class="zui-form">
                
                <div class="zui-inline padding-left60">
                    <label class="zui-form-label ">认证时间</label>
                    <div class="zui-input-inline flex-container flex-align-c">
                        <i class="icon-position icon-rl"></i>
                        <input autocomplete="off" class="zui-input todate wh180 text-indent20" placeholder="开始时间" v-model="param.beginTime" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input autocomplete="off" class="zui-input todate wh180 " placeholder="结束时间" v-model="param.endTime" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline padding-left40">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" autocomplete="off" placeholder="请输入姓名/身份证号/门诊号" type="text" v-model="param.parm" id="jsvalue" @keydown.enter="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>


        <div class="zui-table-view" id="brRyList">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                        </th>
                        <th class="cell-xl">
                            <div class="zui-table-cell cell-xl"><span>身份证号</span></div>
                        </th>
                        <th class="cell-xl">
                            <div class="zui-table-cell cell-xl"><span>个人编码</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>认定病种</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>认定状态</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>认定时间</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>开始时间</span></div>
                        </th>
						<th class="cell-l">
						    <div class="zui-table-cell cell-l"><span>结束时间</span></div>
						</th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作员</span></div>
                        </th>
						    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
						
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table v-if="jsonList.length" class="zui-table table-width50">
                    <tbody>
                    <tr  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                         @mouseenter="switchIndex('hoverIndex',true,$index)"
                         @mouseleave="switchIndex()" v-for="(item, $index) in jsonList"
                         :tabindex="$index"
                         @click="switchIndex('activeIndex',true,$index)"
                         class="tableTr2"
                         >
                        <td class="cell-m"><div class="zui-table-cell cell-m" ><!--序号--> {{$index+1}}</div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="item.ryxm"><!--姓名--></div></td>
                        <td class="cell-xl"><div class="zui-table-cell cell-xl" v-text="item.zjhm"><!--身份证号--></div></td>
                        <td class="cell-xl"><div class="zui-table-cell cell-xl" v-text="item.psnNo"><!--个人编号--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="item.opspDiseName"><!--认定病种--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="mtzt_tran[item.zfbz]"><!--状态--></div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="fDate(item.hospIdeDate,'datetime')"><!--认定时间--></div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="fDate(item.begindate,'date')"><!--开始时间--></div></td>
						<td class="cell-l"><div class="zui-table-cell cell-l" v-text="fDate(item.enddate,'date')"><!--结束时间--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="item.diagDrName"></div></td>
						<td class="cell-l"><!--操作-->
						    <div class="zui-table-cell cell-l flex-container flex-align-c padd-t-5">
						        <span class="width30 icon-js title" v-if="item.zfbz == '0'" data-gettitle="作废"
						              @click="invalidData($index)"></span>
						    </div>
						</td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="!jsonList.length" class="flex noData  text-center zan-border">暂无数据...</p>
            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            class="tableTr2"
                            >
                            <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                            <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                        
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            class="tableTr2"
                            >
                            <td class="cell-l"><!--操作-->
                                <div class="zui-table-cell cell-l flex-container flex-align-c padd-t-5">
                                    <span class="width30 icon-js title" v-if="item.zfbz == '0'" data-gettitle="作废"
                                          @click="invalidData($index)"></span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

        <div class="side-form pop-80 add-or-edit-content flex-container flex-dir-c" id="ckyzContent" :class="{ 'ng-hide': !isShow }" v-cloak>
            <div class="fyxm-side-top flex-between">
                <span class="fr closex ti-close" style="float: left;" @click="cancel"></span>
                <span>&emsp;病种认证</span>
                <span class="fr closex ti-close" @click="cancel"></span>
            </div>


            <div class="ksys-side flex-container padd-b-5 flex-dir-c flex-one">

                <div class="panel">
                    <div class="tong-top flex-container flex-align-c">
                        <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="refresh()">刷新</button>
                        <!--<button class="tong-btn btn-parmary-b icon-ff paddig-left" @click="loadIdCard()"><i class="icon-width icon-dsfz"></i>读身份证</button>
                        <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click="loadYkt()"><i class="icon-width icon-dylk"></i>读医疗卡</button>-->
                        <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click="loadYbk()"><i class="icon-width icon-dybk"></i>读医保卡</button>

                    </div>
                </div>

                <div class="tab-card margin-top-15">
                    <div class="tab-card-header">
                        <div class="tab-card-header-title font14">基本信息</div>
                    </div>
<!--                    检索，门诊号-->
                    <div class="tab-card-body  padd-t-10 flex-container flex-align-c flex-wrap-w">
                        
<!--                        姓名，性别，年龄，身份证号-->
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5 ft-14 whiteSpace">姓&emsp;&emsp;名</span>
                            <div class="position">
                                <input autocomplete="off" class="zui-input wh80"  @mousewheel.prevent @keydown.up.prevent
                                       @keydown.down.prevent  v-model="gzyhybContent.aac003" disabled
                                       data-notEmpty="true"
                                       @keydown="nextFocus($event)"/>
                            </div>
                        </div>
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5 ft-14 whiteSpace ">性&emsp;&emsp;别</span>
                            <select-input @change-data="resultChange" id="xb" disable
                                          :child="brxb_tran" :index="gzyhybContent.aac004" :val="gzyhybContent.aac004"
                                          :name="'gzyhybContent.aac004'" :not_empty="true">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5 ft-14 whiteSpace">身份证号</span>
                            <div class="position">
                                <input class="zui-input wh182" v-model="gzyhybContent.aac002" disabled
                                       @keydown="nextFocus($event)"/>
                            </div>
                        </div>
						<div class="flex-container flex-align-c padd-r-20 padd-b-10">
						    <span class="padd-r-5 ft-14 whiteSpace">电话</span>
						    <div class="position">
						        <input class="zui-input wh182" v-model="gzyhybContent.tel" 
						               @keydown="nextFocus($event)"/>
						    </div>
						</div>
						<div class="flex-container flex-align-c padd-r-20 padd-b-10">
						    <span class="padd-r-5 ft-14 whiteSpace">联系地址</span>
						    <div class="position">
						        <input class="zui-input wh182" v-model="gzyhybContent.addr" 
						               @keydown="nextFocus($event)"/>
						    </div>
						</div>
						<div class="flex-container flex-align-c padd-r-20 padd-b-10">
						    <span class="padd-r-5 ft-14 whiteSpace">开始时间</span>
						    <div class="position">
						        <input autocomplete="off" class="zui-input todate wh180 text-indent20" placeholder="开始时间" v-model="gzyhybContent.yae170" id="beginrq"/>
						    </div>
						</div>
						<div class="flex-container flex-align-c padd-r-20 padd-b-10">
						    <span class="padd-r-5 ft-14 whiteSpace">结束时间</span>
						    <div class="position">
						        <input autocomplete="off" class="zui-input todate wh180 " placeholder="结束时间" v-model="gzyhybContent.yae171" id="endrq" />
						    </div>
						</div>
                        
                        
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5 ft-14 whiteSpace" style="color: red;font-weight: bold">疾病诊断</span>
                            
								   <input class="zui-input height-input" v-model="gzyhybContent.opspDiseName" data-notEmpty="true"
								          
								          @keydown="changeDown1($event,'xy','jbContent','jbsearchCon','opspDiseCode','opspDiseCode','opspDiseName','selSearchs2')"
								          @input="change1(false,'xy2',$event.target.value,'selSearchs2','opspDiseName','opspDiseCode')"
								          >
								   <jbsearch-table :message="jbsearchCon" :selected="selSearchs2" :page="page" :them="jbthem"
								                   @click-one="checkedOneOut"
								                   @click-two="jbselectOne">
								   </jbsearch-table>
                        </div>
                        
                    </div>
                </div>

            <div class="ksys-btn">
                <button class="tong-btn  btn-parmary-d9" @click="cancel">关闭</button>
                <button class="tong-btn btn-parmary paddr-r5" v-show="isadd" @click="save">提交认证</button>
            </div>
        </div>
        </div>
    </div>
	<div id="popCenter" v-cloak>
	    <div class="popshow" v-if="isShow"></div>
	    <div v-if="isShow" class="zui-form podrag bcsz-layer  flex-container flex-dir-c"
	         style="width: auto;overflow: hidden;top: 80px;bottom: 50px; height: auto; left: 100px; right: 100px">
	        <div class="layui-layer-title">请输入参合人员信息</div>
	        <span class="layui-layer-setwin">
	            <a @click="isShow=false" class="closex ti-close" href="javascript:;"></a>
	        </span>
	        <div class="layui-layer-content flex-container flex-dir-c flex-one">
	            <div class="layui-height flex-container flex-dir-c flex-one " id="loadPage">
	
	            </div>
	        </div>
	    </div>
	</div>
</div>
<div class="pop openConfirm printHide" id="pop" ref="tspop" style="display: none">
    <div class="pophide printHide show"></div>
    <div class="zui-form confirm-height podrag show openConfirm pop-548">
        <div class="confirm-title">确认撤销</div>
        <div class="confirm-content">
            <div class="confirm-mad confirm-height">
                <div class="grid-box pop-box" style="width: 100%;position: relative;">
                    <div class="col-xxl-12">
                        <div class="pop-content" style="padding-left: 106px;"><span style="position: absolute;left:0;line-height: 36px;">请输入撤销原因:</span><input id="tfyy" class="zui-input" ></div>
                    </div>
                </div>
            </div>
            <div class="confirm-row ">
                <button class="confirm-btn confirm-primary-b cancel" @click="closes">取消</button>
                <button class="confirm-btn confirm-primary  submit" @click="saveData">确定</button>
            </div>
        </div>
    </div>
</div>


</body>
</html>
<script type="application/javascript" src="/newzui/pub/js/insuranceGbUtils.js"></script>
<script type="application/javascript" src="bzrz.js"></script>
