<div id="gzjqnh">
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">基本信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">门诊号</label>
                    <input id="ghxh"  class="zui-input"  v-model="popContent.brjbxxModel.ghxh" readonly="readonly">
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">病人姓名</label>
                    <div class="zui-input-inline">
                        <input id="brxm"  class="zui-input"  v-model="popContent.brjbxxModel.brxm" readonly="readonly">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">门诊科室</label>
                    <div class="zui-input-inline" id="ryks-box">
                        <input id="ryksmc"  class="zui-input"  v-model="popContent.brjbxxModel.ghksmc" readonly="readonly">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">就诊日期</label>
                    <div class="zui-input-inline zui-date">
                        <i class="datenox fa-calendar"></i>
                        <input id="ryrq"  class="zui-input"  v-model="popContent.brjbxxModel.ghrq" readonly="readonly">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">参保年度</label>
                    <div class="zui-input-inline zui-date">
                        <i class="datenox fa-calendar"></i>
                        <input class="zui-input zui-dateList" v-model="popContent.brjbxxModel.cbnd" id="timeCbnd"
                               data-notEmpty="true" @keydown="nextFocus($event,'',true)" readonly="readonly">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">病人信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box" style="height: 200px;">
                <div class="zui-table-view" id="brRyList" style="overflow: auto;">

                    <div class="zui-inline col-xxl-3" style="width: 50%;padding-left: 0px">
                        <div class="col-xxl-4"style="width: 17%">
                            <select-input
                                    @change-data="commonResultChange"
                                    :not_empty="false"
                                    :child="bookNoType_tran"
                                    :index="popContent.brjbxxModel.bookNoType"
                                    :val="popContent.brjbxxModel.bookNoType"
                                    :name="'popContent.brjbxxModel.bookNoType'" id="bookNoType">
                            </select-input>
                        </div>
                        <div class="zui-input-inline" style="width: 33%">
                            <div class="col-xxl-8" style="padding-right: 5px;width: 100%">
                                <input id="bookNo" placeholder="输入后回车搜索" data-notEmpty="true" class="zui-input"
                                       :title="popContent.brjbxxModel.bookNo"
                                       v-model="popContent.brjbxxModel.bookNo"
                                       @keydown.13="getPersonInfo">
                            </div>
                        </div>
                        <div class="zui-input-inline" style="width: 45%">
                            <div class="col-xxl-8" style="padding-right: 5px;">
                                <button class="tong-btn btn-parmary paddr-r5" @click="getPersonInfo">查询</button>
                            </div>
                        </div>
                    </div>

                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>成员编码</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>辖区编码</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>家庭编码</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>性别</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>身份证</span></div>
                                </th>

                                <th>
                                    <div class="zui-table-cell cell-s"><span>年龄</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>出生日期</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>医疗证（卡）号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>监护人身份证号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>监护人</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xxl"><span>家庭住址</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>电话号码</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xxl"><span>个人身份属性名称</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)">
                        <table v-if="jsonList.length" class="zui-table table-width50">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                                :tabindex="$index"
                                @click="checkSelect([$index,'one','jsonList'],$event)"
                                class="tableTr2"
                                @dblclick="edit($index)">
                                <td><div class="zui-table-cell cell-s" v-text="$index+1"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.memberNO"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.name"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.countryTeamCode"></div></td>
                                <td><div class="zui-table-cell cell-xl" v-text="item.familySysno"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="brxb_tran[item.sexId]"></div></td>
                                <td><div class="zui-table-cell cell-xl" v-text="item.idcardNo"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.age"></div></td>
                                <td><div class="zui-table-cell cell-l" v-text="item.birthday"></div></td>
                                <td><div class="zui-table-cell cell-xl" v-text="item.bookNo"></div></td>
                                <td><div class="zui-table-cell cell-xl" v-text="item.guardianCardno"></div></td>
                                <td><div class="zui-table-cell cell-l" v-text="item.guardianName"></div></td>
                                <td><div class="zui-table-cell cell-xxl" v-text="item.familyAddress"></div></td>
                                <td><div class="zui-table-cell cell-l" v-text="item.tel"></div></td>
                                <td><div class="zui-table-cell cell-xxl" v-text="item.ideName"></div></td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="!jsonList.length" class="flex noData  text-center zan-border">暂无数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">登记信息</div>
        </div>
        <div class="tab-card-body">
            <div class="grid-box zui-form">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">个人编号</label>
                    <input id="memberSysno"  class="zui-input" data-notEmpty="true" v-model="popContent.brjbxxModel.memberNo" readonly="readonly">
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">家庭编号</label>
                    <input id="familySysno"  class="zui-input" data-notEmpty="true" v-model="popContent.brjbxxModel.familyNo" readonly="readonly">
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">农合中心</label>
                    <select-input @change-data="commonResultChange" :search="true"
                                  :child="centerNoList" :index="'centerName'" :index_val="'centerNo'" :val="popContent.brjbxxModel.centerNo"
                                  :name="'popContent.brjbxxModel.centerNo'" :index_mc="'centerName'" id="centerNo">
                    </select-input>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">就诊类型</label>
                    <select-input @change-data="commonResultChange" :not_empty="true"
                                  :child="jzlx_tran" :index="popContent.brjbxxModel.cureCode" :val="popContent.brjbxxModel.cureCode"
                                  :name="'cureCode'">
                    </select-input>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">来诊状态</label>
                    <select-input @change-data="commonResultChange"
                                  :child="jzqk_tran" :index="popContent.brjbxxModel.inHosId" :val="popContent.brjbxxModel.inHosId"
                                  readonly="readonly"
                                  :name="'popContent.brjbxxModel.inHosId'">
                    </select-input>
                </div>
                <div class="zui-inline col-xxl-3" id="code">
                    <label class="zui-form-label">治疗方式</label>
                    <input   class="zui-input" data-notEmpty="true" v-model="popContent.brjbxxModel.code" @keydown="changeDown($event,'zlfs','zlfsContent','zlfsSearchCon')" @input="change(false,'zlfs',$event.target.value)">
                    <zlfssearch-table :message="zlfsSearchCon" :selected="selSearch"
                                      :them="zlfsthem" :page="page"
                                      @click-one="checkedOneOut" @click-two="selectZlfs">
                    </zlfssearch-table>
                </div>
                <div class="zui-inline col-xxl-3" id="jbbmInput">
                    <label class="zui-form-label">门诊诊断</label>
                    <div class="zui-input-inline">
                        <input class="zui-input" data-notEmpty="true" v-model="popContent.brjbxxModel.firstIcdName" @keydown="changeDown($event,'jbbm','jbbmContent','jbsearchCon')"
                               @input="change(false,'jbbm',$event.target.value)">
                        <jbsearch-table :message="jbsearchCon" :selected="selSearch"
                                        :them="jbthem" :page="page"
                                        @click-one="checkedOneOut" @click-two="selectJbbm">
                        </jbsearch-table>
                    </div>
                </div>
                <div class="zui-inline col-xxl-3" id="bclbCode">
                    <label class="zui-form-label">补偿类型</label>
                    <div class="top-zinle">
                        <select-input @change-data="commonResultChange" :not_empty="true"
                                      :child="bclbList" :index="'bclbName'" :index_val="'bclbCode'" :val="popContent.brjbxxModel.bclbCode"
                                      :name="'popContent.brjbxxModel.bclbCode'" :index_mc="'bclbCode'" :search="false" readonly="readonly" id="bclbCode">
                        </select-input>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="panel float-right">
        <div class="tong-top">
            <button class="tong-btn btn-parmary" v-if="popContent.brjbxxModel.type=='1'" @click="mzdj">登记</button>
            <button class="tong-btn btn-parmary" v-if="popContent.brjbxxModel.type=='2'" @click="mzdj">修改</button>
            <button class="tong-btn btn-parmary-b" v-if="popContent.brjbxxModel.type=='2'" @click="qxmzdj">取消登记</button>
            <!--<button class="tong-btn btn-parmary" v-if="popContent.brjbxxModel.jsbz=='1'">取消结算</button>-->
        </div>
    </div>
</div>
<script type="application/javascript" src="insurancePort/004gzjqnh/004gzjqnh.js"></script>