
@image:"../../../../pub/image/";
.l-zk-left{
  width: 36.5%;
  float: left;
}
.l-zk-top{
  width: 100%;
  background:#fafafa;
  border:1px solid #eeeeee;
  height: 50px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 12px;
  position: relative;
  .l-bgtx{
    width: 111px;
   position: absolute;
    right: 10px;
    top:5px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    span{
      display: block;
      position: relative;
      cursor: pointer;
      i{
        position: relative;
        width:100%;
        height:20px;
        text-align: center;
        display: block;
      }
      em{
        width:100%;
        text-align: center;
        display: block;
        color:#9fa9ba;
      }
    }
    .active{
      color: #1abc9c;
      em{
        color: #1abc9c;
      }
    }
    .l-span-line:before{
      content: '';
      position: absolute;
      width:1px;
      height:15px;
      right: -12px;
      top: 14px;
      background: rgba(159,169,186,.5);
    }
  }
}
.l-zk-jysb{
  width: 100%;
  padding: 14px 7px 10px 7px;
  box-sizing: border-box;
  .zui-form{
    .padd-l78{
      padding: 0 0 0 78px;
    }
    .padd-l64{
      padding: 0 0 0 64px;
    }
    .margin-l15{
      margin-left: 15px;
    }
    .margin-l10{
      margin-left: 10px;
    }
  }
  .zui-form-label{
    width: auto;
    padding: 0;
    line-height: 36px;
    color: #7f8fa4;
  }
}
.zui-table-view .zui-table-body{
  border: none !important;
}
.zui-table-view table tr:last-child{
  border-bottom: 1px solid #eee;
}
.zui-table-view{
  min-height: 85vh;
}
.zui-table-view .zui-table-body{
  height:70vh;
}
#divlink{
  margin-left: 86%;
}
.l-zk-right{
 width:63%;
  float: right;
  position: relative;
  .zui-table-view{
    border-top: none;
  }
}
.zui-date .datenox{
  left: 6px;
  color: #c5d0de;
}
.text-indent20{
  text-indent: 20px;
}
//图标



//end

.zklx-select:after{
  right:13px;
}
.zk-right-fixed{
  position: absolute;
  bottom:0px;
  right:0;
  width:100%;
  left:0;
  z-index:10000;
  height: 80px;
  background: rgba(254,250,246,1);
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-around;
  align-items: center;
  span{
    font-size:14px;
    color:#7f8fa4;
    i{
      font-size:18px;
      color:#f2a654;
    }
  }
}





.tab-message{
  width: 100%;
  height:46px;
  background: #1abc9c;
  line-height: 46px;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  a{
    color: #fff;
  }
  .icon-cha:before{
    top: 0;
  }
}
.l-zk-ms{
  width: 100%;
  padding:0 20px;
  box-sizing: border-box;
  float: left;
  overflow: hidden;
  position: relative;
.l-zk-col-6{
  width:48%;
  float: left;
  margin-top: 20px;
  label{
    color:#7f8fa4;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    i{
      width:60px;
      display: block;
      text-align: right;
    }
    .l-data{
      position: absolute;
      width: 20px;
      height: 20px;
      left:72px;
      top:11px;
      display: block;
    }
    .l-color35{
      color:#354052 !important;
    }
  }
  &:nth-child(2n){
    margin-left: 5px;
    float: right !important;
  }
  .l-time,.l-times,.l-times2,.l-time1{
    text-indent: 17px;
  }
  .l-dw-after{
    position: absolute;
    right:9px;
    width: auto;
    height: 20px;
    top: 9px;
    color: #1abc9c;
  }
  .l-label-left{
    float: left;
    color:#354052;
    width: 103px;
    line-height:36px;
    margin-left:15px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

  }
  .l-label-right{
    float: left;
    line-height: 36px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
  .fr{
    float: right !important;
  }
  label{
    color:#7f8fa4;
  }
}
.l-bottom-fixed{
  position: absolute;
  bottom: 0;
  height: 70px;
  right: 0;
  left: 0;
  border-top: 1px solid  #dfe3e9;
  display: flex;
  justify-content: flex-end;
  align-items: center;

}
.l-width100{
  width: 100%;
  height: 100%;
}
.l-textarea{
  width: 100%;
  border:1px solid #dfe3e9;
  max-height: 600px;
  height: 600px;
  margin-top: 8px;
  float: left;
  text-indent: 10px;
  padding: 10px 10px;
  border-radius: 4px;
}
.l-right-tx{
  width: 100%;
  background: #fff;
  padding: 0 24px 90px;
  box-sizing: border-box;
  display: none;
  position: absolute;
  top:0;
  z-index: 9999;
  h2{
    font-size:22px;
    color:#354052;
    width: 100%;
    text-align: center;
  }
  .l-tx-title{
    width: 100%;
    padding: 20px 0;
    float: left;

    span{
      display: block;
      color:#7f8fa4;
      float: left;
      line-height:30px;
      &:nth-child(3n){
        text-indent: 70px;
      }
    }
    .l-tx-col4{
      width:33.3333%;
    }
    .l-tx-col12{
      width: 100%;
    }
  }
  .l-zktu{
    width: 100%;
    height: 500px;
    border: 1px solid #1abc9c;
    float: left;
  }
  .l-zk-cdjg{
    width: 100%;
    padding: 0 10px 0 28px;
    box-sizing: border-box;
    float: left;
    position: relative;
    span{
      width: 100%;
      border-bottom: 1px dashed #1abc9c;
      display: block;
      height:20px;
      position: relative;
      &:after{
       background: #fff;
        content: '测定结果';
        width:auto;
        padding-right: 5px;
        height: 30px;
        color: #1abc9c;
        position: absolute;
        left: 0;
        top: 4px;
        text-align: center;
        line-height: 30px;

      }
      &.l-zk:after{
        content: '质控描述';
      }
    }
  }
  .l-zk-time{
    width: 100%;
    float: left;
    margin: 25px 0;
  }
  .l-time-top{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 36px;
    background:#edf2f1;
    i{
      width: 33.333%;
      display: block;
      text-align: center;
      border-right:1px solid #e9eee6;
      &:last-child{
        border-right: none;
      }
    }
  }
  .l-time-bg{
    width: 100%;
    min-height:312px;
    span{
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #e9eee6;
      border-top: none;
      line-height: 40px;
      i{
        width:33.333%;
        display: block;
        text-align: center;
        border-right:1px solid #e9eee6;
        &:last-child{
          border-right: none;
        }
      }
      &:nth-child(2n){
        background:#fdfdfd;
      }
    }
  }
  .l-zk-popel{
    width: 100%;
    color:#7f8fa4;
    line-height:24px;
    padding: 0 0 20px 26px;
    float: left;
    margin-top: 20px;
  }
  .l-zk-bgr{
    width: 100%;
    padding-top:60px;
    float: left;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span{
      font-size:14px;
      color:#333333;
      width: auto;
      display: block;
      padding: 20px 30px 20px 30px;
      i{
        color:#f2a654;
      }
    }
  }
}
.padd-r50{
  padding-right: 50px !important;
}

.dishide{
  display: none;
}
.disShow{
  display: block;
}

.zui-table-view .zui-table-fixed.table-fixed-r{
  border-left: none;
}
.icon-ms:before{
  left:19px;
}
.dang-tell{
  .cell-2-0{
    width: 50px !important;
  }
}
.ls-td{
  width: 50px !important;
  .zui-table-cell{
    width: 50px !important;
  }
}