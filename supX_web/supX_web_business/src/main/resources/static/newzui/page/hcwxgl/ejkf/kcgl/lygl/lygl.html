<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>领用管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
    <link type="text/css" href="lygl.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel" v-cloak>
        <div class="tong-top">
            <button  class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-show="isShowkd">开单</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kd(1)" v-if="!isShowkd && JSON.stringify(objItem)=='{}'">添加材料</button>
			<button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="zdsc" v-if="!isShowkd && JSON.stringify(objItem)=='{}'">自动生成</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="searchHc">刷新</button>
        </div>
        <div class="tong-search" :class="{'tong-padded':isShow}">
            <div class="zui-form" v-if="isShowkd">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">二级库房</label>
                    <div class="zui-input-inline wh122" style="margin-left: 20px">
                        <select-input @change-data="resultChange"
                                      :not_empty="false" :child="yfList"
                                      :index="'yfmc'" :index_val="'yfbm'"
                                      :val="yfbm" :search="true" :name="'yfbm'"
                                      id="yfbm" :index_mc="'yfbm'">
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">时间段</label>
                    <div class="zui-input-inline flex-container flex-align-c  margin-f-l5">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请开始日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="searchHc()" type="text" v-model="search"/>
                    </div>
                </div>
            </div>
            <div class="jbxx fyxm-hide" :class="{'btn-show':isShow}">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20">
                       <div class="zui-inline padd-l-40 ">
                            <label class="zui-form-label">二级库房</label>
                            <div class="zui-input-inline wh122" style="margin-left: 20px">
                                <select-input @change-data="resultChangeYf"
                                              :not_empty="false" :child="yfList"
                                              :index="'yfmc'" :index_val="'yfbm'"
                                              :val="yfbm" :search="true" :name="'yfbm'"
                                              id="yfbm" :index_mc="'yfbm'" :disable="JSON.stringify(objItem)!='{}'">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">库房</label>
                            <div class="zui-input-inline wh122">
                                <select-input @change-data="resultChange"
                                              :not_empty="false" :child="KFList"
                                              :index="'kfmc'" :index_val="'kfbm'"
                                              :val="kfbm" :search="true" :name="'kfbm'"
                                              id="kfbm" :index_mc="'kfbm'" :disable="JSON.stringify(objItem)!='{}'">
                                </select-input>
                            </div>
                        </div>
						<!-- <div class="zui-inline">
						    <label class="zui-form-label">时间段</label>
						    <div class="zui-input-inline flex-container flex-align-c  margin-f-l5">
						        <i class="icon-position icon-rl"></i>
						        <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请开始日期" id="timeVal2"/><span class="padd-l-5 padd-r-5">~</span>
						        <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal3" />
						    </div>
						</div>-->
						<div class="zui-inline padd-l-40 margin-f-l10">
						    <label class="zui-form-label">数量</label>
						    <div class="zui-input-inline">
						        <input class="zui-input" placeholder="请输入数量" type="number" @input="specifiName()" v-model='tysl' :disabled="JSON.stringify(objItem)!='{}'"/>
						    </div>
						</div>
                        <div class="zui-inline padd-l-40 margin-f-l10" style="width: 30%;">
                            <label class="zui-form-label">备注</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" placeholder="请输入备注" type="text" id="bzms" v-model="bzms" :disabled="JSON.stringify(objItem)!='{}'"/>
                            </div>
                        </div>
						
                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期:<i v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10"   id="utable1" v-cloak >
        <!--入库列表-->
        <div class="zui-table-header" key="a" v-if="isShowkd">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>申领单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>开单时间</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>开单员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>备注</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单据状态</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " key="a"  @scroll="scrollTable($event)" v-if="isShowkd">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item,$index) in bsdList" :tabindex="$index" @click="checkSelect([$index,'one','bsdList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index"
                	@dblclick="showDetail($index,item)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.sldh">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'AllDate')">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zdrmc">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.bzms">备注</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : '' "></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="zhuangtai[item.ckshzfbz]" :class="item.ckshzfbz=='0' ? 'color-dsh':item.ckshzfbz=='1' ? 'color-ysh' : item.ckshzfbz=='2' ? 'color-yzf' : '' "></div>
                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">
                            <span class="flex-center padd-t-5">
                                <em class="width30"  v-if="item.shzfbz== 0">
                                    <i class="icon-sh" @click="showDetail($index,item)" data-title="审核"></i>
                                </em>
                                <em  class="width30" v-if="item.shzfbz == 0 || (item.shzfbz == 1 && item.ckbz == 0)" >
                                    <i class="icon-js" @click="invalidData($index)" data-title="作废"></i>
                                </em>
                                <em class="width30"  v-if="item.shzfbz != '1' && item.shzfbz !='2'">
                                	<i class="icon-bj" @click="bianji($index,item)" data-title="编辑"></i>
                                </em>
                             </span>
                        </div>
                    </td>
                    <p v-show="bsdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <!--添加材料-->
        <div class="zui-table-header " key="b" v-if="isShow" >
            <table class="zui-table table-width50">
                <thead>
                <tr>
					<th class="cell-m" v-if="sldmxList.length>=1">
					    <div class="zui-table-cell cell-m">
					        <input class="green" type="checkbox" v-model="isCheckAll">
					        <label @click="reCheckBoxSon()"></label>
					    </div>
					</th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>领药数量</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>一级库房库存</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>二级库房库存(大)</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>二级库房库存(小)</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>库存上限</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>库存下限</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-heights  " key="b" @scroll="scrollTable($event)" v-if="isShow" id="zui-table">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in sldmxList" :class="[{'table-hovers':isChecked[$index]}]">
					<td class="cell-m" v-if="sldmxList.length>=1">
					    <div class="zui-table-cell cell-m">
					        <input class="green" type="checkbox" v-model="item.isChecked">
					        <label @click="checkSelectSon($index)"></label>
					    </div>
					</td>
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.ypmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" >{{item.ypgg}}</div>
                    </td>
					<td>
					    <div class="zui-table-cell cell-s" v-text="">
							<i>{{item.slsl}}</i>
							<i>{{item.kfdwmc}}</i>
						</div>
					</td>
					<td>
					    <div class="zui-table-cell cell-s">
							<i>{{item.sjkc}}</i>
							<i>{{item.kfdwmc}}</i>
						</div>
					</td>
					<td>
					    <div class="zui-table-cell cell-s">
							<i>{{item.dkc}}</i>
							<i>{{item.kfdwmc}}</i>
						</div>
					</td>
					<td>
					    <div class="zui-table-cell cell-s">
							<i>{{item.xkc}}</i>
							<i>{{item.yfdwmc}}</i>
						</div>
					</td>
					<td>
					    <div class="zui-table-cell cell-s" v-text="item.zgkc">序号</div>
					</td>
					<td>
					    <div class="zui-table-cell cell-s" v-text="item.zdkc">序号</div>
					</td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell text-over-2 cell-s" v-text="item.cdmc">状态</div>
                    </td>

                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                    </td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-show="sldmxList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in sldmxList" :tabindex="$index"  class="tableTr2">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--右侧固定-->
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in sldmxList" :tabindex="$index"  class="tableTr2">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span style="display: flex;justify-content: center;align-items: center;">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <page @go-page="goPage" key="d" :totle-page="totlePage" v-show="isShowkd" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="rkgl-position" key="c" v-if="isShow">
           <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="print()" v-if="objItem.shzfbz=='1'">打印</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-if="objItem.shzfbz=='0'">作废</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-if="JSON.stringify(objItem)=='{}'">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-if="objItem.shzfbz=='0'">审核</button>
           </span>
        </div>
    </div>
</div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-548 contextInfo"  v-cloak id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--编辑材料-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>制单日期</i>
                    <input class="zui-input" disabled="disabled" v-model="zdrq">
            </li>
            <li>
                    <i>制单员</i>
                    <select-input @change-data="resultChange" :child="ryList" :index="'ryxm'" :index_val="'rybm'" :val="zdy"
                                  :search="true" :name="'zdy'">
                    </select-input>
            </li>
            <li>
                    <i>材料名称</i>
                    <input autocomplete="off" id="ypmc" class="zui-input" v-model="popContent.ypmc" @keyup="changeDown($event,'ypmc')" @input="change(false,'ypmc',$event.target.value)"
                           data-notEmpty="true">
                    <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
                                  :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
            </li>
            <li>
                    <i>申领数量</i>
                    <input autocomplete="off" type="number" ref="cksl" data-notEmpty="true" class="zui-input" v-model="popContent.slsl" @keydown.13="addData($event)" />
                    <em style="position:absolute;top:10px;right:10px;z-index:111" v-text="popContent.kfdwmc"></em>
                </li>
            <li>
                    <i>材料规格</i>
                    <input  class="zui-input" v-model="popContent.ypgg" disabled/>
            </li>
            <li>
                    <i>库存数量</i>
                    <input type="number" class="zui-input" disabled="disabled" v-model="popContent.kcsl"   />
                    <em style="position:absolute;top:10px;right:10px;z-index:111" v-text="popContent.kfdwmc"></em>
                </li>
            <li>
                    <i>材料进价</i>
                    <input type="number" class="zui-input" disabled="disabled" v-model="fDec(popContent.ypjj,2)">
            </li>
            <li>
                    <i>材料零价</i>
                    <input type="number" class="zui-input" disabled="disabled" v-model="fDec(popContent.yplj,2)">
            </li>
            <li>
                    <i>材料批号</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="popContent.scph">
            </li>
            <!--<li>
                    <i>有效期至</i>
                    <input type="text" class="zui-input" id="yxqz" disabled="disabled" v-model="fDate(popContent.yxqz,'date')">
            </li>-->
            <li>
                    <i>产地</i>
                    <input type="text" class="zui-input"  v-model="popContent.cdmc" disabled>
            </li>
        	<li>
                    <i>供货商</i>
                    <input type="text" class="zui-input"  v-model="popContent.zdghdwmc" disabled>
            </li>
            <li>
                    <i>分装比例</i>
                    <input type="text" class="zui-input"  disabled="disabled" v-model="popContent.fzbl" >
            </li>
            <li>
                    <i>产品标准&ensp;&ensp;号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.cpbzh">
            </li>
            <li>
                    <i>批准文号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.pzwh">
            </li>
            <!--<li>
                    <i>生产日期</i>
                    <input type="text" class="zui-input " id="scrq" disabled="disabled" v-model="fDate(popContent.scrq,'date')">
            </li>
            <li>
                    <i>备注</i>
                    <input type="text" class="zui-input " v-model="popContent.bzms">
            </li>-->
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="addData">保存</button>
    </div>
</div>

<script src="lygl.js"></script>
</body>

</html>
