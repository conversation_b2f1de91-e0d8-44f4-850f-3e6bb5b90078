.userNameBg{
    background:#708f89;
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    -webkit-user-select: none;
    /*margin-top: -125.58px;*/
    -moz-user-select: none;
    user-select: none;
    background-image: url("/newzui/pub/image/userImg.png");
}
.flex{
    display: flex;
    align-items: center;
}
.userNameImg{
    /*width: 100px;*/
    /*height: 100px;*/
}
.header-item{
    /*height: 100%;*/
    /*padding-top: 125.58px;*/
}
.userNameImg img{
    width: 100%;
    height: 100%;
}
.text-color{
    width: 100%;
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}

.userCwh .text,.userFooter .text{
    display: inline-block;
    white-space: nowrap;
    margin-right: 20px;
}
.userCwh{
    margin-bottom: 4px;
}
.dyblImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    height:80px;
    background-image: url("/newzui/pub/image/yuanquan.png");
}
.dyblImg:after{
    content: '';
    background-image: url("/newzui/pub/image/bl.png");
    width: 60%;
    height: 60%;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{

    color: #B0BFBB;
    text-decoration: underline;
}
.content{
    /*-webkit-user-select: none;*/
    /*-moz-user-select: none;*/
    /*user-select: none;*/
    background: #fff;
}
.blImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/xbl.png");
}
.xyzImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/xyz.png");
}
.fzImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/fz.png");
}
.dy-bgImg{
    width:86px;
    height: 86px;
    background: url("/newzui/pub/image/dy-bl.png") center no-repeat;
    background-size: 86px 86px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}
.dy-img{
    width: 48px;
    height: 41px;
    background: url("/newzui/pub/image/dy-img.png") center no-repeat;
    background-size: 48px 41px;
    cursor: pointer;
    display: block;
}
.blRight{
    position: absolute;
    right: 7px;
    display: flex;
    bottom: -40px;
}



.loadPage{
    position: relative;
    padding-top: 10px !important;
    background: #fff;
    overflow: auto;
    height: 100%;
}
.content{
    width: 100%;
    /*float: left;*/
    overflow: hidden;
    /*height: 100%;*/
    /*padding-top: 36px;*/
}
.fyxm-tab{
    /*margin-top: -36px;*/
}
.pop-width{
    width: 380px;
}
.color-f2a654{
    color:#f2a654;
}
.sdrj-pop{
    border:1px solid #dfe3e9;
    border-radius:4px;
    height:74px;
    width: 284px;
}
.bg-fiexd{
    background-image: url(/newzui/pub/image/hzxx.png);
    position: fixed;
    top: 46%;
    z-index: 111;
    right: 0;
    width: 34px;
    cursor: pointer;
    height: 128px;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
}
.useritem .userlist{
    border-left:1px solid #eeeeee;
    border-right:1px solid #eeeeee;
}
.useritem .userlist:nth-child(even){
    background:#fdfdfd;
}
.useritem .userlist:nth-child(odd){
    background:#ffffff;
}
.useritem .userlist .userKey{
    border-top:1px solid #eeeeee;
    width: 155px;
    min-height: 30px;
    font-size:14px;
    color:#7f8fa4;
    text-align: center;
    padding-right: 20px;
}
.useritem .userlist .userValue{
    width:298px;
    padding: 6px 0;
    padding-left: 19px;
    font-size:14px;
    border-top: 1px solid #eeeeee;
    border-bottom: 1px solid #eeeeee;
    border-left:1px solid #eeeeee;
    color:#354052;
    text-align: center;
}
.userValue:nth-child(even){
    border-top: none;
}
.height-100{
    height: 100%;
}
.useritem .userlist .font-14-654{
    font-size:14px;
    color:#f2a654;
}
.ksys-side .useritem .userlist {
    display: flex;
    padding-bottom: 0;
}
.ksys-side{
    height: 100%;
}
.userlength{
    border-bottom:1px solid #eeeeee;
}