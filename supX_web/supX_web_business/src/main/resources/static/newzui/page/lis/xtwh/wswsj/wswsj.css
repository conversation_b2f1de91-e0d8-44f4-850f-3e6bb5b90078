.fydm-content-left {
  width: 49.5%;
  float: left;
}
.fydm-content-left .content-left-top {
  width: 100%;
}
.fydm-content-left .content-left-top i {
  width: calc((100% / 1));
  text-align: center;
}
.fydm-content-left .sjks-content-left-list li {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
}
.fydm-content-left .sjks-content-left-list li i {
  width: calc((100% / 1));
  text-align: center;
}
.fydm-content-left .sjks-content-left-list li i :nth-child(even) {
  background: #fdfdfd;
}
.fydm-content-left .sjks-content-left-list li:hover {
  background: rgba(26, 188, 156, 0.08);
  border: 1px solid #1abc9c;
}
.sjks-content-right {
  width: 49.5%;
  float: right;
}
.sjks-content-right .content-right-top {
  width: 100%;
}
.sjks-content-right .content-right-top i {
  width: calc((100% / 1));
  text-align: center;
}
.sjks-content-right .content-right-top i em {
  margin-top: 10px;
}
.sjks-content-right li {
  cursor: pointer;
  width: 100%;
}
.sjks-content-right li:hover {
  background: rgba(26, 188, 156, 0.08) !important;
  border: 1px solid #1abc9c;
}
.ksys-side {
  width: 100%;
  padding: 26px 17px;
  float: left;
}
.ksys-side span {
  display: block;
  width: 100%;
  position: relative;
}
.ksys-side span i {
  display: block;
  width: 100%;
  line-height: 36px;
}
.ksys-side #jyxm_icon .switch {
  top: 0;
  left: 17px;
}
.border-r4 {
  border-radius: 4px !important;
}
.ksys-btn {
  position: absolute;
  bottom: 20px;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 40px;
}
.ksys-btn button {
  margin-right: 20px;
}
.xmzb-content-right i {
  width: calc((100% / 2)) !important;
  text-align: center;
}
