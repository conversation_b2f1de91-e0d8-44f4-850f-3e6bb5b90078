<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>待会诊管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="dhzgl.css" rel="stylesheet"/>
</head>

<body class="skin-default flex-container padd-l-10 padd-r-10 flex-dir-c flex-one">
<div class=" background-f percent100" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button v-waves class="tong-btn btn-parmary" @click="goToPage(1)"><span class="fa fa-refresh"></span>刷新</button>
        </div>
        <div class="flex-container padd-b-10 padd-t-10 padd-l-10 flex-align-c">
            <div class="flex-container flex-align-c padd-r-10">
                <span class="padd-r-5 ft-14">状态</span>
                <select-input class="wh122" @change-data="commonResultChange"
                              :child="hzzt_tran" :index="'hzzt'" :val="param.hzzt"
                              :name="'param.hzzt'" >
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="padd-r-5 ft-14">科室</span>
                <select-input :cs="true" @change-data="commonResultChange" :not_empty="false" :search="true"
                              :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="param.sqysks"
                              :name="'param.sqysks'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="padd-r-5 ft-14">时间段</span>
                <input type="text" class="zui-input wh179 times " v-model="param.beginrq"/>
                <div class="flex-container flex-align-c padd-r-5 padd-l-5">至</div>
                <input type="text" class="zui-input wh179 times1 " v-model="param.endrq"/>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="padd-r-5 ft-14">检索</span>
                <input class="zui-input todate wh182 " @keyup.enter="searching" v-model="param.parm" placeholder="请输入关键字" id="timeVal"/>
            </div>
        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10" v-cloak>
        <div class="zui-table-header">
            <table class="zui-table ">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l"><span>会诊编号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>申请医生</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>申请科室</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>申请日期</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m"><span>床位号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m"><span>年龄</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m"><span>性别</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>身份证号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>受邀科室</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>受邀医生</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>会诊安排时间</span></div>
                    </th>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s"><span>状态</span></div>
                    </th>
                    <th class="cell-l">
                        <div class="zui-table-cell cell-l"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)">
            <table class="zui-table zui-collapse">
                <tbody>
                <tr v-for="(item,$index) in hzsqList" :tabindex="$index"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()" @dblclick="group($index)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.hzbh">
                            会诊编号 <!--门诊：颜色状态值color-393f，急诊：color-cff5，住院：color-008-->
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.sqysxm">申请医生</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.sqysksmc">申请科室</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.sqrq,'short')">2017/12/12</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s"><i class="text-line color-dsh" @click="Patient" v-text="item.brxm">患者姓名</i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-m"><i class="text-line color-dsh" @click="Patient" v-text="item.cwbh">床位号</i></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-m" v-text="item.nl+item.nldw">年龄</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-m" v-text="item.brxb">性别</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.sfzjhm">身份证号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yqysksmc">受邀科室</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yqysxm">受邀医生</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.hzkssj,'short')">会诊安排时间</div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s" v-text="hzzt_tran[item.hzzt]" :class="item.hzzt == '0' ? 'color-cff5':'' ">
                            会诊中 <!--会诊中：颜色状态值color-008，待会诊:color-cf3，待诊断：color-c04，已结束:color-ca3-->
                        </div>
                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">
                                <span class="flex-center">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                    <em class="width30" v-if="item.hzzt == '1'">
                                        <i class="iconfont icon-icon69 icon-font25 icon-hover" data-title="填写会诊记录" @click="group($index)"></i>
                                    </em>
                                    <em class="width30 padd-t-5">
                                        <i class="iconfont icon-iocn20 icon-font20 icon-hover" data-title="查看病历" @click="yzCheck(item,['userPage/dzbl',3,item])"></i>
                                    </em>
                                    <i class="user-footer-img yzImg " v-if="item.hzzt == '1'" data-title="医嘱管理" @click="yzCheck(item,['userPage/yzgl',1,item])"></i>
                                    <i class="icon-icon icon-zf" v-if="item.hzzt == '2'" data-title="取消完成" @click="cancelSave(item)"></i>
                                </span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <!--暂无数据提示,绑数据放开-->
            <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
        </div>

        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l background-f">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!-- data-no-change -->
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table zui-collapse">
                    <tbody>
                    <tr v-for="(item, $index) in hzsqList"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @dblclick="group($index)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--右侧固定-->
        <div class="zui-table background-f">
            <div class="zui-table-header">
                <table class="zui-table zui-collapse">
                    <thead>
                    <tr>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in hzsqList"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" @dblclick="group($index)">
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" v-text="hzzt_tran[item.hzzt]"  :class="item.hzzt == '0' ? 'color-cff5':'' ">
                                会诊中 <!--会诊中：颜色状态值color-008，待会诊:color-cf3，待诊断：color-c04，已结束:color-ca3-->
                            </div>
                        </td>
                        <td class="cell-l">
                            <div class="zui-table-cell cell-l">
                                     <span class="flex-center">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                    <em class="width30 padd-t-5">
                                        <i class="iconfont icon-iocn20 icon-font20 icon-hover" data-title="查看病历" @click="yzCheck(item,['userPage/dzbl',3,item])"></i>
                                    </em>
                                    <em class="width30" v-if="item.hzzt == '1'">
                                        <i class="iconfont icon-icon69 icon-font25 icon-hover" data-title="填写会诊记录" @click="group($index)"></i>
                                    </em>
                                     <em class="width30">
                                         <i class="user-footer-img yzImg " v-if="item.hzzt == '1'" data-title="医嘱管理" @click="yzCheck(item,['userPage/yzgl',1,item])"></i>
                                     </em>
                                             <i class="icon-icon65 iconfont " data-title="费用记账" @click="fyjz(item)"></i>
                                </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>


        <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>


<script src="dhzgl.js"></script>
</body>

</html>
