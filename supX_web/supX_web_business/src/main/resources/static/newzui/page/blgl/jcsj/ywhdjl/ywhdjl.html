<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>电子病历临床基础维护</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ywhdjl.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">添加</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
            <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">临床数据组</label>
                    <div class="zui-input-inline padd-l-20">
                        <select-input @change-data="resultChange"
                                      :not_empty="false" :child="LcList"
                                      :index="'cdajcmb'" :index_val="'cdajcmbid'"
                                      :val="cdajcmb" :search="true" :name="'cdajcmb'"
                                      id="cdajcmb" :index_mc="'cdajcmb'">
                        </select-input>
                        <input type="hidden" v-model="cdajcmb" id="lcsjzid"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50-1">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="sexs" z-width="100px">
                        <div class="zui-table-cell">基础模板id</div>
                    </th>
                    <th z-field="sex" z-width="100px">
                        <div class="zui-table-cell">基础模板名称</div>
                    </th>
                    <th z-field="city" z-width="80px">
                        <div class="zui-table-cell">His接口分类</div>
                    </th>
                    <th z-field="jm1" z-width="80px">
                        <div class="zui-table-cell">护理文书</div>
                    </th>
                    <th z-field="jm2" z-width="100px">
                        <div class="zui-table-cell">是否权限控制</div>
                    </th>
                    <th z-field="jm3" z-width="80px">
                        <div class="zui-table-cell">书写方式</div>
                    </th>
                    <th z-field="jms" z-width="80px">
                        <div class="zui-table-cell">使用范围</div>
                    </th>
                    <th z-field="jm4" z-width="100px">
                        <div class="zui-table-cell">活动编码</div>
                    </th>
                    <th z-field="jm5" z-width="80px">
                        <div class="zui-table-cell">活动分类</div>
                    </th>
                    <th z-field="jm6" z-width="70px">
                        <div class="zui-table-cell">活动id</div>
                    </th>
                    <th z-field="jm7" z-width="70px">
                        <div class="zui-table-cell">活动简码</div>
                    </th>
                    <th z-field="jm8" z-width="70px">
                        <div class="zui-table-cell">活动名称</div>
                    </th>
                    <th z-field="jm9" z-width="70px">
                        <div class="zui-table-cell">活动记录数</div>
                    </th>
                    <th z-field="jm10" z-width="70px">
                        <div class="zui-table-cell">业务域</div>
                    </th>
                    <th z-field="jm11" z-width="100px">
                        <div class="zui-table-cell">最近操作时间</div>
                    </th>
                    <th z-field="jm12" z-width="100px">
                        <div class="zui-table-cell">删除标志</div>
                    </th>
                    <th z-field="jm13" z-width="100px">
                        <div class="zui-table-cell">备注说明</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-height" id="zui-table">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jsonList"  @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.cdajcmbid"></div></td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.cdajcmbmc" ></i>
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="hisjkfl[item.hisjkfl]" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="hlws[item.hlws]" ></i>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="sfqxkz[item.sfqxkz]"></div></td>

                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="sxfs[item.sxfs]"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="syfw[item.syfw]" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ywhdjlbm" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ywhdjlfl" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ywhdjlid" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ywhdjljm" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ywhdjlmc" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="ywhdjls[item.ywhdjls]" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ywy" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="fDate(item.zjczsj,'date')" ></i>
                        </div>
                    </td>
                    <td><div class="zui-table-cell">
                        <div class="switch">
                            <input  type="checkbox" :checked="item.scbz==0?true:false" disabled/>
                            <label></label>
                        </div>
                    </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.bzsm" ></i>
                        </div>
                    </td>

                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>基础模板id</i>
                    <input type="text" class="label-input background-h" disabled v-model="popContent.cdajcmbid" @keydown="nextFocus($event)"/>
            </li>

            <li>

                    <i style="float:right;padding-top: 13px;">删除标志</i>
                    <div class="switch" >
                        <input  type="checkbox" :checked="popContent.scbz==0?true:false"/>
                        <label></label>
                    </div>
            </li>
        </ul>
        <ul class="tab-edit-list tab-edit2-list border-dotted-t fl padd-t-15">

            <li>
                    <i>基础模板名称</i>
                    <input type="text" class="label-input "  v-model="popContent.cdajcmbmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>His接口分类</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="hisjkfl" :index="popContent.hisjkfl" :val="popContent.hisjkfl"
                                  :name="'popContent.hisjkfl'">
                    </select-input>
            </li>
            <li>
                    <i>护理文书</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="hlws" :index="popContent.hlws" :val="popContent.hlws"
                                  :name="'popContent.hlws'">
                    </select-input>
            </li>
            <li>
                    <i>书写方式</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="sxfs" :index="popContent.sxfs" :val="popContent.sxfs"
                                  :name="'popContent.sxfs'">
                    </select-input>
            </li>
            <li>
                    <i>使用范围</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="syfw" :index="popContent.syfw" :val="popContent.syfw"
                                  :name="'popContent.syfw'">
                    </select-input>
            </li>
            <li>
                    <i>业务活动编码</i>
                    <input type="text" class="label-input"  v-model="popContent.ywhdjlbm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>业务活动分类</i>
                    <input type="text" class="label-input "   v-model="popContent.ywhdjlfl" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>业务活动id</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.ywhdjlid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>业务活动简码</i>
                    <input type="text" class="label-input "   v-model="popContent.ywhdjljm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>业务活动名称</i>
                    <input type="text" class="label-input "   v-model="popContent.ywhdjlmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>业务活动记录数</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="ywhdjls" :index="popContent.ywhdjls" :val="popContent.ywhdjls"
                                  :name="'popContent.ywhdjls'">
                    </select-input>
            </li>
            <li>
                    <i>业务域</i>
                    <input type="text" class="label-input"  v-model="popContent.ywy" @keydown="nextFocus($event)"/>
            </li>
            <li  style="width: 100%; float:left;margin-top: 20px;">
                    <i>备注说明</i>
                    <input type="text" class="label-input" style="width: 76%;height: 60px"  v-model="popContent.bzsm" @keydown="nextFocus($event)"/>
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ywhdjl.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
