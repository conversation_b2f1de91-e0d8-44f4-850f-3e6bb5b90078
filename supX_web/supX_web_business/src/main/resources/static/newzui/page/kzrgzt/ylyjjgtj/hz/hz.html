<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>会诊</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../kshy/kshy.css">
    <link rel="stylesheet" href="../wjz/wjz.css">
    <link href="hz.css" rel="stylesheet">
</head>
<body class="skin-default">
<div class="wrapper background-f flex-container flex-dir-c flex-one">
    <div class="tong-top font-16 color-c3 font-weight flex-container flex-align-c ">会诊统计</div>
    <div class="content" v-cloak>
        <div class="tong-search">
            <div class="top-form">
                <label class="top-label">时间段</label>
                <div class="top-zinle flex-container">
                    <div class="zui-date position">
                        <i class="iconfont icon-icon61"></i>
                        <input type="text" autocomplete="off" class="zui-input wh122 dateStart" v-model="beginrq"/>
                    </div>
                    <span class="flex-container flex-align-c padd-r-10 padd-l-10">至</span>
                    <div class="zui-date position">
                        <i class="iconfont icon-icon61"></i>
                        <input type="text" autocomplete="off" class="zui-input wh122 dateEnd" v-model="endrq"/>
                    </div>
                </div>
            </div>
            <div class="top-form">
                <label class="top-label">类型</label>
                <div class="top-zinle">
                    <div class="top-zinle">
                        <select-input class="wh122" @change-data="resultChange"
                                      :child="istrue_tran" :index="'nbtclb'" :index_val="'nbtclb'" :val="nbtclb"
                                      :name="'ypContent.nbtclb'" :search="true" :index_mc="'nbtclb'">
                        </select-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="chart">
            <!--<h2 class="text-center font-18 font-weight color-393f">科室医生会议次数统计</h2>-->
            <div class="flex-container height-310">
                <div class="wh40">
                    <div class="canvas" id="pieCanvas"></div>
                    <div class="text-center margin-t-3"><span class="color-757c83 font14">接收会诊次数</span><span class="color-green font-30">123次</span></div>
                </div>
                <div class="wh60">
                    <div class="canvas"  id="canvas"></div>
                </div>
                <div class="flex-container flex-dir-cr">
                    <ul class="ranking margin-b-10">
                        <li class="flex-container whiteSpace flex-align-c" v-for="(list,index) in 22">
                            <div class="rankingNum rankingNumImg font-18 color-c7 text-center" :class="[index==0?'rankingOne':'',index==1?'rankingtwo':'',index==2?'rankingThree':'']">{{index>2?index+1:''}}</div>
                            <div class="rankingImg" style="background-image: url('/newzui/pub/image/2018072106.png')"></div>
                            <div class="rankingName">李浩然</div>
                            <div class="rankingNumber color-f38d4f">10次</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div style="width: 100%;background: #f2f2f2;min-height: 8px;"></div>
 <div class="flex-container flex-jus-sp" >
     <div style="width: 59.5%;border: 1px solid #eeeeee">
         <div class="tong-top   flex-container flex-align-c flex-jus-sp"><p class="font-16 color-c3 font-weight">会诊监管</p><p class="color-green font14 cursor">更多>></p></div>
         <div class=" zui-table-view flex-container flex-dir-c flex-one padd-l-10 padd-r-10  padd-t-10 " id="brRyList01">
             <div class="zui-table-header">
                 <table class="zui-table table-width50">
                     <thead>
                     <tr>
                         <th class="cell-m">
                             <div class="zui-table-cell cell-m "><span>序号</span></div>
                         </th>
                         <th>
                             <div class="zui-table-cell cell-s "><span>患者姓名</span></div>
                         </th>
                         <th>
                             <div class="zui-table-cell cell-s "><span>类型</span></div>
                         </th>
                         <th>
                             <div class="zui-table-cell cell-s"><span>发起时间</span></div>
                         </th>
                         <th>
                             <div class="zui-table-cell cell-s"><span>会诊时间</span></div>
                         </th>
                         <th>
                             <div class="zui-table-cell cell-s"><span>状态</span></div>
                         </th>
                         <th>
                             <div class="zui-table-cell cell-m"><span>操作</span></div>
                         </th>
                     </tr>
                     </thead>
                 </table>
             </div>
             <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                 <table class="zui-table table-width50">
                     <!-- v-if="jsonList.length!=0" -->
                     <tbody>
                     <tr :tabindex="$index" v-for="(item, $index) in 22"
                         :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex},$index%2==0?'errorBg':'']"
                         @mouseenter="hoverMouse(true,$index)"
                         @mouseleave="hoverMouse()"
                         @click="checkSelect([$index,'some','jsonList'],$event)"
                         @dblclick="doPop($index)"
                         class="tableTr2">
                         <td class="cell-m">
                             <div class="zui-table-cell cell-m">
                                 {{$index}}
                             </div>
                         </td>
                         <td>
                             <div class="zui-table-cell cell-s ">
                                 李浩然
                             </div>
                         </td>
                         <td>
                             <div class="zui-table-cell cell-s  color-2cb261 ">
                                 机型会诊
                             </div>
                         </td>
                         <td>
                             <div class="zui-table-cell cell-s">
                                 2017/12/12
                             </div>
                         </td>
                         <td>
                             <div class="zui-table-cell color-f38d4f cell-s">
                                 2017/12/12
                             </div>
                         </td>
                         <td>
                             <div class="zui-table-cell cell-s" :class="$index%2==0?'colr-ff6555':'color-2cb261'">
                                 未确认
                             </div>
                         </td>
                         <td>
                             <div class="zui-table-cell  cell-m">
                                 <i class="iconfont icon-iocn12 icon-hover"></i>
                             </div>
                         </td>
                     </tr>
                     </tbody>
                 </table>
                 <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
             </div>
         </div>
     </div>
     <div style="border: 1px solid #eeeeee;width: 40%">
             <div class="bottom-right">
                 <div class="count-title">
                     会诊流程监管
                 </div>
                 <div class="count-name">患者:李浩然</div>
                 <div class="flex-container flex-jus-c flex-align-c" style="height: calc(100% - 85px)">
                 <div class="count-process">
                     <div class="count-start1 fl">
                         <!--已经进行的流程状态样式start-ys1 color-cf0,未进行start-ws1 color-c42-->
                         <span class="start-ys1 color-cf0">检验科发起</span>
                         <span class="start-text">2018/12/12 12:55</span>
                     </div>
                     <div class="count-start2 fl">
                         <!--已经进行的流程状态样式start-ys2 color-cf0,未进行start-ws2 color-c42-->
                         <span class="start-ys2 color-cf0 text-indent-f40">办公护士接收</span>
                         <span class="start-text start-text-position">2018/12/12 12:55</span>
                     </div>
                     <div class="count-start3 fl">
                         <span class="start-overtime">超时</span>
                         <!--已经进行的流程状态样式start-ys3 color-cf0,未进行start-ws3 color-c42-->
                         <span class="start-ys3 color-c42 text-indent-f40">办公护士接收</span>
                         <span class="start-text start-text-position1">2018/12/12 12:55</span>
                     </div>
                     <div class="count-start4 fl">
                         <!--已经进行的流程状态样式start-ys4 color-cf0,未进行start-ws4 color-c42-->
                         <span class="start-ws6 color-c42  ">医生接收</span>
                         <span class="start-text start-text-position2">2018/12/12 12:55</span>
                     </div>
                 </div>
             </div>
         </div>
     </div>
 </div>
</div>
<script src="hz.js" type="text/javascript"></script>
</body>
</html>
