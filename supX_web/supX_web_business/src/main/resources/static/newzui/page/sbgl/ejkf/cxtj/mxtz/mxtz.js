    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
        data:{
            jsonList: [],
            yfkfList: [],
            yfkf: 0, //药房库房信息
            qShow:true,
            queryType: false,
            searchWord: '', //查询关键词
            slType: '',
            ypSelected: {}, //单击选中的药品
            param: {
                'page': 1,
                'rows': 10,
                'yfbm': '',
                'fsbm':'0',
                'beginrq': null,
                'endrq': null,
                'parm': '',
            },
            fsList:{
                "0":'全部',
                "1":'明细'
            },
        },
        mounted:function (){
            this.getKf()
        },
        methods:{
            getData: function() {
                if(this.queryType!=0){
                    this.qShow=false;
                }else{
                    this.qShow=true;
                }
                this.param.yljgbm = null;
                //设置库房
                if(this.param.yfbm =='') {
                    malert('请选择库房','top','defeadted');
                    return;
                }
                if(this.queryType) {
                    this.param.sort = 'zl';
                } else {
                    this.param.sort = '';
                }
                //日期判断
                if(this.fDate(new Date(), 'date') === this.param.endrq) {
                    this.slType = 'dqsl'
                } else {
                    this.slType = 'jysl'
                }
                $.getJSON("/actionDispatcher.do?reqUrl=YfbCxtjAll&types=mxtz&parm=" + JSON.stringify(this.param), function(json) {
                    if(json.a == "0") {
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.jsonList = json.d.list;
                    }
                });
            },
            getKf: function() {
                //库房列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
                    function(data) {
                        if(data.a == 0) {
                            wrapper.yfkfList = data.d.list;
                            Vue.set(wrapper.param,'yfbm',wrapper.yfkfList[0].yfbm);
                            wrapper.getData();
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
                //获取列表
            },

            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "yfbm":
                        Vue.set(this.barContent, 'yfbm', val[0]);
                        Vue.set(this.barContent, 'yfmc', val[4]);
                        wrapper.getData();
                        break;
                    case "fsbm":
                        Vue.set(this.barContent, 'fsbm', val[0]);
                        Vue.set(this.barContent, 'fsmc', val[4]);
                        if(val[0]==0){
                            this.qShow=true;
                        }else{
                            this.qShow=false;
                        }
                        wrapper.getData();
                        break;
                    default:
                        break;
                }
            },


            exportMxtz: function() {
                //设置行数
                this.param.rows = 20000;
                //设置医疗机构编码
                this.param.yljgbm = jgbm;
                //数据查询参数
                if(this.barContent.kfbm == '') {
                    malert('请选择库房','top','defeadted');
                    return;
                }
                if(this.queryType) {
                    this.param.sort = 'zl';
                } else {
                    this.param.sort = '';
                }
                //日期判断
                if(this.fDate(new Date(), 'date') === this.param.endrq) {
                    this.slType = 'dqsl'
                } else {
                    this.slType = 'jysl'
                }
                //设置库房
                wrapper.param.kfbm = wrapper.barContent.kfbm;
                //准备地址
                var url = "/actionDispatcher.do?reqUrl=YfbCxtjAll&types=exportMxtz&parm=" +
                    JSON.stringify(this.param);
                //组合地址
                this.url = (window.location.protocol + '//' + window.location.host) + url;
                //打开下载页
                console.log(this.url);
                window.location = this.url;
            },


        }
    });

    laydate.render({
        elem: '.todate'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            wrapper.param.beginrq = value.slice(0,10);
            wrapper.param.endrq =value.slice(13,23);
        }
    });



