(function () {
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,done:function (value,data) {
        }
    });

var brerList=new Vue({
    el:'#brerList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data:{
        title:'指标名称指标名称指',
        params:{
            zxsb:'',
            zklx:'1',
            bah:'',
        },
        jysbList:'',
        getsblist:'',
        getsblistyx:'',
        pdxz:{
            lx:'',
            bah:'',
            brxm:'',
            xb:'',
            nl:'',
            ksbm:'',
            ksmc:'',
            sqys:'',
            sqysxm:'',
            cwh:'',
            lczd:'',
            jyxm:'',
            jyxmmc:'',
            ybmc:'',
            yblx:'',

            ybbm:'',
            sqrq:'',
            fyje:'',
            jyxh:'',
            zxsb:'',
            zxsbmc:'',
            cyrq:'',
            ybhsrq:'',
            sjsj:'',
            bz:'',
            bbbh:'',
            jzbz:''
        }
    },
    created:function () {
        this.jysb()

    },
    watch:{
        'params.zxsb':function () {
        	this.getsbbm();
        },
        'params.zklx':function () {
        	this.getsbbm();
        },
    },
    methods:{
        baocun:function () {
            var json='{"list":' + JSON.stringify(this.getsblist.yx) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=saveZkxm',json).then(
                function(data) {
                    console.log(data);
                    if(data.body.a==0){
                        //成功回调提示
                        malert('保存成功','top','success');
                        brerList.getsbbm();
                    }else{
                        malert(data.body.c,'top','success');
                    }

                },
                function(error) {
                    malert(error,'top','success');
                });
        },
        scall:function () {
            if(this.isChecked.length==0){
                malert("请选择需要删除的项目" + json.c,'top','defeadted');
            }else  {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        this.deletexm(this.getsblist.yx[i])
                    }
                }

            }
        },
        jysb: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                if (json.a == 0) {
                    brerList.jysbList = json.d.list;
                    brerList.params.zxsb= json.d.list[0].sbbm;
                    brerList.getsbbm()
                } else {
                    malert("获取申请检验设备失败" + json.c,'top','defeadted');
                    return false;
                }
            });
        },
        getsbbm:function () {
            var par={
                zklx:this.params.zklx,
                sbbm:this.params.zxsb,
                bah:this.params.bah,
            }
            $.getJSON("/actionDispatcher.do?reqUrl=LisZkgl&types=queryAllZkxm&yq="+JSON.stringify(par), function (json) {
                if (json.a == 0) {
                    brerList.getsblist = json.d;
                    brerList.getsblistyx = json.d.yx;
                } else {
                    malert("获取设备编码失败" + json.c,'top','defeadted');
                    return false;
                }
            });
        },
        getchild:function (list) {
            list.zklx=this.params.zklx
            var json='{"list":' + JSON.stringify([list]) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=insertBatch4Zkxm',json).then(
                function(data) {
                    console.log(data);
                    if(data.body.a==0){
                        //成功回调提示
                        malert('新增成功','top','success');
                        brerList.getsbbm();
                    }else{
                        malert(data.body.c,'top','success');
                    }

                },
                function(error) {
                    malert(error,'top','success');
                });
        },
        deletexm:function (list) {
            var json='{"list":' + JSON.stringify([list]) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=deleteBatch4Zkxm',json).then(
                function(data) {
                    console.log(data);
                    if(data.body.a==0){
                        //成功回调提示
                        malert('删除成功','top','success');
                        brerList.getsbbm();
                    }

                },
                function(error) {
                    malert(error,'top','success');
                });
        }
    },
})
    // var pop=new Vue({
    //     el:'#pop',
    //     mixins: [dic_transform, baseFunc, tableBase],
    //     data:{
    //         isShowpopL:false,
    //         isTabelShow:false,
    //         isShow:false,
    //         title:'',
    //         name:'',
    //     },
    //     methods:{
    //         //确定删除
    //         delOk:function () {
    //             pop.isShowpopL=false;
    //             pop.isShow=false;
    //             malert('删除成功','top','success');
    //             // event.currentTarget.remove();
    //         }
    //     }
    // });

})()
