
var d = new Date();
var str = d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate();


var evaluateTop=new Vue({
    el:'.evaluate-top',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data:{
        mlShow:false,
        evAdShow:true,
        list:[
            {
            text:'第一天',
            times:str
            }
        ],


    },
    methods:{
        //初始加载
        initVal:function () {
            this.defaultNumber=JSON.parse( sessionStorage.getItem('ycglitem'));
            //defaultNumber:0 从ycgl跳转来可编辑
            if(this.defaultNumber==0){
                $('.jbxx>input').attr('disabled',false)

            }
            //评估查看ycgl 不可编辑
            if(this.defaultNumber==1){
                $('.jbxx>input').attr('disabled',true)
                this.mlShow=false;
                this.evAdShow=false;
            }
        },
       addMore: function() {
           evaluateTop.list.push({text:'第一天',times:str});
           var len=evaluateTop.list.length;
           console.log(len);

           if(len>=7){
           this.mlShow=true;

           }
        },
       //添加一行
        addYh(){
            //方法同门诊医生站dzcf.html新增一行方法相同
            malert('点击当前','top','success')
        },
        getData:function () {
            malert('点击当前','top','success')
        },
        LeftRoll:function () {
            $('.evaluate-list').css({

            })
        },
        RightRoll:function () {

        }

    }

});













var evalutateContent=new Vue({
    el:'.evaluate-content',
  mixins: [dic_transform, tableBase, baseFunc, mformat,scrollOps],
    data:{
        popContent:{},
        ksList:[],//科室
        zdList:[],//诊断
        ddShow:true,//低度风险
        zdShow:true,//中度风险
        gdShow:true,//高度风险
        dzShow:false,//低中显
        zgShow:false,//中高显
        blShow:false,//不良事件 压疮转归默认隐藏
        ycShow:false,//压疮转归
        dqShow:true,
        total:'0',
        checked:false,
        checkedNames:[],
        rank:'',
    },
    mounted:function(){
        this.grade();
        this.getName();
        // this.$nextTick(() => {
        //     this.getName();
        // })

    },

    methods:{

        //获取下拉框数据方式
        getKsData: function(){
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm", function(json) {
                evalutateContent.ksList = json.d.list;
                //
                evalutateContent.zdList = json.d.list;
            });

        },
        getName:function () {

        },
        //评分计算
        grade:function () {
            $("input[type=radio]").change(function(){
                var sum = 0;
                $(".jbxx-box").find("input[type='radio']:checked").each(function (){
                sum += parseInt($(this).val());
                });
                evalutateContent.total=sum;
               if(sum>=15 && sum<=18){
                   evalutateContent.rank='低度风险（15-18分）'
                   evalutateContent.zdShow=false;
                   evalutateContent.gdShow=false;
                   evalutateContent.zgShow=false;
                   evalutateContent.ddShow=true;
                   evalutateContent.dzShow=true;
               }if(sum>=13 && sum<=14){
                   evalutateContent.rank='中度危险（13-14分）'
                    evalutateContent.ddShow=false;
                    evalutateContent.zdShow=true;
                    evalutateContent.dzShow=true;
               }if(sum>=10 && sum<=12){
                    evalutateContent.rank='高度危险（10-12分）'
                    evalutateContent.ddShow=false;
                    evalutateContent.dzShow=false;
                    evalutateContent.zdShow=false;
                    evalutateContent.gdShow=true;
                    evalutateContent.zgShow=true;
               }if(sum<=9){
                    evalutateContent.rank='非常危险（≤9分）'
                    evalutateContent.ddShow=true;
                    evalutateContent.zdShow=true;
                    evalutateContent.dzShow=true;
                    evalutateContent.zgShow=true;
                    evalutateContent.gdShow=true;
               }

            });
            },
        //不良事件关闭
        blClose:function () {
            this.blShow=false;
        },
      //
        dqClose(){
            this.dqShow=false;
        }

    }

})
var tool=new Vue({
    el:'.zui-table-tool',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data:{

    },
    methods:{
        clickYc:function () {
            evalutateContent.ycShow=true;
        },
        //不良事件
        clickSb:function () {
            evalutateContent.blShow=true;
        },
        //确定提交成功返回上一级
        clickOk:function () {
            malert('提交成功','top','success');
            this.topClosePage('page/hsz/hlyw/ycgl/evaluate/evaluate.html','page/hsz/hlyw/ycgl/ycgl.html');
        }

    }
})
laydate.render({
    elem: '.times',
    type: 'date',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
    }
});
laydate.render({
    elem: '.times1',
    type: 'date',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
    }
});
laydate.render({
    elem: '.times2',
    type: 'date',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
    }
});
evaluateTop.initVal();
evalutateContent.getKsData();

