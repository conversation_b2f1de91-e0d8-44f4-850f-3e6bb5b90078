/**
 * Created by mash on 2017/9/7.
 */
var header = new Vue({
    el: '#header',
    mixins: [tableBase, baseFunc, dic_transform,],
    data: {
        toDay: new Date(),
        selectDay: new Date(),
        pddidrem: [],
        ghKsList: [],
        isEdit: false,
        json: {},
        caqxContent: {},
        mph:''
    },
    created: function () {
        this.readyData()
    },
    methods: {
        getCsqx: function () {
            var parm = {
                "ylbm": 'N050014002',
                "ksbm": encodeURIComponent(this.json.ksbm)
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N05001400201": //科室排班是否有修改权限  0=不允许修改，1=允许修改
                                if (csjson.csz) {
                                    header.caqxContent.N05001400201 = csjson.csz;
                                }
                                break;
                        }
                    }
                } else {
                    malert("参数权限获取失败" + json.c, 'top', 'defeadted');
                }
            });
        },
        resultzcChange: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.$forceUpdate()
            scheduling.getSec()
            this.getPbInfo()
            // this.getCsqx()
            option.getPbsjap()
        },
        //公用查询
        readyData: function () {
            var dg = JSON.stringify({"sort": "ksmc"})
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify({"ghks": "1"}) + "&dg=" + dg, function (json) {
                if (json.a == 0) {
                    header.ghKsList = json.d.list;
                    header.ghKsList.unshift({ksbm: '%', 'ksmc': '全院'});
                    header.json.ksbm = json.d.list[0].ksbm
                    scheduling.getInfo();
                    header.getCsqx()
                } else {
                    malert(types + "查询失败", 'top', 'defeadted');
                }
            });
        },
        //编辑状态
        edit: function () {
            if (header.caqxContent.N05001400201 == '1') {
                option.isEdit = this.isEdit = !this.isEdit;
                if (this.isEdit) {
                    $('.addPb span').show();
                } else {
                    $('.addPb span').hide();
                }
            }
        },
        //保存状态
        baocun: function () {
            this.isEdit = false;
            $('.addPb span').hide();
        },

        //重置排班
        resetMove: function () {
            var json = {pbbid: this.pddidrem};
            this.$http.post('/actionDispatcher.do?reqUrl=New1GhglPbglYspb&types=deleteOne&', JSON.stringify(json)).then(function (data) {
                if (data.body.a == 0) {
                    for (var i = 0; i < this.pddidrem.length; i++) {
                        $('#' + i).remove()
                    }
                } else {
                    malert("排班删除失败" + data.body.c, 'top', 'defeadted');
                }
            }, function (error) {
                malert(error);
            });
        },

        // 跳转到上一周
        prvWeek: function () {
            this.selectDay = new Date(1000 * 60 * 60 * 24 * (-7) + this.selectDay.getTime());
            this.getPbInfo();
        },

        // 跳转到下一周
        nextWeek: function () {
            this.selectDay = new Date(1000 * 60 * 60 * 24 * (7) + this.selectDay.getTime());
            this.getPbInfo();
        },

        // 获取排班的信息（根据是否传参来判断是增加的还是页面初始化查询）
        getPbInfo: function (time, ryBm, id) {
            if (this.isEdit) {
                $(".addPb span").show();
            } else {
                $(".addPb span").hide();
            }
            if (time == null) $(".addPb").remove(); // 为了体验更流畅
            var week = this.selectDay.getDay();
            var first = null;
            var end = null;
            if (week == 0) {
                week = 7;
                first = new Date(this.selectDay.getTime());
                first = first.setDate(first.getDate() + 1);
                first = new Date(first);
                end = new Date(1000 * 60 * 60 * 24 * week + this.selectDay.getTime());
            } else {
                first = new Date(1000 * 60 * 60 * 24 * (1 - week) + this.selectDay.getTime());
                end = new Date(1000 * 60 * 60 * 24 * (7 - week) + this.selectDay.getTime());
            }

            var firstDate = first.getFullYear() + '-' + (first.getMonth() + 1) + '-' + (first.getDate());
            var endDate = end.getFullYear() + '-' + (end.getMonth() + 1) + '-' + end.getDate();
            var str_param = {
                ksbm: encodeURI(header.json.ksbm),
                page: 1,
                rows: 100,
                sort: 'sbsj',
                order: 'asc',
                beginrq: firstDate + ' 00:00:00',
                endrq: endDate + ' 00:00:00'
            };
            setTimeout(function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglPbglYspb&types=query&dg=" + JSON.stringify(str_param), function (json) {
                    scheduling.pbList = json.d.list;
                    var pbList = json.d.list;
                    var date = null;
                    for (var i = 0; i < pbList.length; i++) {
                        header.pddidrem.push(pbList[i].pbbid)
                        date = new Date(pbList[i].sbsj);
                        var el = $('#' + pbList[i].rybm).find('td').eq(date.getDay() - 1);
                        var bg = null;
                        var color = null;
                        for (var j = 0; j < option.optionList.length; j++) {
                            if (pbList[i].bcfabm == option.optionList[j].bcfabm) {
                                bg = option.optionList[j].colour;
                                color = option.optionList[j].fontcolour;
                            }
                        }
                        if (time) {
                            if (time == date.getDate() && ryBm == pbList[i].rybm && id == pbList[i].bcfabm) {
                                el.append("<div id='" + pbList[i].pbbid + "' data-type='" + pbList[i].bcfabm + "' class=' addPb' style='background: " + bg + ";color: " + color + "'>" + pbList[i].bcfamc +
                                    "<br>门牌号：" + (pbList[i].mph?pbList[i].mph:'无') + "<span style='display: block'  onclick=\"remove('" + pbList[i].pbbid + "')\" class='wb-close-mini'></span></div>");
                            }
                        } else {
                            if (scheduling.isEdit) {
                                el.append("<div id='" + pbList[i].pbbid + "' data-type='" + pbList[i].bcfabm + "' class=' addPb' style='background: " + bg + ";color: " + color + "'>" + pbList[i].bcfamc +
                                    "<br>门牌号：" + (pbList[i].mph?pbList[i].mph:'无') + "<span style='display: block' onclick=\"remove('" + pbList[i].pbbid + "')\" class='wb-close-mini'></span></div>");
                            } else {
                                // if(el[0].childNodes.length>=1){
                                //     for (var j = 0; j < el[0].childNodes.length; j++) {
                                //         // 判断是否已经存在同类型的排班方案
                                //         if (pbList[i].pbbid == el[0].childNodes[j].id) {
                                //             malert('已经有同类型的排班了。', 'top', 'defeadted');
                                //             break;
                                //         }
                                //     }
                                // }else{
                                el.append("<div id='" + pbList[i].pbbid + "' data-type='" + pbList[i].bcfabm + "' class=' addPb' style='background: " + bg + ";color: " + color + "'>" + pbList[i].bcfamc +
                                    "<br>门牌号：" + (pbList[i].mph?pbList[i].mph:'无') + "<span  onclick=\"remove('" + pbList[i].pbbid + "')\" class='wb-close-mini'></span></div>");
                                // }
                            }
                        }
                    }
                    header.$forceUpdate()
                    scheduling.setToDayBg();
                });
            }, 200);
        },
    },
});

//排班详细列表信息
var scheduling = new Vue({
    el: '.scheduling',
    mixins: [mformat],
    data: {
        my: {},
        currentDate: '',
        selectDay: header.selectDay,
        person: {},
        isEdit: false,
        pbList: [],
        ifClick: false,
        mph: ''
    },
    methods: {
        //保存排班
        savePb: function (evt, num) {//点击调用排班方法 evt为当前元素，num为第几个
            console.log(evt)
            if (!header.isEdit) return malert('请先点击编辑按钮', 'top', 'defeadted')
            if (evt.target.nodeName == 'TD') {
                if (this.ifClick) {
                    return false;
                }
                this.ifClick = true;

                option.stopDrag(option.Indexevent, option.numIndex, num, evt);
            }
            // if(!scheduling.ifClick){
            //     malert("请勿重复点击！","top","defeadted");
            //     return;
            // }
            // if(option.numIndex != undefined){
            //     scheduling.ifClick = false;
            // }else{
            //     scheduling.ifClick = true;
            // }
            // if (option.numIndex != undefined) {
            // }
        },

        // 根据当前时间计算这周的时间
        weekToDay: function (selectDay, week) {
            var day = new Date(1000 * 60 * 60 * 24 * (week - header.selectDay.getDay()) + header.selectDay.getTime());
            return day.getMonth() + 1 + '月' + day.getDate() + '日';
        },


        // 获取当前操作员的信息
        getInfo: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetUserInfoAction', function (data) {
                scheduling.my = data.d;
                scheduling.getSec();
                header.getPbInfo();
                option.getPbsjap();
            })
        },

        // 获取当前科室需要排班的人员
        getSec: function () {
            var first = new Date(1000 * 60 * 60 * 24 * (1 - header.selectDay.getDay()) + header.selectDay.getTime());
            var firstDate = first.getFullYear() + '-' + (first.getMonth() + 1) + '-' + first.getDate();
            $.getJSON("/actionDispatcher.do?reqUrl=New1GhglPbglYspb&types=queryRybm&ksbm=" + encodeURI(header.json.ksbm) + "&firstDate=" + firstDate, function (json) {
                if (json.a == 0) {
                    scheduling.person = json.d.list;
                } else {
                    malert("排班信息查询失败：" + json.c, ' ', 'defeadted')
                }
            });
        },

        // 这里延迟来加载今天的背景色
        setToDayBg: function () {
            setTimeout(function () {
                if (header.toDay.toString() == header.selectDay.toString()) {
                    var a = header.toDay.getDay() - 1;
                    for (var i = 0; i < $('.toDayTable tr').length; i++) {
                        $('.toDayTable tr').eq(i).find('td').eq(a).addClass('toDayBg');
                    }
                } else {
                    $('.toDayTable td').removeClass('toDayBg');
                }
            }, 100);
        },

        //删除排班信息
        remove: function (id) {
            var json = {pbbid: id};
            this.$http.post('/actionDispatcher.do?reqUrl=New1GhglPbglYspb&types=deleteOne&', JSON.stringify(json)).then(function (data) {
                if (data.body.a == 0) {
                    $('#' + id).remove();
                    malert("删除成功", 'top', 'success');
                } else {
                    malert("排班删除失败" + data.body.c, 'top', 'defeadted');
                }
            }, function (error) {
                malert(error);
            });
        }
    }
});
//获取排班方式
var option = new Vue({
    el: '.option',
    mixins: [tableBase, mformat, dragFun],
    data: {
        btuList: [],
        isEdit: false,
        math: 0,
        numIndex: undefined,
        Indexevent: '',
        tdOneThis: '',
        date: '',
        pbName: null,
        num: null,
        optionList: []
    },
    created: function () { // 这里在创建根实例完成后为将要被拖拉的元素初始化循环的长度
        for (var i = 0; i < this.optionList.length; i++) {
            this.btuList.push(1);
        }
    },
    methods: {
        biegDrag: function (event, n, t) {
            if (!option.isEdit) return malert('请先点击编辑按钮', 'top', 'defeadted')
            this.num = n
            this.numIndex = n
            this.Indexevent = event
            this.pbName = t
        },


        stopDrag: function (event, index, num, evt) {
            this.endDrag(event);
            if (index != undefined) {
                for (var j = 0; j < $(evt.currentTarget).find('div').length; j++) {
                    // 判断是否已经存在同类型的排班方案
                    if (option.optionList[index]['bcfabm'] == $(evt.currentTarget).find('div').eq(j).attr('data-type')) {
                        malert('已经有同类型的排班了。', 'top', 'defeadted');
                        scheduling.ifClick = false;
                        return false;
                    }
                }
                console.log(scheduling.ifClick)
                var week = $(evt.currentTarget).attr('week');
                var date = new Date(1000 * 60 * 60 * 24 * (week - header.selectDay.getDay()) + header.selectDay.getTime());
                this.savePb(index, date, $(evt.currentTarget).attr('ryId'));
            } else {
                scheduling.ifClick = false;
                scheduling.$forceUpdate()
                malert('没有添加新的排班数据', 'top', 'defeadted');
            }
            // this.numIndex=index
            //var color = event.target.style['background-color'];
            // this.pbName = event.target.innerText;
            // event.target.remove();
            // Vue.set(this.btuList, index, this.btuList[index]+1);
            //var td = $(evt);
            // for(var i = 0; i < td.length; i++){
            //  var tdOne = $(td);
            // this.tdOneThis = $(td[i])['0'];
            // if(num==$(td[i])){
            // if(tdOne.offsetLeft  < event.clientX && event.clientX < tdOne.offsetLeft + 50 + tdOne.offsetWidth){
            //     if(tdOne.offsetTop + 100 < event.clientY && event.clientY < tdOne.offsetHeight + tdOne.offsetTop + 242){

            // }
            //}
            // }
            // }
        },

        // 拖拉完成后保存当前排班（第几个排班方案，排班到哪一天，排班到哪一个人）
        savePb: function (index, day, ryBm) {
            if (!header.mph) return malert('请选择门牌号', 'top', 'defeadted')
            var json = {
                ksbm: header.json.ksbm,
                mph: header.mph,
                rybm: ryBm,
                bcfabm: this.optionList[index]['bcfabm'],
                xghs: this.optionList[index]['xhs'],
                bcfamc: this.pbName,
                sbsj: this.fDate(day, 'date') + ' ' + this.optionList[index]['sbsj'] + ':00',
                xbsj: this.fDate(day, 'date') + ' ' + this.optionList[index]['xbsj'] + ':00',
                sfycdcr: '0',
                pbsj: this.fDate(day, 'date') + ' ' + this.optionList[index]['sbsj'] + ':00'
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1GhglPbglYspb&types=insert', JSON.stringify(json)).then(function (data) {
                if (data.body.a == 0) {
                    header.getPbInfo(day.getDate(), ryBm, json.bcfabm);
                    console.log(scheduling.ifClick)
                    scheduling.ifClick = false;
                    // malert("排班增加成功!", 'top', 'success');
                    // setTimeout(function(){
                    //     scheduling.ifClick = true;
                    // },200);
                } else {
                    scheduling.ifClick = false;
                    malert("排班保存失败" + data.body.c, 'top', 'defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },

        //查询排班时间段划分
        getPbsjap: function () {
            var json = {
                syfw: encodeURI(header.json.ksbm)
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBcfa&types=query&dg=" + JSON.stringify(this.param) + "&json=" + JSON.stringify(json), function (json) {
                option.optionList = json.d.list;
                scheduling.optionList = json.d.list;
            });
        },
    },
});

window.remove = function (id) {
    scheduling.remove(id);
};
