var content=new Vue({
    el:'.content',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        beginrq:'',
        endrq:'',
        ypContent:{},
    },
    created:function(){

    },
    mounted:function(){
        this.ajaxChart();
        this.AjaxChartPie();
        //初始化检索日期！为今天0点到今天24点
        var myDate=new Date();
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date');
        this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24, 'date');
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#dateStart',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.beginrq = value;
                } else {
                    content.beginrq = '';
                }
                //获取一次列表
            }
        });
        laydate.render({
            elem: '#dateEnd',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.endrq = value;
                } else {
                    content.endrq = '';
                }
                //获取一次列表
            }
        });
    },
    updated:function(){
        this.ajaxChart();
        this.AjaxChartPie();
        changeWin()
    },
    methods:{
        AjaxChartPie:function(){
            var arrObj=[];
            var textArr=['合格率','不合格率'];
            var bum=[4,5];
            var myChart = echarts.init(document.getElementById('pieCanvas'));
            for(var i=0;i<2;i++){
                var num=parseInt(Math.random()*20);
                // '%\n'+textArr[i]
                arrObj.push({value:num, name:num,selected:true})
            }
            option = {
                color:['#1abc9c','#c4cccb'],
                title : {
                    text: '科室合格率占比',
                    x:'center',
                    bottom:'2%'
                },
                tooltip : {
                    trigger: 'item',
                    formatter: "{a} <br/>{b} : {c} ({d}%)"
                },
                series : [
                    {
                        label:{
                            normal:{
                                show:true,
                                formatter:function (value) {
                                    return value.percent+'%\n'+textArr[value.dataIndex]
                                }
                            },
                        },
                        name: '科室合格率占比',
                        type: 'pie',
                        selectedOffset:2,
                        radius: [0, '70%'],
                        center: ['50%', '43%'],
                        data:arrObj,
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            if (option && typeof option === 'object') {
                myChart.setOption(option,true);
            }
        },
        edit:function(){

        },
        ajaxChart:function (view) {
            var dataArrName=[];
            var dataArr=[];
            for(var i=0;i<40;i++){
                dataArrName.push('李\n浩');
                dataArr.push(parseInt(10*Math.random()))
            }
            var myChart = echarts.init(document.getElementById('canvas'));
            option = {
                title: {
                    text: 'sdsdsd',
                    x:'center',
                    y:'top',
                },
                color: ['#02a9f5'],
                tooltip : {
                    position:function (point, params, dom, rect, size) {
                        $(dom).addClass('topshBefter');
                        return [point[0]+5,point[1]-10]
                    },
                    enterable:true,
                    trigger: 'axis',
                    axisPointer : {
                        type : 'shadow'
                    },
                    formatter:function (params,ticket,callback ) {
                        return '<div class="topsh">合格率：<span class="font12">'+params[0].value+'%</span>\n<p class="font12">不合格患者：<span class="text-decoration padd-r-10" onclick="OpenName()">张东</span><span class="text-decoration padd-r-10">张东</span><span class="text-decoration padd-r-10">张东</span><span class="text-decoration padd-r-10">张东</span><span class="text-decoration padd-r-10">张东</span></p></div>'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis : [
                    {
                        type : 'category',
                        data : dataArrName,

                        axisTick: {
                            alignWithLabel: true
                        },
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92',
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#fff',
                            },
                        },
                        splitLine:{
                            show:true,
                            lineStyle:{
                            },
                        },
                    },
                ],
                yAxis : [
                    {
                        splitNumber:10,
                        type : 'value',
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92'
                            },
                            borderColor:'#8b8f92',
                            formatter:function(value, index){
                                return value+'%'
                            },
                        },
                        axisLine:{
                            show:false,
                            lineStyle:{
                                color:'#fff'
                            },
                        },
                        axisTick:{

                        },
                    },
                ],
                series : [
                    {
                        name:'',
                        type:'bar',
                        barWidth: '11',
                        animationDuration: function (idx) {
                            return idx * 100;
                        },
                        data:dataArr,
                        label:{

                            color:'#b6babb'
                        },

                    },

                ]
            };
            if (option && typeof option === 'object') {
                myChart.setOption(option,true);
            }

        }
    },
});
var brRyList=new Vue({
    el:'#brRyList01',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{

    },
    methods:{

    },
    mounted:function () {

    },
});
function OpenName() {
    alert(1111)

}