(function () {
    var wrapper=new Vue({
        el:'.panel',
        data:{
            index:1,
            pop:{},
            searchAll:'',
            updateList:'',
            delList:'',
        },

        methods:{
            addclass:function (num) {
                this.index=num
            },
            show:function () {
                pop.isShow=true;
            },
            add:function () {
                pop.isShow=true
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                pop.title='新增检验样本'
            },
            save:function(){
            	if(wrapper.updateList.length == 0){
            		malert('数据未有改变','top','defeadted');
            	}else{
            		//保存操作 
            		var data = '{"list":'+ JSON.stringify(wrapper.updateList) +'}';
                	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmYbUpdate',data).then(function(json) {
    	       			 console.log(json.body);
    	       			 if(json.body.a == 0){
    	       				jyx.getData();
              			malert('保存成功！！','top','success');
              			$(".side-form-bg").removeClass('side-form-bg')
              			$(".side-form").addClass('ng-hide');
              			jyx.getData();
    	       			 }else{
    	            			malert('保存失败！！','top','defeadted');
    	            	 }
    	       		 });
            	}
            },
            refresh:function(){
            	jyx.getData();
            },
            del:function(){
            	if(jyx.isChecked.length == 0){
            		malert('请选择要停用的方法','top','defeadted');
            	}else{
            		wrapper.delList = [];
            		for(var i = 0; i < jyx.isChecked.length ; i++){
            			if(jyx.isChecked[i]){
            				wrapper.delList.push(jyx.jsonList[i]);
            			}
            		}
            	}
            	
            	if(wrapper.delList.length == 0){
            		malert('请选择要停用的方法','top','defeadted');
            	}else{
            		//批量停用
            		//保存操作 
            		var data = '{"list":'+ JSON.stringify(wrapper.delList) +'}';
                	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmYbDelete',data).then(function(json) {
    	       			 console.log(json.body);
    	       			 if(json.body.a == 0){
    	       				jyx.getData();
              			malert('删除成功！！','top','success');
              			$(".side-form-bg").removeClass('side-form-bg')
              			$(".side-form").addClass('ng-hide');
              			jyx.getData();
    	       			 }else{
    	            			malert('删除失败！！','top','defeadted');
    	            	 }
    	       		 });
            	}
            }
        },
        watch:{
        	'searchAll':function(){
        		jyx.param.parm = wrapper.searchAll;
        		jyx.getData();
        	}
        }
    });
    var jyx=new Vue({
        el:'#utable1',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
        	isChecked:[],
        	totlePage:'',
        	jsonList:'',
        	param:{
        		parm:'',
        		rows:10,
        		page:1,
        	}
        },
        methods:{
            show:function () {
                pop.isShow=true;
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                pop.title='编辑检验样本'
            },
            getData:function(){
            	pop.yb.ybmc = '';
            	pop.yb.pydm = '';
            	pop.yb.tybz = '0';
            	jyx.isChecked = [];
            	wrapper.updateList = [];
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmYbSelect&param=" + JSON.stringify(jyx.param), function(json) {
            		console.log(json);
            		if(json.a=="0"){
            			jyx.jsonList = json.d.list;
            			jyx.totlePage = Math.ceil(json.d.total / jyx.param.rows);
            		}
            	});
            },
            change:function(index){
            	jyx.jsonList[index].tybz = jyx.jsonList[index].tybz == '0'?'1':'0';
            	//wrapper.updateList;
            	if(wrapper.updateList.length != 0){
            		for(var i = 0; i <wrapper.updateList.length ; i++){
            			if(wrapper.updateList[i].ybbm == jyx.jsonList[index].ybbm){
            				wrapper.updateList[i].tybz = jyx.jsonList[index].tybz;
            				return;
            			}
            		}
            		wrapper.updateList.push(jyx.jsonList[index]);
            	}else{
            		wrapper.updateList.push(jyx.jsonList[index]);
            	}
            },
        },
    })
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            popContent:{},
            centent:'',
            yb:{
            	pydm:'',
            	ybmc:'',
            	tybz:'0'
            }
        },
        methods:{
            close:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            save:function () {
            	//验证
            	if(pop.yb.ybmc == ''){
            		malert('请输入样本名称！','top','defeadted');
            		return;
            	}
            	//保存
            	var data = JSON.stringify(pop.yb);
            	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmYbInsert',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				jyx.getData();
          			malert('添加成功！！','top','success');
          			// $(".side-form-bg").removeClass('side-form-bg')
          			// $(".side-form").addClass('ng-hide');
          			jyx.getData();
	       			 }else{
	            			malert('添加失败！！','top','defeadted');
	            	 }
	       		 });
                // $(".side-form-bg").removeClass('side-form-bg')
                // $(".side-form").addClass('ng-hide');
            },
            change:function(){
            	pop.ff.tybz = pop.ff.tybz == '0' ? '1':'0';
            }
        },
    });
    // document.onkeydown=function (ev) {
    //     var ev=window.event|| ev
    //     var key=ev.keyCode
    //     if(key==83&& ev.ctrlKey){
    //         return false
    //     }
    // }
    
    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
    	if($(this).val() == '' || $(this).val() == null) {
    		$(this).addClass("emptyError");
    	} else {
    		$(this).removeClass("emptyError");
    	}
    });
    
    jyx.getData();
    
})()