var brzcList001 = new Vue({
    el: '#brzcList001',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        num: 0,
        index: 1,
        Isblfx:false,
        Datanum: 1,
        XmzxdljsonList:[],
        XmzxxxjsonList:[],
        zlFajsonList:[],
        title: '',
        textIndx: 0,
        textDw: ['岁', '月', '周','天'],
        popContent: {
            jdBegindw: '0',
            jdEnddw: '0',
            xmzxdl:'',
        },
    },
    created: function () {
        this.$nextTick(function () {
            three.getDataOne()
            laydate.render({
                elem: '.qysj'
                , trigger: 'click'
                , theme: '#1ab394'
                ,done:function (value,data) {
                    brzcList001.popContent.qysj = value
                }
            });
        })
        this.getXmdl()
        this.getXmxx()
        this.getZlfa()
    },
    filters: {
        formDate: function (value) {
            var d = new Date(value);
            return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
        }
    },
    methods: {
        getZlfa:function () {
            $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysZlfa&types=queryZlfa", function (json) {
                if (json.a == '0') {
                    brzcList001.zlFajsonList = json.d.list;
                    // ybglTablelist.jsonList = json.d.list;
                }
            });
        },
        getXmdl:function () {
            $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysZxdl&types=queryzxdl", function (json) {
                if (json.a == '0') {
                    brzcList001.XmzxdljsonList = json.d.list;
                    // ybglTablelist.jsonList = json.d.list;
                }
            });
        },
        getXmxx:function () {
            $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysZxxl&types=queryzxxl", function (json) {
                if (json.a == '0') {
                    brzcList001.XmzxxxjsonList = json.d.list;
                    // ybglTablelist.jsonList = json.d.list;
                }
            });
        },
        add: function () {
            this.index = 0;
            this.popContent={
                    jdBegindw: '0',
                    jdEnddw: '0',
                    cjrq:new Date(),
                    shrq:new Date()
            },
            this.num = three.num;
            brzcList001.Datatype=0
            switch (three.num) {
                case 0:
                    brzcList001.title = '新增病种'
                    break;
                case 1:
                    if (this.Datanum == 1) {
                        brzcList001.title = '新增一级病目录'
                    } else {
                        brzcList001.title = '新增二级病目录'
                    }
                    break;
            }
        },
        closes: function () {
            this.index = 1
        },
        confirms: function (type) {
            switch (this.num) {
                case 0:
                    var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhys&types=save';
                    this.popContent.syks=this.popContent.syks==true?1:0
                    this.popContent.tybz=this.popContent.tybz==true?1:0
                    break;
                case 1:
                    if (this.Datanum == 1) {
                        var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhysJd&types=save';
                        this.popContent.ryljbm=three.jsonList[three.indexNum].ljbm
                        this.popContent.ryljmc=three.jsonList[three.indexNum].ljmc
                        this.popContent.shbz=this.popContent.shbz==true?1:0
                    } else {
                        var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhysEjjd&types=save';
                        this.popContent.ryjdbm=three.cjsonList[three.indexNum].jdbm
                        this.popContent.ryljbm=three.cjsonList[three.indexNum].ryljbm
                        this.popContent.ryljmc=three.cjsonList[three.indexNum].ryljmc
                        this.popContent.ryjdmc=three.cjsonList[three.indexNum].jdmc
                        this.popContent.shbz=this.popContent.shbz==true?1:0
                    }
                    break;
                case 2:
                    var type=this.Datatype==0?'save':'updateBySh'
                    var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhysHl&types='+type;
                    this.popContent.ryjdbm=three.twojsonList[three.indexNum].ryjdbm
                    this.popContent.ryejjdbm=three.twojsonList[three.indexNum].ejjdbm
                    this.popContent.ryljmc=three.twojsonList[three.indexNum].ryljmc
                    this.popContent.ryljbm=three.twojsonList[three.indexNum].ryljbm
                    this.popContent.kxx=this.popContent.kxx==true?1:0
                    break;
                case 3:
                    this.popContent.ryjdbm=three.datelitem[three.indexNum].ryjdbm
                    this.popContent.ryejjdbm=three.datelitem[three.indexNum].ryejjdbm
                    this.popContent.ryljmc=three.datelitem[three.indexNum].ryljmc
                    this.popContent.ryljbm=three.datelitem[three.indexNum].ryljbm
                    this.popContent.yljgbm=three.datelitem[three.indexNum].yljgbm
                    this.popContent.ypyzsfzdsc=this.popContent.ypyzsfzdsc==true?1:0
                    this.popContent.sfcy=this.popContent.sfcy==true?1:0
                    this.popContent.zhyzbz=this.popContent.zhyzbz==true?1:0
                    var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhysYz&types=save';
                    break;
            }
            this.$http.post(url, JSON.stringify(this.popContent)).then(function (data) {
                    if (data.body.a == 0) {
                        malert(data.body.c, 'top', 'success');
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }

                },
                function (error) {
                    malert(error, 'top', 'defeadted');
                });
        },
        qiehuan: function (index) {
            switch (index) {
                case 0:
                    this.textIndx = this.textIndx + 1 >= this.textDw.length ? 0 : this.textIndx + 1
                    this.popContent.jdBegindw = this.textIndx;
                    break;
                case 1:
                    this.textIndx = this.textIndx + 1 >= this.textDw.length ? 0 : this.textIndx + 1
                    this.popContent.jdEnddw =this.textIndx;
                    break;
            }

        }
    },
})

//    非正常树结构
var three = new Vue({
    el: '.xtmktreediv',
    data: {
        toggone: '/newzui/pub/image/toggle01.png',
        toggtwo: '/newzui/pub/image/toggle02.png',
        toggthree: '/newzui/pub/image/toggle03.png',
        jsonList: [],
        cjsonList: [],
        twojsonList: [],
        datelitem: [],
        datalList: [],
        twotoggle: false,
        threetoggle: false,
        sdsdzzzz: false,
        togShow: false,
        sdsdzzzzaaa: false,
        dateltoggle: false,
        num: 0,
        indexNum: 0,
        aryljbm: {
            ryljbm: '',
            ryjdbm: ''
        },
    },
    created: function () {
        // this.getDataOne()
        this.$nextTick(function () {
            three.getDataOne()
        })
    },
    methods: {
        toggle: function (index,num,numlsit,evnet) {
            $('.tree_text1').removeClass('bold')
            evnet.target.className='bold tree_text1'
            this.num = index
            if (num == 2) {
                ybglTablelist.Datanum = num
                brzcList001.Datanum = num
                ybglTablelist.jsonList = three.twojsonList
            } else if (num == 1) {
                ybglTablelist.Datanum = num
                brzcList001.Datanum = num
                ybglTablelist.jsonList = three.cjsonList
            }
            ybglTablelist.num = index
            brzcList001.index = 1
            switch (index) {
                case 0:
                    if(three.jsonList!=''){
                        ybglTablelist.jsonList = three.jsonList;
                    }
                    break;
                case 2:
                    if(three.datelitem!=''){
                        ybglTablelist.jsonList = three.datelitem;
                    }
                    break;
                case 3:
                    if(three.datalList!=''){
                        ybglTablelist.jsonList = three.datalList;
                    }
                    break;
            }
        },
        getShow: function () {
            this.togShow = !this.togShow
        },
        getDataOne: function () {//路径病重种
            $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhys&types=queryall", function (json) {
                if (json.a == '0') {
                    three.jsonList = json.d.list;
                    // ybglTablelist.jsonList = json.d.list;
                }
            });
        },
        getList: function (item,index,event) {//一阶段查询二阶段
                this.aryljbm.ryljbm = item.ljbm
                three.indexNum=index;
                $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysJd&types=queryall&parm=" + JSON.stringify(this.aryljbm), function (json) {
                    if (json.a == '0') {
                        three.cjsonList = json.d.list;
                        // ybglTablelist.jsonList = json.d.list;
                    }
                });
                $('.Json').hide();
                var JsonImg=$('.JsonImg');
                for(var i=0;i<JsonImg.length;i++){
                    $(JsonImg).eq(i).attr('src',this.toggthree)
                }
            if(event.currentTarget.nextElementSibling.nextElementSibling.style.display=='none'){
                event.currentTarget.nextElementSibling.nextElementSibling.style.display='block'
                event.srcElement.src=this.toggtwo
            }else{
                event.currentTarget.nextElementSibling.nextElementSibling.style.display='none'
                event.srcElement.src=this.toggthree
            }
            // this.twotoggle = !this.twotoggle
        },
        getChild: function (item,index,event) {//二阶段查询项目
                this.aryljbm.ryljbm = item.ryljbm
                this.aryljbm.ryjdbm = item.jdbm
                three.indexNum=index;
                $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysEjjd&types=queryall&parm=" + JSON.stringify(this.aryljbm), function (json) {
                    if (json.a == '0') {
                        three.twojsonList = json.d.list;
                    }
                });
            var JsonImg=$('.CjsonImg');
            for(var i=0;i<JsonImg.length;i++){
                $(JsonImg).eq(i).attr('src',this.toggthree)
            }
            $('.Cjson').hide()
            if(event.currentTarget.nextElementSibling.nextElementSibling.style.display=='none'){
                event.currentTarget.nextElementSibling.nextElementSibling.style.display='block'
                event.srcElement.src=this.toggtwo
            }else{
                event.currentTarget.nextElementSibling.nextElementSibling.style.display='none'
                event.srcElement.src=this.toggthree
            }
            // this.threetoggle = !this.threetoggle
        },
        getChildDatel: function (item,index,event) { //项目查询医嘱
                this.aryljbm.ryljbm = item.ryljbm
                this.aryljbm.ryjdbm = item.ryjdbm
                this.aryljbm.ryejjdbm = item.ejjdbm
                three.indexNum=index;
                $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysHl&types=queryall&parm=" + JSON.stringify(this.aryljbm), function (json) {
                    if (json.a == '0') {
                        three.datelitem = json.d.list;
                    }
                });
            $('.twojson').hide()
            var JsonImg=$('.twojsonImg');
            for(var i=0;i<JsonImg.length;i++){
                $(JsonImg).eq(i).attr('src',this.toggthree)
            }
            if(event.currentTarget.nextElementSibling.nextElementSibling.style.display=='none'){
                event.currentTarget.nextElementSibling.nextElementSibling.style.display='block'
                event.srcElement.src=this.toggtwo
            }else{
                event.currentTarget.nextElementSibling.nextElementSibling.style.display='none'
                event.srcElement.src=this.toggthree
            }
            // this.sdsdzzzz = !this.sdsdzzzz
        },
        datel: function (item,index,event) {//项目查询详情
                this.aryljbm.ryljbm = item.ryljbm
                this.aryljbm.ryjdbm = item.ryjdbm
                three.indexNum=index;
                $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysYz&types=queryall&parm=" + JSON.stringify(this.aryljbm), function (json) {
                    if (json.a == '0') {
                        three.datalList = json.d.list;
                    }
                });
            // this.sdsdzzzzaaa = !this.sdsdzzzzaaa
        }
    },
})
var ybglTablelist = new Vue({
    el: '.ybglTablelist',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        jsonList: [],
        cjsonList: [],
        datelList: [],
        twojsonList: [],
        num: 0,
        Datanum: 1,
        hszYzZxd: {
            hszYzZxd: ''
        }
    },
    created: function () {
        this.$nextTick(function () {
            $(".zui-table-view").uitable();
        })
    },
    methods: {
        edit: function (index) {
            brzcList001.Datatype=1
            brzcList001.index = 0
            brzcList001.num = three.num;
            brzcList001.Datanum = this.Datanum;
            switch (this.num) {
                case 0:
                    brzcList001.popContent = this.jsonList[index]
                    brzcList001.title = '编辑' + this.jsonList[index].ljmc
                    break;
                case 1:
                    if (this.Datanum == 1) {
                        brzcList001.title = '编辑' + this.jsonList[index].jdmc
                    } else {
                        brzcList001.title = '编辑' + this.jsonList[index].ejjdmc
                    }
                    brzcList001.popContent = this.jsonList[index];
                    break;
                case 2:
                    brzcList001.title = '编辑' + this.jsonList[index].xmmc
                    brzcList001.popContent=this.jsonList[index]
                    break;
                case 3:
                    brzcList001.title = '编辑' + this.jsonList[index].yzbm
                    brzcList001.popContent=this.jsonList[index]
                    break;
            }
        },
        removeList: function (item,$index) {
            if (item != undefined || this.isChecked.length != 0) {
                var arr = [];
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i]) {
                            arr.push(this.jsonList[i]);
                        }
                    }
                } else {
                    arr.push(item)
                }
                var data = '{"list":' + JSON.stringify(arr) + '}';
                switch (this.num) {
                    case 0:
                        var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhys&types=delete';
                        break;
                    case 1:
                        if (this.Datanum == 1) {
                            var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhysJd&types=delete';
                        } else {
                            var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhysEjjd&types=delete';
                        }
                        break;
                    case 2:
                        var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhysHl&types=delete';
                        break;
                    case 3:
                        var url = '/actionDispatcher.do?reqUrl=LcljXtwhBdwhysBdmb&types=delete';
                        break;
                }
                this.$http.post(url, data).then(function (data) {
                        if (data.body.a == 0) {
                            if(ybglTablelist.isChecked.length>0){
                                for(var j=0;j<ybglTablelist.isChecked.length;j++){
                                    ybglTablelist.jsonList.splice(j,1)
                                }
                            }else{
                                ybglTablelist.jsonList.splice($index,1)
                            }
                            ybglTablelist.isChecked=[],
                            malert(data.body.c, 'top', 'success');
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }

                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            }else{
                malert('老板，你要删除谁？', 'top', 'defeadted');
            }
        }
    },
})

function addListds() {
    brzcList001.add()
}

function removeListData() {
    ybglTablelist.removeList()
}