<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室维护</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="syjgdm.css" rel="stylesheet">
</head>
<style>
    .zui-btn.btn-outline.btn-default:hover {
        background-color: transparent;
    }

    .zui-row:after, .zui-row:before {
        display: initial;
    }

    .rysx_bottom {
        margin: 0
    }

    .tabs-link .tabs-link-list.tabs-link-active {
        color: #1abc9c;
    }

    .ivu-tabs-ink-bar-active {
        background-color: #1abc9c;
    }
    input[type=radio].zui-radio + label:after{
        z-index: 0;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper background-f" v-cloak>
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="updateYnxm()">更新院内项目</button>
            <button class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5" @click="getData">刷新</button>
        </div>
        <div class="grid-box" style="padding:13px 0;">
            <div class=" flex-container margin-l-10">
                <div class="flex-container flex-align-c margin-l-10">
                    <span class="whiteSpace  ft-14 margin-r-5">检索</span>
                    <div class="zui-input-inline wh180">
                        <input class="zui-input " placeholder="请输入关键字" v-model="param.parm" type="text" id="jsvalue" @keydown.13="searchHc()"/>
                    </div>
                </div>

                <div class="flex-container flex-align-c margin-l-10">
                    <span class="whiteSpace  ft-14 margin-r-5">状态</span>
                    <select-input @change-data="commonResultChange" :not_empty="true" :child="type_tran"
                                  :index="param.type" :val="param.type" :search="true"
                                  :name="'param.type'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c margin-l-10">
                    <label class="whiteSpace  ft-14 margin-r-5">对码</label>
                    <select-input @change-data="commonResultChange" :not_empty="true" :child="dm_tran"
                                  :index="param.sfdm" :val="param.sfdm" :search="true"
                                  :name="'param.sfdm'">
                    </select-input>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view hzList padd-r-10 padd-l-10">
        <tabs @tab-active="tabBg" :tab-child="[{text:'院内药品项目'},{text:'院内费用项目'}]"></tabs>
        <div class="fyxm-size " key="a" v-if="num==0">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s"><span>序号</span></div>
                        </th>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'ypbmList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell text-left cell-l"><span>药品名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>规格</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>产地</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>批准文号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>三医编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell text-left cell-l"><span>三医名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品标志</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">单价</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">状态</div>
                        </th>
                    </tr>
                    <!--@click="checkOne($index)"-->
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                <table class="zui-table table-width50" >
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="switchIndex('hoverIndex1',true,$index)"
                        @mouseleave="switchIndex()"
                        :tabindex="$index" @click="edit($index)"
                        v-for="(item, $index) in ypbmList">
                        <td>
                            <div class="zui-table-cell cell-s" v-text="$index+1"></div>
                        </td>
                        <td  class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'ypbmList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yyxmbm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l title text-left" v-text="item.yyxmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s title" v-text="item.zxgg"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.cdmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.pzwh"></div>
                        </td>
                        <th>
                            <div class="zui-table-cell cell-l" v-text="item.syjgxmbm"></div>
                        </th>
                        <th>
                            <div class="zui-table-cell text-left cell-l">
                                <input @click="edit($index)" :id="'mc_'+$index" v-model="item.syjgxmmc" @input="searching($index,false,'ybmc',$event.target.value)" @keyDown="changeDown($index,$event,'text')">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="page"
                                              @click-one="checkedOneOut" @click-two="selectOne">
                                </search-table>
                            </div>
                        </th>
                        <td>
                            <div class="cell-s zui-table-cell ">
                                <input class="green" type="checkbox" :checked="true"><label @dblclick.stop></label>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s title" v-text="item.dj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s title" v-text="stopSign[item.tybz]" :class="item.tybz == '0'?'color-ysh':'color-wtg'"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
            </div>

        </div>
        <div class="fyxm-size" key="b" v-if="num==1">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s"><span>序号</span></div>
                        </th>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'zlxmList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>诊疗编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell text-left cell-l"><span>诊疗名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>三医编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell text-left cell-l"><span>三医名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品标志</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>规格</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">单价</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">状态</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                <table class="zui-table table-width50" >
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                        @mouseleave="switchIndex()"
                        @click="edit($index)" :tabindex="$index"
                        v-for="(item, $index) in zlxmList">
                        <td>
                            <div class="zui-table-cell cell-s" v-text="$index+1"></div>
                        </td>
                        <td  class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'zlxmList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yyxmbm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l title text-left" v-text="item.yyxmmc"></div>
                        </td>
                        <th>
                            <div class="zui-table-cell cell-l" v-text="item.syjgxmbm"></div>
                        </th>
                        <th>
                            <div class="zui-table-cell text-left cell-l">
                                <input @click="edit($index)" :id="'mc_'+$index" v-model="item.syjgxmmc" @input="searching($index,false,'ybmc',$event.target.value)" @keyDown="changeDown($index,$event,'text')">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="page"
                                              @click-one="checkedOneOut" @click-two="selectOne">
                                </search-table>
                            </div>
                        </th>
                        <td>
                            <div class="cell-s zui-table-cell ">
                                <input class="green" type="checkbox" :checked="false"><label @dblclick.stop></label>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s title" v-text="item.zxgg"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s title" v-text="item.dj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s title" v-text="stopSign[item.tybz]" :class="item.tybz == '0'?'color-ysh':'color-wtg'"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
            </div>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
<div class="side-form pop-width" :class="{'ng-hide':index==1}" v-cloak style="padding-top: 0;"
     id="brzcList" role="form">
    <div class="tab-message">
        <a>{{title}}</a>
        <a href="javascript:" class="fr closex ti-close" @click="closes"></a>
    </div>
    <div class="ksys-side">
          <span class="span0">
        <i>编码</i>
        <input type="text" class="zui-input border-r4" v-model="popContent.dwbm" @keyup="nextFocus($event)" />
        </span>
        <span class="span0">
        <i>名称</i>
        <input type="text" class="zui-input border-r4" v-model="popContent.dwmc" @keyup="nextFocus($event)" />
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">确定</button>
    </div>
</div>

<script type="text/javascript" src="syjgdm.js"></script>
</body>
</html>