<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>危机值</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="wjz.css"/>
</head>
<style>
    .zui-table-view{
        overflow: inherit !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10" id="jyxm_icon">
<div class="wrapper">
    <div class="panel">
        <div class="panel-head   background bg-fff">
            <div class="zui-row">
                <div class="col-x-12 bg-f9f9f9 ybhsgl-height">
                    <button class="zui-btn btn-primary icon-sx1 padd-r5">刷新</button>
                    <button class="zui-btn btn-primary-b icon-baocun padd-r5">保存</button>
                    <button class="zui-btn btn-primary-b icon-sc-header padd-r5">删除</button>
                    <button class="zui-btn btn-primary-b icon-gl padd-r5" @click="guolu">过滤</button>
                    <button class="zui-btn btn-primary-b icon-yl padd-r5">预览</button>
                    <button class="zui-btn btn-primary-b icon-dysq padd-r5">打印</button>
                    <!--<div style="po sition: relative;width: 200px">-->
                    <!--</div>-->
                </div>
                <div class="col-x-12 bg-fff top-xia">
                    <div class="col-x-6 xmzb-top-left">
                        <i>搜索</i>
                        <i><input type="text" value="" placeholder="请输入关键字" class="zui-input"/></i>
                        <i><button class="zui-btn btn-primary xmzb-db">查询</button></i>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="zui-table-view" style="border:none; margin-top: 116px">
        <div class="zui-table-header" style="margin-top: -6px;">
            <table class="zui-table">
                <thead>
                <tr>
                    <th  style="text-align:center;">
                        <input-checkbox @result="reCheckBox" :list="'jydjList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="id" z-width="100px">
                        <div class="zui-table-cell"><span>编码</span></div>
                    </th>
                    <th z-field="username" z-width="150px"  >
                        <div class="zui-table-cell" ><span>中文名称</span></div>
                    </th>
                    <th z-field="sex" z-width="150px">
                        <div class="zui-table-cell"><span>判断方式</span></div>
                    </th>
                    <th z-field="experience" z-width="150px" >
                        <div class="zui-table-cell"><span>数据类型</span></div>
                    </th>
                    <th z-field="classify" z-width="100px">
                        <div class="zui-table-cell"><span>英文名称</span></div>
                    </th>

                    <th z-field="sjks" z-width="150px">
                        <div class="zui-table-cell"><span>单位</span></div>
                    </th>
                    <th z-field="cankao" z-width="150px">
                        <div class="zui-table-cell"><span>参考值类型</span></div>
                    </th>
                    <th z-field="daima" z-width="150px">
                        <div class="zui-table-cell"><span>代码</span></div>
                    </th>
                    <th z-field="zhuangtai" z-width="150px">
                        <div class="zui-table-cell"><span>状态</span></div>
                    </th>
                    <th z-field="leixing" z-width="100px">
                        <div class="zui-table-cell"><span>操作</span></div>
                    </th>
                    <!--<th z-field="wealth" z-width="50px">-->
                        <!--<div class="zui-table-cell"><span class="icon-bgzdgl"></span></div>-->
                    <!--</th>-->
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body">
            <table class="zui-table">
                <tbody>
                <tr data-index="0" @dblclick="dbEdit">
                    <td z-width="100px">
                        <!--<input-checkbox @result="reCheckBox" :list="'jydjList'"-->
                                        <!--:type="'some'" :which="$index"-->
                                        <!--:val="isChecked[$index]">-->
                        <!--</input-checkbox>-->
                    </td>
                    <td><div class="zui-table-cell">1</div></td>
                    <td><div class="zui-table-cell">淋巴细胞比率</div></td>
                    <td><div class="zui-table-cell">FGHJ</div></td>
                    <td><div class="zui-table-cell">his</div></td>
                    <td><div class="zui-table-cell">his</div></td>
                    <td><div class="zui-table-cell">his</div></td>
                    <td><div class="zui-table-cell">his</div></td>
                    <td><div class="zui-table-cell">his</div></td>
                    <td  width="150px"><div class="switch" >
                        <input  type="checkbox"/>
                        <label></label>
                    </div>
                    </td>
                    <td><div class="zui-table-cell" @click="DelLine"><i class="icon-sc"></i></div></td>

                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

    <div id="pop">
        <!--<transition name="pop-fade">-->
        <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
        <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title"></div>
            <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
            <div class="layui-layer-content" >
                <div class=" layui-mad layui-height" v-text="centent">
                </div>
            </div>
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
            </div>
        </div>
        <!--</transition>-->
    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide" style="width:320px;padding-top: 0;"  id="brzcList" role="form">
    <div class="tab-message">
        <a>编辑危机值</a>
        <a href="javascript:;" class="fr closex ti-close" @click="closes"></a>
    </div>
    <div class="ksys-side">
        <span>
            <i>判断低值</i>
            <input type="text" class="zui-input border-r4" placeholder="请输入"/>
        </span>
        <span>
            <i>判断高值</i>
            <input type="text" class="zui-input border-r4" placeholder="请输入"/>
        </span>
        <span>
            <i>状态</i>
            <div class="switch">
                  <input  type="checkbox"/>
                <label></label>
            </div>
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>



<div id="isTabel">
    <div class="pophide" :class="{'show':isShow}"></div>
    <div class="zui-form podrag  bcsz-layer zui-800 " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title ">过滤查询</div>
        <div class="guolv-xinzeng">
            <span class="layui-txt" @click="append()">新增一项</span>
            <i class="color-btn" @click="isShow=false"
               style="margin-top:-17px;width: 16px;height: 16px;display: inline-block;margin-left: 10px;float: right">×</i>
        </div>
        <div class="layui-layer-content">
            <div class=" layui-mad">
                <ul class="guolv-header guolv-style">
                    <li class="line">项目</li>
                    <li class="line">条件</li>
                    <li class="line">结果</li>
                    <li class="line">连接条件</li>
                    <li class="line">操作</li>
                </ul>
                <ui class="guolv-content" id="guo_append">
                    <div class="guolv-style guolv-bottom" v-for="(list,index) in appNum" :key="list.num">
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext"  name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <!--<div class="zui-select-inline">-->
                            <!--<input type="text" class="zui-input lsittext" name="input1" check="required" />-->
                            <!--<div class="zui-select-group" role="listbox">-->
                            <!--<ul class="inner">-->
                            <!--<li value="0">中国</li>-->
                            <!--<li value="1">印度</li>-->
                            <!--<li value="2">安道尔</li>-->
                            <!--<li value="3">老挝</li>-->
                            <!--</ul>-->
                            <!--</div>-->
                            <input  class="zui-input"/>

                            <!--</div>-->
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required" />
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <span class="icon-sc" @click="sc(index)"></span>
                        </li>
                    </div>

                </ui>
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="save()">保存</button>
        </div>
    </div>
</div>

<script src="wjz.js"></script>
</body>
</html>
