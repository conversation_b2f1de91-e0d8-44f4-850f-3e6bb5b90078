<div id="yjs" class="padd-b-10 padd-l-10 padd-t-10">
    <div class="flex-container">
        <button class="tong-btn btn-parmary" @click="getData">刷新</button>
        <button class="tong-btn btn-parmary" @click="yjs">预结算</button>
        <button class="tong-btn btn-parmary" @click="ybcy">医保出院</button>
        <button class="tong-btn btn-parmary" @click="cybcy">取消出院</button>
        <button class="tong-btn btn-parmary" @click="print">补偿打印</button>
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font14">入院信息</div>
        </div>
        <div class="tab-card-body padd-t-10">
            <div class="grid-box ">
                <div class="flex-container flex-align-c flex-wrap-w">
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                        <p class="padd-r-5">住&nbsp;院&nbsp;号&nbsp;</p>
                        <input class="zui-input wh180" type="text" v-model="json.zyh" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">病人姓名</p>
                        <input class="zui-input wh180" type="text" v-model="json.brxm" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">性&emsp;&emsp;别</p>
                        <input class="zui-input wh180" type="text" v-model="brxb_tran[json.brxb]" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">床&nbsp;位&nbsp;号</p>
                        <input class="zui-input wh180" type="text" v-model="json.rycwbh" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">人员类别</p>
                        <input class="zui-input wh180" type="text" v-model="fDate(json.csrq,'yyyy-MM-dd')"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">出生日期</p>
                        <input class="zui-input wh180" type="text" v-model="fDate(json.csrq,'yyyy-MM-dd')"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">年&emsp;&emsp;龄</p>
                        <input class="zui-input wh180" type="text" v-model="json.nl" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">入院科室</p>
                        <input class="zui-input wh180" type="text" v-model="json.ryksmc" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">住院医师</p>
                        <input class="zui-input wh180" type="text" v-model="json.zyysxm" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">入院日期</p>
                        <input class="zui-input wh180" type="text" v-model="fDate(json.ryrq,'yyyy-MM-dd')"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c margin-l-15 margin-b-15">
                        <p class="padd-r-5">出院日期</p>
                        <div class="zui-input-inline zui-date">
                            <i class="datenox fa-calendar"></i>
                            <input id="cyrq"  class="wh180 zui-input" @keydown="nextFocus($event)"  v-model="json.cyrq">
                        </div>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">费&emsp;&emsp;别</p>
                        <input class="zui-input wh180" type="text" v-model="json.brfbmc" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">联系电话</p>
                        <input class="zui-input wh180" type="text" v-model="json.lxdh"
                               @keydown="nextFocus($event)" :not_empty="true"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10" style="width: 260px">
                        <p class="padd-r-5">保险病人</p>
                        <input  type="checkbox" v-model="json.bxbr" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">保险类别</p>
                        <input class="zui-input wh180" type="text" v-model="json.bxlbbm" v-text="json.bxlbmc"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">保险卡号</p>
                        <input class="zui-input wh180" type="text" v-model="json.ybkh"
                               @keydown="nextFocus($event)" :not_empty="true"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5 text-red">出院情况</p>
                        <select-input @change-data="resultChange" class="wh180"
                                      :child="gznhcyqk_tran" :index="json.cyqk" :val="json.cyqk"
                                      :search="true" :name="'json.cyqk'" :not_empty="true">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5 text-red">出院类型</p>
                        <select-input class="wh180" @change-data="resultChange" :not_empty="true"
                                      :child="gznhcy_tran" :index="json.cylx" :val="json.cylx"
                                      :search="true" :name="'json.cylx'" :not_empty="true">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">保&nbsp;险&nbsp;费</p>
                        <input class="zui-input wh180" type="text" v-model="json.bxf" data-notEmpty="fasle"
                               @keydown="nextFocus($event)"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10" style="width: 260px">
                        <p class="padd-r-5">是否转诊</p>
                        <input   type="checkbox" v-model="json.zzbz" data-notEmpty="fasle"
                               @keydown="nextFocus($event)"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">转&nbsp;诊&nbsp;号</p>
                        <input class="zui-input wh180" type="text" v-model="json.zzh" data-notEmpty="fasle"
                               @keydown="nextFocus($event)"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">银行卡号</p>
                        <input class="zui-input wh180" type="text" v-model="json.yhkh" data-notEmpty="fasle"
                               @keydown="nextFocus($event)"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">银行卡号<br>开户人姓</p>
                        <input class="zui-input wh180" type="text" v-model="json.yhkhrx" data-notEmpty="fasle"
                               @keydown="nextFocus($event)"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">患者与开<br>户人关系</p>
                        <input class="zui-input wh180" type="text" v-model="json.khgx" data-notEmpty="fasle"
                               @keydown="nextFocus($event)"/>
                    </div>

                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">院内费用<br>金额</p>
                        <input class="zui-input wh180" type="text" v-model="ynfyContent.fyje" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">上传合计<br>金额</p>
                        <input class="zui-input wh180" type="text" v-model="ynfyContent.scfyje" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">预结算补<br>偿金额</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.compensatecost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">新精准优<br>抚补偿</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.salvayfcost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">预交金额</p>
                        <input class="zui-input wh180" type="text" v-model="ynfyContent.yjje" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">预结算退<br>补金额</p>
                        <input class="zui-input wh180" type="text" v-model="fDec(yjsContent.tbje,2)"
                               disabled="disabled"/>
                    </div>

                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">民政救助</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.civilcost" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">新精准残<br>联补偿</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.salvaclcost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">大病商保</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.insurecost" disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">兜底补偿</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.bottomredeem"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">精准目录</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.medicinecost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">新精准疾<br>控补偿</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.salvajkcost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">交通补贴</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.trafficcost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">医疗补贴</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.medicalcost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">计生救助</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.salvajscost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">新精准扶<br>贫补偿</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.salvafpcost"
                               disabled="disabled"/>
                    </div>
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <p class="padd-r-5">生活补贴</p>
                        <input class="zui-input wh180" type="text" v-model="yjsContent.livecost" disabled="disabled"/>
                    </div>
                    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c" style="width: 92%">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th class="cell-m"><div class="zui-table-cell cell-m">序号</div></th>
                                    <th class="cell-m">
                                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                        :type="'all'" :val="isCheckAll">
                                        </input-checkbox>
                                    </th>
                                    <th><div class="zui-table-cell cell-s">个人编号</div></th>
                                    <th><div class="zui-table-cell cell-s">姓名</div></th>
                                    <th><div class="zui-table-cell cell-s">上传费用合计</div></th>
                                    <th><div class="zui-table-cell cell-s">保内费用</div></th>
                                    <th><div class="zui-table-cell cell-s">自费费用</div></th>
                                    <th><div class="zui-table-cell cell-s">实际补偿</div></th>
                                    <th><div class="zui-table-cell cell-s">起付钱</div></th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one over-auto"   @scroll="scrollTable($event)">
                            <table class="zui-table ">
                                <tbody>
                                <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                                    <td class="cell-m"><div  class="zui-table-cell cell-m">{{$index+1}}</div></td>
                                    <td  class="cell-m">
                                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                        :type="'some'" :which="$index"
                                                        :val="isChecked[$index]">
                                        </input-checkbox>
                                    </td>
                                    <td><div  class="zui-table-cell cell-s">{{item.memberid}}</div></td>
                                    <td><div  class="zui-table-cell cell-s">{{item.brxm}}</div></td>
                                    <td><div  class="zui-table-cell cell-s">{{fDec(item.scfyje,2)}}</div></td>
                                    <td><div  class="zui-table-cell cell-s">{{fDec(item.insurancecost,2)}}</div></td>
                                    <td><div  class="zui-table-cell cell-s">{{fDec(item.zffy,2)}}</div></td>
                                    <td><div  class="zui-table-cell cell-s">{{fDec(item.compensatecost,2)}}</div></td>
                                    <td><div  class="zui-table-cell cell-s">{{item.undulatingline}}</div></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="application/javascript" src="yjs.js"></script>
