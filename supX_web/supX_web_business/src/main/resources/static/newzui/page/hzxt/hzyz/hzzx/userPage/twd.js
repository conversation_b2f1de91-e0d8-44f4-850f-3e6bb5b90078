var s = new Date().getTime();
var l = new Date();
var e = l.setDate(l.getDate() + 1);
var panel=new Vue({
    el:'.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        time:'',
        qsxzList:[],
        popContent:{},
        jsons:{
        'zyh': userNameBg.Brxx_List.zyh
        },
    },
    mounted:function(){
        laydate.render({
            elem: '#time',
            format: 'yyyy-MM-dd',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                panel.time = value;
                other.getOther();
            }
        });
        this.iscf()
        this.time3(this.time);
    },
    created: function () {
        this.time = this.$options.filters['formDate'](userNameBg.Brxx_List.ryrq)
    },
    methods:{
        iscf: function () {
            this.qsxzList=[];
            var parm = {
                zyh: userNameBg.Brxx_List.zyh
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' + JSON.stringify(parm), function (json) {
                if (json.a == '0') {
                    if (json.d.list != null && json.d.list.length > 0) {
                        var qb = {
                            yexm: userNameBg.Brxx_List.brxm,//如果有影响请还原上面代码，注释本行代码
                            yebh: "000",
                        }
                        json.d.list.unshift(qb);
                        panel.qsxzList =json.d.list;//亲属选择
                    }
                }
            });
        },
        commonResultChange:function(val){
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            other.getOther();
        },
        time3 :function(val) {
            var sa = val.split('-');
            var  yebh = this.popContent['yebh'] == '000' ? undefined : this.popContent['yebh']
            var date55 = sa[0] + '-' + sa[1] + '-' + (parseInt(sa[2]) < 10 ? '0' + (parseInt(sa[2])) : parseInt(sa[2]));
            param = {
                yebh:yebh,
                'zyh': userNameBg.Brxx_List.zyh,
                'sxrq': date55
            };
        }
    },
});

var red = "#AE0000";
var black = "#6C6C6C";
var lattice = 14;
var temPath = false;
var pulsePath = false;
var heartRatePath = false;
var coolingPath = false;
var tssmList = [];
var wcyyList = [];
// 心率  //脉搏 //tw_json【0：口温  1：腋温  2：肛温】
var xl_json = [], mb_json = [], tw_json = [];
var obj = {
    'i': null,
    'x': null
};
//体温单模板
var canvas = document.getElementById("twd_cvs");
var canvasTitle = document.getElementById("twd_title");
var contextTitle = canvasTitle.getContext("2d");
var context = canvas.getContext("2d");

function tw_title() {
    if (canvasTitle == null) {
        return false;
    }
    canvasTitle.width = lattice * 48;
    canvasTitle.height = lattice * 2;
    for (var i = 1; i < 3; i++) {
        if (i == 2) {
            line(contextTitle, 0, i * lattice, lattice * 48, i * lattice, 'rgb(51, 51, 51)', 1, 0);
        } else {
            line(contextTitle, 0, i * lattice, lattice * 48, i * lattice, "rgb(51, 51, 51)", 1, 1);
        }
    }
    for (var i = 0; i < 49; i++) {
        if (i > 5) {
            if (i % 6 == 0 && i != 6) {
                line(contextTitle, i * lattice, 0, i * lattice, lattice * 40, red, 1, 1);
            }
            if (i == 6) {
                line(contextTitle, i * lattice, 0, i * lattice, lattice * 40, "rgb(51, 51, 51)", 1, 0);
            } else {
                line(contextTitle, i * lattice, 0, i * lattice, lattice * 40, 'rgb(51, 51, 51)', 1, 1);
            }
        }
        if (i == 3) {
            line(contextTitle, i * lattice, lattice, i * lattice, lattice * 40, 'rgb(51, 51, 51)', 1, 1);
        }
    }

    contextTitle.textBaseline = 'middle';
    contextTitle.font = "12px 微软雅黑";
    contextTitle.textAlign = "center";
    contextTitle.fillText("时        间", lattice * 3, lattice * 0.5);
    contextTitle.fillText("脉搏    体温", lattice * 3, lattice * 1.5);
}

// 绘制时间段
function fillTime() {
    contextTitle.textBaseline = 'middle';
    contextTitle.font = "12px 微软雅黑";
    contextTitle.textAlign = "center";
    for (var i = 0; i < 7; i++) {
        contextTitle.fillStyle = red;
        contextTitle.fillText(other.titleTime[0], (i + 1) * lattice * 6 + (lattice * 0.5), lattice * 0.5);
        contextTitle.fillStyle = black;
        contextTitle.fillText(other.titleTime[1], (i + 1) * lattice * 6 + (lattice * 0.5) + lattice, lattice * 0.5);
        contextTitle.fillText(other.titleTime[2], (i + 1) * lattice * 6 + (lattice * 0.5) + (lattice * 2), lattice * 0.5);
        contextTitle.fillText(other.titleTime[3], (i + 1) * lattice * 6 + (lattice * 0.5) + (lattice * 3), lattice * 0.5);
        contextTitle.fillStyle = red;
        contextTitle.fillText(other.titleTime[4], (i + 1) * lattice * 6 + (lattice * 0.5) + (lattice * 4), lattice * 0.5);
        contextTitle.fillText(other.titleTime[5], (i + 1) * lattice * 6 + (lattice * 0.5) + (lattice * 5), lattice * 0.5);
    }
}

function tw_model() {
    if (canvas == null) return false;
    canvas.width = lattice * 48;
    canvas.height = lattice * 40;

    for (var i = 0; i < 41; i++) {
        if (i % 5 == 0 && i != 40) {
            line(context, 6 * lattice, i * lattice, lattice * 48, i * lattice, "#000", 1, 0);
        }
        if (i == 40) {
            line(context, 0, i * lattice, lattice * 48, i * lattice, "#000", 1, 0);
        } else {
            line(context, 6 * lattice, i * lattice, lattice * 48, i * lattice, black, 1, 1);
        }
    }

    for (var i = 0; i < 49; i++) {
        if ((i > 5 || i == 3) && i != 6) {
            if (i % 6 == 0) {
                line(context, i * lattice, 0, i * lattice, lattice * 40, red, 1, 1);
            } else {
                line(context, i * lattice, 0, i * lattice, lattice * 40, black, 1, 1);
            }
        }
        if (i == 6) {
            line(context, i * lattice, 0, i * lattice, lattice * 40, "#000", 1, 0);
        }
    }

    //绘制固定文字内容
    context.textBaseline = 'middle';
    context.fillStyle = "#000000";
    context.font = "12px 微软雅黑";
    context.textAlign = "left";
    context.fillText("(次/分)  (℃)", 0, 7);
    context.textAlign = "center";
    context.fillText("180     42", lattice * 3, lattice * 1.5);
    context.fillText("160     41", lattice * 3, lattice * 5.5);
    context.fillText("140     40", lattice * 3, lattice * 10.5);
    context.fillText("120     39", lattice * 3, lattice * 15.5);
    context.fillText("100     38", lattice * 3, lattice * 20.5);
    context.fillText(" 80     37", lattice * 3, lattice * 25.5);
    context.fillText(" 60     36", lattice * 3, lattice * 30.5);
    context.fillText(" 40     35", lattice * 3, lattice * 35.5);
}

var mbList = [];
var xlList = [];

//循环描点及连线
function draw(event, json) {
    var day = 0;
    var tem = 0;
    var type = 0;
    var pulse = 0;
    var heartRate = 0;
    var cooling = 0;    // 物理降温
    var lastPoint = 0;
    var lastDate = [];
    var lastPoints = [];
    var listType = '';
    var spot = canvas.getContext("2d");
    for (var i = 0; i < json.length; i++) {
        tem = json[i]['temperature'];//对应时段的温度
        type = json[i]['type'];//对应测量部位
        pulse = json[i]['pulse'];//脉搏
        heartRate = json[i]['heartRate'];//心率
        cooling = json[i]['wljw'];//物理降温
        day = 0;
        if (i % 6 != 0 && json[i].date != json[i - 1].date) {
            day += DateDiff(json[i - 1].date, json[i].date);
        }
        // 转换时间再比较
        var jsonTime = json[i].date.split("-")[1] + "月" + json[i].date.split("-")[2] + "日";
        for (var a = 0; a < time.currentDay.length; a++) {
            if (jsonTime == time.currentDay[a]) {
                day = day + a;
            }
        }
        lastDate.push(day);
        // 这里根据other.startTime来计算x坐标
        var x = ((json[i].time - other.startTime) / 4) * lattice + (lattice * 6 + (lattice / 2)) + (day * lattice * 6);
        var y = 0;
        spot.save();
        spot.beginPath();
        if (json[i]['tbsm'] != null && json[i]['tbsm'] != "null") {
            // 绘制特殊说明
            context.fillStyle = "#ff0000";
            for (var d = 0; d < tssmList[i].length; d++) {
                context.fillText(tssmList[i][d], x, lattice * (0.5 + d));
            }
        }
        if (json[i]['wcyy'] != null && json[i]['wcyy'] != "null" && wcyyList.length > 0) {
            // 绘制未测原因
            context.fillStyle = "#ff0000";
            for (var e = 0; e < wcyyList[i].length; e++) {
                context.fillText(wcyyList[i][e], x, (lattice * (0.5 + e)) + (lattice * 35));
            }
        }

        if (heartRate != null) {
            y = (180 - heartRate) / 4 * lattice;
            spot.fillStyle = spot.strokeStyle = "#ff0000";
            // 是否标记心跳起搏器
            if (json[i]['xtqbq'] == "1") {
                spot.fillText('h', x, y);
            } else {
                spot.arc(x, y, 5, 0, Math.PI * 2, true);
            }
            lastPoints.push({"x": x, "y": y});
            listType = 'heartRate';
        } else if (pulse != null) { // 画脉搏
            y = (180 - pulse) / 4 * lattice;
            spot.fillStyle = spot.strokeStyle = "#ff0000";
            spot.arc(x, y, 5, 0, Math.PI * 2, true);
            lastPoints.push({"x": x, "y": y});
            listType = 'pulse';
        } else if (tem != null) {
            y = (42 - tem) * 5 * lattice;
            spot.fillStyle = spot.strokeStyle = "#26006b";
            if (type == 0) {
                spot.arc(x, y, 5, 0, Math.PI * 2, true);
            } else if (type == 1) {
                spot.strokeStyle = "transparent";
                spot.arc(x, y, 5, 0, Math.PI * 2, true);
                spot.fillText('╳', x, y, 10);
            } else if (type == 2) {  // 肛温 空心圆
                spot.arc(x, y, 5, 0, Math.PI * 2, true);
            } else if (type == 3) { // 耳温 空三角形
                spot.font = "20px Georgia";
                spot.fillText('△', x, y, 20);
            } else if (type == 4) { // 额温 实心五角心
                spot.fillText('★', x, y, 20);
            }
            lastPoints.push({"x": x, "y": y});
        } else {
            lastPoint++;
            lastPoints.push({"x": null, "y": null});
        }
        // 此处要在fill前面执行，很关键
        if (event != null) {
            if (spot.isPointInPath(event.offsetX, event.offsetY)) {
                if (tem) temPath = true;
                else if (pulse) pulsePath = true;
                else if (heartRate) heartRatePath = true;
                obj.i = i;
                obj.x = x;
            }
        }
        spot.closePath();
        if (tem) {
            if (type == 0) spot.fill();
            else if (type == 1) spot.stroke();
            else if (type == 2) spot.stroke();
        } else if (pulse) {
            spot.fill();
        } else if (heartRate) {
            spot.stroke();
        }
        if (i != 0) {
            // 在将要连线的点前面的所有点不能为空
            var blankPoint = 0;
            for (var t = 0; t < i; t++) {
                if (json[t].heartRate == null && json[t].pulse == null && json[t].temperature == null) {
                    blankPoint++;
                }
            }
            if (blankPoint == i) lastPoint = 0;
            // 这里根据other.startTime来计算连线的x坐标
            var LastX = 0;
            var LastY = 0;
            if (json[i]['pulse'] && blankPoint != i) {
                LastX = lastPoints[i - (1 + lastPoint)]['x'];
                LastY = lastPoints[i - (1 + lastPoint)]['y'];
                if (!hadReason(i, lastPoint, json)) line(context, LastX, LastY, x, y, '#ff0000', 1, 1);
                lastPoint = 0;
            } else if (json[i]['temperature'] && blankPoint != i) {
                LastX = lastPoints[i - (1 + lastPoint)]['x'];
                LastY = lastPoints[i - (1 + lastPoint)]['y'];
                if (!hadReason(i, lastPoint, json)) line(context, LastX, LastY, x, y, '#26006b', 1, 1);
                lastPoint = 0;
            } else if (json[i]['heartRate'] && blankPoint != i) {
                LastX = lastPoints[i - (1 + lastPoint)]['x'];
                LastY = lastPoints[i - (1 + lastPoint)]['y'];
                if (!hadReason(i, lastPoint, json)) line(context, LastX, LastY, x, y, '#ff0000', 1, 1);
                lastPoint = 0;
            }
        }
        if (obj.i != null && i == obj.i) {
            spot.fillStyle = "#000000";
            if (temPath && tem) {
                if (tem > 41.7)
                    spot.fillText(Math.round(tem * 10) / 10 + "℃", x, y + 20);
                else
                    spot.fillText(Math.round(tem * 10) / 10 + "℃", x, y - 20);
            } else if (pulsePath && pulse) {
                spot.fillText(Math.round(pulse * 10) / 10 + "次/分", x, y - 20);
            } else if (heartRatePath && heartRate) {
                spot.fillText(Math.round(heartRate * 10) / 10 + "次/分", x, y - 20);
            }
        }

        if (cooling) {
            var c = (42 - cooling) * 5 * lattice + lattice;
            var cool = canvas.getContext("2d");
            cool.save();
            cool.beginPath();
            cool.strokeStyle = "#ff0000";
            cool.arc(x, c, 5, 0, Math.PI * 2, true);
            if (event != null && cool.isPointInPath(event.offsetX, event.offsetY)) {
                coolingPath = true;
                obj.i = i;
                obj.x = x;
            }
            cool.closePath();
            cool.stroke();
            line(context, x, y, x, c, '#ff0000', 1, 1, true);
            if (obj.i != null && i == obj.i) {
                cool.fillStyle = "#000000";
                if (coolingPath && cooling) {
                    if (cooling > 41.7) cool.fillText(Math.round(cooling * 10) / 10 + "℃", x, c + 20);
                    else cool.fillText(Math.round(cooling * 10) / 10 + "℃", x, c - 20);
                }
            }
        }
    }
    if (listType == 'heartRate') {
        xlList = lastPoints;
        if (mbList.length > 0) drawShade();
    } else if (listType == 'pulse') {
        mbList = lastPoints;
        if (xlList.length > 0) drawShade();
    }
}

function hadReason(i, lastPoint, json) {
    for (var v = 0; v < lastPoint + 1; v++) {
        if (json[i - v]['wcyy'] != null) return true;
    }
    return false;
}

// 绘制阴影
function drawShade() {
    var list = [];
    var xlLine = [];
    var mbLine = [];
    var x, y, a, b, s, e;
    var startPoint = {};
    var endPoint = {};
    var p1, p2, p3;
    var listY = [];
    var isOdd = 2;
    var sList = [];
    var eList = [];
    var ss = [];
    var es = [];

    for (var i = 0; i < xlList.length; i++) {
        isOdd++;
        if (xlList[i]['x'] && xlList[i]['y'] && xlList[i]['x'] == mbList[i]['x'] && xlList[i]['y'] == mbList[i]['y']) {
            if (isOdd % 2 == 0) {
                es.push(i);
                eList.push({'x': xlList[i]['x'], 'y': xlList[i]['y']});
            } else {
                ss.push(i);
                sList.push({'x': xlList[i]['x'], 'y': xlList[i]['y']});
            }
        }
    }
    for (var p = 0; p < sList.length; p++) {
        s = ss[p];
        e = es[p];
        startPoint = sList[p] || {};
        endPoint = eList[p] || {};

        for (var q = s; q <= e; q++) {
            if (xlList[q]['y'] != null) {
                listY.push(xlList[q]['y']);
                xlLine.push({'x': xlList[q]['x'], 'y': xlList[q]['y']})
            }
            if (mbList[q]['y'] != null) {
                listY.push(mbList[q]['y']);
                mbLine.push({'x': mbList[q]['x'], 'y': mbList[q]['y']})
            }
        }
        // 正反push所有的点
        list = xlLine;
        for (var l = mbLine.length - 1; l >= 0; l--) list.push(mbLine[l]);

        var maxY = Math.max.apply(null, listY);
        var minY = Math.min.apply(null, listY);
        for (var d = 0; d < ((endPoint['x'] - startPoint['x']) + (maxY - minY)) / 7; d++) {
            a = {'x': (startPoint['x'] - (maxY - minY)) + (d * 7), 'y': maxY};
            b = {'x': startPoint['x'] + (d * 7), 'y': minY};
            var first = 2;
            var isLine = true;
            for (var n = 0; n < list.length - 1; n++) {
                if (Math.abs(list[n + 1]['x'] - list[n]['x']) != 14) continue;
                p1 = intersection(a, b, list[n], list[n + 1]);
                if (p1) {
                    first++;
                    for (var g = n; g < list.length - 1; g++) {
                        if (Math.abs(list[g + 1]['x'] - list[g]['x']) != 14) continue;
                        p2 = intersection(a, b, list[g], list[g + 1]);
                        if (p2) {
                            isLine = true;
                            for (var k = 0; k < list.length - 1; k++) {
                                p3 = intersection(p1, p2, list[k], list[k + 1]);
                                if (p3 && !isNaN(p3.x) && (p3.x != p1.x && p3.y != p1.y) && (p3.x != p2.x && p3.y != p2.y)) {
                                    isLine = false;
                                    break;
                                }
                            }
                            if (isLine && first % 2 != 0) {
                                line(context, p1.x, p1.y, p2.x, p2.y, 'red', 0.5, 1);
                            }
                        }
                    }
                }
            }
        }
    }
}

// 计算交点
function intersection(a, b, c, d) {
    var area_abc = (a.x - c.x) * (b.y - c.y) - (a.y - c.y) * (b.x - c.x);
    var area_abd = (a.x - d.x) * (b.y - d.y) - (a.y - d.y) * (b.x - d.x);
    if (area_abc * area_abd > 0) return false;
    var area_cda = (c.x - a.x) * (d.y - a.y) - (c.y - a.y) * (d.x - a.x);
    var area_cdb = area_cda + area_abc - area_abd;
    if (area_cda * area_cdb > 0) return false;
    var t = area_cda / (area_abd - area_abc);
    var dx = t * (b.x - a.x),
        dy = t * (b.y - a.y);
    return {x: a.x + dx, y: a.y + dy};
}

// 重绘
function reDraw() {
    context.clearRect(0, 0, canvas.width, canvas.height);
    tw_model();
    draw(null, tw_json);
    draw(null, mb_json);
    draw(null, xl_json);
}

// 为json赋值
function doJson() {
    canvas.addEventListener('mousemove', function (evt) {
        if (temPath) {
            var temperature = ((210.5 * lattice) - evt.offsetY) / (5 * lattice);
            if (tw_json[obj.i].temperature != temperature) {
                tw_json[obj.i].temperature = temperature;
                reDraw();
            }
        } else if (pulsePath) {
            var pulse = 182 - (evt.offsetY / lattice) * 4;
            if (mb_json[obj.i].pulse != pulse) {
                mb_json[obj.i].pulse = pulse;
                reDraw();
            }
        } else if (heartRatePath) {
            var heartRate = 182 - (evt.offsetY / lattice) * 4;
            if (xl_json[obj.i].heartRate != heartRate) {
                xl_json[obj.i].heartRate = heartRate;
                reDraw();
            }
        } else if (coolingPath) {
            var cool = ((210.5 * lattice) - evt.offsetY) / (5 * lattice);
            if (tw_json[obj.i].wljw != cool) {
                tw_json[obj.i].wljw = cool;
                reDraw();
            }
        }
    });
}

//计算两个日期相差几天
function DateDiff(sDate1, sDate2) {
    var aDate, oDate1, oDate2, iDays;
    aDate = sDate1.split("-");
    oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);
    aDate = sDate2.split("-");
    oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);
    iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24);
    return iDays;
}

//画直线
/*
 * @json context
 * @json fromX
 * @json formY
 * @json toX
 * @json toY
 * @json strokeStyle - default is white
 * @json lineWidth
 * */
function line(context, fromX, formY, toX, toY, strokeStyle, lineWidth, py, isDash) {
    context.save();
    if (py == 1) context.translate(0.5, 0.5);
    if (isDash) context.setLineDash([5, 5]);
    context.lineWidth = lineWidth;
    context.beginPath();
    context.strokeStyle = strokeStyle;
    context.moveTo(fromX, formY);
    context.lineTo(toX, toY);
    context.stroke();
    context.restore();
    context.save();
}

tw_title();
tw_model(tw_json); // 描表格
canvas.addEventListener('mousedown', function (event) { // 监听拖拉
    draw(event, tw_json);
    draw(event, mb_json);
    draw(event, xl_json);
    if (temPath) doJson(tw_json);
    if (pulsePath) doJson(mb_json);
    if (heartRatePath) doJson(xl_json);
    if (coolingPath) doJson(tw_json);
    else reDraw();
});
canvas.addEventListener('mouseup', function () {
    if (obj.i != null) {
        var val = null;
        var json = null;
        if (temPath) {
            val = 'temperature';
            json = tw_json;
        }
        if (pulsePath) {
            val = 'pulse';
            json = mb_json;
        }
        if (heartRatePath) {
            val = 'heartRate';
            json = xl_json;
        }
        if (coolingPath) {
            val = 'wljw';
            json = tw_json;
        }
        if (val && json) {
            var roundNum = json[obj.i][val];
            json[obj.i][val] = (Math.round(roundNum * 10)) / 10;
        }
    }
    obj.i = null;
    temPath = false;
    pulsePath = false;
    heartRatePath = false;
    coolingPath = false;
    reDraw();
});

function getTW() {
    tssmList = [], wcyyList = []
    $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryTwByOneBr&parm=" + JSON.stringify(param), function (json) {
        tw_json = json.d.list;
        var date;
        time.setSshDay(json.d.list)
        for (var i = 0; i < tw_json.length; i++) {
            date = new Date(tw_json[i]['date']);
            tw_json[i]['date'] = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
            tw_json[i]['week'] = date.getDay();
            // 加特殊说明
            var font = "";
            if (tw_json[i]['tbsm'] != null && tw_json[i]['tbsm'] != "null" && tw_json[i]['tbsm'] != "") {
                font = tw_json[i]['tbsm'] + (tw_json[i]['tbsmsj'] || '');
                tssmList.push(font);
            } else {
                tssmList.push('');
            }
            // 加未测原因
            if (tw_json[i]['wcyy'] != null && tw_json[i]['wcyy'] != "null" && tw_json[i]['wcyy'] != "") {
                font = tw_json[i]['wcyy'];
                wcyyList.push(font);
            } else {
                wcyyList.push('');
            }
        }
        draw(null, tw_json);                // 体温描点
    });
}

function getMO() {
    $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryMbByOneBr&parm=" + JSON.stringify(param), function (json) {
        mb_json = json.d.list;
        var date;
        for (var i = 0; i < mb_json.length; i++) {
            date = new Date(mb_json[i]['date']);
            mb_json[i]['date'] = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
        }
        draw(null, mb_json);                // 脉搏描点
    });
}

function getXL() {
    $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryXtByOneBr&parm=" + JSON.stringify(param), function (json) {
        xl_json = json.d.list;
        var date;
        for (var i = 0; i < xl_json.length; i++) {
            date = new Date(xl_json[i]['date']);
            xl_json[i]['date'] = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
        }
        draw(null, xl_json);                // 心率描点
    });
}

var time = new Vue({
    el: '#table_1',
    data: {
        currentDay: ['', '', '', '', '', '', ''],
        zyDay: ['', '', '', '', '', '', ''],
        sshDay: ['', '', '', '', '', '', ''],
        currentDays: ['', '', '', '', '', '', ''],
        currentTime: ['', '', '', '', '', '', ''],
    },
    methods: {
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        // 设置日期、住院天数
        setDay: function (startDay, zyDay) {
            // 删除空数据、再赋值
            this.currentDay = [];
            this.currentDays = [];
            this.currentTime = [];
            this.zyDay = [];
            var list = param.sxrq.split('-');
            for (var i = 0; i < 7; i++) {
                var day = new Date((1000 * 60 * 60 * 24 * i) + startDay);
                this.currentDay.push(day.getMonth() + 1 + '月' + day.getDate() + '日');
                this.currentDays.push(this.Appendzero((day.getMonth() + 1)) + '-' + this.Appendzero(day.getDate()));
                var date = new Date(day.getFullYear(), day.getMonth() + 1, day.getDate()).getTime()
                this.currentTime.push(date);
                other.dayObj[day.getDate()] = i;
                if (zyDay > 0) {
                    if (day.getDate() == (parseInt(list[2]))) {
                        for (var j = 0; j < 7; j++) {
                            this.zyDay.push(zyDay - i + j);
                        }
                    }
                } else {
                    list[2] = parseInt(list[2]) + 1;
                }
                zyDay++;

            }
            // 设置呼吸的List
            // hx.setList();
            // 设置血压的List
            xy.setList();
            // 设置其他信息的List
            other.setList();
            time4($("#time").val(), this.currentDays[0]);
            other.getSsts();
        },
        setSshDay: function (val) {
            for (var i = 0; i < val.length; i++) {
                // var day = new Date(this.other_json[i]['clrq']);
                // var num = parseInt(other.dayObj[day.getDate()]);
            }
        }
    }
});
var hx = new Vue({
    el: '#table_2',
    data: {
        hx_val: [],
        hx_list: []
    },
    created: function () {
        this.getHx();
        for (var i = 0; i < 42; i++) this.hx_list.push('');
    },
    methods: {

        getHx: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryHxByOneBr&parm=" + JSON.stringify(param), function (json) {
                hx.hx_val = json.d.list;
                hx.setList();
            });
        },
        setList: function () {
            if (this.hx_val.length == 0 || JSON.stringify(other.dayObj) == '{}') return false;
            for (var i = 0; i < this.hx_val.length; i++) {
                var day = new Date(this.hx_val[i].date);
                var num = parseInt(parseInt(other.dayObj[day.getDate()]) * 6 + ((this.hx_val[i].time - 3) / 4));
                // var _td = $('#table_2 td');
                // if (i % 2 == 0) {
                //     _td.eq(num + 1).addClass('td_bottom');
                // } else {
                //     _td.eq(num + 1).addClass('td_top');
                // }
                if (this.hx_val[i].fx == 0 || this.hx_val[i].fx == null) {
                    this.hx_val[i].fx = "";
                }
                // 是否人工呼吸
                if (this.hx_val[i]['rgfx'] == '1') {
                    Vue.set(this.hx_list, num, "®");
                } else {
                    Vue.set(this.hx_list, num, this.hx_val[i].fx);
                }
            }
            this.dragStyle()
        },
        dragStyle: function () {
            this.$nextTick(function () {
                for (var index = 0; index <this.hx_list.length ; index++) {
                    var prevCs = $('.setClass' + (index - 1))[0], currentCs = $('.setClass' + index)[0];
                    if (index == 0){
                        currentCs.classList.add('td_bottom');
                        continue;
                    }
                    if (prevCs.innerHTML != '') {
                        if (prevCs.classList[1] == 'td_bottom') {
                             currentCs.classList.add('td_top');
                            continue;
                        } else {
                             currentCs.classList.add('td_bottom');
                            continue;
                        }
                    } else {
                        if (prevCs.classList[1] == 'td_bottom') {
                             currentCs.classList.add('td_bottom');
                            continue;
                        } else {
                             currentCs.classList.add('td_top');
                            continue;
                        }
                    }
                }
            })
        },
    }
});
var xy = new Vue({
    el: '#table_3',
    data: {
        xy_val: [],
        xy_list: ['', '', '', '', '', '', '', '', '', '', '', '', '', '']                        // 血压
    },
    created: function () {
        // for (var i = 0; i < 14; i++) this.xy_list.push('');
    },
    methods: {
        setList: function () {
            for (var i = 0; i < other.other_json.length; i++) {
                var day = new Date(other.other_json[i]['clrq']);
                var num = parseInt(other.dayObj[day.getDate()]) * 2;
                Vue.set(this.xy_list, num, other.other_json[i]['swxy']);
                Vue.set(this.xy_list, num + 1, other.other_json[i]['xwxy']);
            }
        }
    }
});
// var printNumber=new Vue({
//     el:'#printNumber',
//     data:{
//         // dqym:right.dqym,
//     },
//     computed:{
//         dqym:function () {
//             return right.dqym
//         }
//     },
// })
function time4(val, day) {
    var sa = val.split('-');
    return panel.jsons.sxrq = sa[0] + '-' + day;
}
var other = new Vue({
    el: '#table_4',
    data: {
        csqxContent: {},
        N03004200239: [],
        other_json: [],                     // 其他信息的list
        titleTime: [],                      // 日期的list
        startTime: null,                    // 时间段的开始时间
        dayObj: {},                         // 日期的object
        rlList: ['', '', '', '', '', '', ''],
        clList: ['', '', '', '', '', '', ''],
        dbList: ['', '', '', '', '', '', ''],
        tzList: ['', '', '', '', '', '', ''],
        sgList: ['', '', '', '', '', '', ''],
        xbList: ['', '', '', '', '', '', ''],
        stList: ['', '', '', '', '', '', ''],
        szList: ['', '', '', '', '', '', ''],
        mxList: ['', '', '', '', '', '', ''],
        qtxm1List: ['', '', '', '', '', '', ''],
        qtxm2List: ['', '', '', '', '', '', ''],
        qtxm3List: ['', '', '', '', '', '', ''],
        qtxm4List: ['', '', '', '', '', '', ''],
        qtxm5List: ['', '', '', '', '', '', ''],
        qtxm6List: ['', '', '', '', '', '', ''],
        qtxm7List: ['', '', '', '', '', '', ''],
        qtxm8List: ['', '', '', '', '', '', ''],
        qtxm9List: ['', '', '', '', '', '', ''],
        qtxm10List: ['', '', '', '', '', '', ''],
        qtxm11List: ['', '', '', '', '', '', ''],
        qtxm12List: ['', '', '', '', '', '', ''],
        jcdxList: ['', '', '', '', '', '', ''],
        gcList: ['', '', '', '', '', '', ''],
        bldnList: ['', '', '', '', '', '', ''],
        gcqdbcsList: ['', '', '', '', '', '', ''],
        ylList: ['', '', '', '', '', '', ''],
        yrList: ['', '', '', '', '', '', ''],
        LssshDay: ['', '', '', '', '', '', ''],
        xbcList: ['', '', '', '', '', '', ''],
        sstsNum: undefined,
        // qtxmList: right.qtxmList?right.qtxmList:""
    },
    created: function () {
        this.getOther();
        this.getCsqx();
    },
    methods: {
        getCsqx:function(){
            var parm = {"ylbm": 'N030042002', "ksbm": userNameBg.Brxx_List.ryks};
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03004200239":// 展示信息
                                        other.N03004200239 = csjson.csz.split(',');
                                    break;
                                case "N03004200240":// 体温单大小便次 显示导尿灌肠标志
                                    if (csjson.csz) {
                                        other.csqxContent.N03004200240 = csjson.csz;
                                    }
                                    break;
                            }
                        }
                } else {
                    malert("参数权限获取失败!"+json.c,'top','defeadted');
                }
            });
        },
        getSsts: function () {
            other.LssshDay = ['', '', '', '', '', '', ''], other.sstsNum = undefined, time.sshDay = ['', '', '', '', '', '', ''];
            var flag = false;
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=querySstsByOneBr&parm=" + JSON.stringify(panel.jsons), function (json) {
                if (json.a == '0' && json.d) {
                    for (var i = 0; i < json.d.list.length; i++) {
                        var reDate = new Date(json.d.list[i]['sssj']);
                        var jsonTime = new Date(reDate.getFullYear(), reDate.getMonth() + 1, reDate.getDate()).getTime();
                        var num = json.d.list[i].ssts;
                        ddd:
                            for (var j = 0; j < time.currentTime.length; j++) {
                                if (jsonTime == time.currentTime[j]) {
                                    other.sstsNum = j;
                                    break;
                                }
                            }
                        if (!other.sstsNum) {
                            for (var j = 0; j < time.currentTime.length; j++) {
                                if (jsonTime > time.currentTime[j]) {
                                    flag = true
                                } else {
                                    flag = false
                                }
                            }
                        }
                        if (other.sstsNum) {
                            for (var n = other.sstsNum; n < 7; n++) {
                                var sum = n == other.sstsNum ? num : other.LssshDay[n - 1] + 1;
                                if (sum <= 14) {
                                    if (time.sshDay[n]) {
                                        var d = (sum) + '/' + (other.LssshDay[n])
                                        Vue.set(time.sshDay, n, d)
                                        Vue.set(other.LssshDay, n, sum)
                                    } else {
                                        Vue.set(time.sshDay, n, sum)
                                        Vue.set(other.LssshDay, n, sum)
                                    }
                                } else {
                                    for (var a = n; a < 7; a++) {
                                        time.sshDay.push('')
                                        other.LssshDay.push('')
                                    }
                                    break;
                                }
                            }
                        } else {
                            if (flag) return false
                            for (var l = 0; l < 7; l++) {
                                var sum = l == 0 ? num : other.LssshDay[l - 1] + 1;
                                if (sum <= 14) {
                                    if (time.sshDay[l]) {
                                        var d = (sum) + '/' + (other.LssshDay[l])
                                        Vue.set(time.sshDay, l, d)
                                        Vue.set(other.LssshDay, l, sum)
                                    } else {
                                        Vue.set(time.sshDay, l, sum)
                                        Vue.set(other.LssshDay, l, sum)
                                    }
                                } else {
                                    if (time.sshDay.length < 7) {
                                        for (var a = l; a < 7; a++) {
                                            time.sshDay.push('')
                                            other.LssshDay.push('')
                                        }
                                    }
                                    break;
                                    // Vue.set(other.LssshDay,l,'')
                                }

                            }
                        }
                    }
                }
            });
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        getOther: function () {
            panel.time3(panel.time);
            context.clearRect(0, 0, 672, 560);
            contextTitle.clearRect(0, 0, 672, 28);
            tw_model(tw_json);
            tw_title()
            this.rlList = ['', '', '', '', '', '', '']
            this.clList = ['', '', '', '', '', '', '']
            this.dbList = ['', '', '', '', '', '', '']
            this.tzList = ['', '', '', '', '', '', '']
            this.sgList = ['', '', '', '', '', '', '']
            this.xbList = ['', '', '', '', '', '', '']
            this.stList = ['', '', '', '', '', '', '']
            this.szList = ['', '', '', '', '', '', '']
            this.mxList = ['', '', '', '', '', '', '']
            this.qtxm1List = ['', '', '', '', '', '', '']
            this.qtxm2List = ['', '', '', '', '', '', '']
            this.qtxm3List = ['', '', '', '', '', '', '']
            this.qtxm4List = ['', '', '', '', '', '', '']
            this.qtxm5List = ['', '', '', '', '', '', '']
            this.qtxm6List = ['', '', '', '', '', '', '']
            this.qtxm7List = ['', '', '', '', '', '', '']
            this.qtxm8List = ['', '', '', '', '', '', '']
            this.qtxm9List = ['', '', '', '', '', '', '']
            this.qtxm10List = ['', '', '', '', '', '', '']
            this.qtxm11List = ['', '', '', '', '', '', '']
            this.qtxm12List = ['', '', '', '', '', '', '']
            this.jcdxList = ['', '', '', '', '', '', '']
            this.gcList = ['', '', '', '', '', '', '']
            this.bldnList = ['', '', '', '', '', '', '']
            this.gcqdbcsList = ['', '', '', '', '', '', '']
            this.ylList = ['', '', '', '', '', '', '']
            this.yrList = ['', '', '', '', '', '', '']
            this.xbcList = ['', '', '', '', '', '', '']
            hx.hx_list = []
            for (var i = 0; i < 42; i++){
                hx.hx_list.push('');
                var currentCs = $('.setClass' + i)[0];
                currentCs.classList.remove('td_bottom','td_top')
            }
            xy.xy_list = ['', '', '', '', '', '', '', '', '', '', '', '', '', '']
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryQtjl&parm=" + JSON.stringify(param), function (json) {
                if (json.a == '0' && json.d != null) {
                    other.other_json = json.d.list;
                    // 设置标题时间
                    time.setDay(json.d.time.ksrq, json.d.time.zyts);
                    // 设置住院天数
                    other.titleTime = json.d.sd;
                    // 设置时间段的开始时间，以便计算图形位置
                    other.startTime = json.d.sd[0];
                    // 画时间段
                    fillTime();
                    // 获取体温单
                    getTW();
                    // 获取脉搏
                    getMO();
                    // 获取心率
                    getXL();
                    hx.getHx();
                }

            });
        },
        setList: function () {
            for (var i = 0; i < this.other_json.length; i++) {
                var day = new Date(this.other_json[i]['clrq']);
                var num = parseInt(other.dayObj[day.getDate()]);
                /*if (this.other_json[i]['dbcs'] == 0 || this.other_json[i]['dbcs'] == null) {*/
                if (this.other_json[i]['dbcs'] == null) {
                    this.other_json[i]['dbcs'] = "";
                }
                console.log("大便");
                console.log(this.other_json[i]['dbcs']);
//               	if (this.other_json[i]['dbcs'] == 0) {
//                    this.other_json[i]['dbcs'] = "";
//                }
                Vue.set(this.rlList, num, (this.other_json[i]['srl']));
                Vue.set(this.yrList, num, this.other_json[i]['yrl']);
                Vue.set(this.ylList, num, this.other_json[i]['yll']);
                // Vue.set(this.dbList, num, (this.other_json[i]['dbcs']) +(this.other_json[i]['dbl']));
                if(other.csqxContent.N03004200240 == '1'){//体温单 大/小便次 显示灌肠/导尿标志
                    if(this.other_json[i]['gc']){
                        if(this.other_json[i]['gc']>1){
                            Vue.set(this.dbList, num, this.other_json[i]['dbcs']?(this.other_json[i]['dbcs'] + "/" + (this.other_json[i]['gc'] ? this.other_json[i]['gc'] : "") + "E") : "");
                        }else{
                            Vue.set(this.dbList, num, this.other_json[i]['dbcs']?(this.other_json[i]['dbcs'] + "/E") : "");
                        }
                    }else{
                        Vue.set(this.dbList, num, this.other_json[i]['dbcs']?(this.other_json[i]['dbcs']) : "");
                    }
                    if(this.other_json[i]['bldn']){
                        if(this.other_json[i]['bldn']>1){
                            Vue.set(this.xbcList, num, this.other_json[i]['xbc']?(this.other_json[i]['xbc'] + "/" + this.other_json[i]['bldn'] + "C") : "");
                        }else{
                            Vue.set(this.xbcList, num, this.other_json[i]['xbc']?(this.other_json[i]['xbc'] + "/C") : "");
                        }
                    }else{
                        Vue.set(this.xbcList, num, this.other_json[i]['xbc']?(this.other_json[i]['xbc']) : "");
                    }
                }else{
                    Vue.set(this.xbcList, num, (this.other_json[i]['xbc'] || ''));
                    Vue.set(this.dbList, num, (this.other_json[i]['dbcs']));
                }
                Vue.set(this.tzList, num, this.other_json[i]['tz']);
                Vue.set(this.sgList, num, this.other_json[i]['sg']);
                Vue.set(this.xbList, num, this.other_json[i]['xbl']);
                Vue.set(this.stList, num, this.other_json[i]['st']);
                Vue.set(this.szList, num, this.other_json[i]['sz']);
                Vue.set(this.mxList, num, this.other_json[i]['mx']);
                Vue.set(this.qtxm1List, num, this.other_json[i]['qtxm1']);
                Vue.set(this.qtxm2List, num, this.other_json[i]['qtxm2']);
                Vue.set(this.qtxm3List, num, this.other_json[i]['qtxm3']);
                Vue.set(this.qtxm4List, num, this.other_json[i]['qtxm4']);
                Vue.set(this.qtxm5List, num, this.other_json[i]['qtxm5']);
                Vue.set(this.qtxm6List, num, this.other_json[i]['qtxm6']);
                Vue.set(this.qtxm7List, num, this.other_json[i]['qtxm7']);
                Vue.set(this.qtxm8List, num, this.other_json[i]['qtxm8']);
                Vue.set(this.qtxm9List, num, this.other_json[i]['qtxm9']);
                Vue.set(this.qtxm10List, num, this.other_json[i]['qtxm10']);
                Vue.set(this.qtxm11List, num, this.other_json[i]['qtxm11']);
                Vue.set(this.qtxm12List, num, this.other_json[i]['qtxm12']);
                Vue.set(this.jcdxList, num, this.other_json[i]['jcdx']);
                Vue.set(this.gcList, num, this.other_json[i]['gc'] ? (this.other_json[i]['gc'] + "/E") : "");
                Vue.set(this.bldnList, num, this.other_json[i]['bldn'] ? (this.other_json[i]['bldn'] + "/C") : "");
                Vue.set(this.gcqdbcsList, num, this.other_json[i]['gcqdbcs'] ? this.other_json[i]['gcqdbcs'] : "");
            }
        },
        sum: function (number) {

        },
    }
});



function prev() {
    var aa=new Date(panel.time);
    aa.setDate(aa.getDate()-7);
    var pared=new Date(aa).getTime();
    panel.time=formatTime(pared,'date');
    other.getOther()
}
function next() {
    var aa=new Date(panel.time);
    aa.setDate(aa.getDate()+7);
    var nextd=new Date(aa).getTime();
    panel.time=formatTime(nextd,'date');
    other.getOther()
}
