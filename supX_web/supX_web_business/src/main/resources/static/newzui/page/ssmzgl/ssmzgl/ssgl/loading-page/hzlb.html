<link rel="stylesheet" href="loading-page/hzlb.css">
<div id="hzlb">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getHzlb()">刷新</button>
        </div>
    </div>
    <div class="flex-container padd-t-10 padd-b-20" style="flex-wrap: wrap;">
        <div class=" flex-container flex-align-c padd-l-10 margin-t-5">
            <span class="padd-r-5">开始日期</span>
            <div class="flex-container flex-align-c">
                <input class="zui-input" placeholder="请选择开始时间" id="timeStart" type="text"/><span
                    class="padd-r-5 padd-l-5">至</span>
                <input class="zui-input" placeholder="请选择结束时间" id="timeEnd" type="text"/>
            </div>
        </div>
        <!-- <div class="flex-container flex-align-c  margin-l-10 margin-t-5">
                <label class="whiteSpace margin-r-5 ft-14">检索科室</label>
                <select-input :cs="true" @change-data="resultChangeOd" :not_empty="false"
                              :child="allKs" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.ksbm"
                              :name="'popContent.ksbm'">
                </select-input>
        </div> -->
        <div class="flex-container flex-align-c margin-l-10 margin-t-5">
            <label class="whiteSpace  ft-14 margin-r-5">手术类型</label>
            <select-input @change-data="commonResultChange1" :not_empty="true" :child="sslx_tran1"
                          :index="sslx" :val="sslx" :search="true"
                          :name="'sslx'">
            </select-input>
        </div>
        <div class="flex-container flex-align-c margin-l-10 margin-t-5">
            <label class="whiteSpace  ft-14 margin-r-5">排台状态</label>
            <select-input @change-data="commonResultChange" :not_empty="true" :child="pt_tran"
                          :index="ptStatus" :val="ptStatus" :search="true"
                          :name="'ptStatus'">
            </select-input>
        </div>
        <div class="flex-container flex-align-c margin-l-10 margin-t-5">
            <label class="whiteSpace  ft-14 margin-r-5">手术状态</label>
            <select-input @change-data="ssbzResultChange" :not_empty="true" :child="ss_tran"
                          :index="ssbz" :val="ssbz" :search="true"
                          :name="'ssbz'">
            </select-input>
        </div>
        <div class=" margin-l-10 margin-t-5">
            <span class="">检索</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" placeholder="住院号/姓名" v-model="pageState.jiansuoVal"
                       @keyUp.enter="getHzlb()" type="text"/>
            </div>
        </div>

        <!-- <div class="flex-container flex-align-c">
            <label class="padd-r-5" style="padding-top: 3px">历史手术</label>
                <div class="zui-table-cell text-center"><input class="green" v-model="val" type="checkbox" ><label @click="doCheck" @dblclick.stop></label></div>
        </div> -->

    </div>
    <!--检索字段end-->

    <!--格子ui begin-->
    <div class="grid-box kp  bg-fff zui-table-body" ref="kp" v-cloak align="center" v-if="num==0" id="brCard">
        <div :class="{'zt-xrybr': br.jrrybz==1}" style="padding: 0px 7.5px; width: 370px; display: inline-block;"
             v-for="(br,index) in pageState.brList"
             v-if="pageState.brList.length!=0">
            <div v-if="!br.isKc" class="userWidth position" :class="br.sslx=='0' ?'jzBr':''">
                <div class="header-text">
                    <div class="flex-container flex-align-b text-left padd-t-5">
                        <!--                        @dblclick="openYz(br)"-->
                        <span class="userName title ">{{br.brxm}}</span>
                        <span class="username-cwh">{{br.rycwbh}}床</span>
                        <span class="margin-l-10">{{br.total}}元</span>
                    </div>
                    <p class="text-left">
                        <span class="margin-r-10">{{brxb_tran[br.brxb]}}</span>
                        <span class="margin-r-10">{{br.nl}}{{nldw_tran[br.nldw]}}</span>
                        <span class="margin-r-10">{{br.zyh}}</span>
                        <span class="margin-r-10">{{br.ryksmc}}</span>
                        <!--<span class="username-nl">{{list.zyts}}</span>-->
                    </p>
                </div>
                <div class="header-text">
                    <p class="text-left">
                        <span class="margin-r-10">切口分类：{{ssqkfl_tran[br.qkfl]}} 类</span>
                        <span class="margin-r-10">手术等级: {{ssldj_tran[br.ssdj]}} 级</span>

                        <!--<span class="username-nl">{{list.zyts}}</span>-->
                    </p>
                    <p class="text-left">
                        <span class="margin-r-10">麻醉方式: {{br.mzfsmc}} </span>
                    </p>
                    <p class="text-left">
                        <span class="margin-r-10">传染疾病: {{crjb}}</span>
                    </p>


                </div>
                <div class="main-content flex-container flex-align-c" style="justify-content: space-between;">
                    <p class="text-over">{{br.sqzd || ""}}</p>
                    <span :class="[br.sslx == '0' ?'red_1': '']">{{sslx_tran[br.sslx]}}</span>
                </div>

                <div class="main-content flex-container flex-align-c" style="justify-content: flex-end;">
                    <p class="margin-l-10">
                    	<span :class="[br.ssptbz == '1' ?'green' :'red']">
                    		{{br.ssptbz == '0' ? '未排台' : '已排台'}}
                    	</span>
                    </p>
                    <p class="margin-l-10" v-if="br.ssptbz == '1'">
                    	<span :class="[br.ssbz == '1' ?'green' :'red']">
                    		{{br.ssbz == '0' ? '未手术' : '已手术'}}
                    	</span>
                    </p>
                </div>
                <div class="main-content flex-container flex-align-c" style="justify-content: space-between;">
				    <span>
				    	{{br.zsmc || ""}}
				    </span>
                </div>
                <div class="main-content flex-container flex-align-c" style="justify-content: space-between;">
                    <p>
                        <span>{{fDate(br.jhrq,'datetime')}}</span>
                    </p>
                </div>

                <div v-if="br.ssptbz == '1'" class="main-content flex-container flex-align-c">
                    <p class="text-over">安排日期：{{br.aprq }}</p>
                </div>
                <div v-if="br.ssptbz == '1'" class="main-content flex-container flex-align-c">
                    <p class="text-over">手&nbsp;&nbsp;术&nbsp;间：{{br.ssjmc }}</p>
                </div>
                <div class=" padd-b-10  flex-container flex-align-c">
                    <div class="flex-one text-left">
                        <!--                        <span :datafld="br.pkhbz"  :class="{'visibility':br.pkhbz == '1'}" class="userName-pin" data-title="贫困病人"><img src="/newzui/pub/image/pin.png"></span>-->
                        <!--                        <span :datafld="br.jrrybz" :class="{'visibility':br.jrrybz == '1'}"  class="userName-pin xrybr" data-title="新入院病人">新</span>-->
                        <!--
                        <span v-if="br.hzbz == '1'" class="userName-pin hz" data-title="会诊病人">会</span>
                         -->
                        <!--                        <span :datafld="br.dzxshow" :class="{'visibility':br.dzxshow}"   class="userName-pin w" data-title="未执长期医嘱">未</span>-->
                        <!--                        <span :datafld="br.bqcybz" :class="{'visibility':br.bqcybz == '1' && br.zyzt == '0'}" class="userName-pin j" data-title="病区出院">出</span>-->
                        <!--                        <span :datafld="br.bqcybz" :class="{'visibility':br.bqcybz == '1' && br.zyzt == '0'}"  class="userName-pin j" data-title="结算出院">结</span>-->
                        <!--                        <span :datafld="br.yjhj+br.dbje-br.fyhj" :class="{'visibility':br.yjhj+br.dbje-br.fyhj<0}"   class="userName-pin r" data-title="欠费">欠</span>-->
                        <!--                        <span :datafld="br.dtzshow" :class="{'visibility':br.dtzshow}"   class="userName-pin r" data-title="停嘱">停</span>-->
                        <!--                    </div>-->
                        <!--                    <div class="text-left margin-r-5">-->
                        <span class="zyys text-over">{{br.zyysxm}}</span>
                    </div>
                    <div class="cz-butt" :id="'cz-'+index" @dblclick.stop>
                        <span class="butt" @click.stop="czClick(index, $event)">操作</span>
                        <div class="content flex-container flex-wrap-w">
                            <div v-for="(cz, $index) in czList" class="cont-butt" @click="cz.clickBc(index,br)">
                                {{cz.name }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 空床 -->
            <div v-if="br.isKc" class="userWidth kongchuang" style="height: 120px;">
                <p class="chuangweihao text-left">{{br.rycwbh}}床</p>
            </div>
        </div>
        <div style="padding: 0 7.5px;width: auto;display: inline-block" v-for="list in brk_listD"
             v-if="pageState.brList.length!=0"></div>
        <p v-if="pageState.brList.length==0" class=" noData text-center">暂无数据...</p>
    </div>
    <!--格子ui end-->
    <div class="action-bar fixed">
        <div class="hzts-message">
            <span @click="filterFunz()" class="box">全部&ensp;<span class="color-1abc9c"><span class="font-22">{{ noticeContent.zyrs || 0 }}</span>人</span></span>
            <span @click="filterFun(1,'jrrybz')" class="box">今日&ensp;<span class="color-1abc9c"><span class="font-22">{{ noticeContent.jrry || 0 }}</span>人</span></span>
            <span @click="filterFun(1,'hldj')" class="box">未排台&ensp;<span class="color-fe3f3f"><span class="font-22">{{ noticeContent.tjhl || 0 }}</span>人</span></span>
            <span @click="filterFun(2,'hldj')" class="box">已排台&ensp;<span class="color-fa6969"><span class="font-22">{{ noticeContent.yjhl || 0 }}</span>人</span></span>
        </div>
    </div>
</div>


<script src="loading-page/hzlb.js"></script>
