<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!--杨松柏-->
    <title>定性质控</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="zk.css" rel="stylesheet"/>
</head>
<style>
    .zui-table-view .fieldlist {
        display: none;
    }
    .slimScrollDiv{
        overflow: inherit!important;
    }
</style>
<body class="skin-default">
<div class="wrapper" >
    <div class="" style="border:none; margin-top: 5px;" id="jyxm_icon">
        <div class="xmzb-content" style="padding-bottom: 40px;background: #fff">
            <div class="xmzb-content-left zui-table-view">
                <div class="xmzb-top">
                    <div class="col-x-12 ysb_list-50">
                        <button class="zui-btn btn-primary  padd-r5 icon-sx" @click="getsbbm">刷新</button>
                    </div>
                    <div class="col-x-6 xmzb-top-left">
                        <!--<i>检验设备</i>-->
                        <!--<i>-->
                            <!--<select-input @change-data="resultChange"-->
                                          <!--:child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="params.zxsb"-->
                                          <!--:search="true" :name="'params.zxsb'">-->
                            <!--</select-input>-->
                        <!--</i>-->
                        <label class="zui-form-label" >检验设备</label>
                        <select-input @change-data="resultChange"
                                      :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="params.zxsb"
                                      :search="true" :name="'params.zxsb'">
                        </select-input>
                    </div>
                </div>
                <div class="content-left-top">
                    <i>序号</i>
                    <i>质控项目</i>
                    <i>批号</i>
                    <i class="position">正常值<em class="icon-bgzdgl bgfdgl-right"></em></i>
                </div>
                <ul class="content-left-list zui-scroll-left">
                    <li v-for="(list,index) in getsblist" @click="getlist(list.zkxm,list)" :tabindex="index" @click="checkSelect([index,'some','getsblist'],$event)" :class="[{'table-hovers':isChecked[index]}]">
                        <div class="i"><i class="title overflow1" >{{index+1}}</i></div>
                        <div class="i"><i class="title overflow1" v-text="list.zbmc"></i></div>
                        <div class="i"><i class="title overflow1" v-text="list.zkwph"></i></div>
                        <div class="i"><i class="title overflow1" v-text="list.jg">正常值</i></div>
                    </li>
                </ul>
            </div>
            <div class="xmzb-content-right zui-table-view">
                <div class="xmzb-top">
                    <div class="col-x-12 ysb_list-50">
                        <div class="col-x-11">
                            <button class="zui-btn btn-primary  padd-r5" @click="jglr"><span class="icon-ms"></span>结果录入</button>
                            <button class="zui-btn btn-primary-b icon-yl  padd-r5" @click="hsjg">获取结果</button>
                            <button class="zui-btn btn-primary-b icon-sc-header  padd-r5" @click="scall()">删除</button>
                            <button class="zui-btn btn-primary-b icon-dysq  padd-r5" @click="dy">打印</button>
                        </div>
                        <div class="col-x-1">
                            <div class="col-x-6 text-center cursor cursor-bg "  @click="showlist(0)" :class="num==0?'cursor-bg-active':''">
                                <!--icon-bg-->
                                <p class=" icon-bg-style " :class="num==0?'cursor-active-bg':'icon-bg'"></p>
                                <p class="" :class="num==0?'text':'cursor-text'">表格</p>
                            </div>
                            <div @click="showlist(1)"  class="col-x-6 text-center cursor cursor-tx cursor-bg-active">
                                <p class="icon-bg-style" :class="num==1?'cursor-tx-active':'icon-tx'" @click="getyue()"></p>
                                <p class="" :class="num==1?'text':'cursor-text'" @click="getyue()">图形</p>
                            </div>
                        </div>
                    </div>
                    <!--:class="num==1?'hideList':''"-->
                    <div class="col-x-6 xmzb-top-left" >
                        <i>质控日期</i>
                        <i class=""><input type="text" v-model="time" class="zui-input  sqdate"/></i>
                    </div>
                    <!--<div class="col-x-6 xmzb-top-left " :class="num==0?'hideList':''">-->
                        <!--<i>申请日期</i>-->
                        <!--<i class="position"><span class="icon-rl icon-abs"></span><input type="text" v-model="time"  class="zui-input zkdate" style="padding-left: 32px;"/></i>-->
                    <!--</div>-->
                </div>
                <!--style="display: none"-->
                <div class="content-right-top" :class="num==1?'hideList':''">
                    <div class="i" style="width: 50px"><i><input-checkbox @result="reCheckBox" :list="'getchildlist'" :type="'all'" :val="isCheckAll"></input-checkbox></i></div>
                    <div class="i"><i class="title overflow1">序号</i></div>
                    <div class="i"><i class="title overflow1">质控项目</i></div>
                    <div class="i"><i class="title overflow1">日期</i></div>
                    <div class="i"><i class="title overflow1">批号</i></div>
                    <div class="i"><i class="title overflow1">结果值</i></div>
                    <div class="i"><i class="title overflow1">产地</i></div>
                    <div class="i"><i class="title overflow1">操作员</i></div>
                    <div class="i position"><i class="title overflow1 " >操作<em class="icon-bgzdgl bgfdgl-right"></em></i></div>
                </div>
                <!--style="display: none"-->
                <div style="height: 100%" :class="num==1?'hideList':''">
                    <ul class="content-right-list " >
                        <li v-for="(list,index) in getchildlist" :tabindex="index" @click="checkSelect([index,'some','getchildlist'],$event)" :class="[{'table-hovers':isChecked[index]}]">
                            <div class="i" style="width: 50px"><i><input-checkbox @result="reCheckBox" :list="'getchildlist'" :type="'some'" :which="index" :val="isChecked[index]"></input-checkbox></i></div>
                            <div class="i"><i class="title overflow1" v-text="index+1"></i></div>
                            <div class="i"><i class="title overflow1" v-text="list.zbmc"></i></div>
                            <div class="i"><i class="title overflow1 " >{{list.rq|formDate}}</i></div>
                            <div class="i"><i class="title overflow1" v-text="list.zkwph"></i></div>
                            <div class="i"><i class="title overflow1" v-text="list.jg">结果值</i></div>
                            <div class="i"><i class="title overflow1" v-text="list.cd">靶值</i></div>
                            <div class="i"><i class="title overflow1" v-text="list.czy">标准差</i></div>
                            <div class="i"><i><span class="icon-bj"></span><span @click="sc(list,list.zbxm)" class="icon-sc"></span></i></div>
                        </li>

                    </ul>
                </div>
                <!--图形-->
                <!--style="display: none"-->
                    <div class="col-x-12 left-15 " :class="num==0?'hideList':''">
                        <h3 class="text-center y-size-text">定性质控统计表</h3>
                        <div class="col-x-12 y-margin-bottom-7">
                            <p class="col-x-4 " v-if="getyuelist">质控品批号：{{getyuelist[0]== undefined ? '' :  getyuelist[0].zkwph}}</p>
                            <p class="col-x-4 text-center" v-if="getyuelist">质控品产地：{{getyuelist[0]== undefined ? '' : getyuelist[0].cd}}</p>
                            <p class="col-x-4 text-center" v-if="getyuelist">质控日期：{{time}}</p>
                        </div>

                    </div>
                    <div class="col-x-12" style="height: 73%;position: relative;overflow: scroll;" :class="num==0?'hideList':''">
                        <div class="content-right-list-bottom" style="height: 96% ">
                            <div class="header" style="position: absolute;top: 0;" :style="{width:(curMonthDays+2)*100+'px'}">
                                <span class="header-color y-line">项目</span>
                                <span class="header-color y-line">靶值</span>
                                <span class="header-color y-line" v-for="list in arr" v-text="list"></span>
                            </div>
                                <ul class="zui-scroll-right" style="margin-top: 34px" >
                                    <!--<div :style="{width:(curMonthDays+2)*100+'px'}">-->
                                <li class="content" v-for="(item,index) in zktdata" >
                                    <p >
                                        <span class="content-color y-line">{{zktdata[index].zbmc}}</span>
                                        </p>
                                    <p >
                                        <span class="content-color y-line" >{{zktdata[index].jg}}</span>
                                    </p>
                                    <p v-for="list in item.lisZkjlDxModel">

                                        <span class="content-color y-line" v-text="list.jg">2.28</span>
                                        <!--<span class="content-color y-line">{{list.rq|formDate}}</span>-->
                                    </p>
                                </li>
                                    <!--</div>-->
                            </ul>
                            <div style="clear: both"></div>
                        </div>
                </div>
            </div>
        </div>


        <div id="pop">
            <!--<transition name="pop-fade">-->
            <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
            <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}"
                 style="height: max-content;padding-bottom: 20px">
                <div class="layui-layer-title " v-text="title"></div>
                <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn"
                                                                    @click="isShowpopL=false,isShow=false">&times;</i></span>
                <div class="layui-layer-content">
                    <div class=" layui-mad layui-height">确定删除质控记录：<span class="success">{{name}}</span> 吗？</div>
                </div>
                <div class="zui-row buttonbox">
                    <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
                    <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
                </div>
            </div>
            <!--</transition>-->
        </div>
    </div>
        <!--编辑-->
        <div id="silde" class="jyxm">
            <transition name="left-fade">
                <!--ng-hide-->
                <div class="zui-form side-form  pop-width  bcsz-layer ng-hide" id="brzcList" style="height: 100%;padding-top: 0px;width: 600px">
                    <div class="layui-layer-title " v-text="title"></div>
                    <span class="layui-layer-setwin" style="top: 0;">
                <i class="color-btn"@click="close">&times;</i></span>
                    <div class="layui-layer-content">
                        <div class=" layui-mad ysb-input" >
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">中文名称</p> <input class="zui-input ysb-zui-wid" type="text" disabled placeholder="" v-model="dxObj.zbmc" value="" autocomplete="auto"></div></div>
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">&emsp;&emsp;简称</p> <input class="zui-input ysb-zui-wid"type="text" disabled placeholder="" v-model="dxObj.ywmc" value="" autocomplete="auto"></div></div>
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">&emsp;&emsp;批号</p> <input class="zui-input ysb-zui-wid"type="text" disabled  placeholder="" value="" v-model="dxObj.zkwph" autocomplete="auto"></div></div>
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">&emsp;&emsp;产地</p> <input class="zui-input ysb-zui-wid"type="text"  placeholder="" value="" v-model="dxObj.cd" autocomplete="auto"></div></div>
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">检验设备</p>
                               <div class="ysb-zui-wid" style="float: left;">
                                   <select-input @change-data="resultChange"
                                                 :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :disabled="true" :val="dxObj.sbbm"
                                                 :search="true" :name="'dxObj.sbbm'">
                                   </select-input>
                               </div>
                            </div></div>
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">检测日期</p> <input class="zui-input ysb-zui-wid jcrq"  placeholder="" autocomplete="auto"></div></div>
                            <div class="col-x-6"><div class="zui-bottom position"><p class="zui-top zui-text-flex">检验结果</p> <input class="zui-input ysb-zui-wid" value="" v-model="dxObj.jgx" placeholder="" style="padding-right: 48px;" autocomplete="auto"><span class="y-right"></span></div></div>
                        </div>
                    </div>
                    <div class="zui-row buttonbox" style="height: 50px;line-height: 50px;position: absolute;bottom: 10px">
                        <button class="zui-btn table_db_esc btn-default" @click="close">取消</button>
                        <button class="zui-btn btn-primary table_db_save" @click="save">保存</button>
                    </div>
                </div>
            </transition>
        </div>
        <div  class="jyxm">
            <transition name="left-fade">
                <!--ng-hide-->
                <div class="zui-form side-form  pop-width ng-hide  bcsz-layer " id="brzcList1" style="height: 100%;padding-top: 0px;width: 600px">
                    <div class="layui-layer-title " v-text="title"></div>
                    <span class="layui-layer-setwin" style="top: 0;">
                <i class="color-btn" @click="isShow=false">&times;</i></span>
                    <div class="layui-layer-content">
                        <div class=" layui-mad ysb-input" >
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">执行设备</p> <input class="zui-input ysb-zui-wid" type="text" disabled placeholder="雅培i1000发光免疫" autocomplete="auto"></div></div>
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">质控项目</p> <input class="zui-input ysb-zui-wid"type="text" disabled placeholder="红细胞" autocomplete="auto"></div></div>
                            <div class="col-x-6"><div class="zui-bottom"><p class="zui-top zui-text-flex">质控类型</p> <input class="zui-input ysb-zui-wid"type="text"  placeholder="" value="低值" autocomplete="auto"></div></div>
                            <div class="col-x-12">
                                <div class="zui-bottom"><p class="zui-top">多规则质控评价</p>
                                    <textarea placeholder="请输入描述内容" class="y-textarea"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="zui-row buttonbox" style="height: 50px;line-height: 50px;position: absolute;bottom: 10px">
                        <button class="zui-btn table_db_esc btn-default" @click="close">取消</button>
                        <button class="zui-btn btn-primary table_db_save" @click="save">保存</button>
                    </div>
                </div>
            </transition>
        </div>

    <script src="zk.js"></script>
    <script type="text/javascript">
        // $(".zui-table-view").uitable();
        $(".zui-scroll-right,.zui-scroll-left,.content-right-list").uiscroll({
            height: '100%',
            size: '6px',
            opacity: .3,
            disableFadeOut: true,
            position: 'right',
            color: '#000'
        });
    </script>
</div>
</body>
</html>