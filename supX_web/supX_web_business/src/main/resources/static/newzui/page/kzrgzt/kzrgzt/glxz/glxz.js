(function () {

    //顶部工具栏
    var tool=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
        },
        methods:{
            //新增小组
            AddModel:function () {
            brzcList.title='新增小组';
                brzcList.changeShow=true;
                brzcList.open();
            },
            //刷新和检索
            getData:function () {

            },

        }
    });

    //管理小组列表
    var wxdjList = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data: {

        },

        updated:function () {
            changeWin()
        },
        methods: {
            //请求后台查询列表信息
            getData : function(){
                //加载动画效果
                common.openloading('.zui-table-view')
                //数据请求结束时关闭


                 common.closeLoading()
            },
            //编辑
            Edit:function () {
            brzcList.title='编辑小组';
            brzcList.changeShow=true;
              brzcList.open();
            },
            //变更记录
            ChangeRecord:function () {
                brzcList.open();
                brzcList.title='变更记录';
                brzcList.changeShow=false;
            },
            //删除
            remove:function () {
                if (common.openConfirm("确认删除该条信息吗？", function () {


                    })) {
                    return false;
                }
            }
        }
    });



    var brzcList = new Vue({
        el : '#brzcList',
        mixins : [ dic_transform, baseFunc, tableBase, mformat ,checkData,scrollOps,mConfirm],
        data : {
            title : '',
            num:0,
            zt:'0',
            editText:false,
            changeShow:false,
            userName:false,
            objabsolute:{},
            BgList:[],
            popContent:{},
        },
        methods : {
            // 关闭
            close: function() {
                this.num=0;
            },
            open : function() {
                this.num=1;
            },
            //确定
            confirm:function () {

            },
            //报告科室
            getKsData: function(){
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm", function(json) {
                    brzcList.BgList = json.d.list;
                });
            },
            hoverName:function (falg,index,event){

                this.objabsolute.left=event!=undefined?event.layerX+30:0
                this.objabsolute.top=event!=undefined?event.clientY+20:0
                this.userName=falg==true ? true:falg
                console.log(this.objabsolute)
            },
        }
    });
brzcList.getKsData();





    laydate.render({
        elem: '.todate',
        type: 'datetime',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
})();

$(window).resize(function () {
    changHeight();
});
function changHeight() {
 var group_height=$(window).height()-($('.fyxm-side-top').outerHeight()+$('.ksys-btn').outerHeight()+$('.top-form').outerHeight()+$('.ksys-btn').outerHeight()+75)
        console.log(group_height);
        $('.Grouping').css({
            'height':group_height,
        })
};

setTimeout(function () {
    changHeight();
},150);





