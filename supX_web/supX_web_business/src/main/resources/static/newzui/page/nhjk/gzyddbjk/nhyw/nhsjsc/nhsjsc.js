/**
 * Created by mash on 2017/9/29.
 */
    var left_tab1 = new Vue({
        el: '#left_tab1',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data: {
        	billCode: null,
            bxlbbm: null,
            bxurl: null,
        	which: 'hyjl',
            ksList: [],                         // 科室的list
            ksbm: null,                         // 选中的科室
            jsonList: [],         // 病人的list
            json:[],
            searchCon:[],
            selSearch:-1,
            text:"",
            page:{
            	page:1,
            	rows:20,
            	total:null
            },
            them_tran:{},
            them:{
            	'姓名': 'brxm',
                '性别': 'brxb',
                '年龄': 'nl',
                '住院号': 'zyh',
                '病人费别': 'brfb',
                '入院日期': 'ryrq'
            }
        },
        mounted:function(){
            this.getbxlb();
            this.getKsData();
        },
        methods: {
        	 getbxlb: function () {
        	     var that=this;
                 this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify({bxjk: "001"}), function (json) {
                     if (json.a == 0 && json.d && json.d.list.length > 0) {
                         that.bxlbbm = json.d.list[0].bxlbbm;
                         that.bxurl = json.d.list[0].url;
                         that.getS02();
                         } else {
                             malert("保险类别查询失败!" + json.c,"top","defeadted");
                         }
                     });
             },
             getS02: function () {
                 var head = {
                     operCode: "S02",
                     rsa: ""
                 };
                 var body = {
                     userName: "",
                     passWord: ""
                 };
                 var param = {
                     head: head,
                     body: body
                 };
                 var str_param = JSON.stringify(param);
                 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                         console.log(json);
                         if (json.a == 0) {
                        	 left_tab1.billCode = json.d;
                         } else {
                             malert(json.c,"top","defeadted");
                         }
                     });
             },
            // 获取科室
            getKsData: function () {
                var bean = {"zyks": "1"};
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                    if (json.a == 0 && json.d) {
                        left_tab1.ksList = json.d.list;
                        left_tab1.ksList.unshift({ksbm:'%','ksmc':'全院'});
                        left_tab1.param.ksbm = json.d.list[0].ksbm;
                        left_tab1.param.ksmc = json.d.list[0].ksmc;
                        left_tab1.$forceUpdate()
                        left_tab1.getBrData(); //加载完成后自动获取就诊病人列表
                    } else {
                        malert('获取科室失败', 'top', 'defeadted');

                    }
                });
            },
            // 选择科室
            ksChange: function (val) {
                Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                if (val[3] != null) {
                    Vue.set(this[val[2][0]], val[3], val[4]);
                }
                this.$forceUpdate()
                this.getBrData();
            },
            // 获取病人的API
            getBrData: function () {
                 var parm={
                     ryks:this.param.ksbm == '%' ? '':this.param.ksbm,
                     parm:this.text,
                     bxlbbm:this.bxlbbm
                 }
                 $.getJSON('/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=gznhhzxx&parm='+JSON.stringify(parm), function (json) {
                     if (json.a == '0' && json.d) {
                    	 left_tab1.jsonList = json.d.list;
                         left_tab1.total = json.d.total;
                     }
                 });
            },
            // 双击选中病人，获取病人信息
            edit: function (index) {
            	//转换数据格式
            	for(var i=0;i<this.jsonList[index].length;i++){
            		if(this.jsonList[index].bxbr=='0'){
            			this.jsonList[index].bxbr==false;
            		}else{
            			this.jsonList[index].bxbr==true;
            		}
                }
                  hyjl.brxxList = Object.assign(hyjl.brxxList, this.jsonList[index]);
                  hyjl.jbContent.jbmc=hyjl.brxxList.initialdiagnosis;
                  hyjl.brxxList.rylx=hyjl.brxxList.inpatienttypeoflocal;
                hyjl.ssContent.ssmc=  hyjl.brxxList.ssmc;
                  hyjl.$forceUpdate()
            	  if (window.wscjl){
            		  wscjl.zyh=this.jsonList[index].zyh;
                	  wscjl.zyh=this.jsonList[index].inpId;
                	  wscjl.getData();
            	  }
            	  if (window.yjs){
                	 yjs.getData();
            	  }
                  //检索当前病人的医嘱信息
            },
        }
    });

    var menu = new Vue({
        el: '.nh-menu',
        data: {
            which: '0',
            pageIndex:['hyjl','wscjl','cwxx','yjs'],
        },
        created:function(){
            this.loadCon(0);
        },
        methods: {
            loadCon: function (index) {
                this.which=index
                var pageDiv = $("#"+this.pageIndex[index]);
                $(".page_div").hide();
                if(pageDiv.length == 0){
                    $("."+this.pageIndex[index]).load(this.pageIndex[index]+".html").fadeIn(300);
                } else {
                    $("."+this.pageIndex[index]).fadeIn(300);
                }
            }
        }
    });
