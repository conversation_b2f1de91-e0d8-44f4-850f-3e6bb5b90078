<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>财务交款</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="jkhzb.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="">
<div class="wrapper">
    <!--入院登记查询列表视图begin-->
    <div id="tableInfo" v-show="isShow">
        <!--入院登记功能按钮begin-->
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getData">刷新</button>
            </div>
        </div>
        <!--入院登记功能按钮end-->

        <!--检索字段begin-->
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padding-left60">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh240 text-indent20" placeholder="不限定时间范围" id="timeVal"/>
                    </div>
                </div>
                <!--<div class="zui-inline padding-left40">-->
                    <!--<label class="zui-form-label">检索</label>-->
                    <!--<div class="zui-input-inline">-->
                        <!--<input class="zui-input wh250" placeholder="请输入关键字" type="text" id="jsvalue"-->
                               <!--@keydown.enter="getData"/>-->
                    <!--</div>-->
                <!--</div>-->
            </div>
        </div>
        <!--检索字段end-->

        <!--循环列表begin-->
        <div class="zui-table-view" id="brRyList">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th z-fixed="left" z-width="100px">
                            <div class="zui-table-cell"><span>序号</span></div>
                        </th>
                        <th z-field="zyh" z-width="100px">
                            <div class="zui-table-cell"><span>类型</span></div>
                        </th>
                        <th z-field="xm" z-width="100px">
                            <div class="zui-table-cell"><span>凭证号</span></div>
                        </th>
                        <th z-field="xb" z-width="100px">
                            <div class="zui-table-cell"><span>交款时间</span></div>
                        </th>
                        <th z-field="csrq" z-width="100px">
                            <div class="zui-table-cell"><span>交款人</span></div>
                        </th>
                        <th z-field="nl" z-width="100px">
                            <div class="zui-table-cell"><span>状态</span></div>
                        </th>
                        <th z-field="ryks" z-fixed="right" z-width="100px">
                            <div class="zui-table-cell"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"
                        class="tableTr2"><!--@dblclick="edit($index)"双击回调-->
                        <td>
                            <div class="zui-table-cell" v-text="$index+1"><!--序号--></div>
                        </td>
                        <td>
                            <div class="zui-table-cell" v-text="item.jkpzh"><!--住院号--></div>
                        </td>
                        <td>
                            <div class="zui-table-cell" v-text="item.czyxm"><!--姓名--></div>
                        </td>
                        <td>
                            <div class="zui-table-cell" v-text="fDec(item.yjje)"><!--性别--></div>
                        </td>
                        <td>
                            <div class="zui-table-cell" v-text="fDec(item.zyyj)"><!--出生日期--></div>
                        </td>
                        <td>
                            <div class="zui-table-cell">待交款</div>
                        </td>
                        <td><!--操作-->
                            <div class="zui-table-cell">
                                <i class="icon-icon icon-jf-h" title="结算" @click="js(item,false)"></i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
        <!--循环列表end-->
    </div>
    <!--入院登记查询列表视图end-->

    <!--入院登记添加记录视图begin-->
    <!--入院登记添加记录视图end-->

    <!--收/退预交金begin-->
    <div id="syjjInfo" v-if="isShow">
        <!--收/退预交金 功能按钮begin-->
        <div class="tong-search printHide">
            <div class="zui-form">
                <div class="zui-inline padding-left60 flex">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh240 text-indent20" placeholder="不限定时间范围" v-model="sfsj" id="timeValTwo"/>
                    </div>
                </div>
            </div>
        </div>
        <h1 class="text-center bg-fff" style="font-size: 24px">{{yljgmc}}交款表</h1>
        <!--收/退预交金 功能按钮end-->

        <div class="syjj-info-box">
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">基本信息</div>
                </div>
                <div class="tab-card-body">
                    <div class="zui-form grid-box">
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">收费员</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.czyxm}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">交款日期</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.jkrq}}</span>
                            </div>
                        </div>
                        <div class=" col-xxl-3 flex">
                            <span class="noSpace">交款凭证</span>
                            <div class="zui-input-inline">
                            <span>{{pageData.jkpzh}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">门诊预交金额</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.yjje}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">住院预交金额</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.zyyj}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">交款区间</span>
                            <div class="zui-input-inline" style="width: 80%">
                                <span class="text-ov title">{{pageData.jkqj}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">医疗卡支付</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.ylkzf}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">医保卡支付</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.ybkzf}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">优惠金额</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.yhje}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">其他支付</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.qtzf}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">费用金额</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.fyhj}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">交款金额</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.jkje}}</span>
                            </div>
                        </div>
                        <div class=" flex col-xxl-3">
                            <span class="noSpace">大写金额</span>
                            <div class="zui-input-inline">
                                <span>{{pageData.jkjedx}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">费用项目</div>
                </div>
                <div class="tab-card-body">
                    <ul class="brfb-item">
                        <div class="flex brfb-title brfb">
                            <p>序号</p>
                            <p>费别名称</p>
                            <p>收费金额</p>
                            <p>收费笔数</p>
                            <p>退费金额</p>
                            <p>退费笔数</p>
                            <p>优惠金额</p>
                            <p>合计笔数</p>
                            <p>合计金额</p>
                        </div>
                        <li class="flex brfb-list brfb"  @dblclick="showYjjlInfo($index)" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" v-for="(item, $index) in pageData.fylbList">
                            <p v-text="$index+1"></p>
                            <p v-text="item.ryfbmc"></p>
                            <p v-text="fDec(item.sfyje,2)"></p>
                            <p v-text="item.snumber"></p>
                            <p v-text="fDec(item.tfyje,2)"></p>
                            <p v-text="item.tnumber"></p>
                            <p v-text="fDec(item.ybkzf,2)"></p>
                            <p v-text="fDec(item.qtzf,2)"></p>
                            <p v-text="fDec(item.yhje)"></p>
                            <p v-text="item.allNumber"></p>
                            <p v-text="fDec(item.sfyje+item.tfyje,2)"></p>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">病人费别</div>
                </div>
                <div class="tab-card-body">
                    <ul class="brfb-item">
                      <div class="flex brfb-title brfb">
                          <p>序号</p>
                          <p>费别名称</p>
                          <p>收费金额</p>
                          <p>收费笔数</p>
                          <p>退费金额</p>
                          <p>退费笔数</p>
                          <p>医保支付</p>
                          <p>其他支付</p>
                          <p>合计笔数</p>
                          <p>合计金额</p>
                      </div>
                        <li class="flex brfb-list brfb" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" v-for="(item, $index) in pageData.ryfbList">
                            <p v-text="$index+1"></p>
                            <p v-text="item.ryfbmc"></p>
                            <p v-text="fDec(item.sfyje,2)"></p>
                            <p v-text="item.snumber"></p>
                            <p v-text="fDec(item.tfyje,2)"></p>
                            <p v-text="item.tnumber"></p>
                            <p v-text="fDec(item.ybkzf,2)"></p>
                            <p v-text="fDec(item.qtzf,2)"></p>
                            <p v-text="item.allNumber"></p>
                            <p v-text="fDec(item.sfyje+item.tfyje,2)"></p>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="tab-card">
                <div class="tab-card-body">
                    <div class="zui-form grid-box">
                        <div class="margin-b-5 col-xxl-12">
                            <span class="noSpace">门诊收费发票范围</span>
                            <span>(共{{pageData.yjjlList.mzfyNumber}}张)	{{pageData.yjjlList.mzBrfySfphm}}</span>
                        </div>
                        <div class="margin-b-5 col-xxl-12">
                            <span class="noSpace">门诊收费退票范围</span>
                            <span>{{pageData.yjjlList.mzBrfyTfphm}}</span>
                        </div>
                        <div class="margin-b-5 col-xxl-12">
                            <span class="noSpace">门诊预交发票范围</span>
                            <span>(共{{pageData.yjjlList.mzyjNumber}}张)	{{pageData.yjjlList.mzYjjlSfphm}}</span>
                        </div>
                        <div class="margin-b-5 col-xxl-12">
                            <span class="noSpace">门诊预交退票范围</span>
                            <span>{{pageData.yjjlList.mzYjjlTfphm}}</span>
                        </div>
                        <div class="margin-b-5 col-xxl-12">
                            <span class="noSpace">住院预交发票范围</span>
                            <span>(共{{pageData.yjjlList.zyyjNumber}}张)	{{pageData.yjjlList.zyYjjlSfphm}}</span>
                        </div>
                        <div class="col-xxl-12">
                            <span class="noSpace">住院预交退票范围</span>
                            <span>{{pageData.yjjlList.zyYjjlTfphm}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-bar zui-table-tool">
            <button class="zui-btn  btn-f2a654" @click="saveData()">财务上交</button>
            <button class="zui-btn  btn-f2a654" @click="saveData()">取消上交</button>
            <button class="zui-btn btn-primary" @click="print">打印</button>
            <button class="zui-btn btn-default" @click="quxiao">取消</button>
        </div>
    </div>
    <!--收/退预交金end-->
    <div class="side-form  pop-850"  :class="{'ng-hide':num==1}" style="padding-top: 0;"  id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span>费用明细</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side">
            <div class="zui-table-view">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th z-fixed="left" z-width="100px">
                                <div class="zui-table-cell"><span>序号</span></div>
                            </th>
                            <th z-field="zyh" z-width="100px">
                                <div class="zui-table-cell"><span>类型</span></div>
                            </th>
                            <th z-field="xm" z-width="100px">
                                <div class="zui-table-cell"><span>凭证号</span></div>
                            </th>
                            <th z-field="xb" z-width="100px">
                                <div class="zui-table-cell"><span>交款时间</span></div>
                            </th>
                            <th z-field="csrq" z-width="100px">
                                <div class="zui-table-cell"><span>交款人</span></div>
                            </th>
                            <th z-field="nl" z-width="100px">
                                <div class="zui-table-cell"><span>状态</span></div>
                            </th>
                            <th z-field="ryks" z-width="100px">
                                <div class="zui-table-cell"><span>操作</span></div>
                            </th>
                            <th z-field="ryrq" z-width="100px">
                                <div class="zui-table-cell"><span>费用单价</span></div>
                            </th>
                            <th z-field="hjje" z-width="100px">
                                <div class="zui-table-cell"><span>费用金额</span></div>
                            </th>
                            <th z-field="yzlx" z-width="100px">
                                <div class="zui-table-cell"><span>医嘱类型</span></div>
                            </th>
                            <th z-field="yxzhm" z-width="100px">
                                <div class="zui-table-cell"><span>医嘱号码</span></div>
                            </th>
                            <th z-field="fphm" z-width="100px">
                                <div class="zui-table-cell"><span>发票号码</span></div>
                            </th>
                            <th z-field="mzys" z-width="100px">
                                <div class="zui-table-cell"><span>门诊医生</span></div>
                            </th>
                            <th z-field="mzks" z-width="100px">
                                <div class="zui-table-cell"><span>门诊科室</span></div>
                            </th>
                            <th z-field="zxks" z-width="100px">
                                <div class="zui-table-cell"><span>执行科室</span></div>
                            </th>
                            <th z-field="yhbl" z-width="100px">
                                <div class="zui-table-cell"><span>优惠比例</span></div>
                            </th>
                            <th z-field="yhje" z-width="100px">
                                <div class="zui-table-cell"><span>优惠金额</span></div>
                            </th>
                            <th z-field="bzsm" z-width="100px">
                                <div class="zui-table-cell"><span>备注说明</span></div>
                            </th>

                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in mxfyList" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                            <!--@click="单击回调" @dblclick="edit($index)"双击回调-->
                            <td>
                                <div class="zui-table-cell" v-text="$index+1"><!--序号--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.ryghxh"><!--住院号--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.brxm"><!--支付类型--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.ryfbmc"><!--业务窗口--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.rybxlbmc"><!--操作员姓名--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.mxfyxmmc"><!--科室名称--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.fysl"><!--预交金额--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="fDec(item.fydj,2)"><!--预交日期--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="fDec(item.fyje,2)"><!--预交日期--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="yzlx_tran[item.yzlx]"><!--预交日期--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.yzhm"><!--预交金额--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.fphm"><!--预交金额--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.mzysxm"><!--预交金额--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.mzksmc"><!--预交金额--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.zxksmc"><!--预交金额--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.yhbl"><!--预交金额--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="fDec(item.yhje,2)"><!--预交金额--></div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.bzsm"><!--预交金额--></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script src="jkhzb.js" type="text/javascript"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</html>
