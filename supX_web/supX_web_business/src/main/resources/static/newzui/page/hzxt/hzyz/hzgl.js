var panel = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        total: null,
        isflag: true,
        index: 0,
        popContent: {
            ksbm: '',
            zyty: 'zyhzxx',
            brgl: 'zgbr',
            sort: 'cw',
            /*bkgl:'0'*/
        },
        brk_list: [],
        ksrq: null,
        jsrq: null,
        allKs: [],     //住院科室列表
        //Type: 'zy',      //当前病人列表类型   zy-在院   cy-出院
        cwbh: "",
        pxContent: {
            'sort': 'xssx',
            'order': '1',
        },
        is_csqx: {}
    },
    created: function () {
        this.$nextTick(function () {
            this.Wf_getKs();
        })
        if (sessionStorage.getItem('zyty') != null) {
            this.popContent.zyty = sessionStorage.getItem('zyty')
        }
        if (sessionStorage.getItem('brgl') != null) {
            this.popContent.brgl = sessionStorage.getItem('brgl')
        }
    },
    watch: {
        'popContent.ksbm': function (newVal) {
            sessionStorage.setItem('ksbm', newVal)
        },
        'popContent.zyty': function (newVal) {
            sessionStorage.setItem('zyty', newVal)
        },
        'popContent.brgl': function (newVal) {
            sessionStorage.setItem('brgl', newVal)
        }
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var myDate = new Date();
        this.ksrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.jsrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.ksrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    panel.ksrq = value ;
                } else {
                    panel.ksrq = '';
                }
                //获取一次列表
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.jsrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    panel.jsrq = value ;
                } else {
                    panel.jsrq = '';
                }
                //获取一次列表
                panel.Wf_GetBrList(panel.popContent.brgl);
            }
        });
    },
    methods: {
        resultChangeOd: function (val) {
            if (val[2].length > 1) {
                if (Array.isArray(this[val[2][0]])) {
                    Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    panel.getData();
                } else {
                    Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                    if (val[3] != null) {
                        Vue.set(this[val[2][0]], val[3], val[4]);
                    }
                    panel.getData();
                }
            } else {
                this[val[2][0]] = val[0];
                panel.getData();
            }
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
        },
        getData: function () {
            kp.Brxx_List = [];
            this.param.page = 1
            zuiItem.param.page = 1
            this.Wf_GetBrList(this.popContent.brgl);
        },
        tab: function (index) {
            this.index = index;
            zuiItem.index = index;
            kp.index = index;
            kp.Brxx_List = []
            zuiItem.Brxx_List = []
            this.Wf_GetBrList(this.popContent.brgl);
        },
        //获取科室
        Wf_getKs: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"N030032001"}', function (json) {
                if (json.a == '0' && json.d) {
                    var rlksList = [];
                    for (var i = 0; i < json.d.length; i++) {
                        if (json.d[i].bqbm != null) {
                            rlksList.push(json.d[i]);
                        }
                    }
                    panel.allKs = rlksList;
                    panel.popContent.ksbm = rlksList[0].ksbm;
//                	panel.allKs = json.d;
//                	panel.popContent.ksbm =json.d[0].ksbm;
                    if (sessionStorage.getItem('ksbm') != null) {
                        panel.popContent.ksbm = sessionStorage.getItem('ksbm')
                    }
                    //科室获取成功后再查询患者信息
                    panel.Wf_GetBrList(panel.popContent.brgl);
                    panel.getCsqx();
                }
            });
        },
        //科室改变事件
        Wf_KsChange: function (val) {
            //先获取住院医生的值
            Vue.set(this.popContent, 'ksbm', val[0]);
            panel.Wf_GetBrList(panel.popContent.brgl);
            panel.getCsqx();
        },

        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            parm = {
                "ylbm": 'N030032001',
                "ksbm": panel.popContent.ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03003200101": //医嘱默认西药房  --下医嘱和申领药品时默认，为空则取本地配置文件药房编码
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200101 = csjson.csz;
                                    }
                                    break;
                                case "N03003200102": //中药医嘱默认药房 --输入药房编码
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200102 = csjson.csz;
                                    }
                                    break;
                                case "N03003200103": //医生作废医嘱权限	 0-提示是否打印;1-直接打印;2-不打印
                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                        panel.is_csqx.cs003003200103 = csjson.csz;
                                    }
                                    break;
                                case "N03003200104": //中药医嘱默认用药方法   --中药医嘱默认用药方法
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200104 = csjson.csz;
                                    }
                                    break;
                                case "N03003200105": //临时医嘱默认频次编码 --请录入频次编码
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200105 = csjson.csz;
                                    }
                                    break;
                                case "N03003200106": //录入医嘱时指定药品统筹类别  --0、不指定，1、指定
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200106 = csjson.csz;
                                    }
                                    break;
                                case "N03003200107": //保存出院医嘱时是否停嘱 --0、否，1、是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200107 = csjson.csz;
                                    }
                                    break;
                                case "N03003200108": //下转科医嘱是否自动停嘱参数 0、否，1、是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200108 = csjson.csz;
                                    }
                                    break;
                                case "N03003200109": //医嘱单打印行数 --医嘱单打印行数
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200109 = csjson.csz;
                                    }
                                    break;
                                case "N03003200110": //自动失效长期医嘱是否签停嘱医生和时间  --0=否，1=是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200110 = csjson.csz;
                                    }
                                    break;
                                case "N03003200111": //输液速度单位  --0、滴/分 1、gtt/min 2、毫升/小时
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200111 = csjson.csz;
                                    }
                                    break;
                                case "N03003200112": //检查医嘱填写诊断、申请信息 --0-否，1-有
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200112 = csjson.csz;
                                    }
                                    break;
                                case "N03003200113": //检验医嘱填写诊断、申请信息 0-否，1-有
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200113 = csjson.csz;
                                    }
                                    break;
                                case "N03003200114": //是否允许修改医嘱排序功能 0-无 1-有
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200114 = csjson.csz;
                                    }
                                    break;
                                case "N03003200115": //医嘱保存是否需要密码0-不需要,1-需要
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200115 = csjson.csz;
                                    }
                                    break;
                                case "N03003200116": //医嘱保存是否需要密码0-不需要,1-需要
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200116 = csjson.csz;
                                    }
                                    break;
                                case "N03003200117": //审核之后是否允许作废医嘱0-允许，1-不允许
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200117 = csjson.csz;
                                    }
                                    break;
                                case "N03003200118": //是否对抗生素药品使用限制0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200118 = csjson.csz;
                                    }
                                    break;
                                case "N03003200126": //是否对抗肿瘤药品使用限制0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200126 = csjson.csz;
                                    }
                                    break;
                                case "N03003200127": //是否对药品种类中成药使用限制0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200127 = csjson.csz;
                                    }
                                    break;
                                case "N03003200124": //是否使用住院状态全部选项  0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.N03003200124 = csjson.csz;
                                        if (panel.is_csqx.N03003200124 == '1') {
                                            panel.zyTypeBq_tran['qbhzxx'] = '全部';
                                            console.log(panel.zyTypeBq_tran)
                                        }
                                    }
                                    break;
                                case "N03003200130" :    //是否显示双向转诊按钮 ，（临夏）
                                    if (csjson.csz && csjson.csz == '1') {
                                        kp.sxzzan = true;
                                    }
                                    break;
                            }
                        }

                    }
                } else {
                    malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                }
            });

        },


        //住院状态，病人过滤改变事件
        Wf_GetBrList: function (type) {
            if (this.isflag) {
                common.openloading('#jyxm_icon');
            }
            if (!this.isflag) {
                kp.isDoneCb = true;
            }
            //当前病人类型
            if (typeof type == 'object') {
                Vue.set(this[type[2][0]], type[2][1], type[0]);
                type = type[0];
                this.param.page = 1;
                kp.Brxx_List = [];
                zuiItem.Brxx_List = [];
            } else {
                type = type;
            }
            var zgzyys = null;
            if (this.popContent.brgl == 'zgbr') {
                zgzyys = userId;
            }
            var queryparm = {
                ryks: this.popContent.ksbm,
                rows: this.index ? zuiItem.param.rows : kp.param.rows,
                page: this.param.page,
                zyys: zgzyys,
                parm: panel.cwbh,
            };
            if (panel.is_csqx.N03003200124 == '1' && this.popContent.zyty == 'qbhzxx' || this.popContent.zyty == 'Rcyhzxx') {
                queryparm.beginrq = panel.ksrq;
                queryparm.endrq = panel.jsrq;
                queryparm.cxrq="cyrq";
            }
            if(this.popContent.zyty == 'Rcyhzxx')queryparm.cxrq="cyrq"
            // switch (type){
            //case 'zy'://在院
            //panel.Type = type;

            queryparm.sort = panel.pxContent.sort;
            if (panel.pxContent.order == '1'){
                queryparm.order = 'asc';
            } else {
                queryparm.order = 'desc';
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=' + this.popContent.zyty + '&parm=' + JSON.stringify(queryparm), function (json) {
                if (json.a == '0') {
                    kp.scollType = true
                    common.closeLoading();
                    for (var i = 0; i <json.d.list.length ; i++) {
                        json.d.list[i].brxb=panel.brxb_tran[json.d.list[i].brxb]
                        json.d.list[i].nldw=panel.nldw_tran[json.d.list[i].nldw]
                    }
                    zuiItem.Brxx_List = json.d.list;
                    kp.Brxx_List = kp.Brxx_List.concat(json.d.list);
                    panel.total = json.d.total;
                    kp.isDoneCb = false;
                    zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
                    panel.getnljd();
                    kp.brk_list(json.d.list.length);
                } else {
                    kp.isDoneCb = false;
                    common.closeLoading();
                }
            });
            // break;
            // case 'bqcy'://出院
            // 	//panel.Type = type;
            // 	queryparm;
            //     $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=bqcyhzxx&parm='+JSON.stringify(queryparm), function (json) {
            //         if (json.a == '0') {
            //             kp.scollType=true
            //             common.closeLoading();
            //             zuiItem.Brxx_List=json.d.list;
            //             kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //             panel.total=json.d.total;
            //             kp.isDoneCb=false;
            //             zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //             kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //             panel.getnljd();
            //             kp.brk_list(json.d.list.length);
            //         }else {
            //             kp.isDoneCb=false;
            //             common.closeLoading();
            //         }
            //         //zuiItem.total = json.d.total;
            //         //kp.total = json.d.total;
            //     });
            //     break;
            // case 'cy'://出院
            //     //panel.Type = type;
            // 	queryparm.beginrq=panel.ksrq;
            // 	queryparm.endrq=panel.jsrq;
            // 	queryparm.cxrq="cyrq";
            //     $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=Rcyhzxx&parm=" + JSON.stringify(queryparm), function (json) {
            //         if (json.a == '0') {
            //             kp.scollType=true
            //             common.closeLoading();
            //             zuiItem.Brxx_List=json.d.list;
            //             kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //             panel.total=json.d.total;
            //             kp.isDoneCb=false;
            //             zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //             kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //             panel.getnljd();
            //             kp.brk_list(json.d.list.length);
            //         }else {
            //             kp.isDoneCb=false;
            //             common.closeLoading();
            //         }
            //         //zuiItem.total = json.d.total;
            //         //kp.total = json.d.total;
            //
            //     });
            //     break;
            // case 'zk'://转科
            // 	//panel.Type = type;
            //     $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zkhzxx&parm=' + JSON.stringify(queryparm), function (json) {
            //         if (json.a == '0') {
            //             kp.scollType=true
            //             common.closeLoading();
            //             zuiItem.Brxx_List=json.d.list;
            //             kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //             panel.total=json.d.total;
            //             kp.isDoneCb=false;
            //             zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //             kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //             panel.getnljd();
            //             kp.brk_list(json.d.list.length);
            //         }else {
            //             kp.isDoneCb=false;
            //             common.closeLoading();
            //         }
            //         //zuiItem.total = json.d.total;
            //         //kp.total = json.d.total;
            //
            //     });
            //     break;
            // case 'cxbyParm':
            //     if (panel.popContent.zyty == 'zy') {
            //         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm=' + JSON.stringify(queryparm), function (json) {
            //             if (json.a == '0') {
            //                 kp.scollType=true
            //                 common.closeLoading();
            //                 zuiItem.Brxx_List=json.d.list;
            //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //                 panel.total=json.d.total;
            //                 kp.isDoneCb=false;
            //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //                 panel.getnljd();
            //                 kp.brk_list(json.d.list.length);
            //             }else {
            //                 kp.isDoneCb=false;
            //                 common.closeLoading();
            //             }
            //             //zuiItem.total = json.d.total;
            //             //kp.total = json.d.total;
            //
            //         });
            //     } else {
            //       	queryparm.beginrq=panel.ksrq;
            //     	queryparm.endrq=panel.jsrq;
            //     	$.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=cyhzxx&parm="  + JSON.stringify(queryparm), function (json) {
            //             if (json.a == '0') {
            //                 kp.scollType=true
            //                 common.closeLoading();
            //                 zuiItem.Brxx_List=json.d.list;
            //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //                 panel.total=json.d.total;
            //                 kp.isDoneCb=false;
            //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //                 panel.getnljd();
            //                 kp.brk_list(json.d.list.length);
            //             }else {
            //                 kp.isDoneCb=false;
            //                 common.closeLoading();
            //             }
            //             //zuiItem.total = json.d.total;
            //             //kp.total = json.d.total;
            //
            //         });
            //     }
            //     break;
            // case 'zgbr':
            //     if (panel.popContent.zyty == 'zy') {
            //
            //            var parm={
            //         		 ryks:this.popContent.ksbm,
            //         		 zyys:userId,
            //         		 rows:rows,
            //         		 page:page,
            //         		 parm:cxp,
            //            }
            //     	   if(panel.pxContent.sort=='cw'){
            //     		   parm.sort1='1';
            //               }else if(panel.pxContent.sort=='xm'){
            //             	  parm.sort2='1'
            //               }else{
            //             	  parm.sort3='1'
            //               }
            //               if(panel.pxContent.order=='0'){
            //             	  parm.order1='1';
            //               }else{
            //             	  parm.order2='1'
            //               }
            //         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm='+JSON.stringify(parm), function (json) {
            //             if (json.a == '0') {
            //                 kp.scollType=true
            //                 common.closeLoading();
            //                 zuiItem.Brxx_List=json.d.list;
            //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //                 panel.total=json.d.total;
            //                 kp.isDoneCb=false;
            //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //                 panel.getnljd();
            //                 kp.brk_list(json.d.list.length);
            //             }else {
            //                 kp.isDoneCb=false;
            //                 common.closeLoading();
            //             }
            //             //zuiItem.total = json.d.total;
            //             //kp.total = json.d.total;
            //
            //         });
            //     } else {
            //       	queryparm.beginrq=panel.ksrq;
            //     	queryparm.endrq=panel.jsrq;
            //
            //         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=cyhzxx&parm={"cyks":"' + this.popContent.ksbm + '","zyys":"'+userId+'","rows":"'+rows+'","page":'+page+',sort:"'+this.popContent.sort+'"}', function (json) {
            //             if (json.a == '0') {
            //                 kp.scollType=true
            //                 common.closeLoading();
            //                 zuiItem.Brxx_List=json.d.list;
            //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //                 panel.total=json.d.total;
            //                 kp.isDoneCb=false;
            //                 kp.brk_list(json.d.list.length);
            //                 panel.getnljd();
            //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //             }else {
            //                 kp.isDoneCb=false;
            //                 common.closeLoading();
            //             }
            //             //zuiItem.total = json.d.total;
            //             //kp.total = json.d.total;
            //         });
            //     }
            //     break;
            // case 'qk':
            //     if (panel.popContent.zyty == 'zy') {
            //         var parm={
            //        		 ryks:this.popContent.ksbm,
            //        		 rows:rows,
            //        		 page:page,
            //        		 parm:cxp,
            //           }
            //    	   if(panel.pxContent.sort=='cw'){
            //    		   parm.sort1='1';
            //              }else if(panel.pxContent.sort=='xm'){
            //            	  parm.sort2='1'
            //              }else{
            //            	  parm.sort3='1'
            //              }
            //              if(panel.pxContent.order=='0'){
            //            	  parm.order1='1';
            //              }else{
            //            	  parm.order2='1'
            //              }
            //         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm='+JSON.stringify(parm), function (json) {
            //             if (json.a == '0') {
            //                 kp.scollType=true
            //                 common.closeLoading();
            //                 zuiItem.Brxx_List=json.d.list;
            //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //                 panel.total=json.d.total;
            //                 kp.isDoneCb=false;
            //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //                 panel.getnljd();
            //                 kp.brk_list(json.d.list.length);
            //             }else {
            //                 kp.isDoneCb=false;
            //                 common.closeLoading();
            //             }
            //             //zuiItem.total = json.d.total;
            //             //kp.total = json.d.total;
            //         });
            //     } else {
            //       	queryparm.beginrq=panel.ksrq;
            //     	queryparm.endrq=panel.jsrq;
            //         $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=cyhzxx&parm=" + JSON.stringify(model), function (json) {
            //             if (json.a == '0') {
            //                 kp.scollType=true
            //                 common.closeLoading();
            //                 zuiItem.Brxx_List=json.d.list;
            //                 kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
            //                 panel.total=json.d.total;
            //                 kp.isDoneCb=false;
            //                 zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
            //                 kp.totlePage = Math.ceil(json.d.total / kp.param.rows);
            //                 panel.getnljd();
            //                 kp.brk_list(json.d.list.length);
            //             }else{
            //                 kp.isDoneCb=false;
            //                 common.closeLoading();
            //             }
            //             //zuiItem.total = json.d.total;
            //             //kp.total = json.d.total;
            //
            //         });
            //     }
            //     break;
            // }
        },
        //检索查询回车键
        searchHc: function () {
            if (window.event.keyCode == 13) {
                this.Wf_GetBrList('cxbyParm');
                kp.Brxx_List = [];
                zuiItem.Brxx_List = []
            }
        },
        //划分年龄阶段的
        getnljd: function () {
            for (var k = 0; k < kp.Brxx_List.length; k++) {
                //判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                if (kp.Brxx_List[k].nl < 7 && kp.Brxx_List[k].brxb == '1') {
                    kp.Brxx_List[k].nljd = '1';
                } else if (kp.Brxx_List[k].nl < 7 && kp.Brxx_List[k].brxb == '2') {
                    kp.Brxx_List[k].nljd = '2';
                } else if (kp.Brxx_List[k].nl < 18 && kp.Brxx_List[k].nl > 6 && kp.Brxx_List[k].brxb == '1') {
                    kp.Brxx_List[k].nljd = '3';
                } else if (kp.Brxx_List[k].nl < 18 && kp.Brxx_List[k].nl > 6 && kp.Brxx_List[k].brxb == '2') {
                    kp.Brxx_List[k].nljd = '4';
                } else if (kp.Brxx_List[k].nl < 41 && kp.Brxx_List[k].nl > 17 && kp.Brxx_List[k].brxb == '1') {
                    kp.Brxx_List[k].nljd = '5';
                } else if (kp.Brxx_List[k].nl < 41 && kp.Brxx_List[k].nl > 17 && kp.Brxx_List[k].brxb == '2') {
                    kp.Brxx_List[k].nljd = '6';
                } else if (kp.Brxx_List[k].nl < 66 && kp.Brxx_List[k].nl > 40 && kp.Brxx_List[k].brxb == '1') {
                    kp.Brxx_List[k].nljd = '7';
                } else if (kp.Brxx_List[k].nl < 66 && kp.Brxx_List[k].nl > 40 && kp.Brxx_List[k].brxb == '2') {
                    kp.Brxx_List[k].nljd = '8';
                } else if (kp.Brxx_List[k].nl > 65 && kp.Brxx_List[k].brxb == '1') {
                    kp.Brxx_List[k].nljd = '9';
                } else if (kp.Brxx_List[k].nl > 65 && kp.Brxx_List[k].brxb == '2') {
                    kp.Brxx_List[k].nljd = '10';
                } else {
                    kp.Brxx_List[k].nljd = '11';
                }
            }
            //查询危重，护理等级记录
//            var json={
//                ksbm:this.popContent.ksbm,
//                shbz: '1',
//                ystzbz: '0'
//            };
//            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=queryYlyzList&parm=" + JSON.stringify(json), function (json) {
//                if (json.a == '0') {
//                    var ylyzList=json.d.list;
//                    if(ylyzList.length>0){
//                        for (var i = 0; i < ylyzList.length; i++) {
//                            //列表赋值
//                            for (var j = 0; j < zuiItem.Brxx_List.length; j++) {
//                                if(zuiItem.Brxx_List[j].zyh==ylyzList[i].zyh){
//                                    Vue.set(zuiItem.Brxx_List[j],'bqdj',ylyzList[i].bqdj);
//                                    Vue.set(zuiItem.Brxx_List[j],'hldj',ylyzList[i].hldj);
//                                }
//                            }
//                            //平铺赋值
//                            for (var k = 0; k < kp.Brxx_List.length; k++) {
//                                if(kp.Brxx_List[k].zyh==ylyzList[i].zyh){
//                                    Vue.set(kp.Brxx_List[k],'bqdj',ylyzList[i].bqdj);
//                                    Vue.set(kp.Brxx_List[k],'hldj',ylyzList[i].hldj);
//                                }
//                            }
//                        }
//                    }
//                }
//            });
        },
    }
});

//列表展示病人记录
var zuiItem = new Vue({
    el: '.zui-item',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        index: 0,
        param: {
            page: 1,
            rows: 15,
            sort: '',
            order: 'asc'
        },
        Brxx_List: [],
    },
    methods: {
        userGet: function (list, val) {
            kp.userGet(list, val)
        },
        getData: function () {
            kp.Brxx_List = [];
            panel.Wf_GetBrList(panel.popContent.brgl);
        },
    },
});
//平铺展示病人信息
var kp = new Vue({
    el: '.kp',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        askJson:{},
        csContent:{},
        sxzzan:false,
        scollType: true,
        isDoneCb: false,
        index: 0,
        activeIndex: undefined,
        loadData: '',
        param: {
            page: 1,
            rows: 15,
            sort: '',
            order: 'asc'
        },
        hldj_css: {
            "0": "",
            "1": "redCj",
            "2": "red",
            "3": "blue",
            "4": "blues"
        },
        brk_listD: '',
        Brxx_List: [],
    },
    updated: function () {
        changeWin();
    },
    methods: {
        activeClick: function (index) {
            this.activeIndex = index
        },
        loadingData: function () {
            if (event.wheelDelta > 0) return
            if (this.scollType) {
                this.scollType = false
                if (this.Brxx_List.length < panel.total) {
                    if (this.uilPageBottom() == true) {
                        panel.isflag = false;
                        panel.param.page = panel.param.page + 1;
                        panel.Wf_GetBrList(panel.popContent.brgl);
                    }
                } else {
                    this.loadData = '暂无更多数据...'
                }
            }
        },
        brk_list: function (list) {
            // if(list%2==1){
            if (this.index == 0) {
                if (list > 5) {
                    this.brk_listD = parseInt(this.$refs.kp.offsetWidth / 307) - (this.Brxx_List.length % parseInt(this.$refs.kp.offsetWidth / 307))
                }
            }
            // }
        },
        userGet: function (list, val) {
            this.topNewPage(list.brxm, 'page/zyysz/zyysz/hzgl/hzzx/hzzx.html');
            val.push(panel.popContent.ksbm);
            val[2].ksbm = panel.popContent.ksbm;
            sessionStorage.setItem('userPage', JSON.stringify(val))
        },
        topNew: function (list) {
            sessionStorage.setItem("toBagl", JSON.stringify({"entry": "zyys-hzglList"}));
            sessionStorage.setItem("bagljbxx", list.zyh);
            this.topNewPage('基本信息', 'page/bagl/sygl/sydj/jbxx.html');
        },
        sxzz: function (list) {
            var param = {
                brid: list.brid,
                xh: list.zyh,
                bz: '2',
                yljgbm: list.brid
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryZcHzxx&&parm=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    kp.askJson = JSON.parse(json.d);
                    if (!kp.askJson.YWSJ.JBXX.ZJHM) {
                        malert('身份证不能为空', 'top', 'defeadted')
                        return;
                    }
                    console.log(kp.askJson)
                    kp.callByRestful();
                }


            });

        },
        callByRestful() {
            var jsonPara = {
                'YWJSON': kp.askJson
            }
            $.ajax({
                type: 'post',
                url: 'http://127.0.0.1:8081/open',
                data: JSON.stringify(jsonPara),
                contentType: 'application/x-www-form-urlencoded',
                beforeSend: function (xhr) {
                },
                dataType: "json",
                success: function (data) {
                    malert(data.YWXT.MSG)
                }
            });
        },
        //院感监测上报
        ygjc_pcsb: function (list) {
            var zyh = list.zyh;
            if (window.top.J_tabLeft.obj.ygjc == "1") {
                var password = '123'//JSON.parse(this.getcookie("user_info"+userId)).password
                location.href = "SSOAPP" + window.top.J_tabLeft.obj.ygyycx + ":" + userId + "|"+password+"|" + list.zyh
            }

        },
        getData: function () {
            kp.Brxx_List = [];
            panel.Wf_GetBrList(panel.popContent.brgl);
        },
        dzbl:function (item) {
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + item.brid + "&json=" + JSON.stringify(this.param),function (json) {
                if(json.a=='0'){
                    malert('信息上传成功！','top','success')
                }else{
                    malert('信息上传失败失败：'+data.body.c,'top','defeadted')
                    return false
                }
            },function (error) {
                malert(error,'top','defeadted')
                return false
            })
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=" + item.zyh + "&json=" + JSON.stringify(this.param),function (json) {
                if(json.a=='0'){
                    malert('信息上传成功！','top','success')
                } else {
                    malert('信息上传失败失败：'+json.c,'top','defeadted')
                }
            },function () {
                malert(error,'top','defeadted')
                return false
            })
            var sxdz = "";
            var user = "";
            var password = "";
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(this.param),function (json) {
                if(json.a=='0'){
                    kp.csContent = JSON.parse(JSON.stringify(json.d.list[0]));
                    sxdz = kp.csContent.blSxdz;
                    user = userId;
                } else {
                    malert(json.c,'top','defeadted')
                    return false
                }
            },function (error) {
                malert(error,'top','defeadted')
                return false
            })
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId,function (json) {
                if (json.a == "0") {
                    password=json.d.password;
                }else{
                    malert(json.c,'top','defeadted')
                    return false
                }
            },function () {
                malert(error,'top','defeadted')
                return false
            })
            if (sxdz == "") {
                malert("书写地址为空，打开病历失败！",'top','defeadted');
                return
            }
            if (user == "") {
                malert("用户名为空，打开病历失败！！",'top','defeadted');
                return
            }
            if (password == "") {
                malert("用户密码为空，打开病历失败！",'top','defeadted');
                return
            }
            if (!item.zyh) {
                malert("请先选择病人后再书写病历！",'top','defeadted');
                return
            }
            var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=" + item.zyh + ",userid=" + user + ",password=" + password + ",lyzyhmz=0,blhhl=0";
            window.open(url);
        },

    },
});
$(window).resize(function () {
    kp.brk_list()
});
