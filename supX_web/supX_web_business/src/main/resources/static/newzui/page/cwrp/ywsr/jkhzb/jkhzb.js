
var dqsyh = null;
var ryrq=getTodayDateTime();
$(".zui-table-view").uitable();
/********************************华丽分割线***************************************/
    //列表展示
var tableInfo = new Vue({
        el: '#tableInfo',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],
            isShow: true,
            ztType: 'ryrq',
            beginrq: '',
            endrq: '',
            param: {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc'
            },
        },
        //页面渲染完成之后加载数据
        mounted: function () {
            //初始化检索日期！为今天0点到今天24点
            this.beginrq = this.fDate(new Date(),'date');
            this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24 ,'date');
            //入院登记查询列表 时间段选择器
            laydate.render({
                elem: '#timeVal',
                value: this.beginrq + ' - ' + this.endrq,
                rigger: 'click',
                theme: '#1ab394',
                range: true,
                done: function (value, data) {
                    if(value != ''){
                        var A_rq = value.split(' - ');
                        tableInfo.beginrq = A_rq[0];
                        tableInfo.endrq = A_rq[1];
                    }else{
                        tableInfo.beginrq = '';
                        tableInfo.endrq = '';
                    }
                    //获取一次列表
                    tableInfo.getData();
                }
            });
            this.getData();
        },

        methods: {
            ShowMz:function () {
                tableInfo.getMzjk()
                tableInfo.isShow = false;
                syjjInfo.isShow = true;
                syjjInfo.$nextTick(function () {
                    syjjInfo.DateMouted()
                })
            },
            //检索获取交款基本信息
            getMzjk: function () {
                var jkrq = syjjInfo.sfsj;//获取到检索时间
                //检索内容为就清空所有数据
                if (jkrq == "") {
                    syjjInfo.pageData.popContent = {};//总体费用信息
                    syjjInfo.pageData.brfbList = [];//病人费别
                    syjjInfo.pageData.fylbList = [];//费用项目
                    syjjInfo.pageData.mxfyList = []; //门诊费用明细
                    return;
                }
                //请求后台查询基本信息
                var parm = {
                    jkrq: jkrq
                };
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=queryCwjkMsg&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        // syjjInfo.pageData = json.d;//总的费用情况
                        syjjInfo.pageData.jkje = tableInfo.fDec(json.d.jkje, 2);
                        syjjInfo.$set(syjjInfo.pageData, 'jkjedx', numToCn(json.d['jkje'])); //转换成大写金额
                        syjjInfo.$set(syjjInfo.pageData, 'fyhj', syjjInfo.fDec(json.d['fyhj'], 2));
                        var kssj = syjjInfo.fDate(json.d.kssj, "date");
                        syjjInfo.$set(syjjInfo.pageData, 'kssj', kssj);
                        syjjInfo.$set(syjjInfo.pageData, "ryfbList", json.d.ryfbList );//病人费别
                        syjjInfo.$set(syjjInfo.pageData, "fylbList", json.d.fylbList );//费用项目
                        console.log(syjjInfo.pageData)
                    } else {
                        malert(json.c,'top','defeadted')
                    }
                });

                //请求后台查询发票信息
                this.getFphm(jkrq);
                //请求后台获取医疗机构名称
                this.getYljg();
            },
            //请求后台获取到发票号码段
            getFphm: function (jkrq) {
                var parm = {
                    cxsj: jkrq
                };
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjkFphm&types=queryFphm&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        syjjInfo.$set(syjjInfo.pageData, "yjjlList", json.d );//发票信息
                    } else {
                        malert(json.c,'top','defeadted')
                    }
                });
            },
            //请求后台查询医疗机构名称
            getYljg: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryYljg&types=queryOne&jgbm=" + jgbm, function (json) {
                    if (json.a == 0) {
                        syjjInfo.yljgmc = json.d.jgmc;//发票信息
                    } else {
                        malert(json.c,'top','defeadted')
                    }
                });
            },
            js:function (zyh,isShouYJJ) {
                this.isShow = false;
                syjjInfo.isShow = true;
                syjjInfo.isShouYJJ = isShouYJJ;
                syjjInfo.getData(zyh);
            },
            ztTypeCB:function(val){//状态下拉框选中回调
                this.ztType = val[0];
                this.getData();
            },
            getData: function () {
                //获取检索参数
                var jsvalue = $("#jsvalue").val();
                var parm = {
                    jssx: '',
                    jsvalue: jsvalue,
                    cxrq: this.ztType,
                    endrq: this.endrq,
                    beginrq: this.beginrq
                };
                var jkzt=null;
                // if(tableInfo.wzf==true){
                //     jkzt='0';
                // }
                this.param.jkzt=jkzt;
                this.param.beginrq = this.beginrq
                this.param.endrq = this.endrq;
                this.param.sort = "jkrq";
                this.param.order = "desc";
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=queryCwjk&parm=" + JSON.stringify(this.param) + "", function (json) {
                    tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                    tableInfo.jsonList = json.d.list;
                });
            },
            yjfButtClick: function(zyh,isShouYJJ){
                this.isShow = false;
                syjjInfo.isShow = true;
                syjjInfo.isShouYJJ = isShouYJJ;
                syjjInfo.getData(zyh);
            }
        }
    });

/********************************华丽分割线***************************************/
var zd_enter = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifClick: true, //用于判断是否有点击交款按钮
        isCheck: null,
        isShow: true,
        mzjk: true, //门诊交款按钮
        popContent: {},//总体费用信息
        yljgmc: null,
        fphmPopContent: {},//发票号码信息
        brfbList: [],//病人费别
        fylbList: [],//费用项目
        mxfyList: [],//门诊费用明细
        toolMenu: true, //工具栏
        num:1,
    },
    //页面渲染完成之后加载数据
    mounted: function () {
    },
    methods: {
        closes:function () {
            this.num=1
        },
        //选中费用项目查询费用项目明细信息
        showDetail: function (index) {
            this.isCheck = index;
            //判断是否交款凭证号是否存在来决定查询哪个病人费用信息
            if (syjjInfo.pageData.jkpzh == undefined || syjjInfo.pageData.jkpzh == null || syjjInfo.pageData.jkpzh == '') {
                var parm = {
                    fylb: syjjInfo.pageData.fylbList[index]['fylb'],
                    sfsj:syjjInfo.sfsj
                };
                $.getJSON('/actionDispatcher.do?reqUrl=MzsfSfjsBrfy&types=queryByFylb' + '&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            zd_enter.mxfyList = data.d.list;
                        } else {
                            malert(data.c,'top','defeadted')

                        }
                    });
            } else {
                var parm = {
                    fylb:  syjjInfo.pageData.fylbList[index]['fylb'],
                    ryjkpzh: syjjInfo.pageData.jkpzh
                };
                $.getJSON('/actionDispatcher.do?reqUrl=MzsfSfjsBrfy&types=queryByFylbAndRyjkpzh' + '&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            zd_enter.mxfyList = data.d.list;
                        } else {
                            malert(data.c,'top','defeadted')
                        }
                    });
            }
        },
        print: function () {
            window.print();
        }
    }
});

/****************编辑页面 初始化页面加载事件 ******************/


//为table循环添加拖拉的div
var drawWidthNum = $(".patientTable tr").eq(0).find("th").length;
for (var i = 0; i < drawWidthNum; i++) {
    if (i >= 2) {
        $(".patientTable th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
    }
}
//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});

/*********************快捷键********************************/
$(document).keydown(function (e) {
    //F2门诊收费保存
    if (e.keyCode == 113) {
        zd_enter.saveData();//保存
    }
});



//收预交金
var syjjInfo = new Vue({
    el: '#syjjInfo',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data:{
        zyh: '',
        isShow: false,
        sfsj:'',
        isShouYJJ: '',
        yljgmc:'',
        pageData:{},
        zflxServerList: [],
        zflxList:{},
        ifClick:true,
    },
    mounted: function(){
        this.getZflxList();
       this.$nextTick(function () {
           syjjInfo.sfsj =this.$options.filters['formDate'](new Date);
       })
    },
    methods:{
        DateMouted:function () {
            laydate.render({
                elem: '#timeValTwo',
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    syjjInfo.sfsj = value;

                }
            });
        },
        refresh: function(){
            this.getZflxList();
            this.getData(this.zyh);
        },
        //页面加载时自动获取支付类型Dddw数据
        getZflxList: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx", function (json) {
                if (json.a == 0) {
                    var list = json.d.list,
                        listLength = json.d.list.length;
                    for(var i = 0;i < listLength;i++){
                        syjjInfo.zflxList[list[i].zflxbm] = list[i].zflxmc;
                    }
                    syjjInfo.pageData.payType = list[0].zflxbm;
                    syjjInfo.zflxServerList = list; // 为之后的切换用户 清空操作做缓存
                } else {
                    malert(json.c,'top','defeadted');
                    return false;
                }
            });
        },
        getData: function (zyh) {
            this.zyh = zyh.jkpzh; //为之后的刷新页面操作做缓存病人的住院号
            this.getBrxxInfo(zyh.jkpzh,function(brxxInfo){
                syjjInfo.DateMouted()
                syjjInfo.pageData.patientName = brxxInfo.brxm;
                syjjInfo.pageData.hospitalAD = brxxInfo.zyh;
                syjjInfo.pageData = brxxInfo; //缓存一下病人的基本信息   在新增预交记录的时候使用
                syjjInfo.getBryjjlList(zyh,function(yjjlList){
                    syjjInfo.$set( syjjInfo.pageData, "yjjlList", yjjlList );
                });
            });
        },
        showYjjlInfo:function (index) {
            zd_enter.showDetail(index)
            zd_enter.num=0
        },
        //查询病人基本信息
        getBrxxInfo: function(zyh,cb){
            var parm = {
                jkpzh: zyh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=selectByPrimaryKey&parm=" + JSON.stringify(parm), function (brxx) {
                if (brxx.a == 0) {
                    if (brxx.d != null && brxx.d != "") {
                        cb(brxx.d);
                    } else {
                        malert('未查到病人相关记录','top','defeadted');
                    }
                } else {
                    malert('未查到病人相关记录','top','defeadted');
                }
            });
        },
        //查询病人的费用项目
        getBryjjlList: function(zyh,cb){
            $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjkFphm&types=queryFphmByjkpzh&jkpzh=" +zyh.jkpzh, function (yjjlList) {
                if (yjjlList.a == 0) {
                    cb(yjjlList.d);
                } else {
                    malert(yjjlList.c,'top','defeadted');
                }
            });
        },
        //查询预交记录详细信息
        getYjjlInfo: function(yjjlId,cb){
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglFyglYjjl&types=queryById&yjjlid=" + yjjlId, function (yjjlInfo) {
                if (yjjlInfo.a == 0) {
                    if (yjjlInfo.d != null && yjjlInfo.d != "") {
                        cb(yjjlInfo.d);
                    } else {
                        malert('未查到相关记录','top','defeadted');
                    }
                } else {
                    malert(yjjlInfo.c,'top','defeadted');
                }
            });
        },
        quxiao: function(){
            this.zflx = this.zflxServerList[0].zflxbm;
            this.isShow = false;
            tableInfo.isShow = true;
        },
        print: function () {
            window.print();
        },
        //门诊交款
        saveData: function () {
            if (!this.ifClick) return; //如果点击过则直接返回
            this.ifClick = false;
            if (this.pageData.jkje == undefined || this.pageData.jkje == null || this.pageData.jkje <= 0) {
                malert('没有可交款的款项','top','defeadted');
                this.ifClick = true;
                return;
            }
            var json = {
                jkrq: syjjInfo.sfsj,
                kssj: this.pageData.kssj
            };
            this.$http.post('/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=save',
                JSON.stringify(json)).then(function (data) {
                if (data.body.a == 0) {
                    malert('交款成功','top','success');
                    syjjInfo.pageData = {};
                    syjjInfo.ifClick = true;
                } else {
                    malert('交款失败:'+data.body.c,'top','defeadted');
                    syjjInfo.ifClick = true;
                }
            }, function (error) {
                console.log(error);
            });
        },
        // print: function (body) {
        //     // 查询打印模板
        //     var json = {repname: '补打预交单'};
        //     $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
        //         // 清除打印区域
        //         syjjInfo.clearArea(json.d[0]);
        //         // 为打印前生成数据
        //         syjjInfo.printContent(body);
        //         // 开始打印
        //         window.print();
        //     });
        // }
    }
});
