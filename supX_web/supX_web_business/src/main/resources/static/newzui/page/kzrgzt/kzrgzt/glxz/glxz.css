.text-indent-20{
text-indent: 20px;
}
.icon-position{
    top: 9px;
}
.kshyjl-width{
    background:url("/newzui/pub/image/kshyjl-bg.png") center no-repeat;
    background-size:540px 621px;
    width:540px;
    height:621px;
    position: absolute;
    top: calc((100vh - 621px) / 2);
    left: calc((100vw - 540px) / 2);
}
.kshyjl-btn{
    position: absolute;
    bottom: 22px;
    right: 15px;
}
.kshyjl-title{
    font-size: 24px;
    color:#1f2a34;
    width: 100%;
    padding: 25px 0 8px 0;
    display: flex;
    justify-content: center;
    font-weight: 600;
}
.kshyjl-close{
    position: absolute;
    right: 0;
    top: 10px;
    cursor: pointer;
}
.icon-font25:before{
    font-size: 25px;
}
.icon-cf08:hover:before{
    color: rgba(255,255,255,0.36);
}
.kshyjl-table{
    background:rgba(244,178,107,0.08);
    border:1px solid rgba(244,178,107,0.50);
    padding:5px 10px;
    box-sizing: border-box;
    margin: 0 auto;
    width: 485px;
}
.table-Department{
    display: flex;
    justify-content: flex-start;
    color:#354052;
    line-height: 18px;
}
.kshyjl-theme{
    height: 420px;
    right:30px;
    left: 30px;
    padding-top: 10px;
    position: absolute;
    display: flex;
    justify-content: flex-start;
    font-size:14px;
    color:#1f2a34;
    letter-spacing:0;
    flex-wrap: wrap;
    text-align:justify;
    line-height:20px;
}
.kshyjl-theme h2{
    font-size: 16px;
    font-weight: 600;
}


.sjys-width {
    width: 380px;
    height: 220px;
    position: absolute;
    top: calc((100vh - 220px) / 2);
    left: calc((100vw - 380px) / 2);
    z-index: 9999;
    background: #fff;
    box-shadow: 0 0 18px 0px rgba(0,0,0,0.5)
}
.sjys-top {
    width: 100%;
    height: 46px;
    background: #1abc9c;
    color: #fff;
    font-size: 16px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.sjys-content{
    width: 100%;
    float: left;
    padding: 43px 40px 0px 40px;
    box-sizing: border-box;
}
.sjys-pop-btn {
    width: 100%;
    padding: 60px 5px 0 0;
    display: flex;
    justify-content: flex-end;
}
.sjys-textarea{
    width: 100%;
    padding: 13px 15px 0;
    height: 90px;
    box-sizing: border-box;
    float: left;
}
.sjys-textarea textarea{
    width: 100%;
    height: 90px;
    padding: 10px;
    box-sizing: border-box;
    border:1px solid #dfe3e9;
    border-radius:4px;
    -webkit-appearance: none;
}
.sjys-pop-btn-t{
    padding: 33px 5px 0 0;
}
.dyjl{
    background: #fff;
}
.dyjl h2{
    width: 100%;
    font-size: 24px;
    font-weight: bold;
    text-align: center;

}
.font16{
    font-size: 16px !important;
}
.kshyjl-theme h3,.kshyjl-theme h4{
width: 100%;
}
.dyjl-center{
display: flex;
justify-content: center;
width: 100%;
flex-wrap: wrap;
}
.margin-r-0{
margin-right: 0;
}
.bgNone:after{
    width: 10px;
    height: 10px;
    display: block;
    position: absolute;
    top:7px;
    right: 10px;
    content: "\f0d7";
    font-family: 'FontAwesome';
    z-index: 20;
    zoom: 1;
    text-align: center;
    color: #a7b1c2;
}
.glxz-ms{
min-width: 57px;
}
.Absolutely{
width: 100% !important;
}
.height66{
height: 66px;
}
.padd-t-13{
 padding-top: 13px;
}
.Grouping{
width: 100%;
display: flex;
justify-content: space-between;
padding-top: 11px;


}
.group-left{
    width: calc((100% - 10px)/2);

}
.group-left-top{
    background:#4a90e2;
    border-radius:4px 4px 0 0;
    width:100%;
    height:40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}
.group-right-top{
    background:#f3a74f;
}
.group-content{
width: 100%;
border:1px solid #e9eee6;

}
.group-content > li{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 40px;
    border-top: 1px solid #e9eee6;
}
.group-content > li:nth-child(2n){
    background:#fdfdfd;
}
.group-content > li .group-span{
    width: calc((100% - 40px)/3);


}
.group-content > li .group-span:first-child{
width: 40px;
}
.glxz-list{


}
.glxz-list li{
position: relative;
width: 100%;
overflow: hidden;
padding-bottom: 10px;
}
.glxz-list li:after{
    content:'';
    width: 2px;
    height: 100%;
    background:#eff1f4;
    position: absolute;
    top: 0;
    left: 6px;
    z-index: -1;
}
.glxz-list li:last-child:after{
    background: none;
}
.glxz-dot{
    background:#44cebd;
    width:14px;
    height:14px;
    border-radius:100%;
    float: left;
}
.glxz-dot1{
    background:#bcbec0;
    width:10px;
    height:10px;
    border-radius:100%;
    float: left;
    margin-left: 2px;
}
.glxz-title{
    font-size:16px;
    color:#475158;
    float: left;
    width: calc(100% - 40px);
    margin-left: 10px;
    padding: 5px 0;
}
.glxz-content{
    width: calc(100% - 20px);
    background:#f9f9f9;
    border:1px solid #dce0e5;
    border-radius:4px;
    padding: 10px;
    box-sizing: border-box;
    float: right;
    position: relative;
}
.glxz-content h2{
font-weight: bold;
font-size:16px;
    color:#393f45;
}
.glxz-sub{
    width: 100%;
    font-size:14px;
    line-height: 18px;
    color:#393f45;
    padding: 5px 0;
}

.Headimg{
    width:36px;
    cursor: pointer;
    position: relative;
    margin-right: 7px;
    height:36px;
    border-radius:100%;
}
.Headimg .headImg:hover{
    box-shadow: 0px 0 10px 0px #014F40;
}

.Headimg .headImg{
    width: 100%;
    border-radius:100%;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}
.hoverAvter:before{
    width: 0;
    height: 0;
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    border-width:0 5px 5px;
    border-style:solid;
    border-color:transparent transparent black;
}
.hoverAvter{
    background:#ffffff;
    box-shadow: 0 0px 10px 0 rgba(2,41,33,0.30);
    border-radius:4px;
    width:140px;
    height:130px;
    top: 0;
    overflow: hidden;
    position: fixed;
    z-index: 11111;
}
.djzt {
    position: absolute;
    right: -44px;
    top: 1px;
    box-shadow: 0 6px 24px 0 rgba(0, 0, 0, 0.20);
    transform: rotate(46deg);
    color: #ffffff;
    text-align: center;
    overflow: hidden;
    width: 130px;
    font-size: 12px;
    background-color: #f4b26b;
    height: 35px;
    line-height: 35px;
}
.hoverAvter .headImg{
    background:#3dc9ad;
    width:60px;
    height:60px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    margin-left: auto;
    margin-right:auto;
    border-radius:100%;
}

.username{
    color:#333333;
    margin: 0 auto;
    font-weight: bold;
}
.color-d66b3f{
    color: #d66b3f;
}
.color-f4b26b{
    color: #f4b26b;
}
.color-7f5cff{
    color: #7f5cff;
}
.color-ff5c63{
    color: #ff5c63;
}
#content .icon-iocn1:before{
    color: #ff5c63;
    margin-right: 3px;
}
#content .icon-iocn1{
    font-size: 14px;
    vertical-align: initial;
}
.color-3f64d6{
    color: #3f64d6;
}
.color-72bc1a{
    color: #72bc1a;
}
.color-00a7ff{
    color: #00a7ff;
}
.color-757c83{
    color: #757c83;
}