var yzglTable = new Vue({
    el: '.yzglTable',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        parms: {},
        arrWidth: [],
        jsonList:[],
        line:[],
        obj:{}
},
created: function () {
    this.getYzList()
}
,
methods: {
    getYzList: function () {
        this.parms.ryljbm = sessionStorage.ryljbm
        $.getJSON("/actionDispatcher.do?reqUrl=LcljXtwhBdwhysYz&types=queryLjbd&parm=" + JSON.stringify(this.parms), function (json) {
            if (json.a == '0') {
                if (json.d != null) {
                    yzglTable.jsonList = json.d;
                    for(var i=0;i<json.d.nrs.length;i++) {
                        yzglTable.obj.line=JSON.stringify(json.d.nrs[i][i].line)
                            yzglTable.line.push(JSON.parse(JSON.stringify(yzglTable.obj)))
                    }
                    yzglTable.$nextTick(function () {
                        var yzHeader = $('.yzHeader li')
                        $.each(yzHeader, function (index, value) {
                            yzglTable.arrWidth.push($(value).outerWidth())
                        })
                        console.log(yzglTable.arrWidth)
                    })
                } else {
                    malert('暂无最新数据', 'top', 'defeadted');
                }
            } else {
                malert(json.c, 'top', 'defeadted');
            }
        });
    }
,
    edit: function (index) {
    }
,
    remove: function () {

    }
}
,
})

function getDatayz() {
    yzglTable.getYzList()
}
