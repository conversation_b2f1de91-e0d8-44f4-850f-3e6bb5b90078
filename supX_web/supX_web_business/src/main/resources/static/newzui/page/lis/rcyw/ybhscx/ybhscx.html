<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>样本核收查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="ybhscx.css" rel="stylesheet">
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper">
    <div class="panel box-fixed">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-qxhs paddr-r5" @click="qxhs">取消核收</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-jsh paddr-r5" @click="js">拒收</button>
            <button class="tong-btn btn-parmary-b icon-gl paddr-r5" @click="guolu">过滤</button>
            <button class="tong-btn btn-parmary-b icon-yl paddr-r5">预览</button>
            <button class="tong-btn btn-parmary-b icon-dysq paddr-r5" @click="Btnprint">打印</button>
            <button class="tong-btn btn-parmary-b icon-scybh paddr-r5">生成样本号</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">核收日期</label>
                    <div class="zui-input-inline zui-select-inline zui-date">
                        <i class="datenox icon-rl"></i>
                        <input type="text" name="phone" class="zui-input todate padd-l33" v-model="param.time" placeholder="请选择核收日期">
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label padd-l14">检索码</label>
                    <div class="zui-input-inline">
                        <input type="text" class="zui-input" name="input1" placeholder="请输入检索码" v-model="param.bah"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label padd-l14">执行设备</label>
                    <div class="zui-input-inline margin-l13">
                        <select-input @change-data="resultChange"
                                      :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="param.zxsb"
                                      :search="true" :name="'param.zxsb'">
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <button class="zui-btn btn-primary  xmzb-db" @click="cx">查询</button>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable" id="utable1 zui-table-body" z-height="full" style="border:none;margin-top: 108px;padding: 0 10px; background: #fff">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" z-width="50px">
                        <input-checkbox @result="reCheckBox" :list="'jydjList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="id" z-fixed="left" z-width="50px">
                        <div class="zui-table-cell">类型</div>
                    </th>
                    <th z-field="username" z-width="80px" z-style="text-align:center;">
                        <div class="zui-table-cell">样本号</div>
                    </th>
                    <th z-field="city" z-width="80px">
                        <div class="zui-table-cell">类别</div>
                    </th>
                    <th z-field="sign" z-width="80px">
                        <div class="zui-table-cell">病员姓名</div>
                    </th>
                    <th z-field="experience" z-width="60px">
                        <div class="zui-table-cell">性别</div>
                    </th>
                    <th z-field="score" z-width="60px">
                        <div class="zui-table-cell">年龄</div>
                    </th>
                    <th z-field="classify" z-width="110px">
                        <div class="zui-table-cell">住院号/门诊号</div>
                    </th>
                    <th z-field="wealth" z-width="90px">
                        <div class="zui-table-cell">扣费名称</div>
                    </th>
                    <th z-field="wealth1" z-width="90px">
                        <div class="zui-table-cell">送检科室</div>
                    </th>
                    <th z-field="wealth2" z-width="90px">
                        <div class="zui-table-cell">送检医师</div>
                    </th>
                    <th z-field="wealth3" z-width="100px">
                        <div class="zui-table-cell">核收时间</div>
                    </th>
                    <th z-field="caozuo" z-fixed="right" z-width="100px">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table">
            <table class="zui-table">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jydjList" @click="checkSelect([$index,'some','jydjList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                <td width="50px">
                <div class="zui-table-cell">
                <input-checkbox @result="reCheckBox" :list="'jydjList'"
                :type="'some'" :which="$index"
                :val="isChecked[$index]">
                </input-checkbox>
                </div>
                </td>
                <td width="50px"><div class="zui-table-cell" v-text="jydjlx_tran[item.lx]"></div></td>
                <td width="80px"><div class="zui-table-cell" v-text="item.ybbm"></div></td>
                <td width="90px"><div class="zui-table-cell" v-text="jydjyblx_tran[item.yblx]"></div></td>
                <td width="80px"><div class="zui-table-cell" v-text="item.brxm"></div></td>
                <td width="80px"><div class="zui-table-cell" v-text="brxb_tran[item.xb]"></div></td>
                <td width="60px"><div class="zui-table-cell title" :data-title="item.nl" v-text="item.nl"></div></td>
                <td width="110px"><div class="zui-table-cell" v-text="item.bah"></div></td>
                <td width="110px"><div class="zui-table-cell" v-text="item.fymc"></div></td>
                <td width="100px"><div class="zui-table-cell" v-text="item.ksmc"></div></td>
                <td width="90px"><div class="zui-table-cell" v-text="item.sqys"></div></td>
                <td width="90px"><div class="zui-table-cell">{{item.ybhsrq|formDate}}</div></td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-hs icon-font"></i>
                        <i class="icon-js icon-font"  data-toggle="sideform" @click="jsdydj(item)"  data-target="#brzcList"></i>
                        <i class="icon-zf icon-font"></i></div>
                    </td>
                </tr>
                <!--<tr>-->
                <!--<td width="50px">-->
                <!--<div class="zui-table-cell">-->
                <!--&lt;!&ndash;<input-checkbox @result="reCheckBox" :list="'jydjList'"&ndash;&gt;-->
                <!--&lt;!&ndash;:type="'some'" :which="$index"&ndash;&gt;-->
                <!--&lt;!&ndash;:val="isChecked[$index]">&ndash;&gt;-->
                <!--&lt;!&ndash;</input-checkbox>&ndash;&gt;-->
                <!--</div>-->
                <!--</td>-->
                <!--<td width="50px"><div class="zui-table-cell">序号</div></td>-->
                <!--<td width="80px"><div class="zui-table-cell" >类型</div></td>-->
                <!--<td width="90px"><div class="zui-table-cell">来源</div></td>-->
                <!--<td width="80px"><div class="zui-table-cell">样本号</div></td>-->
                <!--<td width="80px"><div class="zui-table-cell">病员姓名</div></td>-->
                <!--<td width="60px"><div class="zui-table-cell">年龄</div></td>-->
                <!--<td width="110px"><div class="zui-table-cell">住院号/门诊号</div></td>-->
                <!--<td width="100px"><div class="zui-table-cell">检验项目</div></td>-->
                <!--<td width="90px"><div class="zui-table-cell">扣费名称</div></td>-->
                <!--<td width="90px"><div class="zui-table-cell">扣费名称</div></td>-->
                <!--<td width="90px"><div class="zui-table-cell">送检科室</div></td>-->
                <!--<td width="100px"><div class="zui-table-cell">-->
                <!--<i class="icon-hs icon-font"></i>-->
                <!--<i class="icon-js icon-font"></i>-->
                <!--<i class="icon-zf icon-font"></i>-->
                <!--</div></td>-->
                <!--</tr>-->

                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
    <div class="popus-right side-form ng-hide" style="width: 320px; padding-top: 0; top:0;bottom: 0;height: 100%;" id="brzcList" role="form">
        <h2><span>拒收理由</span>
            <div class="setwin">
                <a href="javascript:;" class="fr closex ti-close"></a>
            </div>


        </h2>
        <div class="sample">样本拒收原因</div>
        <div class="sample-select">
            <select-input @change-data="resultChange" :not_empty="false"
                          :child="jydjjsyy_tran" :index="popContent.jsyy" :val="popContent.jsyy"
                          :name="'popContent.jsyy'">
            </select-input>
        </div>
        <div class="sample">拒收处理</div>
        <div class=" sample-texarea">
            <textarea v-model="jscl" placeholder="请输入拒收处理意见"></textarea>
        </div>
        <div class="pop-ok sample-btn buttond">
            <button class="pop-btn" @click="close">取消</button>
            <button class="pop-btn pop-confirm" @click="saveOk">确定</button>
        </div>

    </div>

    <div id="pop">
        <!--<transition name="pop-fade">-->
        <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
        <div class="zui-form podrag  bcsz-layer " :class="[{show:isShow},flag ?'pop-850':'pop-width']"
             style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title" v-if="dyShow"></div>
            <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn"
                                                                @click="isShowpopL=false,isShow=false">&times;</i></span>
            <div class="layui-layer-content" v-if="dyShow">
                <div class=" layui-mad layui-height">
                    确定取消核收选中项目吗？
                </div>
            </div>
            <div class="zui-row buttonbox " v-if="dyShow">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="isShowpopL=false,isShow=false">取消
                </button>
                <button class="zui-btn btn-primary table_db_save xmzb-db" @click="delOk">确定</button>
            </div>
        </div>
        <!--</transition>-->
    </div>
</div>
<div id="isTabel">
    <div class="pophide" :class="{'show':isShow}"></div>
    <div class="zui-form podrag  bcsz-layer zui-800 " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title ">过滤查询</div>
        <div class="guolv-xinzeng">
            <span class="layui-txt" @click="append()">新增一项</span>
            <i class="color-btn" @click="isShow=false"
               style="margin-top:-17px;width: 16px;height: 16px;display: inline-block;margin-left: 10px;float: right">×</i>
        </div>
        <div class="layui-layer-content">
            <div class=" layui-mad">
                <ul class="guolv-header guolv-style">
                    <li class="line">项目</li>
                    <li class="line">条件</li>
                    <li class="line">结果</li>
                    <li class="line">连接条件</li>
                    <li class="line">操作</li>
                </ul>
                <ui class="guolv-content" id="guo_append">
                    <div class="guolv-style guolv-bottom" v-for="(item, $index) in cxtjList">
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'xm_' + $index"
              											@change-data="resultChange_item" :not_empty="true"
              											:child="xmybgl_tran" :index="'item.xm'" :val="item.xm"
              											:name="$index + '.xm.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                 <select-input :id="'tj_' + $index"
              											@change-data="resultChangeTj_item" :not_empty="true"
              											:child="tjybgl_tran" :index="'item.tj'" :val="item.tj"
              											:name="$index + '.tj.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>
                            </div>
                        </li>
                        <li class="line">
                        	<!-- 类型 -->
                            <div class="zui-select-inline" v-if="isLxNum.indexOf(''+$index)>=0">
                             	<select-input :id="'LX_' + $index"
              											@change-data="resultChangeTj_item" :not_empty="true"
              											:child="jydjlx_tran" :index="'item.jg'" :val="item.jg"
              											:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>
                            </div>
                            <!-- 输入框通用 -->
                            <div class="zui-select-inline" v-if="isTyNum.indexOf(''+$index)>=0">
                             	<input type="text" class="zui-input" v-model="item.jg"/>
                             </div>
                             <!-- 性别 -->
                            <div class="zui-select-inline" v-if="isXbNum.indexOf(''+$index)>=0">
                             	<select-input :id="'XB_' + $index"
              											@change-data="resultChangeTj_item" :not_empty="true"
              											:child="xtwhxb_tran" :index="'item.jg'" :val="item.jg"
              											:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>
                            </div>
                             <!-- 科室 -->
                            <div class="zui-select-inline" v-if="isKsNum.indexOf(''+$index)>=0">
									<select-input @change-data="Wf_YppfChange" :not_empty="false"
			 									 	:child="util.sjks" :index="'ksmc'" :index_val="'ksbm'"
			  										:val="item.jg" :name="'PfxxJson.'+$index+'.ksbm'" :index_mc="'ksmc'" :search="true">
									</select-input>
                            </div>
                            <!-- 医生 -->
                            <div class="zui-select-inline" v-if="isYsNum.indexOf(''+$index)>=0">
									<select-input @change-data="Wf_YsChange" :not_empty="false"
			 									 	:child="util.sjys" :index="'ysmc'" :index_val="'ysbm'"
			  										:val="item.jg" :name="'PfxxJsonx.'+$index+'.ysbm'" :index_mc="'ysmc'" :search="true">
									</select-input>
                            </div>
                             <!-- 样本类型 -->
                            <div class="zui-select-inline" v-if="isYblxNum.indexOf(''+$index)>=0">
                             	<select-input :id="'XB_' + $index"
              											@change-data="resultChangeTj_item" :not_empty="true"
              											:child="jydjyblx_tran" :index="'item.jg'" :val="item.jg"
              											:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>
                            </div>

                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'ljtj_' + $index"
              											@change-data="resultChangeLjtj_item" :not_empty="true"
              											:child="ljtjybgl_tran" :index="'item.ljtj'" :val="item.ljtj"
              											:name="$index + '.ljtj.' + 1" :search="true" @keydown="nextFocus($event)"
              											data-notEmpty="false">
									</select-input>
                            </div>
                        </li>
                        <li class="line">
                            <span class="icon-sc" @click="sc($index)"></span>
                        </li>
                    </div>

                </ui>
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="save">保存</button>
        </div>
    </div>
</div>

</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ybhscx.js"></script>
<script>
    $(function () {
        $(".zui-input").uicomplete();
        $(".zui-table-view").uitable();
        lay('.zui-date .zui-input').each(function () {
            laydate.render({
                elem: this
                , eventElem: '.zui-date i.datenox'
                , trigger: 'click'
            });
        });
        $(".f-hzgl").uitab();

        //测试JS
        $("#utable1 .zui-table-body tr").dblclick(function () {
            $(".side-form").removeClass("ng-hide").after("<div class='side-form-bg'></div>");
            $("#tab-info input").attr("disabled", true)
            $("body").css("overflow", "hidden");
            $("button#info_edit").text("修改");
            $(".info_name").text($(this).children("[field=username] ").children(".zui-table-cell").html());
        });

        $("button#info_edit").click(function () {
            $("#tab-info input").attr("disabled", false);
            $(this).text("提交数据");
        });
    });
</script>
</body>
</html>
