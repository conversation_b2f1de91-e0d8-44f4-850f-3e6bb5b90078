<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>入库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="pdgl1.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-show="isShowkd">生成盘点</button>
            <button class="tong-btn btn-parmary icon-width icon-zdsc-b padd-l-25 fyxm-hide" @click="kd(1)" :class="{'btn-show':isShowpopL}">自动生成</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMel" v-show="addShow">添加材料</button>
            <button class="tong-btn btn-parmary-b icon-width icon-zdsc-l padd-l-25" @click="autoGen" v-show="addShow">自动生成</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx" v-if="isShowpopL">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx" v-if="isShowpopL==false && isShow==false">刷新</button>
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="sx" v-if="isShow && isShowpopL==false">刷新</button>
        </div>
        <div class="tong-search" :class="{'tong-padded':isShow}">
            <div class="zui-form" v-show="isShowkd">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">二级库房</label>
                    <div class="zui-input-inline wh122">
                        <select-input :search="true" @change-data="resultChangeYf"
                                      :child="YFSelect" :index="'yfmc'" :index_val="'yfbm'" :val="pdbContent.yfbm"
                                      :name="'pdbContent.yfbm'">
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline  flex-container flex-align-c margin-f-l5">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择处方结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="search"/>
                    </div>
                </div>
            </div>
            <div class="jbxx fyxm-hide" :class="{'btn-show':isShow}">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20">
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">当前二级库房</label>
                            <div class="zui-input-inline wh122 margin-l-25">
                                <select-input :search="true" @change-data="resultChangeYf"
                                              :child="YFSelect" :index="'yfmc'" :index_val="'yfbm'" :val="pdbContent.yfbm"
                                              :name="'pdbContent.yfbm'">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label margin-f-l10">盘点方式</label>
                            <div class="zui-input-inline  wh122 margin-l-15">
                                <select-input @change-data="resultChange"
                                              :child="options" :index="'text'" :index_val="'pdWay'" :val="pdbContent.pdWay"
                                              :name="'pdbContent.pdWay'" :search="true" :index_mc="'text'" >
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline padd-l-40" v-show="pdWayShow.zlName">
                            <label class="zui-form-label">材料种类</label>
                            <div class="zui-input-inline  wh122 margin-l-25">
                                <select-input @change-data="resultChangeYpzl"
                                              :child="YPZLSelect" :index="'ypzlmc'" :index_val="'ypzlbm'" :val="pdbContent.ypzlbm"
                                              :name="'pdbContent.ypzlbm'" :search="true" :index_mc="'ypzlmc'" >
                                </select-input>
                            </div>
                        </div>
                        <!-- <div class="zui-inline padd-l-40" v-show="pdWayShow.ypName">
                            <label class="zui-form-label">材料名称</label>
                            <div class="zui-input-inline margin-l-25">
                                <input id="ypmc" @keydown.enter.up.down="changeDown($event,'text')" v-model="ypmcInput" data-notEmpty="true" class="zui-input"/>
                                <search-table :message="searchCon" :selected="selSearch" :current="dg.page" @click-one="checkedOneOut" @click-two="selectOne" :rows="dg.rows"
                                              :total="total" :them="them" :them_tran="them_tran">
                                </search-table>

                            </div>
                        </div> -->
                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期:<i v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" ></i></span>
                    </div>
                </div>
            </div>

            <div class="jbxx fyxm-hide" :class="{'btn-show':addShow}">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20">
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">凭证号</label>
                            <div class="zui-input-inline padd-l-20">
                                <select-input @change-data="resultRydjChange"
                                              :child="pzList" :index="'pdpzh'" :index_val="'pzNum'" :val="pdbContent.pzNum"
                                              :name="'pdbContent.pzNum'" :search="true" :index_mc="'pdpzh'" >
                                </select-input>
                            </div>
                        </div>

                        <div class="zui-inline padd-l-40" v-show="pddhShow">
                            <label class="zui-form-label">盘点单号</label>
                            <div class="zui-input-inline padd-l-20 margin-l-5">
                                <select-input @change-data="resultRydjChange"
                                              :child="pddhList" :index="'pdlrpzh'" :index_val="'pdlrpzh'" :val="pdlrpzh"
                                              :name="'pdlrpzh'" :search="true" :index_mc="'pdlrpzh'" >
                                </select-input>
                            </div>
                        </div>

                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期:<i v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" ></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view  padd-r-10 padd-l-10"   id="utable1" z-height="full">
        <!--未核盘点列表-->
        <div class="zui-table-header" v-show="isShowkd">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>盘点单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>盘点日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单人</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>

        </div>
        <div class="zui-table-body "  v-show="isShowkd" @scroll="scrollTableFixed($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in jsonList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','jsonList'],$event)" :tabindex="$index">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.pdpzh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.pdrq,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zdy"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="zhuangtai[item.qrzfbz]" :class="item.qrzfbz=='0' ? 'color-dsh':item.qrzfbz=='1' ? 'color-ysh' : item.qrzfbz=='2' ? 'color-yzf' : '' ">状态</div>
                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">
                            <span style="display: flex;justify-content: center;align-items: center;">
                                <em class="width30" v-if="item.qrzfbz==0">
                                    <i  class="icon-bj" @click="lr($index,item)" data-title="操作"></i>
                                </em>
                                <em  class="width30" v-if="item.qrzfbz==0">
                                    <i  class="icon-sh" @click="shpd($index)" data-title="审核"></i>
                                </em>
                                <em  class="width30" v-if="item.qrzfbz != 2">
                                    <i  class="icon-js " @click="zf($index)" data-title="作废" ></i>
                                </em>
                                <em class="width30">
                                   <i  class="icon-width icon-widths icon-zk" @click="finish($index,item.pdpzh)" data-title="盘点完成"></i>
                                   <!--<i  class="icon-width icon-widths icon-zk" @click="finish($index,item.pdpzh)" data-title="盘点完成" ></i>-->
                                </em>
                               </span>
                        </div>
                    </td>
                    <p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <div v-show="isShow"  class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料编码</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>实存数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>生产日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <!--盘点生成明细-->
        <div class="zui-table-body" @scroll="scrollTableFixed($event)" v-show="isShow">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in pdsclist"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','pdsclist'],$event)" :tabindex="$index">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypbm">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-left text-over-2" v-text="item.ypmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.ypgg"><i v-text="item.ypgg" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                            <div class="zui-table-cell cell-s relative">
                                <div class="title title-width" :data-title="item.xtph"><i v-text="item.xtph" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                            </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kcsl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.scsl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yplj"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell" v-text="item.ljje"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.scph"><i v-text="item.scph" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="fDate(item.scrq,'date')"><i v-text="item.scrq" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="fDate(item.yxqz,'date')"><i v-text="item.yxqz" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kfdwmc">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cdmc">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ghdw">状态</div>
                    </td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span style="display: flex;justify-content: center;align-items: center;">
                                <!-- <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"></i></em> -->
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index,pdsclist)"></i></em>
                            </span>
                        </div>
                    </td>

                </tr>
                </tbody>
            </table>
        </div>
        <!--盘点录入-->
        <div class="zui-table-header" v-show="addShow">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料编码</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产品标准号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>批准文号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>实存数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>生产日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body "  v-show="addShow" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in pdlrList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','pdlrList'],$event)" :tabindex="$index">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypbm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl relative">
                            <div class="title title-width" :data-title="item.ypmc"><i v-text="item.ypmc" style="width: 84px;display: block;overflow: hidden; text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.ypgg"><i v-text="item.ypgg" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.cpbzh"><i v-text="item.cpbzh" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.pzwh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kcsl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <input class="zui-input border-r4" v-model="item.scsl" type="number"
                            style="width: 70px;height: inherit;text-align: center; margin: 0 auto;"/>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yplj"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ljje"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.scph"><i v-text="item.scph" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.scrq"><i v-text="item.scrq" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.yxqz"><i v-text="item.yxqz" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cdmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ghdw"></div>
                    </td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span style="display: flex;justify-content: center;align-items: center;">
                                <!-- <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"></i></em> -->
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index,pdlrList)"></i></em>
                            </span>
                        </div>
                    </td>

                </tr>
                </tbody>
            </table>
        </div>
        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l"  v-show="addShow">
            <div class="zui-table-header ">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in pdlrList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','pdlrList'],$event)" :tabindex="$index" class="tableTr2">
                        <td class="cell-m"> <div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--end-->
        <!--右侧固定-->
        <div class="zui-table-fixed table-fixed-r" v-show="addShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in pdlrList"  :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','pdlrList'],$event)">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span style="display: flex;justify-content: center;align-items: center;">
                               <!--  <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"></i></em> -->
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index,pdlrList)"></i></em>
                            </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--end-->

        <div class="zui-table-fixed table-fixed-l"  v-show="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in pdsclist" :tabindex="$index"  class="tableTr2"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','pdsclist'],$event)">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage" v-show="isShowkd" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="rkgl-position  action-bar fixed" v-if="isShow">
            <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="savePdsc">提交</button>
           </span>
        </div>
        <div class="rkgl-position  action-bar fixed" v-if="addShow">
            <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="zfpd">作废</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="savelr">保存</button>
           </span>
        </div>
        <div class="rkgl-position  action-bar fixed" v-if="shShow">
        	<span class="rkgl-fr">
        		 <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
        		 <button class="tong-btn btn-parmary-f2a xmzb-db" @click="zfpd">作废</button>
        		 <button class="tong-btn btn-parmary xmzb-db" @click="shlrpd">审核</button>
        	</span>
        </div>
        <div class="rkgl-position  action-bar fixed" v-if="pdfinish" id="pdsh">
        	<span class="rkgl-fr">
        		 <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
        		 <button class="tong-btn btn-parmary-f2a xmzb-db" @click="zfpd">作废</button>
        		 <button class="tong-btn btn-parmary xmzb-db" @click="pdwc">完成盘点</button>
        	</span>
        </div>

    </div>
</div>

    <div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side">
            <ul class="tab-edit-list tab-edit2-list">
                <li>
                        <i>材料名称</i>
                        <input id="ypmc" class="label-input text-indent10" @keydown.enter.up.down="changeDown($event,'text')" v-model="ypmcInput" data-notEmpty="true" />
                        <search-table :message="searchCon" :selected="selSearch" :current="dg.page" @click-one="checkedOneOut" @click-two="selectOne" :rows="dg.rows"
                                      :total="total" :them="them" :them_tran="them_tran">
                        </search-table>
                </li>
                <li>
                        <i>材料规格</i>
                        <input type="text" class="label-input background-h" disabled="disabled" v-model="popContent.ypgg"/>
                </li>
                <li>
                        <i>分装比例</i>
                        <input class="label-input background-h" disabled="disabled" v-model="popContent.fzbl" />
                </li>
                <li>
                        <i>材料批号</i>
                        <input class="label-input background-h" disabled="disabled" v-model="popContent.xtph" />
                </li>
                <li>
                        <i>二级库房单位</i>
                        <input class="label-input background-h" disabled="disabled" v-model="popContent.yfdw" />
                </li>
                <li>
                        <i>生产日期</i>
                        <input class="label-input background-h" disabled="disabled" v-model="popContent.scrq" />
                </li>
                <li>
                        <i>材料零价</i>
                        <input class="label-input background-h" disabled="disabled" v-model="popContent.yplj" />
                </li>
                <li>
                        <i>帐存数量</i>
                        <input class="label-input background-h" disabled="disabled" v-model="popContent.kcsl" />
                </li>
                <li>
                        <i>实存数量</i>
                        <input class="label-input" id="scsl" v-model="popContent.scsl"  @keyup.enter="addOne"/>
                </li>
            </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="adds">保存</button>
        </div>
    </div>

</div>



<script src="pdgl1.js"></script>

</body>

</html>
