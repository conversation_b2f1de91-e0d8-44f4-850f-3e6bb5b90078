/**
 * Created by mash on 2017/9/30.
 */
//(function () {
    var yjs = new Vue({
        el: '#yjs',
        mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
        data: {
            jsonList: [],
            json: {},
            inpid: null,
            yjsContent: {},
            ynfyContent: {},
            brxxContent: {},
            printContentt: {},
            printList: [],
            jtdz: null,
            rylx: null,
            zyts: 0,
            jsgs:""
        },
    updated:function(){
        changeWin()
    },
    created:function(){
        this.json.cyqk = '1';
        this.json.cylx = '0';
        yjs.jsgs = "";
        this.$forceUpdate();
    },
        methods: {
            print: function () {
                var parm = {
                    inpid: yjs.json.inpid
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=yjs&method=queryDzlx&parm=" + JSON.stringify(parm), function (json) {
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            yjs.yjsContent = res.list[0];
                            yjs.jtdz = res.list[0].address;
                            yjs.rylx = yjs.nhrysx_tran[res.list[0].memberpro];
                            console.log(yjs.rylx);
                            yjs.zyts = parseInt(Math.abs(yjs.json.bqcyrq - yjs.json.ryrq) / 1000 / 60 / 60 / 24);
                            if(yjs.zyts==0){
                                yjs.zyts=1;
                            }
                            yjs.printBc();
                        } else {
                            malert(json.c,"bottom","defeadted");
                        }
                    });
            },
            printBc: function () {
                console.log(yjs.yjsContent);
                console.log(yjs.json);
                console.log(yjs.ynfyContent);
                yjs.printContentt.rylx = yjs.rylx;
                yjs.printContentt.brxm = yjs.json.brxm;
                yjs.printContentt.cylx = yjs.gznhcy_tran[yjs.json.cylx];
                yjs.printContentt.brxb = yjs.brxb_tran[yjs.json.brxb];
                yjs.printContentt.nl = yjs.json.nl;
                yjs.printContentt.ybkh = yjs.json.ybkh;
                yjs.printContentt.bcrq = yjs.fDate(new Date(), 'date');
                yjs.printContentt.jtdz = yjs.jtdz;
                yjs.printContentt.lxdh = yjs.json.lxdh;
                yjs.printContentt.ryrq = yjs.fDate(yjs.json.ryrq, 'date');
                yjs.printContentt.bqcyrq = yjs.fDate(yjs.json.bqcyrq, 'date');
                yjs.printContentt.ryzdmc = yjs.json.ryzdmc;
                yjs.printContentt.zyts = yjs.zyts;
                yjs.printContentt.fyje = yjs.ynfyContent.fyje;
                yjs.printContentt.zffy = yjs.fDec(yjs.fDec(yjs.ynfyContent.fyje,2)-yjs.fDec(yjs.yjsContent.insurancecost, 2));
                yjs.printContentt.ndzyts = yjs.json.ndzyts;
                yjs.printContentt.scfyje = yjs.ynfyContent.scfyje;
                yjs.printContentt.undulatingline = yjs.yjsContent.undulatingline;
                yjs.printContentt.bottomredeem = yjs.yjsContent.bottomredeem;
                yjs.printContentt.insurecost = yjs.yjsContent.insurecost;
                yjs.printContentt.medicinecost = yjs.yjsContent.medicinecost;
                yjs.printContentt.salvafpcost = yjs.yjsContent.salvafpcost;
                yjs.printContentt.salvayfcost = yjs.yjsContent.salvayfcost;
                yjs.printContentt.insurecost = yjs.yjsContent.insurecost;
                yjs.printContentt.civilcost = yjs.yjsContent.civilcost;
                yjs.printContentt.salvajscost = yjs.yjsContent.salvajscost;
                yjs.printContentt.compensatecost = yjs.yjsContent.compensatecost;
                yjs.printContentt.insurancecost=yjs.yjsContent.insurancecost;
                yjs.printContentt.sjbc=yjs.fDec(yjs.yjsContent.compensatecost+yjs.yjsContent.insurecost+yjs.yjsContent.civilcost+yjs.yjsContent.salvajscost,2);
                yjs.printContentt.zfje = yjs.fDec(yjs.ynfyContent.fyje - yjs.printContentt.sjbc,2);
                var xs='0.9';
                if(yjs.yjsContent.undulatingline==0){
                    xs='0.95';
                }
                if( yjs.printContentt.zfje=='0.00'){
                    xs='1.00';
                }
                yjs.printContentt.jsgs="("+yjs.yjsContent.insurancecost+"-"+yjs.yjsContent.undulatingline+")*"+xs;
                console.log(yjs.printContentt);
                yjs.doPrint();
            },
            doPrint: function () {
                // 查询打印模板
                var json = {repname: '报销补偿2'};
                $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    // 清除打印区域
                    yjs.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    yjs.drawList = JSON.parse(json.d[0]['canvas']);
                    yjs.creatCanvas();
                    yjs.reDraw();
                    // 为打印前生成数据
                    yjs.printContent(yjs.printContentt);
                    // 开始打印
                    window.print();
                });
            },
            getData: function () {
                yjs.json = hyjl.brxxList;
                console.log('--->',hyjl.brxxList);
                if (yjs.json.zdjb == null || yjs.json.zdjb == undefined ){
                    yjs.json.zdjb = "0";
                }
                yjs.getAccount();
            },

            getAccount: function () {
                this.param = {
                    page: 1,
                    rows: 10,
                    zyh: yjs.json.zyh
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=yjs&method=queryYnfy&parm=" + JSON.stringify(this.param), function (json) {
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            yjs.ynfyContent = res.list[0];
                            console.log(yjs.ynfyContent);
                        } else {
                            malert(json.c,"bottom","defeadted");
                        }
                    });
            },
            ybcy: function () {
                $.ajaxSettings.async = false;
                if (yjs.json.inpid == null || yjs.json.inpid == undefined) {
                    malert("该病人无补偿证号，未农合入院","bottom","defeadted");
                    return
                }
                if (yjs.json.cyqk == null) {
                    malert("出院情况不能为空！","bottom","defeadted");
                    return
                }
                var head = {
                    operCode: "S07",
                    billCode: left_tab1.billCode,
                    rsa: ""
                };
                var body = {
                    inpId: yjs.json.inpid,
                    dischargeDate: yjs.fDate(yjs.json.cyrq, 'date'),
                    //dischargeDepartments: '03',  //出院科室
                    dischargeDepartments: hyjl.brxxList.admissionDepartments,  //出院科室
                    dischargeStatus: yjs.json.cyqk,
                    icdAllNo:yjs.json.diseasecode,//"ZMQSB.XNH",
                    treatCode:yjs.json.treatcode,//"09",
                    turnMode:yjs.json.turnMode,//转诊类型
                    turnCode:yjs.json.turnCode,//转诊转院编码
                    turnDate:yjs.fDate(yjs.json.bqcyrq, 'date'),//转院日期
                    hisTotal:yjs.ynfyContent.fyje,//10500,
                    areaCode:yjs.json.xzqh,//"520421",
                    isTransProvincial:yjs.json.zdjb,
                    majorDiseaseICD:yjs.json.majordiseaseicd//"N18.903"
                };

                var footer = {
                    cyczy: userId
                };
                var param = {
                    head: head,
                    body: body,
                    footer: footer
                };
                var str_param = JSON.stringify(param);
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                        console.log(json);
                        if (json.a == 0) {
                            malert("医保出院成功！","bottom","success");
                            var head = {
                                operCode: "S14",
                                billCode: left_tab1.billCode,
                                rsa: ""
                            };
                            var redeemNo = "";//重大疾病治疗方案2102，其它2101
                            if (yjs.json.zdjb == "1"){
                                redeemNo = "2102";
                            }else{
                                redeemNo = "2101"
                            }
                            var body = {
                                inpId: yjs.json.inpid,
                                redeemNo:redeemNo,//2102
                                outDate:yjs.fDate(yjs.json.bqcyrq, 'date'),
                                areaCode:yjs.json.xzqh,//"520421",  //行政区划
                                isTransProvincial:yjs.json.zdjb
                            };

                            var footer = {
                                cyczy: userId
                            }
                            var param = {
                                head: head,
                                body: body,
                                footer: footer
                            };
                            var str_param = JSON.stringify(param);
                            $.getJSON(
                                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                                    console.log(json);
                                    if (json.a == 0) {
                                        malert("农合结算成功！","bottom","success");
                                        var parm = {
                                            inpid: yjs.json.inpid
                                        };
                                        $.getJSON(
                                            "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=yjs&method=queryDzlx&parm=" + JSON.stringify(parm), function (json) {
                                                if (json.a == 0) {

                                                    var res = eval('(' + json.d + ')');
                                                    yjs.yjsContent = res.list[0];

                                                    yjs.jtdz = res.list[0].address;
                                                    yjs.rylx = yjs.nhrysx_tran[res.list[0].memberpro];
                                                    yjs.zyts = parseInt(Math.abs(yjs.json.bqcyrq - yjs.json.ryrq) / 1000 / 60 / 60 / 24);
                                                    if(yjs.zyts==0){
                                                        yjs.zyts=1;
                                                    }

                                                    var xs='0.9';
			                                        if(yjs.yjsContent.undulatingline==0){
			                                            xs='0.95';
			                                        }
			                                        if( yjs.printContentt.zfje=='0.00'){
			                                            xs='1.00';
			                                        }
			                                        var bcdxx={
			                                            inpid:yjs.json.inpid,
			                                            zyh:yjs.json.zyh,
			                                            rylx: yjs.rylx,
			                                            brxm: yjs.json.brxm,
			                                            cylx: yjs.gznhcy_tran[yjs.json.cylx],
			                                            brxb: yjs.brxb_tran[yjs.json.brxb],
			                                            nl:yjs.json.nl,
			                                            ybkh:yjs.json.ybkh,
			                                            bcrq: yjs.fDate(new Date(), 'date'),
			                                            jtdz:yjs.jtdz,
			                                            lxdh:yjs.json.lxdh,
			                                            ryrq:yjs.fDate(yjs.json.ryrq, 'date'),
			                                            bqcyrq: yjs.fDate(yjs.json.bqcyrq, 'date'),
			                                            ryzdmc: yjs.json.ryzdmc,
			                                            zyts: yjs.zyts,
			                                            fyje: yjs.ynfyContent.fyje,
			                                            zffy: yjs.fDec(yjs.fDec(yjs.ynfyContent.fyje,2)-yjs.fDec(yjs.yjsContent.insurancecost, 2)),
			                                            ndzyts: yjs.json.ndzyts,
			                                            scfyje: yjs.ynfyContent.scfyje,
			                                            undulatingline: yjs.yjsContent.undulatingline,
			                                            bottomredeem: yjs.yjsContent.bottomredeem,
			                                            insurecost: yjs.yjsContent.insurecost,
			                                            medicinecost: yjs.yjsContent.medicinecost,
			                                            salvafpcost: yjs.yjsContent.salvafpcost,
			                                            salvayfcost: yjs.yjsContent.salvayfcost,
			                                            civilcost: yjs.yjsContent.civilcost,
			                                            salvajscost: yjs.yjsContent.salvajscost,
			                                            compensatecost: yjs.yjsContent.compensatecost,
			                                            insurancecost:yjs.yjsContent.insurancecost,
			                                            sjbc:0,
			                                            zfje:0,
			                                            //jsgs:"("+yjs.yjsContent.insurancecost+"-"+yjs.yjsContent.undulatingline+")*"+xs,
                                                        jsgs:yjs.jsgs,
			                                        };
			                                        bcdxx.sjbc = yjs.fDec(yjs.yjsContent.compensatecost+yjs.yjsContent.insurecost+yjs.yjsContent.civilcost+yjs.yjsContent.salvajscost,2);
			                                        bcdxx.zfje = yjs.fDec(yjs.ynfyContent.fyje - bcdxx.sjbc,2);
			                                        $.getJSON(
			                                            "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=yjs&method=saveZybcd&parm=" + JSON.stringify(bcdxx), function (json) {
			                                                if (json.a == 0) {
			                                                    malert("补偿保存成功","bottom","success");
			                                                } else {
			                                                    malert(json.c,"bottom","defeadted");
			                                                }
			                                            });

                                                } else {
                                                    malert(json.c,"bottom","defeadted");
                                                }
                                            });
                                    } else {
                                        malert(json.c,"bottom","defeadted");
                                    }
                                });
                        } else {
                            malert(json.c,"bottom","defeadted");
                        }
                        left_tab1.getBrData();
                    });
            },
            saveZybcd:function(){

            },
            cybcy: function () {
                if (yjs.json.inpid == null || yjs.json.inpid == undefined) {
                    malert("该病人无补偿证号，未农合入院","bottom","defeadted");
                    return
                }
                var head = {
                    operCode: "S15",
                    billCode: left_tab1.billCode,
                    rsa: ""
                };
                var body = {
                    inpId: yjs.json.inpid,
                    areaCode:yjs.json.xzqh,//"520421",  //行政区划
                    isTransProvincial:yjs.json.zdjb
                }

                var footer = {
                    czy: userId,
                    zyh:yjs.json.zyh
                }
                var param = {
                    head: head,
                    body: body,
                    footer: footer
                }
                var str_param = JSON.stringify(param);
                if (!confirm("你确定要将病人" + hyjl.brxxList.brxm + "取消出院吗？")) {
                    return false;
                }
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                        console.log(json);
                        if (json.a == 0) {
                            malert("住院冲红成功！","bottom","success");
                            hyjl.brxxList.memberid = '';
                            yjs.jsonList = [];
                            yjs.yjsContent = {};
                            yjs.$forceUpdate();
                            left_tab1.getBrData();
                        } else {
                            malert(json.c,"bottom","defeadted");
                        }
                    });
            },
            yjs: function () {
                if (yjs.json.inpid == null || yjs.json.inpid == '' || yjs.json.inpid == undefined) {
                    malert("改病人未办理农合入院！请正确选择病人","bottom","defeadted");
                    return
                }
                var head = {
                    operCode: "S13",
                    billCode: left_tab1.billCode,
                    rsa: ""
                };
                var redeemNo = "";//重大疾病治疗方案2102，其它2101
                if (yjs.json.zdjb == "1"){
                    redeemNo = "2102";
                }else{
                    redeemNo = "2101"
                }
                var body = {
                    inpId: yjs.json.inpid,
                    redeemNo:redeemNo,//2102
                    outDate:yjs.fDate(yjs.json.bqcyrq, 'AllDate'),
                    areaCode:yjs.json.xzqh,//"520421",
                    isTransProvincial:"0"
                };
                var param = {
                    head: head,
                    body: body
                };
                var str_param = JSON.stringify(param);
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                        console.log(json);
                        if (json.a == 0) {
                            malert("预结算成功!","bottom","success");
                            var res = eval('(' + json.d + ')')
                            yjs.yjsContent = res.list[0];
                            console.log(yjs.yjsContent);
                            yjs.yjsContent.tbje = yjs.ynfyContent.yjje - (yjs.ynfyContent.fyje - yjs.yjsContent.compensatecost);
                            yjs.jsonList = [],
                            yjs.brxxContent.memberid = hyjl.brxxList.memberid;
                            yjs.brxxContent.brxm = hyjl.brxxList.brxm;
                            yjs.brxxContent.scfyje = yjs.ynfyContent.scfyje;
                            yjs.brxxContent.zffy = yjs.ynfyContent.fyje - yjs.yjsContent.compensatecost;
                            yjs.brxxContent.insurancecost = yjs.yjsContent.insurancecost;
                            yjs.brxxContent.compensatecost = yjs.yjsContent.compensatecost;
                            yjs.brxxContent.undulatingline = yjs.yjsContent.undulatingline;
                            yjs.jsonList.push(yjs.brxxContent);
                            yjs.jsgs = yjs.yjsContent.calculationMethod;
                            console.log(yjs.yjsContent);

                            //保存补偿单信息：

                        } else {
                            malert(json.c,"bottom","defeadted");
                        }
                    });
            },
        }
    })
    yjs.getData();
laydate.render({
    elem: '#cyrq',
    rigger: 'click',
    theme: '#1ab394',
    type: 'datetime',
    done: function (value, data) {
        yjs.json.cyrq = value;
    }
});
//})();
