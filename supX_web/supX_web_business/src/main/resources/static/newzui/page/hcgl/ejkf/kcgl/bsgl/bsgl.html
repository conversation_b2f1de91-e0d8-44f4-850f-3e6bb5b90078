<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>报损管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="bsgl.css" rel="stylesheet"/>
    <link href="rydj.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="printArea printShow"></div>
<div class="background-box">
<div class="wrapper printHide" id="jyxm_icon">
    <div class="panel " v-cloak>
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-show="isShowkd">开单</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5 fyxm-hide" @click="kd(1)" :class="{'btn-show':isShowpopL}">添加材料</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="searchHc" v-show="isShowkd==true || isShowpopL==true">刷新</button>
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" v-show="isShowkd==false && isShowpopL==false" @click="searchHc">刷新</button>
        </div>
        <div class="tong-search" :class="{'tong-padded':isShow}">
            <div class="zui-form" v-show="isShowkd">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">二级库房</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultChange"
                                      :not_empty="false" :child="yfList"
                                      :index="'yfmc'" :index_val="'yfbm'"
                                      :val="yfbm" :search="true" :name="'yfbm'"
                                      id="yfbm" :index_mc="'yfbm'">
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline flex-container flex-align-c  margin-f-l5">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请开始日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label  ">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="searchHc()" type="text" v-model="search"/>
                    </div>
                </div>
            </div>
            <div class="jbxx fyxm-hide" :class="{'btn-show':isShow}">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20 ">
                            <div class="zui-inline">
                                <label class="zui-form-label ">二级库房</label>
                                <div class="zui-input-inline wh122 margin-f-l25">
                                    <select-input @change-data="resultChange"
                                                  :not_empty="false" :child="yfList"
                                                  :index="'yfmc'" :index_val="'yfbm'"
                                                  :val="yfbm" :search="true" :name="'yfbm'"
                                                  id="yfbm" :index_mc="'yfbm'" :disable="jyinput">
                                    </select-input>
                                </div>
                            </div>
                            <div class="zui-inline" style="width:50%;">
                                <label class="zui-form-label margin-f-l10">备注</label>
                                <div class="zui-input-inline margin-f-l35">
                                    <input class="zui-input" placeholder="请输入备注" type="text" id="bzms" v-model="bzms" :disabled="jyinput"/>
                                </div>
                            </div>

                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期：<i v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view  padd-r-10 padd-l-10"   z-height="full">
        <!--入库列表-->
        <div class="zui-table-header" v-show="isShowkd">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>报损单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " v-show="isShowkd" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in bsdList"  :class="[{'table-hovers':$index===activeIndex}]" :tabindex="$index"
                	@dblclick="showDetail($index,item)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.ckdh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zdrxm"></div>
                    </td>
                    <td>
                        <!-- v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.sfzfbz=='1' ? 'color-ysh' : item.sfzfbz=='2' ? 'color-yzf' :'' "-->
                        <div class="zui-table-cell cell-s">
                            <i  v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : '' "></i>

                        </div>
                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">
                             <span  class="flex-center padd-t-5">
                                <em class="width30"  v-if="item.shzfbz== 0">
                                    <i class="icon-sh" @click="showDetail($index,item)" data-title="审核"></i></em>
                                <em class="width30" v-if="item.shzfbz== 0">
                                	<i class="icon-js" @click="invalidData($index)" data-title="作废" ></i>
                                </em>
                                <em class="width30"  v-if="item.shzfbz != '1' && item.shzfbz !='2'">
                                	<i class="icon-bj" @click="editIndex($index)" data-title="编辑"></i>
                                </em>
                               </span>
                        </div>
                    </td>
                    <p v-show="bsdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <!--添加材料-->
        <div class="zui-table-header fyxm-hide" v-show="isShow" :class="{'fyxm-show':isShow}" style="height: 38px !important;;overflow: hidden;">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left text-over-2"><span>供货单位</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left text-over-2"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>报损数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body   fyxm-hide" @scroll="scrollTable($event)" v-show="isShow" :class="{'fyxm-show':isShow}">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in jsonList" :tabindex="$index"  :class="[{'table-hovers':$index===activeIndex}]" @dblclick="edit($index)" >
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ghdwmc"></div>
                        </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <div   v-text="item.ypgg"></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <i  v-text="item.cksl"></i>
                            <i v-text="item.yfdwmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypjj"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yplj"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypph"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypcd"></div>
                    </td>

                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                    </td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-show="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l"  v-show="isShow">
            <div class="zui-table-header ">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"  :class="[{'table-hovers':$index===activeIndex}]" :tabindex="$index" class="tableTr2">
                        <td><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--end-->
        <div class="zui-table-fixed table-fixed-r"  v-show="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage" v-show="isShowkd" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="rkgl-position" v-show="isShow">
           <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="printDJ()" v-show="dyShow">打印</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-show="zfShow">作废</button>
                <!-- <button class="tong-btn btn-parmary-f2a xmzb-db" @click="jujue" v-show="ShShow">拒绝</button> -->
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow">审核</button>
           </span>
        </div>
    </div>
</div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-548"  style="padding-top: 0;"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--编辑材料-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>经办人</i>
                    <!--<select :disabled="added" v-model="bsdContent.jbr" style="width: 157px;height: 26px;">-->
                        <!--<option v-for="item in ryList" :value="item.rybm" v-text="item.ryxm"></option>-->
                    <!--</select>-->
                    <select-input @change-data="resultChange($event),result($event)"
                                  :not_empty="false" :child="ryList"
                                  :index="'ryxm'" :index_val="'rybm'"
                                  :val="bsdContent.jbr" :search="true" :name="'bsdContent.rybm'"
                                  :index_mc="'rybm'"  ref="autofocus" >
                    </select-input>

                    <!-- <select-input @change-data="resultChange"
                                      :not_empty="false" :child="yfList"
                                      :index="'yfmc'" :index_val="'yfbm'"
                                      :val="yfbm" :search="true" :name="'yfbm'"
                                      id="yfbm" :index_mc="'yfbm'">
                   </select-input> -->
            </li>
            <li>
                    <i>制单日期</i>
                    <input class="zui-input" disabled="disabled" v-model="zdrq">
            </li>
            <li>
                    <i>材料名称</i>
                    <input id="ypmc"  class="zui-input" v-model="popContent.ypmc" @keyup="changeDown($event,'ypmc','searchCon')"
                           @input="change($event,'ypmc',$event.target.value)">
                    <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
            </li>
            <li>
                <i>供货单位</i>
                <input  class="zui-input" v-model="popContent.ghdwmc" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                    <i>出库数量</i>
                    <input @mousewheel.prevent id="cksl" type="number" class="zui-input" v-model="popContent.cksl" @keyup="changeDown($event,'cksl','')" @keydown="nextFocus($event)">
                       <em style="position:absolute;top:10px;right:10px;z-index:1111" v-text="popContent.yfdwmc"></em>
                </li>
            <li>
                    <i>材料规格</i>
                    <input  class="zui-input" v-model="popContent.ypgg" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                    <i>库存数量</i>
                    <input @mousewheel.prevent type="number" class="zui-input" disabled="disabled" v-model="popContent.kcsl"   />
                    <em style="position:absolute;top:10px;right:10px;z-index:1111" v-text="popContent.yfdwmc"></em>
                </li>
            <li>
                    <i>材料进价</i>
                    <div class="zui-input background-h"  v-text="fDec(popContent.ypjj,2 ) || '0' " ></div>
            </li>
            <li>
                    <i>材料零价</i>
                    <input @mousewheel.prevent type="number" class="zui-input" disabled="disabled" v-model="fDec(popContent.yplj,2 || '0')">
            </li>
            <li>
                    <i>材料批号</i>
                    <input @mousewheel.prevent type="number" class="zui-input" disabled="disabled" v-model="popContent.scph">
            </li>
            <li>
                    <i>有效期至</i>
                    <input type="text" class="zui-input" id="yxqz" disabled="disabled" v-model="fDate(popContent.yxqz,'date')">
            </li>
            <li>
                    <i>产地</i>
                    <input type="text" class="zui-input"  v-model="popContent.cdmc" @keydown="nextFocus($event)" disabled>
            </li>

            <li>
                    <i>分装比例</i>
                    <input type="text" class="zui-input" disabled="disabled"  v-model="popContent.fzbl" @keydown.enter="nextF">
            </li>
            <li>
                    <i>产品标准&ensp;&ensp;号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.cpbzh">
            </li>
            <li>
                    <i>批准文号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.pzwh">
            </li>
            <li>
                    <i>生产日期</i>
                    <input type="text" class="zui-input " id="scrq" disabled="disabled" v-model="fDate(popContent.scrq,'date')">
            </li>
            <li>
                    <i>备注</i>
                    <input type="text" class="zui-input " v-model="bsdContent.bzms" @keyup="addData()">
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">添加</button>
    </div>
</div>

<script src="bsgl.js"></script>
</body>

</html>
