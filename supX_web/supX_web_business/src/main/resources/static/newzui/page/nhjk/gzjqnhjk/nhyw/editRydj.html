<link rel="stylesheet" href="editRydj.css">
<!--入院登记添加记录视图begin-->
<div id="rydj_info" style="height: 100%;padding-top: 52px;padding-bottom: 66px;">
    <!--入院登记信息录入 功能按钮begin-->
    <div class="panel" style="margin-top: -52px;">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="refresh()">刷新</button>
            <!--<button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="">清空</button>-->
            <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click="loadIdCard()"><i class="icon-width icon-dsfz padding-right5"></i>读身份证</button>
            <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click="loadYkt()"><i class="icon-width icon-dylk padding-right5"></i>读医疗卡</button>
            <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click=""><i class="icon-width icon-dybk padding-right5"></i>读医保卡</button>
            <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click=""><i class="icon-width icon-dybk padding-right5"></i>读磁条卡</button>
        </div>
    </div>
    <!--入院登记信息录入 功能按钮end-->

    <!--入院登记信息录入 详细begin-->
    <div id="scrollbar" class="rydj-info-box no-scrollbar" style="height: 100%;margin-bottom:0px;overflow-y: auto;">
    
    	<div class="tab-card" id="search">
            <div class="tab-card-header">
                <div class="tab-card-header-title">检索信息</div>
            </div>
            <div class="tab-card-body">
                <div class="zui-form grid-box">
                    <div class="zui-inline col-xxl-3 padd-l-40">
                        <label class="zui-form-label " style="width: 40px;padding-right: 7px">检索</label>
                        <input class="zui-input" data-notEmpty="false" :value="brxxContent.text" @keydown="changeDown($event,'text','brxxContent','searchCon')"
                  								 @input="change(false,'text',$event.target.value)">
      			            <search-table :message="searchCon" :selected="selSearch"
      			                          :them="them" :them_tran="them_tran" :page="page"
      			                          @click-one="checkedOneOut" @click-two="selectOne">
      			            </search-table>
                    </div>
                </div>
            </div>
        </div>
    
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">基本信息</div>
            </div>
            <div class="tab-card-body">
                <div class="zui-form grid-box">
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">住院号</label>
                        <input id="zyh"  class="zui-input"  v-model="popContent.brjbxxModel.zyh" readonly="readonly">
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">病人姓名</label>
                        <div class="zui-input-inline">
                            <input id="brxm"  class="zui-input"  v-model="popContent.brjbxxModel.brxm" readonly="readonly">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">入院科室</label>
                        <div class="zui-input-inline" id="ryks-box">
                            <input id="ryksmc"  class="zui-input"  v-model="popContent.brjbxxModel.ryksmc" readonly="readonly">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">住院医生</label>
                        <input id="zyysxm"  class="zui-input"  v-model="popContent.brjbxxModel.zyysxm" readonly="readonly">
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">入院日期</label>
                        <div class="zui-input-inline zui-date">
                            <i class="datenox fa-calendar"></i>
                            <input id="ryrq"  class="zui-input"  v-model="popContent.brjbxxModel.ryrq" readonly="readonly">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">参保年度</label>
                        <div class="zui-input-inline zui-date">
                            <i class="datenox fa-calendar"></i>
                            <input class="zui-input zui-dateList" v-model="popContent.brjbxxModel.cbnd" id="timeCbnd"
                                   data-notEmpty="true" @keydown="nextFocus($event,'',true)" readonly="readonly">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">手机号码</label>
                        <div class="zui-input-inline">
                            <input id="sjhm"  class="zui-input"  v-model="popContent.brjbxxModel.sjhm" readonly="readonly">
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">病人信息</div>
            </div>
            <div class="tab-card-body">
                <div class="zui-form grid-box" style="height: 210px;">
                    <div class="zui-table-view" id="brRyList" style="overflow: auto;">

                        <!--<div class="zui-inline col-xxl-3">-->
                            <!--<label class="zui-form-label">检索病人</label>-->
                            <!--<div class="zui-input-inline">-->
                                <!--<input placeholder="医疗证(卡)号/身份证/监护人身份证/银行卡号" data-notEmpty="false" class="zui-input"-->
                                       <!--:title="chrxx.parm"-->
                                       <!--v-model="chrxx.parm"-->
                                       <!--id="parm"-->
                                       <!--@keydown="changeDown($event,'chr','brxxContent','searchCon')">-->
                            <!--</div>-->
                        <!--</div>-->
                        <div class="zui-inline col-xxl-3" style="width: 50%;padding-left: 0px">
                            <div class="col-xxl-4"style="width: 17%">
                                <select-input
                                        @change-data="commonResultChange"
                                        :not_empty="false"
                                        :child="bookNoType_tran"
                                        :index="popContent.brjbxxModel.bookNoType"
                                        :val="popContent.brjbxxModel.bookNoType"
                                        :name="'popContent.brjbxxModel.bookNoType'" id="bookNoType">
                                </select-input>
                            </div>
                            <div class="zui-input-inline" style="width: 33%">
                                <div class="col-xxl-8" style="width: 100%">
                                    <input id="bookNo" placeholder="输入后回车搜索" data-notEmpty="true" class="zui-input"
                                           :title="popContent.brjbxxModel.bookNo"
                                           v-model="popContent.brjbxxModel.bookNo"
                                           @keydown.13="getPersonInfo">
                                </div>
                            </div>
                            <div class="zui-input-inline" style="width: 45%">
                                <div class="col-xxl-8" style="padding-right: 5px;">
                                    <button class="tong-btn btn-parmary paddr-r5" @click="getPersonInfo">查询</button>
                                </div>
                            </div>
                        </div>

                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>成员编码</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>辖区编码</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl"><span>家庭编码</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>性别</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl"><span>身份证</span></div>
                                    </th>

                                    <th>
                                        <div class="zui-table-cell cell-s"><span>年龄</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>出生日期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl"><span>医疗证（卡）号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl"><span>监护人身份证号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>监护人</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xxl"><span>家庭住址</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>电话号码</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xxl"><span>个人身份属性名称</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event)">
                            <table v-if="jsonList.length" class="zui-table table-width50">
                                <tbody>
                                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                                    :tabindex="$index"
                                    @click="checkSelect([$index,'one','jsonList'],$event)"
                                    class="tableTr2"
                                    @dblclick="edit($index)">
                                    <td><div class="zui-table-cell cell-s" v-text="$index+1"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.memberNO"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.name"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.countryTeamCode"></div></td>
                                    <td><div class="zui-table-cell cell-xl" v-text="item.familySysno"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="brxb_tran[item.sexId]"></div></td>
                                    <td><div class="zui-table-cell cell-xl" v-text="item.idcardNo"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.age"></div></td>
                                    <td><div class="zui-table-cell cell-l" v-text="item.birthday"></div></td>
                                    <td><div class="zui-table-cell cell-xl" v-text="item.bookNo"></div></td>
                                    <td><div class="zui-table-cell cell-xl" v-text="item.guardianCardno"></div></td>
                                    <td><div class="zui-table-cell cell-l" v-text="item.guardianName"></div></td>
                                    <td><div class="zui-table-cell cell-xxl" v-text="item.familyAddress"></div></td>
                                    <td><div class="zui-table-cell cell-l" v-text="item.tel"></div></td>
                                    <td><div class="zui-table-cell cell-xxl" v-text="item.ideName"></div></td>
                                </tr>
                                </tbody>
                            </table>
                            <p v-if="!jsonList.length" class="flex noData  text-center zan-border">暂无数据...</p>
                        </div>
                    </div>
                    <div style="height: 100px;"></div>
                </div>
            </div>
        </div>

        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">登记信息</div>
            </div>
            <div class="tab-card-body">
                <div class="grid-box zui-form">
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">个人编号</label>
                        <input id="memberSysno"  class="zui-input" data-notEmpty="true" v-model="popContent.brjbxxModel.memberSysno">
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">家庭编号</label>
                        <input id="familySysno"  class="zui-input" data-notEmpty="true" v-model="popContent.brjbxxModel.familySysno" >
                    </div>
                    <div class="zui-inline col-xxl-3" id="jbbmInput">
                        <label class="zui-form-label">入院诊断</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" data-notEmpty="true" v-model="popContent.brjbxxModel.icdName" @keydown="changeDown($event,'jbbm','jbbmContent','jbsearchCon')"
                                   @input="change(false,'jbbm',$event.target.value)">
                            <jbsearch-table :message="jbsearchCon" :selected="selSearch"
                                            :them="jbthem" :page="page"
                                            @click-one="checkedOneOut" @click-two="selectJbbm">
                            </jbsearch-table>
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3" id="code">
                        <label class="zui-form-label">治疗方式</label>
                        <input   class="zui-input"  v-model="popContent.brjbxxModel.treatName" @keydown="changeDown($event,'zlfs','zlfsContent','zlfsSearchCon')" @input="change(false,'zlfs',$event.target.value)">
                        <zlfssearch-table :message="zlfsSearchCon" :selected="selSearch"
                                          :them="zlfsthem" :page="page"
                                          @click-one="checkedOneOut" @click-two="selectZlfs">
                        </zlfssearch-table>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">第二诊断</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="popContent.brjbxxModel.secondIcdName" @keydown="changeDown($event,'jbbm2','jbbmContent2','jbsearchCon2')"
                                   @input="change(false,'jbbm',$event.target.value,2)">
                            <jbsearch-table2 :message="jbsearchCon2" :selected="selSearch"
                                            :them="jbthem" :page="page"
                                            @click-one="checkedOneOut" @click-two="selectJbbm2">
                            </jbsearch-table2>
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">治疗方式2</label>
                        <input  id="secondTreatCode" class="zui-input"  v-model="popContent.brjbxxModel.secondTreatName" @keydown="changeDown($event,'zlfs2','zlfsContent2','zlfsSearchCon2')" @input="change(false,'zlfs',$event.target.value,2)">
                        <zlfssearch-table2 :message="zlfsSearchCon2" :selected="selSearch"
                                          :them="zlfsthem" :page="page"
                                          @click-one="checkedOneOut" @click-two="selectZlfs2">
                        </zlfssearch-table2>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">第三诊断</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="popContent.brjbxxModel.threeIcdName" @keydown="changeDown($event,'jbbm3','jbbmContent3','jbsearchCon3')"
                                   @input="change(false,'jbbm',$event.target.value,3)">
                            <jbsearch-table3 :message="jbsearchCon3" :selected="selSearch"
                                            :them="jbthem" :page="page"
                                            @click-one="checkedOneOut" @click-two="selectJbbm3">
                            </jbsearch-table3>
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">治疗方式3</label>
                        <input  id="threeTreatCode" class="zui-input"  v-model="popContent.brjbxxModel.threeTreatName" @keydown="changeDown($event,'zlfs3','zlfsContent3','zlfsSearchCon3')" @input="change(false,'zlfs',$event.target.value,3)">
                        <zlfssearch-table3 :message="zlfsSearchCon3" :selected="selSearch"
                                          :them="zlfsthem" :page="page"
                                          @click-one="checkedOneOut" @click-two="selectZlfs3">
                        </zlfssearch-table3>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">就诊类型</label>
                        <select-input @change-data="commonResultChange" :not_empty="true"
                                      :child="jzlx_tran" :index="popContent.brjbxxModel.cureId" :val="popContent.brjbxxModel.cureId"
                                      :name="'cureId'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">入院状态</label>
                        <select-input @change-data="commonResultChange" :not_empty="true"
                                      :child="ryqk_tran" :index="popContent.brjbxxModel.inHosId" :val="popContent.brjbxxModel.inHosId"
                                      readonly="readonly"
                                      :name="'popContent.brjbxxModel.inHosId'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">重大疾病1</label>
                        <input id="majorDiseaseICD"  class="zui-input"  v-model="popContent.brjbxxModel.majorDiseaseICD">
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">重大疾病2</label>
                        <input id="secondMajorDiseaseICD"  class="zui-input"  v-model="popContent.brjbxxModel.secondMajorDiseaseICD">
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">重大疾病3</label>
                        <input id="threeMajorDiseaseICD"  class="zui-input"  v-model="popContent.brjbxxModel.threeMajorDiseaseICD">
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">农合中心</label>
                        <select-input @change-data="commonResultChange"
                                      :child="centerNoList" :index="'centerName'" :index_val="'centerNo'" :val="popContent.brjbxxModel.centerNo"
                                      :name="'popContent.brjbxxModel.centerNo'" :index_mc="'centerName'" search="true" id="centerNo">
                        </select-input>

                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">转诊类型</label>
                        <input id="turnMode"  class="zui-input"  v-model="popContent.brjbxxModel.turnMode">
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">转诊<br/>转院编号</label>
                        <input id="turnCode"  class="zui-input"  v-model="popContent.brjbxxModel.turnCode">
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">转院日期</label>
                        <div class="zui-input-inline zui-date">
                            <i class="datenox fa-calendar"></i>
                            <input class="zui-input zui-dateList" v-model="popContent.brjbxxModel.turnDate" id="turnDate"
                                   @keydown="nextFocus($event,'',true)" readonly="readonly">
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!--入院登记信息录入 详细end-->

    <!--入院登记信息录入 浮动功能按钮区begin-->
    <div class="action-bar fixed zui-table-tool" style="bottom: 10px;">
        <button class="zui-btn btn-primary xmzb-db" v-if="popContent.brjbxxModel.inpatientSn == null || popContent.brjbxxModel.inpatientSn == ''" @click="submitData">提交</button>
        <button class="zui-btn btn-primary xmzb-db" v-if="popContent.brjbxxModel.inpatientSn != null  && popContent.brjbxxModel.inpatientSn != ''" @click="submitData">修改</button>
        <button class="zui-btn btn-parmary-d2 xmzb-db" @click="cancelRy" v-if="popContent.brjbxxModel.inpatientSn != null && popContent.brjbxxModel.inpatientSn != ''">取消入院</button>
        <button class="zui-btn btn-default xmzb-db" @click="quxiao">取消</button>
    </div>
    <!--入院登记信息录入 浮动功能按钮区begin-->
</div>
<!--入院登记添加记录视图end-->
<script type="text/javascript" src="editRydj.js"></script>