<div id="jyglxx" class="contextInfo">
    <div class="panel tong-top flex-align-c flex-container">
        <button class="tong-btn btn-parmary" @click="getData"><span class="fa fa-refresh padd-r-5"></span>刷新</button>
        <div class="flex-container flex-align-c">
            <span class="ft-14 padd-r-5 ">借阅日期</span>
            <div class=" flex-container flex-align-c">
                <input class="zui-input todate wh200 text-indent20" placeholder="请选择借阅日期" id="timeVal"/><span
                    class="padd-l-5 padd-r-5">~</span>
                <input class="zui-input todate wh200 " placeholder="请选择借阅日期" id="timeVal1"/>
            </div>
        </div>
        <div class="flex-container flex-align-c padd-l-10">
            <span class="ft-14 padd-r-5">检索</span>
                <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
        </div>
    </div>
    <div class="zui-table-view ybglTable" id="utable1" >
        <div class="zui-table-header">
            <table class="font-14 zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>借阅人</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>申请日期</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>病员姓名</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>性别</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>借阅单位</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>状态</span></div>
                    </th>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jyr"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s " v-text="fDate(item.sqrq,'date')">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s " v-text="item.brxm">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s " v-text="brxb_tran[item.brxb]">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s " v-text="item.jydw">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s ">
                            <i v-if="item.ckbz==0 && item.ghbz==0">待审核</i>
                            <i v-if="item.ckbz==1 && item.ghbz==1">已归还</i>
                            <i v-if="item.ckbz==0 && item.ghbz==1">已归还</i>
                            <i v-if="item.ckbz==1 && item.ghbz==0">待归还</i>
                        </div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s ">
                            <!--待归还-->
                            <!--<i class="icon-width icon-dgh" data-title="待归还" @click="dgh"></i>-->
                            <!--待出库-->
                            <span class="flex-center padd-t-5">
                                    <em class="width30">
                                        <i v-if="item.ckbz==1 && item.ghbz==0" class="icon-width icon-dgh" data-title="待归还" @click="dgh($index)"></i>
                                        <i v-if="item.ckbz==0 && item.ghbz==0" class="icon-width icon-dck" data-title="待出库" @click="dck($index)"></i>
                                    </em>
                            </span>
                            <!--待审核,拒绝-->
                            <!--<i class="icon-sh" data-title="待审核" @click="dck"></i>-->
                            <!--<i class="icon-js" data-title="拒绝"></i>-->
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
          :next-more="nextMore"></page>


</div>
<div class="side-form  pop-548" :class="{'ng-hide':nums==1}" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <!--<div class="jygl-top">-->
        <!--<div class="jygl-div">借阅类型</div>-->
        <!--<input type="button" class="jygl-btn jygl-btn-active" value="电子档案"/>-->
        <!--<input type="button" class="jygl-btn" value="纸质档案 "/>-->
        <!--</div>-->
        <!--
住院号 病人id 申请日期 借阅人 借阅单位 借阅事由 借阅人登记 借阅登记日期 出库标志 出库日期 出库人员 审核人员 审批日期 审批意见 归还标志 归还日期 归还人员 归还接收人员 医疗机构编码
no     no     no      no    no      no       no        no         no(0,1) yes    yes     yes      yes     yes    no(0,1)  yes     yes      yes        no
-->
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                <i>姓名</i>
                <input type="text" class="label-input background-h" @keydown="nextFocus($event)" disabled="disabled"
                       v-model="popContent.brxm"/>
            </li>
            <li>
                <i>病人ID</i>
                <input type="text" class="label-input background-h" @keydown="nextFocus($event)" disabled="disabled"
                       v-model="popContent.brid"/>
            </li>
            <li>
                <i>住院号</i>
                <input type="text" class="label-input background-h" @keydown="nextFocus($event)" disabled="disabled"
                       v-model="popContent.zyh"/>
            </li>
            <li>
                <i>病案号</i>
                <input type="text" class="label-input background-h" @keydown="nextFocus($event)" disabled="disabled"
                       v-model="popContent.bah"/>
            </li>
            <li>
                <i>出库标志</i>
                <select-input class="ckbz-input" @change-data="resultChange" :data-notEmpty="false"
                              :child="ckbz" :index="popContent.ckbz" :val="popContent.ckbz"
                              :name="'popContent.ckbz'">
                </select-input>
            </li>
            <li>
                <i>归还标志</i>
                <select-input class="ckbz-input" @change-data="resultChange" :data-notEmpty="false"
                              :child="ghbz" :index="popContent.ghbz" :val="popContent.ghbz"
                              :name="'popContent.ghbz'">
                </select-input>
            </li>
            <li>
                <i>借&emsp;&emsp;阅<br/>登&ensp;记&ensp;人</i>
                <select-input class="ckbz-input" @change-data="resultChange" :not_empty="false"
                              :child="allryList" :index="'ryxm'" :index_val="'rybm'" :val="popContent.jydjr"
                              :name="'popContent.jydjr'" :index_mc="'jydjrxm'" :search="true" disable="true">
                </select-input>
            </li>
            <li>
                <i>申请日期</i>
                <em class="icon-position icon-rl" style="left: 76px;"></em>
                <input type="text" class="label-input times1" @keydown="nextFocus($event)" name="text" disabled="disabled"
                       v-model="fDate(popContent.sqrq,'date')" id="sqrq"/>
            </li>
            <li>
                <i>登记日期</i>
                <em class="icon-position icon-rl" style="left: 76px;"></em>
                <input type="text" class="label-input times2" @keydown="nextFocus($event)" name="text" disabled="disabled"
                       v-model="fDate(popContent.jydjrq, 'date')" id="jydjrq"/>
            </li>

            <li>
                <i>出库日期</i>
                <em class="icon-position icon-rl" style="left: 76px;"></em>
                <input type="text" class="label-input times3" @keydown="nextFocus($event)" name="text" disabled="disabled"
                       v-model="popContent.ckrq" id="ckrq"/>
            </li>

            <li>
                <i>归还日期</i>
                <em class="icon-position icon-rl" style="left: 76px;"></em>
                <input type="text" class="label-input times4" @keydown="nextFocus($event)" name="text"
                       v-model="fDate(popContent.ghrq, 'date')" id="ghrq"/>
            </li>

            <li>
                <i>借阅人</i>
                <input type="text" class="label-input" placeholder="请输入借阅人" @keydown="nextFocus($event)" name="text"
                       disabled="disabled" v-model="popContent.jyr"/>
            </li>
            <li>
                <i>借阅单位</i>
                <input type="text" class="label-input" placeholder="请输入借阅单位" @keydown="nextFocus($event)" name="text"
                       disabled="disabled" v-model="popContent.jydw"/>
            </li>
            <li class="width100 jygl-height">
                <i class="width150">借阅事由</i>
                <textarea class="label-input jygl-height" placeholder="请输入借阅事由" @keydown="nextFocus($event)" name="text"
                          v-model="popContent.jysy"></textarea>
            </li>
            <li v-show="jjShow">
                <i>审核人员</i>
                <select-input class="ckbz-input" @change-data="resultChange" :not_empty="false"
                              :child="allryList" :index="'ryxm'" :index_val="'rybm'" :val="popContent.spry"
                              :name="'popContent.spry'" :index_mc="'spryxm'" :search="true">
                </select-input>
            </li>
            <li v-show="jjShow">
                <i>审批日期</i>
                <em class="icon-position icon-rl" style="left: 76px;"></em>
                <input type="text" class="label-input times5" placeholder="请选择审核时间" @keydown="nextFocus($event)"
                       name="text" v-model="fDate(popContent.sprq, 'date')"/>
            </li>
            <li class="width100 jygl-height" v-show="jjShow">
                <i class="width150">审批意见</i>
                <textarea class="label-input jygl-height" placeholder="请输入审核意见 " @keydown="nextFocus($event)"
                          name="text" v-model="popContent.spyj"></textarea>
            </li>
            <li v-show="ghShow">

                <i>归&emsp;&emsp;还<br/>接收人员</i>
                <select-input class="ckbz-input" @change-data="resultChange" :not_empty="false"
                              :child="allryList" :index="'ryxm'" :index_val="'rybm'" :val="popContent.ghhsry"
                              :name="'popContent.ghhsry'" :index_mc="'ghhsryxm'" :search="true">
                </select-input>
            </li>
            <!--<li v-show="ghShow">-->
            <!--<label>-->
            <!--<i>归还时间</i>-->
            <!--<em class="icon-position icon-rl" style="left: 76px;"></em>-->
            <!--<input type="text" class="label-input times4" placeholder="请选择归还时间" @keydown="nextFocus($event)" v-model="ghrq"/>-->
            <!--</label>-->
            <!--</li>-->


        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes" v-show="qxShow">取消</button>
        <button class="zui-btn btn-parmary-f2a xmzb-db" v-show="qxShow==false">拒绝</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData" v-text="saveTitle"></button>
    </div>
</div>


<script type="text/javascript" src="jyglxx.js"></script>


