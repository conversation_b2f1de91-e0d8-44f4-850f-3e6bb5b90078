<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link href="sssq.css" rel="stylesheet">
</head>
<body class="body skin-default padd-l-10 padd-t-10 padd-r-10 height">
<div class=" padd-r-10 sssq padd-l-10">
  <div class="flex-container">
      <div class="box-content active">
          <div class="flex-container padd-b-5 flex-jus-sb">
              <p class="text-000 padd-r-20">SS20181216000006</p>
              <p class="text-000">2020年12月16日</p>
          </div>
          <div class="flex-container padd-b-5 flex-jus-sb">
              <p class="text-000 padd-r-20">骨伤一科</p>
              <p class="text-000">2020年12月16日</p>
          </div>
          <div class="flex-container padd-b-5 text-000">
              左胫骨平台骨折切开复位内固定术
          </div>
          <div class="flex-container">
              <p class="text-FF0000">排：2020年12月18日 12点 第一手术间</p>
          </div>
      </div>
  </div>
    <div class="flex-container">
        <div class=" flex-container padd-b-20 padd-t-10 flex-align-c padd-r-20">
            <span class="padd-r-5">手术类型</span>
            <span class="padd-r-5 padd-l-10">急诊</span>
            <div class="position padd-r-5">
            <input type="radio" id="four" name="two" v-model="pageState.sslx" value="0" class="zui-radio">
            <label for="four" class="padd-r-5"></label>
            </div>
            <span class="padd-r-5 padd-l-10">择期</span>
            <div class="position padd-r-5">
            <input type="radio" id="four1" name="two" v-model="pageState.sslx" value="1" class="zui-radio">
            <label for="four1" class="padd-r-5"></label>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">申&emsp;&emsp;请<br/>手术日期</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" id="sqTime" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">计&emsp;&emsp;划<br/>手术日期</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" id="jhTime" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">术前诊断</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" v-model="pageState.sqzd" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">手术名称</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" type="text"/>
            </div>
        </div>
    </div>
    <div class="flex-container">
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">术&emsp;&emsp;者</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.ssz" :name="'pageState.ssz'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">一&emsp;&emsp;助</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.sszs1" :name="'pageState.sszs1'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">二&emsp;&emsp;助</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.sszs2" :name="'pageState.sszs2'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">麻醉方式</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.mzfs" :name="'pageState.mzfs'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
    </div>
    <div class="flex-container flex-wrap-w">
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">麻醉医生</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange" :data-notEmpty="true" :child="zyYzclHszType_tran"
                              :index="pageState.zyType" :val="pageState.zyType" :name="'pageState.zyType'"
                              :search="true"></select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">申请医生</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.mzys" :name="'pageState.mzys'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">审核医生</span>
            <div class="zui-input-inline wh180">
                <select-input @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="pageState.shys" :name="'pageState.shys'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">感染类别</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" v-model="pageState.grlb" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-20 padd-t-10 padd-r-20">
            <span class="padd-r-5">合 并 症</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" v-model="pageState.hbz" type="text"/>
            </div>
        </div>
    </div>
    <div class="flex-container flex-wrap-w">
        <div class=" flex-container  padd-b-20 padd-t-10 padd-r-20" style="width: 100%">
            <span class="padd-r-5 whiteSpace">备&emsp;&emsp;注</span>
                <textarea class="zui-input" v-model="pageState.bz" style="height: 100px"></textarea>
        </div>
    </div>
    <div class="zui-table-tool padd-r-10 flex-jus-e flex-align-c zui-border-bottom flex-container font-14-654">
        <button class="root-btn btn-parmary"  @click="add()">新增</button>
        <button class="root-btn btn-parmary" @click="save()">保存</button>
        <button class="root-btn  btn-parmary-d2" @click="zf()">作废</button>
        <button class="root-btn  btn-parmary-f2a1" @click="print()">打印</button>
    </div>
</div>
<script type="text/javascript" src="sssq.js"></script>
</body>
</html>
