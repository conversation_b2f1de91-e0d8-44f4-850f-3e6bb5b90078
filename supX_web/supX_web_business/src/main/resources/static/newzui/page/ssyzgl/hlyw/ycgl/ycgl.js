(function () {
    //顶部工具栏
    var tool=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
            zt: '9',
            popContent:{},
            pgList:[]
        },
        methods:{
            //新建会诊
            AddModel:function (num,val) {
                sessionStorage.setItem('ycglitem',JSON.stringify(val));
                this.topNewPage('压疮评估','page/hsz/hlyw/ycgl/evaluate/evaluate.html')
            },
            //刷新和检索
            getData:function () {

            },
            //评估结果
            getKsData: function(){
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm", function(json) {
                    tool.pgList = json.d.list;
                });

            },

        }
    });

    //危急值管理列表
    var wxdjList = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
        data: {


        },

        updated:function () {
            changeWin()
        },
        methods: {
            //请求后台查询列表信息
            getData : function(){
                //加载动画效果
                common.openloading('.zui-table-view')
                //数据请求结束时关闭


                common.closeLoading()
            },
            //评估查看跳转
            pinggu:function (num,val) {
                sessionStorage.setItem('ycglitem',JSON.stringify(val));
                this.topNewPage('压疮评估','page/hsz/hlyw/ycgl/evaluate/evaluate.html')
            }



        }
    });

tool.getKsData();
    laydate.render({
        elem: '.times',
        type: 'date',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
})();






