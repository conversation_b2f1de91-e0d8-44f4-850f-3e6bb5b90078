<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>科室排班</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <!--<script src="../../../../js/page/kspb.js"></script>-->
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css"/>
</head>
<style>
    .pointerEvent {
        pointer-events: none
    }

    .paiban-week {
        width: auto;
    }
</style>
<body class="skin-default padd-l-10 padd-r-10">
<div class="wrapper over-auto" style="height: 100%">
    <div class="panel">
        <div class="panel-head border-bottom background-f reset " id="header">
            <div class="zui-row padd-b-10 padd-l-10 padd-r-10 padd-t-10">
                <div class="col-x-6">
                    <!--<button class="zui-btn btn-primary">保存</button>-->
                    <button v-if="!isEdit" class="zui-btn btn-primary" @click="edit" ref="list">编辑</button>
                    <button v-if="isEdit" class="zui-btn btn-primary" @click="baocun" ref="list">保存</button>
                    <button v-if="isEdit" class="zui-btn btn-default" @click="edit" ref="list">取消</button>
                    <button v-if="isEdit" class="zui-btn btn-primary" @click="resetMove" ref="list">重置排班</button>

                    <!--<p style="color: #45BFA6" v-show="!isEdit" @click="edit()"><span class="fa fa-edit"></span><span>编辑</span></p>-->
                    <!--<p v-show="isEdit" @click="edit()"><span class="fa fa-share"></span><span>取消</span></p>-->
                    <div class="flex-container flex-align-c margin-b-10">
                        <span class="padd-r-5 ft-14">科室选择</span>
                        <select-input class="wh180" @change-data="resultzcChange" :not_empty="false" :child="ghKsList"
                                      :index="'ksmc'" :index_val="'ksbm'" :val="json.ksbm" :name="'json.ksbm'"
                                      :search="true" :phd="''">
                        </select-input>
                        <!--</div>-->
                    </div>
                </div>
                <div class="col-x-6 text-right">
                    <button class="zui-btn btn-primary">导出.xls</button>
                    <button class="zui-btn btn-primary" @click="selectDay = toDay,getPbInfo()">本周</button>
                    <button class="zui-btn  btn-default btn-pa" @click="prvWeek()"><i class="wb-chevron-left"></i>
                    </button>
                    <button class="zui-btn btn-default btn-pa" @click="nextWeek()"><i class="wb-chevron-right"></i>
                    </button>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-10" v-show="isEdit">
                <span class="padd-r-5 ft-14">门牌号:</span>
                <select-input class="wh180" @change-data="resultChange" :not_empty="true" :child="mph_tran"
                              :index="'mph'" :index_val="'mph'" :val="mph" :name="'mph'"
                              :search="true" :phd="''">
                </select-input>
            </div>
        </div>
        <!--<div class="alert alert-info">
            点击班次再点击表格，可快速进行排班操作
        </div>-->
        <ul class="paiban-config clearfix option">
            <!--@mouseDown="beginDrag($event, item.id)"-->
            <!--@mouseUp="stopDrag($event, $index)"-->
            <!--class ：drag dragCSS-->
            <li data-id="1" v-for="(item, $index) in optionList" @click="biegDrag($event,$index,item.bcfamc)"
                :class="{'bgColor':isEdit && $index==num}">
                <!--<div class="optionBtu" v-text="item.name" :style="{background: item.bg, color: item.color}"></div>-->
                <div :id="item.bcfabm" class="optionBtu " v-text="item.bcfamc"
                     :style="{background: item.colour, color: item.fontcolour}"></div>
                <span>({{item.sbsj}} - {{item.xbsj}})</span>
            </li>
            <!--<li class="toolBtu">-->
            <!--<p style="color: #45BFA6" v-show="!isEdit" @click="edit()"><span class="fa fa-edit"></span><span>编辑</span></p>-->
            <!--<p v-show="isEdit" @click="edit()"><span class="fa fa-share"></span><span>取消</span></p>-->
            <!--</li>-->
        </ul>
        <!-- :style="{'margin':optionList.length==0?'24px auto auto auto':'0' }" -->
        <div class="paiban-week scheduling rysx_bottom_list">
            <table class=" table-render toDayTable">
                <thead>
                <tr>
                    <th class="ng-name">班次</th>
                    <th data-time="2017-10-16" class="wh122">周一{{weekToDay(selectDay, 1)}}</th>
                    <th data-time="2017-10-17" class="wh122">周二{{weekToDay(selectDay, 2)}}</th>
                    <th data-time="2017-10-18" class="wh122">周三{{weekToDay(selectDay, 3)}}</th>
                    <th data-time="2017-10-19" class="wh122">周四{{weekToDay(selectDay, 4)}}</th>
                    <th data-time="2017-10-20" class="wh122">周五{{weekToDay(selectDay, 5)}}</th>
                    <th data-time="2017-10-21" class="wh122">周六{{weekToDay(selectDay, 6)}}</th>
                    <th data-time="2017-10-22" class="wh122">周日{{weekToDay(selectDay, 7)}}</th>
                </tr>

                </thead>
                <tbody>
                <tr v-for="item in person" :id="item.rybm">
                    <th style="border-right: 1px solid #ccc" v-text="item.ryxm"></th>
                    <td @click.stop="savePb($event,$index)" :data-id="JSON.stringify(ifClick)"
                        :class="{'pointerEvent':ifClick}" v-for="(i, $index) in 7" :ryId="item.rybm"
                        :week="$index+1"></td>
                </tr>
                </tbody>
            </table>
        </div>

    </div>
</div>
<script type="application/javascript" src="yspb.js"></script>
</body>
</html>
