@colorff:#fff !important;
@font16:16px !important;
@font14:14px !important;
@color1a:#1abc9c !important;

@media print{

  @page{

    size:A4 portrait;

    margin:3.7cm 2.6cm 3.5cm;

  }



  h1{

    page-break-before:always;

  }



  h1,h2,h3,h4,h5,h6,

  thead,tfoot,tr,th,td,

  li{

    page-break-inside:avoid;

  }



  body{

    background-color:white;

    color:black;

  }



  nav,aside{

    display:none;

  }



  a::after{

    content:"("attr(href)")";

  }



  thead,tfoot{

    display:table-row-group;

  }

}


i,em{
  font-style: normal;
}
.fr{
  float: right !important;
}
.fl{
  float: left !important;
}
.color-1a{
  color: @color1a;
}
.color-red{
  color: #ff4532 !important;
}
.popus{
  width: 100%;
  height: 100%;
  position: fixed;
  z-index:9999;
  display: none;
  top: 0;
  background: rgba(0,0,0,0.5);
  .pop-system{
    width: 380px;
    height: 220px;
    background:@colorff;
    position: absolute;
    left: 50%;
    margin-left:-190px;
    top: 50%;
    margin-top: -110px;
    h2{
      height: 46px;
      background:@color1a;
      line-height: 46px;
      color: @colorff;
      padding: 0 18px;
      span{
        float: left;
        font-size:@font16;
        &:nth-child(2){
          color: rgba(255,255,255,.56);
          font-size:33px !important;
          cursor: pointer;
        }
      }
    }
    .pop-text{
      width: 100%;
      font-size: @font14;
      padding: 43px 0  62px 0;
      text-align: center;
    }

  }
}
.pop-ok{
  width: 100%;
  display: flex;
  justify-content: flex-end;
  .pop-btn{
    width: 88px;
    height: 36px;
    border: none;
    line-height: 36px;
    background:#d9dddc;
    border-radius: 4px;
    color:#8e9694;
    margin-right: 15px;
  }
  .pop-confirm{
    background: @color1a;
    color: @colorff;
  }
}
.popus-right{

  h2{
    height: 46px;
    background:@color1a;
    line-height: 46px;
    color: @colorff;
    padding: 0 18px;
    span{
      float: left;
      font-size:@font16;

    }
   a{
      color: rgba(255,255,255,.56);
      cursor: pointer;
    }
  }
  .sample{
    width: 100%;
    font-size:@font14;
    color:#7f8fa4;
    padding:26px 15px 10px;
  }
  .sample-select{
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    height: 36px;
    select{
      -webkit-appearance: none;
      height:36px;
      border:1px solid #dfe3e9;
      border-radius: 4px;
      width: 100%;
    }
  }
  .sample-texarea{
    width: 100%;
    padding: 0 15px;
    textarea{
      width: 100%;
      border:1px solid #dfe3e9;
      height: 122px;
      padding: 10px;

    }

  }
  .sample-btn{
    position: absolute;
    bottom:24px;
    display: flex;
    justify-content: flex-end;
  }
  .ti-close:before{
    font-size: 20px !important;
  }
}
.min-chuangkou{
  right: 10px;
  bottom: 92px;
  z-index: 1112;
}
.zui-table-view .zui-table-body{
  height: 70vh;
  min-height: 70vh;
}

.zui-table-view .zui-table-fixed.table-fixed-r{
  border-left: none;
}
.zui-table{
  border-bottom: 1px solid #eee;
  border-left: 1px solid #eee;
}
.zui-table-view table td:nth-child(1){
  border-left:none !important;
}



.zui-table-view table td{
  border-bottom: none;
}
//.zui-table-view .zui-table-body tr{
//  border-bottom: 1px solid #eeeeee;
//}
.dishide{
  display: none;
}