<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="x-ua-compatible" content="IE=edge, chrome=1"/>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script type="application/javascript" src="../brgh-hs/insurancePort/001gzydnh/backFun.js"></script>
    <!--<link rel="stylesheet" href="/page/xtwh/xtpz/mzpjgs/mzpjgs.css" media="print"/>-->
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="brgh.css">
    <link rel="stylesheet" href="/FR/ReportServer?op=emb&resource=finereport.css">
    <title>病人挂号</title>
</head>

<body class="skin-default padd-l-10 padd-r-10">
<div class="printArea printShow"></div>

<!-- 打印挂号票据 begin -->
<div id="mz-print" v-show="isShow" v-cloak class="print-mzsffp" style="font-size:3.4mm;line-height: 1.4">
    <p style="padding-left: 10mm;" v-text="printData[0].fphm">卡号</p>
    <p style="padding-left: 10mm;" v-text="printData[0].brfyPrintList[0].brxm">姓名</p>
    <div style="margin-top: 8.5mm;height: 36mm;overflow: hidden;">
        <div class="flex-container" v-for="(item,index) in printData">
            <div class="flex-one" style="mrgin-right: 1.5mm;" v-text="item.brfyPrintList[0].xmmc">费用项目</div>
            <div class="flex-one" v-text="fDec(item.brfyPrintList[0].fyhj,2)">金额</div>
        </div>
    </div>
    <p style="padding-left:50%;line-height: 7mm;text-align: center" v-text="fDec(printData[0].fyhj,2)">合计</p>
    <p style="padding-left:12mm;text-align: center;line-height: 7.5mm;" v-text="printData[0].fyhjdx">金额大写</p>
    <p class="flex-container" style="padding-left: 13.5mm;">
        <span style="width: 14mm;height: 4.76mm;overflow: hidden;"
              v-text="printData[0].brfyPrintList[0].czyxm">收费员</span>
        <span style="mrgin-right:3.5mm;width:9mm;text-align: right;"
              v-text="new Date(printData[0].brfyPrintList[0].sfsj).getFullYear()">年</span>
        <span style="mrgin-right:3.5mm;width: 6.5mm;text-align: right;"
              v-text="new Date(printData[0].brfyPrintList[0].sfsj).getMonth() + 1">月</span>
        <span style="width:7mm;text-align:right;"
              v-text="new Date(printData[0].brfyPrintList[0].sfsj).getDate()">日</span>
    </p>
    <p style="padding-left: 13.5mm;">四川省建筑医院</p>
</div>
<!-- 打印挂号票据 end -->

<div class="wrapper context panel printHide ">
    <div v-cloak class="panel-head bg search padd-b-10 padd-l-10 padd-t-10 padd-r-10">
        <div class="flex-container flex-jus-sb ">
            <div class="flex-container flex-align-s whiteSpace flex-align-c">
                <button class="tong-btn btn-parmary" @click="updata">新增</button>
                <div class="tool-left position">
                    <input autocomplete="off" type="text" title="点击后刷卡" class="zui-input wh120" placeholder="请点击后刷卡" v-model="ykth"
                           @keydown.13="loadYkt($event)" id="ykth"/>
                </div>
                <div class="tool-left position">
                    <input autocomplete="off" v-model="text" @input="searching(null,$event.target.value)" @keyDown="changeDown($event)"
                           id="user" type="text" name="phone" class="zui-input wh120" placeholder="请填写姓名/证件号码/手机号"/>
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </div>
                <button class="tong-btn btn-parmary-b " @click="loadIdCard()"><i class=" icon-width icon-dsfz"></i>读取身份证</button>
                <button class="tong-btn btn-parmary-b " @click="lxmzFun()">离休挂号</button>
                <button v-if="N05001200229 == '1'" class="tong-btn btn-parmary-b " @click="printJkk()"><i
                        class="padd-l-20 icon-width icon-dybk"></i>打印
                </button>
                <button v-if="N05001200229 == '1'" class="tong-btn btn-parmary-b " @click="regJkk()"><i
                        class="padd-l-20 icon-width icon-dybk"></i>注册
                </button>
                <div v-if="N05001200238=='1'" @click="resultChangeData" :class="{'color-green':lstd=='1'}" class="no-pading  cursor">
                    {{nstd_tran[lstd]}}
                </div>
                <div  class="padd-l-10 color-wtg font-12">当前模式为：[{{ghms}}] {{ghfs}} {{dyfs}}</div>
                <div  class="padd-l-10 color-wtg ">已挂人次:{{hycList.yghs}}&emsp;&emsp;待诊人次:{{hycList.djzs}}&emsp;&emsp;剩余号源:{{hycList.syhys}}</div>
            </div>
            <div class=" text-right">
                <button class="zui-btn btn-warning" id="but_regyl" @click="showLogs()">挂号记录</button>
            </div>
        </div>
    </div>
    <div v-cloak class="contextStyle  padd-r-20 padd-l-20 contextSize contextInfo">
        <div class="zui-row ">
            <div class="tab-card">
                <!--<div class="conTit">-->
                <!--<div>挂号信息</div>-->
                <!--</div>-->
                <div class="tab-card-header">
                    <div class="tab-card-header-title">挂号信息</div>
                </div>
                <div class="grid-box  tab-card-body">
                    <div class="flex-container  flex-wrap-w">
                        <div class="flex-align-c padd-r-20  flex-container margin-b-5"  :class="N05002201145 == '1' ? 'font-bolder red' :''">
                            <span :class="lstd == '0' ? 'font-bolder red' :''" class="padd-r-5 ft-14 wh80 text-right">联系人电话</span>

                            <input autocomplete="off" @blur="handlerFun()" :data-notEmpty="N05002201145=='1' ?true:false" @keydown.up.prevent @keydown.down.prevent @mousewheel.prevent class="zui-input wh150"
                                   v-model.trim="json.lxrdh" type="number"
                                   @keydown="nextFocus($event)"
                                   onkeyup="value=value.replace(/[^\d]/g,'')" id="lxrdh">
                        </div>
						<div class="flex-align-c padd-r-20 flex-container margin-b-5">
						    <span class="padd-r-5 font-bolder red wh80 text-right ft-14">检查类型</span>
						    <select-input class="wh150" @change-data="resultChange"
						                  :child="hslx_tran"
						                  :index="json.sffs"
						                  :val="json.sffs" :name="'json.sffs'" :search="false" :disable="false" :phd="''">
						    </select-input>
						</div>
						<div class="flex-align-c padd-r-20 flex-container margin-b-5">
						    <span class="padd-r-5 font-bolder red wh80 text-right ft-14">健康码</span>
						    <select-input class="wh150" @change-data="resultChange"
						                  :child="jkm_tran"
						                  :index="json.tftmys"
						                  :val="json.tftmys" :name="'json.tftmys'" :search="false" :disable="false" :phd="''">
						    </select-input>
						</div>
                    </div>
					
                </div>
            </div>

            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">基本信息</div>
                </div>
                <!--<div class="conInfo">-->
                <!--<div class="conTit">-->
                <!--<div>基本信息</div>-->
                <!--</div>-->
                <div class="grid-box tab-card-body flex-wrap-w  flex-container">
                    
                    <div class="flex-align-c padd-r-20 flex-container margin-b-5">
                        <span class="padd-r-5 font-bolder red ft-14 wh80 text-right">患者姓名</span>
                        <input autocomplete="off" class="zui-input wh150" :disabled="json.sfzjhm && json.sfzjhm.length !=0 && json.brxm ? true : false" v-model.trim="json.brxm" type="text"
                                :data-notEmpty="lstd=='0'?true:'removeFalse'" @keydown="nextFocus($event)" id="hzxm">
                    </div>
                    <div class="flex-align-c padd-r-20 flex-container margin-b-5">
                        <span class="padd-r-5 font-bolder red wh80 text-right ft-14">性别</span>
                        <select-input class="wh150" @change-data="resultChange"
                                      :not_empty="lstd=='0'?true:'removeFalse'" :child="brxb_tran"
                                      :index="json.brxb"
                                      :val="json.brxb" :name="'json.brxb'" :search="false" :disable="false" :phd="''">
                        </select-input>
                    </div>
                    
                    <div class="flex-align-c padd-r-20  flex-container margin-b-5">
                        <span class="padd-r-5 ft-14 wh80 text-right">证件类型</span>
                        <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="zjlxList"
                                      :index="'zymc'"
                                      :index_val="'zybm'" :val="cardType" :name="'cardType'" :search="false"
                                      :disable="false"
                                      id="zjlx">
                        </select-input>
                    </div>
                    <div class="flex-align-c  padd-r-20 flex-container margin-b-5">
                        <span class="padd-r-5 ft-14 wh80 text-right">证件号码</span>
                        <div class="position">
                            <input @blur="setAge()" data-select="no" class="zui-input wh150 title" type="text"
                                   v-model.trim="json.sfzjhm" autocomplete="off"
                                   placeholder="" @keydown.enter="nextFocus($event)" id="zjhm">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">居住信息</div>
            </div>
            <div class="grid-box tab-card-body">
                <div class="col-xxl-12 flex-container flex-wrap-w flex-align-c">
                    <!--<div class="info">-->
                    <span :class="lstd == '0' ? 'font-bolder red' :''"
                          class="ft-14 whiteSpace padd-r-5 wh80 text-right">省/市/(区/县)</span>
                    <!--<p>省/市/(区/县)</p>-->
                        <select-input class="wh150" @change-data="resultzcChange"
                                      :not_empty="lstd=='0'?true:'removeFalse'"
                                      :child="provinceList"
                                      :index="'xzqhmc'"
                                      :index_val="'xzqhbm'" :val="json.jzdsheng" :search="true" :name="'json.jzdsheng'"
                                      :index_mc="'jzdshengmc'" :phd="'省'" id="sheng">
                        </select-input>
                        <select-input class="wh150" @change-data="resultzcChange" :data-notEmpty="false" :child="cityList"
                                      :index="'xzqhmc'"
                                      :index_val="'xzqhbm'" :val="json.jzdshi" :search="true" :name="'json.jzdshi'"
                                      :index_mc="'jzdshimc'"
                                      :phd="'市'" id="shi">
                        </select-input>
                        <select-input class="wh150" @change-data="resultzcChange" :data-notEmpty="false" :child="countyList"
                                      :index="'xzqhmc'" :index_val="'xzqhbm'" :val="json.jzdxian" :search="true"
                                      :name="'json.jzdxian'"
                                      :index_mc="'jzdxianmc'" :phd="'区/县'" id="xian">
                        </select-input>
                    <select-input class="wh150" @change-data="resultzcChange" :data-notEmpty="false" :child="zhenList"
                                  :index="'xzqhmc'" :index_val="'xzqhbm'" :val="json.jzdxiang" :search="true"
                                  :name="'json.jzdxiang'"
                                  :index_mc="'jzdxiangzhenmc'" :phd="'镇'" id="xian">
                    </select-input>
                    <select-input class="wh150" @change-data="resultzcChange" :data-notEmpty="false" :child="cunList"
                                  :index="'xzqhmc'" :index_val="'xzqhbm'" :val="json.jzdcun" :search="true"
                                  :name="'json.jzdcun'"
                                  :index_mc="'jzdcunmc'" :phd="'村'" id="xian">
                    </select-input>
                    <input data-select="no" @blur="setMph($event)" class="zui-input wh120" type="text" v-model="json.jzdmph" placeholder="门牌号"/>
                    <div class=" col-xxl-6 flex-container flex-align-c padd-l-10 padd-t-10 margin-b-5">
                        <span class="ft-14 whiteSpace padd-r-5 wh80 text-right">详细地址</span>
                        <!--<div class="selectInput">-->
                        <input data-select="no" class="zui-input" type="text" v-model="json.jzdmc" placeholder="街道/门牌号"
                               @keydown="saveDateHc($event)"
                               id="xxdz"/>
                        <!--</div>-->
                    </div>
                    <!--<div class="infoIpt">
                <p>邮编</p>
                <div class="selectInput">
                    <input type="number" v-model="json.hkdyb" placeholder="自动生成" />
                </div>
            </div>-->
                </div>
            </div>
        </div>

        <div v-if="gznhType">
		<span style="color:red;font-size:18px;">
		人员属性：<span style="font-weight: bold;">{{gznhObj.jzmzName}}</span>，
		本年度门诊补偿金额：<span style="font-weight: bold;">{{gznhObj.outpCompensateCost}}</span>元，
		本年度慢性病补偿金额：<span style="font-weight: bold;">{{gznhObj.chroCompensateCost}}</span>元。
        本次报销金额：<span style="font-weight: bold;">{{bcfy}}</span>元。
        </span>
        </div>
        <model :s="'确定'" :c="'取消'" @default-click="pkhShow=false" @result-clear="pkhShow=false" :model-show="true"
               @result-close="pkhShow=false" v-if="pkhShow" :title="title">
            <div class="bqcydj_model">
                <div class=" ">
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">姓名:{{pkhObj.XM}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">性别:{{xtwhxb_tran[pkhObj.XB]}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">手机号:{{pkhObj.LXDH}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">地址:{{pkhObj.DZ}}</span>
                    </div>
                </div>
            </div>
        </model>

        <model style="top:10%" :s="'确定'" :c="'取消'" @default-click="bxShow=false" @result-clear="bxShow=false"
               :model-show="false" @result-close="bxShow=false" v-if="bxShow" :title="title">
            <div class="chxx_model" style="height: 100%">
                <div class="flex-container flex-dir-c flex-one" id="loadPage">
                </div>
            </div>
        </model>
    </div>
    <div class="side-form pop flex-container flex-dir-c" :class="{'ng-hide':!isFold}" v-cloak id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span>挂号记录</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="grid-box">
            <div class="flex-container flex-align-c padd-t-10 padd-b-10   margin-l-10">
                <select-input class="wh80" @change-data="resultChangeData"  :child="ghjl_tran"
                              :index="param.userId" :val="param.userId" :name="'param.userId'">
                </select-input>
                    <input type="text" name="phone" class="zui-input margin-l-10  wh120  padd-r-10" v-model="jsValue" placeholder="请填写关键字" @keydown.enter="goToPage(1)"/>
                    <input id="dbegin" class="zui-input margin-l-10 wh112">
                    <span class="padd-l-10 padd-r-10">至</span>
                    <input id="dEnd" class="zui-input wh112 margin-r-10">
                    <button class="tong-btn btn-parmary height-28" @click="getData">查询</button>
            </div>
        </div>
        <div class="zui-table-view  flex-container flex-dir-c ">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-l">挂号序号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">患者姓名</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">费别</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m">性别</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">挂号科室</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">挂号医生</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">患者年龄</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">挂号时间</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">是否退号</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" @dblclick="edit($index,item)"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.ghxh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fbmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m" v-text="brxb_tran[item.brxb]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ghksmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jzysxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.brnl}}{{nldw_tran[item.nldw]}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.ghrq, 'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-S">{{item.thbz==0?'否':'是'}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="zui-table-fixed table-fixed-l">
                <!-- 有浮动就加 table-fixed-r -->
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-l">
                                <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!--@click="hover($index,$event)"-->
                <div class="zui-table-body " @scroll="scrollTableFixed($event)" style="border-right: 1px solid #eee;">
                    <table class="zui-table">
                        <tbody>
                        <tr @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                            v-for="(item, $index) in jsonList"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            class="tableTr2 table-hovers-filexd-l">
                            <td class="cell-l">
                                <div cell="cell-2-0" class="zui-table-cell cell-l">{{item.ghxh}}</div>
                            </td>
                        </tr>
                        </tbody>

                    </table>
                </div>
            </div>
            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>
<div class="printHide zui-table-tool conBtu flex-container flex-jus-sb flex-align-c padd-l-20 padd-r-20" v-cloak>
    <div class="flex-container flex-align-c">
        <button class="zui-btn btn-primary" @click="doSave()" v-text="text_sub"></button>
        <button v-if="is_csqx.cs00400100234=='1'" class="zui-btn btn-primary-b" @click="pkf()">贫困户认证</button>
<!--        <button class="zui-btn btn-default" @click="deleteDate()">退号操作</button>-->
        <!--
        <button class="zui-btn btn-default">补打</button>
        -->
        <p>当前医生挂号数：<span v-text="doctorDayNum"></span>&emsp;当前票号：{{pjData.dqsyh}}&emsp;剩余张数：{{pjData.fpzs}}&emsp;本次挂号费用：<span
                class="font-16 color-wtg">{{totalMoney}}</span>元</p>
    </div>
    <div>
        <button class="zui-btn btn-primary" @click="bdfp()">补打发票</button>
<!--        <button class="zui-btn btn-primary" @click="bkfp()">补开电子发票</button>-->
        <button class="zui-btn btn-primary" v-if="is_csqx.N05001200252 == '1'" style="background: red" @click="resetBrylkxx()">注销卡号</button>
        <button class="zui-btn btn-primary" v-if="is_csqx.N01006400137" @click="printGhfQrcode(null)">挂号费二维码</button>
    </div>

    <model  :s="'确定'" :c="'取消'" @default-click="saveGh" @result-clear="closeFun"
           :model-show="true" @result-close="closeFun" v-if="bxShow" :title="'请选择支付类别'">
        <div class="zflx_model flex-container flex-align-c flex-jus-c flex-one">

        </div>
    </model>
</div>

<!--<div class="bottom_btu printHide">-->
<!--<div class="conBtu">-->
<!--<div class="btu" @click="doSave()" v-text="text_sub"></div>-->
<!--<div class="btu btu_cancel" @click="deleteDate()">退号操作</div>-->
<!--<div class="money">-->
<!--<p>本次挂号费用：</p>-->
<!--<span v-text="totalMoney">15.00</span>-->
<!--<p>元</p>-->
<!--</div>-->
<!--</div>-->
<!--</div>-->

</body>
<script type="application/javascript" src="backFun.js"></script>
<script type="application/javascript" src="brgh.js"></script>

</html>
