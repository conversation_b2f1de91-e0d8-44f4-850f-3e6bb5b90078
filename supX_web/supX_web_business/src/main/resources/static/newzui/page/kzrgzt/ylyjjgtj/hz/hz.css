.canvas{
    width: 100%;
   height: 100%;
    padding-left: 0;
}
.height-310{
    min-height: 310px;
}
#pie<PERSON>anvas{
    height: 85%;
    min-width: 100%;
    width: 100%;
}
.font-30{
    font-size:30px;
}
.color-757c83{
    color:#757c83;
}
.margin-t-3{
    padding-bottom: 6px;
}
.wh40{
    width: 24%;
}
.wh60{
    width: 66%;
}
.chart{
    padding: 0 10px 0 0;
}
.bottom-right{
    width: 100%;
    height: 100%;
}
.start-ys3{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:167px 48px;
    width: 167px;
    height: 48px;
    line-height:70px;
}
.count-start4{
    left: 38px;
}
.start-ws6{
    line-height: 22px;
    background: url(/newzui/pub/image/<EMAIL>) center no-repeat;
    background-size: 155px 22px;
    width: 155px;
    height: 22px;
}
.errorBg{
    background-color:rgba(255,92,99,0.10)!important;
}
.count-title{
    padding: 11px 13px;
}
.zui-table-view .zui-table-fixed{
    top: auto;
}
.tong-top{
    padding-right: 16px;
}