
<div class="bqcydj_model over-auto" id="yndryb_009">
        <div class="flex-container margin-t-10 padd-l-5 flex-wrap-w flex-align-c">
            <div class="flex-container flex-align-c margin-b-15 ">
                <span class="whiteSpace  ft-14 margin-r-5">地&emsp;&emsp;区</span>
                <select-input class="wh120" @change-data="dqresultChange"
                              :child="dqList"
                              :index="'mc'"
                              :index_val="'bm'" :val="json.bm" :name="'json.bm'" :search="true"
                              :disable="false"
                              :phd="''">
                </select-input>
            </div>
            <div class="flex-container flex-align-c margin-b-10 ">
                <label for="four2" class="flex-container flex-align-c">
                    <span class="padd-r-5 padd-l-10">身份证</span>
                    <div class="position padd-r-5">
                        <input type="radio" id="four2" name="two" v-model="json.kh" value="0" class="zui-radio" >
                        <label for="four2" class="padd-r-5"></label>
                    </div>
                </label>
                <label for="four3" class="flex-container flex-align-c">
                    <span class="padd-r-5">医疗卡号</span>
                    <div class="position padd-r-5">
                        <input type="radio" id="four3" name="two" v-model="json.kh" value="1" class="zui-radio" >
                        <label for="four3" class="padd-r-5"></label>
                    </div>
                </label>

            </div>

            <div class="flex-container flex-align-c margin-b-10 " v-if="json.kh==0">
                <span class="whiteSpace  ft-14 margin-r-5">身份证卡号</span>
                <div class=" wh180 margin-r-10">
                    <input class="zui-input " v-model="sfzsearch"  @keydown="query"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-10 " v-if="json.kh==1">
                <span class="whiteSpace  ft-14 margin-r-5">医疗卡号</span>
                <div class=" wh180">
                    <input class="zui-input " v-model="ylhsearch"  @keydown="query"/>
                </div>
            </div>

            <div class="flex-container padd-r-5 flex-align-c margin-b-10" >
                <span class="whiteSpace  ft-14 margin-r-5">读卡</span>
                <select-input  class="wh180" @change-data="resultData"
                               :child="dk_tran" :index="json.prm_aka130" :val="json.prm_aka130"
                               :search="true" :name="'json.prm_aka130'" :not_empty="true">
                </select-input>
            </div>
        </div>
        <div class="zui-table-view hzList " v-show="json.kh==1">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s"><span>家庭序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>姓名</span></div>
                        </th>
                    </tr>
                    <!--@click="checkOne($index)"-->
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" data-no-change="true" style="height: 270px" @scroll="scrollTable($event)">
                <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                <table class="zui-table table-width50" v-if="cyList.length!=0">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                        @mouseleave="switchIndex()"
                        @click="switchIndex('activeIndex',true,$index),ckIndex('activeIndex',true,$index)"
                        :tabindex="$index"
                        v-for="(item, $index) in cyList">
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.xh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cyxm"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="cyList.length==0" class="  noData text-center zan-border">暂无数据...</p>
            </div>
        </div>
        <div class="flex-container flex-wrap-w flex-align-c padd-t-20">
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">姓&emsp;&emsp;名</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.brxm"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">身份证号</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.sfzjhm"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">家庭地址</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.jtdz"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">性別</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.brxb"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">年&emsp;&emsp;龄</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.nl"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">民族</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.mz"/>
                </div>
            </div>
            <!--<div class="flex-container flex-align-c margin-b-15 margin-l-15">-->
                <!--<span class="whiteSpace  ft-14 margin-r-5">个人性质</span>-->
                <!--<div class=" wh180">-->
                    <!--<input class="zui-input " type="text" v-model="grxxJson.grxz"/>-->
                <!--</div>-->
            <!--</div>-->
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">家庭账户余额</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.jtzhye"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">上补时间</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.sbsj"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">统筹余额</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.tcye"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">门诊个人累补</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.mzgrlb"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">门诊家庭累补</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.mzjtlb"/>
                </div>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">成员序号</span>
                <div class=" wh180">
                    <input class="zui-input " type="text" v-model="grxxJson.cyxh"/>
                </div>
            </div>

            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">病情编码</span>
                <input @keydown="changeDown($event)" class="zui-input position" v-model="grxxJson.zdmc" @input ="searching(false,$event.target.value)">
                <search-table :message="searchCon" :selected="selSearch"
                              :them="them" :them_tran="them_tran" :page="pageSelect"
                              @click-one="checkedOneOut"  @click-two="checkedOneOut">
                </search-table>
            </div>

            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">补偿类别</span>
                <select-input class="wh180" @change-data="resultChange"
                              :child="zybclbList"
                              :index="'mc'"
                              :index_val="'bm'" :val="bcjson.bm" :name="'bcjson.bm'" :search="true"
                              :disable="false"
                              :phd="''">
                </select-input>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">来院状态</span>
                <select-input class="wh180" @change-data="resultChange"
                              :child="ryztList"
                              :index="'rymc'"
                              :index_val="'rybm'" :val="lyjson.rybm" :name="'lyjson.rybm'" :search="true"
                              :disable="false"
                              :phd="''">
                </select-input>
            </div>
            <div class="flex-container flex-align-c margin-b-15 margin-l-15">
                <span class="whiteSpace  ft-14 margin-r-5">就诊类型</span>
                <select-input class="wh180" @change-data="resultChange"
                              :child="jzlxList"
                              :index="'jzmc'"
                              :index_val="'jzbm'" :val="jzjson.jzbm" :name="'jzjson.jzbm'" :search="true"
                              :disable="false"
                              :phd="''">
                </select-input>
            </div>
<!--            <div class="flex-container flex-align-c margin-b-15 margin-l-15">-->
<!--                <span class="whiteSpace  ft-14 margin-r-5">联系电话</span>-->
<!--                <div class=" wh180">-->
<!--                    <input class="zui-input " type="text" v-model="grxxJson.sjhm"/>-->
<!--                </div>-->
<!--            </div>-->
            <div class="zui-row buttonbox">
                <div class="zui-btn table_db_esc btn-default" @click="cancel">取消</div>
                <div class="zui-btn table_db_save btn-primary" @click="confirm">确认</div>
            </div>
        </div>
    </div>
<script type="application/javascript" src="insurancePort/009gsnhjk/009gsnhjk.js"></script>