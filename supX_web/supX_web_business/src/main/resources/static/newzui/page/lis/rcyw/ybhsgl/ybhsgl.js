(function () {
    $(".zui-table-view").uitable();
    var s=new Date().getTime()
    var l=new Date()
    var e=l.setDate(l.getDate()+1)
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            index:1,
            old:'原始数据',
            isShowpopL:false,
            isShow:false,
            flag:false,
            title:'',
            centent:'',
            flags:false,
            pop:{},
            param: {
                page: 1,
                rows: 10,
                total: '',
                time: '',
                bah: '',
                jyxh:''
            },
            jydjList: [],
            checkedData:''
            

        },
        methods:{
            //核收
            heshou:function () {
            		  //判断是否选择
            		  var yq=0;
		              for (var i = 0; i < this.isChecked.length; i++) {
		                  if (this.isChecked[i] == true) {
		                      yq++;
		                  }
		              }
		              if(yq==0){
		            	malert("请至少选择一条申请进行核收",'top','defeadted');
		              	return;
		              }
            	
            	
	              var List = [];
	              if(this.isChecked.length>0){
		              for (var i = 0; i < this.isChecked.length; i++) {
		                  if (this.isChecked[i] == true) {
		                      var jydj = {};
		                      List.push(wap.jydjList[i]);
		                  }
		              }
		              this.checkedData = List;
	              }
	              
	              pop.flag=false;
	              pop.isShowpopL=true;
	              pop.isShow=true;
	              pop.title='确认核收';
	              pop.ts=' 确定核收选中的项目吗？';
	              pop.dyShow=true;
	              pop.zfShow=false;
            	

            },
            //打印
            Btnprint:function () {
                window.print();
            },
            //作废
            zuofei:function () {
            	
            	 //判断是否选择
      		  var yq=0;
	              for (var i = 0; i < this.isChecked.length; i++) {
	                  if (this.isChecked[i] == true) {
	                      yq++;
	                  }
	              }
	              if(yq==0){
	            	malert("请至少选择一条申请进行作废",'top','defeadted');
	              	return;
	              }
            	
	              
                var List = [];
                if(this.isChecked.length>0){
	                for (var i = 0; i < this.isChecked.length; i++) {
	                    if (this.isChecked[i] == true) {
	                        var jydj = {};
	                        List.push(wap.jydjList[i]);
	                    }
	                }
	                this.checkedData = '{"list":' + JSON.stringify(List) + '}';
                }
                
                pop.title='确认作废';
                pop.ts=' 确定作废选中的项目吗？';
                pop.dyShow=false;
                pop.flag=false;
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.zfShow=true;
                
            },
          //刷新获取数据
        	sx : function () {
        		//解析时间
               if (this.param.time != null) {
                    var times = this.param.time.split(" - ");
                    this.param.sqrq = times[0];
                    this.param.sqrqEnd = times[1];
                }
                this.param.lx = '1';//住院
                console.log( JSON.stringify(this.param));
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryYbhsdj&yq=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                           wap.$nextTick(function () {
                                $(".zui-table-view").uitable();
                            })
                    	wap.totlePage =Math.ceil(json.d.total / wrapper.param.rows);
                        wap.jydjList = json.d.list;
                        malert("查询成功",'top','success');
                    } else {
                        malert("获取申请失败" + json.c,'top','defeadted');
                        return;
                    }
                });
        	},
        	jysb: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    if (json.a == 0) {
                    	wap.jysbList = json.d.list;
                    } else {
                        malert("获取申请检验设备失败" + json.c);
                        return false;
                    }
                });
            },
        	cx: function(){
        		this.sx();
        	}

        },
        watch:{
        	'param.time':function(){
        		this.sx();
        	},
        	'param.bah':function(){
        		this.sx();
        	}/*,
        	'param.jyxh':function(){
        		var a={
                        page: 1,
                        rows: 20,
                        total: '',
                        sqrq:'',
                        sqrqEnd:'',
                        jyxh:this.param.jyxh
                    };
        			if(a.jyxh != null && a.jyxh != ''){
		                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryYbhsdj&yq=" + JSON.stringify(a), function (json) {
		                	
		                    if (json.a == 0) {
		                    	wap.jydjList = json.d.list;
		                        if(json.d.list.length==1){
		                        	wrapper.isChecked[0]=true;
		                        	
		                        	 var List = [];
		                             if(wrapper.isChecked.length>0){
		             	                for (var i = 0; i < wrapper.isChecked.length; i++) {
		             	                    if (wrapper.isChecked[i] == true) {
		             	                        var jydj = {};
		             	                        List.push(wap.jydjList[i]);
		             	                        isTabel.hsjlList.push(wap.jydjList[i]);
		             	                    }
		             	                }
		             	                isTabel.hsjlListLength=isTabel.hsjlList.length;
		             	               wrapper.checkedData = '{"list":' + JSON.stringify(List) + '}';
		                             }
		                        	
		                        	pop.flag=false;
		                            pop.isShowpopL=true;
		                            pop.isShow=true;
		                            pop.title='确认核收';
		                            pop.ts=' 确定核收选中的项目吗？';
		                            pop.dyShow=true;
		                            pop.zfShow=false;
		                        }
		                        if(a.jyxh == null && a.jyxh == ''){
		                        	malert('未查询该条码的数据','top','defeadted');
		                        }
		                        
		                    } else {
		                        malert("查询失败" + json.c);
		                        return false;
		                    }
		                });
        			}
        	}*/
        }
    });
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394',
        range:true
        ,done:function (value,data) {
            wrapper.param.time = value
        }
    });
    var pop=new Vue({
        el:'#pop',
        data:{
            isShowpopL:false,
            isShow:false,
            dyShow:false,
            zfShow:false,
            flag:false,
            title:'',
            ts:'',
        },
        methods:{

            //确定
            zuofeiok:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updatezf_zy',wrapper.checkedData).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				//成功回调提示
                                malert('作废成功','top','success');
                                this.jsyy='';
                                this.jscl='';
                                wrapper.sx();
                			}
                			
                        }, 
                        function(error) {
                        	malert(error,'top','success');
                        });
            },
            heshouok:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                var data='{"list":' + JSON.stringify(wrapper.checkedData) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updatehs_zy',data).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				//成功回调提示
                                malert('核收成功','top','success');
                                console.log(wrapper.checkedData)
                                for (var int = 0; int < wrapper.checkedData.length; int++) {
                                	isTabel.hsjlList.push(wrapper.checkedData[int]);
								}
            	                isTabel.hsjlListLength=isTabel.hsjlList.length;
                                
                                wrapper.param.jyxh='';
                                wrapper.sx();
                			}
                			
                        }, 
                        function(error) {
                        	malert(error,'top','success');
                        });
            },

        },
    });


var wap=new Vue({
    el:'.zui-table-view',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data:{
    	totlePage:0,
    	page:1,
    	rows:10,
        title:'',
        centent:'',
        jysbList:[],
        jydjList:[],
    },
    created:function(){
        wrapper.param.time=this.formDate(s)+' - '+this.formDate(e)
    },
    filters: {
        formDate: function (value) {
            var d = new Date(value);
            return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
        }
    },
    methods:{
        formDate: function (value) {
            var d = new Date(value);
            return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
        },
    	jsdydj : function(jydj){
        	this.jydj = jydj;
        	console.log(jydj);
        },
        getData : function (){
        	wrapper.param.rows=this.rows;
        	wrapper.param.page=this.page;
        	wrapper.sx();
        }

    }
})
    var wapse=new Vue({
        el:'#brzcList',
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            flag:false,
            isFold: false,
            param:null,
            jsyy:'',
            jscl:''
        },
        methods:{
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
            	//判断是否选择
      		  var yq=0;
	              for (var i = 0; i < wrapper.isChecked.length; i++) {
	                  if (wrapper.isChecked[i] == true) {
	                      yq++;
	                  }
	              }
	              if(yq==0){
	            	  malert("请至少选择一条数据拒收",'top','defeadted');
	                  return;
	              }
	              
	              if(this.jsyy==''){
	            	  malert("请选择拒收原因",'top','defeadted');
	                  return;
	              }
            	
                //多选提交
                var List = [];
                if(wrapper.isChecked.length>0){
	                for (var i = 0; i < wrapper.isChecked.length; i++) {
	                    if (wrapper.isChecked[i] == true) {
	                        var jydj = {};
	                        wap.jydjList[i].jsyy=this.jsyy;
	                        wap.jydjList[i].jscl=this.jscl;
	                        List.push(wap.jydjList[i]);
	                    }
	                }
	                var json = '{"list":' + JSON.stringify(List) + '}';
                }
                this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updatejs_zy',json).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				//成功回调提示
                                malert('拒收成功','top','success');
                                $(".side-form").addClass('ng-hide');
                                this.jsyy='';
                                this.jscl='';
                                wrapper.sx();
                                $(".side-form-bg").removeClass('side-form-bg')
                			}
                			
                        }, 
                        function(error) {
                        	malert(error,'top','success');
                        });
                
                
            },

        },
    });
    var isTabel=new Vue({
        el:'.isTabel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isTabelShow:false,
            minishow:true,
            isShowpopL:false,
            isShow:false,
            
            //核收记录
            hsjlList:[],
            hsjlListLength:0
        },
        methods:{
            tabshow:function () {
                this.isTabelShow=true;
                this.minishow=false;
            },
            showDom:function () {
                this.isTabelShow=false;
                this.minishow=true;
            }
        },
    });
    
  //初始化调用方法
    wrapper.jysb();//获取检验设备
})()
