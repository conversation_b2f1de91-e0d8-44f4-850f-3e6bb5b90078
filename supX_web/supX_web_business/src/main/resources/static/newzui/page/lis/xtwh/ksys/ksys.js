(function () {
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:'',
            searchAll:'',
            param:{
            	page: 1,
    			rows: 10,
    			parm:'',
            },
        },
        methods:{
        	//刷新
        	getData:function(){
        		waps.getData();
        	},
        	//保存
        	saveData:function(){
        		console.log(waps.updateList);
        		if(waps.updateList.length == 0){
        			malert('未有任何变化！','top','success');
        			return;
        		}
        		$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=updateYs&json=" + JSON.stringify(waps.updateList), function(json) {
  					console.log(json);
  					if(json.a=='0'){
  						malert('修改成功！','top','success');
  					}
  					waps.getData();
  				});
        	},
        	//批量删除
        	deleteData:function(){
        		console.log(waps.isChecked);
        		if(waps.isChecked.length == 0){
        			malert("请选择要删除的医生",'top','success');
        			return ;
        		}else{
        			for(i=0;i<waps.isChecked.length;i++){
        				if(waps.isChecked[i]){
        					waps.deleteList.push(waps.jsonList[i]);
        				}
        			}
        		}
        		if(waps.deleteList.length == 0){
        			malert("请选择要删除的医生",'top','success');
        			return ;
        		}
        		$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=deleteYs&json=" + JSON.stringify(waps.deleteList), function(json) {
  					console.log(json);
  					if(json.a=='0'){
  						malert('删除成功！','top','success');
  					}
  					waps.getData();
  					leftWaps.getData();
  				});
        	}
        },
        watch:{
        	'searchAll':function(){
        		console.log(wrapper.searchAll);
        		waps.param.parm = wrapper.searchAll;
        		console.log(waps.param.parm);
        		waps.getData();
        	}
        }
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShow:false,
            pcShow:false,
            jsShowtime:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:'',
            deleteOp:'',
        },
        methods:{
            //确定删除
        	delOk:function(){
        		var temp = [];
        		temp.push(pop.deleteOp);
        		$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=deleteYs&json=" + JSON.stringify(temp), function(json) {
  					console.log(json);
  					if(json.a=='0'){
  						malert('删除成功！','top','success');
  					}
  					pop.isShowpopL=false;
  	                pop.isShow=false;
  					waps.getData();
  				});
        	}

        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            pcShow:false,
            jsShowtime:false,
            jsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:'',
            isFold: false,
            parm:{
            	ysbm:'',
            	ysmc:'',
            	ly:'1',
            	pydm:'',
            	stop:'0'
            }
        },
        methods:{
        	change:function(){
        		wapse.parm.stop = wapse.parm.stop == '0' ?'1':'0';
        	},
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                //成功回调提示
                // malert('111','top','defeadted');
            },
            AddClose:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            } ,
            // //取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
               // malert('111','top','defeadted');

            },
            changeDown: function(event) {
    			this.keyCodeFunction(event, 'param', 'searchCon');
    			if(event.code == 'Enter' || event.code == 13) {
    				this.nextFocus(event);
    			}
    		},
            // //确定
            confirms:function () {
        		//2018/07/19添加状态值属性
                // wapse.parm.stop = wapse.parm.stop == '0' ?'1':'0';
                if(this.parm.stop){
                    this.parm.stop = '0'
                }else{
                    this.parm.stop = '1'
                }

            	//验证
            	if(typeof wapse.parm.ysmc == 'undefined' || wapse.parm.ysmc == null || wapse.parm.ysmc == ''){
            		malert("请输入医生姓名","top","defeadted");
            		return;
            	}
            	if(!this.parm.stop){
            		this.parm.stop = '0';
            	}
            	//添加
            	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=insertYs&param=" + JSON.stringify(wapse.parm), function(json) {
  					console.log(json);
  					if(json.a == "0"){
  	  					malert('添加成功！','top','success');
  	  					waps.getData();
  					}else{
  		                malert('添加失败！','top','defeadted');
  					}
  				});
            	
                // $(".side-form-bg").removeClass('side-form-bg')
                // $(".side-form").addClass('ng-hide');
            }

        },
    });
    var leftWaps = new Vue({
    	el:'#xmzb-content-left',
    	mixins: [dic_transform, baseFunc, tableBase],
    	data:{
    		jsonList:'',
    		isChecked:[],
    		param:{
            	page: 1,
    			rows: 10,
    			parm:'',
            },
    	},
    	methods:{
    		getData:function(){
    			$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=queryHisYs&param=" + JSON.stringify(leftWaps.param), function(json) {
    				console.log(json);
    				leftWaps.jsonList = json.d.list;
    			});
    		},
    		dbAdd:function (index) {
            	console.log(leftWaps.jsonList[index]);
            	waps.updateList.push(leftWaps.jsonList[index]);
            	waps.jsonList.push(leftWaps.jsonList[index]);
            	waps.jsonList[waps.jsonList.length - 1].stop = "0";
            	leftWaps.jsonList.splice(index,1);
            },
    	}
    });
    
    var waps=new Vue({
        el:'#xmzb-content-right',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isFold:false,
            title:'',
            centent:'',
            jsonList:'',
            isChecked:[],
            totlePage:'',
            param:{
            	page: 1,
    			rows: 10,
    			parm:'',
            },
            updateList:[],
            deleteList:[],
        },
        methods:{
            DelLine:function (data) {
            	pop.deleteOp = data;
                pop.isShow=true;
                pop.isShowpopL=true;
                pop.title='系统提示';
                pop.centent='确定删除该项内容吗？'
            },
            getData:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhKsys&types=queryYs&param=" + JSON.stringify(waps.param), function(json) {
  					console.log(json);
  					waps.jsonList = json.d.list;
  					waps.totlePage = Math.ceil(json.d.total / waps.param.rows);
  					console.log(waps.totlePage);
  					//初始化数据
  					waps.updateList = [];
  					waps.deleteList = [];
  					waps.isChecked = [];
  					waps.param.page = 1;
  					waps.param.rows = 10;
  					waps.param.parm = '';
  					wapse.param = {};
  				});
            },
        }
    });
    //初始化数据
    waps.getData();
    leftWaps.getData();
    
    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
    	if($(this).val() == '' || $(this).val() == null) {
    		$(this).addClass("emptyError");
    	} else {
    		$(this).removeClass("emptyError");
    	}
    });
    
})()