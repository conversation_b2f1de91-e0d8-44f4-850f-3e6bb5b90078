<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <script type="text/javascript" src="/newzui/pub/top.js"></script>
    <link href="sjxg.css" rel="stylesheet" type="text/css" />
<!--    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>-->
    <title>业务时间修改</title>
</head>
<body class="padd-t-10 padd-l-10 padd-r-10 padd-b-10 background-f skin-default">
<div class="yzcl_hs  height100 flex-container">
    <div class="left_tab1 printHide  padd-r-10   padd-b-10" style="width: 25%">
        <div class="searchLeft_tab1 ">
            <div class="flex-container flex-align-c">
                <span class="ft-14 padd-r-5 whiteSpace">选择科室：</span>
                <select-input @change-data="KsChange"
                              :child="allKs" :index="'ksmc'" :index_val="'ksbm'" :val="ksId"
                              :name="'ksId'" :search="true" >
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-t-10">
                <span class="whiteSpace ft-14 padd-r-5">搜&emsp;&emsp;索：</span>
                <input class="zui-input" @keydown.enter="getData()" type="text" placeholder="床位号，姓名" v-model="cwbh" />
            </div>
            <tabs :num="num" :tab-child="[{text:'待入院'},{text:'在院'},{text:'出院'},{text:'转院'}]" @tab-active="tabBg"></tabs>
        </div>
        <div class="zui-table-view flex-one padd-b-40 padd-r-10 flex-dir-c flex-container">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox  :list="'jsonList'" :type="'all'" :val="isCheckAll"></input-checkbox>
                        </th>
                        <th v-show="num!=0">
                            <div class="zui-table-cell cell-m">床号</div>
                        </th>
                        <th v-show="num==0">
                            <div class="zui-table-cell cell-s">类型</div>
                        </th>
                        <th>
                            <div class="zui-table-cell  cell-s">病人姓名</div>
                        </th>
                        <th>
                            <div class="zui-table-cell text-left cell-l">诊断名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell  cell-s">住院号</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body  over-auto"  @scroll="scrollTable">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr @click="hoverMouse(true,$index),queryPatient($index),queryYZ(item.zyh)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                        :tabindex="$index" v-for="(item, $index) in jsonList">
                        <td class="cell-m">
                            <input-checkbox @click.stop="checkSome($index),querySome()" @result="reCheckBox" :list="'jsonList'" :type="'one'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td  v-show="num!=0">
                            <div class="zui-table-cell cell-m title position" v-text="item.rycwbh"></div>
                        </td>
                        <td v-show="num==0">
                            <div class="zui-table-cell cell-s" v-text="jzklx_tran[item.jzklx]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l  text-left" v-text="item.ryzdmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s  " v-text="item.zyh"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="yzcl_hs"class="height100 skin-default">
        <div  class="height100">
            <div class="page_div yzcl height100"></div>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="sjxg.js"></script>
</html>
