var pram = {};
var ylbm = "003001004";
var ksbm='';
var sldh='';
var zyh='';
(function () {

    $(".zui-table-view").uitable();

    var toolBar=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{

            yfkfList: [],

            yfkf: 0, //药房库房信息
            param: {
                'page': 1,
                'rows': 10,
                'sort': 'ckd.ckdh,ypmc',
                'order': 'desc',
                'shzfbz': 1,
                'kfbm': '',
                'ksbm':'',
                'ckfs':'01',//01-出库
                'beginrq': null,
                'endrq': null,
                'parm':''
            },
            csqxList: [], //参数权限对象
            sldnum: 0,

            ksbm: null,
            lyksmc: null,


            barContent: {
                "dateBegin": getTodayDateBegin(),
                "dateEnd": getTodayDateEnd()
            },
            yfList: [],
            //zyksList:[],
            csList:[],
        },
        mounted: function () {
        	var begin = getTodayDateBegin();
        	var end = getTodayDateEnd();
        	laydate.render({
                elem: '#timeVal',
                eventElem: '.zui-date',
                value: begin + ' - ' + end,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                range: true,
                done: function (value, data) {
                	var time = value.split('-');
                	//"2018-04-01 00:00:00 - 2018-05-26 23:59:59"
                    // wrapper.param.time = value
                	tableInfo.param.beginrq = value.slice(0,19);
                	tableInfo.param.endrq = value.slice(22);
                    tableInfo.getData();
                }
            });
        },
        methods:{
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    tableInfo.getData();
                }

            },

            //刷新
            sx:function () {
                tableInfo.getData();
            },
            //获取药房
            getYfbm: function() {
                pram = {
                    "ylbm": ylbm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(pram), function(json) {
                    if(json.a == 0) {
                        toolBar.yfList = json.d.list;
                        Vue.set(toolBar.barContent,'yfbm',toolBar.yfList[0].yfbm);//默认第一个药房编码
                        Vue.set(toolBar.barContent,'yfmc',toolBar.yfList[0].yfmc);
                        tableInfo.getData();
                    } else {
                        malert("药房编码获取失败：" + json.c);
                    }
                });
            },
            //查询用例科室参数权限
           /* getCsqx: function() {
                //console.log(this.jsonList);
                pram = {
                    ylbm: ylbm,
                    ksbm: this.jsonList.ksbm
                };
                alert("getcsqx");
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(pram), function(json) {
                    if(json.a == 0) {
                        tableInfo.csList = json.d.list;
                        console.log(tableInfo.csList);
                    } else {
                        malert("科室权限获取失败：" + json.c);
                    }
                });
            },*/
/*            //申领科室选择
            Wf_KsChange: function() {
                var obj = event.currentTarget;
                var selected = $(obj).find("option:selected");
                toolbar.lyksmc = selected.text();
            },*/
            //页面加载时自动获取住院科室Dddw数据
           /* GetZyksData: function () {
                var bean = {"zyks": "1"};
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                    if (json.a == 0) {
                        toolBar.zyksList = json.d.list;
                        Vue.set(toolBar.barContent,'ryks',toolBar.zyksList[0].ksbm);//默认科室
                        console.log(toolBar.zyksList);
                        tableInfo.getData();
                    } else {
                        malert(json.c, "住院科室列表查询失败");
                        return;
                    }
                });
            },*/
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "ryks":
                        //先获取住院医生的值
                        Vue.set(this.barContent, 'ryks', val[0]);
                        Vue.set(this.barContent, 'ryksmc', val[4]);
                        tableInfo.getData();
                        break;
                    case "yfbm":
                        Vue.set(this.barContent, 'yfbm', val[0]);
                        Vue.set(this.barContent, 'yfmc', val[4]);
                        break;
                    default:
                        break;
                }
            },

        }
    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var tableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            person: [],
            sldh: null,
            csqxList: [], //参数权限对象
            isChecked: [],
            times:[],
            zyksList: [],//住院科室
            zyhList: [],
            open: [],
            sldnum: 0,
            ksList: [],
            lyksmc: null,
            ksbm: null,
            total: null,
            zhuangtai:{
                "0":'待摆药',
                "1":'未续发'
            },
            param: {
                page: 1,
                rows: 10,
                //sort: 'cfh',
                //order: 'asc',
                yfbm:'',
                beginrq: getTodayDateBegin(),
                endrq: getTodayDateEnd(),
                parm:''
            },
            jsonList: [], //药房对象
            ksbm : null
            //dataList: [],//申领单返回数据
        },

        methods: {
            //获取数据当前人信息
            getPerson: function(index) {
                tableMxInfo.title='摆药';
                tableMxInfo.contents='摆药';
                //申领单号
                tableInfo.sldh = tableInfo.jsonList[index].sldh;
                var obj = tableInfo.jsonList[index].hzxx;
                tableMxInfo.zyhList = [];
                for(var i = 0 ; i < obj.length ; i++){
                	tableMxInfo.zyhList.push({
						zyh: obj[i].zyh
					});
                }
                tableMxInfo.open();
                //tableMxInfo.zyhList.push({"zyh":zyh});
                console.log(tableMxInfo.zyhList);
                tableMxInfo.getData(sldh);
          },

            getYPInfo: function() {
                console.log(this.isChecked);
                tableMxInfo.getData();
            },
            //刷新摆药单
            getData: function() {
                common.openloading('.zui-table-view')
/*                pram = {};
                pram.yfbm = toolBar.barContent.yfbm;
                pram.beginrq =tableInfo.param.beginrq;
                pram.endrq =tableInfo.param.endrq;
                pram.cxrq='slsj';
                pram.slbz='1';
                pram.fybz='0';
                pram.pram=$("#jsvalue").val();
                pram.ksbm = toolBar.barContent.ryks;*/
            	Vue.set(tableInfo.param,'yfbm',toolBar.barContent.yfbm);
                $.getJSON('/actionDispatcher.do?reqUrl=YfbYfywBqby&types=sldcx&parm='+JSON.stringify(tableInfo.param),function(json) {
                    if(json.a == 0) {
                    	console.log(json.d.list);
                    	if(json.d.list && json.d.list.length >0 ){
                    		//科室编码
                    		tableInfo.ksbm = json.d.list[0].ksbm;
                    		var arry = json.d.list;
                    		var temp = [];
                    		for(var i = 0 ; i < arry.length ; i++){
                    			temp = arry[i].sldh;
                    			if(temp && temp.length > 0 ){
                    				for(var j = 0; j < temp.length ; j ++){
                    					tableInfo.jsonList.push(temp[j]);
                    				}
                    			}
                    		}
                    	}
                    	console.log(tableInfo.jsonList);
                    } else {
                        malert("申领摆药单查询失败：" + json.c)
                    }
                });
                common.closeLoading()
            },

            //双击查询明细
            edit: function(num) {
                //检索摆药单明细
                var bydmx = this.jsonList[num].hzxx;
                // tableMxInfo.jsonList = bydmx;
                toolBar.sldnum = num;
                console.log("num:" + toolBar.sldnum);

            },

        },


    });

    //配方信息
    var tableMxInfo = new Vue({
        el: '#brzcList',
        mixins: [dic_transform, tableBase, mformat],
        data: {
            jsonList: [],
            title:'',
            contents:'',
            times:[],
            moreShow:false,
            mxfyList:[],
            moreText:'',
            zkShow:true,
            sqShow:false,
            num:0,
            param: {
                page: 1,
                rows: 20,
                sort: 'mxxh',
                order: 'asc'
            },
            totlePage: 0,
            zyhList: []
        },
        methods: {
            closes:function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //展开
            zkMore:function (index) {
                this.num=index;
               if(index==0){
                   this.moreShow=true;
                   this.zkShow=false;
                   this.sqShow=true;
               }if(index==1){
                   this.moreShow=false;
                   this.zkShow=true;
                   this.sqShow=false;
               }
            },
            //获取处方列表
            getData: function(sldh) {
                var json = {
                    "list": this.zyhList
                };
                this.$http.post('/actionDispatcher.do?reqUrl=YfbYfywBqby&types=sldmxcx&yfbm=' + toolBar.barContent.yfbm + '&sldh=' + tableInfo.sldh, JSON.stringify(json))
                    .then(function(data) {
                        console.log(data.body);
                        if(data.body.a == 0) {
                            console.log(data.body.d.list);
                            this.mxfyList = data.body.d.list;
                            //this.times=data.body.d.list[0];
                        } else {
                            malert("申领单查询失败");
                        }
                    }, function(error) {
                        console.log(error);
                    });
            },
            //摆药
            bqby: function() {
                var obj = {};
                obj.yfbm = toolBar.barContent.yfbm;
                obj.yfmc = toolBar.barContent.yfmc;
                obj.sldh = tableInfo.sldh;
                obj.ksbm = tableInfo.ksbm;

                if(obj.sldh == undefined || obj.sldh == null || obj.sldh == "") {
                    malert("申领单号为空，不能进行发药!");
                    return;
                }
                if(obj.yfbm == undefined || obj.yfbm == null || obj.yfbm == "") {
                    malert("药房编码为空，不能进行发药!");
                    return;
                }
                if(obj.yfmc == undefined || obj.yfmc == null || obj.yfmc == "") {
                    malert("药房名称为空，不能进行发药!");
                    return;
                }
                if(obj.ksbm == undefined || obj.ksbm == null || obj.ksbm == "") {
                    malert("科室编码为空，不能进行发药!");
                    return;
                }


                this.$http.post('/actionDispatcher.do?reqUrl=YfbYfywBqby&types=bqby', JSON.stringify(obj))
                    .then(function(data) {
                        console.log(data.body);
                        if(data.body.a == 0) {
                            tableInfo.getData();
                            malert("发药保存成功");
                        } else {
                            malert(data.body.c);
                        }
                    }, function(error) {
                        console.log(error);
                    });
            }
        }
    });



    toolBar.getYfbm();
    //toolBar.GetZyksData();

})()
window.getTime = function(event, type) {
    if(type == 'star') {
        tableInfo.param.beginrq = $(event).val().slice(0,10);
    } else if(type == 'end') {
        tableInfo.param.endrq = $(event).val().slice(12,23);
    }
};





