<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>历史库存</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel ">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b">导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">库房</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label   margin-f-l10">时间段</label>
                    <div class="zui-input-inline   margin-f-l20">
                        <input class="zui-input todate wh240 text-indent20" placeholder="请选择申请日期" id="timeVal" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label   margin-f-l10">检索</label>
                    <div class="zui-input-inline margin-f-l35">
                        <input class="zui-input todate wh240 "  v-model="param.parm" @keydown.13="goToPage(1)" placeholder="请输入关键字"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料编码</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料种类</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>二级库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex}]">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypbm">材料收入</div></td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2" v-text="item.ypmc"></div>
                    </td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.ypgg">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kcsl">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.kcsl,2)">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.scph">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cdmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbl">材料收入</div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <div class="zui-table-fixed table-fixed-l" style="left: 10px;">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex}]" class="tableTr2">
                        <td><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>

<script src="lskc.js"></script>
</body>

</html>
