<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>压疮管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link type="text/css" href="evaluate.css" rel="stylesheet"/>
</head>
<body class="skin-default flex-container flex-dir-c flex-one">
<div class="wrapper background-f percent100">
<!---->
    <div class="evaluate-top" id="box" v-cloak>
        <div class="top-left" v-show="mlShow" @click="LeftRoll"><i class="top-left-r"></i><i class="top-left-r top-left-r1"></i></div>
        <div class="evaluate-box">
        <ul class="evaluate-list">
            <li v-for="(item,$index) in list" :class="{'active':$index==1}" @click="getData()">
                <span class="evaluate-span" v-text="item.text"></span>
                <span class="evaluate-span-btn" v-text="item.times"></span>
            </li>
        </ul>
        </div>
        <div class="top-right" v-show="mlShow" @click="RightRoll"><i class="top-right-r"></i><i class="top-right-r top-right-r1"></i></div>
        <button v-waves class="top-add iconfont icon-iocn42 icon-font25 icon-c5" v-show="evAdShow" @click="addMore"></button>

</div>

<div class="evaluate-content" v-cloak>
    <vue-scroll :ops="pageScrollOps">

    <h2 class="font-weight flex-center">压疮评估</h2>
    <div class="jbxx margin-b-15">
        <div class="jbxx-size">
            <div class="jbxx-position">
                <span class="jbxx-top"></span>
                <span class="jbxx-text">患者信息</span>
                <span class="jbxx-bottom"></span>
            </div>
            <div class="jbxx-box flex-start">
                <div class="top-form margin-b-10">
                    <label class="top-label">患者姓名</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182 background-f"/>
                        <!--带搜索的文本框,参考前面-->
                        <!--<search-table :message="searchCon" :selected="selSearch" :them="them"-->
                                      <!--:page="page" @click-one="checkedOneOut" @click-two="selectJbbm">-->
                        <!--</search-table>-->
                    </div>
                </div>
                <div class="top-form margin-b-10">
                    <label class="top-label">科&ensp;&ensp;&ensp;&ensp;室</label>
                    <div class="top-zinle">
                        <select-input class="wh182 background-f" @change-data="resultChange" :data-notEmpty="false"
                                      :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                      :name="'popContent.wxks'" :search="true">
                        </select-input>
                    </div>
                </div>
                <div class="top-form margin-b-10">
                    <label class="top-label">床&ensp;&ensp;&ensp;&ensp;号</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182 background-f"/>
                    </div>
                </div>
                <div class="top-form margin-b-10">
                    <label class="top-label">性&ensp;&ensp;&ensp;&ensp;别</label>
                    <div class="top-zinle">
                        <select-input class="wh182 background-f" @change-data="resultChange" :not_empty="false"
                             :child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"
                             :name="'popContent.brxb'">
                        </select-input>
                    </div>
                </div>
                <div class="top-form margin-b-10">
                    <label class="top-label">住&ensp;院&ensp;号</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182 background-f"/>
                    </div>
                </div>
                <div class="top-form margin-b-10">
                    <label class="top-label">诊&ensp;&ensp;&ensp;&ensp;断</label>
                    <div class="top-zinle">
                        <select-input class="wh182 background-f" @change-data="resultChange" :data-notEmpty="false"
                                      :child="zdList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                      :name="'popContent.wxks'" :search="true">
                        </select-input>
                    </div>
                </div>
                <div class="top-form margin-b-10">
                    <label class="top-label">时&ensp;&ensp;&ensp;&ensp;间</label>
                    <div class="top-zinle">
                        <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                        <input type="text" class="zui-input background-f times2 text-indent-20">
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="jbxx margin-b-15">
        <div class="jbxx-size">
            <div class="jbxx-position wh150">
                <span class="jbxx-top"></span>
                <span class="jbxx-text">压疮Braden评估</span>
                <span class="jbxx-bottom"></span>
            </div>
            <div class="jbxx-box flex-start">
                <dl class="evaluate-dl">
                    <dd>感觉</dd>
                    <dt>
                        <div class="c_radio">
                            <input type="radio" id="1" name="radio1"  value="1">
                            <label for="1"></label>
                            <label for="1" class="lb_text">丧失</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="2" name="radio1" value="2">
                            <label for="2"></label>
                            <label for="2" class="lb_text">严重丧失</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="2_1" name="radio1" value="3">
                            <label for="2_1"></label>
                            <label for="2_1" class="lb_text">轻度丧失</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="3" name="radio1" value="4">
                            <label for="3"></label>
                            <label for="3" class="lb_text">未受损害</label>
                        </div>
                    </dt>
                </dl>
                <dl class="evaluate-dl">
                    <dd>潮湿</dd>
                    <dt>
                        <div class="c_radio">
                            <input type="radio" id="4" name="radio2"  value="1" >
                            <label for="4"></label>
                            <label for="4" class="lb_text">持久浸湿</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="5" name="radio2" value="2">
                            <label for="5"></label>
                            <label for="5" class="lb_text">十分潮湿</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="6" name="radio2" value="3">
                            <label for="6"></label>
                            <label for="6" class="lb_text">偶尔潮湿</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="7" name="radio2" value="4">
                            <label for="7"></label>
                            <label for="7" class="lb_text">很少潮湿</label>
                        </div>
                    </dt>
                </dl>
                <dl class="evaluate-dl">
                    <dd>活动方式</dd>
                    <dt>
                        <div class="c_radio">
                            <input type="radio" id="8" name="radio3"  value="1">
                            <label for="8"></label>
                            <label for="8" class="lb_text">卧床</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="9" name="radio3" value="2">
                            <label for="9"></label>
                            <label for="9" class="lb_text">轮椅</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="10" name="radio3" value="3">
                            <label for="10"></label>
                            <label for="10" class="lb_text">扶助行走</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="11" name="radio3" value="4">
                            <label for="11"></label>
                            <label for="11" class="lb_text">经常行走</label>
                        </div>
                    </dt>
                </dl>
                <dl class="evaluate-dl">
                    <dd>行动能力</dd>
                    <dt>
                        <div class="c_radio">
                            <input type="radio" id="12" name="radio4"  value="1">
                            <label for="12"></label>
                            <label for="12" class="lb_text">完全不能移动</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="13" name="radio4" value="2">
                            <label for="13"></label>
                            <label for="13" class="lb_text">轮椅</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="14" name="radio4" value="3">
                            <label for="14"></label>
                            <label for="14" class="lb_text">扶助行走</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="15" name="radio4" value="4">
                            <label for="15"></label>
                            <label for="15" class="lb_text">经常行走</label>
                        </div>
                    </dt>
                </dl>
                <dl class="evaluate-dl">
                    <dd>营养</dd>
                    <dt>
                        <div class="c_radio">
                            <input type="radio" id="16" name="radio5"  value="1">
                            <label for="16"></label>
                            <label for="16" class="lb_text">严重不良</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="17" name="radio5" value="2">
                            <label for="17"></label>
                            <label for="17" class="lb_text">不良</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="18" name="radio5" value="3">
                            <label for="18"></label>
                            <label for="18" class="lb_text">中等</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="19" name="radio5" value="4">
                            <label for="19"></label>
                            <label for="19" class="lb_text">良好</label>
                        </div>
                    </dt>
                </dl>
                <dl class="evaluate-dl">
                    <dd>摩擦力/剪切力</dd>
                    <dt>
                        <div class="c_radio">
                            <input type="radio" id="20" name="radio6"  value="1">
                            <label for="20"></label>
                            <label for="20" class="lb_text">有</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="21" name="radio6" value="2">
                            <label for="21"></label>
                            <label for="21" class="lb_text">有潜在问题</label>
                        </div>
                        <div class="c_radio">
                            <input type="radio" id="22" name="radio6" value="3">
                            <label for="22"></label>
                            <label for="22" class="lb_text">无</label>
                        </div>
                    </dt>
                </dl>
                <div class="evaluate-value">
                    <span class="evaluate-height"><i class="font32"  v-if="total==0">0</i><i class="font32" v-if="total!=0" v-text="total"></i><small>分</small></span>
                    <span class="evaluate-height1">Braden评分</span>
                </div>
                <dl class="evaluate-dl padd-b-5 padd-t-5">
                    <dd>难免危险因素<i class="color-cff5 padd-l-5">(不可避免压疮条件：Braden评分≤14分+符合以下任何一条件都可申报)</i></dd>
                    <dt class="evaluate-dt">
                        <input id="nmBox1" class="green" type="checkbox" value="白蛋白﹤30g" >
                        <label for="nmBox1">白蛋白﹤30g</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox2" class="green" type="checkbox" value="高度水肿" >
                        <label for="nmBox2">高度水肿</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox3" class="green" type="checkbox" value="心力衰竭" >
                        <label for="nmBox3">心力衰竭</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox4" class="green" type="checkbox" value="呼吸衰竭" >
                        <label for="nmBox4">呼吸衰竭</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox5" class="green" type="checkbox" value="肝肾功能衰竭">
                        <label for="nmBox5">肝肾功能衰竭</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox6" class="green" type="checkbox" value="骨盆骨折" >
                        <label for="nmBox6">骨盆骨折</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox7" class="green" type="checkbox" value="手术后强迫体位">
                        <label for="nmBox7">手术后强迫体位</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox8" class="green" type="checkbox" value="手术过程强迫体位">
                        <label for="nmBox8">手术过程强迫体位</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox9" class="green" type="checkbox" value="极度消瘦">
                        <label for="nmBox9">极度消瘦</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox10" class="green" type="checkbox" value="脑猝中">
                        <label for="nmBox10">脑猝中</label>
                    </dt>
                    <dt class="evaluate-dt">
                        <input id="nmBox11" class="green" type="checkbox" value="其他">
                        <label for="nmBox11">其他</label>
                    </dt>
                </dl>
                <div class="top-form margin-t-15">
                    <label class="top-label">风险等级</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input" v-model="rank" disabled>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <!--
    低度风险（15-18分）：经常翻身、d
    最大限度活动、q
    如果是卧床或依靠轮椅，要使用床垫等减压设备、d
    保护足跟，潮湿的管理，营养的管理，摩擦力/剪切力的管理、q
    每周或病情变化时重新评估，并记录皮肤情况、dz
    健康教育及指导q

    中度危险（13-14分）：使用翻身计划表，Q2h翻身、z
    最大限度活动、q
    使用海绵垫，保证30度侧卧、zg
    使用床垫或椅垫等减压设备、z
    保护足跟，潮湿的管理，营养的管理，摩擦力/剪切力的管理、q
    每周或病情变化时重新评估，并记录皮肤情况、dz
    床旁挂警示牌、zg
    签风险告知书、zg
    健康教育及指导q

    高度危险（10-12分）：保证翻身频率，至少Q2h翻身、g
    增加小幅度的移位、g
    使用海绵垫，保证30度侧卧、zg
    最大限度活动、q
    保护足跟，潮湿的管理，营养的管理，摩擦力/剪切力的管理、q
    填写压疮高危险报表，上报护理部、g
    每天观察并记录皮肤状况，预防措施、g
    每天或病情变化时从新评估、g
    床旁挂警示牌、zg
    签风险告知书、zg
    请会诊、g
    健康教育及指导q

    非常危险（≤9分）：采取以上所有措施、
    使用体表压力缓释设备，当患者有不可控制的疼痛或翻身导致疼痛加剧时或其他危险因素存在、
    每班观察记录皮肤状况，预防措施，床边交接皮肤情况、
    每天记录评估值、
    气垫床不能代替翻身、
    健康教育及指导

    -->
    <div class="jbxx margin-b-15">
        <div class="jbxx-size">
            <div class="jbxx-position">
                <span class="jbxx-top"></span>
                <span class="jbxx-text">预防措施</span>
                <span class="jbxx-bottom"></span>
            </div>
            <div class="jbxx-box flex-start">
              <dl class="evaluate-dl padd-b-5">
                  <dd>预防措施</dd>
                  <!--ddShow：低度风险单有 zdShow：中度风险单有 gdShow:高度风险单有 dzShow：低中显 zgShow:中高显-->
                  <dt class="evaluate-dt"  v-show="ddShow">
                     <input id="checkbBox1" class="green" type="checkbox" value="经常翻身"    v-model="checkedNames">
                      <label for="checkbBox1">经常翻身</label>
                  </dt>
                  <dt class="evaluate-dt">
                      <input id="checkbBox2" class="green"  type="checkbox" value="最大限度活动" v-model="checkedNames">
                      <label for="checkbBox2">最大限度活动</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="ddShow">
                      <input id="checkbBox3" class="green"  type="checkbox" value="如果是卧床或依靠轮椅,要使用床垫等减压设备" v-model="checkedNames">
                      <label for="checkbBox3">如果是卧床或依靠轮椅,要使用床垫等减压设备</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="zgShow">
                      <input id="checkbBox4" class="green"  type="checkbox" value="床旁挂警示牌" v-model="checkedNames">
                      <label for="checkbBox4">床旁挂警示牌</label>
                  </dt>
                  <dt class="evaluate-dt">
                      <input id="checkbBox5" class="green"  type="checkbox" value="保护足跟，潮湿的管理，营养的管理，摩擦力/剪切力的管理" v-model="checkedNames">
                      <label for="checkbBox5">保护足跟，潮湿的管理，营养的管理，摩擦力/剪切力的管理</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="dzShow">
                      <input id="checkbBox6" class="green"  type="checkbox" value="每周或病情变化时重新评估，并记录皮肤情况" v-model="checkedNames">
                      <label for="checkbBox6">每周或病情变化时重新评估，并记录皮肤情况</label>
                  </dt>
                  <dt class="evaluate-dt">
                      <input id="checkbBox7" class="green"  type="checkbox" value="健康教育及指导" v-model="checkedNames">
                      <label for="checkbBox7">健康教育及指导</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="zdShow">
                      <input id="checkbBox8" class="green"  type="checkbox" value="使用翻身计划表，Q2h翻身" v-model="checkedNames">
                      <label for="checkbBox8">使用翻身计划表，Q2h翻身</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="zgShow">
                      <input id="checkbBox9" class="green"  type="checkbox" value="使用海绵垫，保证30度侧卧" v-model="checkedNames">
                      <label for="checkbBox9">使用海绵垫，保证30度侧卧</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="zgShow">
                      <input id="checkbBox10" class="green"  type="checkbox" value="床旁挂警示牌" v-model="checkedNames">
                      <label for="checkbBox10">床旁挂警示牌</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="zgShow">
                      <input id="checkbBox11" class="green"  type="checkbox" value="签风险告知书" v-model="checkedNames" >
                      <label for="checkbBox11">签风险告知书</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="gdShow">
                      <input id="checkbBox12" class="green"  type="checkbox" value="保证翻身频率，至少Q2h翻身" v-model="checkedNames">
                      <label for="checkbBox12">保证翻身频率，至少Q2h翻身</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="gdShow">
                      <input id="checkbBox13" class="green"  type="checkbox" value="增加小幅度的移位" v-model="checkedNames">
                      <label for="checkbBox13">增加小幅度的移位</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="gdShow">
                      <input id="checkbBox14" class="green"  type="checkbox" value="填写压疮高危险报表，上报护理部" v-model="checkedNames">
                      <label for="checkbBox14">填写压疮高危险报表，上报护理部</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="gdShow">
                      <input id="checkbBox15" class="green"  type="checkbox" value="每天观察并记录皮肤状况，预防措施" v-model="checkedNames">
                      <label for="checkbBox15">每天观察并记录皮肤状况，预防措施</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="gdShow">
                      <input id="checkbBox16" class="green"  type="checkbox" value="每天或病情变化时从新评估" v-model="checkedNames">
                      <label for="checkbBox16">每天或病情变化时从新评估</label>
                  </dt>
                  <dt class="evaluate-dt" v-show="gdShow">
                      <input id="checkbBox17" class="green"  type="checkbox" value="请会诊" v-model="checkedNames">
                      <label for="checkbBox17">请会诊</label>
                  </dt>
              </dl>
                <div class="percent100 color-cf3"  v-if="checkedNames.length!=0">
                <span v-for="(item,index) in checkedNames" class="padd-r-10">
                    {{index+1}}、{{checkedNames[index].replace('[','').replace(']','').replace(/\"/g,'')}}
                </span>
                </div>
            </div>
        </div>

    </div>
    <div class="jbxx">
            <div class="jbxx-size padd-t-20 padd-b-25">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">皮肤情况</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <div class="jbxx-box flex-start">
                    <div class="top-form"  style="width:70%">
                        <label class="top-label">皮肤情况</label>
                        <div class="top-zinle" style="width: 90%">
                            <input type="text" class="zui-input background-f">
                        </div>
                    </div>
                    <div class="top-form">
                        <label class="top-label">家属签字</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input background-f">
                        </div>
                    </div>
                </div>
            </div>

        </div>
    <!--不良事件上报-->
    <div class="jbxx margin-b-15" v-show="blShow">
        <span class="evaluate-close iconfont icon-iocn55 icon-cf icon-font14" @click="blClose"></span>

        <div class="jbxx-size padd-t-20 padd-b-25">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">不良事件上报</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <div class="jbxx-box flex-start">
                    <div class="top-form">
                        <label class="top-label">申报目的</label>
                        <div class="top-zinle flex-start">
                            <div class="c_radio">
                                <input type="radio" id="s_1" name="s_1"  value="1">
                                <label for="s_1"></label>
                                <label for="s_1" class="lb_text">备案</label>
                            </div>
                            <div class="c_radio">
                                <input type="radio" id="s_2" name="s_2"  value="2">
                                <label for="s_2"></label>
                                <label for="s_2" class="lb_text">备案+会诊</label>
                            </div>
                            <div class="c_radio">
                                <input type="radio" id="s_3" name="s_3"  value="3">
                                <label for="s_3"></label>
                                <label for="s_3" class="lb_text">申报不可避免压疮</label>
                            </div>
                        </div>
                    </div>
                    <div class="top-form">
                        <label class="top-label">压&ensp;&ensp;&ensp;&ensp;疮报告类型</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="resultChange" :data-notEmpty="false"
                                          :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                          :name="'popContent.wxks'" :search="true">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form">
                        <label class="top-label">压疮发生情况</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="resultChange" :data-notEmpty="false"
                                          :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                          :name="'popContent.wxks'" :search="true">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form flex-start margin-t-10 margin-l-10">
                        <label class="top-label" style="max-width: 160px;">事件带给患者的损害程度</label>
                        <div class="top-zinle">
                            <input type="text" value="" class="zui-input wh450  background-f"/>
                        </div>
                    </div>
                    <div  class="eval-wh100 flex-start" v-for="(i,index) in 2">
                        <div class="top-form flex-start margin-t-10">
                            <label class="top-label">压疮部位</label>
                            <div class="top-zinle">
                                <input type="text" value="" class="zui-input wh182 background-f"/>
                            </div>
                        </div>
                        <div class="top-form flex-start margin-t-10">
                            <label class="top-label">压疮分期</label>
                            <div class="top-zinle">
                                <select-input class="wh182 background-f" @change-data="resultChange" :data-notEmpty="false"
                                              :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                              :name="'popContent.wxks'" :search="true">
                                </select-input>
                            </div>
                        </div>
                        <div class="top-form margin-t-10" v-if="dqShow">
                            <label class="top-label">压疮大小</label>
                            <div class="top-zinle flex-start padd-r-10">
                                <span class="padd-r-5">长</span><input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent value="" class="zui-input wh82 background-f"/>
                                <i class="color-c1 position-cm">cm</i>
                            </div>
                            <div class="top-zinle flex-start padd-r-10">
                                <span class="padd-r-5">宽</span><input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent value="" class="zui-input wh82 background-f"/>
                                <i class="color-c1 position-cm">cm</i>
                            </div>
                            <div class="top-zinle flex-start padd-r-10">
                                <span class="padd-r-5">高</span><input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent value="" class="zui-input wh82 background-f"/>
                                <i class="color-c1 position-cm">cm</i>
                            </div>
                            <span class="evaluate-close iconfont icon-iocn55 icon-cf icon-ccs" @click="dqClose"></span>
                        </div>
                    </div>
                    <div class="padd-l-60">
                        <button v-waves class="root-btn background-f color-c1 border margin-t-5 margin-b-5 flex-start" @click="addYh" ><i class="iconfont icon-iocn42 icon-c1 icon-font20 "></i>添加一行</button>
                    </div>
                </div>
            </div>

    </div>
    <!--end-->
    <!--压疮转归-->
   <div class="jbxx" v-show="blShow">
       <span class="evaluate-close iconfont icon-iocn55 icon-cf icon-font14" @click="ycClose"></span>
            <div class="jbxx-size">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">压疮转归</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <div class="jbxx-box flex-start">
                    <div class="top-form">
                        <label class="top-label">出院日期</label>
                        <div class="top-zinle">
                            <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                            <input type="text" class="zui-input background-f times1 text-indent-20">
                        </div>
                    </div>
                    <div class="top-form">
                        <label class="top-label">类&ensp;&ensp;&ensp;&ensp;别</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="resultChange" :data-notEmpty="false"
                                          :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                          :name="'popContent.wxks'" :search="true">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form">
                        <label class="top-label">压&ensp;&ensp;&ensp;&ensp;疮治疗情况</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="resultChange" :data-notEmpty="false"
                                          :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                          :name="'popContent.wxks'" :search="true">
                            </select-input>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <!--end-->
    <div class="jbxx">
            <div class="jbxx-size">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">评估信息</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <div class="jbxx-box flex-start">
                    <div class="top-form">
                        <label class="top-label">评估护士</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="resultChange" :data-notEmpty="false"
                                          :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                          :name="'popContent.wxks'" :search="true">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form">
                        <label class="top-label">评估时间</label>
                        <div class="top-zinle">
                        <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                            <input type="text" class="zui-input background-f times text-indent-20">
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </vue-scroll>
</div>
    <div class="zui-table-tool flex-end">
        <button v-waves class="root-btn root-btn btn-parmary-d9">取消</button>
        <!--<button class="root-btn btn-parmary-9e" @click="clickYc">压疮转归</button>-->
        <button v-waves class="root-btn btn-parmary-f2a" @click="clickSb">上报</button>
        <button v-waves class="root-btn btn-parmary" @click="clickOk">确定</button>
    </div>
</div>
<script type="text/javascript" src="evaluate.js"></script>
</body>
</html>
