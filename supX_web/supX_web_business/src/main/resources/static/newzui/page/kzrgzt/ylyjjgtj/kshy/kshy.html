<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室会议</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../../kzrgzt/kshyjl/kshyjl.css">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link href="kshy.css" rel="stylesheet">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper background-f flex-container flex-dir-c flex-one">
    <div class="dyjl printShow" v-show="dyShow" v-cloak>
        <h2>科室会议记录</h2>
        <div class="dyjl-center">
            <div class="table-Department">
                <span class="padd-r-15">科室:<i class="color-c1 padd-l-5">消化内科</i></span>
                <span>会议时间:2020年12月12日10：00</span>
            </div>
            <div class="table-Department">
                <span class="padd-r-15">记录者:Henry</span>
                <span class="padd-r-15">主持人:Henry</span>
                <span>参会人员:李浩然，周丽君，boss</span>
            </div>
            <div class="table-Department">
                <span class="padd-r-15">审核人:Henry</span>
            </div>
        </div>
        <div class="kshyjl-theme">
            <h3 class="font16">会议主题今日会议主题</h3>
            <h4>一、传达护士长会议精神</h4>
            <p>1、关于综合绩效考核检查点评：</p>
            <p>1）、人力资源，床护比不够，新老搭配不合理；</p>
            <p>2）、技能考核，15项操作严格培训；</p>
            <p>3）、理论考的不理想、理论题库15000题；</p>
            <p>4）、操作平时要严抓，按时考核，心肺复苏95分及格医学|教育网|搜集整理，每人必过，不按时考试的扣钱，考核才允许进室内训练中心，注意按要求着装；</p>
            <p>2、人员在职在位，严格管控，按级请假；</p>
            <p>3、护士长、安全员进行科室安全排查（人员管理、毒麻、消防等），与每位护士谈一次话；</p>
            <p>4、等级医院评审抓紧准备，从今日开始，从一点一滴做起，从每个人做起，从平时做起，从常规做起，从每一个病种开始；</p>
            <p>5、被服、床单管理好，办公护士要看，工作人员不准带回家；</p>
            <p>6、护士长要公平对待护士，要懂法，知底线；</p>
            <p>7、12月考核内容：规章制度、科室护理常规、所有应急预案、应知应会、工作流程等医|学教育|网搜集整理；</p>
            <p>8、年终总结，科室总结，明年计划；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>9、论文上交（12月12日前）；</p>
            <p>10、新门诊12月8日开业典礼；</p>
        </div>
    </div>
    <div class="tong-top font-16 color-c3 font-weight flex-container flex-align-c printHide">科室会议统计</div>
    <div class="content printHide">
        <div class="tong-search">
                <div class="top-form">
                    <label class="top-label">时间段</label>
                    <div class="top-zinle flex-container">
                       <div class="zui-date position">
                           <i class="iconfont icon-icon61"></i>
                           <input type="text" class="zui-input wh122 dateStart" v-model="beginrq" />
                       </div>
                        <span class="flex-container flex-align-c padd-r-10 padd-l-10">至</span>
                        <div class="zui-date position">
                            <i class="iconfont icon-icon61"></i>
                            <input type="text" class="zui-input wh122 dateEnd" v-model="endrq"/>
                        </div>
                    </div>
                </div>
            </div>
        <div class="chart height-310">
            <!--<h2 class="text-center font-18 font-weight color-393f">科室医生会议次数统计</h2>-->
            <div class="flex-container height-310 flex-align-c">
                <div>
                    <div class="num num-border">
                        <span class="text-num">8</span><span class="text-line">次</span>
                    </div>
                    <div class="text color-393f text-center margin-t-10 font-16">科室会议总次数</div>
                </div>
                <div class="canvas" @load="ajaxChart($event)" id="canvas"></div>
               <div class="flex-container flex-dir-cr" style="height: 100%">
                   <ul class="ranking  margin-b-10">
                       <li class="flex-container whiteSpace flex-align-c" v-for="(list,index) in 22">
                           <div class="rankingNum rankingNumImg font-18 color-c7 text-center" :class="[index==0?'rankingOne':'',index==1?'rankingtwo':'',index==2?'rankingThree':'']">{{index>2?index+1:''}}</div>
                           <div class="rankingImg" style="background-image: url('/newzui/pub/image/2018072106.png')"></div>
                           <div class="rankingName">李浩然</div>
                           <div class="rankingNumber color-f38d4f">10次</div>
                       </li>
                   </ul>
               </div>
            </div>
        </div>
    </div>
   <div style="width: 100%;background: #f2f2f2;height: 8px;" class="printHide"></div>
    <div class="tong-top font-16 color-c3 font-weight flex-container flex-align-c printHide">科室会议列表</div>
    <div class="printHide zui-table-view flex-container flex-dir-c flex-one padd-l-10 padd-r-10  padd-t-10 " id="brRyList01">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m text-left"><span>序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl text-left"><span>会议主题</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xxl text-left"><span>会议内容</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>会议时间</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>参与人数</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>未参与人数</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>科室占比</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>状态</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <!-- v-if="jsonList.length!=0" -->
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in 22" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'some','jsonList'],$event)"
                    @dblclick="Listconfirm($index)"
                    class="tableTr2">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            {{$index}}
                        </div>
                    </td>
                    <td >
                        <div class="zui-table-cell cell-xl text-left">
                            会议主题会议主题会议主题
                        </div>
                    </td>
                    <td >
                        <div class="zui-table-cell cell-xxl title text-left">
                            会议内同会议内同会议内同会议内同会议内同会议内同会议内同
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            2017/12/12
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell color-f38d4f cell-s">
                            16人
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell color-f38d4f cell-s">
                            2人
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell color-4193e5 cell-s">
                            60%
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" :class="$index%2==0?'colr-ff6555':'color-2cb261'">
                            未确认
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-m flex-center">
                            <i class="iconfont icon-iocn12" data-title="审核" @click="unconfirmed($index)"></i>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
        </div>
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" data-no-change  @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in 22"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" @dblclick="Listconfirm($index)">
                        <td class="cell-s">
                            <div class="zui-table-cell cell-m flex-center" >
                                <i class="iconfont icon-iocn12" data-title="审核" @click="unconfirmed($index)"></i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!--科室会议记录-->
    <div id="pop" class="pophide printHide"  :class="{'show':popShow}" v-cloak>
        <div class="kshyjl-width" v-show="MeetShow" >
            <div class="kshyjl-close printHide" @click="popClose"><i class="iconfont icon-iocn55 icon-font25 icon-cf08"></i></div>
            <div class="kshyjl-title">科室会议记录</div>
            <div class="kshyjl-table">
                <div class="table-Department">
                    <span class="padd-r-15">科室:<i class="color-c1 padd-l-5">消化内科</i></span>
                    <span>会议时间:2020年12月12日10：00</span>
                </div>
                <div class="table-Department">
                    <span class="padd-r-15">记录者:Henry</span>
                    <span class="padd-r-15">主持人:Henry</span>
                    <span>参会人员:李浩然，周丽君，boss</span>
                </div>
                <div class="table-Department"  v-show="wtyShow">
                    <span class="padd-r-15">审核人:Henry</span>
                </div>
            </div>
            <div class="kshyjl-theme printShow">
                <vue-scroll :ops="pageScrollOps">
                    <h2>会议主题今日会议主题</h2>
                    <h3>一、传达护士长会议精神</h3>
                    <p>1、关于综合绩效考核检查点评：</p>
                    <p>1）、人力资源，床护比不够，新老搭配不合理；</p>
                    <p>2）、技能考核，15项操作严格培训；</p>
                    <p>3）、理论考的不理想、理论题库15000题；</p>
                    <p>4）、操作平时要严抓，按时考核，心肺复苏95分及格医学|教育网|搜集整理，每人必过，不按时考试的扣钱，考核才允许进室内训练中心，注意按要求着装；</p>
                    <p>2、人员在职在位，严格管控，按级请假；</p>
                    <p>3、护士长、安全员进行科室安全排查（人员管理、毒麻、消防等），与每位护士谈一次话；</p>
                    <p>4、等级医院评审抓紧准备，从今日开始，从一点一滴做起，从每个人做起，从平时做起，从常规做起，从每一个病种开始；</p>
                    <p>5、被服、床单管理好，办公护士要看，工作人员不准带回家；</p>
                    <p>6、护士长要公平对待护士，要懂法，知底线；</p>
                    <p>7、12月考核内容：规章制度、科室护理常规、所有应急预案、应知应会、工作流程等医|学教育|网搜集整理；</p>
                    <p>8、年终总结，科室总结，明年计划；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>9、论文上交（12月12日前）；</p>
                    <p>10、新门诊12月8日开业典礼；</p>
                </vue-scroll>

            </div>
            <div class="kshyjl-btn">
                <button class="root-btn btn-parmary-f2a" @click="Disagree" v-show="prShow==false">不同意</button>
                <button class="root-btn btn-parmary-f2a" @click="print" v-show="prShow">打印</button>
                <button class="root-btn btn-parmary" @click="Agree" v-show="prShow==false">同意</button>
            </div>
        </div>
        <div class="sjys-width" v-show="MeetShow==false">
            <div class="sjys-top">
                <span v-text="topTitle"></span>
                <span class="iconfont icon-iocn55 icon-cf056 icon-font20" @click="popClose"></span>
            </div>
            <div class="sjys-textarea">
                <textarea placeholder="请输入不同意建议"></textarea>
            </div>
            <div class="sjys-pop-btn sjys-pop-btn-t">
                <button class="root-btn btn-parmary-d9" @click="popClose">取消</button>
                <button class="root-btn btn-parmary" @click="popConfirm">确定</button>
            </div>
        </div>
    </div>
</div>
<script src="kshy.js" type="text/javascript"></script>
</body>
</html>
