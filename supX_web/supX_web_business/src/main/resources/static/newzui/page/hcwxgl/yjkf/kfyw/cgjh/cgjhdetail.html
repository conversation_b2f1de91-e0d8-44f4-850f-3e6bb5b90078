<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>采购计划</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
	<script type="application/javascript" src="tz.js"></script>
    <link type="text/css" href="cgjhdetail.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box" >
    <div class="wrapper background-f" id="wrapper" v-cloak :data-list="list">
        <div class="panel  printHide" >
            <div class="tong-top">
				
				<button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="cgSh()" v-show="isSh && !isCk">审核</button>
				<button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="sjcancel()">取消</button>
				<button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="addRow()"v-show=" !isSh && !isCk">增加行</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="saveFun()"v-show=" !isSh && !isCk">保存</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="sccg()" v-show="!isSh && !isCk">生成采购</button>
				
            </div>
			
        </div>
		
        <div class="zui-table-view  hzList">
            <div class="zui-table-header">
                <table class="zui-table table-width50" >
                    <thead>
                    <tr>

                        <th class="cell-m">
                            <div class="zui-table-cell cell-m">序号</div>
                        </th>
						<th>
						    <div class="zui-table-cell cell-xl">材料名称</div>
						</th>
                        <th v-for="(item,index) in table">
                            <div class="zui-table-cell " :class="item.className">{{item.text}}</div>
                        </th>
						<th class="cell-m">
							<div class="zui-table-cell cell-m" >操作</div>
						</th>
                    </tr>
                    </thead>
                </table>
            </div>
			<search-table :message="searchCon" :selected="selSearch" :page="page"
			              :them="them" :them_tran="them_tran" @click-one="checkedOneOut"
			              @click-two="selectOne1"></search-table>
            <div data-no-change  class="zui-table-body zuiTableBody height-300" id='zttable' @scroll="scrollTable($event)">
                <table class="zui-table table-width50" v-if="jsonList.length!=0">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        :tabindex="$index"
                        v-for="(item, $index) in jsonList" @click="getAllSj($index)" @keyup.delete="scmx($index)">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td>
                            <div class="zui-table-cell cell-xl">
                                <textarea rows="3" :id="'ypmc'+$index" class="zui-input zui-textarea" :disabled="disabled" v-model="item.ypbmmc" 
                                                                        @keydown="changeDown($index,$event,'searchCon')"
                                                                        @input="Wf_change(false,$index, $event.target.value,$event)"></textarea>
                            </div>
							</td>
                        <td v-for="(child,index) in table">
                            <div  class="zui-table-cell" :class="child.className" >
                                <span v-if="!child.inType" v-text="item[child.field]"></span>
                                <input v-else v-model="item[child.field]" @keydown="nextFocus1($event,child.field)"  class="zui-input"/>
                            </div>
                        </td>
						<td  class="cell-m">
							<div class="zui-table-cell cell-m" >
								<span class="icon-sc width30  title" v-if="!isSh && !isCk"  data-gettitle="删除" @click="scmx($index)"></span>
							</div>
							
						</td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
            </div>
			
			<!--右侧固定
			<div class="zui-table-fixed table-fixed-r" v-if="!isShowkd">
			    <div class="zui-table-header">
			        <table class="zui-table">
			            <thead>
			            <tr>
			                <th class="cell-m">
			                	<div class="zui-table-cell cell-m" >操作</div>
			                </th>
			            </tr>
			            </thead>
			        </table>
			    </div>
			    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
			        <table class="zui-table">
			            <tbody>
			            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
			                @mouseenter="hoverMouse(true,$index)"
			                @mouseleave="hoverMouse()"
			                :tabindex="$index"
			                v-for="(item, $index) in jsonList" @click="getAllSj($index)">
						<td  class="cell-m">
			                	<div class="zui-table-cell cell-m" >
			                		<span class="icon-sc width30  title" v-if="!isSh && !isCk"  data-gettitle="删除" @click="scmx($index)"></span>
			                	</div>
			                	
			                </td>
			            </tr>
			            </tbody>
			        </table>
			    </div>
			</div>
			-->
        </div>
        <div class="flex-container">
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">库存详情</div>
                </div>
                <div class="tab-card-body padd-t-10">
                    <div class="grid-box">
                        <div class="flex-container">
                            <vue-checkbox class="padd-r-10 cursor" @result="getReCheckYf" :new-text="'所有库房'" :val="'popContent.sykf'"  :new-value="popContent.sykf"></vue-checkbox>
                            <vue-checkbox class="padd-r-10 cursor" @result="getReCheckYf" :new-text="'来源药房'" :val="'popContent.lyyf'"  :new-value="popContent.lyyf"></vue-checkbox>
                            <vue-checkbox class="padd-r-10 cursor" @result="getReCheckYf" :new-text="'来源库房'" :val="'popContent.lykf'"  :new-value="popContent.lykf"></vue-checkbox>

                        </div>
                        <div class="zui-table-view    ">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50" >
                                    <thead>
                                    <tr>
										<th class="cell-m">
										    <div class="zui-table-cell cell-m">序号</div>
										</th>
                                        <th>
                                            <div class="zui-table-cell cell-s">库房</div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-s">可用数量</div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s">实际数量</div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body zuiTableBody kcxq" @scroll="scrollTable($event)" data-no-change>
                                <table class="zui-table table-width50" v-if="ypKcList.length!=0">
                                    <tbody>
                                    <tr :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                                        @mouseenter="switchIndex('hoverIndex1',true,$index)" @mouseleave="switchIndex()"
                                        @click="switchIndex('activeIndex1',true,$index)"
                                        :tabindex="$index"
                                        v-for="(item, $index) in ypKcList">
                                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                                        <td ><div class="zui-table-cell cell-s" v-text="item.kfmc"></div></td>
                                        <td ><div class="zui-table-cell cell-s title " v-text="item.sjsl"></div></td>
                                        <td ><div class="zui-table-cell cell-s title" v-text="item.qyzl"></div></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-card flex-one">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">近期采购计划执行情况</div>
                </div>
                <div class="tab-card-body padd-t-10">
                    <div class="grid-box">
                        <div class="flex-container padd-b-10 padd-t-10">
                            <vue-checkbox class="padd-r-10 cursor" @result="getReCheck" :new-text="'更多'" :val="'popContent.ypbz0'"  :new-value="popContent.ypbz0"></vue-checkbox>
                        </div>
                        <div class="zui-table-view    ">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50" >
                                    <thead>
                                    <tr>
                                        <th><div class="zui-table-cell cell-s">计划数量</div></th>
                                        <th><div class="zui-table-cell  cell-s">执行数量</div></th>
                                        <th><div class="zui-table-cell cell-s">计划类型</div></th>
                                        <th><div class="zui-table-cell cell-s">编制方法</div></th>
                                        <th><div class="zui-table-cell cell-s">编制人</div></th>
                                        <th><div class="zui-table-cell cell-s">编制日期</div></th>
                                        <th><div class="zui-table-cell cell-s">审核人</div></th>
                                        <th><div class="zui-table-cell cell-s">审核日期</div></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body zuiTableBody cgjhzxqk" @scroll="scrollTable($event)" data-no-change>
                                <table class="zui-table table-width50" v-if="ypCgHistoryList.length!=0">
                                    <tbody>
                                    <tr :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                                        @mouseenter="switchIndex('hoverIndex1',true,$index)" @mouseleave="switchIndex()"
                                        @click="switchIndex('activeIndex1',true,$index)"
                                        :tabindex="$index"
                                        v-for="(item, $index) in ypCgHistoryList">
                                        <td ><div class="zui-table-cell cell-s" v-text="item.cgsl"></div></td>
                                        <td ><div class="zui-table-cell cell-s  " v-text="item.zxsl"></div></td>
                                        <td ><div class="zui-table-cell cell-s " v-text="item.jhlx"></div></td>
                                        <td ><div class="zui-table-cell cell-s " v-text="item.bzff"></div></td>
                                        <td ><div class="zui-table-cell cell-s " v-text="item.zdrmc"></div></td>
                                        <td ><div class="zui-table-cell cell-s " v-text="fDate(item.zdrq,'date')"></div></td>
                                        <td ><div class="zui-table-cell cell-s " v-text="item.shrymc"></div></td>
                                        <td ><div class="zui-table-cell cell-s " v-text="fDate(item.shzfrq,'date')"></div></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">基础信息</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="grid-box flex-container">
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 wh80 text-right">金额合计</span>
                        <input class=" zui-input wh80" v-model="popContent.zjehj" disabled>
                    </div>
                   <div class="padd-r-10">
                       <div class="flex-container flex-align-c  padd-b-5">
                           <span class="ft-14 padd-r-5 wh80 text-right">编制人</span>
                           <input class=" zui-input wh160" disabled v-model="popContent.zdymc">
                       </div>
                       <div class="flex-container flex-align-c ">
                           <span class="ft-14 padd-r-5 wh80 text-right">编制日期</span>
                           <input class=" zui-input wh160" disabled v-model="popContent.zdrq">
                       </div>
                   </div>
                    <div class="padd-r-10">
                        <div class="flex-container flex-align-c padd-b-5">
                            <span class="ft-14 padd-r-5 wh80 text-right">审核人</span>
                            <input class=" zui-input wh160" disabled v-model="popContent.shrymc">
                        </div>
                        <div class="flex-container flex-align-c ">
                            <span class="ft-14 padd-r-5 wh80 text-right">审核日期</span>
                            <input class=" zui-input wh160" disabled v-text="fDate(popContent.shzfrq,'date')">
                        </div>
                    </div>

                </div>
                <div class="flex-container  padd-t-10 wh100MAx">
                    <span class="ft-14 padd-r-5 wh80 text-right">摘要</span>
                    <textarea class="ypStyle wh100MAx"  rows="3" cols="100" v-model="popContent.zdr"></textarea>
                </div>
            </div>
        </div>
        <model :s="'确定'" :c="'取消'" @default-click="getCGData" @result-clear="pkhShow=false" :model-show="true"
               @result-close="pkhShow=false" v-if="pkhShow" title="采购计划">
            <div class="bqcydj_model">
                <tabs :num="which" :tab-child="modelText" @tab-active="tabBgqh"></tabs>
                <div class="padd-t-5">
                   <div v-if="which == '0'">
                       <div class="tab-card">
                           <div class="tab-card-header">
                               <div class="tab-card-header-title font14">剂型</div>
                           </div>
                           <div class="tab-card-body padd-t-10">
                               <div class="grid-box">
                                   <vue-checkbox class="padd-r-10 padd-b-10 cursor" @result="getReCheckAll" :new-text="'全选'" :val="'popContent.All'"  :new-value="popContent.All"></vue-checkbox>
                                   <div class="flex-container flex-align-c flex-wrap-w padd-r-5 padd-b-10">
                                   <vue-checkbox v-for="(item,index) in ypjx"  class="padd-r-10 padd-b-10 cursor" @click.native="getReCheckOne(item,index,'jxNews','jxbm')" :new-text="item.jxmc" :val="'popContent.jxNews'"  :new-value="popContent.jxNews[index]"></vue-checkbox>
                                   </div>
                               </div>
                           </div>
                       </div>
                       <div class="tab-card">
                           <div class="tab-card-header">
                               <div class="tab-card-header-title font14">常备药</div>
                           </div>
                           <div class="tab-card-body padd-t-10">
                               <div class="grid-box flex-container flex-align-c flex-wrap-w">
                                   <label class="padd-r-10 flex-container ft-14 cursor flex-align-c"><input v-model="popContent.cyypfl" type="radio" name="cyypfl" value="">&ensp;不考虑是否常备药</label>
                                   <label class="padd-r-10 flex-container ft-14 cursor flex-align-c"><input v-model="popContent.cyypfl" type="radio" name="cyypfl" value="1">&ensp;仅提取常备药</label>
                                   <label class="padd-r-10 flex-container ft-14 cursor flex-align-c"><input v-model="popContent.cyypfl" type="radio" name="cyypfl" value="2">&ensp;仅提取非常备药</label>
                               </div>
                           </div>
                       </div>
                       <div class="tab-card">
                           <div class="tab-card-header">
                               <div class="tab-card-header-title font14">毒理分类</div>
                           </div>
                           <div class="tab-card-body padd-t-10">
                               <div class="grid-box flex-container flex-align-c flex-wrap-w">
                                   <vue-checkbox class="padd-r-10 cursor" @result="getReCheckTwo" :new-text="'提取普通药'" :val="'popContent.ypbz0'"  :new-value="popContent.ypbz0"></vue-checkbox>
                                   <vue-checkbox class="padd-r-10 cursor" @result="getReCheckTwo" :new-text="'提取毒性药'" :val="'popContent.dmypbz'"  :new-value="popContent.dmypbz"></vue-checkbox>
                                   <vue-checkbox class="padd-r-10 cursor" @result="getReCheckTwo" :new-text="'提取精神类药'" :val="'popContent.jslyp'"  :new-value="popContent.jslyp"></vue-checkbox>
                               </div>
                           </div>
                       </div>
                       <div class="tab-card">
                           <div class="tab-card-header">
                               <div class="tab-card-header-title font14">编织方法</div>
                           </div>
                           <div class="tab-card-body padd-t-10">
                               <div class="grid-box flex-container flex-align-c flex-wrap-w">
                                   <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.bzff" type="radio" name="bzff" value="0">&ensp;往年同期线性参照法</label>
                                   <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.bzff" type="radio" name="bzff" value="1">&ensp;临近区间平均参照法</label>
                                   <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.bzff" type="radio" name="bzff" value="2">&ensp;材料储备定额参照法</label>
                                   <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.bzff" type="radio" name="bzff" value="3">&ensp;材料日出库量参照法</label>
                                   <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.bzff" type="radio" name="bzff" value="4">&ensp;自定义区间参照法</label>
                               </div>
                           </div>
                       </div>
                       <div class="tab-card">
                           <div class="tab-card-header">
                               <div class="tab-card-header-title font14">计划类型</div>
                           </div>
                           <div class="tab-card-body padd-t-10">
                               <div class="grid-box flex-container flex-align-c flex-wrap-w">
                                   <span class="padd-r-5 text-right wh70">计划类型:</span>
                                   <label class="padd-r-10 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.jhlx" type="radio" name="jhlx" value="0">&ensp;周计划</label>
                                   <label class="padd-r-10 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.jhlx" type="radio" name="jhlx" value="1">&ensp;月计划</label>
                                   <label class="padd-r-10 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.jhlx" type="radio" name="jhlx" value="2">&ensp;季度计划</label>
                                   <label class="padd-r-10 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.jhlx" type="radio" name="jhlx" value="3">&ensp;年度计划</label>
                               </div>
                           </div>
                       </div>
                   </div>
                    <div v-if="which=='1'">
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title font14">材料用途</div>
                            </div>
                            <div class="tab-card-body padd-t-10">
                                <div class="grid-box">
                                    <vue-checkbox class="padd-r-10 padd-b-10 cursor" @result="getReCheckYpAll" :new-text="'全选'" :val="'popContent.ypAll'"  :new-value="popContent.ypAll"></vue-checkbox>
                                    <div class="flex-container flex-align-c flex-wrap-w padd-r-5 padd-b-10">
                                        <vue-checkbox v-for="(item,index) in ypzl"  class="padd-r-10 padd-b-10 cursor" @click.native="getReCheckOne(item,index,'ypzlNews','ypzlbm')" :new-text="item.ypzlmc" :val="'popContent.ypzlNews'"  :new-value="popContent.ypzlNews[index]"></vue-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="which=='2'">
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title font14">供应商</div>
                            </div>
                            <div class="tab-card-body padd-t-10">
                                <div class="grid-box">
                                    <vue-checkbox class="padd-r-10 padd-b-10 cursor" @result="getReCheckGysAll" :new-text="'全选'" :val="'popContent.gysAll'"  :new-value="popContent.gysAll"></vue-checkbox>
                                    <div class="flex-container flex-align-c flex-wrap-w padd-r-5 padd-b-10">
                                        <vue-checkbox v-for="(item,index) in ypcd"  class="padd-r-10 padd-b-10 wh30 cursor" @click.native="getReCheckOne(item,index,'ypcdNews','dwbm')" :new-text="item.dwmc" :val="'popContent.ypcdNews'"  :new-value="popContent.ypcdNews[index]"></vue-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="which=='3'">
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title font14">采购药房</div>
                            </div>
                            <div class="tab-card-body padd-t-10">
                                <div class="grid-box">
                                    <vue-checkbox class="padd-r-10 padd-b-10 cursor" @result="getReCheckYfAll" :new-text="'全选'" :val="'popContent.yfAll'"  :new-value="popContent.yfAll"></vue-checkbox>
                                    <div class="flex-container flex-align-c flex-wrap-w padd-r-5 padd-b-10">
                                        <vue-checkbox v-for="(item,index) in yfList"  class="padd-r-10 padd-b-10 wh30 cursor" @click.native="getReCheckOne(item,index,'yfbmNews','yfbm')" :new-text="item.yfmc" :val="'popContent.yfbm'"  :new-value="popContent.yfbmNews[index]"></vue-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="which=='4'">
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title font14">采购库房</div>
                            </div>
                            <div class="tab-card-body padd-t-10">
                                <div class="grid-box">
                                    <vue-checkbox class="padd-r-10 padd-b-10 cursor" @result="getReCheckKfAll" :new-text="'全选'" :val="'popContent.kfAll'"  :new-value="popContent.kfAll"></vue-checkbox>
                                    <div class="flex-container flex-align-c flex-wrap-w padd-r-5 padd-b-10">
                                        <vue-checkbox v-for="(item,index) in yfkfList"  class="padd-r-10 padd-b-10 wh30 cursor" @click.native="getReCheckOne(item,index,'kfbmNews','kfbm')" :new-text="item.kfmc" :val="'popContent.kfbm'"  :new-value="popContent.kfbmNews[index]"></vue-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </model>
    </div>
</div>
<script src="cgjhdetail.js"></script>
</body>

</html>
