window.yndryb_009_inta = new Vue({
    el: '#yndryb_009',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable,
    },
    data: {
        jbdmList: [],
        dqList: [],
        jzlxList: [],
        ryztList: [],
        zybclbList: [],
        cyList: [],
        searchCon: [],
        them_tran: {},
        zfb_tran:{},
        grxxJson: {}, // 个人信息对象
        dataJson: {},
        popContent: {},
        pageSelect: {
            page: 1,
            rows: 20,
            total: null
        },
        sfzsearch: '',
        ylhsearch: '',
        json: {
            bm:622921,
            kh: 1,
        },
        lyjson: {},
        jzjson: {},
        bcjson: {},
        selSearch: 0,
        bxlbbm: null,
        bxurl: null,
        searchval: '',
        them: {'疾病编码': 'jbbm', '疾病名称': 'zdmc'},
        myform: {},
        Tocx: {}
    },
    created: function () {
        yndryb_009_inta = this;
    },
    watch: {
        'json.kh': function (n, o) {
            if (n == 0) {
                this.ylhsearch = ''
                this.grxxJson = {}
                this.cyList = []
            } else {
                this.sfzsearch = ''
                this.grxxJson = {}
                this.cyList = []
            }
        }
    },
    mounted: function () {
        this.getbxlb();

    },
    methods: {
        cancel:function(){
            popTable.isShow = false;
        },
        confirm:function(){
            console.log(this.jzjson.jzbm)
            console.log(this.lyjson.ryqk)
            console.log(this.bcjson.bclb)
            this.grxxJson.jzlx = this.jzjson.jzbm
            this.grxxJson.ryqk = this.lyjson.rybm
            this.grxxJson.bclb = this.bcjson.bm
            wrapper.jtcyxxJson=this.grxxJson
            console.log("--->zygrxxJson", wrapper.jtcyxxJson)
            popTable.isShow = false;
        },
        checkedOneOut:function(index) {
            console.log("------->", index);
            var item = yndryb_009_inta.searchCon[index];
            console.log("------->item", item);
            if (index == null) {
                this.pageSelect.page++
                this.searching(true, this.grxxJson.zdmc);
            } else {
                console.log(arguments);
                this.grxxJson.jbdm = item.jbbm;
                this.grxxJson.zdmc = item.zdmc;
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },
        searching: function (add, val) {
            if (val == '') {
                return;
            }
            var param = {
                page: this.pageSelect.page,
                rows: 4,
                sort: "yljgbm",
                order: "asc",
                parm: val,
            }
            if (!add) this.pageSelect.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var dim = [];
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=basic&method=ypQuery&parm=" + JSON.stringify(param)+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    console.log("--->toltal", JSON.parse(json.d).total)
                    yndryb_009_inta.pageSelect.total = JSON.parse(json.d).total;
                    console.log("--->sum", yndryb_009_inta.page.total)
                    yndryb_009_inta.selSearch = 0;
                    if (add) {
                        for (var i = 0; i < JSON.parse(json.d).list.length; i++) {
                            yndryb_009_inta.searchCon.push(JSON.parse(json.d).list[i]);
                        }
                    } else {
                        yndryb_009_inta.searchCon = JSON.parse(json.d).list;
                    }
                    if (JSON.parse(json.d).list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                } else {
                    malert(json.c)
                }
            });
        },
        changeDown: function (event) {
            if (this.searchCon[this.selSearch] == undefined) return;
            this.inputUpDown(event, this.searchCon, 'selSearch')
            this.popContent = this.searchCon[this.selSearch]
            if (event.keyCode == 13) {
                console.log(this.popContent)
                this.grxxJson.zdmc = this.popContent.zdmc
                this.grxxJson.jbdm = this.popContent.jbbm
                this.$forceUpdate()
                this.nextFocus(event)
                $(".selectGroup").hide();
            }
        },
        ckIndex: function (key, type, index) {
            this.dataJson.cyxh = this.cyList[index].xh
            this.dataJson.brdqbm = this.grxxJson.brdqbm
            this.dataJson.ybkh = this.cyList[index].ybkh
            console.log(key, "-->", type, "--->", index, this.dataJson)

            this.getcyxx()
        },


        query: function (event) {
            if (event.keyCode == 13) {
                console.log("///bm", this.json.bm)
                if (this.json.bm == undefined) {
                    malert("请选择地区！");
                    return;
                }

                console.log("////", this.json);
                this.dataJson.brdqbm = this.json.bm;
                this.grxxJson.brdqbm = this.json.bm;
                console.log("////", this.dataJson);
                if (this.json.kh == 0) {
                    console.log("sfz--->", this.dataJson.sfzjhm);
                    if (this.sfzsearch == '') {
                        malert("请输入身份证号码！");
                        return;
                    }
                    this.dataJson.sfzjhm = this.sfzsearch;
                    this.dataJson.brxm = wrapper.zygrxxJson.brxm
                    this.grxxJson.sfzsearch = this.sfzsearch;
                    this.getxxBysfz();
                } else if (this.json.kh == 1) {
                    if (this.ylhsearch == '') {
                        malert("请输入医疗卡号！");
                        return;
                    }
                    this.dataJson.ybkh = this.ylhsearch;
                    this.grxxJson.ybkh = this.ylhsearch;
                    this.getjtcy();
                }
            }
        },
        getbxlb: function () {
            console.log(arguments);
            var param = {bxjk: "009"};
            common.openloading();
            var _this = this;
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            _this.bxlbbm = json.d.list[0].bxlbbm;
                            _this.bxurl = json.d.list[0].url;
                        }
                        common.closeLoading();
                        _this.readyData("dqbm", "dqList");
                        _this.readyData("jzlx", "jzlxList");
                        _this.readyData("ryzt", "ryztList");
                        yndryb_009_inta.grxxJson.brdqbm = '622921' //默认为临夏县
                        _this.zybclb();
                    } else {
                        malert("保险类别查询失败!" + json.c);
                        common.closeLoading();
                    }
                });
        },

        resultData: function (val) {
            console.log("///bm", this.json.bm)
            if (this.json.bm == undefined) {
                malert("请选择地区！");
                return;
            }
            console.log("----》", val[0])
            this.json.prm_aka130=val[0]
            var Tocx = document.getElementById("Ts");
            console.log("----Tocx", Tocx)
            if (val[0] == 1) {
                var result = Tocx.iReadSFZ();
            } else if (val[0] == 3) {
                // json.prm_aka130=val[0]
                var result = Tocx.GetCardInfo();
                var rs = Tocx.pOutInfo;
                this.dataJson.readCard = rs;
            }
            this.dataJson.brdqbm = this.json.bm;
            this.getjtcy();
        },
        dqresultChange:function(val){
            this.grxxJson.brdqbm = val[0];
            // Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.json.bm =val[0];
            console.log("---grxxJsonbrdqbm", this.grxxJson.brdqbm)
            this.$forceUpdate()
            this.zybclb();
        },

        readyData: function (types, listName) {
            // console.log(this, yndryb_009_inta);
            var param = {
                bxlbbm: this.bxlbbm
            };
            var yndryb_009_inta = this;
            console.log(this, yndryb_009_inta, yndryb_009_inta.bxurl);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yndryb_009_inta.bxurl + "&bxlbbm=" + yndryb_009_inta.bxlbbm + "&types=config&method=" + types + "&parm=" + JSON.stringify(param)+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    yndryb_009_inta[listName] = JSON.parse(json.d);
                    console.log(">>>>>", listName, yndryb_009_inta[listName]);
                    // if(types==dqbm){
                    //     yndryb_009_inta[zybclbList] = yndryb_009_inta.readyData("zybclb",zybclbList)
                    // }
                } else {
                    malert(json.c)
                }
            });
        },
        zybclb: function () {
            var param = {
                brdqbm: yndryb_009_inta.grxxJson.brdqbm
            };
            console.log(this, yndryb_009_inta, yndryb_009_inta.bxurl);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yndryb_009_inta.bxurl + "&bxlbbm=" + yndryb_009_inta.bxlbbm + "&types=config&method=" + "zybclb" + "&parm=" + JSON.stringify(param)+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    yndryb_009_inta["zybclbList"] = JSON.parse(json.d);
                    console.log(">>>>>listName", yndryb_009_inta["zybclbList"]);
                } else {
                    malert(json.c)
                }
            });
        },
        getcyxx: function () {
            console.log("----", yndryb_009_inta.dataJson);
            yndryb_009_inta.dataJson.mbbz='1'
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yndryb_009_inta.bxurl + "&bxlbbm=" + yndryb_009_inta.bxlbbm + "&types=sfdj&method=cyxx&parm=" + JSON.stringify(yndryb_009_inta.dataJson)+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>", json);
                if (json.a == "0") {
                    yndryb_009_inta.grxxJson = JSON.parse(json.d);
                    console.log(">>>>>成员grxxJson",  yndryb_009_inta.grxxJson);
                } else {
                    malert(json.c)
                }
            });
        },
        getjtcy: function () {
            console.log("----", yndryb_009_inta.dataJson);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yndryb_009_inta.bxurl + "&bxlbbm=" + yndryb_009_inta.bxlbbm + "&types=sfdj&method=read&parm=" + JSON.stringify(yndryb_009_inta.dataJson)+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>", json);
                if (json.a == "0") {
                    yndryb_009_inta.cyList = JSON.parse(json.d);
                    console.log(">>>>>成员list", yndryb_009_inta.cyList);
                } else {
                    malert(json.c)
                }
            });
        },
        getxxBysfz: function () {
            yndryb_009_inta.dataJson.brxm = wrapper.zygrxxJson.brxm
            console.log("----", yndryb_009_inta.dataJson);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yndryb_009_inta.bxurl + "&bxlbbm=" + yndryb_009_inta.bxlbbm + "&types=sfdj&method=ryzt&parm=" + encodeURI(JSON.stringify(yndryb_009_inta.dataJson))+"&time="+new Date().getTime(), function (json) {
                console.log(">>>>>", json);
                if (json.a == "0") {
                    yndryb_009_inta.grxxJson = JSON.parse(json.d);
                    console.log(">>>>>个人信息", yndryb_009_inta.grxxJson);
                } else {
                    malert(json.c)
                }
            });
        },
    },
})
//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

