var payNo = {
    data: {
        zflxList: [],
        jsjlContent: {},
        resObj: {},
        focus: false,
        codeNum: 0,
        codeContent: '',
    },
    computed: {
        codeContentFun: function () {
            if (this.codeContent.length == 18) {
                this.payment()
            } else if (this.codeNum > 3) {
                conBtu.ifClick = true;
                malert('你已经连续扫码错误三次，请重新确认', 'right', 'defeadted');
            } else if (this.codeNum < 3) {
                ++this.codeNum
            }
        },
    },
    methods: {
        blurFun: function (event) {
            if (this.focus && this.codeContent.length != 18) {
                $('#codeContent').focus();
            }
        },
        // 页面加载时自动获取支付类型数据
        GetZflxData: function () {
            var dg = {
                "page": 1,
                "rows": 20000,
                "sort": "",
                "order": "asc"
            };
            this.updatedAjax("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx&dg=" + JSON.stringify(dg), function (json) {
                if (json.a == 0 && json.d.list.length) {
                    this.zflxList = json.d.list;
                    this.jsjlContent.zflxbm = this.zflxList[0].zflxbm;
                } else {
                    malert(json.c + ",支付类型列表查询失败", 'right', 'defeadted');
                    return false;
                }
            });
        },
        closeFun: function () {
            this.bxShow = false;
            this.ifClick = true;
        },
        saveGh: function () {
            var zflxjk = this.listGetName(this.zflxList, this.jsjlContent.zflxbm, 'zflxbm', 'zflxjk');
            if (zflxjk == '008') {
                if (this.jsjlContent.zflxbm != '27') {
                    $('#codeContent').focus();
                    this.focus = true;
                    common.openloading("", "请出示付款码。。。。");
                } else {
                    this.payment()
                }
            } else {
                this.doSaveBrghFun()
            }
        },
        doSaveBrghFun: function () {
            if (!contextInfo.json.ghrq) {
                contextInfo.json.ghrq = $("#ghrq").val(); //手动给挂号日期赋值
            }
            contextInfo.json = Object.assign(contextInfo.json, this.resObj, this.jsjlContent)
            conBtu.doSaveBrgh()
        },
        payment: function () {
            this.resObj = {};
            var yylx = this.jsjlContent.zflxbm == '27' ? '00' : '02', resObj;
            var param = {
                czybm: userId,
                czyxm: userName,
                mzlx: this.mzlx,
                hisGrbh: '',
                bzsm: this.bzsm,
                inJson: {
                    yylx: yylx,
                    fyje: String(conBtu.totalMoney),
                    // fyje:'0.01',
                    zfcm: this.codeContent,
                    // yjyrq:"",
                    // yjyckh:"",
                    // ysddh:""
                },
                yljgbm: jgbm
            }
            param.inJson = JSON.stringify(param.inJson);
            this.postAjax("http://localhost:9001/posinterface/xf", JSON.stringify(param), function (json) {
                if (json.returnCode == 0) {
                    resObj = JSON.parse(json.outResult);
                    if (resObj.bank_code != '') {
                        malert(json.msgInfo, 'right');
                    } else {
                        malert(resObj.resp_chin, 'right', 'defeadted');
                        resObj = false;
                    }
                } else {
                    resObj = false;
                    malert(json.c, 'right', 'defeadted');
                    return false;
                }
            }, function () {
                common.closeLoading();
            });
            common.closeLoading();
            if (resObj) {
                if (this.jsjlContent.zflxbm == '27') {
                    this.resObj = {
                        yjyrq: resObj.txndate,//原交易日期
                        orderNo: resObj.refdata,//原交易参考号
                    };
                } else {
                    this.resObj = {
                        payNo: resObj.unionMerchant,//原交易参考号
                    };
                }
                this.doSaveBrghFun();
                this.focus = false;
            } else {
                conBtu.ifClick = true;
                return false;
            }

        },
    },
};
