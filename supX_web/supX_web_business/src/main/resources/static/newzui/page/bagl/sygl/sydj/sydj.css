.font12 {
  font-size: 12px ;
}
.font-13{
  font-size: 13px ;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}
.wh240 {
  width: 240px!important;
}
.wh182 {
  width: 182px !important;
}
.wh100 {
  width: 100px !important;
}
.wh66 {
  width: 66px !important;
}
.wh112 {
  width: 112px !important;
}
.wh120 {
  width: 120px !important;
}
.wh122 {
  width: 122px !important;
}
.wh138 {
  width: 138px !important;
}
.wh200 {
  width: 200px !important;
}
.wh220 {
  width: 220px !important;
}
.wh150 {
  width: 150px !important;
}
.wh1000 {
  width: 80% !important;
}
.wh50 {
  width: 50px !important;
}
.wh70 {
  width: 70px !important;
}
.width162 {
  width: 162px !important;
}
.wh160 {
  width: 160px !important;
}
.wh453 {
  width: 453px !important;
}
.wh247 {
  width: 243px !important;
  display: flex;
  justify-content: start;
  align-items: center;
}
.wh179 {
  width: 179px !important;
}
.wh59 {
  width: 59px !important;
}
.padd {
  padding: 0 !important;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-ed {
  background: #edf2f1 !important;
}
.color-green {
  color: #1abc9c !important;
  font-style: normal;
}
.color-dsh {
  color: #f3b169;
}
.color-ysh {
  color: #45e135;
}
.color-wtg {
  color: #ff4735;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.icon-widths:before {
  width: 26px;
  height: 26px;
  position: absolute;
  left: 33px;
  top: 0;
}
.icon-widthy:before {
  left: 0;
  top: 5px;
}
.icon-js:before {
  top: 4px;
}
.icon-width-t:before {
  top: -2px;
}
.xstz {
    position: absolute;
    right: -13px;
    top: 9px;
    width: 28px;
    color: rgb(26, 188, 156);
}
.choice-xs {
  background-image: linear-gradient(-180deg, #ffffff 0%, #e6e8e9 100%);
  border: 1px solid #dfe3e9;
  border-radius: 4px;
  width: 120px;
  height: 36px;
  display: flex;
  justify-content: center;
  color: #1abc9c;
  align-items: center;
  cursor: pointer;
}
.brjz-foot {
  position: fixed;
  right: 10px;
  left: 10px;
  background: #fff;
  height: 68px;
  z-index: 88;
  bottom: 0;
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
}
.brjz-root {
  /* justify-content: flex-end; */
}
.enter_tem1 {
  margin-top: 46px;
  border: 0;
  margin-left: 27px;
  padding: 0;
  margin-bottom: 3px;
}
.InfoMenu {
  width: 100%;
  box-shadow: 0 2px 5px #bbbbbb;
}
.InfoMenu div {
  width: 80px;
}
.enter_tem1_item {
  position: relative;
}
.selectGroup {
  top: 34px;
}
.infoIpt input {
  width: 160px;
}
.selectInput {
  width: auto;
}
.selectInput input {
  width: 100%;
}
.age .selectInput {
  width: calc(-90%);
}
.infoIpt {
  width: auto;
  display: inline-block;
  margin-right: 40px;
}
.infoIpt p {
  width: auto;
  padding-top: 5px;
}
.jbInfo {
  width: 100% !important;
}
.jbInfo input {
  width: 20%;
}
.jbInfo .selectInput {
  width: 20%;
}

.jbInfo ul {
  width: 200px;
  min-width: 30px;
}
.jbInfo {
  width: 100% !important;
}
.printPop {
  position: relative;
  display: inline-block;
  overflow: scroll;
  height: 100%;
  width: 100%;
  background-color: white;
}
.printContent > div >div {
  /*float: left;*/
  /*margin-right: 14px;*/
  margin-top: 3px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.printContent>div {
  display: flex;
  flex-wrap: wrap;
  width: 700px;
  margin: 0 auto;
  margin-top: 10px;
}
/*.printContent > div > div:last-child:not(span) {*/
/*  border-bottom: 1px solid rgba(0, 0, 0, 0.5019607843137255);*/
/*}*/
.printContent > div> div > div:last-child:not(span) {
  border-bottom: 1px solid #CCCCCC;
}
.printContent > div > span {
  display: block;
  float: left;
}
.printContent .values {
  border-bottom: 1px solid #CCCCCC;
}
.printContent hr {
  width: 98%;
  margin-top: 12px;
  border: 1px solid rgba(0, 0, 0, 0.5019607843137255);
}
.printContent .printTable {
  margin: 10px auto;
}
.printTable td {
  border: 1px solid rgba(0, 0, 0, 0.5019607843137255);
  /*border-left: none;*/
  border-bottom: none;
}
.printTable tr:first-child {
  text-align: center;
}
.fyDiv:first-child {
  width: 100px;
}
.fyDiv:last-child {
  border: 0;
  width: calc(0%);
}
.fyDiv div {
  border-bottom: 0 !important;
}
.printTableDiv {
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.5019607843137255);
  border-bottom:none;
  margin-top: -1px;
}
.printTableDiv > div {
  width: 100%;
  min-height: 28px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.5019607843137255);
  display: inline-block;
}
.printTableDiv span {
  display: block;
  float: left;
  height: 24px;
  margin-left: 4px;
}
.printTableDiv > div > div {
  float: left;
  text-align: left;
  /*padding: 0px 0;*/
}
/*.printTableDiv > div > div > span:first-child {*/
/*  font-weight: 600;*/
/*}*/
.selected {
  border: 1px solid rgba(0, 0, 0, 0.5019607843137255);
  width: 17px;
  height: 18px !important;
}
.contextSize {
  height: 8%;
}
.notEmptySign {
  right: -8px;
}
.selectInput span {
  right: -8px;
}
.info > div {
  width: 980px;
  height: 42px;
}
.tableFrame {
  height: auto !important;
}
.tableFrame table {
  border-collapse: collapse;
}
.tableFrame td,
.tableFrame th {
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.5019607843137255);
  padding: 9px;
}
.border{
  padding: 5px;
  border: 1px solid rgba(0, 0, 0, 0.5019607843137255);
}
/*.tableFrame span {*/
  /*display: block;*/
  /*float: left;*/
  /*margin-right: 10px;*/
/*}*/
.tableFrame input {
  display: block;
  float: left;
  margin-right: 10px;
}
.tableFrame .selectInput {
  height: 24px;
}
.spanMag {
  margin-bottom: -1px;
}
.spanMag span {
  margin-left: 16px;
}
.spanMag input {
  width: 100px;
}
.searchTable th,
.searchTable td {
  border: 1px solid #CCCCCC;
}
.patientTable td,
th {
  height: 22px !important;
  padding: 0.5px !important;
}
.blankTable {
  width: 96.1%;
  height: 70px;
  border: 1px solid rgba(0, 0, 0, 0.5019607843137255);
  margin-top: -2px ;
}
.jbxx-table {
  width: 100%;
  /*float: left;*/
  margin-top: 20px;
}
.jbxx-table tbody {
  border: 1px solid #d1d4da;
}
.jbxx-table tbody tr td {
  border: 1px solid #d1d4da;
  height: 36px;
}
.jbxx-table .height-input {
  border: none !important;
}
.jbxx-table .zui-input {
  border: none !important;
}
.dzcf-right-top {
  width: 100%;
  float: left;
  background: #edf2f1;
  height: 34px;
  line-height: 34px;
  color: #333;
}
.dzcf-right-top i {
  width: calc((100% - 50px)/6);
  display: block;
  text-align: center;
  float: left;
}
.dzcf-list li i {
  width: calc((100% - 50px)/6);
  display: block;
  text-align: center;
  float: left;
}
.dzcf-right-top i:first-child,
.dzcf-list i:first-child {
  width: 50px !important;
}
.dzcf-list {
  width: 100%;
  float: left;
  /*overflow: auto;*/
  /*max-height: 700px;*/
  border: 1px solid #eee;
}
.dzcf-list li {
  line-height: 40px;
  border-top: 1px solid #eee;
  height: 40px;
  width: 100%;
}
.dzcf-list li:hover {
  background: rgba(26, 188, 156, 0.08) !important;
}
.cf-right-list li i,
.cf-right-list i {
  width: calc((100% - 50px)/6);
}
.dzcf-list li:nth-child(2n) {
  background: #fdfdfd;
}
.pop-width420 {
  background: #fff;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.4);
  border-radius: 2px;
  width: 420px;
  padding: 20px;
  box-sizing: border-box;
}
.pop-width420 .bagl-sm {
  width: 100%;
  height: 48px;
  line-height: 48px;
  border: 1px solid #1abc9c;
  box-shadow: 0 0 8px 0 rgba(6, 140, 113, 0.4);
  border-radius: 4px;
  font-size: 22px;
  color: #1abc9c;
  letter-spacing: 2.75px;
  text-align: center;
}
.pop-width420 ::placeholder {
  color: #1abc9c !important;
}
.pop-width420 .icon-width:before {
  right: -10px;
  left: inherit;
  top: -15px;
  cursor: pointer;
}
.pop-width420 .bagl-smq {
  width: 100%;
  display: block;
  padding: 10px 0 10px 0;
  text-align: center;
}
.pop-width420 .bagl-smq img {
  width: 128px;
  height: 128px;
}
.pop-width420 .bagl-smq-text {
  width: 100%;
  display: block;
  float: left;
  text-align: center;
  font-size: 14px;
  color: #a2a7af;
}
.pop-width420 .bagl-js-text {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
}
.pop-width420 .bagl-js-text i {
  width: calc(100% / 4);
  display: block;
}
.pop-width420 .bagl-smcg {
  width: 128px;
  margin: 0 auto;
  height: 128px;
  transition: 0.1s ease-in-out;
  background: #d0eedc;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.pop-width420 .bagl-smcg img {
  width: 68px;
  height: 80px;
}
.pop-width420 .bagl-smcg .bagl-gou {
  position: absolute;
  background: #13a950;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  bottom: 0;
  right: 0;
  z-index: 1;
  animation: myfirst 0.5s;
  -moz-animation: myfirst 0.5s;
  /* Firefox */
  -webkit-animation: myfirst 0.5s;
  /* Safari 鍜� Chrome */
  -o-animation: myfirst 0.5s;
}
.pop-width420 .bagl-smcg .bagl-gou img {
  width: 16px;
  height: 14px;
}
.pop-width420 .bagl-smsb {
  background: #fcddd9;
}
.pop-width420 .bagl-smsb .bagl-cha {
  background: #f1543f;
}
.width100 {
  width: 100% !important;
}
.jygl-height {
  height: 120px !important;
  width: 99% !important;
  padding-top: 10px;
}
.icon-left34:before {
  left: 34px !important;
}
.icon-left58:before {
  left: 58px !important;
}
.icon-left16:before {
  left: 16px !important;
  top: 3px;
}
@keyframes myfirst {
  0% {
    right: 30px;
    opacity: 0.3;
  }
  70% {
    right: 15px;
    opacity: 0.7;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}
@-moz-keyframes myfirst {
  /* Firefox */
  0% {
    right: 30px;
    opacity: 0.3;
  }
  70% {
    right: 15px;
    opacity: 0.7;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}
@-webkit-keyframes myfirst {
  /* Safari 鍜� Chrome */
  0% {
    right: 30px;
    opacity: 0.3;
  }
  70% {
    right: 15px;
    opacity: 0.7;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}
@-o-keyframes myfirst {
  /* Opera */
  0% {
    right: 30px;
    opacity: 0.3;
  }
  70% {
    right: 15px;
    opacity: 0.7;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}
.icon-img::before{
  display: inline-block;
  width: 16px;
  height: 16px;
  background-size: 20px auto;
}
.fa{
  font-size: 16px;
}
.tabs-link .tabs-link-list.tabs-link-active{
color:#1abc9c
}
.ivu-tabs-ink-bar-active{
  background-color:#1abc9c
}
.printBtu{
  float: right;
}
.popJBxx{
  z-index: 100;
  position: fixed;
  width: 100%;
  height: 100%;
  text-align: center;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.498039);
  transition: opacity 0.3s ease;
}
.validate, .tipError{
  right: 0;
  z-index: 99;
  background-color:transparent;
}
.padd-r-2{
  padding-right: 2px;
}
.printFormat{
  font-family: '宋体';
}
.xydy{
	width: 710px; 
	margin: 0px auto;
	border: 1px solid #000000; 
	font-size: 3mm; 
	color: #000; 
	font-family: '微软雅黑 宋体 Arial';
}
.basyzt{
	font-size: 1.2rem;
	font-weight: bold;
	width: 100%;
}
.printHead{
	width: 710px;
	margin: 0 auto;
}

.jkkh{
	width: 188px;
	border-bottom: 1px solid #CCCCCC;
}
.values{
	width: 30px;
	border-bottom: 1px solid #CCCCCC;
}
.sybah{
	width: 90px;
	border-bottom: 1px solid #CCCCCC;
}
.syxm{
	width: 110px;
}
.sycsrq{
	width: 126px;
}
.synl{
	width: 60px;
}

.patientTable{
	border: 1px solid #000;
}
.patientTable th{
	border: 1px solid #000;
}
.patientTable td{
	border: 1px solid #000;
}
.baxm{
	width: 110px;
}
.csrq{
	width: 140px;
}
.banl{
	width: 60px;
}
.bagj{
	width: 100px;
}
.bayl{
	width: 50px;
}
.baxcstz{
	width: 60px;
}
.baxrytz{
	width: 55px;
}
.bacsd{
	width: 250px;
}
.bajg{
	width: 200px;
}
.bamz{
	width: 127px;
}
.basfz{
	width: 160px;
}
.baszy{
	width: 170px;
}
.baxzz{
	width: 350px;
}
.badh{
	width: 100px;
}
.bayb{
	width: 127px;
}
.bahkd{
	width: 478px;
}
.bagzdw{
	width: 266px;
}
.badwdh{
	width: 100px;
}
.balxm{
	width: 100px;
}
.balgx{
	width: 64px;
}
.baldz{
	width: 216px;
}
.baldh{
	width: 127px;
}
.baqtyljg{
	width: 100px;border-bottom: 1px solid #CCCCCC;display: inline-block;
}
.basj{
	width: 138px;
}
.yqq-lab{
	float: left;
}
.yqq-xhx{
	float: left;
}
.babf{
	width: 100px;
}
.bazkkb{
	width: 127px;
}
.basj{
	width: 138px;
}
.bakb{
	width: 150px;
}
.babf{
	width: 105px;
}
.basjzy{
	width: 107px;
}
.bamzzd{
	width: 247px;
}
.bajbzd{
	width: 250px;
}
.ryqkms{
	margin-right: 350px;
}
.baryzd{
	width: 347px;
}
.bajbzd{
	width: 150px;
}
.bazzqz{
	width: 138px;
}
.tbcyzd{
	min-width: 120px
}
.tbjbbm{
	min-width: 50px
}
.tbrybq{
	min-width: 50px
}
.tbcyqk{
	min-width: 50px
}
.ywgmdiv{
	width: 440px
}
.line1{
	width:100%;
	display: flex;
}
.linets{
	width:100%;
	display: flex;
}
.sszdwbyy{
	width: 200px;
}
.sszdjbbm{
	width: 100px;
}
.sswbyy{
	width:100px;
}
.ssjbbm{
	width:50px;
}
.ssqkyfxyw{
	width: 310px
}
.sycxsj{
	width: 160px
}
.lhyyzl{
	width: 160px
}
.ssjczbm{
	min-width: 60px
}
.ssjczrq{
	min-width: 50px
}
.ssssjb{
	min-width: 50px
}
.ssjczmc{
	min-width: 120px
}
.ssczjys{
	min-width: 120px
}
.qkyhdj{
	min-width: 40px
}
.ssmzfs{
	min-width: 50px
}
.ssmzys{
	min-width: 50px
}
.qtfyzl{
	width: 500px
}
.sslycxclfxhx{
	width: 100px
}
.zlyycxclfxhx{
	width: 100px
}
.jcyycxyyclxhx{
	width: 100px
}
.qdblzpxhx{
	width: 60px
}
.bdblzpfxhx{
	width: 60px
}
.xyhxyzplxhx{
	width: 60px
}
.zcyfxhx{
	width: 70px
}
.zccyfxhx{
	width: 70px
}
.kjywfyxhx{
	width: 70px
}
.xylxyfxhx{
	width: 70px
}
.zylzyfxhx{
	width: 70px
}
.kflkffxhx{
	width: 530px
}
.ssfxhx{
	width: 70px
}
.mzfxhx{
	width: 70px
}
.sszlfxhx{
	width: 70px
}
.lcwlzlfxhx{
	width: 70px
}
.fsszlxmfxhx{
	width: 70px
}
.lczdxmfxhx{
	width: 70px
}
.yyxzdfxhx{
	width: 70px
}
.ssyzdfxhx{
	width: 70px
}
.blzdfxhx{
	width: 70px
}
.zhqtfyxhx{
	width: 70px
}
.zhhlfxhx{
	width: 70px
}
.ybzlczfxhx{
	width: 70px
}
.zhybylfwfxhx{
	width: 70px
}
.qtzfxhx{
	width: 100px
}
.zfjexhx{
	width: 100px
}
.zyfyzfyxhx{
	width: 100px
}
.ysczyjgtszl{
	width: 300px
}
.sftybzzl{
	width: 280px
}
.ryqsjxhx{
	width: 30px
}
.wdkd{
	width: 40px
}
.swhs{
  background-color: red;
}