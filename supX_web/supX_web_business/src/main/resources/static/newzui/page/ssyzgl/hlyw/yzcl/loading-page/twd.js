var id,
    date,
    brxxContent;
var userInfo = new Vue({
    el: '#userInfo',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        brItem: [],
        brlistjson:{},//只用于接受请求LIST对象
        brList:[],
        yzList: [],
        jkList:[],
        yzshInfoList:[],//真正的列表
        zyhs:[],
        ksid:null,//科室编码
    },
    mounted: function () {
        this.moun();
        window.addEventListener('storage',function (e) {
            if( e.key == 'sance' && e.oldValue !== e.newValue ){
                userInfo.zyhs = [];
                userInfo.moun();
            }
        });
    },
    methods: {
        moun: function () {
            this.brlistjson=JSON.parse(sessionStorage.getItem("sance"));
            this.brList =this.brlistjson.brlist;
            this.brItem=this.brList;
            brxxContent = this.brItem;
            id= this.brItem.zyh;
            //判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
            if(this.brItem.nl<7&&this.brItem.brxb=='1'){
                this.brItem.nljd='1';
            }else if(this.brItem.nl<7&&this.brItem.brxb=='2'){
                this.brItem.nljd='2';
            }else if(this.brItem.nl<18&&this.brItem.nl>6&&this.brItem.brxb=='1'){
                this.brItem.nljd='3';
            }else if(this.brItem.nl<18&&this.brItem.nl>6&&this.brItem.brxb=='2'){
                this.brItem.nljd='4';
            }else if(this.brItem.nl<41&&this.brItem.nl>17&&this.brItem.brxb=='1'){
                this.brItem.nljd='5';
            }else if(this.brItem.nl<41&&this.brItem.nl>17&&this.brItem.brxb=='2'){
                this.brItem.nljd='6';
            }else if(this.brItem.nl<66&&this.brItem.nl>40&&this.brItem.brxb=='1'){
                this.brItem.nljd='7';
            }else if(this.brItem.nl<66&&this.brItem.nl>40&&this.brItem.brxb=='2'){
                this.brItem.nljd='8';
            }else if(this.brItem.nl>65&&this.brItem.brxb=='1'){
                this.brItem.nljd='9';
            }else if(this.brItem.nl>65&&this.brItem.brxb=='2'){
                this.brItem.nljd='10';
            }else{
                this.brItem.nljd='11';
            }

            this.ksid=this.brlistjson.ksid;
            this.zyhs = [{
                zyh: this.brList.zyh
            }];
        }
    }
});

var menu = new Vue({
    el: '#menu',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        num: 0
    },
    mounted: function () {

    },
    methods: {
        show: function (index) {
            if( index != this.num ){
                this.num = index;
                if( index == 0 ){
                    lr.isShow = true;
                    $("#twd-box").hide();
                }else if( index == 1 ){
                    lr.isShow = false;
                    $("#twd-box").show();
                    $("#twd").load('twdPrint.html');
                }
            }
        }
    }
});

var lr = new Vue({
    el: '#lr',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: true,
        lrTime: new Date(),
        item: 0,
        oneTwd: {},                         //第一个时间段
        twoTwd: {},                         //第二个时间段
        threeTwd: {},                       //第三个时间段
        fourTwd: {},                        //第四个时间段
        fiveTwd: {},                        //第五个时间段
        sixTwd: {},                         //第六个时间段
        twdQtjlContent: {},                 //体温单其他记录
        twdList: [],                        //病人体温单信息
        sfccsj:0,
    },
    mounted: function () {
        laydate.render({
            elem: '#lrTime',
            rigger: 'click',
            value: this.lrTime,
            theme: '#1ab394',
            done: function (value, data) {
                if( lr.lrTime != value ){
                    lr.lrTime = value;
                }
                lr.getData();
                lr.jssjhl();
            }
        });
    },
    methods: {
        closePage: function () { //关闭本页面
            this.updateHzlb();
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/twd.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        },
        updateHzlb: function () { // 刷新患者列表
            var x = parseInt( sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem( 'hszHzlbUpdate' , x );
        },
    	jssjhl:function(){
    		lr.sfccsj=0;
    		var starttime=lr.fDate(userInfo.brItem.ryrq,'date');
    		var endtime=$("#lrTime").val();
    		var start = new Date(starttime.replace("-", "/").replace("-", "/"));  
    		var end = new Date(endtime.replace("-", "/").replace("-", "/"));  
    		if(end<start){
                malert('只能填写入院之后的数据','top','defeadted');
    			lr.sfccsj=1;
    		}   
    	},
    	gethztwlist:function(){
    		  var zyh = userInfo.brItem['zyh'];
              var ryksbm = userInfo.brItem['ryks'];
              var yebh = userInfo.brItem['yebh'];
              var clrq = $("#lrTime").val();
              if (zyh != null && ryksbm != undefined && ryksbm != null) {
                  var parm = {
                      zyh: zyh,
                      ryksbm: ryksbm,
                      yebh: yebh,
                      clrq: clrq
                  };
                  //请求后台查询病人体温单信息
                  $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryByOneBr&parm=' + JSON.stringify(parm),
                      function (json) {
                          if (json.a == 0) {
                              if (json.d.list[0] != undefined) {
                                  lr.oneTwd = json.d.list[0];
                              }
                              if (json.d.list[1] != undefined) {
                            	  lr.twoTwd = json.d.list[1];
                              }
                              if (json.d.list[2] != undefined) {
                            	  lr.threeTwd = json.d.list[2];
                              }
                              if (json.d.list[3] != undefined) {
                            	  lr.fourTwd = json.d.list[3];
                              }
                              if (json.d.list[4] != undefined) {
                            	  lr.fiveTwd = json.d.list[4];
                              }
                              if (json.d.list[5] != undefined) {
                            	  lr.sixTwd = json.d.list[5];
                              }
                              lr.oneTwd['clsd'] = '1';
                              lr.twoTwd['clsd'] = '2';
                              lr.threeTwd['clsd'] = '3';
                              lr.fourTwd['clsd'] = '4';
                              lr.fiveTwd['clsd'] = '5';
                              lr.sixTwd['clsd'] = '6';
                              Vue.set(lr.oneTwd, 'tbsmsj', lr.fDate(lr.oneTwd.tbsmsj, "time"));
                              Vue.set(lr.twoTwd, 'tbsmsj', lr.fDate(lr.twoTwd.tbsmsj, "time"));
                              Vue.set(lr.threeTwd, 'tbsmsj', lr.fDate(lr.threeTwd.tbsmsj, "time"));
                              Vue.set(lr.fourTwd, 'tbsmsj', lr.fDate(lr.fourTwd.tbsmsj, "time"));
                              Vue.set(lr.fiveTwd, 'tbsmsj', lr.fDate(lr.fiveTwd.tbsmsj, "time"));
                              Vue.set(lr.sixTwd, 'tbsmsj', lr.fDate(lr.sixTwd.tbsmsj, "time"));
                          } else {
                              malert(json.c,'top','defeadted');
                              return false;
                          }
                      });
                  //请求后台查询体温单其他记录信息
                  $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=select&parm=' + JSON.stringify(parm),
                      function (json) {
                          if (json.a == 0) {
                              //给体温单其他记录对象赋值
                              if (json.d != null) {
                            	  lr.twdQtjlContent = json.d;
                              }
                          } else {
                              malert(json.c,'top','defeadted');
                              return false;
                          }
                      });
              }
    	},
        //刷新
        getData: function () {
            lr.twdList = [];//病人体温单信息
            lr.oneTwd = {};//第一个时间段
            lr.twoTwd = {};//第二个时间段
            lr.threeTwd = {};//第三个时间段
            lr.fourTwd = {};//第四个时间段
            lr.fiveTwd = {}; //第五个时间段
            lr.sixTwd = {};//第六个时间段
            lr.gethztwlist();
        },
        //保存
        save: function () {

			if(lr.sfccsj==1){
                malert('只能填写入院之后的数据','top','defeadted');
			    return false;
			}   
        	lr.twdList = [];
            var zyh = userInfo.brItem.zyh;
            var ryksbm = userInfo.brItem.ryks;
            var yebh =userInfo.brItem.yebh;
            var clrq = $("#lrTime").val();
            if (zyh != undefined && zyh != null && ryksbm != undefined && ryksbm != null && clrq != null) {
                //处理体温单集合
                if ($("#oneTime").val() != null && $("#oneTime").val() != "") {
                	lr.oneTwd['tbsmsj']='1970-01-01 '+$("#oneTime").val()+':00';
                }
                if ($("#twoTime").val() != null && $("#twoTime").val() != "") {
                	lr.twoTwd['tbsmsj']='1970-01-01 '+$("#twoTime").val()+':00';
                }
                if ($("#threeTime").val() != null && $("#threeTime").val() != "") {
                	lr.threeTwd['tbsmsj'] ='1970-01-01 '+$("#threeTime").val()+':00';
                }
                if ($("#fourTime").val() != null && $("#fourTime").val() != "") {
                	lr.fourTwd['tbsmsj'] ='1970-01-01 '+$("#fourTime").val()+':00';
                }
                if ($("#fiveTime").val() != null && $("#fiveTime").val() != "") {
                	lr.fiveTwd['tbsmsj'] ='1970-01-01 '+$("#fiveTime").val()+':00';
                }
                if ($("#sixTime").val() != null && $("#sixTime").val() != "") {
                	lr.sixTwd['tbsmsj']='1970-01-01 '+$("#sixTime").val()+':00';
                }

                lr.oneTwd['clrq'] = clrq;
                lr.twoTwd['clrq'] = clrq;
                lr.threeTwd['clrq'] = clrq;
                lr.fourTwd['clrq'] = clrq;
                lr.fiveTwd['clrq'] = clrq;
                lr.sixTwd['clrq'] = clrq;
                lr.oneTwd['zyh'] = zyh;
                lr.twoTwd['zyh'] = zyh;
                lr.threeTwd['zyh'] = zyh;
                lr.fourTwd['zyh'] = zyh;
                lr.fiveTwd['zyh'] = zyh;
                lr.sixTwd['zyh'] = zyh;
                lr.oneTwd['ryksbm'] = ryksbm;
                lr.twoTwd['ryksbm'] = ryksbm;
                lr.threeTwd['ryksbm'] = ryksbm;
                lr.fourTwd['ryksbm'] = ryksbm;
                lr.fiveTwd['ryksbm'] = ryksbm;
                lr.sixTwd['ryksbm'] = ryksbm;
                lr.oneTwd['ryksmc'] = userInfo.brItem['ryksmc'];
                lr.twoTwd['ryksmc'] = userInfo.brItem['ryksmc'];
                lr.threeTwd['ryksmc'] = userInfo.brItem['ryksmc'];
                lr.fourTwd['ryksmc'] = userInfo.brItem['ryksmc'];
                lr.fiveTwd['ryksmc'] = userInfo.brItem['ryksmc'];
                lr.sixTwd['ryksmc'] = userInfo.brItem['ryksmc'];
                lr.oneTwd['yebh'] = userInfo.brItem['yebh'];
                lr.twoTwd['yebh'] = userInfo.brItem['yebh'];
                lr.threeTwd['yebh'] = userInfo.brItem['yebh'];
                lr.fourTwd['yebh'] = userInfo.brItem['yebh'];
                lr.fiveTwd['yebh'] = userInfo.brItem['yebh'];
                lr.sixTwd['yebh'] = userInfo.brItem['yebh'];
                lr.twdList.push(lr.oneTwd);
                lr.twdList.push(lr.twoTwd);
                lr.twdList.push(lr.threeTwd);
                lr.twdList.push(lr.fourTwd);
                lr.twdList.push(lr.fiveTwd);
                lr.twdList.push(lr.sixTwd);
                //处理体温单其他记录
                lr.twdQtjlContent['clrq'] = clrq;
                lr.twdQtjlContent['zyh'] = zyh;
                lr.twdQtjlContent['ryksbm'] = ryksbm;
                lr.twdQtjlContent['ryksmc'] = userInfo.brItem['ryksmc'];
                lr.twdQtjlContent['yebh'] = userInfo.brItem['yebh'];
                //拼接对象
                //var json = '{"list": {"twbqtjlModel":' + JSON.stringify(right.twdQtjlContent) + ',"twbList":' + JSON.stringify(right.twdList) + '}}';
                var json = '{"twbqtjlModel":' + JSON.stringify(lr.twdQtjlContent) + ',"twbList":' + JSON.stringify(lr.twdList) + '}';
                //请求后台
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=save',
                    json).then(function (data) {
                    if (data.body.a == 0) {
                        malert('上传数据成功','top','success');
//                        left.queryPatient(ischeck);
                        lr.getData();
                    } else {
                        malert(data.body.c,'top','defeadted')
                    }
                }, function (error) {
                    console.log(error);
                });
            } else {
                malert('请选择相关病人','top','defeadted')
            }
        },
        print: function () {
    	    window.print();
        }
    }
});


laydate.render({
    elem: '#time',
    rigger: 'click',
    value: new Date(),
    theme: '#1ab394',
    done: function (value, data) {
        date = time2(value);
        if(param&&other){
            param.sxrq = date;
            other.getOther();
        }
    }

});

function time2(val) {
    var sa = val.split('-');
    return sa[0] + '-' + sa[1] + '-' + (parseInt(sa[2]) + parseInt(1));
}

function printFn() {
    var time = setTimeout(function () {
        $("body,html").css({
            "min-width": "0px"
        });
        document.querySelector(".wrapper").style.border = "none";
        window.print();
        $("body,html").css({
            "min-width": "1103px"
        });
        clearTimeout(time);
    },20);
}



