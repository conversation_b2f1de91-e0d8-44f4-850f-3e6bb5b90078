@import "../../../../css/baseColor";
.icon-hs:before{
  color:#757c83;
}
.icon-bj:before{
  top:-2px;
  left:22px;
}
.icon-sh-h:before{
  top: 0;
  left: 15px;
}
.userNameBg{
  width: 100%;
  height:100px;
  position: relative;
  background-size: cover;
  background: url("/newzui/pub/image/userImg.png") center right no-repeat #708f89;
  .bazs-flex{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 15px 0 20px 20px;
  }
  .bazs-maid{
    width: 68px;
    height: 68px;
    overflow: hidden;
    display: block;
    img{
      width:68px;
      height: 68px;
      border-radius: 50%;
    }
  }
  .bazs-name{
    display: block;
    color: @colorff;
    width: auto;
    padding-left: 20px;
    p{
      line-height: 22px;
      em{
        font-size:22px;
        padding-right: 30px;
      }
      i{
        padding-right: 30px;
        font-size:@font14;
        color: rgba(255,255,255,0.8);
      }
    }
  }
}
.bazs-time{
  width: 100%;
  float: left;
  padding: 30px 0 0 23px;
  background: @colorff;
  min-height:668px;
  .bazs-left-time{
    ul{
      li{
        width:100%;
        float: left;
        min-height: 70px;
        .bazs-now{
          background-image:linear-gradient(-1deg, #1ebe9e 2%, #30edc7 98%);
          width:18px;
          height:18px;
          border-radius:100%;
          display: block;
          float: left;
          position: relative;
          margin-top: 2px;
          &:after{
            content: '';
            width: 2px;
            height: 60px;
            position: absolute;
            left: 8px;
            top: 18px;
            background:#f3f4f6;
          }
        }
        &:last-child .bazs-now:after{
          background: none;
        }
        .bazs-ggg{
          background: url("../../../../css/@{image}<EMAIL>") center left no-repeat;
          background-size:18px 18px;
        }
      }
    }
    .bazs-right-list{
       margin-left:30px;
      float: left;
    }
  }
}
.tong-search{
  padding-bottom: 5px;
}
.ti-close:before{
  color: #1ab160;
  font-size: 14px;
}