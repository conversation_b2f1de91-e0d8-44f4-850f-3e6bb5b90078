var wrapper = new Vue({
    el: '.panel',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        isShowpopL: false,
        isTabelShow: false,
        isShow: false,
        keyWord: '',
        popContent: {},
        title: '',
        LcList: {
            lcsjzid: ''
        },
        zYList: [],
        totle: '',
        num: 0,
        param: {
            page: '',
            rows: '',
            total: ''
        },
        rybmList: [],
    },
    methods: {
        //扫码接收
        smJS: function () {
            pop.popShow = true;
            pop.smShow = true;
            pop.cgShow = false;
            pop.sbShow = false;
            pop.ShowTitle = '请使用扫码枪进行扫码'
        },

        //检索查询回车键
        searchHc: function () {
            if (window.event.keyCode == 13) {
                yjkmtableInfo.getData();
            }

        },
        //审核状态
        yyrSelect: function () {
            this.param.rows = 20000;
            this.param.sort = '';
            this.param.order = '';
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg=" + JSON.stringify(this.param), function (json) {
                wrapper.rybmList = json.d.list;
            });
        },
    }
});
wrapper.yyrSelect();

//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
var saves = null;

//科目
var yjkmtableInfo = new Vue({
    el: '.zui-table-view',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popContent: {},
        jsonList: [],//
        iShow: false,
        isShowpopL: false,
        totlePage: 0,
        total: '',
        page: '',
        kmbm: '',
        LcList: [],
        kmmc: '',
        rows: 10,
        param: {
            page: 1,
            rows: 10,
            sort: '',
            order: 'asc',
            parm: '',
        },
        zhuangtai: {
            "0": '待接收',
            "1": '待审核',
            "2": '已审核'
        }

    },
    methods: {
        //加载获取列表数据
        getData: function () {
            if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                this.param.parm = $("#jsvalue").val();
            } else {
                this.param.parm = '';
            }
            $.getJSON("/actionDispatcher.do?reqUrl=EmrXtwhZdyzsk&types=query&parm=" + JSON.stringify(this.param), function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    yjkmtableInfo.totlePage = Math.ceil(json.d.total / yjkmtableInfo.param.rows);
                    yjkmtableInfo.jsonList = json.d.list;
                }

            });
        },

    },


});

var pop = new Vue({
    el: '#pop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        popContent: {},
        cgShow: false,
        smShow: false,
        sbShow: false,
        ShowTitle: ''
    },
    methods: {
        //保存
        saveMb: function () {

            pop.popShow = false;
        },
        //取消
        cancel: function () {
            pop.popShow = false;
        },
        //扫码成功
        cgSm: function () {
            pop.cgShow = true;
            pop.smShow = false;
            pop.sbShow = false;
            pop.ShowTitle = '扫码接收成功';
        },
        //扫码失败
        sbSm: function () {
            pop.cgShow = false;
            pop.smShow = false;
            pop.sbShow = true;
            pop.ShowTitle = '扫码接收失败';
        }
    },
})

yjkmtableInfo.getData();




