var xm = {'编码': 'xmbm', '名称': 'xmmc','农合编码':'','农合名称':''};
var yjs = new Vue({
        el: '.ybhzgl-box',
        mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer,scrollOps],
        components: {
            'search-table': searchTable,
        },
        data: {
            sswrxmContent:{},
            popContent:{},
            csszContent:{},
            page:{
                page:1,
                rows:20,
                total:null,
                parm:""
            },
            bxlbbm:null,
            bxurl:null,
            selSearch:-1,
            searchCon:[],
            them:xm
        },
        methods: {
        	//获取存在的医保参数信息
            getData: function(){
            	var param = {
                		page:1,
                		rows:30,
                		sort:"yljgbm",
                		order:"asc"
                	};
            	$.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+yjs.bxurl+"&bxlbbm="+yjs.bxlbbm+"&types=cssz&method=getCsszInfo&parm="+JSON.stringify(param), function (json) {
                    if(json.a == "0"){
                    	yjs.popContent = JSON.parse(json.d);
                    }
                });
            },
            //获取保险类别名称
            getbxlb:function(){
              	var param = {bxjk:"005"};
              	common.openloading(".ybhzgl-box");
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                        if (json.a == 0){
                        	if (json.d.list.length > 0){
                        		yjs.bxlbbm = json.d.list[0].bxlbbm;
                        		yjs.bxurl = json.d.list[0].url;
                        		yjs.getData();
                        	}
                        	common.closeLoading();
                        }else{
                        	malert("保险类别查询失败!"+json.c);
                            common.closeLoading();
                        }
                    });
              },

            //保存参数
            save: function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+yjs.bxurl+"&bxlbbm="+yjs.bxlbbm+"&types=cssz&method=insertCssz&parm="+JSON.stringify(yjs.popContent), function (json) {
            		if (json.a == "0") {
                        malert('操作成功！','top','success');
						yjs.getData();
					} else {
                        malert(json.c,'top','defeadted');
					}
                });
            },

            //取消
            cancel: function(){
				yjs.getData();
            },

            //当输入值后才触发
            change: function (add, type, val) {
                if (!add) this.page.page = 1;       // 设置当前页号为第一页
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                //病人基本信息检索
                this.sswrxmContent['sswrxm'] = val;
                if (this.sswrxmContent['sswrxm'] == undefined || this.sswrxmContent['sswrxm'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.sswrxmContent['sswrxm'];
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yjs.bxurl + "&bxlbbm=" + yjs.bxlbbm + "&types=nhdm&method=queryYpxx&parm="
                    + JSON.stringify(yjs.page),
                    function (data) {
                        if(data.a == '0'){
                            var res = JSON.parse(data.d);
                            if (add) {//不是第一页则需要追加
                                for (var i = 0; i < res.list.length; i++) {
                                    yjs.searchCon.push(res.list[i]);
                                }
                            } else {
                                yjs.searchCon = res.list;
                            }
                            yjs.page.total = res.total;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        }else{
                            malert("未获取到相关项目数据！","top","defeated")
                        }
                    });
            },

            changeDown: function (event,type, content, searchCon) {
                this.nextFocus(event,'',true);
                if (this[searchCon][this.selSearch] == undefined) return;
                this.keyCodeFunction(event, content, searchCon);
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (type == 'sswrxm') {
                        yjs.popContent.sswrxm = yjs.csszContent['xmbm'];
                        yjs.selSearch = 0;
                        this.nextFocus(event);
                    }
                }
            },
            selectOne: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.change(true, 'sswrxm', this.csszContent['xmbm']);           // 传参表示请求下一页,不传就表示请求第一页
                } else {                            // 否则就是选中事件,为json赋值
                    yjs.popContent.sswrxm = item.xmbm;
                    $(".selectGroup").hide();

                }
            },
        }
    });
    //初始化查询
	yjs.getbxlb();
