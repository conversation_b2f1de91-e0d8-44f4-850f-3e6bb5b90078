var activeX = document.getElementById("csharpActiveX");

function CreatXmlDoc(obj){
	this.tagName=obj.tagName;
	var children=obj.children.map(function(item){
		if(typeof item =="object")
		{
			item=new CreatXmlDoc(item)
		}
		return item
	})
	this.children=children;
}


CreatXmlDoc.prototype.render=function(){
	var el=document.createElement(this.tagName);
	var children=this.children || [];
	children.forEach(function(child){
		var childEl=(child instanceof CreatXmlDoc)
			? child.render()
			:document.createTextNode(child)
		el.appendChild(childEl);
	})
	return el
}
var gz_012 = new Vue({
	el: '.gz_012',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data: {
		brxxContent:{},
		isShow: false,
		bxlbbm: null,
		bxurl: null,
		birthday: null,
		text: null,
		jbContent: {},
		searchCon: [],
		selSearch: -1,
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		them_tran: {},
		them: {
			'疾病编码': 'yke120',
			'疾病名称': 'yke121',
			'副编码': 'yke223'
		},
		brzt_tran:{
			'1':'在院',
			'2':'未在院'
		},
		zdxxJson:{
			prm_aka130 : "11",
		},
		grxxJson:{},
		ybjsxxJson :{},
		sfsbContent:{},
		cssg:false,
		userInfo:{},
	},
	mounted: function () {

		this.isShow = true;
		this.init();
		//this.userInfo = JSON.parse(sessionStorage.getItem('yljgOrUser' + userId));
		console.log('qqqqqqqqqqqqqqqqqqqqqqqq')
	},
	methods: {
		init: function () {
			var that = this;
			$.post("http://localhost:9089/init", {}, function (json) {
				if (json.aint_appcode > 0) {
					that.zyyhybInit = true;
					malert("初始化成功!");
				} else {
					malert("医保控件初始化失败！请从新打开页面!");
				}
			});
		},

		closegz_012: function () {
			this.isShow = false;
			$("#hyjl").html("");
		},
		getUserInfo: function () {
			this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
				.then(function (json) {
					this.userInfo = json.body.d;
				});
		},
		getbxlb: function () {
			var param = {bxjk: "012"};
			$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
				+ JSON.stringify(param), function (json) {
				if (json.a == 0) {
					if (json.d.list.length > 0) {
						gz_012.bxlbbm = json.d.list[0].bxlbbm;
						gz_012.bxurl = json.d.list[0].url;
					}
				} else {
					malert("保险类别查询失败!" + json.c,'top','defeadted')
				}
			});
		},
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'jbContent', 'searchCon');
			if (event.code == 'Enter' || event.code == 13) {
				if (type == "text") {
					Vue.set(this.jbContent, 'jbmc', this.jbContent['yke121']);
					gz_012.zdxxJson.jbbm = this.jbContent.yke120;
					gz_012.zdxxJson.jbmc = this.jbContent.yke121;
					this.selSearch=0;
					this.nextFocus(event);
				}
			}
		},

		searching: function (add, type,val) {
			this.jbContent['jbmc']=val;
			if (!add) this.page.page = 1;
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			if (this.jbContent[type] == undefined || this.jbContent[type] == null) {
				this.page.parm = "";
			} else {
				this.page.parm = this.jbContent[type];
			}
			var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_012.bxurl + "&bxlbbm=" + gz_012.bxlbbm + "&types=ICD10&method=query&parm="
				+ JSON.stringify(str_param),
				function (json) {
					if (json.a == 0) {
						var date = null;
						var res = eval('(' + json.d + ')');
						if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
							for (var i = 0; i < res.list.length; i++) {
								gz_012.searchCon.push(res.list[i]);
							}
						} else {
							gz_012.searchCon = res.list;
						}
						gz_012.page.total = res.total;
						gz_012.selSearch = 0;
						if (res.list.length > 0 && !add) {
							$(".selectGroup").hide();
							_searchEvent.show();
						}
					} else {
						malert("查询失败  " + json.c,'top','defeadted');
					}
				});
		},
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;               // 设置当前页号
				this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
			} else {   // 否则就是选中事件,为json赋值
				this.jbContent = item;
				Vue.set(this.jbContent, 'jbmc', this.jbContent['yke121']);
				gz_012.zdxxJson.jbbm = this.jbContent.yke120;
				gz_012.zdxxJson.jbmc = this.jbContent.yke121;
				$(".selectGroup").hide();
			}
		},
		//读卡
		load:function(){
			if(this.zyyhybInit){
				var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><aka130>'+this.zdxxJson.prm_aka130+'</aka130><proxy>1</proxy><yinhai>1</yinhai></input>';
				$.post("http://localhost:9089/call", {
					jybh:"03",
					jysr_xml:jysr
				}, function (json) {
					if (json.aint_appcode >= 0) {
						gz_012.sfsbContent = JSON.parse(json.astr_jysc_xml);
						gz_012.grxxJson = gz_012.sfsbContent;
						malert("读卡成功!");
					} else {
						malert(json.astr_appmasg);
					}
				});
			}else{
				malert("医保控件未初始化,请重新打开页面！",'top','defeadted');
			}
		},

		//引入
		enter:function(){
			if (Object.keys(gz_012.grxxJson).length === 0) {
				malert("请先读卡","top","defeadted");
				return;
			}
			if (gz_012.zdxxJson.prm_aka130 == null || gz_012.zdxxJson.prm_aka130 === '' || gz_012.zdxxJson.prm_aka130 === undefined) {
				malert("请选择支付类型","top","defeadted");
				return;
			}
			if(gz_012.grxxJson.prm_ykc023 == '1'){
				malert('病人再院，无法办理医保入院，请办理自费入院','right','defeadted')
				return;
			}
			//个人信息
			gz_012.gzyhybContent = gz_012.grxxJson;


			// 给页面输入框赋值
			contextInfo.json.brxm = gz_012.grxxJson.aac003;
			contextInfo.json.brxb = gz_012.grxxJson.aac004;
			contextInfo.json.sfzjhm = gz_012.grxxJson.aac002;
			contextInfo.updatedData = "1";
			contextInfo.setAge();
			if(contextInfo.json.jzdmc && contextInfo.json.jzdmc.indexOf(gz_012.grxxJson.aab003) == -1){
				contextInfo.json.jzdmc = contextInfo.json.jzdmc + gz_012.grxxJson.aab003;
			}else {
				contextInfo.json.jzdmc =  gz_012.grxxJson.aab003;
			}

		},

		//门诊预结算方法
		mzyjs:function(){
			var result = "0";
			//同步操作
			$.ajaxSettings.async = false;
			//处理费用
			var yhybBrfyList = [];
			var fylist=[];
			var brfyList=[];
			var fyze = 0.00;
			for (var i=0;i<contextInfo.brfyjsonList.length;i++) {
				var fyparam = {};
				fyparam.mxfyxmbm = contextInfo.brfyList[i].mxfybm;
				fyparam.yzlx = '1';
				fyparam.yzhm = contextInfo.brfyList[i].yzhm;
				fyparam.fysl = contextInfo.brfyList[i].fysl;
				brfyList.push(fyparam);
				fyze += contextInfo.brfyList[i].fyje;
			}
			var param = {
				fylist: brfyList
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_012.bxurl + "&bxlbbm=" + gz_012.bxlbbm + "&types=mzjy&method=queryMzfy&parm="
				+ JSON.stringify(param),
				function (json) {
					if(json.a == '0'){
						yhybBrfyList = eval('(' + json.d + ')');
						for (let i = 0; i < yhybBrfyList.length; i++) {
							fylist.push(JSON.parse(yhybBrfyList[i]));
						}
					}else{
						malert(json.c);
						return false;
					}
				});
			if(fylist==null || fylist==undefined || fylist=="" || fylist.length<=0){
				malert("没有可结算费用！");
				return false;
			}

			var rowlist=[];
			for(var i=0;i<fylist.length;i++){
				if(fylist[i] && fylist[i].yka002){
					var taglist=[];
					//记账流水号
					var obj1={
						tagName:'ykc120',
						children:[(new Date()).getTime()+i]
					};
					taglist.push(obj1);
					var obj2={
						tagName:'aae072',
						children:['']

					};
					taglist.push(obj2);
					//单据类型
					var obj3={
						tagName:'aka017',
						children:['0']
					};
					taglist.push(obj3);

					//商品码
					var obj4={
						tagName:'bke030',
						children:['']
					};
					taglist.push(obj4);
					//医院项目编码
					var obj5={
						tagName:'ake005',
						children:[fylist[i].ykd125]
					};
					taglist.push(obj5);
					//医院项目名称
					var obj6={
						tagName:'ake006',
						children:[fylist[i].ykd126]
					};
					taglist.push(obj6);
					//本单收费单位
					var obj7 ={
						tagName:'aka067',
						children:['元']
					};
					taglist.push(obj7);
					//最小收费单位
					var obj8 ={
						tagName:'aka067_mi',
						children:['元']
					};
					taglist.push(obj8);
					//院内收费项目剂型
					var obj9 ={
						tagName:'aka070',
						children:['']
					};
					taglist.push(obj9);
					//院内收费项目规格
					var obj10 ={
						tagName:'aka074',
						children:['']
					};
					taglist.push(obj10);
					//与单次用量同单位规格（数值型）
					var obj11 ={
						tagName:'aka074_s',
						children:['']
					};
					taglist.push(obj11);
					//用法
					var obj12 ={
						tagName:'bkc045',
						children:['']
					};
					taglist.push(obj12);
					//每次用量（数值型）
					var obj13 ={
						tagName:'bkc044',
						children:['']
					};
					taglist.push(obj13);
					//单价
					var obj14 ={
						tagName:'akc225',
						children:[fylist[i].akc225]
					};
					taglist.push(obj14);
					//数量
					var obj15 ={
						tagName:'akc226',
						children:[fylist[i].akc226]
					};
					taglist.push(obj15);
					//金额
					var obj16 ={
						tagName:'aae019',
						children:[fylist[i].yka315]
					};
					taglist.push(obj16);
					//开单科室编码
					var obj17 ={
						tagName:'yka091',
						children:[userInfo.ksbm]
					};
					taglist.push(obj17);
					//开单科室名称
					var obj18 ={
						tagName:'yka092',
						children:[userInfo.ksmc]
					};
					taglist.push(obj18);
					//开单医生编码
					var obj19 ={
						tagName:'yka093',
						children:[userId]
					};
					taglist.push(obj19);
					//开单医生姓名
					var obj20 ={
						tagName:'yka094',
						children:[userName]
					};
					taglist.push(obj20);
					//收单科室编码
					var obj21 ={
						tagName:'yka100',
						children:['']
					};
					taglist.push(obj21);
					//收单科室名称
					var obj22 ={
						tagName:'yka101',
						children:['']
					};
					taglist.push(obj22);
					//收单医生编码
					var obj23 ={
						tagName:'yka102',
						children:['']
					};
					taglist.push(obj23);
					//收单医生姓名
					var obj24 ={
						tagName:'yka103',
						children:['']
					};
					taglist.push(obj24);
					//费用发生时间
					var obj25 ={
						tagName:'ake007',
						children:[this.getRq()]
					};
					taglist.push(obj25);
					//院外检查标志
					var obj26 ={
						tagName:'yke330',
						children:['']
					};
					taglist.push(obj26);
					//门急费用标志
					var obj27 ={
						tagName:'yke331',
						children:['是']
					};
					taglist.push(obj27);
					//门急费用标志
					var obj28 ={
						tagName:'yke331',
						children:['1']
					};
					taglist.push(obj28);
					//医院审核标志
					var obj29 ={
						tagName:'ykz004',
						children:['1']
					};
					taglist.push(obj29);

					//经办人
					var obj30 ={
						tagName:'aae011',
						children:[userId]
					};
					taglist.push(obj30);
					var row={
						tagName:'row',
						children:taglist
					}
					rowlist.push(row);
				}else{
					malert("第【" + (i+1) + "】行，【"+ fylist[i].ykd126 +"】未对码，请先对码！","top","defeadted");
					return false;
				}
			}
			fyze = fyze.toFixed(2);
			var obj={
				tagName:'dataset',
				children:rowlist
			};
			// var startDate = this.getRq();
			doc=new CreatXmlDoc(obj);
			SetupSerial=(new XMLSerializer()).serializeToString(doc.render());
			var reg = new RegExp(' xmlns="http://www.w3.org/1999/xhtml"',"g");
			SetupSerial=SetupSerial.replace(reg,"");
			var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var aka130="<aka130>"+'11'+"</aka130>";//出险方式
			var aae030="<aae030>"+this.getRq()+"</aae030>";//门诊开始日期
			var aae031="<aae031>"+this.getRq()+"</aae031>";//门诊结算日期
			var aac001="<aac001>"+gz_012.grxxJson.aac001+"</aac001>";//个人编号
			var akc193="<akc193></akc193>";//门诊诊断
			var aae072="<aae072>"+'201'+"</aae072>";//发票号
			var aae011="<aae011>"+userId+"</aae011>";//经办人
			var aae013="<aae013>"+userId+"</aae013>";//备注
			var ykz006="<ykz006>"+gz_012.grxxJson.bzsm+"</ykz006>";//计算相关费用标志
			var akc001="<akc001>"+gz_012.grxxJson.bzsm+"</akc001>";//文件id
			var amc026="<amc026></amc026>";//
			var amc029="<amc029></amc029>";//计划生育手术类别
			var yke004="<yke004></yke004>";//医疗类别
			var prm_ykc141="<prm_ykc141>"+ fylist[0].ykc141 +"</prm_ykc141>";
			var prm_ykb065="<prm_ykb065></prm_ykb065>";

			jysr=jysr+aka130+aae030+aae031+aac001+akc193+aae072+aae011+aae013+akc001+
				yke004+amc029+amc026+ykz006+SetupSerial+"</input>";
			//去掉所有不合法字符
			jysr = jysr.replace(/undefined/g,"");
			jysr = jysr.replace(/NaN/g,"");
			jysr = jysr.replace(/null/g,"");
			//调用结算方法
			this.postAjax("http://localhost:9089/call",{
				'jybh':'48',
				'jysr_xml':jysr
			},function (json) {
				if (json.aint_appcode > 0) {
					contextInfo.yjsContentZyyhyb = JSON.parse(json.astr_jysc_xml);
					gz_012.ybjsxxJson =JSON.parse(json.astr_jysc_xml);
					contextInfo.jylsh = json.astr_jylsh;
					contextInfo.jyyzm = json.astr_jjyyzm;
				}else {
					malert(json.astr_appmasg,"top","defeadted");
					result = "1";
				}
			});
			//结算成功后，进行HIS门诊登记
			//这里转换一次，将字段下划线去掉，并修改为驼峰命名
			var enterContent = {
				id : gz_012.grxxJson.aaa027,//挂号的时候存统筹区编码，门诊收费存挂号序号
				prmAac001 : gz_012.grxxJson.aac001,//个人编号 NOT NULL VARCHAR2(15)
				prmAkc021 : '',//gz_012.grxxJson.prm_akc021,//医疗人员类别 NOT NULL VARCHAR2(6) 见代码表
				prmYkc120 : '',//gz_012.grxxJson.prm_ykc120,//公务员级别 NOT NULL VARCHAR2(6) 见代码表
				prmYab139 : gz_012.grxxJson.aab034,//参保所属分中心 NOT NULL VARCHAR2(6) 见代码表
				prmYkb065 : gz_012.grxxJson.ykb065,//执行社会保险办法 NOT NULL VARCHAR2(6) 见代码表
				prmYkc150 : gz_012.grxxJson.aae139,//异地安置标志 NOT NULL VARCHAR2(6) 见代码表
				prmAka130 : gz_012.zdxxJson.prm_aka130,//支付类型
				prmAac003 : gz_012.grxxJson.aac003,//姓名 NULL VARCHAR2(20)
				prmAac004 : gz_012.grxxJson.aac004,//性别 NULL VARCHAR2(6) 见代码表
				prmAac002 : gz_012.grxxJson.aac002,//公民身份号码 NULL VARCHAR2(18)
				prmAac006 : gz_012.grxxJson.aac006,//出生日期 NULL DATETIME
				prmAkc023 : gz_012.grxxJson.yke112,//实足年龄 NULL NUMBER(3,0)
				prmAab001 : '',//gz_012.grxxJson.prm_aab001,//单位编号 NOT NULL VARCHAR2(15)
				prmAab004 : gz_012.grxxJson.aab003,//单位名称 NULL VARCHAR2(50)
				prmAac031 : gz_012.grxxJson.aac031,//个人参保状态 NOT NULL VARCHAR2(6) 见代码表
				prmAkc087 : gz_012.grxxJson.aae240,//个人账户余额 NOT NULL NUMBER(14,2)
				prmYab003 : '',//gz_012.grxxJson.prm_yab003,//分中心编号 NOT NULL VARCHAR2(6) 见代码表
				prmYkc280 : '',//gz_012.grxxJson.prm_ykc280,//居民医疗人员类别 NULL VARCHAR2(6) 见代码表
				prmYkc281 : '',//gz_012.grxxJson.prm_ykc281,//居民医疗人员身份 NULL VARCHAR2(6) 见代码表
				prmYkc023 : '',//gz_012.grxxJson.prm_ykc023,//住院状态 NULL VARCHAR2(6) 当住院的时间内，不允许门诊刷卡时，HIS需要判断该字段。1为在院，2 为未在院
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_012.bxurl + "&bxlbbm=" + gz_012.bxlbbm + "&types=mzjy&method=mzdj&parm="
				+ JSON.stringify(enterContent),
				function (json) {
					if (json.a != '0') {
						result = "1";
					}else {
						//写门诊医保结算输出记录
						gz_012.mzjs();
					}
				});
			return result;
		},

		getRq:function(){
			var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth() + 1;
			var day = date.getDate();
			if (month < 10) {
				month = "0" + month;
			}
			if (day < 10) {
				day = "0" + day;
			}
			var nowDate = year + "-" + month + "-" + day;
			return nowDate;
		},

		//取消
		gz002_quxiao:function(){
			//先初始化
			this.init();
			//读卡
			this.load();



			var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var prm_akc190 = "<prm_akc190>" + rightVue.yjsContentGzyhyb.prm_akc190 + "</prm_akc190>";//就诊编号
			var prm_yab003 = "<prm_yab003>" + gz_002.grxxJson.prm_yab003 + "</prm_yab003>";//分中心编号
			var prm_aka130 = "<prm_aka130>" + gz_002.zdxxJson.prm_aka130 + "</prm_aka130>";//支付类型
			var prm_yka103 = "<prm_yka103>" + rightVue.yjsContentGzyhyb.prm_yka103 + "</prm_yka103>";//结算编号
			var prm_aae011 = "<prm_aae011>" + gz_002.userInfo.czybm + "</prm_aae011>";//操作员编码
			var prm_ykc141 = "<prm_ykc141>" + gz_002.userInfo.czyxm + "</prm_ykc141>";//操作员姓名
			var prm_aae036 = "<prm_aae036>" + gz_002.fDate(new Date(),'datetime') + "</prm_aae036>";//经办时间
			var prm_aae013 = "<prm_aae013>用户取消</prm_aae013>";//退费原因，
			var prm_ykb065 = "<prm_ykb065>" + gz_002.grxxJson.prm_ykb065 + "</prm_ykb065>";
			var prm_aac001 = "<prm_aac001>" + rightVue.yjsContentGzyhyb.prm_aac001 + "</prm_aac001>";
			jysr += prm_akc190 + prm_yab003 + prm_aka130 + prm_yka103 + prm_aae011
				+ prm_ykc141 + prm_aae036 + prm_aae013 + prm_ykb065 + prm_aac001+"</input>";
			$.post("http://localhost:9089/call",{
				'jybh':"42",
				'jysr_xml':jysr,
			},function (json) {
				//成功后调一次 confirm
				if (json.aint_appcode > 0) {
					$.post("http://localhost:9089/confirm",{
						'jylsh':json.astr_jylsh,
						'jyyzm':json.astr_jyyzm,
					},function (data) {
					});
				}else {
					malert(data.astr_appmasg);
				}
			});
		},

		//门诊结算方法
		mzjs:function(){

			//结算成功后，进行HIS门诊登记
			//这里转换一次，将字段下划线去掉，并修改为驼峰命名
			var enterContent = {
				id :gz_012.grxxJson.aaa027,//乐至医保挂号时id 用于存统筹区编码
				prmAkc190 :gz_012.ybjsxxJson.akc190,//就诊编号
				prmAac001 : gz_012.ybjsxxJson.aac001,//个人编号 NOT NULL VARCHAR2(15)
				prmYka103 : gz_012.ybjsxxJson.aaz216,//结算编号
				prmYka055 : gz_012.ybjsxxJson.akc264,//费用总金额
				prmAkc087 : gz_012.ybjsxxJson.ake034,//个人账户支付金额
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_012.bxurl + "&bxlbbm=" + gz_012.bxlbbm + "&types=mzjy&method=mzjs&parm="
				+ JSON.stringify(enterContent),
				function (json) {
					if (json.a != '0') {
						result = "1";
					}
				});

		},
	}
});
gz_012.getbxlb();
gz_012.getUserInfo();

$(document).click(function () {
	if (this.className != 'selectGroup') {
		$(".selectGroup").hide();
	}
	$(".popInfo ul").hide();
});
