    var chlb = new Vue({
        el:"#chlb",
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            edit:false,
            bxlbbm:"",
            bxurl:"",
            jsonList: [],
            zyzt: '0',
            beginrq: '',
            endrq: '',
            cxrq: 'ryrq',
            param: {
                page: 1,
                rows: 10,
                sort: '',
                order: 'desc',
                zyzt: '3',
                parm: '',
            },
            pxContent: {
                'sort': '0',
                'order': '0',
            },
            zt_tran: {
                '0': '在院',
                '1': '出院',
                '3': '待结算'
            },

        },
        methods:{
            // getBxlb: function() {
            //     var param = {
            //         bxjk: "005"
            //     };
            //     common.openloading("#chlb");
            //     $.getJSON(
            //         "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" +
            //         JSON.stringify(param),
            //         function(json) {
            //             if(json.a == 0) {
            //                 common.closeLoading();
            //                 if(json.d.list.length > 0) {
            //                     chlb.bxlbbm = json.d.list[0].bxlbbm;
            //                     chlb.bxurl = json.d.list[0].url;
            //                 }
            //
            //             } else {
            //                 malert("保险类别查询失败!" + json.c,"top","defeadted");
            //                 common.closeLoading();
            //             }
            //         });
            // },
            searching:function(val){
                this.param.parm = val;
                this.getData();
            },
            getData: function(){
                this.param.psyp = null;
                this.param.yyff = null;
                if(chlb.type=='yd'){
                    this.param.psyp = '1';
                }
                if(chlb.type=='wd'){
                    this.param.yyff = '1';
                }
                common.openloading("#chlb");
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + fyxmTab.bxurl + "&bxlbbm=" + fyxmTab.bxlbbm + "&types=inHospital&method=queryAllZybr&parm=" + JSON.stringify(this.param),
                    function(json) {
                        if(json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            chlb.totlePage = Math.ceil(res.total / chlb.param.rows);
                            chlb.jsonList = res.list;
                            common.closeLoading();
                        } else {
                            malert(json.c,"top","defeadted");
                            common.closeLoading();
                        }
                    });
            },
            addData: function () {
                common.openloading("#nhywbl");
                loadPage.saveData('chlb', {
                    pageState: this.pageState,
                    param: this.param
                });
                loadPage.show("editRydj.html");
                common.closeLoading();
                chlb.edit = true;
            },
            editRydj: function (index) {
                common.openloading("#nhywbl");
                        // loadPage.saveData('chlb', {
                        //     pageState: chlb.pageState,
                        //     param: chlb.param,
                        //     pageParam: {
                        //         obj: this.jsonList[index],
                        //         importFlag: "hzlbEdit"
                        //     }
                        // });
                sessionStorage.setItem('obj',JSON.stringify(this.jsonList[index]));
                chlb.edit = true;
                $("#rydj_edit").load("editRydj.html").fadeIn(300);
                common.closeLoading();
            },
            ztChange: function(val){
                this.param.zyzt = val[0];
                if (this.param.zyzt == 1) {
                    //出院
                    this.param.cxrq = 'cyrq'
                }
                if (this.param.zyzt == 0) {
                    //入院
                    this.param.cxrq = 'ryrq'
                }

                if (this.param.zyzt == 2) {
                    //门诊接入
                }
                if (this.param.zyzt == 3) {
                    //结算
                }
                this.getData();
            },
        }
    });

    // chlb.getBxlb();
    setTimeout(function () {
        chlb.getData();
    }, 200)


$(window).resize(function () {
    changHeight();
})
setTimeout(function () {
    changHeight();
}, 150)





