.zui-table-view .zui-table-fixed.table-fixed-l{
    left: 10px!important;;
}
.zui-table-view .zui-table-fixed.table-fixed-r{
    right: 10px !important;
}
.color-f3a74f{
    color: #f3a74f;
}
.wh30{
    width: 30%;
}
.count-name{
    padding-right: 15px;
}
.start-overtime{
    top: -14px;
    left: 48%;
    transform: translate(-48%);
}
.text-decoration {
    color: #1abc9c;
    cursor: pointer;
    text-decoration: underline;
}
.topsh{
    width: 164px;
    white-space: normal;
}
.topshBefter:before{
    width: 0;
    height: 0;
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    border-width: 5px;
    border-style: solid;
    border-left-color: transparent;
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-right-color: rgba(50, 50, 50, 0.7);
}
.userName-pin img{
    /*background-image: url("/newzui/pub/image/pin.png");*/
    /*background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);*/
    /*border: 1px solid #f46161;*/
    border-radius: 4px;
    background-color: #f46161;
    width: 20px;
    height: 20px;
    /*background-position: center center;*/
    /*background-repeat: no-repeat;*/
    font-size: 10px;
    color: #ffffff;
}

.userName-lc img{
    /*background-image: linear-gradient(-180deg, #ffb456 3%, #ed8805 100%);*/
    border: 1px solid #ed8705;
    border-radius: 4px;
    width: 20px;
    background-color: #ed8705;
    height: 20px;
    font-size: 10px;
    color: #ffffff;
}
.errorBg{
    background-color:rgba(255,92,99,0.10);
}