var cd_014 = new Vue({
    el: '.cd_014',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        skr_tran: {
            '本人': '本人',
            '父母': '父母',
            '子女': '子女',
            '配偶': '配偶',
            '同胞兄弟姐妹': '同胞兄弟姐妹',
            '外祖父母': '外祖父母',
        },
        isShow: false,
        bxlbbm: null,
        bxurl: null,
        birthday: null,
        text: null,
        jbContent: {},
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'yke120',
            '疾病名称': 'yke121',
            '副编码': 'yke223'
        },
        brzt_tran: {
            '1': '在院',
            '2': '未在院'
        },
        zdxxJson: {},
        grxxJson: {
            aka130: "0201",
        },
        cssg: false,
        userInfo: {},
        ifclick: true,
        cd_ykc117_tran: {
            '01': '公务员',
            '02': '非公务员 ',
        },
        cdydbz_tran: {
            '1': '本地',
            '2': '省内异地 ',
            '3': '省外异地'
        },
        cd_aka130_tran: {
            '0101': '药店购药',
            '0102': '城居药店支付',
            '0201': '普通门诊',
            '0202': '特殊疾病门诊',
            '0203': '城居普通门诊',
            '0204': '城居门诊特殊病',
            '0205': '城职门诊统筹',
            '0206': '城乡门诊统筹',
            '0207': '酪氨酸城职',
            '0208': '酪氨酸城乡',
            '0209': '大学生门诊',
            '0301': '普通住院',
            '0302': '家庭病床',
            '0304': '市外转诊住院(异地急救参照市外转诊)',
            '0305': '统筹区内转院',
            '0306': '外伤住院',
            '0307': '异地抢救住院(手工报销使用)',
            '0308': '本市非定点抢救住院(手工报销使用)',
            '0309': '城居普通住院',
            '0310': '城居外伤住院',
            '0311': '城居市外转诊',
            '0401': '机关事业单位生育报销',
            '0501': '工伤门诊',
            '0502': '工伤住院',
            '0504': '工伤康复治疗住院',
            '0602': '城居生育分娩住院',
            '0603': '城居产前检查',
            '0604': '城职生育住院',
            '13': '异地门特（就医地）',
        },
        gsdataset_tran: [],
        fylist: [],
        datasetyka026_tran: [],
        gsdataset_show: false,
        datasetyka026_show: false,
        lxxx_show: false,
        ryfbmc: '',
        isdzps: false,//是否电子医保凭证
        pushResult: false,//单子医保凭证推送结果
        yinHaiRequest: {//用于声明银海请求对象，与字段对应关系
            //门诊交易（11）
            code11: {
                control: {
                    akc190: 'akc190',//就诊编码
                    nums: 'nums',//明细条数
                    yka055: 'fyzh',//费用总额 yka055
                    edition: 'edition',//接口版本标志
                    yke550: 'yke550',//零药结算标志  --固定传入值1
                    aac002: 'aac002',//身份证号
                    aac003: 'aac003',//姓名
                    yka719: 'yka719'//个人账户共济标志|1共济账户支付，2非共济账户支付
                },
                data: {
                    dataSetMx: {
                        yka105: 'yka105',//"记账流水号"
                        yka094: 'yka094',//"医保项目编码"
                        yka095: 'yka095',//"医院项目名称(诊疗项目)"
                        akc226: 'akc226',//"数量"
                        akc225: 'akc225',//"单价"
                        yka055: 'yka055',//"费用总额"
                        yke186: 'yke186',//"医院审核标志"
                        yka097: 'yka097',//"开单科室编码"
                        yka098: 'yka098',//"开单科室名称"
                        yka099: 'yka099',//"开单医生"
                        yka100: 'yka100',//"受单科室编码"
                        yka101: 'yka101',//"受单科室名称1"
                        yka102: 'yka102',//"受单医生1"
                        aae011: 'aae011',//"经办人姓名1"
                        aae036: 'aae036',//"2006-06-08 12:12:12"  明细录入时间
                        yke123: 'yke123',//"2006-06-07 12:20:12"  明细发生时间
                        ykd040: 'ykd040',//"手术编号1"
                        yke112: 'yka105',//"医嘱记录序号1"  5.0接口中必传
                        aae013: 'aae013',//"备注1"
                        yke201: 'yke201',//"中药使用方式"
                        yke134: 'yke134',//"处方号"
                        yke553: 'yke553',//"药品进价"
                        //--5.0接口增加
                        yke676: 'yke676',//"外检标志"
                        yke677: 'yke677',//"外检医院编码"
                        ykf008: 'ykf008',//"医职人员编码"
                        ykf013: 'ykf013',//"设备编码"
                        ake005: 'ake005',//"医院对码流水号" ,//2014-09-20 add
                        yka059: 'yka059',//"药品本位吗/诊疗项目编码 "
                        // --离休门诊结算明细项目信息(离休必传) hff add 2014-06-26
                        yke351: 'yke351',//”剂量单位”
                        yke352: 'yke352',//”剂量”
                        yke654: 'yke654',//”每次数量”
                        yke655: 'yke655',//”每次数量单位”
                        yke350: 'yke350',//”频次”
                        yke446: 'yke446'
                    },
                    dataSetYz: {
                        yke112: 'yka105',//医嘱记录序号
                        yke113: 'yka095',//医嘱内容
                        yka287: 'yka099',//医生姓名
                        ykf008: 'ykf008',//医职人员编码
                        aaz307: 'yka097',//医嘱科室编码
                        akf002: 'yka098',//医嘱科室名称
                        ake005: 'ake005',//医院对码编码
                        yke365: 'yke365',//医嘱类别
                        yke658: 'yke658',//医嘱分类
                        yke351: 'yke351',//剂量单位
                        yke352: 'yke352',//剂量
                        yke355: 'yke355',//用药途径
                        yke654: 'yke654',//每次数量
                        yke655: 'yke655',//每次数量单位
                        yke656: 'yke656',//发药量
                        yke657: 'yke657',//发药量单位
                        yke350: 'yke350',//频次
                        yke446: 'yke446',//使用天数
                    },
                    datalxzd: {
                        ykd018: 'ykd018',//第一疾病诊断代码
                        yke122: 'yke122'//诊断信息描述
                    }
                }
            },
            //省医保属性
            provinceCode11: {
                control: {
                    akc190: 'akc190',//就诊编码
                    nums: 'nums',//明细条数
                    yka055: 'fyzh',//费用总额 yka055
                    edition: 'edition',//接口版本标志
                    yke251: 'yke251',//医院内门诊号
                    akc193: 'akc193',//门诊诊断icd-10
                    yke122: 'yke122',//门诊诊断icd-10
                    brbz: 'brbz',//本人标志
                    yka719: 'yka719',//个人账户共济标志|1共济账户支付，2非共济账户支付
                },
                ykd018dataset: {
                    ykd018: 'ykd018',//疾病诊断代码
                    yke122: 'yke122',//诊断信息描述
                },
                data: {
                    row: {
                        yka105: 'yka105',//"记账流水号"
                        yka094: 'yka094',//"医保项目编码"
                        yka095: 'yka095',//"医院项目名称(诊疗项目)"
                        akc226: 'akc226',//"数量"
                        akc225: 'akc225',//"单价"
                        yka055: 'yka055',//"费用总额"
                        yke186: 'yke186',//"医院审核标志"
                        yka097: 'yka097',//"开单科室编码"
                        yka098: 'yka098',//"开单科室名称"
                        yka099: 'yka099',//"开单医生"
                        yka100: 'yka100',//"受单科室编码"
                        yka101: 'yka101',//"受单科室名称1"
                        yka102: 'yka102',//"受单医生1"
                        aae011: 'aae011',//"经办人姓名1"
                        aae036: 'aae036',//"2006-06-08 12:12:12"  明细录入时间
                        yke123: 'yke123',//"2006-06-07 12:20:12"  明细发生时间
                        ykd040: 'ykd040',//"手术编号1"
                        yke112: 'yka105',//"医嘱记录序号1"  5.0接口中必传
                        aae013: 'aae013',//"备注1"
                        yke201: 'yke201',//"中药使用方式"
                        yke134: 'yke134',//"处方号"
                        aka067: 'aka067',//"最小收费单位"
                        aka067_yn: 'aka067',//"本单收费单位"
                        bke026: 'ake005',//"医院内项目编码"
                        yka121: 'ykf008',//"开单医生编码"
                        yka122: 'ykf008',//"受单医生编码"
                        aka074_yn: 'yke351',//"医院内规格
                        aka070_yn: 'yke352',//"院内收费项目剂型
                        aka073_mb: '',//"用法"（码表）非必填
                    },
                    datayka065: {
                        akc190: 'akc190',//原主交易就诊号
                        yka103: 'yka103',//原主交易结算编号
                        aac002: 'aac002',//原主交易身份证号
                        aka130: 'aka130',//原主交易就诊类别
                        yka055: 'yka055',//原主交易费用总额
                        yka107: 'yka107',//原主交易医保基金支付总额
                        yka107: 'yka107',//原主交易个账支付金额
                        ykh012: 'ykh012',//原主交易现金支付金额
                        ykc178: 'ykc178'//原主交易现金支付金额 1 父母 2子女 3 配偶 4其他 5 本人
                    }
                }
            }
        }
    },
    created: function () {
        this.init();
    },
    mounted: function () {
        this.isShow = true;
        this.getbxlb();
        this.getUserInfo();
    },
    methods: {
        init: function () {
            $.post("http://localhost:10014/init", {}, function (json) {
                if (json.aint_appcode > 0) {
                    rightVue.gzyhybInit = true;
                    malert("初始化成功!", "right", "success");
                } else {
                    malert("医保控件初始化失败！请从新打开页面!", "right", "defeadted");
                }
            });
        },
        closeGz_002: function () {
            this.isShow = false;
            $("#hyjl").html("");
        },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    cd_014.userInfo = json.body.d;
                });
        },
        getbxlb: function () {
            var that = this;
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    that.bxlbbm = json.d.list[0].bxlbbm;
                    that.bxurl = json.d.list[0].url;
                } else {
                    malert("保险类别查询失败!" + json.c, 'right', 'defeadted')
                }
            });
        },
        commonResultChange: function (val) {
            var type = val[2][1];
            switch (type) {
                case "yke109":
                    Vue.set(this.grxxJson, type, val[0]);
                    Vue.set(this.grxxJson, "alc022", val[4]);
                    Vue.set(this.grxxJson, "aka130", cd_014.listGetName(cd_014.gsdataset_tran, val[0], 'yke109', 'aka130'));
                    break;
                case "bkc014":
                    Vue.set(this.grxxJson, type, val[0]);
                    Vue.set(this.grxxJson, "bkc117", val[4]);
                    break;
            }
        },
        qdFun: function () {
            //签到
            var qd = true;
            let qd_jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><control><aae011>' + this.userInfo.czyxm + '</aae011></control>';
            let qd_jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><data><yab003>0000</yab003></data>';
            this.postFormAjax("http://localhost:10014/call", {
                'jybh': '05',
                'jykz_xml': qd_jykz,
                'jysr_xml': qd_jysr,
            }, function (sign05json) {
                if (sign05json.aint_appcode > 0) {
                    qd = true
                } else {
                    qd = false
                }
            });
            return qd
        },
        loadqd: function () {
            //签到
            var jykzqd = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>' +
                '<control>' +
                '<aae011>' + sessionStorage.getItem('userName' + userId) + '</aae011>' +
                '</control>';
            var jysrqd = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><data><yab003>0000</yab003></data>';
            $.post("http://localhost:10014/call", {
                jybh: "05",
                jykz_xml: jykzqd,
                jysr_xml: jysrqd,
            }, function (json) {

            })
        },
        getUserYibaoInfo: function (json) {
            cd_014.ifclick = true;
            if (json.aint_appcode > 0) {
                Vue.set(cd_014, 'grxxJson', JSON.parse(json.astr_jysc_xml));
                if (rightVue.fzContent.brxm != cd_014.grxxJson.aac003) {
                    malert("非本人医保卡，请注意", "right", "defeadted");
                }
                if (this.ryfbmc && this.ryfbmc.indexOf("门特") != -1) {
                    Vue.set(cd_014.grxxJson, 'aka130', '0202');
                } else {
                    Vue.set(cd_014.grxxJson, 'aka130', '0201');
                }
                Vue.set(cd_014, 'gsdataset_tran', cd_014.grxxJson.gsdataset);
                if (cd_014.grxxJson.grzhye && cd_014.grxxJson.grzhye.row) {
                    Vue.set(cd_014.grxxJson, 'ykc303', cd_014.grxxJson.grzhye.row.ykc303);
                    Vue.set(cd_014.grxxJson, 'ykc194', cd_014.grxxJson.grzhye.row.ykc194);
                }
                //离休信息展示
                if (cd_014.grxxJson.lxdataset) {
                    Vue.set(cd_014.grxxJson, 'ykc303', cd_014.grxxJson.grzhye.row.ykc303);
                    Vue.set(cd_014.grxxJson, 'ykc194', cd_014.grxxJson.grzhye.row.ykc194);
                }
                Vue.set(cd_014.grxxJson, 'skrgx', '本人');
                if (this.ryfbmc && this.ryfbmc.indexOf("门特") != -1) {
                    var str_param = {
                        ghxh: rightVue.mzjbxxContent.ghxh,
                        zt: '0'
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=querydjxx&parm="
                        + JSON.stringify(str_param),
                        function (json) {
                            if (json.a == 0) {
                                cd_014.grxxJson.akc190 = JSON.parse(json.d).akc190;
                                cd_014.$forceUpdate()
                                malert("读卡成功!", "right", "success");
                            } else {
                                malert("查询失败  " + json.c, 'right', 'defeadted');
                            }
                        });
                } else {
                    cd_014.$forceUpdate()
                    malert("读卡成功!", "right", "success");
                }
            } else {
                malert(json.astr_appmasg, "right", "defeadted");
            }
        },
        loadDzpz: function () {
            var data = {uid: uuid()};
            $.ajax({
                async: false,
                url: 'http://localhost:36658/YinHaiClient/pull',
                type: 'post',
                contentType: "application/x-www-form-urlencoded",
                dataType: 'jsonp',  // 请求方式为jsonp
                jsonpCallback: "jsonpCallback",
                data: data,
                complete: function (res) {
                    if (res.status != 200) {
                        malert("信息加载失败,请关闭菜单项后重试", 'top', 'defeadted');
                    }
                    var url = "http://localhost:10014/getUser?uid=" + data.uid
                    $.post(url, {}, function (json) {
                        cd_014.getUserYibaoInfo(json);
                        if (json.aint_appcode > 0) {
                            cd_014.isdzps = true;//读卡成功后，更新电子医保凭证
                        }
                    });
                }
            })
        },
        //读卡
        load: function () {

            /*if(!cd_014.ifclick){
                malert("请勿重复点击！","right","defeadted");
                return;
            }*/
            cd_014.ifclick = false;

            if (rightVue.gzyhybInit) {

                //身份识别
                var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>' +
                    '<control>' +
                    '<edition>3</edition>' +
                    '<system>1</system>' +
                    '</control>';
                var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><data></data>';
                /*$.post("http://localhost:10014/call", {
                    jybh:"05",
                    jykz_xml:jykzqd,
                    jysr_xml:jysrqd,
                }, function (json) {*/
                $.post("http://localhost:10014/call", {
                    jybh: "03",
                    jykz_xml: jykz,
                    jysr_xml: jysr,
                }, function (json) {
                    cd_014.ifclick = true;
                    if (json.aint_appcode > 0) {
                        Vue.set(cd_014, 'grxxJson', JSON.parse(json.astr_jysc_xml));
                        if (rightVue.fzContent.brxm != cd_014.grxxJson.aac003) {
                            malert("非本人医保卡，请注意", "right", "defeadted");
                        }
                        if (this.ryfbmc && this.ryfbmc.indexOf("门特") != -1) {
                            Vue.set(cd_014.grxxJson, 'aka130', '0202');
                        } else {
                            Vue.set(cd_014.grxxJson, 'aka130', '0201');
                        }
                        Vue.set(cd_014, 'gsdataset_tran', cd_014.grxxJson.gsdataset);
                        if (cd_014.grxxJson.grzhye && cd_014.grxxJson.grzhye.row) {
                            Vue.set(cd_014.grxxJson, 'ykc303', cd_014.grxxJson.grzhye.row.ykc303);
                            Vue.set(cd_014.grxxJson, 'ykc194', cd_014.grxxJson.grzhye.row.ykc194);
                        }
                        //离休信息展示
                        if (cd_014.grxxJson.lxdataset) {
                            Vue.set(cd_014.grxxJson, 'ykc303', cd_014.grxxJson.grzhye.row.ykc303);
                            Vue.set(cd_014.grxxJson, 'ykc194', cd_014.grxxJson.grzhye.row.ykc194);
                        }
                        Vue.set(cd_014.grxxJson, 'skrgx', '本人');
                        if (this.ryfbmc && this.ryfbmc.indexOf("门特") != -1) {
                            var str_param = {
                                ghxh: rightVue.mzjbxxContent.ghxh,
                                zt: '0'
                            };
                            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=querydjxx&parm="
                                + JSON.stringify(str_param),
                                function (json) {
                                    if (json.a == 0) {
                                        cd_014.grxxJson.akc190 = JSON.parse(json.d).akc190;
                                        cd_014.$forceUpdate()
                                        malert("读卡成功!", "right", "success");
                                    } else {
                                        malert("查询失败  " + json.c, 'right', 'defeadted');
                                    }
                                });
                        } else {
                            cd_014.$forceUpdate()
                            malert("读卡成功!", "right", "success");
                        }
                    } else {
                        malert(json.astr_appmasg, "right", "defeadted");
                    }

                    //});
                });
            } else {
                cd_014.ifclick = true;
                malert("医保控件未初始化,请重新打开页面！", 'right', 'defeadted');
            }
        },
        //引入
        enter: function () {
            if (Object.keys(cd_014.grxxJson).length === 0) {
                malert("请先读卡", "right", "defeadted");
                return;
            }
            if (!cd_014.grxxJson.aka130) {
                malert("请选择支付类型", "right", "defeadted");
                return;
            }
            //个人信息
            rightVue.gzyhybContent = cd_014.grxxJson;
            //门诊诊断信息
            rightVue.gzyhybContent.jbbm = this.zdxxJson.jbbm;
            //支付类别
            rightVue.gzyhybContent.aka130 = this.grxxJson.aka130;
            //备注
            rightVue.gzyhybContent.bzsm = this.zdxxJson.bzsm;
            //个人编号,用于结算各种判断
            rightVue.gzyhybContent.grbh = cd_014.grxxJson.aac001;

            popTable.isShow = false;
            malert("引入成功！", "right", "success");
        },
        getFy: function () {
            //处理费用
            var fylist = [];
            var brfyList = [];
            var fyze = 0.00;
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                if (rightVue.brfyjsonList[i].fydj > 0) {
                    var fyparam = {};
                    fyparam.fyid = new Date().getTime() + i;
                    fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                    fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                    fyparam.fysl = String(rightVue.brfyjsonList[i].fysl);

                    fyparam.yka097 = rightVue.brfyjsonList[i].mzks;
                    fyparam.yka098 = rightVue.brfyjsonList[i].mzksmc;
                    fyparam.ykf008 = rightVue.brfyjsonList[i].mzys;
                    fyparam.yka099 = rightVue.brfyjsonList[i].mzysxm;
                    fyparam.yke123 = rightVue.brfyjsonList[i].sfsj || new Date();
                    fyparam.akc225 = String(rightVue.brfyjsonList[i].fydj);

                    brfyList.push(fyparam);
                    fyze += rightVue.brfyjsonList[i].fyje;
                }
            }
            var requestParameters = '{"list":' + JSON.stringify(brfyList) + '}';
            this.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=queryMzfy", requestParameters, function (json) {
                if (json.a == '0') {
                    fylist = eval('(' + json.d + ')');
                } else {
                    malert(json.c, 'right', 'defeadted');
                    return false;
                }
            });
            // var obj = {},fyObj={};
            // var fyObj = fylist.reduce((cur, next) => {
            // 		obj[next.yzhm] && obj[next.zxks] ? "" : obj[next.yzhm] = true && cur.push(next);
            // 		return cur;
            // 	}, []) //设置cur默认类型为数组，并且初始值为空的数组
        },
        //门诊预结算方法
        mzyjs014: function () {
            var fylist = [];
            var brfyList = [];
            var fyze = 0.00;
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                if (rightVue.brfyjsonList[i].fydj > 0) {
                    var fyparam = {};
                    fyparam.fyid = new Date().getTime() + i;
                    fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                    fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                    fyparam.fysl = String(rightVue.brfyjsonList[i].fysl);

                    fyparam.yka097 = rightVue.brfyjsonList[i].mzks;
                    fyparam.yka098 = rightVue.brfyjsonList[i].mzksmc;
                    fyparam.ykf008 = rightVue.brfyjsonList[i].mzys;
                    fyparam.yka099 = rightVue.brfyjsonList[i].mzysxm;
                    fyparam.yke123 = rightVue.brfyjsonList[i].sfsj || new Date();
                    fyparam.akc225 = String(rightVue.brfyjsonList[i].fydj);

                    brfyList.push(fyparam);
                    fyze += rightVue.brfyjsonList[i].fyje;
                }
            }
            var requestParameters = '{"list":' + JSON.stringify(brfyList) + '}';
            this.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=queryMzfy", requestParameters, function (json) {
                if (json.a == '0') {
                    fylist = eval('(' + json.d + ')');
                } else {
                    malert(json.c, 'right', 'defeadted');
                    return false;
                }
            });
            if (!fylist || fylist.length <= 0) {
                malert("没有可结算费用！", 'right', 'defeadted');
                return false;
            }
            this.ryfbmc = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['ryfbbm'], 'fbbm', 'fbmc');
            //yab003 -> 省社保(0090)，yab003->市医保（0000-0022），yab003 返回4位代表成都市医保，5位代表省内医保
            if (this.grxxJson.yab003 == '0090' || this.ryfbmc.indexOf('省异地') != -1) {
                this.gwyjs(fylist, fyze.toFixed(2));
            } else if (this.ryfbmc) {

                if (this.ryfbmc.indexOf("门特") != -1) {
                    //门特
                    this.mtjs(fylist, fyze.toFixed(2));
                } else if (this.ryfbmc.indexOf("离休") != -1) {
                    //离休
                    this.lxjs(fylist, fyze.toFixed(2));
                } else if (this.ryfbmc.indexOf("异地") != -1) {
                    //异地门诊
                    var jybh = '';
                    if (this.ryfbmc.indexOf('本省') != -1) {
                        jybh = '11';
                    }
                    this.ydjs11(fylist, fyze.toFixed(2), jybh);
                } else {
                    //普通
                    this.ptjs(fylist, fyze.toFixed(2));
                }
            } else {
                //普通
                this.ptjs(fylist, fyze.toFixed(2));
            }

        },
        mzyjs014V2: function () {
            var fylist = [];
            var brfyList = [];
            var fyze = 0.00;
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                if (rightVue.brfyjsonList[i].fydj > 0) {
                    var fyparam = {};
                    fyparam.fyid = new Date().getTime() + i;
                    fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                    fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                    fyparam.fysl = String(rightVue.brfyjsonList[i].fysl);

                    fyparam.yka097 = rightVue.brfyjsonList[i].mzks;
                    fyparam.yka098 = rightVue.brfyjsonList[i].mzksmc;
                    fyparam.ykf008 = rightVue.brfyjsonList[i].mzys;
                    fyparam.yka099 = rightVue.brfyjsonList[i].mzysxm;
                    fyparam.yke123 = rightVue.brfyjsonList[i].sfsj || new Date();
                    fyparam.akc225 = String(rightVue.brfyjsonList[i].fydj);

                    brfyList.push(fyparam);
                    fyze += rightVue.brfyjsonList[i].fyje;
                }
            }
            var requestParameters = '{"list":' + JSON.stringify(brfyList) + '}';
            this.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=queryMzfy", requestParameters, function (json) {
                if (json.a == '0') {
                    fylist = eval('(' + json.d + ')');
                } else {
                    malert(json.c, 'right', 'defeadted');
                    return false;
                }
            });
            if (!fylist || fylist.length <= 0) {
                malert("没有可结算费用！", 'right', 'defeadted');
                return false;
            }
            this.ryfbmc = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['ryfbbm'], 'fbbm', 'fbmc')
            // 7815。35
            if (this.grxxJson.ykc117 == '01' || this.grxxJson.yab003 == '0090' || this.ryfbmc.indexOf('省异地') != -1) {
                // this.gwyjs(fylist, fyze.toFixed(2));
                malert("当前用户暂不支持电子健康码！", 'right', 'defeadted');
                return false;
            } else if (this.ryfbmc) {

                if (this.ryfbmc.indexOf("门特") != -1) {
                    //门特
                    // this.mtjs(fylist, fyze.toFixed(2));
                    malert("当前用户暂不支持电子健康码！", 'right', 'defeadted');
                    return false;
                } else if (this.ryfbmc.indexOf("离休") != -1) {
                    //离休
                    // this.lxjs(fylist, fyze.toFixed(2));
                    malert("当前用户暂不支持电子健康码！", 'right', 'defeadted');
                    return false;
                } else if (this.ryfbmc.indexOf("异地") != -1) {
                    // //异地门诊
                    // var jybh = '';
                    // if (this.ryfbmc.indexOf('本省') != -1) {
                    //     jybh = '11';
                    // }
                    // this.ydjs11(fylist, fyze.toFixed(2), jybh);
                    malert("当前用户暂不支持电子健康码！", 'right', 'defeadted');
                    return false;
                } else {
                    //普通
                    return this.ptjsV2(fylist, fyze.toFixed(2));
                }
            } else {
                //普通
                return this.ptjsV2(fylist, fyze.toFixed(2));
            }
        },
        mzyjs014V3: function () {
                        var fylist = [];
            var brfyList = [];
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                if (rightVue.brfyjsonList[i].fydj > 0) {
                    var fyparam = {};
                    fyparam.fyid = new Date().getTime() + i;
                    fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                    fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                    fyparam.fysl = String(rightVue.brfyjsonList[i].fysl);
                    fyparam.yka097 = rightVue.brfyjsonList[i].mzks;
                    fyparam.yka098 = rightVue.brfyjsonList[i].mzksmc;
                    fyparam.ykf008 = rightVue.brfyjsonList[i].mzys;
                    fyparam.yka099 = rightVue.brfyjsonList[i].mzysxm;
                    fyparam.yke123 = rightVue.brfyjsonList[i].sfsj || new Date();
                    fyparam.akc225 = String(rightVue.brfyjsonList[i].fydj);
                    if (typeof (rightVue.brfyjsonList[i].identity) != "undefined") {
                        fyparam.identity = rightVue.brfyjsonList[i].identity;
                    }
                    brfyList.push(fyparam);
                }
            }
            var requestParameters = '{"list":' + JSON.stringify(brfyList) + '}';
            this.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=queryMzfy", requestParameters, function (json) {
                if (json.a == '0') {
                    fylist = eval('(' + json.d + ')');
                } else {
                    malert(json.c, 'top', 'defeadted');
                    return false;
                }
            });


            if (!fylist || fylist.length <= 0) {
                malert("没有可结算费用！", 'top', 'defeadted');
                return false;
            }
            this.ryfbmc = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['ryfbbm'], 'fbbm', 'fbmc');
            //非电子凭证需要先签到，在走医保11 业务
            if (cd_014.isdzps == false) {
                if (!this.qdFun()) {
                    malert("签到失败！", 'top', 'defeadted');
                    return false;
                }
            }
            //去除数量为0 的
            fylist = fylist.filter(function (x) {
                return x.akc226 != 0;
            })
            //查找有医嘱号的
            let yke134List = [];
            fylist.forEach(x => {
                if (typeof (x.yke134) != "undefined") {
                    yke134List.push(x);
                }
            });

            //查找有医嘱号的
            let identityList = [];
            fylist.forEach(x => {
                if (typeof (x.identity) != "undefined") {
                    identityList.push(x);
                }
            });
            //通过医嘱号分组
            let groupYke134 = this.toGroupBy(yke134List, function (item) {
                return [item.yke134];
            });

            //通过identity分组
            let groupIdentity = this.toGroupBy(identityList, function (item) {
                return [item.identity];
            });
            let groupList = groupYke134.concat(groupIdentity);
            return this.feePushToYinhai(groupList);
        },
        //取消
        cd014_quxiao: function () {
            var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jykz = jykz + '<control>';
            jykz = jykz + '<akc190>' + rightVue.yjsContentGzyhyb.akc190 + '</akc190>'; //就诊编码
            jykz = jykz + '<yka103>' + rightVue.yjsContentGzyhyb.yka103 + '</yka103>'; //结算编号
            jykz = jykz + '<aka130>' + rightVue.yjsContentGzyhyb.aka130 + '</aka130>'; //支付类别
            jykz = jykz + '</control>';

            var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jysr = jysr + '<data>';
            jysr = jysr + '<yka055>' + rightVue.yjsContentGzyhyb.yka055 + '</yka055>'; //费用总额
            jysr = jysr + '<yka107>' + rightVue.yjsContentGzyhyb.yka107 + '</yka107>'; //社保基金支付总额
            jysr = jysr + '<yka065>' + rightVue.yjsContentGzyhyb.yka065 + '</yka065>'; //个人帐户支付
            jysr = jysr + '</data>';

            $.post("http://localhost:10014/call", {
                'jybh': "12",
                'jykz_xml': jykz,
                'jysr_xml': jysr,
            }, function (json) {
                //成功后调一次 confirm
                if (json.aint_appcode > 0) {
                    $.post("http://localhost:10014/confirm", {
                        'jylsh': json.astr_jylsh,
                        'jyyzm': json.astr_jyyzm,
                    }, function (json) {
                    });
                } else {
                    malert(json.astr_appmasg, "right", "defeadted");
                }
            });
        },
        gwyjs: function (fylist, fyze) {
            var rowSize = 0;
            var yhFyzh = 0;
            var row = "";
            for (var i = 0; i < fylist.length; i++) {
                var fy = fylist[i];
                if (fy && fy.yka055 && fy.yka094) {// 过滤金额为0的项目
                    yhFyzh = fy.yka055 + yhFyzh;
                    row = row + '<row';
                    row = row + ' yka105="' + fy.yka105 + i + '"';  //记账流水号	NOT NULL	VARCHAR(20)	唯一标识一次就诊akc190中的一条明细
                    row = row + ' yka094="' + fy.yka094 + '"';  //医保项目编码	NOT NULL	VARCHAR(20)	见医保目录信息
                    row = row + ' yka095="' + fy.yka095 + '"';  //医院项目名称	NOT NULL	VARCHAR(100)
                    row = row + ' akc226="' + fy.akc226 + '"';  //数量	NOT NULL	INTEGER	本条明细数量
                    row = row + ' akc225="' + fy.akc225 + '"';  //单价	NOT NULL	NUMBER(14,4)	单价保留四位小数
                    row = row + ' yka055="' + fy.yka055 + '"';  //费用总额	NOT NULL	NUMBER(14,4)	单条明细费用总额保留4位小数，结算时汇总费用总额保留2位小数
                    row = row + ' yke186="' + fy.yke186 + '"';  //医院审核标志	NULL	VARCHAR(4)	需要医院审核后才能报销的项目，门诊暂时为空
                    row = row + ' yka097="' + fy.yka097 + '"';  //开单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka098="' + fy.yka098 + '"';  //开单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka099="' + fy.yka099 + '"';  //开单医生	NULL	VARCHAR(20)
                    row = row + ' yka100="' + fy.yka100 + '"';  //受单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka101="' + fy.yka101 + '"';  //受单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka102="' + fy.yka102 + '"';  //受单医生	NULL	VARCHAR(20)
                    row = row + ' aae011="' + fy.aae011 + '"';  //经办人姓名	NOT NULL	VARCHAR(20)
                    row = row + ' aae036="' + cd_014.fDate(fy.aae036, 'datetime') + '"';  //明细录入时间	NOT NULL	DATETIME	医院收费员录入明细时间
                    row = row + ' yke123="' + cd_014.fDate(fy.yke123, 'datetime') + '"';  //明细发生时间	NOT NULL	DATETIME	明细实际发生时间
                    row = row + ' ykd040="' + fy.ykd040 + '"';  //手术编号	NULL	VARCHAR(20)	门诊暂时为空
                    row = row + ' yke112="' + fy.yke112 + '"';  //医嘱记录序号	NULL	VARCHAR(15)	门诊为空
                    row = row + ' aae013="' + fy.aae013 + '"';  //备注	NULL	VARCHAR(60)
                    row = row + ' yke201="' + fy.yke201 + '"';  //中药使用方式	NULL	VARCHAR(4)	单方、复方参看代码表
                    row = row + ' yke134="' + fy.yke134 + '"';  //处方号	NOT NULL	VARCHAR(15)	处方信息

                    row = row + ' aka067=""';  //最小收费单位	NOT NULL	VARCHAR(15)	 最小收费单位
                    row = row + ' aka067_yn=""';  //本单收费单位	NOT NULL	VARCHAR(15)	 本单收费单位
                    row = row + ' bke026=""';  //处方号	NOT NULL	VARCHAR(15)	 医院内项目编码" 异地就医门诊新增
                    row = row + ' yka121=""';  //开单医生编码	NOT NULL	VARCHAR(15)	 开单医生编码
                    row = row + ' yka122=""';  //受单医生编码	NOT NULL	VARCHAR(15)	 受单医生编码
                    row = row + ' aka074_yn=""';  //医院内规格	NOT NULL	VARCHAR(15)	 医院内规格
                    row = row + ' aka070_yn=""';  //院内收费项目剂型	NOT NULL	VARCHAR(15)	 院内收费项目剂型
                    row = row + ' aka073_mb=""';  //用法"（码表）非必填	NOT NULL	VARCHAR(15)	 用法"（码表）非必填
                    row = row + ' ykf008="' + fy.ykf008 + '"';  //医职人员编码 NOT	NULL	VARCHAR(60)
                    row = row + ' yke683="' + fy.yke134 + '_1"';  //医嘱执行流水号 NOT	NULL	VARCHAR(60)
                    row = row + ' />';
                    rowSize += 1;
                } else {
                    malert("第【" + (i + 1) + "】行，【" + fylist[i].ake005 + "】【" + fylist[i].yka095 + "】未对码，请先对码！", "right", "defeadted");
                    return false;
                }
            }
            var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jykz = jykz + '<control>';
            jykz = jykz + '<akc190>' + (rightVue.gzyhybContent.akc190 || '') + '</akc190>';
            jykz = jykz + '<yke251>' + rightVue.fzContent.ghxh + '</yke251>';//医院内门诊号
            jykz = jykz + '<akc193>' + rightVue.mzjbxxContent.jbbm + '</akc193>';//门诊诊断icd-10
            jykz = jykz + '<yke122>' + rightVue.mzjbxxContent.jbmc + '</yke122>';//诊断信息描述
            jykz = jykz + '<nums>' + rowSize + '</nums>';//  ---按需要结算的记帐流水号汇总的明细条数
            jykz = jykz + '<yka055>' + cd_014.MathFun(yhFyzh) + '</yka055>';//   --按需要结算的记帐流水号汇总的费用总额
            jykz = jykz + '<edition>3</edition>';//  本次接口传入固定值4
            jykz = jykz + '<brbz>' + cd_014.grxxJson.ykc117 + '</brbz>';//  本次接口传入固定值4
            jykz = jykz + '</control>';

            var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jysr = jysr + '<data>' + row + '</data>';
            //去掉所有不合法字符
            jysr = jysr.replace(/undefined/g, "");
            jysr = jysr.replace(/NaN/g, "");
            jysr = jysr.replace(/null/g, "");

            //上传费用
            if (!this.qdFun()) {
                malert("签到失败", "right", "defeadted");
                return false
            }
            ;
            //调用结算方法
            $.ajaxSettings.async = false;
            $.post("http://localhost:10014/call", {'jybh': '11', 'jykz_xml': jykz, 'jysr_xml': jysr},
                function (json) {
                    if (json.aint_appcode > 0) {
                        var output = JSON.parse(json.astr_jysc_xml);
                        if (output.grzhye && Array.isArray(output.grzhye)) {
                            output.grzhye = output.grzhye[0]
                        } else if (output.grzhye && !Array.isArray(output.grzhye)) {
                            output.grzhye = output.grzhye.row
                        }
                        rightVue.yjsContentGzyhyb = {
                            ybfyhj: cd_014.MathFun(yhFyzh),
                            ghxh: rightVue.mzjbxxContent.ghxh,
                            akc190: output.akc190,    //'就诊编码';
                            aka130: output.aka130,    //'支付类别';
                            ykd007: output.ykd007,    //'报销类型';
                            aac001: output.aac001,    //'个人编码';
                            akb020: output.akb020,    //'医院编码';
                            yka103: output.yka103,    //'结算编号';
                            yka055: output.yka055,    //'费用总额';
                            yka056: output.yka056,    //'全自费';
                            yka065: output.yka065,    //'个人帐户支付总额';
                            grzhye: output.grzhye,    //'个人帐户余额';
                            ykc303: output.grzhye.ykc303,    //'个人帐户种类';
                            ykc194: output.grzhye.ykc194,    //'个人帐户余额';
                            yka107: output.yka107,    //'社保基金支付总额';
                            ykh012: output.ykh012,    //'现金及其他自付';
                            yab003: output.yab003,    //'医保经办机构编号';
                            aae011: output.aae011, //经办人姓名
                            aae036: output.aae036, //经办时间
                            yka111: output.yka111, //符合范围
                            yka057: output.yka057, //挂钩自付
                            jslx: output.aka130,//结算类型
                            ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
                            qslbmx: output.dataset.row
                        },
                            rightVue.jylsh = json.astr_jylsh;
                        rightVue.jyyzm = json.astr_jyyzm;
                        rightVue.yjsContentGzyhyb.ifok = true;
                    } else {
                        malert(json.astr_appmasg, "right", "defeadted");
                    }
                });
        },
        //门特结算
        mtjs: function (fylist, fyze) {
            var rowSize = 0;
            var yhFyzh = 0;
            var row = "";
            for (var i = 0; i < fylist.length; i++) {
                var fy = fylist[i];
                if (fy && fy.yka055 && fy.yka094) {// 过滤金额为0的项目
                    row = row + '<row';
                    yhFyzh = fy.yka055 + yhFyzh;
                    row = row + ' yka105="' + fy.yka105 + '"';  //记账流水号	NOT NULL	VARCHAR(20)	唯一标识一次就诊akc190中的一条明细
                    row = row + ' yka094="' + fy.yka094 + '"';  //医保项目编码	NOT NULL	VARCHAR(20)	见医保目录信息
                    row = row + ' yka095="' + fy.yka095 + '"';  //医院项目名称	NOT NULL	VARCHAR(100)
                    row = row + ' akc226="' + fy.akc226 + '"';  //数量	NOT NULL	INTEGER	本条明细数量
                    row = row + ' akc225="' + fy.akc225 + '"';  //单价	NOT NULL	NUMBER(14,4)	单价保留四位小数
                    row = row + ' yka055="' + fy.yka055 + '"';  //费用总额	NOT NULL	NUMBER(14,4)	单条明细费用总额保留4位小数，结算时汇总费用总额保留2位小数
                    row = row + ' yke186="' + fy.yke186 + '"';  //医院审核标志	NULL	VARCHAR(4)	需要医院审核后才能报销的项目，门诊暂时为空
                    row = row + ' yka097="' + fy.yka097 + '"';  //开单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka098="' + fy.yka098 + '"';  //开单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka099="' + fy.yka099 + '"';  //开单医生	NULL	VARCHAR(20)
                    row = row + ' yka100="' + fy.yka100 + '"';  //受单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka101="' + fy.yka101 + '"';  //受单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka102="' + fy.yka102 + '"';  //受单医生	NULL	VARCHAR(20)
                    row = row + ' aae011="' + fy.aae011 + '"';  //经办人姓名	NOT NULL	VARCHAR(20)
                    row = row + ' aae036="' + cd_014.fDate(fy.aae036, 'datetime') + '"';  //明细录入时间	NOT NULL	DATETIME	医院收费员录入明细时间
                    row = row + ' yke123="' + cd_014.fDate(fy.yke123, 'datetime') + '"';  //明细发生时间	NOT NULL	DATETIME	明细实际发生时间
                    row = row + ' ykd040="' + fy.ykd040 + '"';  //手术编号	NULL	VARCHAR(20)	门诊暂时为空
                    row = row + ' yke112="' + fy.yke112 + '"';  //医嘱记录序号	NULL	VARCHAR(15)	门诊为空
                    row = row + ' aae013="' + fy.aae013 + '"';  //备注	NULL	VARCHAR(60)
                    row = row + ' yke201="' + fy.yke201 + '"';  //中药使用方式	NULL	VARCHAR(4)	单方、复方参看代码表
                    row = row + ' yke134="' + fy.yke134 + '"';  //处方号	NOT NULL	VARCHAR(15)	处方信息
                    row = row + ' yka026="' + fy.yka026 + '"';  //病种编码(注释：新门特新增，新门特必传)201308
                    row = row + ' yke676="' + fy.yke676 + '"';  //外检标志	NULL	VARCHAR(6)
                    row = row + ' yke677="' + fy.yke677 + '"';  //外检医院编码	NULL	VARCHAR(20)	对应外检项目所在医院的医院编码，即akb020字段
                    row = row + ' ykf008="' + fy.ykf008 + '"';  //医职人员编码	NOT NULL	VARCHAR(20)	医院自编码，对应医职人员信息上传字段，此处为开单医生编码。
                    row = row + ' yke680="' + fy.yke680 + '"';  //出院带药标志
                    row = row + ' ykf013=""';  //设备编码	NULL	VARCHAR(20)	医院自编码，对应设备信息字段。
                    row = row + ' ake005="' + fy.ake005 + '"';  //医院对码编码	NULL	VARCHAR(20)	医院自编码，对应三目信息上传中的编码
                    row = row + ' yka059="' + fy.yka059 + '"';  //药品本位吗/诊疗项目编码
                    row = row + ' />';
                    rowSize += 1;
                } else {
                    malert("第【" + (i + 1) + "】行，【" + fylist[i].ake005 + "】【" + fylist[i].yka095 + "】未对码，请先对码！", "right", "defeadted");
                    return false;
                }
            }

            //上传费用
            var jykz_41 = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jykz_41 = jykz_41 + '<control>';
            jykz_41 = jykz_41 + '<akc190>' + rightVue.gzyhybContent.akc190 + '</akc190>';//就诊编号
            jykz_41 = jykz_41 + '<aac001>' + rightVue.gzyhybContent.aac001 + '</aac001>';//个人编号
            jykz_41 = jykz_41 + '<aka130>' + rightVue.gzyhybContent.aka130 + '</aka130>';//支付类别
            jykz_41 = jykz_41 + '<yab003>' + rightVue.gzyhybContent.yab003 + '</yab003>';//医保经办机构
            jykz_41 = jykz_41 + '<nums>' + rowSize + '</nums>';//本次写入明细条数
            jykz_41 = jykz_41 + '<yka055>' + cd_014.MathFun(yhFyzh) + '</yka055>';//本次写入明细费用总额
            jykz_41 = jykz_41 + '</control>';

            var jysr_41 = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jysr_41 = jysr_41 + '<data>' + row + '</data>';

            jykz_41 = jykz_41.replace(/undefined/g, "");
            jykz_41 = jykz_41.replace(/NaN/g, "");
            jykz_41 = jykz_41.replace(/null/g, "");

            jysr_41 = jysr_41.replace(/undefined/g, "");
            jysr_41 = jysr_41.replace(/NaN/g, "");
            jysr_41 = jysr_41.replace(/null/g, "");

            //上传费用
            if (!this.qdFun()) {
                malert("签到失败", "right", "defeadted");
                return false
            }
            common.openloading(".cd_014");
            $.post("http://localhost:10014/call", {
                'jybh': '41',
                'jykz_xml': jykz_41,
                'jysr_xml': jysr_41
            }, function (json_41) {
                //上传成功后，进行结算
                if (json_41.aint_appcode > 0) {
                    //拼接疾病
                    let jbbmRows = '<row>';
                    jbbmRows = jbbmRows + '<ykd018>' + rightVue.mzjbxxContent.jbbm + '</ykd018>';//主要用作后期统计
                    jbbmRows = jbbmRows + '<xh>1</xh>';//疾病诊断序号
                    jbbmRows = jbbmRows + '</row>';//主要用作后期统计

                    let xh = 2;
                    var qtzd = rightVue.mzjbxxContent.qtzd;
                    if (qtzd && Array.isArray(JSON.parse(qtzd))) {
                        var qtzd1 = JSON.parse(qtzd)
                        for (let i = 0; i < qtzd1.length; i++) {
                            jbbmRows = jbbmRows + '<row>';//主要用作后期统计
                            jbbmRows = jbbmRows + '<ykd018>' + qtzd1[i].jbmb + '</ykd018>';//主要用作后期统计
                            jbbmRows = jbbmRows + '<xh>' + xh + '</xh>';//疾病诊断序号
                            jbbmRows = jbbmRows + '</row>';//主要用作后期统计
                        }
                    }
                    var jykz_20 = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
                    jykz_20 = jykz_20 + '<control>';
                    jykz_20 = jykz_20 + '<akc190>' + rightVue.gzyhybContent.akc190 + '</akc190>';
                    jykz_20 = jykz_20 + '<aac001>' + rightVue.gzyhybContent.aac001 + '</aac001>';
                    jykz_20 = jykz_20 + '<aka130>' + rightVue.gzyhybContent.aka130 + '</aka130>';
                    jykz_20 = jykz_20 + '<yab003>' + rightVue.gzyhybContent.yab003 + '</yab003>';
                    jykz_20 = jykz_20 + '</control>';

                    var jysr_20 = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
                    jysr_20 = jysr_20 + '<data>';
                    jysr_20 = jysr_20 + '<yka055>' + fyze + '</yka055>';//  ---明细总额
                    jysr_20 = jysr_20 + '<nums>' + rowSize + '</nums>';//  ---不包含退费明细条数
                    jysr_20 = jysr_20 + '<yka110></yka110>';//   --票号
                    jysr_20 = jysr_20 + '<akc194>' + cd_014.fDate(new Date(), 'datetime') + '</akc194>';//  本次接口传入固定值4
                    jysr_20 = jysr_20 + '<ykd018dataset>' + jbbmRows + '</ykd018dataset>';
                    jysr_20 = jysr_20 + '</data>';
                    //去掉所有不合法字符
                    jysr = jysr.replace(/undefined/g, "");
                    jysr = jysr.replace(/NaN/g, "");
                    jysr = jysr.replace(/null/g, "");
                    //调用结算方法
                    $.ajaxSettings.async = false;
                    $.post("http://localhost:10014/call", {
                        'jybh': '20',
                        'jykz_xml': jykz_20,
                        'jysr_xml': jysr_20
                    }, function (data) {
                        if (data.aint_appcode > 0) {
                            var output = JSON.parse(json.astr_jysc_xml);
                            rightVue.yjsContentGzyhyb = {
                                ybfyhj: cd_014.MathFun(yhFyzh),
                                ghxh: rightVue.mzjbxxContent.ghxh,
                                akc190: output.akc190,    //'就诊编码';
                                yka103: output.yka103,    //'结算编号';
                                aka130: output.aka130,    //'支付类别';
                                ykd007: output.ykd007,    //'报销类型';
                                aac001: output.aac001,    //'个人编码';
                                akb020: output.akb020,    //'医院编码';
                                aae011: output.aae011, //经办人姓名
                                aae036: output.aae036, //经办时间
                                yka055: output.yka055,    //'费用总额';
                                yka056: output.yka056,    //'全自费';
                                yka111: output.yka111, //符合范围
                                yka057: output.yka057, //挂钩自付
                                ykc177: output.ykc177, //帐户余额
                                yka107: output.yka107,    //'社保基金支付总额';
                                yka065: output.yka065,    //'个人帐户支付总额';
                                yka719: output.yka719, //现金及其他自付
                                datayka065: output.yka719 == '1' ? output.datayka065.dataset.row : '', //家庭共济账户支付信息
                                num: output.yka719 == '1' ? output.datayka065.num : '', //家庭共济账户支付信息
                                ykh012: output.ykh012, //现金及其他自付
                                yab003: output.yab003,    //'医保经办机构编号';
                                jslx: output.aka130,//结算类型
                                ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
                                qslbmx: output.dataset.row,
                                qslbmxhospital: output.datasethospital.row
                            },
                                rightVue.jylsh = json.astr_jylsh;
                            rightVue.jyyzm = json.astr_jyyzm;
                            rightVue.yjsContentGzyhyb.ifok = true;
                        } else {
                            malert(data.astr_appmasg, "right", "defeadted");
                        }
                    });
                } else {
                    common.closeLoading()
                    malert(json_41.astr_appmasg, "right", "defeadted");
                }
            });
        },
        //离休结算
        lxjs: function (fylist, fyze) {
            var rowSize = 0;
            var yhFyzh = 0;
            var row = "";
            var yzrow = "";
            for (var i = 0; i < fylist.length; i++) {
                var fy = fylist[i];
                if (fy && fy.yka055 && fy.yka094) {
                    yhFyzh = fy.yka055 + yhFyzh;
                    var yke134 = fy.yke134 && fy.yke134.indexOf('Yzh') == 0 ? fy.yke134.substring(0, 15) : fy.yke134;
                    row = row + '<row';
                    row = row + ' yka105="' + fy.yka105 + i + '"';  //记账流水号	NOT NULL	VARCHAR(20)	唯一标识一次就诊akc190中的一条明细
                    row = row + ' yka094="' + fy.yka094 + '"';  //医保项目编码	NOT NULL	VARCHAR(20)	见医保目录信息
                    row = row + ' yka095="' + fy.yka095 + '"';  //医院项目名称	NOT NULL	VARCHAR(100)
                    row = row + ' akc226="' + fy.akc226 + '"';  //数量	NOT NULL	INTEGER	本条明细数量
                    row = row + ' akc225="' + fy.akc225 + '"';  //单价	NOT NULL	NUMBER(14,4)	单价保留四位小数
                    row = row + ' yka055="' + fy.yka055 + '"';  //费用总额	NOT NULL	NUMBER(14,4)	单条明细费用总额保留4位小数，结算时汇总费用总额保留2位小数
                    row = row + ' yke186="' + fy.yke186 + '"';  //医院审核标志	NULL	VARCHAR(4)	需要医院审核后才能报销的项目，门诊暂时为空
                    row = row + ' yka097="' + fy.yka097 + '"';  //开单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka098="' + fy.yka098 + '"';  //开单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka099="' + fy.yka099 + '"';  //开单医生	NULL	VARCHAR(20)
                    row = row + ' yka100="' + fy.yka100 + '"';  //受单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka101="' + fy.yka101 + '"';  //受单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka102="' + fy.yka102 + '"';  //受单医生	NULL	VARCHAR(20)
                    row = row + ' aae011="' + fy.aae011 + '"';  //经办人姓名	NOT NULL	VARCHAR(20)
                    row = row + ' aae036="' + cd_014.fDate(fy.aae036, 'datetime') + '"';  //明细录入时间	NOT NULL	DATETIME	医院收费员录入明细时间
                    row = row + ' yke123="' + cd_014.fDate(fy.yke123, 'datetime') + '"';  //明细发生时间	NOT NULL	DATETIME	明细实际发生时间
                    row = row + ' ykd040="' + fy.ykd040 + '"';  //手术编号	NULL	VARCHAR(20)	门诊暂时为空
                    row = row + ' yke112="' + fy.yka105 + '"';  //医嘱记录序号	NULL	VARCHAR(15)	门诊为空
                    row = row + ' aae013="' + fy.aae013 + '"';  //备注	NULL	VARCHAR(60)
                    row = row + ' yke201="' + fy.yke201 + '"';  //中药使用方式	NULL	VARCHAR(4)	单方、复方参看代码表
                    row = row + ' yke134="' + yke134 + '"';  //处方号	NOT NULL	VARCHAR(15)	处方信息
                    row = row + ' yke553="' + fy.yke553 + '"';  //药品进价	NULL	NUMBER(14,4)	药品进货价格
                    row = row + ' yke676="' + fy.yke676 + '"';  //外检标志	NULL	VARCHAR(6)
                    row = row + ' ake005="' + fy.ake005 + '"';  //医院对码编码	NULL	VARCHAR(20)	医院自编码，对应三目信息上传中的编码
                    // row = row + ' ake005="2225201"';  //医院对码编码	NULL	VARCHAR(20)	医院自编码，对应三目信息上传中的编码
                    row = row + ' yke677="' + fy.yke677 + '"';  //外检医院编码	NULL	VARCHAR(20)	对应外检项目所在医院的医院编码，即akb020字段
                    row = row + ' ykf008="' + fy.ykf008 + '"';  //医职人员编码	NOT NULL	VARCHAR(20)	医院自编码，对应医职人员信息上传字段，此处为开单医生编码。
                    row = row + ' ykf013=""';  //设备编码	NULL	VARCHAR(20)	医院自编码，对应设备信息字段。
                    row = row + ' yka059="' + fy.yka059 + '"';  //药品本位吗/诊疗项目编码
                    //离休上传数据
                    row = row + ' yke351="' + (fy.yke351 || '次') + '"';  //剂量单位
                    row = row + ' yke352="' + (fy.yke352 || 1) + '"';  //剂量
                    row = row + ' yke654="' + (fy.yke654 || 1) + '"';  //每次数量
                    row = row + ' yke655="' + (fy.yke655 || '次') + '"';  //每次数量单位
                    row = row + ' yke350="' + (fy.yke350 || 1) + '"';  //频次
                    row = row + ' yke446="' + (fy.yke446 || 1) + '"';  //使用天数

                    row = row + ' />';

                    //医嘱节点
                    yzrow = yzrow + '<row>';
                    yzrow = yzrow + '<yke112>' + fy.yka105 + '</yke112>';//医嘱记录序号
                    yzrow = yzrow + '<yke113>' + fy.yka095 + '</yke113>';//医嘱内容
                    yzrow = yzrow + '<yka287>' + fy.yka099 + '</yka287>';//医生姓名
                    yzrow = yzrow + '<ykf008>' + fy.ykf008 + '</ykf008>';//医职人员编码
                    yzrow = yzrow + '<aaz307>' + fy.yka097 + '</aaz307>';//医嘱科室编码
                    yzrow = yzrow + '<akf002>' + fy.yka098 + '</akf002>';//医嘱科室名称
                    yzrow = yzrow + '<ake005>' + fy.ake005 + '</ake005>';//医院对码编码
                    // yzrow = yzrow + '<ake005>2225201</ake005>';//医院对码编码
                    yzrow = yzrow + '<yke365></yke365>';//医嘱类别
                    yzrow = yzrow + '<yke658></yke658>';//医嘱分类
                    yzrow = yzrow + '<yke351>' + (fy.yke351 || "次") + '</yke351>';//总剂量单位
                    yzrow = yzrow + '<yke352>' + fy.yke352 + '</yke352>';//总剂量
                    yzrow = yzrow + '<yke355></yke355>';//用药途径
                    yzrow = yzrow + '<yke654>' + fy.yke654 + '</yke654>';//每次数量
                    yzrow = yzrow + '<yke655>' + fy.yke655 + '</yke655>';//每次数量单位
                    yzrow = yzrow + '<yke656>' + fy.yke656 + '</yke656>';//发药量
                    yzrow = yzrow + '<yke657>' + fy.yke657 + '</yke657>';//发药量单位
                    yzrow = yzrow + '<yke350>' + fy.yke350 + '</yke350>';//频次
                    yzrow = yzrow + '<yke446>' + fy.yke446 + '</yke446>';//使用天数
                    yzrow = yzrow + '</row>';
                    rowSize += 1;
                } else {
                    malert("第【" + (i + 1) + "】行，【" + fylist[i].ake005 + "】【" + fylist[i].yka095 + "】未对码，请先对码！", "right", "defeadted");
                    return false;
                }
            }

            var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jykz = jykz + '<control>';
            jykz = jykz + '<akc190></akc190>';
            jykz = jykz + '<aac001>' + rightVue.gzyhybContent.aac001 + '</aac001>';
            jykz = jykz + '<aka130>' + rightVue.gzyhybContent.aka130 + '</aka130>';
            jykz = jykz + '<yab003>' + rightVue.gzyhybContent.yab003 + '</yab003>';
            jykz = jykz + '<nums>' + rowSize + '</nums>';//  ---按需要结算的记帐流水号汇总的明细条数
            jykz = jykz + '<yka055>' + cd_014.MathFun(yhFyzh) + '</yka055>';//   --按需要结算的记帐流水号汇总的费用总额
            jykz = jykz + '<edition>5.0</edition>';//  本次接口传入固定值4
            jykz = jykz + '</control>';
            //rightVue.fyjehj=yhFyzh
            //拼接疾病
            let jbbmRows = '<row>';
            jbbmRows = jbbmRows + '<ykd018>' + rightVue.mzjbxxContent.jbbm + '</ykd018>';//主要用作后期统计
            jbbmRows = jbbmRows + '<yke122>' + rightVue.mzjbxxContent.jbmc + '</yke122>';//疾病描述
            jbbmRows = jbbmRows + '</row>';//主要用作后期统计
            var fjzd = rightVue.mzjbxxContent.fjzd;
            if (fjzd && Array.isArray(JSON.parse(fjzd))) {
                var fjzd1 = JSON.parse(fjzd)
                for (let i = 0; i < fjzd1.length; i++) {
                    jbbmRows = jbbmRows + '<row>';//主要用作后期统计
                    jbbmRows = jbbmRows + '<ykd018>' + fjzd1[i].jbmb + '</ykd018>';//主要用作后期统计
                    jbbmRows = jbbmRows + '<yke122>' + fjzd1[i].jbmc + '</yke122>';//疾病诊断序号
                    jbbmRows = jbbmRows + '</row>';//主要用作后期统计
                }
            }
            var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jysr = jysr + '<data><datasetyz>' + yzrow + '</datasetyz>';
            jysr = jysr + '<datalxzd>' + jbbmRows + '</datalxzd>';
            jysr = jysr + '<datasetmx>' + row + '</datasetmx></data>';
            //签到
            if (!this.qdFun()) {
                malert("签到失败", "right", "defeadted");
                return false
            }
            //去掉所有不合法字符
            jysr = jysr.replace(/undefined/g, "").replace(/NaN/g, "").replace(/null/g, "");
            //调用结算方法
            $.ajaxSettings.async = false;
            $.post("http://localhost:10014/call", {'jybh': '11', 'jykz_xml': jykz, 'jysr_xml': jysr},
                function (json) {
                    if (json.aint_appcode > 0) {
                        var output = JSON.parse(json.astr_jysc_xml);
                        rightVue.yjsContentGzyhyb = {
                            ybfyhj: cd_014.MathFun(yhFyzh),
                            ghxh: rightVue.mzjbxxContent.ghxh,
                            akc190: output.akc190,    //'就诊编码';
                            aka130: output.aka130,    //'支付类别';
                            ykd007: output.ykd007,    //'报销类型';
                            aac001: output.aac001,    //'个人编码';
                            akb020: output.akb020,    //'医院编码';
                            yka103: output.yka103,    //'结算编号';
                            yka055: output.yka055,    //'费用总额';
                            yka056: output.yka056,    //'全自费';
                            grzhye: output.grzhye.row,    //'个人帐户余额';
                            ykc303: output.grzhye.row.ykc303,    //'个人帐户种类';
                            ykc194: output.grzhye.row.ykc194,    //'个人帐户余额';
                            yka065: output.dataset.row.yka065,    //'个人帐户支付总额';
                            yka107: output.dataset.row.yka107,    //'社保基金支付总额';
                            ykh012: output.ykh012,    //'现金及其他自付';
                            yab003: output.yab003,    //'医保经办机构编号';
                            aae011: output.aae011, //经办人姓名
                            aae036: output.aae036, //经办时间
                            yka111: output.yka111, //符合范围
                            yka057: output.yka057, //挂钩自付
                            jslx: output.aka130,//结算类型
                            ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
                            qslbmx: output.dataset.row
                        },
                            rightVue.jylsh = json.astr_jylsh;
                        rightVue.jyyzm = json.astr_jyyzm;
                        rightVue.yjsContentGzyhyb.ifok = true;
                    } else {
                        malert(json.astr_appmasg, "right", "defeadted");
                    }
                });
        },
        //异地结算
        ydjs11: function (fylist, fyze, jybh) {
            var rowSize = 0;
            var yhFyzh = 0;
            var row = "";
            for (var i = 0; i < fylist.length; i++) {
                var fy = fylist[i];
                if (fy && fy.yka055 && fy.yka094) {// 过滤金额为0的项目
                    yhFyzh = fy.yka055 + yhFyzh;
                    row = row + '<row';
                    row = row + ' yka105="' + fy.yka105 + '"';  //记账流水号	NOT NULL	VARCHAR(20)	唯一标识一次就诊akc190中的一条明细
                    row = row + ' yka094="' + fy.yka094 + '"';  //医保项目编码	NOT NULL	VARCHAR(20)	见医保目录信息
                    row = row + ' yka095="' + fy.yka095 + '"';  //医院项目名称	NOT NULL	VARCHAR(100)
                    row = row + ' akc226="' + fy.akc226 + '"';  //数量	NOT NULL	INTEGER	本条明细数量
                    row = row + ' akc225="' + fy.akc225 + '"';  //单价	NOT NULL	NUMBER(14,4)	单价保留四位小数
                    row = row + ' yka055="' + fy.yka055 + '"';  //费用总额	NOT NULL	NUMBER(14,4)	单条明细费用总额保留4位小数，结算时汇总费用总额保留2位小数
                    row = row + ' yke186="' + fy.yke186 + '"';  //医院审核标志	NULL	VARCHAR(4)	需要医院审核后才能报销的项目，门诊暂时为空
                    row = row + ' bkc048="' + fy.ykf008 + '"';  //医生编码
                    row = row + ' yka099="' + fy.yka099 + '"';  //开单医生	NULL	VARCHAR(20)
                    row = row + ' yka097="' + fy.yka097 + '"';  //开单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka098="' + fy.yka098 + '"';  //开单科室名称	NULL	VARCHAR(50)
                    row = row + ' bkc045="' + fy.bkc045 + '"';  //用法
                    row = row + ' bkc044="' + fy.bkc044 + '"';  //用量
                    row = row + ' aae011="' + fy.aae011 + '"';  //经办人姓名	NOT NULL	VARCHAR(20)
                    row = row + ' aka074_yn="' + fy.aka074_yn + '"';  //院内收费项目规格
                    row = row + ' aka070_yn="' + fy.aka070_yn + '"';  //院内收费项目剂型
                    row = row + ' aka067_yn="' + fy.aka067_yn + '"';  //本单收费单位
                    row = row + ' aka067="' + fy.aka067 + '"';  //最小收费单位
                    row = row + ' aka074="' + fy.aka074 + '"';  //与单词用量同单位规格（数值型）
                    row = row + ' />';
                    rowSize += 1;
                } else {
                    malert("第【" + (i + 1) + "】行，【" + fylist[i].ake005 + "】【" + fylist[i].yka095 + "】未对码，请先对码！", "right", "defeadted");
                    return false;
                }
            }
            var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jykz = jykz + '<control>';
            jykz = jykz + '<akc190></akc190>';
            jykz = jykz + '<nums>' + rowSize + '</nums>';//  ---按需要结算的记帐流水号汇总的明细条数
            jykz = jykz + '<yka055>' + cd_014.MathFun(yhFyzh) + '</yka055>';//   --按需要结算的记帐流水号汇总的费用总额
            jykz = jykz + '<edition>5.0</edition>';//  本次接口传入固定值5.0
            jykz = jykz + '<akc193>' + rightVue.mzjbxxContent.jbbm + '</akc193>';
            jykz = jykz + '<bkc021></bkc021>';
            jykz = jykz + '<bkc022></bkc022>';
            jykz = jykz + '<bkc020>' + rightVue.mzjbxxContent.jbmc + '</bkc020>';
            jykz = jykz + '<bkc142></bkc142>';
            jykz = jykz + '<aae011>' + cd_014.userInfo.czyxm + '</aae011>';
            jykz = jykz + '<aac001>' + rightVue.gzyhybContent.aac001 + '</aac001>';
            jykz = jykz + '</control>';

            jykz = jykz.replace(/undefined/g, "");
            jykz = jykz.replace(/NaN/g, "");
            jykz = jykz.replace(/null/g, "");

            var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jysr = jysr + '<data><datasetyka026><row><yka026></yka026></row></datasetyka026>';
            jysr = jysr + '<datasetmx>' + row + '</datasetmx></data>';
            //去掉所有不合法字符
            jysr = jysr.replace(/undefined/g, "");
            jysr = jysr.replace(/NaN/g, "");
            jysr = jysr.replace(/null/g, "");
            //调用结算方法
            if (!this.qdFun()) {
                malert("签到失败", "right", "defeadted");
                return false
            }
            $.ajaxSettings.async = false;
            $.post("http://localhost:10014/call", {'jybh': jybh, 'jykz_xml': jykz, 'jysr_xml': jysr},
                function (json) {
                    if (json.aint_appcode > 0) {
                        var output = JSON.parse(json.astr_jysc_xml);
                        rightVue.yjsContentGzyhyb = {
                            ybfyhj: cd_014.MathFun(yhFyzh),
                            ghxh: rightVue.mzjbxxContent.ghxh,
                            ykc117: cd_014.grxxJson.ykc117,
                            akc190: output.akc190,    //'就诊编码';
                            aka130: output.aka130,    //'支付类别';
                            ykd007: output.ykd007,    //'报销类型';
                            aac001: output.aac001,    //'个人编码';
                            akb020: output.akb020,    //'医院编码';
                            yka103: output.yka103,    //'结算编号';
                            yka055: output.yka055,    //'费用总额';
                            yka056: output.yka056,    //'全自费';
                            grzhye: output.grzhye.row,    //'个人帐户余额';
                            ykc303: output.grzhye.row.ykc303,    //'个人帐户种类';
                            ykc194: output.grzhye.row.ykc194,    //'个人帐户余额';
                            yka065: output.dataset.row.yka065,    //'个人帐户支付总额';
                            yka107: output.dataset.row.yka107,    //'社保基金支付总额';
                            ykh012: output.ykh012,    //'现金及其他自付';
                            yab003: output.yab003,    //'医保经办机构编号';
                            jslx: output.aka130,//结算类型
                            ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
                            qslbmx: output.dataset.row
                        },
                            rightVue.jylsh = json.astr_jylsh;
                        rightVue.jyyzm = json.astr_jyyzm;
                        rightVue.yjsContentGzyhyb.ifok = true;
                    } else {
                        malert(json.astr_appmasg, "right", "defeadted");
                    }
                });
        },
        //普通结算
        ptjs: function (fylist, fyze) {
            var rowSize = 0;
            var yhFyzh = 0;
            var row = "";
            var yzrow = "";
            for (var i = 0; i < fylist.length; i++) {
                var fy = fylist[i];
                if (fy && fy.yka055 && fy.yka094) {
                    yhFyzh = fy.yka055 + yhFyzh;
                    var yke134 = fy.yke134 && fy.yke134.indexOf('Yzh') == 0 ? fy.yke134.substring(0, 15) : fy.yke134;
                    row = row + '<row';
                    row = row + ' yka105="' + fy.yka105 + i + '"';  //记账流水号	NOT NULL	VARCHAR(20)	唯一标识一次就诊akc190中的一条明细
                    row = row + ' yka094="' + fy.yka094 + '"';  //医保项目编码	NOT NULL	VARCHAR(20)	见医保目录信息
                    row = row + ' yka095="' + fy.yka095 + '"';  //医院项目名称	NOT NULL	VARCHAR(100)
                    row = row + ' akc226="' + fy.akc226 + '"';  //数量	NOT NULL	INTEGER	本条明细数量
                    row = row + ' akc225="' + fy.akc225 + '"';  //单价	NOT NULL	NUMBER(14,4)	单价保留四位小数
                    row = row + ' yka055="' + fy.yka055 + '"';  //费用总额	NOT NULL	NUMBER(14,4)	单条明细费用总额保留4位小数，结算时汇总费用总额保留2位小数
                    row = row + ' yke186="' + fy.yke186 + '"';  //医院审核标志	NULL	VARCHAR(4)	需要医院审核后才能报销的项目，门诊暂时为空
                    row = row + ' yka097="' + fy.yka097 + '"';  //开单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka098="' + fy.yka098 + '"';  //开单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka099="' + fy.yka099 + '"';  //开单医生	NULL	VARCHAR(20)
                    row = row + ' yka100="' + fy.yka100 + '"';  //受单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka101="' + fy.yka101 + '"';  //受单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka102="' + fy.yka102 + '"';  //受单医生	NULL	VARCHAR(20)
                    row = row + ' aae011="' + fy.aae011 + '"';  //经办人姓名	NOT NULL	VARCHAR(20)
                    row = row + ' aae036="' + cd_014.fDate(fy.aae036, 'datetime') + '"';  //明细录入时间	NOT NULL	DATETIME	医院收费员录入明细时间
                    row = row + ' yke123="' + cd_014.fDate(fy.yke123, 'datetime') + '"';  //明细发生时间	NOT NULL	DATETIME	明细实际发生时间
                    row = row + ' ykd040="' + fy.ykd040 + '"';  //手术编号	NULL	VARCHAR(20)	门诊暂时为空
                    row = row + ' yke112="' + fy.yka105 + '"';  //医嘱记录序号	NULL	VARCHAR(15)	门诊为空
                    row = row + ' aae013="' + fy.aae013 + '"';  //备注	NULL	VARCHAR(60)
                    row = row + ' yke201="' + fy.yke201 + '"';  //中药使用方式	NULL	VARCHAR(4)	单方、复方参看代码表
                    row = row + ' yke134="' + yke134 + '"';  //处方号	NOT NULL	VARCHAR(15)	处方信息
                    row = row + ' yke553="' + fy.yke553 + '"';  //药品进价	NULL	NUMBER(14,4)	药品进货价格
                    row = row + ' yke676="' + fy.yke676 + '"';  //外检标志	NULL	VARCHAR(6)
                    row = row + ' yke677="' + fy.yke677 + '"';  //外检医院编码	NULL	VARCHAR(20)	对应外检项目所在医院的医院编码，即akb020字段
                    row = row + ' ykf008="' + fy.ykf008 + '"';  //医职人员编码	NOT NULL	VARCHAR(20)	医院自编码，对应医职人员信息上传字段，此处为开单医生编码。
                    row = row + ' ykf013=""';  //设备编码	NULL	VARCHAR(20)	医院自编码，对应设备信息字段。
                    row = row + ' ake005="' + fy.ake005 + '"';  //医院对码编码	NULL	VARCHAR(20)	医院自编码，对应三目信息上传中的编码
                    // row = row + ' ake005="2225201"';  //医院对码编码	NULL	VARCHAR(20)	医院自编码，对应三目信息上传中的编码
                    row = row + ' yka059="' + fy.yka059 + '"';  //药品本位吗/诊疗项目编码
                    row = row + ' />';

                    //医嘱节点
                    yzrow = yzrow + '<row>';
                    yzrow = yzrow + '<yke112>' + fy.yka105 + '</yke112>';//医嘱记录序号
                    yzrow = yzrow + '<yke113>' + fy.yka095 + '</yke113>';//医嘱内容
                    yzrow = yzrow + '<yka287>' + fy.yka099 + '</yka287>';//医生姓名
                    yzrow = yzrow + '<ykf008>' + fy.ykf008 + '</ykf008>';//医职人员编码
                    yzrow = yzrow + '<aaz307>' + fy.yka097 + '</aaz307>';//医嘱科室编码
                    yzrow = yzrow + '<akf002>' + fy.yka098 + '</akf002>';//医嘱科室名称
                    yzrow = yzrow + '<ake005>' + fy.ake005 + '</ake005>';//医院对码编码
                    // yzrow = yzrow + '<ake005>2225201</ake005>';//医院对码编码
                    yzrow = yzrow + '<yke365></yke365>';//医嘱类别
                    yzrow = yzrow + '<yke658></yke658>';//医嘱分类
                    yzrow = yzrow + '<yke351></yke351>';//剂量单位
                    yzrow = yzrow + '<yke352></yke352>';//剂量
                    yzrow = yzrow + '<yke355></yke355>';//用药途径
                    yzrow = yzrow + '<yke654></yke654>';//每次数量
                    yzrow = yzrow + '<yke655></yke655>';//每次数量单位
                    yzrow = yzrow + '<yke656></yke656>';//发药量
                    yzrow = yzrow + '<yke657></yke657>';//发药量单位
                    yzrow = yzrow + '<yke350></yke350>';//频次
                    yzrow = yzrow + '<yke446></yke446>';//使用天数
                    yzrow = yzrow + '</row>';
                    rowSize += 1;
                } else {
                    malert("第【" + (i + 1) + "】行，【" + fylist[i].ake005 + "】【" + fylist[i].yka095 + "】未对码，请先对码！", "right", "defeadted");
                    // return false;
                }
            }
            var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jykz = jykz + '<control>';
            jykz = jykz + '<akc190></akc190>';
            jykz = jykz + '<aac001>' + rightVue.gzyhybContent.aac001 + '</aac001>';
            jykz = jykz + '<aka130>' + rightVue.gzyhybContent.aka130 + '</aka130>';
            jykz = jykz + '<yab003>' + rightVue.gzyhybContent.yab003 + '</yab003>';
            jykz = jykz + '<nums>' + rowSize + '</nums>';//  ---按需要结算的记帐流水号汇总的明细条数
            jykz = jykz + '<yka055>' + cd_014.MathFun(yhFyzh) + '</yka055>';//   --按需要结算的记帐流水号汇总的费用总额
            jykz = jykz + '<edition>5.0</edition>';//  本次接口传入固定值4
            jykz = jykz + '</control>';


            var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jysr = jysr + '<data><datasetyz>' + yzrow + '</datasetyz>';
            jysr = jysr + '<datasetmx>' + row + '</datasetmx></data>';
            //去掉所有不合法字符
            jysr = jysr.replace(/undefined/g, "");
            jysr = jysr.replace(/NaN/g, "");
            jysr = jysr.replace(/null/g, "");


            if (!this.qdFun()) {
                malert("签到失败", "right", "defeadted");
                return false
            }
            //调用结算方法
            $.ajaxSettings.async = false;
            $.post("http://localhost:10014/call", {'jybh': '11', 'jykz_xml': jykz, 'jysr_xml': jysr},
                function (json) {
                    if (json.aint_appcode > 0) {
                        var output = JSON.parse(json.astr_jysc_xml);
                        rightVue.yjsContentGzyhyb = {
                            ybfyhj: cd_014.MathFun(yhFyzh),
                            ghxh: rightVue.mzjbxxContent.ghxh,
                            akc190: output.akc190,    //'就诊编码';
                            aka130: output.aka130,    //'支付类别';
                            ykd007: output.ykd007,    //'报销类型';
                            aac001: output.aac001,    //'个人编码';
                            akb020: output.akb020,    //'医院编码';
                            yka103: output.yka103,    //'结算编号';
                            yka055: output.yka055,    //'费用总额';
                            yka056: output.yka056,    //'全自费';
                            grzhye: output.grzhye.row,    //'个人账户余额';
                            ykc303: output.grzhye.row.ykc303,    //'个人帐户种类';
                            ykc194: output.grzhye.row.ykc194,    //'个人帐户余额';
                            yka065: output.yka065,    //'个人帐户支付总额';
                            yka107: output.yka107,    //'社保基金支付总额';
                            ykh012: output.ykh012,    //'现金及其他自付';
                            yab003: output.yab003,    //'医保经办机构编号';
                            aae011: output.aae011, //经办人姓名
                            aae036: output.aae036, //经办时间
                            yka111: output.yka111, //符合范围
                            yka057: output.yka057, //挂钩自付
                            jslx: output.aka130,//结算类型
                            ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
                            qslbmx: output.dataset.row
                        },
                            rightVue.jylsh = json.astr_jylsh;
                        rightVue.jyyzm = json.astr_jyyzm;
                        rightVue.yjsContentGzyhyb.ifok = true;
                    } else {
                        malert(json.astr_appmasg, "right", "defeadted");
                    }
                });
        },
        pushClinet: function (data, yhFyzh) {
            var $isSuccess = false;
            $.ajax({
                async: false,
                url: 'http://localhost:36658/YinHaiClient/push',
                type: 'GET',
                dataType: 'jsonp',  // 请求方式为jsonp
                contentType: "application/json",
                jsonpCallback: "jsonpCallback",
                data: data,
                complete: function (res) {
                    if (res.status != 200) {
                        malert("医保交易失败", "top", "defeadted");
                    } else {
                        $.ajaxSettings.async = false;
                        var url = "http://localhost:10014/getYbPay?uid=" + data.uid;
                        $.post(url, {}, function (res) {
                                                        if (res.aint_appcode > 0) {
                                var output = JSON.parse(res.astr_jysc_xml);
                                rightVue.yjsContentGzyhyb = {
                                    ybfyhj: cd_014.MathFun(yhFyzh),
                                    ghxh: rightVue.mzjbxxContent.ghxh,
                                    akc190: output.akc190,    //'就诊编码';
                                    aka130: output.aka130,    //'支付类别';
                                    ykd007: output.ykd007,    //'报销类型';
                                    aac001: output.aac001,    //'个人编码';
                                    akb020: output.akb020,    //'医院编码';
                                    yka103: output.yka103,    //'结算编号';
                                    yka055: output.yka055,    //'费用总额';
                                    yka056: output.yka056,    //'全自费';
                                    grzhye: output.grzhye.row,    //'个人账户余额';
                                    ykc303: output.grzhye.row.ykc303,    //'个人帐户种类';
                                    ykc194: output.grzhye.row.ykc194,    //'个人帐户余额';
                                    yka065: output.yka065,    //'个人帐户支付总额';
                                    yka107: output.yka107,    //'社保基金支付总额';
                                    ykh012: output.ykh012,    //'现金及其他自付';
                                    yab003: output.yab003,    //'医保经办机构编号';
                                    aae011: output.aae011, //经办人姓名
                                    aae036: output.aae036, //经办时间
                                    yka111: output.yka111, //符合范围
                                    yka057: output.yka057, //挂钩自付
                                    jslx: output.aka130,//结算类型
                                    ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
                                    qslbmx: output.dataset.row
                                };
                                rightVue.jylsh = res.astr_jylsh;
                                rightVue.jyyzm = res.astr_jyyzm;
                                                                var yka065 = 0, yka107 = 0, ybfyce = 0;
                                if (typeof rightVue.yjsContentGzyhyb.yka065 == 'string') {
                                    yka065 = rightVue.yjsContentGzyhyb.yka065;
                                }
                                if (typeof rightVue.yjsContentGzyhyb.yka107 == 'string') {
                                    yka107 = rightVue.yjsContentGzyhyb.yka107;
                                }
                                popCenter1.jsjlContent.ybkzf = rightVue.fDec(parseFloat(yka065) //个人帐户支付总额
                                    + parseFloat(yka107), 2); //社保基金支付总额
                                ybfyce = rightVue.fDec(popCenter1.jsjlContent.fyhj - popCenter1.jsjlContent.ybkzf, 2);
                                if (Math.abs(ybfyce) < 0.10) {
                                    popCenter1.jsjlContent.ybfyhj = ybfyce;//医保差额
                                }
                                popCenter1.isShow = true;//控制是否显示结算框
                                rightVue.finalMzjs();//门诊结算，弹窗计算放到方法中，
                                $isSuccess = true;
                            } else {
                                //用户取消或交易失败更新，是否电子凭证标识
                                cd_014.isdzps = false;
                                malert(res.aint_appmasg, "top", "defeadted");
                            }
                        });
                    }
                }
            })
            return true;//始终返回true
        },
        pushClinetV2: function (data) {
                        let response = null, fromData = "";
            for (let property in data) {
                fromData += property + "=" + data[property] + "&";
            }
            fromData = fromData.substr(0, fromData.lastIndexOf("&"));
            $.ajax({
                async: false,
                url: 'http://localhost:36658/YinHaiClient/push',
                type: 'GET',
                dataType: 'jsonp',  // 请求方式为jsonp
                contentType: "application/json",
                jsonpCallback: "jsonpCallback",
                data: fromData
            });
            let isContinue = true;
            //因为jsonp 是无法同步，所以只能循环去看后台是否有数据返回
            while (isContinue) {
                this.sleep(1000 * 2);
                $.ajaxSettings.async = false;
                var url = "http://localhost:10014/getYbPay?uid=" + data.uid;
                $.post(url, {}, function (res) {
                    if (res.aint_appcode != -999) {
                        isContinue = false;
                        response = res;
                    }
                });
            }
                        return response;//始终返回true
        },
        ptjsV2: function (fylist, fyze) {
            var $isSuccess = false;
            var rowSize = 0;
            var yhFyzh = 0;
            var row = "";
            var yzrow = "";
            for (var i = 0; i < fylist.length; i++) {
                var fy = fylist[i];
                if (fy && fy.yka055 && fy.yka094) {
                    yhFyzh = fy.yka055 + yhFyzh;
                    var yke134 = fy.yke134 && fy.yke134.indexOf('Yzh') == 0 ? fy.yke134.substring(0, 15) : fy.yke134;
                    row = row + '<row';
                    row = row + ' yka105="' + fy.yka105 + i + '"';  //记账流水号	NOT NULL	VARCHAR(20)	唯一标识一次就诊akc190中的一条明细
                    row = row + ' yka094="' + fy.yka094 + '"';  //医保项目编码	NOT NULL	VARCHAR(20)	见医保目录信息
                    row = row + ' yka095="' + fy.yka095 + '"';  //医院项目名称	NOT NULL	VARCHAR(100)
                    row = row + ' akc226="' + fy.akc226 + '"';  //数量	NOT NULL	INTEGER	本条明细数量
                    row = row + ' akc225="' + fy.akc225 + '"';  //单价	NOT NULL	NUMBER(14,4)	单价保留四位小数
                    row = row + ' yka055="' + fy.yka055 + '"';  //费用总额	NOT NULL	NUMBER(14,4)	单条明细费用总额保留4位小数，结算时汇总费用总额保留2位小数
                    row = row + ' yke186="' + fy.yke186 + '"';  //医院审核标志	NULL	VARCHAR(4)	需要医院审核后才能报销的项目，门诊暂时为空
                    row = row + ' yka097="' + fy.yka097 + '"';  //开单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka098="' + fy.yka098 + '"';  //开单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka099="' + fy.yka099 + '"';  //开单医生	NULL	VARCHAR(20)
                    row = row + ' yka100="' + fy.yka100 + '"';  //受单科室编码	NULL	VARCHAR(20)
                    row = row + ' yka101="' + fy.yka101 + '"';  //受单科室名称	NULL	VARCHAR(50)
                    row = row + ' yka102="' + fy.yka102 + '"';  //受单医生	NULL	VARCHAR(20)
                    row = row + ' aae011="' + fy.aae011 + '"';  //经办人姓名	NOT NULL	VARCHAR(20)
                    row = row + ' aae036="' + cd_014.fDate(fy.aae036, 'datetime') + '"';  //明细录入时间	NOT NULL	DATETIME	医院收费员录入明细时间
                    row = row + ' yke123="' + cd_014.fDate(fy.yke123, 'datetime') + '"';  //明细发生时间	NOT NULL	DATETIME	明细实际发生时间
                    row = row + ' ykd040="' + fy.ykd040 + '"';  //手术编号	NULL	VARCHAR(20)	门诊暂时为空
                    row = row + ' yke112="' + fy.yka105 + '"';  //医嘱记录序号	NULL	VARCHAR(15)	门诊为空
                    row = row + ' aae013="' + fy.aae013 + '"';  //备注	NULL	VARCHAR(60)
                    row = row + ' yke201="' + fy.yke201 + '"';  //中药使用方式	NULL	VARCHAR(4)	单方、复方参看代码表
                    row = row + ' yke134="' + yke134 + '"';  //处方号	NOT NULL	VARCHAR(15)	处方信息
                    row = row + ' yke553="' + fy.yke553 + '"';  //药品进价	NULL	NUMBER(14,4)	药品进货价格
                    row = row + ' yke676="' + fy.yke676 + '"';  //外检标志	NULL	VARCHAR(6)
                    row = row + ' yke677="' + fy.yke677 + '"';  //外检医院编码	NULL	VARCHAR(20)	对应外检项目所在医院的医院编码，即akb020字段
                    row = row + ' ykf008="' + fy.ykf008 + '"';  //医职人员编码	NOT NULL	VARCHAR(20)	医院自编码，对应医职人员信息上传字段，此处为开单医生编码。
                    row = row + ' ykf013=""';  //设备编码	NULL	VARCHAR(20)	医院自编码，对应设备信息字段。
                    row = row + ' ake005="' + fy.ake005 + '"';  //医院对码编码	NULL	VARCHAR(20)	医院自编码，对应三目信息上传中的编码
                    // row = row + ' ake005="2225201"';  //医院对码编码	NULL	VARCHAR(20)	医院自编码，对应三目信息上传中的编码
                    row = row + ' yka059="' + fy.yka059 + '"';  //药品本位吗/诊疗项目编码
                    row = row + ' />';

                    //医嘱节点
                    yzrow = yzrow + '<row>';
                    yzrow = yzrow + '<yke112>' + fy.yka105 + '</yke112>';//医嘱记录序号
                    yzrow = yzrow + '<yke113>' + fy.yka095 + '</yke113>';//医嘱内容
                    yzrow = yzrow + '<yka287>' + fy.yka099 + '</yka287>';//医生姓名
                    yzrow = yzrow + '<ykf008>' + fy.ykf008 + '</ykf008>';//医职人员编码
                    yzrow = yzrow + '<aaz307>' + fy.yka097 + '</aaz307>';//医嘱科室编码
                    yzrow = yzrow + '<akf002>' + fy.yka098 + '</akf002>';//医嘱科室名称
                    yzrow = yzrow + '<ake005>' + fy.ake005 + '</ake005>';//医院对码编码
                    // yzrow = yzrow + '<ake005>2225201</ake005>';//医院对码编码
                    yzrow = yzrow + '<yke365></yke365>';//医嘱类别
                    yzrow = yzrow + '<yke658></yke658>';//医嘱分类
                    yzrow = yzrow + '<yke351></yke351>';//剂量单位
                    yzrow = yzrow + '<yke352></yke352>';//剂量
                    yzrow = yzrow + '<yke355></yke355>';//用药途径
                    yzrow = yzrow + '<yke654></yke654>';//每次数量
                    yzrow = yzrow + '<yke655></yke655>';//每次数量单位
                    yzrow = yzrow + '<yke656></yke656>';//发药量
                    yzrow = yzrow + '<yke657></yke657>';//发药量单位
                    yzrow = yzrow + '<yke350></yke350>';//频次
                    yzrow = yzrow + '<yke446></yke446>';//使用天数
                    yzrow = yzrow + '</row>';
                    rowSize += 1;
                } else {
                    malert("第【" + (i + 1) + "】行，【" + fylist[i].ake005 + "】【" + fylist[i].yka095 + "】未对码，请先对码！", "right", "defeadted");
                    // return false;
                }
            }
            var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jykz = jykz + '<control>';
            jykz = jykz + '<akc190></akc190>';
            jykz = jykz + '<aac001>' + rightVue.gzyhybContent.aac001 + '</aac001>';
            jykz = jykz + '<aka130>' + rightVue.gzyhybContent.aka130 + '</aka130>';
            jykz = jykz + '<yab003>' + rightVue.gzyhybContent.yab003 + '</yab003>';
            jykz = jykz + '<nums>' + rowSize + '</nums>';//  ---按需要结算的记帐流水号汇总的明细条数
            jykz = jykz + '<yka055>' + cd_014.MathFun(yhFyzh) + '</yka055>';//   --按需要结算的记帐流水号汇总的费用总额
            jykz = jykz + '<edition>5.0</edition>';//  本次接口传入固定值4
            jykz = jykz + '</control>';


            var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            jysr = jysr + '<data><datasetyz>' + yzrow + '</datasetyz>';
            jysr = jysr + '<datasetmx>' + row + '</datasetmx></data>';
            //去掉所有不合法字符
            jysr = jysr.replace(/undefined/g, "");
            jysr = jysr.replace(/NaN/g, "");
            jysr = jysr.replace(/null/g, "");

            var data = {
                user: this.userInfo.czyxm,
                jgbm: "0000",
                mzjs_kz_xml: jykz,
                mzjs_data_xml: jysr,
                uid: uuid()
            }
            $isSuccess = this.pushClinet(data, yhFyzh);
            return $isSuccess;
        },
        /**
         * 是否离休患者
         * @param baseInfo 患者基本信息（03）
         * @returns {boolean}
         */
        isLxPatient: function (baseInfo) {
            try {
                if (typeof (baseInfo.lxdataset.yke109) != "undefined") {
                    return baseInfo.lxdataset.yke109 == '1';
                }
            } catch (e) {
                return false
            }
            return false;
        },
        /**
         *
         * @param c 控制对象
         * @param item 费用明细
         * @param jybh 机构编码
         * @returns {{jybh, jysr_xml: string, jykz_xml: string}}
         */
        toYinHaiRequestModel: function (c, item, jybh) {
                        //判读是否是市医疗保险
            let isCityInsurance = (typeof (cd_014.grxxJson.yab003) != "undefined" && cd_014.grxxJson.yab003 != '0090' && cd_014.grxxJson.yab003.length == 4);
            let header = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
            let controlXml = '', mxRow = '', yzXml = '', ykd018Xml = '',
                yka065Xml = '';
            //control 控制参数|mx 费用明细|yz 医嘱|ykd018 疾病列表|yka065 共济相关
            let control = null, mx = null, yz = null, ykd018 = null, yka065 = null;
            if (isCityInsurance) {
                control = this.yinHaiRequest.code11.control;
                mx = this.yinHaiRequest.code11.data.dataSetMx;
                ykd018 = this.yinHaiRequest.code11.data.datalxzd;
                yz = this.yinHaiRequest.code11.data.dataSetYz;

            } else {
                control = this.yinHaiRequest.provinceCode11.control;
                mx = this.yinHaiRequest.provinceCode11.data.row;
                ykd018 = this.yinHaiRequest.provinceCode11.ykd018dataset;
                // yka065 = this.yinHaiRequest.provinceCode11.data.datayka065; //共济账户信息
            }

            // 控制
            for (let property in control) {
                var tempXml = '<' + property + '>' + this.emptyReplace(c[control[property]]) + '</' + property + '>';
                controlXml += tempXml;
            }

            //费用明细
            for (let i = 0; i < item.length; i++) {
                let rows = "<row "
                for (let property in mx) {
                    let value = this.emptyReplace(item[i][mx[property]]);
                    //yka094 ,yke134最单长度不可超过15
                    if (property == 'yke134' && value.length > 15) {
                        value = value.substr(value.length - 15);
                    }
                    //特殊处理日期格式
                    if (property == 'aae036' || property == 'yke123') {
                        value = cd_014.fDate(value, 'datetime');
                    }
                    //成都市医保且为离休病人 ，以下字段如果为null   给默认值
                    if (this.isLxPatient(cd_014.grxxJson) && isCityInsurance) {

                        if (property == 'yke352' || property == 'yke654' || property == 'yke350' || property == 'yke446') {
                            value = (value || 1);
                        }
                        if (property == 'yke351' || property == 'yke655') {
                            value = (value || '次');
                        }
                    }

                    // if (property == 'aae013') {
                    //     value = "建筑医院测试用例";
                    // }
                    rows += property + '="' + value + '" '
                }
                rows += "/> "
                mxRow += rows;
            }


            if (rightVue.isNotNullOrEmpty(yz)) {
                //医嘱,直接取item第一个对象即可，因为是按照医嘱做的结算
                for (let property in yz) {
                    let value = this.emptyReplace(item[0][yz[property]]);
                    //yka094 ,yke134最单长度不可超过15
                    if (property == 'yke134' && value.length > 15) {
                        value = value.substr(value.length - 15);
                    }
                    let tempXml = '<' + property + '>' + value + '</' + property + '>';
                    yzXml += tempXml;
                }
                yzXml = '<row>' + yzXml + '</row>';
            }
            if (rightVue.isNotNullOrEmpty(ykd018)) {
                ykd018Xml = "<row>" +
                    "<ykd018>" + this.emptyReplace(rightVue.mzjbxxContent.jbbm) + "</ykd018>" +
                    "<yke122>" + this.emptyReplace(rightVue.mzjbxxContent.jbmc) + "</yke122>" +
                    "</row>";

                var fjzd = rightVue.mzjbxxContent.fjzd;
                if (fjzd && Array.isArray(JSON.parse(fjzd))) {
                    let arr = JSON.parse(fjzd)
                    for (let i = 0; i < arr.length; i++) {
                        ykd018Xml += "<row>" +
                            "<ykd018>" + this.emptyReplace(arr.jbmb) + "</ykd018>" +
                            "<yke122>" + this.emptyReplace(arr.jbmc) + "</yke122>" +
                            "</row>";
                    }
                }
            }
            if (rightVue.isNotNullOrEmpty(yka065)) {
                for (let property in yka065) {
                    var tempXml = '<' + property + '>' + this.emptyReplace(c[control[property]]) + '</' + property + '>';
                    yka065Xml += tempXml;
                }
            }
                        let reuslt = null;
            if (isCityInsurance) {
                reuslt = {
                    'jybh': jybh,
                    'jykz_xml': header + '<control>' + controlXml + '</control>',
                    'jysr_xml': header + '<data>' + '<datasetmx>' + mxRow + '</datasetmx>' + '<datasetyz>' + yzXml + '</datasetyz>' + '<datalxzd>' + ykd018Xml + '</datalxzd>' + '</data>'
                }
            } else {
                reuslt = {
                    'jybh': jybh,
                    'jykz_xml': header + '<control>' + controlXml + '<ykd018dataset>' + ykd018Xml + '</ykd018dataset>' + '</control>',
                    // 'jysr_xml': header + '<data>' + mxRow + '<datayka065>' + yka065Xml + '</datayka065>' + '</data>'
                    'jysr_xml': header + '<data>' + mxRow + '</data>'
                }
            }

            return reuslt;
        },
        /**
         * 医保结算
         * @param control
         * @param item
         * @returns {{success: boolean}}
         */
        ybjs: function (control, item) {
                        let result = {
                isCancel: false,
                request: item
            };
            $.ajaxSettings.async = false;

            let data = this.toYinHaiRequestModel(control, item, '11');
            if (cd_014.isdzps == true) {
                var parame = {
                    user: this.userInfo.czyxm,
                    jgbm: "0000",
                    mzjs_kz_xml: data.jykz_xml,
                    mzjs_data_xml: data.jysr_xml,
                    uid: uuid()
                }
                result.response = this.pushClinetV2(parame)
            } else {
                                $.post("http://localhost:10014/call", data, function (json) {
                    console.log("json" + JSON.stringify(json));
                    result.response = json;
                });
            }
                        return result;
        },
        /**
         * 异地医保结算
         * @param fylist
         * @param fyzh
         */
        ydybjs: function (fylist, fyzh) {
            let result = {
                success: false
            };
            let mzyjsResponseList = [];
            for (let i = 0; i < fylist.length; i++) {
                let fy = fylist[i];
                fy.edition = '5.0';
                fy.yke550 = '1';
                fy.fyzh = cd_014.MathFun(fyzh);
                fy.nums = fylist.length;
                fy.akc193 = rightVue.mzjbxxContent.jbbm;
                fy.bkc020 = rightVue.mzjbxxContent.jbmc;
                fy.aae011 = cd_014.userInfo.czyxm;
                let data = this.toYinHaiRequestModel(fylist[i], 'YD11');
                //调用结算方法
                $.ajaxSettings.async = false;
                $.post("http://localhost:10014/call", data, function (json) {
                    if (json.aint_appcode > 0) {
                        mzyjsResponseList.push(json);
                    }
                });
                rightVue.yjsContentGzyhyb.ifok = true;
            }
            result.success = true;
            result.msg = 'success';
            result.data = mzyjsResponseList;
            return result;
        },
        emptyReplace: function (para) {
            if (para == null || typeof (para) == "undefined") {
                return '';
            }
            return para;
        },
        toGroupBy: function (array, fn) {
                        const groups = {};
            array.forEach(function (item) {
                const group = JSON.stringify(fn(item));
                //这里利用对象的key值唯一性的，创建数组
                groups[group] = groups[group] || [];
                groups[group].push(item);
            });


            //最后再利用map循环处理分组出来
            return Object.keys(groups).map(function (group) {
                return groups[group];
            });
        },
        /**
         * 取消已上传的费用
         * @param x
         */
        rollback: function (x) {
            let parames = {
                'jylsh': x.astr_jylsh,
                'jyyzm': x.astr_jyyzm
            };
            this.postFormAjax("http://localhost:10014/cancel", parames, function (json) {
            });
        },
        /**
         * 费用推送到银海市医保
         * @param fyMap
         * @returns {{code: number, isCancel: boolean, data: []}}
         */
        feePushToYinhai: function (fyMap) {
                        let akc190 = '';//就诊编码，用于多单
            let errorCount = 0;
            let mzyjsResponseList = [];
            //医嘱单费用上传银海医保
            for (let i = 0; i < fyMap.length; i++) {
                let fyzh = 0.00; //计算费用总和
                let item = fyMap[i];//根据医嘱单拆分后的医嘱明细
                //计算总费用
                item.forEach(function (current, i) {
					console.log(fyzh+"-------"+current.yka055)
                    fyzh = cd_014.MathAdd(fyzh, current.yka055);
					console.log("---------------"+fyzh)
                });
                //判读是否是市医疗保险
                let isCityInsurance = (typeof (cd_014.grxxJson.yab003) != "undefined" && cd_014.grxxJson.yab003 != '0090' && cd_014.grxxJson.yab003.length == 4);
                control = {
                    edition: '5.0',
                    yke550: '1',
                    fyzh: cd_014.MathFun(fyzh),
                    nums: item.length,
                    akc190: akc190,
                    aac001: rightVue.gzyhybContent.aac001,
                    aka130: rightVue.gzyhybContent.aka130,
                    yab003: rightVue.gzyhybContent.yab003,
                    // brbz: cd_014.grxxJson.skrgx == '本人' ? 1 : 0,
                    // yka719: cd_014.grxxJson.skrgx == '本人' ? (isCityInsurance == true ? 0 : 2) : 1
                    //不走共济逻辑，给医保全为本人
                    brbz: 1,
                    yka719: (isCityInsurance == true ? 0 : 2)
                };
                try {
                    let result = this.ybjs(control, item);
                    if (result.response.aint_appcode > 0) {
                        let scJson = JSON.parse(result.response.astr_jysc_xml);
                        akc190 = scJson.akc190;
                        mzyjsResponseList.push(result);
                    } else {
                        if (cd_014.isdzps == true) {
                            //电子医保无法回滚，所以暂不使用全成功或全失败的方案
                            mzyjsResponseList.push(result);
                        } else {
                            //如果是有失败的，就不继续上传费用并且回滚已成功的（要么全部成功，要不失败）,非电子医保
                            mzyjsResponseList.forEach(x => {
                                this.rollback(x.response);
                            });
                            //重新赋值 mzyjsResponse 对象,只保留异常对象
                            mzyjsResponseList = [result];
                        }
                        break;
                    }
                } catch (e) {
                    console.log(e);
                    errorCount++;
                }
            }
            // 99: '全部失败',200: '全部成功(通信)', 300: '部分成功',-1:用户放弃
            let code = errorCount == 0 ? 200 : (errorCount == fyMap.length ? 99 : 300);
            return {
                code: code,
                data: mzyjsResponseList,
            };
        },
        sleep: function (delay) {
                        var start = (new Date()).getTime();
            let end = (new Date()).getTime();
            while ((end - start) < delay) {
                end = (new Date()).getTime()
            }
            return true;
        },
        test: function () {
            this.ybjs();
        },
        test1: function () {
                        var url = "http://localhost:10014/getYbPay?uid=1";
            $.post(url, {}, function (res) {
                if (res.aint_appcode > 0) {
                    var output = JSON.parse(res.astr_jysc_xml);
                    rightVue.yjsContentGzyhyb = {
                        ybfyhj: cd_014.MathFun(0),
                        ghxh: rightVue.mzjbxxContent.ghxh,
                        akc190: output.akc190,    //'就诊编码';
                        aka130: output.aka130,    //'支付类别';
                        ykd007: output.ykd007,    //'报销类型';
                        aac001: output.aac001,    //'个人编码';
                        akb020: output.akb020,    //'医院编码';
                        yka103: output.yka103,    //'结算编号';
                        yka055: output.yka055,    //'费用总额';
                        yka056: output.yka056,    //'全自费';
                        grzhye: output.grzhye.row,    //'个人账户余额';
                        ykc303: output.grzhye.row.ykc303,    //'个人帐户种类';
                        ykc194: output.grzhye.row.ykc194,    //'个人帐户余额';
                        yka065: output.yka065,    //'个人帐户支付总额';
                        yka107: output.yka107,    //'社保基金支付总额';
                        ykh012: output.ykh012,    //'现金及其他自付';
                        yab003: output.yab003,    //'医保经办机构编号';
                        aae011: output.aae011, //经办人姓名
                        aae036: output.aae036, //经办时间
                        yka111: output.yka111, //符合范围
                        yka057: output.yka057, //挂钩自付
                        jslx: output.aka130,//结算类型
                        ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
                        qslbmx: output.dataset.row
                    },
                        rightVue.jylsh = res.astrJylsh;
                    rightVue.jyyzm = res.astrJyyzm;
                    rightVue.yjsContentGzyhyb.ifok = true;
                }
            });
        },
        decodeUnicode: function (str) {
            //先把十六进制unicode编码/u替换为%u
            str = str.replace(/\\u/gi, '%u');
            //再把页面中反斜杠替换为空
            str = str.replace(/\\/gi, '');
            return unescape(str);
        }
    }
});


$(document).click(function () {
    if (this.className != 'selectGroup') {
        $(".selectGroup").hide();
    }
    $(".popInfo ul").hide();
});
