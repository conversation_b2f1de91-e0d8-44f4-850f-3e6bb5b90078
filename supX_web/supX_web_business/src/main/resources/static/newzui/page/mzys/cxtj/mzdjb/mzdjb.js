(function () {
    $(".zui-table-view").uitable();
    var dateend = getTodayDateEnd();
    var datestart = getTodayDateBegin();
    var tableInfo = new Vue({
        el: '#kswh',
        //混合js字典庫
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            jsonList: [],
            ksVal: null,
            param: {},
            type: null,
            ksrq: null,
            isCheckedall:false,
            jsrq: null
        },
        mounted: function () {
            //默认加载当前时间
            $("#dbegin").val(datestart);
            $("#dEnd").val(dateend);
        },
        methods: {
            // 选中单条
            checkOne: function (event,index) {
                if(event.srcElement.checked==true){
                    this.isChecked[index] = false;

                }else{
                    this.isChecked[index] = true;
                }
            },

            // 选中全部
            checkAll: function (event) {
                if (event.srcElement.checked==true) {
                    for (var i = 0; i < this.jsonList.length; i++) {
                        Vue.set(this.isChecked,i,true)
                        this.isCheckedall=true
                        // this.isChecked[i] = true;
                    }
                } else {
                    this.isChecked = [];
                    this.isCheckedall=false
                }
            },
            getData: function () {
                this.ksrq = $("#dbegin").val();
                this.jsrq = $("#dEnd").val();
                this.param.ksrq = this.ksrq;
                this.param.jsrq = this.jsrq;
                $.getJSON("/actionDispatcher.do?reqUrl=MzysCxtjBrdjb&types=query&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == "0") {
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                        tableInfo.jsonList = json.d.list;
                        for (var i = 0; i < tableInfo.jsonList.length; i++) {
                            if (tableInfo.jsonList[i].xy_ssy != null && tableInfo.jsonList[i].xy_szy != null) {
                                tableInfo.jsonList[i].xy = tableInfo.jsonList[i].xy_ssy + '/' + tableInfo.jsonList[i].xy_szy;
                            }
                        }
                        tableInfo.ksVal = '0005';
                    }
                });
            },
            print: function () {
                window.print();
            }
        }
    });
//初始化页面需要加载的数据
    tableInfo.getData();

    laydate.render({
        elem: '#dbegin'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,format:'yyyy-MM-dd HH:mm:ss'
        ,done:function (value,data) {
            tableInfo.ksrq=value
        }
    });
    laydate.render({
        elem: '#dEnd'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,format:'yyyy-MM-dd HH:mm:ss'
        ,done:function (value,data) {
            tableInfo.jsrq=value
        }
    });
})();