//收预交金
var syjjInfo = new Vue({
    el: '#syjjInfo',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer,payNo],
    components: {
        'search-table': searchTable,
    },
    data:{
        posxContent:{},
        zyh: '',
        sfzyxh:0,
        indexNum:0,
        isShow:false,
        bxShow:false,
        isShouYJJ: true,
        beginrq:'',
        endrq:'',
        brxxContent:{},
        codeContent:"",//二维码
        pageData:{
            patientName:'',
            hospitalAD:'',
            medicalCardType:'',
            medicalCardNumber: '',
            payTypeHistory: '',
            payType:'',
            note:'',
            brxxInfo:{},
            yjjlList:[],
            yjjlList1:[],
            jjlInfo:{}
        },
        zflxServerList: [],
        zflxList:{},
        yjjlContent:{},
        csqxContent:{},
        them_tran: {
            'brxb': dic_transform.data.brxb_tran,
            'brnldw': dic_transform.data.nldw_tran
        },
        them: {
            '患者ID': 'brid',
            '患者姓名': 'brxm',
            '性别': 'brxb',
            '年龄': 'brnl',
            '挂号日期': 'ghrq',
            '家庭地址': 'jzdmc',
            '身份证号': 'sfzjhm'
        },
        rysearchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
    },
    watch:{

    },
    updated:function (){
        changeWin();
    },
    mounted: function(){
        this.getZflxList()
        this.getCsqx()

    },
    methods:{
        //当输入值后才触发
        change: function (add, type, val) {
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            //住院病人基本信息
                this.brxxContent[type] = val;
               this.page.parm = this.brxxContent[type];
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON('/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=queryCard&parm=' + JSON.stringify(str_param),
                    function (data) {
                        if (data.d.list.length > 0) {
                            if (add) {
                                for (var i = 0; i < data.d.list.length; i++) {
                                    syjjInfo.rysearchCon.push(data.d.list[i]);
                                }
                            } else {
                                syjjInfo.rysearchCon = data.d.list;
                            }
                        }
                        syjjInfo.page.total = data.d.total;
                        syjjInfo.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
        },
        //鼠标双击（病人住院信息）
        ryselectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change(true, 'zyh', this.brxxContent['zyh']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                syjjInfo.pageData.brxxInfo=item;
                this.getBryjjlList(syjjInfo.pageData.brxxInfo.brid,'yjjlList')
                $(".selectGroup").hide();
            }
        },
        //记账项目检索jsje
        changeDown: function (event, type, content, searchCon) {
            if (this[searchCon][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, content, searchCon);
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                syjjInfo.pageData.brxxInfo=this.brxxContent
                this.getBryjjlList(syjjInfo.pageData.brxxInfo.brid,'yjjlList')
            }
        },

        bdyj:function(index){
            this.yjjlContent = this.pageData.yjjlList[index];
            this.isShow=true
        },
        print: function (data) {
            // 查询打印模板
            syjjInfo.printList=data;
            var json = {repname: '补打预交单'};
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 清除打印区域
                syjjInfo.clearArea(json.d[0]);
                // 为打印前生成数据
                syjjInfo.printContent(syjjInfo.printList);
                // 开始打印
                window.print();
//                syjjInfo.LOdopPrint('补打预交','bdyj','printArea','EPSON LQ-590K');

            });
        },
        //初始化页面数据
        initPageData: function(){
            this.pageData = {
                patientName:'',
                hospitalAD:'',
                medicalCardType:'',
                medicalCardNumber: '',
                payType:this.pageData.payType,
                note:'',
                brxxInfo:{},
                yjjlList:{},
                jjlInfo:{}
            };
        },
        refresh: function(){
            syjjInfo.getBryjjlList(syjjInfo.pageData.brxxInfo.brid,'yjjlList')
        },
        //页面加载时自动获取支付类型Dddw数据
        getZflxList: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx", function (json) {
                if (json.a == 0) {
                    syjjInfo.zflxList = json.d.list;
                    syjjInfo.pageData.payType = json.d.list[0].zflxbm;
                    syjjInfo.pageData.zflxmc = json.d.list[0].zflxmc;
                } else {
                    malert(json.c,'top','defeadted');
                    return false;
                }
            });
        },

        showYjjlInfo:function (item) {
            this.isShouYJJ=false;
            if(!syjjInfo.isShouYJJ){
                if (item.tyjjlid) {
                    malert('此记录为退款记录禁止操作','top','defeadted');
                    return false;
                }else {
                    if(syjjInfo.bxShow){
                        this.getBryjjlList(item.brid,'yjjlList')
                    }
                    syjjInfo.getYjjlInfo(item, function(yjjlInfo){
                        syjjInfo.pageData.payType=item.zflxbm
                        syjjInfo.pageData.zflxmc=item.zflxmc
                        syjjInfo.pageData.jjlInfo = item; // 缓存一下病人预交记录详情   在退预交费用的时候使用
                        syjjInfo.pageData.brxxInfo = item; // 缓存一下病人预交记录详情   在退预交费用的时候使用
                        syjjInfo.pageData.jjlInfo.ktje = yjjlInfo.ktje; // 缓存一下病人预交记录详情   在退预交费用的时候使用
                        syjjInfo.pageData.jjlInfo.yyjje = yjjlInfo.yjje; // 缓存一下病人预交记录详情   在退预交费用的时候使用
                        syjjInfo.pageData.jjlInfo.ytje = yjjlInfo.ytje; // 缓存一下病人预交记录详情   在退预交费用的时候使用
                        syjjInfo.pageData.brxxInfo.yjje = yjjlInfo.ktje; // 缓存一下病人预交记录详情   在退预交费用的时候使用
                        syjjInfo.bxShow=false;
                    });
                }
            }
        },
        getCsqx: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify({'ylbm': 'N050022010'})).then(function(json) {
                if (json.a == 0 && json.d) {
                    //获取权限
                    var parm = {
                        ylbm : 'N050022010',
                        ksbm : ksbm
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&&parm=' + JSON.stringify(parm),
                        function(data) {
                            if(data.a == 0) {
                                if(data.d.length > 0) {
                                    for(var i = 0; i < data.d.length; i++) {
                                        var csjson = data.d[i];
                                        switch(csjson.csqxbm) {
                                            case "N05002201004"://退预交款打印发票
                                                if (csjson.csz){
                                                    syjjInfo.csqxContent.N05002201004 = csjson.csz;
                                                }
                                                break;

                                        }
                                    }
                                }
                            } else {
                                malert('权限获取失败' + data.c);
                            }
                        });

                }
            });
        },

        //查询病人的预交记录
        getBryjjlList: function(zyh,yjjlList){
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglFyglYjjl&types=queryMzyj&parm="+JSON.stringify({parm:zyh,beginrq:this.beginrq,endrq:this.endrq}), function (json) {
                if (json.a == 0) {
                    syjjInfo.pageData[yjjlList]=json.d
                } else {
                    malert(json.c,'top','defeadted');
                }
            });
        },
        //查询预交记录详细信息
        getYjjlInfo: function(obj,cb){
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglFyglYjjl&types=queryByIdMz&parm=" + JSON.stringify({yjjlid:obj.yjjlid,brid:obj.brid}), function (yjjlInfo) {
                if (yjjlInfo.a == 0) {
                    if (yjjlInfo.d != null && yjjlInfo.d != "") {
                        cb(yjjlInfo.d);
                    } else {
                        malert('未查到相关记录','top','defeadted');
                    }
                } else {
                    malert(yjjlInfo.c,'top','defeadted');
                }
            });
        },
        saveData:function(){
        },
        goBack:function (){
            location.reload()
        },
        submitFun: function(flag){
            this.indexNum+1;
            if(mconfirm("请确认是否给病人【"+syjjInfo.pageData.brxxInfo.brxm+"】预交操作")){
                if(this.inAjax)return;  //加入状态字段防止重复点击提交按钮引起多次提交表单
                console.log(this.inAjax)
                this.inAjax = true;
                if(!this.pageData.brxxInfo.yjje){
                    if(this.isShouYJJ)malert('请输入预缴费金额','top','defeadted');
                    else malert('请输入退预缴费金额','top','defeadted');
                    this.inAjax = false;
                    return;
                };
                this.saveSf();
            }
        },
        finalSubm:function(){
            if(this.isShouYJJ){ //收预交款
                this.pageData.brxxInfo.ksbm = this.pageData.brxxInfo.ryks;
                this.pageData.brxxInfo.ksmc = this.pageData.brxxInfo.ryksmc;
                this.pageData.brxxInfo.zflxbm = this.pageData.payType;
                this.pageData.brxxInfo.zflxmc = this.pageData.zflxmc;
                this.pageData.brxxInfo.bzsm = this.pageData.note;
                this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglFyglYjjl&types=saveMzyj', '{"list":' + JSON.stringify([ this.pageData.brxxInfo ]) + '}').then(function (data) {
                    if (data.body.a == 0) {
                        syjjInfo.inAjax = false;
                        syjjInfo.printYjfp(data.body.d.ghxh, data.body.d.fphm);
                    } else {
                        syjjInfo.inAjax = false;
                        malert(data.body.c,'top','defeadted');
                    }
                    syjjInfo.inAjax = false;
                }, function (error) {
                    console.log(error);
                    syjjInfo.inAjax = false;
                });
            }

            else{

                    this.pageData.jjlInfo.bzsm=this.pageData.note;
                    this.pageData.jjlInfo.tkje = this.pageData.brxxInfo.yjje;
                    this.pageData.jjlInfo.jkpzh = "";//退款不能直接复制，清空
                    if (syjjInfo.csqxContent.cs05002200120 == "0"){//预交只退本人
                        if (this.pageData.jjlInfo.czybm != userId){
                            malert('不允许退他人收款记录,请联系【'+this.pageData.jjlInfo.czyxm+'】','top','defeadted');
                            this.inAjax = false;
                            return;
                        }
                    }
                    this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglFyglYjjl&types=tyjjeMz', JSON.stringify(this.pageData.jjlInfo)).then(function (data) {
                        if (data.body.a == 0) {

                            malert('退款成功','top','success');

                            syjjInfo.refresh();
                            syjjInfo.inAjax = false;
                        } else {
                            malert(data.body.c,'top','defeadted');
                        }
                        syjjInfo.inAjax = false;
                    }, function (error) {
                        console.log(error);
                        syjjInfo.inAjax = false;
                    });
            }
        },

        //打印预交发票
        printYjfp: function(zyh, fphm){
            var frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3"){
                frpath = "%2F";
            }else{
                frpath = "/";
            }
            var reportlets ="[{reportlet: 'fpdy"+frpath+"mzsf"+frpath+"mtyjp.cpt',yljgbm:'"+jgbm+"',fphm:'"+fphm+"'}]";
            var ZyjsPrint = null;
            window.top.J_tabLeft.csqxparm.csbm = "N010024005";
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm="+ JSON.stringify(window.top.J_tabLeft.csqxparm) , function (json) {
                if (json.a == 0){
                    syjjInfo.inAjax = false;
                    if (json.d != null && json.d != undefined && json.d.length > 0){
                        ZyjsPrint = json.d[0].csz;
                    }
                    if (!FrPrint(reportlets,ZyjsPrint)){
                    }
                }
                syjjInfo.refresh();
            });
        },

        commonResultChange:function(val){
            syjjInfo.pageData.payType = val[0];
            Vue.set(this[val[2][0]], val[3], val[4]);

        },
        openModel:function (){
            this.bxShow=true;
            this.getBryjjlList('','yjjlList1')
        },
        resultFun:function (){
            this.beginrq='';
            this.endrq='';
            this.bxShow=false;
        },
        showTime:function (elem,key) {
            laydate.render({
                elem: '#'+elem
                , show: true, //直接显示
                theme: '#1ab394',
                done: function (value, data) {
                    syjjInfo[key] = value;
                    this.getBryjjlList('','yjjlList1');
                }
            });
        },
        updatedAjax:function (url,data,succFun,errFun) {
            $.ajax({
                type:'POST',
                url:url,
                dataType:'json',
                contentType:'application/json',
                async:true,
                data:data,
                success:succFun,
                error:errFun,
            });
        },
    }
});
