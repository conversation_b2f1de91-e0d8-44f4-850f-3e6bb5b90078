<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>会诊申请管理审核方</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="hzshf.css" rel="stylesheet"/>
</head>

<body class="skin-default flex-container flex-dir-c flex-one">
    <div class="wrapper  percent100" id="wrapper" >
        <div class="panel">
            <div class="tong-top">
                <button v-waves class="tong-btn btn-parmary" @click="goToPage(1)"><i class="iconfont icon-iocn56 icon-cf"></i>刷新</button>
            </div>
            <div class="flex-container padd-b-10 padd-t-10 padd-l-10 flex-align-c">
                <div class="flex-container flex-align-c padd-r-10">
                    <span class="padd-r-5 ft-14">状态</span>
                            <select-input class="wh122" @change-data="resultChange"
                                          :child="bxzt_tran" :index="'zt'" :val="zt"
                                          :name="'zt'" >
                            </select-input>
                </div>
                <div class="flex-container flex-align-c padd-r-10">
                    <span class="padd-r-5 ft-14">科室</span>
                    <select-input :cs="true" @change-data="resultChange" :not_empty="false"
                                  :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="param.ryks"
                                  :name="'param.ryks'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-r-10">
                    <span class="padd-r-5 ft-14">时间段</span>
                        <input type="text" class="zui-input wh122 times "/>
                    <div class="top-zinle padd-r-5 padd-l-5">
                        至
                    </div>
                        <input type="text" class="zui-input wh122 times1 "/>
                </div>
                <div class="flex-container flex-align-c padd-r-10">
                    <span class="padd-r-5 ft-14">检索</span>
                            <input class="zui-input todate wh182 " placeholder="请输入关键字" id="timeVal"/>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-r-10 padd-l-10" v-cloak >
            <div class="zui-table-header">
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>会诊编号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>申请医生</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>申请日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>患者姓名</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>年龄</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>性别</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>身份证号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>受邀科室</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>受邀医生</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body "   @scroll="scrollTable($event)">
                <table class="zui-table zui-collapse">
                    <tbody>
                    <tr v-for="(item,$index) in 10" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" @dblclick="checkDetail('',8)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l">
                                会诊编号 <!--门诊：颜色状态值color-393f，急诊：color-cff5，住院：color-008-->
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">申请医生</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">2017/12/12</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"><i class="text-line color-dsh" @click="Patient">患者姓名</i></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m">年龄</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m">性别</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl">身份证号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">受邀科室</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">受邀医生</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">会诊安排时间</div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" :class="'会诊中' ? 'color-008':'' ">
                                会诊中 <!--会诊中：颜色状态值color-008，待会诊:color-cf3，待诊断：color-c04，已结束:color-ca3-->
                            </div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                    <em class="width30"><i class="iconfont icon-icon72 icon-font25 icon-hover" data-title="安排会诊" @click="arrange('',5)"></i></em>
                                    <em class="width30 padd-t-3"><i class="iconfont icon-iocn29 icon-font20 icon-hover" data-title="拒绝" @click="refuse($index)"></i></em>
                                    <!--<em class="width30"><i class="iconfont icon-icon69 icon-font25 icon-hover" data-title="会诊报告" @click="Group($index)"></i></em>-->
                                    <!--<em class="width30"><i class="iconfont icon-icon74 icon-font25 icon-hover" data-title="诊断报告" @click="Diagnosis($index)"></i></em>-->
                                </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--暂无数据提示,绑数据放开-->
                <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
            </div>

            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l background-f">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <tr v-for="(item, $index) in 10"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()" @dblclick="checkDetail('',8)">
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r background-f">
                <div class="zui-table-header">
                    <table class="zui-table zui-collapse">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in 10"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()" @dblclick="checkDetail('',8)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" :class="'待审核' ? 'color-cff5':'' ">
                                    待审核 <!--待审核：颜色状态值color-cff5，已拒绝:color-cf3，待会诊：color-dlr，会诊中:color-008，待诊断：color-e9，已结束：color-ca3-->
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" >
                                     <span class="flex-center">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                    <em class="width30"><i class="iconfont icon-icon72 icon-font25 icon-hover" data-title="安排会诊" @click="arrange('',5)"></i></em>
                                    <em class="width30 padd-t-3"><i class="iconfont icon-iocn29 icon-font20 icon-hover" data-title="拒绝" @click="refuse($index)"></i></em>
                                    <!--<em class="width30"><i class="iconfont icon-icon69 icon-font25 icon-hover" data-title="会诊报告" @click="Group($index)"></i></em>-->
                                    <!--<em class="width30"><i class="iconfont icon-icon74 icon-font25 icon-hover" data-title="诊断报告" @click="Diagnosis($index)"></i></em>-->
                                </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

    </div>


<script src="hzshf.js"></script>
</body>

</html>
