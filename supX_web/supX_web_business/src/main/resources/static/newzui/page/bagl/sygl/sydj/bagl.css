.tong-btn {
  width: auto;
  min-width: 72px;
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border: none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right: 10px;
}
.font12 {
  font-size: 12px !important;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}
.wh240 {
  width: 240px!important;
}
.wh182 {
  width: 182px !important;
}
.wh100 {
  width: 100px !important;
}
.wh66 {
  width: 66px !important;
}
.wh112 {
  width: 112px !important;
}
.wh120 {
  width: 120px !important;
}
.wh122 {
  width: 122px !important;
}
.wh138 {
  width: 138px !important;
}
.wh200 {
  width: 200px !important;
}
.wh220 {
  width: 220px !important;
}
.wh150 {
  width: 150px !important;
}
.wh1000 {
  width: 80% !important;
}
.wh50 {
  width: 50px !important;
}
.wh70 {
  width: 70px !important;
}
.width162 {
  width: 162px !important;
}
.wh160 {
  width: 160px !important;
}
.wh453 {
  width: 453px !important;
}
.wh247 {
  width: 243px !important;
  display: flex;
  justify-content: start;
  align-items: center;
}
.wh179 {
  width: 179px !important;
}
.wh59 {
  width: 59px !important;
}
.padd {
  padding: 0 !important;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-ed {
  background: #edf2f1 !important;
}
.color-green {
  color: #1abc9c !important;
  font-style: normal;
}
.color-dsh {
  color: #f3b169;
}
.color-ysh {
  color: #45e135;
}
.color-wtg {
  color: #ff4735;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.icon-hs:before {
  color: #757c83;
}
.icon-bj:before {
  top: -2px;
  left: 22px;
}
.icon-sh-h:before {
  top: 0;
  left: 15px;
}
.pop-width420 {
  background: #fff;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.4);
  border-radius: 2px;
  width: 420px;
  padding: 20px;
  box-sizing: border-box;
}
.pop-width420 .bagl-sm {
  width: 100%;
  height: 48px;
  line-height: 48px;
  border: 1px solid #1abc9c;
  box-shadow: 0 0 8px 0 rgba(6, 140, 113, 0.4);
  border-radius: 4px;
  font-size: 22px;
  color: #1abc9c;
  letter-spacing: 2.75px;
  text-align: center;
}
.pop-width420 ::placeholder {
  color: #1abc9c !important;
}
.pop-width420 .icon-width:before {
  right: -10px;
  left: inherit;
  top: -15px;
  cursor: pointer;
}
.pop-width420 .bagl-smq {
  width: 100%;
  display: block;
  padding: 10px 0 10px 0;
  text-align: center;
}
.pop-width420 .bagl-smq img {
  width: 128px;
  height: 128px;
}
.pop-width420 .bagl-smq-text {
  width: 100%;
  display: block;
  float: left;
  text-align: center;
  font-size: 14px;
  color: #a2a7af;
}
.pop-width420 .bagl-js-text {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
}
.pop-width420 .bagl-js-text i {
  width: calc(100% / 4);
  display: block;
}
.pop-width420 .bagl-smcg {
  width: 128px;
  margin: 0 auto;
  height: 128px;
  transition: 0.1s ease-in-out;
  background: #d0eedc;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.pop-width420 .bagl-smcg img {
  width: 68px;
  height: 80px;
}
.pop-width420 .bagl-smcg .bagl-gou {
  position: absolute;
  background: #13a950;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  bottom: 0;
  right: 0;
}
.pop-width420 .bagl-smcg .bagl-gou img {
  width: 16px;
  height: 14px;
}
.pop-width420 .bagl-smsb {
  background: #fcddd9;
}
.pop-width420 .bagl-smsb .bagl-cha {
  background: #f1543f;
}
