var qjindex = '';
var zlxmbm = "";
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
    data: {
        jsonList: [],
        yfkfList: [],
        popContent: {},
        param: {
            page: 1,
            order: 'desc',
            shzfbz: 1,
            kfbm: null,
            rkfs: '01',//01-出库
            beginrq: null,
            endrq: null,
        }
    },

    mounted: function () {
        this.kfList();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()), 'date');
        this.param.endrq = this.fDate(new Date(), 'date');
        laydate.render({
            elem: '#timeVal'
            , theme: '#1ab394'
            , done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            value: this.param.endrq,
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                wrapper.param.endrq = value;
                wrapper.getData();
                //获取一次列表
            }
        });

    },
    updated: function () {
        changeWin()
    },
    methods: {
        getData: function () {
            if (this.popContent.kfbm) {
                this.param.kfbm = this.popContent.kfbm;
            } else {
                this.param.kfbm = null;
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfcxCrcx&types=rk&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                }
            });
        },
        kfList: function () {
            //库房列表
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf', function (data) {
                if (data.a == 0) {
                    wrapper.yfkfList = data.d.list;
                    Vue.set(wrapper.popContent, 'kfbm', data.d.list[0].kfbm);
                    wrapper.getData();
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            Vue.set(this.popContent, 'kfbm', val[0]);
            Vue.set(this.popContent, 'kfmc', val[4]);
            this.getData();
        },
    }
});



