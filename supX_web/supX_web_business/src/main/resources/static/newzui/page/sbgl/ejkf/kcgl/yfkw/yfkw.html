<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药房库位</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper" v-cloak>
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary  icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="searchHc">刷新</button>
            <button class="tong-btn btn-parmary-b" ><i class=" icon-width icon-dcc icon-dc "></i>导出</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药房</label>
                    <div class="zui-input-inline wh122 margin-l-7">
                        <select-input @change-data="resultRydjChange"
                                      :child="kfList" :index="'yfmc'" :index_val="'yfbm'" :val="param.yfbm"
                                      :name="'param.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">库房编码</label>
                    <div class="zui-input-inline margin-l-5">
                        <input class="zui-input  wh240 " @keydown.enter="goToPage(1)" placeholder="请输入关键字" v-model="param.parm"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库位编码</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>药房名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库位名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item, $index) in YfkwList" @click="checkSelect([$index,'some','YfkwList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" @dblclick="edit($index)">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kwbm">
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-left">
                            <div v-text="item.yfmc"></div>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s relative">
                        <div class="title title-width" :data-title="item.kwmc"><i v-text="item.kwmc" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                    </div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pydm"></div></td>
                    <td><div class="zui-table-cell cell-s">
                        <div class="switch">
                            <input  type="checkbox" true-value="0" false-value="1" v-model="item.tybz" disabled/>
                            <label></label>
                        </div>
                    </div>
                    </td>
                    <p v-if="YfkwList.length==0" class="  noData  text-center zan-border">暂无数据...</p>

                </tr>
                </tbody>
            </table>

        </div>
<!--        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>-->

    </div>

</div>
</div>
<div class="side-form ng-hide pop-width" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <span class="span0">
            <i>库位编码</i>
            <input type="text" class="zui-input border-r4" placeholder="自动生成" v-model="popContent.kwbm" @keydown="nextFocus($event)"
                   disabled="disabled"/>
        </span>
        <span class="span0">
            <i>库位名称</i>
            <input type="text" class="zui-input border-r4" v-model="popContent.kwmc" @keydown="nextFocus($event)"
                   @blur="setPYDM(popContent.kwmc,'popContent','pydm')"/>
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input type="text" class="zui-input border-r4 background-h " disabled v-model="popContent.pydm" @keydown="nextFocus($event)"/>
        </span>
        <span  class="margin-top-10 span0">
            <i>状态</i>
            <div class="switch" >
                <input type="checkbox" id="tybz" true-value="0" false-value="1" v-model="popContent.tybz"/>
                <label for="tybz"></label>
            </div>
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<script src="yfkw.js"></script>
</body>

</html>
