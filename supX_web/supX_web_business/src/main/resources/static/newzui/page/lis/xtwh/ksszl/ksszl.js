$(".zui-table-view").uitable();

// 顶部按钮组域
var top = new Vue({
	el : '.top',
	data : {
		
	},
	methods : {
		// 弹出新增侧边栏
		add : function() {
			popup_insert.isShow = true
			$(".checkShow").removeClass('ng-hide');
			popup_insert.title = '新增抗生素资料';
		}
	},
	watch : {

	}
});

// 数据列表组域
var dataList = new Vue({
	el : '.dataList',
	mixins : [ dic_transform, baseFunc, tableBase],
	data : {
		param:{
    		parm : null,
    		rows : 10,
    		page : 1,
    	},
    	list:[]
	},
	 created:function(){
    	 this.queryList();
    },
	methods : {
		queryList : function() {
			$.getJSON("/actionDispatcher.do?reqUrl=LisWswKsszl&types=query&param="+ JSON.stringify(this.param), function(json) {
						console.log(json);
						if (json.a == "0") {
							dataList.list = json.d.list;
							dataList.total = json.d.total;
							dataList.totlePage = Math.ceil(json.d.total/ dataList.param.rows);
						}
					});
			console.log(this.list);
		}
	},
});

// 新增或者修改侧边弹出框
var popup_insert = new Vue({
	el : '.popup_insert',
	mixins : [ dic_transform, baseFunc, tableBase ],
	data : {
		isShow : false,
		title : '',
	},
	methods : {
		// 关闭侧边栏
		close : function() {
			$(".checkShow").addClass('ng-hide');
		},
		// 保存抗生素资料
		save : function() {

		}

	},
});
// 删除弹出框
var popup_delete = new Vue({
	el : '.popup_delete',
	mixins : [ dic_transform, baseFunc, tableBase ],
	data : {

	},
	methods : {

	},
});
