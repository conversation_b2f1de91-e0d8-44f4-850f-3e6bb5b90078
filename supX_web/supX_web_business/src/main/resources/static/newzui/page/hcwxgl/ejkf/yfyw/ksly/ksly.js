var pram = {};
var ylbm = "N040030020012003";
var begin = null;
var end = null;
(function () {
    $(".zui-table-view").uitable();
  //科目 -- 摆药
    var tableInfo = new Vue({
        el: '.cont-right',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            jsonList: [],
            yfkfList: [],
            fylist:[],
            type: 1,
            fylx: 0, //服药方法类型
            bydList:[],
            times:[],
            yfkf: 0, //药房库房信息
            param: {
                page: 1,
                rows: 10,
                order: 'desc',
                yfbm: '',
                ylbm:ylbm,
                ksbm:'',
                beginrq: null,
                endrq: null,
                fylx: 0,
                parm:''
            },
            isChecked:[]
        },
        watch:{
        	'isChecked':function(){
        		console.log(123);
        	}
        },
        methods: {
            //获取数据
            getData: function() {
                common.openloading('.zui-table-view');
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }

                Vue.set(tableInfo.param,'yfbm',toolBar.barContent.yfbm);
                Vue.set(tableInfo.param,'ksbm',toolBar.barContent.ksbm);
                tableInfo.param.beginrq = begin;
                tableInfo.param.endrq = end;

                //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
                $.getJSON("/actionDispatcher.do?reqUrl=HszHlywFymx&types=bydmxwfy&parm=" + JSON.stringify(tableInfo.param), function(json) {
                	if(json.a == 0) {
                        console.log(json.d.list);
                        tableInfo.fylist=json.d.list;
                    } else {
                        malert( json.c,'top','defeadted')
                    }
                });
                common.closeLoading()
            },
            //编辑
            edit:function (index) {
            	hzlydContext.left = false;
                hzlydContext.right = true;
                hzlydContext.ly = true;
            	hzlydContext.mxList = [];
            	hzlydContext.detailList = [];
            	hzlydContext.bydh = [];

            	//hzlydContext.detailList

            	for(var i = 0 ; i < tableInfo.isChecked.length ; i ++){
            		if(tableInfo.isChecked[i] == true){
            			hzlydContext.detailList.push(tableInfo.fylist[i]);
            			var list = tableInfo.fylist[i].fymx;
            			for(var j = 0 ; j < list.length ; j ++){
            				hzlydContext.mxList.push(list[j]);
            			}
            		}
            	}
            	//hzlydContext.bydh.push(tableInfo.fylist[index].bydh);
            	console.log(hzlydContext.detailList);
            	console.log(hzlydContext.mxList);
                hzlydContext.title='摆药明细';
                hzlydContext.open();
            },
            //单击
            checkO: function(index) {
                this.isCheckAll = false;
                this.isChecked = [];
                this.isChecked[index] = true;
            },


        },
    });
    //左侧-发药单
    var rightList=new Vue({
        el: '.cont-left',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            isChecked:[],
            parm:	{
            	ylbm:"003001004",
            	ksbm:null,
            	beginrq:null,
            	endrq:null,
            	fylx:"0"
            },
            jsonList:[]
        },
        methods:{
            edit:function (index) {
                hzlydContext.title='发药明细';
                hzlydContext.mxList = [];
                hzlydContext.detailList = [];
                hzlydContext.left = true;
                hzlydContext.right = false;
                hzlydContext.ly = false;

                var types = "";
                if(toolBar.type == "0") {
                    types = "fydhzfymx"; //发药单汇总
                } else {
                    types = "fydfymx"; //发药单明细
                }

                //parm:{"ylbm":"003001004","ksbm":"0013","fylx":"0","fycfh":"FYD20180207000001"}
                Vue.set(rightList.parm,'ksbm',toolBar.barContent.ksbm);
                Vue.set(rightList.parm,'fycfh',rightList.jsonList[index].fydh);
                $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types="+types+"&parm=" + JSON.stringify(rightList.parm),
                        function(json) {
                            if(json.a == 0) {
                            	console.log("发药");
                            	hzlydContext.mxList = json.d.list;
                            	console.log(json);
                            } else {
                                malert("发药单查询失败：" + json.c,'top','defeadted')
                            }
                        });

                hzlydContext.open();
            },

            getData: function(){
            	Vue.set(rightList.parm,'ksbm',toolBar.barContent.ksbm);
            	rightList.parm.beginrq = begin;
            	rightList.parm.endrq = end;
            	$.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=fydmx&parm=" + JSON.stringify(rightList.parm),
                        function(json) {
                            if(json.a == 0) {
                            	rightList.jsonList = json.d.list;
                            	console.log("发药");
                            	console.log(rightList.jsonList);
                            } else {
                                malert("摆药单查询失败：" + json.c,'top','defeadted')
                            }
                        });
            }
        }
    });
    var toolBar =new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data:{
            yfList:[],
            barContent:{},
            zyksList:[],
            param: {
                page: 1,
                rows: 10,
                order: 'desc',
                yfbm: '',
                ylbm:ylbm,
                ksbm:'',
                beginrq: null,
                endrq: null,
                fylx: 0,
                parm:''
            },
            isChecked:[],
            ztList:[
                {ztbm:'0',ztmc:'全部'},
                {ztbm:'1',ztmc:'待领药'}
            ]
        },
        mounted: function () {
            var myDate=new Date();
            tableInfo.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
            tableInfo.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
            laydate.render({
                elem: '#timeVal',
                eventElem: '.zui-date',
                value: tableInfo.param.beginrq,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    tableInfo.param.beginrq = value;
                }
            });
            laydate.render({
                elem: '#timeVal1',
                eventElem: '.zui-date',
                value: tableInfo.param.endrq,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    tableInfo.param.endrq = value;
                    tableInfo.getData();
                    rightList.getData();
                }
            });
        },
        methods:{
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "ksbm"    :
                        Vue.set(this.barContent, 'ksbm', val[0]);
                        Vue.set(this.barContent, 'ryksmc', val[4]);
                        tableInfo.getData();
                        break;
                    case "yfbm"    :
                        Vue.set(this.barContent, 'yfbm', val[0]);
                        Vue.set(this.barContent, 'yfmc', val[4]);
                        tableInfo.getData();
                        break;
                    case 'ztbm':
                        Vue.set(this.barContent, 'ztbm', val[0]);
                        Vue.set(this.barContent, 'ztmc', val[4]);
                    default:
                        break;
                }
            },

            //获取药房
            getYfbm: function() {
                pram = {
                    "ylbm": ylbm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(pram), function(json) {
                    if(json.a == 0) {
                        toolBar.yfList = json.d.list;
                        if(json.d.list.length > 0) {
                            Vue.set(toolBar.barContent,'yfbm',json.d.list[0].yfbm);
                            Vue.set(toolBar.barContent,'yfmc',json.d.list[0].yfmc);
                            toolBar.GetZyksData();
                        }
                    } else {
                        malert( json.c,'top','defeadted');
                    }
                });
            },
            //页面加载时自动获取住院科室Dddw数据
            GetZyksData: function () {
                var bean = {"zyks": "1"};
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                    if (json.a == 0) {
                        toolBar.zyksList = json.d.list;
                        Vue.set(toolBar.barContent,'ksbm',json.d.list[0].ksbm);
                        tableInfo.getData();
                        rightList.getData();
                        console.log(toolBar.zyksList);
                    } else {
                        malert(json.c, "住院科室列表查询失败",'top','defeadted');

                    }
                });
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    tableInfo.getData();
                }

            },
            //刷新
            sx:function () {
                tableInfo.getData();
            },
            //查询用例科室参数权限
            getCsqx: function() {
                console.log(this.jsonList);
                pram = {
                    ylbm: ylbm,
                    ksbm: this.jsonList.ksbm
                };
                alert("getcsqx");
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(pram), function(json) {
                    if(json.a == 0) {
                        tableInfo.jsonList = json.d.list;
                        console.log(tableInfo.jsonList);
                    } else {
                        malert( json.c,'top','defeadted');
                    }
                });
            },
        }
    });
   var tool = new Vue({
	   el: '.zui-table-tool',
       mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
       data :{

       },
       methods:{
    	   lingyao : function(){
    		    hzlydContext.left = false;
                hzlydContext.right = true;
                hzlydContext.ly = true;
	           	hzlydContext.mxList = [];
	           	hzlydContext.detailList = [];
	           	hzlydContext.bydh = [];


	           	//hzlydContext.detailList
	           	for(var i = 0 ; i < tableInfo.isChecked.length ; i ++){
	           		if(tableInfo.isChecked[i] == true){
	           			hzlydContext.detailList.push(tableInfo.fylist[i]);
	           			var list = tableInfo.fylist[i].fymx;
	           			for(var j = 0 ; j < list.length ; j ++){
	           				hzlydContext.mxList.push(list[j]);
	           			}
	           		}
	           	}
	           	//hzlydContext.bydh.push(tableInfo.fylist[index].bydh);

	               hzlydContext.title='摆药明细';
	               hzlydContext.open();
    	   }
       }
   });
//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;
//发药明细
    var hzlydContext = new Vue({
        el: '#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            isMx: true,
            jsonList: [],
            title:'',
            num:0,
            prTitle:'',
            nums:1,
            barContent:{},
            type: 1,
            fylx: '0', //服药方法类型
            mxListBak:[],
            mxList:[],
            fylist: { bt: "医嘱明细发药单"},
            isOpen:false,
            detailList :[],
            bydh:[],
            right :false,
            left: false,
            ly:false,
            AllList:
                [
                    {'fylx':'0','yfxzmc':'全部'},
                    {'fylx':'1','yfxzmc':'口服'},
                    {'fylx':'2','yfxzmc':'输液'},
                    {'fylx':'3','yfxzmc':'肌注'},
                    {'fylx':'4','yfxzmc':'其他/口服'},
                    {'fylx':'5','yfxzmc':'输液/肌注'},
                    {'fylx':'6','yfxzmc':'输液/肌注/其他'}
                ],

        },
        created:function(){
        	this.bakData();
        },
        methods: {
        	bakData:function(){
        		this.mxListBak=this.mxList;
        	},
        	resultChangeFy:function(val){
        		 if (val[2].length > 1) {
                     if (Array.isArray(this[val[2][0]])) {
                         Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
//                         hzlydContext.changeList(fylx);
                     } else {
                         Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                         if (val[3] != null) {
                             Vue.set(this[val[2][0]], val[3], val[4]);
//                             hzlydContext.changeList(fylx);
                         }
                     }
                 } else {
                     this[val[2][0]] = val[0];
//                     hzlydContext.changeList(fylx);
                 }
                 if (val[1] != null) {
                     this.nextFocus(val[1]);
                 }
        	},
        	//用药方法改变对应LIST变化
//        	changeList:function(type){
//        		switch (type) {
//				case "":
//					hzlydContext.mxList=[];
//					for(var i=0;i<hzlydContext.mxListBak.length;i++){
//						if(){
//
//						}
//					}
//					break;
//				default:
//					break;
//				}
//        	},
            print:function(){
                $("<link>").attr({ rel: "stylesheet",
                    type: "text/css",
                    href: "pr.css",
                    media:"print"
                }).appendTo("head");
                var dom = document.getElementById('zhcx');
                var dom1 = document.getElementById('zhcx1');
                if(this.num==0){
                    hzlydContext.prTitle='明细发药单';
                    $(dom).jqprint({
                        debug: false,
                        importCSS: true,
                        printContainer: true,
                        operaSupport: false
                    });

                }else if(this.num==1){
                    hzlydContext.prTitle='汇总发药单';
                    $(dom1).jqprint({
                        debug: false,
                        importCSS: true,
                        printContainer: true,
                        operaSupport: false
                    });

                }


                // window.print();
            },


            closes:function () {
                hzlydContext.nums=1;
            },
            open: function () {
                hzlydContext.nums=0;
            },
            tabBg:function (index) {
                this.num=index;
                switch (index){
                    case 0:
                    	toolBar.type = 0;
                        break;
                    case 1:
                    	toolBar.type = 1;
                        break;
                    default:
                        break;
                }
            },
            Y_change:function () {
                tableInfo.edit();
            },
            //领药
            lingyao: function() {
                tableInfo.bydList = [];
              /*  for(var i = 0; i < tableInfo.fylist.length; i++) {
                    if(tableInfo.isChecked[i]) {
                        tableInfo.bydList.push({
                            "bydh": tableInfo.fylist[i].bydh
                        });
                    }
                }*/
                for(var i = 0 ; i < hzlydContext.detailList.length ; i ++){
                	tableInfo.bydList.push({"bydh": hzlydContext.detailList[i].bydh});
                }
                if(tableInfo.bydList.length <= 0) {
                    malert("请选摆药单号",'top','defeadted');
                    return;
                }
                tableInfo.isChecked = []; //领药操作后取消领药选择标志
                var json = {
                    "list": tableInfo.bydList
                };
                console.log("开始摆药:" + json);
                this.$http.post('/actionDispatcher.do?reqUrl=YfbYfywBqby&types=kshzfy&ksbm=' + toolBar.barContent.ksbm, JSON.stringify(json))
                    .then(function(data) {
                        console.log(data.body);
                        if(data.body.a == 0) {
                            tableInfo.fylist = [];
                            hzlydContext.mxList = [];
                            hzlydContext.closes();
                            malert("科室汇总药成功!",'top','success');
                            tableInfo.getData();
                            rightList.getData();
                            // window.print();
                        } else {
                            malert( data.body.c,'top','defeadted');
                            console.log(data.body.c);
                        }
                    });
            },
        }
    });

    //tableInfo.getData();
    toolBar.getYfbm();
/*    toolBar.GetZyksData();
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            // wrapper.param.time = value
            tableInfo.param.beginrq = value.slice(0,10);
            tableInfo.param.endrq =value.slice(13,23);
            tableInfo.getData();
        }
    });*/
})();
window.getTime = function(event, type) {
    if(type == 'star') {
        tableInfo.param.beginrq = $(event).val().slice(0,10);
    } else if(type == 'end') {
        tableInfo.param.endrq = $(event).val().slice(12,23);
    }
};





