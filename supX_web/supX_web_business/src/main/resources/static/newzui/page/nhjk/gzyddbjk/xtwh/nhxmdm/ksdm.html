<div id="ksdm">
    <div class="ks_toolMenu flex-container flex-align-c padd-b-10 padd-l-10 padd-r-10">
        <button class="tong-btn btn-parmary" @click="getData()"><span class="fa fa-refresh"></span>刷新</button>
        <button class="tong-btn btn-parmary" @click="remove()"><span class="fa fa-trash-o"></span>删除</button>
        <button class="tong-btn btn-parmary" @click="autoDm()"><span class="fa fa-save"></span>自动对码（项目名称）</button>
        <div class="flex-container flex-align-c margin-l-20">
            <span>检索项目：</span>
            <input type="text" class="zui-input wh180" @keyDown="sschangeDown" v-model="searchtext" id="search"/>
        </div>
    </div>

    <div class="flex-container padd-t-10">
        <div class="zui-table-view margin-r-10 ksbmXm hzList-border flex-container flex-dir-c">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s">保险类别编码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">保险类别名称</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body flex-one over-auto" @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td>
                            <div class="zui-table-cell cell-s">{{item.bxlbbm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.bxlbmc}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="zui-table-view ksbmMx hzList-border flex-container flex-dir-c">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s">科室名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">科室编码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">农合科室名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">农合科室编码</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body  over-auto" @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr @mouseenter="switchIndex('hoverIndex1',true,$index)" @click="checkOne($index),switchIndex('activeIndex1',true,$index)" @dblclick="edit($index)"
                        @mouseleave="switchIndex()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]">
                        <td>
                            <div class="zui-table-cell cell-s">{{item.ksmc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.ksbm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <span v-show="isEdit != $index" v-text="item.nhksmc"></span>
                                <input :id="'mc_'+$index" v-show="isEdit == $index" v-model="item.nhksmc"
                                       @input="searching($index,false,'nhksmc')"
                                       @keyDown="changeDown($index,$event,'text')">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="page"
                                              @click-one="checkedOneOut" @click-two="selectOne">
                                </search-table>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.nhksbm}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>
<script type="application/javascript" src="ksdm.js"></script>
