
<style type="text/css">
    @media print {
        @page {
            size: A4;
            margin: 0;
        }
    }
    #mtglPrint {
        width: 200mm;
        margin: 0 auto;
        /*border-bottom: 1px solid #b3afaf;*/
    }
    #mtglPrint td,#mtglPrint th{
        font-size: 12px;
        color: #333333;
        font-weight: 500;
    }
    .printList{
        page-break-after: always;
        margin: 0.5cm auto;
    }
    #mtglPrint td div{
        min-height: 18px;
    }
    table
    {
        border-collapse: collapse;
        margin: 0 auto;
        text-align: center;
    }
    table td, table th
    {
        border: 1px solid #cad9ea;
        color: #666;
    }
    table thead th
    {
        width: 100px;
    }
    .xmbm{
        width: 65px;
        margin: 0 auto;
    }
    .zlff{
        width: 80px;
    }
    .title{
        text-align: center;
    }
    .header{
        min-width: 50px;
    }
    .headerText{
        width: 20px;
        margin: 0 auto;
    }
    .border-bottom{
     border-bottom: 1px solid #cad9ea;
    }
    .box-content .headerText{
        margin: 0;
        height: 90%;
        padding-top: 15px;
    }
    .wrapper{
        border: none;
    }
</style>
<div  id="mtglPrint">
   <div class="printList" v-for="($item,index) in faList">
       <div class="title">成都市基本医疗保险门诊特殊疾病治疗方案申请表</div>
       <table >
           <thead>
           <tr>
               <th rowspan="3" class="header">
                   <div class=" headerText">本人申请</div>
               </th>
               <th colspan="1">
                   <div class=" ">社保编码</div>
               </th>
               <th >
                   <div class=" ">{{brxxContent.aac001}}</div>
               </th>
               <th >
                   <div class=" ">姓名</div>
               </th>
               <th colspan="2">
                   <div class=" ">{{brxxContent.brxm}}</div>
               </th>
               <th >
                   <div class=" ">性别</div>
               </th>
               <th >
                   <div class=" ">{{brxb_tran[brxxContent.brxb]}}</div>
               </th>
               <th >
                   <div class=" ">年龄</div>
               </th>
               <th >
                   <div class=" ">{{brxxContent.brnl}}{{nldw_tran[brxxContent.nldw]}}</div>
               </th>
           </tr>
           <tr>
               <th colspan="1">
                   <div class=" ">单位编码</div>
               </th>
               <th colspan="3">
                   <div class=" ">{{brxxContent.aab001}}</div>
               </th>
               <th colspan="2">
                   <div class=" ">单位名称</div>
               </th>
               <th colspan="3">
                   <div class=" ">{{brxxContent.aab004}}</div>
               </th>
           </tr>
           <tr>
               <th colspan="1">
                   <div class=" ">医疗机构名称</div>
               </th>
               <th colspan="3">
                   <div class=" ">四川省建筑医院</div>
               </th>
               <th colspan="1">
                   <div class=" ">申请病种</div>
               </th>
               <th colspan="4">
                   <div class=" ">{{sqbz}}</div>
               </th>
           </tr>
           <tr>
               <td :rowspan="rowspan+3">
                   <div class="headerText">治疗机构意见</div>
               </td>
               <td colspan="1">
                   <div class=" ">病情诊断</div>
               </td>
               <td colspan="8">
                   <div class=" ">{{brxxContent.jbzd}}</div>
               </td>
           </tr>

           <tr>
               <td rowspan="2">
                   <div class="xmbm">治疗及药品项目编码</div>
               </td>
               <td rowspan="2" colspan="3">
                   <div class=" ">诊疗及药品项目名称</div>
               </td>
               <td colspan="5">
                   <div class=" ">用法用量</div>
               </td>
           </tr>

           <tr>
               <td colspan="1">
                   <div class=" zlff">用法</div>
               </td>
               <td colspan="1">
                   <div class="zlff">用量</div>
               </td>
               <td colspan="1">
                   <div class="zlff ">周期</div>
               </td>
               <td :rowspan="rowspan+1"  class="box-content tdContent" colspan="3">
                   <div class="border-bottom margin" :style="setHeight">
                       <p class="headerText margin">本人姓名</p>
                       <p class="text-right padd-r-5">年&emsp;月&emsp;日</p>
                   </div>
                   <div class="border-bottom" :style="setHeight">
                       <p class="headerText margin">医生盖章</p>
                       <p class="text-right padd-r-5">年&emsp;月&emsp;日</p>
                   </div>
                   <div class="" :style="setHeight">
                       <p class="headerText margin">医疗机构盖章</p>
                       <p class="text-right padd-r-5">年&emsp;月日</p>
                   </div>
               </td>
           </tr>
           <tr v-for="(item,index) in $item">
               <td >
                   <div class=" ">{{item.zlorypbm}}</div>
               </td>
               <td colspan="3">
                   <div class="whiteSpace">{{item.zlorypmc}}</div>
               </td>
               <td colspan="1">
                   <div class=" ">{{item.aka073}}</div>
               </td>
               <td colspan="1">
                   <div class=" ">{{item.dl}}</div>
               </td>
               <td colspan="1">
                   <div class=" ">{{item.yka368}}</div>
               </td>
           </tr>
           <tr>
               <td colspan="1">
                   <div class=" ">医保经办机构意见</div>
               </td>
               <td colspan="9">
                   <div class=" text-left padd-l-5 padd-r-5 padd-t-5">
                       <div>1、审核有效期为{{fDate(brxxContent.yae170,'date')}}至{{fDate(brxxContent.yae171,'date')}}，期间治疗与{{sqbz}}(病种)相关的费用可以报销。</div>
                       <div>2、报销时需按基本医疗保险抢围进行审核，属于自费项目的费用自理，治疗方案处方用量时严格按照限《处方管理办法》规定执行，超过部分自费。</div>
                       <div>3、审核有效期内如用药及治疗发生变化请及时到医院进行变更申请，未通过申请并上传的变更方案无效</div>
                       <div>4、一个审核期中请病种不超过5个，审核期内不得增加或变更门诊特殊疾病病种，不得更换治疗机构。</div>
                       <div>5、审核有效期3个月满后请及时办理结算，不能提前结算。</div>
                       <div>6、每次治疗时请出示中请表、身份证及社保卡。</div>
                       <div>7、《门像特殊疾病申请表》是医帅处方、再次中请、费用揪销的重要凭证，请妥善保管</div>
                       <div>8、审核期满后续维续滁疗的，需重新办理申请审核手续。</div>
                       <div>9、中断滁疗6个月需要重新进行病种的认定。</div>
                       <div class="text-right">签章  年  月   日</div>
                   </div>
               </td>
           </tr>
           <tr>
               <td colspan="2">
                   <div class=" ">签名</div>
               </td>
               <td colspan="2">
                   <div class=" "></div>
               </td>
               <td colspan="2">
                   <div class=" ">联系电话</div>
               </td>
               <td colspan="1">
                   <div class=" "></div>
               </td>
               <td colspan="1">
                   <div class=" ">备注</div>
               </td>
               <td colspan="2">
                   <div class=" "></div>
               </td>
           </tr>
           </thead>
       </table>
   </div>

</div>
<script type="text/javascript">
    var mtglPrint = new Vue({
        el: '#mtglPrint',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
            offsetHeight:0,
            rowspan:37,
            brxxContent:[
                {
                    faList:[],
                    brxxContent:{}
                }],
            faList:[],
        },
        computed:{
            setHeight:function (){
                var style={};
                style.height=this.offsetHeight / 3+'px';
                return style
            },
            sqbz:function (){
              var sqbz=this.brxxContent.sqbz.reduce(function (oldVal,newVal){
                   return  oldVal+=newVal.yka027+','
                },'');
              return sqbz.substring(0,sqbz.length-1)
            },
        },
        mounted:function (){
        this.brxxContent=mtgl.brxxContent;
        this.brxxContent.sqbz=JSON.parse(this.brxxContent.sqbz)
        this.faList=mtgl.faList
       this.$nextTick(function (){
           this.initDom();
           this.initList();
       })
        },
        methods:{
             arrTrans:function(num, arr) {
                const newArr = [];
                while(arr.length > 0) {
                    newArr.push(arr.splice(0, num));
                }
                return newArr;
            },
            initList:function (){
                var list=JSON.parse(JSON.stringify(this.faList))
                if(list.length<=this.rowspan){
                    for (var i = 0; i < this.rowspan-list.length; i++) {
                        this.faList.push({})
                    }
                    this.faList.push(this.faList.splice(0,this.faList.length))
                }else {
                  this.faList=this.arrTrans(this.rowspan,this.faList);
                    for (var i = 0; i <this.faList.length; i++) {
                        var list1=JSON.parse(JSON.stringify(this.faList[i].length))
                        for (var j = 0; j <this.rowspan-list1 ; j++) {
                            this.faList[i].push({})
                        }
                    }
                }

              this.$nextTick(function (){
                  window.print();
                  $('#mtglPrint').html('')
              })
            },
            initDom:function (){
                this.offsetHeight=$('.tdContent')[0].offsetHeight;
            },
        },
    })
</script>

