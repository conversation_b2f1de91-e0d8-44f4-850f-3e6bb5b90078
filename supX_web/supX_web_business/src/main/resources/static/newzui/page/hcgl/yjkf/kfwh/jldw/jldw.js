var qjindex = '';
var zlxmbm = "";
var wrapper = new Vue({
    el: '.panel',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    methods: {
        //新增
        AddMdel: function () {
            wap.title = '新增材料计量单位';
            wap.open();
            wap.popContent = {
                tybz:0,
            };

        },
        sx: function () {
            yjkmtableInfo.getData();
        },
        //检索查询回车键
        searchHc: function () {
            if (event.keyCode == 13) {
                this.param.page=1
                yjkmtableInfo.getData();
            }
        },
    },
});


var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        popContent: {},
        title: '新增材料计量单位',
        num: 1,
    },

    methods: {
        //关闭
        closes: function () {
            this.num = 1
        },
        open: function () {
            this.num = 0
        },
        //确定
        confirms: function () {
            if (wap.popContent.jldwmc == null) {
                malert("请输入单位名称", 'top', 'defeadted')
                return;
            }
            wap.popContent.lx = '3';
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhJldwbm&types=save&json=" + JSON.stringify(wap.popContent), function (data) {
                if (data.a == 0) {
                    yjkmtableInfo.getData();
                    wap.closes();
                    malert("数据保存成功", 'top', 'success');
                } else {
                    malert("上传数据失败", 'top', 'defeadted');
                }
            })
        },
    }
});

//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;

//科目
var yjkmtableInfo = new Vue({
    el: '.zui-table-view',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        jsonList: [],
    },
    mounted: function () {
        this.getData();
    },
    methods: {
        //进入页面加载列表信息
        getData: function () {
            common.openloading('.zui-table-body')
            var _json = {
                lx: '3'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhJldwbm&types=query&dg=" + JSON.stringify(wrapper.param)+"&json="+JSON.stringify(_json), function (json) {
                yjkmtableInfo.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                yjkmtableInfo.jsonList = json.d.list;
            });
            common.closeLoading()
        },
        //编辑修改根据num判断
        edit: function (num) {
            wap.title = '编辑材料计量单位'
            wap.open();
            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
        },
        remove: function (index) {
            var jldwList = [];
            // for (var i = 0; i < this.isChecked.length; i++) {
            //     if (this.isChecked[i] == true) {
            //         var jldwbm = {
            //             'jldwbm': this.jsonList[i].jldwbm
            //         };
            //         jldwList.push(JSON.stringify(jldwbm));
            //     }
            // }
            var jldwbm = {
                'jldwbm': this.jsonList[index].jldwbm
            };
            jldwList.push(JSON.stringify(jldwbm));
            if (!confirm("请确认是否删除")) {
                return false;
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhJldwbm&types=delete&json=[" + jldwList + "]", function (data) {
                if (data.a == 0) {
                    yjkmtableInfo.getData();
                    malert("删除成功", 'top', 'success')
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            })
        }
    },
});




