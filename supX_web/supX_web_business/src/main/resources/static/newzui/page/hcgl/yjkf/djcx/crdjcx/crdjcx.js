    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
            djList: [], //单据列表
            shzfbz: {
                '0': '未审核',
                '1': '已审核',
                '2': '已作废',
            },
            yfkfList:[],
            param: {
                page: '',
                rows: '',
                total: ''
            },
        },
        mounted:function(){
            this.getKf();
        },
        updated: function () {
            changeWin();
        },
        methods:{
            getKf:function () {
                //库房列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf',
                    function(data) {
                        if(data.a == 0) {
                            wrapper.yfkfList = data.d.list
                            wrapper.param.kfbm = data.d.list[0].kfbm;
                            wrapper.getData()
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
            },
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                Vue.set(this.param, 'kfbm', val[0]);
                Vue.set(this.param, 'kfmc', val[4]);
                rksh.getData();
            },
            //查询单据
            getData: function() {
                $.getJSON('/actionDispatcher.do?reqUrl=YkglKfywCkd&types=queryCkd' + '&parm=' + JSON.stringify(this.param),
                    function(data) {
                        if(data.a == 0) {
                            wrapper.djList = data.d;
                        } else {
                            malert(data.c,'top','defeadted');
                        }

                    });
            },
        }
    });
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            // wrapper.param.time = value
        }
    });






