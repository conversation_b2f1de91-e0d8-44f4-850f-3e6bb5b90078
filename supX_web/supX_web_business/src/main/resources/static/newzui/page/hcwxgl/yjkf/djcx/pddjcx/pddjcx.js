    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, baseFunc, tableBase, mformat],

        data:{
            shzfbz: {
                '0': '未审核',
                '1': '已审核',
                '2': '已作废',
            },
            yfkfList: [], //药房库房列表
            djList: [], //单据列表
        },
        mounted:function(){
            this.getYf();
        },
        updated:function () {
            changeWin()
        },
        methods:{
            getYf:function () {
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf',
                    function(data) {
                        if(data.a == 0) {
                            wrapper.yfkfList = data.d.list;
                            wrapper.param.kfbm = data.d.list[0].kfbm;
                            wrapper.getData();
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
                //库房列表
            },


            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                Vue.set(this.param, 'kfbm', val[0]);
                Vue.set(this.param, 'kfmc', val[4]);
                this.getData();
            },
            //查询单据
            getData: function() {
                $.getJSON('/actionDispatcher.do?reqUrl=YkglKfywPdb&types=pdbList&parm=' + JSON.stringify(this.param),
                    function(data) {
                        if(data.a == 0) {
                            wrapper.djList = data.d;
                        } else {
                            malert(data.c,'top','defeadted');
                        }

                    });
            },
        }
    });

    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            wrapper.param.beginrq = value.slice(0,10);
            wrapper.param.endrq =value.slice(13,23);
        }
    });
