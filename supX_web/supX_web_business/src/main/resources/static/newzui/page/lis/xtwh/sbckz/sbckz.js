    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            centent:'',
            jysbList:'',
            jysbObj:{
            	sbbm:''
            }
        },
        methods:{
            guolu:function () {
                filter.isShow=true;
            },
            Print:function () {
                window.print()
            },
            delOK:function () {
                pop.isShowpopL=true
                pop.isShow=true
                pop.title='系统提示';
                pop.centent='确定删除该科室吗？';
            },
            queryAll:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    if (json.a == 0) {
                    	wrapper.jysbList = json.d.list;
                    } else {
                        malert("获取检验设备列表失败" + json.c);
                        return false;
                    }
                });
            }
        },
        watch:{
        	'jysbObj.sbbm':function(){
        		$.getJSON("/actionDispatcher.do?reqUrl=XtwhJysb&types=queryJysbCkz&param="+JSON.stringify(this.jysbObj), function (json) {
                    if (json.a == 0) {
                    	wapses.jysbCkz = json.d.list;
                    } else {
                        malert("获取检验设备参考值失败" + json.c);
                        return false;
                    }
                });
        	}
        }
    });
    var wapses=new Vue({
        el:'.xmzb-content',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            isFold: false,
            jysbCkz:[]
        },
        methods:{
            //删除当前
            delNow:function () {
                pop.title='系统提示';
                pop.centent='确定删除该行内容吗？';
                pop.isShowpopL=true;
                pop.isShow=true;
            },
            //dbAdd
            dbAdd:function () {
                alert('双击添加');
            },
            getYbbm:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=gltjutil&yq=", function (json) {
            		brzcList.ybbmList=json.d.ybbm;
                });
            },
            //双击编辑
            dbEdit:function (data) {
            	brzcList.pd=data;
            	brzcList.lisSbckzModel=data.lisSbckzModel ==null ? {} : data.lisSbckzModel;
            	brzcList.lisSbckzNlList=data.lisSbckzNlList;
            	brzcList.lisSbckzYblxList=data.lisSbckzYblxList;
            	
            	
            	console.log(data);
            	
            	var cklx=data.cklx;
            	var sjlx=data.sjlx;
            	
            	//通用
            	if(cklx=='1'){
            		//数值
            		if(sjlx=='1'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao-1").show();
            			$(".cankao-1").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao-2").show();
            			$(".cankao-2").siblings('div').hide();
            		}
            		
            	}
            	//性别
            	if(cklx=='2'){
            		//数值
            		if(sjlx=='1'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao1").show();
            			$(".cankao1").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao").show();
            			$(".cankao").siblings('div').hide();
            		}
            		
            	}
            	//年龄 性别
            	if(cklx=='3' || cklx=='4'){
            		//数值
            		if(sjlx=='1'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao4").show();
            			$(".cankao4").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao4").show();
            			$(".cankao4").siblings('div').hide();
            		}
            		
            	}
            	
            	//样本类型
            	if(cklx=='5'){
            		//数值
            		if(sjlx=='1'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao6").show();
            			$(".cankao6").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao5").show();
            			$(".cankao5").siblings('div').hide();
            		}
            	}
                
            	if(cklx=='6'){
            		//数值
            		if(sjlx=='1'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao9").show();
            			$(".cankao9").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao8").show();
            			$(".cankao8").siblings('div').hide();
            		}
            	}
            	
            	if(cklx=='7'){
            		//数值
            		if(sjlx=='1'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao10").show();
            			$(".cankao10").siblings('div').hide();
            		}
            		//文本
            		if(sjlx=='2'){
            			$("#isFold").addClass('side-form-bg');
                        $("#brzcList").removeClass('ng-hide');
            			$(".cankao11").show();
            			$(".cankao11").siblings('div').hide();
            		}
            	}
            }

        },
    });
    
   
    var brzcList=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
        	pd:{},
        	lisSbckzModel:{},
        	lisSbckzNlList:[],
        	lisSbckzYblxList:[],
        	ybbmList:[],
        	
        	
            isTabelShow:false,
            sideTitle:'',
            isFold: false,
            isShowpopL:false,
            isShow:false,
            title:'',
            popContent:{},
            centent:'',
            appNum:[],
            appObj:{},
            item:1,
            numDom:2,
            domlist:1,
            arrDom:'<input class="zui-input"/>',
            pop10:['年龄段','参考值',],
        },
        created:function () {
            this.xinzeng4()
            var clone=$('.clone')
            var absol=$('.absol')
            var dom='<div  style="min-width: 25%;max-width: 25%" class="text-calc dbremove" >'+this.arrDom+'</div>'
            for(var i=0;i<this.pop10.length;i++){
                $(clone).find($(absol)).before(dom)
            }
        },
        computed:{

        },
        methods:{
        	resultChange_item: function (val) {
        	    Vue.set(this.lisSbckzNlList[val[2][0]], [val[2][1]], val[0]);
        	    this.nextFocus(val[1], parseInt(val[2][2]));
        	},
        	
        	resultChangeYblx_item: function (val) {
        	    Vue.set(this.lisSbckzYblxList[val[2][0]], [val[2][1]], val[0]);
        	    this.nextFocus(val[1], parseInt(val[2][2]));
        	},
        	Wf_YppfChange: function (val) {
        	    var index = "";
        	    //先获取到操作的哪一个
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        var types = val[2][val[2].length - 1];
        	        index = val[2][1];
        	        Vue.set(this.lisSbckzYblxList[index], 'ybbm', val[0]);
        	    }
        	    
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        if (window.event.keyCode == 13) {
        	            this.nextFocus(event);
        	        }
        	    }
        	},
            Guid:function () {
              return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g,function (e) {
                  var r=Math.random()*16|10,v=e=='x'?r:(r&0x3|0x8);
                  return v.toString(16);
              })
            },
            sc:function(index){
                this.lisSbckzNlList.splice(index,1)
            },
            xinzeng4:function () {
            	this.lisSbckzNlList.push({});
            },
            xinzeng5:function () {
                this.lisSbckzYblxList.push({});
            },
            xinzeng6:function () {
            	this.lisSbckzYblxList.push({});
            },
            xinzeng8:function () {
            	this.lisSbckzYblxList.push({});
            },
            xinzeng9:function () {
            	this.lisSbckzYblxList.push({});
            },
            xinzeng10:function () {
            	this.lisSbckzYblxList.push({});
                $('#yb4').append($('#clone').clone(true))
            },
            xinzengheader:function () {
                var nl='年龄段';
                this.pop10.push(nl);
                var clone=$('.clone')
                var dom='<div  style="min-width: 25%;max-width: 25%" class="text-calc dbremove" >'+this.arrDom+'</div>'
                for(var i=0;i<clone.length;i++){
                    $(clone).eq(i).append(dom)
                }
            },
            delHeader:function (n) {
                if(this.pop10.length<=2){
                    malert('最少保留两个','bottom','defeadted')
                }else{
                    this.pop10.splice(n,1)
                    var clone=$('.clone')
                    var dbremove=$('.dbremove')
                    for(var i=0;i<clone.length;i++){
                        $(clone).eq(i).find($(dbremove)).eq(n).remove()

                    }
                }

            },
            delok:function (event) {
                $(event.toElement).parents('li').remove()
            },
            // 取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // 确定
            confirms:function () {
               
                
                var cklx=brzcList.pd.cklx;
                
              //通用
            	if(cklx=='1' || cklx=='2'){
            		brzcList.pd.lisSbckzModel.sbbm=wrapper.jysbObj.sbbm;
            		var json='{"obj":' + JSON.stringify(brzcList.pd.lisSbckzModel) + '}';
               	  	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJysb&types=saveJyzbCkz',json).then(
	                   function(data) {
	                   		if(data.body.a==0){
	                            malert('保存结果成功','top','success');
	                            $(".side-form-bg").removeClass('side-form-bg')
	                            $(".side-form").addClass('ng-hide');
	                   		}
	                    }, 
	                    function(error) {
	                    	malert(error,'top','success');
	                    });
            	}
            	//年龄 性别
            	if(cklx=='3' || cklx=='4'){
            		for (var int = 0; int < brzcList.pd.lisSbckzNlList.length; int++) {
            			brzcList.pd.lisSbckzNlList[int].sbbm=wrapper.jysbObj.sbbm;
            			brzcList.pd.lisSbckzNlList[int].zbbm=brzcList.pd.zbbm;
					}
            		var json='{"list":' + JSON.stringify(brzcList.pd.lisSbckzNlList) + '}';
               	  	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJysb&types=saveJyzbCkzNl',json).then(
	                   function(data) {
	                   		if(data.body.a==0){
	                            malert('保存结果成功','top','success');
	                            $(".side-form-bg").removeClass('side-form-bg')
	                            $(".side-form").addClass('ng-hide');
	                   		}
	                    }, 
	                    function(error) {
	                    	malert(error,'top','success');
	                    });
            		
            	}
            	//样本类型
            	if(cklx=='5' || cklx=='6' || cklx=='7'){
            		for (var int = 0; int < brzcList.pd.lisSbckzYblxList.length; int++) {
            			brzcList.pd.lisSbckzYblxList[int].sbbm=wrapper.jysbObj.sbbm;
            			brzcList.pd.lisSbckzYblxList[int].zbbm=brzcList.pd.zbbm;
					}
            		var json='{"list":' + JSON.stringify(brzcList.pd.lisSbckzYblxList) + '}';
               	  	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJysb&types=saveJyzbCkzYblx',json).then(
	                   function(data) {
	                   		if(data.body.a==0){
	                            malert('保存结果成功','top','success');
	                   		}
	                    }, 
	                    function(error) {
	                    	malert(error,'top','success');
	                    });
            		
            	}
                
            }
        }
    });
    
    
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                malert('删除成功','top','success');
            },
            colse:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                malert('取消删除成功','top','defeadted');
            }

        }
    });
    function delok(event) {
        $(event).parents('li').remove()
    }


wrapper.queryAll();
wapses.getYbbm();
