<%@ page language="java" import="java.util.*" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>
<html>
<head>
<title>Title</title>
<%@include file="/pub/top.js"%>
<title>处方划价</title>
<link href="/demoCss.css" rel="stylesheet" type="text/css" />
<link href="cfhj.css" rel="stylesheet" type="text/css" />
</head>

<style type="text/css">
.popTable input {
	height: 25px;
}
</style>


<body>

	<div id="test" style="height: 2px">
		<transition name="progress">
		<div class="progressBar" v-show="isDone"
			:class="{isDoneStyle: isDone}"></div>
		</transition>
	</div>

	<!-- 主体 -->
	<div style="width: 100%;">
		<!-- 功能按钮 -->
		
		<div id="ToolBar"  class="toolMenu" style="display: block;">
			<div style="float:left;width:100px;margin-right:10px" >
			    <select v-model="yfContent.yfbm" @change="Wf_YfChange($event)">
			    <option v-for="v in jsonList" v-bind:value="v.yfbm"
			    v-bind:ksbm="v.ksbm"
			    v-bind:cflx="v.cflx"
				v-text="v.yfmc"  ></option>
			    </select>
			</div>
		    <div style="left: 300px">
			<button @click="getData">
				<span class="fa fa-refresh"></span>刷新
			</button>
			<button @click="newData">
				<span class="fa fa-plus"></span>新增处方
			</button>
			<button @click="addData">
				<span class="fa fa-plus"></span>添加一行
			</button>
			<button @click="saveData">
				<span class="fa fa-edit"></span>保存
			</button>
			
			
			<span style="color: #FF0000;font-size:14px;">{{yfContent.hzlx == '0'?'门诊划价':'住院划价'}} [F8切换]	 
				{{yfContent.kfbz == '1' ? '已收费':'未收费'}}</span>
			</div>
		</div>

		<!-- 编辑区 -->
		<div 
			style="width: 75%; float: left; border-right: solid; 1 px green; padding: 15px 10px 0px 0px;">
			<div id="cfhjEdit">
			<div  class="tablePfDiv">
				<table class="popTable cfhj_popTable" cellspacing="0" cellpadding="0">
					<tbody class="part_w">
						<tr class="trTitle">
							<td class="partTitle" colspan="3">患者检索区</td>
						</tr>
						<tr>
							<th class="is_empty">检索:</th>
							<td style="width: 25%">
								<input id="bah" data-notEmpty="true" v-model="CfContent.bah" :readonly="readonly"
								@keyup="changeDown($event,'bah','CfContent','hzsearchCon')"
								@input="change($event,'bah')">
							<hzsearch-table :message="hzsearchCon" :selected="selSearch" :total="hztotal" :them="hzthem" :them_tran="them_tran"
								@click-one="checkedOneOut" @click-two="dblclickbah" ></hzsearch-table>
							</td>
							<th class="is_empty">姓名:</th>
							<td style="width: 25%">
							  <input data-notEmpty="true" v-model="CfContent.brxm":readonly="readonly"
							  	 @keyup="changeDown($event)" ></td>
							<th class="is_empty">性别:</th>
							<td style="width: 25%"><select v-model="CfContent.brxb":disabled="readonly">
									<option v-for="(item, $index) in brxb_tran" :value="$index"
										v-text="item"></option>
							</select></td>
							<th class="is_empty">年龄</th>
							<td style="width: 13%">
							  <input v-model="CfContent.brnl" :readonly="readonly" @keydown="changeDown($event)" ></td>
							<td style="width: 12%"><select v-model="CfContent.nldw" :disabled="readonly" size="0" >
								<option v-for="(item, $index) in nldw_tran" :value="$index" v-text="item"></option>
							</select></td>
						</tr>
						<tr>
							<th class="is_empty">处方类型:</th>
							<td><select v-model="yfContent.cflx">
								<option v-for="v in cflxList" v-bind:value="v.cflxbm"
										v-text="v.cflxmc"></option>
							</select></td>
							<th class="is_empty">处方医师:</th>
							<td>
								<select v-model="CfContent.cfys" :disabled="readonly">
									<option v-for="allys in ysbmList" v-bind:value="allys.rybm"
										v-text="allys.ryxm"></option>
							</select>
							</td>
							<th class="is_empty">患者费别:</th>
							<td><!--<input v-model="CfContent.fbbm":readonly="readonly" @keydown="changeDown($event)" >-->
							    <select v-model="CfContent.fbbm" :disabled="readonly">
									<option v-for="allfb in brfbList" v-bind:value="allfb.fbbm"
										v-text="allfb.fbmc"></option>
							</select>
							</td>
							<th class="is_empty">处方号:</th>
							<td colspan="2"><input v-model="CfContent.cfh" @keydown="changeDown($event)" disabled="disabled"></td>
							<!-- <td><select v-model="CfContent.yfbm">
						    <option v-for="allyfbm in yfList" v-bind:value="allyfbm.yfbm"
							v-text="allyfbm.yfmc"></option>
						    </select>
						    </td> -->
						</tr>
					</tbody>
					<tbody class="part_w">
						<tr class="trTitle">
							<td class="partTitle" colspan="3">药品检索区</td>
						</tr>
						<tr>
							<th>药品名称:</th>
							<td style="width: 50%" colspan="5">
							  <input id="ypmc" data-notEmpty="true" v-model="PfContent.text" :readonly="readonly"
							  	 @keyup="changeDown($event,'ypmc','PfContent','searchCon')"
							  	 @input="change($event,'ypmc')">
							  <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" 
							  	@click-one="checkedOneOut" @click-two="dblclickypmc"
							  	></search-table>
							</td>
							<th>药品规格:</th>
							<td style="width: 25%">
							  <input v-model="PfContent.ypgg" @keydown="changeDown($event)" disabled="disabled"></td>
							<th>药品零价:</th>
							<td style="width: 13%">
							  <input type="number" v-model="PfContent.yplj" @keydown="changeDown($event)" disabled="disabled"></td>
							<th>医保统筹:</th>
							<td style="width: 12%"><select v-model="PfContent.ybtclb" disabled="disabled">
									<option v-for="alltclb in tclbList"
										v-bind:value="alltclb.tclbbm" v-text="alltclb.tclbmc"></option>
							</select></td>
						</tr>
						<tr>
							<th>数量:</th>
							<td style="width:12%">
							  <input id="sl" type="number" min=0 max=4 data-notEmpty="true" v-model="PfContent.cfyl" :readonly="readonly"
							  	 @keyup="changeDown($event,'sl')"></td>
							<th>单位:</th>
							<td style="width:13%"><select v-model="PfContent.yfdw" disabled="disabled">
									<option v-for="allypdw in ypdwList"
										v-bind:value="allypdw.jldwbm" v-text="allypdw.jldwmc"></option>
							</select></td>
							<th>科室配药:</th>
							<td style="width:25%"><select v-model="yfContent.pybz" :disabled="readonly">
									<option v-for="(item, $index) in istrue_tran" :value="$index"
										v-text="item"></option>
							</select></td>
							<th>配药科室:</th>
							<td><select v-model="CfContent.pyks" :disabled="readonly">
									<option v-for="allks in ksbmList" v-bind:value="allks.ksbm"
										v-text="allks.ksmc"></option>
							</select></td>
							<th>袋子数:</th>
							<td><input type="number" v-model="CfContent.dzs" @keydown="changeDown($event)" disabled="disabled"></td>
							<th>中药副数:</th>
							<td><input type="number" v-model="CfContent.zyfs" @keydown="changeDown($event)" disabled="disabled"></td>
						</tr>
						<tr>
							<th>药品库存:</th>
							<td colspan="3"><input v-model="PfContent.sjkc" type="number"
								@keydown="changeDown(11)" disabled="disabled"></td>
							
							<th>农合类别:</th>
							<td><select v-model="PfContent.nbtclb" disabled="disabled">
									<option v-for="(item, $index) in istrue_tran" :value="$index"
										v-text="item"></option>
							</select></td>
							<th>有效期至:</th>
							<td><input v-model="fDate(PfContent.yxqz,'date')" @keydown="changeDown($event)"  disabled="disabled"></td>
							<th>余额</th>
							<td><input v-model="CfContent.zhye"
								@keydown="changeDown($event)" disabled="disabled"></td>
							<th>限额</th>
							<td><input v-model="CfContent.jzxe"
								@keydown="changeDown($event)" disabled="disabled">
								<input v-model="PfContent.ypmc"  type="hidden">
								</td>
						</tr>
					</tbody>
				</table>
			</div>
			</div>
			<!-- 配方列表区 --> 
			<div id="pfhjList" class="tablePfListDiv">
				<table style="display: block;" class="patientTable" cellspacing="0"
					cellpadding="0">
					<thead>
						<!--<tr>
							<th class="tableNo"></th>
							<th style="min-width: 80px">药品编码</th>
							<th style="min-width: 100px">药品名称</th>
							<th style="min-width: 80px">药品规格</th>
							<th style="min-width: 60px">用量</th>
							<th style="min-width: 60px">零价</th>
							<th style="min-width: 60px">金额</th>
							<th style="min-width: 80px">药品批号</th>
							<th style="min-width: 80px">生产日期</th>
							<th style="min-width: 80px">有效期至</th>
							<th style="min-width: 80px">产地名称</th>
							<th style="min-width: 100px">处方号</th>
							<th style="min-width: 200px">备注</th>
							<th style="min-width: 50px">系统批号</th>
							<th style="min-width: 50px">产地</th>
							<th style="min-width: 50px">供货单位</th>
							<th style="min-width: 50px">库房单位</th>
							<th style="min-width: 50px">药房单位</th>
							<th style="min-width: 50px">分装比例</th>
							<th style="min-width: 80px">产品标准号</th>
							<th style="min-width: 50px">批准文号</th>
							<th style="min-width: 50px">统筹类别</th>
						</tr>-->
						<tr>
							<th class="tableNo"></th>
							<th>药品编码</th>
							<th>药品名称</th>
							<th>药品规格</th>
							<th>用量</th>
							<th>零价</th>
							<th>金额</th>
							<th>药品批号</th>
							<th>生产日期</th>
							<th>有效期至</th>
							<th>产地名称</th>
							<th>处方号</th>
							<th>备注</th>
							<th>系统批号</th>
							<th>产地</th>
							<th>供货单位</th>
							<th>库房单位</th>
							<th>药房单位</th>
							<th>分装比例</th>
							<th>产品标准号</th>
							<th>批准文号</th>
							<th>统筹类别</th>
						</tr>
						<tr><th v-for="item in 22"></th></tr>
						<tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
							:class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
							@dblclick="edit($index)">

							<th class="tableNo" v-text="$index+1"></th>
							<!-- <th><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)"/></th> -->
							<td v-text="item.ypbm"></td>
							<td v-text="item.ypmc"></td>
							<td v-text="item.ypgg"></td>
							<td v-text="item.cfyl" style="text-align: right;"></td>
							<td v-text="fDec(item.yplj,2)" style="text-align: right;"></td>
							<td v-text="fDec(item.cfyl*item.yplj,2)" style="text-align: right;"></td>
							<td v-text="item.scph"></td>
							<td v-text="fDate(item.scrq,'date')"></td>
							<td v-text="fDate(item.yxqz,'date')"></td>
							<td v-text="item.cdmc"></td>
							<td v-text="item.cfh" style="text-align: center;"></td>
							<td v-text="item.yysm"></td>
							<td v-text="item.xtph"></td>
							<td v-text="item.cdbm"></td>
							<td v-text="item.ghdw"></td>
							<td v-text="item.kfdw"></td>
							<td v-text="item.yfdw"></td>
							<td v-text="item.fzbl" style="text-align: right;"></td>
							<td v-text="item.cpbzh"></td>
							<td v-text="item.pzwh"></td>
							<td v-text="item.ybtclb"></td>
						</tr>
					</thead>

				</table>
			</div>

			<div class="tablePage" style="width: 74%">
				<select v-model="param.rows" @change="getData">
					<option value="10">10</option>
					<option value="20">20</option>
					<option value="30">30</option>
				</select>
				<div class="pageBtu fa fa-angle-left" @click="changePage('prev')"></div>
				第<input v-model="param.page" class="enterPage"
					@keyup.enter="changePage" type="number">页&nbsp;&nbsp; 共<span
					v-text="totlePage"></span>页
				<div class="pageBtu fa fa-angle-right" @click="changePage('next')"></div>
			</div>
		</div>

		<!-- 处方列表区 -->
		<div id="cfhjList" style="width: 23%; float: left;">
			<div class="tableDiv">
				<table style="display: block" class="patientTable" cellspacing="0"
					cellpadding="0">
					<tr>
						<th class="tableNo"></th>
						<th style="min-width: 20px">扣费</th>
						<th style="min-width: 80px">处方号</th>
						<th style="min-width: 80px">患者姓名</th>
						<th style="min-width: 100px">挂号序号/住院号</th>
						<th style="min-width: 80px">处方类型</th>
						<th style="min-width: 60px">患者类型</th>
						<th style="min-width: 100px">处方医师</th>
						<th style="min-width: 100px">处方日期</th>
						<th style="min-width: 60px">处方金额</th>
						<th style="min-width: 60px">实际金额</th>
						<th style="min-width: 60px">年龄</th>
						<th style="min-width: 60px">费别</th>
						<th style="min-width: 60px">性别</th>
						<th style="min-width: 60px">科室配药</th>
						<th style="min-width: 100px">配药科室</th>
						<th style="min-width: 60px">袋子费</th>
						<th style="min-width: 60px">袋子数</th>
						<th style="min-width: 60px">中药副数</th>
						<th style="min-width: 100px">科室</th>
						<th style="min-width: 100px">划价人</th>
						<th style="min-width: 60px">发药标志</th>
						<th style="min-width: 100px">发药人</th>
						<th style="min-width: 100px">发药时间</th>
						<th style="min-width: 100px">扣费人员</th>
						<th style="min-width: 100px">扣费时间</th>
						<th style="min-width: 60px">是否打印</th>
						<th style="min-width: 200px">备注</th>
						<th style="min-width: 80px">年龄</th>
						<th style="min-width: 80px">单位</th>
						<!-- <th style="min-width: 80px">配药科室</th> -->
					</tr>

					<tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
						:class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
						@dblclick="edit($index)">

						<th class="tableNo" v-text="$index+1"></th>
						<td v-text="istrue_tran[item.kfbz]" style="text-align: center;"></td>
						<td v-text="item.cfh"></td>
						<td v-text="item.brxm"></td>
						<td v-text="item.bah"></td>
						<td v-text="item.cflxmc"></td>
						<td v-text="brlx_tran[item.brlx]" style="text-align: center;"></td>
						<td v-text="item.cfysxm"></td>
						<td v-text="fDate(item.cfrq,'datetime')"></td>
						<td v-text="fDec(item.cfje,2)" style="text-align: right;"></td>
						<td v-text="fDec(item.sjje,2)" style="text-align: right;"></td>
						<td v-text="item.nl"></td>
						<td v-text="item.fbmc"></td>
						<td v-text="brxb_tran[item.brxb]" style="text-align: center;"></td>
						<td v-text="istrue_tran[item.pybz]" style="text-align: center;"></td>
						<td v-text="item.pyksmc"></td>
						<td v-text="item.dzf"></td>
						<td v-text="item.dzs"></td>
						<td v-text="item.zyfs"></td>
						<td v-text="item.brksmc"></td>
						<td v-text="item.hjryxm"></td>
						<td v-text="istrue_tran[item.fybz]" style="text-align: center;"></td>
						<td v-text="item.fyryxm"></td>
						<td v-text="fDate(item.fyrq,'datetime')"></td>
						<td v-text="item.kfryxm"></td>
						<td v-text="fDate(item.kfrq,'datetime')"></td>
						<td v-text="istrue_tran[item.isprint]" style="text-align: center;"></td>
						<td v-text="item.bzsm"></td>
						<td v-text="item.brnl"></td>
						<td v-text="item.brnldw"></td>
						<!-- <td v-text="item.pyks"></td> -->
					</tr>
				</table>
			</div>

			<div class="tablePage">
				<select v-model="param.rows" @change="getData">
					<option value="10">10</option>
					<option value="20">20</option>
					<option value="30">30</option>
				</select>
				<div class="pageBtu fa fa-angle-left" @click="changePage('prev')"></div>
				第<input v-model="param.page" class="enterPage"
					@keyup.enter="changePage" type="number">页&nbsp;&nbsp; 共<span
					v-text="totlePage"></span>页
				<div class="pageBtu fa fa-angle-right" @click="changePage('next')"></div>
			</div>

		</div>

	</div>


</body>
<script type="text/javascript" src="cfhj.js"></script>
</html>
