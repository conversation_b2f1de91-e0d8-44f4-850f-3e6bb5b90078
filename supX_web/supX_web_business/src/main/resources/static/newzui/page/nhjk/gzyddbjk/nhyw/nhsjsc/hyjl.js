/**
 * Created by mash on 2017/9/29.
 */
//(function () {
var hyjl = new Vue({
    el: '#hyjl',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable,
        'search-table2': searchTable,
        'search-table3': searchTable,
        'search-table4': searchTable,
        'search-table5': searchTable,
        'search-table6': searchTable,
        'search-table7': searchTable,
        'search-table8': searchTable,
        'search-table9': searchTable,
        'search-table10': searchTable,
        'search-table11': searchTable,
        'search-table12': searchTable,
        'search-tablezdjb': searchTable,
    },
    mounted:function () {
        this.getbxlb();
    },
    created:function(){
        if(sessionStorage.getItem('nhsjsc')){
            this.brxxList=Object.assign(this.brxxList,JSON.parse(sessionStorage.getItem('nhsjsc')))
            sessionStorage.removeItem('nhsjsc')
        }
    },
    data: {
    	qtzdList:[],
        billCode: null,
        bxlbbm: null,
        bxurl: null,
        birthday: null,
        json: {},
        text: null,
        brxxList: {zdjb:0,ksjy:"0",zzlx:"0"},
        ssContent: {},
        jbContent: {},
        jbContent2: {},
        jbContent3: {},
        jbContent4: {},
        jbContent5: {},
        jbContent6: {},
        jbContent7: {},
        jbContent8: {},
        jbContent9: {},
        jbContent10: {},
        jbContent11: {},
        jbContentzdjb: {},
        searchCon: [],
        selSearch: -1,
        searchCon2: [],
        searchCon3: [],
        searchCon4: [],
        searchCon5: [],
        searchCon6: [],
        searchCon7: [],
        searchCon8: [],
        searchCon9: [],
        searchCon10: [],
        searchCon11: [],
        searchCon12: [],
        searchConzdjb: [],
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'bm',
            '疾病名称': 'mc',
            '代码': 'dm'
        },
        them2: {
            '手术编码': 'ssbm',
            '手术名称': 'ssmc',
            '代码': 'pydm'
        }
    },
    methods: {
        getReCheckOne: function (val) {
            Vue.set(this.brxxList, 'zdjb', val[1]);
        },
        cancel: function () {
        	//测试时不用，Inpid给出来了，正式时用（改查询方式）
        	/*if(!hyjl.brxxList.inpid){
        		malert("该病人无补偿证号，未农合入院","top","defeadted");
        		return
        	}*/
        	var head = {
                	operCode:"S05",
                	billCode:hyjl.billCode,
                	rsa:""
                };
                var body = {
                	inpId:hyjl.brxxList.inpid,
                    areaCode:hyjl.brxxList.xzqh,
                    isTransProvincial:"0"
                }
                var footer={
                		zfry:userId
                }
                var param = {
                	head:head,
                	body:body,
                	footer:footer
                }
                var str_param = JSON.stringify(param);

                $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+hyjl.bxurl+"&bxlbbm="+hyjl.bxlbbm+"&types=S&parm="+str_param, function (json) {
                	console.log(json);
                	if (json.a == 0){
                		malert("农合取消入院办理成功！","top","success");
                		hyjl.brxxList.inpid = null;
                        hyjl.brxxList.memberid = "";
                        hyjl.jbContent.jbmc = "";
                        hyjl.brxxList.rylx = "";
                        left_tab1.getBrData();
                	}else{
                		malert(json.c,"top","defeadted");
                	}
                });
        },

        getbxlb: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify({bxjk: "001"}), function (json) {
                    if (json.a == 0 && json.d.list.length > 0) {
                            hyjl.bxlbbm = json.d.list[0].bxlbbm;
                            hyjl.bxurl = json.d.list[0].url;
                            hyjl.getS02();
                    } else {
                        malert("保险类别查询失败!" + json.c,"top","defeadted");
                    }
                });
        },
        getS02: function () {
            var head = {
                operCode: "S02",
                rsa: ""
            };
            var body = {
                userName: "",
                passWord: ""
            };
            var param = {
                head: head,
                body: body
            };
            var str_param = JSON.stringify(param);

            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    console.log(json);
                    if (json.a == 0) {
                        hyjl.billCode = json.d;
                    } else {
                        malert(json.c,"top","defeadted");
                    }
                });
        },
        changeDown: function (event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'jbContent', 'searchCon');
            if (event.code == 'Enter' || event.code == 13) {
                    Vue.set(this.jbContent, 'jbmc', this.jbContent['mc']);
                    hyjl.brxxList.jbbm = this.jbContent.bm;
                    this.selSearch=-1;
                    this.nextFocus(event);
                    $(".selectGroup").hide();
            }
        },
        changeDown2: function (event, type) {
            this.keyCodeFunction(event, 'ssContent', 'searchCon2');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.ssContent, 'ssmc', this.ssContent['ssmc']);
                    hyjl.brxxList.ssbm = this.ssContent.ssbm;
                    console.log(this.ssContent.ssbm);
                    console.log(hyjl.brxxList.ssbm);
                    this.nextFocus(event);
                    this.selSearch = 0;
                }
            }
        },
        changeDown3: function (event, type) {
            this.keyCodeFunction(event, 'jbContent2', 'searchCon3');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent2, 'qtzd1mc', this.jbContent2['mc']);
                    hyjl.brxxList.qtzd1 = this.jbContent2.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                    var qtzd={
                    		mc:this.jbContent2['mc'],
                    		bm:this.jbContent2.bm
                    }
                    hyjl.qtzdList.push(qtzd);
                }
            }
        },
        changeDown4: function (event, type) {
            this.keyCodeFunction(event, 'jbContent3', 'searchCon4');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent3, 'qtzd2mc', this.jbContent3['mc']);
                    hyjl.brxxList.qtzd2 = this.jbContent3.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                    var qtzd={
                    		mc:this.jbContent3['mc'],
                    		bm:this.jbContent3.bm
                    }
                    hyjl.qtzdList.push(qtzd);
                }
            }
        },
        changeDown5: function (event, type) {
            this.keyCodeFunction(event, 'jbContent4', 'searchCon5');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent4, 'qtzd3mc', this.jbContent4['mc']);
                    hyjl.brxxList.qtzd3 = this.jbContent4.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                    var qtzd={
                    		mc:this.jbContent4['mc'],
                    		bm:this.jbContent4.bm
                    }
                    hyjl.qtzdList.push(qtzd);
                }
            }
        },
        changeDown6: function (event, type) {
            this.keyCodeFunction(event, 'jbContent5', 'searchCon6');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent5, 'qtzd4mc', this.jbContent5['mc']);
                    hyjl.brxxList.qtzd4 = this.jbContent5.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                    var qtzd={
                    		mc:this.jbContent5['mc'],
                    		bm:this.jbContent5.bm
                    }
                    hyjl.qtzdList.push(qtzd);
                }
            }
        },
        changeDown7: function (event, type) {
            this.keyCodeFunction(event, 'jbContent6', 'searchCon7');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent6, 'qtzd5mc', this.jbContent6['mc']);
                    hyjl.brxxList.qtzd5 = this.jbContent6.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                    var qtzd={
                    		mc:this.jbContent6['mc'],
                    		bm:this.jbContent6.bm
                    }
                    hyjl.qtzdList.push(qtzd);
                }
            }
        },
        changeDown8: function (event, type) {
            this.keyCodeFunction(event, 'jbContent7', 'searchCon8');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent7, 'qtzd6mc', this.jbContent7['mc']);
                    hyjl.brxxList.qtzd6 = this.jbContent7.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                }
            }
        },
        changeDown9: function (event, type) {
            this.keyCodeFunction(event, 'jbContent8', 'searchCon9');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent8, 'qtzd7mc', this.jbContent8['mc']);
                    hyjl.brxxList.qtzd7 = this.jbContent8.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                }
            }
        },
        changeDown10: function (event, type) {
            this.keyCodeFunction(event, 'jbContent9', 'searchCon10');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent9, 'qtzd8mc', this.jbContent9['mc']);
                    hyjl.brxxList.qtzd8 = this.jbContent9.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                }
            }
        },
        changeDown11: function (event, type) {
            this.keyCodeFunction(event, 'jbContent10', 'searchCon11');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent10, 'qtzd9mc', this.jbContent10['mc']);
                    hyjl.brxxList.qtzd9 = this.jbContent10.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                }
            }
        },
        changeDown12: function (event, type) {
            this.keyCodeFunction(event, 'jbContent11', 'searchCon12');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent11, 'qtzd10mc', this.jbContent11['mc']);
                    hyjl.brxxList.qtzd10 = this.jbContent11.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                }
            }
        },
        changeDownzdjb: function (event, type) {
            this.keyCodeFunction(event, 'jbContentzdjb', 'searchConzdjb');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContentzdjb, 'zdjbmc', this.jbContentzdjb['mc']);
                    hyjl.brxxList.zdjbbm = this.jbContentzdjb.bm;
                    this.nextFocus(event);
                    this.selSearch = 0;
                }
            }
        },
        searching: function (add, type,val) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.page.parm = val;
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching2: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.ssContent[type] == undefined || this.ssContent[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.ssContent[type];
            }
             var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
             $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=ssbm&method=query&parm="
                 + JSON.stringify(str_param),
                 function (json) {
                     if (json.a == 0) {
                         var date = null;
                         var res = eval('(' + json.d + ')');
                         if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                             for (var i = 0; i < res.list.length; i++) {
                                 hyjl.searchCon2.push(res.list[i]);
                             }
                         } else {
                             hyjl.searchCon2 = res.list;
                         }
                         hyjl.page.total = res.total;
                         hyjl.selSearch = 0;
                         if (res.list.length > 0 && !add) {
                             $(".selectGroup").hide();
                             _searchEvent.show();
                         }
                     } else {
                         malert("查询失败  " + json.c,"top","defeadted");
                     }
                 });
        },
        searching3: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent2[type] == undefined || this.jbContent2[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent2[type];
            }
             var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
             $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                 + JSON.stringify(str_param),
                 function (json) {
                     if (json.a == 0) {
                         var date = null;
                         var res = eval('(' + json.d + ')');
                         if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                             for (var i = 0; i < res.list.length; i++) {
                                 hyjl.searchCon3.push(res.list[i]);
                             }
                         } else {
                             hyjl.searchCon3 = res.list;
                         }
                         hyjl.page.total = res.total;
                         hyjl.selSearch = 0;
                         if (res.list.length > 0 && !add) {
                             $(".selectGroup").hide();
                             _searchEvent.show();
                         }
                     } else {
                         malert("查询失败  " + json.c,"top","defeadted");
                     }
                 });
        },
        searching4: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent3[type] == undefined || this.jbContent3[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent3[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon4.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon4 = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching5: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent4[type] == undefined || this.jbContent4[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent4[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon5.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon5 = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching6: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent5[type] == undefined || this.jbContent5[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent5[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon6.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon6 = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching7: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent6[type] == undefined || this.jbContent6[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent6[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon7.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon7 = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching8: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent7[type] == undefined || this.jbContent7[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent7[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon8 = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching9: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent8[type] == undefined || this.jbContent8[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent8[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon9.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon9 = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching10: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent9[type] == undefined || this.jbContent9[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent9[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon10.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon10 = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching11: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent10[type] == undefined || this.jbContent10[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent10[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon11.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon11 = res.list;
                        }
                        hyjl.page.total = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searching12: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent11[type] == undefined || this.jbContent11[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent11[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchCon12.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchCon12 = res.list;
                        }
                        hyjl.page.total12 = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },
        searchingzdjb: function (add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContentzdjb[type] == undefined || this.jbContentzdjb[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContentzdjb[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,sptbz:"1"}
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + hyjl.bxurl + "&bxlbbm=" + hyjl.bxlbbm + "&types=jbbm&method=query&parm="
                + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        var res = eval('(' + json.d + ')');
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.list.length; i++) {
                                hyjl.searchConzdjb.push(res.list[i]);
                            }
                        } else {
                            hyjl.searchConzdjb = res.list;
                        }
                        hyjl.page.totalzdjb = res.total;
                        hyjl.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,"top","defeadted");
                    }
                });
        },

        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent = item;
                Vue.set(this.jbContent, 'jbmc', this.jbContent['mc']);
                hyjl.brxxList.jbbm = this.jbContent.bm;
                hyjl.brxxList.jbmc = this.jbContent.mc;
                $(".selectGroup").hide();
            }
        },
        selectOne2: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'ssmc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.ssContent = item;
                Vue.set(this.ssContent, 'ssmc', this.ssContent['ssmc']);
                hyjl.brxxList.ssbm = this.ssContent.ssbm;
                $(".selectGroup").hide();
            }
        },
        selectOne3: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd1mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent2 = item;
                Vue.set(this.jbContent2, 'qtzd1mc', this.jbContent2['mc']);
                hyjl.brxxList.qtzd1 = this.jbContent2.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne4: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd2mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent3 = item;
                Vue.set(this.jbContent3, 'qtzd2mc', this.jbContent['mc']);
                hyjl.brxxList.qtzd2 = this.jbContent3.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne5: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd3mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent4 = item;
                Vue.set(this.jbContent4, 'qtzd3mc', this.jbContent4['mc']);
                hyjl.brxxList.qtzd3 = this.jbContent4.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne6: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd4mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent5 = item;
                Vue.set(this.jbContent5, 'qtzd4mc', this.jbContent5['mc']);
                hyjl.brxxList.qtzd4 = this.jbContent5.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne7: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd5mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent6 = item;
                Vue.set(this.jbContent6, 'qtzd5mc', this.jbContent6['mc']);
                hyjl.brxxList.qtzd5 = this.jbContent6.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne8: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd6mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent7 = item;
                Vue.set(this.jbContent7, 'qtzd6mc', this.jbContent7['mc']);
                hyjl.brxxList.qtzd6 = this.jbContent7.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne9: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd7mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent8 = item;
                Vue.set(this.jbContent8, 'qtzd7mc', this.jbContent8['mc']);
                hyjl.brxxList.qtzd7 = this.jbContent8.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne10: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd8mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent9 = item;
                Vue.set(this.jbContent9, 'qtzd8mc', this.jbContent9['mc']);
                hyjl.brxxList.qtzd8 = this.jbContent9.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne11: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd9mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent10 = item;
                Vue.set(this.jbContent10, 'qtzd9mc', this.jbContent10['mc']);
                hyjl.brxxList.qtzd9 = this.jbContent10.bm;
                $(".selectGroup").hide();
            }
        },
        selectOne12: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'qtzd10mc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent11 = item;
                Vue.set(this.jbContent11, 'qtzd10mc', this.jbContent11['mc']);
                hyjl.brxxList.qtzd10 = this.jbContent11.bm;
                $(".selectGroup").hide();
            }
        },
        selectOnezdjb: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'zdjbmc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContentzdjb = item;
                Vue.set(this.jbContentzdjb, 'zdjbmc', this.jbContentzdjb['mc']);
                hyjl.brxxList.zdjbbm = this.jbContentzdjb.bm;
                $(".selectGroup").hide();
            }
        },
        edit: function () {
            
        	if(!hyjl.brxxList.lxdh){
        		malert("联系电话不能为空!","top","defeadted");
        		return
        	}
            if (hyjl.brxxList.lxdh.length > 11) {
                console.log(hyjl.brxxList.lxdh.length);
                malert("电话号已超过最大长度，请核对！",'right','defeadted');
                return
            }
            if(!hyjl.brxxList.rylx){
                malert("入院类型不能为空！","top","defeadted");
                return
            }
        	if(!hyjl.brxxList.jbbm){
        		malert("疾病名称不能为空！","top","defeadted");
        		return
        	}
        	if(!hyjl.brxxList.ryqk){
        		malert("入院情况不能为空！","top","defeadted");
        		return
        	}
            if(hyjl.brxxList.zdjb){
                if(!hyjl.brxxList.zdjbbm){
                    malert("重大疾病编码不能为空！","top","defeadted");
                    return
                }
                if(!hyjl.brxxList.zlfs){
                    malert("治疗方式不能为空！","top","defeadted");
                    return
                }
            }
            //temp
            /*hyjl_pop.isShow = true;
            hyjl_pop.brxxList.ybkh2 = hyjl.brxxList.ybkh;*/

        	if(!hyjl.brxxList.memberid){
                hyjl_pop.isShow = true;
            	hyjl_pop.title = '参合人员信息'
            	hyjl_pop.open();
                hyjl_pop.brxxList.ybkh2 = hyjl.brxxList.ybkh;
        	}else{
        	    malert("病人已经登记，请勿重复登记！","top","defeadted");
        	    return;
        		 // var head = {
                 //        	operCode:"S06",
                 //        	billCode:hyjl.billCode,
                 //        	rsa:""
                 //        };
                 //        var body = {
                 //        	inpId:hyjl.brxxList.inpid,
                 //        	inpatientNo:hyjl.brxxList.zyh,
                 //        	admissionDate:hyjl.fDate(hyjl.brxxList.ryrq,'date'),
                 //        	admissionDepartments:hyjl.brxxList.admissionDepartments,
                 //        	treatingPhysician:hyjl.brxxList.zyys,
                 //        	admissionStatus:hyjl.brxxList.ryqk,
                 //        	diseaseCode:hyjl.brxxList.jbbm,
                 //        	initialDiagnosis:null,
                 //        	surgeryCode:hyjl.brxxList.ssbm,
                 //        	berthNo:hyjl.brxxList.rycwh,
                 //        	isIncrease:hyjl.brxxList.zsqq,
                 //        	isReferra:hyjl.brxxList.zzbz,
                 //        	inpatientTypeOflocal:hyjl.brxxList.rylx,
                 //        	bxAccount:hyjl.brxxList.bxf,
                 //        	tel:hyjl.brxxList.lxdh,
                 //        	bankCardNo:hyjl.brxxList.yhkh,
                 //        	accountHolder:hyjl.brxxList.yhkhrx,
                 //        	holderRelation:hyjl.brxxList.khgx,
                 //        	remark:hyjl.brxxList.bz
                 //        }
                 //
                 //        var footer={
                 //           		 jtdz:'测试地址',
                 //           		 lxfs:hyjl.brxxList.lxdh,
                 //           		 jbczy:userId,
                 //           		 jbrq:hyjl.fDate(new Date(),'date'),
                 //        }
                 //        var param = {
                 //        	head:head,
                 //        	body:body,
                 //        	footer:footer
                 //        }
                 //        var str_param = JSON.stringify(param);
                 //        $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+hyjl.bxurl+"&bxlbbm="+hyjl.bxlbbm+"&types=S&parm="+str_param, function (json) {
                 //        	if (json.a == 0){
                 //        		malert("入院修改成功！");
                 //        		left_tab1.getBrData();
                 //        	}else{
                 //        		malert("请取消登记后重新登记！","bottom","defeadted");
                 //        	}
                 //        });
        	}
        },

    }
});


var hyjl_pop = new Vue({
    el: '#hyjl_pop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
    	brxxList:[],
        year:{},
        isShow: false,
        text: null,
        jsonList: [],
        num: 1,
    },
    created:function(){
        this.year.year = new Date().getFullYear();
    },
    methods: {
    	closes: function () {
            this.num = 1

        },
        open: function () {
            this.num = 0
        },
    	getPerson:function(){
   		 var head = {
                   	operCode:"S03",
                   	billCode:hyjl.billCode,
                   	rsa:""
                   };
                   var body = {
                   	year:this.year.year,
                   	medicalNo:hyjl_pop.brxxList.ybkh2,
                    areaCode:hyjl.brxxList.xzqh,
                    isTransProvincial:"0"
                   }

                   var param = {
                   	head:head,
                   	body:body
                   }
                   var str_param = JSON.stringify(param);
                   console.log(str_param);

                   $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+hyjl.bxurl+"&bxlbbm="+hyjl.bxlbbm+"&types=S&parm="+str_param, function (json) {
                   	console.log(json);
                   	if (json.a == 0){
                   		var res=eval('('+json.d+')')
                   		hyjl_pop.jsonList = res.list;
                   	}else{
                   		malert(json.c,"top","defeadted");
                   	}
                   });
   	},
       // 点击确定执行的事件
       saveData: function () {
       	malert("请选择对应病人后，双击保存！","top","defeadted");
       	return
       },
       // 根据输入参合号获取家庭成员

       // 双击赋值，弹出层消失
        edit: function (index) {
            var head = {
                operCode:"S04",
                billCode:hyjl.billCode,
                rsa:""
            };
            var zsqq=null;
            var zzbz=null;
            if(hyjl.brxxList.zsqq==true){
                zsqq='1';
            }else{
                zsqq='0';
            }
            if(hyjl.brxxList.zzbz==true){
                zzbz='1';
            }else{
                zzbz='0';
            }
            var xzbz=null;

            var valuelist=[];
            if(hyjl.qtzdList.length>0){
                for(var i=0;i<hyjl.qtzdList.length;i++){

                    var disCode={
                        disCode:hyjl.qtzdList[i].bm
                    };
                    var valueList={
                        valueList:disCode
                    };
                    valuelist.push(valueList);
                }
            }
            //农合疾病编码 省编码      治疗方式    申请号      行政区划   跨省就医
            var nhjbbm = "",szdjbbm = "",szlfs = "",szdjbsqh = "",sxzqh = "",sksjy = "",familyNo = "",cureId="",zdjbbz = "0";
            if (hyjl.brxxList.zdjb){
                //nhjbbm = hyjl.brxxList.zdjbbm;
            	nhjbbm = hyjl.brxxList.zdjbsqxh ? hyjl.brxxList.jbbm : hyjl.brxxList.zdjbbm;
                szdjbbm = hyjl.brxxList.jbbm;
                szlfs = hyjl.brxxList.zlfs;
                sxzqh = hyjl.brxxList.xzqh;
                sksjy = hyjl.brxxList.ksjy;
                familyNo = hyjl_pop.jsonList[index].familyId;
                cureId = "2";
                zdjbbz = "1";
            }else{
                nhjbbm = hyjl.brxxList.jbbm;
                sxzqh = hyjl.brxxList.xzqh;
                sksjy = hyjl.brxxList.ksjy;
                familyNo = hyjl_pop.jsonList[index].familyId;
            }
            var  turnCode = hyjl.brxxList.turnCode;
            if (turnCode == null){
                turnCode = "";
            }
            var body = {
                memberId:hyjl_pop.jsonList[index].memberId,
                inpatientNo:hyjl.brxxList.zyh,
                admissionDate:hyjl_pop.fDate(hyjl.brxxList.ryrq,'date'),
                admissionDepartments:hyjl.brxxList.admissionDepartments,
                treatingPhysician:hyjl.brxxList.zyysxm,
                admissionStatus:hyjl.brxxList.ryqk,
                diseaseCode:nhjbbm,
                initialDiagnosis:hyjl.brxxList.jbmc,
                surgeryCode:hyjl.brxxList.ssbm,
                berthNo:hyjl.brxxList.rycwh,
                isIncrease:zsqq,
                isReferra:zzbz,
                inpatientTypeOflocal:hyjl.brxxList.rylx,
                bxAccount:hyjl_pop.jsonList[index].account,
                tel:hyjl.brxxList.lxdh,
                bankCardNo:hyjl.brxxList.yhkh,
                accountHolder:hyjl.brxxList.yhkhrx,
                holderRelation:hyjl.brxxList.khgx,
                remark:hyjl.brxxList.bz,
                bigDiseaseNo:hyjl.brxxList.zdjbsqxh,
                isReferraNo:null,
                disList:valuelist,
                uploadType:'0',
                familySysno:familyNo,
                treatCode:szlfs,
                cureId:cureId,
                turnMode:hyjl.brxxList.zzlx,//转诊类型
                turnCode:turnCode,
                registerID:hyjl.brxxList.zyh,
                areaCode:sxzqh,
                isTransProvincial:sksjy || 0,
                majorDiseaseICD:szdjbbm,
                secondMajorDiseaseICD:"",
                threeMajorDiseaseICD:"",
                secondTreatCode:"",
                threeTreatCode:"",

            }
            var date=new Date();
            var memberPro = hyjl_pop.jsonList[index].memberPro;
            if (memberPro == null ||memberPro == undefined ){
                memberPro = "";
            }
            var footer={
                brxm:hyjl.brxxList.brxm,
                brnl:hyjl.brxxList.nl,
                ylzh:hyjl.brxxList.ybkh,
                jtdz:hyjl_pop.jsonList[index].areaName,
                lxfs:hyjl.brxxList.lxdh,
                jbczy:userId,
                jbrq:hyjl_pop.fDate(new Date(),'date'),
                memberPro:memberPro,
                zdjb:zdjbbz,
                xzqh:sxzqh,
                familyNo:familyNo,
                treatcode:szlfs,//治疗方式
                majordiseaseicd:szdjbbm  //重大疗病编码
            }
            var param = {
                head:head,
                body:body,
                footer:footer
            }
            var str_param = JSON.stringify(param).replace(/\+/g, "%2B");
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+hyjl.bxurl+"&bxlbbm="+hyjl.bxlbbm+"&types=S&parm="+str_param, function (json) {
                    console.log(json);
                    if (json.a == 0){
                        var res=eval('('+json.d+')');
                        console.log(res);
                        hyjl.brxxList.inpid=res.list[0];
                        console.log(hyjl.brxxList.inpid);
                        malert("农合入院办理成功！","top","success");
                        hyjl_pop.isShow = false;
                        hyjl.brxxList.memberid = hyjl_pop.jsonList[index].memberId;
                        left_tab1.getBrData();
                    }else{
                        malert(json.c,"top","defeadted");
                    }
                });

        }
   }
});
//})();
