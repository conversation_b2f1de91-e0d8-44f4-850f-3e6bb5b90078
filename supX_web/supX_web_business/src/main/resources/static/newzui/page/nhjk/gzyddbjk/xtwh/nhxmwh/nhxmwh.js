  //单页面菜单的加载
//  var datestart = getTodayDateBegin();
  var InfoMenu = new Vue({
      el: '.InfoMenu',
      data: {
          which: 0,
		  pageIndex:['jbbm','mlzd','sszd']
      },
	  created:function(){
		  this.loadCon(0);
		  this.getbxlb();
	  },
      methods: {
          loadCon: function (index) {
          	this.which=index
              var pageDiv = $("#"+this.pageIndex[this.which]);
              $(".page_div").hide();
              if(pageDiv.length == 0){
                  $("."+this.pageIndex[this.which]).load(this.pageIndex[this.which]+".html").fadeIn(300);
              } else {
                  $("."+this.pageIndex[this.which]).fadeIn(300);
              }
          },
          getbxlb:function(){
//        	$("#nh_date").val(datestart);
          	var param = {bxjk:"001"};
          	console.log(param);
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
	
                    if (json.a == 0){
                    	if (json.d.list.length > 0){
                    		 Menu.bxlbbm = json.d.list[0].bxlbbm;
                    		 Menu.bxurl = json.d.list[0].url;
                    		 console.log("bxlb:"+Menu.bxlbbm+"|url:"+Menu.bxurl);
                    		 //S02认证
  											InfoMenu.getS02();
                    	}
                  
                    }else{
                    	malert("保险类别查询失败!"+json.c)
                    }
                });
          },
          //S02认证
			    getS02 :function(){
			    	var head = {
			        	operCode:"S02",
			        	rsa:""
			        };
			        var body = {
			        	userName:"",
			        	passWord:""
			        }
			        
			        var param = {
			        	head:head,
			        	body:body
			        }
			        var str_param = JSON.stringify(param);
			        
			        $.getJSON(
			        "/actionDispatcher.do?reqUrl=New1BxInterface&url="+Menu.bxurl+"&bxlbbm="+Menu.bxlbbm+"&types=S&parm="+str_param, function (json) {
			        	console.log(json);
			        	if (json.a == 0){
			        		Menu.billCode = json.d;
			        		console.log("billCode:"+Menu.billCode);
			        	}else{
			        		malert(json.c);
			        	}
			        });
			    	}
    
    
      }
  });


// 这个是所选取的时间，可以在其他页面直接使用
var nh_date = null;
window.getTime = function(event) {
    nh_date = $(event).val();
};

var Menu = new Vue({
	data:{
		bxlbbm:null,
		bxurl:null,
		billCode:null
	}
})

