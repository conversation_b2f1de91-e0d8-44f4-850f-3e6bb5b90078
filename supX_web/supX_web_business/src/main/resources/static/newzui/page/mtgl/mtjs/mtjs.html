<div id="mtjs" class="padd-t-10 padd-l-10 padd-b-10">
    <div class="toolMenu flex-container padd-b-10">
        <!--        <button class="tong-btn btn-parmary-b icon-gd icon-font14 paddr-r5" @click="loadYibaoPage">读卡</button>-->
        <button class="tong-btn btn-parmary-b icon-gd icon-font15 paddr-r5" @click="upload">医保上传</button>
        <button class="tong-btn btn-parmary-b icon-fyqk icon-font14 paddr-r5" @click="settlement">结算</button>
        <!--        <button class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5" @click="getData">刷新</button>-->
        <div class="nh-total flex-container">
            <b> 合计</b>：<span style="color: orange;font-size: 18px;">{{nums}}</span>&nbsp;笔
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <b> 累计</b>：<span style="color: orange;font-size: 18px;">{{fDec(totalFee,2)}}</span>&nbsp;元
        </div>
    </div>
    <div class="zui-table-view over-auto hzList padd-r-10 padd-l-10 padd-b-68">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><span>序号</span></th>
                    <th>
                        <div class="zui-table-cell cell-l"><span>项目名称</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>数量</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>金额</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>明细时间</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>科室</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>医生</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="switchIndex('hoverIndex',true,$index)"
                    @mouseleave="switchIndex()"
                    @click="switchIndex('activeIndex',true,$index)"
                    :tabindex="$index"
                    v-for="(item, $index) in jsonList">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-l" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.bxxmmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.akc225"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.akc226"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yka055"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.aae036"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yka097"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yka099"></div>
                    </td>
                </tr>
                </tbody>
            </table>
            <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
        </div>
        <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
              :next-more="nextMore"></page>
    </div>
    <div id="yibao_panel" v-cloak>
        <div v-show="isShowYibaoPage" class="zui-form podrag bcsz-layer  flex-container flex-dir-c"
             style="width: auto;overflow: hidden;top: 80px;bottom: 50px; height: auto; left: 100px; right: 100px">
            <div class="layui-layer-title">患者医保信息</div>
            <span class="layui-layer-setwin">
                <a @click="isShowYibaoPage=false" class="closex ti-close" href="javascript:;"></a>
            </span>
            <div class="layui-layer-content flex-container flex-dir-c flex-one">
                <div class="layui-height flex-container flex-dir-c flex-one " id="loadYibaoPage">

                </div>
                <div class="zui-row buttonbox margin-b-15 margin-r-10">
                    <button class="tong-btn btn-parmary-f2a xmzb-db paddr-r5" @click="load()">读卡</button>
                    <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="enter()">结算</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="application/javascript" src="/newzui/pub/js/insuranceUtil.js"></script>
<script type="application/javascript" src="mtjs.js?v=20210304"></script>
