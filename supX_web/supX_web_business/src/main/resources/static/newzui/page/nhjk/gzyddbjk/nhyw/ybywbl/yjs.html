<div id="yjs">
     <div class="toolMenu">
      <button id = "sxButton" style="margin-right: 30px" @click="sx()">刷新</button>
        <button id = "yjsButton" style="margin-right: 30px" @click="yjs()">预结算</button>
        <button id = "ybcyButton" style="margin-right: 30px" @click="ybcy()">医保出院</button>
        <input v-model="jydjh" style="width: 121px;"  placeholder="输入就医登记号取消">
        <button id = "qxcyButton" style="margin-right: 30px" @click="qxybcy()">取消出院</button>
       <!--  <button id = "dyButton" style="margin-right: 30px" @click="printJs()">打印结算单</button> -->
    </div>
    <div>
     <span v-text= "error"></span>
    </div>
    <div class="djxx" style="display: inline-block;">
        <div class="conTit"><div>登记信息</div></div>
        <div class="infoIpt">
            <p>住院号</p>
            <input type="text" v-model="json.ZYH" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>就医登记号</p>
            <input type="text" v-model="json.SERIAL_NO" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>个人编号</p>
            <input type="text" v-model="json.BRID" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>姓名</p>
            <input type="text" v-model="json.NAME" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>性别</p>
            <input type="text" v-model="json.SEX" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>个人电脑号</p>
            <input type="text" v-model="json.INDI_ID" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>入院日期</p>
            <input type="text"  v-model="fDate(json.RYRQ,'date')" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>出院日期</p>
            <input type="text"  v-model="fDate(json.BQCYRQ,'YY')" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>入院诊断</p>
            <input type="text" v-model="json.RYZDMC" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>入院诊断编码</p>
            <input type="text" v-model="json.RYZDBM" disabled="disabled"/>
        </div>

        <!-- <div class="infoIpt">
            <p>出院诊断名称</p>
            <input type="text" v-model="json.RYZDMC" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>出院诊断编码</p>
            <input type="text" v-model="json.RYZDBM" disabled="disabled"/>
        </div> -->
        <div class="infoIpt">
            <p>出院诊断</p>
            <!-- @keyDown="changeDown($event,'text')" @input="searching(false,'jbmc')"-->
            <input v-model="jbContent.jbmc" @input="searching(false,'jbmc')" @keyDown="changeDown($event,'text')" >
        	<search-table :message="searchCon" :selected="selSearch"
                      :them="them" :them_tran="them_tran" :page="page"
                      @click-one="checkedOneOut" @click-two="selectOne":not_empty="true">
       		</search-table>
        </div>
        <div class="infoIpt">
            <p>操作员工号</p>
            <input type="text" v-model="json.REG_STAFF" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>操作员姓名</p>
            <input type="text" v-model="json.REG_MAN" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>身份证号码</p>
            <input  v-model="json.FACT_IDCARD"  disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>出院原因</p>
         	<select-input @change-data="resultChange"
                      :child="qyyb_fin_info" :index="json.fin_info" :val="json.fin_info"
                      :search="true" :name="'json.fin_info'" :not_empty="true">
        	</select-input>

        </div>

        <div class="infoIpt">
            <p>HIS费用合计</p>
            <input type="text" v-model="json.YBJS_ZFY"  disabled="disabled"/>
        </div>

        <div class="infoIpt">
            <p>提交到医保费用</p>
            <input type="text" v-model="json.YBJS_BXTJFY"  disabled="disabled"/>
        </div>

        <!--
        <div class="infoIpt">
            <p>出院科室</p>
            <input type="text" v-model="rcContent.prm_ykc015"  data-notEmpty="fasle" @keydown="nextFocus($event)"/>
        </div>
        <div class="infoIpt">
            <p>出院诊断</p>
            <input v-model="jbContent.jbmc" @input="searching(false,'jbmc')" @keyDown="changeDown($event,'text')">
        	<search-table :message="searchCon" :selected="selSearch"
                      :them="them" :them_tran="them_tran" :page="page"
                      @click-one="checkedOneOut" @click-two="selectOne":not_empty="true">
       		</search-table>
        </div>
        <div class="infoIpt">
            <p>出院附属诊断代码</p>
            <input type="text" v-model="rcContent.prm_ykd065"  data-notEmpty="fasle" @keydown="nextFocus($event)" disabled="disabled"/>
        </div>
        <div class="infoIpt place">
            <p>第一出院疾病诊断代码</p>
            <input v-model="jbContent1.jbmc1" @input="searching1(false,'jbmc1')" @keyDown="changeDown1($event,'text')">
        	<search-table1 :message="searchCon1" :selected="selSearch"
                      :them="them" :them_tran="them_tran" :page="page"
                      @click-one="checkedOneOut" @click-two="selectOne1":not_empty="true">
       		</search-table1>
        </div>
        <div class="infoIpt place">
            <p>第二出院疾病诊断代码</p>
            <input v-model="jbContent2.jbmc2" @input="searching2(false,'jbmc2')" @keyDown="changeDown2($event,'text')">
        	<search-table2 :message="searchCon2" :selected="selSearch"
                      :them="them" :them_tran="them_tran" :page="page"
                      @click-one="checkedOneOut" @click-two="selectOne2":not_empty="false">
       		</search-table2>
        </div>
        <div class="infoIpt" style="width: 100%">
            <p>第三出院疾病诊断代码</p>
            <input v-model="jbContent3.jbmc3" @input="searching3(false,'jbmc3')" @keyDown="changeDown3($event,'text')">
        	<search-table3 :message="searchCon3" :selected="selSearch"
                      :them="them" :them_tran="them_tran" :page="page"
                      @click-one="checkedOneOut" @click-two="selectOne3":not_empty="false">
       		</search-table3>
        </div>
        <div class="infoIpt" style="margin: 20px 100px;">
            <input type="checkbox" />
            <p style="width: auto">单病种</p>
        </div> -->
    </div>


    <div class="bxqk printHide" style="display: inline-block;">
         <div class="conTit"><div>报销情况</div></div>
        <div class="infoIpt">
            <p>医疗保险统筹基金</p>
            <input type="text" v-model="json.YBJS_TCJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>医疗保险个人帐户</p>
            <input type="text" v-model="json.YBJS_GRZH" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>家庭账户基金</p>
            <input type="text" v-model="json.YBJS_JTZHJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>医疗保险大病互助基金</p>
            <input type="text" v-model="json.YBJS_DBHZJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>医疗保险离休基金</p>
            <input type="text" v-model="json.YBJS_LXJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>医疗保险公务员补助</p>
            <input type="text" v-model="json.YBJS_GWYBZ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>大病补助基金</p>
            <input type="text" v-model="json.YBJS_DBBZJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>二次补助基金</p>
            <input type="text" v-model="json.YBJS_ECBZJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>工伤保险基金</p>
            <input type="text" v-model="json.YBJS_GSBXJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>生育保险基金城乡居民所用</p>
            <input type="text" v-model="json.YBJS_CXSYBXJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>现金</p>
            <input type="text" v-model="json.YBJS_XJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>居民统筹基金</p>
            <input type="text" v-model="json.YBJS_JMTCJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>城乡居民大病补助</p>
            <input type="text" v-model="json.YBJS_CXJMDBBZ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>农合统筹基金</p>
            <input type="text" v-model="json.YBJS_NHTCJJ" disabled="disabled"/>
        </div>
        <div class="infoIpt">
            <p>医院支付</p>
            <input type="text" v-model="json.YBJS_YYZF" disabled="disabled"/>
        </div>
    </div>
</div>
<div class="yjsprint printShow" v-show="yjsShow">

    <div class="boxwidth">
        <h2>青海省城乡居民基本医疗保险住院结算单</h2>
        <p>打印日期:2020年5月20日</p>
        <div class="yjs-title-name">
            <span v-text="ybData.hospital_name">医疗机构名称:{{ybData.hospital_name}}</span>
            <span>医院等级：{{ybData.hosp_level_name}}  {{ybData.hosp_grade_name}}</span>
            <span>医保住院登记号：{{hisData.SERIAL_NO}}</span>
            <span>金额单位：元</span>
        </div>
        <ul class="yjs-box-meassge">
            <li>
                <span class="yjs-span "><i class="border-r wh70 text-right">姓名:</i><i class="wh100">{{ybData.name}}</i></span>
                <span class="yjs-span "><i class="border-r wh50 text-center">性别:</i><i class="wh50">{{ybData.sex}}</i></span>
                <span class="yjs-span "><i class="border-r wh70 text-center">出生年月：</i><i class="wh70">{{ybData.birthday}}</i></span>
                <span class="yjs-span"><i class="border-r wh80 text-center">个人电脑号：</i><i class="wh80">{{ybData.indi_id}}</i></span>
                <span class="yjs-span border-none"><i class="border-r wh70 text-center">人员类别：</i><i class="wh80">{{ybData.pers_name}}</i></span>
            </li>
            <li>
                <span class="yjs-span "><i class="border-r wh70 text-right">单位名称：</i><i class="wh150">{{ybData.corp_name}}</i></span>
                <span class="yjs-span "><i class="border-r wh70 text-center">出院状态：</i><i class="wh70">{{}}</i></span>
                <span class="yjs-span "><i class="border-r wh70 text-center">联系电话：</i><i class="wh80">{{hisData.SJHM}}</i></span>
                <span class="yjs-span border-none"><i class="border-r wh70 text-center">身份证号：</i><i class="wh150">{{hisData.SFZJHM}}</i></span>
            </li>
            <li>
                <span class="yjs-span"><i class="border-r wh70 text-right">住院号:</i><i class="wh70">{{hisData.ZYH}}</i></span>
                <span class="yjs-span"><i class="border-r wh50 text-center">科别：</i><i class="wh70">{{ybData.in_dept_name}}</i></span>
                <span class="yjs-span"><i class="border-r wh50 text-center">床号：</i><i class="wh50">{{hisData.RYCWBH}}</i></span>
                <span class="yjs-span"><i class="border-r wh70 text-center">住院时间：</i><i class="wh70">{{ybData.begin_date}}</i></span>
                <span class="yjs-span"><i class="border-r wh70 text-center">出院时间：</i><i class="wh70">{{ybData.end_date}}</i></span>
                <span class="yjs-span border-none"><i class="border-r wh70 text-center">住院天数：</i><i class="wh50 text-center">{{ybData.days}}</i></span>
            </li>
            <li>
                <span class="yjs-span"><i class="border-r wh100 text-right">入院第一诊断：</i><i class="wh150">{{ybData.in_disease}}</i></span>
                <span class="yjs-span border-none"><i class="border-r wh150 text-right">出院第一诊断：</i><i class="wh150">{{ybData.fin_disease}}</i></span>
            </li>
            <li>
                <span class="yjs-span"><i class="border-r wh70 text-right">待遇类别:</i><i class="wh150">{{ybData.treatment_name}}</i></span>
                <span class="yjs-span"><i class="border-r wh70 text-center">结算时间:</i><i class="wh100">{{hisData.JSRQ}}</i></span>
                <span class="yjs-span border-none"><i class="border-r wh100 text-center">主管医师:</i><i class="wh100">{{hisData.ZYYSXM}}</i></span>
            </li>
            <li>
                <div class="yjs-span yjs-jrfy">既往费用</div>
                <div class="yjs-span yjs-bnzy">
                    <span class="yjs-bt">本年住院</span>
                    <span>{{hisData.INHOSP_YEAR}}次</span>
                </div>
                <div class="yjs-span yjs-bnd border-none">
                    <span class="bnd-span text-center">本年度基本费用累计</span>
                    <span class=" bnd-span bnd-t text-center">{{hisData.SPECIAL_YEAR}}</span>
                </div>
                <div class="yjs-span border-none" style="height: 100px;width:85%;border-left:1px solid #0d0d0d">
                    <h3 class="text-center yjs-h3 bnd-b">本年度已发生的住院医疗费用累计支出</h3>
                    <span class="bnt-width"><p class="line-10">医疗费合计</p><p class="line-10">{{hisData.SPECIAL_YEAR}}</p></span>
                    <span class="bnt-width"><p class="line-10">已付起付钱</p><p class="line-10">{{hisData.QFX_YEAR}}</p></span>
                    <span class="bnt-width"><p class="line-10">统筹支付</p><p class="line-10">{{hisData.FUND_YEAR}}</p></span>
                    <span class="bnt-width"><p class="line-10">政策自付</p><p class="line-10">{{}}</p></span>
                    <span class="bnt-width"><p class="line-10">分段比例自付</p><p class="line-10">{{}}</p></span>
                    <span class="bnt-width border-none"><p>大病支付</p><p class="line-10">{{hisData.ADDITIONAL_YEAR}}</p></span>
                </div>
            </li>
            <li>
                <div class="yjs-fylb bnd-b">
                    <i class="border-r text-center">费用类别</i>
                    <i class="border-r text-center">总费用</i>
                    <i class="border-r text-center">政策完全自费</i>
                    <i class="border-r text-center">政策部分自付</i>
                    <i class="border-r text-center">费用类别</i>
                    <i class="border-r text-center">总费用</i>
                    <i class="border-r text-center">政策完全自费</i>
                    <i class=" text-center">政策部分自付</i>
                </div>
                <div class="yjs-fylb bnd-b">
                    <i class="border-r text-center">西药费</i>
                    <i class="border-r text-center">597.5</i>
                    <i class="border-r text-center">0.0</i>
                    <i class="border-r text-center">150.2</i>
                    <i class="border-r text-center">材料费</i>
                    <i class="border-r text-center">7.90</i>
                    <i class="border-r text-center">7.6</i>
                    <i class="text-center">0.00</i>
                </div>
                <div class="yjs-fylb">
                    <i class="border-r text-center">合计</i>
                    <i class="border-r text-center">178964</i>
                    <i class="border-r text-center">1723.5</i>
                    <i class="border-r text-center">165.3</i>
                    <i class="border-r text-center">165.3</i>
                    <i class="border-r text-center">165.3</i>
                    <i class="border-r text-center">165.3</i>
                    <i class="text-center">165.3</i>
                </div>
            </li>
            <li>
                <div class="yjs-money bnd-b">
                    <i class="border-r text-center">/</i>
                    <i class="border-r text-center">现金支付</i>
                    <i class="border-r text-center">个人账户支付</i>
                    <i class="border-r text-center">基金支付</i>
                    <i class="text-center">合计</i>
                </div>
                <div class="yjs-money bnd-b">
                    <span class="border-r text-center">/</span>
                    <span class="border-r text-center"><em class="yjs-em border-r">金额</em><em class="yjs-em">比例</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">金额</em><em class="yjs-em">比例</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">金额</em><em class="yjs-em">比例</em></span>
                    <span class=" text-center"></span>
                </div>
                <div class="yjs-money bnd-b">
                    <span class="border-r text-center">完全政策自付</span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class=" text-center"> 235.6</span>
                </div>
                <div class="yjs-money bnd-b">
                    <span class="border-r text-center">部分政策自付</span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class=" text-center"> 235.6</span>
                </div>
                <div class="yjs-money bnd-b">
                    <span class="border-r text-center">转外自理费用</span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class=" text-center"> 235.6</span>
                </div>
                <div class="yjs-money bnd-b">
                    <span class="border-r text-center">应付起付钱</span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class=" text-center"> 235.6</span>
                </div>
                <div class="yjs-money bnd-b">
                    <span class="border-r text-center">统筹一段</span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class="border-r text-center"><em class="yjs-em border-r">136.6</em><em class="yjs-em">156.6</em></span>
                    <span class=" text-center"> 235.6</span>
                </div>
                <div class="yjs-money">
                    <i class="border-r text-center">合计</i>
                    <i class="border-r text-center">1010.23</i>
                    <i class="border-r text-center">135.3</i>
                    <i class="border-r text-center">135.102</i>
                    <i class=" text-center">1738.26</i>
                </div>
            </li>
            <li class="jyjs-btn">
                <span class="yjs-span border-r text-right">本次医疗总费用：</span>
                <span class="yjs-span border-r text-right">{{ybData.total_pay}}</span>
                <span class="yjs-span border-r text-center">大写：</span>
                <span class="yjs-span  border-none">壹仟柒佰叁拾捌元肆角玖分</span>
            </li>
            <li class="jyjs-btn">
                <span class="yjs-span border-r text-right">个人自付金额：</span>
                <span class="yjs-span border-r text-right">{{ybData.blzf}}</span>
                <span class="yjs-span border-r text-center">大写：</span>
                <span class="yjs-span border-none">壹仟柒佰叁拾捌元肆角玖分</span>
            </li>
            <li class="jyjs-btn">
                <span class="yjs-span border-r text-right">医院支付金额：</span>
                <span class="yjs-span border-r text-right">{{ybData.hosp_pay}}</span>
                <span class="yjs-span border-r text-center">大写：</span>
                <span class="yjs-span  border-none">壹仟柒佰叁拾捌元肆角玖分</span>
            </li>
            <li class="jyjs-btn">
                <span class="yjs-span border-r text-right">个人账户支付金额：</span>
                <span class="yjs-span border-r text-right">{{ybData.acct_pay}}</span>
                <span class="yjs-span border-r text-center">大写：</span>
                <span class="yjs-span  border-none">壹仟柒佰叁拾捌元肆角玖分</span>
            </li>
            <li class="jyjs-btn">
                <span class="yjs-span border-r text-right">个人现金支付金额：</span>
                <span class="yjs-span border-r text-right">1738.39</span>
                <span class="yjs-span border-r text-center">大写：</span>
                <span class="yjs-span  border-none">壹仟柒佰叁拾捌元肆角玖分</span>
            </li>
            <li class="jyjs-btn">
                <span class="yjs-span border-r text-right">城乡基本统筹支付：</span>
                <span class="yjs-span border-r text-right">{{ybData.base_pay}}</span>
                <span class="yjs-span border-r text-center">大写：</span>
                <span class="yjs-span  border-none">壹仟柒佰叁拾捌元肆角玖分</span>
            </li>
            <li class="jyjs-btn">
                <span class="yjs-span border-r text-right">城乡大病基金支付：</span>
                <span class="yjs-span border-r text-right">{{ybData.zhaogu_pay}}</span>
                <span class="yjs-span border-r text-center">大写：</span>
                <span class="yjs-span  border-none">壹仟柒佰叁拾捌元肆角玖分</span>
            </li>
            <li class="jyjs-btn">
                <div class="yjs-ybj border-r">预缴款</div>
                <div class="yjs-kkkl">
                    <div class="yjs-mmmml bnd-b">
                        <span class="yjs-span border-r">已交的预缴款</span>
                        <span class="yjs-span border-r">{{hisData.DBJE}}</span>
                        <span class="yjs-span border-r">大写：</span>
                        <span class="yjs-span border-none">壹仟柒佰叁拾捌元肆角玖分</span>
                    </div>
                    <div class="yjs-mmmml bnd-b">
                        <span class="yjs-span border-r">应交金额</span>
                        <span class="yjs-span border-r">{{hisData.YBJS_ZFY}}</span>
                        <span class="yjs-span border-r">大写：</span>
                        <span class="yjs-span border-none">壹仟柒佰叁拾捌元肆角玖分</span>
                    </div>
                    <div class="yjs-mmmml ">
                        <span class="yjs-span border-r">应退金额</span>
                        <span class="yjs-span border-r">0.00</span>
                        <span class="yjs-span border-r">大写：</span>
                        <span class="yjs-span border-none">壹仟柒佰叁拾捌元肆角玖分</span>
                    </div>
                </div>

            </li>
        </ul>
        <div class="yjs-gzry">
            <span>结算员 :&ensp;&ensp;&ensp;工作人员1</span>
            <span>财务审核</span>
            <span>病人(家属)签字:</span>
        </div>
    </div>

</div>
<style type="text/css">
    .yjsprint{
        width:1200px;
        height: 279mm;
        font-size: 12px;
        margin: 0 auto;
    }
    .boxwidth{
        float: left;
        margin: 0 auto;
    }
    .yjsprint h2{
        text-align: center;
    }
    .yjsprint p{
        text-align: center;
    }
    .yjs-title-name{
        width: 100%;
        margin: 0 auto;
        text-align: center;
    }
    .yjs-title-name span{
        width: 20%;
        display: inline-block;
    }
    .yjs-title-name span:first-child{
        width: 25%;
    }
    .yjs-box-meassge{
        width: 100%;
        border-bottom: 1px solid #0d0d0d;
        overflow: hidden;
    }
    .yjs-box-meassge li{
        width: 100%;
        box-sizing: border-box;
        border: 1px solid #0d0d0d;
        border-bottom: none;
        overflow: hidden;
        line-height: 26px;
    }
    .line-10{
        line-height: 10px !important;
    }
    .yjs-box-meassge li span{
        display: block;
        float: left;
    }
    .yjs-box-meassge li .yjs-span{
        display: block;
        float: left;
        border-right:1px solid #0d0d0d;
        width: auto;
    }

    .yjs-box-meassge li .yjs-span i{
        float: left;
    }
    .border-r{
        display:block;
        border-right: 1px solid #0d0d0d;
        float: left;
    }
    .wh100{
        width: 100px;
    }
    .wh50{
        width: 50px;
    }
    .wh70{
        width: 70px;
    }
    .wh80{
        width: 80px;
    }
    .wh150{
        width: 150px;
    }
    .wh200{
        width: 200px;
    }
    .text-right{
        text-align: right;
    }
    .text-center{
        text-align: center;
    }

    .yjs-box-meassge li .yjs-jrfy{
        width:15px;
        height: 100px;
        float: left;
    }
    .yjs-box-meassge li .yjs-bnzy{
        width:15px;
        height: 100px;
        float: left;
    }
    .yjs-bt{

    }
    .yjs-box-meassge li .yjs-bnd{
        width:80px;
        float: left;
    }
    .bnd-span{
        display: block;
        width: 100%;
    }
    .bnd-t{
        border-top: 1px solid #0d0d0d;
    }
    .bnd-b{
        border-bottom: 1px solid #0d0d0d;
    }
    .yjs-h3{
        float: left;
        width: 100%;
        margin: 0;
        padding-bottom: 13px;
    }
    .bnt-width{
        width: calc((100%  - 5px) /6);
        border-right: 1px solid #0d0d0d;
    }
    .yjs-box-meassge li .border-none{
        border-right: none;
    }
    .yjs-box-meassge li .yjs-fylb{
        overflow: hidden;
        width: 100%;
    }
    .yjs-box-meassge li .yjs-fylb i{
        width: calc((100% - 8px)/8);
        display: block;
        float: left;
    }
    .yjs-money{
        overflow: hidden;
        width: 100%;
    }
    .yjs-box-meassge li .yjs-money i{
        width: calc((100% - 5px)/5);
        display: block;
        float: left;
    }
    .yjs-box-meassge li .yjs-money span{
        width: calc((100% - 5px)/5);
        display: block;
        float: left;
    }
    .yjs-em{
        width: 49%;
        display: block;
        float: left;
    }
    .yjs-box-meassge li.jyjs-btn span{
        width: calc((100% - 305px ));
    }
    .yjs-box-meassge li.jyjs-btn span:nth-child(3){
        width: 50px;
    }
    .yjs-box-meassge li.jyjs-btn span:nth-child(1){
        width: 150px;
    }
    .yjs-box-meassge li.jyjs-btn span:nth-child(2){
        width: 100px;
    }
    .yjs-ybj{
        width: 15px;
        float: left;
        text-align: center;
    }
    .yjs-kkkl{
        width: calc(100% - 16px);
        float: left;
    }
    .yjs-mmmml{
        width: 100%;
        overflow: hidden;
    }
    .yjs-mmmml span:nth-child(1){
        width: 135px !important;
    }
    .yjs-mmmml span:nth-child(2){
        width: 60px;
    }
    .yjs-mmmml span:nth-child(3){
        width: 50px;
    }
    .yjs-mmmml span{
        width: calc(100% - 245px);
    }
    .yjs-box-meassge li.yjs-gzry{
        width: 100%;
    }
    .yjs-gzry{
        width: 100%;
        padding-top: 40px;
    }
    .yjs-gzry span{
        width: calc(100% / 3);
        display: block;
        float: left;
    }
</style>

<script type="application/javascript" src="yjs.js"></script>
