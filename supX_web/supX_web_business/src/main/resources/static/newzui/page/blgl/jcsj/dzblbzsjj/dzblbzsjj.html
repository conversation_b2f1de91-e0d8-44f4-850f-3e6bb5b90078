<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>电子病历标准数据集</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="dzblbzsjj.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">添加</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
            <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full" >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="sexs" z-width="60px">
                        <div class="zui-table-cell">数据集id</div>
                    </th>
                    <th z-field="sex" z-width="80px">
                        <div class="zui-table-cell">表示格式</div>
                    </th>
                    <th z-field="city" z-width="60px">
                        <div class="zui-table-cell">重复次数</div>
                    </th>
                    <th z-field="sysx" z-width="100px">
                        <div class="zui-table-cell">定义描述</div>
                    </th>
                    <th z-field="jm1" z-width="60px">
                        <div class="zui-table-cell">数据组id</div>
                    </th>
                    <th z-field="jm2" z-width="100px">
                        <div class="zui-table-cell">数据组名称</div>
                    </th>
                    <th z-field="jm3" z-width="70px">
                        <div class="zui-table-cell">内部标志符</div>
                    </th>
                    <th z-field="jm4" z-width="70px">
                        <div class="zui-table-cell">强制性标志</div>
                    </th>
                    <th z-field="jm5" z-width="50px">
                        <div class="zui-table-cell">标识符</div>
                    </th>
                    <th z-field="jm6" z-width="100px">
                        <div class="zui-table-cell">简码</div>
                    </th>
                    <th z-field="jm7" z-width="100px">
                        <div class="zui-table-cell">数据元名称</div>
                    </th>
                    <th z-field="jm8" z-width="50px">
                        <div class="zui-table-cell">数据类型</div>
                    </th><th z-field="jm5" z-width="60px">
                    <div class="zui-table-cell">允许值</div>
                    </th>
                    <th z-field="jm9" z-width="100px">
                        <div class="zui-table-cell">医疗机构编码</div>
                    </th>
                    <th z-field="jm10" z-width="100px">
                        <div class="zui-table-cell">最近操作时间</div>
                    </th>
                    <th z-field="jm11" z-width="100px">
                        <div class="zui-table-cell">备注说明</div>
                    </th>
                    <th z-field="jm12" z-width="60px">
                        <div class="zui-table-cell">状态</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-height" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.bzsjjid"></div></td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.bsgs" :data-title="item.bsgs"></i>
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell" v-text="item.cfcs">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.dyms" :data-title="item.dyms"></i>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.lcsjzid"></div></td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.lcsjzmc" :data-title="item.lcsjzmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.nbbzf" :data-title="item.nbbzf"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.qzxbz" :data-title="item.qzxbz"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sjybzf" :data-title="item.sjybzf"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sjyjm" :data-title="item.sjyjm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sjymc" :data-title="item.sjymc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sjysjlx" :data-title="item.sjysjlx"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.sjyyxz" :data-title="item.sjyyxz"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.yljgbm" :data-title="item.yljgbm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="fDate(item.zjczsj,'date')" :data-title="fDate(item.zjczsj,'date')"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.bzsm" :data-title="item.bzsm"></i>
                        </div>
                    </td>
                    <td><div class="zui-table-cell">
                        <div class="switch">
                            <input  type="checkbox" :checked="item.scbz==0?true:false" disabled/>
                            <label></label>
                        </div>
                    </div>
                    </td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>临床数据组</i>
                    <select-input @change-data="resultChange"
                                  :not_empty="false" :child="LcList"
                                  :index="'sjzmc'" :index_val="'sjzmc'"
                                  :val="sjzmc" :search="true" :name="'sjzmc'"
                                  id="sjzmc" :index_mc="'sjzmc'">
                    </select-input>
            </li>
            <li id="jyxm_icon">
                    <i>状态</i>
                    <div class="switch sy-switch">
                        <input  type="checkbox" :checked="popContent.scbz==0?true:false"/>
                        <label></label>
                    </div>
            </li>
        </ul>
        <ul class="tab-edit-list tab-edit2-list border-dotted-t fl padd-t-15">
            <li>
                    <i>内部标识符</i>
                    <input type="text" class="label-input" v-model="popContent.nbbzf" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>数据元标识符</i>
                    <input type="text" class="label-input"  v-model="popContent.sjybzf" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>数据元名称</i>
                    <input type="text" class="label-input"  v-model="popContent.sjymc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>数据元简码</i>
                    <input type="text" class="label-input"  v-model="popContent.sjyjm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>数据元数据类型</i>
                    <input type="text" class="label-input"  v-model="popContent.sjysjlx" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>数据元允许值</i>
                    <input type="text" class="label-input"  v-model="popContent.sjyyxz" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>医疗机构编码</i>
                    <input type="text" class="label-input"  v-model="popContent.yljgbm" @keydown="nextFocus($event)"/>
            </li>
            <li style="width: 100%;float:left;">
                    <i>定义描述</i>
                    <input type="text" class="label-input" style="width: 76%;height: 60px"  v-model="popContent.dyms" @keydown="nextFocus($event)"/>
            </li>
            <li  style="width: 100%; float:left;margin-top: 20px;">
                    <i>备注说明</i>
                    <input type="text" class="label-input" style="width: 76%;height: 60px"  v-model="popContent.bzsm" @keydown="nextFocus($event)"/>
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="dzblbzsjj.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
