var sssq = new Vue({
    el: '#sssq',
    mixins: [dic_transform, baseFunc, tableBase, mformat, checkData],
    data: {
        pageState: {
            sslx:'1'
        },
        Content: {},
        ssksList: [],
        ysData: [],
        num: 0,
        flag: false,
        disable: false,
        lsYzList: [],
        KsxxList: [],
        zybmList: [],
        json: {
            ysbz: '1',
            tybz: '0',
        },
        val: false,
        dg: {
            page: '1',
            rows: '',
            parm: '',
        },
        objCsqx:{},
        sssqdh: '',
        searchCon: [],
        them_tran: {'jb': dic_transform.data.ssjb_tran},
        them: {'手术编码': 'ssbm', '手术名称': 'ssmc', '拼音代码': 'pydm', '手术级别': 'jb'},
        selSearch: -1,
        allBmContent: {},
        page: {page: 1, rows: 10, total: null},
    },
    components: {
        'search-table': searchTable,
    },
    filters: {
        initDate: function (value) {
            var d = new Date(value);
            return '' + d.getFullYear() + '年' + (d.getMonth() + 1) + '月' + d.getDate() + '日'
        },
        initDate1: function (value) {
            var d = new Date(value);
            return '' + d.getFullYear() + '年' + (d.getMonth() + 1) + '月' + d.getDate() + '日' + d.getHours() + '点'
        },
        initDate2: function (value) {
            if (value) {
                var d = new Date(value);
                return '' + d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
        },
    },
    created: function () {
        this.pageState.sqzd=userNameBg.Brxx_List.ryzdmc
        this.getYs()
        this.getKsxx()
        this.readyData({"zylb": "03"}, "zyzbm", "zybmList"); //值域下拉框
    },
    mounted: function () {
        laydate.render({
            elem: '#sqTime'
            , trigger: 'click',
            theme: '#1ab394'
            , done: function (value, data) {
                sssq.pageState.sqrq = value;
            }
        });
        laydate.render({
            elem: '#jhTime'
            , trigger: 'click'
            , theme: '#1ab394'
            , done: function (value, data) {
                sssq.pageState.jhrq = value;
            }
        });
        this.getSssq('', 'lsYzList')
        this.getCsqx()
    },
    computed: {
        listenSsksList: function () {
            if (this.ssksList.length > 0) {
                for (var i = 0; i < this.ssksList.length; i++) {
                    if(this.ssksList[i].ksbm == this.pageState.ssks){
                        this.pageState.ssks = this.ssksList[i].ksbm;
                        this.pageState.ssksmc = this.ssksList[i].ksmc;
                    }
                }
            }
        },
    },
    methods: {
        getCsqx: function () {
            //获取参数权限
            parm = {
                "ylbm": 'N010064001',
                "ksbm": userNameBg.qxks
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N01006400109": //医嘱手术科室
                                    if (csjson.csz) {
                                        Vue.set(sssq.pageState, 'ssks', csjson.csz)
                                        Vue.set(sssq.objCsqx, 'ssks', csjson.csz)
                                    }
                                    break;

                            }
                        }

                    }
                } else {
                    malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                }
            });

        },
        printData: function () {
            if (this.sssqdh != '') {
                var reportlets = "[{reportlet: 'fpdy%2Fzygl%2Fzygl_sssq.cpt',sssqdh:'" + this.sssqdh + "'}]";
                if (!FrPrint(reportlets, null)) {
                    window.print();
                }
            } else {
                malert('请先选择手术申请单号', 'top', 'defeadted');
            }
        },
        //共用下拉框请求后台
        readyData: function (req, types, listName) {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types + "&json=" + JSON.stringify(req), function (json) {
                if (json.a == 0)
                    sssq[listName] = json.d.list;
                else
                    malert(types + "查询失败", 'top', 'defeadted');
            });
        },
        getKsxx: function () {
            this.param.rows = 2000
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhKsryKsbm&types=query&dg=' + JSON.stringify(this.param) + '', function (json) {
                if (json.a == '0') {
                    sssq.ssksList = json.d.list;
                }
            });
        },
        resultChangeMc: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "sqys":
                    Vue.set(this.pageState, 'sqys', val[0]);
                    Vue.set(this.pageState, 'ysxm', val[4]);
                    this.nextFocus(val[1]);
                    this.$forceUpdate();
                    break;
                case "ssks":
                    Vue.set(this.pageState, 'ssks', val[0]);
                    Vue.set(this.pageState, 'ssksmc', val[4]);
                    this.$forceUpdate();
                    break;
            }
        },
        add: function () {
            this.pageState = {
                ssks:this.objCsqx.ssks
            };
            this.num = undefined
        },
        zf: function () {
            if (this.num == undefined) {
                malert("请选择一个手术申请作废", 'top', 'defeadted');
                return false
            }
            var str = {
                zyh: userNameBg.Brxx_List.zyh,
                sssqdh: this.lsYzList[this.num].sssqdh,
                yljgbm: this.lsYzList[this.num].yljgbm,
                zfbz:1,
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=ModifySssq&parm=' + JSON.stringify(str) + '', function (json) {
                if (json.a == '0') {
                    malert(json.c, 'top', 'success');
                    sssq.num = 0
                    sssq.getSssq('', 'lsYzList')
                }else if (json.a == '-1'){
                    malert(json.c, 'top', 'defeadted');
                }
            });
        },
        getSssq(sssqdh, list) {
            this.sssqdh = sssqdh
            var str = {
                parm: userNameBg.Brxx_List.zyh,
                sssqdh: sssqdh,
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=QuerySssq&parm=' + JSON.stringify(str) + '', function (json) {
                if (json.a == '0' && json.d != null) {
                    if (list == 'pageState') {
                        sssq[list] = json.d.list[0];
                    } else {
                        sssq[list] = json.d.list;
                    }
                    if (!sssq.flag && json.d.list.length != 0) {
                        sssq.getSssq(json.d.list[0].sssqdh, 'pageState')
                    }
                    sssq.flag = true
                } else {
                }
            });
        },
        getSssqData: function (sssqdh, list, index) {
            this.num = index
            this.getSssq(sssqdh, list)
        },
        SaveSssq: function () {
            this.disable = true
            // 提交前验证数据
            if (!this.empty_sub('contextInfo')) {
                this.disable = false;
                return false;
            }
            this.pageState.zyh = userNameBg.Brxx_List.zyh
            this.pageState.ksbm = userNameBg.Brxx_List.ryks
            this.pageState.ksmc = userNameBg.Brxx_List.ryksmc
            this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=SaveSssq', JSON.stringify(this.pageState)).then(function (data) {
                if (data.body.a == 0) {
                    if (sssq.num == undefined) {
                        sssq.add()
                    }
                    sssq.disable = false
                    malert(data.body.c, 'top', 'success');
                    sssq.getSssq('', 'lsYzList')
                } else {
                    sssq.disable = false
                    malert("医嘱保存失败：" + data.body.c, 'top', 'defeadted');
                }
            });
        },
        getYs: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(this.json) + "" + "&dg=" + JSON.stringify(this.dg), function (data) {
                if (data.a == '0' && data.d.list.length != 0) {
                    sssq.ysData = data.d.list;
                }
            });
        },
        doCheck: function () {

        },
        //当输入值后才触发
        change1: function (add, val) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.page.parm = val;
            this.pageState.zsmc = val;
            var str_param = {parm: this.page.parm, page: this.page.page, rows: 30};
            //手术编码
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ssbm' + '&json=' + JSON.stringify(str_param), function (data) {
                console.log(sssq.pageState)
                if (add) {
                        sssq.searchCon=sssq.searchCon.concat(data.d.list);
                } else {
                    console.log(sssq.pageState)
                    sssq.searchCon = data.d.list;
                }
                sssq.page.total = data.d.total;
                sssq.selSearch = 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                }
            });
        },
        //检索
        changeDown: function (event, searchCon) {
            if (this[searchCon][this.selSearch] == undefined) return;
            this.inputUpDown(event, 'searchCon', 'selSearch');
            this.Content = this[searchCon][this.selSearch]
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                Vue.set(this.pageState, 'zsbm', this.Content['ssbm']);
                Vue.set(this.pageState, 'zsmc', this.Content['ssmc']);
                this.nextFocus(event);
                $(".selectGroup").hide();
                this.$forceUpdate()
            }
        },
        selectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.change1(true, this.pageState.zsmc);
            } else {
                Vue.set(this.pageState, 'zsbm', item['ssbm']);
                Vue.set(this.pageState, 'zsmc', item['ssmc']);
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        }
    }
})
$('.contextInfo input[data-notEmpty=true]').on('blur', function () {
    if ($(this).val() == '' || $(this).val() == null) {
        $(this).addClass("emptyError");
    } else {
        $(this).removeClass("emptyError");
    }
});
