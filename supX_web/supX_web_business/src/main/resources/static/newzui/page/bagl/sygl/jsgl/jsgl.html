<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>追溯管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="zsgl.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper flex-container flex-dir-c" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary  icon-sx1 paddr-r5">刷新</button>
            <button class="tong-btn btn-parmary  icon-sx1 paddr-r5" @click="saveDate">登记</button>
            <button class="tong-btn btn-parmary  icon-sx1 paddr-r5" @click="qxdjDate">取消登记</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" 
                        placeholder="请输入关键字" 
                        type="text" 
                        id="jsvalue" 
                        v-model="brxxContent.text" 
                        @keydown="changeDown($event,'text')"
                        @input="change(false,'text')"/>
                        <search-table 
                            :message="searchCon" 
                            :selected="selSearch" 
                            :page="page" :them="them" 
                            :them_tran="them_tran" 
                            @click-one="checkedOneOut"
                            @click-two="selectOne">
                        </search-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="flex-one flex-container flex-dir-c">
        <header class="userNameBg">
            <div class="bazs-flex">
                <span class="bazs-maid"><img src="/newzui/pub/image/maid.png"/></span>
                <span class="bazs-name">
                    <p>
                        <em>{{jsonList[0].brxm}}</em>
                        <i>{{brxb_tran[jsonList[0].brxb]}}</i>
                        <i>{{jsonList[0].nl || ''}}{{nldw_tran[ jsonList[0].nldw ] || ''}}</i>
                    </p> 
                    <p>
                        <i>身份证号：{{jsonList[0].sfzjhm}}</i>
                        <i>住院号：{{jsonList[0].zyh}}</i>
                        <i>病案号：{{jsonList[0].bah}}</i>
                        <i>住院日期：{{jsonList[0].ryrq}}</i>
                    </p>
                    <p>
                        <i>诊断：</i>
                    </p>
                </span>
            </div>
        </header>
        <div class="bazs-time flex-one over-auto">
            <div class="bazs-left-time">
                <ul>
                    <!-- <li>
                        <span class="bazs-now"></span>
                        <div class="bazs-right-list">
                            <p>20183月15日 15:33:22</p>
                             <p class="color-green">系统 电子档案借阅到期，自动关闭借阅权限</p>
                        </div>
                    </li>
                    <li>
                        <span class="bazs-now bazs-ggg"></span>
                        <div class="bazs-right-list">
                            <p>20183月15日 15:33:22</p>
                            <p>系统 电子档案借阅到期，自动关闭借阅权限</p>
                        </div>
                    </li> -->
                </ul>
            </div>
        </div>
    </div>
</div>
<script src="jsgl.js"></script>
</body>
</html>