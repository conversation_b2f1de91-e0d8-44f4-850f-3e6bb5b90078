.tableDiv table {
    table-layout: fixed; /*只有定义了table下面的td才起作用*/
}

.tableDiv table td {
    max-width: 89px;
    overflow: hidden; /*内容超出宽度是隐藏超出部分的内容*/
    text-overflow: ellipsis; /*这两个配合使用（超出部分用...代替）*/

}

.loading {
    display: table;
    position: fixed;
    width: 100%;
    height: 100%;
    text-align: center;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: 999;
}

.loader-inner {
    margin: 200px 0 0 48%;
}

.loader-inner div {
    border: 0 !important;
    background-color: red;
}

.loadText {
    color: green;
    margin-top: 36%;
    font-size: 14px;
}

.toolMenu {
    display: block;
}

.brList {
    width: 30%;
    height: calc(100% - 82px);
    margin-right: 20px;
}

.brList th:nth-child(n+2) {
    min-width: 97px;
}

.xqList {
    width: 66%;
    height: calc(100% - 82px);
}

#jsfph {
    width: 150px;
    height: 25px;
    margin-left: 10px;
    margin-right: 10px;
}

.patientTable th:nth-child(n+2){

}
select {
    width: 9%;
    border: 1px solid #aaaaaa;
    height: 30px;
}
