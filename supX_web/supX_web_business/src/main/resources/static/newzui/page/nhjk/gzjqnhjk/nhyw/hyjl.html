<div id="hyjl">
    <div class="tong-top">
        <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addDj">登记/修改</button>
        <button class="tong-btn btn-parmary-b icon-sx paddr-r5">取消入院</button>
    </div>
    <div class="hyjls padd-t-30 padd-l-10 padd-r-10">
      <div class="jbxx margin-b-15">
          <div class="jbxx-size">
              <div class="jbxx-position">
                  <span class="jbxx-top"></span>
                  <span class="jbxx-text">入院信息</span>
                  <span class="jbxx-bottom"></span>
              </div>
              <div class="jbxx-box flex-start padd-t-10 padd-b-10 padd-l-10 padd-r-10">
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">住&ensp;院&ensp;号</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">病人姓名</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">性&ensp;&ensp;&ensp;&ensp;&ensp;别</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                          <!--<select-input class="wh182 background-f" @change-data="resultChange" :not_empty="false"-->
                                        <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                                        <!--:name="'popContent.brxb'">-->
                          <!--</select-input>-->
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">床&ensp;位&ensp;号</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">人员类别</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">出生日期</label>
                      <div class="top-zinle">
                          <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                          <input type="text" class="zui-input background-h times1 text-indent-20 wh182" disabled>
                      </div>

                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">病人年龄</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input  fl margin-r-5 wh182 background-h"   disabled/>
                      </div>

                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label">入院科室</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">住院医师</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">入院日期</label>
                      <div class="top-zinle flex-start">
                          <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                          <input type="text" class="zui-input background-h times2 text-indent-20 wh182" disabled>
                      </div>

                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">费&ensp;&ensp;&ensp;别</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">联系电话</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled />
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">保险病人</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                          <!--<select-input @change-data="resultChange" :data-notEmpty="false"-->
                                        <!--:child="istrue_tran" :index="popContent.bxbr" :val="popContent.bxbr"-->
                                        <!--:name="'popContent.bxbr'">-->
                          <!--</select-input>-->
                      </div>
                  </div>

                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">保险类别</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">保险卡号</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled />
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">个人代码</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h"  disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">疾病名称</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">入院情况</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h"  disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label">证&ensp;件&ensp;是否&ensp;齐&ensp;全</label>
                      <div class="top-zinle  background-f">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                          <!--<select-input  @change-data="resultChange"-->
                                         <!--:child="istrue_tran" :index="'zjsfqq'" :index_val="'zjsfqq'" :val="zjsfqq"-->
                                         <!--:name="'popContent.zjsfqq'" :search="true" :index_mc="'zjsfqq'">-->
                          <!--</select-input>-->

                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">手&ensp;术&ensp;名称&ensp;代&ensp;码</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label">入院类型</label>
                      <div class="top-zinle  background-f">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                          <!--<select-input  @change-data="resultChange"-->
                                         <!--:child="istrue_tran" :index="'zjsfqq'" :index_val="'zjsfqq'" :val="zjsfqq"-->
                                         <!--:name="'popContent.zjsfqq'" :search="true" :index_mc="'zjsfqq'">-->
                          <!--</select-input>-->

                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">保&ensp;险&ensp;费</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled />
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label">是否转诊</label>
                      <div class="top-zinle  background-f">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                          <!--<select-input  @change-data="resultChange"-->
                                         <!--:child="istrue_tran" :index="'sfzz'" :index_val="'sfzz'" :val="sfzz"-->
                                         <!--:name="'popContent.sfzz'" :search="true" :index_mc="'sfzz'">-->
                          <!--</select-input>-->

                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">转&ensp;诊&ensp;号</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">银行卡号</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">银行卡号开户人姓</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h"  disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">患者与开户人关系</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断1</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h"  disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断2</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断3</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断4</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断5</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断6</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断7</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断8</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其他诊断9</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>
                  <div class="top-form margin-b-10 flex-start margin-r-20">
                      <label class="top-label ">其&ensp;他&ensp;诊断&ensp;&ensp;10</label>
                      <div class="top-zinle">
                          <input type="text" class="zui-input wh182 background-h" disabled/>
                      </div>
                  </div>

              </div>
          </div>

      </div>

  </div>
    <div class="side-form pop-548" :class="{'ng-hide':nums==0}" v-cloak id="brzcList" role="form">
        <div class="fyxm-side-top flex-jus-sp">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--入院登记start-->
        <div class="ksys-side">
            <vue-scroll :ops="pageScrollOps">
            <ul class="tab-edit-list tab-edit2-list">
                <li>
                    <i>住院号</i>
                    <input type="text" class="zui-input" v-model="popContent.zyh" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>病人姓名</i>
                    <input type="text" class="zui-input" v-model="popContent.brxm" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>性别</i>
                    <select-input @change-data="resultChange" :not_empty="false" :child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb" :name="'popContent.brxb'" @keydown="nextFocus($event)">
                    </select-input>
                </li>
                <li>
                    <i>人员类别</i>
                    <input type="text" class="zui-input" v-model="popContent.rylb"/>
                </li>
                <li>
                    <i>出生日期</i>
                    <input type="text" id="scrq" class="zui-input text-indent30 sctimes " v-model="popContent.csrq"  @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>病人年龄</i>
                    <input type="text" class="zui-input flex-start fl margin-r-5 wh122" style="width: 225px" v-model="popContent.brnl"/>
                    <select-input style="width: 59px; margin-left: 6px;" class="flex-start"  @change-data="resultChange" :not_empty="false"
                                  :child="nldw_tran" :index="popContent.nldw" :val="popContent.nldw" :name="'popContent.nldw'">
                    </select-input>
                </li>
                <li>
                    <i>入院科室</i>
                    <input type="text" class="zui-input" v-model="popContent.ryks"/>
                </li>
                <li>
                    <i>住院医师</i>
                    <input type="text" class="zui-input" v-model="popContent.zyys"/>
                </li>
                <li>
                    <i>入院日期</i>
                    <input type="text" id="ryrq" class="zui-input text-indent30 sctimes1 " v-model="popContent.ryrq"  @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>费别</i>
                    <input type="text" class="zui-input" v-model="popContent.fb"/>
                </li>
                <li>
                    <i>联系电话</i>
                    <input type="text" class="zui-input" v-model="popContent.lxdh"/>
                </li>
                <li>
                    <i>保险病人</i>
                    <input type="text" class="zui-input" v-model="popContent.bxbr"/>
                </li>
                <li>
                    <i>保险类别</i>
                    <input type="text" class="zui-input" v-model="popContent.bxlb"/>
                </li>
                <li>
                    <i>保险卡号</i>
                    <input type="text" class="zui-input" v-model="popContent.bxkh"/>
                </li>
                <li>
                <i>个人代码</i>
                <input type="text" class="zui-input" v-model="popContent.grdm"/>
            </li>
                <li>
                    <i>疾病名称</i>
                    <input type="text" class="zui-input" v-model="popContent.jbmc"/>
                </li>
                <li>
                    <i>入院情况</i>
                    <input type="text" class="zui-input" v-model="popContent.ryqk"/>
                </li>
                <li>
                    <i>证件是否齐全</i>
                    <input type="text" class="zui-input" v-model="popContent.zjsfqq"/>
                </li>
                <li>
                    <i>手术名称代码</i>
                    <input type="text" class="zui-input" v-model="popContent.ssmcdm"/>
                </li>
                <li>
                    <i>入院类型</i>
                    <input type="text" class="zui-input" v-model="popContent.rylx"/>
                </li>
                <li>
                    <i>保险费</i>
                    <input type="text" class="zui-input" v-model="popContent.bxf"/>
                </li>
                <li>
                    <i>是否转诊</i>
                    <input type="text" class="zui-input" v-model="popContent.sfzz"/>
                </li>
                <li>
                    <i>转诊号</i>
                    <input type="text" class="zui-input" v-model="popContent.zzh"/>
                </li>
                <li>
                    <i>银行卡号</i>
                    <input type="text" class="zui-input" v-model="popContent.yhkh"/>
                </li>
                <li>
                    <i>银行卡号开户人姓</i>
                    <input type="text" class="zui-input" v-model="popContent.yhkhkhrx"/>
                </li>
                <li>
                    <i>患者与开户人关系</i>
                    <input type="text" class="zui-input" v-model="popContent.hzykhrgx"/>
                </li>
                <li>
                    <i>其他诊断1</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd1"/>
                </li>
                <li>
                    <i>其他诊断2</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd2"/>
                </li>
                <li>
                    <i>其他诊断3</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd3"/>
                </li>
                <li>
                    <i>其他诊断4</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd4"/>
                </li>
                <li>
                    <i>其他诊断5</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd5"/>
                </li>
                <li>
                    <i>其他诊断6</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd6"/>
                </li>
                <li>
                    <i>其他诊断7</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd7"/>
                </li>
                <li>
                    <i>其他诊断8</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd8"/>
                </li>
                <li>
                    <i>其他诊断9</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd9"/>
                </li>
                <li>
                    <i>其他诊断10</i>
                    <input type="text" class="zui-input" v-model="popContent.qtzd10"/>
                </li>




            </ul>
            </vue-scroll>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="save">保存</button>
        </div>
    </div>
</div>
<style type="text/css">
    .hyjls{
    position: relative;
    width: 100%;

    }
    .top-form .top-label{

        width:65px;
    }
</style>


<script type="application/javascript" src="hyjl.js"></script>