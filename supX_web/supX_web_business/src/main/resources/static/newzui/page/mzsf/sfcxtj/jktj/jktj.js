	//取开始时间
    var datebegin = getTodayDateBegin();
    var dateend = getTodayDateEnd();
    var tableInfo = new Vue({
        el: '#jktj',
        //混合js字典庫
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            jsonList: [],
            param: {
                page: 1,
                rows: 20,
                sort: 'jkrq',
                order: 'desc',
            },
    		type: null, //类型
            isCheckedall:false,
    		ksrq: datebegin, //开始时间
    		jsrq: dateend,  //结束时间
    		money: 0        //费用汇总金额
        },
        //页面渲染完成之后加载数据
        mounted: function () {
        	//默认加载当前时间
           $("#startTime").val(datebegin);
           $("#endTime").val(dateend);
        },
        methods: {
            // 选中单条
            checkOne: function (event,index) {
                if(event.srcElement.checked==true){
                    this.isChecked[index] = false;

                }else{
                    this.isChecked[index] = true;
                }
            },

            // 选中全部
            checkAll: function (event) {
                if (event.srcElement.checked==true) {
                    for (var i = 0; i < this.jsonList.length; i++) {
                        Vue.set(this.isChecked,i,true)
                        this.isCheckedall=true
                        // this.isChecked[i] = true;
                    }
                } else {
                    this.isChecked = [];
                    this.isCheckedall=false
                }
            },
            getData: function () {
            	this.param.beginrq=tableInfo.ksrq;
            	this.param.endrq=tableInfo.jsrq;
            	this.param.jkzt='0';
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=queryCwjk&parm="+JSON.stringify(this.param),function (json) {
                    if(json.a == 0){
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                        tableInfo.jsonList = json.d.list;
                    }
                });
                tableInfo.fyhz();
            },
            //插叙交款金额汇总
          	fyhz: function(){
          		var str_param = {
					beginrq:tableInfo.ksrq,
					endrq:tableInfo.jsrq
				}
				$.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=queryHzJkje&parm="+JSON.stringify(str_param),function (json) {
	                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
	            	if(json.a == 0 ){
	            		if(json.d==null){
	            			json.d=0;
	            		}
	            		tableInfo.money = -json.d;//赋值操作
	            	}else{
	            		malert("获取汇总金额失败！"+json.c ,'top','defeadted');
	            	}
				});
          	},
        }
    });
    //初始化页面需要加载的数据
    tableInfo.getData();

    //为table循环添加拖拉的div
    var drawWidthNum = $(".patientTable tr").eq(0).find("th").length;
    for (var i = 0; i < drawWidthNum; i++) {
        if (i >= 2) {
            $(".patientTable th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
        }
    }
    laydate.render({
        elem: '#startTime'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,format:'yyyy-MM-dd HH:mm:ss'
        ,done:function (value,data) {
            tableInfo.ksrq=value
        }
    });
    laydate.render({
        elem: '#endTime'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,format:'yyyy-MM-dd HH:mm:ss'
        ,done:function (value,data) {
            tableInfo.jsrq=value
        }
    });
