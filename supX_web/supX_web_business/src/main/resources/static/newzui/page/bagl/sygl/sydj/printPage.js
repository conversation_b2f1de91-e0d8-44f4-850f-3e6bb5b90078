/**
 * Created by mash on 2017/11/9.
 */
var printPage = new Vue({
        el: '.printFormat',
        mixins: [tableBase, baseFunc, mformat, checkData],
        data: {
            isShow: undefined, //打印展示
            jbxxContent: {}, //病案基本信息
            zdxxContent: {}, //诊断基本信息
            ssjcContent: {}, //手术及检查信息
            fyxxContent: {},  //费用基本信息
            printType: false,
            csqx4007200101: '0',
            csqx4007200110: '0',
            csqx4007200111: '0',
            zybmList: [], //麻醉方式
            yhryList: [], //医护人员
            /*zdTable: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
            ssTable: [{}, {}, {}, {}, {}, {}, {}, {}]*/
        },
        created: function () {
            this.getCsqx()
        },
        mounted: function () {
            console.log(this.zdxxContent.xyzdxx.length)
        },
        filters: {
            formatTime: function (value) {
                if (value != '-') {
                    var d = new Date(value);
                    return d.getFullYear() + '年' + d.getMonth() + 1 + '月' + d.getDate() + '日'
                }
            }
        }
        ,
        methods: {
            getyhmc: function (values) {
                return printPage.listGetName(printPage.yhryList, values, 'rybm', 'ryxm');
            },
            getmzfs: function (values) {
                return printPage.listGetName(printPage.zybmList, values, 'zybm', 'zymc');
            },
            getCsqx: function () {
                var that = this;
                $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"N040072001"}', function (json) {
                    if (json.a == 0 && json.d != null) {
                        if (json.d.length > 0) {
                            //获取参数权限
                            var parm = {
                                "ylbm": 'N040072001',
                                "ksbm": json.d[0].ksbm
                            };
                            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                                if (json.a == 0) {
                                    if (json.d.length > 0) {
                                        for (var i = 0; i < json.d.length; i++) {
                                            var csjson = json.d[i];
                                            switch (csjson.csqxbm) {
                                                case "N04007200101": //首页医生是否显示
                                                    if (csjson.csz) {
                                                        that.csqx4007200101 = csjson.csz
                                                    }
                                                    break;
                                                case "N04007200110": //首页医生是否显示
                                                    if (csjson.csz) {
                                                        that.csqx4007200110 = csjson.csz
                                                    }
                                                    break;
                                                case "N04007200111": //病案最后一行是否显示
                                                    if (csjson.csz) {
                                                        that.csqx4007200111 = csjson.csz
                                                    }
                                                    break;
                                            }
                                        }
                                    }
                                }

                            });

                        }
                    }
                });

            }
            ,
            dysj: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryYljg&types=dysj&zyh=" + jbxx.popContent.zyh, function (json) {
                    if (json.a == 0) {
                    }
                });
            }
            ,
            fanhui: function () {
                this.isShow = undefined;
                $('#jyxm_icon').attr('style', '')
            },
            //初始化页面记载
            print: function (num) {

                this.dysj()
//			if(!printPage.printType){
//				malert("请完善数据后进行打印操作！",'top','defeadted');
//				return
//			}
                window.print();


            }
        }
    })
;
