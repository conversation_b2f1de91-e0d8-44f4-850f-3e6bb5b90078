(function () {


    var tableInfo = new Vue({
        el: '#kswh',
        //混合js字典庫
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            jsonList: [],
            ksVal: null,
            param: {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc'
            },
            bxlbbm:null,
            bxurl:null,
            sfxg:false//是否修改
        },
        methods: {

            getbxlb: function () {
                var param = {bxjk: "001"};
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            if (json.d.list.length > 0){
                                tableInfo.bxlbbm = json.d.list[0].bxlbbm;
                                tableInfo.bxurl = json.d.list[0].url;
                            }
                        } else {
                            malert("保险类别查询失败!" + json.c)
                        }
                    });
            },

            getData: function () {
            	$.getJSON(
                        "/actionDispatcher.do?reqUrl=New1BxInterface&url="+tableInfo.bxurl+"&bxlbbm="+tableInfo.bxlbbm+"&types=czy&method=query&parm="+JSON.stringify(this.param), function (json) {
                        	if (json.a == 0){
                        		var res=eval('('+json.d+')');
                        	     tableInfo.totlePage = Math.ceil(res.total / tableInfo.param.rows);
                                 tableInfo.jsonList = res.list;
                        	}else{
                        		malert(json.c);
                        	}
                        })
            },
            addData: function () {
                popWin.popContent = {};
                popWin.isShow = true;
            },
            edit: function (num) {
            	tableInfo.sfxg=true;
                if (num == null) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if (num == null) {
                        malert("请选中你要修改的数据");
                        return false;
                    }
                }
                //这里要拷贝值到popContent中，不能直接'='
                popWin.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                popWin.isShow = true;
            },
            remove: function () {
                var czyList = [];
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        var czybm = {};
                        czybm.czybm = this.jsonList[i].czybm;
                        czyList.push(czybm);
                    }
                }
                if (czyList.length == 0) {
                    malert("请选中您要删除的数据");
                    return false;
                }
                if (!mconfirm("请确认是否删除")) return false;

                var json = '{"list":' + JSON.stringify(czyList) + '}';
                $.getJSON(
                        "/actionDispatcher.do?reqUrl=New1BxInterface&url="+tableInfo.bxurl+"&bxlbbm="+tableInfo.bxlbbm+"&types=czy&method=delete&parm="+JSON.stringify(this.json), function (json) {
                        	if (json.a == 0){
                        		 malert("删除成功")
                        	}else{
                        		malert("删除失败")
                        	}
                        })
            }
        }
    });

    var popWin = new Vue({
        el: '#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            jkList: [],//保险接口
            rybmList: [],//科主任下拉框
            isShow: false,
            qy: null,
            ifClick: true,
            popContent: {
                'tybz': '1'
            },
            bxContent:{},
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            title: '科室维护',
            dg: {page: 1, rows: 5, sort: "ksbm", order: "asc", parm: ""},
            sfcz:false,//账号是否存在,

        },
        created: function () {
            this.qy = this.stopSign[0];
        },
        methods: {

            saveData: function () {
            	$.ajaxSettings.async = false;
            	popWin.sfcz=false;
                if (!popWin.ifClick) return; //如果为false表示已经点击了不能再点
                popWin.ifClick = false;
                var error = false;
                if (this.popContent['czybm'] == null || this.popContent['czybm'] == '') {
                    malert("院内操作员不能为空！");
                    error = true;
                }
                if (this.popContent['nhczy'] == null || this.popContent['nhczy'] == '') {
                    malert("农合账号不能为空！");
                    error = true;
                }
                if (this.popContent['password'] == null || this.popContent['password'] == '') {
                    malert("农合不能为空！");
                    error = true;
                }

                if (this.bxContent['bxbm'] == null || this.bxContent['bxbm'] == '') {
                    malert("保险接口不能为空！");
                    error = true;
                }
                if (error) {
                    popWin.ifClick = true;
                    return false;
                }
                popWin.popContent.bxlbbm = tableInfo.bxlbbm;
                popWin.popContent['password'] = popWin.popContent['password'].replace(/\%/g,"%25");// 将+号替换为十六进制
                popWin.popContent['password'] = popWin.popContent['password'].replace(/\&/g,"%26");// 将+号替换为十六进制
                popWin.popContent['password'] = popWin.popContent['password'].replace(/\+/g,"%2B");// 将+号替换为十六进制
                popWin.popContent['password'] = popWin.popContent['password'].replace(/\#/g,"%23");// 将+号替换为十六进制
                popWin.popContent['password'] = popWin.popContent['password'].replace(/\=/g,"&3D");// 将+号替换为十六进制
                popWin.popContent['password'] = popWin.popContent['password'].replace(/\?/g,"%3F");// 将+号替换为十六进制
                for(var i=0;i<tableInfo.jsonList.length;i++){
                	if(tableInfo.jsonList[i].czybm==this.popContent.czybm){
                		popWin.sfcz=true;
                		malert("该操作员已存在农合账号！");
                		 popWin.isShow = false;
                         popWin.isAdd = false;
                         popWin.ifClick = true;
                	}
                }
                if(popWin.sfcz==false&&tableInfo.sfxg==false){
                	$.getJSON(
                            "/actionDispatcher.do?reqUrl=New1BxInterface&url="+tableInfo.bxurl+"&bxlbbm="+tableInfo.bxlbbm+"&types=czy&method=save&parm="+JSON.stringify(this.popContent), function (json) {
                            	if (json.a == 0){
                            		 tableInfo.getData();
                                     popWin.isShow = false;
                                     popWin.isAdd = false;
                                     popWin.ifClick = true;
                            		malert("数据保存成功");
                            	}else{
                            		popWin.ifClick = true;
                            		malert(json.c);
                            	}
                            });
                }else if(tableInfo.sfxg==true){
                	$.getJSON(
                            "/actionDispatcher.do?reqUrl=New1BxInterface&url="+tableInfo.bxurl+"&bxlbbm="+tableInfo.bxlbbm+"&types=czy&method=update&parm="+JSON.stringify(this.popContent), function (json) {
                            	if (json.a == 0){
                            		 tableInfo.getData();
                                     popWin.isShow = false;
                                     popWin.isAdd = false;
                                     popWin.ifClick = true;
                                     tableInfo.sfxg=false;
                            		malert("数据保存成功");
                            	}else{
                            		popWin.ifClick = true;
                            		malert(json.c);
                            	}
                            });
                }
                else{
                	malert("该用户已存在农合账号！");
                	 popWin.isShow = false;
                     popWin.isAdd = false;
                     popWin.ifClick = true;
                     return
                }
            },

            GetKzrData: function () {
                this.param.rows = 20000;
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                        popWin.rybmList = json.d.list;
                    } else {
                        malert("人员列表查询失败" + json.c);
                        return;
                    }
                });
            },
            getJkInfo:function(){
        		var types="zyzbm";
        		var req={"zylb": "09"};
        		 $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types + "&json=" + JSON.stringify(req), function (json) {
                     if (json.a == 0)
                    	 popWin.jkList = json.d.list;
                     else
                         malert(types + "查询失败");
                 });
        	},
        }
    });

    //初始化页面需要加载的数据
    tableInfo.getbxlb();
    popWin.GetKzrData();
    popWin.getJkInfo();
    setTimeout(function () {
    	tableInfo.getData();  //锁表头初始化
         }, 400);
    $('body').click(function () {
        $(".selectGroup").hide();
    });

    $(".selectGroup").click(function (e) {
        e.stopPropagation();
    });

    //为table循环添加拖拉的div
    var drawWidthNum = $(".patientTable tr").eq(0).find("th").length;
    for (var i = 0; i < drawWidthNum; i++) {
        if (i >= 2) {
            $(".patientTable th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
        }
    }

    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function () {
        if ($(this).val() == '' || $(this).val() == null) {
            $(this).addClass("emptyError");
        } else {
            $(this).removeClass("emptyError");
        }
    });

    console.log(userId);
})();
