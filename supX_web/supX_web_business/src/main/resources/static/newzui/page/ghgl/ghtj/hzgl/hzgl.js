/**
 * Created by mash on 2017/12/21.
 */
(function () {
    var toolBar = new Vue({
        el: '#toolbar',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data: {
            caqxContent: {},  //参数对象
            ifClick: true,
            dgparm: {rows: 20000},
            popContent: {},
            selSearch: -1,
            objpopContent:{},
            brxxContent: {},
            searchCon: [],
            page: {
                page: 1,
                rows: 10,
                total: null
            },
            them_tran: {'brxb': dic_transform.data.brxb_tran},
            them: {'序号':'brxh','患者姓名': 'brxm', '性别': 'brxb', '年龄': 'brnl', '家庭地址': 'jzdmc', '身份证号': 'sfzjhm'}
        },
        methods: {
            //获取参数权限
            getCsqx: function () {
                //先获取到科室编码
                var ksparm = {"ylbm": 'N050012001'};
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                    if (json.a == 0) {
                        if (json.d.length > 0) {
                            //获取参数权限
                            var parm = {"ylbm": 'N050012001', "ksbm": json.d[0].ksbm};
                            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                                if (json.a == 0) {
                                    if (json.d.length > 0) {
                                        for (var i = 0; i < json.d.length; i++) {
                                            var csjson = json.d[i];
                                            switch (csjson.csqxbm) {
                                                case "N05001200101" :    //一卡通扣费金额  （设置扣费金额）
                                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                        toolBar.caqxContent.cs00400100101 = csjson.csz;
                                                    }
                                                    break;
                                                case "N05001200102" :    //一卡通扣费项目编码；(设置扣费明细项目编码)
                                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                        toolBar.caqxContent.cs00400100102 = csjson.csz;
                                                    }
                                                    break;
                                                case "N05001200103" :   //是否允许修改姓名1-允许 2-不允许
                                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                        toolBar.caqxContent.cs00400100103 = csjson.csz;
                                                    }
                                                    break;
                                                case "N05001200104" :   //是否允许修改身份证号1-允许 2-不允许
                                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                        toolBar.caqxContent.cs00400100104 = csjson.csz;
                                                    }
                                                    break;
                                                case "N05001200105" :  //是否允许修改病人性别1-允许 2-不允许
                                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                        toolBar.caqxContent.cs00400100105 = csjson.csz;
                                                    }
                                                    break;
                                                case "N05001200106" :  //是否允许修改病人出生日期1-允许 2-不允许
                                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                        toolBar.caqxContent.cs00400100106 = csjson.csz;
                                                    }
                                                    break;
                                                case "N05001200107" :  //是否允许修改病人年龄1-允许 2-不允许
                                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                        toolBar.caqxContent.cs00400100107 = csjson.csz;
                                                    }
                                                    break;
                                                case "N05001200108" :  //是否允许修改姓名、性别、年龄1-允许 2-不允许
                                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                        toolBar.caqxContent.cs00400100108 = csjson.csz;
                                                    }
                                                    break;
                                            }
                                        }
                                    }
                                    //是否允许使用医疗卡
                                    if (toolBar.caqxContent.cs00400100108 == "1") {
                                        personInfo.ylkxx = false;//医疗卡信息输入恢复
                                    } else {
                                        personInfo.ylkxx = true;//医疗卡信息输入恢复
                                    }
                                } else {
                                    malert('参数权限获取失败'+json.c,'top','defeadted');
                                }
                            });
                        }
                    } else {
                        malert('权限科室获取失败'+json.c,'top','defeadted');
                    }
                });
            },
            //下拉table
            searching: function (add, type, val) {    // 搜索调用API的方法，add为true就表示请求下一页、为null就为请求第一页
                this.brxxContent[type] = val;
                if (!add) this.page.page = 1;       // 设置当前页号为第一页
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.brxxContent[type] == undefined || this.brxxContent[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.brxxContent[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=queryCard&parm=" +
                    JSON.stringify(str_param), function (json) {
                    if (json.a == 0) {
                        var date = null;
                        if (add) {
                            // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < json.d.list.length; i++) {
                                date = new Date(json.d.list[i]['csrq']);
                                json.d.list[i]['brnl'] = toolBar.datetoage(toolBar.fDate(date));
                                // toolBar.searchCon.push(json.d.list[i]);
                                tableInfo.jsonList.push(json.d.list[i]);
                            }
                        } else {
                            for (var i = 0; i < json.d.list.length; i++) {
                                date = new Date(json.d.list[i]['csrq']);
                                json.d.list[i]['brnl'] = toolBar.datetoage(toolBar.fDate(date));
                                // toolBar.searchCon.push(json.d.list[i]);
                                tableInfo.jsonList.push(json.d.list[i]);
                            }
                            // toolBar.searchCon = json.d.list;
                            tableInfo.jsonList = json.d.list;
                        }

                        toolBar.page.total = json.d.total;
                        toolBar.selSearch = 0;
                        if (json.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,'top','defeadted');
                    }
                })
            },

            selectOne: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching(true, 'text', this.brxxContent['text']);           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.brxxContent = item;
                    Vue.set(this.brxxContent, 'text', this.brxxContent['brid']);
                    this.zcxxsearch();//调用查询
                    $(".selectGroup").hide();
                }
            },
            changeDown: function (event, type) {
                if (this['searchCon'][this.selSearch] == undefined) return;
                this.keyCodeFunction(event, 'brxxContent', 'searchCon');
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (type == "text") {
                        Vue.set(this.brxxContent, 'text', this.brxxContent['brid']);
                        this.zcxxsearch();//调用查询
                        this.nextFocus(event);
                    }
                }
            },
            // 展示注册记录
            showLog: function () {
                tableInfo.isFold = true;
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
            },
            // 测试患者信息查询
            showtest: function () {
                var str_param = {
                    page: tableInfo.param.page,
                    rows: tableInfo.param.rows,
                    sort: 'brid',
                    order: 'desc',
                    brxm: "测试"
                };
                // 如果有分页请用jq的api,一般情况下避免冲突请用vue-resource,具体参照index.js的事例
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=hzxxgl&parm=" + JSON.stringify(str_param), function (json) {
                    // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (json.a == 0) {
                        console.log(Math.ceil(json.d.total / tableInfo.param.rows));
                        console.log(JSON.stringify(json.d));
                    } else {
                        malert("查询失败：" + json.c,'top','defeadted')
                    }
                });
            },
            // 有效期内挂号信息查询
            getghlist: function () {
                var str_param = {
                    page: 1,
                    rows: 10,
                    sort: 'ghxh',
                    order: 'desc'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=queryJzlb&parm=" + JSON.stringify(str_param), function (json) {
                    // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (json.a == 0) {
                        // brghList.totlePage = Math.ceil(json.d.total/brghList.param.rows);
                        console.log(JSON.stringify(json.d));
                    } else {
                        malert("获取挂号列表失败：" + json.c,'top','defeadted')
                    }
                });
            },
            // 过期挂号类别
            getgqghlb: function () {
                var str_param = {
                    page: 1,
                    rows: 10,
                    sort: 'ghxh',
                    order: 'desc'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=queryGqghlb&parm=" + JSON.stringify(str_param), function (json) {
                    // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (json.a == 0) {
                        // brghList.totlePage = Math.ceil(json.d.total/brghList.param.rows);
                        console.log(JSON.stringify(json.d));
                    } else {
                        malert("获取挂号列表失败：" + json.c,'top','defeadted')
                    }
                });
            },
            // 过期挂号类别
            getallghlb: function () {
                var str_param = {
                    page: 1,
                    rows: 10,
                    sort: 'ghxh',
                    order: 'desc'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=query&parm=" + JSON.stringify(str_param), function (json) {
                    if (json.a == 0) {
                        console.log(JSON.stringify(json.d));
                    } else {
                        malert("获取挂号列表失败：" + json.c,'top','defeadted')
                    }
                });
            },
            // 行政区划转邮编码
            xzqhtoyzbm: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhBzzdXzqhyzbm&types=xzqhyzbm&xzqhbm=510700", function (json) {
                    if (json.a == 0) {
                        console.log(json.d);
                    } else {
                        malert("查询失败：" + json.c,'top','defeadted')
                    }
                });
            },
            // 注册信息检索
            zcxxsearch: function () {
                personInfo.popContent = {};
                var kh = toolBar.brxxContent.brid;
                if (kh == null || kh == undefined || kh == "") {
                    malert("检索内容不能为空",'top','defeadted');
                    return;
                }
                var str_param = {
                    parm: kh
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=queryCard&parm=" + JSON.stringify(str_param), function (json) {
                    if (json.a == 0) {
                        personInfo.popContent = json.d.list[0];
                        personInfo.objpopContent = json.d.list[0];
                        personInfo.popContent.csrq = personInfo.fDate(personInfo.popContent.csrq, "date");
                        if (personInfo.popContent.brid != null) {
                            //禁用医疗卡信息部分
                            personInfo.ylkxx = true;
                        }
                    } else {
                        malert("查询失败：" + json.c,'top','defeadted')
                    }
                });
            },
            // 刷新病人注册列表
            // getData: function () {
            //     $(".selectInput ul").hide();
            //     var str_param = {
            //         page: tableInfo.param.page,
            //         rows: tableInfo.param.rows,
            //         sort: 'brid',
            //         order: 'desc'
            //     };
            //     $.getJSON("/actionDispatcher.do?reqUrl=GhglGhywBrzc&types=query&parm=" + JSON.stringify(str_param), function (json) {
            //         if (json.a == 0) {
            //             tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
            //             tableInfo.jsonList = json.d.list;
            //         } else {
            //             malert("查询失败：" + json.c)
            //         }
            //     });
            // },
            // 新增加
            addData: function () {
                personInfo.popContent = {};
                Vue.set(personInfo.popContent, 'ylklx', '07');
                Vue.set(personInfo.popContent, 'brmz', '01');
                Vue.set(personInfo.popContent, 'brgj', 156);
                Vue.set(personInfo.popContent, 'sfzjlx', '01');
                Vue.set(personInfo.popContent, 'hzlx', '0');
                //是否允许使用医疗卡
                if (toolBar.caqxContent.cs00400100108 == "1") {
                    personInfo.ylkxx = false;//医疗卡信息输入恢复
                } else {
                    personInfo.ylkxx = true;//医疗卡信息输入恢复
                }
            },
            // 保存
            saveData: function () {
                if (!toolBar.ifClick) return; //如果为false表示已经点击了不能再点
                personInfo.popContent.csrq = $("#csrq").val();
                // 提交前验证数据(非空)
                if (!personInfo.empty_sub('contextInfo')) {
                    toolBar.ifClick = true;
                    return false;
                }
                //判断身份证号码
                if (personInfo.popContent.sfzjlx == '01') {
                    if (personInfo.popContent.sfzjhm != null && personInfo.popContent.sfzjhm != "") {
                        if (personInfo.popContent.sfzjhm.length != 18) {
                            malert("身份证号码非法！",'top','defeadted');
                            toolBar.ifClick = true;
                            return;
                        }
                    }
                }
                toolBar.ifClick = false;
                personInfo.popContent.brgjmc = toolBar.listGetName(personInfo.gjList, personInfo.popContent['brgj'], 'gjbm', 'gjmc');
                personInfo.popContent.brmzmc = toolBar.listGetName(personInfo.mzList, personInfo.popContent['brmz'], 'mzbm', 'mzmc');
                var json = JSON.stringify(personInfo.popContent);
                this.$http.post('/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=save&',
                    json).then(function (data) {
                    if (data.body.a == 0) {
                        tableInfo.getData(); // 刷新
                        personInfo.popContent = {};
                        toolBar.ifClick = true;
                        Vue.set(personInfo.popContent, 'ylklx', '07');
                        Vue.set(personInfo.popContent, 'brmz', '01');
                        Vue.set(personInfo.popContent, 'brgj', 156);
                        Vue.set(personInfo.popContent, 'hzlx', '0');
                        malert("保存成功!",'top','success')
                    } else {
                        malert("保存失败" + data.body.c,'top','defeadted');
                        toolBar.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
            },

            // 页面加载时自动获取卡类型
            GetYlklxData: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyzbm&json={'zylb':'01'}", function (json) {
                    if (json.a == 0) {
                        personInfo.cardList = json.d.list;
                        //默认医疗卡类型
                        Vue.set(personInfo.popContent,'ylklx', '07');
                    } else {
                        malert("医疗卡类型下拉列表查询失败：" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            // 页面加载时自动获取民族Dddw数据
            GetMzData: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=mzbm", function (json) {
                    if (json.a == 0) {
                        personInfo.mzList = json.d.list;
                        Vue.set(personInfo.popContent,'brmz', '01');
                    } else {
                        malert("民族下拉列表查询失败：" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            // 页面加载时自动获取国籍Dddw数据
            GetGjData: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=gjbm", function (json) {
                    if (json.a == 0) {
                        personInfo.gjList = json.d.list;
                        Vue.set(personInfo.popContent,'brgj', 156);
                    } else {
                        malert("国籍下拉列表查询失败：" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            // 页面加载时自动获取婚姻状况Dddw数据
            GetHyzkData: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=hyzk", function (json) {
                    if (json.a == 0) {
                        personInfo.hyzkList = json.d.list;
                    } else {
                        malert("婚姻状况下拉列表查询失败：" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            // 页面加载时自动获取职业编码Dddw数据
            GetZybmData: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zybm", function (json) {
                    if (json.a == 0) {
                        personInfo.zyList = json.d.list;
                    } else {
                        malert("职业编码下拉列表查询失败：" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            // 页面加载时自动获取联系人关系Dddw数据
            GetlxrgxData: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=lxrgx", function (json) {
                    if (json.a == 0) {
                        personInfo.lxrgxList = json.d.list;
                    } else {
                        malert("联系人下拉列表查询失败：" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            // 页面加载时自动获取省编码Dddw数据
            GetshengData: function () {
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"1"}', function (json) {
                    if (json.a == 0) {
                        personInfo.shengListall = json.d.list;
                        personInfo.jzdshengList = json.d.list;
                        personInfo.hjdshengList = json.d.list;
                    } else {
                        malert("省编码下拉列表查询失败：" + json.c,'top','defeadted');
                        return false;
                    }
                });
            }
        }
    });

    var personInfo = new Vue({
        el: '.brzcedit',
        mixins: [dic_transform, tableBase, baseFunc, mformat,checkData],
        data: {
            popContent: {"ylklx": "07", "brgj": "156", "brmz": "01", "hzlx": "0", "sfzjlx": "01"},
            isShow: false,
            ylkxx: false, //进入查看信息时医疗卡信息都不可以修改
            cardList: [], // 医疗卡类型
            ksbmList: [],
            yljgList: [],
            xlList: [],
            sxzyList: [],
            isKeyDown: null,
            title: '病人注册',
            gjList: [],// 国籍
            hyzkList: [],// 婚姻状况
            zyList: [],// 职业
            mzList: [],// 民族
            lxrgxList: [], // 联系人关系
            shengListall: [],// 省全部编码
            // shiListall: [],// 市全部编码
            // xianListall: [],// 县全部编码
            jzdshengList: [],// 省编码 居住地
            jzdshiList: [],// 市编码
            jzdxianList: [],// 县编码
            hjdshengList: [],// 省编码 户籍地
            hjdshiList: [],// 市编码
            hjdxianList: [],// 县编码
            dgparm: {rows: 20000},
            indexs:0,
            editShow:true,
            submieShow:false,

        },
        created: function () {
            this.readyData({'zylb': '01'}, "zyzbm", "cardList");     // 医疗卡类型
            this.readyData(false, "gjbm", "gjList");                // 病人国籍
            this.readyData(false, "mzbm", "mzList");                // 民族
            this.readyData(false, "hyzk", "hyzkList");                // 婚姻状况
            this.readyData(false, "zybm", "zyList");                // 职业
            this.readyData(false, "lxrgx", "lxrgxList");                // 联系人
            this.readyData({"xzqhlx": "1"}, "xzqh", "jzdshengList");     // 省份
        },
        methods: {
            // 删除
            deleteData: function () {
                if (personInfo.popContent.brid == null || personInfo.popContent.brid == '') {
                    malert('请选择需要删除的病人!','top','defeadted');
                    return;
                }
                var bridList = [], brid = {};
                brid.brid = personInfo.popContent.brid;
                bridList.push(brid);
                if (bridList.length == 0) {
                    malert("请选中您要删除的病人",'top','defeadted');
                    return false;
                }
                var json = '{"list":' + JSON.stringify(bridList) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=delete&',
                    json).then(function (data) {
                    tableInfo.getData();
                    if (data.body.a == 0) {
                        malert("删除成功",'top','success')
                    } else {
                        malert("删除失败" + data.body.c,'top','defeadted')
                    }
                }, function (error) {
                    console.log(error);
                });
            },
            edit:function(){
                this.editShow=false;
                this.submieShow=true
            },
            submit:function(){
                toolBar.saveData();//保存
            },
            userqh:function(one){
                this.indexs=one
            },
            // this.readyData("请求参数", "请求的类型", "接受的对象");
            readyData: function (req, types, listName) {
                if (!req) {
                    $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types, function (json) {
                        if (json.a == 0) {
                            personInfo[listName] = json.d.list;
                        } else {
                            malert(types + "查询失败" + json.c,'top','defeadted');
                        }
                    });
                } else {
                    $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types + "&json=" + JSON.stringify(req), function (json) {
                        if (json.a == 0) {
                            personInfo[listName] = json.d.list;
                            if(types == 'xzqh'){
                                personInfo["shengListall"] = json.d.list;
                                personInfo["hjdshengList"] = json.d.list;
                            }
                        } else {
                            malert(types + "查询失败" + json.c,'top','defeadted');
                        }
                    });
                }
            },
            setAge: function (event) {
                //判断是否已存在该病人信息如果已经存在则直接赋值
                if (this.popContent.sfzjhm != null && this.popContent.sfzjhm != "") {
                    var str_param = {
                        sfzjlx: this.popContent.sfzjlx,
                        sfzjhm: this.popContent.sfzjhm
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=queryCard&parm=" + JSON.stringify(str_param), function (json) {
                        // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                        if (json.a == 0) {
                            if (json.d.list != null && json.d.list != "") {
                                personInfo.popContent = json.d.list[0];
                                personInfo.popContent.csrq = personInfo.fDate(personInfo.popContent.csrq, "date");
                                if (personInfo.popContent.brid != null) {
                                    //禁用医疗卡信息部分
                                    personInfo.ylkxx = true;
                                }
                            }
                        } else {
                            malert("查询失败：" + json.c,'top','defeadted')
                        }
                    });
                }
                //根据身份证号码查询出居住地省市县
                if (this.popContent.sfzjlx == '01') {
                    if (this.popContent.sfzjhm != null && this.popContent.sfzjhm != "") {
                        var shengbm = this.popContent.sfzjhm.substring(0, 2);
                        var shibm = this.popContent.sfzjhm.substring(0, 4);
                        var xianbm = this.popContent.sfzjhm.substring(0, 6);
                        personInfo.popContent.jzdsheng = null;
                        personInfo.popContent.jzdshengmc = null;
                        personInfo.popContent.hjdsheng = null;
                        personInfo.popContent.hjdshengmc = null;
                        var mc = personInfo.listGetName(personInfo.hjdshengList, shengbm, 'xzqhbm', 'xzqhmc');
                        Vue.set(personInfo.popContent, "jzdsheng", shengbm);
                        personInfo.popContent.jzdshengmc = mc;
                        Vue.set(personInfo.popContent, "hjdsheng", shengbm);
                        personInfo.popContent.hjdshengmc = mc;
                        this.popContent.jzdmc = personInfo.popContent.jzdshengmc;
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"2","sjbm":"' + shengbm + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                            if (json.a == 0) {
                                personInfo.hjdshiList = [];
                                personInfo.jzdshiList = [];
                                personInfo.hjdshiList = json.d.list;
                                personInfo.jzdshiList = json.d.list;
                                var mc = personInfo.listGetName(personInfo.hjdshiList, shibm, 'xzqhbm', 'xzqhmc');
                                personInfo.popContent.jzdshi = null;
                                personInfo.popContent.jzdshimc = null;
                                personInfo.popContent.hjdshi = null;
                                personInfo.popContent.hjdshimc = null;
                                if (mc) {
                                    Vue.set(personInfo.popContent, "jzdshi", shibm);
                                    personInfo.popContent.jzdshimc = mc;
                                    Vue.set(personInfo.popContent, "hjdshi", shibm);
                                    personInfo.popContent.hjdshimc = mc;
                                    personInfo.popContent.jzdmc = personInfo.popContent.jzdshengmc + personInfo.popContent.jzdshimc;
                                }
                            } else {
                                malert("市编码下拉列表查询失败：" + json.c,'top','defeadted');
                                return false;
                            }
                        });
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"3","sjbm":"' + shibm + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                            if (json.a == 0) {
                                personInfo.hjdxianList = [];
                                personInfo.jzdxianList = [];
                                personInfo.hjdxianList = json.d.list;
                                personInfo.jzdxianList = json.d.list;
                                var mc = personInfo.listGetName(personInfo.hjdxianList, xianbm, 'xzqhbm', 'xzqhmc');
                                personInfo.popContent.jzdxian = null;
                                personInfo.popContent.jzdxianmc = null;
                                personInfo.popContent.hjdxian = null;
                                personInfo.popContent.hjdxianmc = null;
                                personInfo.popContent.hjdxzqh = null;
                                if (mc) {
                                    Vue.set(personInfo.popContent, "jzdxian", xianbm);
                                    personInfo.popContent.jzdxianmc = mc;
                                    Vue.set(personInfo.popContent, "hjdxian", xianbm);
                                    personInfo.popContent.hjdxianmc = mc;
                                    personInfo.popContent.jzdmc = personInfo.popContent.jzdshengmc + personInfo.popContent.jzdshimc + personInfo.popContent.jzdxianmc;
                                    personInfo.popContent.hjdxzqh = personInfo.popContent.hjdxian;
                                }
                            } else {
                                malert("县编码下拉列表查询失败：" + json.c,'top','defeadted');
                                return false;
                            }
                        });
                    }
                }
                //计算年龄
                if (this.popContent.sfzjlx == '01') {
                    Vue.set(this.popContent, 'csrq', this.sfzhtodate(this.popContent.sfzjhm));
                    this.popContent = Object.assign({}, this.popContent);
                    if (event && this.popContent.sfzjhm != null) this.nextFocus(event, 2);
                    else this.nextFocus(event);
                } else {
                    if (event) this.nextFocus(event);
                }
            },
            // 行政区划选择后回调方法
            resultzcChange: function (val) {
                var types = val[2][val[2].length - 1];
                console.log("types:" + types + "|val[1]:" + val[1]);
                switch (types) {
                    case "jzdsheng":// 居住地省
                        //if(this.popContent.jzdsheng==null){
                        Vue.set(this.popContent, 'jzdsheng', val[0]);
                        Vue.set(this.popContent, 'jzdshengmc', val[4]);
                        //}
                        personInfo.popContent.jzdmc = personInfo.popContent['jzdshengmc'];
                        personInfo.jzdxianList = [];
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"2","sjbm":"' + val[0] + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                            if (json.a == 0) {
                                personInfo.jzdshiList = json.d.list;
                            } else {
                                malert("市编码下拉列表查询失败：" + json.c,'top','defeadted');

                            }
                        });
                        break;
                    case "jzdshi":// 居住地市
                        //if(this.popContent.jzdshi==null){
                        Vue.set(this.popContent, 'jzdshi', val[0]);
                        Vue.set(this.popContent, 'jzdshimc', val[4]);
                        //}
                        personInfo.popContent.jzdmc = personInfo.popContent['jzdshengmc'] + personInfo.popContent['jzdshimc'];
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"3","sjbm":"' + val[0] + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                            if (json.a == 0) {
                                personInfo.jzdxianList = json.d.list;
                            } else {
                                malert("市编码下拉列表查询失败：" + json.c,'top','defeadted');

                            }
                        });
                        break;
                    case "jzdxian":// 居住地县
                        //if(this.popContent.jzdxian==null){
                        Vue.set(this.popContent, 'jzdxian', val[0]);
                        Vue.set(this.popContent, 'jzdxianmc', val[4]);
                        // }
                        personInfo.popContent.jzdmc = personInfo.popContent.jzdshengmc + personInfo.popContent.jzdshimc + personInfo.popContent.jzdxianmc;
                        break;
                    case "hjdsheng":// 户籍地省
                        //if(this.popContent.hjdsheng==null){
                        Vue.set(this.popContent, 'hjdsheng', val[0]);
                        Vue.set(this.popContent, 'hjdshengmc', val[4]);
                        //}
                        personInfo.hjdxianList = [];
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"2","sjbm":"' + val[0] + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                            if (json.a == 0) {
                                personInfo.hjdshiList = json.d.list;
                            } else {
                                malert("市编码下拉列表查询失败：" + json.c,'top','defeadted');

                            }
                        });
                        break;
                    case "hjdshi":// 户籍地市
                        //if(this.popContent.hjdshi){
                        Vue.set(this.popContent, 'hjdshi', val[0]);
                        Vue.set(this.popContent, 'hjdshimc', val[4]);
                        //}
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"3","sjbm":"' + val[0] + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                            if (json.a == 0) {
                                personInfo.hjdxianList = json.d.list;
                            } else {
                                malert("市编码下拉列表查询失败：" + json.c,'top','defeadted');

                            }
                        });
                        break;
                    case "hjdxian":// 户籍地县
                        //if(this.popContent.hjdxian==null){
                        Vue.set(this.popContent, 'hjdxian', val[0]);
                        Vue.set(this.popContent, 'hjdxianmc', val[4]);
                        Vue.set(this.popContent, 'hjdxzqh', val[0]);
                        //}
                        break;
                    default:
                        break;
                }
                //方便更新试图（添加set。get方法）
                this.popContent = Object.assign({}, this.popContent);
                if (val[1] != null) {
                    this.nextFocus(event);
                }
            },
            //在最后一个输入框回车之后调用保存
            saveDateHc: function(event){
                if(event.code == 'Enter' || event.code == 13 ||event.code== 'NumpadEnter'){
                    toolBar.saveData();//保存
                }
            }
        }
    });

    // 病人列表
    var tableInfo = new Vue({
        el: '#utable1',
        // 混合js字典庫
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],
            param: {
                page: 1,
                rows: 20,
                sort: 'brid',
                order: 'asc'
            },
            pages: {
                page: 1,
                rows: 10,
                sort: 'brid',
                order: 'asc'
            },
            totlePage: 0,
            isChecked: [],
            isCheckAll: false,
            testVal: {},
            isFold: false,
            objbrxx:['病人ID','病人姓名','性别','出生日期','身份证号','手机号码','出生地','国籍','民族','证件类型','婚姻状况','职业编码','患者类型','工作单位','单位地址','单位邮编','居住地行政区划','居住地省份','居住地市','居住地县','居住地乡','居住地居委会','居住地-村','居住地-弄','居住地-楼号','居住地-门牌号','居住地名称','户籍地行政区划','户籍地-省','户籍地-市','户籍地-县',
                '户籍地-乡','户籍地居委','户籍地-村','户籍地-弄','户籍地-楼号','户籍地门牌号','户口地址','户口地邮编','联系人姓名','联系人关系','联系人地址','联系人单位','联系人邮编','联系人电话','登记日期','登记人员','拼音代码','身份证件类型名称','婚姻状况名称','职业编码名称','联系电话类别代码','健康档案编号','病人国籍名称','病人民族名称','居住地行政区划名称','居住地省分名称','居住地市名称','居住地县名称','居住地乡名称',
                '居住地居委会名称','户籍地行政区划名称','户籍地省份名称','户籍地市名称','户籍地县名称','户籍地乡名称','户籍地居委会名称','身高','体重'],
        },
        methods: {
            searchingList: function (add, type, val) {    // 搜索调用API的方法，add为true就表示请求下一页、为null就为请求第一页
                if (!add) this.pages.page = 1;       // 设置当前页号为第一页
                if (val == undefined || val == null) {
                    this.pages.parm = "";
                } else {
                    this.pages.parm = type;
                }
                var str_param = {parm: this.pages.parm, page: this.pages.page, rows: this.pages.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=query&parm=" +
                    JSON.stringify(str_param), function (json) {
                    if (json.a == 0) {
                        var date = null;
                        if (add) {
                            // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            tableInfo.jsonList=tableInfo.jsonList.concat(json.d.list)
                        } else {
                            tableInfo.jsonList=json.d.list
                            // for (var i = 0; i < json.d.list.length; i++) {
                            //     date = new Date(json.d.list[i]['csrq']);
                            //     json.d.list[i]['brnl'] = toolBar.datetoage(toolBar.fDate(date));
                            //     toolBar.searchCon.push(json.d.list[i]);
                            // }
                            // toolBar.searchCon = json.d.list;
                        }

                        // toolBar.page.total = json.d.total;
                        // toolBar.selSearch = 0;
                        // if (json.d.list.length > 0 && !add) {
                        //     $(".selectGroup").hide();
                        //     _searchEvent.show();
                        // }
                    } else {
                        malert("查询失败  " + json.c,'top','defeadted');
                    }
                })
            },
            close: function () {

                $(".side-form-bg").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');
                // tableInfo.isFold = false;

            },
            // 获取病人注册列表
            getData: function (event) {
                var str_param = {
                    page: tableInfo.param.page,
                    rows: tableInfo.param.rows,
                    sort: 'brid',
                    order: 'desc'
                };
                // 如果有分页请用jq的api,一般情况下避免冲突请用vue-resource,具体参照index.js的事例
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=query&parm=" + JSON.stringify(str_param), function (json) {
                    // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (json.a == 0) {
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                        tableInfo.jsonList = json.d.list;
                    } else {
                        malert("查询失败：" + json.c,'top','defeadted')
                    }
                });
            },
            // 选中单条
            checkOne: function (event,index) {
                if(event.srcElement.checked==true){
                    this.isChecked[index] = false;
                }else{
                    this.isChecked[index] = true;
                }
            },

            // 选中全部
            checkAll: function (event) {
                if (event.srcElement.checked==true) {
                    for (var i = 0; i < this.jsonList.length; i++) {
                        Vue.set(this.isChecked,i,true)
                        // this.isChecked[i] = true;
                    }
                } else {
                    this.isChecked = [];
                }
            },
            checkSome: function (index) {
                if (!this.isChecked[index]) {
                    this.isCheckAll = false;
                    Vue.set(this.isChecked, index, false);
                } else {
                    Vue.set(this.isChecked, index, true);
                }
            },
            changePage: function (type) {
                if (type == 'next') {
                    if (this.param.page >= this.totlePage) {
                        return false;
                    }
                    this.param.page++;
                    this.getData();
                }
                if (type == 'prev') {
                    if (this.param.page <= 1) {
                        return false;
                    }
                    this.param.page--;
                    this.getData();
                } else {
                    this.getData();
                }
            },
            addData: function () {
                personInfo.popContent = {};
            },
            // 双击进行修改
            edit: function (num) {
                if (num == null) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if (num == null) {
                        malert("请选中你要修改的数据",'top','defeadted');
                        return false;
                    }
                }
                // 这里要拷贝值到popContent中，不能直接=
                // toolBar.GetSelectList(); // 下拉框加载数据
                // personInfo.popContent = [] ;
                personInfo.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                personInfo.popContent.csrq=tableInfo.fDate(personInfo.popContent.csrq,'date');
                personInfo.hjdshiList = personInfo.shiListall;
                personInfo.jzdshiList = personInfo.shiListall;
                personInfo.hjdxianList = personInfo.xianListall;
                personInfo.jzdxianList = personInfo.xianListall;
                personInfo.ylkxx=true;
                this.close();
            },
            remove: function () {
                var bridList = [];
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        var brid = {};
                        brid.brid = this.jsonList[i].brid;
                        bridList.push(brid);
                    }
                }
                if (bridList.length == 0) {
                    malert("请选中您要删除的数据",'top','defeadted');
                    return false;
                }
                var json = '{"list":' + JSON.stringify(bridList) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=delete&',
                    json).then(function (data) {
                    tableInfo.getData();
                    if (data.body.a == 0) {
                        malert("删除成功",'top','success')
                    } else {
                        malert("删除失败",'top','defeadted')
                    }
                }, function (error) {
                    console.log(error);
                });
            }
        }
    });
// 编辑列表框
    toolBar.getCsqx(); //获取参数权限
    tableInfo.getData();                // 自动执行查询数据方法
    toolBar.GetYlklxData();             // 页面加载时自动获取医疗卡类型Dddw数据
    toolBar.GetMzData();                // 页面加载时自动获取民族Dddw数据
    toolBar.GetGjData();                // 页面加载时自动获取国籍Dddw数据
    toolBar.GetHyzkData();              // 页面加载时自动获取婚姻状况Dddw数据
    toolBar.GetZybmData();              // 页面加载时自动获取职业编码Dddw数据
    toolBar.GetlxrgxData();             // 页面加载时自动获取联系人关系Dddw数据
    toolBar.GetshengData();             // 页面加载时自动获取省编码Dddw数据
    // toolBar.GetshiData();            // 页面加载时自动获取市编码Dddw数据
    // toolBar.GetxianData();           // 页面加载时自动获取县编码Dddw数据

    $("#popCon").click(function () {
        $(".popInfo ul").hide();
    });
    // 为table循环添加拖拉的div
    var drawWidthNum = $(".patientTable tr").eq(0).find("th").length;
    for (var i = 0; i < drawWidthNum; i++) {
        if (i >= 2) {
            $(".patientTable th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
        }
    }

    // 验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function () {
        if ($(this).parent()["0"].className == "selectInput") {
            return false
        }
        if ($(this).val() == '' || $(this).val() == null) {
            $(this).addClass("emptyError");
        } else {
            $(this).removeClass("emptyError");
        }
    });

    // 点击空白隐藏弹出层
    $(document).mouseup(function (e) {
        var bol = $(e.target).parents().is(".pop");
        if (!bol) {
            if (tableInfo.isFold) tableInfo.close();
        }
    });
    //针对下拉table
    $('body').click(function () {
        $(".selectGroup").hide();
    });

    $(".selectGroup").click(function (e) {
        e.stopPropagation();
    });

    /*********************快捷键********************************/
    $(document).keydown(function(e){
        //F2门诊收费保存
        if(e.keyCode == 113){
            toolBar.saveData();//保存
        }
    });

    $(".onsubmit").click(function () {
        toolBar.saveData();//保存
    });
    $(".onreset").click(function () {
        personInfo.popContent = {};
    });
    laydate.render({
        elem: '.zui-date .zui-dateList'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,done:function (value,data) {
            search.jsrq = value
        }
    });
    $(".zui-form input").uicomplete({
        iskeyup: false
    });
})();
