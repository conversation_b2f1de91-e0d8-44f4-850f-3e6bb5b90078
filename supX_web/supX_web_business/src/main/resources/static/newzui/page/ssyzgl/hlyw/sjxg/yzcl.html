<div id="yzcl" class="yzcl_context">
    <div id="toolMenu">
        <div class=" flex-container flex-wrap-w flex-align-c">
            <tabs :num="index" :tab-child="tabList" @tab-active="clickYzcl"></tabs>
            <span class="padd-r-5 padd-l-5">从</span>
            <input id="dbegin" class="zui-input wh180" data-select="no"  @keyup="setTime($event,'ksrqq')" @click="showDate($index,'dbegin','ksrqq')" v-model="ksrqq">
            <span class="padd-r-5 padd-l-5">至</span>
            <input id="dEnd" class="zui-input wh180" data-select="no" @keyup="setTime($event,'endrqq')" @click="showDate($index,'dEnd','endrqq')" v-model="endrqq">
            <span class="  flex-container flex-align-b margin-b-5 margin-t-5   margin-l-20 wh120">
			<label class="whiteSpace margin-r-5 ft-14">类型</label>
			<select-input class="wh120" @change-data="resultChange" :not_empty="false" :child="yzlx_tran01"
                          :index="yzlx" :val="yzlx" :name="'yzlx'">
			</select-input>
		</span>
        </div>
        <div class="YZInfo flex-container">
            <!-- 执行时间 -->
            <!--		   <vue-scroll :ops="pageScrollOps" ref="pageTabs">-->
            <div class="yzcl_div yzclyzcx" v-if="index == 0">
                <div class="zui-table-view hzList padd-r-10 padd-l-10" v-for="(parent,index) in jsonList">
                    <div class="jbxx-size margin-t-5">
                        <div class="jbxx-box padd-l-12">
                            科别：<span class="font-14-654 padd-r-18" v-text="parent.ryksmc"></span>
                            床号：<span class="font-14-654 padd-r-18" v-text="parent.rycwbh"></span>
                            姓名：<span class="font-14-654 padd-r-18" v-text="parent.brxm"></span>
                            性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[parent.brxb]"></span>
                            年龄：<span class="font-14-654 padd-r-18" v-text="parent.nl + nldw_tran[parent.nldw]"></span>
                            住院号：<span class="font-14-654 padd-r-18" v-text="parent.zyh"></span>
                        </div>
                    </div>
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m ">
                                    <div class="cell-m text-center">
                                        <input type="checkbox" v-model="isCheckAll"
                                               @click="checkYZAll(1,jsonList,parent.yzxx[0].no)">
                                    </div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-m">序号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s ">下嘱时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">执行时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医嘱内容</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">药品规格</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">剂量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">剂量单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用法</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">频次</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">护士签名</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"  @scroll="scrollTable($event)">
                        <table class="zui-table" v-if="parent.yzxx.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSome('1_'+item.no+'_'+$index)" :tabindex="$index"
                                v-for="(item, $index) in parent.yzxx">
                                <td class="cell-m">
                                    <div class="cell-m text-center">
                                        <input type="checkbox" @click.stop="checkSome('1_'+item.no+'_'+$index)"
                                               v-model="isChecked['1_'+item.no+'_'+$index]">
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(item.ksrq,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(item.zxsj,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.xmmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.ypgg"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.dcjl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.jldwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.sl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.yyffmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.pcmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.zxhsxm"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="parent.length==0" class=" noData text-center zan-border">暂无数据...</p>
                    </div>
                </div>
                <div class=" flex-container flex-align-c addList">
                    <button v-waves class="tong-btn btn-parmary-b" @click="queryyz">刷新</button>
                    <button v-waves class="tong-btn btn-parmary-b" @click="auditing">修改</button>
                    <span>修改为：</span>
                    <input id="xgsj1" class="zui-input wh180" @click="showDate($index,'xgsj1','xgsj1')" v-model="xgsj1">
                </div>
            </div>


            <!-- 医嘱审核时间 -->
            <div class="yzcl_div yzclyzcx" v-if="index == 1">
                <div class="zui-table-view hzList padd-r-10 padd-l-10" v-for="(parent,index) in jsonList">
                    <div class="jbxx-size margin-t-5">
                        <div class="jbxx-box padd-l-12">
                            科别：<span class="font-14-654 padd-r-18" v-text="parent.ryksmc"></span>
                            床号：<span class="font-14-654 padd-r-18" v-text="parent.rycwbh"></span>
                            姓名：<span class="font-14-654 padd-r-18" v-text="parent.brxm"></span>
                            性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[parent.brxb]"></span>
                            年龄：<span class="font-14-654 padd-r-18" v-text="parent.nl + nldw_tran[parent.nldw]"></span>
                            住院号：<span class="font-14-654 padd-r-18" v-text="parent.zyh"></span>
                        </div>
                    </div>
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>

                                <th class="cell-m">
                                    <div class="cell-m  text-center">
                                        <input type="checkbox" v-model="isCheckAll"
                                               @click="checkYZAll(2,jsonList,parent.yzxx[0].no)">
                                    </div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-m">序号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s ">类型</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">分类</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">嘱托</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">下嘱时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">审核时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医嘱内容</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">药品规格</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">剂量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">剂量单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用药方法</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">说明</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医生签名</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">护士签名</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)">
                        <table class="zui-table" v-if="parent.yzxx.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSome('2_'+yZItem.no+'_'+$index)" :tabindex="$index"
                                v-for="(yZItem, $index) in parent.yzxx">
                                <td class="cell-m">
                                    <div class="cell-m  text-center">
                                        <input type="checkbox" v-model="isChecked['2_'+yZItem.no+'_'+$index]"
                                               @click="checkSome('2_'+yZItem.no+'_'+$index)">
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yzlx_tran[yZItem.yzlx]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yzfl_tran[yZItem.yzfl]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="istrue_tran[yZItem.tsyz]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.ksrq,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.shsj,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.xmmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.ypgg"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.dcjl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.jldwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.sl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yfdwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yyffmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yssm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.ysqmxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.zxhsxm"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="parent.yzxx.length==0" class=" noData text-center zan-border">暂无数据...</p>
                    </div>

                </div>
                <div class=" addList flex-container flex-align-c">
                    <button v-waves class="tong-btn btn-parmary-b" @click="queryyz">刷新</button>
                    <button v-waves class="tong-btn btn-parmary-b" @click="auditing">修改</button>
                    <span>修改为：</span>
                    <input id="xgsj2" class="zui-input wh180" @click="showDate($index,'xgsj2','xgsj2')" v-model="xgsj2">
                </div>
            </div>


            <div class="yzcl_div yzclyzcx" v-if="index == 2">
                <div class="zui-table-view hzList padd-r-10 padd-l-10" v-for="(parent,index) in jsonList">
                    <div class="jbxx-size margin-t-5">
                        <div class="jbxx-box padd-l-12">
                            科别：<span class="font-14-654 padd-r-18" v-text="parent.ryksmc"></span>
                            床号：<span class="font-14-654 padd-r-18" v-text="parent.rycwbh"></span>
                            姓名：<span class="font-14-654 padd-r-18" v-text="parent.brxm"></span>
                            性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[parent.brxb]"></span>
                            年龄：<span class="font-14-654 padd-r-18" v-text="parent.nl + nldw_tran[parent.nldw]"></span>
                            住院号：<span class="font-14-654 padd-r-18" v-text="parent.zyh"></span>
                        </div>
                    </div>
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>

                                <th class="cell-m">
                                    <div class="cell-m  text-center">
                                        <input type="checkbox" v-model="isCheckAll"
                                               @click="checkYZAll('4',jsonList,parent.yzxx[0].no)">
                                    </div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-m">序号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s ">类型</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">开始时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医生停嘱时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医嘱内容</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">药品规格</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">频次</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用药方法</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医生签名</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)">
                        <table class="zui-table" v-if="parent.yzxx.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSome('4_'+yZItem.no+'_'+$index)" :tabindex="$index"
                                v-for="(yZItem, $index) in parent.yzxx">

                                <td class="cell-m">
                                    <div class="cell-m text-center">
                                        <input type="checkbox" v-model="isChecked['4_'+yZItem.no+'_'+$index]"
                                               @click="checkSome('4_'+yZItem.no+'_'+$index)">
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yzlx_tran[yZItem.yzlx]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.ksrq,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.ystzsj,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.xmmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.ypgg"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.sl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yfdwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.pcmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yyffmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.ysxm"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="parent.yzxx.length==0" class=" noData text-center zan-border">暂无数据...</p>
                    </div>

                </div>
                <div class="addList flex-container flex-align-c">
                    <button v-waves class="tong-btn btn-parmary-b" @click="queryyz">刷新</button>
                    <button v-waves class="tong-btn btn-parmary-b" @click="auditing">修改</button>
                    <span>修改为：</span>
                    <input id="xgsj4" class="zui-input wh180" @click="showDate($index,'xgsj4','xgsj4')" v-model="xgsj4">
                </div>
            </div>


            <!-- 医生停嘱时间 -->


            <!-- 护士停嘱时间 -->
            <div class="yzcl_div yzclyzcx" v-if="index == 3">
                <div class="zui-table-view hzList padd-r-10 padd-l-10" v-for="(parent,index) in jsonList">
                    <div class="jbxx-size margin-t-5">
                        <div class="jbxx-box padd-l-12">
                            科别：<span class="font-14-654 padd-r-18" v-text="parent.ryksmc"></span>
                            床号：<span class="font-14-654 padd-r-18" v-text="parent.rycwbh"></span>
                            姓名：<span class="font-14-654 padd-r-18" v-text="parent.brxm"></span>
                            性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[parent.brxb]"></span>
                            年龄：<span class="font-14-654 padd-r-18" v-text="parent.nl + nldw_tran[parent.nldw]"></span>
                            住院号：<span class="font-14-654 padd-r-18" v-text="parent.zyh"></span>
                        </div>
                    </div>
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="cell-m  text-center">
                                        <input type="checkbox" v-model="isCheckAll"
                                               @click="checkYZAll('5',jsonList,parent.yzxx[0].no)">
                                    </div>

                                </th>
                                <th>
                                    <div class="zui-table-cell cell-m">序号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s ">类型</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">开始时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医生停嘱时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">护士停嘱时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医嘱内容</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">药品规格</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">频次</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用药方法</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医生签名</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">护士签名</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)">
                        <table class="zui-table" v-if="parent.yzxx.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event),checkSome('5_'+yZItem.no+'_'+$index)"
                                :tabindex="$index"
                                v-for="(yZItem, $index) in parent.yzxx">

                                <td class="cell-m">
                                    <div class="cell-m  text-center">
                                        <input type="checkbox"
                                               v-model="isChecked['5_'+yZItem.no+'_'+$index]"
                                               @click="checkSome('5_'+yZItem.no+'_'+$index)">
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yzlx_tran[yZItem.yzlx]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.ksrq,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.ystzsj,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.hstzsj,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.xmmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.ypgg"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.sl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yfdwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.pcmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yyffmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.ysxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.zxhsxm"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="parent.yzxx.length==0" class=" noData text-center zan-border">暂无数据...</p>
                    </div>

                </div>
                <div class="addList flex-container flex-align-c">
                    <button v-waves class="tong-btn btn-parmary-b" @click="queryyz">刷新</button>
                    <button v-waves class="tong-btn btn-parmary-b" @click="auditing">修改</button>
                    <span>修改为：</span>
                    <input id="xgsj5" class="zui-input wh180" @click="showDate($index,'xgsj5','xgsj5')" v-model="xgsj5">
                </div>
            </div>


            <div class="yzcl_div yzclyzcx" v-if="index == 4">
                <div class="zui-table-view hzList padd-r-10 padd-l-10" v-for="(parent,index) in jsonList">
                    <div class="jbxx-size margin-t-5">
                        <div class="jbxx-box padd-l-12">
                            科别：<span class="font-14-654 padd-r-18" v-text="parent.ryksmc"></span>
                            床号：<span class="font-14-654 padd-r-18" v-text="parent.rycwbh"></span>
                            姓名：<span class="font-14-654 padd-r-18" v-text="parent.brxm"></span>
                            性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[parent.brxb]"></span>
                            年龄：<span class="font-14-654 padd-r-18" v-text="parent.nl + nldw_tran[parent.nldw]"></span>
                            住院号：<span class="font-14-654 padd-r-18" v-text="parent.zyh"></span>
                        </div>
                    </div>
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>

                                <th class="cell-m">
                                    <div class="cell-m  text-center">
                                        <input type="checkbox" v-model="isCheckAll"
                                               @click="checkYZAll('3',jsonList,parent.yzxx[0].no)">
                                    </div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-m">序号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s ">住院号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">病人姓名</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">入院日期</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">病区出院日期</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)">
                        <table class="zui-table" v-if="parent.yzxx.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index"
                                v-for="(yZItem, $index) in parent.yzxx">

                                <td class="cell-m">
                                    <div class="cell-m flex-container flex-align-c flex-jus-c zui-table-cell text-center">
                                        <input type="checkbox"
                                               v-model="isChecked['3_'+yZItem.no+'_'+$index]"
                                               @click="checkSome('3_'+yZItem.no+'_'+$index)">
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.zyh"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.brxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.ryrq,'datetime')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.bqcyrq,'datetime')"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="parent.yzxx.length==0" class=" noData text-center zan-border">暂无数据...</p>
                    </div>

                </div>
                <div class=" addList flex-container flex-align-c">
                    <button v-waves class="tong-btn btn-parmary-b" @click="queryyz">刷新</button>
                    <button v-waves class="tong-btn btn-parmary-b" @click="auditing">修改</button>
                    <span>修改为：</span>
                    <input id="xgsj3" class="zui-input wh180" @click="showDate($index,'dbegin','xgsj3')" v-model="xgsj3">
                </div>
            </div>
            <div class="yzcl_div yzclyzcx" v-if="index == 5">
                <div class="zui-table-view hzList padd-r-10 padd-l-10" v-for="(parent,index) in jsonList">
                    <div class="jbxx-size margin-t-5">
                        <div class="jbxx-box padd-l-12">
                            科别：<span class="font-14-654 padd-r-18" v-text="parent.ryksmc"></span>
                            床号：<span class="font-14-654 padd-r-18" v-text="parent.rycwbh"></span>
                            姓名：<span class="font-14-654 padd-r-18" v-text="parent.brxm"></span>
                            性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[parent.brxb]"></span>
                            年龄：<span class="font-14-654 padd-r-18" v-text="parent.nl + nldw_tran[parent.nldw]"></span>
                            住院号：<span class="font-14-654 padd-r-18" v-text="parent.zyh"></span>
                        </div>
                        <span class="padd-r-5 padd-l-5">  </span>
                        <div class="addList flex-container flex-align-c">
                            <button v-waves class="tong-btn btn-parmary-b" @click="queryyz">刷新</button>
                        </div>
                    </div>
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-m">序号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s ">修改类型</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">修改时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">修改内容</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">修改人员</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">住院号</div>
                                </th>

                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)">
                        <table class="zui-table" v-if="parent.yzxx.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                :tabindex="$index"
                                v-for="(yZItem, $index) in parent.yzxx">
                                <td>
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="xglx_tran[yZItem.xglx]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.czsj,'datetime')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.xgnr"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.czyxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.zyh"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="parent.yzxx.length==0" class=" noData text-center zan-border">暂无数据...</p>
                    </div>

                </div>
                <div class="addList flex-container flex-align-c">
                    <button v-waves class="tong-btn btn-parmary-b" @click="queryyz">刷新</button>
                </div>
            </div>

            <div class="yzcl_div yzclyzcx" v-if="index == 6">
                <div class="zui-table-view hzList padd-r-10 padd-l-10" v-for="(parent,index) in jsonList">
                    <div class="jbxx-size margin-t-5">
                        <div class="jbxx-box padd-l-12">
                            科别：<span class="font-14-654 padd-r-18" v-text="parent.ryksmc"></span>
                            床号：<span class="font-14-654 padd-r-18" v-text="parent.rycwbh"></span>
                            姓名：<span class="font-14-654 padd-r-18" v-text="parent.brxm"></span>
                            性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[parent.brxb]"></span>
                            年龄：<span class="font-14-654 padd-r-18" v-text="parent.nl + nldw_tran[parent.nldw]"></span>
                            住院号：<span class="font-14-654 padd-r-18" v-text="parent.zyh"></span>
                        </div>
                    </div>
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="cell-m  text-center">
                                        <input type="checkbox"
                                               v-model="isCheckAll"
                                               @click="checkYZAll('7',jsonList,parent.yzxx[0].no)">
                                    </div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-m">序号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s ">下嘱时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">首次执行时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">医嘱内容</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">药品规格</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">剂量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">剂量单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用法</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">频次</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">护士签名</div>
                                </th>

                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTable($event)">
                        <table class="zui-table" v-if="parent.yzxx.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event),checkOne('7_'+yZItem.no+'_'+$index)"
                                :tabindex="$index"
                                v-for="(yZItem, $index) in parent.yzxx">

                                <td>
                                    <div class="cell-m  text-center">
                                        <input type="checkbox"
                                               v-model="isChecked['7_'+yZItem.no+'_'+$index]"
                                               @click="checkSome('7_'+yZItem.no+'_'+$index)">
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.ksrq,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(yZItem.zxsj,'short')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.xmmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.ypgg"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.dcjl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.jldwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.sl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yfdwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.yyffmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.pcmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="yZItem.zxhsxm"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="parent.yzxx.length==0" class=" noData text-center zan-border">暂无数据...</p>
                    </div>

                </div>
                <div class="addList flex-container flex-align-c">
                    <button v-waves class="tong-btn btn-parmary-b" @click="queryyz">刷新</button>
                    <button v-waves class="tong-btn btn-parmary-b" @click="auditing">修改</button>
                    <span>修改为：</span>
                    <input id="xgsj6" class="zui-input wh180" @click="showDate($index,'xgsj6','xgsj6')" v-model="xgsj6"></div>
            </div>
            </div>

            <!--		   </vue-scroll>-->
            <button  class="showInfo tong-btn btn-parmary-b" @click="showPop">查看病人基本信息</button>
        </div>
    </div>

    <div class="side-form printHide flex-container flex-dir-c pop-width" id="UserHzxx" :class="{'ng-hide':!isFold}">
        <div class="fyxm-side-top flex-between">
            <span>患者信息</span>
            <span class="fr closex ti-close" @click="isFold=false"></span>
        </div>
        <div class="ksys-side flex-container flex-dir-c flex-one">
            <ul class="tab-edit-list1 useritem flex-start over-auto">
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">住院号</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.zyh"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">病人姓名</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.brxm"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">性别</span><span
                        class="userValue flex-container  flex-align-c" v-text="brxb_tran[baseInfo.brxb]"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">年龄</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.nl"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">工作单位</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.gzdw"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">家庭住址</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.jzdmc"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">联系电话</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.lxdh"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">入院费别</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.ryfb"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">入院时期</span><span
                        class="userValue flex-container  flex-align-c"
                        v-text="fDate(baseInfo.ryrq,'yyyy-MM-dd')"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">出院时间</span><span
                        class="userValue flex-container  flex-align-c"
                        v-text="fDate(baseInfo.cyrq,'yyyy-MM-dd')"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">住院天数</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.zyts"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">护理等级</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.hldj"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">主治医师</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.zyysxm"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">费用合计</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.fyhj"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">现金记账</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.xjjz"></span></li>
                <li class="userlist flex-container"><span
                        class="userKey flex-container flex-jus-e  flex-align-c">预交合计</span><span
                        class="userValue flex-container  flex-align-c" v-text="baseInfo.yjhj"></span></li>
            </ul>
        </div>
    </div>

</div>
<script type="text/javascript" src="yzcl.js"></script>
