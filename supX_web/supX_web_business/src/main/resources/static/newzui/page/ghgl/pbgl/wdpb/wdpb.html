<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>我的排班</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script type="text/javascript">
    </script>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
    <div class="wrapper">
        <div class="alert alert-warning notice ">
            <span v-text="name"></span>，您好，本周您的工作排班情况为
            <span v-for="(item, $index) in obj">
                <span v-text="$index"></span>
                <span v-text="item"></span>
                次，
            </span>
        </div>
        <div class="scheduling ">
        <div class="zui-row toolbar">
            <div class="tool-left">
                <button class="zui-btn btn-default btn-pa"  @click="prvWeek()"><i class="wb-chevron-left"></i></button>
                <span :id="fDate(this.toDay, 'date')" class="toolDate"></span>
                <div class="zui-input-inline zui-date">
                    <input type="text" name="phone"  class="zui-input date">
                </div>

                <button class="zui-btn btn-default btn-pa " @click="nextWeek()"><i class="wb-chevron-right"></i></button>
                <button class="zui-btn btn-primary" @click="selectDay = toDay,getPbInfo()">本周</button>
            </div>
        </div>
            <!--<div class="toolDate">{{fDate(this.toDay, 'date')}}-->
                <!--<div class="zui-input-inline zui-date">-->
                    <!--<input type="text" name="phone"  class="zui-input date">-->
                <!--</div>-->
            <!--</div>-->

        <div class="paiban-week">
            <table class="table-render toDayTable">
                <thead>
                <tr class="ng-scope">
                    <th class="ng-binding">班次</th>
                    <th>周一{{weekToDay(selectDay, 1)}}</th>
                    <th>周二{{weekToDay(selectDay, 2)}}</th>
                    <th>周三{{weekToDay(selectDay, 3)}}</th>
                    <th>周四{{weekToDay(selectDay, 4)}}</th>
                    <th>周五{{weekToDay(selectDay, 5)}}</th>
                    <th>周六{{weekToDay(selectDay, 6)}}</th>
                    <th>周日{{weekToDay(selectDay, 7)}}</th>
                </tr>
                </thead>
                <tbody>
                <tr class="ng-scope" :id="my.czybm">
                    <th v-text="my.czyxm" style="border-right: 1px solid #ccc"></th>
                    <td v-for="(i, $index) in 7" :ryId="my.czybm" :week="$index+1"></td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
        <div class="option">
            <div v-for="(item, $index) in optionList">
                <div class="optionBtu" v-text="item.bcfamc" :style="{background: item.colour, color: item.fontcolour}"></div>
                <span>({{item.sbsj}} - {{item.xbsj}})</span>
            </div>
        </div>
    </div>
    <script type="application/javascript" src="wdpb.js"></script>
</body>
</html>
