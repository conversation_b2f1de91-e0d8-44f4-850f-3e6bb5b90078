.padd-l0 {
  padding-left: 0 !important;
}
.zui-top {
  width: 28%;
  text-align: right;
  float: left;
  height: 36px;
  line-height: 36px;
}
.ysb-zui-wid {
  width: 60% !important;
}
.zui-table-view .zui-table-fixed.table-fixed-r {
  border-left: none !important;
}
.zui-table-view table tr {
  border-bottom: none;
}
.ysb-input .col-x-12 .zui-bottom {
  width: 100%;
  float: left;
}
.ysb-input .validate {
  right: 11px;
}
.ysb-input .l-syfl .validate {
  right: -20px;
}
.l-btm0 {
  margin-bottom: 5px !important;
}
.zui-table-view .zui-table-fixed table {
  border-bottom: 1px solid #eee;
}
#jyxm_icon .switch {
  left: 15% !important;
  overflow: inherit;
  height: auto;
  top: -5px !important;
}
.zui-table-view table td {
  border-bottom: none;
}
@media only screen and (max-width: 1366px) {
  #jyxm_icon .switch {
    left: 11% !important;
  }
}
.tong-search{
padding: 13px 0 5px 20px;
}