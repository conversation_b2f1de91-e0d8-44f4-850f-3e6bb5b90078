
(function () {
	//顶部工具栏
    var yp_toolMenu=new Vue({
        el:'.ypbxxm .tong-top',
        mixins: [dic_transform, tableBase, mConfirm,scrollOps],
        data: {
			bxlbbm: null,
			bxurl: null,
			searchtext2: null,
			type:'qb',

        },
        methods: {
           sschangeDown2: function() {
				if(window.event.keyCode == 13) {
					yp_toolMenu.searchtext2 = $('#search2').val();
					yp_toolMenu.getData();
				}
			},

		    changeType:function(xType){
			  yp_toolMenu.type=xType;
			  yp_toolMenu.getData();
            },

			getbxlb: function() {
				var param = {
					bxjk: "005"
				};
				common.openloading("#ypbxxm");
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" +
					JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							if(json.d.list.length > 0) {
								yp_toolMenu.bxlbbm = json.d.list[0].bxlbbm;
								yp_toolMenu.bxurl = json.d.list[0].url;
							}
							common.closeLoading();
						} else {
							malert("保险类别查询失败!" + json.c);
                            common.closeLoading();
						}
					});
			},

			getData: function() {
				this.param.psyp=null;
            	this.param.yyff=null;
				 if(yp_toolMenu.type=='yd'){
	                	this.param.psyp='1';
	                }
	                if(yp_toolMenu.type=='wd'){
	                	this.param.yyff='1';
	                }
				this.param.parm = yp_toolMenu.searchtext2;
				common.openloading("#ypbxxm");
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=queryYpxx&parm=" + JSON.stringify(this.param),
					function(json) {
						if(json.a == 0) {
							var res = eval('(' + json.d + ')');
                            ypXmMx.totlePage = Math.ceil(res.total / ypXmMx.param.rows);
							ypXmMx.jsonList = res.list;
                            common.closeLoading();
						} else {
							malert(json.c);
                            common.closeLoading();
						}
					});

			},

			// 保存项目详情
			save: function(bxlbbm, ypbm, bxxmlb, bxxmbm, bxxmmc) {
				var param = {
					'page': 1,
					'rows': 30,
					'bxlbbm': bxlbbm,
					'ypbm': ypbm,
					/*'bxxmlb': bxxmlb,*/
					'bxxmbm': bxxmbm,
					'bxxmmc': bxxmmc
				};
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=insertYpxx&parm=" + JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							malert("保存药品保险项目成功！");
                            yp_toolMenu.getData();
						} else {
							malert(json.c);
						}
					});
			},

			// 删除项目详情
			remove: function() {

			},

			//获取药品项目
			loadXm: function() {
				var param = {
					page: 1,
					rows: 30,
					bxlbbm: yp_toolMenu.bxlbbm
				};
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=getHisYpzd&parm=" + JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							malert("获取药品农合项目成功！");
							yp_toolMenu.getData();
						} else {
							malert(json.c);
						}
					});
			},

			//自动对码（项目名称）
			autoDm: function() {
				var param = {
					page: 1,
					rows: 30
				};
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=autoDmYp&parm=" + JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							malert("自动对码（药品项目名称）成功！","top","success");
							yp_toolMenu.getData();
						} else {
							malert(json.c,"top","defeated");
						}
					});
			},
        }
    });

	yp_toolMenu.getbxlb();

    //左边药品
    var ypBxXm = new Vue({
        el: '.ypbxxm-left',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],

        data: {
            jsonList: [],
			searchCon: []
        },
        methods: {
			getData: function() {
				var param = {
					bxjk: '005'
				}
				$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" +
					JSON.stringify(param),
					function(json) {
						ypBxXm.totlePage = Math.ceil(json.d.total / ypBxXm.param.rows);
						ypBxXm.jsonList = json.d.list;
					});
			},
			checkOne: function() {
				yp_toolMenu.getData();
			}
        }
    });

    ypBxXm.getData();

    //右边药品保险对应
    var ypXmMx= new Vue({
        el: '.ypbxxm-right',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],
        components: {
			'search-table': searchTable
		},
        data: {
            qjIndex: null,
			jsonList: [],
			isEdit: null,
            them_tran: {},
			text: null,
			page: {
				page: 1,
				rows: 20,
				total: null
			},
            str_param:{},
			popContent: {},
			searchCon: {},
			selSearch: -1,
			dg: {
				page: 1,
				rows: 50,
				sort: "",
				order: "asc",
                total: null,
				parm: ""
			},
			// them: {'项目编码': 'bxxmbm', '项目名称': 'bxxmmc', '剂型': 'modelName','标准单价': 'selfScale'}
            them: {'项目编码': 'bxxmbm', '项目名称': 'bxxmmc','拼音代码':'inputPyCode','规格':'spec','剂型':'conf','保内比例':'ratio','备注':'remark'}

        },
        methods: {
            edit: function(index) {
				console.log(index);
				this.isEdit = index;
			},
			// 点击进行赋值的操作
			selectOne: function(item) {
				if(item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
					this.page.page++; // 设置当前页号
					this.searching(index, true, 'bxxmmc', ypXmMx.jsonList[index].bxxmmc); // 传参表示请求下一页,不传就表示请求第一页
				} else { // 否则就是选中事件,为json赋值
					ypXmMx.popContent = item;
					Vue.set(ypXmMx.jsonList[ypXmMx.qjIndex], 'bxxmmc', ypXmMx.popContent['bxxmmc']);
					ypXmMx.jsonList[ypXmMx.qjIndex].bxxmbm=this.popContent.mediCode;
					//ypXmMx.jsonList[ypXmMx.qjIndex].bxxmlb=this.popContent.yka001;
					//ypXmMx.jsonList[ypXmMx.qjIndex].zfbl=this.popContent.yka096;
					yp_toolMenu.save(yp_toolMenu.bxlbbm, ypXmMx.jsonList[ypXmMx.qjIndex]['xmbm'], '',item.bxxmbm, item.bxxmmc);
					$(".selectGroup").hide();
				}
			},
			changeDown: function(index, event, type) {
				ypXmMx.qjIndex = index;
				if(this['searchCon'][this.selSearch] == undefined) return;
				this.keyCodeFunction(event, 'popContent', 'searchCon');
				if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
					if(type = "text") {
						Vue.set(ypXmMx.jsonList[index], 'bxxmmc', ypXmMx.popContent['bxxmmc']);
						ypXmMx.jsonList[index].bxxmbm=this.popContent.bxxmbm;
						//ypXmMx.jsonList[index].bxxmlb=this.popContent.yka001;
						//ypXmMx.jsonList[index].zfbl=this.popContent.yka096;
						yp_toolMenu.save(yp_toolMenu.bxlbbm, ypXmMx.jsonList[index]['xmbm'],'',ypXmMx.jsonList[index]['bxxmbm'], ypXmMx.jsonList[index]['bxxmmc']);
						this.nextFocus(event);
					}
				}
			},
			// 输入内容进行检索
			searching: function(index, add, type, val) {
				ypXmMx.qjIndex = index;
				// this.jsonList[index]['bxxmmc'] = val;
				if(!add) this.page.page = 1;
				var _searchEvent = $(event.target.nextElementSibling).eq(0);
				ypXmMx.popContent = {};
                ypXmMx.dg["parm"] = val;
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=ypxx&method=query&parm=" + JSON.stringify(ypXmMx.dg),
					function(json) {
						if(json.a == 0) {
							var res = eval('(' + json.d + ')');
							console.log(JSON.stringify(res));
							if(add) {
								for(var i = 0; i < res.list.length; i++) {
									ypXmMx.searchCon.push(res.list[i]);
								}
							} else {
								ypXmMx.searchCon = res.list;
							}
							ypXmMx.page.total = res.total;
							ypXmMx.selSearch = 0;
							if(res.list.length > 0 && !add) {
								$(".selectGroup").hide();
								_searchEvent.show();
							}
						} else {
							malert(json.c);
						}
					});
			},
            getData: function(){
                yp_toolMenu.getData();
            }
        }
    });

    $('body').click(function() {
		$(".selectGroup").hide();
	});

	$(".selectGroup").click(function(e) {
		e.stopPropagation();
	});

})();
