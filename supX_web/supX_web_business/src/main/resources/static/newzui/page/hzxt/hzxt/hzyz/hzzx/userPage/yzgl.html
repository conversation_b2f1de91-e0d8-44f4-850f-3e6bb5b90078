<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="/newzui/pub/css/print.css" media="print" />
    <link href="userPage/user.css" rel="stylesheet">
    <style>
        .zui-select-inline, .zui-table-view .zui-table-cell {
            height: unset;
            vertical-align: top;
        }

        .hzList input {
            height: 28px;
        }
        .hzList .cell-m{
            width: 60px !important;
        }
        .hzList input:disabled {
            border: none;
            text-align: center;
            background-color: transparent !important;
        }
        .hzList .no-disabled:disabled {
            border: 1px solid #d7dbe1;
            color: #000000;
            text-align: left;
            background-color: #f8f8f8 !important;

        }

        .tem {
            position: relative;
            float: left;
            /* width: 300px; */
            /* height: 450px; */
            width: 800px;
            height: 500px;
            border: 1px solid green;
            margin-left: 20px;
            margin-top: 20px;
        }

        .item {
            position: absolute;
            display: inline-block;
            top: 10px;
            margin-bottom: 20px;
            font-size: 14px;
            cursor: default;
            z-index: 100;
        }

        .nextTickHeight tr td {
            padding: 7px 0 !important;
        }

        .hzList {
            border-bottom: none;
        }

        .yzgl-no-pad .zui-input {
            padding: 0 10px;
        }
        .icon-baocunb:before,.icon-dysqb:before{
            vertical-align: top;
        }
        .hzList  .zui-select-inline {
            width: 100% !important;
        }
        .loadPage{
            overflow: hidden;
        }
        .hide{
            display: none;
        }
        .height-auto{
            height: auto!important;
        }
        .no-print-jcjyfy.no{
            display: none;
        }
        .zui-table-view .zui-table-header .zui-table-cell,
        .zui-table-view .zui-table-header .zui-table-cell span{
            height: 25px;
            line-height: 25px;
        }
        .zui-table-view .zui-table-body tr td{
            padding: 0;
        }
        .zui-table-view .zui-table-tool{
            left: 0;
            right: 0;
        }
        .loadPage .hzgl-flex{
            height: 45px;
        }
       .printShow .zui-table-view .bg-red{
            background-color: rgba(255, 0, 0, 0.3);
        }
        .printShow .zui-table-view  .bg-red1{
            background-color: rgba(255, 255, 0, 0.3);
        }
        .printShow .zui-table-view  .bg-green{
            background-color:rgba(26, 188, 156, 0.3);
        }
        .printShow .zui-table-view  .bg-blue{
            background-color: rgba(0, 0, 255, 0.3);
        }
        .zyyzmb_model{
            height: 400px;
        }

        .N03003200142-sameStart{
            position: absolute;
            height: 50%;
            left: 2%;
            bottom: 0px;
            width: 10px !important;
            border-top: 1px solid rgb(0, 0, 0);
            border-left: 1px solid rgb(0, 0, 0);
            border-right: 0px;
            border-bottom: 0px;
        }
        .N03003200142-sameEnd {
            position: absolute;
            height: 50%;
            left: 2%;
            top: 0px;
            width: 10px !important;
            border-top: 0px;
            border-left: 1px solid rgb(0, 0, 0);
            border-right: 0px;
            border-bottom: 1px solid rgb(0, 0, 0);
        }
        .N03003200142-same {
            position: absolute;
            height: 100%;
            left: 2%;
            top: 0px;
            width: 10px !important;
            border-top: 0px;
            border-left: 1px solid rgb(0, 0, 0);
            border-right: 0px;
            border-bottom: 0px;
        }
        .N0300320014201 {
            height: 50%;
            width: 100%;
            top: 0%;
            position:  relative;
            padding-left: 4%;
            float: left;
        }
        .N0300320014202 {
            height: 50%;
            width: 100%;
            position: relative;
            padding-left: 4%;
            float: left;
        }
        .N0300320014203 {
            height: 100%;
            width: 100%;
            top: 0%;
            position:  relative;
            padding-left: 4%;
            font-size: 13px;
            white-space:normal;
        }
        .orange{
            color:#f2a654
        }
    </style>
</head>

<body>
    <div class="printArea no-print printShow no-print-jcjyfy"></div>
    <div class="printShow padd-l-10 padd-r-10 flex-container flex-dir-c no-print-jcjyfy">
        <div class="panel printHide" v-if="ifShow" :s="yfbmListen" v-cloak>
            <div class="flex-container flex-align-c flex-wrap-w ">
                <div :id="yzList" class="  flex-container flex-align-c margin-b-10  margin-l-20 " v-if="haveYe">
                    <label class="whiteSpace margin-r-5 ft-14">亲属选择</label>
                    <select-input class="wh120" @change-data="commonResultChange"
                                  :child="qsxzList" :index="'yexm'" :index_val="'yebh'" :val="popContent.yebh"
                                  :name="'popContent.yebh'" :index_mc="'yexm'" search="true" id="yebh">
                    </select-input>
                </div>
                <div class="  flex-container flex-align-c margin-b-10  margin-l-20 ">
                    <label class="whiteSpace margin-r-5 ft-14">类型</label>
                    <select-input class="wh120" @change-data="resultChange_type" :not_empty="false" :child="yzlx_tran01"
                        :index="popContent.yzxx" :val="popContent.yzxx" :name="'popContent.yzxx'">
                    </select-input>
                </div>
                <div class=" flex-container flex-align-c margin-b-10 margin-l-20 ">
                    <label class="whiteSpace margin-r-5 ft-14">医嘱过滤</label>
                    <select-input class="wh120" @change-data="resultChange_type" :not_empty="false" :child="yzgl_tran"
                        :index="popContent.yzgl" :val="popContent.yzgl" :name="'popContent.yzgl'">
                    </select-input>
                </div>
                <div class=" flex-container flex-align-c margin-b-10 margin-l-20 ">
                    <label  class="whiteSpace margin-r-5 ft-14">药房选择</label>
                    <select-input class="wh120" @change-data="resultChange_text" :not_empty="false" :child="Yf_List"
                        :index="'yfmc'" :index_val="'yfbm'" :val="popContent.yfbm" :name="'popContent.yfbm'">
                    </select-input>
                </div>
                <div class=" flex-container flex-align-c margin-b-10 margin-l-20 ">
                    <label class="whiteSpace margin-r-5 ft-14">停嘱时间</label>
                    <div class="zui-input-inline wh180">
                        <input class="zui-input wh180" type="text"  id="timeTabArr" v-model="timeTabArr">
                    </div>
                </div>
                <div class=" flex-container flex-align-c margin-b-10 margin-l-20 ">
                    <label class="whiteSpace margin-r-5 ft-14 cursor pc-select flex-container flex-align-c flex-jus-c" @click="tabCode">{{jsfw_tran[qhCode]}}【F4】切换</label>
                </div>
                <div class=" flex-container flex-align-c margin-b-10 margin-l-20 ">
                	<label class="whiteSpace margin-r-5 ft-14 cursor pc-select flex-container flex-align-c flex-jus-c" @click="HlyyReview">【F8】用药说明书</label>
                </div>
            </div>
        </div>
        <div class="zui-table-view  hzList printHide" v-cloak style="height: auto;">
            <div class="zui-table-header" v-if="isShow">
                <table class="zui-table table-width50">
                    <thead>
                        <tr>

                            <th class="wh50 text-center">
                                <input-checkbox @result="reCheckBox" :list="'Yzxx_List'" :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class=" text-center" style="width: 30px">
                                <div class="zui-table-cell  " style="width: 30px"><span>序号</span></div>
                            </th>
                            <th class=" cell-m">
                                <div class="zui-table-cell  cell-m"><span>标识</span></div>
                            </th>
                            <th class=" cell-m">
                                <div class="zui-table-cell  cell-m"><span>类型</span></div>
                            </th>
                            <th class=" cell-m" v-if="N03003200137 == '1'">
                                <div class="zui-table-cell  cell-m"><span>是否急诊</span></div>
                            </th>
                            <th class=" cell-m">
                                <div class="zui-table-cell  cell-m"><span>分类</span></div>
                            </th>
                            <th style="width:170px;">
                                <div class="zui-table-cell " style="width:170px;"><span>开始时间</span></div>
                            </th>
                            <th class=" " style="width: 30px">
                                <div class="zui-table-cell  " style="width: 30px"><span>组号</span></div>
                            </th>
                            <th class=" wh50">
                                <div class="zui-table-cell wh50 "><span>嘱托</span></div>
                            </th>
                            <th style="width: 250px">
                                <div style="width: 250px" class="zui-table-cell   text-left "><span>医嘱名称</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s"><span>规格</span></div>
                            </th>
                            <th class="wh70 " >
                                <div class="zui-table-cell  wh70" ><span>剂量</span></div>
                            </th>
                            <th class=" " style="width: 80px" >
                                <div class="zui-table-cell  " style="width: 80px"><span>用药方法</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s"><span>频次</span></div>
                            </th>
                            <th class=" cell-m">
                                <div class="zui-table-cell cell-m "><span>总量</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s cell-s"><span>输液速度</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s"><span>单位</span></div>
                            </th>
                            <th class=" cell-xxxxl">
                                <div class="zui-table-cell  cell-xxxxl"><span>用药说明</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s"><span>医生签名</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-l"><span>签名时间</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-l"><span>执行时间</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s"><span>执行科室</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s"><span>停嘱医生</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-xl"><span>停嘱时间</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s"><span>下单医生</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell  cell-s"><span>药房名称</span></div>
                            </th>
                            <th class=" cell-m">
                                <div class="zui-table-cell cell-s"><span>状态</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>申请单号</span></div>
                            </th>
                            <th class=" cell-s" v-show="haveYe">
                                <div class="zui-table-cell  cell-s"><span>使用人</span></div>
                            </th>
                            <th class=" cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
            <!-- @click="checkSelect([$index,'some','Yzxx_List'],$event,'notIndex'),hover($index,$event)"-->
            <div class="zui-table-body" ref="body" v-if="isShow" @scroll="scrollTable($event)">
                <table class="zui-table " v-if="Yzxx_List.length!=0">
                    <tbody>
                        <tr @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" v-for="(item, $index) in Yzxx_List"
                            :data-change="checkAll" :class="[{'table-hovers':hlyyCSs[$index],'table-hover':$index === hoverIndex},{'table-hovers':checkAll},color[item.zt]]"
                            @dblclick="Wf_DblClick2($index)">
                            <td class="wh50 text-center">
                                <input-checkbox @result="reCheckChange"  v-if="item.sfhzyz" :list="'Yzxx_List'" :type="'some'" :which="$index"
                                    :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class=" text-center" style="width: 30px">
                                <div :class="color[item.zt]" style="width: 30px"  class="zui-table-cell text-center " v-text="item.xssx"></div>
                            </td>
                            <td class="cell-m text-center">
                                <div :class="[{'orange':item.sfhzyz == '1'}, {'': item.sfhzyz != '1'}]" style="width: 30px"  class="zui-table-cell text-center cell-m" v-text="item.sfhzyz?'会诊':'无'"></div>
                            </td>
                            <td class="cell-m">
                                <div class=" yzgl-no-pad cell-m">
                                    <select-input ref="addLx" @change-data="resultChange_item_fzh" :not_empty="false" :child="yzlx_tran"
                                        :index="'item.yzlx'" :val="item.yzlx" :name="$index + '.yzlx.' + 2" :disable="item.readonly"
                                        :search="true">
                                    </select-input>
                                </div>
                            </td>
                            <td class="cell-m" v-if="N03003200137 == '1'">
                                <div class=" yzgl-no-pad cell-m">
                                    <select-input @change-data="resultChange_item" :not_empty="false" :child="istrue_tran"
                                                  :index="'item.sfjz'" :val="item.sfjz" :name="$index + '.sfjz.' + 2" :disable="item.readonly"
                                                  :search="true">
                                    </select-input>
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class=" yzgl-no-pad cell-m">
                                    <select-input @change-data="resultChange_item_fzh" :not_empty="false" :child="yzfl_tran"
                                        :index="'item.yzfl'" :val="item.yzfl" :name="$index + '.yzfl.' + 2" :disable="item.readonly"
                                        :search="true">
                                    </select-input>
                                </div>
                            </td>
                            <td style="width:170px;">
                                <div class="zui-table-cell " style="width:170px;">
                                    <!--onclick="WdatePicker({ skin: 'whyGreen',maxDate:'%y-%M-%d' })"-->
                                    <input style="text-indent: 0;" type="text" class="zui-input" :class="'startdate'+$index"
                                        v-model="item.ksrq" @click="showDate($index,$event)"  @keydown="nextFocus($event)" :disabled="item.readonly" />
                                </div>
                            </td>
                            <td class="" style="width: 30px">
                                <div style="width: 30px" class="  zui-table-cell">
                                    <!--<select-input @change-data="resultChange_item_fzh" :not_empty="false" :child="fzh_tran"-->
                                        <!--:index="'item.fzh'" :val="item.fzh" :name="$index + '.fzh.' + 2" :search="true">-->
                                    <!--</select-input>-->
                                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" v-model="item.fzh" @input="fzhFun(item.fzh,$index)" @keyup.enter="fzhFun(item.fzh,$index)"  @keydown="nextFocus($event)" :disabled="item.readonly" />
                                </div>
                            </td>
                            <td class="wh50">
                                <div class="zui-table-cell wh50 ">
                                    <input type="checkbox" class="green" :id="'green'+$index" true-value="1" false-value="0" v-model="item.tsyz"
                                         :disabled="item.readonly" @click="Wf_XmChange($event,$index,'tsyz')" />
                                    <label :for="'green'+$index"></label>
                                </div>
                            </td>
                            <td class="cell-xl">
                                <div style="width: 250px" class="  " :id="item.tzbj" :class="[item.tzbj ]">
                                    <input :id="'xmmc_' + $index" class="zui-input text-left title overflow1" type="text"
                                        :value="item.xmmc" @keyup="Wf_changeDown($index,$event,'xmmc',$event.target.value)"
                                        @input="Wf_change(false,$index,'xmmc', $event.target.value)" :disabled="item.readonly" />
                                    <search-table :message="searchCon" :selected="selSearch" :page="page" :them="them"
                                        :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne2">
                                    </search-table>
                                </div>
                            </td>
                            <!--<input v-model="item.ypgg" class="zui-input hzgl-top text-center"/>-->
                            <td class="cell-s">
                                <div class="zui-table-cell   title overflow1 cell-s" :class="color[item.zt]" style="overflow: hidden;"
                                    v-text="item.ypgg"></div>
                            </td>
                            <td class="wh70" >
                                <div  class="zui-table-cell  wh70 flex-container flex-align-c">
                                    <input @mousewheel.prevent type="number" class="zui-input " min='0' max='1000' :id="'dcjl_' + $index"
                                        v-model="item.dcjl" @change="Wf_editChange($index,'dcjl')" @blur="Wf_editChange($index,'dcjl')"
                                        :disabled="item.readonly" :disabled="item.zlbz" @keydown="nextFocusJl($event,$index,false,false,item.dcjl,'剂量')"
                                        data-notEmpty="false" />
                                    <span :class="color[item.zt]">{{item.jldwmc}}</span>
                                </div>
                            </td>
                            <!--<input v-model="$index" class="zui-input hzgl-top text-center"/>-->
                            <!--<input v-model="$index" class="zui-input hzgl-top text-center"/>-->
                            <td class="" style="width: 80px">
                                <div class="" style="width: 80px">
                                    <input class="zui-input" :id="'yyffmc_' + $index" type="text" :value="item.yyffmc"
                                        data-notEmpty="false" @keydown="Wf_changeDown2($index,$event,'yyffmc')"
                                        @input="Wf_change2(false,$index,'yyffmc',$event.target.value)" :disabled="item.readonly" />
                                    <search-table2 :message="searchCon2" :selected="selSearch2" :them="them2"
                                        :them_tran="them_tran2" :page="pageStr" @click-one="checkedOneOut" @click-two="selectOne1">
                                    </search-table2>
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class=" cell-s">
                                    <input class="zui-input " @blur="Wf_editChange($index,'dcjl')" :id="'pcmc_' + $index"
                                        type="text" :value="item.pcmc" data-notEmpty="false" @keyup="Wf_changeDown3($index,$event,'pcmc',item.pcmc)"
                                        @input="Wf_change3(false,$index,'pcmc',$event.target.value)" :disabled="item.readonly" />
                                    <search-table3 :message="searchCon3" :selected="selSearch3" :page="queryStr" :them="them3"
                                        :them_tran="them_tran3" @click-one="checkedOneOut" @click-two="selectOne3">
                                    </search-table3>
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell flex-container flex-align-c cell-m ">
                                    <input  class="zui-input jcjy-top-side" type="number" :id="'sl_' + $index"
                                        @keyup.13="Wf_keyEnter($event,$index,'sl')" v-model="item.sl" :disabled="item.readonly"
                                        data-notEmpty="false" />
                                    <span :class="color[item.zt]">{{item.yfdwmc}}</span>
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell  cell-s">
                                    <input @mousewheel.prevent class="zui-input " type="number" @input="pubFzhsame($index)"
                                        :id="'sysd_' + $index" @keyup.13="Wf_keyEnter($event,$index,'sysd')" v-model="item.sysd"
                                        :disabled="item.readonly" data-notEmpty="false" @keydown="prevFocus,nextFocus($event)"
                                        data-notEmpty="false" />
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class=" padd-r-5 cell-s">
                                    <select-input :id="'sysddw_' + $index" @change-data="resultChange_item" :not_empty="false"
                                        :child="sydw_tran" :index="'item.sysddw'" :val="item.sysddw" :name="$index + '.sysddw.' + 1"
                                        :search="true" data-notEmpty="false">
                                    </select-input>
                                </div>
                            </td>
                            <td class="cell-xxxxl">
                                <div class="zui-table-cell  cell-xxxxl">
                                    <input @keyup.enter="prevFocus,Wf_keyEnter($event,$index,'yysm')"
                                        class="zui-input " type="text" :id="'yysm_' + $index" v-model="item.yysm"
                                        :disabled="item.readonly" data-notEmpty="false" />
                                </div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-s" v-text="item.ysqmxm"></div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-l" v-text="fDate(item.ysqmsj,'datetime')"></div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-l" v-text="fDate(item.zxsj,'datetime')"></div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-s" v-text="item.zxksmc"></div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-s" v-text="item.tzysxm"></div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-xl" v-text="fDate(item.ystzsj,'datetime')"></div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell -5 cell-s" v-text="item.xdysxm"></div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-s" v-text="item.yfmc"></div>
                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-s" :id="item.zt" v-text="yzzt_tran[item.zt]" ></div>

                            </td>
                            <td class="cell-s">
                                <div :class="color[item.zt]" class="zui-table-cell  cell-s title" v-text="item.sqdh"></div>
                            </td>
                            <td class="cell-s" v-if="haveYe">
                                <div class=" wdit120 cell-s">
                                    <!--                                    <select-input @change-data="resultChange_item_fzh" :not_empty="false" :child="yeList"-->
                                    <!--                                        :index="'yexm'" :index_val="'yebh'" :val="item.yebh" :name="$index + '.yebh.' + 2"-->
                                    <!--                                        :search="true"></select-input>-->
                                    {{item.yexm}}
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                    <i data-title="删除医嘱" class="icon-sc margin-r-10" @click="Wf_delete($index)" v-if="item.sfhzyz=='1' ?  toolIsSHow :  !toolIsSHow"></i>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <p v-if="Yzxx_List.length==0" class="  noData text-center zan-border">暂无数据...</p>
            </div>
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" v-for="(item, $index) in Yzxx_List" @click="hlyy(item,$index)"
                            :data-change="checkAll" :class="[{'table-hovers':hlyyCSs[$index],'table-hover':$index === hoverIndex},{'table-hovers':checkAll},color[item.zt]]"
                            @dblclick="Wf_DblClick2($index)"
                            class="tableTr2">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                    <i data-title="删除医嘱" class="icon-sc margin-r-10" @click="Wf_delete($index)" v-if="item.sfhzyz=='1'"></i>
                                    <i data-title="禁止操作" class="icon-zf-h margin-r-10" v-if="item.sfhzyz!='1'"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="zui-table-tool hzgl-flex" v-if="isShow">
                <button v-waves v-if="isShow" ref="addBot" @click="Wf_addYZ()" class="tong-btn  btn-parmary-b paddr-r5" >添加</button>
                <button v-waves v-if="isChecked.length>0" class="tong-btn  btn-parmary-b  paddr-r5" @click="Wf_delete()">作废</button>
                <button v-waves class="tong-btn btn-parmary-b   paddr-r5" @click="showLsYz(false)">复制医嘱</button>
                <button v-waves class="tong-btn btn-parmary-b   paddr-r5" @click="showYzTem()">西医医嘱模板</button>
                <button v-waves class="tong-btn btn-parmary-b paddr-r5 " @click="openJcjy()">检查/检验模板</button>
                <button v-waves class="tong-btn btn-parmary-b    paddr-r5 " @click="printJcShow()">医嘱单</button>
                <button v-waves class="tong-btn btn-parmary-b    paddr-r5 " @click="zyyz()">中药医嘱</button>
                <button v-waves class="tong-btn btn-parmary btn-parmary-not  yellow-bg" @click="Wf_saveYZ">保存</button>
                <p>
                    <span class="">待审核</span>
                    <span class="red">已停嘱</span>
                    <span class="red">已作废</span>
                    <span class="blue">待停嘱</span>
                    <span class="zgs">草稿</span>
                </p>
                <p class="padd-l-10">{{yxxSum}}元</p>
            </div>
            <div class="zui-table-tool hzgl-flex printHide" v-if="!isShow">
                <button v-waves class="tong-btn btn-parmary-d9" @click="yzShow()">取消</button>
                <button v-waves class="tong-btn btn-parmary   " @click="doPrint(false)">打印</button>
                <button v-waves class="tong-btn btn-parmary-f2a  " @click="doPrint(true)">续打</button>
            </div>

            <model  :model-show="false" @result-close="kss=false"
                   v-if="kss" :title="'抗生素使用目的'">
                <div class="bqcydj_model">
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">抗生素使用目的</span>
                            <select-input ref="kss" class="wh120" @change-data="resultChangeKss" :not_empty="false" :child="kjYwsymd_tran" :index="kssmd.kjywsymd"
                                          :val="kssmd.kjywsymd" :name="'kssmd.kjywsymd'">
                            </select-input>
                        </div>
                    </div>
                </div>
            </model>

            <model  :model-show="false" @result-close="tzsjType=false"
                   v-if="tzsjType" :title="'医嘱开始时间设置'">
                <div class="bqcydj_model">
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">医嘱开始时间</span>
                            <div class="zui-input-inline wh180">
		                    	<input @click="showUpdateDate($event)" v-model="updateTime" id="updateTime" class="zui-input wh180" type="text" readonly>
		                    </div>
                        </div>
                    </div>
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <div class="zui-input-inline wh180">
		                    	<button v-waves style="margin-left: 200px;" class="tong-btn btn-parmary btn-parmary-not
		                    	yellow-bg" @click="saveStartTime">保存</button>
		                    </div>
                        </div>
                    </div>
                </div>
            </model>

            <model  :model-show="false" @result-close="zxksType=false"
                   v-if="zxksType" :title="'医嘱执行科室设置'">
                <div class="bqcydj_model">
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">医嘱执行科室</span>
                            <div class="zui-input-inline wh180">
		                    	<select-input class="wh120" @change-data="resultChange_zxks" :not_empty="false" :child="zxksArr"
			                        :index="'ksmc'" :index_val="'ksbm'" :val="updateZxks" :name="'updateZxks'" :search="true">
			                    </select-input>
		                    </div>
                        </div>
                    </div>
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <div class="zui-input-inline wh180">
		                    	<button v-waves style="margin-left: 200px;" class="tong-btn btn-parmary btn-parmary-not
		                    	yellow-bg" @click="saveZxks">保存</button>
		                    </div>
                        </div>
                    </div>
                </div>
            </model>

            <!--弹窗展示另存模板和处方诊断（新加代码） -->
                <model class="popo" :s="'保存'" :c="'退出'"  @default-click="saveYzmb" @result-clear="exitYzmb" :model-show="true" @result-close="exitYzmb"
                       v-if="mbModel" :title="'保存为模板'">
                <div class="bqcydj_model background-f zyyzmb_model">
                    <div class="flex-container  flex-align-c flex-wrap-w" v-if="bcShow">
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">医嘱名称</span>
                            <input class="zui-input wh185" placeholder="请输入医嘱名称" type="text" :id="yzmc"
                                   v-model="mbZhyzContent.zhyzmc"
                                   @keydown="nextFocus($event)" data-notEmpty="false"
                                   @blur="setPYDM(mbZhyzContent.zhyzmc,'mbZhyzContent','pydm')"/>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">拼音代码</span>
                            <input class="zui-input wh185 no-disabled" placeholder="请输入模板名称" type="text"
                                   v-model="mbZhyzContent.pydm"
                                   @keydown="nextFocus($event)" data-notEmpty="false" disabled="disabled"/>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">医嘱类型</span>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="zhyzlx_tran"
                                          :index="mbZhyzContent.zhyzlx"
                                          :val="mbZhyzContent.zhyzlx" :name="'mbZhyzContent.zhyzlx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">拥&nbsp;&nbsp;有&nbsp;&nbsp;者</span>
                            <select-input @change-data="resultChange" :not_empty="false" :search="true"
                                          :child="rybmList" :index="'ryxm'" :index_val="'rybm'" :val="mbZhyzContent.yyz"
                                          :name="'mbZhyzContent.yyz'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">拥有科室</span>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="ksbmList" :index="'ksmc'" :search="true" :index_val="'ksbm'" :val="mbZhyzContent.yyks"
                                          :name="'mbZhyzContent.yyks'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">药&emsp;&emsp;房</span>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="yfList" :index="'yfmc'" :index_val="'yfbm'" :val="mbZhyzContent.yfbm"
                                          :name="'mbZhyzContent.yfbm'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">处方类型</span>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="cflxList" :index="'cflxmc'" :index_val="'cflxbm'" :val="mbZhyzContent.cflxbm"
                                          :name="'mbZhyzContent.cflxbm'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">是否药品</span>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="istrue_tran"
                                          :index="mbZhyzContent.ypbz"
                                          :val="mbZhyzContent.ypbz" :name="'mbZhyzContent.ypbz'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">是否草药</span>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="istrue_tran"
                                          :index="mbZhyzContent.sfcy"
                                          :val="mbZhyzContent.sfcy" :name="'mbZhyzContent.sfcy'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14" >治&emsp;&emsp;法</span>
                            <input class="zui-input wh185 " placeholder="请输入治法" type="text"
                                   v-model="mbZhyzContent.zf"
                                   @keydown="nextFocus($event)" data-notEmpty="false"/>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20  padd-b-10" >
                            <span class="padd-r-5 whiteSpace ft-14">主&emsp;&emsp;治</span>
                            <input class="zui-input wh185 " placeholder="请输入请输入主治" type="text"
                                   v-model="mbZhyzContent.zz"
                                   @keyup.13="saveMb()" data-notEmpty="false"/>
                        </div>
                    </div>
                </div>
            </model>

        </div>

        <!--毒麻精神类处方代办人信息弹窗-->
        <div id="dbrPop" class="pophide" :class="{'show':popShow}">
            <div class="pop-width160 bcsz-layer"
                 style="height: max-content;width:500px;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative">
                <div class="layui-layer-title " style="padding: 0 0 0 20px;">
                    <span class="dzcf-fl" v-text="popTitle"></span>
                    <span class="dzcf-fr closex ti-close" @click="cancel"></span>
                </div>
                <div class="layui-layer-content">
                    <div class=" layui-mad layui-height">
                        <div class="flex-container  flex-align-c flex-wrap-w">
                            <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                                <label class="padd-r-5">姓&emsp;&emsp;名</label>
                                <input class="zui-input wh150" placeholder="请输入姓名" type="text"
                                       v-model="smxx.xm"
                                       @keydown="nextFocus($event)" data-notEmpty="false" disabled/>
                            </div>
                            <div class="flex-container flex-jus-c flex-align-c padd-b-10" >
                                <label class="padd-r-5">联系电话</label>
                                <input class="zui-input wh150" placeholder="请输入联系电话" type="text"
                                       v-model="smxx.lxdh" @keydown="nextFocus($event)"
                                       onkeyup="value=value.replace(/[^\d]/g,'')" data-notEmpty="false"/>
                            </div>
                            <div class="flex-container flex-jus-c flex-align-c padd-b-10" >
                                <label class="padd-r-5" style="left: 35px;">身份证号</label>
                                <input class="zui-input wh200 " placeholder="请输入身份证号" type="text"
                                       v-model="smxx.sfzh"
                                       @keydown="nextFocus($event)" data-notEmpty="false"/>
                            </div>

                        </div>

                    </div>
                </div>
                <div class="zui-row buttonbox">
                    <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">取消</button>
                    <button v-waves class="zui-btn btn-primary xmzb-db margin-r-15 height36" @click="saveDbrxx">确定</button>
                </div>
            </div>
        </div>

        <div id="yzd" class="flex-container flex-dir-c hide" style="height: 100%;">
            <div class="toolMenu_yzd printHide">
                <div @click="long(0)" :class="{'yzd_select': which==0}">长&nbsp;&nbsp;期</div>
                <div @click="short(1)" :class="{'yzd_select': which==1}">临&nbsp;&nbsp;时</div>
                <!--<button @click="doPrint(false)">打印</button>-->
                <!--<button @click="doPrint(true)">续打</button>-->
            </div>

            <div v-cloak class="cqyzd printHide" v-if="isShow">
                <div class="yzdTitle" >长期医嘱单</div>
                <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
                    <div>
                        <span>科别:</span>
                        <span>{{BrxxJson.ryksmc}}</span>
                    </div>
                    <div>
                        <span>床号:</span>
                        <span>{{BrxxJson.rycwbh}}</span>
                    </div>
                    <div>
                        <span>姓名:</span>
                        <span>{{BrxxJson.brxm}}</span>
                    </div>
                    <div>
                        <span>性别:</span>
                        <span>{{BrxxJson.brxb}}</span>
                    </div>
                    <div>
                        <span>年龄:</span>
                        <span>{{BrxxJson.nl}}{{BrxxJson.nldw}}</span>
                    </div>
                    <div>
                        <span>住院号:</span>
                        <span>{{BrxxJson.zyh}}</span>
                    </div>
                </div>

                <div class="yzd-table">
                    <table cellspacing="0" cellpadding="0">
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th colspan="2">开始</th>
                            <th rowspan="2">执行<br>时间</th>
                            <th rowspan="2" style="width: 320px">长期医嘱</th>
                            <th colspan="2">签名</th>
                            <th colspan="2">停止</th>
                            <th rowspan="2">停止<br>执行<br>时间</th>
                            <th colspan="2">签名</th>
                        </tr>
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th>日<br>月</th>
                            <th>时<br>间</th>
                            <th>医师</th>
                            <th>护士</th>
                            <th>日<br>月</th>
                            <th>时<br>间</th>
                            <th>医师</th>
                            <th>护士</th>
                        </tr>
                        <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                            @click="goPrint($index)">
                            <td v-text="sameDate('ksrq', $index, 'ry')"></td>
                            <td v-text="sameDate('ksrq', $index, 'sj')"></td>
                            <td v-text="sameDate('zxsj', $index, 'sj')"></td>
                            <td class="flex-container border-left-top-none">
                                <span v-if="is_csqx.N03003200142 != '1'"  class="yzd-name" v-text="item.xmmc"></span>
                                <span v-if="is_csqx.N03003200142 != '1'" :class="[{'sameStart': sameSE($index) == 'start'}, {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                                <span v-if="is_csqx.N03003200142 == '1'" :class="[{'N03003200142-sameStart': sameSE($index) == 'start'}, {'N03003200142-sameEnd': sameSE($index) == 'end'},{'N03003200142-same': sameSE($index) == 'all'}]"></span>
                                <div v-if="is_csqx.N03003200142 == '1'" style="width: 100%;height:  100%; ">
                                    <div v-if="!item.end || item.end != 'plus'" class="text-left N0300320014201">
                                        {{item.xmmc}}<span class="float-right" v-if="item.plusXsyl == 'xsyl'">{{item.sl}}&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                    </div>
                                    <div v-if="item.plusYyff == 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300320014202">
                                        <span v-if="item.ypbz == '1'"> 单次剂量：</span>{{item.dcjl}}&emsp;{{item.jldwmc}}
                                    </div>
                                    <div v-if="item.plusYyff != 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300320014202">
                                        <span v-if="item.ypbz == '1'"> 用法：</span>{{item.yyffmc}}&emsp;{{item.sysd}}{{item.sysddw}}&emsp;{{item.pcmc}}<span class="text-right">{{item.yysm}}</span>
                                    </div>
                                    <div v-if="item.end && item.end == 'plus'" class="text-left N0300320014202">
                                        用法：{{item.yyffmc}}&emsp;{{item.sysd}}{{item.sysddw}}&emsp;{{item.pcmc}}
                                    </div>
                                </div>
                               <p v-if="is_csqx.N03003200142 != '1'">
                                   <span class="yzd-way" v-show="isShowItem($index)" v-text="item.yyffmc"></span>
                                   <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                   <span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}</span>
                               </p>
                            </td>
                            <td >
                                <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                <span v-if="is_csqx.N03003200125 == '1'" >{{item.ysqmxm}}</span>
                            </td>
                            <td >
                                <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                <span v-if="is_csqx.N03003200125 == '1'" >{{item.zxhsxm}}</span>
                            </td>
                            <td >
                                {{is_csqx.N03003200139=='1'? sameDate('ystzsj', $index, 'ry'):''}}
                            </td>
                            <td >
                                {{is_csqx.N03003200139=='1'? sameDate('ystzsj', $index, 'sj'):''}}
                            </td>
                            <td >
                                {{is_csqx.N03003200139=='1'? sameDate('hstzsj', $index, 'sj'):''}}
                            </td>
                            <td >
                                <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                <span v-if="is_csqx.N03003200125 == '1'" >{{item.tzysxm}}</span>
                            </td>
                            <td>
                                <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                <span v-if="is_csqx.N03003200125 == '1'" >{{item.tzhsxm}}</span>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                    <div class="yzd-ysInfo">
                        <div style="margin-right: 120px;">
                            <span>主管医生:</span>
                            <span>{{BrxxJson.zyysxm}}</span>
                        </div>
                        <div>
                            <span>护士:</span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="cqPrint">
                <!--<transition name="pop-fade">-->
                <!--:style="index|compuGd()"-->
                <div v-show="isShow"  class="background-f">
                    <div  :style="index|compuGd"  class="popCenter" v-for="(itemList, index) in list" >
                        <div class="yzdTitle" :class="{'goPrintHide':cssFun(index)}">长期医嘱单</div>
                        <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                            <div>
                                <span>科别:</span>
                                <span>{{BrxxJson.ryksmc}}</span>
                            </div>
                            <div>
                                <span>床号:</span>
                                <span>{{BrxxJson.rycwbh}}</span>
                            </div>
                            <div>
                                <span>姓名:</span>
                                <span>{{BrxxJson.brxm}}</span>
                            </div>
                            <div>
                                <span>性别:</span>
                                <span>{{BrxxJson.brxb}}</span>
                            </div>
                            <div>
                                <span>年龄:</span>
                                <span>{{BrxxJson.nl}}{{BrxxJson.nldw}}</span>
                            </div>
                            <div>
                                <span>住院号:</span>
                                <span>{{BrxxJson.zyh}}</span>
                            </div>
                        </div>

                        <div class="yzd-table">
                            <table cellspacing="0" cellpadding="0">
                                <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                    <th colspan="2">开始</th>
                                    <th rowspan="2">执行<br>时间</th>
                                    <th rowspan="2" style="width: 320px">长期医嘱</th>
                                    <th colspan="2">签名</th>
                                    <th colspan="2">停止</th>
                                    <th rowspan="2">停止<br>执行<br>时间</th>
                                    <th colspan="2">签名</th>
                                </tr>
                                <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                    <th>日<br>月</th>
                                    <th>时<br>间</th>
                                    <th>医师</th>
                                    <th>护士</th>
                                    <th>日<br>月</th>
                                    <th>时<br>间</th>
                                    <th>医师</th>
                                    <th>护士</th>
                                </tr>
                                <tr v-for="(item, $index) in itemList" :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]">
                                    <td v-text="sameDate('ksrq', $index, index, 'ry')"></td>
                                    <td v-text="sameDate('ksrq', $index, index, 'sj')"></td>
                                    <td v-text="sameDate('zxsj', $index, index, 'sj')"></td>
                                    <td class="flex-container border-left-top-none">
                                        <span v-if="is_csqx.N03003200142 != '1'"  class="yzd-name" v-text="item.xmmc"></span>
                                        <span v-if="is_csqx.N03003200142 != '1'" :class="[{'sameStart': sameSE($index) == 'start'}, {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                                        <span v-if="is_csqx.N03003200142 == '1'" :class="[{'N03003200142-sameStart': sameSE($index) == 'start'}, {'N03003200142-sameEnd': sameSE($index) == 'end'},{'N03003200142-same': sameSE($index) == 'all'}]"></span>
                                        <div v-if="is_csqx.N03003200142 == '1'" style="width: 100%;height:  100%; ">
                                            <div v-if="!item.end || item.end != 'plus'" class="text-left N0300320014201">
                                                {{item.xmmc}}<span class="float-right" v-if="item.plusXsyl == 'xsyl'">{{item.sl}}&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                            </div>
                                            <div v-if="item.plusYyff == 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300320014202">
                                                <span v-if="item.ypbz == '1'"> 单次剂量：</span>{{item.dcjl}}&emsp;{{item.jldwmc}}
                                            </div>
                                            <div v-if="item.plusYyff != 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300320014202">
                                                <span v-if="item.ypbz == '1'"> 用法：</span>{{item.yyffmc}}&emsp;{{item.sysd}}{{item.sysddw}}&emsp;{{item.pcmc}}<span class="text-right">{{item.yysm}}</span>
                                            </div>
                                            <div v-if="item.end && item.end == 'plus'" class="text-left N0300320014202">
                                                用法：{{item.yyffmc}}&emsp;{{item.sysd}}{{item.sysddw}}&emsp;{{item.pcmc}}
                                            </div>
                                        </div>
                                        <p v-if="is_csqx.N03003200142 != '1'">
                                            <span class="yzd-way" v-show="isShowItem($index)" v-text="item.yyffmc"></span>
                                            <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                            <span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}</span>
                                        </p>
                                    </td>
                                    <td >
                                        <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                        <span v-if="is_csqx.N03003200125 == '1'" >{{item.ysqmxm}}</span>
                                    </td>
                                    <td >
                                        <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                        <span v-if="is_csqx.N03003200125 == '1'" >{{item.zxhsxm}}</span>
                                    </td>
                                    <td >
                                        {{is_csqx.N03003200139=='1'? sameDate('ystzsj', $index,index, 'ry'):''}}
                                    </td>
                                    <td >
                                        {{is_csqx.N03003200139=='1'? sameDate('ystzsj', $index,index, 'sj'):''}}
                                    </td>
                                    <td >
                                        {{is_csqx.N03003200139=='1'? sameDate('hstzsj', $index,index, 'sj'):''}}
                                    </td>
                                    <td >
                                        <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                        <span v-if="is_csqx.N03003200125 == '1'" >{{item.tzysxm}}</span>
                                    </td>
                                    <td>
                                        <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                        <span v-if="is_csqx.N03003200125 == '1'" >{{item.tzhsxm}}</span>
                                    </td>

                                </tr>
                            </table>
                        </div>

                        <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                            <div class="yzd-ysInfo">
                                <div style="margin-right: 120px;">
                                    <span>主管医生:</span>
                                    <span>{{BrxxJson.zyysxm}}</span>
                                </div>
                                <div>
                                    <span>护士:</span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                        <div :class="{'goPrintHide':cssFun(index)}" class="text-center font-14" v-text="'第  ' + (index + 1) + '  页'"></div>
                    </div>
                </div>
                <!--</transition>-->
            </div>

            <div v-cloak class="lsyzd printHide" v-if="isShow">
                <div class="yzdTitle" >临时医嘱单</div>
                <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
                    <div>
                        <span>科别:</span>
                        <span>{{BrxxJson.ryksmc}}</span>
                    </div>
                    <div>
                        <span>床号:</span>
                        <span>{{BrxxJson.rycwbh}}</span>
                    </div>
                    <div>
                        <span>姓名:</span>
                        <span>{{BrxxJson.brxm}}</span>
                    </div>
                    <div>
                        <span>性别:</span>
                        <span>{{BrxxJson.brxb}}</span>
                    </div>
                    <div>
                        <span>年龄:</span>
                        <span>{{BrxxJson.nl}}{{BrxxJson.nldw}}</span>
                    </div>
                    <div>
                        <span>住院号:</span>
                        <span>{{BrxxJson.zyh}}</span>
                    </div>
                </div>

                <div class="yzd-table">
                    <table cellspacing="0" cellpadding="0">
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th colspan="2">吩咐时间</th>
                            <th rowspan="2" style="width: 445px">临时医嘱</th>
                            <th rowspan="2">医师签名</th>
                            <th rowspan="2">执行<br>时间</th>
                            <th rowspan="2">执行者签名</th>
                        </tr>
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th>日<br>月</th>
                            <th>时<br>间</th>
                        </tr>
                        <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                            @click="goPrint($index)">
                            <td v-text="sameDate('ksrq', $index, 'ry')"></td>
                            <td v-text="sameDate('ksrq', $index, 'sj')"></td>
                            <td class="flex-container border-left-top-none">
                                <span v-if="is_csqx.N03003200142 != '1'" class="yzd-name" v-text="item.xmmc"></span>
                                <span v-if="is_csqx.N03003200142 != '1'" :class="[{'sameStart': sameSE($index) == 'start'},  {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                                <span v-if="is_csqx.N03003200142 == '1'" :class="[{'N03003200142-sameStart': sameSE($index) == 'start'},  {'N03003200142-sameEnd': sameSE($index) == 'end'},{'N03003200142-same': sameSE($index) == 'all'}]"></span>
                                <div v-if="is_csqx.N03003200142 == '1'" style="width: 100%;height:  100%; ">
                                    <div v-if="(!item.end || item.end != 'plus') && item.doubleLine != '1'" class="text-left N0300320014201">
                                        {{item.xmmc}}<span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                    </div>
                                    <div v-if="(!item.end || item.end != 'plus') && item.doubleLine == '1'" class="text-left N0300320014203">
                                        {{item.xmmc}}<span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                    </div>
                                    <div v-if="item.plusYyff == 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300320014202">
                                        <span v-if="item.ypbz == '1'"> 单次剂量：</span>{{item.dcjl}}&emsp;{{item.jldwmc}}
                                    </div>
                                    <div v-if="item.plusYyff != 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300320014202">
                                        <span v-if="item.ypbz == '1'">用法：</span>{{item.yyffmc}}&emsp;{{item.sysd}}{{item.sysddw}}&emsp;{{item.pcmc}}<span class="text-right">{{item.yysm}}</span>
                                        <span class="padd-l-20" style="width: auto" v-show="item.psjg != '无'">(&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;)</span>
                                        <!--<span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>-->
                                    </div>
                                    <div v-if="item.end && item.end == 'plus'" class="text-left N0300320014202">
                                        用法：{{item.yyffmc}}&emsp;{{item.sysd}}{{item.sysddw}}&emsp;{{item.pcmc}}
                                    </div>
                                </div>
                               <p v-if="is_csqx.N03003200142 != '1'">
                                   <span class="yzd-way"  v-text="item.yyffmc"></span>
                                   <span class="padd-l-20" style="width: auto" v-show="item.psjg != '无'">(&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;)</span>
                                   <span class="yzd-sm"  v-text="item.yysm"></span>
                                   <span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}</span>
                               </p>
                            </td>
                            <td  >
                                <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                <span v-if="is_csqx.N03003200125 == '1'" >{{item.ysqmxm}}</span>
                            </td>
                            <td >
                                {{is_csqx.N03003200139=='1'? fDate(item.zxsj,'shortY'):''}}
                            </td>
                            <td >
                                <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                <span v-if="is_csqx.N03003200125 == '1'" >{{item.zxhsxm}}</span>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                    <div class="yzd-ysInfo">
                        <div class="padd-r-40" style="margin-right: 120px">
                            <span>主管医生:</span>
                            <span></span>
                        </div>
                        <div>
                            <span>护士:</span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lsPrint">
                    <div v-show="isShow" class="background-f">
                        <div class="popCenter " :style="index|compuGd"   v-for="(itemList, index) in list" >
                            <div class="yzdTitle" :class="{'goPrintHide':cssFun(index)}">临时医嘱单</div>
                            <!--<div class="yzdTitle" :class="{'goPrintHide': isGoPrint && pagePrint == index}">临时医嘱单</div>-->
                            <div class="yzd-brInfo"  :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                <div>
                                    <span>科别:</span>
                                    <span>{{BrxxJson.ryksmc}}</span>
                                </div>
                                <div>
                                    <span>床号:</span>
                                    <span>{{BrxxJson.rycwbh}}</span>
                                </div>
                                <div>
                                    <span>姓名:</span>
                                    <span>{{BrxxJson.brxm}}</span>
                                </div>
                                <div>
                                    <span>性别:</span>
                                    <span>{{BrxxJson.brxb}}</span>
                                </div>
                                <div>
                                    <span>年龄:</span>
                                    <span>{{BrxxJson.nl}}{{BrxxJson.nldw}}</span>
                                </div>
                                <div>
                                    <span>住院号:</span>
                                    <span>{{BrxxJson.zyh}}</span>
                                </div>
                            </div>

                            <div class="yzd-table">
                                <table cellspacing="0" cellpadding="0">
                                    <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                        <th colspan="2">吩咐时间</th>
                                        <th rowspan="2" style="width: 445px">临时医嘱</th>
                                        <th rowspan="2">医师签名</th>
                                        <th rowspan="2">执行<br>时间</th>
                                        <th rowspan="2">执行者签名</th>
                                    </tr>
                                    <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                        <th>日<br>月</th>
                                        <th>时<br>间</th>
                                    </tr>
                                    <tr v-for="(item, $index) in itemList" :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]">
                                        <td v-text="sameDate('ksrq', $index, index, 'ry')"></td>
                                        <td v-text="sameDate('ksrq', $index, index, 'sj')"></td>
                                        <td class="flex-container border-left-top-none">
                                            <span  v-if="is_csqx.N03003200142 != '1'" class="yzd-name" v-text="item.xmmc"></span>
                                            <span  v-if="is_csqx.N03003200142 != '1'"  :class="[{'sameStart': sameSE($index, index) == 'start'}, {'sameEnd': sameSE($index, index) == 'end'},{'same': sameSE($index, index) == 'all'}]"></span>
                                            <span v-if="is_csqx.N03003200142 == '1'" :class="[{'N03003200142-sameStart': sameSE($index,index) == 'start'},{'N03003200142-sameEnd': sameSE($index,index) == 'end'},{'N03003200142-same': sameSE($index,index) == 'all'}]"></span>
                                            <div v-if="is_csqx.N03003200142 == '1'" style="width: 100%;height:  100%; ">
                                                <div v-if="(!item.end || item.end != 'plus') && item.doubleLine != '1'" class="text-left N0300320014201">
                                                    {{item.xmmc}}<span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                                </div>
                                                <div v-if="(!item.end || item.end != 'plus') && item.doubleLine == '1'" class="text-left N0300320014203">
                                                    {{item.xmmc}}<span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                                </div>
                                                <div v-if="item.plusYyff == 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300320014202">
                                                    <span v-if="item.ypbz == '1'"> 单次剂量：</span>{{item.dcjl}}&emsp;{{item.jldwmc}}
                                                </div>
                                                <div v-if="item.plusYyff != 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300320014202">
                                                    <span v-if="item.ypbz == '1'">用法：</span>{{item.yyffmc}}&emsp;{{item.sysd}}{{item.sysddw}}&emsp;{{item.pcmc}}<span class="text-right">{{item.yysm}}</span>
                                                    <span class="padd-l-20" style="width: auto" v-show="item.psjg != '无'">(&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;)</span>
                                                </div>
                                                <div v-if="item.end && item.end == 'plus'" class="text-left N0300320014202">
                                                    用法：{{item.yyffmc}}&emsp;{{item.sysd}}{{item.sysddw}}&emsp;{{item.pcmc}}
                                                </div>
                                            </div>
                                        <p v-if="is_csqx.N03003200142 != '1'">
                                            <span class="yzd-way"  v-text="item.yyffmc"></span>
                                            <span style="margin-left: 20px;width: auto" v-show="item.psjg != '无'">(&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;)</span>
                                            <span class="yzd-sm" v-text="item.yysm"></span>
                                            <span v-if="is_csqx.N03003200141 == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}</span>
                                        </p>
                                        </td>
                                        <td  >
                                            <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                            <span v-if="is_csqx.N03003200125 == '1'" >{{item.ysqmxm}}</span>
                                        </td>
                                        <td >
                                            {{is_csqx.N03003200139=='1'? fDate(item.zxsj,'shortY'):''}}
                                        </td>
                                        <td  >
                                            <img v-if="is_csqx.N03003200125 == '0'" :src="item.src" />
                                            <span v-if="is_csqx.N03003200125 == '1'" >{{item.zxhsxm}}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                                <div class="yzd-ysInfo">
                                    <div class="padd-r-40" style="margin-right: 120px">
                                        <span>主管医生:</span>
                                        <span></span>
                                    </div>
                                    <div>
                                        <span>护士:</span>
                                        <span></span>
                                    </div>
                                </div>
                            </div>
                            <div :class="{'goPrintHide':cssFun(index)}" class="text-center" v-text="'第  ' + (index + 1) + '  页'"></div>
                        </div>
                    </div>
            </div>
        </div>
        </div>

    <!--新增药品-->
    <div class="side-form  pop-width pop-805 printHide" v-cloak :class="{'ng-hide':numOne==1}" id="brzcList00" role="form">
        <div class="fyxm-side-top" style="display: block;">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="flex-align-c flex-container">
            <div class="flex-align-c flex-container margin-top-10 margin-l-20">
                <label class="whiteSpace margin-r-5 ft-14">医嘱类型</label>
                <select-input class="wh80" @change-data="resultChange" :not_empty="false" :child="yzfl_tran" :index="popContent.lxyz"
                              :val="popContent.lxyz" :name="'popContent.lxyz'">
                </select-input>
            </div>
            <div class="flex-align-c flex-container  margin-top-10 margin-l-20">
                <label :id="setZyf" class="whiteSpace margin-r-5 ft-14">药房选择</label>
                <select-input class="wh120" @change-data="resultChangeYf" :not_empty="false" :child="zctDate"
                              :index="'yfmc'" :index_val="'yfbm'" :val="popContent.yfbm" :name="'popContent.yfbm'">
                </select-input>
            </div>
            <div class="flex  margin-top-10 margin-l-20">
                <button v-waves class="tong-btn btn-parmary-b  icon-mb paddr-r5" @click="showYzTem">医嘱模板</button>
                <button v-waves class="tong-btn btn-parmary-b  icon-mb paddr-r5" @click="fzzyyz">复制中药医嘱</button>
            </div>
        </div>
        <div class="ksys-side hzgl-not-pad " >
            <div class="header-hzgl ">
                <div class="tab-card-body position">
                    <!--<p class="zcyImg"></p>-->
                    <span class="dzcf-rps"><img src="/newzui/pub/image/<EMAIL>" /></span>
                    <div class="flex-container flex-align-c flex-wrap-w zui-form over-auto zvyHeight">
                        <div style="width: 20%" class=" hzgl-width margin-r-10" v-for="(item,$index) in zcyList">
                            <div class=" zcy-width margin-r-5  hzgl-sc margin-b-10">
                                <select-input @change-data="resultChange_yyff" :not_empty="false" :child="yyffList"
                                              :index="'yyffmc'" :index_val="'yyffbm'" :val="item.yyff" :name="'zcyList.'+$index+'.yyffmc'"
                                              :index_mc="'yyffmc'">
                                </select-input>
                            </div>
                            <div class=" zcy-width margin-b-10 margin-r-5 position">
                                <i class="hzgl-shanchu" @click="remove($index)"></i>
                                <input class="zui-input " v-model="item.xmmc" @keyup="changeDown($index,$event,'xmmc',$event.target.value),autoAdd($event, $index)"
                                       @input="searching(false,$index,'xmmc', $event.target.value)" :id="'xmsr_' + $index" />
                                <search-table :message="searchCon" :selected="selSearch" :page="page" :them="them"
                                              :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne"></search-table>
                            </div>
                            <div class=" zcy-width position  margin-b-10">
                                <input class="zui-input " @input="dcjl('dcjl',$index,$event.target.value)"
                                       @mousewheel.prevent :tabindex="$index" type="number" @keydown="nextAdd($event,$index)"
                                       v-model="item.dcjl" placeholder="请输入药品剂量" /><span class="cm">{{item.jldwmc}}</span>
                            </div>
                        </div>
                        <p class="add-yp-hzgl" @click="add()"></p>
                    </div>
                </div>
                <div class="grid-box zui-form">
                    <div class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15 margin-top20">
                        <label class="zui-form-label">处方类型</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="ypInfo.yplx" readonly="readonly" placeholder="请输入处方类型"
                                   @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15 margin-top20">
                        <label class="zui-form-label">剂数</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" type="number" v-model="ypInfo.ypjs" placeholder="请输入处方类型"
                                   @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15 margin-top20">
                        <label class="zui-form-label">用药说明</label>
                        <!--<div class="zui-input-inline">-->
                        <!--<input class="zui-input" v-model="ypInfo.ypyf" placeholder="请在此填写用药说明"-->
                        <!--@keydown="nextFocus($event)">-->
                        <!--</div>-->
                        <select-input @changevalue="ypyfFun" :phd="'请在此填写用药说明'" @change-data="resultChangeMb"
                                      :not_empty="false" :child="jsonList" :index="'yysmmc'" :index_val="'yysmbm'" :val="ypInfo.yysmbm"
                                      :name="'ypInfo.yysmbm'" :index_mc="'ypyf'" :search="true">
                        </select-input>
                    </div>
                </div>
                <div class="grid-box zui-form">
                    <div class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15">
                        <label class="zui-form-label">备注说明</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="ypInfo.bzsm"  placeholder="请输入备注说明"
                                   @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div v-if="is_csqx.N03003200123 == '1' " class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15">
                        <label class="zui-form-label">汤头</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="ypInfo.zytt" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15">
                        <label class="zui-form-label">主病</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="ypInfo.zyzh">
                        </div>
                    </div>
                    <div class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15">
                        <label class="zui-form-label">主证</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="ypInfo.zyzf" @keydown="saveBc($event,'confirms')">
                        </div>
                    </div>
                    <!--<div class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15">
                        <label class="zui-form-label">方剂名称</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="ypInfo.fjmc" readonly="readonly" placeholder="请输入方剂名称"
                                @keydown="nextFocus($event)">
                        </div>
                    </div>-->
                    <!--@yqq 中医只保存主病和主症<div class="col-xxl-4 zui-inline hzgl-not-margin margin-b-15">
                        <label class="zui-form-label">中医诊断</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="ypInfo.zyzd">
                        </div>-->
                </div>
            </div>

            <div class="  printHide flex-container flex-jus-sb flex-align-c ksys-btn" style="position: absolute;bottom: 0;width: 100%;right: 0;height: 60px;justify-content: space-between">
                <!--<div class="left">-->
                <!--<span class="hzgl-tj">调剂：<span class="yellow">0mg</span></span>-->
                <!--<span class="hzgl-tj">科别：<span class="yellow" v-text="brlt.ksmc"></span></span>-->
                <!--<span class="hzgl-tj">医师：<span class="yellow" v-text="brlt.zyysxm"></span></span>-->
                <!--<span class="hzgl-tj">总价：<span class="yellow">{{sum}}元</span></span>-->
                <!--</div>-->
                <!--<button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>-->
                <div class="padd-l-10">
                    科别：{{brlt.ryksmc}} 床号： {{brlt.rycwbh}} 姓名： {{brlt.brxm}} 性别： {{brxb_tran[brlt.brxb]}} 年龄： {{brlt.nl}}{{nldw_tran[brlt.nldw]}} 住院号： {{brlt.zyh}}
                </div>
                <div class="padd-l-10" v-if="!sfxz">
<!--                    <button class="tong-btn btn-parmary btn-parmary-not  yellow-bg" @click="Wf_saveYZ">保存</button>-->
                    <button v-waves class="tong-btn btn-parmary btn-parmary-not  yellow-bg" @click="printZy">打印</button>
                </div>
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="confirms">提交</button>
            </div>
        </div>

    </div>

    <!--复制弹窗-->
        <div class="side-form  pop-width printHide" v-cloak :class="{'ng-hide':num==1}" id="brzcList" role="form" style="width: 970px">
            <div class="fyxm-side-top" style="display: block">
                <span v-text="title"></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="grid-box">
                <div class="flex margin-l-10">
                    <div class="flex margin-b-10 margin-top-10 margin-l-10">
                        <label class="whiteSpace margin-r-5 ft-14">检索</label>
                        <div class="  margin-r-10">
                            <input class="zui-input wh112" placeholder="请输入关键字" v-model="param.parm" type="text" @input="getIndexData()" />
                        </div>
                        <label class="whiteSpace margin-r-5 ft-14">类型</label>
                        <select-input class="wh70 margin-r-10" @change-data="commonResultChange" :not_empty="false" :child="yzlx_tran"
                            :index="param.yzlx" :val="param.yzlx" :name="'param.yzlx'">
                        </select-input>
                        <div class="flex margin-l13 margin-r-10">
                            <input class="zui-input wh112"  v-model="param.beginrq" type="text" id="startTime"  /><span class="margin-r-5 margin-l-5">至</span>
                            <input class="zui-input wh112"  v-model="param.endrq" type="text" id="endTime"  />
                        </div>
                        <label class="whiteSpace margin-r-5 ft-14">下嘱时间</label>
                        <div class="  margin-r-10">
                            <input class="zui-input wh179"  v-model="xzsj" id="xzsj" type="text" />
                        </div>
                        <label class="whiteSpace margin-r-5 ft-14" v-if="title=='医嘱模板'">药品标志</label>
                        <select-input v-if="title=='医嘱模板'" class="wh80 margin-r-10" @change-data="commonResultChange" :not_empty="false" :child="ypbz_tran"
                                      :index="param.yzlx" :val="param.ypbz" :name="'param.ypbz'">
                        </select-input>
                    </div>
                </div>
            </div>
            <div class="ksys-side hzgl-height flex-container">
                <div class="col-x-12 flex-one">
                    <div class="col-x-5 hzgl-wiwidth-one flex-one flex-container" style="width: 23%">
                        <div class="zui-table-view over-auto zui-item " v-cloak style="border:none;padding: 0 10px; ">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                        <tr v-if="isyz">
                                            <th>
                                                <div class="zui-table-cell cell-s">组合医嘱名称</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">医嘱类型</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">拥有者</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">拥有科室</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">处方类型</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">类型</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">治法</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">主治</div>
                                            </th>
                                        </tr>
                                        <tr v-if="!isyz">
                                            <th>
                                                <div class="zui-table-cell cell-m">床位号</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">病人姓名</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">诊断名称</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">住院号</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">入院日期</div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s">入院科室</div>
                                            </th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTable($event),scrollGata($event)">
                                <table class="zui-table table-width50">
                                    <tbody>
                                        <tr @click="getTemMxData($index)" :class="[{'table-hovers':mxIndex==$index}]"
                                            v-if="isyz" :tabindex="$index" v-for="(item, $index) in yzList" class="tableTr2"
                                            @dblclick="dballCheck" ref="list">
                                            <td>
                                                <div class="zui-table-cell cell-s text-over-2" v-text="item.zhyzmc"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="zhyzlx_tran[item.zhyzlx]"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.yyzxm"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.yyksmc"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.cflxmc"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="zhyzBylx_tran[item.lx]"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s text-over-2" v-text="item.zf"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.zz"></div>
                                            </td>
                                        </tr>
                                        <tr @click="getLsTemMxData($index)" v-if="!isyz" :class="[{'table-hovers':mxIndex==$index}]"
                                            @click="getLsTemMxData($index)" v-for="(item, $index) in lsYzList" :key="item.jyxmbm"
                                            @dblclick="dballCheck">
                                            <td>
                                                <div class="zui-table-cell cell-m" v-text="item.rycwbh"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s text-over-2">{{item.brxm}}
                                                    ({{item.nl}}{{nldw_tran[item.nldw]}} ){{brxb_tran[item.brxb]}}</div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.ryzdmc"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.zyh"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="fDate(item.ryrq,'date')"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.ryksmc"></div>
                                            </td>
                                            <!--<td><div class="zui-table-cell cell-s" v-text="item.zf"></div></td>-->
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>
                    <div class="col-x-7 margin-l-10 hzgl-wiwidth-two" style="width: 75.7%">
                        <div class="zui-table-view" id="zuiTable">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                        <tr>

                                            <th class="cell-m">
                                                <input-checkbox @result="reCheckBox" :list="'yzMxList'" :type="'all'"
                                                    :val="isCheckAll">
                                                </input-checkbox>
                                            </th>
                                            <th v-if="!isyz">
                                                <div class="zui-table-cell cell-m"><span>类型</span></div>
                                            </th>
                                            <th v-if="!isyz">
                                                <div class="zui-table-cell cell-s"><span>组号</span></div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-xl text-left"><span>项目名称</span></div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s text-left"><span>药品规格</span></div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s"><span>单次剂量</span></div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s"><span>剂量单位</span></div>
                                            </th>
                                            <th v-if="!isyz">
                                                <div class="zui-table-cell cell-s"><span>频次</span></div>
                                            </th>
                                            <th>
                                                <div class="zui-table-cell cell-s"><span>用药方法</span></div>
                                            </th>
                                            <th v-if="!isyz">
                                                <div class="zui-table-cell cell-s"><span>用量</span></div>
                                            </th>
                                            <th v-if="!isyz">
                                                <div class="zui-table-cell cell-s"><span>输液速度</span></div>
                                            </th>
                                            <th v-if="!isyz">
                                                <div class="zui-table-cell cell-s"><span>速度单位</span></div>
                                            </th>
                                            <th v-if="!isyz">
                                                <div class="zui-table-cell cell-s"><span>医保类别</span></div>
                                            </th>
                                            <th v-if="!isyz">
                                                <div class="zui-table-cell cell-s"><span>用药说明</span></div>
                                            </th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" data-no-attr="true" @scroll="scrollTable">
                                <table class="zui-table table-width50" v-if="yzMxList.length!=0">
                                    <tbody>
                                        <tr :tabindex="$index" @mouseenter="switchIndex('hoverIndex',true,$index)" @dblclick="getZyyz(item)"
                                            @mouseleave="switchIndex()" @click="switchIndex('activeIndex',true,$index)"
                                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                            v-for="(item, $index) in yzMxList">
                                            <td class="cell-m">
                                                <input-checkbox @result="reCheckChange" :list="'yzMxList'" :type="'some'"
                                                    :which="$index" :val="isChecked[$index]">
                                                </input-checkbox>
                                            </td>
                                            <td v-if="!isyz">
                                                <div class="zui-table-cell cell" v-text="yzlx_tran[item.yzlx]"></div>
                                            </td>
                                            <td v-if="!isyz">
                                                <div class="zui-table-cell cell-s" v-text="item.fzh"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-xl text-over-2 text-left title overflow1"
                                                    style="overflow: hidden" v-text="item.xmmc"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s text-left" v-text="item.ypgg"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.dcjl"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.jldwmc"></div>
                                            </td>
                                            <td v-if="!isyz">
                                                <div class="zui-table-cell cell-s" v-text="item.pcmc"></div>
                                            </td>
                                            <td>
                                                <div class="zui-table-cell cell-s" v-text="item.yyffmc"></div>
                                            </td>
                                            <td v-if="!isyz">
                                                <div class="zui-table-cell cell-s" v-text="item.sl"></div>
                                            </td>
                                            <td v-if="!isyz">
                                                <div class="zui-table-cell cell-s" v-text="item.sysd"></div>
                                            </td>
                                            <td v-if="!isyz">
                                                <div class="zui-table-cell cell-s" v-text="item.sysddw"></div>
                                            </td>
                                            <td v-if="!isyz">
                                                <div class="zui-table-cell cell-s" v-text="item.ybtclbmc"></div>
                                            </td>
                                            <td v-if="!isyz">
                                                <div class="zui-table-cell cell-s" v-text="item.yysm"></div>
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>
                                <p v-if="yzMxList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                            </div>
                            <div v-if="yzMxList.length!=0" class="zui-table-fixed table-fixed-l">
                                <!-- 有浮动就加 table-fixed-r -->
                                <div class="zui-table-header">
                                    <table class="zui-table">
                                        <thead>
                                            <tr>
                                                <th class="cell-m">
                                                    <input-checkbox @result="reCheckBox" :list="'yzMxList'" :type="'all'"
                                                        :val="isCheckAll">
                                                    </input-checkbox>
                                                </th>
                                                <th class="cell-m" v-if="!isyz">
                                                    <div cell="cell-2-0" class="zui-table-cell  cell-m"><span>类型</span></div>
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                                <!--@click="hover($index,$event)"-->
                                <div class="zui-table-body" ref="nextTickHeight" @scroll="scrollTableFixed($event)">
                                    <table class="zui-table">
                                        <tbody>
                                            <tr @mouseenter="switchIndex('hoverIndex',true,$index)" @mouseleave="switchIndex()"
                                                @click="switchIndex('activeIndex',true,$index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                                v-for="(item, $index) in yzMxList" class="tableTr2 table-hovers-filexd-l">
                                                <td class="cell-m">
                                                    <input-checkbox @result="reCheckChange" :list="'yzMxList'" :type="'some'"
                                                        :which="$index" :val="isChecked[$index]">
                                                    </input-checkbox>
                                                </td>
                                                <td class="cell-m" v-if="!isyz">
                                                    <div cell="cell-2-0" class="zui-table-cell cell-m">{{yzlx_tran[item.yzlx]}}</div>
                                                </td>
                                            </tr>
                                        </tbody>

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ksys-btn">
                <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
            </div>
        </div>



    <!--复制弹窗-->
    <div class="side-form  pop-width pop-805 printHide" v-cloak :class="{'ng-hide':num==1}" id="brzcList01" role="form">
        <div class="fyxm-side-top" style="display: block">
            <span>中药明细</span>
            <span class="fr closex ti-close" @click="num=1"></span>
        </div>
        <div class="ksys-side hzgl-height flex-container">
            <div class="col-x-12 flex-one">
                <div class="col-x-12 margin-l-10 " >
                    <div class="zui-table-view">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th >
                                        <div class="zui-table-cell cell-m"><span>名称</span></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s"><span>用法</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl text-left"><span>剂量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s text-left"><span>规格</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" data-no-attr="true" @scroll="scrollTable">
                            <table class="zui-table table-width50" v-if="jxMxList.length!=0">
                                <tbody>
                                <tr :tabindex="$index" @mouseenter="switchIndex('hoverIndex',true,$index)"
                                    @mouseleave="switchIndex()" @click="switchIndex('activeIndex',true,$index)"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    v-for="(item, $index) in jxMxList">
                                    <td >
                                        <div class="zui-table-cell cell" v-text="yzlx_tran[item.yzlx]"></div>
                                    </td>
                                    <td >
                                        <div class="zui-table-cell cell-s" v-text="item.fzh"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-xl text-over-2 text-left title overflow1" v-text="item.xmmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-left" v-text="item.ypgg"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <p v-if="jxMxList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="ksys-btn">
            <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="num=1">取消</button>
            <button  v-waves class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
        </div>
    </div>
        <!--检查检验项目-->
        <div id="pop" class="printHide" v-cloak>
            <!--<transition name="pop-fade">-->
            <div class="pophide" :class="{'show':is_csqx.cs003003200112=='1' || is_csqx.cs003003200113=='1' && isShow}"></div>
            <div class="zui-form podrag pop-540 bcsz-layer " :class="{'show':is_csqx.cs003003200112=='1' || is_csqx.cs003003200113=='1' && isShow}" style="height: max-content;padding-bottom: 20px">
                <div class="layui-layer-title ">检查检验诊断</div>
                <span class="layui-layer-setwin">
                    <a href="javascript:" style="color:rgba(255,255,255,0.5);font-size: 26px; margin-top: -15px;"
                        @click="isShow=false">&times;</a>
                </span>
                <div class="layui-layer-content">
                    <div class=" layui-mad layui-height">
                        <div class="grid-box zui-form">
                            <div class="col-xxl-6 flex zui-inline hzgl-not-margin margin-b-15">
                                <label class="zui-form-label">临床诊断</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="lczdJson.lczd" id="pop_lczd" @keydown="nextFocus($event)">
                                </div>
                            </div>
                            <div class="col-xxl-6 flex zui-inline hzgl-not-margin margin-b-15" v-if="jcbz">
                                <label class="zui-form-label">检查描述</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="lczdJson.jcms" id="pop_jcms" @keydown="nextFocus($event)">
                                </div>
                            </div>
                        </div>
                        <div class="grid-box zui-form" v-if="jcbz">
                            <div class="col-xxl-6 flex zui-inline hzgl-not-margin margin-b-15">
                                <label class="zui-form-label">临床症状</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="lczdJson.lczz" id="pop_lczz" @keydown="nextFocus($event)">
                                </div>
                            </div>
                            <div class="col-xxl-6 flex zui-inline hzgl-not-margin margin-b-15" v-if="jcbz">
                                <label class="zui-form-label">检查部位</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="lczdJson.jcbw" id="pop_jcbw" @keydown="saveBc($event,'Wf_save')">
                                </div>
                            </div>
                        </div>
                        <div class="grid-box zui-form" v-if="jybz">
                            <div class="col-xxl-6 flex zui-inline hzgl-not-margin margin-b-15">
                                <label class="zui-form-label">检验标本</label>
                                <input class="zui-input" v-model="lczdJson.jybb" id="pop_jybb" @keydown="nextFocus($event)">

                            </div>
                            <div class="col-xxl-6 flex zui-inline hzgl-not-margin margin-b-15">
                                <label class="zui-form-label">标本说明</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="lczdJson.bbsm" id="pop_bbsm" @keydown="nextFocus($event)">
                                </div>
                            </div>
                        </div>
                        <div class="grid-box zui-form" v-if="jybz">
                            <div class="col-xxl-6 flex zui-inline hzgl-not-margin margin-b-15">
                                <label class="zui-form-label">检验目的</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="lczdJson.jymd" id="pop_jymd" @keydown="saveBc($event,'Wf_save')">
                                </div>
                            </div>
                            <div class="col-xxl-6 flex zui-inline hzgl-not-margin margin-b-15" v-if="jcbz">
                                <label class="zui-form-label">检查目的</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="lczdJson.jcmd" id="pop_jcmd" @keydown="saveBc($event,'Wf_save')">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="zui-row buttonbox">
                    <button v-waves class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
                    <button v-waves class="zui-btn btn-primary table_db_save" @click="Wf_save">保存</button>
                </div>
            </div>
            <!--</transition>-->
        </div>

        <!-- 检查/检验费用模板begin -->
        <div class="side-form  pop-width pop-805 printHide" v-cloak :class="{'ng-hide':!ifShow}" id="jcjyfy" role="jcjyfy"  >
            <div class="fyxm-side-top printHide" style="display:block;">
                <span v-text="title"></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="grid-box printHide">
                <div class="col-xxl-4 flex margin-l-10">
                    <div class="flex margin-b-10 margin-top-10 margin-l-10">
                        <label class="whiteSpace margin-r-5 ft-14">检查检验单</label>
                        <select-input @change-data="resultChangeMb" :not_empty="false" :child="jcjydList" :index="'mbmc'"
                            :index_val="'mbbm'" :val="mbbm" :name="'mbbm'" :index_mc="'mbmc'" :search="true">
                        </select-input>
                    </div>
                </div>
                <div class="col-xxl-4 flex margin-l-10" v-if="N03003200137 == '1'">
                    <div class="flex margin-b-10 margin-top-10 margin-l-10">
                        <label class="whiteSpace margin-r-5 ft-14">是否急诊</label>
                        <select-input @change-data="resultChange" :not_empty="false" :child="istrue_tran" :index="'djxxContent.sfjz'"
                                      :index_val="'djxxContent.sfjz'" :val="djxxContent.sfjz" :name="'djxxContent.sfjz'" :index_mc="'djxxContent.sfjz'" readonly="readonly">
                        </select-input>
                    </div>
                </div>
            </div>
            <div class="ksys-side hzgl-height">
                <div class="col-x-12">
                    <div style="padding-bottom: 45px;">
                        <h1 class="text-center margin-b-10" v-text="djxxContent.mbmc"></h1>
                        <div class="flex-container flex-align-c margin-b-10">
                            <span class="margin-r-20">病人姓名：{{brInfo.brxm}}</span>
                            <span class="margin-r-20">性别：{{brxb_tran[brInfo.brxb]}}</span>
                            <span class="margin-r-20">年龄：<span v-text="brInfo.nl"></span><span v-text="nldw_tran[brInfo.nldw]"></span></span>
                            <span class="margin-r-20">科室：{{brInfo.ryksmc}}</span>
                            <span class="margin-r-20">床位号：{{brInfo.rycwbh}}</span>
                            <span class="margin-r-20">住院号：{{ brInfo.zyh }}</span>
                        </div>
                        <div class="flex-container margin-b-10" v-if="djxxContent.havejcms == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">检查描述</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" v-model="djxxContent.jcms" class="zui-input" />
                            </div>
                        </div>
                        <div class="flex-container margin-b-10" v-if="djxxContent.havelczd == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">临床诊断</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" v-model="djxxContent.lczd" class="zui-input" />
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10" v-if="djxxContent.havelczz == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">临床症状</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" v-model="djxxContent.lczz" class="zui-input" />
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10" v-if="djxxContent.havejcbw == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">检查部位</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" v-model="djxxContent.jcbw" class="zui-input" />
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10" v-if="djxxContent.havejybb == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">检验标本</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" v-model="djxxContent.jybb" class="zui-input" />
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10" v-if="djxxContent.havebbsm == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">标本说明</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" v-model="djxxContent.bbsm" class="zui-input" />
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10" v-if="djxxContent.havejymd == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">检验目的</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" v-model="djxxContent.jymd" class="zui-input" />
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">备注</span>
                            </div>
                            <div class="flex-one" v-html="djxxContent.wxts">
                                <!-- <input type="text" class="zui-input" :value="djxxContent.wxts" /> -->
                            </div>
                        </div>
                        <div v-for="(zh,index) in djxxContent.zhList">
                            <h2 class=" margin-b-10">{{zh.mbxmmc}}</h2>
                            <div class="grid-box">
                                <div v-for="(fymx,fyindex) in zh.mxList" class="col-x-4 flex-container margin-b-10"
                                    style="padding: 0 5px;">
                                    <div v-if="fymx.czbw =='0'"><input type="checkbox" :id="'jcjy-zh'+ index +'-fymx-' + fyindex"
                                            class="h-checkbox" v-model="fymx.fymx">
                                        <label :for="'jcjy-zh'+ index +'-fymx-' + fyindex"></label></div>
                                    <div v-if="fymx.czbw =='1'">
                                        <div><input type="checkbox" v-model="fymx.bwLeft" :id="'jcjy-zh'+ index +'-fymx-' + fyindex +'-left'"
                                                class="h-checkbox">
                                            <label :for="'jcjy-zh'+ index +'-fymx-' + fyindex +'-left'">左</label></div>
                                        <div><input type="checkbox" v-model="fymx.bwRight" :id="'jcjy-zh'+ index +'-fymx-' + fyindex +'-right'"
                                                class="h-checkbox">
                                            <label :for="'jcjy-zh'+ index +'-fymx-' + fyindex +'-right'">右</label></div>
                                    </div>
                                    <div class="flex-one text-over-2 margin-l-5 margin-r-5" v-text="fymx.mxxmmc"></div>
                                </div>
                            </div>
                        </div>
                        <!--<div class="flex-container flex-jus-sp">-->
                            <!--<span>送检医生：{{userName}}</span><span>日期：{{ fDate(new Date().getTime(),"datetime") }}</span>-->
                        <!--</div>-->
                    </div>
                </div>
            </div>
            <div class="ksys-btn printHide">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <!--<button class="zui-btn btn-primary-b xmzb-db" @click="print">打印</button>-->
                <button class="zui-btn btn-primary xmzb-db" @click="add">添加</button>
            </div>
        </div>
        <!-- 检查/检验费用模板 end -->

    <!-- 检查/检验费用模板 打印 begin -->
    <!--<div id="print-jcjyfy" v-cloak :class="{ 'ng-hide': !ifShow }">-->
        <!--<h1 class="text-center margin-b-10" v-text="djxxContent.mbmc"></h1>-->
        <!--<div class="flex-container flex-align-c margin-b-10">-->
            <!--<span class="margin-r-20">病人姓名：{{brInfo.brxm}}</span>-->
            <!--<span class="margin-r-20">性别：{{brxb_tran[brInfo.brxb]}}</span>-->
            <!--<span class="margin-r-20">年龄：<span v-text="brInfo.nl"></span><span v-text="nldw_tran[brInfo.nldw]"></span></span>-->
            <!--<span class="margin-r-20">科室：{{brInfo.ryksmc}}</span>-->
            <!--<span class="margin-r-20">床位号：{{brInfo.rycwbh}}</span>-->
            <!--<span class="margin-r-20">住院号：{{ brInfo.zyh }}</span>-->
        <!--</div>-->
        <!--<div class="flex-container margin-b-10" v-if="djxxContent.havebt == '1'">-->
            <!--<div class="flex-container flex-align-c" style="width: 80px;height: 36px;">-->
                <!--<span class="text-over-2">病史及查体</span>-->
            <!--</div>-->
            <!--<div class="flex-one">-->
                <!--<input type="text" class="zui-input" />-->
            <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container margin-b-10" v-if="djxxContent.havets == '1'">-->
            <!--<div class="flex-container flex-align-c" style="width: 80px;height: 36px;">-->
                <!--<span class="text-over-2">临床诊断</span>-->
            <!--</div>-->
            <!--<div class="flex-one">-->
                <!--<input type="text" class="zui-input" />-->
            <!--</div>-->
        <!--</div>-->
        <!--<div class=" flex-container margin-b-10" v-if="djxxContent.havezd == '1'">-->
            <!--<div class="flex-container flex-align-c" style="width: 80px;height: 36px;">-->
                <!--<span class="text-over-2">摘要</span>-->
            <!--</div>-->
            <!--<div class="flex-one">-->
                <!--<input type="text" class="zui-input" />-->
            <!--</div>-->
        <!--</div>-->
        <!--<div class=" flex-container margin-b-10" v-if="djxxContent.havebz == '1'">-->
            <!--<div class="flex-container flex-align-c" style="width: 80px;height: 36px;">-->
                <!--<span class="text-over-2">备注</span>-->
            <!--</div>-->
            <!--<div class="flex-one" v-html="djxxContent.wxts">-->
                <!--&lt;!&ndash; <input type="text" class="zui-input" :value="djxxContent.wxts" /> &ndash;&gt;-->
            <!--</div>-->
        <!--</div>-->
        <!--<div v-for="(zh,index) in djxxContent.zhList">-->
            <!--<h2 class=" margin-b-10">{{index+1}}、{{zh.mbxmmc}}</h2>-->
            <!--<div class="grid-box">-->
                <!--<div v-for="(fymx,fyindex) in zh.mxList" class="col-x-4 flex-container margin-b-10" style="padding: 0 5px;">-->
                    <!--<div v-if="fymx.czbw =='0'"><input type="checkbox" :id="'jcjy-zh'+ index +'-fymx-' + fyindex" class="h-checkbox"-->
                            <!--v-model="fymx.fymx">-->
                        <!--<label :for="'jcjy-zh'+ index +'-fymx-' + fyindex"></label></div>-->
                    <!--<div v-if="fymx.czbw =='1'">-->
                        <!--<div><input type="checkbox" v-model="fymx.bwLeft" :id="'jcjy-zh'+ index +'-fymx-' + fyindex +'-left'"-->
                                <!--class="h-checkbox">-->
                            <!--<label :for="'jcjy-zh'+ index +'-fymx-' + fyindex +'-left'">左</label></div>-->
                        <!--<div><input type="checkbox" v-model="fymx.bwRight" :id="'jcjy-zh'+ index +'-fymx-' + fyindex +'-right'"-->
                                <!--class="h-checkbox">-->
                            <!--<label :for="'jcjy-zh'+ index +'-fymx-' + fyindex +'-right'">右</label></div>-->
                    <!--</div>-->
                    <!--<div class="flex-one text-over-2 margin-l-5 margin-r-5" v-text="fymx.mxxmmc"></div>-->
                <!--</div>-->
            <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-jus-sp">-->
            <!--<span>送检医生：{{userName}}</span><span>日期：{{ fDate(new Date().getTime(),"datetime") }}</span>-->
        <!--</div>-->
    <!--</div>-->
    <!-- 检查/检验费用模板 打印 end -->

    <script type="text/javascript" src="userPage/yzgl.js"></script>
</body>

</html>
