function ONECOLOR(e) {
    if ("[object Array]" === Object.prototype.toString.apply(e)) {
        if ("string" == typeof e[0] && "function" == typeof ONECOLOR[e[0]]) return new ONECOLOR[e[0]](e.slice(1, e.length));
        if (4 === e.length) return new ONECOLOR.RGB(e[0] / 255, e[1] / 255, e[2] / 255, e[3] / 255)
    } else if ("string" == typeof e) {
        var t = e.toLowerCase();
        namedColors[t] && (e = "#" + namedColors[t]), "transparent" === t && (e = "rgba(0,0,0,0)");
        var n = e.match(cssColorRegExp);
        if (n) {
            var r = n[1].toUpperCase(), o = undef(n[8]) ? n[8] : parseFloat(n[8]), a = "H" === r[0],
                i = n[3] ? 100 : a ? 360 : 255, l = n[5] || a ? 100 : 255, c = n[7] || a ? 100 : 255;
            if (undef(ONECOLOR[r])) throw new Error("one.color." + r + " is not installed.");
            return new ONECOLOR[r](parseFloat(n[2]) / i, parseFloat(n[4]) / l, parseFloat(n[6]) / c, o)
        }
        e.length < 6 && (e = e.replace(/^#?([0-9a-f])([0-9a-f])([0-9a-f])$/i, "$1$1$2$2$3$3"));
        var s = e.match(/^#?([0-9a-f][0-9a-f])([0-9a-f][0-9a-f])([0-9a-f][0-9a-f])$/i);
        if (s) return new ONECOLOR.RGB(parseInt(s[1], 16) / 255, parseInt(s[2], 16) / 255, parseInt(s[3], 16) / 255)
    } else if ("object" == typeof e && e.isColor) return e;
    return !1
}

function installColorSpace(e, t, n) {
    function r(e, t) {
        var n = {};
        n[t.toLowerCase()] = new Function("return this.rgb()." + t.toLowerCase() + "();"), ONECOLOR[t].propertyNames.forEach(function (e, r) {
            n[e] = n["black" === e ? "k" : e[0]] = new Function("value", "isDelta", "return this." + t.toLowerCase() + "()." + e + "(value, isDelta);")
        });
        for (var r in n) n.hasOwnProperty(r) && void 0 === ONECOLOR[e].prototype[r] && (ONECOLOR[e].prototype[r] = n[r])
    }

    ONECOLOR[e] = new Function(t.join(","), "if (Object.prototype.toString.apply(" + t[0] + ") === '[object Array]') {" + t.map(function (e, n) {
        return e + "=" + t[0] + "[" + n + "];"
    }).reverse().join("") + "}if (" + t.filter(function (e) {
        return "alpha" !== e
    }).map(function (e) {
        return "isNaN(" + e + ")"
    }).join("||") + '){throw new Error("[' + e + ']: Invalid color: ("+' + t.join('+","+') + '+")");}' + t.map(function (e) {
        return "hue" === e ? "this._hue=hue<0?hue-Math.floor(hue):hue%1" : "alpha" === e ? "this._alpha=(isNaN(alpha)||alpha>1)?1:(alpha<0?0:alpha);" : "this._" + e + "=" + e + "<0?0:(" + e + ">1?1:" + e + ")"
    }).join(";") + ";"), ONECOLOR[e].propertyNames = t;
    var o = ONECOLOR[e].prototype;
    ["valueOf", "hex", "hexa", "css", "cssa"].forEach(function (t) {
        o[t] = o[t] || ("RGB" === e ? o.hex : new Function("return this.rgb()." + t + "();"))
    }), o.isColor = !0, o.equals = function (n, r) {
        undef(r) && (r = 1e-10), n = n[e.toLowerCase()]();
        for (var o = 0; o < t.length; o += 1) if (Math.abs(this["_" + t[o]] - n["_" + t[o]]) > r) return !1;
        return !0
    }, o.toJSON = new Function("return ['" + e + "', " + t.map(function (e) {
        return "this._" + e
    }, this).join(", ") + "];");
    for (var a in n) if (n.hasOwnProperty(a)) {
        var i = a.match(/^from(.*)$/);
        i ? ONECOLOR[i[1].toUpperCase()].prototype[e.toLowerCase()] = n[a] : o[a] = n[a]
    }
    o[e.toLowerCase()] = function () {
        return this
    }, o.toString = new Function('return "[one.color.' + e + ':"+' + t.map(function (e, n) {
        return '" ' + t[n] + '="+this._' + e
    }).join("+") + '+"]";'), t.forEach(function (e, n) {
        o[e] = o["black" === e ? "k" : e[0]] = new Function("value", "isDelta", "if (typeof value === 'undefined') {return this._" + e + ";}if (isDelta) {return new this.constructor(" + t.map(function (t, n) {
            return "this._" + t + (e === t ? "+value" : "")
        }).join(", ") + ");}return new this.constructor(" + t.map(function (t, n) {
            return e === t ? "value" : "this._" + t
        }).join(", ") + ");")
    }), installedColorSpaces.forEach(function (t) {
        r(e, t), r(t, e)
    }), installedColorSpaces.push(e)
}

function gs() {
    var e = this.rgb(), t = .3 * e._red + .59 * e._green + .11 * e._blue;
    return new ONECOLOR.RGB(t, t, t, this._alpha)
}

// function colorNav() {
//     function e() {
//         var e = window.pageYOffset;
//         e >= a && i > e ? (classie.add(r, "fixed"), l && (classie.add(l, "fixed"), classie.add(n, "fixed"))) : (classie.remove(r, "fixed"), l && (classie.remove(l, "fixed"), classie.remove(n, "fixed")))
//     }
//
//     var t = document.querySelector(".js-header"), n = document.querySelector(".js-content"),
//         r = document.querySelector(".js-social"), o = document.querySelector(".js-footer"),
//         a = n.offsetTop + t.offsetHeight, i = document.body.offsetHeight - o.offsetHeight - 720;
//     if (document.querySelector(".color-nav")) var l = document.querySelector(".color-nav");
//     if (document.querySelector(".js-sidebar")) {
//         document.querySelector(".js-sidebar")
//     }
//     window.addEventListener("scroll", e, !1)
// }

function viewport() {
    var e = window, t = "inner";
    return "innerWidth" in window || (t = "client", e = document.documentElement || document.body), {
        width: e[t + "Width"],
        height: e[t + "Height"]
    }
}

function isDesktop() {
    return viewport().width > 768 ? !0 : !1
}

// function colorModal() {
//     for (var e = document.querySelectorAll(".js-export"), t = document.getElementById("modal"), n = !1, r = 0; r < e.length; r++) e[r].addEventListener("click", function (e) {
//         group = e.target.getAttribute("data-export"), console.log(e.target), exportColor(group, "modal"), classie.add(document.body, "modal-active"), n = !0
//     });
//     t.addEventListener("click", function (e) {
//         e.stopPropagation();
//         var r = e.target, o = document.getElementById("modal-close");
//         (n && r === t || r === o) && (classie.remove(document.body, "modal-active"), n = !1)
//     })
// }

function colorExport() {
    var e = location.search, t = e.split("=")[1];
    exportColor(t, "page")
}

function exportColor(e, t) {
    if ("palette" === e) {
        var n = JSON.parse(localStorage.getItem("currentColors")), r = JSON.parse(localStorage.getItem("lockedColors"));
        n && r ? colors = n.concat(r) : n ? colors = n : r ? colors = r : colors = null, buildModal(colors, e, t)
    } else if ("harmony" === e) {
        var o = JSON.parse(localStorage.getItem("harmonyColors"));
        o ? colors = o : colors = null, buildModal(colors, e, t)
    } else "wechat" === e && buildShareModal(e, t)
}

function buildModal(e, t, n) {
    var r = null === localStorage.getItem("currentHarmony") ? "analogous" : localStorage.getItem("currentHarmony");
    for ("modal" === n ? modal = document.getElementById("modal") : "page" === n && (modal = document.getElementById("export")); modal.firstChild;) modal.removeChild(modal.firstChild);
    var o = document.createElement("div");
    o.setAttribute("class", "modal-content");
    var a = document.createElement("h5");
    a.innerHTML = "Export", o.appendChild(a);
    var i = document.createElement("h2");
    if ("palette" === t ? i.innerHTML = "Current palette" : r.match(/^(shades|tints|tones)$/) ? i.innerHTML = "Color " + r : i.innerHTML = r + " colors", o.appendChild(i), "modal" === n) {
        var l = document.createElement("span");
        l.setAttribute("id", "modal-close"), l.innerHTML = "&times;", o.appendChild(l)
    }
    var c = document.createElement("div");
    c.setAttribute("class", "modal-swatches row");
    for (var s = 0; s < e.length; s++) {
        var u = document.createElement("div"), d = document.createElement("div");
        u.style.width = 100 / e.length + "%", d.style.backgroundColor = e[s].hex, u.appendChild(d), c.appendChild(u)
    }
    o.appendChild(c);
    for (var f = ["hex", "rgb", "html", "css", "scss"], h = 0; h < f.length; h++) {
        var p = document.createElement("div"), g = document.createElement("div"), m = document.createElement("h5");
        p.setAttribute("class", "modal-code"), m.innerHTML = f[h], g.appendChild(m);
        for (var v = 0; v < e.length; v++) code = document.createElement("span"), "hex" === f[h] ? code.innerHTML = e[v].hex : "rgb" === f[h] ? code.innerHTML = "rgb(" + e[v].rgb + ")" : "html" === f[h] ? code.innerHTML = 'style="color:' + e[v].hex + ';"' : "css" === f[h] ? code.innerHTML = ".color-" + (v + 1) + " {color: " + e[v].hex + ";}" : "scss" === f[h] && (code.innerHTML = "$color-" + (v + 1) + ": " + e[v].hex + ";"), g.appendChild(code);
        p.appendChild(g), o.appendChild(p)
    }
    modal.appendChild(o)
}

function buildShareModal(e, t) {
    for (; modal.firstChild;) modal.removeChild(modal.firstChild);
    var n = document.createElement("div");
    n.setAttribute("class", "modal-content");
    var r = document.createElement("h5");
    r.innerHTML = "\u5206\u4eab", n.appendChild(r);
    var o = document.createElement("span");
    o.setAttribute("id", "modal-close"), o.innerHTML = "&times;", n.appendChild(o);
    var a = document.createElement("h2");
    a.innerHTML = "\u5fae\u4fe1\u5206\u4eab\u6211\u4eec\u7684\u94fe\u63a5", n.appendChild(a);
    var i = document.createElement("h2");
    i.setAttribute("class", "no-caps"), i.innerHTML = "https://htmlcolorcodes.com/zh/", n.appendChild(i), modal.appendChild(n)
}

function colorListener(e) {
    var t = e.target, n = t.getAttribute("data-hex"), r = t.getAttribute("data-rgb"), o = t.getAttribute("data-hsl"),
        a = {hex: n, rgb: r, hsl: o};
    swapColor(".js-swatch", n), swapColor(".js-hex", n), swapColor(".js-rgb", r), swapColor(".js-hsl", o), localStorage.setItem("currentColor", JSON.stringify(a)), classie.has(t.parentNode.parentNode, "js-palette") || checkColor(a)
}

function checkColor(e) {
    var t = JSON.parse(localStorage.getItem("currentColors")), n = JSON.parse(localStorage.getItem("lockedColors"));
    if (null === t && null === n) pushColor(e); else {
        t && n ? allColors = t.concat(n) : t ? allColors = t : allColors = n;
        for (var r = !1, o = allColors.length - 1; o >= 0; o--) if (-1 !== e.hex.indexOf(allColors[o].hex)) {
            r = !0;
            break
        }
        r || pushColor(e)
    }
}

function pushColor(e) {
    var t = JSON.parse(localStorage.getItem("currentColors")), n = JSON.parse(localStorage.getItem("lockedColors"));
    if ((null === t || t.length < 1) && (null === n || n.length < 6)) {
        var r = [];
        r.push(e), localStorage.setItem("currentColors", JSON.stringify(r)), addColor(e, "prepend", "unlocked")
    } else t && (null === n || n.length < 1) ? t.length < 6 ? (t.unshift(e), localStorage.setItem("currentColors", JSON.stringify(t)), addColor(e, "prepend", "unlocked")) : (t.pop(), t.unshift(e), localStorage.setItem("currentColors", JSON.stringify(t)), removeColor(".js-palette"), removeColor(".js-mobile-palette"), addColor(e, "prepend", "unlocked")) : (totalColors = t.length + n.length, totalColors < 6 && t.length > 0 ? (t.unshift(e), localStorage.setItem("currentColors", JSON.stringify(t)), addColor(e, "prepend", "unlocked")) : totalColors >= 6 && t.length > 0 && (t.pop(), t.unshift(e), localStorage.setItem("currentColors", JSON.stringify(t)), removeColor(".js-palette"), removeColor(".js-mobile-palette"), addColor(e, "prepend", "unlocked")));
    updateCode()
}

function toggleColor(e, t) {
    for (var n = e.target.getAttribute("data-hex"), r = JSON.parse(localStorage.getItem("currentColors")), o = JSON.parse(localStorage.getItem("lockedColors")), a = "locked" === t ? o : r, i = "locked" === t ? "lockedColors" : "currentColors", l = "locked" === t ? r : o, c = "locked" === t ? "currentColors" : "lockedColors", s = l.length - 1; s >= 0; s--) -1 !== n.indexOf(l[s].hex) && (null === a ? (newArray = [], newArray.unshift(l[s]), localStorage.setItem(i, JSON.stringify(newArray))) : a.length <= 5 && (a.unshift(l[s]), localStorage.setItem(i, JSON.stringify(a))), l.splice(s, 1));
    localStorage.setItem(c, JSON.stringify(l));
    for (var u = document.querySelectorAll('span[data-hex="' + n + '"]'), s = 0; s < u.length; s++) updateColor(u[s], t, !0);
    for (var d = document.querySelectorAll('div[data-update="' + n + '"]'), s = 0; s < d.length; s++) updateColor(d[s], t, !1);
    var f = document.querySelector('span[data-update="' + n + '"]');
    updateColor(f, t, !1)
}

function updateColor(e, t, n) {
    var r = "locked" === t ? "unlocked" : "locked";
    n ? (e.removeEventListener("click", function (e) {
        toggleColor(e, t)
    }, !1), e.addEventListener("click", function (e) {
        toggleColor(e, r)
    }, !1), classie.remove(e.parentNode, r), classie.add(e.parentNode, t)) : (classie.remove(e, r), classie.add(e, t))
}

function swapColor(e, t) {
    for (var n = document.querySelectorAll(e), r = 0; r < n.length; r++) ".js-swatch" == e ? n[r].style.backgroundColor = t : n[r].innerHTML = t
}

function addColor(e, t, n) {
    for (var r = document.querySelectorAll(".js-palette"), o = 0; o < r.length; o++) {
        var a = document.createElement("div"), i = document.createElement("div"), l = document.createElement("span"),
            c = document.createElement("h4");
        i.style.backgroundColor = e.hex, i.setAttribute("class", "js-palette-color"), i.setAttribute("data-hex", e.hex), i.setAttribute("data-rgb", e.rgb), i.setAttribute("data-hsl", e.hsl), i.addEventListener("click", function (e) {
            colorListener(e)
        }, !1), l.setAttribute("data-hex", e.hex), c.innerHTML = e.hex, "locked" === n ? (a.setAttribute("class", "locked"), l.setAttribute("class", "color-lock js-lock"), l.addEventListener("click", function (e) {
            toggleColor(e, "unlocked")
        }, !1)) : (a.setAttribute("class", "unlocked"), l.setAttribute("class", "color-lock js-lock"), l.addEventListener("click", function (e) {
            toggleColor(e, "locked")
        }, !1)), a.appendChild(i), a.appendChild(l), a.appendChild(c), "prepend" == t ? r[o].insertBefore(a, r[o].firstChild) : r[o].appendChild(a, r[o].firstChild)
    }
    for (var s = document.querySelectorAll(".js-mobile-palette"), o = 0; o < s.length; o++) {
        var i = document.createElement("div");
        i.style.backgroundColor = e.hex, i.setAttribute("data-update", e.hex), i.setAttribute("class", n), "prepend" == t ? s[o].insertBefore(i, s[o].firstChild) : s[o].appendChild(i, s[o].firstChild)
    }
    if (document.querySelector(".js-color-codes")) {
        var u = document.querySelector(".js-color-codes"),
            d = null === localStorage.getItem("currentCode") ? "css" : localStorage.getItem("currentCode"),
            f = buildCode(d, e, n);
        "prepend" == t ? u.insertBefore(f, u.firstChild) : u.appendChild(f, u.firstChild)
    }
}

function removeColor(e) {
    for (var t = document.querySelectorAll(e), n = 0; n < t.length; n++) {
        colors = t[n].childNodes;
        for (var r = colors.length - 1; r >= 0; r--) if (classie.has(colors[r], "unlocked")) {
            node = colors[r];
            break
        }
        t[n].removeChild(node)
    }
}

function loadColor() {
    var e = JSON.parse(localStorage.getItem("currentColors")), t = JSON.parse(localStorage.getItem("lockedColors"));
    if (null === e && null === t) {
        var n = [{hex: "#DAF7A6", rgb: "218, 247, 166", hsl: "81, 33%, 81%"}, {
            hex: "#FFC300",
            rgb: "255, 195, 0",
            hsl: "46, 100%, 50%"
        }, {hex: "#FF5733", rgb: "255, 87, 51", hsl: "11, 80%, 60%"}, {
            hex: "#C70039",
            rgb: "199, 0, 57",
            hsl: "343, 100%, 39%"
        }, {hex: "#900C3F", rgb: "144, 12, 63", hsl: "337, 92%, 31%"}, {
            hex: "#581845",
            rgb: "88, 24, 69",
            hsl: "318, 73%, 22%"
        }];
        localStorage.setItem("currentColors", JSON.stringify(n)), e = n
    }
    if (e) for (var r = 0; r < e.length; r++) addColor(e[r], "append", "unlocked");
    if (t) for (var r = 0; r < t.length; r++) addColor(t[r], "append", "locked")
}

function mobileColor() {
    for (var e = document.querySelectorAll(".js-mobile-palette"), t = 0; t < e.length; t++) e[t].addEventListener("click", function (e) {
        classie.toggle(e.target.parentNode.parentNode, "open")
    }, !1)
}

// function sidebarColor() {
//     var e = null === localStorage.getItem("currentCode") ? "css" : localStorage.getItem("currentCode");
//     s = document.getElementById(e + "-code"), classie.add(s, "active"), updateCode(), nameCode(e);
//     for (var t = document.querySelectorAll(".js-color-code"), n = 0; n < t.length; n++) t[n].addEventListener("click", function (e) {
//         var t = e.target.getAttribute("data-code"),
//             n = null === localStorage.getItem("currentCode") ? "css" : localStorage.getItem("currentCode"),
//             r = document.getElementById(n + "-code");
//         classie.has(r, "active") && classie.remove(r, "active"), classie.add(e.target, "active"), localStorage.setItem("currentCode", t), updateCode(), nameCode(t)
//     })
// }

function updateCode() {
    for (var e = document.querySelector(".js-color-codes"); e.firstChild;) e.removeChild(e.firstChild);
    var t = null === localStorage.getItem("currentCode") ? "css" : localStorage.getItem("currentCode"),
        n = JSON.parse(localStorage.getItem("currentColors")), r = JSON.parse(localStorage.getItem("lockedColors"));
    n && r ? allColors = n.concat(r) : n ? allColors = n : r && (allColors = r);
    for (var o = 0; o < allColors.length; o++) {
        var a = buildCode(t, o + 1, allColors[o]);
        e.appendChild(a)
    }
}

function buildCode(e, t, n) {
    return newCode = document.createElement("span"), "hex" === e ? newCode.innerHTML = n.hex : "rgb" === e ? newCode.innerHTML = "rgb(" + n.rgb + ")" : "html" === e ? newCode.innerHTML = 'style="color:' + n.hex + ';"' : "css" === e ? newCode.innerHTML = ".color-" + t + " { color:" + n.hex + "; }" : "scss" === e && (newCode.innerHTML = "$color-" + t + ": " + n.hex + ";"), newCode
}

function nameCode(e) {
    var t = document.getElementById("format"), n = document.createElement("span");
    n.setAttribute("class", "triangle gray"), t.innerHTML = e, t.appendChild(n)
}

function colorPicker() {
    var e = JSON.parse(localStorage.getItem("currentColor")), t = null === e ? "#FF5733" : e.hex;
    colorjoe.rgb("js-picker", t, ["currentColor", "hex", ["fields", {
        space: "RGB",
        limit: 255
    }], ["fields", {space: "HSL", limit: [360, 100, 100]}], ["fields", {
        space: "CMYK",
        limit: 100
    }]]).on("change", function (e) {
        rgbR = 255 * e.red(), rgbG = 255 * e.green(), rgbB = 255 * e.blue(), rgbVal = rgbR.toFixed(0) + ", " + rgbG.toFixed(0) + ", " + rgbB.toFixed(0), hslH = 360 * e.hue(), hslS = 100 * e.saturation(), hslL = 100 * e.lightness(), hslVal = hslH.toFixed(0) + ", " + hslS.toFixed(0) + "%, " + hslL.toFixed(0) + "%", swapColor(".js-swatch", e.css()), swapColor(".js-hex", e.hex()), swapColor(".js-rgb", rgbVal), swapColor(".js-hsl", hslVal);
        var t = {hex: e.hex(), rgb: rgbVal, hsl: hslVal};
        localStorage.setItem("currentColor", JSON.stringify(t)), localStorage.setItem("pickerColor", JSON.stringify(t)), document.getElementById("harmonies") && colorHarmony(hslH, e.hex())
    }).update();
    var n = document.querySelector(".currentColor");
    n.addEventListener("click", function () {
        var e = localStorage.getItem("pickerColor");
        checkColor(JSON.parse(e))
    }, !1)
}

function colorHarmony(e, t) {
    swapHarmony(e, t);
    var n = localStorage.getItem("currentHarmony"), r = null === n ? "analogous" : n,
        o = document.getElementById(r).innerHTML, a = new Harmonizer;
    "shades" === r ? colors = a.shades(t, 8) : "tints" === r ? colors = a.tints(t, 8) : "tones" === r ? colors = a.tones(t, 8) : colors = a.harmonize(t, r), nameHarmony(o);
    for (var i = document.getElementById("harmonies"), l = []; i.firstChild;) i.removeChild(i.firstChild);
    for (var c = 0; c < colors.length; c++) {
        var s = one.color(colors[c]);
        rgbR = 255 * s.red(), rgbG = 255 * s.green(), rgbB = 255 * s.blue(), rgbVal = rgbR.toFixed(0) + ", " + rgbG.toFixed(0) + ", " + rgbB.toFixed(0), hslH = 360 * s.hue(), hslS = 100 * s.saturation(), hslL = 100 * s.lightness(), hslVal = hslH.toFixed(0) + ", " + hslS.toFixed(0) + "%, " + hslL.toFixed(0) + "%", colorHash = {
            hex: s.hex(),
            rgb: rgbVal,
            hsl: hslVal
        }, l.push(colorHash);
        var u = document.createElement("div");
        u.setAttribute("class", "color-card");
        var d = document.createElement("div");
        d.setAttribute("class", "color-swatch");
        var f = document.createElement("div");
        f.style.backgroundColor = s.hex(), f.setAttribute("class", "js-color"), f.setAttribute("data-hex", s.hex()), f.setAttribute("data-rgb", rgbVal), f.setAttribute("data-hsl", hslVal), d.appendChild(f), u.appendChild(d);
        var h = document.createElement("div");
        h.setAttribute("class", "color-data");
        var t = document.createElement("h4");
        t.setAttribute("class", "selectable"), t.innerHTML = s.hex(), h.appendChild(t);
        var p = document.createElement("h4");
        p.setAttribute("class", "selectable"), p.innerHTML = rgbVal, h.appendChild(p), u.appendChild(h), i.appendChild(u)
    }
    localStorage.setItem("harmonyColors", JSON.stringify(l)), refreshColor(".js-color"), colorNav()
}

function refreshColor(e) {
    for (var t = document.querySelectorAll(e), n = 0; n < t.length; n++) t[n].removeEventListener("click", function (e) {
        colorListener(e)
    }, !1), t[n].addEventListener("click", function (e) {
        colorListener(e)
    }, !1)
}

function pickerHeight() {
    var e = document.querySelector(".twod").offsetWidth, t = document.querySelector(".twod"),
        n = document.querySelector(".oned");
    n.style.height = .86 * e + "px", t.style.height = .86 * e + "px"
}

// function swapHarmony(e, t) {
//     for (var n = document.querySelectorAll(".js-harmony"), r = 0; r < n.length; r++) n[r].addEventListener("click", function (n) {
//         var r = n.target.id;
//         sessionStorage.setItem("currentHarmony", r), colorHarmony(e, t)
//     }, !1)
// }

// function nameHarmony(e) {
//     var t = document.getElementById("harmony"), n = document.createElement("span");
//     n.setAttribute("class", "triangle gray"), t.innerHTML = e, t.appendChild(n)
// }

function currentY() {
    return self.pageYOffset ? self.pageYOffset : document.documentElement && document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop ? document.body.scrollTop : 0
}

function elementY(e) {
    for (var t = document.getElementById(e), n = t.offsetTop, r = t; r.offsetParent && r.offsetParent != document.body;) r = r.offsetParent, n += r.offsetTop;
    return n
}

function smoothScroll(e) {
    var t = currentY(), n = elementY(e) - 100;
    console.log(t), console.log(n);
    var r = n > t ? n - t : t - n;
    if (100 > r) return void scrollTo(0, n);
    var o = Math.round(r / 100);
    o >= 20 && (o = 20);
    var a = Math.round(r / 25), i = n > t ? t + a : t - a, l = 0;
    if (!(n > t)) {
        for (var c = t; c > n; c -= a) setTimeout("window.scrollTo(0, " + i + ")", l * o), i -= a, n > i && (i = n), l++;
        return !1
    }
    for (var c = t; n > c; c += a) setTimeout("window.scrollTo(0, " + i + ")", l * o), i += a, i > n && (i = n), l++
}

// function initSmoothScroll() {
//     for (var e = document.querySelectorAll(".scrollable"), t = 0; t < e.length; t++) e[t].addEventListener("click", function (e) {
//         var t = e.target, n = t.getAttribute("data-scroll");
//         smoothScroll(n)
//     }, !1)
// }

function selector(e) {
    var t, n, r = document, o = e;
    r.body.createTextRange ? (t = r.body.createTextRange(), t.moveToElementText(o), t.select()) : window.getSelection && (n = window.getSelection(), t = r.createRange(), t.selectNodeContents(o), n.removeAllRanges(), n.addRange(t))
}

function nextElementSibling(e) {
    do e = e.nextSibling; while (e && 1 !== e.nodeType);
    return e
}

!function () {
    "use strict";

    function e(t, r) {
        function o(e, t) {
            return function () {
                return e.apply(t, arguments)
            }
        }

        var a;
        if (r = r || {}, this.trackingClick = !1, this.trackingClickStart = 0, this.targetElement = null, this.touchStartX = 0, this.touchStartY = 0, this.lastTouchIdentifier = 0, this.touchBoundary = r.touchBoundary || 10, this.layer = t, this.tapDelay = r.tapDelay || 200, this.tapTimeout = r.tapTimeout || 700, !e.notNeeded(t)) {
            for (var i = ["onMouse", "onClick", "onTouchStart", "onTouchMove", "onTouchEnd", "onTouchCancel"], l = this, c = 0, s = i.length; s > c; c++) l[i[c]] = o(l[i[c]], l);
            n && (t.addEventListener("mouseover", this.onMouse, !0), t.addEventListener("mousedown", this.onMouse, !0), t.addEventListener("mouseup", this.onMouse, !0)), t.addEventListener("click", this.onClick, !0), t.addEventListener("touchstart", this.onTouchStart, !1), t.addEventListener("touchmove", this.onTouchMove, !1), t.addEventListener("touchend", this.onTouchEnd, !1), t.addEventListener("touchcancel", this.onTouchCancel, !1), Event.prototype.stopImmediatePropagation || (t.removeEventListener = function (e, n, r) {
                var o = Node.prototype.removeEventListener;
                "click" === e ? o.call(t, e, n.hijacked || n, r) : o.call(t, e, n, r)
            }, t.addEventListener = function (e, n, r) {
                var o = Node.prototype.addEventListener;
                "click" === e ? o.call(t, e, n.hijacked || (n.hijacked = function (e) {
                    e.propagationStopped || n(e)
                }), r) : o.call(t, e, n, r)
            }), "function" == typeof t.onclick && (a = t.onclick, t.addEventListener("click", function (e) {
                a(e)
            }, !1), t.onclick = null)
        }
    }

    var t = navigator.userAgent.indexOf("Windows Phone") >= 0, n = navigator.userAgent.indexOf("Android") > 0 && !t,
        r = /iP(ad|hone|od)/.test(navigator.userAgent) && !t, o = r && /OS 4_\d(_\d)?/.test(navigator.userAgent),
        a = r && /OS [6-7]_\d/.test(navigator.userAgent), i = navigator.userAgent.indexOf("BB10") > 0;
    e.prototype.needsClick = function (e) {
        switch (e.nodeName.toLowerCase()) {
            case"button":
            case"select":
            case"textarea":
                if (e.disabled) return !0;
                break;
            case"input":
                if (r && "file" === e.type || e.disabled) return !0;
                break;
            case"label":
            case"iframe":
            case"video":
                return !0
        }
        return /\bneedsclick\b/.test(e.className)
    }, e.prototype.needsFocus = function (e) {
        switch (e.nodeName.toLowerCase()) {
            case"textarea":
                return !0;
            case"select":
                return !n;
            case"input":
                switch (e.type) {
                    case"button":
                    case"checkbox":
                    case"file":
                    case"image":
                    case"radio":
                    case"submit":
                        return !1
                }
                return !e.disabled && !e.readOnly;
            default:
                return /\bneedsfocus\b/.test(e.className)
        }
    }, e.prototype.sendClick = function (e, t) {
        var n, r;
        document.activeElement && document.activeElement !== e && document.activeElement.blur(), r = t.changedTouches[0], n = document.createEvent("MouseEvents"), n.initMouseEvent(this.determineEventType(e), !0, !0, window, 1, r.screenX, r.screenY, r.clientX, r.clientY, !1, !1, !1, !1, 0, null), n.forwardedTouchEvent = !0, e.dispatchEvent(n)
    }, e.prototype.determineEventType = function (e) {
        return n && "select" === e.tagName.toLowerCase() ? "mousedown" : "click"
    }, e.prototype.focus = function (e) {
        var t;
        r && e.setSelectionRange && 0 !== e.type.indexOf("date") && "time" !== e.type && "month" !== e.type ? (t = e.value.length, e.setSelectionRange(t, t)) : e.focus()
    }, e.prototype.updateScrollParent = function (e) {
        var t, n;
        if (t = e.fastClickScrollParent, !t || !t.contains(e)) {
            n = e;
            do {
                if (n.scrollHeight > n.offsetHeight) {
                    t = n, e.fastClickScrollParent = n;
                    break
                }
                n = n.parentElement
            } while (n)
        }
        t && (t.fastClickLastScrollTop = t.scrollTop)
    }, e.prototype.getTargetElementFromEventTarget = function (e) {
        return e.nodeType === Node.TEXT_NODE ? e.parentNode : e
    }, e.prototype.onTouchStart = function (e) {
        var t, n, a;
        if (e.targetTouches.length > 1) return !0;
        if (t = this.getTargetElementFromEventTarget(e.target), n = e.targetTouches[0], r) {
            if (a = window.getSelection(), a.rangeCount && !a.isCollapsed) return !0;
            if (!o) {
                if (n.identifier && n.identifier === this.lastTouchIdentifier) return e.preventDefault(), !1;
                this.lastTouchIdentifier = n.identifier, this.updateScrollParent(t)
            }
        }
        return this.trackingClick = !0, this.trackingClickStart = e.timeStamp, this.targetElement = t, this.touchStartX = n.pageX, this.touchStartY = n.pageY, e.timeStamp - this.lastClickTime < this.tapDelay && e.preventDefault(), !0
    }, e.prototype.touchHasMoved = function (e) {
        var t = e.changedTouches[0], n = this.touchBoundary;
        return Math.abs(t.pageX - this.touchStartX) > n || Math.abs(t.pageY - this.touchStartY) > n ? !0 : !1
    }, e.prototype.onTouchMove = function (e) {
        return this.trackingClick ? ((this.targetElement !== this.getTargetElementFromEventTarget(e.target) || this.touchHasMoved(e)) && (this.trackingClick = !1, this.targetElement = null), !0) : !0
    }, e.prototype.findControl = function (e) {
        return void 0 !== e.control ? e.control : e.htmlFor ? document.getElementById(e.htmlFor) : e.querySelector("button, input:not([type=hidden]), keygen, meter, output, progress, select, textarea")
    }, e.prototype.onTouchEnd = function (e) {
        var t, i, l, c, s, u = this.targetElement;
        if (!this.trackingClick) return !0;
        if (e.timeStamp - this.lastClickTime < this.tapDelay) return this.cancelNextClick = !0, !0;
        if (e.timeStamp - this.trackingClickStart > this.tapTimeout) return !0;
        if (this.cancelNextClick = !1, this.lastClickTime = e.timeStamp, i = this.trackingClickStart, this.trackingClick = !1, this.trackingClickStart = 0, a && (s = e.changedTouches[0], u = document.elementFromPoint(s.pageX - window.pageXOffset, s.pageY - window.pageYOffset) || u, u.fastClickScrollParent = this.targetElement.fastClickScrollParent), l = u.tagName.toLowerCase(), "label" === l) {
            if (t = this.findControl(u)) {
                if (this.focus(u), n) return !1;
                u = t
            }
        } else if (this.needsFocus(u)) return e.timeStamp - i > 100 || r && window.top !== window && "input" === l ? (this.targetElement = null, !1) : (this.focus(u), this.sendClick(u, e), r && "select" === l || (this.targetElement = null, e.preventDefault()), !1);
        return r && !o && (c = u.fastClickScrollParent, c && c.fastClickLastScrollTop !== c.scrollTop) ? !0 : (this.needsClick(u) || (e.preventDefault(), this.sendClick(u, e)), !1)
    }, e.prototype.onTouchCancel = function () {
        this.trackingClick = !1, this.targetElement = null
    }, e.prototype.onMouse = function (e) {
        return this.targetElement ? e.forwardedTouchEvent ? !0 : !e.cancelable || this.needsClick(this.targetElement) && !this.cancelNextClick ? !0 : (e.stopImmediatePropagation ? e.stopImmediatePropagation() : e.propagationStopped = !0, e.stopPropagation(), e.preventDefault(), !1) : !0
    }, e.prototype.onClick = function (e) {
        var t;
        return this.trackingClick ? (this.targetElement = null, this.trackingClick = !1, !0) : "submit" === e.target.type && 0 === e.detail ? !0 : (t = this.onMouse(e), t || (this.targetElement = null), t)
    }, e.prototype.destroy = function () {
        var e = this.layer;
        n && (e.removeEventListener("mouseover", this.onMouse, !0), e.removeEventListener("mousedown", this.onMouse, !0), e.removeEventListener("mouseup", this.onMouse, !0)), e.removeEventListener("click", this.onClick, !0), e.removeEventListener("touchstart", this.onTouchStart, !1), e.removeEventListener("touchmove", this.onTouchMove, !1), e.removeEventListener("touchend", this.onTouchEnd, !1), e.removeEventListener("touchcancel", this.onTouchCancel, !1)
    }, e.notNeeded = function (e) {
        var t, r, o, a;
        if ("undefined" == typeof window.ontouchstart) return !0;
        if (r = +(/Chrome\/([0-9]+)/.exec(navigator.userAgent) || [, 0])[1]) {
            if (!n) return !0;
            if (t = document.querySelector("meta[name=viewport]")) {
                if (-1 !== t.content.indexOf("user-scalable=no")) return !0;
                if (r > 31 && document.documentElement.scrollWidth <= window.outerWidth) return !0
            }
        }
        if (i && (o = navigator.userAgent.match(/Version\/([0-9]*)\.([0-9]*)/), o[1] >= 10 && o[2] >= 3 && (t = document.querySelector("meta[name=viewport]")))) {
            if (-1 !== t.content.indexOf("user-scalable=no")) return !0;
            if (document.documentElement.scrollWidth <= window.outerWidth) return !0
        }
        return "none" === e.style.msTouchAction || "manipulation" === e.style.touchAction ? !0 : (a = +(/Firefox\/([0-9]+)/.exec(navigator.userAgent) || [, 0])[1], a >= 27 && (t = document.querySelector("meta[name=viewport]"), t && (-1 !== t.content.indexOf("user-scalable=no") || document.documentElement.scrollWidth <= window.outerWidth)) ? !0 : "none" === e.style.touchAction || "manipulation" === e.style.touchAction ? !0 : !1)
    }, e.attach = function (t, n) {
        return new e(t, n)
    }, "function" == typeof define && "object" == typeof define.amd && define.amd ? define(function () {
        return e
    }) : "undefined" != typeof module && module.exports ? (module.exports = e.attach, module.exports.FastClick = e) : window.FastClick = e
}(), !function (e, t) {
    function n(e, t) {
        var n = e.createElement("p"), r = e.getElementsByTagName("head")[0] || e.documentElement;
        return n.innerHTML = "x<style>" + t + "</style>", r.insertBefore(n.lastChild, r.firstChild)
    }

    function r() {
        var e = y.elements;
        return "string" == typeof e ? e.split(" ") : e
    }

    function o(e, t) {
        var n = y.elements;
        "string" != typeof n && (n = n.join(" ")), "string" != typeof e && (e = e.join(" ")), y.elements = n + " " + e, s(t)
    }

    function a(e) {
        var t = b[e[m]];
        return t || (t = {}, v++, e[m] = v, b[v] = t), t
    }

    function i(e, n, r) {
        if (n || (n = t), d) return n.createElement(e);
        r || (r = a(n));
        var o;
        return o = r.cache[e] ? r.cache[e].cloneNode() : g.test(e) ? (r.cache[e] = r.createElem(e)).cloneNode() : r.createElem(e), !o.canHaveChildren || p.test(e) || o.tagUrn ? o : r.frag.appendChild(o)
    }

    function l(e, n) {
        if (e || (e = t), d) return e.createDocumentFragment();
        n = n || a(e);
        for (var o = n.frag.cloneNode(), i = 0, l = r(), c = l.length; c > i; i++) o.createElement(l[i]);
        return o
    }

    function c(e, t) {
        t.cache || (t.cache = {}, t.createElem = e.createElement, t.createFrag = e.createDocumentFragment, t.frag = t.createFrag()), e.createElement = function (n) {
            return y.shivMethods ? i(n, e, t) : t.createElem(n)
        }, e.createDocumentFragment = Function("h,f", "return function(){var n=f.cloneNode(),c=n.createElement;h.shivMethods&&(" + r().join().replace(/[\w\-:]+/g, function (e) {
            return t.createElem(e), t.frag.createElement(e), 'c("' + e + '")'
        }) + ");return n}")(y, t.frag)
    }

    function s(e) {
        e || (e = t);
        var r = a(e);
        return !y.shivCSS || u || r.hasCSS || (r.hasCSS = !!n(e, "article,aside,dialog,figcaption,figure,footer,header,hgroup,main,nav,section{display:block}mark{background:#FF0;color:#000}template{display:none}")), d || c(e, r), e
    }

    var u, d, f = "3.7.3-pre", h = e.html5 || {},
        p = /^<|^(?:button|map|select|textarea|object|iframe|option|optgroup)$/i,
        g = /^(?:a|b|code|div|fieldset|h1|h2|h3|h4|h5|h6|i|label|li|ol|p|q|span|strong|style|table|tbody|td|th|tr|ul)$/i,
        m = "_html5shiv", v = 0, b = {};
    !function () {
        try {
            var e = t.createElement("a");
            e.innerHTML = "<xyz></xyz>", u = "hidden" in e, d = 1 == e.childNodes.length || function () {
                t.createElement("a");
                var e = t.createDocumentFragment();
                return "undefined" == typeof e.cloneNode || "undefined" == typeof e.createDocumentFragment || "undefined" == typeof e.createElement
            }()
        } catch (n) {
            u = !0, d = !0
        }
    }();
    var y = {
        elements: h.elements || "abbr article aside audio bdi canvas data datalist details dialog figcaption figure footer header hgroup main mark meter nav output picture progress section summary template time video",
        version: f,
        shivCSS: h.shivCSS !== !1,
        supportsUnknownElements: d,
        shivMethods: h.shivMethods !== !1,
        type: "default",
        shivDocument: s,
        createElement: i,
        createDocumentFragment: l,
        addElements: o
    };
    e.html5 = y, s(t), "object" == typeof module && module.exports && (module.exports = y)
}("undefined" != typeof window ? window : this, document), !function (e) {
    "undefined" != typeof exports ? e(exports) : (window.hljs = e({}), "function" == typeof define && define.amd && define("hljs", [], function () {
        return window.hljs
    }))
}(function (e) {
    function t(e) {
        return e.replace(/&/gm, "&amp;").replace(/</gm, "&lt;").replace(/>/gm, "&gt;")
    }

    function n(e) {
        return e.nodeName.toLowerCase()
    }

    function r(e, t) {
        var n = e && e.exec(t);
        return n && 0 == n.index
    }

    function o(e) {
        return /no-?highlight|plain|text/.test(e)
    }

    function a(e) {
        var t, n, r, a = e.className + " ";
        if (a += e.parentNode ? e.parentNode.className : "", n = /\blang(?:uage)?-([\w-]+)\b/.exec(a)) return C(n[1]) ? n[1] : "no-highlight";
        for (a = a.split(/\s+/), t = 0, r = a.length; r > t; t++) if (C(a[t]) || o(a[t])) return a[t]
    }

    function i(e, t) {
        var n, r = {};
        for (n in e) r[n] = e[n];
        if (t) for (n in t) r[n] = t[n];
        return r
    }

    function l(e) {
        var t = [];
        return function r(e, o) {
            for (var a = e.firstChild; a; a = a.nextSibling) 3 == a.nodeType ? o += a.nodeValue.length : 1 == a.nodeType && (t.push({
                event: "start",
                offset: o,
                node: a
            }), o = r(a, o), n(a).match(/br|hr|img|input/) || t.push({event: "stop", offset: o, node: a}));
            return o
        }(e, 0), t
    }

    function c(e, r, o) {
        function a() {
            return e.length && r.length ? e[0].offset != r[0].offset ? e[0].offset < r[0].offset ? e : r : "start" == r[0].event ? e : r : e.length ? e : r
        }

        function i(e) {
            function r(e) {
                return " " + e.nodeName + '="' + t(e.value) + '"'
            }

            u += "<" + n(e) + Array.prototype.map.call(e.attributes, r).join("") + ">"
        }

        function l(e) {
            u += "</" + n(e) + ">"
        }

        function c(e) {
            ("start" == e.event ? i : l)(e.node)
        }

        for (var s = 0, u = "", d = []; e.length || r.length;) {
            var f = a();
            if (u += t(o.substr(s, f[0].offset - s)), s = f[0].offset, f == e) {
                d.reverse().forEach(l);
                do c(f.splice(0, 1)[0]), f = a(); while (f == e && f.length && f[0].offset == s);
                d.reverse().forEach(i)
            } else "start" == f[0].event ? d.push(f[0].node) : d.pop(), c(f.splice(0, 1)[0])
        }
        return u + t(o.substr(s))
    }

    function s(e) {
        function t(e) {
            return e && e.source || e
        }

        function n(n, r) {
            return new RegExp(t(n), "m" + (e.cI ? "i" : "") + (r ? "g" : ""))
        }

        function r(o, a) {
            if (!o.compiled) {
                if (o.compiled = !0, o.k = o.k || o.bK, o.k) {
                    var l = {}, c = function (t, n) {
                        e.cI && (n = n.toLowerCase()), n.split(" ").forEach(function (e) {
                            var n = e.split("|");
                            l[n[0]] = [t, n[1] ? Number(n[1]) : 1]
                        })
                    };
                    "string" == typeof o.k ? c("keyword", o.k) : Object.keys(o.k).forEach(function (e) {
                        c(e, o.k[e])
                    }), o.k = l
                }
                o.lR = n(o.l || /\b\w+\b/, !0), a && (o.bK && (o.b = "\\b(" + o.bK.split(" ").join("|") + ")\\b"), o.b || (o.b = /\B|\b/), o.bR = n(o.b), o.e || o.eW || (o.e = /\B|\b/), o.e && (o.eR = n(o.e)), o.tE = t(o.e) || "", o.eW && a.tE && (o.tE += (o.e ? "|" : "") + a.tE)), o.i && (o.iR = n(o.i)), void 0 === o.r && (o.r = 1), o.c || (o.c = []);
                var s = [];
                o.c.forEach(function (e) {
                    e.v ? e.v.forEach(function (t) {
                        s.push(i(e, t))
                    }) : s.push("self" == e ? o : e)
                }), o.c = s, o.c.forEach(function (e) {
                    r(e, o)
                }), o.starts && r(o.starts, a);
                var u = o.c.map(function (e) {
                    return e.bK ? "\\.?(" + e.b + ")\\.?" : e.b
                }).concat([o.tE, o.i]).map(t).filter(Boolean);
                o.t = u.length ? n(u.join("|"), !0) : {
                    exec: function () {
                        return null
                    }
                }
            }
        }

        r(e)
    }

    function u(e, n, o, a) {
        function i(e, t) {
            for (var n = 0; n < t.c.length; n++) if (r(t.c[n].bR, e)) return t.c[n]
        }

        function l(e, t) {
            if (r(e.eR, t)) {
                for (; e.endsParent && e.parent;) e = e.parent;
                return e
            }
            return e.eW ? l(e.parent, t) : void 0
        }

        function c(e, t) {
            return !o && r(t.iR, e)
        }

        function f(e, t) {
            var n = y.cI ? t[0].toLowerCase() : t[0];
            return e.k.hasOwnProperty(n) && e.k[n]
        }

        function h(e, t, n, r) {
            var o = r ? "" : w.classPrefix, a = '<span class="' + o, i = n ? "" : "</span>";
            return a += e + '">', a + t + i
        }

        function p() {
            if (!x.k) return t(O);
            var e = "", n = 0;
            x.lR.lastIndex = 0;
            for (var r = x.lR.exec(O); r;) {
                e += t(O.substr(n, r.index - n));
                var o = f(x, r);
                o ? (L += o[1], e += h(o[0], t(r[0]))) : e += t(r[0]), n = x.lR.lastIndex, r = x.lR.exec(O)
            }
            return e + t(O.substr(n))
        }

        function g() {
            var e = "string" == typeof x.sL;
            if (e && !E[x.sL]) return t(O);
            var n = e ? u(x.sL, O, !0, N[x.sL]) : d(O, x.sL.length ? x.sL : void 0);
            return x.r > 0 && (L += n.r), e && (N[x.sL] = n.top), h(n.language, n.value, !1, !0)
        }

        function m() {
            return void 0 !== x.sL ? g() : p()
        }

        function v(e, n) {
            var r = e.cN ? h(e.cN, "", !0) : "";
            e.rB ? (S += r, O = "") : e.eB ? (S += t(n) + r, O = "") : (S += r, O = n), x = Object.create(e, {parent: {value: x}})
        }

        function b(e, n) {
            if (O += e, void 0 === n) return S += m(), 0;
            var r = i(n, x);
            if (r) return S += m(), v(r, n), r.rB ? 0 : n.length;
            var o = l(x, n);
            if (o) {
                var a = x;
                a.rE || a.eE || (O += n), S += m();
                do x.cN && (S += "</span>"), L += x.r, x = x.parent; while (x != o.parent);
                return a.eE && (S += t(n)), O = "", o.starts && v(o.starts, ""), a.rE ? 0 : n.length
            }
            if (c(n, x)) throw new Error('Illegal lexeme "' + n + '" for mode "' + (x.cN || "<unnamed>") + '"');
            return O += n, n.length || 1
        }

        var y = C(e);
        if (!y) throw new Error('Unknown language: "' + e + '"');
        s(y);
        var k, x = a || y, N = {}, S = "";
        for (k = x; k != y; k = k.parent) k.cN && (S = h(k.cN, "", !0) + S);
        var O = "", L = 0;
        try {
            for (var M, _, A = 0; x.t.lastIndex = A, M = x.t.exec(n), M;) _ = b(n.substr(A, M.index - A), M[0]), A = M.index + _;
            for (b(n.substr(A)), k = x; k.parent; k = k.parent) k.cN && (S += "</span>");
            return {r: L, value: S, language: e, top: x}
        } catch (T) {
            if (-1 != T.message.indexOf("Illegal")) return {r: 0, value: t(n)};
            throw T
        }
    }

    function d(e, n) {
        n = n || w.languages || Object.keys(E);
        var r = {r: 0, value: t(e)}, o = r;
        return n.forEach(function (t) {
            if (C(t)) {
                var n = u(t, e, !1);
                n.language = t, n.r > o.r && (o = n), n.r > r.r && (o = r, r = n)
            }
        }), o.language && (r.second_best = o), r
    }

    function f(e) {
        return w.tabReplace && (e = e.replace(/^((<[^>]+>|\t)+)/gm, function (e, t) {
            return t.replace(/\t/g, w.tabReplace)
        })), w.useBR && (e = e.replace(/\n/g, "<br>")), e
    }

    function h(e, t, n) {
        var r = t ? k[t] : n, o = [e.trim()];
        return e.match(/\bhljs\b/) || o.push("hljs"), -1 === e.indexOf(r) && o.push(r), o.join(" ").trim()
    }

    function p(e) {
        var t = a(e);
        if (!o(t)) {
            var n;
            w.useBR ? (n = document.createElementNS("http://www.w3.org/1999/xhtml", "div"), n.innerHTML = e.innerHTML.replace(/\n/g, "").replace(/<br[ \/]*>/g, "\n")) : n = e;
            var r = n.textContent, i = t ? u(t, r, !0) : d(r), s = l(n);
            if (s.length) {
                var p = document.createElementNS("http://www.w3.org/1999/xhtml", "div");
                p.innerHTML = i.value, i.value = c(s, l(p), r)
            }
            i.value = f(i.value), e.innerHTML = i.value, e.className = h(e.className, t, i.language), e.result = {
                language: i.language,
                re: i.r
            }, i.second_best && (e.second_best = {language: i.second_best.language, re: i.second_best.r})
        }
    }

    function g(e) {
        w = i(w, e)
    }

    function m() {
        if (!m.called) {
            m.called = !0;
            var e = document.querySelectorAll("pre code");
            Array.prototype.forEach.call(e, p)
        }
    }

    function v() {
        addEventListener("DOMContentLoaded", m, !1), addEventListener("load", m, !1)
    }

    function b(t, n) {
        var r = E[t] = n(e);
        r.aliases && r.aliases.forEach(function (e) {
            k[e] = t
        })
    }

    function y() {
        return Object.keys(E)
    }

    function C(e) {
        return E[e] || E[k[e]]
    }

    var w = {classPrefix: "hljs-", tabReplace: null, useBR: !1, languages: void 0}, E = {}, k = {};
    return e.highlight = u, e.highlightAuto = d, e.fixMarkup = f, e.highlightBlock = p, e.configure = g, e.initHighlighting = m, e.initHighlightingOnLoad = v, e.registerLanguage = b, e.listLanguages = y, e.getLanguage = C, e.inherit = i, e.IR = "[a-zA-Z]\\w*", e.UIR = "[a-zA-Z_]\\w*", e.NR = "\\b\\d+(\\.\\d+)?", e.CNR = "(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)", e.BNR = "\\b(0b[01]+)", e.RSR = "!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~", e.BE = {
        b: "\\\\[\\s\\S]",
        r: 0
    }, e.ASM = {cN: "string", b: "'", e: "'", i: "\\n", c: [e.BE]}, e.QSM = {
        cN: "string",
        b: '"',
        e: '"',
        i: "\\n",
        c: [e.BE]
    }, e.PWM = {b: /\b(a|an|the|are|I|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such)\b/}, e.C = function (t, n, r) {
        var o = e.inherit({cN: "comment", b: t, e: n, c: []}, r || {});
        return o.c.push(e.PWM), o.c.push({cN: "doctag", b: "(?:TODO|FIXME|NOTE|BUG|XXX):", r: 0}), o
    }, e.CLCM = e.C("//", "$"), e.CBCM = e.C("/\\*", "\\*/"), e.HCM = e.C("#", "$"), e.NM = {
        cN: "number",
        b: e.NR,
        r: 0
    }, e.CNM = {cN: "number", b: e.CNR, r: 0}, e.BNM = {cN: "number", b: e.BNR, r: 0}, e.CSSNM = {
        cN: "number",
        b: e.NR + "(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",
        r: 0
    }, e.RM = {
        cN: "regexp",
        b: /\//,
        e: /\/[gimuy]*/,
        i: /\n/,
        c: [e.BE, {b: /\[/, e: /\]/, r: 0, c: [e.BE]}]
    }, e.TM = {cN: "title", b: e.IR, r: 0}, e.UTM = {cN: "title", b: e.UIR, r: 0}, e
}),
    function (e) {
        "use strict";

        function t(e) {
            return new RegExp("(^|\\s+)" + e + "(\\s+|$)")
        }

        function n(e, t) {
            var n = r(e, t) ? a : o;
            n(e, t)
        }

        var r, o, a;
        "classList" in document.documentElement ? (r = function (e, t) {
            return e.classList.contains(t)
        }, o = function (e, t) {
            e.classList.add(t)
        }, a = function (e, t) {
            e.classList.remove(t)
        }) : (r = function (e, n) {
            return t(n).test(e.className)
        }, o = function (e, t) {
            r(e, t) || (e.className = e.className + " " + t)
        }, a = function (e, n) {
            e.className = e.className.replace(t(n), " ")
        });
        var i = {hasClass: r, addClass: o, removeClass: a, toggleClass: n, has: r, add: o, remove: a, toggle: n};
        "function" == typeof define && define.amd ? define(i) : e.classie = i
    }(window), !function () {
    var e = "a" !== "a"[0], t = function (t) {
        if (null === t) throw new TypeError("can't convert " + t + " to object");
        return e && "string" == typeof t && t ? t.split("") : new Object(t)
    };
    Array.prototype.forEach || (Array.prototype.forEach = function (e) {
        var n = t(this), r = arguments[1], o = -1, a = n.length >>> 0;
        if ("function" != typeof e) throw new TypeError;
        for (; ++o < a;) o in n && e.call(r, n[o], o, n)
    }), Array.prototype.map || (Array.prototype.map = function (e) {
        var n, r = t(this), o = r.length >>> 0, a = new Array(o), i = arguments[1];
        if ("function" != typeof e) throw new TypeError(e + " is not a function");
        for (n = 0; o > n; n += 1) n in r && (a[n] = e.call(i, r[n], n, r));
        return a
    }), Array.prototype.filter || (Array.prototype.filter = function (e) {
        var n, r, o = t(this), a = o.length >>> 0, i = [], l = arguments[1];
        if ("function" != typeof e) throw new TypeError(e + " is not a function");
        for (r = 0; a > r; r += 1) r in o && (n = o[r], e.call(l, n, r, o) && i.push(n));
        return i
    })
}();
var installedColorSpaces = [], namedColors = {}, undef = function (e) {
        return "undefined" == typeof e
    }, channelRegExp = /\s*(\.\d+|\d+(?:\.\d+)?)(%)?\s*/, alphaChannelRegExp = /\s*(\.\d+|\d+(?:\.\d+)?)\s*/,
    cssColorRegExp = new RegExp("^(rgb|hsl|hsv)a?\\(" + channelRegExp.source + "," + channelRegExp.source + "," + channelRegExp.source + "(?:," + alphaChannelRegExp.source + ")?\\)$", "i");
ONECOLOR.installMethod = function (e, t) {
    installedColorSpaces.forEach(function (n) {
        ONECOLOR[n].prototype[e] = t
    })
}, installColorSpace("RGB", ["red", "green", "blue", "alpha"], {
    hex: function () {
        var e = (65536 * Math.round(255 * this._red) + 256 * Math.round(255 * this._green) + Math.round(255 * this._blue)).toString(16);
        return "#" + "00000".substr(0, 6 - e.length) + e
    }, hexa: function () {
        var e = Math.round(255 * this._alpha).toString(16);
        return "#" + "00".substr(0, 2 - e.length) + e + this.hex().substr(1, 6)
    }, css: function () {
        return "rgb(" + Math.round(255 * this._red) + "," + Math.round(255 * this._green) + "," + Math.round(255 * this._blue) + ")"
    }, cssa: function () {
        return "rgba(" + Math.round(255 * this._red) + "," + Math.round(255 * this._green) + "," + Math.round(255 * this._blue) + "," + this._alpha + ")"
    }
}), "undefined" != typeof module ? module.exports = ONECOLOR : "function" != typeof define || undef(define.amd) ? "undefined" != typeof jQuery && undef(jQuery.color) ? jQuery.color = ONECOLOR : (one = window.one || {}, one.color = ONECOLOR) : define([], function () {
    return ONECOLOR
}), namedColors = {
    aliceblue: "f0f8ff",
    antiquewhite: "faebd7",
    aqua: "0ff",
    aquamarine: "7fffd4",
    azure: "f0ffff",
    beige: "f5f5dc",
    bisque: "ffe4c4",
    black: "000",
    blanchedalmond: "ffebcd",
    blue: "00f",
    blueviolet: "8a2be2",
    brown: "a52a2a",
    burlywood: "deb887",
    cadetblue: "5f9ea0",
    chartreuse: "7fff00",
    chocolate: "d2691e",
    coral: "ff7f50",
    cornflowerblue: "6495ed",
    cornsilk: "fff8dc",
    crimson: "dc143c",
    cyan: "0ff",
    darkblue: "00008b",
    darkcyan: "008b8b",
    darkgoldenrod: "b8860b",
    darkgray: "a9a9a9",
    darkgrey: "a9a9a9",
    darkgreen: "006400",
    darkkhaki: "bdb76b",
    darkmagenta: "8b008b",
    darkolivegreen: "556b2f",
    darkorange: "ff8c00",
    darkorchid: "9932cc",
    darkred: "8b0000",
    darksalmon: "e9967a",
    darkseagreen: "8fbc8f",
    darkslateblue: "483d8b",
    darkslategray: "2f4f4f",
    darkslategrey: "2f4f4f",
    darkturquoise: "00ced1",
    darkviolet: "9400d3",
    deeppink: "ff1493",
    deepskyblue: "00bfff",
    dimgray: "696969",
    dimgrey: "696969",
    dodgerblue: "1e90ff",
    firebrick: "b22222",
    floralwhite: "fffaf0",
    forestgreen: "228b22",
    fuchsia: "f0f",
    gainsboro: "dcdcdc",
    ghostwhite: "f8f8ff",
    gold: "ffd700",
    goldenrod: "daa520",
    gray: "808080",
    grey: "808080",
    green: "008000",
    greenyellow: "adff2f",
    honeydew: "f0fff0",
    hotpink: "ff69b4",
    indianred: "cd5c5c",
    indigo: "4b0082",
    ivory: "fffff0",
    khaki: "f0e68c",
    lavender: "e6e6fa",
    lavenderblush: "fff0f5",
    lawngreen: "7cfc00",
    lemonchiffon: "fffacd",
    lightblue: "add8e6",
    lightcoral: "f08080",
    lightcyan: "e0ffff",
    lightgoldenrodyellow: "fafad2",
    lightgray: "d3d3d3",
    lightgrey: "d3d3d3",
    lightgreen: "90ee90",
    lightpink: "ffb6c1",
    lightsalmon: "ffa07a",
    lightseagreen: "20b2aa",
    lightskyblue: "87cefa",
    lightslategray: "789",
    lightslategrey: "789",
    lightsteelblue: "b0c4de",
    lightyellow: "ffffe0",
    lime: "0f0",
    limegreen: "32cd32",
    linen: "faf0e6",
    magenta: "f0f",
    maroon: "800000",
    mediumaquamarine: "66cdaa",
    mediumblue: "0000cd",
    mediumorchid: "ba55d3",
    mediumpurple: "9370d8",
    mediumseagreen: "3cb371",
    mediumslateblue: "7b68ee",
    mediumspringgreen: "00fa9a",
    mediumturquoise: "48d1cc",
    mediumvioletred: "c71585",
    midnightblue: "191970",
    mintcream: "f5fffa",
    mistyrose: "ffe4e1",
    moccasin: "ffe4b5",
    navajowhite: "ffdead",
    navy: "000080",
    oldlace: "fdf5e6",
    olive: "808000",
    olivedrab: "6b8e23",
    orange: "ffa500",
    orangered: "ff4500",
    orchid: "da70d6",
    palegoldenrod: "eee8aa",
    palegreen: "98fb98",
    paleturquoise: "afeeee",
    palevioletred: "d87093",
    papayawhip: "ffefd5",
    peachpuff: "ffdab9",
    peru: "cd853f",
    pink: "ffc0cb",
    plum: "dda0dd",
    powderblue: "b0e0e6",
    purple: "800080",
    red: "f00",
    rosybrown: "bc8f8f",
    royalblue: "4169e1",
    saddlebrown: "8b4513",
    salmon: "fa8072",
    sandybrown: "f4a460",
    seagreen: "2e8b57",
    seashell: "fff5ee",
    sienna: "a0522d",
    silver: "c0c0c0",
    skyblue: "87ceeb",
    slateblue: "6a5acd",
    slategray: "708090",
    slategrey: "708090",
    snow: "fffafa",
    springgreen: "00ff7f",
    steelblue: "4682b4",
    tan: "d2b48c",
    teal: "008080",
    thistle: "d8bfd8",
    tomato: "ff6347",
    turquoise: "40e0d0",
    violet: "ee82ee",
    wheat: "f5deb3",
    white: "fff",
    whitesmoke: "f5f5f5",
    yellow: "ff0",
    yellowgreen: "9acd32"
}, installColorSpace("XYZ", ["x", "y", "z", "alpha"], {
    fromRgb: function () {
        var e = function (e) {
            return 100 * (e > .04045 ? Math.pow((e + .055) / 1.055, 2.4) : e / 12.92)
        }, t = e(this._red), n = e(this._green), r = e(this._blue);
        return new ONECOLOR.XYZ(.4124 * t + .3576 * n + .1805 * r, .2126 * t + .7152 * n + .0722 * r, .0193 * t + .1192 * n + .9505 * r, this._alpha)
    }, rgb: function () {
        var e = this._x / 100, t = this._y / 100, n = this._z / 100, r = function (e) {
            return e > .0031308 ? 1.055 * Math.pow(e, 1 / 2.4) - .055 : 12.92 * e
        };
        return new ONECOLOR.RGB(r(3.2406 * e + -1.5372 * t + n * -.4986), r(e * -.9689 + 1.8758 * t + .0415 * n), r(.0557 * e + t * -.204 + 1.057 * n), this._alpha)
    }, lab: function () {
        var e = function (e) {
            return e > .008856 ? Math.pow(e, 1 / 3) : 7.787037 * e + 4 / 29
        }, t = e(this._x / 95.047), n = e(this._y / 100), r = e(this._z / 108.883);
        return new ONECOLOR.LAB(116 * n - 16, 500 * (t - n), 200 * (n - r), this._alpha)
    }
}), installColorSpace("LAB", ["l", "a", "b", "alpha"], {
    fromRgb: function () {
        return this.xyz().lab()
    }, rgb: function () {
        return this.xyz().rgb()
    }, xyz: function () {
        var e = function (e) {
            var t = Math.pow(e, 3);
            return t > .008856 ? t : (e - 16 / 116) / 7.87
        }, t = (this._l + 16) / 116, n = this._a / 500 + t, r = t - this._b / 200;
        return new ONECOLOR.XYZ(95.047 * e(n), 100 * e(t), 108.883 * e(r), this._alpha)
    }
}), installColorSpace("HSV", ["hue", "saturation", "value", "alpha"], {
    rgb: function () {
        var e, t, n, r = this._hue, o = this._saturation, a = this._value, i = Math.min(5, Math.floor(6 * r)),
            l = 6 * r - i, c = a * (1 - o), s = a * (1 - l * o), u = a * (1 - (1 - l) * o);
        switch (i) {
            case 0:
                e = a, t = u, n = c;
                break;
            case 1:
                e = s, t = a, n = c;
                break;
            case 2:
                e = c, t = a, n = u;
                break;
            case 3:
                e = c, t = s, n = a;
                break;
            case 4:
                e = u, t = c, n = a;
                break;
            case 5:
                e = a, t = c, n = s
        }
        return new ONECOLOR.RGB(e, t, n, this._alpha)
    }, hsl: function () {
        var e, t = (2 - this._saturation) * this._value, n = this._saturation * this._value, r = 1 >= t ? t : 2 - t;
        return e = 1e-9 > r ? 0 : n / r, new ONECOLOR.HSL(this._hue, e, t / 2, this._alpha)
    }, fromRgb: function () {
        var e, t = this._red, n = this._green, r = this._blue, o = Math.max(t, n, r), a = Math.min(t, n, r), i = o - a,
            l = 0 === o ? 0 : i / o, c = o;
        if (0 === i) e = 0; else switch (o) {
            case t:
                e = (n - r) / i / 6 + (r > n ? 1 : 0);
                break;
            case n:
                e = (r - t) / i / 6 + 1 / 3;
                break;
            case r:
                e = (t - n) / i / 6 + 2 / 3
        }
        return new ONECOLOR.HSV(e, l, c, this._alpha)
    }
}), installColorSpace("HSL", ["hue", "saturation", "lightness", "alpha"], {
    hsv: function () {
        var e, t = 2 * this._lightness, n = this._saturation * (1 >= t ? t : 2 - t);
        return e = 1e-9 > t + n ? 0 : 2 * n / (t + n), new ONECOLOR.HSV(this._hue, e, (t + n) / 2, this._alpha)
    }, rgb: function () {
        return this.hsv().rgb()
    }, fromRgb: function () {
        return this.hsv().hsl()
    }
}), installColorSpace("CMYK", ["cyan", "magenta", "yellow", "black", "alpha"], {
    rgb: function () {
        return new ONECOLOR.RGB(1 - this._cyan * (1 - this._black) - this._black, 1 - this._magenta * (1 - this._black) - this._black, 1 - this._yellow * (1 - this._black) - this._black, this._alpha)
    }, fromRgb: function () {
        var e = this._red, t = this._green, n = this._blue, r = 1 - e, o = 1 - t, a = 1 - n, i = 1;
        return e || t || n ? (i = Math.min(r, Math.min(o, a)), r = (r - i) / (1 - i), o = (o - i) / (1 - i), a = (a - i) / (1 - i)) : i = 1, new ONECOLOR.CMYK(r, o, a, i, this._alpha)
    }
}), ONECOLOR.installMethod("clearer", function (e) {
    return this.alpha(isNaN(e) ? -.1 : -e, !0)
}), ONECOLOR.installMethod("darken", function (e) {
    return this.lightness(isNaN(e) ? -.1 : -e, !0)
}), ONECOLOR.installMethod("desaturate", function (e) {
    return this.saturation(isNaN(e) ? -.1 : -e, !0)
}), ONECOLOR.installMethod("greyscale", gs), ONECOLOR.installMethod("grayscale", gs), ONECOLOR.installMethod("lighten", function (e) {
    return this.lightness(isNaN(e) ? .1 : e, !0)
}), ONECOLOR.installMethod("mix", function (e, t) {
    e = ONECOLOR(e).rgb(), t = 1 - (isNaN(t) ? .5 : t);
    var n = 2 * t - 1, r = this._alpha - e._alpha, o = ((n * r === -1 ? n : (n + r) / (1 + n * r)) + 1) / 2, a = 1 - o,
        i = this.rgb();
    return new ONECOLOR.RGB(i._red * o + e._red * a, i._green * o + e._green * a, i._blue * o + e._blue * a, i._alpha * t + e._alpha * (1 - t))
}), ONECOLOR.installMethod("negate", function () {
    var e = this.rgb();
    return new ONECOLOR.RGB(1 - e._red, 1 - e._green, 1 - e._blue, this._alpha)
}), ONECOLOR.installMethod("opaquer", function (e) {
    return this.alpha(isNaN(e) ? .1 : e, !0)
}), ONECOLOR.installMethod("rotate", function (e) {
    return this.hue((e || 0) / 360, !0)
}), ONECOLOR.installMethod("saturate", function (e) {
    return this.saturation(isNaN(e) ? .1 : e, !0)
}), ONECOLOR.installMethod("toAlpha", function (e) {
    var t = this.rgb(), n = ONECOLOR(e).rgb(), r = 1e-10, o = new ONECOLOR.RGB(0, 0, 0, t._alpha),
        a = ["_red", "_green", "_blue"];
    return a.forEach(function (e) {
        t[e] < r ? o[e] = t[e] : t[e] > n[e] ? o[e] = (t[e] - n[e]) / (1 - n[e]) : t[e] > n[e] ? o[e] = (n[e] - t[e]) / n[e] : o[e] = 0
    }), o._red > o._green ? o._red > o._blue ? t._alpha = o._red : t._alpha = o._blue : o._green > o._blue ? t._alpha = o._green : t._alpha = o._blue, t._alpha < r ? t : (a.forEach(function (e) {
        t[e] = (t[e] - n[e]) / t._alpha + n[e]
    }), t._alpha *= o._alpha, t)
}), function (e, t) {
    "object" == typeof exports ? module.exports = t(require("onecolor")) : "function" == typeof define && define.amd ? define(["onecolor"], t) : e.colorjoe = t(e.one.color)
}(this, function (e) {
    function t(e, t, n) {
        var r = document.createElement(e);
        return r.className = t, n.appendChild(r), r
    }

    function n(e) {
        var t = Array.prototype.slice, n = t.apply(arguments, [1]);
        return function () {
            return e.apply(null, n.concat(t.apply(arguments)))
        }
    }

    function r(e, t, n, r) {
        var i = M(e, n), l = o(t, i), c = a("text", i, r);
        return {label: l, input: c}
    }

    function o(e, n) {
        var r = t("label", "", n);
        return r.innerHTML = e, r
    }

    function a(e, n, r) {
        var o = t("input", "getColor", n);
        return o.type = e, r && (o.maxLength = r), o
    }

    function i(e, t) {
        e.style.left = s(100 * t, 0, 100) + "%"
    }

    function l(e, t) {
        e.style.top = s(100 * t, 0, 100) + "%"
    }

    function c(e, t) {
        e.style.background = t
    }

    function s(e, t, n) {
        return Math.min(Math.max(e, t), n)
    }

    function u(e) {
        var t = _.div("currentColorContainer", e), n = _.div("currentColor", t);
        return {
            change: function (e) {
                _.BG(n, e.cssa())
            }
        }
    }

    function d(e, t, n) {
        function r() {
            t.done()
        }

        function o(e) {
            e.ctrlKey || e.altKey || !/^[a-zA-Z]$/.test(e.key) || e.preventDefault()
        }

        function a() {
            var e = [i];
            h.forEach(function (t, n) {
                l instanceof Array ? e.push(t.e.input.value / l[n]) : e.push(t.e.input.value / l)
            }), d || e.push(t.getAlpha()), t.set(e)
        }

        // var i = n.space, l = n.limit || 255, c = n.fix >= 0 ? n.fix : 0, s = k(l) ? ("" + l).length + c : "3";
        // s = c ? s + 1 : s;
        // var u = i.split(""), d = "A" == i[i.length - 1];
        // if (i = d ? i.slice(0, -1) : i, ["RGB", "HSL", "HSV", "CMYK"].indexOf(i) < 0) return console.warn("Invalid field names", i);
        // var f = _.div("colorFields", e), h = u.map(function (e, t) {
        //     e = e.toLowerCase();
        //     var n = _.labelInput("color " + e, e, f, s);
        //     return n.input.onblur = r, n.input.onkeydown = o, n.input.onkeyup = a, {name: e, e: n}
        // });
        // return {
        //     change: function (e) {
        //         h.forEach(function (t, n) {
        //             l instanceof Array ? "l" === t.name ? t.e.input.value = (e.lightness() * l[n]).toFixed(c) : t.e.input.value = (e[t.name]() * l[n]).toFixed(c) : t.e.input.value = (e[t.name]() * l).toFixed(c)
        //         })
        //     }
        // }
    }

    function f(e, t) {
        function n() {
            t.done()
        }

        function r(e) {
            var n = _.clamp(e.y, 0, 1);
            _.Y(e.pointer, n), t.setAlpha(1 - n)
        }

        var o = L.slider({parent: e, "class": "oned alpha", cbs: {begin: r, change: r, end: n}});
        return {
            change: function (e) {
                _.Y(o.pointer, 1 - e.alpha())
            }
        }
    }
    getColorobj={}
    function h(e, t, n) {
        var r = _.labelInput("hex", n.label || "#", e, 7);
        return r.input.value = "#", r.input.onkeyup = function (e) {
            var n = e.keyCode || e.which, r = e.target.value;
            r = "#" == r[0] ? r : "#" + r, r = g(r, 7, "0"), 13 == n && t.set(r)
        }, r.input.onblur = function (e) {
            t.set(e.target.value), t.done()
        }, {
            change: function (e) {
                r.input.value = ("#" == r.input.value[0], ""), r.input.value += e.hex().slice(1)
                getColorobj.val=e.hex().slice(1)
            }
        }
    }

    function p(e, t, n) {
        var r = _.e("a", n["class"] || "close", e);
        r.href = "#", r.innerHTML = n.label || "Close", r.onclick = function (e) {
            e.preventDefault(), t.hide()
        }
    }

    function g(e, t, n) {
        for (var r = e, o = e.length; t > o; o++) r += n;
        return r
    }

    function m(t, n) {
        _.BG(t, new e.HSV(n, 1, 1).cssa())
    }

    function v(e) {
        function t(e) {
            u = i.xy(u, {x: _.clamp(e.x, 0, 1), y: _.clamp(e.y, 0, 1)}, l, c), r()
        }

        function n(e) {
            u = i.z(u, _.clamp(e.y, 0, 1), l, c), r()
        }

        function r(e) {
            e = k(e) ? e : [];
            for (var t, n = d.change, r = 0, o = n.length; o > r; r++) t = n[r], -1 == e.indexOf(t.name) && t.fn(u)
        }

        function o() {
            if (!s.equals(u)) {
                for (var e = 0, t = d.done.length; t > e; e++) d.done[e].fn(u);
                s = u
            }
        }

        if (!e.e) return console.warn("colorjoe: missing element");
        var a = x(e.e) ? document.getElementById(e.e) : e.e;
        a.className = "colorPicker";
        var i = e.cbs, l = L.xyslider({parent: a, "class": "twod", cbs: {begin: t, change: t, end: o}}),
            c = L.slider({parent: a, "class": "oned", cbs: {begin: n, change: n, end: o}}), s = b(e.color),
            u = i.init(s, l, c), d = {change: [], done: []}, f = {
                e: a, done: function () {
                    return o(), this
                }, update: function (e) {
                    return r(e), this
                }, hide: function () {
                    return a.style.display = "none", this
                }, show: function () {
                    return a.style.display = "", this
                }, get: function () {
                    return u
                }, set: function (e) {
                    var t = this.get();
                    return u = i.init(b(e), l, c), t.equals(u) || this.update(), this
                }, getAlpha: function () {
                    return u.alpha()
                }, setAlpha: function (e) {
                    return u = u.alpha(e), this.update(), this
                }, on: function (e, t, n) {
                    return "change" == e || "done" == e ? d[e].push({
                        name: n,
                        fn: t
                    }) : console.warn('Passed invalid evt name "' + e + '" to colorjoe.on'), this
                }, removeAllListeners: function (e) {
                    if (e) delete d[e]; else for (var t in d) delete d[t];
                    return this
                }
            };
        return y(a, f, e.extras), r(), f
    }

    function b(t) {
        if (!N(t)) return e("#000");
        if (t.isColor) return t;
        var n = e(t);
        return n ? n : (N(t) && console.warn("Passed invalid color to colorjoe, using black instead"), e("#000"))
    }

    function y(e, t, n) {
        if (n) {
            var r, o, a, i = _.div("extras", e);
            n.forEach(function (e, n) {
                if (k(e) ? (o = e[0], a = e.length > 1 ? e[1] : {}) : (o = e, a = {}), extra = o in T._extras ? T._extras[o] : null, extra) {
                    r = extra(i, C(t, o + n), a);
                    for (var l in r) t.on(l, r[l], o)
                }
            })
        }
    }

    function C(e, t) {
        var n = w(e);
        return n.update = function () {
            e.update([t])
        }, n
    }

    function w(e) {
        var t = {};
        for (var n in e) t[n] = e[n];
        return t
    }

    function E(e, t) {
        return t.map(e).filter(O).length == t.length
    }

    function k(e) {
        return "[object Array]" === Object.prototype.toString.call(e)
    }

    function x(e) {
        return "string" == typeof e
    }

    function N(e) {
        return "undefined" != typeof e
    }

    function S(e) {
        return "function" == typeof e
    }

    function O(e) {
        return e
    }

    var L = function () {
            function e(e, t) {
                return e ? void(i() ? l(e, t, "touchstart", "touchmove", "touchend") : l(e, t, "mousedown", "mousemove", "mouseup")) : void console.warn("drag is missing elem!")
            }

            function t(t) {
                var n = o(t["class"] || "", t.parent), a = o("pointer", n);
                return o("shape shape1", a), o("shape shape2", a), o("bg bg1", n), o("bg bg2", n), e(n, r(t.cbs, a)), {
                    background: n,
                    pointer: a
                }
            }

            function n(t) {
                var n = o(t["class"], t.parent), a = o("pointer", n);
                return o("shape", a), o("bg", n), e(n, r(t.cbs, a)), {background: n, pointer: a}
            }

            function r(e, t) {
                function n(e) {
                    return function (n) {
                        n.pointer = t, e(n)
                    }
                }

                var r = {};
                for (var o in e) r[o] = n(e[o]);
                return r
            }

            function o(e, t) {
                return a("div", e, t)
            }

            function a(e, t, n) {
                var r = document.createElement(e);
                return t && (r.className = t), n.appendChild(r), r
            }

            function i() {
                return "undefined" != typeof window.ontouchstart
            }

            function l(e, t, n, r, o) {
                var a = !1;
                t = u(t);
                var i = t.begin, l = t.change, d = t.end;
                c(e, n, function (t) {
                    function n() {
                        a = !1, s(document, r, u), s(document, o, n), h(d, e, t)
                    }

                    a = !0;
                    var u = p(h, l, e);
                    c(document, r, u), c(document, o, n), h(i, e, t)
                })
            }

            function c(e, t, n) {
                e.addEventListener ? e.addEventListener(t, n, !1) : e.attachEvent && e.attachEvent("on" + t, n)
            }

            function s(e, t, n) {
                e.removeEventListener ? e.removeEventListener(t, n, !1) : e.detachEvent && e.detachEvent("on" + t, n)
            }

            function u(e) {
                if (e) return {begin: e.begin || f, change: e.change || f, end: e.end || f};
                var t, n;
                return {
                    begin: function (e) {
                        t = {x: e.elem.offsetLeft, y: e.elem.offsetTop}, n = e.cursor
                    }, change: function (e) {
                        d(e.elem, "left", t.x + e.cursor.x - n.x + "px"), d(e.elem, "top", t.y + e.cursor.y - n.y + "px")
                    }, end: f
                }
            }

            function d(e, t, n) {
                e.style[t] = n
            }

            function f() {
            }

            function h(e, t, n) {
                n.preventDefault();
                var r = g(t), o = t.clientWidth, a = t.clientHeight, i = {x: m(t, n), y: v(t, n)}, l = (i.x - r.x) / o,
                    c = (i.y - r.y) / a;
                e({x: isNaN(l) ? 0 : l, y: isNaN(c) ? 0 : c, cursor: i, elem: t, e: n})
            }

            function p(e) {
                var t = Array.prototype.slice, n = t.apply(arguments, [1]);
                return function () {
                    return e.apply(null, n.concat(t.apply(arguments)))
                }
            }

            function g(e) {
                var t = 0, n = 0;
                if (e.offsetParent) do t += e.offsetLeft, n += e.offsetTop; while (e = e.offsetParent);
                return {x: t, y: n}
            }

            function m(e, t) {
                if (y(e)) {
                    var n = parseInt(C(document.body, "marginLeft"), 10) - b(e, "scrollLeft") + window.pageXOffset + e.style.marginLeft;
                    return t.clientX - n
                }
                return t.pageX ? t.pageX : t.clientX ? t.clientX + document.body.scrollLeft : void 0
            }

            function v(e, t) {
                if (y(e)) {
                    var n = parseInt(C(document.body, "marginTop"), 10) - b(e, "scrollTop") + window.pageYOffset + e.style.marginTop;
                    return t.clientY - n
                }
                return t.pageY ? t.pageY : t.clientY ? t.clientY + document.body.scrollTop : void 0
            }

            function b(e, t) {
                for (var n = 0; "HTML" != e.nodeName;) n += e[t], e = e.parentNode;
                return n
            }

            function y(e) {
                for (; "HTML" != e.nodeName && "fixed" != w(e, "position");) e = e.parentNode;
                return "HTML" == e.nodeName ? !1 : !0
            }

            function C(e, t) {
                return e.currentStyle ? e.currentStyle[t] : document.defaultView && document.defaultView.getComputedStyle ? document.defaultView.getComputedStyle(e, "")[t] : e.style[t]
            }

            function w(e, t) {
                var n;
                return n = window.getComputedStyle ? window.getComputedStyle(e, null) : e.currentStyle, n[t]
            }

            return e.xyslider = t, e.slider = n, e
        }(), M = n(t, "div"), _ = {clamp: s, e: t, div: M, partial: n, labelInput: r, X: i, Y: l, BG: c},
        A = {currentColor: u, fields: d, hex: h, alpha: f, close: p}, T = function (e) {
            return E(S, [e.init, e.xy, e.z]) ? function (t, n, r) {
                return v({e: t, color: n, cbs: e, extras: r})
            } : console.warn("colorjoe: missing cb")
        };
    T.rgb = T({
        init: function (t, n, r) {
            var o = e(t).hsv();
            return this.xy(o, {x: o.saturation(), y: 1 - o.value()}, n, r), this.z(o, o.hue(), n, r), o
        }, xy: function (e, t, n, r) {
            return _.X(n.pointer, t.x), _.Y(n.pointer, t.y), e.saturation(t.x).value(1 - t.y)
        }, z: function (e, t, n, r) {
            return _.Y(r.pointer, t), m(n.background, t), e.hue(t)
        }
    }), T.hsl = T({
        init: function (t, n, r) {
            var o = e(t).hsl();
            return this.xy(o, {x: o.hue(), y: 1 - o.saturation()}, n, r), this.z(o, 1 - o.lightness(), n, r), o
        }, xy: function (e, t, n, r) {
            return _.X(n.pointer, t.x), _.Y(n.pointer, t.y), m(r.background, t.x), e.hue(t.x).saturation(1 - t.y)
        }, z: function (e, t, n, r) {
            return _.Y(r.pointer, t), e.lightness(1 - t)
        }
    }), T._extras = {}, T.registerExtra = function (e, t) {
        e in T._extras && console.warn('Extra "' + e + '"has been registered already!'), T._extras[e] = t
    };
    for (var R in A) T.registerExtra(R, A[R]);
    return T
});
var onecolor = one.color, Harmonizer = function () {
    var e = this, t = {
        complementary: [0, 180],
        splitComplementary: [0, 150, 320],
        splitComplementaryCW: [0, 150, 300],
        splitComplementaryCCW: [0, 60, 210],
        triadic: [0, 120, 240],
        clash: [0, 90, 270],
        tetradic: [0, 90, 180, 270],
        fourToneCW: [0, 60, 180, 240],
        fourToneCCW: [0, 120, 180, 300],
        fiveToneA: [0, 115, 155, 205, 245],
        fiveToneB: [0, 40, 90, 130, 245],
        fiveToneC: [0, 50, 90, 205, 320],
        fiveToneD: [0, 40, 155, 270, 310],
        fiveToneE: [0, 115, 230, 270, 320],
        sixToneCW: [0, 30, 120, 150, 240, 270],
        sixToneCCW: [0, 90, 120, 210, 240, 330],
        neutral: [0, 15, 30, 45, 60, 75],
        analogous: [0, 30, 60, 90, 120, 150]
    }, n = function (e) {
        var t = onecolor(e);
        return t || (t = onecolor("#000000")), t
    }, r = function (e, t) {
        var n, r, o, a, i, l, c, s = [];
        for (n = e.hsl(), r = n._hue, o = n._saturation, a = n._lightness, i = n._alpha, l = 0; l < t.length; l++) c = t[l], isFinite(c) && "number" == typeof c && s.push(new onecolor.HSL((r + 1 / 360 * c) % 1, o, a, i).hex());
        return s
    }, o = function (e, t, n) {
        var r, o, a, i, l, c, s, u, d = [];
        for (isFinite(t) && "number" == typeof t || (t = 10), o = e.red(), a = e.green(), i = e.blue(), l = e.alpha(), c = (n - o) / t, s = (n - a) / t, u = (n - i) / t, r = 0; t > r; r++) d.push(new onecolor.RGB(o, a, i, l).hex()), o += c, a += s, i += u;
        return d
    };
    return e.add = function (e, n) {
        Array.isArray(n) && (t[e] = n)
    }, e.harmonizeAll = function (e) {
        var o = {}, a = n(e);
        for (var i in t) t.hasOwnProperty(i) && (o[i] = r(a, t[i]));
        return o
    }, e.harmonize = function (e, o) {
        var a = n(e);
        return t.hasOwnProperty(o) && (o = t[o]), Array.isArray(o) ? r(a, o) : []
    }, e.shades = function (e, t) {
        return o(n(e), t, 0)
    }, e.tints = function (e, t) {
        return o(n(e), t, 1)
    }, e.tones = function (e, t) {
        return o(n(e), t, .5)
    }, e
};
!function () {
    function e() {
        t()
    }

    function t() {
        u && u.addEventListener("click", n), d && d.addEventListener("click", n), o && o.addEventListener("click", function (e) {
            var t = e.target;
            f && t !== u && n()
        })
    };

}()
    , window.onresize = function () {
    document.getElementById("home") || document.getElementById("picker") ? (pickerHeight(), colorNav()) : colorNav()}, "addEventListener" in document && document.addEventListener("DOMContentLoaded", function () {
    FastClick.attach(document.body)
}, !1),
    hljs.initHighlightingOnLoad(),  loadColor(), mobileColor(), document.getElementById("home") ? (colorPicker(), pickerHeight(), refreshColor(".js-color")) : document.getElementById("picker") ? (colorPicker(), pickerHeight()) : refreshColor(".js-color"), document.getElementById("export") && colorExport();
