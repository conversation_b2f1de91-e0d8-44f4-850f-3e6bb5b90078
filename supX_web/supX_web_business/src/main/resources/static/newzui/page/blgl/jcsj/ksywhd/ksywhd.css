.tong-btn {
  width: auto;
  min-width: 72px;
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border: none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right: 10px;
}
.font12 {
  font-size: 12px !important;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}
.wh240 {
  width: 240px!important;
}
.wh182 {
  width: 182px !important;
}
.wh100 {
  width: 100px !important;
}
.wh66 {
  width: 66px !important;
}
.wh112 {
  width: 112px !important;
}
.wh120 {
  width: 120px !important;
}
.wh122 {
  width: 122px !important;
}
.wh138 {
  width: 138px !important;
}
.wh200 {
  width: 200px !important;
}
.wh220 {
  width: 220px !important;
}
.wh150 {
  width: 150px !important;
}
.wh1000 {
  width: 80% !important;
}
.wh50 {
  width: 50px !important;
}
.wh70 {
  width: 70px !important;
}
.width162 {
  width: 162px !important;
}
.wh160 {
  width: 160px !important;
}
.wh453 {
  width: 453px !important;
}
.wh247 {
  width: 243px !important;
  display: flex;
  justify-content: start;
  align-items: center;
}
.wh179 {
  width: 179px !important;
}
.wh59 {
  width: 59px !important;
}
.padd {
  padding: 0 !important;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-ed {
  background: #edf2f1 !important;
}
.color-green {
  color: #1abc9c !important;
  font-style: normal;
}
.color-dsh {
  color: #f3b169;
}
.color-ysh {
  color: #45e135;
}
.color-wtg {
  color: #ff4735;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.icon-bj:before {
  top: 4px;
  width: 20px;
  height: 16px;
}
#jyxm_icon .switch {
  left: 12% !important;
  top: -23% !important;
}
.tab-edit-list li label .label-input {
  text-indent: 10px !important;
}
.ksys-side span {
  margin-bottom: 15px !important;
}
.ksys-side span i {
  line-height: 30px;
}
.border-dotted-t {
  border-top: 1px dashed #1abc9c;
}
.fl {
  float: left;
}
.zui-select-inline {
  margin-right: 0 !important;
}
.ksywhd-left {
  width: 220px;
  float: left;
  position: relative;
}
.ksywhd-left .ksywhd-top {
  float: left;
  width: 100%;
  height: 31px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  left: 0;
  padding: 0 0 0 20px;
  top: 48px;
  z-index: 11;
  background: #fff;
  border: 1px solid #eee;
}
.ksywhd-left .ksywhd-top .ksywhd-title {
  padding-left: 10px;
  cursor: pointer;
}
.ksywhd-left .ksywhd-top.active {
  background: rgba(26, 188, 156, 0.1) !important;
  color: #1abc9c;
}
.ksywhd-left .ksywhd-ej {
  width: 100%;
  float: left;
  margin-top: 35px;
  padding-top: 10px;
  border: 1px solid #eee;
  overflow: auto;
  border-top: none;
}
.ksywhd-left .ksywhd-ej li {
  padding-top: 10px;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 42px;
  box-sizing: border-box;
}
.ksywhd-right {
  width: 539px;
  float: right;
  position: relative;
}
.ksywhd-right .right-top {
  width: 100%;
  background: #edf2f1;
  height: 36px;
  line-height: 36px;
  position: absolute;
  top: 48px;
  left: 0;
}
.ksywhd-right .right-top i {
  display: block;
  float: left;
  text-align: center;
  width: calc((100% - 50px) / 5);
}
.ksywhd-right .right-top i:first-child {
  width: 50px !important;
}
.ksywhd-right .right-list {
  margin-top: 35px;
  width: 100%;
  overflow: auto;
}
.ksywhd-right .right-list li {
  width: 100%;
  line-height: 40px;
  height: 40px;
  overflow: hidden;
  border: 1px solid #eee;
  border-top: none;
}
.ksywhd-right .right-list li i {
  display: block;
  float: left;
  text-align: center;
  width: calc((100% - 50px) / 5);
}
.ksywhd-right .right-list li i:first-child {
  width: 50px !important;
}
.zui-form-label {
  width: auto !important;
}
.zui-form .zui-inline {
  padding: 0 20px 0 37px;
}
