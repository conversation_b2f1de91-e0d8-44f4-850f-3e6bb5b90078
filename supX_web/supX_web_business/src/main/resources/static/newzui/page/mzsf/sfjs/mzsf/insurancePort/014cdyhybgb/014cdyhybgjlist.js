var cd_014gjlist = new Vue({
    el: '.cd_014gjlist',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        
        isShow: false,
        bxlbbm: null,
        bxurl: null,
        
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        userInfo: {},
		lsjsxx:[],
    },
    created: function () {
        this.init();
    },
    mounted: function () {
        this.isShow = true;
        this.getbxlb();
        this.getUserInfo();
    },
    methods: {
        init: function () {
            this.lsjsxx = rightVue.lsjsxx;
        },
        
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    cd_014gjlist.userInfo = json.body.d;
                });
        },
        getbxlb: function () {
            var that = this;
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    that.bxlbbm = json.d.list[0].bxlbbm;
                    that.bxurl = json.d.list[0].url;
                } else {
                    malert("保险类别查询失败!" + json.c, 'right', 'defeadted')
                }
            });
        },
        qdFun: function () {
            //签到
            window.insuranceGbUtils.qd();
        },
        loadqd: function () {
            window.insuranceGbUtils.qd();
        },
        
        emptyReplace: function (para) {
            if (para == null || typeof (para) == "undefined") {
                return '';
            }
            return para;
        },
        toGroupBy: function (array, fn) {
                        const groups = {};
            array.forEach(function (item) {
                const group = JSON.stringify(fn(item));
                //这里利用对象的key值唯一性的，创建数组
                groups[group] = groups[group] || [];
                groups[group].push(item);
            });


            //最后再利用map循环处理分组出来
            return Object.keys(groups).map(function (group) {
                return groups[group];
            });
        },
		
        sleep: function (delay) {
                        var start = (new Date()).getTime();
            let end = (new Date()).getTime();
            while ((end - start) < delay) {
                end = (new Date()).getTime()
            }
            return true;
        },
        decodeUnicode: function (str) {
            //先把十六进制unicode编码/u替换为%u
            str = str.replace(/\\u/gi, '%u');
            //再把页面中反斜杠替换为空
            str = str.replace(/\\/gi, '');
            return unescape(str);
        }
    }
});


$(document).click(function () {
    if (this.className != 'selectGroup') {
        $(".selectGroup").hide();
    }
    $(".popInfo ul").hide();
});
