<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>退库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
    <link type="text/css" href="tkgl.css" rel="stylesheet" />
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
    <div class="background-box">
        <div class="wrapper" id="jyxm_icon">
            <div class="panel printHide" v-cloak>
                <div class="tong-top">
                    <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-show="isShowkd">开单</button>
                    <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kd(1)" v-if="isShowpopL">添加材料</button>
                    <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="sx" v-show="yshShow">刷新</button>
                    <button class="tong-btn btn-parmary icon-sx1 paddr-r5 icon-font14" @click="sx" v-show="!yshShow">刷新</button>
                    <!--<button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="clearAll()" v-show="TjShow">删除</button>-->
                </div>
                <div class="tong-search" :class="{'tong-padded':isShow}">
                    <div class="flex-container flex-align-c padd-b-10" v-show="isShowkd">
                        <div class="flex-container flex-align-c padd-r-10">
                            <label class="ft-14 padd-r-10 whiteSpace ">一级库房</label>
                                <select-input class="wh122" @change-data="resultRydjChange" :child="KFList"
                                    :index="'kfmc'" :index_val="'kfbm'" :val="popContent.kfbm" :name="'popContent.kfbm'"
                                    :search="true" :index_mc="'kfmc'">
                                </select-input>
                        </div>
                        <div class="flex-container flex-align-c padd-r-10">
                            <span class="ft-14 padd-r-10 whiteSpace ">审核标志</span>
                                <select-input @change-data="resultChange"
                                              :child="ckglzt_tran"
                                              class="wh122"
                                              :index="popContent.zt"
                                              :val="popContent.zt"
                                              :name="'popContent.zt'" @keydown="nextFocus($event)" >
                                </select-input>
                        </div>
                        <div class="flex-container flex-align-c padd-r-10">
                            <span class="ft-14 padd-r-10 whiteSpace">时间段</span>
                            <div class=" margin-f-l5 flex-container flex-align-c">
                                <input class="zui-input todate wh120 text-indent20" placeholder="请选择申请日期"
                                    id="timeVal" /><span class="padd-l-5 padd-r-5">~</span>
                                <input class="zui-input todate wh120 " placeholder="请选择处方结束时间" id="timeVal1" />
                            </div>
                        </div>
                        <div class="flex-container flex-align-c">
                            <span class="ft-14 padd-r-10 whiteSpace">检索</span>
                                <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="search" />
                        </div>
                    </div>
                    <div class="jbxx" v-show="isShow">
                        <div class="jbxx-size">
                            <div class="jbxx-position">
                                <span class="jbxx-top"></span>
                                <span class="jbxx-text">基本信息</span>
                                <span class="jbxx-bottom"></span>
                            </div>
                            <div class="zui-form padd-l24 padd-t-20 grid-box">
                                <div class="zui-inline">
                                    <label class="zui-form-label ">库房</label>
                                    <div class="zui-input-inline wh122 margin-f-l20">
                                        <select-input @change-data="resultRydjChange" :child="KFList" :index="'kfmc'"
                                            :index_val="'kfbm'" :val="popContent.kfbm" :name="'popContent.kfbm'"
                                            :search="true" :index_mc="'kfmc'" :disable="jyinput">
                                        </select-input>
                                    </div>
                                </div>
                                <div class="zui-inline" style="width: 50%;">
                                    <label class="zui-form-label">备注</label>
                                    <div class="zui-input-inline margin-f-l20">
                                        <input class="zui-input" placeholder="请输入备注" type="text" id="bzms"
                                            v-model="popContent.bzms" :disabled="jyinput" />
                                    </div>
                                </div>

                            </div>

                            <div class="rkgl-kd">
                                <span>开单日期:<i v-text="zdrq"></i></span>
                                <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="zui-table-view  padd-r-10 padd-l-10"  v-cloak>
                <!--入库列表-->
                <div class="zui-table-header" v-show="isShowkd">
                    <table class="zui-table">
                        <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl"><span>退库单号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>退库方式</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>退库科室</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>退库二级库房</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>发票号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>制单员</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>制单日期</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>SPD单号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>状态</span></div>
                                </th>
                                <th class="cell-l printHide">
                                    <div class="zui-table-cell cell-l"><span>操作</span></div>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body " v-show="isShowkd" id="zui-table" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                            <tr :tabindex="$index" v-for="(item,$index) in tkdList"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                @dblclick="showDetail($index,item)">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl" v-text="item.rkdh">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="rkfs_tran[item.rkfs]">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.tkksmc">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.tkyfmc">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fphm">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.zdyxm">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="fDate(item.zdrq,'AllDate')">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="item.spdno">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <i v-text="zhuangtai[item.shzfbz]"
                                            :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : item.shzfbz=='3' ? 'color-wtg':'' "></i>
                                    </div>
                                </td>
                                <td class="cell-l">
                                    <div class="zui-table-cell cell-l ">
                                        <span class="flex-center padd-t-5">
                                            <em class="width30" v-if="item.shzfbz == '0'">
                                                <i class="icon-sh" data-title="审核" @click="showDetail($index,item)"></i>
                                            </em>
                                            <em class="width30" v-if="item.shzfbz == '0'">
                                                <i class="icon-js" data-title="作废" @click="invalidData($index)"></i>
                                            </em>
                                            <em class="width30" v-if="item.shzfbz != '1' && item.shzfbz !='2'">
                                                <i class="icon-bj" data-title="编辑"
                                                    @click="editIndex($index,item.zdyxm)"></i>
                                            </em>
                                        </span>
                                    </div>
                                </td>
                                <p v-show="tkdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>

                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--添加材料-->
                <div class="zui-table-header " v-show="isShow">
                    <table class="zui-table">
                        <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl text-left"><span>供货单位</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料规格</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>退库数量</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料进价</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料零价</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料批号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料产地</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                                </th>
                                <th class="cell-s printHide">
                                    <div class="zui-table-cell cell-s"><span>操作</span></div>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body " v-show="isShow" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                            <tr v-for="(item,$index) in jsonList" :tabindex="$index"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ghdwmc">序号
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc">序号
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.ypgg">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                       {{item.rksl}}{{item.kfdwmc}}
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.ypjj">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.yplj">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.scph">状态</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">状态</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s text-over-2" v-text="item.cdmc">状态</div>
                                </td>
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                                </td>
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-s flex-container flex-align-c flex-jus-c">
                                        <span class="icon-bj  icon-bj-t width30 " v-if="mxShShow" data-title="编辑" @click="edit($index)"></span>
                                        <span class="icon-sc width30 " v-if="mxShShow" data-title="删除" @click="scmx($index)"></span></span>
                                    </div>
                                </td>
                            </tr>
                            <p v-show="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                        </tbody>
                    </table>
                </div>
                <div class="zui-table-fixed table-fixed-l" v-show="isShow">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                                <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2">
                                    <td>
                                        <div class="zui-table-cell cell-m">{{$index+1}}</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="zui-table-fixed table-fixed-r" v-show="isShow">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                                <tr>
                                    <th class="cell-s">
                                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                                <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2">
                                    <td class="cell-s">
                                        <div class="zui-table-cell cell-s">
                                            <span class="icon-bj  icon-bj-t width30 " v-if="mxShShow" data-title="编辑" @click="edit($index)"></span>
                                            <span class="icon-sc width30 " v-if="mxShShow" data-title="删除" @click="scmx($index)"></span></span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <page @go-page="goPage" :totle-page="totlePage" v-show="isShowkd" class="printHide" :page="page"
                    :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
                <div class="rkgl-position flex-container padd-l-10 padd-r-10 flex-jus-sb printHide" v-show="isShow">
                    <div class="flex-container " :id="money">
                        <p class="padd-r-10">材料进价总价: <em class="color-wtg">{{fDec(json.jjzj,2)}}元</em></p>
                        <p>材料零价总价: <em class="color-wtg">{{fDec(json.ljzj,2)}}元</em></i>
                    </div>
                    <div class="flex-container ">
                        <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="print()" v-if="dyShow">打印</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()"
                            v-show="zfShow">作废</button>
                        <!--  <button class="tong-btn btn-parmary-f2a" @click="jujue" v-show="ShShow">拒绝</button> -->
                        <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow">提交</button>
                        <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow">审核</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--侧边窗口-->
    <div class="side-form ng-hide pop-548 printHide" v-cloak id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--编辑材料-->
        <div class="ksys-side">
            <ul class="tab-edit-list tab-edit2-list">
                <li>
                    <i>退库方式</i>
                    <input class="zui-input" disabled="disabled" value="退货入库" @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>制单日期</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" class="zui-input text-indent20" v-model="zdrq" disabled="disabled"
                        @keydown="nextFocus($event)" />
                </li>
                <li>
                    <i>退库科室</i>
                    <select-input ref="autofocus" @change-data="resultKsChange" :child="KSList" :index="'ksmc'"
                        :index_val="'ksbm'" :val="tkdContent.lyks" :search="true" :name="'tkdContent.lyks'" id="lyks"
                        @keydown="nextFocus($event)">
                    </select-input>
                </li>
                <li>
                    <i>退库二级库房</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="tkdContent.lyyfmc"
                        @keydown="nextFocus($event)" />
                </li>
                <li>
                    <i>材料名称</i>
                    <input id="ypmc" class="zui-input" v-model="popContent.ypmc" @keydown.13="changeDown($event,'ypmc')"
                        @input="change(false,$event.target.value)">
                    <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them"
                        :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </li>
                <li>
                    <i>供货单位</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="popContent.ghdwmc"
                        @keydown="nextFocus($event)" />
                </li>

                <li>
                    <i>退库数量</i>
                    <input type="number" class="zui-input" id="rksl" v-model="popContent.rksl"
                        @keydown="nextFocus($event)" @keyup.enter="changeDown($event,'kcsl')" />
                </li>
                <li>
                    <i>材料规格</i>
                    <input class="zui-input" v-model="popContent.ypgg" disabled="disabled"
                        @keydown="nextFocus($event)" />
                </li>
                <li>
                    <i>库存数量</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.ykkc"
                        @keydown="nextFocus($event)" />
                    <em style="position: absolute;top: 10px;right: 10px; z-index: 111;" v-text="popContent.kfdwmc"></em>
                </li>
                <li>
                    <i>材料进价</i>
                    <input type="number" class="zui-input" disabled="disabled" v-model="popContent.ypjj"
                        @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>材料零价</i>
                    <input type="number" class="zui-input" disabled="disabled" v-model="popContent.yplj"
                        @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>材料批号</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="popContent.scph"
                        @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>有效期至</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" class="zui-input text-indent20" id="yxqz" disabled="disabled"
                        :value="fDate(popContent.yxqz,'date')" @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>材料产地</i>
                    <input type="text" class="zui-input" v-model="popContent.cdmc" @keydown="nextFocus($event)"
                        disabled>
                </li>

                <li>
                    <i>分装比例</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="popContent.fzbl"
                        @keydown.13="addData($event)">
                </li>
                <li>
                    <i>产品标准&ensp;&ensp;号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.cpbzh"
                        @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>批准文号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.pzwh"
                        @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>生产日期</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" class="zui-input  text-indent20" id="scrq" disabled="disabled"
                        :value="fDate(popContent.scrq,'date')" @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>系统批号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.xtph"
                        @keydown="nextFocus($event)">
                </li>
            </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="addData()">保存</button>
        </div>
    </div>

    <script src="tkgl.js"></script>

</body>

</html>
