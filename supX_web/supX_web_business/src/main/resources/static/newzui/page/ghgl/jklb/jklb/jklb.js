/********************************华丽分割线***************************************/
    //财务交款列表展示
(function () {
    var dateend = getTodayDateEnd();
    var datestart = getTodayDateBegin();

var tableInfo = new Vue({
        el: '#cwjkList',
        mixins: [dic_transform, tableBase, mformat],
        data: {
            jsonList: [],
            isShow: false,
            isChecked: [],
            qxjk: true,//取消交款按钮
            cwsj: true,//财务上交按钮
            qxsj: true //取消上交按钮
        },
        //页面渲染完成之后加载数据
        mounted: function () {
            $("#startRq").val(datestart);
            $("#endtRq").val(dateend);
        },
        methods: {
            // 选中单条
            checkOne: function (event,index) {
                if(event.srcElement.checked==true){
                    this.isChecked[index] = false;
                }else{
                    this.isChecked[index] = true;
                }
            },
            // 选中全部
            checkAll: function (event) {
                if (event.srcElement.checked==true) {
                    for (var i = 0; i < this.jsonList.length; i++) {
                        Vue.set(this.isChecked,i,true)
                        // this.isChecked[i] = true;
                    }
                } else {
                    this.isChecked = [];
                }
            },
            getData: function () {
                //获取检索参数
                var beginrq = $("#startRq").val();
                var endrq = $("#endtRq").val();
                this.param.beginrq = beginrq;
                this.param.endrq = endrq;
                this.param.sort = "jkrq";
                this.param.order = "desc";
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=queryCwjk&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                        tableInfo.jsonList = json.d.list;
                    } else {
                        malert('查询失败'+json.c,'top','defeadted');
                    }
                });
            },


            //列表页面查询查询回车键
            searchListHc: function () {
                if (window.event.keyCode == 13) {
                    this.getData();
                }
            },
            //双击查看
            edit: function (num) {
                if (num == null) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if (num == null) {
                        return false;
                    }
                }
                //判断一下是否作废，如果已经作废则不允许查看
                if (this.jsonList[num].jkzt == 1) {
                    return false;
                }
                //查询后台
                var jkpzh = this.jsonList[num].jkpzh;
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=selectByPrimaryKey&jkpzh=" + jkpzh, function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (json.a == 0) {
                        if (json.d != null && json.d != "") {
                            zd_enter.popContent = {};//总体费用信息
                            zd_enter.brfbList = [];//病人费别
                            zd_enter.fylbList = [];//费用项目
                            zd_enter.mxfyList = []; //门诊费用明细

                            zd_enter.popContent = json.d;//总的费用情况
                            /*zd_enter.popContent.jkje =*/
                            Vue.set(zd_enter.popContent, 'jkjedx', numToCn(zd_enter.popContent['jkje'])); //转换成大写金额
                            var jkrq = zd_enter.fDate(zd_enter.popContent.jkrq, "date");
                            Vue.set(zd_enter.popContent, 'jkrq', jkrq);
                            zd_enter.brfbList = json.d.ryfbList; //病人费别
                            zd_enter.fylbList = json.d.fylbList; //费用项目
                            //查询票据信息
                            tableInfo.getFphm(jkpzh);
                        }
                    } else {
                        malert('查询失败'+json.c,'top','defeadted');
                    }
                });
                //隐藏按钮
                zd_enter.toolMenu = false;
                zd_enter.mzjk = false;
                //菜单切换显示隐藏
                tableInfo.isShow = false;
                zd_enter.isShow = true;
                InfoMenu.isEnter = false;
            },

            //请求后台获取到发票号码段
            getFphm: function (jkpzh) {
                $.getJSON("/actionDispatcher.do?reqUrl=MzsfSfjsCwjkFphm&types=queryFphmByjkpzh&jkpzh=" + jkpzh, function (json) {
                    if (json.a == 0) {
                        zd_enter.fphmPopContent = json.d;//发票信息
                    } else {
                        malert('发票查询失败:'+json.c,'top','defeadted');
                    }
                });
            },

            //取消交款
            qxjkData: function () {
                if (tableInfo.isChecked.length == 0) {
                    malert('请选中您要操作的数据:','top','defeadted');
                    return;
                }
                var jkpzh = '';
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        if (tableInfo.jsonList[i]['jkzt'] == '1') {//交款单作废时不可以取消交款
                            malert('此记录已作废不鞥进行取消交款:','top','defeadted');
                            return;
                        }
                        if (tableInfo.jsonList[i]['sjbz'] == '1') {//交款单已上交时不可以取消交款
                            malert('此记录已上交不鞥进行取消交款:','top','defeadted');
                            return;
                        }
                        jkpzh = tableInfo.jsonList[i]['jkpzh'];
                    }
                }
                var json = {
                    jkpzh: jkpzh
                };
                this.$http.post('/actionDispatcher.do?reqUrl=MzsfSfjsCwjk&types=update&',
                    JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        malert('取消交款成功:','top','success');
                        tableInfo.getData();
                    } else {
                        malert('取消交款失败:'+data.body.c,'top','success');
                    }
                }, function (error) {
                    console.log(error);
                });
            },
            //财务上交
            cwsjData: function () {
                if (tableInfo.isChecked.length == 0) {
                    malert('请选中您要操作的数据:','top','defeadted');
                    return;
                }
                var jkpzh = '';
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        if (tableInfo.jsonList[i]['jkzt'] == '1') {//交款单作废时不可以财务上交
                            malert('此记录已作废不鞥进行财务上交','top','defeadted');
                            return;
                        }
                        if (tableInfo.jsonList[i]['sjbz'] == '1') {//交款单已上交时不可以再次进行上交
                            malert('此记录已上交不鞥进行财务上交','top','defeadted');
                            return;
                        }
                        jkpzh = tableInfo.jsonList[i]['jkpzh']
                    }
                }
                var json = {
                    jkpzh: jkpzh
                };
                this.$http.post('/actionDispatcher.do?reqUrl=MzsfSfjsCwjkjl&types=save&',
                    JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        malert('财务上交成功','top','success');
                        tableInfo.getData();
                    } else {
                        malert('交款失败:'+data.body.c,'top','defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            },
            //取消财务上交
            qxsjData: function () {
                if (tableInfo.isChecked.length == 0) {
                    malert('请选中您要操作的数据','top','defeadted');
                    return;
                }
                var jkpzh = '';
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        if (tableInfo.jsonList[i]['jkzt'] == '1') {//交款单作废时不可以取消财务上交
                            malert('此记录已作废不鞥进行取消上交','top','defeadted');
                            return;
                        }
                        if (tableInfo.jsonList[i]['sjbz'] == '0') {//交款单作废时不可以取消财务上交
                            malert('此记录未上交不鞥进行取消上交','top','defeadted');
                            return;
                        }
                        jkpzh = tableInfo.jsonList[i]['jkpzh']
                    }
                }
                var json = {
                    jkpzh: jkpzh
                };
                this.$http.post('/actionDispatcher.do?reqUrl=MzsfSfjsCwjkjl&types=update&',
                    JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        malert('取消财务上交成功','top','success');
                        tableInfo.getData();
                    } else {
                        malert('取消财务上交失败：'+data.body.c,'top','defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            },
        }
    });
    laydate.render({
        elem: '.zui-date .todats'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,done:function (value,data) {
        }
    });
    laydate.render({
        elem: '.zui-date .todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,done:function (value,data) {
        }
    });
})()