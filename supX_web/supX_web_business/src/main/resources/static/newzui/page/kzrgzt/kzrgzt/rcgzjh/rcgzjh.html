<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>管理小组</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link type="text/css" href="rcgzjh.css" rel="stylesheet"/>
</head>

<body class="skin-default flex-container flex-dir-c flex-one">

<div class="wrapper background-box" id="jyxm_icon" >
    <div class="panel" v-cloak>
        <div class="tong-top">
            <button class="tong-btn btn-parmary" @click="AddModel"><i class="iconfont icon-iocn42 icon-cf"></i>新增计划</button>
            <button class="tong-btn btn-parmary-b" @click="getData"><i class="iconfont icon-iocn56 icon-c1"></i>刷新</button>

        </div>
        <div class="tong-search">
            <div class="top-form">
                <label class="top-label">类型</label>
                <div class="top-zinle">
                    <select-input class="wh122" @change-data="resultChange"
                                  :child="bxzt_tran" :index="'zt'" :val="zt"
                                  :name="'zt'" >
                    </select-input>
                </div>
            </div>
            <div class="top-form">
                <label class="top-label">状态</label>
                <div class="top-zinle">
                    <select-input class="wh122" @change-data="resultChange"
                                  :child="bxzt_tran" :index="'zt'" :val="zt"
                                  :name="'zt'" >
                    </select-input>
                </div>
            </div>
            <div class="top-form">
                <label class="top-label">年</label>
                <div class="top-zinle">
                    <select-input class="wh82" @change-data="resultChanges"
                                  :child="YearList" :index="'year'" :index_val="'yrbm'" :val="popContent.yrbm"
                                  :name="'popContent.yrbm'" :search="true" :index_mc="'year'" >
                    </select-input>

                </div>
            </div>
            <div class="top-form">
                <label class="top-label">月</label>
                <div class="top-zinle">
                    <select-input class="wh62" @change-data="resultChanges"
                                  :child="monthList" :index="'month'" :index_val="'mbm'" :val="popContent.mbm"
                                  :name="'popContent.mbm'" :search="true" :index_mc="'month'" >
                    </select-input>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10" v-cloak  >
        <div class="zui-table-header">
            <table class="zui-table ">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>年份</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>时段</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>计划类型</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>是否总结</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body "   @scroll="scrollTable($event)" v-cloak>
            <table class="zui-table zui-collapse">
                <tbody>
                <tr v-for="(item,$index) in 10" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @dblclick="Listconfirm($index)"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">医疗指控小组</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">消化内科</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">12个</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">工作描述工作描述工作描述工作描述工作描述</div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span class="flex-center">
                                    <!--ui操作状态-->
                                    <em class="width30"><i class="iconfont icon-iocn46 icon-font20 icon-hover" data-title="编辑"></i></em>
                                    <em class="width30"><i class="iconfont icon-icon76 icon-font25 icon-hover" data-title="总结"></i></em>
                                </span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <!--暂无数据提示,绑数据放开-->
            <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
        </div>

        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l background-f">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!-- data-no-change -->
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table zui-collapse">
                    <tbody>
                    <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
                    <tr v-for="(item, $index) in 10"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-active':$index==2}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--右侧固定-->
        <div class="zui-table-fixed table-fixed-r background-f">
            <div class="zui-table-header">
                <table class="zui-table zui-collapse">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table zui-collapse">
                    <tbody>
                    <tr v-for="(item, $index) in 10"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" >
                                    <span class="flex-center">
                                     <!--ui操作状态-->
                                    <em class="width30"><i class="iconfont icon-iocn46 icon-font20 icon-hover" data-title="编辑"></i></em>
                                    <em class="width30"><i class="iconfont icon-icon76 icon-font25 icon-hover" data-title="总结"></i></em>
                                    </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>


        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<script src="rcgzjh.js"></script>
</body>

</html>