<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>三级医师查房</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script src="/newzui/pub/js/highcharts.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../kshy/kshy.css">
    <link rel="stylesheet" href="../wjz/wjz.css">
    <link href="../hz/hz.css" rel="stylesheet">
    <link href="sjyscf.css" rel="stylesheet">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper background-f flex-container flex-dir-c">
    <div class="tong-top font-16 color-c3 font-weight flex-container flex-align-c ">三级医师查房合格率统计</div>
    <div class="content" v-cloak>
        <div class="tong-search">
            <div class="top-form">
                <label class="top-label">时间段</label>
                <div class="top-zinle flex-container">
                    <div class="zui-date position">
                        <i class="iconfont icon-icon61"></i>
                        <input type="text" class="zui-input wh122 dateStart" v-model="beginrq"/>
                    </div>
                    <span class="flex-container flex-align-c padd-r-10 padd-l-10">至</span>
                    <div class="zui-date position">
                        <i class="iconfont icon-icon61"></i>
                        <input type="text" class="zui-input wh122 dateEnd" v-model="endrq"/>
                    </div>
                </div>
            </div>
            <div class="top-form">
                <label class="top-label">类型</label>
                <div class="top-zinle">
                    <div class="top-zinle">
                        <select-input class="wh122" @change-data="resultChange"
                                      :child="istrue_tran" :index="'nbtclb'" :index_val="'nbtclb'" :val="nbtclb"
                                      :name="'ypContent.nbtclb'" :search="true" :index_mc="'nbtclb'">
                        </select-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="chart">
            <!--<h2 class="text-center font-18 font-weight color-393f">科室医生会议次数统计</h2>-->
            <div class="flex-container height-310" style="height: 280px">
                <div style="width: 80%" >
                    <div class="canvas"  id="canvas"></div>
                </div>
                <div class=" padd-b-20" style="width: 20%">
                    <div class="canvas" id="pieCanvas"></div>
                    <div class="text-center ">
                        <span class="color-757c83 font14">患者数</span><span style="padding-right: 28px;padding-left: 2px;" class="color-wtg "><span class="font-30">123</span>人</span>
                        <span class="color-757c83 font14" style="padding-right: 2px;">总合格率</span><span class="color-green font-30">38%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="width: 100%;background: #f2f2f2;height: 8px;"></div>
    <div class="flex-container flex-jus-sp flex-one" id="brRyList01" style="background: #f3f3f3">
        <div style="width: 59.5%;border: 1px solid #eeeeee" class="flex-container flex-dir-c">
            <div class="tong-top   flex-container flex-align-c flex-jus-sp"><p class="font-16 color-c3 font-weight">在院患者</p><p class="color-green font14 cursor">更多>></p></div>
            <div class=" zui-table-view flex-container flex-dir-c flex-one padd-l-10 padd-r-10  padd-t-10 " >
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m "><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s "><span>患者姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s "><span>住院天数</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>标识</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>床位</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>诊断名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>住院号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>主管医师</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body flex-one margin-b-10"  @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <!-- v-if="jsonList.length!=0" -->
                        <tbody>
                        <tr :id="activeIndex" :tabindex="$index" v-for="(item, $index) in 22"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'table-hovers':$index===0},$index%2==0?'errorBg':'']"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)"
                            @dblclick="doPop($index)"
                            class="tableTr2">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    {{$index}}
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s text-decoration">
                                    患者姓名
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s ">
                                    住院天数
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <span class="userName-pin" v-if="$index%2==0"><img src="/newzui/pub/image/pin.png"></span>
                                    <span class="userName-lc" v-else><img src="/newzui/pub/image/cp.png"></span>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell  cell-s">
                                    床位
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell  cell-xl text-left">
                                    诊断名称
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    住院号
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell  cell-s">主管医师
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                </div>
                <div class="zui-table-fixed table-fixed-l">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"   @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in 22"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                @dblclick="doPop($index)">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                        {{$index}}
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s text-decoration">
                                        患者姓名
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>上报医生</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"   @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in 22"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                @dblclick="doPop($index)">
                                <td>
                                    <div class="zui-table-cell  cell-s">
                                        主管医师
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div style="border: 1px solid #eeeeee;width: 40%" class=" flex-container flex-dir-c">
            <div class="bottom-right flex-container flex-dir-c">
                <div class="count-title">
                    查房次数（本周）
                </div>
                <div class="count-name flex-container flex-jus-sp">
                    <span>患者:李浩然</span>
                    <span class="color-f3a74f">入院时间：2018/12/12 12:22</span>
                </div>
                <div class="flex-container flex-jus-c flex-align-c flex-one">
                    <div class="flex-container flex-dir-c flex-align-c wh30">
                        <p class="color-green"><span style="font-size: 50px">1</span>次</p>
                        <p class="color-c3 font-weight font14" style="margin-top: -9px">主任医师</p>
                        <p class="font12">上次查房时间</p>
                        <p class="font12">2018/12/12</p>
                    </div>
                    <div class="flex-container flex-dir-c flex-align-c wh30">
                        <p class="color-green"><span style="font-size: 50px">1</span>次</p>
                        <p class="color-c3 font-weight font14" style="margin-top: -9px">主任医师</p>
                        <p class="font12">上次查房时间</p>
                        <p class="font12">2018/12/12</p>
                    </div>
                    <div class="flex-container relative flex-dir-c flex-align-c wh30">
                        <p class="color-green"><span style="font-size: 50px">1</span>次</p>
                        <p class="color-c3 font-weight font14" style="margin-top: -9px">主任医师</p>
                        <p class="font12">上次查房时间</p>
                        <p class="font12">2018/12/12</p>
                        <p class="start-overtime text-center">延时</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="sjyscf.js" type="text/javascript"></script>
</body>
</html>