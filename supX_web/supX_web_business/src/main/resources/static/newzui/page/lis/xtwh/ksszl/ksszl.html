<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>抗生素资料</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ksszl.css" rel="stylesheet"/>
</head>
<style>

</style>
<body class="skin-default">
<div class="wrapper">
    <div class="top flex-container">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="add()">新增抗生素</button>
            <button class="tong-btn btn-parmary-b icon-baocun paddr-r5">保存</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5">删除</button>
            <button class="tong-btn btn-parmary-b icon-yl paddr-r5" >预览</button>
            <button class="tong-btn btn-parmary-b icon-dysq paddr-r5">打印</button>
                <div class="flex-container">
                    <span class="ft-14 padd-r-5">搜&nbsp;索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text"/>
                </div>
        </div>
    </div>

    <div class="zui-table-view dataList padd-r-10 padd-l-10" z-height="full" style="background: #fff">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'list'"
                                                                                          :type="'all'" :val="isCheckAll">
                    </input-checkbox></div></th>
                    <th><div class="zui-table-cell cell-s"><span>编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>中文名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>英文名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>参考用量1</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>参考用量2</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>血药峰值1</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>血药峰值2</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>代码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>大类</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>Whonet代码(MIC)</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>序号</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table" >
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in list"  :class="[{'table-hovers':isChecked[$index]}]">
                    <td  class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'list'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td ><div class="zui-table-cell cell-s" v-text="item.kssbh"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ksszwmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kssmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yl1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yl2"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.xyfz1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.xyfz2"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kssdm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ksszlid"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.whonetMicbm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.xh"></div></td>
                    <td class="cell-s"><div class="zui-table-cell cell-s">
                        <i class="icon-sc icon-font"></i>
                    </div></td>
                    <p v-if="list.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'list'"
                                                                               :type="'all'" :val="isCheckAll">
                        </input-checkbox></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!-- data-no-change -->
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in list"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','list'],$event)">
                        <td class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'list'"
                                                                                              :type="'some'" :which="$index"
                                                                                              :val="isChecked[$index]">
                        </input-checkbox></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in list"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','list'],$event)">
                        <td class="cell-s"><div class="zui-table-cell cell-s">
                            <i class="icon-sc icon-font"></i>
                        </div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>


        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>


    </div>

    <div class="popup_insert">
        <transition name="left-fade">
            <div class="zui-form side-form ng-hide pop-width  bcsz-layer checkShow" style="height: 93%;padding-top: 0px;width: 700px">
                <div class="layui-layer-title " v-text="title"></div>
                <span class="layui-layer-setwin" style="top: 0;">
                <i class="color-btn" @click="close()">&times;</i></span>
                <div class="layui-layer-content">
                    <div class=" layui-mad ysb-input" >
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">编码&emsp;</p>
                                <input class="zui-input ysb-zui-wid" data-notempty="true" @keydown="nextFocus($event)" type="text" placeholder="请输入编号" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">中文名称&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入中文名称" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">英文名称&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入英文名称" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">代码&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入代码" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">参考用量1&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入参考用量1" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">参考用量2&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入参考用量2" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">大类&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入大类" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">血药峰值1&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入血药峰值1" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">血药峰值2&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入血药峰值2" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">序号&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入序号" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">WhonetMIC&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入WhonetMIC代码" autocomplete="auto">
                            </div>
                        </div>
                        <div class="col-x-6">
                            <div class="zui-bottom">
                                <p class="zui-top zui-text-flex">纸片法&emsp;</p>
                                <input class="zui-input ysb-zui-wid" @keydown="nextFocus($event)" type="text" placeholder="请输入纸片法" autocomplete="auto">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="zui-row buttonbox" style="height: 50px;line-height: 50px;position: absolute;bottom: 10px">
                    <button class="zui-btn table_db_esc btn-default xmzb-db" @click="close()">取消</button>
                    <button class="zui-btn btn-primary table_db_save xmzb-db" @click="save()">保存</button>
                </div>
            </div>
        </transition>
    </div>

    <div class="popup_delete">
        <!--<transition name="pop-fade">-->
        <div class="pophide" :class="{'show':isShowpopL}"></div>
        <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title"></div>
            <span class="layui-layer-setwin">
                <a @click="isShowpopL=false,isShow=false" class="layui-layer-ico layui-layer-close layui-layer-close1" href="javascript:;"></a>
            </span>
            <div class="layui-layer-content" >
                <div class=" layui-mad layui-height">
                    确定删除：{{centent}}项吗？
                </div>
            </div>
            <div class="zui-row buttonbox buttond">
                <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save" @click="success()">确定</button>
            </div>
        </div>
        <!--</transition>-->
    </div>

</div>


<script src="ksszl.js"></script>

</body>
</html>