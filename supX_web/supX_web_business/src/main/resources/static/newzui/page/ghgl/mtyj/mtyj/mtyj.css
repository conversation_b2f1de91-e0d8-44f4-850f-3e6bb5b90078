.danwei-box .zui-input {
    padding-right: 34px;
}

.danwei-box .danwei {
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -9.5px;
    color: #1abc9c;
}

.tem {
    position: relative;
    float: left; /*width: 300px;*/ /*height: 450px;*/
    width: 800px;
    height: 500px;
    border: 1px solid green; /*margin-left: 20px;*/
    margin-top: 20px;
}

.bgbs {
    background-color: #ffffff;
}

.item {
    position: absolute;
    display: inline-block;
    top: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    cursor: default;
    z-index: 100;
}

.item span {
    float: left; /*height: 38px;*/
    z-index: 1; /*line-height: 38px;*/
}
.bg-color{
    background-color: rgba(255, 0, 0, 0.1) !important;
}
.tem{
    position: relative;
    float: left;
    /* width: 300px; */
    /* height: 450px; */
    width: 800px;
    height: 500px;
    border: 1px solid green;
    margin-left: 20px;
    /*font-family: cursive;*/
    margin-top: 20px;
}
.tem #table_cvs{
    display: none;
}
.item{
    position: absolute;
    display: inline-block;
    top: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    cursor: default;
    z-index: 100;
}
.icon-width:before{
    position: static;
}
.chxx_model{
    width: 100%;
    height:350px;
    overflow: hidden;
    padding: 14px 18px 14px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.chxx_model .zui-table-body{
    height: 250px;
}
#model{
    z-index: 11111;
    max-width: 1200px;
}
