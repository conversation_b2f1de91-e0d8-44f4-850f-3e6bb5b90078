<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>病案管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="sydj.css" rel="stylesheet"/>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="jyxm_icon">
        <div class="panel tong-top flex-container">
            <!--<button class="tong-btn btn-parmary  icon-xz1 paddr-r5" @click="jyDJ">借阅</button>-->
            <!--<button class="tong-btn btn-parmary-b  icon-sx paddr-r5" @click="sx">刷新</button>-->
            <button class="tong-btn btn-parmary icon-width icon-widthy icon-sm" @click="smJS">扫码接收</button>
            <div class="flex-container flex-align-c">
                <span class="ft-14 padd-r-5 whiteSpace">审核状态</span>
                <!--<select-input @change-data="resultRydjChange"-->
                <!--:child="rybmList" :index="'shmc'" :index_val="'shbz'" :val="popContent.shbz"-->
                <!--:name="'popContent.shbz'" :search="true" :index_mc="'shmc'" >-->
                <!--</select-input>-->
                <select-input class="wh100" @change-data="resultRydjChange"
                              :not_empty="false" :child="rybmList"
                              :index="'shmc'" :index_val="'shbz'"
                              :val="param.shbz" :search="true" :name="'param.shbz'"
                              id="yfbm" :index_mc="'shbz'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-l-5">
                <span class="ft-14 padd-r-5 whiteSpace">出院日期</span>
                <div class=" flex-container flex-align-c">
                    <input class="zui-input todate wh200 text-indent20" placeholder="请选择出院开始日期" id="timeVal"/><span
                        class="padd-l-5 padd-r-5">~</span>
                    <input class="zui-input todate wh200 " placeholder="请选择出院结束日期" id="timeVal1"/>
                </div>
            </div>

            <div class="flex-container flex-align-c padd-l-5">
                <span class="ft-14 padd-r-5 whiteSpace">检索</span>
                <input class="zui-input wh100" placeholder="请输入关键字" type="text" v-model="param.parm"
                       @keydown.enter="goToPage(1)"/>
            </div>
            <div class="flex-container flex-align-c padd-l-5">
                    <span class="ft-14 padd-r-5 whiteSpace">科别</span>
                <select-input :cs="true" @change-data="resultRydjChange" :not_empty="false"
                              :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="popCont.ryks"
                              :name="'popCont.ryks'">
                </select-input>
            </div>
        </div>
        <div class="zui-table-view padd-r-10 padd-l-10" z-height="full">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>住院号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>病案号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>病人姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m"><span>性别</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m"><span>年龄</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>出院日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>状态</span></div>
                        </th>
<!--                        <th class="cell-s">-->
<!--                            <div class="zui-table-cell cell-s"><span>操作</span></div>-->
<!--                        </th>-->
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr :tabindex="$index"
                        v-for="(item, $index) in jsonList"
                        @dblclick="goNew(item)"
                        @click="checkSelect([$index,'some','jsonList'],$event)"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td class="cell-m">
                            <div class="zui-table-cell" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.zyh">住院号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.bah">病案号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.brxm">病人姓名</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m" v-text="brxb_tran[item.brxb]">病人性别</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m" v-text="item.nl">病人姓名</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="fDate(item.bqcyrq,'date')">出院日期</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l">
                                <i v-if="item.shbz==0 || item.shbz==null" class="color-wtg">待审核</i>
                                <i v-else-if="item.shbz==1">
                                    <span class="color-ysh">已审核</span>
                                    <!-- <span class="color-wtg">待接收</span> -->
                                </i>
                                <!-- <i v-else-if="item.shbz==1 && item.jsbz==1" class="color-ysh">已接收</i> -->
                            </div>
                        </td>
<!--                        <td class="cell-s">-->
<!--                            <div class="zui-table-cell  cell-s">-->
<!--                                <i v-if="item.shbz==0||item.shbz==null" class="icon-img icon-sh-h icon-width-t"-->
<!--                                   data-title="审核" @click="sh(item.zyh)"></i>-->
<!--                                &lt;!&ndash; <i v-if="item.shbz==1&&item.jsbz==0" class="fa fa-plus-square"  data-title="接收" @click="js($index)"></i> &ndash;&gt;-->
<!--                                <i v-if="item.shbz==1" class="icon-img icon-quxiaoshenhe"-->
<!--                                   data-title="取消审核" @click="qxsh(item.zyh)"></i>-->
<!--                                &lt;!&ndash; <i v-if="item.shbz==1 && item.jsbz==1" class="icon-img icon-zk" data-title="取消接收"></i> &ndash;&gt;-->
<!--                                <i v-if="item.shbz==0||item.shbz==null" @click="goNew(item)" class="icon-bj"-->
<!--                                   data-title="编辑"></i>-->
<!--                            </div>-->
<!--                        </td>-->
                    </tr>
                    </tbody>
                </table>

            </div>
            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>
        </div>

    </div>
</div>
<div id="pop" class="pophide" :class="{'show':popShow}">
    <div class="pop-width420 bcsz-layer "
         style="height: max-content;padding-bottom:40px;margin: 180px auto auto auto;background: #fff; position: relative">
        <i class="icon-width icon-close-l" @click="cancel"></i>
<!--        <div class="bagl-sm background-f" @click="cgSm">请扫码</div>-->
<!--        <input class="zui-input wh100" placeholder="请输入关键字" type="text" v-model="popContent.parm"-->
<!--               @keydown.enter="goToPage(1)"/>-->
        <input class="bagl-sm background-f"  placeholder="请扫码" type="text" v-model="popContent.parm" autofocus='true'
               @keydown.enter="updateBajs()"/>
        <span class="bagl-js-text color-dsh" v-show="cgShow">
            <i>{{baxx.zyh}}</i>
            <i>{{baxx.brxm}}</i>
            <i>{{baxx.ryksmc}}</i>
        </span>
        <span class="bagl-smq" v-show="smShow"><img src="/newzui/pub/image/<EMAIL>"></span>
        <span class="bagl-smcg" v-show="cgShow" @click="sbSm">
            <img src="/newzui/pub/image/<EMAIL>">
            <i class="bagl-gou">
                <img src="/newzui/pub/image/<EMAIL>">
            </i>

        </span>
        <span class="bagl-smcg bagl-smsb" v-show="sbShow">
            <img src="/newzui/pub/image/<EMAIL>">
            <i class="bagl-gou bagl-cha"><img src="/newzui/pub/image/<EMAIL>"></i>
        </span>
        <span class="bagl-smq-text" v-text="ShowTitle"></span>
    </div>

</div>

<script src="sydj.js"></script>
</body>
</html>
