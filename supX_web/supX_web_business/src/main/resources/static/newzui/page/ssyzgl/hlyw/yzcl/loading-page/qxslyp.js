var shyz = new Vue({
    el: '#loadingPage',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat,printer],
    data: {
    	brlistjson:{},//只用于接受请求LIST对象
        brList:[],
        yzList: [],
        jkList:[],
        ypslInfoList:[],//真正的列表
        zyhs:[],
        ksid:null,//科室编码
        caqxContent:null,//参数权限对象
        ifClick:true,
    },
    mounted: function () {
    	this.moun();
    	window.addEventListener("storage",function (e) {
           if (e.key == "qxslyp" && e.newValue !== e.oldValue){
               shyz.zyhs= [];
               shyz.moun();
           }
        });
    },
    updated: function () {
        changeWin();
    },
    methods: {
        moun: function () {
            this.brlistjson=JSON.parse(sessionStorage.getItem("qxslyp"));
            this.brList =this.brlistjson.brlist;
            this.ksid=this.brlistjson.ksid;
            this.caqxContent=this.brlistjson.csqx;
            for(var i=0;i<this.brList.length;i++){
                var zyh={
                    zyh:this.brList[i].zyh
                };
                this.zyhs.push(zyh);
            }
            this.initSlData();
        },
    	//重写选中
  	  checkSelectSl:function (val,event) {
            if (val[1] == 'some') {
                Vue.set(this.isChecked, val[0], !this.isChecked[val[0]]);
                if (!val[2]) this.isCheckAll = false;
                console.log(this.isChecked)
            } else if (val[1] == 'one') {
                this.isCheckAll = false;
                this.isChecked = [];
                Vue.set(this.isChecked, val[0], !this.isChecked[val[0]]);
            } else if (val[1] == 'all') {
                this.isCheckAll = !this.isChecked[val[0]];
                console.log(this.isCheckAll)
                if (val[2] == null) val[2] = "jsonList";
                if (this.isCheckAll) {
                    for (var i = 0; i < this[val[2]].length; i++) {
                        Vue.set(this.isChecked, i, true);
                    }
                } else {
                    this.isChecked = [];
                }
            }
            var that=this;
            this.ckChecked=val[0]
            event.currentTarget.onkeydown=function (e) {
                if(e.keyCode==40){
                    that.isChecked=[]
                    that.ckChecked=that.ckChecked>that[val[2]].length?0:that.ckChecked+1;
                    that.$set(that.isChecked,that.ckChecked,true)
                }else if(e.keyCode==38){
                    that.isChecked=[]
                    that.ckChecked=that.ckChecked<0?that[val[2]].length:that.ckChecked-1;
                    that.$set(that.isChecked,that.ckChecked,true)
                }
            }
            console.log("+++++++++++++++++++++++++++++");
            console.log(this.ypslInfoList);
        },
        //重写选中
  	  reCheckBoxSl: function () {
  		  if( arguments.length == 1 ){
  		      var isCheckAll = this.ypslInfoList[arguments[0]].isCheckAll? false:true,
                    yzshInfo = this.ypslInfoList[arguments[0]],
                    yzxxList = yzshInfo.yzxx;

                this.ypslInfoList[arguments[0]].isCheckAll = isCheckAll;
                for ( var i = 0; i < yzxxList.length; i++ ){
                    this.ypslInfoList[arguments[0]].yzxx[i].isChecked = isCheckAll;
                }
            }else if(arguments.length == 2){
  		      this.activeIndex = arguments[1];
  		      var isChecked = this.ypslInfoList[arguments[0]].yzxx[arguments[1]].isChecked? false:true,
                    yzshInfo = this.ypslInfoList[arguments[0]],
                    yzxxList = yzshInfo.yzxx,
                    isCheckAll = true;

                this.ypslInfoList[arguments[0]].yzxx[arguments[1]].isChecked = isChecked;
                for ( var y = 0; y < yzxxList.length; y++ ){
                    if( !yzxxList[y].isChecked ){
                        this.ypslInfoList[arguments[0]].isCheckAll = false;
                        isCheckAll = false;
                        break;
                    }
                }
                if( isCheckAll ) this.ypslInfoList[arguments[0]].isCheckAll = true;
            }

            this.$forceUpdate();
        },
        qxypsl: function () {
            tspop.open();
        },
        
        
        //获取药品申领信息
        initSlData:function(){
        	  shyz.ypslInfoList = [];
              if (this.zyhs.length == 0) {
                  malert("请选择病人后再进行此操作！");
                  return
              }
              var zyh = JSON.stringify(this.zyhs);
              $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=qxypsl&ksbm=' +  this.ksid + '&zyh=' + zyh,
                  function (json) {
                      console.log("list:" + json.d.list);
                      if (json.d.list.length > 0) {
                          for (var i = 0; i < json.d.list.length; i++) {
                              for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                  json.d.list[i].yzxx[int].no = i;
                              }
                          }
                      }
                      shyz.ypslInfoList = json.d.list;
                  	for (var k = 0; k < shyz.ypslInfoList.length; k++) {
                    	//判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                    	if(shyz.ypslInfoList[k].nl<7&&this.brList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='1';
                    	}else if(shyz.ypslInfoList[k].nl<7&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='2';
                    	}else if(shyz.ypslInfoList[k].nl<18&&shyz.ypslInfoList[k].nl>6&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='3';
                    	}else if(shyz.ypslInfoList[k].nl<18&&shyz.ypslInfoList[k].nl>6&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='4';
                    	}else if(shyz.ypslInfoList[k].nl<41&&shyz.ypslInfoList[k].nl>17&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='5';
                    	}else if(shyz.ypslInfoList[k].nl<41&&shyz.ypslInfoList[k].nl>17&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='6';
                    	}else if(shyz.ypslInfoList[k].nl<66&&shyz.ypslInfoList[k].nl>40&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='7';
                    	}else if(shyz.ypslInfoList[k].nl<66&&shyz.ypslInfoList[k].nl>40&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='8';
                    	}else if(shyz.ypslInfoList[k].nl>65&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='9';
                    	}else if(shyz.ypslInfoList[k].nl>65&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='10';
                    	}else{
                            shyz.ypslInfoList[k].nljd='11';
                        }
                    }
//                      YZInfo.jsonList = YZInfo.ypslInfoList;
                  });
        },
        dy: function () {
            // 开始打印
            window.print();
        },
        print: function (body) {
            // 查询打印模板
            // var json = {repname: '领药申领单'};
            // $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
            //     // 清除打印区域
            // 	shyz.clearArea(json.d[0]);
            //     // 为打印前生成数据
            // 	shyz.printContent(shyz.ypslInfoList);
            // 	window.print();
            // });
        //    注释掉上面的打印模板是因为目前还没有画完模板待画完后全部改回调用模板生成打印数据
            sessionStorage.setItem('dylyd', JSON.stringify( this.ypslInfoList ));
            this.topNewPage('打印-药品申领单','page/hsz/hlyw/yzcl/loading-page/dylyd.html');
        },
        closePage: function () { //关闭本页面
            var x = parseInt( sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem( 'hszHzlbUpdate' , x );
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/qxslyp.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        }

    },
});

var tspop = new Vue({
    el: '#tspop',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        ifClick: true, //判断是否点击了结算按钮

    },
    mounted: function(){
        laydate.render({
            elem: '#timeVal',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                // 这里放时间选择之后需要处理的代码  比如给数据赋值之类的

            }
        });
    },
    methods: {
        //关闭
        closes: function () {
            $(this.$refs.tspop).hide();
        },
        open: function () {
            $(this.$refs.tspop).show();
        },
        //药品申领
        qxsl: function () {
            //这里执行确定结算操作
        	if (!tspop.ifClick) return //如果为false表示已经点击了不能再点
            tspop.ifClick = false;
        	 if (shyz.caqxContent.cs00900100111 == '0') {
                 malert("对不起，您无权取消申领！",'top','defeadted');
                 tspop.ifClick = true;
                 return
             }
        	 var ypslData=[];
             for (var i = 0; i < shyz.ypslInfoList.length; i++) {
                 for (var j = 0; j < shyz.ypslInfoList[i]['yzxx'].length; j++) {
                	 if (shyz.ypslInfoList[i]['yzxx'][j].isChecked) {
                		 ypslData.push({"ypzxlsh": shyz.ypslInfoList[i]['yzxx'][j].ypzxlsh});
                     }
                 }
             }
             if (ypslData.length <= 0) {
                 malert("无药品取消申领！",'top','defeadted');
                 tspop.ifClick = true;
                 return;
             }
             //console.log("ypsl:"+JSON.stringify({list:this.auditingData}));
             this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=qxypsl&ksbm=' + shyz.ksid,
                 JSON.stringify({list: ypslData})).then(function (data) {
                 if (data.body.a == 0) {
                     malert("药品取消申领成功");
                     tspop.ifClick = true;
                     tspop.closes();
                     shyz.initSlData();//刷新
                 } else {
                     malert("药品取消申领失败",'top','defeadted');
                     tspop.ifClick = true;
                 }
             }, function (error) {
                 console.log(error);
             });
        }
    }
});

