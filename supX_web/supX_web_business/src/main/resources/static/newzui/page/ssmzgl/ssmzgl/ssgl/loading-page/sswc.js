var userNameBg = new Vue({
    el: '.userNameBg',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        Brxx_List: {},
        urlPage: '',
        Num: '',
        num: 0,
        page: '',
        jcShow: false,
        val: true,
        mzShow: true,
        qxks:''
    },
    mounted:function () {
        if(sessionStorage.getItem('sswc')){
            Vue.set(this,'Brxx_List',JSON.parse(sessionStorage.getItem('sswc')));
        }
        window.addEventListener('setItemEvent', function (e) {
            if(e.key=='sswc'){
                Vue.set(userNameBg,'Brxx_List',JSON.parse(e.newValue));
            }
        });
        window.addEventListener('storage',function (e) {
            if(e.key=='sswc'){
                Vue.set(userNameBg,'Brxx_List',JSON.parse(e.newValue));
            }

        });
    },
    created: function () {
    },
    methods: {
        getHeight:function () {
            this.$nextTick(function () {
                fyxmTab.setH=$('.height').height()-120
            })
        },

    },
});
var content =new Vue({
    el:'.content',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components : {
        'search-table' : searchTable
    },
    data:{
        disable:false,
        pageState:{},
        them_tran: {'jb': dic_transform.data.ssjb_tran},
        them: {'手术编码': 'ssbm', '手术名称': 'ssmc', '拼音代码': 'pydm', '手术级别': 'jb'},
        dg: {
            page: 1,
            rows: 2000,
            sort: "",
            order: "asc",
            parm: ""
        },
        ysjson: {
            ysbz: '1',
            tybz: '0',
        },
        hsjson: {
            hsbz: '1',
            tybz: '0',
        },
        allKs:[],
        total:0,
        zybmList:[],
        ssjList:[],
        searchCon:[],
        searchCon1:[],
        searchCon2:[],
        searchCon3:[],
        searchCon4:[],
        Content: {},
        Content1: {},
        Content2: {},
        Content3: {},
        Content4: {},
        ysData:[],
        hsData:[],
        selSearch:-1,
        selSearch1:-1,
        selSearch2:-1,
        selSearch3:-1,
        selSearch4:-1,
        popContent:{},
        them: {'疾病编码': 'jbmb', '疾病名称': 'jbmc'},
        them_tran:{},
        pageSelect: {
            page: 1,
            rows: 20,
            total: null
        },
        popContent: {},
        con: [],
        selSearch: 0,
    },
    created: function () {
        this.getYs()

    },
    filters:{
        initDate2:function (value) {
            if(value){
                var d=new Date(value);
                return ''+d.getFullYear()+'-'+(d.getMonth()+1)+'-'+d.getDate()+' '+(d.getHours())+':'+(d.getMinutes())+':'+(d.getMilliseconds())
            }
        },
    },
    mounted: function () {
        laydate.render({
            elem: '#ksTime'
            ,type:'datetime'
            , done: function (value, data) {
                content.pageState.ssksrq = value;
            }
        });
        laydate.render({
            elem: '#jsTime'
            ,type:'datetime'
            , done: function (value, data) {
                content.pageState.ssjsrq = value;
            }
        });
        laydate.render({
            elem: '#ssdjrq'
            ,type:'datetime'
            , done: function (value, data) {
                content.pageState.djrq = value;
            }
        });
        laydate.render({
            elem: '#mzdjrq'
            ,type:'datetime'
            , done: function (value, data) {
                content.pageState.mzdjrq = value;
            }
        });
        if (sessionStorage.getItem('allKs')) {
            Vue.set(this, 'allKs', JSON.parse(sessionStorage.getItem('allKs')));
        }
        this.readyData()
    },
    methods:{
        searching:function (add, val) {
            if (val == '') {
                return;
            }
            this.pageSelect.val=val;
            var param = {
                page: this.pageSelect.page,
                rows: 10,
                sort: "yljgbm",
                order: "asc",
                parm: val,
            }
            if (!add) this.pageSelect.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm'
                + '&json=' + JSON.stringify(param),
                function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    console.log("--->toltal",json.d.total)
                    content.pageSelect.total = json.d.total;
                    console.log("--->sum", content.page.total)
                    content.selSearch = 0;
                    if (add) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            content.con.push(json.d.list[i]);
                        }
                    } else {
                        content.con = json.d.list;
                    }
                    if (json.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                } else {
                    malert(json.c)
                }
            });
        },
        checkedOneOut: function (index) {
            console.log("------->", index);
            var item = content.con[index];
            console.log("------->item", item);
            if (index == null) {
                this.pageSelect.page++
                this.searching(true,this.pageSelect.val);
            } else {
                console.log(arguments);
                this.pageState.shzdmc=item.jbmc
                this.pageState.shzd= item.jbmb;
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },
        checkedOneOut1: function (index) {
            console.log("------->", index);
            var item = content.con[index];
            console.log("------->item", item);
            if (index == null) {
                this.pageSelect.page++
                this.searching(true,this.pageSelect.val);
            } else {
                console.log(arguments);
                this.pageState.ssbfzmc=item.jbmc
                this.pageState.ssbfz= item.jbmb;
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },
        changeDown: function (event, index) {
            if (this.con[this.selSearch] == undefined) return;
            this.inputUpDown(event, this.con, 'selSearch')
            this.popContent = this.con[this.selSearch]
            if (event.keyCode == 13) {
                console.log(this.popContent)
                if (index == 1) {
                    this.pageState.shzd = this.popContent.jbmb
                    this.pageState.shzdmc = this.popContent.jbmc
                } else if (index == 2) {
                    this.pageState.ssbfz = this.popContent.jbbm
                    this.pageState.ssbfzmc = this.popContent.jbmc
                }
                this.$forceUpdate()
                this.nextFocus(event)
                $(".selectGroup").hide();
            }
        },

        doCheck: function (type) {
            this.pageState[type]=!parseInt(this.pageState[type])?1:0
            this.$forceUpdate()
        },
        getKsxx:function(){
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhKsryKsbm&types=queryOne&ksbm='+userNameBg.Brxx_List.ksbm+'', function (json) {
                if (json.a == '0') {
                    content.pageState.ssksmc=json.d.ssksmc
                    content.pageState.mzclbz=1
                    content.$forceUpdate()
                    malert(json.c, 'top', 'success');
                }
            });
        },
        getSssq() {
            var str={
                sqdh:userNameBg.Brxx_List.sssqdh,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SsRcywSsap&types=query&parm='+JSON.stringify(str)+'', function (json) {
                if (json.a == '0' && json.d.list.length!=0 && json.d.list != null) {
                    content.pageState = json.d.list[0];
                    content.pageState.sssqdh=userNameBg.Brxx_List.aprq? content.pageState.sqdh:content.pageState.sssqdh
                    content.$forceUpdate()
                    content.getKsxx()
                }else {
                    malert(json.c, 'top', 'defeadted');
                }
            });
        },
        readyData: function (req, types, listName) {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyzbm&json=" + JSON.stringify({"zylb": "03"}), function (json) {
                if (json.a == 0)
                    content.zybmList = json.d.list;
                else {
                    malert("查询失败", 'top', 'defeadted');
                }
            });
        },
        getYs: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(this.ysjson) + "" + "&dg=" + JSON.stringify(this.dg), function (data) {
                if (data.a == '0' && data.d.list.length != 0) {
                    content.ysData = data.d.list;
                    content.geths()

                }
            });
        },
        geths: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(this.hsjson) + "" + "&dg=" + JSON.stringify(this.dg), function (data) {
                if (data.a == '0' && data.d.list.length != 0) {
                    content.hsData = data.d.list;
                    content.getSssq()
                }
            });
        },
        SaveSssq:function () {
            this.disable=true
            this.pageState.type=1
            this.$http.post('/actionDispatcher.do?reqUrl=New1SsRcywSsap&types=save', JSON.stringify(this.pageState)).then(function (data) {
                if (data.body.a == 0) {
                    content.disable=false
                    malert(data.body.c, 'top', 'success');
                    content.getSssq(true)
                } else {
                    content.disable=false
                    malert("医嘱保存失败：" + data.body.c, 'top', 'defeadted');
                }
            });
        },

    },
})
$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    hzList.searchCon = [];
    e.stopPropagation();
});
