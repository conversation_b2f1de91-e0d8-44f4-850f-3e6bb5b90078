(function () {
    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.background',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            centent:'',
        },
        methods:{

        }
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                pop.title='系统提示';
                pop.centent='确定删除该科室吗？';
                pop.isShowpopL=false;
                pop.isShow=false;
            }

        }
    });
    var wapses=new Vue({
        el:'.xmzb-content',
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            isFold: false,
        },
        methods:{
            //删除当前
            delNow:function () {
                pop.title='系统提示';
                pop.centent='确定删除该科室吗？';
                pop.isShowpopL=true;
                pop.isShow=true;
            }

        },
    });
    var wapse=new Vue({
        el:'#brzcList',
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            pcShow:false,
            jsShowtime:false,
            jsShow:false,
            qsShow:false,
            title:'',
            centent:'',
            isFold: false,
        },
        methods:{
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                //成功回调提示
                // malert('111','top','defeadted');
            },
            AddClose:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            } ,
            // //取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // //确定
            confirms:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('222','top','defeadted');
            }

        },
    });
})()