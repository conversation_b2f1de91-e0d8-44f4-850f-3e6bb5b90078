var content=new Vue({
    el:'.content',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        beginrq:'',
        endrq:'',
    },
    created:function(){
    },
    mounted:function(){
        this.ajaxChart();
        //初始化检索日期！为今天0点到今天24点
        var myDate=new Date();
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date');
        this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24, 'date');
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#dateStart',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.beginrq = value;
                } else {
                    content.beginrq = '';
                }
                //获取一次列表
            }
        });
        laydate.render({
            elem: '#dateEnd',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.endrq = value;
                } else {
                    content.endrq = '';
                }
                //获取一次列表
            }
        });
    },
    updated:function(){
    },
    methods:{
        edit:function(){

        },
        ajaxChart:function (view) {
            var dataArrName=[];
            var dataArr=[];
            for(var i=0;i<40;i++){
                dataArrName.push('李\n浩');
                dataArr.push(parseInt(10*Math.random()))
            }
            var myChart = echarts.init(document.getElementById('canvas'));
            option = {
                title: {
                    text: '科室医生会议次数统计',
                    x:'center',
                    y:'top',
                },
                color: ['#02a9f5'],
                tooltip : {
                    trigger: 'axis',
                    axisPointer : {
                        type : 'shadow'
                    },
                    formatter:function (params,ticket,callback ) {
                        return '<span>'+params[0].value+'次</span>'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis : [

                    {
                        type : 'category',
                        data : dataArrName,

                        axisTick: {
                            interval:2,
                            alignWithLabel: true
                        },
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92',
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#fff',
                            },
                        },
                        splitLine:{
                            show:true,
                            lineStyle:{
                            },
                        },
                    },
                ],
                yAxis : [
                    {
                        splitNumber:10,
                        type : 'value',
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92'
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine:{
                            show:false,
                            lineStyle:{
                                color:'#fff'
                            },
                        },
                        axisTick:{
                            interval:2
                        },
                    },
                ],
                series : [
                    {
                        name:'',
                        type:'bar',
                        barMaxWidth : '11',
                        animationDuration: function (idx) {
                            return idx * 100;
                        },
                        data:dataArr,
                        label:{
                            color:'#b6babb'
                        },

                    },

                ]
            };
            if (option && typeof option === 'object') {
                myChart.setOption(option,true);
            }

        }
    },
});
var brRyList=new Vue({
    el:'#brRyList01',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{

    },
    methods:{
        unconfirmed:function (num) {
            pop.popShow=true;
            pop.MeetShow=true;
        },
        //已确认弹窗打印
        Listconfirm:function () {
            pop.popShow=true;
            pop.MeetShow=true;
            pop.wtyShow=true;
            pop.prShow=true;
        },
    },
    mounted:function () {

    },
});
var dyjl=new Vue({
    el:'.dyjl',
    data:{
        dyShow:false,
    },
    methods:{

    }
});
var pop=new Vue({
    el:'#pop',
    mixins: [dic_transform, baseFunc, tableBase, mformat,scrollOps],
    data:{
        popShow:false,
        wjzShow:true,
        prShow:false,
        wtyShow:false,
        topTitle:'',
        ryxmList:[],
        MeetShow:false,
        popContent:{},
    },
    methods:{
        //不同意操作
        Disagree:function () {
            this.MeetShow=false;
            this.wtyShow=false;
            this.prShow=false;
            this.topTitle='不同意建议'
        },
        Agree:function () {
            malert('同意','top','success');
            setTimeout(function () {
                pop.popClose();
            },1000)
        },
        //关闭取消操作
        popClose:function () {
            this.popShow=false;
            this.prShow=false;
            this.MeetShow=false;
        },
        //确定
        popConfirm:function () {
            this.prShow=false;
            malert('确定','top','success')
        },
        //打印
        print:function () {
                window.print();

        }
    }
});