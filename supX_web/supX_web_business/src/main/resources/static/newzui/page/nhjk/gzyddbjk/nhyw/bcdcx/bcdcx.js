(function () {

    var tableInfo = new Vue({
        el: '#context',
        //混合js字典庫
        mixins:[dic_transform, baseFunc, tableBase, mformat],
        data: {
            ksbm: "",
            ksmc: "",
            allKs: [],
            jsonList: [],
            jsonListPrint: [],
            ksVal: null,
            param: {},
            type: null,
            ksrq: null,
            jsrq: null,
            brxxContent:{},
            fyqdContent:{},
        },
        methods: {
            getData: function () {
                this.param.ksrq=this.ksrq;
                this.param.jsrq=this.jsrq;
                tableInfo.fyqdContent.ksrq=this.ksrq;
                tableInfo.fyqdContent.jsrq=this.jsrq;
                tableInfo.jsonListPrint=[];
                console.log(tableInfo.fyqdContent.ksrq);
                console.log(tableInfo.fyqdContent.jsrq);
                this.param.zyh=tableInfo.fyqdContent.zyh;
                $.getJSON("/actionDispatcher.do?reqUrl=HszCxtjFyqd&types=queryMx&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == "0") {
                    	var fyhj=0.00;
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                        tableInfo.jsonList = json.d.list;
                        tableInfo.ksVal = '0005';
                        for(var i=0;i<tableInfo.jsonList.length;i++){
                        	fyhj+=tableInfo.jsonList[i].fyze;
                            tableInfo.jsonListPrint.push( tableInfo.jsonList[i] );
                            if( tableInfo.jsonList[i]["fymx"].length > 0 ){
                                for( var x=0;x<tableInfo.jsonList[i]["fymx"].length;x++ ){
                                    tableInfo.jsonListPrint.push( tableInfo.jsonList[i]["fymx"][x] );
                                }
                            }
                        }

                        tableInfo.fyqdContent.fyhj=fyhj;
                        tableInfo.fyqdContent.xjzf=tableInfo.fyqdContent.fyhj-tableInfo.fyqdContent.bxzfje;
                        tableInfo.fyqdContent.bcdx=numToCn(tableInfo.fyqdContent.bxzfje);
                    }
                });
            },
            ksbmselect: function () {
                this.param.rows = 20000;
                var bean={
                		zyks:'1'
                }
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json="+JSON.stringify(bean)+"&dg=" + JSON.stringify(this.param), function (json) {
                    brSearch.allKs = json.d.list;
                    brSearch.ksbm = json.d.list[0].ksbm;
                    brSearch.ksmc = json.d.list[0].ksmc;
                    //科室获取成功后再查询患者信息
//                    brSearch.searching();
                });
            },
            print: function () {
                window.print();
            }
        }
    });

    var brSearch = new Vue({
        el: '.brSearch',
        components: {
            'search-table': searchTable
        },
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            ksbm: "",
            ksmc: "",
            allKs: [],
            brContent:{},
            searchCon: [],
            selSearch: -1,
            json: {},
            page: {
                page: 1,
                rows: 20,
                brxm:'',
                ryks:'',
                total:null
            },
            title: '病人信息',
            dg: {page: 1, rows: 5, sort: "", order: "asc", parm: ""},
            them_tran: {
            	'brxb': dic_transform.data.brxb_tran
            },
            them: {'入院日期':'ryrq','患者姓名': 'brxm', '性别': 'brxb', '年龄': 'nl'}
        },
        methods: {
            Wf_KsChange: function (index) {
                //科室获取成功后再查询患者信息
//                brSearch.search();
                var obj = event.currentTarget;
                var selected = $(obj).find("option:selected");
                var ks = $(obj).val();
                var mc = selected.text();
                brSearch.ksmc = selected.text();
            },
            changeDown: function (event, type) {
                if (this['searchCon'][this.selSearch] == undefined) return;
                this.keyCodeFunction(event, 'brContent', 'searchCon');
                if (event.code == 'Enter' || event.code == 13) {
                    if (type == "text") {
                    	tableInfo.brxxContent=brSearch.brContent;
                    	tableInfo.fyqdContent=brSearch.brContent;
                    	var d1=brSearch.fDate(tableInfo.fyqdContent.bqcyrq,'date');
                    	var d2=brSearch.fDate(tableInfo.fyqdContent.ryrq,'date');
                    	var dd1=null
                    	if(d1!=null&&d1!=undefined&&d1!=''){
                    		dd1=Date.parse(d1);
                    	}else{
                    		dd1=new Date();
                    	}
                    	var dd2=Date.parse(d2);
                    	tableInfo.fyqdContent.zyts = parseInt(Math.abs(dd1 - dd2) / 1000 / 60 / 60 / 24);
                    	  if(tableInfo.fyqdContent.zyts==0){
                    		  tableInfo.fyqdContent.zyts=1;
                          }
                        Vue.set(this.brContent, 'brxm', this.brContent['brxm']);
                        brSearch.json.brxb = this.brContent.brxb;
                        brSearch.json.nl = this.brContent.nl;
                        brSearch.json.rycwbh = this.brContent.rycwbh;
                        brSearch.json.brfbmc = this.brContent.brfbmc;
                        brSearch.json.ryrq = this.brContent.ryrq;
                        tableInfo.fyqdContent= Object.assign({}, tableInfo.fyqdContent);
                        brSearch.json= Object.assign({}, brSearch.json);
                        this.selSearch=0;
                        this.nextFocus(event);
                    }
                }
            },
//            changeDown: function (event) {
//                this.keyCodeFunction(event, 'brSearch', 'searchCon');
//                if (event.code == 'Enter' || event.code == 13) {
//                    this.nextFocus(event);
//                }
//            },
            
            searching: function (add, type,val) {
            	this.brContent['brxm']=val;
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.brContent[type] == undefined || this.brContent[type] == null) {
                    this.page.brxm= "";
                } else {
                    this.page.brxm= this.brContent[type];
                }
//                this.dg.parm = this.json['brxm'];
                	this.page.ryks=brSearch.ksbm;
               
                	$.getJSON("/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=qdhzxx&parm="+ JSON.stringify(this.page),
//                $.getJSON("/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=nhhzxx&parm="+ JSON.stringify(this.page)
//                    + "&dg=" + JSON.stringify(brSearch.dg),
                    function (json) {
                        if (json.a == 0) {
//                            var date = null;
//                            var res = eval('(' + json.d + ')');
                        	for(var x=0;x<json.d.list.length;x++){
                        		json.d.list[x].ryrq=brSearch.fDate(json.d.list[x].ryrq,'date');
                        	}
                            if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                for (var i = 0; i < json.d.list.length; i++) {
                                	brSearch.searchCon.push(json.d.list[i]);
                                }
                            } else {
                            	brSearch.searchCon = json.d.list;
                            }
                            brSearch.page.total = json.d.total;
                            brSearch.selSearch = 0;
                            if (json.d.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert("查询失败  " + json.c);
                        }
                    });
            },
//            searching: function (add,type) {
//                var _searchEvent = $(event.target.nextElementSibling).eq(0);
//                this.dg.parm = this.json['brxm'];
//                $.getJSON('/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=zyhzxx&parm={"ryks":"' + brSearch.ksbm + '"}'
//                    + '&dg=' + JSON.stringify(brSearch.dg),
//                    function (data) {
//                        brSearch.searchCon = data.d.list;
//                        brSearch.total = data.d.total;
//                        brSearch.selSearch = -1;
//                        if (data.d.list.length != 0) {
//                            $(".selectGroup").hide();
//                            _searchEvent.show()
//                        } else {
//                            $(".selectGroup").hide();
//                        }
//                    });
//            },
            // 下拉table的选中事件
            selectOne: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching(true, 'brmc',this.brContent['brxm']);           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                	this.brContent=item;
                    tableInfo.brxxContent=brSearch.brContent;
                	tableInfo.fyqdContent=brSearch.brContent;
                 	var d1=brSearch.fDate(tableInfo.fyqdContent.bqcyrq,'date');
                	var d2=brSearch.fDate(tableInfo.fyqdContent.ryrq,'date');
                	var dd1=null
                	if(d1!=null&&d1!=undefined&&d1!=''){
                		dd1=Date.parse(d1);
                	}else{
                		dd1=new Date();
                	}
                	var dd2=Date.parse(d2);
                	tableInfo.fyqdContent.zyts = parseInt(Math.abs(dd1 - dd2) / 1000 / 60 / 60 / 24);
                	  if(tableInfo.fyqdContent.zyts==0){
                		  tableInfo.fyqdContent.zyts=1;
                      }
                    Vue.set(this.brContent, 'brxm', this.brContent['brxm']);
                    brSearch.json.brxb = this.brContent.brxb;
                    brSearch.json.nl = this.brContent.nl;
                    brSearch.json.rycwbh = this.brContent.rycwbh;
                    brSearch.json.brfbmc = this.brContent.brfbmc;
                    brSearch.json.ryrq = this.brContent.ryrq;
                    tableInfo.fyqdContent= Object.assign({}, tableInfo.fyqdContent);
                    brSearch.json= Object.assign({}, brSearch.json);
                    this.selSearch=0;
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                }
            },
            setJson: function (item) {
                console.log(item);
                this.json=item;
            }
        }
    });

    window.getTime = function (event, type) {
        if (type == 'star') {
            tableInfo.ksrq = $(event).val();
        } else if (type == 'end') {
            tableInfo.jsrq = $(event).val();
        }
    };
    //初始化页面需要加载的数据
 
    tableInfo.ksbmselect();

    //为table循环添加拖拉的div
    // var drawWidthNum = $(".patientTable tr").eq(0).find("th").length;
    // for (var i = 0; i < drawWidthNum; i++) {
    //     if (i >= 1) {
    //         $(".patientTable th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
    //     }
    // }
    
    $(document).click(function () {
        if (this.className != 'selectGroup') {
            $(".selectGroup").hide();
        }
        $(".popInfo ul").hide();
    });
})();