<div id="jsjl" class="padd-t-10 padd-l-10 padd-b-10">
    <div class="toolMenu flex-container padd-b-10">
        <button class="tong-btn btn-parmary-b icon-sc icon-font14 paddr-r5" @click="cancelFee">明细作废</button>
        <button class="tong-btn btn-parmary-b icon-qxsh icon-font14 paddr-r5" @click="cancelSettlement">取消结算</button>
        <!--        <div class="nh-total flex-container">-->
        <!--            <b> 合计</b>：<span style="color: orange;font-size: 18px;">{{nums}}</span>&nbsp;笔-->
        <!--            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>-->
        <!--            <b> 累计</b>：<span style="color: orange;font-size: 18px;">{{fDec(totalFee,2)}}</span>&nbsp;元-->
        <!--        </div>-->
    </div>
    <div class="zui-table-view over-auto hzList padd-r-10 padd-l-10 padd-b-68">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l"><span>项目名称</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>数量</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>金额</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>明细时间</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>科室</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>医生</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="switchIndex('hoverIndex',true,$index)"
                    @mouseleave="switchIndex()"
                    @click="switchIndex('activeIndex',true,$index)"
                    :tabindex="$index"
                    v-for="(item, $index) in jsonList">
                    <td class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.bxxmmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.akc225"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.akc226"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yka055"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.aae036"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yka097"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yka099"></div>
                    </td>
                </tr>
                </tbody>
            </table>
            <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
        </div>
        <page @go-page="goPage" :totle-page="totalPage" :page="page" :param="param" :prev-more="prevMore"
              :next-more="nextMore"></page>
    </div>
</div>
<script type="application/javascript" src="/newzui/pub/js/insuranceUtil.js"></script>
<script type="application/javascript" src="jsjl.js"></script>
