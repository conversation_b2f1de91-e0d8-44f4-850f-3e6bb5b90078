<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script type="application/javascript" src="/pub/top.js"></script>
    <title>医保参数设置</title>
    <link rel="stylesheet" href="cssz.css"/>
</head>
<body>
<div class="toolMenu" id="toolMenu">
    <button @click="load"><span class="fa fa-refresh"></span>读取参数</button>
    <button @click="save"><span class="fa fa-save"></span>保存</button>
</div>

<div class="cssz" id="cssz">
        <div class="bx">
            <div class="wh250">
                <span>医疗机构编码:</span>
                <input type="text" v-model="json.yljgbm"/>
            </div>
            <div class="wh250">
                <span>IP:</span>
                <input type="text" v-model="json.addr"/>
            </div>
            <div class="wh250">
                <span>端    口:</span>
                <input type="text" v-model="json.port"/>
            </div>
            <div class="wh250">
                <span>医保中心编码:</span>
                <input type="text" v-model="json.ybzxbh"/>
            </div>
            <div class="wh250">
                <span>医院编号:</span>
                <input type="text" v-model="json.tqcbh"/>
            </div>
            <div class="wh250">
                <span>员工号:</span>
                <input type="text" v-model="json.yyjb"/>
            </div>
            <div class="wh250">
                <span>登陆ID:</span>
                <input type="text" v-model="json.dlid"/>
            </div>
            <div class="wh250">
                <span>登陆密码:</span>
                <input type="text" v-model="json.dkma"/>
            </div>
            <div class="wh250">
                <span>服务器名称:</span>
                <input type="text" v-model="json.servlet"/>
            </div>
            <div class="wh250">
                <span>websocket地址:</span>
                <input type="text" v-model="json.cxservlet"/>
            </div>
            <!-- 
            <div class="wh250">
                <span>城乡医疗机构:</span>
                <input type="text" v-model="json.cxyljg"/>
            </div>
            <div class="wh250">
                <span>城乡地址:</span>
                <input type="text" v-model="json.cxaddr"/>
            </div>
            <div class="wh250">
                <span>城乡服务器入口:</span>
                <input type="text" v-model="json.cxservlet"/>
            </div> -->
        </div>
</div>
</body>
<script type="application/javascript" src="cssz.js"></script>
</html>