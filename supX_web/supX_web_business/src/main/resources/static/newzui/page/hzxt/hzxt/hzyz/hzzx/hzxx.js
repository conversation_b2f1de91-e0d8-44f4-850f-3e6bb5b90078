var fyxmTab = new Vue({
    el: '.content',
    mixins: [dic_transform, baseFunc, tableBase],
    data: {
        num: 0,
        jcShow: false,
        setH:'',
        mzShow: true,
        //医嘱单参数
        yzdcs:{
            cfss:'0',
            yebh:'',
            yexxList:[],
        }
    },
    created:function(){
    },
    methods: {
        tabBg: function (page,obj) {
            obj.num = this.num;
            $(".loadPage").load(page + ".html").fadeIn(300);
        },
        topNew:function () {
            this.topNewPage('会诊申请','page/hzxt/hzxt/hzshf/Subdirectory/apzj.html');
            sessionStorage.setItem('hzsqglitem',JSON.stringify(userNameBg.Brxx_List))
        },
    },
});
var userNameBg = new Vue({
    el: '.userNameBg',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        Brxx_List: {},
        urlPage: '',
        Num: '',
        num: 0,
        page: '',
        jcShow: false,
        mzShow: true,
        qxks:''
    },
    created:function(){

    },
    watch: {
    	 'Brxx_List': {
            deep: true,
            handler: function (newVal, oldVal) {
                immediate: true;
                brzcListUser.popContent=userNameBg.Brxx_List
                //未出院取病区出院时间
                if (brzcListUser.popContent.cyrq == null){
                	brzcListUser.popContent.cysj = brzcListUser.popContent.bqcyrq;
                }else{
                	brzcListUser.popContent.cysj = brzcListUser.popContent.cyrq;
                }
        		brzcListUser.jbbmContent['jbbm']=brzcListUser.popContent.ryzdbm;
            }
        },
    },
    mounted:function () {
        this.getHlyy()
        if(sessionStorage.getItem('userPage1')){
            this.Brxx_List=JSON.parse(sessionStorage.getItem('userPage1'))[2]
            this.qxks=JSON.parse(sessionStorage.getItem('userPage1'))[3]
            fyxmTab.num=JSON.parse(sessionStorage.getItem('userPage1'))[1]
            fyxmTab.tabBg(JSON.parse(sessionStorage.getItem('userPage1'))[0],this);
        }
        window.addEventListener('setItemEvent', function (e) {
            if(e.key=='userPage1'){
                userNameBg.Brxx_List=JSON.parse(e.newValue)[2]
                fyxmTab.num=JSON.parse(e.newValue)[1]
                fyxmTab.tabBg(JSON.parse(e.newValue)[0],userNameBg);
            }
        });
        window.addEventListener('storage',function (e) {
            if(e.key=='userPage1'){
                userNameBg.Brxx_List=JSON.parse(e.newValue)[2]
                fyxmTab.num=JSON.parse(e.newValue)[1]
                fyxmTab.tabBg(JSON.parse(e.newValue)[0],userNameBg);
            }

        });
    },
    methods: {
        getHeight:function () {
            this.$nextTick(function () {
                fyxmTab.setH=$('.height').height()-120
            })
        },
        qtflj: function () {
            brzcListUser.index = 1
        },
        getHlyy: function () {
            if (window.top.J_tabLeft.obj.hlyy == '1') { //0-无,1-美康合理用药
                this.loadScript('/newzui/pub/PassJs/McLoader.js', function () {
                })
            }
        },
    },
});
// var pop=new Vue({
//     el:'.pop',
//     data:{
//         sfrj:true,
//         ishow:true,
//         popModel:'',
//         ljbmtext:'临床路径入径',
//     },
//     methods:{
//         close:function () {
//             this.ishow=false
//         },
//         Wf_save:function () {
//             this.ishow=false
//         },
//         flagIS:function () {
//             pop.sfrj=false;
//             pop.ljbmtext='填写原因'
//             this.$forceUpdate()
//         },
//     },
// });

var poplj=new Vue({
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    el:'.poplj',
    data:{
        isShow:false,
        popContent:{}
    },
    methods:{
        Wf_save:function () {
            this.isShow=false;
            userNameBg.topNew()
        }
    },
});
var brzcListUser = new Vue({
    el: '#brzcListUser',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'jbsearch-table': searchTable,
    },
    data: {
        index: 0,
        xhitem: 1,
        popContent: {},
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        //疾病编码下拉table
        jbbmContent: {},
        jbsearchCon: [],
        jbthem: {
            '疾病编码': 'jbmb',
            '疾病名称': 'jbmc',
            '拼音代码': 'pydm'
        },
    },
    /*watch: {
    	 'popContent': {
            deep: true,
            handler: function (newVal, oldVal) {
                immediate: true;
                this.popContent=userNameBg.Brxx_List
        		brzcListUser.jbbmContent['jbbm']=this.popContent.ryzdbm;
            }
        },
    },*/
    mounted: function () {
        this.popContent=userNameBg.Brxx_List
        this.jbbmContent['jbbm']=this.popContent.ryzdbm;
        console.log(this.jbbmContent['jbbm']);
        // this.xh();
    },
    methods: {
        count:function(cysj,rysj){
            return Math.round((cysj-rysj)/(1000*60*60*24));
        },
        xh: function () {
            this.$nextTick(function () {
                if ($('.xunhuanlist').length > 0) {
                    this.xhitem = parseInt((parseInt(this.$refs.brzcList.clientHeight) - parseInt($('.xunhuanlist').offset().top)) / 30)
                }
            })
        },
        save: function(){
			this.popContent.zyzt = null;
        	var json = JSON.stringify(this.popContent);
            this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=update', json).then(function (data) {
                if (data.body.a == 0) {
                    malert(data.body.c,'top','success');
                } else {
                    malert(data.body.c,'top','defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        //病人基本信息下拉检索
        changeDown: function (event, type, content, searchCon) {
            this.nextFocus(event,'',true);
            if (this[searchCon][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, content, searchCon);
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'jbbm') {
                    this.jbbmContent['jbbm']=this.jbbmContent['jbmb']
                    this.popContent['ryzdbm'] = this.jbbmContent['jbmb'];
                    this.popContent['ryzdmc'] = this.jbbmContent['jbmc'];
                }
            }
        },

        //当输入值后才触发
        change: function (add, type, val) {
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (type == 'jbbm') {
                this.jbbmContent['jbbm'] = val;
                if (this.jbbmContent['jbbm'] == undefined || this.jbbmContent['jbbm'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbbmContent['jbbm'];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm'
                    + '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) {//不是第一页则需要追加
                            for (var i = 0; i < data.d.list.length; i++) {
                                brzcListUser.jbsearchCon.push(data.d.list[i]);
                            }
                        } else {
                            brzcListUser.jbsearchCon = data.d.list;
                        }
                        brzcListUser.page.total = data.d.total;
                        brzcListUser.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }

        },

        //鼠标双击（入院诊断信息）
        selectJbbm: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change(true, 'jbbm', this.jbbmContent['jbmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {     // 否则就是选中事件,为json赋值
                this.jbbmContent = item;
                this.jbbmContent['jbbm']=this.jbbmContent['jbmb']
                this.popContent['ryzdbm'] = this.jbbmContent['jbmb'];
                this.popContent['ryzdmc'] = this.jbbmContent['jbmc'];
                $(".selectGroup").hide();
            }
        },




    },
});
var pageList='';
function tabBg(page, index, event,num) {
    if (num) {
        userNameBg.page = page
    }
    if (userNameBg.page == page &&pageList==page) {
        if(num==10){
            dybl()
        }
        xyz()

    } else {
        pageList=page;
        userNameBg.page = '';
        $('.isative').removeClass('active');
        fyxmTab.num = index;
        userNameBg.num = index;
        $(event).addClass('active');
        $(".loadPage").load(page + ".html", '', function () {
            if(num==10){
                dybl()
            }

        }).fadeIn(300);
    }

}
function newPage() {
    poplj.isShow=true
}
function tabPage(page, index, event, num) {
    if (this.page == 'userPage/hzgl') {
        xyz()
    } else {

    }
}

var orignalSetItem = sessionStorage.setItem;
sessionStorage.setItem = function (key, newValue) {
    var setItemEvent = new Event('setItemEvent');
    setItemEvent.newValue = newValue;
    window.dispatchEvent(setItemEvent);
    orignalSetItem.apply(this, arguments);
};
laydate.render({
    elem: '#time'
    , trigger: 'click'
    , theme: '#1ab394'
    , done: function (value, data) {
        pop.popContent.time = value;
    }
});
laydate.render({
    elem: '#rysj'
    , trigger: 'click',
    format:'yyyy-MM-dd HH:mm:ss'
    , theme: '#1ab394'
    , done: function (value, data) {
        brzcListUser.brxxContent.ryrq = value;
    }
});
laydate.render({
    elem: '#cysj'
    , trigger: 'click',
    format:'yyyy-MM-dd HH:mm:ss'
    , theme: '#1ab394'
    , done: function (value, data) {
        brzcListUser.brxxContent.cysj = value;
    }
});
