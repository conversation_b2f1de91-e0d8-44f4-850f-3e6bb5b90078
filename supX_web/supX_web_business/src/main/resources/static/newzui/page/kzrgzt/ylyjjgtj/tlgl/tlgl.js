var titleTab=new Vue({
    el:'.titleTab',
    data:{
    index:0
    },
    mounted:function(){

    },
    methods:{
        tab:function (index) {
            this.index=index;
            content.index=index
        }
    },
});
var content=new Vue({
    el:'.content',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        beginrq:'',
        endrq:'',
        index:0,
    },
    created:function(){
    },
    mounted:function(){
        this.ajaxChart();
        //初始化检索日期！为今天0点到今天24点
        var myDate=new Date();
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date');
        this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24, 'date');
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#dateStart',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.beginrq = value;
                } else {
                    content.beginrq = '';
                }
                //获取一次列表
            }
        });
        laydate.render({
            elem: '#dateEnd',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.endrq = value;
                } else {
                    content.endrq = '';
                }
                //获取一次列表
            }
        });
    },
    updated:function(){
        content.ajaxChart()
    },
    methods:{
        newOpenPage(){
            if(this.index==1){
                this.topNewPage('死亡讨论-认不到','page/zyysz/bqgl/swtl/swtlUser/hzgl.html')
            }else {
                this.topNewPage('疑难病讨论-认不到','page/zyysz/bqgl/ynbtl/swtlUser/hzgl.html') // 为了方便维护都在一个目录下面
            }

        },
        edit:function(){

        },
        ajaxChart:function (view) {
            var dataArrName=[];
            var dataArr=[];
            for(var i=0;i<40;i++){
                dataArrName.push('李\n浩\n僧\n本');
                dataArr.push(parseInt(10*Math.random()))
            }
            var myChart = echarts.init(document.getElementById('canvas'));
            option = {
                title: {
                    text: '科室医生会议次数统计',
                    x:'center',
                    y:'top',
                },
                color: ['#02a9f5'],
                tooltip : {
                    trigger: 'axis',
                    axisPointer : {
                        type : 'shadow'
                    },
                    formatter:function (params,ticket,callback ) {
                        return '<span>'+params[0].value+'次</span>'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis : [
                    {
                        type : 'category',
                        data : dataArrName,

                        axisTick: {
                            alignWithLabel: true
                        },
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92',
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#fff',
                            },
                        },
                        splitLine:{
                            show:true,
                            lineStyle:{
                            },
                        },
                    },
                ],
                yAxis : [
                    {
                        splitNumber:10,
                        type : 'value',
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92'
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine:{
                            show:false,
                            lineStyle:{
                                color:'#fff'
                            },
                        },
                        axisTick:{

                        },
                    },
                ],
                series : [
                    {
                        name:'',
                        type:'bar',
                        barWidth: '11',
                        animationDuration: function (idx) {
                            return idx * 100;
                        },
                        data:dataArr,
                        label:{
                            color:'#b6babb'
                        },

                    },

                ]
            };
            if (option && typeof option === 'object') {
                myChart.setOption(option,true);
            }

        }
    },
});
