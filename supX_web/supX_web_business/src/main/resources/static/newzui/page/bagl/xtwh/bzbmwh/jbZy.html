<div id="jbzy" class="contextInfo">
	<!--<div class="panel box-fixed" style="top:65px;">-->
		<!--<div class="tong-top">-->
			<!--<button class="tong-btn btn-parmary" @click="addData"><span class="fa fa-plus padd-r-5"></span>新增</button>-->
			<!--<button class="tong-btn btn-parmary-b" @click="getData"><span class="fa fa-refresh padd-r-5"></span>刷新</button>-->
			<!--<button class="tong-btn btn-parmary-b" @click="remove"><span class="fa fa-trash-o paddr-r5"></span>删除</button>-->
		<!--</div>-->
		<!--<div class="tong-search">-->
			<!--<div class="zui-form">-->
				<!--<div class="zui-inline ">-->
					<!--<label class="zui-form-label">检索</label>-->
					<!--<div class="zui-input-inline width162">-->
						<!--<input type="text" class="zui-input" id="jbZyjsvalue" @keydown="searchHc()"/>-->
					<!--</div>-->
				<!--</div>-->
			<!--</div>-->
		<!--</div>-->
	<!--</div>-->
	<div class="zui-table-view ybglTable" z-height="full" style="border: none;">
		<div class="zui-table-header">
			<table class="font-14 zui-table table-width50">
				<thead>
				<tr>
					<th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox  @result="reCheckBox" :list="'jsonList'"
																									   :type="'all'" :val="isCheckAll">
                            </input-checkbox></span></div></th>
					<th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>疾病编码</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>疾病名称</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>统计码</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>级别</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>五笔码</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>类型</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>停用标志</span></div></th>
					<th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
				</tr>
				</thead>
			</table>
		</div>
		<div class="zui-table-body" @scroll="scrollTable($event)">
			<table class="zui-table table-width50">
				<tbody>
				<tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
					<td class="cell-m"><div class="zui-table-cell cell-m">
						<input-checkbox @result="reCheckBox" :list="'jsonList'"
										:type="'some'" :which="$index"
										:val="isChecked[$index]">
						</input-checkbox>
					</div>
					</td>
					<td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.jbbm"></div></td>
					<td>
						<div class="zui-table-cell cell-s " v-text="item.jbmc">
						</div>
					</td>
					<td>
						<div class="zui-table-cell cell-s " v-text="item.pydm">
						</div>
					</td>
					<td><div class="zui-table-cell cell-s" v-text="item.tjm"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.jb"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.wbbm"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="bazyjblx_tran[item.lx]"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div></td>
					<td class="cell-s"><div class="zui-table-cell cell-s">
							<span class="flex-center padd-t-5">
								<em class="width30">
									<i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>
								</em>
							</span>

					</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
		<div class="zui-table-fixed table-fixed-l">
			<div class="zui-table-header">
				<table class="zui-table">
					<thead>
					<tr>
						<th class="cell-m"><div class="zui-table-cell cell-m">
                            <input-checkbox   @result="reCheckBox" :list="'jsonList'"
											 :type="'all'" :val="isCheckAll">
                            </input-checkbox></div></th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="zui-table-body" @scroll="scrollTableFixed($event)">
				<table class="zui-table">
					<tbody>
					<tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
						<td class="cell-m"><div class="zui-table-cell cell-m">
							<input-checkbox @result="reCheckBox" :list="'jsonList'"
											:type="'some'" :which="$index"
											:val="isChecked[$index]">
							</input-checkbox>
						</div></td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
</div>
<!--侧边窗口-->
<div class="side-form  pop-width" :class="{'ng-hide':nums==1}"  style="padding-top: 0;"  id="brzcList" role="form">
	<div class="fyxm-side-top">
		<span v-text="title"></span>
		<span class="fr closex ti-close" @click="closes"></span>
	</div>
	<!--值域类别-->
	<div class="ksys-side">
        <span class="span0">
            <i>疾病编码</i>
            <input class="zui-input border-r4" v-model="popContent.jbbm" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
		<span class="span0">
            <i>疾病名称</i>
            <input class="zui-input border-r4" v-model="popContent.jbmc" :data-notEmpty="true" @keydown="nextFocus($event)"
				   @blur="setPYDM(popContent.jbmc,'popContent','pydm')">
        </span>
		<span class="span0">
            <i>拼音代码</i>
            <input class="zui-input border-r4 background-h" v-model="popContent.pydm" disabled="disabled">
        </span>
		<span class="span0">
            <i>统计码</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" @keydown="nextFocus($event)">
        </span>
		<span class="span0">
            <i>级别</i>
            <input class="zui-input border-r4" v-model="popContent.jb" @keydown="nextFocus($event)">
        </span>
		<span class="span0">
            <i>五笔码</i>
            <input class="zui-input border-r4" v-model="popContent.wbbm" @keydown="nextFocus($event)">
        </span>
		<span class="span0">
            <i>类型</i>
            <select-input @change-data="resultChange" :data-notEmpty="false"
						  :child="bazyjblx_tran" :index="popContent.lx" :val="popContent.lx"
						  :name="'popContent.lx'">
            </select-input>
        </span>
		<span class="span0">
            <i>停用标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
							  :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
							  :name="'popContent.tybz'">
              </select-input>
			<input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
	</div>

	<div class="ksys-btn">
		<button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
		<button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
	</div>
</div>




<script type="text/javascript" src="jbZy.js"></script>
    
	
