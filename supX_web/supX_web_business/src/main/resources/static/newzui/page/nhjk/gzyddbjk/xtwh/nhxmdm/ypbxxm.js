/**
 * Created by mash on 2017/10/8.
 */
var yp_toolMenu = new Vue({
    el: '.yp_toolMenu',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        hoverIndex1: undefined,
        activeIndex1: undefined,
        param: {
            page: 1,
            rows: 10,
            parm: ''
        },
        dmzt_tran: {
            'qb': '全部',
            'yd': '已对码',
            'wd': '未对码',
        },
        yfkfList: [],
        bxlbbm: null,
        bxurl: null,
        searchtext2: null,
        type: 'qb',
    },
    mounted: function () {
        this.getKfData()
    },
    methods: {
        getKfData: function () {
            //库房列表
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf', function (data) {
                if (data.a == 0) {
                    yp_toolMenu.yfkfList = data.d.list;
                    yp_toolMenu.param.yfbm = data.d.list[0]['kfbm']
					ypXmMx.param.yfbm = data.d.list[0]['kfbm']
                    yp_toolMenu.$forceUpdate()
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
            //获取列表
        },
		resultRydjChange: function (val) {
			Vue.set(ypXmMx.param, 'yfbm', val[0]);
			Vue.set(ypXmMx.param, 'yfmc', val[4]);
			this.getData()
		},
        changeType: function (val) {
			this.type = val[0];
            this.getData();
        },
        getbxlb: function () {
            var param = {
                bxjk: "001"
            };
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" +
                JSON.stringify(param),
                function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            yp_toolMenu.bxlbbm = json.d.list[0].bxlbbm;
                            yp_toolMenu.bxurl = json.d.list[0].url;
                        }
                    } else {
                        malert("保险类别查询失败!" + json.c)
                    }
                });
        },
        // 请求保险类别
        getData: function () {
            ypXmMx.param.psyp = null;
            ypXmMx.param.yyff = null;
            if (this.type == 'yd') {
                ypXmMx.param.psyp = '1';
            } else if (this.type == 'wd') {
                ypXmMx.param.yyff = '1';
            }
            ypXmMx.param.parm = this.searchtext2;
			// yp_toolMenu.bxurl='http://192.168.0.154:8080/interface/gzydnh/post';
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=queryYp&parm=" + JSON.stringify(ypXmMx.param),
                function (json) {
                    if (json.a == 0) {
                        var res = eval('(' + json.d + ')');
                        ypXmMx.totlePage = Math.ceil(res.total / ypXmMx.param.rows);
                        ypXmMx.jsonList = res.list;

                    } else {
                        malert(json.c,'top','defeadted');
                    }
                });

        },
        // 保存项目详情
        save: function (bxlbbm, ypbm, bxxmlb, bxxmbm, bxxmmc) {
            var param = {
                'page': 1,
                'rows': 30,
                'bxlbbm': bxlbbm,
                'ypbm': ypbm,
                'bxxmlb': bxxmlb,
                'bxxmbm': bxxmbm,
                'bxxmmc': bxxmmc
            };
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=saveYp&parm=" + JSON.stringify(param),
                function (json) {
                    if (json.a == 0) {
                        malert("保存诊疗项目成功！", 'top', 'success');
                        //                            		zl_toolMenu.getData();
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });
        },
        //获取药品项目
        loadXm: function () {
            var param = {
                page: 1,
                rows: 30,
                bxlbbm: yp_toolMenu.bxlbbm
            };
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=getYp&parm=" + JSON.stringify(param),
                function (json) {
                    if (json.a == 0) {
                        malert("获取诊疗项目成功！", 'top', 'success');
                        yp_toolMenu.getData();
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });
        },
        //自动对码（项目名称）
        autoDm: function () {
            var param = {
                page: 1,
                rows: 30
            };
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=autoDmYp&parm=" + JSON.stringify(param),
                function (json) {
                    if (json.a == 0) {
                        malert("自动对码（项目名称）成功！", 'top', 'success');
                        yp_toolMenu.getData();
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });
        },
    }
});
yp_toolMenu.getbxlb();
var ypBxXm = new Vue({
    el: '.ypBxXm',
    mixins: [tableBase,baseFunc],
    data: {
        jsonList: [],
        searchCon: [],
        param: {},
    	page: {
			page: 1,
			rows: 20,
			total: null
		},
    },
	mounted:function(){
		this.getData();
	},
    updated: function () {
        changeWin()
    },
    methods: {
    	getData: function () {
            var param = {
                bxjk: '001'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" +
                JSON.stringify(param),
                function (json) {
                    ypBxXm.totlePage = Math.ceil(json.d.total / ypBxXm.param.rows);
                    ypBxXm.jsonList = json.d.list;
                });
        },
        checkOne: function () {
            yp_toolMenu.getData();
        }
    }
});

var ypXmMx = new Vue({
    el: '.ypXmMx',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        qjIndex: null,
        jsonList: [],
        isEdit: null,
        text: null,
        page: {
            page: 1,
            rows: 50,
            total: null
        },
        param: {},
        popContent: {},
        searchCon: {},
        selSearch: -1,
        dg: {
            page: 1,
            rows: 50,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {
            'fw': dic_transform.data.nhtclb_tran
        },
        them: {'项目编码': 'bm', '项目名称': 'mc', '项目类别': 'xmlb', '范围': 'fw','批准文号':'pzwh','报销比例':'bxbl','规格':'gg','生产厂家':'scdw'}
    },
    methods: {
    	getData:function(){
    		yp_toolMenu.getData();
        },
        edit: function (index) {
            console.log(index);
            this.isEdit = index;
        },
        // 点击进行赋值的操作
        selectOne: function (item) {
            if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++; // 设置当前页号
                this.searching(index, true, 'bxxmmc', ypXmMx.jsonList[index].bxxmmc); // 传参表示请求下一页,不传就表示请求第一页
            } else { // 否则就是选中事件,为json赋值
                ypXmMx.popContent = item;
                Vue.set(ypXmMx.jsonList[ypXmMx.qjIndex], 'bxxmmc', ypXmMx.popContent['mc']);
                ypXmMx.jsonList[ypXmMx.qjIndex].bxxmbm = this.popContent.bm;
                ypXmMx.jsonList[ypXmMx.qjIndex].bxxmlb = this.popContent.xmlb;
                ypXmMx.jsonList[ypXmMx.qjIndex].fw = this.popContent.fw;
                yp_toolMenu.save(yp_toolMenu.bxlbbm, ypXmMx.jsonList[ypXmMx.qjIndex]['xmbm'], ypXmMx.jsonList[ypXmMx.qjIndex]['bxxmlb'], ypXmMx.jsonList[ypXmMx.qjIndex]['bxxmbm'], ypXmMx.jsonList[ypXmMx.qjIndex]['bxxmmc']);
                $(".selectGroup").hide();
            }
        },
        changeDown: function (index, event, type) {
            ypXmMx.qjIndex = index;
            //if(this['searchCon'][this.selSearch] == undefined) return;
            //this.keyCodeFunction(event, 'popContent', 'searchCon');
            this.inputUpDown(event, 'searchCon', 'selSearch')
            this.popContent = this['searchCon'][this.selSearch]
            if (event.code == 'Enter' || event.keyCode == 13 || event.code == 'NumpadEnter') {
                if (this.popContent) {
                    Vue.set(ypXmMx.jsonList[index], 'bxxmmc', ypXmMx.popContent['mc']);
                    ypXmMx.jsonList[index].bxxmbm = this.popContent.bm;
                    ypXmMx.jsonList[index].bxxmlb = this.popContent.xmlb;
                    ypXmMx.jsonList[index].fw = this.popContent.fw;
                    yp_toolMenu.save(yp_toolMenu.bxlbbm, ypXmMx.jsonList[index]['xmbm'], ypXmMx.jsonList[index]['bxxmlb'], ypXmMx.jsonList[index]['bxxmbm'], ypXmMx.jsonList[index]['bxxmmc']);
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                    this.selSearch = -1
                } else {
                    yp_toolMenu.save(yp_toolMenu.bxlbbm, ypXmMx.jsonList[index]['xmbm'], ypXmMx.jsonList[index]['bxxmlb'], ypXmMx.jsonList[index]['bxxmbm'], ypXmMx.jsonList[index]['bxxmmc']);
                }
            }
        },
        // 输入内容进行检索
        searching: function (index, add, type, val) {
            ypXmMx.qjIndex = index;
            this.jsonList[index]['bxxmmc'] = val;
            this.jsonList[index]['mc'] = '';
            this.jsonList[index]['bxxmbm'] = '';
            this.jsonList[index]['fw'] = '';
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            ypXmMx.popContent = {};
            ypXmMx.dg['parm'] = val;
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=xmbm&method=query&parm=" + JSON.stringify(ypXmMx.dg),
                function (json) {
                    if (json.a == 0) {
                        var res = eval('(' + json.d + ')');
                        if (add) {
                            for (var i = 0; i < res.list.length; i++) {
                                ypXmMx.searchCon.push(res.list[i]);
                            }
                        } else {
                            ypXmMx.searchCon = res.list;
                        }
                        ypXmMx.page.total = res.total;
                        //ypXmMx.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });
        }
    }
});

$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});
