.sjys-box{
    width: 100%;
    padding: 4px 10px 0 20px;
    box-sizing: border-box;
}
.sjys-time-line li{
    width: 100%;
    position: relative;
    float: left;
    padding-bottom: 20px;
}
.sjys-time-line li:after{
    content: '';
    top: 20px;
    width: 2px;
    left:6px;
    height:100%;
    background:#eff1f4;
    position: absolute;
    z-index: 9;
}

.sjys-time-line li:last-child:after{
     background: none;
 }
.sjys-dsh{
    width:60px;
    height: 61px;
    position: absolute;
    right:0px;
    top:0;
    background: url("/newzui/pub/image/dsh.png") top right no-repeat;
    background-size: 60px 61px;
 }
.sjys-wtg{
    background: url("/newzui/pub/image/wtg.png") top right no-repeat;
    background-size: 60px 61px;
 }
.sjys-dot{
    display: block;
    background:#44cebd;
    width:14px;
    height:14px;
    border-radius:100%;
    position: absolute;
    left: 0;
    top: 6px;
    z-index: 99;
}
.sjys-dot1{
    background:#bcbec0;
    width:10px;
    height:10px;
    border-radius:100%;
    display: block;
    position: absolute;
    left: 2px;
    top: 6px;
    z-index: 99;
}
.sjys-dot2{
    background:#feb151;
}
.sjys-check{
    display: flex;
    justify-content: space-between;
    padding-bottom: 5px;

}
.sjys-check h3{
    font-size:17px;
    color:#fa6969;
    font-weight: 600;
    line-height:22px;
    text-align:left;
    padding-left:24px;
}
.sjys-btn{
    background:#1abc9c;
    border-radius:41px;
    width:51px;
    height:24px;
    line-height: 24px;
    color: #fff;
    position: absolute;
    right: 53px;
    top: 0;
    z-index: 12;
}
.sjys-btn1{
    background:#feb151;
}
.sjys-btn:hover{
color: rgba(255,255,255,0.8);
}
.sjys-inquiry{
    background:rgba(250,105,105,0.06);
    border:1px solid #f8baba;
    border-radius:25px;
    line-height: 18px;
    width:auto;
    color:#475158;
    font-size:14px;
    float: left;
    padding: 6px 15px;
    margin-left: 24px;
}
.sjys-charge{
    font-size:12px;
    color:#9b9b9b;
    line-height:22px;
    text-align:left;
    margin-left: 40px;
    float: left;
    width: 100%;
    padding-top: 4px;

}
.sjys-fill{
    position: fixed;
    bottom:35px;
    right: 25px;
    width:68px;
    height:68px;
    padding: 10px;
    z-index: 999;
    box-sizing: border-box;
    background:#1abc9c;
    box-shadow:0 0 10px 0 rgba(2,28,23,0.49);
    border-radius:100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    cursor: pointer;
}
.icon-27:before{
    font-size: 27px;
}
.sjys-font12{
    font-size: 12px;
    color: #fff;
}
.sjys-width {
    width: 380px;
    height: 220px;
    position: absolute;
    top: calc((100vh - 220px) / 2);
    left: calc((100vw - 380px) / 2);
    z-index: 9999;
    background: #fff;
    box-shadow: 0 0 18px 0px rgba(0,0,0,0.5)
}
.sjys-top {
    width: 100%;
    height: 46px;
    background: #1abc9c;
    color: #fff;
    font-size: 16px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.sjys-content{
    width: 100%;
    float: left;
    padding: 43px 40px 0px 40px;
    box-sizing: border-box;
}
.sjys-pop-btn {
    width: 100%;
    padding: 60px 5px 0 0;
    display: flex;
    justify-content: flex-end;
}
.sjys-textarea{
    width: 100%;
    padding: 13px 15px 0;
    height: 90px;
    box-sizing: border-box;
    float: left;
}
.sjys-textarea textarea{
    width: 100%;
    height: 90px;
    padding: 10px;
    box-sizing: border-box;
    border:1px solid #dfe3e9;
    border-radius:4px;
    -webkit-appearance: none;
}
.sjys-pop-btn-t{
 padding: 33px 5px 0 0;
}
.sjys-report{
    background:#ffffff;
    box-shadow: 0 0px 8px 0 rgba(2,41,33,0.30);
    border-radius:4px;
    width:598px;
    height:370px;
    position:fixed;
    padding: 12px 16px 6px;
    box-sizing: border-box;
    top: calc((100vh - 370px) /2);
    left: calc((100vw - 598px) /2);
}
.report-title{
width: 100%;
display: flex;
justify-content: center;
position: relative;
padding-bottom: 3px;

}
.report-title h2{
    font-size:22px;
    color:#000;
    }
.report-close{
    position: absolute;
    right: -3px;
    top: 3px;
    cursor: pointer;
}
.icon-font24:before{
    font-size: 24px;
}
.icon-c1056:before {
    color: rgba(26,188,156,.56);
}
.report-width{
width: 100%;
height:230px;
}
.report-text{
    width: 100%;
    font-size:14px;
    color:#475158;
    text-align: justify;
    line-height: 18px;
    overflow: hidden;
}
.report-standard{
    width: 100%;
    float: left;
    background: url("/newzui/pub/image/gtt.png") center left no-repeat;
    background-size: 16px 16px;
    color:#FF5C63;
    font-size: 12px;
    padding-left: 20px;
    margin-top: 10px;
}
.report-director{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-top: 7px;
    position: relative;
}
.director-left,.director-right{
 width: 180px;
 display: flex;
 justify-content: flex-start;
}
.director-job{
    line-height: 40px;
    padding-right: 12px;
}
.director-Imgtext{
    width: 40px;

}
.director-img{
    width: 40px;
    height: 40px;
    position: relative;
}
.director-img img{
    width: 38px;
    height: 38px;
    border-radius: 100%;
    object-fit: cover;
}
.director-del{
    opacity:0.8;
    background:#ff5c63;
    width:12px;
    height:12px;
    border-radius:100%;
    display: block;
    position: absolute;
    top: -7px;
    right: 0;
    cursor: pointer;
}
.director-del:after{
    content: '-';
    position: absolute;
    width: 10px;
    height: 2px;
    background: #fff;
    top: 5px;
    left: 1px;
}
.font12{
    font-size: 12px;
}
.director-add{
    width:38px;
    height: 38px;
    background:rgba(255,255,255,0.10);
    border:1px solid #dfe3e9;
    border-radius:100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 2px;
    cursor: pointer;
    margin-left:5px;
}
.director-add:hover{
    background:#edfaf7;
}
.icon-font25:before{
    font-size: 25px;
}
.director-position{
    position: absolute;
    right:-8px;

}

.slide-fade-enter {
    opacity: 0;
}
.slide-fade-leave-to {
    opacity: 0;
}
.slide-fade-enter-active{
    transition: all .3s ease;
}
.slide-fade-leave-active {
    transition: all .3s;
}
.slide-fade-enter{
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
}
.pop-width .ksys-side {
    padding: 15px 13px;
}
.sjys-fill:hover .icon-iocn46:before{
    color: rgba(255,255,255,0.8);
}
.sjys-fill:hover .sjys-font12{
    color: rgba(255,255,255,0.8);
}
.icon-iocn55:hover:before{
   color: rgba(26,188,156,0.56);
}
.icon-cf056:hover:before{
    color: rgba(255,255,255,0.36);
}
*[contenteditable="true"]:empty:before{
    content:attr(placeholder);
    color:#e0e1e2;
}