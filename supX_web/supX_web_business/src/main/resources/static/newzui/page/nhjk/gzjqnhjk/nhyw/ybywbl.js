var fyxmTab = new Vue({
    el: '.fyxm-tab',
    data: {
        num: 0
    },
    created: function () {
        this.$nextTick(function () {
            this.tabBg('rydj')
        })
    },
    methods: {
        tabBg: function (page) {
            $(".loadPage").load(page + ".html").fadeIn(300);
        }
    },
});
function tabBg(page, index, event,num) {
    if(num){
        this.page=page
    }
    if( this.page==page){
    }else{
        this.page=''
        $('.isative').removeClass('active')
        fyxmTab.num = index;
        $(event).addClass('active')
        $(".loadPage").load(page + ".html",'',function () {
            if(num==1){
                setTimeout(function () {
                },1000)
            }

        }).fadeIn(300);
    }
}