<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="x-ua-compatible" content="IE=edge, chrome=1"/>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script type="application/javascript" src="../brgh/insurancePort/001gzydnh/backFun.js"></script>
    <!--<link rel="stylesheet" href="/page/xtwh/xtpz/mzpjgs/mzpjgs.css" media="print"/>-->
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="brgh.css">
    <title>病人挂号</title>
</head>

<body class="skin-default padd-l-10 padd-r-10">
<div class="printArea printShow"></div>

<!-- 打印挂号票据 begin -->
<div id="mz-print" v-show="isShow" v-cloak class="print-mzsffp" style="font-size:3.4mm;line-height: 1.4">
    <p style="padding-left: 10mm;" v-text="printData[0].fphm">卡号</p>
    <p style="padding-left: 10mm;" v-text="printData[0].brfyPrintList[0].brxm">姓名</p>
    <div style="margin-top: 8.5mm;height: 36mm;overflow: hidden;">
        <div class="flex-container" v-for="(item,index) in printData">
            <div class="flex-one" style="mrgin-right: 1.5mm;" v-text="item.brfyPrintList[0].xmmc">费用项目</div>
            <div class="flex-one" v-text="fDec(item.brfyPrintList[0].fyhj,2)">金额</div>
        </div>
    </div>
    <p style="padding-left:50%;line-height: 7mm;text-align: center" v-text="fDec(printData[0].fyhj,2)">合计</p>
    <p style="padding-left:12mm;text-align: center;line-height: 7.5mm;" v-text="printData[0].fyhjdx">金额大写</p>
    <p class="flex-container" style="padding-left: 13.5mm;">
        <span style="width: 14mm;height: 4.76mm;overflow: hidden;"
              v-text="printData[0].brfyPrintList[0].czyxm">收费员</span>
        <span style="mrgin-right:3.5mm;width:9mm;text-align: right;"
              v-text="new Date(printData[0].brfyPrintList[0].sfsj).getFullYear()">年</span>
        <span style="mrgin-right:3.5mm;width: 6.5mm;text-align: right;"
              v-text="new Date(printData[0].brfyPrintList[0].sfsj).getMonth() + 1">月</span>
        <span style="width:7mm;text-align:right;"
              v-text="new Date(printData[0].brfyPrintList[0].sfsj).getDate()">日</span>
    </p>
    <p style="padding-left: 13.5mm;">四川省建筑医院</p>
</div>
<!-- 打印挂号票据 end -->

<div class="wrapper context panel printHide ">
    <div v-cloak class="panel-head bg search padd-b-10 padd-l-10 padd-t-10 padd-r-10">
        <div class="flex-container flex-jus-sp flex-align">
            <div class="flex-container flex-align-s whiteSpace flex-align-c">
                <button class="tong-btn btn-parmary" @click="updata">新增</button>
                <div class="tool-left position">
                    <input type="text" title="点击后刷卡" class="zui-input wh120" placeholder="请点击后刷卡" v-model="ykth"
                           @keydown="loadYkt($event)" id="ykth"/>
                </div>
                <div class="tool-left position">
                    <input v-model="text" @input="searching(null,$event.target.value)" @keyDown="changeDown($event)"
                           id="user" type="text" name="phone" class="zui-input wh120" placeholder="请填写姓名/证件号码/手机号"/>
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </div>
                <!--
            <div class="tool-left position padd-r-5">
                <input class="zui-input" :value="text" @input="searching(null,$event.target.value)"
                       @keyDown="changeDown($event)"
                       placeholder="请点击后刷卡" id="jsk">
                <search-table :message="searchCon" :selected="selSearch"
                              :them="them" :them_tran="them_tran" :page="page"
                              @click-one="checkedOneOut" @click-two="selectOne">
                </search-table>
            </div>
            -->
                <!--<button class="tong-btn btn-parmary">检索</button>-->
                <button class="tong-btn btn-parmary-b " @click="loadIdCard()"><i class=" icon-width icon-dsfz"></i>读取身份证
                </button>
                <button class="tong-btn btn-parmary-b " @click="clickBx()"><i class="  icon-width icon-dylk"></i>读取保险
                </button>
                <button class="tong-btn btn-parmary-b " @click="loadBx()"><i class=" icon-width icon-dybk"></i>读取社保卡
                </button>

                <button v-if="N05001200229 == '1'" class="tong-btn btn-parmary-b " @click="printJkk()"><i
                        class="padd-l-20 icon-width icon-dybk"></i>打印
                </button>
                <button v-if="N05001200229 == '1'" class="tong-btn btn-parmary-b " @click="regJkk()"><i
                        class="padd-l-20 icon-width icon-dybk"></i>注册
                </button>
                <div v-if="N05001200238=='1'" @click="resultChangeData" :class="{'color-green':lstd=='1'}"
                     class="no-pading wh70 cursor">
                    {{nstd_tran[lstd]}}
                    <!--                        <select-input @change-data="resultChangeData" :not_empty="true" :child="nstd_tran"-->
                    <!--                                      :index="lstd"-->
                    <!--                                      :val="lstd" :name="'lstd'" :search="false" :disable="false"-->
                    <!--                                      :phd="''">-->
                    <!--                        </select-input>-->
                </div>
                <div style="color: red;font-size: 12px;" class="padd-l-10">当前模式为：[{{ghms}}] {{ghfs}} {{dyfs}}</div>
                <div style="color: red" class="padd-l-10">已挂人次:{{hycList.yghs}}&emsp;&emsp;待诊人次:{{hycList.djzs}}&emsp;&emsp;剩余号源:{{hycList.syhys}}</div>
            </div>
            <div class=" text-right">
                <button class="zui-btn btn-warning" id="but_regyl" @click="showLogs()">挂号记录</button>
            </div>
        </div>
    </div>
    <div v-cloak class="contextStyle  padd-r-20 padd-l-20 contextSize contextInfo">
        <div class="zui-row ">
            <div class="tab-card">
                <!--<div class="conTit">-->
                <!--<div>挂号信息</div>-->
                <!--</div>-->
                <div class="tab-card-header">
                    <div class="tab-card-header-title">挂号信息</div>
                </div>
                <div class="grid-box  tab-card-body">
                    <div class="flex-container  flex-wrap-w">
                        <div class=" flex-container flex-align-c padd-r-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red">挂号科室</span>
                            <select-input class="wh150" :disabled="isEdit" @change-data="resultzcChange"
                                          :not_empty="true"
                                          :child="ghKsList"
                                          :index="'ksmc'" :index_val="'ksbm'" :val="json.ghks" :name="'json.ghks'"
                                          :disable="isEdit"
                                          :search="true" :phd="''">
                            </select-input>
                        </div>
                        <div class="flex-align-c flex-container padd-r-20 margin-b-10">
                            <span class="padd-r-5 ft-14">接诊医生</span>

                            <input v-if="N05001200245 == '0'" :disabled="isEdit" type="text" class="zui-input wh150"
                                   :data-notEmpty="cs00400100204=='2'&& cs00400100204=='3'?true:false"
                                   v-model="json.jzysxm"
                                   @keydown="changeDown2($event,'text')"
                                   @input="change2(null,$event.target.value,$event)"
                                   id="jzys">
                            <search-table2 v-if="N05001200245 == '0'" :message="searchCon2" :selected="selSearch"
                                           :page="page" :them="them2"
                                           :them_tran="them_tran2" @click-one="checkedOneOut" @click-two="selectOne2">
                            </search-table2>

                            <select-input class="wh150" v-if="N05001200245 == '1'" @change-data="resultzcChangeJzys"
                                          :not_empty="false"
                                          :child="ghYsList" :index="'ryxm'" :index_val="'rybm'" :index_mc="'jzysxm'"
                                          :val="json.jzys"
                                          :name="'json.jzys'" :search="true" :disable="false" :phd="''" id="jzys">
                            </select-input>

                        </div>
                        <div class="flex-align-c padd-r-20 flex-container margin-b-10">
                            <span class="padd-r-5 ft-14">号源池&emsp;</span>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false"
                                          :child="hycList" :index="'hymc'" :index_val="'hyid'" :val="json.hyid"
                                          :name="'json.hyid'" :disable="hycjy">
                            </select-input>
                        </div>

                        <!--<div class="zui-inline  col-xxl-3 flex-container margin-b-10">-->
                        <!--<label class="zui-form-label">预约时间</label>-->
                        <!--<input class="zui-input" type="text" data-notEmpty="false" @click="fybzShow=true">-->
                        <!--&lt;!&ndash;<date-div v-show="fybzShow" :time="'time'" :list="objData" :num="'num'"&ndash;&gt;-->
                        <!--&lt;!&ndash;@reslutTime="reslutTime"></date-div>&ndash;&gt;-->
                        <!--</div>-->


                        <div class="flex-align-c flex-container padd-r-20 margin-b-10">
                            <span class="padd-r-5 ft-14" :class="lstd == '0' ? 'font-bolder red' :''">挂号种类</span>
                            <select-input class="wh150" @change-data="resultzcChange"
                                          :not_empty="lstd=='0'?true:'removeFalse'" :child="ghZlList"
                                          :index="'ghzlmc'"
                                          :index_val="'ghzlbm'" :val="type" :name="'type'" :search="true"
                                          :disable="isEdit"
                                          :phd="''">
                            </select-input>
                        </div>
                        <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                            <span class="padd-r-5 ft-14">是否急诊</span>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false"
                                          :child="istrue_tran"
                                          :index="json.jzgh"
                                          :val="json.jzgh" :name="'json.jzgh'" :search="true" :disable="false"
                                          :phd="''">
                            </select-input>
                        </div>
                        <div class="flex-align-c padd-r-20 flex-container margin-b-10">
                            <span :class="lstd == '0' ? 'font-bolder red' :''"
                                  class="padd-r-5 ft-14">费&emsp;&emsp;别</span>
                            <select-input class="wh150" @change-data="resultzcChange"
                                          :not_empty="lstd=='0'?true:'removeFalse'" :child="brFbList" :index="'fbmc'"
                                          :index_val="'fbbm'" :val="json.fbbm" :name="'json.fbbm'" :search="true"
                                          :disable="false"
                                          :phd="''">
                            </select-input>
                        </div>
                        <div class="flex-align-c padd-r-20 flex-container margin-b-10">
                            <span class="padd-r-5 ft-14">保险类别</span>
                            <select-input class="wh150" :disable="true" @change-data="resultChange" :not_empty="false"
                                          :child="bxLbList" :index="'bxlbmc'"
                                          :index_val="'bxlbbm'" :val="json.bxlbbm" :name="'json.bxlbbm'" :search="true"
                                          :disable="false"
                                          :phd="''">
                            </select-input>
                        </div>
                        <div class="flex-align-c padd-r-20 flex-container margin-b-10">
                            <span class="padd-r-5 ft-14">医保卡号</span>
                            <input @mousewheel.prevent type="number" maxlength="16" @keydown.up.prevent
                                   @keydown.down.prevent class="zui-input wh150"
                                   v-model.trim="json.ybkh" type="text" data-notEmpty="false" @blur="ybkhBlur"
                                   @keydown="nextFocus($event)"
                                   :disable="false">
                        </div>
                        <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                            <span class="padd-r-5 ft-14">联&ensp;系&ensp;人</br>电&emsp;&emsp;话</span>
                            <input @keydown.up.prevent @keydown.down.prevent @mousewheel.prevent class="zui-input wh150"
                                   v-model.trim="json.lxrdh" type="number" data-notEmpty="false"
                                   @keydown="nextFocus($event)"
                                   onkeyup="value=value.replace(/[^\d]/g,'')" id="lxrdh">
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">基本信息</div>
                </div>
                <!--<div class="conInfo">-->
                <!--<div class="conTit">-->
                <!--<div>基本信息</div>-->
                <!--</div>-->
                <div class="grid-box tab-card-body flex-wrap-w  flex-container">
                    <div class="flex-align-c padd-r-20 flex-container margin-b-10">
                        <span class="padd-r-5 ft-14">医&ensp;疗&ensp;卡</br>类&emsp;&emsp;型</span>
                        <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="cards"
                                      :index="'zymc'"
                                      :index_val="'zybm'" :val="json.ylklx" :name="'json.ylklx'" :search="false"
                                      :disable="ylkxx">
                        </select-input>
                    </div>
                    <div class="flex-align-c padd-r-20 flex-container margin-b-10">
                        <span class="padd-r-5 font-bolder red ft-14">患者姓名</span>
                        <input class="zui-input wh150" v-model.trim="json.brxm" type="text"
                               data-notEmpty="true" @keydown="nextFocus($event)" id="hzxm">
                    </div>
                    <div class="flex-align-c padd-r-20 flex-container margin-b-10">
                        <span class="padd-r-5 font-bolder red  ft-14">性&emsp;&emsp;别</span>
                        <select-input class="wh150" @change-data="resultChange"
                                      :not_empty="true" :child="brxb_tran"
                                      :index="json.brxb"
                                      :val="json.brxb" :name="'json.brxb'" :search="false" :disable="false" :phd="''">
                        </select-input>
                    </div>
                    <div class="flex-align-c padd-r-20 flex-container margin-b-10">
                        <span :class="lstd == '0' ? 'font-bolder red' :''" class="padd-r-5 ft-14">国&emsp;&emsp;籍</span>
                        <select-input class="wh150" @change-data="resultChange"
                                      :not_empty="lstd=='0'?true:'removeFalse'" :child="gjList" :index="'gjmc'"
                                      :index_val="'gjbm'" :val="json.brgj" :name="'json.brgj'" :search="true"
                                      :disable="false"
                                      :phd="''" id="gj">
                        </select-input>
                    </div>
                    <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                        <span :class="lstd == '0' ? 'font-bolder red' :''" :data-mz="setMz" class="padd-r-5 ft-14">民&emsp;&emsp;族</span>
                        <select-input class="wh150" @change-data="resultChange" :not_empty="true" :child="mzList"
                                      :index="'mzmc'"
                                      :index_val="'mzbm'" :val="json.brmz" :name="'json.brmz'"
                                      :search="lstd=='0'?true:'removeFalse'"
                                      :disable="false"
                                      :phd="''" id="mz">
                        </select-input>
                    </div>
                    <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                        <span class="padd-r-5 ft-14">婚姻状况</span>
                        <!--<div class="infoIpt">-->
                        <!--<p>婚姻状况</p>-->
                        <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="hyList"
                                      :index="'hyzkmc'"
                                      :index_val="'hyzkbm'" :val="json.hyzk" :name="'json.hyzk'" :search="false"
                                      :disable="false"
                                      :phd="''" id="hyzk">
                        </select-input>
                    </div>
                    <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                        <span class="padd-r-5 ft-14">患者类型</span>
                        <!--<div class="infoIpt">-->
                        <!--<p>患者类型</p>-->
                        <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="ryhzlx_tran"
                                      :index="json.hzlx"
                                      :val="json.hzlx" :name="'json.hzlx'" :search="false" :disable="false" :phd="''"
                                      id="hzlx">
                        </select-input>
                    </div>
                    <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                        <span class="padd-r-5 ft-14">证件类型</span>
                        <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="zjlxList"
                                      :index="'zymc'"
                                      :index_val="'zybm'" :val="cardType" :name="'cardType'" :search="false"
                                      :disable="false"
                                      id="zjlx">
                        </select-input>
                    </div>
                    <div class="flex-align-c  padd-r-20 flex-container margin-b-10">
                        <span class="padd-r-5 ft-14">证件号码</span>
                        <input @blur="setAge()" data-select="no" class="zui-input wh150 title" type="text"
                               v-model.trim="json.sfzjhm"
                               placeholder="" @keydown.enter="setAge($event)" id="zjhm">
                    </div>
                    <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                        <span class="padd-r-5 font-bolder red ft-14">生&emsp;&emsp;日</span>
                        <input v-model="birthday" id="csrq" @blur="dateForVal($event, 'birthday')"
                               class="zui-input wh150"
                               onclick="WdatePicker({ skin: 'whyGreen',maxDate:'%y-%M-%d' })"
                               @keydown="nextFocus($event),dateForVal($event, 'birthday')"
                               data-notEmpty="true">
                    </div>
                    <div class="flex-align-c  padd-r-20 flex-container margin-b-10">
                        <span class="padd-r-5 font-bolder red ft-14">年&emsp;&emsp;龄</span>
                        <input data-notEmpty="true" @keydown.up.prevent @keydown.down.prevent
                               @mousewheel.prevent class="zui-input wh70 margin-r-10" type="number"
                               v-model.trim="json.brnl" @blur="setCsrq()" @keydown.enter="setCsrq($event)"
                               placeholder="">
                        <select-input class="wh70 padd-l-10" @change-data="resultChange"
                                      :not_empty="false" :child="nldw_tran"
                                      :index="json.nldw"
                                      :val="json.nldw" :name="'json.nldw'" :search="false" :disable="false"
                                      :phd="''">
                        </select-input>
                        <!--</div>-->
                    </div>
                    <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                        <span :class="lstd == '0' ? 'font-bolder red' :''" class="padd-r-5 ft-14">职&emsp;&emsp;业</span>
                        <select-input class="wh150" @change-data="resultChange"
                                      :not_empty="lstd=='0'?true:'removeFalse'" :child="zyList" :index="'zymc'"
                                      :index_val="'zybm'" :val="json.zybm" :name="'json.zybm'" :index_mc="'zybmmc'"
                                      :search="true"
                                      id="zy">
                        </select-input>
                    </div>
                    <div class="flex-align-c padd-r-20  flex-container margin-b-10">
                        <span class="padd-r-5 ft-14">联&ensp;系&ensp;人</br>姓&emsp;&emsp;名</span>
                        <input class="zui-input wh150" v-model="json.lxrxm" data-notEmpty="false"
                               @keydown="nextFocus($event)"
                               id="lxrxm">
                    </div>
                    <div class="flex-align-c  padd-r-20 flex-container margin-b-10">
                        <span class="padd-r-5 ft-14">联&ensp;系&ensp;人</br>关&emsp;&emsp;系</span>
                        <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="lxrgxList"
                                      :index="'lxrgxmc'"
                                      :index_val="'lxrgxbm'" :val="json.lxrgx" :search="true" :name="'json.lxrgx'"
                                      id="lxrgx">
                        </select-input>
                    </div>
                    <div class="flex-align-c  padd-r-20 flex-container margin-b-10">
                        <label class="padd-r-5 ft-14">贫困户&emsp;</label>
                        <input class="zui-input wh150" v-model="istrue_tran[json.pkhbz]" type="text"
                               data-notEmpty="false"
                               @keydown="nextFocus($event)" :disabled="ylkxx" id="pkh">
                    </div>
                    <div class="flex-align-c padd-r-20   flex-container margin-b-10">
                        <label class="padd-r-5 ft-14">学校/工</br>作单位&emsp;</label>
                        <input class="zui-input wh150" v-model="json.gzdw" type="text" data-notEmpty="false"
                               @keydown="nextFocus($event)" :disabled="ylkxx">
                    </div>
                    <div v-if="N05001200229 == '1'" class=" padd-r-20 flex-align-c flex-container margin-b-10">
                        <label class="padd-r-5 ft-14">电话号码</label>
                        <input class="zui-input wh150" v-model="json.sjhm" type="text" data-notEmpty="false"
                               @keydown="nextFocus($event)"
                               id="sjhm">
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card" style="padding-bottom: 20px;">
            <div class="tab-card-header">
                <div class="tab-card-header-title">居住信息</div>
            </div>
            <!--<div class="conInfo" style="padding-bottom: 20px;">-->
            <!--<div class="conTit">-->
            <!--<div>居住信息</div>-->
            <!--</div>-->
            <div class="grid-box tab-card-body">
                <div class="col-xxl-12 flex-container flex-align-c">
                    <!--<div class="info">-->
                    <span :class="lstd == '0' ? 'font-bolder red' :''"
                          class="ft-14 whiteSpace padd-r-5">省/市/(区/县)</span>
                    <!--<p>省/市/(区/县)</p>-->
                    <div class=" col-xxl-2">

                        <select-input class="wh150" @change-data="resultzcChange"
                                      :not_empty="lstd=='0'?true:'removeFalse'"
                                      :child="provinceList"
                                      :index="'xzqhmc'"
                                      :index_val="'xzqhbm'" :val="json.jzdsheng" :search="true" :name="'json.jzdsheng'"
                                      :index_mc="'jzdshengmc'" :phd="'省'" id="sheng">
                        </select-input>
                    </div>
                    <div class=" col-xxl-2">
                        <select-input @change-data="resultzcChange" :data-notEmpty="false" :child="cityList"
                                      :index="'xzqhmc'"
                                      :index_val="'xzqhbm'" :val="json.jzdshi" :search="true" :name="'json.jzdshi'"
                                      :index_mc="'jzdshimc'"
                                      :phd="'市'" id="shi">
                        </select-input>
                    </div>
                    <div class=" col-xxl-2">
                        <select-input @change-data="resultzcChange" :data-notEmpty="false" :child="countyList"
                                      :index="'xzqhmc'" :index_val="'xzqhbm'" :val="json.jzdxian" :search="true"
                                      :name="'json.jzdxian'"
                                      :index_mc="'jzdxianmc'" :phd="'区/县'" id="xian">
                        </select-input>
                    </div>
                    <div class=" col-xxl-6 flex-container flex-align-c padd-l-10">
                        <span class="ft-14 whiteSpace padd-r-5">详细地址</span>
                        <!--<div class="selectInput">-->
                        <input data-select="no" class="zui-input" type="text" v-model="json.jzdmc" placeholder="街道/门牌号"
                               @keydown="saveDateHc($event)"
                               id="xxdz"/>
                        <!--</div>-->
                    </div>
                    <!--<div class="infoIpt">
                <p>邮编</p>
                <div class="selectInput">
                    <input type="number" v-model="json.hkdyb" placeholder="自动生成" />
                </div>
            </div>-->
                </div>
            </div>
        </div>
        <!--<model :s="'保存'" :c="'取消'" @default-click="save" :model-show="true" @result-clear="close" @result-close="close"-->
        <!--v-if="bqcy" :title="'贫困户'">-->
        <!--<div class="bqcydj_model">-->
        <!--<div class="flex-container flex-wrap-w">-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">姓&emsp;&emsp;名</span>-->
        <!--<div class="zui-input-inline wh120">-->
        <!--<input class="zui-input" v-model="popContentPkun.xm" type="text"/>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">性&emsp;&emsp;别</span>-->
        <!--<select-input class="wh120" @change-data="resultChange" :not_empty="true" :child="brxb_tran"-->
        <!--:index="popContentPkun.brxb"-->
        <!--:val="popContentPkun.brxb" :name="'popContentPkun.brxb'" :search="false"-->
        <!--:disable="false" :phd="''">-->
        <!--</select-input>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">民&emsp;&emsp;族</span>-->
        <!--<select-input class="wh120" @change-data="resultChange" :not_empty="true" :child="mzList"-->
        <!--:index="'mzmc'"-->
        <!--:index_val="'mzbm'" :val="popContentPkun.brmz" :name="'popContentPkun.brmz'"-->
        <!--:search="true" :disable="false"-->
        <!--:phd="''" id="mz">-->
        <!--</select-input>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">婚&emsp;&emsp;姻</span>-->
        <!--<select-input class="wh120" @change-data="resultChange" :not_empty="true" :child="hyList"-->
        <!--:index="'hyzkmc'"-->
        <!--:index_val="'hyzkbm'" :val="popContentPkun.hyzk" :name="'popContentPkun.hyzk'"-->
        <!--:search="false" :disable="false"-->
        <!--:phd="''" id="hyzk">-->
        <!--</select-input>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">身份证号</span>-->
        <!--<div class="zui-input-inline wh120">-->
        <!--<input class="zui-input" v-model="popContentPkun.sfzh" type="text"/>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">联系电话</span>-->
        <!--<div class="zui-input-inline wh120">-->
        <!--<input class="zui-input" v-model="popContentPkun.lxdh" type="text"/>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">家庭人数</span>-->
        <!--<div class="zui-input-inline wh120">-->
        <!--<input class="zui-input" v-model="popContentPkun.jtrk" type="text"/>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">地&emsp;&emsp;址</span>-->
        <!--<div class="zui-input-inline wh120">-->
        <!--<input class="zui-input" v-model="popContentPkun.dz" type="text"/>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">是否兑低<br/>保&ensp;障&ensp;户</span>-->
        <!--<div class="zui-input-inline wh120">-->
        <!--<select-input class="wh120" @change-data="resultChange" :not_empty="true" :child="nh_tran"-->
        <!--:index="popContentPkun.ddbz"-->
        <!--:val="popContentPkun.ddbz" :name="'popContentPkun.ddbz'" :search="false"-->
        <!--:disable="false" :phd="''">-->
        <!--</select-input>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">空巢老人</span>-->
        <!--<select-input class="wh120" @change-data="resultChange" :not_empty="true" :child="nh_tran"-->
        <!--:index="popContentPkun.kclr"-->
        <!--:val="popContentPkun.kclr" :name="'popContentPkun.kclr'" :search="false"-->
        <!--:disable="false" :phd="''">-->
        <!--</select-input>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">负债金额</span>-->
        <!--<div class="zui-input-inline wh120">-->
        <!--<input class="zui-input" v-model="popContentPkun.fzqk" type="number"/>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">负债金额</span>-->
        <!--<div class="zui-input-inline wh120">-->
        <!--<input class="zui-input" v-model="popContentPkun.fzqk" type="number"/>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="flex-container flex-align-c padd-r-20 padd-b-10">-->
        <!--<span class="padd-r-5">家庭成员是否参加基本医疗保险</span>-->
        <!--<select-input class="wh120" @change-data="resultChange" :not_empty="true" :child="nh_tran"-->
        <!--:index="popContentPkun.yb"-->
        <!--:val="popContentPkun.yb" :name="'popContentPkun.yb'" :search="false"-->
        <!--:disable="false" :phd="''">-->
        <!--</select-input>-->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->
        <!--</model>-->
        <div v-if="gznhType">
		<span style="color:red;font-size:18px;">
		人员属性：<span style="font-weight: bold;">{{gznhObj.jzmzName}}</span>，
		本年度门诊补偿金额：<span style="font-weight: bold;">{{gznhObj.outpCompensateCost}}</span>元，
		本年度慢性病补偿金额：<span style="font-weight: bold;">{{gznhObj.chroCompensateCost}}</span>元。
        本次报销金额：<span style="font-weight: bold;">{{bcfy}}</span>元。
        </span>
        </div>
        <model :s="'确定'" :c="'取消'" @default-click="pkhShow=false" @result-clear="pkhShow=false" :model-show="true"
               @result-close="pkhShow=false" v-if="pkhShow" :title="title">
            <div class="bqcydj_model">
                <div class=" ">
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">姓名:{{pkhObj.XM}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">性别:{{xtwhxb_tran[pkhObj.XB]}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">手机号:{{pkhObj.LXDH}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">地址:{{pkhObj.DZ}}</span>
                    </div>
                </div>
            </div>
        </model>

        <model style="top:10%" :s="'确定'" :c="'取消'" @default-click="bxShow=false" @result-clear="bxShow=false"
               :model-show="false" @result-close="bxShow=false" v-if="bxShow" :title="'请输入参合人员信息'">
            <div class="chxx_model" style="height: 100%">
                <div class="flex-container flex-dir-c flex-one" id="loadPage">
                </div>
            </div>
        </model>
    </div>
    <div class="side-form pop flex-container flex-dir-c" :class="{'ng-hide':!isFold}" v-cloak id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span>挂号记录</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="grid-box">
            <div class="flex-container margin-l-10">
                <div class="flex-container margin-b-15 margin-top20 margin-l-10">
                    <input type="text" name="phone" class="zui-input  wh120 margin-l13 padd-r-10" v-model="jsValue"
                           placeholder="请填写关键字"
                           @keydown.enter="goToPage(1)"/>
                </div>
                <div class="flex-container flex-align-c  margin-b-15 margin-top20 margin-l-10">
                    <input id="dbegin" class="zui-input">
                    <span class="padd-l-10 padd-r-10">至</span>
                    <input id="dEnd" class="zui-input margin-r-10">
                    <button class="tong-btn btn-parmary" @click="getData">查询</button>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-r-10 flex-container flex-dir-c padd-l-10">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-l">挂号序号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">患者姓名</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m">性别</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">挂号科室</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">挂号医生</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">患者年龄</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">挂号时间</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">是否退号</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" @dblclick="edit($index,item)"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.ghxh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m" v-text="brxb_tran[item.brxb]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ghksmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jzysxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.brnl}}{{nldw_tran[item.nldw]}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.ghrq, 'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-S">{{item.thbz==0?'否':'是'}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="zui-table-fixed table-fixed-l">
                <!-- 有浮动就加 table-fixed-r -->
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-l">
                                <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!--@click="hover($index,$event)"-->
                <div class="zui-table-body " @scroll="scrollTableFixed($event)" style="border-right: 1px solid #eee;">
                    <table class="zui-table">
                        <tbody>
                        <tr @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                            v-for="(item, $index) in jsonList"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            class="tableTr2 table-hovers-filexd-l">
                            <td class="cell-l">
                                <div cell="cell-2-0" class="zui-table-cell cell-l">{{item.ghxh}}</div>
                            </td>
                        </tr>
                        </tbody>

                    </table>
                </div>
            </div>
            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>
        </div>
    </div>
</div>
<div class="printHide zui-table-tool conBtu flex-container flex-jus-sb flex-align-c padd-l-20 padd-r-20" v-cloak>
    <div class="flex-container flex-align-c">
        <button class="zui-btn btn-primary" @click="doSave()" v-text="text_sub"></button>
        <button v-if="is_csqx.cs00400100234=='1'" class="zui-btn btn-primary-b" @click="pkf()">贫困户认证</button>
        <button class="zui-btn btn-default" @click="deleteDate()">退号操作</button>
        <!--
        <button class="zui-btn btn-default">补打</button>
        -->
        <p>当前医生挂号数：<span v-text="doctorDayNum"></span>&emsp;当前票号：{{pjData.dqsyh}}&emsp;剩余张数：{{pjData.fpzs}}&emsp;本次挂号费用：<span
                class="font-16 color-wtg">{{totalMoney}}</span>元</p>
    </div>
    <button class="zui-btn btn-primary" @click="bdfp()">补打发票</button>
</div>

<!--<div class="bottom_btu printHide">-->
<!--<div class="conBtu">-->
<!--<div class="btu" @click="doSave()" v-text="text_sub"></div>-->
<!--<div class="btu btu_cancel" @click="deleteDate()">退号操作</div>-->
<!--<div class="money">-->
<!--<p>本次挂号费用：</p>-->
<!--<span v-text="totalMoney">15.00</span>-->
<!--<p>元</p>-->
<!--</div>-->
<!--</div>-->
<!--</div>-->

</body>
<script type="application/javascript" src="brgh.js"></script>

</html>
