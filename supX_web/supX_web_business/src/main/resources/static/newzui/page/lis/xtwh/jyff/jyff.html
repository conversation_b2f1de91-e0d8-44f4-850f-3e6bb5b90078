<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检验方法</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
</head>
<style>
    .tong-search{
        padding: 13px 0 5px 20px;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary " @click="add"><i class="icon-xz1 paddr-r5"></i>新增方法</button>
            <button class="tong-btn btn-parmary-b " @click="refresh"><i class="icon-sx paddr-r5"></i>刷新</button>
            <button class="tong-btn btn-parmary-b " @click="save"><i class="icon-baocun paddr-r5"></i>保存</button>
            <button class="tong-btn btn-parmary-b " @click="del"><i class="icon-sc-header paddr-r5"></i>删除</button>
            <button class="tong-btn btn-parmary-b " ><i class="icon-yl paddr-r5"></i>预览</button>
            <button class="tong-btn btn-parmary-b " @click="piliang"><i class="icon-dysq paddr-r5"></i>打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">搜&nbsp;索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input type="text" name="phone" class="zui-input" v-model="searchAll" placeholder="请输入关键字">
                    </div>
                </div>
                <button class="zui-btn btn-primary xmzb-db margin-f-l30" @click="queryAll">查询</button>
            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"  :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                        </div>
                    </th>
                    <th><div class="zui-table-cell cell-s"><span>方法编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>方法名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>方法代码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table" >
                <tbody>
                <tr :tabindex="$index" data-index="0"  v-for="(item, $index) in jsonList"  @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                                            :type="'some'" :which="$index"
                                                                                                            :val="isChecked[$index]">
                    </input-checkbox></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ffbm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ffmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pydm"></div></td>
                    <td>
                        <div class="switch cell-s" >
                            <input disabled :id="'checked'+$index" type="checkbox" v-model="item.tybz" true-value="0" false-value="1"  />
                            <label :for="'checked'+$index"></label>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>


    </div>

    <div id="pop" class="jyxm">
        <transition name="left-fade">
            <!--:class="{'show':isShow}"-->
            <div class="zui-form side-form ng-hide pop-width bcsz-layer " id="brzcList" style="height: 100%;padding-bottom: 20px;padding-top: 0">
                <div class="layui-layer-title " v-text="title"></div>
                <span class="layui-layer-setwin" style="top: 0;">
                        <i class="color-btn" @click="close">&times;</i></span>
                <div class="layui-layer-content">
                    <div class=" layui-mad ">
                        <div class="zui-bottom"><p class="zui-top">方法编码</p> <input class="zui-input "
                                                                                   placeholder="请输入编号"
                                                                                   autocomplete="auto" disabled></div>
                        <div class="zui-bottom"><p class="zui-top">方法名称</p> <input class="zui-input "
                                                                                   placeholder="请输入方法"
                                                                                   autocomplete="auto" v-model="ff.ffmc"
                                                                                   data-notempty="true"
                                                                                   @blur="setPYDM(ff.ffmc, 'ff', 'pydm')"></div>
                        <div class="zui-bottom"><p class="zui-top">方法代码</p> <input class="zui-input "
                                                                                   placeholder="请输入方法代码"
                                                                                   autocomplete="auto" v-model="ff.pydm"
                                                                                   readonly="readonly"></div>
                        <div class="zui-bottom">
                            <p class="zui-top">状态</p>
                            <div class="switch" >
                                <input type="checkbox" v-model="ff.tybz" true-value="0" false-value="1" />
                                <label></label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ksys-btn">
                    <button class="zui-btn table_db_esc btn-default xmzb-db" @click="close">取消</button>
                    <button class="zui-btn btn-primary xmzb-db" @click="save">保存</button>
                </div>
            </div>
        </transition>
    </div>

</div>

<style>
    .side-form-bg{
        background: none;
        position: inherit;
    }
</style>
<script src="jyff.js"></script>

</body>
</html>
