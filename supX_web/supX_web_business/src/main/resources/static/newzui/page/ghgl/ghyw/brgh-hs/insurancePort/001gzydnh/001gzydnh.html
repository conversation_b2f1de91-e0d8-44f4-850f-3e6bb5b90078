<div id="gz_001">
    <div class="flex-container flex-align-c ">
        <div class="flex-container flex-align-c">
            <span class="ft-14 whiteSpace padd-r-5">病人类型</span>
            <select-input class="wh150" placeholder="请输入备注说明"  @change-data="resultChange"
                          :child="mznh_tran" :index="popContent.rylx" :val="popContent.rylx"
                          :search="true" :name="'popContent.rylx'" >
            </select-input>
        </div>
        <div class="  flex-container padd-l-10 flex-align-c ">
                        <span class="ft-14 whiteSpace padd-r-5">电话号码</span>
                        <input @input="change_dhhm()" class="zui-input" v-model="gh_dhhm"
                               id="sjhm">
                    </div>
        <button class="tong-btn btn-parmary-b color-wtg margin-l-10" @click="getPerson()">获取家庭成员</button>
    </div>
    <div class="zui-table-view " id="yjjlTable00">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th >
                        <div class="zui-table-cell cell-s"><span>序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>个人编号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>人员类型</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>姓名</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>性别</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>出生日期</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l"><span>身份证号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l"><span>医疗证号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>账户余额</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>参保状态</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>住院总费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>住院保内费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>住院补偿费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>单病种总费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>单病种保内费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>单病种补偿费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>门诊总费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>门诊保内费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>门诊补偿费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>慢性病总费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>慢性病保内费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>慢性病补偿费用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>人员属性</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" data-no-change  style="height: 212px" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in InfoList"
                    @dblclick="edit($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                    <!--@click="单击回调" @dblclick="edit($index)"双击回调-->
                    <td>
                        <div class="zui-table-cell cell-s" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.memberId"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="nhrysx_tran[item.memberPro]"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.memberName"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="brxb_tran[item.memberSex]"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.birthday"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.idcard"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.medicalNo"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.account"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.memberStatus"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.hosTotalCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.hosInsuranceCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.hosCompensateCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.sigTotalCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.sigInsuranceCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.sigCompensateCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.outpTotalCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.outpInsuranceCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.outpCompensateCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.chroTotalCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.chroInsuranceCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.chroCompensateCost"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.ideName"></div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<script type="application/javascript" src="insurancePort/001gzydnh/001gzydnh.js"></script>
