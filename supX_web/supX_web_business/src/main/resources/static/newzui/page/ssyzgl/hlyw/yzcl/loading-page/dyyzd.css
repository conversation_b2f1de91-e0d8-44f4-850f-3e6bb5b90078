.right {
    width: calc(100% - 250px);
    /*height: 100%;*/
    float: left;
}

.yzclSearch {
    /* background-color: #79D6FF; */
    color: #000000;
    border-bottom: 4px solid #eeeeee;
    border-right: 4px solid #eeeeee;
    margin-top: 0;
}

.YPTable {
    padding-left: 4%;
}

.YPTable td {
    border: 1px solid #bbbbbb;
    height: 25px;
    font-size: 12px;
}

.YPTable th {
    font-size: 12px;
}

.YPTable tr:nth-child(n+2):hover {
    background-color: #EAF2FF;
}

.CFTabTable {
    float: left;
    padding: 0;
    width: calc(100% - 10px);
    height: calc(100% - 214px);
    border-right: 4px solid #EEEEEE;
    overflow: auto;
    margin-left: 6px;
}

.CFTabTable table {
    /*border-left: 1px solid #bbbbbb;*/
    /*border-right: 1px solid #bbbbbb;*/
    /*border-top: 1px solid #bbbbbb;*/
}

.CFTabTable tr {
    border-bottom: 1px solid #bbbbbb;
}

.CFTabTable td {
    border: 1px solid #CCCCCC;
    padding: 6px;
}

.toolMenu img {
    width: 20px;
    margin-bottom: 4px;
}

.yzcl_context {
    float: left;
    margin-top: 44px;
    width: 100%;
}

.YZInfo {
    width: 70%;
    margin-left: 8px;
    float: left;
}

.YZChoice {
    display: inline-block;
    background-color: #eee;
    width: 100%;
}

.YZChoice > div {
    float: left;
    position: relative;
    margin: 5px 10px 5px 4px;
}

.YZChoice > div > input {
    width: 90px;
}

.YZChoice > div > span {
    position: absolute;
    top: 3px;
    font-size: 14px;
}

.personInfo {
    display: inline-block;
}

.personInfo > div {
    float: left;
    margin-right: 10px;
}

.yzdItem {
    margin-top: 20px;
    text-align: center;
}

.table_tab1 tr td:first-child {
    text-align: center;
}

.table_tab1 {
    height: auto;
}

.table_tab1 th {
    background-color: #eeeeee;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
}

.AllYZ {
    height: calc(100% - 260px);
    overflow: scroll;
}

.AllYZSH {
    height: calc(100% - 270px);
    overflow: scroll;
}

.AllYZCX {
    height: calc(100% - 160px);
    overflow: scroll;
}

.YZInfo textarea {
    /* margin-top: 10px; */
    height: 70px;
    width: 100%;
    background-color: #eeeeee;
}

.patientBaseInfo {
    width: calc(30% - 14px);
    background-color: #eeeeee;
    float: left;
    margin-left: 6px;
}

.baseTitle {
    border-bottom: 1px solid #bbbbbb;
    padding: 6px 0 6px 12px;
}

.baseInfo div {
    width: 100%;
    margin: 2px;
    display: inline-block;
    position: relative;
}

.baseInfo span {
    display: inline-block;
    background-color: #FFFFFF;
    float: left;
    padding: 4px;
}

.baseInfo span:first-child {
    width: 50px;
    margin-right: 2px;
    padding-left: 20px;
}

.baseInfo span:nth-child(2) {
    width: calc(100% - 90px);
    min-height: 15px;
}

.baseInfo span:nth-child(3) {
    position: absolute;
    left: 0;
    color: #F7D063;
    top: 2px;
}

.ChildtablePage {
    border-top: 4px solid #EEEEEE;
    border-right: 4px solid #EEEEEE;
    background: linear-gradient(to bottom, #ffffff 0, #ffffff 100%);
    width: 230px;
    padding: 4px 0;
}

.toolMenu {
    margin: 4px 4px 0 8px;
    width: 99%;
    display: block;
}

.InfoMenu {
    padding: 10px 0 0 8px;
}

#demoTable {
    border-top: 2px solid #1AB394 !important;
}

#demoTable td {
    overflow: inherit;
}

.selectGroup {
    position: absolute;
    top: 34px;
    left: 6px;
    max-width: 630px;;
    overflow-x: scroll;
}

.searchTableDiv {
    overflow-x: visible;
}

.bookMark {
    border-radius: 0;
}

.bookMarkDiv_selected div:first-child {
    background: #1AB394;
}

select, input[type = text] {
    /*width: 150px;*/
    width: 80px;
    height: 26px;
    border: 1px solid #aaa;
    border-radius: 0;
    outline: none;
}

#my_div {
    overflow: scroll;
    width: calc(100% - 18px);
    height: calc(100% - 68px);
}

#searchBr {
    width: 140px;
    height: 23px;
}

.notEmptySign {
    margin-top: 5px;
    right: 3px;
}

.yzcz {
    display: inline-block;
    border: 0;
    width: auto;
    padding: 0;
}

.yzcz > button {
    background-color: #029377;
}

.yzcz:hover > ul {
    display: block;
}

.yzcz ul {
    display: none;
    position: absolute;
    width: 90px;
    margin: -1px 0 0 4px;
    background-color: #fff;
    border: 1px solid #029377;
    z-index: 100;
}

.yzcz ul > li {
    padding: 6px 4px;
}

.yzcz ul > li:hover {
    background-color: #1AB394;
    color: #FFFFFF;
    cursor: pointer;
}

.CFUse {
    position: relative;
    background-color: #FFFFFF;
    box-shadow: #333 2px 2px 8px;
    width: 800px;
    height: 450px;
    margin: 0 auto;
}

.CFUse img {
    position: absolute;
    top: 8px;
    right: 8px;
}

.CFUseTitle {
    font-size: 16px;
    background-color: #eeeeee;
    padding: 6px 0 6px 12px;
    text-align: left;
}

.searchCFUse {
    margin: 10px 0 0 0;
    display: inline-block;
    width: 100%;
}

.searchCFUse > div {
    margin-left: 16px;
}

.searchCFUse select {
    width: 100px;
}

.YzTabTable {
    float: left;
    overflow: scroll;
    padding: 0;
    margin: 10px 0 0 16px;
    width: 30%;
}

.yzd .toolMenu > div {
    float: left;
    padding: 5px 16px;
    margin-right: 20px;
    cursor: pointer;
}

.yzd .toolMenu > div:hover {
    border-bottom: 2px solid #1AB394;
}

.yzd_select {
    border-bottom: 2px solid #1AB394;
}

.yzdTitle {
    font-size: 22px;
    text-align: center;
}

.yzd-brInfo, .yzd-ysInfo {
    display: inline-block;
    width: 740px;
    margin: 10px 0;
}

.yzd-ysInfo {
    width: 84%;
}

.yzd-brInfo > div, .yzd-ysInfo > div {
    float: left;
    font-size: 14px;
    margin-right: 20px;
}

.yzd-ysInfo > div > span{
    display: block;
    float: left;
}

.yzd-ysInfo > div > span:last-child{
    text-align: left;
    width: 120px;
}

.tablePage span{
    display: inherit;
}

.yzd-table table {
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
}

.yzd-table td, .yzd-table th {
    border: 1px solid #999;
    font-weight: 500;
    height: 34px;
    width: 30px;
    text-align: center;
}

.yzd-table td span {
    display: block;
    float: left;
    width: calc(50% - 2px);
    text-align: left;
}

.cqyzd, .lsyzd {
    text-align: center;
    overflow: scroll;
    height: calc(100% - 36px);
}

.ysDiv {
    width: 100%;
    text-align: center;
    /*position: absolute;*/
    bottom: 0;
}

.copyTem {
    float: right;
    margin-right: 27px;
    font-size: 14px;
    cursor: pointer;
    padding: 3px;
    border-radius: 3px;
    background-color: #EEEEEE;
}

.copyTem:hover {
    background-color: #1AB394;
    color: #FFFFFF;
}

.copy-br th:nth-child(2) {
    min-width: 97px;
}

.tableDiv select, .tableDiv input {
    width: 100%;
}

.tableDiv .selectInput {
    width: 98%;
    height: 26px;
}

.tableDiv td {
    text-align: center;
    position: relative;
}

.selectGroup td {
    text-align: left;
}

.zuNum ul {
    width: 60px;
    min-width: 60px;
    top: 28px;
}

.toolMenu_yzd div {
    float: left;
    text-align: center;
    padding: 6px 16px;
    cursor: pointer;
}

.same {
    border-right: 1px solid #000000;
}

.yzd-name {
    float: left;
    height: 42px;
}

.yzd-sm {
    float: right !important;
}

.tableDiv table {
    table-layout: fixed;
}

.tableDiv table td {
    max-width: 60px;
    text-overflow: ellipsis;
}

#tdkd {
    width: 72px;
}

.sameStart{
    position: absolute;
    border-top: 1px solid #000000;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 50%;
    right: 50%;
    bottom: 0;
}
.sameEnd{
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 1px solid #000000;
    width: 10px !important;
    height: 50%;
    right: 50%;
}
.same{
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 100%;
    right: 50%;
}

.yzd-way{
    width: auto !important;
    margin-left: 18px;
}

.yzd-table td, .yzd-table th{
    position: relative;
}

@media print {
    .goPrintHide{
        visibility: hidden;
    }
}
[v-cloak]{
    display: none;
}
.wrapper{
    padding-top: 40px;
}
.toolMenu_yzd{
    position: fixed;
    background: #fff;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1000;
}