<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>库存查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel ">
        <div class="tong-top">
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="flex-container padd-b-10 padd-l-10 padd-t-10">
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="ft-14 padd-r-5">库房</span>
                        <select-input class="wh122" @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                </div>
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="ft-14 padd-r-5">查询方式</span>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="options" :index="'fsmc'" :index_val="'cxfs'" :val="param.cxfs"
                                      :name="'param.cxfs'" :search="true" :index_mc="'fsmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="ft-14 padd-r-5">时间段</span>
                        <input class="zui-input  wh120 " autocomplete="off" v-model="param.beginrq" @click="showTime('timeVal','beginrq')" placeholder="请选择申请日期" id="timeVal" />
                        <span class="padd-r-5 padd-l-5">至</span>
                        <input class="zui-input  wh120 " v-model="param.endrq" autocomplete="off" @click="showTime('timeVal1','endrq')" placeholder="请选择申请日期" id="timeVal1" />
                </div>
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="ft-14 padd-r-5">检索</span>
                        <input class="zui-input  wh120 " placeholder="请输入关键字" v-model="param.parm" @keydown.enter="goToPage(1)"/>
                </div>
            <div class="flex-container flex-align-c">
                <span class="color-wtg font-18">库存总额： {{totalAmount}} &ensp;元</span>
            </div>

        </div>
    </div>
    <div class="zui-table-view " >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料编码</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料种类</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                    <th  v-if="qShow"><div class="zui-table-cell cell-s"><span>材料进价</span></div></th>
                    <th  v-if="qShow"><div class="zui-table-cell cell-s"><span>材料零价</span></div></th>
                    <th  v-if="qShow"><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th  v-if="qShow"><div class="zui-table-cell cell-s"><span>材料批号</span></div></th>
                    <th  v-if="qShow"><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th  v-if="qShow"><div class="zui-table-cell cell-s"><span>材料产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>二级库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>
                    <td><div class="zui-table-cell cell-s"  v-text="item.ypbm">001</div></td>
                    <td>
                        <div class="zui-table-cell cell-xl text-left text-over-2" :data-title="item.ypmc"  v-text="item.ypmc"></div>
                    </td>
                    <td><div class="zui-table-cell cell-l" v-text="item.ypgg">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kcsl">材料收入</div></td>
                    <td v-if="qShow"><div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">材料收入</div></td>
                    <td v-if="qShow"><div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">材料收入</div></td>
                    <td v-if="qShow"><div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.kcsl,2)">材料收入</div></td>
                    <td v-if="qShow"><div class="zui-table-cell cell-s" v-text="item.scph">材料收入</div></td>
                    <td v-if="qShow"><div class="zui-table-cell cell-s" >{{getYearFun(item.yxqz) ? fDate(item.yxqz,'date') : ''}}</div></td>
                    <td v-if="qShow"><div class="zui-table-cell cell-s" v-text="item.cdmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbl">材料收入</div></td>
                    <p v-if="jsonList.length==0" class="  noData margin-top-100 text-center">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-width"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <span class="span0">
            <i>有效期</i>
            <input type="text" class="zui-input border-r4 ytimes"  @blur="dateForVal($event, 'popContent.xxq')" v-model="popContent.xxq" @keydown="nextFocus($event)" id="_xxq"/>
        </span>
        <span  class="span0">
            <i>批次停用</i>
            <select-input @change-data="resultChange" :not_empty="true" :child="stopSign"
                          :index="popContent.xpcty" :val="popContent.xpcty"
                          :name="'popContent.xpcty'">
            </select-input>
        </span>

    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<script src="kccx.js"></script>

</body>

</html>
