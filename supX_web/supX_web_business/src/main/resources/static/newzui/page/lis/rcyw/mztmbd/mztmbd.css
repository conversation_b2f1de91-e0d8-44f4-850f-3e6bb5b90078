input[type=checkbox].green + label{
    display: inline-block;
    padding-left: 18px;
    height: 18px;
    line-height: 18px;
    position: relative;
    cursor: pointer;
    color: #a7b1c2;
    vertical-align: middle;
}

input[type=checkbox].green + label:before{
    content: " ";
    width: 16px;
    height: 16px;
    line-height: 16px;
    font-family: 'WebIcons';
    position: absolute;
    z-index: 2;
    zoom: 1;
    top: 50%;
    left: 0;
    margin-top: -8px;
    border: 1px #d2d2d2 solid;
    background-color: #fff;
    font-size: 12px;
    border-radius: 2px;
    text-align: center;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

input[type=checkbox].green:checked + label:before{
    content: "\f192";
    background-color: #1ABC9C;
    border-color: #1ABC9C;
    color: #fff;
}

input[type=checkbox].green{
    display: none;
}

.uitable_1 .cell-1-0{
    width: auto !important;
}
.zui-table-view .zui-table-body{
    /*height: 70vh;*/
    /*min-height: 70vh;*/
}
/*.zui-table-view table tr{*/
    /*border-bottom: 1px solid #eee;*/
/*}*/
.zui-table-view .zui-table-fixed.table-fixed-l{
    left: 10px;
}
.zui-table{
    border-bottom: 1px solid #eee;
    border-left: 1px solid #eee;
}
.zui-table-view table td:nth-child(1){
    border-left:none;
}
.zui-table-view table .table-hovers{
    background:rgba(26,188,156,0.08) !important;
/*background: #000;*/
    border: 1px solid #1abc9c !important;
    box-shadow:0 0 6px 0 rgba(26,188,156,0.45);
}
.zui-table-view table td{
    border-bottom: none;
}
