<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室维护</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper background-f" v-cloak id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary  paddr-r5" @click="saveData">保存</button>
        </div>
    </div>
    <div class="zui-table-view padd-t-10 hzList flex-container padd-r-10 padd-l-10">
        <div>
            <div class="zui-table-header">
                <table class="zui-table table-width50" >
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-s">科室编码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">科室名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s text-left">病案科室</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">科室人员</div>
                        </th>
                    </tr>
                    <!--@click="checkOne($index)"-->

                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <!--v-if="jsonList.length!=0"-->
                <table class="zui-table table-width50" >
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                        @mouseleave="switchIndex()"
                        @click="switchIndex('activeIndex',true,$index)" :tabindex="$index" v-for="(item, $index) in 10"   @dblclick="edit($index)">
                        <td ><div class="zui-table-cell cell-s" v-text="$index"></div></td>
                        <td ><div class="zui-table-cell cell-s text-left" v-text="$index"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="$index"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="$index"></div></td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
            </div>
        </div>
        <div class="padd-l-10">
            <div class="zui-table-header">
                <table class="zui-table" >
                    <thead>
                    <tr>
                        <th >
                            <div class="zui-table-cell cell-s">统计码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">科室编码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">科室名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">编制床位</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">加床位</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">产科标志</div>
                        </th>

                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <!--v-if="jsonList.length!=0"-->
                <table class="zui-table table-width50" >
                    <tbody>
                    <tr v-for="(item, $index) in 30" :tabindex="$index" :key="item.ksbm" :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                        @mouseenter="switchIndex('hoverIndex1',true,$index)"
                        @mouseleave="switchIndex()"
                        @click="switchIndex('activeIndex1',true,$index)" @dblclick="edit1($index)">
                        <td ><div class="zui-table-cell cell-s" v-text="$index"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="$index"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="$index"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="$index"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="$index"></div></td>
                        <td  class="cell-s">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
            </div>
        </div>

    </div>
</div>
<script type="text/javascript" src="ksdy.js"></script>
</body>
</html>