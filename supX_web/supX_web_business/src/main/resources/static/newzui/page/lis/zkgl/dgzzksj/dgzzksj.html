<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!--杨松柏-->
    <title>West多规则质控数据</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="zk.css" rel="stylesheet"/>
</head>
<body class="skin-default">
<div class="wrapper" id="jyxm_icon">
    <div class="" style="border:none; margin-top: 5px;">
        <div class="xmzb-content" id="brerList" style="padding-bottom: 40px;background: #fff">
            <div class="xmzb-top">
                <div class="col-x-12 ysb_list-50">
                    <button class="zui-btn btn-primary  padd-r5 icon-baocunb" @click="baocun">保存</button>
                    <button class="zui-btn btn-primary-b  padd-r5 icon-sx" @click="jysb()">刷新</button>
                    <button class="zui-btn btn-primary-b  padd-r5 icon-sc-header" @click="scall()">删除</button>
                </div>
                <div class="col-x-12">
                    <div class="zui-inline col-x-2 col-fm-2  xmzb-top-left">
                        <label class="zui-form-label" style="width: 122px;">检验设备</label>
                        <div class="zui-input-inline margin-l13">
                            <select-input @change-data="resultChange"
                                          :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="params.zxsb"
                                          :search="true" :name="'params.zxsb'">
                            </select-input>
                        </div>
                    </div>
                    <div class="col-x-2 col-fm-2  col-fm-2 xmzb-top-left">
                        <i>检索码</i>
                        <i><input type="text" value="" @input="getsbbm()" placeholder="请输入关键字" v-model="params.bah" class="zui-input"/></i>
                    </div>
                    <div class="zui-inline col-x-2 col-fm-2 xmzb-top-left">
                        <label class="zui-form-label" style="width: 122px">质控物浓度</label>
                        <select-input id="zklx_xz"  @change-data="resultChange" :not_empty="false" :child="jydjzklx_tran"
                                      :index="'pdxz.zklx'"  :val="params.zklx"
                                      :name="'params.zklx'" :search="true">
                        </select-input>
                    </div>
                </div>
            </div>
            <div class="xmzb-content-left zui-table-view">
                <div class="content-left-top">
                    <div class="i" style="width: 50px"><i class="">序号</i></div>
                    <div class="i"><i class="">指标编码</i></div>
                    <div class="i" style="text-align: left"><i class="">指标名称</i></div>
                    <div class="i"><i class="">简称</i></div>
                </div>
                <div style="height: 100%">
                    <ul class="content-left-list zui-scroll-left">
                        <li :class="[{'table-hovers':index===activeIndex,'table-hover':index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,index)"
                            :tabindex="index" v-for="(list,index) in getsblist.wx" @dblclick="getchild(list)">
                            <div class="i" style="width: 50px"><i class="title overflow1" v-text="index+1"></i></div>
                            <div class="i"><i class="title overflow1" v-text="list.zbxm"></i></div>
                            <div class="i position" style="text-align: left"><i class="title overflow1" :data-title="list.zbmc" v-text="list.zbmc"></i></div>
                            <div class="i"><i class="title overflow1" v-text="list.ywmc"></i></div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="xmzb-content-right zui-table-view">
                <div class="xmzb-top">
                </div>
                <div class="content-right-top">
                    <div class="i" style="width: 50px"><i>   <input-checkbox @result="reCheckBox" :list="'getsblistyx'" :type="'all'" :val="isCheckAll"></input-checkbox></i></div>
                    <div class="i"><i class="title ">项目名称</i></div>
                    <div class="i"><i class="title ">浓度</i></div>
                    <div class="i"><i class="title ">X</i></div>
                    <div class="i"><i class="title ">SD</i></div>
                    <div class="i"><i class="title ">CV</i></div>
                    <div class="i"><i class="title ">批号</i></div>
                    <div class="i"><i class="title ">产地</i></div>
                    <div class="i "><i class="">检验方法</i></div>
                    <div class="i "><i class="">参考值-低</i></div>
                    <div class="i "><i class="">参考值-高</i></div>
                    <div class="i position"><i class="">操作<em class="icon-bgzdgl bgfdgl-right"></em></i></div>
                </div>
                <!--style="display: none"-->
                <div style="height: 100%">
                    <ul class="content-right-list">
                        <li :class="[{'table-hovers':index===activeIndex,'table-hover':index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,index)"
                            :tabindex="index" v-for="(list,index) in getsblist.yx" @dblclick="deletexm(list)">
                            <!--<div class="i" style="width: 50px"><i><input type="checkbox" id="item" class="green"/><label for="item"></label></i></div>-->
                            <div class="i" style="width: 50px"><i><input-checkbox @result="reCheckBox" :list="'getsblist.yx'" :type="'some'" :which="index" :val="isChecked[index]"></input-checkbox></i></div>
                            <div class="i"><i class="title overflow1" v-text="list.zbmc"></i></div>
                            <div class="i"><i class="title overflow1">浓度</i></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.x" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.sd" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.cv" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.zkwph" class="zui-input y-input-width" @keydown="nextFocus($event)" /></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.cd" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.jyff"  class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.ckzD" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.ckzG" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><i><span @click="deletexm(list)" class="icon-sc"></span></i></div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <!--<div id="pop">-->
            <!--&lt;!&ndash;<transition name="pop-fade">&ndash;&gt;-->
            <!--<div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>-->
            <!--<div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}"-->
                 <!--style="height: max-content;padding-bottom: 20px">-->
                <!--<div class="layui-layer-title " v-text="title"></div>-->
                <!--<span class="layui-layer-setwin" style="top: 0;"><i class="color-btn"-->
                                                                    <!--@click="isShowpopL=false,isShow=false">&times;</i></span>-->
                <!--<div class="layui-layer-content">-->
                    <!--<div class=" layui-mad layui-height">确定删除质控记录：<span class="success">{{name}}</span> 吗？</div>-->
                <!--</div>-->
                <!--<div class="zui-row buttonbox">-->
                    <!--<button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>-->
                    <!--<button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>-->
                <!--</div>-->
            <!--</div>-->
            <!--&lt;!&ndash;</transition>&ndash;&gt;-->
        <!--</div>-->
    </div>
    <script src="zk.js"></script>
    <script type="text/javascript">
        $(".zui-scroll-right,.zui-scroll-left,.content-right-list").uiscroll({
            height: '100%',
            size: '6px',
            opacity: .3,
            disableFadeOut: true,
            position: 'right',
            color: '#000'
        });
    </script>
</div>
</body>
</html>