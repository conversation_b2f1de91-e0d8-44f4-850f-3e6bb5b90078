var ycglTj=new Vue({
    el:'.ycgl-tj',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat, scrollOps],
    data:{
        param:{
            beginrq:null,
            endrq:null
        },
        chartData: {
            ryrs: [],
            cyrs: []
        },
        date:[]
    },
    mounted:function () {
        var myDate=new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-30), 'date');
        this.param.endrq = this.fDate(new Date(), 'date');
        laydate.render({
            elem: '#timeVal',
            eventElem: '.times',
            type: 'date',
            value: this.param.beginrq,
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                this.param.beginrq = value;
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.times1',
            type: 'date',
            value: this.param.endrq,
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                this.param.endrq = value;
            }
        });
    },
    methods:{
        getDateNum: function (type) {
            this.date = [];
            if (type == 0) {
                for (var i = 1; i < 8; i++) {
                    this.date.push(i);
                }
            } else {
                var d = new Date();
                var curMonthDays = new Date(d.getFullYear(), (d.getMonth() + 2), 0).getDate();
                for (var i = 1; i < curMonthDays; i++) {
                    this.date.push(i);
                }
            }
        },

        PieChart:function () {
            // 基于准备好的dom，初始化echarts实例
            var myChart = echarts.init(document.getElementById('container'));

            // 指定图表的配置项和数据
            var option = {
                title: {
                    text: ''
                },
                tooltip : {
                    trigger: 'item',
                    formatter: "{a} <br/>{b} : {c} ({d}%)",

                },

                series: [{
                    name: ' ',
                    type: 'pie',
                    radius: [0, '80%'],
                    label: {
                        normal: {
                            position: 'outside',
                            formatter: function (item) {
                                if (item['dataIndex'] == "0") {
                                    return '' + item['name'] + '例\n发生牙疮'
                                } else if (item['dataIndex'] == 1) {
                                    return '' + item['name'] + '例\n高度风险'
                                } else if(item['dataIndex'] == 2) {
                                    return '' + item['name'] +'例\n中度风险'
                                }else {
                                    return '' + item['name'] + '例\n低度风险'
                                }
                            },
                            textStyle : {
                                fontWeight : 'normal',
                                fontSize : 14
                            }
                        },
                    },
                    data:[
                        {value:28, name:'28'},
                        {value:16, name:'16'},
                        {value:12, name:'12'},
                        {value:20, name:'20'},
                    ],
                },

                ],
                color: ['#ff5c63', '#04a9f5', '#f38d4f','#c4cccb'],
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        },
        //走势图
        chart:function () {
            var date = this.date;
            new Highcharts.Chart({
                chart: {
                    renderTo:'main',
                    type: 'area',
                },
                title: {
                    text: '发生压疮走势统计',
                    style: {
                        color: '#393f45',
                        fontWeight: 'bold'
                    }
                },
                subtitle: {
                    text: ''
                },
                gridLineColor: '#eeee',
                legend: {
                    enabled: false
                },
                credits: {
                    enabled: false
                },

                    xAxis: {
                    gridLineColor: '#eee',
                    gridLineWidth: 1,
                    categories: date,
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: ''
                    }
                },

                tooltip: {
                    backgroundColor: {
                        linearGradient: [0, 0, 0, 5],
                        stops: [
                            [0, 'rgba(0,0,0,0.7)'],
                            [1, 'rgba(0,0,0,0.7)']
                        ]
                    },
                    headerFormat: '<table>',
                    pointFormat: '<tr><td style="padding:0;color:#fff">{series.name}:</td>' +
                    '<td style="padding:0;color: #fff"><b style="color: #fff;">{point.y:.1f}</b></td></tr>',
                    footerFormat: '</table>',
                    shared: false,
                    useHTML: false,
                },
                plotOptions: {
                    area: {
                        borderWidth: 1,
                        lineWidth:1,
                        pointPadding: 0.15,
                        fillOpacity: 0.1, // 指定所有面积图的透明度
                        marker: {
                            enabled: false,
                            symbol: 'none',
                            radius: 0,
                            cursor: 'pointer',
                            states: {
                                hover: {
                                    enabled: true,
                                }
                            }
                        }
                    }
                },
                colors: ['#ff5c63'],
                series: [{

                    name: '压疮次数',
                    data: [11,20,230,320,11,90,80,60,40,11,11,3,5,6,9]
                },
                    {
                        color: 'ff5c63',

                    }

                ]
            });
            
        }


    }
})

var bottomBox = new Vue({
    el: '.ycgl-sb',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        defaltObj:{
            title:'提示信息',

            cb:'取消',
            sb:'确定'
        },
    },
    mounted: function () {
        changeWin()
    },
    methods: {
    //更多跳转压疮管理列表页
        morePage:function () {
            this.topNewPage('压疮管理','page/hsz/hlyw/ycgl/ycgl.html')
        },
        //未审核
        Unaudited:function () {
            common.openConfirm('您确定要审核'+'<span class="color-c1">'+"李浩然"+'</span>'+'患者的吗',function () {
                malert('审核','top','success');
            },function () {
            },this.defaltObj)
        },
        //已审核
        examine:function () {
            common.openConfirm('您确定要接收'+'<span class="color-c1">'+"李浩然"+'</span>'+'患者的审核信息吗',function () {
                malert('审核','top','success');
            },function () {
            },this.defaltObj)
        }
    }
});
$(window).resize(function () {
    ycglTj.PieChart();
    ycglTj.chart();
});
ycglTj.PieChart();
ycglTj.chart();


