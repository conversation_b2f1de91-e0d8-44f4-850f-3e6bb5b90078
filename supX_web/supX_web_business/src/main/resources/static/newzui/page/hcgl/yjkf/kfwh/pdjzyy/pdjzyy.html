<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>入库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="pdjzyy.css" rel="stylesheet" />
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
    <link rel="stylesheet" href="/FR/ReportServer?op=emb&resource=finereport.css">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="printArea printShow"></div>
<div class="background-box">
    <div class="wrapper printHide" id="wrapper" v-cloak>
        <div class="panel" >
            <div class="tong-top" v-if="isShow">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kdFun(0)" >盘点生成</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">打印</button>
            </div>
            <div class="tong-search" v-if="isShow">
                <div class="flex-container padd-b-10 flex-align-c" >
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">药库</span>
                        <select-input class="wh122" @change-data="resultRydjChange" :child="yfkfList"
                                      :index="'kfmc'" :index_val="'kfbm'" :val="params.kfbm" :name="'params.kfbm'"
                                      :search="true" :index_mc="'kfmc'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace ">审核标志</span>
                        <select-input @change-data="resultChange"
                                      :child="ckglzt_tran"
                                      class="wh122"
                                      :index="params.zt"
                                      :val="params.zt"
                                      :name="'params.zt'"  >
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                        <div class="  flex-container flex-align-c">
                            <input class="zui-input  wh120 " v-model="params.beginrq" @click="showTime('timeVal','params.beginrq')" placeholder="请选择开始日期" id="timeVal" />
                            <span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input todate wh120 " v-model="params.endrq" @click="showTime('timeVal1','params.endrq')" placeholder="请选择结束时间" id="timeVal1" />
                        </div>
                    </div>
                    <div class="flex-container flex-align-c">
                        <span class="ft-14 padd-r-5 whiteSpace">检索</span>
                        <input class="zui-input wh180 " placeholder="请输入关键字"  @keydown.enter="goToPage(1)" type="text" v-model="params.parm" />
                    </div>
                </div>
            </div>
            <div class="tong-search" v-if="!isShow">
                <div class="flex-container padd-b-10 flex-align-c" >
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">金额差合计</span>
                        <span>0.00</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10" :data-je="initJe">
                        <span class="ft-14 padd-r-5 whiteSpace">盘点金额合计</span>
                        <span class="color-wtg">{{fDec(money.pdjec,2)}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">盘点成本金额合计</span>
                        <span class="color-wtg">{{fDec(money.pdjecb,2)}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">填制人</span>
                        <span class="color-wtg">{{popContent.tzryxm}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">填制日期</span>
                        <span>{{popContent.tzrq}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">审核人</span>
                        <span class="color-wtg">{{popContent.shryxm}}</span>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">填制日期</span>
                        <span>{{popContent.shrq}}</span>
                    </div>
                </div>
                <div class="flex-container  wh100MAx padd-b-10">
                    <span class="ft-14 padd-r-5 whiteSpace">备注</span>
                    <textarea class="zui-input zui-textarea wh100MAx" v-model="popContent.bzsm"  type="text"  ></textarea>
                </div>
            </div>
        </div>
        <div class="zui-table-view">
            <!--入库列表-->
            <div class="zui-table-header" v-if="isShow">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>单号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>盘点时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>填制人</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>填制日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>审核人</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>审核日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body " v-if="isShow" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                        @mouseleave="switchIndex()"  :tabindex="$index"
                        @click="switchIndex('activeIndex',true,$index)"
                        :tabindex="$index">
                        <td class=" cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.pddh">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.tzryxm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="fDate(item.tzrq,'date')">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.shryxm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.shrq,'date')">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s flex-center padd-t-5">
                                <span class="width30 title icon-sh" v-if="item.shbz == 0" data-gettitle="审核" @click="shrFun(item,true)"></span>
                                <span class="width30 title icon-js" v-if="item.shbz == 0" data-gettitle="作废" @click="invalidData(item)"></span>
                                <span class="fa-eye font-18 width30" @click="shrFun(item,false)" ></span>
                            </div>
                        </td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--左侧固定-->
            <!--end-->
            <!--添加药品-->
            <div class="zui-table-header" v-if="!isShow">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>药品名称</span></div></th>
                        <th v-for="(item,index) in table">
                            <div class="zui-table-cell " :class="item.className">{{item.text}}</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body  " v-if="!isShow" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in jsonMxList" :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                        @mouseenter="switchIndex('activeIndex1',true,$index)" @mouseleave="switchIndex()" @click="switchIndex('activeIndex1',true,$index)"
                        :tabindex="$index">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">序号</div></td>
                        <td >
                            <div class="zui-table-cell cell-xl  ">
                                <input  :id="'ypmc'+$index" class="zui-input " :disabled="disabled" v-model="item.ypmc"
                                         @keyup="changeDown($index,$event,'searchCon')"
                                         @input="Wf_change(false,$index, $event.target.value,$event)"/>
                                <search-table :message="searchCon" :selected="selSearch" :page="page"
                                              :them="them" :them_tran="them_tran" @click-one="checkedOneOut"
                                              @click-two="selectOne1"></search-table>
                            </div>
                        <td v-for="(child,index) in table">
                            <div  class="zui-table-cell" :class="child.className" >
                                <span v-if="!child.inType" v-text="filterFun(item,child)"></span>
                                <input v-else v-model="item[child.field]"  class="zui-input"/>
                            </div>
                        </td>
                        </td>
                        <p v-if="jsonMxList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>

            <page @go-page="goPage" v-if="isShow" :totle-page="totlePage" :page="page" :param="params" :prev-more="prevMore" :next-more="nextMore"></page>
            <div class="zui-table-tool flex-container padd-l-10 padd-r-10 flex-jus-sb" v-if="!isShow">
                <span class="flex-container">
                        <button class="tong-btn btn-parmary-d9 xmzb-db"  @click="cancel">取消</button>
                        <button v-if="!shbzStatus && eye"  :disabled="isSubmited" class="tong-btn btn-parmary xmzb-db" @click="submitAll()">保存</button>
                        <button v-if="!shbzStatus && eye"  :disabled="isSubmited" class="tong-btn btn-parmary xmzb-db" @click="addFun()">新增一行</button>
                        <button v-if="shbzStatus && eye" :disabled="isSubmited" class="tong-btn btn-parmary xmzb-db" @click="showDetail()">审核</button>
                    </span>
            </div>
        </div>


        <model :s="'确定'" :c="'取消'" @default-click="getPdData" @result-clear="resultFun" :model-show="true"
               @result-close="resultFun" v-if="pkhShow" title="盘点条件设置">
            <div class="bqcydj_model">
                <tabs :num="which" :tab-child="modelText" @tab-active="tabBg"></tabs>
                <div class="padd-t-5 heightCalc">

                    <div v-if="which == '0'">
                        <div class="flex-container flex-align-c">
                            <div class="flex-container flex-align-c padd-r-10">
                                <span class="ft-14 padd-r-5 whiteSpace ">库房</span>
                                <input class="zui-input  wh160 " disabled v-model="popContent.kfmc" />
                            </div>
                        </div>
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title font14">剂型</div>
                            </div>
                            <div class="tab-card-body padd-t-10">
                                <div class="grid-box">
                                    <vue-checkbox class="padd-r-10 padd-b-10 cursor" @result="getReCheckAll" :new-text="'全选'" :val="'popContent.jxAll'"  :new-value="popContent.jxAll"></vue-checkbox>
                                    <div class="flex-container flex-align-c flex-wrap-w padd-r-5 padd-b-10">
                                        <vue-checkbox v-for="(item,index) in ypjx"  class="padd-r-10 padd-b-10 cursor" @click.native="getReCheckOneNew(item,index,'jxNews','jxbm')" :new-text="item.jxmc" :val="'popContent.jxNews'"  :new-value="popContent.jxNews[index]"></vue-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title font14">方式</div>
                            </div>
                            <div class="tab-card-body padd-t-10">
                                <div class="grid-box flex-container flex-align-c flex-wrap-w">
                                    <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.fs" type="radio" name="fs" value="0">&ensp;每日</label>
                                    <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.fs" type="radio" name="fs" value="1">&ensp;每周</label>
                                    <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.fs" type="radio" name="fs" value="2">&ensp;每月</label>
                                    <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.fs" type="radio" name="fs" value="3">&ensp;每季度</label>
                                    <label class="padd-r-5 flex-container cursor ft-14 flex-align-c"><input v-model="popContent.fs" type="radio" name="fs" value="4">&ensp;忽略盘点方式</label>
                                </div>
                            </div>
                        </div>
                       <div class="flex-container flex-align-c">
                           <div class="flex-container flex-align-c padd-r-10">
                               <span class="ft-14 padd-r-5 whiteSpace ">时间</span>
                               <input class="zui-input  wh160 " v-model="popContent.tzrq" @click="showTime('timeVal12','popContent.tzrq')" placeholder="请选择申请开始日期" id="timeVal12" />
                           </div>
                           <div class="flex-container flex-align-c padd-r-10">
                               <vue-checkbox class="padd-r-10  cursor" @result="getReCheckOne" :new-text="'盘无库存记录药品'" :val="'popContent.iskc'"  :new-value="popContent.iskc"></vue-checkbox>
                           </div>
                       </div>
                    </div>
                    <div v-if="which=='1'">
                        <div class="tab-card">
                            <div class="tab-card-header">
                                <div class="tab-card-header-title font14">药品用途</div>
                            </div>
                            <div class="tab-card-body padd-t-10">
                                <div class="grid-box">
                                    <vue-checkbox class="padd-r-10 padd-b-10 cursor" @result="getReCheckYpAll" :new-text="'全选'" :val="'popContent.ypAll'"  :new-value="popContent.ypAll"></vue-checkbox>
                                    <div class="flex-container flex-align-c flex-wrap-w padd-r-5 padd-b-10">
                                        <vue-checkbox v-for="(item,index) in ypzl"  class="padd-r-10 padd-b-10 cursor" @click.native="getReCheckOneNew(item,index,'ypzlNews','ypzlbm')" :new-text="item.ypzlmc" :val="'popContent.ypzlNews'"  :new-value="popContent.ypzlNews[index]"></vue-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </model>
    </div>
</div>
<script src="pdjzyy.js"></script>
</body>

</html>
