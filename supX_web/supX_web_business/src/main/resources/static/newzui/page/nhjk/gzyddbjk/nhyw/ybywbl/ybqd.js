/**
 * Created by mash on 2017/9/30.
 */

    var ybqd = new Vue({
        el: '#ybqd',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],
            totalContent:{},
            ifClick: true,
            fyisCheckAll:false,
            
            yb_fymx:[],
            error:'',
            ybbs : 0,
        },
        methods: {
        	getData:function(){
        		if(menu.qhyb_conn != 'onopen'){
        			malert("医保连接失败,请刷新页面重试！");
        			return;
        		}
        		ybqd.zyh=left_tab1.rydjData.ZYH; 
        		this.isfyChecked = [];
        		this.param={
        				zyh : ybqd.zyh,
        				serial_no : left_tab1.rydjData.SERIAL_NO
        		};
        		if(ybqd.zyh == null){
        			malert("请选择患者！");
        			return;
        		}
        		if(left_tab1.rydjData.SERIAL_NO == null){
        			malert("该患者还未入院登记！");
        			return;
        		}
        		ybqd.error = '' ;
        		//从his数据库获取数据
            		$.getJSON(
                            "/actionDispatcher.do?reqUrl=New1BxInterface&url="+left_tab1.bxurl+"&bxlbbm="+left_tab1.bxlbbm+"&types=inhospital&method=queryYscfyqd&parm="+JSON.stringify(this.param), function (json) {
                            	if (json.a == 0){
                            		var res=eval('('+json.d+')');
                            		ybqd.totlePage = Math.ceil(res.total/ybqd.param.rows);
                            		ybqd.yb_fymx = res.list;
                            		var bs=0;
                            		var account=0;
                            		for(var i=0;i<ybqd.yb_fymx.length;i++){
                            			bs++;
                            			account+=ybqd.yb_fymx[i].je;
                            		}
                            		//ybqd.totalContent.account=account;
                            		//ybqd.totalContent.bs=bs;
                            		
                            		setTimeout(function () {
                            			// 从医保中心拉取已上传费用数据
                                		var message= [
                                  	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
                                  	              	['start','','BIZC131253'],
                                  	              	// 固定参数
                                  	              	['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
                                  	              	['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
                                  	              	['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],
                                  	              	// 入参
                                  	              	['putcol','','hospital_id',left_tab1.qhybCs.tqcbh],
                                  	              	['putcol','','serial_no',left_tab1.rydjData.SERIAL_NO],
                                  	              	
                                  	              	['run',''],
                                  	              	
                                  	              	['setresultset','','feeinfo'],
                              	              	
                                  	              	// 获取结果集数据结构
                        	      	              	['getlist',
                        	      	              	 
                        	      	              	 	[
                        										['getbyname','','hospital_id',''],
                        										['getbyname','','serial_fee',''],
                        										['getbyname','','serial_apply',''],
                        										['getbyname','','serial_no',''],
                        										['getbyname','','recipe_no',''],	
                        										['getbyname','','doctor_no',''],
                        										['getbyname','','doctor_name',''],
                        										['getbyname','','fee_date',''],
                        										['getbyname','','stat_type',''],
                        										['getbyname','','medi_item_type',''],
                        										['getbyname','','defray_type',''],
                        										['getbyname','','his_item_code',''],
                        										['getbyname','','his_item_name',''],
                        										['getbyname','','item_code',''],
                        										['getbyname','','item_name',''],
                        										['getbyname','','model',''],
                        										['getbyname','','factory',''],
                        										['getbyname','','standard',''],
                        										['getbyname','','unit',''],
                        										['getbyname','','money',''],
                        										['getbyname','','reduce_money',''],
                        										['getbyname','','fee_date',''],
                        										['getbyname','','usage_flag',''],
                        										['getbyname','','usage_days',''],
                        										['getbyname','','opp_serial_fee',''],	
                        										['getbyname','','input_staff',''],
                        										['getbyname','','input_name',''],
                        										['getbyname','','input_date',''],
                        										['getbyname','','hos_serial',''],
                        										['getbyname','','trans_date',''],
                        										['getbyname','','trade_no','']
                        	      	              	 	 ]
                        	          	              	
                        	      	              	],
                                  	              	
                                  	              	['destoryinterface','']
                                  	              ];
                          						console.log(message);
                          						var ybbs = 0;
                          						if (socket.readyState===1) {
                          		                    socket.send(JSON.stringify(message));
                          		                    var cs=0;
                          		        			var interval=setInterval(function(){
                          		        					cs+=1;
                          		        					console.log(cs);
                          		            				if(rs){
                          		                				if(ifok){
                          		                					console.log(rslmsg);
                          		                					if(rslmsg.run > 0 && rslmsg.code=="success"){
                          		                						//ybqd.yb_fymx = rslmsg.getlist;
                          		                						//var bs=0;
                        	  		                              		//var account=0;
                          		                						var ybbs = 0;
                        	  		                              		for(var i=0;i<ybqd.yb_fymx.length;i++){
                        	  		                              			//bs++;
                        	  		                              			/*if(typeof(ybqd.yb_fymx[i].money) != 'undefined'){
                        	  		                              			account+=parseFloat(ybqd.yb_fymx[i].money);
                        	  		                              			}*/
                        	  		                              			ybbs ++ ;
                        	  		                              		}
                        	  		                              		//ybqd.totalContent.bs=bs;
                        	  		                              		//ybqd.totalContent.account=account;
                        	  		                              		ybqd.ybbs=ybbs;
                        	  		                              	ybqd.totalContent.bs=bs;
                        	  		                              ybqd.totalContent.account=account;
                        	  		                              		console.log(ybqd.ybbs)
                        	  		                              		
                        	  		                              	if(bs != ybbs){
                        	  	                            			malert("本地费用与实际上传费用条数不匹配，请取消后重新上传费用！");
                        	  	                            			ybqd.error = '本地费用与实际上传费用条数不匹配，请取消后重新上传费用！';
                        	  		                              	}else{
                        	  	                            			malert("已上传费用查询成功！");
                        	  	                            		}
                        	  		                              	
                          		                							
                          		                   	                }else{
                          		                   	                	malert("已上传费用查询失败！"+rslmsg.error);
                          		                   	                }
                          		                					
                          		                					clearInterval(interval);
                          		                					rs=false;
                          		                					ifok=false;
                          		                				}else{
                          		                					clearInterval(interval);
                          		                					rs=false;
                          		                					ifok=false;
                          		                				}
                          		                			}
                          		            				if(cs>=10){
                          		            					malert("医保超时,请重试！ ");
                          		            					rs=false;
                          		        	    				ifok=false;
                          		            					clearInterval(interval);
                          		            				}
                          		            			},left_tab1.socketTime);
                          		                }else{
                          		                	malert("医保通信失败！ ");
                          		                }	
                            			
                            			
            		                },500);
                            		
                            	}else{
                            		malert(json.c);
                            	}
                            });
        		
        		
        	},
        	qxsc: function () {
        		if(menu.qhyb_conn != 'onopen'){
        			malert("医保连接失败,请刷新页面重试！");
        			return;
        		}
        		if(ybqd.zyh == null){
        			malert("请选择患者！");
        			return;
        		}
        		if(left_tab1.rydjData.SERIAL_NO == null){
        			malert("该患者还未入院登记！");
        			return;
        		}
            	if(!ybqd.ifClick) return; //如果为false表示已经点击了不能再点
        		ybqd.ifClick = false;
        		$.ajaxSettings.async = false;
        		if(ybqd.yb_fymx.length==0||ybqd.yb_fymx==null){
        			malert("无上传费用！");
        			ybqd.ifClick = true;
        			return
        		}
        		console.log("------取消费用上传");
        		var qdxx = left_tab1.rydjData;;
        		var message= [
          	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
          	              	['start','','BIZC131274'],
          	              	// 固定参数
          	              	['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
          	              	['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
          	              	['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],
          	              	// 入参
          	              	['putcol','','hospital_id',left_tab1.qhybCs.tqcbh],
          	              	['putcol','','serial_no',left_tab1.rydjData.SERIAL_NO],
          	              	['putcol','','fin_staff',qdxx.REG_STAFF],
          	              	['putcol','','fin_man',qdxx.REG_MAN],
          	              	['run',''],
        	              	
          	              	['destoryinterface','']
          	              ];
  						console.log(message);
  						if (socket.readyState===1) {
  		                    socket.send(JSON.stringify(message));
  		                    var cs=0;
  		        			var interval=setInterval(function(){
  		        					cs+=1;
  		        					console.log(cs);
  		            				if(rs){
  		                				if(ifok){
  		                					if(rslmsg.run > 0 && rslmsg.code=="success"){
  		                							var parm={
  		                		                			zyh:left_tab1.rydjData.ZYH
  		                		                	}
  		                   	                	 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=inhospital&method=qxfysc&parm="
  		                   	                             + JSON.stringify(parm),
  		                   	                             function (json) {
  		                   	                             if (json.a == 0) {
  		                   	                            	 	malert(json.c);
  		                   	                            	 	ybqd.getData();
  		                   	                                 } else {
  		                   	                                     malert("His内部错误， "+json.c);
  		                   	                                 }
  		                   	                             });
  		                					}else{
  		                						malert("取消上传费用失败！ "+rslmsg.error);
  		                					}
  		                					
  		                					clearInterval(interval);
  		                					rs=false;
  		                					ifok=false;
  		                				}else{
  		                					clearInterval(interval);
  		                					rs=false;
  		                					ifok=false;
  		                				}
  		                			}
  		            				if(cs>=10){
  		            					malert("医保超时,请重试！ ");
  		            					rs=false;
  		        	    				ifok=false;
  		            					clearInterval(interval);
  		            				}
  		            			},left_tab1.socketTime);
  		                }else{
  		                	malert("医保通信失败！ ");
  		                }		
        		ybqd.ifClick = true;
         		//left_tab1.getBrData();
            },
            fyCheckOne: function (index) {
                this.fyisCheckAll = false;
                this.isfyChecked = [];
                this.isfyChecked[index] = true;
            },
         fyCheckAll: function (list) {
                if (this.fyisCheckAll) {
                    for (var i = 0; i < this[list].length; i++) this.isfyChecked[i] = true;
                } else {
                    this.isfyChecked = [];
                }
            },
          fyCheckSome: function (index) {
                if (!this.isfyChecked[index]) {
                    this.fyisCheckAll = false;
                    Vue.set(this.isfyChecked, index, false);
                } else {
                    Vue.set(this.isfyChecked, index, true);
                }
            },
        }
    });
    ybqd.getData();
