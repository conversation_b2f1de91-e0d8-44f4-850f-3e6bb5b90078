var content=new Vue({
    el:'.content',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        beginrq:'',
        endrq:'',
        ypContent:{},
    },
    created:function(){
    },
    mounted:function(){
        this.ajaxChart();
        this.AjaxChartPie();
        //初始化检索日期！为今天0点到今天24点
        var myDate=new Date();
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date');
        this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24, 'date');
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#dateStart',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.beginrq = value;
                } else {
                    content.beginrq = '';
                }
                //获取一次列表
            }
        });
        laydate.render({
            elem: '#dateEnd',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    content.endrq = value;
                } else {
                    content.endrq = '';
                }
                //获取一次列表
            }
        });
    },
    updated:function(){
        this.ajaxChart();
        this.AjaxChartPie();
        changeWin()
    },
    methods:{
        AjaxChartPie:function(){
            var arrObj=[];
            var textArr=['Ⅳ级','Ⅲ级','Ⅱ级','Ⅰ级'];
            var myChart = echarts.init(document.getElementById('pieCanvas'));
            for(var i=0;i<4;i++){
                var num=parseInt(Math.random()*20);

                arrObj.push({value:num, name:num+'次\n'+textArr[i],selected:true})
            }
            option = {
                color:['#ff5c62','#02a9f5','#f38d4f','#c4cccb'],
                title : {
                    text: '不良事件级别统计',
                    x:'center',
                    bottom:'2%'
                },
                tooltip : {
                    trigger: 'item',
                    formatter: "{a} <br/>{b} : {c} ({d}%)"
                },
                series : [
                    {
                        name: '不良事件级别统计',
                        type: 'pie',
                        selectedOffset:2,
                        radius : '68%',
                        center: ['50%', '43%'],
                        data:arrObj,
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            if (option && typeof option === 'object') {
                myChart.setOption(option,true);
            }
        },
        edit:function(){

        },
        ajaxChart:function (view) {

            var dataArrName=[];
            var dataArr=[];
            for(var i=0;i<40;i++){
                dataArrName.push('李\n浩');
                dataArr.push(parseInt(10*Math.random()))
            }
            var myChart = echarts.init(document.getElementById('canvas'));
            option = {
                title: {
                    text: '医生不良事件上报次数统计',
                    x:'center',
                    y:'top',
                },
                color: ['#02a9f5'],
                tooltip : {
                    trigger: 'axis',
                    axisPointer : {
                        type : 'shadow'
                    },
                    formatter:function (params,ticket,callback ) {
                        return '<span>'+params[0].value+'次</span>'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis : [
                    {
                        type : 'category',
                        data : dataArrName,

                        axisTick: {
                            alignWithLabel: true
                        },
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92',
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#fff',
                            },
                        },
                        splitLine:{
                            show:true,
                            lineStyle:{
                            },
                        },
                    },
                ],
                yAxis : [
                    {
                        splitNumber:10,
                        type : 'value',
                        axisLabel : {
                            textStyle: {
                                color: '#8b8f92'
                            },
                            borderColor:'#8b8f92',
                        },
                        axisLine:{
                            show:false,
                            lineStyle:{
                                color:'#fff'
                            },
                        },
                        axisTick:{

                        },
                    },
                ],
                series : [
                    {
                        name:'',
                        type:'bar',
                        barWidth: '11',
                        animationDuration: function (idx) {
                            return idx * 100;
                        },
                        data:dataArr,
                        label:{
                            color:'#b6babb'
                        },

                    },

                ]
            };
            if (option && typeof option === 'object') {
                myChart.setOption(option,true);
            }

        }

    },
});
var brRyList=new Vue({
    el:'#brRyList01',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{

    },
    methods:{
        openPage:function () {
            this.topNewPage('患者姓名','page/kzrgzt/ylyjjgtj/blsj/blsjsb/blsjsb.html')
        },
        NewopenPage:function () {
            this.topNewPage('不良事件报告','page/blsj//blsj/blsjbg/blsjbg.html')
        }
    },
    mounted:function () {

    },
});
var container=new Vue({
    el:'#container',
    data:{

    },
    mounted:function(){
        this.canvas()
    },
    methods:{
        canvas: function () {
            $('#container').highcharts({
                chart: {
                    type: 'bar'
                },
                title: {
                    text: ''
                },
                subtitle: {
                    text: ''
                },
                gridLineColor: '#eeee',
                legend: {
                    enabled: false
                },
                credits: {
                    enabled: false
                },
                xAxis: {
                    gridLineColor: '#eee',
                    gridLineWidth: 1,
                    crosshair: true,
                    categories: [
                        '心脏停博',
                        '急性心肌缺血',
                        '多源性、RonT型…',
                        '心脏停博',
                        '急性心肌缺血',
                        '多源性、RonT型…',
                        '心脏停博',
                        '急性心肌缺血',
                        '多源性、RonT型…',
                        '心脏停博',
                        '急性心肌缺血',
                        '多源性、RonT型…',
                        '多源性、RonT型…',
                        '多源性、RonT型…',
                        '多源性、RonT型…',
                        '多源性、RonT型…',
                    ],
                    labels: {
                        formatter: function () {
                            var colorArr=['#ffa8ac','#f5b66c','#50c4f8','#7e8d89'];
                            var color=this.pos<=2?colorArr[this.pos]:colorArr[3];
                            return '<div  style="color:'+color+'">' + this.value + '</div>';
                        }
                    },
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: ''
                    },
                    labels: {
                        // formatter:function (value) {
                        //     this.value
                        // }
                    }
                },
                tooltip: {
                    backgroundColor: {
                        linearGradient: [0, 0, 0, 10],
                        stops: [
                            [0, 'rgba(0, 0, 0, .6)'],
                            [1, 'rgba(0, 0, 0, .6)']
                        ]
                    },
                    borderColor:'transparent',
                    formatter:function (v) {
                        return '<span style="background:rgba(0, 0, 0, .6);border-radius:4px;width:42px;height:29px;color:#ffffff;font-size: 12px">'+this.y+'次</span>'
                    },
                    shared: false,
                    useHTML: false,
                },
                plotOptions: {
                    bar: {
                        borderWidth: 0,
                        pointPadding: 0.15,
                        animation: true,
                    },
                    series: {
                        cursor: 'pointer',
                        states: {
                            hover: {
                                enabled: false,
                            },
                        },
                    },

                },
                series: [{

                    // data: [19,18,17,15,14,9,12,5,12]
                    data: [{
                        name: '心脏停博',
                        y: 19,
                        color: "#ff0000"
                    }, {
                        name: '急性心肌缺血',
                        y: 18,
                        color: "#f3a74f"
                    }, {
                        name: '多源性、RonT型…',
                        y: 17,
                        color: "#04a9f5"
                    }, {
                        name: '心脏停博',
                        y: 15,
                        color: "#627571"
                    }, {
                        name: '急性心肌缺血',
                        y: 14,
                        color: "#627571"
                    }, {
                        name: '多源性、RonT型…',
                        y: 19,
                        color: "#627571"
                    }, {
                        name: '心脏停博',
                        y: 12,
                        color: "#627571"
                    }, {
                        name: '急性心肌缺血',
                        y: 5,
                        color: "#627571"
                    }, {
                        name: '多源性、RonT型…',
                        y: 3,
                        color: "#627571"
                    }, {
                        name: '多源性、RonT型…',
                        y: 3,
                        color: "#627571"
                    },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        }, {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },

                    ]
                }
                ]
            });
        },

    },
});
