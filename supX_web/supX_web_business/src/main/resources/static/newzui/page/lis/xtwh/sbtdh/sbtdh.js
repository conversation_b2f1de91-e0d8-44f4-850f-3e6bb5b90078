(function () {
    var wapse=new Vue({
        el:'.xmzb-content',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            centent:'',
            isFold: false,
            jysbList:[],
            zbbmList:[],
            data:{
            	
            },
            zbbmParam:{
            	'hostname':''
            },
            zbbmObj:{},
            
            jyxhTdhLength:0
    
        },
        methods:{
            // 点击增加对应标签
            AddList: function (data) {
            		isTabel.jysbObj=data;
                	isTabel.jysbTdhList=data.jysbTdhList;
                	isTabel.sumNum=isTabel.jysbTdhList.length;
                	var json={
                        	'hostname':this.zbbmParam.hostname,
                        	'sbbm':data.sbbm
                    };
                	
                	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJysb&types=queryOtherZbbm&param="+JSON.stringify(json), function (json) {
                		console.log(json);
                        if (json.a == 0) {
                        	wapse.zbbmList=json.d.list;
                        	console.log(wapse.zbbmList);
                        } else {
                            malert("查询失败" + json.c);
                            return;
                        }
                    });
                
            },
            
            queryAll:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJysb&types=queryJysbTdh&param=" + JSON.stringify(this.data), function (json) {
                    if (json.a == 0) {
                    	wapse.jysbList = json.d.list;
                        console.log(wapse.jysbList);
                    } else {
                        malert("查询失败" + json.c,'top','success');
                        return;
                    }
                });
            },
            zbdj:function(data,index){
            	wapse.zbbmList.splice(index,1);
                
                isTabel.jysbTdhList.push(data);
                isTabel.sumNum=isTabel.jysbTdhList.length;
            }
            // //AddDown右边双击
            // AddDown:function () {
            //     alert('双击添加到下面');
            //
            // }
            //

        },
        watch:{
        	'zbbmParam.hostname':function(){
        		var json={
                    	'hostname':this.zbbmParam.hostname,
                    	'sbbm':isTabel.jysbObj.sbbm
                };
            	
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJysb&types=queryOtherZbbm&param="+JSON.stringify(json), function (json) {
            		console.log(json);
                    if (json.a == 0) {
                    	wapse.zbbmList=json.d.list;
                    	console.log(wapse.zbbmList);
                    } else {
                        malert("查询失败" + json.c);
                        return;
                    }
                });
        	}
        }
    });
    var isTabel=new Vue({
        el:'.isTabel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isTabelShow:false,
            minishow:true,
            title:'',
            centent:'',
            isShowpopL:false,
            isShow:false,
            tdhMx:'',
            sumNum:0,
            jysbTdhList:[],
            jysbObj:{}
        },
        methods:{
            //dbDel双击删除
            dbDel:function (data,index) {
                /*pop.isShowpopL=true;
                pop.isShow=true;
                pop.title='项目指标';
                pop.centent='确定删除该项目指标吗？';*/
                
                isTabel.jysbTdhList.splice(index,1);
                wapse.zbbmList.push(data);
                isTabel.sumNum=isTabel.jysbTdhList.length;
                
                
                
            },
            bc:function(){
            	/*pop.isTabelShow=false;
            	pop.minishow=true;*/
            	//拼装提交的数据
            	
            	
            	if(isTabel.jysbObj.sbbm==undefined){
            		 malert('请选择设备！','top','defeadted');
            		 return;
            	}
            	
            	
            	
              var List=[];
         	   for (var int = 0; int < isTabel.jysbTdhList.length; int++) {
         		   //拼装为需要的数据格式
         		   var d={
         				  sbbm:isTabel.jysbObj.sbbm,
         				  tdh:isTabel.jysbTdhList[int].tdh ==undefined ? '' : isTabel.jysbTdhList[int].tdh,
         				  tzxs:isTabel.jysbTdhList[int].tzxs ==undefined ? '' : isTabel.jysbTdhList[int].tzxs,
         				  tzz:isTabel.jysbTdhList[int].tzz ==undefined ? '' : isTabel.jysbTdhList[int].tzz,
         				  xh:isTabel.jysbTdhList[int].xh ==undefined ? '' : isTabel.jysbTdhList[int].xh,
         				  zbbm:isTabel.jysbTdhList[int].zbbm
         		   };
         		   if(d.zbbm != '' && d.zbbm != null){
         			   List.push(d);
         		   }
         		   
         	   }
         	   
    		   if(List.length==0){
    			  var d={
        				  sbbm:isTabel.jysbObj.sbbm
        		   };
    			 List.push(d);
    		   }
         	  var json='{"list":' + JSON.stringify(List) + '}';
         	  this.$http.post('/actionDispatcher.do?reqUrl=XtwhJysb&types=saveJyzbTdh',json).then(
              		function(data) {
              			console.log(data);
              			if(data.body.a==0){
                              malert('保存结果成功','top','success');
              			}
                      }, 
                      function(error) {
                      	malert(error,'top','success');
                      });
            	
            	
            }
        },
    });

    wapse.queryAll();

})()
