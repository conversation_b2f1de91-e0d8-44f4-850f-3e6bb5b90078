<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>死亡讨论-患者管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link href="hyjl.css" rel="stylesheet">
    <script type="text/javascript">
        common.openloading()
    </script>
</head>
<body class="body skin-default">
<div class="header-item over-auto  background-f">
    <header class="header-title">科室会议记录</header>
    <main id="content" class="main-pb" v-cloak>
        <div class="flex-container">
                <ul class="swtl-item">
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-map swtl-icon"></span>
                            <span class="icon-text color-7f5cff">科室</span>
                        </div>
                        <div class="padd-b-15 padd-l-32 padd-t-5">
                            <div class="top-form">
                                <div class="top-zinle">
                                    <select-input class="wh182" @change-data="resultChange"
                                                  :child="bxzt_tran" :index="'zt'" :val="zt"
                                                  :name="'zt'"  :disable="true">
                                    </select-input>
                                    </div>
                                </div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-date swtl-icon"></span>
                            <span class="icon-text color-green">会议时间</span>
                        </div>
                        <div class="padd-b-15">
                            <div class="zui-date relative swtl-text-content">
                                <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                                <input class="zui-input  color-333 text-indent-20 wh262 " id="timeVal" style="padding-top: 0"  :value="new Date()">
                            </div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-ch swtl-icon"></span>
                            <span class="icon-text color-f4b26b">参加人员</span>
                        </div>
                        <div class="flex-container margin-b-15 padd-b-15 swtl-text-content">
                            <div @mouseleave="hoverName()"  class="Headimg" :class="!editText?'':'HeadImg'" v-for="(list,$index) in 1" :id="list">
                                <p class="HeadPortrait" @click="delImg($index)"></p>
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="color-757c83 font12">刘医生</p>
                            </div>
                            <span class="icon-upload" v-if="!editText"   @mouseenter="tipsShow=true"
                                  @mouseleave="tipsShow=false" @click="addHost">
                                    <i v-show="tipsShow"  class="tips">添加主持人</i>
                             </span>
                            <div  @mouseleave="hoverName()" class="Headimg" :class="!editText?'':''" v-for="(list,$index) in 6" :id="list">
                                <p class="HeadPortrait" @click="delImg($index)"></p>
                                <p @mouseenter="hoverName(true,$index,$event)"  class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="color-757c83 font12">刘医生</p>
                            </div>
                            <span class="iconfont  icon-upload" v-if="!editText"   @mouseenter="tipsShow1=true"
                                  @mouseleave="tipsShow1=false" @click="addParticipants">
                                    <i v-show="tipsShow1" class="tips">添加参与人</i>
                                </span>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="username text-center margin-t-10 margin-b-5 font-16 padd-t-10">刘医生</p>
                                <div class="flex-container flex-jus-c padd-t-5">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-zt swtl-icon"></span>
                            <span class="icon-text color-ff5c63">会议主题</span>
                        </div>
                        <div class="padd-b-15 width-p100">
                            <div contenteditable="true"   class=" swtl-text-content color-333 text-indent-0 border-text" @keydown="rowsJ($event)">1、今日会议主题：某某某。</div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-zj swtl-icon"></span>
                            <span class="icon-text color-72bc1a">会议内容</span>
                        </div>
                        <div class="padd-b-15 width-p100">
                            <div contenteditable="true" class="swtl-text-content text-indent-0 color-333 border-text" @keydown="rowsJ($event)">
                                <p>一、传达护士长会议精神</p>
                                <p>1、关于综合绩效考核检查点评：</p>
                                <p>1）、人力资源，床护比不够，新老搭配不合理；</p>
                                <p>2）、技能考核，15项操作严格培训；</p>
                                <p>3）、理论考的不理想、理论题库15000题；</p>
                                <p>4）、操作平时要严抓，按时考核，心肺复苏95分及格医学|教育网|搜集整理，每人必过，不按时考试的扣钱，考核才允许进室内训练中心，注意按要求着装；</p>
                                <p>2、人员在职在位，严格管控，按级请假；</p>
                                <p>3、护士长、安全员进行科室安全排查（人员管理、毒麻、消防等），与每位护士谈一次话；</p>
                                <p>4、等级医院评审抓紧准备，从今日开始，从一点一滴做起，从每个人做起，从平时做起，从常规做起，从每一个病种开始；</p>
                            </div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-ch swtl-icon"></span>
                            <span class="icon-text color-f4b26b">记录者</span>
                        </div>
                        <div  class="flex-container margin-b-15 padd-b-15 swtl-text-content">
                            <div @mouseleave="hoverName()" class="Headimg" :class="!editText?'':'HeadImg'" v-for="(list,$index) in 1" :id="list">
                                <p class="HeadPortrait" @click="delImg($index)"></p>
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="color-757c83 font12">刘医生</p>
                            </div>
                            <span class="icon-upload" v-if="!editText"   @mouseenter="tipsShow2=true"
                                  @mouseleave="tipsShow2=false" @click="addRecorder">
                                    <i v-show="tipsShow2"  class="tips">添加记录者</i>
                             </span>
                        </div>
                    </li>

                </ul>

        </div>
        <div class="zui-table-tool flex-between">
            <div class="hyjl-gtt color-ff5c63">未按标准填写，请重新填写！</div>
            <div><button class="root-btn btn-parmary-d9" @click="eidtCancel">取消</button>
            <button class="root-btn btn-parmary" @click="editShow">确定</button>
            </div>
        </div>
    </main>

</div>

<div class="side-form  pop-width" :class="{'ng-hide':numOne==1}"  id="brzcList" role="form" v-cloak>
    <div class="fyxm-side-top flex-between">
        <span v-text="title"></span>
        <span class="fr iconfont icon-iocn55 icon-cf056 icon-font20" @click="close"></span>
    </div>
    <div class="ksys-side">
        <div class="top-form">
            <label class="top-label">检索</label>
            <div class="top-zinle">
                <input type="text" class="zui-input wh182"/>
            </div>
        </div>

        <div class="zui-table-view" style="margin-top: 10px;">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>选择</span></div></th>
                        <th><div class="zui-table-cell cell-s "><span>医生姓名</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>科室</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>职称</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <vue-scroll :ops="pageScrollOps">
                <div class="zui-table-body body-height">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in 30"
                            :tabindex="$index"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','10'],$event)">
                            <td class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'10'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <!--字段超过3个字加data-title提示-->
                            <td><div class="zui-table-cell cell-s " data-title="医生姓名">医生姓名</div></td>
                            <td><div class="zui-table-cell cell-m" >科室</div></td>
                            <td><div class="zui-table-cell cell-m">职称</div></td>

                        </tr>
                        </tbody>
                    </table>
                </div>
            </vue-scroll>
        </div>
    </div>
    <div class="ksys-btn padd-r-10">
        <button class="root-btn btn-parmary-d9" @click="close">取消</button>
        <button class="root-btn btn-parmary" @click="sideOk">确定</button>
    </div>
</div>


<script type="text/javascript" src="hyjl.js"></script>
</body>
</html>