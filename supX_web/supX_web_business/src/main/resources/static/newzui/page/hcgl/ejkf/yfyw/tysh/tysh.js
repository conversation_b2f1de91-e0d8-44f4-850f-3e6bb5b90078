// 工具条
var toolBar = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        yfList: [],
        zhuangtai: {
            "1": '已发药',
        },
        shstatus: {
            "1": '未审核',
            "2": '已审核',
        },
        //打印机
        num:0,
        jsonList: [{tysl: '1'}],
        param: {
            page: 1,
            rows: 10,
            sort: 'zdy',
            order: 'asc',
            begin: null,
            end: null,
            fybz1: '1',
            fybz: '1',
             // zfbz: '1',
            tybz:'0',
            ystysqbz:'1',
        },
        totlePage: 0,
        fybzClass: {
            '0': 'color-dsh',
            '1': 'color-dlr',
            '2': 'color-yzf',
            '5': 'color-wtg',
        },
        tybzClass: {
            '0': '',
            '1': 'color-wtg',
            '2': 'color-wtg',
        },
        tyztArr: {
            "0": '未退药',
            "1": '全部退药',
            "2": '部分退药'
        },
        popContent:{},
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {
        this.getYfbm();
        var myDate = new Date();
        this.param.beginrq = this.fDate(new Date(), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(myDate.setDate(myDate.getDate() + 1), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.param.beginrq
            , theme: '#1ab394'
            , done: function (value) {
                toolBar.param.beginrq = value;
                toolBar.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.param.endrq
            , theme: '#1ab394'
            , done: function (value) {
                toolBar.param.endrq = value;
                toolBar.getData();
            }
        });
    },
    methods: {
        tysq:function (item){
            this.popContent=item;
            brzcList.popContent=item;
            brzcList.open();
            brzcList.getData(item.cfh)
        },
        getYfbm: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify({ ylbm: 'N040030022002'}), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    toolBar.yfList = json.d.list;
                        Vue.set(toolBar.param, 'yfbm', toolBar.yfList[0].yfbm);
                        //获取药房成功后查询处方数
                        toolBar.getData();
                } else {
                    malert("药房编码获取失败：" + json.c, 'top', 'defeadted');
                }
            });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            //先获取到操作的哪一个
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            if (val[3] != null) {
                Vue.set(this[val[2][0]], val[3], val[4]);
            }
            if (val[2][val[2].length - 1] == 'fybz' && val[0] == '0') {
                Vue.set(this[val[2][0]], val[2][val[2].length - 1], '0');
                Vue.set(this[val[2][0]], 'kfbz', '1');
            }
            this.goToPage(1);
        },
        //刷新处方
        getData: function () {
            common.openloading('.setScroll');
            if (this.param.fybz1 == '5'){
                this.param.fybz = '0';
                this.param.kfbz = '0';
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=queryypcf&dg=" + JSON.stringify(this.param) + "&parm=" + JSON.stringify(this.param), function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    toolBar.totlePage = Math.ceil(json.d.total / toolBar.param.rows);
                    toolBar.jsonList = json.d.list;
                } else {
                    malert("处方查询失败：" + json.c, 'top', 'defeadted')
                }
            });
            common.closeLoading()
        },
    }
});

//侧边弹框部分
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        title: '',
        ifClick: true, // 用于判断是否点击保存按钮
        popContent: {},
        jsonList: [],
        param: {
            page: 1,
            rows: 100,
            sort: 'mxxh',
            order: 'asc'
        },
    },
    updated: function () {
        changHeight()
    },
    mounted: function () {
    },
    methods: {
        //关闭
        closes: function () {
            $(".side-form").removeClass('side-form-bg');
            $(".side-form").addClass('ng-hide');
            toolBar.getData();
            $("#ylkh").focus();
            this.hoverIndex = undefined;
            this.activeIndex = undefined;
        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg');
            $(".side-form").removeClass('ng-hide');
        },
        getData: function (cfh) {
            if (cfh == undefined) {
                return;
            }
            //如果有分页请用jq的api,一般情况下避免冲突请用vue-resource,具体参照index.js的事例
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=queryyppfhb&dg=" + JSON.stringify(this.param) + '&parm={"cfh":"' + cfh + '"}', function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    brzcList.totlePage = Math.ceil(json.d.total / brzcList.param.rows);
                    brzcList.jsonList = json.d.list;
                    console.log(brzcList.jsonList)
                } else {
                    malert("查询失败：" + json.c, 'top', 'defeadted')
                }
            });
        },
        tysq:function (num,item){
            if (!this.ifClick) {// 已经点击过就不能再点
                malert("请勿重复点击！", "top", "defeadted");
                return;
            }
            this.ifClick = false;
            if(item.ystysqbz =='2'){
                this.ifClick = true;
                return     malert("已经审核不能审核", 'top', 'defeadted');
            }
            if(this.num == 2){
                this.ifClick = true;
                return false
            }
            ++this.num;
            this.popContent=item
            var obj = JSON.stringify({
                "cfh": item.cfh,
                "ystysqbz":num
            });
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCftyzf&types=cfsh', obj).then(function (data) {
                if (data.body.a == 0) {
                    brzcList.zfcf(item);
                    brzcList.ifClick = true;
                    brzcList.closes();
                    toolBar.getData();
                    malert("申请退药成功", 'top', 'success');
                } else {
                    brzcList.ifClick = true;
                    malert("申请退药失败", 'top', 'defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        zfcf:function (item){
            var obj = JSON.stringify({
                "cfh": item.cfh
            });
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCftyzf&types=cfzf', obj).then(function (data) {
                if (data.body.a == 0) {
                    malert('处方作废成功', 'top', 'success')
                } else {
                    toolBar.tysq('1',brzcList.popContent)
                    malert('处方作废失败：' + data.body.c, 'top', 'defeadted')
                }
            });
        },
    }
});
