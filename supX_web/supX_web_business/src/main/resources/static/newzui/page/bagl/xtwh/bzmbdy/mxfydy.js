(function(){
    //统筹类别
    var tableInfo = new Vue({
        el: '#mxfydy',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
        	param: {
	            page: 1,
	            rows: 10,
	            sort: '',
	            order: 'asc'
	        },
            jsonList: [], //列表集合
            fylbList: [], //费用类别下拉框
            fylb: null, //费用类别值
            balbList: [], //病案类别
            balb: null   //病案类别值
        },
        methods : {
        	//初始化页面加载列表
            getData: function () {
            	var json={
            		tybz:0
            	};
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhYlfwxmMxfyxm&types=query&dg="+JSON.stringify(this.param)+"&json"+JSON.stringify(json),function (json) {
                   if(json.d!=null){
                    	tableInfo.jsonList = json.d.list;
                    	tableInfo.totlePage = Math.ceil(json.d.total/tableInfo.param.rows);
                    	tableInfo.isCheckAll = false;
           				tableInfo.checkAll();//调用全选
                   }
        		});
            },
            
          	//下拉框费用类别加载
          	fylbSelect: function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=fybm&dg=",function (json) {
       			 	tableInfo.fylbList = json.d.list;
                });
          	},
          	//下拉框病案类别加载
          	balbSelect: function(){
          		var json={
          			zylb: "08"
          		}
            	$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyzbm&dg="+"&json="+JSON.stringify(json),function (json) {
       			 	tableInfo.balbList = json.d.list;
                });
          	},
          
            //保存
          	saveData: function(){
          		var mxfyList = [];
          		for (var i = 0; i < this.isChecked.length; i++) {
                	if (this.isChecked[i] == true) {
                		mxfyList.push(this.jsonList[i]);
                	}
                }
          		var json = '{"list":'+JSON.stringify(mxfyList)+'}';
                this.$http.post('/actionDispatcher.do?reqUrl=XtwhYlfwxmMxfyxm&types=updateBetch&',
                		json).then(function (data) {
                            if(data.body.a == 0){
                                malert("保存成功");
                                tableInfo.getData();
                            } else {
                                malert("保存失败");
                            }
                        }, function (error) {
                            console.log(error);
                });
          	},
          	//设置框
          	remove: function(){
          		this.balb=null;
          		this.fylb=null;
          	},
          	
          	//设置按钮
          	szDate: function(){
          		var json = {
          			lbbm : this.fylb,
          			balb : this.balb
          		};
                this.$http.post('/actionDispatcher.do?reqUrl=XtwhYlfwxmMxfyxm&types=updateByFylb&',
                		JSON.stringify(json)).then(function (data) {
                            if(data.body.a == 0){
                                malert("设置成功");
                                tableInfo.getData();
                            } else {
                                malert("设置失败");
                            }
                        }, function (error) {
                            console.log(error);
                });
          	},
        }
    });
    
    //列表
    tableInfo.getData();
    tableInfo.fylbSelect();
    tableInfo.balbSelect();


    //为table循环添加拖拉的div
    var drawWidthNumMxfy = $(".patientTableMxfy tr").eq(0).find("th").length;
    for(var i=0;i<drawWidthNumMxfy;i++){
        if(i>=2){
            $(".patientTableMxfy th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
        }
    }

})();