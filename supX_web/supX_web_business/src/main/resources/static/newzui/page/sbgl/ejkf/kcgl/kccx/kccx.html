<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>库存查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="kccx.css" rel="stylesheet">

</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="jyxm_icon"  v-cloak>
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary " @click="exportKc"><i class="icon-width icon-dc-b "></i>导出</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
            <div class="tong-search">
                <div class="flex-container flex-align-c padd-b-10">
                    <div class="flex-container padd-r-10 flex-align-c ">
                        <span class="ft-14 padd-r-5">二级库房</span>
                        <select-input @change-data="resultRydjChange" class=" wh122 margin-l-5" :child="yfkfList" :index="'yfmc'"
                                      :index_val="'yfbm'" :val="param.yfbm" :name="'param.yfbm'" :search="true"
                                      :index_mc="'yfmc'">
                        </select-input>
                    </div>
                    <div class="flex-container padd-r-10 flex-align-c">
                        <span class="ft-14 padd-r-5">查询方式</span>
                        <select-input class=" wh122 margin-l-5" @change-data="resultRydjChange" :child="options" :index="'fsmc'"
                                      :index_val="'cxfs'" :val="param.cxfs" :name="'param.cxfs'" :search="true"
                                      :index_mc="'fsmc'">
                        </select-input>
                    </div>
                    <div class="flex-container padd-r-10 flex-align-c">
                        <span class="ft-14 padd-r-5">边界</span>
                        <select-input class=" wh122 margin-l-5" @change-data="resultChangeXl" :child="cxbjlist" :index="'cxbj'"
                                      :index_val="'cxbj'" :val="param.cxbj" :name="'param.cxbj'" :search="true" :index_mc="'cxbj'">
                        </select-input>
                    </div>
                    <div class="flex-container padd-r-10 flex-align-c">
                        <span class="ft-14 padd-r-5">批次</span>
                        <select-input class=" wh122 "  @change-data="resultChangeXl" :not_empty="false"
                                       :child="pcty_tran" :index="param.pcty1" :val="param.pcty1"
                                       :name="'param.pcty1'">
                        </select-input>
                    </div>
                    <div class="flex-container padd-r-10 flex-align-c">
                        <span class="ft-14 padd-r-5">检索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="goToPage(1)" type="text" v-model="param.parm" />
                    </div>
                    <div class="flex-container flex-align-c">
                        <span class="color-wtg font-18">库存总额： {{totalAmount}} &ensp;元</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="fyqd hzList zui-table-view flex-container flex-dir-c ">
			<div class="zui-table-header">
			<table class="zui-table table-width50">
			    <thead>
			    <tr class="flex-container flex-align-c qdmx_item" >
					<th>
					    <div class="zui-table-cell cell-m"><span>序号</span></div>
					</th>
					<th>
					    <div class="zui-table-cell cell-xl"><span>材料编码</span></div>
					</th>
					<th>
					    <div class="zui-table-cell cell-s"><span>材料名称</span></div>
					</th>
					<th>
					    <div class="zui-table-cell cell-s"><span>单位</span></div>
					</th>
					<th>
					    <div class="zui-table-cell cell-s"><span>大库存</span></div>
					</th>
					<th>
					    <div class="zui-table-cell cell-s"><span>小库存</span></div>
					</th>
					<th>
					    <div class="zui-table-cell cell-s"><span>总库存</span></div>
					</th>
			        <th>
			            <div class="zui-table-cell cell-s"><span>材料规格</span></div>
			        </th>
			        <th v-if="ysbShow">
			            <div class="zui-table-cell cell-s"><span>剂型</span></div>
			        </th>
			        <th>
			            <div class="zui-table-cell cell-s"><span>材料种类</span></div>
			        </th>
			
			        <th v-if="ysbShow">
			            <div class="zui-table-cell cell-s"><span>材料产地</span></div>
			        </th>
			        <th>
			            <div class="zui-table-cell cell-s"><span>库存数量</span></div>
			        </th>
			        <th v-if="ysbShow">
			            <div class="zui-table-cell cell-s"><span>材料进价</span></div>
			        </th>
					<th v-if="ysbShow">
					    <div class="zui-table-cell cell-s"><span>材料零价</span></div>
					</th>
			        <th v-if="ysbShow">
			            <div class="zui-table-cell cell-s"><span>零价金额</span></div>
			        </th>
			        <th v-if="ysbShow">
			            <div class="zui-table-cell cell-s"><span>材料批号</span></div>
			        </th>
			        <th>
			            <div class="zui-table-cell cell-s"><span>停用状态</span></div>
			        </th>
			        <th v-if="ysbShow">
			            <div class="zui-table-cell cell-s"><span>有效期至</span></div>
			        </th>
			        <th v-if="ysbShow">
			            <div class="zui-table-cell cell-s"><span>入库日期</span></div>
			        </th>
			        <th v-if="ysbShow">
			            <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
			        </th>
			        <th class="cell-l">
			            <div class="zui-table-cell cell-s"><span>分装比例</span></div>
			        </th>
			    </tr>
			    </thead>
			</table>
			</div>
            <!-- <div>
                <ul   class="flex-container flex-align-c flex_9">
                    <li class="cell-m">序号</li>
                    <li class="list">材料编码</li>
                    <li class="list text-left cell-xxl" style="min-width: 300px">材料名称</li>
                    <li class="list">单位</li>
                    <li class="list">大库存</li>
                    <li class="list">小库存</li>
                    <li class="list">总库存</li>
                    <li class="list">材料规格</li>
                    <li class="list" v-if="ysbShow">剂型</li>
                    <li class="list">材料种类</li>
                    <li class="list" v-if="ysbShow">材料产地</li>
                    <li class="list">库存数量</li>
                    <li class="list" v-if="ysbShow">材料进价</li>
                    <li class="list" v-if="ysbShow">材料零价</li>
                    <li class="list" v-if="ysbShow">零价金额</li>
                    <li class="list" v-if="ysbShow">材料批号</li>
                    <li class="list">停用状态</li>
                    <li class="list" v-if="ysbShow">有效期至</li>
                    <li class="list" v-if="ysbShow">入库日期</li>
                    <li class="list" v-if="ysbShow">拼音代码</li>
                    <li class="list" >分装比例</li>
                </ul>
            </div>-->
			<!-- 
            <div class="zui-table-body" @scroll="scrollTable">
                <ul class="item  " v-for='(ypItem,$index) in jsonList'>
                    <li class="scroll"  >
                        <div v-for='(item,index) in ypItem.ypList' :class="[{'red':Number(item.kcsl)<Number(item.zdkc)}]" class="flex-container flex-align-c qdmx_item"  @dblclick="edit($index,index)">
                            <p class="list cell-m" :class="{'color-wtg':item.kcsl=='0'}">{{index+1}}</p>
                            <p class="list " >{{item.ypbm}}</p>
                            <p class="list text-left cell-xxl flex-container flex-align-c  position"><span class="title">{{item.ypmc}}</span></p>
                            <p class="list ">{{item.yfdwmc}}</p>
                            <p class="list ">{{item.dkc}}</p>
                            <p class="list">{{item.xkc}}</p>
                            <p class="list"></p>
                            <p class="list">{{item.ypgg}}</p>
                            <p class="list" v-if="ysbShow">{{item.jxmc}}</p>
                            <p class="list">{{item.ypzlmc}}</p>
                            <p class="list" v-if="ysbShow">{{item.cdmc}}</p>
                            <p class="list">{{item.kcsl}}</p>
                            <p class="list" v-if="ysbShow">{{fDec(item.ypjj,4)}}</p>
                            <p class="list" v-if="ysbShow">{{fDec(item.yplj,4)}}</p>
                            <p class="list" v-if="ysbShow">{{fDec(item.yplj*item.kcsl,4)}}</p>
                            <p class="list" v-if="ysbShow">{{item.scph}}</p>
                            <p class="list" >{{item.pcty == 0 ? '启用' : '停用'}}</p>
                            <p class="list" v-if="ysbShow">{{fDate(item.yxqz,'date')}}</p>
                            <p class="list" v-if="ysbShow">{{item.rksj}}</p>
                            <p class="list" v-if="ysbShow">{{item.pydm}}</p>
                            <p class="list" >{{item.fzbl}}</p>
                        </div>
                        <div class="flex-container flex-align-c qdmx_title">
                            <p class="list notBorder " :data-text="ypItem.ypmc"></p>
                            <p class="list notBorder cell-m ysb-green margin-left whiteSpace">材料合计</p>
                            <p class="list notBorder cell-xxl" :data-text="ypItem.ypmc"></p>
                            <p class="list notBorder" :data-text="ypItem.ypmc"></p>
                            <p class="list notBorder" :data-text="ypItem.ypmc">{{ypItem.ypdkc}}</p>
                            <p class="list notBorder" :data-text="ypItem.ypmc">{{ypItem.ypxkc}}</p>
                            <p class="list notBorder" :data-text="ypItem.ypmc">{{ypItem.yphj}}</p>
                            <p class="list notBorder" :data-text="ypItem.ypmc"></p>
                            <p class="list notBorder" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                            <p class="list ysb-yellow" :data-text="ypItem.ypmc"></p>
                        </div>
                    </li>
                </ul>
            </div>-->
			<div class="zui-table-body " @scroll="scrollTable($event)">
			    <table class="zui-table table-width50">
			        <tbody v-for='(ypItem,$index) in jsonList'>
			        <tr v-for='(item,index) in ypItem.ypList' :class="[{'red':Number(item.kcsl)<Number(item.zdkc)}]" class="flex-container flex-align-c qdmx_item"  @dblclick="edit($index,index)">
			            <td>
			                <div class="zui-table-cell cell-m" :class="{'color-wtg':item.kcsl=='0'}" v-text="$index+1"></div>
			            </td>
			            <td>
			                <div class="zui-table-cell cell-xl" v-text="item.ypbm"></div>
			            </td>
			            <td>
			                <div class="zui-table-cell cell-s " v-text="item.ypmc"></div>
			            </td>
			            <td>
			                <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
			            </td>
			            <td>
			                <div class="zui-table-cell cell-s" v-text="item.dkc"></div>
			            </td>
			            <td>
			                <div class="zui-table-cell cell-s" v-text="item.xkc"></div>
			            </td>
			            <td>
			                <div class="zui-table-cell cell-s" v-text=""></div>
			            </td>
			            <td>
			                <div class="zui-table-cell cell-s" v-text="item.ypgg"></div>
			            </td>
			            <td v-if="ysbShow">
			                <div class="zui-table-cell cell-s" v-text="item.jxmc"></div>
			            </td>
			            <td>
			                <div class="zui-table-cell cell-s" v-text="item.ypzlmc"></div>
			            </td>
			            <td v-if="ysbShow">
			                <div class="zui-table-cell cell-s" v-text="item.cdmc"></div>
			            </td>
						<td>
						    <div class="zui-table-cell cell-s" v-text="item.kcsl"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,4)"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,4)"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.kcsl,4)"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s" v-text="item.scph"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s" v-text="item.pcty == 0 ? '启用' : '停用'"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s" v-text="item.rksj"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s" v-text="item.pydm"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
						</td>
			            
			        </tr>
					<tr class="flex-container flex-align-c qdmx_item">
						<td>
						    <div class="zui-table-cell cell-m"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-xl">材料合计</div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s "></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s" v-text="ypItem.ypdkc"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s" v-text="ypItem.ypxkc"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s" v-text="ypItem.yphj"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td v-if="ysbShow">
						    <div class="zui-table-cell cell-s"></div>
						</td>
						<td>
						    <div class="zui-table-cell cell-s"></div>
						</td>
					</tr>
			        </tbody>
					<p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
			    </table>
			</div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>
<!--侧边窗口-->
<div class="side-form  pop-width" v-cloak :class="{'ng-hide':type}"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
            <span class="span0">
                <i>有效期</i>
                <input type="text" class="zui-input border-r4 ytimes"
                       v-model="popContent.yxqz" @keydown="nextFocus($event)" id="_xxq" />
            </span>
        <span class="span0">
                <i>批次停用</i>
                 <select-input
                         @change-data="resultChange"
                         :not_empty="false"
                         :child="stopSign"
                         :index="popContent.pcty"
                         :val="popContent.pcty"
                         :name="'popContent.pcty'" >
                  </select-input>
            </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<script src="kccx.js"></script>
</body>

</html>
