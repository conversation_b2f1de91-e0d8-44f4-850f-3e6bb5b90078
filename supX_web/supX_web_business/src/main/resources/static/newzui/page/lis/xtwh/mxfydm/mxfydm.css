.sjks-content-right {
  width: 100%;
  float: right;
  padding: 0 10px;
  box-sizing: border-box;
}
.sjks-content-right .content-right-top {
  width: 100%;
}
.sjks-content-right .content-right-top i {
  width: calc((100% -50px)/6);
  text-align: center;
}
.sjks-content-right .content-right-top i em {
  margin-top: 10px;
}
.sjks-content-right li {
  cursor: pointer;
  width: 100%;
}
.sjks-content-right li:hover {
  background: rgba(26, 188, 156, 0.08) !important;
  border: 1px solid #1abc9c;
}
.pop-content {
  width: 100%;
  float: right;
}
.pop-content .content-right-top,
.pop-content .content-right-list {
  width: 100%;
}
.pop-content .content-right-top i,
.pop-content .content-right-list i,
.pop-content .content-right-top span,
.pop-content .content-right-list span {
  width: calc((100% / 3));
  text-align: center;
}
.pop-content .content-right-top i em,
.pop-content .content-right-list i em,
.pop-content .content-right-top span em,
.pop-content .content-right-list span em {
  margin-top: 10px;
}
.pop-content li {
  cursor: pointer;
  width: 100%;
}
.pop-content li:hover {
  background: rgba(26, 188, 156, 0.08) !important;
  border: 1px solid #1abc9c;
}
.ksys-side {
  width: 100%;
  padding: 26px 17px;
  float: left;
}
.ksys-side .jiansuo {
  margin-bottom: 20px;
}
.ksys-side span {
  display: flex;
  width: 100%;
  justify-content: center;
  position: relative;
}
.ksys-side span i {
  width: 56px;
  float: left;
  align-items: center;
  align-self: center;
}
.ksys-side #jyxm_icon .switch {
  top: 0;
  left: 17px;
}
.border-r4 {
  border-radius: 4px !important;
}
.ksys-btn {
  position: absolute;
  bottom: 0px;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 60px;
}
.ksys-btn button {
  margin-right: 20px;
}
.content-right-list {
  width: 100%;
  height: 80vh;
  overflow: auto;
  border: 1px solid #e9eee6;
  border-top: none;
}
.content-right-list li {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  line-height: 54px;
  border: 1px solid #e9eee6;
  border-top: 1px solid #fff;
}
.content-right-list li .i {
  width: calc((100% -50px)/6) !important;
  text-align: center;
}
.content-right-list li:nth-child(2n) {
  background: #fdfdfd;
}
.tong-search {
  padding: 13px 0px 5px 20px;
}
