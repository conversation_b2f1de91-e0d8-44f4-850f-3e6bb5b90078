

window.addEventListener('storage',function (e) {
    if( e.key == 'dylyd' && e.oldValue !== e.newValue ) pageInit( JSON.parse( e.newValue ) );
});

function pageInit( dylyd ) {
    setTimeout(function () {
        printFormat.jsonList = dylyd || JSON.parse( sessionStorage.getItem( 'dylyd' ) );
    },100);
}

var printFormat = new Vue({
    el: '#printFormat',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        jsonList: []
    },
    mounted: function () {
        //初始化页面
        pageInit();
    },
    methods: {
        print: function () {
            window.print();
        }
    }
});