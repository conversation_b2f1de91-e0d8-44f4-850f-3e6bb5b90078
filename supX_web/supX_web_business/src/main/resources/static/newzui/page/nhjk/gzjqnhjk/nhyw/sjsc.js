var sjsc_left = new Vue({
    el: '.sjsc-left',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],
    data: {
        ifsc:"",
        popContent:{
        },
        rydjBrxx:{
            sccx:"1",//该标记用于查询农合已经入院登记的人
        },
        zyhList:[],
        rydjBrList:[],
        zyksList:[],
        isChecked:[],
        page:{
            page:1,
            rows:20,
            total:null,
            parm:""
        },
        param:{
            page: 1,
            rows: 20,
            total: null
        },
    },
    methods: {
        searching:function(val){
            sjsc_left.page.parm = val;
            sjsc_left.getData();
        },
        getData:function(){
            sjsc_left.rydjBrxx.page = sjsc_left.page.page;
            sjsc_left.rydjBrxx.rows = sjsc_left.page.rows;
            sjsc_left.rydjBrxx.parm = sjsc_left.page.parm;
            common.openloading("#sjsc");
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + fyxmTab.bxurl + "&bxlbbm=" + fyxmTab.bxlbbm + "&types=inHospital&method=queryAllZybr&parm="
                + JSON.stringify(sjsc_left.rydjBrxx),function(json){
                if(json.a=='0'){
                    var res = JSON.parse(json.d);
                    sjsc_left.rydjBrList = res.list;
                    common.closeLoading();
                }else{
                    malert("获取农合已登记入院病人失败！","top","defeadted");
                    common.closeLoading();
                }
            });
        },
        commonResultChange:function(val){
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "ksbm":
                    Vue.set(this.rydjBrxx, 'ksbm', val[0]);
                    sjsc_left.getData();
                    break;
            }
        },
        changeDown: function (event) {
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                sjsc_left.getData();
            }
        },
        //页面加载时自动获取住院科室数据
        GetZyksData: function () {
            sjsc_left.zyksList = fyxmTab.zyksList;
            sjsc_left.rydjBrxx.ksbm = fyxmTab.ksbm;
        },
        //选中查询该病人未上传费用
        selectOne:function(index){
            var ifPush = true;
            if(this.isChecked[index]){
                for(var i=0;i< sjsc_left.zyhList.length;i++) {
                    if (sjsc_left.zyhList[i].zyh == sjsc_left.rydjBrList[index].zyh) {
                        ifPush = false;
                    }
                }
                if(ifPush){
                    sjsc_left.zyhList.push(sjsc_left.rydjBrList[index]);
                }
            }else {
                for(var i=0;i< sjsc_left.zyhList.length;i++) {
                    if (sjsc_left.zyhList[i].zyh == sjsc_left.rydjBrList[index].zyh) {
                        sjsc_left.zyhList.splice(i, 1);
                    }
                }
            }
            if(sjsc_left.zyhList.length>0){
                sjsc_left.getWscfyByZyh();
            }else{
                wscjl.wscjlList = [];
            }
        },

        getWscfyByZyh:function(){
            if(sjsc_left.zyhList.length<=0){
                malert('请先选择要查询的病人！','top','defeadted');
                return;
            }
            var method = "queryWscjl";
            var zyhStr = "";
            for(var i=0;i<sjsc_left.zyhList.length;i++){
                zyhStr = sjsc_left.zyhList[i].zyh + "," +   zyhStr;
            }
            zyhStr = zyhStr.substr(0,zyhStr.length-1);
            wscjl.param.parm = zyhStr;
            if(sjsc_left.ifsc != null && sjsc_left.ifsc!='' && sjsc_left.ifsc!=undefined){
                method = "queryYscfy";
            }
            common.openloading("#wscjl");
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + fyxmTab.bxurl + "&bxlbbm=" + fyxmTab.bxlbbm + "&types=inHospital&method=" + method + "&parm=" + JSON.stringify(wscjl.param),
                function (json) {
                if (json.a == '0') {
                    common.closeLoading();
                    var res = JSON.parse(json.d);
                    wscjl.wscjlList = res.list;
                    wscjl.totlePage = Math.ceil(res.total / wscjl.param.rows);
                } else{
                    common.closeLoading();
                    malert(json.c,'top','defeadted');
                }
            });
        },
    }
});
sjsc_left.GetZyksData();
setTimeout(function () { // 这里延迟来加载
    sjsc_left.getData();
    menu.selectSchedule();
}, 300);
var menu = new Vue({
    el: '.nh-menu',
    data: {
        which: '0',
        popContent:{},
        startFlag:false,
        ifClick:false,
    },
    methods: {
        selectSchedule:function(){
            common.openloading("#wscjl");
            var str = {
                yljgbm:jgbm,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + fyxmTab.bxurl + "&bxlbbm=" + fyxmTab.bxlbbm + "&types=scheduleTask&method=selectSchedule&parm=" + JSON.stringify(str),
                function (json) {
                    if (json.a == '0') {
                        common.closeLoading();
                        var res = JSON.parse(json.d);
                        menu.popContent.cronDate = res.cronDate;
                        if(res.status == '0'){//如果是开启状态就选中
                            menu.ifClick = true;
                            menu.startFlag = true;
                        }
                    } else{
                        common.closeLoading();
                    }
                });
        },
        loadCon: function (page) {
            var pageDiv = $("#"+page);
            $(".page_div").hide();
            if(pageDiv.length == 0){
                $("."+page).load(page+".html").fadeIn(300);
            } else {
                $("."+page).fadeIn(300);
            }
        },
        zdsc:function(val){
            if(!menu.popContent.cronDate){
                menu.ifClick = false;
                malert("请先选择自动上传启动时间！","top","defeadted");
                return;
            }

            var method = "startTask";
            if(menu.startFlag && !menu.ifClick){
                method = "stopTask";
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + fyxmTab.bxurl + "&bxlbbm=" + fyxmTab.bxlbbm + "&types=scheduleTask&method="+ method +"&parm="
                + JSON.stringify(menu.popContent),function(json){
                if(json.a=='0'){
                    menu.startFlag = !menu.startFlag;
                    malert(json.c);
                }else{
                    menu.ifClick = false;
                    malert(json.c,"top","defeadted");
                }
            });
        },
    }
});
laydate.render({
    elem:"#cronDate",
    rigger: 'click',
    theme: '#1ab394',
    type: 'time',
    done: function (value, data) {
        //应该在这个储存这个值
        menu.popContent.cronDate = value;
    }
});

menu.loadCon('wscjl');