var wrapper = new Vue({
    el: '#jyxm_icon',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
    data: {
        ypSelected:{},
        jsonList: [],
        yfkfList: [],
        totalAmount: 0, //库存总额
        options: {
            '0': '全部',
            '1': '明细',
            '2': '0库存材料',
            '3': '非0库存材料'
        },
        cxbjlist: {
            "2": "全部",
            "0": "低于下限",
            '1': "高于上限"
        },
        pcty_tran: {
            "2": "全部",
            "0": "未停",
            '1': "停用"
        },
        ysbShow:true,
        param: {
            'page': 1,
            'rows': 10,
            'sort': '',
            'order': '',
            'shzfbz': 1,
            'yfbm': '',
            'beginrq': null,
            'endrq': null,
            'parm': '',
            'cxfs':'0',
             pcty1:'2',
             cxbj: '2',
        }
    },
    updated:function(){
        changeWin()
    },
    mounted: function () {
        this.getLfData()
    },
    methods: {
        //获取二级库房
        getLfData: function () {
        	// $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
            //         function(data) {
            //             if(data.a == 0) {
            //                 wrapper.yfkfList = data.d.list;
            //                 Vue.set(wrapper.param, 'yfbm', wrapper.yfkfList[0].yfbm);//默认库房编码
            //                 Vue.set(wrapper.param, 'yfmc', wrapper.yfkfList[0].yfmc);
            //                 wrapper.ysbShow = true;
            //                 wrapper.initMsg();
            //                 wrapper.getData();
            //             } else {
            //                 malert(data.c,'top','defeadted');
            //             }
            //         });

        	//使用权限进行库存查询,用例:N040100021009
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm={"ylbm": "N040100021009"}', function(data) {
                    if(data.a == 0) {
                        wrapper.yfkfList = data.d.list;
                        Vue.set(wrapper.param, 'yfbm', wrapper.yfkfList[0].yfbm);//默认库房编码
                        Vue.set(wrapper.param, 'yfmc', wrapper.yfkfList[0].yfmc);
                        wrapper.ysbShow = true;
                        wrapper.initMsg();
                        wrapper.getData();
                    } else {
                        malert(data.c,'top','defeadted');
                    }
                });
        },
        initMsg:function(){
            if(sessionStorage.getItem("messageParam") && sessionStorage.getItem("messageParam") != 'undefined'){
                var messageParam = JSON.parse(sessionStorage.getItem("messageParam"));
                wrapper.param.yfbm = messageParam.yfbm;
                wrapper.param.cxbj = messageParam.cxbj;
            }
        },
        resultChangeXl: function (val) {
            if (val[2].length > 1) {
                if (Array.isArray(this[val[2][0]])) {
                    Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    this.getData();
                } else {
                    Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);

                    if (val[3] != null) {
                        Vue.set(this[val[2][0]], val[3], val[4]);
                    }
                    this.getData();
                }
            } else {
                this[val[2][0]] = val[0];
                this.param.cxbj = val[0]
                this.getData();
            }
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "yfbm":
                    Vue.set(this.param, 'yfbm', val[0]);
                    Vue.set(this.param, 'yfmc', val[4]);
                    this.param.page=1
                    wrapper.getData();
                    break;
                case "cxfs":
                    if (val[0] == 0) {
                        wrapper.ysbShow = false;
                    }
                    if (val[0] == 1) {
                        wrapper.ysbShow = true;
                    }
                    Vue.set(this.param, 'cxfs', val[0]);
                    Vue.set(this.param, 'fsmc', val[4]);
                    this.param.page=1
                    wrapper.getData();
                    break;
                default:
                    break;
            }
        },
        exportKc: function () {
            if (this.popContent.yfbm == 0) {
                malert('请选择库房', 'top', 'defeadted');
                return;
            }
            //数据查询参数
            var param = {
                'page': 1,
                'rows': 20000,
                'yljgbm': jgbm,
                'yfbm': this.popContent.yfbm,
                'yfmc': this.popContent.yfmc
            };
            //准备地址
            var url = "/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=exportKc&parm=" +
                JSON.stringify(param);
            //组合地址
            this.url = (window.location.protocol + '//' + window.location.host) + url;
            //打开下载页
            window.location = this.url;
        },
        getData: function () {
            this.jsonList=[]
            if (!this.param.yfbm) {
                malert('请选择库房', 'top', 'defeadted');
                return;
            }
            this.param.order = this.param.cxfs == 0 ? 'sum' : null;
            this.param.zeroKc = this.param.cxfs == 2 ? null : '1';
            this.param.pcty = this.param.pcty1 == 2 ? '' : this.param.pcty1;
            if (this.param.cxbj == '0') {
                this.param.cxbjzd = '1';
            } else {
                this.param.cxbjzd = null;
            }
            if (this.param.cxbj == '1') {
                this.param.cxbjzg = '1';
            } else {
                this.param.cxbjzg = null;
            }
            //二级库房编码
            common.openloading('.loadingTable')
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=kc&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0" && json.d) {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                    wrapper.getTotalAmount();
                    sessionStorage.removeItem("messageParam");
                }
            });
            common.closeLoading()
        },
        getTotalAmount:function(){
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=getTotalAmount&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    if(json.d){
                        wrapper.totalAmount = wrapper.fDec(json.d.totalAmountMx,2);
                    }
                }
            });
        },
        //单击选中
        checkOne: function (index, item) {
            this.ypSelected = item;
        },
        //双击修改有效期和批次停用
        edit: function ($index,index) {
            wap.open();
            if (this.queryType) {
                return;
            }
            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[$index]['ypList'][index]))
            wap.popContent.yxqz=this.fDate(wrapper.jsonList[$index]['ypList'][index]['yxqz'], 'date')
        },
    },
});
var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        type: true,
        isShow: false,
        popContent: {},
        isKeyDown: null,
        title: '有效期、批次停用修改'
    },
    methods: {
        //关闭
        closes: function () {
            this.type = true
        },
        open: function () {
            this.type = false
        },
        saveData: function () {
            if (this.popContent.yxqz == null) {
                malert("请输入有效期", 'top', 'defeadted')
                return;
            }
            if (this.popContent.pcty == null) {
                malert("请输入停用标志", 'top', 'defeadted')
                return;
            }

            var ypxxJson = {
                yfbm:this.popContent.yfbm,
                ypbm:this.popContent.ypbm,
                xtph:this.popContent.xtph,
                yxqz:this.popContent.yxqz,
                pcty:this.popContent.pcty
            }

            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfcxCrcx&types=update&parm=" + JSON.stringify(ypxxJson), function (json) {
                if (json.a == 0) {
                    wap.popContent = {}
                    malert("数据保存成功", 'top', 'success');
                    //刷新页面
                    wap.closes();
                    wrapper.getData();
                } else {
                    malert("上传数据失败", 'top', 'defeadted');
                }
            });
        }

    }
});
laydate.render({
    elem: '.ytimes'
    , trigger: 'click'
    , theme: '#1ab394', done: function (value, data) {
        wap.popContent.yxqz = value;
    }
});
