/**
 * Created by mash on 2017/10/8.
 */
var jbbm = new Vue({
    el: '#jbbm',
    mixins: [baseFunc, tableBase],
    data: {
        jsonList: [],
        totlePage: null
    },
    updated: function () {
        changeWin()
    },
    methods: {
        // 下载编码
        downData: function () {
            if (!nh_date) {
                malert("请选择编码下载时间!", 'top', 'defeadted')
                return;
            }
            if (!Menu.bxlbbm) {
                malert("保险编码为空,不能保险接口交易!", 'top', 'defeadted');
                return;
            }
            if (!Menu.bxurl) {
                malert("保险接口地址不能为空,请与管理员联系!", 'top', 'defeadted');
                return;
            }
            if (!Menu.billCode) {
                //jbbm.getS02();
                return;
            }
            var head = {
                operCode: "S29",
                billCode: Menu.billCode,
                rsa: ""
            };
            var body = {
                startDate: nh_date + " 000"
            }

            var param = {
                head: head,
                body: body
            }
            var str_param = JSON.stringify(param);
            console.log(str_param);

            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + Menu.bxurl + "&bxlbbm=" + Menu.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    console.log(json);
                    if (json.a == 0 && json.d) {
                        jbbm.totlePage = Math.ceil(json.d.total / jbbm.param.rows);
                        jbbm.jsonList = json.d;
                        jbbm.getData();
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });


        },
        // 请求数据
        getData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + Menu.bxurl + "&bxlbbm=" + Menu.bxlbbm + "&types=jbbm&method=query&parm=" + JSON.stringify(this.param), function (json) {
//                	console.log(json);
                    if (json.a == 0) {
                        var res = eval('(' + json.d + ')');
                        jbbm.totlePage = Math.ceil(res.total / jbbm.param.rows);
                        jbbm.jsonList = res.list;
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });
        }
    }
});
setTimeout(function () {
    jbbm.getData();
},200)
