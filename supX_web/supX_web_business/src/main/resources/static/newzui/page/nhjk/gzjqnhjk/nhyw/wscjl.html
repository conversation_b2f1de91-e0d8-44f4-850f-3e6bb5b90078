<div id="wscjl">
    <div class="tong-top">
        <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="refresh">刷新</button>
        <button class="tong-btn btn-parmary-b  paddr-r5" @click="scfy" v-if="ifsc">上传费用</button>
        <button class="tong-btn btn-parmary-b  paddr-r5" @click="qxscfy" v-if="!ifsc">取消上传</button>
        <!--<button class="tong-btn btn-parmary-b  paddr-r5" @click="scfyAll" v-if="ifsc">全部费用上传</button>-->
        <!--<button class="tong-btn btn-parmary-b  paddr-r5" @click="qxscfyAll" v-if="!ifsc">全部取消上传</button>-->
        <div>
            <label><input type="checkbox" @click="clickCheckBox" @result="reCheckBox"/>已上传记录</label>
        </div>
    </div>
    <div class="zui-table-view">
        <!--入院登记start-->
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'wscjlList'"
                                                                                                :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>病人姓名</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>项目编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>项目名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>农合编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>农合名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>收费日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>操作员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>类型</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>来源</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body"  @scroll="scrollTable($event)" style="overflow: hidden">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item, $index) in wscjlList"
                    :tabindex="$index"
                    @dblclick="edit(item)"
                    ref="list"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()">
                    <td class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'wscjlList'"
                                                                                          :type="'some'" :which="$index"
                                                                                          :val="isChecked[$index]">
                    </input-checkbox></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.name">病人姓名</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.mxfyxmbm">项目编码</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.mxfyxmmc">项目名称</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bxxmbm">农合编码</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bxxmmc">农合名称</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fydj">单价</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fysl">数量</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fyje">金额</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fygg">规格</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.dw">单位</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.sfrq">收费日期</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.czyxm">操作员</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.type">类型</div></td>
                    <td><div class="zui-table-cell cell-s">费用</div></td>
                    <!--绑定数据放开 数据为空的时候占位-->
                    <p v-if="wscjlList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <div style="height: 100px;"></div>
        <!--左侧固定start-->
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'wscjlList'"
                                                                                                    :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in wscjlList" :tabindex="$index" class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">

                        <td class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'wscjlList'"
                                                                                              :type="'some'" :which="$index"
                                                                                              :val="isChecked[$index]">
                        </input-checkbox></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--左侧固定end-->
        <!--右侧固定start-->
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>医生</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in wscjlList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s" v-text="item.zyysxm"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>


</div>
<div class="wrapper printHide" id="loadingPage"></div>
<script type="application/javascript" src="wscjl.js"></script>