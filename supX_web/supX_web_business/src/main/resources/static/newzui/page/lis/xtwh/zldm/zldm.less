
.sjks-content-right{
  width:100%;
  float: right;
  .content-right-top{
    width: 100%;
    i{
      width: calc(~"(100% -50px)/4");
      text-align: center;
      em{
        margin-top: 10px;
      }
    }
  }
  li{
    cursor: pointer;
    width: 100%;
    &:hover{
      background:rgba(26,188,156,0.08) !important;
      //border:1px solid #1abc9c;
    }
  }
}
.pop-content{
  width:100%;
  float: right;
  .content-right-top,.content-right-list{
    width: 100%;
    i,span{
      width: calc(~"(100% / 3)");
      text-align: center;
      em{
        margin-top: 10px;
      }
    }
  }
  li{
    cursor: pointer;
    width: 100%;
    i{
      width: calc(~"(100% -50px)/4");
      text-align: center;
    }
    &:hover{
      background:rgba(26,188,156,0.08) !important;
      //border:1px solid #1abc9c;
      //height: 52px;
    }
  }
}
.ksys-side{
  width: 100%;
  padding: 15px 10px 15px 14px;
  float: left;
  .jiansuo{
    margin-bottom: 20px;
  }

  #jyxm_icon .switch{
    top:0;
    left:17px;
  }

}
.border-r4{
  border-radius: 4px !important;
}
.ksys-btn{
  position: absolute;
  bottom: 0px;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 60px;
  button{
    margin-right: 20px;
  }
}
.content-right-list{
  width: 100%;
  height:80vh;
  overflow: auto;
  border: 1px solid #e9eee6;
  border-top: none;
  li{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-top: 1px solid #e9eee6;
    i{

      width: calc(~"(100% -50px)/4")!important;
      text-align: center;
    }
    &:nth-child(2n){
      background: #fdfdfd;
    }
    &:first-child{
      border-top: none;
    }
  }
}
body{
  padding: 0;
}


.tong-search{
  padding: 13px 0 5px 20px;
}
.wrapper{
border: none !important;
}