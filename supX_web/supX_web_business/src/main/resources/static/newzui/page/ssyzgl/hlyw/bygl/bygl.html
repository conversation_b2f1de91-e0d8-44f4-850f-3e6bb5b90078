<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>病员管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../../../../css/icon.css">
    <link rel="stylesheet" href="bygl.css">
</head>

<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
    <div class="wrapper" id="loadingPage">
        <!--患者列表-->
        <div v-cloak id="hzlb">
            <div class="panel">
                <div class="tong-top">
                    <button v-waves class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getHzData(),getYsCw()">刷新</button>
                </div>
            </div>

            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label">科室</label>
                        <div class="zui-input-inline wh120">
                            <select-input @change-data="resultChange_ks" :not_empty="false" :child="allKs" :index="'ksmc'"
                                :index_val="'ksbm'" :val="acContent.ksbm" :name="'acContent.ksbm'" :search="true"></select-input>
                        </div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">住院状态</label>
                        <div class="zui-input-inline wh120">
                            <select-input @change-data="resultChange_jkac" :child="zyYzclType_tran" :index="acContent.zyzt"
                                :val="acContent.zyzt" :name="'acContent.zyzt'" :search="true"></select-input>
                        </div>
                    </div>
                    <div class="zui-inline padding-left40">
                        <label class="zui-form-label">护理等级</label>
                        <div class="zui-input-inline wh120">
                            <select-input @change-data="resultChange_jkac" :child="hldj_tran" :index="acContent.hldj"
                                :val="acContent.hldj" :name="'acContent.hldj'" :search="true"></select-input>
                        </div>
                    </div>
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label">检索</label>
                        <div class="zui-input-inline wh182">
                            <input class="zui-input titel" placeholder="请输入检索关键字" id="text-ss" v-model="jsVal" type="text"
                                @keyUp.enter="getHzData()" />
                            <!--@keyUp.enter传检索获取病人的方法！-->
                        </div>
                    </div>
                </div>
            </div>

            <div class="zui-table-view">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>住院号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>性别</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>年龄</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>门特</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>申请科室</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl text-left"><span>西医入院诊断</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl text-left"><span>中医入院诊断</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>入科类型</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>拟入科室</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>状态</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell  cell-l"><span>申请时间</span></div>
                                </th>
                                <th class="cell-s">
                                    <div class="zui-table-cell cell-s"><span>操作</span></div>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body loadingTable" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody v-if="brlist.length">
                            <tr :tabindex="$index" v-for="(item,$index) in brlist" @click="checkSelect([$index,'one','brlist'],$event)"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" class="tableTr2">
                                <!--@dblclick="edit($index)"双击回调-->
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.zyh"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.nl+nldw_tran[item.nldw]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.mtbbz=='1' ? '是':'否'"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.zcksmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl title text-left" v-text="item.xyryzd"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl title text-left" v-text="item.zyryzd"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="jzklx_tran[item.jzklx]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.zrksmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.hszzt"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="fDate(item.djrq,'datetime')">2</div>
                                </td>
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-s flex-container flex-align-c flex-jus-c">
                                        <i v-if="item.hszzt=='待接入'" class="icon-jkac butt-hover" data-title="接科安床"
                                            @click="jkacClick($index)"></i>
                                        <i v-if="item.hszzt=='在院'" class="icon-zkac butt-hover" data-title="转科换床"
                                            @click="zkacClick($index)"></i>
                                        <i v-if="item.hszzt=='在院'" class="fa fa-exchange butt-hover" data-title="迁床处理"
                                            @click="qcclClick($index)"></i>
                                        <i v-if="item.hszzt=='在院'" class="icon-gdfy butt-hover" data-title="固定费用"
                                            @click="gdfyClick($index)"></i>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <p v-if="!brlist.length" class=" noData  text-center zan-border">暂无数据...</p>
                </div>
                <!--左侧固定-->
                <div class="zui-table-fixed table-fixed-l">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                                <tr :tabindex="$index" v-for="(item, $index) in brlist" @click="checkSelect([$index,'one','brlist'],$event)"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" class="tableTr2">
                                    <td>
                                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--右侧固定-->
                <div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell  cell-l"><span>申请时间</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                                <tr :tabindex="$index" v-for="(item, $index) in brlist" @click="checkSelect([$index,'one','brlist'],$event)"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" class="tableTr2">
                                    <td>
                                        <div class="zui-table-cell cell-l" v-text="fDate(item.djrq,'datetime')">2</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s flex-container flex-align-c flex-jus-c">
                                            <i v-if="item.hszzt=='待接入'" class="icon-jkac butt-hover" data-title="接科安床"
                                                @click="jkacClick($index)"></i>
                                            <i v-if="item.hszzt=='在院'" class="icon-zkac butt-hover" data-title="转科换床"
                                                @click="zkacClick($index)"></i>
                                            <i v-if="item.hszzt=='在院'" class="fa fa-exchange butt-hover" data-title="迁床处理"
                                                @click="qcclClick($index)"></i>
                                            <i v-if="item.hszzt=='在院'" class="icon-gdfy butt-hover" data-title="固定费用"
                                                @click="gdfyClick($index)"></i>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

<!--            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>-->
            </div>
        </div>

        <div class="side-form  pop-width" :class="{'ng-hide':Class}" v-cloak  id="jkac" role="form">
            <div class="fyxm-side-top">
                <span v-text="title"></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="ksys-side">
                <div>
                    <i>主管医生</i>
                    <select-input-list :them="them2" :index_mc="'ryxm'" @change-data="resultChangeYs" :data-notEmpty="false" :child="searchCon2" :index="'ryxm'"
                                  :index_val="'rybm'" :val="jkacContent.zyys" :name="'jkacContent.zyys'" :search="true"></select-input-list>
<!--                    <div class="margin-top-5 margin-b-20">-->
<!--                        <input class="zui-input" data-notEmpty="true" v-model="jkacContent.ryxm" @keydown="changeDown2($event,'text')"-->
<!--                            @input="change2(false,$event.target.value)" id="jzys">-->
<!--                        <search-table2 style="width: 275px;" :message="searchCon2" :selected="selSearch" :page="page"-->
<!--                            :them="them2" :them_tran="them_tran2" @click-one="checkedOneOut" @click-two="selectOne2">-->
<!--                        </search-table2>-->
<!--                    </div>-->
                </div>
                <div>
                    <i>责任护士</i>
                    <div class="margin-top-5 margin-b-20">
<!--                        <input class="zui-input" data-notEmpty="true" v-model="jkacContent.ryxm1" @keydown="changeDown3($event,'text')"-->
<!--                               @input="change3(false,$event.target.value)" id="zrhs">-->
<!--                        <search-table2 style="width: 275px;" :message="searchCon3" :selected="selSearch3" :page="page"-->
<!--                                       :them="them3" :them_tran="them_tran2" @click-one="checkedOneOut" @click-two="selectOne3">-->
<!--                        </search-table2>-->

                        <select-input-list :them="them3" :index_mc="'ryxm1'" @change-data="resultChangeYs" :data-notEmpty="false" :child="searchCon3" :index="'ryxm'"
                         :index_val="'rybm'" :val="jkacContent.zrhs" :name="'jkacContent.zrhs'" :search="true"></select-input-list>
                    </div>
                </div>
                <div>
                    <i>床位</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="false" :child="cwList" :index="'cwbh'"
                            :index_val="'cwid'" :val="jkacContent.cwid" :name="'jkacContent.cwid'" :search="true"></select-input>
                    </div>
                </div>
                <div>
                    <i>接入时间</i>
                    <div class="margin-top-5 margin-b-20">
                        <input id="dbegin"  class="zui-input wh180" @click="showDate($index,'dbegin','jrrq')" v-model="jkacContent.jrrq">
                    </div>
                </div>
                <div>
                    <i>西医诊断</i>
                    <div class="margin-top-5 margin-b-20">
                        <textarea type="text" class="zui-textarea border-r4" @keyup="nextFocus($event)" placeholder="请输入西医诊断"
                            v-model='jkacContent.xyryzd'></textarea>
                    </div>
                </div>
                <div>
                    <i>中医诊断</i>
                    <div class="margin-top-5 margin-b-20">
                        <textarea type="text" class="zui-textarea border-r4" @keyup="nextFocus($event)" placeholder="请输入中医诊断"
                            v-model='jkacContent.zyryzd'></textarea>
                    </div>
                </div>
            </div>
            <div class="ksys-btn">
                <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveJkac()">确定</button>
            </div>
        </div>

        <div class="side-form  pop-width" v-cloak :class="{'ng-hide':Class}"  id="zkac" role="form">
            <div class="fyxm-side-top">
                <span v-text="title"></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="ksys-side">
                <div>
                    <i>主管医生</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" disable="disabled" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                            :index_val="'rybm'" :val="zkhcContent.zyys" :name="'zkhcContent.zyys'" :search="true"></select-input>
                    </div>
                </div>
                <div>
                    <i>原科室</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :not_empty="false" :child="Kslist" :index="'ksmc'"
                            :index_val="'ksbm'" :val="zkhcContent.ryks" :name="'zkhcContent.ryks'" :search="true"
                            disable="disabled"></select-input>
                    </div>
                </div>
                <div>
                    <i>转往科室</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :not_empty="false" :child="Kslist" :index="'ksmc'"
                            :index_val="'ksbm'" :val="zkhcContent.zrksbm" :name="'zkhcContent.zrksbm'" :search="true"></select-input>
                    </div>
                </div>
                <!--
            <div>
                <i>床位</i>
                <div class="margin-top-5 margin-b-20">
                    <select-input @change-data="resultChange"
                                  :data-notEmpty="false"
                                  :child="cwList"
                                  :index="'cwbh'"
                                  :index_val="'cwid'"
                                  :val="zkhcContent.cwid"
                                  :name="'zkhcContent.cwid'"
                                  :search="true"></select-input>
                </div>
            </div>
             -->
            </div>
            <div class="ksys-btn">
                <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveZkhc()">确定</button>
            </div>
        </div>

        <div class="side-form  pop-width" v-cloak :class="{'ng-hide':Class}"  id="qccl" role="form">
            <div class="fyxm-side-top">
                <span v-text="title"></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="ksys-side">
                <div>
                    <i>病人姓名</i>
                    <div class="margin-top-5 margin-b-20">
                        <input type="text" class="zui-input" disabled v-model="brInfo.brxm" />
                    </div>
                </div>
                <div>
                    <i>主管医生</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                            :index_val="'rybm'" :val="brInfo.zyys" :name="'brInfo.zyys'" disable></select-input>
                    </div>
                </div>
                <div>
                    <i>原床位</i>
                    <div class="margin-top-5 margin-b-20">
                        <input type="text" class="zui-input" disabled v-model="brInfo.rycwbh" />
                    </div>
                </div>
                <div>
                    <i>现床位</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="false" :child="cwList" :index="'cwbh'"
                            :index_val="'cwid'" :val="qcContent.cwid" :name="'qcContent.cwid'" :search="true"></select-input>
                    </div>
                </div>
            </div>
            <div class="ksys-btn">
                <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveQccl">确定</button>
            </div>
        </div>

    </div>
    <script src="bygl.js"></script>
</body>

</html>
