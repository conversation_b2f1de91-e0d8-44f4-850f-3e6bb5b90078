
/********************************华丽分割线***************************************/
    //列表展示
var tableInfo = new Vue({
        el: '#tableInfo',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
        	fyqkContent:{}, //费用情况对象
			userInfo:{},
			csqxContent:{
                N05001200609:"0",
                N05001200610:"0",
            },
            jsonList: [],

            ksbm:'',
            gxhj:{
        	    fyhj:0,
                ybkzf:0,
                xjzf:0,
            },
			param: {
				sfzjhm:'',
				brxm:'',
			},
			ifclick:false,
			bxurl:'',
			bxlbbm:'',

        },
        //页面渲染完成之后加载数据
        mounted: function () {
			this.getbxlb();
			this.getUserInfo();
        },
	updated:function (){
		changeWin();
	},
        computed:{

        },
        methods: {
			printFun:function (index){
				var reportlets ="[{reportlet:'cwfx%2Fyb%2Fqssqd.cpt',id:'"+tableInfo.jsonList[index].id+"'}]";
				if(!window.top.J_tabLeft.obj.FRorWindow){
				    if (FrPrint(reportlets,null)){
				        return;
				    }
				}
			},
			getUserInfo: function () {
				
				this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
					.then(function (json) {
						tableInfo.userInfo = json.body.d;
					});
			},
			getbxlb: function () {
			    var param = { bxjk : "B07"};
			    $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="+ JSON.stringify(param), function (json) {
			            if (json.a == 0 && json.d.list.length > 0) {
			                    tableInfo.bxlbbm = json.d.list[0].bxlbbm;
			                    tableInfo.bxurl = json.d.list[0].url;
								tableInfo.getData();
			            } else {
			                malert("保险类别查询失败!" + json.c,'right','defeadted');
			            }
			        });
			},

			huitui:function(index){
								if(tableInfo.jsonList[index].yjje>0){
					tpop.isShow = true
					tpop.popContent = JSON.parse(JSON.stringify(tableInfo.jsonList[index]));
					tpop.popContent.yyjje =tableInfo.MathAdd(tableInfo.jsonList[index].yjje,tableInfo.jsonList[index].ytyjje?tableInfo.jsonList[index].ytyjje:0);
					tpop.popContent.yjje =tableInfo.MathAdd(tableInfo.jsonList[index].yjje,tableInfo.jsonList[index].ytyjje?tableInfo.jsonList[index].ytyjje:0);

				}else{
					malert("已退数据不可再进行退费",'right','defeadted');
				}
			},
            ShowMz:function () {
                pop.isShow = true
            },
            getData:function(){
				var str_param = {parm: this.param.parm, page: this.param.page, rows: this.param.rows,sfzjhm:this.param.sfzjhm,brxm:tableInfo.param.brxm};
				$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=queryBryj&parm=" + JSON.stringify(str_param), function (json) {
				    if (json.a == 0) {
						var datalist = eval('(' + json.d + ')');
				        tableInfo.jsonList = datalist.records
				    } else {
				        malert(json.c,'right','defeadted');
				    }

				});
			}
            }
    });
var pop = new Vue({
    el: '#yjxxPop',
    mixins: [dic_transform, baseFunc, tableBase, mformat, scrollOps],
    data: {
        isShow: false,
        previewshow: false,
        clearNum: 0,
        index: 0,
        edit: true,
        loginuserId: '',
		loginuserName:'',
        popContent: {
			czybm:'',
			czyxm:'',

		},
		zflxList:[{
			zflxbm:'1',
			zflxmc:'现金',
		},{
			zflxbm:'25',
			zflxmc:'Minpos微信',
		},{
			zflxbm:'26',
			zflxmc:'Minpos支付宝',
		},{
			zflxbm:'27',
			zflxmc:'Minpos银行卡',
		},],
		popTitle:'新增押金',
		saveclick:false,

    },
	mounted: function () {
		this.popContent.czybm = userId;
		this.popContent.czyxm = userName;

	},
    methods: {

		selectCzyBz:function (val){
			Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);

		},
		saveYjxx: function () {
			if(!pop.popContent.sfzjhm){
				malert("身份证必填",'right','defeadted');
				return false;
			}
			if(!pop.popContent.brxm){
				malert("病人姓名必填",'right','defeadted');
				return false;
			}
			if(!pop.popContent.yjje){
				malert("金额必填",'right','defeadted');
				return false;
			}
			if(tpop.popContent.yjje<=0){
				malert("金额必须大于0",'right','defeadted');
				return false;
			}
			if(!pop.popContent.zflxbm){
				malert("支付类型必填",'right','defeadted');
				return false;
			}

			if(pop.saveclick){
				malert("不要重复点击",'right','defeadted');
				return false;
			}

			pop.saveclick = true;

			if(pop.popContent.zflxbm == '1'){
				$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + tableInfo.bxurl + "&bxlbbm=" + tableInfo.bxlbbm + "&types=mzjy&method=insertBryj&parm=" + JSON.stringify(pop.popContent), function (json) {
					pop.saveclick = false;
					if (json.a == 0) {
						malert("保存成功",'right','success');
						pop.isShow = false;
						pop.popContent={}
						tableInfo.getData();
					} else {
						malert(json.c,'right','defeadted');
					}

				});
			}else{
				if(pop.popContent.zflxbm == '25'){ //微信
					$("#codeContent").focus();
					common.openloading("#yjxxPop","请出示付款码。。。。");
					pop.setTimeOutForPay(0,pop.popContent.zflxbm);
				}else if(pop.popContent.zflxbm == '26'){ //支付宝
					$("#codeContent").focus();
					common.openloading("#yjxxPop","请出示付款码。。。。");
					pop.setTimeOutForPay(0,pop.popContent.zflxbm);
				}else if(pop.popContent.zflxbm == '27'){ // 银行卡
					pop.scanToPayForJzyy(pop.popContent.zflxbm);
				}
			}
        },

		setTimeOutForPay:function(time,zflxjk){
			if(time < 60000){
				if(pop.popContent.codeContent && pop.popContent.codeContent.length >= 18){
					pop.scanToPayForJzyy(zflxjk);
				}else{
					setTimeout(function(){
						time += 1000;
						pop.setTimeOutForPay(time,zflxjk);
					},1000);
				}
			}else{
				malert("已等待一分钟！","top","defeadted");
				pop.popContent.codeContent = "";
				common.closeLoading();
				
				return;
			}
		},

		scanToPayForJzyy:function(zflxjk){//建筑医院
			this.resObj={};
			var yylx=zflxjk=='27'?'00':'02',resObj;
			var param={
				czybm:userId,
				czyxm:userName,
				hisGrbh:pop.popContent.sfzjhm,
				bzsm:'门诊押金款',
				inJson: {
					yylx:yylx,
					fyje:pop.popContent.yjje,
					zfcm:pop.popContent.codeContent,
				},
				yljgbm:jgbm
			}
			param.inJson=JSON.stringify(param.inJson);
			common.openloading("#syjjInfo","正在支付。。。。。");
			param.zflxbm = zflxjk;//支付类型编码
			pop.updatedAjax("http://localhost:9002/posinterface/xf",JSON.stringify(param), function (json) {
				if(json.returnCode == "0"){
					malert(json.msgInfo);
					var outResult = JSON.parse(json.outResult);
					pop.saveclick = false;
					common.closeLoading();
					pop.popContent.innerorderno = outResult.refer?outResult.refer:'';
					pop.popContent.tradeno = outResult.trace?outResult.trace:'';

					$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + tableInfo.bxurl + "&bxlbbm=" + tableInfo.bxlbbm + "&types=mzjy&method=insertBryj&parm=" + JSON.stringify(pop.popContent), function (json) {
						pop.saveclick = false;
						if (json.a == 0) {
							malert("保存成功",'right','success');
							pop.isShow = false;
							pop.popContent={}
							tableInfo.getData();
						} else {
							malert(json.c,'right','defeadted');
						}

					});


					pop.popContent.codeContent = "";
					common.closeLoading();
					return;
				}else{
					malert(json.msgInfo,"top","defeadted");
					common.closeLoading();
					pop.saveclick = false;
					common.closeLoading();
					pop.popContent.codeContent = "";
					return;
				}
			},function (error) {
				pop.saveclick = false;
				common.closeLoading();
				malert("支付发生错误！错误原因：" + error.statusText,"top","defeadted");
				pop.popContent.codeContent = "";
				common.closeLoading();
				return;
			});
		},


		cancel: function () {
			pop.isShow = false;
			pop.popContent={}
        },

    },
});


var tpop = new Vue({
	el: '#tyjxxPop',
	mixins: [dic_transform, baseFunc, tableBase, mformat, scrollOps],
	data: {
		isShow: false,
		previewshow: false,
		clearNum: 0,
		index: 0,
		edit: true,
		loginuserId: '',
		loginuserName:'',
		popContent: {
			czybm:'',
			czyxm:'',

		},
		zflxList:[{
			zflxbm:'1',
			zflxmc:'现金',
		},{
			zflxbm:'25',
			zflxmc:'Minpos微信',
		},{
			zflxbm:'26',
			zflxmc:'Minpos支付宝',
		},{
			zflxbm:'27',
			zflxmc:'Minpos银行卡',
		},],
		popTitle:'退押金',
		saveclick:false,

	},
	mounted: function () {
		this.popContent.czybm = userId;
		this.popContent.czyxm = userName;

	},
	methods: {

		selectCzyBz:function (val){
			Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);

		},
		saveYjxx: function () {

			if(!tpop.popContent.yjje){
				malert("金额必填",'right','defeadted');
				return false;
			}

			if(tpop.popContent.yjje<=0){
				malert("金额必须大于0",'right','defeadted');
				return false;
			}

			if(!tpop.popContent.zflxbm){
				malert("支付类型必填",'right','defeadted');
				return false;
			}

			if(tpop.saveclick){
				malert("不要重复点击",'right','defeadted');
				return false;
			}

			tpop.saveclick = true;





			if(pop.popContent.zflxbm == '1'){
				tpop.finalesave();
			}else{
				tpop.jzyyYdtf()
			}


		},
		finalesave:function(){
			let param = JSON.parse(JSON.stringify(tpop.popContent));
			param.yjje = -tpop.popContent.yjje

			param.tyjjlid = tpop.popContent.yjjlid
			param.yjjlid = null
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + tableInfo.bxurl + "&bxlbbm=" + tableInfo.bxlbbm + "&types=mzjy&method=insertBryj&parm=" + JSON.stringify(param), function (json) {
				tpop.saveclick = false;
				if (json.a == 0) {
					malert("保存成功",'right','success');
					tpop.isShow = false;
					tpop.popContent={}
					tableInfo.getData();
				} else {
					malert(json.c,'right','defeadted');
				}

			});
		},
		jzyyYdtf:function(){

			var yylx=tpop.popContent.zflxbm=='27'?'00':'02';

			var param={
				czybm:userId,
				czyxm:userName,
				hisGrbh:tpop.popContent.sfzjhm,
				bzsm:'预交款退费',
				inJson: {
					fyje:tpop.popContent.yjje,
					yylx:yylx,
				},
				yljgbm:jgbm
			}
			if(this.pageData.jjlInfo.zflxbm  == '27') {
				param.inJson.yjyrq=tpop.popContent.innerorderno//原交易日期
				param.inJson.yjyckh=tpop.popContent.tradeno//原交易参考号
			}else {
				param.inJson.ysddh=tpop.popContent.tradeno //原交易参考号
			}
			param.inJson=JSON.stringify(param.inJson);
			this.postAjax("http://localhost:9002/posinterface/tf",JSON.stringify(param), function (json) {
				if (json.returnCode == 0) {
					resObj=JSON.parse(json.outResult);
					if(resObj.bank_code != ''){
						malert(json.msgInfo , 'right');
						tpop.finalesave();
					}else {
						malert(resObj.resp_chin , 'right', 'defeadted');
						tpop.saveclick = false;
					}
				} else {
					tpop.saveclick = false;
					malert(json.c , 'right', 'defeadted');
					return false;
				}
			},function (){

			});
		},
		cancel: function () {
			tpop.isShow = false;
			tpop.popContent={}
		},

	},
});

/****************编辑页面 初始化页面加载事件 ******************/


/*********************快捷键********************************/
$(document).keydown(function (e) {
    //F2门诊收费保存
    if (e.keyCode == 113) {
        zd_enter.saveData();//保存
    }
});


