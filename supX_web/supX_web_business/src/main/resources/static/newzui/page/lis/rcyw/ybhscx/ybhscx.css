@media print {
    @page {
        size: A4 portrait;
        margin: 3.7cm 2.6cm 3.5cm;
    }
    h1 {
        page-break-before: always;
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    thead,
    tfoot,
    tr,
    th,
    td,
    li {
        page-break-inside: avoid;
    }
    body {
        background-color: white;
        color: black;
    }
    nav,
    aside {
        display: none;
    }
    a::after {
        content: "(" attr(href) ")";
    }
    thead,
    tfoot {
        display: table-row-group;
    }
}
i,
em {
    font-style: normal;
}
.fr {
    float: right !important;
}
.fl {
    float: left !important;
}
.color-1a {
    color: #1abc9c !important;
}
.color-red {
    color: #ff4532 !important;
}
.popus {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 9999;
    display: none;
    top: 0;
    background: rgba(0, 0, 0, 0.5);
}
.popus .pop-system {
    width: 380px;
    height: 220px;
    background: #fff !important;
    position: absolute;
    left: 50%;
    margin-left: -190px;
    top: 50%;
    margin-top: -110px;
}
.popus .pop-system h2 {
    height: 46px;
    background: #1abc9c !important;
    line-height: 46px;
    color: #fff !important;
    padding: 0 18px;
}
.popus .pop-system h2 span {
    float: left;
    font-size: 16px !important;
}
.popus .pop-system h2 span:nth-child(2) {
    color: rgba(255, 255, 255, 0.56);
    font-size: 33px !important;
    cursor: pointer;
}
.popus .pop-system .pop-text {
    width: 100%;
    font-size: 14px !important;
    padding: 43px 0  62px 0;
    text-align: center;
}
.pop-ok {
    width: 100%;
    display: flex;
    justify-content: flex-end;
}
.pop-ok .pop-btn {
    width: 88px;
    height: 36px;
    border: none;
    line-height: 36px;
    background: #d9dddc;
    border-radius: 4px;
    color: #8e9694;
    margin-right: 15px;
}
.pop-ok .pop-confirm {
    background: #1abc9c !important;
    color: #fff !important;
}
.popus-right h2 {
    height: 46px;
    background: #1abc9c !important;
    line-height: 46px;
    color: #fff !important;
    padding: 0 18px;
}
.popus-right h2 span {
    float: left;
    font-size: 16px !important;
}
.popus-right h2 a {
    color: rgba(255, 255, 255, 0.56);
    cursor: pointer;
}
.popus-right .sample {
    width: 100%;
    font-size: 14px !important;
    color: #7f8fa4;
    padding: 26px 15px 10px;
}
.popus-right .sample-select {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    height: 36px;
}
.popus-right .sample-select select {
    -webkit-appearance: none;
    height: 36px;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    width: 100%;
}
.popus-right .sample-texarea {
    width: 100%;
    padding: 0 15px;
}
.popus-right .sample-texarea textarea {
    width: 100%;
    border: 1px solid #dfe3e9;
    height: 122px;
    padding: 10px;
}
.popus-right .sample-btn {
    position: absolute;
    bottom: 24px;
    display: flex;
    justify-content: flex-end;
}
.popus-right .ti-close:before {
    font-size: 20px !important;
}
.zui-table-view .zui-table-fixed.table-fixed-r{
    border-left:none;
}
.zui-table-view .zui-table-body{
    height: 70vh;
    min-height: 70vh;
}
/*.zui-table-view .zui-table-body tr{*/
    /*border-bottom: 1px solid #eee;*/
/*}*/