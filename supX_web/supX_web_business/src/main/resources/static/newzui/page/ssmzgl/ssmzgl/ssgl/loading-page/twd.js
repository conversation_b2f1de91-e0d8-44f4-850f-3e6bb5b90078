var id;
var s = new Date().getTime();
var l = new Date();
var e = l.setDate(l.getDate() + 1);
var panel=new Vue({
    el:'.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        time:''
    },
    created: function () {
        id=userNameBg.Brxx_List.zyh;
        this.time = this.$options.filters['formDate'](s)
    },
});
var red = "#AE0000";
var black = "#6C6C6C";
var lattice = 14;
var temPath = false;
var pulsePath = false;
var heartRatePath = false;
var coolingPath = false;
var tssmList = [];
var wcyyList = [];
// json的type【0：口温  1：腋温  2：肛温】
var tw_json = [
//    {"date": '2017-4-17', "time": 2, "temperature": 40, "type": 0},
//    {"date": '2017-4-17', "time": 6, "temperature": 41, "type": 0},
//    {"date": '2017-4-17', "time": 10, "temperature": 39, "type": 1},
//    {"date": '2017-4-17', "time": 18, "temperature": 38, "type": 0},
//    {"date": '2017-4-18', "time": 2, "temperature": 37.8, "type": 2},
//    {"date": '2017-4-18', "time": 6, "temperature": 41, "type": 0},
//    {"date": '2017-4-18', "time": 10, "temperature": 39, "type": 2},
//    {"date": '2017-4-19', "time": 2, "temperature": 38, "type": 0},
//    {"date": '2017-4-19', "time": 22, "temperature": 40, "type": 0},
//    {"date": '2017-4-20', "time": 6, "temperature": 41, "type": 1},
//    {"date": '2017-4-20', "time": 10, "temperature": 39, "type": 0},
//    {"date": '2017-4-20', "time": 18, "temperature": 38, "type": 0}
];
// 脉搏
var mb_json = [
    // {"date": '2017-4-17', "time": 2, "pulse": 100},
    // {"date": '2017-4-17', "time": 6, "pulse": 112},
    // {"date": '2017-4-17', "time": 10, "pulse": 96},
    // {"date": '2017-4-17', "time": 18, "pulse": 84},
    // {"date": '2017-4-18', "time": 2, "pulse": 93},
    // {"date": '2017-4-18', "time": 6, "pulse": 102},
    // {"date": '2017-4-18', "time": 10, "pulse": 94},
    // {"date": '2017-4-19', "time": 2, "pulse": 80},
    // {"date": '2017-4-19', "time": 22, "pulse": 100},
    // {"date": '2017-4-20', "time": 6, "pulse": 110},
    // {"date": '2017-4-20', "time": 10, "pulse": 90},
    // {"date": '2017-4-20', "time": 18, "pulse": 100}
];
// 心率
var xl_json = [
    // {"date": '2017-4-17', "time": 2, "heartRate": 100},
    // {"date": '2017-4-17', "time": 6, "heartRate": 112},
    // {"date": '2017-4-17', "time": 10, "heartRate": 96},
    // {"date": '2017-4-17', "time": 18, "heartRate": 84},
    // {"date": '2017-4-18', "time": 2, "heartRate": 93},
    // {"date": '2017-4-18', "time": 6, "heartRate": 102},
    // {"date": '2017-4-18', "time": 10, "heartRate": 94},
    // {"date": '2017-4-19', "time": 2, "heartRate": 90},
    // {"date": '2017-4-19', "time": 22, "heartRate": 100},
    // {"date": '2017-4-20', "time": 6, "heartRate": 110},
    // {"date": '2017-4-20', "time": 10, "heartRate": 90},
    // {"date": '2017-4-20', "time": 18, "heartRate": 100}
];
var obj = {
    'i': null,
    'x': null
};

//体温单模板
var canvas = document.getElementById("twd_cvs");
var canvasTitle = document.getElementById("twd_title");
var contextTitle = canvasTitle.getContext("2d");
var context = canvas.getContext("2d");

function tw_title() {
    if (canvasTitle == null) {
        return false;
    }
    canvasTitle.width = lattice * 48;
    canvasTitle.height = lattice * 2;

    for (var i = 1; i < 3; i++) {
        if (i == 2) {
            line(contextTitle, 0, i * lattice, lattice * 48, i * lattice, black, 1, 0);
        } else {
            line(contextTitle, 0, i * lattice, lattice * 48, i * lattice, "#000", 1, 1);
        }
    }
    for (var i = 0; i < 49; i++) {
        if (i > 5) {
            if (i % 6 == 0 && i != 6) {
                line(contextTitle, i * lattice, 0, i * lattice, lattice * 40, red, 1, 1);
            }
            if (i == 6) {
                line(contextTitle, i * lattice, 0, i * lattice, lattice * 40, "#000", 1, 0);
            } else {
                line(contextTitle, i * lattice, 0, i * lattice, lattice * 40, black, 1, 1);
            }
        }
        if (i == 3) {
            line(contextTitle, i * lattice, lattice, i * lattice, lattice * 40, black, 1, 1);
        }
    }

    contextTitle.textBaseline = 'middle';
    contextTitle.font = "宋体";
    contextTitle.textAlign = "center";
    contextTitle.fillText("时        间", lattice * 3, lattice * 0.5);
    contextTitle.fillText("脉搏    体温", lattice * 3, lattice * 1.5);
}

// 绘制时间段
function fillTime() {
    contextTitle.textBaseline = 'middle';
    contextTitle.font = "宋体";
    contextTitle.textAlign = "center";
    for (var i = 0; i < 7; i++) {
        contextTitle.fillStyle = red;
        contextTitle.fillText(other.titleTime[0], (i + 1) * lattice * 6 + (lattice * 0.5), lattice * 0.5);
        contextTitle.fillStyle = black;
        contextTitle.fillText(other.titleTime[1], (i + 1) * lattice * 6 + (lattice * 0.5) + lattice, lattice * 0.5);
        contextTitle.fillText(other.titleTime[2], (i + 1) * lattice * 6 + (lattice * 0.5) + (lattice * 2), lattice * 0.5);
        contextTitle.fillText(other.titleTime[3], (i + 1) * lattice * 6 + (lattice * 0.5) + (lattice * 3), lattice * 0.5);
        contextTitle.fillStyle = red;
        contextTitle.fillText(other.titleTime[4], (i + 1) * lattice * 6 + (lattice * 0.5) + (lattice * 4), lattice * 0.5);
        contextTitle.fillText(other.titleTime[5], (i + 1) * lattice * 6 + (lattice * 0.5) + (lattice * 5), lattice * 0.5);
    }
}

function tw_model() {
    if (canvas == null) return false;
    canvas.width = lattice * 48;
    canvas.height = lattice * 40;

    for (var i = 0; i < 41; i++) {
        if (i % 5 == 0 && i != 40) {
            line(context, 6 * lattice, i * lattice, lattice * 48, i * lattice, "#000", 1, 0);
        }
        if (i == 40) {
            line(context, 0, i * lattice, lattice * 48, i * lattice, "#000", 1, 0);
        } else {
            line(context, 6 * lattice, i * lattice, lattice * 48, i * lattice, black, 1, 1);
        }
    }

    for (var i = 0; i < 49; i++) {
        if ((i > 5 || i == 3) && i != 6) {
            if (i % 6 == 0) {
                line(context, i * lattice, 0, i * lattice, lattice * 40, red, 1, 1);
            } else {
                line(context, i * lattice, 0, i * lattice, lattice * 40, black, 1, 1);
            }
        }
        if (i == 6) {
            line(context, i * lattice, 0, i * lattice, lattice * 40, "#000", 1, 0);
        }
    }

    //绘制固定文字内容
    context.textBaseline = 'middle';
    context.fillStyle = "black";
    context.font = "宋体";
    context.textAlign = "left";
    context.fillText("(次/分)  (℃)", 0, 7);
    context.textAlign = "center";
    context.fillText("180     42", lattice * 3, lattice * 1.5);
    context.fillText("160     41", lattice * 3, lattice * 5.5);
    context.fillText("140     40", lattice * 3, lattice * 10.5);
    context.fillText("120     39", lattice * 3, lattice * 15.5);
    context.fillText("100     38", lattice * 3, lattice * 20.5);
    context.fillText(" 80     37", lattice * 3, lattice * 25.5);
    context.fillText(" 60     36", lattice * 3, lattice * 30.5);
    context.fillText(" 40     35", lattice * 3, lattice * 35.5);
}

var mbList = [];
var xlList = [];
//循环描点及连线
function draw(event, json) {
    var day = 0;
    var tem = 0;
    var type = 0;
    var pulse = 0;
    var heartRate = 0;
    var cooling = 0;    // 物理降温
    var lastPoint = 0;
    var lastDate = [];
    var lastPoints = [];
    var listType = '';
    var spot = canvas.getContext("2d");
    for (var i = 0; i < json.length; i++) {
        tem = json[i]['temperature'];
        type = json[i]['type'];
        pulse = json[i]['pulse'];
        heartRate = json[i]['heartRate'];
        cooling = json[i]['wljw'];
        day = 0;
        if (i % 6 != 0 && json[i].date != json[i - 1].date) {
            day += DateDiff(json[i - 1].date, json[i].date);
        }
        // 转换时间再比较
        var jsonTime = json[i].date.split("-")[1] + "月" + json[i].date.split("-")[2] + "日";
        for (var a = 0; a < time.currentDay.length; a++) {
            if (jsonTime == time.currentDay[a]) {
                day = day + a;
            }
        }
        lastDate.push(day);
        // 这里根据other.startTime来计算x坐标
        var x = ((json[i].time - other.startTime) / 4) * lattice + (lattice * 6 + (lattice / 2)) + (day * lattice * 6);
        var y = 0;
        spot.save();
        spot.beginPath();
        if (json[i]['tbsm'] != null && json[i]['tbsm'] != "null") {
            // 绘制特殊说明
            context.fillStyle = "red";
            for (var d = 0; d < tssmList[i].length; d++) {
                context.fillText(tssmList[i][d], x, lattice * (0.5 + d));
            }
        }
        if (json[i]['wcyy'] != null && json[i]['wcyy'] != "null" && wcyyList.length > 0) {
            // 绘制未测原因
            context.fillStyle = "red";
            for (var e = 0; e < wcyyList[i].length; e++) {
                context.fillText(wcyyList[i][e], x, (lattice * (0.5 + e)) + (lattice * 35));
            }
        }

        if (heartRate != null) {
            y = (180 - heartRate) / 4 * lattice;
            spot.fillStyle = spot.strokeStyle = "red";
            // 是否标记心跳起搏器
            if (json[i]['xtqbq'] == "1") {
                spot.fillText('h', x, y);
            } else {
                spot.arc(x, y, 5, 0, Math.PI * 2, true);
            }
            lastPoints.push({"x": x, "y": y});
            listType = 'heartRate';
        } else if (pulse != null) {
            y = (180 - pulse) / 4 * lattice;
            spot.fillStyle = spot.strokeStyle = "red";
            spot.arc(x, y, 5, 0, Math.PI * 2, true);
            lastPoints.push({"x": x, "y": y});
            listType = 'pulse';
        } else if (tem != null) {
            y = (42 - tem) * 5 * lattice;
            spot.fillStyle = spot.strokeStyle = "#66B3FF";
            if (type == 0) {
                spot.arc(x, y, 5, 0, Math.PI * 2, true);
            } else if (type == 1) {
                spot.strokeStyle = "transparent";
                spot.arc(x, y, 5, 0, Math.PI * 2, true);
                spot.fillText('╳', x, y, 10);
            } else if (type == 2) {
                spot.arc(x, y, 5, 0, Math.PI * 2, true);
            }
            lastPoints.push({"x": x, "y": y});
        } else {
            lastPoint++;
            lastPoints.push({"x": null, "y": null});
        }
        // 此处要在fill前面执行，很关键
        if (event != null) {
            if (spot.isPointInPath(event.offsetX, event.offsetY)) {
                if (tem) temPath = true;
                else if (pulse) pulsePath = true;
                else if (heartRate) heartRatePath = true;
                obj.i = i;
                obj.x = x;
            }
        }
        spot.closePath();
        if (tem) {
            if (type == 0) spot.fill();
            else if (type == 1) spot.stroke();
            else if (type == 2) spot.stroke();
        } else if (pulse) {
            spot.fill();
        } else if (heartRate) {
            spot.stroke();
        }
        if (i != 0) {
            // 在将要连线的点前面的所有点不能为空
            var blankPoint = 0;
            for (var t = 0; t < i; t++) {
                if (json[t].heartRate == null && json[t].pulse == null && json[t].temperature == null) {
                    blankPoint++;
                }
            }
            if (blankPoint == i) lastPoint = 0;
            // 这里根据other.startTime来计算连线的x坐标
            var LastX = 0;
            var LastY = 0;
            if (json[i]['pulse'] && blankPoint != i) {
                LastX = lastPoints[i - (1 + lastPoint)]['x'];
                LastY = lastPoints[i - (1 + lastPoint)]['y'];
                if (!hadReason(i, lastPoint, json)) line(context, LastX, LastY, x, y, 'red', 1, 1);
                lastPoint = 0;
            } else if (json[i]['temperature'] && blankPoint != i) {
                LastX = lastPoints[i - (1 + lastPoint)]['x'];
                LastY = lastPoints[i - (1 + lastPoint)]['y'];
                if (!hadReason(i, lastPoint, json)) line(context, LastX, LastY, x, y, '#66B3FF', 1, 1);
                lastPoint = 0;
            } else if (json[i]['heartRate'] && blankPoint != i) {
                LastX = lastPoints[i - (1 + lastPoint)]['x'];
                LastY = lastPoints[i - (1 + lastPoint)]['y'];
                if (!hadReason(i, lastPoint, json)) line(context, LastX, LastY, x, y, 'red', 1, 1);
                lastPoint = 0;
            }
        }
        if (obj.i != null && i == obj.i) {
            spot.fillStyle = "#000000";
            if (temPath && tem) {
                if (tem > 41.7)
                    spot.fillText(Math.round(tem * 10) / 10 + "℃", x, y + 20);
                else
                    spot.fillText(Math.round(tem * 10) / 10 + "℃", x, y - 20);
            } else if (pulsePath && pulse) {
                spot.fillText(Math.round(pulse * 10) / 10 + "次/分", x, y - 20);
            } else if (heartRatePath && heartRate) {
                spot.fillText(Math.round(heartRate * 10) / 10 + "次/分", x, y - 20);
            }
        }

        if (cooling) {
            var c = (42 - cooling) * 5 * lattice + lattice;
            var cool = canvas.getContext("2d");
            cool.save();
            cool.beginPath();
            cool.strokeStyle = "red";
            cool.arc(x, c, 5, 0, Math.PI * 2, true);
            if (event != null && cool.isPointInPath(event.offsetX, event.offsetY)) {
                coolingPath = true;
                obj.i = i;
                obj.x = x;
            }
            cool.closePath();
            cool.stroke();
            line(context, x, y, x, c, 'red', 1, 1, true);
            if (obj.i != null && i == obj.i) {
                cool.fillStyle = "#000000";
                if (coolingPath && cooling) {
                    if (cooling > 41.7) cool.fillText(Math.round(cooling * 10) / 10 + "℃", x, c + 20);
                    else cool.fillText(Math.round(cooling * 10) / 10 + "℃", x, c - 20);
                }
            }
        }
    }
    if (listType == 'heartRate') {
        xlList = lastPoints;
        if (mbList.length > 0) drawShade();
    } else if (listType == 'pulse') {
        mbList = lastPoints;
        if (xlList.length > 0) drawShade();
    }
}

function hadReason(i, lastPoint, json) {
    for (var v = 0; v < lastPoint + 1; v++) {
        if (json[i - v]['wcyy'] != null) return true;
    }
    return false;
}

// 绘制阴影
function drawShade() {
    var list = [];
    var xlLine = [];
    var mbLine = [];
    var x, y, a, b, s, e;
    var startPoint = {};
    var endPoint = {};
    var p1, p2, p3;
    var listY = [];
    var isOdd = 2;
    var sList = [];
    var eList = [];
    var ss = [];
    var es = [];

    for (var i = 0; i < xlList.length; i++) {
        isOdd++;
        if (xlList[i]['x'] && xlList[i]['y'] && xlList[i]['x'] == mbList[i]['x'] && xlList[i]['y'] == mbList[i]['y']) {
            if (isOdd % 2 == 0) {
                es.push(i);
                eList.push({'x': xlList[i]['x'], 'y': xlList[i]['y']});
            } else {
                ss.push(i);
                sList.push({'x': xlList[i]['x'], 'y': xlList[i]['y']});
            }
        }
    }
    for (var p = 0; p < sList.length; p++) {
        s = ss[p];
        e = es[p];
        startPoint = sList[p];
        endPoint = eList[p];

        for (var q = s; q <= e; q++) {
            if (xlList[q]['y'] != null) {
                listY.push(xlList[q]['y']);
                xlLine.push({'x': xlList[q]['x'], 'y': xlList[q]['y']})
            }
            if (mbList[q]['y'] != null) {
                listY.push(mbList[q]['y']);
                mbLine.push({'x': mbList[q]['x'], 'y': mbList[q]['y']})
            }
        }
        // 正反push所有的点
        list = xlLine;
        for (var l = mbLine.length - 1; l >= 0; l--) list.push(mbLine[l]);

        var maxY = Math.max.apply(null, listY);
        var minY = Math.min.apply(null, listY);
        for (var d = 0; d < ((endPoint['x'] - startPoint['x']) + (maxY - minY)) / 7; d++) {
            a = {'x': (startPoint['x'] - (maxY - minY)) + (d * 7), 'y': maxY};
            b = {'x': startPoint['x'] + (d * 7), 'y': minY};
            var first = 2;
            var isLine = true;
            for (var n = 0; n < list.length - 1; n++) {
                if (Math.abs(list[n + 1]['x'] - list[n]['x']) != 14) continue;
                p1 = intersection(a, b, list[n], list[n + 1]);
                if (p1) {
                    first++;
                    for (var g = n; g < list.length - 1; g++) {
                        if (Math.abs(list[g + 1]['x'] - list[g]['x']) != 14) continue;
                        p2 = intersection(a, b, list[g], list[g + 1]);
                        if (p2) {
                            isLine = true;
                            for (var k = 0; k < list.length - 1; k++) {
                                p3 = intersection(p1, p2, list[k], list[k + 1]);
                                if (p3 && !isNaN(p3.x) && (p3.x != p1.x && p3.y != p1.y) && (p3.x != p2.x && p3.y != p2.y)) {
                                    isLine = false;
                                    break;
                                }
                            }
                            if (isLine && first % 2 != 0) {
                                line(context, p1.x, p1.y, p2.x, p2.y, 'red', 0.5, 1);
                            }
                        }
                    }
                }
            }
        }
    }
}

// 计算交点
function intersection(a, b, c, d) {
    var area_abc = (a.x - c.x) * (b.y - c.y) - (a.y - c.y) * (b.x - c.x);
    var area_abd = (a.x - d.x) * (b.y - d.y) - (a.y - d.y) * (b.x - d.x);
    if (area_abc * area_abd > 0) return false;
    var area_cda = (c.x - a.x) * (d.y - a.y) - (c.y - a.y) * (d.x - a.x);
    var area_cdb = area_cda + area_abc - area_abd;
    if (area_cda * area_cdb > 0) return false;
    var t = area_cda / ( area_abd - area_abc );
    var dx = t * (b.x - a.x),
        dy = t * (b.y - a.y);
    return {x: a.x + dx, y: a.y + dy};
}

// 重绘
function reDraw() {
    context.clearRect(0, 0, canvas.width, canvas.height);
    tw_model();
    draw(null, tw_json);
    draw(null, mb_json);
    draw(null, xl_json);
}

// 为json赋值
function doJson() {
    canvas.addEventListener('mousemove', function (evt) {
        if (temPath) {
            var temperature = ((210.5 * lattice) - evt.offsetY) / (5 * lattice);
            if (tw_json[obj.i].temperature != temperature) {
                tw_json[obj.i].temperature = temperature;
                reDraw();
            }
        } else if (pulsePath) {
            var pulse = 182 - (evt.offsetY / lattice) * 4;
            if (mb_json[obj.i].pulse != pulse) {
                mb_json[obj.i].pulse = pulse;
                reDraw();
            }
        } else if (heartRatePath) {
            var heartRate = 182 - (evt.offsetY / lattice) * 4;
            if (xl_json[obj.i].heartRate != heartRate) {
                xl_json[obj.i].heartRate = heartRate;
                reDraw();
            }
        } else if (coolingPath) {
            var cool = ((210.5 * lattice) - evt.offsetY) / (5 * lattice);
            if (tw_json[obj.i].wljw != cool) {
                tw_json[obj.i].wljw = cool;
                reDraw();
            }
        }
    });
}

//计算两个日期相差几天
function DateDiff(sDate1, sDate2) {
    var aDate, oDate1, oDate2, iDays;
    aDate = sDate1.split("-");
    oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);
    aDate = sDate2.split("-");
    oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);
    iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24);
    return iDays;
}
//画直线
/*
 * @param context
 * @param fromX
 * @param formY
 * @param toX
 * @param toY
 * @param strokeStyle - default is white
 * @param lineWidth
 * */
function line(context, fromX, formY, toX, toY, strokeStyle, lineWidth, py, isDash) {
    context.save();
    if (py == 1) context.translate(0.5, 0.5);
    if (isDash) context.setLineDash([5, 5]);
    context.lineWidth = lineWidth;
    context.beginPath();
    context.strokeStyle = strokeStyle;
    context.moveTo(fromX, formY);
    context.lineTo(toX, toY);
    context.stroke();
    context.restore();
    context.save();
}

tw_title();
tw_model(tw_json); // 描表格
canvas.addEventListener('mousedown', function (event) { // 监听拖拉
    draw(event, tw_json);
    draw(event, mb_json);
    draw(event, xl_json);
    if (temPath) doJson(tw_json);
    if (pulsePath) doJson(mb_json);
    if (heartRatePath) doJson(xl_json);
    if (coolingPath) doJson(tw_json);
    else reDraw();
});
canvas.addEventListener('mouseup', function () {
    if (obj.i != null) {
        var val = null;
        var json = null;
        if (temPath) {
            val = 'temperature';
            json = tw_json;
        }
        if (pulsePath) {
            val = 'pulse';
            json = mb_json;
        }
        if (heartRatePath) {
            val = 'heartRate';
            json = xl_json;
        }
        if (coolingPath) {
            val = 'wljw';
            json = tw_json;
        }
        if (val && json) {
            var roundNum = json[obj.i][val];
            json[obj.i][val] = (Math.round(roundNum * 10)) / 10;
        }
    }
    obj.i = null;
    temPath = false;
    pulsePath = false;
    heartRatePath = false;
    coolingPath = false;
    reDraw();
});

var date55;

function time3(val) {
    var sa = val.split('-');
    date55 = sa[0] + '-' + sa[1] + '-' + parseInt(sa[2]);
     param = {
        'zyh': id,
        'sxrq': date55
    };
}
time3($("#time").val());

function getTW() {
    $.getJSON("/actionDispatcher.do?reqUrl=HszHlywTwd&types=queryTwByOneBr&parm=" + JSON.stringify(param), function (json) {
        tw_json = json.d.list;
        var date, tbsmsj;
        for (var i = 0; i < tw_json.length; i++) {
            date = new Date(tw_json[i]['date']);
            tw_json[i]['date'] = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
            tw_json[i]['week'] = date.getDay();
            // 加特殊说明
            var font = "";
            if (tw_json[i]['tbsm'] != null && tw_json[i]['tbsm'] != "null" && tw_json[i]['tbsm'] != "") {
                font = tw_json[i]['tbsm'] + tw_json[i]['tbsmsj'];
                tssmList.push(font);
            } else {
                tssmList.push(null);
            }
            // 加未测原因
            if (tw_json[i]['wcyy'] != null && tw_json[i]['wcyy'] != "null" && tw_json[i]['wcyy'] != "") {
                font = tw_json[i]['wcyy'];
                wcyyList.push(font);
            } else {
                wcyyList.push(null);
            }
        }
        draw(null, tw_json);                // 体温描点
    });
}

function getMO() {
    $.getJSON("/actionDispatcher.do?reqUrl=HszHlywTwd&types=queryMbByOneBr&parm=" + JSON.stringify(param), function (json) {
        mb_json = json.d.list;
        var date;
        for (var i = 0; i < mb_json.length; i++) {
            date = new Date(mb_json[i]['date']);
            mb_json[i]['date'] = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
        }
        draw(null, mb_json);                // 脉搏描点
    });
}

function getXL() {
    $.getJSON("/actionDispatcher.do?reqUrl=HszHlywTwd&types=queryXtByOneBr&parm=" + JSON.stringify(param), function (json) {
        xl_json = json.d.list;
        var date;
        for (var i = 0; i < xl_json.length; i++) {
            date = new Date(xl_json[i]['date']);
            xl_json[i]['date'] = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
        }
        draw(null, xl_json);                // 心率描点
    });
}

var time = new Vue({
    el: '#table_1',
    data: {
        currentDay: ['', '', '', '', '', '', ''],
        zyDay: ['', '', '', '', '', '', '']
    },
    methods: {
        // 设置日期、住院天数
        setDay: function (startDay, zyDay) {
            // 删除空数据、再赋值
            this.currentDay = [];
            this.zyDay = [];
            var list = param.sxrq.split('-');
            for (var i = 0; i < 7; i++) {
                var day = new Date((1000 * 60 * 60 * 24 * i) + startDay);
                this.currentDay.push(day.getMonth() + 1 + '月' + day.getDate() + '日');
                other.dayObj[day.getDate()] = i;
                if (day.getDate() == (parseInt(list[2] - 1))) {
                    for (var j = 0; j < 7; j++) {
                        this.zyDay.push(zyDay - i + j);
                    }
                }
            }
            // 设置呼吸的List
            hx.setList();
            // 设置血压的List
            xy.setList();
            // 设置其他信息的List
            other.setList();
        }
    }
});
var hx = new Vue({
    el: '#table_2',
    data: {
        hx_val: [],
        hx_list: []
    },
    created: function () {
        this.getHx();
        for (var i = 0; i < 42; i++) this.hx_list.push('');
    },
    methods: {
        getHx: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=HszHlywTwd&types=queryHxByOneBr&parm=" + JSON.stringify(param), function (json) {
                hx.hx_val = json.d.list;
                hx.setList();
            });
        },
        setList: function () {
            if (this.hx_val.length == 0 || JSON.stringify(other.dayObj) == '{}') return false;
            for (var i = 0; i < this.hx_val.length; i++) {
                var day = new Date(this.hx_val[i].date);
                var num = parseInt(other.dayObj[day.getDate()]) * 6 + ((this.hx_val[i].time - 3) / 4);
                var _td = $('#table_2 td');
                if (i % 2 == 0) {
                    _td.eq(num + 1).addClass('td_bottom');
                } else {
                    _td.eq(num + 1).addClass('td_top');
                }
                if (this.hx_val[i].fx == 0 || this.hx_val[i].fx == null) {
                    this.hx_val[i].fx = "";
                }
                // 是否人工呼吸
                if (this.hx_val[i]['rgfx'] == '1') {
                    Vue.set(this.hx_list, num, "®");
                } else {
                    Vue.set(this.hx_list, num, this.hx_val[i].fx);
                }
            }
        }
    }
});
var xy = new Vue({
    el: '#table_3',
    data: {
        xy_val: [],
        xy_list: []                        // 血压
    },
    created: function () {
        for (var i = 0; i < 14; i++) this.xy_list.push('');
    },
    methods: {
        setList: function () {
            for (var i = 0; i < other.other_json.length; i++) {
                var day = new Date(other.other_json[i]['clrq']);
                var num = parseInt(other.dayObj[day.getDate()]) * 2;
                Vue.set(this.xy_list, num, other.other_json[i]['swxy']);
                Vue.set(this.xy_list, num + 1, other.other_json[i]['xwxy']);
            }
        }
    }
});
var other = new Vue({
    el: '#table_4',
    data: {
        other_json: [],                     // 其他信息的list
        titleTime: [],                      // 日期的list
        startTime: null,                    // 时间段的开始时间
        dayObj: {},                         // 日期的object
        rlList: ['', '', '', '', '', '', ''],
        clList: ['', '', '', '', '', '', ''],
        dbList: ['', '', '', '', '', '', ''],
        tzList: ['', '', '', '', '', '', ''],
        sgList: ['', '', '', '', '', '', '']
    },
    created: function () {
        this.getOther();
    },
    methods: {
        getOther: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=HszHlywTwd&types=queryQtjl&parm=" + JSON.stringify(param), function (json) {
                other.other_json = json.d.list;
                // 设置标题时间
                time.setDay(json.d.time.ksrq, json.d.time.zyts);
                // 设置住院天数
                other.titleTime = json.d.sd;
                // 设置时间段的开始时间，以便计算图形位置
                other.startTime = json.d.sd[0];
                // 画时间段
                fillTime();
                // 获取体温单
                getTW();
                // 获取脉搏
                getMO();
                // 获取心率
                getXL();
            });
        },
        setList: function () {
            for (var i = 0; i < this.other_json.length; i++) {
                var day = new Date(this.other_json[i]['clrq']);
                var num = parseInt(other.dayObj[day.getDate()]);
                /*if (this.other_json[i]['dbcs'] == 0 || this.other_json[i]['dbcs'] == null) {*/
                if (this.other_json[i]['dbcs'] == null) {
                    this.other_json[i]['dbcs'] = "";
                }
                console.log("大便");
                console.log(this.other_json[i]['dbcs']);
//               	if (this.other_json[i]['dbcs'] == 0) {
//                    this.other_json[i]['dbcs'] = "";
//                }
                Vue.set(this.rlList, num, String(this.other_json[i]['srl']).replace("null", "") + ' ' + String(this.other_json[i]['yrl']).replace("null", "") + ' ' + String(this.other_json[i]['zrl']).replace("null", ""));
                Vue.set(this.clList, num, String(this.other_json[i]['zcl']).replace("null", ""));
                Vue.set(this.dbList, num, String(this.other_json[i]['dbcs']).replace("null", "") + ' ' + String(this.other_json[i]['dbl']).replace("null", ""));
                Vue.set(this.tzList, num, String(this.other_json[i]['tz']).replace("null", ""));
                Vue.set(this.sgList, num, String(this.other_json[i]['sg']).replace("null", ""));
            }
        }
    }
});
laydate.render({
    elem: '.todate'
    , eventElem: '.zui-date i.datenox'
    , trigger: 'click'
    , theme: '#1ab394'
    // range: true
    , done: function (value, data) {
        panel.time = value;
        time3(value);
        other.getOther()
    }
});
function prev() {
    var prev=new Date();
    var aa=new Date(panel.time);
    prev.setDate(aa.getDate()-7);
    var pared=new Date(prev).getTime();
    panel.time=formatTime(pared,'date');
    time3(panel.time);
other.getOther()
}
function next() {
    var next=new Date();
    var aa=new Date(panel.time);
    next.setDate(aa.getDate()+7);
    var nextd=new Date(next).getTime();
    panel.time=formatTime(nextd,'date');
    time3(panel.time);
    other.getOther()
}
$(window).resize(function () {
    $('.twd').css('height', $('body').outerHeight() - $('.twd').offset().top-10)
});
$('.twd').css('height', $('body').outerHeight() - $('.twd').offset().top-10);