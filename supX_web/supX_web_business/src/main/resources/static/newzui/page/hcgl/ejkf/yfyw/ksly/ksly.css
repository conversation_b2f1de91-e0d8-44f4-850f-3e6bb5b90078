.tong-btn {
  width: auto;
  min-width: 72px;
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border: none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right: 10px;
}
.font12 {
  font-size: 12px !important;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}
.wh240 {
  width: 240px!important;
}
.wh182 {
  width: 182px !important;
}
.wh100 {
  width: 100px !important;
}
.wh66 {
  width: 66px !important;
}
.wh112 {
  width: 112px !important;
}
.wh120 {
  width: 120px !important;
}
.wh122 {
  width: 122px !important;
}
.wh138 {
  width: 138px !important;
}
.wh200 {
  width: 200px !important;
}
.wh220 {
  width: 220px !important;
}
.wh150 {
  width: 150px !important;
}
.wh1000 {
  width: 80% !important;
}
.wh50 {
  width: 50px !important;
}
.wh70 {
  width: 70px !important;
}
.width162 {
  width: 162px !important;
}
.wh160 {
  width: 160px !important;
}
.wh453 {
  width: 453px !important;
}
.wh247 {
  width: 243px !important;
  display: flex;
  justify-content: start;
  align-items: center;
}
.wh179 {
  width: 179px !important;
}
.wh59 {
  width: 59px !important;
}
.padd {
  padding: 0 !important;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-ed {
  background: #edf2f1 !important;
}
.color-green {
  color: #1abc9c !important;
  font-style: normal;
}
.color-dsh {
  color: #f3b169;
}
.color-ysh {
  color: #45e135;
}
.color-wtg {
  color: #ff4735;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.rkgl-position {
  position: fixed;
  bottom: 10px;
  display: flex;
  justify-content: flex-start;
  left: 10px;
  right: 10px;
  width: auto;
  z-index: 11;
  height: 60px;
  background: #fff;
  align-items: center;
  color: #81878e;
  padding-left: 20px;
  border-top: 1px solid #eee;
}
.rkgl-position .rkgl-fl {
  width: auto;
  float: left;
  padding-left: 15px;
}
.rkgl-position .rkgl-fl i {
  float: left;
  padding-left: 20px;
}
.rkgl-position span {
  display: block;
}
.rkgl-position .rkgl-fr {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.icon-fy:before {
  left: 39px !important;
}
.icon-dy:before {
  left: 17px !important;
}
.icon-ty:before {
  left: 77px !important;
}
.slgl-by {
  width: 100%;
  height: 40px;
  display: flex;
  line-height: 40px;
  background: rgba(242, 166, 84, 0.08);
  justify-content: space-between;
}
.slgl-by i {
  width: 20%;
  display: block;
  text-align: center;
  color: #757c83;
}
.slgl-by i:nth-child(5) {
  padding-right: 15px;
}
.slgl-by i em {
  color: #354052;
  padding-left: 5px;
  float: left;
}
.fyxm-tab div {
  border: none !important;
  padding: 0 40px 0 0;
}
.fyxm-tab div:first-child {
  border: none !important;
}
.actives {
  border-bottom: 2px solid #1abc9c;
  color: #1abc9c;
}
.fyty-fr {
  float: right;
  width: 120px;
  padding-right: 20px !important;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
}
.fyty-fr i {
  float: left;
}
.fyty-fr .fyty-select {
  width: 50px;
  border: none !important;
  text-align: right;
  -webkit-appearance: none;
  position: relative;
  height: 26px;
  line-height: 26px;
}
.fyty-fr .fyty-dsj {
  width: 10px;
  position: absolute;
  top: 29px;
  right: 0;
  height: 10px;
  background: url("../../../../css/images/<EMAIL>") center right no-repeat;
  transform: rotate(270deg);
}
.cfhj-top {
  width: 100%;
  height: 36px;
  background: #edf2f1;
  line-height: 36px;
}
.cfhj-top li {
  width: 100%;
  display: flex;
  justify-content: center;
}
.cfhj-top li i {
  width: calc((100% - 50px)/8);
  display: block;
  text-align: center;
}
.cfhj-top li i:nth-child(1) {
  width: 50px !important;
}
.cfhj-content {
  width: 100%;
  overflow: auto;
  max-height: 500px;
}
.cfhj-content li {
  width: 100%;
  display: flex;
  border: 1px solid #eee;
  border-top: none;
  justify-content: center;
  height: 40px;
  line-height: 16px;
  align-items: center;
}
.cfhj-content li i {
  width: calc((100% - 50px)/8);
  display: block;
  text-align: center;
}
.cfhj-content li i:nth-child(1) {
  width: 50px !important;
}
.cfhj-content li:hover {
  background: rgba(26, 188, 156, 0.08);
}
.title-width {
  width: 70px !important;
}
.contenter {
  width: 100%;
  background: #fff;
  min-height: 768px;
  float: left;
  padding: 0 15px;
}
.contenter .cont-left {
  width: calc((100% - 15px) /2);
  float: left;
}
.contenter .cont-left .left-top {
  width: 100%;
  background: #edf2f1;
  overflow: hidden;
  height: 36px;
  line-height: 36px;
}
.contenter .cont-left .left-top i {
  width: calc((100% - 50px)/3);
  display: block;
  float: left;
  text-align: center;
}
.contenter .cont-left .left-top i:nth-child(1) {
  width: 50px !important;
}
.contenter .cont-left .left-list {
  width: 100%;
  overflow: auto;
  max-height: 650px;
}
.contenter .cont-left .left-list li {
  width: 100%;
  line-height: 40px;
  border: 1px solid #eee;
  border-top: none;
  overflow: hidden;
  cursor: pointer;
}
.contenter .cont-left .left-list li i {
  width: calc((100% - 50px)/3);
  display: block;
  float: left;
  text-align: center;
}
.contenter .cont-left .left-list li i:nth-child(1) {
  width: 50px !important;
}
.contenter .cont-left .left-list li i em {
  margin: 0 auto;
}
.contenter .cont-left .left-list li:hover {
  background: #edfaf7 !important;
}
.contenter .cont-right {
  float: right;
}
.contenter .cont-right .right-top {
  width: 100%;
  background: #edf2f1;
  overflow: hidden;
  height: 36px;
  line-height: 36px;
}
.contenter .cont-right .right-top i {
  width: calc((100% - 50px)/5);
  display: block;
  text-align: center;
  float: left;
}
.contenter .cont-right .right-top i:nth-child(1) {
  width: 50px !important;
}
.contenter .cont-right .right-list {
  width: 100%;
  overflow: auto;
  max-height: 650px;
}
.contenter .cont-right .right-list li {
  width: 100%;
  line-height: 40px;
  border: 1px solid #eee;
  border-top: 1px solid #fff;
  overflow: hidden;
  cursor: pointer;
}
.contenter .cont-right .right-list li i {
  width: calc((100% - 50px)/5);
  display: block;
  float: left;
  text-align: center;
}
.contenter .cont-right .right-list li i:nth-child(1) {
  width: 50px !important;
}
.contenter .cont-right .right-list li i em {
  margin: 0 auto;
}
.contenter .cont-right .right-list li:hover {
  background: #edfaf7 !important;
}
.zui-form .zui-form-label {
  left: 5px !important;
}
.h2title {
  display: none;
}
.pop-850 .ksys-side {
  padding: 15px;
}
