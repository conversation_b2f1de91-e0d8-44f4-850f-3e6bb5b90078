var menuTool=new Vue({
    el:'#jyxm_icon',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    components: {
        'search-table': searchTable
    },
    data:{
        zyh:null, //选中的住院号
        jsbz:null, //选中的接收标志
        ksrq: '', //开始时间
        jsrq: '',  //结束时间
        brxxContent:{},//下拉table检索选中对象
        jsonList:[],
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        parm:{},
        them_tran: {},
        them: {
            '病案号' : 'bah',
            '住院号' : 'zyh',
            '病人姓名':'brxm',
            '病人性别':'brxbmc',
            '年龄':'nl',
            '入院日期':'ryrq',
            '入院科室':'ryksmc',
            '住院医生':'zyysxm',
            '床位编号':'rycwbh'
        }},
    //页面渲染完成之后加载数据
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var myDate=new Date();
        this.ksrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
        this.jsrq = this.fDate(new Date(), 'date')+' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.ksrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    menuTool.ksrq = value;
                } else {
                    menuTool.ksrq = '';
                }
                menuTool.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.jsrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    menuTool.jsrq = value;
                } else {
                    menuTool.jsrq = '';
                }
                //获取一次列表
                menuTool.getData();
            }
        });},
    methods:{
        sx:function () {
            menuTool.getData();
            },
        //检索查询回车键
        searchHc: function() {
            if(window.event.keyCode == 13) {
                menuTool.getData();
            }
        },
        //病人基本信息下拉检索
        changeDown: function (event,type) {
            if(this['searchCon'][this.selSearch]==undefined)
                return;
            this.keyCodeFunction(event,'brxxContent','searchCon');
            //选中之后的回调操作
            if(event.code == 'Enter' || event.code == 13 ||event.code== 'NumpadEnter'){
                if(type=='text'){
                    Vue.set(menuTool.brxxContent, 'text', this.brxxContent['zyh']);
                    menuTool.getData(0);
                }
            }},
        //当输入值后才触发
        change:function(add,type){
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.brxxContent[type] == undefined || this.brxxContent[type] == null){
                this.page.parm = "";
            }else{
                this.page.parm = this.brxxContent[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
            var json={shbz: 1};
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=getBaJbxxList'+'&dg='+JSON.stringify(str_param)+"&json="+JSON.stringify(json),
            function (data) {
                if(data.d.list.length>0){
                    //如果查询结果只有1条则直接请求后台查询门诊费用信息
                    if(data.d.list.length==1){
                        menuTool.getData(data.d.list[0].zyh);
                    }
                    if(add){
                        for(var i=0;i<data.d.list.length;i++){
                            var ryrq = menuTool.fDate(data.d.list[i].ryrq, "date");
                            Vue.set(data.d.list[i], 'ryrq', ryrq);
                            menuTool.searchCon.push(data.d.list[i]);
                        }
                    }else{
                        for(var i=0;i<data.d.list.length;i++){
                            var ryrq = menuTool.fDate(data.d.list[i].ryrq, "date");
                            Vue.set(data.d.list[i], 'ryrq', ryrq);
                        }
                        menuTool.searchCon = data.d.list;
                    }
                }
                menuTool.page.total = data.d.total;
                menuTool.selSearch = 0;
                if(data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                    return false;
                }
            });
        },
        //鼠标双击（病人挂号信息）
        selectOne: function (item) {
            if(item==null){
                this.page.page++;
                this.searching(true,'text');
            }else{
                this.brxxContent = item;
                Vue.set(menuTool.brxxContent, 'text', this.brxxContent['zyh']);
                menuTool.getData(0);
                $(".selectGroup").hide();
            }
        },

        //重写复选框方法
        reCheckBoxBr: function (val) {
            // console.log("复选框对象如下：");
            // console.log(val);
            // if (val[0] == 'all') {
            //     this.isCheckAll = val[2];
            //     console.log(this.isCheckAll);
            //     if (val[1] == null) val[1] = "jsonList";
            //     if (this.isCheckAll) {
            //         for (var i = 0; i < this[val[1]].length; i++) {
            //             Vue.set(this.isChecked, i, true);
            //             // this.isChecked[i] = true;
            //         }
            //     } else {
            //         this.isChecked = [];
            //     }
            // }
            // this.fyhjAll = 0;
            // if (val[2]){
            //     for(var i=0; i<this.jsonList.length; i++){
            //         var item = this.jsonList[i];
            //         if (this.isChecked[i]){
            //             this.fyhjAll += item.fyje;
            //         }
            //     }
            // }
            // this.fyhjAll = this.fyhjAll.toFixed(2);

        },

        //处理复选业务逻辑
        ischeck: function (index) {
            for (var i = 0; i < this.jsonList.length; i++) {
                if (i == index) {
                    Vue.set(this.isChecked, i, true);
                } else {
                    Vue.set(this.isChecked, i, false);
                }
            }
        },

        //重写行选择方法
        checkSelect: function (val, event,item) {
            console.log("复选框对象如下：");
            console.log(val);
            if (val[1] == 'some') {
                Vue.set(this.isChecked, val[0], !this.isChecked[val[0]]);
                if (!val[2]) this.isCheckAll = false;
                if (!this.isChecked[val[0]]) {
                    this.isCheckAll = false;
                    this.isChecked[val[0]] = false;
                    for (var i = 0; i < this.jsonList.length; i++) {
                        Vue.set(this.isChecked, i, false);
                    }
                } else { //选中
                    this.ischeck(val[0]);
                }
                console.log(this.isChecked)
            }
        },

        //登记
        saveDate: function(){
            var jsglList = [];
            var zyhList=[];
            for(var i=0;i<this.isChecked.length;i++){
                if(this.isChecked[i] == true){
                    var gdbz={};
                    var zyh={};
                    gdbz.gdbz = this.jsonList[i].gdbz;
                    zyh.zyh = this.jsonList[i].zyh;
                    jsglList.push(gdbz);
                    zyhList.push(zyh);
                    console.log(gdbz.gdbz);
                }
            }
            if(gdbz.gdbz==1){
                malert("该记录已经登记！",'top','defeadted');
                return false;
            }else if(gdbz.gdbz==0){
                // var parm={
                //     zyh
                // };
                this.$http.post("/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateGd&",
                    JSON.stringify(zyh)).then(function (data) {
                        if(data.body.a == 0){
                            malert("登记成功",'top','success');
                            this.getData(1);
                        } else {
                            malert("登记失败"+data.body.c,'top','defeadted');
                        }},function (error) {
                        console.log(error);
                    });
            }
        },
        //取消登记
        qxdjDate: function(){
            var jsglList = [];
            var zyhList=[];
            for(var i=0;i<this.isChecked.length;i++){
                if(this.isChecked[i] == true){
                    var gdbz={};
                    var zyh={};
                    gdbz.gdbz = this.jsonList[i].gdbz;
                    zyh.zyh = this.jsonList[i].zyh;
                    jsglList.push(gdbz);
                    zyhList.push(zyh);
                    console.log(gdbz.gdbz);
                }
            }
            if(gdbz.gdbz==0){
                malert("该记录还未登记！",'top','defeadted');
                return false;
            }else if(gdbz.gdbz==1){
                this.$http.post("/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateQxgd&",
                    JSON.stringify(zyh)).then(function (data) {
                        if(data.body.a == 0){
                            malert("取消登记成功",'top','success');
                            this.getData(1);
                        } else {
                            malert("取消登记失败"+data.body.c,'top','defeadted');
                        }},function (error) {
                        console.log(error);
                    });
            }
        },
        getData: function (isNum) {
            var parm={
                shbz: 1
            };
            if(isNum==0){//根据住院号检索
                parm.zyh=menuTool.brxxContent['zyh'];
            }else{//根据时间筛选
                menuTool.brxxContent={};
                //结束时间加上一天再查询
                parm.beginrq=menuTool.ksrq;
                parm.endrq=menuTool.jsrq;
                parm.gdbz='1';
            }
            menuTool.jsonList =[];
            $.getJSON("/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=queryIsSh&parm="
                +JSON.stringify(parm), function (json) {
                if(json.a == 0){
                    menuTool.jsonList = json.d.list;
                }
            });
        },
        //双击跳转
        goNew:function(item){
            // this.topNewPage('追溯管理','page/bagl/sygl/jsgl/bazs.html?item='+JSON.stringify(item))
            //还没有接口
        },
        //获取到选中的住院号与接收标志
        checkOne: function(index){
            this.isChecked = [];
            this.isChecked[index] = true;
            menuTool.zyh=this.jsonList[index]['zyh'];
            menuTool.gdbz=this.jsonList[index]['gdbz'];
        }
    }
});

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目

    menuTool.getData(1);






