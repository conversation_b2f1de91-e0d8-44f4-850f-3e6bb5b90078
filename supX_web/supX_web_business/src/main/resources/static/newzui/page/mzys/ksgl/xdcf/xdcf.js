(function(){
    //改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
    //统筹类别
    var tableInfo = new Vue({
        el: '#kswh',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
            jsonList: [],
            indexs:0,
            isShow:false,
            isShowpopL:false,
            isCheckedall:false,
            title: '组合医嘱',
            popContent: {},
            ksbmList: [],//科室集合
            rybmList: [],//人员集合
            cflxList: [],//处方类型集合
            yfList: [],  //药房集合
        },
        methods : {
            go: function (index) {
                this.indexs = index;
                this.getData()
                this.isChecked = [];
                this.isCheckedall = false;
            },
            //初始化页面加载列表
            getData: function () {
                this.param.rows=20;
                this.param.sort='zhyzbm';
                this.param.lx='1';
                if($("#zhyzjsvalue").val()!=null&&$("#zhyzjsvalue").val()!=''){
                    this.param.parm=$("#zhyzjsvalue").val();
                }else{
                    this.param.parm='';
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=query&parm="+JSON.stringify(this.param),function (json) {
                    if(json.d!=null){
                        tableInfo.totlePage = Math.ceil(json.d.total/tableInfo.param.rows);
                        tableInfo.jsonList = json.d.list;
                    }
                });
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    this.getData();
                }
            },
            //下拉框科室加载
            yyksSelect: function(){
                this.param.rows=20000;
                this.param.sort='';
                this.param.order='';
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg="+JSON.stringify(this.param),function (json) {
                    tableInfo.ksbmList = json.d.list;
                });
            },
            //下拉框人员加载
            yyrSelect: function(){
                this.param.rows=20000;
                this.param.sort='';
                this.param.order='';
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg="+JSON.stringify(this.param),function (json) {
                    tableInfo.rybmList = json.d.list;
                });
            },
            //下拉框处方类型加载
            cflxSelect: function(){
                this.param.rows=20000;
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=cflx&dg="+JSON.stringify(this.param),function (json) {
                    tableInfo.cflxList = json.d.list;
                });
            },

            //下拉框药房加载
            yfSelect: function(){
                this.param.rows=20000;
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf&dg="+JSON.stringify(this.param),function (json) {
                    tableInfo.yfList = json.d.list;
                });
            },

            //弹出层显示
            addData: function () {
                tableInfo.popContent = {};
                tableInfo.isShow = true;
                tableInfo.isShowpopL = true;
            },
            //修改
            edit: function (num) {
                if(num == null){
                    for(var i=0;i<this.isChecked.length;i++){
                        if(this.isChecked[i] == true){
                            num = i;
                            break;
                        }
                    }
                    if(num == null){
                        //malert('111','top','defeadted')
                        malert("请选中你要修改的数据",'top','defeadted');
                        return false;
                    }
                }
                tableInfo.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                tableInfo.isShow = true;
                tableInfo.isShowpopL = true;
            },
            //删除
            remove: function () {
                var zhyzList = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                        var zhyz ={};
                        zhyz.zhyzbm = this.jsonList[i].zhyzbm;
                        zhyzList.push(zhyz);
                    }
                }
                if(zhyzList.length == 0){
                    malert("请选中您要删除的数据",'top','defeadted');
                    return false;
                }
                if(!confirm("请确认是否删除")){
                    return false;
                }

                var json = '{"list":'+JSON.stringify(zhyzList)+'}';
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=delete&',
                    json).then(function (data) {
                    tableInfo.getData();
                    if(data.body.a == 0){
                        malert("删除成功",'top','success')
                    } else {
                        malert("删除失败",'top','defeadted')
                    }
                }, function (error) {
                    console.log(error);
                });
            },
            saveData: function () {
                if(!tableInfo.ifClick) return;
                tableInfo.ifClick=false;
                if(this.popContent.cflxbm==null||this.popContent.cflxbm==""){
                    malert("处方类型不能为空！",'top','defeadted');
                    tableInfo.ifClick=true;
                    return;
                }
                if(this.popContent.yfbm==null||this.popContent.yfbm==""){
                    malert("药房不能为空！",'top','defeadted');
                    tableInfo.ifClick=true;
                    return;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=save',
                    JSON.stringify(this.popContent))
                    .then(function (data) {
                        if(data.body.a == 0){
                            malert("数据更新成功",'top','defeadted');
                            this.popContent={};
                            tableInfo.ifClick=true;
                            tableInfo.getData();
                            tableInfo.isShow = false;
                            tableInfo.isShowpopL = false;
                        } else {
                            malert("数据失败",'top','defeadted');
                            tableInfo.ifClick=true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
            },
            //最后一个回车保存
            saveHc: function(event){
                if(event.code == 'Enter' || event.code == 13 ||event.code== 'NumpadEnter'){
                    this.saveData();
                }
            }
        }
    });

    //列表
    tableInfo.getData();
    tableInfo.yyksSelect();
    tableInfo.yyrSelect();
    tableInfo.cflxSelect();
    tableInfo.yfSelect();


    //为table循环添加拖拉的div（组合医嘱）
    var drawWidthNumZhyz = $(".patientTableZhyz tr").eq(0).find("th").length;
    for(var i=0;i<drawWidthNumZhyz;i++){
        if(i>=2){
            $(".patientTableZhyz th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
        }
    }

})();
