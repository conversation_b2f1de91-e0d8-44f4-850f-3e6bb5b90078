@import "../../../../css/baseColor";
.emrmb-left{
  width:10%;
  float: left;
  max-width: 250px;
  min-width: 250px;
  overflow: auto;
  padding-top: 20px;
  min-height: 700px;
  background: @colorff;
  position: relative;
  border: 1px solid @coloree;
  .ksywhd-top{
    float: left;
    width: 100%;
    height: 31px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: absolute;
    left: 0;
    padding: 0 0 0 15px;
    top:64px;
    z-index: 11;
    background: @colorff;
    .ksywhd-title{
      padding-left:10px;
      cursor: pointer;

    }
    &.active{
      background: @colorRgb01;
      color: @color1a;
    }
  }
  .emrmb-list{
    width: 100%;
    float: left;
    margin-top: 31px;
    .emrmb-yj{
      width: 100%;
      padding-left:35px;
      box-sizing: border-box;
    }
    .emrmb-ej-detail{
      display: none;
      li{
        padding:5px 0 0 10px;
        display: flex;
        cursor: pointer;
        justify-content: flex-start;
        align-items: center;
        &:last-child{
          margin-bottom: 10px;
        }
      }
    }
  }
  .emrmb-yj-title{
    display: block;
    cursor: pointer;
    width: 100%;
    margin-bottom: 10px;
    overflow: hidden;
  }
}
.emrmb-right{
  width:84%;
  float: right;
  min-width: 840px;
}
.zui-form .zui-inline{
  padding: 0 20px 0 60px;
}
.zui-form-label{
  width: 55px !important;
  line-height: 28px;
}


.tab-edit-list li label .dz-height{
  height: 70px !important;
  width: 76% !important;
  overflow: auto;

}
.width100{
  width: 100% !important;
  float: left;
  height: 70px !important;
}
.width150{
  width: 70px !important;
}
.ksys-side{
span {
  position: inherit;
}
  .zui-input{
    width: 162px !important;
  }
}
.validate{
  top: 57%;
  left: -54px;
}

@media screen and (max-width: 1366px) {
.emrmb-right{
  width: auto;
  max-width: 84%;
  min-width: 840px;
}
}