<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>收费统计</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper background-f" id="kswh">
        <div class="panel " >
            <div class="flex-container flex-align-c">
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)">查询</button>
            </div>
            <div class="grid-box" style="padding: 13px 0;">
                <div class="col-xxl-12 flex-container margin-l-10">
                    <div class="flex-container flex-align-c  margin-l-10">
                        <label class="whiteSpace margin-r-5 ft-14">科室</label>
                            <select-input class="wh80" @change-data="resultChangeItem"
                            :child="cardList" :index="'bdfp'" :index_val="'bdfpnum'" :val="param.bdfpnum"
                            :name="'param.bdfp'" :search="false">
                            </select-input>
                    </div>
                    <div class="flex-container flex-align-c margin-l-20">
                        <div class=" margin-l13 flex-container flex-align-c">
                            <label class="whiteSpace margin-r-5 ft-14" v-if="indexs==0">检索</label>
                            <input type="text" class="zui-input" v-if="indexs==0" @keydown.13="goToPage(1)" v-model="param.parm" placeholder="病人姓名/挂号序号/发票号" />
                            <label class="whiteSpace margin-r-5 ft-14">&emsp;时间段</label>
                            <input type="text" name="phone" class="zui-input" id="startTime" v-model="param.beginrq" placeholder="开始时间" />
                            <span class="padd-l-10 padd-r-10">至</span>
                            <input type="text" name="phone" class="zui-input" id="endTime" v-model="param.endrq" placeholder="结束时间" />
                        </div>
                    </div>
                    <div class="flex-container flex-align-c  margin-l-10" v-if="indexs==2">
                        <span class="fyhjTotal">费用合计：{{fDec(jeContent.fyhj?jeContent.fyhj:0, 2)}}元&emsp;</span>
                        <span class="fyhjTotal">医保卡合计：{{fDec(jeContent.ybkzf?jeContent.ybkzf:0, 2)}}元&emsp;</span>
                        <span class="fyhjTotal" >现金合计：{{fDec(jeContent.xjzf?jeContent.xjzf:0, 2)}}元</span>
                    </div>
                    <div class="flex-container flex-align-c margin-l-10" v-if="indexs==0">
                        <span class="fyhjTotal">费用合计：{{fDec(money, 2)}}元</span>
                    </div>
                </div>
            </div>
        <div class="zui-table-view hzList padd-l-10 padd-r-10">
            <tabs :num="indexs"   :tab-child="[{text:'收费明细'},{text:'科室汇总'},{text:'收费查询'}]" @tab-active="go"></tabs>
            <div class="gj_all " key="a" v-if="indexs==0">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>保险类别</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>发票号码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>费别</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>费用类别</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>组合费用</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>费用项目</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>单价</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>数量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell  cell-s"><span>金额</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>收费日期</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>医嘱号码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>收费窗口</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>收费员</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>门诊医生</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>门诊科室</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50" v-if="jsonList.length!=0">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                            @dblclick="edit($index)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.brxm"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l"><span v-text="item.ryghxh"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.rybxlbmc"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.fphm"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.ryfbmc"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.fylbmc"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.zhfymc"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.mxfyxmmc"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="fDec(item.fydj,2)"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.fysl"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell  cell-s"><span v-text="fDec(item.fyje,2)"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="fDate(item.sfsj,'datetime')"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.yzhm"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.ywckmc"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.czyxm"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.mzysxm"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.mzksmc"></span></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </div>
            </div>
            <div class="mz_all " key="b" v-show="indexs==1">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>科室</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>费用类别</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>操作员</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>费用</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body">
                    <table class="zui-table" v-if="jsonList.length!=0">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                            @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td v-show="item.zxksmc">
                                <div class="zui-table-cell cell-s">{{item.zxksmc}}</div>
                            </td>
                            <td v-show="item.mzksmc">
                                <div class="zui-table-cell cell-s">{{item.mzksmc}}</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.fylbmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.czyxm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.fyje"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </div>
            </div>
            <div class="mz_all1" key="c" v-show="indexs==2">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>保险类别</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>费别</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>费用合计</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>医保卡支付</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>医疗卡支付</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>现金支付</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>其他支付</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>操作员姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>是否结算</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body">
                    <table class="zui-table" v-if="jsonList.length!=0">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                            @dblclick="edit($index)">
                           <td class="cell-m">
                                <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.brxm"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l"><span v-text="item.ryghxh"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.bxlbmc"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.fbmc"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="fDec(item.fyhj,2)"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="fDec(item.ybkzf,2)"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="fDec(item.ylkzf,2)"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="fDec(item.xjzf,2)"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="fDec(item.qtzf,2)"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="item.czyxm"></span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s"><span v-text="istrue_tran[item.jszt]"></span></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </div>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>
<script type="text/javascript" src="sftj.js"></script>
</body>
</html>
