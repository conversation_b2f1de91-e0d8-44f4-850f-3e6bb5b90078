<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>费用项目</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="xzqhyzbm.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div id="jbbm" class="contextInfo background-f">
    <!--<div class="panel box-fixed" style="top:65px;">-->
        <!--<div class="tong-top">-->
            <!--<button class="tong-btn btn-parmary" @click="addData"><span class="icon-xz1 padd-r-5"></span>新增</button>-->
            <!--<button class="tong-btn btn-parmary-b" @click="getData"><span class="icon-sx paddr-r5 icon-font14"></span>刷新</button>-->
            <!--<button class="tong-btn btn-parmary-b" @click="remove"><span class="icon-sc-header paddr-r5 icon-font15"></span>删除</button>-->
        <!--</div>-->
        <!--<div class="tong-search">-->
            <!--<div class="zui-form">-->
            <!--<div class="zui-inline ">-->
                <!--<label class="zui-form-label">检索</label>-->
                <!--<div class="zui-input-inline width162">-->
                    <!--<input type="text" class="zui-input" id="jbbmjsvalue" @keydown="searchHc()"/>-->
                <!--</div>-->
            <!--</div>-->
            <!--</div>-->
        <!--</div>-->
    <!--</div>-->
    <div class="zui-table-view " id="utable1" z-height="full" style="border: none;">
        <div class="zui-table-header">
            <table class="font-14 zui-table">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox  class=" padd-t-5" @result="reCheckBox" :list="'jsonList'"
                                                                                  :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </div>
                    </th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>疾病编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>疾病名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>统计码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>疾病级别</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>五笔码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>传染病标志</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>附加编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产妇分娩标志</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>停用标志</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'some','jsonList'],$event)">
                    <td class="cell-m"><div class="zui-table-cell cell-m ">
                        <input-checkbox class=" padd-t-5" @result="reCheckBox" :list="'jsonList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jbmb"></div></td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jbmc">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s " v-text="item.pydm">
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.tjm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jbjb"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.wbbm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="istrue_tran[item.crbbz]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fjbm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="istrue_tran[item.cffmbz]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="stopSign[item.tybz]"></div></td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span  class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-width icon-sc" data-title="删除" @click="remove($index)"></i>
                                </em>
                               </span>
                       </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table font-14">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m padd-t-5"><input-checkbox  @result="reCheckBox" :list="'jsonList'"
                                                                                      :type="'all'" :val="isCheckAll">
                        </input-checkbox></div></th>
                        <th class="cell-m"><div class="zui-table-cell cell-m font-14"><span>序号</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)":class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                              :type="'some'" :which="$index"
                                                                                              :val="isChecked[$index]">
                        </input-checkbox></div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
<!--侧边窗口-->
<div class="side-form  pop-width" :class="{'ng-hide':nums==1}"  style="padding-top: 0;"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--值域类别-->
    <div class="ksys-side">
       <span class="span0">
            <i>疾病编码</i>
            <input class="zui-input border-r4" v-model="popContent.jbmb" :data-notEmpty="true" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>疾病名称</i>
            <input class="zui-input border-r4" v-model="popContent.jbmc" :data-notEmpty="true" @keydown="nextFocus($event)"
       @blur="setPYDM(popContent.jbmc,'popContent','pydm')">
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input class="zui-input border-r4 background-h" v-model="popContent.pydm" disabled="disabled">
        </span>
        <span class="span0">
            <i>统计码</i>
            <input class="zui-input border-r4" v-model="popContent.tjm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>疾病级别</i>
            <input class="zui-input border-r4" v-model="popContent.jbjb" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>五笔码</i>
            <input class="zui-input border-r4" v-model="popContent.wbbm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>附加编码</i>
            <input class="zui-input border-r4" v-model="popContent.fjbm" @keydown="nextFocus($event)">
        </span>
        <span class="span0">
            <i>传染病标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
              :child="istrue_tran" :index="popContent.crbbz" :val="popContent.crbbz"
              :name="'popContent.crbbz'">
               </select-input>
        </span>
        <span class="span0">
            <i>产妇分娩标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
              :child="istrue_tran" :index="popContent.cffmbz" :val="popContent.cffmbz"
              :name="'popContent.cffmbz'">
                </select-input>
        </span>
        <span class="span0">
            <i>停用标志</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
              :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
              :name="'popContent.tybz'">
              </select-input>
            <input type="text" style="border: none;width: 0;height: 0;"  @keydown.enter="saveData"/>
        </span>
    </div>

    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>




<script type="text/javascript" src="jbbm.js"></script>

</body>
</html>
