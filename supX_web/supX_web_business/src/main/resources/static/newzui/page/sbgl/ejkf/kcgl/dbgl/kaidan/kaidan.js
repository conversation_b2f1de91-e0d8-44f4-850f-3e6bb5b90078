var wrapper = new Vue({
        el: '#wrapper',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            keyWord: '',
            zfShow: false,
            mxShShow: true,
            dyShow: false,
            jsonList: [],
            ylbm: 'N040030020022003',
            title: '',
            totle: '',
            zdy: userId,
            dbdList: [],
            thdDetail: [], // 退货单明细集合
            yfbm: null,
            zhuangtai: {
                "0": "待审核",
                "1": "已审核",
                "2": "作废",
            },
            queren: {
                "0": "待确认",
                "1": "确认"
            },
            cgryList: [], // 退库人员
            KSList: [],
            KFList: [],
            ghdwList: [],
            // 材料信息对象
            isSubmited: false,
            isUpdate: 0,
            modifyIndex: null,
            isShow: false,
            popContent: {},
            bsdContent: {
                ckfs: "03", // 报损方式
                "zdrq": getTodayDateTime(),
                lyr: userId
                // 操作人
            },
            // 调拨单
            dbdContent: {
                'zdrq': getTodayDate(),
                'kfbm': null,
            },
            zdrq: getTodayDateTime(), // 获取制单日期
            zdyxm: '',
            jyinput: false, //禁用输入框
            TjShow: true,
			AudShow: true,
            qxksbm: '',
            rkd: {}, // 入库单对象
            json: {
                jjzj: 0,
                ljzj: 0
            },//价格总计
            yfList: [],
			dqyfList:[],
            ryList: [],
            flag: false,
            ckd: null,//页面传过来的数据-出库单号
            isNew: 0
        },
        computed:{
            getKS:function () {
                    for (var i = 0; i < this.yfList.length; i++) {
                        if (this.popContent.yfbm == wrapper.yfList[i].yfbm) {
                            this.ksbm = this.yfList[i].ksbm;
                            break;
                        }
                    }
            },
            money:function () {
                var reducers = {
                    totalInEuros: function(state, item) {
                        return state.jjzj += item.ypjj * parseFloat(item.cksl);
                    },
                    totalInYen: function(state, item) {
                        return state.ljzj += item.yplj * parseFloat(item.cksl);
                    }
                };
                var manageReducers = function(reducers){
                    return function(state, item){
                        return Object.keys(reducers).reduce(function(nextState, key){
                            reducers[key](state, item);
                            return state;
                        },{})
                    }
                }
                var bigTotalPriceReducer = manageReducers(reducers);
                this.jsonList.reduce(bigTotalPriceReducer, this.json={
                    jjzj:0,
                    ljzj:0,
                });
            }
        },
        watch: {
            'popContent.yfbm': function () {
                if (wrapper.popContent.yfbm == wrapper.popContent.dbyf) {
                    malert("调入、调出二级库房不能相同！",'top','defeadted');
                    Vue.set(wrapper.popContent, 'yfbm', '');
                }
            },
            'popContent.dbyf': function () {
                if (wrapper.popContent.yfbm == wrapper.popContent.dbyf) {
                    malert("调入、调出二级库房不能相同！",'top','defeadted');
                    Vue.set(wrapper.popContent, 'dbyf', '');
                }
            },
        },
        updated: function () {
            changeWin();
        },
        mounted: function () {
            this.initial();
        },
        methods: {
            initial: function () {
                var reg = /^[\'\"]+|[\'\"]+$/g;
                this.zdyxm = sessionStorage.getItem("userName" + userId).replace(reg, '');
                this.getYFData();
				this.getDqYFData();
				this.ckd = JSON.parse(sessionStorage.getItem('dbglitem'));
				if(this.ckd && this.ckd.ckdh){
					
					common.openloading('.zui-table-view')
					var bean = {
					    ckdh: this.ckd.ckdh
					}
					$.getJSON("/actionDispatcher.do?reqUrl=YfbKcglDbgl&types=queryMx&bean=" + JSON.stringify(bean), function (json) {
					        if (json.a == 0 && json.d) {
					            wrapper.jsonList = json.d;
					        }
					    });
					common.closeLoading()
					$.getJSON("/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=query&bean=" + JSON.stringify(this.ckd), function (json) {
					        if (json.a == 0 && json.d) {
					            wrapper.dbd = json.d.list[0];//调拨单详情
					            wrapper.getYFData(wrapper.dbd);
					        }
					    });
				}
            },
            // 新增
            AddMdel: function () {
                this.isUpdate = 0;
                pop.title = '添加材料'
                pop.open();
                pop.dbdContent = {
                };
                    //新增
                    Vue.set(pop.dbdContent, 'zdrq', getTodayDateTime());// 当前时间
                    Vue.set(pop.dbdContent, 'zdr', userId)// 默认当前操作员-先为第一个
            },

            // 获取二级库房
            getYFData: function (item) {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf", function (json) {
                        if (json.a == 0) {
                            wrapper.yfList = json.d.list;
							
							if (item) {
							    Vue.set(wrapper.popContent, 'yfbm', item.yfbm);//调出
							    Vue.set(wrapper.popContent, 'dbyf', item.dbyf);//调入
							    Vue.set(wrapper.bsdContent, 'zdrq', item.zdrq);//制单日期
							    Vue.set(wrapper.bsdContent, 'lyr', item.jbrmc);//制单人
							    
							}else{
								if (wrapper.yfList.length > 0){
								    Vue.set(wrapper.popContent, 'yfbm', wrapper.yfList[0].yfbm);//二级库房默认
								}
							}
                        }
                    });
            },
			//获取二级库房
			getDqYFData: function () {
			    //初始化页面记载库房
			    $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm={"ylbm": "N040100021003"}',
			        function (data) {
			            if (data.a == 0) {
			                wrapper.dqyfList = data.d.list;
			                if (wrapper.dqyfList.length > 0){
			                    Vue.set(wrapper.popContent, 'dbyf', wrapper.dqyfList[0].yfbm);//二级库房默认
			                }
			            } else {
			                malert("当前科室获取失败", 'right', 'defeadted');
			            }
			        });
			},
			
            resultChanges: function (val) {
                Vue.set(this.popContent, 'yfbm', val[0]);
                Vue.set(this.popContent, 'yfmc', val[4]);
            },
            resultChange: function (val) {
                Vue.set(this.popContent, 'dbyf', val[0]);
                Vue.set(this.popContent, 'dbyfmc', val[4]);
            },
            submitAll: function () {
                // 是否禁止提交
                if (this.isSubmited) {
                    malert('数据提交中，请稍候！','top','defeadted');
                    return;
                }
                if (this.jsonList.length == 0) {
                    malert('没有数据可以提交','top','defeadted');
                    return;
                }
                // 备注描述
                var bzms = pop.dbdContent.bzms;
                if (!bzms) {
                    Vue.set(pop.dbdContent, 'bzms', "调拨管理");
                }
                ;
                // 是否禁止提交
                this.isSubmited = true;
                Vue.set(pop.dbdContent, 'lyks', wrapper.ksbm);
                if (wrapper.dbd) {
                    this.$set(pop, 'dbdContent', wrapper.dbd);
                }
                pop.dbdContent.bzms = wrapper.popContent.bzms;
                var json = {
                    "list": {
                        "dbd": pop.dbdContent,
                        "dbdmx": this.jsonList
                    }
                };
                // 发送请求保存数据
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=modify',
                    JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        wrapper.jsonList = [];
                        malert("提交成功",'top','success');
						window.top.$("#调拨管理")[0].contentWindow.getData();
                        wrapper.topClosePage('page/hcgl/ejkf/kcgl/dbgl/kaidan/kaidan.html', 'page/hcgl/ejkf/kcgl/dbgl/dbgl.html');
                        

                    } else {
                        malert(data.c,'top','defeadted');
                    }
                    // 是否禁止提交
                    wrapper.isSubmited = false;
                }, function (error) {
                    // 是否禁止提交
                    wrapper.isSubmited = false;
                });
            },
            // 取消
            cancel: function () {
                this.topClosePage('page/hcgl/ejkf/kcgl/dbgl/kaidan/kaidan.html', 'page/hcgl/ejkf/kcgl/dbgl/dbgl.html');
            },
            // 编辑
            edit: function (index) {
                wrapper.isUpdate = 1;
                wrapper.modifyIndex = index;
                pop.popContent = JSON.parse(JSON.stringify(wrapper.jsonList[index]));
                pop.open();
                pop.title = "编辑材料";
                // if (wrapper.isNew == 0) {
                //     Vue.set(pop.dbdContent, 'zdrq', wrapper.dbd.zdrq);// 当前时间
                //     Vue.set(pop.dbdContent, 'zdr', wrapper.dbd.jbr);//经办人
                // }

            },
            // 删除2018/07/06二次弹窗删除提示
            remove: function (index) {
                if (common.openConfirm("确认删除该条信息吗？", function () {
                    wrapper.jsonList.splice(index, 1);
                })) {
                    return false;
                }
            }
        }

    });
var pop = new Vue(
    {
        el: '#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data: {
            title: '',
            dbdContent: {},
            popContent: {},
            ryList: [],
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            them_tran: {},
            them: {
                '生产批号': 'scph',
                '材料编号': 'ypbm',
                '材料名称': 'ypmc',
                '商品名': 'ypspm',
                '供应商': 'ghdwmc',
                '库存数量': 'kcsl',
                '二级库房单位': 'yfdwmc',
                '一级库房库存': 'ykkc',
                '库房单位': 'kfdwmc',
                '有效期至': 'yxqz',
                '规格': 'ypgg',
                '分装比例': 'fzbl',
                '进价': 'ykjj',
                '零价': 'yklj',
                '效期': 'yxqz',
                '材料剂型': 'jxmc'
            },

        },
        mounted:function(){
            this.getZdr()
        },
        methods: {
            getZdr:function(){
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm', function (data) {
                    pop.ryList = data.d.list;
                });
            },
            // 关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
                // 设置库房
            },
            // 材料名称下拉table检索数据
            changeDown: function (event, type) {
               this.keyCodeFunction(event, 'popContent', 'searchCon');
                // 选中之后的回调操作
                if (event.keyCode == 13) {
                    if (type == 'ypmc') {
                        if (this.popContent.ypmc == undefined) {
                            return;
                        }
                        $(".selectGroup").hide();
                        this.selSearch=-1;
                        this.nextFocus(event);
                    }else if (type == 'cksl') {
                        if (this.popContent.cksl > this.popContent.kcsl) {
                            malert('库存不足！','top','defeadted');
                            return;
                        } else {
                            this.add();
                        }
                    }

                }

            },
            // 当输入值后才触发
            change: function ( type,add,val) {
				this.popContent['ypmc']=val;
                if (!wrapper.popContent.yfbm ) {
                    malert("请先选择当前科室!", 'top', 'defeadted');
                    return;
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                    this.popContent.parm = this.popContent[type];
                if (!add) this.page.page = 1;
                var dg = {
                    page: this.page.page,
                    rows: 10,
                    parm: $.trim(this.popContent.parm)
                }
                var json = {
                    yfbm: wrapper.popContent.yfbm,
                    // "yfbm":'02',
                    ksbm: wrapper.ksbm,
                    sfpdkcsl: 1
                };
                $.getJSON(
                    '/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc' + '&dg=' + JSON.stringify(dg) + '&json=' + JSON.stringify(json),
                    function (data) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                            data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                            // 判断可用库存数量
                            data.d.list[i]['kykc'] = data.d.list[i]['kcsl'] - (data.d.list[i]['wshcks'] == undefined ? 0 : data.d.list[i]['wshcks']);
                        }
                        if (add) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                pop.searchCon.push(data.d.list[i]);
                            }
                        } else {
                            pop.searchCon = data.d.list;
                        }
                        pop.page.total = data.d.total;
                        pop.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            },

            // 双击选中下拉table
            selectOne: function (item) {
                // 查询下页
                if (item == null) {
                    this.page.page++;
                    this.change('ypmc',true)
                    return;
                }
                this.popContent = item;
                $(".selectGroup").hide();
            },
            // 新增
            add: function () {
                if (!wrapper.popContent.yfbm ) {
                    malert('请选择调出二级库房','top','defeadted');
                    return;
                }
                Vue.set(pop.dbdContent, 'yfbm', wrapper.popContent.yfbm);
                if (!wrapper.popContent.dbyf) {
                    malert('请选择调入二级库房','top','defeadted');
                    return;
                }
                Vue.set(pop.dbdContent, 'dbyf', wrapper.popContent.dbyf);
                if (wrapper.popContent.yfbm == wrapper.popContent.dbyf) {
                    malert('相同二级库房不能调拨','top','defeadted');
                    return;
                }
                if (!this.dbdContent.zdr) {
                    malert('请选择制单人','top','defeadted');
                    return;
                }
                if (this.popContent.cksl <= 0 || !this.popContent.cksl) {
                    malert('出库数量不正确','top','defeadted');
                    return;
                }
                if (this.popContent.cksl > this.popContent.kcsl) {
                    malert('出库数量大于库存数量！','top','defeadted');
                    return;
                }
                if (wrapper.isUpdate == 0) {
                    //新增
                    if (wrapper.jsonList) {
                        for (var i = 0; i < wrapper.jsonList.length; i++) {
                            if (wrapper.jsonList[i].ypbm === this.popContent.ypbm && wrapper.jsonList[i].xtph === this.popContent.xtph) {
                                malert('材料【' + this.popContent.ypmc + '】已存在，不能重复录入，请修改。','top','defeadted');
                                return;
                            }
                        }
                    }
                    wrapper.jsonList.push(this.popContent);
                    pop.popContent = {};
                    this.$refs.ypmc.focus()
                } else {
                    //修改
                    wrapper.$set(wrapper.jsonList, wrapper.modifyIndex, pop.popContent);
                    pop.closes();
                    pop.popContent = {};
                }
            },
        }
    });


// 监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

})
