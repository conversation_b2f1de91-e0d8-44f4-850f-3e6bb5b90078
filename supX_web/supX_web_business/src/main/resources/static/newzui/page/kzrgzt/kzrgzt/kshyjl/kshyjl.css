.text-indent-20{
text-indent: 20px;
}
.icon-position{
    top: 9px;
}
.kshyjl-width{
    background:url("/newzui/pub/image/kshyjl-bg.png") center no-repeat;
    background-size:540px 621px;
    width:540px;
    height:621px;
    position: absolute;
    top: calc((100vh - 621px) / 2);
    left: calc((100vw - 540px) / 2);
}
.kshyjl-btn{
    position: absolute;
    bottom: 22px;
    right: 15px;
}
.kshyjl-title{
    font-size: 24px;
    color:#1f2a34;
    width: 100%;
    padding: 25px 0 8px 0;
    display: flex;
    justify-content: center;
    font-weight: 600;
}
.kshyjl-close{
    position: absolute;
    right: 0;
    top: 10px;
    cursor: pointer;
}
.icon-font25:before{
    font-size: 25px;
}
.icon-cf08:hover:before{
    color: rgba(255,255,255,0.36);
}
.kshyjl-table{
    background:rgba(244,178,107,0.08);
    border:1px solid rgba(244,178,107,0.50);
    padding:5px 10px;
    box-sizing: border-box;
    margin: 0 auto;
    width: 485px;
}
.table-Department{
    display: flex;
    justify-content: flex-start;
    color:#354052;
    line-height: 18px;
}
.kshyjl-theme{
    height: 420px;
    right:30px;
    left: 30px;
    padding-top: 10px;
    position: absolute;
    display: flex;
    justify-content: flex-start;
    font-size:14px;
    color:#1f2a34;
    letter-spacing:0;
    flex-wrap: wrap;
    text-align:justify;
    line-height:20px;
}
.kshyjl-theme h2{
    font-size: 16px;
    font-weight: 600;
}


.sjys-width {
    width: 380px;
    height: 220px;
    position: absolute;
    top: calc((100vh - 220px) / 2);
    left: calc((100vw - 380px) / 2);
    z-index: 9999;
    background: #fff;
    box-shadow: 0 0 18px 0px rgba(0,0,0,0.5)
}
.sjys-top {
    width: 100%;
    height: 46px;
    background: #1abc9c;
    color: #fff;
    font-size: 16px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.sjys-content{
    width: 100%;
    float: left;
    padding: 43px 40px 0px 40px;
    box-sizing: border-box;
}
.sjys-pop-btn {
    width: 100%;
    padding: 60px 5px 0 0;
    display: flex;
    justify-content: flex-end;
}
.sjys-textarea{
    width: 100%;
    padding: 13px 15px 0;
    height: 90px;
    box-sizing: border-box;
    float: left;
}
.sjys-textarea textarea{
    width: 100%;
    height: 90px;
    padding: 10px;
    box-sizing: border-box;
    border:1px solid #dfe3e9;
    border-radius:4px;
    -webkit-appearance: none;
}
.sjys-pop-btn-t{
    padding: 33px 5px 0 0;
}
.dyjl{
    background: #fff;
}
.dyjl h2{
    width: 100%;
    font-size: 24px;
    font-weight: bold;
    text-align: center;

}
.font16{
    font-size: 16px !important;
}
.kshyjl-theme h3,.kshyjl-theme h4{
width: 100%;
}
.dyjl-center{
display: flex;
justify-content: center;
width: 100%;
flex-wrap: wrap;
}