var activeX = document.getElementById("csharpActiveX");
var left_tab1 = new Vue({
    el: '.left_tab1',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        zyh: '',
        popContent: {},
        csqxContent: {},
        billCode: null,
        bxlbbm: null,
        bxurl: null,
        which: 'hyjl',
        KsId: "",
        IsZyh: "",
        ksList: [],                         // 科室的list
        jsonList: [],         // 病人的list
        json: [],
        searchCon: [],
        selSearch: -1,
        text: "",
        page: {
            page: 1,
            rows: 50,
            total: null
        },
        totlePage: 0,
        param: {
            page: 1,
            rows: 50,
            total: null,
            parm: ""
        },
        userInfo: {},
    },
    created: function () {
        this.getbxlb();
        this.getKsData();
        this.getUserInfo();
    },
    updated: function () {
        changeWin()
    },
    methods: {

        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    left_tab1.userInfo = json.body.d;
                });
        },
        getbxlb: function () {
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        left_tab1.bxlbbm = json.d.list[0].bxlbbm;
                        left_tab1.bxurl = json.d.list[0].url;
                    }
                } else {
                    malert("保险类别查询失败!" + json.c, 'bottom', 'defeadted');
                }
            });
        },
        clear: function () {
            $(".ybdj").load("ybdj.html").fadeIn(300);
            left_tab1.brxxContent = {};
        },
        getKsData: function () {
            var bean = {"zyks": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    left_tab1.ksList = json.d.list;
                    left_tab1.popContent.ksbm = json.d.list[0].ksbm;
                    left_tab1.getData();
                } else {
                    malert('住院科室列表查询失败', 'bottom', 'defeadted');

                }
            });
        },
        // 选择科室
        ksChange: function (val) {
            Vue.set(left_tab1.popContent, 'ksbm', val[0]);
            Vue.set(left_tab1.popContent, 'ksmc', val[4]);
            left_tab1.getData();
        },
        // 获取病人的API
        getData: function () {
            if (!this.popContent.ksbm) {
                malert("请选择就诊科室！");
                return;
            }
            if (left_tab1.popContent.text && left_tab1.popContent.text.length == 10) {
                left_tab1.param.ykc010 = left_tab1.popContent.text;
            } else {
                left_tab1.param.ykc010 = null;
            }
            left_tab1.param.ykc011 = left_tab1.popContent.ksbm;
            left_tab1.param.parm = left_tab1.popContent.text ? left_tab1.popContent.text : "";
            left_tab1.param.mt = 1;
            left_tab1.jsonList = [];
            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=inhospital&method=queryList&parm="
                + JSON.stringify(left_tab1.param),
                function (json) {
                    if (json.a == '0') {
                        if (json.d) {
                            var res = eval('(' + json.d + ')');
                            left_tab1.totlePage = Math.ceil(res.total / left_tab1.param.rows);
                            left_tab1.jsonList = res.records;
                        } else {
                            left_tab1.totlePage = 1;
                            left_tab1.brxxContent = {};
                            left_tab1.clear();
                            malert("未查询到相关记录！", 'bottom', 'defeadted');
                        }
                    } else {
                        malert(json.c, 'bottom', 'defeadted');

                    }
                });
        },
        // 双击选中病人，获取病人信息
        detail: function (index) {
            common.openloading();
            let jsonObj = this.jsonList[index];
            let param = {
                ykc010: jsonObj.zyh,
                page: this.param.page,
                rows: this.param.rows,
            };
            left_tab1.zyh = null;
            let url = "/actionDispatcher.do?reqUrl=New1BxInterface&a=1&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=fyxx&method=getNotUploadRecordByPage&parm=";
            $.fn.getJSONAjax(url, param, function (res) {
                if (res.a != '0') {
                    malert(res.c, "top", "defeadted");
                } else {
                    left_tab1.zyh = jsonObj.zyh;
                    mtjs.jsonList = JSON.parse(res.d);
                }
            });
            common.closeLoading();
        },
        //回车
        changeDown: function (event) {
            left_tab1.getData();
        },
    }
});
var menu = new Vue({
    el: '.nh-menu',
    data: {
        which: 0,
        json: {},
        pageArr: ['mtjs', 'jsjl']
    },
    created: function () {
        this.loadCon('mtjs');
    },
    methods: {
        loadCon: function () {
            $(".page_div").hide();
            $("." + this.pageArr[this.which]).load(this.pageArr[this.which] + ".html").fadeIn(300);
        },
        tabBg: function (index) {
            this.which = index
        },
        doCheck: function (type) {
            this.json[type] = !parseInt(this.json[type]) ? 1 : 0;
            this.$forceUpdate()
        },
    },
});
