var content=new Vue({
    el:'#content',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data:{
        editText:false,
        tipsShow:false,
        tipsShow1:false,
        tipsShow2:false,
        userName:false,
        zt:'0',
        objabsolute:{},
        popContent:{},

    },
    mounted(){
        this.popContent.shsj =this.fDate(new Date(),'date')+' '+this.fDate(new Date(),'times');
        console.log(this.shsj);
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    content.popContent.shsj = value;
                } else {
                    content.popContent.shsj = '';
                }
            }
        });
    },
    methods:{
        rowsJ:function(event){
            if(event.keyCode==13){
                event.srcElement.rows=event.srcElement.rows+1
            }else if(event.keyCode==8){
                if(event.srcElement.rows!=1){
                    event.srcElement.rows=event.srcElement.rows-1
                }
            }
        },
        //取消返回上一级操作
        eidtCancel:function () {
            // this.editText=!this.editText
            this.topClosePage('page/kzrgzt/kzrgzt/kshyjl/hyjl.html','page/kzrgzt/kzrgzt/kshyjl/kshyjl.html');
        },
        //确定返回上一级操作
        editShow:function(){
            // this.editText=!this.editText
            this.topClosePage('page/kzrgzt/kzrgzt/kshyjl/hyjl.html','page/kzrgzt/kzrgzt/kshyjl/kshyjl.html');

        },
        hoverName:function (falg,index,event){
            this.objabsolute.left=event!=undefined?event.clientX-80:0
            this.objabsolute.top=event!=undefined?event.clientY+20:0
                this.userName= falg==true ? true:falg
            console.log(this.objabsolute)
            this.$forceUpdate()
        },
        //添加主持人
        addHost:function () {
            brzcList.title='添加主持人';
            brzcList.open();
        },
        //添加参与人
        addParticipants:function () {
            brzcList.title='添加参与人';
            brzcList.open();
        },
        addRecorder:function () {
            brzcList.title='添加记录者';
            brzcList.open();
        },
        //删除当前
        delImg:function (index) {
           malert('删除','top','success');

        }

    },
})
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat,scrollOps],
    data: {

        numOne: 1,
        title:'',
        popContent:{},
        XsList:[

            {
                bgxs:'本科室',
                ksbm:'01',
            },
            {
                bgxs:'外科',
                ksbm:'02',
            },
        ],

    },

    methods: {
        //关闭
        close: function () {
            this.numOne = 1;
        },
        //打开侧窗
        open:function () {
            this.numOne = 0;
        },
        //确定添加主任医生
        sideOk:function () {
            malert('添加成功','top','success')
        }
    },
});