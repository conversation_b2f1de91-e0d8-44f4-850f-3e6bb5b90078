<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>患者中心</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link href="./hzxx.css" rel="stylesheet">
    <script type="text/javascript">
        common.openloading()
    </script>
</head>

<body class="body padd-b-10 padd-l-10 padd-t-10 padd-r-10 skin-default height flex-container flex-dir-c flex-one">
    <div class="header-item flex-container flex-dir-c flex-one">
        <header class="userNameBg printHide" v-cloak>
            <div class="flex">
                <!-- <div class="userNameImg">
                    <img v-if="Brxx_List.nljd==1" src="/newzui/pub/image/maleBaby.png">
                    <img v-if="Brxx_List.nljd==2" src="/newzui/pub/image/femalebaby.png">
                    <img v-if="Brxx_List.nljd==3" src="/newzui/pub/image/Group <EMAIL>">
                    <img v-if="Brxx_List.nljd==4" src="/newzui/pub/image/Group <EMAIL>">
                    <img v-if="Brxx_List.nljd==5" src="/newzui/pub/image/juvenile.png">
                    <img v-if="Brxx_List.nljd==6" src="/newzui/pub/image/maid.png">
                    <img v-if="Brxx_List.nljd==7" src="/newzui/pub/image/youth.png">
                    <img v-if="Brxx_List.nljd==8" src="/newzui/pub/image/woman.png">
                    <img v-if="Brxx_List.nljd==9" src="/newzui/pub/image/grandpa.png">
                    <img v-if="Brxx_List.nljd==10" src="/newzui/pub/image/grandma.png">
                    <img v-if="Brxx_List.nljd==11" src="/newzui/pub/image/<EMAIL>">
                </div> -->
                <div class="text-color">
                    <p class="userHeader userCwh padd-l-30">
                        <span class="userName">{{Brxx_List.brxm}}</span>
                        <span class="sex text">{{Brxx_List.brxb}}</span>
                        <span class="nl text">{{Brxx_List.nl}}{{Brxx_List.nldw}}</span>

                        <span class="cwh text">床位号：{{Brxx_List.rycwbh}}号</span>
                        <span class="zyh text">住院号：{{Brxx_List.zyh}}</span>
                        <span class="bq text">费用：{{Brxx_List.fyhj}}元</span>
                        <span class="ks text">住院医生：{{Brxx_List.zyysxm}}</span>
                        <span class="ks text">入院日期：<span v-text="fDate(Brxx_List.ryrq,'')"></span></span>
                        <span class="ks text">身份证号码：<span v-text="Brxx_List.sfzjhm"></span></span>
                        <span class="ks text">接入日期：<span v-text="fDate(Brxx_List.jrrq,'')"></span></span>
                    </p>
                    <!-- <div class="userCwh">
                        <span class="cwh text">床位号：{{Brxx_List.rycwbh}}号</span>
                        <span class="zyh text">住院号：{{Brxx_List.zyh}}</span>
                        <span class="bq text">费用：{{Brxx_List.fyhj}}元</span>
                        <span class="ks text">住院医生：{{Brxx_List.zyysxm}}</span>
                        <span class="ks text">入院日期：{{Brxx_List.ryrq|formDate}}</span>
                    </div> -->
                    <!-- <div>
                        <p class="heaf text">更多详细信息>></p>
                    </div> -->
                </div>
            </div>
            <div class="blRight">
                <span class="dyblImg imgHover" v-if="num==0||num==10" onclick="tabBg('userPage/wzbrqj',10,this,10)"></span>
                <span class="blImg imgHover" v-if="num==0 || num==3"></span>
                <!--<span class="xyzImg imgHover" v-if="num==0 || num==1" onclick="tabBg('userPage/yzgl',1,this,1)"></span>-->
                <span class="fzImg imgHover" v-if="num==0 || num==4" onclick="newPage()"></span>
                <span class="dy-bgImg imgHover" v-if="num==11">
                    <i class="dy-img"></i>
                </span>
            </div>
            <div  class="bg-fiexd printHide" @click="qtflj"></div>
        </header>
        <div class="content flex-container flex-dir-c flex-one" :id="setH">
            <div class="fyxm-tab printHide">
                <div><span :class="{'active':num==1}" onclick="tabBg('userPage/yzgl',1,this)">医嘱管理</span></div>
<!--                <div><span :class="{'active':num==2}" onclick="tabBg('userPage/jcjy',2,this)">检查检验</span></div>-->
<!--                &lt;!&ndash;<div><span :class="{'active':num==3}" onclick="tabBg('userPage/dzbl',3,this)">电子病历</span></div>&ndash;&gt;-->
<!--                <div><span :class="{'active':num==4}" onclick="tabBg('userPage/lclj',4,this)">临床路径</span></div>-->
<!--                &lt;!&ndash;<div><span :class="{'active':num==5}" onclick="tabBg('userPage/hlws',5,this)">护理文书</span></div>&ndash;&gt;-->
<!--                <div><span :class="{'active':num==6}" onclick="tabBg('userPage/fyqd',6,this)">费用清单</span></div>-->
<!--                <div><span :class="{'active':num==7}" onclick="tabBg('userPage/twd',7,this)">体温单</span></div>-->
<!--                &lt;!&ndash;<div><span :class="{'active':num==8}" @click="tabBg(8)">手术管理</span></div>&ndash;&gt;-->
<!--                <div><span :class="{'active':num==9}" onclick="tabBg('userPage/sxyp',9,this)">受限药品</span></div>-->
<!--                <div><span :class="{'active':num==10}" onclick="tabBg('userPage/wzbrqj',10,this)">危重病人抢救</span></div>-->
<!--                <div><span :class="{'active':num==11}" onclick="tabBg('userPage/sssq',11,this)">手术申请</span></div>-->
<!--                <div><span :class="{'active':num==12}" @click="topNew()">会诊申请</span></div>-->
                <!--<div><span :class="{'active':num==11}"  onclick="tabBg('userPage/sjyscf',11,this)">三级医师查房</span></div>-->
            </div>
            <div class="loadPage wrapper col-x-12 " style="border-top: none">

            </div>
        </div>
        <!--<div class="pop" v-if="ishow">-->
        <!--<div class="pophide " :class="{'show':ishow}"></div>-->
        <!--<div class="zui-form podrag pop-width bcsz-layer padd-b-15" :class="{'show':ishow}">-->
        <!--<div class="layui-layer-title text-left">{{ljbmtext}}</div>-->
        <!--<span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close" @click="close"></a></span>-->
        <!--<div class="layui-layer-content">-->
        <!--<div class=" layui-mad layui-height flex-container flex-jus-c " :class="{'flex-align-c':sfrj}">-->
        <!--<div v-if="sfrj"> 该患者符合<span class="color-f2a654">ICD-10</span>疾病编码，是否入径？</div>-->
        <!--<div v-else class="flex-container  flex-jus-c margin-b-20">-->
        <!--<p class="whiteSpace margin-r-5 ft-14">原因</p>-->
        <!--<textarea placeholder="请输入原因" v-model="popModel"  type="text" class="zui-input sdrj-pop"></textarea>-->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="zui-row buttonbox" key="a" v-if="sfrj">-->
        <!--<button class="zui-btn xmzb-db table_db_esc margin-r-10 btn-default" @click="flagIS">否</button>-->
        <!--<button class="zui-btn xmzb-db btn-primary table_db_save" @click="ishow=false" onclick="tabBg('userPage/lclj',4,this)">是</button>-->
        <!--</div>-->
        <!--<div class="zui-row buttonbox" key="b" v-else>-->
        <!--<button class="zui-btn xmzb-db table_db_esc margin-r-10 btn-default" @click="ishow=false">取消</button>-->
        <!--<button class="zui-btn xmzb-db btn-primary table_db_save" @click="Wf_save">确定</button>-->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->

        <div class="poplj">
            <div class="pophide" :class="{'show':isShow}"></div>
            <div class=" podrag height-500 pop-850  bcsz-layer padd-b-15" :class="{'show':isShow}">
                <div class="layui-layer-title ">新增分路径</div>
                <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close" @click="isShow=false"></a></span>
                <div class="layui-layer-content">
                    <div class=" layui-mad layui-height padd-l-20 flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">使用科室</label>
                            <select-input @change-data="resultChange" :not_empty="false" :child="yzlx_tran01" :index="popContent.yzxx"
                                :val="popContent.yzxx" :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">&emsp;主路径</label>
                            <select-input @change-data="resultChange" :not_empty="false" :child="yzlx_tran01" :index="popContent.yzxx"
                                :val="popContent.yzxx" :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">使用模板</label>
                            <select-input @change-data="resultChange" :not_empty="false" :child="yzlx_tran01" :index="popContent.yzxx"
                                :val="popContent.yzxx" :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">路径编码</label>
                            <select-input @change-data="resultChange" :not_empty="false" :child="yzlx_tran01" :index="popContent.yzxx"
                                :val="popContent.yzxx" :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">路径名称</label>
                            <div class="zui-input-inline">
                                <input class="zui-input " v-model="popContent.yplx" placeholder="请输入路径名称" @keydown="nextFocus($event)">
                            </div>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">病例分型</label>
                            <select-input @change-data="resultChange" :not_empty="false" :child="yzlx_tran01" :index="popContent.yzxx"
                                :val="popContent.yzxx" :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">适用病情</label>
                            <select-input @change-data="resultChange" :not_empty="false" :child="yzlx_tran01" :index="popContent.yzxx"
                                :val="popContent.yzxx" :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">适用性别</label>
                            <select-input @change-data="resultChange" :not_empty="false" :child="yzlx_tran01" :index="popContent.yzxx"
                                :val="popContent.yzxx" :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">&emsp;&emsp;分类</label>
                            <select-input @change-data="resultChange" :not_empty="false" :child="yzlx_tran01" :index="popContent.yzxx"
                                :val="popContent.yzxx" :name="'popContent.yzxx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">标准天数</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" v-model="popContent.yplx" placeholder="请输入标准天数" @keydown="nextFocus($event)">
                                <span class="cm">天</span>
                            </div>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">适用对象</label>
                            <div class="zui-input-inline">
                                <input class="zui-input " v-model="popContent.yplx" placeholder="请输入适用对象" @keydown="nextFocus($event)">
                            </div>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace  margin-r-5 ft-14">参&emsp;&emsp;考<br />费用标准</label>
                            <div class="zui-input-inline flex-container flex-align-c flex-jus-c">
                                <input class="zui-input" v-model="popContent.yplx" @keydown="nextFocus($event)">
                                <span class="padd-l-10 padd-r-10 color-green">至</span>
                                <input class="zui-input" v-model="popContent.yplx" @keydown="nextFocus($event)">
                            </div>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">启用时间</label>
                            <div class="zui-input-inline zui-date">
                                <i class="datenox icon-rl"></i>
                                <input class="zui-input " id="time" readonly="readonly" @keydown="nextFocus($event)">
                            </div>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                            <label class="whiteSpace margin-r-5 ft-14">拼音代码</label>
                            <div class="zui-input-inline">
                                <input class="zui-input " v-model="popContent.yplx" disabled @keydown="nextFocus($event)">
                            </div>
                        </div>
                        <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4" style="height: 36px">
                            <label class="whiteSpace margin-r-5 ft-14">启用状态</label>
                            <div class="zui-input-inline" id="jyxm_icon" style="height: 36px">
                                <div class="switch" style="left:8%;top: 22%;">
                                    <input type="checkbox" checked />
                                    <label></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class=" ksys-btn flex-container flex-align-c flex-jus-e">
                    <button v-waves class="zui-btn xmzb-db table_db_esc btn-default" @click="isShow=false">取消</button>
                    <button v-waves class="zui-btn xmzb-db btn-primary table_db_save" @click="Wf_save">确定</button>
                </div>
            </div>
        </div>

        <div class="side-form printHide  pop-width" :class="{'ng-hide':index==0}" v-cloak ref="brzcList" id="brzcListUser" role="form">
                <div class="fyxm-side-top flex-between">
                    <span>患者信息</span>
                    <span class="fr closex ti-close" @click="index=0"></span>
                </div>
                <div class="ksys-side">
                    <ul class="tab-edit-list1 useritem flex-start over-auto">
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e  flex-align-c">住院号</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.zyh" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">病人姓名</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.brxm" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">性别</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.brxb" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">年龄</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.nl" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">家庭住址</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.jzdmc" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">联系电话</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.lxdh" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">入院费别</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.brfbmc" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">入院时间</span>
                            <input class="userValue flex-container  flex-align-c" id="rysj" disabled="disabled" :value="fDate(popContent.ryrq,'yyyy-MM-dd')">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">出院时间</span>
                            <input class="userValue flex-container  flex-align-c" id="cysj" disabled="disabled" :value="fDate(popContent.cysj,'yyyy-MM-dd')">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">住院天数</span>
                            <input class="userValue flex-container  flex-align-c" :value="popContent.zyts" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">诊断编码</span>
                            <!--<input class="userValue flex-container  flex-align-c" v-model="brxxContent.ryzdbm">-->
                            <input class="userValue flex-container  flex-align-c" data-notEmpty="true" :value="jbbmContent.jbbm" @keydown="changeDown($event,'jbbm','jbbmContent','jbsearchCon')"
                                   @input="change(false,'jbbm',$event.target.value)">
                            <jbsearch-table :message="jbsearchCon" :selected="selSearch"
                                            :them="jbthem" :page="page"
                                            @click-one="checkedOneOut" @click-two="selectJbbm">
                            </jbsearch-table>
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">诊断名称</span>
                            <input class="userValue flex-container flex-align-c" v-model="popContent.ryzdmc">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">担保人</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.dbr" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">担保金额</span>
                            <input class="userValue flex-container  flex-align-c" v-model="popContent.dbje" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">护理等级</span>
                            <input class="userValue flex-container  flex-align-c" v-model="hldj_tran[popContent.hldj]" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">费用合计</span>
                            <input class="userValue flex-container  flex-align-c font-14-654" v-model="popContent.fyhj" disabled="disabled">
                        </li>
                        <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">预交合计</span>
                            <input class="userValue flex-container  flex-align-c font-14-654" v-model="popContent.yjhj" disabled="disabled">
                        </li>
                        <li class="userlist flex-container xunhuanlist" :class="{'userlength':item==xhitem}" v-for="item in xhitem"><span
                                class="userKey flex-container  flex-align-c"></span><span class="userValue flex-container flex-jus-c flex-align-c"></span></li>
                    </ul>
                </div>
            <div class="ksys-btn" style="position: absolute">
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="save">确认</button>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="hzxx.js"></script>
    <script type="text/javascript" src="/newzui/pub/PassJs/McLoader.js"></script>
</body>

</html>
