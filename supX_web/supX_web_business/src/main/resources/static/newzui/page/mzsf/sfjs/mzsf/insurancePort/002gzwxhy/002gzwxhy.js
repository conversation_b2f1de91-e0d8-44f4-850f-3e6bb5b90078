var activeX = document.getElementById("csharpActiveX");

function CreatXmlDoc(obj){
	this.tagName=obj.tagName;
	var children=obj.children.map(function(item){
		if(typeof item =="object")
		{
			item=new CreatXmlDoc(item)
		}
		return item
	})
	this.children=children;
}


CreatXmlDoc.prototype.render=function(){
	var el=document.createElement(this.tagName);
	var children=this.children || [];
	children.forEach(function(child){
		var childEl=(child instanceof CreatXmlDoc)
			? child.render()
			:document.createTextNode(child)
		el.appendChild(childEl);
	})
	return el
}
var gz_002 = new Vue({
	el: '.gz_002',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data: {
		isShow: false,
		bxlbbm: null,
		bxurl: null,
		birthday: null,
		text: null,
		jbContent: {},
		searchCon: [],
		selSearch: -1,
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		them_tran: {},
		them: {
			'疾病编码': 'yke120',
			'疾病名称': 'yke121',
			'副编码': 'yke223'
		},
		brzt_tran:{
			'1':'在院',
			'2':'未在院'
		},
		zdxxJson:{
			prm_aka130 : "11",
		},
		grxxJson:{},
		sfsbContent:{},
		dzpzContent:{},
		cssg:false,
		userInfo:{},
		ifclick:true,
	},
	mounted: function () {
		this.isShow = true;
		this.init();
	},
	methods: {
		init: function () {
			$.post("http://localhost:9089/init", {}, function (json) {
				if (json.aint_appcode > 0) {
					rightVue.gzyhybInit = true;
					malert("初始化成功!");
				} else {
					malert("医保控件初始化失败！请从新打开页面!");
				}
			});
		},

		closeGz_002: function () {
			this.isShow = false;
			$("#hyjl").html("");
		},
		getUserInfo: function () {
			this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
				.then(function (json) {
					this.userInfo = json.body.d;
				});
		},
		getbxlb: function () {
			var param = {bxjk: "002"};
			$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
				+ JSON.stringify(param), function (json) {
				if (json.a == 0) {
					if (json.d.list.length > 0) {
						gz_002.bxlbbm = json.d.list[0].bxlbbm;
						gz_002.bxurl = json.d.list[0].url;
					}
				} else {
					malert("保险类别查询失败!" + json.c,'top','defeadted')
				}
			});
		},
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'jbContent', 'searchCon');
			if (event.code == 'Enter' || event.code == 13) {
				if (type == "text") {
					Vue.set(this.jbContent, 'jbmc', this.jbContent['yke121']);
					gz_002.zdxxJson.jbbm = this.jbContent.yke120;
					gz_002.zdxxJson.jbmc = this.jbContent.yke121;
					this.selSearch=0;
					this.nextFocus(event);
				}
			}
		},

		searching: function (add, type,val) {
			this.jbContent['jbmc']=val;
			if (!add) this.page.page = 1;
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			if (this.jbContent[type] == undefined || this.jbContent[type] == null) {
				this.page.parm = "";
			} else {
				this.page.parm = this.jbContent[type];
			}
			var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=ICD10&method=query&parm="
				+ JSON.stringify(str_param),
				function (json) {
					if (json.a == 0) {
						var date = null;
						var res = eval('(' + json.d + ')');
						if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
							for (var i = 0; i < res.list.length; i++) {
								gz_002.searchCon.push(res.list[i]);
							}
						} else {
							gz_002.searchCon = res.list;
						}
						gz_002.page.total = res.total;
						gz_002.selSearch = 0;
						if (res.list.length > 0 && !add) {
							$(".selectGroup").hide();
							_searchEvent.show();
						}
					} else {
						malert("查询失败  " + json.c,'top','defeadted');
					}
				});
		},
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;               // 设置当前页号
				this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
			} else {   // 否则就是选中事件,为json赋值
				this.jbContent = item;
				Vue.set(this.jbContent, 'jbmc', this.jbContent['yke121']);
				gz_002.zdxxJson.jbbm = this.jbContent.yke120;
				gz_002.zdxxJson.jbmc = this.jbContent.yke121;
				$(".selectGroup").hide();
			}
		},
		//读卡
		load:function(){
			if(!gz_002.ifclick){
				malert("请勿重复点击！","top","defeadted");
				return;
			}
			gz_002.ifclick = false;
			if(rightVue.gzyhybInit){
				var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><prm_payoptype>02</prm_payoptype ></input>';
				$.post("http://localhost:9089/call", {
					jybh:"03",
					jysr_xml:jysr
				}, function (json) {
					gz_002.ifclick = true;
					if (json.aint_appcode > 0) {
						gz_002.sfsbContent = JSON.parse(json.astr_jysc_xml);
						gz_002.grxxJson = gz_002.sfsbContent;
                       var prm_yae921=typeof gz_002.grxxJson.prm_yae921=='string'?gz_002.grxxJson.prm_yae921:'';
                        rightVue.gzyhybContent.prm_yae921=prm_yae921;
						malert("读卡成功!");
					} else {
						malert(json.astr_appmasg);
					}
				});
			}else{
				gz_002.ifclick = true;
				malert("医保控件未初始化,请重新打开页面！",'top','defeadted');
			}
		},

		//引入
		enter:function(){
			if (Object.keys(gz_002.grxxJson).length === 0) {
				malert("请先读卡","top","defeadted");
				return;
			}
			if (gz_002.zdxxJson.prm_aka130 == null || gz_002.zdxxJson.prm_aka130 === '' || gz_002.zdxxJson.prm_aka130 === undefined) {
				malert("请选择支付类型","top","defeadted");
				return;
			}
			if(gz_002.grxxJson.prm_ykc023 == '1'){
				malert('病人再院，无法办理医保入院，请办理自费入院','right','defeadted')
				return;
			}
			if(rightVue.fzContent.brxm != gz_002.grxxJson.prm_aac003 ){
				malert("医保报销，禁止使用非本人医保卡，请核对后再试！","top","defeadted");
				return;
			}
			//个人信息
			rightVue.gzyhybContent = gz_002.grxxJson;
			//门诊诊断信息
			rightVue.gzyhybContent.jbbm = this.zdxxJson.jbbm;
			//支付类别
			rightVue.gzyhybContent.prm_aka130 = this.zdxxJson.prm_aka130;
			//备注
			rightVue.gzyhybContent.bzsm = this.zdxxJson.bzsm;
			//个人编号,用于结算各种判断
			rightVue.gzyhybContent.grbh = gz_002.grxxJson.prm_aac001;

			//根据个人编号查询是否已经进行过登记，如果是 则 使用其中的就诊编号（用于同一个人二次结算）
			var str_param = {
				id : rightVue.brxxContent.ghxh,
				prmAac001 : gz_002.grxxJson.prm_aac001
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=queryMzdjxxByGrbh&parm="
				+ JSON.stringify(str_param),
				function (json) {
					if (json.a == 0) {
						if(json.d){
							rightVue.gzyhybContent.prm_akc190 = json.d.prmAkc190;//就诊编号赋值
						}
						popTable.isShow = false;
						malert("引入成功！");
					}else{
						malert("引入失败！");
					}
				});

		},

		smzfqr:function(){
			var fylist=[];
			var brfyList=[];
			var fyze = 0.00;
			for (var i=0;i<rightVue.brfyjsonList.length;i++) {
				var fyparam = {};
				fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
				fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
				fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
				fyparam.fysl = rightVue.brfyjsonList[i].fysl;
				brfyList.push(fyparam);
				fyze+=rightVue.brfyjsonList[i].fyje;
			}
			var param = {
				fylist: brfyList
			};
			this.updatedAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=queryMzfy&parm=" + JSON.stringify(param), function (json) {
				if(json.a == '0'){
					yhybBrfyList = eval('(' + json.d + ')');
					for (var i = 0; i < yhybBrfyList.length; i++) {
						fylist.push(JSON.parse(yhybBrfyList[i]));
					}
				}else{
					malert(json.c,'right','defeadted');
					return false;
				}
			});
			if(fylist==null || fylist==undefined || fylist=="" || fylist.length<=0){
				malert("没有可结算费用！",'right','defeadted');
				return false;
			}
			fyze = fyze.toFixed(2);
			var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var prm_yae921="<prm_yae921>"+gz_002.grxxJson.prm_yae921+"</prm_yae921>";
			var prm_aac001="<prm_aac001>"+gz_002.grxxJson.prm_aac001+"</prm_aac001>";
			var prm_count="<prm_count>"+1+"</prm_count>";
			var prm_hisfyze="<prm_hisfyze>"+fyze+"</prm_hisfyze>";
			var prm_misecond="<prm_misecond>"+60000+"</prm_misecond>";
			jysr=jysr+prm_yae921+prm_aac001+prm_count+prm_hisfyze+prm_hisfyze+prm_misecond+"</input>";
			$.post("http://localhost:9089/call", {
				jybh:"48f",
				jysr_xml:jysr
			}, function (json) {
				gz_002.ifclick = true;
				if (json.aint_appcode > 0) {
					gz_002.dzpzContent = JSON.parse(json.astr_jysc_xml);

					malert("调用电子凭证成功!");
				} else {
					malert(json.astr_appmasg);
				}
			});
		},

		//门诊预结算方法
		mzyjs:function(){
			var result = "0";
			var mzyssfzh = "";
			//同步操作
			$.ajaxSettings.async = false;
			//取医生身份证号
            var str_param = {
                parm: rightVue.brfyjsonList[0]['mzys'],
                page: 1,
                rows: 10
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=query&dg=" + JSON.stringify(str_param), function(json) {
                if (json.a == 0) {
                        if (json.d.list.length > 0){
                            mzyssfzh = json.d.list[0].sfzhm;
                        }
                } else {
                    malert("查询失败  " + json.c, 'top', 'defeadted');
                    return;
                }
            });
			//处理费用
			var yhybBrfyList = [];
			var fylist=[];
			var brfyList=[];
			var fyze = 0.00;
			for (var i=0;i<rightVue.brfyjsonList.length;i++) {
				var fyparam = {};
				fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
				fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
				fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
				fyparam.fysl = rightVue.brfyjsonList[i].fysl;
				brfyList.push(fyparam);
				fyze+=rightVue.brfyjsonList[i].fyje;
			}
			var param = {
				fylist: brfyList
			};
			this.updatedAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=queryMzfy&parm=" + JSON.stringify(param), function (json) {
					if(json.a == '0'){
						yhybBrfyList = eval('(' + json.d + ')');
						for (var i = 0; i < yhybBrfyList.length; i++) {
							fylist.push(JSON.parse(yhybBrfyList[i]));
						}
					}else{
						malert(json.c,'right','defeadted');
						return false;
					}
				});
			if(fylist==null || fylist==undefined || fylist=="" || fylist.length<=0){
				malert("没有可结算费用！",'right','defeadted');
				return false;
			}

			var rowlist=[];
			for(var i=0;i<fylist.length;i++){
			if(fylist[i] && fylist[i].yka002    
					// 过滤金额为0的项目
					&& fylist[i].yka315 && fylist[i].yka315 != 0 && fylist[i].yka315 != '0'){
				var taglist=[];
				var obj1={
					tagName:'yka105',
					children:[(new Date()).getTime()+i]
				};
				taglist.push(obj1);
				var obj2={
					tagName:'ykd125',
					children:[fylist[i].ykd125]
				};
				taglist.push(obj2);
				var obj3={
					tagName:'ykd126',
					children:[fylist[i].ykd126]
				};
				taglist.push(obj3);
				var obj4={
					tagName:'yka002',
					children:[fylist[i].yka002]
				};
				taglist.push(obj4);
				var obj5={
					tagName:'yka003',
					children:[fylist[i].yka003]
				};
				taglist.push(obj5);
				var obj6={
					tagName:'akc226',
					children:[fylist[i].akc226]
				};
				taglist.push(obj6);
				var obj7={
					tagName:'akc225',
					children:[fylist[i].akc225]
				};
				taglist.push(obj7);
				var obj8={
					tagName:'yka315',
					children:[fylist[i].yka315]
				};
				taglist.push(obj8);
				var obj9={
					tagName:'yka097',
					children:[rightVue.brfyjsonList[0]['mzks']]
				};
				taglist.push(obj9);
				var obj10={
					tagName:'yka098',
					children:[rightVue.brfyjsonList[0]['mzksmc']]
				};
				taglist.push(obj10);
				var obj11={
					tagName:'ykd102',
					children:[mzyssfzh]
				};
				taglist.push(obj11);
				var obj12={
					tagName:'yka099',
					children:[rightVue.brfyjsonList[0]['mzysxm']]
				};
				taglist.push(obj12);
				var obj13={
					tagName:'yka100',
					children:[rightVue.brfyjsonList[0]['mzks']]
				};
				taglist.push(obj13);
				var obj14={
					tagName:'yka101',
					children:[rightVue.brfyjsonList[0]['mzksmc']]
				};
				taglist.push(obj14);
				var obj15={
					tagName:'ykd106',
					children:[mzyssfzh]
				};
				taglist.push(obj15);
				var obj16={
					tagName:'yka102',
					children:[rightVue.brfyjsonList[0]['mzysxm']]
				};
				taglist.push(obj16);
				var obj17={
					tagName:'yke123',
					children:[gz_002.fDate(new Date(),'date')]
				};
				taglist.push(obj17);
				var obj18={
					tagName:'ykc141',
					children:[userId]
				};
				taglist.push(obj18);
				var obj19={
					tagName:'aae036',
					children:[gz_002.fDate(new Date(),'date')]
				};
				taglist.push(obj19);
				var obj20={
					tagName:'aae013',
					children:['']
				};
				taglist.push(obj20);
				var obj21={
					tagName:'yke201',
					children:['']
				};
				taglist.push(obj21);
				var obj22={
					tagName:'yka295',
					children:['']
				};
				taglist.push(obj22);
				var obj23={
					tagName:'aka074',
					children:['']
				};
				taglist.push(obj23);
				var obj24={
					tagName:'aka070',
					children:['']
				};
				taglist.push(obj24);
				var obj25={
					tagName:'yae374',
					children:['']
				};
				taglist.push(obj25);
				var obj26={
					tagName:'yke009',
					children:['']
				};
				taglist.push(obj26);
				var obj27={
					tagName:'yke186',
					children:['']
				};
				taglist.push(obj27);
				var row={
					tagName:'row',
					children:taglist
				}
				rowlist.push(row);
			}else{
				malert("第【" + (i+1) + "】行，【"+ fylist[i].ykd126 +"】未对码，请先对码！","top","defeadted");
				return false;
			}
			}
			fyze = fyze.toFixed(2);
			var obj={
				tagName:'dataset',
				children:rowlist
			};
            var o =JSON.stringify(obj);
            var reg=new RegExp("null","g");
            obj =JSON.parse(o.replace(reg,''));

			doc=new CreatXmlDoc(obj);
			SetupSerial=(new XMLSerializer()).serializeToString(doc.render());
			var reg = new RegExp(' xmlns="http://www.w3.org/1999/xhtml"',"g");
			SetupSerial=SetupSerial.replace(reg,"");
			var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var prm_akc190="<prm_akc190>"+rightVue.gzyhybContent.prm_akc190+"</prm_akc190>";
			var prm_aac001="<prm_aac001>"+rightVue.gzyhybContent.prm_aac001+"</prm_aac001>";
			var prm_ykc173="<prm_ykc173>"+(rightVue.mzjbxxContent.jbmc?rightVue.mzjbxxContent.jbmc:'-')+"</prm_ykc173>";
			var prm_hisfyze="<prm_hisfyze>"+fyze+"</prm_hisfyze>";
			var prm_aka130="<prm_aka130>"+rightVue.gzyhybContent.prm_aka130+"</prm_aka130>";
			var prm_yka110="<prm_yka110></prm_yka110>";
			var prm_aae013="<prm_aae013></prm_aae013>";
			var prm_aae011="<prm_aae011>"+userId+"</prm_aae011>";
			var prm_ykc141="<prm_ykc141>"+ fylist[0].ykc141 +"</prm_ykc141>";
			var prm_ykb065="<prm_ykb065>"+rightVue.gzyhybContent.prm_ykb065+"</prm_ykb065>";

			jysr=jysr+prm_akc190+prm_aac001+prm_ykc173+prm_hisfyze+prm_aka130+prm_yka110+prm_aae013+
				prm_aae011+prm_ykc141+prm_ykb065+SetupSerial+"</input>";
			//去掉所有不合法字符
			jysr = jysr.replace(/undefined/g,"");
			jysr = jysr.replace(/NaN/g,"");
			jysr = jysr.replace(/null/g,"");
			//调用结算方法
			$.post("http://localhost:9089/call",{
				'jybh':'48',
				'jysr_xml':jysr
			},function (json) {
				if (json.aint_appcode > 0) {
					rightVue.yjsContentGzyhyb = JSON.parse(json.astr_jysc_xml);
					rightVue.jylsh = json.astr_jylsh;
					rightVue.jyyzm = json.astr_jjyyzm;
				}else {
					malert(json.astr_appmasg,"top","defeadted");
					result = "1";
				}
			});
			//结算成功后，进行HIS门诊登记
			//这里转换一次，将字段下划线去掉，并修改为驼峰命名
			var enterContent = {
				id : rightVue.brxxContent.ghxh,//取挂号序号
				prmAac001 : typeof gz_002.grxxJson.prm_aac001 == 'string'?gz_002.grxxJson.prm_aac001:'',//个人编号 NOT NULL VARCHAR2(15)
				prmAkc021 : typeof gz_002.grxxJson.prm_akc021 == 'string'?gz_002.grxxJson.prm_akc021:'',//医疗人员类别 NOT NULL VARCHAR2(6) 见代码表
				prmYkc120 : typeof gz_002.grxxJson.prm_ykc120 == 'string'?gz_002.grxxJson.prm_ykc120:'',//公务员级别 NOT NULL VARCHAR2(6) 见代码表
				prmYab139 : typeof gz_002.grxxJson.prm_yab139 == 'string'?gz_002.grxxJson.prm_yab139:'',//参保所属分中心 NOT NULL VARCHAR2(6) 见代码表
				prmYkb065 : typeof gz_002.grxxJson.prm_ykb065 == 'string'?gz_002.grxxJson.prm_ykb065:'',//执行社会保险办法 NOT NULL VARCHAR2(6) 见代码表
				prmYkc150 : typeof gz_002.grxxJson.prm_ykc150 == 'string'?gz_002.grxxJson.prm_ykc150:'',//异地安置标志 NOT NULL VARCHAR2(6) 见代码表
				prmAka130 : typeof gz_002.zdxxJson.prm_aka130 == 'string'?gz_002.zdxxJson.prm_aka130:'',//支付类型
				prmAac003 : typeof gz_002.grxxJson.prm_aac003 == 'string'?gz_002.grxxJson.prm_aac003:'',//姓名 NULL VARCHAR2(20)
				prmAac004 : typeof gz_002.grxxJson.prm_aac004 == 'string'?gz_002.grxxJson.prm_aac004:'',//性别 NULL VARCHAR2(6) 见代码表
				prmAac002 : typeof gz_002.grxxJson.prm_aac002 == 'string'?gz_002.grxxJson.prm_aac002:'',//公民身份号码 NULL VARCHAR2(18)
				prmAac006 : typeof gz_002.grxxJson.prm_aac006 == 'string'?gz_002.grxxJson.prm_aac006:'',//出生日期 NULL DATETIME
				prmAkc023 : typeof gz_002.grxxJson.prm_akc023 == 'string'?gz_002.grxxJson.prm_akc023:0,//实足年龄 NULL NUMBER(3,0)
				prmAab001 : typeof gz_002.grxxJson.prm_aab001 == 'string'?gz_002.grxxJson.prm_aab001:'',//单位编号 NOT NULL VARCHAR2(15)
				prmAab004 : typeof gz_002.grxxJson.prm_aab004 == 'string'?gz_002.grxxJson.prm_aab004:'',//单位名称 NULL VARCHAR2(50)
				prmAac031 : typeof gz_002.grxxJson.prm_aac031 == 'string'?gz_002.grxxJson.prm_aac031:'',//个人参保状态 NOT NULL VARCHAR2(6) 见代码表
				prmAkc087 : typeof gz_002.grxxJson.prm_akc087 == 'string'?gz_002.grxxJson.prm_akc087:0,//个人账户余额 NOT NULL NUMBER(14,2)
				prmYab003 : typeof gz_002.grxxJson.prm_yab003 == 'string'?gz_002.grxxJson.prm_yab003:'',//分中心编号 NOT NULL VARCHAR2(6) 见代码表
				prmYkc280 : typeof gz_002.grxxJson.prm_ykc280 == 'string'?gz_002.grxxJson.prm_ykc280:'',//居民医疗人员类别 NULL VARCHAR2(6) 见代码表
				prmYkc281 : typeof gz_002.grxxJson.prm_ykc281 == 'string'?gz_002.grxxJson.prm_ykc281:'',//居民医疗人员身份 NULL VARCHAR2(6) 见代码表
				prmYkc023 : typeof gz_002.grxxJson.prm_ykc023 == 'string'?gz_002.grxxJson.prm_ykc023:'',//住院状态 NULL VARCHAR2(6) 当住院的时间内，不允许门诊刷卡时，HIS需要判断该字段。1为在院，2 为未在院
                prmYae921 : typeof gz_002.grxxJson.prm_yae921=='string'?gz_002.grxxJson.prm_yae921:'',//二维码
                prmSymbol : typeof gz_002.grxxJson.prm_symbol=='string'?gz_002.grxxJson.prm_symbol:'',//特殊病标识 限遵义统筹区：1 为已申报慢特病，0 为 未申报慢特病
                prmQrcodetype : typeof gz_002.grxxJson.prm_qrcodetype=='string'?gz_002.grxxJson.prm_qrcodetype:'',//二维码类型 02-电子社保卡（医保付款码）；03-医保
                prmYkc010 : typeof gz_002.grxxJson.prm_ykc010=='string'?gz_002.grxxJson.prm_ykc010:'',//特殊疾病登记信息
                prmYkc296 : typeof gz_002.grxxJson.prm_ykc296=='string'?gz_002.grxxJson.prm_ykc296:'',//居民人员类别
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=mzdj&parm="
				+ JSON.stringify(enterContent),
				function (json) {
					if (json.a != '0') {
						result = "1";
					}
				});
			return result;
		},

		//取消
		gz002_quxiao:function(){
			var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var prm_akc190 = "<prm_akc190>" + rightVue.yjsContentGzyhyb.prm_akc190 + "</prm_akc190>";//就诊编号
			var prm_yab003 = "<prm_yab003>" + gz_002.grxxJson.prm_yab003 + "</prm_yab003>";//分中心编号
			var prm_aka130 = "<prm_aka130>" + gz_002.zdxxJson.prm_aka130 + "</prm_aka130>";//支付类型
			var prm_yka103 = "<prm_yka103>" + rightVue.yjsContentGzyhyb.prm_yka103 + "</prm_yka103>";//结算编号
			var prm_aae011 = "<prm_aae011>" + gz_002.userInfo.czybm + "</prm_aae011>";//操作员编码
			var prm_ykc141 = "<prm_ykc141>" + gz_002.userInfo.czyxm + "</prm_ykc141>";//操作员姓名
			var prm_aae036 = "<prm_aae036>" + gz_002.fDate(new Date(),'datetime') + "</prm_aae036>";//经办时间
			var prm_aae013 = "<prm_aae013>用户取消</prm_aae013>";//退费原因，
			var prm_ykb065 = "<prm_ykb065>" + gz_002.grxxJson.prm_ykb065 + "</prm_ykb065>";
			var prm_aac001 = "<prm_aac001>" + rightVue.yjsContentGzyhyb.prm_aac001 + "</prm_aac001>";
			jysr += prm_akc190 + prm_yab003 + prm_aka130 + prm_yka103 + prm_aae011
				+ prm_ykc141 + prm_aae036 + prm_aae013 + prm_ykb065 + prm_aac001+"</input>";
			$.post("http://localhost:9089/call",{
				'jybh':"42",
				'jysr_xml':jysr,
			},function (json) {
				//成功后调一次 confirm
				if (json.aint_appcode > 0) {
					$.post("http://localhost:9089/confirm",{
						'jylsh':json.astr_jylsh,
						'jyyzm':json.astr_jyyzm,
					},function (data) {
					});
				}else {
					malert(data.astr_appmasg);
				}
			});
		},

		//门诊结算方法
		mzjs:function(){

		},
	}
});
gz_002.getbxlb();
gz_002.getUserInfo();

$(document).click(function () {
	if (this.className != 'selectGroup') {
		$(".selectGroup").hide();
	}
	$(".popInfo ul").hide();
});
