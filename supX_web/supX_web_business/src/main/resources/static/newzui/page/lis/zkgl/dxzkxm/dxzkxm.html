<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!--杨松柏-->
    <title>West多规则质控数据</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="dxzkxm.css" rel="stylesheet"/>
</head>
<style>
    .zui-table-view .fieldlist {
        display: none;
    }
</style>
<body class="skin-default">
<div class="wrapper" id="jyxm_icon">
    <div class="" style="border:none; margin-top: 5px;">
        <div class="xmzb-content" id="brerList" style="padding-bottom: 40px;background: #fff">
            <div class="xmzb-top">
                <div class="col-x-12 ysb_list-50">
                    <button class="zui-btn btn-primary  padd-r5 icon-baocun" @click="bc">保存</button>
                    <button class="zui-btn btn-primary-b  padd-r5 icon-sx" @click="getsbbm()">刷新</button>
                    <button class="zui-btn btn-primary-b  padd-l28 icon-plyr" @click="cr()">批量移入</button>
                    <button class="zui-btn btn-primary-b  padd-l28 icon-plyc" @click="sc()">批量移除</button>
                </div>
                <div class="col-x-12">
                    <div class="zui-inline col-x-2  col-fm-2 xmzb-top-left">
                        <label class="zui-form-label">检验设备</label>
                        <select-input @change-data="resultChange"
                                      :child="jysbList" :index="'hostname'" :index_val="'sbbm'" :val="params.zxsb"
                                      :search="true" :name="'params.zxsb'">
                        </select-input>
                    </div>
                    <div class="col-x-2  col-fm-2 xmzb-top-left">
                        <i>检索码</i>
                        <i><input type="text" @input="getsbbm()" v-model="params.parm" value="" placeholder="请输入关键字" class="zui-input"/></i>
                    </div>
                </div>
            </div>
            <div class="xmzb-content-left ">
                <div class="content-left-top">
                        <div class="i" style="width: 50px"><i><input-checkbox @result="reCheckBox" :list="'getobjlistwx'" :type="'all'" :val="isCheckAll"></input-checkbox></i></div>
                    <div class="i" ><i class="">指标编码</i></div>
                    <div class="i"><i class="">指标名称</i></div>
                    <div class="i" ><i class="">简称</i></div>
                </div>
                <div style="height: 100%">
                    <ul class="content-left-list zui-scroll-left">
                        <li :class="[{'table-hovers':index===activeIndex,'table-hover':index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,index)"
                            :tabindex="$index"  v-if="getobjlist.wx"  v-for="(list,index) in getobjlist.wx" @dblclick="cr(list)">
                            <div class="i" style="width: 50px"><i><input-checkbox @result="reCheckBox" :list="'getobjlistwx'" :type="'some'" :which="index" :val="isChecked[index]"></input-checkbox></i></div>
                            <div class="i"><i class="title overflow1" v-text="list.zkxm"></i></div>
                            <div class="i"><i class="title overflow1" v-text="list.zbmc"></i></div>
                            <div class="i position" ><i class="title overflow1" v-text="list.ywmc"></i></div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="xmzb-content-right ">
                <div class="xmzb-top">
                </div>
                <div class="content-right-top">
                    <div class="i" style="width: 50px"><i><input-checkbox @result="reCheckBox" :list="'getobjlistyx'" :type="'all'" :val="isCheckAll"></input-checkbox></i></div>
                    <div class="i"><i class="">质控项目</i></div>
                    <div class="i"><i class="">批号</i></div>
                    <div class="i"><i class="">产地</i></div>
                    <div class="i"><i class="">正常值</i></div>
                    <div class="i position"><i class="">操作<em class="icon-bgzdgl bgfdgl-right"></em></i></div>
                </div>
                <!--style="display: none"-->
                <div style="height: 100%">
                    <ul class="content-right-list">
                        <li  :class="[{'table-hovers':index===activeIndex1,'table-hover':index === hoverIndex1}]"
                             @mouseenter="switchIndex('hoverIndex1',true,index)"
                             @mouseleave="switchIndex()"
                             @click="switchIndex('activeIndex1',true,index)"
                             :tabindex="$index" v-if="getobjlist.yx"  @dblclick="sc(list)" v-for="(list,index) in getobjlist.yx">
                            <div class="i" style="width: 50px"><i><input-checkbox @result="reCheckBox" :list="'getobjlistyx'" :type="'some'" :which="index" :val="isChecked[index]"></input-checkbox></i></div>
                            <div class="i"><i class="title overflow1" v-text="list.zbmc"></i></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.zkwph" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.cd" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><input type="text" placeholder="" v-model="list.jg" class="zui-input y-input-width" @keydown="nextFocus($event)"/></div>
                            <div class="i"><i><span @click="sc(list)" class="icon-sc"></span></i></div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div id="pop">
            <!--<transition name="pop-fade">-->
            <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
            <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}"
                 style="height: max-content;padding-bottom: 20px">
                <div class="layui-layer-title " v-text="title"></div>
                <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn"
                                                                    @click="isShowpopL=false,isShow=false">&times;</i></span>
                <div class="layui-layer-content">
                    <div class=" layui-mad layui-height">确定删除质控记录：<span class="success">{{name}}</span> 吗？</div>
                </div>
                <div class="zui-row buttonbox">
                    <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
                    <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
                </div>
            </div>
            <!--</transition>-->
        </div>
    </div>
    <script src="dxzkxm.js"></script>
    <script type="text/javascript">
        // $(".zui-table-view").uitable();
        $(".zui-input").uicomplete();
    $(".zui-table-view").uitable();
        $(".zui-scroll-right,.zui-scroll-left,.content-right-list").uiscroll({
            height: '100%',
            size: '6px',
            opacity: .3,
            disableFadeOut: true,
            position: 'right',
            color: '#000'
        });
    </script>
</div>
</body>
</html>