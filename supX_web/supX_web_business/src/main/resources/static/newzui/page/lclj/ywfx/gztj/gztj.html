<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>路径变异审核</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
    .spanDate{
        width: 21px;
        display: inline-block;
        text-align: center;
    }
    .zui-date-line{
        width: auto !important;
        display: inline-block;
    }
    .qxsh{
        border:1px solid #dfe3e9;
        border-radius:4px;
        height:120px;
        width: 100%;
        padding: 7px 12px ;
    }
    .side-form-bg{
        background: none;
    }
    .buttonbox {
        display: inherit;
    }
</style>
<body class="skin-default">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-shh paddr-r5" @click="AddMdel">审核变异</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b" @click="gl"><i class="icon-gl paddr-r5"></i>过滤</button>
            <button class="tong-btn btn-parmary-b" @click="yl"><i class="icon-yl paddr-r5"></i>预览</button>
            <button class="tong-btn btn-parmary-b" @click="dy"><i class="icon-dysq paddr-r5"></i>打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline" style="margin-left: -26px;">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">入径日期</label>
                    <div class="zui-input-inline zui-date zui-date-line">
                        <i class="datenox icon-rl" lay-key="1"></i>
                        <input class="zui-input wh120 show icon-date startDate padd-l-30" placeholder="" type="text"/>
                    </div><span class="spanDate">至</span>
                    <div class="zui-input-inline zui-date zui-date-line">
                        <i class="datenox icon-rl" lay-key="1"></i>
                        <input class="zui-input wh120 show padd-l-30 endDate" placeholder="" type="text" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="fyxm-table">
        <div class="zui-table-view ybglTable"  style=" padding:5px 10px 36px 10px;">
            <div class="fyxm-size">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                <span>
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"  :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                                </span>
                                </div>
                            </th>
                            <th><div class="zui-table-cell cell-s"><span>住院号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>姓名</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>性别</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>年龄</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>病种</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>入径操作员</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>入径日期</span></div></th>
                            <th><div class="zui-table-cell cell-l text-left"><span>审核原因</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in 12"  @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-l text-left" >{{item}}</div></td>
                            <td><div class="zui-table-cell cell-s" >{{item}}</div></td>
                            <td class="cell-s"><div class="zui-table-cell cell-s">
                                <i class="icon-bj icon-font" data-title="编辑"></i>
                            </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!--侧边窗口-->
<div class="side-form  pop-width" :class="{'ng-hide':num==0}" style="padding-top: 0;"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span>取消、变异原因描述</span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
            <textarea class="qxsh" placeholder="请输入取消、变异原因描述"></textarea>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<div id="isTabel">
    <div class="pophide" :class="{'show':isShow}"></div>
    <div class="zui-form podrag  bcsz-layer zui-800 " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title ">过滤查询</div>
        <div class="guolv-xinzeng">
            <span class="layui-txt" @click="append()">新增一项</span>
            <i class="color-btn" @click="isShow=false"
               style="margin-top:-17px;width: 16px;height: 16px;display: inline-block;margin-left: 10px;float: right">×</i>
        </div>
        <div class="layui-layer-content">
            <div class=" layui-mad">
                <ul class="guolv-header guolv-style">
                    <li class="line">关系符</li>
                    <li class="line">条件</li>
                    <li class="line">结果</li>
                    <li class="line">输入值</li>
                    <li class="line">操作</li>
                </ul>
                <ui class="guolv-content" id="guo_append">
                    <div class="guolv-style guolv-bottom" v-for="(item, $index) in cxtjList">
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'ljtj_' + $index"
                                              @change-data="resultChangeLjtj_item" :not_empty="true"
                                              :child="ljtjybgl_tran" :index="'item.ljtj'" :val="item.ljtj"
                                              :name="$index + '.ljtj.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'xm_' + $index"
                                              @change-data="resultChange_item" :not_empty="true"
                                              :child="xmybgl_tran" :index="'item.xm'" :val="item.xm"
                                              :name="$index + '.xm.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'tj_' + $index"
                                              @change-data="resultChangeTj_item" :not_empty="true"
                                              :child="tjybgl_tran" :index="'item.tj'" :val="item.tj"
                                              :name="$index + '.tj.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'tj_' + $index"
                                              @change-data="resultChangeTj_item" :not_empty="true"
                                              :child="tjybgl_tran" :index="'item.tj'" :val="item.tj"
                                              :name="$index + '.tj.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                        </li>
                        <!--<li class="line">-->
                        <!--&lt;!&ndash; 类型 &ndash;&gt;-->
                        <!--<div class="zui-select-inline" v-if="isLxNum.indexOf(''+$index)>=0">-->
                        <!--<select-input :id="'LX_' + $index"-->
                        <!--@change-data="resultChangeTj_item" :not_empty="true"-->
                        <!--:child="jydjlx_tran" :index="'item.jg'" :val="item.jg"-->
                        <!--:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"-->
                        <!--data-notEmpty="false">-->
                        <!--</select-input>-->
                        <!--</div>-->
                        <!--&lt;!&ndash; 输入框通用 &ndash;&gt;-->
                        <!--<div class="zui-select-inline" v-if="isTyNum.indexOf(''+$index)>=0">-->
                        <!--<input type="text" class="zui-input" v-model="item.jg"/>-->
                        <!--</div>-->
                        <!--&lt;!&ndash; 性别 &ndash;&gt;-->
                        <!--<div class="zui-select-inline" v-if="isXbNum.indexOf(''+$index)>=0">-->
                        <!--<select-input :id="'XB_' + $index"-->
                        <!--@change-data="resultChangeTj_item" :not_empty="true"-->
                        <!--:child="xtwhxb_tran" :index="'item.jg'" :val="item.jg"-->
                        <!--:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"-->
                        <!--data-notEmpty="false">-->
                        <!--</select-input>-->
                        <!--</div>-->
                        <!--&lt;!&ndash; 科室 &ndash;&gt;-->
                        <!--<div class="zui-select-inline" v-if="isKsNum.indexOf(''+$index)>=0">-->
                        <!--<select-input @change-data="Wf_YppfChange" :not_empty="false"-->
                        <!--:child="util.sjks" :index="'ksmc'" :index_val="'ksbm'"-->
                        <!--:val="item.jg" :name="'PfxxJson.'+$index+'.ksbm'" :index_mc="'ksmc'" :search="true">-->
                        <!--</select-input>-->
                        <!--</div>-->
                        <!--&lt;!&ndash; 医生 &ndash;&gt;-->
                        <!--<div class="zui-select-inline" v-if="isYsNum.indexOf(''+$index)>=0">-->
                        <!--<select-input @change-data="Wf_YsChange" :not_empty="false"-->
                        <!--:child="util.sjys" :index="'ysmc'" :index_val="'ysbm'"-->
                        <!--:val="item.jg" :name="'PfxxJsonx.'+$index+'.ysbm'" :index_mc="'ysmc'" :search="true">-->
                        <!--</select-input>-->
                        <!--</div>-->
                        <!--&lt;!&ndash; 样本类型 &ndash;&gt;-->
                        <!--<div class="zui-select-inline" v-if="isYblxNum.indexOf(''+$index)>=0">-->
                        <!--<select-input :id="'XB_' + $index"-->
                        <!--@change-data="resultChangeTj_item" :not_empty="true"-->
                        <!--:child="jydjyblx_tran" :index="'item.jg'" :val="item.jg"-->
                        <!--:name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"-->
                        <!--data-notEmpty="false">-->
                        <!--</select-input>-->
                        <!--</div>-->

                        <!--</li>-->
                        <li class="line">
                            <span class="icon-sc" @click="sc($index)"></span>
                        </li>
                    </div>

                </ui>
            </div>
        </div>
        <div class="zui-row buttonbox">
            <div class="left-radio">
                <p class="text-left"><label class="green-radio"><input class="green-radius" type="radio" name="a" checked><i></i><span>重新过滤</span></label></p>
                <p class="text-left"><label class="green-radio"><input type="radio" name="a"><i></i><span>在上次过滤结果中查找</span></label></p>
                <p class="text-left"><label class="green-radio"><input type="radio" name="a"><i></i><span>本次过滤上次过滤的交集</span></label></p>
            </div>
            <div class="right">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save xmzb-db" @click="save">保存</button>
            </div>
        </div>
    </div>
</div>

<script src="gztj.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>