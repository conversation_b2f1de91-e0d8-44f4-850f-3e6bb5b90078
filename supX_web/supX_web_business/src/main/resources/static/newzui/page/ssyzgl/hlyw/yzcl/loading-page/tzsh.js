var tzsh = new Vue({
    el: '#loadingPage',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        brlistjson:{},//只用于接受请求LIST对象
        brList:[],
        yzList: [],
        jkList:[],
        yzshInfoList:[],//真正的列表
        zyhs:[],
        ksid:null,//科室编码
        isOver: false,//是否全选
        hoverBrListIndex: undefined,
    },
    mounted: function () {
        this.moun();
        window.addEventListener('storage',function (e) {
            if( e.key == 'tzsh' && e.oldValue !== e.newValue ){
                tzsh.zyhs = [];
                tzsh.moun();
            }
        });
    },
    updated: function () {
      changeWin();
    },
    methods: {
        closePage: function () { //关闭本页面
            var x = parseInt( sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem( 'hszHzlbUpdate' , x );
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/tzsh.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        },
        isOverClick: function (isOver) {
            this.isOver = isOver || !this.isOver;
            this.yzshInfoList.forEach(function (br) {
                br.isCheckAll = tzsh.isOver;
                br.yzxx.forEach(function (yz) {
                    yz.isChecked = tzsh.isOver;
                });
            });
        },
        moun: function () {
            this.brlistjson=JSON.parse( sessionStorage.getItem( 'tzsh' ) );
            this.brList =this.brlistjson.brlist;
            this.ksid=this.brlistjson.ksid;
            for(var i=0;i<this.brList.length;i++){
                var zyh={
                    zyh:this.brList[i].zyh
                };
                this.zyhs.push(zyh);
            }
            this.initShData();
        },
        checkSelectSh:function (brIndex,yzIndex) {
            var yzStatus = !this.yzshInfoList[ brIndex ].yzxx[ yzIndex ].isChecked;
            this.yzshInfoList[ brIndex ].yzxx[ yzIndex ].isChecked = yzStatus;
            if( yzStatus ){

                var yzIsOverCk = true;
                for ( var x = 0; x < this.yzshInfoList[ brIndex ].yzxx.length; x++ ){
                    if( !this.yzshInfoList[ brIndex ].yzxx[ x ].isChecked ){
                        yzIsOverCk = false;
                        break;
                    }
                }
                this.yzshInfoList[ brIndex ].isCheckAll = yzIsOverCk;

                var isOverCk = true;
                for ( var x = 0; x < this.yzshInfoList.length; x++ ){
                    if( !this.yzshInfoList[x].isCheckAll ){
                        isOverCk = false;
                        break;
                    }
                }
                this.isOver = isOverCk;

            }else {
                this.yzshInfoList[ brIndex ].isCheckAll = false;
                this.isOver = false;
            }
        },
        reCheckBoxSh: function () {
            if( arguments.length == 1 ){
                var isCheckAll = this.yzshInfoList[arguments[0]].isCheckAll? false:true,
                    yzshInfo = this.yzshInfoList[arguments[0]],
                    yzxxList = yzshInfo.yzxx;

                this.yzshInfoList[arguments[0]].isCheckAll = isCheckAll;
                for ( var i = 0; i < yzxxList.length; i++ ){
                    this.yzshInfoList[arguments[0]].yzxx[i].isChecked = isCheckAll;
                }
            }else if(arguments.length == 2){
                this.activeBrListIndex = arguments[0];
                this.activeIndex = arguments[1];
                // var isChecked = this.yzshInfoList[arguments[0]].yzxx[arguments[1]].isChecked? false:true,
                //     yzshInfo = this.yzshInfoList[arguments[0]],
                //     yzxxList = yzshInfo.yzxx,
                //     isCheckAll = true;
                //
                // this.yzshInfoList[arguments[0]].yzxx[arguments[1]].isChecked = isChecked;
                // for ( var y = 0; y < yzxxList.length; y++ ){
                //     if( !yzxxList[y].isChecked ){
                //         this.yzshInfoList[arguments[0]].isCheckAll = false;
                //         isCheckAll = false;
                //         break;
                //     }
                // }
                // if( isCheckAll ) this.yzshInfoList[arguments[0]].isCheckAll = true;
            }

            this.$forceUpdate();
            console.log("-------------------------------");
            console.log(this.yzshInfoList);
        },

        //单个医嘱全选
//          checkYZAll: function (types, object, numb) {
//              if (this.isCheckAll) {
//                  var obj = object[numb].yzxx;
//                  for (var i = 0; i < obj.length; i++) {
//                      var check = types + "_" + numb + "_" + i;
//                      this.isChecked[check] = true;
//                  }
//              } else {
//                  this.isChecked = [];
//              }
//          },
        saveOne:function(){

        },
        shenhe: function () {
            tspop.open();
        },
        showIndex:function(index){
            console.log(index);
        },
        //获取审核医嘱信息
        initShData:function(){
            this.yzshInfoList = [];
            if (this.zyhs.length == 0) {
                malert("请选择病人后再进行此操作！");
                return
            }
            if(this.ksid==null){
                malert("科室编码不能为空！");
                return
            }
            var zyh = JSON.stringify(this.zyhs);
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=hstzsh&ksbm=' + this.ksid + '&zyh=' + zyh,
                function (json) {
                    if (json.d.list.length > 0) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                json.d.list[i].yzxx[int].no = i;
                            }
                        }
                    }
                    tzsh.yzshInfoList = json.d.list;
                	for (var k = 0; k < tzsh.yzshInfoList.length; k++) {
                    	//判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                    	if(tzsh.yzshInfoList[k].nl<7&&this.brList[k].brxb=='1'){
                    		tzsh.yzshInfoList[k].nljd='1';
                    	}else if(tzsh.yzshInfoList[k].nl<7&&tzsh.yzshInfoList[k].brxb=='2'){
                    		tzsh.yzshInfoList[k].nljd='2';
                    	}else if(tzsh.yzshInfoList[k].nl<18&&tzsh.yzshInfoList[k].nl>6&&tzsh.yzshInfoList[k].brxb=='1'){
                    		tzsh.yzshInfoList[k].nljd='3';
                    	}else if(tzsh.yzshInfoList[k].nl<18&&tzsh.yzshInfoList[k].nl>6&&tzsh.yzshInfoList[k].brxb=='2'){
                    		tzsh.yzshInfoList[k].nljd='4';
                    	}else if(tzsh.yzshInfoList[k].nl<41&&tzsh.yzshInfoList[k].nl>17&&tzsh.yzshInfoList[k].brxb=='1'){
                    		tzsh.yzshInfoList[k].nljd='5';
                    	}else if(tzsh.yzshInfoList[k].nl<41&&tzsh.yzshInfoList[k].nl>17&&tzsh.yzshInfoList[k].brxb=='2'){
                    		tzsh.yzshInfoList[k].nljd='6';
                    	}else if(tzsh.yzshInfoList[k].nl<66&&tzsh.yzshInfoList[k].nl>40&&tzsh.yzshInfoList[k].brxb=='1'){
                    		tzsh.yzshInfoList[k].nljd='7';
                    	}else if(tzsh.yzshInfoList[k].nl<66&&tzsh.yzshInfoList[k].nl>40&&tzsh.yzshInfoList[k].brxb=='2'){
                    		tzsh.yzshInfoList[k].nljd='8';
                    	}else if(tzsh.yzshInfoList[k].nl>65&&tzsh.yzshInfoList[k].brxb=='1'){
                    		tzsh.yzshInfoList[k].nljd='9';
                    	}else if(tzsh.yzshInfoList[k].nl>65&&tzsh.yzshInfoList[k].brxb=='2'){
                    		tzsh.yzshInfoList[k].nljd='10';
                    	}else{
                            tzsh.yzshInfoList[k].nljd='11';
                        }
                    }

                    if( tzsh.yzshInfoList.length > 1 ){
                        tzsh.isOverClick(true);
                    }
//                      YZInfo.jsonList = YZInfo.yzshInfoList;
                }, function (error) {
                    console.log(error);
                });
        },
    },
});
var tspop = new Vue({
    el: '#tspop',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        ifClick: true, //判断是否点击了结算按钮
        shsj:'',//审核时间
    },
    mounted: function(){
        this.shsj =this.fDate(new Date(),'date')+' '+this.fDate(new Date(),'times');
        console.log(this.shsj);
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    tspop.shsj = value;
                } else {
                    tspop.shsj = '';
                }
            }
        });
    },
    methods: {
        //关闭
        closes: function () {
            $(this.$refs.tspop).fadeOut(600);
        },
        open: function () {
            $(this.$refs.tspop).fadeIn(600);
        },
        //确认审核
        oksh: function () {
            var tzshData=[];
            for (var i = 0; i < tzsh.yzshInfoList.length; i++) {
                for (var j = 0; j < tzsh.yzshInfoList[i]['yzxx'].length; j++) {
                    if (tzsh.yzshInfoList[i]['yzxx'][j].isChecked) {
                        tzshData.push({"xhid": tzsh.yzshInfoList[i]['yzxx'][j].xhid});
                    }
                }
            }
            if (tzshData.length <= 0) {
                malert("无医嘱审核！");
                tspop.ifClick = true;
                return;
            }
            //console.log("yzsh:"+JSON.stringify({list:this.auditingData}));
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=hstzsh&ksbm=' + tzsh.ksid,
                JSON.stringify({list: tzshData})).then(function (data) {
                if (data.body.a == 0) {
                    malert("审核成功");
                    tspop.ifClick = true;
                    $(this.$refs.tspop).fadeOut(600);
                    tzsh.initShData();//刷新
                } else {
                    malert("审核失败");
                    tspop.ifClick = true;
                }
            }, function (error) {
                console.log(error);
            });
        }
    }
});