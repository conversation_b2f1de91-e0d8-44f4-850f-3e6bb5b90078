var socket;
var wsImpl = window.WebSocket || window.MozWebSocket;
var lxzdwyb = new Vue({
	el: '#lxzdwyb',
	mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
	components: {
		'search-table': searchTable,
		'jbsearch-table': searchTable,
	},
	data: {
		socket:null,
		bxcw: false, //保险是否有错
		dwyltclb_tran: {
			'4': '门诊大病',
			'6': '普通门诊',
		},
		xzbz_tran:{
			'C':'医疗',
			'D':'工伤',
			'E':'生育',
		},
		lxzdwybBrxxContent:{
			yltclb:'6',
			xzbz:'C',
			jylb:'01',//门诊默认本地
		},
		requestParameters:{},
		brxxContent:{},
		bxlbbm: null,
		bxurl: null,
		searchCon: [],
		selSearch: -1,
		them_tran: {},
		ifClick: true, // 用于判断是否点击保存按钮
		jbbm: null,
		jbContent:{},
		jbList:[],
		them: {
			'疾病编码': 'jbbm',
			'疾病名称': 'jbmc',
			'拼音代码': 'pydm',
		},
		bxlb_tran:{
			'00':'非报销',
			'01':'院内设备异常',
			'02':'内部转诊',
			'03':'异地安置',
			'04':'长期驻外',
			'05':'出差探亲',
			'06':'本地转外治疗',
			'07':'异地安置转外治疗',
			'08':'长期驻外转外治疗',
			'09':'急诊转住院',
		},
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		dwybGhxh:null,
		dwybJshid:null,
	},
	mounted: function () {
		this.openConnection();
		this.getbxlb();
	},
	created:function(){
		this.lxzdwybBrxxContent.fyrq = this.fDate(new Date(),'date');
		// setTimeout(function () {
		// 	lxzdwyb.getRightVueBrxx();
		// }, 500);
	},
	methods: {

		loadCard: function(){//读卡
			if(socket.readyState != '1'){
				malert("医保连接已经中断，请重新打开该页面！","top","defeadted");
				return;
			}
			if(lxzdwyb.lxzdwybBrxxContent.jshid){
				malert("此病人已经登记，请直接结算！","top","defeadted");
				return;
			}
			//如果填写就默认为无卡人员
			lxzdwyb.requestParameters = {};//先清空请求对象
			if((lxzdwyb.lxzdwybBrxxContent.grbh && !lxzdwyb.lxzdwybBrxxContent.sbjgbh) || (!lxzdwyb.lxzdwybBrxxContent.grbh && lxzdwyb.lxzdwybBrxxContent.sbjgbh)){
				malert("无卡人员请同时填写个人编号与社保机构编号！","top","defeadted");
				return;
			}
			if(lxzdwyb.lxzdwybBrxxContent.grbh && lxzdwyb.lxzdwybBrxxContent.sbjgbh){
				if(!lxzdwyb.lxzdwybBrxxContent.yltclb){
					malert("医疗统筹类别不能为空！","top","defeadted");
					return;
				}
				if(!lxzdwyb.lxzdwybBrxxContent.xm){
					malert("病人姓名不能为空！","top","defeadted");
					return;
				}
				var prm = {
					grbh : lxzdwyb.lxzdwybBrxxContent.grbh,
					xm : lxzdwyb.lxzdwybBrxxContent.xm,
					yltclb : lxzdwyb.lxzdwybBrxxContent.yltclb,
					sbjgbh : lxzdwyb.lxzdwybBrxxContent.sbjgbh,
					xzbz : lxzdwyb.lxzdwybBrxxContent.xzbz,
				};
				lxzdwyb.requestParameters.query_person_info = prm;
			}else{//否则读卡
				var prm = {
					yltclb : lxzdwyb.lxzdwybBrxxContent.yltclb,
					jymmbz : '1',
					// readertype : '',
				};
				lxzdwyb.requestParameters.read_card = prm;
			}
			socket.send(JSON.stringify(lxzdwyb.requestParameters));
		},

		selectOne: function (item) {
			if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++; // 设置当前页号
				this.searching(true, 'jbmc', this.jbContent['jbmc']); // 传参表示请求下一页,不传就表示请求第一页
			} else { // 否则就是选中事件,为json赋值
				this.jbContent = item;
				Vue.set(this.jbContent, 'jbmc', this.jbContent['jbmc']);
				Vue.set(this.lxzdwybBrxxContent, 'jbbm', this.jbContent['jbbm']);
				Vue.set(this.lxzdwybBrxxContent, 'jbmc', this.jbContent['jbmc']);
				this.$forceUpdate();
				$(".selectGroup").hide();
			}
		},
		searching: function (add, type, val) {

			this.jbContent['jbmc'] = val;
			if (!add) this.page.page = 1;
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			if (this.jbContent['jbmc'] == undefined || this.jbContent['jbmc'] == null) {
				this.page.parm = "";
			} else {
				this.page.parm = this.jbContent['jbmc'];
			}
			var str_param = {
				parm: this.page.parm,
				page: this.page.page,
				rows: this.page.rows,
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + contextInfo.lxzBxurl + "&bxlbbm=" + contextInfo.lxzBxlbbm + "&types=baseData&method=queryJbbm&parm=" +
				JSON.stringify(str_param),
				function (json) {
					if (json.a == 0) {
						var date = null;
						var res = eval('(' + json.d + ')');
						if (add) { // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
							for (var i = 0; i < res.list.length; i++) {
								lxzdwyb.searchCon.push(res.list[i]);
							}
						} else {
							lxzdwyb.searchCon = res.list;
						}
						lxzdwyb.page.total = res.total;
						lxzdwyb.selSearch = 0;
						if (res.list.length > 0 && !add) {
							$(".selectGroup").hide();
							_searchEvent.show();
						}
					} else {
						malert("查询失败  " + json.c, 'top', 'defeadted');
					}
				});
		},
		changeDown: function (event, type) {

			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'jbContent', 'searchCon');
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
				if (type == "text") {
					Vue.set(this.jbContent, 'jbmc', this.jbContent['jbmc']);
					lxzdwyb.jbbm = this.jbContent.jbbm;
					Vue.set(this.jbContent, 'jbmc', this.jbContent['jbmc']);
					Vue.set(this.lxzdwybBrxxContent, 'jbbm', this.jbContent['jbbm']);
					Vue.set(this.lxzdwybBrxxContent, 'jbmc', this.jbContent['jbmc']);
					$(".selectGroup").hide();
					this.selSearch = 0;
					this.nextFocus(event);
				}
			}
		},
		//登记
		loadBx: function () {
            // contextInfo.json.sfzjhm = '510922199406083896';
            // contextInfo.json.brxm = '赵辉';
            // contextInfo.json.brxb = '1';
            // contextInfo.bxShow = false;
            // contextInfo.setAge();
			if (!lxzdwyb.ifClick) {
				malert("请勿重复点击！","top","defeadted");
				return;
			};

            lxzdwyb.ifClick = false;

			if(socket.readyState != '1'){
				malert("医保连接已经中断，请重新打开该页面！","top","defeadted");
				lxzdwyb.ifClick = true;
				return;
			}
			if(lxzdwyb.lxzdwybBrxxContent.jshid){
				malert("此病人已经登记，请勿重复登记！","top","defeadted");
				lxzdwyb.ifClick = true;
				return;
			}
			if (!lxzdwyb.lxzdwybBrxxContent.grbh) {
				malert("个人编号不能为空！", 'top', 'defeadted');
				lxzdwyb.ifClick = true;
				return
			}
			if (!lxzdwyb.lxzdwybBrxxContent.xm) {
				malert("姓名不能为空！", 'top', 'defeadted');
				lxzdwyb.ifClick = true;
				return
			}
			if (!lxzdwyb.lxzdwybBrxxContent.xb) {
				malert("性别不能为空！", 'top', 'defeadted');
				lxzdwyb.ifClick = true;
				return
			}
			if(lxzdwyb.lxzdwybBrxxContent.yltclb == '6' && lxzdwyb.lxzdwybBrxxContent.xzbz == 'C'){
				lxzdwyb.lxzdwybBrxxContent.jbbm = "";
			}else{
				if(lxzdwyb.lxzdwybBrxxContent.yltclb == '4' || lxzdwyb.lxzdwybBrxxContent.xzbz =='C' || lxzdwyb.lxzdwybBrxxContent.xzbz == 'E'){
					if (!lxzdwyb.lxzdwybBrxxContent.jbbm) {
						malert("疾病编码不能为空！", 'top', 'defeadted');
						lxzdwyb.ifClick = true;
						return
					}
				}
			}
			if (!lxzdwyb.lxzdwybBrxxContent.sbjgbh) {
				malert("社保机构编号不能为空！", 'top', 'defeadted');
				lxzdwyb.ifClick = true;
				return
			}
			if (!lxzdwyb.lxzdwybBrxxContent.yltclb) {
				malert("医疗统筹类别不能为空！", 'top', 'defeadted');
				lxzdwyb.ifClick = true;
				return
			}
			if (!lxzdwyb.lxzdwybBrxxContent.xzbz) {
				malert("险种标志不能为空！", 'top', 'defeadted');
				lxzdwyb.ifClick = true;
				return
			}
			if (!lxzdwyb.lxzdwybBrxxContent.yltclb) {
				malert("医疗统筹类别不能为空！", 'top', 'defeadted');
				lxzdwyb.ifClick = true;
				return
			}
			lxzdwyb.requestParameters = {};
			lxzdwyb.requestParameters.init_mz = {
				sbjgbh	: lxzdwyb.lxzdwybBrxxContent.sbjgbh?lxzdwyb.lxzdwybBrxxContent.sbjgbh:"",
				yltclb	: lxzdwyb.lxzdwybBrxxContent.yltclb?lxzdwyb.lxzdwybBrxxContent.yltclb:""	,
				xzbz	: lxzdwyb.lxzdwybBrxxContent.xzbz?lxzdwyb.lxzdwybBrxxContent.xzbz:""	,
				grbh	: lxzdwyb.lxzdwybBrxxContent.grbh?lxzdwyb.lxzdwybBrxxContent.grbh:""	,
				xm	: lxzdwyb.lxzdwybBrxxContent.xm?lxzdwyb.lxzdwybBrxxContent.xm:""	,
				xb	: lxzdwyb.lxzdwybBrxxContent.xb?lxzdwyb.lxzdwybBrxxContent.xb:""	,
				jbbm	: lxzdwyb.lxzdwybBrxxContent.jbbm?lxzdwyb.lxzdwybBrxxContent.jbbm:"",
				fyrq	: lxzdwyb.lxzdwybBrxxContent.fyrq?lxzdwyb.lxzdwybBrxxContent.fyrq:"",
				kh	: lxzdwyb.lxzdwybBrxxContent.kh?lxzdwyb.lxzdwybBrxxContent.kh:"",
				ysbm	: lxzdwyb.lxzdwybBrxxContent.ysbm?lxzdwyb.lxzdwybBrxxContent.ysbm:"",
				mzlx	: lxzdwyb.lxzdwybBrxxContent.mzlx?lxzdwyb.lxzdwybBrxxContent.mzlx:""	,
				jylb	: lxzdwyb.lxzdwybBrxxContent.jylb?lxzdwyb.lxzdwybBrxxContent.jylb:""	,
				jyyybm	: lxzdwyb.lxzdwybBrxxContent.jyyybm?lxzdwyb.lxzdwybBrxxContent.jyyybm:""	,
				bxlb	: lxzdwyb.lxzdwybBrxxContent.bxlb?lxzdwyb.lxzdwybBrxxContent.bxlb:""	,
				mzghbh	: lxzdwyb.lxzdwybBrxxContent.mzghbh?lxzdwyb.lxzdwybBrxxContent.mzghbh:""	,
				ptmzskbz: lxzdwyb.lxzdwybBrxxContent.ptmzskbz?lxzdwyb.lxzdwybBrxxContent.ptmzskbz:"",
			};
			socket.send(JSON.stringify(lxzdwyb.requestParameters));
		},

		//预結算
		lxzdwYjs:function(){
			//同步操作
			$.ajaxSettings.async = false;
			//处理费用
			var dwybBrfyList = [];
			var fylist=[];
			var brfyList=[];
			for (var i=0;i<contextInfo.brfyList.length;i++) {
				var fyparam = {};
				fyparam.mxfyxmbm = contextInfo.brfyList[i].mxfybm;
				fyparam.mxfyxmmc = contextInfo.brfyList[i].mxfymc;
				fyparam.yzlx = '1';
				fyparam.yzhm = null;
				fyparam.fysl = 1;
				fyparam.zxks = contextInfo.json.ghks;
				fyparam.mzks = contextInfo.json.ghks;
				fyparam.sfsj = new Date();
				fyparam.fydj = contextInfo.brfyList[i].fydj;
				brfyList.push(fyparam);
			}
			var requestParameters = '{"list":' + JSON.stringify(brfyList) + '}';
			lxzdwyb.$http.post("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + contextInfo.lxzBxurl + "&bxlbbm=" + contextInfo.lxzBxlbbm + "&types=mzyw&method=queryMzfy",
				requestParameters).then(function (json) {
				if (json.body.a == '0') {
					dwybBrfyList = eval('(' + json.body.d + ')');
					fylist = dwybBrfyList.list;
					for(var i=0;i<fylist.length;i++){
						fylist[i].sbjgbh = lxzdwyb.lxzdwybBrxxContent.sbjgbh;
					}
					//上传费用
					lxzdwyb.requestParameters = {};
					lxzdwyb.requestParameters.put_fymx = fylist;
					socket.send(JSON.stringify(lxzdwyb.requestParameters));
				} else {
					malert(json.body.c);
					common.closeLoading();
					return;
				}
			}, function (error) {
				malert(json.body.c,"top","defeadted");
				common.closeLoading();
				return;
			});
		},


		//正式结算
		lxzdwMzjs:function(){
			lxzdwyb.requestParameters = {};
			lxzdwyb.requestParameters.settle_mz_real = "";
			socket.send(JSON.stringify(lxzdwyb.requestParameters));
		},
		getbxlb: function () {
			var param = {bxjk: "011"};
			$.getJSON(
				"/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
				+ JSON.stringify(param), function (json) {
					if (json.a == 0) {
						if (json.d.list.length > 0) {
                            contextInfo.lxzBxlbbm = json.d.list[0].bxlbbm;
                            contextInfo.lxzBxurl = json.d.list[0].url;
						}
						common.closeLoading();
					} else {
						malert("保险类别查询失败!" + json.c,"top","defeated")
						common.closeLoading();
					}
				});
		},
		openConnection:function(){
			console.log("初始化..............");
			var host = "ws://localhost:14444/" + jgbm;
			if (!socket || socket.readyState == '3') {
				socket = new wsImpl(host);
				console.log("socket连接成功！");
			}
			try {
				socket.onerror = function () {
					if(socket){
						socket.close();
					}
					console.log("发生错误！");
				};
				socket.onopen = function () {
					var init = {"init":{
						yybm:'225001',
						gzrybh:'0001',
						pwd:'1234',
						}};
					socket.send(JSON.stringify(init));
					console.log("连接开启！");
				};
				socket.onclose = function () {
					if(socket){
						socket.close();
					}
					console.log("连接关闭！");
				};
				socket.onmessage = function (evt) {
					var outParm = eval('(' + evt.data + ')');
					handleMessage(outParm);
					console.log("收到消息了！");
				};
			} catch (ex) {
				console.log("异常！");
				if(socket){
					socket.close();
				}
			}
		},
		getRightVueBrxx:function () {
		    if(!contextInfo.json.ghxh){
		        return;
            }
			//查询病人登记信息
			var parm_str = {
				ghxh:contextInfo.json.ghxh,
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + contextInfo.lxzBxurl + "&bxlbbm=" + contextInfo.lxzBxlbbm + "&types=mzyw&method=queryMzdjxx&parm="
				+ JSON.stringify(parm_str),
				function (data) {
					if (data.a == '0') {
						if(data.d){
							var obj = JSON.parse(data.d);
							lxzdwyb.lxzdwybBrxxContent = Object.assign({},lxzdwyb.lxzdwybBrxxContent, obj);
							if(obj.jshid){
                                contextInfo.yjsContentLxzdwyb.jshid = obj.jshid;
                                contextInfo.lxzdwybContent = lxzdwyb.lxzdwybBrxxContent;
								Vue.set(this.jbContent, 'jbbm', this.lxzdwybBrxxContent['jbbm']);
								Vue.set(this.jbContent, 'jbmc', this.lxzdwybBrxxContent['jbmc']);
							}
						}
					}
				});
			this.$forceUpdate();

		},
		//取消结算，删除费用清单
		lxzdwQxjs:function(){
            contextInfo.yjsContentLxzdwyb={}
			lxzdwyb.requestParameters = {};
            contextInfo.lxzdwybYjsFineshed = false;
            contextInfo.bxbz = "";//取消后必须重新初始化
            contextInfo.outpId = "";//取消后必须重新初始化
			var jshids = [];
			var obj = {
				jshid :contextInfo.yjsContentLxzdwyb.jshid,
			};
			jshids.push(obj);
			lxzdwyb.requestParameters.destroy_mz = jshids;
			socket.send(JSON.stringify(lxzdwyb.requestParameters));
		},

		//取消医保挂号
		qxybgh:function(jsjl){
			var jshids = [];
			var obj = {
				jshid :jsjl.bxjsh,
			};
			jshids.push(obj);
			lxzdwyb.requestParameters.destroy_mz = jshids;
			lxzdwyb.dwybGhxh = jsjl.ryghxh;
			lxzdwyb.dwybJshid = jsjl.bxjsh;
			socket.send(JSON.stringify(lxzdwyb.requestParameters));
		},
	}
});

function handleMessage(message){
	console.log(message);
	var method = message.method;
	var returnCode = message.returnCode;
	var outResult = message.outResult;
	switch (method) {
		case "init"://登陆医保接口
			if(returnCode == '0'){
				malert("医保连接成功！");
			}else{
				malert("初始化医保失败，请重新进入该页面！","top","defeadted");
				if(socket){
					socket.close();
					socket = null;
				}
			}
			break;
		case "read_card"://身份认证(有卡)
			if(returnCode == '0'){
				read_card_backFun(outResult);
			}else{
				malert(message.msgInfo,"top","defeadted");
				return;
			}
			break;
		case "query_person_info"://身份认证(无卡)
			if(returnCode == '0'){
				query_person_info_backFun(outResult);
			}else{
				malert(message.msgInfo,"top","defeadted");
				return;
			}
			break;
		case "init_mz"://门诊登记
			if(returnCode == '0'){
				init_mz_backFun(outResult);
			}else{
				malert(message.msgInfo,"top","defeadted");
				return;
			}
			break;
		case "put_fymx"://上传费用
			if(returnCode == '0'){
				put_fymx_backFun(outResult);
			}else{
				malert(message.msgInfo,"top","defeadted");
				common.closeLoading();
				return;
			}
			break;
		case "settle_mz_pre"://预结算
			if(returnCode == '0'){
				settle_mz_pre_backFun(outResult);
			}else{
				malert(message.msgInfo,"top","defeadted");
				common.closeLoading();
				return;
			}
			break;
		case "settle_mz_real"://正式结算
			if(returnCode == '0'){
				settle_mz_real_backFun(outResult);
			}else{
				malert(message.msgInfo,"top","defeadted");
				common.closeLoading();
				return;
			}
			break;
		case "destroy_mz"://撤销门诊
			var parm = {
				ghxh: lxzdwyb.dwybGhxh ,
				jshid:lxzdwyb.dwybJshid,
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + contextInfo.lxzBxurl + "&bxlbbm=" + contextInfo.lxzBxlbbm + "&types=mzyw&method=qxmzjs&parm="
			    + JSON.stringify(parm),function (json) {
			    if (json.a == '0') {
			        malert("医保撤销门诊交易成功！");
                    contextInfo.bxShow = false;//登记成功，关闭弹窗
                    lxzdwyb.ifClick = true;
			    } else {
			        malert(json.c,"top","defeadted");
			        return;
			    }
			});
			break;
	}
}
laydate.render({
	elem: '#fyrq',
	rigger: 'click',
	theme: '#1ab394',
	type: 'date',
	done: function (value, data) {
		lxzdwyb.lxzdwybBrxxContent.fyrq = value;
	}
});
