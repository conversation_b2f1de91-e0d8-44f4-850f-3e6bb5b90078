<div id="ybqd">
    <div class="toolMenu">
        <button @click="getData">查询</button>
        <button @click="qxsc">取消上传（当前）</button>
           	    HIS合计：<span>{{totalContent.bs}}</span>笔
           	    医保合计：<span>{{ybbs}}</span>笔
                                              累计：<span>{{fDec(totalContent.account,2)}}</span>元
  
    </div>
    <div>
     <span v-text= "error"></span>
    </div>
    <div class="tableDiv" style="margin: 10px;">
        <table class="patientTable" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;">
            <tr>
                <th class="tableNo"></th>
                <th><input type="checkbox" v-model="fyisCheckAll" @click="fyCheckAll('jsonList')" /></th>
                <!-- <th>费用序列号</th>
                <th>医院药品项目编码</th>
                <th>医院药品项目名称</th>
                <th>中心药品项目编码</th>
                <th>中心药品项目名称</th>
                <th>金额</th>
                <th>就医登记号</th>
                <th>费用发生时间</th>
                <th>录入人工号</th>
                <th>录入人姓名</th>
                <th>录入时间</th>
                <th>上传时间</th> -->
                <th>费用ID</th>
                <th>医院药品项目编码</th>
                <th>医院药品项目名称</th>
                <th>中心药品项目编码</th>
                <th>中心药品项目名称</th>
                <th>金额</th>
                <th>就医登记号</th>
                <th>上传时间</th>
            </tr>
            </thead>
            <tbody>
            <tr><th v-for="item in 12"></th></tr>
            <tr v-for="(item, $index) in yb_fymx" @click="fyCheckOne($index)"
                :class="[{'tableTrSelect':isfyChecked[$index]},{'tableTr': $index%2 == 0}]">
                <th class="tableNo" v-text="$index+1"></th>
                <th><input type="checkbox" name="checkNo" v-model="isfyChecked[$index]" @click.stop="fyCheckSome($index)"/></th>
                <!-- <td v-text="item.serial_fee"></td>
                <td v-text="item.his_item_code"></td>
                <td v-text="item.his_item_name"></td>
                <td v-text="item.item_code"></td>
                <td v-text="item.item_name"></td>
                <td v-text="item.money"></td>
                <td v-text="item.serial_no"></td>
                <td v-text="item.fee_date"></td>
                <td v-text="item.input_staff"></td>
                <td v-text="item.input_name"></td>
                <td v-text="item.input_date"></td>
                <td v-text="item.trans_date"></td> -->
                <td v-text="item.fyid"></td>
                <td v-text="item.mxfyxmbm"></td>
                <td v-text="item.mxfyxmmc"></td>
                <td v-text="item.bxxmbm"></td>
                <td v-text="item.bxxmmc"></td>
                <td v-text="item.je"></td>
                <td v-text="item.serial_no"></td>
                <td v-text="fDate(item.djsj,'YY')"></td>
            </tr>
            </tbody>
        </table>
        <!-- <div class="nh-total">
            <div>
                合计：<span>{{totalContent.bs}}</span>笔
            </div>
            <div>
                累计：<span>{{fDec(totalContent.account,2)}}</span>元
            </div>
        </div> -->
    </div>
</div>
<script type="application/javascript" src="ybqd.js"></script>