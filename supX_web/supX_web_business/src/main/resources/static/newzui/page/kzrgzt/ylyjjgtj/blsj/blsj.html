<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>不良事件</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script src="/newzui/pub/js/highcharts.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../kshy/kshy.css">
    <link rel="stylesheet" href="../wjz/wjz.css">
    <link href="../hz/hz.css" rel="stylesheet">
    <link href="blsj.css" rel="stylesheet">
    <script src="blsj.js"></script>
</head>
<body class="">
<div class=" background-f ">
    <div class="tong-top font-16 color-c3 font-weight flex-container flex-align-c ">不良事件统计</div>
    <div class="content" v-cloak>
        <div class="tong-search">
            <div class="top-form">
                <label class="top-label">时间段</label>
                <div class="top-zinle flex-container">
                    <div class="zui-date position">
                        <i class="iconfont icon-icon61"></i>
                        <input type="text" class="zui-input wh122 dateStart" v-model="beginrq"/>
                    </div>
                    <span class="flex-container flex-align-c padd-r-10 padd-l-10">至</span>
                    <div class="zui-date position">
                        <i class="iconfont icon-icon61"></i>
                        <input type="text" class="zui-input wh122 dateEnd" v-model="endrq"/>
                    </div>
                </div>
            </div>
            <div class="top-form">
                <label class="top-label">类型</label>
                <div class="top-zinle">
                    <div class="top-zinle">
                        <select-input class="wh122" @change-data="resultChange"
                                      :child="istrue_tran" :index="'nbtclb'" :index_val="'nbtclb'" :val="nbtclb"
                                      :name="'ypContent.nbtclb'" :search="true" :index_mc="'nbtclb'">
                        </select-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="chart">
            <!--<h2 class="text-center font-18 font-weight color-393f">科室医生会议次数统计</h2>-->
            <div class="flex-container height-310">
                <div class="wh40">
                    <div class="canvas" id="pieCanvas"></div>
                    <div class="text-center margin-t-3"><span class="color-757c83 font14">不良事件上报总</span><span class="color-green font-30">123次</span></div>
                </div>
                <div class="wh60">
                    <div class="canvas"  id="canvas"></div>
                </div>
                <div class="flex-container flex-dir-cr">
                    <ul class="ranking margin-b-10">
                        <li class="flex-container whiteSpace flex-align-c" v-for="(list,index) in 22">
                            <div class="rankingNum rankingNumImg font-18 color-c7 text-center" :class="[index==0?'rankingOne':'',index==1?'rankingtwo':'',index==2?'rankingThree':'']">{{index>2?index+1:''}}</div>
                            <div class="rankingImg" style="background-image: url('/newzui/pub/image/2018072106.png')"></div>
                            <div class="rankingName">李浩然</div>
                            <div class="rankingNumber color-f38d4f">10次</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div style="width: 100%;background: #f2f2f2;height: 8px;"></div>
    <div class="flex-container flex-jus-sp" id="brRyList01" style="background: #f3f3f3">
        <div style="width: 59.5%;border: 1px solid #eeeeee" class="flex-container flex-dir-c">
            <div class="tong-top   flex-container flex-align-c flex-jus-sp"><p class="font-16 color-c3 font-weight">不良事件上报</p><p class="color-green font14 cursor" @click="NewopenPage()">更多>></p></div>
            <div class=" zui-table-view flex-container flex-dir-c flex-one padd-l-10 padd-r-10  padd-t-10 " >
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m "><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s "><span>患者姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s "><span>事件类型</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>发生时间</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>事件名称</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>上报医生</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>状态</span></div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body flex-one margin-b-10"  @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <!-- v-if="jsonList.length!=0" -->
                        <tbody>
                        <tr @click="openPage" :tabindex="$index" v-for="(item, $index) in 22"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)"
                            @dblclick="doPop($index)"
                            class="tableTr2">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    {{$index}}
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s text-decoration">
                                    患者姓名
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s ">
                                    事件类型
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    发生时间
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell  cell-s">
                                    事件名称
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell  cell-s">
                                    上报医生
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" :class="$index%2==0?'colr-ff6555':'color-2e88e2'">
                                    {{$index%2==0?'未确认':'待审核'}}
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell  cell-m">
                                    <i v-if="$index%2==0" class="iconfont icon-iocn46 icon-hover"></i>
                                    <i v-else class="iconfont icon-font25 icon-icon73 icon-hover"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                </div>
                <div class="zui-table-fixed table-fixed-l">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"   @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in 22"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                @dblclick="doPop($index)">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                        {{$index}}
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s text-decoration">
                                        患者姓名
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>上报医生</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>状态</span></div>
                                </th>
                                <th><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"   @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in 22"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                @dblclick="doPop($index)">
                                <td>
                                    <div class="zui-table-cell  cell-s">
                                        上报医生
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" :class="$index%2==0?'colr-ff6555':'color-2e88e2'">
                                        {{$index%2==0?'未确认':'待审核'}}
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-m flex-center" >
                                        <i v-if="$index%2==0" class="iconfont icon-iocn46 icon-hover"></i>
                                        <i v-else class="iconfont icon-font25 icon-icon73 icon-hover"></i>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div style="border: 1px solid #eeeeee;width: 40%">
            <div class="bottom-right">
                <div class="count-title">
                    不良事件流程监管
                </div>
                <div class="count-name">患者:李浩然</div>
                <div class="flex-container flex-jus-c flex-align-c" style="height: calc(100% - 85px)">
                    <div class="count-process">
                        <div class="count-start1 fl">
                            <!--已经进行的流程状态样式start-ys1 color-cf0,未进行start-ws1 color-c42-->
                            <span class="start-ys1 color-cf0">医务人员上报</span>
                            <span class="start-text">2018/12/12 12:55</span>
                        </div>
                        <div class="count-start2 fl">
                            <!--已经进行的流程状态样式start-ys2 color-cf0,未进行start-ws2 color-c42-->
                            <span class="start-ys2 color-cf0 text-indent-f40">科主任填写建议</span>
                            <span class="start-text start-text-position">2018/12/12 12:55</span>
                        </div>
                        <div class="count-start3 fl">
                            <!--<span class="start-overtime">超时</span>-->
                            <!--已经进行的流程状态样式start-ys3 color-cf0,未进行start-ws3 color-c42-->
                            <span class="start-ys3 color-c42 text-indent-f40">医务科审核填写意见</span>
                            <span class="start-text start-text-position1">2018/12/12 12:55</span>
                        </div>
                        <div class="count-start4 fl">
                            <!--已经进行的流程状态样式start-ys4 color-cf0,未进行start-ws4 color-c42-->
                            <span class="start-ws6 color-c42  ">结束</span>
                            <span class="start-text start-text-position2">2018/12/12 12:55</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="width: 100%;background: #f2f2f2;height: 8px;"></div>
    <div style="border: 1px solid #eeeeee">
        <div class="tong-top font-16 color-c3 font-weight flex-container flex-align-c ">不良事件上报类型统计</div>
        <div class="canvas-height">
            <div id="container" style="min-width:420px;"></div>
        </div>
    </div>
</div>
<script src="blsj.js" type="text/javascript"></script>
</body>
</html>