<link rel="stylesheet" href="jcjy.css">
<div class="wrapper flex-container" style="border: none;">

    <div id="bgdList" class="zui-table-view flex-container flex-dir-c margin-r-10">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                    <tr>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s">项目类型</div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l">项目名称</div>
                        </th>
                    </tr>
                </thead>
            </table>
        </div>

        <div class="zui-table-body flex-one">
            <table class="zui-table table-width50" v-if="djList.length!=0">
                <tbody>
                    <tr ref="list" v-for="(item, $index) in djList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" :tabindex="$index"
                        @click="checkSelect([$index,'one','djList'],$event),showDj($index)">
                        <td>
                            <div class="zui-table-cell cell-s">{{ item.ifJcbgd ? "检查" : "检验" }}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.fymc"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p v-if="djList.length==0" class="noData text-center zan-border">暂无数据...</p>
        </div>
    </div>

    <div id="jcbgd" v-cloak v-if="ifShow" class="flex-one flex-container flex-dir-c">
        <div class="margin-b-10 butt-box">
            <a :href="bgdImageHref" target="_blank">点击查看图片</a>
        </div>
        <div class="flex-one">
            <iframe width='100%' height='100%' :src='iframeSrc'></iframe>
        </div>
    </div>

    <div id="jybgd" v-cloak v-if="ifShow" class="flex-one over-auto">
        <div class="jybgd">
            <h1 class="text-center">四川省建筑医院检验报告单</h1>
            <div class="grid-box">
                <div class="col-x-2">姓&emsp;&emsp;名：<em>{{ jybgd.brxm || '' }}</em></div>
                <div class="col-x-3">住&ensp;院&ensp;号：{{ jybgd.bah || '' }}</div>
                <div class="col-x-3">送检医师：无字段</div>
                <div class="col-x-4">申请日期：{{ fDate( jybgd.sqrq , "datetime" ) }}</div>
                <div class="col-x-2">性&emsp;&emsp;别：{{ brxb_tran[ jybgd.xb ] }}</div>
                <div class="col-x-3">床&ensp;位&ensp;号：{{ jybgd.cwh || '' }}</div>
                <div class="col-x-3">样本类型：{{ jybgd.yblx || '' }}</div>
                <div class="col-x-4">采集日期：{{ fDate( jybgd.cyrq , "datetime" ) }}</div>
                <div class="col-x-2">年&emsp;&emsp;龄：{{ jybgd.nl }}{{ nldw_tran[ jybgd.nldw ] }}</div>
                <div class="col-x-3">科&emsp;&emsp;室：<em>{{ jybgd.ksbm }}</em></div>
                <div class="col-x-3">条&ensp;形&ensp;码：{{ jybgd.jyxh }}</div>
                <div class="col-x-4">接收日期：{{ fDate( jybgd.ybhsrq , "datetime" ) }}</div>
                <div class="col-x-2">仪&ensp;检&ensp;号：<em>{{ jybgd.bbbh || ''}}</em></div>
                <div class="col-x-3">类&emsp;&emsp;型：{{ jybgd.yblx || ''}}</div>
                <div class="col-x-3">临床诊断：{{jybgd.lczd || ''}}</div>
                <div class="col-x-4">备&emsp;&emsp;注：{{ jybgd.bz || '' }}</div>
            </div>
            <table class="table-fixed jybgd-table">
                <thead>
                    <tr>
                        <th class="text-center" style="width:50px;">序号</th>
                        <th>项目名称</th>
                        <th style="width:100px;">简称</th>
                        <th style="width:100px;">结果</th>
                        <th style="width:50px;">状态</th>
                        <th style="width:150px;">参考范围</th>
                        <th style="width:100px;">单位</th>
                        <th style="width:150px;">检测方法</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item,index) in jybgdmx">
                        <td class="text-center">{{ item.xh }}</td>
                        <td>{{ item.zwmc }}</td>
                        <td></td>
                        <td>{{item.valueN}}</td>
                        <td></td>
                        <td>{{item.ckzT}}</td>
                        <td>{{ item.dw }}</td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
            <div class="grid-box">
                <div class="col-x-4">审核时间：{{ fDate( jybgd.shrq , "datetime" ) }}</div>
                <div class="col-x-4">打印时间：{{ fDate( jybgd.djrq , "datetime" ) }}</div>
                <div class="col-x-2">检验者：{{ jybgd.jyxm }}</div>
                <div class="col-x-2">审核者：{{ jybgd.shry }}</div>
                <div class="col-x-12">本测试结果仅对本样本负责！</div>
            </div>
        </div>
    </div>

</div>
<script type="text/javascript" src="jcjy.js"></script>
