.tong-search .zui-form .padd-r-20 {
    padding-left: 45px;
}

.text-decoration {
    color: #1abc9c;
    cursor: pointer;
    text-decoration: underline;
}

.userNameImg {
    border-radius: 50px;
    width: 70px;
    display: inline-block;
    height: 70px;
    margin: 0 0 0 2px;
    text-align: center;
}

.userNameImg img {
    width: 100%;
    height: 100%;
    margin: auto;
    cursor: pointer;
    display: inline-block;
}

.userName {
    font-size: 18px;
    color: #1abc9c;
    cursor: pointer;
    font-weight: 600;
    font-family: '幼圆';
}

.userName-pin img{
    /*background-image: url("/newzui/pub/image/pin.png");*/
    /*background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);*/
    /*border: 1px solid #f46161;*/
    border-radius: 4px;
    background-color: #f46161;
    width: 20px;
    height: 20px;
    /*background-position: center center;*/
    /*background-repeat: no-repeat;*/
    font-size: 10px;
    color: #ffffff;
}

.userName-lc img{
    /*background-image: linear-gradient(-180deg, #ffb456 3%, #ed8805 100%);*/
    border: 1px solid #ed8705;
    border-radius: 4px;
    width: 20px;
    background-color: #ed8705;
    height: 20px;
    font-size: 10px;
    color: #ffffff;
}

.sex {
    font-size: 14px;
    margin-right: 12px;
}
.color-man{
    color:#4b8ad4;
}
.color-woman{
    color: #fa6969;
}
.blues{
    background-color: #123456;
}
.username-nl {
    font-size: 14px;
    color: #354052;
    margin-right: 19px;
}

.djzt {
    position: absolute;
    right: -44px;
    top: -1px;
    box-shadow:0 6px 24px 0 rgba(0, 0, 0, 0.20);
    transform: rotate(36deg);
    font-size: 15px;
    color: #ffffff;
    text-align: center;

    overflow: hidden;
    width: 130px;
    height: 35px;
    line-height: 35px;
}
.red{
    background-color: #EF6263;
}
.blue{
    background-color: #4B9BFD;
}
.redCj{
    background-color: #F23E3E;
}
.header-text {
    background: #F6FDFA;
    width: 100%;
    height: 71px;
    border-bottom: 1px solid #E9F0EE;
}

.userWidth {
    /*padding: 0 9px;*/
    /*margin: 0 9px;*/
    background: #ffffff;
    box-shadow: rgba(1, 23, 18, 0.4) 0px 0px 9px 0px;
    /*width: 307px;*/
    overflow: hidden;
    margin-bottom: 20px;
}
.flex{
    display: flex;
}
.flex_items{
    align-items: center;
}
.userName {
    margin-right: 14px;
    max-width: 72px;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    /*text-overflow: ellipsis;*/

}

.userName-pin {
    margin-right: 5px;
}

.main-content {
    margin-top: 6px;
    padding-left: 12px;
}

.main-content p {
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-weight: 300;
    margin-bottom: 5px;
}
.font-20{
font-size: 24px;
    position: relative;
    cursor: pointer;
    color: rgba(100, 111, 130, 0.5490196078431373);
}
.shuxian:after{
    content: '';
    height: 80%;
    width: 1px;
    background: #dfe3e9;
    position: absolute;
    z-index: 111;
    right: -10px;
    top: 2px;
}
.margin-l13 {
    margin-left: 56px;
}
.zui-item .user-footer-img{
    background-color: transparent;
    width: 24px;
    height: 24px;
    background-size: cover;
    vertical-align: text-top;
}
.user-footer-img {
    background: #E9EAEB;
    width: 35px;
    height: 35px;
    display: inline-block;
    cursor: pointer;
    border-radius: 100%;
    /*margin-right: 25px;*/
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 70% 70%;
    text-align: center;
}
.ljImg{
    background-image: url("/newzui/pub/image/lujing.png");
}
.yzImg{
    background-image: url("/newzui/pub/image/yizhu.png");
}
.blImg{
    background-image: url("/newzui/pub/image/bingli.png");
}
.sbImg{
    background-image: url("/newzui/pub/image/shangbao.png");
}

.footer-text {
    text-align: center;
}

.kpFlex {
    padding-top: 15px;
    border: none;
    overflow: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-self: center;
    /*align-items: center;*/
    align-content: flex-start;
    margin: 0 auto;
    justify-content: center;
}
.hz-user{

}
.alignItems{
    align-items: center;
}
.hzbr{
    color: #F17676;
}
.menu-right {
    padding: 0px 15px;
    height: 32px;
}
.menu-right {
    padding: 0px 15px;
    height: 32px;
}
.menu-right span{
    padding: 0 5px;
    display: inline-block;
    /*height: 24px;*/
}
.menu-right .fenge{
    font-size: 0;
    width: 1px;
    height: 17px;
    background:#646f82;
    padding: 0;
}
.fa-th-large:before {
    font-size: 18px;
    padding-top: 3px;
}
 .kpFlex .active {
    border: solid 1px #f96868;
    box-shadow: 1px 0 13px 0 #f96868;
}
