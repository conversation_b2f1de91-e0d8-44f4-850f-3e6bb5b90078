    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            title:'',
            zYList:[],
            totle:'',
            sjyzymc:null,
            num:0,
            param: {
                page: '',
                rows: '',
                total: ''
            }
        },
        watch:{
            sjyzymc:function () {
                var sjyzymc = '';
                for(var i = 0; i < wrapper.zYList.length; i++) {
                    if(wrapper.zYList[i].sjyzymc == this.sjyzymc) {
                        sjyzymc = wrapper.zYList[i].sjyzymc;
                    }
                }
            } ,
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增数据元值域代码';
                wap.open();
                wap.popContent={};

            },
            sx:function () {
                yjkmtableInfo.getData();
            },
            //删除
            del:function () {
                yjkmtableInfo.remove();
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    yjkmtableInfo.getData();
                }

            },
            yzy:function () {
              $.getJSON('/actionDispatcher.do?reqUrl=EmrXtwhSjy&types=queryFl',function (json) {
                  if(json.a==0){
                      wrapper.zYList=json.d.list;
                  }
                  console.log(json);
              }) ;
            },
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            iShow:false,
            isTabelShow:false,
            flag:false,
            jsShow:false,
            ksList:[],
            hszList:[],
            ywckList:[],
            centent:'',
            isFold: false,
            title:'',
            ifClick:true,
            num:0,
            csContent: {},
            jsonList: [],
            popContent: {
                'tybz': ''
            },
        },

        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                yjkmtableInfo.saveData();
            },
        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            popContent: {},
            jsonList: [],//
            iShow:false,
            isShowpopL:false,
            totlePage:0,
            total:'',
            page:'',
            kmbm:'',
            kmmc:'',
            rows:10,
            param: {
                page:1,
                rows:10,
                sort: '',
                order: 'asc',
                parm:''
            },


        },
        methods: {
            getData: function () {
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }
                this.param.sjyzymc = wrapper.sjyzymc;
                $.getJSON("/actionDispatcher.do?reqUrl=EmrXtwhSjy&types=query&parm="+JSON.stringify(this.param),function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if(json.a==0){
                        yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                        yjkmtableInfo.jsonList = json.d.list;
                    }
                });
            },

            //保存
            saveData: function() {

                if(wap.popContent.scbz){
                    wap.popContent.scbz = '0'
                }else{
                    wap.popContent.scbz = '1'
                }
                if(wap.popContent.sjyzydm==null || wap.popContent.sjyzydm=='' || wap.popContent.sjyzydm==undefined){
                    malert('请添加元值域代码','top','defeadted');
                    return;
                }if(wap.popContent.sjyzyjm==null || wap.popContent.sjyzyjm=='' || wap.popContent.sjyzyjm==undefined){
                    malert('请添加元值域简码','top','defeadted');
                    return;
                }if(wap.popContent.sjyzymc==null || wap.popContent.sjyzymc=='' || wap.popContent.sjyzymc==undefined){
                    malert('请添加元值域名称','top','defeadted');
                    return;
                }if(wap.popContent.sjyzyz==null || wap.popContent.sjyzyz=='' || wap.popContent.sjyzyz==undefined){
                    malert('请添加元值域值编码','top','defeadted');
                    return;
                }if(wap.popContent.sjyzyzhy==null || wap.popContent.sjyzyzhy=='' || wap.popContent.sjyzyzhy==undefined){
                    malert('请添加元值域值名称','top','defeadted');
                    return;
                }
                var json=JSON.stringify(wap.popContent);
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhSjy&types=save',
                    json).then(function (data) {
                    if(data.body.a == 0){
                        wap.closes();
                        yjkmtableInfo.getData();
                        malert("保存成功","top","success");
                    } else {
                        malert("上传数据失败","top","defeadted");
                    }
                },function (error) {
                    console.log(error);
                });
            },

            //删除
            remove: function() {
                var list = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                        var sjyzyid={};
                        sjyzyid.sjyzyid=this.jsonList[i].sjyzyid
                        list.push(sjyzyid);
                    }
                }
                if(list.length == 0){
                    malert("请选中您要删除的数据","top","defeadted");
                    return false;
                }
                if(!confirm("请确认是否删除")){
                    return false;
                }
                var json='{"list":'+JSON.stringify(list)+'}'
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhSjy&types=updateBz',
                    json).then(function (data) {
                    if(data.body.a == 0){
                        malert("删除成功","top","success")
                        yjkmtableInfo.getData();
                    } else {
                        malert("删除失败","top","defeadted")
                    }
                }, function (error) {
                    console.log(error);
                });

            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑数据元值域代码'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },


        },


    });
    yjkmtableInfo.getData();
    wrapper.yzy();





