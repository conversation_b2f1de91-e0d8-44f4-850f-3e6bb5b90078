var jlxq=new Vue({
    el:'#jlxq',
    data:{
        jlContent:{},
        popContent:{
            time:''
        },
    },
    methods:{
        getJlData: function () {
            var ksqp=this.popContent.time.split(' - ')
            var parm={
                ksrq:ksqp[0],
                jsrq:ksqp[1],
                zyh:userNameBg.Brxx_List.zyh
            };
            $.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=queryAll&parm='+ JSON.stringify(parm), function (json) {
                if (json.a == '0') {
                    jlxq.jlxx_list = json.d.list;
                }
            });
        }
    },
    created:function () {
    this.getJlData()
    },
})
laydate.render({
    elem: '.todate'
    , eventElem: '.zui-date i.datenox'
    , trigger: 'click'
    , theme: '#1ab394',
    range: true
    , done: function (value, data) {
        jlxq.popContent.time = value
    }
});