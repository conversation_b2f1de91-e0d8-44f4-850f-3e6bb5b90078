<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>压疮管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link type="text/css" href="ycgl.css" rel="stylesheet"/>
</head>
<body class="skin-default flex-container flex-dir-c flex-one">
    <div class="wrapper background-f percent100" id="jyxm_icon" >
        <div class="panel" v-cloak>
            <div class="tong-top">
                <button v-waves class="tong-btn btn-parmary" @click="AddModel('',0)"><i class="iconfont icon-iocn42 icon-cf"></i>评估压疮</button>
                <button v-waves class="tong-btn btn-parmary-b" @click="getData"><i class="iconfont icon-iocn56 icon-c1"></i>刷新</button>
            </div>
            <div class="tong-search">
                <div class="top-form">
                    <label class="top-label">状态</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChange"
                                          :child="bxzt_tran" :index="'zt'" :val="zt"
                                          :name="'zt'" >
                            </select-input>
                        </div>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">评估结果</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChange" :data-notEmpty="false"
                                          :child="pgList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                                          :name="'popContent.wxks'" :search="true">
                            </select-input>
                        </div>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">时间</label>
                    <div class="top-zinle">
                        <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                        <input type="text" class="zui-input wh122 times text-indent-20"/>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">检索</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <input class="zui-input todate wh182 text-indent-10" placeholder="请输入关键字" id="timeVal"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-r-10 padd-l-10" v-cloak>
            <div class="zui-table-header">
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>患者姓名</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>年龄</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>性别</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>住院号</span></div></th>
                        <th><div class="zui-table-cell cell-xxl text-left"><span>诊断</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>入院时间</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>评估时间</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>Braden评分</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>评估结果</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body "   @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item,$index) in 10" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s color-c1 text-line">
                                    患者姓名
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m">年龄</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m">性别</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">住院号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xxl text-left">诊断诊断诊断诊断诊断诊断诊断诊断诊断诊断</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">入院时间</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">评估时间</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">Braden评分</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="'中度风险' ? 'color-c04':''">
                            中度风险<!--中度风险:颜色状态值color-c04，高度风险：颜色状态值color-cff5 ，发生压疮：颜色状态值：color-9e，低度风险：color-c7--->

                            </div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" :class="'待审核' ? 'color-cff5':'' ">
                                待审核 <!--待审核：颜色状态值color-cff5，已审核：color-c04，已结束：color-008-->
                            </div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-2">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                    <em class="width30"><i class="iconfont icon-icon75 icon-font25 icon-hover" data-title="评估" @click="pinggu('',1)"></i></em>
                                </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--暂无数据提示,绑数据放开-->
                <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
            </div>

            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l background-f">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <tr v-for="(item, $index) in 10"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()">
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r background-f">
                <div class="zui-table-header">
                    <table class="zui-table zui-collapse">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in 10"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()" >
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" :class="'待审核' ? 'color-cff5':'' ">
                                    待审核 <!--待审核：颜色状态值color-cff5，已拒绝：color-cf3，已取消：color-dlr-->
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" >
                                     <span class="flex-center padd-t-2">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                    <em class="width30"><i class="iconfont icon-icon75 icon-font25 icon-hover" data-title="评估" @click="pinggu('',1)"></i></em>
                                </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>


            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

    </div>


<script src="ycgl.js"></script>
</body>

</html>
