.skin-default{
    overflow-y: auto;
}
.wrapper{
    background-color: #fff;
    min-height: 100%;
}
.userNameBg{
    background:#708f89;
    /*height:180px;*/
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    background-image: url("/newzui/pub/image/userImg.png");
    padding: 10px;
}
.userNameImg img{
    width: 100px;
}
.text-color{
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#333333;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#333333;
    text-align:left;
}
.zyh,.bq,.ys,.brzt,.bz,.cwh{
    margin-right: 30px;
}

.hl {
    margin-right: 52px;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{
    color: #333333;
    text-decoration: underline;
}
.ksys-btn{
    background-color: #fff;
}

.qxsh{
    background: #d9dddc !important;
    color: #8e9694 !important;
}

.zx{
    background: #f2a654 !important;
}
.dy{
    background: #1abc9c !important;
}
.action-bar.fixed{
     width: auto;
    right: 10px;
    left: 10px;
}

.zui-table-view .zui-table-body .bg-red{
    background-color: rgba(255, 0, 0, 0.30980392156862746);
}
