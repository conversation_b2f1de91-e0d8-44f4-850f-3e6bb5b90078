<style type="text/css">
    @media print {
        @page {
            size: 180mm 126mm;
            margin: 0 0.5mm 0;
        }
    }

    #print-box {

        /*display: flex;*/
        flex-direction: column;
        justify-content: center;

        /*overflow: hidden;*/
        page-break-after: always;
        font-size: 2.5mm;
        color: #000;
        font-family: '微软雅黑 宋体 Arial';
    }
    #print-box .list{
        padding: 17mm 4mm 4mm;
        width: 180mm;
        /*height: 126mm; !*去掉了高度展示，渲染不会出错，不知道打印如何*!*/
        height: 120mm;
        page-break-after: always;
    }
    table.gridtable {
        font-family: verdana, arial, sans-serif;
        font-size: 11px;
        color: #f3b169;
        /*border-width: 1px;*/
        /*border-color: #f3b169;*/
        border-collapse: collapse;
    }

    table.gridtable th, table.gridtable td {
        /*border-width: 1px;*/
        padding: 3.5px 4px 3px;
        white-space: nowrap;
        /*border-style: solid;*/
        /*border-color: #f3b169;*/
    }

    .content-right table.gridtable th, .content-right table.gridtable td {
        border: none;
        background-color: transparent;
        padding: 2px 4px;
    }

    .content-right table.gridtable td {
        color: #333333;
    }
    .content-right table.gridtable  .mxfyxmmc{
        width: 100px;
        /*display: -webkit-inline-box;*/
        overflow: hidden;
    }
    table.gridtable .color-block {
        color: #333333;
    }

    .color-dsh {
        color: #f3b169;
    }

    .font-20 {
        font-size: 20px;
    }

    .visibility {
        visibility: hidden;
    }

    .content-right-box {
        height: 313px;
    }

    .transform-left {
        transform: rotate(90deg);
        position: absolute;
        top: 50%;
        left: -100px;
    }
</style>
<div id="print-box" class="over-auto" v-cloak>
    <div class="list" v-for="(item,index) in PrintList">
<!--        <header class="flex-container flex-align-c ">-->
<!--            <div class="title-left padd-l-30">-->
<!--                <div class="font-12">门诊号:{{mzjbxxContent.ghxh}} &ensp;发票号码:{{printObj.fphm}}</div>-->
<!--            </div>-->
<!--        </header>-->
        <main class="print-content flex-container relative">
            <div class="content-left padd-l-10  ">
                <div class="content-header ">
                    <div class="flex-container ">
                        <div class="padd-r-10"><span class="font-12">&emsp;&emsp;</span>{{printObj.brid || printObj.rybrid}}</div>
<!--                        <div><span class="font-12">&emsp;&emsp;&emsp;&emsp;</span>{{item[0].zxksmc}}</div>-->
                    </div>
                    <div class="flex-container flex-jus-sb padd-r-45">
                        <div class="padd-r-10"><span class="font-12"></span>{{printObj.brxm}}</div>
                        <div class="font-12">{{fDate(new Date(),'year')}}&emsp;&emsp;{{fDate(new Date(),'month')}}&emsp;&emsp;{{fDate(new Date(),'day')}}</div>
                    </div>
                </div>
                <div class="content-box">
                    <table class="gridtable">
                        <tr>
                            <th class="visibility">费别</th>
                            <th class="visibility">金额</th>
                            <th class="visibility">费别</th>
                            <th class="visibility">金额</th>
                        </tr>
                        <tr>
                            <td class="visibility">西药费</td>
                            <td class="color-blue">{{setJe(item,'西药费')}}</td>
                            <td class="visibility">手术费</td>
                            <td class="color-blue">{{setJe(item,'手术费')}}</td>
                        </tr>
                        <tr>
                            <td class="visibility">中草药</td>
                            <td class="color-blue">{{setJe(item,'中草药')}}</td>
                            <td class="visibility">输血费</td>
                            <td class="color-blue">{{setJe(item,'输血费')}}</td>
                        </tr>
                        <tr>
                            <td class="visibility">中成药</td>
                            <td class="color-blue">{{setJe(item,'中成药费')}}</td>
                            <td class="visibility">输氧费</td>
                            <td class="color-blue">{{setJe(item,'氧气费')}}</td>
                        </tr>
                        <tr>
                            <td class="visibility">检查费</td>
                            <td class="color-blue">{{setJe(item,'检查费')}}</td>
                            <td class="visibility">其他</td>
                            <td class="color-blue">&emsp;&emsp;{{setJe(item,'材料费')}}</td>
                        </tr>
                        <tr>
                            <td class="visibility">放射费</td>
                            <td class="color-blue">{{setJe(item,'放射检查')}}</td>
                            <td class="color-blue"></td>
                            <td class="color-blue"></td>
                        </tr>
                        <tr>
                        <td class="visibility">CT 等</td>
                        <td class="color-blue">{{setJe(item,'CT')}}</td>
                        <td class="color-blue"></td>
                        <td class="color-blue"></td>
                        </tr>
                        <tr>
                            <td class="visibility">诊查费</td>
                            <td class="color-blue">{{setJe(item,'诊查费')}}</td>
                            <td class="color-blue"></td>
                            <td class="color-blue"></td>
                        </tr>
                        <tr>
                            <td class="visibility">化验费</td>
                            <td class="color-blue">{{setJe(item,'化验费')}}</td>
                            <td class="color-blue"></td>
                            <td class="color-blue"></td>
                        </tr>
                        <tr>
                            <td class="visibility">治疗费</td>
                            <td class="color-blue">{{setJe(item,'治疗费')}}</td>
                            <td class="color-blue"></td>
                            <td class="color-blue"></td>
                        </tr>
                        <tr>
                            <td class="visibility" colspan="2">现金支付</td>
                            <td class="color-blue">{{printObj.xjzf}}</td>
                            <td class="color-blue"></td>
                        </tr>
                        <tr>
                            <td class="visibility" colspan="2">个人账户支付</td>
                            <td class="color-blue">{{printObj.ybkzf}}</td>
                            <td class="color-blue"></td>
                        </tr>
                        <tr>
                            <td class="visibility" colspan="2">合计</td>
                            <td class="color-blue"></td>
                            <td class="color-blue"></td>
                        </tr>
                    </table>
                    <div class="flex-container font-12 flex-align-c  padd-b-10 padd-t-10">
                        &emsp;&emsp;&emsp;<span class="color-blue padd-r-10">&emsp;&nbsp;{{userName.replace(/^\"|\"$/g,'')}}</span>
                        &emsp;&emsp;&emsp;<span class="color-blue padd-l-35">&emsp;&nbsp;{{printObj.jsjlid || printObj.ryjsjlid}}</span>
                    </div>
                    <div class="flex-container font-12 flex-align-c padd-b-5">
                        &emsp;&emsp;&emsp;&emsp;<span class="color-blue padd-l-20">四川省乐至县妇幼保健计划生育中心</span>
                    </div>
                    <div class="flex-container font-12 flex-align-c">
<!--                        &emsp;&emsp;&emsp;&emsp;<span class="color-blue padd-l-10">{{jsjlContent.zflxmc || '医保卡支付'}}</span>-->
<!--                        &emsp;&emsp;&emsp;&emsp;<span class="color-blue padd-l-10">{{jsjlContent.ybkzf}}</span>-->
                    </div>
                </div>
            </div>
            <div class="content-right ">
                <div class="content-header visibility">
                    <div class="flex-container ">
                        <div class="padd-r-10"><span class="font-12">卡号:</span>0000156798</div>
                        <div><span class="font-12">执行科室:</span>0000156798</div>
                    </div>
                    <div class="flex-container ">
                        <div class="padd-r-10"><span class="font-12">姓名:</span>美珠颂</div>
                        <!--<div class="font-12">2020年12月25日 15:05:33</div>-->
                    </div>
                </div>
                <div class="content-right-box">
                    <table class="gridtable">
                        <tr>
                            <th class="visibility">项目</th>
                            <th class="visibility">单位</th>
                            <th class="visibility">单价</th>
                            <th class="visibility">数量</th>
                            <th class="visibility">金额</th>
                        </tr>
                        <tr v-for="(childItem,childIndex) in item.list">
                            <td class="color-blue mxfyxmmc text-center font-12">{{childItem.yzfl == '4' ? childItem.zhfymc : childItem.mxfyxmmc}}</td>
                            <td class="color-blue font-12 wh50 text-center">{{childItem.dw}}</td>
                            <td class="color-blue font-12 wh50 text-center">{{childItem.fydj}}</td>
                            <td class="color-blue font-12 wh50 text-center">{{childItem.fysl}}</td>
                            <td class="color-blue font-12 wh50 text-center">{{childItem.fyje}}</td>
                        </tr>
                    </table>
                </div>
                <div class="flex-container flex-jus-c font-14 flex-align-c"><span class="color-blue font-12">{{totalFun(item)}}</span>
                </div>

            </div>
        </main>
    </div>
</div>
<script type="text/javascript">
    var printPageBox = new Vue({
        el: '#print-box',
        mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
        data: {
            PrintList: [],
            newArray: [],
            printObj: {},
            userName: sessionStorage.getItem('userName' + userId),
        },
        mounted: function () {
            console.log(this.printObj)
            if(tableInfo.$el.id =='panel'){
                this.printObj = Object.assign(tableInfo.jztfList[0]);
            }else {
                this.printObj = Object.assign(this.printObj,popCenter1.printObj[0].brfyPrintList[0],popCenter1.jsjlContent,rightVue.mzjbxxContent)
                for (var i = 0; i <popCenter1.printObj.length ; i++) {
                    this.printObj.fphm+=','+popCenter1.printObj[i]['fphm']
                }
            }
            this.getPrintData();
        },
        methods: {
            totalFun: function (item) {
                var sum = item.list.reduce(function (total, obj) {
                    return total + parseFloat(obj.fyje)
                }, 0)
                return this.fDec(sum, 2)
            },
            setJe: function (item, type) {
                var total = 0
                for (var i = 0; i < item.list.length; i++) {
                    if(item.isType=='1'){
                        total += parseFloat(item.list[i].fyje)
                    }else{
                        if (item.fylbmc == type) {
                            total += parseFloat(item.list[i].fyje)
                        }
                    }
                }
                return total == 0 ? '' : this.fDec(total, 2)
            },
            getPrintData: function () {
                var that = this;
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryMzbrfymx&ghxh=" + (this.printObj.ghxh || this.printObj.ryghxh) + "&yljgbm="+jgbm+"&fpList="+JSON.stringify(this.printObj.fphm), function (json) {
                    if (json.a == 0&& json.d && json.d.list.length!=0) {
                        that.filterData(json.d.list)
                        console.log(json.d.list)
                    }else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });
            },
            filterData: function (list) {
                this.newArray = [];
                // for (var i = 0; i < list.length; i++) {
                //     var map = list.reduce(function (p, c, index, array) {
                //         [p[c.zxksmc] = p[c.zxksmc] || [], p[c.zxksmc].push(c), p][2]
                //         return p
                //     }, {});
                //     // var list=[]
                //     var psllsh = (Object.keys(map).map(function (i) {
                //         return map[i]
                //     }));
                // }
                // for (var n = 0; n <psllsh.length ; n++) {
                //     this.group(psllsh[n])
                // }
                this.PrintList = list
                setTimeout(function () {
                    window.print()
                    $('#printPage').html('')
                }, 1000)
            },
            group: function (array) {
                var index = 0;
                while (index < array.length) {
                    this.newArray.push(array.slice(index, index += 12));
                }
                return this.newArray;
            }
        },
    })
</script>
