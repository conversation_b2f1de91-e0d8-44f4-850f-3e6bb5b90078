(function () {
    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.panel',
        data:{
            index:1,
            pop:{},
            searchAll:'',
            delList:[],
            updateList:[],
        },

        methods:{
        	//2018/07/20查询方法
            queryAll:function () {

            },
            addclass:function (num) {
                this.index=num
            },
            show:function () {
                pop.isShow=true;
            },
            add:function () {
                pop.title='新增检验分类'
            },
            del:function(){
            	wrapper.delList = [];
            	//删除
            	if(jyx.isChecked.length != 0){
            		//添加要删除的分类信息
            		for (var i = 0; i < jyx.isChecked.length; i++) {
						if(jyx.isChecked[i]){
							wrapper.delList.push(jyx.jsonList[i]);
						}
					}
            		//删除请求
            		var data =  '{"list":'+ JSON.stringify(wrapper.delList) +'}';
            		console.log(data);
    	       		this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmFlDelete',data).then(function(json) {
    	       			 console.log(json.body);
    	       			 if(json.body.a == 0){
    	       				jyx.getData();
                			malert('删除成功！！','top','success');
                			jyx.getData();
    	       			 }else{
    	            			malert('删除失败！！','top','defeadted');
    	            	 }
    	       		 });
            	}else{
            		malert('请选择要删除的分类','top','defeadted')
            	}
            },
            save:function(){
            	console.log(wrapper.updateList);
            	if(wrapper.updateList.length == 0){
            		malert('数据未有改变','top','defeadted');
            	}else{
            		//保存操作 
            		var data = '{"list":'+ JSON.stringify(wrapper.updateList) +'}';
                	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmFlUpdate',data).then(function(json) {
    	       			 console.log(json.body);
    	       			 if(json.body.a == 0){
    	       				jyx.getData();
              			malert('保存成功！！','top','success');
              			$(".side-form-bg").removeClass('side-form-bg')
              			$(".side-form").addClass('ng-hide');
              			jyx.getData();
    	       			 }else{
    	            			malert('保存失败！！','top','defeadted');
    	            	 }
    	       		 });
            	}
            },
            refresh:function(){
            	//刷新
            	jyx.getData();
            }
        },
        watch:{
        	'searchAll':function(){
        		jyx.param.parm = wrapper.searchAll;
        		jyx.getData();
        	}
        }
    });
    var jyx=new Vue({
        el:'#utable1',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
        	jsonList:'',
        	param:{
        		parm:'',
        		page: 1,
    			rows: 10,
        	},
        	isChecked:[],
        	totlePage:'',
        },
        methods:{
            show:function () {
                pop.title='编辑检验分类'
            },
            getData:function(){
            	jyx.isChecked = [];
            	wrapper.updateList = [];
            	pop.reqParams.flmc = '';
            	pop.reqParams.lxdh = '';
            	pop.reqParams.tybz = '0';
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmFlSelect&param=" + JSON.stringify(jyx.param), function(json) {
            		if(json.a=="0"){
            			jyx.jsonList = json.d.list;
            			jyx.totlePage = Math.ceil(json.d.total / jyx.param.rows);
            		}
            	});
            },
            change:function(index){
            	jyx.jsonList[index].tybz = jyx.jsonList[index].tybz == '0'?'1':'0';
            	//wrapper.updateList;
            	if(wrapper.updateList.length != 0){
            		for(var i = 0; i <wrapper.updateList.length ; i++){
            			if(wrapper.updateList[i].flbm == jyx.jsonList[index].flbm){
            				wrapper.updateList[i].tybz = jyx.jsonList[index].tybz;
            				return;
            			}
            		}
            		wrapper.updateList.push(jyx.jsonList[index]);
            	}else{
            		wrapper.updateList.push(jyx.jsonList[index]);
            	}
            },

        },
    })
    var pop=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            popContent:{},
            centent:'',
            reqParams:{
            	flmc:'',
            	lxdh:'',
            	tybz:'0'
            }
        },
        methods:{
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                //成功回调提示
                // malert('111','top','defeadted');
            },
            AddClose:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            } ,
            // //取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // //确定
            confirms:function () {
            	var data = JSON.stringify(pop.reqParams);
        		console.log(data);
	       		this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmFlInsert',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				jyx.getData();
            			malert('保存成功！！','top','success');
	       			 }else{
	            			malert('保存失败！！','top','defeadted');
	            	 }
	       		 });
                // $(".side-form-bg").removeClass('side-form-bg')
                // $(".side-form").addClass('ng-hide');
            },
            //停用
            tyfl:function(){
            	pop.reqParams.tybz = pop.reqParams.tybz == '0' ? '1' :'0';
            }

        },
    });
    document.onkeydown=function (ev) {
        var ev=window.event|| ev
        var key=ev.keyCode
        if(key==83&& ev.ctrlKey){
            return false
        }
    }
    
  //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
    	if($(this).val() == '' || $(this).val() == null) {
    		$(this).addClass("emptyError");
    	} else {
    		$(this).removeClass("emptyError");
    	}
    });
    
    jyx.getData();
})()