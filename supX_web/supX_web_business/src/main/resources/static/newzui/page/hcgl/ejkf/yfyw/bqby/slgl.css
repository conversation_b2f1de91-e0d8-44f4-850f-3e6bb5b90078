.iconfont.font-14{
  font-size: 14px;
}
.icon-iocn56:before,.icon-iocn31:before,.icon-Artboard-12 .path1:before,.icon-Artboard-12  .path2:before,.icon-iocn38:before{
  color: #1abc9c;
  font-size: 18px;
}
.icon-iocn31:before{
  font-size: 21px;
}
.icon-Artboard-12  .path2:before{
  font-size: 16px;
}
.icon-icon60:before,.icon-iocn9:before,.icon-iocn45:before{
  color: #ffffff;
}
.icon-iocn9:before{
  font-size: 18px;
}
.fyxm-tab,.tong-search{
  float: none;
}
.iconfont.padd-r-10{
  padding-right: 10px;
}
.icon-Artboard-12{
  padding-right: 5px;
}
.tree_list{
  background:#fdfdfd;
  border:1px solid #e9eee6;
  border-top: none;
  overflow: auto;
  width:auto;
  height:265px;
}
.tree_-two-list{
  background:#fdfdfd;
  border:1px solid #e9eee6;
  border-top: none;
  overflow: auto;
  white-space: nowrap;
  /*width:324px;*/
}
.background-box{
  height: 100%;
}
.wrapper{
  width: 100%;
}
.h1title{
  font-weight: bold;
  font-size:22px;
  color:#3a3a3a;
  text-align:center;
  line-height:19px;
}
.ksfyd-title{
  font-size:14px;
  color:#7f8fa4;
  min-height: 19px;
  line-height:19px;
}
.icon-dysq:before{
  color: #ffffff;
}
.zui-input{
  text-indent: 0;
}
.tree_tem1 ul,.tree_tem11 ul {
  padding-left: 0px;
}

.tree_text1 {
  padding: 0 3px;
}

.tree_tem1 li ,.tree_tem11 li {
  line-height: 40px;
  width: 100%;
  padding-left: 15px;
}

.tree_tem1 li:hover ,.tree_tem11 li:hover {
  background: rgba(26, 188, 156, 0.08);
}

.tree_tem1 li div.active_tree,.tree_tem11 li div.active_tree {
  background: rgba(26, 188, 156, 0.08);
  border: 1px solid #1abc9c;
}
