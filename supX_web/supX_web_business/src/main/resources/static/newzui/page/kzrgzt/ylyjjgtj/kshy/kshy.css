.icon-icon61{
    position: absolute;
    left: 10px;
    top: 25%;
    cursor: pointer;
}
.icon-icon61:before{
    color: #c5d0de;
}
.num-border{
    position: relative;
    width: 172px;
    height: 172px;
}
.num-border:after{
    content: '';
    background:#eef8f6;
    width:172px;
    display: block;
    position: absolute;
    top: 0;
    border: 1px solid #ddefec;
    border-radius:100%;
    height:172px;
}
.num-border:before{
    content: '';
    width:141px;
    height:141px;
    z-index: 1;
    position: absolute;
    border:6px solid #1abc9c;
    box-shadow:0 0 4px 0 rgba(2,43,35,0.25);
    border-radius:100%;
    display: block;
    top: 50%;
    transform: translate(-50%,-50%);
    left: 50%;
}
.text-num{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 11;
    color: #1abc9c;
    font-size: 80px;
}
.text-line{
    position: absolute;
    top: 64%;
    color: #1abc9c;
    text-decoration: none;
    left: 70%;
    transform: translate(-65%,-64%);
    z-index: 11;
    font-size: 20px;
}
.canvas{
    width: 80%;
    min-width: 585px;
    min-height:249px;
    height: 310px;
    padding-left: 62px;
}
.chart{

    padding: 0 10px 0 54px;
}
.height-310{
    height: 310px;
    min-height: 310px;
}
.ranking{
    background:rgba(243,167,79,0.04);
    border:1px solid rgba(243,167,79,0.37);
    border-radius:4px;
    width:192px;
    height:243px;
    overflow: auto;
    padding: 8px 0 8px 12px;
}
.rankingImg{
    background-size: contain;
    background-position: center center;
    background-image: none;
    background-repeat: no-repeat;
    width:26px;
    height:26px;
    border-radius:100%;
    margin:3px 3px 0 10px;
}
.rankingNumImg{
    background-size: contain;
    background-position: center center;
    background-image: none;
    width:30px;
    height:30px;
    background-repeat: no-repeat;
}
.rankingOne{
    background-image: url('/newzui/pub/image/19265610734201234.png')
}
.rankingtwo{
    background-image: url('/newzui/pub/image/89120521071806125.png')
}
.rankingThree{
    background-image: url('/newzui/pub/image/188905246533614683.png')
}
.color-f38d4f{
    color: #f38d4f;
}
.color-4193e5{
    color: #4193e5;
}
.colr-ff6555{
    color: #ff6555;
}
.color-2cb261{
    color: #2cb261;
}
.color-ec7d30{
    color: #ec7d30;
}
.rankingName{
    padding-right: 14px;
}
.tong-top{
    padding-left: 20px;
    min-height: 45px;
}

.kshyjl-theme{
    position: static;
}