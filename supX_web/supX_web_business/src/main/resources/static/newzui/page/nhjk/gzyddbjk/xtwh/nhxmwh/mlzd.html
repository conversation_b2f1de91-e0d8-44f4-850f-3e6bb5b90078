<div id="mlzd">
    <div class="flex-container flex-align-c padd-b-10">
        <button  class="tong-btn btn-parmary"  @click="getData"><span class="fa fa-refresh"></span>读取</button>
        <button  class="tong-btn btn-parmary"  @click="downData"><span class="fa fa-plus"></span>下载编码</button>
    </div>

    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m">序号</div></th>
                    <th class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th><div class="zui-table-cell cell-xxl text-left">项目编码</div></th>
                    <th><div class="zui-table-cell cell-s">名称</div></th>
                    <th><div class="zui-table-cell cell-s">类别</div></th>
                    <th><div class="zui-table-cell cell-s">剂型</div></th>
                    <th><div class="zui-table-cell cell-s">范围</div></th>
                    <th><div class="zui-table-cell cell-s">使用级别</div></th>
                    <th><div class="zui-table-cell cell-s">代码</div></th>
                    <th><div class="zui-table-cell cell-s">报销比例</div></th>
                    <th><div class="zui-table-cell cell-s">单词限价</div></th>
                    <th><div class="zui-table-cell cell-s">限制次数</div></th>
                    <th><div class="zui-table-cell cell-s">规格</div></th>
                    <th><div class="zui-table-cell cell-s">单位</div></th>
                    <th><div class="zui-table-cell cell-s">是否基药</div></th>
                    <th><div class="zui-table-cell cell-s">拼音码</div></th>
                    <th><div class="zui-table-cell cell-s">价格（省）</div></th>
                    <th><div class="zui-table-cell cell-s">价格（市）</div></th>
                    <th><div class="zui-table-cell cell-s">价格（县）</div></th>
                    <th><div class="zui-table-cell cell-s">价格（乡）</div></th>
                    <th><div class="zui-table-cell cell-s">价格（村）</div></th>
                    <th><div class="zui-table-cell cell-s">限价（省）</div></th>
                    <th><div class="zui-table-cell cell-s">限价（市）</div></th>
                    <th><div class="zui-table-cell cell-s">限价（市）</div></th>
                    <th><div class="zui-table-cell cell-s">限价（县）</div></th>
                    <th><div class="zui-table-cell cell-s">限价（乡）</div></th>
                    <th><div class="zui-table-cell cell-s">限价（村）</div></th>
                    <th><div class="zui-table-cell cell-s">目录级别</div></th>
                    <th><div class="zui-table-cell cell-s">审核状态</div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one over-auto"   @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                    <td class="cell-m"><div  class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    <td  class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td><div  class="zui-table-cell  cell-xxl text-left">{{item.bm}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.mc}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.xmlb}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.jx}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.fw}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.syjb}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.dm}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.bxbl}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.djxj}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.xzcs}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.gg}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.dw}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.sfjy}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.pym}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.jg1}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.jg2}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.jg3}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.jg4}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.jg5}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.xj1}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.xj2}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.xj3}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.xj4}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.xj5}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.mljb}}</div></td>
                    <td><div  class="zui-table-cell cell-s">{{item.shzt}}</div></td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
<script type="application/javascript" src="mlzd.js"></script>