.popCenter{
    display: inline-block;
    overflow: scroll;
    height: 100%;
    width: 100%;
    background-color: white;
}

.ypsl-table table{
    width: 760px;
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
}

.ypsl-table th, .ypsl-table td{
    border: 1px solid #000;
    padding: 10px;
}

.ypsl-table td{
    padding: 4px 2px;
}

.lydTitle{
    font-size: 22px;
    text-align: center;
}

.printBtu{
    right: 0;
    position: absolute;
}

.yzd-brInfo{
    width: 758px;
    padding: 14px 0;
    overflow: hidden;
    border: 1px solid #000;
    margin: 10px auto 0;
    border-bottom: 0;
}

.yzd-brInfo > div{
    float: left;
}

.yzd-brInfo > div > span{
    font-size: 14px;
    margin: 6px;
}

.yzd-brInfo > div > span:last-child{
    margin: 10px;
}

.cqyzd{
    margin-bottom: 60px;
}


.sameStart{
    position: absolute;
    border-top: 1px solid #000000;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 50%;
    bottom: 0;
}
.sameEnd{
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 1px solid #000000;
    width: 10px !important;
    height: 50%;
    top: 0;
}
.same{
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 100%;
    top: 0
}

.zxTime{
    position: absolute;
    top: 0;
    left: 28px;
}

.YZChoice span{
    font-size: 16px;
    margin: 0 10px;
}

.stop{
    color: rgb(167, 167, 167);
}

.noFinish{
    color: rgb(189, 54, 47);
}