var payNo={
    data:{
        zflxList:[],
        resObj:{},
        codeNum:0,
        zflxbm:'',
        codeContent:'',
    },
    computed:{
    },
    mounted:function (){
    },
    methods:{
        saveSf:function (zflxjk,zflxbm){
            this.zflxbm=zflxbm;
           if( this.payment()){
               return true
           }else {
               return false;
           }
        },
        payment:function (){
                            var yylx=this.zflxbm=='27'||this.zflxbm=='24' ?'00':'02',resObj;
                var param={
                    czybm:userId,
                    czyxm:userName,
                    mzlx:this.mzlx,
                    hisGrbh:tableInfo.fzContent.ryghxh,
                    bzsm:'门诊',
                    inJson: {
                        fyje:tableInfo.fxjtfList[0].qtzf,
                        // fyje:'0.01',
                        yylx:yylx,
                    },
                    yljgbm:jgbm
                }
            if(this.zflxbm  == '27'||this.zflxbm=='24') {
                param.inJson.yjyrq=tableInfo.fxjtfList[0].yjyrq//原交易日期
                param.inJson.yjyckh=tableInfo.fxjtfList[0].orderNo//原交易参考号
            }else {
                param.inJson.ysddh=tableInfo.fxjtfList[0].payno //原交易参考号
            }
            param.inJson=JSON.stringify(param.inJson);

                this.postAjax("http://localhost:9001/posinterface/tf",JSON.stringify(param), function (json) {
                    if (json.returnCode == 0) {
                        resObj=JSON.parse(json.outResult);
                        if(resObj.bank_code != ''){
                            malert(json.msgInfo , 'right');
                        }else {
                            malert(resObj.resp_chin , 'right', 'defeadted');
                            resObj=false;
                        }
                    } else {
                        resObj=false;
                        malert(json.c , 'right', 'defeadted');
                        return false;
                    }
                },function (){
                    common.closeLoading();
                });
            common.closeLoading();
                if(resObj){
                    return  resObj
                }else {
                    this.ifClick = true;
                    return false;
                }

        },
    },
};
