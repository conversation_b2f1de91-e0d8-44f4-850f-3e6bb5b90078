var userNameBg = new Vue({
    el: '.userNameBg',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        Brxx_List: {},
        urlPage: '',
        Num: '',
        num: 0,
        page: '',
        jcShow: false,
        mzShow: true,
        qxks: ''
    },
    methods: {
    },
});

var content = new Vue({
    el: '#content',
    mixins: [dic_transform, baseFunc, tableBase, mformat,checkData],
    components: {
        'search-table': searchTable
    },
    data: {
        cs03004200246:userNameBg.Brxx_List.cs03004200246,
        pageState: {},
        csqx: {},
        them_tran: {'jb': dic_transform.data.ssjb_tran},
        them: {'手术编码': 'ssbm', '手术名称': 'ssmc', '拼音代码': 'pydm', '手术级别': 'jb'},
        allKs: [],
        dg: {
            page: 1,
            rows: 1000,
            sort: "",
            order: "asc",
            parm: ""
        },
        ysjson: {
            ysbz: '1',
            tybz: '0',
            type:1
        },
        hsjson: {
            hsbz: '1',
            tybz: '0',
        },
        total: 0,
        zybmList: [],
        ssjList: [],
        searchCon: [],
        searchCon1: [],
        searchCon2: [],
        searchCon3: [],
        searchCon4: [],
        Content: {},
        Content1: {},
        Content2: {},
        Content3: {},
        Content4: {},
        ysData: [],
        hsData: [],
        selSearch: -1,
        selSearch1: -1,
        selSearch2: -1,
        selSearch3: -1,
        selSearch4: -1,
        popContent: {},
        ifClick: true,
    },
    mounted: function () {
        laydate.render({
            elem: '#apri'
            , value: getTodayDateBegin()
            , trigger: 'click'
            , type: 'datetime'
            , theme: '#1ab394'
            , done: function (value, data) {
                content.pageState.aprq = value;
            }
        });
        this.getYs()
        this.readyData()
        this.ssjreadyData();
        if (sessionStorage.getItem('sspt')) {
            Vue.set(userNameBg, 'Brxx_List', JSON.parse(sessionStorage.getItem('sspt')).sspt);
            Vue.set(this, 'allKs', JSON.parse(sessionStorage.getItem('sspt')).allKs);
            Vue.set(this, 'csqx', JSON.parse(sessionStorage.getItem('sspt')).csqx);
        }
        window.addEventListener('storage', function (e) {
            if (e.key == 'sspt') {
                Vue.set(userNameBg, 'Brxx_List', JSON.parse(sessionStorage.getItem('sspt')).sspt);
                Vue.set(content, 'allKs', JSON.parse(sessionStorage.getItem('sspt')).allKs);
                Vue.set(content, 'csqx', JSON.parse(sessionStorage.getItem('sspt')).csqx);
                content.getSssq()
                this.$nextTick(function () {
                    this.setNotEmpty()
                })
            }
        });
        this.$nextTick(function () {
            this.setNotEmpty()
        })
    },
    filters: {
        initDate2: function (value) {
            if (value) {
                var d = new Date(value);
                return '' + d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
        },
    },
    methods: {
        resultChangeOd: function (val) {
           this.pageState.mzks=val[0];
           this.$forceUpdate()
        },
        resultChange22: function (val) {
            this.pageState.ssysxm=val[4]
            this.pageState.ssys=val[0]
            this.checkYs(val[0]);
        },

        doCheck: function (type) {
            this.pageState[type] = !parseInt(this.pageState[type]) ? 1 : 0
            this.$forceUpdate()
        },
        checkYs:function(val){
            parm={
                jb: content.pageState.ssdj,
                ssys:val
            },
            $.getJSON("/actionDispatcher.do?reqUrl=New1SsRcywSsap&types=checkYs&parm=" + JSON.stringify(parm) , function (data) {
                if (data.a == '0' ) {
                    content.pageState.ssys=val;
                    content.$forceUpdate()
                }else{
                    malert(data.c, 'top', 'defeadted');
                }
            });
        },
        getYs: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(this.ysjson) + "" + "&dg=" + JSON.stringify(this.dg), function (data) {
                if (data.a == '0' && data.d.list.length != 0) {
                    content.ysData = data.d.list;
                    content.geths()
                }
            });
        },
        getKsxx: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhKsryKsbm&types=queryOne&ksbm=' + userNameBg.Brxx_List.ksbm + '', function (json) {
                if (json.a == '0') {
                    content.pageState.ssksmc = json.d.ssksmc
                    content.$forceUpdate()
                }
            });
        },
        getSssq(flag) {
            var str = {
                zyh: userNameBg.Brxx_List.zyh,
            }
            this.pageState = {}
            userNameBg.Brxx_List.aprq ? str.sqdh = userNameBg.Brxx_List.sssqdh : str.sssqdh = userNameBg.Brxx_List.sssqdh
            var type = flag ? 'New1SsRcywSsap' : !userNameBg.Brxx_List.aprq ? 'New1ZyysYsywYzcl' : 'New1SsRcywSsap'
            var QueryType = flag ? 'query' : !userNameBg.Brxx_List.aprq ? 'QuerySssq' : 'query'
            $.getJSON('/actionDispatcher.do?reqUrl=' + type + '&types=' + QueryType + '&parm=' + JSON.stringify(str) + '', function (json) {
                if (json.a == '0' && json.d.list.length != 0) {
                    content.pageState = json.d.list[0];
                    if(!content.pageState.aprq){
                    	content.pageState.aprq = content.fDate(content.pageState.jhrq,'datetime');
                    }
                    // 取手术申请中的手术者和责任护士
                    content.pageState.ssys = content.pageState.ssz;
                    content.pageState.zs1 = content.pageState.sszs1;
                    content.pageState.zs2 = content.pageState.sszs2;
                    content.pageState.zs3 = content.pageState.sszs3;

                    content.pageState.mzks =content.pageState.mzks ==null ? content.allKs[0].ksbm : content.pageState.mzks;
                    content.pageState.dqzt = content.pageState.ssptbz == '0' ? '新排台' : '已排台'
                    content.pageState.sssqdh = userNameBg.Brxx_List.aprq ? content.pageState.sqdh : content.pageState.sssqdh
                    content.pageState.ssdj = json.d.list[0].jb==null? json.d.list[0].ssdj:json.d.list[0].jb
                    //content.getKsxx()
                } else {
                    malert(json.c, 'top', 'defeadted');
                }
            });
        },
        geths: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(this.hsjson) + "" + "&dg=" + JSON.stringify(this.dg), function (data) {
                if (data.a == '0' && data.d.list.length != 0) {
                    content.hsData = data.d.list;
                    content.getSssq()
                }
            });
        },
        SaveSssq: function () {
            if (!content.ifClick) return; //如果为false表示已经点击了不能再点
            content.ifClick = false;
        	if(content.pageState.ssptbz == '1'){
        		malert("该申请已排台，请勿重新排台！", 'top', 'defeadted');
                content.ifClick = true;
                return false;
        	}
        	
            if (!this.empty_sub('contextInfo')) {
                content.ifClick = true;
                return false;
            }
            // if(!this.ry_DataValid()){
            //     return;
            // }
            this.pageState.zyh = userNameBg.Brxx_List.zyh
            this.pageState.ksbm = userNameBg.Brxx_List.ksbm
            this.pageState.ksmc = userNameBg.Brxx_List.ksmc
            this.pageState.sqdh = this.pageState.sssqdh
            this.$http.post('/actionDispatcher.do?reqUrl=New1SsRcywSsap&types=save', JSON.stringify(this.pageState)).then(function (data) {
                if (data.body.a == 0) {
                    malert(data.body.c, 'top', 'success');
                    content.ifClick = true;
                    content.getSssq(true)
                } else {
                    content.ifClick = true;
                    malert("医嘱保存失败：" + data.body.c, 'top', 'defeadted');
                }
            });
        },
        change: function (add, type, val, searchCon, selSearch) {
            if (!add) this.param.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.pageState[type] = val;
            if (this.pageState[type] == undefined || this.pageState[type] == null) {
                this.param.parm = "";
            } else {
                this.param.parm = this.pageState[type];
            }
            var str_param = {parm: this.param.parm, page: this.param.page, rows: this.param.rows};
            //手术编码
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ssbm' + '&json=' + JSON.stringify(str_param), function (data) {
                if (add) {
                    for (var i = 0; i < data.d.list.length; i++) {
                        content[searchCon].push(data.d.list[i]);
                    }
                } else {
                    content[searchCon] = data.d.list;
                }
                content.total = data.d.total;
                content[selSearch] = 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                    return false;
                }
            });
        },
        //检索
        changeDown: function (event, searchCon, Content, selSearch, zsbm, zsmc) {
            this.nextFocus(event);
            console.log(111)
            if (this[searchCon][this[selSearch]] == undefined) return;
            this.inputUpDown(event, searchCon, selSearch);
            this[Content] = this[searchCon][this[selSearch]]
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                Vue.set(this.pageState, zsbm, this[Content]['ssbm']);
                Vue.set(this.pageState, zsmc, this[Content]['ssmc']);
                $(".selectGroup").hide();
                this.nextFocus(event, 2);
            }
        },
        selectOne: function (item) {
            if (item == null) {
                this.param.page++;
                this.change(true, 'zsbm', this['pageState']['zsmc'], 'searchCon', 'selSearch');
            } else {
                this.Content = item;
                Vue.set(this.pageState, 'zsbm', this.Content['ssbm']);
                Vue.set(this.pageState, 'zsmc', this.Content['ssmc']);
                $(".selectGroup").hide()
            }
        },
        selectOne1: function (item) {
            if (item == null) {
                this.param.page++;
                this.change(true, 'ssbm1', this['pageState']['ssmc1'], 'searchCon1', 'selSearch1');
            } else {
                this.Content1 = item;
                Vue.set(this.pageState, 'ssbm1', this.Content1['ssbm']);
                Vue.set(this.pageState, 'ssmc1', this.Content1['ssmc']);
                $(".selectGroup").hide()
            }
        },
        selectOne2: function (item) {
            if (item == null) {
                this.param.page++;
                this.change(true, 'ssbm2', this['pageState']['ssmc2'], 'searchCon2', 'selSearch2');
            } else {
                this.Content2 = item;
                Vue.set(this.pageState, 'ssbm2', this.Content2['ssbm']);
                Vue.set(this.pageState, 'ssmc2', this.Content2['ssmc']);
                $(".selectGroup").hide()
            }
        },
        selectOne3: function (item) {
            if (item == null) {
                this.param.page++;
                this.change(true, 'ssbm3', this['pageState']['ssmc3'], 'searchCon3', 'selSearch3');
            } else {
                this.Content3 = item;
                Vue.set(this.pageState, 'ssbm3', this.Content3['ssbm']);
                Vue.set(this.pageState, 'ssmc3', this.Content3['ssmc']);
            }
        },
        selectOne4: function (item) {
            if (item == null) {
                this.param.page++;
                this.change(true, 'ssbm4', this['pageState']['ssmc4'], 'searchCon4', 'selSearch4');
            } else {
                this.Content4 = item;
                Vue.set(this.pageState, 'ssbm4', this.Content4['ssbm']);
                Vue.set(this.pageState, 'ssmc4', this.Content4['ssmc']);
                $(".selectGroup").hide()
            }
        },
        readyData: function (req, types, listName) {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyzbm&json=" + JSON.stringify({"zylb": "03"}), function (json) {
                if (json.a == 0)
                    content.zybmList = json.d.list;
                else {
                    malert("查询失败", 'top', 'defeadted');
                }
            });
        },
        ssjreadyData: function (req, types, listName) {
            $.getJSON("/actionDispatcher.do?reqUrl=New1SsSjwhSsj&types=query&parm=" + JSON.stringify({'stop': '0'}), function (json) {
                if (json.a == 0)
                    content.ssjList = json.d.list;
                else {
                    malert("查询失败", 'top', 'defeadted');
                }
            });
        },
    },
})
