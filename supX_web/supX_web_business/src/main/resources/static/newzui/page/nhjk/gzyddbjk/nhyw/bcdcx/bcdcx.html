<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <script type="application/javascript" src="/pub/top.js"></script>
    <title>费用清单</title>
    <link rel="stylesheet" href="bcdcx.css"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body>

<div id="fyqd">
    <div class="brSearch printHide">
        <div>
            <span style="display: block;float: left;margin-top: 6px">选择科室：</span>
            <div style="float: right">
                <select v-model="ksbm" @change="Wf_KsChange($event)">
                    <option :value="item.ksbm" v-for="(item,$index) in allKs" v-text="item.ksmc"></option>
                </select>
            </div>
        </div>
        <div>
            <span>检索</span>
            <input v-model="brContent.brxm" @input="searching(false,'brxm',$event.target.value)" @keyDown="changeDown($event,'text')">
        	<search-table :message="searchCon" :selected="selSearch"
                      :them="them" :them_tran="them_tran" :page="page"
                      @click-one="checkedOneOut" @click-two="selectOne":not_empty="true">
       		</search-table>
        </div>
        <div>
            <span>性别</span>
            <input type="text" v-model="brxb_tran[json.brxb]" disabled="disabled"/>
        </div>
        <div>
            <span>年龄</span> 
            <input type="text" v-model="json.nl" disabled="disabled"/>
        </div>
        <div>
            <span>床位</span>
            <input type="text" v-model="json.rycwbh" disabled="disabled"/>
        </div>
        <div>
            <span>入院日期</span>
            <input type="text" v-model="fDate(json.ryrq,'date')" disabled="disabled">
        </div>
    </div>

    <div id="context">
        <div class="toolMenu printHide" style="display: block;">
            <input onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" onchange="getTime(this, 'star')"/>至
            <input onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" onchange="getTime(this, 'end')"/>
            <button @click="getData"><span class="fa fa-refresh"></span>查询</button>
            <button @click=""><span class="fa fa-check-square-o"></span>报表</button>
            <button @click="print"><span class="fa fa-print"></span>打印</button>
        </div>

        <div class="fyqdContext">
            <h2>住院补偿费用统计清单</h2>
            <div class="float-clear">
                <div class="fyqdTime">
                    <span>入/出院时间：</span>
                    <span v-text="fDate(fyqdContent.ryrq,'date')"></span>
                    <span>、</span>
                    <span v-text="fDate(fyqdContent.bqcyrq,'date')"></span>
                </div>
                <div class="infoIpt">
                    <p>住院号：</p>
                    <span v-text="fyqdContent.zyh"></span>
                </div>
                <div class="infoIpt">
                    <p>病员姓名：</p>
                    <span v-text="fyqdContent.brxm"></span>
                </div>
                <div class="infoIpt">
                    <p>参合证号：</p>
                    <span v-text="fyqdContent.ybkh"></span>
                </div>
                <div class="infoIpt">
                    <p>补偿号：</p>
                    <span v-text="fyqdContent.inpid"></span>
                </div>
                <div class="infoIpt">
                    <p>诊断：</p>
                    <span v-text="fyqdContent.ryzdmc"></span>
                </div>
                <div class="infoIpt">
                    <p>住院天数：</p>
                    <span v-text="fyqdContent.zyts"></span>
                </div>
                <div class="infoIpt">
                    <p>总费用：</p>
                    <span v-text="fDec(fyqdContent.fyhj,2)"></span>
                </div>
                <div class="infoIpt">
                    <p>实际补偿：</p>
                    <span v-text="fDec(fyqdContent.bxzfje,2)"></span>
                </div>
                <div class="infoIpt">
                    <p>自付：</p>
                    <span v-text="fDec(fyqdContent.xjzf,2)"></span>
                </div>
                <div class="infoIpt">
                    <p>补偿大写：</p>
                    <span v-text="fyqdContent.bcdx"></span>
                </div>
                <div class="infoIpt">
                    <p>类别：</p>
                    <span>新省农合</span>
                </div>
            </div>
            <div class="table-box">
                <div class="col table-head">
                    <div class="cell">费用编码</div>
                    <div class="cell">费用名称</div>
                    <div class="cell">规格</div>
                    <div class="cell">单位</div>
                    <div class="cell">单价</div>
                    <div class="cell">数量</div>
                    <div class="cell">金额</div>
                    <div class="cell">类别</div>
                </div>
                <div class="col" v-for="(item,index) in jsonListPrint" :class="{'print-always':index==21||((index-21)%29==0&&index>28),'col-border-top':index==22||((index-22)%29==0&&index>29)}">
                    <div class="cell cell-one" v-if="item.fymx">
                        <div v-text="item.fylbmc"></div>
                        <div class="cell-one-last" v-text="fDec(item.fyze,2) + '元'"></div>
                    </div>
                    <div class="cell" v-if="!item.fymx" v-text="item.xmbm"></div>
                    <div class="cell" v-if="!item.fymx" v-text="item.xmmc"></div>
                    <div class="cell" v-if="!item.fymx" v-text="item.fygg"></div>
                    <div class="cell" v-if="!item.fymx" ></div>
                    <div class="cell" v-if="!item.fymx" v-text="fDec(item.fydj,3)"></div>
                    <div class="cell" v-if="!item.fymx" v-text="item.fysl"></div>
                    <div class="cell" v-if="!item.fymx" v-text="item.fyje"></div>
                    <div class="cell" v-if="!item.fymx" v-text="nhtclb_tran[item.fw]"></div>
                </div>
            </div>
            <div class="total">
                <div>
                    <span>合计：</span>
                    <span v-text="fDec(fyqdContent.fyhj,2)"></span>元
                </div>
                <div>
                    <span>四舍五入：</span>
                    <span></span>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="bcdcx.js"></script>
</html>
