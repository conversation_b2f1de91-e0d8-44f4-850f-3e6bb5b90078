<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <title>项目对码</title>
    <link rel="stylesheet" href="xmdm.css"/>
</head>
<body class="skin-default background-f  padd-l-10 padd-r-10">
<div id="InfoMenu" class="padd-b-10">
    <div ref="zlBxXm" class="panel flex-container flex-align-c tong-top">
        <button class="tong-btn btn-parmary-b  icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        <button class="tong-btn btn-parmary-b  icon-sx paddr-r5" @click="autoMapping">自动对码</button>
        <button class="tong-btn btn-parmary-b  icon-width icon-dc " @click="exportData">导出</button>
    </div>
    <div class=" flex-container margin-l-10 padd-t-10 padd-b-10">
        <div class="flex-container flex-align-c margin-l-10">
            <label class="whiteSpace  ft-14 margin-r-5">检索项目</label>
            <div class="zui-input-inline">
                <input class="zui-input wh180" placeholder="请输入关键字" @keyDown.13="goToPage(1)" v-model="param.parm" type="text" id="jsvalue" />
            </div>
        </div>
        <div class="flex-container flex-align-c margin-l-10">
            <label class="whiteSpace  ft-14 margin-r-5">编码类别</label>
            <select-input @change-data="commonResultChange" :not_empty="true" :child="lbbm_tran"
                          :index="param.lbbm" :val="param.lbbm" :search="true"
                          :name="'param.lbbm'">
            </select-input>
        </div>
        <div class="flex-container flex-align-c margin-l-10">
            <label class="whiteSpace  ft-14 margin-r-5">对码</label>
            <select-input @change-data="commonResultChange" :not_empty="true" :child="dm_tran"
                          :index="param.type" :val="param.type" :search="true"
                          :name="'param.type'">
            </select-input>
        </div>
    </div>
        <div class="zui-table-view">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-s">院内编码</div></th>
                        <th><div class="zui-table-cell cell-l">院内名称</div></th>
                        <th><div class="zui-table-cell cell-s">编码类别</div></th>
                        <th><div class="zui-table-cell cell-s">标准编码</div></th>
                        <th><div class="zui-table-cell cell-l">标准名称</div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body"  @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td><div class="zui-table-cell cell-s">{{item.ynbm}}</div></td>
                        <td><div class="zui-table-cell cell-l text-left title">{{item.ynmc}}</div></td>
                        <td><div class="zui-table-cell cell-s">{{lbbm_tran[item.lbbm]}}</div></td>
                        <td><div class="zui-table-cell cell-s">{{item.bzbm}}</div></td>

                        <td><div class="zui-table-cell cell-l">
                            <input autocomplete="off"  class="zui-input position" :id="'mc_'+$index"  v-model="item.bzmc"
                                   @input="searching($index,false,'bzmc',$event.target.value)"
                                   @keyDown="changeDown($index,$event,'text')">
                            <search-table :message="searchCon" :selected="selSearch"
                                          :them="them" :them_tran="them_tran" :page="queryPage"
                                          @click-one="checkedOneOut" @click-two="selectOne">
                            </search-table>
                        </div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
</div>


</body>
<script type="application/javascript" src="xmdm.js"></script>
</html>
