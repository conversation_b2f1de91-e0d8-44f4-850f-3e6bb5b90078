    var wscjl = new Vue({
        el: '#wscjl',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],
            totalContent:{},
            zyh:null,
            isfyChecked:[],
            fyisCheckAll:false,
            inpid:null,
            totlePage:null,
            all:false,
            ifClick: true,
            error:'',
        },
        methods: {
        	/*showall:function(){
        		wscjl.zyh=null;
        		wscjl.all=true;
        		wscjl.getData();
        	},*/
        	getData:function(){
        		if(menu.qhyb_conn != 'onopen'){
        			malert("医保连接失败,请刷新页面重试！");
        			return;
        		}
        		wscjl.error='';
        		if(!wscjl.all){
        		wscjl.zyh=left_tab1.rydjData.ZYH;
        		}
        		this.isfyChecked = [];
        		this.param['zyh']=wscjl.zyh;
        		if(wscjl.zyh == null){
        			malert("请选择患者！");
        			return;
        		}
        		$.getJSON(
                        "/actionDispatcher.do?reqUrl=New1BxInterface&url="+left_tab1.bxurl+"&bxlbbm="+left_tab1.bxlbbm+"&types=inhospital&method=queryWscjl&parm="+JSON.stringify(this.param), function (json) {
                        	if (json.a == 0){
                        		var res=eval('('+json.d+')');
                        		wscjl.totlePage = Math.ceil(res.total/wscjl.param.rows);
                        		wscjl.jsonList = res.list;
                        		var bs=0;
                        		var account=0;
                        		for(var i=0;i<wscjl.jsonList.length;i++){
                        			bs++;
                        			account+=wscjl.jsonList[i].MONEY;
                        		}
                        		wscjl.totalContent.bs=bs;
                        		wscjl.totalContent.account=account;
                        		wscjl.all=false;
                        	}else{
                        		malert(json.c);
                        	}
                        });
        	},
        	// [wscjl.fDate(wscjl.jsonList[i].sfrq,'date')]
        	upload:function(){
        		if(menu.qhyb_conn != 'onopen'){
        			malert("医保连接失败,请刷新页面重试！");
        			return;
        		}
        		if(left_tab1.rydjData.ZYH == null){
         			malert("请选择患者！");
         			return;
         		}
        		if(left_tab1.rydjData.SERIAL_NO == null){
        			malert("该患者还未入院登记！");
        			return;
        		}
        		if(!wscjl.ifClick) return; //如果为false表示已经点击了不能再点
        		wscjl.ifClick = false;
        		$.ajaxSettings.async = false;
        		if(wscjl.jsonList.length==0||wscjl.jsonList==null){
        			return
        		}
        		console.log("------上传费用")
        		var bxscmxList = [];
        		
        		var jbxx = wscjl.jsonList[0];
        		var message= [
            	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
            	              	['start','','BIZC131252'],
            	              	// 固定参数
            	              	['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
            	              	['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
            	              	['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],
            	              	// 入参
            	              	['putcol','','hospital_id',jbxx.HOSPITAL_ID],
            	              	['putcol','','indi_id',jbxx.INDI_ID],
            	              	['putcol','','biz_type',jbxx.BIZ_TYPE],
            	              	['putcol','','serial_no',jbxx.SERIAL_NO],
            	              	['putcol','','input_staff',jbxx.INPUT_STAFF],
            	              	['putcol','','input_man',jbxx.INPUT_MAN],
            	              	['putcol','','recipe_no',jbxx.CFH],
            	              	['putcol','','doctor_no',null],
            	              	['putcol','','doctor_name',null],
            	              	['setresultset','','feeinfo']
            	              	
            	              	];
        		console.log(wscjl.jsonList);
        	//for(var i=0;i<wscjl.jsonList.length;i++){
        		var yq = 0;
        		if(wscjl.jsonList.length >100){
        			yq = 100;
        		}else{
        			yq = wscjl.jsonList.length;
        		}
        		for(var i=0;i<yq;i++){
        			if(wscjl.jsonList[i].ITEM_CODE != null){
        				var fyxx = wscjl.jsonList[i];
        				// 组装要保存到ZYB_BXSCMX表的数据
	        			var mx={
	        				zyh:ryInfo.ZYH,
	        				bxlbbm:"05",
	        				ly:"1",
	        				fyid:wscjl.jsonList[i].FYID,
	        				scid:wscjl.jsonList[i].FYID,
	        				dj:wscjl.jsonList[i].PRICE,
	        				sl:wscjl.jsonList[i].DOSAGE,
	        				je:wscjl.jsonList[i].MONEY,
	        				mxfyxmbm:wscjl.jsonList[i].MXFYXMBM,
	        				mxfyxmmc:wscjl.jsonList[i].MXFYXMMC,
	        				bxxmbm:wscjl.jsonList[i].BXXMBM,
	        				bxxmmc:wscjl.jsonList[i].BXXMMC,
	        				djsj:new Date(),
	        			}
	        			bxscmxList.push(mx);
	        			// 调用动态库将费用信息提交到青海医保
	        			message.push(['putcol','','medi_item_type',fyxx.MEDI_ITEM_TYPE]);
	        			message.push(['putcol','','stat_type',fyxx.STAT_TYPE]);
	        			message.push(['putcol','','his_item_code',fyxx.HIS_ITEM_CODE]);
	        			message.push(['putcol','','item_code',fyxx.ITEM_CODE]);
	        			message.push(['putcol','','his_item_name',fyxx.HIS_ITEM_NAME]);
	        			message.push(['putcol','','model',null]);
	        			message.push(['putcol','','factory',null]);
	        			message.push(['putcol','','standard',null]);
      	              	message.push(['putcol','','fee_date',wscjl.fDate(fyxx.FEE_DATE,'date')]);
      	              	// TODO 测试使用假数据
      	              	//message.push(['putcol','','fee_date','2018-10-13']);
      	              	message.push(['putcol','','unit',null]);
      	              	message.push(['putcol','','price',fyxx.PRICE]);
      	              	message.push(['putcol','','dosage',fyxx.DOSAGE]);
      	              	message.push(['putcol','','money',fyxx.MONEY]);
      	              	message.push(['putcol','','usage_flag',fyxx.USAGE_FLAG]);
      	              	message.push(['putcol','','usage_days',null]);
      	              	message.push(['putcol','','opp_serial_fee',fyxx.OPP_SERIAL_FEE]);
      	              	message.push(['putcol','','hos_serial',null]);
      	              	message.push(['putcol','','remark',null]);
      	              	message.push(['putcol','','serial_apply',null]);
      	              	message.push(['putcol','','make_flag',null]);
      	              	message.push(['nextrow','']);
	        						
        			}
        		}
        		if(bxscmxList.length <= 0){
        			malert("没有可上传的费用信息！ ");
        			wscjl.ifClick = true;
        			return;
        		}
        		message.push(['run','']);
        		message.push(['destoryinterface','']);
        		
        		$("#scfyButton")[0].setAttribute("disabled", true);
    			$("#scfyButton")[0].style.background = '#aaaaaa';
        		if (socket.readyState===1) {
        			console.log(message);
                    socket.send(JSON.stringify(message));
                    var cs=0;
        			var interval=setInterval(function(){
        					cs+=1;
        					console.log(cs);
            				if(rs){
                				if(ifok){
                					console.log(rslmsg.error)
                					console.log(rslmsg)
                					if(rslmsg.run >= 0 && rslmsg.code=="success" && rslmsg.error == ''){
                						var param = {
                								list : 	{
                									'scmxList' : bxscmxList
                								}
                						};
                						wscjl.$http.post("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=inhospital&method=fysc"
                								 ,JSON.stringify(param)).then(
                			                		function(json) {
                              	                		 console.log(json);
                              	                             if (json.body.a == 0) {
                              	                                	malert(json.body.c);
                              	                                	wscjl.getData();
                              	                                 } else {
                              	                                     malert("His内部错误， "+json.body.c);
                              	                                 }
                			                        }, 
                			                        function(error) {
                			                        	malert(error,'top','success');
                			                        });
                					}else{
                						wscjl.error = '错误信息：' +rslmsg.error;
                						malert("上传费用失败！ "+rslmsg.error);
                					}
                					
                					clearInterval(interval);
                					rs=false;
                					ifok=false;
                				}else{
                					clearInterval(interval);
                					rs=false;
                					ifok=false;
                				}
                			}
            				if(cs>=10){
            					malert("医保超时,请重试！ ");
            					rs=false;
        	    				ifok=false;
            					clearInterval(interval);
            				}
            			},left_tab1.socketTime);
                }else{
                	malert("医保通信失败！ ");
                }
        		wscjl.ifClick = true;
         		//left_tab1.getBrData();
        		setTimeout(function () {
        			$("#scfyButton")[0].removeAttribute("disabled");
                  	$("#scfyButton")[0].style.background = '';
                }, 6000);
        	},
        	
        	fyCheckOne: function (index) {
                   this.fyisCheckAll = false;
                   this.isfyChecked = [];
                   this.isfyChecked[index] = true;
               },
            fyCheckAll: function (list) {
                   if (this.fyisCheckAll) {
                       for (var i = 0; i < this[list].length; i++) this.isfyChecked[i] = true;
                   } else {
                       this.isfyChecked = [];
                   }
               },
             fyCheckSome: function (index) {
                   if (!this.isfyChecked[index]) {
                       this.fyisCheckAll = false;
                       Vue.set(this.isfyChecked, index, false);
                   } else {
                       Vue.set(this.isfyChecked, index, true);
                   }
               },
        }
    })
    wscjl.getData();
//})();