/**
 * Created by mash on 2017/9/15.
 */
(function () {
    //
    var notice = new Vue({
        el: '.notice',
        data: {
            name: null,
            obj: {}
        },
        methods: {
            getPbTime: function (pbList) {
                for(var j = 0; j < pbList.length; j++){
                    var m = 1;
                    for(var k = 0; k < pbList.length; k++){
                        if(pbList[j].bcfamc == pbList[k].bcfamc) this.obj[pbList[j].bcfamc] = m++;
                    }
                }
                this.obj = Object.assign({}, this.obj);
            }
        }
    });


    var scheduling = new Vue({
        el: '.scheduling',
        mixins: [mformat, baseFunc],
        components: {
            'calendar': calendar,
        },
        data: {
            my: {},
            currentDate: '',
            selectDay: new Date(),
            toDay: new Date(),
            d:new Date(),
            pbList: [],
            selectDate: ''
        },
        watch: {
            pbList: function (val) {
                notice.getPbTime(val);
            },
            selectDate: function (val) {
                if(val == null || val == '') return false;
                var list = val.split("-");
                this.selectDay.setFullYear(list[0], list[1] - 1, list[2]);
                this.getPbInfo();
            }
        },
        methods: {
            // 根据当前时间计算这周的时间
            weekToDay: function (selectDay, week) {
                var day = new Date(1000 * 60 * 60 * 24 * (week - selectDay.getDay()) + selectDay.getTime());
                return day.getMonth() + 1 + '月' + day.getDate() + '日';
            },
            // 跳转到上一周
            prvWeek: function () {
                this.selectDay = new Date(1000 * 60 * 60 * 24 * (-7) + this.selectDay.getTime());
                this.getPbInfo();
            },
            // 跳转到下一周
            nextWeek: function () {
                this.selectDay = new Date(1000 * 60 * 60 * 24 * (7) + this.selectDay.getTime());
                this.getPbInfo();
            },
            // 获取当前操作员的信息
            getInfo: function () {
                $.getJSON('/actionDispatcher.do?reqUrl=GetUserInfoAction', function (data) {
                    scheduling.my = data.d;
                    notice.name = scheduling.my.czyxm;
                    scheduling.getPbInfo();
                    option.getPbsjap();
                })
            },
            // 获取排班的信息（根据是否传参来判断是增加的还是页面初始化查询）
            getPbInfo: function () {
                var week = this.selectDay.getDay();
                $(".addPb").remove();
                if(week == 0) week = 7;
                var first = new Date(1000 * 60 * 60 * 24 * (1 - week) + this.selectDay.getTime());
                var end = new Date(1000 * 60 * 60 * 24 * (7 - week) + this.selectDay.getTime());
                var firstDate = first.getFullYear() + '-' + (first.getMonth() + 1) + '-' + first.getDate();
                var endDate = end.getFullYear() + '-' + (end.getMonth() + 1) + '-' + end.getDate();
                var str_param = {
                    ksbm: this.my.ksbm,
                    page: 1,
                    rows: 100,
                    sort: 'sbsj',
                    order: 'asc',
                    beginrq: firstDate + ' 00:00:00',
                    endrq: endDate + ' 00:00:00',
                    rybm: scheduling.my.czybm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglPbglYspb&types=query&dg=" + JSON.stringify(str_param), function (json) {
                    scheduling.pbList = json.d.list;
                    var pbList = json.d.list;
                    var date = null, bg = null, color = null;
                    for (var i = 0; i < pbList.length; i++) {
                        date = new Date(pbList[i].sbsj);
                        var el = $('#' + pbList[i].rybm).find('td').eq(date.getDay() - 1);
                        for(var j = 0; j < option.optionList.length; j++){
                            if(pbList[i].bcfabm == option.optionList[j].bcfabm){
                                bg = option.optionList[j].colour;
                                color = option.optionList[j].color;
                            }
                        }
                        el.append("<div id='" + pbList[i].pbbid + "' data-type='"+pbList[i].bcfabm+"' class='optionBtu addPb'" +
                            "style='background: "+bg+";color: "+color+"'>" + pbList[i].bcfamc +
                            "<span class='fa fa-minus-circle'></span></div>");
                    }
                    scheduling.setToDayBg();
                });
            },
            setToDayBg: function () {
                setTimeout(function () { // 这里延迟来加载今天的背景色
                    if(scheduling.toDay.toString() == scheduling.selectDay.toString()){
                        var a = scheduling.toDay.getDay() - 1;
                        for (var i = 0; i < $('.toDayTable tr').length; i++) {
                            $('.toDayTable tr').eq(i).find('td').eq(a).addClass('toDayBg');
                        }
                    } else {
                        $('.toDayTable td').removeClass('toDayBg');
                    }
                }, 100);
            },
        }
    });
    scheduling.getInfo();


    var option = new Vue({
        el: '.option',
        mixins: [tableBase, mformat,dragFun],
        data: {
            pbName: null,
            optionList: []
        },
        methods:{
            //查询排班时间段划分
            getPbsjap: function(){
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBcfa&types=query&dg=" + JSON.stringify(this.param) + "&json={'syfw':'" + scheduling.my.ksbm + "'}", function (json) {
                    option.optionList=json.d.list;
                });
            },

        },
    });


   laydate.render({
        elem:'.date'
        , trigger: 'click'
        , theme: '#1ab394'
       ,value : new Date()
        ,done:function (value,data) {
            var val={
                0:value,
                1:'selectDate'
            }

            scheduling.resCalendar(val)
            scheduling.toDay=new Date(value+' '+scheduling.d.getHours()+':'+scheduling.d.getMinutes()+':'+scheduling.d.getSeconds())
        }
    });
})();