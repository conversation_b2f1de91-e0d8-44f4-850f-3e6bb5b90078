$(window).resize(function () {
    hzlb.brk_list()
});


var hzlb = new Vue({
    el: '#hzlb',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        kcList: [],
        zyhMsg: [],
        pageState: {
            ksList: {},
            ks: '',
            sort: 'xssx',
            zyType: '',
            order:'1',
            hldj: '0',
            kssj:'',
            jssj:'',
            bkzt: '0',
            jiansuoVal: '',
            brList: [],
            brgl:'qk',
            mtbr:'0'
        },
        scollType:false,
        isDoneCb:false,
        isActive:[],
        loadData:'',
        total:0,
        hldj_css: {
            "0": "",
            "1": "redCj",
            "2": "blue",
            "3": "yellow",
            "4": "green"
        },
        activeIndex:undefined,
        yzztlist: [],
        caqxContent: {},//参数权限对象
        csContent: {},
        param: {
            page: 1,
            rows: 15,
            sort: '',
            order: 'asc',
            parm: '',
        },
        bqcyrq:'',
        brListCheckBox: [],
        isChecked: [],
        isCheckAll: false,
        ztShow: [],
        oldActiveIndex: 0,
        num: 0,
        lsBrList: [],
        brk_listD: 0,
        noticeContent: {},
		zycymc:'在院',
		xzlx_tran:{
			'310':'职工基本医疗保险',
			'320':'公务员医疗补助',
			'330':'大额医疗费用补助',
			'340':'离休人员医疗保障',
			'390':'城乡居民基本医疗保险',
			'392':'城乡居民大病医疗保险',
			'510':'生育保险',
		},
        userPhoto: [
            "/newzui/pub/image/maleBaby.png",
            "/newzui/pub/image/femalebaby.png",
            "/newzui/pub/image/Group <EMAIL>",
            "/newzui/pub/image/Group <EMAIL>",
            "/newzui/pub/image/juvenile.png",
            "/newzui/pub/image/maid.png",
            "/newzui/pub/image/youth.png",
            "/newzui/pub/image/woman.png",
            "/newzui/pub/image/grandpa.png",
            "/newzui/pub/image/grandma.png",
            "/newzui/pub/image/<EMAIL>"
        ],
        czList: [{
            name: "医嘱审核",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "0",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "医嘱执行",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "2",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "药品申领",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "3",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "退药申请",
            clickBc: function (brIndex) {
            	hzlb.caozuo('tysq', [hzlb.pageState.brList[brIndex]]);
            }
        }, {
            name: "执行单",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "4",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "停嘱审核",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "1",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "费用记账",
            clickBc: function (brIndex) {
                sessionStorage.setItem("hszToZygl"+userId, JSON.stringify({
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    brxx: hzlb.pageState.brList[brIndex],
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    ksmc: hzlb.pageState.brList[brIndex].ryksmc,
                }));
                hzlb.topNewPage('费用记账', 'page/zygl/rcygl/fyjz/fyjz.html','N050022011');
            }
        }, {
            name: "退费",
            clickBc: function (brIndex) {
                sessionStorage.setItem('jztf', JSON.stringify({
                    type: "jztf",
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    brxx: hzlb.pageState.brList[brIndex],
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                }));
                hzlb.topNewPage('记账退费', 'page/zygl/rcygl/fyjz/jztf.html');
                // hzlb.topNewPage('记账退费', 'page/zygl/rcygl/rydj/rydj.html');
            }
        }, {
            name: "医嘱单",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "5",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "病历书写",
            clickBc: function (brIndex,br) {
              hzlb.dzbl(br)
            }
        }, {
            name: "跌到坠床压疮",
            clickBc:function (index) {
                ddzcyc.brInfo = JSON.parse(JSON.stringify(hzlb.pageState.brList[index]));
                ddzcyc.open();
            }
        }, {
            name: "报告单",
            clickBc: function (brIndex) {
            	 sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                     msgtype: "10",
                     ksbm: hzlb.pageState.brList[brIndex].ryks,
                     zyh: hzlb.pageState.brList[brIndex].zyh,
                     zyType:hzlb.pageState.zyType
                 }));
                 hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "皮试结果录入",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "4",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "体温单",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "6",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
				                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }
		// , {
  //           name: "护理记录",
  //           clickBc: function (brIndex) {
  //               sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
  //                   msgtype: "7",
  //                   ksbm: hzlb.pageState.brList[brIndex].ryks,
  //                   zyh: hzlb.pageState.brList[brIndex].zyh,
  //                   zyType:hzlb.pageState.zyType
  //               }));
  //               hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
  //           }
  //       }
		// , {
  //           name: "危重记录",
  //           clickBc: function (brIndex) {
  //               sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
  //                   msgtype: "8",
  //                   ksbm: hzlb.pageState.brList[brIndex].ryks,
  //                   zyh: hzlb.pageState.brList[brIndex].zyh,
  //                   zyType:hzlb.pageState.zyType
  //               }));
  //               hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
  //           }
  //       }
        // , {
        //     name: "新生儿护理记录"
        // }
        , {
            name: "固定费用",
            clickBc: function (brIndex) {
                sessionStorage.setItem("gdfy", JSON.stringify(hzlb.pageState.brList[brIndex]));
                hzlb.topNewPage('固定费用', 'page/hsz/hlyw/bygl/gdfy.html');
            }
        }, {
            name: "更换主管医生",
            clickBc: function (brIndex) {
                hzlb.changeDoc(brIndex);
            }
        }, {
            name: "病人转科",
            clickBc: function (brIndex) {
                if (hzlb.pageState.brList[brIndex].rycwbh == null || hzlb.pageState.brList[brIndex].rycwbh == undefined || hzlb.pageState.brList[brIndex].rycwbh == '') {
                    malert("该患者未接科，请先接科安床！", 'top', "defeadted");
                    return
                }
                zkac.zkhcContent = hzlb.pageState.brList[brIndex];
                zkac.getData();
                zkac.open();
            }
        }, {
            name: "病人换床",
            clickBc: function (brIndex) {
                qccl.getYsData();
                qccl.getCwData();
                qccl.brInfo = hzlb.pageState.brList[brIndex];
                qccl.open();
            }
        }, {
            name: "病区出院",
            clickBc: function (brIndex) {
                hzlb.brzyh =hzlb.pageState.brList[brIndex].zyh;
                hzlb.caozuo('bqcy', [hzlb.pageState.brList[brIndex]]);
            }
        }, {
            name: "取消出院",
            clickBc: function (brIndex) {
                hzlb.brzyh =hzlb.pageState.brList[brIndex].zyh;
                hzlb.caozuo('qxbqcy', [hzlb.pageState.brList[brIndex]]);
            }
        }, {
            name: "费用清单",
            clickBc: function (brIndex) {
                sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                    msgtype: "9",
                    ksbm: hzlb.pageState.brList[brIndex].ryks,
                    zyh: hzlb.pageState.brList[brIndex].zyh,
                    zyType:hzlb.pageState.zyType
                }));
                hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
            }
        }, {
            name: "补打腕带[成人]",
            clickBc: function (brIndex) {
                hzlb.wdbd(hzlb.pageState.brList[brIndex],0);
            }
        }, {
            name: "补打腕带[婴儿]",
            clickBc: function (brIndex) {
                hzlb.wdbd(hzlb.pageState.brList[brIndex],1);
            }
        }, {
            name: "补打腕带[普儿]",
            clickBc: function (brIndex) {
                hzlb.wdbd(hzlb.pageState.brList[brIndex],2);
            }
        },{
                name: "病历查看",
                clickBc: function (brIndex,br) {
                    hzlb.dzblck(br)
                }
         },{
                name: "护理评估[新]",
                clickBc: function (brIndex,br) {
					                    hzlb.hlpgx(br)
                }
         },{
                name: "血糖打印[新]",
                clickBc: function (brIndex,br) {
                                        console.log(hzlb.pageState.brList[brIndex]);
                    let rysq =  hzlb.fDate(hzlb.pageState.brList[brIndex].ryrq,'date');
                    let dqrq = window.insuranceGbUtils.fDate('date');
                    window.open('http://220.220.220.15:81/dms/Pages/Report/Patient_FixedRecord.aspx?PatientBedNo=&PatientCode='+hzlb.pageState.brList[brIndex].zyh+'&StartDate='+rysq+'&EndDate='+dqrq+'&needAutoSearch=1','_blank');
                }
            }
		 
		 ]
    },
    watch: {
        'pageState.brList': function (val, oldVal) {
            if (val !== oldVal) {
                this.brListCheckBox = this.pageState.brList;
                this.isChecked = [];
            }
        },
        'isChecked': function (val, oldVal) {
            var length = this.brListCheckBox.length,
                isTrueAll = false;
            for (var i = 0; i < length; i++) {
                if (!val[i]) {
                    this.isCheckAll = false;
                    return false;
                } else isTrueAll = true;
            }
            if (isTrueAll) {
                this.isCheckAll = true;
            }
        },
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {
        Mask.newMask(this.MaskOptions('bqcyrq'));
        Mask.newMask(this.MaskOptions('kssj'));
        Mask.newMask(this.MaskOptions('kssj1'));
        if (loadPage.stateMap.hzlb !== undefined) {
            this.pageState = loadPage.stateMap.hzlb;
            this.getCsqx();
            this.getHzlb();
			this.getKc();
        } else {
            this.getKsList(function () {
                hzlb.pageState.zyType = 'zy';
                hzlb.getCsqx();
                hzlb.getHzlb();
				hzlb.getKc();
                hzlb.filterZyh(window.top.navli.noticeList)
                ghys.getData({ysbz: '1'},'zgysList','jsonListYsData')
                ghys.getData({hsbz: '1'},'hsListData','jsonListHsData')
            });
        }
        sessionStorage.setItem('hszHzlbUpdate', 1);
        window.addEventListener('storage', function (e) {
            if (e.key == 'hszHzlbUpdate' && e.oldValue !== e.newValue) {
                hzlb.pageState.brList=[]
                hzlb.getHzlb();
            }
        });
        window.top.navli.$watch('noticeList',function (newValue,oldValue) {
            hzlb.filterZyh(newValue)
        })
    },
    methods: {
		hlpgx:function(br){
			if (!br.zyh) {
			    malert("请先选择病人后再书写病历！", 'top', 'defeadted');
			    return
			}
			this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction').then(function (json) {
			    if (json.body.d != null) {
                    let zd = ['czybm','czykl','czyxm']
                    let params = {}
                    zd.forEach(element => {
                        if(Object.prototype.hasOwnProperty.call(json.body.d,element))
                            params[element] = json.body.d[element]
                    });
                                        
					// var url = "http://*************:30800/hlpg_index?zyh="+base64encode(utf16to8(br.zyh))+"&userInfo="+window.encodeURIComponent(base64encode(utf16to8(JSON.stringify(params))))+"&ryzd="+window.encodeURIComponent(base64encode(utf16to8(br.ryzdbm)));
					var url = "http://localhost:9527/hlpg_index?zyh="+base64encode(utf16to8(br.zyh))+"&userInfo="+window.encodeURIComponent(base64encode(utf16to8(JSON.stringify(params))))+"&ryzd="+window.encodeURIComponent(base64encode(utf16to8(br.ryzdbm)))+"&ryzdmc="+window.encodeURIComponent(base64encode(utf16to8(br.ryzdmc)))+"&ksbm="+base64encode(utf16to8(json.body.d.ksbm));
					window.open(url);
			    }
			});
			
			
		},
        filterZyh:function(newValue){
            var zyhMsg=[];
            for (var i = 0; i <newValue.length ; i++) {
                if(newValue[i].msgtype == '0' && newValue[i].ksbm==this.pageState.ks){
                    zyhMsg.push(newValue[i].sbid.split('_')[0])
                }
            }
            hzlb.zyhMsg=this.getZyh(this.distinct(zyhMsg));
        },
        getZyh:function(list){
            var obj={};
            for (var i = 0; i <list.length ; i++) {
                obj[list[i]]=list[i]
            }
            return obj
        },
        wdbd:function(br,sfcr){
            var bm = 'N010024008';
            var strPrint;
                if(sfcr == '0'){
                    strPrint=[{reportlet: 'fpdy%2Fzygl%2Fzygl_crwddy.cpt',zyh:br.zyh}];
                }else if(sfcr == '1'){
                    strPrint=[{reportlet: 'fpdy%2Fzygl%2Fzygl_xsrwddy.cpt',zyh:br.zyh}];
                }else if(sfcr == '2'){
                    strPrint=[{reportlet: 'fpdy%2Fhsz%2Fswd_pewddy.cpt',zyh:br.zyh}];
                }
                this.frPrint(strPrint,bm);
        },


        frPrint:function(printData,bm){
            window.top.J_tabLeft.csqxparm.csbm = bm;
            var ZyyjPrint='';
            //打印方法调整致取参数之后
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0) {
                    if (json.d != null && json.d.length > 0) {
                        ZyyjPrint = json.d[0].csz;
                    }
                }
                FrPrint(printData, ZyyjPrint)
            });
        },
        dzbl: function (br) {
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + br.brid + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    malert('信息上传成功！', 'top', 'success')
                } else {
                    malert('信息上传失败失败：' + data.body.c, 'top', 'defeadted')
                }
            });
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=" + br.zyh + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    malert('信息上传成功！', 'top', 'success')
                } else {
                    malert('信息上传失败失败：' + data.body.c, 'top', 'defeadted')
                }
            });
            var sxdz = "";
            // var user = "";
            var password = "";
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    hzlb.csContent = JSON.parse(JSON.stringify(json.d.list[0]));
                    sxdz = hzlb.csContent.blSxdz;
                    // user = userId;
                }
            });
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                if (json.a == "0") {
                    password = json.d.password;
                }
            });
            if (!sxdz) {
                malert("书写地址为空，打开病历失败！", 'top', 'defeadted');
                return
            }
            if (!userId) {
                malert("用户名为空，打开病历失败！！", 'top', 'defeadted');
                return
            }
            if (!password) {
                malert("用户密码为空，打开病历失败！", 'top', 'defeadted');
                return
            }
            if (!br.zyh) {
                malert("请先选择病人后再书写病历！", 'top', 'defeadted');
                return
            }
            var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=" + br.zyh + ",userid=" + userId + ",password=" + password + ",lyzyhmz=0,blhhl=1";
            window.open(url);
        },
        dzblck: function (br) {
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + br.brid + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    malert('信息上传成功！', 'top', 'success')
                } else {
                    malert('信息上传失败失败：' + data.body.c, 'top', 'defeadted')
                }
            });
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=" + br.zyh + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    malert('信息上传成功！', 'top', 'success')
                } else {
                    malert('信息上传失败失败：' + data.body.c, 'top', 'defeadted')
                }
            });
            var sxdz = "";
            // var user = "";
            var password = "";
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    hzlb.csContent = JSON.parse(JSON.stringify(json.d.list[0]));
                    sxdz = hzlb.csContent.blSxdz;
                    // user = userId;
                }
            });
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                if (json.a == "0") {
                    password = json.d.password;
                }
            });
            if (!sxdz) {
                malert("书写地址为空，打开病历失败！", 'top', 'defeadted');
                return
            }
            if (!userId) {
                malert("用户名为空，打开病历失败！！", 'top', 'defeadted');
                return
            }
            if (!password) {
                malert("用户密码为空，打开病历失败！", 'top', 'defeadted');
                return
            }
            if (!br.zyh) {
                malert("请先选择病人后再书写病历！", 'top', 'defeadted');
                return
            }
            var url = sxdz + "/BLCX/DSEMRHISQuery?sn=zyh=" + br.zyh + ",userid=" + userId + ",password=" + password + ",lyzyhmz=0,blhhl=1";
            window.open(url);
        },
        activeClick:function(index){
            this.activeIndex=index
        },
        kongchuang: function () {
            this.pageState.brList = [];
            hzlb.pageState.brList = hzlb.kcList || [];
        },
        filterFun: function (index, zt) {
            this.pageState.brList = [];
            for (var i = 0; i < hzlb.lsBrList.length; i++) {
                if (index == hzlb.lsBrList[i][zt]) {
                    this.pageState.brList.push(hzlb.lsBrList[i]);
                }
            }
        },
        filterFunz: function () {
            this.pageState.brList = hzlb.lsBrList
        },
        filterFunq: function () {
            this.pageState.brList = [];
            for (var i = 0; i < hzlb.lsBrList.length; i++) {
                if (hzlb.lsBrList[i].yjhj+hzlb.lsBrList[i].dbje - hzlb.lsBrList[i].fyhj < 0) {
                    this.pageState.brList.push(hzlb.lsBrList[i]);
                }
            }
        },
        getQxbqcyCwData:function(){
            qxbqcy.initCw();
        },
        cydj: function () {
            var ksObj = {
                ksbm: hzlb.pageState.ks
            }
            sessionStorage.setItem('cyksbm', JSON.stringify(ksObj));
            var djhtml = !hzlb.caqxContent.cs004200272 || hzlb.caqxContent.cs004200272 == "0"?"":"new";
            this.topNewPage('采样登记', 'page/hsz/hlyw/yzcl/loading-page/cydj'+djhtml+".html");
        },
        jkacOpen:function(){
            this.topNewPage('接科安床', 'page/hsz/hlyw/bygl/bygl.html');
        },
		csqdcsh:function(){
		    window.insuranceGbUtils.init();
			
			let input = {
				tran_time:window.insuranceGbUtils.fDate('AllDate'),
				data : {
					psn_no:userId,
					fixmedins_code:window.insuranceGbUtils.fixmedins_code
				}
			}
			window.insuranceGbUtils.call('1101','');
			// window.insuranceGbUtils.qd();
			
			// window.insuranceGbUtils.loginout();
			
		},
		
		
        brk_list: function () {
            if (this.pageState.brList.length > 5) {
                this.brk_listD = parseInt(this.$refs.kp.offsetWidth / 270) - (this.pageState.brList.length % parseInt(this.$refs.kp.offsetWidth / 270))
            }
        },
        show: function (index) {
            if (index != this.num) {
                this.num = index;
                if (index == 0) {
                    $('body').css({
                        'padding-bottom': '10px',
                    });
                    $('.wrapper').css({
                        'padding-bottom': '28px!important',
                    });
                } else if (index == 1) {
                    $('body').css({
                        'padding-bottom': '10px',
                    });
                    $('.wrapper').css({
                        'padding-bottom': '0px!important',
                    });
                }
            }
        },
        duoxuan: function (index) {
            if (event.shiftKey) {
                var min = this.oldActiveIndex < index ? this.oldActiveIndex : index,
                    max = this.oldActiveIndex < index ? index : this.oldActiveIndex;
                for (var i = min; i <= max; i++) {
                    this.isChecked[i] = this.isChecked[index];
                }
            }
            this.oldActiveIndex = index;
        },
        reCheckBoxCB: function (arg) {
            this.reCheckBox(arg);
            this.duoxuan(arg[1]);
        },
        //获取参数权限
        getCsqx: function () {
            var ksId = hzlb.pageState.ks;
            var parm = {
                "ylbm": 'N030042002',
                "ksbm": ksId
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0  && json.d) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03004200201": //申领药品是否判断库存  0=不判断,1=判断
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100101 = csjson.csz;
                                    }
                                    break;
                                case "N03004200202": //取消审核医嘱权限  0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100102 = csjson.csz;
                                    }
                                    break;
                                case "N03004200203": //审核转科医嘱是否停用执行医嘱0=否，1=是
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100103 = csjson.csz;
                                    }
                                    break;
                                case "N03004200204": //审核出院医嘱是否停用执行医嘱0=否，1=是
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100104 = csjson.csz;
                                    }
                                    break;
                                //		 						case "N03004200205": //是否允许修改审核时间0=否，1=是
                                //		 							if(csjson.csz) {
                                //		 								hzlb.caqxContent.cs00900100105 = csjson.csz;
                                //		 							}
                                //		 							break;
                                case "N03004200206": //医嘱是否语音提示0=否，1=是
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100106 = csjson.csz;
                                    }
                                    break;
                                case "N03004200207": //取消停嘱审核权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100107 = csjson.csz;
                                    }
                                    break;
                                case "N03004200208": //确定出院权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100108 = csjson.csz;
                                    }
                                    break;
                                case "N03004200209": //取消出院权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100109 = csjson.csz;
                                    }
                                    break;
                                case "N03004200210": //取消执行长期医嘱权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100110 = csjson.csz;
                                    }
                                    break;
                                case "N03004200211": //取消药品申领权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100111 = csjson.csz;
                                    }
                                    break;
                                case "N03004200212": //退药申请权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100112 = csjson.csz;
                                    }
                                    break;
                                case "N03004200213": //费用记账权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100113 = csjson.csz;
                                    }
                                    break;
                                case "N03004200214": //记账退费权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100114 = csjson.csz;
                                    }
                                    break;
                                case "N03004200215": //退费申请权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100115 = csjson.csz;
                                    }
                                    break;
                                case "00900100116": //更改主管医生权限0=无，1＝有
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100116 = csjson.csz;
                                    }
                                    break;
                                case "N03004200216": //医嘱扣费是否判断余额0=不判断，1＝判断
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100117 = csjson.csz;
                                    }
                                    break;
                                case "N03004200218": //主管医生选择范围0=默认当前病区医生，1=全院医生
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100118 = csjson.csz;
                                    }
                                    break;
                                case "N03004200219": //病区出院是否判断检查检验是否扣费、药品发药等0=不判断，1=判断
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100119 = csjson.csz;
                                    }
                                    break;
                                case "N03004200220": //申领药品是否同时发药0=否，1=是
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100120 = csjson.csz;
                                    }
                                    break;
                                case "N03004200221": //病区退药申请后是否自动审核0=否，1=是
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100121 = csjson.csz;
                                    }
                                    break;
                                case "N03004200222": //病人帐户默认开始统计时间0-当天 1-入院日期
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100122 = csjson.csz;
                                    }
                                    break;
                                case "N03004200223": //长期医嘱是否每天可以多次执行0=不允许，1=允许
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs00900100123 = csjson.csz;
                                    }
                                    break;
                                case "N03004200236": //申请退药默认药房
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs004200236 = csjson.csz;
                                    }
                                    break;
                                case "N03004200272": //检验标本是否采样环节 0-否 1-是
                                    if (csjson.csz) {
                                        hzlb.caqxContent.cs004200272 = csjson.csz;
                                    }
                                    break;
                                case "N03004200707": //药房是否显示 0 显示- 1 不显示
                                    if (csjson.csz) {
                                        hzlb.caqxContent.N03004200707 = csjson.csz;
                                    }
                                    break;
                                //		 						case "00900100124": //执行医嘱自动药品申领0=否，1=是
                                //		 							if(csjson.csz) {
                                //		 								hzlb.caqxContent.cs00900100124 = csjson.csz;
                                //		 							}
                                //		 							break;
                            }
                        }
                } else {
                    malert("参数权限获取失败" + json.c, 'top', 'defeadted');
                }
            });
        },
        openYz: function (brInfo) {
            var brlist = [];
            brlist.push(brInfo);
            var brjson = {
                brlist: brlist,
                ksid: hzlb.pageState.ks,
            };
            sessionStorage.setItem("yzlb", JSON.stringify(brjson));
            this.topNewPage('医嘱列表', 'page/hsz/hlyw/yzcl/loading-page/yzlb.html');
        },

        ztClick: function (index) {
            //            if( this.ztShow[index]==true){
            //                this.ztShow[index]=false;
            //            }else{
            //                this.ztShow[index]=true;
            //            }
        },
        //获取科室列表
        getZyKsList:function(){  //页面加载时自动获取挂号科室Dddw数据
            var parm_str = {
                rows:99999,
                page:1
            };
            var bean = {"zyks":"1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json="+JSON.stringify(bean) + "&dg=" + JSON.stringify(parm_str),function (json) {
                if (json.a == 0 && json.d) {
                    zkac.Kslist = json.d.list;
                } else {
                    malert("获取科室列表失败!", 'top', 'defeadted')
                }
            });
        },
        getKsList: function (cb) {
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"N030042002"}', function (json) {
                if (json.a == 0 && json.d) {
                    var rlksList = [];
                    for (var i = 0; i < json.d.length; i++) {
                        if (json.d[i].bqbm != null) {
                            rlksList.push(json.d[i]);
                        }
                    }
                    length = rlksList.length;
                    ksList = {};
                    for (var i = 0; i < length; i++) {
                        ksList[rlksList[i].ksbm] = rlksList[i].ksmc;
                    }
                    hzlb.pageState.ksList = ksList;
                    hzlb.pageState.ks = rlksList[0].ksbm;
                    cb && cb(ksList);
                    hzlb.getZyKsList();
                } else {
                    malert("获取科室列表失败!", 'top', 'defeadted')
                }
            });
        },
        loadingData:function () {
            if(event.wheelDelta>0)return
            if(this.scollType){
                this.scollType=false
                if(this.pageState.brList.length<this.total){
                    // if(this.uilPageBottom()){
                        this.param.page=this.param.page+1;this.getHzlb();
                    // }
                }else {
                    this.loadData='暂无更多数据...'
                }
            }
        },
        getData:function(){
            hzlb.pageState.brList=[]
            this.param.page=1
            this.getHzlb()
        },
        //获取患者列表
        getHzlb: function () {
            common.openBar('#brCard');
            var ajaxUrl = '/actionDispatcher.do?reqUrl=';
            // 在院状态
            if(this.pageState.brgl == 'zgbr'){
                this.pageState['zrhs'] = userId;
            }else{
                this.pageState['zrhs'] = '';
            }
            var mtbbz = null;
            if (this.pageState.mtbr == '1')
            {
                mtbbz = '1';
            } else if (this.pageState.mtbr == '2') {
                mtbbz = '0';
            }
            switch (this.pageState.zyType) {
                case 'zy': //在院
                    var json = {
                        ryks: this.pageState.ks,
                        parm: this.pageState.parm,
                        mtbbz: mtbbz,
                        page: this.param.page,
                        rows: 2000,
                        sort:this.pageState.sort,
                      //  sort1:'1',
                      //  order2:'1',
                    };
                    if (this.pageState.order == '1'){
                        json.order = 'asc';
                    } else {
                        json.order = 'desc';
                    }
                    if (this.pageState.sort == 'rycwbh'){
                        json.sort1 = '1';
                    } else if(this.pageState.sort == 'brxm'){
                        json.sort2 = '1';
                    }else{//zyh
                        json.sort3 = '1';
                    }
                    ajaxUrl = ajaxUrl + 'New1ZyysYsywYzcl&types=zyhzxx&parm=' + JSON.stringify(json) + "&zrhs=" + this.pageState['zrhs'];
                    break;
                case 'bqcy':
                    var json = {
                        ryks: this.pageState.ks,
                        cyks: this.pageState.ks,
                        mtbbz: mtbbz,
                        parm: this.pageState.parm,
                        page: this.param.page,
                        rows: 200,
                        sort:this.pageState.sort,
                        beginrq:this.pageState.beginrq,
                        endrq:this.pageState.endrq,
                        cxrq:'bqcyrq',
                    };
                    if (this.pageState.order == '1'){
                        json.order = 'asc';
                    } else {
                        json.order = 'desc';
                    }
                    //parm.cyks = this.pageState.ks;
                    ajaxUrl = ajaxUrl + 'New1ZyysYsywYzcl&types=bqcyhzxx&parm=' + JSON.stringify(json) + "&zrhs=" + this.pageState['zrhs'];
                    break;
                case 'jscy':
                    var json = {
                        ryks: this.pageState.ks,
                        cyks: this.pageState.ks,
                        mtbbz: mtbbz,
                        parm: this.pageState.parm,
                        page: this.param.page,
                        rows: 200,
                        beginrq:this.pageState.beginrq,
                        endrq:this.pageState.endrq,
                        cxrq:'bqcyrq',
                        sort:this.pageState.sort
                    };
                    if (this.pageState.order == '1'){
                        json.order = 'asc';
                    } else {
                        json.order = 'desc';
                    }
                    if (this.pageState.sort == 'rycwbh'){
                        json.sort1 = '1';
                    } else if(this.pageState.sort == 'brxm'){
                        json.sort2 = '1';
                    }else{//zyh
                        json.sort3 = '1';
                    }
                    //parm.cyks = this.pageState.ks;
                    ajaxUrl = ajaxUrl + 'New1ZyysYsywYzcl&types=Rcyhzxx&parm=' + JSON.stringify(json) + "&zrhs=" + this.pageState['zrhs'];
                    break;
                case 'zk': //转院
                    ajaxUrl = ajaxUrl + 'New1HszHlywYzclCx&types=zchz&ksbm=' + this.pageState.ks + "&zrhs=" + this.pageState['zrhs'];
                    break;
            }
            common.openloading('.zui-table-body');
            $.getJSON(ajaxUrl, function (json) {
				                if (json.a == 0) {
                    common.closeLoading()
                    hzlb.scollType=true
                    hzlb.isDoneCb=false;
                    hzlb.totlePage = Math.ceil(json.d.total / hzlb.param.rows);
                    var start = new Date(new Date(new Date().toLocaleDateString()).getTime());
                    // if(hzlb.pageState.order=='0'){
                    //     json.d.list.sort(function (a,b) {
                    //         return b[hzlb.pageState.sort]-a[hzlb.pageState.sort]
                    //     })
                    // }else{
                    //     json.d.list.sort(function (a,b) {
                    //         return a[hzlb.pageState.sort]-b[hzlb.pageState.sort]
                    //     })
                    // }
					hzlb.pageState.brList=json.d.list;
                    // if(hzlb.num==0){
                    //     hzlb.pageState.brList=hzlb.pageState.brList.concat(json.d.list)
                    // }else{
                    //     hzlb.pageState.brList=json.d.list
                    // }
                    hzlb.lsBrList = json.d.list;
                    //用作下方提示栏
                    var zyrs = 0;
                    var jrry = 0;
                    var qfrs = 0;
                    var yjhl = 0;
                    var ejhl = 0;
                    var sjhl = 0;
                    var tjhl = 0;
                    var bwrs = 0;
                    var bzrs = 0;
                    for (var i = 0; i < hzlb.pageState.brList.length; i++) {
                        if (hzlb.pageState.brList[i].nl < 7 && hzlb.pageState.brList[i].brxb == '1') {
                            hzlb.pageState.brList[i].nljd = '1';
                        } else if (hzlb.pageState.brList[i].nl < 7 && hzlb.pageState.brList[i].brxb == '2') {
                            hzlb.pageState.brList[i].nljd = '2';
                        } else if (hzlb.pageState.brList[i].nl < 18 && hzlb.pageState.brList[i].nl > 6 && hzlb.pageState.brList[i].brxb == '1') {
                            hzlb.pageState.brList[i].nljd = '3';
                        } else if (hzlb.pageState.brList[i].nl < 18 && hzlb.pageState.brList[i].nl > 6 && hzlb.pageState.brList[i].brxb == '2') {
                            hzlb.pageState.brList[i].nljd = '4';
                        } else if (hzlb.pageState.brList[i].nl < 41 && hzlb.pageState.brList[i].nl > 17 && hzlb.pageState.brList[i].brxb == '1') {
                            hzlb.pageState.brList[i].nljd = '5';
                        } else if (hzlb.pageState.brList[i].nl < 41 && hzlb.pageState.brList[i].nl > 17 && hzlb.pageState.brList[i].brxb == '2') {
                            hzlb.pageState.brList[i].nljd = '6';
                        } else if (hzlb.pageState.brList[i].nl < 66 && hzlb.pageState.brList[i].nl > 40 && hzlb.pageState.brList[i].brxb == '1') {
                            hzlb.pageState.brList[i].nljd = '7';
                        } else if (hzlb.pageState.brList[i].nl < 66 && hzlb.pageState.brList[i].nl > 40 && hzlb.pageState.brList[i].brxb == '2') {
                            hzlb.pageState.brList[i].nljd = '8';
                        } else if (hzlb.pageState.brList[i].nl > 65 && hzlb.pageState.brList[i].brxb == '1') {
                            hzlb.pageState.brList[i].nljd = '9';
                        } else if (hzlb.pageState.brList[i].nl > 65 && hzlb.pageState.brList[i].brxb == '2') {
                            hzlb.pageState.brList[i].nljd = '10';
                        } else {
                            hzlb.pageState.brList[i].nljd = '11';
                        }

                        if(hzlb.pageState.brList[i].nl == 0 ){
                            hzlb.pageState.brList[i].nl= null;
                            hzlb.pageState.brList[i].nldw = null;
                        }
                        //判断是否今日入院
                        if (hzlb.pageState.brList[i].ryrq >= start) {
                            hzlb.pageState.brList[i].jrrybz = '1';
                        } else {
                            hzlb.pageState.brList[i].jrrybz = '0';
                        }
                        zyrs++;
                        if (hzlb.pageState.brList[i].jrrybz == '1') {
                            jrry++;
                        }
                        if (hzlb.pageState.brList[i].yjhj+hzlb.pageState.brList[i].dbje - hzlb.pageState.brList[i].fyhj < 0) {
                            qfrs++;
                        }
                        if (hzlb.pageState.brList[i].hldj == '1') {
                            tjhl++;
                        }
                        if (hzlb.pageState.brList[i].hldj == '2') {
                            yjhl++;
                        }
                        if (hzlb.pageState.brList[i].hldj == '3') {
                            ejhl++;
                        }
                        if (hzlb.pageState.brList[i].hldj == '4') {
                            sjhl++;
                        }
                        if (hzlb.pageState.brList[i].bqdj == '1') {
                            bzrs++;
                        }
                        if (hzlb.pageState.brList[i].bqdj == '2') {
                            bwrs++;
                        }
                    }
                    hzlb.noticeContent.zyrs = zyrs;
                    hzlb.noticeContent.jrry = jrry;
                    hzlb.noticeContent.qfrs = qfrs;
                    hzlb.noticeContent.yjhl = yjhl;
                    hzlb.noticeContent.ejhl = ejhl;
                    hzlb.noticeContent.sjhl = sjhl;
                    hzlb.noticeContent.tjhl = tjhl;
                    hzlb.noticeContent.bzrs = bzrs;
                    hzlb.noticeContent.bwrs = bwrs;
                    hzlb.pageState = Object.assign({}, hzlb.pageState);
                    console.log(hzlb.pageState);
                    hzlb.brk_list();
                    common.closeLoading();
                    hzlb.dzxShow();
                    hzlb.dtzShow();
                } else {
                    hzlb.isDoneCb=false;
                    common.closeLoading()
                    malert("获取患者列表失败!" + json.c, 'top', 'defeadted')
                }
            });

        },
        getKc: function () {
			            var kcsl = 0;
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=querykc&parm=" + JSON.stringify({ ksbm: this.pageState.ks }), function (json) {
                if (json.a == 0) {
                    if (json.d)
                        hzlb.kcList = json.d.list || [];
                    for (var i = 0; i < hzlb.kcList.length; i++) {
                        hzlb.kcList[i].brxm = '无';
                        hzlb.kcList[i].rycwbh = hzlb.kcList[i].cwbh;
                       // hzlb.kcList[i].isKc = true;
                        kcsl++;
                    }
                    hzlb.noticeContent.kc = kcsl;
                    hzlb.kcList = Object.assign({}, hzlb.kcList);
                } else {
                    malert("获取空床失败！", "top", "defeadted");
                }
            });
        },
		getQueryKc: function () {
			var kcsl = 0;
			hzlb.pageState.brList = [];
			hzlb.pageState.brList = hzlb.kcList;
		    $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=querykc&parm=" + JSON.stringify({ ksbm: this.pageState.ks }), function (json) {
		       				if (json.a == 0) {
					kcsl=0;
					if (json.d)
					    hzlb.kcList = json.d.list || [];
					for (var i = 0; i < hzlb.kcList.length; i++) {
					    hzlb.kcList[i].brxm = '无';
					    hzlb.kcList[i].rycwbh = hzlb.kcList[i].cwbh;
					   // hzlb.kcList[i].isKc = true;
					    kcsl++;
					}
					hzlb.noticeContent.kc = kcsl;
					hzlb.kcList = Object.assign({}, hzlb.kcList);
					hzlb.pageState.brList = hzlb.kcList;
					 
		            // hzlb.noticeContent.kc = kcsl;
		           // hzlb.kcList = Object.assign({}, hzlb.kcList);
		        } else {
		            malert("获取空床失败！", "top", "defeadted");
		        }
		    });
		},
        dzxShow: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=yzzx&ksbm=' + hzlb.pageState.ks,
                function (json) {
                    if (json.a =='0' &&json.d && json.d.list.length > 0) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                if (json.d.list[i].yzxx[int].xmbm == json.d.list[i].yzxx[int].xmmc) {
                                    json.d.list[i].yzxx[int].numb = 1;
                                }
                                if (json.d.list[i].yzxx[int].numb == '0') {
                                    json.d.list[i].dzxshow = true;
                                    break
                                }
                            }
                        }
                        for (var i = 0; i < hzlb.pageState.brList.length; i++) {
                            for (var j = 0; j < json.d.list.length; j++) {
                                if (hzlb.pageState.brList[i].zyh == json.d.list[j].zyh) {
                                    hzlb.pageState.brList[i].dzxshow = json.d.list[j].dzxshow;
                                }
                            }
                        }
                    }
                }, function (error) {
                    console.log(error);
                });
        },
        dtzShow: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=hstzsh&ksbm=' + hzlb.pageState.ks,
                function (json) {
                    if (json.d!=null && json.d.list.length > 0) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                if (json.d.list[i].yzxx[int].hstzbz == '0') {
                                    json.d.list[i].dtzshow = true;
                                    break
                                }
                            }
                        }
                        for (var i = 0; i < hzlb.pageState.brList.length; i++) {
                            for (var j = 0; j < json.d.list.length; j++) {
                                if (hzlb.pageState.brList[i].zyh == json.d.list[j].zyh) {
                                    hzlb.pageState.brList[i].dtzshow = json.d.list[j].dtzshow;
                                }
                            }
                        }
                    }
                    hzlb.pageState = Object.assign({}, hzlb.pageState);
                }, function (error) {
                    console.log(error);
                });
        },
        //    下拉table选择回调
        selectCB: function (item) {
			console.log(item);
            this.pageState[item[2][1]] = item[0];
            this.pageState['zrhs'] = null;
            hzlb.pageState.brList=[];
            this.getHzlb();
			this.getKc();
            ghys.fliter(ghys.jsonListYsData,'zgysList');
            ghys.fliter(ghys.jsonListHsData,'hsListData');
        },
		selectCBz: function (item) {
			console.log(item);
			if(item[0] =='jscy'){
				this.zycymc = '出院';
			}else if(item[0]=='bqcy'){
				this.zycymc = '病区出院';
			}else if(item[0]=='zk'){
				this.zycymc = '转科';
			}else{
				this.zycymc = '在院';
			}
		    this.pageState[item[2][1]] = item[0];
		    this.pageState['zrhs'] = null;
		    hzlb.pageState.brList=[];
		    this.getHzlb();
		    ghys.fliter(ghys.jsonListYsData,'zgysList');
		    ghys.fliter(ghys.jsonListHsData,'hsListData');
		},
        //更换主管医生
        changeDoc: function (index) {
            ghys.isShow = true;
            ghys.brInfo = hzlb.pageState.brList[index];
        },
        //    操作按钮点击事件
        caozuo: function (type) {
            switch (type) {
                case 'shenhe':
                    if (arguments.length > 1) { //单个审核
                        if (!arguments[1][0].dshshow) {
                            malert("该患者无医嘱审核！", 'top', 'defeadted');
                            return;
                        }
                        var brjson = {
                            brlist: arguments[1],
                            ksid: hzlb.pageState.ks,
                            csqx: hzlb.caqxContent,
                        };
                        sessionStorage.setItem('shyz', JSON.stringify(brjson));
                        this.topNewPage('审核医嘱', 'page/hsz/hlyw/yzcl/loading-page/shyz.html');
                    } else { // 批量审核
                        var activeBrList = [],
                            isCheckedLength = this.isChecked.length;
                        var shdatalistbak = [];
                        if (isCheckedLength == 0) {
                            malert("请选择患者后再操作！", 'top', 'defeadted');
                            return;
                        }
                        for (var i = 0; i < isCheckedLength; i++) {
                            if (this.isChecked[i]) {
                                activeBrList.push(this.pageState.brList[i]);
                                shdatalistbak.push(this.pageState.brList[i]);
                            }
                        }
                        var shlength = 0;
                        for (var i = 0; i < shdatalistbak.length; i++) {
                            if (shdatalistbak[i].dshshow != true) {
                                activeBrList.splice(i - shlength, 1);
                                shlength = shlength + 1;
                            }
                        }
                        if (activeBrList == null || activeBrList.length < 1) {
                            malert("无医嘱审核！", 'top', 'defeadted');
                            return;
                        }
                        if (this.isCheckAll || activeBrList.length > 0) { // 检查是否选中了患者
                            var brjson = {
                                brlist: activeBrList,
                                ksid: hzlb.pageState.ks,
                                csqx: hzlb.caqxContent,
                            };
                            sessionStorage.setItem('shyz', JSON.stringify(brjson));
                            this.topNewPage('批量审核医嘱', 'page/hsz/hlyw/yzcl/loading-page/shyz.html');
                        } else {
                            malert("你还没有选择任何患者！先至少选中一个后再操作！", 'top', 'defeadted');
                        }
                    }
                    break;
                case 'zhixing':
                    if (arguments.length > 1) { //单个执行
                        var brjson = {
                            brlist: arguments[1],
                            ksid: hzlb.pageState.ks,
                            csqx: hzlb.caqxContent,
                        };
                        sessionStorage.setItem('zxyz', JSON.stringify(brjson));
                        this.topNewPage('执行医嘱', 'page/hsz/hlyw/yzcl/loading-page/zxyz.html');
                    } else { // 批量执行
                        var activeBrList = [],
                            isCheckedLength = this.isChecked.length;
                        for (var i = 0; i < isCheckedLength; i++) {
                            if (this.isChecked[i]) activeBrList.push(this.pageState.brList[i]);
                        }
                        if (activeBrList == null || activeBrList.length < 1) {
                            malert("无医嘱执行！", 'top', 'defeadted');
                            return;
                        }
                        if (this.isCheckAll || activeBrList.length > 0) { // 检查是否选中了患者
                            var brjson = {
                                brlist: activeBrList,
                                ksid: hzlb.pageState.ks,
                                csqx: hzlb.caqxContent,
                            };
                            sessionStorage.setItem('zxyz', JSON.stringify(brjson));
                            this.topNewPage('批量执行医嘱', 'page/hsz/hlyw/yzcl/loading-page/zxyz.html');
                        } else {
                            malert("你还没有选择任何患者！先至少选中一个后再操作！", 'top', 'defeadted');
                        }
                    }
                    break;
                case 'shenling':
                    if (arguments.length > 1) { //单个申领
                        if (!arguments[1][0].dlyshow) {
                            malert("该患者无药品申领！", "top", "defeadted");
                            return;
                        }
                        var brjson = {
                            brlist: arguments[1],
                            ksid: hzlb.pageState.ks,
                            csqx: hzlb.caqxContent,
                        };
                        sessionStorage.setItem('slyp', JSON.stringify(brjson));
                        this.topNewPage('申领药品', 'page/hsz/hlyw/yzcl/loading-page/slyp.html');
                    } else { // 批量申领
                        var activeBrList = [],
                            isCheckedLength = this.isChecked.length;
                        var sldatalistbak = [];
                        if (isCheckedLength == 0) {
                            malert("请选择患者后再操作！", "top", "defeadted");
                            return;
                        }
                        for (var i = 0; i < isCheckedLength; i++) {
                            if (this.isChecked[i]) {
                                activeBrList.push(this.pageState.brList[i]);
                                sldatalistbak.push(this.pageState.brList[i]);
                            }
                        }

                        var sllength = 0;
                        for (var i = 0; i < sldatalistbak.length; i++) {
                            if (sldatalistbak[i].dlyshow != true) {
                                activeBrList.splice(i - sllength, 1);
                                sllength = sllength + 1;
                            }
                        }
                        if (activeBrList == null || activeBrList.length < 1) {
                            malert("无药品申领！", "top", "defeadted");
                            return;
                        }
                        if (this.isCheckAll || activeBrList.length > 0) { // 检查是否选中了患者
                            var brjson = {
                                brlist: activeBrList,
                                ksid: hzlb.pageState.ks,
                                csqx: hzlb.caqxContent,
                            };
                            sessionStorage.setItem('slyp', JSON.stringify(brjson));
                            this.topNewPage('批量申领药品', 'page/hsz/hlyw/yzcl/loading-page/slyp.html');
                        } else {
                            malert("你还没有选择任何患者！先至少选中一个后再操作！", 'top', 'defeadted');
                        }
                    }
                    break;
                case 'qxsl':
                    if (arguments.length > 1) { //单个取消申领药品
                        var brjson = {
                            brlist: arguments[1],
                            ksid: hzlb.pageState.ks,
                            csqx: hzlb.caqxContent,
                        };
                        sessionStorage.setItem("qxslyp", JSON.stringify(brjson));
                        this.topNewPage('取消申领药品', 'page/hsz/hlyw/yzcl/loading-page/qxslyp.html');
                    } else { // 批量取消申领药品
                        var activeBrList = [],
                            isCheckedLength = this.isChecked.length;
                        for (var i = 0; i < isCheckedLength; i++) {
                            if (this.isChecked[i]) activeBrList.push(this.pageState.brList[i]);
                        }
                        if (this.isCheckAll || activeBrList.length > 0) { // 检查是否选中了患者
                            var brjson = {
                                brlist: arguments[1],
                                ksid: hzlb.pageState.ks,
                                csqx: hzlb.caqxContent,
                            };
                            sessionStorage.setItem("qxslyp", JSON.stringify(brjson));
                            this.topNewPage('批量取消申领药品', 'page/hsz/hlyw/yzcl/loading-page/qxslyp.html');
                        } else {
                            malert("你还没有选择任何患者！先至少选中一个后再操作！", 'top', 'defeadted');
                        }
                    }
                    break;
                case 'tzsh':
                    if (arguments.length > 1) { //单个申领
                        if (!arguments[1][0].dtzshow) {
                            malert("该患者无停嘱审核！", 'top', 'defeadted');
                            return;
                        }
                        var brjson = {
                            brlist: arguments[1],
                            ksid: hzlb.pageState.ks,
                            csqx: hzlb.caqxContent,
                        };
                        sessionStorage.setItem('tzsh', JSON.stringify(brjson));
                        this.topNewPage('停嘱审核', 'page/hsz/hlyw/yzcl/loading-page/tzsh.html');
                    } else { // 批量申领
                        var activeBrList = [],
                            isCheckedLength = this.isChecked.length;
                        var tzdatalistbak = [];
                        if (isCheckedLength == 0) {
                            malert("请选择患者后再操作！", "top", "defeadted");
                            return;
                        }
                        for (var i = 0; i < isCheckedLength; i++) {
                            if (this.isChecked[i]) {
                                activeBrList.push(this.pageState.brList[i]);
                                tzdatalistbak.push(this.pageState.brList[i]);
                            }

                        }
                        var tzlength = 0;
                        for (var i = 0; i < tzdatalistbak.length; i++) {
                            if (tzdatalistbak[i].dtzshow != true) {
                                activeBrList.splice(i - tzlength, 1);
                                tzlength = tzlength + 1;
                            }
                        }
                        if (activeBrList == null || activeBrList.length < 1) {
                            malert("无停嘱审核！", 'top', 'defeadted');
                            return;
                        }
                        if (this.isCheckAll || activeBrList.length > 0) { // 检查是否选中了患者
                            var brjson = {
                                brlist: activeBrList,
                                ksid: hzlb.pageState.ks,
                                csqx: hzlb.caqxContent,
                            };
                            sessionStorage.setItem('tzsh', JSON.stringify(brjson));
                            this.topNewPage('批量停嘱审核', 'page/hsz/hlyw/yzcl/loading-page/tzsh.html');
                        } else {
                            malert("你还没有选择任何患者！先至少选中一个后再操作！", 'top', 'defeadted');
                        }
                    }
                    break;
                case 'tysq':
                    if (arguments.length > 1) { //单个申领
                        var brjson = {
                            brlist: arguments[1],
                            ksid: hzlb.pageState.ks,
                            csqx: hzlb.caqxContent,
                            zyType:hzlb.pageState.zyType
                        };
                        sessionStorage.setItem('tysq', JSON.stringify(brjson));
                        this.topNewPage('退药申请', 'page/hsz/hlyw/yzcl/loading-page/tysq.html');
                    }
                    // else { // 批量申领
                    //     var activeBrList = [],
                    //         isCheckedLength = this.isChecked.length;
                    //     for(var i = 0; i < isCheckedLength; i++ ){
                    //         if( this.isChecked[i] )activeBrList.push( this.pageState.brList[i] );
                    //     }
                    //     if ( this.isCheckAll || activeBrList.length > 0 ){ // 检查是否选中了患者
                    //     	var brjson={
                    //         		brlist:arguments[1],
                    //         		ksid:hzlb.pageState.ks,
                    //         		csqx:hzlb.caqxContent,
                    //         	}
                    //         this.topNewPage('批量申领药品','page/hsz/hlyw/yzcl/loading-page/tysq.html?hz='+JSON.stringify(brjson));
                    //     }else {
                    //         malert("你还没有选择任何患者！先至少选中一个后再操作！",'top','defeadted');
                    //     }
                    // }
                    break;
                case 'bqcy':
                    if (arguments.length > 1) { //单个病区出院
                        console.log(arguments);
                        var parm = {
                            zyh: arguments[1][0].zyh,
                            ksbm: hzlb.pageState.ks,
                            bqcyrq:hzlb.bqcyrq
                        };
                        this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=bqcy',
                            JSON.stringify(parm))
                            .then(function (data) {
                                if (data.body.a == "0") {
                                    malert("病区出院成功！", 'top', 'success');

                                    //新加代码，调用护理接口
                                    if (window.top.J_tabLeft.obj.hljkurl !=null && window.top.J_tabLeft.obj.hljkurl !=undefined){
                                        var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                                        var inJson ={
                                            yljgbm:jgbm,
                                            zyh:hzlb.brzyh
                                        }
                                        var params ={
                                            yljgbm:jgbm,
                                            zyh:hzlb.brzyh,
                                            types:'hzxx',
                                            method:'ADT_A03',
                                            inJson : JSON.stringify(inJson)
                                        }
                                        this.postAjax(hljkurl,JSON.stringify(params),function (result) {
                                            if (result.code==0){
                                                console.log("护理接口调取成功!");
                                            } else {
                                                console.log("护理接口调取失败:"+result.msgInfo);
                                                console.log("护理接口调取失败的住院号为:"+params.zyh)
                                            }
                                        })}

                                    hzlb.pageState.brList=[]
                                    hzlb.getHzlb();
                                } else {
                                    console.log("error:" + data.body.c);
                                    malert(data.body.c, 'top', 'defeadted');
                                }
                            });
                    } else { // 批量申领
                        malert("只能选择单个病人操作！", 'top', 'defeadted');
                        return
                    }
                    break;
                case 'qxbqcy':
                    if (arguments.length > 1) { //单个病区出院
                        if (arguments[1][0].zyh != null) {
                            if (!confirm("你确定要取消出院【" + (arguments[1][0].zyh) + "】住院号的病人吗？")) {
                                return false;
                            }
                            if(arguments[1][0].bqcybz == '0'){
                                malert('病人在院，不能取消病区出院！','top','defeadted');
                                return;
                            }
                            if(arguments[1][0].zyzt == '1'){
                                malert('病人已经结算出院，不能取消病区出院！','top','defeadted');
                                return;
                            }
                            qxbqcy.qxbqcyBrContent = {
                                zyh: arguments[1][0].zyh,
                                cyks: hzlb.pageState.ks
                            };
                            qxbqcy.initCw('qxbqcy',arguments[1][0].rycwbh);
                        } else {
                            malert("请选择需要取消病区出院的病人！", "top", "defeadted");
                            return
                        }
                    } else { // 批量申领
                        malert("只能选择单个病人操作！", 'top', 'defeadted');
                        return
                    }
                    break;
                case 'huli':
                    if (arguments.length > 1) {
                        sessionStorage.setItem("NoticeObj"+userId, JSON.stringify({
                            msgtype: "8",
                            ksbm: arguments[1].ryks,
                            zyh: arguments[1].zyh,
                            zyType:hzlb.pageState.zyType
                        }));
                        hzlb.topNewPage('快捷操作', 'page/hsz/hlyw/yzgl/yzgl.html');
                    }

                    break;
                case 'sance':
                    if (arguments.length > 1) {
                        var brjson = {
                            brlist: arguments[1],
                            ksid: hzlb.pageState.ks,
                            csqx: hzlb.caqxContent,
                        };
                        sessionStorage.setItem("sance", JSON.stringify(brjson));
                        this.topNewPage(arguments[1].brxm + '--体温单', 'page/hsz/hlyw/yzcl/loading-page/twd.html');
                    }
                    break;
                case 'kjcz':
                    this.topNewPage("护士站快捷操作", 'page/hsz/hlyw/yzgl/yzgl.html');
                    break;
            }
        },

        qxbqcyOperation:function(){
            if(qxbqcy.sfCxfc){
                qxbqcy.qxbqcyBrContent.cwid = qxbqcy.jkacContent.cwid;
                qxbqcy.qxbqcyBrContent.cwbh = qxbqcy.jkacContent.cwbh;
            }
            common.openloading();
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=qxbqcy',
                JSON.stringify(qxbqcy.qxbqcyBrContent))
                .then(function (data) {
                    if (data.body.a == "0") {
                        malert("取消病区出院成功！", 'top', 'success');

                        //新加代码，调用护理接口
                        if (window.top.J_tabLeft.obj.hljkurl !=null && window.top.J_tabLeft.obj.hljkurl !=undefined){
                            var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                            var inJson ={
                                yljgbm:jgbm,
                                zyh:qccl.brInfo.zyh
                            }
                            var params ={
                                yljgbm:jgbm,
                                zyh:qccl.brInfo.zyh,
                                types:'hzxx',
                                method:'ADT_A13',
                                inJson : JSON.stringify(inJson)
                            }
                            this.postAjax(hljkurl,JSON.stringify(params),function (result) {
                                if (result.code==0){
                                    console.log("护理接口调取成功!");
                                } else {
                                    console.log("护理接口调取失败:"+result.msgInfo);
                                    console.log("护理接口调取失败的住院号为:"+params.zyh)
                                }
                            })}


                        hzlb.pageState.brList=[]
                        hzlb.getHzlb();
                        qxbqcy.Class = true;
                        qxbqcy.cwList = [];
                        qxbqcy.jkacContent = {};
                        common.closeLoading();
                    } else {
                        console.log("error:" + data.body.c);
                        malert(data.body.c, 'top', 'defeadted');
                        common.closeLoading();
                    }
                });
        },

        //病区出院操作
        bqcy: function () {
            if (arguments.length > 1) {
                var parm = {
                    zyh: arguments[1].zyh,
                    ksbm: hzlb.pageState.ks
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=bqcy',
                    JSON.stringify(parm))
                    .then(function (data) {
                        if (data.body.a == "0") {
                            malert("病区出院成功！", 'top', 'success');
                            hzlb.pageState.brList=[]
                            hzlb.getHzlb();
                        } else {
                            console.log("error:" + data.body.c);
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    });
            } else {
                malert("只能选择单个病人操作！", 'top', 'defeadted');

            }
        },

        openBrInfo: function (br) {
            var cxbrparm = {
                ryks: hzlb.pageState.ks,
                page: 1,
                rows: 2000,
                sfkj: '1',
                zyh: br.zyh,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm=" + JSON.stringify(cxbrparm), function (json) {
                if (json.a == 0) {
                    brInfo.brxxContent = json.d.list[0];
                    brInfo.open();
                } else {
                    malert("人员列表查询失败" + json.c, "top", "defeadted");
                    return;
                }
            });
        },
        czClick: function (index, $event) {
            this.activeIndex=index
            removeActiveCzButt(index);
            var el = $("#cz-" + index),
                conEl = $('.content', el),
                height = $(window).height(),
                width = $(window).width(),
                x = $event.clientX,
                y = $event.clientY;
            if (x > width / 2) {
                conEl.addClass('left');
            }
            if (y > height / 2) {
                conEl.addClass('top');
            }
            if (y > height / 3 * 2) {
                conEl.addClass('bottom');
            }
            // el.addClass("active");
        }
    }
});

$(window).click(function () {
    hzlb.isActive=[];
});
function removeActiveCzButt(index) {
    hzlb.isActive=[];
    hzlb.isActive[index]=!hzlb.isActive[index]
    hzlb.$forceUpdate()
    // var box = $('.cz-butt.active');
    // if (box.length > 0) {
    //     $('.content', box).removeClass("left top bottom");
    //     box.removeClass("active");
    // }
}

// 更换医生
var ghys = new Vue({
    el: '#ghys',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        isShow: false,
        title: '更换医生',
        zgysList: [],
        doctor: null,           // 选择的主管医生
        nurse: null,            //选择责任护士
        brInfo: {},              // 病人信息
        jsonListYsData: [],
        jsonListHsData: [],
        hsListData:[]
    },
    mounted:function(){

    },
    methods: {
        //关闭
        closes: function () {
            this.isShow = false;
        },
        open: function () {
            this.isShow = true;
        },
        submit: function () {
            // 确定按钮事件
        },
        // 请求所有的主管医生的API,给searchCon赋值
        getData: function (obj,objContent,list) {
            // rybmjkac&json=" + JSON.stringify({ysbz: '1'}
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybmjkac&json="+JSON.stringify(obj), function (json) {
                if (json.a == "0") {
                    ghys[list] = json.d.list;
                    //0=默认当前病区医生，1=全院医生
                    //                    if (hzlb.caqxContent.cs00900100118 == '0') {
                    //过滤医生
                    ghys.fliter(ghys[list],objContent)
                    //                    } else {
                    //                    	ghys.zgysList = json.d.list;
                    //                    }

                    //过滤护士

                }
            });
        },
        fliter:function(list,objContent){
            ghys[objContent] = jsonFilter(list, "ksbm", hzlb.pageState.ks);
        },
        // 更换主管医生请求的API
        saveData: function () {
            this.brInfo['zyys'] = this.doctor;
            this.brInfo['zyysxm'] = this.listGetName(this.zgysList, this.doctor, 'rybm', 'ryxm');
            if(this.nurse){
                this.brInfo['zrhs'] = this.nurse;
            }
            hzlb.brzyh = this.brInfo.zyh;
            this.brInfo['zrhsxm'] = this.listGetName(this.zgysList, this.nurse, 'rybm', 'ryxm');
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=hzgys&parm=" + JSON.stringify(this.brInfo), function (json) {
                if (json.a == "0") {
                    ghys.isShow = false;
                    malert("更新数据成功", 'top', 'success');

                    //新加代码，调用护理接口
                    if (window.top.J_tabLeft.obj.hljkurl !=null && window.top.J_tabLeft.obj.hljkurl !=undefined){
                        var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                        var inJson ={
                            yljgbm:jgbm,
                            zyh:hzlb.brzyh
                        }
                        var params ={
                            yljgbm:jgbm,
                            zyh:hzlb.brzyh,
                            types:'hzxx',
                            method:'ADT_A54',
                            inJson : JSON.stringify(inJson)
                        }
                        hzlb.postAjax(hljkurl,JSON.stringify(params),function (result) {
                            if (result.code==0){
                                console.log("护理接口调取成功!");
                            } else {
                                console.log("护理接口调取失败:"+result.msgInfo);
                                console.log("护理接口调取失败的住院号为:"+params.zyh)
                            }
                        })}


                    hzlb.pageState.brList = [];
                    hzlb.getHzlb();
                }
            });
        },
    }
});

/**
 * 更多操作按钮区域鼠标移入移出事件处理函数
 * @param  {[布尔类型]} type [true表示移入，false表示移除，默认为false]
 * @return 无
 */
function moreMouse(dom, type) {
    var $thisDom = $(dom),
        $contentDom = $('.content', $thisDom);
    if (type) {
        var height = $(window).height() / 2,
            moreY = event.clientY;
        if (moreY > height) {
            $contentDom.addClass('top');
        }
    } else {
        $contentDom.removeClass("top,left,bottom");
    }
}

// 病人详细信息
var brInfo = new Vue({
    el: '#brInfo',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data: {
        isShow: false,
        brxxContent: {},
    },
    mounted: function () {
        document.onclick = function (event) {
            if (event.srcElement._prevClass && event.srcElement._prevClass.indexOf('userName') == -1) {
                brInfo.isShow = false;
            }
        }
    },
    methods: {
        open: function () {
            this.isShow = true;
        },
        close: function () {
            this.isShow = false;
        }
    },
});


//转科按床pop
var zkac = new Vue({
    el: '#zkac',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        title: '转科按床',
        ryListData: [],
        cwListData: [],
        zgysList: [],
        zgys: '02',
        cwList: [],
        cw: '01',
        zkhcContent: {},
        ifClick: true,//重复点击判断
        Class: true,
        sqlist: [],//转科申请列表
        sfsq: false,//是否申请
        jsonListData: []
    },
    computed: {
        // "Kslist": function () {
        //     return hzlb.pageState.ksList;
        // }
    },
    methods: {
        //关闭
        closes: function () {
            zkac.Class = true
            zkac.zkhcContent = {};
        },

        //打开
        open: function () {
            zkac.Class = false
        },

        //获取患者是否已申请转科换床
        getSfsq: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=HszByglCwgl&types=queryJzkjl&parm={"zyh":"' + zkac.zkhcContent.zyh + '","zfbz":"0"}',
                function (json) {
                    if (json.a == "0") {
                        zkac.sqList = json.d.list;
                    } else {
                        malert(json.c, 'top', "defeadted");
                    }
                });
        },
        // 请求所有的主管医生的API,给searchCon赋值
        getData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm", function (json) {
                if (json.a == "0") {
                    zkac.jsonListData = json.d.list;
                    //0=默认当前病区医生，1=全院医生
                    //                    if (hzlb.caqxContent.cs00900100118 == '0') {
                    //过滤医生
                    zkac.zgysList = jsonFilter(zkac.jsonListData, "ksbm", hzlb.pageState.ks);
                    //                    } else {
                    //                    	ghys.zgysList = json.d.list;
                    //                    }
                }
            });
        },

        //转科换床
        saveZkhc: function () {
            $.ajaxSettings.async = false;
            if (!this.ifClick) {
                return;
            }//保存提交限制只允许一次
            this.ifClick = false;
            if (zkac.zkhcContent.zyh == undefined || zkac.zkhcContent.zyh == null || zkac.zkhcContent.zyh == "") {
                malert("请先选择需接科的患者!", 'top', "defeadted");
                this.ifClick = true;
                return;
            }
            zkac.getSfsq();
            for (var i = 0; i < zkac.sqList.length; i++) {
                if (zkac.sqList[i].jrbz == '0') {
                    zkac.sfsq = true;
                }
            }
            if (zkac.sfsq) {
                malert("该患者已申请，请不要重复操作！", 'top', "defeadted");
                this.ifClick = true;
                return;
            }
            zkac.zkhcContent.ksmc = hzlb.pageState.ks;

            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=zkhc',
                JSON.stringify(zkac.zkhcContent))
                .then(function (data) {
                    if (data.body.a == "0") {
                        malert("转科换床申请成功！");
                        zkac.Class = true;
                        $("#zkac.side-form").removeClass('side-form-bg');
                        $("#zkac.side-form").addClass('ng-hide');
                        zkac.closes();
                        hzlb.getData()
                    } else {
                        console.log("error:" + data.body.c);
                        malert(data.body.c, 'top', "defeadted");
                    }
                    this.ifClick = true;
                });
        },
    }
});

//迁床处理
var qccl = new Vue({
    el: '#qccl',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        title: '迁床处理',
        zgysList: [],
        cwList: [],
        cwid: null,
        ifClick: true,//重复点击判断
        Class: true,
        brInfo: {},
        jsonListData: [],
        qcContent: {},
    },
    methods: {
        getCwData: function () {
            //床位查询
            parm = { "ywbz": "1" };
            var dg={
                rows:20000,
                page:1
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCwglCwh&types=query&parm=" + JSON.stringify(parm)+"&dg="+JSON.stringify(dg),
                function (json) {
                    qccl.cwList = [];
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            qccl.cwList = jsonFilter(json.d.list, "ksbm", hzlb.pageState.ks);
                        }
                    } else {
                        malert(json.c, 'top', "defeadted");
                    }
                });
        },
        getYsData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm", function (json) {
                if (json.a == "0") {
                    qccl.jsonListData = json.d.list;
                    //                    //0=默认当前病区医生，1=全院医生
                    //                    if (yzclLeft.caqxContent.cs00900100118 == '0') {
                    //                        //过滤医生
                    qccl.zgysList = jsonFilter(qccl.jsonListData, "ksbm", hzlb.pageState.ks);
                    //                    } else {
                    //                   	 ghys.zgysList = json.d.list;
                    //                    }
                }
            });
        },
        //关闭
        closes: function () {
            qccl.Class = true
            qccl.brInfo = {};
            qccl.qcContent = {};
        },

        //打开
        open: function () {
            qccl.Class = false
        },


        //转科换床
        saveQccl: function () {
            if (!this.ifClick) {
                return;
            }//保存提交限制只允许一次
            this.ifClick = false;
            var parm = {
                'zyh': qccl.brInfo.zyh,
                'qccwid': qccl.brInfo.rycwid,
                'qrcwid': qccl.qcContent.cwid,
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=Qccl',
                JSON.stringify(parm))
                .then(function (data) {
                    if (data.body.a == "0") {

                        //新加代码，调用护理接口
                        if (window.top.J_tabLeft.obj.hljkurl !=null && window.top.J_tabLeft.obj.hljkurl !=undefined){
                            var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                            var inJson ={
                                yljgbm:jgbm,
                                zyh:qccl.brInfo.zyh
                            }
                            var params ={
                                yljgbm:jgbm,
                                zyh:qccl.brInfo.zyh,
                                types:'hzxx',
                                method:'ADT_A02',
                                inJson : JSON.stringify(inJson)
                            }
                            hzlb.postAjax(hljkurl,JSON.stringify(params),function (result) {
                                if (result.code==0){
                                    console.log("护理接口调取成功!");
                                } else {
                                    console.log("护理接口调取失败:"+result.msgInfo);
                                    console.log("护理接口调取失败的住院号为:"+params.zyh)
                                }
                            })}
                        hzlb.getData()
                        qccl.closes();
                        malert("更新数据成功");
                    } else {
                        malert("申请失败", json.c, "top", "defeadted");
                    }
                    this.ifClick = true;
                });
        },
    }
});

var qxbqcy = new Vue({
    el: '#qxbqcy',
    mixins: [baseFunc, tableBase, dic_transform, mformat],
    data: {
        title: '取消病区出院',
        ryListData: [],
        cwListData: [],
        zgysList: [],//主管医生列表
        zgys: '02',
        cwList: [],//床位列表
        cw: '01',
        jkacContent: {},
        ifClick: true,//重复点击判断
        Class: true,
        sfCxfc:true,
        them_tran2: {
        },
        qxbqcyBrContent:{

        },
        page: {
            page: 1,
            rows: 20,
            total: null
        },
    },
    methods: {
        initCw:function(type,cwbh){
            var parm = {
                "ywbz": "1" ,
                "ksbm":hzlb.pageState.ks,
            };
            var dg={
                rows:20000,
                page:1
            };
            common.openloading();
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCwglCwh&types=query&parm=" + JSON.stringify(parm)+"&dg="+JSON.stringify(dg),
                function (json) {
                    qxbqcy.cwList = [];
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            qxbqcy.cwListData = json.d.list;
                            qxbqcy.cwList = jsonFilter(qxbqcy.cwListData, "ksbm", hzlb.pageState.ks);
                            if(type == 'qxbqcy'){
                                for (var i = 0; i < qxbqcy.cwList.length; i++) {
                                    if(qxbqcy.cwList[i].cwbh == cwbh){//若果空床位中有该需要取消病区出院的病人的床位
                                        hzlb.qxbqcyOperation();
                                        return false;
                                    }
                                }
                                qxbqcy.showCxfc();
                                common.closeLoading();
                                return false;
                            }
                        }
                        common.closeLoading();
                    } else {
                        malert(json.c, 'top', "defeadted");
                        common.closeLoading();
                    }
                });
        },
        commonResultChange:function(val){
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "cwid":
                    qxbqcy.jkacContent.cwid = val[0];
                    qxbqcy.jkacContent.cwbh = val[4];
                    this.$forceUpdate();
                    break;
            }
        },
        showCxfc:function(){
            this.Class = false;
        },
        closes: function () {
            this.Class = true;
            qxbqcy.cwList = [];
            qxbqcy.jkacContent = {};
        },
        //由于原床位被占用，需要重新分配床位，避免一个床位两个人
        saveJkac:function(){
            if(!qxbqcy.jkacContent.cwid){
                malert("请选择床位号！","top","defeadted");
                return;
            }
            hzlb.qxbqcyOperation();
        },
        getPsData: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=HszHlywYzzxdCx&types=queryPs&parm=' + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    pslr.psYzList = json.d.list;
                } else {
                    malert(json.c, 'top', 'defeadted')
                }
            }, function (error) {
                console.log(error);
            });
        },

    }
});

var ddzcyc = new Vue({
    el: '#ddzcyc',
    mixins: [baseFunc, tableBase, dic_transform, mformat],
    data: {
        title: '跌倒坠床压疮',
        brInfo: {},
        ifClick: true,//重复点击判断
		Class: true,
        them_tran2: {
        },
		ddzc:{
		    '0':'否',
		    '1':'是',
		},
		ycdj:{
			'0':'否',
			'1':'Ⅰ度',
			'2':'Ⅱ度',
			'3':'Ⅲ度',
			'4':'Ⅳ度',
		}
    },
    methods: {
        //打开
        open: function () {
            ddzcyc.Class = false
        },
        selectCB: function (item) {
        	console.log(item);
            this.brInfo[item[2][1]] = item[0];
        },
        showCxfc:function(){
            this.Class = false;
        },
        closes: function () {
            this.Class = true;
            ddzcyc.brInfo = {};
        },
        saveDdzcyc:function(){
			var upparm = {
			    zyh: ddzcyc.brInfo.zyh,
			    dd: ddzcyc.brInfo.dd?ddzcyc.brInfo.dd:'0',
			    zc: ddzcyc.brInfo.zc?ddzcyc.brInfo.zc:'0',
			    yc: ddzcyc.brInfo.yc?ddzcyc.brInfo.yc:'0',
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=ddzcycUpdate&parm=" + JSON.stringify(upparm), function (json) {
			    if (json.a == 0) {
			        ddzcyc.closes();
					hzlb.getData();
			    } else {
			        malert("更新跌倒坠床压疮信息失败" + json.c, "top", "defeadted");
			        return;
			    }
			});
			
			
		}

    }
});
laydate.render({
    elem: '#bqcyrq',
    rigger: 'click',
    theme: '#1ab394',
    type: 'datetime',
    done: function (value, data) {
        hzlb.bqcyrq = value;
    }
});

laydate.render({
    elem: '#kssj',
    rigger: 'click',
    theme: '#1ab394',
    type: 'datetime',
    done: function (value, data) {
        hzlb.pageState.beginrq = value;
        hzlb.getHzlb();
    }
});
laydate.render({
    elem: '#kssj1',
    rigger: 'click',
    theme: '#1ab394',
    type: 'datetime',
    done: function (value, data) {
        hzlb.pageState.endrq = value;
        hzlb.getHzlb();
    }
});
