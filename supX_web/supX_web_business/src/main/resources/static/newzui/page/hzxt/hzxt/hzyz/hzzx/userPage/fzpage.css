.fzpage{
    height: calc(100% - 66px);
}
.fzFiexd{
    width:128px;
    height:34px;
    position: fixed;
    right: -46px;
    color:#ffffff;
    top: 50%;
    cursor: pointer;
    text-align: center;
}
.bg-fiexd{
    background-image: url(/newzui/pub/image/<EMAIL>);
    position: fixed;
    top: 46%;
    right: 0;
    cursor: pointer;
    width: 34px;
    height: 128px;
    transform: translate(0,-50%);
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
}
.fzpage-title{
    font-size:14px;
    color:#7f8fa4;
    margin-bottom: 2px;
}
.fzpage-bt{
    font-size:22px;
    color:#3a3a3a;
    font-weight: bold;
    margin-bottom: 5px;
    text-align:center;
}
.fzpage-left{
    margin-left: 6px;
}
.tree_tem1 ul{
    padding-left: 0;
}
.tree_text1{
    padding: 0 9px;
}
#brzcList .ksys-btn{
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}
.icon-xz1:before{
    color: #1abc9c;
}
.zui-select-inline,.zui-input-inline{
    width: 182px;
}
.layui-height label{
    color:#7f8fa4;
}
.height-500{
    height: 500px;
}
.pop .ksys-btn{
    height: 70px;
}
.template{
    overflow: auto;
}
.template::-webkit-scrollbar-thumb{
    background: none;
}
.template:hover::-webkit-scrollbar-thumb{
    background-color: rgba(0, 0, 0, 0.5);
}
.template::-webkit-scrollbar-button {
    display: none;
}
.template::-webkit-scrollbar {
    width: 0;
}
.ksys-side{
    padding: 0px;

}
.padd-child .tree-padd{
     padding: 6px 0px;
    padding-left: 22px;
    cursor: pointer;
}
.padd-child .tree_tem1{
    padding-left: 0px;
    padding: 0;

}
.padd-child>.tree_tem1:hover,.tree-padd:hover{
    background:rgba(26, 188, 156, .1);
    color:#1abc9c;
}
.tree-padd:hover .tree_text1{
    color:#1abc9c;
}
.tab-message{
    margin-bottom: 6px;
}