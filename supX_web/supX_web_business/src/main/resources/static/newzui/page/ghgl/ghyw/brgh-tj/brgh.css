#calendar {
    top: 32px;
}
.validate, .tipError{
    right: 0;
    z-index: 111;
    background-color:transparent;
}
.closeBtu {
    position: absolute;
    left: 20px;
    z-index: 100;
}

.infoIpt {
    width: 24%;
}

.listDiv {
    /*overflow: hidden;*/
}

.popup-right {
    position: absolute;
    right: 0;
    float: right;
    background-color: #FFFFFF;
    height: 80%;
    overflow: hidden;
}

.listDiv2 {
    position: relative;
    overflow: scroll;
    margin-top: 20px;
    height: calc(100% - 135px);
    margin-left: 20px;
}

/* 门诊收费发票样式 begin */
.print-mzsffp{
    page-break-after: always;
    padding-top: 20mm; /* 21.5mm */
    padding-right: 13mm;
    padding-left: 11mm
}
@media print{
    @page{
        size: 86mm 100mm;
        margin: 0;
    }
}
.bqcydj_model{
    width: auto;
    padding: 14px 15px 33px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
#model{
    z-index: 1000;
}
/* 门诊收费发票样式 end */

.panel {
    height: calc(100% - 66px);
    overflow: hidden;
}

.contextStyle {
    height: 100%;
    overflow: auto;
    padding-bottom: 70px;
}

.hzgl-flex {
    justify-content: space-between;
    background: #fff !important;
    display: flex;
    align-items: center;
    border-top: 1px solid #eee;
    height: 66px;
}

.bg {
    background-color: #f9f9f9;
}
.no-pading input {
    padding-right: 0 !important;
}
.brgh-box {
    width: 400px;
    height: 200px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 8px 0px rgba(2, 41, 33, 0.6);
    border-radius: 4px;
    position: absolute;
    z-index: 1112;
    left: 0;
    top: 38px;
    overflow: auto;
}

.brgh-ul {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;

}
.brgh-ul li {
    width: 100%;
    display: flex;
    line-height: 28px;
    justify-content: space-around;
    border-top: 1px solid #eee;
    overflow: hidden;
    padding: 5px 0;
}

.brgh-ul li:first-child {
    border-top: none;
}

.brgh-ul li span {
    display: block;
}

.brgh-ul li .brgh-time {
}

.brgh-ul li .brgh-yuyue {
    width: 60px;
    height: 28px;
    background: rgba(26, 188, 156, 1);
    border-radius: 18px;
    color: #fff;
    text-align: center;
    line-height: 28px;
    cursor: pointer;
}
.bqcydj_model{
    width: auto;
    padding: 14px 18px 14px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.page-more:before,.page-nextMore:before,.page-next:before,.page-prev:before{
    left: 0;
}
.chxx_model{
    width: 800px;
    height: 300px;
    overflow: hidden;
    padding: 14px 18px 14px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.hqjtcy{
    background-image:linear-gradient(-180deg, #ffffff 0%, #e6e8e9 100%);
    border:1px solid #dfe3e9;
    border-radius:4px;
    width:140px !important;
    text-align: center;
    line-height: 34px;
    height:34px;
    margin-left: 5px;
    font-size:14px;
    color:#f3ab5e;
    cursor: pointer;
}
.red {
    color: #ff4532;
}
.font-bolder{
    font-weight: bold;
}
