(function () {
    //汇总明细内容显示
    var tableInfo = new Vue({
        el: '#context',
        //混合js字典庫
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            ksbm: "",
            ksmc: "",
            jsonList: [],
            jsonMxList: [],
            ksVal: null,
            param: {},
            type: null,
            ksrq: null,
            jsrq: null,
            brxxContent: {},
            fyqdContent: {},
            qdType: 0,              // 当前的清单类型
            //detailFyqd: {},         // 明细清单
            fyhj: 0,
        },
        watch: {
        },
        methods: {
            // 获取汇总清单
            getData: function () {
                this.param.ksrq = this.ksrq;
                this.param.jsrq = this.jsrq;
                tableInfo.fyqdContent.ksrq = this.ksrq;
                tableInfo.fyqdContent.jsrq = this.jsrq;

                this.param.zyh = tableInfo.brxxContent.zyh;
                $.getJSON("/actionDispatcher.do?reqUrl=HszCxtjFyqd&types=queryMx&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == "0") {
                        var fyhj = 0.00;
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                        tableInfo.jsonList = json.d.list;
                        fylistbak=json.d.list;
                        for (var i = 0; i < tableInfo.jsonList.length; i++) {
                            var fylistbak=tableInfo.jsonList[i].fymx;
                            var length=0;
                            for(var j = 0; j <fylistbak.length; j++){
                                if (fylistbak[j].fysl==0||fylistbak[j].fysl=='0') {
                                    tableInfo.jsonList[i].fymx.splice(j - length, 1);
                                    length = length + 1;
                                }
                            }

                        }
                        tableInfo.ksVal = '0005';
                        for (var i = 0; i < tableInfo.jsonList.length; i++) {
                            fyhj += tableInfo.jsonList[i].fyze;
                        }
                        tableInfo.fyqdContent.fyhj = fyhj;
                        console.log(tableInfo.jsonList);
                    }
                });
                this.getMxData();
            },
            // 获取明细清单
            getMxData: function () {
                this.param.ksrq = this.ksrq;
                this.param.jsrq = this.jsrq;
                tableInfo.fyqdContent.ksrq = this.ksrq;
                tableInfo.fyqdContent.jsrq = this.jsrq;
                this.param.zyh = tableInfo.brxxContent.zyh;
                //请求后台查询
                $.getJSON("/actionDispatcher.do?reqUrl=HszCxtjFyqd&types=queryFyqdMxxm&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == "0") {
                        var length=0;
                        var fylistbak=[];
                        var fyhj = 0.00;
                        tableInfo.jsonMxList = json.d.list;
                        fylistbak=json.d.list;
                        for (var i = 0; i < fylistbak.length; i++) {
                            if (fylistbak[i].fysl==0||fylistbak[i].fysl=='0') {
                                tableInfo.jsonMxList.splice(i - length, 1);
                                length = length + 1;
                            }
                        }
                        for (var i = 0; i < tableInfo.jsonList.length; i++) {
                            fyhj += tableInfo.jsonList[i].ftje;
                        }
                        tableInfo.fyhj = fyhj;
                        console.log(tableInfo.jsonMxList);
                    }
                });
            },
            print: function () {
                window.print();
            }
        }
    });
    //检索显示区
    var brSearch = new Vue({
        el: '.brSearch',
        components: {
            'search-table': searchTable
        },
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            ksbm: "",
            ksmc: "",
            allKs: [],
            brContent: {},
            json: {},
            page: {
                page: 1,
                rows: 10,
                brxm: '',
                ryks: '',
                total:null
            },
            title: '病人信息',
        },
        mounted:function () {
            if(sessionStorage.getItem('fyqd')){
                Vue.set(this,'json',JSON.parse(sessionStorage.getItem('fyqd'))[0]);
                Vue.set(tableInfo,'fyqdContent',JSON.parse(sessionStorage.getItem('fyqd'))[0]);
                Vue.set(tableInfo,'qdType',JSON.parse(sessionStorage.getItem('fyqd'))[3]);
                if(JSON.parse(sessionStorage.getItem('fyqd'))[1]){
                    tableInfo.ksrq=JSON.parse(sessionStorage.getItem('fyqd'))[1];
                    tableInfo.jsrq=JSON.parse(sessionStorage.getItem('fyqd'))[2]
                }
                tableInfo.brxxContent.zyh=this.json.zyh
            }
            window.addEventListener('storage',function (e) {
                if(e.key=='fyqd'){
                    Vue.set(brSearch,'json',JSON.parse(sessionStorage.getItem('fyqd'))[0]);
                    Vue.set(tableInfo,'fyqdContent',JSON.parse(sessionStorage.getItem('fyqd'))[0]);
                    Vue.set(tableInfo,'qdType',JSON.parse(sessionStorage.getItem('fyqd'))[3]);
                    if(JSON.parse(sessionStorage.getItem('fyqd'))[1]){
                        tableInfo.ksrq=JSON.parse(sessionStorage.getItem('fyqd'))[1];
                        tableInfo.jsrq=JSON.parse(sessionStorage.getItem('fyqd'))[2]
                    }
                    tableInfo.brxxContent.zyh=brSearch.json.zyh
                }

            });
            tableInfo.getData()
        },
        methods: {
            Wf_KsChange: function (index) {
                //科室获取成功后再查询患者信息
                var obj = event.currentTarget;
                var selected = $(obj).find("option:selected");
                var ks = $(obj).val();
                var mc = selected.text();
                brSearch.ksmc = selected.text();
            },
            setJson: function (item) {
                console.log(item);
                this.json = item;
            },
            print:function () {
              window.print()
            },
        }
    });

    window.getTime = function (event, type) {
        if (type == 'star') {
            tableInfo.ksrq = $(event).val();
        } else if (type == 'end') {
            tableInfo.jsrq = $(event).val();
        }
    };

    //为table循环添加拖拉的div
    var drawWidthNum = $(".patientTable tr").eq(0).find("th").length;
    for (var i = 0; i < drawWidthNum; i++) {
        if (i >= 1) {
            $(".patientTable th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
        }
    }
    $(document).click(function () {
        if (this.className != 'selectGroup') {
            $(".selectGroup").hide();
        }
        $(".popInfo ul").hide();
    });
})();