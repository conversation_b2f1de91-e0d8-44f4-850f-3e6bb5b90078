<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <title>门特结算</title>
    <link rel="stylesheet" href="index.css?v=1">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10 ">

<div class="printArea printShow"></div>
<div class="brjz padd-t-10 background-f printHide flex-container">
    <div class="left_tab1" v-cloak style="width: 30%;">
        <div class=" padd-l-10 padd-r-10 flex-container" id="searchLeft">
            <div class="flex-container padd-r-10 flex-align-c">
                <span class="whiteSpace">入院科室：</span>
                <select-input @change-data="ksChange" :child="ksList"
                              :index="'ksmc'" :index_val="'ksbm'" :val="popContent.ksbm"
                              :name="'popContent.ksbm'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c">
                <span class="whiteSpace">搜&emsp;&emsp;索：</span>
                <input class="zui-input" v-model="popContent.text" @keyDown.13="changeDown($event)"
                       placeholder="姓名/挂号序号">
            </div>
        </div>

        <!-- 病人列表 -->
        <div class="personList zui-table-view padd-l-10 padd-r-10" id="brxxList">
            <div class="zui-table-header margin-top-10 cbd0d5">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>住院号</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>姓名</span></div>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>年龄</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table v-if="jsonList.length" class="zui-table table-width50">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :tabindex="$index"
                        @click="checkOne($index)"
                        class="ht30 cursor" :class="item.prmAkc190==undefined?'':'dd1c7'"
                        @dblclick="detail($index)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"><!--序号--></div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" v-text="item.zyh"><!--住院号--></div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" v-text="item.brxm"><!--姓名--></div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m"
                                 v-text="item.nl?(item.nl + '' + nldw_tran[item.nldw]):''"><!--年龄--></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="!jsonList.length" class="flex noData  text-center zan-border">暂无数据...</p>
            </div>
            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>
        </div>

    </div>

    <div class="contextDiv" style="width: 70%;">
        <div class="nh-menu ">
            <tabs @tab-active="tabBg" :num="which" key="a" @click.native="loadCon()"
                  :tab-child="[{text:'医保结算'},{text:'结算记录'}]"></tabs>
            <div class="padd-b-10 padd-t-10 flex-container flex-align-c">

            </div>
        </div>
        <div class="context">
            <div id="scnh">
                <div class="page_div mtjs"></div>
                <div class="page_div jsjl"></div>
            </div>
        </div>
    </div>
</div>
</body>
<script type="application/javascript" src="/newzui/pub/js/insuranceUtil.js"></script>
<script type="application/javascript" src="index.js"></script>
</html>
