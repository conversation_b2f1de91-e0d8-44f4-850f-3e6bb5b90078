<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>归档管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="gdgl.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary  icon-sx1 paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b"  @click="saveDate"><i class="icon-xz paddr-r5"></i>登记</button>
            <button class="tong-btn btn-parmary-b "  @click="qxdjDate"><i class=" ti-close paddr-r5"></i>取消登记</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline margin-f-l5">
                        <input  class="zui-input wh180" data-notEmpty="false" v-model="brxxContent.text" @keydown="changeDown($event,'text')"
                               @input="change(false,'text')" style="position: relative;">
                        <search-table :message="searchCon" :selected="selSearch"
                                      :page="page" :them="them" :them_tran="them_tran"
                                      @click-one="checkedOneOut" @click-two="selectOne">
                        </search-table>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline margin-f-l15 flex-container flex-align-c">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="timeVal" @keydown="searchHc()"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="出院时间" id="timeVal1" />
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view padd-r-10 padd-l-50" z-height="full" >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>住院号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>病案号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>姓名</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>性别</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出生日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>年龄</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>身份证号码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>工作单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>电话</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>入院时间</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>入院科室</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出院科室</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>入院诊断</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出院诊断</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>住院医生</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>归档标志</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>归档日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>归档人</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>接收标志</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>接收日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>接收人</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"  @dblclick="goNew(item)" ref="list" @click="checkSelect([$index,'some','brlist'],$event)" :class="[{'table-hovers':isChecked[$index]}]">

                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s relative">
                        <i class="title title-width" :data-title="item.zyh"  v-text="item.zyh"></i>

                    </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bah"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.brxm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.csrq,'date')"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.nl+nldw_tran[item.nldw]"></div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <i class="title title-width" :data-title="item.sfzjhm"  v-text="item.sfzjhm"></i>

                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <i class="title title-width" :data-title="item.gzdw"  v-text="item.gzdw"></i>

                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <i class="title title-width" :data-title="item.sjhm"  v-text="item.sjhm"></i>

                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.ryrq,'date')"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ryksmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cyksmc"></div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <i class="title title-width" :data-title="item.ryzdmcBz"  v-text="item.ryzdmcBz"></i>

                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cyzyzdmcBz"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.zyysxm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="istrue_tran[item.gdbz]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.gdrq,'date')"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.gdryxm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="istrue_tran[item.jsbz]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.jsrq,'date')"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jsryxm"></div></td>
                </tr>
                </tbody>
            </table>
        </div>
        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
<!--                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>-->
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" class="tableTr2">
<!--                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>-->
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBoxBr" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>
<script src="gdgl.js"></script>
</body>
</html>