var rightVue=new Vue({
    el:'#wrapper',
    data:{
        brxxContent:{},
        gzyhybContent:[],
		loadName:'/newzui/page/mzsf/sfjs/mzsf/insurancePort/014cdyhybgb/014cdyhyb',
		brxxContent:{},
		allIndex : null,
		jbContent:{},
		jbsearchCon:{},
		
    },
    created:function (){

    },
    mounted:function (){

    },
    updated:function (){

    },
    methods:{
		ryba:function(){
			popTable.isShow = true;
			
			loadPage(this.loadName);
			popTable.$nextTick(function () {
			    loadPage(rightVue.loadName);
			})
		},
		changeDown: function (event, type, content, searchCon, modelBm, showBm, modelMc,selSearch) {
		    //全局变量
		    bm = modelBm;
		    allBm = showBm;
		    selSearch = selSearch;
		    mc = modelMc;
		    this.selectType = type;
		    if (this[searchCon][this[selSearch]] == undefined) return;
		    this.inputUpDown(event,this[searchCon],selSearch)
		    this[content]=this[searchCon][this[selSearch]]
		    //选中之后的回调操作
		    if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
		        //************************************疾病（西医）
		        if (type.indexOf("xy") >= 0) {
		            var str = rightVue.jbContent['opspDiseCode'];
		            
		            Vue.set(this.popContent, type, rightVue.jbContent['opspDiseCode']);
		            Vue.set(this.popContent, modelBm, this.jbContent['opspDiseCode']);
		            Vue.set(this.popContent, modelMc, this.jbContent['opspDiseName']);
		        }
		        
		        this.nextFocus(event);
		        $(".selectGroup").hide();
		        this[selSearch]=-1
		    }
		},
		//当输入值后才触发
		change: function (add, type, val,selSearch,mc,bm) {
		    this.selectType = type;
		    if (!add) this.page.page = 1;
		    var _searchEvent = $(event.target.nextElementSibling).eq(0);
		    this.popContent[bm] = val;
		     this.page.parm = val;
		     this.popContent[mc]='';
		    //西医
		    if (type.indexOf("xy") >= 0) {
		        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm'
		            + '&json=' + JSON.stringify(this.page),
		            function (data) {
		                if (add) {
		                    for (var i = 0; i < data.d.list.length; i++) {
		                        rightVue.jbsearchCon.push(data.d.list[i]);
		                    }
		                } else {
		                    rightVue.jbsearchCon = data.d.list;
		                }
		                rightVue.page.total = data.d.total;
		                rightVue[selSearch] = 0;
		                if (data.d.list.length > 0 && !add) {
		                    $(".selectGroup").hide();
		                    _searchEvent.show();
		                    return false;
		                }
		            });
		    }
		    
		
		},
    },
})

var popTable = new Vue({
    el: '#popCenter',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        isShow: false,
        jsonList: []
    },
});


laydate.render({
    elem: '#enddate'
    , trigger: 'click',
	min:rightVue.getDate(),
    value: rightVue.brxxContent.enddate
    , theme: '#1ab394'
    , done: function (value, data) {
        rightVue.brxxContent.enddate = value;
        
    }
});
laydate.render({
    elem: '#begndate'
    , trigger: 'click',
	min:rightVue.getDate(),
    value: rightVue.brxxContent.begndate
    , theme: '#1ab394'
    , done: function (value, data) {
        rightVue.brxxContent.begndate = value;
        
    }
});
laydate.render({
    elem: '#hospIdeDate',
    type: 'datetime'
    , trigger: 'click',
	min:rightVue.getDate(),
    value: rightVue.getDate()
    , theme: '#1ab394'
    , done: function (value, data) {
        rightVue.brxxContent.hospIdeDate = value;
    }
});
