.ycgl-tj,.ycgl-sb{
width: 100%;
background: #fff;
overflow: hidden;
float: left;
}
.ycgl-title{
    width: 100%;
    background:#fbfbfb;
    min-height: 45px;
    font-size: 16px;
    padding:0 15px;
    box-sizing: border-box;
    color:#333333;
    display: flex;justify-content: space-between;
    align-items: center;
    font-weight: bold;
}
.ycgl-time{
    padding: 10px 0 10px 21px;
    width:100%;
    box-sizing: border-box;
}
.ycgl-echart{
width: 100%;
display: flex;
justify-content: space-between;
}
.ycgl-left{
    width: 420px;
    float: left;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: -14px;
}
.ycgl-canvas{
width: 100%;
float: left;
}
.ycgl-left-title{
    width: 100%;
    font-size:16px;
    color:#393f45;
    line-height: 30px;
    float: left;
    text-align: center;
    margin-top: -10px;
}
#main > div > canvas{
width: 100% !important;
}
#main > div:first-child{
 width: 100% !important;
}
.ycgl-right{
    float: right;
    width: calc(100% - 420px);
}
.color-c7{
    color:#757c83;
}
.font14{
    font-size: 14px;
}
.font30{
font-size: 30px;
}
.ycgl-list{
width: 100%;
padding: 10px;
box-sizing: border-box;
}