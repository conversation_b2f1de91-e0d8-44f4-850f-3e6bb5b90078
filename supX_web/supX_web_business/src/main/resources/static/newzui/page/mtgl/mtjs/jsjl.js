let jsjl = new Vue({
    el: '#jsjl',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        nums: 0,//明细汇总笔数
        totalFee: 0,//明细金额汇总
        totalPage: 0,//总页数
        page: 0,//当前页
        jsonList: [],
        totalContent: {},
        ifClick: true,
        isChecked: [],
        param: {
            page: 1,
            rows: 50,
            sort: '',
            parm: '',
            order: 'asc'
        },
    },
    mounted: function () {
        this.getData();
    },
    updated: function () {

    },
    methods: {
        getData: function () {
            common.openloading();
            let param = {
                ykc010: left_tab1.zyh,
                page: this.param.page,
                rows: this.param.rows,
            };
            let url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=fyxx&method=getMtFeeUploadDetail&parm=";
            $.fn.getJSONAjax(url, param, function (res) {
                if (res.a != '0') {
                    malert(res.c, "top", "defeadted");
                } else {
                    let page_model = JSON.parse(res.d);
                    jsjl.jsonList = page_model.records;
                    jsjl.totalPage = page_model.pages;
                    jsjl.page = page_model.current;
                }
            });
            common.closeLoading();
        },
        cancelSettlement: function () {
            common.openloading();
            let isSuccess = false, data = {}, param = {ykc010: left_tab1.zyh};
            let url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=settle&method=settleReportByMt&parm=";
            $.fn.getJSONAjax(url, param, function (res) {
                if (res.a != '0') {
                    malert(res, "top", "defeadted");
                    return;
                }
                data = JSON.parse(res.d);
                if (Object.keys(data).length == 0) {
                    malert("暂无结算信息,请确认患者是否已结算！", "top", "defeadted");
                    return;
                }
                isSuccess = true;
            })
            if (!isSuccess) return window.insuranceUtils.failCloseLoading();
            let code20a_request = this.toYinHaiRequestModel(param, window.insuranceUtils.models.code20A, "20A");
            let code20a_response = window.insuranceUtils.httpCall(code20a_request);
            if (!code20a_response) {
                return isSuccess;
            }
            //撤销结算上报
            let revoke_url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=settle&method=revoke&parm=";
            $.fn.getJSONAjax(revoke_url, new {yka103: data.yka103}, function (res) {
                if (res.a != '0') {
                    malert(res.c, "top", "defeadted");
                    //通知返回错误，取消医保出院交易
                    window.insuranceUtils.cancel(code20a_response.astr_jylsh, code20a_response.astr_jyyzm)
                } else {
                    window.insuranceUtils.confirm(code20a_response.astr_jylsh, code20a_response.astr_jyyzm)
                    isSuccess = true;
                }
            });
        },
        cancelFee: function () {
            if (!jsjl.jsonList || jsjl.jsonList.length <= 0) {
                malert("没有可取消的费用！", "bottom", "defeadted");
                return
            }
            if (this.isChecked.length == 0) {
                malert("请选择要取消的费用明细！", "bottom", "defeadted");
                return false;
            }
            common.openloading();
            if (!jsjl.ifClick) {
                malert("请勿重复点击！", "bottom", "defeadted");
                return;
            }
            jsjl.ifClick = false;
            let cancelList = [];//药品list
            for (let i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i]) {
                    cancelList.push(jsjl.jsonList[i]);
                }
            }
            if (cancelList.length > 50) {
                malert("单次最多选择50 条记录,后续我们会在优化！", "top", "defeadted");
                jsjl.ifClick = true;
                common.closeLoading();
                return false;
            }
            if (cancelList.length > 0) {
                let code43_xml = window.insuranceUtils.toYinHaiRequestModel(cancelList, window.insuranceUtils.models.code43, '43', true);
                let response = window.insuranceUtils.httpCall(code43_xml);
                if (!response) return this.resultFail();
                let url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=fyxx&method=del&parm=";
                let paramList = this.mapToParam(cancelList);
                $.fn.getJSONAjax(url, paramList, function (res) {
                    if (json.a != 0) {
                        malert(res.c, "top", "defeadted");
                    }
                });
            }
            jsjl.ifClick = true;
            common.closeLoading();
        }
    }
});
