(function(){
    //病理诊断
    var tableInfo = new Vue({
        el: '#blzd',
        mixins: [dic_transform, baseFunc, tableBase, mformat,checkData],
        data:{
        	popContent: {},
            jsonList: [],
        },
        methods: {
        	//进入页面加载列表信息
            getData: function () {
            	this.param.rows=10;
            	this.param.sort='zdbm';
            	if($("#blzdjsvalue").val()!=null&&$("#blzdjsvalue").val()!=''){
			        this.param.parm=$("#blzdjsvalue").val();
			    }else{
			        this.param.parm='';
			    }
        		$.getJSON("/actionDispatcher.do?reqUrl=BaglBmwhBlzd&types=queryBaglBlzd&parm="+JSON.stringify(this.param),function (json) {
        			tableInfo.totlePage = Math.ceil(json.d.total/tableInfo.param.rows);
        			tableInfo.jsonList = json.d.list;
        		});
            },
            //检索查询回车键  
		    searchHc: function() {
		        if(window.event.keyCode == 13) {
		          	this.getData();
		        }
		    },

            //修改值域类别
            edit: function (num) {
                    wap.open();
                    wap.title='编辑'
                //这里要拷贝值到popContent中，不能直接=
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            },
            
            //删除值域类别
            remove: function () {
                var blzdList = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                    	var blzd={};
                    	var removeUrl="/actionDispatcher.do?reqUrl=BaglBmwhBlzd&types=delete&";
                    	blzd.zdbm = this.jsonList[i].zdbm
                    	blzdList.push(blzd);
                    }
                }
                if(blzdList.length == 0){
                    malert("请选中您要删除的数据");
                    return false;
                }
                var json='{"list":'+JSON.stringify(blzdList)+'}';
                this.$http.post(removeUrl,json).then( function (data) {
                    this.getData();
                    if(data.body.a == 0){
                        malert("删除成功")
                    } else {
                        malert("删除失败")
                    }
                }, function (error) {
                    console.log(error);
                });
            },
            //新增清空编辑区
            addData: function(){
                wap.open();
                wap.title='新增'
                wap.popContent={};
            }
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            nums:1,
            title:'',
            popContent:{}

        },
        methods:{
            //关闭
            closes:function () {
                // brzcList.hzShow=false;
                wap.nums=1;
            },
            open: function () {
                wap.nums=0;
            },

            //保存值域类别
            saveData: function () {
                // 提交前验证数据（主要是非空）
                if(wap.popContent.zdbm=='' || wap.popContent.zdbm==null || wap.popContent.zdbm==undefined){
                    malert('诊断编码不能为空','top','defeadted');
                    return false;
                }if(wap.popContent.zdmc=='' || wap.popContent.zdmc==null || wap.popContent.zdmc==undefined){
                    malert('诊断名称不能为空','top','defeadted');
                    return false;
                }
                var json=JSON.stringify(wap.popContent);
                this.$http.post("/actionDispatcher.do?reqUrl=BaglBmwhBlzd&types=save&",json).then(function (data) {
                    if(data.body.a == 0){
                        tableInfo.getData();
                        if(wap.title=='编辑'){
                            wap.closes();
                            malert("保存成功");
                            return;
                        }if(wap.title=='新增'){
                            malert("新增成功");
                        }
                        this.popContent = {};
                    } else {
                        malert("上传数据失败");
                    }
                },function (error) {
                    console.log(error);
                });
            },
        }


    });
    tableInfo.getData();
    
    
    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function () {
        if ($(this).val() == '' || $(this).val() == null) {
            $(this).addClass("emptyError");
        } else {
            $(this).removeClass("emptyError");
        }
    });

    //为table循环添加拖拉的div
    var drawWidthNumBlzd = $(".patientTableBlzd tr").eq(0).find("th").length;
    for(var i=0;i<drawWidthNumBlzd;i++){
        if(i>=2){
            $(".patientTableBlzd th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
        }
    }
})();