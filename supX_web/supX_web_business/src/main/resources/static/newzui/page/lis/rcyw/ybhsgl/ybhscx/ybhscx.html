<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>样本审核管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ybhsgl.css" rel="stylesheet"/>
</head>
<style>
    .endActive{
        border: 1px solid #1abc9c;
        height: 30px;
        box-shadow: 0 0 6px 0 rgba(26,188,156,0.43);
        border-radius: 4px;
    }
    .xiala{
        position: absolute;
        background: #fff;
        z-index: 11;
        width: 100%;
        top: 39px;
    }
    .xiala li{
        color:#757c83;
        font-size:14px;
    }
    .xiala li:hover{
        cursor: pointer;
        background:rgba(26,188,156,0.08);
    }
    .xiala li.Xactive{
        color:#ffffff;;
        background:#1abc9c;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper">
    <div class="panel">
        <div class="panel-head border-bottom  background bg-fff">
            <div class="zui-row">
                <div class="col-x-12 bg-f9f9f9">
                    <button class="zui-btn btn-primary" @click="heshou">核收</button>
                    <button class="zui-btn btn-primary-b">刷新</button>
                    <button class="zui-btn btn-primary-b" @click="jushou()">拒收</button>
                    <button class="zui-btn btn-primary-b">作废</button>
                    <button class="zui-btn btn-primary-b">打印</button>
                    <button class="zui-btn btn-primary-b">生成样本号</button>
                    <!--<div style="position: relative;width: 200px">-->
                    <!--</div>-->
                </div>
                <div class="col-x-12 bg-fff top-xia">
                    <div class="zui-inline  zui_bottom">
                        <div class="zui-input-inline zui-date wh250">
                            <i class="datenox fa-calendar"></i>
                            <span style="float: left;line-height: 32px;margin-right: 4px;">申请日期</span> <input type="text" name="phone" class="zui-input todate"   style="width:75%;height: 32px"  placeholder="请选择开始时间" />
                        </div>
                        <!--<span style="margin-left: 5px;">到</span>-->
                        <!--<div class="zui-input-inline zui-date wh150">-->
                        <!--<i class="datenox fa-calendar"></i>-->
                        <!--<input type="text" name="phone" class="zui-input todatetwo" style="width: 97%;height: 32px" v-model="jsrq"  placeholder="请选择结束时间" />-->
                        <!--</div>-->
                    </div>
                    <!--<button class="zui-btn btn-primary" @click="getData">查询</button>-->
                    <div class="zui-inline  zui_bottom">
                        <!--<label class="zui-form-label">条码打印机</label>-->
                        <label>条码打印机</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" placeholder="FAX" type="text"/>
                        </div>
                        <!--<select-input @change-data="resultChange" :not_empty="false"-->
                        <!--:child="lxrdhlx_tran" :index="popContent.lxdhlbdm" :val="popContent.lxdhlbdm"-->
                        <!--:name="'popContent.lxdhlbdm'">-->
                        <!--</select-input>-->
                    </div>
                    <div class="zui-inline  zui_bottom">
                        <label class="">检索码</label>
                        <div class="zui-input-inline">
                            <input   class="zui-input" placeholder="FAX" type="text"/>
                        </div>
                    </div>

                    <div class="zui-inline  zui_bottom">
                        <button class="zui-btn btn-primary">查询</button>
                        <button class="zui-btn btn-primary show zui-auto color-primary zui-select-inline">展开查询条件</button>
                        <button class="zui-btn btn-primary hide zui-auto color-primary zui-select-inline">关闭查询条件</button>
                    </div>
                    <div class="MaxRize showRize hide">
                        <div class="zui-inline  zui_bottom">
                            <label class="">门诊卡号</label>
                            <div class="zui-input-inline">
                                <input   class="zui-input" placeholder="请输入门诊卡号" type="text"/>
                            </div>
                        </div>
                        <div class="zui-inline  zui_bottom">
                            <label class="">门诊卡号</label>
                            <div class="zui-input-inline">
                                <input   class="zui-input" placeholder="请输入门诊卡号" type="text"/>
                            </div>
                        </div>
                        <div class="zui-inline  zui_bottom">
                            <label class="">门诊卡号</label>
                            <div class="zui-input-inline">
                                <input   class="zui-input" placeholder="请输入门诊卡号" type="text"/>
                            </div>
                        </div>
                        <div class="zui-inline  zui_bottom">
                            <label class="">门诊卡号</label>
                            <div class="zui-input-inline">
                                <input   class="zui-input" placeholder="请输入门诊卡号" type="text"/>
                            </div>
                        </div>
                    </div>
                </div>

                <!--<input type="checkbox" />打印取报告条码-->
                <!--<input type="checkbox" />登记后打印条码-->

            </div>
        </div>
    </div>
    <div class="zui-table-view top_size"  style="border:none; margin-top: 109px">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th fixed="left" style="text-align:center;">
                        <div class="zui-table-cell"><input type="checkbox" /></div>
                    </th>
                    <th :field="id" fixed="left" :width="100px" :sort="true">
                        <div class="zui-table-cell"><span>ID</span></div>
                    </th>
                    <th :field="username"   :width="100px" :sort="true" :style="text-align:center;">
                        <div class="zui-table-cell"><span>患者姓名</span></div>
                    </th>
                    <th :field="sex" :width="80px" :sort="true">
                        <div class="zui-table-cell"><span>年龄</span></div>
                    </th>
                    <th :field="city" :width="80px" :sort="true">
                        <div class="zui-table-cell"><span>性别</span></div>
                    </th>
                    <th :field="sign" :width="120px">
                        <div class="zui-table-cell"><span>联系电话</span></div>
                    </th>
                    <th :field="experience" :width="200px">
                        <div class="zui-table-cell"><span>证件号码</span></div>
                    </th>
                    <th :field="score" :width="100px">
                        <div class="zui-table-cell"><span>证件类型</span></div>
                    </th>
                    <th :field="classify" :width="100px" :sort="true">
                        <div class="zui-table-cell"><span>患者类型</span></div>
                    </th>
                    <th :field="wealth" :width="100px">
                        <div class="zui-table-cell"><span>状态</span></div>
                    </th>
                    <th :field="wealthq" :width="100px">
                        <div class="zui-table-cell"><span>保险类别1</span></div>
                    </th>
                    <th :field="wealthqw" :width="100px">
                        <div class="zui-table-cell"><span>保险类别12</span></div>
                    </th>
                    <th :field="wealtheq" :width="100px">
                        <div class="zui-table-cell"><span>保险类别31</span></div>
                    </th>
                    <th :field="wealthqr" :width="100px">
                        <div class="zui-table-cell"><span>保险类别41</span></div>
                    </th>
                    <th :width="100px" fixed="right" :style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body">
            <table class="zui-table" id="zui-table">
                <tbody>
                <tr data-index="0" v-for="(item, $index) in object" >
                    <td>
                        <div class="zui-table-cell">
                            <input type="checkbox" />
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.num"></div></td>
                    <td><div class="zui-table-cell" v-text="item.name"></div></td>
                    <td><div class="zui-table-cell" v-text="item.time"></div></td>
                    <td><div class="zui-table-cell" v-text="item.sex"></div></td>
                    <td><div class="zui-table-cell" v-text="item.phone"></div></td>
                    <td><div class="zui-table-cell title" data-title="item.card" v-text="item.card"></div></td>
                    <td><div class="zui-table-cell" v-text="item.cardtype"></div></td>
                    <td><div class="zui-table-cell" v-text="item.patient"></div></td>
                    <td><div class="zui-table-cell" v-text="item.safe"></div>
                    <td><div class="zui-table-cell" v-text="item.safe"></div>
                    <td><div class="zui-table-cell" v-text="item.safe"></div>
                    <td><div class="zui-table-cell" v-text="item.safe"></div>
                    <td><div class="zui-table-cell" v-text="item.safe"></div>

                    <td>
                        <div class="zui-table-cell">
                            <div class="cell_dropdown">
                                <i class="icon-ylfwxm icon-font"></i>
                                <i class="icon-ysyw icon-font" @click="jushouOne($index)"></i>
                                <i class="icon-yyyw icon-font"></i>
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<div class="popus" id="popus">
    <div class="pop-system">
        <h2><span>系统提示</span>
            <span class="fr" @click="cancel">&times;</span>
        </h2>
        <div class="pop-text"><i class="dangqian">确定作废</i>：<i class="color-1a">陈小春</i>的申请信息吗？</div>
        <div class="pop-ok">
            <button class="pop-btn" @click="cancel">取消</button>
            <button class="pop-btn pop-confirm" @click="confirm">确定</button>
        </div>
    </div>
</div>
<div class="popus-right" id="pright">
    <h2><span>拒收理由</span>
        <span class="fr" @click="cancel">&times;</span>
    </h2>
    <div class="sample">样本拒收原因</div>
    <div class="sample-select">
        <select>
            <option>样本抗凝剂选择错误</option>
            <option>样本抗凝剂选择错误</option>
            <option>样本抗凝剂选择错误</option>
            <option>样本抗凝剂选择错误</option>
            <option>样本抗凝剂选择错误</option>
        </select>
    </div>
    <div class="sample">拒收处理</div>
    <div class=" sample-texarea">
        <textarea placeholder="请输入拒收处理意见"></textarea>
    </div>
    <div class="pop-ok sample-btn">
        <button class="pop-btn" @click="cancel">取消</button>
        <button class="pop-btn pop-confirm" @click="confirm">确定</button>
    </div>
</div>

<script src="ybhsgl.js"></script>
</body>
</html>
