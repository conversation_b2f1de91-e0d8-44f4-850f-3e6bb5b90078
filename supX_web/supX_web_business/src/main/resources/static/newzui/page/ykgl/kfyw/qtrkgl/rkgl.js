var qxksbm = '';

//列表信息
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data: {
        cxShow: false,
        ifClick: true,
        isShowpopL: false,
        isTabelShow: false,
        isShowkd: true,
        popContent: {
            rkfs: '01',
            cgrkfs: '01'
        },
        ghdwList: [],//供货单位
        keyWord: '',
        zdrq: getTodayDateTime(), //获取制单日期
        zdyxm: '',
        cgry: '',
        cgryList: [],
        jsonList: [],
        KFList: [], //库房
        isUpdate: 0,
        isSh: 0,//审核
        search: '',
        jyinput: false, //禁用输入框
        rkfs_tran: {
            '01': '入库',
            '02': '退库',
            '03': '盘点入库',
            '04': '采购入库',
            '05': '库存调整',
            '06': '药房退库',
            '07': '科室退库',
            '08': '其他',
        },
        cgfs_tran: {
            '01': '其它入库',
        },
        //打印数据
        printData: {},
        mxShShow: true,
        rkdList: [], //入库单集合
        rkd: {}, //入库单对象采购员
        csqxContent: {
            N04003001201111: undefined,
        },
        param: {
            zt: '9',
            rows: 10,
            page: 1,
            beginrq: null,
            endrq: null,
            parm: ''
        },
        dg: {
            page: 1,
            rows: 9999,
            sort: "",
            order: "asc",
            parm: ""
        },
        isCheck: null,
        rkdDetail: [], //入库明细集合
        zfIfHide: true, //作废按钮是否显示
        num: 0,

        zhuangtai: {
            "0": "未审核",
            "1": "已审核",
            "2": "已作废",
            "3": "未通过",
        },

        TjShow: true,//提交
        ShShow: false,//拒绝 审核
        zfShow: true, //作废
        dyShow: false, //打印
        json: {
            jjzj: 0.0,
            ljzj: 0.0,
        },//价格
        totlePage: 0,
        activeIndex1: undefined,
        hoverIndex1: undefined,
		totalypjj:0,
		totalyplj:0,
    },
    watch: {
        'jsonList': {
            handler: function (val, oldval) {
                this.money()
            },
            deep: true,//对象内部的属性监听，也叫深度监听
            immediate: true
        },
    },
    mounted: function () {
        var myDate = new Date();
        this.param.beginrq = this.fDate(new Date(), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: this.param.beginrq,
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: this.param.endrq,
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });

        this.getKFData();
        this.getData();
$('.wh182').find('.zui-input').addClass("title");

    },
    updated: function () {
        changeWin();
    },
    methods: {
        getCxsl: function (rksl, cxsl, ycxsl, index) {
            if (rksl - ycxsl < parseFloat(cxsl)) {
                malert('冲销数量不得大于入库数量', 'top', 'defeadted');
                this.jsonList[index]['cxsl'] = ''
            } else {
                return true
            }
        },
        resultChangeReset: function (val) {
            this.$set(this.rkd, "ghdw", val[0])
            this.$set(this.rkd, "ghdwmc", val[4].dwmc)
        },
        isCx: function (item) {
            return JSON.stringify(item) != '{}' ? item.totalYpjj ? item.totalYpjj.indexOf('-') == -1 ? true : false : true : true;
        },
        isShFun: function (item) {
            return JSON.stringify(item) != '{}' && item.shzfbz == 0 ? true : false;
        },
        cxClick: function () {
            this.cxShow = !this.cxShow;
            this.ShShow = !this.ShShow;
            this.dyShow = !this.dyShow;
        },
        money: function () {
            var reducers = {
                totalInEuros: function (state, item) {
                    return state.jjzj += item.ypjj * parseFloat(item.rksl);
                },
                totalInYen: function (state, item) {
                    return state.ljzj += item.yplj * parseFloat(item.rksl);
                }
            };
            var manageReducers = function (reducers) {
                return function (state, item) {
                    return Object.keys(reducers).reduce(function (nextState, key) {
                        reducers[key](state, item);
                        return state;
                    }, {})
                }
            }
            var bigTotalPriceReducer = manageReducers(reducers);
            this.jsonList.reduce(bigTotalPriceReducer, this.json = {
                jjzj: 0.0,
                ljzj: 0.0,
            });
        },
        loadNum: function () {
            this.num = wrapper.num;
        },
        //进入页面加载单据列表信息
        getData: function () {
            common.delayOpenloading({el: '.zui-table-view'});
            //清空进价和零件总计
            this.json.jjzj = 0;
            this.json.ljzj = 0;
            //清空入库明细信息
            this.rkdDetail = [];
            this.rkdList = [];
            //拼接参数对象
            if (this.param.zt != '9') {
                Vue.set(this.param, 'shzfbz', this.param.zt);
            } else {
                Vue.set(this.param, 'shzfbz', null);
            }
			Vue.set(this.param, 'cgrkfs', '01');
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywRkd&types=queryRkd&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == 0) {
                        wrapper.rkdList = data.d.list;
                        wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
						if(data.d.count){
							wrapper.totalyplj=data.d.count.TOTALYPLJ
							wrapper.totalypjj=data.d.count.TOTALYPJJ
						}
                        common.closeLoading()
                    } else {
                        malert(data.c, 'top', 'defeadted');
                        common.closeLoading()
                    }
                });
        },
        //入库审核
        passData: function () {
            if (!this.ifClick) return;
            this.ifClick = false;
            if (!this.rkd.kfbm) {
                malert("请选择库房!", 'top', 'defeadted');
                return;
            }
            if (!this.rkdList[this.isCheck]) {
                malert("请选择单据!", 'top', 'defeadted');
                return;
            }
            var json = {
                "rkdh": this.rkdList[this.isCheck]['rkdh'],
                "kfbm": this.rkd.kfbm,
                "qxksbm": qxksbm
            };
            if (!json.rkdh) {
                malert("请选择单据!", 'top', 'defeadted');
                return;
            }
            this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywRkd&types=passRkd', JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.ifClick = true;
                        malert("审核成功!", 'top', 'success');
                        //打印数据
                        var mes = confirm('是否打印入库单');
                        if (mes == true) {
                            //帆软打印
                            console.log(json.kfbm + "||" + json.rkdh);
                            wrapper.updateDycsAndPrint(json.kfbm, json.rkdh);
                        } else {

                        }
                        wrapper.isShowkd = true;
                        wrapper.TjShow = false;
                        wrapper.zfShow = false;
                        wrapper.TjShow = false;
                        wrapper.getData();
                    } else {
                        wrapper.ifClick = true;
                        wrapper.getData();
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },

        //打印
        printDJ: function () {
            //是否有打印权限
            if (wap.csqxContent.cs00200100101 == '0') {
                this.printData = {};
                return;
            }
            var json = {
                "rkdh": this.rkdList[this.isCheck]['rkdh'],
                "kfbm": this.rkd.kfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywRkd&types=print&parm=' + JSON.stringify(json), function (data) {
                wrapper.printData = data.d;
                //小数部分和日期格式调整
                var djmx = data.d.djmx;
                var jjzj = 0;
                var ljzj = 0;
                for (var i = 0; i < djmx.length; i++) {
                    //小数部分和日期格式调整
                    var yxqz = djmx[i].yxqz;
                    jjzj += djmx[i].ypjjje;
                    ljzj += djmx[i].ypljje;
                    wrapper.printData.djmx[i]['yxqz'] = wrapper.fDate(yxqz, 'date');
                    djmx[i].ypjjje = wrapper.fDec(djmx[i].ypjjje, 2);
                    djmx[i].ypljje = wrapper.fDec(djmx[i].ypljje, 2);
                }
                wrapper.printData.dj.jjjehz = wrapper.fDec(jjzj, 2);
                wrapper.printData.dj.ljjehz = wrapper.fDec(ljzj, 2);
            });
        },

        updateDycsAndPrint: function (kfbm, rkdh) {
            var parm = {
                'rkdh': rkdh
            };
            //更新打印次数
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywRkd&types=updateDycs' + '&parm=' + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        //调用帆软打印
                        var frpath = "";
                        if (window.top.J_tabLeft.obj.frprintver == "3") {
                            frpath = "%2F";
                        } else {
                            frpath = "/";
                        }
                        var reportlets = "[{reportlet: 'fpdy" + frpath + "ykgl" + frpath + "ykgl_rkd.cpt',yljgbm:'" + jgbm + "',kfbm:'" + kfbm + "',djh:'" + rkdh + "',dysj:'" + wrapper.fDate(wrapper.rkd.uptimestamp, 'AllDate') + "'}]";
                        console.log(reportlets);
                        if (FrPrint(reportlets, null)) {
                            return;
                        }
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },

        print: function () {
            if (this.printData.dj == undefined) {
                console.log('无打印权限');
                return;
            }
            // 查询打印模板
            var json = {
                repname: '药库入库单'
            };
            //帆软打印
            if (!window.top.J_tabLeft.obj.FRorWindow) {
				let rkdh = '';
				
				if(this.rkd.iscx =='1'){
				            	rkdh = this.rkd.cxdh;
				            }else{
				            	rkdh = this.rkd.rkdh;
				            } 
                this.updateDycsAndPrint(this.rkd.kfbm, rkdh);
            } else {
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    // 根据每页行数循环打印
                    for (var i = 0; i < Math.ceil(wrapper.printData['djmx'].length / wrapper.printData['dj']['rows']); i++) {
                        // 清除打印区域
                        wrapper.clearArea(json.d[0]);
                        // 绘制模板的canvas
                        wrapper.drawList = JSON.parse(json.d[0]['canvas']);
                        wrapper.creatCanvas();
                        wrapper.reDraw();
                        // 为打印前生成数据
                        var list = [];
                        var pageYpjjje = 0;
                        var pageYpljje = 0;
                        for (var j = 0; j < wrapper.printData['dj']['rows']; j++) {
                            var row = j + (i * wrapper.printData['dj']['rows']);
                            if (wrapper.printData['djmx'][row] == null) break;
                            list.push(wrapper.printData['djmx'][row]);
                            pageYpjjje += parseInt(wrapper.printData['djmx'][row]['ypjjje']);
                            pageYpljje += parseInt(wrapper.printData['djmx'][row]['ypljje']);
                        }
                        wrapper.printData['dj']['ypjjjePage'] = wrapper.fDec(pageYpjjje, 2);
                        wrapper.printData['dj']['ypljjePage'] = wrapper.fDec(pageYpljje, 2);
                        // 在调用静态字段处理前，保留两位小数的转换
                        wrapper.printData['dj']['jjjehz'] = wrapper.fDec(wrapper.printData['dj']['jjjehz'], 2);
                        wrapper.printData['dj']['ljjehz'] = wrapper.fDec(wrapper.printData['dj']['ljjehz'], 2);
                        wrapper.printData['dj']['total'] = Math.ceil(wrapper.printData['djmx'].length / wrapper.printData['dj']['rows']);
                        wrapper.printData['dj']['page'] = i + 1 + "     /";
                        wrapper.printContent(wrapper.printData['dj']);
                        wrapper.printTrend(list);
                        // 开始打印

                        window.print();
                    }
                });
            }

        },

        //作废2018/07/04二次弹窗作废提示
        invalidData: function (num) {
            if (num != null && num != undefined) {
                this.isCheck = num;
            }
            if (!this.param.kfbm) {
                malert("请选择库房!", 'top', 'defeadted');
                return;
            }

            if (common.openConfirm("确认作废该条信息吗？", function () {
                var json = {
                    rkdh: wrapper.rkdList[wrapper.isCheck].rkdh,
                    kfbm: wrapper.param.kfbm
                };
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywRkd&types=invalidRkd', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.getData();
                        malert("作废成功！", 'top', 'success');
                        wrapper.cancel();
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
            })) {
                return false;
            }
        },
        openDetail: function (index) {
            wap.title = '查看';
            this.isCheck = index;
            this.isShowpopL = false;
            this.TjShow = false;
            this.zfShow = false;
            this.TjShow = false;
            this.dyShow = false;
            this.ShShow = false;
            this.mxShShow = false;
            this.isShowkd = false;
            if(this.rkdList[index].iscx =='1'){
            	this.dg['parm'] = this.rkdList[index]['cxdh'];
            }else{
            	this.dg['parm'] = this.rkdList[index]['rkdh'];
            }
            this.parm['uptimestamp'] = this.rkdList[index]['uptimestamp'];
            this.rkd = this.rkdList[index];//赋值
            wap.rkd = this.rkdList[index];
            wap.shzfbz = this.rkdList[index].shzfbz;
            this.zdyxm = this.rkdList[index].zdyxm;
            this.zdrq = this.rkdList[index].zdrq;
            this.initMxList()
        },
        //选中单据信息加载出相对应的单据内容明细（双击查看）
        showDetail: function (index) {
            this.isSh = 1;//审核标志
            wap.title = '审核';
            $("#divlink").remove();
            this.isCheck = index;
            this.isShowpopL = false;
            this.isShowkd = false;
            $('#bzms').attr('disabled', true);
            this.TjShow = false;
            this.zfShow = false;
            this.TjShow = true;
            this.dyShow = false;
            this.ShShow = true;
            if(this.rkdList[index].iscx =='1'){
            	this.dg['parm'] = this.rkdList[index]['cxdh'];
            }else{
            	this.dg['parm'] = this.rkdList[index]['rkdh'];
            }
            this.parm['uptimestamp'] = this.rkdList[index]['uptimestamp'];
            this.rkd = this.rkdList[index];//赋值
            wap.rkd = this.rkdList[index];
            this.zdyxm = this.rkdList[index].zdyxm;
            this.zdrq = this.rkdList[index].zdrq;
            wap.shzfbz = this.rkdList[index].shzfbz;
            this.initMxList()


        },
        //编辑（按钮）
        editIndex: function (index) {
            //编辑标志
            this.isCheck = index;
            this.isShowpopL = true;
            this.isShowkd = false;
            this.dyShow = false;
            this.TjShow = true;
            this.zfShow = false;
            this.ShShow = false;
            this.jyinput = false;
            this.mxShShow = true;
            $("#zfbtn").hide();
            $("#divlink").remove();
            if (this.rkdList[index].shzfbz == 0) {
                this.ShShow = true;
                this.mxShShow = true;
                this.jyinput = false;
            } else {
                this.ShShow = false;
                this.jyinput = true;
                this.mxShShow = false;
                if (this.rkdList[index].shzfbz == 1) {
                    this.dyShow = true;
                    this.mxShShow = true;
                    this.TjShow = true;
                    this.isShowpopL = false;
                }
            }
            this.zdyxm = this.rkdList[index].zdyxm;
            this.zdrq = this.rkdList[index].zdrq;
            //this.ShShow=true;
            if(this.rkdList[index].iscx =='1'){
            	this.dg['parm'] = this.rkdList[index]['cxdh'];
            }else{
            	this.dg['parm'] = this.rkdList[index]['rkdh'];
            }
            this.parm['uptimestamp'] = this.rkdList[index]['uptimestamp'];
            this.rkd = this.rkdList[index];
            wap.shzfbz = this.rkdList[index].shzfbz;
            this.initMxList();
            this.printDJ();
        },
        initMxList: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywRkd&types=queryRkdMxByRkd&dg=' + JSON.stringify(this.dg) + '&parm=' + JSON.stringify(this.parm), function (data) {
                if (data.a == '0' && data.d && data.d.list.length != 0) {
                    wrapper.jsonList = data.d.list;

                    wrapper.rkd.ghdw = data.d.list[0].ghdw
                }
            });
        },
        //提交所有药品
        submitAll: function () {
            if (this.isSubmited) {
                malert("数据提交中，请稍后！", 'top', 'defeadted');
                return;
            }
            if (this.jsonList.length <= 0) {
                malert("没有可提交的数据", 'top', 'defeadted');
                return;
            }
            var jsonList = [];
            if (this.cxShow) {
                for (var i = 0; i < this.jsonList.length; i++) {
                    if (this.jsonList[i].cxsl) {
                        var item = JSON.parse(JSON.stringify(this.jsonList[i]));
                        item.mxxh = ++this.jsonList[this.jsonList.length - 1]['mxxh']
                        item.cxsl = '-' + item.cxsl;
                        jsonList.push(item);
                    }
                }
            } else {
                jsonList = this.jsonList
            }
            //锁定提交功能
            this.isSubmited = true;
            var json = {
                "list": {
                    "rkd": this.rkd,
                    "rkdmx": jsonList
                }
            };
            var url = !this.cxShow ? 'savemx' : 'rkcx'
            this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywRkd&types=' + url, JSON.stringify(json)).then(function (data) {
                if (data.body.a == 0) {
                    malert("数据更新成功", 'top', 'success');
                    wrapper.cancel()
                    wrapper.getData();
                } else {
                    var msg = '';
                    if (data.body.d != null && data.body.d.length != 0) {
                        for (var i = 0; i < data.body.d.length; i++) {
                            msg += data.body.d[i];
                        }
                        malert(msg, 'top', 'defeadted');

                    } else {
                        malert("保存失败:" + data.body.c, 'top', 'defeadted');
                    }
                }
                //解锁提交功能
                wrapper.isSubmited = false;
            }, function (error) {
                console.log(error);
                //解锁提交功能
                wrapper.isSubmited = false;
            });
        },
        //双击修改弹窗
        edit: function (num) {
            this.isUpdate = 1;
            wap.open();
            wap.num = num;
            if (wrapper.isSh == 0) {
                wap.title = '编辑药品';
            }
            if (wrapper.isSh == 1) {
                wap.title = '查看详情';
            }
            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            //药品名称
            Vue.set(wap.popContent, 'text', wap.popContent.ypmc);
            //招标方式
            Vue.set(wap.popContent, 'zbfsbm', wap.popContent.zbfs);
            this.mxxh = wap.popContent.mxxh;
        },
        //删除2018-07-04 二次删除弹窗提示
        scmx: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.jsonList.splice(index, 1);
            })) {
                return false;
            }

        },

        //取消
        cancel: function () {
            this.rkd = {};
            wap.rkd = {};
            wap.shzfbz = '';
            this.jsonList = [];
            this.cxShow = false;
            this.isShowpopL = false;
            this.isShowkd = true;
            this.ShShow = false;
            this.zfShow = false;
            this.dyShow = false;
        },
        kdFun: function (index) {
            this.num = index;
            this.loadNum();
            switch (wrapper.num) {
                case 0:
                    this.rkd = {
                        kfbm: this.param.kfbm,
                        cgry: this.csqxContent.N04003001201112,
                        ghdw: this.ghdwList[0].dwbm,
                        dwmc: this.ghdwList[0].dwmc,
                        cgrkfs: '01'
                    };
                    this.jsonList = [];
                    this.isShowkd = false;
                    this.TjShow = true;
                    this.ShShow = false;
                    this.mxShShow = true;
                    this.isShowpopL = true;
                    this.zfShow = false;
                    this.dyShow = false;
                    $('#bzms').attr('disabled', false);
                    $("#zfbtn").hide();
                    if (sessionStorage.getItem("userName" + userId)) {
                        var reg = /^[\'\"]+|[\'\"]+$/g;
                        wrapper.zdyxm = sessionStorage.getItem("userName" + userId).replace(reg, '');
                    }
                    wrapper.jyinput = false;
                    break;
                case 1:
                    if (!this.rkd.cgrkfs) {
                        malert("请先选择采购方式！", 'top', 'defeadted');
                        return
                    }
                    if (!this.rkd.ghdw) {
                        malert("请先选择供货单位！", 'top', 'defeadted');
                        return
                    }
                    if (!this.rkd.cgry) {
                        malert("请先选择采购员！", 'top', 'defeadted');
                        return
                    }

                    wap.open();
                    wap.title = '添加药品';
                    this.isUpdate = 0;
                    break;
            }

        },
        getKFData: function () {
            //初始化页面记载库房
            this.updatedAjax('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040030012011"}',
                function (data) {
                    if (data.a == 0 && data.d && data.d.length > 0) {
                        this.KFList = data.d;
                        qxksbm = data.d[0].ksbm;
                        //默认选择库房
                        Vue.set(this.param, 'kfbm', data.d[0].kfbm);
                        Vue.set(this.rkd, 'kfbm', data.d[0].kfbm);
                    } else {
                        malert("药库获取失败", 'top', 'defeadted');
                    }
                });
            //初始化页面记载采购人员
            var str_parm = {};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(str_parm), function (json) {
                if (json.a == 0) {
                    wrapper.cgryList = json.d.list;
                } else {
                    malert("采购人员获取失败", 'top', 'defeadted');
                }
            });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            Vue.set(this.param, 'kfbm', val[0]);
            Vue.set(this.param, 'kfmc', val[4]);
            for (var i = 0; i < this.KFList.length; i++) {
                if (val[0] == this.KFList[i].kfbm) {
                    qxksbm = this.KFList[i].ksbm;
                }
            }
            Vue.set(this.rkd, 'kfbm', val[0]);
            wap.getCsqx();
            this.getData();
            this.nextFocus(val[1])
        },
        resultChange: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);

            wap.popContent.cgrkfs = val[0]
            this.$forceUpdate()
            wap.$forceUpdate()
            this.getData();
        },
    }
});


var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        index: 0,
        flag: false,
        ksList: [],
        hszList: [],
        ywckList: [],
        title: '',
        shzfbz: '',
        num: 0,
        csContent: {},
        jsonList: [],
        cgryList: [],//采购员
        csqxContent: {}, //参数权限对象
        ckdContent: {}, //出库单对象
        KSList: [], //领用科室
        zbfsList: [],//招标方式
        ghdwList: [],//供货单位
        KFList: [], //库房
        ryList: [], //领用人
        glryList: [], //过滤领用人
        ypcdList: [], //药品产地
        popContent: {},
        jcqjList: [],
        dg: {
            page: 1,
            rows: 200,
            sort: "",
            order: "asc",
            total: null

        },
        page: {
            page: 1,
            rows: 200,
            sort: "",
            order: "asc",

        },
        them_tran: {},
        them: {
            '生产批号': 'scph',
            '药品编号': 'ypbm',
            '药品名称': 'ypmc',
            '商品名': 'ypspm',
            '产地': 'cdmc',
            '库存数量': 'kcsl',
            '可用库存': 'sjkc',
            '有效期至': 'yxqz',
            '规格': 'ypgg',
            '分装比例': 'fzbl',
            '进价': 'ypjj',
            '零价': 'yplj',
            '药房进价': 'yfypjj',
            '药房零价': 'yfyplj',
            '库房单位': 'kfdwmc',
            '药房单位': 'yfdwmc',
            '效期': 'yxqz',
            '药品剂型': 'jxmc',
            '药品国家码': 'ypgjm',
        },
        selSearch: 0,
        str: '',//监听jsonlist改变
        them_tran1: {},
        rkd: {},
        them1: {
            '产地编号': 'cdbm',
            '产地名称': 'cdmc',
        },
        queryStr: {
            page: 1,
            rows: 20,
            sort: "cdbm",
            order: "asc",
            total: null
        },
        selSearch1: -1,
        searchCon1: [],
        mxxh: 0,//明细序号，用户更改出库单
    },
    computed: {
        jgfsFun: function () {
            if (this.popContent.jgfs == '0') {
                return true
            } else if (this.popContent.jgfs == '1') {
                return false
            }
        },
        shDisabled: function () {
            return this.shzfbz == '1' ? true : false
        },
        isDisabled: function () {
            return this.popContent.cgrkfs != '00';
        },
        isQTRK: function () {
            let shzfbz = this.shzfbz == '1' ? true : false;
            let jgfs = this.popContent.jgfs == '0'
            let qtrk = this.popContent.cgrkfs == '01' || wrapper.rkd.rkfs == '01';
            if (!qtrk) {
                return (shzfbz || jgfs);
            }
            return qtrk;
        },
        isQTRK2: function () {
            let shzfbz = this.shzfbz == '1' ? true : false;
            let qtrk = this.popContent.cgrkfs == '01' || wrapper.rkd.rkfs == '01';
            if (!qtrk) {
                return shzfbz;
            }
            return qtrk;
        }
    },
    mounted: function () {
        Mask.newMask(this.MaskOptions('_scrq', 'YYYY-DD-MM'));
        Mask.newMask(this.MaskOptions('_yxqz', 'YYYY-DD-MM'));
        this.getJzData();
        this.getCsqx(); //加载完库房再次加载参数权限
    },
    methods: {
        setTime(event, val) {
            this.popContent[val] = event.target.value
            console.log(this.timeTabArr)
        },
        showTime: function (element, code) {
            var _laydate = laydate.render({
                elem: '#' + element,
                show: true,//直接显示
                type: 'date'
                , theme: '#1ab394',
                done: function (value, data) {
                    wap.popContent[code] = value

                }
            });
            if (code == 'yxqz') {
                _laydate.config.min = {
                    year: new Date().getFullYear(),
                    month: new Date().getMonth(), //关键
                    date: new Date().getDate(),
                    hours: 0,
                    minutes: 0,
                    seconds: 0

                }
            }
            this.$forceUpdate();
        },
        validity: function (value) {
            var nowDate = new Date();
            var valueDate = new Date(value);
            if (nowDate.getTime() > valueDate.getTime()) {
                malert("有效期小于当前时间", 'top', 'defeadted');
                return false;
            } else {

                var iday = parseInt(valueDate - nowDate) / 1000 / 60 / 60 / 24;
                if (iday < 90) {
                    malert("有效期小于90天", 'top', 'defeadted');
                    return false;
                }
            }

        },
        Mul: function (arg1, arg2) {
            var r1 = arg1.toString(), r2 = arg2.toString(), m, resultVal, d = arguments[2];
            m = (r1.split(".")[1] ? r1.split(".")[1].length : 0) + (r2.split(".")[1] ? r2.split(".")[1].length : 0);
            resultVal = Number(r1.replace(".", "")) * Number(r2.replace(".", "")) / Math.pow(10, m);
            if (d) {
                return this.fDec(resultVal, d);
            } else {
                return Number(resultVal)
            }
        },
        //是否使用加成方式调价
        getXlj: function (value) {
            this.popContent.ypjj = parseFloat(value)
            if (this.popContent.jcfs == "0") {
                //按加成比例四舍五入获取零价
                if (this.popContent.yplx == '2') {
                    if (this.popContent.kfdwmc == 'KG') {
                        var temp = this.Mul(this.popContent.jcbl, this.popContent.ypjj, 4)
                        if ((temp + "").indexOf(".") != -1) {
                            temp = temp + "";
                            temp = temp.substr(0, temp.indexOf(".") + 2)
                        }
                        this.popContent.yplj = temp
                    } else {
                        this.popContent.yplj = this.Mul(this.popContent.jcbl, this.popContent.ypjj)
                    }
                } else {

                    this.popContent.yplj = this.Mul(this.popContent.jcbl, this.popContent.ypjj)
                }

            } else {
                //按区间加成
                for (var i = 0; i < this.jcqjList.length; i++) {
                    var jcqj = this.jcqjList[i];
                    if (jcqj.jsz) {
                        if (this.popContent.ypjj >= jcqj.qsz && this.popContent.ypjj < jcqj.jsz) {
                            var jcz = 0;
                            if (jcqj.jclx == '1') { //固定值加成
                                jcz = jcqj.jcz;
                            } else {//比例值加成
                                jcz = this.popContent.ypjj * (jcqj.jcz - 1.0);
                                this.popContent.jcbl = jcqj.jcz;
                            }
                            if (jcqj.zgxj == null || jcqj.zgxj == 0) {
                                //不限价
                                this.popContent.yplj = this.popContent.ypjj + jcz;
                            } else {
                                this.popContent.yplj = this.popContent.ypjj + (jcqj.zgxj > jcz ? jcz : jcqj.zgxj);
                            }
                            this.popContent.yplj = this.fDec(this.popContent.yplj, 4);
                            break;
                        }
                    } else if (this.popContent.ypjj >= jcqj.qsz) {
                        var jcz = 0;
                        if (jcqj.jclx == '1') { //固定值加成
                            jcz = this.popContent.ypjj + jcqj.jcz;
                        } else {//比例值加成
                            jcz = this.popContent.ypjj * (jcqj.jcz - 1.0);
                            this.popContent.jcbl = jcqj.jcz;
                        }
                        if (jcqj.zgxj == null || jcqj.zgxj == 0) {
                            //不限价
                            this.popContent.yplj = this.popContent.ypjj + jcz;
                        } else {
                            this.popContent.yplj = this.popContent.ypjj + (jcqj.zgxj > jcz ? jcz : jcqj.zgxj);
                        }
                        this.popContent.yplj = this.fDec(this.popContent.yplj, 4);
                        break;
                    }
                }
            }
            //监听到进价更改，更新总价&差额
            this.watchChange();

        },
        //当输入值后才触发
        change1: function (add, val) {
            // //库房非空判断
            if (!add) this.queryStr.page = 1;       // 设置当前页号为第一页
            this.queryStr.parm = val;
            this.popContent.cdbm = '';
            var _searchEvent = $(event.target.nextElementSibling)[0];
            //初始化页面加载产地编码
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=query&dg=" + JSON.stringify(this.queryStr), function (data) {
                if (data.a == 0) {
                    if (add) {
                        wap.searchCon1 = wap.searchCon1.concat(data.d.list)
                    } else {
                        wap.searchCon1 = data.d.list;
                    }
                    //药品产地
                    wap.queryStr.total = data.d.total;
                    wap.selSearch1 = 0;
                    if (data.d.list.length != 0 && !add) {
                        $(".selectGroup").hide();
                        $(_searchEvent).show()
                    }
                }

            });
        },
        setYplj: function (value) {
            if (this.popContent.jgfs == '2' && value >= this.popContent.yplj1) {
                this.popContent.yplj = this.popContent.yplj1;
                return malert("当前药品为采购限价，不允许修改大于当前价格", 'top', 'defeadted');
            }
        },
        //当输入值后才触发
        change: function (add, val) {
            this.popContent['ypmc'] = val
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            if (!wrapper.rkd["kfbm"]) {
                malert("库房不能为空", 'top', 'defeadted');
                return;
            }
            if (!wrapper.rkd["cgry"]) {
                malert("采购人员不能为空", 'top', 'defeadted');
                return;
            }
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var str = {
                kfbm: wrapper.rkd.kfbm
            }
            var dg = JSON.parse(JSON.stringify(this.page));
            dg.parm = encodeURIComponent(val);
            //分页参数
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd' + '&dg=' + JSON.stringify(dg) + '&json=' + JSON.stringify(str), function (data) {
                if (data.a == 0) {
                    if (add) {
                        wap.searchCon = wap.searchCon.concat(data.d.list)
                    } else {
                        wap.searchCon = data.d.list;
                    }
                    //药品产地
                    Vue.set(wap.page, 'total', data.d.total)
                    wap.selSearch = 0;
                    if (data.d.list.length != 0 && !add) {
                        $(".selectGroup").hide();
                        $(_searchEvent).show()
                    }
                }
            });
        },

        //药品名称下拉table检索数据
        changeDown: function (event, value, type) {
            if (!this.searchCon[this.selSearch]) return
            this.inputUpDown(event, this.searchCon, "selSearch");
            //选中之后的回调操作
            if (event.keyCode == 13) {
                this.popContent = this.searchCon[this.selSearch]
                this.popContent.wgzl = '0'
                this.popContent.ysjl = '0'
                this.popContent.ghdw = wrapper.rkd.ghdw;
                this.popContent.ghdwmc = wrapper.rkd.ghdwmc;
                this.popContent.yplj1 = this.popContent.yplj;
                this.popContent.zbfsbm = this.zbfsList[0].zbfsbm
                this.popContent.cgrkfs = wrapper.rkd.cgrkfs
                if (type == "text") {
                    this.popContent['text'] = this.popContent['ypmc'];
                    if (this.popContent.jcfs == '1') {
                        //区间加成
                        this.queryYpzlJcList();
                    } else {
                        this.csqxjjlj(); //调用公用方法生成进价和零价
                    }
                    //保留两位小数
                    //this.popContent['ypjj'] = this.fDec(this.popContent['ypjj'], 2);
                    //   this.popContent['yplj'] = this.fDec(this.popContent['yplj'], 2);
                    $("#rksl").focus();
                } else if (type == "bzsm") {
                    wap.addData();
                }
                //跳转下一个输入框
                this.selSearch = -1
                this.nextFocus(event);
                $(".selectGroup").hide();
            }
        },
        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                this.dg.page++;
                this.change1(true, this.popContent['ypmc'])
                return false;
            }
            item.cgrkfs = this.popContent.cgrkfs;//记录下采购入库方式
            this.popContent = item;
            this.popContent.ghdw = wrapper.rkd.ghdw;
            this.popContent.ghdwmc = wrapper.rkd.ghdwmc;
            this.popContent.yplj1 = item.yplj;
            this.popContent.wgzl = '0'
            this.popContent.ysjl = '0'
            this.popContent.zbfsbm = this.zbfsList[0].zbfsbm
            this.popContent['text'] = this.popContent['ypmc'];
            $(".selectGroup").hide();
            $("#rksl").focus();
            if (this.popContent.jcfs == '1') {
                //区间加成
                this.queryYpzlJcList();
            } else {
                this.csqxjjlj();
            }
            //保留两位小数
        },
        //查询种类对应的加成
        queryYpzlJcList: function () {
            var qjParm = {ypzlbm: this.popContent.zlbm};
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=queryYpzlJc&json=" + JSON.stringify(qjParm), function (json) {
                if (json.a == '0') {
                    wap.jcqjList = json.d;
                    wap.csqxjjlj();
                } else {
                    malert(json.c, 'top', 'defeadted')
                }
            });
        },

        //双击选中下拉table
        selectOne1: function (item) {
            //查询下页
            if (item == null) {
                this.queryStr.page++;
                this.change1(true, this.popContent['cdmc'])
                return;
            }
            this.popContent['cdbm'] = item['cdbm'];
            this.popContent['cdmc'] = item['cdmc'];
            $(".selectGroup").hide();
        },
        //药品名称下拉table检索数据
        changeDown1: function (event) {
            // if (!this.searchCon1[this.selSearch1]) return
            this.popContent1 = this.searchCon1[this.selSearch1]
            this.inputUpDown(event, this.searchCon1, "selSearch1");
            //选中之后的回调操作
            if (event.code == 'Enter' || event.keyCode == 13 || event.keyCode == 'NumpadEnter') {
                if (this.popContent['cdbm']) {
                    this.selSearch1 = -1;
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                    this.addData();
                    return false;
                }
                this.popContent['cdmc'] = this.popContent1['cdmc'];
                this.popContent['cdbm'] = this.popContent1['cdbm'];
                //保留两位小数
                this.selSearch1 = -1;
                this.nextFocus(event);
                $(".selectGroup").hide();
                this.addData();
            }
        },
        //关闭
        closes: function () {
            this.index = 0
        },
        open: function () {
            this.popContent = {
                ghdw: wrapper.rkd.ghdw,
                ghdwmc: wrapper.rkd.ghdwmc,
                shdjh: wrapper.rkd.shdjh,
                cgrkfs: wrapper.rkd.cgrkfs,
            };
            this.index = 1;
            this.$nextTick(function () {
                this.$refs.ypmc.focus();
            });
        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            if (!qxksbm) {
                return;
            }
            var parm = {
                "ylbm": 'N040030012011',
                "ksbm": qxksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N04003001201101": //入库开单打印单据1-开单保存后打印单据，0-开单保存后不打印单据
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100101 = csjson.csz;
                                }
                                break;
                            case "N04003001201102": //入库修改进价1-允许，0-不允许
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100102 = csjson.csz;
                                }
                                break;
                            case "N04003001201103": //入库修改零价1-允许，0-不允许
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100103 = csjson.csz;
                                }
                                break;
                            case "N04003001201104": //药品加成方式1-进价生成零价，0-不加成
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100104 = csjson.csz;
                                }
                                break;
                            case "N04003001201106": //药库入库数量是否允许为小数1、允许；0、不允许
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100106 = csjson.csz;
                                }
                                break;
                            case "N04003001201107": //入库药品价格是否取字典里的价格1、是,0、否
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100107 = csjson.csz;
                                }
                                break;
                            case "N04003001201108": //基本药物是否随种类进行加成1、是,0、否
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100108 = csjson.csz;
                                }
                                break;
                            case "N04003001201109": //入库开单审核权限0-有开单审核  1-有开单 2-有审核
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100109 = csjson.csz;
                                }
                                break;
                            case "N04003001201110": //入库单作废权限1、是,0、否
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wap.csqxContent.cs00200100110 = csjson.csz;
                                }
                                break;
                            case "N04003001201111": //是否显示发票号
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wrapper.csqxContent.N04003001201111 = csjson.csz;
                                }
                                break;
                            case "N04003001201112": //采购入库默认操作员
                                if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                    wrapper.csqxContent.N04003001201112 = csjson.csz;
                                }
                                break;
                        }
                    }
                } else {
                    malert("参数权限获取失败" + json.c, 'top', 'defeadted');
                }
            });
        },
        //
        getJzData: function () {
            //初始化页面记载供货单位
            var parm = {
                page: 1,
                rows: 1000,
                tybz: '0',
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        wap.ghdwList = json.d.list;
                        wrapper.ghdwList = json.d.list;
                    } else {
                        malert("供货单位获取失败", 'top', 'defeadted');
                    }
                });

            //初始化页面加载招标方式
            //                this.dg.rows = 200;

            var parm2 = {
                page: 1,
                rows: 1000,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhZbfs&types=query&dg=" + JSON.stringify(parm2),
                function (data) {
                    if (data.a == 0) {
                        wap.zbfsList = data.d.list;
                    } else {
                        malert("招标方式获取失败!", 'top', 'defeadted');
                    }

                });
        },
        //参数权限判断进价和零价
        csqxjjlj: function () {
            //入库药品价格是否取字典里的价格1、是,0、否
            if (wap.csqxContent.cs00200100107 == '0') {
                this.popContent['ypjj'] = null;
                this.popContent['yplj'] = null;
            }
            //药品加成方式1-进价生成零价，0-不加成
            if (wap.csqxContent.cs00200100104 == '1') {
                this.getXlj(this.popContent['ypjj']);
                // this.popContent['yplj'] = (this.popContent['jcbl'] || 1) * this.popContent['ypjj'];
            } else {
                // this.fDec(this.popContent['yplj'], 4)
            }
        },

        //招标方式改变
        resultZbfsChange: function (val) {
            Vue.set(wap.popContent, 'zbfsbm', val[0]);
            this.addData();
        },
        //添加入库信息
        addData: function () {
            //非空判断
            var isNotnull = $('input[data-notEmpty=true],select[data-notEmpty=true]');
            for (var i = 0; i < isNotnull.length; i++) {
                if ($(isNotnull[i]).val() == '' || $(isNotnull[i]).val() == null) {
                    $(isNotnull[i]).addClass("emptyError");
                    malert('数据输入不完整！', 'top', 'defeadted');
                    return false;
                }

            }
            if (wrapper.rkd.rkfs == '01' || this.popContent.cgrkfs == '01') {
                if (!this.popContent['rksl']) {
                    malert("入库数量不能为空", 'top', 'defeadted');
                    return false;
                }
                if (!this.popContent['scph']) {
                    malert("生产批号不能为空", 'top', 'defeadted');
                    return false;
                }
                if (this.popContent.yplx == '0' && !this.popContent['yxqz']) {
                    malert("有效期限不能为空", 'top', 'defeadted');
                    return false;
                }
                //判断格式
                if (!this.popContent['scrq']) {
                    malert("生产日期格式不正确", 'top', 'defeadted');
                    return false;
                }
            } else {
                if (!wrapper.rkd["kfbm"]) {
                    malert("库房不能为空", 'top', 'defeadted');
                    return false;
                }
                if (!wrapper.rkd["cgry"]) {
                    malert("采购人员不能为空", 'top', 'defeadted');
                    return false;
                }
                if (!this.popContent["cdbm"]) {
                    malert("产地不能为空", 'top', 'defeadted');
                    return false;
                }

                if (!this.popContent['wgzl']) {
                    malert("外观质量不能为空", 'top', 'defeadted');
                    return false;
                }
                if (!this.popContent['ysjl']) {
                    malert("验收结论不能为空", 'top', 'defeadted');
                    return false;
                }
                if (this.popContent['ysjl'] == 1) {
                    malert("验收不合格不能入库", 'top', 'defeadted');
                    return false;
                }
                //根据参数权限判断是都允许为小数
                var xs = /^(\d|[1-9]\d+)(\.\d+)?$/; //大于0的整数小数
                var zzs = /^[1-9]*[1-9][0-9]*$/; //正整数的表达式
                // var num=  /(^[1-9]([0-9]+)?(\.[0-9]{1,4})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;//有4位数的数字
                if (wap.csqxContent.cs00200100106 == '0') { //不允许
                    if (!zzs.test(this.popContent['rksl'])) {
                        malert("入库数量为正整数", 'top', 'defeadted');
                        return false;
                    }
                } else { //允许
                    if (!this.popContent['rksl']) {
                        malert("入库数量为大于0的整数", 'top', 'defeadted');
                        return false;
                    }
                }
                if (!xs.test(this.popContent['ypjj']) || this.popContent['ypjj'] <= 0) {
                    malert("药品进价为大于0的数", 'top', 'defeadted');
                    return false;
                }
                if (!xs.test(this.popContent['yplj']) || this.popContent['yplj'] <= 0) {
                    malert("药品零价为大于0的数", 'top', 'defeadted');
                    return false;
                }
                if (!this.popContent['rksl']) {
                    malert("入库数量不能为空", 'top', 'defeadted');
                    return false;
                }
                if (!this.popContent['scph']) {
                    malert("生产批号不能为空", 'top', 'defeadted');
                    return false;
                }

                if (!this.popContent['yxqz']) {
                    this.popContent['yxqz'] = this.setYearFun();
                }

                if (!this.popContent['ghdw']) {
                    malert("供货单位不能为空", 'top', 'defeadted');
                    return false;
                }
                if (this.popContent.yplx == '0' && !this.popContent['yxqz']) {
                    malert("有效期限不能为空", 'top', 'defeadted');
                    return false;
                }
                //判断格式
                if (!this.popContent['scrq']) {
                    malert("生产日期格式不正确", 'top', 'defeadted');
                    return false;
                }
            }

            //招标方式wrapper.rkd.fphm
            Vue.set(this.popContent, 'zbfs', this.popContent.zbfsbm);
            // Vue.set(this.popContent, 'fphm', wrapper.rkd.fphm);
            // Vue.set(this.popContent, 'qyfphm', wrapper.rkd.qyfphm);
            //相同药品不能重复入库
            var scph = wap.popContent.scph;
            var ypbm = wap.popContent.ypbm;
            var mxxh = wap.popContent.mxxh;
            if (wrapper.isUpdate == 0) {
                //添加
                for (var i = 0; i < wrapper.jsonList.length; i++) {
                    if (wrapper.jsonList[i].ypbm == ypbm && wrapper.jsonList[i].scph == scph) {
                        malert("药品【" + wrapper.jsonList[i].ypmc + "】生产批号【" + this.popContent.scph + "】已存在,请修改已有数据!", 'top', 'defeadted');
                        return;
                    }
                }
                // this.popContent.yxqz = new Date(this.popContent.yxqz).getTime()
                this.popContent.mxxh = wrapper.jsonList.length + 1;
                wrapper.jsonList.push(this.popContent);
                malert("添加成功！", 'top', 'success');

            } else {
                //编辑
                // this.popContent.yxqz = new Date(this.popContent.yxqz).getTime()
                //修改
                this.popContent.mxxh = wrapper.mxxh;//序号和修改前保持
                wrapper.jsonList[this.num] = JSON.parse(JSON.stringify(this.popContent));
                malert("修改成功！", 'top', 'success');
                //关闭侧窗
                wap.closes();
            }
            wrapper.$forceUpdate();
            this.popContent = {ghdw: wrapper.rkd['ghdw'], cgrkfs: wrapper.rkd.cgrkfs};
            $("#ypmc").focus();
        },
        watchChange: function () {
            var yplj = this.popContent.yplj;
            var ypjj = this.popContent.ypjj;
            if (typeof (yplj) != 'undefined') {
                this.popContent.total_yplj = (yplj * this.popContent.rksl).toFixed(2);
            }
            if (typeof (yplj) != 'undefined') {
                this.popContent.total_ypjj = (ypjj * this.popContent.rksl).toFixed(2);
            }
            if (typeof (yplj) != 'undefined' && typeof (yplj) != 'undefined') {
                this.popContent.total_diff = (this.popContent.total_yplj - this.popContent.total_ypjj).toFixed(2);
            }
        },
        watchTimeFormat: function (event, el) {
            let new_v = '';
            var value = event.currentTarget.value;
            if (value == '' && typeof (value) == "undefined") {
                return new_v;
            }
            if (value.length == 8) {
                new_v = value.substring(0, 4) + '-' + value.substring(4, 6) + '-' + value.substring(6);
            }
            let date = new Date(new_v);
            if (date == 'Invalid Date') {
                malert("【" + new_v + "】为无效时间，请检查！", 'top', 'defeadted');
                $(event.currentTarget).focus();
                return;
            }
            this.popContent[el] = new_v;
            event.currentTarget.value = new_v;
        },
        watchFocus: function (event) {
            var el = event.currentTarget;
            $(el).trigger("click");
        }
    },
    watch: {
        'popContent.scrq': function (n, o) {
            if (n == null || n == '') {
                return false;
            }
            var sdate = new Date(n);
            Y = sdate.getFullYear(),
                m = sdate.getMonth() + 1,
                d = sdate.getDate()
            if (m < 10) {
                m = '0' + m;
            }
            if (d < 10) {
                d = '0' + d;
            }
            // if (!this.popContent.yxqz) {
            //     return this.popContent.yxqz = Y + 2 + '-' + m + '-' + d;
            // }
        },
        'popContent.rksl': function (n, o) {
            this.watchChange();
        }
    }
});

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

});





