var nhBx={
    data:{
        bxurl:'',
        billCode:'',
        rztg:null,
        outpId:"",
        json:{
            billCode:'',
            rztg:null,
            chh:'',
            bxlbbm:''
        },
    },
    methods:{
        //获取保险类别
        getbxlb: function () {
            var self=this
            var result = "0";//0-成功，1-错误直接退回
            var param = {
                bxjk: "001"
            };
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                    if (json.a == 0 && json.d) {
                            self.bxurl = json.d.list[0].url;
                            self.getS02();
                    } else {
                        result = "1";
                        malert("保险类别查询失败!" + json.c, 'top', 'defeadted')
                    }
                });
            return result;
        },
        //认证鉴权
        getS02: function () {
            var self=this
            var result = "0";
            var head = {
                operCode: "S02",
                rsa: ""
            };
            var body = {
                userName: "",
                passWord: ""
            };
            var param = {
                head: head,
                body: body
            };
            var str_param = JSON.stringify(param);
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.json.bxlbbm + "&types=S&parm=" + str_param,
                function (json) {
                    if (json.a == 0) {
                        self.billCode = json.d;
                        self.rztg = true;
                    } else {
                        result = "1";
                        malert("认证鉴权失败，请从新操作", 'top', 'defeadted');
                        self.rztg = false;
                    }
                });
            return result;
        },
        //冲红
        nhjs26:function () {
            var self=this
            var result = "0";
            var head = {
                operCode: "S26",
                billCode: this.billCode,
                rsa: ""
            };
            var body = {
                outpId:contextInfo.json.bxjsh,
            };
            var param = {
                head: head,
                body: body,
            };
            var str_param = JSON.stringify(param);
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.json.bxlbbm + "&types=S&parm=" + str_param,
                function (json) {
                    var obj = {};
                    if(typeof(json)=='string'){
                        obj = JSON.parse(json);
                    }else {
                        obj = json;
                    };

                    if(obj.a==0){
                        malert("农合预结算成功!");
                    }else {
                        result =  "1";
                        malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!");
                    }
                },function (error) {
                    result =  "1";
                    console.log(error);
                });
            return result;
        },
    },
};
