<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>管理小组</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script src="/newzui/pub/js/highcharts.js"></script>
    <script src="/newzui/pub/js/echarts.min.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link type="text/css" href="ycgl.css" rel="stylesheet"/>
</head>

<body class="skin-default flex-container flex-dir-c flex-one">
<div class="ycgl-tj flex-container flex-dir-c flex-one">
    <div class="ycgl-title">压疮统计分析</div>
    <div class="ycgl-time">
        <div class="top-form">
            <label class="top-label">时间段</label>
            <div class="top-zinle">
                <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                <input type="text" class="zui-input wh122 times text-indent-20" id="timeVal"/>
            </div>
            <div class="top-zinle padd-r-5 padd-l-5">
                至
            </div>
            <div class="top-zinle">
                <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                <input type="text" class="zui-input wh122 times1 text-indent-20" id="timeVal1"/>
            </div>
        </div>
    </div>
   <div class="ycgl-echart">
       <div class="ycgl-left">
           <div class="ycgl-canvas">
               <div class="canvas" id="container" style="width:400px;height:280px;"></div>
           </div>
           <div class="ycgl-left-title font-weight">压疮风险评估</div>
           <div class="ycgl-left-title color-c7 padd-t-10 font14">压疮评估总<i class="color-c1 font30">123</i><small class="color-c1">例</small>&ensp;&ensp;压疮发生比例<i class="font30 color-cff5">80</i><small class="color-cff5">%</small></div>
       </div>
       <div class="ycgl-right">
           <div id="main" style="width:100%; min-height:350px;">area</div>
       </div>
   </div>
</div>
<div class="ycgl-sb flex-container flex-dir-c flex-one margin-t-10">
    <div class="ycgl-title">压疮不良事件上报处理<span class="color-c1 fr font12" @click="morePage">更多>></span></div>
    <div class="ycgl-list flex-container flex-dir-c">
        <div class="zui-table-view flex-container flex-dir-c over-auto">
            <div class="zui-table-header" style="overflow: initial">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>患者姓名</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>类型</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>年龄</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>性别</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>住院号</span></div></th>
                        <th><div class="zui-table-cell cell-xxl text-left"><span>诊断</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>入院时间</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>评估次数</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>评估时间</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>Braden评分</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!-- data-no-change -->
            <div class="zui-table-body over-auto"  @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in 20"
                        :tabindex="$index"
                        @dblclick="edit(item)"
                        ref="list"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','6'],$event)">
                        <!-- 有复选框的时候传 some  没有复选框的时候传one  -->
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s">
                            <i class="text-line color-dsh">患者姓名</i>
                        </div></td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="'发生压疮' ? 'color-9e':'' ">
                                发生压疮<!--发生压疮：颜色状态值color-9e，高度风险：color-cff5，中度风险 color-c04，低度风险color-c7：-->
                            </div>
                        </td>
                        <td><div class="zui-table-cell cell-m">年龄</div></td>
                        <td><div class="zui-table-cell cell-m">性别</div></td>
                        <td><div class="zui-table-cell cell-s">住院号</div></td>
                        <td><div class="zui-table-cell cell-xxl text-left">诊断诊断诊断诊断诊断诊断诊断诊断诊断</div></td>
                        <td><div class="zui-table-cell cell-s">入院时间</div></td>
                        <td><div class="zui-table-cell cell-s">评估次数</div></td>
                        <td><div class="zui-table-cell cell-s">评估时间</div></td>
                        <td><div class="zui-table-cell cell-s">Braden评分</div></td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" :class="'已审核' ? 'color-c04':'' ">
                                已审核<!--未接收：颜色状态值color-cff5，已审核：color-c04-->
                            </div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">
                                         <span class="flex-center padd-t-2">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                        <em class="width30"><i class="iconfont icon-iocn45 icon-font20 icon-hover" data-title="未审核" @click="Unaudited"></i></em>
                                        <em class="width30"><i class="iconfont  icon-iocn12 icon-font20 icon-hover" data-title="已审核" @click="examine"></i></em>
                                         </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l background-f">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->

                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <tr v-for="(item, $index) in 20"
                            :tabindex="$index"
                            @dblclick="edit(item)"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','6'],$event)">
                            <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r background-f">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table zui-collapse">
                        <tbody>
                        <tr v-for="(item, $index) in 20"
                            :tabindex="$index"
                            @dblclick="edit(item)"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','6'],$event)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" :class="'未审核' ? 'color-cff5':'' ">
                                    未审核<!--未接收：颜色状态值color-cff5，已审核：color-c04-->
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" >
                                            <span class="flex-center padd-t-2">
                                    <!--ui状态根据当前状态做对应的icon显示:例如:<em class="width30" v-if="item.zt==1"><i></i></em>-->
                                        <em class="width30"><i class="iconfont icon-iocn45 icon-font20 icon-hover" data-title="未审核" @click="Unaudited"></i></em>
                                        <em class="width30"><i class="iconfont iconfont icon-iocn12 icon-font20 icon-hover" data-title="已审核" @click="examine"></i></em>
                                         </span>
                                </div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


    </div>
</div>

</body>
<script src="ycgl.js"></script>

</html>
