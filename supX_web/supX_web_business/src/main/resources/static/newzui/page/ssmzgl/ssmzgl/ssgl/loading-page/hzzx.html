<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>患者中心</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="./hzxx.css" rel="stylesheet">
</head>

<body class="body padd-b-10 padd-l-10 padd-t-10 padd-r-10 height">
    <div class="header-item flex-container flex-dir-c">
        <header class="userNameBg printHide" v-cloak>
            <div class="flex">
                <div class="text-color">
                    <p class="userHeader userCwh padd-l-30">
                        <span class="userName">{{Brxx_List.brxm}}</span>
                        <span class="sex text">{{brxb_tran[Brxx_List.brxb]}}</span>
                        <span class="nl text">{{Brxx_List.nl}}{{nldw_tran[Brxx_List.nldw]}}</span>

                        <span class="cwh text">床位号：{{Brxx_List.rycwbh}}号</span>
                        <span class="zyh text">住院号：{{Brxx_List.zyh}}</span>
                        <span class="bq text">费用：{{Brxx_List.total}}元</span>
                        <span class="ks text">住院医生：{{Brxx_List.zyysxm}}</span>
                        <span class="ks text">入院日期：{{Brxx_List.ryrq}}</span>
                        <span class="ks text">身份证号码：{{Brxx_List.sfzjhm}}</span>
                    </p>
                </div>
            </div>
        </header>
        <div class="content" >
            <div class="fyxm-tab printHide" v-if="ssyz!=1">
                <div><span :class="{'active':num==1}" onclick="tabBg('yzgl',1,this)">医嘱管理</span></div>
                <div><span :class="{'active':num==2}" onclick="tabBg('jcjy',2,this)">检查检验</span></div>
<!--                <div><span :class="{'active':num==3}" onclick="tabBg('dzbl',3,this)">电子病历</span></div>-->
<!--                <div><span :class="{'active':num==6}" onclick="tabBg('fyqd',6,this)">费用清单</span></div>-->
                <div><span :class="{'active':num==7}" onclick="tabBg('twd',7,this)">体温单</span></div>
<!--                <div><span :class="{'active':num==8}" onclick="tabBg('sssq',8,this)">手术申请</span></div>-->
            </div>
            <div class="loadPage wrapper col-x-12 " style="border-top: none">
            </div>
        </div>

    </div>
    <script type="text/javascript" src="hzxx.js"></script>
</body>

</html>
