(function () {
    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.background',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:''
        },
        methods:{

            //addJg
            addJg:function () {
                wapse.isFold = true;
                wapse.sideTitle='新增体检单位';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
            }
        }
    });

    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                this.isShowpopL=false;
                this.isShow=false;
                malert('删除成功','top','success');
            }

        }
    });
    var waps=new Vue({
        el:'.zui-table-body',
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isFold:false,
            title:'',
            sideTitle:'',
            centent:'',
        },
        methods:{
            DelLine:function () {
                pop.isShow=true;
                pop.isShowpopL=true;
                pop.title='系统提示';
                pop.centent='确定删除该项内容吗？'
            },
            //双击编辑
            dbEdit:function () {
                wapse.isFold = true;
                wapse.sideTitle='编辑体检单位';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');

            }
        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            title:'',
            sideTitle:'',
            centent:'',
            isFold: false,
        },
        methods:{
            // 取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // 确定
            confirms:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('222','top','success');
            }

        }
    });
})()