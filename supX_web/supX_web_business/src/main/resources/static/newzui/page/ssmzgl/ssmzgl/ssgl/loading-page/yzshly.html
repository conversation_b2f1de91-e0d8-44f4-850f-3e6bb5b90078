<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>医嘱审核领药</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css" />
    <link href="yzshly.css" rel="stylesheet">
</head>
<body class="background-f padd-b-10 padd-l-10 padd-t-10 padd-r-10 skin-default">
<div v-cloak class="padd-l-10 margin-r-10 yzclRight padd-t-10 flex-one flex-dir-c flex-container">
        <div class="flex-container printHide flex-align-c" style="min-height: 36px;">
            <div class="fyxm-tab flex-container flex-align-c" style="width:auto;margin-right:20px;">
                <div v-for="(text,index) in tabArr">
                    <span :class="{'active':num==index}" :tabindex="index" onclick="tabBg(this)" @click="tabBg(undefined,index,false),queryYzxx()">{{text}}</span>
                </div>
            </div>
            <div class="zui-inline flex-container flex-align-c" v-show="num==1||num==2||num==4">
                <label class="zui-form-label">{{tabArr[num]}}时间</label>
                <div class="zui-input-inline wh240">
                    <input id="timeTabArr" class="zui-input" style="text-indent: 0;" type="text" />
                </div>
            </div>
        </div>
    <div v-if="num!=4 && num!=5" id="wrapper" class="printHide  flex-dir-c flex-container">
        <div  @scroll="scrollFn(true)" class="wh100 position  " :class="{'over-auto padd-b-68':rightJsonList.length>=2}">
            <div :style="{'height': brItemListSumHeight + 'px'}">
                <div :style="{'top': brPosition[brListIndex] + 'px'}" style="position: absolute;left:0;"
                     v-for="(brListItem,brListIndex) in rightJsonList">
                    <div v-if="(brListWinSize.top < brPosition[brListIndex] && brPosition[brListIndex] < brListWinSize.bottom) || (brListWinSize.top < brPosition[brListIndex + 1] && brPosition[brListIndex] < brListWinSize.bottom)">
                        <div class="jbxx-size margin-t-5">
                            <div class="jbxx-box padd-l-12">
                                科别：<span class="font-14-654 padd-r-18" v-text="brListItem.ryksmc"></span>
                                床号：<span class="font-14-654 padd-r-18" v-text="brListItem.rycwbh"></span>
                                姓名：<span class="font-14-654 padd-r-18" v-text="brListItem.brxm"></span>
                                年龄：<span class="font-14-654 padd-r-18" v-text="brxb_tran[brListItem.brxb]"></span>
                                住院号：<span class="font-14-654 padd-r-18" v-text="brListItem.brzyh==null?brListItem.zyh:brListItem.brzyh"></span>
                                <span v-if="num == 3 || num == 4">

                                            </span>
                                药品费用合计：
                                <span class="font-14-654 padd-r-18">
                                                {{fDec(brListItem.ypfyhj || 0,2) }}元
                                            </span>
                            </div>
                        </div>
                        <div class="zui-table-view   flex-dir-c flex-container">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                    <tr>
                                        <th class="cell-m" v-if="brListItem.yzxx.length>=1">
                                            <div class="zui-table-cell cell-m">
                                                <input class="green" type="checkbox" v-model="brListItem.isCheckAll">
                                                <label @click="reCheckBoxSon(brListIndex)"></label>
                                            </div>
                                        </th>

                                        <!-- 医嘱查询列表 -->
                                        <th class="cell-m" v-if="num==0">
                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-s"><span>医嘱类型</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-l"><span>下嘱时间</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-xxl text-left"><span>医嘱内容</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-s"><span>剂量单位</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-xl"><span>用量</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-s"><span>用量单位</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-l"><span>用法</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-l"><span>频次</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-l"><span>医生签名</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-l"><span>护士签名</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-l"><span>医生停嘱时间</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-l"><span>护士停嘱时间</span></div>
                                        </th>
                                        <th v-if="num==0">
                                            <div class="zui-table-cell cell-xl text-left "><span>说明</span></div>
                                        </th>

                                        <!-- 医嘱审核列表 -->
                                        <th class="cell-s" v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>序号</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>类型</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>分类</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>嘱托</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-xl text-left"><span>医嘱内容</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-xl"><span>药品规格</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>剂量单位</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>用量</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>用量单位</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-l"><span>用药方法</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-xl text-left"><span>说明</span></div>
                                        </th>

                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-l"><span>频次</span></div>
                                        </th>

                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                        </th>
                                        <th v-if="num==1">
                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                        </th>

                                        <!-- 执行医嘱列表 -->
                                        <th class="cell-s" v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>序号</span></div>
                                        </th>
                                        <th class="cell-s" v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>已执行</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-xl text-left"><span>医嘱内容</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-xl"><span>药品规格</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>剂量单位</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>用量</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>用量单位</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-l text-left"><span>用药方法</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-xl text-left"><span>说明</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                        </th>
                                        <th v-if="num==2">
                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                        </th>


                                        <!-- 药品申领列表 -->
                                        <th class="cell-m" v-if="num==3">
                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-s"><span>类型</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-xxl text-left"><span>医嘱内容</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-s"><span>用量</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-s"><span>单位</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-l"><span>用药方法</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-l"><span>医生签名</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-l"><span>护士签名</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-l"><span>执行到</span></div>
                                        </th>
                                        <th v-if="num==3">
                                            <div class="zui-table-cell cell-xxl text-left"><span>说明</span></div>
                                        </th>

                                        <!--取消申领药品（查询）  -->
                                        <th class="cell-m" v-if="num==4">
                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-m"><span>类型</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-xl text-left"><span>医嘱内容</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-m"><span>用量</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-m"><span>单位</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-s"><span>用药方法</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-s"><span>执行到</span></div>
                                        </th>
                                        <th v-if="num==4">
                                            <div class="zui-table-cell cell-xl text-left"><span>说明</span></div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body  over-auto" :data-no-change="rightJsonList.length>=2?'true':undefined"
                                 @scroll="scrollTable" style="width: 98% !important;">
                                <table class="zui-table table-width50">
                                    <tbody>
                                    <tr v-for="(yzListItem, $index) in brListItem.yzxx" :tabindex="$index"
                                        @click="reCheckBoxSon(brListIndex,$index)" :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index === hoverIndex&&brListIndex===hoverBrListIndex},{'stop': yzListItem.hstzbz == '1' && yzListItem.ystzbz == '1'},{'yzxyz':yzListItem.numb>0&&num==2}]"
                                        @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                        @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')">
                                        <td class="cell-m" v-if="brListItem.yzxx.length>=1">
                                            <div class="zui-table-cell cell-m">
                                                <input class="green" type="checkbox" v-model="yzListItem.isChecked">
                                                <label @click="checkSelectSon(brListIndex,$index)"></label>
                                            </div>
                                        </td>
                                        <!-- 医嘱查询列表 -->
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-s" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                        </td>
                                        <td v-if="num==0" :class="[ yzListItem.tzbj ]">
                                            <div class="zui-table-cell cell-xxl text-over-2 text-left "
                                                 v-text="yzListItem.xmmc"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-xl" v-text="yzListItem.sl"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.yyffmc"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.pcmc"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.ysqmxm"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.zxhsxm"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.ystzsj,'shortY')"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.hstzsj,'shortY')"></div>
                                        </td>
                                        <td v-if="num==0">
                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yssm"></div>
                                        </td>

                                        <!--医嘱审核列表  -->
                                        <td class="cell-s" v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="$index+1"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="yzfl_tran[yzListItem.yzfl]"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="istrue_tran[yzListItem.tsyz]"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                        </td>
                                        <td v-if="num==1" :class="[ yzListItem.tzbj ]">
                                            <div class="zui-table-cell cell-xl text-over-2 text-left "
                                                 v-text="yzListItem.xmmc"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-xl" v-text="yzListItem.ypgg"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sl"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.yyffmc"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yysm"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.pcmc"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysqmxm"></div>
                                        </td>
                                        <td v-if="num==1">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                        </td>

                                        <!-- 执行医嘱列表 -->
                                        <td class="cell-s" v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="$index+1"></div>
                                        </td>
                                        <td class="cell-s" v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.numb"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-xl text-over-2 text-left "
                                                 v-text="yzListItem.ryypmc"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-xl" v-text="yzListItem.ypgg"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sl"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-l text-left " v-text="yzListItem.yyffmc"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yysm"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysqmxm"></div>
                                        </td>
                                        <td v-if="num==2">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                        </td>

                                        <!-- 申领药品列表 -->
                                        <td class="cell-m" v-if="num==3">
                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-s" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-xxl text-over-2 text-left "
                                                 v-text="yzListItem.ryypmc"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sl"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.yyffmc"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.ysxm"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-l" v-text="yzListItem.zxhsxm"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.zxEnd,'datetime')"></div>
                                        </td>
                                        <td v-if="num==3">
                                            <div class="zui-table-cell cell-xxl text-left " v-text="yzListItem.yssm"></div>
                                        </td>

                                        <!--取消申领（查询）  -->
                                        <td class="cell-m" v-if="num==4">
                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-m" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.ryypmc"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-m" v-text="yzListItem.sl"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-m" v-text="yzListItem.yfdwmc"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yyffmc"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysxm"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxEnd,'datetime')"></div>
                                        </td>
                                        <td v-if="num==4">
                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yssm"></div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <p v-if="brListItem.yzxx.length==0" class="  noData text-center zan-border">暂无数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zui-table-tool padd-r-10 flex-align-c zui-border-bottom flex-container font-14-654">
                            <span class="flex-one">
                                <input class="green" type="checkbox" v-model="isOver">
                                <label @click.stop="isOverClick(!isOver)">&ensp;全选</label>
                            </span>
                <button class="root-btn  btn-parmary-f2a" v-if="num==2" @click="auditing(2)">取消审核</button>
                <!--<button class="root-btn  btn-parmary-f2a" v-if="num==2" @click="auditing(4)">取消执行</button>-->
                <button class="root-btn  btn-parmary-f2a" v-if="num==1" @click="auditing(1)">审核医嘱</button>
                <button class="root-btn  btn-parmary-f2a" v-if="num==3" @click="auditing(6)">取消申领</button>
                <button class="root-btn btn-parmary" v-if="num==3" @click="printYpsld">药品申领单打印</button>
                <button class="root-btn btn-parmary" v-if="num==2" @click="auditing(5)">药品申领</button>
                <button class="root-btn btn-parmary" v-if="num==5" @click="auditing(7)">停嘱审核</button>
            </div>
        </div>
    </div>
    <!-- 去掉height-100的原因是因为有这个class的时候会引起医嘱管理版面的医嘱被遮挡一部分 -->
    <!-- <div id="loadingPage001" class="loadingPage height-100 flex-container flex-dir-c"> -->
    <div id="loadingPage001" class="loadingPage  flex-one  flex-container flex-dir-c"></div>

</div>
<script type="text/javascript" src="yzshly.js"></script>
</body>
</html>
