var payNo={
    data:{
        zflxList:[],
        resObj:{},
        codeContent:'',
        codeNum:0,
    },
    computed:{
        codeContentFun:function (){
            if(this.codeContent.length==18){
                this.payment()
            }else if(this.codeNum>3){
                this.inAjax = false;
                malert('你已经连续扫码错误三次，请重新确认', 'right', 'defeadted');
            }else if(this.codeNum<3){
                ++this.codeNum
            }
        },
    },
    methods:{
        saveSf:function (){
            if(this.isShouYJJ){
                var zflxjk=this.listGetName(this.zflxList, this.pageData.payType, 'zflxbm', 'zflxjk');
                if(zflxjk == '008'){
                    if(this.pageData.payType != '27'){
                        $('#codeContent').focus()
                        common.openloading("","请出示付款码。。。。");
                    }else {
                        this.payment()
                    }
                }else {
                    this.doSaveBrghFun()
                }
            }else {
                var zflxjk=this.listGetName(this.zflxList, this.pageData.jjlInfo.zflxbm, 'zflxbm', 'zflxjk');
                if(zflxjk){
                    this.tfPayment()
                }else {
                    this.doSaveBrghFun()
                }
            }
        },
        doSaveBrghFun:function (){
            this.pageData.brxxInfo=Object.assign(this.pageData.brxxInfo,this.resObj);
            this.finalSubm();
        },
        payment:function (){
            this.resObj={};
                var yylx=this.pageData.payType=='27'?'00':'02',resObj;
                var param={
                    czybm:userId,
                    czyxm:userName,
                    mzlx:this.mzlx,
                    hisGrbh:'',
                    bzsm:'门诊',
                    inJson: {
                        yylx:yylx,
                        fyje:String(this.pageData.brxxInfo.yjje),
                        // fyje:'0.01',
                        zfcm:this.codeContent,
                        // yjyrq:"",
                        // yjyckh:"",
                        // ysddh:""
                    },
                    yljgbm:jgbm
                }
            param.inJson=JSON.stringify(param.inJson);
                this.postAjax("http://localhost:9001/posinterface/xf",JSON.stringify(param), function (json) {
                    if (json.returnCode == 0) {
                        resObj=JSON.parse(json.outResult);
                        if(resObj.bank_code != ''){
                            malert(json.msgInfo , 'right');
                        }else {
                            malert(resObj.resp_chin , 'right', 'defeadted');
                            resObj=false;
                        }
                    } else {
                        resObj=false;
                        malert(json.c , 'right', 'defeadted');
                        return false;
                    }
                },function (){
                    common.closeLoading();
                },function (){
                    this.inAjax = false;
                });
            common.closeLoading();
                if(resObj){
                    if(this.pageData.payType == '27') {
                        this.resObj={
                            yjyrq:resObj.txndate,//原交易日期
                            orderNo:resObj.refdata,//原交易参考号
                        };
                    }else {
                        this.resObj={
                            payNo:resObj.unionMerchant ,//原交易参考号
                        };
                    }
                    this.doSaveBrghFun()
                }else {
                    this.inAjax = false;
                    return false;
                }

        },
        tfPayment:function (){
            var yylx=this.pageData.payType=='27'?'00':'02',resObj;
            var param={
                czybm:userId,
                czyxm:userName,
                mzlx:this.mzlx,
                hisGrbh:'',
                bzsm:'门诊',
                inJson: {
                    fyje:this.pageData.brxxInfo.yjje,
                    // fyje:'0.01',
                    yylx:yylx,
                },
                yljgbm:jgbm
            }
            if(this.pageData.payType  == '27') {
                param.inJson.yjyrq=this.pageData.jjlInfo.yjyrq//原交易日期
                param.inJson.yjyckh=this.pageData.jjlInfo.orderNo//原交易参考号
            }else {
                param.inJson.ysddh=this.pageData.jjlInfo.payNo //原交易参考号
            }
            param.inJson=JSON.stringify(param.inJson);
            this.postAjax("http://localhost:9001/posinterface/tf",JSON.stringify(param), function (json) {
                if (json.returnCode == 0) {
                    resObj=JSON.parse(json.outResult);
                    if(resObj.bank_code != ''){
                        malert(json.msgInfo , 'right');
                    }else {
                        malert(resObj.resp_chin , 'right', 'defeadted');
                        resObj=false;
                    }
                } else {
                    resObj=false;
                    malert(json.c , 'right', 'defeadted');
                    return false;
                }
            },function (){
                this.inAjax = false;
                common.closeLoading();
            });
            common.closeLoading();
            if(resObj){

            }else {
                this.inAjax = false;
                return false;
            }

        },
    },
};
