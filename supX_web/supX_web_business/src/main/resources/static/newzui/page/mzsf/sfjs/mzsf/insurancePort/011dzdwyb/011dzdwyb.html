<div id="lxzdwyb" class="flex-container flex-dir-c">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary" @click="loadCard()" >读卡</button>
            <button class="tong-btn btn-parmary-b" @click="loadBx()">登记</button>
            <button class="tong-btn btn-parmary-b" @click="lxzdwQxjs()">取消登记</button>
            <input class="zui-input wh180 margin-l-30" v-model="bxjsh" style="width: 220px;" type="text" placeholder="保险结算号..">&nbsp;&nbsp;
            <button class="tong-btn btn-parmary-b btn-parmary-f2a" @click="cancelYbjs()">医保冲红</button>
        </div>
    </div>
<div class="over-auto">
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">卡片</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">个人编号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="true" class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="lxzdwybBrxxContent.grbh" type="text" placeholder="无卡人员请手动填写..">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">社保机构<br/>编&emsp;&emsp;号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="true" class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="lxzdwybBrxxContent.sbjgbh" type="text" placeholder="无卡人员请手动填写..">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">录入信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">医疗统筹<br>类&emsp;&emsp;别</label>
                    <select-input  @change-data="resultChange"
                                   :not_empty="true"
                                   :child="dwyltclb_tran"
                                   :val="lxzdwybBrxxContent.yltclb"
                                   :name="'lxzdwybBrxxContent.yltclb'">
                    </select-input>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">险种标志</label>
                    <select-input  @change-data="resultChange" :not_empty="true" :child="xzbz_tran"
                                   :index="lxzdwybBrxxContent.xzbz"
                                   :val="lxzdwybBrxxContent.xzbz" :name="'lxzdwybBrxxContent.xzbz'" :search="false" :disable="false">
                    </select-input>
                </div>
                <!--<div class="zui-inline col-xxl-3">-->
                    <!--<label class="zui-form-label">疾病诊断</label>-->
                    <!--<div class="zui-input-inline">-->
                    <!--<input class="zui-input" v-model="jbContent.jbmc" @input="searching(false,'jbmc', $event.target.value)"-->
                                       <!--@keyDown="changeDown($event,'text')" id="jbmc">-->
                    <!--<search-table :message="searchCon" :selected="selSearch"-->
                                              <!--:them="them" :them_tran="them_tran" :page="page"-->
                                              <!--@click-one="checkedOneOut" @click-two="selectOne" :not_empty="true">-->
                    <!--</search-table>  -->
                    <!--</div>-->
                <!--</div>-->
                <div class="zui-inline col-xxl-3">
                    <span class="">疾病诊断</span>
                    <!--<div class="zui-input-inline">-->
                        <!--<input class="zui-input" v-model="jbContent.jbmc" @input="searching(false,'jbmc', $event.target.value)"-->
                               <!--@keyDown="changeDown($event,'text')" id="jbmc">-->
                        <!--<search-table :message="searchCon" :selected="selSearch"-->
                                      <!--:them="them" :them_tran="them_tran" :page="page"-->
                                      <!--@click-one="checkedOneOut" @click-two="selectOne" :not_empty="true">-->
                        <!--</search-table>-->
                    <!--</div>-->
                    <select-input tabindex="8"	class="wh122" @change-data="resultChange" :not_empty="false" :child="jbList"
                                  :index="'jbmc'" :index_val="'jbbm'" :index_mc="'jbmc'" :val="lxzdwybBrxxContent.jbbm"
                                  :name="'lxzdwybBrxxContent.jbbm'" :search="true">
                    </select-input>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">病人基本信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">姓名</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="lxzdwybBrxxContent.xm">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">身份证号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="lxzdwybBrxxContent.sfzhm">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">性别</label>
                    <select-input  :not_empty="true"
                                   @change-data="resultChange"
                                   :not_empty="false"
                                   :child="brxb_tran"
                                   :val="lxzdwybBrxxContent.xb"
                                   :name="'lxzdwybBrxxContent.xb'"
                                   :disable="true">
                    </select-input>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">费用日期</label>
                    <div class="zui-input-inline zui-date">
                        <i class="datenox fa-calendar"></i>
                        <input id="fyrq"  class="zui-input"  v-model="lxzdwybBrxxContent.fyrq" readonly="readonly">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">医保卡号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="lxzdwybBrxxContent.kh">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">个人账户余&emsp;&emsp;额</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="lxzdwybBrxxContent.ye">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">报销类别</label>
                    <div class="zui-input-inline">
                        <select-input  @change-data="resultChange" :not_empty="true" :child="bxlb_tran"
                                       :index="lxzdwybBrxxContent.bxlb"
                                       :val="lxzdwybBrxxContent.bxlb" :name="'lxzdwybBrxxContent.bxlb'" :search="false" :disable="false">
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">门诊挂号<br/>编&emsp;&emsp;号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="lxzdwybBrxxContent.mzghbh">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">登记信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">住院流水号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="lxzdwybBrxxContent.zylsh">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">结算号ID</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="lxzdwybBrxxContent.jshid">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">费用ID</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="lxzdwybBrxxContent.fyid">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<script type="application/javascript" src="./insurancePort/011dzdwyb/011dzdwyb.js"></script>
<script type="application/javascript" src="./insurancePort/011dzdwyb/011backfunction.js"></script>