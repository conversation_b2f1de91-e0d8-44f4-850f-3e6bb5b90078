.evaluate-top{
width: 100%;
padding: 0 10px;
display: flex;
justify-content: flex-start;
height: 50px;
}

.evaluate-content{
    padding: 0 12px;
    width: 100%;
    box-sizing: border-box;
    height:calc(100% - 95px);
}
.evaluate-content h2{
    padding:5px 0 20px 0;
   width: 100%;
    font-size: 24px;
    color:#1f2a34;
}
.wh150{
width: 150px
}
.evaluate-dl{
width: 100%;
padding-bottom: 12px;
display: flex;
justify-content: flex-start;
flex-wrap: wrap;
}
.evaluate-dl dd{
    width: 100%;
color:#757c83;
padding-bottom: 8px;
}
.evaluate-dl dt{
    color: #222832;
}
.evaluate-dl dt .c_radio{
    color: #222832;
}
.evaluate-value{
    display: flex;
    justify-content:center;
    align-items: center;
    width: 132px;
    height: 67px;
    background:rgba(243,141,79,0.08);
    border:1px solid #f38d4f;
    font-size:14px;
    flex-wrap: wrap;
    padding-bottom: 5px;
    box-sizing: border-box;
    color:#f38d4f;
}
.evaluate-height{
height: 30px;
display: flex;
justify-content: center;
align-items: baseline;
width: 100%;
}
.evaluate-height{
    height: 30px;
    width: 100%;
}
.font32{
font-size: 32px !important;
}
.top-form .top-label{
    min-width: 56px;
    max-width: 56px;
}
.Float-position {
    position: absolute !important;
    right: 20px;
    left: 51px;
}
.evaluate-dt{
display: flex;
justify-content: flex-start;
align-items: center;
padding-right: 20px;
margin-bottom: 10px;
}
input[type=checkbox].green + label{
padding-left: 25px;
color:#222832;
font-size: 14px !important;
}
.padd-b-25{
padding-bottom: 25px !important;
}
.padd-t-20{
padding-top: 20px !important;
}
.padd-b-5{
padding-bottom: 5px !important;
}
.evaluate-box{
    max-width: 960px;
}
.evaluate-list{
    display: flex;
    justify-content: flex-start;
    /*padding-left: 44px;*/
}
.evaluate-list li{
        background:#f9f9f9;
        box-shadow:0 4px 7px 0 rgba(2,49,39,0.30);
        width:140px;
        height:44px;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
        margin-right: 10px;
}
.evaluate-span{
font-size: 16px;
font-weight: bold;
    color:#575f67;
    display: flex;
    justify-content: center;
    align-items: baseline;
    padding-top: 3px;
}
.evaluate-span-btn{
font-size: 12px;
color: #767d85;
display: flex;
justify-content: center;
align-items: center;
}
.evaluate-list li.active{
    background:#1abc9c;
}
.evaluate-list li.active >span{
color: #fff;
}
.wh82{
width: 82px !important;
}
.wh450{
width: 450px;
}
.margin-l-10{
margin-left: 10px !important;
}
.position-cm{
 position: absolute;
 right: 18px;
 top: 8px;
 z-index: 1;
}
.evaluate-close{
    background:#1abc9c;
    width:20px;
    height:20px;
    border-radius:100%;
    position: absolute;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    top: -7px;
}
.top-left{
    background:#f9f9f9;
    box-shadow:0 4px 7px 0 rgba(2,49,39,0.30);
    width:36px;
    height:44px;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    position: absolute;
    left: 10px;
    top: 0;
    cursor: pointer;
}
.top-right{
    background:#f9f9f9;
    box-shadow:0 4px 7px 0 rgba(2,49,39,0.30);
    width:36px;
    height:44px;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    float: left;
    margin-right: 10px;
    cursor: pointer;
    position: relative;
}
.top-add{
    background:#f9f9f9;
    box-shadow:0 4px 7px 0 rgba(2,49,39,0.30);
    width:44px;
    height:44px;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    float: left;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}
.top-right-r:after{
    position: absolute;
    right: 15px;
    top: 50%;
    display: inline-block;
    content: "";
    width: 10px;
    height: 10px;
    border: solid #1abc9c;
    border-width: 1px 1px 0 0;
    -webkit-transform: translate(0,-50%) rotate(45deg);
    transform: translate(0,-50%) rotate(45deg);
}
.top-right-r1:after{
right: 18px;
}

.top-left-r:after{
    position: absolute;
    left: 15px;
    top: 50%;
    display: inline-block;
    content: "";
    width: 10px;
    height: 10px;
    border: solid #cccfd4;
    border-width: 1px 1px 0 0;
    -webkit-transform: translate(0,-50%) rotate(-135deg);
    transform: translate(0,-50%) rotate(-135deg);
}
.top-left-r1:after{
    left: 18px;
}
.eval-wh100{
    width:100%;
}
.icon-ccs{
    width: 15px;
    height: 15px;
}
.icon-ccs:before{
    font-size: 12px !important;
}
.padd-l-60{
padding-left: 60px;
}