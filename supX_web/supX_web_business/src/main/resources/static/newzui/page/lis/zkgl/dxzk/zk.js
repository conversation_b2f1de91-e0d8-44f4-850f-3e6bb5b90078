(function () {
    var s=new Date().getTime()
    var l=new Date()
    var e=l.setDate(l.getDate()+1)
    $(".zui-table-view").uitable();
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        ,done:function (value,data) {
        }
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            name:'',
            list:[],
        },
        methods:{
            //确定删除
            delOk:function () {
                var jsondata=''
                if(list.isChecked.length>0){
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            jsondata=this.list.push(list.getchildlist[i])
                        }
                    }
                }else{
                    jsondata='{"list":' + JSON.stringify(list.delobj) + '}';
                }
                pop.isShowpopL=false;
                pop.isShow=false;
                var json=jsondata
                this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=deleteBatch4Dxjl',json).then(
                    function(data) {
                        console.log(data);
                        if(data.body.a==0){
                            //成功回调提示
                            malert('作废成功','top','success');
                            list.getsbbm();
                        }

                    },
                    function(error) {
                        malert(error,'top','defeadted');
                    });
                malert('删除成功','top','success');
                // event.currentTarget.remove();
            }
        }
    });
    var list=new Vue({
        el:'#jyxm_icon',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            jysbList:'',
            params:{
                zxsb:'',
            },
            time:'',
            delobj:'',
            zkxm:'',
            num:0,
            getsblist:'',
            wzkxm:'',
            getchildlist:'',
            pd:{},
            arr:[],
            getyuelist:'',
            curMonthDays:'',
            
            zktdata:[],//质控图数据
            	
            zktArr:[]	
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            },
            formDatehanzi: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '年' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '月' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate()) + '日'
            },
        },
        methods:{
            getData: function () {
                var d = new Date();
                 this.curMonthDays = new Date(d.getFullYear(), (d.getMonth() + 1), 0).getDate();
                // var curMonthDays = 10;
                for (var i = 1; i < this.curMonthDays+1; i++) {
                    this.arr.push(i);
                    
                    var obj={
           					jg:''   
           			   };
                    
                }
                for (var i = 0; i < this.curMonthDays; i++) {
                    
                    var obj={
           					jg:''   
           			   };
                    
                    this.zktArr.push(obj);
                    
                }
            },
            getyue:function () {
                var data={
                    sbbm:this.params.zxsb,//当前选择设备编码
                    rq:this.time+"-01"//当前日期
                };
                $.getJSON("/actionDispatcher.do?reqUrl=LisZkgl&types=query4Tb&yq="+JSON.stringify(data), function (json) {
                    if (json.a == 0) {
                       if(json.d!=null){
                           // json.d.list[0].lisZkjlDxModel=json.d.list[0].lisZkjlDxModel.slice(1,28)
                           list.getyuelist = json.d.list;
                           list.zktdata=json.d.list;
                           for (var int = 0; int < list.zktdata.length; int++) {
                        	   console.log(JSON.stringify(list.zktArr));
                        	   var ar=JSON.parse(JSON.stringify(list.zktArr));
                        	   for (var j = 0; j < list.zktdata[int].lisZkjlDxModel.length; j++) {
                        		   var rq=parseInt(list.formDate(list.zktdata[int].lisZkjlDxModel[j].rq).split('-')[2])-1;
                        		   var jg=list.zktdata[int].lisZkjlDxModel[j].jg;
                        		   
                        		   ar[rq].jg=jg;
                        		   
                        	   }
                        	   
                        	   list.zktdata[int].lisZkjlDxModel=ar;
                           }
                           
                           
                           
                           $('.zui-scroll-right').css('width',(list.curMonthDays+2)*100+'px')
                       }
                    } else {
                        malert("获取申请检验设备失败" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            showlist:function (i) {
                this.num=i
            },
            
            hsjg:function(){
            	
            },
            
            jglr:function () {
            	if(list.pd==null){
            		malert('请选择质控项目！','top','defeadted');
            		return;
            	}
            	
                $("#brzcList").removeClass('ng-hide');
                wapse.title='定性质控结果新增';
                wapse.dxObj=list.pd;
                $("#isFold").addClass('side-form-bg');
            },
            dy:function () {
              window.print()
            },
            scall:function () {
              if(this.isChecked.length>0){
                  pop.isShowpopL=true;
                  pop.isShow=true;
                  pop.name='多条数据';
                  pop.title='删除质控记录';
              }else{
                  malert('请选择需要删除的项目！','top','defeadted');
              }
            },
            sc:function (listobj,name) {
                this.delobj=listobj
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.name=name;
                pop.title='删除质控记录';

            },
            jysb: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    if (json.a == 0) {
                        list.jysbList = json.d.list;
                        wapse.jysbList = json.d.list;
                        list.params.zxsb= json.d.list[0].sbbm;
                        wapse.params.zxsb= json.d.list[0].sbbm;
                        list.getsbbm()

                    } else {
                        malert("获取申请检验设备失败" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            getsbbm:function () {
                var par={
                    sbbm:this.params.zxsb,
                    }
                $.getJSON("/actionDispatcher.do?reqUrl=LisZkgl&types=queryAllDx_jl&yq="+JSON.stringify(par), function (json) {
                    if (json.a == 0) {
                        list.getsblist = json.d.list;
                        list.time=list.formDateMonth(s)
                        // list.
                    } else {
                        malert("获取设备编码失败" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            getlist:function (zkxm,data) {
                this.zkxm=zkxm
                    var par={
                        sbbm:this.params.zxsb,
                        zbxm:zkxm,
                        rq:this.time+'-01'
                    };
                    list.pd=data;
                    
                    $.getJSON("/actionDispatcher.do?reqUrl=LisZkgl&types=queryDxjl&yq="+JSON.stringify(par), function (json) {
                        if (json.a == 0) {
                            list.getchildlist = json.d.list;
                            wapse.listData = json.d.list[0];
                        } else {
                            malert("获取数据失败" + json.c,'top','defeadted');
                            return false;
                        }
                    });
            },
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            },
            formDateMonth: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1);
            },
        },
        created:function () {
            this.jysb();
            //this.getyue();
            this.getData();


        },
        watch:{
            'params.zxsb':function () {
                this.getsbbm()
            },
            'time':function () {
               this.$nextTick(function () {
                   this.wzkxm=this.zkxm==''?this.getsblist[0].zkxm:this.zkxm
                   this.getlist(this.wzkxm)
               })
            }
        },
    })
    var wapse=new Vue({
        el:'#silde',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            time:'',
            isShowpopL:false,
            isShow:false,
            title:'',
            listData:'',
            centent:'',
            jcjg:'',
            params:{
                zxsb:'',
            },
            jysbList:'',
            isFold: false,
            dxObj:{}
        },
        methods:{
            close:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            save:function () {
            	this.dxObj.zbxm=this.dxObj.zkxm;
            	this.dxObj.jg=this.dxObj.jgx;
            	console.log(this.dxObj);
                /*var List=[{
                    rq:this.time,
                    sbbm:list.pd.sbbm,
                    zkwph:list.pd.zkwph
                }]*/
            	var List=[this.dxObj];
                var json='{"list":' + JSON.stringify(List) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=insertBatch4Dxjl',json).then(
                    function(data) {
                        console.log(data);
                        if(data.body.a==0){
                            //成功回调提示
                            malert('新增成功','top','success');
                        }else{
                            malert(data.body.c,'top','defeadted');
                        }
                    },
                    function(error) {
                        malert(error,'top','defeadted');
                    });
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            }
        },
    });
    var wapse1=new Vue({
        el:'#brzcList1',
        data:{
            isShowpopL:false,
            isShow:false,
            title:'多规则质控评价',
            centent:'',
            isFold: false,
        },
        methods:{
            close:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            save:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            }
        },
    });
    laydate.render({
        elem: '.zqdate',
        trigger: 'click',
        theme: '#1ab394',
        done:function (value,data) {
        }
    });
    laydate.render({
        elem: '.sqdate',
        trigger: 'click',
        theme: '#1ab394',
        type:'month',
        format: 'yyyy-MM',
        done:function (value,data) {
            list.time=value
        }
    });
    laydate.render({
        elem: '.jcrq',
        trigger: 'click',
        theme: '#1ab394',
        done:function (value,data) {
            wapse.dxObj.rq=value
        }
    });
    /*				
      var data={
            			sbbm:'001',//当前选择设备编码
            			rq:'2018-02-08'//当前日期
            	};
            	$.getJSON("/actionDispatcher.do?reqUrl=LisZkgl&types=query4Tb&yq="+JSON.stringify(data), function (json) {
                   console.log(json);
                });
                
           返回数据结构
           [{cd:"80",czy:"00000",djrq:1518060552000,dw:"sadaas",jg:"70",

           lisZkjlDxModel:[{jg:"12",rq:"23123123"},{jg:"12",rq:"23123123"},{jg:"12",rq:"23123123"},{jg:"12",rq:"23123123"}]

           ,ywmc:null,zbmc:"真菌",zkwph:"50",zkxm:"0034" }
                
                
                
     */

})()
