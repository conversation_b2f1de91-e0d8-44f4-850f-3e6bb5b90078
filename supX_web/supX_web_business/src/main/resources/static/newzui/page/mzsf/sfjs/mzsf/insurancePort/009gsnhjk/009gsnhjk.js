window.mzsfjs = new Vue({
    el: '#yndryb_009',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable,
    },
    data: {
        yltclb_tran: {
            '2': '门诊慢病',
            '1': '普通门诊',
        },
        jbdmList: [],
        dqList: [],
        jzlxList: [],
        ryztList: [],
        mzbclbList: [],
        cyList: [],
        searchCon: [],
        them_tran: {},
        grxxJson: {}, // 个人信息对象
        dataJson: {},
        popContent: {},
        pageSelect: {
            page: 1,
            rows: 20,
            total: null
        },
        sfzsearch: '',
        ylhsearch: '',
        json: {
            bm:622921,
            kh: 1,
        },
        lyjson: {},
        jzjson: {},
        bcjson: {},
        selSearch: 0,
        bxlbbm: null,
        bxurl: null,
        searchval: '',
        them: {'疾病编码': 'jbbm', '疾病名称': 'zdmc'},
        myform: {},
        Tocx: {}
    },
    created: function () {
        mzsfjs = this;
    },
    watch: {
        'json.kh': function (n, o) {
            if (n == 0) {
                this.ylhsearch = ''
                this.grxxJson = {}
                this.cyList = []
            } else {
                this.sfzsearch = ''
                this.grxxJson = {}
                this.cyList = []
            }
        }
    },
    mounted: function () {
        this.getbxlb();

    },
    methods: {

        dqresultChange: function (val) {
            this.grxxJson.brdqbm = val[0];
            // Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.json.bm = val[0];
            console.log("---grxxJsonbrdqbm", this.grxxJson.brdqbm)
            this.$forceUpdate()
            this.mzbclb();
        },
        mzbclb: function () {
            var param = {
                brdqbm: this.grxxJson.brdqbm
            };
            console.log(this, mzsfjs, mzsfjs.bxurl);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + mzsfjs.bxurl + "&bxlbbm=" + mzsfjs.bxlbbm + "&types=config&method=" + "mzbclx" + "&parm=" + JSON.stringify(param), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    mzsfjs["mzbclbList"] = JSON.parse(json.d);
                    console.log(">>>>>listName", mzsfjs["mzbclbList"]);
                } else {
                    malert(json.c)
                }
            });
        },
        cancel() {
            this.grxxJson = {},
                this.cyList = []
                this.grxxJson={}
        },
        confirm() {
            console.log("--->gerenxx", this.grxxJson)
            this.grxxJson.jzlx = this.jzjson.jzbm
            this.grxxJson.ryqk = this.lyjson.rybm
            this.grxxJson.bclb = this.bcjson.bm
            rightVue.gsnhContentJson = this.grxxJson
            popTable.isShow = false;
        },
        checkedOneOut(index) {
            console.log("------->", index);
            var item = mzsfjs.searchCon[index];
            console.log("------->item", item);
            if (index == null) {
                this.pageSelect.page++
                this.searching(true, this.grxxJson.zdmc);
            } else {
                console.log(arguments);
                this.grxxJson.jbdm = item.jbbm;
                this.grxxJson.zdmc = item.zdmc;
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },
        mzjs: function () {
            var parm_str = {list:this.grxxJson};
            var url="/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mz&method=mzzsjs&time="+new Date().getTime()
            mzsfjs.$http.post(url, JSON.stringify(parm_str)).then(function (data) {
                console.log(">>>>>" + data.body.a);
                if (data.body.a == 0) {
                    malert("结算成功！")
                } else {
                    malert(data.body.c, 'top', 'defeadted')
                }
            });
        },

        gsnhmzYjs: function () {
            console.log("---》brfyjsonList", rightVue.brfyjsonList)
            var brfyList = [];
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                var fyparam = {};
                fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                if (fyparam.yzlx == null) {
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzfl;
                }
                fyparam.fyje = rightVue.brfyjsonList[i].fyje;
                fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                fyparam.fysl = rightVue.brfyjsonList[i].fysl;
                fyparam.fydj = rightVue.brfyjsonList[i].fydj;
                brfyList.push(fyparam);
            }
            // mzsfjs.grxxJson.zybRydjList = this.brfyList
            mzsfjs.grxxJson.mzks = rightVue.fzContent.mzks
            mzsfjs.grxxJson.brid = rightVue.fzContent.brid
            mzsfjs.grxxJson.sjhm = rightVue.brxxContent.sjhm
            mzsfjs.grxxJson.ghxh = rightVue.brxxContent.ghxh
            mzsfjs.grxxJson.jzys = rightVue.brfyjsonList[0].mzysxm

            mzsfjs.grxxJson.hiszfy = rightVue.fyhjAll
            param = {
                zybRydjList: brfyList,
                brdqbm: this.grxxJson.brdqbm,
                ybkh: this.grxxJson.ybkh,
                cyxh: this.grxxJson.cyxh,
                jbdm: this.grxxJson.jbdm,
                bclb: this.grxxJson.bclb,
                sjhm: this.grxxJson.sjhm,
                brid: this.grxxJson.brid,
                jzlx: this.grxxJson.jzlx,
                lyzt: this.grxxJson.ryqk,
                ghxh: this.grxxJson.ghxh,
                brxm: this.grxxJson.brxm,
                nl: this.grxxJson.nl,
                brxb: this.grxxJson.brxb,
                jtdz: this.grxxJson.jtdz,
                mzgrlb: this.grxxJson.mzgrlb,
                mzjtlb: this.grxxJson.mzjtlb,
                sbsj: this.grxxJson.sbsj,
                tcye: this.grxxJson.tcye,
                sfzjhm: this.grxxJson.sfzjhm,
                mz: this.grxxJson.mz,
                jtzhye: this.grxxJson.jtzhye,
                jzks: this.grxxJson.mzks,
                jzys: this.grxxJson.jzys,
                hiszfy:this.grxxJson.hiszfy

            }
            console.log("----->param", param)
            var parm_str = {list:param};
            var url="/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mz&method=mzyjs&time="+new Date().getTime()
            mzsfjs.$http.post(url, JSON.stringify(parm_str)).then(function (data) {
                console.log(">>>>>" + data.body.a);
                if (data.body.a == 0) {
                    mzsfjs.grxxJson={}
                    mzsfjs.grxxJson = JSON.parse(data.body.d);
                    rightVue.gsnhContentJson=mzsfjs.grxxJson
                    malert("预结算成功！",'right')
                    common.closeLoading();
                } else {
                    malert(data.body.c, 'top', 'defeadted')
                    common.closeLoading();
                }
            });

        },
        searching: function (add, val) {
            var param = {
                page: this.pageSelect.page,
                rows: 1,
                sort: "yljgbm",
                order: "asc",
                parm: val,
            }
            this.grxxJson.zdmc=val;
            if (!add) this.pageSelect.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var dim = [];
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=basic&method=ypQuery&parm=" + JSON.stringify(param), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    console.log("--->toltal", JSON.parse(json.d).total)
                    mzsfjs.pageSelect.total = JSON.parse(json.d).total;
                    console.log("--->sum", mzsfjs.page.total)
                    mzsfjs.selSearch = 0;
                    if (add) {
                        for (var i = 0; i < JSON.parse(json.d).list.length; i++) {
                            mzsfjs.searchCon.push(JSON.parse(json.d).list[i]);
                        }
                    } else {
                        mzsfjs.searchCon = JSON.parse(json.d).list;
                    }
                    if (JSON.parse(json.d).list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                } else {
                    malert(json.c)
                }
            });
        },
        changeDown: function (event) {
            if (this.searchCon[this.selSearch] == undefined) return;
            this.inputUpDown(event, this.searchCon, 'selSearch')
            this.popContent = this.searchCon[this.selSearch]
            if (event.keyCode == 13) {
                console.log(this.popContent)
                this.grxxJson.zdmc = this.popContent.zdmc
                this.grxxJson.jbbm = this.popContent.jbbm
                this.$forceUpdate()
                this.nextFocus(event)
                $(".selectGroup").hide();
            }
        },
        ckIndex: function (key, type, index) {
            this.dataJson.cyxh = this.cyList[index].xh
            this.dataJson.brdqbm = this.grxxJson.brdqbm
            this.grxxJson.mbbz = this.json.yltclb
            this.dataJson.mbbz = this.json.yltclb
            console.log(key, "-->", type, "--->", index, this.dataJson)

            this.getcyxx()
        },
        getcyxx: function () {
            console.log("----", mzsfjs.dataJson);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + mzsfjs.bxurl + "&bxlbbm=" + mzsfjs.bxlbbm + "&types=mz&method=hqmzjcxx&parm=" + JSON.stringify(mzsfjs.dataJson), function (json) {
                console.log(">>>>>", json);
                if (json.a == "0") {
                    mzsfjs.grxxJson = JSON.parse(json.d);
                    console.log(">>>>>成员grxxJson", mzsfjs.grxxJson);
                } else {
                    malert(json.c)
                }
            });
        },

        query: function (event) {
            if (event.keyCode == 13) {
                console.log("///bm", this.json.bm)
                if (this.json.bm == undefined) {
                    malert("请选择地区！");
                    return;
                }

                console.log("////", this.json);
                this.dataJson.brdqbm = this.json.bm;
                this.grxxJson.brdqbm = this.json.bm;
                console.log("////", this.dataJson);
                if (this.json.kh == 0) {
                    console.log("sfz--->", this.dataJson.sfzjhm);
                    if (this.sfzsearch == '') {
                        malert("请输入身份证号码！");
                        return;
                    }
                    this.dataJson.sfzjhm = this.sfzsearch;
                    this.dataJson.brmx = '病人姓名'
                    this.getxxBysfz();
                } else if (this.json.kh == 1) {
                    if (this.ylhsearch == '') {
                        malert("请输入医疗卡号！");
                        return;
                    }
                    this.dataJson.ybkh = this.ylhsearch;
                    this.getjtcy();
                }
            }
        },
        getbxlb: function () {
            console.log(arguments);
            var param = {bxjk: "009"};
            common.openloading();
            var _this = this;
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            _this.bxlbbm = json.d.list[0].bxlbbm;
                            _this.bxurl = json.d.list[0].url;
                        }
                        common.closeLoading();
                        _this.readyData("dqbm", "dqList");
                        _this.readyData("jzlx", "jzlxList");
                        _this.readyData("ryzt", "ryztList");
                        mzsfjs.grxxJson.brdqbm = '622921' //默认为临夏县
                        _this.mzbclb();
                    } else {
                        malert("保险类别查询失败!" + json.c);
                        common.closeLoading();
                    }
                });
        },

        resultData: function (val) {
            console.log("----》", val[0])
            var Tocx = document.getElementById("Ts");
            var result = "";
            if (val[0] == 0) {
                result = Tocx.pOutInfo;
            } else if (val[0] == 1) {
                result = Tocx.iReadSFZ();
            } else if (val[0] == 2) {
                result = Tocx.ReadCardT();
            } else if (val[0] == 3) {
                result = Tocx.GetCardInfo();
            }
            this.dataJson.readCard = result;
            this.getjtcy();
        },

        readyData: function (types, listName) {
            // console.log(this, mzsfjs);
            var param = {
                bxlbbm: this.bxlbbm
            };
            var mzsfjs = this;
            console.log(this, mzsfjs, mzsfjs.bxurl);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + mzsfjs.bxurl + "&bxlbbm=" + mzsfjs.bxlbbm + "&types=config&method=" + types + "&parm=" + JSON.stringify(param), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    mzsfjs[listName] = JSON.parse(json.d);
                    console.log(">>>>>", listName, mzsfjs[listName]);
                } else {
                    malert(json.c)
                }
            });
        },
        getjtcy: function () {
            console.log("----", mzsfjs.dataJson);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + mzsfjs.bxurl + "&bxlbbm=" + mzsfjs.bxlbbm + "&types=sfdj&method=read&parm=" + JSON.stringify(mzsfjs.dataJson), function (json) {
                console.log(">>>>>", json);
                if (json.a == "0") {
                    mzsfjs.cyList = JSON.parse(json.d);
                    console.log(">>>>>成员list", mzsfjs.cyList);
                } else {
                    malert(json.c)
                }
            });
        },
        getxxBysfz: function () {
            console.log("----", mzsfjs.dataJson);
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + mzsfjs.bxurl + "&bxlbbm=" + mzsfjs.bxlbbm + "&types=sfdj&method=ryzt&parm=" + JSON.stringify(mzsfjs.dataJson), function (json) {
                console.log(">>>>>", json);
                if (json.a == "0") {
                    mzsfjs.grxxJson = JSON.parse(json.d);
                    console.log(">>>>>个人信息", mzsfjs.grxxJson);
                } else {
                    malert(json.c)
                }
            });
        },
    },
})
//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});
