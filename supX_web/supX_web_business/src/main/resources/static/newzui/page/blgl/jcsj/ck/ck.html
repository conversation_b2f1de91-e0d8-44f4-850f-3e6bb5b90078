<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>词库</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ck.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">添加</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
            <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full" >
        <div class="zui-table-header">
            <table class="zui-table table-width50-1">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="sexs" z-width="100px">
                        <div class="zui-table-cell">数据集id</div>
                    </th>
                    <th z-field="sex" z-width="100px">
                        <div class="zui-table-cell">数据集名称</div>
                    </th>
                    <th z-field="city" z-width="80px">
                        <div class="zui-table-cell">词库id</div>
                    </th>
                    <th z-field="jm1" z-width="80px">
                        <div class="zui-table-cell">词库简码</div>
                    </th>
                    <th z-field="jm2" z-width="80px">
                        <div class="zui-table-cell">可见范围</div>
                    </th>
                    <th z-field="jm3" z-width="80px">
                        <div class="zui-table-cell">词库内容</div>
                    </th>
                    <th z-field="jm4" z-width="80px">
                        <div class="zui-table-cell">节点名称</div>
                    </th>
                    <th z-field="jm5" z-width="80px">
                        <div class="zui-table-cell">父节点序号</div>
                    </th>
                    <th z-field="jm6" z-width="80px">
                        <div class="zui-table-cell">节点序号</div>
                    </th>
                    <th z-field="jm7" z-width="80px">
                        <div class="zui-table-cell">词库类别</div>
                    </th>
                    <th z-field="jm8" z-width="60px">
                        <div class="zui-table-cell">科室id</div>
                    </th>
                    <th z-field="jm9" z-width="80px">
                        <div class="zui-table-cell">科室名称</div>
                    </th>
                    <th z-field="jm10" z-width="80px">
                        <div class="zui-table-cell">排序号</div>
                    </th>
                    <th z-field="jm11" z-width="80px">
                        <div class="zui-table-cell">词库内容</div>
                    </th>
                    <th z-field="jm12" z-width="80px">
                        <div class="zui-table-cell">用户id</div>
                    </th>
                    <th z-field="jm13" z-width="80px">
                        <div class="zui-table-cell">用户名称</div>
                    </th>
                    <th z-field="jm14" z-width="80px">
                        <div class="zui-table-cell">机构编码</div>
                    </th>
                    <th z-field="jm15" z-width="80px">
                        <div class="zui-table-cell">最近操作时间</div>
                    </th>
                    <th z-field="jm16" z-width="80px">
                        <div class="zui-table-cell">删除标志</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-height" id="zui-table">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :tabindex="$index"  @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.bzsjjid"></div></td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.bzsjjmc" :data-title="item.bzsjjmc"></i>
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ckid" :data-title="item.ckid"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ckjm" :data-title="item.ckjm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="ckkjfw[item.ckkjfw]" :data-title="ckkjfw[item.ckkjfw]"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.cknr" :data-title="item.cknr"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.kbName" :data-title="item.kbName"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.kbParent" :data-title="item.kbParent"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.kbSeq" :data-title="item.kbSeq"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="kbStyle[item.kbStyle]" :data-title="kbStyle[item.kbStyle]"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ksid" :data-title="item.ksid"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ksmc" :data-title="item.ksmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.listindex" :data-title="item.listindex"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.parentMc" :data-title="item.parentMc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.用户id" :data-title="item.用户id"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.yhmc" :data-title="item.yhmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.yljgbm" :data-title="item.yljgbm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="fDate(item.zjczsj,'date')" :data-title="fDate(item.zjczsj,'date')"></i>
                        </div>
                    </td>
                    <td><div class="zui-table-cell">
                        <div class="switch">
                            <input  type="checkbox"  :checked="item.scbz==0?true:false" disabled/>
                            <label></label>
                        </div>
                    </div>
                    </td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>标准数据集id</i>
                    <input type="text" class="label-input background-h" disabled v-model="popContent.bzsjjid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>标准数据集名称</i>
                    <input type="text" class="label-input"  v-model="popContent.bzsjjmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>词库id</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.ckid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>词库简码</i>
                    <input type="text" class="label-input"  v-model="popContent.ckjm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>词库可见范围</i>
                    <select-input class="width162" @change-data="resultChange" :data-notEmpty="false"
                                  :child="ckkjfw" :index="popContent.ckkjfw" :val="popContent.ckkjfw"
                                  :name="'popContent.ckkjfw'">
                    </select-input>
            </li>
            <li>
                    <i>词库内容</i>
                    <input type="text" class="label-input"  v-model="popContent.cknr" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>节点名称</i>
                    <input type="text" class="label-input"  v-model="popContent.kbName" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>父节点序号</i>
                    <select-input class="width162" @change-data="resultChange"
                                  :not_empty="false" :child="LcList"
                                  :index="'kbParent'" :index_val="'kbParent'"
                                  :val="kbParent" :search="true" :name="'kbParent'"
                                  id="kbParent" :index_mc="'kbParent'">
                    </select-input>
            </li>
            <li>
                    <i>节点序号</i>
                    <input type="text" class="label-input"  v-model="popContent.kbSeq" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>词库类别</i>
                    <select-input class="width162" @change-data="resultChange" :data-notEmpty="false"
                                  :child="kbStyle" :index="popContent.kbStyle" :val="popContent.kbStyle"
                                  :name="'popContent.kbStyle'">
                    </select-input>
            </li>
            <li>
                    <i>科室id</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.ksid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	科室名称</i>
                    <input type="text" class="label-input"  v-model="popContent.ksmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>排序号</i>
                    <input type="text" class="label-input"   v-model="popContent.listindex" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>词库内容</i>
                    <input type="text" class="label-input"  v-model="popContent.parentMc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>用户id</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.yhid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>用户名称</i>
                    <input type="text" class="label-input"  v-model="popContent.yhmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>	医疗机构编码</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.yljgbm" @keydown="nextFocus($event)"/>
            </li>
            <li >
                    <i style="padding-top: 13px">删除标准</i>
                    <div class="switch" >
                        <input  type="checkbox" :checked="popContent.scbz==0?true:false"/>
                        <label></label>
                    </div>
            </li>

        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ck.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>