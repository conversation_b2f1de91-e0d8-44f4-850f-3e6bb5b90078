.xmzb-top-left{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  i{
    margin-right: 5px;
    &:nth-child(2){
      margin-right: 19px;
    }
  }
}



.ksys-side{
  width: 100%;
  padding: 15px 14px;
  float: left;
  overflow: auto;
  #jyxm_icon .switch{
    top:0;
    left:17px;
  }

}
.border-r4{
  border-radius: 4px !important;
}
.ksys-btn{
  position: absolute;
  bottom: 0px;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 60px;
  left: 0;
  border-top: 1px solid #eee;
  button{
    margin-right: 20px;
  }
}
.bg-img{
  background-image: url("heLp.png");
  width: 500px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: auto;
  min-height: 600px;
  z-index: 9999999999;
  left: 50%;
  display: none;
  top: 50%;
  position: absolute;
  padding: 30px;
  transform: translate(-50%,-50%);
  .header{
    text-align: center;
    padding:30px 0 22px 0;
    font-size: 28px;
  }
  .content{
    padding: 0 29px 0;
    margin-bottom: 30px;
    font-size: 14px;
    max-height: 500px;
    overflow-y: scroll;
  }
  .nobottom{
    background:#1abc9c;
    border-radius:4px;
    width:88px;
    float: right;
    margin-right: 37px;
    line-height: 36px;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
    height:36px;
  }
}
.uitable_1 .cell-1-0{
  width: 50px !important;
}
.zui-table-view .zui-table-fixed.table-fixed-r{
  border-left: none;
}
.zui-table{
  border-bottom: 1px solid #eee;
}
.zui-table-view .zui-table-body{
  border-bottom: none;
}
.zui-table-view .zui-table-fixed table{
  border-bottom: 1px solid #eee;
}
.tong-search{
  padding: 13px 0 5px 20px;
}
.tong-search .zui-form .zui-inline{
  margin: 0 5px 0 0;
}