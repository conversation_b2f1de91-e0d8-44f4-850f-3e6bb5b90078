.cgjy-menu {
    position: absolute;
    top: 32px;
    left: 0;
    display: none;
    background: #fff;
    border: 1px solid #00cccc;
    border-top: none;
    width: 102px;
    z-index: 9999;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    text-align: center;
}
.cgjy-menu a {
    display: block;
    color: #1abc9c;
    line-height: 30px;
    cursor: pointer;
}
.cgjy-btn:hover .cgjy-menu {
    display: block;
}
.tab-message {
    width: 100%;
    height: 46px;
    background: #1abc9c;
}
.tab-message .hidden {
    display: none;
    color: #fff;
    font-size: 16px;
    float: left;
    line-height: 46px;
    padding-left: 30px;
}
.tab-message .tab-a {
    width: 530px;
    float: left;
    display: inline-block;
}
.tab-message .tab-a a {
    width: 120px;
    text-align: center;
    font-size: 16px;
    height: 44px;
    display: inline-block;
    line-height: 44px;
    margin-top: 2px;
    color: #fff;
    cursor: pointer;
}
.tab-message .tab-a .active {
    color: #1abc9c;
    background: #fff;
}
.tab-message .tab-right {
    width: auto;
    line-height: 46px;
    float: right;
    color: rgba(255, 255, 255, 0.56) !important;
    background: #1abc9c !important;
    font-size: 16px;
}
.tab-box-list {
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.fr {
    float: right !important;
    line-height: 46px;
    color: rgba(255, 255, 255, 0.56);
    font-size: 20px;
    padding-right: 20px;
}
.tab-edit-list {
    padding: 0!important;
}
.tab-edit-list .label-time,
.tab-edit-list .label-time1,
.tab-edit-list .label-time2,
.tab-edit-list .label-time3 {
    background: #00B83F;
}
i,
em {
    font-style: normal;
}
.tab-table {
    width: 100%;
    float: left;
    margin-top: 15px;
    padding-bottom: 10px;
    min-height: 600px;
    /* 垂直滚动条的滑动块 */
}
.tab-table .tab-suoxie li {
    width: calc((100% / 7));
    align-self: center;
    height: auto;
    line-height: inherit;
}
.tab-table .tab-suoxie li .tab-gl {
    width: 120px;
    padding: 0 10px;
    border: 1px solid  #1abc9c;
    border-radius: 4px;
    font-style: normal;
    height: 36px;
    line-height: 36px;
    margin-top: 9px;
    display: flex;
    justify-content: space-between;
}
.tab-table .tab-suoxie li .tab-gl em {
    color: #1abc9c;
}
.tab-table .tab-suoxie li .tab-blue {
    color: #15aa52;
}
.tab-table .tab-suoxie li .tab-red {
    color: #ff6151;
}
.tab-table ::-webkit-scrollbar {
    width: 0;
    height: 0;
}
.tab-table ::-webkit-scrollbar-thumb:vertical {
    border-radius: 0;
    -webkit-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0.3);
    background-color: #f9f9f9;
}
.tab-tables {
    max-height: 600px;
    overflow: auto;
}
.tab-confirm {
    width: 100%;
    position: absolute;
    bottom: 20px;
    display: flex;
    justify-content: flex-end;
    right: 0;
    height: 36px;
}
.tab-confirm button {
    margin-right: 20px;
}
.tab-kuai {
    width: 100%;
    padding: 20px 18px;
}
.tab-kuai p {
    font-size: 14px;
    color: #1abc9c;
    line-height: 28px;
    text-align: left;
}
.isTabels {
    width: 100%;
    height: 100%;
    display: none;
    position: fixed;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.5);
    top: 0;
}
.isTabels .table {
    left: 50%;
    top: 50%;
    position: fixed;
    z-index: 9999;
    transform: translate(-50%, -50%);
}
.pop-cig {
    width: 100%;
    height: 46px;
    line-height: 46px;
    background: #1abc9c;
    color: #fff;
    font-size: 16px;
    padding-left: 20px;
}
.pop-cig a {
    font-size: 36px;
}
.pop-search {
    padding: 14px 20px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.pop-search i {
    margin-right: 10px;
}
.pop-input {
    width: 182px;
    height: 36px;
    text-indent: 10px;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
}
.content-padding {
    width: 100%;
    padding: 0 10px;
}
.pop-cg {
    width: 100%;
    border-top: none;
}
.pop-cg li {
    width: calc((100% / 5));
}
.pop-cg .cgjy-color {
    color: #2885e2;
}
.pop-ci {
    width: 100%;
    border-top: none;
    height: 288px;
    overflow: auto;
    display: inherit;
    border-right: none;
}
.pop-ci li {
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid #dfe3e9;
}
.pop-ci li i {
    display: block;
    float: left;
    width: calc((100% / 5));
}
.pop-ci li:first-child {
    border-top: none;
}
.dbclick {
    width: 100%;
    font-size: 14px;
    color: #f2a654;
    display: flex;
    justify-content: space-between;
    align-items: center;
    float: left;
}
.dbclick .addfl {
    float: left;
    width: auto;
    max-width: 500px;
    padding-top: 1px;
}
.dbclick .addfr {
    float: right;
    padding-top: 7px;
}
.dbclick .addfr .zui-btn {
    padding: 5px 30px;
}
.jieguo {
    width: 100%;
    height: 410px;
}
.jieguo-left {
    width: 310px;
    height: 410px;
    float: left;
}
.jieguo-left ul {
    width: 100%;
    overflow: auto;
    height: 381px;
    border-bottom: 1px solid #e9eee6;
    border-left: 1px solid #e9eee6;
}
.jieguo-left ul li {
    border-top: 1px solid #e9eee6;
    overflow: hidden;
    line-height: 54px;
    cursor: pointer;
}
.jieguo-left ul li:first-child {
    border-top: none;
}
.jieguo-top {
    width: 100%;
    background: #edf2f1;
    height: 34px;
    line-height: 34px;
    border: 1px solid #e9eee6;
}
.jieguo-top span {
    text-indent: 30px;
    display: block;
    float: left;
    width: 40%;
}
.jieguo-top span:nth-child(2) {
    width: 60%;
    text-align: center;
}
.jieguo-top i {
    width: calc((100% / 4));
    display: block;
    text-align: center;
    float: left;
}
.fl {
    float: left !important;
    width: 40%;
    text-indent: 30px;
}
.jieguo-r {
    width: 60%;
    text-align: center;
    float: right;
}
.jieguo-right {
    width: 509px;
    float: right;
    height: 410px;
}
.jieguo-right .jieguo-list {
    width: 100%;
    overflow: auto;
    height: 381px;
    border-bottom: 1px solid #e9eee6;
    border-left: 1px solid #e9eee6;
    border-right: 1px solid #e9eee6;
}
.jieguo-right .jieguo-list li {
    width: 100%;
    line-height: 54px;
    border-top: 1px solid #e9eee6;
    overflow: hidden;
}
.jieguo-right .jieguo-list li:first-child {
    border-top: none;
}
.jieguo-right .jieguo-list li i {
    width: calc(((100% - 39px) / 4));
    display: block;
    text-align: center;
    float: left;
}
.jieguo-right .jieguo-list li i:nth-child(4) {
    border: 1px solid #1abc9c;
    border-radius: 4px;
    height: 34px;
    line-height: 34px;
    margin: 10px 0 0 15px;
    width: 100px;
    padding: 0 10px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
}
.jieguo-right .jieguo-list li i:nth-child(4) em {
    color: #1abc9c;
}
.pc-zhi {
    width: 96%;
    border-top: 1px dashed #dfe3e9;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 0 auto;
    padding-top: 15px;
}
.pc-zhi i {
    margin-right: 10px;
    position: relative;
}
.pc-zhi select {
    width: 182px;
    height: 34px;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
}
.pc-zhi .pc-select {
    width: 280px;
    position: absolute;
    top: 36px;
    left: 0;
    display: none;
    z-index: 9999;
    height: 260px;
    background: #fff;
    border: 1px solid #dfe3e9;
    box-shadow: 0 0 5px #dfe3e9;
}
.pc-zhi .pc-select span {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    color: #333333;
    font-size: 14px;
    height: 34px;
    border-bottom: 1px solid #dfe3e9;
}
.pc-zhi .pc-select span b {
    width: 40%;
    display: block;
}
.pc-zhi .pc-select span b:nth-child(2) {
    width: 60%;
}
.pc-zhi .pc-select .pc-option {
    height: 210px;
    overflow: auto;
    width: 100%;
    background: #fff;
}
.pc-zhi .pc-select a {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    line-height: 52px;
    border-top: 1px solid #dfe3e9;
    cursor: pointer;
}
.pc-zhi .pc-select a b {
    width: 40%;
    display: block;
}
.pc-zhi .pc-select a b:nth-child(2) {
    width: 60%;
}
.pc-zhi .pc-select a:hover {
    background: rgba(26, 188, 156, 0.08);
    border: 1px solid #1abc9c;
    line-height: 52px;
}
.pc-zhi .pc-select a:first-child {
    border-top: none;
}
b {
    font-weight: normal;
}
.addline {
    line-height: 24px !important;
}
.pici {
    height: 350px;
    width: 100%;
    padding-top: 20px;
}
.cgjy-fixed {
    width: 360px;
    position: fixed;
    bottom: 42px;
    right: 0;
    background: #fff;
    height: auto;
}
.cgjy-fixed .header {
    height: 40px;
    background: #1abc9c;
    line-height: 40px;
    color: #fff;
    padding-left: 15px;
    font-size: 14px;
}
.cgjy-fixed .header .titel {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.cgjy-fixed .header .icon-shanchu {
    width: 47px;
    height: 40px !important;
    position: absolute;
    right: 10px;
    cursor: pointer;
}
.cgjy-fixed .header .icon-shanchu:after {
    right: 0;
    position: absolute;
    font-size: 15px;
    content: '';
    width: 22px;
    top: 50%;
    background: #fff;
    cursor: pointer;
    height: 2px;
    border: 1px solid #fff;
    transform: translate(-50%, 0%);
}
.cgjy-content {
    width: 100%;
    max-height: 336px;
}
.cgjy-list {
    max-height: 300px;
    overflow: auto;
}
.cgjy-list span {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    border-bottom: 1px solid  #e9eee6;
}
.cgjy-list span i {
    width: 50%;
    display: block;
    text-align: center;
}
.cgjy-list span:nth-child(2n) {
    background: #fdfdfd;
}
.cgjy-tt {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #edf2f1;
    height: 34px;
}
.cgjy-tt i {
    width: 50%;
    text-align: center;
}
.dvmenu {
    width: 100px;
    height: 32px;
    float: left;
    position: relative;
}
.dvmenu:hover .cgjy-menu {
    display: block;
}
.bgcx-money {
    font-size: 14px;
    width: 100%;
    padding-top: 10px;
    text-indent: 30px;
}
.bgcx-money i {
    color: #1abc9c;
}
.border-none {
    width: 100%;
    border: none;
    background: none;
    height: 30px;
}
.zui-table-view .zui-table-fixed.table-fixed-r {
    border-left: none;
}
.zui-table-view table tr {
    border-bottom: none;
}
.inner li {
    width: 100%;
    margin-bottom: 0;
}
.more-sq {
    text-indent: 20px;
    color: #1abc9c;
    line-height: 30px;
    font-weight: bold;
}
@media only screen and (max-height: 768px) {
    .tab-tables {
        max-height: 450px !important;
        overflow: auto;
    }
}
.tong-top{
    overflow: inherit;
}
.icon-position{
    top: 11px;
}
.text-indent-20{
    text-indent: 20px !important;
}
.icon-font25:before{
    font-size: 25px;
}
.crisis-danger {
    background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);
    border: 1px solid #f46161;
    border-radius: 38px;
    width: 16px;
    height: 16px;
    font-size: 12px;
    color: #fff;
    display: block;
    float: left;
    margin: 6px 4px 0 0;
}
small {
    transform: scale(0.75);
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 11px;
}

.zui-collapse{
    border-collapse: collapse;
}
.zui-collapse tr.table-active{
    background:rgba(255,69,50,0.08);
}
.pop-850 .ksys-side{
    padding: 15px 15px 15px 10px;
}
.cgjy-wjz{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background:#edf2f1;
    border:1px solid #e9eee6;
    height:34px;
}
.cgjy-wjz i{

    color:#333333;
}
.cgjy-wjz i,.wjz-list li span{
    width: calc((100% - 300px) / 3);
    text-align: center;
}
.cgjy-wjz i:nth-child(1),.cgjy-wjz i:nth-child(5),.wjz-list li span:nth-child(1),.wjz-list li span:nth-child(5){
    width: 50px;
}
.cgjy-wjz i:nth-child(3),.wjz-list li span:nth-child(3){
    width: 200px;
    text-align: left;
}
.wjz-list{
    width: 100%;
    max-height:320px;
}
.wjz-list li{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    background:#fff;
    border: 1px solid #e9eee6;
    border-top: none;
}
.wjz-list li:nth-child(2n){
    background:#fdfdfd;
}
.wjz-list li:hover{
    background: #edfaf7;
}
.crisis-dangers {
    margin: 2px 4px 0 14px;
}
.text-indent-30{
text-indent: 30px;
}
.wjz-span{
    flex-wrap: nowrap;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
/*站位*/
.wjz-zw{
    border-radius: 38px;
    width: 27px;
    height: 16px;
    font-size: 12px;
    color: #fff;
    display: block;
    float: left;
    margin: 6px 4px 0 0;
}

.wjz-width {
    width: 380px;
    height: 257px;
    position: absolute;
    top: calc((100vh - 257px) / 2);
    left: calc((100vw - 380px) / 2);
    z-index: 9999;
    background: #fff;
}
.wjz-top {
    width: 100%;
    height: 46px;
    background: #1abc9c;
    color: #fff;
    font-size: 16px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.wjz-content {
    padding: 19px 15px 15px 15px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.wjz-textarea {
    width: 100%;
    border: 1px solid #d7dbe1;
    padding: 10px 12px;
    box-sizing: border-box;
    height: 68px;
    border-radius: 5px;
    color: #354052;
}
.wjz-bz {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding:0 15px 20px 15px;
    box-sizing: border-box;
}
.wjz-btn {
    width: 100%;
    padding:0 5px 0 0;
    display: flex;
    justify-content: flex-end;
}

.crunch {
    width: 100%;
}
.crunch-name{
    color:#7f8fa4;
    padding-bottom: 4px;
}
.crisis-title{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 34px;
    width:292px;
    color:#333333;
    background:#edf2f1;
    border:1px solid #e9eee6;
}
.crisis-list {
    width: 100%;
    max-height: 300px;
}
.crisis-list li {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    background: #fff;
    border: 1px solid #e9eee6;
    border-top: none;
    width: 292px;
}
.crisis-list li:nth-child(2n) {
    background: #fdfdfd;
}
.crisis-list li:hover {
    background: #edfaf7;
}
.crisis-title .crisis-span,
.crisis-list li .crisis-span {
    width: calc((100% - 80px) / 2);
    text-align: center;
    flex-wrap: nowrap;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.crisis-title .crisis-span:nth-child(3),
.crisis-list li .crisis-span:nth-child(3) {
    width: 80px;
    text-align: center;
}
.text-indent-28{
    text-indent: 28px !important;
}

.wjz-list li.table-active,.crisis-list li.table-active{
    background:rgba(255,69,50,0.08);
}
.icon-positions{
    top:34px;
}