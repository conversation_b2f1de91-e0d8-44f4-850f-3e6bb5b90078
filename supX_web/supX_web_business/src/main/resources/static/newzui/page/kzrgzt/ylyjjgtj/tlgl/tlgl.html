<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>讨论管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link rel="stylesheet" href="tlgl.css">
    <link rel="stylesheet" href="../kshy/kshy.css">
</head>
<body class="skin-default flex-container flex-dir-c flex-one">
<div class="background-f flex-container flex-dir-c flex-one">
<div class="tong-top flex-container flex-align-c titleTab">
    <div class="tabItem font-16 text-center flex-container color-c3 flex-align-c flex-jus-c font-weight" @click="tab(0)" :class="index==0?'active':''">疑难病讨论</div>
    <div class="tabItem font-16 text-center flex-container color-c3 flex-align-c flex-jus-c font-weight" @click="tab(1)" :class="index==1?'active':''">死亡讨论</div>
</div>
    <div class="content flex-container flex-dir-c flex-one" v-cloak>
        <div class="flex-container flex-dir-c flex-one" key="a" v-if="index==0">
            <div  >
                <div class="tong-search">
                    <div class="top-form">
                        <label class="top-label">时间段</label>
                        <div class="top-zinle flex-container">
                            <div class="zui-date position">
                                <i class="iconfont icon-icon61"></i>
                                <input type="text" class="zui-input wh122 dateStart" v-model="beginrq" />
                            </div>
                            <span class="flex-container flex-align-c padd-r-10 padd-l-10">至</span>
                            <div class="zui-date position">
                                <i class="iconfont icon-icon61"></i>
                                <input type="text" class="zui-input wh122 dateEnd" v-model="endrq"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chart height-310">
                    <!--<h2 class="text-center font-18 font-weight color-393f">科室医生会议次数统计</h2>-->
                    <div class="flex-container height-310 flex-align-c">
                        <div>
                            <div class="num num-border">
                                <span class="text-num">5</span><span class="text-line">次</span>
                            </div>
                            <div class="text color-393f text-center margin-t-10 font-16">疑难病讨论总次数</div>
                        </div>
                        <div class="canvas" @load="ajaxChart($event)" id="canvas"></div>
                        <div class="flex-container flex-dir-cr" style="height: 100%">
                        <ul class="ranking margin-b-10">
                            <li class="flex-container whiteSpace flex-align-c" v-for="(list,index) in 22">
                                <div class="rankingNum rankingNumImg font-18 color-c7 text-center" :class="[index==0?'rankingOne':'',index==1?'rankingtwo':'',index==2?'rankingThree':'']">{{index>2?index+1:''}}</div>
                                <div class="rankingImg" style="background-image: url('/newzui/pub/image/2018072106.png')"></div>
                                <div class="rankingName">李浩然</div>
                                <div class="rankingNumber color-f38d4f">10次</div>
                            </li>
                        </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div style="width: 100%;background: #f2f2f2;height: 8px;"></div>
            <div class="tong-top font-16 color-c3 font-weight flex-container flex-align-c ">疑难病讨论列表</div>
            <div class=" zui-table-view flex-container flex-dir-c flex-one padd-l-10 padd-r-10  padd-t-10 ">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m text-left"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xxl"><span>身份证号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>年龄</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>讨论时间</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>死亡时间</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xxl text-left"><span>临床诊断</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>参与人数</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>未参与人数</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>科室占比</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>状态</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-m"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <!-- v-if="jsonList.length!=0" -->
                        <tbody>
                        <tr :tabindex="$index" v-for="(item, $index) in 22" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)"
                            @dblclick="doPop($index)"
                            class="tableTr2">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    {{$index}}
                                </div>
                            </td>
                            <td >
                                <div class="zui-table-cell cell-s color-green text-decoration">
                                    李浩然
                                </div>
                            </td>
                            <td >
                                <div class="zui-table-cell cell-xxl title ">
                                    510623199412235236
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                   //
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    2017/2/2
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    2017/2/2
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xxl title text-left">
                                   临床诊断肾虚，需要肾宝片
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell color-f38d4f cell-s">
                                    16人
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell color-f38d4f cell-s">
                                    2人
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell color-4193e5 cell-s">
                                    60%
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" :class="$index%2==0?'colr-ff6555':'color-bf71ec'">
                                    未确认
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-m flex-center">
                                    <i class="iconfont icon-iocn45" data-title="审核" @click="newOpenPage()"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                </div>
                <div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"data-no-change  @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in 22"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                @dblclick="doPop($index)">
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-m flex-center" >
                                        <i class="iconfont icon-iocn45" data-title="审核" @click="newOpenPage()"></i>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex-container flex-dir-c flex-one" key="b" v-if="index==1">
            <div>
                <div class="tong-search">
                    <div class="top-form">
                        <label class="top-label">时间段</label>
                        <div class="top-zinle flex-container">
                            <div class="zui-date position">
                                <i class="iconfont icon-icon61"></i>
                                <input type="text" class="zui-input wh122 dateStart" v-model="beginrq" />
                            </div>
                            <span class="flex-container flex-align-c padd-r-10 padd-l-10">至</span>
                            <div class="zui-date position">
                                <i class="iconfont icon-icon61"></i>
                                <input type="text" class="zui-input wh122 dateEnd" v-model="endrq"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chart height-310">
                    <!--<h2 class="text-center font-18 font-weight color-393f">科室医生会议次数统计</h2>-->
                    <div class="flex-container height-310">
                        <div class="flex-container  flex-align-c">
                            <div>
                                <div class="num num-border">
                                    <span class="text-num">2</span><span class="text-line">次</span>
                                </div>
                                <div class="text color-393f text-center margin-t-10 font-16">死亡讨论总次数</div>
                            </div>
                            <div style="padding-left: 44px">
                                <div class="num num-border border-error">
                                    <span class="text-num text-num-error">5</span><span class="text-line text-num-error">例</span>
                                </div>
                                <div class="text color-393f text-center margin-t-10 font-16">延时讨论次数</div>
                            </div>
                        </div>
                        <div class="canvas1 canvas"  id="canvas"></div>
                        <div class="flex-container flex-dir-cr" style="height: 100%">
                        <ul class="ranking margin-b-10">
                            <li class="flex-container whiteSpace flex-align-c" v-for="(list,index) in 22">
                                <div class="rankingNum rankingNumImg font-18 color-c7 text-center" :class="[index==0?'rankingOne':'',index==1?'rankingtwo':'',index==2?'rankingThree':'']">{{index>2?index+1:''}}</div>
                                <div class="rankingImg" style="background-image: url('/newzui/pub/image/2018072106.png')"></div>
                                <div class="rankingName">李浩然</div>
                                <div class="rankingNumber color-f38d4f">10次</div>
                            </li>
                        </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div style="width: 100%;background: #f2f2f2;height: 8px;"></div>
            <div class="tong-top font-16 color-c3 font-weight flex-container flex-align-c ">疑难病讨论列表</div>
            <div class=" zui-table-view flex-container flex-dir-c flex-one padd-l-10 padd-r-10  padd-t-10 ">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m text-left"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xxl"><span>身份证号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>年龄</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>讨论时间</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>死亡时间</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xxl text-left"><span>临床诊断</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>参与人数</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>未参与人数</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>科室占比</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>状态</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-m"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <!-- v-if="jsonList.length!=0" -->
                        <tbody>
                        <tr :tabindex="$index" v-for="(item, $index) in 22" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex},$index%2==0?'errorBg':'']"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)"
                            @dblclick="doPop($index)"
                            class="tableTr2">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    {{$index}}
                                </div>
                            </td>
                            <td >
                                <div class="zui-table-cell cell-s color-green text-decoration">
                                    李浩然
                                </div>
                            </td>
                            <td >
                                <div class="zui-table-cell cell-xxl title ">
                                    510623199412235236
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    //
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    2017/2/2
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    2017/2/2
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xxl title text-left">
                                    临床诊断肾虚，需要肾宝片
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell color-f38d4f cell-s">
                                    16人
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell color-f38d4f cell-s">
                                    2人
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell color-4193e5 cell-s">
                                    60%
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" :class="$index%2==0?'colr-ff6555':'color-bf71ec'">
                                    未确认
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-m flex-center">
                                    <i class="iconfont icon-iocn45" data-title="审核" @click="newOpenPage()"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                </div>
                <div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"data-no-change  @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in 22"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                @dblclick="doPop($index)">
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-m flex-center" >
                                        <i class="iconfont icon-iocn45" data-title="审核" @click="newOpenPage()"></i>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
<script type="text/javascript" src="tlgl.js"></script>
</body>
</html>