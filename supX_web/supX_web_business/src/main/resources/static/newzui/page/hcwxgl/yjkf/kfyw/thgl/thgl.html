<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>退货管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="thgl.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper" v-cloak>
    <div class="panel" >
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kdFun(0)" v-show="isShowkd">开单</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kdFun(1)" v-if="isShowpopL">添加材料</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="searchHc()" v-if="!sxShow">刷新</button>
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="searchHc()" v-if="sxShow">刷新</button>
        </div>
        <div class="tong-search" :class="{'tong-padded':isShow}">
            <div class="zui-form" v-show="isShowkd">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">一级库房</label>
                    <div class="zui-input-inline wh122" style="margin-left: 20px">
                        <select-input @change-data="resultRydjChanges"
                                      :child="KFList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">时间段</label>
                    <div class="zui-input-inline margin-f-l5 flex-container flex-align-c">
                        <input class="zui-input todate wh120 " v-model="param.beginrq" @click="showTime('timeVal','beginrq')" placeholder="请选择申请开始日期" id="timeVal"/>
                        <span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh120 " v-model="param.endrq" @click="showTime('timeVal','endrq')" placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="searchHc()" type="text" v-model="param.parm"/>
                    </div>
                </div>
            </div>
            <div class="jbxx" v-show="isShow">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20">
                            <div class="zui-inline">
                                <label class="zui-form-label ">库房</label>
                                <div class="zui-input-inline wh122 margin-f-l20">
                                    <select-input @change-data="resultRydjChanges"
                                                  :child="KFList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                                  :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" :disable="jyinput">
                                    </select-input>
                                </div>
                            </div>
                            <div class="zui-inline" style="width:50%;">
                                <label class="zui-form-label ">备注</label>
                                <div class="zui-input-inline margin-f-l20">
                                    <input class="zui-input" placeholder="请输入备注" type="text" id="bzms" v-model="popContent.bzms" :disabled="jyinput"/>
                                </div>
                            </div>
                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期:<i v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view  padd-r-10 padd-l-10" >
        <!--入库列表-->
        <div class="zui-table-header" v-show="isShowkd">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl "><span>退货单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库方式</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>制单日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>SPD单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body  zuiTableBody" v-show="isShowkd" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                    <tr v-for="(item,$index) in thdList"  :class="[{'table-hovers':$index===activeIndex}]" :tabindex="$index"
                    	@dblclick="showDetail($index,item)">
                        <td class=" cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.ckdh">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">退货</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zdyxm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="fDate(item.zdrq,'AllDate')">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.spdno">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <i v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : item.shzfbz=='3' ? 'color-wtg':'' "></i>
                            </div>
                        </td>
                        <td class="cell-l">
                            <div class="zui-table-cell cell-l">
                               <span class="flex-center padd-t-5">
                                <em class="width30" v-if="item.shzfbz == '0'"><i class="icon-sh " data-title="审核" @click="showDetail($index)" ></i></em>
                                <em  class="width30"  v-if="item.shzfbz == '0'"><i class="icon-js " data-title="作废" @click="invalidData($index)"></i></em>
                                <em  class="width30"  v-if="item.shzfbz != '1' && item.shzfbz !='2'"><i class="icon-bj" data-title="编辑" @click="editIndex($index)"></i></em>
                               </span>
                            </div>
                        </td>
                        <p v-show="thdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                </tbody>
            </table>
        </div>
        <!--添加材料-->
        <div class="zui-table-header"  v-show="isShow" style="height: 38px !important;overflow: hidden">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class=" cell-m"><div class="zui-table-cell"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>供货单位</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>退库数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料总价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" v-show="isShow" @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item,$index) in jsonList"  :tabindex="$index"  :class="[{'table-hovers':$index===activeIndex}]">
                    <td class=" cell-m">
                        <div class="zui-table-cell" v-text="$index+1">序号</div>
                    </td>
                    <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ghdwmc">
                            </div>
                        </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypgg">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            -{{item.cksl}}{{item.kfdwmc}}
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypjj">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yplj">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj * item.cksl,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.scph">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-over-2" v-text="item.cdmc">状态</div>
                    </td>

                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                    </td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                            <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="zui-table-fixed table-fixed-l" v-show="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" v-show="isShowkd" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="rkgl-position flex-container padd-l-10 padd-r-10 flex-jus-sb" v-show="isShow">
            <div class="flex-container " :id="money">
                        <i class="padd-r-10">材料进价总价: <em class="color-wtg">{{fDec(json.jjzj,2)}}元</em></i>
                        <i>材料零价总价: <em class="color-wtg">{{fDec(json.ljzj,2)}}元</em></i>
            </div>
           <span class="flex-container ">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="print">打印</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-show="zfShow">作废</button>
                <!-- <button class="tong-btn btn-parmary-f2a xmzb-db" @click="jujue" v-show="ShShow">拒绝</button> -->
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow">审核</button>
           </span>
        </div>
    </div>
</div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-548" v-cloak  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--编辑材料-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>材料名称</i>
                    <input id="ypmc" ref="autofocus" class="zui-input" v-model="popContent.ypmc" @keydown="changeDown($event,'ypmc')"
                           :not_Empty="true" @input="change(false,'ypmc',$event.target.value)">
                    <search-table :message="searchCon" :selected="selSearch" :current="dg.page" :rows="dg.rows" :total="total"
                                  :them="them" :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
            </li>
            <li>
                    <i>供货单位</i>
                    <input type="text"  class="zui-input" disabled v-model="popContent.ghdwmc" @keydown="nextFocus($event)" @keydown.enter="addData" />
            </li>
            <li>
                <i>随货单据号</i>
                <input type="text" class="zui-input"  @keydown="nextFocus($event)" v-model="popContent.shdjh">
            </li>
            <li>
                <i>发票号</i>
                <input type="text" class="zui-input"  @keydown="nextFocus($event)" v-model="popContent.fph">
            </li>
            <li>
                    <i>退货数量</i>
                    <input type="text" id="thsl" class="zui-input" v-model="popContent.cksl" @keydown="nextFocus($event)" @keydown.enter="addData" />
            </li>
            <li>
                    <i>制单日期</i>
                    <input type="text" id="rksl" disabled="disabled"  class="zui-input " v-model="zdrq" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>材料规格</i>
                    <input type="text" class="zui-input" id="ypjj" disabled="disabled" v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
            </li>
            <li class="position">
                    <i>库存数量</i>
                    <input type="number" class="zui-input background-h" disabled="disabled" id="yplj" v-model="popContent.kcsl" @keydown="nextFocus($event)"/>
                    <span class="cm" v-text="popContent.kfdwmc"></span>
                </li>
            <li>
                    <i>可用库存</i>
                    <input type="number" class="zui-input background-h" disabled="disabled" v-model="popContent.kykc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>材料进价</i>
                    <input type="number" class="zui-input" disabled="disabled" v-model="popContent.ypjj"  @keydown="nextFocus($event)" />
            </li>
            <li>
                    <i>材料零价</i>
                    <input type="number" class="zui-input " disabled="disabled" v-model="popContent.yplj"  @keydown="nextFocus($event)" />
            </li>
            <li>
                    <i>材料批号</i>
                    <input type="text" class="zui-input" disabled="disabled" @keydown="nextFocus($event)" v-model="popContent.scph">
            </li>
            <li>
                    <i>有效期至</i>
                    <input type="text" class="zui-input times2  " id="_yxqz" disabled="disabled" :value="fDate(popContent.yxqz,'date')" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>材料产地</i>
                    <input type="text" class="zui-input" disabled="disabled" @keydown="nextFocus($event)" v-model="popContent.cdmc">
            </li>

            <li>
                    <i>分装比例</i>
                    <input type="text" class="zui-input"  disabled="disabled" @keydown="nextFocus($event)" v-model="popContent.fzbl">
            </li>
            <li>
                    <i>产品标准&ensp;&ensp;号</i>
                    <input type="text" class="zui-input"  disabled="disabled" @keydown="nextFocus($event)" v-model="popContent.cpbzh">
            </li>
            <li>
                    <i>批准文号</i>
                    <input type="text" class="zui-input"  disabled="disabled" @keydown.enter="addData" v-model="popContent.pzwh">
            </li>
            <li>
                    <i>生产日期</i>
                    <input type="text" class="zui-input  times1" id="_scrq" disabled="disabled" :value="fDate(popContent.scrq,'date')" @keydown="nextFocus($event)">
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="addData">添加</button>
    </div>
</div>

<script src="thgl.js"></script>
</body>

</html>
