var qjindex = '';
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        num: 0,
        popContent: {},
        jsonList: [],
        ksList: [],
        baksList: [],
        edits: [],
        baksContent: {}, //病案科室对象
        totlePage: ''
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        this.getData();
    },
    methods: {
        //新增
        addData: function () {
            wap.loadNum();
            switch (this.num) {
                case 0:
                    wap.title = '新增疾病编码';
                    wap.popContent = {};
                    wap.open();
                    break;
                case 1:
                    wap.title = '新增手术编码';
                    wap.open();
                    wap.popContent = {};
                    break;
                case 2:
                    wap.title = '新增肿瘤形态学编码';
                    wap.open();
                    wap.popContent = {};
                    break;
                case 3:
                    wap.title = '新增病理诊断编码';
                    wap.open();
                    wap.popContent = {};
                    break;
                case 4:
                    wap.title = '新增中医症候编码';
                    wap.open();
                    wap.popContent = {};
                    break;
                case 5:
                    wap.title = '新增病原学名称编码';
                    wap.open();
                    wap.popContent = {};
                    break;
                case 6:
                    // wap.title = '新增病案科室维护';
                    // wap.open();
                    // wap.popContent={};
                    break;
            }
        },

        //删除
        del: function () {
            this.remove();
        },
        //保存病案科室
        saveBaks: function () {
            if (wap.baksList.length == 0) {
                malert("保存的对象不能为空！",'top','defeadted');
                return;
            }
            //请求后台进行保存
            var json = '{"list":' + JSON.stringify(baksList.baksList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=BaglBmwhBaks&types=updateBetch&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert("保存成功");
                    wrapper.getData();
                    wrapper.edit = [];
                    wrapper.isChecked = [];
                } else {
                    malert("保存失败：" + data.body.c,'top','defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        //进入页面加载列表信息
        //切换
        tabBg: function (index) {
            this.num = index;
            wap.num = index;
            this.param.page = 1;
            this.param.parm = '';
            this.getData();
            this.isChecked = [];
            this.isCheckedall = false;
        },
        getData: function () {
            common.openloading('.zui-table-view');
            switch (this.num) {
                case 0:
                    this.param.sort = 'jbmb';
                    this.jsonList = [];
                    $.getJSON("/actionDispatcher.do?reqUrl=New1BaglBmwhJbbm&types=queryBaglJbbm&parm=" + JSON.stringify(this.param), function (json) {
                        if (json.a == 0 && json.d) {
                            wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                            wrapper.jsonList = json.d.list;
                        }
                    });
                    break;
                case 1:
                    this.param.sort = 'ssbm';
                    this.jsonList = [];
                    $.getJSON("/actionDispatcher.do?reqUrl=New1BaglBmwhSsbm&types=queryBaglJbbm&parm=" + JSON.stringify(this.param), function (json) {
                        if (json.a == 0 && json.d) {
                            wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                            wrapper.jsonList = json.d.list;
                        }
                    });
                    break;
                case 2:
                    this.param.sort = 'jbbm';
                    this.jsonList = [];
                    $.getJSON("/actionDispatcher.do?reqUrl=BaglBmwhM&types=queryBaglJbbmM&parm=" + JSON.stringify(this.param), function (json) {
                        if (json.a == 0 && json.d) {
                            wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                            wrapper.jsonList = json.d.list;
                        }
                    });
                    break;
                case 3:
                    this.param.sort = 'zdbm';
                    this.jsonList = [];
                    $.getJSON("/actionDispatcher.do?reqUrl=BaglBmwhBlzd&types=queryBaglBlzd&parm=" + JSON.stringify(this.param), function (json) {
                        if (json.a == 0 && json.d) {
                            wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                            wrapper.jsonList = json.d.list;
                        }
                    });
                    break;
                case 4:
                    this.param.sort = 'jbbm';
                    this.jsonList = [];
                    $.getJSON("/actionDispatcher.do?reqUrl=BaglBmwhZy&types=queryBaglJbbmZy&parm=" + JSON.stringify(this.param), function (json) {
                        if (json.a == 0 && json.d) {
                            wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                            wrapper.jsonList = json.d.list;
                        }
                    });
                    break;
                case 5:
                    this.param.sort = 'byxbm';
                    this.jsonList = [];
                    $.getJSON("/actionDispatcher.do?reqUrl=BaglBmwhByx&types=queryBaglByx&parm=" + JSON.stringify(this.param), function (json) {
                        if (json.a == 0 && json.d) {
                            wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                            wrapper.jsonList = json.d.list;
                        }
                    });
                    break;
                case 6:
                    //请求后台查询所有科室
                    var parm = {
                        tybz: "0"
                    };
                    this.ksList=[]
                    this.param.sort = 'ksbm';
                    $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryKsbm&types=query&dg=" + JSON.stringify(this.param) + "&parm=" + JSON.stringify(parm),
                        function (json) {
                            if (json.a == 0 && json.d) {
                                wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                                wrapper.ksList = json.d.list;
                            } else {
                                malert("查询失败：" + json.c);
                            }
                        });
                    break;
            }
            common.closeLoading()
        },

        //删除二级科目
        remove: function (index) {
            switch (wrapper.num) {
                case 0:
                    var jbbmList = [];
                    var jbbm = {};
                    var removeUrl = "/actionDispatcher.do?reqUrl=New1BaglBmwhJbbm&types=delete";

                    if (index != null) {
                        jbbm.jbmb = this.jsonList[index].jbmb;
                        jbbmList.push(jbbm);
                    } else {
                        for (var i = 0; i < this.isChecked.length; i++) {
                            if (this.isChecked[i] == true) {
                                jbbm.jbmb = this.jsonList[i].jbmb;
                                jbbmList.push(jbbm);
                            }
                        }
                    }
                    if (common.openConfirm("确认删除该条信息吗？", function () {
                        var json = '{"list":' + JSON.stringify(jbbmList) + '}';
                        wrapper.$http.post(removeUrl, json).then(function (data) {
                            if (data.body.a == 0) {
                                wrapper.getData();
                                malert("删除成功")
                            } else {
                                malert("删除失败", 'top', 'defeadted')
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                        return false;
                    }
                    break;
                case 1:
                    var ssbmList = [];
                    var ssbm = {};
                    var removeUrl = "/actionDispatcher.do?reqUrl=New1BaglBmwhSsbm&types=delete";
                    if (index != null) {
                        ssbm.ssbm = this.jsonList[index].ssbm;
                        ssbmList.push(ssbm);
                    } else {
                        for (var i = 0; i < this.isChecked.length; i++) {
                            if (this.isChecked[i] == true) {
                                ssbm.ssbm = this.jsonList[i].ssbm;
                                ssbmList.push(ssbm);
                            }
                        }
                    }
                    if (common.openConfirm("确认删除该条信息吗？", function () {
                        var json = '{"list":' + JSON.stringify(ssbmList) + '}';
                        wrapper.$http.post(removeUrl, json).then(function (data) {
                            if (data.body.a == 0) {
                                wrapper.getData();
                                malert("删除成功")
                            } else {
                                malert("删除失败", 'top', 'defeadted')
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                        return false;
                    }

                    break;
                case 2:
                    var zlxtxList = [];
                    var jbbmM = {};
                    var removeUrl = "/actionDispatcher.do?reqUrl=BaglBmwhM&types=delete";
                    if (index != null) {
                        jbbmM.jbbm = this.jsonList[index].jbbm
                        zlxtxList.push(jbbmM);
                    } else {
                        for (var i = 0; i < this.isChecked.length; i++) {
                            if (this.isChecked[i] == true) {
                                jbbmM.jbbm = this.jsonList[i].jbbm
                                zlxtxList.push(jbbmM);
                            }
                        }
                    }
                    if (common.openConfirm("确认删除该条信息吗？", function () {
                        var json = '{"list":' + JSON.stringify(zlxtxList) + '}';
                        wrapper.$http.post(removeUrl, json).then(function (data) {
                            if (data.body.a == 0) {
                                wrapper.getData();
                                malert("删除成功")
                            } else {
                                malert("删除失败", 'top', 'defeadted')
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                        return false;
                    }
                    break;
                case 3:
                    var blzdList = [];
                    var blzd = {};
                    var removeUrl = "/actionDispatcher.do?reqUrl=BaglBmwhBlzd&types=delete";
                    if (index != null) {
                        blzd.zdbm = this.jsonList[index].zdbm
                        blzdList.push(blzd);
                    } else {
                        for (var i = 0; i < this.isChecked.length; i++) {
                            if (this.isChecked[i] == true) {
                                blzd.zdbm = this.jsonList[i].zdbm
                                blzdList.push(blzd);
                            }
                        }
                    }
                    if (common.openConfirm("确认删除该条信息吗？", function () {
                        var json = '{"list":' + JSON.stringify(blzdList) + '}';
                        wrapper.$http.post(removeUrl, json).then(function (data) {
                            if (data.body.a == 0) {
                                wrapper.getData();
                                malert("删除成功")
                            } else {
                                malert("删除失败", 'top', 'defeadted')
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                        return false;
                    }

                    break;
                case 4:
                    var jbZyList = [];
                    var jbZy = {};
                    var removeUrl = "/actionDispatcher.do?reqUrl=BaglBmwhZy&types=delete";
                    if (index != null) {
                        jbZy.jbbm = this.jsonList[index].jbbm
                        jbZyList.push(jbZy);
                    } else {
                        for (var i = 0; i < this.isChecked.length; i++) {
                            if (this.isChecked[i] == true) {
                                jbZy.jbbm = this.jsonList[i].jbbm
                                jbZyList.push(jbZy);
                            }
                        }
                    }
                    if (common.openConfirm("确认删除该条信息吗？", function () {
                        var json = '{"list":' + JSON.stringify(jbZyList) + '}';
                        wrapper.$http.post(removeUrl, json).then(function (data) {
                            if (data.body.a == 0) {
                                wrapper.getData();
                                malert("删除成功")
                            } else {
                                malert("删除失败", 'top', 'defeadted')
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                        return false;
                    }
                    break;
                case 5:
                    var byxList = [];
                    var byx = {};
                    var removeUrl = "/actionDispatcher.do?reqUrl=BaglBmwhByx&types=delete";
                    if (index != null) {
                        byx.byxbm = this.jsonList[index].byxbm
                        byxList.push(byx);
                    } else {
                        for (var i = 0; i < this.isChecked.length; i++) {
                            if (this.isChecked[i] == true) {
                                byx.byxbm = this.jsonList[i].byxbm
                                byxList.push(byx);
                            }
                        }
                    }
                    if (common.openConfirm("确认删除该条信息吗？", function () {
                        var json = '{"list":' + JSON.stringify(byxList) + '}';
                        wrapper.$http.post(removeUrl, json).then(function (data) {
                            if (data.body.a == 0) {
                                wrapper.getData();
                                malert("删除成功")
                            } else {
                                malert("删除失败", 'top', 'defeadted')
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                        return false;
                    }

                    break;
                case 6:

                    break;

            }

        },
        //双击选中科室
        editThis: function (index) {
            //将当前选中的科室保存到病案科室中
            for (var i = 0; i < wrapper.baksList.length; i++) {
                if (this.ksList[index]['ksbm'] == wrapper.baksList[i].ksbm) {
                    malert("该科室已作为病案科室请重新选择");
                    return;
                }
            }
            this.baksContent['ksbm'] = this.ksList[index]['ksbm'];
            this.baksContent['ksmc'] = this.ksList[index]['ksmc'];
            var json = JSON.stringify(this.baksContent);
            this.$http.post("/actionDispatcher.do?reqUrl=BaglBmwhBaks&types=insert&", json).then(function (data) {
                if (data.body.a == 0) {
                    wrapper.getDatas();
                    this.baksContent = {};
                } else {
                    malert("上传数据失败:" + data.body.c);
                }
            });
        },

        //请求后台查询查询病案科室信息
        getDatas: function () {
            this.param.rows = 20000;
            this.param.sort = "ksbm";
            $.getJSON("/actionDispatcher.do?reqUrl=BaglBmwhBaks&types=queryBaks&parm=" + JSON.stringify(this.param),
                function (json) {
                    if (json.a == 0 && json.d) {
                            wrapper.baksList = json.d.list;
                    } else {
                        malert("查询失败：" + json.c);
                    }
                });
        },
        // //双击修改当前行
        // editThis: function (index) {
        //     Vue.set(this.edits, index, true);
        // },
        //编辑修改根据num判断
        edit: function (num) {
            wap.loadNum();
            switch (wrapper.num) {
                case 0:
                    wap.title = '编辑疾病编码';
                    wap.open();
                    wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                    break;
                case 1:
                    wap.title = '编辑手术编码';
                    wap.open();
                    wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                    break;
                case 2:
                    wap.title = '编辑肿瘤形态学编码';
                    wap.open();
                    //这里要拷贝值到popContent中，不能直接=
                    wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                    break;
                case 3:
                    wap.title = '编辑病理诊断编码';
                    wap.open();
                    wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                    break;
                case 4:
                    wap.title = '编辑中医症候编码';
                    wap.open();
                    wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                    break;
                case 5:
                    wap.title = '编辑病原学名称编码';
                    wap.open();
                    wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                    break;
                case 6:
                    // wap.title = '编辑病案科室维护';
                    // wap.open();
                    // wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                    break;
            }

        },
    }
});

var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        num: 1,
        index: 1,
        title: '',
        popContent: {}

    },
    methods: {
        //关闭
        closes: function () {
            this.index = 1

        },
        open: function () {
            this.index = 0
        },
        loadNum: function () {
            this.num = wrapper.num;
        },
        //确定
        confirms: function () {
            //保存
            switch (this.num) {
                case 0:
                    if (wap.popContent.jbmb == '' || wap.popContent.jbmb == null || wap.popContent.jbmb == undefined) {
                        malert('疾病编码不能为空', 'top', 'defeadted');
                        return false;
                    }
                    if (!wap.popContent.jbmc) {
                        malert('疾病名称不能为空', 'top', 'defeadted');
                        return false;
                    }
                    var json = JSON.stringify(wap.popContent);
                    this.$http.post("/actionDispatcher.do?reqUrl=New1BaglBmwhJbbm&types=save", json).then(function (data) {
                        if (data.body.a == 0) {
                            wrapper.getData();
                            if (wap.title == '编辑疾病编码') {
                                wap.closes();
                                malert("保存成功");
                                return;
                            }
                            if (wap.title == '新增疾病编码') {
                                malert("新增成功");
                            }
                            this.popContent = {};
                        } else {
                            malert("上传数据失败");
                        }
                    }, function (error) {
                        console.log(error);
                    });
                    break;
                case 1:
                    if (wap.popContent.ssbm == '' || wap.popContent.ssbm == null || wap.popContent.ssbm == undefined) {
                        malert('手术编码不能为空', 'top', 'defeadted');
                        return false;
                    }
                    if (!wap.popContent.ssmc) {
                        malert('手术名称不能为空', 'top', 'defeadted');
                        return false;
                    }
                    var json = JSON.stringify(wap.popContent);
                    this.$http.post("/actionDispatcher.do?reqUrl=New1BaglBmwhSsbm&types=save", json).then(function (data) {
                        if (data.body.a == 0) {
                            if (wap.title == '编辑手术编码') {
                                wrapper.getData();
                                wap.closes();
                                malert("保存成功");
                                return;
                            }
                            if (wap.title == '新增手术编码') {
                                malert("新增成功");
                            }
                            this.popContent = {};
                        } else {
                            malert("上传数据失败");
                        }
                    }, function (error) {
                        console.log(error);
                    });

                    break;
                case 2:
                    // 提交前验证数据（主要是非空）
                    if (wap.popContent.jbbm == '' || wap.popContent.jbbm == null || wap.popContent.jbbm == undefined) {
                        malert('形态学编码不能为空', 'top', 'defeadted');
                        return false;
                    }
                    if (!wap.popContent.jbmc) {
                        malert('形态学名称不能为空', 'top', 'defeadted');
                        return false;
                    }
                    var json = JSON.stringify(wap.popContent);
                    this.$http.post("/actionDispatcher.do?reqUrl=BaglBmwhM&types=save", json).then(function (data) {
                        if (data.body.a == 0) {
                            if (wap.title == '编辑肿瘤形态学编码') {
                                wrapper.getData();
                                wap.closes();
                                malert("保存成功");
                                return;
                            }
                            if (wap.title == '新增肿瘤形态学编码') {
                                malert("新增成功");
                            }
                            this.popContent = {};
                        } else {
                            malert("上传数据失败");
                        }
                    }, function (error) {
                        console.log(error);
                    });
                    break;
                case 3:
                    // 提交前验证数据（主要是非空）
                    if (wap.popContent.zdbm == '' || wap.popContent.zdbm == null || wap.popContent.zdbm == undefined) {
                        malert('诊断编码不能为空', 'top', 'defeadted');
                        return false;
                    }
                    if (!wap.popContent.zdmc) {
                        malert('诊断名称不能为空', 'top', 'defeadted');
                        return false;
                    }
                    var json = JSON.stringify(wap.popContent);
                    this.$http.post("/actionDispatcher.do?reqUrl=BaglBmwhBlzd&types=save", json).then(function (data) {
                        if (data.body.a == 0) {
                            if (wap.title == '编辑病理诊断编码') {
                                wrapper.getData();
                                wap.closes();
                                malert("保存成功");
                                return;
                            }
                            if (wap.title == '新增病理诊断编码') {
                                malert("新增成功");
                            }
                            this.popContent = {};
                        } else {
                            malert("上传数据失败");
                        }
                    }, function (error) {
                        console.log(error);
                    });
                    break;
                case 4:
                    // 提交前验证数据（主要是非空）
                    if (wap.popContent.jbbm == '' || wap.popContent.jbbm == null || wap.popContent.jbbm == undefined) {
                        malert('疾病编码不能为空', 'top', 'defeadted');
                        return false;
                    }
                    if (!wap.popContent.jbmc) {
                        malert('疾病名称不能为空', 'top', 'defeadted');
                        return false;
                    }
                    var json = JSON.stringify(wap.popContent);
                    this.$http.post("/actionDispatcher.do?reqUrl=BaglBmwhZy&types=save", json).then(function (data) {
                        if (data.body.a == 0) {
                            if (wap.title == '编辑中医症候编码') {
                                wrapper.getData();
                                wap.closes();
                                malert("保存成功");
                                return;
                            }
                            if (wap.title == '新增中医症候编码') {
                                malert("新增成功");
                            }
                            this.popContent = {};
                        } else {
                            malert("上传数据失败");
                        }
                    }, function (error) {
                        console.log(error);
                    });
                    break;
                case 5:
                    // 提交前验证数据（主要是非空）
                    if (wap.popContent.byxbm == '' || wap.popContent.byxbm == null || wap.popContent.byxbm == undefined) {
                        malert('病原学编码不能为空', 'top', 'defeadted');
                        return false;
                    }
                    if (wap.popContent.byxmc == '' || wap.popContent.byxmc == null || wap.popContent.byxmc == undefined) {
                        malert('病原学名称不能为空', 'top', 'defeadted');
                        return false;
                    }
                    var json = JSON.stringify(wap.popContent);
                    this.$http.post("/actionDispatcher.do?reqUrl=BaglBmwhByx&types=save", json).then(function (data) {
                        if (data.body.a == 0) {
                            if (wap.title == '编辑') {
                                wrapper.getData();
                                wap.closes();
                                malert("保存成功");
                                return;
                            }
                            if (wap.title == '新增') {
                                malert("新增成功");
                            }
                            this.popContent = {};
                        } else {
                            malert("上传数据失败");
                        }
                    }, function (error) {
                        console.log(error);
                    });

                    break;
                case 6:

                    break;
            }
        },
    }


});


$('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function () {
    if ($(this).val() == '' || $(this).val() == null) {
        $(this).addClass("emptyError");
    } else {
        $(this).removeClass("emptyError");
    }
});




