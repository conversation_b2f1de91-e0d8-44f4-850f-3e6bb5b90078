<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>词库模板</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ckmb.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">添加</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
            <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view ybglTable" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="jm20" z-width="80px">
                        <div class="zui-table-cell">模板id</div>
                    </th>
                    <th z-field="jm21" z-width="80px">
                        <div class="zui-table-cell">可见范围</div>
                    </th>
                    <th z-field="jm22" z-width="100px">
                        <div class="zui-table-cell">使用范围</div>
                    </th>
                    <th z-field="jm23" z-width="100px">
                        <div class="zui-table-cell">名称</div>
                    </th>
                    <th z-field="jm24" z-width="100px">
                        <div class="zui-table-cell">父节点</div>
                    </th>
                    <th z-field="jm25" z-width="100px">
                        <div class="zui-table-cell">拼音简码</div>
                    </th>
                    <th z-field="jm1" z-width="100px">
                        <div class="zui-table-cell">科室id</div>
                    </th>
                    <th z-field="jm2" z-width="100px">
                        <div class="zui-table-cell">科室名称号</div>
                    </th>
                    <th z-field="jm3" z-width="100px">
                        <div class="zui-table-cell">排列序号</div>
                    </th>
                    <th z-field="jm4" z-width="100px">
                        <div class="zui-table-cell">用户id</div>
                    </th>
                    <th z-field="jm5" z-width="100px">
                        <div class="zui-table-cell">用户名称</div>
                    </th>
                    <th z-field="jm6" z-width="100px">
                        <div class="zui-table-cell">模板内容</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-height" id="zui-table">
            <table class="zui-table table-width50">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jsonList"  @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.ckmbid"></div></td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="ckkjfw[item.ckkjfw]"></i>
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="cksyfw[item.cksyfw]"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.kbName"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.kbParent"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.kbPyjm"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ksid"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ksmc" ></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.listindex"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.yhid"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.yhmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width"  style="width: 100px !important;" v-text="item.kbMbnr"></i>
                        </div>
                    </td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>模板id</i>
                    <input type="text" class="label-input background-h" disabled v-model="popContent.ckmbid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>可见范围</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="ckkjfw" :index="popContent.ckkjfw" :val="popContent.ckkjfw"
                                  :name="'popContent.ckkjfw'">
                    </select-input>
            </li>
            <li>
                    <i>使用范围</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="cksyfw" :index="popContent.cksyfw" :val="popContent.cksyfw"
                                  :name="'popContent.cksyfw'">
                    </select-input>
            </li>
            <li>
                    <i>名称</i>
                    <input type="text" class="zui-input border-r4"  v-model="popContent.kbName"  @keydown="nextFocus($event)"
                           @blur="setPYDM(popContent.kbName,'popContent','kbPyjm')" />
            </li>
            <li>
                    <i>父节点</i>
                    <input type="text" class="label-input"  v-model="popContent.kbParent" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>拼音简码</i>
                    <input type="text" class="label-input background-h"  disabled v-model="popContent.kbPyjm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>词库模板序号</i>
                    <input type="text" class="label-input"  v-model="popContent.kbSeq" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>科室id</i>
                    <input type="text" class="label-input background-h"  disabled v-model="popContent.ksid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>科室名称号</i>
                    <input type="text" class="label-input"  v-model="popContent.ksmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>排列序号</i>
                    <input type="text" class="label-input"  v-model="popContent.listindex" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>父节点名称</i>
                    <input type="text" class="label-input"  v-model="popContent.parentmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>用户id</i>
                    <input type="text" class="label-input background-h"  disabled v-model="popContent.yhid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>用户名称</i>
                    <input type="text" class="label-input"   v-model="popContent.yhmc" @keydown="nextFocus($event)"/>
            </li>
            <li class="width100">
                    <i class="width150">模板内容</i>
                    <textarea  class="label-input dz-height"  v-model="popContent.kbMbnr" @keydown="nextFocus($event)"></textarea>
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ckmb.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>