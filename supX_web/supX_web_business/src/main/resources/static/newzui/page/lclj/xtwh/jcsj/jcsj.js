$(".zui-table-view").uitable();
/**
 * 顶部工具栏信息
 */
var jcsj = new Vue({
    el: '#jcsj',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        num: 0,
        searchMessage: false, //检索栏是否显示
    },
    methods: {
    	//新增
        addData: function () {
        	brzcList.index = 0;
        	brzcList.isEditOrAdd=0;
            if (table.num == 0) { //科室对应
                brzcList.ksdyPopContent = {};
                brzcList.title = '新增科室对应';
                brzcList.num=0;
            }else if(table.num==1){ //病种对应
            	brzcList.bzdyPopContent = {};
                brzcList.title = '新增病种对应';
                brzcList.num=1;
            }
        },
        //刷新
        getData: function () {
        	if (table.num == 0) { //科室对应
        		jcsj.searchMessage=false; //检索栏是否显示
            	table.getDataKsdy(); //查询
            }else if(table.num==1){ //病种对应
            	
            }
        },
        //保存
        save: function () {

        },
        //删除
        remove: function () {
            table.remove()
        },
        //过滤
        gl: function () {
            isTabel.isShow = true;
        },
        //预览
        yl: function () {

        },
        //打印
        dy: function () {

        },
        
    },
});

/**
 * 主体表格栏
 */
var table = new Vue({
    el: '.ybglTable',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        num: 0,
        ksdyJsonList: [], //科室对应列表
        bzdyJsonList: [], //病种对应列表
        jbdyLjJsonList: [], //疾病编码对应中路径列表
        jbdyJbJsonList: [], //疾病编码对应中疾病列表
        jbdyJgJsonList: [], //疾病编码对应中结果列表
        ssdyJgJsonList: [], //手术编码对应中结果列表
        bbdyBbJsonList: [], //报表对应中报表列表
        bbdyJgJsonList: [], //报表对应中结果列表
        
        ryObj: {},
        parmsC: {
            ryljbm: '',
            parm: '',
        },
        param: {
            page: 1,
            rows: 10,
            sort: '',
            order: 'asc'
        },
    }
    ,
    created: function () {
       
    },
    methods: {
    	//导航栏切换
        tabBg: function (index) {
            jcsj.num = index
            this.num = index;
            this.param.page = 1;
            this.isChecked = [];
            this.isCheckedall = false;
            this.getData();
            brzcList.closes();
            
        },
        getData:function(){
        	switch (this.num) {
            case 0:
            	jcsj.searchMessage=false; //检索栏是否显示
            	table.getDataKsdy(); //查询
                break;
            case 1:
            	jcsj.searchMessage=false;
            	table.getDataBzdy(); //查询
                break;
            case 2:
            	jcsj.searchMessage=true;
                break;
            case 3:
            	jcsj.searchMessage=true;
                break;
            case 4:
            	jcsj.searchMessage=true;
                break;
        }
        },
        /**
         * 1.查询科室对应集合
         */
         getDataKsdy: function () {
        	 var that = this
        	common.openloading('.zui-table-body')
            $.getJSON("/actionDispatcher.do?reqUrl=New1LcljXtwhBdwhysSyks&types=queryall&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                	that.totlePage = Math.ceil(json.d.total / that.param.rows);
                    table.ksdyJsonList = json.d.list;
                    common.closeLoading()
                }else {
                	malert("获取科室对应集合失败！"+json.c ,'top','defeadted');
                }
            });
        },
        
        /**
         * 2.查询病种对应
         */
        getDataBzdy: function(){
        	var that = this
        	common.openloading('.zui-table-body')
            $.getJSON("/actionDispatcher.do?reqUrl=New1LcljXtwhBdwhysSybz&types=queryall&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                	that.totlePage = Math.ceil(json.d.total / that.param.rows);
                    table.bzdyJsonList = json.d.list;
                    common.closeLoading()
                }else {
                	malert("获取病种对应集合失败！"+json.c ,'top','defeadted');
                }
            });
        },
        
        /**
         * 编辑信息
         */
        bj: function (item) {
        	brzcList.index = 0;
        	brzcList.isEditOrAdd=1;
        	if(this.num==0){ // 科室对应
        		brzcList.ksdyPopContent= item;
	            brzcList.num=0;
	            brzcList.title = '编辑科室对应';
        	}else if(this.num==1){ //病种对应
        		brzcList.bzdyPopContent= item;
	            brzcList.num=1;
	            brzcList.title = '编辑病种对应';
        	}
        },
        /**
         * 删除信息
         */
        remove: function (item) {
            var daTa = []
            if (this.num == 0) {
                if (!item && this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            this.ryObj.ryljbm = table.ksdyJsonList[i].ryljbm
                            this.ryObj.ryksbm = table.ksdyJsonList[i].ryksbm
                            daTa.push(JSON.parse(JSON.stringify(this.ryObj)))
                        }
                    }
                } else {
                    this.ryObj.ryljbm = item.ryljbm
                    this.ryObj.ryksbm = item.ryksbm
                    daTa.push(this.ryObj)
                }
                
                var url = '/actionDispatcher.do?reqUrl=New1LcljXtwhBdwhysSyks&types=delete'
            } else if (this.num == 1) {
            	if (!item && this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            this.ryObj.id = table.bzdyJsonList[i].id;
                            daTa.push(JSON.parse(JSON.stringify(this.ryObj)));
                        }
                    }
                } else {
                    this.ryObj.id = item.id;
                    daTa.push(this.ryObj);
                }             
                var url = '/actionDispatcher.do?reqUrl=New1LcljXtwhBdwhysSybz&types=delete'
            }

            var json = '{"list":' + JSON.stringify(daTa) + '}';
            this.$http.post(url, json).then(function (data) {
                    if (data.body.a == 0) {
                    	table.getData();
                    	this.isChecked = [];
                        malert(data.body.c, 'top', 'success');
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                },
                function (error) {
                    malert(error, 'top', 'defeadted');
                });
        },
    },
});

/**
 * 右侧弹框部分
 */
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    components: {
        'search-table': searchTable,
        'search-table1': searchTable,
    },
    data: {
        index: 1,
        num: 0,
        title: '',
        ksdyPopContent: {}, //科室对应对象
        bzdyPopContent: {}, //病种对应对象
        ljJsonList: [], //路径下拉框
        ksJsonList: [], //科室下拉框
        jbJsonList: [], //疾病下拉框
        ifClick: true, //判断有没有点击这个按钮
        isEditOrAdd: 0, //判断是编辑
        ryObj: {},
        
        page: {
        	parm:'',
            page: 1,
            rows: 10,
            total: null
        },
        them: { // 检索的标题字段
        	'疾病编码': 'jbmb',
            '疾病名称': 'jbmc'
        },
        searchCon: [],
        popContent: {}, //暂存下拉table选中对象
    },
    created: function () {
       
    },
    methods: {
    	// 1.查询路径下拉框
        getLj: function(){
        	this.param.rows = 20000;
            $.getJSON("/actionDispatcher.do?reqUrl=New1LcljXtwhBdwhbz&types=queryall&parm=" + JSON.stringify(this.param), function (json) {
                 if (json.a == 0) {
                    brzcList.ljJsonList = json.d.list;
                } else{
                    malert('路径列表查询失败'+json.c,'top','defeadted');

                }
            });
        },
        // 2.查询科室下拉框
        getKs: function(){
        	var bean = {"zyks": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    brzcList.ksJsonList = json.d.list;
                } else{
                    malert('住院科室列表查询失败'+json.c,'top','defeadted');

                }
            });
        },
        // 3.保存
        confirms: function () {
        	if (!brzcList.ifClick) return; //如果为false表示已经点击了不能再点
            brzcList.ifClick = false;
            if(this.num==0){//科室对应
            	//非空判断
            	if(!this.ksdyPopContent.ryljbm){
            		malert('路径不能为空','top','defeadted');
            		brzcList.ifClick = true;
            		return false;
            	}else if(!this.ksdyPopContent.ryksbm){
            		malert('科室不能为空','top','defeadted');
            		brzcList.ifClick = true;
            		return false;
            	}
            	//获取名名称
            	this.ksdyPopContent.ryljmc = brzcList.listGetName(brzcList.ljJsonList, brzcList.ksdyPopContent.ryljbm, 'ljbm', 'ljmc');
            	this.ksdyPopContent.ryksmc = brzcList.listGetName(brzcList.ksJsonList, brzcList.ksdyPopContent.ryksbm, 'ksbm', 'ksmc');
            	//判断是新增还是修改
            	var type = brzcList.isEditOrAdd==0 ? 'save' : 'update';
	            this.$http.post('/actionDispatcher.do?reqUrl=New1LcljXtwhBdwhysSyks&types='+type+'&', JSON.stringify(this.ksdyPopContent)).then(function (data) {
	                if (data.body.a == 0) {
	                    malert('上传数据成功','top','success');
	                    brzcList.ifClick = true;
	                    table.getDataKsdy(); //查询
	                } else {
	                	if(type == 'update'){
	                		table.getDataKsdy(); //查询
	                	}
	                    malert('上传数据失败'+data.body.c,'top','defeadted');
	                    brzcList.ifClick = true;
	                }
	            }, function (error) {
	                console.log(error);
	            });
            }else if(this.num==1){ //病种对应
            	//非空判断
            	if(!brzcList.bzdyPopContent.ryljbm){
            		malert('路径不能为空','top','defeadted');
            		brzcList.ifClick = true;
            		return false;
            	}else if(!brzcList.bzdyPopContent.ryjbbm){
            		malert('疾病编码不能为空','top','defeadted');
            		brzcList.ifClick = true;
            		return false;
            	}
            	brzcList.bzdyPopContent.ryljmc = brzcList.listGetName(brzcList.ljJsonList, brzcList.bzdyPopContent.ryljbm, 'ljbm', 'ljmc');
            	//判断是新增还是修改
            	var type = brzcList.isEditOrAdd==0 ? 'save' : 'update';
	            this.$http.post('/actionDispatcher.do?reqUrl=New1LcljXtwhBdwhysSybz&types='+type+'&', JSON.stringify(this.bzdyPopContent)).then(function (data) {
	                if (data.body.a == 0) {
	                    malert('上传数据成功','top','success');
	                    brzcList.ifClick = true;
	                    table.getDataBzdy(); //查询
	                } else {
	                	if(type == 'update'){
	                		table.getDataBzdy(); //查询
	                	}
	                    malert('上传数据失败'+data.body.c,'top','defeadted');
	                    brzcList.ifClick = true;
	                }
	            }, function (error) {
	                console.log(error);
	            });
            	
            }
        },
        
        //关闭弹窗
        closes: function () {
            this.index = 1;
            brzcList.ifClick = true;
        },
        
        //查询疾病下拉框
        searching: function (add, val) {
        	this.page.parm = val;
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);

            $.getJSON("/actionDispatcher.do?reqUrl=New1BaglBmwhJbbm&types=queryBaglJbbm&parm="+JSON.stringify(this.page),function (json) {
            	if (json.a == 0) {
	            	if (json.d.list == null || json.d.list == "") {
	                    $(".selectGroup").hide();
	                }
	            	if (add) {
	                    for (var i = 0; i < json.d.list.length; i++) {
	                    	brzcList.searchCon.push(json.d.list[i]);
	                    }
	                } else {
	                	brzcList.searchCon = json.d.list;
	                }
	            	brzcList.page.total = json.d.total;
	            	brzcList.selSearch = 0;
	                if (json.d.list.length > 0 && !add) {
	                    $(".selectGroup").hide();
	                    _searchEvent.show();
	                }
            	}else{
            		malert("查询失败  " + json.c, 'top', 'deteadted');
            	}   
    		});
        },
        
        //回车
        changeDown: function (event,mc,bm) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'json', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
            	brzcList.bzdyPopContent[bm] = this.json.jbmb;
            	brzcList.bzdyPopContent[mc] = this.json.jbmc;
            	this.prevFocus(event);
            }
        },
        
        //鼠标双击（开始疾病名称）
        selectOne: function (item) {
        	this.popContent = item;
            if (item == null) {
                this.page.page++;
                this.searching(true, this.page.parm);
            } else {
                $(".selectGroup").hide();
                Vue.set(brzcList.bzdyPopContent, 'ryjbbm', this.popContent.jbmb);
                Vue.set(brzcList.bzdyPopContent, 'ryjbmc', this.popContent.jbmc);
            }
        },
        
        //鼠标双击（结束疾病名称）
//        selectOne1: function (item) {
//        	this.popContent = item;
//            if (item == null) {
//                this.page.page++;
//                this.searching(true, this.page.parm);
//            } else {
//                $(".selectGroup").hide();
//                Vue.set(brzcList.bzdyPopContent, 'ryjsjbbm', this.popContent.jbmb);
//                Vue.set(brzcList.bzdyPopContent, 'ryjsjbmc', this.popContent.jbmc);
//            }
//        },
    },
});

//过滤页面
var isTabel = new Vue({
    el: '#isTabel',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        isTabelShow: false,
        isShow: false,
        minishow: true,
        isShowpopL: false,
        popContent: {},
        item: 0,
        appNum: [],
        appObj: {},

        cxtjList: [],
        util: {},
        isLxNum: '',
        isTyNum: '',
        isXbNum: '',
        isKsNum: '',
        isYsNum: '',
        isYblxNum: '',
    },
    created: function () {
        this.append()
    },
    methods: {
        resultChange_item: function (val) {
            console.log(val);
            var index = val[2][0];
            if (val[0] == 'LX') {//类型
                var pd = index + ',';
                this.isLxNum += pd;
            }
            if (val[0] == 'BAH' || val[0] == 'BRXM' || val[0] == 'NL' || val[0] == 'FYMC' || val[0] == 'XZ') {//通用
                var pd = index + ',';
                this.isTyNum += pd;
            }
            if (val[0] == 'XB') {//性别
                var pd = index + ',';
                this.isXbNum += pd;
            }
            if (val[0] == 'KSBM') {//科室
                var pd = index + ',';
                this.isKsNum += pd;
            }
            if (val[0] == 'SQYS') {//医生
                var pd = index + ',';
                this.isYsNum += pd;
            }
            if (val[0] == 'YBLX') {//样本类型
                var pd = index + ',';
                this.isYblxNum += pd;
            }

            Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
            if (event.keyCode != null) {
                this.nextFocus(val[1], parseInt(val[2][2]));
            }
        },
        resultChangeTj_item: function (val) {
            Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
            if (event.keyCode != null) {
                this.nextFocus(val[1], parseInt(val[2][2]));
            }
        },
        resultChangeLjtj_item: function (val) {
            Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
            if (event.keyCode != null) {
                this.nextFocus(val[1], parseInt(val[2][2]));
            }
        },
        Wf_YppfChange: function (val) {
            var index = "";
            //先获取到操作的哪一个
            if (typeof val == 'object') {//判断是否是属于对象（下拉框）
                var types = val[2][val[2].length - 1];
                index = val[2][1];
                Vue.set(this.cxtjList[index], 'jg', val[0]);
            }

            if (typeof val == 'object') {//判断是否是属于对象（下拉框）
                if (window.event.keyCode == 13) {
                    this.nextFocus(event);
                }
            }
        },
        Wf_YsChange: function (val) {
            var index = "";
            //先获取到操作的哪一个
            if (typeof val == 'object') {//判断是否是属于对象（下拉框）
                var types = val[2][val[2].length - 1];
                index = val[2][1];
                Vue.set(this.cxtjList[index], 'jg', val[0]);
            }

            if (typeof val == 'object') {//判断是否是属于对象（下拉框）
                if (window.event.keyCode == 13) {
                    this.nextFocus(event);
                }
            }
        },

        save: function () {
            console.log(this.cxtjList);
            for (var int = 0; int < this.cxtjList.length; int++) {
                if (wrapper.param.time != null) {
                    var times = wrapper.param.time.split(" - ");
                    this.cxtjList[int].starttime = times[0];
                    this.cxtjList[int].endtime = times[1];
                }
                this.cxtjList[int].type = '1'
            }
            var json = '{"list":' + JSON.stringify(this.cxtjList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=gjcx', json).then(
                function (data) {
                    console.log(data);
                    if (data.body.a == 0) {
                        wap.jydjList = data.body.d.list;
                        malert('查询成功', 'top', 'success');
                    } else {
                        malert('查询失败，请检查参数是否正确', 'top', 'defeadted');
                    }

                },
                function (error) {
                    malert(error, 'top', 'defeadted');
                });
        },


        sc: function (index) {
            this.cxtjList.splice(index, 1)
            // for(var i=0;i<this.appNum.length;i++){
            //     if(this.appNum[i].num==index){
            //
            //     }
            // }

        },
        append: function () {
            this.cxtjList.push({});
            /* this.item = this.item + 1
             this.appObj.num=this.item
             this.appNum.push(JSON.parse(JSON.stringify(this.appObj)))
            this.$nextTick(function () {
                $(".zui-form .lsittext").uicomplete({
                    iskeyup: true
                });
            })*/
        },
        tabshow: function () {
            this.isTabelShow = true;
            this.minishow = false;
            pop.dyShow = false;
        },
        showDom: function () {
            pop.isShowpopL = true;
            pop.isShow = true;
            pop.dyShow = false;
            pop.flag = true;
        }
    },
});

//页面加载完成查询数据
brzcList.getLj(); // 初始化加载路径列表
brzcList.getKs(); // 初始化加载科室列表
table.getDataKsdy(); // 查询科室对应
