<!DOCTYPE html>
<html lang="en">
<head>
    <link href="userPage/user.css" rel="stylesheet">
</head>
<style>

    canvas {
        cursor: default;
    }

    table {
        border-collapse: collapse;
        /*overflow: hidden;*/
        font-size: 12px;
        border: 0;
    }

    .twd table tr td:first-child {
        padding-left: 4px;
        border-left: 0;
    }

    .twd td {
        height: 14px;
        border-left: 1px solid #555;
        border-bottom: 1px solid #555;
    }

</style>
<body>
<div class="wrapper flex-container flex-dir-c flex-one">
    <div class="panel padd-b-40">

        <div class="flex-container">
            <div class="flex-container   flex-align-c  margin-l-10 margin-top-5">
                <span class="whiteSpace margin-r-5 ft-14">时间段</span>
                <input class="zui-input wh200  todate" v-model="time" id="time" type="text"/>
            </div>
            <div class="flex-container  margin-top-5 flex-align-c padd-l-10">
                <span class="margin-r-10 whiteSpace">体温单筛选</span>
                <select-input class="wh120" @change-data="commonResultChange"
                              :child="qsxzList" :index="'yexm'" :index_val="'yebh'" :val="popContent.yebh"
                              :name="'popContent.yebh'" :index_mc="'yexm'" search="true">
                </select-input>
            </div>
        </div>
    </div>
    <div class="flex-container flex-one  flex-align-c flex-jus-c">
        <div class="paev fa-angle-left" onclick="prev()"></div>
        <div class="twd-scroll">
            <div class="twd">
                <section style="width:676px;margin:0;border: 2px solid #000000">
                    <table id="table_1" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>日期</td>
                            <td v-for="item in currentDay" v-text="item"></td>
                        </tr>
                        <tr>
                            <td>住院天数</td>
                            <td v-for="item in zyDay" v-text="item"></td>
                        </tr>
                        <tr>
                            <td>手术后天数</td>
                            <td v-for="item in 7"></td>
                        </tr>
                    </table>
                    <canvas id="twd_title"></canvas>
                    <canvas id="twd_cvs" style="margin-top: -1px"></canvas>
                    <table id="table_2" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>呼吸(次/分)</td>
                            <td :class="[('setClass'+index)]" v-for="(item,index) in hx_list" v-text="item"></td>
                        </tr>
                    </table>
                    <table id="table_3" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>血压(mmHg)</td>
                            <td v-for="item in xy_list" v-text="item"></td>
                        </tr>
                    </table>
                    <table id="table_4" cellpadding="0" cellspacing="0">
                        <tr v-if="N03004200239[0]!='0'">
                            <td class="text-center">输入量(ml)</td>
                            <td v-for="item in rlList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[1]!='0'">
                            <td class="text-center">引入量(ml)</td>
                            <td v-for="item in yrList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[2]!='0'">
                            <td class="text-center">引流量(ml)</td>
                            <td v-for="item in ylList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[3]!='0'">
                            <td class="text-center">大便(次/日)</td>
                            <td v-for="item in dbList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[4]!='0'">
                            <td class="text-center">小便(次)</td>
                            <td v-for="item in xbcList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[5]!='0'">
                            <td class="text-center">尿量</td>
                            <td v-for="item in xbList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[6]!='0'">
                            <td class="text-center">体重(kg)</td>
                            <td v-for="item in tzList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[7]!='0'">
                            <td class="text-center">身高(cm)</td>
                            <td v-for="item in sgList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[8]!='0'">
                            <td class="text-center">脉象</td>
                            <td v-for="item in mxList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[9]!='0'">
                            <td class="text-center">基础代谢</td>
                            <td v-for="item in jcdxList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[10]!='0'">
                            <td class="text-center">灌肠</td>
                            <td v-for="item in gcList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[11]!='0'">
                            <td class="text-center">灌肠前大便</td>
                            <td v-for="item in gcqdbcsList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[12]!='0'">
                            <td class="text-center">导尿</td>
                            <td v-for="item in bldnList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[0]">
                            <td class="text-center">{{qtxmList[0]}}</td>
                            <td v-for="item in qtxm1List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[1]">
                            <td class="text-center">{{qtxmList[1]}}</td>
                            <td v-for="item in qtxm2List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[2]">
                            <td class="text-center">{{qtxmList[2]}}</td>
                            <td v-for="item in qtxm3List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[3]">
                            <td class="text-center">{{qtxmList[3]}}</td>
                            <td v-for="item in qtxm4List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[4]">
                            <td class="text-center">{{qtxmList[4]}}</td>
                            <td v-for="item in qtxm5List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[5]">
                            <td class="text-center">{{qtxmList[5]}}</td>
                            <td v-for="item in qtxm6List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[6]">
                            <td class="text-center">{{qtxmList[6]}}</td>
                            <td v-for="item in qtxm7List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[7]">
                            <td class="text-center">{{qtxmList[7]}}</td>
                            <td v-for="item in qtxm8List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[8]">
                            <td class="text-center">{{qtxmList[8]}}</td>
                            <td v-for="item in qtxm9List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[9]">
                            <td class="text-center">{{qtxmList[9]}}</td>
                            <td v-for="item in qtxm10List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[10]">
                            <td class="text-center">{{qtxmList[10]}}</td>
                            <td v-for="item in qtxm11List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="qtxmList && qtxmList[11]">
                            <td class="text-center">{{qtxmList[11]}}</td>
                            <td v-for="item in qtxm12List" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[13]!='0'">
                            <td rowspan="2" class="text-center ">
                                <div class="flex-container height100 flex-align-c">
                                    <p class="text-center flex-container col-fm-5 flex-align-c flex-jus-c">舌</p>
                                    <div class="st_border">
                                        <p class="text-center st_border_bottom">苔</p>
                                        <p class="text-center">质</p>
                                    </div>
                                </div>
                            </td>
                            <td rowspan="1" v-for="item in stList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-if="N03004200239[14]!='0'">
                            <td rowspan="1" v-for="item in szList" class="text-center" v-text="item"></td>
                        </tr>
                        <tr v-for="item in 2">
                            <td class="text-center"></td>
                            <td class="text-center" v-for="item in 7"></td>
                        </tr>
                    </table>
                </section>
            </div>
        </div>
        <div class="next fa-angle-right" onclick="next()"></div>
    </div>
</div>
<script type="text/javascript" src="userPage/twd.js"></script>
</body>
</html>
