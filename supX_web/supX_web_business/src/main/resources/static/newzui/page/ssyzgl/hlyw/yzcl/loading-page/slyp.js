var shyz = new Vue({
    el: '#loadingPage',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
    	brlistjson:{},//只用于接受请求LIST对象
        brList:[],
        yzList: [],
        jkList:[],
        ypslInfoList:[],//真正的列表
        zyhs:[],
        ksid:null,//科室编码
        caqxContent:null,//参数权限对象
        ifClick:true,
        isOver: false,//是否全选
        hoverBrListIndex: undefined,
    },
    mounted: function () {
        this.moun();
        window.addEventListener('storage',function (e) {
            if( e.key == 'slyp' && e.oldValue !== e.newValue ){
                shyz.zyhs = [];
                shyz.moun();
            }
        });
    },
    updated: function () {
        changeWin();
    },
    methods: {
        closePage: function () { //关闭本页面
            var x = parseInt( sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem( 'hszHzlbUpdate' , x );
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/slyp.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        },
        isOverClick: function (isOver) {
            this.isOver = isOver || !this.isOver;
            this.ypslInfoList.forEach(function (br) {
                br.isCheckAll = shyz.isOver;
                br.yzxx.forEach(function (yz) {
                    yz.isChecked = shyz.isOver;
                });
            });
        },
        moun: function () {
            this.brlistjson=JSON.parse( sessionStorage.getItem( 'slyp' ) );
            this.brList =this.brlistjson.brlist;
            this.ksid=this.brlistjson.ksid;
            this.caqxContent=this.brlistjson.csqx;
            for(var i=0;i<this.brList.length;i++){
                var zyh={
                    zyh:this.brList[i].zyh
                };
                this.zyhs.push(zyh);
            }
            this.initSlData();
        },
    	//重写选中
  	    checkSelectSl:function (brIndex,yzIndex) {
            var yzStatus = !this.ypslInfoList[ brIndex ].yzxx[ yzIndex ].isChecked;
            this.ypslInfoList[ brIndex ].yzxx[ yzIndex ].isChecked = yzStatus;
            if( yzStatus ){

                var yzIsOverCk = true;
                for ( var x = 0; x < this.ypslInfoList[ brIndex ].yzxx.length; x++ ){
                    if( !this.ypslInfoList[ brIndex ].yzxx[ x ].isChecked ){
                        yzIsOverCk = false;
                        break;
                    }
                }
                this.ypslInfoList[ brIndex ].isCheckAll = yzIsOverCk;

                var isOverCk = true;
                for ( var x = 0; x < this.ypslInfoList.length; x++ ){
                    if( !this.ypslInfoList[x].isCheckAll ){
                        isOverCk = false;
                        break;
                    }
                }
                this.isOver = isOverCk;

            }else {
                this.ypslInfoList[ brIndex ].isCheckAll = false;
                this.isOver = false;
            }
        },
        //重写选中
  	  reCheckBoxSl: function () {
  		  if( arguments.length == 1 ){
  		      var isCheckAll = this.ypslInfoList[arguments[0]].isCheckAll? false:true,
                    yzshInfo = this.ypslInfoList[arguments[0]],
                    yzxxList = yzshInfo.yzxx;

                this.ypslInfoList[arguments[0]].isCheckAll = isCheckAll;
                for ( var i = 0; i < yzxxList.length; i++ ){
                    this.ypslInfoList[arguments[0]].yzxx[i].isChecked = isCheckAll;
                }
            }else if(arguments.length == 2){
              this.activeBrListIndex = arguments[0];
              this.activeIndex = arguments[1];
                // var isChecked = this.ypslInfoList[arguments[0]].yzxx[arguments[1]].isChecked? false:true,
                //     yzshInfo = this.ypslInfoList[arguments[0]],
                //     yzxxList = yzshInfo.yzxx,
                //     isCheckAll = true;
                //
                // this.ypslInfoList[arguments[0]].yzxx[arguments[1]].isChecked = isChecked;
                // for ( var y = 0; y < yzxxList.length; y++ ){
                //     if( !yzxxList[y].isChecked ){
                //         this.ypslInfoList[arguments[0]].isCheckAll = false;
                //         isCheckAll = false;
                //         break;
                //     }
                // }
                // if( isCheckAll ) this.ypslInfoList[arguments[0]].isCheckAll = true;
            }

          var isOverCk = true;
          for ( var x = 0; x < this.ypslInfoList.length; x++ ){
              if( !this.ypslInfoList[x].isCheckAll ){
                  isOverCk = false;
                  break;
              }
          }
          this.isOver = isOverCk;

            this.$forceUpdate();
            // console.log("-------------------------------");
            // console.log(this.ypslInfoList);
        },
        ypsl: function () {
            tspop.open();
        },

        //取消执行
        qxzx:function(){
        	  var qxzxData=[];
        	  if (!shyz.ifClick) return; //如果为false表示已经点击了不能再点
              shyz.ifClick = false;
              if (shyz.caqxContent.cs00900100110 == '0') {
                  malert("对不起，您无权取消执行");
                  shyz.ifClick = true;
                  return
              }
              for (var i = 0; i < shyz.ypslInfoList.length; i++) {
                  for (var j = 0; j < shyz.ypslInfoList[i]['yzxx'].length; j++) {
                	  if (shyz.ypslInfoList[i]['yzxx'][j].isChecked) {
                		  qxzxData.push({"zxlsh": shyz.ypslInfoList[i]['yzxx'][j].ypzxlsh});
                          }
                  }
              }
              if(qxzxData.length <= 0) {
                  malert("无医嘱取消执行！");
                  shyz.ifClick = true;
                  return;
              }
              this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=qxyzzx&ksbm=' + shyz.ksid,
                  JSON.stringify({list: qxzxData})).then(function (data) {
                  if (data.body.a == 0) {
                      malert("取消执行成功");
                      shyz.ifClick = true;
                      shyz.initSlData();//刷新
                  } else {
                      malert("取消执行失败");
                      shyz.ifClick = true;
                  }
              }, function (error) {
                  console.log(error);
              });
        },

        //获取药品申领信息
        initSlData:function(){
        	  this.ypslInfoList = [];
              if (this.zyhs.length == 0) {
                  malert("请选择病人后再进行此操作！");
                  return
              }
              var zyh = JSON.stringify(this.zyhs);
              $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=ypsl&ksbm=' +  this.ksid + '&zyh=' + zyh,
                  function (json) {
                      console.log("list:" + json.d.list);
                      if (json.d.list.length > 0) {
                          for (var i = 0; i < json.d.list.length; i++) {
                              for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                  json.d.list[i].yzxx[int].no = i;
                              }
                          }
                      }
                      shyz.ypslInfoList = json.d.list;
                  	for (var k = 0; k < shyz.ypslInfoList.length; k++) {
                    	//判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                    	if(shyz.ypslInfoList[k].nl<7&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='1';
                    	}else if(shyz.ypslInfoList[k].nl<7&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='2';
                    	}else if(shyz.ypslInfoList[k].nl<18&&shyz.ypslInfoList[k].nl>6&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='3';
                    	}else if(shyz.ypslInfoList[k].nl<18&&shyz.ypslInfoList[k].nl>6&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='4';
                    	}else if(shyz.ypslInfoList[k].nl<41&&shyz.ypslInfoList[k].nl>17&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='5';
                    	}else if(shyz.ypslInfoList[k].nl<41&&shyz.ypslInfoList[k].nl>17&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='6';
                    	}else if(shyz.ypslInfoList[k].nl<66&&shyz.ypslInfoList[k].nl>40&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='7';
                    	}else if(shyz.ypslInfoList[k].nl<66&&shyz.ypslInfoList[k].nl>40&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='8';
                    	}else if(shyz.ypslInfoList[k].nl>65&&shyz.ypslInfoList[k].brxb=='1'){
                    		shyz.ypslInfoList[k].nljd='9';
                    	}else if(shyz.ypslInfoList[k].nl>65&&shyz.ypslInfoList[k].brxb=='2'){
                    		shyz.ypslInfoList[k].nljd='10';
                    	}else{
                            shyz.ypslInfoList[k].nljd='11';
                        }
                    }
                      if( shyz.ypslInfoList.length > 1 ){
                          shyz.isOverClick(true);
                      }
//                      YZInfo.jsonList = YZInfo.ypslInfoList;
                  });
        },
    },
});
var tspop = new Vue({
    el: '#tspop',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        ifClick: true, //判断是否点击了结算按钮

    },
    mounted: function(){
        laydate.render({
            elem: '#timeVal',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                // 这里放时间选择之后需要处理的代码  比如给数据赋值之类的

            }
        });
    },
    methods: {
        //关闭
        closes: function () {
            $(this.$refs.tspop).hide();
        },
        open: function () {
            $(this.$refs.tspop).show();
        },
        //药品申领
        oksl: function () {
            //这里执行确定结算操作
        	if (!tspop.ifClick) return; //如果为false表示已经点击了不能再点
            tspop.ifClick = false;
        	 var ypslData=[];
             for (var i = 0; i < shyz.ypslInfoList.length; i++) {
                 for (var j = 0; j < shyz.ypslInfoList[i]['yzxx'].length; j++) {
                	 if (shyz.ypslInfoList[i]['yzxx'][j].isChecked) {
                		 ypslData.push({"ypzxlsh": shyz.ypslInfoList[i]['yzxx'][j].ypzxlsh});
                     }
                 }
             }
             if (ypslData.length <= 0) {
                 malert("无药品申领！","top","defeadted");
                 tspop.ifClick = true;
                 return;
             }
             //console.log("ypsl:"+JSON.stringify({list:this.auditingData}));
             this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=ypsl&ksbm=' + shyz.ksid,
                 JSON.stringify({list: ypslData})).then(function (data) {
                 if (data.body.a == 0) {
                     malert("药品申领成功");
                     tspop.ifClick = true;
                     $(this.$refs.tspop).fadeOut(600);
                     // shyz.initSlData();//刷新
                     setTimeout(function () {
                         shyz.closePage();
                     },2500);
                 } else {
                     malert(data.body.c,"top","defeadted");
                     tspop.ifClick = true;
                 }
             }, function (error) {
                 console.log(error);
             });
        }
    }
});
