var hzlb = new Vue({
	el: '#hzlb',
	mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
	data: {
		jsVal: '',
		acContent: {},//安床对象
		brlist: [],//病人列表
		allKs: [],//科室列表
		caqxContent:{},
	},
	updated: function () {
		changeWin();
	},
	mounted:function(){
		this.initData();
	},
	methods: {

		getCsQx: function(){
			//获取参数权限
            parms = {
                "ylbm": 'N010064001',
                "ksbm": hzlb.acContent.ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parms), function (json) {
                if (json.a == 0 && json.d) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
								case "N01006400120": // 是否显示全院医生
									if (csjson.csz) {
										hzlb.caqxContent.N01006400120 = csjson.csz;
									}
									break;
                                case "N01006400121": // 是否显示全院护士
                                    if (csjson.csz) {
                                        hzlb.caqxContent.N01006400121 = csjson.csz;
                                    }
                                    break;
                            }
                        }

                } else {
                    malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                }
            });
		},

		//查询患者列表
		getHzData: function () {
			common.openloading('.loadingTable')
			var ksbm = hzlb.acContent.ksbm;
			var jsparm = hzlb.jsVal;
			var hldj = hzlb.acContent.hldj;
			if (hldj == '0'){
				hldj = null;
			}
			this.brlist = []
			if (hzlb.acContent.zyzt == 'dry') {//待入院
				var json = {
					ryks: ksbm,
					parm: jsparm,
					/*page:this.param.page,
					rows:this.param.rows*/
					page: 1,
					rows: 2000
				};
				$.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=wrzhz&parm=' + JSON.stringify(json), function (json) {
					if (json.a == 0) {
						hzlb.totlePage = Math.ceil(json.d.total / hzlb.param.rows);
						hzlb.brlist = json.d.list;
						common.closeLoading()
					} else {
						common.closeLoading()
					}
				});
			} else if (hzlb.acContent.zyzt == 'zy') {//在院
				var json = {
					ryks: ksbm,
					parm: jsparm,
					hldj: hldj,
					page: 1,
					rows: 2000
				};
				$.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm=' + JSON.stringify(json), function (json) {
					if (json.a == '0') {
						hzlb.brlist = json.d.list;
						common.closeLoading()
					} else {
						common.closeLoading()
					}
				});
			} else if (hzlb.acContent.zyzt == 'cy') {//出院
				var json = {
					ryks: ksbm,
					parm: jsparm,
					/*page:this.param.page,
					rows:this.param.rows*/
					page: 1,
					rows: 2000
				};
				$.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=cyhzxx&parm=' + JSON.stringify(json), function (json) {
					if (json.a == 0) {
						hzlb.totlePage = Math.ceil(json.d.total / hzlb.param.rows);
						hzlb.brlist = json.d.list;
						common.closeLoading()
					} else {
						common.closeLoading()
					}
				});
			} else if (hzlb.acContent.zyzt == 'zk') {//转科
				var json = {
					ryks: ksbm,
					parm: jsparm,
					/*page:this.param.page,
					rows:this.param.rows*/
					page: 1,
					rows: 2000
				};
				$.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=zchz&ksbm=' + ksbm, function (json) {
					if (json.a == 0) {
						hzlb.totlePage = Math.ceil(json.d.total / hzlb.param.rows);
						hzlb.brlist = json.d.list;
						common.closeLoading()
					} else {
						common.closeLoading()
					}
				});
			}
		},


		initData: function () {
			//初始化科室
			$.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"N030042002"}',
				function (json) {
					//console.log(json.d);
					if (json.a == "0") {
						var rlksList = [];
						for (var i = 0; i < json.d.length; i++) {
							if (json.d[i].bqbm != null) {
								rlksList.push(json.d[i]);
							}
						}
						hzlb.allKs = rlksList;
						zkac.Kslist = rlksList;
						
						if (rlksList.length > 0) {
							Vue.set(hzlb.acContent, 'ksbm', rlksList[0].ksbm);
							hzlb.getCsQx();
						}
						hzlb.acContent.zyzt = 'dry';
						hzlb.acContent.hldj = '0';
						//科室编码获取成功后查询病人列表
						hzlb.getHzData();
						//初始化成功后获取医生和床位,原因为给医生和床位赋初值
						hzlb.getYsCw();
					} else {
						malert(json.c, 'top', "defeadted");
					}
				});

		},


		//获取医生床位
		getYsCw: function () {
			var ksbm = hzlb.acContent.ksbm;
			//医生查询
			var parm = { ysbz: '1' };
			$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=' + JSON.stringify(parm),
				function (json) {
					if (json.a == 0) {
						jkac.ryListData = json.d.list;
						//                    if(!jkac.qyys){//判断是否显示全院医生
						//                        //初始过滤第一个科室
						jkac.zgysList = jsonFilter(jkac.ryListData, "ksbm", hzlb.acContent.ksbm);
						zkac.zgysList = jsonFilter(jkac.ryListData, "ksbm", hzlb.acContent.ksbm);
						//            		}else{
						//            			jkac.ryList = json.d.list;
						//            		}
					} else {
						console.log("医生查询错误.");
						malert(json.c, 'top', "defeadted");
					}
				});
			//床位查询
			parm = { "ywbz": "1" };
			var dg={
            	rows:20000,
            	page:1
            };
			$.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCwglCwh&types=query&parm=" + JSON.stringify(parm)+"&dg="+JSON.stringify(dg),
				function (json) {
					jkac.cwList = [];
					if (json.a == 0) {
						if (json.d.list.length > 0) {
							jkac.cwListData = json.d.list;
							//初始过滤第一个科室
							jkac.cwList = jsonFilter(jkac.cwListData, "ksbm", hzlb.acContent.ksbm);
							zkac.cwList = jsonFilter(jkac.cwListData, "ksbm", hzlb.acContent.ksbm);
						}
					} else {
						malert(json.c, 'top', "defeadted");
					}
				});
		},


		resultChange_ks: function (val) {
			console.log(val);
			if (val[2].length > 1) {
				if (Array.isArray(this[val[2][0]])) {
					Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
					hzlb.getHzData();
					hzlb.getYsCw();
				} else {
					Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
					if (val[3] != null) {
						Vue.set(this[val[2][0]], val[3], val[4]);
					}
					hzlb.getHzData();
					hzlb.getYsCw();
				}
			} else {
				this[val[2][0]] = val[0];
				hzlb.getHzData();
				hzlb.getYsCw();
			}
			if (val[1] != null) {
				this.nextFocus(val[1]);
			}
			jkac.Class = true
			jkac.fliter(jkac.jsonList,'searchCon2')
			jkac.fliter(jkac.jsonList1,'searchCon3')
		},
		resultChange_jkac: function (val) {
			console.log(val);
			if (val[2].length > 1) {
				if (Array.isArray(this[val[2][0]])) {
					Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
					hzlb.getHzData();
				} else {
					Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
					if (val[3] != null) {
						Vue.set(this[val[2][0]], val[3], val[4]);
					}
					hzlb.getHzData();
				}
			} else {
				this[val[2][0]] = val[0];
				hzlb.getHzData();
			}
			if (val[1] != null) {
				this.nextFocus(val[1]);
			}
		},

		jkacClick: function (index) {
			if (hzlb.brlist[index].rycwbh != null) {
				malert("该患者已接科！", 'top', "defeadted");
				return
			}
			jkac.jkacContent = JSON.parse(JSON.stringify(hzlb.brlist[index]));
			jkac.open();
		},
		zkacClick: function (index) {
			if (hzlb.brlist[index].rycwbh == null || hzlb.brlist[index].rycwbh == undefined || hzlb.brlist[index].rycwbh == '') {
				malert("该患者未接科，请先接科安床！", 'top', "defeadted");
				return
			}
			zkac.zkhcContent = hzlb.brlist[index];
			zkac.open();
		},

		qcclClick: function (index) {
			qccl.getYsData();
			qccl.getCwData();
			qccl.brInfo = hzlb.brlist[index];
			qccl.open();
		},

		gdfyClick: function (index) {
			sessionStorage.setItem("gdfy", JSON.stringify(hzlb.brlist[index]));
			this.topNewPage('固定费用', 'page/hsz/hlyw/bygl/gdfy.html');
		}
	}
});

//接科按床pop
var jkac = new Vue({
	el: '#jkac',
	mixins: [dic_transform, tableBase, mConfirm, baseFunc],
	components: {
		'search-table2': searchTable
	},
	data: {
		title: '接科安床',
		ryListData: [],
		cwListData: [],
		zgysList: [],//主管医生列表
		zgys: '02',
		cwList: [],//床位列表
		cw: '01',
		jkacContent: {
		},
		ifClick: true,//重复点击判断
		Class: true,
		searchjson: {},
		selSearch3:-1,
		searchCon2: [],
		searchjson3: {},
		searchCon3: [],
		them_tran2: {
		},
		jsonList:[],
		jsonList1:[],
		them2: {
			'医生姓名': 'ryxm',
			'医生编码': 'rybm',
			'医生科室': 'ksmc',
		},
		them3: {
			'护士姓名': 'ryxm',
			'护士编码': 'rybm',
			'护士科室': 'ksmc',
		},
		page: {
			page: 1,
			rows: 20,
			total: null
		},
	},
	mounted:function(){
		Mask.newMask(this.MaskOptions('dbegin'));
		// this.jkacContent.jrrq= this.fDate(new Date(),'date');
		setTimeout(function(){
			jkac.getzgys({ysbz: '1'},'searchCon2','jsonList');
			jkac.getzgys({hsbz: '1'},'searchCon3','jsonList1');
		},500);
	},
	methods: {
		setTime(event,code){
			jkac.jkacContent[code]=event.target.value
		},
		showDate: function (index, el,code) {
			this._laydate = laydate.render({
				elem: '#'+el
				, show: true //直接显示
				, type: 'datetime'
				, theme: '#1ab394',
				min:zkac.fDate(jkac.jkacContent.djrq,'datetime'),
				done: function (value, data) {
					jkac.jkacContent[code]=value
				}
			})
		},
		changeDown2: function (event, type) {
			if (this['searchCon2'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'searchjson', 'searchCon2');
			// 选中之后的回调操作
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter' ) {
				if (type == 'text') {
					Vue.set(this.jkacContent, 'ryxm', this.searchjson['ryxm']);
					Vue.set(this.jkacContent, 'zyys', this.searchjson['rybm']);
					this.nextFocus(event)
				}
			}
		},
		resultChangeYs:function(val){

			Vue.set(this.jkacContent, val[2][1], val[0]);
			Vue.set(this.jkacContent, val[3], val[4]);
			this.nextFocus(val[1]);
		},
		getzgys:function(obj,objContent,list){
						$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybmjkac&json=" + JSON.stringify(obj), function (data) {
				console.log(data)
				if (data.a=='0' && data.d) {
					jkac[list]=data.d.list
					jkac.fliter(jkac[list],objContent)
				}
			});
		},
		fliter:function(list,objContent){
			if(objContent == 'searchCon2'){//医生
				if(hzlb.caqxContent.N01006400120 == '1'){
					jkac[objContent] = list;
				}else{
					jkac[objContent] = jsonFilter(list, "ksbm", hzlb.acContent.ksbm);
				}
			}else if(objContent == 'searchCon3'){
				if(hzlb.caqxContent.N01006400121 == '1'){
					jkac[objContent] = list;
				}else{
					jkac[objContent] = jsonFilter(list, "ksbm", hzlb.acContent.ksbm);
				}
			}else{
				jkac[objContent] = jsonFilter(list, "ksbm", hzlb.acContent.ksbm);
			}
		},
		change2: function (add, val) {
			this.jkacContent['ryxm'] = val;
			if (!add) this.page.page = 1;       //  设置当前页号为第一页
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
				this.page.parm = this.jkacContent['ryxm'];
			var json = {
				ysbz: '1'
			};
			var dg = {
				page: this.page.page,
				rows: this.page.rows,
				parm: this.page.parm,
			};
			$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybmjkac&json=" + JSON.stringify(json) + "" +
				"&dg=" + JSON.stringify(dg), function (data) {
					if (add) {
						if (data.d.list.length > 0) {
							var zgysList2 = jsonFilter(data.d.list, "ksbm", hzlb.acContent.ksbm);
							for (var i = 0; i < zgysList2.length; i++) {
								jkac.searchCon2.push(data.d.list[i]);
							}
						}
					} else {
						if (data.d.list.length > 0) {
							var zgysList2 = jsonFilter(data.d.list, "ksbm", hzlb.acContent.ksbm);
							jkac.searchCon2 = zgysList2;
						}
					}
					jkac.page.total = data.d.total;
					jkac.selSearch = 0;
					if (data.d.list.length > 0 && !add) {
						$(".selectGroup").hide();
						_searchEvent.show();
					}
				});
		},

		selectOne2: function (item) {
			if (item == null) {
				this.page.page++;
				this.change2(true,  this.searchjson['text']);
			} else {
				this.searchjson = item;
				Vue.set(jkac.jkacContent, 'ryxm', this.searchjson['ryxm']);
				Vue.set(jkac.jkacContent, 'zyys', this.searchjson['rybm']);
				$(".selectGroup").hide();
			}
		},

		changeDown3: function (event, type) {
			if (this['searchCon3'][this.selSearch3] == undefined) return;
			this.inputUpDown(event,this.searchCon3,'selSearch3')
			//this.keyCodeFunction(event, 'searchjson3', 'searchCon3');
			this.searchjson3=this['searchCon3'][this.selSearch3]
			// 选中之后的回调操作
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
					Vue.set(jkac.jkacContent, 'ryxm1', this.searchjson3['ryxm']);
					Vue.set(jkac.jkacContent, 'zrhs', this.searchjson3['rybm']);
				$(".selectGroup").hide();
				this.nextFocus(event)
			}
		},
		change3: function (add, val) {
			this.searchjson3['ryxm1'] = val;
			if (!add) this.page.page = 1;       //  设置当前页号为第一页
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
				this.page.parm = val;
			var json = {
				hsbz: '1'
			};
			var dg = {
				page: this.page.page,
				rows: this.page.rows,
				parm: this.page.parm,
			};
			$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybmjkac&json=" + JSON.stringify(json) + "" +
				"&dg=" + JSON.stringify(dg), function (data) {
				if (add) {
					if(hzlb.caqxContent.N03004200101 == '1'){
						jkac.searchCon3 = data.d.list;
					}else{
						var zgysList2 = jsonFilter(data.d.list, "ksbm", hzlb.acContent.ksbm);
						for (var i = 0; i < zgysList2.length; i++) {
							jkac.searchCon3.push(data.d.list[i]);
						}
					}

					this.page.total = data.d.total;
				} else {
					if(hzlb.caqxContent.N03004200101 == '1'){
						jkac.searchCon3 = data.d.list;
					}else{
						var zgysList2 = jsonFilter(data.d.list, "ksbm", hzlb.acContent.ksbm);
						jkac.searchCon3 = zgysList2;
					}
				}
				jkac.page.total = data.d.total;
				jkac.selSearch3 = 0;
				if (data.d.list.length > 0 && !add) {
					$(".selectGroup").hide();
					_searchEvent.show();
				}
			});
		},

		selectOne3: function (item) {
			if (item == null) {
				this.page.page++;
				this.change3(true,  this.jkacContent['ryxm1']);
			} else {
				Vue.set(jkac.jkacContent, 'ryxm1', item['ryxm']);
				Vue.set(jkac.jkacContent, 'zrhs', item['rybm']);
				$(".selectGroup").hide();
				this.selSearch3=-1
			}
		},

		//关闭
		closes: function () {
			this.Class = true
			jkac.jkacContent = {};
		},

		//打开
		open: function () {
			this.Class = false;
			this.$forceUpdate()
		},

		//接科安床
		saveJkac: function () {
			if (!this.ifClick) {
				return;
			}//保存提交限制只允许一次
			
			if(new Date(zkac.fDate(jkac.jkacContent.djrq,'datetime'))>new Date(jkac.jkacContent.jrrq)){
				malert("接科时间需大于入院时间!", 'top', "defeadted");
				return false;
			}
			this.ifClick = false;
			if (!jkac.jkacContent.zyh) {
				malert("请先选择需接科的患者!", 'top', "defeadted");
				this.ifClick = true;
				return;
			}
			
			jkac.jkacContent.ksbm = hzlb.acContent.ksbm;
			jkac.jkacContent.ksmc = hzlb.acContent.ksmc;

			this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=jkac',
				JSON.stringify(jkac.jkacContent))
				.then(function (data) {
					if (data.body.a == "0") {
						malert("接科安床成功！");

                        //新加代码，调用护理接口
                        if (window.top.J_tabLeft.obj.hljkurl !=null && window.top.J_tabLeft.obj.hljkurl !=undefined){
                            var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                            var inJson ={
                                yljgbm:jgbm,
                                zyh:jkac.jkacContent.zyh
                            }
                            var params ={
                                yljgbm:jgbm,
                                zyh:jkac.jkacContent.zyh,
                                types:'hzxx',
                                method:'ADT_A10',
                                inJson : JSON.stringify(inJson)
                            }
                            this.postAjax(hljkurl,JSON.stringify(params),function (result) {
                                if (result.code==0){
                                    console.log("护理接口调取成功!");
                                } else {
                                    console.log("护理接口调取失败:"+result.msgInfo);
                                    console.log("护理接口调取失败的住院号为:"+params.zyh)
                                }
                            })}

						jkac.Class = true,
						jkac.jkacContent = {};
						$("#jkac.side-form").removeClass('side-form-bg');
						$("#jkac.side-form").addClass('ng-hide');
					} else {
						console.log("error:" + data.body.c);
						malert(data.body.c, 'top', "defeadted");
					}
					jkac.ifClick = true;
					hzlb.getHzData();
					hzlb.getYsCw();
				});
		},
	}
});

//转科按床pop
var zkac = new Vue({
	el: '#zkac',
	mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
	data: {
		title: '转科按床',
		ryListData: [],
		cwListData: [],
		zgysList: [],
		zgys: '02',
		cwList: [],
		cw: '01',
		zkhcContent: {},
		Kslist: [],
		ifClick: true,//重复点击判断
		Class: true,
		sqlist: [],//转科申请列表
		sfsq: false,//是否申请
	},
	methods: {
		//关闭
		closes: function () {
			zkac.Class = true
			zkac.zkhcContent = {};
		},

		//打开
		open: function () {
			zkac.Class = false
		},

		//获取患者是否已申请转科换床
		getSfsq: function () {
			$.getJSON('/actionDispatcher.do?reqUrl=HszByglCwgl&types=queryJzkjl&parm={"zyh":"' + zkac.zkhcContent.zyh + '","zfbz":"0"}',
				function (json) {
					if (json.a == "0") {
						zkac.sqList = json.d.list;
					} else {
						malert(json.c, 'top', "defeadted");
					}
				});
		},

		//转科换床
		saveZkhc: function () {
			$.ajaxSettings.async = false;
			if (!this.ifClick) {
				return;
			}//保存提交限制只允许一次
			this.ifClick = false;
			if (!zkac.zkhcContent.zyh) {
				malert("请先选择需接科的患者!", 'top', "defeadted");
				this.ifClick = true;
				return;
			}
			zkac.getSfsq();
			for (var i = 0; i < zkac.sqList.length; i++) {
				if (zkac.sqList[i].jrbz == '0') {
					zkac.sfsq = true;
				}
			}
			if (zkac.sfsq) {
				malert("该患者已申请，请不要重复操作！", 'top', "defeadted");
				this.ifClick = true;
				return;
			}
			zkac.zkhcContent.ksmc = hzlb.acContent.ksmc;
			var zrksmc=zkac.listGetName(zkac.Kslist, zkac.zkhcContent.zrksbm, 'ksbm', 'ksmc');
			this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=zkhc',
				JSON.stringify(zkac.zkhcContent))
				.then(function (data) {
					if (data.body.a == "0") {
						malert("转科换床申请成功！");

                        //新加代码，调用护理接口
                        if (window.top.J_tabLeft.obj.hljkurl !=null && window.top.J_tabLeft.obj.hljkurl !=undefined){
                            var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                            var inJson ={
                                yljgbm:jgbm,
                                zyh:zkac.zkhcContent.zyh
                            }
                            var params ={
                                yljgbm:jgbm,
                                zyh:zkac.zkhcContent.zyh,
                                types:'hzxx',
                                method:'ADT_A32',
                                inJson : JSON.stringify(inJson)
                            }
                            this.postAjax(hljkurl,JSON.stringify(params),function (result) {
                                if (result.code==0){
                                    console.log("护理接口调取成功!");
                                } else {
                                    console.log("护理接口调取失败:"+result.msgInfo);
                                    console.log("护理接口调取失败的住院号为:"+params.zyh)
                                }
                            })}


						zkac.Class = true;
						$("#zkac.side-form").removeClass('side-form-bg');
						$("#zkac.side-form").addClass('ng-hide');
						zkac.closes;
					    // var sendmsg={
	        //                         msgtype:'1',
	        //                         ksbm:hzlb.acContent.ksbm,
	        //                         yljgbm:jgbm,
	        //                         yqbm:yqbm,
	        //                         msg:'有转科申请病人：'+zkac.zkhcContent.brxm+",转入科室："+zrksmc+",申请科室："+zkac.zkhcContent.ryksmc,
	        //                         toaddress:'page/hsz/hlyw/bygl/bygl.html',
	        //                         sbid:zkac.zkhcContent.brxm+"_"+zkac.fDate(new Date(),'YY'),
	        //                         ylbm:'N030042001',
	        //                         pagename:"病员管理"
	        //                     };
	        //                     console.log(sendmsg);
	        //                     window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
	                        	zkac.zkhcContent={};
					} else {
						console.log("error:" + data.body.c);
						malert(data.body.c, 'top', "defeadted");
						zkac.zkhcContent={};
					}
					zkac.ifClick = true;
					hzlb.getHzData();
					hzlb.getYsCw();
				});
		},
	}
});

//迁床处理
var qccl = new Vue({
	el: '#qccl',
	mixins: [dic_transform, tableBase, mConfirm, baseFunc],
	data: {
		title: '迁床处理',
		zgysList: [],
		cwList: [],
		cwid: null,
		ifClick: true,//重复点击判断
		Class: true,
		brInfo: {},
		jsonListData: [],
		qcContent: {},
	},
	methods: {
		getCwData: function () {
			//床位查询
			parm = { "ywbz": "1" };
			$.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCwglCwh&types=query&parm=" + JSON.stringify(parm),
				function (json) {
					qccl.cwList = [];
					if (json.a == 0) {
						if (json.d.list.length > 0) {
							qccl.cwList = jsonFilter(json.d.list, "ksbm", hzlb.acContent.ksbm);
						}
					} else {
						malert(json.c, 'top', "defeadted");
					}
				});
		},
		getYsData: function () {
			$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm", function (json) {
				if (json.a == "0") {
					qccl.jsonListData = json.d.list;
					//                    //0=默认当前病区医生，1=全院医生
					//                    if (yzclLeft.caqxContent.cs00900100118 == '0') {
					//                        //过滤医生
					qccl.zgysList = jsonFilter(qccl.jsonListData, "ksbm", hzlb.acContent.ksbm);
					//                    } else {
					//                   	 ghys.zgysList = json.d.list;
					//                    }
				}
			});
		},
		//关闭
		closes: function () {
			qccl.Class = true
			qccl.brInfo = {};
			qccl.qcContent = {};
		},

		//打开
		open: function () {
			qccl.Class = false
		},


		//转科换床
		saveQccl: function () {
			if (!this.ifClick) {
				return;
			}//保存提交限制只允许一次
			this.ifClick = false;
			var parm = {
				'zyh': qccl.brInfo.zyh,
				'qccwid': qccl.brInfo.rycwid,
				'qrcwid': qccl.qcContent.cwid,
			};
			this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=Qccl',
				JSON.stringify(parm))
				.then(function (data) {
					if (data.body.a == "0") {
                        //新加代码，调用护理接口
                        if (window.top.J_tabLeft.obj.hljkurl !=null && window.top.J_tabLeft.obj.hljkurl !=undefined){
                            var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                            var inJson ={
                                yljgbm:jgbm,
                                zyh:qccl.brInfo.zyh
                            }
                            var params ={
                                yljgbm:jgbm,
                                zyh:qccl.brInfo.zyh,
                                types:'hzxx',
                                method:'ADT_A02',
                                inJson : JSON.stringify(inJson)
                            }
                            this.postAjax(hljkurl,JSON.stringify(params),function (result) {
                                if (result.code==0){
                                    console.log("护理接口调取成功!");
                                } else {
                                    console.log("护理接口调取失败:"+result.msgInfo);
                                    console.log("护理接口调取失败的住院号为:"+params.zyh)
                                }
                            })}

						qccl.closes();
						hzlb.getHzData();
						hzlb.getYsCw();
						malert("更新数据成功");
					} else {
						malert("申请失败", json.c,"top","defeadted");
					}
					qccl.ifClick = true;
				});
		},
	}
});
$('body').click(function () {
	$(".selectGroup").hide();
});
