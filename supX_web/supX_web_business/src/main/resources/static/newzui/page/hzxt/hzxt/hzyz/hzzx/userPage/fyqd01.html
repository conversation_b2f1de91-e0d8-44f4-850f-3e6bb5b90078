<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <title>费用清单</title>
    <link rel="stylesheet" href="fyqd.css"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body >

<div id="fyqd" class=" bg-fff  over-auto height100">
    <div class="brSearch printHide flex-container flex-align-c">
        <div class="flex-container flex-align-c padd-r-20 padd-l-10">
            <span class="ft-14 padd-r-5">性别</span>
            <input class="zui-input " type="text" v-model="brxb_tran[json.brxb]" disabled="disabled"/>
        </div>
        <div class="flex-container flex-align-c padd-r-20">
            <span class="ft-14 padd-r-5">年龄</span>
            <input class="zui-input" type="text" v-model="json.nl" disabled="disabled"/>
        </div>
        <div class="flex-container flex-align-c padd-r-20">
            <span class="ft-14 padd-r-5">床位</span>
            <input class="zui-input" type="text" v-model="json.rycwbh" disabled="disabled"/>
        </div>
        <div class="flex-container flex-align-c padd-r-20">
            <span class="ft-14 padd-r-5">入院日期</span>
            <input class="zui-input" type="text" v-model="fDate(json.ryrq,'date')" disabled="disabled">
        </div>
        <div class="flex-container flex-align-c padd-r-20">
            <span class="ft-14 padd-r-5">费别</span>
            <input class="zui-input" type="text" v-model="json.brfbmc" disabled="disabled"/>
        </div>
        <button v-waves @click="print" class="tong-btn btn-parmary  xmzb-db" >打印</button>
    </div>

    <div id="context">
        <!--<div class="toolMenu printHide">-->

            <!--<span style="margin-left: 20px">清单类型</span>-->
            <!--<select v-model="qdType">-->
                <!--<option :value="0">汇总清单</option>-->
                <!--<option :value="1">明细清单</option>-->
            <!--</select>-->
        <!--</div>-->

        <!--汇总清单-->
        <div class="fyqdContext" v-show="qdType == 0">
            <h2>住院费用汇总清单</h2>
            <div class="fyqdTime">
                <span>发生时间：</span>
                <span v-text="fyqdContent.ksrq"></span>
                <span>至</span>
                <span v-text="fyqdContent.jsrq"></span>
            </div>
            <div class="infoIpt">
                <p>住院号：</p>
                <span v-text="fyqdContent.zyh"></span>
            </div>
            <div class="infoIpt">
                <p>病员姓名：</p>
                <span v-text="fyqdContent.brxm"></span>
            </div>
            <div class="infoIpt">
                <p>住院科室：</p>
                <span v-text="fyqdContent.ryksmc"></span>
            </div>
            <div class="infoIpt">
                <p>床位号：</p>
                <span v-text="fyqdContent.rycwbh"></span>
            </div>
            <div class="infoIpt">
                <p>入院日期：</p>
                <span v-text="fDate(fyqdContent.ryrq,'date')"></span>
            </div>
            <div class="infoIpt">
                <p>病区出院日期：</p>
                <span v-text="fDate(fyqdContent.bqcyrq,'date')"></span>
            </div>
            <div class="infoIpt">
                <p>工作单位：</p>
                <span></span>
            </div>
            <div class="infoIpt">
                <p>费别：</p>
                <span v-text="fyqdContent.brfbmc"></span>
            </div>
            <div class="infoIpt" style="width: 46%">
                <p>家庭地址：</p>
                <span v-text="fyqdContent.jtdz"></span>
            </div>
            <div class="infoIpt">
                <p>费用合计：</p>
                <span v-text="fDec(fyqdContent.fyhj,2)"></span>
            </div>
            <div class="infoIpt">
                <p>预交合计：</p>
                <span v-text="fDec(fyqdContent.yjhj,2)"></span>
            </div>

            <div class="infoIpt" style="width: 46%">
                <p>入院诊断：</p>
                <span v-text="fyqdContent.ryzdmc"></span>
            </div>

            <div class="fyqdTable">
                <table class="patientTable" cellspacing="0" cellpadding="0">
                    <tr>
                        <td style="width: 80px">费用编码</td>
                        <td style="width: 150px">费用名称</td>
                        <td style="width: 80px">规格</td>
                        <td style="width: 30px">单位</td>
                        <td style="width: 50px">单价</td>
                        <td style="width: 40px">数量</td>
                        <td style="width: 80px">金额</td>
                        <td style="width: 40px">类型</td>
                        <td style="width: 80px">医保类别</td>
                    </tr>
                    <tbody v-for='itemlist in jsonList'>
                    <tr>
                        <td colspan="9"><span v-text="itemlist.fylbmc"></span><span v-text="fDec(itemlist.fyze,2) + '元'"></span></td>
                    </tr>
                    <tr v-for="item in itemlist.fymx">
                        <td v-text="item.xmbm"></td>
                        <td v-text="item.xmmc"></td>
                        <td v-text="item.fygg"></td>
                        <td></td>
                        <td v-text="fDec(item.fydj,3)"></td>
                        <td v-text="item.fysl"></td>
                        <td v-text="fDec(item.fyje,2)"></td>
                        <td>费用</td>
                        <td v-text="nhtclb_tran[item.fw]"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="total">
                <div>
                    <span>合计：</span>
                    <span v-text="fDec(fyqdContent.fyhj,2)"></span>元
                </div>
                <div>
                    <span>四舍五入：</span>
                    <span></span>
                </div>
            </div>
        </div>

        <!--明细清单-->
        <div class="fyqdContext detailList" v-show="qdType == 1">
            <h2>住院费用明细清单（费用+药品）</h2>
            <div class="fyqdTime">
                <span>发生时间：</span>
                <span v-text="fyqdContent.ksrq"></span>
                <span>至</span>
                <span v-text="fyqdContent.jsrq"></span>
            </div>
            <div class="infoIpt">
                <p>住院号：</p>
                <span v-text="fyqdContent.zyh"></span>
            </div>
            <div class="infoIpt">
                <p>病员姓名：</p>
                <span v-text="fyqdContent.brxm"></span>
            </div>
            <div class="infoIpt">
                <p>住院科室：</p>
                <span v-text="fyqdContent.ryksmc"></span>
            </div>
            <div class="infoIpt">
                <p>床位号：</p>
                <span v-text="fyqdContent.rycwbh"></span>
            </div>
            <div class="infoIpt">
                <p>入院日期：</p>
                <span v-text="fDate(fyqdContent.ryrq,'date')"></span>
            </div>
            <div class="infoIpt">
                <p>病区出院日期：</p>
                <span v-text="fDate(fyqdContent.bqcyrq,'date')"></span>
            </div>
            <div class="infoIpt">
                <p>工作单位：</p>
                <span></span>
            </div>
            <div class="infoIpt">
                <p>费别：</p>
                <span v-text="fyqdContent.brfbmc"></span>
            </div>
            <div class="infoIpt" style="width: 46%">
                <p>家庭地址：</p>
                <span v-text="fyqdContent.jtdz"></span>
            </div>
            <div class="infoIpt">
                <p>费用合计：</p>
                <span v-text="fDec(fyqdContent.fyhj,2)"></span>
            </div>
            <div class="infoIpt">
                <p>预交合计：</p>
                <span v-text="fDec(fyqdContent.yjhj,2)"></span>
            </div>

            <div class="fyqdTable">
                <table class="patientTable" cellspacing="0" cellpadding="0">
                    <tr>
                        <td style="width: 100px">记帐时间</td>
                        <td style="width: 150px">项目名称</td>
                        <td style="width: 80px">规格</td>
                        <td style="width: 30px">数量</td>
                        <td style="width: 50px">单价</td>
                        <td style="width: 50px">金额</td>
                        <td style="width: 40px">医师</td>
                        <td style="width: 80px">费用科目</td>
                    </tr>
                    <!--<tbody v-for='itemlist in jsonMxList'>
                     <tr>
                        <td colspan="8"><span v-text="itemlist.fylbmc"></span><span v-text="fDec(itemlist.fyze,2) + '元'"></span></td>
                    </tr> -->
                    <tr v-for="item in jsonMxList">
                        <td v-text="fDate(item.djrq,'date')"></td>
                        <td v-text="item.xmmc"></td>
                        <td v-text="item.fygg"></td>
                        <td v-text="item.fysl"></td>
                        <td v-text="fDec(item.fydj,3)"></td>
                        <td v-text="fDec(item.fyje,2)"></td>
                        <td v-text="item.zyysxm"></td>
                        <td v-text="item.fylbmc"></td>
                    </tr>
                    <!-- </tbody> -->
                </table>
            </div>
            <!--<div class="total">-->
            <!--<div>-->
            <!--<span>合计：</span>-->
            <!--<span v-text="fDec(fyqdContent.fyhj,2)"></span>元-->
            <!--</div>-->
            <!--<div>-->
            <!--<span>四舍五入：</span>-->
            <!--<span></span>-->
            <!--</div>-->
            <!--</div>-->
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="fyqd01.js"></script>
</html>
