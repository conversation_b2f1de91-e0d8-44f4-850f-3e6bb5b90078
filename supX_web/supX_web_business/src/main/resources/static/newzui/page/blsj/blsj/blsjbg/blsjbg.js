(function () {
    //顶部工具栏
    var tool=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
            dg: {
                page: 1,
                rows: 20,
                sort: '',
                order: 'asc',
                parm: '',
                beginrq:'',
                endrq:''
            },
            zt: '9',
        },
        methods:{
            //新建会诊
            AddModel:function (num,val) {
                sessionStorage.setItem('dhzglitem',JSON.stringify(val));
                this.topNewPage('新建不良事件报告','page/blsj/blsj/blsjbg/sub/hzbg.html')
            },
            //刷新和检索
            getData:function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1Blsjbg&types=queryList&dg=" + JSON.stringify(this.dg),function (json) {
                   console.log(json);
                });
            },
            //导出
            exports:function () {

            }

        }
    });

    //危急值管理列表
    var wxdjList = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data: {

        },

        updated:function () {
            changeWin()
        },
        methods: {
            //请求后台查询列表信息
            getData : function(){
                //加载动画效果
                common.openloading('.zui-table-view');
                //数据请求结束时关闭


                common.closeLoading()
            },
            //
            // edit:function (index) {
            // brzcList.title='编辑危急值'
            //  brzcList.open();
            // },
            //患者姓名跳转到电子病历
            Patient:function () {
                // this.topNewPage('患者中心','page/zyysz/zyysz/hzgl/hzzx/hzzx.html')
            },
        }
    });
    laydate.render({
        elem: '.times',
        type: 'date',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
    laydate.render({
        elem: '.times1',
        type: 'date',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
})();





