    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>医嘱执行单</title>
    <!--<link href="/page/zyys/ysyw/yzblcl/yzblcl.css" rel="stylesheet" type="text/css"/>-->
    <link href="yzzxd.css" rel="stylesheet" type="text/css"/>
    <!-- <link rel="stylesheet" href="/pub/css/print.css" media="print"/> -->
<div class="flex-container printHide height-100">
    <div class=" printHide padd-l-10 padd-r-10 flex-container flex-dir-c" id="bodyMenu" v-cloak>
        <!--检索-->
        <div class="enter_tem1 zxTime flex-container flex-align-c" style="padding: 6px 0">
            <span>执行日期:</span>
            <input class="zui-input wh180" type="text" readonly="readonly" v-model="ksrq" id="dbegin"
                   @keydown="searchListHc">
            <span style="margin: 0 10px 0 10px">至</span>
            <input class="zui-input wh180" type="text" readonly="readonly"  v-model="jsrq" id="dEnd"
                   @keydown="searchListHc">
            <!--<label style="margin-left: 30px"><input name="isInHol" type="radio" v-model="yzlx" value="qb" @change="YzlxChange('qb')"><span>全部</span></label>-->
            <div class="flex-container"><div class="position padd-r-5" @change="YzlxChange('qb')"><input v-model="picked" type="radio" id="one" name="one" value="0" class="zui-radio"> <label for="one" class="padd-r-5"></label></div> <div class="lj-pop-text">全部</div></div>
            <div class="flex-container"><div class="position padd-r-5" @change="YzlxChange('ls')"><input v-model="picked" type="radio" id="two" name="one" value="1" class="zui-radio"> <label for="two" class="padd-r-5"></label></div> <div class="lj-pop-text">临时</div></div>
            <div class="flex-container"><div class="position padd-r-5" @change="YzlxChange('cq')"><input v-model="picked" type="radio" id="three" name="one" value="2" class="zui-radio"> <label for="three" class="padd-r-5"></label></div> <div class="lj-pop-text">长期</div></div>
            <label class="font-14" style="margin-left: 20px;">输液滴速</label>
            <select-input class="wh120" @change-data="resultChangeSysd" :child="sysdfw_tran" :index="searchContent.sysd"
                :val="searchContent.sysd" :name="'searchContent.sysd'" :search="true">
            </select-input>
            <!--<label style="margin-left: 10px"><input name="isInHol" type="radio" v-model="yzlx" value="ls" @change="YzlxChange('ls')"><span>临时</span></label>-->
            <!--<label style="margin-left: 10px"><input name="isInHol" type="radio" v-model="yzlx" value="cq" @change="YzlxChange('cq')"><span>长期</span></label>-->
        </div>

        <!-- 功能按钮 -->
        <div class="fyxm-tab  InfoMenu padd-t-10 padd-l-10">
            <div><span :class="{'active':num==0}" @click="clickMenu('all',0)">全部</span></div>
            <div><span :class="{'active':num==1}" @click="clickMenu('kf',1)">口服</span></div>
            <div><span :class="{'active':num==2}" @click="clickMenu('zs',2)">注射</span></div>
            <div><span :class="{'active':num==3}" @click="clickMenu('sy',3)">输液</span></div>
            <div><span :class="{'active':num==4}" @click="clickMenu('hl',4)">护理</span></div>
            <div><span :class="{'active':num==5}" @click="clickMenu('zl',5)">治疗</span></div>
            <div><span :class="{'active':num==6}" @click="clickMenu('syt',6)">输液贴</span></div>
            <button @click="showLsYz" class="tong-btn btn-parmary-b  paddr-r5" style="float: right">皮试结果录入</button>
            <button @click="printFormat" class="tong-btn btn-parmary-b -sx paddr-r5" style="float: right">打印</button>
        </div>
        <div style="clear: both"></div>
        <!-- 输液贴 -->
        <div v-if="num==6" class="flex-one flex-container flex-dir-c">
            <div class="flex-container" style="margin: 10px 0;">
                <button class="tong-btn btn-parmary-b paddr-r5" @click="sytNumberOfColumns=1">一栏式</button>
                <button class="tong-btn btn-parmary-b paddr-r5" @click="sytNumberOfColumns=3">三栏式</button>
            </div>
            <div class="flex-one syt-col-box flex-container flex-wrap-w over-auto" :class="{'col-one': sytNumberOfColumns==1,'col-three': sytNumberOfColumns==3}">
                <div class="syt-item" v-for="item in yzzxdList">
                    <div class="flex-container syt-top">
                        <span class="flex-one" v-text="item.yzlxmc"></span>
                        <span class="flex-one font-bold" v-text="item.rycwbh"></span>
                        <span class="flex-tow text-center font-bold" v-text="item.brxm"></span>
                        <span class="flex-tow text-center" v-text="item.brnl"></span>
                    </div>
                    <div class="syt-center">
                        <div class="flex-container" v-for="mx in item.yzxx">
                            <div class="flex-one" v-text="mx.xmmc"></div>
                            <div v-text="mx.ypgg"></div>
                        </div>
                    </div>
                    <div class="syt-bottom flex-container">
                        <div class="flex-one" v-text="item.yyffmc"></div>
                        <div class="flex-one text-center" v-text="item.pcmc"></div>
                        <div class="flex-one text-right" v-text="item.sysd"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 医嘱 -->
        <div class="zxdDiv" v-else>
            <!-- 全部医嘱执行单 -->
            <div style="margin-bottom: 60px" v-for="item in yzzxdList" v-if="item.yzxx.length != 0">
                <table cellspacing="0" cellpadding="0" style="width: 100%;font-size: 12px;">
                    <!-- 基本信息 -->
                    <tr>
                        <th style="width: 32px;padding-left: 24px">姓名:</th>
                        <td v-text="item.brxm"></td>
                        <th>性别:</th>
                        <td v-text="brxb_tran[item.brxb]"></td>
                        <th>年龄:</th>
                        <td v-text="item.nl"></td>
                        <th>科室:</th>
                        <td v-text="item.ryksmc"></td>
                        <th>床位号:</th>
                        <td v-text="item.ywckbh"></td>
                        <th>住院号:</th>
                        <td v-text="item.zyh"></td>
                    </tr>
                </table>
                <!-- start-->
                <div class="enter_tem1 yzzxTable">
                    <!--<div class="enter_tem1_title">医嘱执行单</div>-->
                    <div class="table_tab1">
                        <table>
                            <tr style="border: 1px solid #ccc">
                                <th>序号</th>
                                <th style="min-width: 50px;">分组号</th>
                                <!--<th style="min-width: 60px;">分组序号</th>-->
                                <th style="min-width: 60px;">医嘱类型</th>
                                <th style="min-width: 250px;">医嘱项目</th>
                                <th style="min-width: 60px;">剂量</th>
                                <th style="min-width: 60px;">剂量单位</th>
                                <th style="min-width: 60px;">执行护士</th>
                                <!--<th style="min-width: 60px;">执行时间</th>-->
                                <!--<th style="min-width: 60px;">执行开始时间</th>-->
                                <!--<th style="min-width: 60px;">执行结束时间</th>-->
                                <th style="min-width: 60px;">用法</th>
                                <th style="min-width: 60px;">频次</th>
                                <th style="min-width: 60px;">备注</th>
                                <th style="min-width: 60px;">皮试结果</th>
                                <th style="min-width: 60px;">皮试人员</th>
                                <th style="min-width: 60px;">皮试时间</th>
                            </tr>
                            <tr v-for="(zxdItem, $index) in item.yzxx">
                                <td v-text="$index+1"></td>
                                <td v-text="zxdItem.fzh"></td>
                                <!--<td v-text="zxdItem.fzxh"></td>-->
                                <td v-text="yzlx_tran[zxdItem.yzlx]"></td>
                                <td v-text="zxdItem.xmmc"></td>
                                <td v-text="zxdItem.jl"></td>
                                <td v-text="zxdItem.jldwmc"></td>
                                <td v-text="zxdItem.zxhsxm"></td>
                                <!--<td v-text="fDate(zxdItem.zxsj,'datetimes')"></td>-->
                                <!--<td v-text="fDate(zxdItem.zx_begin,'datetimes')"></td>-->
                                <!--<td v-text="fDate(zxdItem.zx_end,'datetimes')"></td>-->
                                <td v-text="zxdItem.yyffmc"></td>
                                <td v-text="zxdItem.pcmc"></td>
                                <td></td>
                                <td v-text="psjg_tran[zxdItem.psjg]"></td>
                                <td v-text="zxdItem.pslrry"></td>
                                <td v-text="fDate(zxdItem.pslrsj,'yyyy-MM-dd')"></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="right printHide" v-cloak id="rightPcxx">
        <!--<div class="tableDiv">-->
            <!--<table class="patientTable" cellspacing="0" cellpadding="0">-->
                <!--<thead>-->
                <!--<tr>-->
                    <!--<th><input type="checkbox" v-model="isCheckAll" @click="checkAll('pcList')"></th>-->
                    <!--<th style="min-width: 151px;">频次名称</th>-->
                <!--</tr>-->
                <!--</thead>-->
                <!--<tr v-for="(item, $index) in pcList" @click="checkOne($index)"-->
                    <!--:class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">-->
                    <!--<th>-->
                        <!--<input class="" type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)"/>-->
                    <!--</th>-->
                    <!--<td style="min-width: 143px;max-width: 143px" v-text="item.pcmc"></td>-->
                <!--</tr>-->
            <!--</table>-->
        <!--</div>-->
        <div class="zui-table-view flex-one padd-b-40 padd-r-10 flex-dir-c flex-container">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'pcList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">频次名称</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body flex-one over-auto" data-no-change @scroll="scrollTable">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr   @click="checkSelect([$index,'some','brlist'],$event)"
                          :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                          @mouseenter="hoverMouse(true,$index)"
                          @mouseleave="hoverMouse()"
                          :class="[{'tableTr': $index%2 == 0},{'table-hovers':isChecked[$index]}]" :tabindex="$index"
                          v-for="(item, $index) in pcList" >
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'pcList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.pcmc}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 输液贴打印 begin -->
<!-- <div v-if="num==6" class="flex-one flex-container flex-dir-c">
    <div class="flex-one syt-col-box flex-container flex-wrap-w over-auto" :class="{'col-one': sytNumberOfColumns==1,'col-three': sytNumberOfColumns==3}">
        <div class="syt-item" v-for="item in yzzxdList">
            <div class="flex-container syt-top">
                <span class="flex-one" v-text="item.yzlxmc"></span>
                <span class="flex-one font-bold" v-text="item.rycwbh"></span>
                <span class="flex-tow text-center font-bold" v-text="item.brxm"></span>
                <span class="flex-tow text-center" v-text="item.brnl"></span>
            </div>
            <div class="syt-center">
                <div class="flex-container" v-for="mx in item.yzxx">
                    <div class="flex-one" v-text="mx.xmmc"></div>
                    <div v-text="mx.ypgg"></div>
                </div>
            </div>
            <div class="syt-bottom flex-container">
                <div class="flex-one" v-text="item.yyffmc"></div>
                <div class="flex-one text-center" v-text="item.pcmc"></div>
                <div class="flex-one text-right" v-text="item.sysd"></div>
            </div>
        </div>
    </div>
</div> -->
<!-- 输液贴打印 end -->

<div class="printFormat">
    <transition name="pop-fade">
        <div class="pophide" :class="{'show':isShow}">
            <div class="popCenter">
                <button class="fa fa-mail-reply-all margin-t-5 tong-btn btn-parmary-b  paddr-r5 printBtu" @click="isShow = false" style="right: 160px"></button>
                <button class="tong-btn btn-parmary-b margin-t-5 paddr-r5 printBtu" @click="print"> 打印</button>
                <button class="tong-btn btn-parmary-b margin-t-5 paddr-r5 printBtu" @click="goOnPrint" style="right: 80px"> 续打</button>
                <div class="cqyzd" v-for="(itemList, index) in jsonList">
                    <div class="yzdTitle" :class="{'goPrintHide': isGoPrint}"><span v-text="title"></span>医嘱执行单</div>
                    <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
                        <div>
                            <span>科别:</span>
                            <span>{{itemList.ryksmc}}</span>
                        </div>
                        <div>
                            <span>床号:</span>
                            <span>{{itemList.rycwbh}}</span>
                        </div>
                        <div>
                            <span>姓名:</span>
                            <span>{{itemList.brxm}}</span>
                        </div>
                        <div>
                            <span>性别:</span>
                            <span>{{brxb_tran[itemList.brxb]}}</span>
                        </div>
                        <div>
                            <span>年龄:</span>
                            <span>{{itemList.nl}}{{nldw_tran[itemList.nldw]}}</span>
                        </div>
                        <div>
                            <span>住院号:</span>
                            <span>{{itemList.zyh}}</span>
                        </div>
                    </div>

                    <div class="yzd-table">
                        <table cellspacing="0" cellpadding="0" style="width: 760px">
                            <tr :class="{'goPrintHide': isGoPrint}">
                                <th rowspan="1" colspan="5" style="width: 50%">医嘱起始</th>
                                <th colspan="7" rowspan="2" style="width: 300px">医嘱执行</th>
                            </tr>
                            <tr :class="{'goPrintHide': isGoPrint}">
                                <th colspan="5">药名(剂量、用法)</th>
                            </tr>
                            <tr v-for="(item, $index) in itemList.yzxx"
                                :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                                @click="isChecked = $index">
                                <td v-if="item.xmmc == null" colspan="1" class="yzd-table-blank">
                                    <span>开始时间</span><span v-text="toTime(item.ksrq)"></span>
                                </td>
                                <td v-if="item.xmmc == null" colspan="1" class="yzd-table-blank">
                                    <span>医生姓名</span><span></span>
                                </td>
                                <td v-if="item.xmmc == null" colspan="1" class="yzd-table-blank">
                                    <span>停止时间</span><span></span>
                                </td>
                                <td v-if="item.xmmc == null" colspan="1" class="yzd-table-blank">
                                    <span>医生姓名</span><span></span>
                                </td>
                                <td v-if="item.xmmc == null" colspan="1" class="yzd-table-blank">
                                    <span>护士签字</span><span></span>
                                </td>
                                <td v-if="item.xmmc != null" colspan="5">
                                    <span class="yzd-name">{{item.xmmc}}{{item.jl}}{{item.jldwmc}}</span>
                                    <span :class="[{'sameStart': sameSE($index, index) == 'start'},
                                    {'sameEnd': sameSE($index, index) == 'end'},{'same': sameSE($index, index) == 'all'}]"></span>
                                    <label style="float: right;width: 50%;">
                                        <span class="yzd-way" v-show="isShowItem(index, $index)"
                                              v-text="item.yyffmc + '  ' + item.sysd + item.sysddw"></span>
                                        <span class="yzd-sm" v-text="item.yysm"></span>
                                        <span style="margin-left: 20px;width: auto" v-show="item.yyffmc == '皮试'">结果(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span>
                                        <span style="margin-left: 20px;width: auto"
                                              v-show="item.yyffmc != '皮试' && isShowItem(index, $index)"
                                              v-text="item.pcmc+'  '+item.tyxx"></span>
                                    </label>
                                </td>
                                <td class="yzd-table-blank">
                                    <span>时间</span>
                                    <span>签字</span>
                                </td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                            </tr>
                            <tr v-for="item in blankList" class="blank-list">
                                <td colspan="5"></td>
                                <td class="yzd-table-blank">
                                    <span>时间</span>
                                    <span>签字</span>
                                </td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                                <td class="yzd-table-blank"><span></span><span></span></td>
                            </tr>
                        </table>
                    </div>

                    <div class="ysDiv" style="position: relative">
                        <div class="yzd-ysInfo">
                            <div>
                                <span>主管医生:</span>
                                <span>{{itemList.zyysxm}}</span>
                            </div>
                            <div>
                                <span>护士:</span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</div>
    <div class="pop pslr pop-table" v-show="isShow">
        <div class="pophide" :class="{'show':isShow}"></div>
        <div class="zui-form podrag pop-850 bcsz-layer padd-b-15  flex-dir-c" :class="{'flex-container':isShow}"  style="height: 500px">
            <div class="layui-layer-title text-left">皮试结果录入</div>
            <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close"  @click="isShow = false"></a></span>
            <div class="layui-layer-content flex-container flex-dir-c flex-one" style="margin-bottom: 56px;">
                <div class=" layui-mad layui-height  flex-container flex-dir-c flex-one">
                    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-m">组号</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l text-left">项目名称</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell  cell-s">药品规格</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">单次剂量</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">剂量单位</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">用药方法</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">皮试结果</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">皮试人员</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s">皮试时间</div>
                                    </th>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one over-auto" data-no-change  @scroll="scrollTable($event)">
                            <table class="zui-table ">
                                <tbody>
                                <tr @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()" v-for="(item, $index) in psYzList"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                                    <td>
                                        <div  class="zui-table-cell  cell-m " v-text="item.fzh"></div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-l text-left" v-text="item.xmmc"></div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s" v-text="item.ypgg"></div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s" v-text="item.dcjl"></div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s" v-text="item.jldwmc"></div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s" v-text="item.yyffmc"></div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s" over-auto>
                                            <select-input @change-data="resultChange_save" :not_empty="false" :child="psjg_tran"
                                                          :index="'item.psjg'" :val="item.psjg" :name="$index + '.psjg'"
                                                          :search="true">
                                            </select-input>
                                        </div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s" v-text="item.pslrry">片</div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell cell-s" v-text="fDate(item.pslrsj,'yyyy-MM-dd')">片</div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<script type="text/javascript" src="yzzxd.js"></script>
<style type="text/css" media="print">
    .pophide{
        position: static;
        width: auto;
        height: auto;
    }
</style>