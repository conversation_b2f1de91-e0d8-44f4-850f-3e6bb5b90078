(function () {
    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:''
        },
        methods:{
            //过滤
            guolu:function () {
                isTabel.isShow=true;
            }
        }
    });
    // var filter=new  Vue({
    //     el:'.filter',
    //     data:{
    //         isShow:false
    //     },
    //     methods:{
    //         baocun:function () {
    //             this.isShow=false;
    //             malert('保存成功','top','success');
    //         },
    //         guanbi:function () {
    //             this.isShow=false;
    //             malert('取消保存','top','defeadted ');
    //         }
    //     },
    // })
    var isTabel=new Vue({
        el:'#isTabel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isTabelShow:false,
            isShow:false,
            minishow:true,
            isShowpopL:false,
            popContent:{},
            item: 0,
            appNum:[],
            appObj:{},
        },
        created:function () {
            this.append()
        },
        methods:{
            sc: function (index) {
                this.appNum.splice(index,1)
                // for(var i=0;i<this.appNum.length;i++){
                //     if(this.appNum[i].num==index){
                //
                //     }
                // }

            },
            append: function () {
                this.item = this.item + 1
                this.appObj.num=this.item
                this.appNum.push(JSON.parse(JSON.stringify(this.appObj)))
                this.$nextTick(function () {
                    $(".zui-form .lsittext").uicomplete({
                        iskeyup: true
                    });
                })
            },
            tabshow:function () {
                this.isTabelShow=true;
                this.minishow=false;
                pop.dyShow=false;
            },
            showDom:function () {
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.dyShow=false;
                pop.flag=true;
            }
        },
    })
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
                this.isShowpopL=false;
                this.isShow=false;
                malert('删除成功','top','success');
            }

        }
    });
    var waps=new Vue({
       el:'.zui-table-body',
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isFold:false,
            title:'',
            centent:'',
        },
        methods:{
            DelLine:function () {
                pop.isShow=true;
                pop.isShowpopL=true;
                pop.title='系统提示';
                pop.centent='确定删除该项内容吗？'
            },
            //双击编辑
            dbEdit:function () {
                wapse.isFold = true;
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');

            }
        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            title:'',
            centent:'',
            isFold: false,
        },
        methods:{
            // 取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // 确定
            confirms:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('222','top','success');
            }

        }
    });
})()