<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <title>农合参数设置</title>
    <link rel="stylesheet" href="cssz.css"/>
</head>
<body class="skin-default background-f  padd-l-10 padd-r-10">
<div class="flex-container flex-align-c padd-b-10 padd-t-10" id="toolMenu">
    <button class="tong-btn btn-parmary" @click="load"><span class="fa fa-refresh"></span>读取参数</button>
    <button class="tong-btn btn-parmary" @click="save"><span class="fa fa-save"></span>保存</button>
</div>

<div class="cssz" id="cssz">
    <div class="left padd-r-10">
        <div class="bx">
            <div class="flex-container flex-align-c margin-b-10">
                <span>地区编码:</span>
                <input class="zui-input" type="text" v-model="json.hacccode" v-text="json.hacccode"/>
            </div>
            <div class="flex-container flex-align-c margin-b-10">
                <span>医疗机构编码:</span>
                <input  class="zui-input" type="text" v-model="json.nhyljgbm" v-text="json.nhyljgbm"/>
            </div>
            <div class="flex-container flex-align-c margin-b-10">
                <span>接口地址:</span>
                <input class="zui-input" type="text" v-model="json.interfacewww" v-text="json.interfacewww"/>
            </div>
            <div class="flex-container flex-align-c margin-b-10">
                <span>保险类别编码:</span>
                <select-input @change-data="resultChange" :not_empty="false"
                                              :child="bxlbList" :index="'bxlbmc'" :index_val="'bxlbbm'" :val="json.bxlbbm"
                                              :name="'json.bxlbbm'" :search="true">
                </select-input>
            </div>
        </div>
    </div>
    <div class="right padd-t-20">
        <div class="flex-container flex-align-c margin-b-10">
            <span>四舍五入上传项目:</span>
            <input class="zui-input" type="text" />
        </div>
        <div class="flex-container flex-align-c margin-b-10">
            <span>自动上传间隔时间:</span>
            <input class="zui-input" type="text" v-model="json.internerTime" v-text="json.interfacewww"/>
        </div>
        <div class="flex-container flex-align-c margin-b-10">
            <span>多地区编码:</span>
            <input class="zui-input" type="checkbox" v-model="json.ddzbm" v-text="json.ddzbm"/>
        </div>
        <div class="flex-container flex-align-c margin-b-10">
            <span>门诊打印合医信息:</span>
            <input class="zui-input" type="checkbox" v-model="json.dyhyxxMz" v-text="json.dyhyxxMz"/>
        </div>
        <div class="flex-container flex-align-c margin-b-10">
            <span>住院打印合医信息:</span>
            <input class="zui-input" type="checkbox" v-model="json.dyhyxxZy" v-text="json.dyhyxxZy"/>
        </div>
        <div class="flex-container flex-align-c margin-b-10">
            <span>门诊打印农合结算单:</span>
            <input class="zui-input" type="checkbox" v-model="json.dynhjsd" v-text="json.dynhjsd"/>
        </div>
        <div class="flex-container flex-align-c margin-b-10">
            <span>住院打印农合结算单:</span>
            <input class="zui-input" type="checkbox" v-model="json.dynhjsdZy" v-text="json.dynhjsdZy"/>
        </div>
        <div class="flex-container flex-align-c margin-b-10">
            <span>拍照地址:</span>
            <input class="zui-input" type="text" />
        </div>
    </div>
</div>
</body>
<script type="application/javascript" src="cssz.js"></script>
</html>