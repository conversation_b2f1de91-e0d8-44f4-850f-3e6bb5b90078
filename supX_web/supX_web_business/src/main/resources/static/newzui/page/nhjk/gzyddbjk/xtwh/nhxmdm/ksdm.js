/**
 * Created by mash on 2017/10/8.
 */
var ks_toolMenu = new Vue({
    el: '.ks_toolMenu',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        bxlbbm: null,
        hoverIndex1: undefined,
        activeIndex1: undefined,
        bxurl: null,
        searchtext: null
    },
    methods: {
        sschangeDown: function () {
            if (window.event.keyCode == 13) {
                ks_toolMenu.getData();
            }
        },
        getbxlb: function () {
            var param = {bxjk: "001"};
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            ks_toolMenu.bxlbbm = json.d.list[0].bxlbbm;
                            ks_toolMenu.bxurl = json.d.list[0].url;
                            //ks_toolMenu.bxurl = "http://localhost:9005/interface/gzydnh/post";//
                        }
                    } else {
                        malert("保险类别查询失败!" + json.c)
                    }
                });
        },
        // 请求保险类别
        getData: function () {
            var param = {
                page: 1,
                rows: 30,
                parm: ks_toolMenu.searchtext
            };
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + ks_toolMenu.bxurl + "&bxlbbm=" + ks_toolMenu.bxlbbm + "&types=nhdm&method=queryKs&parm=" + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        var res = eval('(' + json.d + ')');
                        ksbmMx.jsonList = res.list;
                    } else {
                        malert(json.c);
                    }
                });
        },
        // 保存项目详情
        save: function (ksbm, nhbm, nhksmc) {
            var param = {
                'page': 1,
                'rows': 30,
                'ksbm': ksbm,
                'nhksbm': nhbm,
                'nhksmc': nhksmc,
                'bxlbbm': ks_toolMenu.bxlbbm
            };
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + ks_toolMenu.bxurl + "&bxlbbm=" + ks_toolMenu.bxlbbm + "&types=nhdm&method=saveKs&parm=" + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        malert("保存科室成功！");
//                        		ks_toolMenu.getData();
                    } else {
                        malert(json.c);
                    }
                });
        },
        // 删除项目详情
        remove: function () {

        },
        //写本地科室编码到农合表
        loadXm: function () {
            var param = {
                page: 1,
                rows: 30,
                bxlbbm: ks_toolMenu.bxlbbm
            };
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + ks_toolMenu.bxurl + "&bxlbbm=" + ks_toolMenu.bxlbbm + "&types=nhdm&method=getKs&parm=" + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        malert("获取科室成功！");
                        ks_toolMenu.getData();
                    } else {
                        malert(json.c);
                    }
                });
        },
        //自动对码（项目名称）
        autoDm: function () {
            var param = {
                page: 1,
                rows: 30
            };
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + ks_toolMenu.bxurl + "&bxlbbm=" + ks_toolMenu.bxlbbm + "&types=nhdm&method=autoDmKs&parm=" + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        malert("自动对码（项目名称）成功！");
                        ks_toolMenu.getData();
                    } else {
                        malert(json.c);
                    }
                });
        },
    }
});
ks_toolMenu.getbxlb();
ks_toolMenu.loadXm();  //锁表头初始化
ks_toolMenu.getData();  //锁表头初始化
var ksbmXm = new Vue({
    el: '.ksbmXm',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        jsonList: [],
        searchCon: [],
        param: {}
    },
    methods: {
        getData: function () {
            var param = {
                bxjk: '001'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                ksbmXm.totlePage = Math.ceil(json.d.total / ksbmXm.param.rows);
                ksbmXm.jsonList = json.d.list;
            });
        },
        checkOne: function () {
            ks_toolMenu.getData();
        }
    }
});
ksbmXm.getData();
var ksbmMx = new Vue({
    el: '.ksbmMx',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        jsonList: [],
        isEdit: null,
        text: null,
        page: {
            page: 1,
            rows: 20,
            total: null
        },
        popContent: {},
        searchCon: {},
        selSearch: -1,
        dg: {page: 1, rows: 5, sort: "", order: "asc", parm: ""},
        them_tran: {},
        them: {'编码': 'bm', '名称': 'mc', '代码': 'dm'}
    },
    updated: function () {
        changeWin()
    },
    methods: {
        edit: function (index) {
            console.log(index);
            this.isEdit = index;
        },
        // 点击进行赋值的操作
        selectOne: function (item, index) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(index, true, 'bxxmmc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                ksbmMx.popContent = item;
                Vue.set(ksbmMx.jsonList[index], 'nhksmc', ksbmMx.popContent['mc']);
                Vue.set(ksbmMx.jsonList[index], 'nhksbm', ksbmMx.popContent['bm']);
                ks_toolMenu.save(ksbmMx.jsonList[index]['ksbm'], ksbmMx.jsonList[index]['nhksbm'], ksbmMx.jsonList[index]['nhksmc']);
                $(".selectGroup").hide();
            }
        },
        changeDown: function (index, event, type) {
            //if (this['searchCon'][this.selSearch] == undefined) return;
            //this.keyCodeFunction(event, 'popContent', 'searchCon');
            this.inputUpDown(event,'searchCon','selSearch')
            this.popContent=this['searchCon'][this.selSearch]
            if (event.code == 'Enter' || event.keyCode == 13 || event.code == 'NumpadEnter') {
                if(ksbmMx.popContent){
                    Vue.set(ksbmMx.jsonList[index], 'nhksmc', ksbmMx.popContent['mc']);
                    Vue.set(ksbmMx.jsonList[index], 'nhksbm', ksbmMx.popContent['bm']);
                    ks_toolMenu.save(ksbmMx.jsonList[index]['ksbm'], ksbmMx.jsonList[index]['nhksbm'], ksbmMx.jsonList[index]['nhksmc']);
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                    this.selSearch=-1
                }else{
                    ks_toolMenu.save(ksbmMx.jsonList[index]['ksbm'], ksbmMx.jsonList[index]['nhksbm'], ksbmMx.jsonList[index]['nhksmc']);
                }
            }
        },
        // 输入内容进行检索
        searching: function (index, add, type) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            ksbmMx.popContent = {};
            this.jsonList[index]['nhksbm']=''
            this.jsonList[index]['nhksbm']=''
            this.page.parm = this.jsonList[index][type];
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,}
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + ks_toolMenu.bxurl + "&bxlbbm=" + ks_toolMenu.bxlbbm + "&types=nhdm&method=queryKs2&parm=" + JSON.stringify(str_param), function (json) {
                    if (json.a == 0) {
                        var res = eval('(' + json.d + ')');
                        if (add) {
                            for (var i = 0; i < res.list.length; i++) {
                                ksbmMx.searchCon.push(res.list[i]);
                            }
                        } else {
                            ksbmMx.searchCon = res.list;
                        }
                        ksbmMx.page.total = res.total;
                        ksbmMx.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert(json.c);
                    }
                });
        }
    }
});

$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});
