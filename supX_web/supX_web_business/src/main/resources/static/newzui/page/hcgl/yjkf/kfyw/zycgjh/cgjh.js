var wrapper=new Vue({
    el: '#wrapper',
    components: {
        'search-table': searchTable,
		
    },
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data:{
		
        pkhShow:false,
		isShowkd:true,
		isShow:true,
		cgList :[],
        searchCon:[],
        selSearch:-1,
        
		zhuangtai: {
		    "0": "未审核",
		    "1": "已审核",
		    "2": "已作废",
		    "3": "未通过",
		},
		cglzt_tran:{
			'1':'已审核',
			'0':'未审核',
			'9':'全部',
		},
        jhlx_tran:{
            '0':'周',
            '1':'月',
            '2':'季',
            '4':'年',
        },
		tablePage: {
		       total: 0,
		       currentPage: 1,
		       pageSize: 10,
		       pageSizes: [10, 20, 50, 100, 200, 500],
		       layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
		       perfect: true
		     },
        table:[
			{ type: 'seq', width: 60,title:'序号', fixed: 'left' },
			{title:'采购单号',width:350,field:'jhdh',fixed:"left"},
            {title:'制单日期',width:150,field:'zdrqmc'},
			{title:'金额',field:'zjehj'},
			{title:'编制方法',field:'bzffmc'},
			{title:'计划类型',field:'jhlxmc'},
            {title:'制单员',field:'cgrymc'},
			{title:'审核人员',field:'shzfryxm'},
            {title:'备注',field:'bzms'},
			{title:'状态',field:'shzfbzmc'},
			
			{ title: '操作', width: 200, slots: { default: 'operate' } },
        ],
		detailtable:[
			{ type: 'seq', width: 60,title:'序号', fixed: 'left' },
			{title:'材料名称',width:350,field:'ypbmmc',fixed:"left"},
            {title:'商品名',field:'ypspm'},
            {title:'材料来源',field:'ypflmc'},
            {title:'规格',field:'ypgg'},
			{title:'生产商',field:'ghdwmc'},
			{title:'原产地',field:'cdmc'},
            {title:'单位',field:'kfdwmc'},
            {title:'医保类型',field:'tclbmc'},
            {title:'上期数量',field:'sqcgsl'},
            {title:'库存上限',field:'kcsx'},
            {title:'库存下限',field:'kcxx'},
            {title:'库存数量',field:'qyzl'},
            {title:'库存金额',field:'qyzje'},
            {title:'上期销量',field:'sqxsl',},
            {title:'本期销量',field:'bqxsl'},
            {title:'计划数量',field:'cgsl'},
            {title:'上次供应商',field:'sqghdwmc'},
            {title:'说明',field:'bz'},
            {title:'基本药物',field:'sbjbyw'},
            {title:'批准文号',field:'pzwh'}
        ],
        popContent:{
			zjehj:0,
            ypbz0:'0',
            dmypbz:'0',
            jslyp:'0',
            bzff:'1',
            jhlx:'1',
            cyypfl:'',
            jxbm:[],
            zlbm:[],
            ghdw:[],
            yfbm:[],
            kfbm:[],
            jxNews:[],
            ypzlNews:[],
            ypcdNews:[],
            yfbmNews:[],
            kfbmNews:[],
        },
		tppopContent:{
			
		},
		searchpopContent:{
			
		},
        jsonList:[],
        which:0,
        ypjx:[],
        ypzl:[],
        yfList:[],
        yfkfList:[],
        ypcd:[],
        modelText:[{text:'计划'},{text:'材料分类'},{text:'供应商'},{text:'来源二级库房'},{text:'来源库房'}],
		param: {
		    shzfbz:'',
		    rows: 10,
		    page: 1,
		    beginrq: null,
		    endrq: null,
		    jhdh: '',
			order:"desc"
		},
		isSh : false,
		isCk:false,
		ypKcList:[],
		ypCgHistoryList:[]
    },
    computed:{
        
    },
    mounted:function () {
		var myDate = new Date();
        var time = new Date();
        var day = ('0' + time.getDate()).slice(-2);
        var month = ('0' + (time.getMonth() + 1)).slice(-2);
        var today = time.getFullYear() + '-' + month + '-' + day ;

        //1.一个月前 month直接减1
        var monthAgo = ('0' + (time.getMonth() )).slice(-2);
        var oneMonAgo = time.getFullYear() + '-' + monthAgo + '-' + day ;


        this.param.beginrq = this.fDate(new Date(), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: this.param.beginrq,
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: this.param.endrq,
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });

        this.popContent.zdy = userId;
        this.popContent.zdymc = userName;
        this.popContent.cgry = userId;
        this.popContent.zdrq = this.fDate(new Date(),'datetime');
		this.getData();
		Vue.set(this.param, 'zfbz', '9');
		sessionStorage.setItem('jhdh','')
    },
	created:function(){
		
	},
    methods:{
		searchEvent :function() {
		     this.tablePage.currentPage = 1
		     this.getData()
		   },
		   handlePageChange :function({ currentPage, pageSize }) {
		     this.tablePage.currentPage = currentPage
		     this.tablePage.pageSize = pageSize
			 this.param.rows = pageSize
		    this.param.page = currentPage
			 
			 
		     this.getData()
		   },
		tabBg: function (page) {
		    //$(".loadPage").load(page + ".html").fadeIn(300);
			window.location.href=page + ".html";
		},
		footerMethod :function() {
		      
		      return this.footerData
		    },
		removeRowEvent :function(row) {
			if (common.openConfirm("您确定要删除该数据?", function () {
				var json = {
				    jhdh: row.jhdh,
					shzfbz:'3'
				};
				wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=shzf', JSON.stringify(json)).then(function (data) {
				    if (data.body.a == "0") {
				        wrapper.getData();
				        malert("删除成功！", 'top', 'success');
				        
				    } else {
				        malert(data.body.c);
				    }
				});
			})) {
			    return false;
			}
		},
		cellClickEvent :function(row) {
			console.log(row.row.jhdh)
			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=cgjhmxN&json='+JSON.stringify(row.row), function (data) {
						        if (data.a == 0) {
									
									data.d.forEach(function (item){
									        				if(item && item.ypbm && item.ypmc){
									        					item.ypbmmc = '【'+item.ypbm+'】'+item.ypmc
									        				}
									                    })
									
						            wrapper.jsonList = data.d;
						        } else {
						            malert(data.c, 'top', 'defeadted');
						        }
						    }, function (error) {
						    });
			
		},
		dbclickFun :function(row) {
			sessionStorage.setItem('jhdh',row.row.jhdh)
			sessionStorage.setItem('isCk',true)
			wrapper.tabBg('cgjhdetail');
		},
		mouseDown:function(event){
			            
		},
		resultChange: function (val) {
				
				Vue.set(this.param, 'zfbz', val[0]);
				if(val[0] =='9'){
					Vue.set(this.param, 'shzfbz', '');
				}else{
					Vue.set(this.param, 'shzfbz', val[0]);
				}
				wrapper.getData();
		},
		getType:function(value,type){
			if(type ==1){
				if(value =='1'){
					return "月计划";
				}else if(value =='2'){
					return "季度计划";
				}else if(value =='3'){
					return "年计划";
				}else{
					return "周计划";
				}
			}else if(type ==2){
				if(value =='1'){
					return "临近区间平均参照法";
				}else if(value =='2'){
					return "材料储备定额参照法";
				}else if(value =='3'){
					return "材料日出库量参照法";
				}else if(value =='4'){
					return "自定义区间参照法";
				}else{
					return "往年同期线性参照法";
				}
			}else{
				if(value =='1'){
					return "已审核";
				}else if(value =='2'){
					return "已作废";
				}else if(value =='3'){
					return "未通过";
				}else{
					return "未审核";
				}
			}
		},
		//作废2018/07/04二次弹窗作废提示
		invalidData: function (row) {
		   
		    if (common.openConfirm("确认作废该条信息吗？", function () {
		        var json = {
		            jhdh: row.jhdh,
					shzfbz:'2'
		        };
		        wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=shzf', JSON.stringify(json)).then(function (data) {
		            if (data.body.a == "0") {
		                wrapper.getData();
		                malert("作废成功！", 'top', 'success');
		                
		            } else {
		                malert(data.body.c);
		            }
		        });
		    })) {
		        return false;
		    }
		},
		getData: function () {
		    common.delayOpenloading({el: '.zui-table-view'});
		   
		    $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=cgjhList&json='+JSON.stringify(this.param), function (data) {
				common.closeLoading()
		            if (data.a == 0) {
						var tpcglist = data.d.list
						
						for (var i = 0; i < tpcglist.length; i++) {
							tpcglist[i].bzffmc = wrapper.getType(tpcglist[i].bzff,1);
							tpcglist[i].jhlxmc = wrapper.getType(tpcglist[i].jhlx,2);
							tpcglist[i].shzfbzmc = wrapper.getType(tpcglist[i].shzfbz,3);
							tpcglist[i].zdrqmc = wrapper.fDate(tpcglist[i].zdrq,'AllDate');
						}
		                wrapper.cgList = tpcglist;
						wrapper.tablePage.total = data.d.total
		                
		                
		            } else {
		                malert(data.c, 'top', 'defeadted');
		                
		            }
		        }, function (error) {
					common.closeLoading()
		        });
		    
		},
		showDetail:function(row){
			sessionStorage.setItem('jhdh',row.jhdh)
			sessionStorage.setItem('isCk',false)
			sessionStorage.setItem('isSh',true)
			wrapper.tabBg('cgjhdetail');
			
		},
		openDetail:function(row){
			sessionStorage.setItem('jhdh',row.jhdh)
			sessionStorage.setItem('isCk',false)
			sessionStorage.setItem('isSh',false)
			wrapper.tabBg('cgjhdetail');
		},
		loadNum: function () {
		    this.num = wrapper.num;
		},
		kdFun: function () {
		    sessionStorage.setItem('jhdh','')
		    sessionStorage.setItem('isCk',false)
			sessionStorage.setItem('isSh',false)
		    wrapper.tabBg('cgjhdetail');
		},
		
    },
})