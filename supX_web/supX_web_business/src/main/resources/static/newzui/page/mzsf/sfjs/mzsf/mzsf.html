<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>门诊收费</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <!--<script type="application/javascript" src="/newzui/pub/js/LodopFuncs.js"></script>-->
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css"/>
    <link href="mzsf.css" rel="stylesheet"/>
    <link href="rydj1.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="skin-default">
<div class="printArea printShow"></div>
<div id="printPage"></div>
<div class="wrapper  padd-l-10 padd-t-10 padd-r-10 padd-b-10  printHide background-f">
    <!--入院登记查询列表视图begin-->
    <div v-cloak id="tableInfo">
        <!--入院登记功能按钮begin-->
        <div class="panel">
            <div class="tong-top flex-container flex-align-c">
                <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="mzjs()">保存[F1]</button>
                <button class="tong-btn btn-parmary-b     " @click="getData">刷新</button>
                <button class="tong-btn btn-parmary-b  " @click="loadbx">保险[F4]</button>
                <button class="tong-btn btn-parmary-b  " @click="printBc">补偿打印</button>
                <button class="tong-btn btn-parmary-b  " @click="history">历史挂号信息</button>
                <span class="padd-r-10">票据打印机:</span>
                <select-input class="wh122" @change-data="printResultChange"
                              :child="printList"
                              :index="'printName'"
                              :index_val="'printId'"
                              :val="currentPrintId"
                              :name="'currentPrintName'"
                              :search="false">
                </select-input>
            </div>
        </div>

    </div>
    <div class="flex-container rightVueAll flex-container" v-cloak>
        <div class=" leftVue  flex-container flex-dir-c">
            <div class="box-border">
                <div class="box-header">本次费用</div>
                <div class="box-contnet">
                    <p>实&emsp;&emsp;收&ensp;<span style="font-size: 20px;font-weight:bold;" class="color-e96509">{{bjyss}}元</span>
                    </p>
                    <p>应&emsp;&emsp;收&ensp;<span style="font-size: 20px;font-weight:bold;" class="color-e96509">{{bjyys}}元</span>
                    </p>
                    <p>找&emsp;&emsp;补&ensp;<span style="font-size: 20px;font-weight:bold;" class="color-e96509">{{bjzbje}}元</span>
                    </p>
                </div>
            </div>
            <div class="box-border">
                <div class="box-header">票据信息</div>
                <div class="box-contnet">
                    <p>起&nbsp;始&nbsp;号&ensp;<span style="font-size: 20px;font-weight:bold;" class="color-e96509">{{pjxxContent.qsh}}</span>
                    </p>
                    <p>结&nbsp;束&nbsp;号&ensp;<span style="font-size: 20px;font-weight:bold;" class="color-e96509">{{pjxxContent.jsh}}</span>
                    </p>
                    <p>现&nbsp;使&nbsp;用&ensp;<span style="font-size: 20px;font-weight:bold;" class="color-e96509">{{pjxxContent.dqsyh}}</span>
                    </p>
                    <p>剩&ensp;&ensp;&ensp;余&ensp;<span style="font-size: 20px;font-weight:bold;" class="color-e96509">{{pjxxContent.fpzs}}张</span>
                    </p>
                </div>
            </div>
            <div class="box-border flex-one flex-container flex-dir-c">
                <div class="box-header">收费信息</div>
                <div class="box-contnet flex-one">
                    <div class="zui-table-view flex-container flex-dir-c" id="brRyList">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>现金支付</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" @scroll="scrollTable($event)" data-no-change>
                            <table class="zui-table table-width50">
                                <!-- v-if="jsonList.length!=0" -->
                                <tbody>
                                <tr :tabindex="$index" v-for="(item, $index) in jsxxJsonList"
                                    class="tableTr2"><!--@dblclick="edit($index)"双击回调-->
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-s">
                                            <span v-text="item.brxm"></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">
                                            <span v-text="item.xjzf"></span>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                        </div>
                        <div>合计&ensp;<span class="color-e96509" style="font-weight: bolder;font-size: 18px;">{{jrsrhj || 0}}元</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zdy-width rightVue flex-container  flex-dir-c">
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">信息录入区</div>
                </div>
                <div class="tab-card-body padd-t-10">
                    <div class="grid-box">
                        <div class="">
                            <span class="color-f2a654 font-14 padd-l-10">{{mzjbxxContent.brxm}}&emsp;</span>
                            <span class="color-354052">{{brxb_tran[mzjbxxContent.brxb]}}&emsp;</span>
                            <span class="color-354052" v-text="mzjbxxContent.brnl"></span>
                            <span class="pad-l-46">医疗卡余额：<span class="color-f2a654 font-14 padd-l-10">{{mzjbxxContent.ylkye}}&emsp;</span></span>
                            <span class="pad-l-46">诊断：<span class="color-f2a654 font-14 padd-l-10">{{mzjbxxContent.jbmc}}</span></span>
                            <span class="color-wtg" v-if="gznhType">
                            人员属性：<span class="font-weight">{{gznhObj.jzmzName}}</span>，
                            本年度门诊补偿金额：<span class="font-weight">{{gznhObj.outpCompensateCost}}</span>元，
                            本年度慢性病补偿金额：<span class="font-weight">{{gznhObj.chroCompensateCost}}</span>元。</span>
                            <div class="flex-container flex-align-c flex-wrap-w ">
                                <div class=" flex-container flex-align-c padd-l-20  margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">卡号序号</span>
                                    <input tabindex="0" autocomplete="off" @keydown="changeDown1($event)"
                                           class="zui-input  wh122"
                                           type="text" v-model.trim.number="brxxContent.ghxh" id="ykth"
                                           data-notEmpty="false"
                                           @keyup="selecIsNo($event)" placeholder="挂号序号"/>
                                    <search-table :message="searchCon1" :selected="selSearch1"
                                                  :them="them1" :them_tran="them_tran1" :page="page"
                                                  @click-one="checkedOneOut" @click-two="selectOne1">
                                    </search-table>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">姓名</span>
                                    <input tabindex="1" class="zui-input text-indent-5 wh122" type="text"
                                           v-model="fzContent.brxm" @keydown.13="resultMzsfChange('brxm')"
                                           placeholder="姓名"/>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">开单科室</span>
                                    <select-input tabindex="3" class="wh122" @change-data="resultMzsfChange"
                                                  :not_empty="true"
                                                  :child="ghksList" :index="'ksmc'" :index_val="'ksbm'"
                                                  :val="fzContent.mzks"
                                                  :search="true"
                                                  :name="'fzContent.mzks'" :index_mc="'mzksmc'">
                                    </select-input>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">开单医生</span>
                                    <select-input tabindex="4" class="wh122" @change-data="resultMzsfChange"
                                                  :not_empty="true"
                                                  :child="mzysList" :search_key="'rybm'" :index="'ryxm'"
                                                  :index_val="'rybm'" :val="fzContent.mzys"
                                                  :search="true"
                                                  :name="'fzContent.mzys'" :index_mc="'mzysxm'">
                                    </select-input>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">费别</span>
                                    <select-input tabindex="5" class="wh122" @change-data="resultMzsfChange"
                                                  :not_empty="true"
                                                  :child="brfbList" :index="'fbmc'" :index_val="'fbbm'"
                                                  :val="fzContent.ryfbbm"
                                                  :name="'fzContent.ryfbbm'" :search="true"
                                                  :index_mc="'ryfbmc'">
                                    </select-input>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right" :id="a">保险</span>
                                    <select-input tabindex="6" class="wh122" @change-data="resultChange"
                                                  :not_empty="false"
                                                  :child="bxlbList" :index="'bxlbmc'" :index_val="'bxlbbm'"
                                                  :val="fzContent.rybxlbbm"
                                                  :search="true"
                                                  :name="'fzContent.rybxlbbm'" :index_mc="'rybxlbmc'">
                                    </select-input>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">费用项目</span>
                                    <input tabindex="7" autocomplete="off" class="zui-input wh122"
                                           data-notEmpty="true" :value="brfyContent.text"
                                           @keyup="changeDown($event,'text')"
                                           @input="change(null,$event.target.value)" id="mxxm">
                                    <search-table :message="searchCon" :selected="selSearch"
                                                  :page="page"
                                                  :them="them" :them_tran="them_tran"
                                                  @click-one="checkedOneOut" @click-two="selectOne">
                                    </search-table>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right" :id="lxzdwybListener">核算科室</span>
                                    <select-input tabindex="8" class="wh122" @change-data="resultChange"
                                                  :not_empty="false" :child="hsksList"
                                                  :index="'ksmc'" :index_val="'ksbm'" :val="fzContent.zxks"
                                                  :name="'fzContent.zxks'" :search="true">
                                    </select-input>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right"
                                          :id="lxnhListener">处方号</span>
                                    <input tabindex="9" type="text" class="zui-input wh122"
                                           v-model="brfyContent.yzhm" @keydown="changeDown($event,'yzhm')"
                                           data-notEmpty="false"
                                           id="yzhm" :disabled="yzhm"/>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">单价</span>
                                    <div class="position">
                                        <input tabindex="10" @mousewheel.prevent @keydown.up.prevent
                                               @keydown.down.prevent class="zui-input wh122" type="number"
                                               v-model="brfyContent.fydj"
                                               @keydown="changeDown($event,'fydj',brfyContent.fydj)" :disabled="fydj"
                                               id="fydj"
                                               data-notEmpty="false"/>
                                        <span class="cm">元</span>
                                    </div>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">数量</span>
                                    <input @mousewheel.prevent @keydown.up.prevent
                                           tabindex="11" type="number" @mousewheel.prevent
                                           @keydown.down.prevent min="0" class="zui-input wh122"
                                           v-model="brfyContent.fysl"
                                           @keydown="changeDown($event,'fysl',brfyContent.fysl)"
                                           data-notEmpty="true" id="fysl"/>
                                </div>
                                <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                                    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">金额</span>
                                    <div class="position">
                                        <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                                               tabindex="12" type="number" class="zui-input wh122" v-model="sum"
                                               data-notEmpty="false" disabled/><span class="cm">元</span>
                                    </div>
                                </div>
								
								<div class=" flex-container flex-align-c padd-l-20 margin-b-10">
								    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right" :id="a">是否个账</span>
								    <select-input class="wh120" @change-data="resultChange" :not_empty="false"
								                  :child="sfgzList"
								                  :index="rysfgz" :val="rysfgz" :name="'rysfgz'">
								    </select-input>
								</div>
								
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-card   flex-dir-c flex-container flex-one">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">信息显示区</div>
                </div>
                <div class="tab-card-body  flex-dir-c flex-container flex-one ">
                    <div class=" zui-table-view  flex-dir-c  padd-l-10 flex-container " id="brRyList01">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-l text-left"><span>医嘱号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xxl text-left"><span>明细费用</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>组合费用</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>数量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>金额</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>优惠比例</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>优惠金额</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>核算科室</span></div>
                                    </th>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>操作</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body over-auto flex-one flex-container setScroll" data-no-change
                             @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <!-- v-if="jsonList.length!=0" -->
                                <tbody>
                                <tr :tabindex="$index" v-for="(item, $index) in brfyjsonList"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()"
                                    @click="checkSelect([$index,'some','brfyjsonList'],$event)"
                                    @dblclick="doPop($index)"
                                    class="tableTr2">
                                    <td>
                                        <div class="zui-table-cell  cell-l ">
                                            {{item.yzhm}}
                                        </div>
                                        <span v-if="item.class" :class="item.class"></span>
                                        <span v-if="item.yzhmHj" class="color-wtg yzhmHj fa-play"
                                              :style="item.trNums | dragStyle">{{item.yzhmHj}}元</span>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell text-over-2 cell-xxl text-left">
                                            {{item.mxfyxmmc}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell text-over-2 cell-s">
                                            {{item.zhfymc}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">
                                            {{item.fysl}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">
                                            {{item.fydj}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell title position cell-s">
                                            {{item.fyje}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">
                                            {{item.yhbl}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">
                                            {{item.yhje}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">
                                            {{item.zxksmc}}
                                        </div>
                                    </td>
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m flex-center">
                                            <span class="width30 icon-sc icon-font" @click="remove($index)"
                                                  data-title="删除"></span>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                        </div>
                        <div class="zui-table-fixed table-fixed-r">
                            <div class="zui-table-header">
                                <table class="zui-table">
                                    <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>操作</span></div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" data-no-change @scroll="scrollTableFixed($event)">
                                <table class="zui-table">
                                    <tbody>
                                    <tr :tabindex="$index" v-for="(item, $index) in brfyjsonList"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                        @click="checkSelect([$index,'some','brfyjsonList'],$event)"
                                        @dblclick="doPop($index)" class="tableTr2">
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m flex-center">
                                                <span class="width30 icon-sc icon-font" @click="remove($index)"
                                                      data-title="删除"></span>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mzsf-fyje padd-r-10 addList  flex-align-c zui-border-bottom flex-container font-14-654">
                <div class="font-18 font-weight">合计&ensp;<span class="color-e96509  ">{{yjhj}}元</span></div>
                <div class=" font-18 font-weight padd-l-45">本次合计: <span class="color-e96509">{{bchj}}元</span></div>
            </div>

        </div>
    </div>
    <div id="popCenter" v-cloak>
        <div class="popshow" v-if="isShow"></div>
        <div v-if="isShow" class="zui-form podrag bcsz-layer  flex-container flex-dir-c"
             style="width: auto;overflow: hidden;top: 80px;bottom: 50px; height: auto; left: 100px; right: 100px">
            <div class="layui-layer-title">请输入参合人员信息</div>
            <span class="layui-layer-setwin">
                <a @click="isShow=false" class="closex ti-close" href="javascript:;"></a>
            </span>
            <div class="layui-layer-content flex-container flex-dir-c flex-one">
                <div class="layui-height flex-container flex-dir-c flex-one " id="loadPage">

                </div>
            </div>
        </div>
    </div>

<div id="gjpopCenter" v-cloak>
        <div class="popshow" v-if="isShow"></div>
        <div v-if="isShow" class="zui-form podrag bcsz-layer  flex-container flex-dir-c"
             style="width: auto;overflow: hidden;top: 80px;bottom: 50px; height: auto; left: 100px; right: 100px">
            <div class="layui-layer-title">请输入共济人员信息</div>
            <span class="layui-layer-setwin">
                <a @click="guanbi()" class="closex ti-close" href="javascript:;"></a>
            </span>
            <div class="layui-layer-content flex-container flex-dir-c flex-one">
                <div class="layui-height flex-container flex-dir-c flex-one " id="loadPage1">

                </div>
            </div>
        </div>
    </div>


    <model v-cloak class="popCenter1" :model-show="true" @result-close="quxiao" :s="'保存'" :c="cancelText"
           @default-click="success" @result-clear="quxiao"
           v-if="isShow" :title="'收费结算'" >
        <div class="bqcydj_model ">
            <div class="flex-container flex-wrap-w    lineDiv">
                <div class="flex-container  padd-r-10 flex-jus-c flex-align-c margin-b-10">
                    <span class="whiteSpace font-14 padd-r-5 wh120 text-right">支付方式</span>
                    <select-input class="wh122" @change-data="resultChange" :data-notEmpty="true"
                                  :child="zflxList" :index="'zflxmc'" :index_val="'zflxbm'"
                                  :val="jsjlContent.zflxbm" :search="true"
                                  :name="'jsjlContent.zflxbm'" :index_mc="'zflxmc'">
                    </select-input>
                </div>
                <div class="flex-container padd-r-10  flex-jus-c flex-align-c margin-b-10">
                    <span class="whiteSpace font-14 padd-r-5 wh120 text-right">发票张数</span>
                    <input type="text" class="zui-input wh122 background-h" disabled="disabled" value="1张"/>
                </div>
                <div class="flex-container  padd-r-10 flex-jus-c flex-align-c margin-b-10">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">应收金额</span>
                    <input disabled type="number" class="zui-input wh122" v-model="jsjlContent.ysje"/>
                </div>
                <div class="flex-container padd-r-10  flex-jus-c flex-align-c margin-b-10">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">优惠金额</span>
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                           :disabled="csqxContent.N05001200338 != '1'"
                           @keydown="nextFocus($event)" min="0" value="0" class="zui-input wh122"
                           @input="yhhj(jsjlContent.yhhj,$event)" v-model="jsjlContent.yhhj"/>
                </div>
                <div class="flex-container  padd-r-10 flex-jus-c flex-align-c margin-b-10">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">费用合计</span>
                    <input type="number" disabled class="zui-input wh122" v-model="jsjlContent.fyhj"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10  flex-jus-c flex-align-c">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">现金支付</span>
                    <input type="number" disabled class="zui-input wh122" v-model="jsjlContent.xjzf"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10  flex-jus-c flex-align-c">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">医疗卡支付</span>
                    <input type="number" disabled class="zui-input wh122" v-model="jsjlContent.ylkzf"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10  flex-jus-c flex-align-c"
                     v-if="csqxContent.N05001200335 == '1' && bxlbbm== '07'">
                    <span class="whiteSpace padd-r-5 font-14 color-wtg wh120 text-right">大病商保</span>
                    <input type="number" disabled class="zui-input wh122" v-model="jsjlContent.insureCost"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10  flex-jus-c flex-align-c"
                     v-if="csqxContent.N05001200335 == '1' && bxlbbm== '07'">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">计生救助</span>
                    <input type="number" disabled class="zui-input wh122" v-model="jsjlContent.salvaJSCost"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10  flex-jus-c flex-align-c"
                     v-if="csqxContent.N05001200335 == '1' && bxlbbm== '07'">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">民政优抚</span>
                    <input type="number" disabled class="zui-input wh122" v-model="jsjlContent.civilCost"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10  flex-jus-c flex-align-c"
                     v-if="csqxContent.N05001200335 == '1' && bxlbbm== '07'">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">精准目录补偿</span>
                    <input type="number" disabled class="zui-input wh122" v-model="jsjlContent.medicineCost"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10 flex-jus-c flex-align-c">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">保险卡支付</span>
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                           class="zui-input wh122"
                           v-model="jsjlContent.ybzhzf" id='ybzhzf'/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10  flex-jus-c flex-align-c">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">统筹支付</span>
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                           class="zui-input wh122"
                           v-model="jsjlContent.ybtczf" id='ybtczf'/>
                </div>
                <div class="flex-container padd-r-10  margin-b-10 flex-jus-c flex-align-c">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">其他支付</span>
                    <!--@keydown.enter="zjjs"-->
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                           class="zui-input wh122"
                           @keydown="nextFocus($event)" v-model="jsjlContent.qtzf" @input="qtzfChange($event)"/>
                </div>
                <div class="flex-container  padd-r-10 margin-b-10 flex-jus-c flex-align-c">
                    <span class="color-wtg padd-r-5 whiteSpace font-14 wh120 text-right">实收金额</span>
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                           class="zui-input font-18 wh122 font-weight color-wtg"
                           id="ssje" @input="ssjeChange"
                           @keydown.enter="zjjs"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10  flex-jus-c flex-align-c">
                    <span class="color-wtg padd-r-5 whiteSpace font-14 wh120 text-right">找补金额</span>
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                           class="zui-input font-18 wh122 font-weight color-wtg"
                           id="zbje" disabled="disabled"/>
                </div>
                <div class="flex-container padd-r-10  margin-b-10 flex-jus-c flex-align-c">
                    <span class="color-wtg padd-r-5 whiteSpace font-14 wh120 text-right">请扫码</span>
                    <input autocomplete="new-password" :data-code="codeContentFun" @keydown="clearLoadFun()"
                           @blur="blurFun($event)" type="password"
                           class="zui-input font-weight font-18 color-wtg hidden wh122"
                           v-model="jsjlContent.codeContent" id="codeContent"/>
                </div>
                <div class="flex-container  padd-r-10 flex-jus-c flex-align-c margin-b-10">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">医保误差</span>
                    <input type="number" disabled class="zui-input wh122" v-model="jsjlContent.ybfyhj"/>
                </div>
                <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                    <!-- 复选框 + 券金额 -->
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" v-model="jsjlContent.useCoupon"
                               :disabled="Number(jsjlContent.ybtczf) !== 0 || Number(jsjlContent.ybzhzf) !== 0"
                               style="margin-right: 5px;" />
                        <span class="whiteSpace padd-r-5 wh100 text-right font-14">券金额</span>
                    </label>
                    <input type="number"  @mousewheel.prevent @keydown.up.prevent
                           @keydown.down.prevent @input="quanChange" class="zui-input wh122"
                           v-model="jsjlContent.couponAmount" :disabled="!jsjlContent.useCoupon" />
                </div>
                <!-- 券优惠 -->
                <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                    <span class="whiteSpace padd-r-5 font-14 wh120 text-right">券优惠</span>
                    <input type="number" class="zui-input wh122" v-model="jsjlContent.couponPrice" disabled />
                </div>
            </div>
        </div>
        <!-- <div class="flex-container   flex-jus-c flex-align-c" slot="footer">
            <button class="zui-btn table_db_esc btn-default xmzb-db" v-if="csqxContent.cs05001200324 !='008'"
                    @click="postApply">银联支付
            </button>
            <button class="zui-btn table_db_esc btn-default xmzb-db" v-if="csqxContent.cs05001200327 != '008'"
                    @click="wxApply()">微信支付
            </button>
            <button class="zui-btn table_db_esc btn-default xmzb-db" v-if="csqxContent.cs05001200328 != '008'"
                    @click="zfbApply()">支付宝支付
            </button>
        </div>-->
    </model>

</div>
<div class="popPf pop-table printHide">
    <div class="pophide" :class="{'show':ishow}"></div>
    <div class="zui-form podrag pop-850 bcsz-layer   flex-dir-c" :class="{'flex-container':ishow}"
         style="height: 500px">
        <div class="layui-layer-title text-left">药品配方查询</div>
        <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close" @click="ishow=false"></a></span>
        <div class="layui-layer-content flex-container flex-dir-c flex-one">
            <div class=" layui-mad layui-height  flex-container flex-dir-c flex-one">
                <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-s">统筹类别</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">范围</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-xl text-left">药品名称</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">规格</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">零价</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用量</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">用法</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">处方号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">分装比例</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">产地</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">生产日期</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">有效期至</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body flex-one over-auto" data-no-change @scroll="scrollTable($event)">
                        <table class="zui-table ">
                            <tbody>
                            <tr @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.tclbmc"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.fw =='0' ? '保外':'保内'"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-xl text-over-2 text-left">
                                        <span v-text="item.ypmc"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.ypgg"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.yplj"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.cfyl"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.yfdwmc"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.yyffmc"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.cfh"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.fzbl"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="item.cdmc"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="fDate(item.scrq,'date')"></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <span v-text="fDate(item.yxqz,'date')"></span>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="side-form pop flex-container flex-dir-c printHide" :class="{'ng-hide':!isFold}" v-cloak id="brzcList"
     role="form">
    <div class="fyxm-side-top flex-jus-sb">
        <span>挂号记录</span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="grid-box">
        <div class="flex-container margin-l-10">
            <div class="flex-container margin-b-15 margin-top20 margin-l-10">
                <input type="text" name="phone" class="zui-input  wh120 margin-l13 padd-r-10" v-model="jsValue"
                       placeholder="请填写关键字"
                       @keydown.enter="goToPage(1)"/>
            </div>
            <div class="flex-container flex-align-c  margin-b-15 margin-top20 margin-l-10">
                <input @click="showTime('dbegin','ksrq')" v-model="ksrq" id="dbegin" class="zui-input wh112">
                <span class="padd-l-10 padd-r-10">至</span>
                <input @click="showTime('dEnd','jsrq')" id="dEnd" v-model="jsrq" class="zui-input wh112 margin-r-10">
                <button class="tong-btn btn-parmary" @click="getData">查询</button>
            </div>
        </div>
    </div>
    <div class="zui-table-view  flex-container flex-dir-c ">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th>
                        <div class="zui-table-cell cell-l">挂号序号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">患者姓名</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m">性别</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">挂号科室</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">挂号医生</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">患者年龄</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">挂号时间</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">是否退号</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @dblclick="edit($index,item)"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.ghxh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-m" v-text="brxb_tran[item.brxb]"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ghksmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jzysxm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.brnl}}{{nldw_tran[item.nldw]}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.ghrq, 'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-S">{{item.thbz==0?'否':'是'}}</div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="zui-table-fixed table-fixed-l">
            <!-- 有浮动就加 table-fixed-r -->
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!--@click="hover($index,$event)"-->
            <div class="zui-table-body " @scroll="scrollTableFixed($event)" style="border-right: 1px solid #eee;">
                <table class="zui-table">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                        v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        class="tableTr2 table-hovers-filexd-l">
                        <td class="cell-l">
                            <div cell="cell-2-0" class="zui-table-cell cell-l">{{item.ghxh}}</div>
                        </td>
                    </tr>
                    </tbody>

                </table>
            </div>
        </div>
        <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
              :next-more="nextMore"></page>
    </div>
</div>
</body>
<script src="backFun.js" type="text/javascript"></script>
<script src="mzsf.js?v=20210322" type="text/javascript"></script>
</html>
