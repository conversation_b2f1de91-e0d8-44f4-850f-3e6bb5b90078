  window.gz_001 = new Vue({
        el: '.gz_001',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable,
            'search-table2': searchTable,
            'search-table3': searchTable,
            'search-table4': searchTable,
            'search-table5': searchTable,
            'search-table6': searchTable,
            'search-tableZdjb': searchTable,
        },
        data: {
        	yisContent: {},//预结算数据2(贵州移动农合)
        	yjsContent2: {},
            yjsContent3:{},//预结算返回信息(贵州移动农合)
        	bxcw: false,//保险是否有错
        	nhsjContent1: {},//农合参合人员信息
        	nhsjContent2: {},//农合窗口填写信息
            bxlbbm: null,
            bxurl: null,
            billCode: null,
            isShow: false,
            chh: null,
            InfoList: [],
            popContent: {
                sfzz:'0',
                sftg:'0',
                sfkjtzh:'0',
                sycs:0,
                jzlx:"1",
                rylx:'1',
                zzlx:'0'
            },
            jyList: [],
            them_tran: {
                'mbbz':dic_transform.data.istrue_tran,
            },
            them: {
                '疾病编码': 'bm',
                '疾病名称': 'mc',
                '代码': 'dm',
                '慢病': 'mbbz',
            },
            jbContent: {},
            jbContent2: {},
            jbContent3: {},
            jbContent4: {},
            jbContent5: {},
            jbContent6: {},
            jbContentZdjb:{},
            searchCon: [],
            selSearch: -1,
            searchCon2: [],
            searchCon3: [],
            searchCon4: [],
            searchCon5: [],
            searchCon6: [],
            searchConZdjb: [],
            page: {
                page: 1,
                rows: 10,
                total: null
            },
            rztg: false,
            outpid:null,//门诊补偿号
            fycfh: [],//病人药品费用处方号集合
            fybm: [],//病人费用项目编码集合,
            scfyList:[],//上传费用集合
            bcdxxContent:{},//补偿单
            FpPrintContent:{},//打印内容
            details:[],
            qtzdList:[], // 其他诊断
            inpid:'',
            yjsContent:{},
            jtdz: null,
            rylx: null,
            zyts: 0,
        },
        mounted: function () {
            this.isShow = true;
            $('#chh').focus()
        },
        created: function () {
        	console.log(rightVue.mzjbxxContent);
        	if(rightVue.mzjbxxContent.jbbm && rightVue.mzjbxxContent.jbmc){
            	this.jbContent.jbmc = rightVue.mzjbxxContent.jbmc;
            	this.jbContent.jbbm = rightVue.mzjbxxContent.jbbm;
            	this.popContent.jbbm = rightVue.mzjbxxContent.jbbm;
            }
        	this.popContent.rylx = '1';
        	this.popContent.lxdh = rightVue.mzjbxxContent.lxrdh;
        },
        methods: {
            reCheckOne:function(val){
                var name=val[0].split('.')
                Vue.set(this.popContent, name[1], val[1])
            },
            getChzh: function () {
                if (rightVue.mzjbxxContent.ybkh != null && rightVue.mzjbxxContent.ybkh != undefined) {
                    gz_001.chh = rightVue.mzjbxxContent.ybkh;
                }
            },

            getbxlb: function () {
                var param = {bxjk: "001"};
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {

                        if (json.a == 0) {
                            if (json.d.list.length > 0){
                                gz_001.bxlbbm = json.d.list[0].bxlbbm;
                                gz_001.bxurl = json.d.list[0].url;//"http://localhost:9005/interface/gzydnh/post";//
                                //gz_001.bxurl = "http://localhost:9005/interface/gzydnh/post";//
                                gz_001.getS02();
                                //gz_001.getnhks();
                            }
                        } else {
                            malert("保险类别查询失败!" + json.c,'right')
                        }
                    });
            },
            getnhks: function () {
                var param = {
                    page: 1,
                    rows: 30,
                    ksbm:rightVue.mzjbxxContent.ghks
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=nhdm&method=queryKs&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            ksbmMx.jsonList = res.list;
                        } else {
                            malert(json.c);
                        }
                    });
            },
            getS02: function () {
                var head = {
                    operCode: "S02",
                    rsa: ""
                };
                var body = {
                    userName: "",
                    passWord: ""
                };
                var param = {
                    head: head,
                    body: body
                };
                var str_param = JSON.stringify(param);

                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                        if (json.a == 0) {
                            gz_001.billCode = json.d;
                            gz_001.rztg = true;
                        } else {
                            malert("认证鉴权失败，请从新操作",'right','defeadted');
                            gz_001.rztg = false;
                        }
                    });
            },
            changeDown: function (event, type) {
                if (this['searchCon'][this.selSearch] == undefined) return;
                this.keyCodeFunction(event, 'jbContent', 'searchCon');
                if (event.code == 'Enter' || event.code == 13) {
                    if (type == "text") {
                        Vue.set(this.jbContent, 'jbmc', this.jbContent['mc']);
                        this.jbContent.bm = this.jbContent.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                        gz_001.popContent.jbbm = this.jbContent.bm;
                        this.selSearch = 0;
                        this.nextFocus(event);
                    }
                }
            },
            changeDown2: function (event, type) {
                this.keyCodeFunction(event, 'jbContent2', 'searchCon2');
                if (event.code == 'Enter' || event.code == 13) {
                    if (type == "text") {
                        Vue.set(this.jbContent2, 'qtzd1mc', this.jbContent2['mc']);
                        this.jbContent2.bm = this.jbContent2.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                        gz_001.popContent.qtzd1 = this.jbContent2.bm;
                        this.selSearch = 0;

                        var qtzd={
                        		mc:this.jbContent2['mc'],
                        		bm:this.jbContent2.bm
                        }
                        gz_001.qtzdList.push(qtzd);

                        this.nextFocus(event);
                    }
                }
            },
            changeDown3: function (event, type) {
                this.keyCodeFunction(event, 'jbContent3', 'searchCon3');
                if (event.code == 'Enter' || event.code == 13) {
                    if (type == "text") {
                        Vue.set(this.jbContent3, 'qtzd2mc', this.jbContent2['mc']);
                        this.jbContent3.bm = this.jbContent3.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                        gz_001.popContent.qtzd2 = this.jbContent3.bm;
                        this.selSearch = 0;

                        var qtzd={
                        		mc:this.jbContent3['mc'],
                        		bm:this.jbContent3.bm
                        }
                        gz_001.qtzdList.push(qtzd);

                        this.nextFocus(event);
                    }
                }
            },
            changeDown4: function (event, type) {
                this.keyCodeFunction(event, 'jbContent4', 'searchCon4');
                if (event.code == 'Enter' || event.code == 13) {
                    if (type == "text") {
                        Vue.set(this.jbContent4, 'qtzd3mc', this.jbContent4['mc']);
                        this.jbContent4.bm = this.jbContent4.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                        gz_001.popContent.qtzd3 = this.jbContent4.bm;
                        this.selSearch = 0;

                        var qtzd={
                        		mc:this.jbContent4['mc'],
                        		bm:this.jbContent4.bm
                        }
                        gz_001.qtzdList.push(qtzd);

                        this.nextFocus(event);
                    }
                }
            },
            changeDown5: function (event, type) {
                this.keyCodeFunction(event, 'jbContent5', 'searchCon5');
                if (event.code == 'Enter' || event.code == 13) {
                    if (type == "text") {
                        Vue.set(this.jbContent5, 'qtzd4mc', this.jbContent2['mc']);
                        this.jbContent5.bm = this.jbContent5.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                        gz_001.popContent.qtzd4 = this.jbContent5.bm;
                        this.selSearch = 0;

                        var qtzd={
                        		mc:this.jbContent5['mc'],
                        		bm:this.jbContent5.bm
                        }
                        gz_001.qtzdList.push(qtzd);

                        this.nextFocus(event);
                    }
                }
            },
            changeDown6: function (event, type) {
                this.keyCodeFunction(event, 'jbContent6', 'searchCon6');
                if (event.code == 'Enter' || event.code == 13) {
                    if (type == "text") {
                        Vue.set(this.jbContent6, 'qtzd5mc', this.jbContent6['mc']);
                        this.jbContent6.bm = this.jbContent6.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                        gz_001.popContent.qtzd5 = this.jbContent6.bm;
                        this.selSearch = 0;

                        var qtzd={
                        		mc:this.jbContent6['mc'],
                        		bm:this.jbContent6.bm
                        }
                        gz_001.qtzdList.push(qtzd);

                        this.nextFocus(event);
                    }
                }
            },
            changeDownzdjb: function (event, type) {
                this.keyCodeFunction(event, 'jbContentZdjb', 'searchConZdjb');
                if (event.code == 'Enter' || event.code == 13) {
                    if (type == "text") {
                        Vue.set(this.jbContentZdjb, 'zdjbmc', this.jbContentZdjb['mc']);
                        this.jbContentZdjb.bm = this.jbContentZdjb.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                        gz_001.popContent.zdjbbm = this.jbContentZdjb.bm;
                        this.selSearch = 0;
                        this.nextFocus(event);
                    }
                }
            },
            searching: function (add, type, val) {
                this.jbContent['jbmc'] = val;
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.jbContent['jbmc'] == undefined || this.jbContent['jbmc'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbContent['jbmc'];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=jbbm&method=query&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var date = null;
                            var res = eval('(' + json.d + ')');
                            if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                for (var i = 0; i < res.list.length; i++) {
                                    gz_001.searchCon.push(res.list[i]);
                                }
                            } else {
                                gz_001.searchCon = res.list;
                            }
                            gz_001.page.total = res.total;
                            gz_001.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert("查询失败  " + json.c,'right','defeadted');
                        }
                    });
            },
            searching2: function (add, type) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.jbContent2[type] == undefined || this.jbContent2[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbContent2[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=jbbm&method=query&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var date = null;
                            var res = eval('(' + json.d + ')');
                            if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                for (var i = 0; i < res.list.length; i++) {
                                    gz_001.searchCon2.push(res.list[i]);
                                }
                            } else {
                                gz_001.searchCon2 = res.list;
                            }
                            gz_001.page.total = res.total;
                            gz_001.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert("查询失败  " + json.c,'right','defeadted');
                        }
                    });
            },
            searching3: function (add, type) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.jbContent3[type] == undefined || this.jbContent3[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbContent3[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=jbbm&method=query&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var date = null;
                            var res = eval('(' + json.d + ')');
                            if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                for (var i = 0; i < res.list.length; i++) {
                                    gz_001.searchCon3.push(res.list[i]);
                                }
                            } else {
                                gz_001.searchCon3 = res.list;
                            }
                            gz_001.page.total = res.total;
                            gz_001.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert("查询失败  " + json.c,'right','defeadted');
                        }
                    });
            },
            searching4: function (add, type) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.jbContent4[type] == undefined || this.jbContent4[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbContent4[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=jbbm&method=query&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var date = null;
                            var res = eval('(' + json.d + ')');
                            if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                for (var i = 0; i < res.list.length; i++) {
                                    gz_001.searchCon4.push(res.list[i]);
                                }
                            } else {
                                gz_001.searchCon4 = res.list;
                            }
                            gz_001.page.total = res.total;
                            gz_001.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert("查询失败  " + json.c,'right','defeadted');
                        }
                    });
            },
            searching5: function (add, type) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.jbContent5[type] == undefined || this.jbContent5[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbContent5[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=jbbm&method=query&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var date = null;
                            var res = eval('(' + json.d + ')');
                            if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                for (var i = 0; i < res.list.length; i++) {
                                    gz_001.searchCon5.push(res.list[i]);
                                }
                            } else {
                                gz_001.searchCon5 = res.list;
                            }
                            gz_001.page.total = res.total;
                            gz_001.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert("查询失败  " + json.c,'right','defeadted');
                        }
                    });
            },
            searching6: function (add, type) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.jbContent6[type] == undefined || this.jbContent6[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbContent6[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=jbbm&method=query&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var date = null;
                            var res = eval('(' + json.d + ')');
                            if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                for (var i = 0; i < res.list.length; i++) {
                                    gz_001.searchCon6.push(res.list[i]);
                                }
                            } else {
                                gz_001.searchCon6 = res.list;
                            }
                            gz_001.page.total = res.total;
                            gz_001.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert("查询失败  " + json.c,'right','defeadted');
                        }
                    });
            },
            searchingzdjb: function (add, type) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.jbContentZdjb[type] == undefined || this.jbContentZdjb[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbContentZdjb[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=jbbm&method=query&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var date = null;
                            var res = eval('(' + json.d + ')');
                            if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                for (var i = 0; i < res.list.length; i++) {
                                    gz_001.searchConZdjb.push(res.list[i]);
                                }
                            } else {
                                gz_001.searchConZdjb = res.list;
                            }
                            gz_001.page.total = res.total;
                            gz_001.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert("查询失败  " + json.c,'right','defeadted');
                        }
                    });
            },
            selectOne: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching(true, 'jbmc', this.jbContent['jbmc']);           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.jbContent = item;
                    Vue.set(this.jbContent, 'jbmc', this.jbContent['mc']);
                    this.jbContent.bm = this.jbContent.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.jbbm = this.jbContent.bm;
                    $(".selectGroup").hide();
                }
            },
            selectOne2: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching2(true, 'qtzd1mc');           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.jbContent2 = item;
                    Vue.set(this.jbContent2, 'qtzd1mc', this.jbContent2['mc']);
                    this.jbContent2.bm = this.jbContent2.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.qtzd1 = this.jbContent2.bm;
                    $(".selectGroup").hide();
                }
            },
            selectOne3: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching3(true, 'qtzd2mc');           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.jbContent3 = item;
                    Vue.set(this.jbContent3, 'qtzd2mc', this.jbContent3['mc']);
                    this.jbContent3.bm = this.jbContent3.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.qtzd2 = this.jbContent3.bm;
                    $(".selectGroup").hide();
                }
            },
            selectOne4: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching4(true, 'qtzd1mc');           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.jbContent4 = item;
                    Vue.set(this.jbContent4, 'qtzd3mc', this.jbContent4['mc']);
                    this.jbContent4.bm = this.jbContent4.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.qtzd3 = this.jbContent4.bm;
                    $(".selectGroup").hide();
                }
            },
            selectOne5: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching5(true, 'qtzd1mc');           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.jbContent5 = item;
                    Vue.set(this.jbContent5, 'qtzd4mc', this.jbContent5['mc']);
                    this.jbContent5.bm = this.jbContent5.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.qtzd4 = this.jbContent5.bm;
                    $(".selectGroup").hide();
                }
            },
            selectOne6: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching6(true, 'qtzd1mc');           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.jbContent6 = item;
                    Vue.set(this.jbContent6, 'qtzd5mc', this.jbContent6['mc']);
                    this.jbContent6.bm = this.jbContent6.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.qtzd5 = this.jbContent6.bm;
                    $(".selectGroup").hide();
                }
            },
            selectOnezdjb: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searchingzdjb(true, 'zdjbmc');           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.jbContentZdjb = item;
                    Vue.set(this.jbContentZdjb, 'zdjbmc', this.jbContentZdjb['mc']);
                    this.jbContentZdjb.bm = this.jbContentZdjb.bm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.zdjbbm = this.jbContentZdjb.bm;
                    $(".selectGroup").hide();
                }
            },
            saveData: function () {
                malert("请选择对应病人后，双击保存！",'right','defeadted');
                return
            },
            getPerson:function(){
                if (gz_001.chh.length == 16 || gz_001.chh.length == 19){
                    gz_001.getPerson_ylk();
                }else{
                    if (gz_001.chh.length != 18 && gz_001.chh.length != 19){
                        malert("身份证号或银行卡号有误,请重新输入!");
                        return;
                    }
                    var head = {
                        operCode: "S32",
                        billCode: gz_001.billCode,
                        rsa: ""
                    };
                    var yeare = new Date();
                    var body = {
                        cardNo: gz_001.chh
                    }

                    var param = {
                        head: head,
                        body: body
                    }
                    var str_param = JSON.stringify(param);
                    console.log(str_param);
                    $.getJSON(
                        "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                            console.log(json);
                            if (json.a == 0) {
                                var res = eval('(' + json.d + ')')
                                gz_001.chh = json.d ;
                                gz_001.getPerson_ylk();
                            } else {
                                malert("农合网络错误，请关闭该窗口从新操作");
                            }
                        });
                }
            },
            //医疗卡获取参合人员信息
            getPerson_ylk: function () {
                var head = {
                    operCode: "S03",
                    billCode: gz_001.billCode,
                    rsa: ""
                };
                var yeare = new Date();
                var body = {
                    year: yeare.getFullYear(),
                    medicalNo: gz_001.chh
                }

                var param = {
                    head: head,
                    body: body
                }
                var str_param = JSON.stringify(param);
                console.log(str_param);

                gz_001.popContent.ylzh = gz_001.chh;
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                        console.log(json);
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')')
                            gz_001.InfoList = res.list;

                            if(json.e){ // @yqq
                            	var res1 = eval('(' + json.e + ')')
                                gz_001.details = res1.data[0].member;
                            }
                        } else {
                            malert("农合网络错误，请关闭该窗口从新操作",'right','defeadted');
                        }
                    });
            },
            //赋值操作
            edit: function (index) {
                gz_001.popContent.brxm = gz_001.InfoList[index].memberName;
                if (rightVue.mzjbxxContent.ybjbbm != null && rightVue.mzjbxxContent.ybjbbm != undefined) {
                    rightVue.mzjbxxContent.ybjbbm = rightVue.mzjbxxContent.ybjbbm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.jbbm = rightVue.mzjbxxContent.ybjbbm;
                }
                if (rightVue.mzjbxxContent.qtzdbm != null && rightVue.mzjbxxContent.qtzdbm != undefined) {
                    rightVue.mzjbxxContent.qtzdbm = rightVue.mzjbxxContent.qtzdbm.replace(/\+/g,"%2B");// 将+号替换为十六进制
                    gz_001.popContent.qtzd1bm = rightVue.mzjbxxContent.qtzdbm;
                }
                if (rightVue.mzjbxxContent.ybjbmc != null && rightVue.mzjbxxContent.ybjbmc != undefined) {
                    gz_001.jbContent.jbmc = rightVue.mzjbxxContent.ybjbmc;
                }
                if (rightVue.mzjbxxContent.qtzdmc != null && rightVue.mzjbxxContent.qtzdmc != undefined) {
                    gz_001.jbContent2.qtzd1mc = rightVue.mzjbxxContent.qtzdmc;
                }
                if (rightVue.mzjbxxContent.lxrdh != null) {
                    gz_001.popContent.lxdh = rightVue.mzjbxxContent.lxrdh;
                }
                if (gz_001.rztg) {
                    if (gz_001.popContent.brxm == null) {
                        malert("病人姓名不能为空！",'right','defeadted');
                        return
                    }
                    if (gz_001.popContent.lxdh == null) {
                        malert("联系电话不能为空！",'right','defeadted');
                        return
                    }
                    if (gz_001.popContent.rylx == null) {
                        malert("入院类型不能为空！",'right','defeadted');
                        return
                    }
                    if (gz_001.jbContent.jbmc == null) {
                        malert("门诊诊断不能为空！",'right','defeadted');
                        return
                    }
                    if (gz_001.popContent.lxdh.length == null){
                        malert("电话号不能为空，请核对！",'right','defeadted');
                        return
                    }
                    if (gz_001.popContent.lxdh.length > 11) {
                        console.log(gz_001.popContent.lxdh.length);
                        malert("电话号已超过最大长度，请核对！",'right','defeadted');
                        return
                    }
                    // @yqq 重大疾病申请序号不为空时，重大疾病编码不能为空
               	 	if(gz_001.popContent.zdjbsqxh && !gz_001.popContent.zdjbbm){
               	 		malert("重大疾病申请序号不为空时，重大疾病编码不能为空！", 'right', 'defeadted');
               	 		return
               	 	}
                    rightVue.bxurl = gz_001.bxurl;
                    rightVue.billCode = gz_001.billCode;
                    rightVue.bxlbbm = gz_001.bxlbbm;

                    // @yqq
                    rightVue.gznhType = true;
                    rightVue.gznhObj = { jzmzName:'无', outpCompensateCost:'0', chroCompensateCost:'0'};
                    rightVue.gznhObj.outpCompensateCost = gz_001.InfoList[index].outpCompensateCost;
                    rightVue.gznhObj.chroCompensateCost = gz_001.InfoList[index].chroCompensateCost;
                    if(gz_001.details.length > 0){
                    	rightVue.gznhObj.jzmzName = gz_001.details[index].jzmzName;
                    }

                    rightVue.nhsjContent1 = gz_001.InfoList[index];
                    rightVue.nhsjContent2 = gz_001.popContent;
                    rightVue.nhsjContent2.jbmc = $("#jbmc").val();
                    console.log(rightVue.nhsjContent1);
                    console.log(rightVue.nhsjContent2);

                    malert("农合信息保存成功！",'right');
                    popTable.isShow = false;
                } else {
                    malert("请稍等，正在进行农合身份认证",'right');
                }
            },

            //农合相关业务
            saveBx:function(){
             $.ajaxSettings.async = false;
             gz_001.bxcw=false;
            //参数完整性判断
              if (gz_001.rztg) {
              if (gz_001.popContent.brxm == null) {
                  malert("病人姓名不能为空！",'right', 'defeadted');
                  return
              }
              if (gz_001.popContent.lxdh == null) {
                  malert("联系电话不能为空！",'right', 'defeadted');
                  return
              }
              if (gz_001.popContent.rylx == null) {
                  malert("入院类型不能为空！",'right', 'defeadted');
                  return
              }
              if (gz_001.jbContent.jbmc == null) {
                  malert("门诊诊断不能为空！",'right', 'defeadted');
                  return
              }
              if (gz_001.popContent.lxdh.length > 11) {
                  console.log(gz_001.popContent.lxdh.length);
                  malert("电话号已超过最大长度，请核对！",'right', 'defeadted');
                  return
              }
             //农合对象赋值
              gz_001.nhsjContent2 = gz_001.popContent;
              gz_001.nhsjContent2.jbmc = $("#jbmc").val();
             //判断对象是否合理
              if (gz_001.nhsjContent1 == {}) {
                malert('为获取参合人员信息', 'right', 'defeadted');
                return
              }
              if (gz_001.nhsjContent2 == {}) {
                malert('参合人员信息不全', 'right', 'defeadted');
                return
              }

            //开始农合交易
            var head = {
            operCode: "S18",
            billCode: gz_001.billCode,
            rsa: ""
            };
            var mzzd = '无';
            var sycs = 0;
            if (gz_001.nhsjContent2.sycs != null) {
            	sycs = gz_001.nhsjContent2.sycs;
            }
            if (gz_001.popContent.jbbm != null) {
            	mzzd = gz_001.popContent.jbbm;
            }
            var jzysxm = null;
            jzysxm = rightVue.mzjbxxContent.jzysxm;
            var body = {
            		memberId: gz_001.nhsjContent1.memberId,
            		inpatientDate: gz_001.fDate(new Date(), 'date'),
            		inpatientDepartments: '03',
            		treatingPhysician: jzysxm,
            		diseaseCode: gz_001.nhsjContent2.jbbm,
            		initialDiagnosis: mzzd,
            		isIncrease: gz_001.nhsjContent2.sftg,
            		isInfusion: gz_001.nhsjContent2.sfzz,
            		isAccountPay: gz_001.nhsjContent2.sfkjtzh,
            		isOutpPay: '1',
            		inpatientTypeOflocal: gz_001.nhsjContent2.rylx,
            		bxAccount: gz_001.nhsjContent2.bxje,
            		tel: gz_001.nhsjContent2.lxdh,
            		bankCardNo: gz_001.nhsjContent2.card,
            		accountHolder: gz_001.nhsjContent2.khrxm,
            		holderRelation: gz_001.nhsjContent2.khrgx,
            		infusionCount: sycs,
            		remark: gz_001.nhsjContent2.bz,
            		uploadType: '0'/*,
                    treatCode:gz_001.popContent.zlfs,
                    cureCode:gz_001.popContent.jzlx,
                    majorDiseaseICD:gz_001.popContent.zdjbbm*/
            }
            var brxm = null;
            brxm = rightVue.mzjbxxContent.brxm;
            var brnl = 0;
            if (rightVue.mzjbxxContent.brnl == undefined || rightVue.mzjbxxContent.brnl == null) {
            	brnl = 0;
            } else {
            	brnl = rightVue.mzjbxxContent.brnl;
            }
            //-----------------------------------------------------------农合业务在此结束---------------------------------------------
              //农合业务结束，显示结算信息
                  ;
              rightVue.bxcw=gz_001.bxcw;
              rightVue.yjsContent=gz_001.yjsContent;
              rightVue.yjsContent2=gz_001.yjsContent2;
              rightVue.nhsjContent1=gz_001.nhsjContent1;
              rightVue.nhsjContent2=gz_001.nhsjContent2;
              rightVue.zxlshSn = gz_001.outpid;

              if(!gz_001.bxcw){
            	  popTable.isShow=false;
                  malert("农合信息保存成功！",'right', 'success');
              }else{
            	  malert("农合业务存在错误！",'right', 'defeadted');
            	  return
              }
          } else {
              malert("农合身份认证未通过，请检查网络情况或操作！",'right', 'defeadted');
          }
            },

        mznhCh:function(){
        	//结算失败后，农合冲红
        	 var head = {
                     operCode: "S26",
                     billCode: rightVue.billCode,
                     rsa: ""
                 };
                 var body = {
                     outpId: rightVue.outpid
                 };
                 var param = {
                     head: head,
                     body: body
                 };
                 var str_param = JSON.stringify(param);
                 $.getJSON(
                     "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                         console.log(json);
                         if (json.a == 0) {

//                             popWin.czbx = false;
                         } else {
                        	 malert("农合冲红失败，请手动冲红该患者！",'right', 'defeadted');
                         }
                     })
        },
        //农合预结算
        gzydnhyjs:function(){
        	// 清空结算记录
            rightVue.outpid = "";
        	popCenter1.jsjlContent = {};
        	rightVue.yjsContent = {};
            var result = "0";
            var head = {
                operCode: "S18",
                billCode: rightVue.billCode,
                rsa: ""
            };
            var mzzd = '无';
            var sycs = 0;
            if (rightVue.nhsjContent2.sycs != null) {
                sycs = rightVue.nhsjContent2.sycs;
            }
            if (rightVue.fzContent.rymzzd != null) {
                mzzd = rightVue.fzContent.rymzzd;
            }
            //判断是否是挂号病人
            var jzysxm=null;
            if(rightVue.mzjbxxContent.jzysxm==undefined||rightVue.mzjbxxContent.jzysxm==null){
                jzysxm=rightVue.listGetName(rightVue.mzysList, rightVue.fzContent['mzys'], 'rybm', 'ryxm');
            }else{
                jzysxm=rightVue.mzjbxxContent.jzysxm;
            }
            var body = {
                memberId: rightVue.nhsjContent1.memberId,
                inpatientDate: rightVue.fDate(new Date(), 'date'),
                inpatientDepartments: '03',
                treatingPhysician: jzysxm,
                diseaseCode: rightVue.nhsjContent2.jbbm,
                initialDiagnosis: mzzd,
                isIncrease: rightVue.nhsjContent2.sftg,
                isInfusion: rightVue.nhsjContent2.sfzz,
                isAccountPay: rightVue.nhsjContent2.sfkjtzh,
                isOutpPay: '1',
                inpatientTypeOflocal: rightVue.nhsjContent2.rylx,
                bxAccount: rightVue.nhsjContent2.bxje,
                tel: rightVue.nhsjContent2.lxdh,
                bankCardNo: rightVue.nhsjContent2.card,
                accountHolder: rightVue.nhsjContent2.khrxm,
                holderRelation: rightVue.nhsjContent2.khrgx,
                infusionCount: sycs,
                remark: rightVue.nhsjContent2.bz,
                uploadType: '0'
            }
            //判断是否是挂号病人
            var brxm=null;
            if(rightVue.mzjbxxContent.brxm==undefined||rightVue.mzjbxxContent.brxm==null){
                brxm=rightVue.fzContent['brxm'];
            }else{
                brxm=rightVue.mzjbxxContent.brxm;
            }
            var brnl=0;
            if(rightVue.mzjbxxContent.brnl==undefined||rightVue.mzjbxxContent.brnl==null){
                brnl=0;
            }else{
                brnl=rightVue.mzjbxxContent.brnl;
            }
            var footer = {
                ylzh: rightVue.nhsjContent2.ylzh,
                brnl: brnl,
                brxm: brxm,
                jtdz: rightVue.nhsjContent1.areaName,
                jbczy: userId,
            }
            //需要实体内容一并传入
            var data = {
                nhsjContent1:rightVue.nhsjContent1,
                nhsjContent2:rightVue.nhsjContent2,
                fzContent:rightVue.fzContent,
                mzjbxxContent:rightVue.mzjbxxContent,
                brfyjsonList:rightVue.brfyjsonList
            }
            //测试
            var param = {
                head: head,
                body: body,
                footer: footer
            }
            var params ={
                param : param,
                data:data,
            }
            //测试post begin
            console.log(params);
            console.log(JSON.stringify(params));
            //toolMenu.bxurl = "http://10.2.50.30:8080/interface/gzydnh/post";
            // rightVue.bxurl ='192.168.0.240:9005/interface/gzydnh/post';
            var paramobj = '{"list":' + JSON.stringify(params) + '}';
            // this.$http.post("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + rightVue.bxurl + "&bxlbbm=" + rightVue.bxlbbm + "&types=Mzsf",
            //     paramobj).then(function(json) {
            //     console.log("预结算成功!");
            //     console.log(json.body.d);
            //     if (json.body.a == 0) {
            //         malert("预结算成功!");
            //         var res = eval('(' + json.body.d + ')')
            //         rightVue.yjsContent = res.list[0];
            //         rightVue.disable=false
            //         rightVue.yjsContent2 = res.list[0];
            //         rightVue.outpid = res.outpid;
            //         result =  "0";
            //     } else {
            //         result =  "1";
            //         malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!");
            //     }
            //
            // }, function(error) {
            //     result =  "1";
            //     console.log(error);
            // });
            // rightVue.bxurl='http://127.0.0.1:9005/interface/gzydnh/post';
            this.postAjax("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + rightVue.bxurl + "&bxlbbm=" + rightVue.bxlbbm + "&types=Mzsf",paramobj,function (json) {
                var obj = {};
                if(typeof(json)=='string'){
                    obj = JSON.parse(json);
                }else {
                    obj = json;
                }

                if(obj.a==0 && obj.d){
                    console.log("预结算成功!");
                    malert("预结算成功!",'right');
                    //var res = eval('(' + json.d + ')')
                    var res = JSON.parse(obj.d).list;
                    var data = JSON.parse(obj.e).data;
                    rightVue.yjsContent = res[0];
                    rightVue.disable=false
                    rightVue.yjsContent2 = res[0];
                    rightVue.yjsContent=Object.assign(rightVue.yjsContent,data[0]);
                    rightVue.outpid = JSON.parse(obj.d).outpid;
                    result =  "0";
                }else {
                    result =  "1";
                    malert(json.c,"right","defeadted");
                }
            },function (error) {
                result =  "1";
                console.log(error);
            })
            return result;
        },
        //农合结算
        gzydnhJs:function(){
        	$.ajaxSettings.async = false;
        	/*
        	  var head = {
                      operCode: "S25",
                      billCode: rightVue.billCode,
                      rsa: ""
                  };
                  var body = {
                      outpId: rightVue.outpid
                  };
                  var param = {
                      head: head,
                      body: body
                  };
                  var str_param = JSON.stringify(param);
                  */
            // 合并成一个对象
                    var json = {
                        brfyList: rightVue.brfyjsonList,
                        brghModel: rightVue.mzjbxxContent,
                        jsjlModel: popCenter1.jsjlContent,
                        cfkfxmList: popCenter1.cfkfxmList,
                        bxjsh: rightVue.outpid
                    };
                    var head = {
                        operCode: "S25",
                        billCode: rightVue.billCode,
                        rsa: ""
                    };
                    var body = {
                        outpId: rightVue.outpid
                    };
                    var param = {
                        head: head,
                        body: body
                    };
                    //var str_param = JSON.stringify(param);
                    //补偿单信息赋值，nhContent会清空。
                        rightVue.bcdxxContent1=rightVue.nhsjContent1;
                        rightVue.bcdxxContent2=rightVue.nhsjContent2;

                    var data = {
                        bcdxxContent1:rightVue.bcdxxContent1,
                        bcdxxContent2:rightVue.bcdxxContent2,
                        yjsContent2:rightVue.yjsContent2,
                        ghxh:rightVue.fzContent['ryghxh'],
                        rylx:rightVue.nhrysx_tran[rightVue.bcdxxContent1.memberPro],
                        mzlx:rightVue.mznh_tran[rightVue.bcdxxContent2.rylx],
                    };
                    //合并测试
                    var  params = {
                        json:json,
                        param:param,
                        data:data
                    };
                  var paramobj = '{"list":' + JSON.stringify(params) + '}';
                  //$.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + rightVue.bxurl + "&bxlbbm=" + rightVue.bxlbbm + "&types=MzSave&parm=" + str_param, function (json) {
                 this.postAjax("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + rightVue.bxurl + "&bxlbbm=" + rightVue.bxlbbm + "&bxjk="+rightVue.fzContent.bxjk+"&types=MzSave",
                paramobj,function(json) {
                         var obj = {};
                         if(typeof(json)=='string'){
                             obj = JSON.parse(json);
                         }else {
                             obj = json;
                         }
                      if (obj.a == 0) {
                          gz_001.FpPrintContent = obj.d;
                          malert("农合结算成功!",'right', 'success');
                          var cljz = null;
                          var mzyf = null;
                          var jsfz = null;
                          var ylfp = null;
                          var mzyljz = null;
                          ;
                          if (!rightVue.fDec(rightVue.yjsContent2.salvaclcost, 2)) {
                              cljz = 0;
                          } else {
                              cljz = rightVue.fDec(rightVue.yjsContent2.salvaclcost, 2);
                          }
                          if (!rightVue.fDec(rightVue.yjsContent2.salvayfcost, 2) ) {
                              mzyf = 0;
                          } else {
                              mzyf = rightVue.fDec(rightVue.yjsContent2.salvayfcost, 2);
                          }
                          if (!rightVue.fDec(rightVue.yjsContent2.salvajscost, 2)) {
                              jsfz = 0;
                          } else {
                              jsfz = rightVue.fDec(rightVue.yjsContent2.salvajscost, 2);
                          }
                          if (!rightVue.fDec(rightVue.yjsContent2.salvafpcost, 2) ) {
                              ylfp = 0;
                          } else {
                              ylfp = rightVue.fDec(rightVue.yjsContent2.salvafpcost, 2);
                          }
                          if (!rightVue.fDec(rightVue.yjsContent2.civilcost, 2) ) {
                              mzyljz = 0;
                          } else {
                              mzyljz = rightVue.fDec(rightVue.yjsContent2.civilcost, 2);
                          }

                          var bcxx = {
                              ghxh: rightVue.fzContent['ryghxh'],
                              rylx: rightVue.nhrysx_tran[rightVue.nhsjContent1.memberPro],
                              bcrq: rightVue.fDate(new Date(), 'date'),
                              mzlx: rightVue.mznh_tran[gz_001.popContent.rylx],
                              lxdh: rightVue.nhsjContent2.lxdh,
                              jtdz: rightVue.nhsjContent1.areaName,
                              brxm: rightVue.nhsjContent1.memberName,
                              memberid: rightVue.nhsjContent1.memberId,
                              ybkh: rightVue.nhsjContent1.medicalNo,
                              outpid: rightVue.outpid,
                              fyje: rightVue.fDec(rightVue.yjsContent2.totalcost, 2),
                              zfje: rightVue.fDec(rightVue.yjsContent2.totalcost - rightVue.yjsContent2.compensatecost - rightVue.yjsContent2.salvaclcost - rightVue.yjsContent2.salvayfcost - rightVue.yjsContent2.salvajscost - rightVue.yjsContent2.salvafpcost - rightVue.yjsContent2.civilcost, 2),
                              bcfy: rightVue.fDec(rightVue.yjsContent2.compensatecost + rightVue.yjsContent2.salvaclcost + rightVue.yjsContent2.salvayfcost + rightVue.yjsContent2.salvajscost + rightVue.yjsContent2.salvafpcost + rightVue.yjsContent2.civilcost, 2),
                              jbmc: rightVue.nhsjContent2.jbmc,
                              cljz: cljz,
                              mzyf: mzyf,
                              jsfz: jsfz,
                              ylfp: ylfp,
                              mzyljz: mzyljz,
                              bnfy: rightVue.fDec(rightVue.yjsContent2.insurancecost,2),
                              dysj: rightVue.fDate(new Date(), 'YY'),
                              czyxm: sessionStorage.getItem('userName'+userId),
                          }
                          gz_001.bcdxxContent = bcxx;
                          /*$.getJSON(
                              "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + rightVue.bxurl + "&bxlbbm=" + rightVue.bxlbbm + "&types=yjs&method=saveMzbcd&parm=" + JSON.stringify(bcxx), function (json) {
                                  if (json.a == 0) {
                                      malert("补偿保存成功",'top', 'success');
                                  } else {
                                      malert(json.c,'top', 'defeadted');
                                  }
                              });*/
                          rightVue.outpid=rightVue.outpid;
                          // rightVue.yjsContent=null;
                          // rightVue.yjsContent2=null;
                          // rightVue.nhsjContent1=null;
                          // rightVue.nhsjContent2=null;
                          // rightVue.pop=null;
                          // rightVue.chh=null;
                          gz_001.InfoList=[{}, {}, {}];
                          rightVue.popContent={};
                      }else {
                          malert("农合结算失败，错误信息提示:"+obj.c+"",'right', 'defeadted');
                      }
                  });
        },



     /**-------------------------------------------------大病结算--------------------------------------------------------**/

        // @yqq 贵州移动农合大病结算
        dbjs_gzydnh:function(){
        	// 入院登记
        	gz_001.rydj_gzydnh();

        },
        // @yqq 第一步：入院登记
        rydj_gzydnh:function(){
        	var head = {
                	operCode:"S04",
                	billCode:gz_001.billCode,
                	rsa:""
                };
       	 	var zsqq= '1'; // 证件齐全
       	 	var zzbz= '0'; // 是否转诊
       	 	var xzbz=null;
       	 	var valuelist=[];
       	 	if(gz_001.qtzdList.length>0){ // 判断是否有其他诊断
	       	 	for(var i=0;i<gz_001.qtzdList.length;i++){
		       	 	var disCode={
		       	 			disCode:gz_001.qtzdList[i].bm
		       	 	};
		       	 	var valueList={
		       	 		valueList:disCode
		       	 	};
		       	 	valuelist.push(valueList);
	       	 	}
       	 	}
       	 	  //农合疾病编码 省编码      治疗方式    申请号      行政区划   跨省就医
       	 	var nhjbbm = "",szdjbbm = "",szlfs = "",szdjbsqh = "",sxzqh = "",sksjy = "",familyNo = "",cureId="",zdjbbz = "0";

	       	 nhjbbm = gz_001.popContent.zdjbbm; //重大疾病疾病编码
	         szdjbbm = gz_001.popContent.zdjbbm; // 疾病编码
	         szlfs = gz_001.popContent.zlfs ? gz_001.popContent.zlfs :'09'; // 治疗方式
            var  turnCode = gz_001.popContent.turnCode;
            if (turnCode == null){
                turnCode = "";
            }
	         sxzqh = ''; // 行政区划
	         sksjy = ''; // 跨省就医
	         familyNo = rightVue.nhsjContent1.familyId;
	         cureId = "2";
	         zdjbbz = "1";
	            if (sksjy == null || sksjy == undefined ){
	                sksjy = "0";
	            }
                var body = {
                	memberId:rightVue.nhsjContent1.memberId,
                	inpatientNo:  rightVue.brxxContent.ghxh, // 取挂号序号
                	admissionDate:gz_001.fDate(new Date(),'date'),
                	admissionDepartments: '04', // TODO rightVue.fzContent.mzks 农合科室编码
                	treatingPhysician:rightVue.fzContent.mzys,
                	admissionStatus:'3', // 入院情况
                	diseaseCode:nhjbbm,
                	initialDiagnosis:'',
                    surgeryCode:'',
                    berthNo:"",
                	isIncrease:zsqq,
                	isReferra:zzbz,
                	inpatientTypeOflocal:'A0', // 入院类型 A0 重大疾病普通类型
                	bxAccount:rightVue.nhsjContent1.account,
                	tel:gz_001.popContent.lxdh,
                	bankCardNo:'', // 银行卡号
                	accountHolder:'',
                	holderRelation:'',
                	remark:'',
                	bigDiseaseNo:gz_001.popContent.zdjbsqxh,
                	isReferraNo:null,
                	disList:valuelist,
                	uploadType:'0',
                    familySysno:familyNo,
                    treatCode:szlfs,
                    cureId:cureId,
                    turnMode:gz_001.popContent.zzlx,//转诊类型
                    turnCode:turnCode,
                    registerID:rightVue.brxxContent.ghxh,
                    areaCode:sxzqh,
                    isTransProvincial:sksjy,
                    majorDiseaseICD:szdjbbm,
                    secondMajorDiseaseICD:"",
                    threeMajorDiseaseICD:"",
                    secondTreatCode:"",
                    threeTreatCode:""
                }
                var date=new Date();
                var memberPro = rightVue.nhsjContent1.memberPro;
                if (memberPro == null ||memberPro == undefined ){
                    memberPro = "";
                }
                var footer={
               		 brxm:rightVue.fzContent.brxm,
               		 brnl:rightVue.mzjbxxContent.brnl,
               		 ylzh:gz_001.chh,
               		 jtdz:rightVue.nhsjContent1.areaName,
               		 lxfs:gz_001.popContent.lxdh,
               		 jbczy:userId,
               		 jbrq:gz_001.fDate(new Date(),'date'),
               		 memberPro:memberPro,
                     zdjb:zdjbbz,
                     xzqh:sxzqh,
                     familyNo:familyNo,
                    treatcode:szlfs,//治疗方式
                    majordiseaseicd:szdjbbm  //重大疗病编码
                }
                var param = {
                	head:head,
                	body:body,
                	footer:footer
                }
                var str_param = JSON.stringify(param).replace(/\+/g, "%2B");
                console.log(str_param);
                $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+gz_001.bxurl+"&bxlbbm="+gz_001.bxlbbm+"&types=S&parm="+str_param, function (json) {
                	console.log(json);
                	if (json.a == 0){
                		var res=eval('('+json.d+')');
                		console.log(res);
                		var inpid=res.list[0];
                		console.log(inpid);
                		gz_001.inpid = inpid;

                		// 第二步：上传费用
                    	gz_001.uploadFyxx();

                		//malert("农合入院办理成功！","top","success");
                	}else{
                        var pidlen = json.c.indexOf('补偿序号为：');
                        gz_001.inpid = json.c.substr(pidlen+6);
                		gz_001.cybcy();
                		malert(json.c,"right","defeadted");
                	}
                });
        },
     // @yqq 第二步：上传费用
        uploadFyxx:function(){
            //处理处方否存在相同处方号
            var fyxx = [];
            var fyxxt = rightVue.brfyjsonList;
            var str = "";
            for (var i = fyxxt.length - 1; i >= 0 ; i--){
                if (fyxxt[i].yzlx != "2"){//非药品
                    fyxx.push(fyxxt[i]);
                }else{//药品处理是否重复的
                    var substr = fyxxt[i].yzhm;
                    if (str.indexOf(substr) <= 0){
                        str = str +"$"+ substr;
                        fyxx.push(fyxxt[i]);
                    }
                }
            }
        	var data = {
              brfyjsonList:fyxx
            }
            var params ={
              data:data
            }
        	var paramobj = '{"list":' + JSON.stringify(params) + '}';
        	// 查询保险项目信息
        	this.postAjax("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl
        			+ "&bxlbbm=" + gz_001.bxlbbm + "&types=zdjb&method=queryFyxx",paramobj,function (json) {
        		console.log(json);
                if(json.a==0 && json.d){
                	var fyxx = JSON.parse(json.d).list;
                	gz_001.upload(fyxx);
                }else {
                	malert(json.c,"right","defeadted");
                }
            },function (error) {
                console.log(error);
            });
        },
        upload:function(fyxx){
        	if(fyxx <= 5){
    			var bodylist=[];
        		var footerlist=[];
        		var wdmxm=[];
        		var ts=0;
    		for(var i=0;i<fyxx.length;i++){
    			if(fyxx[i].bxxmbm){
    				 ts++;
                    if (fyxx[j].bxxmlb == undefined){
                        fyxx[j].bxxmlb = fyxx[j].xmlb;
                    }
    	             var detail = {
    	                     	detailName:fyxx[i].bxxmmc,
    	                     	detailCode:fyxx[i].bxxmbm,
    	                     	hisDetailCode:fyxx[i].fyid,
    	                     	detailHosCode:fyxx[i].yljgbm,
    	                     	typeCode:fyxx[i].bxxmlb,  //剑河修改为bxxmlb 如需修改先群里沟通
    	                     	num:fyxx[i].fysl,
    	                     	price:fyxx[i].fydj,
    	                     	totalCost:fyxx[i].fyje,
    	                     	date:gz_001.fDate(new Date(),'date')
    	                     	// unit:null,
    	                     	// standard:null,
    	                     	// formulations:null,
    	                     };
    	             var footer ={
    	                    	mxfyxmbm:fyxx[i].mxfyxmbm,
    	                     };
    	             bodylist.push(detail);
    	             footerlist.push(footer);
    			}else if(!fyxx[i].bxxmbm){
                    malert("其中" + fyxx[i].mxfyxmmc+ "未对码，请对码后再上传","right","defeadted");
                    return false;
    				// wdmxm.push(fyxx[i]);
    			}
    		}
			// if(wdmxm.length > 0){
			// 	malert("其中" + wdmxm.length + "条数据未成功上传，请对码后再上传","right","defeadted");
			// }
    		if(bodylist.length <= 0){
				malert("无可上传的费用！","right","defeadted");
				return;
			}
    		 var list={
    				 detail:bodylist
    		 }
    		 var footlist={
    				 detail:footerlist
    		 }
    		 var head = {
                     	operCode:"S10",
                     	billCode:gz_001.billCode,
                     	rsa:""
                     };
    		 var body={
    			 inpId:gz_001.inpid,
				 doctor:rightVue.fzContent.mzys,
				 bookNo:gz_001.chh,//"5204210201010612"
				 name:rightVue.fzContent.brxm,//"测试1",
				 familyNo:rightVue.nhsjContent1.familyId,//"59846890",
				 memberNo:rightVue.nhsjContent1.memberNo,//"214144193",  //memberId
				 areaCode:rightVue.nhsjContent1.areaCode,//"520421",//
				 isTransProvincial:"0",
				 list:list
     		 };
    		 var footer={
    				bxlbbm:gz_001.bxlbbm,
    				list:footlist
    		 };
    		 var param = {
                     	head:head,
                     	body:body,
                     	footer:footer
                     };
    	     var str_param = JSON.stringify(param);
             $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+gz_001.bxurl+"&bxlbbm="+gz_001.bxlbbm+"&types=S&parm="+str_param,
				 function (json){
                   if (json.a == 0){
                     		//malert("费用上传成功！共上传" + ts + "条费用记录！","bottom","success");
                     		if(wdmxm.length > 0){
                     			malert("其中" + wdmxm.length + "条数据未成功上传，请对码后再上传","right","defeadted");
								wscjl.ifClick = true;
                     		}
                     	}else{
                     		gz_001.cybcy();
                     		malert(json.c,"right","defeadted");
                     	}
                    });
    		}else{
    			for(var i=0;i<fyxx.length;i+=5){
    				var bodylist2=[];
            		var footerlist2=[];
            		var wdmxm2=[];
    				var xjsonlist=fyxx.slice(i,i+5);
    				for(var j=0;j<xjsonlist.length;j++){
            			if(xjsonlist[j].bxxmbm!=null){
            			    if (xjsonlist[j].bxxmlb == undefined){
                                xjsonlist[j].bxxmlb = xjsonlist[j].xmlb;
                            }
            	             var detail2 = {
            	                     	detailName:xjsonlist[j].bxxmmc,
            	                     	detailCode:xjsonlist[j].bxxmbm,
            	                     	hisDetailCode:xjsonlist[j].fyid,
            	                     	detailHosCode:xjsonlist[j].yljgbm,
            	                     	typeCode:xjsonlist[j].bxxmlb,//剑河修改为bxxmlb，如需修改先群里沟通
            	                     	num:xjsonlist[j].fysl,
            	                     	price:xjsonlist[j].fydj,
            	                     	totalCost:xjsonlist[j].fyje,
            	                     	date:gz_001.fDate(new Date(),'date'),
            	                     	unit:null,
            	                     	standard:null,
            	                     	formulations:null,
            	                     }
            	             var footer2={
            	                    	mxfyxmbm:xjsonlist[j].mxfyxmbm,
            	                     }
            	             bodylist2.push(detail2);
            	             footerlist2.push(footer2);
            			}
            		}
            		 var list2={
            				 detail:bodylist2
            		 }
            		 var footlist2={
            				 detail:footerlist2
            		 }
            		 var head = {
    	                     	operCode:"S10",
    	                     	billCode:gz_001.billCode,
    	                     	rsa:""
    	                     };
            		 var body={
            				 inpId:gz_001.inpid,
            				 doctor:rightVue.fzContent.mzys,
            				 bookNo:gz_001.chh,//"5204210201010612"
            				 name:rightVue.fzContent.brxm,//"测试1",
            				 familyNo:rightVue.nhsjContent1.familyId,//"59846890",
            				 memberNo:rightVue.nhsjContent1.memberNo,//"214144193",  //memberId
            				 areaCode:rightVue.nhsjContent1.areaCode,//"520421",//
            				 isTransProvincial:"0",
            			list:list2
             		 };
            		 var footer={
            				bxlbbm:gz_001.bxlbbm,
            				list:footlist2
            		 };
            		 var param = {
    	                     	head:head,
    	                     	body:body,
    	                     	footer:footer
    	                     };
					var str_param = JSON.stringify(param);
					$.getJSON(
						"/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+gz_001.bxurl+"&bxlbbm="+gz_001.bxlbbm+"&types=S&parm="+str_param,
						function (json){
							if (json.a == 0){
								//malert("费用上传成功！",'bottom','success');
							}else{
								gz_001.cybcy();
								malert(json.c,'right','defeadted');
							}
						});
    			}
    		}

        	// 结算农合
        	gz_001.ybcy();
        },

        ybcy: function () {
            $.ajaxSettings.async = false;
            if (gz_001.inpid == null || gz_001.inpid == undefined) {
                malert("该病人无补偿证号，未农合入院","right","defeadted");
                return
            }
//            if (gz_001.cyqk == null) {
//                malert("出院情况不能为空！","bottom","defeadted");
//                return
//            }
            var head = {
                operCode: "S07",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var body = {
                inpId: gz_001.inpid,
                dischargeDate: gz_001.fDate(new Date(), 'date'),
                dischargeDepartments: '03',  //出院科室
                dischargeStatus: '2', // 出院情况
                icdAllNo:gz_001.popContent.zdjbbm,//
                isTransProvincial:'0',
            };

            var footer = {
                cyczy: userId
            };
            var param = {
                head: head,
                body: body,
                footer: footer
            };
            var str_param = JSON.stringify(param);
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    console.log(json);
                    if (json.a == 0) {
                        //malert("医保出院成功！","bottom","success");
                        var head = {
                            operCode: "S14",
                            billCode: gz_001.billCode,
                            rsa: ""
                        };
                        var body = {
                            inpId: gz_001.inpid,
                            redeemNo:"2102",
                            outDate:gz_001.fDate(new Date(), 'date'),
                            //areaCode:'522630100213',//"520421",  //行政区划
                            isTransProvincial:'0'
                        };

                        var footer = {
                            cyczy: userId
                        }
                        var param = {
                            head: head,
                            body: body,
                            footer: footer
                        };
                        var str_param = JSON.stringify(param);
                        $.getJSON(
                            "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                                console.log(json);
                                if (json.a == 0) {
                                    //malert("农合结算成功！","bottom","success");
                                    var parm = {
                                        inpid: gz_001.inpid
                                    };
                                    $.getJSON(
                                        "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=yjs&method=queryDzlx&parm=" + JSON.stringify(parm), function (json) {
                                            console.log(json)
                                        	if (json.a == 0) {
                                                var res = eval('(' + json.d + ')');
                                                gz_001.yjsContent = res.list[0];
                                                rightVue.yjsContent=gz_001.yjsContent;

                                                // @yqq update 大病显示商保金额 民政扶优
                                                rightVue.yjsContent.insureCost = gz_001.yjsContent.insurecost;
                                                rightVue.yjsContent.salvaJSCost = gz_001.yjsContent.salvajscost;
                                                rightVue.yjsContent.civilCost = gz_001.yjsContent.civilcost;
                                                rightVue.yjsContent.totalcost =gz_001.yjsContent.totalcost;
                                                rightVue.yjsContent.medicineCost = gz_001.yjsContent.medicinecost;

                                                gz_001.jtdz = res.list[0].address;
                                                gz_001.rylx = gz_001.nhrysx_tran[res.list[0].memberpro];
                                                //gz_001.zyts = parseInt(Math.abs(yjs.json.bqcyrq - yjs.json.ryrq) / 1000 / 60 / 60 / 24);
                                                if(gz_001.zyts==0){
                                                	gz_001.zyts=1;
                                                }

                                                var xs='0.9';
		                                        if(gz_001.yjsContent.undulatingline==0){
		                                            xs='0.95';
		                                        }
//		                                        if( gz_001.printContentt.zfje=='0.00'){
//		                                            xs='1.00';
//		                                        }
		                                        var bcdxx={
		                                            inpid:gz_001.inpid,
		                                            zyh:rightVue.brxxContent.ghxh,
		                                            //rylx: yjs.rylx,
		                                            brxm: rightVue.fzContent.brxm,
		                                            cylx: 'A0',
		                                            brxb: rightVue.mzjbxxContent.brxb,
		                                            nl:rightVue.mzjbxxContent.brnl,
		                                            ybkh:gz_001.chh,
		                                            bcrq: gz_001.fDate(new Date(), 'date'),
		                                            //jtdz:yjs.jtdz,
		                                            lxdh:gz_001.popContent.lxdh,
		                                            ryrq:gz_001.fDate(new Date(), 'date'),
		                                            bqcyrq: gz_001.fDate(new Date(), 'date'),
		                                            //ryzdmc: gz_001.json.ryzdmc,
		                                            zyts: gz_001.zyts,
		                                            //fyje: yjs.ynfyContent.fyje,
		                                            //zffy: yjs.fDec(yjs.fDec(yjs.ynfyContent.fyje,2)-yjs.fDec(yjs.yjsContent.insurancecost, 2)),
		                                            //ndzyts: yjs.json.ndzyts,
		                                            //scfyje: yjs.ynfyContent.scfyje,
		                                            undulatingline: gz_001.yjsContent.undulatingline,
		                                            bottomredeem: gz_001.yjsContent.bottomredeem,
		                                            insurecost: gz_001.yjsContent.insurecost,
		                                            medicinecost: gz_001.yjsContent.medicinecost,
		                                            salvafpcost: gz_001.yjsContent.salvafpcost,
		                                            salvayfcost: gz_001.yjsContent.salvayfcost,
		                                            civilcost: gz_001.yjsContent.civilcost,
		                                            salvajscost: gz_001.yjsContent.salvajscost,
		                                            compensatecost: gz_001.yjsContent.compensatecost,
		                                            insurancecost:gz_001.yjsContent.insurancecost,
		                                            sjbc:0,
		                                            zfje:0,
		                                            //jsgs:"("+yjs.yjsContent.insurancecost+"-"+yjs.yjsContent.undulatingline+")*"+xs,
                                                    jsgs:gz_001.jsgs,
		                                        };
		                                        bcdxx.sjbc = gz_001.fDec(gz_001.yjsContent.compensatecost+gz_001.yjsContent.insurecost+gz_001.yjsContent.civilcost+gz_001.yjsContent.salvajscost,2);
		                                        //bcdxx.zfje = gz_001.fDec(gz_001.ynfyContent.fyje - bcdxx.sjbc,2);
		                                        $.getJSON(
		                                            "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=yjs&method=saveZybcd&parm=" + JSON.stringify(bcdxx), function (json) {
		                                                if (json.a == 0) {

		                                                	// @yqq update 赋值补偿单信息用于 补偿单打印
		                                                	gz_001.bcdxxContent = bcdxx;
		                                                	gz_001.bcdxxContent.outpid = bcdxx.inpid;
		                                                	rightVue.bxcw=false;
		                                                    malert("补偿保存成功","right","success");
		                                                } else {
		                                                    malert(json.c,"right","defeadted");
		                                                }
		                                            });

                                            } else {
                                                malert(json.c,"right","defeadted");
                                            }
                                        });
                                } else {
                                	gz_001.cybcy(); // 调用冲红接口
                                    malert(json.c,"right","defeadted");
                                }
                            });
                    } else {
                    	gz_001.cybcy();
                        malert(json.c,"right","defeadted");
                    }
                });
        },


        cybcy: function () {
            if (gz_001.inpid == null || gz_001.inpid == undefined) {
                malert("该病人无补偿证号，未农合入院","right","defeadted");
                return
            }
            var head = {
                operCode: "S15",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var body = {
                inpId: gz_001.inpid,
                //areaCode:yjs.json.xzqh,//"520421",  //行政区划
                isTransProvincial:'0'
            }
            var footer = {
                czy: userId,
                zyh: rightVue.brxxContent.ghxh
            }
            var param = {
                head: head,
                body: body,
                footer: footer
            }
            var str_param = JSON.stringify(param);
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    console.log(json);
                    if (json.a == 0) {
                        malert("大病冲红成功！","right","success");
                    } else {
                        malert(json.c,"right","defeadted");
                    }
                });
        },
        /**
         * 取消入院
         */
        qxry: function () {
        	var head = {
                	operCode:"S05",
                	billCode:gz_001.billCode,
                	rsa:""
                };
                var body = {
                	inpId:gz_001.inpid

                }
                var footer={
                		zfry:userId
                }
                var param = {
                	head:head,
                	body:body,
                	footer:footer
                }
                var str_param = JSON.stringify(param);

                $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url="+gz_001.bxurl+"&bxlbbm="+gz_001.bxlbbm+"&types=S&parm="+str_param, function (json) {
                	console.log(json);
                	if (json.a == 0){
                		malert("农合取消入院办理成功！","right","success");
                		gz_001.inpid = null;
                	}else{
                		malert(json.c,"right","defeadted");
                	}
                });
        },

        yjs: function () {
            if (gz_001.inpid == null || gz_001.inpid == '' || gz_001.inpid == undefined) {
                malert("改病人未办理农合入院！请正确选择病人","right","defeadted");
                return
            }
            var head = {
                operCode: "S13",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var body = {
                inpId: gz_001.inpid,
                redeemNo:"1",//2102
                outDate: gz_001.fDate(new Date(), 'AllDate'),//gz_001.fDate(new Date(), 'AllDate'),
                isMaterials:'1',
                operationName:userId,
                isTransProvincial:"0"
            };
            var param = {
                head: head,
                body: body
            };
            var str_param = JSON.stringify(param);
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    console.log(json);
                    if (json.a == 0) {
                        malert("预结算成功!","right","success");
                        var res = eval('(' + json.d + ')')
//                        yjs.yjsContent = res.list[0];
//                        console.log(yjs.yjsContent);
//                        yjs.yjsContent.tbje = yjs.ynfyContent.yjje - (yjs.ynfyContent.fyje - yjs.yjsContent.compensatecost);
//                        yjs.jsonList = [],
//                        yjs.brxxContent.memberid = hyjl.brxxList.memberid;
//                        yjs.brxxContent.brxm = hyjl.brxxList.brxm;
//                        yjs.brxxContent.scfyje = yjs.ynfyContent.scfyje;
//                        yjs.brxxContent.zffy = yjs.ynfyContent.fyje - yjs.yjsContent.compensatecost;
//                        yjs.brxxContent.insurancecost = yjs.ynfyContent.fyje - yjs.yjsContent.insurancecost;
//                        yjs.brxxContent.compensatecost = yjs.ynfyContent.fyje - yjs.yjsContent.compensatecost;
//                        yjs.brxxContent.undulatingline = yjs.ynfyContent.fyje - yjs.yjsContent.undulatingline;
//                        yjs.jsonList.push(yjs.brxxContent);
//                        yjs.jsgs = yjs.yjsContent.calculationMethod;
                        console.log(res);

                        //保存补偿单信息：

                    } else {
                        malert(json.c,"right","defeadted");
                    }
                });
        },

        }
    });
    gz_001.getbxlb();
    gz_001.getChzh();
