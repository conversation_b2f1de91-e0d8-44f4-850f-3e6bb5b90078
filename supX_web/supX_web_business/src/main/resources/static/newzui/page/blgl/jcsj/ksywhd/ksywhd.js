    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            title:'',
            LcList:{
                lcsjzid:''
            },
            ksmc:null,
            zYList:[],
            totle:'',
            num:0,
            param: {
                page: '',
                rows: '',
                total: ''
            }
        },
        watch:{
            ksmc:function () {
                var ksmc = '';
                for(var i = 0; i < wrapper.LcList.length; i++) {
                    if(this.LcList[i].ksmc == this.ksmc) {
                        ksmc = this.LcList[i].ksmc;

                    }

                }

            }
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='设置科室业务活动';
                wap.open();
                wap.popContent={};

            },
            sx:function () {
              yjkmtableInfo.getData();
            },
            //删除
            del:function () {
                yjkmtableInfo.remove();
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    yjkmtableInfo.getData();
                }

            },
            LcGetData:function () {
                $.getJSON('/actionDispatcher.do?reqUrl=EmrXtwhKsywhd&types=query',function (json) {
                    if(json.a==0){
                        wrapper.LcList=json.d.list;
                        wap.LcList=json.d.list;
                        // console.log(wrapper.LcList)

                    }

                });
            },
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            flag:false,
            ejShow:false,
            treeData:{},
             treeList: [],
            ksList:[],
            hszList:[],
            LcList:[],
            rList:[],
            ywckList:[],
            ejMore:true,
            centent:'',
            sjzmc:null,
            title:'',
            jsonList: [],
            popContent: {
                'tybz': '',
            },

        },
        watch:{
            sjzmc:function () {
                var sjzmc = '';
                for(var i = 0; i < wap.LcList.length; i++) {
                    if(this.LcList[i].lcsjzid == this.lcsjzid) {
                        sjzmc = this.LcList[i].lcsjzid;

                    }

                }


            }
        },
        created:function () {
            this.getDatas()
        },
        methods:{
            getDatas: function () {
                if ($("#jsvalues").val() != null && $("#jsvalues").val() != '') {
                    this.param.parm = $("#jsvalues").val();
                } else {
                    this.param.parm = '';
                }
                $.getJSON("/actionDispatcher.do?reqUrl=EmrXtglKsgl&types=queryAll&parm="+JSON.stringify(this.param), function (json) {
                    // data = {
                    //     mkmc: '系统模块',
                    //     children: json.d
                    // };
                    if(json.a==0){
                        wap.treeData = {};
                        wap.treeData = json.d.list;
                    }
                    console.log(json);
                });
            },
            //展开二级
            ejUp:function(){
                this.ejShow=true;
                this.ejMore=false;

            },
            ejDown:function () {
                this.ejShow=false;
                this.ejMore=true;
            },
            //
            searchKs:function () {
                this.getDatas();
            },
            searchCs:function () {
              this.rightGata();
            },
            //获取左边数据
            rightGata:function (index) {
                if ($("#jsvalues1").val() != null && $("#jsvalues1").val() != '') {
                    this.param.parm = $("#jsvalues1").val();
                } else {
                    this.param.parm = '';
                }
                this.param.ksid=index;
                this.param.rows=100000;
                wap.rList=[];
                $.getJSON("/actionDispatcher.do?reqUrl=EmrXtwhKsywhd&types=queryYwhdByKs&parm="+JSON.stringify(this.param),function (json) {
                    //注意此处如果有jq的代码必须要用
                    // tableInfo.jsonList而不是this.jsonList
                    if(json.a==0){
                        // yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                        wap.rList = json.d.list;
                    }

                });
            },

            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //保存
            saveData: function() {
                var list = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                        // var ksywhdjlid={};
                        // ksywhdjlid.ksywhdjlid=this.rList[i].ksywhdjlid
                        list.push(this.rList[i]);

                    }
                }
                if(list.length == 0){
                    malert("请选中您要添加的数据","top","defeadted");
                    return false;
                }
                var json='{"list":'+JSON.stringify(list)+'}'
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhKsywhd&types=save',json).then(function (data) {
                    if(data.body.a == 0){
                        yjkmtableInfo.getData();
                        wap.closes();
                        malert("保存成功","top","success");
                    } else {
                        malert("上传数据失败","top","defeadted");
                    }
                },function (error) {
                    console.log(error);
                });
            },

        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            popContent: {},
            jsonList: [],//
            iShow:false,
            isShowpopL:false,
            totlePage:0,
            total:'',

            page:'',
            kmbm:'',
            LcList:[],
            kmmc:'',
            rows:10,
            param: {
                page:1,
                rows:10,
                sort: '',
                order: 'asc',
                parm:'',
            },

        },
        methods: {

            getData: function () {
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }
                this.param.ksid=$("#lcsjzid").val();
                $.getJSON("/actionDispatcher.do?reqUrl=EmrXtwhKsywhd&types=query&parm="+JSON.stringify(this.param),function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if(json.a==0){
                        yjkmtableInfo.totlePage = Math.ceil(json.d.total/yjkmtableInfo.param.rows);
                        yjkmtableInfo.jsonList = json.d.list;
                    }

                });
            },



            //删除
            remove: function() {
                var list = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                        var ksywhdjlid={};
                        ksywhdjlid.ksywhdjlid=this.jsonList[i].ksywhdjlid
                        list.push(ksywhdjlid);
                    }
                }
                if(list.length == 0){
                    malert("请选中您要删除的数据","top","defeadted");
                    return false;
                }
                if(!confirm("请确认是否删除")){
                    return false;
                }
                var json='{"list":'+JSON.stringify(list)+'}'
                this.$http.post('/actionDispatcher.do?reqUrl=EmrXtwhKsywhd&types=updateBz',
                    json).then(function (data) {
                    if(data.body.a == 0){
                        malert("删除成功","top","success")
                        yjkmtableInfo.getData();
                    } else {
                        malert("删除失败","top","defeadted")
                    }
                }, function (error) {
                    console.log(error);
                });

            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑科室业务活动'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },


        },


    });
    yjkmtableInfo.getData();
    wrapper.LcGetData();
