.selectInput {
    width: 90%;
    height: 30px;
}

.toolMenu > div {
    width: 150px;
    height: 30px;
}

.toolMenu input {
    height: 30px;
    width: 150px;
    margin: 0 10px;
}

.tableDiv {
    width: calc(100% - 4px);
    height: calc(100% - 140px);
}

.brSearch {
    display: inline-block;
    width: 100%;
    border: 1px solid #eee;
    padding-bottom: 10px;
    padding-top: 8px;
}

.brSearch > div{
    float: left;
}

.brSearch > div > input{
    height: 30px;
    width: 90px;
    margin: 2px 0 0 10px;
}

.brSearch > div > span{
    margin-left: 10px;
}

.fyqdTime{
    width: 100%;
    text-align: center;
}

.fyqdContext{
    width: 780px;
    margin: 0 auto;
    padding-bottom: 30px;
}

.fyqdContext h2{
    width: 100%;
    text-align: center;
}

.infoIpt{
      width: 27%;
}

.infoIpt span{
    display: block;
    float: left;
    padding: 8px 0 0 0;
}

.fyqdTable td,
.fyqdTable th{
    border: 1px solid #000000;
}

.fyqdTable tr:first-child{
    text-align: center;
}

.total{
    margin-top: -1px;
    padding: 4px 10px;
    border: 1px solid #000000;
}

.fyqdTable td span:first-child{
    float: left;
}

.fyqdTable td span:last-child{
    float: right;
    margin-right: 176px;
}

.infoIpt{
    width: auto;
    min-width: 192px;
    height: 28px;
}

.infoIpt p{
    width: 70px;
    padding: 7px 0;
}
.float-clear::after{
    content: "";
    display: block;
    clear: both;
}
.fyqdTable table{
    border-collapse: collapse;
    width: 100%;
}
.table-box{
    border-top: 1px solid #000;
    font-size: 14px;
}
.table-box .table-head{
    text-align: center;
}
.table-box .col{
    display: -ms-flex;
    display: -moz-flex;
    display: -o-flex;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
}

.table-box .col .cell{
    overflow: hidden;
    border-right: 1px solid #000;
    border-bottom: 1px solid #000;
    width: 80px;
    padding: 0 6px;
    box-sizing: border-box;
    height: 35px;
    line-height: 16px;
    display: -ms-flex;
    display: -moz-flex;
    display: -o-flex;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
       -moz-box-align: center;
        -ms-flex-align: center;
            align-items: center;
}
.table-box .col .cell:first-child{
    border-left: 1px solid #000;
}
.table-box .col .cell.cell-one{
    display: -ms-flex;
    display: -moz-flex;
    display: -o-flex;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
}
.table-box .col .cell.cell-one .cell-one-last{
    -webkit-box-flex: 1;
    -webkit-flex: 1;
       -moz-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    margin-right: 150px;
    text-align: right;
}
.table-box .col .cell:nth-child(2),
.table-box .col .cell.cell-one{
    -webkit-box-flex: 1;
    -webkit-flex: 1;
       -moz-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
}
.table-box .col .cell:nth-child(6),
.table-box .col .cell:nth-child(4){
    width: 50px;
}
@media print{
    html,body{
        width: auto;
        height: auto;
        overflow-x: auto;
        display: block;
        min-width: auto;
    }
    .fyqdContext{

        width: 189mm;
    }
    .print-always{
        width: 100%;
        page-break-after: always;
    }
    .table-box .col.col-border-top{
        border-top: 1px solid #000;
    }
}