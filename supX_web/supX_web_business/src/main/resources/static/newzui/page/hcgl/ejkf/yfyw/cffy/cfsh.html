<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>处方管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="cffy.css" rel="stylesheet"/>
    <link href="pr.css" rel="stylesheet" media="print">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<style>
</style>
<body class="skin-default padd-b-10 padd-r-10 padd-l-10 padd-t-10">
<div class="printArea printShow"></div>
<div class="background-box printHide">
    <div class="wrapper" id="wrapper">
        <div class="panel " v-cloak>
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
                <div class="flex-container padd-l-10 padd-b-10 padd-t-10" >
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14  padd-r-5">检索</span>
                            <input data-enter="no" class="zui-input wh120" placeholder="请输入关键字" autocomplete="off" id="autofocus"
                                   type="text" @keydown.enter="goToPage(1)" v-model="search"/>
                    </div>

                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5">刷卡</span>
                            <input data-enter="no" type="text" autofocus title="就诊卡读取" class="zui-input wh120"
                                   placeholder="就诊卡读取" v-model="ylkh"
                                   @keydown.enter="loadYkt()" id="ykth"/>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5">药房</span>
                            <select-input class="wh122" @change-data="resultRydjChange" :child="yfList" :index="'yfmc'"
                                          :index_val="'yfbm'" :val="barContent.yfbm" :name="'barContent.yfbm'"
                                          :search="true"
                                          :index_mc="'kwmc'" :ksbm="barContent.ksbm" :cflx="barContent.cflx">
                            </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5">发药状态</span>
                        <select-input class="wh90" @change-data="resultRydjChange" :not_empty="false"
                                      :child="zhuangtai" :index="barContent.fybz" :val="barContent.fybz"
                                      :name="'barContent.fybz'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5">审核状态</span>
                        <select-input class="wh90" @change-data="resultChange" :not_empty="false"
                                      :child="shstatus" :index="barContent.cfshbz" :val="barContent.cfshbz"
                                      :name="'barContent.cfshbz'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class=" padd-r-5 ft-14">时间段</span>
                        <div class="zui-input-inline flex-container flex-align-c  -f-l15">
                            <input class="zui-input  wh200 " placeholder="请选择申请开始日期" id="timeVal"/><span
                                class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input  wh200 " placeholder="请选择申请结束时间" id="timeVal1"/>
                        </div>
                    </div>
                </div>
        </div>
        <div class="zui-table-view  padd-r-10 padd-l-10" v-cloak>
            <!--发药-->
            <div class="zui-table-header" >
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>处方号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>绿色通道病人</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>处方金额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>性别</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>年龄</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>科室</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>诊断名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>挂号序号/住院号</span></div>
                        </th>

                        <th>
                            <div class="zui-table-cell cell-s"><span>发药状态</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>审核状态</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>是否退药</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body  setScroll"  @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList" :tabindex="$index" @click="switchIndex('activeIndex',true,$index)"
                        :class="[{'table-hover':$index===hoverIndex},{'table-hovers':$index===activeIndex}]"
                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                        @mouseleave="switchIndex()"
                        :class="[{'table-hovers':$index===activeIndex},{'table-hover':$index===activeIndex}]" :data-fybz="item.fybz" @dblclick="Ckdetail($index,item.fybz)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.cfh">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="item.lstdbz == '1' ?'color-green':''" v-text="item.lstdbz=='1'?'是':'否'">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s text-right" v-text="fDec(item.cfje,2)">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="xtwhxb_tran[item.brxb]">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.nl}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brksmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s text-over-2" v-text="item.lczd">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.bah">序号</div>
                        </td>

                        <td>
                            <div class="zui-table-cell cell-s" v-text="zhuangtai[item.fybz]" :class="fybzClass[item.fybz]"></div>
                            <pop-page v-show="fybzShow[$index]" :date="'date'" :text="'text'" :type="'type'" :lsit="objData" @reslut="reslut"></pop-page>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s text-over-2" v-text="item.cfshbz=='1'?'已审核':'未审核'">序号</div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s" :class="tybzClass[item.tyzt]">
                                {{item.cfje < 0 ? '' : tyztArr[item.tyzt]}}
                            </div>
                        </td>
                        <td class="cell-l">
                            <div class="zui-table-cell cell-l">
                                        <span class="flex-center">
                                            <em class="width30" ">
                                                <i class="icon-width icon-fy-h" data-title="审核"
                                                   @click="fayao($index,item)"></i>
                                            </em>

                                        </span>

                            </div>
                        </td>
                        <p v-if="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>

            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l" >
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','jsonList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r" >
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>

                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>发药状态</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>是否退药</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>审核状态</span></div>
                            </th>
                            <th class="cell-l">
                                <div class="zui-table-cell cell-l"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','jsonList'],$event)">
                            <td class="cell-s">
                                <div :data-fybz="item.fybz" class="zui-table-cell cell-s" v-text="zhuangtai[item.fybz]"
                                     :class="fybzClass[item.fybz]">
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s"
                                     :class="tybzClass[item.tyzt]">
                                	{{item.cfje < 0 ? '' : tyztArr[item.tyzt]}}
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s text-over-2" >{{item.cfshbz=='1'?'已审核':'未审核'}}</div>
                            </td>
                            <td class="cell-l">
                                <div class="zui-table-cell cell-l">
                                    <span class="flex-center">
                                        <em class="width30">
                                            <i class="icon-width icon-fy-h" data-title="审核"
                                               @click="fayao($index,item)"></i>
                                        </em>

                                    </span>

                                </div>
                            </td>

                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-850 printHide" v-cloak  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--编辑药品-->
    <div class="ksys-side">
        <div class="jbxx flex-container flex-dir-c" style="height:100%;">
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">患者信息</div>
                </div>
                <div class="grid-box tab-card-body">
                    <ul class="cffy-list">
                        <li>
                            <!--<i v-if="popContent.bah.length==14 || popContent.bah==''" style="width: 25%">挂号序号:<em
                                    v-text="popContent.bah"></em></i>
                            <i v-if="popContent.bah.length!=14 &&  popContent.bah.length!=''" style="width: 25%">住院号:<em
                                    v-text="popContent.bah"></em></i>-->
                            <i>处方号:<em v-text="popContent.cfh"></em></i>
                            <i><em v-text="popContent.brxm"></em></i>
                            <i><em v-text="brxb_tran[popContent.brxb]"></em></i>
                            <i><em v-text="popContent.nl"></em></i>
                            <i>医生:<em v-text="popContent.cfysxm"></em></i>
                        </li>
                        <li class="flex-container">
                            <div class="flex-container width50">
                                <p class="whiteSpace">过敏史：</p>
                                <p class="zui-table-cell overflowHide">{{popContent.gms}}</p>
                            </div>
                            <div class="flex-container width50">
                                <p class="whiteSpace">临床诊断：</p>
                                <p class="zui-table-cell overflowHide">{{popContent.lczd}}</p>
                            </div>
                        </li>
                        <li v-if="popContent.cflb==2">
                            <i>主治:<em v-text="popContent.zyzf"></em></i>
                            <i>主症:<em v-text="popContent.zyzh"></em></i>
                            <i>剂数:<em v-text="popContent.zyfs"></em></i>
                            <i>用法:<em v-text="popContent.yysm"></em></i>
                        </li>
                    </ul>
                </div>
            </div>
            <div v-if="tyShow==false" class="zui-table-view flex-one flex-container flex-dir-c">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell text-left cell-xl"><span>药品名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell text-left cell-s"><span>药品规格</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>单次剂量</span></div>
                            </th>
                            <!--<th>-->
                                <!--<div class="zui-table-cell cell-s"><span>频次</span></div>-->
                            <!--</th>-->
                            <th>
                                <div class="zui-table-cell cell-s"><span>频次名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>用药用量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>用药方法</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>用药说明</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>进价</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>零价</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品批号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品产地</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body " @scroll="scrollTable($event)" >
                    <table class="zui-table">
                        <tbody>
                        <tr style="border-top: 1px #354052 solid;border-bottom: 1px #354052 solid;" v-for="(item,$index) in jsonList" :tabindex="$index" @click="switchIndex('activeIndex',true,$index)"
                            :class="[{'table-hover':$index===hoverIndex},{'table-hovers':$index===activeIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)"
                            @mouseleave="switchIndex()" class="tableTr2">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m"><span v-text="$index+1">序号</span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell text-left cell-xl" v-text="item.ypmc">药品名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell text-left cell-s" v-text="item.ypgg">药品规格</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">{{item.yyjl || ''}} {{item.yyjl ? item.jldwmc :''}}</div>
                            </td>
                            <!--<td>-->
                                <!--<div class="zui-table-cell cell-s" v-text="item.yypc">频次</div>-->
                            <!--</td>-->
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yypcmc">频次名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cfyl+item.yfdwmc">用药用量</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yyffmc">用药方法</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yysm">用药说明</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s text-right" v-text="fDec(item.ypjj,2)">进价</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s text-right" v-text="fDec(item.yplj,2)">零价</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.scph">药品批号</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cdmc">药品产地</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div v-if="tyShow" class="zui-table-view flex-one flex-container flex-dir-c">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell text-left cell-xl"><span>药品名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell text-left cell-l"><span>药品规格</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>用量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>退药</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>已退数量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>进价</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>零价</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body " @scroll="scrollTable($event)" >
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item,$index) in jsonList" :tabindex="$index"
                            :class="[{'table-hover':$index===hoverIndex},{'table-hovers':$index===activeIndex}]"
                            @mouseenter="switchIndex('activeIndex',true,$index)"
                            @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,$index)"
                            class="tableTr2">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m"><span v-text="$index+1">序号</span></div>
                            </td>
                            <td>
                                <div class="zui-table-cell text-left cell-xl" v-text="item.ypmc">药品名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell text-left cell-l" v-text="item.ypgg">药品规格</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cfyl">用量</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <input type="text" value="" class="zui-input" style="height: 24px;"
                                           v-model="item.tysl"/>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                  {{item.ytsl}}
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">进价</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">零价</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="ksys-btn">
        <div class="slgl-fl" v-show="tyShow"><i class="icon-width icon-zy"></i><em style="padding-left: 32px;">请注意药品使用合理性！</em>
        </div>
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" v-if="!printshow" @click="fysh" v-text="contents"></button>

    </div>
</div>


<script src="cfsh.js"></script>
</body>

</html>
