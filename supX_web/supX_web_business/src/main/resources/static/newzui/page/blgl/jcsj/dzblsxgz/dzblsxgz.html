<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>电子病历书写规则</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="dzblsxgz.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">添加</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
            <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50-1">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="jm20" z-width="80px">
                        <div class="zui-table-cell">书写规则id</div>
                    </th>
                    <th z-field="jm21" z-width="80px">
                        <div class="zui-table-cell">规则简码</div>
                    </th>
                    <th z-field="jm22" z-width="100px">
                        <div class="zui-table-cell">规则名称</div>
                    </th>
                    <th z-field="jm23" z-width="100px">
                        <div class="zui-table-cell">违规关系</div>
                    </th>
                    <th z-field="jm24" z-width="100px">
                        <div class="zui-table-cell">违规内容</div>
                    </th>
                    <th z-field="jm25" z-width="100px">
                        <div class="zui-table-cell">违规提示信息</div>
                    </th>
                    <th z-field="jm26" z-width="100px">
                        <div class="zui-table-cell">必填项判断</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-height" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jsonList"  @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.sxgzid"></div></td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.gzjm" :data-title="item.gzjm"></i>
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.gzmc" :data-title="item.gzmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.wggx" :data-title="item.wggx"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.wgnr" :data-title="item.wgnr"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.wgtsxx" :data-title="item.wgtsxx"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.btxpd" :data-title="item.btxpd"></i>
                        </div>
                    </td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>书写规则id</i>
                    <input type="text" class="label-input background-h" disabled v-model="popContent.sxgzid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>规则名称</i>
                    <input type="text" class="zui-input border-r4"  v-model="popContent.gzmc"  @keydown="nextFocus($event)"
                           @blur="setPYDM(popContent.gzmc,'popContent','gzjm')" />
            </li>
            <li>
                    <i>规则简码</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.gzjm" @keydown="nextFocus($event)"/>
            </li>
            <li class="width100">
                    <i class="width150">规则表达式</i>
                    <textarea  class="label-input dz-height"  v-model="popContent.gzbds" @keydown="nextFocus($event)"></textarea>
            </li>
            <li class="width100" style="height: 100px !important;">
                    <i class="width150">规则表达式定义说明</i>
                    <div class="dz-height">
                        表达式由四段组成：[参数] &lt;运算符&gt; '值' 关系 。 分段用空格分开。1、参数：放在综括号中。参数分两类取值，一类：全文档检索，直接命名为[文档]；二类：域值检索，取电子病历标准数据集中的内部标识符和固定域名编码，如[HR02.02.001]代表性别。2、运算符：放在尖括号中，取值为：‘大于’、‘小于’、‘等于’或‘包含’。3、值：放在单引号中代表要判定的值。4、关系：为数学逻辑关系，取值：‘并且’或‘或者’连接多个表达式。 特别说明：每一对关系必须用半角括号‘()’括起来。例如：([HR02.02.001] &lt;等于&gt; '女') 代表：性别=女
                    </div>
            </li>
            <li>
                    <i>违规关系</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="wggx" :index="popContent.wggx" :val="popContent.wggx"
                                  :name="'popContent.wggx'">
                    </select-input>
            </li>
            <li class="width100">
                    <i class="width150">违规内容</i>
                    <textarea  class="label-input dz-height"  v-model="popContent.wgnr" @keydown="nextFocus($event)"></textarea>
            </li>
            <li class="width100">
                    <i class="width150">违规提示信息</i>
                    <textarea  class="label-input dz-height"  v-model="popContent.wgtsxx" @keydown="nextFocus($event)"></textarea>
            </li>
            <li class="width100">
                    <i class="width150">必填项判断</i>
                    <textarea  class="label-input dz-height"  v-model="popContent.btxpd" @keydown="nextFocus($event)"></textarea>
            </li>
            <li class="width100" style="height: 100px !important;">
                    <i class="width150">必填项判断说明</i>
                    <div class="dz-height padd-t-10">
                        判断域值是否为空。判断项用综括号括起来，多个判断项之间用逗号分隔。例如：“[患者姓名],[患者性别]”
                    </div>
            </li>

        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="dzblsxgz.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
