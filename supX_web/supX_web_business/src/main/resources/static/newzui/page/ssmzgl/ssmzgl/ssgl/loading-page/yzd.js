/**
 * Created by mash on 2017/10/10.
 */
var printGd=20


var cqyzd = new Vue({
    el: '.cqyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        jsonList: [],
        pageList: [],
        isShow: true,
        param: {},
        which: 0,
        BrxxJson: {},
        pageH: 790,
        isGoPrint: false            // 是否续打
    },
    watch: {
        jsonList: function () {
            toolMenu_yzd.setPrintData();
        }
    },
    methods: {
        doPrint: function (isGoOn) {
            $('.no-print').html('')
            var cqTr;
            cqPrint.list = [];
            lsPrint.list = [];
            if (this.which == 0) {
                cqTr = $(".cqyzd tr");
            } else {
                cqTr = $(".lsyzd tr");
            }
            var _height = 0;
            var a = 0, b = -1;
            for (var i = 1; i < cqTr.length - 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height >= cqyzd.pageH) {
                    b++;
                    var as = [];
                    for (var f = a; f < i; f++) {
                        if (this.which == 0) {
                            as.push(cqyzd.jsonList[f]);
                        } else {
                            as.push(lsyzd.jsonList[f]);
                        }
                    }
                    if (this.which == 0) cqPrint.list[b] = as;
                    else lsPrint.list[b] = as;
                    a = i;
                    _height = 0;
                    this.pageList.push(as.length);
                }
            }
            var pp = [];
            if (this.which == 0) {
                for (var p = a; p < cqyzd.jsonList.length; p++) {
                    pp.push(cqyzd.jsonList[p]);
                }
            } else {
                for (var ls = a; ls < lsyzd.jsonList.length; ls++) pp.push(lsyzd.jsonList[ls]);
            }
            for (var l = 0; l < 21; l++) {
                _height += 40;
                if (_height >= cqyzd.pageH) {
                    break;
                }
                pp.push({ 'psjg': '无' });
            }
            if (this.which == 0) {
                cqPrint.list[b + 1] = pp;
                cqPrint.isShow = true;
                lsPrint.isShow = false;
            } else {
                lsPrint.list[b + 1] = pp;
                lsPrint.isShow = true;
                cqPrint.isShow = false;
            }
            if (isGoOn) {
                cqPrint.isGoPrint = true;
                lsPrint.isGoPrint = true;
                setTimeout(function () {
                    cqyzd.hideTable(cqyzd.which);
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border-left', '1px solid transparent');
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("tr").css('border', '1px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border-left', '1px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("tr").css('border', '1px solid transparent');
                }, 50);
                setTimeout(function () {
                    window.print();
                    cqPrint.isGoPrint = false;
                    lsPrint.isGoPrint = false;
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border-left', '1px solid #999');
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("tr").css('border', '1px solid #999');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border-left', '1px solid #999');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("tr").css('border', '1px solid #999');
                }, 100);
            } else {
                setTimeout(function () {
                    window.print();
                    printGd=20
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                }, 100);
            }
        },
        hideTable: function (type) {
            var num = 0;
            if (type == 0) {
                for (var i = 0; i < cqPrint.pagePrint; i++) {
                    $('.cqPrint .popCenter').eq(i).hide();
                    num += this.pageList[i];
                }
                cqPrint.isChecked = cqPrint.isChecked - num;
            } else {
                for (var j = 0; j < lsPrint.pagePrint; j++) {
                    $('.lsPrint .popCenter').eq(j).hide();
                    num += this.pageList[j];
                }
                lsPrint.isChecked = lsPrint.isChecked - num;
            }
        },
        goPrint: function (index) {
            cqyzd.isChecked = index;
            cqPrint.isChecked = index;
            var cqTr = $(".cqyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > cqyzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            cqPrint.pagePrint = b;
        },
        getData: function () {
            if (!cqyzd.BrxxJson.zyh ) {
                malert("请选择病人后再查看医嘱单！");
                return
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                zyh: this.BrxxJson.zyh,
                yzlx: '1'
            };
            // if(toolMenu_yzd.searchContent.yfbm!=''){
            //     this.param.yyffbm=toolMenu_yzd.searchContent.yfbm;
            // }
            // if(toolMenu_yzd.searchContent.yypc!=''){
            //     this.param.pcbm=toolMenu_yzd.searchContent.yypc;
            // }
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    cqyzd.jsonList = json.d.list;
                    for(var i = 0; i < cqyzd.jsonList.length; i++){
                        cqyzd.jsonList[i]['xmmc'] = cqyzd.jsonList[i]['xmmc'].replace('null', '');
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc'].replace('null', '');
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc'].replace('null', '');
                    }
                } else {
                    malert("病人医嘱单信息查询失败！",'top','defeadted');
                }
            });
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var prvVal = null, nextVal = null;
            if (index != this.jsonList.length - 1 && index != 0) {
                prvVal = this.jsonList[index - 1][name];
                nextVal = this.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return this.Appendzero((reDate.getMonth() + 1)) + '-' + this.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return this.Appendzero(reDate.getHours()) + ':' + this.Appendzero(reDate.getMinutes());
            }
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if(fzh == 0) return false;
            if(index == 0 && fzh == this.jsonList[index + 1]['fzh']){
                return 'start';
            }
            if(index != 0 && index != this.jsonList.length - 1){
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if(fzh == null || fzh != nextFzh && fzh != prvFzh){
                    return 'null';
                }
                if(fzh == nextFzh && fzh != prvFzh){
                    return 'start';
                }
                if(fzh != nextFzh && fzh == prvFzh){
                    return 'end';
                }
                if(fzh == nextFzh && fzh == prvFzh){
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = cqPrint.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var lsyzd = new Vue({
    el: '.lsyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        jsonList: [],
        isShow: false,
        param: {},
        BrxxJson: {},
        isGoPrint: false            // 是否续打
    },
    methods: {
        goPrint: function (index) {
            lsyzd.isChecked = index;
            lsPrint.isChecked = index;
            var cqTr = $(".lsyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > cqyzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            lsPrint.pagePrint = b;
        },
        getData: function () {
            if (!lsyzd.BrxxJson.zyh) {
                malert("请选择病人后再查看医嘱单！");
                return
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                zyh: this.BrxxJson.zyh,
                yzlx: '0'
            };
            // if(toolMenu_yzd.searchContent.yfbm!=''){
            //     this.param.yyffbm=toolMenu_yzd.searchContent.yfbm;
            // }
            // if(toolMenu_yzd.searchContent.yypc!=''){
            //     this.param.pcbm=toolMenu_yzd.searchContent.yypc;
            // }
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    lsyzd.jsonList = json.d.list;
                    for(var i = 0; i < lsyzd.jsonList.length; i++){
                        lsyzd.jsonList[i]['xmmc'] = lsyzd.jsonList[i]['xmmc'].replace('null', '');
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc'].replace('null', '');
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc'].replace('null', '');
                    }
                } else {
                    malert("查询临时医嘱失败！");
                }
            });
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = this.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            if (val == prvVal && index != 0) {
                return '"';
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if(fzh == 0) return false;
            if(index == 0 && fzh ==  this.jsonList[index + 1]['fzh']){
                return 'start';
            }
            if(index != 0 && index != this.jsonList.length - 1){
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if(fzh == null || fzh != nextFzh && fzh != prvFzh){
                    return 'null';
                }
                if(fzh == nextFzh && fzh != prvFzh){
                    return 'start';
                }
                if(fzh != nextFzh && fzh == prvFzh){
                    return 'end';
                }
                if(fzh == nextFzh && fzh == prvFzh){
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = cqPrint.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var cqPrint = new Vue({
    el: '.cqPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: 0,
        BrxxJson: {},
        isGoPrint: false,
        printGd:20,
    },
    filters:{
        compuGd:function (index) {
            if(index>=1){
                return  'paddingTop:'+(printGd+=10)+'px'
            }
        },
    },
    methods: {

        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                cqPrint.isGoPrint = false;
                printGd=20
                $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var val = cqyzd.jsonList[index][name];
            var prvVal = cqyzd, nextVal = null;
            if (index != cqyzd.jsonList.length - 1 && index != 0) {
                prvVal = cqyzd.jsonList[index - 1][name];
                nextVal = cqyzd.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var fzh = cqyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == cqyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != cqyzd.jsonList.length - 1) {
                var nextFzh = cqyzd.jsonList[index + 1]['fzh'];
                var prvFzh = cqyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }

            }
            if (index == cqyzd.jsonList.length - 1) {
                return 'end'
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});
var lsPrint = new Vue({
    el: '.lsPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: null,
        BrxxJson: {},
        isGoPrint: false
    },
    filters:{
        compuGd:function (index) {
            if(index>=1){
                return  'paddingTop:'+(printGd+=10)+'px'
            }
        },
    },
    methods: {
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                lsPrint.isGoPrint = false;
                $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = lsyzd.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            if (val == prvVal && index != 0) {
                return '"';
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) {
                return null;
            }
            var fzh = lsyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == lsyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != lsyzd.jsonList.length - 1) {
                var nextFzh = lsyzd.jsonList[index + 1]['fzh'];
                var prvFzh = lsyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            if (lsyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (lsyzd.jsonList[index]['fzh'] == lsyzd.jsonList[index + 1]['fzh']) {
                if (lsyzd.jsonList[index]['yyffmc'] == lsyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var toolMenu_yzd = new Vue({
    el: '#toolMenu_yzd',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        which: 0,
        yfList:{
            'a': "用法1"
        },
        yf: "a",
        pcList: {
            "a": "20~80"
        },
        pc: "a",
        yyffList:[],
        yypcList:[],
        qsxzList:[],
        searchContent:{},
        popContent:{},
    },
    mounted:function(){
        this.initData()
        window.addEventListener('setItemEvent', function (e) {
            if(e.key=='yzshly'){
                toolMenu_yzd.initData()
            }
        });
        window.addEventListener('storage',function (e) {
            if(e.key=='yzshly'){
                toolMenu_yzd.initData()
            }

        });
        this.getYf();
        // this.getPc();
        // this.getPc();
    },
    methods: {
        initData:function(){
            cqyzd.BrxxJson=JSON.parse(sessionStorage.getItem("yzshly"));
            lsyzd.BrxxJson=JSON.parse(sessionStorage.getItem("yzshly"));
            lsPrint.BrxxJson=JSON.parse(sessionStorage.getItem("yzshly"));
            cqPrint.BrxxJson=JSON.parse(sessionStorage.getItem("yzshly"));
            cqyzd.getData();
        },
        iscf: function () {
            var parm = {
                zyh: cqyzd.BrxxJson.zyh
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' + JSON.stringify(parm), function (json) {
                if (json.a == '0') {
                    if (json.d.list != null && json.d.list.length > 0) {
                        var qb = {
                            yexm: cqyzd.BrxxJson.brxm,//如果有影响请还原上面代码，注释本行代码
                            yebh: "000",
                        }
                        json.d.list.push(qb);
                        toolMenu_yzd.qsxzList = json.d.list;//亲属选择
                    } else {

                    }
                }
            });
        },
        long: function (num) {
            this.which = num;
            cqyzd.which = num;
            lsyzd.isShow = false;
            cqyzd.isShow = true;
            cqyzd.getData();
        },
        short: function (num) {
            this.which = num;
            cqyzd.which = num;
            cqyzd.isShow = false;
            lsyzd.isShow = true;
            lsyzd.getData();
        },
        // 加上假数据填充格子
        setPrintData: function () {
            var sa = 20 - cqyzd.jsonList.length;
            for (var i = 0; i < sa; i++) {
                cqyzd.jsonList.push({});
            }
        },
        doPrint: function (istpye) {
            // cqyzd.isGoPrint = false;
            // lsyzd.isGoPrint = false;
            // setTimeout(function () {
            //     window.print();
            // }, 100);
            cqyzd.doPrint(istpye);
        },
        resultChangePc:function(val){
            Vue.set(this[val[2][0]], [val[2][1]], val[0]);
            Vue.set(this[val[2][0]], 'pcbm', val[0]);
            if(toolMenu_yzd.which==0){
                cqyzd.getData();
            }else{
                lsyzd.getData();
            }
        },
        resultChangeYf:function(val){
            Vue.set(this[val[2][0]], [val[2][1]], val[0]);
            Vue.set(this[val[2][0]], 'yyffbm', val[0]);
            if(toolMenu_yzd.which==0){
                cqyzd.getData();
            }else{
                lsyzd.getData();
            }
        },
        getYf:function(){
            var json = {
                sfcy: 0
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yyff" + '&json=' + JSON.stringify(json),
                function (json) {
                    if (json.a == 0) {
                        toolMenu_yzd.yyffList = json.d.list;
                        var nlYf={
                            yyffbm:'',
                            yyffmc:'全部'
                        };
                        toolMenu_yzd.yyffList.push(nlYf);
                        toolMenu_yzd.searchContent.yfbm='';
                    } else {
                        malert('用药方法列表查询失败'+json.c,'top','defeadted')
                        return;
                    }
                });
        },
        getPc:function(){
            var pc_dg = {page: 1, rows: 100, sort: "sor", order: "asc", parm: ""};
            $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmpc&types=query&dg=" + JSON.stringify(pc_dg), function (json) {
                if (json.a == 0) {
                    toolMenu_yzd.yypcList = json.d.list;  //频次下拉窗口绑定数据
                    var nlPc={
                        pcbm:'',
                        pcmc:'全部'
                    };
                    toolMenu_yzd.yypcList.push(nlPc);
                    toolMenu_yzd.searchContent.yypc='';
                } else {
                    malert('频次列表查询失败'+json.c,'top','defeadted')
                    return;
                }
            });
        },
        goOnPrint: function () {
            cqyzd.isGoPrint = true;
            lsyzd.isGoPrint = true;
            $('.yzd-table td').css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                $('.yzd-table td').css('border', '1px solid #999');
            }, 100);
        }
    }
});