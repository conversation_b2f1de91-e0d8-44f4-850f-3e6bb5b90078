
.wrapper{
    background-color: #fff;
    height: 100%;
    padding-bottom: 66px!important;
    overflow: auto;
}
.userNameBg{
    background:#708f89;
    /*height:180px;*/
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    background-image: url("/newzui/pub/image/userImg.png");
    padding: 10px;
}
.flex{
    display: flex;
    align-items: center;
}
.userNameImg img{
    width: 100px;
}
.text-color{
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}
.zyh,.bq,.ys,.brzt,.bz,.cwh{
    margin-right: 60px;
}
.userCwh{
    margin-bottom: 4px;
}
.fyhj {
    margin-right: 39px;
}
.yjhj {
    margin-right: 104px;
}
.zyts {
    margin-right: 32px;
}
.phone {
    margin-right: 53px;
}
.hl {
    margin-right: 52px;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{
    color: #B0BFBB;
    text-decoration: underline;
}
.ksys-btn{
    background-color: #fff;
}
.zui-form-label{
    width: 60px;
}
.zui-form .zui-inline {
    padding: 0 0px 0 60px;
}
.zui-inline, .zui-input-inline, .zui-select-inline{
    margin-right: 0px;
}
.todate{
    width: 448px;
}
.tisi{
    color:#f2a654;
    margin-top: 10px;
}
.icon-width{
    width: 24px;
    display: inline-block;
    height: 24px;
    position: relative;
}
.icon-width::before {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 0px;
    top: 0px;
}
.action-bar.fixed{
    right: 10px;
    left: 10px;
    width: auto;
    bottom: 10px;
    padding-top: 16px;
    padding-bottom: 16px;
    height: 68px;
}
.zui-table-view{
    margin: 10px;
    margin-bottom: 0;
}