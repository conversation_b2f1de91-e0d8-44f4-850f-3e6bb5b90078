(function () {
    $(".zui-table-view").uitable();
    var s=new Date().getTime()
    var l=new Date()
    var e=l.setDate(l.getDate()+1)
    var wrapper = new Vue({
        el: '.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            index: 1,
            old: '原始数据',
            pop: {},
            param: {
                page: 1,
                rows: 10,
                total: '',
                time:'',
                bah: ''
            },
            jydjList: []
        },

        methods: {
            //过滤
            guolu:function () {
                isTabel.isShow=true;
            } ,
            //打印
            Btnprint:function () {
                window.print();
            },
            sx : function (){
            	//解析时间
                if (this.param.time != null) {
                    var times = this.param.time.split(" - ");
                    this.param.sqrq = times[0];
                    this.param.sqrqEnd = times[1];
                }
                this.param.lx = '0';//门诊
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryShcg_mz&yq=" + JSON.stringify(this.param), function (json) {
                	console.log(json);
                    if (json.a == 0) {
                        $(".zui-table-view").uitable();
                    	list.totlePage =Math.ceil(json.d.total / wrapper.param.rows);
                        list.jydjList = json.d.list;
                        malert('查询成功','top','success');
                        
                    } else {
                        malert("获取申请失败" + json.c);
                        return false;
                    }
                });
            },
            util:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=gltjutil&yq=", function (json) {
            		isTabel.util=json.d;
                });
            },
        },
        watch:{
            'jydjList':function () {

            },
        	'param.time':function(){
        		this.sx();
        	},
        	'param.bah':function(){
        		this.sx();
        	}
        }
    });
    var isTabel=new Vue({
        el:'#isTabel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isTabelShow:false,
            isShow:false,
            minishow:true,
            isShowpopL:false,
            popContent:{},
            item: 0,
            appNum:[],
            appObj:{},
            
            cxtjList:[],
            util:{},
            isLxNum:'',
            isTyNum:'',
            isXbNum:'',
            isKsNum:'',
            isYsNum:'',
            isYblxNum:'',
        },
        created:function () {
            this.append()
        },
        methods:{
        	resultChange_item: function (val) {
        		console.log(val);
        		var index=val[2][0];
        		if(val[0]=='LX'){//类型
        			var pd=index+',';
        			this.isLxNum+=pd;
        		}
        		if(val[0]=='BAH' || val[0]=='BRXM' || val[0]=='NL' || val[0]=='FYMC' || val[0]=='XZ'){//通用
        			var pd=index+',';
        			this.isTyNum+=pd;
        		}
        		if(val[0]=='XB'){//性别
        			var pd=index+',';
        			this.isXbNum+=pd;
        		}
        		if(val[0]=='KSBM'){//科室
        			var pd=index+',';
        			this.isKsNum+=pd;
        		}
        		if(val[0]=='SQYS'){//医生
        			var pd=index+',';
        			this.isYsNum+=pd;
        		}
        		if(val[0]=='YBLX'){//样本类型
        			var pd=index+',';
        			this.isYblxNum+=pd;
        		}
        		
        	    Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
        	    if(event.keyCode != null){
        	    	this.nextFocus(val[1], parseInt(val[2][2]));
        	    }
        	},
        	resultChangeTj_item: function (val) {
        	    Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
        	    if(event.keyCode != null){
        	    	this.nextFocus(val[1], parseInt(val[2][2]));
        	    }
        	},
        	resultChangeLjtj_item: function (val) {
        		Vue.set(this.cxtjList[val[2][0]], [val[2][1]], val[0]);
        		if(event.keyCode != null){
        			this.nextFocus(val[1], parseInt(val[2][2]));
        		}
        	},
        	Wf_YppfChange: function (val) {
        	    var index = "";
        	    //先获取到操作的哪一个
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        var types = val[2][val[2].length - 1];
        	        index = val[2][1];
        	        Vue.set(this.cxtjList[index], 'jg', val[0]);
        	    }
        	    
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        if (window.event.keyCode == 13) {
        	            this.nextFocus(event);
        	        }
        	    }
        	},
        	Wf_YsChange: function (val) {
        	    var index = "";
        	    //先获取到操作的哪一个
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        var types = val[2][val[2].length - 1];
        	        index = val[2][1];
        	        Vue.set(this.cxtjList[index], 'jg', val[0]);
        	    }
        	    
        	    if (typeof val == 'object') {//判断是否是属于对象（下拉框）
        	        if (window.event.keyCode == 13) {
        	            this.nextFocus(event);
        	        }
        	    }
        	},
        	
        	save:function(){
        		console.log(this.cxtjList);
        		for (var int = 0; int < this.cxtjList.length; int++) {
        			if (wrapper.param.time != null) {
                        var times = wrapper.param.time.split(" - ");
                        this.cxtjList[int].starttime = times[0];
                        this.cxtjList[int].endtime = times[1];
                    }
        			this.cxtjList[int].type='0'
				}
        		var json = '{"list":' + JSON.stringify(this.cxtjList) + '}';
        		this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=gjcx',json).then(
                		function(data) {
                			console.log(data);
                			if(data.body.a==0){
                				list.jydjList = data.body.d.list;
                				 malert('查询成功','top','success');
                			}else{
                				malert('查询失败，请检查参数是否正确','top','defeadted');
                			}

                        },
                        function(error) {
                        	malert(error,'top','defeadted');
                        });
        	},
        	
        	
            sc: function (index) {
                this.appNum.splice(index,1)
                // for(var i=0;i<this.appNum.length;i++){
                //     if(this.appNum[i].num==index){
                //
                //     }
                // }

            },
            append: function () {
            	this.cxtjList.push({});
            }

        },
    })


    var list = new Vue({
        el: '.ybglTable',
        mixins: [dic_transform,  baseFunc, tableBase, mformat],
        data: {
            jydjList: '',
            page: 1,
            rows: 10
        },
        created:function(){
            wrapper.param.time=this.formDate(s)+' - '+this.formDate(e)
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods: {
        	 formDate: function (value) {
                 var d = new Date(value);
                 return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
             },
        	getData: function (){
        		wrapper.param.rows=this.rows;
            	wrapper.param.page=this.page;
        		wrapper.sx();
        	}
        }
    });

    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            wrapper.param.time = value
        }
    });

    // document.onkeydown = function (ev) {
    //     var ev = window.event || ev;
    //     var key = ev.keyCode;
    //     if (key == 83 && ev.ctrlKey) {
    //         return false
    //     }
    // };

    window.doCheck = function (event){
        var $event = $(event.srcElement.previousElementSibling);
        $event.prop("checked", !$event.prop("checked"));
    };
})();