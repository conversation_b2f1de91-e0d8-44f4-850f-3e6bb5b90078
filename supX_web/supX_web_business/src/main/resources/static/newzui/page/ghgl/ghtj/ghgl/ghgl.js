/**
 * Created by mash on 2017/9/13.
 */
    var search = new Vue({
        el: '.toolMenu',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            ksrq: '', //开始时间
    		jsrq: '',  //结束时间
        },
        //页面渲染完成之后加载数据
        mounted: function () {},
        methods: {
        	//检索按钮
			getData: function(){
				ghList.getList();
			}
        }
    });

    var ghList = new Vue({
        el: '.ghList',
        mixins: [dic_transform, mformat, tableBase],
        data: {
            list: [],
            total: null,
            page: 1,
            showPage: [],
            show:undefined,
            str_param: {
                page: 1,
                rows: 10,
                sort: 'ghxh',
                order: 'desc',
                total: null
            },
            page:{
                page:1,
                rows:20,
                total:null,
                parm:""
            },
            param:{
                page: 1,
                rows: 20,
                sort: 'ghxh',
                order: 'desc',
                total: null
            },
        },
        created: function () {
            this.getList();
        },
        mounted:function () {
            changeWin();
        },
        watch: {
            page: function (val) {
                if(this.total > 5){
                    this.showPage = [1, val - 1, val, val + 1, this.total];
                } else {
                    this.showPage = [1, 2, 3, 4, 5];
                }
            }
        },
        methods: {
            detail:function(){
                alert('你点击的是详情按钮')
            },
            close:function(){
                alert('你点击的是取消按钮')
            },
            zuofei:function(){
                alert('你点击的是作废按钮')
            },
            showclick:function(index){
              this.show=index
            },
            getList: function () {
            	this.param.beginrq = search.ksrq;
            	this.param.endrq = search.jsrq;
                $.getJSON("/actionDispatcher.do?reqUrl=GhglGhywBrgh&types=queryJzlb&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                        ghList.list = json.d.list;
                        ghList.total = json.d.total;
                        ghList.totlePage = Math.ceil(json.d.total / ghList.param.rows);
                    } else {
                        malert("获取挂号列表失败" + json.c,'top','defeadted');
                    }
                });
            },
            getData:function(){
                ghList.getList();
            }
            // goPage: function (index) {
            //     this.page = index;
            //     this.str_param.page = index;
            //     this.getList();
            // },
        }
    });
        laydate.render({
            elem: '#timeVal'
            , trigger: 'click'
            , theme: '#1ab394'
            , type: 'date'
            ,done:function (value,data) {
                search.ksrq = value
            }
        });
        laydate.render({
             elem: '#timeVal1'
            , trigger: 'click'
            , theme: '#1ab394'
            , type: 'date'
            ,done:function (value,data) {
                search.jsrq = value
            }
        });
    // 点击空白隐藏弹出层
    $(document).mouseup(function (e) {
        var bol = $(e.target).parents().is(".btn-blue");
        if (!bol) {
            ghList.show=undefined
        }
    });
