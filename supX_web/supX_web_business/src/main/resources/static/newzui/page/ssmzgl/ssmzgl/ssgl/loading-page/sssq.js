var sssq=new Vue({
    el:'.sssq',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        pageState:{},
        ysData:[],
        json:{
            ysbz: '1',
            tybz: '0',
        },
        val:false,
        dg:{
            page:'1',
            rows:10,
            parm:'',
        },
    },
    created:function () {
    this.getYs()
    },
    mounted:function(){
        laydate.render({
            elem: '.sqTime'
            , trigger: 'click'
            , theme: '#1ab394'
            , done: function (value, data) {
                sssq.pageState.sqri = value;
            }
        });
        laydate.render({
            elem: '.jhTime'
            , trigger: 'click'
            , theme: '#1ab394'
            , done: function (value, data) {
                sssq.pageState.jhrq = value;
            }
        });
    },
    methods:{
        getYs:function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(this.json) + "" + "&dg=" + JSON.stringify(this.dg), function (data) {
                    if(data.a=='0' && data.d.list.length!=0){
                        sssq.ysData = data.d.list;
                    }
            });
        },
        doCheck:function () {

        }
    },
})
