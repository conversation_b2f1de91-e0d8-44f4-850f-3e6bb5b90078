var yzlb = new Vue({
    el: '#loadingPage',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
    	brlistjson:{},//只用于接受请求LIST对象
        brList:[],
        yzList: [],
        jkList:[],
        yzzxInfoList:[],//真正的列表
        zyhs:[],
        ksid:null,//科室编码
        ifClick:true,
        yzlx: '1' //医嘱类型
    },
    mounted: function () {
        this.moun();
        window.addEventListener('storage',function (e) {
            if( e.key == 'yzlb' && e.oldValue !== e.newValue ){
            	yzlb.zyhs = [];
            	yzlb.moun();
            }
        });
    },
    updated: function () {
      changeWin()
    },
    methods: {
        moun:function () {
            this.brlistjson=JSON.parse( sessionStorage.getItem( 'yzlb' ) );
            this.brList =this.brlistjson.brlist;
            this.ksid=this.brlistjson.ksid;
            for(var i=0;i<this.brList.length;i++){
                var zyh={
                    zyh:this.brList[i].zyh
                };
                this.zyhs.push(zyh);
            }
            this.initCxData();
        },
     	resultChangeYzlx:function(val){
     		if (val[2].length > 1) {
                if (Array.isArray(this[val[2][0]])) {
                    Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    yzlb.initCxData();
                    yzzxd.jsContent.yzlx=yzlb.yzlx;
                } else {
                    Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                    if (val[3] != null) {
                        Vue.set(this[val[2][0]], val[3], val[4]);
                    }
                    yzlb.initCxData();
                    yzzxd.jsContent.yzlx=yzlb.yzlx;
                }
            } else {
                this[val[2][0]] = val[0];
                yzlb.initCxData();
                yzzxd.jsContent.yzlx=yzlb.yzlx;
            }
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
     		},
        lr: function () {
        	lr.getPsData();
            lr.open();
        },
        yzzxd: function () {
        	yzzxd.getZxjl();
            yzzxd.open();
        },
        yzd: function () {
            sessionStorage.setItem('dyyzd', JSON.stringify( this.brList[0] ));
            this.topNewPage('打印-医嘱单','page/hsz/hlyw/yzcl/loading-page/dyyzd.html');
        },
    	//重写选中
  	    checkSelectZx:function (val,event) {
            if (val[1] == 'some') {
                Vue.set(this.isChecked, val[0], !this.isChecked[val[0]]);
                if (!val[2]) this.isCheckAll = false;
            } else if (val[1] == 'one') {
                this.isCheckAll = false;
                this.isChecked = [];
                Vue.set(this.isChecked, val[0], !this.isChecked[val[0]]);
            } else if (val[1] == 'all') {
                this.isCheckAll = !this.isChecked[val[0]];
                if (val[2] == null) val[2] = "jsonList";
                if (this.isCheckAll) {
                    for (var i = 0; i < this[val[2]].length; i++) {
                        Vue.set(this.isChecked, i, true);
                    }
                } else {
                    this.isChecked = [];
                }
            }
            var that=this;
            this.ckChecked=val[0];
            event.currentTarget.onkeydown=function (e) {
                if(e.keyCode==40){
                    that.isChecked=[];
                    that.ckChecked=that.ckChecked>that[val[2]].length?0:that.ckChecked+1;
                    that.$set(that.isChecked,that.ckChecked,true)
                }else if(e.keyCode==38){
                    that.isChecked=[];
                    that.ckChecked=that.ckChecked<0?that[val[2]].length:that.ckChecked-1;
                    that.$set(that.isChecked,that.ckChecked,true)
                }
            }
        },
        //重写选中
  	  reCheckBoxZx: function () {
  		  if( arguments.length == 1 ){
  		      var isCheckAll = this.yzzxInfoList[arguments[0]].isCheckAll? false:true,
                    yzshInfo = this.yzzxInfoList[arguments[0]],
                    yzxxList = yzshInfo.yzxx;

                this.yzzxInfoList[arguments[0]].isCheckAll = isCheckAll;
                for ( var i = 0; i < yzxxList.length; i++ ){
                    this.yzzxInfoList[arguments[0]].yzxx[i].isChecked = isCheckAll;
                }
            }else if(arguments.length == 2){
              this.activeBrListIndex = arguments[0];
              this.activeIndex = arguments[1];
                // var isChecked = this.yzzxInfoList[arguments[0]].yzxx[arguments[1]].isChecked? false:true,
                //     yzshInfo = this.yzzxInfoList[arguments[0]],
                //     yzxxList = yzshInfo.yzxx,
                //     isCheckAll = true;
                //
                // this.yzzxInfoList[arguments[0]].yzxx[arguments[1]].isChecked = isChecked;
                // for ( var y = 0; y < yzxxList.length; y++ ){
                //     if( !yzxxList[y].isChecked ){
                //         this.yzzxInfoList[arguments[0]].isCheckAll = false;
                //         isCheckAll = false;
                //         break;
                //     }
                // }
                // if( isCheckAll ) this.yzzxInfoList[arguments[0]].isCheckAll = true;
            }

            this.$forceUpdate();
        },
    	   //获取审核医嘱信息
        initCxData:function(){
            this.yzzxInfoList = [];
              if (this.zyhs.length == 0) {
                  malert("请选择病人后再进行此操作！","top","defeadted");
                  return
              }
              if(this.ksid==null){
            	  malert("科室编码不能为空！","top","defeadted");
            	  return
              }
              var zyh = JSON.stringify(this.zyhs);
              $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=yzcx&ksbm=' + this.ksid + '&zyh=' + zyh+'&yzlx=' +(this.yzlx=="2"?"":this.yzlx),
                  function (json) {
                      if (json.d.list.length > 0) {
                          for (var i = 0; i < json.d.list.length; i++) {
                              for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                  json.d.list[i].yzxx[int].no = i;
                                  if(json.d.list[i].yzxx[int].zfbz=='1'){
                                	  json.d.list[i].yzxx[int].yzzt='已作废';
                                  }else if(json.d.list[i].yzxx[int].shbz=='0'){
                                	  json.d.list[i].yzxx[int].yzzt='待审核';
                                  }else if(json.d.list[i].yzxx[int].ystzbz=='1'&&json.d.list[i].yzxx[int].hstzbz=='0'){
                                	  json.d.list[i].yzxx[int].yzzt='医生申请停嘱';
                                  }else if(json.d.list[i].yzxx[int].ystzbz=='1'&&json.d.list[i].yzxx[int].hstzbz=='1'){
                                	  json.d.list[i].yzxx[int].yzzt='已审核停嘱';
                                  }else if(json.d.list[i].yzxx[int].zxsj!=null&&json.d.list[i].yzxx[int].zxsj!=''){
                                	  json.d.list[i].yzxx[int].yzzt='已执行';
                                  }else{
                                	  json.d.list[i].yzxx[int].yzzt='待执行';
                                  }
                              }
                          }
                      }
                      var list = json.d.list;
                      list.forEach(function (item,index) {
                          var yzList = item.yzxx,
                              length = yzList.length;
                          if( length > 1 ){
                              for( var i = 0; i < length; i++ ){
                                  if( yzList[i].fzh != 0 ){
                                      if( i == 0 ){ // 第一个
                                          if( yzList[i].fzh == yzList[i+1].fzh && yzList[i].yzxh == yzList[i+1].yzxh ){
                                              yzList[i]['tzbj'] = 'tz-start';
                                          }
                                      }else if( i == length - 1 ){ // 最后一个
                                          if( yzList[i].fzh == yzList[i-1].fzh && yzList[i].yzxh == yzList[i-1].yzxh ){
                                              yzList[i]['tzbj'] = 'tz-stop';
                                          }
                                      }else{
                                          if( (yzList[i].fzh != yzList[i-1].fzh || yzList[i].yzxh != yzList[i-1].yzxh) && ( yzList[i].fzh == yzList[i+1].fzh && yzList[i].yzxh == yzList[i+1].yzxh ) ){
                                              yzList[i]['tzbj'] = 'tz-start';
                                          }else if( yzList[i].fzh == yzList[i-1].fzh && yzList[i].yzxh == yzList[i-1].yzxh && yzList[i].fzh == yzList[i+1].fzh && yzList[i].yzxh == yzList[i+1].yzxh ){
                                              yzList[i]['tzbj'] = 'tz-center';
                                          }else if( (yzList[i].fzh == yzList[i-1].fzh && yzList[i].yzxh == yzList[i-1].yzxh) && ( yzList[i].fzh != yzList[i+1].fzh || yzList[i].yzxh != yzList[i+1].yzxh ) ){
                                              yzList[i]['tzbj'] = 'tz-stop';
                                          }
                                      }
                                  }
                              }
                          }
                      });
                      yzlb.yzzxInfoList = list;
                  	for (var k = 0; k < yzlb.yzzxInfoList.length; k++) {
                    	//判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                    	if(yzlb.yzzxInfoList[k].nl<7&&this.brList[k].brxb=='1'){
                    		yzlb.yzzxInfoList[k].nljd='1';
                    	}else if(yzlb.yzzxInfoList[k].nl<7&&yzlb.yzzxInfoList[k].brxb=='2'){
                    		yzlb.yzzxInfoList[k].nljd='2';
                    	}else if(yzlb.yzzxInfoList[k].nl<18&&yzlb.yzzxInfoList[k].nl>6&&yzlb.yzzxInfoList[k].brxb=='1'){
                    		yzlb.yzzxInfoList[k].nljd='3';
                    	}else if(yzlb.yzzxInfoList[k].nl<18&&yzlb.yzzxInfoList[k].nl>6&&yzlb.yzzxInfoList[k].brxb=='2'){
                    		yzlb.yzzxInfoList[k].nljd='4';
                    	}else if(yzlb.yzzxInfoList[k].nl<41&&yzlb.yzzxInfoList[k].nl>17&&yzlb.yzzxInfoList[k].brxb=='1'){
                    		yzlb.yzzxInfoList[k].nljd='5';
                    	}else if(yzlb.yzzxInfoList[k].nl<41&&yzlb.yzzxInfoList[k].nl>17&&yzlb.yzzxInfoList[k].brxb=='2'){
                    		yzlb.yzzxInfoList[k].nljd='6';
                    	}else if(yzlb.yzzxInfoList[k].nl<66&&yzlb.yzzxInfoList[k].nl>40&&yzlb.yzzxInfoList[k].brxb=='1'){
                    		yzlb.yzzxInfoList[k].nljd='7';
                    	}else if(yzlb.yzzxInfoList[k].nl<66&&yzlb.yzzxInfoList[k].nl>40&&yzlb.yzzxInfoList[k].brxb=='2'){
                    		yzlb.yzzxInfoList[k].nljd='8';
                    	}else if(yzlb.yzzxInfoList[k].nl>65&&yzlb.yzzxInfoList[k].brxb=='1'){
                    		yzlb.yzzxInfoList[k].nljd='9';
                    	}else if(yzlb.yzzxInfoList[k].nl>65&&yzlb.yzzxInfoList[k].brxb=='2'){
                    		yzlb.yzzxInfoList[k].nljd='10';
                    	}else{
                            yzlb.yzzxInfoList[k].nljd='11';
                        }
                    }
//                      YZInfo.jsonList = YZInfo.yzzxInfoList;
                  }, function (error) {
                      console.log(error);
                  });
        },
    },
});

var lr = new Vue({
    el: '#lr',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: false,
        item: 'item1',
        psYzList: [],
        index: 0,
    },
    mounted:function(){
    	  //初始化检索日期！为今天0点到今天24点
        this.beginrq = this.fDate(new Date(), 'date');
        this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24, 'date');
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#timeValPs',
            value: this.beginrq + ' - ' + this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            range: true,
            done: function (value, data) {
                if (value != '') {
                    var A_rq = value.split(' - ');
                    lr.beginrq = A_rq[0];
                    lr.endrq = A_rq[1];
                } else {
                	lr.beginrq = '';
                	lr.endrq = '';
                }
                //获取一次列表
                lr.getPsData();
            }
        });
    },
    methods: {
        //关闭
        closes: function () {
            this.isShow = false;
        },
        open: function () {
            this.isShow = true;
        },
        getPsData: function () {
            var yzlxx = "";
//            var endrq = $("#dEnd").val();
//            var beginrq = $("#dbegin").val();
            yzlxx = null;
            var zyhs=[];
            for(var i=0;i<yzlb.brList.length;i++){
            	zyhs.push(yzlb.brList[i].zyh);
            }
            if (zyhs.length <= 0) {//住院号为空则直接返回不请求后台
            	malert("住院号不能为空");
                return;
            }
            var parm = {
                beginrq: lr.beginrq,
                endrq: lr.endrq,
                yzlx: yzlxx,
                searchzyh: zyhs,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryPs&parm=' + JSON.stringify(parm),
                function (json) {
                    lr.psYzList = json.d.list;
                }, function (error) {
                    console.log(error);
                });
        },
        resultChange_save: function (val) {
            Vue.set(this.psYzList[val[2][0]], [val[2][1]], val[0]);
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
            var parm = {
                psjg: val[0],
                ypyzxh: lr.psYzList[lr.index].yzxh,
                mxxh: lr.psYzList[lr.index].yzmxxh,
                ryypmc: lr.psYzList[lr.index].xmmc,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=savePs&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == '0') {
                        lr.getPsData();
                        malert("皮试结果保存成功");
                    } else {
                        malert("皮试结果保存失败");
                    }
                });
        },
        isfzcfMxChecked: function () {

        },
        fzCheckOne: function (index) {
            lr.index = index;
        }
    }
});

var yzzxd = new Vue({
    el: '#yzzxd',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: false,
        item: 'item1',
        jsContent:{
        	yzlx:"1",
        },
        zxjlList:[],
        pcs:[],
    },
    updated:function () {
        this.$nextTick(function () {
            this.onsize()
        })
    },
    mounted:function(){
    	        //初始化检索日期！为今天0点到今天24点
    	        this.beginrq = this.fDate(new Date(), 'date');
    	        this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24, 'date');
    	        //入院登记查询列表 时间段选择器
    	        laydate.render({
    	            elem: '#timeval',
    	            value: this.beginrq + ' - ' + this.endrq,
    	            rigger: 'click',
    	            theme: '#1ab394',
    	            range: true,
    	            done: function (value, data) {
    	                if (value != '') {
    	                    var A_rq = value.split(' - ');
    	                    yzzxd.beginrq = A_rq[0];
    	                    yzzxd.endrq = A_rq[1];
    	                } else {
    	                	yzzxd.beginrq = '';
    	                	yzzxd.endrq = '';
    	                }
    	                //获取一次列表
    	                yzzxd.getZxjl();
    	            }
    	        });
    	        this.jsContent.yzlx='0';
    	        this.jsContent.zxdlx='all';
    },
    methods: {
        onsize:function () {
            var tablebodyy = $('.yzzxd-body');
            $(tablebodyy).css({
                'height':  $('body').outerHeight() - $(tablebodyy).offset().top - 60,
                'overflow-y': 'auto'
            });
        },
    	resultChangeYz:function(val){
    		 if (val[2].length > 1) {
                 if (Array.isArray(this[val[2][0]])) {
                     Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                     yzzxd.getZxjl();
                 } else {
                     Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                     if (val[3] != null) {
                         Vue.set(this[val[2][0]], val[3], val[4]);
                     }
                     yzzxd.getZxjl();
                 }
             } else {
                 this[val[2][0]] = val[0];
                 yzzxd.getZxjl();
             }
             if (val[1] != null) {
                 this.nextFocus(val[1]);
             }
    	},
    	
    	resultChangeZx:function(val){
    		 if (val[2].length > 1) {
                 if (Array.isArray(this[val[2][0]])) {
                     Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                     yzzxd.getZxjl();
                 } else {
                     Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                     if (val[3] != null) {
                         Vue.set(this[val[2][0]], val[3], val[4]);
                     }
                     yzzxd.getZxjl();
                 }
             } else {
                 this[val[2][0]] = val[0];
                 yzzxd.getZxjl();
             }
             if (val[1] != null) {
                 this.nextFocus(val[1]);
             }
    	},
        //关闭
        closes: function () {
            this.isShow = false;
        },
        open: function () {
            this.isShow = true;
        },
        savelr:function(){

        },
        getZxjl:function(){
        	yzzxd.pcs=[];
        	yzzxd.zxjlList=[];
            //取住院号 查看查询
            var zyhs=[];
            for(var i=0;i<yzlb.brList.length;i++){
            	zyhs.push(yzlb.brList[i].zyh);
            }
            if (zyhs.length <= 0) {//住院号为空则直接返回不请求后台
            	malert("住院号不能为空");
                return;
            }
            var yzlxx = "";
            var endrq = yzzxd.endrq;
            var beginrq = yzzxd.beginrq;
            // if(yzzxd.jsContent.yzlx=="2"){
            // 	yzzxd.jsContent.yzlx="";
            // }
            var parm = {
                beginrq: beginrq,
                endrq: endrq,
                yzlx: yzzxd.jsContent.yzlx,
                searchzyh: zyhs
            };
            if (yzzxd.jsContent.zxdlx == 'all') {//*******************全部
                yzzxd.zxjlList = [];
                //请求后台查询全部执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryAll&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        yzzxd.zxjlList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });

            } else if (yzzxd.jsContent.zxdlx == 'kf') {//********************口服
            	yzzxd.zxjlList  = [];
                //请求后台查询口服执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryKf&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        yzzxd.zxjlList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });

            } else if (yzzxd.jsContent.zxdlx == 'zs') {//**********************************注射
            	yzzxd.zxjlList = [];
                //请求后台查询注射的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryZs&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        yzzxd.zxjlList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });

            } else if (yzzxd.jsContent.zxdlx == 'sy') {//********************输液
            	yzzxd.zxjlList = [];
                //请求后台查询输液的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=querySy&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        yzzxd.zxjlList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });

            } else if (yzzxd.jsContent.zxdlx == 'hl') {//**********************护理
            	yzzxd.zxjlList = [];
                //请求后台查询护理的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryHl&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        yzzxd.zxjlList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });

            } else if (yzzxd.jsContent.zxdlx == 'zl') {//******************************治疗
            	yzzxd.zxjlList = [];
                //请求后台查询治疗的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryZl&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                    	json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                    	json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if(json.d.list[i].yzxx[int].tysl>0){
                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
                                    }else{
                                    	json.d.list[i].yzxx[int].tyxx="";
                                    }
                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
                                    	json.d.list[i].yzxx[int].pcmc="";
                                    }
                                }
                            }
                        }
                        yzzxd.zxjlList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });

            } else if (yzzxd.jsContent.zxdlx == 'syt') {//输液贴
            	yzzxd.zxjlList = [];
                //取频次 查看查询
            	yzzxd.pcs = [];
//                for (var i = 0; i < rightPcxx.pcList.length; i++) {
//                    if (rightPcxx.isChecked[i]) {
//                        var obj = {};
//                        this.pcs.push(rightPcxx.pcList[i].pcbm);
//                    }
//                }
//                if (this.pcs.length <= 0) {//频次为空则直接返回不请求后台
//                    return;
//                }
//                parm.searchpcbm = this.pcs;
//                //请求后台查询输液贴的执行单
//                $.getJSON('/actionDispatcher.do?reqUrl=HszHlywYzzxdCx&types=querySyt&parm=' + JSON.stringify(parm),
//                    function (json) {
//                        if (json.d.list.length > 0) {
//                            for (var i = 0; i < json.d.list.length; i++) {
//                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
//                                    json.d.list[i].yzxx[int].no = i;
//                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
//                                    	json.d.list[i].yzxx[int].sysd = " ";
//                                    }
//                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
//                                    	json.d.list[i].yzxx[int].sysddw = " ";
//                                    }
//                                    if(json.d.list[i].yzxx[int].tysl>0){
//                                    	json.d.list[i].yzxx[int].tyxx="（该记录有退药信息！）";
//                                    }else{
//                                    	json.d.list[i].yzxx[int].tyxx="";
//                                    }
//                                    if(json.d.list[i].yzxx[int].pcmc==null||json.d.list[i].yzxx[int].pcmc==undefined){
//                                    	json.d.list[i].yzxx[int].pcmc="";
//                                    }
//                                }
//                            }
//                        }
//                        bodyMenu.yzzxdList = json.d.list;
//                    }, function (error) {
//                        console.log(error);
//                    });
            }
        },
    }
});
window.onresize = function (ev) {
   setTimeout(function () {
       yzzxd.onsize()
   },50)
};