<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>门诊押金</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
	<link href="mzyj.css" rel="stylesheet" />
	<style>

		#pop{
			background-color: hsla(170, 100%, 4%, .5) !important;
			position: absolute;
			    top: 10%;
			    width: 70%;
			    height: 70%;
			    left: 15%;
				border: 1px solid;

		}
	</style>
</head>
<body class="skin-default">
<div class="wrapper background-f">

    <!--入院登记查询列表视图begin-->
    <div id="tableInfo"  v-cloak>
        <!--入院登记功能按钮begin-->
        <div class="panel">
            <div class="tong-top flex-container flex-align-c">
                <button class="tong-btn  btn-parmary" @click="ShowMz()">新增</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="getData()">刷新</button>

				<div class="flex-container flex-align-c  margin-l-20">
					<span class="whiteSpace margin-r-5 ft-14">身份证号:</span>
					<input class="zui-input" placeholder="请输入身份证号" v-model="param.sfzjhm"/>
				</div>
				<div class="flex-container flex-align-c  margin-l-20">
					<span class="whiteSpace margin-r-5 ft-14">病人姓名:</span>
					<input class="zui-input" placeholder="请输入病人姓名" v-model="param.brxm"/>
				</div>
            </div>

        </div>
        <!--循环列表begin-->
        <div class="zui-table-view padd-r-10 padd-l-10" id="brRyList">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-l"><span>身份证号</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>支付类型</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>金额</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>收费人</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>收费时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table" v-if="jsonList.length!=0">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"   :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">

                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.sfzjhm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zflxmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yjje"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.czyxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.yjrq,'date')"></div>
                        </td>

						<td ><!--操作-->
						    <div class="zui-table-cell cell-s flex-container flex-align-c flex-jus-c">
								<i class="icon-icon icon-th margin-r-10"  title="回退" @click="huitui($index)"></i>
								<i class=" wb-print"   title="打印" @click="printFun($index)"></i>
						    </div>
						</td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
            </div>

            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
        <!--循环列表end-->
    </div>

    <div id="yjxxPop" class="pophide" :class="{'show':isShow}">
        <div class="pop-width520 bcsz-layer"
             style="height: 40%;width:40%;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative;overflow: auto">
            <div class="layui-layer-title " style="padding: 0 0 0 20px;">
                <span class="dzcf-fl" v-text="popTitle"></span>
                <span class="dzcf-fr closex ti-close" style="float: right;margin-right: 10px;" @click="cancel"></span>
            </div>

            <div class="layui-layer-content">
                <div class=" layui-mad layui-height">
                    <div class="" @scroll="scrollTable($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        病人姓名:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <input class="zui-input" autocomplete="off" placeholder=""  v-model="popContent.brxm"  @keydown.enter="nextFocus($event)"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        病人身份证:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <input class="zui-input" autocomplete="off" placeholder=""  v-model="popContent.sfzjhm"  @keydown.enter="nextFocus($event)"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        支付类型:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <select-input class="cell-xl" @change-data="resultChange" :data-notEmpty="true"
                                                      :child="zflxList" :index="'zflxmc'" :index_val="'zflxbm'"
                                                      :val="popContent.zflxbm" :search="true"
                                                      :name="'popContent.zflxbm'" :index_mc="'zflxmc'">
                                        </select-input>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        金额:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <input class="zui-input" autocomplete="off" placeholder=""  type="number" v-model="popContent.yjje"  @keydown.enter="nextFocus($event)"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        扫码:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <input class="zui-input wh182 margin-r-10" v-model="popContent.codeContent" placeholder="扫码支付..." id="codeContent">
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="zui-row buttonbox">
                <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">取消</button>
                <button  v-waves class="zui-btn btn-primary xmzb-db margin-r-15 height36" @click="saveYjxx">确定</button>
            </div>
        </div>
    </div>


    <div id="tyjxxPop" class="pophide" :class="{'show':isShow}">
        <div class="pop-width520 bcsz-layer"
             style="height: 40%;width:40%;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative;overflow: auto">
            <div class="layui-layer-title " style="padding: 0 0 0 20px;">
                <span class="dzcf-fl" v-text="popTitle"></span>
                <span class="dzcf-fr closex ti-close" style="float: right;margin-right: 10px;" @click="cancel"></span>
            </div>

            <div class="layui-layer-content">
                <div class=" layui-mad layui-height">
                    <div class="" @scroll="scrollTable($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        病人姓名:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <input class="zui-input" autocomplete="off" placeholder=""  v-model="popContent.brxm"  @keydown.enter="nextFocus($event)"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        病人身份证:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <input class="zui-input" autocomplete="off" placeholder=""  v-model="popContent.sfzjhm"  @keydown.enter="nextFocus($event)"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        支付类型:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <select-input class="cell-xl" @change-data="resultChange" :data-notEmpty="true"
                                                      :child="zflxList" :index="'zflxmc'" :index_val="'zflxbm'"
                                                      :val="popContent.zflxbm" :search="true"
                                                      :name="'popContent.zflxbm'" :index_mc="'zflxmc'">
                                        </select-input>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        可退金额:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <input class="zui-input" autocomplete="off" placeholder=""  type="number" v-model="popContent.yyjje"  @keydown.enter="nextFocus($event)"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        待退金额:
                                    </div>
                                </td>
                                <td>
                                    <div class=" zui-table-cell  cell-xl">
                                        <input class="zui-input" autocomplete="off" placeholder=""  type="number" v-model="popContent.yjje"  @keydown.enter="nextFocus($event)"/>
                                    </div>
                                </td>
                            </tr>



                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="zui-row buttonbox">
                <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">取消</button>
                <button  v-waves class="zui-btn btn-primary xmzb-db margin-r-15 height36" @click="saveYjxx">确定</button>
            </div>
        </div>
    </div>



</div>




</body>
<script src="mzyj.js" type="text/javascript"></script>
</html>
