var topMenu = new Vue({
    el: '#topMenu',
    data: {
        num: 0,
        page:['jydj','jyglxx'],
    },
    created: function () {
        this.$nextTick(function () {
            this.tab(0)
        })
    },
    methods: {
        tab:function(index){
            this.num=index
            tabBg(this.page[index])
        },
    },
});
function tabBg(page) {
    $(".loadPage").load(page + ".html",'',function () {
    }).fadeIn(300);
}
