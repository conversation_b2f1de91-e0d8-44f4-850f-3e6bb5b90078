function CreatXmlDoc(obj){
    this.tagName=obj.tagName;
    var children=obj.children.map(function(item){
        if(typeof item =="object")
        {
           item=new CreatXmlDoc(item)
        }
        return item
    })
    this.children=children;
}


CreatXmlDoc.prototype.render=function(){
    var el=document.createElement(this.tagName);
    var children=this.children || [];
    children.forEach(function(child){
        var childEl=(child instanceof CreatXmlDoc)
        ? child.render()
        :document.createTextNode(child)
    el.appendChild(childEl);
    })
    return el
}

var abyhyb_007=new Vue({
    el:'#abyhyb_007',
    mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
    components: {
        'search-table': searchTable
    },
    data:{
        ip:'',// 当前客户端ip
        userInfo:{},// 当前用户信息
        bxlbbm:'', // 保险类别编码
        grxxJson:{}, // 个人信息对象
        abzyb_zflb_tran: {// 阿坝州医保支付类别
            '11': '普通门诊',
            '12': '药店购药',
            '13': '二乙门诊',
            '21': '门诊特殊病'
        },
        jbContent:{}, // 疾病类容
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'aka120',
            '疾病名称': 'aka121',
            '拼音代码': 'aka020'
        },
        zdxxJson:{
			"aka130": "11",
		},
    },
    mounted: function () {
    	// 获取当前客户端ip
    	this.getUserIP(function(ip){
            this.ip = 'http://' + ip + ':14444';
            console.log(this.ip);
            abyhyb_007.init();
        });
	},
    methods:{
    	init: function() {
    		// 调用银海医保初始化方法
            $.post(ip + "/abzCom/init", {}, function (json) {
    			if(json.code == 0){
    				if (json.data.aint_appcode >= 0) {
    					rightVue.abyhybInit = true;
    					malert("医保控件初始化成功!");
    				} else {
    					malert("医保控件初始化失败！" + json.data.astr_appmasg,'top','defeadted');
    				}
    			}else{
    				malert("医保系统错误！" + json.msg,'top','defeadted');
    			}

    		});
    	},
		// 获取用户信息
		getUserInfo: function () {
			this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
				.then(function (json) {
					this.userInfo = json.body.d;
				});
		},
		// 获取保险类别信息
		getbxlb: function () {
			var param = {bxjk: "007"};
			$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
				+ JSON.stringify(param), function (json) {
				if (json.a == 0) {
					if (json.d.list.length > 0) {
						abyhyb_007.bxlbbm = json.d.list[0].bxlbbm;
					}
				} else {
					malert("保险类别查询失败!" + json.c,'top','defeadted')
				}
				});
		},

		// 读卡
	     load:function(){
	     	if(rightVue.abyhybInit){
				var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai></input>';
					$.post(ip + "/abzCom/call", {
						jybh: "03",
						jysr_xml: jysr
					}, function (json) {
						if(json.code == 0){
							if (json.data.aint_appcode > 0) {
								var jyscModel = JSON.parse(json.data.astr_jysc_xml);
								abyhyb_007.grxxJson = jyscModel;
								malert("读卡成功!");
							} else {
								malert("读卡失败！" + json.data.astr_appmasg,'top','defeadted');
							}
						}else{
							malert("医保系统错误！" + json.msg,'top','defeadted');
						}
					});
	     	}else{
	     		malert("医保控件未初始化,请重新打开页面！",'top','defeadted');
	     	}
	     },

	   //引入
	     enter:function(){
			 if (Object.keys(abyhyb_007.grxxJson).length === 0) {
				 malert("请先读卡",'top','defeadted');
				 return;
			 }
			 if (abyhyb_007.zdxxJson.aka130 == null || abyhyb_007.zdxxJson.aka130 === '' || abyhyb_007.zdxxJson.aka130 === undefined) {
				 malert("请选择支付类型",'top','defeadted');
				 return;
			 }
			 //个人信息
			 rightVue.abyhybContent = abyhyb_007.grxxJson;
			 //门诊诊断信息
			 rightVue.abyhybContent.jbbm = this.zdxxJson.jbbm;
			 //支付类别
			 rightVue.abyhybContent.aka130 = this.zdxxJson.aka130;
			 //备注
			 rightVue.abyhybContent.bzsm = this.zdxxJson.bzsm;
			 //个人编号,用于结算各种判断
			 rightVue.abyhybContent.grbh = abyhyb_007.grxxJson.aac001;
			 malert("引入成功！");
			 popTable.isShow = false;
	     },

	   //门诊预结算
		 mzyjs:function(){
			var result = "0";
			//同步操作
			$.ajaxSettings.async = false;
			 //处理费用
			 var yhybBrfyList = [];
			 var fylist=[];
			 var brfyList=[];
			 for (var i=0;i<rightVue.brfyjsonList.length;i++) {
				 var fyparam = {};
				 fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
				 fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
				 if(fyparam.yzlx == null ){
					 fyparam.yzlx = rightVue.brfyjsonList[i].yzfl;
				 }
				 fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
				 fyparam.fysl = rightVue.brfyjsonList[i].fysl;
				 fyparam.mzys = rightVue.brfyjsonList[i].mzys;
				 fyparam.mzysxm = rightVue.brfyjsonList[i].mzysxm;
				 fyparam.mzks = rightVue.brfyjsonList[i].mzks;
				 fyparam.mzksmc = rightVue.brfyjsonList[i].mzksmc;
				 brfyList.push(fyparam);
			 }
			 var param = {
				 fylist: brfyList,
				 userId: userId,
				 bxlbbm: abyhyb_007.bxlbbm
			 };

			 $.post(ip + "/mzyw/queryMzfy", {
					pd: JSON.stringify(param)
				}, function (json) {
					if(json.code == 0){
						fylist = json.data;
					}else{
						malert("查询费用信息失败！" + json.msg,'top','defeadted');
						return false;
					}
				});
			if(fylist==null || fylist==undefined || fylist=="" || fylist.length<=0){
				malert("没有可结算费用！");
				return false;
			}
	    	 var fyze = 0.00;
			 var rowlist=[];
	 		for(var i=0;i<fylist.length;i++){
	 			var taglist=[];
	 			var obj1={
	 					tagName:'yke112',
	 					children:[fylist[i].yke112]
	 			};
	 			taglist.push(obj1);
	 			var obj2={
	 					tagName:'aae072',
	     				children:[fylist[i].aae072]
	 			};
	 			taglist.push(obj2);
	 			var obj3={
	 					tagName:'aka017',
	     				children:[fylist[i].aka017]
	 			};
	 			taglist.push(obj3);
	 			var obj4={
	 					tagName:'ykc120',
	     				children:[fylist[i].ykc120]
	 			};
	 			taglist.push(obj4);
	 			var obj5={
	 					tagName:'ake005',
	     				children:[fylist[i].ake005]
	 			};
	 			taglist.push(obj5);
	 			var obj6={
	 					tagName:'ake006',
	     				children:[fylist[i].ake006]
	 			};
	 			taglist.push(obj6);
	 			var obj7={
	 					tagName:'ake001',
	     				children:[fylist[i].ake001]
	 			};
	 			taglist.push(obj7);
	 			var obj8={
	 					tagName:'ake002',
	     				children:[fylist[i].ake002]
	 			};
	 			taglist.push(obj8);
	 			var obj9={
	 					tagName:'aka067',
	     				children:[fylist[i].aka067]
	 			};
	 			taglist.push(obj9);
	 			var obj10={
	 					tagName:'aka070',
	     				children:[fylist[i].aka070]
	 			};
	 			taglist.push(obj10);
	 			var obj11={
	 					tagName:'aka074',
	     				children:[fylist[i].aka074]
	 			};
	 			taglist.push(obj11);
	 			var obj12={
	 					tagName:'akc225',
	     				children:[fylist[i].akc225]
	 			};
	 			taglist.push(obj12);
	 			var obj13={
	 					tagName:'akc226',
	     				children:[fylist[i].akc226]
	 			};
	 			taglist.push(obj13);
	 			var obj14={
	 					tagName:'aae019',
	     				children:[fylist[i].aae019]
	 			};
	 			taglist.push(obj14);
	 			var obj15={
	 					tagName:'yka091',
	     				children:[fylist[i].yka091]
	 			};
	 			taglist.push(obj15);
	 			var obj16={
	 					tagName:'yka092',
	     				children:[fylist[i].yka092]
	 			};
	 			taglist.push(obj16);
	 			var obj17={
	 					tagName:'yka093',
	     				children:[fylist[i].yka093]
	 			};
	 			taglist.push(obj17);
	 			var obj18={
	 					tagName:'yka094',
	     				children:[fylist[i].yka094]
	 			};
	 			taglist.push(obj18);
	 			var obj19={
	 					tagName:'yka100',
	     				children:[fylist[i].yka100]
	 			};
	 			taglist.push(obj19);
	 			var obj20={
	 					tagName:'yka101',
	 					children:[fylist[i].yka101]
	 			};
	 			taglist.push(obj20);
	 			var obj21={
	 					tagName:'yka102',
	     				children:[fylist[i].yka102]
	 			};
	 			taglist.push(obj21);
	 			var obj22={
	 					tagName:'yka103',
	     				children:[fylist[i].yka103]
	 			};
	 			taglist.push(obj22);
	 			var obj23={
	 					tagName:'ake007',
	     				children:[fylist[i].ake007]
	 			};
	 			taglist.push(obj23);
	 			var obj24={
	 					tagName:'yke330',
	     				children:[fylist[i].yke330]
	 			};
	 			taglist.push(obj24);
	 			var obj25={
	 					tagName:'aae414',
	     				children:[fylist[i].aae414]
	 			};
	 			taglist.push(obj25);
	 			var obj26={
	 					tagName:'yke098',
	     				children:[fylist[i].yke098]
	 			};
	 			taglist.push(obj26);
	 			var row={
	 					tagName:'row',
	 					children:taglist
	 			}
	 			rowlist.push(row);
	 			fyze+=fylist[i].aae019;
	 		}
			 fyze = fyze.toFixed(2);
	 		 var obj={
	 			    tagName:'dataset',
	 			    children:rowlist
	 			  };
	 		 // 去除参数中的null
	 		var o = JSON.stringify(obj);
			var reg=new RegExp("null","g");
			obj = JSON.parse(o.replace(reg,''));

	 		doc=new CreatXmlDoc(obj);
	 		SetupSerial=(new XMLSerializer()).serializeToString(doc.render());
	 		 var reg = new RegExp(' xmlns="http://www.w3.org/1999/xhtml"',"g");
	 		 SetupSerial=SetupSerial.replace(reg,"");
	 		 var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
	 		 var aka130 = "<aka130>"+rightVue.abyhybContent.aka130+"</aka130>";
	 		 var aac001 = "<aac001>"+rightVue.abyhybContent.grbh+"</aac001>";
	 		 var akc193 = "<akc193>"+rightVue.abyhybContent.jbbm+"</akc193>";
	 		 var yka026 = "<yka026></yka026>";
	 		 var aae072 = "<aae072>"+(new Date()).getTime()+"</aae072>";
	 		 var aae013 = "<aae013>"+rightVue.abyhybContent.bzsm+"</aae013>";
	 		 var aae011 = "<aae011>"+userId+"</aae011>";

	         jysr = jysr+aka130+aac001+akc193+yka026+aae072+aae013+aae011+SetupSerial+"</input>";
	         //去掉所有不合法字符
			 jysr = jysr.replace(/undefined/g,"");
			 jysr = jysr.replace(/NaN/g,"");
			 jysr = jysr.replace(/null/g,"");

			 // 调用结算方法并进行门诊登记
			 abyhyb_007.grxxJson.ghxh = rightVue.brxxContent.ghxh,//取挂号序号
			 abyhyb_007.grxxJson.aka130 = this.zdxxJson.aka130; // 支付类别
			 abyhyb_007.grxxJson.jbbm = this.zdxxJson.jbbm; // 疾病编码
			 abyhyb_007.grxxJson.bzsm = this.zdxxJson.bzsm; // 备注说明
			 $.post( ip + "/mzyw/mzyjs",{
				 'jybh':'48',
				 'jysr_xml':jysr,
				 'grxx': JSON.stringify(abyhyb_007.grxxJson)
			 },function (json) {
				 if(json.code == 0){
					 rightVue.yjsContentAbyhyb = JSON.parse(json.data.astr_jysc_xml);
					 // 计算医保支付金额
					 var yka132 = 0;
					 var yka130 = 0;
                     var yka730 = 0;
                     var yka330 = 0;
                     var yka530 = 0;
                     if(typeof rightVue.yjsContentAbyhyb.yka132 == 'string'){
                    	 yka132 = rightVue.yjsContentAbyhyb.yka132;
                     }
                     if(typeof rightVue.yjsContentAbyhyb.yka130 == 'string'){
                    	 yka130 = rightVue.yjsContentAbyhyb.yka130;
                     }
                     if(typeof rightVue.yjsContentAbyhyb.yka730 == 'string'){
                    	 yka730 = rightVue.yjsContentAbyhyb.yka730;
                     }
                     if(typeof rightVue.yjsContentAbyhyb.yka330 == 'string'){
                    	 yka330 = rightVue.yjsContentAbyhyb.yka330;
                     }
                     if(typeof rightVue.yjsContentAbyhyb.yka530 == 'string'){
                    	 yka530 = rightVue.yjsContentAbyhyb.yka530;
                     }
                     rightVue.yjsContentAbyhyb.bxje = rightVue.fDec(
                         parseFloat(yka132) //个人账户支付
                         + parseFloat(yka130) //统筹支付
                         + parseFloat(yka730) //大病补保支付
                         + parseFloat(yka330) //大病救助（民政）
                         + parseFloat(yka530) ,2); //伤残军人补助

					 rightVue.ab_jylsh = json.data.astr_jylsh;
					 rightVue.ab_jyyzm = json.data.astr_jyyzm;
				 }else{
					 malert(json.msg,'top','defeadted');
					 result = "1";
					}
			 });
			 return result;
	     },

	   //门诊结算
		 mzjs:function(){
			 var param = {
				'jylsh':rightVue.ab_jylsh,
                'jyyzm':rightVue.ab_jyyzm,
                'ghxh': rightVue.brxxContent.ghxh
			 };
			 $.post( ip + "/mzyw/mzjs",{
				 'pd':JSON.stringify(param)
			 },function (json) {
				 if(json.code == 0){
					 rightVue.zxlshSn = rightVue.ab_jylsh;
					 malert(json.msg);
				 }else{
					 malert(json.msg,'top','defeadted');
				 }
			 });
		 },
		 // 取消结算
		 qxjs: function(){
			 var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			 var akc190='<akc190>' + rightVue.yjsContentAbyhyb.akc190 + '</akc190>';// 就诊编号
			 var aaz216='<aaz216>' + rightVue.yjsContentAbyhyb.aaz216 + '</aaz216>';// 结算编号
			 var aac001='<aac001>'+ abyhyb_007.grxxJson.aac001 +'</aac001>';// 个人编号
			 var aka130='<aka130>' + abyhyb_007.grxxJson.aka130 + '</aka130>';// 支付类别
			 var aae011='<aae011>' + this.userInfo.czybm + '</aae011>';// 经办人
			 jysr += akc190 + aaz216 + aac001 + aka130 + aae011+"</input>";

			 $.post( ip + "/mzyw/qxjs",{
				 'pd': jysr,
				 'jylsh':rightVue.ab_jylsh,
	             'jyyzm':rightVue.ab_jyyzm,
	             'userId': this.userInfo.czybm
			 },function (json) {
				 if(json.code == 0){
					 malert(json.msg);
				 }else{
					 malert(json.msg,'top','defeadted');
				 }
			 });
		 },

		 qxjsTest: function(){
			 var jysr='<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			 var akc190='<akc190>' + rightVue.yjsContentAbyhyb.akc190 + '</akc190>';// 就诊编号
			 var aaz216='<aaz216>' + rightVue.yjsContentAbyhyb.aaz216 + '</aaz216>';// 结算编号
			 var aac001='<aac001>'+ abyhyb_007.grxxJson.aac001 +'</aac001>';// 个人编号
			 var aka130='<aka130>' + abyhyb_007.grxxJson.aka130 + '</aka130>';// 支付类别
			 var aae011='<aae011>' + this.userInfo.czybm + '</aae011>';// 经办人
			 jysr += akc190 + aaz216 + aac001 + aka130 + aae011+"</input>";

			 $.post( ip + "/mzyw/qxjs",{
				 'pd': jysr,
				 'jylsh':rightVue.ab_jylsh,
	             'jyyzm':rightVue.ab_jyyzm,
	             'userId': this.userInfo.czybm
			 },function (json) {
				 if(json.code == 0){
					 malert(json.msg);
				 }else{
					 malert(json.msg,'top','defeadted');
				 }
			 });
		 },

		// 获取当前浏览器客户端ip
		getUserIP:function(onNewIP) {
            var MyPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;
            var pc = new MyPeerConnection({
                iceServers: []
              });
            noop = function() {},
            localIPs = {};
            ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g;
            function iterateIP(ip) {
                if (!localIPs[ip]) onNewIP(ip);
                localIPs[ip] = true;
            }
            pc.createDataChannel('');
            pc.createOffer().then((sdp) => {
              sdp.sdp.split('\n').forEach(function (line) {
                if (line.indexOf('candidate') < 0) return;
                line.match(ipRegex).forEach(iterateIP);
              });
              pc.setLocalDescription(sdp, noop, noop);
            }).catch((reason) => {
            });
            pc.onicecandidate = (ice) => {
              if (!ice || !ice.candidate || !ice.candidate.candidate || !ice.candidate.candidate.match(ipRegex)) return;
              ice.candidate.candidate.match(ipRegex).forEach(iterateIP);
            };
          },

          // 检索疾病编码
          searching: function (add, type,val) {
        	  console.log(val)
         	 this.jbContent['jbmc']=val;
              if (!add) this.page.page = 1;
              var _searchEvent = $(event.target.nextElementSibling).eq(0);
              if (this.jbContent[type] == undefined || this.jbContent[type] == null) {
                  this.page.parm = "";
              } else {
                  this.page.parm = this.jbContent[type];
              }
              var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
              $.ajax({
  				type: "post",
  				url: ip + "/basicData/queryIcd",
  				data:{pd:JSON.stringify(str_param)},
  				dataType: "json",
  				success:function(data){
  					if (data.code == 0) {
                        var res = data.data.list;
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < res.length; i++) {
                          	  abyhyb_007.searchCon.push(res[i]);
                            }
                        } else {
                      	  abyhyb_007.searchCon = res;
                        }
                        abyhyb_007.page.total = data.data.total;
                        abyhyb_007.selSearch = 0;
                        if (res.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert(data.msg,'top','defeadted');
                    }
  				}
  			});
          },
          selectOne: function (item) {
              if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                  this.page.page++;               // 设置当前页号
                  this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
              } else {   // 否则就是选中事件,为json赋值
                  this.jbContent = item;
                  Vue.set(this.jbContent, 'jbmc', this.jbContent['aka121']);
                  abyhyb_007.zdxxJson.jbbm = this.jbContent.aka120;
                  abyhyb_007.zdxxJson.jbmc = this.jbContent.aka121;
                  $(".selectGroup").hide();
              }
          },
          changeDown: function (event, type) {
              if (this['searchCon'][this.selSearch] == undefined) return;
              this.keyCodeFunction(event, 'jbContent', 'searchCon');
              if (event.code == 'Enter' || event.code == 13) {
                  if (type == "text") {
                      Vue.set(this.jbContent, 'jbmc', this.jbContent['aka121']);
                      abyhyb_007.zdxxJson.jbbm = this.jbContent.aka120;
                      abyhyb_007.zdxxJson.jbmc = this.jbContent.aka121;
                      this.selSearch=0;
                      this.nextFocus(event);
                  }
              }
          },
    },
});
abyhyb_007.getbxlb();
abyhyb_007.getUserInfo();
