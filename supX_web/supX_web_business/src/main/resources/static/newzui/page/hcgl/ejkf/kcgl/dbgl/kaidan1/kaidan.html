<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>调拨管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper" :ks="getKS" :money="money">
    <div class="panel">
        <div class="tong-search tong-padded">
            <div class="jbxx">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20">
						<div class="zui-inline padd-l-40 margin-f-l10">
						    <label class="zui-form-label">当前科室</label>
						    <div class="zui-input-inline wh122 margin-l-25" style="margin-left: 50px">
						        <select-input @change-data="resultChange"
						                      :not_empty="false" :child="yfList"
						                      :index="'yfmc'" :index_val="'yfbm'"
						                      :val="popContent.dbyf" :search="true" :name="'popContent.dbyf'"
						                      :index_mc="'yfbm'" :disable="true">
						        </select-input>
						    </div>
						</div>
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">申领科室</label>
                            <div class="zui-input-inline wh122 margin-l-25" style="margin-left: 50px">
                                <select-input @change-data="resultChanges"
                                              :not_empty="false" :child="yfList"
                                              :index="'yfmc'" :index_val="'yfbm'"
                                              :val="popContent.yfbm" :search="true" :name="'popContent.yfbm'"
                                              :index_mc="'yfbm'" :disable="true">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline padd-l-40 margin-f-l10">
                            <label class="zui-form-label">备注描述</label>
                            <div class="zui-input-inline wh150 margin-l-25">
                                <input class="zui-input" placeholder="请输入备注" type="text" id="bzms" v-model="popContent.bzms" :disabled="true"/>
                            </div>
                        </div>
                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期:<i class="color-wtg" v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10"  z-height="full" >
        <div class="zui-table-header ">
            <table class="zui-table ">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>供应商</span></div></th>
                    <th><div class="zui-table-cell cell-l "><span>材料编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>二级库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)" >
            <table class="zui-table ">
                <tbody>
                <tr v-for="(item,$index) in jsonList"   @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2 text-left">{{item.ypmc}}<span class="color-wtg">{{item.ypspm}}</span></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl  " v-text="item.ghdwmc">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l  " v-text="item.ypbm">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypgg">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.cksl}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cdmc">状态</div>
                    </td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>

        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="zui-table-tool rkgl-position">
             <span class="rkgl-fl">
               <i>材料进价总价: <em class="color-wtg">{{fDec(json.jjzj,2)}}元</em></i>
               <i>材料零价总价: <em class="color-wtg">{{fDec(json.ljzj,2)}}元</em></i>
           </span>

            <span class="rkgl-fr flex-container">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="printDJ" v-if="ckd.shzfbz=='1'">打印</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()"  v-if="ckd.qrbz=='0'">作废</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="passData"  v-if="ckd.shzfbz=='0'">审核</button>
           </span>
        </div>
    </div>
</div>
</div>
<script src="kaidan.js"></script>
</body>
</html>
