var yndryb_010 = new Vue({
    el: '#yndryb_010',
    mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
    components: {
        'search-table': searchTable
    },
    data: {
        ip:'',
        userInfo: {},// 当前用户信息
        signInfo: {},//操作员签到信息
        bxlbbm: '', // 保险类别编码
        bxurl: '',
        grxxJson: {}, // 个人信息对象
        ybdjxx:{},  //医保登记信息
        yndryb_yllb_tran: {// 门诊医疗类别
            '11': '普通门诊',
            '12': '特殊病门诊',
            '13': '慢性病门诊',
            '14': '急诊抢救',
            '19': '门诊透析',
            '41': '血腹透门诊'
        },
        yndryb_jslb_tran: {// 结算类别
            '1': '正常结算',
            '2': '中途结算',
            '3': '卡挂失结算',
            '4': '自费结算'
        },
        yndryb_fs_tran: {
            '0': '无',
            '1': '存在医疗封锁'
        },
        yndryb_rylb_tran:{
            '1':'城镇职工',
            '2':'城镇居民',
            '3':'离休'
        },
        yndryb_jydlb_tran:{
            '1':'本地',
            '2':'异地'
        },
        yndryb_sex_tran:{
            '1':'男',
            '2':'女'
        },
        yndryb_ylrylb_tran:{//医疗人员类别
            '00':'无类别',
            '01':'在职(普通)',
            '02':'退休(普通)',
            '03':'离职人员',
            '05':'革命工作者',
            '06':'离休人员(老红军)',
            '07':'二乙残人员',
            '08':'建国初期参加革命工作者',
            '09':'离休遗孀',
            '11':'内退人员',
            '15':'退休审核期'
        },
        jbContent: {}, // 疾病类容
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'aka120',
            '疾病名称': 'aka121',
            '拼音代码': 'aka066'
        },
        zdxxJson: {
            "jslx": "1",
        },
    },
    mounted: function () {
        // this.ip = 'http://localhost:9001';
        // this.init();

        this.getUserIP(function(ip){
            this.ip = 'http://' + ip + ':9001';
            yndryb_010.init();
            yndryb_010.getSignInfo();
            yndryb_010.getRightVueBrxx();
        });
    },

    methods: {
        init: function () {
            //调用云南东软医保初始化
            $.post(ip + "/yndrybYdjy/initYdjy", {}, function (json) {
                if(json.code == 0){
                    rightVue.yndrybInit = true;
                    malert("医保控件初始化成功!");
                }else{
                    rightVue.yndrybInit = true;
                    malert("医保控件初始化失败！" + json.msg,'top','defeadted');
                }
            });
        },

        getRightVueBrxx:function () {
            yndryb_010.grxxJson.aka130 = "11";
            Vue.set(this.grxxJson,"ykc010",rightVue.mzjbxxContent.ghxh);
            Vue.set(this.grxxJson,"akc193",rightVue.mzjbxxContent.jbbm);
            Vue.set(this.grxxJson,"ykc600",rightVue.mzjbxxContent.jbmc);
            this.$forceUpdate();
        },

        getSignInfo: function(){
            //查询操作员签到信息
            $.post(ip + "/yndrybYdjy/querySignInfo", {parm:userId}, function (json) {
                if(json.code == 0){
                    if (json.data == null){
                        malert("操作员未签到,请先签到！",'top','defeadted');
                    } else {
                        yndryb_010.signInfo = json.data;
                    }
                }else{
                    malert("查询签到信息失败：" + json.msg,'top','defeadted');
                }
            });
        },

        // 获取当前浏览器客户端ip
        getUserIP:function(onNewIP) {
            var MyPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;
            var pc = new MyPeerConnection({
                iceServers: []
            });
            noop = function() {},
                localIPs = {};
            ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g;
            function iterateIP(ip) {
                if (!localIPs[ip]) onNewIP(ip);
                localIPs[ip] = true;
            }
            pc.createDataChannel('');
            pc.createOffer().then((sdp) => {
                sdp.sdp.split('\n').forEach(function (line) {
                    if (line.indexOf('candidate') < 0) return;
                    line.match(ipRegex).forEach(iterateIP);
                });
                pc.setLocalDescription(sdp, noop, noop);
            }).catch((reason) => {
            });
            pc.onicecandidate = (ice) => {
                if (!ice || !ice.candidate || !ice.candidate.candidate || !ice.candidate.candidate.match(ipRegex)) return;
                ice.candidate.candidate.match(ipRegex).forEach(iterateIP);
            };
        },

        // 获取操作员用户信息
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    this.userInfo = json.body.d;
                });
        },

        // 获取保险类别信息
        getbxlb: function () {
            var param = {bxjk: "010"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        yndryb_010.bxlbbm = json.d.list[0].bxlbbm;
                        yndryb_010.bxurl = json.d.list[0].url;
                    }
                } else {
                    malert("保险类别查询失败!" + json.c, 'top', 'defeadted')
                }
            });
        },

        // 读卡
        load: function () {
            if (rightVue.yndrybInit) {
                $.post(ip + "/yndrybYdjy/readCard", null, function (json) {
                    if(json.code == 0){
                        yndryb_010.grxxJson = Object.assign(json.data,yndryb_010.grxxJson);
                        malert("读卡成功!");
                        yndryb_010.queryYbdj();
                    }else{
                        malert("读卡失败！" + json.msg,'top','defeadted');
                    }
                });
            } else {
                malert("医保控件未初始化,请重新打开页面！", 'top', 'defeadted');
            }
        },

        //查询医保登记信息
        queryYbdj: function(){
            var parm = {
                AAC001: yndryb_010.grxxJson.AAC001,
                YKC010: rightVue.brxxContent.ghxh
            };
            $.post(ip + "/yndrybYdjy/queryYbdj", {parm: JSON.stringify(parm)}, function (json) {
                if(json.code == 0){
                    yndryb_010.ybdjxx = json.data;
                }else{
                    malert("查询登记信息失败！" + json.msg,'top','defeadted');
                }
            });
        },

        //引入
        enter: function () {
            if (Object.keys(yndryb_010.grxxJson).length === 0) {
                malert("请先读卡", 'top', 'defeadted');
                return;
            }
            if (yndryb_010.zdxxJson.jslx == null || yndryb_010.zdxxJson.jslx === '' || yndryb_010.zdxxJson.jslx === undefined) {
                malert("请选择结算类型", 'top', 'defeadted');
                return;
            }
            if (Object.keys(yndryb_010.signInfo).length === 0) {
                malert("操作员未签到，请先签到!", 'top', 'defeadted');
                return;
            }
            if (yndryb_010.ybdjxx == null) {
                var param = {
                    'sign' : JSON.stringify(yndryb_010.signInfo),
                    'parm' : JSON.stringify(yndryb_010.grxxJson)
                };
                //此处进行医保登记
                $.post(ip + "/yndrybYdjy/ydybdj", param, function (json) {
                    if(json.code == 0){
                        yndryb_010.ybdjxx = json.data;

                        //个人信息
                        rightVue.yndrybContent = yndryb_010.grxxJson;
                        //门诊诊断信息
                        rightVue.yndrybContent.jbbm = yndryb_010.zdxxJson.jbbm;
                        //支付类别
                        // rightVue.yndrybContent.aka130 = this.zdxxJson.aka130;

                        //个人编号,用于结算各种判断
                        rightVue.yndrybContent.grbh = yndryb_010.grxxJson.aac001;
                        malert("引入成功！");
                        popTable.isShow = false;
                    }else{
                        malert("异地医保登记失败！" + json.msg,'top','defeadted');
                    }
                });
            }else {
                //个人信息
                rightVue.yndrybContent = yndryb_010.grxxJson;
                //门诊诊断信息
                rightVue.yndrybContent.jbbm = yndryb_010.zdxxJson.jbbm;
                //支付类别
                // rightVue.yndrybContent.aka130 = this.zdxxJson.aka130;

                //个人编号,用于结算各种判断
                rightVue.yndrybContent.grbh = yndryb_010.grxxJson.aac001;
                malert("引入成功！");
                popTable.isShow = false;
            }

        },

        //门诊预结算
        mzyjs: function () {
            var result = "0";
            //同步操作
            $.ajaxSettings.async = false;
            //处理费用
            var fylist = [];
            var brfyList = [];
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                var fyparam = {};
                fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                if (fyparam.yzlx == null) {
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzfl;
                }
                fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                fyparam.fysl = rightVue.brfyjsonList[i].fysl;
                fyparam.mzys = rightVue.brfyjsonList[i].mzys;
                fyparam.mzysxm = rightVue.brfyjsonList[i].mzysxm;
                fyparam.mzks = rightVue.brfyjsonList[i].mzks;
                fyparam.mzksmc = rightVue.brfyjsonList[i].mzksmc;
                fyparam.yzxh = rightVue.brfyjsonList[i].yzxh;
                fyparam.sqsj = rightVue.brfyjsonList[i].sqsj;
                brfyList.push(fyparam);
            }
            var ybParm = {
                ybgrbh: yndryb_010.ybdjxx.aac001,
                ybkh: yndryb_010.ybdjxx.akc020,
                cbtcqh: yndryb_010.ybdjxx.yab003,
                czybm: userId,
                ywzqh: yndryb_010.signInfo.ywzqh
            };
            var param = {
                fylist: brfyList,
                userId: userId,
                bxlbbm: yndryb_010.bxlbbm,
                ybgrbh: yndryb_010.ybdjxx.aac001,
                djlsh:  yndryb_010.ybdjxx.ybdjid,
                akc190: yndryb_010.ybdjxx.akc190
            };

            // 查询门诊费用
            $.post(ip + "/mzywYdjy/queryMzfy", {ybParm: JSON.stringify(ybParm), parm: JSON.stringify(param)}, function (json) {
                if(json.code == 0){
                    malert(json.msg);
                }else{
                    result = '1';
                    malert("查询费用信息失败！" + json.msg,'top','defeadted');
                    return false;
                }
            });

            //费用上传失败，不往下走了
            if (result == '1'){
                return result;
            }

            var yjsParam = {
                akc190: yndryb_010.ybdjxx.akc190,
                cbtcqh: yndryb_010.ybdjxx.yab003,
                ybgrbh: yndryb_010.ybdjxx.aac001,
                hzybkh: yndryb_010.ybdjxx.akc020,
                ywzqh: yndryb_010.signInfo.ywzqh,
                ybdjid: yndryb_010.ybdjxx.ybdjid,
                aae011: userId,
                hisywbh: yndryb_010.ybdjxx.ykc010
            };

            $.post(ip + "/yndrybYdjy/ybyjs", {parm: JSON.stringify(yjsParam)}, function (json) {
                if(json.code == 0){
                    // fylist = json.data;
                    rightVue.yjsContentYndryb = json.data;
                    var zfy = 0;    //总费用
                    var ybzf = 0;   //医保支付
                    var xjzf = 0;   //现金支付
                    if (typeof rightVue.yjsContentYndryb.akc264 == 'string'){
                        zfy = rightVue.yjsContentYndryb.akc264;
                    }
                    if (typeof rightVue.yjsContentYndryb.akc260 == 'string'){
                        ybzf = rightVue.yjsContentYndryb.akc260;
                    }
                    if (typeof rightVue.yjsContentYndryb.akc261 == 'string'){
                        xjzf = rightVue.yjsContentYndryb.akc261;
                    }

                    rightVue.yjsContentYndryb.bxje = rightVue.fDec(
                        parseFloat(zfy) - parseFloat(xjzf), 2);

                    rightVue.yndr_jylsh = json.data.jslsh;

                }else{
                    malert("预结算失败！" + json.msg,'right','defeadted');
                    result = "1";
                }
            });

            return result;
        },

        //门诊结算
        mzjs: function () {
            var yjsParam = {
                akc190: yndryb_010.ybdjxx.akc190,
                cbtcqh: yndryb_010.ybdjxx.yab003,
                ybgrbh: yndryb_010.ybdjxx.aac001,
                hzybkh: yndryb_010.ybdjxx.akc020,
                ywzqh: yndryb_010.signInfo.ywzqh,
                ybdjid: yndryb_010.ybdjxx.ybdjid,
                aae011: userId,
                hisywbh: yndryb_010.ybdjxx.ykc010
            };
            $.post(ip + "/yndrybYdjy/ybjs", {parm: JSON.stringify(rightVue.yjsContentYndryb)
            }, function (json) {
                if (json.code == 0) {
                    rightVue.jsContentYndryb = json.data;
                    rightVue.zxlshSn = json.data.jslsh;
                    popCenter1.jssb = false;
                    malert(json.msg);
                } else {
                    popCenter1.jssb = true;
                    malert(json.msg, 'top', 'defeadted');
                }
            });
        },

        // 取消结算
        qxjs: function () {
            var parm = {
                ybgrbh: yndryb_010.ybdjxx.ybgrbh,
                ddbh: yndryb_010.ybdjxx.ddbh,
                djh: yndryb_010.ybdjxx.djid,
                jylsh: rightVue.jsContentYndryb.jylsh,
                fph: rightVue.jsContentYndryb.fph,
                jbr: userId+userName
            };

            $.post(ip + "/mzywYdjy/qxjs", {parm:JSON.stringify(rightVue.yjsContentYndryb)}, function (json) {
                if (json.code == 0) {
                    malert(json.msg);
                } else {
                    malert(json.msg, 'top', 'defeadted');
                }
            });
        },

        // 检索疾病编码
        searching: function (add, type, val) {
            console.log(val)
            this.jbContent['jbmc'] = val;
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent[type] == undefined || this.jbContent[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};

            $.post(ip + "/baseData/queryYbJbml", {parm: JSON.stringify(str_param)},
                function (json) {
                    if (json.code == 0) {
                        var res = json.data;
                        if (add) {
                                yndryb_010.searchCon=yndryb_010.searchCon.concat(res.list);
                        } else {
                            yndryb_010.searchCon = res.list;
                        }
                        yndryb_010.page.total = res.totalSize;
                        yndryb_010.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询疾病项目失败：" +json.msg, "top", "defeadted");
                    }
                });

            // $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yndryb_010.bxurl + "&bxlbbm=" + yndryb_010.bxlbbm + "&types=baseData&method=queryJbml&parm="
            //     + JSON.stringify(str_param),
            //     function (data) {
            //         if (data.a == '0') {
            //             var res = JSON.parse(data.d);
            //             if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
            //                 for (var i = 0; i < res.list.length; i++) {
            //                     yndryb_010.searchCon.push(res[i]);
            //                 }
            //             } else {
            //                 yndryb_010.searchCon = res.list;
            //             }
            //             yndryb_010.page.total = res.totalSize;
            //             yndryb_010.selSearch = 0;
            //             if (res.list.length > 0 && !add) {
            //                 $(".selectGroup").hide();
            //                 _searchEvent.show();
            //             }
            //         } else {
            //             malert("查询疾病项目失败：" + data.c, "top", "defeadted");
            //         }
            //     });
        },
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent = item;
                Vue.set(this.jbContent, 'jbmc', this.jbContent['aka121']);
                yndryb_010.grxxJson.akc193 = this.jbContent.aka120;
                yndryb_010.grxxJson.ykc600 = this.jbContent.aka121;
                $(".selectGroup").hide();
            }
        },
        changeDown: function (event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'jbContent', 'searchCon');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent, 'jbmc', this.jbContent['aka121']);
                    yndryb_010.grxxJson.akc193 = this.jbContent.aka120;
                    yndryb_010.grxxJson.ykc600 = this.jbContent.aka121;

                    this.selSearch = 0;
                    this.nextFocus(event);
                }
            }
        },
    },
});
yndryb_010.getbxlb();
