<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>数据上报</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="sjsb.css" rel="stylesheet"/>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="wrapper">
    <div class="panel padd-l-10 padd-r-10">
        <div class="tong-top ">
            <button class="tong-btn btn-parmary-b  icon-sx paddr-r5" @click="getData">刷新</button>
            <button class="tong-btn btn-parmary-b  icon-width icon-dc " @click="exportCSV">导出</button>
            <div class="flex-container flex-align-c margin-l-10">
                <label class="whiteSpace  ft-14 margin-r-5">格式</label>
                <select-input @change-data="commonResultChange" :not_empty="true" :child="sjgs_tran"
                              :index="param.sjgs" :val="param.sjgs" :search="true"
                              :name="'param.sjgs'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c margin-l-10">
                <label class="whiteSpace  ft-14 margin-r-5">类型</label>
                <select-input @change-data="commonResultChange" :not_empty="true" :child="yylx_tran"
                              :index="param.zxlx" :val="param.zxlx" :search="true"
                              :name="'param.zxlx'">
                </select-input>
            </div>

            <label class=" sjsb-label">出院日期</label>
                    <div class="position flex-container flex-align-c">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh182 text-indent20" placeholder="请选择出院日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh182 " placeholder="请选择出院日期" id="timeVal1" />
                    </div>
        </div>
    </div>
    <div class="zui-table-view  padd-r-10 padd-l-10">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m" >序号</div></th>
                    <th v-for="(item,index) in htmlData" class="zui-table-cell" :class="item.className || 'cell-s'"><div class="zui-table-cell" :class="item.className || 'cell-s'">{{item.text}}</div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body"  @scroll="scrollTable($event)">
            <table class="zui-table table-width50" v-if="scJsonList.length != 0 || jsonList.length!=0">
                <tbody>
                <tr :tabindex="$index" v-show="xyOrZy != 'sc'" v-for="(item, $index) in jsonList"
                    @mouseenter="switchIndex('hoverIndex',true,$index)"
                    @mouseleave="switchIndex()"
                    @click="switchIndex('activeIndex',true,$index)"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]" class="tableTr2" ref="list">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">序号</div></td>
                     <td><div class="zui-table-cell cell-l" v-text="item.jgmc"></div><td>
                     <td><div class="zui-table-cell cell-s" v-text="item.zzjgdm"></div><td>
                     <td><div class="zui-table-cell cell-s" v-text="item.zycs"></div><td>
                     <td><div class="zui-table-cell cell-s" v-text="item.bah"></div><td>
                     <td><div class="zui-table-cell cell-s" v-text="item.brxm"></div><td>
                     <td><div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div><td>
                     <td><div class="zui-table-cell cell-s" v-text="fDate(item.csrq,'date')"></div><td>
                     <td><div class="zui-table-cell cell-s" v-text="item.nl"></div><td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.sfzjhm"></div><td>
                    <td><div class="zui-table-cell cell-l" v-text="item.jzdmc"></div><td>
                    <td><div class="zui-table-cell cell-s" v-text="item.sjhm"></div><td>
                    <td><div class="zui-table-cell cell-l" v-text="item.hkdz"></div><td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.ryrq,'date')"></div><td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ryks"></div><td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cyzyzdmc"></div><td>
                    <td><div class="zui-table-cell cell-s" v-text="item.zzysxm"></div><td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.cyrq,'date')"></div><td>
                    <td><div class="zui-table-cell cell-s" v-text="item.zfy"></div><td>
                <!--
                                        <td><div class="zui-table-cell cell-s" v-text="item.lxrxm"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.lxrgx"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.lxrdz"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.lxrdh"></div><td>

                                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.ryrq,'date')"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.ryks"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.rybf"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.cyrq,'date')"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.cyks"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.kzrxm"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.zrysxm"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.zzysxm"></div><td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.zyysxm"></div><td>
                    -->
                </tr>
                </tbody>
            </table>
            <p v-if="scJsonList.length==0 && jsonList.length ==0" class="  noData text-center zan-border">暂无数据...</p>
        </div>
            <div class="sjsb-box" v-if="!scShow"><span >暂无数据,请先<i class="color-green cursor" @click="saveDate">生成数据</i></span></div>
     <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<script src="sjsb.js"></script>
</body>
</html>
