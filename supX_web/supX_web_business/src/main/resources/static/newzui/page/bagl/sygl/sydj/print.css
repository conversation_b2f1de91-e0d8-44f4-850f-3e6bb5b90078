
/*设置纸张大小和横向*/
@page {
    size: A4 portrait;
    
	margin: 0;
}
.printHide {
    display: none !important;
	height:0px !important;
}

.printShow {
    display: block !important;
}
.t-values {
    border: 0;
}

.tem {
    border: 0;
}

.printContent1{
		page-break-inside: avoid !important;
		page-break-after: always !important;
		page-break-before: always !important;
	}
.printContent2{
		page-break-inside: avoid !important;
		page-break-after: always !important;
		page-break-before: always !important;
	}

.drag {
    display: none;
}
body{
    height: auto !important;
}
body,
html{
    height: auto;
    width: auto;
    min-height: auto;
    min-width: auto;
	font-family: "宋体";
}

/*隐藏页脚和页眉*/
nav, aside {
    display: none !important;
}

button {
    display: none !important;
}
.padd-r-10 {
    padding-right: 0 !important;
}

.basyzt{
	font-size: 0.8cm;
	
	width: 100%;
	text-align: center;
	color: #000000;
}
.printHead{
	width: 17.9cm;
	margin: 0 auto;
	
}
.printPop {
  position: relative;
  display: inline-block;
  overflow: visible;
  height: 100%;
  width: 100%;
  
  background-color: white;
}
.flex-container {
    display: -ms-flex;
    display: -moz-flex;
    display: -o-flex;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
}

.printContent{
  display: flex;
  flex-wrap: wrap;
  width: 17.9cm;
  margin: 0 auto;
  height: 23.2cm;
  border: 0.1mm solid #000;
  
}
.printContent1{
	height: 29.7cm !important;
	width: 21cm !important;
	color:#000000;
}
.printContent2{
	height: 26.2cm ;
	width: 21cm !important;
}
.printContent22{
	margin-top: 2cm;
}
.printTable1{
	display: flex !important;
	flex-wrap: wrap !important;
	margin: 0mm 0.5mm 0 0.5mm !important;
	overflow: hidden;
}

.printTable2{
	display: flex !important;
	flex-wrap: wrap !important;
	margin: 0mm 0.5mm 0 0.5mm !important;
}

.printheadersj{
	display: flex;
	flex-wrap: wrap;
	margin: 3mm auto 0;
	padding: 0 3mm 0 3mm;
}







.flex-jus-c { /*子项目在主轴方向上 居中对齐*/
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.baxm{
	width:2.75cm !important;
	
	
}
.selected {
  border: 0.1mm solid #000 !important;
  width: 0.5cm !important;
  height: 0.5cm !important;
  text-align: center;
}
.printFormat{
	color: #000000;
	margin-top: 1cm;
}
.csrq{
	width:3.1cm !important;
	
}
.yqq-xhx{
	border-bottom: 0.1mm solid #CCCCCC;
	text-align: left;
	
}
.banl{
	width:1.5cm !important;
	
}
.bayl{
	width:1.5cm !important;
	
}
 .bagj{
	width:1.5cm !important;
	
}
.baxcstz{
	width:1.7cm !important;
	
}
.baxrytz{
	width:1.7cm !important;
	
}
.bacsd{
	width:6.6cm !important;
	
}
.bajg {
	width:3.1cm !important;
	
}
.bazy {
	width:3.1cm !important;
	
}
.badwdh{
	width:3.1cm !important;
	
}
.bamz{
	width:2.3cm !important;
	
}
.basfz{
	width:4.5cm !important;
	
}
.baxzz{
	width:6.65cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.badh{
	width:3.05cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bayb{
	width:2.4cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bahkd {
	width:9cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bagzdw{
	width:6.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.balxm{
	width:1.9cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.balgx{
	width:1.3cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.baldz{
	width:6.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.baldh{
	width:2.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.baqtyljg{
	width:5cm !important;
	border-bottom: 0.1mm solid #CCCCCC;display: inline-block;
}
.basj{
	width:3.1cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bakb{
	width:3.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.babf{
	width:1cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazkkb{
	width:2.9cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.baszy{
	width:3.1cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.basjzy{
	width:1.1cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bamzzd{
	width:4.3cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bajbzd{
	width:3.3cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.baryzd{
	width:4.3cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazzqz{
	width: 2.4cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.baqj{
	width:0.95cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.baqjcs{
	width:0.9cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.patientTable{
	width: 17.8cm !important;
	
	border: 0.1mm solid #000;
}
.patientTable th{
	height: 0.85cm !important;
	max-height: 0.85cm !important;
	text-align: center;
	border: 0.1mm solid #000;
}
.patientTable td{
	border: 0.1mm solid #000;
}
.patientTable td div{
	height: 0.6cm !important;
	max-height: 0.6cm !important;
	
	overflow: hidden;
}



.tbcyzd{
	width: 5.25cm !important;
	
}
.tbjbbm{
	width: 2.15cm !important;
	
}
.tbrybq{
	width: 0.8cm !important;
	
}
.tbcyqk{
	width: 0.7cm !important;
	
}
.printTableDiv{
	margin-top: 0 !important;
	
}
.printTableDivzs{
	width:17.8cm !important;
	border-bottom: 0.1mm solid #000;
}
.sswbyy{
	width: 8.2cm !important;
	
}
.ssjbbm{
	width: 3cm !important;
	
}
.bablzdmc{
	width: 3.8cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bablzdbm{
	width: 2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bablh{
	width: 2.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.babltnm{
	width: 2.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.blbz{
	width: 5.7cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.ywgmdiv{
	width: 11.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.gmyw{
	width: 6.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.szqx{
	width: 3.3cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bakzr{
	width: 1.75cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazrys{
	width: 1.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazzys{
	width: 1.6cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazzhiys{
	width: 1.4cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazyys{
	width: 1.45cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazrhs{
	width: 1.65cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bajxys{
	width: 2.65cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.basxys{
	width: 1.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.babmy{
	width: 1.45cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazkys{
	width: 2.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazkhs{
	width: 2.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazkrq{
	width: 2.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.xmzl{
	width: 4cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.line1{
	width:17.9cm;
	display: flex;
	font-size: 0.3cm !important;
	margin-top: 1.25mm;
}
.linets{
	width:17.9cm;
	display: flex;
	font-size: 0.3cm !important;
	margin-top: 1mm;
}


.xbzl{
	width: 3.05cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.cszl{
	width: 4.7cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.nlzl{
	width: 2.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.gjzl{
	width: 3.3cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.ylzl{
	margin-left: 1cm;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
	width: 6cm !important;
	
}
.xsecstzzl{
	width: 5.1cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.xserytzzl{
	width: 5.75cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.csdzl{
	width: 9.55cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.jgdzl{
	width: 4.35cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.mzzl{
	width: 3.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.sfzzl{
	width: 6.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.zyzl{
	width: 4.25cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.hyzl{
	width: 7.05cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.xzzzl{
	width: 9.4cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.dhzl{
	width: 4.3cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}

.ybzl{
	width: 3.7cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.hkdzl{
	width: 13.7cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.gzdwzl{
	width: 8.95cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.dwdhzl{
	width: 4.75cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.lxrxmzl{
	width: 3.85cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.lxrdxzl{
	width: 2.2cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.lxrdzzl{
	width: 7.35cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.lxrdhzl{
	width: 3.75cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.rytjzl{
	width: 17.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.rysjzl{
	width: 4.7cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.rykbzl{
	width: 5.05cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.rybfzl{
	width: 2.85cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.zzkbzl{
	width: 4.55cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.sjzyzl{
	width: 4.55cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.bazdzl{
	width: 6.9cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.jbbmzl{
	width: 5.1cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.yqq-lab{
	float: left;
}
.yqq-xhx{
	float: left;
	
}
.ryqkzl{
	width: 5.35cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.ryzdzl{
	width: 6.9cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.zzqrzl{
	width: 5.3cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}
.zzqjwhzl{
	width: 7.1cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}

.qjcszl{
	width: 10.5cm !important;
	overflow: hidden;
	    display: block;
	    height: 0.5cm;
}


.printTableDivzs1{
	min-height:0.7cm;
	max-height: 0.7cm;
	width: 17.8cm;
}
.printTableDivzs2{
	min-height:1.3cm;
	max-height: 1.3cm;
	width: 17.8cm;
}
.printTableDivzs3{
	min-height:0.7cm;
	max-height: 0.7cm;
	width: 17.8cm;
}
.printTableDivzs4{
	min-height:0.65cm;
	max-height: 0.65cm;
	width: 17.8cm;
}
.printTableDivzs5{
	min-height:0.75cm;
	max-height: 0.75cm;
	width: 17.8cm;
}
.printTableDivzs6{
	min-height:1.15cm;
	max-height: 1.15cm;
	width: 17.8cm;
}
.printTableDivzs7{
	min-height:0.8cm;
	max-height: 0.8cm;
	width: 17.8cm;
	border-bottom: 0mm solid #000 !important;
}
.sszdwbyy{
	width: 12.2cm;
}
.sszdjbbm{
	width: 5.45cm;
}
.blzdmczl{
	width: 6cm;
}
.blzdbmzl{
	width: 3.5cm;
}
.blzdhzl{
	width: 4.4cm;
}
.bltnmzl{
	width: 3.7cm;
}
.crbzl{
	width: 4.5cm;
}
.crbbkzl{
	width: 5.45cm;
}
.crbbzzl{
	width: 7.95cm;
}
.ywgmdiv{
	width: 12.2cm;
}
.swsjbg{
	width: 5.7cm;
}
.xxzl{
	width: 9.35cm;
}
.rhzl{
	width: 8.55cm;
}
.szzl{
	width: 5cm;
}
.szqxzl{
	width: 12.9cm;
}
.kzrzl{
	width: 3.65cm;
}
.fzrzl{
	width: 4.45cm;
}
.zzyszl{
	width: 3.2cm;
}
.zzzyszl{
	width: 3.3cm;
}
.zyyszl{
	width: 3.3cm;
}
.zrhszl{
	width: 3.15cm;
}
.jxyszl{
	width: 4.5cm;
}
.sxyszl{
	width: 3.25cm;
}
.bmyzl{
	width: 6.2cm;
}
.bazlzl{
	width: 4.5cm;
}
.zkyszl{
	width: 4.25cm;
}
.zkhszl{
	width: 4.25cm;
}
.zkrqzl{
	width: 4.9cm;
}
.ssqkyfxyw{
	width: 7.3cm;
}
.sycxsjzl{
	width: 1.5cm;
}
.sycxsj{
	width: 5.25cm;
}
.lhyyzl{
	width: 4.35cm
}
.ssjczbm{
	width:1.5cm;
}
.ssjczrq{
	width:1.8cm;
}
.ssssjb{
	width:1.55cm;
}
.ssjczmc{
	width:3.9cm;
}
.ssczjys{
	width:4.75cm;
}
.qkyhdj{
	width:1.45cm;
}
.ssmzfs{
	width:1.55cm;
}
.ssmzys{
	width:1.45cm;
}
.ssczjyssz{
	width:1.55cm;
}
.ssczjysiz{
	width:1.65cm;
}
.ssczjysez{
	width:1.55cm;
}
.patientTable2{
	width: 17.8cm !important;
	
	border: 0.1mm solid #000;
}
.patientTable2 th{
	text-align: center;
	height: 0.45cm !important;
	border: 0.1mm solid #000;
}
.patientTable2 td{
	text-align: center;
	height: 0.6cm !important;
	border: 0.1mm solid #000;
}

.patientTable2 td div{
	height: 0.6cm !important;
	max-height: 0.6cm !important;
	
	overflow: hidden;
}
.lcljtcyy{
	width:4.5cm;
}
.sfbyyy{
	width:11.75cm;
}
.qtfyxhx{
	width: 2.45cm;
}
.qtfyzl{
	margin-left: 0.35cm;
	width: 17.8cm;
}
.sslycxclfzl{
	margin-left: 0.35cm;
	width: 16.25cm;
}
.sslycxclfxhx{
	width: 1.85cm;
}
.zlyycxclfxhx{
	width: 2cm;
}
.zlyycxclfzl{
	width: 9.85cm;
}
.jcyycxyyclzl{
	margin-left: 0.35cm;
	width: 7.95cm;
}
.jcyycxyyclxhx{
	width: 1.2cm
}
.xbyzlzpfzl{
	width: 9.65cm
}
.xbyzlzpfxhx{
	width: 1.3cm
}
.zxlyzlzpzl{
	margin-left: 3.35cm;
	width: 5.5cm
}
.zxlyzlzpxhx{
	width: 1.1cm
}
.qdblzpxhx{
	width: 1.5cm
}
.qdblzpfzl{
	width: 7.1cm
}
.bdblzpfxhx{
	width: 1.3cm
}
.bdblzpfzl{
	width: 4.6cm
}
.xyhxyzplzl{
	margin-left: 0.35cm;
	width: 6.1cm
}
.xyhxyzplxhx{
	width: 1cm
}
.zcyfzl{
	width: 12.3cm
}
.zcyfxhx{
	width: 1.3cm
}
.zccyfzl{
	margin-left: 0.35cm;
	width: 5.5cm
}
.zccyfxhx{
	width: 1.35cm
}
.kjywfyxhx{
	width: 3cm
}
.kjywfyzl{
	width: 10.8cm
}
.xylxyfxhx{
	width: 2cm
}
.xylxyfzl{
	margin-left: 0.35cm;
	width: 6.5cm
}
.zylzyfzl{
	margin-left: 0.35cm;
	width: 17.85cm
}
.zylzyfxhx{
	width: 1.4cm
}
.kflkffxhx{
	width: 1.5cm
}
.kflkffzl{
	margin-left: 0.35cm;
	width: 17.85cm
}
.mzfzl{
	margin-left: 0.35cm;
	width: 3.55cm
}
.ssfzl{
	width: 14cm
}
.mzfxhx{
	width: 1.6cm
}
.ssfxhx{
	width: 1.5cm
}
.sszlfzl{
	width: 6.25cm
}
.sszlfxhx{
	width: 2.5cm
}
.lcwlzlfzl{
	width: 4.7cm
}
.lcwlzlfxhx{
	width: 1.7cm
}
.fsszlxmfzl{
	margin-left: 0.35cm;
	width: 6.85cm
}
.fsszlxmfxhx{
	width: 1.6cm
}
.lczdxmfxhx{
	width: 3.35cm
}
.lczdxmfzl{
	margin-left: 0.35cm;
	width: 16.35cm
}
.yyxzdfxhx{
	width: 2.2cm
}
.yyxzdfzl{
	width: 5.1cm
}
.ssyzdfxhx{
	width: 2.3cm
}
.ssyzdfzl{
	width: 5.3cm
}
.blzdfxhx{
	width: 2.9cm
}
.blzdfzl{
	margin-left: 0.35cm;
	width: 7.5cm
}
.zhqtfyxhx{
	width: 1.6cm
}
.zhqtfyzl{
	margin-left: 0.35cm;
	width: 15.1cm
}
.zhhlfxhx{
	width: 2.1cm
}
.zhhlfzl{
	width: 4.1cm
}
.ybzlczfxhx{
	width: 1.8cm
}
.ybzlczfzl{
	width: 5.1cm
}
.zhybylfwfxhx{
	width: 2.3cm
}
.zhybylfwfzl{
	margin-left: 0.35cm;
	width: 8.7cm
}
.qtzfxhx{
	width: 2.3cm
}
.qtzfzl{
	width: 7cm
}
.zfjexhx{
	width: 2.35cm
}
.zfjezl{
	width: 4.5cm
}
.zyfyzfyxhx{
	width: 3cm
}
.zyfyzfyzl{
	margin-left: 0.35cm;
	width: 7.15cm
}
.ysczyjgtsxhx{
	width: 0.85cm
}
.ysczyjgtszl{
	width: 10.5cm
}
.sftybzzl{
	margin-left: 0.35cm;
	width: 7.7cm
}
.lnsshzhmsjzl{
	margin-left: 0.35cm;
	width: 4.15cm
}
.lnsshzryhmsjzl{
	
	width: 7cm
}
.lnsshzryhhmsjzl{
	width: 6.7cm
}
.ryqsjxhx{
	width: 1cm
}
.sfycyzzyjhzl{
	margin-left: 0.35cm;
	width: 7.15cm
}
.sfycyzzymdzl{
	width: 10.7cm
}
.zzyjhmd{
	width: 9.6cm
}
.njsyljgmczl{
	margin-left: 0.35cm;
	width: 17.85cm;
	margin-bottom: 0.1cm;
}
.njsyljgmc{
	width: 14.3cm
}
.lyfszl{
	margin-left: 0.35cm;
	width: 17.85cm;
	
}

.sxfyzl{
	width: 6cm
}
.sxqtzl{
	width: 7.85cm
}
.lcdxhx{
	width: 0.7cm
}
.sxqtzlxhx{
	width: 3cm
}
.sybdbzl{
	margin-left: 0.35cm;
	width: 3.85cm
}
.sybdbxhx{
	width: 1.1cm
}
.ztxhszl{
	width: 3.7cm
}
.ztxhsxhx{
	width: 0.9cm
}
.qxzl{
	width: 2.1cm
}
.qxxhx{
	width: 0.8cm
}
.xjzl{
	width: 2.9cm
}
.xjxhx{
	width: 0.9cm
}
.xxbzl{
	width: 3.3cm
}
.xxbxhx{
	width: 0.9cm
}
.hxbzl{
	margin-left: 0.35cm;
	width: 4.75cm
}
.hxbxhx{
	width: 0.7cm
}
.ywhzzl{
	width: 13cm
}
.ywhzcsxhx{
	width: 1.1cm
}
.ywhzqtxhx{
	width: 5.8cm
}
.ynhzcszl{
	width: 3.3cm
}
.ynhzcsxhx{
	width: 0.85cm
}
.hzqkzl{
	margin-left: 0.35cm;
	width: 4.35cm
}
.sfbyzl{
	margin-left: 0.35cm;
	width: 17.85cm
}
.sfbyyyxhx{
	width: 11.8cm
}
.sfsslcljzl{
	margin-left: 0.35cm;
	width: 17.85cm
}
.lcljtcyyxhx{
	width: 4.5cm
}
.kzywzl{
	width: 10.05cm;
	margin-left: 0.35cm;
}
.kzywcxsjxhx{
	width: 1.1cm
}
.printTableDivss{
	height: 0.9cm;
	padding-top: 0.2cm;
}
.zylhyyzl{
	width: 3.8cm
}
.ljdddszl{
	width: 3.8cm
}
.ljddds{
	width: 2cm
}
.linetxhx{
	border-bottom: 0.1mm solid #000;
}
.divgd{
	padding-top:0.2cm;
	height: 0.8cm !important;
}
.sftybzgd{
	padding-top:0.3cm;
	height: 1cm !important;
}
.xbyzlzpfxhx{
	width: 1.2cm
}
.yljgxhx{
	width:4cm;
}
.yljgzl{
	width:7cm;
}
.ylfkfszl{
	width:17cm;
}
.ylfkfsxhx{
	width:5cm;
}
.jkkhzl{
	width:7.3cm;
}
.jkkhxhx{
	width:5.5cm;
}
.djczyzl{
	width:6cm;
}
.djczyxhx{
	width:1cm;
}
.sybahzl{
	width:4cm;
}
.sybahxhx{
	width:2.5cm;
}
.zzjgdmzl{
	width:10cm;
}
.zzjgdmxhx{
	width:2.7cm;
}
.imgfd{
	position: absolute;
	top: 1cm;
	right: 2.5cm;
}
.babzsm{
	margin-left: 1.8cm;
}
.pagePrintBefore{page-break-before:auto;}
.pagePrintAfter{page-break-after:always;}
