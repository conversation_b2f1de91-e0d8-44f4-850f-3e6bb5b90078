var wrapper = new Vue({
    el: '.panel',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        jsonList: [],
        isShow: true,
        kfList: [],
        popContent: {},
        kfEmpty: false,
        changeList: [],
        search: ''
    },
    methods: {
        //刷新
        refresh: function () {
            yjkmtableInfo.getData();
        },
        //保存
        save: function () {
            yjkmtableInfo.save();
        },
        getKfData: function () {
            // 请求库房的api
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100014004"}', function (data) {
                if (data.a == 0) {
                    wrapper.kfList = data.d;
                    Vue.set(wrapper.popContent, 'kfbm', data.d[0].kfbm);
                    yjkmtableInfo.getData();
                } else {
                    malert("获取库房列表失败", 'top', 'defeadted');
                }
                /* for (var i = 0; i < data.d.length; i++)
                     Vue.set(wrapper.kfList, i, data.d[i])*/
            });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "kfbm":
                    Vue.set(this.popContent, 'kfbm', val[0]);
                    Vue.set(this.popContent, 'kfmc', val[4]);
                    yjkmtableInfo.getData();
                    // wap.getCsqx();
                    break;
                default:
                    break;
            }
        },
    },
    watch: {
        'search': function () {
            Vue.set(yjkmtableInfo.param, 'parm', wrapper.search);
            Vue.set(yjkmtableInfo.param, 'page', 1);
            yjkmtableInfo.getData();
        }
    }
});

//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
var saves = null;

//科目
var yjkmtableInfo = new Vue({
    el: '.zui-table-view',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        param: {
            page: 1,
            rows: 100,
            parm: '',
            sort: 'kfbm,ypbm'
        },
        jsonList: [],
        isShow: true,
        ypjx: {},
        ypgx: {},
        kfEmpty: false,
        ypzl: {},
        ypcd: {},
        ypdw: {},
        ybtclb: {},
        yyff: {},
        searchWord: '',
        updateList: []

    },
    methods: {
        //进入页面加载列表信息
        getData: function () {
            common.openloading('.zui-table-view')
            this.updateList = [];
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhKcxl&types=queryYpzd&" +
                "kfbm=" + wrapper.popContent.kfbm + "&dg=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    yjkmtableInfo.totlePage = Math.ceil(json.d.total / yjkmtableInfo.param.rows);
                    yjkmtableInfo.jsonList = json.d.list;
                }
            });
            common.closeLoading()
        },

        save: function () {
            var isEmpty = false;
            if (wrapper.popContent.kfbm == null) {
                malert("请先选择库房！");
                this.kfEmpty = true;
                isEmpty = true;
                return
            }
            if (this.updateList.length == 0) {
                malert("请设置限量值");
                return
            }
            //设置库房编码属性
            for (var i = 0; i < this.updateList.length; i++) {
                Vue.set(this.updateList[i], 'kfbm', wrapper.popContent.kfbm);
            }

            var json = {
                "list": this.updateList
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfwhKcxl&types=update',
                JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == 0) {
                        malert("数据更新成功");
                        yjkmtableInfo.getData();
                    } else {
                        malert("数据更新失败");
                    }
                })
        },
        //修改
        listSave: function (num) {
            var obj = this.jsonList[num];
            if (this.updateList.length == 0) {
                this.updateList.push(obj);
            } else {
                //数组中是否存在该值
                for (var i = 0; i < this.updateList.length; i++) {
                    if (this.updateList[i].kfbm == obj.kfbm && this.updateList[i].ypbm == obj.ypbm) {
                        //相同
                        this.updateList[i] = obj;
                        return
                    }
                }
                //未找到添加
                this.updateList.push(obj);
            }
        }

    },

});
wrapper.getKfData();
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

})



