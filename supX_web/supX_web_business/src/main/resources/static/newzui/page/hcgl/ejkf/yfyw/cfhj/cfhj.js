//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
var cflx = ""; //处方类型
var ylbm = "N040030022001";
var ksbm = "";
var parm = {};
var hzlx = "0"; //患者类型 默认门诊 0-门诊,1-住院
var hzlxqhfs = "1"; //默认取上次
var bckjj = null; //保存快捷键
//var json = {hzlx:"0"};//"yfbm":"01"

//工具栏
var toolBar = new Vue({
    el: '#ToolBar',
    mixins: [dic_transform, baseFunc, tableBase, mformat,printer],
    data: {
        cfhObj:{},
        jsonList: [],
        yfContent: {
            hzlx: "0"
        }

        //hzlx:"门诊划价" //默认门诊划价
    },
    methods: {
        getOpen:function(){
            tableInfo.index=0;
        },
        //刷新获取处方
        getData: function() {
            tableInfo.getData();
        },
        //药房选择
        Wf_YfChange: function(val) {
            // Vue.set(this[val[2][0]],val[2][[1]],val[0])
            Vue.set(this.yfContent,'yfbm',val[0]);
            this.$forceUpdate();
            popWin.PfContent = {};
            Vue.set(popWin.yfContent, 'cflx', this.jsonList[val[5]]['cflx']);
            Vue.set(toolBar.yfContent, 'kfbz', "0");
            popWin.CflxJosn = jsonFilter(popWin.cflxList, "yfbm", this.yfContent.yfbm);
            tableInfo.getData();
            toolBar.newData();
        },

        //新增处方信息
        newData: function() {
            //初始化完成提示
            /*if (popWin.CfContent.kfbz == "1"){
                malert("已扣费处方,不允许添加配方!")
                return;
            }*/
            popWin.PfContent = {};
            popWin.CfContent = {};
            //			popWin.CfContent = {};
            tablePfInfo.jsonList = [];
            popWin.yfContent["pybz"] = "0";

            //每次取默认处方类型和患者类型
            if(hzlxqhfs == "0") {
                popWin.yfContent["cflx"] = cflx;
                toolBar.yfContent["hzlx"] = hzlx;
            }
            $(".selectGroup").hide(); //隐藏下拉框
            document.getElementById("bah").focus();
        },
        //添加一行
        addData: function() {
            if(popWin.CfContent.kfbz == "1") {
                malert("已扣费处方,不允许添加配方!",'top','defeadted')
                return;
            }
            var ypbm = popWin.PfContent.ypbm;
            if(!ypbm) {
                malert("请选择药品!",'top','defeadted')
                return;
            }
            //判断当前用是不是不能大于库存量
            popWin.PfContent.cfyl=popWin.PfContent.cfyl*(popWin.CfContent.zyfs || 1)
            var cfyl = popWin.PfContent.cfyl;
            var sjkc = popWin.PfContent.sjkc;
            var kcsl = popWin.PfContent.kcsl;
            var wfy = popWin.PfContent.wfy;
            if(wfy >= kcsl) {
                malert("库存不足，请检查未发药库存",'top','defeadted');
                return;
            }
            if(cfyl == undefined) {
                malert("处方用量不能为空",'top','defeadted');
                return;
            }
            if(cfyl <= 0) {
                malert("处方用量不能小于等于零",'top','defeadted');
                return;
            }
            if(cfyl > sjkc) {
                popWin.PfContent.cfyl = sjkc;
                malert("当前使用量不能大于库存量!",'top','defeadted')
                return;
            }
            //判断是否有药品存在
            for(var i = 0; i < tablePfInfo.jsonList.length; i++) {
                if(tablePfInfo.jsonList[i].ypbm == ypbm) {
                    //var cm = mconfirm("药品【"+popWin.PfContent.ypmc+"】已存在,请修改已有数据!");
                    malert("药品【" + popWin.PfContent.ypmc + "】已存在,请修改已有数据!",'top','defeadted');
                    return;
                }
            }
            var json = {
                "ypbm": ypbm,
                "yfbm": toolBar.yfContent.yfbm
                // "yfbm": '1'
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkcone' +
                '&json=' + JSON.stringify(json),
                function(data) {
                    if(data.a == "0" && data.d && data.d.list.length > 0) { //成功
                            for(var int = 0; int < data.d.list.length; int++) {
                                if(cfyl < data.d.list[int].sjkc) { //批次够用
                                    data.d.list[int].cfyl = cfyl;
                                    tablePfInfo.jsonList.push(data.d.list[int]);
                                    break;
                                } else { //批次不够用
                                    data.d.list[int].cfyl = data.d.list[int].sjkc;
                                    tablePfInfo.jsonList.push(data.d.list[int]);
                                    cfyl = cfyl - data.d.list[int].sjkc;
                                }
                            }
                            //数量回车后，光标回到药品名称
                            document.getElementById("ypmc").focus();
                    } else {
                        malert("药品批次检索失败:" + data.c,'top','defeadted');
                    }
                });
            popWin.PfContent = {};
            document.getElementById("ypmc").focus();
        },

        //作废处方信息
        delData: function() {
            if(popWin.CfContent.cfh == null || popWin.CfContent.cfh == undefined) {
                malert('请选择处方','top','defeadted');
                return;
            }

            var obj = {
                'cfh': popWin.CfContent.cfh
            };

            this.$http.post('/actionDispatcher.do?reqUrl=YfbYfywCftyzf&types=cfzf', JSON.stringify(obj))
                .then(function(data) {
                    if(data.body.a == 0) {
                        this.getData();
                        this.isChecked = [];
                        malert("处方作废保存成功",'top','success');
                    } else {
                        malert(data.body.c,'top','defeadted');
                    }
                }, function(error) {
                    console.log(error);
                });
        },

        //保存处方方信息
        saveData: function() {
        	console.log("cfhj");
        	if (!popWin.ifClick) return;// 已经点击过就不能再点
            popWin.ifClick = false;
            $.ajaxSettings.async = false;
            var xm = popWin.CfContent.brxm;
            /*
            if(xm == undefined || xm == null || xm == "") {
                malert("请输入或选择病人!",'top','defeadted')
                popWin.ifClick = true;
                return;
            }
            */
            var kfbz = popWin.CfContent.kfbz;
            if(kfbz == "1") {
                malert("已扣费处方不允许再次修改保存!",'top','defeadted')
                popWin.ifClick = true;
                return;
            }
            var fybz = popWin.CfContent.fybz;
            if(fybz == "1") {
                malert("已发药处方不允许再次修改保存!",'top','defeadted')
                popWin.ifClick = true;
                return;
            }
            if(toolBar.yfContent.hzlx == "0") {
//				this.dg.sort = "ghxh";
//				popWin.types = "brgh";
//				popWin.hzthem = mzthem;
            } else {
//				popWin.CfContent.kfbz="1";
            }
            popWin.CfContent["brlx"] = toolBar.yfContent.hzlx;
            popWin.CfContent["pybz"] = popWin.yfContent["pybz"];
            popWin.CfContent["cflx"] = popWin.yfContent["cflx"];
            popWin.CfContent["yfbm"] = toolBar.yfContent["yfbm"];
            //获取处方类型名称
            popWin.CfContent['cflxmc'] = popWin.listGetName(popWin.cflxList, popWin.CfContent["cflx"], 'cflxbm', 'cflxmc');
            var cf = JSON.stringify(popWin.CfContent); //处方
            var pf = JSON.stringify(tablePfInfo.jsonList); //配方
            var json = '{"list" : [{"cf": ' + cf + ',"pf": ' + pf + '}]}';
            console.log(json);
            if(toolBar.yfContent.N04003002200117 == '1' && !popWin.CfContent.jzys){
                this.postAjax('/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=update',JSON.stringify({jzys:popWin.CfContent.cfys,ghxh:popWin.CfContent.ghxh,yljgbm:jgbm}),function (json) {
                        if(json.a== '0'){
                            malert(json.c)
                        }
                })
            }

            common.openloading('.hzList');
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCfhj&types=save', json).then(function(data) {
                common.closeLoading();
                popWin.ifClick = true;
                if(data.body.a == 0) {
                        toolBar.cfhObj=data.body.d;
                        var cfzyh=popWin.CfContent.bah;
                        var zyys=popWin.CfContent.zyys;
                        var zyysxm=popWin.CfContent.zyysxm;
                        var zyks=popWin.CfContent.ryks;
                        var zyksmc=popWin.CfContent.ryksmc;
                        console.log(popWin.CfContent);
                        popWin.ifClick = true;
                        malert("数据更新成功",'top','success');
                        if(toolBar.yfContent.hzlx == "1"){
                            setTimeout(function () {
                                toolBar.getWsfcf(cfzyh,zyys,zyysxm,zyks,zyksmc);
                                toolBar.saveHjCfFyData();
                                tableInfo.getData();
                                popWin.PfContent = {};
                            }, 400);
                        }else{
                            tableInfo.getData();
                            popWin.PfContent = {};
                        }
                        document.getElementById("bah").focus();
                    } else {
                    	popWin.ifClick = true;
                        malert(data.body.c,'top','defeadted');
                    }
                }, function(error) {
                    popWin.ifClick = true;
                    common.closeLoading();
                    console.log(error);
                });
        },
        deleteData: function() {},
        click: function() {},
        //针对住院处方划价自动生成费用
        saveHjCfFyData: function () {
            if (toolBar.brfyjsonList.length == 0) {
                return;
            }
            var json = '{"list":' + JSON.stringify(toolBar.brfyjsonList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=ZyglFyglBrfy&types=save&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert("上传数据成功",'top','success');
                    toolBar.ifClick = true;
                } else {
                    malert("上传数据失败:"+data.body.c,'top','defeadted');
                    toolBar.ifClick = true;
                }
            }, function (error) {
                console.log(error);
            });
        },
        //获取未收费处方（针对住院）
        getWsfcf:function(zyh,zyys,zyysxm,zyks,zyksmc){
//	            $("#sfsj").val(getTodayDate());//获取当前时间
            if (!zyh) {
                malert("住院号不能为空", 'top', 'defeadted');
                return;
            }
            this.param.rows = 2000;
            this.param.zyh = zyh;
            this.param.zyks = zyks;
            this.param.zyksmc = zyksmc;
            this.param.zyys = zyys;
            this.param.zyysxm = zyysxm;
            this.param.brid = popWin.CfContent['brid'];
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglFyglBrfy&types=queryWxfy&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == 0) {
                    toolBar.brfyjsonList = json.d;
                    for(var i=0;i<toolBar.brfyjsonList.length;i++){
                        toolBar.brfyjsonList[i].brid=popWin.CfContent['brid'];
                        toolBar.brfyjsonList[i].yfbm=popWin.yfContent["yfbm"];
                        toolBar.brfyjsonList[i].zyks=zyks;
                        toolBar.brfyjsonList[i].zyksmc=zyksmc;
                        toolBar.brfyjsonList[i].zyys=zyys;
                        toolBar.brfyjsonList[i].zyysxm=zyysxm;
                        toolBar.brfyjsonList[i].sfrq =toolBar.fDate(new Date(),'date');
                        toolBar.brfyjsonList[i].yfbm=toolBar.yfContent["yfbm"];
                        toolBar.brfyjsonList[i].yfmc=toolBar.yfContent["yfmc"];
                        // toolBar.brfyjsonList[i].yfbm=toolBar.yfContent["yfbm"];
                        // toolBar.brfyjsonList[i].zyks=zyks;
                        // toolBar.brfyjsonList[i].zyksmc=zyksmc;
                        // toolBar.brfyjsonList[i].zyys=zyys;
                        // toolBar.brfyjsonList[i].zyysxm=zyysxm;
                    }
                    toolBar.saveHjCfFyData();
                    //此处将费用金额累加起来
                } else {
                    malert("费用查询失败:"+data.body.c,'top','defeadted');
                }
            });
        },
        //获取药房
        getYfbm: function() {
            parm = {
                "ylbm": ylbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(parm), function(json) {
                if(json.a == 0) {
                    toolBar.jsonList = json.d.list;
                    if(toolBar.jsonList.length > 0) {
                        //						Vue.set(toolBar.yfContent, 'yfbm', toolBar.jsonList[0].yfbm);
                        Vue.set(popWin.yfContent, 'cflx', toolBar.jsonList[0].cflx);
                        toolBar.yfContent.yfbm = toolBar.jsonList[0].yfbm;//默认加载第一个
                        popWin.CflxJosn = jsonFilter(popWin.cflxList, "yfbm", toolBar.yfContent.yfbm);
                        ksbm = toolBar.jsonList[0].ksbm;
                        toolBar.getCsqx();
                    }
                } else {
                    //alert("药房编码获取失败：" + json.c);
                }
            });
        },
        //查询用例科室参数权限
        getCsqx: function() {
            if(!ksbm) {
                malert("科室编码无效!",'top','defeadted');
                return;
            }
            parm = {
                "ylbm": ylbm,
                "ksbm": ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function(json) {
                if(json.a == 0) {
                    popWin.PfContent = {};
                    var mzcflx = null;
                    var zycflx = null;
                    for(var i = 0; i < json.d.length; i++) {
                        var csz = json.d[i].csz;
                        //默认药房
                        if(json.d[i].csqxbm == "N04003002200101") {
                            if(csz) {
                                Vue.set(toolBar.yfContent, 'yfbm', csz);
                                //ksbm = json.d[i].ksbm;
                            }
                        }
                        //默认门诊处方类型
                        if(json.d[i].csqxbm == "N04003002200105") {
                            if(csz) {
                                Vue.set(toolBar.yfContent, 'cflxbm', csz);
                                mzcflx = csz;
                            }
                        }
                        //默认住院处方类型
                        if(json.d[i].csqxbm == "N04003002200104") {
                            if(csz) {
                                zycflx = csz;
                                Vue.set(toolBar.yfContent, 'cflxbm', csz);
                            }
                        }
                        //默认患者类型
                        if(json.d[i].csqxbm == "N04003002200113") {
                            if(csz == "0" || csz == "1") {
                                hzlx = csz;
                                Vue.set(toolBar.yfContent, 'hzlx', csz);
                            }
                        }
                        //病人类型切换方式  0-每次自动默认  1-默认上一次
                        if(json.d[i].csqxbm == "N04003002200110") {
                            if(csz == "0" || csz == "1") {
                                hzlxqhfs = csz;
                            }
                        }
                        //保存快捷键
                        if(json.d[i].csqxbm == "N04003002200108") {
                            if(csz == "1") {
                                bckjj = 112;
                            }
                        }
                        if(json.d[i].csqxbm == "N04003002200117") { //0 否有 1 是
                            if(csz == "1") {
                                Vue.set(toolBar.yfContent, 'N04003002200117', csz);
                            }
                        }
                    }
                    popWin.CflxJosn = jsonFilter(popWin.cflxList, "yfbm", toolBar.yfContent.yfbm);
                    //根据患者类型设置处方类型
                    if(hzlx == "0") {
                        Vue.set(popWin.yfContent, 'cflx', mzcflx);
                        Vue.set(popWin.yfContent, 'cflxbm', mzcflx);
                    } else {
                        Vue.set(popWin.yfContent, 'cflx', zycflx);
                        Vue.set(popWin.yfContent, 'cflxbm', zycflx);
                    }
                    tableInfo.getData();
                    toolBar.newData();
                } else {
                    malert("科室权限获取失败:"+json.c,'top','defeadted')
                }
            });
        },
        //获取处方类型
        // getCflx: function() {
        //     var cflx_dg = {
        //         page: 1,
        //         rows: 100,
        //         sort: "cflxbm",
        //         order: "asc",
        //         parm: ""
        //     };
        //     $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmCflx&types=query&dg=' + JSON.stringify(cflx_dg), function(data) {
        //         if(data.a == 0) {
        //             popWin.cflxList = data.d.list;
        //         } else {
        //         }
        //     });
        // }
    }
});

//处方列表
var tableInfo = new Vue({
    el: '#cfhjList',
    //混合js字典庫
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        jsonList: [],
        param: {
            page: 1,
            rows: 20,
            sort: 'cfh',
            order: 'asc'
        },
        totlePage: 0,
        index: 1,
        isChecked: [],
        isCheckAll: false,
        testVal: {},
        cfKssj:getTodayDateBegin(),
        cfJssj:getTodayDateEnd(),
        cfListSum : 0,
        cfListTotal : 0,
        parm:''
    },
    methods: {

        //获取处方列表
        getData: function() {
            var cf_param = {
                page: this.param.page,
                rows: 9999,
                sort: 'cfh',
                order: 'desc'
            };
            popWin.PfContent = {};
            //			popWin.CfContent = {};
            tablePfInfo.jsonList = [];
            parm = {
                "yfbm": toolBar.yfContent.yfbm,
                'sfdzcf': '0',
                'beginrq':this.cfKssj,
                'endrq':this.cfJssj,
                'parm':this.parm,
            };
            if(parm.yfbm == undefined) {
                return;
            }
            //如果有分页请用jq的api,一般情况下避免冲突请用vue-resource,具体参照index.js的事例
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCfhj&types=queryypcf&dg=" + JSON.stringify(cf_param) + "&parm=" + JSON.stringify(parm), function(json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if(json.a == 0) {
                    tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                    tableInfo.totlePage = tableInfo.totlePage == 0 ? 1 : tableInfo.totlePage;
                    tableInfo.jsonList = json.d.list;
                    var cfListSum = 0;
                    if(tableInfo.jsonList && tableInfo.jsonList.length>0){
                        for(var i=0;i<tableInfo.jsonList.length;i++){
                            cfListSum += tableInfo.jsonList[i].cfje;
                        }
                        tableInfo.cfListSum = tableInfo.fDec(cfListSum,2);
                        tableInfo.cfListTotal = json.d.total;
                    }
                }
            });
            $(".selectGroup").hide(); //隐藏下拉框
        },
        //双击进行修改
        edit: function(num) {
            if(num == null) {
                for(var i = 0; i < this.isChecked.length; i++) {
                    if(this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if(num == null) {
                    malert("请选中你要修改的数据",'top','defeadted')
                    return false;
                }
            }
            //这里要拷贝值到popContent中，不能直接= 扣费或发药处方不允许修改
            popWin.CfContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            popWin.CfContent.nldw = popWin.CfContent.brnldw;
            //检索配方
            var cfh = this.jsonList[num].cfh;
            tablePfInfo.getData(cfh);
            popWin.yfContent.kfbz = this.jsonList[num].kfbz;
            if(popWin.CfContent.kfbz == "1") {
                Vue.set(toolBar.yfContent, 'kfbz', "1");
                popWin.readonly = true;
            } else {
                Vue.set(toolBar.yfContent, 'kfbz', "0");
                popWin.readonly = false;
            }
        }
    }
});

//配方列表
var tablePfInfo = new Vue({
    el: '#pfhjList',
    //混合js字典庫
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        jsonList: [],
        param: {
            page: 1,
            rows: 20,
            sort: 'mxxh',
            order: 'asc'
        },
        totlePage: 0,
        isChecked: [],
        isCheckAll: false,
        testVal: {}
    },
    computed:{
        sum:function () {
            var sum=0;
            for(var i=0;i<this.jsonList.length;i++){
                sum+= parseFloat(this.jsonList[i].cfyl)*parseFloat(this.jsonList[i].yplj);
            }
            return this.fDec(sum, 2);
        }
    },
    updated:function(){
        changeWin()
    },
    methods: {
        //配方列表删除一行
        delLine: function(num) {
            this.jsonList.splice(num, 1);
        },
        //获取处方列表
        getData: function(cfh) {
            if(cfh == undefined) {
                return;
            }
            //如果有分页请用jq的api,一般情况下避免冲突请用vue-resource,具体参照index.js的事例
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCfhj&types=queryyppf&dg=" + JSON.stringify(tablePfInfo.param) + '&parm={"cfh":"' + cfh + '"}', function(json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if(json.a == 0) {
                    tablePfInfo.totlePage = Math.ceil(json.d.total / tablePfInfo.param.rows);
                    tablePfInfo.jsonList = json.d.list;
                } else {
                    //						alert("getdata查询失败：" + json.c)
                }
            });
        },

        //双击进行修改
        edit: function(num) {
            if(num == null) {
                for(var i = 0; i < this.isChecked.length; i++) {
                    if(this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if(num == null) {
                    malert("请选中你要修改的数据",'top','defeadted');
                    return false;
                }
            }
            //这里要拷贝值到popContent中，不能直接=
            popWin.PfContent = this.jsonList[num];
            popWin.CfContent["pybz"] = popWin.yfContent["pybz"];
            popWin.CfContent["cflx"] = popWin.yfContent["cflx"];
            popWin.PfContent.text = popWin.PfContent.ypmc;
            if(popWin.CfContent.kfbz == "1" || popWin.CfContent.fybz == "1") {
                popWin.readonly = true;
                malert("已扣费处方不允许修改配方!",'top','defeadted')
            } else {
                popWin.readonly = false;
            }
        },
        remove: function() {
            var cfhList = [];
            for(var i = 0; i < this.isChecked.length; i++) {
                if(this.isChecked[i] == true) {
                    var cf = {};
                    cf.cfh = this.jsonList[i].cfh
                    cfhList.push(cf);
                }
            }
            if(cfhList.length == 0) {
                malert("请选中您要删除的数据",'top','defeadted');
                return false;
            }
            var json = '{"list":' + JSON.stringify(cfhList) + '}'
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCfhj&types=deletecf&',
                json).then(function(data) {
                tableInfo.getData();
                if(data.body.a == 0) {
                    malert("删除成功",'top','success');
                } else {
                    malert("删除失败",'top','defeadted');
                }
            }, function(error) {
                console.log(error);
            });
        },
        searchclose: function() {
            $(".selectGroup").hide();
        }

    }
});

var them = {
    '统筹类别': 'ybtclbmc',
    '农保类别': 'nbtclbmc',
    '药品名称': 'ypmc',
    '药品规格': 'ypgg',
    '库存数量': 'kcsl',
    '实际库存': 'sjkc',
    '单位': 'yfdw',
    '均价': 'yplj',
    '分装比例': 'fzbl',
    '剂型': 'jxmc',
    '拼音代码 ': 'pydm',
    '化学名称': 'hxmc',
    '化学名代码': 'hxmcdm'
};
var mzthem = {
    '姓名': 'brxm',
    '挂号序号': 'bah',
    '性别': 'brxb',
    '年龄': 'brnl',
    '就诊科室': 'brksmc',
    '接诊医生': 'cfysxm'
};
var zythem = {
    '姓名': 'brxm',
    '住院号': 'bah',
    '性别': 'brxb',
    '年龄': 'brnl',
    '住院科室': 'brksmc',
    '住院医生': 'cfysxm'
};
//编辑列表框   popWin.cflxList
var popWin = new Vue({
    el: '#cfhjEdit',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    components: {
        'search-table': searchTable,
        'hzsearch-table': searchTable
    },
    data: {
        types: '', //分页查询类型
        PfContent: {},
        CfContent: {},
        yfContent: {},
        ysbmList: [], //医生
        ksbmList: [], //科室
        ypdwList: [], //药品单位
        tclbList: [], //统筹类别
        brfbList: [], //病人费别
        ypList: [], //药品
        //cflxList:[{"cflxbm":"01","cflxmc":"西药处方"},{"cflxbm":"02","cflxmc":"中药处方"},{"cflxbm":"02","cflxmc":"成药处方"}],//处方类型
        cflxList: [], //处方类型
        CflxJosn: [], //处方类型
        yfbmList: [],
        dg: {
            page: 1,
            rows: 20,
            sort: "",
            order: "asc",
            parm: ""
        }, //分页信息
        json: {}, //实体参数信息
        readonly: false, //判断控件是否可用
        selSearch: -1,
        //患者
        hzsearchCon: [],
        hztotal: null,
        hzthem: mzthem,
        //药品
        searchCon: [],
        total: 0,
        them: them,
        them_tran: {
            'brxb': dic_transform.data.brxb_tran
        },
        ifClick:true
    },
    methods: {
        resultChangeData:function(val){
            Vue.set(this[val[2][0]],val[2][[1]],val[0])
            Vue.set(popWin.yfContent, 'cflx', val[0]);
            // Vue.set(popWin.yfContent, 'cflx', this.cflxList[val[5]]['cflxbm']);
            this.nextFocus(val[1])
        },
        //双击患者信息
        dblclickbah: function(item) {
            //分页查询
            if(item == null) {
                popWin.dg.page++;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=' + popWin.types +
                    '&dg=' + JSON.stringify(popWin.dg) + '&json=' + JSON.stringify(popWin.json),
                    function(data) {
                        if(data.a == "0") { //成功
                            if(data.d.list.length > 0) {
                                for(var i = 0; i < data.d.list.length; i++) {
                                    popWin.hzsearchCon.push(data.d.list[i]);
                                }
                            }
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
                return;
            }

            this.CfContent = item;
            $(".selectGroup").hide();
            document.getElementById("ypmc").focus();
        },
        //双击药品

        dblclickypmc: function(item) {
            //分页查询
            if(item == null) {
                //分页信息
                this.dg.page++;
               this.change('',true,'ypmc',this.PfContent.text)
            }
            this.PfContent = item;
            this.PfContent.text = this.PfContent.ypmc;
            $(".selectGroup").hide();
            document.getElementById("sl").focus();
        },

        changeDown: function(event, type, content, searchCon) {
            if(searchCon && content){
                if(!this[searchCon][this.selSearch]) return;
            }
            this.keyCodeFunction(event, content, searchCon);
            if(event.keyCode == 13) {
                if(type == "bah") {
                    this.nextFocus(event,5)
                    // document.getElementById('ypmc').focus();
                } else if(type == "ypmc") {
                    this.PfContent.text = this.PfContent.ypmc;
                    document.getElementById('sl').focus();
                }
            }
        },
        //值改变时触发
        change: function(add,event, type, val) {
            if(!add)this.dg.page = 1
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if(type == "ypmc") { //药品下接框
                this.PfContent["text"] = val;
                popWin.dg.parm = encodeURIComponent(val);
                popWin.json.yfbm = toolBar.yfContent.yfbm;
                popWin.json.cflx = this.yfContent["cflx"];
                popWin.json.ksbm = ksbm;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc' + '&dg=' + JSON.stringify(popWin.dg) + '&json=' + JSON.stringify(popWin.json), function(data) {
                        if(data.a == "0") { //成功
                            if(add){
                                popWin.searchCon.push(data.d.list)
                            }else{
                                popWin.searchCon = data.d.list;
                            }
                            popWin.dg.total = data.d.total;
                            popWin.selSearch = 0;
                            if(data.d.list.length != 0) {
                                $(".selectGroup").hide();
                                _searchEvent.show()
                            }
                        } else {
                            malert("药品检索失败:"+ data.c,'top','defeadted');
                        }
                    });
            }
            else if(type == "bah") { //患者信息下拉框
                popWin.types = "";
                this.CfContent[type] = val;
                    this.dg.parm = this.CfContent[type];
                if(toolBar.yfContent.hzlx == "0") {
                    this.dg.sort = "ghxh";
                    popWin.types = "brgh";
                    popWin.hzthem = mzthem;
                } else {
                    this.dg.sort = "zyh";
                    popWin.hzthem = zythem;
                    popWin.types = "rydj";
                }
                //分页参数
                popWin.dg.page = 1;
                popWin.dg.rows = 5;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=' + popWin.types +
                    '&dg=' + JSON.stringify(popWin.dg) + '&json=' + JSON.stringify(popWin.json),
                    function(data) {
                        if(data.a == "0") { //成功
                            popWin.hzsearchCon = data.d.list;
                            popWin.hztotal = data.d.total;
                            popWin.selSearch = 0;
                            if(data.d.list.length > 0) {
                                if(toolBar.yfContent.hzlx == "0") {
                                    //处理列名不一致问题
                                    for(var int = 0; int < popWin.hzsearchCon.length; int++) {
                                        popWin.hzsearchCon[int].bah = popWin.hzsearchCon[int].ghxh;
                                        popWin.hzsearchCon[int].brks = popWin.hzsearchCon[int].ghks;
                                        popWin.hzsearchCon[int].brksmc = popWin.hzsearchCon[int].ghksmc;
                                        popWin.hzsearchCon[int].cfys = popWin.hzsearchCon[int].jzys;
                                        popWin.hzsearchCon[int].cfysxm = popWin.hzsearchCon[int].jzysxm;
                                    }
                                } else {
                                    for(var int = 0; int < popWin.hzsearchCon.length; int++) {
                                        popWin.hzsearchCon[int].bah = popWin.hzsearchCon[int].zyh;
                                        popWin.hzsearchCon[int].brks = popWin.hzsearchCon[int].ryks;
                                        popWin.hzsearchCon[int].brksmc = popWin.hzsearchCon[int].ryksmc;
                                        popWin.hzsearchCon[int].cfys = popWin.hzsearchCon[int].zyys;
                                        popWin.hzsearchCon[int].cfysxm = popWin.hzsearchCon[int].zyysxm;
                                        popWin.hzsearchCon[int].brnl = popWin.hzsearchCon[int].nl;
                                    }
                                }
                                if(data.d.list.length != 0) {
                                    $(".selectGroup").hide();
                                    _searchEvent.show()
                                }
                            } else {
                                malert("未找到匹配的患者",'top','defeadted');
                            }
                        } else {
                            malert("患者信息检索失败:" + data.c,'top','defeadted');
                        }
                    });
            }
        },
        //插入一行
        addData: function() {
                toolBar.addData();
        },
        //下拉框加载科室
        ksbmselect: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify({
                page: 1,
                rows: 100,
                sort: 'ksbm',
                order: 'asc'
            }), function(json) {
                popWin.ksbmList = json.d.list;
            });
        },
        //下拉框加载人员
        ysbmselect: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json="+JSON.stringify({ysbz:'1'})+"&dg=" + JSON.stringify({
                page: 1,
                rows: 1000,
                sort: 'rybm',
                order: 'asc'
            }), function(json) {
                popWin.ysbmList = json.d.list;
            });
        },
        //下拉框加载药品单位
        ypdwselect: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ypdw&dg=" + JSON.stringify({
                page: 1,
                rows: 100,
                sort: 'jldwbm',
                order: 'asc'
            }), function(json) {
                popWin.ypdwList = json.d.list;
            });
        },
        //下拉框加载统筹类别
        tclbselect: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yptclb&dg=" + JSON.stringify({
                page: 1,
                rows: 20,
                sort: 'tclbbm',
                order: 'asc'
            }), function(json) {
                popWin.tclbList = json.d.list;
            });
        },
        //下拉框加载处方类型
        cflxselect: function() {
            var cflx_dg = {
                page: 1,
                rows: 100,
                sort: "cflxbm",
                order: "asc",
                parm: ""
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmYfyycflx&types=query&dg=' + JSON.stringify(cflx_dg), function(data) {
                if(data.a == 0) {
                    popWin.cflxList = data.d.list;
                } else {
                    malert("获取处方类型失败:" + data.c,'top','defeadted');
                }
            });
        },
        //下拉框病人费别
        brfbselect: function() {
            var brfb_dg = {
                page: 1,
                rows: 100,
                sort: "fbbm",
                order: "asc",
                parm: ""
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmBrfb&types=query&dg=' + JSON.stringify(brfb_dg), function(data) {
                if(data.a == 0) {
                    popWin.brfbList = data.d.list;
                } else {
                    malert("获取病人费别失败:" + data.c,'top','defeadted');
                }
            });
        }
    }
});



//下拉框
popWin.ksbmselect(); //检索科室下拉框
popWin.ysbmselect(); //检索医生下拉框
popWin.ypdwselect(); //检索药品单位下拉框
popWin.tclbselect(); //检索统筹类别下拉框
popWin.cflxselect(); //处方类型
popWin.brfbselect(); //病人费别下拉框

//默认为门诊划价
//toolBar.hzlx = "门诊划价";
//获取药房编码
toolBar.getYfbm();

window.onkeydown = function(e) {
    //F8切换门诊住院 类型
    if(e.keyCode == 119) {
        if(toolBar.yfContent.hzlx == "0") {
            toolBar.yfContent.hzlx = "1"
        } else {
            toolBar.yfContent.hzlx = "0";
        }
        $(".selectGroup").hide(); //隐藏下拉框
        toolBar.newData(); //新增处方
    }
    //F1保存
    if(e.keyCode == 112) {
        toolBar.saveData();
    }
}


//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol) {
        $(".selectGroup").hide();
    }

});

laydate.render({
    elem: '#cfKssj',
    rigger: 'click',
    theme: '#1ab394',
    type: 'datetime',
    done: function (value, data) {
        tableInfo.cfKssj = value;
        tableInfo.getData();
    }
});
laydate.render({
    elem: '#cfJssj',
    rigger: 'click',
    theme: '#1ab394',
    type: 'datetime',
    done: function (value, data) {
        tableInfo.cfJssj = value;
        tableInfo.getData();
    }
});
