//有卡身份认证回调
function read_card_backFun(outResult) {
    //将查询出的数据与his住院数据对象合并
    if (outResult) {
        Object.assign(lxzdwyb.lxzdwybBrxxContent, outResult);
        console.log(JSON.stringify(lxzdwyb.lxzdwybBrxxContent));
        if(!lxzdwyb.lxzdwybBrxxContent.grbh){
            lxzdwyb.lxzdwybBrxxContent.grbh = outResult.grbh;
            lxzdwyb.lxzdwybBrxxContent.sbjgbh = outResult.sbjgbh;
        }
        if(outResult.mzdbjbs){
            lxzdwyb.jbList = [];
            var jbArr = outResult.mzdbjbs.split("/");
            for (var i = 0; i < jbArr.length; i++) {
                var jb = jbArr[i].split("#m");
                var jbC = {
                    jbmc:jb[0],
                    jbbm:jb[1],
                };
                lxzdwyb.jbList.push(jbC);
                if(i==0){
                    lxzdwyb.jbContent.jbbm = jb[0];
                    Vue.set(lxzdwyb.lxzdwybBrxxContent, 'jbbm', jb[1]);
                    Vue.set(lxzdwyb.lxzdwybBrxxContent, 'jbmc', jb[0]);
                }
            }
        }
        lxzdwyb.$forceUpdate();
        malert("身份信息获取成功!");
    } else {
        malert("身份信息获取失败,未获取到相关医保人员信息!", "top", "defeadted");
        return;
    }
}

//无卡身份认证回调
function query_person_info_backFun(outResult) {
    //将查询出的数据与his住院数据对象合并
    if (outResult) {
        Object.assign(lxzdwyb.lxzdwybBrxxContent, outResult);
        malert("身份信息获取成功!");
    } else {
        malert("身份信息获取失败,未获取到相关医保人员信息!", "top", "defeadted");
        return;
    }
}

function init_mz_backFun(outResult){
    Object.assign(lxzdwyb.lxzdwybBrxxContent, outResult);
    lxzdwyb.lxzdwybBrxxContent.mzghbh = lxzdwyb.lxzdwybBrxxContent.jshid;//这里是挂号缴费，将结算号存入挂号序号
    $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + contextInfo.lxzBxurl + "&bxlbbm=" + contextInfo.lxzBxlbbm + "&types=mzyw&method=mzdj&parm="
        + encodeURIComponent(JSON.stringify(lxzdwyb.lxzdwybBrxxContent)),
        function (data) {
            if (data.a == '0') {
                contextInfo.lxzdwybContent = lxzdwyb.lxzdwybBrxxContent;
                malert("门诊登记成功!");
                contextInfo.bxShow = false;//登记成功，关闭弹窗
                contextInfo.bxbz = '1';
                lxzdwyb.ifClick = true;

                //将相关信息引入到挂号页面
                contextInfo.json.brxm = contextInfo.lxzdwybContent.xm;
                contextInfo.json.brxb = contextInfo.lxzdwybContent.xb;
                contextInfo.json.sfzjhm = contextInfo.lxzdwybContent.sfzhm;
                contextInfo.setAge();
            } else {
                malert(data.c, "top", "defeadted");
                return;
            }
        });
}

//费用上传
function put_fymx_backFun(outResult){
    lxzdwyb.requestParameters = {};
    lxzdwyb.requestParameters.settle_mz_pre = {};
    socket.send(JSON.stringify(lxzdwyb.requestParameters));
}

function settle_mz_pre_backFun(outResult){
    contextInfo.yjsContentLxzdwyb = Object.assign({},contextInfo.yjsContentLxzdwyb, JSON.parse(outResult));
    contextInfo.outpId = contextInfo.yjsContentLxzdwyb.jshid;
    lxzdwyb.lxzdwMzjs();
}

function settle_mz_real_backFun(outResult){
    outResult = JSON.parse(outResult);
    outResult.ghxh = lxzdwyb.lxzdwybBrxxContent.mzghbh;
    contextInfo.yjsContentLxzdwyb = {};
    $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + contextInfo.lxzBxurl + "&bxlbbm=" + contextInfo.lxzBxlbbm + "&types=mzyw&method=mzjs&parm="
        + JSON.stringify(outResult),function (json) {
        if (json.a == '0') {
            malert(json.c);

            var ybkzf = 0;

            if(outResult){
                if(outResult.grzhzf){
                    ybkzf += outResult.grzhzf;
                }
                if(outResult.ylbzje){
                    ybkzf +=outResult.ylbzje;
                }
                if(outResult.tczf){
                    ybkzf += outResult.tczf;
                }
                if(outResult.dezf){
                    ybkzf += outResult.dezf;
                }
                if(outResult.desybx){
                    ybkzf += outResult.desybx;
                }
                if(outResult.gwybz){
                    ybkzf += outResult.gwybz;
                }
                if(outResult.czlz){
                    ybkzf += outResult.czlz;
                }
                if(outResult.zhzf){
                    ybkzf += outResult.zhzf;
                }
                if(outResult.yljmje){
                    ybkzf += outResult.yljmje;
                }
                if(outResult.qttczf){
                    ybkzf += outResult.qttczf;
                }
                if(outResult.qzjbzhzf){
                    ybkzf += outResult.qzjbzhzf;
                }
                if(outResult.dbbzje){
                    ybkzf += outResult.dbbzje;
                }
            }
            contextInfo.bcfy = contextInfo.fDec(ybkzf, 2);

            //调用挂号，打印票据等
            conBtu.doSaveBrgh();

            common.closeLoading();
        } else {
            malert(json.c,"top","defeadted");
            common.closeLoading();
        }
    });
}
function destroy_mz_backFun(){
    malert("医保取消结算！");
}
