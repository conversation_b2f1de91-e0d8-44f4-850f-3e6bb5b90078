var wscjl = new Vue({
    el: '#wscjl',
    mixins: [dic_transform, baseFunc,tableBase, mConfirm],
    data: {
        ifsc:true,
        ifClick:true,
        ifQxClick:true,
        wscjlList:[],
        qxscList:[],
        isChecked:[],
        scfyList:[],
        zyhList:[],
        popContent:{},

    },
    methods: {
        scfy:function(){
            common.openloading();
            if (!wscjl.ifClick) {
                malert("已经点击上传，请勿重复操作！","top","defeadted");
                return;
            } //如果为false表示已经点击了不能再点
            wscjl.ifClick = false;
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    wscjl.scfyList.push(JSON.parse(JSON.stringify(this.wscjlList[i])));
                }
            }
            if(wscjl.scfyList.length<=0){
                malert("无可上传的费用！","top","defeadted");
                wscjl.ifClick = true;
                return;
            }
            var postParm = '{"list":' + JSON.stringify(wscjl.scfyList) + '}';
            this.$http.post("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + fyxmTab.bxurl + "&bxlbbm=" + fyxmTab.bxlbbm + "&types=inHospital&method=uploadInpatientDetails",
                postParm).then(function (json) {
                if(json.body.a == 0) {
                        sjsc_left.getWscfyByZyh();
                        malert("费用上传成功！",'top','success');
                        wscjl.ifClick = true;
                        this.isChecked = false;
                    } else {
                        malert(json.body.c,"top","defeadted");
                        wscjl.ifClick = true;
                    }
                wscjl.scfyList = [];
                common.closeLoading();
            }, function (error) {
                malert(json.c,"top","defeadted");
                wscjl.scfyList = [];
                common.closeLoading();
            });
        },
        //全部费用上传
        scfyAll: function(){

        },
        qxscfy: function(){
            if (!wscjl.ifQxClick) return; //如果为false表示已经点击了不能再点
            wscjl.ifQxClick = false;
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    wscjl.qxscList.push(JSON.parse(JSON.stringify(this.wscjlList[i])));
                }
            }
            if(wscjl.qxscList.length <= 0){
                malert("请选择需要取消上传的费用项目！","top","defeadted");
                wscjl.ifQxClick = true;
                return;
            }
            var postParmQx = '{"list":' + JSON.stringify(wscjl.qxscList) + '}';
            common.openloading("#wscjl");
            this.$http.post("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + fyxmTab.bxurl + "&bxlbbm=" + fyxmTab.bxlbbm + "&types=inHospital&method=inpSingleCancelFee",
                postParmQx).then(function (json) {
                if(json.body.a == 0) {
                    common.closeLoading();
                    sjsc_left.getWscfyByZyh();
                    malert("取消上传费用成功！","top","defeadted");
                    wscjl.ifQxClick = true;
                    this.isChecked = false;
                } else {
                    common.closeLoading();
                    malert(json.body.c);
                    wscjl.ifQxClick = true;
                }
                wscjl.qxscList = [];
            }, function (error) {
                common.closeLoading();
                malert(json.c);
                wscjl.qxscList = [];
            });
        },
        //取消全部（按人）
        qxscfyAll: function(){
            if(sjsc_left.zyhList.length<=0){
                malert('请先选择要操作的病人！',"top","defeadted");
                return;
            }
        },
        refresh:function(){
            sjsc_left.getWscfyByZyh();
        },
        getData:function(){
            sjsc_left.getWscfyByZyh();
        },
        clickCheckBox:function(){
            if(sjsc_left.ifsc == "1"){
                sjsc_left.ifsc = "";
                wscjl.ifsc = true;
            }else{
                sjsc_left.ifsc = "1";
                if( wscjl.ifsc){
                    wscjl.ifsc = false;
                }else{
                    wscjl.ifsc = true;
                }
            }
            sjsc_left.getWscfyByZyh();
        }
    }
});
