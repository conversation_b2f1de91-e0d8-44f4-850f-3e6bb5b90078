var cd_014gj = new Vue({
    el: '.cd_014gj',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        skr_tran: {
            '本人': '本人',
            '父母': '父母',
            '子女': '子女',
            '配偶': '配偶',
            '同胞兄弟姐妹': '同胞兄弟姐妹',
            '外祖父母': '外祖父母',
        },
        isShow: false,
        bxlbbm: null,
        bxurl: null,
        birthday: null,
        text: null,
        jbContent: {},
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'yke120',
            '疾病名称': 'yke121',
            '副编码': 'yke223'
        },
        brzt_tran: {
            '1': '在院',
            '2': '未在院'
        },
        zdxxJson: {},
        grxxJson: {
            aka130: "0201",
            relation:""
        },
        cssg: false,
        userInfo: {},
        ifclick: true,
        cd_ykc117_tran: {
            '1': '公务员',
            '0': '非公务员 ',
        },
        cdydbz_tran: {
            '1': '本地',
            '2': '省内异地 ',
            '3': '省外异地'
        },
        cd_aka130_tran: {
            '0101': '药店购药',
            '0102': '城居药店支付',
            '0201': '普通门诊',
            '0202': '特殊疾病门诊',
            '0203': '城居普通门诊',
            '0204': '城居门诊特殊病',
            '0205': '城职门诊统筹',
            '0206': '城乡门诊统筹',
            '0207': '酪氨酸城职',
            '0208': '酪氨酸城乡',
            '0209': '大学生门诊',
            '0301': '普通住院',
            '0302': '家庭病床',
            '0304': '市外转诊住院(异地急救参照市外转诊)',
            '0305': '统筹区内转院',
            '0306': '外伤住院',
            '0307': '异地抢救住院(手工报销使用)',
            '0308': '本市非定点抢救住院(手工报销使用)',
            '0309': '城居普通住院',
            '0310': '城居外伤住院',
            '0311': '城居市外转诊',
            '0401': '机关事业单位生育报销',
            '0501': '工伤门诊',
            '0502': '工伤住院',
            '0504': '工伤康复治疗住院',
            '0602': '城居生育分娩住院',
            '0603': '城居产前检查',
            '0604': '城职生育住院',
            '13': '异地门特（就医地）',
        },
		cd_relation_tran:{
			'1':'父母',
			'2':'配偶',
			'3':'子女',
		},
		insutype_tran:{
			'310':'职工基本医疗保险',
			'320':'公务员医疗补助',
			'330':'大额医疗费用补助',
			'340':'离休人员医疗保障',
			'390':'城乡居民基本医疗保险',
			'392':'城乡居民大病医疗保险',
			'510':'生育保险',
		},
        gsdataset_tran: [],
        fylist: [],
        datasetyka026_tran: [],
        gsdataset_show: false,
        datasetyka026_show: false,
        lxxx_show: false,
        ryfbmc: '',
        isdzps: false,//是否电子医保凭证
        pushResult: false,//单子医保凭证推送结果
        yinHaiRequest: {//用于声明银海请求对象，与字段对应关系
            //门诊交易（11）
            
        },
		dtxx:false,
		idetinfo:[],
		isZxChecked:[],
		isZxCheckAll:false,
		syxx:null,
		sfydList:{
			'0':'否',
			'1':'是'
		},
		sfyd:'0',
        gjjsxx:null,
    },
    created: function () {
        this.init();
    },
    mounted: function () {
        this.isShow = true;
        this.getbxlb();
        this.getUserInfo();
    },
    methods: {
        init: function () {
            window.insuranceGbUtils.gjinit();
        },
        closeGz_002: function () {
            this.isShow = false;
            $("#hyjl").html("");
        },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    cd_014gj.userInfo = json.body.d;
                });
        },
        getbxlb: function () {
            var that = this;
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    that.bxlbbm = json.d.list[0].bxlbbm;
                    that.bxurl = json.d.list[0].url;
                } else {
                    malert("保险类别查询失败!" + json.c, 'right', 'defeadted')
                }
            });
        },
        commonResultChange: function (val) {
            var type = val[2][1];
            switch (type) {
                case "yke109":
                    Vue.set(this.grxxJson, type, val[0]);
                    Vue.set(this.grxxJson, "alc022", val[4]);
                    Vue.set(this.grxxJson, "aka130", cd_014gj.listGetName(cd_014gj.gsdataset_tran, val[0], 'yke109', 'aka130'));
                    break;
                case "bkc014":
                    Vue.set(this.grxxJson, type, val[0]);
                    Vue.set(this.grxxJson, "bkc117", val[4]);
                    break;
            }
        },
        qdFun: function () {
            //签到
            window.insuranceGbUtils.qd();
        },
        loadqd: function () {
            window.insuranceGbUtils.qd();
        },

        gjgxcx:function(){

                let param_S5902 = window.insuranceGbUtils.param_S5902(rightVue.sbjyxx,cd_014gj.grxxJson);
                console.log(param_S5902)
                let ryxx = JSON.parse(sessionStorage.getItem('gjhzybxx'));
                let data2 = window.insuranceGbUtils.call1("S5902",param_S5902,ryxx.insuplc_admdvs,cd_014gj.sfyd);

                if(data2){
                    if(data2.result && data2.result.length>0){
                        cd_014gj.grxxJson.relation = data2.result[0].hser_rlts
                        cd_014gj.$forceUpdate()
                    }
                }

        },
        //读卡
        load: function () {

            /*if(!cd_014gj.ifclick){
                malert("请勿重复点击！","right","defeadted");
                return;
            }*/
            cd_014gj.ifclick = false;
			window.insuranceGbUtils.qd();
			window.insuranceGbUtils.gjinit();
            if (window.insuranceGbUtils.initStatus) {
				let ret1 =  window.insuranceGbUtils.callgj('1101','',cd_014gj.sfyd,cd_014gj.userInfo,"01301");
				if(ret1){
					let ryxx =  window.insuranceGbUtils.gjryxx(cd_014gj.sfyd);
					this.syxx = ryxx;
					this.insuinfo = ryxx.insuinfo
					if(ryxx.insuinfo.length>=1){
						this.dtxx = true;
					}else{
						this.dtxx = false;
					}
					
				}
            } else {
                cd_014gj.ifclick = true;
                malert("医保控件未初始化,请重新打开页面！", 'right', 'defeadted');
            }
        },
		//确定使用人员信息
		saveZxXx:function(){
					let param = [];
					for (var i = 0; i < this.isZxChecked.length; i++) {
							if(this.isZxChecked[i]){
								param.push(this.insuinfo[i])
							}
						}
					console.log(param)
					if(param){
						cd_014gj.grxxJson.aac001 = cd_014gj.syxx.baseinfo.psn_no
						cd_014gj.grxxJson.aac003 = cd_014gj.syxx.baseinfo.psn_name
						cd_014gj.grxxJson.aac004 = cd_014gj.syxx.baseinfo.gend
						cd_014gj.grxxJson.akc023 = cd_014gj.syxx.baseinfo.age
						cd_014gj.grxxJson.aac002 = cd_014gj.syxx.baseinfo.certno
						
						cd_014gj.grxxJson.aac006 = cd_014gj.syxx.baseinfo.brdy
						cd_014gj.grxxJson.ykc303 = param[0].insutype
						console.log(cd_014gj.insutype_tran[param[0].insutype]);
						cd_014gj.grxxJson.ykc194 = param[0].balc
						cd_014gj.grxxJson.ykc177 = param[0].cvlserv_flag
						cd_014gj.grxxJson.psn_type = param[0].psn_type
						cd_014gj.grxxJson.psn_insu_stas = param[0].psn_insu_stas
						cd_014gj.grxxJson.psn_insu_date = param[0].psn_insu_date
						cd_014gj.grxxJson.paus_insu_date = param[0].paus_insu_date
						cd_014gj.grxxJson.insuplc_admdvs = param[0].insuplc_admdvs
						let ryxx = JSON.parse(sessionStorage.getItem('gjhzybxx'));
						ryxx.insuplc_admdvs = param[0].insuplc_admdvs;
						sessionStorage.setItem('gjhzybxx',JSON.stringify(ryxx));
						
						cd_014gj.grxxJson.emp_name = param[0].emp_name
						this.dtxx = false;
						if(cd_014gj.syxx.baseinfo.psn_name && rightVue.fzContent && rightVue.fzContent.brxm && cd_014gj.syxx.baseinfo.psn_name != rightVue.fzContent.brxm){
							malert("非本人医保卡请确认", "right", "defeadted");
						}
					}
					
				},
				
		reZxCheckChange: function (val) {
			this.isZxChecked = []
					        for (var i = 0; i < this.insuinfo.length; i++) {
		            if (!val[2]) {
		                Vue.set(this.isZxChecked, val[1], val[2]);
		            } else {
		                Vue.set(this.isZxChecked, val[1], val[2]);
		            }
		        }
		    
		},
        //引入
        enter: function () {
            if (Object.keys(cd_014gj.grxxJson).length === 0) {
                malert("请先读卡", "right", "defeadted");
                return;
            }
            if (!cd_014gj.grxxJson.relation) {
                malert("请选择亲属关系", "right", "defeadted");
                return;
            }

            let ryxx = JSON.parse(sessionStorage.getItem('gjhzybxx'));
            let ret1 =  window.insuranceGbUtils.callgj('S2101',{
                card_token:ryxx.card_token,
                enddate:rightVue.fDate(new Date(),'date'),
                adsetl_codg:'0',
                rea:'',
                fixmedins_code:window.insuranceGbUtils.fixmedins_code,
                psn_no:cd_014gj.grxxJson.aac001,
                mdtrtarea_admvs:window.insuranceGbUtils.mdtrtarea_admvs,
                local_type:'3',
                out_type:'2',
            },cd_014gj.sfyd,cd_014gj.userInfo,"01301");
            if(ret1){
                let jyxx = JSON.parse(sessionStorage.getItem('gjjyjbxx'));


                let param_S2101 = window.insuranceGbUtils.param_S2101(rightVue.sbjyxx,cd_014gj.grxxJson,jyxx);
                console.log(param_S2101)



                let data2 = window.insuranceGbUtils.call1("S2101",param_S2101,ryxx.insuplc_admdvs,cd_014gj.sfyd);

                if(data2){

                    let jsxx ={
                        jzid:data2.result.mdtrt_id,
                        jsid:data2.result.setl_id,
                        mz:data2.result.naty,
                        csrq:data2.result.brdy,
                        nl:data2.result.age,
                        xzlx:data2.result.insutype,
                        rylb:data2.result.psn_type,
                        gwybz:data2.result.cvlserv_flag,
                        jssj:data2.result.setl_time,
                        jzpzlx:data2.result.mdtrt_cert_type,
                        yllb:data2.result.med_type,
                        ylfze:data2.result.medfee_sumamt,
                        cxjzffy:data2.result.overlmt_selfpay,
                        xxzfje:data2.result.preselfpay_amt,
                        fhzcfwje:data2.result.inscp_scp_amt,
                        jbylbxtcjjzc:data2.result.hifp_pay,
                        jbylbxtcjjzfbl:data2.result.pool_prop_selfpay,
                        gwyylbzzjzc:data2.result.cvlserv_pay,
                        qybcylbxjjzc:data2.result.hifes_pay,
                        jmdbbxzjzc:data2.result.hifmi_pay,
                        zgdeylfybzjjzc:data2.result.hifob_pay,
                        yljzjjzc:data2.result.maf_pay,
                        qtzc:data2.result.oth_pay,
                        jjzfze:data2.result.fund_pay_sumamt,
                        sjzfqfx:data2.result.act_pay_dedc,
                        grfdzje:data2.result.psn_part_amt,
                        ryzjlx:data2.result.psn_cert_type,
                        grzhzc:data2.result.acct_pay,
                        rybh:data2.result.psn_no,
                        qzfje:data2.result.fulamt_ownpay_amt,
                        zjhm:data2.result.certno,
                        zrxjzc:data2.result.psn_cash_pay,
                        yyfdje:data2.result.hosp_part_amt,
                        xb:data2.result.gend,
                        ye:data2.result.balc,
                        ryxm:data2.result.psn_name,
                        grzhzjzfje:data2.result.acct_mulaid_pay,
                        yyjgjsid:(data2.msgid?data2.msgid:data2.result.medins_setl_id),
                        qsjbjg:data2.result.clr_optins,
                        qsfs:data2.result.clr_way,
                        qslb:data2.result.clr_type,
                        zfbz:'0',
                        zfry:'',
                        zfrq:'',
                        jszt:'1',
                        jsczy:userName,
                        grjsfs:'01',
                        sfpch:'',
                        fph:'',
                        cjrq:data2.result.setl_time,
                        gjid:rightVue.sbjyxx.setlinfo.setl_id,
                        insuplcadmdvs:ryxx.insuplc_admdvs,
                    }

                    rightVue.gjjsxx = data2

                    let ybkzf = cd_014.MathAdd(popCenter1.jsjlContent.ybkzf, data2.result.acct_pay);
                    //todo 社保基金支付总额
                    popCenter1.jsjlContent.ybkzf = rightVue.fDec(ybkzf, 2);




                    console.log(JSON.stringify(jsxx));
                    $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjsxx&parm="
                        + JSON.stringify(jsxx),
                        function (json) {
                            if (json.a != 0) {
                                malert("保存失败  " + json.c, 'right', 'defeadted');
                            }
                        });

                    let jsjjfx = [];
                    for (let i = 0; i < data2.setldetail.length; i++) {

                        jsjjfx.push({
                            jzid:data2.result.mdtrt_id,
                            jsid:data2.result.setl_id,
                            jjzflx:data2.setldetail[i].fund_pay_type,
                            fhzcfwje:data2.setldetail[i].inscp_scp_amt,
                            bckzfxeje:data2.setldetail[i].crt_payb_lmt_amt,
                            jjzfje:data2.setldetail[i].fund_payamt,
                            jjzflxmc:data2.setldetail[i].fund_pay_type_name,
                            jsgcxx:data2.setldetail[i].setl_proc_info?data2.setldetail[i].setl_proc_info.replace(/%/g,"/"):'',

                        })
                    }
                    console.log(JSON.stringify(jsjjfx));
                    if(jsjjfx.length>0){
                        $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjjfxxx&parm="
                            + JSON.stringify(jsjjfx),
                            function (json) {
                                if (json.a != 0) {
                                    malert("保存失败  " + json.c, 'right', 'defeadted');
                                }
                            });
                    }

                    popTable1.isShow = false;
                    rightVue.jejsfs();

                }


            }

            
        },
        
        emptyReplace: function (para) {
            if (para == null || typeof (para) == "undefined") {
                return '';
            }
            return para;
        },
        toGroupBy: function (array, fn) {
                        const groups = {};
            array.forEach(function (item) {
                const group = JSON.stringify(fn(item));
                //这里利用对象的key值唯一性的，创建数组
                groups[group] = groups[group] || [];
                groups[group].push(item);
            });


            //最后再利用map循环处理分组出来
            return Object.keys(groups).map(function (group) {
                return groups[group];
            });
        },
		
        sleep: function (delay) {
                        var start = (new Date()).getTime();
            let end = (new Date()).getTime();
            while ((end - start) < delay) {
                end = (new Date()).getTime()
            }
            return true;
        },
        decodeUnicode: function (str) {
            //先把十六进制unicode编码/u替换为%u
            str = str.replace(/\\u/gi, '%u');
            //再把页面中反斜杠替换为空
            str = str.replace(/\\/gi, '');
            return unescape(str);
        }
    }
});


$(document).click(function () {
    if (this.className != 'selectGroup') {
        $(".selectGroup").hide();
    }
    $(".popInfo ul").hide();
});
