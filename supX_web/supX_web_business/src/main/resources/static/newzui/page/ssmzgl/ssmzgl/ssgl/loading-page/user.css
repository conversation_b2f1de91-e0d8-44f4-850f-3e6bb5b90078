.hljl {
    border: 1px solid #eeeeee;
    border-bottom: none;
}

.hjli-header {
    background: #fbfbfb;
    height: 43px;
    border-bottom: 1px solid #eeeeee;
    line-height: 43px;
}

.hljl .hjli-line {
    height: 40px;
    align-items: center;
    justify-content: space-between;
    align-content: center;
    width: 100%;
    cursor: pointer;
    border-bottom: 1px solid #eeeeee;
    display: flex;
}
.hljl .hjli-line:hover{
    background-color: rgba(26, 188, 156, 0.08);
}
.hljl .title-text {
    font-size: 14px;
    color: #757c83;
    margin: 0 60px 0 15px;
    width: 96%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    height: 40px;
    line-height: 40px;
    display: inline-block;
}

.text-position {
    width: 36%;
}

.hljl .hljl-date {
    font-size: 14px;
    margin-right: 24px;
    color: #757c83;
    overflow: hidden;
    text-align: center;
}

.hljl-left-line {
    color:#333333;
    font-size: 16px;
    margin-left: 15px;
    font-weight: bolder;
    margin-right: 17px;
}

.hljl-right-line {
    font-size:12px;
    color:#757c83;
    cursor: pointer;
    margin-right: 12px;
}
.hljl-right-line:hover{
    opacity: 0.4;
}
.wyc {
    margin-right: 26px;
}

.usermz {
    margin-right: 18px;
}

.hzList .zui-table-body .green td input ,.green{
    color: #13a950;
}

.hzList .zui-table-body .red td input,.red{
    color: #ff4532;
}

.hzList .zui-table-body .blue td input,.blue{
    color: #4b8ad4;
}
.hzList .zui-table-body .zgs td input,.zgs{
    color: RGB(132,0,0);
}
.common-right {
    background: #ffffff;
    border: 1px solid #eeeeee;
    height: 244px;
}
.margin-b-8{
    margin-left: 8px;
}
.icon-date-left {
    position: relative;
    width: 22px;
    height: 22px;
    vertical-align: text-bottom;
    background-image: url("/newzui/pub/image/LeftButton.png");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    display: inline-block;
    margin-right: 9px;
    cursor: pointer;
}

.icon-date-right {
    position: relative;
    width: 22px;
    height: 22px;
    margin-left: 9px;
    vertical-align: text-bottom;
    background-image: url("/newzui/pub/image/RightButton.png");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    display: inline-block;
    cursor: pointer;
}

.date-text {
    vertical-align: top;
}
.EmRbltx {
    font-size: 12px;
    color: #ffffff;
    text-align: center;
    background: #1abc9c;
    border-radius: 17px;
    width: 60px;
    height: 28px;
    line-height: 28px;
    margin-right: 11px;
}

.yellow {
    color: #f2a654;
}

.yellow-bg {
    background-color: #f2a654;
}

.feise-bg {
    background: #718dc7;
}

.yz-item {
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    flex-wrap: wrap;
}

.yz-list {
    width: 30%;
    text-align: center;
    margin-bottom: 11px;
}

.xnh-list {
    width: 50%;
    text-align: center;
    margin-bottom: 11px;
}

.zxz-number {
    font-size: 30px;
    color: #1abc9c;

}

.zxz-text {
    font-size: 14px;
    color: #757c83;
}

.notEdit {
    border: none;
    background: transparent;
    text-align: center;
}

.notEdit:focus {
    background-color: transparent;
}

.hzgl-top {
    margin-top: -7px;
}

.tong-search .zui-form .hzgl-margin {
    padding: 0 0 8px 20px;
}

.tong-search .zui-form .hzgl-margin-pop {
    padding: 0 0 8px 20px;
}

.hzgl-margin .zui-input {
    background: #ffffff;
    border-radius: 4px;
    width: 120px;
}

.side-form {
    padding-top: 0;
}

.pop-805 {
    width: 805px !important;
}

.pop-content {
    width: 100%;
    float: right;
}

.pop-content .content-right-top,
.pop-content .content-right-list {
    width: 100%;

}

.pop-content .content-right-list {
    /*overflow: auto;*/
    overflow-x:auto !important;
    height: 100%;
}

.pop-content .content-right-top i,
.pop-content .content-right-list i,
.pop-content .content-right-top span,
.pop-content .content-right-list span {
    width: 100px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
}

.pop-content .content-right-top i em,
.pop-content .content-right-list i em,
.pop-content .content-right-top span em,
.pop-content .content-right-list span em {
    margin-top: 10px;
}

.pop-content li {
    cursor: pointer;
    width: 100%;
}

.pop-content li:hover {
    background: rgba(26, 188, 156, 0.08) !important;
    border: 1px solid #1abc9c;
}

.xmzb-content-right .content-right-top {
    width: auto;
    display: flex;
    justify-content: flex-start;
    border: 1px solid #e9eee6;
    background: #edf2f1;
    height: 40px;
    align-items: center;
    white-space: nowrap;
    /*overflow: auto;*/
    overflow: hidden;
}

/*.ksys-side span {*/
/*display: flex;*/
/*width: 100%;*/
/*justify-content: center;*/
/*position: relative;*/
/*}*/

.xmzb-content-right .content-right-list li {
    width: max-content;
    /*display: flex;*/
    justify-content: flex-start;
    align-items: center;
    height: 41px;
    border: 1px solid #e9eee6;
    border-top: none;
    cursor: pointer;
    line-height: 40px;
    overflow: hidden;
}

.hzgl-height {
    height: calc(100% - 154px);
    padding: 8px 10px;
    overflow: hidden;
}

.hzgl-wiwidth-one {
    width: 38.3%;
    /*height: calc(100% - 40px);*/
}

.hzgl-wiwidth-two {
    width: 60%;
}

.btn-parmary-not {
    border: none;
    color: #fff;
}

.loadPage .hzgl-flex {
    justify-content: flex-end;
    background: #fff !important;
    display: flex;
    align-items: center;
    border-top: 1px solid #eee;
    height: 66px;
}

h1 {
    font-size: 24px;
    font-weight: bold;
    color: #354052;
}

.hzgl-title {
    font-size: 14px;
    color: #7f8fa4;
}

.add-yp-hzgl {
    background: rgba(26, 188, 156, 0.07);
    border: 1px solid #1abc9c;
    border-radius: 4px;
    margin: 0 10px 15px 0;
    line-height: 34px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    position: relative;
    width: 34px;
    height: 34px;
}

.add-yp-hzgl:before {
    position: absolute;
    content: '+';
    left: 22%;
    margin-top: -1px;
    color: #1abc9c;;
    font-size: 24px;

}

.hzgl-sc {
    position: relative;
}

.hzgl-shanchu {
    width: 20px;
    /*height: 20px;*/
    cursor: pointer;
}

.hzgl-shanchu:before {
    position: absolute;
    top: -5px;
    content: '';
    background: #dfe3e9;
    width: 20px;
    left: -5px;
    height: 20px;
    z-index: 1;
    border-radius: 100%;
}

.hzgl-shanchu:after {
    position: absolute;
    top: -5px;
    content: '';
    background: #ffffff;
    width: 10px;
    left: -5px;
    height: 3px;
    z-index: 1;
    text-align: center;
    margin: 8px 5px;
}

.zui-form-label {
    width: 70px;
    /*padding:8px 21px 8px 0;*/
}

.zui-form .hzgl-not-margin {
    padding-left: 70px;
    margin-right: 0;

}

.zui-form .margin-b-15 {
    margin-bottom: 15px;
}

.hzgl-btn {
    /*height: 68px;*/
    bottom: 0;
    z-index: 12;
    justify-content: space-between;
    line-height: 68px;
    background: #ffffff;
}

.hzgl-btn .left {
    margin-left: 17px;
}

.hzgl-tj {
    margin-right: 50px;
}

.hzgl-not-pad {
    padding: 12px 0 26px 0;
}

.hzgl-ck {
    height: 44px;
    line-height: 33px;
}

.hgzl-border {
    padding-bottom: 14px;
    border-bottom: 1px dashed rgba(26, 188, 156, 0.3);
    width: 98%;
    margin: 0 auto;
}

.jcjy {
    width: 108px;
    height: 99%;
    font-size: 16px;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.jcjy-active {
    color: #1abc9c;
    background: #ffffff;
    position: relative;
}

.jcjy-active:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 2px;
    width: 100%;
    background-color: #1abc9c;
}

.jcjy-top-side {
    padding: 0;
}

.twd {
    width: 675px;
    height: 600px;
    border: 1px solid #EEEEEE;
    /*overflow: auto;*/
}
.twd-scroll{
    overflow: auto;
    padding-right: 5px;
}
.twd-scroll::-webkit-scrollbar{
    width: 0;
    height: 0;
}
.paev {
    margin-right: 68px;
}

.next {
    margin-left: 68px;
}

.paev, .next {
    content: '';
    position: relative;
    width: 68px;
    height: 68px;
    cursor: pointer;
    border-radius: 100%;
    background: rgba(255, 255, 255, 0.10);
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.15);
}
.paev:hover, .next:hover{
    opacity: .5;
    /*box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.15);*/
}
.paev.fa-angle-left:before, .next.fa-angle-right:before {
    position: absolute;
    left: 50%;
    color: #E2E2E2;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 40px;
}

.twd-center {
    justify-content: center;
}

.zui-date .todate {
    /*padding-right: 10px;*/
}

.tong-search .zui-form .lclj-padd {
    padding-left: 30px;
}

.lclj-bg {
    background: #fdfdfd;
    border: 1px solid #e9eee6;
    height: 403px;
}

.maxContent {
    width: max-content;
    min-width: 100%;
}

.yzHeader {
    background: #edf2f1;
    border: 1px solid #e9eee6;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.cssz-list li {
    align-items: end;
}

.yzContent {
    height: 45px;
}

.yzHeader li {
    padding: 9px 15px 7px 16px;
    font-size: 14px;
    color: #333333;
    border: 1px solid #e9eee6;
    text-align: center;
}

.topData {
    width: 138px;
    padding: 9px 15px 7px 16px;
    font-size: 14px;
    color: #333333;
    text-align: center;
}

.yzDataList .leftData {
    background: #fdfdfd;
    border: 1px solid #e9eee6;
    width: 138px;
    font-size: 14px;
    text-align: center;
    border-right: none;
    border-top: none;
    color: #1abc9c;
    display: inline-block;
}

.yzDataList .title, .yzDataList input, .yzDataList .titleText {
    display: inline-block;
    background: #ffffff;
    border: 1px solid #e9eee6;
    height: 45px;
    text-align: center;
    padding: 11px 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    border-top: none;
    border-right: none;
}

.yzDataList .titleText:last-child {
    border-right: 1px solid #e9eee6;
}

.yzDataList .leftData {
    background: #fdfdfd;
    border: 1px solid #e9eee6;
    width: 138px;
    font-size: 14px;
    text-align: center;
    border-right: none;
    border-top: none;
    color: #1abc9c;
    display: inline-block;
}

.headerText {
    font-size: 22px;
    color: #3a3a3a;
    margin-bottom: 10px;
    text-align: center;
}

.headerList {
    font-size: 14px;
    color: #7f8fa4;
    margin-left: 9px;
    margin-bottom: 3px;
    margin-right: 10px;
    text-align: center;
    display: flex;
    justify-content: space-between;
}

.grid-box .right-bg {
    width: 74%;
    margin-left: 9px;
}

.brzilist {
    margin-top: 25px;
}

.value-pgsj {
    background: #ffffff;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    height: 170px;
    width: 100%;
    padding: 9px 12px;
}

.not-align-items {
    align-items: end;
}

.pgmb-text {
    font-size: 12px;
    color: #f2a654;
    text-align: right;
    line-height: 16px;
    cursor: pointer;
    position: relative;
    display: inline-block;
    float: right;
}

.pgmb-text:before {
    position: absolute;
    left: -14px;
    top: 2px;
    content: '';
    width: 12px;
    height: 14px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
    background-image: url("/newzui/pub/image/pgmb.png");
}

.lclj-pop-list {
    height: 96px;
}

.lclj-height {
    height: 60px;
    line-height: 60px;
}

.lclj-list {
    background: #fbfbfb;
}

.fyxm-size {
    height: 100%;
}

.lclj-po {
    position: absolute;
    bottom: 15px;
    right: 18px;
}

.value-lclj {
    color: #767d85;
    font-size: 14px;
    border-radius: 4px;
    padding: 0 10px;
    resize: none;
    overflow: hidden;
    height: 100%;
    width: 93%;
    display: inline-block;
}

.notCursor {
    cursor: default;
}

.lclj-baocun {
    background: #f2a654;
    border-radius: 4px;
    width: 48px;
    cursor: pointer;
    height: 100%;
    text-align: center;
    margin-left: 7px;
}

.un-text {
    width: 14px;
    display: inline-block;
    text-align: center;
    margin-top: 19.5px;
    color: #ffffff;
}

.blur-edit {
    padding: 7px 16px;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
}

.icon-bj:before {
    top: 0;
    position: static;
}
.yzcz {
    display: inline-block;
    border: 0;
    width: auto;
    padding: 0;
}

.yzcz > button {
    background-color: #029377;
}

.yzcz:hover > ul {
    display: block;
}

.yzcz ul {
    display: none;
    position: absolute;
    width: 90px;
    margin: -1px 0 0 4px;
    background-color: #fff;
    border: 1px solid #029377;
    z-index: 100;
}

.yzcz ul > li {
    padding: 6px 4px;
}

.yzcz ul > li:hover {
    background-color: #1AB394;
    color: #FFFFFF;
    cursor: pointer;
}

.CFUse {
    position: relative;
    background-color: #FFFFFF;
    box-shadow: #333 2px 2px 8px;
    width: 800px;
    height: 450px;
    margin: 0 auto;
}

.CFUse img {
    position: absolute;
    top: 8px;
    right: 8px;
}

.CFUseTitle {
    font-size: 16px;
    background-color: #eeeeee;
    padding: 6px 0 6px 12px;
    text-align: left;
}

.searchCFUse {
    margin: 10px 0 0 0;
    display: inline-block;
    width: 100%;
}

.searchCFUse > div {
    margin-left: 16px;
}

.searchCFUse select {
    width: 100px;
}

.YzTabTable {
    float: left;
    overflow: scroll;
    padding: 0;
    margin: 10px 0 0 16px;
    width: 30%;
}

.yzd {
    margin-top: 44px;
}

.yzd .toolMenu > div {
    float: left;
    padding: 5px 16px;
    margin-right: 20px;
    cursor: pointer;
}

.yzd .toolMenu > div:hover {
    border-bottom: 2px solid #1AB394;
}

.yzd_select {
    border-bottom: 2px solid #1AB394;
}

.yzdTitle {
    font-size: 22px;
    text-align: center;
    color:#3a3a3a;
}

.yzd-brInfo, .yzd-ysInfo {
    display: inline-block;
    display: flex;
    justify-content: center;
    color:#7f8fa4;
    margin: 10px 0;
}


.yzd-brInfo > div, .yzd-ysInfo > div {
    font-size: 14px;
    margin-right: 20px;
}

.tablePage span {
    display: inherit;
}

.yzd-table table {
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
}

.yzd-table td, .yzd-table th {
    font-weight: 500;
    border-left: 1px solid #999;
    height: 39px;
    white-space: nowrap;
    /*width: 30px;*/
    color:#757c83;
    /*padding: 0 5px;*/
    /*text-align: center;*/
}
.yzd-table tr{
    border: 1px solid #999;
}
.yzd-table .border-left-top-none{
    /*border-top: none;*/
    /*border-left: none;*/
    /*border-right: none;*/
}
.yzd-table table {
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
}

.patientTable td, th {
    height: 24px;
    font-size: 14px;
    cursor: default;
    white-space: nowrap;
    padding: 6px;
}

.yzd-table td .yzd-name {
    display: block;
    width: 60%;
    font-size: 13px;
    text-align: left;
    margin-left: 2px;
    word-break:normal;
    white-space:pre-wrap;
    word-wrap : break-word ;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
}

.cqyzd, .lsyzd {
    text-align: center;
    overflow: scroll;
    height: 100%;
}

.ysDiv {
    width: 100%;
    text-align: center;
    /*position: absolute;*/
    bottom: 0;
}

.copyTem {
    float: right;
    margin-right: 27px;
    font-size: 14px;
    cursor: pointer;
    padding: 3px;
    border-radius: 3px;
    background-color: #EEEEEE;
}

.copyTem:hover {
    background-color: #1AB394;
    color: #FFFFFF;
}

.copy-br th:nth-child(2) {
    min-width: 97px;
}

.tableDiv select, .tableDiv input {
    width: 100%;
}

.tableDiv .selectInput {
    width: 98%;
    height: 26px;
}

.tableDiv td {
    text-align: center;
    position: relative;
}

.selectGroup td {
    text-align: left;
}

.zuNum ul {
    width: 60px;
    min-width: 60px;
    top: 28px;
}

.toolMenu_yzd div {
    float: left;
    text-align: center;
    padding: 6px 16px;
    cursor: pointer;
}

.same {
    border-right: 1px solid #000000;
}

.yzd-name {
    /*float: left;*/
    height: 42px;
}

.yzd-sm {
    float: right !important;
}

.tableDiv table {
    table-layout: fixed;
    height: 100% !important;
}

.tableDiv table td {
    max-width: 60px;
    text-overflow: ellipsis;
}

#tdkd {
    width: 72px;
}

.sameStart {
    position: absolute;
    border-top: 1px solid #000000;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 50%;
    right: 41%;
    bottom: 0;
}

.sameEnd {
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 1px solid #000000;
    width: 10px !important;
    height: 50%;
    right: 41%;
    top: 0;
}

.same {
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 100%;
    right: 41%;
    top: 0;
}

.yzd-way {
    text-align: left;
    display: block;
    font-size: 13px
}

.yzd-table td, .yzd-table th {
    position: relative;
}

.yzcl_xmmc .selectGroup th:nth-child(3) {
    min-width: 300px;
}

.yzcl_xmmc .selectGroup th:nth-child(4) {
    min-width: 150px;
}

.yzd-name {
    height: auto;
}

.zcy-title {
    font-size: 20px;
    font-weight: 600;
}

.zcy-brxx {
    text-align: left;
    margin-top: 4px;
}

.zcy-brxx span {
    margin-left: 6px;
}

.zcy-item {
    float: left;
    margin: 0 30px;
}

.zcy-item > div {
    position: relative;
    width: 50px;
    height: 26px;
}

.zcy-item input, .zcy-item select {
    float: left;
    height: 24px;
}

.zcy-ypxx {
    position: absolute;
    bottom: 50px;
    right: 10px;
    text-align: right;
}

.zcy-ypxx span {
    margin: 0 4px;
}

.zcy-ypxx input {
    margin-bottom: 6px;
}

.zcy-ewxx {
    position: absolute;
    width: 100%;
    bottom: 10px;
}

.zcy-ewxx span {
    float: left;
    margin-left: 20px;
}

@media print {
    /*@page {*/
    /*margin-top: 86px;*/
    /*}*/
    .goPrintHide {
        visibility: hidden;
    }
    .enter_tem1 {
        border: 0;
    }
}

.yzd {
    height: 538px;
    overflow: auto;
}

.pop {
    display: table;
    width: 100%;
    height: 100%;
    text-align: center;
    top: 0;
    left: 0;
    position: absolute;
    transition: opacity 0.3s ease;
    z-index: 111111;
}

.fyqdTable td span:first-child {
    float: left;
    margin-left: 176px;
}

.fyqdTable td span:last-child {
    float: right;
    margin-right: 176px;
}

#table_1 tr td:first-child {
    width: 84px;
    border-right: 2px solid #888;
}

#table_1 td {
    width: 84px;
}

#table_2 {
    margin-top: -1px;
}

#table_2 tr:first-child {
    border-top: 1px solid #888;
}

#table_2 td {
    height: 28px;
    text-align: center;
    min-width: 14px;
    max-width: 14px;
}

#table_2 tr td:first-child {
    width: 83px;
    border-right: 2px solid #888;
    min-width: 83px;
    max-width: 83px;
    padding: 0;
    margin: 0;
}

#table_3 tr td:first-child {
    width: 83px;
    border-right: 2px solid #888;
}

#table_3 td {
    height: 19px;
    width: 42px;
}

#table_4 td {
    height: 19px;
    width: 84px;
}

#table_4 tr td:first-child {
    width: 83px;
    border-right: 2px solid #888;
}

.td_top {
    vertical-align: top;
    color: red;
}

.td_bottom {
    vertical-align: bottom;
    color: red;
}

.twd-title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    /*width: 676px;*/
    padding: 10px 0;
}

#jlxq {
    width: 100%;
    margin: 0 auto;
    overflow: scroll;
}

.jlxq_brInfo {
    display: inline-block;
    width: 660px;
    border: 1px solid #bbbbbb45;
    padding: 10px 10px 0 10px;
}

.jlxq_brInfo > div {
    float: left;
    margin-right: 20px;
    margin-bottom: 10px;
}

.jlxq_rl, .jlxq_cl, .jlxq_tk {
    position: relative;
    float: left;
    border: 1px solid #bbbbbb45;
    padding: 10px;
    margin-top: 10px;
}

.jlxq_title {
    position: absolute;
    top: -10px;
    background-color: #FFFFFF;
    font-size: 14px;
    min-width: 50px;
    text-align: center;
}

.jlxq_tk_title {
    text-align: center;
    height: auto !important;
    margin-top: 30px;
}

.jlxq_other {
    float: left;
    margin: 10px 0;
}

.jlxq_other span {
    display: block;
    float: left;
    width: 60px;
    text-align: right;
    margin-top: 6px;
}

.jlxq_other input {
    margin-left: 9px;
    display: block;
    float: left;
}

.r-textarea {
    width: 100%;
    padding: 0 10px;
    height: 80px;
    resize: none;
    border: 1px solid #e6e6e6;
    background-color: #fff;
    color: #767d85;
    font-size: 14px;
    border-radius: 4px;
}

.jlxq_rl {
    border-right: none;
}

.tkcss {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}


.dzcf-right-top {
    width: 100%;
    float: left;
    background: #edf2f1;
    height: 34px;
    line-height: 34px;
    color: #333;
}

.dzcf-right-top i {
    width: calc((100% - 50px) / 2);
    display: block;
    text-align: center;
    float: left;
}

.cf-xz i, .cf-xz li i {
    width: calc((100% - 50px) / 6);
    display: block;
    text-align: center;
    float: left;
}

.dzcf-list {
    width: 100%;
    float: left;
    overflow: auto;
    /* max-height: 700px; */
    border: 1px solid #eee;
}

.dzcf-list li {
    line-height: 40px;
    border-top: 1px solid #eee;
    height: 40px;
    cursor: pointer;
    width: 100%;
}

.dzcf-right-top i:first-child, .dzcf-list i:first-child {
    width: 50px !important;
}
.zcyItem{
    margin-top: 52px;
}

.printShow {
    position: relative;
    width: 100%;
    height: 100%;
}

.bgNone input {
    border: none;
    text-align: center;
    background-color: transparent !important;
}

.tem {
    position: relative;
    float: left;
    /* width: 300px; */
    /* height: 450px; */
    width: 800px;
    height: 500px;
    border: 1px solid green;
    margin-left: 20px;
    margin-top: 20px;
}

.tem .item {
    position: absolute;
    display: inline-block;
    top: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    cursor: default;
    z-index: 100;
}
.ksys-side .dzcf-rps{
    position: absolute;
    top: 0;
    text-align: center;
    right: 0;
    justify-content: flex-end;
}
.ksys-side .dzcf-rps img{
    width:53px;
    height: 52px;
}
.flex_10{
    align-items: center;
}
.flex_9{
    align-items: center;
}
.flex_9 li{
    height: 39px;
    text-align: center;
    line-height: 39px;
    color: #2F2F2F;
    background: #edf2f1;
    width: calc((100% - 50px)/8) ;
}
.flex_9 p{
    padding: 10px 0;
    cursor: pointer;
    text-align: center;
    color: #7C8189;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: calc((100% - 50px)/8) ;
}
.flex_10 li{
    height: 39px;
    text-align: center;
    line-height: 39px;
    color: #2F2F2F;
    background: #edf2f1;
    width: calc((100% - 50px)/9) ;
}
.fyqd{
    padding: 0 10px;
    height: calc(100% - 198px);
    overflow: hidden;
}
.qdmx_title .list{
    width: calc((100% - 50px)/9) ;
    padding: 10px 0;
    text-align: center;
    /*border-top: 1px #eee solid;*/
}
.qdmx_title .notBorder{
    border-top: none;
    font-size: 16px;
}
.qdmx_item .list{
    width: calc((100% - 50px)/9) ;
    padding: 10px 0;
    text-align: center;
}
.qdmx_item .list span{
    cursor: pointer;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    display: inline-block;
    color: #7C8189;
    text-overflow: ellipsis;

}
.qdmx_item:hover,.flex_Mx:hover{
    background-color: rgba(26,188,156,0.08)
}
.qdmx_item,.flex_Mx{
    border-top: 1px #eee solid;
    border-left: 1px #eee solid;
    border-right: 1px #eee solid;

}
.scroll .qdmx_item:last-child{
    border-bottom: 1px #eee solid;
}
.qdmx_title{
    border-left: 1px #eee solid;
    border-right: 1px #eee solid;
}
.margin-left{
    margin-left: 9px;
}
.lclj-title{
    margin: 10px;
    font-size:14px;
    color:#7f8fa4;
    margin-top: 48px;
}
.height-100{
    height: 100%;
}
.border-dzbl{
    background-color: #ffffff;
    border: 1px solid #ffffff;
    border-radius: 100%;
    width: 160px;
    height: 160px;
    cursor: pointer;
    margin-right: 30px;
    box-shadow:0 0 5px 1px rgba(51, 51, 51, 0.2196078431372549)
}
.height-500{
    height: 500px;
}
.zvyHeight{
    max-height: 552px;
}
.pc-select{
    border: 1px solid #d7dbe1;
}