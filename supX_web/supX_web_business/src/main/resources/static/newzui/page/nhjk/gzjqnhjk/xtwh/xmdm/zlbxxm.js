(function(){
    var zl_toolMenu = new Vue({
        el:'.zlbxxm .tong-top',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],
        data: {
            bxlbbm: null,
            bxurl: null,
            searchtext: null,
            type:'qb',
        },
        methods: {
            sschangeDown: function () {
                if (window.event.keyCode == 13) {
                    zl_toolMenu.searchtext = $('#search').val();
                    zl_toolMenu.getData();
                }
            },
            changeType:function(xType){
                zl_toolMenu.type=xType;
                zl_toolMenu.getData();
            },
            // 请求保险类别
            getbxlb: function () {
                var param = {bxjk: "005"};
                common.openloading("#zlbxxm");
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            if (json.d.list.length > 0) {
                                zl_toolMenu.bxlbbm = json.d.list[0].bxlbbm;
                                zl_toolMenu.bxurl = json.d.list[0].url;
                            }
                            common.closeLoading();
                        } else {
                            malert("保险类别查询失败!" + json.c,"top","defeated")
                            common.closeLoading();
                        }
                    });
            },
            getData: function () {
                this.param.fpks=null;
                this.param.fpfs=null;
                if(zl_toolMenu.type=='yd'){
                    this.param.fpks='1';
                }
                if(zl_toolMenu.type=='wd'){
                    this.param.fpfs='1';
                }
                this.param.parm = zl_toolMenu.searchtext;
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=queryZllx&parm=" + JSON.stringify(this.param), function (json) {
                        if (json.a == 0) {
                            var res = JSON.parse(json.d);
                            zlXmMx.totlePage = Math.ceil(res.total / zlXmMx.param.rows);
                            zlXmMx.jsonList = res.list;
                        } else {
                            malert(json.c,"top","defeated")
                        }
                    });
            },
            // 保存项目详情
            save: function (bxlbbm, fyxmbm, bxxmlb, bxxmbm, bxxmmc) {
                var param = {
                    'page': 1,
                    'rows': 30,
                    'bxlbbm': bxlbbm,
                    'fyxmbm': fyxmbm,
                    /*'bxxmlb': bxxmlb,*/
                    'bxxmbm': bxxmbm,
                    'bxxmmc': bxxmmc
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=insertZllx&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            malert("保存诊疗项目成功！","top","success");
                            zl_toolMenu.getData();
                        } else {
                            malert(json.c,"top","defeated");
                        }
                    });
            },
            // 删除项目详情
            remove: function () {

            },
            //获取HIS诊疗项目
            loadXm: function () {
                var param = {
                    page: 1,
                    rows: 30,
                    bxlbbm: zl_toolMenu.bxlbbm
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=getHisZllx&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            malert("获取诊疗项目成功！","top","success");
                            zl_toolMenu.getData();
                        } else {
                            malert(json.c,"top","defeated");
                        }
                    });

            },
            //自动对码（项目名称）
            autoDm: function () {
                var param = {
                    page: 1,
                    rows: 30
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=autoDmZl&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            malert("自动对码（项目名称）成功！","top","success");
                            zl_toolMenu.getData();
                        } else {
                            malert(json.c,"top","defeated");
                        }
                    });
            }
        }
    });

     zl_toolMenu.getbxlb();
    //左边列表
    var zlBxXm = new Vue({
        el: '.sjsc-left',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],
        data: {
            jsonList: [],
            searchCon: [],
            param: {},
            page: {
                page: 1,
                rows: 20,
                total: null
            },
        },
        methods: {
            getData: function () {
                var param = {
                    bxjk: '005'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                    zlBxXm.totlePage = Math.ceil(json.d.total / zlBxXm.param.rows);
                    zlBxXm.jsonList = json.d.list;
                });
            },
            getMx: function () {
                zl_toolMenu.getData();
            }
        }
    });

    zlBxXm.getData();
    //右边列表
    var zlXmMx = new Vue({
        el: '.sjsc-right',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],
        components: {
            'search-table': searchTable
        },
        data: {
            qjIndex:null,
            jsonList: [],
            isEdit: null,
            text: null,
            them_tran: {},
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            tybz_tran:{
                "0":"启用",
                "1":"停用",
            },
            str_param:{},
            popContent: {},
            searchCon: {},
            selSearch: -1,
            dg: {page: 1, rows: 50, sort: "", order: "asc", parm: ""},
            them: {'项目编码': 'insureId', '项目名称': 'name','拼音代码':'inputPyCode','规格':'spec','剂型':'conf','保内比例':'ratio','备注':'remark'}
        },
        methods: {
            edit: function (index) {
                console.log(index);
                this.isEdit = index;
            },
            // 点击进行赋值的操作
            selectOne: function (item, index) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching(zlXmMx.qjIndex, true,'bxxmmc',zlXmMx.jsonList[zlXmMx.qjIndex].bxxmmc); // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    zlXmMx.popContent = item;
                    console.log(JSON.stringify(item));
                    Vue.set(zlXmMx.jsonList[zlXmMx.qjIndex], 'bxxmmc', zlXmMx.popContent['name']);
                    zlXmMx.jsonList[zlXmMx.qjIndex].bxxmbm=this.popContent.insureId;
                    /*zlXmMx.jsonList[zlXmMx.qjIndex].bxxmlb=this.popContent.yka001;
                    zlXmMx.jsonList[zlXmMx.qjIndex].zfbl=this.popContent.yka096;*/
                    zl_toolMenu.save(zl_toolMenu.bxlbbm, zlXmMx.jsonList[zlXmMx.qjIndex]['xmbm'], '',item.insureId, item.name);
                    $(".selectGroup").hide();
                }
            },
            changeDown: function (index, event, type) {
                if (this['searchCon'][this.selSearch] == undefined) {return;
                }else{
                    this.keyCodeFunction(event, 'popContent', 'searchCon');
                    if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                        Vue.set(zlXmMx.jsonList[index], 'bxxmmc', zlXmMx.popContent['name']);
                        Vue.set(zlXmMx.jsonList[index], 'bxxmbm', zlXmMx.popContent['insureId']);
                        /*Vue.set(zlXmMx.jsonList[index], 'bxxmlb', zlXmMx.popContent['yka001']);
                        Vue.set(zlXmMx.jsonList[index], 'zfbl', zlXmMx.popContent['yka096']);*/
                        zl_toolMenu.save(zl_toolMenu.bxlbbm, zlXmMx.jsonList[index]['xmbm'], '',zlXmMx.jsonList[index]['bxxmbm'], zlXmMx.jsonList[index]['bxxmmc']);
                        this.nextFocus(event);
                    }
                }
            },
            getData:function(){
                zl_toolMenu.getData();
            },
            // 输入内容进行检索
            searching: function (index, add, type,val) {
                zlXmMx.qjIndex = index;
                this.jsonList[index]['bxxmmc'] = val;
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                zlXmMx.popContent = {};
                zlXmMx.dg["parm"] = val;
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=zlxx&method=query&parm=" + JSON.stringify(zlXmMx.dg), function (json) {
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            if (add) {
                                for (var i = 0; i < res.list.length; i++) {
                                    zlXmMx.searchCon.push(res.list[i]);
                                }
                            } else {
                                zlXmMx.searchCon = res.list;
                            }
                            zlXmMx.page.total = res.total;
                            zlXmMx.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert(json.c);
                        }
                    });
            }
        }
    });

    $('body').click(function() {
        $(".selectGroup").hide();
    });

    $(".selectGroup").click(function(e) {
        e.stopPropagation();
    });
})();
