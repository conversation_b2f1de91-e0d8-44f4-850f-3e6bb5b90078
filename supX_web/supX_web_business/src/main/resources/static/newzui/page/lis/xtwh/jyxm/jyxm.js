    $(".zui-table-view").uitable();
    var wrapper = new Vue({
        el: '.panel',
        data: {
            index: 1,
            pop: {},
            searchAll:'',
            updateList:[]
        },
        methods: {
            addclass: function (num) {
                this.index = num
            },
            show: function () {
                pop.isShow = true;
            },
            add: function () {//添加
            	pop.xm.bm='',
            	pop.xm.mc='',
            	pop.xm.flbm='',
            	pop.xm.bbbm='',
            	pop.xm.bgmc='',
            	pop.xm.jyff='',
            	pop.xm.tybz='0',
            	pop.xm.dyhs='',
            	pop.xm.fzbm='',
            	pop.xm.jysb='',
            	pop.xm.scfs='',
            	pop.xm.dj='',
            	pop.xm.bglx='',
            	pop.xm.dyhs='',
            	pop.xm.xssx='',
            	pop.xm.sfjh='0',
            	pop.xm.dyjyff='0',
                pop.isShow = true
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                pop.title = '新增检验项目';
            },
            deleteList: function () {//删除
            	if(jyx.isChecked.length == 0){
            		malert('请选择要删除的项目','top','defeadted');
            		return;
            	}else{
            		wrapper.delList = [];
            		for(var i = 0; i < jyx.isChecked.length ; i++){
            			if(jyx.isChecked[i]){
            				wrapper.delList.push(jyx.jsonList[i]);
            			}
            		}
            	}

            	if(wrapper.delList.length == 0){
            		malert('请选择要删除的项目','top','defeadted');
            		return;
            	}

            	popCenter.centent = "";
            	for(var j = 0 ;j < wrapper.delList.length ;j++){
            		popCenter.centent += wrapper.delList[j].bm+"-"+wrapper.delList[j].mc+",";
            	}
                popCenter.isShow = true;
                popCenter.isShowpopL = true;
                popCenter.title = '删除提示';
            },
            refresh:function(){//刷新
            	jyx.getData();
            },
            save:function(){//保存
            	if(wrapper.updateList.length == 0){
            		malert('数据未有改变','top','defeadted');
            	}else{
            		//保存操作 
            		var data = '{"list":'+ JSON.stringify(wrapper.updateList) +'}';
                	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmSave',data).then(function(json) {
    	       			 console.log(json.body);
    	       			 if(json.body.a == 0){
    	       				jyx.getData();
              			malert('保存成功！！','top','success');
              			$(".side-form-bg").removeClass('side-form-bg')
              			$(".side-form").addClass('ng-hide');
              			jyx.getData();
    	       			 }else{
    	            			malert('保存失败！！','top','defeadted');
    	            	 }
    	       		 });
            	}
            }
        },
        watch:{
        	'searchAll':function(){
        		jyx.param.parm = wrapper.searchAll;
        		jyx.getData();
        	}
        }
    });
    var jyx = new Vue({
        el: '#utable1',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
        	jsonList:'',
        	isChecked:[],
        	totlePage:'',
        	total:'',
        	param:{
        		parm:'',
        		rows:10,
        		page:1,
        	}
        },
        methods: {
            show: function (index) {
                pop.isShow = true;
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
                pop.title = '编辑检验项目';
                pop.xm = JSON.parse(JSON.stringify(jyx.jsonList[index]));
            },
            getData:function(){//获取数据
            	jyx.isChecked = [];
            	wrapper.updateList = [];
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmSelect&param=" + JSON.stringify(jyx.param), function(json) {
            		console.log(json);
            		if(json.a=="0"){
            			jyx.jsonList = json.d.list;
            			jyx.total = json.d.total;
            			jyx.totlePage = Math.ceil(json.d.total / jyx.param.rows);
            		}
            	});
            },
            change:function(index){
            	jyx.jsonList[index].tybz = jyx.jsonList[index].tybz == '0'?'1':'0';
            	//wrapper.updateList;
            	if(wrapper.updateList.length != 0){
            		for(var i = 0; i <wrapper.updateList.length ; i++){
            			if(wrapper.updateList[i].bm == jyx.jsonList[index].bm){
            				wrapper.updateList[i].tybz = jyx.jsonList[index].tybz;
            				return;
            			}
            		}
            		wrapper.updateList.push(jyx.jsonList[index]);
            	}else{
            		wrapper.updateList.push(jyx.jsonList[index]);
            	}
            },
            del:function(index){//删除
            	var del = [];
            	del.push(jyx.jsonList[index]);
            	var data = '{"list":'+ JSON.stringify(del) +'}';
            	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmDelete',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				jyx.getData();
          			malert('删除成功！！','top','success');
          			$(".side-form-bg").removeClass('side-form-bg')
          			$(".side-form").addClass('ng-hide');
          			jyx.getData();
	       			 }else{
	            			malert('删除失败！！','top','defeadted');
	            	 }
	       		 });
            }
        },
    })
    var pop = new Vue({
        el: '#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            isShowpopL: false,
            isShow: false,
            title: '',
            popContent: {},
            centent: '',
            xm:{
            	bm:'',
            	mc:'',
            	flbm:'',
            	bbbm:'',
            	bgmc:'',
            	jyff:'',
            	tybz:'0',
            	dyhs:'',
            	fzbm:'',
            	jysb:'',
            	scfs:'',
            	dj:'',
            	bglx:'',
            	dyhs:'',
            	xssx:'',
            	sfjh:'0',
            	dyjyff:'0',
            },
            cs:{},
            fs:[
                {num:'0',text:'项目确定指标'},{num:'1',text:'结果确定指标'}
                ],
            lx:[
                {num:'1',text:'通用报告'},{num:'2',text:'sysmex xs-1000i'},{num:'3',text:'三分类血球仪(图片gif*3)'},{num:'4',text:'sysmex uf-500i'},{num:'5',text:'KES-900B血流变检测仪'},{num:'6',text:'酶标仪'},
                {num:'7',text:'微生物'},{num:'8',text:'大文本报告'},{num:'9',text:'AVE-763A尿沉渣'},{num:'10',text:'迈瑞BC5500'},{num:'11',text:'sysmex xs-800i'},{num:'12',text:'AC920E0+血球仪'},
                {num:'13',text:'龙鑫LX-3000i尿沉渣分析仪'},{num:'14',text:'ABX PENTRA60血球仪'},{num:'15',text:'瑞典Medonic血球仪'},{num:'16',text:'文本类型参考值报告'},{num:'17',text:'三分类血球仪(描点256*3)'},{num:'18',text:'ST_360酶标仪'},
                {num:'19',text:'百特1300三分类血球仪(描点128*3)'},{num:'20',text:'ABX MICROS 60 三分类血球仪'},{num:'21',text:'迈瑞BC5180血球仪'},{num:'22',text:'骨髓检查报告单'},{num:'23',text:'迈瑞BC5500血球仪(双栏)'},{num:'24',text:'迈瑞BC5300血球仪'},
                {num:'25',text:'AVE-763B尿沉渣'},{num:'26',text:'ABX PENTRA80血球仪'},{num:'27',text:'赛科希德SA-5600血流变'},{num:'28',text:'sysmex xs-800i_双栏'},{num:'29',text:'通用报告1'},{num:'30',text:'迈瑞BC5180血球仪_双栏'},{num:'31',text:'贝克曼LH750血球仪'},
                {num:'32',text:'雅培CELL-DYN1800血球仪'},{num:'33',text:'通用报告单栏'},{num:'34',text:'SYSMEX 2000I血球仪'},{num:'35',text:'迈瑞EH-2050A'},{num:'36',text:'乙肝五项'},{num:'37',text:'免疫定量结果显示定性'},{num:'38',text:'雷杜RT7600三分类'},{num:'39',text:'迈瑞EH-2080C'},
                {num:'40',text:'珠海科域KU-500尿沉渣'},{num:'41',text:'LTS-E100 粪便分析仪6图'},{num:'42',text:'珠海科域KU-1000尿沉渣'},{num:'43',text:'杭州天创微生物'},{num:'44',text:'Mejer1600美桥尿沉渣'}
                ]
        },
        created:function(){
        	 //检验分组、方法、分类、样本、设备
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmcsQuery" , function(json) {
        		console.log(json);
        		if(json.a=="0"){
        			pop.cs = json.d;
        		}
        	});
        },
        methods: {
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            save: function () {
            	//验证
            	if(typeof pop.xm.mc == 'undefined' || pop.xm.mc == null ||pop.xm.mc == ''){
	            	malert('请输入项目名称','top','defeadted');
	            	return;
            	}
            	if(typeof pop.xm.flbm == 'undefined' ||pop.xm.flbm == null ||pop.xm.flbm == ''){
            		malert('请选择检验分类','top','defeadted');
	            	return;
            	}
            	if(pop.xm.dj == ''){
            		malert('请填写单价','top','defeadted');
	            	return;
            	}
            	if(pop.xm.bglx == ''){
            		malert('请填写报告类型','top','defeadted');
            		return;
            	}
            	if(pop.xm.bglx == ''){
            		malert('请填写报告类型','top','defeadted');
            		return;
            	}
            	if(pop.xm.dyhs == ''){
            		malert('请填写打印行数','top','defeadted');
            		return;
            	}
            	if(pop.xm.fzbm == ''){
            		malert('请选择分组','top','defeadted');
            		return;
            	}
            	if(pop.xm.xssx == ''){
            		malert('请填写显示序号','top','defeadted');
            		return;
            	}
            	if(pop.xm.scfs == ''){
            		malert('请选择结果生成方式','top','defeadted');
            		return;
            	}
            	
            	console.log(pop.xm);
            	//保存操作 
        		var data = JSON.stringify(pop.xm);
            	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmInsertOrUpdate',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				jyx.getData();
          			malert('保存成功！！','top','success');
          			if(pop.title!='新增检验项目'){
                        $(".side-form-bg").removeClass('side-form-bg')
                        $(".side-form").addClass('ng-hide');
					}else{
                        pop.xm={};
					}
          			jyx.getData();
	       			 }else{
	            			malert('保存失败！！','top','defeadted');
	            	 }
	       		 });
            },
            //jyff
            jyff:function(){
            	pop.xm.dyjyff = pop.xm.dyjyff == '0'?'1':'0';
            },
            sfjh:function(){
            	pop.xm.sfjh = pop.xm.sfjh == '0'?'1':'0';
            },
            tybz:function(){
            	pop.xm.tybz = pop.xm.tybz == '0'?'1':'0';
            }
        },
    });
    var popCenter = new Vue({
        el: '#popCenter',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            isShowpopL: false,
            isShow: false,
            title: '',
            popContent: {},
            centent: ''
        },
        methods: {
            success: function () {
        		//批量停用
        		//保存操作
        		var data = '{"list":'+ JSON.stringify(wrapper.delList) +'}';
            	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmDelete',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				jyx.getData();
          			malert('删除成功！！','top','success');
          			$(".side-form-bg").removeClass('side-form-bg')
          			$(".side-form").addClass('ng-hide');
          			jyx.getData();
	       			 }else{
	            			malert('删除失败！！','top','defeadted');
	            	 }
	       		 });
                malert('删除成功', 'top', '')
                this.isShowpopL = false;
                this.isShow = false
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            }
        },
    });
    document.onkeydown = function (ev) {
        var ev = window.event || ev
        var key = ev.keyCode
        if (key == 83 && ev.ctrlKey) {
            return false
        }
    }
    $(".jyxm input").uicomplete({
        iskeyup: false
    });
    
  //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
    	if($(this).val() == '' || $(this).val() == null) {
    		$(this).addClass("emptyError");
    	} else {
    		$(this).removeClass("emptyError");
    	}
    });
    
    //加载数据
    jyx.getData();
