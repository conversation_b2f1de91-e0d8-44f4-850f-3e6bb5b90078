/**
 * Created by mash on 2017/10/8.
 */
    var zl_toolMenu = new Vue({
        el: '.zl_toolMenu',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
        	param: {
				page: 1,
				rows: 10,
				parm: ''
			},
            fylbsList:[],
            bxlbbm: null,
            bxurl: null,
            searchtext: null,
            type:'qb',
            dmzt_tran:{
                'qb':'全部',
                'yd':'已对码',
                'wd':'未对码',
            },
        },
        methods: {
            getFylb:function(){
                var param=JSON.parse(JSON.stringify(this.param))
                param.rows=1000;
                var that=this;
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmFylb&types=query&dg=" + JSON.stringify(param), function (json) {
                    that.totlePage = Math.ceil(json.d.total / that.param.rows);
                    that.fylbsList = json.d.list;
                });
            },
            sschangeDown: function () {
                if (window.event.keyCode == 13) {
                    zl_toolMenu.searchtext = $('#search').val();
                    zl_toolMenu.getData();
                }
            },
            changeType:function(xType){
            	zl_toolMenu.type=xType[0];
            	zl_toolMenu.getData();
            },
            getbxlb: function () {
                var param = {bxjk: "001"};
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            if (json.d.list.length > 0) {
                                zl_toolMenu.bxlbbm = json.d.list[0].bxlbbm;
                                zl_toolMenu.bxurl = json.d.list[0].url;
                            }
                        } else {
                            malert("保险类别查询失败!" + json.c)
                        }
                    });
            },
            commonResultChange:function(val){
                Vue.set(zlXmMx.param, 'lbbm', val[0]);
                Vue.set(this.param, 'lbbm', val[0]);
                this.getData();
            },
            // 请求保险类别
            getData: function () {
//                var param = {
//                    page: 1,
//                    rows: 500,
//                    parm: zl_toolMenu.searchtext
//                };
                zlXmMx.param.fpks=null;
                zlXmMx.param.fpfs=null;
            	 if(zl_toolMenu.type=='yd'){
                     zlXmMx.param.fpks='1';
                 }
                 if(zl_toolMenu.type=='wd'){
                     zlXmMx.param.fpfs='1';
                 }
                zlXmMx.param.parm=zl_toolMenu.searchtext;
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=queryZl&parm=" + JSON.stringify(zlXmMx.param), function (json) {
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            zlXmMx.totlePage = Math.ceil(res.total / zlXmMx.param.rows);
                            zlXmMx.jsonList = res.list;
                        } else {
                            malert(json.c);
                        }
                    });
            },
            // 保存项目详情
            save: function (bxlbbm, fyxmbm, bxxmlb, bxxmbm, bxxmmc) {
                var param = {
                    'page': 1,
                    'rows': 30,
                    'bxlbbm': bxlbbm,
                    'fyxmbm': fyxmbm,
                    'bxxmlb': bxxmlb,
                    'bxxmbm': bxxmbm,
                    'bxxmmc': bxxmmc
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=saveZl&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            malert("保存诊疗项目成功！");
//                        		zl_toolMenu.getData();
                        } else {
                            malert(json.c);
                        }
                    });
            },
            // 删除项目详情
            remove: function () {

            },
            //获取诊疗项目
            loadXm: function () {
                var param = {
                    page: 1,
                    rows: 30,
                    bxlbbm: zl_toolMenu.bxlbbm
                };
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=getZl&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            malert("获取诊疗项目成功！");
                            zl_toolMenu.getData();
                        } else {
                            malert(json.c);
                        }
                    });

            },
            //自动对码（项目名称）
            autoDm: function () {
              var param = {
                  page: 1,
                  rows: 30
              };
              $.getJSON(
                  "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=nhdm&method=autoDmZl&parm=" + JSON.stringify(param), function (json) {
                      if (json.a == 0) {
                          malert("自动对码（项目名称）成功！");
                          zl_toolMenu.getData();
                      } else {
                          malert(json.c);
                      }
                  });
            }
        }
    });
    zl_toolMenu.getbxlb();
    zl_toolMenu.getFylb();

    var zlBxXm = new Vue({
        el: '.zlBxXm',
        mixins: [tableBase, baseFunc],
        data: {
            jsonList: [],
            searchCon: [],
            param: {},
        	page: {
				page: 1,
				rows: 20,
				total: null
			},
        },
        updated:function(){
            changeWin()
        },
        methods: {
            getData: function () {
                var param = {
                    bxjk: '001'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                    zlBxXm.totlePage = Math.ceil(json.d.total / zlBxXm.param.rows);
                    zlBxXm.jsonList = json.d.list;
                });
            },
            getMx: function () {
                zl_toolMenu.getData();
            }
        }
    });
    zlBxXm.getData();

    var zlXmMx = new Vue({
        el: '.zlXmMx',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        updated:function(){
            changeWin()
        },
        data: {
        	qjIndex:null,
            jsonList: [],
            isEdit: null,
            text: null,
            page: {
                page: 1,
                rows: 50,
                total: null
            },
            param:{},
            popContent: {},
            searchCon: {},
            selSearch: -1,
            dg: {page: 1, rows: 50, sort: "", order: "asc", parm: ""},
            them_tran: {
                'fw': dic_transform.data.nhtclb_tran
            },
            them: {'项目编码': 'bm', '项目名称': 'mc', '项目类别': 'xmlb', '范围': 'fw','批准文号':'pzwh','报销比例':'bxbl','规格':'gg','生产厂家':'scdw'}
        },
        methods: {
            getData:function(){
                zl_toolMenu.getData();
            },
            edit: function (index) {
                console.log(index);
                this.isEdit = index;
            },
            // 点击进行赋值的操作
            selectOne: function (item, index) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching(zlXmMx.qjIndex, true,'bxxmmc',zlXmMx.jsonList[zlXmMx.qjIndex].bxxmmc); // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    zlXmMx.popContent = item;
                    Vue.set(zlXmMx.jsonList[zlXmMx.qjIndex], 'bxxmmc', zlXmMx.popContent['mc']);
                    zlXmMx.jsonList[zlXmMx.qjIndex].bxxmbm=this.popContent.bm;
                    zlXmMx.jsonList[zlXmMx.qjIndex].bxxmlb=this.popContent.xmlb;
                    zlXmMx.jsonList[zlXmMx.qjIndex].fw=this.popContent.fw;
                    zl_toolMenu.save(zl_toolMenu.bxlbbm, zlXmMx.jsonList[zlXmMx.qjIndex]['xmbm'], zlXmMx.jsonList[zlXmMx.qjIndex]['bxxmlb'], zlXmMx.jsonList[zlXmMx.qjIndex]['bxxmbm'], zlXmMx.jsonList[zlXmMx.qjIndex]['bxxmmc']);
                    $(".selectGroup").hide();
                }
            },
            changeDown: function (index, event, type) {
                //if (this['searchCon'][this.selSearch] == undefined) return;
                // this.keyCodeFunction(event, 'popContent', 'searchCon');
                this.inputUpDown(event,'searchCon','selSearch')
                this.popContent=this['searchCon'][this.selSearch]
                if (event.code == 'Enter' || event.keyCode == 13 || event.code=='NumpadEnter') {
                    if(zlXmMx.popContent){
                        Vue.set(zlXmMx.jsonList[index], 'bxxmmc', zlXmMx.popContent['mc']);
                        Vue.set(zlXmMx.jsonList[index], 'bxxmbm', zlXmMx.popContent['bm']);
                        Vue.set(zlXmMx.jsonList[index], 'bxxmlb', zlXmMx.popContent['xmlb']);
                        Vue.set(zlXmMx.jsonList[index], 'fw', zlXmMx.popContent['fw']);
                        zl_toolMenu.save(zl_toolMenu.bxlbbm, zlXmMx.jsonList[index]['xmbm'], zlXmMx.jsonList[index]['bxxmlb'], zlXmMx.jsonList[index]['bxxmbm'], zlXmMx.jsonList[index]['bxxmmc']);
                        this.selSearch=-1
                    }else{
                        zl_toolMenu.save(zl_toolMenu.bxlbbm, zlXmMx.jsonList[index]['xmbm'], zlXmMx.jsonList[index]['bxxmlb'], zlXmMx.jsonList[index]['bxxmbm'], zlXmMx.jsonList[index]['bxxmmc']);
                    }
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                }
            },
            // 输入内容进行检索
            searching: function (index, add, type,val) {
            	zlXmMx.qjIndex=index;
            	this.jsonList[index]['bxxmmc'] = val;
            	this.jsonList[index]['bxxmbm'] = '';
            	this.jsonList[index]['mc'] = '';
            	this.jsonList[index]['fw'] = '';
            	this.jsonList[index]['fygg'] = '';
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                zlXmMx.popContent = {};
                zlXmMx.dg['parm'] = val;
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=xmbm&method=query&parm=" + JSON.stringify(zlXmMx.dg), function (json) {
                        if (json.a == 0) {
                            var res = eval('(' + json.d + ')');
                            if (add) {
                                for (var i = 0; i < res.list.length; i++) {
                                    zlXmMx.searchCon.push(res.list[i]);
                                }
                            } else {
                                zlXmMx.searchCon = res.list;
                            }
                            zlXmMx.page.total = res.total;
                            // zlXmMx.selSearch = 0;
                            if (res.list.length > 0 && !add) {
                                $(".selectGroup").hide();
                                _searchEvent.show();
                            }
                        } else {
                            malert(json.c);
                        }
                    });
            }
        }
    });

    $('body').click(function () {
        $(".selectGroup").hide();
    });

    $(".selectGroup").click(function (e) {
        e.stopPropagation();
    });
