var payNo = {
    data: {
        zflxList: [],
        jsjlContent: {},
        resObj: {},
        focus: false,
        codeNum: 0,
        codeContent: '',
    },
    computed: {
        codeContentFun: function () {
            if (this.codeContent.length == 18) {
                this.payment()
            } else if (this.codeNum > 3) {
                conBtu.ifClick = true;
                malert('你已经连续扫码错误三次，请重新确认', 'right', 'defeadted');
            } else if (this.codeNum < 3) {
                ++this.codeNum
            }
        },
    },
    methods: {
        blurFun: function (event) {
            if (this.focus && this.codeContent.length != 18) {
                $('#codeContent').focus();
            }
        },
        // 页面加载时自动获取支付类型数据
        GetZflxData: function () {
            var dg = {
                "page": 1,
                "rows": 20000,
                "sort": "",
                "order": "asc"
            };
            this.updatedAjax("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx&dg=" + JSON.stringify(dg), function (json) {
                if (json.a == 0 && json.d.list.length) {

                    this.zflxList = json.d.list;
                    this.zflxList.push({
                        beginrq: null,
                        endrq: null,
                        order: "asc",
                        page: 1,
                        parm: null,
                        pydm: "YB",
                        qtzfxm: null,
                        rows: 20000,
                        sfxj: "1",
                        sort: "",
                        tybz: "0",
                        yjjlx: "1",
                        yjpx: null,
                        yljgbm: "000001",
                        zflxbm: "8888",
                        zflxjk: null,
                        zflxmc: "医保",
                    })
                    this.jsjlContent.zflxbm = this.zflxList[0].zflxbm;

                } else {
                    malert(json.c + ",支付类型列表查询失败", 'right', 'defeadted');
                    return false;
                }
            });
        },
        closeFun: function () {
            this.bxShow = false;
            this.ifClick = true;
        },
        saveGh: function () {
            var zflxjk = this.listGetName(this.zflxList, this.jsjlContent.zflxbm, 'zflxbm', 'zflxjk');
            if (zflxjk == '008') {
                if (this.jsjlContent.zflxbm != '27') {
                    $('#codeContent').focus();
                    this.focus = true;
                    common.openloading("", "请出示付款码。。。。");
                } else {
                    this.payment()
                }
            } else if (this.jsjlContent.zflxbm == '8888') {


                //如果是走 医保 就需要先去获取 医保数据
                if (!contextInfo.ybbx) {
                    malert("请获取医保数据", 'right', 'defeadted');
                    return false;
                }
                var str_param = {
                    list: [{}]
                };

                let ghxh = '';
                //开始交易
                let ryxx = JSON.parse(sessionStorage.getItem('hzybxx'));
                let ret1 = window.insuranceGbUtils.call('2207A', {
                    card_token: ryxx.card_token,
                    enddate: contextInfo.fDate(new Date(), 'date'),
                    adsetl_codg: '0',
                    rea: '',
                    fixmedins_code: window.insuranceGbUtils.fixmedins_code,
                    psn_no: cd_014.grxxJson.aac001,
                    mdtrtarea_admvs: window.insuranceGbUtils.mdtrtarea_admvs,
                    local_type: '3',
                    out_type: '2',
                }, cd_014.sfyd, cd_014.userInfo, '01301');
                $.ajaxSettings.async = false;

                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=getGhxh&ghrq=" + contextInfo.json.ghrq,
                    function (data) {
                        if (data.a == 0) {
                            ghxh = data.d;
                            if (ret1) {
                                let jyxx = JSON.parse(sessionStorage.getItem('jyjbxx'));
                                let med_type = '12';
                                let brfyjsonList = [];
                                for (let i = 0; i < contextInfo.brfyList.length; i++) {
                                    let brfyjson = {
                                        mxfyxmbm: contextInfo.brfyList[i].mxfybm,
                                        yzlx: '1',
                                        yzhm: '',
                                        fysl: contextInfo.brfyList[i].fysl,
                                        mzks: contextInfo.json.ghks,
                                        mzksmc: contextInfo.json.ghksmc,
                                        mzys: contextInfo.json.jzys,
                                        mzysxm: contextInfo.json.jzysxm,
                                        sfsj: new Date(),
                                        fydj: contextInfo.brfyList[i].fydj,
                                    }
                                    brfyjsonList.push(brfyjson)
                                }
                                let yhResult = cd_014.mzyjs014V3(brfyjsonList);
                                console.log(yhResult)
                                let param = {
                                    "data": {
                                        "psn_no": cd_014.grxxJson.aac001, //人员编号
                                        "insutype": cd_014.grxxJson.ykc303, //险种类型
                                        "begntime": contextInfo.fDate($('#ghrq').val(), 'All'), //挂号时间
                                        "mdtrt_cert_type": jyxx.mdtrt_cert_type, //就诊凭证类型
                                        "mdtrt_cert_no": jyxx.mdtrt_cert_no,//就诊凭证编号
                                        "ipt_otp_no": ghxh, //门诊号
                                        "atddr_no": contextInfo.json.jzys, //医生编码
                                        "dr_name": contextInfo.json.jzysxm, //医生姓名
                                        "dept_code": contextInfo.json.ghks, //科室编码
                                        "dept_name": contextInfo.json.ghksmc, //科室名称
                                        "caty": contextInfo.json.ghks, //科别
                                        "exp_content": {
                                            "card_token": jyxx.card_token
                                        },
                                    },
                                };
                                let data1 = window.insuranceGbUtils.call1("2201", param, ryxx.insuplc_admdvs, cd_014.sfyd)
                                if (!data1) {
                                    conBtu.ifClick = true;
                                    malert("上传挂号信息失败", 'right', 'defeadted');
                                    return false;
                                }
                                // let param1 = {
                                // 		"mdtrtinfo" :{
                                // 			"psn_no":cd_014.grxxJson.aac001, //人员编号
                                // 			"mdtrt_id":data1.data.mdtrt_id, //就诊id
                                // 			"begntime":contextInfo.fDate($('#ghrq').val(),'All'), //接诊时间
                                // 			"med_type":med_type, //医疗类别
                                // 			"main_cond_dscr":'',//主诉
                                // 			"dise_codg":'', //病种编码
                                // 			"dise_name":'', //病种编码
                                // 			"birctrl_type":'', //生育门诊需要
                                // 			"birctrl_matn_date":'', //计划生育手术或生育日期
                                // 			"exp_content":{
                                // 				"card_token":jyxx.card_token
                                // 			},
                                // 		},
                                // 		"diseinfo" :[],
                                // 	}
                                // 	param1.diseinfo.push({
                                // 		"diag_type":'1', //
                                // 		"diag_srt_no":1, //
                                // 		"diag_code":'', //
                                // 		"diag_name":'', //
                                // 		"diag_dept":contextInfo.json.ghks,//
                                // 		"dise_dor_no":contextInfo.json.ghksmc, //
                                // 		"dise_dor_name":contextInfo.json.jzysxm, //
                                // 		"diag_time":contextInfo.fDate($('#ghrq').val(),'All'), //
                                // 		"vali_flag":'1', //
                                // 	})
                                // let data2203a = window.insuranceGbUtils.call1("2203A",param1,ryxx.insuplc_admdvs)
                                // if(!data2203a){
                                // 	let param_02 = {
                                // 		"data" :{
                                // 			"psn_no":cd_014.grxxJson.aac001, //人员编号
                                // 			"mdtrt_id":data1.data.mdtrt_id, //就诊id
                                // 			"ipt_otp_no":ghxh, //门诊号
                                // 			"exp_content":{
                                // 				"card_token":jyxx.card_token
                                // 			},
                                // 		},
                                // 	}

                                // 	 window.insuranceGbUtils.call1("2202",param_02,ryxx.insuplc_admdvs)
                                // 	 conBtu.ifClick = true;
                                // 	return false;
                                // }
                                //个人医保帐户支付总额,社保基金支付总额,现金及其他支付;用于结算框的展示
                                let yka065 = 0, yka107 = 0, ykh012 = 0;
                                for (let i = 0; i < yhResult.length; i++) {
                                    let tpxx = yhResult[i];
                                    let fyzh = 0.00; //计算费用总和
                                    let chrg_bchno = tpxx[0].yke134
                                    if (!chrg_bchno) {
                                        chrg_bchno = new Date().getTime();
                                    }
                                    //计算总费用
                                    tpxx.forEach(function (current, i) {
                                        var n1 = new BigNumber(current.yka055);
                                        current.yka055 = n1.toFixed(2);
                                        fyzh = cd_014.MathAdd(fyzh, current.yka055);

                                    });
                                    let param_04 = {
                                        feedetail: [],
                                    }
                                    for (let k = 0; k < tpxx.length; k++) {
                                        let tpsfpch = tpxx[k].yke134;
                                        if (!tpsfpch) {
                                            tpsfpch = chrg_bchno
                                        }
                                        param_04.feedetail.push({
                                            feedetl_sn: tpxx[k].yka105, //费用明细流水号,
                                            mdtrt_id: data1.data.mdtrt_id, //就诊id
                                            psn_no: cd_014.grxxJson.aac001, //人员编号
                                            chrg_bchno: tpsfpch, //收费批次号
                                            dise_codg: '', //病种编码
                                            rxno: tpxx[k].yke134, //处方号
                                            rx_circ_flag: '0', //外购标志
                                            fee_ocur_time: cd_014.fDate(tpxx[k].yke123, 'datetime'), //费用发生时间
                                            med_list_codg: tpxx[k].yka094, //医疗目录编码,
                                            medins_list_codg: tpxx[k].yka094, //医药机构目录编码
                                            det_item_fee_sumamt: tpxx[k].yka055, //明细费用项目总额
                                            cnt: tpxx[k].akc226, //数量
                                            pric: tpxx[k].akc225, //单价
                                            sin_dos_dscr: tpxx[k].yke352, //单次剂量描述
                                            used_frqu_dscr: tpxx[k].yke350, //频次
                                            prd_days: tpxx[k].yke446, //天数
                                            medc_way_dscr: tpxx[k].yke201, //用药方法
                                            bilg_dept_codg: tpxx[k].yka097, //开单科室编码
                                            bilg_dept_name: tpxx[k].yka098, //开单科室名称
                                            bilg_dr_codg: tpxx[k].ykf008, //开单医生编码
                                            bilg_dr_name: tpxx[k].yka099, //开单医生姓名
                                            acord_dept_codg: tpxx[k].yka100, //受单科室编码
                                            acord_dept_name: tpxx[k].yka101, //受单科室名称
                                            orders_dr_code: tpxx[k].ykf008, //受单医生编码
                                            orders_dr_name: tpxx[k].yka102, //受单医生姓名
                                            hosp_appr_flag: tpxx[k].yke186, //医院审批标志
                                            tcmdrug_used_way: tpxx[k].yke201, //中药使用方式
                                            etip_flag: '0', //外检标志
                                            etip_hosp_code: '', //外检医院编码
                                            dscg_tkdrug_flag: '0', //出院带药标志
                                            matn_fee_flag: '', //生育费用标志
                                            comb_no: '', //组套编号
                                            "exp_content": {
                                                "card_token": jyxx.card_token
                                            },
                                        })
                                    }
                                    let data2204 = window.insuranceGbUtils.call1("2204", param_04, ryxx.insuplc_admdvs, cd_014.sfyd);

                                    if (!data2204) {
                                        conBtu.ifClick = true;
                                        return false;
                                    }

                                    let param_06a = {
                                        data: {
                                            "psn_no": cd_014.grxxJson.aac001, //人员编号
                                            "mdtrt_cert_type": jyxx.mdtrt_cert_type, //就诊凭证类型
                                            "mdtrt_cert_no": jyxx.mdtrt_cert_no,//就诊凭证编号
                                            "med_type": med_type, //医疗类别
                                            "medfee_sumamt": cd_014.MathFun(fyzh), //费用总额
                                            "psn_setlway": '01', //个人结算方式
                                            "mdtrt_id": data1.data.mdtrt_id, //就诊id
                                            "chrg_bchno": chrg_bchno, //收费批次号
                                            "acct_used_flag": '1', //个人账户使用类型
                                            "insutype": cd_014.grxxJson.ykc303,//险种类型
                                            "exp_content": {
                                                "card_token": jyxx.card_token,
                                                'enddate': '',
                                                'adsetl_codg': '',
                                                'rea': '',
                                            },
                                        }
                                    }
                                    let data2206 = window.insuranceGbUtils.call1("2206A", param_06a, ryxx.insuplc_admdvs, cd_014.sfyd);
                                    if (!data2206) {
                                        conBtu.ifClick = true;
                                        return false;
                                    }


                                    let param_07a = {
                                        data: {
                                            "psn_no": cd_014.grxxJson.aac001, //人员编号
                                            "mdtrt_cert_type": jyxx.mdtrt_cert_type, //就诊凭证类型
                                            "mdtrt_cert_no": jyxx.mdtrt_cert_no,//就诊凭证编号
                                            "med_type": med_type, //医疗类别
                                            "medfee_sumamt": cd_014.MathFun(fyzh), //费用总额
                                            "psn_setlway": '01', //个人结算方式
                                            "mdtrt_id": data1.data.mdtrt_id, //就诊id
                                            "chrg_bchno": chrg_bchno, //收费批次号
                                            "acct_used_flag": '1', //个人账户使用类型
                                            "insutype": cd_014.grxxJson.ykc303,//险种类型
                                            "invono": '',
                                            "fulamt_ownpay_amt": '',
                                            "overlmt_selfpay": '',
                                            "preselfpay_amt": '',
                                            "inscp_scp_amt": '',
                                            "pub_hosp_rfom_flag": '',
                                            "exp_content": {
                                                "card_token": jyxx.card_token,
                                                'enddate': '',
                                                'adsetl_codg': '',
                                                'rea': '',
                                            },
                                        }
                                    }
                                    let data2 = window.insuranceGbUtils.call1("2207A", param_07a, ryxx.insuplc_admdvs, cd_014.sfyd);
                                    if (!data2) {
                                        conBtu.ifClick = true;
                                        malert("保险内部错误,不能完成结算，请稍后重试或反馈IT", 'top', 'defeadted');
                                        return false;
                                    } else {
                                        data2.setlinfo.ghxh = ghxh;
                                        data2.setlinfo.yke134 = tpxx[0].yke134;
                                        contextInfo.gbjsxx.push(data2.setlinfo)

                                        yka065 = cd_014.MathAdd(yka065, data2.setlinfo.acct_pay);
                                        yka107 = cd_014.MathAdd(yka107, data2.setlinfo.fund_pay_sumamt);
                                        ykh012 = cd_014.MathAdd(ykh012, data2.setlinfo.psn_cash_pay);
                                        if (i == 0) {
                                            let jzxx = {
                                                rybh: cd_014.grxxJson.aac001,
                                                ryzjlx: cd_014.syxx.baseinfo.psn_cert_type,
                                                zjhm: cd_014.syxx.baseinfo.certno,
                                                ryxm: cd_014.syxx.baseinfo.psn_name,
                                                xb: cd_014.syxx.baseinfo.gend,
                                                mz: cd_014.syxx.baseinfo.naty,
                                                csrq: cd_014.syxx.baseinfo.brdy,
                                                nl: cd_014.syxx.baseinfo.age,
                                                ye: cd_014.grxxJson.ykc194,
                                                rylb: ryxx.psn_type,
                                                rycbzt: ryxx.psn_insu_stas,
                                                xzlx: ryxx.insutype,
                                                grcbrq: ryxx.psn_insu_date,
                                                ztcbrq: ryxx.paus_insu_date,
                                                gwybz: ryxx.cvlserv_flag,
                                                cbdqybqh: ryxx.insuplc_admdvs,
                                                dwmc: ryxx.emp_name,
                                                rysfdj: cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].psn_type_lv ? cd_014.syxx.idetinfo[0].psn_type_lv : '',
                                                rysflb: cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].psn_idet_type ? cd_014.syxx.idetinfo[0].psn_idet_type : '',
                                                ksrq: cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].begntime ? cd_014.syxx.idetinfo[0].begntime : '',
                                                jzpzlx: ryxx.mdtrt_cert_type,
                                                jzpzbh: ryxx.mdtrt_cert_no,
                                                mzh: ghxh,
                                                zzysbm: param.data.atddr_no,
                                                zzysxm: param.data.dr_name,
                                                ryksbm: param.data.dept_code,
                                                ryksmc: param.data.dept_name,
                                                kb: param.data.caty,
                                                jzid: data1.data.mdtrt_id,
                                                yllb: param_06a.data.med_type,
                                                zybqms: '',
                                                bzbm: '',
                                                bzmc: '',
                                                jhsysslb: '',
                                                jhsyssrq: '',
                                                sylb: '',
                                                yzs: '',
                                                zfbz: '0',
                                                zfr: '',
                                                zfrq: '',
                                                bz: '',
                                                ywlsh: '',
                                                mtbbah: '',
                                                jsrq: contextInfo.fDate(new Date(), 'All'),
                                            }
                                            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjzxx&parm="
                                                + JSON.stringify(jzxx),
                                                function (json) {
                                                    if (json.a != 0) {
                                                        malert("保存失败  " + json.c, 'right', 'defeadted');
                                                    }
                                                });
                                        }
                                        let jsxx = {
                                            jzid: data2.setlinfo.mdtrt_id,
                                            jsid: data2.setlinfo.setl_id,
                                            mz: data2.setlinfo.naty,
                                            csrq: data2.setlinfo.brdy,
                                            nl: data2.setlinfo.age,
                                            xzlx: data2.setlinfo.insutype,
                                            rylb: data2.setlinfo.psn_type,
                                            gwybz: data2.setlinfo.cvlserv_flag,
                                            jssj: data2.setlinfo.setl_time,
                                            jzpzlx: data2.setlinfo.mdtrt_cert_type,
                                            yllb: data2.setlinfo.med_type,
                                            ylfze: data2.setlinfo.medfee_sumamt,
                                            cxjzffy: data2.setlinfo.overlmt_selfpay,
                                            xxzfje: data2.setlinfo.preselfpay_amt,
                                            fhzcfwje: data2.setlinfo.inscp_scp_amt,
                                            jbylbxtcjjzc: data2.setlinfo.hifp_pay,
                                            jbylbxtcjjzfbl: data2.setlinfo.pool_prop_selfpay,
                                            gwyylbzzjzc: data2.setlinfo.cvlserv_pay,
                                            qybcylbxjjzc: data2.setlinfo.hifes_pay,
                                            jmdbbxzjzc: data2.setlinfo.hifmi_pay,
                                            zgdeylfybzjjzc: data2.setlinfo.hifob_pay,
                                            yljzjjzc: data2.setlinfo.maf_pay,
                                            qtzc: data2.setlinfo.oth_pay,
                                            jjzfze: data2.setlinfo.fund_pay_sumamt,
                                            sjzfqfx: data2.setlinfo.act_pay_dedc,
                                            grfdzje: data2.setlinfo.psn_part_amt,
                                            ryzjlx: data2.setlinfo.psn_cert_type,
                                            grzhzc: data2.setlinfo.acct_pay,
                                            rybh: data2.setlinfo.psn_no,
                                            qzfje: data2.setlinfo.fulamt_ownpay_amt,
                                            zjhm: data2.setlinfo.certno,
                                            zrxjzc: data2.setlinfo.psn_cash_pay,
                                            yyfdje: data2.setlinfo.hosp_part_amt,
                                            xb: data2.setlinfo.gend,
                                            ye: data2.setlinfo.balc,
                                            ryxm: data2.setlinfo.psn_name,
                                            grzhzjzfje: data2.setlinfo.acct_mulaid_pay,
                                            yyjgjsid: data2.setlinfo.medins_setl_id,
                                            qsjbjg: data2.setlinfo.clr_optins,
                                            qsfs: data2.setlinfo.clr_way,
                                            qslb: data2.setlinfo.clr_type,
                                            zfbz: '0',
                                            zfry: '',
                                            zfrq: '',
                                            jszt: '1',
                                            jsczy: userName,
                                            grjsfs: '01',
                                            sfpch: chrg_bchno,
                                            fph: '',
                                            cjrq: data2.setlinfo.setl_time,
                                        }
                                        console.log(JSON.stringify(jsxx));
                                        $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjsxx&parm="
                                            + JSON.stringify(jsxx),
                                            function (json) {
                                                if (json.a != 0) {
                                                    conBtu.ifClick = true;
                                                    malert("保存失败  " + json.c, 'right', 'defeadted');
                                                }
                                            });
                                        let jsjjfx = [];
                                        for (let i = 0; i < data2.setldetail.length; i++) {

                                            jsjjfx.push({
                                                jzid: data2.setlinfo.mdtrt_id,
                                                jsid: data2.setlinfo.setl_id,
                                                jjzflx: data2.setldetail[i].fund_pay_type,
                                                fhzcfwje: data2.setldetail[i].inscp_scp_amt,
                                                bckzfxeje: data2.setldetail[i].crt_payb_lmt_amt,
                                                jjzfje: data2.setldetail[i].fund_payamt,
                                                jjzflxmc: data2.setldetail[i].fund_pay_type_name,
                                                jsgcxx: data2.setldetail[i].setl_proc_info ? data2.setldetail[i].setl_proc_info.replace(/%/g, "/") : '',

                                            })
                                        }
                                        console.log(JSON.stringify(jsjjfx));
                                        if (jsjjfx.length > 0) {
                                            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjjfxxx&parm="
                                                + JSON.stringify(jsjjfx),
                                                function (json) {
                                                    if (json.a != 0) {
                                                        conBtu.ifClick = true;
                                                        malert("保存失败  " + json.c, 'right', 'defeadted');
                                                    }
                                                });
                                        }
                                    }
                                }
                            } else {
                                conBtu.ifClick = true;
                                malert("医保交易失败  " + json.c, 'right', 'defeadted');
                                return false;
                            }

                            for (let i = 0; i < contextInfo.gbjsxx.length; i++) {
                                var parm = {
                                    ybfyhj: contextInfo.gbjsxx[i].fund_pay_sumamt,
                                    ghxh: ghxh,  //'挂号序号';
                                    zfbz: '0',  //'作废标志';
                                    yljgbm: '000001',    //'医疗机构编码';
                                    aac003: contextInfo.gbjsxx[i].psn_name,
                                    skrgx: cd_014.grxxJson.skrgx,
                                    akc190: contextInfo.gbjsxx[i].mdtrt_id,    //'就诊编码';
                                    aka130: contextInfo.gbjsxx[i].insutype,    //'支付类别';
                                    ykd007: contextInfo.gbjsxx[i].insutype,    //'报销类型';
                                    aac001: contextInfo.gbjsxx[i].psn_no,    //'个人编码';
                                    akb020: window.insuranceGbUtils.fixmedins_code,    //'医院编码';
                                    yka103: contextInfo.gbjsxx[i].setl_id,    //'结算编号';
                                    yka055: contextInfo.gbjsxx[i].medfee_sumamt,    //'费用总额';
                                    yka056: contextInfo.gbjsxx[i].fulamt_ownpay_amt,    //'全自费';
                                    ykc303: contextInfo.gbjsxx[i].psn_type,    //'个人帐户种类';
                                    ykc194: contextInfo.gbjsxx[i].balc,    //'个人帐户余额';
                                    yka065: contextInfo.gbjsxx[i].acct_pay,    //'个人帐户支付总额';
                                    yka107: contextInfo.gbjsxx[i].fund_pay_sumamt,    //'社保基金支付总额';
                                    ykh012: contextInfo.gbjsxx[i].psn_cash_pay,    //'现金及其他自付';
                                    yab003: contextInfo.gbjsxx[i].medins_setl_id,    //'医保经办机构编号';
                                    aae011: userName,    //'经办人姓名';
                                    aae036: contextInfo.gbjsxx[i].setl_time,    //'经办时间';
                                    yka111: contextInfo.gbjsxx[i].inscp_scp_amt,    //'符合范围';
                                    yka057: contextInfo.gbjsxx[i].fulamt_ownpay_amt,    //'挂钩自付';
                                    grzhye: contextInfo.gbjsxx[i].balc,    //'个人账户余额';
                                    ykc117: contextInfo.gbjsxx[i].balc,    //'个人账户余额';
                                    akc021: cd_014.grxxJson.med_type,    //医疗人员类别
                                    jylsh: '', //'交易流水号';
                                    jyyzm: '', //'交易验证码';
                                    jslx: '',//结算类型
                                    ydbz: '',//异地标志
                                    ykc177: cd_014.grxxJson.ykc177, //公务员标志
                                    insuplcadmdvs: cd_014.grxxJson.insuplc_admdvs,//参保地区划
                                };
                                var dataset_rows = [];

                                dataset_rows.push({
                                    bah: contextInfo.gbjsxx[i].ghxh,
                                    bxjsh: contextInfo.gbjsxx[i].setl_id,
                                    jzlx: '0',
                                    yab139: cd_014.grxxJson.insuplc_admdvs,
                                    aka213: '',
                                    yka115: contextInfo.gbjsxx[i].act_pay_dedc,
                                    yka058: '',
                                    ykc125: '',
                                    yka107: contextInfo.gbjsxx[i].fund_pay_sumamt,
                                    yka065: contextInfo.gbjsxx[i].acct_pay,
                                    ykc121: '11',
                                    ykb037: contextInfo.gbjsxx[i].clr_optins,
                                    yka054: contextInfo.gbjsxx[i].clr_way,
                                    yka316: contextInfo.gbjsxx[i].clr_type,
                                    jssj: contextInfo.gbjsxx[i].setl_time,
                                    jsry: userId,
                                    jsryxm: userName,

                                })
                                parm.qslbmx = dataset_rows;
                                parm = '{"list":' + JSON.stringify(parm) + '}';
                                parm = parm.replace(/undefined/g, "");
                                parm = parm.replace(/NaN/g, "");
                                parm = parm.replace(/null/g, "");
                                var flag = true;
                                contextInfo.postAjax("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=mzjs", parm,
                                    function (data) {
                                        if (data.a == '0') {
                                            flag = true;

                                            contextInfo.outpId = contextInfo.gbjsxx[0].setl_id;
                                            malert(data.c, 'right', 'success');
                                        } else {
                                            flag = false
                                            conBtu.ifClick = true;
                                            malert(data.c, 'right', 'defeadted');

                                            //HIS结算失败后，调用医保冲红
                                            payNo.quxiao();
                                        }
                                    });

                                if (!flag) {
                                    conBtu.ifClick = true;
                                    return false
                                }
                            }
                        }
                    })
                this.doSaveBrghFun()

            } else {
                this.doSaveBrghFun()
            }
        },

        quxiao: function () {
            conBtu.ifClick = true;
            for (let i = 0; i < contextInfo.gbjsxx.length; i++) {

                let ryxx = JSON.parse(sessionStorage.getItem('hzybxx'));
                let jyxx = JSON.parse(sessionStorage.getItem('jyjbxx'));
                let param_08 = {
                    data: {
                        "psn_no": contextInfo.gbjsxx[i].psn_no, //人员编号
                        "setl_id": contextInfo.gbjsxx[i].setl_id, //结算id
                        "mdtrt_id": contextInfo.gbjsxx[i].mdtrt_id, //就诊id
                        "exp_content": {
                            "card_token": jyxx.card_token
                        },
                    }
                }
                let data = window.insuranceGbUtils.call1("2208", param_08, ryxx.insuplc_admdvs);
                if (!data) {

                    malert("取消交易失败", 'top', 'defeadted');
                    return false;
                }
                let jsxx = {
                    jzid: contextInfo.gbjsxx[i].mdtrt_id,
                    jsid: contextInfo.gbjsxx[i].setl_id,

                    zfbz: '1',
                    zfry: userName,
                    zfrq: this.fDate(new Date(), 'datetime'),

                }

                $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=upGbjsxx&parm="
                    + JSON.stringify(jsxx),
                    function (json) {
                        if (json.a != 0) {
                            malert("保存失败  " + json.c, 'right', 'defeadted');
                        }
                    });
            }
        },

        doSaveBrghFun: function () {
            if (!contextInfo.json.ghrq) {
                contextInfo.json.ghrq = $("#ghrq").val(); //手动给挂号日期赋值
            }
            contextInfo.json = Object.assign(contextInfo.json, this.resObj, this.jsjlContent)
            conBtu.doSaveBrgh()
        },
        payment: function () {
            this.resObj = {};
            var yylx = this.jsjlContent.zflxbm == '27' ? '00' : '02', resObj;
            var param = {
                czybm: userId,
                czyxm: userName,
                mzlx: this.mzlx,
                hisGrbh: '',
                bzsm: this.bzsm,
                inJson: {
                    yylx: yylx,
                    fyje: String(conBtu.totalMoney),
                    // fyje:'0.01',
                    zfcm: this.codeContent,
                    // yjyrq:"",
                    // yjyckh:"",
                    // ysddh:""
                },
                yljgbm: jgbm
            }
            param.inJson = JSON.stringify(param.inJson);
            this.postAjax("http://localhost:9001/posinterface/xf", JSON.stringify(param), function (json) {
                if (json.returnCode == 0) {
                    resObj = JSON.parse(json.outResult);
                    if (resObj.bank_code != '') {
                        malert(json.msgInfo, 'right');
                    } else {
                        malert(resObj.resp_chin, 'right', 'defeadted');
                        resObj = false;
                    }
                } else {
                    resObj = false;
                    malert(json.c, 'right', 'defeadted');
                    return false;
                }
            }, function () {
                common.closeLoading();
            });
            common.closeLoading();
            if (resObj) {
                if (this.jsjlContent.zflxbm == '27') {
                    this.resObj = {
                        yjyrq: resObj.txndate,//原交易日期
                        orderNo: resObj.refdata,//原交易参考号
                    };
                } else {
                    this.resObj = {
                        payNo: resObj.unionMerchant,//原交易参考号
                    };
                }
                this.doSaveBrghFun();
                this.focus = false;
            } else {
                conBtu.ifClick = true;
                return false;
            }

        },
    },
};
