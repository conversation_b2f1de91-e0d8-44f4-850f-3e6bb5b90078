var jbbm = {'治疗方式':'treatCode','疾病编码': 'icdAllNo', '疾病名称': 'icdName'};
var zlfs_tran = {'编码': 'code', '名称': 'name','拼音代码':'','医疗机构':''};
var gzjqnh = new Vue({
	el:'#gzjqnh',
    mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
	components:{
		"zlfssearch-table":searchTable,
        "jbsearch-table":searchTable
	},
	data:{
	    ysje:0,
		jsonList:[],
        bclbList:[],
        centerNoList:[],
        jbsearchCon:[],
        zlfsSearchCon:[],
        fybm:[],
        fycfh:[],
        jbthem: jbbm,
        zlfsthem: zlfs_tran,
        selSearch:-1,
        jbbmContent:{},
        zlfsContent:{},
        brxx:{},
		chrxx:{
            bookNoType:"",
			bookNo:""
		},
        popContent:{
            brjbxxModel:{
                type:"1",
                jsbz:'0'
			}
        },
        page:{
	        page:1,
            rows:10,
            total:null
        },
        fychfStr:"",
        fybmStr:"",
        bookNoType_tran:{
            "1":"医疗卡（证）号",
            "2":"身份证号",
            "3":"监护人身份证",
            "4":"银行卡号",
        },
        jzlx_tran:{
            '1': '门诊',
            '2': '住院',
            '3': '体格检查',
            '4': '正常分娩住院',
            '9': '其他',
        },
        jzqk_tran: {
            '1': '危',
            '2': '急',
            '3': '一般',
            '4': '其他',
        },
	},
	methods:{
        // 请求保险类别
        getbxlb: function () {
            var param = {bxjk: "005"};
            common.openloading("#rydj_info");
            $.ajaxSettings.async = false;
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            gzjqnh.bxlbbm = json.d.list[0].bxlbbm;
                            gzjqnh.bxurl = json.d.list[0].url;
                        }
                        common.closeLoading();
                    } else {
                        malert("保险类别查询失败!" + json.c,"top","defeated")
                        common.closeLoading();
                    }
                });
        },
        getCenterNoList:function(){
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=commonBase&method=getCenterNoList&parm=" + JSON.stringify(gzjqnh.popContent.brjbxxModel),
                function(json) {
                    if(json.a == 0) {
                        var res = eval('(' + json.d + ')');
                        gzjqnh.centerNoList = res.list;
                    } else {
                        malert(json.c);
                    }
                });
        },
        //初始化保险页面信息
		getBrxxData:function(){
            if(rightVue.brxxContent){
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=clinic&method=queryMzdjxx&parm=" + JSON.stringify(rightVue.brxxContent),
                    function(json) {
                        if(json.a == '0') {
                            var data = JSON.parse(json.d);
                            if(data){
                                gzjqnh.popContent.brjbxxModel = data;
                                if(data.clinicNo!=null && data.clinicNo!='' && data.clinicNo!=undefined){
                                    gzjqnh.popContent.brjbxxModel.type = '2';
                                }
                                gzjqnh.popContent.brjbxxModel.bclbCode = data.redeemNo;
                                gzjqnh.popContent.brjbxxModel.code = data.treatCode;
                                rightVue.yjsContentGzjqnh.grbh = data.memberNo;
                                gzjqnh.popContent.brjbxxModel.cbnd = data.cbnd;
                            }else{
                                gzjqnh.popContent.brjbxxModel.bookNoType = "2";
                                gzjqnh.popContent.brjbxxModel.bookNo = rightVue.brxxContent.sfzh;
                                gzjqnh.popContent.brjbxxModel.cbnd = new Date().getFullYear();
                                gzjqnh.popContent.brjbxxModel.centerNo = '522401';
                            }
                            if(rightVue.brxxContent.brxm){
                                gzjqnh.popContent.brjbxxModel.ghxh = rightVue.brxxContent.ghxh;
                                gzjqnh.popContent.brjbxxModel.brxm = rightVue.brxxContent.brxm;
                                gzjqnh.popContent.brjbxxModel.ghksmc = rightVue.brxxContent.ghksmc;
                                gzjqnh.popContent.brjbxxModel.ghks = rightVue.brxxContent.ghks;
                                gzjqnh.popContent.brjbxxModel.ghrq = rightVue.brxxContent.ghrq;
                            }else{
                                gzjqnh.popContent.brjbxxModel.ghxh = rightVue.mzjbxxContent.ghxh;
                                gzjqnh.popContent.brjbxxModel.brxm = rightVue.mzjbxxContent.brxm;
                                gzjqnh.popContent.brjbxxModel.ghksmc = rightVue.mzjbxxContent.ghksmc;
                                gzjqnh.popContent.brjbxxModel.ghks = rightVue.mzjbxxContent.ghks;
                                gzjqnh.popContent.brjbxxModel.ghrq = gzjqnh.fDate(rightVue.mzjbxxContent.ghrq, "datetime");;
                            }
                            if(!rightVue.mzjbxxContent.jzysxm){
                                gzjqnh.popContent.brjbxxModel.clinicDoctor = rightVue.brxxContent.jzysxm;
                            }else{
                                gzjqnh.popContent.brjbxxModel.clinicDoctor = rightVue.mzjbxxContent.jzysxm;
                            }
                            console.log(JSON.stringify(gzjqnh.popContent.brjbxxModel));
                        } else {
                            malert(json.c,'top','defeated');
                        }
                    });
            }
		},
        commonResultChange:function(val){
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "bookNoType":
                    Vue.set(this.popContent.brjbxxModel, 'bookNoType', val[0]);
                    break;
				case "cureCode":
                    Vue.set(this.popContent.brjbxxModel, 'cureCode', val[0]);
					break;
                case "inHosId":
                    Vue.set(this.popContent.brjbxxModel, 'inHosId', val[0]);
                    break;
				case "bclbCode":
                    Vue.set(this.popContent.brjbxxModel, 'redeemNo', val[0]);
                    Vue.set(this.popContent.brjbxxModel, 'bclbCode', val[0]);
                    break;
                case "centerNo":
                    Vue.set(this.popContent.brjbxxModel, 'centerNo', val[0]);
                    break;
            }
        },
        selectJbbm: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change(true, 'jbbm', this.page.parm);           // 传参表示请求下一页,不传就表示请求第一页
                gzjqnh.selSearch = 0;
            } else {
                gzjqnh.popContent.brjbxxModel['firstIcdNo'] = item.icdAllNo;
                gzjqnh.popContent.brjbxxModel['firstIcdName'] = item.icdName;
                $(".selectGroup").hide();
                gzjqnh.selSearch = 0;
            }
        },
        //当输入值后才触发
        change: function (add, type, val) {
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            //病人基本信息检索
            if(type == 'zlfs'){
                this.page.parm = val;
                var str_param = {parm: val, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=inHospital&method=queryZlfs&parm="
                    + JSON.stringify(str_param),
                    function (data) {
                        data.d = JSON.parse(data.d);
                        if (data.d.list.length > 0) {
                            if (add) {//不是第一页则需要追加
                                for (var i = 0; i < data.d.list.length; i++) {
                                    gzjqnh.zlfsSearchCon.push(data.d.list[i]);
                                }
                            } else {//第一页则直接赋值
                                gzjqnh.zlfsSearchCon = data.d.list;
                            }
                        }
                        gzjqnh.page.total = data.d.total;
                        gzjqnh.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }
            if (type == 'jbbm') {
                this.page.parm = val;
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=inHospital&method=queryJbbmByZlfs&parm="
                    + JSON.stringify(str_param),
                    function (data) {
                        var res = JSON.parse(data.d);
                        if (add) {//不是第一页则需要追加
                            for (var i = 0; i < res.list.length; i++) {
                                gzjqnh.jbsearchCon.push(res.list[i]);
                            }
                        } else {
                            gzjqnh.jbsearchCon = res.list;
                        }
                        gzjqnh.page.total = res.total;
                        gzjqnh.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }
        },
        getPersonInfo: function(){
            if(gzjqnh.popContent.brjbxxModel.bookNoType == null || gzjqnh.popContent.brjbxxModel.bookNoType == '' || gzjqnh.popContent.brjbxxModel.bookNoType == undefined){
                malert("相关证件类型不能为空！");
                return false;
            }
            if(gzjqnh.popContent.brjbxxModel.bookNo == null || gzjqnh.popContent.brjbxxModel.bookNo == '' || gzjqnh.popContent.brjbxxModel.bookNo == undefined){
                malert("相关证件号码不能为空！");
                return;
            }
            if (gzjqnh.popContent.brjbxxModel.cbnd == null || gzjqnh.popContent.brjbxxModel.cbnd == undefined || gzjqnh.popContent.brjbxxModel.cbnd == "") {
                malert("参保年度不能为空！");
                return;
            }
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=commonBase&method=getPersonInfo&parm=" + JSON.stringify(gzjqnh.popContent.brjbxxModel),
                function(json) {
                    if(json.a == 0) {
                        var res = eval('(' + json.d + ')');
                        gzjqnh.jsonList = res.list;
                    } else {
                        gzjqnh.jsonList = [];
                        malert(json.c);
                    }
                });
        },
        edit: function(index){
            gzjqnh.popContent.brjbxxModel.memberNo = gzjqnh.jsonList[index].memberNO;
            gzjqnh.popContent.brjbxxModel.familyNo = gzjqnh.jsonList[index].familySysno;
        },
        getBclbbm:function(){
            var parm = {page:1,rows:9999,parm:""};
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=inHospital&method=queryBclb&parm=" + JSON.stringify(parm),
                function(json) {
                    if(json.a == 0) {
                        var res = eval('(' + json.d + ')');
                        gzjqnh.bclbList = res.list;
                    } else {
                        malert(json.c,'top','defeated');
                    }
                });
        },
        selectZlfs: function(item){
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change(true, 'zlfs', this.page.parm);           // 传参表示请求下一页,不传就表示请求第一页
                gzjqnh.selSearch = 0;
            } else {                            // 否则就是选中事件,为json赋值
                //清空入院诊断
                gzjqnh.popContent.brjbxxModel.code = item.code;
                gzjqnh.zlfsContent.treatCode = item.code;
                $(".selectGroup").hide();
                gzjqnh.selSearch = 0;
            }
        },
        changeDown: function (event,type, content, searchCon) {
            this.nextFocus(event,'',true);
            if (this[searchCon][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, content, searchCon);
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'text') {
                    gzjqnh.popContent.brjbxxModel = "";
                    gzjqnh.selSearch = 0;
                    this.nextFocus(event);
                }
                if (type == 'jbbm') {
                    gzjqnh.popContent.brjbxxModel.firstIcdNo = gzjqnh.jbbmContent['icdAllNo'];
                    gzjqnh.popContent.brjbxxModel.firstIcdName = gzjqnh.jbbmContent['icdName'];
                    gzjqnh.selSearch = 0;
                    this.nextFocus(event);
                }
                if (type == 'zlfs') {
                    gzjqnh.popContent.brjbxxModel.code = gzjqnh.zlfsContent['code'];
                    gzjqnh.jbbmContent.treatCode = gzjqnh.zlfsContent['code'];
                    gzjqnh.selSearch = 0;
                    this.nextFocus(event);
                }
                if(type == 'centerNo'){
                    gzjqnh.popContent.brjbxxModel.centerNo = gzjqnh.zlfsContent['code'];
                    this.nextFocus(event);
                }
            }
        },
        mzdj:function(){
            if(!mzdj_DataValid()){
                return false;
            }
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=clinic&method=clinicRegister&parm=" + JSON.stringify(gzjqnh.popContent.brjbxxModel),
                function(json) {
                    if(json.a == '0') {
                        var data = JSON.parse(json.d);
                        gzjqnh.popContent.brjbxxModel = data;
                        if(data.clinicNo){
                            gzjqnh.popContent.brjbxxModel.type = '2';
                        }
                        gzjqnh.popContent.brjbxxModel.bclbCode = data.redeemNo;
                        //给结算页面的个人编号赋值
                        rightVue.yjsContentGzjqnh.grbh = data.memberNo;
                        malert("操作成功！");
                    } else {
                        malert(json.c);
                    }
                });
        },
        qxmzdj:function(){
            $("#popCenter").hide();
            common.openConfirm('<textarea placeholder="确认取消门诊登记？请输入原因：" class="zui-textarea" style="width: 100%;height: 92px;text-indent:0;" id="cancelCauseText"></textarea>',function () {
                gzjqnh.popContent.brjbxxModel.cancelCause = $("#cancelCauseText").val();
                var json = JSON.stringify(gzjqnh.popContent.brjbxxModel);
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=clinic&method=cancelClinicRegister&parm="
                    + json,function (json) {
                    if (json.a == '0') {
                        gzjqnh.popContent.brjbxxModel = {};
                        gzjqnh.popContent.brjbxxModel.ghxh = rightVue.brxxContent.ghxh;
                        gzjqnh.popContent.brjbxxModel.brxm = rightVue.brxxContent.brxm;
                        gzjqnh.popContent.brjbxxModel.ghksmc = rightVue.brxxContent.ghksmc;
                        gzjqnh.popContent.brjbxxModel.ghks = rightVue.brxxContent.ghks;
                        gzjqnh.popContent.brjbxxModel.ghrq = rightVue.brxxContent.ghrq;
                        gzjqnh.popContent.brjbxxModel.type = '1';
                        gzjqnh.jsonList = [];
                        malert("取消门诊登记成功！");
                        $("#popCenter").show();
                    } else {
                        malert(json.c);
                        $("#popCenter").show();
                    }
                });
            },function () {
                $("#popCenter").show();
            },'')
        },
        nhyjs:function(){//农合预结算
            rightVue.bxcw = false;
            //查询门诊费用
            var str1 = "";
            var str2 = "";
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                if (rightVue.brfyjsonList[i].yzlx == '2' || rightVue.brfyjsonList[i].yzlx == null) {
                    str1 = rightVue.brfyjsonList[i].yzhm + "," + str1;
                } else {
                    str2 =  rightVue.brfyjsonList[i].mxfyxmbm + "|" + rightVue.brfyjsonList[i].fysl + "," + str2;
                }
            }
            str2 = str2.substr(0,str2.length-1);
            str1 = str1.substr(0,str1.length-1);
            if ((str1 == null || str1 == '')&& (str2 == null || str2=='')) {
                malert("无费用信息！", 'top', 'defeadted');
                return
            }
            gzjqnh.brxx = gzjqnh.popContent.brjbxxModel;
            gzjqnh.brxx.ysje = gzjqnh.ysje;
            gzjqnh.brxx.type = "1";
            gzjqnh.brxx.fycfhStr = str1;
            gzjqnh.brxx.fybmStr = str2;
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=clinic&method=preClinicCalculate&parm="
                + JSON.stringify(gzjqnh.brxx),function (json) {
                if (json.a == '0') {
                    var res = JSON.parse(json.d);
                    gzjqnh.brxx.sccgList = res.sccgList;
                    var yjsResMap = res.yjsResMap;
                    rightVue.yjsContentGzjqnh = yjsResMap;
                } else {
                    malert(json.c);
                }
            });
        },
        nhzsjs:function(){//正式结算
            gzjqnh.brxx.type = "2";
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=clinic&method=clinicCalculate&parm="
                + JSON.stringify(gzjqnh.brxx),function (json) {
                if (json.a == '0') {

                } else {
                    malert(json.c);
                }
            });
        },
        nhqxjs:function(){
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gzjqnh.bxurl + "&bxlbbm=" + gzjqnh.bxlbbm + "&types=clinic&method=cancelPreClinicCalculate&parm="
                + JSON.stringify(gzjqnh.popContent.brjbxxModel),function (json) {
                if (json.a == '0') {
                } else {
                    malert(json.c);
                }
            });
        },
	},
});


//保存时数据判断
function mzdj_DataValid() {
    var i = 0;
    var errString = "";
    if (gzjqnh.popContent.brjbxxModel.memberNo == null || gzjqnh.popContent.brjbxxModel.memberNo == undefined || gzjqnh.popContent.brjbxxModel.memberNo == "") {
        i++;
        errString += "</br>【个人编号】不能为空！";
    }
    if (gzjqnh.popContent.brjbxxModel.familyNo == null || gzjqnh.popContent.brjbxxModel.familyNo == undefined || gzjqnh.popContent.brjbxxModel.familyNo == "") {
        i++;
        errString += "</br>【家庭编号】不能为空！";
    }
    if (gzjqnh.popContent.brjbxxModel.centerNo == null || gzjqnh.popContent.brjbxxModel.centerNo == undefined || gzjqnh.popContent.brjbxxModel.centerNo == "") {
        i++;
        errString += "</br>【农合中心】不能为空！";
    }
    if (gzjqnh.popContent.brjbxxModel.cureCode == null || gzjqnh.popContent.brjbxxModel.cureCode == undefined || gzjqnh.popContent.brjbxxModel.cureCode == "") {
        i++;
        errString += "</br>【就诊类型】不能为空！";
    }
    if (gzjqnh.popContent.brjbxxModel.firstIcdNo == null || gzjqnh.popContent.brjbxxModel.firstIcdNo == undefined || gzjqnh.popContent.brjbxxModel.firstIcdNo == "") {
        i++;
        errString += "</br>【门诊诊断】不能为空！";
    }
    if (gzjqnh.popContent.brjbxxModel.redeemNo == null || gzjqnh.popContent.brjbxxModel.redeemNo == undefined || gzjqnh.popContent.brjbxxModel.redeemNo == "") {
        i++;
        errString += "</br>【补偿类别】不能为空！";
    }

    if (i > 0) {
        malert(errString,'top','defeadted');
        return false;
    } else {
        return true;
    }
}

laydate.render({
    elem: '#timeCbnd',
    rigger: 'click',
    theme: '#1ab394',
    type: 'year',
    done: function (value, data) {
        //应该在这个储存这个值
        gzjqnh.popContent.brjbxxModel.cbnd = value;
    }
});
gzjqnh.getbxlb();
gzjqnh.getBclbbm();
gzjqnh.getCenterNoList();
gzjqnh.getBrxxData();
