<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检验指标</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="jyzb.css" rel="stylesheet"/>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="add">新增指标</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="refresh">刷新</button>
            <button class="tong-btn btn-parmary-b " @click="save"><i class="icon-baocun paddr-r5"></i>保存</button>
            <button class="tong-btn btn-parmary-b " @click="delok"><i class="icon-sc-header paddr-r5"></i>删除</button>
            <button class="tong-btn btn-parmary-b " ><i class="icon-dr paddr-r5"></i>导入</button>
            <button class="tong-btn btn-parmary-b " @click="guolv()"><i class="icon-gl paddr-r5"></i>过滤</button>
            <button class="tong-btn btn-parmary-b " @click="dayin"><i class="icon-yl paddr-r5"></i>预览</button>
            <button class="tong-btn btn-parmary-b " @click="dayin"><i class="icon-dysq paddr-r5"></i>打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">搜&nbsp;索</label>
                    <div class="zui-input-inline margin-l13">
                        <input class="zui-input wh180" v-model="searchAll" placeholder="FAX" type="text"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                          :type="'all'" :val="isCheckAll">
                    </input-checkbox></div></th>
                    <th><div class="zui-table-cell cell-s"><span>编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>中文名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>英文名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>数据类型</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>参考值类型</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>小数位数</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>代码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table">
            <table class="zui-table" >
                <tbody>
                <tr  @dblclick="show($index)" :tabindex="$index" v-for="(item, $index) in jsonList"  @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zbbm">001</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zwmc">淋巴细胞比率</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ywmc">FGHJ</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="tran_dataType[item.sjlx]">数值</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.dw">10*9/L</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="tran_czkType[item.cklx]">通用</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.xsws">2</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.pydm">FGHJ</div>
                    </td>
                    <td>
                        <div class="switch cell-s" >
                            <input :id="'checked'+$index" type="checkbox" disabled v-model="item.tybz" true-value="0" false-value="1"  />
                            <label :for="'checked'+$index"></label>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-sc icon-font" @click="del($index)"></i></em>
                            </span>
                        </div>
                    </td>
               </tr>
               </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>


</div>
<!--侧边窗口-->
<div id="isTabel">
    <div class="pophide" :class="{'show':isShow}"></div>
    <div class="zui-form podrag  bcsz-layer zui-800 " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title ">过滤查询</div>
        <div class="guolv-xinzeng">
            <span class="layui-txt" @click="append()">新增一项</span>
            <i class="color-btn" @click="isShow=false"
               style="margin-top:-17px;width: 16px;height: 16px;display: inline-block;margin-left: 10px;float: right">×</i>
        </div>
        <div class="layui-layer-content">
            <div class=" layui-mad">
                <ul class="guolv-header guolv-style">
                    <li class="line">项目</li>
                    <li class="line">条件</li>
                    <li class="line">结果</li>
                    <li class="line">连接条件</li>
                    <li class="line">操作</li>
                </ul>
                <ui class="guolv-content" id="guo_append">
                    <div class="guolv-style guolv-bottom" v-for="(list,index) in appNum" :key="list.num">
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required"/>
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required"/>
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <!--<div class="zui-select-inline">-->
                            <!--<input type="text" class="zui-input lsittext" name="input1" check="required" />-->
                            <!--<div class="zui-select-group" role="listbox">-->
                            <!--<ul class="inner">-->
                            <!--<li value="0">中国</li>-->
                            <!--<li value="1">印度</li>-->
                            <!--<li value="2">安道尔</li>-->
                            <!--<li value="3">老挝</li>-->
                            <!--</ul>-->
                            <!--</div>-->
                            <input class="zui-input"/>

                            <!--</div>-->
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <input type="text" class="zui-input lsittext" name="input1" check="required"/>
                                <div class="zui-select-group" role="listbox">
                                    <ul class="inner">
                                        <li value="0">中国</li>
                                        <li value="1">印度</li>
                                        <li value="2">安道尔</li>
                                        <li value="3">老挝</li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li class="line">
                            <span class="icon-sc" @click="sc(index)"></span>
                        </li>
                    </div>

                </ui>
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="save()">保存</button>
        </div>
    </div>
</div>
<div id="popCenter">
    <div class="pophide" :class="{'show':isShow}"></div>
    <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}"
         style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin"><a class="layui-layer-ico layui-layer-close layui-layer-close1"
                                            href="javascript:;"></a></span>
        <div class="layui-layer-content">
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="isShowpopL=false,isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save xmzb-db" @click="delsave">保存</button>
        </div>
    </div>
</div>
<div id="pop" class="jszb">
    <transition name="left-fade">
        <!--:class="{'show':isShow}"-->
        <!--podrag-->
        <div role="form" id="brzcList" class="side-form ng-hide zui-form  pop-850 bcsz-layer " style="height: max-content;padding-bottom: 20px;height: 100%;padding-top: inherit;">
            <!--<div class="layui-layer-title " v-text="title"></div>-->
            <!--<span class="layui-layer-setwin" style="top: 0;">-->
                <!--<i class="color-btn" @click="close()">&times;</i></span>-->

            <div class="tab-message">
                <!--<a v-text="title"></a>-->
                <a v-text="title"></a>
                <a href="javascript:;" class="fr closex ti-close" style="color:rgba(255,255,255,.56) !important;" @click="close"></a>
            </div>
            <div class="ksys-side">
                    <ul class="tab-edit-list">
                        <li><i>指标编码</i>
                            <input v-model="zb.zbbm" readonly="readonly" type="number" class="zui-input">
                        </li>
                        <li><i>指标名称</i>
                            <input data-notempty="true" v-model="zb.zwmc" @blur="setPYDM(zb.zwmc, 'zb', 'pydm')" class="zui-input " type="text"></li>
                        <li><i>英文名称</i>
                            <input v-model="zb.ywmc" class="zui-input " type="text">
                        </li>
                        <li><i>拼音代码</i>
                            <input v-model="zb.pydm" class="zui-input" type="text">
                        </li>
                        <li><i>数据类型</i>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="sjlx" :index="'text'" :index_val="'num'"
                                          :val="zb.sjlx"  :name="'zb.sjlx'">
                            </select-input>
                        </li>
                        <li><i>参考类型</i>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="cklx" :index="'text'" :index_val="'num'"
                                          :val="zb.cklx":name="'zb.cklx'">
                            </select-input>
                        </li>
                        <li><i>单位</i>
                            <input v-model="zb.dw" class="zui-input">
                        </li>
                        <li><i>结果精度</i>
                            <input v-model="zb.xsws" type="number" class="zui-input ">
                        </li>
                        <li id="jgsel" v-show="isShowQsSe"><i>缺  省 值</i>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="qxz" :index="'mc'" :index_val="'mc'"
                                          :val="zb.xzqsz"  :name="'zb.xzqsz'">
                            </select-input>
                        </li>
                        <li v-show="isShowQsIn"><i>缺  省 值</i>
                            <input v-model="zb.xzqsz"  class="zui-input ">
                        </li>
                        <li><i>计算式</i>
                            <input v-model="zb.expression" @blur="cal($event)" class="zui-input" type="text">
                        </li>
                        <li style="width: 100%"><i>临床意义</i>
                            <textarea v-model="zb.lcyy" class="zui-input height-72" type="text" > </textarea>
                        </li>
                        <li><i>指标分类</i>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="zbfl" :index="'text'" :index_val="'num'"
                                          :val="zb.zbfl":name="'zb.zbfl'">
                            </select-input>
                        </li>
                        <li><i>检测方法</i>
                            <input v-model="zb.syff" class="zui-input" type="text">
                        </li>
                        <li><i>强制检验结果</i>
                            <input v-model="zb.dxqzjg" class="zui-input" type="text">
                        </li>
                        <li><i>质控参考值</i>
                            <select-input @change-data="resultChange" :not_empty="false"
                                                      :child="zkz" :index="'text'" :index_val="'num'"
                                                      :val="zb.zkckz":name="'zb.zkckz'">
                        </select-input>
                        </li>
                        <li><i>危机值判断方式</i>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="wjz" :index="'text'" :index_val="'num'"
                                          :val="zb.wjzpdfs":name="'zb.wjzpdfs'">
                            </select-input>
                        </li>
                        <li><i>参考判断方式</i>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="ckz" :index="'text'" :index_val="'num'"
                                          :val="zb.ckzpdfs":name="'zb.ckzpdfs'">
                            </select-input>

                        </li>
                        <li><i>显示序号</i>
                            <input v-model="zb.xh" data-notempty="true" class="zui-input" type="text">
                        </li>
                    </ul>
                <div class="col-fm-12 padd-l-15">
                    <div class="col-fm-2 width-170">
                        <input id="list1" type="checkbox" :checked="zb.have == 0 ?false:true" class="green" @change="jgchange" class="green"/>
                        <label for="list1"></label>
                        <span class="middle">结果为0时必须保留</span>
                    </div>
                    <div class="col-fm-2 width-170">
                        <input id="list2" type="checkbox" :checked="zb.sfdy == 0 ?false:true" class="green" @change="dychange" />
                        <label for="list2"></label>
                        <span class="middle">是否打印</span>
                    </div>
                    <div class="col-fm-2" style="width: 124px">
                        <input id="list3" type="checkbox" :checked="zb.tybz == 0 ?false:true" class="green" @change="tychange" />
                        <label for="list3"></label>
                        <span class="middle">启用停用标志</span>
                    </div>
                    <div class="col-fm-2 width-170">
                        <input id="list4" type="checkbox" :checked="zb.chxt == 0? false:true" class="green" @change="chchange"/>
                        <label for="list4"></label>
                        <span class="middle">餐后血糖</span>
                    </div>
                    <div class="col-fm-2 width-170">
                        <input id="list" type="checkbox" :checked="zb.jfsy == 0? false:true" class="green" @change="jfchange"/>
                        <label for="list"></label>
                        <span class="middle">激发试验</span>
                    </div>
                </div>
                <div class="col-fm-12 top-28">
                    <p class="xuxian">参考值</p>
                    <div class="cankaozhi01" style="display: none">
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">最低值</span>
                                <input v-model="zb.zbCkz.allN" class="zui-input width-170">
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">最高值</span>
                                <input v-model="zb.zbCkz.allNH" class="zui-input width-170">
                            </div>
                        </div>
                    </div>
                    <div class="cankaozhi" style="display: none">
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">男性</span>
                                <input v-model="zb.zbCkz.manT" class="zui-input width-170">
                            </div>
                        </div>
                        <div class="  col-fm-2 col-width-3">
                            <div class="zui-input-inline">
                                <span class="span-input">女性</span>
                                <input v-model="zb.zbCkz.womanT" class="zui-input width-170">
                            </div>
                        </div>
                    </div>
                    <div class="cankao1" style="display: none">
                        <div class="col-fm-12">
                            <div class="  col-fm-2 col-width-3 left-20">
                                <p class="center bottom-4">男性</p>
                                <div class="zui-input-inline">
                                    <span class="span-input">最低值</span>
                                    <input v-model="zb.zbCkz.manN" type="number" class="zui-input ">
                                </div>
                            </div>
                            <div class="  col-fm-2 col-width-3">
                                <p class="center bottom-4">女性</p>
                                <div class="zui-input-inline">
                                    <input v-model="zb.zbCkz.womanN" type="number" class="zui-input ">

                                </div>
                            </div>
                        </div>
                        <div class="col-fm-12">
                            <div class="  col-fm-2 col-width-3 left-20">
                                <div class="zui-input-inline">
                                    <span class="span-input">最高值</span>
                                    <input v-model="zb.zbCkz.manNH" type="number" class="zui-input ">
                                </div>
                            </div>
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <input v-model="zb.zbCkz.womanNH" type="number" class="zui-input ">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cankao2" style="display: none">
                        <div class="col-fm-12">
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">年龄段1</span>
                                    <input type="number" style="padding-right: 55px;"
                                           class="zui-input width-170">
                                    <span class="absolate">岁以下</span>
                                </div>
                            </div>
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">男&emsp;&emsp;性</span>
                                    <input type="number" class="zui-input width-170">
                                </div>
                            </div>
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">女&emsp;&emsp;性</span>
                                    <input type="number" class="zui-input width-170">
                                </div>
                            </div>
                        </div>
                        <div class="col-fm-12">
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <span class="span-input">年龄段2</span>
                                        <input type="number" style="width: 70px;padding-right: 26px;"
                                               class="zui-input ">
                                        <span class="abs">岁</span>
                                    </div>
                                    <span class="">至</span>
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <input type="number" style="width: 70px;padding-right: 26px;"
                                               class="zui-input ">
                                        <span class="abs">岁</span>
                                    </div>

                                </div>
                            </div>
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">男&emsp;&emsp;性</span>
                                    <input type="number" class="zui-input width-170">
                                </div>
                            </div>
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">女&emsp;&emsp;性</span>
                                    <input type="number" class="zui-input width-170">
                                </div>
                            </div>
                        </div>
                        <div class="col-fm-12">
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">年龄段1</span>
                                    <input type="number" style="padding-right: 55px;"
                                           class="zui-input width-170">
                                    <span class="absolate" style="right: 48px;">岁以上</span>
                                </div>
                            </div>
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">男&emsp;&emsp;性</span>
                                    <input type="number" class="zui-input width-170">
                                </div>
                            </div>
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">女&emsp;&emsp;性</span>
                                    <input type="number" class="zui-input width-170">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display: none" class="cankao3">
                        <div class="col-fm-12">
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">年龄段1</span>
                                    <input type="number" style="padding-right: 55px;"
                                           class="zui-input width-170">
                                    <span class="absolate">岁以下</span>
                                </div>
                            </div>
                            <div class=" col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <span class="span-input">男&emsp;&emsp;性</span>
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>
                                    <span class="left-right"
                                          style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>

                                </div>
                            </div>
                            <div class=" col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <span class="span-input">女&emsp;&emsp;性</span>
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>
                                    <span class="left-right"
                                          style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="col-fm-12">
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <span class="span-input">年龄段2</span>
                                        <input type="number" style="width: 70px;padding-right: 26px;"
                                               class="zui-input ">
                                        <span class="abs">岁</span>
                                    </div>
                                    <span class="left-right"
                                          style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <input type="number" style="width: 70px;padding-right: 26px;"
                                               class="zui-input ">
                                        <span class="abs">岁</span>
                                    </div>

                                </div>
                            </div>
                            <div class=" col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <span class="span-input">男&emsp;&emsp;性</span>
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>
                                    <span class="left-right"
                                          style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>

                                </div>
                            </div>
                            <div class=" col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <span class="span-input">女&emsp;&emsp;性</span>
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>
                                    <span class="left-right"
                                          style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="col-fm-12">
                            <div class="  col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <span class="span-input">年龄段1</span>
                                    <input type="number" style="padding-right: 55px;"
                                           class="zui-input width-170">
                                    <span class="absolate">岁以上</span>
                                </div>
                            </div>
                            <div class=" col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <span class="span-input">男&emsp;&emsp;性</span>
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>
                                    <span class="left-right"
                                          style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>

                                </div>
                            </div>
                            <div class=" col-fm-2 col-width-3">
                                <div class="zui-input-inline">
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <span class="span-input">女&emsp;&emsp;性</span>
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>
                                    <span class="left-right"
                                          style="min-width: auto;max-width: initial;margin: 0 5px;">至</span>
                                    <div style="position: relative;display: flex;align-items: center;">
                                        <input type="number" style="width: 70px;" class="zui-input ">
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display: none" class="cankao4">
                        <p class="buttom" @click="xinzeng4">新增参考值</p>
                        <ul class="ck-header" id="nl0">
                            <li class="line">
                                <span class="text" style="width: 175px;">年龄</span>
                                <span class="text" style="width: 157px;">年龄</span>
                                <span class="text">最小值</span>
                                <span class="text">最大值</span>
                                <span class="text">性别</span>
                                <span class="text">操作</span>
                            </li>
                            <li class="line bottom-border" v-for="(item, $index) in zbNlCkz">
                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl1"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw1_' + $index"
                                                      @change-data="resultChange_item" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nldw1'" :val="item.nldw1"
                                                      :name="$index + '.nldw1.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <span class="left-right"
                                      style="min-width: auto;max-width: initial">至</span>
                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" class="zui-input " v-model="item.nl2" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw2_' + $index"
                                                      @change-data="resultChange_item" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nldw2'" :val="item.nldw2"
                                                      :name="$index + '.nldw2.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <div class="text-calc" v-show="isValue">
                                    <div><input type="number" style="width: 70px;" class="zui-input " v-model="item.min" @keydown="nextFocus($event)">
                                    </div>
                                </div>
                                <div class="text-calc" v-show="isValue">
                                    <div><input type="number" style="width: 70px;" class="zui-input " v-model="item.max" @keydown="nextFocus($event)">
                                    </div>
                                </div>
                                <div class="text-calc" v-show="isText">
                                    <div><input  style="width: 140px; margin-left:50px" class="zui-input " v-model="item.nlT" @keydown="nextFocus($event)">
                                    </div>
                                </div>
                                <div class="text-calc">
                                    <div class="zui-select-inline" style="width: 70px;margin: 0 auto;">
                                        <select-input :id="'xb_' + $index"
                                                      @change-data="resultChange_item" :not_empty="true"
                                                      :child="xtwhxb_tran" :index="'item.xb'" :val="item.xb"
                                                      :name="$index + '.xb.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <div class="text-calc">
                                    <div><span @click="sc($index)" class="icon-sc"></span></div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div  style="display: none"  class="cankao5">
                        <p class="buttom" @click="xinzeng5">新增参考值</p>
                        <ul class="ck-header" id="yb0">
                            <li class="line">
                                <span class="text">样本类型</span>
                                <span class="text">参考值</span>
                                <span class="text">操作</span>
                            </li>
                            <li class="line bottom-border"  v-for="(item,$index) in zbYbCkz">
                                <div class="zui-select-inline text-calc">
                                    <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                                  :child="ybList" :index="'ybmc'" :index_val="'ybbm'"
                                                  :val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
                                    </select-input>
                                </div>
                                <div class="text-calc"><input v-model="item.allT" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><span @click="delok($index)" data-target="num5" data-num="aeaaaaee-beaf-4eee-aabf-bbefeabebfef" class="icon-sc"></span></div>
                            </li>
                            <!-- <li class="line bottom-border"  v-for="(list,index) in appNum" :key="list.num">
                                <div class="zui-select-inline text-calc">
                                    <input type="text" class="zui-input" name="input1"
                                           placeholder="请选择" readonly/>
                                    <div class="zui-select-group" role="listbox">
                                        <ul class="inner">
                                            <li value="0">年</li>
                                            <li value="1">月</li>
                                            <li value="2">日</li>
                                            <li value="3">小时</li>
                                            <li value="3">分</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><span onclick="delok(this)" data-target="num5" data-num="aeaaaaee-beaf-4eee-aabf-bbefeabebfef" class="icon-sc"></span></div>
                            </li> -->
                        </ul>
                    </div>
                    <div style="display: none" class="cankao6">
                        <p class="buttom" @click="xinzeng6">新增参考值</p>
                        <ul class="ck-header" id="yb1">
                            <li class="line">
                                <span class="text">样本类型</span>
                                <span class="text">最低值</span>
                                <span class="text">最低值</span>
                                <span class="text">操作</span>
                            </li>
                            <li class="line bottom-border"  v-for="(item,$index) in zbYbCkz">
                                <div class="zui-select-inline text-calc">
                                    <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                                  :child="ybList" :index="'ybmc'" :index_val="'ybbm'"
                                                  :val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
                                    </select-input>
                                </div>
                                <div class="text-calc"><input v-model="item.allN" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><input v-model="item.allNH" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><span @click="delok($index)" data-target="num5" data-num="aeaaaaee-beaf-4eee-aabf-bbefeabebfef" class="icon-sc"></span></div>
                            </li>
                            <!-- <li class="line bottom-border" v-for="(list,index) in appNum" :key="list.num">
                                <div class="zui-select-inline text-calc">
                                    <input type="text" class="zui-input" name="input1"
                                           placeholder="请选择" readonly/>
                                    <div class="zui-select-group" role="listbox">
                                        <ul class="inner">
                                            <li value="0">年</li>
                                            <li value="1">月</li>
                                            <li value="2">日</li>
                                            <li value="3">小时</li>
                                            <li value="3">分</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><span onclick="delok(this)"
                                                             data-num="efffbfaf-eeae-4fbb-bbfe-aefbaeafebef"
                                                             data-target="num6" class="icon-sc"></span>
                                </div>
                            </li> -->
                        </ul>
                    </div>
                    <div class="cankao7" style="display:none ">
                        <div class="  col-fm-12 " style="margin-bottom: 20px;">
                            <div class="zui-input-inline">
                                <span class="span-input" style="width: 65px">参考值</span>
                                <textarea class="zui-input height-72 " v-model="zb.zbCkz.allT">
                                        </textarea>
                            </div>
                        </div>
                    </div>
                    <div style="display: none" class="cankao8" >
                        <p class="buttom" @click="xinzeng8">新增参考值</p>
                        <ul class="ck-header" id="yb2">
                            <li class="line">
                                <span class="text">样本类型</span>
                                <span class="text">男性</span>
                                <span class="text">女性</span>
                                <span class="text">操作</span>
                            </li>
                            <li class="line bottom-border"  v-for="(item,$index) in zbYbCkz">
                                <div class="zui-select-inline text-calc">
                                    <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                                  :child="ybList" :index="'ybmc'" :index_val="'ybbm'"
                                                  :val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
                                    </select-input>
                                </div>
                                <div class="text-calc"><input v-model="item.manT" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><input v-model="item.womanT" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><span @click="delok($index)" data-target="num5" data-num="aeaaaaee-beaf-4eee-aabf-bbefeabebfef" class="icon-sc"></span></div>
                            </li>
                            <!-- <li class="line bottom-border" v-for="(list,index) in appNum" :key="list.num">
                                <div class="zui-select-inline text-calc">
                                    <input type="text" class="zui-input" name="input1"
                                           placeholder="请选择" readonly/>
                                    <div class="zui-select-group" role="listbox">
                                        <ul class="inner">
                                            <li value="0">年</li>
                                            <li value="1">月</li>
                                            <li value="2">日</li>
                                            <li value="3">小时</li>
                                            <li value="3">分</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><span onclick="delok(this)"
                                                             data-num="aabaabff-afbe-4fbf-aeba-eebbeebfbbfa"
                                                             data-target="num8" class="icon-sc"></span>
                                </div>
                            </li> -->
                        </ul>
                    </div>
                    <div style="display:none;" class="ck11 cankao10" >
                        <p class="buttom" @click="xinzeng10">新增参考值</p>
                        <ul class="ck-header" id="yb3">
                            <!-- <li class="line">
                                <span class="text">样本类型</span>
                                <span class="text">年龄段1</span>
                                <span class="text">低值</span>
                                <span class="text">高值</span>
                                <span class="text">年龄段2</span>
                                <span class="text">低值</span>
                                <span class="text">高值</span>
                                <span class="text">年龄段3</span>
                                <span class="text">低值</span>
                                <span class="text">高值</span>
                                <span class="text">年龄段4</span>
                                <span class="text">低值</span>
                                <span class="text">高值</span>
                                <span class="text">操作</span>
                            </li> -->
                            <li class="line" style="margin-bottom: 20px;">
                                <!-- @dblclick="delHeader(index)" -->
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">样本类型</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段1</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">低值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"> <span class="text">高值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段2</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">低值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">高值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段3</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">低值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">高值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段4</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">低值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">高值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">操作</span></p>
                            </li>
                            <li class="line bottom-border" v-for="(item,$index) in zbYbCkz">
                                <select-input style="min-width: 14%; width: 14%;" @change-data="Wf_YppfChange" :not_empty="false"
                                              :child="ybList" :index="'ybmc'" :index_val="'ybbm'"
                                              :val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
                                </select-input>
                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl1"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw1_' + $index"
                                                      @change-data="YbNl1Change" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nl1Dw'" :val="item.nl1Dw"
                                                      :name="$index + '.nl1Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <!-- <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1" class="zui-input"></div> -->
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1N" class="zui-input"></div>
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1NH" class="zui-input"></div>

                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl2"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw2_' + $index"
                                                      @change-data="YbNl2Change" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nl2Dw'" :val="item.nl2Dw"
                                                      :name="$index + '.nl2Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <!-- <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2" class="zui-input"></div> -->
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2N" class="zui-input"></div>
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2NH" class="zui-input"></div>

                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl3"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw3_' + $index"
                                                      @change-data="YbNl3Change" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nl3Dw'" :val="item.nl3Dw"
                                                      :name="$index + '.nl3Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <!-- <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3" class="zui-input"></div> -->
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3N" class="zui-input"></div>
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3NH" class="zui-input"></div>

                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl4"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw4_' + $index"
                                                      @change-data="YbNl4Change" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nl4Dw'" :val="item.nl4Dw"
                                                      :name="$index + '.nl4Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <!-- <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4" class="zui-input"></div> -->
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4N" class="zui-input"></div>
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4NH" class="zui-input"></div>
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><span @click="delok($index)"
                                                                                                 data-num="efeebebf-ebaf-4ebe-baaf-eeebbbeeffef"
                                                                                                 data-target="num9" class="icon-sc"></span>
                                </div>
                            </li>
                            <!-- <li class="line bottom-border"  v-for="(item,$index) in zbYbCkz">
                                 <div class="zui-select-inline text-calc">
                                    <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                                           :child="ybList" :index="'ybmc'" :index_val="'ybbm'"
                                                           :val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
                                     </select-input>
                                 </div>

                                 <div class="text-calc">
                                     <div><input type="number" style="width: 70px;" v-model="item.nl1"
                                                 class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                     </div>
                                     <div class="zui-select-inline">
                                         <select-input :id="'nldw1_' + $index"
                                                               @change-data="resultChange_item" :not_empty="true"
                                                               :child="xtwhnldw_tran" :index="'item.nl1Dw'" :val="item.nl1Dw"
                                                               :name="$index + '.nl1Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                               data-notEmpty="false">
                                         </select-input>
                                     </div>
                                 </div>
                                    <div class="text-calc"><input type="number" v-model="item.nl1N" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input type="number" v-model="item.nl1NH" class="zui-input" @keydown="nextFocus($event)"></div>

                                    <div class="text-calc">
                                     <div><input type="number" style="width: 70px;" v-model="item.nl1"
                                                 class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                     </div>
                                     <div class="zui-select-inline">
                                         <select-input :id="'nldw2_' + $index"
                                                               @change-data="resultChange_item" :not_empty="true"
                                                               :child="xtwhnldw_tran" :index="'item.nl2Dw'" :val="item.nl2Dw"
                                                               :name="$index + '.nl2Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                               data-notEmpty="false">
                                         </select-input>
                                     </div>
                                 </div>
                                    <div class="text-calc"><input type="number" v-model="item.nl2N" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input type="number" v-model="item.nl2NH" class="zui-input" @keydown="nextFocus($event)"></div>

                                    <div class="text-calc">
                                     <div><input type="number" style="width: 70px;" v-model="item.nl1"
                                                 class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                     </div>
                                     <div class="zui-select-inline">
                                         <select-input :id="'nldw3_' + $index"
                                                               @change-data="resultChange_item" :not_empty="true"
                                                               :child="xtwhnldw_tran" :index="'item.nl3Dw'" :val="item.nl2Dw"
                                                               :name="$index + '.nl3Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                               data-notEmpty="false">
                                         </select-input>
                                     </div>
                                 </div>
                                    <div class="text-calc"><input type="number" v-model="item.nl3N" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input type="number" v-model="item.nl3NH" class="zui-input" @keydown="nextFocus($event)"></div>

                                    <div class="text-calc">
                                     <div><input type="number" style="width: 70px;" v-model="item.nl1"
                                                 class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                     </div>
                                     <div class="zui-select-inline">
                                         <select-input :id="'nldw4_' + $index"
                                                               @change-data="resultChange_item" :not_empty="true"
                                                               :child="xtwhnldw_tran" :index="'item.nl4Dw'" :val="item.nl4Dw"
                                                               :name="$index + '.nl4Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                               data-notEmpty="false">
                                         </select-input>
                                     </div>
                                 </div>
                                    <div class="text-calc"><input type="number" v-model="item.nl4N" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input type="number" v-model="item.nl4NH" class="zui-input" @keydown="nextFocus($event)"></div>

                                    <div class="text-calc"><input v-model="item.womanN" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input v-model="item.manNH" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input v-model="item.manN" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input v-model="item.womanNH" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input v-model="item.womanN" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input v-model="item.manNH" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input v-model="item.manN" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input v-model="item.womanNH" class="zui-input" @keydown="nextFocus($event)"></div>
                                    <div class="text-calc"><input v-model="item.womanN" class="zui-input" @keydown="nextFocus($event)"></div>
                                 <div class="text-calc"><span @click="delok($index)" data-target="num5" data-num="aeaaaaee-beaf-4eee-aabf-bbefeabebfef" class="icon-sc"></span></div>
                             </li> -->
                            <!-- <li class="line bottom-border" v-for="(list,index) in appNum" :key="list.num">
                                <div class="zui-select-inline text-calc">
                                    <input type="text" class="zui-input" name="input1"
                                           placeholder="请选择" readonly/>
                                    <div class="zui-select-group" role="listbox">
                                        <ul class="inner">
                                            <li value="0">年</li>
                                            <li value="1">月</li>
                                            <li value="2">日</li>
                                            <li value="3">小时</li>
                                            <li value="3">分</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><input class="zui-input"></div>
                                <div class="text-calc"><span onclick="delok(this)"
                                                             data-num="efeebebf-ebaf-4ebe-baaf-eeebbbeeffef"
                                                             data-target="num9" class="icon-sc"></span>
                                </div>
                            </li> -->
                        </ul>
                    </div>
                    <div style="display:none;" class="cankao9" >
                        <p class="buttom" @click="xinzeng9">新增参考值</p>
                        <ul class="ck-header" id="yb3">
                            <li class="line">
                                <span class="text">样本类型</span>
                                <span class="text">男性高值</span>
                                <span class="text">男性低值</span>
                                <span class="text">女性高值</span>
                                <span class="text">女性低值</span>
                                <span class="text">操作</span>
                            </li>
                            <li class="line bottom-border"  v-for="(item,$index) in zbYbCkz">
                                <div class="zui-select-inline text-calc">
                                    <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                                  :child="ybList" :index="'ybmc'" :index_val="'ybbm'"
                                                  :val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
                                    </select-input>
                                </div>
                                <div class="text-calc"><input v-model="item.manNH" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><input v-model="item.manN" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><input v-model="item.womanNH" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><input v-model="item.womanN" class="zui-input" @keydown="nextFocus($event)"></div>
                                <div class="text-calc"><span @click="delok($index)" data-target="num5" data-num="aeaaaaee-beaf-4eee-aabf-bbefeabebfef" class="icon-sc"></span></div>
                            </li>
                        </ul>
                    </div>
                    <div style="display:none;" class="ck10 cankao10" >
                        <p class="buttom" @click="xinzeng10">新增参考值</p>
                        <ul class="ck-header" id="yb3">
                            <li class="line" style="margin-bottom: 20px;">
                                <!-- @dblclick="delHeader(index)" -->
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">样本类型</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段1</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">参考值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段2</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">参考值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段3</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">参考值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">年龄段4</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">参考值</span></p>
                                <p  class="text "style="min-width: 14%;background: #edf2f1;max-width: 14%"><span class="text">操作</span></p>
                            </li>
                            <li class="line bottom-border" v-for="(item,$index) in zbYbCkz">
                                <select-input style="min-width: 14%; width: 14%;" @change-data="Wf_YppfChange" :not_empty="false"
                                              :child="ybList" :index="'ybmc'" :index_val="'ybbm'"
                                              :val="item.ybbm" :name="'PfxxJson.'+$index+'.ybbm'" :index_mc="'ybmc'" :search="true">
                                </select-input>
                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl1"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw1_' + $index"
                                                      @change-data="YbNl1Change" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nl1Dw'" :val="item.nl1Dw"
                                                      :name="$index + '.nl1Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <!-- <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1" class="zui-input"></div> -->
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl1T" class="zui-input"></div>

                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl2"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw2_' + $index"
                                                      @change-data="YbNl2Change" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nl2Dw'" :val="item.nl2Dw"
                                                      :name="$index + '.nl2Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <!-- <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2" class="zui-input"></div> -->
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl2T" class="zui-input"></div>

                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl3"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw3_' + $index"
                                                      @change-data="YbNl3Change" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nl3Dw'" :val="item.nl3Dw"
                                                      :name="$index + '.nl3Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <!-- <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3" class="zui-input"></div> -->
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl3T" class="zui-input"></div>

                                <div class="text-calc">
                                    <div><input type="number" style="width: 70px;" v-model="item.nl4"
                                                class="zui-input value_abafaeae-ebab-4fff-afef-fbbafeebafab" @keydown="nextFocus($event)">
                                    </div>
                                    <div class="zui-select-inline">
                                        <select-input :id="'nldw4_' + $index"
                                                      @change-data="YbNl4Change" :not_empty="true"
                                                      :child="xtwhnldw_tran" :index="'item.nl4Dw'" :val="item.nl4Dw"
                                                      :name="$index + '.nl4Dw.' + 1" :search="true" @keydown="nextFocus($event)"
                                                      data-notEmpty="false">
                                        </select-input>
                                    </div>
                                </div>
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><input @keydown="nextFocus($event)"  v-model="item.nl4T" class="zui-input"></div>
                                <div class="text-calc" style="min-width: 14%; width: 14%;"><span @click="delok($index)"
                                                                                                 data-num="efeebebf-ebaf-4ebe-baaf-eeebbbeeffef"
                                                                                                 data-target="num9" class="icon-sc"></span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- <div  class="ck10 cankao10">
                        <span class="buttom" @click="xinzeng10">新增参考值</span>
                        <span class="buttom" @click="xinzengheader">新增年龄段</span>
                        <div style="width: 100%;">
                            <ul class="ck-header" id="yb4">
                                <li class="line bg-f2" style="margin-bottom: 18px;">
                                    <span class="text" style="min-width: 14%;width: 14%">样本类型</span>
                                    <p @dblclick="delHeader(index)" class="text "
                                       style="min-width: 12%;background: #edf2f1;max-width: 12%;width: 12%"
                                       v-for="(list,index) in pop10"><span class="txt">{{list}}</span>
                                    </p>
                                    <span class="text absol" style="width:80px;">操作</span>
                                </li>
                                <li class="line bottom-border clone" id="clone" v-for="list in cankaozhilist">
                                    <div class="zui-select-inline text-calc" style="min-width: 14%;width: 14%">
                                        <input type="text" class="zui-input" name="input1"
                                               placeholder="请选择" readonly/>
                                        <div class="zui-select-group" role="listbox">
                                            <ul class="inner">
                                                <li value="0">年</li>
                                                <li value="1">月</li>
                                                <li value="2">日</li>
                                                <li value="3">小时</li>
                                                <li value="3">分</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <span style="width: 189px;display: flex;"  v-for="list in nianlinduanlist" >
                                        <div style="min-width: 96.88px;max-width: 96.88px;width: 96.88px" class="text-calc dbremove" ><input class="zui-input"/></div>
                                        <div style="min-width: 96.88px;max-width: 96.88px;width: 96.88px" class="text-calc dbremove" ><input class="zui-input"/></div>
                               </span>
                                    <div class="text-calc absol" style="width:80px;background: #fff">
                                        <span onclick="delok(this)" data-target="num10" class="icon-sc"></span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div> -->
                    <!--style="display:none;"-->
                    <!-- <div style="display:none;"  class="ck11 cankao10">
                        <span class="buttom" @click="xinzeng11">新增参考值</span>
                        <span class="buttom" @click="xinzengheader11">新增年龄段</span>
                        <div style="width: 100%;">
                            <ul class="ck-header" id="yb5">
                                <li class="line bg-f2" style="margin-bottom: 18px;">
                                    <span class="text" style="min-width: 14%;width: 14%">样本类型</span>
                                    <p @dblclick="delHeader11(index)" class="text "
                                       style="min-width: 12%;background: #edf2f1;max-width: 12%;width: 12%"
                                       v-for="(list,index) in pop11"><span class="txt">{{list}}</span>
                                    </p>
                                    <span class="text absol" style="width:80px;">操作</span>
                                </li>
                                <li class="line bottom-border clone11" id="clone11" v-for="list in zuidizhilist">
                                    <div class="zui-select-inline text-calc" style="min-width: 14%;width: 14%">
                                        <input type="text" class="zui-input" name="input1"
                                               placeholder="请选择" readonly/>
                                        <div class="zui-select-group" role="listbox">
                                            <ul class="inner">
                                                <li value="0">年</li>
                                                <li value="1">月</li>
                                                <li value="2">日</li>
                                                <li value="3">小时</li>
                                                <li value="3">分</li>
                                            </ul>
                                        </div>
                                    </div>
                               <span style="width: 297px;display: flex;"  v-for="list in gaozhilist" >
                                        <div style="min-width: 98.13px;max-width: 98.13px;width: 98.13px" class="text-calc dbremove11" ><input class="zui-input"/></div>
                                    <div  style="min-width:98.13px;max-width: 98.13px;width: 98.13px" class="text-calc dbremove11" ><input class="zui-input"/></div>
                                    <div  style="min-width:98.13px;max-width: 98.13px;width: 98.13px" class="text-calc dbremove11" ><input class="zui-input"/></div>
                               </span>
                                    <div class="text-calc absol" style="width:80px;background: #fff">
                                        <span onclick="delok(this)" data-target="num10" class="icon-sc"></span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div> -->
                </div>



            </div>
                <div class="zui-row col-fm-12 z-bottom" style="text-align: right;height: 44px;position: absolute;bottom: 10px;">
                    <span class="gananhao"
                          style="float: left;color:#f2a654;margin-left: 47px;text-align: left;line-height: 22px;width: 300px">
                        *计算式格式为：「指标编码1」+「指标编码2」
                        可括号“（）”「」里面可以是固定值
                    </span>
                    <span class="buttonbox" style="align-items: center;height: 100%;">
                        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="close()">取消</button>
                    <button class="zui-btn btn-primary table_db_save xmzb-db" @click="save()">保存</button>
                   </span>
                </div>
            </div>
        </transition>
    </div>

<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="jyzb.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>
