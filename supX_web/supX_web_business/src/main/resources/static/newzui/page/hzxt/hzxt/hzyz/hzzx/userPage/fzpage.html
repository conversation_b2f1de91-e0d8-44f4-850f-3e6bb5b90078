<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>新增分支</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="fzpage.css" rel="stylesheet">
</head>
<body class="skin-default">
<div class="fzpage bg-fff flex-one flex-container flex-dir-c  padd-t-10 padd-b-10 padd-l-15">
    <div class="flex-container flex-dir-c ">
        <div class="flex-container  flex-align-c">
         <div>
             <input type="checkbox" class="green" v-model="ischeck">
             <label @click="docheck"></label>
         </div>
            <div class="flex-text-active fzpage-left">使用整个表单</div>
        </div>
        <div class="flex-container flex-jus-c fzpage-bt">混合痔临床路径-子路径</div>
        <div class="flex-container fzpage-title padd-l-5">适用对象：第一诊断为急性（. HHICD-10:K35.902）标准住院日：大于7-10天 标准费用：10000-12000元&emsp;&emsp;说明：无无无无无无无无无无无无无</div>
    </div>
     <div class="flex-container flex-one flex-dir-c template">
         <td-template @checkselectsh="checkSelectSh" @menu-active="menuActive" v-if="ismenu" :ischecked="ischecked" @checked="checked" :iscon="true" :istype="false" :isck="true" @menu-click="menu"
                      :contextmenuone="contextmenuone" :contextmenutwo="contextmenutwo"
                      :optionlist="optionList"></td-template>
 </div>
    <div class="flex-container flex-dir-c">
        <div class="zui-table-tool flex-container flex-align-c flex-jus-e">
            <button v-waves class="tong-btn btn-parmary-d9   xmzb-db " @click="closeAndOpenPage">取消</button>
            <button v-waves class="tong-btn btn-parmary  xmzb-db" @click="doZjjl">保存</button>
        </div>
    </div>
    <div class="fzFiexd" @click="qtflj">
        <div class="bg-fiexd"></div>
    </div>
</div>
<div class="side-form" v-cloak :class="{'ng-hide':index==1}" style="width:320px;" id="brzcList" role="form">
    <div class="tab-message">
        <a>分路径列表</a>
        <a href="javascript:" class="fr closex ti-close" @click="closes"></a>
    </div>
    <div id="jyxm_icon" class="ksys-side">
        <tree_tem3 v-for="item in treeData" :list="item" :child="'zljList'" :id="'id'" :checked="[]"
                   :name="'name'" @change="checkedVal"></tree_tem3>
    </div>
    <div class="ksys-btn flex-container">
        <button v-waves class="flex-text-active zui-btn icon-xz1 paddr-r5" @click="popShow">新增分路径</button>
    </div>
</div>
<div class="pop">
    <div class="pophide" :class="{'show':isShow}" ></div>
    <div class=" podrag height-500 pop-850  bcsz-layer padd-b-15" :class="{'show':isShow}">
        <div class="layui-layer-title ">新增分路径</div>
        <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close" @click="isShow=false"></a></span>
        <div class="layui-layer-content">
            <div class=" layui-mad layui-height padd-l-20 flex-container flex-wrap-w">
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">使用科室</label>
                        <select-input  @change-data="resultChange" :not_empty="false"
                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                       :name="'popContent.yzxx'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">&emsp;主路径</label>
                        <select-input  @change-data="resultChange" :not_empty="false"
                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                       :name="'popContent.yzxx'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">使用模板</label>
                        <select-input  @change-data="resultChange" :not_empty="false"
                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                       :name="'popContent.yzxx'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">路径编码</label>
                        <select-input  @change-data="resultChange" :not_empty="false"
                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                       :name="'popContent.yzxx'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">路径名称</label>
                        <div class="zui-input-inline">
                            <input class="zui-input " v-model="popContent.yplx"  placeholder="请输入路径名称"
                                   @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">病例分型</label>
                        <select-input  @change-data="resultChange" :not_empty="false"
                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                       :name="'popContent.yzxx'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">适用病情</label>
                        <select-input  @change-data="resultChange" :not_empty="false"
                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                       :name="'popContent.yzxx'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">适用性别</label>
                        <select-input  @change-data="resultChange" :not_empty="false"
                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                       :name="'popContent.yzxx'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">&emsp;&emsp;分类</label>
                        <select-input  @change-data="resultChange" :not_empty="false"
                                       :child="yzlx_tran01" :index="popContent.yzxx" :val="popContent.yzxx"
                                       :name="'popContent.yzxx'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">标准天数</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="popContent.yplx"  placeholder="请输入标准天数"
                                   @keydown="nextFocus($event)">
                            <span class="cm">天</span>
                        </div>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">适用对象</label>
                        <div class="zui-input-inline">
                            <input class="zui-input " v-model="popContent.yplx"  placeholder="请输入适用对象"
                                   @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace  margin-r-5 ft-14">参&emsp;&emsp;考<br/>费用标准</label>
                        <div class="zui-input-inline flex-container flex-align-c flex-jus-c">
                            <input class="zui-input" v-model="popContent.yplx"   @keydown="nextFocus($event)">
                            <span class="padd-l-10 padd-r-10 color-green">至</span>
                            <input class="zui-input" v-model="popContent.yplx"  @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">启用时间</label>
                        <div class="zui-input-inline zui-date">
                            <i class="datenox icon-rl"></i>
                            <input class="zui-input " id="time" readonly="readonly"
                                   @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4">
                        <label class="whiteSpace margin-r-5 ft-14">拼音代码</label>
                        <div class="zui-input-inline">
                            <input class="zui-input " v-model="popContent.yplx" disabled
                                   @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-c margin-b-20  col-x-4" style="height: 36px">
                        <label class="whiteSpace margin-r-5 ft-14">启用状态</label>
                        <div class="zui-input-inline" id="jyxm_icon" style="height: 36px">
                            <div class="switch" style="left:8%;top: 22%;">
                                <input type="checkbox" checked />
                                <label></label>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
        <div class=" ksys-btn flex-container flex-align-c flex-jus-e">
            <button v-waves class="zui-btn xmzb-db table_db_esc btn-default" @click="isShow=false">取消</button>
            <button v-waves class="zui-btn xmzb-db btn-primary table_db_save" @click="Wf_save">确定</button>
        </div>
    </div>
</div>
<script type="text/javascript" src="fzpage.js"></script>
</body>
</html>
