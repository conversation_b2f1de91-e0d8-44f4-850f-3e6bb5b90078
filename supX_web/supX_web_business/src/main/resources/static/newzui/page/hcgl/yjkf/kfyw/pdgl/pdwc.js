var toolMenu_4 = new Vue({
	el: '.toolMenu_4',
	mixins: [dic_transform, tableBase, mConfirm, baseFunc],
	data: {
		//打印数据
		printData: {},
		pzNum: 0,
		ypjs: null,
		pzhList: [],
		//权限信息之科室编码
		qxksbm: ''
	},
	//加载启动
	mounted: function() {
		//获取判断凭证单号
		this.getWshpdb();
		//获取权限科室编码
		this.getKFData();
	},
	//凭证变更
	watch: {
		pzNum: function(val) {
			//选择“-请选择-”时不进行操作
			if(val == 0) {
				return;
			}
			//加载盘点表明细
			this.showDetail(val);
		}
	},
	updated:function(){
		changeWin()
	},
	methods: {
		//获取盘点表明细
		showDetail: function(parm) {
			//打印数据
			toolMenu_4.printData.dj = parm;
			//查询盘点表明细
			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=pdbmxList&cxfs=wcpdb&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					//转换日期和数字
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['scrq'] = formatTime(json.d[i]['scrq'], 'date');
						json.d[i]['yxqz'] = formatTime(json.d[i]['yxqz'], 'date');
						json.d[i]['ljje'] = Math.round(json.d[i]['yplj'] * json.d[i]['kcsl'] * 100) / 100;
					}
					enter_pdbmx.jsonList = json.d;
					//打印数据
					toolMenu_4.printData.djmx = enter_pdbmx.jsonList;
				} else {
					malert('数据获取失败！','top','defeadted')
				}
			});
		},
		//获取盘点表列表
		getWshpdb: function() {
			var kfbm = document.getElementById('_kfbm').value;
			if(kfbm == '') {
				malert('请选择库房','top','defeadted');
				return;
			}
			var parm = {
				'kfbm': kfbm,
				'qrzfbz': 0
			};
			//查询盘点表
			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=pdbList&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['pdrq'] = formatTime(json.d[i]['pdrq'], 'date');
					}
					toolMenu_4.pzhList = json.d;
				} else {
					malert('数据获取失败！','top','defeadted')
				}

			});
		},
		//审核盘点表
		shPdb: function() {
			if(toolMenu_4.pzNum == 0) {
				malert('请先选择凭证号！','top','defeadted');
			} else if(enter_pdbmx.jsonList.length == 0) {
				malert('没有可以审核的内容！','top','defeadted')
			};
			//准备参数
			var json = {
				list: {
					'pdb': toolMenu_4.pzNum,
					'pdbmx': enter_pdbmx.jsonList,
					'ksbm': this.qxksbm
				}
			};

			this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=pdwc', JSON.stringify(json))
				.then(function(data) {
					if(data.body.a == 0) {
						//打印数据
						console.log(toolMenu_4.printData)
						malert("审核成功",'top','success');
					} else {
						if(data.body.d == 0) {
							malert(data.body.c,'top','success');
						} else {
							malert(data.body.c,'top','defeadted');
						}
					}
				}, function(error) {
					console.log(error);
				});
			//清空明细列表
			enter_pdbmx.jsonList = [];
			//重新获取凭证号
			toolMenu_4.getWshpdb();
			//获取判断凭证单号
			toolMenu_4.pzNum = 0;
		},
		//获取库房信息
		getKFData: function() {//002001006
			$.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100011006"}',
				function(data) {
					if(data.a == 0) {
						//默认获取该用例第一个科室的权限科室编码
						toolMenu_4.qxksbm = data.d[0].ksbm;
					} else {
						malert("一级库房获取失败",'top','defeadted');
					}
				});
		},
	},

});

var enter_pdbmx = new Vue({
	el: '.enter_pdbmx',
	mixins: [dic_transform, tableBase, mConfirm, baseFunc],
	data: {
		pdWay: 0,
		jsonList: []
	},
	methods: {}
})
