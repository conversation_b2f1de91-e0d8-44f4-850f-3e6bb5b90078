<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>项目指标</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="xmzb.css" rel="stylesheet"/>
</head>
<style>
    .zui-table-view .fieldlist{
        display: none !important;
    }
</style>
<body class="skin-default">
<div class="wrapper">
    <div class="zui-table-view top_size"  style="border:none; margin-top: 5px;">
        <div class="xmzb-top">
            <div class="col-x-5 xmzb-top-left">
                <i>检验项目检索</i>
                <i><input type="text" v-model="parm.pydm" placeholder="请输入关键字" class="zui-input"/></i>
                <i><button class="zui-btn btn-primary xmzb-db" @click="queryData">查询</button></i>
            </div>
            <div class="col-x-5 xmzb-top-left" style="margin-left: -5%;">
            <i>备选指标检索</i>
            <i><input type="text" v-model="pydm" placeholder="请输入关键字" class="zui-input"/></i>
            <i><button class="zui-btn btn-primary xmzb-db">查询</button></i>
       	 </div>
        </div>
     <div class="xmzb-content" style="padding-bottom: 40px;background: #fff" >
            <div class="xmzb-content-left">
                <div class="content-left-top">
                    <i>编码</i>
                    <i>项目名称</i>
                    <i>代码</i>
                </div>
                <ul class="content-left-list">
                    <li v-for="(item, $index) in jyxmList" @click="AddList(item)">
                    	<i v-text="item.bm"></i>
                    	<i v-text="item.mc"></i>
                    	<i v-text="item.pydm"></i>
                    </li>
                </ul>

            </div>
            <div class="xmzb-content-right">
                <div class="content-right-top">
                    <i>指标编码</i>
                    <i>指标名称</i>
                    <i>英文名称</i>
                    <i>单位</i>
                    <i>数据类型</i>
                    <i>参考类型</i>
                </div>
                <ul class="content-right-list">
                    <li v-for="(item,$index) in zbbmList" @dblclick="AddDown(item,$index)">
                    	<i v-text="item.zbbm">001</i>
                    	<i v-text="item.zwmc"></i>
                    	<i v-text="item.ywmc"></i>
                    	<i v-text="item.dw"></i>
                    	<i v-text="zbbmsjlx_tran[item.sjlx]"></i>
                    	<i v-text="zbbmcklx_tran[item.cklx]"></i>
                    </li>
                </ul>
            </div>
        </div>

    <div class="isTabel">
        <div class="table" v-show="isTabelShow">
            <div class="poptable">
                <div class="header">
                    <h3 class="titel">
                        <span class="font16">
                             已选项目（{{sumNum}}）
                         </span>
                        <!--<i class="font-icon"></i>-->
                        <i class="icon-shanchu" @click="isTabelShow=false,minishow=true"></i>
                    </h3>
                </div>
                <div   style="border:none;">
                    <div class="xmzb-title">
                        <i>检验项目</i>
                        <i>指标编码</i>
                        <i>指标名称</i>
                        <i>序号</i>
                        <i>指标简称</i>
                    </div>
                    <ul class="xmzb-list">
                        <li v-for="(item, $index) in jyxmMx.zbbmList" @dblclick="dbDel(item,$index)">
                        	<i v-text="jyxmMx.mc"></i>
                        	<i v-text="item.zbbm"></i>
                        	<i v-text="item.zwmc"></i>
                        	<i v-text="item.xh"></i>
                        	<i v-text="item.ywmc"></i>
                        </li>
                    </ul>
                </div>
                <div class="xmzb-ok">
                    <button class="zui-btn table_db_esc btn-default xmzb-db" @click="isTabelShow=false,minishow=true">取消</button>
                    <button class="zui-btn btn-primary xmzb-db" @click="bcjg">确定</button>
                </div>

            </div>
        </div>
        <div class="min-chuangkou" v-show="minishow">
            <p><span class="text-min">已选项目（{{sumNum}}）</span> <span class="icon-min" @click="isTabelShow=true,minishow=false"></span></p>
        </div>

    </div>

    <div id="pop">
        <!--<transition name="pop-fade">-->
        <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
        <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title"></div>
            <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
            <div class="layui-layer-content" >
                <div class=" layui-mad layui-height" v-text="centent">
                </div>
            </div>
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
            </div>
        </div>
        <!--</transition>-->
    </div>
</div>
<script src="xmzb.js"></script>
</body>
</html>