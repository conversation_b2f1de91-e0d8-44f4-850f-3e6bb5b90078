<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>退费统计</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="tftj">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn  btn-parmary icon-sx1 paddr-r5" @click="getData">刷新</button>
            <button class="tong-btn  btn-parmary-b icon-xz1 paddr-r5" >报表</button>
        </div>
            <div class=" flex-container  flex-align-c margin-l-10 padd-t-10 padd-b-10">
                <div class="flex-container  flex-align-c  margin-l-10">
                    <label class="whiteSpace margin-r-5 ft-14">时间段</label>
                    <input type="text" name="phone" class="zui-input wh182" v-model="param.ksrq" id="startTime" placeholder="开始时间"/>
                    <span class="padd-l-10 padd-r-10">至</span>
                    <input type="text" name="phone" class="zui-input wh182" v-model="param.jsrq" id="endTime" placeholder="结束时间"/>
                    <input type="text"  class="zui-input margin-l-10 wh182" v-model="param.parm"  @keyup.13="goToPage(1)" />
                </div>
                <div class="flex-container alignItems  margin-l-10">
                    <span>合计：{{fDec(money, 2)}}元</span>
                </div>
            </div>
    </div>
    <div class="zui-table-view hzList  " id="utable1">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>

                    <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s text-left"><span>病人姓名</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>挂号序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>保险类别</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>发票号码</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>费别</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>费用类别</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s text-left"><span>组合费用</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s text-left"><span>费用项目</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>数量</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>金额</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>收费日期</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>医嘱号码</span></div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s"><span>收费窗口</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>收费员</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>门诊医生</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>门诊科室</span></div>
                    </th>
                </tr>

                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item, $index) in jsonList"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
                    <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-left" v-text="item.brxm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s title" v-text="item.ryghxh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.rybxlbmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fphm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ryfbmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fylbmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-left" v-text="item.zhfymc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-left" v-text="item.mxfyxmmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.fydj,2)"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fysl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.fyje,2)"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.sfsj,'datetime')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yzhm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ywckmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.czyxm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.mzysxm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.mzksmc"></div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div  class="zui-table-fixed table-fixed-l " v-if="jsonList.length!=0"> <!-- 有浮动就加 table-fixed-r -->
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m ">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>

                    </tr>
                    </thead>
                </table>
            </div>
            <!--@click="hover($index,$event)"-->
            <div class="zui-table-body "  @scroll="scrollTableFixed($event)"
                 style="border-right: 1px solid #eee;">
                <table class="zui-table">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        class="tableTr2 table-hovers-filexd-l">
                        <td class="cell-m ">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                    </tr>
                    </tbody>

                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>

<script type="text/javascript" src="tftj.js"></script>
</body>
</html>