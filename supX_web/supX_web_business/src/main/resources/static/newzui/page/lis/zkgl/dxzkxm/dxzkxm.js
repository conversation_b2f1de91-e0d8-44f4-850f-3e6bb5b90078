(function () {
    $(".zui-input").uicomplete();
    $(".zui-table-view").uitable();
    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394'
        , done: function (value, data) {
        }
    });
    var top = new Vue({
        el: '.xmzb-top',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            jysbList: {},
            params: {
                zxsb: '',
                parm: '',
            },
        },
        created: function () {
            this.jysb()
        },
        watch: {
            'params.zxsb': function () {
                this.getsbbm()
            }
        },
        methods: {
            getsbbm: function () { //获取左边数据方法
                var data = {
                    sbbm: this.params.zxsb,
                    zkxm: this.params.parm
                }
                $.getJSON("/actionDispatcher.do?reqUrl=LisZkgl&types=queryAllDx&yq=" + JSON.stringify(data), function (json) {
                    if (json.a == 0) {
                        left.getobjlistwx = json.d.wx;
                        left.getobjlist = json.d;
                        right.getobjlist = json.d;
                        right.getobjlistyx = json.d.yx;
                        console.log(brerList.getobjlist)
                        //brerList.getchild(left.getobjlist[0])
                    } else {
                        malert("获取数据失败" + json.c, 'top', 'defeadted');
                        return false;
                    }
                });
            },
            jysb: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    if (json.a == 0) {
                        top.jysbList = json.d.list;
                        top.params.zxsb = json.d.list[0].sbbm;
                        top.getsbbm()
                    } else {
                        malert("获取数据失败" + json.c, 'top', 'defeadted');
                        return false;
                    }
                });
            },
            bc: function () {
                var jsondata = '{"list":' + JSON.stringify(right.getobjlist.yx) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=saveDx', jsondata).then(
                    function (data) {
                        if (data.body.a == 0) {
                            malert('保存成功', 'top', 'success');
                            this.getsbbm();
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            },
            cr: function () {
                left.cr()
            },
            sc: function () {
                right.sc()
            }
        }
    })
    var left = new Vue({
        el: '.xmzb-content-left',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            detarr:[],
            title: '',
            getobjlistwx:'',
            getobjlist: '',
        },
        methods: {
            cr: function (list) {
                var jsondata = '';
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            this.list = this.getobjlist.wx[i]
                            this.detarr.push(this.list)
                            jsondata = '{"list":' + JSON.stringify(this.detarr) + '}';
                        }
                    }
                } else {
                    jsondata = '{"list":' + JSON.stringify([list]) + '}';
                }
                var json = jsondata;
                this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=insertBatch4Dx', json).then(
                    function (data) {
                        if (data.body.a == 0) {
                            malert(data.body.c, 'top', 'success');
                            top.getsbbm();
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            },
        },
    })
    var right = new Vue({
        el: '.xmzb-content-right',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            title: '',
            detarr:[],
            getobjlist: '',
            getobjlistyx: '',
        },
        methods: {
            sc: function (list) {
                var jsondata = '';
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            this.list = this.getobjlist.yx[i]
                            this.detarr.push(this.list)
                            jsondata = '{"list":' + JSON.stringify(this.detarr) + '}';
                        }
                    }
                } else {
                    jsondata = '{"list":' + JSON.stringify([list]) + '}';
                }
                var json = jsondata;
                this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=deleteBatch4Dx', json).then(
                    function (data) {
                        if (data.body.a == 0) {
                            malert(data.body.c, 'top', 'success');
                            top.getsbbm();
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    },
                    function (error) {
                        malert(error, 'top', 'defeadted');
                    });
            }
        },
    })
    var pop = new Vue({
        el: '#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            isShowpopL: false,
            isTabelShow: false,
            isShow: false,
            title: '',
            name: '',
        },
        methods: {
            //确定删除
            delOk: function () {
                pop.isShowpopL = false;
                pop.isShow = false;
                malert('删除成功', 'top', 'success');
                // event.currentTarget.remove();
            }
        }
    });

})()
