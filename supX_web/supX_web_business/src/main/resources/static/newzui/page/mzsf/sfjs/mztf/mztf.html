<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script type="application/javascript" src="../mztf/insurancePort/001gzydnh/001gzydnh.js"></script>
    <link href="rydj.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="">
<div class="printArea printShow"></div>
<div class="wrapper printHide background-f">
    <!--入院登记查询列表视图begin-->
    <div id="tableInfo" v-show="isShow" v-cloak>
        <!--入院登记功能按钮begin-->
        <div class="panel">
            <div class="tong-top flex-container flex-align-c">
                <!--<button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addData">退费界面</button>-->
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
                <div class="tool-left position">
                    <input type="number" title="点击后刷卡" @keydown="loadYkt($event)" class="zui-input"
                           placeholder="请点击后刷卡"/>
                </div>
            </div>
        </div>
        <!--入院登记功能按钮end-->
        <div class="flex bg-fff" style="padding: 13px 0;">
            <div class=" flex margin-l-10 ">
                <div class="flex  flex_items  margin-l-10">
                    <label class="whiteSpace margin-r-5 ft-14">时间段</label>
                    <div class="position margin-l13 flex-container flex-align-c">
                        <input class="zui-input todate wh180 " placeholder="请选择退费开始时间" id="timeVal"/>
                        <span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh180 " placeholder="请选择退费结束时间" id="timeVal1"/>
                    </div>
                </div>
            </div>
            <div class=" flex">
                <div class="flex  flex_items margin-l-10">
                    <label class="whiteSpace margin-r-5 ft-14">检索</label>
                    <div class="zui-input-inline margin-l13">
                        <input class="zui-input wh180" placeholder="请输入挂号序号/姓名" type="text" id="jsvalue"
                               @keydown.enter="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-r-10 padd-l-10" id="brRyList">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>性别</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>挂号日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>年龄</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>病人费别</span></div>
                        </th>
                        <!--
                        <th z-field="ryrq" z-width="100px">
                            <div class="zui-table-cell"><span>收费类型</span></div>
                        </th>
                        -->
                        <th>
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table" v-if="jsonList.length!=0">
                    <tbody>
                    <tr :tabindex="$index" v-for="(item, $index) in jsonList" class="tableTr2">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.ghxh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.ghrq,'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"
                                 v-text="(item.brnl || '')+(nldw_tran[item.nldw] || '')"></div>

                        </td>
                        <!--
                        <td>
                            <div class="zui-table-cell" v-text="item.ryksmc"></div>
                        </td>
                        -->
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fbmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="sfzt_tran[item.sfzt]"></div>
                        </td>
                        <td><!--操作-->
                            <div class="zui-table-cell cell-s icon-bj_parent">
                                <!--<i class="icon-icon icon-tk-h" title="退预缴费" ></i>-->
                                <i class="icon-icon icon-jf-h" title="退费"
                                   @click="yjfButtClick(item.ghxh,item.fphm,false,0,item)"></i>
                                <i v-if="item.fbmc == '城乡居民保险'" class="icon-icon icon-yjf" title="部分退费"
                                   @click="yjfButtClick(item.ghxh,item.fphm,false,1)"></i>
                                <!--<i class="icon-icon icon-dysqb" style="vertical-align: text-bottom;" title="打印申请"></i>-->
                            </div>
                        </td>
                    </tr>
                    </tbody>
                    <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
                </table>
            </div>
            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>
        </div>
        <!--循环列表end-->
    </div>
    <!--入院登记查询列表视图end-->

    <!--入院登记添加记录视图begin-->
    <!--入院登记添加记录视图end-->

    <!--收/退预交金begin-->
    <div id="syjjInfo" style="height: calc(100% - 66px);" v-if="isShow" v-cloak>
        <!--收/退预交金 功能按钮begin-->
        <div class="syjj-info-box flex-container flex-dir-c" style="height: 100%">
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">患者信息</div>
                </div>
                <div class="tab-card-body">
                    <div class="zui-form grid-box">
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">患者姓名</label>
                            <div class="zui-input-inline">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                                <input class="zui-input" :disabled="mzjbxxContent.isedit" v-model="mzjbxxContent.brxm"
                                       @input="searching0(false,'brxm',$event.target.value)"
                                       data-notEmpty="false" @keyDown="changeDown0($event,'brxm')"
                                       placeholder="姓名，挂号序号">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="page"
                                              @click-one="checkedOneOut" @click-two="selectOne0">
                                </search-table>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">病人性别</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" :disabled="mzjbxxContent.isedit"
                                       :title="mzjbxxContent.hospitalAD" :value="brxb_tran[mzjbxxContent.brxb]">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3 flex">
                            <label class="zui-form-label">年龄</label>
                            <input class="zui-input" type="text" disabled v-model="mzjbxxContent.brnl"/>
                            <div style="width: 72px">
                                <select-input
                                        disable="disabled"
                                        @change-data="resultChange"
                                        :not_empty="true"
                                        :child="nldw_tran"
                                        :index="mzjbxxContent.nldw"
                                        :val="mzjbxxContent.nldw"
                                        :name="'mzjbxxContent.nldw'">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">医疗卡余</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" disabled
                                       :title="fzContent.ylkye"
                                       v-model="mzjbxxContent.ylkye">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">医保卡余</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" disabled
                                       value="留着">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <!--<div class="zui-inline col-xxl-3">-->
                        <!--<label class="zui-form-label">费别</label>-->
                        <!--<select-input @change-data="resultMzsfChange" :not_empty="true"-->
                        <!--:child="brfbList" :index="'fbmc'" :index_val="'fbbm'" :val="fzContent.ryfbbm"-->
                        <!--:name="'fzContent.ryfbbm'" :search="true" :index_mc="'ryfbmc'">-->
                        <!--</select-input>-->
                        <!--</div>-->
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">保险</label>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="bxlbList" :index="'bxlbmc'" :index_val="'bxlbbm'"
                                          :val="fzContent.rybxlbbm"
                                          :search="true"
                                          :name="'fzContent.rybxlbbm'" :index_mc="'rybxlbmc'">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">门诊诊断</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" v-model="mzjbxxContent.jbmc" @keydown="nextFocus($event)">
                            </div>
                        </div>
						
						<div class="zui-inline col-xxl-3">
						    <label class="zui-form-label">是否异地</label>
						    <select-input  @change-data="resultChange" id="sfyd"
						                  :child="sfydList" :index="sfyd" :val="sfyd"
						                  :search="true" :name="'sfyd'" :not_empty="true">
						    </select-input>
						</div>
						
                    </div>
                </div>
                <div class="tab-card flex-container flex-dir-c flex-one">
                    <div class="tab-card-header">
                        <div class="tab-card-header-title">费用明细</div>
                    </div>
                    <div class="tab-card-body flex-one flex-container flex-dir-c">
                        <div class="zui-table-view flex-container flex-dir-c" id="yjjlTable">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <input-checkbox
                                                    @result="checkSelectTf(['all','brfyjsonList',isCheckAll],$event)"
                                                    :list="'brfyjsonList'"
                                                    :type="'all'" :val="isCheckAll">
                                            </input-checkbox>
                                        </th>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>发票号码</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xl text-left"><span>费用项目</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>组合费用</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>单价</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>数量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>金额</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>收费时间</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>医嘱号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>收费人</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>支付类型</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>优惠比例</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>优惠金额</span></div>
                                        </th>
                                        <!--<th>
                                            <div class="zui-table-cell cell-s"><span>收费日期</span></div>
                                        </th>-->
                                        <!--<th>-->
                                        <!--<div class="zui-table-cell cell-s"><span>操作</span></div>-->
                                        <!--</th>-->
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body over-auto " @scroll="scrollTable($event)">
                                <table class="zui-table table-width50">
                                    <tbody>
                                    <tr v-for="(item, $index) in brfyjsonList" v-if="getIfShow(item.sftf)"
                                        :class="[{'table-hovers':isChecked[$index]},{'tableTr': $index%2 == 0},{'bg-red':item.sftf==1}]"
                                        @click="checkSelectTf([$index,'some','brfyjsonList'],$event,item)">
                                        <!--@click="单击回调" @dblclick="edit($index)"双击回调-->
                                        <td class="cell-m">
                                            <input-checkbox @result="reCheckBoxTf" :list="'brfyjsonList'"
                                                            :type="'some'" :which="$index"
                                                            :val="isChecked[$index]">
                                            </input-checkbox>
                                        </td>
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m" v-text="$index+1"><!--序号--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.fphm"><!--发票号码--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xl text-over-2 text-left"
                                                 v-text="item.mxfyxmmc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.zhfymc"><!--支付类型--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.fydj"><!--业务窗口--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.fysl"><!--操作员姓名--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.fyje"><!--科室名称--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="fDate(item.sfsj,'datetime')">
                                                <!--科室名称--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.yzhm"><!--预交金额--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.czyxm"><!--预交金额--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.zflxmc"><!--预交金额--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.yhbl"><!--预交金额--></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.yhje"><!--预交日期--></div>
                                        </td>

                                        <!--<td ><div class="zui-table-cell cell-s icon-bj_parent">-->
                                        <!--<i class="icon-bj" @click="edit($index,item.yzhm,item.yzlx),doPop($index)"></i>-->
                                        <!--</div></td>-->
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <model style="top:10%" :s="'确定'" :c="'取消'" @default-click="bxShow=false" @result-clear="bxShow=false"
                   :model-show="false" @result-close="bxShow=false" v-if="bxShow" :title="'请输入参合人员信息'">
                <div class="chxx_model" style="height: 100%">
                    <div class="flex-container flex-dir-c flex-one" id="loadPage">
                    </div>
                </div>
            </model>
            <div class="action-bar fixed zui-table-tool flex-container flex-align-c">
                <button :disabled="!ifClick" class="zui-btn btn-primary xmzb-db" @click="saveData">退费</button>
                <!--<button :disabled="!ifClick" class="zui-btn btn-warning xmzb-db" @click="refundMethod">电子凭证退费</button>
				<button :disabled="!ifClick" class="zui-btn btn-warning xmzb-db" @click="qxzx">取消执行</button>-->
                <!-- <button :disabled="!ifClick" class="zui-btn btn-primary xmzb-db" @click="bftf" >部分退费</button> -->
				<button class="zui-btn btn-default xmzb-db" @click="ybjsd">医保结算单</button>
                <button class="zui-btn btn-default xmzb-db" @click="fymxFun">医保结算明细单</button>
                <button class="zui-btn btn-default xmzb-db" @click="quxiao">取消</button>
				
                <p>&ensp;&ensp;合计:&ensp;<span
                        style="color:#d25747; font-weight: bolder;font-size: 18px;">{{fyhjAll}}元</span></p>
            </div>
        </div>

    </div>
</div>
</body>
<script type="application/javascript" src="/newzui/pub/js/uuid.js"></script>
<script type="application/javascript" src="/newzui/pub/js/insuranceGbUtils.js"></script>
<script src="backFun.js" type="text/javascript"></script>
<script src="mztf.js" type="text/javascript"></script>
</html>
