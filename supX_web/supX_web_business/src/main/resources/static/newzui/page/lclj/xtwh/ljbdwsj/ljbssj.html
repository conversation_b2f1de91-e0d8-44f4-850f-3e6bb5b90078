<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="style.css">
    <link href="/newzui/css/common.css" rel="stylesheet" type="text/css"/>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="/newzui/js/zui/zui.min.css" rel="stylesheet" type="text/css"/>
    <!--<script type="text/javascript" src="js/color.js"></script>-->
    <!--<script type="application/javascript" src="/newzui/pub/top.js"></script>-->
    <style>
        .ksys-side .Shape{
            display: inline-block;
            width: 20px;
        }
    </style>
</head>
<body class="bgbs">
<div class="bb-view" id="bbView" v-if="bbViewIS">
    <div id="toolbar" class="panel-head border-bottom  background_top">
        <div class="zui-row">
            <div class="col-x-12 ybhsgl-height" style="margin: 8px">

                <button   class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
                <button   class="tong-btn btn-parmary icon-sj paddr-r20" @click="addData">设计</button>
                <!--<button  class="tong-btn btn-parmary-b paddr-r5" @click="edit">打开格式(SQL)</button>-->
                <!--<button  class="tong-btn btn-parmary-b paddr-r5 icon-sc-header" @click="copySave">另存为</button>-->
                <button  class="tong-btn btn-parmary-b paddr-r25 icon-dc icon-hf" @click="exportRy">恢复原样</button>
                <button  class="tong-btn btn-parmary-b paddr-r5 icon-yl" @click="yl">预览</button>
                <button  class="tong-btn btn-parmary-b paddr-r5  icon-dysq" @click="print">打印</button>
            </div>
            <div class="col-x-2" >
                <div class="tool-left bgbs" >
                    <div class="" style="margin: 14px 25px; display: flex; align-self: center; justify-content: center; align-items: center;"><i style="margin-right: 5px">关键字检索</i>
                        <input type="text" id="jsvalue" @input="getData()" style="width: 65%" class="zui-input inp_search" placeholder="请输入关键字"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
   <div class="col-x-2">
       <div class="list tree_tem1">
           <ul >
               <li class="xtmktreediv"><img src="/newzui/pub/image/toggle03.png" class="toggleIMg"><div class="tree_text1">路径病种</div></li>
               <ul>
                   <li @click="getContent(item)" v-for="(item,index) in jsonList" class="xtmktreediv"><img src="/newzui/pub/image/toggle01.png" class="toggleIMg" /><div :class="{'bold':num==index}" class="tree_text1">{{item.ljmc}}</div></li>
               </ul>
           </ul>
       </div>
   </div>
   <div  class="bgbs col-x-10">
       <div class="demoonstrate chakandata">

       </div>
   </div>
</div>
<div  class="max-html sheji hide">
    <div class="left">
        <div class="header">
            <div class="gj text" :class="{'active':index==0}" @click="getmb(0)">工具</div>
            <div class="mb text" :class="{'active':index==1}" @click="getmb(1)">模板</div>
        </div>
        <div class="main" v-if="index==0">
            <div class="jczj">基础组建</div>
            <div class="content">
                <ul class="itemzj">
                    <li @mousedown="drag(list.name,list.type,list.title)" :data-title="list.title" :data-type="list.type" class="list" v-for="list in objList">
                        <img :src="list.src">
                        <p v-text="list.name"></p>
                    </li>
                </ul>
            </div>
            <div class="footer">
                <ul class="itemzj">
                    <li class="list" v-for="list in footerlist">
                        <img :src="list.src" :title="list.name">
                    </li>
                </ul>
            </div>
        </div>
        <div v-if="index==1">
            <div class="zui-table-view">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <th z-field="ry" z-style="text-align:center;" z-width="100px">
                            <div class="zui-table-cell"><span>模板名称</span></div>
                        </th>
                        <th z-field="lx" z-style="text-align:center;" z-width="100px">
                            <div class="zui-table-cell"><span>类型</span></div>
                        </th>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @click="checkOne($index),edit($index)"
                            :class="[{'tableTrSelect':isChecked == $index},{'tableTr': $index%2 == 0},
                {'initTem': item.repid == null}]" ref="list">
                            <td>
                                <div class="zui-table-cell">{{item.repname}}</div>
                            </td>
                            <td>
                                <div class="zui-table-cell">{{item.types}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="right">
        <div class="header setSava">
            <span class="left-text">{{json.repname}}</span>
            <div class="right-icon">
                <span class="icon-sava" @click="xz()">新增</span>
                <span class="icon-sava" @click="savaList()">保存</span>
                <span class="icon-lc" @click="savaAs">另存为模版</span>
                <span class="icon-rest" @click="restore()">还原</span>
                <span class="icon-szmb" @click="Setting('headline','content')">设置模板</span>
                <span class="icon-close" @click="padlock()">关闭</span>
            </div>
        </div>
       <div class="main">
           <div class="content demoonstrate xinzengdata tem"  v-if="tagShow"  :style="{width:json.k+'px' ,height:json.g+'px'} ">
               <div class="showTemList">
                   <div class="trendAll" >
                       <div class="trendDiv" v-if="trend">
                           <div class="item mousedown " v-for="(item, $index) in trendList" :data-title="item.title" :data-type="item.type" >
                               <span :id="guid()" class="t-values mouitem" :class="item.name"  v-text="item.name"></span>
                           </div>
                       </div>
                   </div>
                   <div class="temTable mousedown " id="temTable" v-if="isTableShow" >
                       <table class="printTable" cellspacing="0" cellpadding="0">
                           <tr  v-for="(item_tr, $index) in trJson" :id="guid()" @click="deleteevent($event)"
                                @dblclick="setbg('form',$event)">
                               <td :id="guid()" v-for="item_td in tdJson"><P
                                       style="height: 100%;position: absolute; right: 0;top: 0; width: 1px;z-index: 11;"
                                       class="drawWidth mousedown "></P></td>
                           </tr>

                       </table>
                   </div>
               </div>
           </div>
           <div class="demoonstrate  test001 showTem" v-if="!tagShow">
           </div>
       </div>
        <div class="cler"></div>
    </div>
    <div class="sideForm ng-hide">
        <div class="tab-message title">
            <a class="text">{{title}}</a>
            <span class="fr closex " onclick="closeX(false)">X</span>
        </div>
        <div class="ksys-side" id="brzcList">
            <div class="header">
                <div class="tab">
                    <div class="kjsx line " :class="{'active':index==0}" @click="tab(0)">控件属性</div>
                    <div class="bds line" :class="{'active':index==1}" @click="tab(1)">表达式</div>
                </div>
                <div class="no-xiugai" v-if="index==0">默认属性（不允许修改）</div>
                <div class="id-list" v-if="index==0">
                    <div class="left-id value-line">文本id</div>
                    <div class="right-value value-line color-yello" :title="obj.TextControl">{{obj.TextControl}}</div>
                </div>
                <div class="no-xiugai" v-if="index==0">基本属性</div>
            </div>
            <div class="content" v-if="index==0">
                <div class="id-list" v-if="istypeindex=='money'">
                    <div class="left-id value-line">金额控件id</div>
                    <div class="right-value value-line select">
                        <select class="border-sr value-select" v-model="obj.Amountmoney">
                            <option value="88888888">88888888</option>
                        </select>
                    </div>
                </div>
                <div class="id-list" v-if="istypeindex=='txt'">
                    <div class="left-id value-line">内容</div>
                    <input class="right-value value-line border-sr" v-model="obj.mbtxt"/>
                    <!--<div class="right-value value-line border-sr" @keyup="changeData($event,'mbtxt')" contenteditable="true">{{mbtxt}}</div>-->
                </div>
                <div class="id-list" v-if="istypeindex=='Dynamictext'">
                    <div class="left-id value-line">数据源</div>
                    <input class="right-value value-line border-sr" v-model="obj.sjy"/>
                    <!--<div class="right-value value-line border-sr" contenteditable="true">{{sjy}}</div>-->
                </div>
                <p class="xtsx">
                <div class="id-list"
                     v-if="istypeindex=='headline'">
                    <div class="left-id value-line">标题</div>
                    <input class="right-value value-line border-sr" v-model="obj.headline"/>
                    <!--<div class="right-value value-line border-sr out" contenteditable="true">{{widthbg}}</div>-->
                </div>
                <div class="id-list"
                     v-if="istypeindex=='straightline'||istypeindex=='verticalline'||istypeindex=='form' || istypeindex=='headline'">
                    <div class="left-id value-line">宽度</div>
                    <input class="right-value value-line border-sr" v-model="obj.widthbg"/>
                    <!--<div class="right-value value-line border-sr out" contenteditable="true">{{widthbg}}</div>-->
                </div>
                <div class="id-list"
                     v-if="istypeindex=='straightline'||istypeindex=='verticalline'||istypeindex=='form' || istypeindex=='headline'">
                    <div class="left-id value-line">高度</div>
                    <!--<div class="right-value value-line border-sr out" contenteditable="true">{{heightbg}}</div>-->
                    <input class="right-value value-line border-sr" v-model="obj.heightbg"/>
                </div>
                </p>
                <div class="id-list" v-if="istypeindex=='txt'|| istypeindex=='Dynamictext'">
                    <div class="left-id value-line">文本格式</div>
                    <div class="right-value value-line select">
                        <select class="border-sr value-select" v-model="obj.textForm">
                            <option value="bold">粗体</option>
                            <option value="oblique">斜体</option>
                            <option value="underline">下划线</option>
                            <option value="through">删除线</option>
                        </select>
                    </div>
                </div>
                <div class="id-list" v-if="istypeindex=='txt'|| istypeindex=='Dynamictext'|| istypeindex=='headline'">
                    <div class="left-id value-line">字体</div>
                    <div class="right-value value-line select">
                        <select class="border-sr value-select" v-model="obj.Typeface">
                            <option value="微软雅黑">微软雅黑</option>
                        </select>
                    </div>
                </div>
                <div class="id-list" v-if="istypeindex=='txt'|| istypeindex=='Dynamictext'">
                    <div class="left-id value-line">字体大小</div>
                    <!--<div class="right-value value-line border-sr" contenteditable="true">{{fontSize}}</div>-->
                    <input class="right-value value-line border-sr" v-model="obj.fontSize"/>
                </div>
                <div class="id-list" v-if="istypeindex=='txt'|| istypeindex=='Dynamictext'">
                    <div class="left-id value-line">对齐方式</div>
                    <div class="right-value value-line select">
                        <select class="border-sr value-select" v-model="obj.align">
                            <option value="center">居中</option>
                            <option value="left">左对齐</option>
                            <option value="right">右对齐</option>
                        </select>
                    </div>
                </div>
                <div class="id-list"
                     v-if="istypeindex=='Dynamictext'||istypeindex=='straightline'||istypeindex=='verticalline'||istypeindex=='form'||istypeindex=='txt'">
                    <div class="left-id value-line">
                        {{istypeindex=='txt'?'字体':istypeindex=='form'?'表格':istypeindex=='straightline'?'直线':istypeindex=='Dynamictext'?'字体':'竖线'}}颜色
                    </div>
                    <!--<div class="right-value value-line border-sr" contenteditable="true">{{fontColor}}</div>-->
                    <input class="right-value value-line border-sr" v-model="obj.fontColor"/>
                </div>
                <div class="id-list"
                     v-if="istypeindex=='form'">
                    <div class="left-id value-line cursor" @click="addRow">增加一行</div>
                    <div class="right-value value-line  cursor" @click="addCol">增加一列</div>
                </div>
                <div class="id-list"
                     v-if="istypeindex=='form'">
                    <div class="left-id value-line cursor" @click="deleteRow">删除一行</div>
                    <div class="right-value value-line cursor" @click="deleteCol">删除一列</div>
                </div>
                <div class="color">
                    <ul class="color-item">
                        <!--boxShadow:[' 0 0 6px 0']-->
                        <li class="color-list" v-for="(c,index) in color"
                            :style="{backgroundColor: c,boxShadow:[index==num?setcolor:'']}"
                            @click="getColor(c,$event,index)"></li>
                        <span class="Shape" @click="clickRgb"></span>
                    </ul>
                </div>
            </div>
            <div class="biaodashi" v-if="index==1">
                <textarea class="value-textarea" placeholder="请输入表达式" v-model="obj.expression"></textarea>
            </div>
        </div>
        <article id="picker" class="js-content hide">
            <section class="row">
                <div id="js-picker" class="colorPicker"></div>
            </section>
        </article>
        <!--<div class="ksys-btn">-->
        <!--<button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>-->
        <!--<button class="zui-btn btn-primary xmzb-db" @click="confirms">确定</button>-->
        <!--</div>-->
    </div>
    <div  id="pop">
        <transition name="pop-fade">
            <div class="pop" v-if="isShow">
                <div class="popCenter">
                    <div class="save">
                        <div class="title">
                            模板名称
                            <span @click="cancel" class="closex">X</span>

                        </div>
                        模板名称&emsp;<input v-model="json.repname" :autofocus="autoFocus" id="temName" type="text"/><br>
                       <div class="footerSave">
                           <button class="zui-btn btn-primary xmzb-db" @click="save">保存</button>
                       </div>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</div>
<div class="viewPop" v-if="isview">
    <div class="popview"></div>
    <div class="contentView">
        <div class="viewHeadder">
            <span class="leftText">自定义报表定义</span>
            <span class="rightText" @click="closes">X</span>
        </div>
        <div class="contentItem" @keyup.enter="saveData">
            <div class="viewList">
                <span>使用模版</span>
                <input v-model="sessData.mb"  @keydown="nextFocus($event)" class="zui-input"/>
            </div>
            <div class="viewList">
                <span>中文名称</span>
                <input v-model="sessData.repname" @blur="setPYDM(sessData.repname, 'sessData', 'yw')" @keydown="nextFocus($event)" class="zui-input"/>
            </div>
            <div class="viewList">
                <span>英文名称</span>
                <input v-model="sessData.yw" @keydown="nextFocus($event)" class="zui-input"/>
            </div>
            <div class="viewList">
                <div class="col-x-6 position">
                    <span>打印宽度</span>
                    <input v-model="sessData.k"  @keydown="nextFocus($event)" style="width: 70%" class="zui-input"/>
                    <span class="cm" style="right: 26px">cm</span>
                </div>
                <div class="col-x-6 position">
                    <span>打印高度</span>
                    <input v-model="sessData.g" @keydown="nextFocus($event)" style="width: 76%" class="zui-input"/>
                    <span class="cm">cm</span>
                </div>
                <div class="clear"></div>
            </div>
            <div class="viewList">
                <span class="excel">制定输出excel模版</span>
                <input v-model="sessData.excel" @keydown="nextFocus($event)" style="width: 87%"  class="zui-input"/>
            </div>
        </div>
            <div class="ksys-btn">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button class="zui-btn btn-primary xmzb-db" @click="saveData">确定</button>
                <div class="clear"></div>
            </div>
    </div>
</div>
</body>

<script type="text/javascript" src="color.js"></script>
<script type="text/javascript" src="/lib/jquery.min.js"></script>
<script type="text/javascript" src="/newzui/js/zui/jquery.zui.js"></script>
<script type="text/javascript" src="/lib/vue/vue.js"></script>
<script type="text/javascript" src="/lib/vue/vue-resource.js"></script>
<script type="text/javascript" src="/newzui/pub/js/common.js"></script>
<script type="text/javascript" src="/newzui/pub/js/jsg.min.js"></script>
<script type="text/javascript" src="/pub/js/printTemplets.js"></script>
<script type="text/javascript" src="style.js"></script>
<script type="text/javascript" >
    $(".zui-table-view").uitable();
</script>
</html>

