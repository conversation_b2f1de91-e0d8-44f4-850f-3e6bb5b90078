var gdfy = new Vue({
    el: '#loadingPage',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        br: {},
        fyisCheckAll: false,
        fyisChecked: [],
        bdisCheckAll: false,
        bdisChecked: [],
        num: 0,
        fylist: [],//费用列表
        bdlist: [],//变动记录列表
        zyh: null,
        addShow: true,//添加按钮展示
    },
    mounted: function () {
        this.moun();
        window.addEventListener("storage", function (e) {
            if (e.key == "gdfy" && e.oldValue !== e.newValue) {
                gdfy.moun();
            }
        });
    },
    updated: function () {
        changeWin();
    },
    methods: {
        moun: function () {
            this.br = JSON.parse(sessionStorage.getItem("gdfy"));
            this.ininGdData();
        },
        quxiao: function () {
            this.topClosePage(
                'page/hsz/hlyw/bygl/gdfy.html',
                'page/hsz/hlyw/bygl/bygl.html');
        },
        //保存项目
        saveGdfy: function () {
            if (this.zyh == null || this.zyh == undefined) {
                malert("请选择病人之后再执行此操作！", 'top', 'defeadted');
                return
            }
            var saveList = [];
            for (var i = 0; i < gdfy.fylist.length; i++) {
                if (gdfy.fylist[i].isxz == true) {
                    var fy = {};
                    fy.zyh = this.zyh;
                    fy.mxfyxmbm = gdfy.fylist[i].mxfyxmbm;
                    fy.mxfyxmmc = gdfy.fylist[i].mxfyxmmc;
                    fy.yl = gdfy.fylist[i].yl;
                    fy.dj = gdfy.fylist[i].dj;
                    fy.xh = gdfy.fylist[i].xh;
                    fy.djty = gdfy.fylist[i].djty;
                    saveList.push(fy);
                }
            }
            if (saveList.length == 0) {
                malert("无新增数据！", 'top', 'defeadted');
                return false;
            }
            var json = '{"list":' + JSON.stringify(saveList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=saveBrgdfy&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert("保存成功", 'top', 'success');
                    gdfy.ininGdData();
                } else {
                    malert("保存失败", 'top', 'defeadted')
                }
            }, function (error) {
                console.log(error);
            });
        },

        //删除项目
        deleteXm: function () {
            if (this.zyh == null || this.zyh == undefined) {
                malert("请选择病人之后再执行此操作！", 'top', 'defeadted');
                return
            }
            var fy = {};
            var deleteList = [];
            if (gdfy.isChecked.length <= 0) {
                malert("请选中您要删除的数据", 'top', 'defeadted');
                return false
            }
            for (var i = 0; i < gdfy.isChecked.length; i++) {
                if (gdfy.isChecked[i] == true) {
                    if (gdfy.fylist[i].isxz) {
                        gdfy.fylist[i].remove = true
                    } else {
                        fy.zyh = this.zyh;
                        fy.mxfyxmbm = gdfy.fylist[i].mxfyxmbm;
                        fy.mxfyxmmc = gdfy.fylist[i].mxfyxmmc;
                        fy.yl = gdfy.fylist[i].yl;
                        fy.dj = gdfy.fylist[i].dj;
                        deleteList.push(JSON.parse(JSON.stringify(fy)));
                    }
                }
            }
            for (var j = gdfy.fylist.length - 1; j >= 0; j--) {
                if (gdfy.fylist[j].remove) {
                    gdfy.fylist.splice(j , 1)
                    gdfy.isChecked = [];
                    gdfy.isCheckAll = false;
                }
            }
            if (deleteList.length > 0) {
                var json = '{"list":' + JSON.stringify(deleteList) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=deleteBrgdfy',
                    json).then(function (data) {
                    if (data.body.a == 0) {
                        malert("删除成功", 'top', 'success');
                        gdfy.isChecked = [];
                        gdfy.isCheckAll = false;
                        gdfy.ininGdData();
                        /*if(type=='some'){

                        }else {
                            gdfy.fylist=[];
                            gdfy.ininGdData();
                        }*/
                    } else {
                        malert("删除失败", 'top', 'defeadted')
                    }
                }, function (error) {
                    console.log(error);
                });
            }
        },

        //编辑
        edit: function (num) {
            lr.title = '编辑';
            //这里要拷贝值到popContent中，不能直接=
            lr.fzContent = JSON.parse(JSON.stringify(this.fylist[num]));
            lr.fzContent.mxfymc = lr.fzContent.mxfyxmmc;
            lr.fzContent.mxfybm = lr.fzContent.mxfyxmbm;
            lr.fzContent.fydj = lr.fzContent.dj;
            lr.mxfyContent.mxfyxmmc = lr.fzContent.mxfyxmmc;
            lr.fzContent.yl = lr.fzContent.yl;
            lr.open();
        },

        tabBg: function (index) {
            if (index == 0) {
                gdfy.addShow = true;
            } else {
                gdfy.addShow = false;
            }
            this.num = index;
            gdfy.ininGdData();
        },

        shenhe: function () {
            tspop.open();
        },
        lr: function () {
        	lr.title = '新增';
            lr.open();
        },
        ininGdData: function () {
            common.openloading('.zui-table-body');
            this.zyh = this.br.zyh;
            var parm = {
                zyh: this.zyh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=queryBrgdfy&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    gdfy.fylist = json.d.list;
                    common.closeLoading();
                    for (var i = 0; i < gdfy.fylist.length; i++) {
                        gdfy.fylist[i].fyje = gdfy.fylist[i].dj * gdfy.fylist[i].yl;
                    }
                    gdfy.getGdfybdjl(gdfy.zyh);
                } else {
                    malert(json.c, "固定费用查询失败：", 'top', 'defeadted');
                    common.closeLoading()
                }
            });
        },

        //获取变动记录
        getGdfybdjl: function (zyh) {
            var parm = {
                zyh: zyh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=queryBrgdfybdjl&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    gdfy.bdlist = json.d.list;
                } else {
                    malert(json.c, "固定费用变动记录查询失败：", 'top', 'defeadted');
                    common.closeLoading()
                }
            });
        },
    },
});
var lr = new Vue({
    el: '#lr',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    components: {
        'search-table': searchTable,
    },
    data: {
        isShow: false,
        title: "新增",
        them_tran: {
            'fylx': dic_transform.data.fylx_tran,
            'fylb': dic_transform.data.fylb_tran,
            'nbtclb': dic_transform.data.istrue_tran,
            'sfgd': dic_transform.data.istrue_tran,
            'ypfy': dic_transform.data.ypfy_tran,
            'zhfy': dic_transform.data.sfzhfy_tran
        },
        them: {
            '类型': 'zhfy',
            '明细费用名称': 'mxfymc',
            '拼音代码': 'pydm',
            '费用规格': 'fygg',
            '费用类别编码': 'fylbmc',
            '费用类型': 'fylx',
            '费用统筹类别': 'tclbmc',
            '农合报价': 'nbtclb',
            '费用单价': 'fydj',
            '执行科室': 'zxksmc',
            '是否固定': 'sfgd',
            '药品费用': 'ypfy',
            '费用类别': 'ffylb',
            '优惠比例': 'yhbl'
        },
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 20,
            total: null
        },
        mxfyContent: {},
        fzContent: {
            yl: 1,
        },
        djty: '1'
    },
    watch: {
        "fzContent.yl": function (newVal, oldVal) {
            if (newVal < 0 || newVal === "") {
                this.fzContent.yl = 0;
            }
            if (newVal !== oldVal && this.fzContent.fydj >= 0) {
                this.fzContent.fyje = this.fzContent.yl * this.fzContent.fydj;
            }
        }
    },
    methods: {
    	jszje:function(){
    		lr.fzContent.fyje = lr.fzContent.yl * lr.fzContent.fydj;
    	},
        //关闭
        closes: function () {
            lr.fzContent = {};
            lr.mxfyContent = {};
            this.isShow = false;
        },
        open: function () {
            this.isShow = true;
        },
        // 修改项目
        updateGdfy: function () {
            if (gdfy.zyh == null || gdfy.zyh == undefined) {
                malert("请选择病人之后再执行此操作！", 'top', 'defeadted');
                return
            }
            var arr = [];
            lr.fzContent.dj = lr.fzContent.fydj;
            arr.push(lr.fzContent);
            var json = '{"list":' + JSON.stringify(arr) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=updateBrgdfy&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert("修改成功", 'top', 'success');
                    gdfy.ininGdData();
                } else {
                    malert("修改失败", 'top', 'defeadted')
                }
            }, function (error) {
                console.log(error);
            });
        },
        
      //保存项目
        saveGdfy: function () {
            if (gdfy.zyh == null || gdfy.zyh == undefined) {
                malert("请选择病人之后再执行此操作！", 'top', 'defeadted');
                return
            }
            var saveList = [];
            for (var i = 0; i < gdfy.fylist.length; i++) {
                if (gdfy.fylist[i].isxz == true) {
                    var fy = {};
                    fy.zyh = gdfy.fylist[i].zyh;
                    fy.mxfyxmbm = gdfy.fylist[i].mxfyxmbm;
                    fy.mxfyxmmc = gdfy.fylist[i].mxfyxmmc;
                    fy.yl = gdfy.fylist[i].yl;
                    fy.dj = gdfy.fylist[i].dj;
                    fy.xh = gdfy.fylist[i].xh;
                    fy.djty = gdfy.fylist[i].djty;
                    saveList.push(fy);
                }
            }
            if (saveList.length == 0) {
                malert("无新增数据！", 'top', 'defeadted');
                return false;
            }
            var json = '{"list":' + JSON.stringify(saveList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=saveBrgdfy&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert("保存成功", 'top', 'success');
                    gdfy.ininGdData();
                } else {
                    malert("保存失败", 'top', 'defeadted')
                }
            }, function (error) {
                console.log(error);
            });
        },
        save: function () {
            if (this.mxfyContent.mxfyxmmc == undefined) {
                malert('项目名称不能为空!', 'top', 'defeadted');
                return false;
            }
            if (this.fzContent.yl == undefined) {
                malert('数量不能为空!', 'top', 'defeadted');
                return false;
            }
            if (this.fzContent.zhfy == '1') {//如果是组合费用则先查询出明细费用后循环赋值
                var jsons = {
                    mxfybm: lr.fzContent.mxfybm
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=queryZhfy&json=' + JSON.stringify(jsons),
                    function (data) {
                        if (data.a == 0) {
                            if (data.d.list.length > 0) {
                                for (var i = 0; i < data.d.list.length; i++) {
                                    for (var j = 0; j < gdfy.fylist.length; j++) {
                                        if (data.d.list[i].mxfybm == gdfy.fylist[j].mxfyxmbm) {
                                            malert('此费用项目已记录，请重新选择!', 'top', 'defeadted');
                                            return;
                                        }
                                    }
                                    var xh = null;
                                    if (gdfy.fylist.length > 0) {
                                        xh = gdfy.fylist[gdfy.fylist.length - 1].xh + 1;
                                    } else {
                                        xh = 1;
                                    }
                                    var xzxm = {
                                        'zyh': gdfy.zyh,
                                        'mxfyxmmc': data.d.list[i].mxfymc,
                                        'mxfyxmbm': data.d.list[i].mxfybm,
                                        'yl': lr.fzContent.yl,
                                        'dj': data.d.list[i].fydj,
                                        'xh': xh,
                                        'isxz': true,
                                        'djty': lr.djty
                                    };
                                    gdfy.fylist.push(xzxm);
                                }
                                lr.fzContent = {
                                    yl: 1
                                };
                                lr.saveGdfy();
                                lr.mxfyContent = {};
                                $("#mxfyxmmc").focus();
                            } else {
                                malert('未查到相关记录', 'top', 'defeadted');
                            }
                        } else {
                            malert('查询失败' + data.c, 'top', 'defeadted');
                        }
                    });
            } else {
                for (var i = 0; i < gdfy.fylist.length; i++) {
                    if (lr.fzContent.mxfybm == gdfy.fylist[i].mxfyxmbm) {
                        malert('此费用项目已记录，请重新选择!', 'top', 'defeadted');
                        return;
                    }
                }
                var xh = null;

                if (gdfy.fylist.length > 0) {
                    xh = gdfy.fylist[gdfy.fylist.length - 1].xh + 1;
                } else {
                    xh = 1;
                }
                var xzxm = {
                    'zyh': gdfy.zyh,
                    'mxfyxmmc': lr.fzContent.mxfymc,
                    'mxfyxmbm': lr.fzContent.mxfybm,
                    'yl': lr.fzContent.yl,
                    'dj': lr.fzContent.fydj,
                    'xh': xh,
                    'isxz': true,
                    'djty': lr.djty
                };
                gdfy.fylist.push(xzxm);
                lr.saveGdfy();
                lr.fzContent = {};
                lr.mxfyContent = {};
                $("#mxfyxmmc").focus();
                //						this.isShow = false;
            }
        },
        //下拉框检索
        searching: function (add, val) {
            lr.mxfyContent['mxfyxmmc'] = val;
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (lr.mxfyContent['mxfyxmmc'] == null || lr.mxfyContent['mxfyxmmc'] == "") {
                this.page.parm = "";
            } else {
                this.page.parm = lr.mxfyContent['mxfyxmmc'];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=queryJzxm'
                + '&dg=' + JSON.stringify(str_param),
                function (data) {
                    if (data.d.list.length > 0) {
                        if (add) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                lr.searchCon.push(data.d.list[i]);
                            }
                        } else {
                            lr.searchCon = data.d.list;
                        }
                    }
                    lr.page.total = data.d.total;
                    lr.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
        },

        //费用项目检索
        changeDown: function (event, type) {
            if (type == 'mxfyxmmc') {
                if (this['searchCon'][this.selSearch] == undefined) return;
            }
            this.keyCodeFunction(event, 'mxfyContent', 'searchCon');
            // 选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'mxfyxmmc') {
                    Vue.set(this.mxfyContent, 'mxfyxmmc', this.mxfyContent.mxfymc);
                    Vue.set(this.fzContent, 'fydj', this.mxfyContent.fydj);
                    this.fzContent.zhfy = this.mxfyContent.zhfy;
                    this.fzContent.mxfybm = this.mxfyContent.mxfybm;
                    this.fzContent.mxfymc = this.mxfyContent.mxfymc;
                    this.fzContent.fyje = this.fzContent.yl * this.fzContent.fydj;
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                }
            }
        },

        //鼠标双击（费用项目）
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, this.mxfyContent['mxfyxmmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                this.mxfyContent = item;
                Vue.set(this.mxfyContent, 'mxfyxmmc', this.mxfyContent.mxfymc);
                Vue.set(this.fzContent, 'fydj', this.mxfyContent.fydj);
                this.fzContent.zhfy = this.mxfyContent.zhfy;
                this.fzContent.mxfybm = this.mxfyContent.mxfybm;
                this.fzContent.mxfymc = this.mxfyContent.mxfymc;
                this.fzContent.fyje = this.fzContent.yl * this.fzContent.fydj;
                this.nextFocus(event);
                $(".selectGroup").hide();
            }
        },
        bclr: function () {
            lr.save();
            $("#mxfyxmmc").focus();
        },
    }
});

$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});

$(document).keydown(function (e) {
    // F2门诊收费保存
    if (e.keyCode == 113) {
        //        toolMenu.saveData();// 保存
    }
    // F12保险快捷键
    if (e.keyCode == 123) {
        // 这里暂时不知道怎么处理
    }
});