var activeX = document.getElementById("csharpActiveX");

function CreatXmlDoc(obj) {
	this.tagName = obj.tagName;
	var children = obj.children.map(function (item) {
		if (typeof item == "object") {
			item = new CreatXmlDoc(item)
		}
		return item
	})
	this.children = children;
}


CreatXmlDoc.prototype.render = function () {
	var el = document.createElement(this.tagName);
	var children = this.children || [];
	children.forEach(function (child) {
		var childEl = (child instanceof CreatXmlDoc)
			? child.render()
			: document.createTextNode(child)
		el.appendChild(childEl);
	})
	return el
}
var gz_002 = new Vue({
	el: '.gz_002',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data: {
		isShow: false,
		bxlbbm: null,
		bxurl: null,
		birthday: null,
		text: null,
		jbContent: {},
		searchCon: [],
		selSearch: -1,
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		them_tran: {},
		them: {
			'疾病编码': 'yke120',
			'疾病名称': 'yke121',
			'副编码': 'yke223'
		},
		zdxxJson: {
			prm_aka130: "11",
		},
		brzt_tran:{
			'1':'在院',
			'2':'未在院'
		},
		grxxJson: {},
		sfsbContent: {},
		cssg: false,
		userInfo: {},
		gzyhybInit: false,
		gzyhybContent: {},
		brxxContent: {},
		brfyList: {},
		yjsContentGzyhyb: {},
		zxlshSn: '',
		result: '',
		ybuuid:''
	},
	mounted: function () {
		this.isShow = true;
		this.init();
		this.getUserInfo();
		console.log('qqqqqqqqqqqqqqqqqqqqqqqq')
	},
	methods: {
		init: function () {
			var that = this;
			$.post("http://localhost:9089/init", {}, function (json) {
				if (json.aint_appcode > 0) {
					that.gzyhybInit = true;
					malert("初始化成功!");
				} else {
					malert("医保控件初始化失败！请从新打开页面!");
				}
			});
		},

		//读卡
		load: function () {
			if (this.gzyhybInit) {
				var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai></input>';
				$.post("http://localhost:9089/call", {
					jybh: "03",
					jysr_xml: jysr
				}, function (json) {
					if (json.aint_appcode > 0) {
						gz_002.sfsbContent = JSON.parse(json.astr_jysc_xml);
						gz_002.grxxJson = gz_002.sfsbContent;
						malert("读卡成功!");
					} else {
						malert(json.astr_appmasg);
					}
				});
			} else {
				malert("医保控件未初始化,请重新打开页面！", 'top', 'defeadted');
			}
		},

		//引入
		enter: function () {
			if (Object.keys(gz_002.grxxJson).length === 0) {
				malert("请先读卡", "top", "defeadted");
				return;
			}
			if (gz_002.zdxxJson.prm_aka130 == null || gz_002.zdxxJson.prm_aka130 === '' || gz_002.zdxxJson.prm_aka130 === undefined) {
				malert("请选择支付类型", "top", "defeadted");
				return;
			}
			if(gz_002.grxxJson.prm_ykc023 == '1'){
				malert('病人再院，无法办理医保入院，请办理自费入院','right','defeadted')
				return;
			}
			//个人信息
			gz_002.gzyhybContent = gz_002.grxxJson;

			// 给页面输入框赋值
			contextInfo.json.brxm = gz_002.grxxJson.prm_aac003;
			contextInfo.json.brxb = gz_002.grxxJson.prm_aac004 == '男' ? '1' : '2';
			contextInfo.json.sfzjhm = gz_002.grxxJson.prm_aac002;
			contextInfo.updatedData = "1";
			contextInfo.setAge();
			if(contextInfo.json.jzdmc && contextInfo.json.jzdmc.indexOf(gz_002.grxxJson.prm_aab004) == -1){
				contextInfo.json.jzdmc = contextInfo.json.jzdmc + gz_002.grxxJson.prm_aab004;
			}else {
				contextInfo.json.jzdmc =  gz_002.grxxJson.prm_aab004;
			}



			//门诊诊断信息
			gz_002.gzyhybContent.jbbm = this.zdxxJson.jbbm;
			//支付类别
			gz_002.gzyhybContent.prm_aka130 = this.zdxxJson.prm_aka130;
			//备注
			gz_002.gzyhybContent.bzsm = this.zdxxJson.bzsm;
			//个人编号,用于结算各种判断
			gz_002.gzyhybContent.grbh = gz_002.grxxJson.prm_aac001;

			contextInfo.bxShow = false;
			conBtu.yrcgbz = true;

			malert("引入成功！");
			//根据个人编号查询是否已经进行过登记，如果是 则 使用其中的就诊编号（用于同一个人二次结算）
			/*var str_param = {
				id: gz_002.brxxContent.ghxh,
				prmAac001: gz_002.grxxJson.prm_aac001
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=queryMzdjxxByGrbh&parm="
				+ JSON.stringify(str_param),
				function (json) {
					if (json.a == 0) {
						if (json.d) {
							gz_002.gzyhybContent.prm_akc190 = json.d.prmAkc190;//就诊编号赋值
						}
						contextInfo.bxShow = false;
						conBtu.yrcgbz = true;
						malert("引入成功！");
					} else {
						malert("引入失败！");
					}
				});*/

		},

		//门诊预结算方法
		mzyjs: function () {
			gz_002.result = "0";
			//处理费用
			var yhybBrfyList = [];
			var fylist = [];
			var scbrfyList = [];
			var fyze = 0.00;
			for (var i = 0; i < contextInfo.brfyList.length; i++) {
				var fyparam = {};
				fyparam.mxfyxmbm = contextInfo.brfyList[i].mxfybm;
				fyparam.yzlx = '1';
				fyparam.yzhm = contextInfo.brfyList[i].yzhm;
				fyparam.fysl = contextInfo.brfyList[i].fysl;
				scbrfyList.push(fyparam);
				fyze += contextInfo.brfyList[i].fyje;
			}
			var param = {
				fylist: scbrfyList
			};
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=queryMzfy&parm="
				+ JSON.stringify(param),
				function (json) {
					if (json.a == '0') {
						yhybBrfyList = eval('(' + json.d + ')');
						for (var i = 0; i < yhybBrfyList.length; i++) {
							fylist.push(JSON.parse(yhybBrfyList[i]));
						}
					} else {
						malert(json.c);
						return false;
					}
				});
			if (fylist == null || fylist == undefined || fylist == "" || fylist.length <= 0) {
				malert("没有可结算费用！");
				return false;
			}

			var rowlist = [];
			for (var i = 0; i < fylist.length; i++) {
				var taglist = [];
				var obj1 = {
					tagName: 'yka105',
					children: [(new Date()).getTime() + i]
				};
				taglist.push(obj1);
				var obj2 = {
					tagName: 'ykd125',
					children: [fylist[i].ykd125]
				};
				taglist.push(obj2);
				var obj3 = {
					tagName: 'ykd126',
					children: [fylist[i].ykd126]
				};
				taglist.push(obj3);
				var obj4 = {
					tagName: 'yka002',
					children: [fylist[i].yka002]
				};
				taglist.push(obj4);
				var obj5 = {
					tagName: 'yka003',
					children: [fylist[i].yka003]
				};
				taglist.push(obj5);
				var obj6 = {
					tagName: 'akc226',
					children: [fylist[i].akc226]
				};
				taglist.push(obj6);
				var obj7 = {
					tagName: 'akc225',
					children: [fylist[i].akc225]
				};
				taglist.push(obj7);
				var obj8 = {
					tagName: 'yka315',
					children: [fylist[i].yka315]
				};
				taglist.push(obj8);
				var obj9 = {
					tagName: 'yka097',
					children: ['']
				};
				taglist.push(obj9);
				var obj10 = {
					tagName: 'yka098',
					children: ['']
				};
				taglist.push(obj10);
				var obj11 = {
					tagName: 'ykd102',
					children: ['']
				};
				taglist.push(obj11);
				var obj12 = {
					tagName: 'yka099',
					children: ['']
				};
				taglist.push(obj12);
				var obj13 = {
					tagName: 'yka100',
					children: ['']
				};
				taglist.push(obj13);
				var obj14 = {
					tagName: 'yka101',
					children: ['']
				};
				taglist.push(obj14);
				var obj15 = {
					tagName: 'ykd106',
					children: ['']
				};
				taglist.push(obj15);
				var obj16 = {
					tagName: 'yka102',
					children: ['']
				};
				taglist.push(obj16);
				var obj17 = {
					tagName: 'yke123',
					children: [gz_002.fDate(new Date(), 'date')]
				};
				taglist.push(obj17);
				var obj18 = {
					tagName: 'ykc141',
					children: [userId]
				};
				taglist.push(obj18);
				var obj19 = {
					tagName: 'aae036',
					children: [gz_002.fDate(new Date(), 'date')]
				};
				taglist.push(obj19);
				var obj20 = {
					tagName: 'aae013',
					children: ['']
				};
				taglist.push(obj20);
				var obj21 = {
					tagName: 'yke201',
					children: ['']
				};
				taglist.push(obj21);
				var obj22 = {
					tagName: 'yka295',
					children: ['']
				};
				taglist.push(obj22);
				var obj23 = {
					tagName: 'aka074',
					children: ['']
				};
				taglist.push(obj23);
				var obj24 = {
					tagName: 'aka070',
					children: ['']
				};
				taglist.push(obj24);
				var obj25 = {
					tagName: 'yae374',
					children: ['']
				};
				taglist.push(obj25);
				var obj26 = {
					tagName: 'yke009',
					children: ['']
				};
				taglist.push(obj26);
				var obj27 = {
					tagName: 'yke186',
					children: ['']
				};
				taglist.push(obj27);
				var row = {
					tagName: 'row',
					children: taglist
				}
				rowlist.push(row);
			}
			fyze = fyze.toFixed(2);
			var obj = {
				tagName: 'dataset',
				children: rowlist
			};
			doc = new CreatXmlDoc(obj);
			SetupSerial = (new XMLSerializer()).serializeToString(doc.render());
			var reg = new RegExp(' xmlns="http://www.w3.org/1999/xhtml"', "g");
			SetupSerial = SetupSerial.replace(reg, "");
			var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var prm_akc190 = "<prm_akc190>" + gz_002.gzyhybContent.prm_akc190 + "</prm_akc190>";
			var prm_aac001 = "<prm_aac001>" + gz_002.gzyhybContent.prm_aac001 + "</prm_aac001>";
			var prm_ykc173 = "<prm_ykc173></prm_ykc173>";
			var prm_hisfyze = "<prm_hisfyze>" + fyze + "</prm_hisfyze>";
			var prm_aka130 = "<prm_aka130>" + gz_002.gzyhybContent.prm_aka130 + "</prm_aka130>";
			var prm_yka110 = "<prm_yka110></prm_yka110>";
			var prm_aae013 = "<prm_aae013></prm_aae013>";
			var prm_aae011 = "<prm_aae011>" + userId + "</prm_aae011>";
			var prm_ykc141 = "<prm_ykc141>" + fylist[0].ykc141 + "</prm_ykc141>";
			var prm_ykb065 = "<prm_ykb065>" + gz_002.gzyhybContent.prm_ykb065 + "</prm_ykb065>";
			jysr = jysr + prm_akc190 + prm_aac001 + prm_ykc173 + prm_hisfyze + prm_aka130 + prm_yka110 + prm_aae013 +
				prm_aae011 + prm_ykc141 + prm_ykb065 + SetupSerial + "</input>";
			//去掉所有不合法字符
			jysr = jysr.replace(/undefined/g, "");
			jysr = jysr.replace(/NaN/g, "");
			jysr = jysr.replace(/null/g, "");
			//调用结算方法
			//同步操作
			$.ajaxSettings.async = false;
			$.post("http://localhost:9089/call", {
				'jybh': '48',
				'jysr_xml': jysr
			}, function (json) {
				if (json.aint_appcode > 0) {
					gz_002.yjsContentGzyhyb = JSON.parse(json.astr_jysc_xml);
					gz_002.jylsh = json.astr_jylsh;
					gz_002.jyyzm = json.astr_jjyyzm;
				} else {
					malert(json.astr_appmasg);
					gz_002.result = "1";
				}
			});
			//结算成功后，进行HIS门诊登记
			//这里转换一次，将字段下划线去掉，并修改为驼峰命名
			gz_002.ybuuid = new Date().getTime();
			var enterContent = {
				id: gz_002.ybuuid,//取挂号序号
				prmAac001: gz_002.grxxJson.prm_aac001,//个人编号 NOT NULL VARCHAR2(15)
				prmAkc021: gz_002.grxxJson.prm_akc021,//医疗人员类别 NOT NULL VARCHAR2(6) 见代码表
				prmYkc120: gz_002.grxxJson.prm_ykc120,//公务员级别 NOT NULL VARCHAR2(6) 见代码表
				prmYab139: gz_002.grxxJson.prm_yab139,//参保所属分中心 NOT NULL VARCHAR2(6) 见代码表
				prmYkb065: gz_002.grxxJson.prm_ykb065,//执行社会保险办法 NOT NULL VARCHAR2(6) 见代码表
				prmYkc150: gz_002.grxxJson.prm_ykc150,//异地安置标志 NOT NULL VARCHAR2(6) 见代码表
				prmAka130: gz_002.zdxxJson.prm_aka130,//支付类型
				prmAac003: gz_002.grxxJson.prm_aac003,//姓名 NULL VARCHAR2(20)
				prmAac004: gz_002.grxxJson.prm_aac004,//性别 NULL VARCHAR2(6) 见代码表
				prmAac002: gz_002.grxxJson.prm_aac002,//公民身份号码 NULL VARCHAR2(18)
				prmAac006: gz_002.grxxJson.prm_aac006,//出生日期 NULL DATETIME
				prmAkc023: gz_002.grxxJson.prm_akc023,//实足年龄 NULL NUMBER(3,0)
				prmAab001: gz_002.grxxJson.prm_aab001,//单位编号 NOT NULL VARCHAR2(15)
				prmAab004: gz_002.grxxJson.prm_aab004,//单位名称 NULL VARCHAR2(50)
				prmAac031: gz_002.grxxJson.prm_aac031,//个人参保状态 NOT NULL VARCHAR2(6) 见代码表
				prmAkc087: gz_002.grxxJson.prm_akc087,//个人账户余额 NOT NULL NUMBER(14,2)
				prmYab003: gz_002.grxxJson.prm_yab003,//分中心编号 NOT NULL VARCHAR2(6) 见代码表
				prmYkc280: gz_002.grxxJson.prm_ykc280,//居民医疗人员类别 NULL VARCHAR2(6) 见代码表
				prmYkc281: gz_002.grxxJson.prm_ykc281,//居民医疗人员身份 NULL VARCHAR2(6) 见代码表
				prmYkc023: gz_002.grxxJson.prm_ykc023,//住院状态 NULL VARCHAR2(6) 当住院的时间内，不允许门诊刷卡时，HIS需要判断该字段。1为在院，2 为未在院
                prmYae921 : gz_002.grxxJson.prm_yae921,//二维码
                prmSymbol : gz_002.grxxJson.prm_symbol,//特殊病标识 限遵义统筹区：1 为已申报慢特病，0 为 未申报慢特病
                prmQrcodetype :gz_002.grxxJson.prm_qrcodetype,//二维码类型 02-电子社保卡（医保付款码）；03-医保
                prmYkc010 : typeof gz_002.grxxJson.prm_ykc010=='string'?gz_002.grxxJson.prm_ykc010:'',//特殊疾病登记信息
                prmYkc296 : typeof gz_002.grxxJson.prm_ykc296=='string'?gz_002.grxxJson.prm_ykc296:'',//居民人员类别
			};
			//同步操作
			$.ajaxSettings.async = false;
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=mzdj&parm="
				+ JSON.stringify(enterContent),
				function (json) {
					if (json.a != '0') {
						gz_002.result = "1";
					} else {
						gz_002.mzjs();
					}
				});
			return gz_002.result;
		},

		//取消
		gz002_quxiao: function () {
			var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
			var prm_akc190 = "<prm_akc190>" + gz_002.yjsContentGzyhyb.prm_akc190 + "</prm_akc190>";//就诊编号
			var prm_yab003 = "<prm_yab003>" + gz_002.grxxJson.prm_yab003 + "</prm_yab003>";//分中心编号
			var prm_aka130 = "<prm_aka130>" + gz_002.zdxxJson.prm_aka130 + "</prm_aka130>";//支付类型
			var prm_yka103 = "<prm_yka103>" + gz_002.yjsContentGzyhyb.prm_yka103 + "</prm_yka103>";//结算编号
			var prm_aae011 = "<prm_aae011>" + gz_002.userInfo.czybm + "</prm_aae011>";//操作员编码
			var prm_ykc141 = "<prm_ykc141>" + gz_002.userInfo.czyxm + "</prm_ykc141>";//操作员姓名
			var prm_aae036 = "<prm_aae036>" + gz_002.fDate(new Date(), 'datetime') + "</prm_aae036>";//经办时间
			var prm_aae013 = "<prm_aae013>用户取消</prm_aae013>";//退费原因，
			var prm_ykb065 = "<prm_ykb065>" + gz_002.grxxJson.prm_ykb065 + "</prm_ykb065>";
			var prm_aac001 = "<prm_aac001>" + gz_002.yjsContentGzyhyb.prm_aac001 + "</prm_aac001>";
			jysr += prm_akc190 + prm_yab003 + prm_aka130 + prm_yka103 + prm_aae011
				+ prm_ykc141 + prm_aae036 + prm_aae013 + prm_ykb065 + prm_aac001 + "</input>";
			$.post("http://localhost:9089/call", {
				'jybh': "42",
				'jysr_xml': jysr,
			}, function (json) {
				//成功后调一次 confirm
				if (json.aint_appcode > 0) {
					$.post("http://localhost:9089/confirm", {
						'jylsh': json.astr_jylsh,
						'jyyzm': json.astr_jyyzm,
					}, function (data) {
					});
				} else {
					malert(data.astr_appmasg);
				}
			});
		},

		//门诊结算方法
		mzjs: function () {
			//进行医保结算确认
			$.post("http://localhost:9089/confirm", {
				'jylsh': gz_002.jylsh,
				'jyyzm': gz_002.jyyzm
			}, function (json) {

				//医保结算确认成功,his执行医保结算记录保存
				if (json.aint_appcode > 0) {
					var parm = {
						id: gz_002.ybuuid,
						jsjlid: gz_002.brxxContent.jsjlid,
						jylsh: gz_002.jylsh,
						jyyzm: gz_002.jyyzm,
						prmAkc190: typeof gz_002.yjsContentGzyhyb.prm_akc190 == "string" ? gz_002.yjsContentGzyhyb.prm_akc190 : 0,// 就诊编号 NOT NULL VARCHAR2(20)
						prmYab003: typeof gz_002.yjsContentGzyhyb.prm_yab003 == 'string' ? gz_002.yjsContentGzyhyb.prm_yab003 : 0,// 分中心编号 NOT NULL VARCHAR2(6) 见代码表
						prmYka103: typeof gz_002.yjsContentGzyhyb.prm_yka103 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka103 : 0,// 结算编号 NOT NULL VARCHAR2(20)
						prmAac001: typeof gz_002.yjsContentGzyhyb.prm_aac001 == 'string' ? gz_002.yjsContentGzyhyb.prm_aac001 : 0,// 个人编号 NOT NULL VARCHAR2(15)
						prmYka065: typeof gz_002.yjsContentGzyhyb.prm_yka065 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka065 : 0,// 个人帐户支付金额 NULL NUMBER(14,2)
						prmAae036: typeof gz_002.yjsContentGzyhyb.prm_aae036 == 'string' ? gz_002.yjsContentGzyhyb.prm_aae036 : 0,// 经办时间 NULL DATETIME
						prmYka055: typeof gz_002.yjsContentGzyhyb.prm_yka055 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka055 : 0,// 医疗费总额 NOT NULL NUMBER(14,2)
						prmYka056: typeof gz_002.yjsContentGzyhyb.prm_yka056 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka056 : 0,// 全自费金额 NOT NULL NUMBER(14,2)
						prmYka057: typeof gz_002.yjsContentGzyhyb.prm_yka057 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka057 : 0,// 挂钩自付金额 NOT NULL NUMBER(14,2)
						prmYka111: typeof gz_002.yjsContentGzyhyb.prm_yka111 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka111 : 0,// 符合范围金额 NOT NULL NUMBER(14,2)
						prmYka058: typeof gz_002.yjsContentGzyhyb.prm_yka058 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka058 : 0,// 进入起付线金额 NULL NUMBER(14,2)贵州省社会保险全省统一应用系统医保支付接口应用编程接口规范
						prmYka248: typeof gz_002.yjsContentGzyhyb.prm_yka248 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka248 : 0,// 基本医疗统筹支付金额 NULL NUMBER(14,2)
						prmYka062: typeof gz_002.yjsContentGzyhyb.prm_yka062 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka062 : 0,// 大额医疗支付金额 NULL NUMBER(14,2)
						prmYke030: typeof gz_002.yjsContentGzyhyb.prm_yke030 == 'string' ? gz_002.yjsContentGzyhyb.prm_yke030 : 0,// 公务员补助报销金额 NOT NULL NUMBER(14,2)
						prmAke032: typeof gz_002.yjsContentGzyhyb.prm_ake032 == 'string' ? gz_002.yjsContentGzyhyb.prm_ake032 : 0,// 伤残人员医疗保障基金 NOT NULL NUMBER(14,2)
						prmAke181: typeof gz_002.yjsContentGzyhyb.prm_ake181 == 'string' ? gz_002.yjsContentGzyhyb.prm_ake181 : 0,// 民政补助基金 NOT NULL NUMBER(14,2)
						prmAke173: typeof gz_002.yjsContentGzyhyb.prm_ake173 == 'string' ? gz_002.yjsContentGzyhyb.prm_ake173 : 0,// 其他基金支付 NOT NULL NUMBER(14,2)
						prmAkc087: typeof gz_002.yjsContentGzyhyb.prm_akc087 == 'string' ? gz_002.yjsContentGzyhyb.prm_akc087 : 0,// 本次个人账户支付后帐户余额 NOT NULL NUMBER(14,2)
						prmYkb037: typeof gz_002.yjsContentGzyhyb.prm_ykb037 == 'string' ? gz_002.yjsContentGzyhyb.prm_ykb037 : 0,// 清算分中心 NOT NULL VARCHAR2(6) 见代码表
						prmYka316: typeof gz_002.yjsContentGzyhyb.prm_yka316 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka316 : 0,// 清算类别 NULL VARCHAR2(6) 见代码表
						prmAkc021: typeof gz_002.yjsContentGzyhyb.prm_akc021 == 'string' ? gz_002.yjsContentGzyhyb.prm_akc021 : 0,// 医疗人员类别 NOT NULL VARCHAR2(6) 见代码表
						prmYkc120: typeof gz_002.yjsContentGzyhyb.prm_ykc120 == 'string' ? gz_002.yjsContentGzyhyb.prm_ykc120 : 0,// 公务员级别 NOT NULL VARCHAR2(6) 见代码表
						prmYab139: typeof gz_002.yjsContentGzyhyb.prm_yab139 == 'string' ? gz_002.yjsContentGzyhyb.prm_yab139 : 0,// 参保所属分中心 NOT NULL VARCHAR2(6) 见代码表
						prmAac003: typeof gz_002.yjsContentGzyhyb.prm_aac003 == 'string' ? gz_002.yjsContentGzyhyb.prm_aac003 : 0,// 姓名 NULL VARCHAR2(20)
						prmAac004: typeof gz_002.yjsContentGzyhyb.prm_aac004 == 'string' ? gz_002.yjsContentGzyhyb.prm_aac004 : 0,// 性别 NULL VARCHAR2(6) 见代码表
						prmAac002: typeof gz_002.yjsContentGzyhyb.prm_aac002 == 'string' ? gz_002.yjsContentGzyhyb.prm_aac002 : 0,// 公民身份号码 NULL VARCHAR2(18)
						prmAac006: typeof gz_002.yjsContentGzyhyb.prm_aac006 == 'string' ? gz_002.yjsContentGzyhyb.prm_aac006 : 0,// 出生日期 NULL DATETIME
						prmAkc023: typeof gz_002.yjsContentGzyhyb.prm_akc023 == 'string' ? gz_002.yjsContentGzyhyb.prm_akc023 : 0,// 实足年龄 NULL NUMBER(3,0)
						prmAab001: typeof gz_002.yjsContentGzyhyb.prm_aab001 == 'string' ? gz_002.yjsContentGzyhyb.prm_aab001 : 0,// 单位编号 NOT NULL VARCHAR2(15)
						prmAab004: typeof gz_002.yjsContentGzyhyb.prm_aab004 == 'string' ? gz_002.yjsContentGzyhyb.prm_aab004 : 0,// 单位名称 NULL VARCHAR2(50)
						prmAac031: typeof gz_002.yjsContentGzyhyb.prm_aac031 == 'string' ? gz_002.yjsContentGzyhyb.prm_aac031 : 0,// 个人参保状态 NOT NULL VARCHAR2(6) 见代码表
						prmYkc280: typeof gz_002.yjsContentGzyhyb.prm_ykc280 == 'string' ? gz_002.yjsContentGzyhyb.prm_ykc280 : 0,// 居民医疗人员类别 NULL VARCHAR2(6) 见代码表
						prmYkc281: typeof gz_002.yjsContentGzyhyb.prm_ykc281 == 'string' ? gz_002.yjsContentGzyhyb.prm_ykc281 : 0,// 居民医疗人员身份 NULL VARCHAR2(6) 见代码表
						prmYka054: typeof gz_002.yjsContentGzyhyb.prm_yka054 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka054 : 0,// 清算方式 NOT NULL VARCHAR2(6)
						prmYae366: typeof gz_002.yjsContentGzyhyb.prm_yae366 == 'string' ? gz_002.yjsContentGzyhyb.prm_yae366 : 0,// 清算期号 NOT NULL VARCHAR2(6)
						prmAkc090: typeof gz_002.yjsContentGzyhyb.prm_akc090 == 'string' ? gz_002.yjsContentGzyhyb.prm_akc090 : 0,// 本年真实住院次数 NULL NUMBER(6) V1.0.0.11 增加
						prmYka120: typeof gz_002.yjsContentGzyhyb.prm_yka120 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka120 : 0,// 基本统筹已累计金额 NULL NUMBER(14,2) V1.0.0.11 增加
						prmYka122: typeof gz_002.yjsContentGzyhyb.prm_yka122 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka122 : 0,// 大额统筹已累计金额 NULL NUMBER(14,2) V1.0.0.11 增加
						prmYka368: typeof gz_002.yjsContentGzyhyb.prm_yka368 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka368 : 0,// 公务员补助普通门诊起付年度累计(含慢性病) NULL NUMBER(14,2) V1.0.0.11 增加
						prmYke025: typeof gz_002.yjsContentGzyhyb.prm_yke025 == 'string' ? gz_002.yjsContentGzyhyb.prm_yke025 : 0,// 本年公务员门诊补助累计额(含慢性病) NULL NUMBER(14,2) V1.0.0.11 增加
						prmYka900: typeof gz_002.yjsContentGzyhyb.prm_yka900 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka900 : 0,// 规定病种起付线累计 NULL NUMBER(14,2) V1.0.0.11 增加
						prmAae001: typeof gz_002.yjsContentGzyhyb.prm_aae001 == 'string' ? gz_002.yjsContentGzyhyb.prm_aae001 : 0,// 年度 NULL NUMBER(4) V1.0.0.11 增加
						prmYka089: typeof gz_002.yjsContentGzyhyb.prm_yka089 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka089 : 0,// 单病种(结算)编码 NULL VARCHAR2(20) V1.0.0.20 增加
						prmYka027: typeof gz_002.yjsContentGzyhyb.prm_yka027 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka027 : 0,// 单病种(结算)病种名称 NULL VARCHAR2(200) V1.0.0.20 增加
						prmYka028: typeof gz_002.yjsContentGzyhyb.prm_yka028 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka028 : 0,// 单病种(结算)医疗机构自 NULL NUMBER(14,2) V1.0.0.20 增加贵州省社会保险全省统一应用系统医保支付接口应用编程接口规范费费用
						prmYka345: typeof gz_002.yjsContentGzyhyb.prm_yka345 == 'string' ? gz_002.yjsContentGzyhyb.prm_yka345 : 0,// 单病种(结算)包干标准 NULL NUMBER(14,2) V1.0.0.20 增加
                        rmAke183 : typeof gz_002.yjsContentGzyhyb.prm_ake183 == 'string'?gz_002.yjsContentGzyhyb.prm_ake183:0,// //优抚补偿金额
                        prmYka083 : typeof gz_002.yjsContentGzyhyb.prm_yka083 == 'string'?gz_002.yjsContentGzyhyb.prm_yka083:0,// //大病额外报销
                        prmYka084 : typeof gz_002.yjsContentGzyhyb.prm_yka084 == 'string'?gz_002.yjsContentGzyhyb.prm_yka084:0,// //公务员额外报销
                        prmYka085 : typeof gz_002.yjsContentGzyhyb.prm_yka085 == 'string'?gz_002.yjsContentGzyhyb.prm_yka085:0,// //公务员额外报销
                        prmYka086 : typeof gz_002.yjsContentGzyhyb.prm_yka086 == 'string'?gz_002.yjsContentGzyhyb.prm_yka086:0,// 进口自付金额
                        prmYka088 : typeof gz_002.yjsContentGzyhyb.prm_yka088 == 'string'?gz_002.yjsContentGzyhyb.prm_yka088:0,// 床位超标自付金额
                        prmYka090 : typeof gz_002.yjsContentGzyhyb.prm_yka090 == 'string'?gz_002.yjsContentGzyhyb.prm_yka090:0,// 限价材料超标自付金额
                        prmYka087 : typeof gz_002.yjsContentGzyhyb.prm_yka087 == 'string'?gz_002.yjsContentGzyhyb.prm_yka087:0,// 诊疗超标自付金额
                        prmYka501 : typeof gz_002.yjsContentGzyhyb.prm_yka501 == 'string'?gz_002.yjsContentGzyhyb.prm_yka501:0,// 门诊产前补助
                        prmYkd092 : typeof gz_002.yjsContentGzyhyb.prm_yka092 == 'string'?gz_002.yjsContentGzyhyb.prm_yka092:0,// 重大疾病标识(遵义、六盘水、安顺本地、省本级本地结算时 返回该字段)
                        prmYka194 : typeof gz_002.yjsContentGzyhyb.prm_yka194 == 'string'?gz_002.yjsContentGzyhyb.prm_yka194:0,// 超限额标志
                        prmYka119 : typeof gz_002.yjsContentGzyhyb.prm_yka119 == 'string'?gz_002.yjsContentGzyhyb.prm_yka119:0,// 已使用额度
					};
					parm = JSON.stringify(parm);
					parm = parm.replace(/undefined/g, "");
					parm = parm.replace(/NaN/g, "");
					parm = parm.replace(/null/g, "");
					$.getJSON(
						"/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=mzjs&parm=" + parm,
						function (jsjson) {
							if (jsjson.a == '0') {
								malert("医保结算信息保存成功！");
								contextInfo.outpId = JSON.parse(parm).prmYka103;
								contextInfo.bcfy = JSON.parse(parm).prmYka248;// 基本医疗统筹支付金额 NULL NUMBER(14,2)
							} else {
								//HIS结算成功后，调用医保确认
								$.post("http://localhost:9089/call", {
									'jylsh': gz_002.jylsh,
								}, function (json) {
									if (json.aint_appcode > 0) {
										malert("HIS医保结算信息保存失败!");
									} else {
										gz_002.result = '0';
										malert(json.astr_appmasg);
									}
								});
							}
						});
				} else {
					malert(json.astr_appmasg);
				}
			});
		},

		thqs: function () {
			var result = '0';
			//保存初始化
			$.post("http://localhost:9089/init", {}, function (json) {
				if (json.aint_appcode > 0) {
				} else {
					malert("医保控件初始化失败！请从新打开页面!");
					result = '1';
					return;
				}
			});
			//查询相关参数
			var str_parm = {
				id: contextInfo.json.ghxh
			};
			var histf_parm = {
				id: contextInfo.json.ghxh
			};
			$.getJSON(
				"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=queryForMztf&parm=" + JSON.stringify(str_parm),
				function (json) {
					if (json.a == '0') {
						var res = JSON.parse(json.d);
						histf_parm.prmAkc190 = res.prmAkc190;
						histf_parm.prmAac001 = res.prmAac001;
						var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><input><proxy>1</proxy><yinhai>1</yinhai>';
						var prm_akc190 = "<prm_akc190>" + res.prmAkc190 + "</prm_akc190>";
						var prm_yab003 = "<prm_yab003>" + res.prmYab003 + "</prm_yab003>";
						var prm_aka130 = "<prm_aka130>" + res.prmAka130 + "</prm_aka130>";
						var prm_yka103 = "<prm_yka103>" + res.prmYka103 + "</prm_yka103>";
						var prm_aae011 = "<prm_aae011>" + gz_002.userInfo.czybm + "</prm_aae011>";
						var prm_ykc141 = "<prm_ykc141>" + gz_002.userInfo.czyxm + "</prm_ykc141>";
						var prm_aae036 = "<prm_aae036>" + gz_002.fDate(new Date(), 'datetime') + "</prm_aae036>";
						var prm_aae013 = "<prm_aae013>用户退费</prm_aae013>";
						var prm_ykb065 = "<prm_ykb065>" + res.prmYkb065 + "</prm_ykb065>";
						var prm_aac001 = "<prm_aac001>" + res.prmAac001 + "</prm_aac001>";

						jysr = jysr + prm_akc190 + prm_yab003 + prm_aka130 + prm_yka103 + prm_aae011 +
							prm_ykc141 + prm_aae036 + prm_aae013 + prm_ykb065 + prm_aac001 + "</input>";

						//调用医保退费接口
						$.post("http://localhost:9089/call", {
							'jybh': '42',
							'jysr_xml': jysr
						}, function (json1) {
							if (json1.aint_appcode > 0) {
								//his退费
								$.getJSON(
									"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=mzjy&method=qxmzjs&parm=" + JSON.stringify(histf_parm),
									function (json2) {
										if (json2.a == '0') {
											malert("医保退费成功！");
										} else {
											malert("医保退费成功,本地退费失败！");
											gz_002.gzyhybtf = false;
										}
									});
							} else {
								tableInfo.gzyhybtf = false;
								malert(json1.astr_appmasg);
								result = '1';
								error;
								return;
							}
						});
					} else {
						malert(json.c);
						tableInfo.gzyhybtf = false;
						error;
						return;
					}
				});
		},

		wxhyjs: function () {
			gz_002.result = "0";
			gz_002.result = gz_002.mzyjs();
			if (gz_002.result == "1") {
				malert("抱歉，结算失败，点击确认重新预结算!");
				return;
			}
		},


		closeGz_002: function () {
			this.isShow = false;
			$("#hyjl").html("");
		},
		getUserInfo: function () {
			this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
				.then(function (json) {
					gz_002.userInfo = json.body.d;
				});
		},
		getbxlb: function () {
			var param = { bxjk: "002" };
			$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
				+ JSON.stringify(param), function (json) {
					if (json.a == 0) {
						if (json.d.list.length > 0) {
							gz_002.bxlbbm = json.d.list[0].bxlbbm;
							gz_002.bxurl = json.d.list[0].url;
						}
					} else {
						malert("保险类别查询失败!" + json.c, 'top', 'defeadted')
					}
				});
		},
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'jbContent', 'searchCon');
			if (event.code == 'Enter' || event.code == 13) {
				if (type == "text") {
					Vue.set(this.jbContent, 'jbmc', this.jbContent['yke121']);
					gz_002.zdxxJson.jbbm = this.jbContent.yke120;
					gz_002.zdxxJson.jbmc = this.jbContent.yke121;
					this.selSearch = 0;
					this.nextFocus(event);
				}
			}
		},

		searching: function (add, type, val) {
			this.jbContent['jbmc'] = val;
			if (!add) this.page.page = 1;
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			if (this.jbContent[type] == undefined || this.jbContent[type] == null) {
				this.page.parm = "";
			} else {
				this.page.parm = this.jbContent[type];
			}
			var str_param = { parm: this.page.parm, page: this.page.page, rows: this.page.rows, };
			$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_002.bxurl + "&bxlbbm=" + gz_002.bxlbbm + "&types=ICD10&method=query&parm="
				+ JSON.stringify(str_param),
				function (json) {
					if (json.a == 0) {
						var date = null;
						var res = eval('(' + json.d + ')');
						if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
							for (var i = 0; i < res.list.length; i++) {
								gz_002.searchCon.push(res.list[i]);
							}
						} else {
							gz_002.searchCon = res.list;
						}
						gz_002.page.total = res.total;
						gz_002.selSearch = 0;
						if (res.list.length > 0 && !add) {
							$(".selectGroup").hide();
							_searchEvent.show();
						}
					} else {
						malert("查询失败  " + json.c, 'top', 'defeadted');
					}
				});
		},
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;               // 设置当前页号
				this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
			} else {   // 否则就是选中事件,为json赋值
				this.jbContent = item;
				Vue.set(this.jbContent, 'jbmc', this.jbContent['yke121']);
				gz_002.zdxxJson.jbbm = this.jbContent.yke120;
				gz_002.zdxxJson.jbmc = this.jbContent.yke121;
				$(".selectGroup").hide();
			}
		},
	}
});
gz_002.getbxlb();

$(document).click(function () {
	if (this.className != 'selectGroup') {
		$(".selectGroup").hide();
	}
	$(".popInfo ul").hide();
});
