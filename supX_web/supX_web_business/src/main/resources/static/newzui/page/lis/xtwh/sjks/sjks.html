<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>送检科室</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../xmzb/xmzb.css"/>
    <link rel="stylesheet" href="sjks.css"/>
</head>
<style>
    .zui-table-view .fieldlist{
        display: none !important;
    }
</style>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="zui-btn btn-primary"  data-toggle="sideform" data-target="#brzcList"><i class=" icon-xz1 padd-r5"></i>新增科室</button>
            <button class="zui-btn btn-primary-b" @click="getData"><i class=" icon-sx "></i>刷新</button>
            <button class="zui-btn btn-primary-b" @click="saveData"><i class=" icon-baocun "></i>保存</button>
            <button class="zui-btn btn-primary-b " @click="deleteData"><i class="icon-sc-header "></i>删除</button>
            <button class="zui-btn btn-primary-b"><i class="icon-dysq "></i>打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">科室检索</label>
                    <div class="zui-input-inline zui-select-inline ">
                        <input type="text" v-model="searchAll" placeholder="请输入关键字" class="zui-input wh274"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="zui-table-view padd-l-10 padd-r-10" id="jyxm" >
    <div class="ksys-content" >
        <div class="xmzb-content-left sjks-content-left">
            <div class="content-left-top">
                <i style="width: 50px !important;">
                	<div class="zui-table-cell">
                		<input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                	</div>
                </i>
                <i>科室编码</i>
                <i>科室名称</i>
                <i>代码</i>
                <i>操作<em class="icon-bgzdgl"></em></i>
            </div>
            <ul class="content-left-list sjks-content-left-list">
                <li @dblclick="dbAdd($index)" v-for="(item, $index) in jsonList" :key="item.fybm">
                	<i style="width: 50px !important;">
                		<div class="zui-table-cell">
                			<input-checkbox @result="reCheckBox" :list="'jsonList'"
	                                        :type="'some'" :which="$index"
	                                        :val="isChecked[$index]">
	                        </input-checkbox>
                		</div>
                	</i>
                	<i v-text="item.ksbm">001</i>
                	<i v-text="item.ksmc">淋巴细胞比率</i>
                	<i v-text="item.ksdm">FGHJ</i>
                	<i class="icon-sc" @click="delNow($event)"></i>
                </li>
            </ul>

        </div>
        <div class="xmzb-content-right xmzb-content-left sjks-content-right">
            <div class="content-right-top">
                <i style="width: 50px !important;">
                	<div class="zui-table-cell">
                		<input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                	</div>
                </i>
                <i>科室编码</i>
                <i>科室名称</i>
                <i>代码</i>
                <i>来源</i>
                <i>血库</i>
                <i>状态</i>
                <i>操作<em class="icon-bgzdgl"></em></i>
            </div>
            <ul class="content-right-list">
                <li v-for="(item, $index) in jsonList" :key="item.fybm">
                	<i style="width: 50px !important;">
                		<div class="zui-table-cell">
                			 <input-checkbox @result="reCheckBox" :list="'jsonList'"
	                                        :type="'some'" :which="$index"
	                                        :val="isChecked[$index]">
	                         </input-checkbox>
                		</div>
                	</i>
	                	<i v-text="item.ksbm"></i>
	                	<i v-text="item.ksmc"></i>
	                	<i v-text="item.ksdm"></i>
	                	<i v-text="hosly[item.ly==null?'0': item.ly == '0' ? '0':'1']"></i>
                	<i>
                		<div class="zui-table-cell">
                			<input-checkbox  :checked="item.xkbz==1?true:false"/>
                		</div>
                	</i>
                	<i>
                        <div class="switch cell-s" >
                            <input disabled :id="'checked'+$index" type="checkbox" v-model="item.stop" true-value="0" false-value="1"  />
                            <label :for="'checked'+$index"></label>
                        </div>
            		</i>
            		<i class="icon-sc" @click="delNow($index)"></i>
            	</li>
            </ul>
        </div>
    </div>
    </div>
</div>
<div id="pop">
    <!--<transition name="pop-fade">-->
    <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
    <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
        <div class="layui-layer-content" >
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
        </div>
    </div>
    <!--</transition>-->
</div>

<!--侧边窗口-->
<div class="side-form ng-hide" style="width:320px;padding-top: 0;"  id="brzcList" role="form">
    <div class="tab-message">
        <a>新增送检科室</a>
        <a href="javascript:;" class="fr closex ti-close" style="color:rgba(255,255,255,.56) !important;" @click="AddClose"></a>
    </div>
    <div class="ksys-side">
        <span class="span0">
            <i>科室编码</i>
            <input type="text" class="zui-input border-r4" disabled placeholder="请输入科室编码"/>
        </span>
        <span class="span0">
            <i>科室名称</i>
            <input type="text" v-model="parm.ksmc"  @blur="setPYDM(parm.ksmc, 'parm', 'ksdm')" class="zui-input border-r4" placeholder="请输入科室名称"/>
        </span>
        <span class="span0">
            <i>科室代码</i>
            <input type="text" v-model="parm.ksdm" class="zui-input border-r4" placeholder="请输入科室代码"/>
        </span>
        <span class="span0" id="jyxm_icon">
            <i>状态</i>
            <div class="switch">
                  <input v-model="parm.stop" :checked="parm.stop==0?true:false" type="checkbox"/>
                <label></label>
            </div>
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">确定</button>
    </div>
</div>

<style>
    .side-form-bg{
        background: none;
        position: inherit;
    }
</style>

<script src="sjks.js"></script>
</body>
</html>
