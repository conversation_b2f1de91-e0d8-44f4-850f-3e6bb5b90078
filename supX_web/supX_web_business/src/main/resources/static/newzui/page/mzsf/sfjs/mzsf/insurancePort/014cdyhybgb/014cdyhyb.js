var cd_014 = new Vue({
    el: '.cd_014',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        skr_tran: {
            '本人': '本人',
            '父母': '父母',
            '子女': '子女',
            '配偶': '配偶',
            '同胞兄弟姐妹': '同胞兄弟姐妹',
            '外祖父母': '外祖父母',
        },
        isShow: false,
        bxlbbm: null,
        bxurl: null,
        birthday: null,
        text: null,
        jbContent: {},
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'yke120',
            '疾病名称': 'yke121',
            '副编码': 'yke223'
        },
        brzt_tran: {
            '1': '在院',
            '2': '未在院'
        },
        zdxxJson: {},
        grxxJson: {
            aka130: "0201",
        },
        cssg: false,
        userInfo: {},
        ifclick: true,
        cd_ykc117_tran: {
            '1': '公务员',
            '0': '非公务员 ',
        },
        cdydbz_tran: {
            '1': '本地',
            '2': '省内异地 ',
            '3': '省外异地'
        },
        cd_aka130_tran: {
            '0101': '药店购药',
            '0102': '城居药店支付',
            '0201': '普通门诊',
            '0202': '特殊疾病门诊',
            '0203': '城居普通门诊',
            '0204': '城居门诊特殊病',
            '0205': '城职门诊统筹',
            '0206': '城乡门诊统筹',
            '0207': '酪氨酸城职',
            '0208': '酪氨酸城乡',
            '0209': '大学生门诊',
            '0301': '普通住院',
            '0302': '家庭病床',
            '0304': '市外转诊住院(异地急救参照市外转诊)',
            '0305': '统筹区内转院',
            '0306': '外伤住院',
            '0307': '异地抢救住院(手工报销使用)',
            '0308': '本市非定点抢救住院(手工报销使用)',
            '0309': '城居普通住院',
            '0310': '城居外伤住院',
            '0311': '城居市外转诊',
            '0401': '机关事业单位生育报销',
            '0501': '工伤门诊',
            '0502': '工伤住院',
            '0504': '工伤康复治疗住院',
            '0602': '城居生育分娩住院',
            '0603': '城居产前检查',
            '0604': '城职生育住院',
            '13': '异地门特（就医地）',
        },
		insutype_tran:{
			'310':'职工基本医疗保险',
			'320':'公务员医疗补助',
			'330':'大额医疗费用补助',
			'340':'离休人员医疗保障',
			'390':'城乡居民基本医疗保险',
			'392':'城乡居民大病医疗保险',
			'510':'生育保险',
		},
        gsdataset_tran: [],
        fylist: [],
        datasetyka026_tran: [],
        gsdataset_show: false,
        datasetyka026_show: false,
        lxxx_show: false,
        ryfbmc: '',
        isdzps: false,//是否电子医保凭证
        pushResult: false,//单子医保凭证推送结果
        yinHaiRequest: {//用于声明银海请求对象，与字段对应关系
            //门诊交易（11）
            
        },
		dtxx:false,
		idetinfo:[],
		isZxChecked:[],
		isZxCheckAll:false,
		syxx:null,
		sfydList:{
			'0':'否',
			'1':'是'
		},
		sfyd:'0',
    },
    created: function () {
        this.init();
    },
    mounted: function () {
        this.isShow = true;
        this.getbxlb();
        this.getUserInfo();
    },
    methods: {
        init: function () {
            window.insuranceGbUtils.init();
        },
        closeGz_002: function () {
            this.isShow = false;
            $("#hyjl").html("");
        },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    cd_014.userInfo = json.body.d;
                });
        },
        getbxlb: function () {
            var that = this;
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    that.bxlbbm = json.d.list[0].bxlbbm;
                    that.bxurl = json.d.list[0].url;
					// that.bxurl='http://172.20.103.68:10015/interface/cdyhyb/post'
                } else {
                    malert("保险类别查询失败!" + json.c, 'right', 'defeadted')
                }
            });
        },
        commonResultChange: function (val) {
            var type = val[2][1];
            switch (type) {
                case "yke109":
                    Vue.set(this.grxxJson, type, val[0]);
                    Vue.set(this.grxxJson, "alc022", val[4]);
                    Vue.set(this.grxxJson, "aka130", cd_014.listGetName(cd_014.gsdataset_tran, val[0], 'yke109', 'aka130'));
                    break;
                case "bkc014":
                    Vue.set(this.grxxJson, type, val[0]);
                    Vue.set(this.grxxJson, "bkc117", val[4]);
                    break;
            }
        },
        qdFun: function () {
            //签到
            window.insuranceGbUtils.qd();
        },
        loadqd: function () {
            window.insuranceGbUtils.qd();
        },
        //读卡
        load: function () {

            /*if(!cd_014.ifclick){
                malert("请勿重复点击！","right","defeadted");
                return;
            }*/
            cd_014.ifclick = false;
			window.insuranceGbUtils.qd();
			window.insuranceGbUtils.init();
            if (window.insuranceGbUtils.initStatus) {
				let ret1 =  window.insuranceGbUtils.call('1101','',cd_014.sfyd,cd_014.userInfo,'01301');
				if(ret1){
					let ryxx =  window.insuranceGbUtils.ryxx(cd_014.sfyd);
					this.syxx = ryxx;
					this.insuinfo = ryxx.insuinfo
					if(ryxx.insuinfo.length>=1){
						this.dtxx = true;
					}else{
						this.dtxx = false;
					}
					
				}
            } else {
                cd_014.ifclick = true;
                malert("医保控件未初始化,请重新打开页面！", 'right', 'defeadted');
            }
        },
		//确定使用人员信息
		saveZxXx:function(){
					let param = [];
					for (var i = 0; i < this.isZxChecked.length; i++) {
							if(this.isZxChecked[i]){
								param.push(this.insuinfo[i])
							}
						}
					console.log(param)
					if(param){
						cd_014.grxxJson.aac001 = cd_014.syxx.baseinfo.psn_no
						cd_014.grxxJson.aac003 = cd_014.syxx.baseinfo.psn_name
						cd_014.grxxJson.aac004 = cd_014.syxx.baseinfo.gend
						cd_014.grxxJson.akc023 = cd_014.syxx.baseinfo.age
						cd_014.grxxJson.aac002 = cd_014.syxx.baseinfo.certno
						
						cd_014.grxxJson.aac006 = cd_014.syxx.baseinfo.brdy
						cd_014.grxxJson.ykc303 = param[0].insutype
						console.log(cd_014.insutype_tran[param[0].insutype]);
						cd_014.grxxJson.ykc194 = param[0].balc
						cd_014.grxxJson.ykc177 = param[0].cvlserv_flag
						cd_014.grxxJson.psn_type = param[0].psn_type
						cd_014.grxxJson.psn_insu_stas = param[0].psn_insu_stas
						cd_014.grxxJson.psn_insu_date = param[0].psn_insu_date
						cd_014.grxxJson.paus_insu_date = param[0].paus_insu_date
						cd_014.grxxJson.insuplc_admdvs = param[0].insuplc_admdvs
						let ryxx = JSON.parse(sessionStorage.getItem('hzybxx'));
						ryxx.insuplc_admdvs = param[0].insuplc_admdvs;
						sessionStorage.setItem('hzybxx',JSON.stringify(ryxx));
						
						cd_014.grxxJson.emp_name = param[0].emp_name
						this.dtxx = false;
						if(cd_014.syxx.baseinfo.psn_name && rightVue.fzContent && rightVue.fzContent.brxm && cd_014.syxx.baseinfo.psn_name != rightVue.fzContent.brxm){
							malert("非本人医保卡请确认", "right", "defeadted");
						}
					}
					
				},
				
		reZxCheckChange: function (val) {
			this.isZxChecked = []
					        for (var i = 0; i < this.insuinfo.length; i++) {
		            if (!val[2]) {
		                Vue.set(this.isZxChecked, val[1], val[2]);
		            } else {
		                Vue.set(this.isZxChecked, val[1], val[2]);
		            }
		        }
		    
		},
        //引入
        enter: function () {
            if (Object.keys(cd_014.grxxJson).length === 0) {
                malert("请先读卡", "right", "defeadted");
                return;
            }
            if (!cd_014.grxxJson.aka130) {
                malert("请选择支付类型", "right", "defeadted");
                return;
            }
            //个人信息
            rightVue.gzyhybContent = cd_014.grxxJson;
            //门诊诊断信息
            rightVue.gzyhybContent.jbbm = this.zdxxJson.jbbm;
            //支付类别
            rightVue.gzyhybContent.aka130 = this.grxxJson.aka130;
            //备注
            rightVue.gzyhybContent.bzsm = this.zdxxJson.bzsm;
            //个人编号,用于结算各种判断
            rightVue.gzyhybContent.grbh = cd_014.grxxJson.aac001;

            popTable.isShow = false;
            malert("引入成功！", "right", "success");
        },
        mzyjs014V3: function () {
                        var fylist = [];
            var brfyList = [];
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                if (rightVue.brfyjsonList[i].fydj > 0) {
                    var fyparam = {};
                    fyparam.fyid = new Date().getTime() + i;
                    fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                    fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                    fyparam.fysl = String(rightVue.brfyjsonList[i].fysl);
                    fyparam.yka097 = rightVue.brfyjsonList[i].mzks;
                    fyparam.yka098 = rightVue.brfyjsonList[i].mzksmc;
                    fyparam.ykf008 = rightVue.brfyjsonList[i].mzys;
                    fyparam.yka099 = rightVue.brfyjsonList[i].mzysxm;
                    fyparam.yke123 = rightVue.brfyjsonList[i].sfsj || new Date();
                    fyparam.akc225 = String(rightVue.brfyjsonList[i].fydj);
                    if (typeof (rightVue.brfyjsonList[i].identity) != "undefined") {
                        fyparam.identity = rightVue.brfyjsonList[i].identity;
                    }
                    brfyList.push(fyparam);
                }
            }
            var requestParameters = '{"list":' + JSON.stringify(brfyList) + '}';
            this.postAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=queryMzfy", requestParameters, function (json) {
                if (json.a == '0') {
                    fylist = eval('(' + json.d + ')');
                } else {
                    malert(json.c, 'top', 'defeadted');
                    return false;
                }
            });


            if (!fylist || fylist.length <= 0) {
                malert("没有可结算费用！", 'top', 'defeadted');
                return false;
            }
            this.ryfbmc = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['ryfbbm'], 'fbbm', 'fbmc');
            //非电子凭证需要先签到，在走医保11 业务
            // if (cd_014.isdzps == false) {
            //     if (!this.qdFun()) {
            //         malert("签到失败！", 'top', 'defeadted');
            //         return false;
            //     }
            // }
            //去除数量为0 的
            fylist = fylist.filter(function (x) {
                return x.akc226 != 0;
            })
            //查找有医嘱号的
            let yke134List = [];
            fylist.forEach(x => {
                if (typeof (x.yke134) != "undefined") {
                    yke134List.push(x);
                }
            });

            //查找有医嘱号的
            let identityList = [];
            fylist.forEach(x => {
                if (typeof (x.identity) != "undefined") {
                    identityList.push(x);
                }
            });
            //通过医嘱号分组
            let groupYke134 = this.toGroupBy(yke134List, function (item) {
                return [item.yke134];
            });

            //通过identity分组
            let groupIdentity = this.toGroupBy(identityList, function (item) {
                return [item.identity];
            });
            let groupList = groupYke134.concat(groupIdentity);
			return groupList;
            // return this.feePushToYinhai(groupList);
        },
        emptyReplace: function (para) {
            if (para == null || typeof (para) == "undefined") {
                return '';
            }
            return para;
        },
        toGroupBy: function (array, fn) {
                        const groups = {};
            array.forEach(function (item) {
                const group = JSON.stringify(fn(item));
                //这里利用对象的key值唯一性的，创建数组
                groups[group] = groups[group] || [];
                groups[group].push(item);
            });


            //最后再利用map循环处理分组出来
            return Object.keys(groups).map(function (group) {
                return groups[group];
            });
        },
		
        sleep: function (delay) {
                        var start = (new Date()).getTime();
            let end = (new Date()).getTime();
            while ((end - start) < delay) {
                end = (new Date()).getTime()
            }
            return true;
        },
        decodeUnicode: function (str) {
            //先把十六进制unicode编码/u替换为%u
            str = str.replace(/\\u/gi, '%u');
            //再把页面中反斜杠替换为空
            str = str.replace(/\\/gi, '');
            return unescape(str);
        }
    }
});


$(document).click(function () {
    if (this.className != 'selectGroup') {
        $(".selectGroup").hide();
    }
    $(".popInfo ul").hide();
});
