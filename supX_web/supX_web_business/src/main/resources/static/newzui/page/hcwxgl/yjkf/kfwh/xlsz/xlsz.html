<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>限量设置</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="xlsz.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary flex-start" @click="save"><i class="icon-baocunb paddr-r5"></i>保存</button>
            <button class="tong-btn btn-parmary-b  icon-sx paddr-r5 icon-font14" @click="refresh">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">库房</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="kfList" :index="'kfmc'" :index_val="'kfbm'" :val="popContent.kfbm"
                                      :name="'popContent.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label   margin-f-l10">检索</label>
                    <div class="zui-input-inline margin-f-l35">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="search"/>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                 :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>上量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>下量</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-heights" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :tabindex="$index" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" >
                    <td  class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>

                    <td><div class="zui-table-cell cell-xl text-over-2" v-text="item.YPMC"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.YPGG"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.YPDW"></div></td>
                    <td><div class="zui-table-cell cell-s"><input class="zui-input border-r4" style="height:28px;" @change="listSave($index)"  type="number" v-model="item.ZDKC" @keydown="nextFocus($event)"></div></td>
                    <td><div class="zui-table-cell cell-s"><input class="zui-input border-r4" style="height:28px;" @change="listSave($index)" type="number" v-model="item.ZGKC" @keydown="nextFocus($event)"></div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="xlsz.js"></script>

</body>

</html>
