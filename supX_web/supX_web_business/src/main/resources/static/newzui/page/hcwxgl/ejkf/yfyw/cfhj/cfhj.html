<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <title>处方划价</title>
    <link href="cfhj.css" rel="stylesheet" type="text/css"/>
</head>
<body class="skin-default flex-container flex-dir-c padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<!--<div id="test" style="height: 2px">-->
    <!--<transition name="progress">-->
        <!--<div class="progressBar" v-show="isDone" :class="{isDoneStyle: isDone}"></div>-->
    <!--</transition>-->
<!--</div>-->
<!-- 主体 -->
<!-- 功能按钮 -->
<div id="ToolBar" v-cloak class="toolMenu padd-b-10 flex-container flex-align-c">
    <div class="padd-r-10">
        <!--<select v-model="yfContent.yfbm" @change="Wf_YfChange($event,'yfbm',$event.target.value)">-->
            <!--<option v-for="v in jsonList" :value="v.yfbm" :ksbm="v.ksbm" :cflx="v.cflx"-->
                    <!--v-text="v.yfmc"></option>-->
        <!--</select>-->
        <select-input @change-data="Wf_YfChange" :not_empty="false" :child="jsonList"
                      :index="'yfmc'" :index_mc="'cflx'" :index_val="'yfbm'" :val="yfContent.yfbm"
                      :name="'yfContent.yfbm'" :search="true">
        </select-input>
    </div>
    <div class="flex-container flex-align-c">
        <button class="tong-btn btn-parmary " @click="getData">
            <span class="fa fa-refresh"></span>刷新
        </button>
        <button class="tong-btn btn-parmary fa fa-plus" @click="newData">新增处方
        </button>
        <button class="tong-btn btn-parmary fa fa-plus" @click="addData">添加一行
        </button>
        <button class="tong-btn btn-parmary fa fa-edit" @click="saveData">保存
        </button>
        <button class="tong-btn btn-parmary fa fa-edit" @click="delData">作废
        </button>
        <button class="tong-btn btn-parmary " @click="getOpen">处方查询
        </button>
        <span class="color-wtg ft-14">{{yfContent.hzlx == '0'?'门诊划价':'住院划价'}} [F8切换]
				{{yfContent.kfbz == '1' ? '已收费':'未收费'}}</span>
        <span class="padd-l-10 color-wtg ft-14">上一处方号：{{cfhObj.cfh}} 上一处方号金额：{{cfhObj.cfje}}</span>
    </div>
</div>
<div class="flex-container skin-default" style="height: 100%" >
    <!-- 编辑区 -->
    <div class="editArea flex-container flex-dir-c">
        <div id="cfhjEdit" v-cloak>
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">患者检索区</div>
                </div>
                <div class="tab-card-body padd-t-10">
                        <div class="info flex-container  flex-wrap-w">
                            <div class="infoIpt flex-container flex-align-c padd-b-10 padd-r-10">
                                <p class="padd-r-5">检&emsp;&emsp;索：</p>
                                <div>
                                    <input class="zui-input wh120" id="bah" data-notEmpty="true" v-model="CfContent.bah"
                                           :readonly="readonly"
                                           @keydown="changeDown($event,'bah','CfContent','hzsearchCon')"
                                           @input="change(false,$event,'bah',$event.target.value)"/>
                                    <hzsearch-table :message="hzsearchCon" :selected="selSearch"
                                                    :total="hztotal" :them="hzthem"
                                                    :them_tran="them_tran" @click-one="checkedOneOut"
                                                    @click-two="dblclickbah">
                                    </hzsearch-table>
                                </div>
                            </div>
                            <div class="infoIpt flex-container flex-align-c padd-b-10 padd-r-10">
                                <p class="padd-r-5">姓&emsp;&emsp;名：</p>
                                <div>
                                    <input class="zui-input wh120" data-notEmpty="true" v-model="CfContent.brxm"
                                           :readonly="readonly" @keydown="nextFocus($event)">
                                </div>
                            </div>
                            <div class="infoIpt flex-container flex-align-c padd-b-10 padd-r-10">
                                <p class="padd-r-5">性&emsp;&emsp;别：</p>
                                <div>
                                    <!--<select class="zui-input wh120" v-model="CfContent.brxb" :disabled="readonly">-->
                                        <!--<option v-for="(item, $index) in brxb_tran" :value="$index"-->
                                                <!--v-text="item"></option>-->
                                    <!--</select>-->
                                    <select-input class="wh120" @change-data="resultChange" :not_empty="true" :child="brxb_tran"
                                                  :index="CfContent.brxb" :val="CfContent.brxb" :search="true"
                                                  :name="'CfContent.brxb'">
                                    </select-input>
                                </div>
                            </div>
                            <div class="infoIpt flex-container flex-align-c padd-b-10 padd-r-10">
                                <p class="padd-r-5">年&emsp;&emsp;龄：</p>
                                <div class="age flex-container flex-align-c">
                                    <input class="zui-input wh59 " v-model="CfContent.brnl" :readonly="readonly" @keydown="nextFocus($event)">
                                    <select-input  @change-data="resultChange" :not_empty="true" :child="nldw_tran"
                                                  :index="CfContent.nldw" :val="CfContent.nldw" :search="true"
                                                  :name="'CfContent.nldw'">
                                    </select-input>
                                </div>
                            </div>
                            <div class="infoIpt flex-container flex-align-c padd-b-10 padd-r-10">
                                <p class="padd-r-5">处方类型：</p>
                                <div>
                                    <!--<select class="zui-input wh120" v-model="yfContent.cflx">-->
                                        <!--<option v-for="v in cflxList" :value="v.cflxbm" v-text="v.cflxmc"></option>-->
                                    <!--</select>-->

                                    <select-input class="wh120" @change-data="resultChangeData" :not_empty="false" :child="CflxJosn"
                                                  :index="'cflxmc'" :index_val="'cflxbm'" :val="yfContent.cflxbm"
                                                  :name="'yfContent.cflxbm'" :search="true">
                                    </select-input>
                                </div>
                            </div>
                            <div class="infoIpt flex-container flex-align-c padd-b-10 padd-r-10">
                                <p class="padd-r-5">处方医师：</p>
                                <div>
                                    <!--<select class="zui-input wh120" v-model="CfContent.cfys" :disabled="readonly">-->
                                        <!--<option v-for="allys in ysbmList" :value="allys.rybm"-->
                                                <!--v-text="allys.ryxm"></option>-->
                                    <!--</select>-->

                                    <select-input class="wh120" @change-data="resultChange" :disable="readonly" :not_empty="false" :child="ysbmList"
                                                  :index="'ryxm'" :index_mc="'cfysxm'" :index_val="'rybm'" :val="CfContent.cfys"
                                                  :name="'CfContent.cfys'" :search="true">
                                    </select-input>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">患者费别：</p>
                                <div>
                                    <!--<select class="zui-input wh120" v-model="CfContent.fbbm" :disabled="readonly">-->
                                        <!--<option v-for="allfb in brfbList" :value="allfb.fbbm"-->
                                                <!--v-text="allfb.fbmc"></option>-->
                                    <!--</select>-->

                                    <select-input class="wh120" @change-data="resultChange" :disable="readonly" :not_empty="false" :child="brfbList"
                                                  :index="'fbmc'" :index_val="'fbbm'" :val="CfContent.fbbm"
                                                  :name="'CfContent.fbbm'" :search="true">
                                    </select-input>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">处&nbsp;方&nbsp;号&ensp;：</p>
                                <div>
                                    <input class="zui-input wh240" v-model="CfContent.cfh" @keydown="changeDown($event)"
                                           disabled="disabled">
                                </div>
                            </div>
                        </div>
                </div>
            </div>

            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">药品检索区</div>
                </div>
                <div class="tab-card-body padd-t-10">
                    <div class="grid-box">
                        <div class=" flex-container flex-wrap-w">
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">药品名称：</p>
                                <div>
                                    <input class="wh120 zui-input" id="ypmc" data-notEmpty="true"
                                           v-model="PfContent.text" :readonly="readonly"
                                           @keydown="changeDown($event,'ypmc','PfContent','searchCon')"
                                           @input="change(false,$event,'ypmc',$event.target.value)">
                                    <search-table :message="searchCon" :selected="selSearch"
                                                  :page="dg" :them="them"
                                                  @click-one="checkedOneOut" @click-two="dblclickypmc">
                                    </search-table>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">药品规格：</p>
                                <div>
                                    <input class="wh120 zui-input" v-model="PfContent.ypgg"
                                           @keydown="changeDown($event)" disabled="disabled"></td>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">药品零价：</p>
                                <div>
                                    <input class="wh120 zui-input" type="number" v-model="PfContent.yplj"
                                           @keydown="changeDown($event)" disabled="disabled"></td>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">医保统筹：</p>
                                <div>
                                    <!--<select class="wh120 zui-input" v-model="PfContent.ybtclb" disabled="disabled">-->
                                        <!--<option v-for="alltclb in tclbList" :value="alltclb.tclbbm"-->
                                                <!--v-text="alltclb.tclbmc"></option>-->
                                    <!--</select>-->

                                    <select-input class="wh120" @change-data="resultChange" :disable="true" :not_empty="false" :child="tclbList"
                                                  :index="'tclbmc'" :index_val="'tclbbm'" :val="PfContent.tclbbm"
                                                  :name="'PfContent.tclbbm'" :search="true">
                                    </select-input>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">数&emsp;&emsp;量：</p>
                                <div>
                                    <input class="wh120 zui-input" id="sl" type="number" min=0 max=4
                                           data-notEmpty="true"
                                           v-model.number="PfContent.cfyl" :readonly="readonly"
                                           @keydown.13="addData($event)"/>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">单&emsp;&emsp;位：</p>
                                <div>
                                    <select class="wh120 zui-input" v-model="PfContent.yfdw" disabled="disabled">
                                        <option v-for="allypdw in ypdwList" :value="allypdw.jldwbm"
                                                v-text="allypdw.jldwmc"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">科室配药：</p>
                                <div>
                                    <!--<select class="wh120 zui-input" v-model="yfContent.pybz" :disabled="readonly">-->
                                        <!--<option v-for="(item, $index) in istrue_tran" :value="$index"-->
                                                <!--v-text="item"></option>-->
                                    <!--</select>-->
                                    <select-input class="wh120" @change-data="resultChange" :not_empty="true" :child="istrue_tran"
                                                  :index="yfContent.pybz" :val="yfContent.pybz" :search="true"
                                                  :name="'yfContent.pybz'">
                                    </select-input>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">配药科室：</p>
                                <div>
                                    <!--<select class="wh120 zui-input" v-model="CfContent.pyks" :disabled="readonly">-->
                                        <!--<option v-for="allks in ksbmList" :value="allks.ksbm"-->
                                                <!--v-text="allks.ksmc"></option>-->
                                    <!--</select>-->

                                    <select-input class="wh120" @change-data="resultChange" :disable="readonly" :not_empty="false" :child="ksbmList"
                                                  :index="'ksmc'" :index_val="'ksbm'" :val="CfContent.ksbm"
                                                  :name="'CfContent.ksbm'" :search="true">
                                    </select-input>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">袋&ensp;子&ensp;数：</p>
                                <div>
                                    <input class="wh120 zui-input" type="number" v-model="CfContent.dzs"
                                           @keydown="changeDown($event)" disabled="disabled">
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">中药副数：</p>
                                <div>
                                    <input class="wh120 zui-input" type="number" v-model.number="CfContent.zyfs"
                                           @keydown="changeDown($event)" >
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">药品库存：</p>
                                <div>
                                    <input class="wh120 zui-input" v-model="PfContent.sjkc" type="number"
                                           @keydown="changeDown(11)" disabled="disabled">
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">农合类别：</p>
                                <div>
                                    <select class="wh120 zui-input" v-model="PfContent.nbtclb" disabled="disabled">
                                        <option v-for="(item, $index) in istrue_tran" :value="$index"
                                                v-text="item"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">有效期至：</p>
                                <div>
                                    <input class="wh120 zui-input" v-model="fDate(PfContent.yxqz,'date')"
                                           @keydown="changeDown($event)" disabled="disabled">
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">余&emsp;&emsp;额：</p>
                                <div>
                                    <input class="wh120 zui-input" v-model="CfContent.zhye"
                                           @keydown="changeDown($event)" disabled="disabled">
                                </div>
                            </div>
                            <div class="infoIpt flex-container padd-r-10 padd-b-10 flex-align-c">
                                <p class="padd-r-5">限&emsp;&emsp;额：</p>
                                <div>
                                    <input class="wh120 zui-input" v-model="CfContent.jzxe"
                                           @keydown="changeDown($event)" disabled="disabled">
                                    <input v-model="PfContent.ypmc" type="hidden">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 配方列表区 -->
        <div id="pfhjList" v-cloak class="tablePfListDiv position flex-container flex-one">
            <div class="zui-table-view hzList  ">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell text-left cell-s"><span>药品规格</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>用量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>零价</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>金额</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品批号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>生产日期</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>产地名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>处方号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>备注</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>系统批号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>产地</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>供货单位</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>库房单位</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药房单位</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>产品标准号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>批准文号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>统筹类别</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">操作</div>
                            </th>
                        </tr>
                        <!--@click="checkOne($index)"-->
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50" v-if="jsonList.length!=0">
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkOne($index)"
                            :tabindex="$index"
                            v-for="(item, $index) in jsonList"
                            @dblclick="edit($index)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ypbm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ypmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ypgg"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cfyl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDec(item.cfyl*item.yplj,2)"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.scph"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDate(item.scrq,'date')"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cdmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cfh"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yysm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.xtph"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cdbm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ghdw"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.kfdw"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yfdw"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cpbzh"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.pzwh"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ybtclb"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell  cell-s">
                                <span class="flex-center padd-t-5">
                                       <em class="width30"><i class="icon-sc icon-font" @click="delLine($index)" data-title="删除"></i></em>
                                </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </div>
                <div class="zui-table-fixed table-fixed-r background-f">
                    <div class="zui-table-header">
                        <table class="zui-table zui-collapse">
                            <thead>
                            <tr>
                                <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in jsonList"
                                :tabindex="$index"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()" >
                                <td>
                                    <div class="zui-table-cell  cell-s">
                                <span class="flex-center padd-t-5">
                                       <em class="width30"><i class="icon-sc icon-font" @click="delLine($index)" data-title="删除"></i></em>
                                </span>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="addList flex-container flex-jus-e flex-align-c jizheng">
                    总金额 {{sum}} 元
                </div>
            </div>
        </div>
    </div>
    <!-- 处方列表区 -->
    <div class="side-form pop-805 flex-container flex-dir-c" :class="[{'ng-hide':index==1}]" v-cloak   id="cfhjList" role="form">
        <div class="tab-message">
            <a>处方查询</a>
            <a href="javascript:" class="fr closex ti-close" @click="index=1"></a>
        </div>
        <div class="ksys-side flex-one">
            <div  class="flex-container">
                <div class="zui-table-view hzList  padd-r-10">
                    <div class="tong-top flex-container flex-align-c">
                        <span class="padd-r-10 whiteSpace">时间段</span>
                        <div class="top-zinle flex-container flex-align-c">
                            <input class="zui-input zui-date text-indent-20" id="cfKssj" placeholder="请输入开始时间" type="text" v-model="cfKssj"/>
                            <span class="padd-r-10 padd-l-10 flex-container flex-align-c">至</span>
                            <input class="zui-input zui-date text-indent-20" id="cfJssj" placeholder="请输入结束时间" type="text" v-model="cfJssj"/>
                        </div>
                        <span class="padd-l-10 whiteSpace padd-r-10">搜索</span>
                        <input class="zui-input  wh112" type="text" v-model="parm" @keyup.13="getData()"/>
                            <span class="padd-r-10 padd-l-10 flex-container flex-align-c whiteSpace">总笔数:&nbsp;<i class="color-wtg">{{cfListTotal || 0}}</i></span>
                            <span class="padd-r-10 flex-container flex-align-c whiteSpace">总费用:&nbsp;<i class="color-wtg">{{cfListSum}}</i></span>
                    </div>
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>扣费</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>处方号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell  cell-s"><span>患者姓名</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>挂号序号/住院号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>处方类型</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>患者类型</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>处方医师</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>处方日期</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>处方金额</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>实际金额</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>年龄</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>费别</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>性别</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>科室配药</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>配药科室</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>袋子费</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>袋子数</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>中药副数</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>科室</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>划价人</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>发药标志</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">发药人</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l">发药时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">扣费人员</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l">扣费时间</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">是否打印</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">备注</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">年龄</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">单位</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">配药科室</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                        <table class="zui-table table-width50" v-if="jsonList.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkOne($index)"
                                :tabindex="$index"
                                v-for="(item, $index) in jsonList"
                                @dblclick="edit($index)">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="istrue_tran[item.kfbz]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="item.cfh"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="item.bah"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.cflxmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="brlx_tran[item.brlx]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.cfysxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="fDate(item.cfrq,'datetime')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDec(item.cfje,2)"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDec(item.sjje,2)"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.nl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fbmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="istrue_tran[item.pybz]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.pyksmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.dzf"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.dzs"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.zyfs"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.brksmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.hjryxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="istrue_tran[item.fybz]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fyryxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="fDate(item.fyrq,'datetime')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.kfryxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="fDate(item.kfrq,'datetime')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="istrue_tran[item.isprint]"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.bzsm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.brnl"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.brnldw"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.pyks"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
</body>
<script type="text/javascript" src="cfhj.js"></script>
</html>
