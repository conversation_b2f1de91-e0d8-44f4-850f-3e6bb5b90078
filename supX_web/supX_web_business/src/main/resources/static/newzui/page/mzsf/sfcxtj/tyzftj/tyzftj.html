<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>统一支付统计</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="wrapper">
    <div class="panel tong-top flex-container flex-align-c">
        <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="goToPage(1)">刷新</button>
        <div class="flex-container flex-align-c ">
            <div class="flex-container padd-r-10 flex-align-c ">
                <span class="whiteSpace margin-r-5 ft-14">检索</span>
                <input class="zui-input wh180" placeholder="请输入票据起始号" v-model="param.parm" type="text" id="jsvalue" @keydown.enter="goToPage(1)"/>
            </div>
            <div class="flex-container padd-r-10 flex-align-c">
                <span class="ft-14 padd-r-5 whiteSpace">查询方式</span>
                <select-input @change-data="resultChange" :not_empty="true" :child="options"
                              :index="param.type" :val="param.type" :search="true"
                              :name="'param.type'">
                </select-input>
            </div>
        </div>

    </div>
    <div class="zui-table-view padd-r-10 padd-l-10" >
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">序号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">平台成交金额</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">平台唯一标识</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">交易日期</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">操作员编号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">交易名称</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">HIS缴费记录ID</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">HIS缴费金额</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50" v-if="jsonList.length!=0">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jsonList" @dblclick="edit($index)"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="hoverMouse(true,$index)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.amount"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.innerorderno"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.jyrq,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.czyh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jymc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.jsjlid"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.yjje"></div>
                    </td>
                </tr>
                </tbody>
            </table>
            <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
<script src="tyzftj.js"></script>
</body>

</html>
