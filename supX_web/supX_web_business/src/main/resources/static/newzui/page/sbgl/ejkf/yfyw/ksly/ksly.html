<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室领药</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script type="text/javascript" src="jquery.jqprint-0.3.js"></script>
    <link rel="stylesheet" href="pr.css" media="print"/>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ksly.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel box-fixed">
        <div class="tong-top">
            <button class="tong-btn btn-parmary "><i class="icon-width icon-dc-b "></i>导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="sx">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药房</label>
                    <div class="zui-input-inline wh122 margin-l-7">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfList" :index="'yfmc'" :index_val="'yfbm'" :val="barContent.yfbm"
                                      :name="'barContent.yfbm'" :search="true" :index_mc="'yfmc'">
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">科室</label>
                    <div class="zui-input-inline wh122 margin-f-l25">
                        <select-input @change-data="resultRydjChange"
                                      :child="zyksList" :index="'ksmc'" :index_val="'ksbm'" :val="barContent.ksbm"
                                      :name="'barContent.ksbm'" :search="true" :index_mc="'ryksmc'" >
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">发药状态</label>
                    <div class="zui-input-inline wh122 margin-l-5">
                        <select-input @change-data="resultRydjChange"
                                      :child="ztList" :index="'ztmc'" :index_val="'ztbm'" :val="barContent.ztbm"
                                      :name="'barContent.ztbm'" :search="true" :index_mc="'ztmc'" >
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">时间段</label>
                    <div class="zui-input-inline flex-container flex-align-c  margin-f-l10">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请日期" id="timeVal"  /><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择处方结束时间"  id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>




            </div>
        </div>
    </div>
   <div class="contenter" style="margin-top: 108px;">
       <div class="cont-left">
               <div class="left-top">
                <i>序号</i>
                <i>发药单号</i>
                <i>发药时间</i>
                <i>发药人</i>
               </div>
           <ul class="left-list">
               <li v-for="(item,$index) in jsonList"  :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index" ref="list" @dblclick="edit($index)">
                   <i v-text="$index+1"></i>
                   <i class="relative"><em class="title " :data-title="item.fydh" v-text="item.fydh">发药单</em></i>
                   <i v-text="fDate(item.fysj,'date')">2015/12-10</i>
                   <i v-text="item.fyrxm">张三</i>
               </li>
               <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
           </ul>
       </div>
       <div class="cont-left cont-right">
           <div class="right-top">
           <i>
               <input-checkbox @result="reCheckBox" :list="'fylist'"
                               :type="'all'" :val="isCheckAll">
               </input-checkbox>
           </i>
           <i>序号</i>
           <i>摆药单号</i>
           <i>摆药时间</i>
           <i>摆药人</i>
           <i>状态</i>
           </div>
           <ul class="right-list">
               <li v-for="(item,$index) in fylist" @click="checkSelect([$index,'some','fylist'],$event)":class="[{'tableTr': $index%2 == 0},{'table-hovers':isChecked[$index]}]":tabindex="$index" ref="list" @dblclick="edit($index)">
                   <i>
                      <input-checkbox @result="reCheckBox" :list="'fylist'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                      </input-checkbox>
                   </i>
                   <i v-text="$index+1"></i>
                   <i class="relative"><em class="title " :data-title="item.bydh" v-text="item.bydh">摆药单</em></i>
                   <i v-text="fDate(item.bysj,'date')">2015/12-10</i>
                   <i v-text="item.byrxm">张三</i>

               </li>
               <p v-if="fylist.length==0" class="  noData  text-center zan-border">暂无数据...</p>
           </ul>
       </div>

   </div>
    <div class="zui-table-tool">
        <div class="rkgl-position" style="display: flex;justify-content: flex-end;align-items:center;">
           <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db">取消领药</button>
                <button class="tong-btn btn-parmary xmzb-db"  @click="lingyao">领药</button>
           </span>
        </div>
    </div>
</div>
</div>
<div class="side-form  pop-850"  :class="{'ng-hide':nums==1}" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="slgl-by" v-show="right" v-for="(item, $index) in detailList">
        <i style="width: 34%;text-indent: 20px;"><em>摆药单号:</em><em v-text="item.bydh"></em></i>
        <i style="width: 25%"><em>领药时间:</em><em v-text="fDate(item.fysj,'date')"></em></i>
        <i><em>领药科室:</em><em v-text="item.ksmc"></em></i>
        <i><em>摆药药房:</em><em v-text="item.yfmc"></em></i>
    </div>
    <div class="slgl-by" v-show="left" v-for="(item, $index) in detailList">
        <i style="width: 34%;text-indent: 20px;"><em>发药单号:</em><em v-text="item.fydh"></em></i>
        <i style="width: 25%"><em>领药时间:</em><em v-text="fDate(item.fysj,'date')"></em></i>
        <i><em>领药科室:</em><em v-text="item.ksmc"></em></i>
        <i><em>摆药药房:</em><em v-text="item.yfmc"></em></i>
    </div>
    <div class="ksys-side">
        <div class="fyxm-tab">
            <div><span :class="{'actives':num==0}" @click="tabBg(0)">明细发药单</span></div>
            <div><span :class="{'actives':num==1}" @click="tabBg(1)">汇总发药单</span></div>
            <div class="fyty-fr" style="float: right;">
                <i class="color-dsh fyxm-show" style="width: 65px;"><em>用法选择:</em></i>
                <select class="zui-input fyty-select" v-model="fylx" @change="Y_change(fylx)">
                    <option :value="0">全部</option>
                    <option :value="1">口服</option>
                    <option :value="2">输液</option>
                    <option :value="3">肌注</option>
                    <option :value="4">其他/口服</option>
                    <option :value="5">输液/肌注</option>
                    <option :value="6">输液/肌注/其他</option>
                </select>
                 <!--<select-input @change-data="resultChangeFy" :not_empty="true" :child="fylx_tran"-->
                            <!--:index="fylx" :val="fylx" :search="true"-->
                            <!--:name="'fylx'">-->
                 <!--</select-input>-->
                <span class="iconClass"></span>
            </div>
        </div>
        <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==0}"  id="zhcx">
            <h2 class="h2title" v-text="prTitle"></h2>
            <ul class="cfhj-top all-height">
                <li>

                    <i>序号</i>
                    <i>住院号</i>
                    <i>病员姓名</i>
                    <i>药品名称</i>
                    <i>规格</i>
                    <i>数量</i>
                    <i>单位</i>
                    <i>医师</i>
                    <i>零价</i>
                </li>
            </ul>
            <ul class="cfhj-content all-height">
                <li v-for="(item,$index) in mxList">
                    <i v-text="$index+1">1</i>
                    <i class="relative">
                        <em class="title " v-text="item.zyh" ></em>
                    </i>
                    <i v-text="item.brxm"></i>
                    <i class="relative">
                        <em class="title" v-text="item.ryypmc" ></em>
                    </i>
                    <i class="relative">
                        <em class="title " v-text="item.ypgg" ></em>
                    </i>
                    <i v-text="fDec(item.fysl,2)">频次</i>
                    <i v-text="item.yfdwmc">单位</i>
                    <i v-text="item.ysxm">单位</i>
                    <i v-text="fDec(item.yplj,2)"></i>
                    <p v-if="mxList.length==0" class="  noData -top-10 text-center zan-border">暂无数据...</p>
                </li>
            </ul>
        </div>
        <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==1}"  id="zhcx1">
            <h2 class="h2title" v-text="prTitle"></h2>
            <ul class="cfhj-top all-height">
                <li>
                    <i>序号</i>
                    <i>药品名称</i>
                    <i>剂型</i>
                    <i>用法</i>
                    <i>规格</i>
                    <i>单位</i>
                    <i>零价（均价）</i>
                    <i>发药数量</i>
                    <i>金额</i>
                </li>
            </ul>
            <ul class="cfhj-content all-height">
                <li v-for="(item,$index) in mxList">
                    <i v-text="$index+1">1</i>
                    <i class="relative">
                        <em class="title" v-text="item.ryypmc" ></em>
                    </i>
                    <i v-text="item.jxmc">剂量</i>
                    <i v-text="item.yyffmc">用法</i>
                    <i class="relative">
                        <em class="title" v-text="item.ypgg" ></em>
                    </i>
                    <i v-text="item.yfdwmc">规格</i>
                    <i v-text="item.yplj">单位</i>
                    <i v-text="fDec(item.fysl,2)">单位</i>
                    <i v-text="fDec(item.fysl * item.yplj,2)">单位</i>
                </li>
            </ul>
        </div>

    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-parmary-f2a xmzb-db" @click="print">打印</button>
        <button class="zui-btn btn-parmary xmzb-db" v-show="ly" @click="lingyao">领药</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ksly.js"></script>

</body>

</html>