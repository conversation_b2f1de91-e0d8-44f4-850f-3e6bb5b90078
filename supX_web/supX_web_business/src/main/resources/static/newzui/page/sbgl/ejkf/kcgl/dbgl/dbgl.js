function getData() {
    wrapper.getData()
}
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        bsdContent: {
            ckfs: "03", //报损方式
            lyr: userId //操作人
        },
        //调拨单
        dbdContent: {
            'zdrq': getTodayDate(),
            'kfbm': null,
        },
        jsonList: [],
        ylbm: 'N040030020022003',
        yfList: [],
        qxksbm: '',
        rkd: {}, //入库单对象
        num: 0,
        rkdList: [], //入库单集合
        json: {},
        csParm: {},
        ryList: [],
        zdy: userId,
        dbdList: [],
        thdList: [], //退货单集合
        param: {
            zt:'9',
            page: 1,
            rows: 10,
            sort: "",
            order: "asc",
            parm: "",
            beginrq: null,
            endrq: null
        },
        zhuangtai: {
            "0": "待审核",
            "1": "已审核",
            "2": "作废",
        },
        queren: {
            "0": "待确认",
            "1": "确认"
        },

        KSList: [],
        KFList: [],
        //材料信息对象
        popContent: {},
        totlePage: '',
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {
        var myDate = new Date();
        this.getYFData();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: this.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: this.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
    },
    methods: {
        kd: function () {
            sessionStorage.removeItem('dbglitem');
            // this.topNewPage('开单', 'page/yfgl/kcgl/dbgl/kaidan/kaidan.html');
            this.topNewPage('开单', 'page/hcgl/ejkf/kcgl/dbgl/kaidan/kaidan.html');
        },
        //获取二级库房
        getYFData: function () {
            //初始化页面记载库房
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm={"ylbm": "N040100021003"}',
                function (data) {
                    if (data.a == 0) {
                        wrapper.yfList = data.d.list;
                        if (wrapper.yfList.length > 0){
                            Vue.set(wrapper.popContent, 'yfbm', wrapper.yfList[0].yfbm);//二级库房默认
                            wrapper.getData();
                        }
                    } else {
                        malert("二级库房获取失败", 'right', 'defeadted');
                    }
                });
        },
        /*getYFData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf", function (json) {
                if (json.a == 0) {
                    wrapper.yfList = json.d.list;
                    Vue.set(wrapper.popContent, 'yfbm', wrapper.yfList[0].yfbm);//二级库房默认
                    wrapper.getData();
                }
            });
        },*/
        //调出二级库房改变
        resultChanges: function (val) {
            Vue.set(this.popContent, 'yfbm', val[0]);
            Vue.set(this.popContent, 'yfmc', val[4]);
            wrapper.getData();
        },
        resultChange:function (val){
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData();
        },
        //作废
        invalidData: function (num) {
            if (common.openConfirm("确认作废-" + this.dbdList[num].ckdh + "-调拨单吗?", function () {
                var obj = {
                    ckdh: wrapper.dbdList[num].ckdh
                };
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=zf', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.getData();
                        malert('作废成功', 'top', 'success');

                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
            })) {
                return false;

            }
        },
        showDetail: function (val) {
            sessionStorage.setItem('dbglitem', JSON.stringify(val));
            if(this.popContent.yfbm==val.yfbm){  //审核
                // this.topNewPage('开单', 'page/yfgl/kcgl/dbgl/kaidan1/kaidan.html');
                this.topNewPage('开单', 'page/hcgl/ejkf/kcgl/dbgl/kaidan1/kaidan.html');
            }else if(this.popContent.yfbm==val.dbyf && val.shzfbz== 0){
				
				this.topNewPage('开单', 'page/hcgl/ejkf/kcgl/dbgl/kaidan/kaidan.html');
			}else{ //已审核
                // this.topNewPage('开单', 'page/yfgl/kcgl/dbgl/kaidan2/kaidan.html');
                this.topNewPage('开单', 'page/hcgl/ejkf/kcgl/dbgl/kaidan2/kaidan.html');
            }
        },
        //确认
        confirm: function (index) {
            var obj = JSON.parse(JSON.stringify(this.dbdList[index]));
            if (obj.ckdh == undefined) {
                malert('请选择调拨单！','top','defeadted');
                return;
            }
            //设置确认标志
            obj.shzfbz = 1;
            obj.qrbz = 1;

            //方式请求确认调拨单
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=updateDbd',
                JSON.stringify(this.dbdSelected))
                .then(function (data) {
                    if (data.body.a == 0) {
                        //打印数据
                        this.print();
                        malert(data.body.c,'top','success');
                    } else {
                        malert(data.body.c,'top','defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });

        },
        //进入页面加载单据列表信息
        getData: function () {
            common.openloading('.zui-table-view');
            this.json.jjzj = 0;
            this.json.ljzj = 0;
            if (!wrapper.popContent.yfbm) {
                malert("请选择二级库房!", 'top', 'defeadted');
                return;
            }
            if (this.param.zt != '9') {
                Vue.set(this.param, 'shzfbz', this.param.zt);
            } else {
                Vue.set(this.param, 'shzfbz', null);
            }
            //拼接参数对象
            this.dateBegin = this.param.beginrq;
            this.dateEnd = this.param.endrq;
            var bean = {
                shzfbz:this.param.shzfbz,
                yfbm: wrapper.popContent.yfbm,
                beginrq: this.dateBegin,
                endrq: this.dateEnd,
                parm: this.param.parm,
                page: this.param.page,
                rows: this.param.rows
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=query&bean=' + JSON.stringify(bean),
                function (data) {
                    if (data.a == 0) {
                        wrapper.dbdList = data.d.list;
                        wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
                    } else {
                        malert(data.c,'top','defeadted');
                    }
                });
            common.closeLoading()
        },
    }
});
