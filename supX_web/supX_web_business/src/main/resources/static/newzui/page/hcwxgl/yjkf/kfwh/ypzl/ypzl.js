
var qjindex = '';
var zlxmbm = "";
(function () {
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            popContent:{},
            title:'',
            totle:'',
            num:0,
            param: {
                page: '',
                rows: 100,
                total: ''
            },
            search : ''
        },
        watch:{
        	'search' : function(){
        		yjkmtableInfo.getData();
        	}
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.closes();
                wap.title='新增材料种类';
                wap.isYpzl = true;
                wap.open();
                wap.popContent={
                    yplx: '3'
                };

            },
            sx:function () {
                yjkmtableInfo.getData();
            },
            //删除
            del:function () {
                yjkmtableInfo.remove();
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    yjkmtableInfo.getData();
                }

            },
        }
    });


    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
            isYpzl: false,
            isJcqj: false,
            isShow: false,
            popContent: {
                yplx: '3'
            },
            fyContent: {},
            isKeyDown: null,
            title: '材料种类',
            dyFy: [],
            dg: {
                page: 1,
                rows: 10,
                sort: "",
                order: "asc",
                parm: ""
            },
            them_tran: {},
            them: {
                '项目编码': 'mxfybm',
                '项目名称': 'mxfymc',

            }
        },

        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                wap.isYpzl = false;
                wap.isJcqj = false;
            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                if (wap.isYpzl){
                    yjkmtableInfo.saveData();
                } else if(wap.isJcqj){
                    yjkmtableInfo.saveJcqj();
                }
            },
            //下拉table检索数据
            changeDown: function(event, type) {
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var isReq = this.keyCodeFunction(event, 'fyContent', 'searchCon');

                Vue.set(this.popContent, 'dyfyxmmc', this.fyContent.mxfymc);
                Vue.set(this.popContent, 'dyfyxm', this.fyContent.mxfybm);
            },
            //当输入值后才触发
            change: function(event, type) {
                var _searchEvent = $(event.target.nextElementSibling).eq(0);

                //分页参数
                this.dg.page = 1;
                this.dg.rows = 15;
                this.dg.parm = wap.popContent.dyfyxmmc;
                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=queryDyFyXm&json=' +
                    JSON.stringify(this.dg),
                    function(data) {
                        wap.searchCon = data.d.list;
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                        if(data.d.list.length != 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return;
                        } else {
                            $(".selectGroup").hide();
                        }
                    });
            },

            //单击选中
            selectOne: function(item) {
                //查询下页
                if(item == null) {
                    //分页操作
                    var parm = {};
                    this.dg.page++;

                    $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=queryDyFyXm&dg=&json=' +
                        JSON.stringify(this.dg),
                        function(data) {
                            if(data.a == 0) {
                                for(var i = 0; i < data.d.list.length; i++) {
                                    wap.searchCon.push(data.d.list[i]);
                                }
                                wap.total = data.d.total;
                                wap.selSearch = 0;
                            } else {
                                malert('分页信息获取失败','top','defeadted')
                            }

                        });
                    return;
                };
                Vue.set(this.popContent, 'dyfyxmmc', item.mxfymc);
                Vue.set(this.popContent, 'dyfyxm', item.mxfybm);
                $(".selectGroup").hide();
                $("#rksl").focus();
                //保留两位小数

            },
        }
    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],
            jcqjList: [], //加成区间
            dyFyList: {},
            isChecked: []
        },
        methods: {
            clickOne: function (index) {
                if (this.ifEdit) return;
                // 重置选中
                for (var i = 0; i < this.jsonList.length; i++) {
                    if (this.isChecked[i]) this.isChecked[i] = false;
                }
                this.isChecked[index] = true;
                this.ypzlbm=this.jsonList[index].ypzlbm;
                this.getJcqjData();
            },
            //进入页面加载列表信息
            getData: function () {
                common.openloading('.zui-table-view')
            	Vue.set(this.param,'parm',wrapper.search);
                var _json = {
                    yplx : '3'
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=query&dg=" + JSON.stringify(this.param)+"&json="+JSON.stringify(_json), function(json) {
                    if(json.a == '0') {
                        yjkmtableInfo.totlePage = Math.ceil(json.d.total / yjkmtableInfo.param.rows);
                        yjkmtableInfo.jsonList = json.d.list;
                    } else {
                        // malert(json.c,'top','defeadted')
                    }
                });
                common.closeLoading()
            },

            //获取种类对应的加成区间列表
            getJcqjData: function () {
                common.openloading('.zui-table-view')
                var qjParm = {ypzlbm: yjkmtableInfo.ypzlbm};
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=queryYpzlJc&json=" + JSON.stringify(qjParm), function(json) {
                    if(json.a == '0') {
                        yjkmtableInfo.jcqjList = json.d;
                    } else {
                        malert(json.c,'top','defeadted')
                    }
                });
                common.closeLoading()
            },

            //保存
            saveData: function() {
            	if(!wap.popContent.tybz){
            		Vue.set(wap.popContent,'tybz','0');
            	}
                if(wap.popContent.ypzlmc == null) {
                    malert("请输入材料种类名称",'top','defeadted')
                    return;
                }
                if(wap.popContent.jcbl == null) {
                    malert("请输入加成比例",'top','defeadted')
                    return;
                }
                wap.popContent.yplx = '3';//默认材料
                if(wap.popContent.yplx == null) {
                    malert("请输入材料类型",'top','defeadted')
                    return;
                }
                if(wap.popContent.dyfyxm == null) {
                    malert("请输入对应费用项目",'top','defeadted')
                    return;
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=save&json=" +
                    JSON.stringify(wap.popContent),
                    function(data) {
                        if(data.a == 0) {
                            yjkmtableInfo.getData();
                            wap.closes();
                            malert("数据保存成功",'top','success')
                        } else {
                            malert("上传数据失败",'top','defeadted');
                        }
                    })
            },

            //保存加成区间
            saveJcqj: function() {
                if(wap.popContent.qsz == null) {
                    malert("请输入起始值",'top','defeadted')
                    return;
                }
                if(wap.popContent.jclx == null) {
                    malert("请输入加成类型",'top','defeadted')
                    return;
                }
                if(wap.popContent.jsz && parseFloat(wap.popContent.qsz) >= parseFloat(wap.popContent.jsz)) {
                    malert("结束值必须大于起始值",'top','defeadted')
                    return;
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=saveYpzlJc&json=" + JSON.stringify(wap.popContent),
                    function(data) {
                        if(data.a == 0) {
                            yjkmtableInfo.getJcqjData();
                            wap.closes();
                            malert("数据保存成功",'top','success')
                        } else {
                            malert("上传数据失败",'top','defeadted');
                        }
                    })
            },

            //编辑修改根据num判断
            edit: function(num) {
                wap.closes();
                wap.isYpzl = true;
                wap.title='编辑材料种类'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            },

            //新增加成区间
            addJcqj: function(num) {
                wap.closes();
                wap.isJcqj = true;
                wap.title = "新增加成区间"
                wap.popContent={};
                wap.popContent={
                    ypzlbm : this.jsonList[num].ypzlbm,
                    ypzlmc : this.jsonList[num].ypzlmc
                };
                wap.open();
            },

            //编辑加成区间
            editJcqj: function(num) {
                wap.closes();
                wap.isJcqj = true;
                wap.title = "编辑加成区间"
                wap.popContent = JSON.parse(JSON.stringify(this.jcqjList[num]));
                wap.open();
            },

            //删除加成区间
            delQj: function(num) {
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=delYpzlJc&json=" + JSON.stringify(this.jcqjList[num]),
                    function(data) {
                        if(data.a == 0) {
                            yjkmtableInfo.getJcqjData();
                            wap.closes();
                            malert("删除成功",'top','success')
                        } else {
                            malert("删除失败",'top','defeadted');
                        }
                    })
            },

            remove: function() {
                var ypzlList = [];
                for(var i = 0; i < this.isChecked.length; i++) {
                    if(this.isChecked[i] == true) {
                        var ypzlbm = {
                            'ypzlbm': this.jsonList[i].ypzlbm
                        };
                        ypzlList.push(JSON.stringify(ypzlbm));
                    }
                }
                if(ypzlList.length == 0) {
                    malert("请选中您要删除的数据",'top','defeadted');
                    return false;
                }
                if(!confirm("请确认是否删除")) {
                    return false;
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=delete&json=[" +
                    ypzlList + "]",
                    function(data) {
                        yjkmtableInfo.getData();
                        if(data.a == 0) {
                            malert("删除成功",'top','success')
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    })
            }


        },


    });
    yjkmtableInfo.getData();
})()
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol) {
        $(".selectGroup").hide();
    }
});
$(window).resize(function () {
    changHeight();
})
function changHeight() {

    var height_b=$(window).height()-$(".zui-table-tool").outerHeight();
    $('.background-box').css({
        'height':height_b,
        'overflow':'auto'
    })
}

setTimeout(function () {
    changHeight();
},150)





