.tong-btn {
  width: auto;
  min-width: 72px;
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border: none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right: 10px;
}
.font12 {
  font-size: 12px !important;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}
.wh240 {
  width: 240px!important;
}
.wh182 {
  width: 182px !important;
}
.wh100 {
  width: 100px !important;
}
.wh66 {
  width: 66px !important;
}
.wh112 {
  width: 112px !important;
}
.wh120 {
  width: 120px !important;
}
.wh122 {
  width: 122px !important;
}
.wh138 {
  width: 138px !important;
}
.wh200 {
  width: 200px !important;
}
.wh220 {
  width: 220px !important;
}
.wh150 {
  width: 150px !important;
}
.wh1000 {
  width: 80% !important;
}
.wh50 {
  width: 50px !important;
}
.wh70 {
  width: 70px !important;
}
.width162 {
  width: 162px !important;
}
.wh160 {
  width: 160px !important;
}
.wh453 {
  width: 453px !important;
}
.wh247 {
  width: 243px !important;
  display: flex;
  justify-content: start;
  align-items: center;
}
.wh179 {
  width: 179px !important;
}
.wh59 {
  width: 59px !important;
}
.padd {
  padding: 0 !important;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-ed {
  background: #edf2f1 !important;
}
.color-green {
  color: #1abc9c !important;
  font-style: normal;
}
.color-dsh {
  color: #f3b169;
}
.color-ysh {
  color: #45e135;
}
.color-wtg {
  color: #ff4735;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.icon-hs:before {
  color: #757c83;
}
.icon-bj:before {
  top: -2px;
  left: 22px;
}
.icon-sh-h:before {
  top: 0;
  left: 15px;
}
.userNameBg {
  width: 100%;
  height: 100px;
  position: relative;
  background-size: cover;
  background: url("/newzui/pub/image/userImg.png") center right no-repeat #708f89;
}
.userNameBg .bazs-flex {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 15px 0 20px 20px;
}
.userNameBg .bazs-maid {
  width: 68px;
  height: 68px;
  overflow: hidden;
  display: block;
}
.userNameBg .bazs-maid img {
  width: 68px;
  height: 68px;
  border-radius: 50%;
}
.userNameBg .bazs-name {
  display: block;
  color: #fff;
  width: auto;
  padding-left: 20px;
}
.userNameBg .bazs-name p {
  line-height: 22px;
}
.userNameBg .bazs-name p em {
  font-size: 22px;
  padding-right: 30px;
}
.userNameBg .bazs-name p i {
  padding-right: 30px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}
.bazs-time {
  width: 100%;
  padding: 30px 0 0 23px;
  background: #fff;
}
.bazs-time .bazs-left-time ul li {
  width: 100%;
  float: left;
  min-height: 70px;
}
.bazs-time .bazs-left-time ul li .bazs-now {
  background-image: linear-gradient(-1deg, #1ebe9e 2%, #30edc7 98%);
  width: 18px;
  height: 18px;
  border-radius: 100%;
  display: block;
  float: left;
  position: relative;
  margin-top: 2px;
}
.bazs-time .bazs-left-time ul li .bazs-now:after {
  content: '';
  width: 2px;
  height: 60px;
  position: absolute;
  left: 8px;
  top: 18px;
  background: #f3f4f6;
}
.bazs-time .bazs-left-time ul li:last-child .bazs-now:after {
  background: none;
}
.bazs-time .bazs-left-time ul li .bazs-ggg {
  background: url("../../../../css/images/<EMAIL>") center left no-repeat;
  background-size: 18px 18px;
}
.bazs-time .bazs-left-time .bazs-right-list {
  margin-left: 30px;
  float: left;
}

.box-fixed {
  top: 10px;
}
.ts{
  font-size:12px;
  color:#757c83;
  line-height: 36px;
  text-align: right;
  padding-right: 10px;
}
.ts span{
  color: #afe8dc;
}