.xmzb-top {
  width: 100%;
  padding: 15px 34px;
  overflow: hidden;
}
.xmzb-top-left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.xmzb-top-left i {
  margin-right: 5px;
}
.xmzb-top-left i:nth-child(2) {
  margin-right: 19px;
}
.xmzb-content {
  width: 100%;
  padding: 10px 10px 0;
  box-sizing: border-box;
}
.xmzb-content-left {
  width: 35%;
  float: left;
}
.xmzb-content-left .content-left-top {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  border: 1px solid #e9eee6;
  background: #edf2f1;
  height: 36px;
  line-height: 36px;
  align-items: center;
}
.xmzb-content-left .content-left-top i {
  width: calc((100% - 50px)/2);
  text-align: center;
}
.xmzb-content-left .content-left-top i:first-child {
  width: 50px;
}
.xmzb-content-left .content-left-list {
  width: 100%;
  overflow: auto;
  border-top: none;
  border-right: none;
}
.xmzb-content-left .content-left-list li {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
  line-height: 40px;
  border: 1px solid #e9eee6;
  border-top: 1px solid #fff;
}
.xmzb-content-left .content-left-list li i {
  width: calc((100% / 3));
  text-align: center;
}
.xmzb-content-left .content-left-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-content-left .content-left-list li:first-child {
  border-top: none;
}
.xmzb-content-left .content-left-list li:hover {
  background: rgba(26, 188, 156, 0.08);
}
.xmzb-content-right {
  width: 63%;
  float: right;
}
.xmzb-content-right .content-right-top {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  border: 1px solid #e9eee6;
  background: #edf2f1;
  height: 36px;
  align-items: center;
}
.xmzb-content-right .content-right-top i {
  width: calc((100% / 6));
  text-align: center;
}
.xmzb-content-right .content-right-list {
  width: 100%;
  overflow: auto;
  border-top: none;
}
.xmzb-content-right .content-right-list li {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  line-height: 40px;
  border: 1px solid #e9eee6;
  border-top: none;
  cursor: pointer;
}
.xmzb-content-right .content-right-list li i {
  width: calc((100% / 6));
  text-align: center;
}
.xmzb-content-right .content-right-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-content-right .content-right-list li:hover {
  background: rgba(26, 188, 156, 0.08);
}
.xmzb-title {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: #edf2f1;
  border: 1px solid #e9eee6;
  height: 34px;
}
.xmzb-title i {
  width: calc((100% / 5));
  text-align: center;
}
.xmzb-list {
  width: 100%;
  max-height: 320px;
  overflow: auto;
}
.xmzb-list li {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-top: 1px solid #e9eee6;
  height: 52px;
}
.xmzb-list li:nth-child(2n) {
  background: #fdfdfd;
}
.xmzb-list li:first-child {
  border-top: none;
}
.xmzb-list i {
  width: calc((100% / 5));
  text-align: center;
}
.font16 {
  font-size: 16px !important;
}
.xmzb-ok {
  width: 100%;
  height: 70px;
  border-top: 1px solid #e9eee6;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.xmzb-ok button {
  margin-right: 15px;
}
.font-icon {
  position: absolute;
  right: 90px;
  top: 3px;
  color: rgba(255, 255, 255, 0.8);
}
.min-chuangkou {
  right: 11px;
}
