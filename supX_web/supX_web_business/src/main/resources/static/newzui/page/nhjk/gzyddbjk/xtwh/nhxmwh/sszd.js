/**
 * Created by mash on 2017/10/8.
 */
    var sszd = new Vue({
        el: '#sszd',
        mixins: [baseFunc, tableBase],
        data: {
            jsonList: [],
            totlePage: null
        },
        methods: {
            // 下载编码
            downData: function () {
            	   if (!nh_date){
                   	malert("请选择编码下载时间!", 'top', 'defeadted')
                   	return;
                   }
                   if (!Menu.bxlbbm){
                   	malert("保险编码为空,不能保险接口交易!", 'top', 'defeadted');
                   	return;
                   }
                   if (!Menu.bxurl){
                   	malert("保险接口地址不能为空,请与管理员联系!", 'top', 'defeadted');
                   	return;
                   }
                   if (!Menu.billCode){
                   	return;
                   }
                   var head = {
                   	operCode:"S31",
                   	billCode:Menu.billCode,
                   	rsa:""
                   };
                   var body = {
                   	startDate:nh_date + " 000"
                   }
                   
                   var param = {
                   	head:head,
                   	body:body
                   }
                   var str_param = JSON.stringify(param);
                   
                   $.getJSON(
                   "/actionDispatcher.do?reqUrl=New1BxInterface&url="+Menu.bxurl+"&bxlbbm="+Menu.bxlbbm+"&types=S&parm="+str_param, function (json) {
                   	console.log(json);
                   	if (json.a == 0 && json.d){
                   		sszd.jsonList = json.d;
                   	}else{
                   		malert(json.c, 'top', 'defeadted');
                   	}
                   });
            },
            // 请求数据
            getData: function () {
    				$.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url="+Menu.bxurl+"&bxlbbm="+Menu.bxlbbm+"&types=ssbm&method=query&parm="+JSON.stringify(this.param), function (json) {
                    	if (json.a == 0 && json.d){
                    		var res=eval('('+json.d+')');
                    		sszd.totlePage = Math.ceil(res.total/sszd.param.rows);
                    		sszd.jsonList = res.list;
                    	}else{
                    		malert(json.c, 'top', 'defeadted');
                    	}
                    });
            }
        }
    });
    sszd.getData();
