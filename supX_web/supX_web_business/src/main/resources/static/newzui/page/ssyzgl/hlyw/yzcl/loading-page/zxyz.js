var WRAPPER,
    WRAPPER_HEIGHT;
var shyz = new Vue({
    el: '#loadingPage',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        brlistjson: {},//只用于接受请求LIST对象
        brList: [],
        yzList: [],
        jkList: [],
        yzzxInfoList: [],//真正的列表
        showBrInfoList: [], // 渲染的病人数组
        n_brItemListSumHeight: 0, // 所有病人加起来的高度
        n_meanHeight: 0, // 平均病人的高度
        a_brPosition: [0],// 病人位置定位
        a_brBoxHeight: [], // 病人的高度列表
        j_brListWinSize: { // 显示区域的边界
            top: null,
            bottom: null,
            paddTop: null,
            paddBottom: null
        },
        zyhs: [],
        ksid: null,//科室编码
        caqxContent: null,//参数权限对象
        ifClick: true,
        isOver: false,//是否全选
        hoverBrListIndex: undefined,
        userPhoto: [
            "/newzui/pub/image/maleBaby.png",
            "/newzui/pub/image/femalebaby.png",
            "/newzui/pub/image/Group <EMAIL>",
            "/newzui/pub/image/Group <EMAIL>",
            "/newzui/pub/image/juvenile.png",
            "/newzui/pub/image/maid.png",
            "/newzui/pub/image/youth.png",
            "/newzui/pub/image/woman.png",
            "/newzui/pub/image/grandpa.png",
            "/newzui/pub/image/grandma.png",
            "/newzui/pub/image/<EMAIL>"
        ],
        scrollFn: function () { },
    },
    computed: {
        hzListBoxStyle: function () {
            if (this.yzzxInfoList.length > 1) {
                return {
                    height: this.n_brItemListSumHeight + "px",
                    paddingTop: this.j_brListWinSize.paddTop + "px",
                    paddingBottom: this.j_brListWinSize.paddBottom + "px"
                }
            } else {
                return false;
            }
        }
    },
    created() {
        this.scrollFn = this.brScrollFn();
    },
    mounted: function () {
        this.moun();
        window.addEventListener('storage', function (e) {
            if (e.key == 'zxyz' && e.oldValue !== e.newValue) {
                shyz.zyhs = [];
                shyz.moun();
            }
        });
        // 初始化显示边界
        this.$nextTick(function () {
            WRAPPER = $("#wrapper");
            WRAPPER_HEIGHT = WRAPPER.height();
            this.brListWinSize = {
                top: 0 - WRAPPER_HEIGHT,
                bottom: WRAPPER_HEIGHT * 2
            };
            shyz.$nextTick(function () {
                changeWin()
            });
        });
    },
    methods: {
        brScrollFn: function () {
            var timer = null;
            return function () {
                clearTimeout(timer)
                timer = setTimeout(function () {
                    shyz.showListFn();
                }, 0);
            }
        },
        indexFn: function( type, posi ){
            var _meanIndex = Math.round(posi / this.n_meanHeight),
                minIndex = null,
                maxIndex = null;
            if (_meanIndex <= 0) {
                minIndex = 0;
                maxIndex = 0;
            } else if (_meanIndex >= this.a_brPosition.length - 1 ){
                _meanIndex = this.a_brPosition.length - 2;
            }
            while (minIndex === null || maxIndex === null) {
                if (this.a_brPosition[_meanIndex] < posi && this.a_brPosition[_meanIndex + 1] < posi) {
                    _meanIndex++;
                    continue;
                } else if (this.a_brPosition[_meanIndex] > posi && this.a_brPosition[_meanIndex - 1] > posi) {
                    _meanIndex--;
                    continue;
                }else if (this.a_brPosition[_meanIndex] <= posi && posi <= this.a_brPosition[_meanIndex + 1]) {
                    minIndex = _meanIndex;
                    maxIndex = _meanIndex + 1;
                    break;
                } else if (this.a_brPosition[_meanIndex - 1] <= posi && posi <= this.a_brPosition[_meanIndex]) {
                    minIndex = _meanIndex - 1;
                    maxIndex = _meanIndex;
                    break;
                }
            }
            if (type=="min"){
                return minIndex;
            }else if(type=="max"){
                return maxIndex;
            }
        },
        showListFn: function () { 
            var _top = WRAPPER.scrollTop(),
                beginIndex = this.indexFn("min", _top - WRAPPER_HEIGHT ),
                endIndex = this.indexFn("max", (_top + WRAPPER_HEIGHT * 2) > this.n_brItemListSumHeight ? this.n_brItemListSumHeight : (_top + WRAPPER_HEIGHT * 2));
            
            this.j_brListWinSize = {
                top: beginIndex,
                bottom: endIndex,
                paddTop: this.a_brPosition[ beginIndex ],
                paddBottom: this.n_brItemListSumHeight - this.a_brPosition[ endIndex ]
            };

            this.showBrInfoList = this.yzzxInfoList.slice(beginIndex, endIndex);

            this.$nextTick(function () {
                changeWin()
            });
        },
        isOverClick: function (isOver) {
            this.isOver = isOver || !this.isOver;
            this.yzzxInfoList.forEach(function (br) {
                br.isCheckAll = shyz.isOver;
                br.yzxx.forEach(function (yz) {
                    yz.isChecked = shyz.isOver;
                });
            });
        },
        moun: function () {
            this.brlistjson = JSON.parse(sessionStorage.getItem('zxyz'));
            this.brList = this.brlistjson.brlist;
            this.ksid = this.brlistjson.ksid;
            this.caqxContent = this.brlistjson.csqx;
            for (var i = 0; i < this.brList.length; i++) {
                var zyh = {
                    zyh: this.brList[i].zyh
                };
                this.zyhs.push(zyh);
            }
            this.initShData();
        },
        //重写选中
        checkSelectZx: function (brIndex, yzIndex) {
            var yzStatus = !this.yzzxInfoList[brIndex].yzxx[yzIndex].isChecked;
            this.yzzxInfoList[brIndex].yzxx[yzIndex].isChecked = yzStatus;
            if (yzStatus) {

                var yzIsOverCk = true;
                for (var x = 0; x < this.yzzxInfoList[brIndex].yzxx.length; x++) {
                    if (!this.yzzxInfoList[brIndex].yzxx[x].isChecked) {
                        yzIsOverCk = false;
                        break;
                    }
                }
                this.yzzxInfoList[brIndex].isCheckAll = yzIsOverCk;

                var isOverCk = true;
                for (var x = 0; x < this.yzzxInfoList.length; x++) {
                    if (!this.yzzxInfoList[x].isCheckAll) {
                        isOverCk = false;
                        break;
                    }
                }
                this.isOver = isOverCk;

            } else {
                this.yzzxInfoList[brIndex].isCheckAll = false;
                this.isOver = false;
            }
        },
        //重写选中
        reCheckBoxZx: function () {
            if (arguments.length == 1) {
                var isCheckAll = this.yzzxInfoList[arguments[0]].isCheckAll ? false : true,
                    yzshInfo = this.yzzxInfoList[arguments[0]],
                    yzxxList = yzshInfo.yzxx;

                this.yzzxInfoList[arguments[0]].isCheckAll = isCheckAll;
                for (var i = 0; i < yzxxList.length; i++) {
                    this.yzzxInfoList[arguments[0]].yzxx[i].isChecked = isCheckAll;
                }
            } else if (arguments.length == 2) {
                this.activeBrListIndex = arguments[0];
                this.activeIndex = arguments[1];
            }

            var isOverCk = true;
            for (var x = 0; x < this.yzzxInfoList.length; x++) {
                if (!this.yzzxInfoList[x].isCheckAll) {
                    isOverCk = false;
                    break;
                }
            }
            this.isOver = isOverCk;

            this.$forceUpdate();
        },
        zhixing: function () {
            tspop.open();
        },
        //取消审核
        qxshenhe: function () {
            var qxshData = [];
            if (!shyz.ifClick) return; //如果为false表示已经点击了不能再点
            shyz.ifClick = false;
            if (shyz.caqxContent.cs00900100107 == '0') {
                malert("对不起，您无权取消审核！", "top", "defeadted");
                YZInfo.ifClick = true;
                return
            }
            for (var i = 0; i < shyz.yzzxInfoList.length; i++) {
                for (var j = 0; j < shyz.yzzxInfoList[i]['yzxx'].length; j++) {
                    if (shyz.yzzxInfoList[i]['yzxx'][j].isChecked) {
                        qxshData.push({ "xhid": shyz.yzzxInfoList[i]['yzxx'][j].xhid });
                    }
                }
            }
            if (qxshData.length <= 0) {
                malert("无医嘱取消审核！", "top", "defeadted");
                shyz.ifClick = true;
                return;
            }
            //已执行医嘱无法取消审核
            //            for(var i = 0; i < qxshData.length; i++){
            //            	if(){
            //            		
            //            	}
            //            }
            //console.log("yzsh:"+JSON.stringify({list:this.auditingData}));
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=qxyzsh&ksbm=' + shyz.ksid,
                JSON.stringify({ list: qxshData })).then(function (data) {
                    if (data.body.a == 0) {
                        malert("取消审核成功");
                        shyz.ifClick = true;
                        shyz.initShData();//刷新
                    } else {
                        malert("取消审核失败", "top", "defeadted");
                        shyz.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
        },
        lr: function () {
            lr.open();
        },
        //获取审核医嘱信息
        initShData: function () {
            common.openloading();
            this.yzzxInfoList = [];
            if (this.zyhs.length == 0) {
                malert("请选择病人后再进行此操作！", "top", "defeadted");
                return
            }
            if (this.ksid == null) {
                malert("科室编码不能为空！", "top", "defeadted");
                return
            }
            var zyh = JSON.stringify(this.zyhs);
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=yzzx&ksbm=' + this.ksid + '&zyh=' + zyh, function (json) {
                var _list = json.d.list,
                    _length = _list.length,
                    _a_brPosition = [0],
                    _a_brBoxHeight = [];
                if (_length > 0) {
                    for (var i = 0; i < _length; i++) {
                        var _yzxxListLength = _list[i].yzxx.length,
                            _n_brBoxHeight = 193 + (_yzxxListLength || 1) * 40 - (_yzxxListLength ? 0 : 15);

                        // 存储当前元素的高度
                        _a_brBoxHeight[i] = _n_brBoxHeight;

                        // 计算后一个元素的定位
                        _a_brPosition[i + 1] = _n_brBoxHeight + _a_brPosition[i];

                        for (var int = 0; int < _yzxxListLength; int++) {
                            _list[i].yzxx[int].no = i;
                        }

                        //判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                        if (_list[i].nl < 7 && _list[i].brxb == '1') {
                            _list[i].nljd = '1';
                        } else if (_list[i].nl < 7 && _list[i].brxb == '2') {
                            _list[i].nljd = '2';
                        } else if (_list[i].nl < 18 && _list[i].nl > 6 && _list[i].brxb == '1') {
                            _list[i].nljd = '3';
                        } else if (_list[i].nl < 18 && _list[i].nl > 6 && _list[i].brxb == '2') {
                            _list[i].nljd = '4';
                        } else if (_list[i].nl < 41 && _list[i].nl > 17 && _list[i].brxb == '1') {
                            _list[i].nljd = '5';
                        } else if (_list[i].nl < 41 && _list[i].nl > 17 && _list[i].brxb == '2') {
                            _list[i].nljd = '6';
                        } else if (_list[i].nl < 66 && _list[i].nl > 40 && _list[i].brxb == '1') {
                            _list[i].nljd = '7';
                        } else if (_list[i].nl < 66 && _list[i].nl > 40 && _list[i].brxb == '2') {
                            _list[i].nljd = '8';
                        } else if (_list[i].nl > 65 && _list[i].brxb == '1') {
                            _list[i].nljd = '9';
                        } else if (_list[i].nl > 65 && _list[i].brxb == '2') {
                            _list[i].nljd = '10';
                        } else {
                            _list[i].nljd = '11';
                        }
                    }
                }
                shyz.n_brItemListSumHeight = _a_brPosition[_length];
                shyz.n_meanHeight = Math.round( _a_brPosition[_length] / _length );
                shyz.a_brPosition = _a_brPosition;
                shyz.a_brBoxHeight = _a_brBoxHeight;
                shyz.yzzxInfoList = _list;
                shyz.showListFn();
                if (_length > 1) {
                    shyz.isOverClick(true);
                }
                common.closeLoading();
                shyz.$nextTick(function () {
                    changeWin();
                });
            }, function (error) {
                common.closeLoading();
            });
        },
        closePage: function () { //关闭本页面
            this.updateHzlb();
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/zxyz.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        },
        updateHzlb: function () { // 刷新患者列表
            var x = parseInt(sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem('hszHzlbUpdate', x);
        }
    },
});

var tspop = new Vue({
    el: '#tspop',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        ifClick: true, //判断是否点击了结算按钮
        sytIsChecked: false,
        lsIsChecked: false
    },
    mounted: function () {
        laydate.render({
            elem: '#timeVal',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                // 这里放时间选择之后需要处理的代码  比如给数据赋值之类的

            }
        });
    },
    methods: {
        //关闭
        closes: function () {
            $(this.$refs.tspop).hide();
        },
        open: function () {
            $(this.$refs.tspop).show();
        },
        //执行医嘱
        okzx: function () {
            if (!tspop.ifClick) return; //如果为false表示已经点击了不能再点
            tspop.ifClick = false;
            var zxyzData = [];
            var zxListBak = [];
            for (var i = 0; i < shyz.yzzxInfoList.length; i++) {
                for (var j = 0; j < shyz.yzzxInfoList[i]['yzxx'].length; j++) {
                    if (shyz.yzzxInfoList[i]['yzxx'][j].isChecked) {
                        zxyzData.push({ "xhid": shyz.yzzxInfoList[i]['yzxx'][j].xhid });
                        zxListBak.push({ "numb": shyz.yzzxInfoList[i]['yzxx'][j].numb });
                    }
                }
            }
            if (zxyzData.length <= 0) {
                malert("无医嘱执行！", "top", "defeadted");
                tspop.ifClick = true;
                return;
            }
            if (zxListBak.length <= 0) {
                malert("无医嘱执行！", "top", "defeadted");
                tspop.ifClick = true;
                return;
            }
            console.log(shyz.caqxContent);
            if (shyz.caqxContent.cs00900100123 == '0') {
                var length = 0;
                for (var i = 0; i < zxListBak.length; i++) {
                    if (zxListBak[i].numb > 0) {
                        malert("第" + (i + 1) + "条医嘱今日已执行！无法再次执行，请检查后再执行！", 'top', 'defeadted');
                        zxyzData.splice(i - length, 1);
                        length = length + 1;
                    }
                }
                if (zxyzData.length <= 0) {
                    tspop.ifClick = true;
                    zxListBak = [];
                    return;
                }
                common.openBar();
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzzx&ksbm=' + shyz.ksid,
                    JSON.stringify({ list: zxyzData })).then(function (data) {
                        if (data.body.a == 0) {
                            malert("医嘱执行成功");
                            tspop.ifClick = true;
                            shyz.initShData();//刷新
                            zxListBak = [];
                            $(this.$refs.tspop).hide();

                            this.updateHzlb();
                            var brjson = {
                                brlist: shyz.brList,
                                ksid: shyz.ksid,
                                csqx: shyz.caqxContent,
                            };
                            sessionStorage.setItem('slyp', JSON.stringify(brjson));
                            this.topNewPage('批量申领药品', 'page/hsz/hlyw/yzcl/loading-page/slyp.html');
                        } else {
                            malert(data.body.c, "top", "defeadted");
                            tspop.ifClick = true;
                        }
                        common.closeLoading();
                    }, function (error) {
                        console.log(error);
                        common.closeLoading();
                    });
            } else {
                common.openBar();
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzzx&ksbm=' + shyz.ksid,
                    JSON.stringify({ list: zxyzData })).then(function (data) {
                        if (data.body.a == 0) {
                            malert("医嘱执行成功");
                            tspop.ifClick = true;
                            shyz.initShData();//刷新
                            $(this.$refs.tspop).fadeOut(600);
                            this.updateHzlb();
                            var brjson = {
                                brlist: shyz.brList,
                                ksid: shyz.ksid,
                                csqx: shyz.caqxContent,
                            };
                            sessionStorage.setItem('slyp', JSON.stringify(brjson));
                            this.topNewPage('批量申领药品', 'page/hsz/hlyw/yzcl/loading-page/slyp.html');
                        } else {
                            malert(data.body.c, "top", "defeadted");
                            tspop.ifClick = true;
                        }
                        common.closeLoading();
                    }, function (error) {
                        console.log(error);
                        common.closeLoading();
                    });
            }
        },
        closePage: function () { //关闭本页面
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/zxyz.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        },
        updateHzlb: function () { // 刷新患者列表
            var x = parseInt(sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem('hszHzlbUpdate', x);
        }
    }
});
