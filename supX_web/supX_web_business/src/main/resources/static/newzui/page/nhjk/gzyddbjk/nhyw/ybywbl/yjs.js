/**
 * Created by mash on 2017/9/30.
 */
    var yjs = new Vue({
        el: '#yjs',
        mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
        components: {
            'search-table': searchTable,
            'search-table1': searchTable,
            'search-table2': searchTable,
            'search-table3': searchTable,
        },
        data: {
        	 json: {},
             yjsContent: {},
             jtdz: null,
             rylx: null,
             zyts: 0,
             rcContent:{},
             jbContent: {},
             jbContent1: {},
             jbContent2: {},
             jbContent3: {},
             searchCon: [],
             searchCon1: [],
             searchCon2: [],
             searchCon3: [],
             selSearch: -1,
             page: {
                 page: 1,
                 rows: 10,
                 total: null
             },
             them_tran: {},
             them: {
                 '疾病编码': 'JBMB',
                 '疾病名称': 'JBMC',
                 '副编码': 'PYDM'
             },
             bxjson:{},
             
             yjs_list:[],
             yb_isjs : left_tab1.qhyb_isjs,
             jsbz : '',
             error:''
        },
        methods: {
        	sx : function(){
        		this.getData();
        	},
        	 getData: function () {
        		 if(menu.qhyb_conn != 'onopen'){
         			malert("医保连接失败,请刷新页面重试！");
         			return;
         		}
        		 yjs.error= '';
        		 yjs.json.end_disease = '';
                 yjs.json.end_disease_name = '';
                 yjs.jbContent.jbmc = '';
        		 if(left_tab1.rydjData.ZYH == null){
         			malert("请选择患者！");
         			return;
         		}
        			$("#ybcyButton")[0].removeAttribute("disabled");
                  	$("#ybcyButton")[0].style.background = '';
                 yjs.json = left_tab1.rydjData;
                 //yjs.json.fin_info = '好转';
                 console.log(yjs.json);
                 yjs.getAccount();
             },

             getAccount: function () {
                 this.param = {
                     page: 1,
                     rows: 10,
                     zyh: yjs.json.ZYH
                 };
                 $.getJSON(
                     "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=inhospital&method=queryYnfy&parm=" + JSON.stringify(this.param), function (json) {
                    	console.log(json)
                    	 if (json.a == 0) {
                             var res = eval('(' + json.d + ')');
                             yjs.json.YBJS_ZFY = res.FYJE;
                             yjs.json.YBJS_BXTJFY = res.SCFYJE;
                             //Vue.set();
                         } else {
                             malert(json.c);
                         }
                     });
             },
        	
        	yjs: function () {
        		if(menu.qhyb_conn != 'onopen'){
        			malert("医保连接失败,请刷新页面重试！");
        			return;
        		}
        		if(left_tab1.rydjData.ZYH == null){
         			malert("请选择患者！");
         			return;
         		}
        		if(left_tab1.rydjData.SERIAL_NO == null){
        			malert("该患者还未入院登记！");
        			return;
        		}
        		yjs.error = '';
        		yjs.json = left_tab1.rydjData;
        		yjs.json.YBJS_TCJJ= 0;
				yjs.json.YBJS_GRZH= 0;
				yjs.json.YBJS_JTZHJJ= 0;
				yjs.json.YBJS_DBHZJJ= 0;
				yjs.json.YBJS_LXJJ= 0;
				yjs.json.YBJS_GWYBZ= 0;
				yjs.json.YBJS_DBBZJJ= 0;
				yjs.json.YBJS_ECBZJJ= 0;
				yjs.YBJS_GSBXJJ= 0;
				yjs.json.YBJS_CXSYBXJJ= 0;
				yjs.json.YBJS_XJ= 0;
				yjs.json.YBJS_JMTCJJ= 0;
				yjs.json.YBJS_CXJMDBBZ= 0;
				yjs.json.YBJS_NHTCJJ= 0;
				yjs.json.YBJS_YYZF= 0;
                var message= [
            	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
            	              	['start','','BIZC131255'],
            	              	// 固定参数
            	              	['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
            	              	['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
            	              	['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],
            	              	// 入参
            	              	['putcol','','hospital_id',left_tab1.qhybCs.tqcbh],
            	              	['putcol','','biz_type',left_tab1.qhyb_ywlx],
            	              	['putcol','','center_id',left_tab1.qhybCs.ybzxbh],
            	              	
            	              	['putcol','','hospital_id',yjs.json.HOSPITAL_ID],
            	              	['putcol','','serial_no',yjs.json.SERIAL_NO],
            	              	['putcol','','last_balance',null],
            	              	['putcol','','save_flag',null],
            	              	['putcol','','treatment_type',yjs.json.TREATMENT_TYPE],
            	              	//TODO 出院诊断 
            	              	['putcol','','end_disease',yjs.json.end_disease],
            	              	['putcol','','reg_flag',null],
            	              	// 出院时间
            	              	['putcol','','end_date',toolMenu.fDate(yjs.json.BQCYRQ,'YY')],
            	              	
            	              	['run',''],
            	              	// 基本信息
            	              	['setresultset','','payinfo'],
            	              	
            	              	// 获取结果集数据结构
            	              	['getlist',
            	              	 
            	              	 	[
										['getbyname','','fund_id',''],
										['getbyname','','fund_name',''],
										['getbyname','','real_pay','']
            	              	 	 ]
	            	              	
            	              	],
            	              	
            	              	
	              	            // 业务申请信息
	              	            ['setresultset','','fetusinfo'],
	              	            ['getbyname','','fetus_name',''],
	              	            ['getbyname','','fetus_value','']
          	              	
            	              	//['destoryinterface','']
            	              ];
      			
      		
                		console.log(message);
                		if (socket.readyState===1) {
                            socket.send(JSON.stringify(message));
                            var cs=0;
                			var interval=setInterval(function(){
                					cs+=1;
                					console.log(cs);
                    				if(rs){
                        				if(ifok){
                        					console.log(rslmsg);
                        					if(rslmsg.run >= 0 ){
                        						yjs.yjs_list = rslmsg.getlist;
                        						for(var i=0;i<yjs.yjs_list.length;i++){
                        							var id = yjs.yjs_list[i].fund_id;
                        							var money = yjs.yjs_list[i].real_pay;
                        							switch(id){
                        								case '001':
                        									yjs.json.YBJS_TCJJ += parseFloat(money);
                        									break;
	                        							case '003':
	                        								yjs.json.YBJS_GRZH += parseFloat(money);
                        									break;
	                        							case '005':
	                        								yjs.json.YBJS_JTZHJJ += parseFloat(money);
                        									break;
	                        							case '201':
	                        								yjs.json.YBJS_DBHZJJ += parseFloat(money);
                        									break;
	                        							case '202':
	                        								yjs.json.YBJS_LXJJ += parseFloat(money);
                        									break;
	                        							case '301':
	                        								yjs.json.YBJS_GWYBZ += parseFloat(money);
                        									break;
	                        							case '401':
	                        								yjs.json.YBJS_DBBZJJ += parseFloat(money);
                        									break;
	                        							case '402':
	                        								yjs.json.YBJS_ECBZJJ += parseFloat(money);
                        									break;
	                        							case '501':
	                        								yjs.json.YBJS_GSBXJJ += parseFloat(money);
                        									break;
	                        							case '511':
	                        								yjs.json.YBJS_CXSYBXJJ += parseFloat(money);
                        									break;
	                        							case '999':
	                        								yjs.json.YBJS_XJ += parseFloat(money);
                        									break;
	                        							case '801':
	                        								yjs.json.YBJS_JMTCJJ += parseFloat(money);
                        									break;
	                        							case '803':
	                        								yjs.json.YBJS_CXJMDBBZ += parseFloat(money);
                        									break;
	                        							case '805':
	                        								yjs.json.YBJS_NHTCJJ += parseFloat(money);
                        									break;
	                        							case '996':
	                        								yjs.json.YBJS_YYZF += parseFloat(money);
                        									break;
                        							}
                        						}
                        						yjs.getAccount();
                        						malert("预结算查询成功");
                        					}else{
                        						yjs.error = rslmsg.error;
                        						malert("预结算失败： "+rslmsg.error);
                        					}
                        					clearInterval(interval);
                        					rs=false;
                        					ifok=false;
                        				}else{
                        					clearInterval(interval);
                        					rs=false;
                        					ifok=false;
                        				}
                        			}
                    				if(cs>=10){
                    					malert("医保超时,请重试！ ");
                    					rs=false;
                	    				ifok=false;
                    					clearInterval(interval);
                    				}
                    			},left_tab1.socketTime);
                        }else{
                        	malert("医保通信失败！ ");
                        }
            },
            
            ybcy: function () {
            	if(menu.qhyb_conn != 'onopen'){
        			malert("医保连接失败,请刷新页面重试！");
        			return;
        		}
            	yjs.error = '';
            	$.ajaxSettings.async = false;
            	if(left_tab1.rydjData.ZYH == null){
         			malert("请选择患者！");
         			return;
         		}
            	if(left_tab1.rydjData.SERIAL_NO==null){
            		malert("病人未办理医保入院！");
            		return
            	}
            	if(yjs.json.end_disease == '' || yjs.json.end_disease == null){
        			malert("出院结算前，请填写出院诊断！");
        			return;
        		}
            	yjs.json.YBJS_TCJJ= 0;
				yjs.json.YBJS_GRZH= 0;
				yjs.json.YBJS_JTZHJJ= 0;
				yjs.json.YBJS_DBHZJJ= 0;
				yjs.json.YBJS_LXJJ= 0;
				yjs.json.YBJS_GWYBZ= 0;
				yjs.json.YBJS_DBBZJJ= 0;
				yjs.json.YBJS_ECBZJJ= 0;
				yjs.YBJS_GSBXJJ= 0;
				yjs.json.YBJS_CXSYBXJJ= 0;
				yjs.json.YBJS_XJ= 0;
				yjs.json.YBJS_JMTCJJ= 0;
				yjs.json.YBJS_CXJMDBBZ= 0;
				yjs.json.YBJS_NHTCJJ= 0;
				yjs.json.YBJS_YYZF= 0;
            	$("#ybcyButton")[0].setAttribute("disabled", true);
    			$("#ybcyButton")[0].style.background = '#aaaaaa';
    			yjs.error = "结算中，请不要进行其他操作....";
            	var message= [
            	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
            	              	['readtaccode','yes',left_tab1.qhybCs.ybzxbh,left_tab1.qhybCs.tqcbh,left_tab1.qhybCs.yyjb],
            	              	['start','','BIZC131256'],
            	              	// 固定参数
            	              	['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
            	              	['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
            	              	['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],
            	              	// 入参
            	              	['putcol','','save_flag','3'],
            	              	['putcol','','hospital_id',yjs.json.HOSPITAL_ID],
            	              	['putcol','','serial_no',yjs.json.SERIAL_NO],
            	              	['putcol','','indi_id',yjs.json.INDI_ID],
            	              	['putcol','','last_balance',null],
            	              	
            	              	['putcol','','end_disease',yjs.json.end_disease],
            	              	['putcol','','end_disease_name',yjs.json.end_disease_name],
            	              	
            	              	['putcol','','end_date',toolMenu.fDate(yjs.json.BQCYRQ,'YY')],
            	              	['putcol','','fin_disease1',null],
            	              	['putcol','','fin_disease2',null],
            	              	['putcol','','fin_info',yjs.json.fin_info],
            	              	['putcol','','staff_id',yjs.json.REG_STAFF],
            	              	['putcol','','staff_name',yjs.json.REG_MAN],
            	              	['putcol','','treatment_type',yjs.json.TREATMENT_TYPE],
            	              	['putcol','','bill_no',null],
            	              	['putcol','','reg_flag',null],
            	              	['putcol','','reg_info',null],
            	              	['putcol','','serial_apply',null],
            	              	['putcol','','fetus',null],
            	              	['putcol','','fact_idcard',yjs.json.FACT_IDCARD],
            	              	//['putcol','','tac_code',null],
            	              	['putcol','','cure_mehtod',null],
            	              	
            	              	['run',''],
            	              	
            	              	['setresultset','','payinfo'],
            	              	
            	              	// 获取结果集数据结构
            	              	['getlist',
            	              	 
            	              	 	[
										['getbyname','','fund_id',''],
										['getbyname','','fund_name',''],
										['getbyname','','real_pay','']
            	              	 	 ]
	            	              	
            	              	],
            	              	
            	              	
	              	            // 业务申请信息
	              	            ['setresultset','','bizinfo'],
	              	            ['getbyname','','serial_no',''],
	              	            ['getbyname','','bill_no','']
          	              	
            	              	//['destoryinterface','']
            	              ];
    						console.log(message);
    						if (socket.readyState===1) {
    		                    socket.send(JSON.stringify(message));
    		                    var cs=0;
    		        			var interval=setInterval(function(){
    		        					cs+=1;
    		        					console.log(cs);
    		            				if(rs){
    		                				if(ifok){
    		                					console.log(rslmsg)
    		                					if(rslmsg.run > 0 && rslmsg.code=="success" && rslmsg.data.serial_no != ''){
    	                        						for(var i=0;i<rslmsg.getlist.length;i++){
    	                        							var id = rslmsg.getlist[i].fund_id;
    	                        							var money = rslmsg.getlist[i].real_pay;
    	                        							switch(id){
    	                        								case '001':
    	                        									yjs.json.YBJS_TCJJ += parseFloat(money);
    	                        									break;
    		                        							case '003':
    		                        								yjs.json.YBJS_GRZH += parseFloat(money);
    	                        									break;
    		                        							case '005':
    		                        								yjs.json.YBJS_JTZHJJ += parseFloat(money);
    	                        									break;
    		                        							case '201':
    		                        								yjs.json.YBJS_DBHZJJ += parseFloat(money);
    	                        									break;
    		                        							case '202':
    		                        								yjs.json.YBJS_LXJJ += parseFloat(money);
    	                        									break;
    		                        							case '301':
    		                        								yjs.json.YBJS_GWYBZ += parseFloat(money);
    	                        									break;
    		                        							case '401':
    		                        								yjs.json.YBJS_DBBZJJ += parseFloat(money);
    	                        									break;
    		                        							case '402':
    		                        								yjs.json.YBJS_ECBZJJ += parseFloat(money);
    	                        									break;
    		                        							case '501':
    		                        								yjs.json.YBJS_GSBXJJ += parseFloat(money);
    	                        									break;
    		                        							case '511':
    		                        								yjs.json.YBJS_CXSYBXJJ += parseFloat(money);
    	                        									break;
    		                        							case '999':
    		                        								yjs.json.YBJS_XJ += parseFloat(money);
    	                        									break;
    		                        							case '801':
    		                        								yjs.json.YBJS_JMTCJJ += parseFloat(money);
    	                        									break;
    		                        							case '803':
    		                        								yjs.json.YBJS_CXJMDBBZ += parseFloat(money);
    	                        									break;
    		                        							case '805':
    		                        								yjs.json.YBJS_NHTCJJ += parseFloat(money);
    	                        									break;
    		                        							case '996':
    		                        								yjs.json.YBJS_YYZF += parseFloat(money);
    	                        									break;
    	                        							}
    	                        						}
    	                        						$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=inhospital&method=ybjs&parm="
    	    		                                            + JSON.stringify(yjs.json),
    	    		                                            function (json) {
    	    		                                                if (json.a == 0) {
    	    		                                                	yjs.error = "入院结算成功！"+json.c;
    	    		                                                	malert("入院结算成功！"+json.c);
    	    		                                                	yjs.getAccount();
    	    		                                                	left_tab1.getBrData();
    	    		                                                	left_tab1.rydjData.ISJS = '1';
    	    		                                                } else {
    	    		                                                    malert("His内部错误，请从新办理:"+json.c);
    	    		                                                }
    	    		                                            });			
    		                					}else{
    		                						yjs.error = rslmsg.error;
    		                						malert("医保已出院或者 "+rslmsg.error);
    		                					}
    		                					
    		                					clearInterval(interval);
    		                					rs=false;
    		                					ifok=false;
    		                				}else{
    		                					clearInterval(interval);
    		                					rs=false;
    		                					ifok=false;
    		                				}
    		                			}
    		            				if(cs>=20){
    		            					malert("医保超时,请重试！ ");
    		            					rs=false;
    		        	    				ifok=false;
    		            					clearInterval(interval);
    		            				}
    		            			},1500);
    		                }else{
    		                	yjs.error = "医保通信失败！请刷新页面再试 ";
    		                	malert("医保通信失败！ ");
    		                }
    						setTimeout(function () {
    		        			$("#ybcyButton")[0].removeAttribute("disabled");
    		                  	$("#ybcyButton")[0].style.background = '';
    		                }, 40000);
            },
            qxybcy: function () {
            	if(menu.qhyb_conn != 'onopen'){
        			malert("医保连接失败,请刷新页面重试！");
        			return;
        		}
            	if(left_tab1.rydjData.ZYH == null){
         			malert("请选择患者！");
         			return;
         		}
        		if(left_tab1.rydjData.SERIAL_NO == null){
        			malert("该患者还未入院登记！");
        			return;
        		}
            	if(left_tab1.rydjData.ISJS != '1'){
            		malert("该病人未办理医保结算");
            		return
            	}
            	$.ajaxSettings.async = false;
            	var message= [
            	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
            	              	['start','','BIZC131259'],
            	              	// 固定参数
            	              	['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
            	              	['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
            	              	['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],
            	              	// 入参
            	              	['putcol','','save_flag','3'],
            	              	['putcol','','hospital_id',left_tab1.rydjData.HOSPITAL_ID],
            	              	['putcol','','serial_no',left_tab1.rydjData.SERIAL_NO],
            	              	['putcol','','treatment_type',left_tab1.rydjData.TREATMENT_TYPE],
            	              	['putcol','','bill_no',left_tab1.rydjData.BILL_NO],
            	              	['putcol','','indi_id',left_tab1.rydjData.INDI_ID],
            	              
            	              	['run','']
          	              	
            	              	//['destoryinterface','']
            	              ];
    						console.log(message);
    						if (socket.readyState===1) {
    		                    socket.send(JSON.stringify(message));
    		                    var cs=0;
    		        			var interval=setInterval(function(){
    		        					cs+=1;
    		        					console.log(cs);
    		            				if(rs){
    		                				if(ifok){
    		                					console.log(rslmsg);
    		                					if(rslmsg.run > 0 && rslmsg.code=="success"){
    		                							$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=inhospital&method=qxybjs&parm="
    		                       	                             + JSON.stringify(left_tab1.rydjData),
    		                       	                             function (json) {
    		                       	                                 if (json.a == 0) {
    		                       	                                	malert("取消出院结算成功！");
    		                       	                                	left_tab1.rydjData = {};
    		                       	                                	left_tab1.getBrData();
    		                       	                                 } else {
    		                       	                                     malert("His内部错误， "+json.c);
    		                       	                                 }
    		                       	                             });
    		                					}else{
    		                						malert("上传费用失败！ "+rslmsg.error);
    		                					}
    		                					
    		                					clearInterval(interval);
    		                					rs=false;
    		                					ifok=false;
    		                				}else{
    		                					clearInterval(interval);
    		                					rs=false;
    		                					ifok=false;
    		                				}
    		                			}
    		            				if(cs>=10){
    		            					malert("医保超时,请重试！ ");
    		            					rs=false;
    		        	    				ifok=false;
    		            					clearInterval(interval);
    		            				}
    		            			},left_tab1.socketTime);
    		                }else{
    		                	malert("医保通信失败！ ");
    		                }	
            },
        	printJs:function(){
        		/*if(left_tab1.rydjData.ZYH == null){
         			malert("请选择患者！");
         			return;
         		}
        		if(left_tab1.rydjData.SERIAL_NO == null){
        			malert("该患者还未入院登记！");
        			return;
        		}
        		if(left_tab1.rydjData.ISJS != '1'){
        			malert("该病人未办理结算，无法打印!");
        			return
        		}*/
        		
        		var message= [		
          	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
          	              	['start','','BIZC200101'],
          	              	// 固定参数
          	                ['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
        	              		['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
        	              		['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],
          	              	// 入参
          	              	['putcol','','hospital_id',left_tab1.rydjData.HOSPITAL_ID],
          	              	['putcol','','serial_no',left_tab1.rydjData.SERIAL_NO],
          	              	['run',''],
          	              	['setresultset','','info'],
          	              	['getbyname','','indi_id',''],
          	              	['getbyname','','name'],
          	              	['getbyname','','sex'],
          	              	['getbyname','','birthday'],
          	              	['getbyname','','ic_no'],
          	              	['getbyname','','idcard'],
          	              	['getbyname','','pers_name'],
          	              	['getbyname','','office_grade'],
          	              	['getbyname','','Official_name'],
          	              	['getbyname','','patient_id'],
          	              	['getbyname','','hospital_name'],
          	              	['getbyname','','hosp_level_name'],
          	              	['getbyname','','hosp_grade_name'],
          	              	['getbyname','','corp_name'],
          	              	['getbyname','','in_disease'],
          	              	['getbyname','','fin_disease'],
          	              	['getbyname','','begin_date'],
          	              	['getbyname','','end_date'],
          	              	['getbyname','','days'],
          	              	['getbyname','','in_dept_name'],
          	              	['getbyname','','treatment_type'],
          	              	['getbyname','','treatment_name'],
          	              	['getbyname','','injury_borth_sn'],
          	              	
          	              	['setresultset','','fee'],
          	              	['getbyname','','stat_type'],
          	              	['getbyname','','stat_name'],
          	              	['getbyname','','zfy'],
          	              	['getbyname','','qzf'],
          	              	['getbyname','','blzf'],
          	              	
          	              	['getbyname','','seg'],
          	              	['getbyname','','policy_type'],
          	              	['getbyname','','total_pay'],
          	              	['getbyname','','cash_pay'],
          	              	['getbyname','','acct_pay'],
          	              	['getbyname','','base_pay'],
          	              	['getbyname','','additional_pay'],
          	              	['getbyname','','official_pay'],
          	              	['getbyname','','hosp_pay'],
          	              	['getbyname','','corp_pay'],
          	              	['getbyname','','zhaogu_pay'],
          	              	['getbyname','','offi_tsbt'],
          	              	['getbyname','','fund402_pay'],
          	              	['getbyname','','limit'],
          	              	['getbyname','','major_disease'],
          	              	['getbyname','','dbbz_qfx'],
          	              	['getbyname','','difference_pay'],
          	              	
          	              	/*['setresultset','','seg'],
          	              	['setresultset','','total_pay'],
          	              	['setresultset','','self_pay'],
          	              	['setresultset','','policy_self_pay'],
          	              	['setresultset','','seg_self_pay'],
          	              	['setresultset','','fund_pay'],
          	              	['setresultset','','hosp_pay'],*/
          	              	
          	              	['setresultset','','fund'],
          	              	['getbyname','','total_pay'],
          	              	['getbyname','','fund_pay'],
          	              	['getbyname','','db_pay'],
          	              	['getbyname','','self_pay'],
          	              	['getbyname','','Part_pay'],
          	              	['getbyname','','part_pay_offi'],
          	              	['getbyname','','start_pay'],
          	              	['getbyname','','start_pay_offi'],
          	              	['getbyname','','base_pay'],
          	              	['getbyname','','self_pay_seg'],
          	              	['getbyname','','official_pay_seg'],
          	              	['getbyname','','additional_pay'],
          	              	['getbyname','','additional_pay_cash'],
          	              	['getbyname','','additional_pay_offi'],
          	              	['getbyname','','declare_pay'],
          	              	['getbyname','','self_pay_exceed']
          	              ];
    			
    		
    		console.log(message);
    		if (socket.readyState===1) {
                socket.send(JSON.stringify(message));
                var cs=0;
    			var interval=setInterval(function(){
    					cs+=1;
    					console.log(cs);
        				if(rs){
            				if(ifok){
            					console.log(rslmsg);
            					if(rslmsg.run >= 0 && rslmsg.code == 'success'){
            						yjsprint.ybData = rslmsg.data;
            						yjsprint.ybData.corp_name = rslmsg.data.corp_name.substring(0,9);
            						
            						yjsprint.hisData = left_tab1.rydjData;
            						yjsprint.hisData.JSRQ = toolMenu.fDate(yjsprint.hisData.JSRQ,'date');
            						setTimeout(function () {
            							yjsprint.yjsShow=true;
                		                window.print();
                		                setTimeout(function () {
                		                    yjsprint.yjsShow=false;
                		                },500);
            		                },500);
            						
            					}else{
            						malert("查询结算信息： "+rslmsg.error);
            					}
            					clearInterval(interval);
            					rs=false;
            					ifok=false;
            				}else{
            					clearInterval(interval);
            					rs=false;
            					ifok=false;
            				}
            			}
        				if(cs>=10){
        					malert("医保超时,请重试！ ");
        					rs=false;
    	    				ifok=false;
        					clearInterval(interval);
        				}
        			},left_tab1.socketTime);
            }else{
            	malert("医保通信失败！ ");
            }
        		
                
        	},
            
             changeDown1: function (event, type) {
                 if (this['searchCon1'][this.selSearch] == undefined) return;
                 this.keyCodeFunction(event, 'jbContent1', 'searchCon1');
                 if (event.code == 'Enter' || event.code == 13) {
                     if (type == "text") {
                         Vue.set(this.jbContent1, 'jbmc1', this.jbContent1['yke121']);
                         yjs.rcContent.prm_ykd018 = this.jbContent1.yke120;
                         this.selSearch=0;
                         this.nextFocus(event);
                     }
                 }
             },
             changeDown2: function (event, type) {
                 if (this['searchCon2'][this.selSearch] == undefined) return;
                 this.keyCodeFunction(event, 'jbContent2', 'searchCon2');
                 if (event.code == 'Enter' || event.code == 13) {
                     if (type == "text") {
                         Vue.set(this.jbContent2, 'jbmc2', this.jbContent2['yke121']);
                         yjs.rcContent.prm_ykd019 = this.jbContent2.yke120;
                         this.selSearch=0;
                         this.nextFocus(event);
                     }
                 }
             },
             changeDown3: function (event, type) {
                 if (this['searchCon3'][this.selSearch] == undefined) return;
                 this.keyCodeFunction(event, 'jbContent3', 'searchCon3');
                 if (event.code == 'Enter' || event.code == 13) {
                     if (type == "text") {
                         Vue.set(this.jbContent3, 'jbmc3', this.jbContent3['yke121']);
                         yjs.rcContent.prm_ykd020 = this.jbContent3.yke120;
                         this.selSearch=0;
                         this.nextFocus(event);
                     }
                 }
             },

             searching: function (add, type) {
                 if (!add) this.page.page = 1;
                 var _searchEvent = $(event.target.nextElementSibling).eq(0);
                 if (this.jbContent[type] == undefined || this.jbContent[type] == null) {
                     this.page.parm = "";
                 } else {
                     this.page.parm = this.jbContent[type];
                 }
                 var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=inhospital&method=queryJbxx&parm="
                     + JSON.stringify(str_param),
                     function (json) {
                         if (json.a == 0) {
                             var date = null;
                             var res = eval('(' + json.d + ')');
                             console.log(res)
                             if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                 for (var i = 0; i < res.list.length; i++) {
                                	 yjs.searchCon.push(res.list[i]);
                                 }
                             } else {
                            	 yjs.searchCon = res.list;
                             }
                             yjs.page.total = res.total;
                             yjs.selSearch = 0;
                             if (res.list.length > 0 && !add) {
                                 $(".selectGroup").hide();
                                 _searchEvent.show();
                             }
                         } else {
                             malert("查询失败  " + json.c);
                         }
                     });
             },
             searching1: function (add, type) {
                 if (!add) this.page.page = 1;
                 var _searchEvent = $(event.target.nextElementSibling).eq(0);
                 if (this.jbContent1[type] == undefined || this.jbContent1[type] == null) {
                     this.page.parm = "";
                 } else {
                     this.page.parm = this.jbContent1[type];
                 }
                 var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=ICD10&method=query&parm="
                     + JSON.stringify(str_param),
                     function (json) {
                         if (json.a == 0) {
                             var date = null;
                             var res = eval('(' + json.d + ')');
                             if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                 for (var i = 0; i < res.list.length; i++) {
                                	 yjs.searchCon1.push(res.list[i]);
                                 }
                             } else {
                            	 yjs.searchCon1 = res.list;
                             }
                             yjs.page.total = res.total;
                             yjs.selSearch = 0;
                             if (res.list.length > 0 && !add) {
                                 $(".selectGroup").hide();
                                 _searchEvent.show();
                             }
                         } else {
                             malert("查询失败  " + json.c);
                         }
                     });
             },
             searching2: function (add, type) {
                 if (!add) this.page.page = 1;
                 var _searchEvent = $(event.target.nextElementSibling).eq(0);
                 if (this.jbContent2[type] == undefined || this.jbContent2[type] == null) {
                     this.page.parm = "";
                 } else {
                     this.page.parm = this.jbContent2[type];
                 }
                 var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=ICD10&method=query&parm="
                     + JSON.stringify(str_param),
                     function (json) {
                         if (json.a == 0) {
                             var date = null;
                             var res = eval('(' + json.d + ')');
                             if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                 for (var i = 0; i < res.list.length; i++) {
                                	 yjs.searchCon2.push(res.list[i]);
                                 }
                             } else {
                            	 yjs.searchCon2 = res.list;
                             }
                             yjs.page.total = res.total;
                             yjs.selSearch = 0;
                             if (res.list.length > 0 && !add) {
                                 $(".selectGroup").hide();
                                 _searchEvent.show();
                             }
                         } else {
                             malert("查询失败  " + json.c);
                         }
                     });
             },
             searching3: function (add, type) {
                 if (!add) this.page.page = 1;
                 var _searchEvent = $(event.target.nextElementSibling).eq(0);
                 if (this.jbContent3[type] == undefined || this.jbContent3[type] == null) {
                     this.page.parm = "";
                 } else {
                     this.page.parm = this.jbContent3[type];
                 }
                 var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=ICD10&method=query&parm="
                     + JSON.stringify(str_param),
                     function (json) {
                         if (json.a == 0) {
                             var date = null;
                             var res = eval('(' + json.d + ')');
                             if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                                 for (var i = 0; i < res.list.length; i++) {
                                	 yjs.searchCon3.push(res.list[i]);
                                 }
                             } else {
                            	 yjs.searchCon3 = res.list;
                             }
                             yjs.page.total = res.total;
                             yjs.selSearch = 0;
                             if (res.list.length > 0 && !add) {
                                 $(".selectGroup").hide();
                                 _searchEvent.show();
                             }
                         } else {
                             malert("查询失败  " + json.c);
                         }
                     });
             },
             selectOne: function (item) {
                 if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                     this.page.page++;               // 设置当前页号
                     this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
                 } else {   // 否则就是选中事件,为json赋值
                     this.jbContent = item;
                     Vue.set(this.jbContent, 'jbmc', this.jbContent['JBMC']);
                     yjs.json.end_disease = this.jbContent.JBMB;
                     yjs.json.end_disease_name = this.jbContent.JBMC;
                     $(".selectGroup").hide();
                 }
             },
             changeDown: function (event, type) {
                 if (this['searchCon'][this.selSearch] == undefined) return;
                 this.keyCodeFunction(event, 'jbContent', 'searchCon');
                 if (event.code == 'Enter' || event.code == 13) {
                     if (type == "text") {
                         Vue.set(this.jbContent, 'jbmc', this.jbContent['JBMC']);
                         yjs.json.end_disease = this.jbContent.JBMB;
                         yjs.json.end_disease_name = this.jbContent.JBMC;
                         this.selSearch=0;
                         this.nextFocus(event);
                     }
                 }
             },
             selectOne1: function (item) {
                 if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                     this.page.page++;               // 设置当前页号
                     this.searching1(true, 'jbmc1');           // 传参表示请求下一页,不传就表示请求第一页
                 } else {   // 否则就是选中事件,为json赋值
                     this.jbContent = item;
                     Vue.set(this.jbContent1, 'jbmc1', this.jbContent1['yke121']);
                     yjs.rcContent.prm_ykd018 = this.jbContent1.yke120;
                     $(".selectGroup").hide();
                 }
             },
             selectOne2: function (item) {
                 if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                     this.page.page++;               // 设置当前页号
                     this.searching2(true, 'jbmc2');           // 传参表示请求下一页,不传就表示请求第一页
                 } else {   // 否则就是选中事件,为json赋值
                     this.jbContent = item;
                     Vue.set(this.jbContent2, 'jbmc2', this.jbContent2['yke121']);
                     yjs.rcContent.prm_ykd019 = this.jbContent2.yke120;
                     $(".selectGroup").hide();
                 }
             },
             selectOne3: function (item) {
                 if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                     this.page.page++;               // 设置当前页号
                     this.searching3(true, 'jbmc3');           // 传参表示请求下一页,不传就表示请求第一页
                 } else {   // 否则就是选中事件,为json赋值
                     this.jbContent = item;
                     Vue.set(this.jbContent3, 'jbmc3', this.jbContent['yke121']);
                     yjs.rcContent.prm_ykd020 = this.jbContent3.yke120;
                     $(".selectGroup").hide();
                 }
             },

        }
    })
    yjs.getData();
    
    $(document).click(function () {
        if (this.className != 'selectGroup') {
            $(".selectGroup").hide();
        }
        $(".popInfo ul").hide();
    });
//住院结算打印
var yjsprint = new Vue({
    el: '.yjsprint',
    data: {
        isShow: false,
        yjsShow:false,//青海医保结算打印
        ybData :{},
        hisData :left_tab1.rydjData
    },

    methods: {

    }
});