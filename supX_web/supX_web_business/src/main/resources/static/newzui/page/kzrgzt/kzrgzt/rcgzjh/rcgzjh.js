(function () {

    //顶部工具栏
    var tool=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
            zt:'1',
            popContent:{},
            YearList:[],
            monthList:[]
        },
        methods:{
            //新增计划
            AddModel:function () {

            },
            //刷新和检索
            getData:function () {

            },
            //获取年
            getYearData: function(){
                var y = new Date().getFullYear();
                var yearArry=[];
                for(var i=(y-100);i < (y+1) ;i++){
                 yearArry.push({'year':i,'yrbm':i});
                }

                tool.YearList=yearArry;
                var month=new Date().getMonth()+1;
                var monthArry=[];
                for(var i=1;i<=12;i++){
                    monthArry.push({'month':i,'mbm':i});
                }
                tool.monthList=monthArry;
                console.log(monthArry);
            },

            //选择年
            resultChanges:function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "yrbm":
                        Vue.set(this.popContent, 'yrbm', val[0]);
                        Vue.set(this.popContent, 'year', val[4]);
                        break;
                    case "mbm":
                        Vue.set(this.popContent, 'mbm', val[0]);
                        Vue.set(this.popContent, 'month', val[4]);
                        break;
                    default:
                        break;
                }
            }

        }
    });

    //管理小组列表
    var wxdjList = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data: {

        },

        updated:function () {
            changeWin()
        },
        methods: {
            //请求后台查询列表信息
            getData : function(){
                //加载动画效果
                common.openloading('.zui-table-view')
                //数据请求结束时关闭


                common.closeLoading()
            },


            //删除
            remove:function () {
                if (common.openConfirm("确认删除该条信息吗？", function () {


                    })) {
                    return false;
                }
            }
        }
    });

    tool.getYearData();

    laydate.render({
        elem: '.todate',
        type: 'datetime',
        trigger: 'click',
        theme: '#1ab394',
        done: function (value, data) {
        }
    });
})();





