<div id="qhtfsn" class="flex-container flex-dir-c">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary" @click="loadCard()" >读卡</button>
            <!--<button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="">清空</button>-->
            <button class="tong-btn btn-parmary-b" @click="loadBx()">引入</button>
            <!--qxjstest()-->
            <button class="tong-btn btn-parmary-b" @click="qxjstest()">取消结算</button>
        </div>
    </div>
<div class="over-auto">
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">卡片</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">卡类型</label>
                    <select-input  @change-data="resultChange"
                                   :not_empty="false"
                                   :child="klxList"
                                   :val="klxIndex"
                                   :name="'klxIndex'">
                    </select-input>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">密码</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="true" class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="ybkpassword" type="password">
                    </div>
                </div>
                <div class="zui-inline col-xxl-2">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">使用慢特病报销</label>
                    <select-input @change-data="mzbbzResultChange" 
                        :not_empty="true" 
                        :child="mxbpd" 
                        :index="'mxbbzmc'"
                        :index_val="'mxbbz'" 
                        :val="cbryxxContent.mxbbz" 
                        :name="cbryxxContent.mxbbzmc" 
                        :search="false" 
                        >
                    </select-input>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">录入信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">疾病诊断</label>
                    <div class="zui-input-inline">
                    <input class="zui-input" v-model="jbContent.jbmc" @input="searching(false,'jbmc', $event.target.value)"
                                       @keyDown="changeDown($event,'text')" id="jbmc">
                    <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="page"
                                              @click-one="checkedOneOut" @click-two="selectOne" :not_empty="true">
                    </search-table>  
                    </div>
                </div>
                <div class="zui-inline col-xxl-6">
                    <label class="zui-form-label">病情描述</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.qtsm">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">医疗类别</label>
                    <select-input  @change-data="resultChange"
                                   :not_empty="false"
                                   :child="snqhtf_yllbMz"
                                   :val="cbryxxContent.yllbMz"
                                   :name="'cbryxxContent.yllbMz'"
                                   >
                    </select-input>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">病人基本信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">个人编号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.grbh">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">身份证号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.sfzh">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">姓名</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.xm">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">性别</label>
                    <select-input  :not_empty="true"
                                   @change-data="resultChange"
                                   :not_empty="false"
                                   :child="brxb_tran"
                                   :val="cbryxxContent.xb"
                                   :name="'cbryxxContent.xb'"
                                   :disable="true">
                    </select-input>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">参工日期</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.cgrq">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">出生日期</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.csrq">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">个人账户余&emsp;&emsp;额</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="true" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.grzhye">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">缴费年限</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.jfnx">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">参保机构</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.cbjgmc">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">单位保管码</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.cbdwglm">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">贫困人员标&emsp;&emsp;志</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"   v-model="cbryxxContent.pkbz">
                    </div>
                </div>
                <div class="zui-inline col-xxl-6">
                    <label class="zui-form-label">单位名称</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.dwmc">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">年内住院次数</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.nnzycs">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3" style="padding-left: 85px;">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;width: 85px;">慢性特殊疾病可报金额</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.mxtsjbkbje">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">住院补充待遇状态</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"   v-model="cbryxxContent.zybcdyzt">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">大额待遇状&emsp;&emsp;态</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.dedyzt">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">公务员待遇状态</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.gwydyzt">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3" style="padding-left: 85px;">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;width: 85px;">年内基本医疗已报金额</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.nnjbylybje">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">年内住院总费用</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.nnzfy">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">年内医保实际支付</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.nnybsjzf">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">年内基本已支付</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.nnjbyzf">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">年内住院补充已支</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.nnzybcyzf">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">年内大额已支付</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)" v-model="cbryxxContent.nndeyzf">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">医疗类别门&emsp;&emsp;诊</label>
                    <select-input  :not_empty="true"
                                   @change-data="resultChange"
                                   :not_empty="false"
                                   :child="brxb_tran"
                                   :val="cbryxxContent.yllbMz"
                                   :name="'cbryxxContent.yllbMz'"
                                   :disable="true">
                    </select-input>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">提取日期</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.tqrq">
                    </div>
                </div>
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">医保卡号</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.ybkh">
                    </div>
                </div>
                <div class="zui-inline col-xxl-12">
                    <label class="zui-form-label" style="line-height: 1; padding: 2px 10px 2px 0px;">医保卡信息</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" disabled class="zui-input"
                               @keydown.enter="nextFocus($event)"  v-model="cbryxxContent.ybkxx">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title">慢特病信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline">
                        <input data-notEmpty="false" class="zui-input"
                               @keydown.enter="nextFocus($event)">
                    </div>
                </div>
            </div>
            <div class="zui-table-view">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                    </div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>病种类别</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>疾病代码</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>疾病名称</span></div>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>

                <div class="zui-table-body" @scroll="scrollTable($event)" data-no-change style="height: 200px;padding-bottom: 20px;">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr  @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index" v-for="(item, $index) in mxblist">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'brListCheckBox'"
                                                    :type="'one'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td >
                                <div class="zui-table-cell cell-s">{{item.bzlb}}</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">{{item.bxbm}}</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">{{item.bxmc}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<script type="application/javascript" src="./insurancePort/003qhtfsn/003qhtfsn.js"></script>