<div id="kswh">
    <div class="flex-container flex-align-c padd-b-10 padd-l-10 padd-r-10">
        <button class="tong-btn btn-parmary" @click="getData"><span class="fa fa-refresh"></span>刷新</button>
        <button class="tong-btn btn-parmary" @click="addData"><span class="fa fa-plus"></span>新增</button>
        <button class="tong-btn btn-parmary" @click="edit()"><span class="fa fa-edit"></span>修改</button>
        <button class="tong-btn btn-parmary" @click="remove"><span class="fa fa-trash-o"></span>删除</button>
    </div>
    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">序号</div>
                    </th>
                    <th class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">操作员编码</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">操作员名称</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">农合账号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">农合密码</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">保险类别名称</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">保险类别编码</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one over-auto" @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr @mouseenter="hoverMouse(true,$index)" @click="checkOne($index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">{{$index+1}}</div>
                    </td>
                    <td class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.czybm}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.czymc}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.nhczy}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.password}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.bxlbmc}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.bxlbbm}}</div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>

<!--弹出框-->
<div id="pop" v-cloak class="czydmPop">
    <transition name="pop-fade">
        <div v-show="isShow" class="pop-open">
            <div class="pophide show"></div>
            <div class=" podrag  show bcsz-layer padd-b-15" style="width: auto">
                <div class="layui-layer-title ">个人资料</div>
                <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close"
                                                    @click="isShow=false"></a></span>
                <div class="layui-layer-content">
                    <div class=" layui-mad latui-mad layui-height">
                        <div class="flex-container flex-align-c padd-b-10">
                            <div class="flex-container flex-align-c  padd-r-10">
                                <span class="padd-r-5">人员名称</span>
                                <select-input @change-data="resultChange" :not_empty="true" class="wh180"
                                              :child="rybmList" :index="'ryxm'" :index_val="'rybm'"
                                              :val="popContent.czybm"
                                              :name="'popContent.czybm'" :search="true">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c padd-r-10">
                                <span class="padd-r-5">农合账号</span>
                                <input class="zui-input wh180" v-model="popContent.nhczy" @keydown="nextFocus($event)"
                                       type="text">
                            </div>
                        </div>
                        <div class="flex-container flex-align-c padd-b-10">
                            <div class="flex-container flex-align-c padd-r-10">
                                <span class="padd-r-5">农合密码</span>
                                <input class="zui-input wh180" v-model="popContent.password"
                                       @keydown="nextFocus($event)"  type="text">
                            </div>
                            <div class="flex-container flex-align-c padd-r-10">
                                <span class="padd-r-5">农合账号</span>
                                <select-input @change-data="resultChange" :not_empty="false" class=" wh180"
                                              :child="jkList" :index="'zymc'" :index_val="'zybm'" :val="bxContent.bxbm"
                                              :name="'bxContent.bxbm'">
                                </select-input>
                            </div>
                        </div>
                    </div>
                    <div class="popDoBtu flex-container flex-jus-e">
                        <button class="tong-btn btn-parmary" @click="saveData" ><span class="fa fa-save"></span>保存</button>
                        <button class="tong-btn btn-parmary cancel" @click="isShow = false" ><span class="fa fa-close"></span>取消</button>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</div>

</body>
<script type="text/javascript" src="czydm.js"></script>
</html>
