<div id="yb" class="contextInfo background-f">
    <div class="wrapper">
        <div class="panel" >
            <div style="float: left;width: 66%">
                <div class="tong-top">
                    <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
                    <button class="tong-btn btn-parmary-b"  @click="nhyjs"><i class="iconfont icon-iocn26 icon-c1"></i>预结算</button>
                    <button class="tong-btn btn-parmary-b paddr-r5" @click="nhzsjs"><i class=" iconfont icon-iocn40 icon-c1 "></i>正式结算</button>
                    <button class="tong-btn btn-parmary-b  paddr-r5" @click="qxjs"><i class="iconfont icon-iocn22 icon-c1"></i>取消结算</button>
                    <button class="tong-btn btn-parmary-b  paddr-r5" @click="cydj" ><i class="iconfont icon-iocn22 icon-c1"></i>出院登记</button>
                    <button class="tong-btn btn-parmary-b  paddr-r5" @click="qxcy" ><i class="iconfont icon-iocn22 icon-c1"></i>取消出院</button>
                    <!--<button class="tong-btn btn-parmary-b  paddr-r5" @click="dybcd" ><i class="iconfont icon-iocn22 icon-c1"></i>打印补偿单</button>-->
                </div>
            </div>
            <div style="float: right;width: 34%;padding-top: 12px;">
                <span class="commom_zao commom_zao-color-1abc9c position color-1a" style="font-size: 22px" v-if="popContent.ifjs=='1'" >{{ybc_status}}</span>
                <span style="font-size: 22px;color: #f2a654" v-if="popContent.ifjs!='1' && popContent.ifleave=='1'">{{dbc_status}}</span>
                <span style="font-size: 22px" v-if="popContent.ifjs!='1' && popContent.ifleave!='1'">{{zy_status}}</span>
            </div>
        </div>
    </div>
    <div class="tab-card panel printHide" id="nhjs_search">
        <div class="tab-card-header">
            <div class="tab-card-header-title">检索信息</div>
        </div>
        <div class="tab-card-body">
            <div class="zui-form grid-box">
                <div class="zui-inline col-xxl-3 padd-l-40">
                    <label class="zui-form-label " style="width: 40px;padding-right: 7px">检索</label>
                    <input class="zui-input" data-notEmpty="false" :value="brxxContent.text" @keydown="changeDown($event,'text','brxxContent','searchCon')"
                           @input="change(false,'text',$event.target.value)"/>
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </div>
            </div>
        </div>
    </div>
    <div class="ybjs padd-l-10 padd-r-10">
        <div class="jbxx margin-b-15">
            <div class="jbxx-size">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">入院信息</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <div class="jbxx-box flex-start padd-t-10 padd-b-10 padd-l-10 padd-r-10">
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">住院号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.zyh"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">单据流水号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.inpatientSn"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">病人姓名</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.brxm"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">性&ensp;&ensp;&ensp;&ensp;别</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                          :child="brxb_tran" :index="popContent.hisBrjbxx.brxb" :val="popContent.hisBrjbxx.brxb"
                                          :name="'popContent.hisBrjbxx.brxb'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">床位号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.bedNo"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">个人编号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" readonly="readonly" data-notempty="true" v-model="popContent.hisBrjbxx.memberSysno"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">家庭编号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" readonly="readonly" data-notempty="true" v-model="popContent.hisBrjbxx.familySysno"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">入院科室</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" readonly="readonly" v-model="popContent.hisBrjbxx.ryksmc"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">出院院科室</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="true"
                                          :child="cyks_tran" :index="popContent.hisBrjbxx.outOfficeId" :val="popContent.hisBrjbxx.outOfficeId"
                                          :name="'popContent.hisBrjbxx.outOfficeId'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">就诊类型</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                          :child="jzlx_tran" :index="popContent.hisBrjbxx.cureId" :val="popContent.hisBrjbxx.cureId"
                                          :name="'popContent.hisBrjbxx.cureId'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">入院诊断</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" readonly="readonly" v-model="popContent.hisBrjbxx.icdName"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">出院日期</label>
                        <div class="top-zinle">
                            <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                            <input type="text" class="zui-input background-f times1 text-indent-20" readonly="readonly" data-notEmpty="true" v-model="popContent.hisBrjbxx.bqcyrq"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">出院情况</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="commonResultChange" readonly="readonly" :not_empty="true"
                                          :child="cyzt_tran" :index="popContent.hisBrjbxx.outHosId" :val="popContent.hisBrjbxx.outHosId"
                                          :name="'popContent.hisBrjbxx.outHosId'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">出院诊断</label>
                        <div class="top-zinle">
                            <input class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.outIcdName" @keydown="changeDown($event,'jbbm','jbbmContent','jbsearchCon')"
                                    data-notempty="true"
                                   @input="change(false,'jbbm',$event.target.value)">
                            <jbsearch-table :message="jbsearchCon" :selected="selSearch"
                                            :them="jbthem" :page="page"
                                            @click-one="checkedOneOut" @click-two="selectJbbm">
                            </jbsearch-table>
                        </div>
                    </div>
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">出院诊断2</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">出院诊断3</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">出院诊断4</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">出院诊断5</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->

                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">治疗方式</label>
                        <div class="top-zinle">
                            <input class="zui-input wh182 background-f"  v-model="popContent.hisBrjbxx.treatName" @keydown="changeDown($event,'zlfs','zlfsContent','zlfsSearchCon')" @input="change(false,'zlfs',$event.target.value)">
                            <zlfssearch-table :message="zlfsSearchCon" :selected="selSearch"
                                              :them="zlfsthem" :page="page"
                                              @click-one="checkedOneOut" @click-two="selectZlfs">
                            </zlfssearch-table>
                        </div>
                    </div>
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">治疗方式2</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">治疗方式3</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">治疗方式4</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">治疗方式5</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">补偿类型</label>
                        <div class="top-zinle">
                            <select-input @change-data="commonResultChange" search="true" :not_empty="true"
                                          :child="bclbList" :index="'bclbName'" :index_val="'bclbCode'" :val="popContent.hisBrjbxx.bclbCode"
                                          :name="'popContent.hisBrjbxx.bclbCode'" :index_mc="'bclbName'" id="bclbCode">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">转诊类型</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                          :child="turnMode_tran" :index="popContent.hisBrjbxx.turnMode" :val="popContent.hisBrjbxx.turnMode"
                                          :name="'popContent.hisBrjbxx.turnMode'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">转诊转院编码</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.turnCode"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">转院日期</label>
                        <div class="top-zinle">
                            <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                            <input type="text" class="zui-input background-f times2 text-indent-20" v-model="popContent.hisBrjbxx.turnDate"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">五保户证明材料</label>
                        <div class="top-zinle">
                            <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                          :child="wbhcl_tran" :index="popContent.hisBrjbxx.isMaterials" :val="popContent.hisBrjbxx.isMaterials"
                                          :name="'popContent.hisBrjbxx.isMaterials'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">农合中心</label>
                        <div class="top-zinle">
                            <select-input @change-data="commonResultChange" search="true"
                                          :child="centerNoList" :index="'centerName'" :index_val="'centerNo'" :val="popContent.hisBrjbxx.centerNo"
                                          :name="'popContent.hisBrjbxx.centerNo'" :index_mc="'centerName'" id="centerNo">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">重大疾病</label>
                        <div class="top-zinle">
                            <input class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.majorDiseaseName" @keydown="changeDown($event,'disease','diseaseContent','diseaseSearchCon')"
                                   @input="change(false,'disease',$event.target.value)">
                            <dsssearch-table :message="diseaseSearchCon" :selected="selSearch"
                                            :them="diseasethem" :page="page"
                                            @click-one="checkedOneOut" @click-two="selectDisease">
                            </dsssearch-table>
                        </div>
                    </div>
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">重大疾病2</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">重大疾病3</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">重大疾病4</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="top-form margin-b-10 flex-start margin-r-20">-->
                        <!--<label class="top-label ">重大疾病5</label>-->
                        <!--<div class="top-zinle">-->
                            <!--<input type="text" class="zui-input wh182 background-f" v-model="popContent.hisBrjbxx.icdName"/>-->
                        <!--</div>-->
                    <!--</div>-->
            </div>
        </div>
            <div class="jbxx margin-b-15 padd-t-10 padd-b-10 ">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">农合结算信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="jbxx-box flex-start padd-t-10 padd-b-10 padd-l-10 padd-r-10">
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">登记流水号</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.inpatientSn"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">姓名</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.name"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">性别</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.sexName"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">就诊类型</label>
                            <div class="top-zinle">
                                <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                              :child="jzlx_tran" :index="popContent.hisBrjbxx.cureId" :val="popContent.hisBrjbxx.cureId"
                                              :name="'popContent.hisBrjbxx.cureId'">
                                </select-input>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">出生日期</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.birthday"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">户主</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.masterName"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">个人编号</label>
                            <div class="top-zinle flex-start">
                                <input type="text" class="zui-input wh182 background-f" readonly="readonly" data-notEmpty="true" v-model="popContent.nhjsBrxx.memberNo"/>
                            </div>

                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">医疗卡号</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.bookNo"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">身份证号</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.idCard"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">与户主关系名称</label>
                            <div class="top-zinle flex-start">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.relationName"/>
                            </div>

                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">个人身份属性名称</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.identityName"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">当前年度成员住院已补偿次数</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.currYearRedeemCount"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">当前年度成员住院已补偿总医疗费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.currYearTotal"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">当前年度成员住院已补偿总保内费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.currYearEnableMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">当前年度成员住院已补偿金额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.currYearReddemMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">成员家庭编码</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.familyNo"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">成员家庭住址</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.address"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">参合属性名称</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.joinPropName"/>
                            </div>
                        </div>

                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">当前年度家庭住院已补偿次数</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.currFamilyRedeemCount"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">当前年度家庭住院已补偿总医疗费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.currFamilyTotal"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">当前年度家庭住院已补偿保内费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.currFamilyEnableMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">当前年度家庭住院已补偿金额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.currFamilyReddemMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次住院总医疗费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.totalCosts"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次住院保内费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.enableMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次住院费用中国定基本药品费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.essentialMedicineMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次住院费用中省补基本药品费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.provinceMedicineMoney"/>
                            </div>
                        </div>

                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次住院补偿扣除起付线金额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.startMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次住院补偿金额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.calculateMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">补偿类型名称</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.redeemTypeName"/>
                            </div>
                        </div>

                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">是否为单病种补偿</label>
                            <div class="top-zinle">
                                <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                              :child="isOrNot_tran" :index="popContent.nhjsBrxx.isSpecial" :val="popContent.nhjsBrxx.isSpecial"
                                              :name="'popContent.nhjsBrxx.isSpecial'">
                                </select-input>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">是否实行保底补偿</label>
                            <div class="top-zinle">
                                <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                              :child="isOrNot_tran" :index="popContent.nhjsBrxx.isPaul" :val="popContent.nhjsBrxx.isPaul"
                                              :name="'popContent.nhjsBrxx.isPaul'">
                                </select-input>
                            </div>
                        </div>

                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">追补金额，中药和国定基本药品提高补偿额部分</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.anlagernMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">单病种费用定额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.fundPayMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">医疗机构承担费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.hospAssumeMoney"/>
                            </div>
                        </div>

                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">个人自付费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.personalPayMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">民政优抚医疗补助</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.yFmedicalAid"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">民政城乡医疗救助</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.cXmedicalAid"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">高额材料限价超额费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.materialMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次结算计算方法</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.calculationMethod"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">慈善总会支付金额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.chinaCharityPay"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">是否长周期定额付费</label>
                            <div class="top-zinle">
                                <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                              :child="isOrNot_tran" :index="popContent.nhjsBrxx.isLongPeriod" :val="popContent.nhjsBrxx.isLongPeriod"
                                              :name="'popContent.nhjsBrxx.isLongPeriod'">
                                </select-input>
                            </div>
                        </div>

                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">是否进入大病保险</label>
                            <div class="top-zinle">
                                <select-input class="wh182 background-f" @change-data="commonResultChange" :not_empty="false"
                                              :child="isOrNot_tran" :index="popContent.nhjsBrxx.isCII" :val="popContent.nhjsBrxx.isCII"
                                              :name="'popContent.nhjsBrxx.isCII'">
                                </select-input>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">大病保险合规费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.cIIEligibleCosts "/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次大病保险起付线</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.cIIStartMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">本次大病保险补偿金额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.cIICalculateMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">累计大病保险补偿额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.cIICumulativePay"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">累计大病保险扣除起付线金额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.cIICumulativeStart"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">累计进入大病保险合规费用</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.cIICumulativeEligible"/>
                            </div>
                        </div>

                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">计生两户减免费用金额</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.familyPlanningWaiver"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">其他补偿</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.otherPay"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">家庭账户支付</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.familyAccount"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">生活费补偿</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.livingExpensesMoney"/>
                            </div>
                        </div>
                        <div class="top-form margin-b-10 flex-start margin-r-20">
                            <label class="top-label ">差旅费补偿</label>
                            <div class="top-zinle">
                                <input type="text" class="zui-input wh182 background-f" v-model="popContent.nhjsBrxx.travelExpenseMoney"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="height: 100px"></div>
        <div class="yb-width zui-table-tool">
            <span>费用合计:&nbsp;<i class="color-wtg" v-text="hisfy.fyje">0</i></span>
            <span>补偿费用:&nbsp;<i class="color-wtg" v-text="nhfy.kbfy">0</i></span>
            <span>不可报费用:&nbsp;<i class="color-wtg" v-text="nhfy.bkbfy">0</i></span>
        </div>
    </div>

</div>
<style type="text/css">
    .top-form .top-label{
        width:120px !important;
    }
    .yb-width{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 20px;
        padding: 20px 40px;
        box-sizing: border-box;
    }
</style>
<script type="text/javascript" src="yb.js"></script>