@import "../../../../css/baseColor";
.slgl-by{
  width: 100%;
  height: 40px;
  display: flex;
  line-height: 40px;
  background:@colorRgbf2a;
  justify-content: space-between;
  i{
    width: 20%;
    display: block;
    text-align: center;
    &:nth-child(5){
      padding-right: 15px;
    }
    em{
      color: @color35;
      padding-left: 5px;
      float: left;
    }
  }
}
.cffy-list{
  width: 100%;
  padding: 20px;
  li{
    width: 100%;
    float: left;
    font-size: @font14;
    display: flex;
    line-height: 30px;
    justify-content: flex-start;
    i{
      float: left;
      display: flex;
      justify-content:flex-start;
      width: calc(~"(100% - 30px)/4");
      text-align: left;
      color: @color7f8;
      margin-right: 10px;
    }
    em{
      color: @color35;
    }
    &.zkmore{
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.color-75{
  color: @color75;
  font-size: @font12;
}
.cfhj-top{
  width: 100%;
  height: 36px;
  background:@colored;
  line-height: 36px;
  li{
    width: 100%;
    display: flex;
    justify-content: center;
    i{
      width: calc(~"(100% - 50px)/6");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
    }
  }
}
.cfhj-content{
  width: 100%;
  overflow: auto;
  max-height:500px;
  li{
    width: 100%;
    display: flex;
    border:1px solid @coloree;
    border-top: none;
    justify-content: center;
    i{
      width: calc(~"(100% - 50px)/6");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
    }
    &:hover{
      background:rgba(26,188,156,0.08);
    }
  }
}
.all-check{
  position: absolute;
  right: 0;
  top:-10px;
  width: 20px;
  height: 20px;
}
.slgl-fl{
  position: absolute;
  left:0;
  top: 20px;
  line-height:32px;
  padding-left:65px;
  color: @colorwtg;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.icon-width:before{
  left: 34px !important;
}
input[type=checkbox].green + label:before{
  top: 41% !important;
}
.ksys-btn{
  bottom: 0px;
}