.wrapper{
    background-color: #fff;
    height: 100%;
}
.userNameBg{
    background:#708f89;
    /*height:180px;*/
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    background-image: url("/newzui/pub/image/userImg.png");
    padding: 10px;
}
.flex{
    display: flex;
    align-items: center;
}
.userNameImg img{
    width: 100px;
}
.text-color{
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}
.zyh,.bq,.ys,.brzt,.bz,.cwh{
    margin-right: 60px;
}
.userCwh{
    margin-bottom: 4px;
}
.fyhj {
    margin-right: 39px;
}
.yjhj {
    margin-right: 104px;
}
.zyts {
    margin-right: 32px;
}
.phone {
    margin-right: 53px;
}
.hl {
    margin-right: 52px;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{
    color: #B0BFBB;
    text-decoration: underline;
}
.menu {
    padding: 0px 15px;
    height: 50px;
}
.menu span{
    padding: 0 5px;
    display: inline-block;
}
.menu .fenge{
    font-size: 0;
    width: 1px;
    height: 17px;
    background:#646f82;
    padding: 0;
}
.fa-th-large:before {
    font-size: 18px;
    padding-top: 3px;
}
.fa-th-large.active::before {
    color: #1abc9c;
}
.pop-input-box .danwei{
    color:#1abc9c;
    line-height:36px;
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 11px;
}
.pop-input-box .fenge{
    position: absolute;
    right: -15px;
    top: 9px;
}
.zui-inline, .zui-input-inline, .zui-select-inline{
    margin-right: 0px;
}
.action-bar.fixed{
    right: 10px;
    left: 10px;
    width: auto;
    bottom: 10px;
    padding-top: 16px;
    padding-bottom: 16px;
    height: 68px;
}
.zui-table-view{
    padding: 10px 10px 0;
}
.zui-form-label{
    line-height: 24px;
}
.tong-search{
    padding-top: 0;
}
.tong-search .zui-form .zui-inline{
    padding-bottom: 0;
}