
var sjys=new Vue({
    el:'.sjys-box',
    mixins: [dic_transform, baseFunc, tableBase, mformat,scrollOps],
    data:{

    },
    methods:{
        reviewed:function () {
            pop.popShow=true;
            pop.topTitle='三级医师查房报告审核';
        },
        //填写报告
        bgOpen:function () {
            SjysReport.bgShow=true;
        },
        //编辑报告
        EditReport:function () {
            SjysReport.bgShow=true;
        }

    }

})


//
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat,scrollOps],
    data: {

        numOne: 1,
        title:'添加主任医生',
        popContent:{},
        XsList:[

            {
                bgxs:'本科室',
                ksbm:'01',
            },
            {
                bgxs:'外科',
                ksbm:'02',
            },
        ],

    },

    methods: {
        //关闭
        close: function () {
            this.numOne = 1;
        },
        //打开侧窗
        open:function () {
            this.numOne = 0;
        },
        //确定添加主任医生
        sideOk:function () {
            malert('添加成功','top','success')
        }
    },
});

var pop=new Vue({
    el:'#pop',
    mixins: [dic_transform, baseFunc, tableBase, mformat,scrollOps],
    data:{
        popShow:false,
        wjzShow:true,
        ryxmList:[],
        topTitle:'',
        patientShow:true,//患者显示
        popContent:{},
    },
    methods:{
        //关闭取消操作
        Popclose:function () {
            this.popShow=false;
            this.patientShow=true;
        },
        //审核
        popConfirm:function () {
            this.popShow=false;
            this.patientShow=true;
            malert('审核','top','success')
        },
        refuse:function () {
            this.topTitle='拒绝原因';
            this.patientShow=false;
        },
        //确定
        popConfirms:function () {
            this.popShow=false;
            malert('确定','top','success')
        }
    }
})
var SjysReport=new Vue({
    el:'.sjys-report',
    mixins: [ baseFunc, dic_transform, scrollOps],
    data:{
        bgShow:false,//报告填写
        delShow:true,
    },
    methods:{
    //添加主任医师
        addDoctor:function () {
            brzcList.open();
        },
        //关闭报告填写
        bgClose:function () {
            this.bgShow=false;
        },
        //添加主治医生
        addDrug:function () {
            brzcList.open();
        },
        //确定报告操作
        reportOk:function () {
            this.bgShow=false;
            malert('确定','top','success')
        },
        //删除
        del:function () {
            this.delShow=false;
        }
    }
})


$(window).resize(function () {
    changHeight();
});
function changHeight() {

    var side_height=$(window).height()-($('.fyxm-side-top').outerHeight()+$('.ksys-btn').outerHeight()+$('.top-form').outerHeight()+$('.zui-table-header').outerHeight()+30);
    $('.body-height').css({
        'height':side_height,
        // 'overflow':'auto'
    });
}

setTimeout(function () {
    changHeight();
},150);
//
// var a = 0;
//
// function xyz() {
//     if (a == 0) {
//         a = 1
//     }
//     brzcList.numOne = 0;
//
// }
