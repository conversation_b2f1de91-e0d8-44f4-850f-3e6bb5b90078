(function () {

    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            index:1,
            pop:{},
            searchAll:'',
            saveList:[],
            delList:[],
        },

        methods:{
            addclass:function (num) {
                this.index=num
            },
            show:function () {
                pop.isShow=true;
            },
            add:function () {
                //获取送检科室
                $.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=querySjks", function(json) {
            		console.log(json);
            		if(json.a=="0"){
            			wapse.ksList = json.d.list;
            		}
            	});
            },
            refresh:function(){
            	jyx.isChecked = [];
            	jyx.getData();
            },
            save:function(){
            	console.log(wrapper.saveList);
            	if(wrapper.saveList.length == 0){
            		malert('未做任何修改！','top','defeadted');
            	}else{
            		//保存操作
            		var data =  '{"list":'+ JSON.stringify(wrapper.saveList) +'}';
	        		this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=batchOpJyfz',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				jyx.getData();
	           			malert('更新成功！！','top','success');
	           			jyx.getData();
	       			 }else{
	            			malert('更新失败！！','top','defeadted');
	            	 }
   	       		 });
            	}
            },
            del:function(){
            	wrapper.delList = [];
            	if(jyx.isChecked.length == 0){
            		malert('请选择要删除的行','top','defeadted');
            	}else{
            		for(var i = 0 ; i < jyx.isChecked.length ; i++){
            			if(jyx.isChecked[i]){
            				wrapper.delList.push(jyx.jsonList[i]);
            			}
            		}
            		//删除
            		var data =  '{"list":'+ JSON.stringify(wrapper.delList) +'}';
            		console.log(data);
    	       		this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmFzDelete',data).then(function(json) {
    	       			 console.log(json.body);
    	       			 if(json.body.a == 0){
    	       				jyx.getData();
                			malert('删除成功！！','top','success');
                			jyx.getData();
    	       			 }else{
    	            			malert('删除失败！！','top','defeadted');
    	            	 }
    	       		 });
            	}
            	
            }
        },
        watch:{
        	'searchAll':function(){
        		jyx.param.parm = wrapper.searchAll;
        		jyx.getData();
        	}
        }
    });
    var jyx=new Vue({
        el:'#utable1',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
        	jsonList:'',
        	isChecked:[],
        	totlePage:'',
        	param:{
        		parm:'',
        		page: 1,
    			rows: 10,
        	},
        },
        methods:{
            show:function () {
                pop.isShow=true;
                pop.title='编辑检验分组'
            },
            getData:function(){
            	/*param:{
                    ysbm:'',
                    ysmc:'',
                    ly:'1',
                    pydm:'',
                    stop:''
                },*/
            	wapse.fz.fzmc = '';
            	wapse.fz.ksbm = '';
            	wapse.fz.tybz = '0';
            	jyx.isChecked = [];
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmFzSelect&param=" + JSON.stringify(jyx.param), function(json) {
            		console.log(json);
            		if(json.a=="0"){
            			jyx.jsonList = json.d.list;
            			jyx.totlePage = Math.ceil(json.d.total / jyx.param.rows);
            		}
            	});
            },
            change:function(index){
            	jyx.jsonList[index].tybz = jyx.jsonList[index].tybz == '0' ? '1':'0';
            	for (var i = 0; i < wrapper.saveList.length; i++) {
					if(wrapper.saveList[i].fzbm == jyx.jsonList[index].fzbm){
						wrapper.saveList[i] = jyx.jsonList[index];
						return;
					}
				}
            	//添加
            	wrapper.saveList.push(jyx.jsonList[index]);
            }
        },
    })
   
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            pcShow:false,
            jsShowtime:false,
            jsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            popContent:{},
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:'',
            isFold: false,
            param:{
                ysbm:'',
                ysmc:'',
                ly:'1',
                pydm:'',
                stop:''
            },
            ksList:[],
            fz:{
            	ksbm:'',
                fzmc:'',
                tybz:'0',
            }
            
        },
        methods:{
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                //成功回调提示
                // malert('111','top','defeadted');
            },
            AddClose:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            } ,
            // //取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');
            },
            change:function(){
            	wapse.tybz = wapse.tybz == "0" ?  "1" :"0";
            },
            // //确定
            confirms:function () {
            	if(wapse.fz.fzmc == ''){
            		malert('请填写检验分组名称','top','defeadted');
            		return;
            	}
            	console.log(wapse.fz);
            	var data = JSON.stringify(wapse.fz);
            	this.$http.post('/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmFzInsert',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == 0){
	       				jyx.getData();
           			malert('添加成功！！','top','success');
           		    // $(".side-form-bg").removeClass('side-form-bg')
                    // $(".side-form").addClass('ng-hide');
           			jyx.getData();
	       			 }else{
	            			malert('添加失败！！','top','defeadted');
	            	 }
	       		 });
              
            }

        },
    });


    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
    	if($(this).val() == '' || $(this).val() == null) {
    		$(this).addClass("emptyError");
    	} else {
    		$(this).removeClass("emptyError");
    	}
    });

    document.onkeydown=function (ev) {
        var ev=window.event|| ev
        var key=ev.keyCode
        if(key==83&& ev.ctrlKey){
            return false
        }
    }
    
    jyx.getData();
})()