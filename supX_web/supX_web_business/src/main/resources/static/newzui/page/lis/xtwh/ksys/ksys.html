<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室医生</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../xmzb/xmzb.css"/>
    <link rel="stylesheet" href="ksys.css"/>
</head>

<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="zui-btn btn-primary"  data-toggle="sideform" data-target="#brzcList"><i class=" icon-xz1 padd-r5"></i>新增医师</button>
            <button class="zui-btn btn-primary-b" @click="getData"><i class=" icon-sx "></i>刷新</button>
            <button class="zui-btn btn-primary-b" @click="saveData"><i class=" icon-baocun "></i>保存</button>
            <button class="zui-btn btn-primary-b " @click="deleteData"><i class="icon-sc-header "></i>删除</button>
            <button class="zui-btn btn-primary-b"><i class="icon-dysq "></i>打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">医师检索</label>
                    <div class="zui-input-inline zui-select-inline ">
                        <input type="text" v-model="searchAll" placeholder="请输入关键字" class="zui-input wh274"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view padd-l-10 flex-container padd-r-10" id="jyxm" >
              <div id="xmzb-content-left" class="padd-r-10">
                  <div class="zui-table-header">
                      <div class="zui-table-header">
                          <table class="zui-table table-width50">
                              <thead>
                              <tr>
                                  <th class="cell-m">
                                      <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                      :type="'all'"
                                                      :val="isCheckAll">
                                      </input-checkbox>
                                  </th>
                                  <th >
                                      <div class="zui-table-cell cell-s"><span>医师编码</span></div>
                                  </th>
                                  <th >
                                      <div class="zui-table-cell cell-s"><span>医师姓名</span></div>
                                  </th>
                                  <th >
                                      <div class="zui-table-cell cell-s text-left"><span>拼音代码</span></div>
                                  </th>
                              </tr>
                              </thead>
                          </table>
                      </div>
                  </div>
                  <div class="zui-table-body ">
                      <table class="zui-table table-width50" v-if="jsonList.length!=0">
                          <tbody>
                          <tr v-for="(item, $index) in jsonList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                              @mouseenter="hoverMouse(true,$index)"
                              @mouseleave="hoverMouse()"
                              @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index" @dblclick="edit($index)" >
                              <td class="cell-m">
                                  <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                  :type="'some'" :which="$index"
                                                  :val="isChecked[$index]">
                                  </input-checkbox>
                              </td>
                              <td>
                                  <div class="zui-table-cell cell-s" v-text="item.ysbm"></div>
                              </td>
                              <td>
                                  <div class="zui-table-cell cell-s" v-text="item.ysmc"></div>
                              </td>
                              <td>
                                  <div class="zui-table-cell cell-s text-left" v-text="item.pydm"></div>
                              </td>
                          </tr>
                          </tbody>
                      </table>
                      <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                  </div>
              </div>
        <div id="xmzb-content-right">
            <div class="zui-table-header">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'"
                                                :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s"><span>医师编码</span></div>
                            </th>
                            <th >
                                <div class="zui-table-cell cell-s"><span>医师姓名</span></div>
                            </th>
                            <th ><div class="zui-table-cell cell-s "><span>拼音代码</span></div></th>
                            <th ><div class="zui-table-cell cell-s "><span>来源</span></div></th>
                            <th class="cell-m"><div class="zui-table-cell cell-m "><span>血库</span></div></th>
                            <th ><div class="zui-table-cell cell-s "><span>状态</span></div></th>
                            <th ><div class="zui-table-cell cell-s "><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div class="zui-table-body ">
                <table class="zui-table table-width50" v-if="jsonList.length!=0">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index" @dblclick="edit($index)" >
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td >
                            <div class="zui-table-cell cell-s" v-text="item.ysbm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ysmc"></div>
                        </td>
                        <td><div class="zui-table-cell cell-s " v-text="item.pydm"></div></td>
                        <td><div class="zui-table-cell cell-s " v-text="hosly[item.ly==null?'0':'1']"></div></td>
                        <td class="cell-m">
                            <input-checkbox :val="item.xkbz == null ? false : item.xkbz == 1? true : false" type="checkbox"/>
                       </td>
                        <td>
                            <div class="switch cell-s" >
                                <input :id="'checked'+$index" type="checkbox" v-model="item.stop" true-value="0" false-value="1"  />
                                <label :for="'checked'+$index"></label>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" >
                                <i class="icon-sc" @click="DelLine(item)"></i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
            </div>
        </div>
    </div>
</div>
<div id="pop">
    <!--<transition name="pop-fade">-->
    <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
    <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title " v-text="title"></div>
        <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
        <div class="layui-layer-content" >
            <div class=" layui-mad layui-height" v-text="centent">
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
        </div>
    </div>
    <!--</transition>-->
</div>

<div class="side-form ng-hide" style="width:320px;padding-top: 0;"  id="brzcList" role="form">
    <div class="tab-message">
        <a>新增送检医师</a>
        <a href="javascript:;" class="fr closex ti-close"  @click="AddClose"></a>
    </div>
    <div class="ksys-side">
        <span class="span0">
            <i>医师编码</i>
            <input type="text" class="zui-input border-r4" disabled placeholder="请输入医师编码"/>
        </span>
        <span class="span0">
            <i>医师姓名</i>
            <input type="text" class="zui-input border-r4" v-model="parm.ysmc"
                   @blur="setPYDM(parm.ysmc, 'parm', 'pydm')"
                   placeholder="请输入医师姓名"/>
        </span>
        <span class="span0">
            <i>拼音代码</i>
            <input type="text" class="zui-input border-r4" readonly="readonly"  v-model="parm.pydm" placeholder="请输入拼音代码"/>
        </span>
        <span class="span0">
            <i>状态</i>
            <!--<div class="switch">-->
                <!--<input v-model="parm.stop" class="green" id="xinzeng"  type="checkbox" :checked="param.stop==0?true:false" @change="change"/>-->
                <!--<label for="xinzeng"></label>-->
            <!--</div>-->
            <div class="switch" >
                <input v-model="parm.stop" :checked="parm.stop==0?true:false" type="checkbox"/>
                <label></label>
            </div>
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">确定</button>
    </div>
</div>

<style>
    .side-form-bg{
        background: none;
        position: inherit;
    }
</style>

<script src="ksys.js"></script>
</body>
</html>
