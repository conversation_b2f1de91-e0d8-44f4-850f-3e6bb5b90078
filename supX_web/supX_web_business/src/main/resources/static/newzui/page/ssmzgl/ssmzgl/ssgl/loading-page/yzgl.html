<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="/newzui/pub/css/print.css" media="print" />
    <link href="user.css" rel="stylesheet">
    <style>
        .tem {
            position: relative;
            float: left;
            /* width: 300px; */
            /* height: 450px; */
            width: 800px;
            height: 500px;
            border: 1px solid green;
            margin-left: 20px;
            margin-top: 20px;
        }

        .item {
            position: absolute;
            display: inline-block;
            top: 10px;
            margin-bottom: 20px;
            font-size: 14px;
            cursor: default;
            z-index: 100;
        }

        .loadPage{
            overflow: hidden;
        }
    </style>
</head>

<body>
    <div class="printShow padd-l-10 padd-r-10 flex-container flex-dir-c no-print-jcjyfy">
        <div id="yzd" class="flex-container flex-dir-c">
            <div class="toolMenu_yzd printHide">
                <div @click="long(0)" :class="{'yzd_select': which==0}">长&nbsp;&nbsp;期</div>
                <div @click="short(1)" :class="{'yzd_select': which==1}">临&nbsp;&nbsp;时</div>
            </div>

            <div v-cloak class="cqyzd printHide" v-if="isShow">
                <div class="yzdTitle" :class="{'goPrintHide': isGoPrint}">长期医嘱单</div>
                <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
                    <div>
                        <span>科别:</span>
                        <span>{{BrxxJson.ryksmc}}</span>
                    </div>
                    <div>
                        <span>床号:</span>
                        <span>{{BrxxJson.rycwbh}}</span>
                    </div>
                    <div>
                        <span>姓名:</span>
                        <span>{{BrxxJson.brxm}}</span>
                    </div>
                    <div>
                        <span>性别:</span>
                        <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                    </div>
                    <div>
                        <span>年龄:</span>
                        <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                    </div>
                    <div>
                        <span>住院号:</span>
                        <span>{{BrxxJson.zyh}}</span>
                    </div>
                </div>

                <div class="yzd-table">
                    <table cellspacing="0" cellpadding="0">
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th colspan="2">开始</th>
                            <th rowspan="2">执行<br>时间</th>
                            <th rowspan="2" style="width: 320px">长期医嘱</th>
                            <th colspan="2">签名</th>
                            <th colspan="2">停止</th>
                            <th rowspan="2">停止<br>执行<br>时间</th>
                            <th colspan="2">签名</th>
                        </tr>
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th>日<br>月</th>
                            <th>时<br>间</th>
                            <th>医师</th>
                            <th>护士</th>
                            <th>日<br>月</th>
                            <th>时<br>间</th>
                            <th>医师</th>
                            <th>护士</th>
                        </tr>
                        <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                            @click="goPrint($index)">
                            <td v-text="sameDate('ksrq', $index, 'ry')"></td>
                            <td v-text="sameDate('ksrq', $index, 'sj')"></td>
                            <td v-text="sameDate('zxsj', $index, 'sj')"></td>
                            <td>
                                <span class="yzd-name" v-text="item.xmmc"></span>
                                <span :class="[{'sameStart': sameSE($index) == 'start'},
                                    {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                                <span class="yzd-way" v-show="isShowItem($index)" >{{item.psff}}{{item.ldmc}}</span>
                                <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                            </td>
                            <td style="vertical-align: inherit" v-text="item.ysqmxm"></td>
                            <td style="vertical-align: inherit" v-text="item.zxhsxm"></td>
                            <td v-text="sameDate('ystzsj', $index, 'ry')"></td>
                            <td v-text="sameDate('ystzsj', $index, 'sj')"></td>
                            <td v-text="sameDate('hstzsj', $index, 'sj')"></td>
                            <td v-text="item.tzysxm"></td>
                            <td v-text="item.tzhsxm"></td>
                        </tr>
                    </table>
                </div>

                <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                    <div class="yzd-ysInfo">
                        <div class="wh150 text-left">
                            <span>主管医生:</span>
                            <span>{{BrxxJson.zyysxm}}</span>
                        </div>
                        <div>
                            <span>护士:</span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="cqPrint">
                <!--<transition name="pop-fade">-->
                <div v-if="isShow" style="background-color: #ffffff">
                    <div v-for="(itemList, index) in list" :style="{'paddingTop':(index+1)*50+'px'}">
                        <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                            <div>
                                <span>科别:</span>
                                <span>{{BrxxJson.ryksmc}}</span>
                            </div>
                            <div>
                                <span>床号:</span>
                                <span>{{BrxxJson.rycwbh}}</span>
                            </div>
                            <div>
                                <span>姓名:</span>
                                <span>{{BrxxJson.brxm}}</span>
                            </div>
                            <div>
                                <span>性别:</span>
                                <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                            </div>
                            <div>
                                <span>年龄:</span>
                                <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                            </div>
                            <div>
                                <span>住院号:</span>
                                <span>{{BrxxJson.zyh}}</span>
                            </div>
                        </div>

                        <div class="yzd-table">
                            <table cellspacing="0" cellpadding="0">
                                <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                    <th colspan="2">开始</th>
                                    <th rowspan="2">执行<br>时间</th>
                                    <th rowspan="2" style="width: 320px">长期医嘱</th>
                                    <th colspan="2">签名</th>
                                    <th colspan="2">停止</th>
                                    <th rowspan="2">停止<br>执行<br>时间</th>
                                    <th colspan="2">签名</th>
                                </tr>
                                <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                    <th>日<br>月</th>
                                    <th>时<br>间</th>
                                    <th>医师</th>
                                    <th>护士</th>
                                    <th>日<br>月</th>
                                    <th>时<br>间</th>
                                    <th>医师</th>
                                    <th>护士</th>
                                </tr>
                                <tr v-for="(item, $index) in itemList" :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]">
                                    <td v-text="sameDate('ksrq', $index, index, 'ry')"></td>
                                    <td v-text="sameDate('ksrq', $index, index, 'sj')"></td>
                                    <td v-text="sameDate('zxsj', $index, index, 'sj')"></td>
                                    <td>
                                        <span class="yzd-name" v-text="item.xmmc"></span>
                                        <span :class="[{'sameStart': sameSE($index, index) == 'start'},
                                    {'sameEnd': sameSE($index, index) == 'end'},{'same': sameSE($index, index) == 'all'}]"></span>
                                        <span class="yzd-way" v-show="isShowItem($index)" >{{item.psff}}{{item.ldmc}}</span>
                                        <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                    </td>
                                    <td style="vertical-align: inherit" v-text="item.ysqmxm"></td>
                                    <td style="vertical-align: inherit" v-text="item.zxhsxm"></td>
                                    <td v-text="sameDate('ystzsj', $index, index, 'ry')"></td>
                                    <td v-text="sameDate('ystzsj', $index, index, 'sj')"></td>
                                    <td v-text="sameDate('hstzsj', $index, index, 'sj')"></td>
                                    <td v-text="item.tzysxm"></td>
                                    <td v-text="item.tzhsxm"></td>
                                </tr>
                            </table>
                        </div>

                        <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                            <div class="yzd-ysInfo">
                                <div class="wh150 text-left">
                                    <span>主管医生:</span>
                                    <span>{{BrxxJson.zyysxm}}</span>
                                </div>
                                <div>
                                    <span>护士:</span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                        <div class="text-center" v-text="'第  ' + (index + 1) + '  页'"></div>
                    </div>
                </div>
                <!--</transition>-->
            </div>

            <div v-cloak class="lsyzd printHide" v-if="isShow">
                <div class="yzdTitle" :class="{'goPrintHide': isGoPrint}">临时医嘱单</div>
                <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
                    <div>
                        <span>科别:</span>
                        <span>{{BrxxJson.ryksmc}}</span>
                    </div>
                    <div>
                        <span>床号:</span>
                        <span>{{BrxxJson.rycwbh}}</span>
                    </div>
                    <div>
                        <span>姓名:</span>
                        <span>{{BrxxJson.brxm}}</span>
                    </div>
                    <div>
                        <span>性别:</span>
                        <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                    </div>
                    <div>
                        <span>年龄:</span>
                        <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                    </div>
                    <div>
                        <span>住院号:</span>
                        <span>{{BrxxJson.zyh}}</span>
                    </div>
                </div>

                <div class="yzd-table">
                    <table cellspacing="0" cellpadding="0">
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th colspan="2">吩咐时间</th>
                            <th rowspan="2" style="width: 445px">临时医嘱</th>
                            <th rowspan="2">医师签名</th>
                            <th rowspan="2">执行<br>时间</th>
                            <th rowspan="2">执行者签名</th>
                        </tr>
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th>日<br>月</th>
                            <th>时<br>间</th>
                        </tr>
                        <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                            @click="goPrint($index)">
                            <td v-text="sameDate('ksrq', $index, 'ry')"></td>
                            <td v-text="sameDate('ksrq', $index, 'sj')"></td>
                            <td>
                                <span class="yzd-name" v-text="item.xmmc"></span>
                                <span :class="[{'sameStart': sameSE($index) == 'start'},
                                    {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                                <span class="yzd-way" v-show="isShowItem($index)" >{{item.psff}}{{item.ldmc}}</span>
                                <span style="margin-left: 20px;width: auto" v-show="item.psjg != '无'">结果(&nbsp;&nbsp;&nbsp;&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;&nbsp;&nbsp;&nbsp;)</span>
                                <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                            </td>
                            <td v-text="item.ysqmxm"></td>
                            <td v-text="fDate(item.zxsj,'shortY')"></td>
                            <td v-text="item.zxhsxm"></td>
                        </tr>
                    </table>
                </div>

                <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                    <div class="yzd-ysInfo">
                        <div class="wh150 text-left">
                            <span>主管医生:</span>
                            <span></span>
                        </div>
                        <div>
                            <span>护士:</span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lsPrint">
                <transition name="pop-fade">
                    <div v-if="isShow" style="background-color: #fff">
                        <div class="popCenter" v-for="(itemList, index) in list" :style="{'paddingTop':(index+1)*50+'px'}">
                            <!--<button @click="print">打印</button>-->
                            <!--<button @click="goOnPrint">续打</button>-->
                            <!--<button @click="isShow = false">取消</button>-->
                            <!--<div class="yzdTitle" :class="{'goPrintHide': isGoPrint && pagePrint == index}">临时医嘱单</div>-->
                            <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                <div>
                                    <span>科别:</span>
                                    <span>{{BrxxJson.ryksmc}}</span>
                                </div>
                                <div>
                                    <span>床号:</span>
                                    <span>{{BrxxJson.rycwbh}}</span>
                                </div>
                                <div>
                                    <span>姓名:</span>
                                    <span>{{BrxxJson.brxm}}</span>
                                </div>
                                <div>
                                    <span>性别:</span>
                                    <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                                </div>
                                <div>
                                    <span>年龄:</span>
                                    <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                                </div>
                                <div>
                                    <span>住院号:</span>
                                    <span>{{BrxxJson.zyh}}</span>
                                </div>
                            </div>

                            <div class="yzd-table">
                                <table cellspacing="0" cellpadding="0">
                                    <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                        <th colspan="2">吩咐时间</th>
                                        <th rowspan="2" style="width: 445px">临时医嘱</th>
                                        <th rowspan="2">医师签名</th>
                                        <th rowspan="2">执行<br>时间</th>
                                        <th rowspan="2">执行者签名</th>
                                    </tr>
                                    <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                        <th>日<br>月</th>
                                        <th>时<br>间</th>
                                    </tr>
                                    <tr v-for="(item, $index) in itemList" :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]">
                                        <td v-text="sameDate('ksrq', $index, index, 'ry')"></td>
                                        <td v-text="sameDate('ksrq', $index, index, 'sj')"></td>
                                        <td>
                                            <span class="yzd-name" v-text="item.xmmc"></span>
                                            <span :class="[{'sameStart': sameSE($index, index) == 'start'},
                                    {'sameEnd': sameSE($index, index) == 'end'},{'same': sameSE($index, index) == 'all'}]"></span>
                                            <span class="yzd-way" v-show="isShowItem($index)">{{item.psff}}{{item.ldmc}}</span>
                                            <span style="margin-left: 20px;width: auto" v-show="item.psjg != '无'">结果(&nbsp;&nbsp;&nbsp;&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;&nbsp;&nbsp;&nbsp;)</span>
                                            <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                        </td>
                                        <td v-text="item.ysqmxm"></td>
                                        <td v-text="fDate(item.zxsj,'shortY')"></td>
                                        <td v-text="item.zxhsxm"></td>
                                    </tr>
                                </table>
                            </div>

                            <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                                <div class="yzd-ysInfo">
                                    <div class="wh150 text-left">
                                        <span>主管医生:</span>
                                        <span></span>
                                    </div>
                                    <div>
                                        <span>护士:</span>
                                        <span></span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center" v-text="'第  ' + (index + 1) + '  页'"></div>
                        </div>
                    </div>
                </transition>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="yzgl.js"></script>
</body>

</html>
