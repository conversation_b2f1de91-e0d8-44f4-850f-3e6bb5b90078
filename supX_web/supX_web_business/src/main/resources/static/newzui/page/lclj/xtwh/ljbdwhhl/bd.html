<div class="maxContent yzblsj yzglTable">
    <div class="headerText">{{jsonList.title}}</div>
    <div class="headerList">使用对象：{{jsonList.sybq}}</div>
    <div class="headerList">标准住院日：{{jsonList.bzts}}天</div>
    <div class="fyxm-size ybglTable ">
        <ul class="cssz-list yzHeader">
            <p class="topData">时间</p>
            <!--横轴-->
            <li v-for="(item,index) in jsonList.jdList">{{item.jdmc}}</li>
        </ul>
        <div class="zui-table-body body-height yzDataList">
            <ul class="cssz-list">
                <li class="fixed" v-for="(list,index) in jsonList.dlList" :class="[{'table-hovers':index===activeIndex,'table-hover':index === hoverIndex}]"
                    @mouseenter="switchIndex('hoverIndex',true,index)"
                    @mouseleave="switchIndex()"
                    @click="switchIndex('activeIndex',true,index)">
                    <!--竖轴-->
                    <p class="leftData"
                       :style="[{height:jsonList.nrs[index].length*45+'px'},{lineHeight:jsonList.nrs[index].length*45+'px'}]">
                        {{list.mc}}</p>
                    <!--<ul>-->
                    <!--<li class="yzContent" v-for="(list,index) in jsonList.nrs[index]">-->
                    <!--<div  class="position" >-->
                    <!--<span v-for="(Aindex,num) in list.nr" v-if="list.nr"  :data-num="JSON.stringify(Aindex.content)"    class=" titleText" :style="{width:arrWidth[num]+'px'}" >{{Aindex.content}}</span>-->
                    <!--<span v-for="Cindex in list.line" v-if="!list.nr" :data-title="JSON.stringify(Cindex)"  :style="{width:arrWidth[Cindex-1]+'px'}" class=" titleText" ></span>-->
                    <!--</div>-->
                    <!--</li>-->
                    <!--</ul>-->
                    <ul>
                        <li class="yzContent" v-for="(list,Index) in jsonList.nrs[index][0].line"
                            :data-title="JSON.stringify(jsonList.nrs[index][0].line)" :data-text="Index">
                            <div class="position" :data-text="JSON.stringify(jsonList.nrs[index][add].nr)">
                                <span v-for="(Aindex,num) in jsonList.nrs[index][add].nr"
                                      v-if="jsonList.nrs[index][Index].nr" :data-num="JSON.stringify(jsonList.nrs[index])"
                                      class=" titleText" :style="{width:arrWidth[num]+'px'}">{{Aindex.content}}</span>
                                <span v-for="Cindex in jsonList.nrs[index][add].line"
                                      v-if="!jsonList.nrs[index][Index].nr" :data-title="JSON.stringify(Cindex)"
                                      :style="{width:arrWidth[Cindex-1]+'px'}" class=" titleText"></span>
                            </div>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>
<script src="bd.js" type="text/javascript"></script>