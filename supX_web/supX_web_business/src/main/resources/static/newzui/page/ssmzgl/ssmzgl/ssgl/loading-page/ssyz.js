//医嘱处理
// $("#zuiTable").uitable();
var qjIndex = 0;
var yyffcz = false;
var pccz = false;
var sfzt = false;
var index2 = "";
printGd = 20
var zdtj = false;//指定添加
var tjIndex = 0;//添加位置
var change = false;
//顶部工具栏
var panel = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifShow: true,
        haveYe: false,
        /*isShow: true,*/
        popContent: {
            yzxx: '2', //医嘱类型
            /*yzzt: '1',*/
            yzgl: '0',  //医嘱过滤
            yfbm: '2',  //药房
            yfmc: '',
            yess: '0',
        },
        sfgr_tran: {
            "1": "是",
            "0": "否"
        },
        /*YzdpopContent: {
            yzxx: '0'
        },*/
        Yf_List: [],
        is_csqx: userNameBg.csqx,
        csqxContent: {}
    },
    created: function () {
        this.getCsQx();
    },
    computed: {
        setKsbm: function () {
            if (this.is_csqx.N03012200104) {
                for (var i = 0; i < this.Yf_List.length; i++) {
                    if (this.is_csqx.N03012200104 == this.Yf_List[i].yfbm) {
                        this.popContent.yfbm = this.Yf_List[i].yfbm
                        this.popContent.yfmc = this.Yf_List[i].yfmc
                    }
                }
            } else {
                if (this.Yf_List.length != 0) {
                    this.popContent.yfbm = this.Yf_List[0].yfbm
                    this.popContent.yfmc = this.Yf_List[0].yfmc
                }

            }
        }
    },
    methods: {
        getCsQx: function () {
            //获取参数权限
            parms = {
                "ylbm": 'N030122001',
                "ksbm": userNameBg.Brxx_List.ssks
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parms), function (json) {
                if (json.a == 0 && json.d && json.d.length > 0) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N03012200101": // 手术医嘱分类是否可修改
                                if (csjson.csz) {
                                    panel.csqxContent.N03012200101 = csjson.csz;
                                    hzList.csContent.N03012200101 = csjson.csz;
                                }
                                break;
                            case "N03012200105": // 是否使用麻醉药实名登记
                                if (csjson.csz) {
                                    panel.csqxContent.N03012200105 = csjson.csz;
                                    hzList.csContent.N03012200105 = csjson.csz;
                                }
                                break;
                        }
                    }
                } else {
                    malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                }
            });
        },
        //医嘱类型改变
        resultChange_yzxx: function (val) {
            Vue.set(this[val[2][0]], val[2][1], val[0]);
            cqyzd.which = val[0];
            if (val[0] === '1') {
                cqyzd.isShow = false;
                lsyzd.isShow = true;
                lsyzd.getData();
            } else {
                lsyzd.isShow = false;
                cqyzd.isShow = true;
                cqyzd.getData();
            }
        },

        resultChangeSs: function (val) {
            Vue.set(this[val[2][0]], val[2][1], val[0]);
            hzList.Wf_selectYZ()
        },
        //医嘱过滤改变
        resultChange_type: function (val) {
            Vue.set(this[val[2][0]], val[2][1], val[0]);
            hzList.Wf_selectYZ()
        },
        //药房改变
        resultChange_text: function (val) {
            Vue.set(this[val[2][0]], [val[2][1]], val[0]);
            Vue.set(this[val[2][0]], 'yfmc', val[0]);
            hzList.Wf_selectYZ()
        },
        //初始化数据
        Wf_init: function () {
            var yf_dg = {page: 1, rows: 1000, sort: "yfbm", order: "asc", parm: ""};
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmYf&types=query&dg=' + JSON.stringify(yf_dg) + '&json=' + JSON.stringify({tybz: '0'}), function (data) {
                if (data.a == 0) {
                    panel.Yf_List = data.d.list;  //绑定药房
                    panel.popContent.yfbm = data.d.list[0].yfbm;  //绑定药房
                    panel.popContent.yfmc = data.d.list[0].yfmc;  //绑定药房
                } else {
                    malert('获取药房失败', 'top', 'defeadted');
                }
            });
        },
    },
});

//医嘱列表
var hzList = new Vue({
        el: '.hzList',
        mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
        components: {
            'search-table': searchTable,
            'search-table2': searchTable,
            'search-table3': searchTable
        },
        data: {
            haveYe: false,
            mbModel: false,
            yeList: [],
            numClass: null,
            zcyYzList: [],
            isShow: true,
            Init: false,     //初始化标志
            TableInit: false,  //锁表头初始化状态
            Yf_List: [],     //药房列表
            Yzxx_List: [],    //医嘱列表
            Yyff_List: [],    //用药方法
            Pc_List: [],     //频次
            Yzxm_List: [],   //医嘱项目列表
            BrxxJson: {},    //当前住院病人信息
            csContent: {},
            is_ksbm: '',   //当前科室编码
            is_ksmc: '',   //当前科室名称
            is_yfbm: '',   //当前药房编码
            is_yfmc: '',   //当前药房名称

            is_yzlx: '%',   //医嘱类型   %-全部  1-长期,0-临时
            is_yzgl: '0',   //医嘱过滤   %-未停  1-已停,0-作废

            popContent: {},  //选中的值
            selSearch: 0,
            selSearch2: 0,
            selSearch3: 0,
            searchCon3: [],
            searchCon2: [],
            searchCon: [],
            title: '医嘱项目',
            them_tran: {
                'zdksbz': dic_transform.data.zdksbz_tran,
                'nh': dic_transform.data.nh_tran,
                'jclx': dic_transform.data.yzjcfl_tran,
            },
            them: {
                '类型': 'lx',
                '项目编码': 'xmbm',
                '项目名称': 'xmmc',
                '规格': 'xmgg',
                '库存': 'kc',
                '单位': 'yfdwmc',
                '价格': 'cklj',
                '分装比例': 'fzbl',
                '医保': 'ybtclbmc',
                '农合': 'nh',
                '药品种类': 'zlmc',
                '基本剂量': 'jbjl',
                '检查类型': 'jclx',
                '剂量单位': 'jldwbm',
                '剂量单位': 'jldwmc',
                '用药方法': 'yyff',
                '最大剂量': 'zdjl',
                '抗生素级别': 'kssjb',
                '用药方法': 'yyffmc',
                '可拆分': 'kcfbz'
            },
            title2: '用药方法',
            them_tran2: {'zxdlx': dic_transform.data.zxdlx_tran},
            them2: {
                '方法编码': 'yyffbm',
                '方法名称': 'yyffmc',
                '拼音简码': 'pydm',
                '执行单类型': 'zxdlx'
            },
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            pageStr: {
                page: 1,
                rows: 20,
                total: null
            },
            queryStr: {
                page: 1,
                rows: 20,
                total: null
            },
            them_tran3: {'tybz': dic_transform.data.stopSign},
            them3: {
                '频次编码': 'pcbm',
                '频次名称': 'pcmc',
                '拉丁名称': 'ldmc',
                '次数': 'cs',
                '停用标志': 'tybz'
            },
            sfgr_tran: {
                "1": "是",
                "0": "否"
            },
            ifClick: true,
            param: {},
            isShowYzTem: false,
            mbModel: false,
            bcShow: false,
            falg: false,
            _laydate: '',
            isShowLsYz: false,
            printList: [],
            jcList: [],
            jyList: [],
            isChecked: [],
            toolIsSHow: true,
            checkAll: false,
            isCheck: [],
            kssContent: null, //查询到的抗生素
            sfqk: 0, //是否清空
        },

        watch: {
            'Yzxx_List': function (ov, nv) {
                this.height();
                changeWin();
            },
            'isChecked': function () {
                if (this.notempty(this.isChecked).length == this.Yzxx_List.length) {
                    this.isChecked.every(function (el) {
                        if (el === true) {
                            return hzList.isCheckAll = true
                        } else {
                            return hzList.isCheckAll = false
                        }
                    })
                }
            }
        },
        created: function () {
            this.iscf();
            this.yyksSelect()
            this.yyrSelect()
            this.cflxSelect()
            this.yfSelect();
        },
        updated: function () {
            changeWin();
        },
        methods: {
            reCheckChange: function (val) {
                var fzh = this.Yzxx_List[val[1]].fzh;//临时分组号

                for (var i = 0; i < this.Yzxx_List.length; i++) {
                    if (parseInt(fzh) != 0 || parseInt(fzh) != '') {
                        if (fzh == this.Yzxx_List[i].fzh) {
                            Vue.set(this.isChecked, i, !this.isChecked[i]);
                        }
                    } else if (fzh == 0 || fzh == '') {
                        // Vue.set(this.isChecked, i, false);
                        Vue.set(this.isChecked, val[1], val[2]);
                    }
                }
                if (!val[2]) this.isCheckAll = false;
            },
            iscf: function () {
                var parm = {
                    zyh: userNameBg.Brxx_List.zyh
                }
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' +
                    JSON.stringify(parm)
                    , function (json) {
                        if (json.a == '0') {
                            if (json.d.list != null && json.d.list.length > 0) {
                                hzList.haveYe = true;
                                panel.haveYe = true;
                                var qb = {
                                    yexm: "产妇",
                                    yebh: "000",
                                }
                                json.d.list.push(qb);
                                hzList.yeList = json.d.list;
                            } else {
                                hzList.haveYe = false;
                                panel.haveYe = false;
                            }
                        }
                    });
            },
            height: function () {
                var that = this;
                setTimeout(function () {
                    var nextTickHeight = $('.nextTickHeight');
                    for (var i = 0; i < nextTickHeight.length; i++) {
                        nextTickHeight[i].style.height = parseInt(that.$refs.body.style.height.split('px')[0]) - 5 + 'px'
                    }
                }, 1100)
            },
            yzShow: function () {
                this.isShow = true;
                panel.isShow = true;
                cqyzd.isShow = false;
                $('#yzd').addClass('hide');
                panel.ifShow = true;
            },

            pubFzhsame: function (index) {
                if (this.Yzxx_List[index].fzh > 0) {
                    //同组药品用药方法，频次，输液速度，输液速度单位同样改变
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        if (this.Yzxx_List[index].fzh == this.Yzxx_List[i].fzh) {
                            this.Yzxx_List[i].yyff = this.Yzxx_List[index].yyff;
                            this.Yzxx_List[i].yyffmc = this.Yzxx_List[index].yyffmc;
                            this.Yzxx_List[i].pcbm = this.Yzxx_List[index].pcbm;
                            this.Yzxx_List[i].pcmc = this.Yzxx_List[index].pcmc;
                            this.Yzxx_List[i].sysd = this.Yzxx_List[index].sysd;
                            this.Yzxx_List[i].sysddw = this.Yzxx_List[index].sysddw;
                        }
                    }
                }
            },
            //查询抗生素申请记录
            getKssSq: function (item) {
                if (panel.is_csqx.cs003003200118 == '0') {
                    var ypbm = null;
                    if (item.xmbm) {
                        ypbm = item.xmbm;
                    } else {
                        ypbm = item.ypbm;
                    }
                    var json = {
                        rows: 50000,
                        //spbz: '1',
                        zyh: userNameBg.Brxx_List.zyh,
                        ypbm: ypbm
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywKsssyba&types=query&parm=' + JSON.stringify(json), function (data) {
                        if (data.a == 0) {
                            if (data.d.list.length > 0) {
                                hzList.kssContent = data.d.list[0];
                            }
                            hzList.pubKss(item);

                        } else {
                            malert("获取项目列表失败！", data.c, 'top', 'defeadted');
                        }
                    });
                }

            },

            //针对抗生素药物的判定（住院医生（04）一级（2），主治医生（03）一（2）、二（3）级，副主任医师（02）和主任医师（01）一（2）、二（3）、三（4）级）
            pubKss: function (json) {
                hzList.sfqk = 0;
                var ypmc = '';
                if (!json['ypmc']) {
                    ypmc = json['xmmc'];
                } else {
                    ypmc = json['ypmc'];
                }
                if (window.top.J_tabLeft.jszgdm == '04' || window.top.J_tabLeft.jszgdm == null) {
                    if (json.kssjb == '3' || json.kssjb == '4') {
                        if (hzList.kssContent == null || hzList.kssContent == '') {
                            malert(ypmc + "属于" + hzList.kssjb_tran[json['kssjb']] + "不允许使用", 'top', 'defeadted');
                            hzList.sfqk = 1;
                            return false;
                        } else if (isthzL.kssContent.spbz != 1) {
                            malert(ypmc + "属于" + hzList.kssjb_tran[json['kssjb']] + "," + hzList.sfsp_tran[isthzL.kssContent.spbz] + "不允许使用", 'top', 'defeadted');
                            hzList.sfqk = 1;
                            return
                        }
                    }
                }
                if (window.top.J_tabLeft.jszgdm == '03') {
                    if (json.kssjb == '4') {
                        if (hzList.kssContent == null || hzList.kssContent == '') {
                            malert(ypmc + "属于" + hzList.kssjb_tran[json['kssjb']] + "不允许使用", 'top', 'defeadted');
                            hzList.sfqk = 1;
                            return false;
                        }
                    }
                }

            },
            //医嘱单显示
            printJcShow: function () {
                this.isShow = false;
                panel.isShow = false;
                cqyzd.isShow = true;
                $('#yzd').removeClass('hide')
                panel.ifShow = false;
            },
            //打印医嘱单
            doPrint: function (istpye) {
                cqyzd.doPrint(istpye);
            },
            openJcjy: function () {
                jcjyfy.show();
                jcjyfy.getJcjyd();
            },

            //************************打印检查检验申请单start***************************
            printJcJy: function () {
                userNameBg.Brxx_List.brxb = hzList.brxb_tran[userNameBg.Brxx_List.brxb];
                userNameBg.Brxx_List.nldw = hzList.nldw_tran[userNameBg.Brxx_List.nldw];
                // 获取选中检查检验的信息
                var jcList = [], jyList = [];
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            if (this.Yzxx_List[i]['jcfl'] == '1') {
                                jcList.push(this.Yzxx_List[i]);
                            } else if (this.Yzxx_List[i]['jcfl'] == '2') {
                                jyList.push(this.Yzxx_List[i]);
                            }
                        }
                    }
                } else {
                    malert('请选择需要打印的申请单', 'top', 'defeadted');
                    return
                }
                if (jcList.length == 0 && jyList.length == 0) {
                    malert('没有检查检验的项目', 'top', 'defeadted');
                    return
                }
                if (jcList.length != 0) {
                    userNameBg.Brxx_List.jcms = jcList[0].jcms;
                    userNameBg.Brxx_List.lczd = jcList[0].lczd;
                    userNameBg.Brxx_List.lczz = jcList[0].lczz;
                    userNameBg.Brxx_List.bzsm = jcList[0].yysm;
                    userNameBg.Brxx_List.zxksmc = jcList[0].zxksmc;
                    userNameBg.Brxx_List.yzlx = hzList.yzlx_tran[jcList[0].yzlx];
                    var date = new Date();
                    var dyrq_n = hzList.fDate(date, 'year');
                    var dyrq_y = hzList.fDate(date, 'month');
                    var dyrq_r = hzList.fDate(date, 'day');
                    var dyrq_sf = hzList.fDate(date, 'times');
                    userNameBg.Brxx_List.n = dyrq_n;
                    userNameBg.Brxx_List.y = dyrq_y;
                    userNameBg.Brxx_List.r = dyrq_r;
                    userNameBg.Brxx_List.sf = dyrq_sf;
                    console.log(userNameBg.Brxx_List);
                    if (jcList.length <= 3) {
                        this.doPrintJc(jcList);
                    } else {
                        for (var i = 0; i < jcList.length; i += 3) {
                            var xjsonlist = jcList.slice(i, i + 3);
                            this.doPrintJc(xjsonlist);
                        }
                    }
                }
                if (jyList.length != 0) {
                    userNameBg.Brxx_List.jymd = jyList[0].jymd;
                    userNameBg.Brxx_List.lczd = jyList[0].lczd;
                    userNameBg.Brxx_List.bbsm = jyList[0].bbsm;
                    userNameBg.Brxx_List.bzsm = jyList[0].yysm;
                    userNameBg.Brxx_List.zxksmc = jyList[0].zxksmc;
                    userNameBg.Brxx_List.yzlx = hzList.yzlx_tran[jyList[0].yzlx];
                    var date = new Date();
                    var dyrq_n = hzList.fDate(date, 'year');
                    var dyrq_y = hzList.fDate(date, 'month');
                    var dyrq_r = hzList.fDate(date, 'day');
                    var dyrq_sf = hzList.fDate(date, 'times');
                    userNameBg.Brxx_List.n = dyrq_n;
                    userNameBg.Brxx_List.y = dyrq_y;
                    userNameBg.Brxx_List.r = dyrq_r;
                    userNameBg.Brxx_List.sf = dyrq_sf;
                    if (jyList.length <= 3) {
                        this.doPrintJy(jyList);
                    } else {
                        for (var i = 0; i < jyList.length; i += 3) {
                            var xjsonlist = jyList.slice(i, i + 3);
                            this.doPrintJy(xjsonlist);
                        }
                    }
                }

            },
            // 打印住院检验申请单
            doPrintJy: function (jyList) {
                // 查询打印模板
                var json = {repname: '住院检验申请单'};
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    // 清除打印区域
                    hzList.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    hzList.drawList = JSON.parse(json.d[0]['canvas']);
                    hzList.creatCanvas();
                    hzList.reDraw();
                    // 为打印前生成数据
                    hzList.printContent(userNameBg.Brxx_List);
                    hzList.printTrend(jyList);
                    // 开始打印
                    window.print();
                });
            },
            // 打印住院检查申请单
            doPrintJc: function (jcList) {
                // 查询打印模板
                var json = {repname: '住院检查申请单'};
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    // 清除打印区域
                    hzList.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    hzList.drawList = JSON.parse(json.d[0]['canvas']);
                    hzList.creatCanvas();
                    hzList.reDraw();
                    // 为打印前生成数据
                    var reg = /^[\'\"]+|[\'\"]+$/g;
                    userNameBg.Brxx_List.czyxm = sessionStorage.getItem("userName" + userId).replace(reg, '');
                    hzList.printContent(userNameBg.Brxx_List);
                    hzList.printTrend(jcList);
                    // 开始打印
                    window.print();
                });
            },
            //************************打印检查检验申请单end***************************

            //修改医属
            Wf_update: function (index) {
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }
                if (this.Yzxx_List[index].shbz == '0') {
                    if (this.Yzxx_List[index].lx == "诊疗") { // 检查检验医嘱点击修改是弹出检查检验模板来选择的
                        this.openJcjy();
                    } else {
                        this.Yzxx_List[index].readonly = false;   //只读标志
                        this.Yzxx_List[index].updateBz = true;   //修改标志
                    }
                } else {
                    malert("第" + index + "条医嘱已审核，不能修改！", 'top', 'defeadted');
                }
            },
            //删除医嘱
            Wf_delete: function (index) {
                this.falg = false
                $.ajaxSettings.async = false;
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return;
                }
                if (index != null) {
                    this.isChecked = [];
                    this.isChecked[index] = true;
                }
                if (this.isChecked.length > 0) {
                    common.openConfirm("你确定要删除所选医嘱信息吗？", this.deleteyz, function () {
                        hzList.isChecked = []
                    })
                } else {
                    malert("请选中需要删除的医嘱信息！", 'top', 'defeadted');

                }
            },
            deleteyz: function () {
                var deleteYzList = [];
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        if (hzList.Yzxx_List[i].sfcy == '0' || hzList.Yzxx_List[i].sfcy == null || hzList.Yzxx_List[i].sfcy == undefined) {
                            if (hzList.Yzxx_List[i].insertBz == true) {
                                hzList.Yzxx_List.splice(i, 1);  //新增加的直接删除
                                continue;
                            }
                            if (hzList.Yzxx_List[i].zt != '2') {
                                this.falg = true
                                deleteYzList.push(hzList.Yzxx_List[i]);
                            }
                        } else {
                            // common.openConfirm("你确定要删除第【" + (i) + "】行的医嘱信息吗？", deledate)
                            // function deledate() {
                            var parm = {
                                zyh: userNameBg.Brxx_List.zyh,
                                yzxh: hzList.Yzxx_List[i].yzxh,
                                page: 1,
                                rows: 200
                            };
                            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=queryZyyz" + '&parm=' + JSON.stringify(parm), function (json) {
                                if (json.a == 0) {
                                    hzList.zcyYzList = json.d.list;
                                } else {
                                    malert("中草药列表查询失败" + json.c, 'top', 'defeadted');

                                }
                            });
                            for (var j = 0; j < hzList.zcyYzList.length; j++) {
                                if (hzList.zcyYzList[j].zfbz == '0') {
                                    this.falg = true
                                    deleteYzList.push(hzList.zcyYzList[j]);
                                }
                            }
                            // }
                        }
                    }
                }
                this.isChecked = [];
                this.isCheckAll = false;
                //没有记录就直接返回
                if (this.falg == true) {
                    if (deleteYzList.length <= 0) {
                        malert("未找到允许作废的医嘱信息", 'top', 'defeadted');
                        return false;
                    }
                    var json = {
                        list: [
                            {
                                yzxx: deleteYzList,          //医嘱信息
                                lczd: []              //诊断信息（检查，检验的诊断）
                            }
                        ]
                    };
                    this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzdelete', JSON.stringify(json)).then(function (data) {
                        if (data.body.a == 0) {
                            hzList.Wf_selectYZ();  //查询医嘱
                            malert("医嘱作废成功！", 'top', 'success');
                            this.isChecked = [];
                        } else {
                            malert("医嘱作废失败：" + data.body.c, 'top', 'defeadted');
                        }
                    });
                }
            },
            //停医嘱
            Wf_stop: function (index) {
                var deleteYzList = [];
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }
                if (index != null) {
                    this.isChecked = [];
                    this.isChecked[index] = true;
                }
                var sfts = true;
                if (this.isChecked.length > 0) {
                    for (var list in this.isChecked) {
                        if (this.isChecked[list] == true) {
                            if (this.Yzxx_List[list].rqxgbz == null) {
                                this.Yzxx_List[list].rqxgbz = '0';
                            }
                            if (this.Yzxx_List[list].shbz == '1') {
                                if (this.Yzxx_List[list].zt == '3') {
                                    deleteYzList.push(JSON.parse(JSON.stringify(this.Yzxx_List[list])))
                                }
                            } else {
                                malert("第" + list + "条医嘱未审核，请直接作废！");
                            }

                        }
                    }
                } else {
                    malert("请选择需要停的医嘱！", 'top', 'defeadted');
                    return false;
                }
                //没有记录就直接返回
                if (deleteYzList.length <= 0) {
                    malert("未找到允许停嘱的信息", 'top', 'defeadted');
                    return false;
                }
                for (var j = 0; j < deleteYzList.length; j++) {
                    if (deleteYzList[j].rqxgbz == '1') {
                        sfts = false;
                    }
                    if (deleteYzList[j].rqxgbz == '0') {
                        sfts = true;
                        break;
                    }
                }

                //没有记录就直接返回
                var json = {
                    list: [
                        {
                            yzxx: deleteYzList,          //医嘱信息
                            lczd: []              //诊断信息（检查，检验的诊断）
                        }
                    ]
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzstop', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        hzList.Wf_selectYZ();  //查询医嘱
                        malert('医嘱停止申请成功', 'top', 'success');
                        this.isChecked = [];
                    } else {
                        malert("医嘱停止申请失败:" + data.body.c, 'top', 'defeadted');
                    }
                });

            },

            //取消停医嘱
            cancelStopYz: function (index) {
                var deleteYzList = [];
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }
                if (index != null) {
                    this.isChecked = [];
                    this.isChecked[index] = true;
                }
                if (this.isChecked.length > 0) {
                    for (var list in this.isChecked) {
                        if (this.isChecked[list] == true) {
                            if (this.Yzxx_List[list].ystzbz == '1') {
                                deleteYzList.push(JSON.parse(JSON.stringify(this.Yzxx_List[list])))
                            }
                        }
                    }
                } else {
                    malert("请选择需要取消停嘱的信息！", 'top', 'defeadted');
                    return false;
                }
                //没有记录就直接返回
                var json = {
                    list: [
                        {
                            yzxx: deleteYzList,          //医嘱信息
                            lczd: []              //诊断信息（检查，检验的诊断）
                        }
                    ]
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzstopqx', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        hzList.Wf_selectYZ();  //查询医嘱
                        malert('医嘱取消停嘱成功', 'top', 'success');
                    } else {
                        malert("医嘱取消停嘱失败:" + data.body.c, 'top', 'defeadted');
                    }
                });

            },

            tsypPrint: function () {
                var jcxhid = '', str = '', xhid = "xhid:'";
                let tsyp = '';
                if (this.isChecked.length != 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            jcxhid += "yzxh:'" + this.Yzxx_List[i]['yzxh'] + ",";
                            xhid += this.Yzxx_List[i]['xhid'] + ",";
                            tsyp = this.Yzxx_List[i]['tsyp']
                        }
                    }
                    jcxhid = jcxhid.substr(0, jcxhid.length - 1);
                    xhid = xhid.substr(0, xhid.length - 1);
                    // str += "{reportlet: 'fpdy%2Fzyys%2Ftsyp.cpt'," + jcxhid + "',"+xhid+"'}";
                    if (tsyp == '2') {
                        str += "{reportlet: 'fpdy%2Fzyys%2Ftsyp.cpt'," + jcxhid + "'," + xhid + "'}";
                    } else {
                        str += "{reportlet: 'fpdy%2Fzyys%2Ftsypmj.cpt'," + jcxhid + "'," + xhid + "'}";
                    }
                    reportlets = '[' + str + ']';
                    if (FrPrint(reportlets,null,null,true)) {
                        return;
                    }
                } else {
                    malert('请选择需要打印的特殊药品', 'top', 'defeadted')
                }
            },
            //保存医嘱
            Wf_saveYZ: function () {
                $.ajaxSettings.async = false;
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }

                if (!this.ifClick) return; //如果为false表示已经点击了不能再点
                this.ifClick = false;
                var sfts = true;
                var dmjslyp = false;
                //var tzyz = false;//针对医嘱类型是否停嘱
                try {
                    if (this.Yzxx_List.length <= 0) return;   //没有数据就直接返回
                    var yzList = [];   //用于提交的医嘱信息List
                    //循环判断每条医嘱是否正常
                    console.log(this.Yzxx_List);
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        var insertBz = this.Yzxx_List[i].insertBz;  //新增加标志
                        var updateBz = this.Yzxx_List[i].updateBz;  //修改标志
                        if (this.Yzxx_List[i].rqxgbz == null) {
                            this.Yzxx_List[i].rqxgbz = '0';
                        }
                        if ((insertBz == null || insertBz == false) && (updateBz == null || updateBz == false)) continue;  //如果不是修改也不是新增加就跳出本次循环进入下一次
                        //医嘱编码为空的直接删除
                        if (this.Yzxx_List[i].tsyz == true) {
                            this.Yzxx_List[i].xmbm = this.Yzxx_List[i].xmmc;
                        }
                        if (!this.Yzxx_List[i].xmbm) {
                            //this.Yzxx_List.splice(i, 1);  //直接删除
                            continue;
                        }
                        //抗生素只能是临时医嘱需要进行判断
                        // if (this.Yzxx_List[i].kssjb) {
                        //     if (this.Yzxx_List[i].kssjb != '1' && this.Yzxx_List[i].kssjb != '0') {
                        //         if (this.Yzxx_List[i].yzlx == '1') {
                        //             malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的属于抗生素【医嘱类型】不能长期！", 'top', 'defeadted');
                        //             this.ifClick = true;
                        //             return;
                        //         }
                        //     }
                        // }

                        //医嘱类型判断
                        if (!this.Yzxx_List[i].yzlx) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【医嘱类型】不能为空！", 'top', 'defeadted');
                            this.ifClick = true;
                            return;
                        }
                        //医嘱分类
                        if (!this.Yzxx_List[i].yzfl) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【医嘱分类】不能为空！", 'top', 'defeadted');
                            this.ifClick = true;
                            return;
                        }
                        //医嘱日期
                        if (!this.Yzxx_List[i].ksrq) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【医嘱日期】不能为空！", 'top', 'defeadted');
                            this.ifClick = true;
                            return;
                        }
                        //医嘱名称
                        if (!this.Yzxx_List[i].xmmc) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【医嘱日期】不能为空！", 'top', 'defeadted');
                            return;
                        }
                        //药品还是诊疗
                        if (!this.Yzxx_List[i].lx) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【医嘱分类(药品，诊断)】不能为空！", 'top', 'defeadted');
                            this.ifClick = true;
                            return;
                        }

                        if (this.Yzxx_List[i].yebh != null && this.Yzxx_List[i].yebh != '000') {
                            this.Yzxx_List[i].xseyzbz = '1';
                        }

                        if (this.Yzxx_List[i].yebh == '000') {
                            this.Yzxx_List[i].yebh = null;
                        }

                        //药品
                        if (this.Yzxx_List[i].lx == '药品') {
                            //单次剂量
                            if (!this.Yzxx_List[i].dcjl || this.Yzxx_List[i].dcjl <= 0) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【单次剂量】必需大于0！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }
                            //用药方法
                            if (!this.Yzxx_List[i].yyffbm) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【用药方法】不能为空！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }
                            //用药方法名称
                            if (!this.Yzxx_List[i].yyffmc) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【用药方法名称】不能为空！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }
                            //频次
                            if (!this.Yzxx_List[i].pcbm) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【频次】不能为空！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }

                            //用药天数
                            if (!this.Yzxx_List[i].yyts || this.Yzxx_List[i].yyts <= 0) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【用药天数】必需大于0！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }

                            //毒麻精神类标志
                            if (this.Yzxx_List[i].dmypbz == '1' || this.Yzxx_List[i].jslypbz == '1' || this.Yzxx_List[i].jslypbz == '2') {
                                this.Yzxx_List[i].dmjslyp = true;
                                dmjslyp = true;
                            }
                        }
                        //医嘱总量
                        // if (this.Yzxx_List[i].sl == null || this.Yzxx_List[i].sl == undefined || this.Yzxx_List[i].sl <= 0) {
                        //     malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【医嘱总量】必需大于0！", 'top', 'defeadted');
                        //     this.ifClick = true;
                        //     return;
                        // }
                        //处理各种标志
                        if (this.Yzxx_List[i].lx == '药品') {
                            this.Yzxx_List[i].ypbz = '1';
                        } else {
                            this.Yzxx_List[i].ypbz = '0';
                        }

                        yzList.push(this.Yzxx_List[i]);  //增加到保存医嘱json中
                    }  //循环完
                    if (yzList.length <= 0) {
                        malert("没有可保存的医嘱项目 ！", 'top', 'defeadted');
                        this.ifClick = true;
                        return;
                    }
                    for (var i = 0; i < yzList.length; i++) {
                        if (yzList[i].tsyz) {
                            yzList[i].tsyz = '1';
                        } else {
                            yzList[i].tsyz = '0';
                        }
                    }
                    for (var j = 0; j < yzList.length; j++) {
                        if (yzList[j].rqxgbz == '1') {
                            sfts = false;
                        }
                        if (yzList[j].rqxgbz == '0') {
                            sfts = true;
                            break;
                        }
                    }

                    /* //判断是否需要停掉之前所有未停医嘱
                     for (var i = 0; i < yzList.length; i++) {
                         if (yzList[i].yzfl == '1' ||yzList[i].yzfl == '2' ||yzList[i].yzfl == '3' || yzList[i].yzfl == '4' || yzList[i].yzfl == '5') {
                             tzyz = true;
                             break;
                         }
                     }

                     //执行停止所有未停医嘱
                     if (tzyz) {
                         var wtyzList = [];
                         var ksbm = userNameBg.Brxx_List.ryks;   //科室
                         var zyh = userNameBg.Brxx_List.zyh;  //住院号
                         var yzlx = '';
                         var ystzbz = '0';
                         var parm_ksbm = {
                             ksbm: ksbm,
                             yzlx: yzlx,
                             ystzbz: ystzbz,
                         };
                         var parm_zyh = [{
                             zyh: zyh
                         }];
                         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzxx&parm=' + JSON.stringify(parm_ksbm) + '&zyh=' + JSON.stringify(parm_zyh)
                             , function (json) {
                                 if (json.a == '0') {
                                     wtyzList = json.d.list;
                                 }
                             });

                         if (wtyzList.length < 1) {
            //                    	break;
                         } else {
                             var tzjson = {
                                 list: [
                                     {
                                         yzxx: wtyzList,          //医嘱信息
                                         lczd: []              //诊断信息（检查，检验的诊断）
                                     }
                                 ]
                             };
                             this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzstop', JSON.stringify(tzjson)).then(function (data) {
                                 if (data.body.a == 0) {
                                     malert("医嘱停止申请成功",'top','success');
                                 } else {
                                     malert("医嘱停止申请失败：" + data.body.c, 'top', 'defeadted');
                                 }
                             });
                         }
                     }*/

                    //弹出毒麻实名认证信息
                    if (!panel.csqxContent.N03012200105 == false && panel.csqxContent.N03012200105 == "1" && dmjslyp) {
                        // if (!dbrPop.isPopUped){
                        dbrPop.show();
                        dbrPop.yzList = yzList;
                        // }
                    } else {
                        hzList.final_saveYz(yzList);
                    }

                } catch (e) {
                    malert("医嘱保存异常" + e.message, 'top', 'defeadted');
                    hzList.ifClick = true;
                }
            },

            final_saveYz: function (yzList) {
                try {
                    var json = {
                        list: [
                            {
                                hzxx: userNameBg.Brxx_List,   //病人基本信息
                                yzxx: yzList,          //医嘱信息
                                lczd: []              //诊断信息（检查，检验的诊断）
                            }
                        ]
                    };
                    this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzsave', JSON.stringify(json)).then(function (data) {
                        if (data.body.a == 0) {
                            hzList.Wf_selectYZ();  //查询医嘱
                            hzList.ifClick = true;
                            malert("医嘱保存成功", 'top', 'success');
                        } else {
                            malert("医嘱保存失败" + data.body.c, 'top', 'defeadted');
                            hzList.ifClick = true;
                        }
                    });
                } catch (e) {
                    malert("医嘱保存异常" + e.message, 'top', 'defeadted');
                    hzList.ifClick = true;
                }
            },

            //保存医嘱模板
            saveYzmb: function () {

                var yzmbList = [];
                for (var i = 0; i < hzList.isChecked.length; i++) {
                    if (hzList.isChecked[i]) {
                        yzmbList.push(this.Yzxx_List[i]);
                    }
                }
                if (!this.mbZhyzContent.zhyzmc) {
                    malert("医嘱名称不能为空！", "top", "defeadted");
                    return;
                }
                if (!this.mbZhyzContent.zhyzlx) {
                    malert("医嘱类型不能为空！", "top", "defeadted");
                    return;
                }
                if (!this.mbZhyzContent.cflxbm) {
                    malert("处方类型不能为空！", "top", "defeadted");
                    return;
                }
                if (!this.mbZhyzContent.yfbm) {
                    malert("药房不能为空！", "top", "defeadted");
                    return;
                }
                var json = {
                    list: [
                        {
                            zhyz: this.mbZhyzContent,   //组合医嘱
                            zhyzmx: yzmbList,          //组合医嘱明细
                        }
                    ]
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=saveYhyzAndMx', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        malert(data.body.c);
                        hzList.mbModel = false;
                        hzList.bcShow = false;
                        hzList.mbZhyzContent = {};
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                }, function (error) {
                    malert(data.body.c, 'top', 'defeadted');
                    console.log(error);
                });
            },

            //保存为模板(新加代码)
            openModel: function () {
                var goOn = false;
                loop:
                    for (var i = 0; i < hzList.isChecked.length; i++) {
                        if (hzList.isChecked[i]) {
                            goOn = true;
                            break loop;
                        }
                    }
                if (!goOn) {
                    malert("请选中要保存为模板的医嘱！", "top", "defeadted");
                    return;
                }
                this.mbZhyzContent = {};
                this.mbZhyzContent.yyks = ksbm;
                this.mbZhyzContent.yyz = userId;
                this.mbZhyzContent.sfcy = '0';
                this.mbZhyzContent.lx = '2';
                this.mbZhyzContent.zhyzlx = '0';
                this.mbZhyzContent.cflxbm = hzList.cflxbm;
                this.mbZhyzContent.ypbz = '0';
                this.mbZhyzContent.yfbm = panel.popContent.yfbm;
                this.mbModel = true;
                this.bcShow = true;
                $("#yzmc").focus();
            },

            resultChange_zxks: function (val) {
                this[val[2][0]] = val[0];
                this.updateZxksmc = hzList.listGetName(hzList.zxksArr, this.updateZxks, 'ksbm', 'ksmc');
            },

            //下拉框科室加载
            yyksSelect: function () {
                this.$http.get("/actionDispatcher.do", {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: 'ksbm',
                        dg: JSON.stringify(this.page_str)
                    }
                }).then(function (json, status, xhr) {
                    if (json.body.a == 0 && json.body.d) {
                        hzList.ksbmList = json.body.d.list;
                    } else {
                        malert("拥有科室获取失败：" + json.c, 'top', 'defeadted');
                    }
                })
            },

            //下拉框处方类型加载
            cflxSelect: function () {

                this.$http.get("/actionDispatcher.do", {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: 'cflx',
                        dg: JSON.stringify(this.page_str)
                    }
                }).then(function (json, status, xhr) {
                    if (json.body.a == 0 && json.body.d) {
                        hzList.cflxList = json.body.d.list;
                        hzList.cflxbm = json.body.d.list[0].cflxbm;
                    } else {
                        malert("处方类型获取失败：" + json.c, 'top', 'defeadted');
                    }
                })
            },

            exitYzmb: function () {
                this.mbModel = false;
                this.bcShow = false;
            },

            //下拉框人员加载
            yyrSelect: function () {

                this.$http.get("/actionDispatcher.do", {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: 'rybm',
                        dg: JSON.stringify(this.page_str)
                    }
                }).then(function (json, status, xhr) {
                    if (json.body.a == 0 && json.body.d) {
                        hzList.rybmList = json.body.d.list;
                    } else {
                        malert("拥有人员获取失败：" + json.c, 'top', 'defeadted');
                    }
                })
            },

            //下拉框药房加载
            yfSelect: function () {

                this.$http.get("/actionDispatcher.do", {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: 'yf',
                        dg: JSON.stringify(this.page_str)
                    }
                }).then(function (json, status, xhr) {
                    if (json.body.a == 0 && json.body.d) {
                        hzList.yfList = json.body.d.list;
                    } else {
                        malert("药房获取失败：" + json.c, 'top', 'defeadted');
                    }
                })
            },

            //新增加医嘱
            Wf_addYZ: function () {
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }
                var xxssx = null;
                if (hzList.Yzxx_List.length > 0) {
                    xxssx = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].xssx + 1;
                    if (hzList.Yzxx_List[hzList.Yzxx_List.length - 1].xmmc == '') {
                        malert("数据格式有问题，请正确输入医嘱名称", 'top', 'defeadted');
                        return false;
                    }
                    if (panel.is_csqx.N03003200119 == 1) {
                        hzList.Yzxx_List = [];
                    }
                } else {
                    xxssx = 1;
                }
                var yebh = '';
                var ksrq = getTodayDateTime();
                var fzh = null;
                var yyffmc = null;
                var yyffbm = null;
                var pcmc = null;
                var pcbm = null;
                var pccs = null;
                var gllb = null;
                var rqxgbz = '0';
                yyffcz = false;
                pccz = false;
                if (hzList.Yzxx_List.length > 0 && hzList.Yzxx_List[hzList.Yzxx_List.length - 1].insertBz) {
                    yebh = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].yebh;
                    ksrq = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].ksrq;
                    fzh = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].fzh;
                    gllb = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].gllb;
                    rqxgbz = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].rqxgbz;
                }
                if (fzh > 0 && hzList.Yzxx_List.length > 0) {
                    yyffmc = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].yyffmc;
                    yyffbm = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].yyffbm;
                    pcmc = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].pcmc;
                    pcbm = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].pcbm;
                    pccs = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].pccs;
                    gllb = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].gllb;
                    rqxgbz = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].rqxgbz;
                    yyffcz = true;
                    pccz = true;
                }
                var yzxx = {
                    insertBz: true,
                    updateBz: false,
                    readonly: false,
                    xssx: xxssx,
                    yzlx: '0',
                    yebh: yebh,
                    yzfl: userNameBg.csqx.N03012200103,
                    ksrq: ksrq,
                    fzh: fzh,
                    ysqmks: userNameBg.Brxx_List.ssks,
                    ysqmksmc: userNameBg.Brxx_List.ssksmc,
                    xmbm: '',
                    xmmc: '',
                    yyffbm: yyffbm,
                    yyffmc: yyffmc,
                    pcmc: pcmc,
                    pcbm: pcbm,
                    pccs: pccs,
                    gllb: gllb,
                    zt: '4',
                    rqxgbz: rqxgbz,
                };
                // if (!zdtj) {
                //赋值
                if (fzh > 0) {
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                            yzxx.sysddw = this.Yzxx_List[i].sysddw;
                            yzxx.sysd = this.Yzxx_List[i].sysd;
                        }
                    }
                }
                if (hzList.isChecked.length > 0) {
                    if (hzList.Yzxx_List[hzList.isChecked.length]['zt'] == '4') {
                        hzList.Yzxx_List.splice(hzList.isChecked.length + 1, 0, yzxx);
                    } else {
                        hzList.Yzxx_List.push(yzxx);
                    }
                } else {
                    hzList.Yzxx_List.push(yzxx);
                }
                var cou = hzList.Yzxx_List.length - 1;
                setTimeout(function () {   //延时0.1秒执行
                    if (hzList.Yzxx_List[cou].fzh && hzList.Yzxx_List[cou].fzh != 0 && hzList.Yzxx_List[cou].fzh == hzList.Yzxx_List[hzList.Yzxx_List.length - 2].fzh) {
                        $("#xmmc_" + cou).focus();
                    }

                }, 100);
            },
            showDate: function (index, event) {
                var elm = '.startdate' + index
                this._laydate = {
                    elem: elm
                    , show: true //直接显示
                    , type: 'datetime'
                    , theme: '#1ab394',
                    done: function (value, data) {
                        hzList.Yzxx_List[index]['ksrq'] = value
                        hzList.Yzxx_List[index]['rqxgbz'] = '1';
                    }
                }
                laydate.render(this._laydate)//初始化时间插件
            },


            // 显示医嘱模板
            showYzTem: function () {
                brzcList.yzList = [];
                brzcList.sfcy = '0';
                brzcList.getTemData();
                brzcList.num = 0;
            }
            ,


            // 显示历史医嘱
            showLsYz: function () {
                brzcList.getLsTemData();
                brzcList.isfzcfMxChecked = [];
                brzcList.num = 0;
            }
            ,
            commonResultChange: function (val) {
                var type = val[2][val[2].length - 1];
                switch (type) {
                    // case "yzlx":
                    //     Vue.set(this.param, 'yzlx', val[0]);
                    //     brzcajList.getTemData();
                    //     break;
                    // case "ypbz":
                    //     Vue.set(this.param, 'ypbz', val[0]);
                    //     brzcajList.getTemData();
                    //     break;
                    case "sfgr":
                        Vue.set(this.param, 'sfgr', val[0]);
                        if (val[0] && val[0] != '0') {
                            Vue.set(this.param, 'yyz', userId);
                        } else {
                            Vue.set(this.param, 'yyz', '');
                        }

                        brzcajList.getTemData();
                        break;
                    // case "sfhzyz":
                    //     Vue.set(this.param, 'sfhzyz', val[0]);
                    //     if (val[0] == '1') {
                    //         brzcajList.getHzyzTemData(brzcajList.mxIndex);
                    //     } else {
                    //         brzcajList.getLsTemMxData(brzcajList.mxIndex, null);
                    //     }
                    //     break;
                }
            },
            // 表格的下拉框赋值
            resultChange_item: function (val) {
                Vue.set(this.Yzxx_List[val[2][0]], [val[2][1]], val[0]);
                //调用相同分组号时处理方法
                // , parseInt(val[2][2])
                this.nextFocus(val[1]);
                this.pubFzhsame(val[2][0]);
                this.nextFocusLeft(event, 1)
            }
            ,
            //分组号

            resultChange_item_fzh: function (val) {
                console.log(val);
                yyffcz = false;
                pccz = false;
                Vue.set(this.Yzxx_List[val[2][0]], [val[2][1]], val[0]);
                if ([val[2][1]] == 'yzfl') {
                    if (val[0] == '3') {
                        $('#green' + val[2][0]).click()
                        Vue.set(this.Yzxx_List[val[2][0]], 'tsyz', true);
                        Vue.set(this.Yzxx_List[val[2][0]], 'xmmc', '术后医嘱');
                        setTimeout(function () {
                            hzList.Wf_addYZ();
                        }, 500)
                    }
                } else {
                    Vue.set(this.Yzxx_List[val[2][0]], 'tsyz', false);
                }
                if ([val[2][1]] == "fzh") {
                    if (val[0] > 0) {
                        var change = false;
                        for (var i = 0; i < this.Yzxx_List.length; i++) {
                            if (val[0] > 0 && this.Yzxx_List[i].fzh > 0 && this.Yzxx_List[i].fzh == val[0]) {
                                this.Yzxx_List[val[2][0]].yyffbm = this.Yzxx_List[i].yyffbm;
                                this.Yzxx_List[val[2][0]].yyffmc = this.Yzxx_List[i].yyffmc;
                                this.Yzxx_List[val[2][0]].gllb = this.Yzxx_List[i].gllb;
                                this.Yzxx_List[val[2][0]].pcbm = this.Yzxx_List[i].pcbm;
                                this.Yzxx_List[val[2][0]].pcmc = this.Yzxx_List[i].pcmc;
                                this.Yzxx_List[val[2][0]].pccs = this.Yzxx_List[i].pccs;
                                this.Yzxx_List[val[2][0]].sysd = this.Yzxx_List[i].sysd;
                                this.Yzxx_List[val[2][0]].sysddw = this.Yzxx_List[i].sysddw;
                            }
                        }
//                    if (!change) {
//                        this.Yzxx_List[val[2][0]].yyff = null;
//                        this.Yzxx_List[val[2][0]].yyffmc = null;
//                        this.Yzxx_List[val[2][0]].pcbm = null;
//                        this.Yzxx_List[val[2][0]].pcmc = null;
//                        this.Yzxx_List[val[2][0]].sysd = null;
//                        this.Yzxx_List[val[2][0]].sysddw = null;
//                    }
                    } else {
                        this.Yzxx_List[val[2][0]].yyffbm = null;
                        this.Yzxx_List[val[2][0]].yyffmc = null;
                        this.Yzxx_List[val[2][0]].gllb = null;
                        this.Yzxx_List[val[2][0]].pcbm = null;
                        this.Yzxx_List[val[2][0]].pcmc = null;
                        this.Yzxx_List[val[2][0]].pccs = null;
                        this.Yzxx_List[val[2][0]].sysd = null;
                        this.Yzxx_List[val[2][0]].sysddw = null;

                    }
                }
                //调用相同分组号时处理方法
                // , parseInt(val[2][2])
                this.nextFocus(val[1]);
            }
            ,
            //下拉框检索
            //药品
            Wf_changeDown: function (index, event, type, value) {

                if (hzList.Yzxx_List[index].tsyz == true) {
                    //嘱吒医嘱回车
                    // if(value!=''){
                    if (event.keyCode == 13) {
                        hzList.Wf_addYZ();
                    }
                    return false;
                    // }
                }
                qjIndex = index;
                var yfbm = panel.popContent.yfbm;
                if (!yfbm) {
                    malert("请选择药房!", 'top', 'defeadted');
                    return false
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.searchCon[this.selSearch] == undefined && value == '') return;
                this.keyCodeFunction(event, 'popContent', 'searchCon');
                //赋值
                if (event.keyCode == 13) {
                    if (this.popContent == undefined) {
                        // Vue.set(hzList.Yzxx_List[index], 'tsyz', true)
                        $('#green' + index).click()
                        $(".selectGroup").hide();
                        return false
                    }
                    if (change) {
                        //this.getKssSq(this.popContent); //对抗生素药物使用的判定
                        console.log(this.popContent);
                        //下拉框回车后回调.
                        var sfcy = '0';
                        var ysqmks = userNameBg.Brxx_List.ssks;
                        var ysqmksmc = userNameBg.Brxx_List.ssksmc;
                        var xmbm = this.popContent.xmbm;
                        var bqdj = this.popContent.bqdj;
                        var hldj = this.popContent.hldj;
                        var xmmc = this.popContent.xmmc;
                        var cklj = this.popContent.cklj;
                        var jbjl = this.popContent.jbjl;
                        var jcfl = this.popContent.jclx;
                        var jldw = this.popContent.jldwbm;
                        var jldwmc = this.popContent.jldwmc;
                        var lx = this.popContent.lx;
                        var yfdw = this.popContent.yfdw;
                        var yfdwmc = this.popContent.yfdwmc;
                        var yyff = this.popContent.yyff;
                        var yyffmc = this.popContent.yyffmc;
                        var zdjl = this.popContent.zdjl;
                        var ypgg = this.popContent.xmgg;
                        var yfbm = panel.popContent.yfbm;
                        var yfmc = panel.popContent.yfmc;
                        var ybtclb = this.popContent.yb;
                        var nbtclb = this.popContent.nh;
                        var ybtclbmc = this.popContent.ybtclbmc;
                        var kssjb = this.popContent.kssjb;
                        var zlbm = this.popContent.zlbm;
                        var kcfbz = this.popContent.kcfbz;
                        var jxbm = this.popContent.jxbm;
                        var dmypbz = this.popContent.dmypbz;
                        var jslypbz = this.popContent.jslypbz;

                        Vue.set(this.Yzxx_List[index], 'ysqmks', ysqmks);
                        Vue.set(this.Yzxx_List[index], 'ysqmksmc', ysqmksmc);
                        Vue.set(this.Yzxx_List[index], 'sfcy', sfcy);
                        Vue.set(this.Yzxx_List[index], 'lx', lx);       //类型  药品？诊疗
                        Vue.set(this.Yzxx_List[index], 'xmbm', xmbm);   //项目编码
                        Vue.set(this.Yzxx_List[index], 'bqdj', bqdj);   //项目编码
                        Vue.set(this.Yzxx_List[index], 'hldj', hldj);   //项目编码
                        Vue.set(this.Yzxx_List[index], 'xmmc', xmmc);   //项目名称
                        Vue.set(this.Yzxx_List[index], 'ypgg', ypgg);   //药品规格
                        Vue.set(this.Yzxx_List[index], 'cklj', cklj);   //价格
                        Vue.set(this.Yzxx_List[index], 'jbjl', jbjl);   //基本剂量
                        Vue.set(this.Yzxx_List[index], 'jcfl', jcfl);   //检查分类
                        Vue.set(this.Yzxx_List[index], 'jldw', jldw);   //剂量单位
                        Vue.set(this.Yzxx_List[index], 'jldwmc', jldwmc);  //剂量单位名称
                        Vue.set(this.Yzxx_List[index], 'yfdw', yfdw);      //药房单位
                        Vue.set(this.Yzxx_List[index], 'yfdwmc', yfdwmc);  //药房单位名称
                        Vue.set(this.Yzxx_List[index], 'zdjl', zdjl);      //最大剂量
                        Vue.set(this.Yzxx_List[index], 'zyh', userNameBg.Brxx_List.zyh);  //住院号
                        Vue.set(this.Yzxx_List[index], 'ksbm', userNameBg.Brxx_List.ssks); //科室
                        Vue.set(this.Yzxx_List[index], 'ksmc', userNameBg.Brxx_List.ssksmc); //科室
                        Vue.set(this.Yzxx_List[index], 'yyts', 1);            //用药天数
                        Vue.set(this.Yzxx_List[index], 'sl', 1);              //数量
                        Vue.set(this.Yzxx_List[index], 'yfbm', panel.popContent.yfbm); //药房编码
                        Vue.set(this.Yzxx_List[index], 'yfmc', panel.popContent.yfmc); //药房名称
                        Vue.set(this.Yzxx_List[index], 'ybtclb', ybtclb);      //医保统筹类别
                        Vue.set(this.Yzxx_List[index], 'ybtclbmc', ybtclbmc);  //医保统筹类别名称
                        Vue.set(this.Yzxx_List[index], 'nbtclb', nbtclb);      //农保统筹类别
                        Vue.set(this.Yzxx_List[index], 'ypzl', zlbm);          //药品种类
                        Vue.set(this.Yzxx_List[index], 'kcfbz', kcfbz);        //可拆分标志
                        Vue.set(this.Yzxx_List[index], 'kssjb', kssjb);        //抗生素级别
                        Vue.set(this.Yzxx_List[index], 'jxbm', jxbm);          //剂型编码
                        Vue.set(this.Yzxx_List[index], 'dmypbz', dmypbz);      //毒麻药品标志
                        Vue.set(this.Yzxx_List[index], 'jslypbz', jslypbz);    //精神类药品标志
                        change = false;
                        this.selSearch = 0;
                    }
                    if (lx == '诊疗') {
                        Vue.set(this.Yzxx_List[index], 'zlbz', true);        //诊疗标志
                        Vue.set(this.Yzxx_List[index], 'fzh', "");        //诊疗标志
                        $("#pcbm_" + index).focus();  //诊疗焦点就跳到频次编码
                        $("#sl_" + index).focus();  //诊疗焦点就跳到频次编码
                    } else {
                        Vue.set(this.Yzxx_List[index], 'zlbz', false);        //诊疗标志
                        $("#dcjl_" + index).focus();  //药品焦点就跳到单次剂量
                    }
                    setTimeout(function () {
                        if (hzList.sfqk == 1) {
                            hzList.Yzxx_List.splice(index, 1);
                            hzList.sfqk = 0;
                        }
                    }, 300);
                }

                // 判断药品有没有值 有值才跳下一个输入框
                if (event.target.value != '') {
                    this.prevFocus(event);
                }

            }
            ,
            //用药方法
            Wf_changeDown2: function (index, event, type) {
                this.prevFocus(event);
                if (this.Yzxx_List[index].tsyz == true) {
                    return;
                }
                qjIndex = index;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.keyCodeFunction(event, 'popContent', 'searchCon2');
                this.inputUpDown(event, this.searchCon2, "selSearch2");
                //赋值
                var fzh = this.Yzxx_List[index].fzh;
                if (event.keyCode == 13) {
                    if (this.popContent == undefined) return
                    //下拉框回车后回调.
                    if (!yyffcz && change) {
                        var yyffbm = this.popContent.yyffbm;
                        var yyffmc = this.popContent.yyffmc;
                        var gllb = this.popContent.zxdlx;
                        Vue.set(this.Yzxx_List[index], 'yyffbm', yyffbm);   //项目编码
                        Vue.set(this.Yzxx_List[index], 'yyffmc', yyffmc);   //项目名称
                        Vue.set(this.Yzxx_List[index], 'gllb', gllb);   //关联类别
                        change = false;
                    }
                    if (fzh > 0) {
                        for (var i = 0; i < this.Yzxx_List.length; i++) {
                            if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                this.Yzxx_List[i].yyffbm = this.Yzxx_List[index].yyffbm;
                                this.Yzxx_List[i].yyffmc = this.Yzxx_List[index].yyffmc;
                                this.Yzxx_List[i].gllb = this.Yzxx_List[index].gllb;
                            }
                        }
                    }
                    this.selSearch2 = 0;
                    this.nextFocus(event);
                }
            }
            ,
            //频次
            Wf_changeDown3: function (index, event, type, value) {
                this.prevFocus(event);
                if (this.Yzxx_List[index].tsyz == true) return;
                qjIndex = index;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.keyCodeFunction(event, 'popContent', 'searchCon3');
                this.inputUpDown(event, this.searchCon3, "selSearch3");
                //赋值
                var fzh = this.Yzxx_List[index].fzh;
                if (event.keyCode == 13) {
                    if (hzList.Yzxx_List[index].lx == '药品' && value == undefined) {
                        malert("药品模式下，请完整输入频次", 'top', 'defeadted');
                        return false
                    }
                    if (this.popContent == undefined) return
                    if (!pccz && change) {
                        var pcbm = this.popContent.pcbm;
                        var pcmc = this.popContent.pcmc;
                        var pccs = this.popContent.cs;
                        Vue.set(this.Yzxx_List[index], 'pcmc', pcmc);    //频次名称
                        Vue.set(this.Yzxx_List[index], 'pccs', pccs);  //频次次数
                        Vue.set(this.Yzxx_List[index], 'pcbm', pcbm);  //频次次数
                        change = false;
                    }
                    //分组设置
                    if (fzh > 0) {
                        for (var i = 0; i < this.Yzxx_List.length; i++) {
                            if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                this.Yzxx_List[i].pcbm = this.Yzxx_List[index].pcbm;
                                this.Yzxx_List[i].pcmc = this.Yzxx_List[index].pcmc;
                                Vue.set(this.Yzxx_List[i], 'pccs', this.Yzxx_List[index].pccs);
                                //Change事件，用于计算药品总量
                                this.Wf_editChange(i, 'pccs');
                            }
                        }
                    } else {
                        this.Wf_editChange(index, 'pccs');
                    }
                    this.selSearch = 0;
                    this.nextFocus(event);
                }
            }
            ,
            //值改变事件
            //药品
            Wf_change: function (add, index, type, val) {
                if (!add) this.page.page = 1;       // 设置当前页号为第一页
                this.Yzxx_List[index][type] = val;
                if (this.Yzxx_List[index].tsyz == true) {
                    return;
                }
                change = true;
                var yfbm = panel.popContent.yfbm;
                if (yfbm == null || yfbm == undefined || yfbm == "") {
                    malert("请选择药房", 'top', 'defeadted');
                    return false
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var pram = this.Yzxx_List[index][type];
                //截取首尾空格
                this.page.parm = trimStr(pram);
                this.page.yfbm = yfbm;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yzxm&json=' + JSON.stringify(this.page), function (data) {
                    if (add) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            hzList.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        hzList.searchCon = data.d.list;
                    }

                    // 屏蔽的原因是本来重写后search-table自身就处理了位置边界的。暂时没搞懂这里处理这个top是怎么回事。暂时屏蔽
                    // if (543 + 36 + ((index + 1) * 40) > document.body.clientHeight && (index + 1) * 40 > 300) {
                    //     _searchEvent.css("top", "-330px");
                    // }
                    hzList.page.total = data.d.total;
                    hzList.selSearch = 0;
                    if (data.d.list.length != 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });

            }
            ,
            //用药方法
            Wf_change2: function (index, type, val) {
                this.Yzxx_List[index][type] = val;
                if (this.Yzxx_List[index].tsyz == true) {
                    return;
                }
                yyffcz = false;
                pccz = false;
                change = true;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var pram = this.Yzxx_List[index][type];
                if (pram == null || pram == "") {
                    this.pageStr.parm = "";
                } else {
                    //截取首尾空格
                    pram = trimStr(pram);
                    //截取首尾空格
                    pram = trimStr(pram);
                    this.pageStr.parm = pram;
                }
                var str_param = {parm: this.pageStr.parm, page: this.pageStr.page, rows: this.pageStr.rows};
                $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmyyff&types=query&dg=" + JSON.stringify(str_param), function (data) {
                    if (data.a == 0) {
                        hzList.searchCon2 = data.d.list;
                        hzList.selSearch = 0;
                        if (data.d.list.length > 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c, 'top', 'defeadted');
                    }
                });
            }
            ,
            //频次
            Wf_change3: function (add, index, type, val) {
                this.Yzxx_List[index][type] = val;
                if (this.Yzxx_List[index].tsyz == true) {
                    return;
                }
                yyffcz = false;
                pccz = false;
                change = true;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var pram = this.Yzxx_List[index][type];
                if (pram == null || pram == "") {
                    this.queryStr.parm = "";
                } else {
                    //截取首尾空格
                    pram = trimStr(pram);
                    //截取首尾空格
                    pram = trimStr(pram);
                    this.queryStr.parm = pram;
                }
                var pc_dg = {
                    parm: this.queryStr.parm,
                    page: this.queryStr.page,
                    rows: this.queryStr.rows,
                    sort: "sor",
                    order: "asc"
                };
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yzpc&dg=" + JSON.stringify(pc_dg), function (json) {
                    hzList.searchCon3 = json.d.list;
                    hzList.total = json.d.total;
                    hzList.selSearch = 0;
                    if (json.d.list.length != 0) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    } else {
                        $(".selectGroup").hide();
                    }
                });
            }
            ,
            //双击事件
            //用药方法
            selectOne1: function (item) {
                this.popContent = item;
                if (!yyffcz && change) {
                    var yyffbm = this.popContent.yyffbm;
                    var yyffmc = this.popContent.yyffmc;
                    var gllb = this.popContent.zxdlx;
                    console.log(gllb);
                    Vue.set(this.Yzxx_List[qjIndex], 'yyffbm', yyffbm);   //项目编码
                    Vue.set(this.Yzxx_List[qjIndex], 'yyffmc', yyffmc);   //项目名称
                    Vue.set(this.Yzxx_List[qjIndex], 'gllb', gllb);   //关联类别名称
                    change = false;
                    this.selSearch2 = 0;
                }
                var fzh = this.Yzxx_List[qjIndex].fzh;
                //针对相同分组号处理方法
                if (fzh > 0) {
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                            this.Yzxx_List[i].yyffbm = this.Yzxx_List[qjIndex].yyffbm;
                            this.Yzxx_List[i].yyffmc = this.Yzxx_List[qjIndex].yyffmc;
                            this.Yzxx_List[i].gllb = this.Yzxx_List[qjIndex].gllb;
                        }
                    }
                }

                this.nextFocus(event);
                $(".selectGroup").hide();

            }
            ,
            //药品
            selectOne2: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.Wf_change(true, qjIndex, 'xmmc', this.Yzxx_List[qjIndex]['xmmc']);           // 传参表示请求下一页,不传就表示请求第一页
                } else {
                    //this.getKssSq(item); //对抗生素药物使用的判定

                    this.popContent = item;
                    var sfcy = '0';
                    var xmbm = this.popContent.xmbm;
                    var hldj = this.popContent.hldj;
                    var xmmc = this.popContent.xmmc;
                    var cklj = this.popContent.cklj;
                    var jbjl = this.popContent.jbjl;
                    var jcfl = this.popContent.jclx;
                    var jldw = this.popContent.jldwbm;
                    var jldwmc = this.popContent.jldwmc;
                    var lx = this.popContent.lx;
                    var yfdw = this.popContent.yfdw;
                    var yfdwmc = this.popContent.yfdwmc;
                    var yyff = this.popContent.yyff;
                    var yyffmc = this.popContent.yyffmc;
                    var zdjl = this.popContent.zdjl;
                    var ypgg = this.popContent.xmgg;
                    var yfbm = panel.popContent.yfbm;
                    var yfmc = panel.popContent.yfmc;
                    var ybtclb = this.popContent.yb;
                    var nbtclb = this.popContent.nh;
                    var ybtclbmc = this.popContent.ybtclbmc;
                    var kssjb = this.popContent.kssjb;
                    var zlbm = this.popContent.zlbm;
                    var kcfbz = this.popContent.kcfbz;
                    var jxbm = this.popContent.jxbm;
                    if (change) {
                        Vue.set(this.Yzxx_List[qjIndex], 'sfcy', sfcy);       //类型  药品？诊疗
                        Vue.set(this.Yzxx_List[qjIndex], 'lx', lx);       //类型  药品？诊疗
                        Vue.set(this.Yzxx_List[qjIndex], 'xmbm', xmbm);   //项目编码
                        Vue.set(this.Yzxx_List[qjIndex], 'hldj', hldj);   //项目编码
                        Vue.set(this.Yzxx_List[qjIndex], 'xmmc', xmmc);   //项目名称
                        Vue.set(this.Yzxx_List[qjIndex], 'ypgg', ypgg);   //药品规格
                        Vue.set(this.Yzxx_List[qjIndex], 'cklj', cklj);   //价格
                        Vue.set(this.Yzxx_List[qjIndex], 'jbjl', jbjl);   //基本剂量
                        Vue.set(this.Yzxx_List[qjIndex], 'jcfl', jcfl);   //检查分类
                        Vue.set(this.Yzxx_List[qjIndex], 'jldw', jldw);   //剂量单位
                        Vue.set(this.Yzxx_List[qjIndex], 'jldwmc', jldwmc);  //剂量单位名称
                        Vue.set(this.Yzxx_List[qjIndex], 'yfdw', yfdw);      //药房单位
                        Vue.set(this.Yzxx_List[qjIndex], 'yfdwmc', yfdwmc);  //药房单位名称
                        Vue.set(this.Yzxx_List[qjIndex], 'zdjl', zdjl);      //最大剂量
                        Vue.set(this.Yzxx_List[qjIndex], 'zyh', userNameBg.Brxx_List.zyh);  //住院号
                        Vue.set(this.Yzxx_List[qjIndex], 'ksbm', userNameBg.Brxx_List.ssks); //科室
                        Vue.set(this.Yzxx_List[qjIndex], 'ksmc', userNameBg.Brxx_List.ssksmc); //科室
                        Vue.set(this.Yzxx_List[qjIndex], 'yyts', 1);            //用药天数
                        Vue.set(this.Yzxx_List[qjIndex], 'sl', 1);              //数量
                        Vue.set(this.Yzxx_List[qjIndex], 'yfbm', panel.popContent.yfbm); //药房编码
                        Vue.set(this.Yzxx_List[qjIndex], 'yfmc', panel.popContent.yfmc); //药房名称
                        Vue.set(this.Yzxx_List[qjIndex], 'ybtclb', ybtclb);      //医保统筹类别
                        Vue.set(this.Yzxx_List[qjIndex], 'ybtclbmc', ybtclbmc);  //医保统筹类别名称
                        Vue.set(this.Yzxx_List[qjIndex], 'nbtclb', nbtclb);      //农保统筹类别
                        Vue.set(this.Yzxx_List[qjIndex], 'ypzl', zlbm);          //药品种类
                        Vue.set(this.Yzxx_List[qjIndex], 'kcfbz', kcfbz);        //可拆分标志
                        Vue.set(this.Yzxx_List[qjIndex], 'kssjb', kssjb);        //抗生素级别
                        Vue.set(this.Yzxx_List[qjIndex], 'jxbm', jxbm);          //剂型编码
                        change = false;
                        this.selSearch = 0;
                    }
                    if (lx == '诊疗') {
                        Vue.set(this.Yzxx_List[qjIndex], 'zlbz', true);        //诊疗标志
                        Vue.set(this.Yzxx_List[qjIndex], 'fzh', "");        //诊疗标志
                        $("#pcbm_" + qjIndex).focus();  //诊疗焦点就跳到频次编码
                    } else {
                        Vue.set(this.Yzxx_List[qjIndex], 'zlbz', false);        //诊疗标志
                        $("#dcjl_" + qjIndex).focus();  //药品焦点就跳到单次剂量
                    }
                    $(".selectGroup").hide();
                    setTimeout(function () {
                        if (hzList.sfqk == 1) {
                            hzList.Yzxx_List.splice(qjIndex, 1);
                            hzList.sfqk = 0;
                        }
                    }, 300);

                }
            }
            ,

            //频次
            selectOne3: function (item) {
                this.popContent = item;
                if (!pccz && change) {
                    var pcbm = this.popContent.pcbm;
                    var pcmc = this.popContent.pcmc;
                    var pccs = this.popContent.cs;
                    Vue.set(this.Yzxx_List[qjIndex], 'pcmc', pcmc);    //频次名称
                    Vue.set(this.Yzxx_List[qjIndex], 'pccs', pccs);  //频次次数
                    Vue.set(this.Yzxx_List[qjIndex], 'pcbm', pcbm);  //频次次数
                    change = false;
                }
                var fzh = this.Yzxx_List[qjIndex].fzh;
                //分组设置
                if (fzh > 0) {
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                            this.Yzxx_List[i].pcbm = this.Yzxx_List[qjIndex].pcbm;
                            this.Yzxx_List[i].pcmc = this.Yzxx_List[qjIndex].pcmc;
                            Vue.set(this.Yzxx_List[i], 'pccs', this.Yzxx_List[qjIndex].pccs);
                            //Change事件，用于计算药品总量
                            this.Wf_editChange(i, 'pccs');
                        }
                    }
                } else {
                    this.Wf_editChange(qjIndex, 'pccs');
                }
                this.selSearch3 = 0;
                this.nextFocus(event);
                $(".selectGroup").hide();
            }
            ,

            //查询医嘱
            Wf_selectYZ: function () {
                this.isChecked = [];
                var ksbm = userNameBg.Brxx_List.ksbm;   //入院科室
                var kdks = userNameBg.Brxx_List.ssks;   //医生所在科室
                var ryks = userNameBg.Brxx_List.ryks;   //入院登记表显示当前科室
                var zyh = userNameBg.Brxx_List.zyh;  //住院号
                var yzlx = panel.popContent.yzxx == '2' ? '' : panel.popContent.yzxx;
                var ystzbz = panel.popContent.yzgl;
                var hstzbz = null;
                var zfbz = null;
                var shbz = null;
                var cfss = null;
                if (panel.popContent.yess == '1') {
                    cfss = '0'
                } else if (panel.popContent.yess == '2') {
                    cfss = '1'
                } else {
                    cfss = "";
                }
                if (ystzbz == '2') { //作废
                    ystzbz = '%';
                    zfbz = '1';
                }
                if (ystzbz == '3') { //停嘱未审
                    ystzbz = '1';
                    hstzbz = '0';
                }
                if (ystzbz == '4') { //未审核
                    zfbz = '0';
                    shbz = '0';
                    ystzbz = '0';
                }
                if (yzlx == '%') yzlx = '';
                if (ystzbz == '%') ystzbz = '';
                if (!ksbm) {
                    malert("病人科室不能为空！，请选择科室.", 'top', 'defeadted');
                    return;
                }
                if (!zyh) {
                    malert("请选择病人！", 'top', 'defeadted');
                    return;
                }
                this.Yzxx_List = [];   //先清空再说
                var parm_ksbm = {
                    ksbm: ksbm,
                    ssks: kdks,
                    ryks: ryks,
                    yzlx: yzlx,
                    ystzbz: ystzbz,
                    hstzbz: hstzbz,
                    zfbz: zfbz,
                    shbz: shbz,
                    cfss: cfss,
                };
                var parm_zyh = [{
                    zyh: zyh
                }];
                common.openloading('.loadPage');
                $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzxx&parm=' +
                    JSON.stringify(parm_ksbm) + '&zyh=' + JSON.stringify(parm_zyh), function (json) {
                    if (json.a == '0') {
                        console.log(json.d.list);
                        //把时间戳改改时间
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                json.d.list[i].readonly = true;   //只读
                                json.d.list[i].ksrq = formatTime(json.d.list[i].ksrq, "datetime");
                                if (json.d.list[i].xssx == "0") {
                                    json.d.list[i].xssx = i + 1;
                                }
                                //特殊医嘱
                                var tsyz = json.d.list[i].tsyz;
                                if (tsyz == "1") {
                                    json.d.list[i].tsyz = true;
                                } else {
                                    json.d.list[i].tsyz = false;
                                }
                                json.d.list[i].zt = null;
                                //判断状态
                                /*'0': '待审核','1': '已停嘱','2': '已作废','3': '待停嘱','4'：'草稿'"*/
                                if (json.d.list[i].zfbz == '1') {
                                    json.d.list[i].zt = '2';
                                } else if (json.d.list[i].shbz == '0' && json.d.list[i].ystzbz == '0') {
                                    json.d.list[i].zt = '0';
                                } else if (json.d.list[i].ystzbz == '1') {
                                    json.d.list[i].zt = '1';
                                } else if (json.d.list[i].shbz == '1' && json.d.list[i].ystzbz == '0') {
                                    json.d.list[i].zt = '3';
                                }
                                if (json.d.list[i].fzh != 0) {
                                    if (i == 0) { // 第一个
                                        if (json.d.list[i + 1] != undefined) {
                                            if (json.d.list[i].fzh == json.d.list[i + 1].fzh && json.d.list[i].yzxh == json.d.list[i + 1].yzxh) {
                                                json.d.list[i]['tzbj'] = 'tz-start';
                                            }
                                        }
                                    } else if (i == json.d.list.length - 1) { // 最后一个
                                        if (json.d.list[i].fzh == json.d.list[i - 1].fzh && json.d.list[i].yzxh == json.d.list[i - 1].yzxh) {
                                            json.d.list[i]['tzbj'] = 'tz-stop';
                                        }
                                    } else {
                                        if ((json.d.list[i].fzh != json.d.list[i - 1].fzh || json.d.list[i].yzxh != json.d.list[i - 1].yzxh) && (json.d.list[i].fzh == json.d.list[i + 1].fzh && json.d.list[i].yzxh == json.d.list[i + 1].yzxh)) {
                                            json.d.list[i]['tzbj'] = 'tz-start';
                                        } else if (json.d.list[i].fzh == json.d.list[i - 1].fzh && json.d.list[i].yzxh == json.d.list[i - 1].yzxh && json.d.list[i].fzh == json.d.list[i + 1].fzh && json.d.list[i].yzxh == json.d.list[i + 1].yzxh) {
                                            json.d.list[i]['tzbj'] = 'tz-center';
                                        } else if ((json.d.list[i].fzh == json.d.list[i - 1].fzh && json.d.list[i].yzxh == json.d.list[i - 1].yzxh) && (json.d.list[i].fzh != json.d.list[i + 1].fzh || json.d.list[i].yzxh != json.d.list[i + 1].yzxh)) {
                                            json.d.list[i]['tzbj'] = 'tz-stop';
                                        }
                                    }
                                }
                            }

                            hzList.Yzxx_List = json.d.list;
                            common.closeLoading()
                        } else {
                            hzList.Wf_addYZ();
                            common.closeLoading()
                        }
                    }
                });
            }
            ,


            //项目改变事件
            Wf_XmChange: function (event, index, type) {
                var obj = event.currentTarget;
                var fzh = this.Yzxx_List[index].fzh;  //分组号
                switch (type) {
                    case "fzh":  //分组号
                        //同一组设置相同属性的值
                        if (fzh > 0 && index > 0 && this.Yzxx_List[index - 1].insertBz == true) {
                            if (fzh == this.Yzxx_List[index - 1].fzh) {
                                Vue.set(this.Yzxx_List[index], 'yyffbm', this.Yzxx_List[index - 1].yyffbm);    //用药方法名称
                                this.Yzxx_List[index].yyffmc = this.Yzxx_List[index - 1].yyffmc;
                                this.Yzxx_List[index].pcbm = this.Yzxx_List[index - 1].pcbm;
                                this.Yzxx_List[index].pcmc = this.Yzxx_List[index - 1].pcmc;
                                this.Yzxx_List[index].pccs = this.Yzxx_List[index - 1].pccs;
                                this.Yzxx_List[index].sysd = this.Yzxx_List[index - 1].sysd;
                                this.Yzxx_List[index].gllb = this.Yzxx_List[index - 1].gllb;
                                Vue.set(this.Yzxx_List[index], 'sysddw', this.Yzxx_List[index - 1].sysddw);    //输液速度编码
                            }
                        }
                        break;
                    case "yyffbm":  //用药方法
                        var selected = $(obj).find("option:selected");
                        var mc = selected.text();
                        var zxdlx = selected.attr('zxdlx');
                        Vue.set(this.Yzxx_List[index], 'yyffmc', mc);   //用法名称
                        Vue.set(this.Yzxx_List[index], 'gllb', zxdlx); //执行单类型
                        //分组设置
                        if (fzh > 0) {
                            for (var i = 0; i < this.Yzxx_List.length; i++) {
                                if (i != index && fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                    this.Yzxx_List[i].yyffbm = this.Yzxx_List[index].yyffbm;
                                    this.Yzxx_List[i].gllb = this.Yzxx_List[index].gllb;   //执行单类型
                                    Vue.set(this.Yzxx_List[i], 'yyffmc', this.Yzxx_List[index].mc);    //用药方法名称
                                }
                            }
                        }
                        break;
                    case "pcbm":    //频次
                        var selected = $(obj).find("option:selected");
                        var mc = selected.text();
                        var pccs = selected.attr('cs');
                        Vue.set(this.Yzxx_List[index], 'pcmc', mc);    //频次名称
                        Vue.set(this.Yzxx_List[index], 'pccs', pccs);  //频次次数
                        //分组设置
                        if (fzh > 0) {
                            for (var i = 0; i < this.Yzxx_List.length; i++) {
                                if (i != index && fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                    this.Yzxx_List[i].pcbm = this.Yzxx_List[index].pcbm;
                                    this.Yzxx_List[i].pcmc = this.Yzxx_List[index].pcmc;
                                    Vue.set(this.Yzxx_List[i], 'pccs', this.Yzxx_List[index].pccs);

                                    //Change事件，用于计算药品总量
                                    this.Wf_editChange(i, 'pccs');
                                }
                            }
                        }
                        break;
                    case "xz":    //选择
                        tjIndex = index;
                        if ($(obj).is(':checked')) {
                            Vue.set(this.Yzxx_List[index], 'xz', true);
                            zdtj = true;
                        } else {
                            Vue.set(this.Yzxx_List[index], 'xz', false);
                            zdtj = false;
                        }
                        break;
                    case "tsyz":
                        if (this.Yzxx_List[index].insertBz != true) {
                            Vue.set(this.Yzxx_List[index], 'tsyz', false);
                            malert("非新增加医嘱不允许修改为特殊医嘱", 'top', 'defeadted');
                            return;
                        }
                        sfzt = true;
                        if ($(obj).is(':checked')) {
                            this.Yzxx_List[index].xmbm = this.Yzxx_List[index].xmmc;
                            this.Yzxx_List[index].ypgg = "";
                            Vue.set(this.Yzxx_List[index], 'ypzl', "");          //药品种类
                            Vue.set(this.Yzxx_List[index], 'kcfbz', "");         //可拆分标志
                            Vue.set(this.Yzxx_List[index], 'ybtclb', "");        //医保统筹类别
                            Vue.set(this.Yzxx_List[index], 'ybtclbmc', "");      //医保统筹类别名称
                            Vue.set(this.Yzxx_List[index], 'nbtclb', "");        //农保统筹类别
                            //Vue.set(this.Yzxx_List[index], 'yfbm', "");          //药房编码
                            Vue.set(this.Yzxx_List[index], 'yfmc', "");           //药房名称
                            Vue.set(this.Yzxx_List[index], 'lx', "诊疗");         //类型  药品？诊疗
                            Vue.set(this.Yzxx_List[index], 'cklj', "");          //价格
                            Vue.set(this.Yzxx_List[index], 'jbjl', 1);           //基本剂量
                            Vue.set(this.Yzxx_List[index], 'jcfl', "0");         //检查分类
                            Vue.set(this.Yzxx_List[index], 'jldw', "");          //剂量单位
                            Vue.set(this.Yzxx_List[index], 'jldwmc', "");        //剂量单位名称
                            Vue.set(this.Yzxx_List[index], 'kssjb', "0");        //抗生素级别
                            Vue.set(this.Yzxx_List[index], 'sl', "1");
                            Vue.set(this.Yzxx_List[index], 'tsyz', true);
                        } else {
                            this.Yzxx_List[index].xmbm = "";
                            this.Yzxx_List[index].xmmc = "";
                            Vue.set(this.Yzxx_List[index], 'tsyz', false);
                        }
                        break;
                }
            }
            ,

            //值改变事件，主要用来计算医嘱总量
            Wf_editChange: function (index, type) {
                if (this.Yzxx_List[index].lx == '诊疗') return;   //诊疗就直接返回不管
                var sl = 1; //数量
                var cs = this.Yzxx_List[index].pccs;   //频次次数
                var dcjl = this.Yzxx_List[index].dcjl;   //单次剂量
                var jbjl = this.Yzxx_List[index].jbjl;   //基本剂量
                var kcfbz = this.Yzxx_List[index].kcfbz;   //可拆分标志 0-不可拆分 ； 1-可拆分
                if (cs == null || cs == undefined || cs == "") cs = 1;
                if (jbjl == null || jbjl == undefined || jbjl == "") jbjl = 1;
                if (kcfbz == null || kcfbz == undefined || kcfbz == "") kcfbz = 0;
                switch (type) {
                    case "dcjl":
                        if (isNaN(dcjl)) {
                            malert("第【" + (index + 1) + "】行的单次剂量不是有效数字！", 'top', 'defeadted');
                            return;
                        }
                        break;
                }
                //自动给用药问题赋值
                if (kcfbz == "0") {
                    sl = Math.ceil(dcjl / jbjl) * cs;  //自动算药品总量
                } else {
                    sl = Math.ceil(dcjl / jbjl * cs);  //自动算药品总量
                }
                Vue.set(this.Yzxx_List[index], 'sl', sl);     //给总量赋值
            }
            ,
            //医嘱类型
            Wf_yzlxClick: function (type) {
                switch (type) {
                    case 'qb':
                        this.is_yzlx = '%';
                        break;
                    case 'cq':
                        this.is_yzlx = '1';
                        break;
                    case 'ls':
                        this.is_yzlx = '0';
                        break;
                }
                this.Wf_selectYZ();  //查询医嘱
            }
            ,
            //医嘱过滤
            Wf_yzglClick: function (type) {
                switch (type) {
                    case 'qb':
                        this.is_yzgl = '%';
                        break;
                    case 'wt':
                        this.is_yzgl = '0';
                        break;
                    case 'yt':
                        this.is_yzgl = '1';
                        break;
                    case 'zf':
                        this.is_yzgl = '2';
                        break;
                    case 'wstz':
                        this.is_yzgl = '3';
                        break;
                }
                this.Wf_selectYZ();  //查询医嘱
            }
            ,

            nextFocusJl: function (event, index, num, tpye, value, text) {
                console.log("进B");
                if (event.keyCode == 13) {
                    if (hzList.Yzxx_List[index].lx == '药品' && value == undefined) {
                        malert("药品模式下，请完整输入" + text + "", 'top', 'defeadted');
                        return false
                    }
                    if (hzList.Yzxx_List.length > 1 && this.Yzxx_List[index].fzh == this.Yzxx_List[index - 1].fzh && hzList.Yzxx_List[index].yyffbm != null) {
                        hzList.Wf_addYZ();
                    } else {
                        this.nextSelect(event)
                    }
                    return false
                } else if (event.keyCode == 37) {
                    if (hzList.Yzxx_List.length > 1 && hzList.Yzxx_List[index].yyffbm != null) {
                        hzList.Wf_addYZ();
                    } else {
                        this.nextSelect(event)
                    }
                    return false
                } else if (event.keyCode == 39) {
                    if (hzList.Yzxx_List.length > 1 && hzList.Yzxx_List[index].yyffbm != null) {
                        hzList.Wf_addYZ();
                    } else {
                        this.nextSelect(event)
                    }
                    return false
                }
            }
            ,
            //回车事件
            Wf_keyEnter: function (event, index, type) {
                console.log("jinA");
                var value;
                var obj = event.currentTarget;
                var lx = this.Yzxx_List[index].lx;
                switch (type) {
                    //                case  "xmmc":
                    //                    if (!sfzt) {
                    //                        sfzt = false;
                    //                        this.Wf_addYZ();
                    //                    }
                    //                    break;
                    //单次剂量
                    case "dcjl":
                        value = $(obj).val();

                        if (!$.isNumeric(value)) {
                            $(obj).focus();
                            return;
                        }
                        //判断是药品还是诊疗
                        if (lx == "药品") {
                            $("#yyffbm_" + index).focus();
                        } else {
                            $("#sl_" + index).focus();
                        }
                        break;
                    //                    }

                    //用药方法
                    case "yyffbm":
                        $("#pcbm_" + index).focus();
                        break;
                    //用药方法
                    case "pcbm":
                        $("#sl_" + index).focus();
                        break;
                    //用药天数
                    case "yyts":
                        value = $(obj).val();
                        if (!$.isNumeric(value)) {
                            $(obj).focus();
                            return;
                        }
                        $("#sl_" + index).focus();
                        break;
                    //数量
                    case "sl":
                        value = $(obj).val();
                        if (value == '' || value == 0) {
                            common.openConfirm('第' + index + '行' + this.Yzxx_List[index].xmmc + '总量为零，是否继续', function () {
                                //判断是药品还是诊疗
                                if (lx == "药品") {
                                    if (hzList.Yzxx_List[index].gllb == 3) {
                                        $("#sysd_" + index).focus();
                                    } else {
                                        $("#yysm_" + index).focus();
                                    }
                                } else {
                                    if (hzList.Yzxx_List.length <= (index + 1)) {
                                        hzList.Wf_addYZ();   //如果医嘱行数是最后一行就新加一条医嘱
                                    } else {
                                        $("#yysm_" + index).focus();
                                    }
                                }
                            }, function () {
                                $(obj).focus();
                            })
                        } else {
                            //判断是药品还是诊疗
                            if (lx == "药品") {
                                if (hzList.Yzxx_List[index].gllb == 3) {
                                    $("#sysd_" + index).focus();
                                } else {
                                    $("#yysm_" + index).focus();
                                }
                            } else {
                                if (hzList.Yzxx_List.length <= (index + 1)) {
                                    hzList.Wf_addYZ();   //如果医嘱行数是最后一行就新加一条医嘱
                                } else {
                                    $("#yysm_" + index).focus();
                                }
                            }
                        }

                        // if (!$.isNumeric(value)) {
                        //     $(obj).focus();
                        //     return;
                        // }

                        break;
                    //输液速度
                    case "sysd":
                        value = $(obj).val();
                        if (!$.isNumeric(value)) {
                            $(obj).focus();
                            return;
                        }
                        //赋值
                        var fzh = this.Yzxx_List[index - 1].fzh;
                        if (fzh > 0) {
                            for (var i = 0; i < this.Yzxx_List.length; i++) {
                                if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                    this.Yzxx_List[i].sysd = this.Yzxx_List[index].sysd;
                                }
                            }
                        }
                        $("#sysddw_" + index).focus();
                        break;
                    //输液速度单位
                    case "sysddw":
                        $("#yysm_" + index).focus();
                        //赋值
                        var fzh = this.Yzxx_List[index - 1].fzh;
                        if (fzh > 0) {
                            for (var i = 0; i < this.Yzxx_List.length; i++) {
                                if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                    this.Yzxx_List[i].sysddw = this.Yzxx_List[index].sysddw;
                                }
                            }
                        }
                        break;
                    //医生说明
                    case "yysm":
                        var fzh = this.Yzxx_List[index].fzh;
                        if (this.Yzxx_List.length <= (index + 1)) {
                            this.Wf_addYZ();   //如果医嘱行数是最后一行就新加一条医嘱
                            this.nextFocus(event)
                        }
                        setTimeout(function () {   //由于新增加一条数据需要延时0.1秒执行
                            $("#xmmc_" + (index + 1)).focus();
                            Vue.set(hzList.Yzxx_List[index + 1], 'fzh', fzh);     //分组号
                            hzList.Wf_XmChange(event, index + 1, "fzh");  //触发fzh的Change事件
                        }, 100);
                        break;
                }
            }
            ,
        }
    })
;

//毒麻精神类处方代办人信息
var dbrPop = new Vue({
    el: '#dbrPop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        isPopUped: false,
        popTitle: '本人实名认证信息',
        smxx: {}, //实名信息
        type: null,
        yzList: null
    },

    methods: {
        //保存
        saveDbrxx: function () {
            if (!dbrPop.smxx.xm) {
                malert("姓名不能为空！", 'top', 'defeadted');
                return false;
            }

            if (dbrPop.smxx.sfzh) {
                if (dbrPop.smxx.sfzh.length != 18) {
                    malert("身份证件号码长度非法！", 'top', 'defeadted');
                    return false;
                }
            }

            if (dbrPop.smxx.dbrzjhm) {
                if (dbrPop.smxx.dbrzjhm.length != 18) {
                    malert("代办人身份证件号码长度非法！", 'top', 'defeadted');
                    return false;
                }
            }

            if (dbrPop.smxx.dbrlxdh) {
                //座机验证
                var zjyz = /^([0-9]{3,4})?[0-9]{7,8}$/;
                //手机验证
                var sjyz = /^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[02356789][0-9]{8}|147[0-9]{8}|1349[0-9]{7})$/;
                var value = dbrPop.smxx.dbrlxdh.trim();
                if (!zjyz.test(value) && !sjyz.test(value)) {
                    malert("联系电话格式不正确！", 'top', 'defeadted');
                    return false;
                }
            } else {
                malert("联系电话不能为空！", 'top', 'defeadted');
                return false;
            }

            //代办人信息
            for (var i = 0; i < dbrPop.yzList.length; i++) {
                if (dbrPop.yzList[i].lx == '药品' && dbrPop.yzList[i].dmjslyp) {
                    dbrPop.yzList[i].dbrxm = dbrPop.smxx.dbrxm;
                    dbrPop.yzList[i].dbrzjhm = dbrPop.smxx.dbrzjhm;
                    dbrPop.yzList[i].dbrlxdh = dbrPop.smxx.dbrlxdh;
                }
            }

            hzList.final_saveYz(dbrPop.yzList);
            dbrPop.popShow = false;
        },
        //取消
        cancel: function () {
            dbrPop.popShow = false;
            hzList.ifClick = true;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})
        },

        //reset
        show: function () {
            dbrPop.smxx.xm = userNameBg.Brxx_List.brxm;
            dbrPop.smxx.sfzh = userNameBg.Brxx_List.sfzjhm;

            // if (userNameBg.Brxx_List.lxdh){
            //     this.dbrxx.lxdh = zcy.cfcontent.lxrdh;
            // } else {
            //     this.dbrxx.lxdh = userNameBg.Brxx_List.sjhm;
            // }
            //
            // if (userNameBg.Brxx_List.sfzjhm){
            //     this.dbrxx.sfzh = zcy.cfcontent.lxrsfzh;
            // } else{
            //     this.dbrxx.sfzh = userNameBg.Brxx_List.sfzh;
            // }
            dbrPop.popShow = true;
        }
    },
});

//复制医嘱弹框
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 1,
        mxIndex: undefined,
        title: '复制医嘱',
        popContent: {
            yzlx: '1'
        },  //选中的值
        isShow: false,
        scollType: true,
        lsYzList: [],
        total: null,
        isyz: true,
        param: {},
        yzList: [],
        yzMxList: [],
        a: undefined,
        isChecked: [],
        YZ_GetRow: 0,
        dg: {page: 1, rows: 20, sort: "", order: "asc", parm: ""},//分页信息
        sfcy: null,
        sfgr_tran: {
            "1": "是",
            "0": "否"
        },
    },
    updated: function () {
        changeWin();
    },
    methods: {
        //医嘱模板明细
        getTemMxData: function (index, event) {
            this.isCheckAll = false;
            this.isChecked = [];
            this.mxIndex = index;
            var zhyzbm = this.yzList[index].zhyzbm;
            var json = {
                zhyzbm: zhyzbm
            };
            brzcList.dg.rows = 20;
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zhyzmx&dg=' + JSON.stringify(brzcList.dg) + '&json=' + JSON.stringify(json), function (data) {
                brzcList.yzMxList = [];
                if (data.d != null) {
                    brzcList.yzMxList = data.d.list;
                    console.log(brzcList.yzMxList)
                }
            });
        },
        //历史医嘱明细
        getLsTemMxData: function (index, event) {
            this.isCheckAll = false;
            this.mxIndex = index;
            var zyh = this.lsYzList[index].zyh;
            var ksbm = this.lsYzList[index].ryks;   //科室
            var yzlx = panel.popContent.yzlx;
            var ystzbz = panel.popContent.yzgl;
            if (yzlx == '%') yzlx = '';
            if (ystzbz == '%') ystzbz = '';
            if (ksbm == null || ksbm == undefined || ksbm == "") {
                malert("病人科室不能为空！，请选择科室.", 'top', 'defeadted');
                return false;
            }
            if (zyh == null || zyh == undefined || zyh == "") {
                malert("请选择病人！", 'top', 'defeadted');
                return false;
            }

            var parm_ksbm = {
                ksbm: ksbm,
                yzlx: yzlx,
                ystzbz: ystzbz
            };
            var parm_zyh = [{
                zyh: zyh
            }];
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzxx&parm=' + JSON.stringify(parm_ksbm) + '&zyh=' + JSON.stringify(parm_zyh), function (json) {
                if (json.a == '0') {
                    brzcList.yzMxList = [];
                    //把时间戳改改时间
                    if (json.d.list.length > 0) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            json.d.list[i].readonly = true;   //只读
                            json.d.list[i].ksrq = formatTime(json.d.list[i].ksrq, "datetime");
                            if (json.d.list[i].xssx == "0") {
                                json.d.list[i].xssx = i + 1;
                            }
                            //特殊医嘱
                            var tsyz = json.d.list[i].tsyz;
                            if (tsyz == "1") {
                                json.d.list[i].tsyz = true;
                            } else {
                                json.d.list[i].tsyz = false;
                            }

                        }
                        brzcList.yzMxList = json.d.list;
                    } else {
                        malert("该病人无历史医嘱！", 'top', 'defeadted');
                    }
                }
            });
        },
        //滚动条
        scroll: function (event) {
            this.$refs.scroll.style.marginLeft = 0 - $(event.target).scrollLeft() + "px";
        },
        //关闭弹框
        closes: function () {
            this.isChecked = [];
            this.num = 1
        },
        //双击全选
        dballCheck: function () {
            //全选
            this.isCheckAll = true;
            for (var i = 0; i < brzcList.yzMxList.length; i++) {
                Vue.set(brzcList.isChecked, i, true);
            }
            this.confirms();
        },
        addxm: function () {
            let tpmxIndex = this.mxIndex

            if (tpmxIndex == undefined) {

                malert("请选择模板!", 'right', 'defeadted')

                return false;

            }

            if (this.yzList[tpmxIndex].yyz != userId) {

                malert("该模板不是您创建!", 'right', 'defeadted')

                return false;

            }

            let zhyzbm = this.yzList[tpmxIndex].zhyzbm;
            let zllist = [];
            let yplist = [];
            for (var i = 0; i < hzList.isChecked.length; i++) {
                if (hzList.isChecked[i]) {
                    hzList.Yzxx_List[i].zhyzbm = zhyzbm
                    if (hzList.Yzxx_List[i].ypbz == '1') {
                        hzList.Yzxx_List[i].yyff = hzList.Yzxx_List[i].yyffbm
                        hzList.Yzxx_List[i].ypbm = hzList.Yzxx_List[i].xmbm
                        yplist.push(hzList.Yzxx_List[i])
                    } else {
                        hzList.Yzxx_List[i].mxzlxmbm = hzList.Yzxx_List[i].xmbm
                        zllist.push(hzList.Yzxx_List[i])
                    }
                }
            }
            if (zllist.length > 0) {
                for (let i = 0; i < zllist.length; i++) {
                    hzList.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhylyz&types=save',
                        JSON.stringify(zllist[i]))
                        .then(function (data) {
                            if (data.body.a == 0) {
                                brzcList.getTemMxData(tpmxIndex);
                            } else {
                                malert(data.body.c + "数据失败", 'top', 'defeadted');
                            }
                        }, function (error) {
                            console.log(error);
                        });
                }

            }
            if (yplist.length > 0) {
                for (let i = 0; i < yplist.length; i++) {
                    hzList.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhypyz&types=save',
                        JSON.stringify(yplist[i]))
                        .then(function (data) {
                            if (data.body.a == 0) {
                                brzcList.getTemMxData(tpmxIndex);
                            } else {
                                malert(data.body.c + "数据失败", 'top', 'defeadted');
                            }
                        }, function (error) {
                            console.log(error);
                        });
                }

            }
            // if(yplist.length >0){
            //     var obj = {
            //             bean: yplist
            //     }
            //     hzList.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhypyz&types=saveForCz',
            //         JSON.stringify(obj))
            //         .then(function (data) {
            //             if(data.body.a == 0){
            //                 brzcajList.getTemMxData(tpmxIndex);
            //             } else {
            //                 malert(data.body.c+"数据失败",'top', 'defeadted');
            //             }
            //         }, function (error) {
            //             console.log(error);
            //         });
            // }
        },
        shanchumb: function () {
            let tpmxIndex = this.mxIndex
            if (tpmxIndex == undefined) {
                malert("请选择模板!", 'right', 'defeadted')
                return false;
            }
            if (this.yzList[tpmxIndex].yyz != userId) {
                malert("该模板不是您创建!", 'right', 'defeadted')
                return false;
            }
            let zhyzbm = this.yzList[tpmxIndex].zhyzbm;
            let zhlist = [
                {
                    zhyzbm: zhyzbm,
                    yljgbm: '000001'
                }
            ]
            if (common.openConfirm("确认删除该条信息吗？", function () {
                if (zhlist.length > 0) {
                    var json = '{"list":' + JSON.stringify(zhlist) + '}';
                    hzList.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=delete&',
                        json).then(function (data) {
                        if (data.body.a == 0) {
                            malert("删除成功")
                        } else {
                            malert("删除失败", 'right', 'defeadted')
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
                brzcajList.getTemData();
            })) {
                return false;
            }

        },
        shanchu: function () {
            let tpmxIndex = this.mxIndex
            if (tpmxIndex == undefined) {
                malert("请选择模板!", 'right', 'defeadted')
                return false;
            }
            let zhyzbm = this.yzList[tpmxIndex].zhyzbm;
            if (this.yzList[tpmxIndex].yyz != userId) {
                malert("该模板不是您创建!", 'right', 'defeadted')
                return false;
            }
            if (this.isChecked.length > 0) {
                let zllist = [];
                let yplist = [];

                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        if (this.yzMxList[i].lx == '药品') {
                            yplist.push({
                                zhyzbm: zhyzbm,
                                zhypyzxh: this.yzMxList[i].zhypxh,
                                yljgbm: '000001'
                            })
                        } else {
                            zllist.push({
                                zhyzbm: zhyzbm,
                                zhypxh: this.yzMxList[i].zhypxh,
                                yljgbm: '000001'
                            })
                        }


                    }
                }
                if (common.openConfirm("确认删除该条信息吗？", function () {
                    if (zllist.length > 0) {
                        var json = '{"list":' + JSON.stringify(zllist) + '}';
                        hzList.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhylyz&types=delete&',
                            json).then(function (data) {
                            if (data.body.a == 0) {
                                malert("删除成功")
                            } else {
                                malert("删除失败", 'right', 'defeadted')
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    }
                    if (yplist.length > 0) {
                        var json = '{"list":' + JSON.stringify(yplist) + '}';
                        hzList.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhypyz&types=delete', json).then(function (data) {
                            if (data.body.a == 0) {
                                malert("删除成功", 'right')
                            } else {
                                malert("删除失败", 'right', 'defeadted')
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    }
                    yzMxList.getTemMxData(tpmxIndex);
                })) {
                    return false;
                }


            }
        },
        //保存
        confirms: function () {
            if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                return false;
            }
            if (this.isChecked.length > 0) {

                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        delete this.yzMxList[i].shbz
                        delete this.yzMxList[i].shhs
                        delete this.yzMxList[i].shhsxm
                        delete this.yzMxList[i].shsj
                        delete this.yzMxList[i].zxbz
                        delete this.yzMxList[i].zxhs
                        delete this.yzMxList[i].tsyz;
                        delete this.yzMxList[i].zxhsxm
                        delete this.yzMxList[i].zxks
                        delete this.yzMxList[i].zxksmc
                        delete this.yzMxList[i].zxsj
                        delete this.yzMxList[i].ystzbz
                        delete this.yzMxList[i].tzys
                        delete this.yzMxList[i].tzysxm
                        delete this.yzMxList[i].ystzsj
                        delete this.yzMxList[i].ystzsm
                        delete this.yzMxList[i].hstzbz
                        delete this.yzMxList[i].tzhs
                        delete this.yzMxList[i].tzhsxm
                        delete this.yzMxList[i].hstzsj
                        delete this.yzMxList[i].sqdh
                        delete this.yzMxList[i].ysqm
                        delete this.yzMxList[i].ysqmxm
                        delete this.yzMxList[i].ysqmsj
                        delete this.yzMxList[i].xdys
                        delete this.yzMxList[i].xdysxm
                        this.yzMxList[i].yzlx = '0';
                        this.yzMxList[i].ksrq = getTodayDateTime();
                        this.yzMxList[i].ysqmks = userNameBg.Brxx_List.ssks;
                        this.yzMxList[i].ysqmksmc = userNameBg.Brxx_List.ssksmc;
                        this.yzMxList[i].ksbm = panel.is_csqx.cs01006400106;
                        // this.yzMxList[i].ksmc = userNameBg.Brxx_List.ksmc;
                        this.yzMxList[i].yfbm = panel.popContent.yfbm;
                        this.yzMxList[i].yfmc = panel.popContent.yfmc;
                        this.yzMxList[i].yzfl = '8';
                        this.yzMxList[i].sfcy = '0';
                        this.yzMxList[i].yyts = 1;
                        this.yzMxList[i].insertBz = true;
                        this.yzMxList[i].updateBz = false;
                        this.yzMxList[i].readonly = false;
                        this.yzMxList[i].xssx = hzList.Yzxx_List.length + 1;
                        this.yzMxList[i].zt = '4';
                        this.yzMxList[i].yzxh = null;
                        this.yzMxList[i].shbz = '0';
                        hzList.Yzxx_List.push(JSON.parse(JSON.stringify(this.yzMxList[i])));
                    }
                }
                this.isChecked = []
                this.mxIndex = undefined
            } else {
                malert("请选择需要复制的医嘱", 'top', 'defeadted');
                return false;
            }

            this.num = 1
            this.$nextTick(function () {
                hzList.$refs.body.scrollTop = hzList.$refs.body.scrollHeight
            })
        },
        commonResultChange: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                // case "yzlx":
                //     Vue.set(this.param, 'yzlx', val[0]);
                //
                //     brzcList.getTemData();
                //     break;
                // case "ypbz":
                //     Vue.set(this.param, 'ypbz', val[0]);
                //     brzcList.getTemData();
                //     break;
                case "sfgr":
                    Vue.set(this.param, 'sfgr', val[0]);
                    if (val[0] && val[0] != '0') {
                        Vue.set(this.param, 'yyz', userId);
                    } else {
                        Vue.set(this.param, 'yyz', '');
                    }

                    brzcList.getTemData();
                    break;
                // case "sfhzyz":
                //     Vue.set(this.param, 'sfhzyz', val[0]);
                //     if (val[0] == '1') {
                //         brzcList.getHzyzTemData(brzcList.mxIndex);
                //     } else {
                //         brzcList.getLsTemMxData(brzcList.mxIndex, null);
                //     }
                //     break;
            }
        },
        //所有在院患者
        getLsTemData: function (index) {
            this.isyz = false;
            this.mxIndex = index;
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm={"ryks":"' + panel.is_csqx.cs01006400106 + '"}', function (json) {
                if (json.a == '0') {
                    changeW();
                    brzcList.lsYzList = json.d.list;
                    brzcList.getLsTemMxData(0);
                    // $(".content-right-list").uiscroll({ height: '100%', size: '3px', opacity: 1 });
                }
            });
        },
        scrollGata(event) {
            if (event.srcElement.scrollHeight - event.srcElement.scrollTop === event.srcElement.clientHeight) {
                if (event.target.scrollLeft < 0 || this.scrollLeft == event.target.scrollLeft) {
                    if (event.target.scrollTop > this.scrollTop) {
                        if (this.scollType) {
                            this.scollType = false
                            if (this.yzList.length < this.total) {
                                if (this.uilPageBottom() == true) {
                                    this.param.page = this.param.page + 1;
                                    this.getTemData();
                                }
                            } else {
                            }
                        }
                    }
                }
            }
            this.scrollLeft = event.target.scrollLeft
            this.scrollTop = event.target.scrollTop
        },
        //医嘱模板
        getTemData: function () {
            this.isyz = true;
            this.yzMxList = [];
            this.param.rows = 20;
            this.param.sort = 'zhyzbm';
            this.param.order = 'desc';
            this.param.sfcy = brzcList.sfcy;
            if (this.param.sfgr == 0) {
                this.param.yyks = ksbm;
                this.param.yyz = "";
            } else {
                this.param.yyz = userId;
                this.param.yyks = "";

            }
            //            this.param.lx='2';
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=queryCfmb&parm=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) {
                    brzcList.scollType = true;
                    brzcList.total = json.d.total;
                    brzcList.yzList = json.d.list;
                }
            });
        },
    },
});
var toolMenu_yzd = new Vue({
    el: '.toolMenu_yzd',
    data: {
        which: 0,
        pageList: [],
        pageH: 860
    },
    methods: {
        long: function (num) {
            this.which = num;
            cqyzd.which = num;
            lsyzd.isShow = false;
            cqyzd.isShow = true;
            cqyzd.getData();
        },
        short: function (num) {
            this.which = num;
            cqyzd.which = num;
            cqyzd.isShow = false;
            lsyzd.isShow = true;
            lsyzd.getData();
        },
    }
});
var cqyzd = new Vue({
    el: '.cqyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        list: [],
        jsonList: [],
        isShow: false,
        param: {},
        BrxxJson: [],
        isGoPrint: false,
        which: 0,
        pageList: [],
        pageH: 790
    },
    methods: {
        doPrint: function (isGoOn) {
            var cqTr;
            cqPrint.list = [];
            lsPrint.list = [];
            if (this.which == 0) {
                cqTr = $(".cqyzd tr");
            } else {
                cqTr = $(".lsyzd tr");
            }
            var _height = 0;
            var a = 0, b = -1;
            for (var i = 1; i < cqTr.length - 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height >= cqyzd.pageH) {
                    b++;
                    var as = [];
                    for (var f = a; f < i; f++) {
                        if (this.which == 0) {
                            as.push(cqyzd.jsonList[f]);
                        } else {
                            as.push(lsyzd.jsonList[f]);
                        }
                    }
                    if (this.which == 0) cqPrint.list[b] = as;
                    else lsPrint.list[b] = as;
                    a = i;
                    _height = 0;
                    this.pageList.push(as.length);
                }
            }
            var pp = [];
            if (this.which == 0) {
                for (var p = a; p < cqyzd.jsonList.length; p++) {
                    pp.push(cqyzd.jsonList[p]);
                }
            } else {
                for (var ls = a; ls < lsyzd.jsonList.length; ls++) pp.push(lsyzd.jsonList[ls]);
            }
            for (var l = 0; l < 21; l++) {
                _height += 40;
                if (_height >= cqyzd.pageH) {
                    break;
                }
                pp.push({'psjg': '无'});
            }
            if (this.which == 0) {
                cqPrint.list[b + 1] = pp;
                cqPrint.isShow = true;
                lsPrint.isShow = false;
            } else {
                lsPrint.list[b + 1] = pp;
                lsPrint.isShow = true;
                cqPrint.isShow = false;
            }
            cqyzd.showTable(cqyzd.which);
            if (isGoOn) {
                cqPrint.isGoPrint = true;
                lsPrint.isGoPrint = true;
                setTimeout(function () {
                    cqyzd.hideTable(cqyzd.which);
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border-left', '1px solid transparent');
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("tr").css('border', '1px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border-left', '1px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("tr").css('border', '1px solid transparent');
                }, 50);
                setTimeout(function () {
                    window.print();
                    cqPrint.isGoPrint = false;
                    lsPrint.isGoPrint = false;
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border-left', '1px solid #999');
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("tr").css('border', '1px solid #999');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border-left', '1px solid #999');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("tr").css('border', '1px solid #999');
                }, 100);
            } else {
                setTimeout(function () {
                    window.print();
                    printGd = 20
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                }, 100);
            }
        },
        hideTable: function (type) {
            var num = 0;
            if (type == 0 && cqyzd.isChecked == cqPrint.isChecked) {
                for (var i = 0; i < cqPrint.pagePrint; i++) {
                    $('.cqPrint .popCenter').eq(i).hide();
                    num += this.pageList[i];
                }
                cqPrint.isChecked = cqPrint.isChecked - num;
                cqPrint.$forceUpdate()
            } else if (type == 0) {
                cqPrint.isChecked = cqyzd.isChecked
                this.hideTable(cqyzd.which)
            }
            if (type == 1 && lsyzd.isChecked == lsPrint.isChecked) {
                for (var j = 0; j < lsPrint.pagePrint; j++) {
                    $('.lsPrint .popCenter').eq(j).hide();
                    num += this.pageList[j];
                }
                lsPrint.isChecked = lsPrint.isChecked - num;
                lsPrint.$forceUpdate()
            } else if (type == 1) {
                lsPrint.isChecked = lsyzd.isChecked
                this.hideTable(cqyzd.which)

            }
        },
        showTable: function (type) {
            if (type == 0) {
                for (var i = 0; i < $('.cqPrint .popCenter').length; i++) {
                    $('.cqPrint .popCenter').eq(i).show();
                }
                cqPrint.$forceUpdate()
            } else {
                for (var j = 0; j < $('.lsPrint .popCenter').length; j++) {
                    $('.lsPrint .popCenter').eq(j).show();
                }
            }
        },
        goPrint: function (index) {
            cqyzd.isChecked = index;
            cqPrint.isChecked = index;
            var cqTr = $(".cqyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > 720) {
                    b++;
                    _height = 0;
                }
            }
            cqPrint.pagePrint = b;
        },
        getData: function () {
            cqyzd.BrxxJson = userNameBg.Brxx_List;
            cqPrint.BrxxJson = userNameBg.Brxx_List;
            if (userNameBg.Brxx_List.zyh == null || userNameBg.Brxx_List.zyh == '' || userNameBg.Brxx_List.zyh == undefined) {
                malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                return
            }
            this.param = {
                page: 1,
                rows: 100,
                sort: '',
                order: 'asc',
                zyh: userNameBg.Brxx_List.zyh,
                yzlx: '1',
                ssks: userNameBg.Brxx_List.ssks
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    cqyzd.jsonList = json.d.list;
                    for (var i = 0; i < cqyzd.jsonList.length; i++) {
                        cqyzd.jsonList[i]['xmmc'] = cqyzd.jsonList[i]['xmmc'].replace('null', '');
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc'].replace('null', '');
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc'].replace('null', '');

                    }
                } else {
                    malert("病人医嘱单信息查询失败！", 'top', 'defeadted');
                }
            });
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var prvVal = null, nextVal = null;
            if (index != this.jsonList.length - 1 && index != 0) {
                prvVal = this.jsonList[index - 1][name];
                nextVal = this.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index) {
            console.log(11);
            var fzh = this.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList.length - 1) {
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }

            }
            if (index == this.jsonList.length - 1) {
                return 'end'
            }
            return 'null'
        },
        isShowItem: function (index) {
            if (this.jsonList[index + 1] == null) {
                return true;
            }
            if (this.jsonList[index]['fzh'] == this.jsonList[index + 1]['fzh'] && this.jsonList[index]['fzh'] != 0) {
                if (this.jsonList[index]['yyffmc'] == this.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var cqPrint = new Vue({
    el: '.cqPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: 0,
        BrxxJson: cqyzd.BrxxJson,
        isGoPrint: false
    },
    filters: {
        compuGd: function (index) {
            if (!cqyzd.isGoOn) {
                if (index >= 1) {
                    return 'paddingTop:30px'
                }
            } else {
                if (index >= cqPrint.pagePrint + 1) {
                    return 'paddingTop:30px'
                }
            }

        },
    },
    methods: {
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                cqPrint.isGoPrint = false;
                $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var val = cqyzd.jsonList[index][name];
            var prvVal = cqyzd, nextVal = null;
            if (index != cqyzd.jsonList.length - 1 && index != 0) {
                prvVal = cqyzd.jsonList[index - 1][name];
                nextVal = cqyzd.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqPrint.Appendzero((reDate.getMonth() + 1)) + '-' + cqPrint.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqPrint.Appendzero(reDate.getHours()) + ':' + cqPrint.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var fzh = cqyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == cqyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != cqyzd.jsonList.length - 1) {
                var nextFzh = cqyzd.jsonList[index + 1]['fzh'];
                var prvFzh = cqyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }

            }
            if (index == cqyzd.jsonList.length - 1) {
                return 'end'
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var lsyzd = new Vue({
    el: '.lsyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        jsonList: [],
        isShow: false,
        param: {},
        BrxxJson: [],
        isGoPrint: false
    },
    methods: {
        goPrint: function (index) {
            lsyzd.isChecked = index;
            lsPrint.isChecked = index;
            var cqTr = $(".lsyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > cqyzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            lsPrint.pagePrint = b;
        },
        getData: function () {
            lsyzd.BrxxJson = userNameBg.Brxx_List;
            lsPrint.BrxxJson = userNameBg.Brxx_List;
            if (userNameBg.Brxx_List.zyh == null || userNameBg.Brxx_List.zyh == '' || userNameBg.Brxx_List.zyh == undefined) {
                malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                return
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                zyh: userNameBg.Brxx_List.zyh,
                yzlx: '0',
                ssks: userNameBg.Brxx_List.ssks,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    lsyzd.jsonList = json.d.list;
                    for (var i = 0; i < lsyzd.jsonList.length; i++) {
                        lsyzd.jsonList[i]['xmmc'] = lsyzd.jsonList[i]['xmmc'].replace('null', '');
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc'].replace('null', '');
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc'].replace('null', '');
                    }
                } else {
                    malert("查询临时医嘱失败！", 'top', 'defeadted');
                }
            });
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = this.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            if (val == prvVal && index != 0) {
                return '"';
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList.length - 1) {
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index) {
            if (this.jsonList[index + 1] == null) {
                return true;
            }
            if (this.jsonList[index]['fzh'] == this.jsonList[index + 1]['fzh']) {
                if (this.jsonList[index]['yyffmc'] == this.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var lsPrint = new Vue({
    el: '.lsPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: null,
        BrxxJson: lsyzd.BrxxJson,
        isGoPrint: false
    },
    filters: {
        compuGd: function (index) {
            if (!cqyzd.isGoOn) {
                if (index >= 1) {
                    return 'paddingTop:30px'
                }
            } else {
                if (index >= cqPrint.pagePrint + 1) {
                    return 'paddingTop:30px'
                }
            }

        },
    },
    methods: {
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                lsPrint.isGoPrint = false;
                $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = lsyzd.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            if (val == prvVal && index != 0) {
                return '"';
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) {
                return null;
            }
            var fzh = lsyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == lsyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != lsyzd.jsonList.length - 1) {
                var nextFzh = lsyzd.jsonList[index + 1]['fzh'];
                var prvFzh = lsyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            if (lsyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (lsyzd.jsonList[index]['fzh'] == lsyzd.jsonList[index + 1]['fzh']) {
                if (lsyzd.jsonList[index]['yyffmc'] == lsyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});
cqyzd.getData();
var a = 0;
var check = '', copy = [];
document.onkeydown = function (event) {
    if (event.ctrlKey && event.keyCode === 65) {
        console.log('ctrl + a');
        hzList.checkAll = true;
        hzList.isChecked = [];
        for (var init in hzList.Yzxx_List) {
            Vue.set(hzList.isChecked, parseInt(init), true)
        }
    }
    if (event.ctrlKey && event.keyCode === 67) {
        console.log('ctrl + c');
        if (hzList.isChecked.length > 0) {
            for (var i = 0; i < hzList.isChecked.length; i++) {
                if (hzList.isChecked[i] == true) copy.push(JSON.parse(JSON.stringify(hzList.Yzxx_List[i])))
            }
            check = copy;
            copy = []
        }
    }
    if (event.ctrlKey && event.keyCode === 86) {
        console.log('ctrl + v');
        if (check != '') {
            hzList.Yzxx_List = hzList.Yzxx_List.concat(JSON.parse(JSON.stringify(check)))
        }
    }
};
panel.Wf_init();
hzList.Wf_selectYZ();
//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    hzList.searchCon = [];
    e.stopPropagation();
});

