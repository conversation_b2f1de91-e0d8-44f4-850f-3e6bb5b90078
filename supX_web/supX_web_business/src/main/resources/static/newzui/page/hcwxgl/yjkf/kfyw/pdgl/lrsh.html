<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<div id="lrsh" class="padd-t-10 padd-l-10">
    <div class="toolMenu toolMenu_3 flex-container flex-align-c" >
        <button @click="shPdlr" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-check-square-o"></span>审核
        </button>
        <button @click="zfPdlr" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-scissors"></span>作废</button>
        <div class="flex-container flex-align-c padd-r-10">
            <span class="padd-r-5">盘点凭证号</span>
            <select class="zui-input wh120" v-model="pdlrpz" @change="getPdlrList()">
                <option :value="0">-请选择-</option>
                <option v-for="item in pdblrList" v-text="item.pdpzh" :value="item"></option>
            </select>
        </div>
        <div class="flex-container padd-r-10 flex-align-c">
            <span class="padd-r-5">材料检索</span>
            <input type="text" class="zui-input wh120" v-model="ypjs">
        </div>
    </div>
    <div class="flex-container">
        <div class="tab-card enter_djList_sh">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">单据列表</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="grid-box">
                    <div class="zui-table-view hzList  padd-t-10">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>盘点单号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>盘点日期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell text-left cell-s"><span>制单人</span></div>
                                    </th>
                                </tr>
                                <!--@click="checkOne($index)"-->
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                            <table class="zui-table table-width50" v-if="jsonList.length!=0">
                                <tbody>
                                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()"
                                    @click="checkOne($index),getPdblrmx(item)"
                                    :tabindex="$index"
                                    v-for="(item, $index) in jsonList"
                                    @dblclick="edit($index)">
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.pdpzh"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.zdrq"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.zdy"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card enter_djDetail_sh">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">单据明细</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="grid-box">
                    <div class="zui-table-view hzList  padd-t-10">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>材料编码</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>材料名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>规格</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>材料批号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>库存数量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>录入数量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单位</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>零价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>零价金额</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>生产批号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>生产日期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>库房单位</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>产地</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>供货单位</span></div>
                                    </th>
                                </tr>
                                <!--@click="checkOne($index)"-->
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()"
                                    @click="checkOne($index)"
                                    :tabindex="$index"
                                    v-for="(item, $index) in jsonList"
                                    @dblclick="edit($index)">
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.ypbm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l title" v-text="item.ypmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l title" v-text="item.ypgg"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.xtph"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.kcsl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.lrsl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.yfdwmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.yplj"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.ljje"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.scph"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.scrq"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.yxqz"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.kfdwmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.fzbl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.cdmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="item.ghdwmc"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    <script type="text/javascript" src="lrsh.js"></script>