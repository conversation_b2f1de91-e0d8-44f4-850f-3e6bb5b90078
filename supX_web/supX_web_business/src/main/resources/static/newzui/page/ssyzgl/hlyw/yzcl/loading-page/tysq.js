var tysq = new Vue({
    el: '#loadingPage',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        brInfo: {},
        brlistjson: {},//只用于接受请求LIST对象
        brList: [],
        yzList: [],
        tydmxList: [],
        jkList: [],
        num: 0,
        yzshInfoList: [],//真正的列表
        zyhs: [],
        ksid: null,//科室编码
        jsContent: {},
        allKs: [],//科室列表
        text: null,//检索关键字
        fymxList: [],
        ifClick: true,
        tydhSelected:null
    },
    created:function(){
        this.moun();
    },
    mounted: function () {
        this.getYf();
        window.addEventListener('storage', function (e) {
            if (e.key == 'tysq' && e.oldValue !== e.newValue) {
                tysq.zyhs = [];
                tysq.moun();
                tysq.getData();
            }
        });
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#timeVal',
            rigger: 'click',
            theme: '#1ab394',
            range: true,
            done: function (value, data) {
                if (value != '') {
                    var A_rq = value.split(' - ');
                    tysq.beginrq = A_rq[0];
                    tysq.endrq = A_rq[1];
                    tysq.getTyList();
                }
            }
        });

    },
    updated:function(){
        changeWin()
    },
    computed: {
        defaultYfbm: function () {
            if (this.brlistjson.csqx && this.brlistjson.csqx.cs004200236) {
                for (var i = 0; i < this.allKs.length; i++) {
                    if (this.allKs[i].yfbm == this.brlistjson.csqx.cs004200236) {
                        Vue.set(this.jsContent, 'yfbm', this.allKs[i].yfbm);
                    }
                }
            } else if (this.allKs.length > 0) {
                Vue.set(this.jsContent, 'yfbm', this.allKs[0].yfbm);
            }
            // if (this.jsContent.yfbm) {
                this.getData();
            // }
        }
    },
    methods: {
		checkSelect: function (val, event, type) {
		    if (val[1] !== 'all') {
				this.activeIndex = val[0];
				let tpyp =  new Array();
				for (let i = 0; i < tysq.fymxList.length; i++) {
					if(i != val[0] && tysq.fymxList[i].ryypbm != tysq.fymxList[val[0]].ryypbm && tysq.fymxList[i].yyrq == tysq.fymxList[val[0]].yyrq && tysq.fymxList[i].yzxh == tysq.fymxList[val[0]].yzxh && tysq.fymxList[i].fzh!=0 && tysq.fymxList[i].fzh == tysq.fymxList[val[0]].fzh){
						
						tpyp.push(tysq.fymxList[i].ryypmc);
					}
				}
				if(tpyp && tpyp.length>0){
					let ypts = tpyp.join();
					malert('该药品与'+ypts+'成组，请注意', 'top', 'defeadted');
				}
				
				
			}
		},
        editComputed:function (item){
            item.sqsl=item.fysl-item.tysl-item.ysqsl
        },
        moun: function () {
            this.brlistjson = JSON.parse(sessionStorage.getItem("tysq"));
            this.brList = this.brlistjson.brlist;
            this.brInfo = this.brList[0];
            if (this.brInfo.nl < 7 && this.brInfo.brxb == '1') {
                this.brInfo.nljd = '1';
            } else if (this.brInfo.nl < 7 && this.brInfo.brxb == '2') {
                this.brInfo.nljd = '2';
            } else if (this.brInfo.nl < 18 && this.brInfo.nl > 6 && this.brInfo.brxb == '1') {
                this.brInfo.nljd = '3';
            } else if (this.brInfo.nl < 18 && this.brInfo.nl > 6 && this.brInfo.brxb == '2') {
                this.brInfo.nljd = '4';
            } else if (this.brInfo.nl < 41 && this.brInfo.nl > 17 && this.brInfo.brxb == '1') {
                this.brInfo.nljd = '5';
            } else if (this.brInfo.nl < 41 && this.brInfo.nl > 17 && this.brInfo.brxb == '2') {
                this.brInfo.nljd = '6';
            } else if (this.brInfo.nl < 66 && this.brInfo.nl > 40 && this.brInfo.brxb == '1') {
                this.brInfo.nljd = '7';
            } else if (this.brInfo.nl < 66 && this.brInfo.nl > 40 && this.brInfo.brxb == '2') {
                this.brInfo.nljd = '8';
            } else if (this.brInfo.nl > 65 && this.brInfo.brxb == '1') {
                this.brInfo.nljd = '9';
            } else if (this.brInfo.nl > 65 && this.brInfo.brxb == '2') {
                this.brInfo.nljd = '10';
            } else {
                this.brInfo.nljd = '11';
            }
            this.ksid = this.brlistjson.ksid;
            for (var i = 0; i < this.brList.length; i++) {
                var zyh = {
                    zyh: this.brList[i].zyh
                };
                this.zyhs.push(zyh);
            }
            console.log(this.brlistjson.csqx.N03004200707)
            sessionStorage.removeItem('tysq')
        },
        tabBg: function (index) {
            this.num = index;
           this.getTyList()
        },
        getTyList:function(){
            if (this.num == 0) {
                this.getData()
            } else {
                this.getTysqData()
            }
        },
        closePage: function () { //关闭本页面
            var x = parseInt(sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem('hszHzlbUpdate', x);
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/slyp.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        },
        getYf: function () {
            //初始化药房
            var yf_dg = {page: 1, rows: 1000, sort: "yfbm", order: "asc", parm: ""};
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmYf&types=query&dg=' + JSON.stringify(yf_dg), function (data) {
                if (data.a == 0) {
                    tysq.allKs = data.d.list;  //绑定药房
                    Vue.set(tysq.jsContent, 'yfbm', tysq.allKs[0].yfbm);
                    tysq.getTyList();
                } else {
                    malert('获取药房失败', 'top', 'defeadted');
                }
            });
        },
        //药房选择
        resultChange_ks: function (val) {
            Vue.set(this.jsContent,"yfbm",val[0]);
            // if (val[2].length > 1) {
            //     if (Array.isArray(this[val[2][0]])) {
            //         Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
            //         this.jsContent.yfmc = val[4];
            //     } else {
            //         Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            //         if (val[3] != null) {
            //             Vue.set(this[val[2][0]], val[3], val[4]);
            //         }
            //         this.jsContent.yfmc = val[4];
            //     }
            // } else {
            //     this[val[2][0]] = val[0];
            //     this.jsContent.yfmc = val[4];
            // }
            // if (val[1] != null) {
            //     this.nextFocus(val[1]);
            // }
            this.getTyList()
        },
        openTy:function(){
            var brJson = {
                ksbm: this.brList[0].ryks,
                num: 0,
            };
            sessionStorage.setItem('bqby', JSON.stringify(brJson));
            this.topNewPage('退药审核', 'page/yfgl/yfyw/bqby/bqby.html');
        },
        saveTy: function () {

        	if(this.brlistjson.zyType == 'bqcy' || this.brlistjson.zyType == 'jscy'){
        		malert('病区出院或结算出院不能操作退药！' , 'top', 'defeadted');
        		return;
        	}

            $.ajaxSettings.async = false;
            if (!tysq.ifClick) return;// 已经点击过就不能再点
            tysq.ifClick = false;
            var tylist = [];
            for (var i = 0; i < tysq.fymxList.length; i++) {
                if (tysq.fymxList[i].sqsl != null && tysq.fymxList[i].sqsl != undefined && tysq.fymxList[i].sqsl > 0 && (tysq.fymxList[i].fysl-tysq.fymxList[i].tysl-tysq.fymxList[i].ysqsl)>=tysq.fymxList[i].sqsl) {
                    tylist.push(tysq.fymxList[i]);
                }
            }
            if (tylist.length < 1) {
                malert("无退药申请！", 'top', 'defeadted');
				tysq.ifClick = true;
                return
            }
            var json = '{"list":' + JSON.stringify(tylist) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywBqty&types=bqty&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    tysq.getData();
                    tysq.ifClick = true;
                    malert("退药申请成功！", 'top', 'defeadted');
                    //发送通知
                    //查询药房对应科室编码
                    var yfbm = tysq.jsContent.yfbm;
                    var serachparm = {
                        yfbm: yfbm,
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=queryKsbmByYf&parm=' + JSON.stringify(serachparm),
                        function (json) {
                            if (json.d.list.length > 0) {
                                var sendmsg = {
                                    msgtype: '3',
                                    ksbm: json.d.list[0].ksbm,
                                    yljgbm: jgbm,
                                    yqbm: yqbm,
                                    msg: '有新的退药申请,请悉知!',
                                    toaddress: 'page/yfgl/yfyw/bqby/bqby.html',
                                    pagename: '病区摆药',
                                    sbid: tysq.jsContent.yfbm + "_" + tysq.fDate(new Date(), 'YY'),
                                    ylbm: 'N040030022004',
                                };
                                console.log(sendmsg);
                                window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                            }
                        }, function (error) {
                            console.log(error);
                        });
                } else {
                    malert('上传数据失败:' + data.body.c, 'top', 'defeadted');
                    tysq.ifClick = true;
                }
            }, function (error) {
                console.log(error);
            });
        },

        getData: function () {      // 根据病人信息获取药品
            var zyh = this.brInfo.zyh;
            var yfbm = this.brlistjson.csqx.N03004200707 ==1 ?'' : this.jsContent.yfbm;
            var ksbm = this.ksid;
            var beginrq = this.beginrq
            var endrq = this.endrq
            if(this.beginrq!=null&&this.beginrq!=''){
                beginrq = this.beginrq+' 00:00:00';
            }
            if(this.endrq!=null&&this.endrq!=''){
                endrq = this.endrq+' 23:59:59';
            }
            var parm = this.text;
            if (zyh != null && zyh != '') {
                var parm = {
                    ksbm: ksbm,
                    yfbm: yfbm,
                    zyh: zyh,
                    beginrq: beginrq,
                    endrq: endrq,
                    parm: parm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=bydfymx&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        tysq.fymxList = json.d.list;
                        console.log(tysq.fymxList);
                        for (var i = 0; i < tysq.fymxList.length; i++) {
                            if (tysq.fymxList[i].tysl > 0 || tysq.fymxList[i].fysl < 0) {
                                tysq.fymxList[i].sfty = 1;
                            }
                        }
                        console.log(tysq.fymxList);
                    } else {
                        malert(json.c, "医嘱费用明细查询失败：");
                    }
                });
            }
        },
        tyslchange: function (item) {
            console.log(item);
            var fysl = item.fysl; //发药数量
            var tysl = item.tysl; //已退数量
            var ysqsl = item.ysqsl; //已申请数量
            var sqsl = item.sqsl; //当前申请数量
            if (fysl - tysl - ysqsl - sqsl < 0) {
                item.sqsl = 0;
                malert("最大可退数量[" + (fysl - tysl - ysqsl) + "]");
            }
        },
        printTysqd: function (tysqdh) {
            if (!tysqdh && !this.tydhSelected) {
                malert("没有相关退药单，请确认是否有申请退药！", "top", "defeadted");
                return;
            }
            tysqdh = tysqdh?tysqdh:this.tydhSelected;
            var frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            var reportlets = "[{reportlet: 'hsz" + frpath + "hsz_hlyw_tysqd.cpt',tysqdh:'" + tysqdh + "',yljgbm:'" + jgbm + "'}]";
            if (!FrPrint(reportlets, null, null, true)) {
                window.print();
            }
        },
        getTydPerson: function (item,index) {
            this.tydmxList = item;
            this.tydhSelected = this.treeTydList[index].tysqdh;
            this.isCheckAll=true;
            for (var i = 0; i < this.tydmxList.length; i++) {
                this.isChecked[i] = true;
            }
        },
        getTysqData: function () {
            var pram = {
                "ylbm": 'N040030022004',
                "ryksbm": this.brInfo.ryks,
                "beginrq": this.beginrq,
                "endrq": this.endrq,
                'yfbm': this.brlistjson.csqx.N03004200707 ==1 ?'' : this.jsContent.yfbm,
                'ryyfbm': this.brlistjson.csqx.N03004200707 ==1 ?'' : this.jsContent.yfbm,
                'zyh': this.brInfo.zyh,
            };
            //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
            common.openloading('.tymx');
            this.tydmxList = [];
            this.treeTydList = [];
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywBqty&types=tydcx&parm=" + JSON.stringify(pram), function (data) {
                if (data.a == 0) {
                    tysq.treeTydList = data.d.list;
                    tysq.tydmxList = [];
                } else {
                    malert("退药单查询失败：" + data.c, 'top', 'defeadted');
                }
                common.closeLoading();
            });
        },
        notCheck: function () {
			            var tyid = [];
            for (var i = 0; i < this.tydmxList.length; i++) {
                if (this.isChecked[i] && this.tydmxList[i].hdbz =='0') {
                    tyid.push(this.tydmxList[i].tysqid);
                }
            }
            this.isChecked = [];//取消选择
            if (tyid.length <= 0) {
                malert("请选择需要退药审核药品明细!", 'top', 'defeadted');
                return;
            }
			
            var json = {
                searchtysqid: tyid
            };
            common.openloading('#tymx');
            this.$http.post('/actionDispatcher.do?reqUrl=HszHlywBqty&types=deleteTysq', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        malert("病区退药作废成功!", 'top', 'success');
                        tysq.getTyList()
                    } else {
                        malert(data.body.c, "top", "defeadted");
                    }
                    common.closeLoading();
                }, function (error) {
                    console.log(error);
                    common.closeLoading();
                });
        },
    }
});

