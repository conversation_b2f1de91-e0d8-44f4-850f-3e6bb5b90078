var jbbm = {'疾病编码': 'jbmb', '疾病名称': 'jbmc', '拼音代码': 'pydm'};
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat, scrollOps],
    components: {
        'jbsearch-table': searchTable,
    },
    data: {
        ifClick: true,
        popContent: {},
        jbsearchCon: {},
        jbbmContent: {},
        jbthem: jbbm,
        selSearch: -1,
        apList: {},
        BgList: {},
        ksList: [],
        ryList: [],
        sqysList:[],
        ghYsList: [],
        glghYsList: [],
        text: '',
//         group_type:{
//             '0':'预约会诊',
//             '1':'紧急会诊',
//             '2':'普通会诊',
// //            '3':'未上交',
// //            '4':'已上交',
//         },
        group_type: [
            {
                type: '预约会诊',
                number: '0',
            },
            {
                type: '紧急会诊',
                number: '1',
            },
            {
                type: '普通会诊',
                number: '2',
            },
        ],
        defaltObj: {
            title: '拒绝申请',
            cs: 'btn-parmary fr-right',
            cm: 'btn-parmary-f2a1 fr-right',
            bm: 'flex-none',
            cb: '取消',
            sb: '拒绝'
        },
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        csqxContent:{},
    },
    mounted: function () {
        this.getKsbm();
        this.initVal();
        this.getCsqx();
        console.log("======================================================");
        this.readyData({"ysbz": "1"}, "rybm", "glghYsList");
        window.addEventListener('setItemEvent', function (e) {
            if (e.key == 'userPage') {
                wrapper.initVal()
            }
        });
    },
    methods: {
    	//获取参数权限
        getCsqx: function () {
			let that = this;
			this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
			    .then(function (json) {
			        //获取参数权限
			        let parm = {
			            "ylbm": 'N030102002',
			            "ksbm": json.body.d.ksbm
			        };
			        that.$http.get("/actionDispatcher.do", {
			            params: {
			                reqUrl: 'CsqxAction',
			                types: 'csqx',
			                parm: JSON.stringify(parm)
			            }
			        }).then(function (json, status, xhr) {
			            if (json.body.a == 0) {
			                if(json.body.d){
			                    for (var i = 0; i < json.body.d.length; i++) {
			                        var csjson = json.body.d[i];
			                        switch (csjson.csqxbm) {
			                            case "N03010200203"://会诊开医嘱方式 0-默认（直接开）1-会诊建议医嘱(宁蒗)
			                                if(csjson.csz){
			                                    wrapper.csqxContent.N03010200203 = csjson.csz;
			                                }
			                                break;
			                        }
			                    }
			                }
			            } else {
			                malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
			            }
			        })
			    });
        },
        initVal: function () {
            console.log(sessionStorage.getItem('hzsqglitem')+"`````````````````````````");
            if (sessionStorage.getItem('hzsqglitem')) {
                console.log(sessionStorage.getItem('hzsqglitem')+"++++++++++++++++++++++++++++++++++++");
                //panel.mzbrgl_tran["2"] = "全院病人";
                this.popContent = JSON.parse(sessionStorage.getItem('hzsqglitem'));
                this.popContent.cbzd = JSON.parse(sessionStorage.getItem('hzsqglitem')).ryzdmc;
                this.popContent.sqysksmc = JSON.parse(sessionStorage.getItem('hzsqglitem')).ryksmc;
                this.popContent.sqys = JSON.parse(sessionStorage.getItem('hzsqglitem')).zyys;
                this.popContent.hzlx='2';
                if (!this.popContent.xbs) {//如果为空 调用电子病历接口查询
                    var id = [];
                    var xbs = {
                        zyh: this.popContent.zyh,
                        zybm: 'DSEMR001.00.02.0001',
                        zymc: '现病史',
                        // keyword : '',
                    };
                    var jws = {
                        zyh: this.popContent.zyh,
                        zybm: 'DSEMR001.00.03.0001',
                        zymc: '既往史',
                        keyword: '',
                    };
                    var gms = {
                        zyh: this.popContent.zyh,
                        zybm: 'DSEMR001.00.04.0001',
                        zymc: '个人史',
                        keyword: '',
                    };
                    var cbzd = {
                        zyh: this.popContent.zyh,
                        zybm: 'DSEMR001.01.01.0002',
                        zymc: '入院诊断1',
                        keyword: '',
                    };
                    var bqgs = {
                        zyh: this.popContent.zyh,
                        zybm: 'DSEMR001.00.01.0001',
                        zymc: '主诉',
                        keyword: '',
                    };
                    id.push(xbs);
                    id.push(jws);
                    id.push(gms);
                    id.push(cbzd);
                    id.push(bqgs);
                    console.log(sessionStorage.getItem('hzsqglitem'));

                    // let ryzdmc = JSON.parse(sessionStorage.getItem('hzsqglitem')).ryzdmc;
                    // let sqysksmc = JSON.parse(sessionStorage.getItem('hzsqglitem')).ryksmc;
                    // let sqysxm = JSON.parse(sessionStorage.getItem('hzsqglitem')).zyysxm;
                    $.getJSON("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=DOMAIN&method=DSEMR_DOMAIN&id=" + JSON.stringify(id) + "&json="
                        + JSON.stringify(this.param), function (json) {
                        if (json.a == "0" && json.d) {
                            var jsonArr = json.d;
                            for (var i = 0; i < jsonArr.length; i++) {
                                var zybm = jsonArr[i].ZYBM;
                                switch (zybm) {
                                    case "DSEMR001.00.02.0001"://现病史
                                        wrapper.popContent.xbs = jsonArr[i].EMRZYZ;
                                        break;
                                    case "DSEMR001.00.03.0001"://既往史
                                        wrapper.popContent.jws = jsonArr[i].EMRZYZ;
                                        break;
                                    case "DSEMR001.01.01.0002"://入院诊断1
                                        wrapper.popContent.cbzd = jsonArr[i].EMRZYZ;
                                        break;
                                    case "DSEMR001.00.04.0001"://过敏史
                                    	if(wrapper.csqxContent.N03010200203 == '0'){
                                    		wrapper.popContent.gms = jsonArr[i].EMRZYZ;
                                    	}

                                        break;
                                    case "DSEMR001.00.01.0001"://病情概述
                                        wrapper.popContent.bqgs = jsonArr[i].EMRZYZ;
                                        break;
                                }
                            }
                            wrapper.$forceUpdate();
                        } else {
                            malert(json.c, 'top', 'defeadted')
                        }
                    });
                }
                this.ryList = jsonFilter(this.glghYsList, "ksbm", this.popContent.yqysks);
                this.popContent.sqrq = this.popContent.sqrq ? this.fDate(this.popContent.sqrq, 'datetime') : getTodayDateTime();
                this.popContent.hzkssj = this.popContent.hzkssj ? this.fDate(this.popContent.hzkssj, 'datetime') : getTodayDateTime();
                sessionStorage.removeItem('hzsqglitem')
            }
        },
        change: function (add, type, val) {
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (type == 'jbbm') {
                this.popContent['cbzd'] = val;
                if (this.popContent['cbzd'] == undefined || this.popContent['cbzd'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.popContent['cbzd'];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm'
                    + '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) {//不是第一页则需要追加
                                wrapper.jbsearchCon=wrapper.jbsearchCon.concat(data.d.list);
                        } else {
                            wrapper.jbsearchCon = data.d.list;
                        }
                        wrapper.page.total = data.d.total;
                        wrapper.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }

        },
        changeDown: function (event, type, content, searchCon) {
            if (this[searchCon][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, content, searchCon);
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'jbbm') {
                    wrapper.popContent.cbzd = jbxwrapperx.jbbmContent['jbmc'];
                }
                this.selSearch = -1
            }
        },
        //鼠标双击（入院诊断信息）
        selectJbbm: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change(true, 'jbbm', this.jbbmContent['jbmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {     // 否则就是选中事件,为json赋值
                wrapper.jbbmContent = item;
                wrapper.popContent.cbzd = wrapper.jbbmContent['jbmc'];
                wrapper.$forceUpdate();
                $(".selectGroup").hide();
            }
        },
        //公用查询
        readyData: function (req, types, listName) {
            console.log(!req);
            if (!req) {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types, function (json) {
                    if (json.a == 0) {
                        wrapper[listName] = json.d.list;
                    } else {
                        malert(types + "查询失败", 'top', 'defeadted');
                    }
                });
            } else {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types + "&json=" + JSON.stringify(req), function (json) {
                    if (json.a == 0) {
                        wrapper[listName] = json.d.list;
                        wrapper.sqysList =  json.d.list;
                        if (wrapper.popContent.yqysks && listName == 'glghYsList'){
                                wrapper.ryList = jsonFilter(wrapper.glghYsList, "ksbm", wrapper.popContent.yqysks);
                        }
                    } else {
                        malert(types + "查询失败", 'top', 'defeadted');
                    }
                });
            }
        },

        commonResult: function (val) {
                    var type = val[2][val[2].length - 1];
                    this.popContent.sqys = val[0];
                    this.popContent.sqysxm = val[4];
                    this.$forceUpdate();
        },

        //申请科室数据请求方式
        commonResultChange: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "yqysks":
                    this.popContent.yqysks = val[0];
                    this.popContent.yqys = "";
                    this.popContent.yqysksmc = val[4];
                    //先清空
                    this.ryList = [];
                    //过滤挂号医生
                    this.ryList = jsonFilter(this.glghYsList, "ksbm", this.popContent.yqysks);
                    this.$forceUpdate();
                    break;
                case "yqys":
                    console.log("===================================================");
                    this.popContent.yqys = val[0];
                    this.popContent.yqysxm = val[4];
                    this.$forceUpdate();
                    break;
                default:
                    break;
            }
        },
        //拒绝申请
        refuse: function () {
            common.openConfirm('您确定要拒绝该会诊申请吗', function () {
                wrapper.topClosePage('page/hzxt/hzxt/hzshf/Subdirectory/apzj.html', 'page/hzxt/hzxt/hzshf/hzshf.html');
            }, function () {

            }, this.defaltObj)

        },
        //安排专家
        specialist: function () {
            this.topClosePage('page/hzxt/hzxt/hzshf/Subdirectory/apzj.html', 'page/hzxt/hzxt/hzshf/hzshf.html');
        },
        //返回
        Return: function () {
            this.topClosePage('page/hzxt/hzxt/hzshf/Subdirectory/apzj.html', 'page/hzxt/hzxt/hzsqgl/hzsqgl.html');
        },


        save: function () {
            if (this.popContent.hzzt && this.popContent.hzzt != "0") {
                malert("已经提交，不允许再操作！", "top", "defeadted");
                return;
            }
            this.popContent.hzzt = "0";
            this.doSaveFun();
        },
        printHzsqd: function () {
            if (this.popContent.hzbh) {
                var reportlets = "[{reportlet: 'fpdy%2Fzyys%2Fzyys_hzsq.cpt',hzbh:'" + this.popContent.hzbh + "'}]";
                if (!FrPrint(reportlets, null)) {
                    window.print();
                }
            } else {
                malert('请从会诊申请管理双击进入打印', 'top', 'defeadted');
            }
        },
        submit: function () {
            // if (this.popContent.hzzt && this.popContent.hzzt != "0") {
            //     malert("已经提交，不允许再操作！", "top", "defeadted");
            //     return;
            // }
            if(this.popContent.hzzt){
                this.popContent.hzzt = "1";
            }
            this.doSaveFun();
        },
        yzSave:function(){
            var yzList=[
                {
                    cklj: "",
                    fzh: null,
                    gllb: null,
                    insertBz: true,
                    jbjl: 1,
                    jcfl: "0",
                    jldw: "",
                    jldwmc: "",
                    kcfbz: "",
                    kssjb: "0",
                    lx: "诊疗",
                    mbbm: null,
                    nbtclb: "",
                    pcbm: null,
                    pccs: null,
                    pcmc: null,
                    readonly: false,
                    rqxgbz: "1",
                    updateBz: false,
                    //xmbm: '请'+(this.popContent.yqysksmc || '')+''+(this.popContent.yqysxm || '')+'医生会诊，会诊目的：'+(this.popContent.hzyy || '')+'',
                    //xmmc: '请'+(this.popContent.yqysksmc || '')+''+(this.popContent.yqysxm || '')+'医生会诊，会诊目的：'+(this.popContent.hzyy || '')+'',
                    xmbm: '请 '+(this.popContent.yqysksmc || '')+' 会诊',
                    xmmc: '请 '+(this.popContent.yqysksmc || '')+' 会诊',
                    xssx: 2,
                    ybtclb: "",
                    ybtclbmc: "",
                    yebh: "",
                    yfmc: "",
                    ypbz: "0",
                    ypgg: "",
                    ypzl: "",
                    ysqmks: this.popContent.ryks,
                    ysqmksmc: this.popContent.ryksmc,
                    ksmc: this.popContent.ryksmc,
                    ksbm: this.popContent.ryks,
                    yyffbm: null,
                    yyffmc: null,
                    yzlx: "0",
                    zt: "4",
                    yzfl:'0',
                    ksrq:this.fDate(new Date(),'datetime'),
                    tsyz:1,
                    sl:1,
                    zxks:this.popContent.ksbm,
                    zxksmc:this.popContent.ksmc,
                }
            ]
            var json = {
                list: [
                    {
                        hzxx: this.popContent,   //病人基本信息
                        yzxx: yzList,          //医嘱信息
                        lczd: []              //诊断信息（检查，检验的诊断）
                    }
                ]
            };
            this.postAjax('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzsave', JSON.stringify(json),function (data) {
                if (data.a == 0) {
                    wrapper.ifClick = true;
                    malert("医嘱保存成功", 'top', 'success');
                }else{
                    wrapper.ifClick = true;
                    malert("医嘱保存失败" + data.c, 'top', 'defeadted');
                }
            })
        },
        doSaveFun: function () {
            if (!this.ifClick) return; //如果为false表示已经点击了不能再点
            this.ifClick = false;

            // if (!this.popContent.hzyy) {
            //     malert("会诊原因不能为空！", "top", "defeadted");
            //     this.popContent.hzzt = "0";
            //     this.ifClick = true;
            //     return;
            // }
            // if (!this.popContent.cbzd) {
            //     malert("初步诊断不能为空！", "top", "defeadted");
            //     this.popContent.hzzt = "0";
            //     this.ifClick = true;
            //     return;
            // }
            // if (!this.popContent.bqgs) {
            //     malert("病情概述不能为空！", "top", "defeadted");
            //     this.popContent.hzzt = "0";
            //     this.ifClick = true;
            //     return;
            // }
            // if (!this.popContent.hzlx) {
            //     malert("会诊类型不能为空！", "top", "defeadted");
            //     this.popContent.hzzt = "0";
            //     this.ifClick = true;
            //     return;
            // }
            // if (!this.popContent.hzdd) {
            //     malert("会诊地点不能为空！", "top", "defeadted");
            //     this.popContent.hzzt = "0";
            //     this.ifClick = true;
            //     return;
            // }
            if (!this.popContent.yqysks) {
                malert("会诊科室不能为空！", "top", "defeadted");
                this.popContent.hzzt = "0";
                this.ifClick = true;
                return;
            }
            // if (!this.popContent.hzmd) {
            //     malert("会诊目的不能为空！", "top", "defeadted");
            //     this.popContent.hzzt = "0";
            //     this.ifClick = true;
            //     return;
            // }
            if(!this.popContent.hzfs){
                this.popContent.hzfs = "1";
            }
                      if (this.popContent.hzzt !=2) {
                this.popContent.hzzt = "1";
            }
            this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=saveHzgl&', JSON.stringify(this.popContent)).then(function (data) {
                if (data.body.a == 0) {
                    malert("操作成功！");
                    if(!wrapper.popContent.editHz){
                        wrapper.yzSave()
                    }
                    if (wrapper.popContent.hzzt == "1") {

                        //Todo:发送消息通知，待办
                        var sendmsg = {
                            msgtype: '110',
                            ksbm: wrapper.popContent.yqysks,
                            yljgbm: jgbm,
                            yqbm: yqbm,
                            msg: '有会诊邀请待处理，病人：' + wrapper.popContent.brxm + ",住院号:" + wrapper.popContent.zyh,
                            toaddress: '/newzui/page/hzxt/hzxt/dhzgl/dhzgl.html',
                            sbid: wrapper.popContent.zyh + "_" + wrapper.fDate(new Date(), 'YY'),
                            ylbm: 'N030102002',
                            pagename: "待会诊管理"
                        };
                        console.log(sendmsg);
                        window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));

                        //todo:短信平台，短信平台

                    }
                    //todo:刷新跳转页面
                    sessionStorage.setItem('apzj_saveHzsq', new Date().getTime());
                    wrapper.topClosePage('page/hzxt/hzxt/hzshf/Subdirectory/apzj.html', 'page/hzxt/hzxt/hzsqgl/hzsqgl.html');
                } else {
                    wrapper.ifClick = true;
                    malert(data.body.c, 'top', 'defeadted');
                    return;
                }
            }, function (error) {
                wrapper.ifClick = true;
                console.log(error);
            });
        },
        getKsbm: function () {
            var parm_str = {
                rows: 99999,
                page: 1
            };
            this.$http.get('/actionDispatcher.do', {params:{reqUrl:'GetDropDown',types:'ksbm',json:JSON.stringify({"zyks": "1"}),dg:JSON.stringify(parm_str)}}).then( function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    wrapper.ksList = json.body.d.list;
                }else{
                    malert('获取科室列表失败', 'top', 'defeadted');
                }
            });
        },
    }

})
laydate.render({
    elem: '.times',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        wrapper.popContent.hzkssj = value;
    }
});
laydate.render({
    elem: '.times1',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        wrapper.popContent.hzjssj = value;
    }
});
laydate.render({
    elem: '.times2',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        wrapper.popContent.zdsj = value;
    }
});
laydate.render({
    elem: '.sqrq',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        wrapper.popContent.sqrq = value;
    }
});
$('body').click(function () {
    $(".selectGroup").hide();
});
