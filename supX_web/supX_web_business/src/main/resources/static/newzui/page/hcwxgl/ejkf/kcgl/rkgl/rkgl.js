var qxksbm = '';
//改变vue异步请求传输的格式
//弹出框保存路径全局变量
var saves = null;
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data: {
		cxShow: false,
        ifClick:true,
        isShowpopL: false,
        isTabelShow: false,
        csqx: null,
        ylbm: 'N040030020022001',
        keyWord: '',
        zdyxm: '',
        zdrq: getTodayDateTime(), //获取制单日期
        jyinput: false, //禁用输入
        ckdContent: {},
        yfbm: '',
        bzms: '',
        YFList: [],
        title: '',
        totle: '',
        isUpdate: 0,//修改标志
        search: '',//模糊查询条件
        //打印数据
        printData: {},
        isShowkd: true,
        isShow: false,
        rkdList: [], //入库单集合
        jsonList: [],
        rkd: {}, //入库单对象
        TjShow: true,
        ShShow: false,
        mxShShow: true,
        zfShow: true,
        dyShow: false, //打印
        param: {
            zt:'9',
            page: 1,
            rows: 20,
            sort: "",
            order: "asc",
            parm: "",
            beginrq: null,
            endrq: null
        },
        isCheck: null,
        rkdDetail: [], //入库明细集合
        dateBegin: null, //getTodayDateBegin(),
        dateEnd: null, //getTodayDateEnd(),
        zfIfHide: true, //作废按钮是否显示
        num: 0,
        json: {
            jjzj: 0.0,
            ljzj: 0.0,
        },
        zhuangtai: {
            "0": "待审核",
            "1": "已审核",
            "2": "作废",
        },
        cansh: false,
        modifyIndex: '',
        rkdh: null,
        totlePage: 0,
		cxshowAll : false,
		totalypjj:0,
		totalyplj:0,
    },
    mounted: function () {
        this.getYFData();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            type: 'datetime',
            value: this.param.beginrq,
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            type: 'datetime',
            value: this.param.endrq,
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
    },
    updated: function () {
        changeWin();
    },
    computed:{
        money:function () {
            var reducers = {
                totalInEuros: function(state, item) {
                    return state.jjzj += item.ypjj * parseFloat(item.rksl);
                },
                totalInYen: function(state, item) {
                    return state.ljzj += item.yplj * parseFloat(item.rksl);
                }
            };
            var manageReducers = function(reducers){
                return function(state, item){
                    return Object.keys(reducers).reduce(function(nextState, key){
                        reducers[key](state, item);
                        return state;
                    },{})
                }
            }
            var bigTotalPriceReducer = manageReducers(reducers);
             this.jsonList.reduce(bigTotalPriceReducer, this.json={
                jjzj:0,
                ljzj:0,
            });
        }
    },
    methods: {
		getCxsl: function (rksl, cxsl, ycxsl, index) {
		    if (rksl - ycxsl < parseFloat(cxsl)) {
		        malert('冲销数量不得大于入库数量', 'top', 'defeadted');
		        this.jsonList[index]['cxsl'] = ''
		    } else {
		        return true
		    }
		},
		isCx:function (){
			var item = this.rkd;
		    return JSON.stringify(item) != '{}' ? item.totalypjj ? item.totalypjj>0 ? true:false:true : true;
		},
		isShFun:function (){
			var item = this.rkd;
		    return JSON.stringify(item) != '{}' && item.shzfbz == 0 ? true:false;
		},
		cxClick:function (){
		    this.cxShow=!this.cxShow;
		    this.ShShow=!this.ShShow;
		    this.dyShow=!this.dyShow;
		},
        loadNum: function () {
            this.num = wrapper.num;
        },
        resultChangeFun:function (val){
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.$forceUpdate();
            this.getData();
        },
        resultChange:function (val){
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.$forceUpdate();
        },
        //进入页面加载单据列表信息
        getData: function () {
            //清空进价和零件总计
            if (this.param.zt != '9') {
                Vue.set(this.param, 'shzfbz', this.param.zt);
            } else {
                Vue.set(this.param, 'shzfbz', null);
            }
            this.json.jjzj = 0;
            this.json.ljzj = 0;
            //this.param.rklx = '02'; //入库类型 01库房入二级库房 02 二级库房入库 03盘盈入库 04调拨入库
            common.openloading('.zui-table-view');
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=allrkd&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == 0) {
                        common.closeLoading()
                        wrapper.rkdList = data.d.list;
                        wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
						if(data.d.count){
							wrapper.totalyplj=data.d.count.TOTALYPLJ
							wrapper.totalypjj=data.d.count.TOTALYPJJ
						}
                    } else {
                        common.closeLoading()
                        malert(data.c, 'right', 'defeadted');
                    }
                });
        },

        //入库审核
        passData: function () {
            if (!wrapper.ifClick) return;
            wrapper.ifClick = false;
            if (wrapper.yfbm == null || wrapper.kfbm == "") {
                malert("请选择二级库房!", 'right', 'defeadted');
                return;
            }
            var json = {
                "rkdh": this.rkdList[this.isCheck]['rkdh'],
            };
            if (json.rkdh == null) {
                malert("请选择单据!", 'right', 'defeadted');
                return;
            }
			
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=shrkd&yfbm=' + wrapper.yfbm, JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.ifClick = true;
                        //打印数据
                        wrapper.cancel();
                        wrapper.getData();
                        wrapper.print();
                    } else {
                        wrapper.ifClick = true;
                        wrapper.getData();
                        malert(data.body.c, 'right', 'defeadted');
                    }
                });
        },
        //打印
        printDJ: function () {
            //是否有打印权限
            if (!wap.csqxContent) {
                return;
            }
            if (!(wap.csqxContent.cs00200100104 == '1')) {
                this.printData = {};
                console.log(this.printData);
                return;
            }
            var json = {
                rkdh: this.rkdList[this.isCheck]['rkdh'],
                yfbm: this.yfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=print&parm=' + JSON.stringify(json),
                function (data) {
                    wrapper.printData = data.d;
                    //小数部分和日期格式调整
                    var djmx = data.d.djmx;
                    for (var i = 0; i < djmx.length; i++) {
                        //小数部分和日期格式调整
                        var yxqz = djmx[i].yxqz;
                        wrapper.printData.djmx[i]['yxqz'] = wrapper.fDate(yxqz, 'date');
                        djmx[i].ypjjje = wrapper.fDec(djmx[i].ypjjje, 2);
                        djmx[i].ypljje = wrapper.fDec(djmx[i].ypljje, 2);
                    }
                    console.log(wrapper.printData)
                });
        },
        print: function () {

            // //帆软打印
            if(!window.top.J_tabLeft.obj.FRorWindow) {
                let drkdh='';
                if(this.rkdList[this.isCheck].iscx =='1'){
                	drkdh = this.rkdList[this.isCheck]['cxdh'];
                }else{
                	drkdh = this.rkdList[this.isCheck]['rkdh'];
                }
                var rkdh = {
                    'rkdh': drkdh
                };
                var yfbm = this.rkdList[this.isCheck]['yfbm'];
                var rk_dh = this.rkdList[this.isCheck]['rkdh'];

                $.getJSON("/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=updateDycs&parm=" + JSON.stringify(rkdh), function (json) {
                    if (json.a == 0) {
                        console.log("打印次数更新成功！");
                        // 开始打印
                        var frpath = "";
                        if (window.top.J_tabLeft.obj.frprintver == "3") {
                            frpath = "%2F";
                        } else {
                            frpath = "/";
                        }

                        // var reportlets = "[{reportlet: 'fpdy" + frpath + "ejkf" + frpath + "yfgl_rkd.cpt',yljgbm:'" + jgbm + "',yfbm:'" + wrapper.printData.dj.yfbm + "',ckdh:'" + wrapper.printData.dj.rkdh + "'}]";
                        var reportlets = "[{reportlet: 'fpdy" + frpath + "yfgl" + frpath + "yfgl_rkd.cpt',yljgbm:'" + jgbm + "',yfbm:'" + wrapper.yfbm + "',ckdh:'" + drkdh + "'}]";
                        console.log(reportlets);
                       FrPrint(reportlets, null)
                       return false;

                    } else {
                        malert(json.c, 'right', 'defeadted');
                    }
                });
            }else {
                    // 查询打印模板
            var json = {
                repname: '二级库房入库单'
            };
            var rows = wrapper.printData['dj']['rows'];
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 根据每页行数循环打印
                for (var i = 0; i < Math.ceil(wrapper.printData['djmx'].length / wrapper.printData['dj']['rows']); i++) {
                    // 清除打印区域
                    wrapper.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    wrapper.drawList = JSON.parse(json.d[0]['canvas']);
                    wrapper.creatCanvas();//这里common。js报错
                    wrapper.reDraw();
                    // 为打印前生成数据
                    var list = [];
                    var pageYpjjje = 0;
                    var pageYpljje = 0;
                    for (var j = 0; j < rows; j++) {
                        var row = j + (i * rows);
                        if (wrapper.printData['djmx'][row] == null) break;
                        list.push(wrapper.printData['djmx'][row]);
                        var jjjeTemp = wrapper.printData['djmx'][row]['ypjjje'];
                        var ljjeTemp = wrapper.printData['djmx'][row]['ypljje'];

                        pageYpjjje += parseFloat(jjjeTemp);
                        pageYpljje += parseFloat(ljjeTemp);
                    }
                    var jjjehz = wrapper.printData['dj']['jjjehz'];
                    var ljjehz = wrapper.printData['dj']['ljjehz'];
                    wrapper.printData['dj']['jjjehz'] = wrapper.fDec(jjjehz, 2);
                    wrapper.printData['dj']['ljjehz'] = wrapper.fDec(ljjehz, 2);
                    wrapper.printData['dj']['ypjjjePage'] = wrapper.fDec(pageYpjjje, 2);
                    wrapper.printData['dj']['ypljjePage'] = wrapper.fDec(pageYpljje, 2);
                    wrapper.printData['dj']['total'] = Math.ceil(wrapper.printData['djmx'].length / rows);
                    wrapper.printData['dj']['page'] = i + 1 + "     /";
                    wrapper.printContent(wrapper.printData['dj']);
                    wrapper.printTrend(list);
                    window.print()
                }
            });
            }
        },

        //作废2018/07/07作废二次弹窗提示
        invalidData: function (num) {
            if (num != null && num != undefined) {
                this.isCheck = num;
            }
            if (!wrapper.yfbm ) {
                malert("请选择二级库房!", 'right', 'defeadted');
                return;
            }
            var json = {
                rkdh: wrapper.rkdList[wrapper.isCheck]['rkdh']
            };
            if (common.openConfirm("确认作废该条信息吗？", function () {

                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=zfrkd&yfbm=' + wrapper.yfbm, JSON.stringify(json)).then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.getData();
                        malert("作废成功！", 'right', 'success');
                        wrapper.cancel();
                    } else {
                        malert(data.body.c, 'right', 'defeadted');
                    }
                });
            })) {
                return false;
            }
        },
        //编辑
        editIndex: function (index) {
            wrapper.rkdh = this.rkdList[index]['rkdh'];
            wrapper.isUpdate = 1;//编辑
            this.isCheck = index;
            wrapper.isShow = true;
            wrapper.isShowkd = false;
            wrapper.isShowpopL = true;//允许添加材料
            wrapper.isShow = true;
            wrapper.isShowkd = false;
            wrapper.isShowkd = false;
            
            wrapper.ShShow = false;
            //this.dg['rkdh'] = this.rkdList[index]['rkdh'];
            wrapper.zfShow = false;
            wrapper.mxShShow = true;
            wrapper.jyinput = false;
            //2018/07/12
            wrapper.zdyxm = this.rkdList[index].zdyxm;
            wrapper.zdrq = wrapper.fDate(this.rkdList[index].zdrq, 'date');
            wrapper.yfbm = this.rkdList[index].yfbm;
            wrapper.bzms = this.rkdList[index].bzms;
			this.rkd = this.rkdList[index];
			this.ShShow=false;
			this.TjShow = true;
			if(this.rkdList[index].shzfbz==0){
				this.cxshowAll = false;
				this.mxShShow=true;
				this.dyShow = false;
				
				this.cxShow = false;
			}else{
				this.cxshowAll = true;
				
				wrapper.jyinput=true;
				this.mxShShow=false;
				this.zfShow = false;
				wrapper.zfShow = false;
			    if(this.rkdList[index].shzfbz == 1){
			        wrapper.dyShow=true;
					wrapper.isShowpopL=false;
					if(this.rkdList[index].totalypjj< 0){
						this.TjShow   = false;
					}
			    }
			}
			
            let rkdh='';
            if(this.rkdList[index].iscx =='1'){
            	rkdh = this.rkdList[index]['cxdh'];
            }else{
            	rkdh = this.rkdList[index]['rkdh'];
            }
            // 测试打印
            var parm = {
                "rkdh": rkdh,
            	uptimestamp:this.rkd.uptimestamp
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=rkdmxcx' +
                '&parm=' + JSON.stringify(parm),
                function (data) {
                    wrapper.jsonList = data.d.list;
                });
        },
        //选中单据信息加载出相对应的单据内容明细
        showDetail: function (index) {
            this.isCheck = index;
			wrapper.ShShow = true;
            wrapper.isShow = true;
            wrapper.isShowkd = false;
            wrapper.isShowpopL = false;
            //$('#bzms').attr('disabled',true);
            this.TjShow = false;
            wrapper.zfShow = false;
            wrapper.dyShow=false;
            //根据状态判断
            wrapper.jyinput = true;
			this.rkd = this.rkdList[index];
            wrapper.zdyxm = this.rkdList[index].zdrxm;
            wrapper.zdrq = wrapper.fDate(this.rkdList[index].zdrq, 'date');
            wrapper.yfbm = this.rkdList[index].yfbm;
            wrapper.bzms = this.rkdList[index].bzms;
            this.param.rkdh = this.rkdList[index].rkdh;
            // 测试打印
            this.printDJ();
			this.mxShShow = false;
			
			let rkdh='';
			if(this.rkdList[index].iscx =='1'){
				rkdh = this.rkdList[index]['cxdh'];
			}else{
				rkdh = this.rkdList[index]['rkdh'];
			}
			// 测试打印
			var parm = {
			    "rkdh": rkdh,
				uptimestamp:this.rkd.uptimestamp
			};
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=rkdmxcx' +
                '&parm=' + JSON.stringify(parm),
                function (data) {
                    wrapper.jsonList = data.d.list;
                });
        },


        //提交所有材料
        submitAll: function () {
            if (this.isSubmited) {
                malert("数据提交中，请稍后！", 'right', 'defeadted');
                return;
            }
            if (this.jsonList.length <= 0) {
                malert("请录入入库材料明细", 'right', 'defeadted');
                return false;
            }
            //锁定提交功能
            this.isSubmited = true;
            var rkd = {
                yfbm: this.param.yfbm,
                bzms: this.bzms
            };
            if (this.rkdh != null) {
                Vue.set(rkd, 'rkdh', this.rkdh);
            }
			var jsonList = [];
			if (this.cxShow) {
			    for (var i = 0; i < this.jsonList.length; i++) {
					if (this.jsonList[i].uptimestamp) {
					    this.jsonList[i].uptimestamp='';
					}
			        if (this.jsonList[i].cxsl) {
			            var item = JSON.parse(JSON.stringify(this.jsonList[i]));
			            item.mxxh = ++this.jsonList[this.jsonList.length - 1]['mxxh']
			            item.cxsl = '-' + item.cxsl;
			            jsonList.push(item);
			        }
			    }
			} else {
				for (var i = 0; i < this.jsonList.length; i++) {
				    if (this.jsonList[i].uptimestamp) {
				        this.jsonList[i].uptimestamp='';
				    }
				}
			    jsonList = this.jsonList
			}
            var json = {
                "list": {
                    "rkd": rkd,
                    "rkdmx": jsonList
                }
            };
			var url = !this.cxShow ? 'modify' : 'rkcx'
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types='+url+'&yfbm=' + wrapper.yfbm, JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == 0) {
                        malert("数据更新成功", 'right', 'success');
                        
						
						
						this.rkd = {};
						wap.rkd = {};
						wap.shzfbz = '';
						wrapper.jsonList = [];
						wrapper.cancel();
						
                        wrapper.getData();
                    } else {
                        malert("保存失败:" + data.body.c, 'right', 'defeadted');
                    }
                    //解锁提交功能
                    wrapper.isSubmited = false;
                }, function (error) {
                    console.log(error);
                    //解锁提交功能
                    wrapper.isSubmited = false;
                });
        },
        //双击修改
        edit: function (num) {
            wrapper.modifyIndex = num;
            wrapper.isUpdate = 1;
            wap.title = "编辑材料";
            if (num == null) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if (num == null) {
                    malert("请选中你要修改的数据", 'right', 'success');
                    return false;
                }
            }
            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            wap.popContents.ghdw = wap.popContent.ghdw;
            wap.popContents.ghdwmc = wap.popContent.ghdwmc;
            wap.inputYpmc = wap.popContent.ypmc;
            wap.open();
        },
        //删除2018/07/06删除弹窗提示
        scmx: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.jsonList.splice(index, 1);
            })) {
                return false;
            }
            // this.jsonList.splice(index,1);
        },

        //取消
        cancel: function () {
            wrapper.isShowpopL = false;
            wrapper.isShow = false;
            wrapper.isShowkd = true;
			wrapper.cxshowAll = false;
			wrapper.dyShow = false;
			wrapper.cxShow = false;
			wrapper.zfShow = false;
			wrapper.ShShow = false;
			
        },
        kd: function (index) {
            this.num = index;
            wrapper.loadNum();
            setTimeout(function () {
                wap.$refs.autofocus.$refs.inputFu.focus();
                wap.$refs.autofocus.setLiShow(wap.$refs.autofocus.$refs.inputFu)
            }, 1000);
            switch (wrapper.num) {
                case 0:
                    wrapper.rkdh = null;
                    wrapper.isUpdate = 0;
                    wrapper.jsonList = [];
                    this.isShowkd = false;
                    this.isShow = true;
                    this.isShowpopL = true;
                    wrapper.TjShow = true;
                    wrapper.ShShow = false;
                    $('#bzms').attr('disabled', false);
                    wrapper.zfShow = false;
                    wrapper.mxShShow = true;
                    wrapper.jyinput = false;
                    var reg = /^[\'\"]+|[\'\"]+$/g;
                    wrapper.zdyxm = sessionStorage.getItem("userName"+userId).replace(reg, '');
                    break;
                case 1:
                    wap.open();
                    wrapper.isUpdate = 0;
                    wap.title = '添加材料';
                    wap.popContent = {};
                    wap.inputYpmc = '';
                    break;
            }

        },

        getYFData: function () {
            //初始化页面记载库房
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm={"ylbm": "N040100021001"}',
                function (data) {
                    if (data.a == 0) {
                        wrapper.YFList = data.d.list;
                        wrapper.param.yfbm = wrapper.YFList[0].yfbm;//默认二级库房
                        qxksbm = wrapper.YFList[0].ksbm;
                        wap.getCsqx();
                    } else {
                        malert("二级库房获取失败", 'right', 'defeadted');
                    }
                });
        },
    }
});


var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        isShowpopL: false,
        iShow: false,
        isTabelShow: false,
        flag: false,
        jsShow: false,
        centent: '',
        yfbm: null,
        inputYpmc: '', //材料名称
        isFold: false,
        ksList: [],
        hszList: [],
        ywckList: [],
        title: '',
        num: 0,
        csContent: {},
        YFList: [],
        jsonList: [],
        popContents: {},
        cgryList: [],//采购员
        csqxContent: {}, //参数权限对象
        csqx: null,
        ckdContent: {}, //出库单对象
        KSList: [], //领用科室
        zbfsList: [],//招标方式
        ghdwList: [],//供货单位
        KFList: [], //库房
        ryList: [], //领用人
        glryList: [], //过滤领用人
        ypcdList: [], //材料产地
        rkd: {}, //入库单对象
        popContent: {

        },
        dg: {
            page: 1,
            rows: 5,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '材料编号': 'ypbm',
            '材料名称': 'ypmc',
            '规格': 'ypgg',
            '分装比例': 'fzbl',
            '进价': 'yfypjj',
            '零价': 'yfyplj',
            '一级库房进价': 'ypjj',
            '一级库房零价': 'yplj',
            '库房单位': 'kfdwmc',
            '二级库房单位': 'yfdwmc',
            '二级库房种类': 'ypzlmc',
            '材料剂型': 'jxmc'
        },

    },
    watch: {
        'popContent.scrq': function (n, o) {
            if (n == null || n =='') {
                return false
            }
            var sdate = new Date(n);
            Y = sdate.getFullYear(),
                m = sdate.getMonth() + 1,
                d = sdate.getDate()
            if (m < 10) {
                m = '0' + m;
            }
            if (d < 10) {
                d = '0' + d;
            }
            if (!this.popContent.yxqz) {
                return this.popContent.yxqz = Y + 2 + '-' + m + '-' + d;
            }
        }
    },
    mounted: function () {
        this.getJzData();
        this.getcdData();
        Mask.newMask(this.MaskOptions('_scrq','YYYY-DD-MM'));
        Mask.newMask(this.MaskOptions('_yxqz','YYYY-DD-MM'));
    },
    methods: {
		
		nextFocus1: function (event, num, tpye, SpecifiedIndex) {
		    var _input = $("input,textarea").not(":disabled,input[type=checkbox],[data-skip]");
		    if (event.keyCode == 13 || event.type == 'text') {
		        for (var i = 0; i < _input.length; i++) {
		            if (_input.eq(i)[0] == (event.srcElement || event)) {
		                if (num) {
		                    _input.eq(i + num).focus();
		                    return false
		                } else {
		                    setTimeout(function () {
								$("#_yxqz")[0].focus();
		                    },100)
		                }
		                break;
		            }
		        }
		        return false
		    } else if (event.keyCode == 37) {
		        for (var i = 0; i < _input.length; i++) {
		            if (_input.eq(i)[0] == (event.srcElement || event)) {
		                if (num) {
		                    _input.eq(i - num).focus();
		                    console.log(i - num);
		                } else {
		                    _input.eq(i - 1).focus();
		                }
		                break;
		            }
		        }
		        return false
		    } else if (event.keyCode == 39) {
		        for (var i = 0; i < _input.length; i++) {
		            if (_input.eq(i)[0] == (event.srcElement || event)) {
		                if (num) {
		                    _input.eq(i + num).focus();
		                    console.log(i + num);
		                } else {
		                    _input.eq(i + 1).focus();
		                }
		                break;
		            }
		        }
		        return false
		    }
		},
        closes: function () {
            $(".side-form").removeClass('side-form-bg');
            $(".side-form").addClass('ng-hide');

        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg');
            $(".side-form").removeClass('ng-hide');
        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            if (qxksbm == undefined && wrapper.rkd.kfbm == undefined) {
                return;
            }

            var parm = {
                "ylbm": 'N040030020022001',
                "ksbm": qxksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N04003002002200101":  //入库修改进价1-允许，0-不允许
                                    if (csjson.csz) {
                                        wap.csqxContent.cs00200100101 = parseInt(csjson.csz);
                                    }
                                    break;
                                case "N04003002002200102": //入库修改零价1-允许，0-不允许
                                    if (csjson.csz) {
                                        wap.csqxContent.cs00200100102 = parseInt(csjson.csz);
                                    }
                                    break;
                                case "N04003002002200103": //
                                    if (csjson.csz) {
                                        wap.csqxContent.cs00200100103 = parseInt(csjson.csz);
                                    }
                                    break;
                                case "N04003002002200104"://二级库房入库是否打印单据
                                    if (csjson.csz) {
                                        wap.csqxContent.cs00200100104 = parseInt(csjson.csz);
                                    }
                                    break;
                                case "N04003002002200105": //二级库房入库开单权限
                                    if (csjson.csz) {
                                        wap.csqxContent.cs00200100105 = parseInt(csjson.csz);
                                        wrapper.isShowkd = parseInt(csjson.csz) == 1 ? true : false;
                                    } else {
                                        wrapper.isShowkd = false;//入库开单
                                    }
                                    break;
                                case "N04003002002200106": //二级库房入库审核权限
                                    if (csjson.csz) {
                                        wap.csqxContent.cs00200100106 = parseInt(csjson.csz);
                                        wrapper.cansh = parseInt(csjson.csz) == 1 ? true : false;//审核权限
                                    } else {
                                        wrapper.cansh = false;
                                    }
                                    break;
                            }
                        }
                    }
                    wrapper.getData();
                } else {
                    malert('参数权限获取失败' + json.c, 'right', 'defeadted')
                }
            });
        },
        //
        getJzData: function () {
            //初始化页面记载供货单位
            this.dg.rows = 20000;
            this.dg.sort = 'dwbm';
            this.dg.tybz = '0';
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(this.dg),
                function (json) {
                    if (json.a == 0) {
                        wap.ghdwList = Object.freeze(json.d.list);
                    } else {
                        malert("供货单位获取失败", 'right', 'defeadted');
                    }
                });
            //初始化页面记载采购人员
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm", function (json) {
                if (json.a == 0) {
                    wap.cgryList = Object.freeze(json.d.list);
                } else {
                    malert("采购人员获取失败", 'right', 'defeadted');
                }
            });

            //初始化页面加载招标方式
            this.dg.rows = 20000;
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhZbfs&types=query&dg=" + JSON.stringify(this.param),
                function (data) {
                    if (data.a == 0) {
                        wap.zbfsList = Object.freeze(data.d.list);
                    } else {
                        malert("招标方式获取失败!", 'right', 'defeadted');
                    }

                });
        },
        getcdData:function(){
            //初始化页面加载产地编码
            var obj={
                rows:20000,
                sort:'cdbm',
                tybz:'0',
                page:1,
            }
            this.$nextTick(function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=query&dg=" + JSON.stringify(obj),
                    function (data) {
                        if (data.a == 0) {
                            wap.ypcdList = Object.freeze(data.d.list);
                            wap.$forceUpdate()
                            setTimeout(function () {
                                wap.$refs.cdbm.setLiShow(wap.$refs.cdbm.$refs.inputFu)
                            },100)
                        } else {
                            malert("材料产地获取失败!", 'right', 'defeadted');
                        }
                    });
            })
        },

        //材料名称下拉table检索数据
        changeDown: function (event, type, searchCon) {
             this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (event.keyCode == 13) {
                this.inputYpmc = this.popContent.ypmc;
                this.popContent.ghdw = this.popContents.ghdw;
                this.popContent.ghdwmc = this.popContents.ghdwmc;
                this.nextFocus(event);
                //此处材料价格为库房单位价格， 需要显示二级库房单位价格，所以处以分装比例
                this.popContent.yklj = this.popContent.yplj;
                this.popContent.ykjj = this.popContent.ypjj;
                if (this.popContent.yfypjj == null || this.popContent.yfypjj == 0){
                  this.popContent.ypjj = wrapper.fDec(this.popContent.ypjj/this.popContent.fzbl, 6);
                }else{
                    this.popContent.ypjj = this.popContent.yfypjj;
                }
                if (this.popContent.yfyplj == null || this.popContent.yfyplj == 0){
                    this.popContent.yplj = wrapper.fDec(this.popContent.yplj/this.popContent.fzbl, 6);
                }else{
                    this.popContent.yplj = this.popContent.yfyplj;
                }
                $(".selectGroup").hide();
                this.selSearch=-1;
            }
        },
        //当输入值后才触发
        change: function (add, val) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            this.popContent['ypmc'] = val;
            this.dg.parm = val;
            this.dg.sort = 'cdbm';
			var bean = {
			    'kfbm': '03'
			};
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(wap.dg)+'&parm='+ JSON.stringify(bean), function (data) {
                    if (data.a == 0) {
                        if (add) {
                            wap.searchCon = wap.searchCon.concat(data.d.list)
                        } else {
                            wap.searchCon = data.d.list;
                        }
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                        if (data.d.list.length != 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show()
                        } else {
                            $(".selectGroup").hide();
                        }
                    } else {
                        malert("材料检索失败!",'right','defeadted');
                    }

                });
        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                this.dg.page++;
                this.change(true, this.popContent['ypmc'])
                return false;
            }

            this.popContent = item;
            this.popContent.ghdw = this.popContents.ghdw;
            this.popContent.ghdwmc = this.popContents.ghdwmc;
            //此处材料价格为库房单位价格， 需要显示二级库房单位价格，所以处以分装比例
            //this.popContent.ypjj = wrapper.fDec(this.popContent.ypjj/this.popContent.fzbl, 6);
            //this.popContent.yplj = wrapper.fDec(this.popContent.yplj/this.popContent.fzbl, 6);
            this.popContent.yklj = this.popContent.yplj;
            this.popContent.ykjj = this.popContent.ypjj;
            if (this.popContent.yfypjj == null || this.popContent.yfypjj == 0){
                this.popContent.ypjj = wrapper.fDec(this.popContent.ypjj/this.popContent.fzbl, 6);
              }else{
                  this.popContent.ypjj = this.popContent.yfypjj;
              }
              if (this.popContent.yfyplj == null || this.popContent.yfyplj == 0){
                  this.popContent.yplj = wrapper.fDec(this.popContent.yplj/this.popContent.fzbl, 6);
              }else{
                  this.popContent.yplj = this.popContent.yfyplj;
              }
            this.inputYpmc = this.popContent.ypmc;
            $(".selectGroup").hide();
            this.selSearch=-1;
            $('#rksl').focus()
        },
		validity :function(value){
			var nowDate = new Date();
			var valueDate = new Date(value);
			if(nowDate.getTime()>valueDate.getTime()){
				malert("有效期小于当前时间", 'top', 'defeadted');
				return false;
			}else {

				var iday = parseInt(valueDate - nowDate) / 1000 / 60 / 60 / 24;
				if(iday<90){
					malert("有效期小于90天", 'top', 'defeadted');
				}
				return true;
			}

		},
        //添加入库信息
        addData: function () {
            if (!wrapper.param.yfbm) {
                malert("二级库房不能为空!", 'right', 'defeadted');
                return;
            }
            if (!this.popContent['ypmc'] ) {
                malert("材料不能为空!", 'right', 'defeadted');
                return;
            }
            if (!this.popContent['rksl']  || this.popContent['rksl'] <= 0) {
                malert("入库数量不能为空!", 'right', 'defeadted');
                return;
            }
            var scph = this.popContent.scph;
            if (scph = undefined || scph == null || scph == "") {
                malert("材料批号不能为空!", 'right', 'defeadted');
                return;
            }
            var scrq = this.popContent.scrq;
            if (scrq = undefined || scrq == null) {
                malert("生产日期不能为空!", 'right', 'defeadted');
                return;
            }
            var yxqz = this.popContent.yxqz;
            if (yxqz = undefined || yxqz == null) {
                malert("有效期不能为空!", 'right', 'defeadted');
                return;
            }
			if(!this.validity(this.popContent.yxqz)){
				return false;
			}

            if (wrapper.isUpdate == 0) {
                //添加
                //相同材料不能重复入库
                var ypbm = wap.popContent.ypbm;
                for (var i = 0; i < wrapper.jsonList.length; i++) {
                    if (wrapper.jsonList[i].ypbm == ypbm && wrapper.jsonList[i].scph == scph) {
                        malert("材料【" + this.popContent.ypmc + "】已存在,请修改已有数据!", 'right', 'defeadted');
                        return;
                    }
                }
                wrapper.jsonList.push(this.popContent);
                wap.popContent = {};
                wap.inputYpmc = "";
                $("#ypmc").focus();
            }
            /*if(wap.title=='编辑材料'){
                wap.closes();
            }else{

            }*/
            if (wrapper.isUpdate == 1) {
                //修改
                wrapper.$set(wrapper.jsonList, wrapper.modifyIndex, wap.popContent);
                malert("修改成功！", 'right', 'success');
                wap.closes();
            }

        },

    }
});

laydate.render({
    elem: '.times1'
    , theme: '#1ab394',
    done: function (value, data) {
        wap.popContent.scrq = value
    }
});
laydate.render({
    elem: '.times2'
    , theme: '#1ab394',
    done: function (value, data) {
        wap.popContent.yxqz = value
    }
});

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

});




