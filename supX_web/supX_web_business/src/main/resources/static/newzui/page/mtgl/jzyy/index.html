<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>门特管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="index.css">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper " id="loadingPage">
    <div id="mtgl_index" v-cloak class="printHide">
        <div class="panel">
            <div class="tong-top flex-container flex-align-c">
<!--                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addDj">登记</button>-->
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">打印</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">门特结算</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="printFun">取消结算</button>
            </div>
        </div>
            <div class="flex-container background-f flex-align-c padd-l-10 padd-t-10 padd-b-10">
                <div class="flex-container flex-align-c padd-r-20">
                    <span class="ft-14 padd-r-5">状态</span>
                        <select-input class="wh120" @change-data="commonResultChange" :child="mtzt_tran"
                                      :index="param.zt" :val="param.zt" :search="true"
                                      :name="'param.zt'">
                        </select-input>
                </div>
                <div class="flex-container flex-align-c padd-r-20">
                    <span class="ft-14 padd-r-5">科室</span>
                        <select-input :search="true" class="wh120" @change-data="commonResultChange" :not_empty="false"
                                      :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="param.ksbm"
                                      :name="'param.ksbm'">
                        </select-input>
                </div>
                <div class="flex-container flex-align-c padd-r-20">
                    <span class="ft-14 padd-r-5 ">时间段</span>
                    <div class=" flex-container flex-align-c">
                        <input autocomplete="off" @click="showTime('timeVal','beginTime')" class="zui-input todate wh180 " placeholder="开始时间" id="timeVal" v-model="param.beginTime"/>
                        <span class="padd-l-5 padd-r-5">~</span>
                        <input autocomplete="off"  @click="showTime('timeVal1','endTime')" class="zui-input todate wh180 " placeholder="结束时间" v-model="param.endTime" id="timeVal1" />
                    </div>
                </div>
                <div class="flex-container flex-align-c">
                    <span class="ft-14 padd-r-5">检索</span>
                        <input autocomplete="off" class="zui-input wh180" placeholder="请输入姓名/手机号码/身份证号/住院号" type="text" v-model="param.parm"  @keydown.enter="goToPage(1)"/>
                </div>
            </div>

        <!--循环列表begin-->
        <div class="zui-table-view" id="brRyList">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>申请流水号</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>就诊编号</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>变更流水号</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>个人编码</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>姓名</span></div>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>性别</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>支付类别</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>医疗人员类别</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>经办机构</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>治疗开始时间</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>治疗结束时间</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>医生</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>登记时间</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作员</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>登记状态</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table v-if="jsonList.length" class="zui-table table-width50">
                    <tbody>
                    <tr  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                         @mouseenter="switchIndex('hoverIndex',true,$index)"
                         @mouseleave="switchIndex()" v-for="(item, $index) in jsonList"
                         :tabindex="$index"
                         @click="switchIndex('activeIndex',true,$index)"
                         class="tableTr2">
                        <td class="cell-m"><div class="zui-table-cell cell-m" ><!--序号--> {{$index+1}}</div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="item.ykc112"><!--申请流水号--></div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="item.akc190"><!--申请流水号--></div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="item.oldYkc112"><!--变更流水号-->{{item.ghxh}}</div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="item.aac001"><!--个人编码--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-l" v-text="item.ghxh"><!--挂号序号--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="item.brxm"><!--姓名--></div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="brxb_tran[item.brxb]"><!--性别--></div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="mtgl_zflb_tran[item.aka130]"><!--支付类别--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s"  v-text="akc021_tran[item.akc021]"><!--医疗人员类别--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="yab003_tran[item.yab003]"><!--经办机构--></div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="fDate(item.yae170,'datetime')"><!--治疗开始时间--></div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="fDate(item.yae171,'datetime')"><!--治疗结束时间--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="item.jzysxm"><!--医生--></div></td>
                        <td class="cell-l"><div class="zui-table-cell cell-l" v-text="fDate(item.czrq,'datetime')"><!--登记时间--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" v-text="item.czyxm"><!--操作员--></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s" :class="mtzt_tran[item.zt]" v-text="mtzt_tran[item.zt]"></div></td>
                        <td class="cell-l"><!--操作-->
                            <div class="zui-table-cell flex-center cell-l">
                                <span @click="edit(item,0)"  class="" v-if="item.zt =='9'">制作方案</span>
                                <span class="" v-if="item.zt === '0'" @click="edit(item,0,true,false,'修改方案')">修改方案</span>
                                <span class="" v-if="item.zt === '0'" @click="edit(item,1,true,true,'待审核')">待审核</span>
                                <span class="" v-if="item.zt === '1'" @click="edit(item,1,false,false,'变更')">变更</span>
                                <span class="" v-if="item.zt === '1' " @click="backward(item,3)">回退</span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="!jsonList.length" class="flex noData  text-center zan-border">暂无数据...</p>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                            <tr>
                                <th class="cell-s"><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                                <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            class="tableTr2">
                            <td class="cell-s"><div class="zui-table-cell cell-s" :class="mtzt_tran[item.zt]" v-text="mtzt_tran[item.zt]"></div></td>
                            <td class="cell-l"><!--操作-->
                                <div class="zui-table-cell cell-l">
                                    <span @click="edit(item,0)"  class="" v-if="item.zt =='9'">制作方案</span>
                                    <span class="" v-if="item.zt === '0' && !qxUser()" @click="edit(item,0,true,false,'修改方案')">修改方案</span>
                                    <span class="" v-if="item.zt === '0' && qxUser()" @click="edit(item,1,true,true,'待审核')">待审核</span>
                                    <span class="" v-if="item.zt === '1' " @click="edit(item,1,false,false,'变更')">变更</span>
                                    <span class="" v-if="item.zt === '1' " @click="backward(item,3)">回退</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
<!--门特登记-->
        <div class="side-form pop-80 add-or-edit-content flex-container flex-dir-c" id="ckyzContent" :class="{ 'ng-hide': !isShow }" v-cloak>
            <div class="fyxm-side-top flex-between">
                <span class="fr closex ti-close" style="float: left;" @click="quxiao"></span>
                <span>&emsp;门特登记</span>
                <span class="fr closex ti-close" @click="quxiao"></span>
            </div>
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">基本信息</div>
                </div>
                <div class="tab-card-body  padd-t-10 flex-container flex-align-c flex-wrap-w">
                    <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                        <span class="padd-r-5 ft-14 whiteSpace">个人编码</span>
                        <input autocomplete="off" v-model.trim="brxxContent.aac001" @keyup.13="initBz($event)" placeholder="社保号" :disabled="brxxContent.aac001? true:false"
                              class="zui-input wh120" />
                    </div>
                    <div class="flex-container flex-align-c padd-r-10 padd-b-10">
                        <span class="padd-r-5 ft-14 whiteSpace">姓名</span>
                        <div class="position">
                            <input class="zui-input wh80"  @mousewheel.prevent @keydown.up.prevent
                                   @keydown.down.prevent  v-model="brxxContent.brxm" disabled
                                   data-notEmpty="true" autocomplete="off"
                                   @keydown="nextFocus($event)"/>
                        </div>
                    </div>

                    <div class="flex-container flex-align-c padd-r-10 padd-b-10">
                        <span class="padd-r-5 ft-14 whiteSpace ">登记流水号</span>
                        <input class="zui-input wh120" disabled v-model="brxxContent.ykc112"
                               @keydown="nextFocus($event)"/>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10 padd-b-10">
                        <span class="padd-r-5 ft-14 whiteSpace ">挂号日期</span>
                        <input v-model="brxxContent.ghrq" @click="showTime('timeVal4','ghrq')" class="zui-input  wh120 margin-r-10" placeholder="请选择时间" id="timeVal4" />
                    </div>
                    <div class="flex-container flex-align-c padd-r-10 padd-b-10">
                            <button class="tong-btn btn-parmary icon-font14 paddr-r5" @click="getJbxx">读卡</button>
                            <!--<button class="tong-btn btn-parmary icon-font14 paddr-r5" @click="query">修改</button>-->
                            <button class="tong-btn btn-parmary icon-font14 paddr-r5" @click="copyFun">复制</button>
                            <button class="tong-btn btn-parmary icon-font14 paddr-r5" @click="updatedGhrq">更新挂号日期</button>
                            <div class="flex-container flex-align-c padd-l-10" >
                                <span class="ft-14 padd-r-5 whiteSpace">治疗时间</span>
                                <div class="  flex-container flex-align-c">
                                    <input v-model="brxxContent.yae170" @click="showTime('timeVal2','yae170')" class="zui-input  wh120 margin-r-10" placeholder="请选择治疗开始时间" id="timeVal2" />
                                    <input v-model="brxxContent.yae171" @click="showTime('timeVal3','yae171')" class="zui-input  wh120 " placeholder="请选择治疗结束时间" id="timeVal3" />
                                </div>
                            </div>
                            <div class="color-wtg margin-l-10 margin-r-10">{{day}}天</div>
                        <div class="flex-container flex-align-c padd-r-10" v-if="brxxContent.id  || brxxContent.zt">
                            <div class="ft-14 padd-r-5 whiteSpace">医院经办人</div>
                            <input class="zui-input wh80" disabled type="text" v-model="brxxContent.jbrxm"  />
                        </div>
                        <div class="flex-container flex-align-c ">
                            <div class="ft-14 padd-r-5 whiteSpace">医生</div>
                            <input class="zui-input wh80" disabled type="text" v-model="brxxContent.ysxm"  />
                        </div>
                    </div>
                   <div class="wh100MAx flex-container">
<!--                       病情诊断-->
                       <div class=" wh50MAx padd-r-20 padd-b-10">
                           <span class="padd-r-5 ft-14 whiteSpace ">病情诊断</span>
                           <textarea  class="rkgl-position wh100MAx padd-b-5 padd-r-5 padd-l-5 padd-t-5"  v-model="brxxContent.jbzd" @keydown="nextFocus($event)"></textarea>
                       </div>
                       <div  class="zui-table-view wh50MAx">
                           <div class="zui-table-header " >
                               <table class="zui-table table-width50">
                                   <thead>
                                   <tr>
                                       <th><div class="zui-table-cell cell-s"><span>病种编码</span></div></th>
                                       <th><div class="zui-table-cell cell-s"><span>病种名称</span></div></th>
                                       <th><div class="zui-table-cell cell-s"><span>方案金额</span></div></th>
                                       <th><div class="zui-table-cell cell-s"><span>药品</span></div></th>
                                       <th><div class="zui-table-cell cell-s"><span>非药品</span></div></th>
                                   </tr>
                                   </thead>
                               </table>
                           </div>
                           <div  data-no-change class="zui-table-body  zuiTableBodyHzlist flex-one flex-align-c" ref="body" v-if="isShow" @scroll="scrollTable($event)">
                               <table class="zui-table " v-if="rdbzContent.length!=0">
                                   <tbody>
                                   <tr v-for="(item, $index) in rdbzContent">
                                       <td><div class="zui-table-cell cell-s" v-text="item.yka026"></div></td>
                                       <td><div class="zui-table-cell cell-s " v-text="item.yka027"></div></td>
                                       <td><div class="zui-table-cell cell-s " v-text="item.sum" ></div></td>
                                       <td><div class="zui-table-cell cell-s"  v-text="item.ypfyje"></div></td>
                                       <td><div class="zui-table-cell cell-s" v-text="item.fypfyje"></div></td>
                                   </tr>
                                   </tbody>
                               </table>
                               <p v-if="rdbzContent.length==0" class="  noData text-center zan-border">暂无数据...</p>
                           </div>
                       </div>
                   </div>
                    <div>
                        <div class="color-wtg"> 通过个人编码，复制已审核的治疗方案</span></div>
                        1、【用法】、数字、表示周期内用药或治疗次数。<br>
                        2、【用量】、数字、单次用量、单次用量统一按照经办机构药品中。<br>
                        3、【周期】、数字、以天为单位。【申请单中】、相同的项目只允许输入一次。<br>
                        4、医保大类本地数据未及时更新。请注意药品单量使用剂量单位，总量使用住院单位<br>
                    </div>

                </div>
            </div>
            <div class="flex-container mtdjTable padd-b-5 flex-dir-c">
                        <div  class="zui-table-view  padd-l-10 padd-r-10">
                            <div class="zui-table-header " >
                                <table class="zui-table table-width50">
                                    <thead>
                                    <tr>
                                        <th><div class="zui-table-cell cell-l"><span>医院项目名称</span></div></th>
                                        <th><div class="zui-table-cell cell-l "><span>医院项目编码</span></div></th>
                                        <th><div class="zui-table-cell cell-l "><span>组合费用编码</span></div></th>
                                        <th><div class="zui-table-cell cell-l "><span>医保编码</span></div></th>
                                        <th><div class="zui-table-cell cell-s "><span>医保大类</span></div></th>
                                        <th><div class="zui-table-cell cell-s"><span>物价编码</span></div></th>
                                        <th><div class="zui-table-cell cell-s"><span>单价</span></div></th>
                                        <th><div class="zui-table-cell cell-s"><span>规格</span></div></th>
                                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>用法</span></div></th>
                                        <th><div class="zui-table-cell cell-s"><span>单量/单位</span></div></th>
                                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>周期</span></div></th>
                                        <th><div class="zui-table-cell cell-s"><span>总量/单位</span></div></th>
                                        <th><div class="zui-table-cell cell-s"><span>病种</span></div></th>
                                        <th class="cell-m" v-if="brxxContent.zt == '9' || brxxContent.zt == '0' || brxxContent.zt == '1'"><div class="zui-table-cell cell-m"><span>操作</span></div></th>

                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div  data-no-change class="zui-table-body hzlistHeight  flex-one flex-align-c" ref="body" v-if="isShow" @scroll="scrollTable($event)">
                                <table class="zui-table " v-if="zlfaList.length!=0">
                                    <tbody>
                                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                                        @mouseleave="switchIndex()"
                                        @click="switchIndex('activeIndex',true,$index)"
                                        :tabindex="$index"
                                        v-for="(item, $index) in zlfaList">
                                        <td><div class="zui-table-cell cell-l">
                                            <input :id="'zlorypmc' + $index" class="zui-input" type="text"
                                                   v-model="item.zlorypmc" @keydown="Wf_changeDown($index,$event,'zlorypmc',$event.target.value)"
                                                   @input="Wf_change(false,$index,'zlorypmc', $event.target.value)" :disabled="item.readonly"/>

                                            <search-table :message="searchCon1" :selected="selSearch1" :page="queryObj" :them="them"
                                                          :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne2">
                                            </search-table>
                                        </div></td>
                                        <td><div class="zui-table-cell cell-l" v-text="item.zlorypbm"></div></td>
                                        <td><div class="zui-table-cell cell-l" v-text="item.zyfybm"></div></td>
                                        <td><div class="zui-table-cell cell-l " v-text="item.yka094"></div></td>
                                        <td><div class="zui-table-cell cell-s " v-text="item.ybdl"></div></td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.yaa027"></div></td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.cklj"></div></td>
                                        <td><div class="zui-table-cell cell-s" v-text="item.gg"></div></td>
                                        <td class="cell-m"><div class="zui-table-cell cell-m">
<!--                                            @input="Wf_change2(false,$index,'aka073',$event.target.value)"-->
<!--                                            @keydown="Wf_changeDown2($index,$event,'aka073',item.aka073)"-->
                                            <input @input="setYYzl($index)" class="zui-input" :id="'yyffmc_' + $index" type="text" v-model.trim.number="item.aka073"
                                                   data-notEmpty="false" @keydown.13="nextSelect($event)"
                                                   :disabled="item.readonly" />
<!--                                            <search-table :message="searchCon2" :selected="selSearch2" :them="them2"-->
<!--                                                           :them_tran="them_tran2" :page="pageStr" @click-one="checkedOneOut" @click-two="selectOne1">-->
<!--                                            </search-table>-->

                                        </div></td>
                                        <td><div class="zui-table-cell flex-container cell-s">
                                            <input class="zui-input" @blur="setYYzl($index)" @keydown.13="nextSelect($event)" v-model.trim.number="item.dl" :disabled="item.readonly"/>
                                            {{item.jldwmc}}
                                        </div></td>
                                        <td class="cell-m"><div class="zui-table-cell cell-m">
                                            <input class="zui-input" @blur="setYYzl($index)" @keydown.13="nextSelect($event)" v-model.trim.number="item.yka368" :disabled="item.readonly"/>
                                        </div></td>
                                        <td><div class="zui-table-cell cell-s" >{{item.yyzl}}{{item.dw}}</div></td>
                                        <td>
                                            <div class="zui-table-cell cell-s" >
                                                <select-input  @change-data="resultChange_yfbm" :not_empty="false"
                                                              :child="rdbzContent" :index="'yka027'" :index_val="'yka026'" :val="item.yka026"
                                                              :name="'yka026.' + $index + '.yka026'" :index_mc="'yka027'" :search="true">
                                                </select-input>
                                            </div>
                                        </td>
                                        <td class="cell-m" v-if="brxxContent.zt == '9' || brxxContent.zt == '0' || brxxContent.zt == '1'"><!--操作-->
                                            <div class="zui-table-cell cell-m">
                                                <i data-title="删除" class="icon-sc margin-r-10" @click="Wf_delete($index)" ></i>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <p v-if="zlfaList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                            </div>
                        </div>
            </div>


            <div class="flex-container flex-align-c padd-r-10 padd-b-10">
                <button class="tong-btn  btn-parmary" @click="addList">增加行</button>
                <button class="tong-btn btn-parmary paddr-r5" v-if="brxxContent.zt =='9'"  @click="hisSave()">保存登记</button>
                <button class="tong-btn btn-parmary paddr-r5" v-if="brxxContent.zt =='0' && !readonly"  @click="updatedMtdj">保存</button>
                <button class="tong-btn btn-parmary paddr-r5" v-if="brxxContent.zt == '0' && readonly"  @click="saveFun">审核</button>
                <button class="tong-btn btn-parmary paddr-r5" v-if="brxxContent.zt == '1'"  @click="updatedWard">变更</button>
                <button class="tong-btn  btn-parmary-d9" @click="quxiao">取消</button>

                <button class="tong-btn  btn-parmary" style="margin-left: 200px;" @click="backwardDj">回退登记</button>
                <button class="tong-btn  btn-parmary"  @click="backwardSq">回退申请</button>

            </div>


        </div>

    </div>
    <div id="loadPage"></div>
</div>
</body>
</html>
<script type="application/javascript" src="index.js"></script>
<!--676531111dd-->
