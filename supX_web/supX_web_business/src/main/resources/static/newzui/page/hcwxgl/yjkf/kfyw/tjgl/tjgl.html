<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>调价管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="tjgl.css" rel="stylesheet"/>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="jyxm_icon">
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-show="isShowkd">开单</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kd(1)" v-if="isShowpopL">添加材料</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="searchHc"
                        v-show="isShowkd || isShowpopL">刷新
                </button>
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" v-show="!isShowkd && !isShowpopL"
                        @click="searchHc">刷新
                </button>
            </div>
            <div class="tong-search">
                <div class="flex-container flex-align-c padd-b-10  " v-show="isShowkd">
                    <div class="flex-container flex-align-c margin-r-10">
                        <label class="ft-14 margin-r-10">库房</label>
                        <select-input @change-data="resultRydjChange" class="wh122"
                                      :child="KFList" :index="'kfmc'" :index_val="'kfbm'" :val="popContent.kfbm"
                                      :name="'popContent.kfbm'" :search="true" :index_mc="'kfmc'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c margin-r-10">
                        <label class="ft-14 margin-r-10">时间段</label>
                        <div class="  flex-container flex-align-c">
                            <input class="zui-input todate wh120 " placeholder="请选择申请开始日期" id="timeVal"/>
                            <span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input todate wh120 " placeholder="请选择申请结束时间" id="timeVal1"/>
                        </div>
                    </div>
                    <div class="flex-container flex-align-c margin-r-10">
                        <label class="ft-14 margin-r-10">检索</label>
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" @keydown.enter="searchHc()"
                               v-model="search"/>
                    </div>

                </div>
                <div class="jbxx fyxm-hide" :class="{'btn-show':isShowkd==false}">
                    <div class="jbxx-size">
                        <div class="jbxx-position">
                            <span class="jbxx-top"></span>
                            <span class="jbxx-text">基本信息</span>
                            <span class="jbxx-bottom"></span>
                        </div>
                        <div class="flex-container padd-t-10 padd-b-10 padd-l-10 flex-align-c grid-box">
                            <div class="flex-container flex-align-c margin-r-10">
                                <label class="ft-14 margin-r-10 ">库房</label>
                                <select-input @change-data="resultRydjChange" class="wh100"
                                              :child="KFList" :index="'kfmc'" :index_val="'kfbm'" :val="popContent.kfbm"
                                              :name="'popContent.kfbm'" :search="true" :index_mc="'kfmc'"
                                              :disable="jyinput">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-r-10">
                                <label class="ft-14 margin-r-10 ">调价类型</label>
                                <select-input @change-data="resultLexChange" class="wh100"
                                              :child="tjlx" :index="'tjlxz'" :index_val="'tjlxz'" :val="tjlxvalue"
                                              :name="'tjlx.tjlxvalue'" :search="true" :index_mc="'tjlxz'"
                                              :disable="jyinput">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-r-10">
                                <label class="ft-14 margin-r-10 ">是否即时</label>
                                <select-input @change-data="resultJsChange" class="wh100"
                                              :child="sfjs" :index="'sfjsz'" :index_val="'sfjsz'" :val="sfjsvalue"
                                              :name="'sfjs.sfjsvalue'" :search="true" :index_mc="'sfjsz'"
                                              :disable="jyinput">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-r-10" v-show="sfjsz!=1">
                                <label class="ft-14 margin-r-10 ">调整时间</label>
                                <input class="zui-input wh180" placeholder="请选择调整时间" id="tzsjVal"/>
                            </div>
                            <div class="flex-container flex-align-c margin-r-10">
                                <label class="ft-14 margin-r-10">备注</label>
                                <input class="zui-input wh180" placeholder="请输入备注" type="text" id="bzms"
                                       v-model="popContent.bzms" :disabled="jyinput"/>
                            </div>

                        </div>

                        <div class="rkgl-kd">
                            <span>开单日期:<i v-text="zdrq"></i></span>
                            <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view  padd-r-10 padd-l-10" id="utable1">
            <!--入库列表-->
            <div class="zui-table-header" v-show="isShowkd">
                <table class="zui-table table-width50-1">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>调价单号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>调价人</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>制单日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
						<th>
						    <div class="zui-table-cell cell-l"><span>调价时间</span></div>
						</th>
						<th>
						    <div class="zui-table-cell cell-s"><span>调价状态</span></div>
						</th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body " v-show="isShowkd" id="zui-table" @scroll="scrollTable($event)">
                <table class="zui-table table-width50-1">
                    <tbody>
                    <tr v-for="(item,$index) in tjdList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)"
                        :tabindex="$index"
                        @dblclick="showDetail($index,item)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.tjdjh">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.tjrxm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <i v-text="isshzh_tran[item.qrzfbz]"
                                   :class="item.qrzfbz=='0' ? 'color-dsh':item.qrzfbz=='1' ? 'color-ysh' : item.qrzfbz=='2' ? 'color-wtg' : item.qrzfbz=='3' ? 'color-yzf':'' "></i>
                            </div>
                        </td>
						<td>
							<div class="zui-table-cell cell-l" v-if="item.qrzfbz == '0'">无</div>
							<div class="zui-table-cell cell-l" v-if="item.qrzfbz=='2'">无</div>
						    <div class="zui-table-cell cell-l" v-if="item.qrzfbz == '1' && item.sfjsvalue == '1'" v-text="fDate(item.qrzfrq,'AllDate')"></div>
							<div class="zui-table-cell cell-l" v-else-if="item.qrzfbz == '1' && item.sfjsvalue != '1'" v-text="fDate(item.tzsjval,'AllDate')">序号</div>
							
						</td>
						<td>
						    <div class="zui-table-cell cell-s" v-if="item.qrzfbz == '1' && item.tzzt == '1'">已调价</div>
						    <div class="zui-table-cell cell-s" v-else-if="item.qrzfbz == '1' && item.tzzt != '1'">待调价</div>
							<div class="zui-table-cell cell-s" v-else-if="item.qrzfbz=='2'">已作废</div>
							<div class="zui-table-cell cell-s" v-else>待调价</div>
						    
						</td>
						
                        <td class="cell-l">
                            <div class="zui-table-cell cell-l">
                                <span class="flex-center padd-t-5">
                                       <em class="width30" v-if="item.qrzfbz == '0'">
                                         <i class="icon-sh" data-title="审核" @click="showDetail($index,item)"></i>
                                       </em>
                                       <em class="width30" v-if="item.qrzfbz == '0'">
                                        <i class="icon-js" data-title="作废" @click="invalidData($index)"></i>
                                       </em>
                                    <em class="width30" v-if="item.qrzfbz != '1' && item.qrzfbz !='2'">
                                        <i class="icon-bj" data-title="编辑" @click="editIndex($index)"></i>
                                    </em>
                                </span>


                            </div>
                        </td>
                        <p v-if="tjdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--添加材料-->
            <div class="zui-table-header" v-if="isShow" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
						<th class="cell-m">
						    <div class="zui-table-cell cell-m">序号</div>
						</th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>调价库房</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>供应商</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>材料编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>材料批号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>有效期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>材料名称</span></div>
                        </th>

                        <th>
                            <div class="zui-table-cell cell-s"><span>数量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>单位</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>产地</span></div>
                        </th>

                        <th>
                            <div class="zui-table-cell cell-s"><span>原进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>现进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>进价差额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>原零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>现零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>零价差额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                        </th>
						<th class="cell-s">
						    <div class="zui-table-cell cell-s"><span>操作</span></div>
						</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)" v-if="isShow" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index">
						<td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-if="item.yply =='1'" v-text="item.kfmc">二级库房</div>
							<div class="zui-table-cell cell-s" v-if="item.yply =='2'" v-text="item.yfmc">二级库房</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ghdwmc">二级库房</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ypbm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.xtph">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz, 'date')">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ypmc">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.tjsl">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jldwmc">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cdmc"></div>
                        </td>

                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yjj">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.xjj">状态</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="Math.round((item.xjj-item.yjj) * 10000) / 10000">
                                状态
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ylj">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.xlj">状态</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="Math.round((item.xlj-item.ylj) * 10000) / 10000">
                                状态
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fzbl">状态</div>
                        </td>

								<td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                                       @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除"
                                                                       @click="scmx($index)"></i></em>
                            </span>
                                </div>
                            </td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="zui-table-fixed table-fixed-l" v-if="isShow">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="zui-table-fixed table-fixed-r" v-if="isShow">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                                       @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除"
                                                                       @click="scmx($index)"></i></em>
                            </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage" :totle-page="totlePage" v-show="isShowkd" :page="page" :param="param"
                  :prev-more="prevMore" :next-more="nextMore"></page>
            <div class="rkgl-position" v-show="isShow">
           <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-show="zfShow">作废</button>
               <!-- <button class="tong-btn btn-parmary-f2a xmzb-db" @click="jujue" v-show="ShShow">拒绝</button> -->
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow">审核</button>
           </span>
            </div>
        </div>


    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--编辑材料-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <!--
            <li>
                    <i>调价范围</i>
                    <input class="zui-input" disabled="disabled" value="一级库房调价" @keydown="nextFocus($event)">
            </li>
            -->
            <li>
                <i>材料名称</i>
                <input id="ypmc" class="zui-input" v-model="popContent.ypmc" :disabled="wisUpdate ? true :false"
                       v-if="wisUpdate==0" @keyup.up.down.enter="changeDown($event,'ypmc')"
                       @input="change($event,'ypmc', $event.target.value)"/>
                <search-table :message="searchCon" :selected="selSearch" :current="dg.page" :rows="dg.rows"
                              :total="total"
                              :them="them" :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne">
                </search-table>
            </li>


            <li>
                <i>商品名</i>
                <input type="text" class="zui-input" disabled="disabled" v-model="popContent.ypspm"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>材料编码</i>
                <input type="text" class="zui-input" disabled="disabled" v-model="popContent.ypbm"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>产地</i>
                <input type="text" class="zui-input" disabled="disabled" v-model="popContent.cdbm"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>单位</i>
                <input type="number" class="zui-input" disabled="disabled" v-model="popContent.ghdwmc"
                       @keydown="nextFocus($event)">
            </li>

            <li>
                <i>库房单位</i>
                <input type="text" class="zui-input" v-model="popContent.kfdwmc" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                <i>二级库房单位</i>
                <input type="text" class="zui-input" disabled="disabled" v-model="popContent.yfdwmc"
                       @keydown="nextFocus($event)"/>
            </li>
			<!-- 
            <li>
                <i>一级库房库存</i>
                <input type="text" class="zui-input text-indent20" disabled="disabled" v-model="popContent.ykkcsl"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>二级库房库存</i>
                <input type="text" class="zui-input text-indent20" disabled="disabled" v-model="popContent.yfkcsl"
                       @keydown="nextFocus($event)"/>
            </li>
			-->
            
            <li>
                <i>规格</i>
                <input type="text" class="zui-input " disabled="disabled" v-model="popContent.ypgg"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>原进价</i>
                <input type="number" class="zui-input" disabled="disabled" v-model="popContent.yjj"
                       @keydown="nextFocus($event)">
            </li>
            <li>
                <i>现进价</i>
                <input type="number" class="zui-input" id="xjj" type="number" @change="getXlj()" v-if="wtjlxz==2"
                       disabled="disabled"
                       @keydown="nextFocus($event)" v-model="popContent.xjj">
                <input type="number" class="zui-input" id="xjj" type="number" @change="getXlj()" v-if="wtjlxz==1"
                       @keydown="nextFocus($event)" v-model="popContent.xjj">
                <input type="number" class="zui-input" id="xjj" type="number" @change="getXlj()" v-if="wtjlxz==3"
                       @input="getXlj()"
                       @keydown="nextFocus($event)" v-model="popContent.xjj">
            </li>
            <li>
                <i>原零价</i>
                <input type="text" class="zui-input" disabled="disabled" v-model="popContent.ylj"
                       @keydown="nextFocus($event)">
            </li>
            <li>
                <i>现零价</i>
                <input class="zui-input" id="xlj" type="number" v-if="wtjlxz==1" disabled="disabled"
                       @keydown.enter="changeDown($event,'xlj')"
                       v-model="popContent.xlj">
                <input class="zui-input" id="xlj" type="number" v-if="wtjlxz!=1"
                       @keydown.enter="changeDown($event,'xlj')"
                       v-model="popContent.xlj">
            </li>
			
			<li>
                <i>总库存</i>
                <input type="text" class="zui-input text-indent20" disabled="disabled" v-model="popContent.kcsl"
                       @keydown="nextFocus($event)"/>
            </li>
			
			<li v-for="(item, $index) in kclist">
			    <i v-text="item.kfmc"></i>
			    <input type="text" class="zui-input text-indent20" :value="item.ykkcsl" disabled="disabled" />
			</li>
<li>
			    <i>批次</i>
			    <input type="text" class="zui-input text-indent20" disabled="disabled" v-model="popContent.scph"
			           @keydown="nextFocus($event)"/>
			</li>
            <li>
                <i>调价依据</i>
                <input type="text" class="zui-input" placeholder="请输入..." @keydown="nextFocus($event)"
                       v-model="tjdContent.bzms" @keydown="nextFocus($event)"/>
            </li>
            <!--
            <li>
                    <i>原&ensp;药&ensp;房进&ensp;&ensp;&ensp;&ensp;价</i>
                    <input type="text" class="zui-input"  v-model="popContent.yyfjj" @keydown="nextFocus($event)" disabled>
            </li>
            <li>
                    <i>原&ensp;药&ensp;房零&ensp;&ensp;&ensp;&ensp;价</i>
                    <input type="text" class="zui-input"   v-model="popContent.yyflj" @keydown="nextFocus($event)" disabled>
            </li>
			-->
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">添加</button>
    </div>
</div>

<script src="tjgl.js"></script>

</body>

</html>
