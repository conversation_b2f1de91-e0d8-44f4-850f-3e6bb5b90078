var content = new Vue({
    el: '#content',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data: {
        objAvatar: 8,
        edit: false,
        pageShow: true,
        editText: true,
        tipsShow: [],
        tipsShow1: [],
        tipsShow2: [],
        tipsShow3: [],
        canvasImgArr:[],
        userName: false,
        previewshow: false,
        Brxx_List: [],
        objabsolute: {},
        defaltObj: {
            title: '死亡讨论邀请',
            cs: 'background-f4b26b color-8e9694',
            cb: '拒绝',
            sb: '接受'
        },
        subObj: {
            title: '死亡讨论邀请',
            cs: '',
            cb: '',
            sb: ''
        },
        refuseObj: {
            title: '拒绝原因',
        },
    },
    mounted:function() {
        this.popContent.shsj = this.fDate(new Date(), 'date') + ' ' + this.fDate(new Date(), 'times');
        console.log(this.shsj);
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    content.popContent.shsj = value;
                } else {
                    content.popContent.shsj = '';
                }
            }
        });
    },
    methods: {
        cyrqz: function (event) {
            autograph.objSetLT.left = event.clientY / 2
            autograph.objSetLT.top = event.clientX *2
            autograph.autographShow = true
        },
        deleteImg: function () {
            alert(1111)
        },
        tipsShowList: function (type, index, variable) {
            this[variable][index] = type ? true : false
            return this.$forceUpdate()
        },
        tjys: function () {
            brzcList.type = false
        },
        rowsJ: function (event) {
            if (event.keyCode == 13) {
                event.srcElement.rows = event.srcElement.rows + 1
            } else if (event.keyCode == 8) {
                if (event.srcElement.rows != 1) {
                    event.srcElement.rows = event.srcElement.rows - 1
                }
            }
        },
        editShow: function () {
            this.editText = !this.editText
        },
        preview: function () {
            this.previewshow = true
        },
        previewHide: function () {
            this.previewshow = false
        },
        hoverName: function (falg, index, event) {
            this.objabsolute.left = event != undefined ? event.clientX - 80 : 0
            this.objabsolute.top = event != undefined ? event.clientY + 20 : 0
            this.userName = falg == true ? true : falg
            console.log(this.objabsolute)
            this.$forceUpdate()
        },
        accept() {
            common.openConfirm('确定接受患者：<span class="color-green">' + "李浩然" + '</span>的死亡讨论吗？', function () {

            }, function () {
                common.openConfirm('<textarea placeholder="请输入拒绝原因" class="zui-textarea" style="width: 100%;height: 92px;text-indent:0;" id="refuseText"></textarea>', function () {

                }, function () {

                }, content.refuseObj)
            }, this.defaltObj)
        },
        submit() {
            common.openConfirm('确定审核患者：<span class="color-green">' + "李浩然" + '</span>的死亡讨论吗？', function () {

            }, function () {
            }, this.subObj)
        },
    },
    create() {

    },
})

var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data: {
        type: true,
        popContent: {}
    },
    create() {

    },
    methods: {
        fqtl: function () {

        },
        getData: function () {

        },
        Sure: function () {
            this.type = true
        },
        cancel: function () {
            this.type = true
        }
    },
})
var rescue = new Vue({
    el: '#rescue',
    data: {
        objAvatar: 8,
        fqqjShow: false,
        pageShow: false,
        success: false,
        fail: false,
        fqsucc: true,
        fqqj: '发起危重病人抢救...',
    },
    methods: {
        rescueClick: function () {
            this.fqqj = '正在发起…';
            this.fqqjShow = true
        },
        successShow: function () {
            this.fqqj = '发起成功'
            this.success = true
            this.fqsucc = false
            this.fail = false
            setTimeout(function () {
                rescue.pageShow = false
                content.pageShow = true
            }, 2000)
        },
        clear: function () {

        },
        failShow: function () {
            this.fqqj = '网络连接异常！'
            this.success = undefined
            this.fail = true
        }
    },
})
var popPrint = new Vue({
    el: '.popPrint',
    data: {
        PrintShow: false,
    },
    methods: {
        close: function () {
            this.PrintShow = false
        },
        print:function () {
            window.print()
        }
    },
    created: function () {

    },

})
context=''
var autograph = new Vue({
    el: '#autograph',
    data: {
        objSetLT: {},
        point: {},
        autographShow: false,
        clickX:[],
        clickY:[],
        context:'',
        clickDrag:[],
        paint:false,
        flag:false,
    },
    mounted:function(){
        context = this.$refs.canvas.getContext("2d");
        this.point.notFirst = false;
    },
    methods: {
        close: function () {
            this.autographShow = false
        },
        mousedowns: function (e) {
            this.paint = true;
            this.flag=true
            this.addClick(e.offsetX - this.$refs.canvas.offsetLeft, e.offsetY+48 - this.$refs.canvas.offsetTop);
            this.redraw();
        },
        mouseups: function (e) {
            this.paint = false;
        },
        mousemoves: function (e) {
            console.log(this.paint)
            if (this.paint) {
                this.addClick(e.offsetX - this.$refs.canvas.offsetLeft, e.offsetY+48 - this.$refs.canvas.offsetTop, true);
                this.redraw();

            }
        },
        mouseleaves: function (e) {
            console.log(this.paint)
            this.paint = false;
        },
        addClick: function (x, y, dragging) {

            this.clickX.push(x);

            this.clickY.push(y);

            this.clickDrag.push(dragging);

        },
        redraw: function () {
            context.strokeStyle = "#000000";

            context.lineJoin = "round";

            context.lineWidth = 4;
            while (this.clickX.length > 0) {
                this.point.bx = this.point.x;

                this.point.by = this.point.y;

                this.point.x = this.clickX.pop();

                this.point.y = this.clickY.pop();

                this.point.drag = this.clickDrag.pop();

                context.beginPath();

                if (this.point.drag && this.point.notFirst) {
                    console.log(this.point.bx, this.point.by)
                    context.moveTo(this.point.bx, this.point.by);

                } else {
                    this.point.notFirst = true;

                    context.moveTo(this.point.x - 1, this.point.y);

                }
                context.lineTo(this.point.x, this.point.y);
                context.closePath();
                context.stroke();
            }
        },
        blob:function(dataURL){
                const arr = dataURL.split(',');
                const mime = arr[0].match(/:(.*?);/)[1];
                const bStr = atob(arr[1]);
                let n = bStr.length;
                const u8arr = new Uint8Array(n);
                while (n--) {
                    u8arr[n] = bStr.charCodeAt(n);
                }
                return new Blob([u8arr], { type: mime });
        },
        submit:function () {
            if(this.flag!=false){
                content.ajaxImgArr=this.blob(this.$refs.canvas.toDataURL("image/png")) //这个是需要提交到后台额数据
                content.canvasImgArr.push(this.$refs.canvas.toDataURL("image/png")) //展示数据
                this.$forceUpdate()
                this.clear();
                this.flag=false //保存了图片需要设置为false，不然会出现空图片
            }
        },
        clear:function () {
            this.$refs.canvas.width = this.$refs.canvas.width;
        }
    },
})

function dybl() {
    popPrint.PrintShow=true
}






