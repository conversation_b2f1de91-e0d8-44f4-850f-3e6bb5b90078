<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <title>农合数据上传</title>
    <link rel="stylesheet" href="/page/xtwh/xtpz/mzpjgs/mzpjgs.css" media="print"/>
    <link rel="stylesheet" href="/newzui/pub/css/print.css" media="print"/>
    <!--<link rel="stylesheet" href="/page/mzys/zlgl/brjz/brjz.css">-->
    <link rel="stylesheet" href="nhsjsc.css">
</head>
<body class="skin-default background-f  padd-l-10 padd-r-10">
<div class="printArea printShow"></div>
<div class="brjz printHide flex-container ">
    <div class="left_tab1 flex-container flex-dir-c" id="left_tab1" v-cloak>
        <div class="searchLeft_tab1 " id="searchLeft">
            <div class="flex-container flex-align-c">
                <span class="whiteSpace">当前科室：</span>
                <select-input 	class="wh182" @change-data="ksChange" :not_empty="false" :child="ksList"
                              :index="'ksmc'" :index_val="'ksbm'" :index_mc="'ksmc'" :val="param.ksbm"
                              :name="'param.ksbm'" :search="true">
                </select-input>
            </div>
            <div class="flex-container flex-align-c">
                <span class="whiteSpace">搜&emsp;&emsp;索：</span>
                <div class="position flex-container flex-align-c wh182">
                  	<input class="zui-input" v-model="text" @input="getBrData()"  placeholder="姓名，拼音代码">
                    <span class="fa fa-search cm leftSearchIcon" @clikc="searching()"></span>
                </div>
            </div>
        </div>

        <!-- 病人列表 -->
        <div class="personList over-auto" id="brxxList" >
            <div style="font-size: 14px">患者信息</div>
            <div v-for="(item, $index) in jsonList" @click="checkOne($index)" class="margin-top-10 margin-b-5" style="cursor: pointer" @dblclick="edit($index)" :brid="item.brid" :ghxh="item.ghxh">
                    <span v-text="item.brxm"></span>
                    <span>（<i v-text="item.nl"></i>&nbsp;&nbsp;{{nldw_tran[item.nldw]}} )</span>
                    <span><i v-text="item.zyh"></i>&nbsp;&nbsp;({{item.ryzdmc}})</span>
            </div>
        </div>
    </div>
        <div class="contextDiv">
            <div v-cloak class="nh-menu flex-container flex-jus-sb">
                <tabs  :num="which" :tab-child="[{text:'合医记录'},{text:'未上传记录'},{text:'错误信息'},{text:'预结算'}]" @tab-active="loadCon"></tabs>
                <div class="nh-screen">
                    <div>
                        <label><input type="checkbox" />自动上传</label>
                    </div>
                    <div>
                        <label><input type="checkbox" />出院病人</label>
                    </div>
                    <div>
                        <label><input type="checkbox" />未对码</label>
                    </div>
                    <div>
                        <label><input type="checkbox" />已对码</label>
                    </div>
                </div>
            </div>
            <div class="context">
                <div id="scnh">
                    <div class="page_div hyjl"></div>
                    <div class="page_div wscjl"></div>
                    <div class="page_div cwxx"></div>
                    <div class="page_div yjs"></div>
                </div>
            </div>
        </div>
</div>


</body>
<script type="application/javascript" src="nhsjsc.js"></script>
</html>
