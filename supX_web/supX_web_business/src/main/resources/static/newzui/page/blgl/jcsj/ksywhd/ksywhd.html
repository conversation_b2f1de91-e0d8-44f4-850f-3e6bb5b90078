<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室业务活动</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ksywhd.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">添加</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
            <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">科室</label>
                    <div class="zui-input-inline">
                        <select-input @change-data="resultChange"
                                      :not_empty="false" :child="LcList"
                                      :index="'ksmc'" :index_val="'ksid'"
                                      :val="ksmc" :search="true" :name="'ksmc'"
                                      id="cdajcmb" :index_mc="'ksmc'">
                        </select-input>
                        <input type="hidden" v-model="ksmc" id="lcsjzid"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="zui-table-view ybglTable padd-l-10 padd-r-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="sexs" z-width="100px">
                        <div class="zui-table-cell">科室id</div>
                    </th>
                    <th z-field="sex" z-width="100px">
                        <div class="zui-table-cell">科室名称</div>
                    </th>
                    <th z-field="city" z-width="100px">
                        <div class="zui-table-cell">科室业务活动id</div>
                    </th>
                    <th z-field="jm1" z-width="80px">
                        <div class="zui-table-cell">业务活动编码</div>
                    </th>
                    <th z-field="jm2" z-width="100px">
                        <div class="zui-table-cell">业务活动id</div>
                    </th>
                    <th z-field="jm3" z-width="80px">
                        <div class="zui-table-cell">业务活动分类</div>
                    </th>
                    <th z-field="jms" z-width="80px">
                        <div class="zui-table-cell">业务活动名称</div>
                    </th>
                    <th z-field="jm4" z-width="100px">
                        <div class="zui-table-cell">业务域</div>
                    </th>

                    <th z-field="jm5" z-width="80px">
                        <div class="zui-table-cell">最近操作时间</div>
                    </th>
                    <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                        <div class="zui-table-cell">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-height" id="zui-table">
            <table class="zui-table table-width50">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jsonList"  @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <div class="zui-table-cell">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.ksid"></div></td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ksmc" :data-title="item.ksmc"></i>
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ksywhdjlid" :data-title="item.ksywhdjlid"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.hdbm" :data-title="item.hdbm"></i>
                        </div>
                    </td>
                    <td><div class="zui-table-cell" v-text="item.ywhdjlid"></div></td>

                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.hdfl" :data-title="item.hdfl"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.hdmc" :data-title="item.hdmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="item.ywy" :data-title="item.ywy"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell relative">
                            <i class="title title-width" v-text="fDate(item.zjczsj,'date')" :data-title="fDate(item.zjczsj,'date')"></i>
                        </div>
                    </td>
                    <td width="100px"><div class="zui-table-cell">
                        <i class="icon-bj" @click="edit($index)"></i>
                        <i class="icon-sc icon-font" @click="remove"></i>
                    </div></td>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<div class="side-form ng-hide pop-850" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
       <div class="ksywhd-left">
           <div class="zui-form">
               <div class="zui-inline">
                   <label class="zui-form-label">检索</label>
                   <div class="zui-input-inline">
                       <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalues" @keydown="searchKs()"/>
                   </div>
               </div>
           </div>
         <div class="ksywhd-top" :class="{'active':ejShow}"><i class="ksywhd-toggle ksywhd-toggleup" :class="{'ksywhd-toggledown':ejShow}"></i><i class="ksywhd-title" @click="ejMore?ejUp(): ejDown()">科室列表</i></div>
          <ul class="ksywhd-ej" v-show="ejShow">
              <li v-for="(item,$index) in treeData" @click="rightGata(item.ksid)"><i class="icon-bd"></i><i class="padd-l-5" v-text="item.ksmc"></i></li>
          </ul>
       </div>
       <div class="ksywhd-right">
           <div class="zui-form">
               <div class="zui-inline">
                   <label class="zui-form-label">检索</label>
                   <div class="zui-input-inline">
                       <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalues1" @keydown="searchCs()"/>
                   </div>
               </div>
           </div>
           <div class="right-top">
               <i>
                   <input-checkbox @result="reCheckBox" :list="'rList'"
                                   :type="'all'" :val="isCheckAll">
                   </input-checkbox>
               </i>
               <i>活动记录id</i>
               <i>业务域</i>
               <i>记录分类</i>
               <i>活动编码</i>
               <i>记录名称</i>
           </div>
            <ul class="right-list">
                <li :tabindex="$index" v-for="(item,$index) in rList" @click="checkSelect([$index,'some','rList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index">
                    <i>
                        <input-checkbox @result="reCheckBox" :list="'rList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </i>
                    <i v-text="item.ywhdjlid"></i>
                    <i v-text="item.ywy"></i>
                    <i v-text="item.ywhdjlfl"></i>
                    <i v-text="item.ywhdjlbm"></i>
                    <i v-text="item.ywhdjlmc"></i>
                </li>
            </ul>
       </div>

    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ksywhd.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>