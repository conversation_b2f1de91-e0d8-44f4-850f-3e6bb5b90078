<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="rydj.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="printArea printShow"></div>
<div class="wrapper printHide" style="background: #fff">
    <!--入院登记查询列表视图begin-->
    <div id="tableInfo" v-show="isShow">
        <!--入院登记功能按钮begin-->
        <div class="panel">
            <div class="tong-top" >
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addData">新增收费</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData">刷新</button>
                <button class="tong-btn btn-parmary-b "><i class="padd-l-20 icon-width icon-dsfz"></i>读身份证</button>
                <button class="tong-btn btn-parmary-b"><i class="padd-l-20 icon-width icon-dylk"></i>读医疗卡</button>
                <button class="tong-btn btn-parmary-b"><i class="padd-l-20 icon-width icon-dybk"></i>读医保卡</button>
            </div>
            <div class="flex" style="padding: 13px 0;">
                <div class="flex flex_items  margin-l-20">
                    <label class="whiteSpace margin-r-5 ft-14">时间段</label>
                    <div class="position margin-l13 flex-container flex-align-c">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh120 text-indent20" placeholder="不限定时间范围" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh120 " placeholder="请选择处方结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="flex flex_items  margin-l-20">
                    <label class="whiteSpace margin-r-5 ft-14">检索</label>
                    <div class="zui-input-inline margin-l13">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown.enter="getData"/>
                    </div>
                </div>
            </div>
        </div>
        <!--入院登记功能按钮end-->

        <!--检索字段begin-->

        <!--检索字段end-->

        <!--循环列表begin-->
        <div class="zui-table-view padd-l-10 padd-r-10 padd-l-10" id="brRyList">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-l"><span>挂号序号/住院号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>姓名</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>性别</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>出生日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>年龄</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>病人费别</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)" >
                <table class="zui-table table-width50" v-if="jsonList.length!=0">
                    <tbody>
                    <tr :tabindex="$index" v-for="(item, $index) in jsonList"
                        class="tableTr2"><!--@dblclick="edit($index)"双击回调-->
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.ghxh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.csrq,'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brnl+nldw_tran[item.nldw]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fbmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="sfzt_tran[item.sfzt]"></div>
                        </td>
                        <td><!--操作-->
                            <div class="zui-table-cell cell-s icon-bj_parent">
                                <!--<i class="icon-icon icon-tk-h" title="退预缴费" ></i>-->
                                <i class="icon-icon icon-jf-h" title="收费" @click="yjfButtClick(item.ghxh,false)"></i>
                                <!--<i class="icon-icon icon-dysqb" style="vertical-align: text-bottom;" title="打印申请"></i>-->
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
            </div>

            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
        <!--循环列表end-->
    </div>
    <!--入院登记查询列表视图end-->

    <!--入院登记添加记录视图begin-->
    <!--入院登记添加记录视图end-->

    <!--收/退预交金begin-->
    <div id="syjjInfo" v-if="isShow">
        <!--收/退预交金 功能按钮begin-->
        <div class="syjj-info-box">
            <div v-if="userShow" class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">患者信息</div>
                </div>
                <div class="tab-card-body">
                    <div class="zui-form grid-box">
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">患者姓名</label>
                            <div class="zui-input-inline">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                                <input class="zui-input" :disabled="mzjbxxContent.isedit" v-model="mzjbxxContent.brxm"
                                       @input="searching0(false,'brxm',$event.target.value)"
                                       data-notEmpty="false" @keyDown="changeDown0($event,'brxm')"
                                       placeholder="姓名，挂号序号">
                                <search-table :message="searchCon" :selected="selSearch"
                                              :them="them" :them_tran="them_tran" :page="page"
                                              @click-one="checkedOneOut" @click-two="selectOne0">
                                </search-table>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">病人性别</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" :disabled="mzjbxxContent.isedit"
                                       :title="mzjbxxContent.hospitalAD" :value="brxb_tran[mzjbxxContent.brxb]">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3 flex">
                            <label class="zui-form-label">年龄</label>
                            <input class="zui-input" type="text" disabled v-model="mzjbxxContent.brnl"/>
                            <div style="width: 72px">
                                <select-input
                                        disable="disabled"
                                        @change-data="resultChange"
                                        :not_empty="true"
                                        :child="nldw_tran"
                                        :index="mzjbxxContent.nldw"
                                        :val="mzjbxxContent.nldw"
                                        :name="'mzjbxxContent.nldw'">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">医疗卡余</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" disabled
                                       :title="fzContent.ylkye"
                                       v-model="mzjbxxContent.ylkye">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">医保卡余</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" disabled
                                       value="留着">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">费别</label>
                            <select-input @change-data="resultMzsfChange" :not_empty="true"
                                          :child="brfbList" :index="'fbmc'" :index_val="'fbbm'" :val="fzContent.ryfbbm"
                                          :name="'fzContent.ryfbbm'" :search="true" :index_mc="'ryfbmc'">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">保险</label>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="bxlbList" :index="'bxlbmc'" :index_val="'bxlbbm'"
                                          :val="fzContent.rybxlbbm"
                                          :search="true"
                                          :name="'fzContent.rybxlbbm'" :index_mc="'rybxlbmc'">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">门诊诊断</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" v-model="mzjbxxContent.jbmc" @keydown="nextFocus($event)">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="userShow" class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">票据信息</div>
                </div>
                <div class="tab-card-body">
                    <div class="zui-form grid-box">
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">起始号</label>
                            <div class="zui-input-inline zui-select-inline">
                                <input class="zui-input" disabled
                                       :title="pjxxContent .qsh"
                                       v-model="pjxxContent.qsh">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">结束号</label>
                            <div class="zui-input-inline danwei-box">
                                <input class="zui-input" disabled
                                       :title="pjxxContent.jsh"
                                       v-model="pjxxContent.jsh">
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">现使用</label>
                            <div class="zui-input-inline danwei-box">
                                <input class="zui-input" disabled
                                       :title="pjxxContent.dqsyh"
                                       v-model="pjxxContent.dqsyh">
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">剩余</label>
                            <div class="zui-input-inline danwei-box">
                                <input placeholder="" data-notEmpty="false" class="zui-input" disabled
                                       :title="pjxxContent.fpzs"
                                       v-model="pjxxContent.fpzs">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="userShow" class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">费用明细</div>
                </div>
                <div class="tab-card-body">
                    <div class="zui-table-view" id="yjjlTable">
                        <div v-if="!mzjbxxContent.isedit" class="hsColor" @click="addxm()">添加项目</div>
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                                    </th>
                                    <th class="text-left cell-s">
                                        <div class="zui-table-cell cell-l text-left"><span>费用项目</span></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s"><span>组合费用</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>费用单价</span></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s"><span>费用数量</span></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s"><span>费用金额</span></div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s"><span>优惠比例(%)</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>优惠金额</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>核算科室</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in brfyjsonList" @dblclick="showYjjlInfo($index)" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                                    <!--@click="单击回调" @dblclick="edit($index)"双击回调-->
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m" v-text="$index+1"><!--序号--></div>
                                    </td>
                                    <td class="cell-s">
                                        <div class="zui-table-cell overflow1 cell-l text-left title"  v-text="item.mxfyxmmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.zhfymc"><!--支付类型--></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.fydj"><!--业务窗口--></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.fysl"><!--操作员姓名--></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.fyje"><!--科室名称--></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yhbl"><!--预交金额--></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yhje"><!--预交日期--></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.zxksmc"><!--预交日期--></div>
                                    </td>
                                    <td width="100px"><div class="zui-table-cell cell-s icon-bj_parent">
                                        <i class="icon-bj" @click="edit($index,item.yzhm,item.yzlx),doPop($index)"></i>
                                        <!--<i class="icon-sc icon-font" @click="remove($index)" v-show="item.xzxm"></i>-->
                                    </div></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-card LoadPage"  id="loadPage"></div>
            <div class="tab-card" v-if="hjShow">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">合计信息</div>
                </div>
                <div class="tab-card-body">
                    <div class="zui-form grid-box">
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">支付类型</label>
                            <select-input @change-data="resultChange" :data-notEmpty="true"
                                          :child="zflxList" :index="'zflxmc'" :index_val="'zflxbm'"
                                          :val="jsjlContent.zflxbm" :search="true"
                                          :name="'jsjlContent.zflxbm'" :index_mc="'zflxmc'">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">发票张数</label>
                            <div class="zui-input-inline danwei-box">
                                <input placeholder="请输入备注说明" class="zui-input" value="1张" disabled>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">应收金额</label>
                            <div class="zui-input-inline">
                                <input disabled type="number" id="advanceOrRefund" placeholder="请输入金额" class="zui-input"
                                       data-notEmpty
                                       :title="jsjlContent.ysje"
                                       v-model.number="jsjlContent.ysje"
                                       @keydown.enter="nextFocus($event)">
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">优惠金额</label>
                            <div class="zui-input-inline">
                                <input disabled placeholder="请输入备注说明" class="zui-input" data-notEmpty
                                       :title="jsjlContent.yhhj"
                                       v-model="jsjlContent.yhhj"
                                       @keydown.enter="nextFocus($event)">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">实收金额</label>
                            <div class="zui-input-inline">
                                <input disabled placeholder="请输入备注说明" class="zui-input" data-notEmpty
                                       :title="jsjlContent.fyhj"
                                       v-model="jsjlContent.fyhj"
                                       @keydown.enter="nextFocus($event)">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">现金支付</label>
                            <div class="zui-input-inline">
                                <input disabled placeholder="请输入备注说明" class="zui-input" data-notEmpty
                                       :title="jsjlContent.xjzf"
                                       v-model="jsjlContent.xjzf"
                                       @keydown.enter="nextFocus($event)">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">医疗卡支付</label>
                            <div class="zui-input-inline">
                                <input disabled placeholder="请输入备注说明" class="zui-input" data-notEmpty
                                       :title="jsjlContent.ylkzf"
                                       v-model="jsjlContent.ylkzf"
                                       @keydown.enter="nextFocus($event)">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">保险卡支付</label>
                            <div class="zui-input-inline">
                                <input placeholder="请输入备注说明" class="zui-input" data-notEmpty
                                       :title="jsjlContent.ybkzf" disabled="jsjlContent.isybkzf"
                                       v-model="jsjlContent.ybkzf"
                                       @keydown.enter="nextFocus($event)" id="ybkzf">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">其他支付</label>
                            <div class="zui-input-inline">
                                <input placeholder="请输入备注说明" class="zui-input" data-notEmpty
                                       :title="jsjlContent.qtzf"
                                       v-model="jsjlContent.qtzf" @input="qtzfChange"
                                       @keydown.enter="nextFocus($event)">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">实收金额</label>
                            <div class="zui-input-inline">
                                <input  class="zui-input"  v-model="sszbContent.ssje"  @input="ssjeChange" style="color: red">
                                <span class="cm">元</span>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">找补金额</label>
                            <div class="zui-input-inline">
                                <input  class="zui-input"  v-model="sszbContent.zbje" style="color: red" disabled="disabled">
                                <span class="cm">元</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class=" action-bar fixed flex-container flex-jus-e flex-align-c">
            <button class="zui-btn  btn-default xmzb-db btn-primary" @click="saveData()" v-if="hjShow">提交</button>
            <button class="zui-btn table_db_esc btn-default xmzb-db" v-if="hjShow" @click="hjShow=false,userShow=true,next=true,clearBx()">上一步</button>
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="quxiao">取消</button>
            <button class="zui-btn  btn-default xmzb-db btn-primary" v-if="next" @click="tabBg()" >下一步</button>
        </div>
    </div>
    <!--收/退预交金end-->
    <div class="side-form pop-width" :class="{'ng-hide':num==1}" style="padding-top: 0;" id="brzcList" role="form">
        <div class="tab-message">
            <a>添加项目</a>
            <a href="javascript:" class="fr closex ti-close"
               @click="closes"></a>
        </div>
        <div class="ksys-side">
        <span class="span0">
            <i>费用项目</i>
            <input class="zui-input" :value="brfyContent.text" @keydown="changeDown($event,'text')"
                   @input="change(null,$event.target.value)" id="mxxm">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :page="page"
                                  :them="them" :them_tran="them_tran"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
        </span>
            <span class="span0">
            <i>姓名</i>
              <input type="text" class="zui-input" name="input1" v-model="fzContent.brxm" @keydown="nextFocus($event)"/>
        </span>
            <span class="span0">
            <i>开单科室</i>
              <select-input @change-data="resultMzsfChange" :not_empty="false"
                            :child="ghksList" :index="'ksmc'" :index_val="'ksbm'" :val="fzContent.mzks"
                            :search="true"
                            :name="'fzContent.mzks'" :index_mc="'mzksmc'">
                    </select-input>
        </span>
            <span class="span0">
            <i>门诊医生</i>
                <select-input @change-data="resultChange" :not_empty="true"
                              :child="mzysList" :index="'ryxm'" :index_val="'rybm'" :val="fzContent.mzys"
                              :search="true"
                              :name="'fzContent.mzys'" :index_mc="'mzysxm'">
                    </select-input>
        </span>
            <span class="span0">
            <i>核算科室</i>
              <select-input @change-data="resultChange" :not_empty="false" :child="hsksList"
                            :index="'ksmc'" :index_val="'ksbm'" :val="fzContent.zxks"
                            :name="'fzContent.zxks'" :search="true">
                        </select-input>
        </span>
            <span class="span0 position">
            <i>单价</i>
            <input class="zui-input" :disabled="fydjShow" type="number" v-model="brfyContent.fydj"
                   @keydown="nextFocus($event)"><a class="cm">元</a>
        </span>
            <span class="span0">
            <i>处方号</i>
              <input class="zui-input" v-model="brfyContent.yzhm" @keydown="nextFocus($event)"
                     :disabled="yzhmdisa">
        </span>
            <span class="span0">
            <i>数量</i>
               <input class="zui-input" type="number" v-model="brfyContent.fysl" @keydown="changeDown($event,'fysl')">
        </span>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="searchDjqr">确定</button>
        </div>
    </div>
    <div class="side-form  pop-850 flex-container flex-dir-c" v-cloak :class="{'ng-hide':num==1}" id="popTable" role="form">
        <div class="fyxm-side-top">
            <span >明细清单</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side hzgl-height flex-container flex-dir-c flex-one ">
            <div class="col-x-12 flex-one  flex-container flex-dir-c">
                    <div class="zui-table-view flex-container flex-dir-c" id="zuiTable">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th >
                                        <div class="zui-table-cell cell-m">序号</div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-xl text-left">药品名称</div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s ">规格</div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s ">数量</div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s">单价</div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-s">总价</div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" data-no-attr="true" data-no-change style="padding-bottom: 10px" @scroll="scrollTable">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr :tabindex="$index" :class="[{'table-hovers':isChecked[$index]}]"
                                    @click="checkSelect([$index,'some','yzMxList'],$event)"
                                    v-for="(item, $index) in 30">
                                    <td>
                                        <div class="zui-table-cell cell" >12</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-xl text-over-2 text-left">支气管炎支气管炎</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">mg</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">2</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">20元</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">20元</div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>
</body>
<script src="mzsf.js" type="text/javascript"></script>
</html>
