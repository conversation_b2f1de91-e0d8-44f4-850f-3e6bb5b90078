var yndryb_008 = new Vue({
    el: '#yndryb_008',
    mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
    components: {
        'search-table': searchTable
    },
    data: {
        ip:'',
        userInfo: {},// 当前用户信息
        bxlbbm: '', // 保险类别编码
        bxurl: '',
        grxxJson: {}, // 个人信息对象
        cardParm: '', //无卡输入
        ybdjxx:{},  //医保登记信息
        yndryb_jslb_tran: {// 门诊医疗类别
            '11': '普通门诊',
            '12': '特殊病门诊',
            '13': '慢性病门诊',
            '14': '急诊抢救',
            '19': '门诊透析',
            '41': '血腹透门诊'
        },
        yndryb_fs_tran: {
            '0': '无',
            '1': '存在医疗封锁'
        },
        yndryb_sex_tran:{
            '1':'男',
            '2':'女'
        },
        jbContent: {}, // 疾病类容
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'aKA120',
            '疾病名称': 'aKA121',
            '拼音代码': 'aKA066'
        },
        zdxxJson: {
            "jslx": "1",
        },
        yndrybInit:false
    },
    mounted: function () {
        // this.ip = 'http://localhost:9001';
        // this.init();

        this.getUserIP(function(ip){
            this.ip = 'http://' + ip + ':9001';
            yndryb_008.init();
        });
    },
    methods: {
        init: function () {
            //调用云南东软医保初始化
            $.post(ip + "/yndryb/init", {}, function (json) {
                if(json.code == 0){
                    yndryb_008.yndrybInit = true;
                    malert("医保控件初始化成功!");
                }else{
                    yndryb_008.yndrybInit = true;
                    malert("医保控件初始化失败！" + json.msg,'top','defeadted');
                }
            });
        },

        // 获取当前浏览器客户端ip
        getUserIP:function(onNewIP) {
            var MyPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;
            var pc = new MyPeerConnection({
                iceServers: []
            });
            noop = function() {},
                localIPs = {};
            ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g;
            function iterateIP(ip) {
                if (!localIPs[ip]) onNewIP(ip);
                localIPs[ip] = true;
            }
            pc.createDataChannel('');
            pc.createOffer().then((sdp) => {
                sdp.sdp.split('\n').forEach(function (line) {
                    if (line.indexOf('candidate') < 0) return;
                    line.match(ipRegex).forEach(iterateIP);
                });
                pc.setLocalDescription(sdp, noop, noop);
            }).catch((reason) => {
            });
            pc.onicecandidate = (ice) => {
                if (!ice || !ice.candidate || !ice.candidate.candidate || !ice.candidate.candidate.match(ipRegex)) return;
                ice.candidate.candidate.match(ipRegex).forEach(iterateIP);
            };
        },

        // 获取操作员用户信息
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    this.userInfo = json.body.d;
                });
        },

        // 获取保险类别信息
        getbxlb: function () {
            var param = {bxjk: "008"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        yndryb_008.bxlbbm = json.d.list[0].bxlbbm;
                        yndryb_008.bxurl = json.d.list[0].url;
                    }
                } else {
                    malert("保险类别查询失败!" + json.c, 'top', 'defeadted')
                }
            });
        },

        // 读卡
        load: function () {
            if (yndryb_008.yndrybInit) {
                var inputData = '1||'; //卡类型|个人编号|身份证Or个人编号
                $.post(ip + "/yndryb/getEmpInfo", {inputData: inputData}, function (json) {
                    if(json.code == 0){
                        yndryb_008.grxxJson = json.data;
                        malert("读卡成功!");
                        // yndryb_008.queryYbdj();
                    }else{
                        malert("读卡失败！" + json.msg,'top','defeadted');
                    }
                });
            } else {
                malert("医保控件未初始化,请重新打开页面！", 'top', 'defeadted');
            }
        },

        // 无卡读卡
        loadSFZ: function () {
            if (yndryb_008.yndrybInit) {
                if (this.cardParm == null || this.cardParm == undefined || this.cardParm.length==0){
                    malert("请输入身份证号或个人编号！",'top','defeadted');
                    return
                }

                var inputData = '2||' + this.cardParm; //卡类型|个人编号|身份证Or个人编号
                $.post(ip + "/yndryb/getEmpInfo", {inputData: inputData}, function (json) {
                    if(json.code == 0){
                        yndryb_008.grxxJson = json.data;
                        malert("读卡成功!");
                        // yndryb_008.queryYbdj();
                    }else{
                        malert("读卡失败！" + json.msg,'top','defeadted');
                    }
                });
            } else {
                malert("医保控件未初始化,请重新打开页面！", 'top', 'defeadted');
            }
        },

        //查询医保登记信息
        queryYbdj: function(){
            var parm = {
                ybgrbh: yndryb_008.grxxJson.grbh,
                hisywbh: rightVue.brxxContent.ghxh
            };
            $.post(ip + "/yndryb/queryYbdj", {parm: parm}, function (json) {
                if(json.code == 0){
                    yndryb_008.ybdjxx = json.data;
                }else{
                    malert("查询登记信息失败！" + json.msg,'top','defeadted');
                }
            });
        },

        //引入
        enter: function () {
            if (Object.keys(yndryb_008.grxxJson).length === 0) {
                malert("请先读卡", 'top', 'defeadted');
                return;
            }
            if (yndryb_008.grxxJson.yllb == null || yndryb_008.grxxJson.yllb === '' || yndryb_008.grxxJson.yllb === undefined) {
                malert("请选择医疗类别", 'top', 'defeadted');
                return;
            }
            //个人信息
            contextInfo.ybgrxx = yndryb_008.grxxJson;
            // contextInfo.loadBxGrxx();
            contextInfo.json.ybkh = contextInfo.ybgrxx.ybkh;
            contextInfo.json.brxm = contextInfo.ybgrxx.xm;
            contextInfo.json.sfzjhm = contextInfo.ybgrxx.sfzh;
            contextInfo.json.csrq = contextInfo.ybgrxx.csrq;
            if (contextInfo.ybgrxx.rylbmc.indexOf('建档立卡')){
                contextInfo.json.pkhbz = '1';
            }
            contextInfo.setAge();
            contextInfo.$forceUpdate();
            malert("引入成功！");
            popTable.isShow = false;
        },

    },
});
yndryb_008.getbxlb();
// yndryb_008.getUserInfo();
