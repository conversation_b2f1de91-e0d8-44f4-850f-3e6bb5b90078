var userInfo = new Vue({
    el: '#userInfo',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        brItem: [],
        brlistjson:{},//只用于接受请求LIST对象
        brList:[],
        yzList: [],
        jkList:[],
        yzshInfoList:[],//真正的列表
        zyhs:[],
        ksid:null,//科室编码
    },
    mounted: function () {
        this.moun();
        window.addEventListener('storage',function (e) {
            if( e.key == 'wzhl' && e.oldValue !== e.newValue ){
                userInfo.zyhs = [];
                userInfo.moun();
            }
        });
    },
    methods: {
        moun: function () {
            this.brlistjson=JSON.parse(sessionStorage.getItem("wzhl"));
            this.brList =this.brlistjson.brlist;
            this.brItem=this.brList;
            if(this.brItem.nl<7&&this.brItem.brxb=='1'){
                this.brItem.nljd='1';
            }else if(this.brItem.nl<7&&this.brItem.brxb=='2'){
                this.brItem.nljd='2';
            }else if(this.brItem.nl<18&&this.brItem.nl>6&&this.brItem.brxb=='1'){
                this.brItem.nljd='3';
            }else if(this.brItem.nl<18&&this.brItem.nl>6&&this.brItem.brxb=='2'){
                this.brItem.nljd='4';
            }else if(this.brItem.nl<41&&this.brItem.nl>17&&this.brItem.brxb=='1'){
                this.brItem.nljd='5';
            }else if(this.brItem.nl<41&&this.brItem.nl>17&&this.brItem.brxb=='2'){
                this.brItem.nljd='6';
            }else if(this.brItem.nl<66&&this.brItem.nl>40&&this.brItem.brxb=='1'){
                this.brItem.nljd='7';
            }else if(this.brItem.nl<66&&this.brItem.nl>40&&this.brItem.brxb=='2'){
                this.brItem.nljd='8';
            }else if(this.brItem.nl>65&&this.brItem.brxb=='1'){
                this.brItem.nljd='9';
            }else if(this.brItem.nl>65&&this.brItem.brxb=='2'){
                this.brItem.nljd='10';
            }else{
                this.brItem.nljd='11';
            }
            this.ksid=this.brlistjson.ksid;
            for(var i=0;i<this.brList.length;i++){
                var zyh={
                    zyh:this.brList[i].zyh
                };
                this.zyhs.push(zyh);
            }
        }
    }
});

var lr = new Vue({
    el: '#lr',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: true,
        jlContent:{},
        lrsj:'',//录入时间
        qtContent:{},
        add:true,
    },
    mounted: function () {

    },
    methods: {
        edit:function(){
    		if(lr.jlContent.yjbz==true){
    			lr.jlContent.yjbz='1'
    		}else{
    			lr.jlContent.yjbz='0'
    		}
    		lr.jlContent.jlsj=lr.lrsj;
    		lr.jlContent.zyh=userInfo.brItem.zyh;
    		$.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywWzhljl&types=insert&parm='+ JSON.stringify(lr.jlContent), function (json) {
             if (json.a == '0') {
             	malert("保存成功！");
             	lr.jlContent={};
             	lr.qtContent={};
//             	lr.getJlData();
             }else{
            	 malert("保存失败！",'top','defeadted');
             }
         });
    },
        closePage: function () { //关闭本页面
            this.topClosePage(
                'page/hsz/hlyw/yzcl/loading-page/wzhl.html',
                'page/hsz/hlyw/yzcl/yzcl_main.html',
                '医嘱处理');
        },
        updateHzlb: function () { // 刷新患者列表
            var x = parseInt( sessionStorage.getItem('hszHzlbUpdate'));
            x++;
            sessionStorage.setItem( 'hszHzlbUpdate' , x );
        },
    
    remove:function(){
    	 var parm={
    			 id:list.jlxx_list[index].id
    	 };
    	 $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywWzhljl&types=delete&parm='+ JSON.stringify(parm), function (json) {
             if (json.a == '0') {
             	malert("删除成功！");
//             	jlxq.jlContent={};
//             	jlxq.getJlData();
             }else{
            	 malert("删除失败！","top","defeadted");
             }
         });
    },
    	
    	
    }
});

var list = new Vue({
    el: '#list',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isShow: false,
        jlxx_list:[],
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {

        this.getJlData();
    },
    methods: {
    	
        getJlData: function () {
        	var parm={
        		ksrq:this.beginrq,
        		jsrq:this.endrq,
        		zyh:userInfo.brItem.zyh
        	};
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywWzhljl&types=queryAll&parm='+ JSON.stringify(parm), function (json) {
                if (json.a == '0') {
                	list.jlxx_list = json.d.list;
                }
            });
        },
        
        openJl:function(index){
        	lr.jlContent=list.jlxx_list[index];
//        	lr.add=false;
        	list.isShow = false;
        	lr.isShow = true;
            menu.num = 0;
        },

        ylHld: function(){ // 预览病员临床护理单
            var param = {
                brxx: userInfo.brItem,
                hljl: this.jlxx_list
            };
            sessionStorage.setItem('wzhlHld',JSON.stringify(param));
            this.topNewPage("护理单-"+userInfo.brItem.brxm, "page/hsz/hlyw/yzcl/loading-page/wzhl-hld.html");
        }
    	
    }
});

var menu = new Vue({
    el: '#menu',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        num: 0
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        this.beginrq = this.fDate(new Date(), 'date');
        this.endrq = this.fDate(new Date().getTime() + 1000 * 60 * 60 * 24, 'date');
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#timeVal2',
            value: this.beginrq + ' - ' + this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            range: true,
            done: function (value, data) {
                if (value != '') {
                    var A_rq = value.split(' - ');
                    list.beginrq = A_rq[0];
                    list.endrq = A_rq[1];
                } else {
                    list.beginrq = '';
                    list.endrq = '';
                }
                //获取一次列表
                list.getJlData();
            }
        });
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            value: this.fDate(new Date(),'date')+' '+this.fDate(new Date(),'times'),
            done: function (value, data) { //回调方法
                if (value != '') {
                    lr.lrsj = value;
                } else {
                    lr.lrsj = '';
                }
            }
        });
    },
    updated:function () {
        changeWin();
    },
    methods: {
        show: function (index) {
            if( index != this.num ){
                this.num = index;
                if( index == 0 ){
                    lr.isShow = true;
                    list.isShow = false;
                    lr.add=true;
                }else if( index == 1 ){
                    lr.isShow = false;
                    list.isShow = true;
                    list.getJlData();
                    lr.add=true;
                }
            }
        }
    }
});

