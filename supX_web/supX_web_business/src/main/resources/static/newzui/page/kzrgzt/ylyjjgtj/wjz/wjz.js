var countBox = new Vue({
    el: '.count-box',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat, scrollOps],
    data: {
        label:[],
        param: {
            beginrq:null,
            endrq:null
        },
    },
    mounted: function () {
        var myDate=new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-30), 'date');
        this.param.endrq = this.fDate(new Date(), 'date');
        laydate.render({
            elem: '#timeVal',
            eventElem: '.times',
            type: 'date',
            value: this.param.beginrq,
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                this.param.beginrq = value;
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.times1',
            type: 'date',
            value: this.param.endrq,
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                this.param.endrq = value;
            }
        });
    },
    methods: {




        canvas: function () {
            var chart =  Highcharts.chart('container',{
                chart: {
                    type: 'bar'
                },
                title: {
                    text: ''
                },
                subtitle: {
                    text: ''
                },
                gridLineColor: '#eeee',
                legend: {
                    enabled: false
                },
                credits: {
                    enabled: false
                },
                xAxis: {
                    gridLineColor: '#eee',
                    gridLineWidth: 1,
                    crosshair: true,
                    categories: [
                        '心脏停博',
                        '急性心肌缺血',
                        '多源性、RonT型…',
                        '心脏停博',
                        '急性心肌缺血',
                        '多源性、RonT型…',
                        '心脏停博',
                        '急性心肌缺血',
                        '多源性、RonT型…',
                        '心脏停博',
                        '急性心肌缺血',
                        '多源性、RonT型…',
                        '多源性、RonT型…',
                        '多源性、RonT型…',
                        '多源性、RonT型…',
                        '多源性、RonT型…',
                    ],
                    labels: {
                        formatter: function () {
                            var colorArr=['#ff0000','#f3a74f','#04a9f5','#627571'];
                            var color=this.pos<=2?colorArr[this.pos]:colorArr[3];
                            return '<div  style="color:'+color+'">' + this.value + '</div>';
                        }
                    },
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: ''
                    },
                    labels: {
                        style: {
                            color:'#ffffff',
                        },
                        formatter:function (value) {
                            return this.value
                        }
                    }
                },
                tooltip: {
                    backgroundColor: {
                        linearGradient: [0, 0, 0, 10],
                        stops: [
                            [0, 'rgba(0,0,0,0.7)'],
                            [1, 'rgba(0,0,0,0.7)']
                        ]
                    },
                    headerFormat: '<span style="font-size:10px;color: #fff">{point.key}</span><table>',
                    pointFormat: '<tr><td style="color:{series.color};padding:0;color: #fff">{series.name}:</td>' +
                    '<td style="padding:0;color: #fff"><b>{point.y:.1f}</b></td></tr>',
                    footerFormat: '</table>',
                    shared: false,
                    useHTML: false,
                },
                plotOptions: {
                    bar: {
                        borderWidth: 0,
                        pointPadding: 0.15,
                        animation: true,
                    },
                    series: {
                        cursor: 'pointer',
                        states: {
                            hover: {
                                enabled: false,
                            },
                        },
                    },

                },
                series: [{

                    // data: [19,18,17,15,14,9,12,5,12]
                    data: [{
                        name: '心脏停博',
                        y: 19,
                        color: "#ff0000"
                    }, {
                        name: '急性心肌缺血',
                        y: 18,
                        color: "#f3a74f"
                    }, {
                        name: '多源性、RonT型…',
                        y: 17,
                        color: "#04a9f5"
                    }, {
                        name: '心脏停博',
                        y: 15,
                        color: "#627571"
                    }, {
                        name: '急性心肌缺血',
                        y: 14,
                        color: "#627571"
                    }, {
                        name: '多源性、RonT型…',
                        y: 19,
                        color: "#627571"
                    }, {
                        name: '心脏停博',
                        y: 12,
                        color: "#627571"
                    }, {
                        name: '急性心肌缺血',
                        y: 5,
                        color: "#627571"
                    }, {
                        name: '多源性、RonT型…',
                        y: 3,
                        color: "#627571"
                    }, {
                        name: '多源性、RonT型…',
                        y: 3,
                        color: "#627571"
                    },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        }, {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },
                        {
                            name: '多源性、RonT型…',
                            y: 3,
                            color: "#627571"
                        },

                    ]
                }
                ]
            });
            for(var  i in chart.axes[1].ticks){
                this.label.push({x:chart.axes[1].ticks[i].label.xy.x+6+'px',y:chart.axes[1].ticks[i].label.xy.y-40+'px'})
            }
        },
        //危急值报告率
        wjz: function () {
            this.jxwdel = echarts.init(document.getElementById('jxwd'));
            var option = {
                series: [
                    {
                        axisLine: {
                            lineStyle: {
                                width: 15,
                                color: [
                                    [0.25, "#7f8fa4"],
                                    [0.75, "#4caeda"],
                                    [1, "#ff5c63"]
                                ]
                            }
                        },
                            pointer: {
                                length: "70%",
                                width: 3,
                                color:'#5a859b'
                            },
                        axisLabel:{

                            distance:-12,
                                    formatter: function(v){
                                        switch (v+''){
                                            case '0': return '0';
                                            case '20': return '20';
                                            case '50': return '50';
                                            case '70': return '70';
                                            case '100': return '100';
                                            default: return '';
                                        }
                                    },
                        },
                        splitLine: {
                            show: false
                        },
                        axisTick: {
                            splitNumber: 1,
                            length: 10,
                        },
                        radius: "100%",
                        startAngle: 180,
                        endAngle: 0,
                        name: '业务指标',
                        type: 'gauge',
                        detail: {
                        formatter: '{value}%',
                        show: false,
                        },
                        data: [{value: 80}]
                    }
                ]

            };
            setInterval(function () {
                option.series[0].data[0].value = (Math.random() * 100).toFixed(2) - 0;
                countBox.jxwdel.setOption(option, true);
            },2000);

            this.jxwdel.setOption(option);
        },
        //危机值处理及时率
        jscl: function () {
            this.jscldel = echarts.init(document.getElementById('jscl'));
            var option = {
                series: [
                    {
                        axisLine: {
                            lineStyle: {
                                width: 15,
                                color: [
                                    [0.25, "#7f8fa4"],
                                    [0.75, "#4caeda"],
                                    [1, "#ff5c63"]
                                ]
                            }
                        },
                        pointer: {
                            length: "70%",
                            width: 3,
                            color:'#5a859b'
                        },
                        axisLabel:{

                            distance:-12,
                            formatter: function(v){
                                switch (v+''){
                                    case '0': return '0';
                                    case '20': return '20';
                                    case '50': return '50';
                                    case '70': return '70';
                                    case '100': return '100';
                                    default: return '';
                                }
                            },
                        },
                        splitLine: {
                            show: false
                        },
                        axisTick: {
                            splitNumber: 1,
                            length: 10,
                        },
                        radius: "100%",
                        startAngle: 180,
                        endAngle: 0,
                        name: '业务指标',
                        type: 'gauge',
                        detail: {
                            formatter: '{value}%',
                            show: false,
                        },
                        data: [{value: 50}]
                    }
                ]
            };
            this.jscldel.setOption(option);
        }
    }

});
var bottomBox = new Vue({
    el: '.bottom-box',
    mixins: [dic_transform, tableBase, baseFunc, mformat, scrollOps],
    data: {},
    mounted: function () {
        changeWin()
    },
    methods: {
        //未接收
        unabsorbed: function () {
            pop.popShow = true;
        }
    }
});
//危急值复述
var pop = new Vue({
    el: '#pop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        ryxmList: [],
        popContent: {},
    },
    methods: {

        //单选操作
        getRadio: function (num) {
            console.log(num);
            switch (num) {
                //复述选择信息⽆无误则接收报告成功
                case 0:

                    break;
                //选择转科，出院，死亡则返回上⼀一级
                case 1:

                    break;
                case 2:

                    break;
                case 3:

                    break;

                default:
                    break;
            }
        },
        //关闭取消操作
        Popclose: function () {
            this.popShow = false;
        },
        //确定
        popConfirm: function () {
            malert('确定', 'top', 'success')
        },

    }
});

countBox.canvas();
countBox.wjz();
countBox.jscl();


