var page = new Vue({
    el: '#page',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
    	isCheckAll: false,
        isChecked: [],
        jsonList: [],// 列表数据
        jsValue: '',// 检索关键字
    },
    mounted: function () {
        this.getData();
    },
    updated: function () {
        changeWin();
    },
    methods: {
        Ckdetail: function (cfh) {
            brzcList.Show = true;
            brzcList.getData(cfh)
        },
    	deleteMore:function(){
    		var zxlist=[];
            for (var i = 0; i < page.isChecked.length; i++) {
                     if (page.isChecked[i] == true) {
                    	 zxlist.push(JSON.parse(JSON.stringify(page.jsonList[i])));
                     }
                 }
            if (zxlist.length == 0) {
                     malert("请选中您要退费的数据,警告：");
                     return;
                 }
            var json = '{"list":' + JSON.stringify(zxlist) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=zfZtcf', json)
                    .then(function (data) {
                        if (data.body.a == 0) {
                        	page.isChecked=[];
                        	page.isCheckAll=false;
                        	malert("执行成功！");
                        	page.getData();
                        } else {
                            malert(data.body.c+",执行失败");
                        }
                    }, function (error) {
                        console.log(error);
                    });
    	},
        getData: function(){  // 获取列表
            common.delayOpenloading({el:".zui-table-view"});//显示加载动画
            var parm={
            	rows:this.param.rows,
            	page:this.param.page,
            	parm:this.jsValue,
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=queryZtcf&parm=" + JSON.stringify(parm),function (json) {
                common.closeLoading();// 关闭加载动画
                if( json.a == 0 ){
                		page.totlePage = Math.ceil(json.d.total / page.param.rows);
                    	page.jsonList=json.d.list;
                }else {
                    malert("获取数据失败!" + json.c,'top','defeadted');
                }
            });
        },
    }
});
//侧边弹框部分
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        jsonList: [],
        Show:false,
        param: {
            page: 1,
            rows: 100,
            sort: 'mxxh',
            order: 'asc'
        },
    },
    updated: function () {
        changHeight()
    },
    mounted: function () {
    },
    methods: {
        //关闭
        closes: function () {
          this.Show=false
        },
        open: function () {
            this.Show=true;
        },
        getData: function (cfh) {
            if (cfh == undefined) {
                return;
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=queryyppf&dg=" + JSON.stringify(this.param) + '&parm={"cfh":"' + cfh + '"}', function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    brzcList.jsonList = json.d.list;
                } else {
                    malert("查询失败：" + json.c, 'top', 'defeadted')
                }
            });
        },
    }
});
