.text{
    color: #F57B70;
}
.icon-width:before{
    top: 4px;
}
.paddig-left{
    padding-left: 30px;
}

.tong-search .zui-form .zui-inline.padding-left40{
    padding-left: 40px;
}

.tong-search .zui-form .zui-inline.padding-left60{
    padding-left: 60px;
}

.table-width50 .cell-1-1{ /*由于当前tablist列表没有复选框 故不能让main.css中的cell-1-1覆盖掉默认宽度100px!*/
    width: 100px !important;
}

.icon-icon{
    margin: 0 5px;
}
.icon-icon,.icon-icon:before{
    width: 24px;
    height: 24px;
    display: inline-block;
}
.icon-icon:before{
    position: static;
}
.rydj-info-box,
.syjj-info-box{
    padding-top: 10px;
    background-color: #fff;
    margin-bottom: 52px;
}

.zui-inline, .zui-input-inline, .zui-select-inline{
    margin-right: 0;
}

.zui-form-label{
    width: 70px;
    padding: 8px 10px 8px 0;
}
.zui-form .zui-inline{
    padding-left: 70px;
}

.action-bar.fixed{
    right: 10px;
    left: 10px;
    width: auto;
}

.danwei-box .zui-input{
    padding-right: 34px;
}
.danwei-box .danwei{
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -9.5px;
    color:#1abc9c;
}
.icon-dysqb:before{
    color: #757c83;
}
.flex{
    display: flex;
}
.noSpace{
    white-space: nowrap;
    padding-right: 16px;
}
.text-ov{
    overflow: hidden;
    width: 100%;
    white-space: nowrap;
    display: flow-root;
    text-overflow: ellipsis;
}
.brfb p{
    text-align: center;
    width: calc(100%/10);
}
.brfb-title{
    background:#edf2f1;
    border:1px solid #e9eee6;
    height:34px;
    align-items: center;
    border-bottom: none;
}
.brfb-item{
    border-bottom:1px solid #e9eee6;
}
.brfb-list{
    background:#ffffff;
    border:1px solid #e9eee6;
    height:38px;
    align-items: center;
    border-bottom: none;
}