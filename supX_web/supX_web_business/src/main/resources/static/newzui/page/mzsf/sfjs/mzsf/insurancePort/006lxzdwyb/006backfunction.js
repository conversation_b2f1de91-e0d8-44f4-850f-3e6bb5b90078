//有卡身份认证回调
function read_card_backFun(outResult) {
    //将查询出的数据与his住院数据对象合并
    if (outResult) {
        Object.assign(lxzdwyb.lxzdwybBrxxContent, outResult);
        console.log(JSON.stringify(lxzdwyb.lxzdwybBrxxContent));
        if(!lxzdwyb.lxzdwybBrxxContent.grbh){
            lxzdwyb.lxzdwybBrxxContent.grbh = outResult.grbh;
            lxzdwyb.lxzdwybBrxxContent.sbjgbh = outResult.sbjgbh;
        }
        if(outResult.mzdbjbs){
            lxzdwyb.jbList = [];
            var jbArr = outResult.mzdbjbs.split("/");
            for (var i = 0; i < jbArr.length; i++) {
                var jb = jbArr[i].split("#m");
                var jbC = {
                    jbmc:jb[0],
                    jbbm:jb[1],
                };
                lxzdwyb.jbList.push(jbC);
                if(i==0){
                    lxzdwyb.jbContent.jbbm = jb[0];
                    Vue.set(lxzdwyb.lxzdwybBrxxContent, 'jbbm', jb[1]);
                    Vue.set(lxzdwyb.lxzdwybBrxxContent, 'jbmc', jb[0]);
                }
            }
            // Vue.set(lxzdwyb.lxzdwybBrxxContent, 'jbbm', outResult.mzdbjbs);
            // Vue.set(lxzdwyb.lxzdwybBrxxContent, 'jbmc', outResult.mzdbjbs);
            // Vue.set(lxzdwyb.jbContent, 'jbbm', outResult.mzdbjbs);
            // Vue.set(lxzdwyb.jbContent, 'jbmc', outResult.mzdbjbs);
        }
        lxzdwyb.$forceUpdate();
        malert("身份信息获取成功!");
    } else {
        malert("身份信息获取失败,未获取到相关医保人员信息!", "top", "defeadted");
        return;
    }
}

//无卡身份认证回调
function query_person_info_backFun(outResult) {
    //将查询出的数据与his住院数据对象合并
    if (outResult) {
        Object.assign(lxzdwyb.lxzdwybBrxxContent, outResult);
        malert("身份信息获取成功!");
    } else {
        malert("身份信息获取失败,未获取到相关医保人员信息!", "top", "defeadted");
        return;
    }
}

function init_mz_backFun(outResult){
    Object.assign(lxzdwyb.lxzdwybBrxxContent, outResult);
    $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + rightVue.lxzBxurl + "&bxlbbm=" + rightVue.lxzBxlbbm + "&types=mzyw&method=mzdj&parm="
        + encodeURIComponent(JSON.stringify(lxzdwyb.lxzdwybBrxxContent)),
        function (data) {
            if (data.a == '0') {
                rightVue.lxzdwybContent = lxzdwyb.lxzdwybBrxxContent;
                malert("门诊登记成功!");
                popTable.isShow = false;
                lxzdwyb.ifClick = true;
            } else {
                malert(data.c, "top", "defeadted");
                return;
            }
        });
}

//费用上传
function put_fymx_backFun(outResult){
    lxzdwyb.requestParameters = {};
    lxzdwyb.requestParameters.settle_mz_pre = {};
    socket.send(JSON.stringify(lxzdwyb.requestParameters));
}

function settle_mz_pre_backFun(outResult){
    rightVue.yjsContentLxzdwyb = Object.assign({},rightVue.yjsContentLxzdwyb, JSON.parse(outResult));
    rightVue.zxlshSn = rightVue.yjsContentLxzdwyb.jshid;
}

function settle_mz_real_backFun(outResult){
    outResult = JSON.parse(outResult);
    //这里调打印
    /*var parm = {};
    parm.print_dj = {
        jshid:outResult.jshid,
        djlx:'FP',
    };
    socket.send(JSON.stringify(parm.print_dj));*/
    outResult.ghxh = lxzdwyb.lxzdwybBrxxContent.mzghbh;
    rightVue.yjsContentLxzdwyb={}
    //rightVue.yjsContentLxzdwyb = Object.assign({},rightVue.yjsContentLxzdwyb, outResult);
    $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + rightVue.lxzBxurl + "&bxlbbm=" + rightVue.lxzBxlbbm + "&types=mzyw&method=mzjs&parm="
        + JSON.stringify(outResult),function (json) {
        if (json.a == '0') {
            malert(json.c);
            popCenter1.successSaveAndPrint();
            common.closeLoading();
        } else {
            malert(json.c,"top","defeadted");
            common.closeLoading();
        }
    });
}
function destroy_mz_backFun(){
    malert("医保取消结算！");
    // $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + rightVue.lxzBxurl + "&bxlbbm=" + rightVue.lxzBxlbbm + "&types=mzyw&method=qxmzjs&parm="
    //     + JSON.stringify(lxzdwyb.yjsContentLxzdwyb),function (json) {
    //     if (json.a == '0') {
    //         malert(json.c);
    //     } else {
    //         malert(json.c,"top","defeadted");
    //     }
    // });
}
