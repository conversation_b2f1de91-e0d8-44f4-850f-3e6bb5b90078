<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>历史库存</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="lskc.css" rel="stylesheet">
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b">导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药房</label>
                    <div class="zui-input-inline wh122 margin-l-7">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'yfmc'" :index_val="'yfbm'" :val="param.yfbm"
                                      :name="'param.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline flex-container flex-align-c  margin-f-l5">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh200 text-indent20" v-model="param.beginrq" placeholder="请选择申请开始日期" id="timeVal"/>
                        <span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " v-model="param.endrq" placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="goToPage(1)" type="text" v-model="param.parm" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10"  z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品编码</span></div></th>
                    <th><div class="zui-table-cell cell-xxl text-left"><span>药品名称</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>药品规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品种类</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body"  @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.ypbm"><i v-text="item.ypbm" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i></div>
                         </div>

                    </td>
                    <td>
                        <div class="zui-table-cell  cell-xxl text-left text-over-2" v-text="item.ypmc"></div>
                    </td>
                    <td><div class="zui-table-cell cell-l ">
                        <div  v-text="item.ypgg" ></div>
                    </div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kcsl"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.kcsl,2)">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.scph"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cdmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbl"></div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>

<script src="lskc.js"></script>
</body>

</html>
