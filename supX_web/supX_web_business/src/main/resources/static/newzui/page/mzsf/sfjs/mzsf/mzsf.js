var isAddOrUpdate = 0; // 判断是新增还是修改默认为新增（0为新增，1为修改）
var isnum = 0; // 获取当前操作的是第几个对象
var tableInfo = new Vue({
    el: '#tableInfo',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer, getDataObj],
    data: {
        csqxContent: {},
        loadName: null,
        userName: null,
        ylkh: null,
        printList: [],
        currentPrintId: null,
        currentPrintName: "",
        ksbm: ""
    },
    methods: {
        history() {
            toDayList.isFold = true;
            toDayList.getData();
        },



        printPage: function () {
            $('#printPage').load('printPage/lzPrint.html');
        },
        printBcdPage: function () {
            if (tableInfo.csqxContent.N05001200341 == '2') {
                $('#printPage').load('printPage/jhbcd.html');
            } else {
                rightVue.doPrint()
            }
        },
        mzjs: function () {


            if (!rightVue.fzContent.mzksmc) {
                malert("开单科室忘记选了", 'right', 'defeadted');
                return false;
            }
            if (!rightVue.fzContent.mzys) {
                malert("开单医生忘记选了", 'right', 'defeadted');
                return false;
            }
			rightVue.lsjsxx = [];
            rightVue.mzjs()
        },
        loadbx: function () {
			console.log(this.fDate(new Date(), 'datetime'))
            //判断不允许报销诊断判断
            if (!tableInfo.csqxContent.N05001200339 == false && !rightVue.mzjbxxContent.jbmc == false
                && rightVue.mzjbxxContent.jbmc.indexOf(tableInfo.csqxContent.N05001200339) >= 0) {
                malert('门诊诊断【' + tableInfo.csqxContent.N05001200339 + '】不允许保险报销！', 'right', 'defeadted');
                return;
            }
            switch (rightVue.fzContent.bxjk) {

                case 'B07'://成都银海医保 @by huihui
					// this.loadName = 'insurancePort/014cdyhyb/014cdyhyb';
					   this.loadName = 'insurancePort/014cdyhybgb/014cdyhyb';
                    break;
                default:
                    malert("未开通此保险接口！", 'right', 'defeadted');
                    return;
            }
            popTable.isShow = true;
            console.log(rightVue.fzContent.rybxlbbm, this.loadName);
            loadPage(this.loadName);
            popTable.$nextTick(function () {
                loadPage(tableInfo.loadName);
            })
        },
        getData: function () {
            $("#ykth").focus();
            rightVue.brxxContent = {};
            rightVue.mzjbxxContent = {};
            rightVue.brfyjsonList = []; //清空未收费信息
            rightVue.brfyContent = {};
            rightVue.fzContent = {};
            rightVue.yndrybContent = {};
            //清空农合对象
            popCenter1.jsjlContent = {
                codeContent: '', ybfyhj: 0
            };
            rightVue.yjsContent = {};
            rightVue.nhsjContent2 = {};
            this.$forceUpdate()
            this.userName = JSON.parse(sessionStorage.getItem("userName" + userId));
        },

        printResultChange: function (val) {
            this.currentPrintId = val[0];
            this.currentPrintName = val[4];
            this.setCookie("mzsf-printindex", val[0]);
            this.setCookie("mzsf-printname", val[4]);
        },
        printBc: function () {
            if (gz_001.bcdxxContent != null) {
                //帆软打印
                var reportlets = "";
                if (window.top.J_tabLeft.obj.frprintver == "3") {
                    reportlets = "[{reportlet: 'fpdy%2Fgznh%2Fmzbcd.cpt',yljgbm:'" + jgbm + "',outpid:'" + gz_001.bcdxxContent.outpid + "'}]";
                } else {
                    reportlets = "[{reportlet: 'fpdy/gznh/mzbcd.cpt',yljgbm:'" + jgbm + "',outpid:'" + gz_001.bcdxxContent.outpid + "'}]";
                }

                var MzbcdPrint = null;
                var MzbcdSize = null;
                window.top.J_tabLeft.csqxparm.csbm = "N010024008";
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                    if (json.a == 0) {
                        console.log(json.d);
                        if (json.d != null && json.d != undefined && json.d.length > 0) {
                            MzbcdPrint = json.d[0].csz;
                            MzbcdSize = json.d[0].cszmc;
                        }
                        if (!window.top.J_tabLeft.obj.FRorWindow) {
                            if (!FrPrint(reportlets, MzbcdPrint, MzbcdSize)) {
                            }
                        } else {
                            tableInfo.printBcdPage()
                        }

                    }
                });
            }
        }
    },
});
var rightVue = new Vue({
    el: '.rightVueAll',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer, getDataObj],
    components: {
        'search-table': searchTable
    },
    data: {
        zdbx: true,
        printContentt: {},
        yjsContentZyyhyb: {},
        bjyss: 0,
        fyjehj: 0,
        yhFyzh: 0,
        bjyys: 0,
        bjzbje: 0,
        liValue: '',
        time: '',
        pjxxContent: {}, // 票据基本信息
        jsxxJsonList: [], // 结算记录集合
        srhj: null,
        yjhj: null,
        brxxContent: {}, // 专门用于下拉检索
        searchCon1: [],
        brfyContent: {},
        mzjbxxContent: {}, // 门诊费用基本信息
        ypContent: {},
        fzContent: {},
        brfyjsonList: [],
        ghksList: [], // 开单科室下拉框集合
        glghksList: [], // 过滤之后的开单科室
        mzysList: [], // 门诊医生下拉框集合
        glmzysList: [], // 过滤之后的门诊医生
        hsksList: [], // 核算科室下拉框集合
        brfbList: [], // 病人费别
        bxlbList: [], // 保险类别
        fyhjAll: 0, // 动态费用金额
        sjjeAll: 0, //动态实际金额
        yhhjAll: 0, //动态优惠金额
        selSearch: -1,
        selSearch1: -1,
        searchCon: [],
        yzhm: true,
        fydj: true,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran1: {},
        them_tran: {
            'fylx': dic_transform.data.fylx_tran,
            'nbtclb': dic_transform.data.istrue_tran,
            'sfgd': dic_transform.data.istrue_tran,
            'ypfy': dic_transform.data.ypfy_tran,
            'zhfy': dic_transform.data.sfzhfy_tran
        },
        them1: {
            '姓名': 'brxm',
            '性别': 'brxbmc',
            '年龄': 'brnl',
            '挂号序号': 'ghxh',
            '挂号种类': 'ghzl',
            '挂号日期': 'ghrq',
            '接诊医生': 'jzysxm',
            '挂号科室': 'ghksmc'
        },
        them: {
            '类型': 'zhfy',
            '费用编码': 'mxfybm',
            '明细费用名称': 'mxfymc',
            '拼音代码': 'pydm',
            '费用规格': 'fygg',
            '费用类别': 'fylbmc',
            '费用类型': 'fylx',
            '费用统筹类别': 'tclbmc',
            '农合报价': 'nbtclb',
            '费用单价': 'fydj',
            '执行科室': 'zxksmc',
            '是否固定': 'sfgd',
            '药品费用': 'ypfy',
            '优惠比例': 'yhbl'
        },
        ylkh: '',
        bxcw: false,
        sum: 0,
        //----------农合医保对象（用于打印及最后金额计算）
        yjsContent: {}, //预结算数据2(贵州移动农合)
        yjsContent2: {},
        nhsjContent1: {}, //农合参合人员信息
        nhsjContent2: {}, //农合窗口填写信息
        snybryContent: {}, //遂宁清华同方保存对象
        snybcsContent: {}, //遂宁医保参数对象
        gzjqnhContent: {}, //贵州晶奇农合保存对象
        gzyhybContent: {}, //贵州银海医保保存对象
        lxzdwybContent: {}, //临夏州地玮医保
        yjsContentGzyhyb: {}, //贵州银海医保预结算对象
        yjsContentSnqhtf: {}, //遂宁清华同方医保结算对象
        yjsContentGzjqnh: {}, //贵州晶奇农合结算对象
        outpid: null, //贵州移动农合补偿号
        zxlshSn: null,
        jrsrhj: 0,
        gzyhybInit: false,
        flag: false,
        abyhybInit: false, // @yqq 阿坝州银海医保初始化标志
        abyhybContent: {}, // @yqq 阿坝州银海医保保存对象
        yjsContentAbyhyb: {}, // @yqq 阿坝医保银海医保预结算对象
        yndrybInit: false, //云南东软医保初始化标记
        yndrybContent: {}, //云南东软医保保存对象
        yjsContentYndryb: {},//云南东软医保预结算对象
        jsContentYndryb: {},//云南东软医保结算对象
        yjsContentLxzdwyb: {},//临夏州地玮医保预结算对象
        gsnhContentJson: {},  //甘肃农合预结算对象

        lxzdwybYjsFineshed: false,
        lxzBxurl: null,
        gztyzfCsContent: {},
        lxzBxlbbm: null,
        //printContent:{}, //贵州农合补偿单打印
        gznhObj: {
            jzmzName: '无',
            outpCompensateCost: '0',
            chroCompensateCost: '0'
        }, //@yqq 用来承载显示详情数据的对象
        gznhType: false,
        mzyjsYinHaiResult: null,//门诊预结算返回值
        mzyjsExceptionResult: [],//门诊预结算失败集合
		gbjsxx:[],
		sfgzList:{
			'0':'否',
			'1':'是'
		},
		rysfgz:'1',
		lsjsxx:[],
        jzids:[],
        xtjs:'',
        yhResult:[],
        med_type:'',
        sbjyxx : null,
        gjjsxx : null,
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
    },
    watch: {
        'brfyContent': {
            deep: true,
            handler: function (newValue, oldValue) {
                immediate: true
                return this.sum = newValue.fydj * parseInt(newValue.fysl)

            }
        },
    },
    computed: {

        //以保险接口为标准，保险类别编码不作为标准
        a: function () {
            for (var i = 0; i < this.bxlbList.length; i++) {
                if (rightVue.fzContent.rybxlbbm != '01') {
                    if (rightVue.fzContent.rybxlbbm == this.bxlbList[i].bxlbbm) {
                        rightVue.fzContent.bxjk = this.bxlbList[i].bxjk;
						console.log(rightVue.fzContent.bxjk)
                        return false;
                    }
                } else {
                    rightVue.fzContent.bxjk = null;
                }
            }
        },

        lxnhListener: function () {
            if (this.gsnhContentJson.hsbcje != undefined && this.gsnhContentJson.hsbcje != '') {
                popCenter1.jsjlContent.ybkzf = this.fDec(this.gsnhContentJson.hsbcje, 2);
                rightVue.finalMzjs();
                popCenter1.isShow = true;
                rightVue.lxzdwybYjsFineshed = true;
                popCenter1.$nextTick(function () {
                    common.closeLoading();
                })
            }
        },

        //解决临夏州地玮异步处理问题
        lxzdwybListener: function () {
            if (JSON.stringify(this.yjsContentLxzdwyb) != "{}") {
                var ybkzf = 0;
                if (this.yjsContentLxzdwyb) {
                    if (this.yjsContentLxzdwyb.grzhzf) {
                        ybkzf += this.yjsContentLxzdwyb.grzhzf;
                    }
                    if (this.yjsContentLxzdwyb.ylbzje) {
                        ybkzf += this.yjsContentLxzdwyb.ylbzje;
                    }
                    if (this.yjsContentLxzdwyb.tczf) {
                        ybkzf += this.yjsContentLxzdwyb.tczf;
                    }
                    if (this.yjsContentLxzdwyb.dezf) {
                        ybkzf += this.yjsContentLxzdwyb.dezf;
                    }
                    if (this.yjsContentLxzdwyb.desybx) {
                        ybkzf += this.yjsContentLxzdwyb.desybx;
                    }
                    if (this.yjsContentLxzdwyb.gwybz) {
                        ybkzf += this.yjsContentLxzdwyb.gwybz;
                    }
                    if (this.yjsContentLxzdwyb.czlz) {
                        ybkzf += this.yjsContentLxzdwyb.czlz;
                    }
                    if (this.yjsContentLxzdwyb.zhzf) {
                        ybkzf += this.yjsContentLxzdwyb.zhzf;
                    }
                    if (this.yjsContentLxzdwyb.yljmje) {
                        ybkzf += this.yjsContentLxzdwyb.yljmje;
                    }
                    if (this.yjsContentLxzdwyb.qttczf) {
                        ybkzf += this.yjsContentLxzdwyb.qttczf;
                    }
                    if (this.yjsContentLxzdwyb.qzjbzhzf) {
                        ybkzf += this.yjsContentLxzdwyb.qzjbzhzf;
                    }
                    if (this.yjsContentLxzdwyb.dbbzje) {
                        ybkzf += this.yjsContentLxzdwyb.dbbzje;
                    }
                }
                popCenter1.jsjlContent.ybkzf = this.fDec(ybkzf, 2);
                rightVue.finalMzjs();
                rightVue.lxzdwybYjsFineshed = true;
                popCenter1.$nextTick(function () {
                    common.closeLoading();
                })
            }
        },
        bchj: function () {
            if (this.brfyjsonList.length) {
                this.fyhjAll = 0;
                this.sjjeAll = 0;
                var yzhmHj = 0, trNums = 0;

                this.brfyjsonList = this.filterID(this.brfyjsonList, 'yzhm');
                                this.$forceUpdate();
                for (var i = 0; i < this.brfyjsonList.length; i++) {
                    this.fyhjAll += this.brfyjsonList[i].fyje + parseFloat(this.brfyjsonList[i].yhje);
                    this.sjjeAll += this.brfyjsonList[i].fyje;
                    if (this.brfyjsonList[i]['class'] == 'sameStart' || this.brfyjsonList[i]['class'] == 'same') {
                        yzhmHj = this.MathAdd(this.brfyjsonList[i].fyje , yzhmHj);
                        ++trNums;
                    } else if (this.brfyjsonList[i]['class'] == 'sameEnd') {
                        if (trNums % 2 == 0) {
                            ++trNums;
                        }
                        yzhmHj = this.MathAdd(this.brfyjsonList[i].fyje , yzhmHj);
                        this.brfyjsonList[i]['yzhmHj'] = this.fDec(yzhmHj, 2);
                        this.brfyjsonList[i]['trNums'] = trNums;
                        yzhmHj = 0;
                        trNums = 0;
                    }
                }
                this.yhhjAll = this.floatSub(this.fyhjAll ,this.sjjeAll)
                return this.fDec(this.sjjeAll, 2);
            }
        }
    },
    filters: {
        dragStyle: function (val) {
            var style = {};
            style.top = '-' + (val / 2 * 40 - 10) + 'px'
            return style
        }
    },
    methods: {
        resultChange_zxks: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            Vue.set(this[val[2][0]], 'zxksmc', val[4]);
        },
        selecIsNo: function (event) {
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter' || event.code == 'Digit4') {
                if (tableInfo.csqxContent.N05001200333 == '1' && event.target.value.length == 66) {
                    if (event.target.value.indexOf(":") != -1) {
                        event.target.value = event.target.value.substr(0, event.target.value.lastIndexOf(":"));
                    }
                }
                if (event.target.value.length === 64) {
                    if (event.target.value != '') {
                        this.searching1(false, 'ghxh', event.target.value)
                        return;
                    }
                }
                // 35 36 位  公司就诊卡 // 18位 身份证  //19位  银行卡
                //  || event.target.value.length === 18 || event.target.value.length === 19
                if (event.target.value.length === 35 || event.target.value.length === 36) {
                    this.loadYkt(event.target.value, event)
                    // $(".selectGroup").hide();
                } else if (event.target.value.length > 0) {
                    // this.searching1(false, 'ghxh', event.target.value)
                }
            }
        },
        loadYkt: function (value, event) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var host = document.location.hostname == 'localhost' ? '*************' : document.location.hostname
            $.get("http://" + host + ":18888/" + value, function (kh) {
                if (kh == undefined || kh == null || kh == "0" || kh == "-1") {
                    malert("卡解密失败,请重试！", 'right', 'defeadted');
                } else {
                    tableInfo.getData()
                    rightVue.ylkh = kh;
                    var str_param = {
                        parm: kh,
                        page: rightVue.page.page,
                        rows: rightVue.page.rows,
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=brgh' + '&dg=' + JSON.stringify(str_param), function (data) {
                        if (data.d.list.length > 0) {
                            // rightVue.brxxContent.ghxh = data.d.list[0].ghxh;
                            for (var i = 0; i < data.d.list.length; i++) {
                                var ghrq = rightVue.fDate(data.d.list[i].ghrq, "date");
                                Vue.set(data.d.list[i], 'ghrq', ghrq);
                            }
                            // 如果查询结果只有1条则直接请求后台查询门诊费用信息
                            if (data.d.list.length == 1) {
                                rightVue.getData(data.d.list[0].ghxh);
                                Vue.set(rightVue.brxxContent, 'text', data.d.list[0].ghxh);
                                $(".selectGroup").hide();
                                return true;
                            } else {
                                rightVue.searchCon1 = data.d.list;
                            }
                        } else {
                            //malert('没找到患者信息，请重试','top','defeadted')
                        }
                        rightVue.page.total = data.d.total;
                        rightVue.selSearch1 = 0;
                        if (data.d.list.length > 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return false;
                        }
                    });
                }
            }).error(function (xhr, errorText, errorType) {
                malert("卡解密失败,请重试！", 'right', 'defeadted');
            });
        },
        getData: function (jsxx) {
            rightVue.clear();
            if (jsxx == null || jsxx == "") {
                malert("检索内容不能为空", 'right', 'defeadted');
                return;
            }
            var parmGhxx = {
                ghxh: jsxx
            };
            // 请求后台查询门诊挂号详情
            $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=queryByMzsf&parm=" + JSON.stringify(parmGhxx), function (json) {
                if (json.a == 0 && json.d) {
                    rightVue.flag = true;
                    // 判断查询出来的数据大于1条则需要进行选择
                    rightVue.mzjbxxContent = JSON.parse(JSON.stringify(json.d[0]));
                    console.log(rightVue.mzjbxxContent);
                    rightVue.fzContent['ryghxh'] = rightVue.mzjbxxContent.ghxh;
                    Vue.set(rightVue.fzContent, 'brxm', rightVue.mzjbxxContent.brxm);
                    rightVue.fzContent['brid'] = rightVue.mzjbxxContent.brid;
                    rightVue.fzContent['mzys'] = rightVue.mzjbxxContent.jzys || rightVue.mzjbxxContent.cfys;
                    rightVue.fzContent['mzks'] = rightVue.mzjbxxContent.ghks;
                    rightVue.fzContent['mzksmc'] = rightVue.mzjbxxContent.ghksmc;
                    rightVue.mzysList = jsonFilter(rightVue.glmzysList, "ksbm", rightVue.fzContent.mzks);
                    rightVue.fzContent['ryfbbm'] = rightVue.mzjbxxContent.fbbm;
                    rightVue.fzContent['rybxlbbm'] = rightVue.mzjbxxContent.bxlbbm;
                    popCenter1.bxlbbm = rightVue.mzjbxxContent.bxlbbm;
                    rightVue.fzContent['sfyh'] = rightVue.mzjbxxContent.sfyh;
                    // 门诊收费核算科室生成方案0-按费用项目核算科室生存，1-为空时，默认医生科室
                    if (tableInfo.csqxContent.cs00500100104 == '1') {
                        if (rightVue.fzContent['zxks'] == undefined || rightVue.fzContent['zxks'] == null || rightVue.fzContent['zxks'] == "") {
                            rightVue.fzContent['zxks'] = rightVue.mzjbxxContent.ghks;
                        }
                    }
                    rightVue.brfyjsonList = []; //清空未收费信息
                    // 请求后台查询未收费门诊费用
                    var parmWsfFy = {
                        ryghxh: jsxx
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryByWsf&parm=" + JSON.stringify(parmWsfFy), function (json) {
                        if (json.a == 0 && json.d) {
                            for (var i = 0; i < json.d.length; i++) {
                                json.d[i].xzxm = false;
                            }
                            rightVue.brfyjsonList = json.d;
                            //此处将费用金额累加起来
                            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                                if (rightVue.fzContent['sfyh'] == '1') {

                                } else {
                                    rightVue.brfyjsonList[i].yhje = 0.00;
                                }
                                rightVue.fyhjAll += rightVue.brfyjsonList[i].fyje + rightVue.brfyjsonList[i].yhje;
                                rightVue.sjjeAll += rightVue.brfyjsonList[i].fyje;
                            }
                            rightVue.fyhjAll = rightVue.fyhjAll.toFixed(2);
                            rightVue.yhhjAll = rightVue.fyhjAll - rightVue.sjjeAll;
                        } else {
                            malert("查询失败!" + json.c, 'right', 'defeadted');
                        }
                    });
                    // 检索之后设置焦点
                    $("#mxxm").focus();
                    //贵州晶奇农合用,不影响其他业务
                    rightVue.yjsContentGzjqnh.grbh = null;
                } else {
                    malert("查询失败：" + json.c, 'right', 'defeadted');
                }
            });
        },
        doPrint: function () {
            var head = {
                operCode: "S27",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var body = {
                outpId: rightVue.outpid,

            };
            var param = {
                head: head,
                body: body
            };
            var self = this;
            var str_param = JSON.stringify(param);
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    if (json.a == 0) {
                        rightVue.ydnhMzjsd = JSON.parse(json.e).data[0];

                        rightVue.printContentt.rylx = self.nhrysx_tran[rightVue.nhsjContent1.memberPro];
                        rightVue.printContentt.bcrq = rightVue.fDate(new Date(), 'date');
                        rightVue.printContentt.mzlx = self.mznh_tran[rightVue.nhsjContent2.rylx];
                        rightVue.printContentt.lxdh = rightVue.nhsjContent2.lxdh;
                        rightVue.printContentt.jtdz = rightVue.nhsjContent1.areaName;
                        rightVue.printContentt.brxm = rightVue.nhsjContent1.memberName;
                        rightVue.printContentt.memberid = rightVue.nhsjContent1.memberId;
                        rightVue.printContentt.ybkh = rightVue.nhsjContent1.medicalNo;
                        rightVue.printContentt.sfzh = rightVue.nhsjContent1.idcard;
                        rightVue.printContentt.brxb = self.brxb_tran[rightVue.nhsjContent1.memberSex];
                        rightVue.printContentt.brnl = rightVue.mzjbxxContent.brnl;
                        rightVue.printContentt.outpid = rightVue.outpid;
                        rightVue.printContentt.fyje = rightVue.fDec(rightVue.yjsContent2.totalcost, 2);
                        rightVue.printContentt.zfje = rightVue.fDec(rightVue.yjsContent2.totalcost - rightVue.yjsContent2.compensatecost - rightVue.yjsContent2.salvaclcost - rightVue.yjsContent2.salvayfcost - rightVue.yjsContent2.salvajscost - rightVue.yjsContent2.salvafpcost - rightVue.yjsContent2.civilcost, 2);
                        rightVue.printContentt.bcfy = rightVue.fDec(rightVue.yjsContent2.compensatecost + rightVue.yjsContent2.salvaclcost + rightVue.yjsContent2.salvayfcost + rightVue.yjsContent2.salvajscost + rightVue.yjsContent2.salvafpcost + rightVue.yjsContent2.civilcost, 2);
                        rightVue.printContentt.jbmc = rightVue.nhsjContent2.jbmc;
                        rightVue.printContentt.cljz = rightVue.fDec(rightVue.yjsContent2.salvaclcost, 2);
                        rightVue.printContentt.mzyf = rightVue.fDec(rightVue.yjsContent2.salvayfcost, 2);
                        rightVue.printContentt.jsfz = rightVue.fDec(rightVue.yjsContent2.salvajscost, 2);
                        rightVue.printContentt.ylfp = rightVue.fDec(rightVue.yjsContent2.salvafpcost, 2);
                        rightVue.printContentt.mzyljz = rightVue.fDec(rightVue.yjsContent2.civilcost, 2);
                        rightVue.printContentt.bnfy = rightVue.fDec(rightVue.yjsContent2.insurancecost, 2);
                        rightVue.printContentt.dysj = rightVue.fDate(new Date(), 'YY');
                        rightVue.printContentt.czyxm = sessionStorage.getItem('userName' + userId);
                        if (!rightVue.printContentt.cljz) {
                            rightVue.printContentt.cljz = 0;
                        }
                        if (!rightVue.printContentt.mzyf) {
                            rightVue.printContentt.mzyf = 0;
                        }
                        if (!rightVue.printContentt.jsfz) {
                            rightVue.printContentt.jsfz = 0;
                        }
                        if (!rightVue.printContentt.ylfp) {
                            rightVue.printContentt.ylfp = 0;
                        }
                        if (!rightVue.printContentt.mzyljz) {
                            rightVue.printContentt.mzyljz = 0;
                        }
                        var json = {};
                        //慢性病
                        if (rightVue.ydnhMzjsd.compensateType == 12) {
                            json = {repname: '慢性病门诊'}
                            rightVue.printContentt.compensateCost = rightVue.ydnhMzjsd.compensateCost
                        } else {
                            json = {repname: '门诊报销'}
                        }

                        // 查询打印模板
                        //var json = {repname: '门诊报销'}; //慢性病门诊 门诊报销
                        self.updatedAjax("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                            // 清除打印区域
                            rightVue.clearArea(json.d[0]);
                            // 绘制模板的canvas
                            rightVue.drawList = JSON.parse(json.d[0]['canvas']);
                            rightVue.creatCanvas();
                            rightVue.reDraw();
                            // 为打印前生成数据
                            rightVue.printContent(rightVue.printContentt);
                            // 开始打印
                            window.print();
                        });
                    } else {

                    }
                });
        },
        // 当输入值后才触发
        selectOne1: function (item) {
            if (item == null) {
                this.page.page++;
                this.searching1(true, 'ghxh', this.brxxContent['ghxh']);
            } else {
                this.brxxContent = item;
                // Vue.set(rightVue.brxxContent, 'text', this.brxxContent['ghxh']);
                rightVue.getData(this.brxxContent['ghxh']);
                $(".selectGroup").hide();
            }
        },
        // 记账项目检索
        changeDown1: function (event) {
            this.inputUpDown(event, this.searchCon1, "selSearch1");
            // 选中之后的回调操作
            if (event.code == 'Enter' || event.keyCode == 13 || event.code == 'NumpadEnter') {
                // 35 36 位  公司就诊卡 // 18位 身份证  //19位  银行卡
                // && event.target.value.length != 18 && event.target.value.length != 19

                if (tableInfo.csqxContent.N05001200333 == '1' && event.target.value.length == 66) {
                    if (event.target.value.indexOf(":") != -1) {
                        event.target.value = event.target.value.substr(0, event.target.value.lastIndexOf(":"));
                    }
                }

                if (event.target.value.length != 35 && event.target.value.length != 36) {
                    if (event.target.value != '') {
                        this.searching1(false, 'ghxh', event.target.value)
                        return;
                    }
                }

                if (this['searchCon1'][this.selSearch1] == undefined) return;
                this.brxxContent = JSON.parse(JSON.stringify(this.searchCon1[this.selSearch1]))
                // Vue.set(rightVue.brxxContent, 'text', this.brxxContent['ghxh']);
                rightVue.getData(this.brxxContent['ghxh']);
                this.searchCon1 = [];
                Vue.set(this, 'selSearch1', -1);
                $(".selectGroup").hide();
                this.nextFocus(event);
            }
        },
        searching1: function (add, type, val) {
            tableInfo.getData()
            this.flag = true;
            //this.brxxContent[type] = '';
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.page.parm = val;
            var date = new Date();
            //var beginrq = date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + (date.getDate()-parseInt(tableInfo.csqxContent.N05001200202)) + " 23:59:59";
            var beginrq = date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + (date.getDate()) + " 00:00:00";
            var endrq = getTodayDateEnd();
            if (val.length >= 6) {//输入卡号大于等于6位，可查询今日之前的数据
                beginrq = null;
                endrq = null;
            }
            var str_param = {
                parm: this.page.parm,
                page: this.page.page,
                rows: this.page.rows,
                beginrq: beginrq,
                endrq: endrq,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=brgh' + '&dg=' + JSON.stringify(str_param), function (data) {
                if (data.d.list.length > 0) {
                    for (var i = 0; i < data.d.list.length; i++) {
                        var ghrq = rightVue.fDate(data.d.list[i].ghrq, "date");
                        Vue.set(data.d.list[i], 'ghrq', ghrq);
                    }
                    // 如果查询结果只有1条则直接请求后台查询门诊费用信息
                    if (data.d.list.length == 1) {
                        rightVue.getData(data.d.list[0].ghxh);
                        Vue.set(rightVue.brxxContent, 'ghxh', data.d.list[0].ghxh);
                        $(".selectGroup").hide();
                        return true;
                    }
                    if (add) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            //                            var ghrq = rightVue.fDate(data.d.list[i].ghrq, "date");
                            //                            Vue.set(data.d.list[i], 'ghrq', ghrq);
                            rightVue.searchCon1.push(data.d.list[i]);
                        }
                    } else {
                        for (var i = 0; i < data.d.list.length; i++) {
                            //                            var ghrq = rightVue.fDate(data.d.list[i].ghrq, "date");
                            //                            Vue.set(data.d.list[i], 'ghrq', ghrq);
                        }
                        rightVue.searchCon1 = data.d.list;
                    }
                } else {
                    malert('没找到患者信息，请重试', 'right', 'defeadted')
                }
                rightVue.page.total = data.d.total;
                // rightVue.selSearch1 = 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                    rightVue.flag = false
                    return false;
                }
            });
        },

        // 集合下拉框选中之后的回调
        resultMzsfChange: function (val) {
            var types = val[2][val[2].length - 1]; // 方便获取到是什么类型
            // 左边挂号基本信息根据输入的门诊医生检索开单科室
            if (val == 'brxm') {
                if (tableInfo.csqxContent.cs00500100113 == '0') { // 是否按科室来过滤医生--0 否，1-是
                    this.nextFocus(event, 2);
                } else {
                    this.nextFocus(event, 1);
                }
                isTwo = true;
            }
            if (types == 'mzys') {
                Vue.set(rightVue.fzContent, 'mzys', val[0]);
                Vue.set(rightVue.fzContent, 'mzysxm', val[4]);
                if (tableInfo.csqxContent.cs00500100113 == '0') { // 是否按科室来过滤医生--0 否，1-是
                    var mzks = rightVue.listGetName(rightVue.mzysList, rightVue.fzContent.mzys, 'rybm', 'ksbm');
                    Vue.set(rightVue.fzContent, 'mzks', mzks);
                }
                if (tableInfo.csqxContent.cs00500100122) {
                    Vue.set(rightVue.fzContent, 'ryfbbm', tableInfo.csqxContent.cs00500100122)
                    Vue.set(rightVue.fzContent, 'rybxlbbm', tableInfo.csqxContent.cs00500100123)
                    this.nextFocus(event, 3);
                } else {
                    this.nextFocus(event);
                    return false
                }

                this.nextFocus(event, 3);
            }
            if (types == 'mzks') {
                Vue.set(rightVue.fzContent, 'mzks', val[0]);
                Vue.set(rightVue.fzContent, 'mzksmc', val[4]);
                // 判断一下如果核算科室没有值则用开单科室
                // 门诊收费核算科室生成方案0-按费用项目核算科室生存，1-为空时，默认医生科室
                if (tableInfo.csqxContent.cs00500100104 == '1') {
                    if (rightVue.fzContent.zxks == undefined || rightVue.fzContent.zxks == null || rightVue.fzContent.zxks == '') {
                        Vue.set(rightVue.fzContent, 'zxks', val[0]);
                        Vue.set(rightVue.fzContent, 'zxksmc', val[4]);
                    }
                }
                rightVue.mzysList = []; // 先清空
                if (tableInfo.csqxContent.cs00500100113 == '1') { // 是否按科室来过滤医生--0不是
                    Vue.set(rightVue.brfyContent, 'mzys', null);
                    Vue.set(rightVue.brfyContent, 'mzysxm', null);
                    // 赋值过滤后的
                    rightVue.mzysList = jsonFilter(rightVue.glmzysList, "ksbm", rightVue.fzContent.mzks);
                }
                this.nextFocus(event);
            }
            if (types == 'ryfbbm') { //费别
                Vue.set(rightVue.fzContent, 'ryfbbm', val[0]);
                Vue.set(rightVue.fzContent, 'ryfbmc', val[4]);
                for (var i = 0; i < rightVue.brfbList.length; i++) {
                    if (val[0] == rightVue.brfbList[i]['fbbm']) {
                        Vue.set(rightVue.fzContent, 'rybxlbbm', rightVue.brfbList[i]['bxlbbm']);
                        Vue.set(popCenter1, 'bxlbbm', rightVue.brfbList[i]['bxlbbm'])
                        this.nextFocus(event, 2);
                        isTwo = true;
                    }
                }
            }
            if (types == 'bxlbbm') {
                Vue.set(popCenter1, 'bxlbbm', val[0]);
            }
            //方便更新试图（添加set。get方法）
            rightVue.fzContent = Object.assign({}, rightVue.fzContent);
        },
        // 双击进行修改
        edit: function (num, yzhm, yzlx) {
            if ((yzhm == undefined || yzhm == null || yzhm == "") && yzlx != '2') {
                if (num == null) {
                    malert("请选中你要修改的数据", 'right', 'defeadted');
                    return false;
                }
                rightVue.brfyContent = JSON.parse(JSON.stringify(rightVue.brfyjsonList[num]));
                Vue.set(rightVue.brfyContent, 'text', rightVue.brfyjsonList[num].mxfyxmmc);
                Vue.set(rightVue.brfyContent, 'mxfymc', rightVue.brfyjsonList[num].mxfyxmmc);
                isAddOrUpdate = 1; // 判断是新增还是修改1为修改0为新增
                isnum = num; // 判断操作的是第几个对象
                $("#mxxm").attr("disabled", true);
            }
        },
        // 删除一个对象
        // 669405
        // 52079.34
        // 215.81
        remove: function (num) {
            if ('1' == tableInfo.csqxContent.N05001200342) {
                malert('收费员无权限删除费用，请联系医生！', 'right', 'defeadted');
                return;
            }
            //删除处方药品费用时，删除同一处方对应的所有费用
            if (rightVue.brfyjsonList[num].yzlx == '2') {
                var yzhm = rightVue.brfyjsonList[num].yzhm
                var tmpList = [];
                for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                    if (yzhm == rightVue.brfyjsonList[i].yzhm) {
                        //
                    } else {
                        tmpList.push(rightVue.brfyjsonList[i]);
                    }
                }
                rightVue.brfyjsonList = tmpList;
            } else {
                //单独删除附加费是否作废
                if (rightVue.brfyjsonList[num].yzhm) {
                    this.brfyjsonList = this.spliceNewFun(this.brfyjsonList, 'yzhm', rightVue.brfyjsonList[num].yzhm)
                    return false;
                }
                if (rightVue.brfyjsonList[num].yzlx == '9' && rightVue.brfyjsonList[num].fyjlid != undefined) {
                    if (tableInfo.csqxContent.cs05001200326 == '1') {
                        //作废该费用
                        var parm = {
                            "fyjlid": rightVue.brfyjsonList[num].fyjlid
                        };
                        $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=delete_fjf&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0) {
                                malert('作废成功！', 'right', 'success');
                            } else {
                                malert(json.c, 'right', 'defeadted');
                            }
                        });
                    }
                }
                rightVue.brfyjsonList.splice(num, 1);
                $('#tplink').remove()
            }
        },
        // 记账项目检索
        changeDown: function (event, type, value) {
            if (type == 'text') {
                if (this['searchCon'][this.selSearch] == undefined) {
                    return false;
                }
                this.keyCodeFunction(event, 'brfyContent', 'searchCon');
            }
            // 选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'text') {
                    this.brfyContent['text'] = this.brfyContent['mxfymc'];
                    //根据参数判断是否需要收费室划价
                    if (tableInfo.csqxContent.cs00500100103 == "1") {
                        //调用处方划价窗口
                        if (rightVue.brfyContent.ypfy == '1') {
                            //                			tableInfo.cfhj();
                        }
                    }

                    // 选中之后赋值操作
                    if (!rightVue.brfyContent.fysl) {
                        rightVue.brfyContent.fysl = "1";
                    }
                    // 门诊收费核算科室生成方案0-按费用项目核算科室生存，1-为空时，默认医生科室
                    if (tableInfo.csqxContent.cs00500100104 == '0') {
                        this.fzContent.zxks = this.brfyContent.hsks;
                        rightVue.fzContent['zxksmc'] = rightVue.listGetName(rightVue.hsksList, rightVue.fzContent['zxks'], 'ksbm', 'ksmc');
                    } else {
                        if (rightVue.brfyContent.hsks == null) {
                            this.fzContent.zxks = this.fzContent.mzks;
                        } else {
                            this.fzContent.zxks = rightVue.brfyContent.hsks;
                        }
                        rightVue.fzContent['zxksmc'] = rightVue.listGetName(rightVue.hsksList, rightVue.fzContent['zxks'], 'ksbm', 'ksmc');
                    }

                    // 判断是否是属于药品费用0-非药品，1--药品
                    if (this.brfyContent.ypfy == '1') {
                        this.fydj = true
                        this.yzhm = false
                        // 设置焦点
                        this.$nextTick(function () {
                            this.nextSelect(event, 2)
                            // $("#yzhm").focus();
                        })
                    } else {
                        this.yzhm = true
                        // 设置焦点
                        if (this.brfyContent.sfgd == '1' || this.brfyContent.zhfy == '1') { //如果该项目固定
                            this.fydj = true
                            // $('#fysl').focus().select()
                        } else {
                            this.fydj = false
                            this.$nextTick(function () {
                                this.nextSelect(event, 2)
                            })
                        }
                    }
                    if (this.brfyContent.sfgd == '1') {
                        this.fydj = true
                        // $("#fysl").focus().select();
                    }
                    this.nextSelect(event, 2)
                } else if (type == "fydj") {
                    if (parseFloat(value) > 0) {
                        this.nextSelect(event, 1)
                    } else {
                        malert('请输入大于0的整数', 'right', 'defeadted')
                    }
                    // $('#fysl').focus().select()
                } else if (type == "fysl" && value != "0") {
                    this.searchDjqr(); // 调用保存
                } else if (type == "yzhm") {
                    if (this.brfyContent.yzhm != null && this.brfyContent.yzhm != "" && this.brfyContent.yzhm.length < 6) {
                        Vue.set(this.brfyContent, 'yzhm', this.toghxh(this.brfyContent.yzhm, "3"));
                        $("#yzhm").val(rightVue.brfyContent['yzhm']);
                    }
                    // 判断如果已经查询过改处方费用信息将不再查询
                    if (rightVue.brfyjsonList.length > 0) {
                        for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                            if (rightVue.brfyContent.yzhm == rightVue.brfyjsonList[i].yzhm) {
                                malert("该处方费用信息已加载", 'right', 'defeadted');
                                // $("#yzhm").focus().select();
                                return false;
                            }
                        }
                        rightVue.searchDjqr(true); // 调用保存
                    } else {
                        rightVue.searchDjqr(true); // 调用保存
                    }
                }
            }
        },
        // 当输入值后才触发
        change: function (add, val) {
            rightVue.brfyContent['text'] = val;
            rightVue.brfyContent['isType'] = '1';
            if (!add) this.page.page = 1; //  设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (rightVue.brfyContent['text'] == undefined || rightVue.brfyContent['text'] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = rightVue.brfyContent['text'];
            }
            var str_param = {
                parm: this.page.parm,
                page: this.page.page,
                rows: this.page.rows,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=queryJzxm' +
                '&dg=' + JSON.stringify(str_param),
                function (data) {
                    if (add) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            rightVue.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        rightVue.searchCon = data.d.list;
                    }
                    rightVue.page.total = data.d.total;
                    rightVue.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
        },
        // 鼠标双击（记账项目）
        selectOne: function (item) {
            if (item == null) { //  如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++; //  设置当前页号
                this.change(true, rightVue.brfyContent['text']); //  传参表示请求下一页,不传就表示请求第一页
            } else {
                this.brfyContent = item;
                //根据参数判断是否需要收费室划价
                if (tableInfo.csqxContent.cs00500100103 == "1") {
                    //调用处方划价窗口
                    if (rightVue.brfyContent.ypfy == '1') {
                        //            			tableInfo.cfhj();
                    }
                }
                if (this.brfyContent.fysl == null || this.brfyContent.fysl == '' || this.brfyContent.fysl == undefined) {
                    this.brfyContent.fysl = "1";
                }
                // 门诊收费核算科室生成方案0-按费用项目核算科室生存，1-为空时，默认医生科室
                if (tableInfo.csqxContent.cs00500100104 == '0') {
                    this.fzContent.zxks = this.brfyContent.hsks;
                    rightVue.fzContent['zxksmc'] = rightVue.listGetName(rightVue.hsksList, rightVue.fzContent['zxks'], 'ksbm', 'ksmc');
                } else {
                    if (rightVue.brfyContent.hsks == null) {
                        this.fzContent.zxks = this.fzContent.mzks;
                    } else {
                        this.fzContent.zxks = rightVue.brfyContent.hsks;
                    }
                    rightVue.fzContent['zxksmc'] = rightVue.listGetName(rightVue.hsksList, rightVue.fzContent['zxks'], 'ksbm', 'ksmc');
                }
                this.brfyContent['text'] = this.brfyContent['mxfymc'];
                // 判断是否是属于药品费用0-非药品，1--药品
                if (this.brfyContent.ypfy == '1') {
                    this.yzhm = false
                    this.fydj = true
                    // 设置焦点
                    this.$nextTick(function () {
                        $("#yzhm").focus().select();
                    })
                } else {
                    this.yzhm = true
                    // 设置焦点
                    if (this.brfyContent.sfgd == '1') { //如果该项目固定
                        this.$nextTick(function () {
                            this.fydj = true
                            $("#fysl").focus().select();
                        })
                    } else {
                        this.fydj = false
                        this.$nextTick(function () {
                            $("#fydj").focus().select();
                        })
                    }
                }
                $(".selectGroup").hide();
            }
        },
        // 费用录入点击确认按钮进行费用新增行
        searchDjqr: function (falg) {
            console.log(rightVue.brfyContent);
            if (isAddOrUpdate == 0) { // 新增行
                if (rightVue.brfyContent.zhfy == '1') { // 如果是组合费用则先查询出明细费用后循环赋值
                    var jsons = {
                        mxfybm: rightVue.brfyContent.mxfybm
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=queryZhfy' + '&json=' + JSON.stringify(jsons), function (data) {
                        if (data.a == 0) {
                            if (data.d.list.length > 0) {
                                for (var i = 0; i < data.d.list.length; i++) {

                                    // 进行赋值操作
                                    // tableInfo.fzcz();
                                    var json = jQuery.extend({}, rightVue.brfyContent);
                                    json.zhfybh = rightVue.brfyContent.mxfybm;
                                    json.zhfymc = rightVue.brfyContent.mxfymc;
                                    json.mxfyxmbm = data.d.list[i].mxfybm;
                                    json.mxfyxmmc = data.d.list[i].mxfymc;
                                    json.fylb = data.d.list[i].lbbm;
                                    json.fylbmc = data.d.list[i].fylbmc;
                                    json.fydj = data.d.list[i].fydj;
                                    json.yhbl = data.d.list[i].yhbl;
                                    json.yzfl = data.d.list[i].ffylb;
                                    json.yzlx = '0';
                                    json.fysl = data.d.list[i].sl * rightVue.brfyContent.fysl;
                                    if (rightVue.fzContent['sfyh'] == '1') {
                                        json.yhje = parseFloat(rightVue.brfyContent.fydj) * data.d.list[i].sl * parseFloat(rightVue.brfyContent.fysl) * parseFloat(rightVue.brfyContent.yhbl);
                                    } else {
                                        json.yhje = 0.00;
                                    }

                                    json.fyje = parseFloat(data.d.list[i].fydj) * data.d.list[i].sl * parseFloat(rightVue.brfyContent.fysl) - parseFloat(json.yhje);
                                    // 门诊收费核算科室生成方案0-按费用项目核算科室生存，1-为空时，默认医生科室
                                    if (data.d.list[i].hsks != null) {
                                        json.zxks = data.d.list[i].hsks;
                                    } else {
                                        json.zxks = rightVue.fzContent['zxks'];
                                    }
                                    json.zxksmc = rightVue.listGetName(rightVue.hsksList, json.zxks, 'ksbm', 'ksmc');

                                    // 数据验证
                                    if (!rightVue.bdyz()) {
                                        return false;
                                    }
                                    json.xzxm = true;
                                    json.isType = '1';
                                    rightVue.brfyjsonList.unshift(json);
                                }
                                rightVue.brfyContent = {};
                            } else {
                                malert("未查到相关记录,查询失败：", 'right', 'defeadted');
                            }
                        } else {
                            malert(data.c + ",查询失败：", 'right', 'defeadted');
                            return false;
                        }
                        $("#mxxm").focus();
                    });
                } else { // 不是组合费用
                    if (rightVue.brfyContent.ypfy == '1') { // 属于药品// 根据输入的处方号（医嘱号码）请求后台查询
                        if (rightVue.brfyContent.yzhm != null && rightVue.brfyContent.yzhm != '') {
                            $.getJSON('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryWsfcfOne' + '&cfh=' + rightVue.brfyContent.yzhm, function (data) {
                                if (data.a == 0) {
                                    if (data.d.list.length > 0) {
                                        for (var i = 0; i < data.d.list.length; i++) {
                                            var json = jQuery.extend({}, rightVue.brfyContent);
                                            rightVue.brfyContent.mxfymc = data.d.list[i].mxfymc;
                                            json.fysl = 1; // 如果是处方费用费用数量默认为1
                                            json.mxfyxmbm = data.d.list[i].mxfybm;
                                            json.mxfyxmmc = data.d.list[i].mxfymc;
                                            json.fylb = data.d.list[i].lbbm;
                                            json.fylbmc = data.d.list[i].lbmc;
                                            json.fydj = data.d.list[i].je;
                                            json.fyje = data.d.list[i].je;
                                            json.yzhm = data.d.list[i].cfh;
                                            json.yzfl = data.d.list[i].ffylb;
                                            json.brxm = data.d.list[i].brxm;
                                            json.yzlx = '2';
                                            json.isType = '1';
                                            // 门诊收费核算科室生成方案0-按费用项目核算科室生存，1-为空时，默认医生科室
                                            if (data.d.list[i].hsks != null) {
                                                json.zxks = data.d.list[i].hsks;
                                            } else {
                                                json.zxks = rightVue.fzContent['mzks'];
                                            }
                                            json.zxksmc = rightVue.listGetName(rightVue.hsksList, json.zxks, 'ksbm', 'ksmc');
                                            // 数据验证
                                            // if (!rightVue.bdyz()) {
                                            //     return;
                                            // }
                                            // 不匹配则弹出框进行选择
                                            if (data.d.list[0].brks != rightVue.fzContent['mzks'] || data.d.list[0].cfys != rightVue.fzContent['mzys']) {
                                                // 弹出选择框(只在第一次弹出)
                                                //                                                if (popChoose.mzOrCf == null) {
                                                //                                                    popChoose.chooseContent = true;
                                                //                                                }
                                            }
                                            json.xzxm = true;
                                            rightVue.brfyjsonList.unshift(json);
                                        }
                                        //此处将费用金额累加起来
                                        rightVue.fyhjAll = 0;
                                        rightVue.sjjeAll = 0;
                                        for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                                            rightVue.fyhjAll += rightVue.brfyjsonList[i].fyje + parseFloat(rightVue.brfyjsonList[i].yhje);
                                            rightVue.sjjeAll += rightVue.brfyjsonList[i].fyje;
                                        }
                                        rightVue.yjhjAll = rightVue.fyhjAll - rightVue.sjjeAll;
                                        rightVue.brfyContent = {};
                                    } else {
                                        malert("未查到相关记录,查询失败：", 'right', 'defeadted');
                                    }
                                } else {
                                    malert(data.c + ",查询失败：", 'right', 'defeadted');
                                }
                                // 设置焦点
                                if (JSON.stringify(rightVue.brfyContent) == '{}') {
                                    $("#mxxm").focus();
                                } else {
                                    $("#yzhm").focus().select();
                                }
                            });
                        } else {
                            malert("请输入处方号", 'right', 'defeadted');
                            return false;
                        }
                    } else { // 医嘱号码为空时直接赋值
                        rightVue.brfyContent.mxfyxmbm = rightVue.brfyContent.mxfybm;
                        rightVue.brfyContent.mxfyxmmc = rightVue.brfyContent.mxfymc;
                        rightVue.brfyContent.fylb = rightVue.brfyContent.lbbm;
                        rightVue.brfyContent.fylbmc = rightVue.brfyContent.fylbmc;
                        rightVue.brfyContent.yzfl = rightVue.brfyContent.ffylb;
                        rightVue.brfyContent.fydj = rightVue.brfyContent.fydj;
                        if (rightVue.fzContent['sfyh'] == '1') {
                            rightVue.brfyContent.yhje = parseFloat(rightVue.brfyContent.yhbl) * parseFloat(rightVue.brfyContent.fydj) * parseFloat(rightVue.brfyContent.fysl);
                        } else {
                            rightVue.brfyContent.yhje = 0.00;
                        }

                        rightVue.brfyContent.fyje = parseFloat(rightVue.brfyContent.fydj) * parseFloat(this.brfyContent.fysl) - parseFloat(rightVue.brfyContent.yhje);
                        rightVue.brfyContent.zxks = rightVue.fzContent.zxks;
                        if (rightVue.fzContent.zxks != null) {
                            rightVue.brfyContent.zxksmc = rightVue.listGetName(rightVue.hsksList, rightVue.fzContent['zxks'], 'ksbm', 'ksmc');
                        } else {
                            rightVue.brfyContent.zxksmc = rightVue.listGetName(rightVue.hsksList, rightVue.fzContent['mzks'], 'ksbm', 'ksmc');
                        }
                        rightVue.brfyContent.yzlx = '1';
                        // tableInfo.fzcz();
                        // 数据验证
                        if (!rightVue.bdyz()) {
                            return;
                        }
                        rightVue.brfyContent.xzxm = true;
                        rightVue.brfyjsonList.unshift(rightVue.brfyContent);
                        rightVue.brfyContent = {};
                        $("#mxxm").focus();
                    }
                    isAddOrUpdate = 0; // 判断是修改还是新增
                    isnum = 0;
                }
            } else { // 修改行
                // 修改时禁用费用项目按钮
                if (rightVue.brfyContent.fydj == null || rightVue.brfyContent.fydj == '') {
                    rightVue.brfyContent.fydj = '0';
                }
                if (rightVue.brfyContent.fysl == null || rightVue.brfyContent.fysl == '') {
                    rightVue.brfyContent.fysl = '0';
                }
                if (rightVue.brfyContent.yhje == null || rightVue.brfyContent.yhje == '') {
                    rightVue.brfyContent.yhje = '0';
                }
                if (rightVue.fzContent['sfyh'] == '1') {

                } else {
                    rightVue.brfyContent.yhje = 0.00;
                }
                rightVue.brfyContent.fyje = parseFloat(rightVue.brfyContent.fydj) * parseFloat(rightVue.brfyContent.fysl) - parseFloat(rightVue.brfyContent.yhje);
                rightVue.fzContent['zxksmc'] = rightVue.listGetName(rightVue.hsksList, rightVue.fzContent['mzks'], 'ksbm', 'ksmc');
                rightVue.brfyContent.yzlx = '1';
                // 数据验证
                if (!tableInfo.bdyz()) {
                    return;
                }
                rightVue.brfyjsonList[isnum] = rightVue.brfyContent;
                rightVue.brfyContent = {};
                isAddOrUpdate = 0;
                isnum = 0;
                $("#mxxm").attr('disabled', false);
                $("#mxxm").focus();
            }
        },
        // 表单验证
        bdyz: function () {
            var i = 0;
            var errString = "";
            if (!rightVue.fzContent.brxm) {
                i++;
                errString += "</br>【病人姓名】不能为空！";
            }
            if (!rightVue.fzContent.mzks) {
                i++;
                errString += "</br>【开单科室】不能为空！";
            }
            if (!rightVue.fzContent.mzys) {
                i++;
                errString += "</br>【门诊医生】不能为空！";
            }
            if (!rightVue.fzContent.ryfbbm) {
                i++;
                errString += "</br>【费别】不能为空！";
            }
            if (!rightVue.fzContent.rybxlbbm) {
                i++;
                errString += "</br>【保险】不能为空！";
            }
            if (rightVue.brfyContent.ypfy != '1') {
                if (rightVue.brfyContent.fysl == null || rightVue.brfyContent.fysl <= 0 || rightVue.brfyContent.fysl == "") {
                    i++;
                    errString += "</br>【数量】不能为空！";
                }
                var xs = /^(\d|[0-9]\d+)(\.\d+)?$/; // 大于0的整数小数
                if (!xs.test(rightVue.brfyContent['fydj'])) {
                    i++;
                    errString += "</br>【单价】为大于0的数！";
                }
            }
            if (i > 0) {
                malert(errString + ",加载失败：", 'right', 'defeadted');
                return false;
            } else {
                return true;
            }
        },
        doPop: function (index) {
            if (!rightVue.brfyjsonList[index].yzhm) {
                return;
            }
            var param = {
                page: 1,
                rows: 20000
            };
            //如果有分页请用jq的api,一般情况下避免冲突请用vue-resource,具体参照index.js的事例
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCfhj&types=queryyppf&dg=" + JSON.stringify(param) + '&parm={"cfh":"' + rightVue.brfyjsonList[index].yzhm + '"}', function (json) {
                if (json.a == 0) {
                    popPf.jsonList = json.d.list;
                    popPf.ishow = true;
                } else {
                    malert("该费用无配方明细！", 'right', 'defeadted');
                }
            });

        },
        clear: function () {
            //清空农合对象
            popCenter1.jsjlContent = {ybfyhj: 0};
            rightVue.yjsContent = {};
            rightVue.yjsContentGzyhyb = {};
            rightVue.yjsContentZyyhyb = {};
            rightVue.nhsjContent1 = {};
            rightVue.nhsjContent2 = {};
            rightVue.mzjbxxContent = {};
            rightVue.brfyContent = {};
            rightVue.fzContent = {};
            rightVue.snybryContent = {};
            rightVue.brfyjsonList = [];
            popCenter1.cfkfxmList = [];
            rightVue.fyhjAll = 0;
            rightVue.sjjeAll = 0;
            if (typeof (rightVue.gzyhybContent) != "undefined") {
                rightVue.gzyhybContent = null;
            }
        },
        mzjs: function () {
            rightVue.outpid = "";
            popCenter1.jsjlContent = {
                codeContent: '',
                ybfyhj: 0,
                ylkzf: 0,
                useCoupon: false, // 复选框默认未选中
                couponAmount: 0,// 添加默认值，防止 undefined 绑定
                couponPrice:0,// 添加默认值，防止 undefined 绑定
            };
            rightVue.yjsContent = {};
            rightVue.yjsContentGzyhyb = {};
            rightVue.yjsContentZyyhyb = {};
            rightVue.yjsOontentZyyhyb = {};
            popCenter1.cfkfxmList = [];
            //每次保存设为保险都可报销
            rightVue.zdbx = true;
            if (!tableInfo.csqxContent.N05001200339 == false && !rightVue.mzjbxxContent.jbmc == false
                && rightVue.mzjbxxContent.jbmc.indexOf(tableInfo.csqxContent.N05001200339) >= 0) {
                rightVue.zdbx = false;
            }
            //不允许报销诊断不走保险
            if (rightVue.fzContent.bxjk != null && rightVue.zdbx) {
                switch (rightVue.fzContent.bxjk) {

                    case "B07":
                        //gzyhybContent 医保信息
                        if (!rightVue.isNotNullOrEmpty(rightVue.gzyhybContent)) {
                            malert("此病人为保险病人，请点击保险按钮进行保险业务操作！", 'right', 'defeadted');
                            return;
                        }
                        break;
                    default:
                        malert("暂为开放此保险接口！", 'right', 'defeadted');
                        break;
                }
            }
            // 传入 需要修改的门诊费用集合、添加的门诊费用集合、添加的结算记录对象、修改的病人挂号对象
            $.ajaxSettings.async = false;
            var yhhj = "0";
            var zfyExist = false;
            var ffyExist = false;
            //根据质控科室分配identity ，后面分票用
            let zxksMap = [];
            rightVue.fyjehj = 0;
            if (rightVue.brfyjsonList.length > 0) {
                for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                    if (!rightVue.brfyjsonList[i].zxks) {
                        rightVue.brfyjsonList[i].zxks = rightVue.fzContent.mzks
                        //return false;
                    }
                    // 基本信息在这里赋值(区最新的值)
                    if (rightVue.fzContent['ryghxh']) {
                        rightVue.brfyjsonList[i]['ryghxh'] = rightVue.fzContent['ryghxh'];
                    }
                    if (rightVue.fzContent['brid']) {
                        rightVue.brfyjsonList[i]['brid'] = rightVue.fzContent['brid'];
                    }
                    rightVue.brfyjsonList[i]['brxm'] = rightVue.fzContent['brxm'];
                    if (!rightVue.brfyjsonList[i].mzys) {
                        rightVue.brfyjsonList[i]['mzys'] = rightVue.fzContent['mzys'];
                    }
                    if (!rightVue.brfyjsonList[i].mzysxm) {
                        rightVue.brfyjsonList[i]['mzysxm'] = rightVue.listGetName(rightVue.mzysList, rightVue.fzContent['mzys'], 'rybm', 'ryxm');
                    }
                    rightVue.brfyjsonList[i]['mzks'] = rightVue.fzContent['mzks'];
                    rightVue.brfyjsonList[i]['mzksmc'] = rightVue.listGetName(rightVue.ghksList, rightVue.fzContent['mzks'], 'ksbm', 'ksmc');
                    rightVue.brfyjsonList[i]['ryfbbm'] = rightVue.fzContent['ryfbbm'];
                    rightVue.brfyjsonList[i]['ryfbmc'] = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['ryfbbm'], 'fbbm', 'fbmc');
                    rightVue.brfyjsonList[i]['rybxlbbm'] = rightVue.fzContent['rybxlbbm'];
                    rightVue.brfyjsonList[i]['rybxlbmc'] = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['rybxlbbm'], 'bxlbbm', 'bxlbmc');
                    // 判断有没有处方号
                    var cffy = {};
                    if (rightVue.brfyjsonList[i]['yzhm'] != null && rightVue.brfyjsonList[i]['yzhm'] != '') {
                        cffy['cfh'] = rightVue.brfyjsonList[i].yzhm;
                        popCenter1.cfkfxmList.push(cffy); // 给处方费用信息集合赋值
                    } else {
                        let zkks = rightVue.brfyjsonList[i].zxks;
                        let identity = (i + 1);
                        let ix = zxksMap.findIndex(x => x.zkks == zkks);
                        if (ix != -1) {
                            identity = zxksMap[ix].identity;
                        } else {
                            zxksMap.push({
                                identity: identity,
                                zkks: zkks
                            })
                        }
                        //没有医嘱号，手动录入的费用，自动生成一个identity 属性
                        rightVue.brfyjsonList[i]['identity'] = identity;
                    }
                    if (rightVue.brfyjsonList[i].yhje == null || rightVue.brfyjsonList[i].yhje == "") {
                        rightVue.brfyjsonList[i].yhje = '0';
                    }
                    yhhj = rightVue.MathAdd(yhhj,rightVue.brfyjsonList[i].yhje); // 循环相加获取优惠金额合计
                    rightVue.fyjehj = rightVue.MathAdd(rightVue.fyjehj,rightVue.brfyjsonList[i].fyje); // 循环获取实收金额（此费用金额已经是减了优惠金额的）
                    if (rightVue.brfyjsonList[i].fyje && rightVue.brfyjsonList[i].fyje > 0) {
                        zfyExist = true;
                    }
                    if (rightVue.brfyjsonList[i].fyje && rightVue.brfyjsonList[i].fyje < 0) {
                        ffyExist = true;
                    }
                }
            } else {
                malert("请选择费用项目!", 'right', 'defeadted');
                return;
            }
            rightVue.fyjehj = rightVue.fyjehj.toFixed(2);
            if (tableInfo.csqxContent.N05001200336 == '1') {//限制正负一起收费0-否 1-是
                if (zfyExist && ffyExist) {
                    malert("请将正负记录分开收费！", "top", "defeadted");
                    return;
                }
            }
            let ryfbbmName = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['ryfbbm'], 'fbbm', 'fbmc');
            if (rightVue.fzContent.bxjk != null && rightVue.zdbx) {
                switch (rightVue.fzContent.bxjk) {
                    case 'B07':

                        rightVue.yjsContentGzyhyb.ifok = false;
                        if (rightVue.gzyhybContent.grbh != null) {

                            rightVue.yjsContentGzyhyb.ifok = this.jzyyMzjs();
                            //在这儿要进行更改
							if(rightVue.sbjyxx){
								common.openConfirm("当前交易有现金支付，是否进行共济？", function () {
																		popTable1.isShow = true;
									let loadName = "insurancePort/014cdyhybgb/014cdyhybgj";
									$('#loadPage1').load(loadName+'.html');
									popTable1.$nextTick(function () {
										$('#loadPage1').load(loadName+'.html');
									})
								}, function(){
									rightVue.jejsfs();
								})
							}else{
								rightVue.jejsfs();
							}
							// rightVue.jejsfs();
                        } else {
                            malert("此病人为保险病人，请点击保险按钮进行保险业务操作！", 'right', 'defeadted');
                            return
                        }
                        break;
                    default:
                        malert("暂为开放此保险接口！", 'right', 'defeadted');
                        return;
                        break;
                }
            }else{
				rightVue.jejsfs();
			}

        },

		jejsfs:function(){
			if (!rightVue.bxcw) {
			    if (!popCenter1.jsjlContent.qtzf) {
			        popCenter1.jsjlContent.qtzf = '0';
			    }

			    //重新计算费用合计&优惠金额
			    yhhj = 0;
			    rightVue.fyjehj = 0;
			    rightVue.brfyjsonList.forEach((x, i) => {
			        if (rightVue.brfyjsonList[i].yhje == null || rightVue.brfyjsonList[i].yhje == "") {
			            rightVue.brfyjsonList[i].yhje = '0';
			        }
			        yhhj = rightVue.MathAdd(yhhj, rightVue.brfyjsonList[i].yhje); // 循环相加获取优惠金额合计
			        rightVue.fyjehj = rightVue.MathAdd(rightVue.fyjehj, rightVue.brfyjsonList[i].fyje);// 循环获取实收金额（此费用金额已经是减了优惠金额
			    });

			    // 对结算记录进行赋值操作
			    popCenter1.jsjlContent.brxm = rightVue.fzContent['brxm'];
			    popCenter1.jsjlContent.rybxlbbm = rightVue.fzContent.rybxlbbm; // 当前费别类型

			    popCenter1.jsjlContent.ysje = rightVue.fDec(rightVue.MathAdd(parseFloat(yhhj),parseFloat(rightVue.fyjehj)), 2);
			    popCenter1.jsjlContent.yhhj = rightVue.fDec(yhhj, 2);
			    popCenter1.jsjlContent.fyhj = rightVue.fDec(rightVue.fyjehj, 2);
			    popCenter1.jsjlContent.insureCost = rightVue.yjsContent.insureCost;
			    popCenter1.jsjlContent.salvaJSCost = rightVue.yjsContent.salvaJSCost;
			    popCenter1.jsjlContent.civilCost = rightVue.yjsContent.civilCost;
			    popCenter1.jsjlContent.totalcost = rightVue.yjsContent.totalcost;
			    popCenter1.jsjlContent.medicineCost = rightVue.yjsContent.medicineCost;

			    if (!popCenter1.jsjlContent.ybkzf) {
			        popCenter1.jsjlContent.ybkzf = '0';
			    }

			    if (!popCenter1.jsjlContent.ybtczf) {
			        popCenter1.jsjlContent.ybtczf = '0';
			    }

			    popCenter1.isShow = true;
			    popCenter1.$nextTick(function () {
			        if (!popCenter1.zflxList.length) {
			            popCenter1.getPrintName();
			            popCenter1.GetZflxData(); // 加载出支付类型下拉框
			        }
			        popCenter1.jsjlContent.zflxbm = popCenter1.zflxList[0].zflxbm;
			        rightVue.finalMzjs();
			        // 弹出框显示
			    })
			} else {
			    malert("保险内部错误,不能完成结算，请联系工程师！", 'right', 'defeadted');
			}
		},


        getErrorMassage: function (item) {
            let error = [];
            item.forEach((x) => {
                let msg = x.response.astr_appmasg
                if (error.indexOf(msg) < 0) {
                    error.push(msg);
                }
            })
            let message = error.join('\n');
            return message;
        },
		xtch : function(yhResult,data1,med_type,mtxx,xtjs,jyxx,ryxx,jzids){
			let trt_dcla_detl_sn = xtjs.split(",")
			for (let i = 0; i <jzids.length ; i++) {
				let tpxx = yhResult[i];
				let chrg_bchno = tpxx[0].yke134
				if(!chrg_bchno){
					chrg_bchno = new Date().getTime();
				}
				if(tpxx[0].sfcf =='1'){
					//门特每个药的开始 结束日期
					for (let lyjmt = 0; lyjmt < tpxx.length; lyjmt++) {
						let param_S3701 = window.insuranceGbUtils.fparam_S3701(tpxx[lyjmt],jzids[i],med_type,mtxx,xtjs,jyxx,ryxx,chrg_bchno,'0',tpxx[lyjmt].trt_dcla_detl_sn);
						let data3701 = window.insuranceGbUtils.call1("S3701A",param_S3701,ryxx.insuplc_admdvs,cd_014.sfyd);
						if(!data3701){
							return false;
						}
					}

				}

                //费用数据
                let param_05 = window.insuranceGbUtils.fparam_2205(jzids[i],jyxx,'0000');

                let data2205 = window.insuranceGbUtils.call1("2205",param_05,ryxx.insuplc_admdvs,cd_014.sfyd);

                if(!data2205){

                    return false;
                }

                // 取消挂号
                let param_02 = window.insuranceGbUtils.fparam_2202(jzids[i],jyxx);
                window.insuranceGbUtils.call1("2202",param_02,ryxx.insuplc_admdvs,cd_014.sfyd)
			}



			return false;
		},
        jzyyMzjs: function () {

            rightVue.sbjyxx = null;
            rightVue.gjjsxx = null;
			rightVue.gbjsxx = [];
			let ryxx = JSON.parse(sessionStorage.getItem('hzybxx'));
			let ret1 =  window.insuranceGbUtils.call('2207A',{
				card_token:ryxx.card_token,
				enddate:rightVue.fDate(new Date(),'date'),
				adsetl_codg:'0',
				rea:'',
				fixmedins_code:window.insuranceGbUtils.fixmedins_code,
				psn_no:cd_014.grxxJson.aac001,
				mdtrtarea_admvs:window.insuranceGbUtils.mdtrtarea_admvs,
				local_type:'3',
				out_type:'2',
			},cd_014.sfyd,cd_014.userInfo,'01301');
			if(ret1){
				let jyxx = JSON.parse(sessionStorage.getItem('jyjbxx'));
				let dr_name = rightVue.mzjbxxContent.jzysxm;
				if(rightVue.fzContent['mzysmc']){
					dr_name = rightVue.fzContent['mzysmc']
				}

				let med_type = '11';
				if(cd_014.grxxJson.ykc303 == '390' || cd_014.grxxJson.ykc303 == '392'){
				    if(cd_014.sfyd=='1'){
                        med_type = '110104';
                    }

				}
				// 只进行核算检测 走 核酸门诊
				// if(rightVue.brfyjsonList.length == 1 && rightVue.brfyjsonList[0].mxfyxmmc.indexOf('新型冠状病毒') != -1){
				// 	med_type = '990103';
				// }

				let mtxx = '';

				//根据就诊时间
				let nowDate = rightVue.fDate(rightVue.mzjbxxContent.jzsj,'date')
				let enddate = nowDate
				if(rightVue.fzContent.ryfbbm =='40'|| rightVue.fzContent.ryfbbm =='08' || rightVue.fzContent.ryfbbm =='41'){
					let param5301 = {
						"data" :{
							"psn_no":cd_014.grxxJson.aac001, //人员编号
						}
					}
					let data5301 = window.insuranceGbUtils.call1("5301",param5301,ryxx.insuplc_admdvs,cd_014.sfyd)
					if(!data5301){
						malert("获取备案信息失败", 'right', 'defeadted');
						return false;
					}else{
						if(typeof(data5301) =='boolean'){
							malert("该人员未进行门特备案", 'right', 'defeadted');
							return false;
						}else if(data5301.feedetail && data5301.feedetail.length>0){
                            med_type = '14';

						}else{
							malert("该人员未进行门特备案", 'right', 'defeadted');
							return false;
						}

					}


				}
				if(rightVue.fzContent.ryfbbm =='41'){
                    if(cd_014.grxxJson.ykc303 == '390' || cd_014.grxxJson.ykc303 == '392'){
                        med_type = '1404';
                    }else{
                        med_type = '1403';
                    }

				}
                if(rightVue.fzContent.ryfbbm =='43'){
                    med_type = '1102';

                }
				let xtjcxx = {};
				if(rightVue.fzContent.ryfbbm =='08'){
					// med_type = '992102';

					let xtparam = {
						ghxh:rightVue.fzContent['ryghxh']
					}
					$.ajaxSettings.async = false;
					$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=getXytx&parm="
					    + JSON.stringify(xtparam),
					    function (json) {
					        if (json.a == 0) {
								if(json.d){
									xtjcxx = JSON.parse(json.d)
								}else{
									malert("该人员未填写血透信息，请联系医生进行填写", 'right', 'defeadted');
									return false;
								}
					        }else{
								malert("获取血透检查信息失败", 'right', 'defeadted');
								return false;
							}
					    });
				}

				let yhResult =  cd_014.mzyjs014V3();
				console.log(yhResult)
                rightVue.yhResult = yhResult;
                rightVue.xtjs = '';
                rightVue.jzids = [];
                rightVue.med_type = med_type;
				//个人医保帐户支付总额,社保基金支付总额,现金及其他支付;用于结算框的展示
				let yka065 = 0, yka107 = 0, ykh012 = 0;
				//门特多病种结算
				// if(rightVue.fzContent.ryfbbm =='40' || rightVue.fzContent.ryfbbm =='41'){

					for (let i = 0; i < yhResult.length; i++) {
                        let tpxx = yhResult[i];
                        let sfdwybm = true;
                        for (let tqyz = 0; tqyz < tpxx.length; tqyz++) {
                            if(!tpxx[tqyz].yka094){
                                ykh012 = cd_014.MathAdd(ykh012, tpxx[tqyz].yka055);
                            }else{
                                sfdwybm = false;
                            }
                        }

                        if(sfdwybm){
                            continue;
                        }


                        let param = window.insuranceGbUtils.fparam_2201(ryxx,jyxx,dr_name);
                        let data1 = window.insuranceGbUtils.call1("2201",param,ryxx.insuplc_admdvs,cd_014.sfyd)
                        if(!data1){
                            malert("上传挂号信息失败", 'right', 'defeadted');
                            return false;
                        }
                        this.jzids.push(data1);

						// let tpxx = yhResult[i];
						let chrg_bchno = tpxx[0].yke134
						if(!chrg_bchno){
							chrg_bchno = new Date().getTime();
						}

						if(rightVue.fzContent.ryfbbm =='40' ){
							mtxx.begndate = cd_014.fDate(tpxx[0].yke123, 'date');
							mtxx.enddate = cd_014.fDate(tpxx[0].yke123, 'date');
						}



						//需要进行修改 如果是处方 就诊信息就要进行处理
						let param1 = '';
						if(tpxx[0].sfcf =='1' && cd_014.sfyd !='1' && rightVue.fzContent.ryfbbm =='40'){
							//门特每个药的开始 结束日期
							for (let lyjmt = 0; lyjmt < tpxx.length; lyjmt++) {

								let param_S3701 = window.insuranceGbUtils.fparam_S3701(tpxx[lyjmt],data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx,chrg_bchno,'1','');

								let data3701 = window.insuranceGbUtils.call1("S3701A",param_S3701,ryxx.insuplc_admdvs,cd_014.sfyd);
								if(!data3701){
									return false;
								}else{
								    let dysbxx = {
								        jzid:data1.data.mdtrt_id,
                                        dylsh:data3701.result.trt_dcla_detl_sn,
                                        yka105:tpxx[lyjmt].yka105, //流水号
                                        yke134:tpxx[lyjmt].yke134, //处方号
                                        yka059:tpxx[lyjmt].yka059, //药品编码
                                    }

                                    $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=addDyxx&parm="
                                        + JSON.stringify(dysbxx),
                                        function (json) {
                                            if (json.a != 0) {

                                            }
                                        });

                                    tpxx[lyjmt].trt_dcla_detl_sn = data3701.result.trt_dcla_detl_sn
                                    rightVue.xtjs = data3701.result.trt_dcla_detl_sn+",";
								}
							}
						}

                        if(rightVue.fzContent.ryfbbm =='08'){

                            xtjcxx.psn_no = cd_014.grxxJson.aac001;
                            xtjcxx.mdtrt_id = data1.data.mdtrt_id;
                            xtjcxx.vali_flag = '1';
                            let param_S3702 = {
                                data:xtjcxx
                            }
                            let data3702 = window.insuranceGbUtils.call1("S3702",param_S3702,ryxx.insuplc_admdvs,cd_014.sfyd);
                            if(!data3702){
                                if(rightVue.xtjs){
                                    this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx,rightVue.jzids)
                                }
                                return false;
                            }
                        }

                        //门特2203A
                        param1= window.insuranceGbUtils.fparam_mt2203A(data1,med_type,jyxx,dr_name,tpxx[0].lczd,tpxx[0].mtbzmc,tpxx[0].mtbzbm);
                        let data2203a = window.insuranceGbUtils.call1("2203A",param1,ryxx.insuplc_admdvs,cd_014.sfyd)
                        if(!data2203a){
                            if(rightVue.xtjs){
                                this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx,rightVue.jzids)
                            }
                            return false;
                        }




						let fyzh = 0.00; //计算费用总和
						//计算总费用
						tpxx.forEach(function (current, i) {
						    if(current.yka094){
                                var n1=new BigNumber(current.yka055);
                                current.yka055 = n1.toFixed(2);
                                fyzh = cd_014.MathAdd(fyzh, current.yka055);
                            }


						});

                        let sjfyzh = 0.00; //计算费用总和
                        //计算总费用
                        tpxx.forEach(function (current, i) {
                                var n1=new BigNumber(current.yka055);
                                current.yka055 = n1.toFixed(2);
                                sjfyzh = cd_014.MathAdd(sjfyzh, current.yka055);
                        });


							let param_04 = window.insuranceGbUtils.fparam_2204(tpxx,data1,ryxx,jyxx,tpxx[0].mtbzbm);
							let data2204 = window.insuranceGbUtils.call1("2204",param_04,ryxx.insuplc_admdvs,cd_014.sfyd);

							if(!data2204){
								if(rightVue.xtjs){
									this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx,rightVue.jzids)
								}
								return false;
							}



						//非血透患者 无须该参数
						if(rightVue.fzContent.ryfbbm !='08'){
							mtxx.enddate = ''
							adsetl_codg = ''
							rea = ''
						}
						let param_06a = window.insuranceGbUtils.fparam_2206A(data1,med_type,ryxx,jyxx,chrg_bchno,mtxx,adsetl_codg,rea,fyzh,tpxx);
						let data2206 =  window.insuranceGbUtils.call1("2206A",param_06a,ryxx.insuplc_admdvs,cd_014.sfyd);
						if(!data2206){
							if(rightVue.xtjs){
								this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx,rightVue.jzids)
							}
							return false;
						}



						let param_07a = window.insuranceGbUtils.fparam_2207A(data1,med_type,ryxx,jyxx,chrg_bchno,mtxx,adsetl_codg,rea,fyzh,tpxx);
						let data2 = window.insuranceGbUtils.call1("2207A",param_07a,ryxx.insuplc_admdvs,cd_014.sfyd);
						if(!data2){
							if(rightVue.xtjs){
								this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx,rightVue.jzids)
							}
							malert("保险内部错误,不能完成结算，请稍后重试或反馈IT", 'top', 'defeadted');
							return false;
						}else{
							data2.setlinfo.ghxh =rightVue.fzContent['ryghxh'];
							data2.setlinfo.yke134 =tpxx[0].yke134;
                            data2.setlinfo.sjfyzh = sjfyzh;
							rightVue.gbjsxx.push(data2.setlinfo)
							yka065 = cd_014.MathAdd(yka065, data2.setlinfo.acct_pay);
							yka107 = cd_014.MathAdd(yka107, data2.setlinfo.fund_pay_sumamt);
							// ykh012 = cd_014.MathAdd(ykh012, data2.setlinfo.psn_cash_pay);
							if(i==0){
								let jzxx={
									rybh:cd_014.grxxJson.aac001,
									ryzjlx:cd_014.syxx.baseinfo.psn_cert_type,
									zjhm:cd_014.syxx.baseinfo.certno,
									ryxm:cd_014.syxx.baseinfo.psn_name,
									xb:cd_014.syxx.baseinfo.gend,
									mz:cd_014.syxx.baseinfo.naty,
									csrq:cd_014.syxx.baseinfo.brdy,
									nl:cd_014.syxx.baseinfo.age,
									ye:cd_014.grxxJson.ykc194,
									rylb:ryxx.psn_type,
									rycbzt:ryxx.psn_insu_stas,
									xzlx:ryxx.insutype,
									grcbrq:ryxx.psn_insu_date,
									ztcbrq:ryxx.paus_insu_date,
									gwybz:ryxx.cvlserv_flag,
									cbdqybqh:ryxx.insuplc_admdvs,
									dwmc:ryxx.emp_name,
									rysfdj:cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].psn_type_lv?cd_014.syxx.idetinfo[0].psn_type_lv:'',
									rysflb:cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].psn_idet_type?cd_014.syxx.idetinfo[0].psn_idet_type:'',
									ksrq:cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].begntime?cd_014.syxx.idetinfo[0].begntime:'',
									jzpzlx:ryxx.mdtrt_cert_type,
									jzpzbh:ryxx.mdtrt_cert_no,
									mzh:rightVue.fzContent['ryghxh'],
									zzysbm:param.data.atddr_no,
									zzysxm:param.data.dr_name,
									ryksbm:param.data.dept_code,
									ryksmc:param.data.dept_name,
									kb:param.data.caty,
									jzid:data1.data.mdtrt_id,
									yllb:param_06a.data.med_type,
									zybqms:rightVue.fzContent['zs'],
									bzbm:rightVue.fzContent['jbbm'],
									bzmc:rightVue.fzContent['jbmc'],
									jhsysslb:'',
									jhsyssrq:'',
									sylb:'',
									yzs:'',
									zfbz:'0',
									zfr:'',
									zfrq:'',
									bz:'',
									ywlsh:'',
									mtbbah:'',
									jsrq:rightVue.fDate(new Date(),'All'),
								}
								$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjzxx&parm="
								    + JSON.stringify(jzxx),
								    function (json) {
								        if (json.a != 0) {
								          malert("保存失败  " + json.c, 'right', 'defeadted');
								        }
								    });
								let zdxx = []
								for (let zdxxi = 0; zdxxi < param1.diseinfo.length; zdxxi++) {

									zdxx.push({
										ghxh:rightVue.fzContent['ryghxh'],
										jzid:data1.data.mdtrt_id,
										zdlb:param1.diseinfo[zdxxi].diag_type,
										zdpxh:param1.diseinfo[zdxxi].diag_srt_no,
										zddm:param1.diseinfo[zdxxi].diag_code,
										zdmc:param1.diseinfo[zdxxi].diag_name,
										zdks:param1.diseinfo[zdxxi].diag_dept,
										zdysbm:param1.diseinfo[zdxxi].dise_dor_no,
										zdysxm:param1.diseinfo[zdxxi].dise_dor_name,
										zdsj:param1.diseinfo[zdxxi].diag_time,
										yxbz:param1.diseinfo[zdxxi].vali_flag,
									})
								}
								$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbzdxx&parm="
								    + JSON.stringify(zdxx),
								    function (json) {
								        if (json.a != 0) {
								          malert("保存失败  " + json.c, 'right', 'defeadted');
								        }
								    });
							}
							let jsxx ={
								jzid:data2.setlinfo.mdtrt_id,
								jsid:data2.setlinfo.setl_id,
								mz:data2.setlinfo.naty,
								csrq:data2.setlinfo.brdy,
								nl:data2.setlinfo.age,
								xzlx:data2.setlinfo.insutype,
								rylb:data2.setlinfo.psn_type,
								gwybz:data2.setlinfo.cvlserv_flag,
								jssj:data2.setlinfo.setl_time,
								jzpzlx:data2.setlinfo.mdtrt_cert_type,
								yllb:data2.setlinfo.med_type,
								ylfze:data2.setlinfo.medfee_sumamt,
								cxjzffy:data2.setlinfo.overlmt_selfpay,
								xxzfje:data2.setlinfo.preselfpay_amt,
								fhzcfwje:data2.setlinfo.inscp_scp_amt,
								jbylbxtcjjzc:data2.setlinfo.hifp_pay,
								jbylbxtcjjzfbl:data2.setlinfo.pool_prop_selfpay,
								gwyylbzzjzc:data2.setlinfo.cvlserv_pay,
								qybcylbxjjzc:data2.setlinfo.hifes_pay,
								jmdbbxzjzc:data2.setlinfo.hifmi_pay,
								zgdeylfybzjjzc:data2.setlinfo.hifob_pay,
								yljzjjzc:data2.setlinfo.maf_pay,
								qtzc:data2.setlinfo.oth_pay,
								jjzfze:data2.setlinfo.fund_pay_sumamt,
								sjzfqfx:data2.setlinfo.act_pay_dedc,
								grfdzje:data2.setlinfo.psn_part_amt,
								ryzjlx:data2.setlinfo.psn_cert_type,
								grzhzc:data2.setlinfo.acct_pay,
								rybh:data2.setlinfo.psn_no,
								qzfje:data2.setlinfo.fulamt_ownpay_amt,
								zjhm:data2.setlinfo.certno,
								zrxjzc:data2.setlinfo.psn_cash_pay,
								yyfdje:data2.setlinfo.hosp_part_amt,
								xb:data2.setlinfo.gend,
								ye:data2.setlinfo.balc,
								ryxm:data2.setlinfo.psn_name,
								grzhzjzfje:data2.setlinfo.acct_mulaid_pay,
								yyjgjsid:(data2.msgid?data2.msgid:data2.setlinfo.medins_setl_id),
								qsjbjg:data2.setlinfo.clr_optins,
								qsfs:data2.setlinfo.clr_way,
								qslb:data2.setlinfo.clr_type,
								zfbz:'0',
								zfry:'',
								zfrq:'',
								jszt:'1',
								jsczy:userName,
								grjsfs:'01',
								sfpch:chrg_bchno,
								fph:'',
								cjrq:data2.setlinfo.setl_time,
                                insuplcadmdvs:ryxx.insuplc_admdvs,
							}
								console.log(JSON.stringify(jsxx));
							$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjsxx&parm="
							    + JSON.stringify(jsxx),
							    function (json) {
							        if (json.a != 0) {
							          malert("保存失败  " + json.c, 'right', 'defeadted');
							        }
							    });

							let jsjjfx = [];
							for (let i = 0; i < data2.setldetail.length; i++) {

								jsjjfx.push({
									jzid:data2.setlinfo.mdtrt_id,
									jsid:data2.setlinfo.setl_id,
									jjzflx:data2.setldetail[i].fund_pay_type,
									fhzcfwje:data2.setldetail[i].inscp_scp_amt,
									bckzfxeje:data2.setldetail[i].crt_payb_lmt_amt,
									jjzfje:data2.setldetail[i].fund_payamt,
									jjzflxmc:data2.setldetail[i].fund_pay_type_name,
									jsgcxx:data2.setldetail[i].setl_proc_info?data2.setldetail[i].setl_proc_info.replace(/%/g,"/"):'',

								})
							}
							console.log(JSON.stringify(jsjjfx));
							if(jsjjfx.length>0){
								$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjjfxxx&parm="
								    + JSON.stringify(jsjjfx),
								    function (json) {
								        if (json.a != 0) {
								          malert("保存失败  " + json.c, 'right', 'defeadted');
								        }
								    });
							}

                            let param_5204 = {
                                data:{
                                    "psn_no":data2.setlinfo.psn_no, //人员编号
                                    "setl_id":data2.setlinfo.setl_id, //结算id
                                    "mdtrt_id":data2.setlinfo.mdtrt_id, //就诊id
                                    "exp_content":{
                                        "card_token":''
                                    },
                                }
                            }
                            let data = window.insuranceGbUtils.call1("5204",param_5204,ryxx.insuplc_admdvs,cd_014.sfyd);

                            if(data && data.length>0) {

                                let fyxx = [];
                                for (let i = 0; i < data.length; i++) {
                                    fyxx.push({
                                        jzid: data[i].mdtrt_id,
                                        jsid: data[i].setl_id,
                                        bxxmmc: data[i].hilist_name,
                                        bxxmbm: data[i].hilist_code,
                                        hisxmmc: data[i].medins_list_name,
                                        hisxmbm: data[i].medins_list_codg,
                                        dj: data[i].pric ? data[i].pric : 0,
                                        sl: data[i].cnt ? data[i].cnt : 0,
                                        fyje: data[i].det_item_fee_sumamt ? data[i].det_item_fee_sumamt : 0,
                                        ybSfxmdj: data[i].chrgitm_lv ? data[i].chrgitm_lv : 0,
                                        ybBz: data[i].memo,
                                        ybZbbz: data[i].drt_reim_flag,
                                        ybXzsybz: data[i].lmt_used_flag,
                                        ybMltxbz: data[i].list_sp_item_flag,
                                        ybEttpybz: data[i].chld_medc_flag,
                                        ybYbtpybz: data[i].hi_nego_drug_flag,
                                        ybJbywbz: data[i].bas_medn_flag,
                                        ybYlsfxmlb: data[i].med_chrgitm_type,
                                        ybFhzcfwje: data[i].inscp_scp_amt ? data[i].inscp_scp_amt : 0,
                                        ybXxzfje: data[i].preselfpay_amt ? data[i].preselfpay_amt : 0,
                                        ybCxjje: data[i].overlmt_amt ? data[i].overlmt_amt : 0,
                                        ybQzfje: data[i].fulamt_ownpay_amt ? data[i].fulamt_ownpay_amt : 0,
                                        ybZfbl: data[i].selfpay_prop,
                                        ybDjsxje: data[i].pric_uplmt_amt ? data[i].pric_uplmt_amt : 0,
                                        feeOcurTime: data[i].opt_time,
                                        sfpch: data[i].rx_drord_no ? data[i].rx_drord_no : chrg_bchno,
                                        ghxh: rightVue.fzContent['ryghxh'],
                                        mzksmc: rightVue.fzContent['mzksmc'],
                                        jsrq: data2.setlinfo.setl_time,
                                    })
                                }

                                var params = '{"list":' + JSON.stringify(fyxx) + '}';
                                $.ajax({
                                    type:"POST",
                                    url:"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=addFyxx&parm=",
                                    contentType: "application/json", //
                                    dataType:"json",
                                    data:params,//
                                    async: false,
                                    success:function (data) {

                                    }
                                })



                            }


                            //判断是否可以走共济

                            if(yhResult.length== 1 && data2.setlinfo.psn_cash_pay>0){
                                rightVue.sbjyxx = data2
                            }


                            ykh012 = cd_014.MathAdd(ykh012, data2.setlinfo.psn_cash_pay);

						}


					}

				// }
				// else{
                //     //其余走向
                //     let param = window.insuranceGbUtils.fparam_2201(ryxx,jyxx,dr_name);
                //     let data1 = window.insuranceGbUtils.call1("2201",param,ryxx.insuplc_admdvs,cd_014.sfyd)
                //     if(!data1){
                //         malert("上传挂号信息失败", 'right', 'defeadted');
                //         return false;
                //     }
                //
                //     let rea ='';
                //     let adsetl_codg ='';
                //     //获取就诊时间
                //     let tpnowDate = new Date(nowDate);
                //     var Year=tpnowDate.getFullYear();
                //     var Month=tpnowDate.getMonth()+1;
                //     var Datetp=tpnowDate.getDate();
                //     //获取就诊时间之后的 3个月日期
                //     var mydate=new Date(Year,Month+3,Datetp);
                //
                //     //获取当前日期
                //     let mynowDate = new Date();
                //     //如果当前日期在 3个月之类 视为提前结算 如果不在3个月之内  就视为正常结算
                //     if(mynowDate< mydate){
                //         rea = '患者提前结算'
                //
                //         adsetl_codg = '1'
                //     }else{
                //         mtxx.enddate = mydate
                //     }
                //     //
                //
                //
                //     if(!yhResult || yhResult.length==0){
                //
                //         return false;
                //     }
                //
                //
                //
                //     let param1 = window.insuranceGbUtils.fparam_2203A(data1,med_type,jyxx,dr_name);
                //     let data2203a = window.insuranceGbUtils.call1("2203A",param1,ryxx.insuplc_admdvs,cd_014.sfyd)
                //     if(!data2203a){
                //         if(rightVue.xtjs){
                //             this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx)
                //         }
                //         return false;
                //     }
                //
                //
                //     for (let i = 0; i < yhResult.length; i++) {
                //         let tpxx = yhResult[i];
                //         let fyzh = 0.00; //计算费用总和
                //         let chrg_bchno = tpxx[0].yke134
                //         if(!chrg_bchno){
                //             chrg_bchno = new Date().getTime();
                //         }
                //         //计算总费用
                //         tpxx.forEach(function (current, i) {
                //             var n1=new BigNumber(current.yka055);
                //             current.yka055 = n1.toFixed(2);
                //             fyzh = cd_014.MathAdd(fyzh, current.yka055);
                //
                //         });
                //
                //         let param_04 = window.insuranceGbUtils.fparam_2204(tpxx,data1,ryxx,jyxx,'');
                //         let data2204 = window.insuranceGbUtils.call1("2204",param_04,ryxx.insuplc_admdvs,cd_014.sfyd);
                //
                //         if(!data2204){
                //             if(rightVue.xtjs){
                //                 this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx)
                //             }
                //             return false;
                //         }
                //
                //
                //
                //
                //
                //
                //         //非血透患者 无须该参数
                //         if(rightVue.fzContent.ryfbbm !='08'){
                //             mtxx.enddate = ''
                //             adsetl_codg = ''
                //             rea = ''
                //         }
                //         let param_06a = window.insuranceGbUtils.fparam_2206A(data1,med_type,ryxx,jyxx,chrg_bchno,mtxx,adsetl_codg,rea,fyzh,tpxx);
                //         let data2206 =  window.insuranceGbUtils.call1("2206A",param_06a,ryxx.insuplc_admdvs,cd_014.sfyd);
                //         if(!data2206){
                //             if(rightVue.xtjs){
                //                 this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx)
                //             }
                //             return false;
                //         }
                //
                //
                //
                //         let param_07a = window.insuranceGbUtils.fparam_2207A(data1,med_type,ryxx,jyxx,chrg_bchno,mtxx,adsetl_codg,rea,fyzh,tpxx);
                //         let data2 = window.insuranceGbUtils.call1("2207A",param_07a,ryxx.insuplc_admdvs,cd_014.sfyd);
                //         if(!data2){
                //             if(rightVue.xtjs){
                //                 this.xtch(yhResult,data1,med_type,mtxx,rightVue.xtjs,jyxx,ryxx)
                //             }
                //             malert("保险内部错误,不能完成结算，请稍后重试或反馈IT", 'top', 'defeadted');
                //             return false;
                //         }else{
                //             data2.setlinfo.ghxh =rightVue.fzContent['ryghxh'];
                //             data2.setlinfo.yke134 =tpxx[0].yke134;
                //             rightVue.gbjsxx.push(data2.setlinfo)
                //             yka065 = cd_014.MathAdd(yka065, data2.setlinfo.acct_pay);
                //             yka107 = cd_014.MathAdd(yka107, data2.setlinfo.fund_pay_sumamt);
                //
                //             if(i==0){
                //                 let jzxx={
                //                     rybh:cd_014.grxxJson.aac001,
                //                     ryzjlx:cd_014.syxx.baseinfo.psn_cert_type,
                //                     zjhm:cd_014.syxx.baseinfo.certno,
                //                     ryxm:cd_014.syxx.baseinfo.psn_name,
                //                     xb:cd_014.syxx.baseinfo.gend,
                //                     mz:cd_014.syxx.baseinfo.naty,
                //                     csrq:cd_014.syxx.baseinfo.brdy,
                //                     nl:cd_014.syxx.baseinfo.age,
                //                     ye:cd_014.grxxJson.ykc194,
                //                     rylb:ryxx.psn_type,
                //                     rycbzt:ryxx.psn_insu_stas,
                //                     xzlx:ryxx.insutype,
                //                     grcbrq:ryxx.psn_insu_date,
                //                     ztcbrq:ryxx.paus_insu_date,
                //                     gwybz:ryxx.cvlserv_flag,
                //                     cbdqybqh:ryxx.insuplc_admdvs,
                //                     dwmc:ryxx.emp_name,
                //                     rysfdj:cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].psn_type_lv?cd_014.syxx.idetinfo[0].psn_type_lv:'',
                //                     rysflb:cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].psn_idet_type?cd_014.syxx.idetinfo[0].psn_idet_type:'',
                //                     ksrq:cd_014.syxx.idetinfo && cd_014.syxx.idetinfo[0] && cd_014.syxx.idetinfo[0].begntime?cd_014.syxx.idetinfo[0].begntime:'',
                //                     jzpzlx:ryxx.mdtrt_cert_type,
                //                     jzpzbh:ryxx.mdtrt_cert_no,
                //                     mzh:rightVue.fzContent['ryghxh'],
                //                     zzysbm:param.data.atddr_no,
                //                     zzysxm:param.data.dr_name,
                //                     ryksbm:param.data.dept_code,
                //                     ryksmc:param.data.dept_name,
                //                     kb:param.data.caty,
                //                     jzid:data1.data.mdtrt_id,
                //                     yllb:param_06a.data.med_type,
                //                     zybqms:rightVue.fzContent['zs'],
                //                     bzbm:rightVue.fzContent['jbbm'],
                //                     bzmc:rightVue.fzContent['jbmc'],
                //                     jhsysslb:'',
                //                     jhsyssrq:'',
                //                     sylb:'',
                //                     yzs:'',
                //                     zfbz:'0',
                //                     zfr:'',
                //                     zfrq:'',
                //                     bz:'',
                //                     ywlsh:'',
                //                     mtbbah:'',
                //                     jsrq:rightVue.fDate(new Date(),'All'),
                //                 }
                //                 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjzxx&parm="
                //                     + JSON.stringify(jzxx),
                //                     function (json) {
                //                         if (json.a != 0) {
                //                             malert("保存失败  " + json.c, 'right', 'defeadted');
                //                         }
                //                     });
                //                 let zdxx = []
                //                 for (let zdxxi = 0; zdxxi < param1.diseinfo.length; zdxxi++) {
                //
                //                     zdxx.push({
                //                         ghxh:rightVue.fzContent['ryghxh'],
                //                         jzid:data1.data.mdtrt_id,
                //                         zdlb:param1.diseinfo[zdxxi].diag_type,
                //                         zdpxh:param1.diseinfo[zdxxi].diag_srt_no,
                //                         zddm:param1.diseinfo[zdxxi].diag_code,
                //                         zdmc:param1.diseinfo[zdxxi].diag_name,
                //                         zdks:param1.diseinfo[zdxxi].diag_dept,
                //                         zdysbm:param1.diseinfo[zdxxi].dise_dor_no,
                //                         zdysxm:param1.diseinfo[zdxxi].dise_dor_name,
                //                         zdsj:param1.diseinfo[zdxxi].diag_time,
                //                         yxbz:param1.diseinfo[zdxxi].vali_flag,
                //                     })
                //                 }
                //                 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbzdxx&parm="
                //                     + JSON.stringify(zdxx),
                //                     function (json) {
                //                         if (json.a != 0) {
                //                             malert("保存失败  " + json.c, 'right', 'defeadted');
                //                         }
                //                     });
                //             }
                //             let jsxx ={
                //                 jzid:data2.setlinfo.mdtrt_id,
                //                 jsid:data2.setlinfo.setl_id,
                //                 mz:data2.setlinfo.naty,
                //                 csrq:data2.setlinfo.brdy,
                //                 nl:data2.setlinfo.age,
                //                 xzlx:data2.setlinfo.insutype,
                //                 rylb:data2.setlinfo.psn_type,
                //                 gwybz:data2.setlinfo.cvlserv_flag,
                //                 jssj:data2.setlinfo.setl_time,
                //                 jzpzlx:data2.setlinfo.mdtrt_cert_type,
                //                 yllb:data2.setlinfo.med_type,
                //                 ylfze:data2.setlinfo.medfee_sumamt,
                //                 cxjzffy:data2.setlinfo.overlmt_selfpay,
                //                 xxzfje:data2.setlinfo.preselfpay_amt,
                //                 fhzcfwje:data2.setlinfo.inscp_scp_amt,
                //                 jbylbxtcjjzc:data2.setlinfo.hifp_pay,
                //                 jbylbxtcjjzfbl:data2.setlinfo.pool_prop_selfpay,
                //                 gwyylbzzjzc:data2.setlinfo.cvlserv_pay,
                //                 qybcylbxjjzc:data2.setlinfo.hifes_pay,
                //                 jmdbbxzjzc:data2.setlinfo.hifmi_pay,
                //                 zgdeylfybzjjzc:data2.setlinfo.hifob_pay,
                //                 yljzjjzc:data2.setlinfo.maf_pay,
                //                 qtzc:data2.setlinfo.oth_pay,
                //                 jjzfze:data2.setlinfo.fund_pay_sumamt,
                //                 sjzfqfx:data2.setlinfo.act_pay_dedc,
                //                 grfdzje:data2.setlinfo.psn_part_amt,
                //                 ryzjlx:data2.setlinfo.psn_cert_type,
                //                 grzhzc:data2.setlinfo.acct_pay,
                //                 rybh:data2.setlinfo.psn_no,
                //                 qzfje:data2.setlinfo.fulamt_ownpay_amt,
                //                 zjhm:data2.setlinfo.certno,
                //                 zrxjzc:data2.setlinfo.psn_cash_pay,
                //                 yyfdje:data2.setlinfo.hosp_part_amt,
                //                 xb:data2.setlinfo.gend,
                //                 ye:data2.setlinfo.balc,
                //                 ryxm:data2.setlinfo.psn_name,
                //                 grzhzjzfje:data2.setlinfo.acct_mulaid_pay,
                //                 yyjgjsid:(data2.msgid?data2.msgid:data2.setlinfo.medins_setl_id),
                //                 qsjbjg:data2.setlinfo.clr_optins,
                //                 qsfs:data2.setlinfo.clr_way,
                //                 qslb:data2.setlinfo.clr_type,
                //                 zfbz:'0',
                //                 zfry:'',
                //                 zfrq:'',
                //                 jszt:'1',
                //                 jsczy:userName,
                //                 grjsfs:'01',
                //                 sfpch:chrg_bchno,
                //                 fph:'',
                //                 cjrq:data2.setlinfo.setl_time,
                //                 insuplcadmdvs:ryxx.insuplc_admdvs,
                //             }
                //             console.log(JSON.stringify(jsxx));
                //             $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjsxx&parm="
                //                 + JSON.stringify(jsxx),
                //                 function (json) {
                //                     if (json.a != 0) {
                //                         malert("保存失败  " + json.c, 'right', 'defeadted');
                //                     }
                //                 });
                //
                //             let jsjjfx = [];
                //             for (let i = 0; i < data2.setldetail.length; i++) {
                //
                //                 jsjjfx.push({
                //                     jzid:data2.setlinfo.mdtrt_id,
                //                     jsid:data2.setlinfo.setl_id,
                //                     jjzflx:data2.setldetail[i].fund_pay_type,
                //                     fhzcfwje:data2.setldetail[i].inscp_scp_amt,
                //                     bckzfxeje:data2.setldetail[i].crt_payb_lmt_amt,
                //                     jjzfje:data2.setldetail[i].fund_payamt,
                //                     jjzflxmc:data2.setldetail[i].fund_pay_type_name,
                //                     jsgcxx:data2.setldetail[i].setl_proc_info?data2.setldetail[i].setl_proc_info.replace(/%/g,"/"):'',
                //
                //                 })
                //             }
                //             console.log(JSON.stringify(jsjjfx));
                //             if(jsjjfx.length>0){
                //                 $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjjfxxx&parm="
                //                     + JSON.stringify(jsjjfx),
                //                     function (json) {
                //                         if (json.a != 0) {
                //                             malert("保存失败  " + json.c, 'right', 'defeadted');
                //                         }
                //                     });
                //             }
                //
                //             let param_5204 = {
                //                 data:{
                //                     "psn_no":data2.setlinfo.psn_no, //人员编号
                //                     "setl_id":data2.setlinfo.setl_id, //结算id
                //                     "mdtrt_id":data2.setlinfo.mdtrt_id, //就诊id
                //                     "exp_content":{
                //                         "card_token":''
                //                     },
                //                 }
                //             }
                //             let data = window.insuranceGbUtils.call1("5204",param_5204,ryxx.insuplc_admdvs,cd_014.sfyd);
                //
                //             if(data && data.length>0) {
                //
                //                 let fyxx = [];
                //                 for (let i = 0; i < data.length; i++) {
                //                     fyxx.push({
                //                         jzid: data[i].mdtrt_id,
                //                         jsid: data[i].setl_id,
                //                         bxxmmc: data[i].hilist_name,
                //                         bxxmbm: data[i].hilist_code,
                //                         hisxmmc: data[i].medins_list_name,
                //                         hisxmbm: data[i].medins_list_codg,
                //                         dj: data[i].pric ? data[i].pric : 0,
                //                         sl: data[i].cnt ? data[i].cnt : 0,
                //                         fyje: data[i].det_item_fee_sumamt ? data[i].det_item_fee_sumamt : 0,
                //                         ybSfxmdj: data[i].chrgitm_lv ? data[i].chrgitm_lv : 0,
                //                         ybBz: data[i].memo,
                //                         ybZbbz: data[i].drt_reim_flag,
                //                         ybXzsybz: data[i].lmt_used_flag,
                //                         ybMltxbz: data[i].list_sp_item_flag,
                //                         ybEttpybz: data[i].chld_medc_flag,
                //                         ybYbtpybz: data[i].hi_nego_drug_flag,
                //                         ybJbywbz: data[i].bas_medn_flag,
                //                         ybYlsfxmlb: data[i].med_chrgitm_type,
                //                         ybFhzcfwje: data[i].inscp_scp_amt ? data[i].inscp_scp_amt : 0,
                //                         ybXxzfje: data[i].preselfpay_amt ? data[i].preselfpay_amt : 0,
                //                         ybCxjje: data[i].overlmt_amt ? data[i].overlmt_amt : 0,
                //                         ybQzfje: data[i].fulamt_ownpay_amt ? data[i].fulamt_ownpay_amt : 0,
                //                         ybZfbl: data[i].selfpay_prop,
                //                         ybDjsxje: data[i].pric_uplmt_amt ? data[i].pric_uplmt_amt : 0,
                //                         feeOcurTime: data[i].opt_time,
                //                         sfpch: data[i].rx_drord_no ? data[i].rx_drord_no : chrg_bchno,
                //                         ghxh: rightVue.fzContent['ryghxh'],
                //                         mzksmc: rightVue.fzContent['mzksmc'],
                //                         jsrq: data2.setlinfo.setl_time,
                //                     })
                //                 }
                //
                //                 var params = '{"list":' + JSON.stringify(fyxx) + '}';
                //                 $.ajax({
                //                     type:"POST",
                //                     url:"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=addFyxx&parm=",
                //                     contentType: "application/json", //
                //                     dataType:"json",
                //                     data:params,//
                //                     async: false,
                //                     success:function (data) {
                //
                //                     }
                //                 })
                //
                //
                //
                //             }
                //
                //             //判断是否可以走共济
                //
                //             if(yhResult.length== 1 && data2.setlinfo.psn_cash_pay>0){
                //                 rightVue.sbjyxx = data2
                //             }
                //
                //
                //             ykh012 = cd_014.MathAdd(ykh012, data2.setlinfo.psn_cash_pay);
                //         }
                //     }
                // }
                    //todo 总费用
                    let fyjehj = rightVue.fDec(rightVue.fyjehj, 2);
                    let ybkzf = cd_014.MathAdd(yka065, yka107);
                    //todo 社保基金支付总额
                    popCenter1.jsjlContent.ybkzf = rightVue.fDec(ybkzf, 2);
                    // todo 计算医保差额（总费用-（个人帐户支付总额+社保基金支付总额））
                    let ybce = cd_014.floatSub(fyjehj, ybkzf);
                    let ybfyce = rightVue.fDec(ybce, 3);
                    if (Math.abs(ybfyce) < 0.02) {
                        popCenter1.jsjlContent.ybfyhj = ybfyce;//医保差额
                    }
                    return true;

			}else{
				return false;
			}
			return false;
        },
        //门诊结算，弹窗计算放到方法中，
        finalMzjs: function () {

            // popCenter1.jsjlContent.ybzhzf = rightVue.fDec(parseFloat(popCenter1.jsjlContent.ybkzf) - parseFloat(popCenter1.jsjlContent.ybtczf), 2);
            popCenter1.jsjlContent.ybzhzf = rightVue.fDec(rightVue.floatSub(popCenter1.jsjlContent.ybkzf,popCenter1.jsjlContent.ybtczf), 2);

                        popCenter1.jsjlContent.ylkzf = 0;
                        let tmp1 = rightVue.fDec(rightVue.MathAdd(parseFloat(popCenter1.jsjlContent.ybkzf), parseFloat(popCenter1.jsjlContent.ylkzf)),2);
                        let tmp2 = rightVue.fDec(rightVue.MathAdd(parseFloat(popCenter1.jsjlContent.qtzf), parseFloat(popCenter1.jsjlContent.ybfyhj)),2);
                        let tmp3 = rightVue.fDec(rightVue.MathAdd(tmp1, tmp2),2);
                        //费用金额-（医保卡支付+医疗卡支付+其他支付+医保差额）
                        let xjzf = rightVue.floatSub(parseFloat(rightVue.fyjehj), tmp3);
                        popCenter1.jsjlContent.xjzf = rightVue.fDec(xjzf, 2); //rightVue.fDec(parseFloat(rightVue.fyjehj) - parseFloat(popCenter1.jsjlContent.ybkzf) - parseFloat(popCenter1.jsjlContent.ylkzf) - parseFloat(popCenter1.jsjlContent.qtzf) - parseFloat(popCenter1.jsjlContent.ybfyhj), 2);

                        $("#ssje").val(rightVue.fDec(xjzf, 2));
            console.log("金额合计:[" + rightVue.fyjehj + "]- (医保卡支付[" + popCenter1.jsjlContent.ybkzf + "]+医疗卡支付[" + popCenter1.jsjlContent.ylkzf + "]" + "+其他支付[" + popCenter1.jsjlContent.qtzf + "]+医保差额[" + popCenter1.jsjlContent.ybfyhj + "])");

            $("#zbje").val("");
            // 参数权限是否允许录入保险金额（此处保险金额还未做处理还有各种限定）
            if (tableInfo.csqxContent.cs00500100110 == '0') {
                $("#ybkzf").attr('disabled', true);
            } else {
                $("#ybkzf").attr('disabled', false);
            }
            Vue.nextTick(function () {
                $('#ssje').focus();
            });
            popCenter1.$forceUpdate();
        },
        mzjsQhtf: function () {
            // 传入 需要修改的门诊费用集合、添加的门诊费用集合、添加的结算记录对象、修改的病人挂号对象
            $.ajaxSettings.async = false;
            var yhhj = "0";
            var fyjehj = "0";
            if (rightVue.brfyjsonList.length > 0) {
                for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                    if (!rightVue.brfyjsonList[i].zxks) {
                        rightVue.brfyjsonList[i].zxks = rightVue.fzContent.mzks
                        return false;
                    }
                    // 基本信息在这里赋值(区最新的值)
                    if (rightVue.fzContent['ryghxh']) {
                        rightVue.brfyjsonList[i]['ryghxh'] = rightVue.fzContent['ryghxh'];
                    }
                    if (rightVue.fzContent['brid']) {
                        rightVue.brfyjsonList[i]['brid'] = rightVue.fzContent['brid'];
                    }
                    rightVue.brfyjsonList[i]['brxm'] = rightVue.fzContent['brxm'];
                    rightVue.brfyjsonList[i]['mzys'] = rightVue.fzContent['mzys'];
                    rightVue.brfyjsonList[i]['mzysxm'] = rightVue.listGetName(rightVue.mzysList, rightVue.fzContent['mzys'], 'rybm', 'ryxm');
                    rightVue.brfyjsonList[i]['mzks'] = rightVue.fzContent['mzks'];
                    rightVue.brfyjsonList[i]['mzksmc'] = rightVue.listGetName(rightVue.ghksList, rightVue.fzContent['mzks'], 'ksbm', 'ksmc');
                    rightVue.brfyjsonList[i]['ryfbbm'] = rightVue.fzContent['ryfbbm'];
                    rightVue.brfyjsonList[i]['ryfbmc'] = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['ryfbbm'], 'fbbm', 'fbmc');
                    rightVue.brfyjsonList[i]['rybxlbbm'] = rightVue.fzContent['rybxlbbm'];
                    rightVue.brfyjsonList[i]['rybxlbmc'] = rightVue.listGetName(rightVue.brfbList, rightVue.fzContent['rybxlbbm'], 'bxlbbm', 'bxlbmc');
                    // 判断有没有处方号
                    var cffy = {};
                    if (rightVue.brfyjsonList[i]['yzhm']) {
						if(rightVue.brfyjsonList[i]['yzlx'] !='9'){
							cffy['cfh'] = rightVue.brfyjsonList[i].yzhm;
							popCenter1.cfkfxmList.push(cffy); // 给处方费用信息集合赋值
						}
                    }
                    if (rightVue.brfyjsonList[i].yhje == null || rightVue.brfyjsonList[i].yhje == "") {
                        rightVue.brfyjsonList[i].yhje = '0';
                    }
                    yhhj = parseFloat(yhhj) + parseFloat(rightVue.brfyjsonList[i].yhje); // 循环相加获取优惠金额合计
                    fyjehj = parseFloat(fyjehj) + parseFloat(rightVue.brfyjsonList[i].fyje); // 循环获取实收金额（此费用金额已经是减了优惠金额的）
                }
            } else {
                malert("请选择费用项目!", 'right', 'defeadted');
                return;
            }
            if (!popCenter1.jsjlContent.qtzf) {
                popCenter1.jsjlContent.qtzf = '0';
            }

            // 对结算记录进行赋值操作
            popCenter1.jsjlContent.brxm = rightVue.fzContent['brxm'];
            popCenter1.jsjlContent.ysje = rightVue.fDec(parseFloat(yhhj) + parseFloat(fyjehj), 2);
            popCenter1.jsjlContent.yhhj = rightVue.fDec(yhhj, 2);
            popCenter1.jsjlContent.fyhj = rightVue.fDec(fyjehj, 2);
            popCenter1.jsjlContent.ybkzf = 0;


            //四川遂宁清华同方
            if (rightVue.yjsContentSnqhtf) {
                if (rightVue.yjsContentSnqhtf.bxje) {
                    popCenter1.jsjlContent.ybkzf = rightVue.yjsContentSnqhtf.bxje;
                }
            }


            if (!popCenter1.jsjlContent.ybkzf) {
                popCenter1.jsjlContent.ybkzf = '0';
            }

            popCenter1.jsjlContent.ylkzf = 0;
            popCenter1.jsjlContent.xjzf = rightVue.fDec(parseFloat(fyjehj) - parseFloat(popCenter1.jsjlContent.ybkzf) - parseFloat(popCenter1.jsjlContent.ylkzf) - parseFloat(popCenter1.jsjlContent.qtzf), 2);
            // 弹出框显示
            popCenter1.isShow = true;
            if (popCenter1.jsjlContent.xjzf == null || popCenter1.jsjlContent.xjzf == '' || popCenter1.jsjlContent.xjzf == '0') {
                $("#ssje").val('0.0');
            } else {
                $("#ssje").val(rightVue.fDec(popCenter1.jsjlContent.xjzf, 2));
            }
            $("#zbje").val("");
            // 参数权限是否允许录入保险金额（此处保险金额还未做处理还有各种限定）
            if (tableInfo.csqxContent.cs00500100110 == '0') {
                $("#ybkzf").attr('disabled', true);
            } else {
                $("#ybkzf").attr('disabled', false);
            }
            Vue.nextTick(function () {
                $('#ssje').focus();
            });
        },
        isNotNullOrEmpty: function (o) {
            if (o == null || typeof (o) == "undefined" || o == '') {
                return false;
            }
            if (Array.isArray(o)) {
                return o.length > 0;
            }
            return true;
        }
    }
})

var popCenter1 = new Vue({
    el: '.popCenter1',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer, payNo, getDataObj],
    data: {
        nbtclb: '',
        payType: {
            '002': '5',
            '003': '4',
            '004': '19',
        },
        bxlbbm: '',
        MzsfPrint: '',
        ifClick: true, // 用于判断是否点击保存按钮
        zflxList: [], // 支付类型集合
        cffyList: [], // 处方费用集合
        jsjlContent: {
            codeContent: '',
        }, // 结算记录对象
        csqxContent: {}, //
        printObj: {}, //
        cfkfxmList: [], // 处方费用
        isShow: false,
        jssb: false,
        socketCd: null,
        bcdxxContent1: {},//补偿单信息
        cancelText: ''// 【取消】 按钮暂时不外放| 收费结算窗口取消按钮，若果无值，表示禁用   （电子医保凭证需要禁用此按钮）
    },
    mounted: function () {

    },
    watch: {},
    methods: {
        //pos支付
        postApply: function () {
            switch (tableInfo.csqxContent.cs05001200324) {
                case "001"://贵州POS支付
                    if (!rightVue.gztyzfCsContent) {
                        malert("请先维护统一支付相关参数！", "right", "defeadted");
                        return;
                    }
                    var inJson = Object.assign({}, rightVue.gztyzfCsContent);

                    inJson.operId = userId;
                    inJson.data = {

                        patId: rightVue.brxxContent.ghxh,  //病人唯一识别码		Y	在HIS中的唯一码
                        cardNo: rightVue.mzjbxxContent.sfzh,     //卡号		Y	如：****************，无特殊字符
                        cardType: '4',   //卡类型		Y	1-就诊卡，2-医保卡，3-居民健康卡，4-身份证
                        statementNo: '',    //结算单据号（收据号）		N	HIS结算收费唯一号 (预算时不生成可以不传)
                        receiptList: '',   //单据列表		Y	多个编号用|分隔 (根据HIS情况可以传空)
                        chargeTypeList: '',     //费用类型列表		N	多个类别用|分隔，与单据列表对应 (根据HIS情况可以传空)
                        personAmtList: '',  //自付金额列表		Y	多个类别用“|”分隔，与单据列表对应，患者应该自己承担的费用（=总金额-优惠金额-医保支付）
                        chargeTamt: 0.01,//popCenter1.jsjlContent.xjzf,     //总金额		Y	预算时返回  两位小数
                        payMoney: 0.01,//popCenter1.jsjlContent.xjzf,   //支付金额		Y	支付金额
                        discountsAmt: '',   //医院优惠金额		Y	预算时返回  保留两位小数
                        personAmt: 0.01,//popCenter1.jsjlContent.xjzf,  //个人支付金额		Y	预算时返回  保留两位小数
                        payWay: '3',    //支付方式		Y	0-无第三方支付（即个人支付金额为0），1-支付宝，2-微信支付，3-银联卡支付
                        payChannel: inJson.appCode,     //支付渠道		Y	参照appCode
                        payTradeno: '',     //平台支付流水号		Y	支付的唯一流水号，用于对账，平台需保存
                        whetherDed: '1',     //是否扣院内账户		Y	0-根据病人医保代码结算，1-自费结算
                        whetherSet: '1',     //是否自费结算		N	0-根据病人医保代码结算，1-自费结算
                        hospitalcardNo: '',     //院内卡号		N	当选择院内卡支付时，卡号不能为空
                        password: '',   //院内支付密码		N	仅用于院内支付时使用
                    };
                    var parameter = {
                        yljgbm: jgbm,
                        czybm: userId,
                        types: 'Clinic',
                        method: 'ClinicAccount',
                        inJson: JSON.stringify(inJson),
                    };
                    // malert(rightVue.mzjbxxContent.ghxh);
                    var param = {zflxjk: "004"};//根据固定支付类型接口查询支付类型编码
                    $.ajaxSettings.async = false;
                    common.openloading(".popCenter1");
                    $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx&json=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            if (json.d && json.d.list && json.d.list.length > 0) {
                                parameter.zflxbm = json.d.list[0].zflxbm;//支付类型编码

                                common.openloading(".popCenter1");
                                popCenter1.$http.post(rightVue.gztyzfCsContent.url,
                                    JSON.stringify(parameter)).then(function (data) {
                                    if (data.body.returnCode == "0") {
                                        var outResult = JSON.parse(data.body.outResult);
                                        popCenter1.jsjlContent.qtzf = 11;
                                        popCenter1.jsjlContent.zflxbm = parameter.zflxbm;
                                        popCenter1.jsjlContent.ifbank = "1";
                                        popCenter1.jsjlContent.payno = '参考号';//退费要用，必须存这个字段
                                        popCenter1.jsjlContent.paychannel = '交易流水';
                                        popCenter1.jsjlContent.bzsm = '门诊POS支付';
                                        malert(data.body.msgInfo, "right", "success");
                                        rightVue.finalMzjs();
                                        popCenter1.$forceUpdate();
                                        common.closeLoading();
                                    } else {
                                        malert(data.body.msgInfo, "right", "defeadted");
                                        common.closeLoading();
                                        return;
                                    }
                                });
                            } else {
                                malert("没有维护银联支付类型！", "right", "defeadted");
                                common.closeLoading();
                                return;
                            }

                        } else {
                            malert(json.c, "right", "defeadted");
                            common.closeLoading();
                            return false;
                        }
                    });
                    break;

                case "004"://甘肃POS接口
                    var inJson = {
                        trans: "00",
                        amount: popCenter1.jsjlContent.xjzf,
                    };
                    var parameter = {
                        yljgbm: jgbm,
                        types: "0",//门诊
                        czybm: userId,
                        hisGrbh: rightVue.brxxContent.ghxh,
                        inJson: JSON.stringify(inJson)
                    };

                    var param = {zflxjk: "004"};//根据固定支付类型接口查询支付类型编码
                    $.ajaxSettings.async = false;
                    common.openloading(".popCenter1");
                    $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx&json=" + JSON.stringify(param), function (json) {

                        if (json.a == 0) {

                            if (json.d && json.d.list && json.d.list.length > 0) {

                                parameter.zflxbm = json.d.list[0].zflxbm;//支付类型编码

                                common.openloading(".popCenter1");
                                popCenter1.$http.post('http://127.0.0.1:9001/posinterface/00',
                                    JSON.stringify(parameter)).then(function (data) {

                                    if (data.body.returnCode == "0") {
                                        var outResult = JSON.parse(data.body.outResult);
                                        popCenter1.jsjlContent.qtzf = outResult.amount;
                                        popCenter1.jsjlContent.zflxbm = parameter.zflxbm;
                                        popCenter1.jsjlContent.ifbank = "1";
                                        popCenter1.jsjlContent.payno = outResult.refer;
                                        popCenter1.jsjlContent.paychannel = outResult.trace;
                                        popCenter1.jsjlContent.bzsm = outResult.bzsm;
                                        malert(data.body.msgInfo, "right", "success");
                                        rightVue.finalMzjs();
                                        popCenter1.$forceUpdate();
                                        common.closeLoading();
                                    } else {
                                        malert(data.body.msgInfo, "right", "defeadted");
                                        common.closeLoading();
                                        return;
                                    }
                                });
                            } else {
                                malert("没有维护银联支付类型！", "right", "defeadted");
                                common.closeLoading();
                                return;
                            }

                        } else {
                            malert(json.c, "right", "defeadted");
                            common.closeLoading();
                            return false;
                        }
                    });
                    break;
                default:
                    malert("未开通POS支付接口！", "right", "defeadted");
                    return;
                    break;
            }
            // 支付方式联动
            this.zffsld(0);
        },

        // 支付方式联动
        zffsld: function (index) {
            if (tableInfo.csqxContent.N05001200331) {
                var zffsArr = tableInfo.csqxContent.N05001200331.split(':');
                Vue.set(popCenter1.jsjlContent, 'zflxbm', zffsArr[index]);
                Vue.set(popCenter1.jsjlContent, 'zflxmc',
                    popCenter1.listGetName(popCenter1.zflxList, zffsArr[index], 'zflxbm', 'zflxmc'));
            }
        },
        wxApply: function () {//微信支付
            switch (tableInfo.csqxContent.cs05001200327) {
                case "001"://貴州微信支付接口
                    $('#codeContent').attr("disabled", false);
                    $("#codeContent").focus();
                    common.openloading(".popCenter1", "请出示付款码。。。。");
                    popCenter1.setTimeOutForWechatPay(0, "002");
                    break;
                case "004"://甘肃微信支付接口
                    $('#codeContent').attr("disabled", false);
                    $("#codeContent").focus();
                    common.openloading(".popCenter1", "请出示付款码。。。。");
                    popCenter1.setTimeOutForWechatPay(0, "002");
                    break;
                default:
                    malert("未开通微信支付接口！", "right", "defeadted");
                    return;
                    break;
            }
            this.zffsld(1);
        },
        zfbApply: function () {//支付宝
            switch (tableInfo.csqxContent.cs05001200328) {
                case "001"://貴州支付宝支付接口
                    $('#codeContent').attr("disabled", false);
                    $("#codeContent").focus();
                    common.openloading(".popCenter1", "请出示付款码。。。。");
                    popCenter1.setTimeOutForAliPay(0, "003");
                    break;
                case "004"://甘肃支付宝接口
                    $('#codeContent').attr("disabled", false);
                    $("#codeContent").focus();
                    common.openloading(".popCenter1", "请出示付款码。。。。");
                    popCenter1.setTimeOutForAliPay(0, "003");
                    break;
                default:
                    malert("未开通支付宝支付接口！", "right", "defeadted");
                    return;
                    break;
            }
            // 支付方式联动
            this.zffsld(2);
        },

        setTimeOutForWechatPay: function (time, zflxjk) {
            if (time < 60000) {
                if (popCenter1.jsjlContent.codeContent && popCenter1.jsjlContent.codeContent.length >= 18) {
                    $("#codeContent").blur();
                    $('#codeContent').attr("disabled", "disabled");
                    switch (tableInfo.csqxContent.cs05001200328) {
                        case "001"://贵州微信支付
                            popCenter1.scanToPayForGuiZhou(zflxjk, "01");//第二个参数可以用来区分具体是门诊还是住院的什么交易
                            break;
                        case "004"://临夏微信支付
                            popCenter1.scanToPayForGanSu(zflxjk, "01");
                            break;
                    }
                } else {
                    setTimeout(function () {
                        time += 1000;
                        popCenter1.setTimeOutForWechatPay(time, zflxjk);
                    }, 1000);
                }
            } else {
                malert("已等待一分钟！", "right", "defeadted");
                $('#codeContent').attr("disabled", false);
                popCenter1.jsjlContent.codeContent = "";
                common.closeLoading();
                return;
            }
        },

        setTimeOutForAliPay: function (time, zflxjk) {
            if (time < 60000) {
                if (popCenter1.jsjlContent.codeContent && popCenter1.jsjlContent.codeContent.length >= 18) {
                    switch (tableInfo.csqxContent.cs05001200327) {
                        case "001"://贵州
                            popCenter1.scanToPayForGuiZhou(zflxjk, "01");//第二个参数可以用来区分具体是门诊还是住院的什么交易
                            break;
                        case "004"://临夏
                            popCenter1.scanToPayForGanSu(zflxjk, "02");
                            break;
                    }
                } else {
                    setTimeout(function () {
                        time += 1000;
                        popCenter1.setTimeOutForWechatPay(time, zflxjk);
                    }, 1000);
                }
            } else {
                malert("已等待一分钟！", "right", "defeadted");
                popCenter1.jsjlContent.codeContent = "";
                common.closeLoading();
                return;
            }
        },

        //甘肃扫码支付
        scanToPayForGanSu: function (zflxjk, types) {//甘肃扫码支付
            var inJson = {
                trans: "60",
                amount: popCenter1.jsjlContent.xjzf,
                szQRCode: popCenter1.jsjlContent.codeContent
            };
            var parameter = {
                yljgbm: jgbm,
                types: types,//门诊
                czybm: userId,
                hisGrbh: rightVue.brxxContent.ghxh,
                inJson: JSON.stringify(inJson)
            };

            var param = {zflxjk: zflxjk};//根据固定支付类型接口查询支付类型编码
            $.ajaxSettings.async = false;
            common.openloading(".popCenter1", "扫码成功，正在支付。。。。。");
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {

                    if (json.d && json.d.list && json.d.list.length > 0) {

                        parameter.zflxbm = json.d.list[0].zflxbm;//支付类型编码

                        common.openloading(".popCenter1");
                        popCenter1.$http.post('http://127.0.0.1:9001/posinterface/60',
                            JSON.stringify(parameter)).then(function (data) {
                            if (data.body.returnCode == "0") {

                                var outResult = JSON.parse(data.body.outResult);
                                popCenter1.jsjlContent.qtzf = outResult.amount;
                                popCenter1.jsjlContent.zflxbm = parameter.zflxbm;
                                popCenter1.jsjlContent.ifbank = "1";
                                popCenter1.jsjlContent.payno = outResult.orderNo;//二维码订单号
                                popCenter1.jsjlContent.paychannel = outResult.trace;//流水号
                                popCenter1.jsjlContent.orderNo = outResult.refer;//参考号
                                popCenter1.jsjlContent.bzsm = outResult.bzsm;
                                popCenter1.jsjlContent.codeContent = "";
                                malert(data.body.msgInfo, "right", "success");
                                rightVue.finalMzjs();
                                popCenter1.$forceUpdate();
                                common.closeLoading();
                            } else {
                                malert(data.body.msgInfo, "right", "defeadted");
                                popCenter1.jsjlContent.codeContent = "";
                                common.closeLoading();
                                return;
                            }
                        });
                    } else {
                        malert("没有维护支付宝/微信支付类型！", "right", "defeadted");
                        popCenter1.jsjlContent.codeContent = "";
                        common.closeLoading();
                        return;
                    }
                } else {
                    malert(json.c, "right", "defeadted");
                    popCenter1.jsjlContent.codeContent = "";
                    common.closeLoading();
                    return false;
                }
            });
        },
        //贵州扫码支付
        scanToPayForGuiZhou: function (zflxjk, types) {

            if (!rightVue.gztyzfCsContent) {
                malert("请先维护统一支付相关参数！", "right", "defeadted");
                return;
            }
            var inJson = Object.assign({}, rightVue.gztyzfCsContent);//获取参数，合并陈一个对象

            // var payWay = "";
            // if(zflxjk == "002"){//微信
            //     payWay = "5";
            // }else if (zflxjk=='0'){
            //     payWay = "4";
            // }else {
            //     payWay = "19";
            // }
            inJson.operId = userId;

            inJson.data = {
                zflxjk: zflxjk,//待完善
                czry: userId,
                subject: '门诊收费室收费',
                storeId: '0088',//业务窗口
                type: '0',//用于贵州统一支付区分门诊还是住院收费，0门诊，住院
                authCode: popCenter1.jsjlContent.codeContent,
                patId: rightVue.brxxContent.ghxh,  //病人唯一识别码		Y	在HIS中的唯一码
                cardNo: rightVue.mzjbxxContent.sfzh,     //卡号		Y	如：****************，无特殊字符
                cardType: '4',   //卡类型		Y	1-就诊卡，2-医保卡，3-居民健康卡，4-身份证
                statementNo: '',    //结算单据号（收据号）		N	HIS结算收费唯一号 (预算时不生成可以不传)
                receiptList: '',   //单据列表		Y	多个编号用|分隔 (根据HIS情况可以传空)
                chargeTypeList: '',     //费用类型列表		N	多个类别用|分隔，与单据列表对应 (根据HIS情况可以传空)
                personAmtList: '',  //自付金额列表		Y	多个类别用“|”分隔，与单据列表对应，患者应该自己承担的费用（=总金额-优惠金额-医保支付）
                chargeTamt: $("#ssje").val(),//popCenter1.jsjlContent.xjzf,     //总金额		Y	预算时返回  两位小数
                payMoney: $("#ssje").val(),//popCenter1.jsjlContent.xjzf,   //支付金额		Y	支付金额
                discountsAmt: '',   //医院优惠金额		Y	预算时返回  保留两位小数
                personAmt: $("#ssje").val(),//popCenter1.jsjlContent.xjzf,  //个人支付金额		Y	预算时返回  保留两位小数
                payWay: this.payType[zflxjk],    //支付方式		Y	0-无第三方支付（即个人支付金额为0），4-支付宝，5-微信支付，19-银联扫码
                payChannel: inJson.appCode,     //支付渠道		Y	参照appCode
                payTradeno: '',     //平台支付流水号		Y	支付的唯一流水号，用于对账，平台需保存
                whetherDed: '1',     //是否扣院内账户		Y	0-根据病人医保代码结算，1-自费结算
                whetherSet: '1',     //是否自费结算		N	0-根据病人医保代码结算，1-自费结算
                hospitalcardNo: '',     //院内卡号		N	当选择院内卡支付时，卡号不能为空
                password: '',   //院内支付密码		N	仅用于院内支付时使用
                brmc: popCenter1.jsjlContent.brxm,//病人姓名
                brxh: rightVue.fzContent['ryghxh'],//病人挂号序号
            };
            var parameter = {
                yljgbm: jgbm,
                czybm: userId,
                types: 'Clinic',
                method: 'ClinicAccount',
                inJson: JSON.stringify(inJson),
            };
            var param = {zflxjk: zflxjk};//根据固定支付类型接口查询支付类型编码
            $.ajaxSettings.async = false;
            common.openloading(".popCenter1");
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {

                    if (json.d && json.d.list && json.d.list.length > 0) {

                        parameter.zflxbm = json.d.list[0].zflxbm;//支付类型编码

                        common.openloading(".popCenter1");
                        popCenter1.$http.post(rightVue.gztyzfCsContent.url,//请求统一支付
                            JSON.stringify(parameter)).then(function (data) {
                            if (data.body.returnCode == "0") {
                                var outResult = JSON.parse(data.body.outResult);

                                popCenter1.jsjlContent.qtzf = inJson.data.payMoney;
                                popCenter1.jsjlContent.zflxbm = parameter.zflxbm;
                                popCenter1.jsjlContent.ifbank = "1";
                                popCenter1.jsjlContent.payno = data.body.payno;//'参考号';//退费要用，必须存这个字段
                                popCenter1.jsjlContent.paychannel = data.body.tradeNo;
                                popCenter1.jsjlContent.orderNo = popCenter1.jsjlContent.codeContent;//参考号,二维码
                                popCenter1.jsjlContent.bzsm = this.payType[zflxjk] == 4 ? "门诊支付宝支付" : (this.payType[zflxjk] == 5 ? "门诊微信支付" : "门诊pos支付");
                                popCenter1.jsjlContent.codeContent = "";

                                malert(data.body.msgInfo, "right", "success");
                                rightVue.finalMzjs();
                                popCenter1.$forceUpdate();
                                common.closeLoading();
                                popCenter1.success();
                            } else {
                                malert(data.body.msgInfo, "right", "defeadted");
                                popCenter1.jsjlContent.codeContent = "";
                                common.closeLoading();
                                return;
                            }
                        });
                    } else {
                        malert("没有维护银联支付类型！", "right", "defeadted");
                        popCenter1.jsjlContent.codeContent = "";
                        common.closeLoading();
                        return;
                    }

                } else {
                    malert(json.c, "right", "defeadted");
                    popCenter1.jsjlContent.codeContent = "";
                    common.closeLoading();
                    return false;
                }
            });
        },


        yhhj: function (value, event) {
            this.jsjlContent.xjzf = rightVue.fDec(parseFloat(this.jsjlContent.ysje) - parseFloat(value == '' ? 0 : value)
                - parseFloat(this.jsjlContent.qtzf) - parseFloat(popCenter1.jsjlContent.ybzhzf) - parseFloat(popCenter1.jsjlContent.ybtczf), 2)
            $("#ssje").val(popCenter1.fDec(popCenter1.jsjlContent.xjzf, 2));
            this.$forceUpdate()
        },
        zjjs: function (event) {
            if ($('#ssje').val().length > 10) {
                malert('支付金额有问题，有可能是微信或者是支付宝支付码，请注意', 'right', 'defeadted');
                return false;
            }
            this.success();
        },

        // 实收金额改变时找补金额应相应变化
        ssjeChange: function () {
            let couponAmount = parseFloat(this.jsjlContent.couponAmount) || 0;  // 处理 NaN
            let ssje = $("#ssje").val();
            if (ssje == null || ssje == "") {
                ssje = '0.0';
            }
            if(couponAmount !="" && couponAmount > 0){
                $("#zbje").val(popCenter1.fDec(parseFloat(couponAmount) + parseFloat(ssje) - parseFloat(popCenter1.jsjlContent.xjzf), 2));
            }else{
                $("#zbje").val(popCenter1.fDec(parseFloat(ssje) - parseFloat(popCenter1.jsjlContent.xjzf), 2));
            }
        },
        quanChange: function () {
            //当券金额<应收金额 券优惠=券金额 实收金额=应收金额-券金额
            //当券金额>=应收金额 券优惠=应收金额 实收金额=0.00
            var zbje = $("#zbje").val();
            var ssje = $("#ssje").val();
            let xjzf = parseFloat(popCenter1.jsjlContent.xjzf) || 0;
            let couponAmount = parseFloat(this.jsjlContent.couponAmount) || 0;  // 处理 NaN
            if (xjzf > couponAmount) {
                this.jsjlContent.couponPrice = couponAmount;
                $("#ssje").val(this.fDec(xjzf - couponAmount, 2));
                if(zbje != 0){
                    $("#zbje").val('0');
                }
            } else if (xjzf <= couponAmount) {
                this.jsjlContent.couponPrice = xjzf;
                $("#ssje").val('0.0');
            }
        },
        // 其他支付
        qtzfChange: function (event) {
            if (this.jsjlContent.qtzf < 0 || this.jsjlContent.qtzf == '') {
                this.jsjlContent.qtzf = 0;
                malert("其他支付不允许小于0", 'right', 'defeadted');
            }
            var fy1 = parseFloat(this.jsjlContent.fyhj).toFixed(2) * 100;
            var fy2 = parseFloat(this.jsjlContent.ybkzf).toFixed(2) * 100;
            var fy3 = parseFloat(this.jsjlContent.ylkzf).toFixed(2) * 100;
            var fy4 = parseFloat(this.jsjlContent.qtzf).toFixed(2) * 100;
            var je = (fy1 - fy2 - fy3 - fy4) / 100;
            if (je < 0) {
                this.jsjlContent.qtzf = 0;
                malert("其他支付不允许大于现金支付", 'right', 'defeadted');
                return false
            }
            Vue.set(this.jsjlContent, 'xjzf', popCenter1.fDec(je, 2));
            this.jsjlContent = Object.assign({}, this.jsjlContent);
            $("#ssje").val(popCenter1.fDec(popCenter1.jsjlContent.xjzf, 2));
            this.nextFocus(event)
        },

        quxiao: function () {

            //@HUIHUI 成都银海医保
                if (rightVue.fzContent.bxjk == 'B07' && rightVue.gbjsxx != null) {
                    let ryxx = JSON.parse(sessionStorage.getItem('hzybxx'));
                    let jyxx = '';
                    if(sessionStorage.getItem('jyjbxx')){
                        jyxx = JSON.parse(sessionStorage.getItem('jyjbxx'));
                    }


                    if(rightVue.gjjsxx){

                        let gjryxx = '';
                        if(sessionStorage.getItem('gjhzybxx')){
                            gjryxx = JSON.parse(sessionStorage.getItem('gjhzybxx'));
                        }

                        let gjjyxx = '';
                        if(sessionStorage.getItem('gjjyjbxx')){
                            gjjyxx = JSON.parse(sessionStorage.getItem('gjjyjbxx'));
                        }

                        if(gjryxx ){

                            let param_S2102 = {
                                data:{
                                    "psn_no":rightVue.gjjsxx.result.psn_no, //人员编号
                                    "setl_id":rightVue.gjjsxx.result.setl_id, //结算id
                                    "mdtrt_id":rightVue.gjjsxx.result.mdtrt_id, //就诊id
                                    "exp_content":{
                                        "card_token":gjjyxx.card_token
                                    },
                                }
                            }
                            let data = window.insuranceGbUtils.call1("S2102",param_S2102,gjryxx.insuplc_admdvs,cd_014gj.sfyd);
                            if(!data){
                                malert("取消交易失败", 'top', 'defeadted');
                                return false;
                            }
                            let jsxx ={
                                jzid:rightVue.gjjsxx.result.mdtrt_id,
                                jsid:rightVue.gjjsxx.result.setl_id,

                                zfbz:'1',
                                zfry:userName,
                                zfrq:this.fDate(new Date(), 'datetime'),

                            }

                            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=upGbjsxx&parm="
                                + JSON.stringify(jsxx),
                                function (json) {
                                    if (json.a != 0) {
                                        malert("保存失败  " + json.c, 'right', 'defeadted');
                                    }
                                });


                            let tjsxx ={
                                jzid:data.result.mdtrt_id,
                                jsid:data.result.setl_id,
                                mz:data.result.naty,
                                csrq:data.result.brdy,
                                nl:data.result.age,
                                xzlx:data.result.insutype,
                                rylb:data.result.psn_type,
                                gwybz:data.result.cvlserv_flag,
                                jssj:data.result.setl_time,
                                jzpzlx:data.result.mdtrt_cert_type,
                                yllb:data.result.med_type,
                                ylfze:data.result.medfee_sumamt,
                                cxjzffy:data.result.overlmt_selfpay,
                                xxzfje:data.result.preselfpay_amt,
                                fhzcfwje:data.result.inscp_scp_amt,
                                jbylbxtcjjzc:data.result.hifp_pay,
                                jbylbxtcjjzfbl:data.result.pool_prop_selfpay,
                                gwyylbzzjzc:data.result.cvlserv_pay,
                                qybcylbxjjzc:data.result.hifes_pay,
                                jmdbbxzjzc:data.result.hifmi_pay,
                                zgdeylfybzjjzc:data.result.hifob_pay,
                                yljzjjzc:data.result.maf_pay,
                                qtzc:data.result.oth_pay,
                                jjzfze:data.result.fund_pay_sumamt,
                                sjzfqfx:data.result.act_pay_dedc,
                                grfdzje:data.result.psn_part_amt,
                                ryzjlx:data.result.psn_cert_type,
                                grzhzc:data.result.acct_pay,
                                rybh:data.result.psn_no,
                                qzfje:data.result.fulamt_ownpay_amt,
                                zjhm:data.result.certno,
                                zrxjzc:data.result.psn_cash_pay,
                                yyfdje:data.result.hosp_part_amt,
                                xb:data.result.gend,
                                ye:data.result.balc,
                                ryxm:data.result.psn_name,
                                grzhzjzfje:data.result.acct_mulaid_pay,
                                yyjgjsid:(data.msgid?data.msgid:data.result.medins_setl_id),
                                qsjbjg:data.result.clr_optins,
                                qsfs:data.result.clr_way,
                                qslb:data.result.clr_type,
                                zfbz:'0',
                                zfry:'',
                                zfrq:'',
                                jszt:'1',
                                jsczy:userName,
                                grjsfs:'01',
                                sfpch:'',
                                fph:'',
                                cjrq:data.result.setl_time,
                                tfbs:'1',
                                tfjsid:rightVue.gjjsxx.result.setl_id,
                            }
                            console.log(JSON.stringify(tjsxx));
                            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjsxx&parm="
                                + JSON.stringify(tjsxx),
                                function (json) {
                                    if (json.a != 0) {

                                    }
                                });

                            let tjsjjfx = [];
                            for (let i = 0; i < data.setldetail.length; i++) {

                                tjsjjfx.push({
                                    jzid:data.result.mdtrt_id,
                                    jsid:data.result.setl_id,
                                    jjzflx:data.setldetail[i].fund_pay_type,
                                    fhzcfwje:data.setldetail[i].inscp_scp_amt,
                                    bckzfxeje:data.setldetail[i].crt_payb_lmt_amt,
                                    jjzfje:data.setldetail[i].fund_payamt,
                                    jjzflxmc:data.setldetail[i].fund_pay_type_name,
                                    jsgcxx:data.setldetail[i].setl_proc_info?data.setldetail[i].setl_proc_info.replace(/%/g,"/"):'',

                                })
                            }
                            console.log(JSON.stringify(tjsjjfx));
                            if(tjsjjfx.length>0){
                                $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjjfxxx&parm="
                                    + JSON.stringify(tjsjjfx),
                                    function (json) {
                                        if (json.a != 0) {

                                        }
                                    });
                            }



                        }




                    }


                    for (let i = rightVue.gbjsxx.length-1; i >=0 ; i--) {


						let param_08 = {
							data:{
								"psn_no":rightVue.gbjsxx[i].psn_no, //人员编号
								"setl_id":rightVue.gbjsxx[i].setl_id, //结算id
								"mdtrt_id":rightVue.gbjsxx[i].mdtrt_id, //就诊id
								"exp_content":{
									"card_token":jyxx.card_token
								},
							}
						}
						let data = window.insuranceGbUtils.call1("2208",param_08,ryxx.insuplc_admdvs,cd_014.sfyd);
						if(!data){
							malert("取消交易失败", 'top', 'defeadted');
							return false;
						}
						let jsxx ={
							jzid:rightVue.gbjsxx[i].mdtrt_id,
							jsid:rightVue.gbjsxx[i].setl_id,

							zfbz:'1',
							zfry:userName,
							zfrq:this.fDate(new Date(), 'datetime'),

						}

						$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=upGbjsxx&parm="
						    + JSON.stringify(jsxx),
						    function (json) {
						        if (json.a != 0) {
						          malert("保存失败  " + json.c, 'right', 'defeadted');
						        }
						    });


						let tjsxx ={
							jzid:data.setlinfo.mdtrt_id,
							jsid:data.setlinfo.setl_id,
							mz:data.setlinfo.naty,
							csrq:data.setlinfo.brdy,
							nl:data.setlinfo.age,
							xzlx:data.setlinfo.insutype,
							rylb:data.setlinfo.psn_type,
							gwybz:data.setlinfo.cvlserv_flag,
							jssj:data.setlinfo.setl_time,
							jzpzlx:data.setlinfo.mdtrt_cert_type,
							yllb:data.setlinfo.med_type,
							ylfze:data.setlinfo.medfee_sumamt,
							cxjzffy:data.setlinfo.overlmt_selfpay,
							xxzfje:data.setlinfo.preselfpay_amt,
							fhzcfwje:data.setlinfo.inscp_scp_amt,
							jbylbxtcjjzc:data.setlinfo.hifp_pay,
							jbylbxtcjjzfbl:data.setlinfo.pool_prop_selfpay,
							gwyylbzzjzc:data.setlinfo.cvlserv_pay,
							qybcylbxjjzc:data.setlinfo.hifes_pay,
							jmdbbxzjzc:data.setlinfo.hifmi_pay,
							zgdeylfybzjjzc:data.setlinfo.hifob_pay,
							yljzjjzc:data.setlinfo.maf_pay,
							qtzc:data.setlinfo.oth_pay,
							jjzfze:data.setlinfo.fund_pay_sumamt,
							sjzfqfx:data.setlinfo.act_pay_dedc,
							grfdzje:data.setlinfo.psn_part_amt,
							ryzjlx:data.setlinfo.psn_cert_type,
							grzhzc:data.setlinfo.acct_pay,
							rybh:data.setlinfo.psn_no,
							qzfje:data.setlinfo.fulamt_ownpay_amt,
							zjhm:data.setlinfo.certno,
							zrxjzc:data.setlinfo.psn_cash_pay,
							yyfdje:data.setlinfo.hosp_part_amt,
							xb:data.setlinfo.gend,
							ye:data.setlinfo.balc,
							ryxm:data.setlinfo.psn_name,
							grzhzjzfje:data.setlinfo.acct_mulaid_pay,
							yyjgjsid:(data.msgid?data.msgid:data.setlinfo.medins_setl_id),
							qsjbjg:data.setlinfo.clr_optins,
							qsfs:data.setlinfo.clr_way,
							qslb:data.setlinfo.clr_type,
							zfbz:'0',
							zfry:'',
							zfrq:'',
							jszt:'1',
							jsczy:userName,
							grjsfs:'01',
							sfpch:'',
							fph:'',
							cjrq:data.setlinfo.setl_time,
							tfbs:'1',
							tfjsid:rightVue.gbjsxx[i].setl_id,
						}
							console.log(JSON.stringify(tjsxx));
						$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjsxx&parm="
						    + JSON.stringify(tjsxx),
						    function (json) {
						        if (json.a != 0) {

						        }
						    });

						let tjsjjfx = [];
						for (let i = 0; i < data.setldetail.length; i++) {

							tjsjjfx.push({
								jzid:data.setlinfo.mdtrt_id,
								jsid:data.setlinfo.setl_id,
								jjzflx:data.setldetail[i].fund_pay_type,
								fhzcfwje:data.setldetail[i].inscp_scp_amt,
								bckzfxeje:data.setldetail[i].crt_payb_lmt_amt,
								jjzfje:data.setldetail[i].fund_payamt,
								jjzflxmc:data.setldetail[i].fund_pay_type_name,
								jsgcxx:data.setldetail[i].setl_proc_info?data.setldetail[i].setl_proc_info.replace(/%/g,"/"):'',

							})
						}
						console.log(JSON.stringify(tjsjjfx));
						if(tjsjjfx.length>0){
							$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=gbjjfxxx&parm="
							    + JSON.stringify(tjsjjfx),
							    function (json) {
							        if (json.a != 0) {

							        }
							    });
						}





                    }
                    if(rightVue.xtjs){
                        rightVue.xtch(rightVue.yhResult,null,rightVue.med_type,null,rightVue.xtjs,jyxx,ryxx,rightVue.jzids)
                    }



					rightVue.gbjsxx = [];
                }

            popCenter1.jsjlContent.codeContent = "";
            popCenter1.isShow = false;
            popCenter1.ifClick = true;
        },


        success: function () {
            if (!this.ifClick) return; // 已经点击过就不能再点
            this.ifClick = false;
			$.ajaxSettings.async = false;
            //调用保险接口且为建筑医院接口且非电子医保凭证
            if (typeof (rightVue.fzContent.bxjk) != "undefined" && rightVue.fzContent.bxjk == 'B07') {
				if (rightVue.fzContent.bxjk != null && rightVue.fzContent.bxjk == 'B07') {
				    switch (rightVue.fzContent.bxjk) {
				        //成都银海医保
				        case 'B07':
				            //进行医保结算确认
				            var result_014 = true;


				            if (result_014) {

								var arr = [];
								for(var i = 0; i < rightVue.gbjsxx.length; i++){
									if(i == 0) {
										arr.push(JSON.parse(JSON.stringify(rightVue.gbjsxx[i])));
									}
									b = false;
									if(arr.length > 0 && i > 0){
										for(var j = 0; j < arr.length; j++){
											if(arr[j].setl_id == rightVue.gbjsxx[i].setl_id){
												b = true;
											//break;
											}
										}
										if(!b){
											arr.push(JSON.parse(JSON.stringify(rightVue.gbjsxx[i])));
											}
									}
								}
								rightVue.gbjsxx = arr;



								for (let i = 0; i < rightVue.gbjsxx.length; i++) {
									var parm = {
									    ybfyhj: rightVue.gbjsxx[i].fund_pay_sumamt,
									    ghxh: rightVue.gbjsxx[i].ghxh,  //'挂号序号';
									    zfbz: '0',  //'作废标志';
									    yljgbm: jgbm,    //'医疗机构编码';
									    aac003: rightVue.gbjsxx[i].psn_name,
									    skrgx: cd_014.grxxJson.skrgx,
									    akc190:  rightVue.gbjsxx[i].mdtrt_id ,    //'就诊编码';
									    aka130:  rightVue.gbjsxx[i].insutype ,    //'支付类别';
									    ykd007:  rightVue.gbjsxx[i].insutype ,    //'报销类型';
									    aac001:  rightVue.gbjsxx[i].psn_no ,    //'个人编码';
									    akb020: window.insuranceGbUtils.fixmedins_code,    //'医院编码';
									    yka103:  rightVue.gbjsxx[i].setl_id ,    //'结算编号';
									    yka055:  rightVue.gbjsxx[i].medfee_sumamt ,    //'费用总额';
									    yka056:  rightVue.gbjsxx[i].fulamt_ownpay_amt ,    //'全自费';
									    ykc303:  rightVue.gbjsxx[i].psn_type ,    //'个人帐户种类';
									    ykc194:  rightVue.gbjsxx[i].balc ,    //'个人帐户余额';
									    yka065:  rightVue.gbjsxx[i].acct_pay ,    //'个人帐户支付总额';
									    yka107:  rightVue.gbjsxx[i].fund_pay_sumamt ,    //'社保基金支付总额';
									    ykh012:  rightVue.gbjsxx[i].psn_cash_pay ,    //'现金及其他自付';
									    yab003:  rightVue.gbjsxx[i].medins_setl_id ,    //'医保经办机构编号';
									    aae011: userName,    //'经办人姓名';
									    aae036: rightVue.gbjsxx[i].setl_time,    //'经办时间';
									    yka111: rightVue.gbjsxx[i].inscp_scp_amt,    //'符合范围';
									    yka057: rightVue.gbjsxx[i].fulamt_ownpay_amt,    //'挂钩自付';
									    grzhye: rightVue.gbjsxx[i].balc,    //'个人账户余额';
									    ykc117: rightVue.gbjsxx[i].balc,    //'个人账户余额';
									    akc021: cd_014.grxxJson.med_type,    //医疗人员类别
									    jylsh: '', //'交易流水号';
									    jyyzm: '', //'交易验证码';
									    jslx: '',//结算类型
									    ydbz: '',//异地标志
										ykc177:cd_014.grxxJson.ykc177, //公务员标志
										insuplcadmdvs:cd_014.grxxJson.insuplc_admdvs,//参保地区划
									};
									var dataset_rows = [];

									dataset_rows.push({
										bah:rightVue.gbjsxx[i].ghxh,
										bxjsh:rightVue.gbjsxx[i].setl_id,
										jzlx:'0',
										yab139:cd_014.grxxJson.insuplc_admdvs,
										aka213:'',
										yka115:rightVue.gbjsxx[i].act_pay_dedc,
										yka058:'',
										ykc125:'',
										yka107:rightVue.gbjsxx[i].fund_pay_sumamt,
										yka065:rightVue.gbjsxx[i].acct_pay,
										ykc121:'11',
										ykb037:rightVue.gbjsxx[i].clr_optins,
										yka054:rightVue.gbjsxx[i].clr_way,
										yka316:rightVue.gbjsxx[i].clr_type,
										jssj:rightVue.gbjsxx[i].setl_time,
										jsry:userId,
										jsryxm:userName,

									})
									parm.qslbmx = dataset_rows;
									parm = '{"list":' + JSON.stringify(parm) + '}';
									parm = parm.replace(/undefined/g, "");
									parm = parm.replace(/NaN/g, "");
									parm = parm.replace(/null/g, "");
									var flag = true;
									rightVue.postAjax("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + cd_014.bxurl + "&bxlbbm=" + cd_014.bxlbbm + "&types=mzjy&method=mzjs", parm,
									    function (data) {
									        if (data.a == '0') {
									            flag = true;
									            rightVue.zxlshSn = rightVue.yjsContentGzyhyb.yka103;
									            malert(data.c, 'right', 'success');
									        } else {
									            flag = false
									            malert(data.c, 'right', 'defeadted');
									            popCenter1.ifClick = true;
									            //HIS结算失败后，调用医保冲红
									            popCenter1.quxiao();
									        }
									    });

									if (!flag) {
									    return false
									}
								}


				            }
				            break;
				        default:
				            malert("暂为开放此保险接口！", 'right', 'defeadted');
				            this.ifClick = true;
				            break;
				    }
				}
                this.successV2();
                return;
            }
            var ssje = $("#ssje").val();
            if (ssje == '0') { // 当实收金额为0
                if (tableInfo.csqxContent.cs00500100109 == '0') { // 参数权限控制实收金额是否允许为0----0不允许
                    malert("收费金额不允许为0", 'right', 'defeadted');
                    this.ifClick = true;
                    return
                }
            }
            if (!ssje) {
                malert("收费金额不允许为空", 'right', 'defeadted');
                this.ifClick = true;
                return
            }
            if ((this.jsjlContent.couponPrice + ssje) * 1 < this.jsjlContent.xjzf * 1) {
                malert("收费金额不允许小于现金支付金额", 'right', 'defeadted');
                this.ifClick = true;
                return
            }
            //如果有优惠额, 现金支付需要减去优惠额
            if(this.jsjlContent.useCoupon && this.jsjlContent.couponPrice > 0){
                this.jsjlContent.xjzf = popCenter1.fDec(parseFloat(popCenter1.jsjlContent.xjzf) - parseFloat(this.jsjlContent.couponPrice), 2)
            }
            this.jsjlContent.zflxmc = this.listGetName(this.zflxList, this.jsjlContent.zflxbm, 'zflxbm', 'zflxmc');
            rightVue.gznhType = false;
            this.saveSf()
        },
        successV2: function () {
            rightVue.mzyjsExceptionResult = [];
            let ghxh = rightVue.brxxContent['ghxh'];
            var ssje = $("#ssje").val();
            if (ssje == '0') { // 当实收金额为0
                if (tableInfo.csqxContent.cs00500100109 == '0') { // 参数权限控制实收金额是否允许为0----0不允许
                    malert("收费金额不允许为0", 'right', 'defeadted');
                    this.ifClick = true;
                    return
                }
            }
            if (!ssje) {
                malert("收费金额不允许为空", 'right', 'defeadted');
                this.ifClick = true;
                return
            }
            if (ssje * 1 < this.jsjlContent.xjzf * 1) {
                malert("收费金额不允许小于现金支付金额", 'right', 'defeadted');
                this.ifClick = true;
                return
            }
            this.jsjlContent.zflxmc = this.listGetName(this.zflxList, this.jsjlContent.zflxbm, 'zflxbm', 'zflxmc');
            if (rightVue.fzContent.bxjk != null && rightVue.zdbx) {
                rightVue.gznhType = false;
                this.saveSf()
                if (rightVue.isNotNullOrEmpty(rightVue.mzyjsExceptionResult)) {
                    let msg = this.getErrorMsg(rightVue.mzyjsExceptionResult);
                    rightVue.brxxContent['ghxh'] = ghxh;
                    //重新加载页面
                    rightVue.getData(ghxh);
                    malert(msg, 'top', 'defeadted');
                }
            }
        },
        /**
         * 封装医保接口上传参数
         * @param output
         * @returns {{akc021: *, yka107: *, aac001: *, yljgbm: string|*, yka065: *, skrgx: *|pn.grxxJson.skrgx, yka103: *, ykc303: *, ykc194: *, ybfyhj: *, aac003: string|*, ydbz: string|*, jylsh: *, aae011: *, ghxh: *, aka130: *, akb020: *, jslx: *, aae036: string|*, yab003: *, grzhye: number|*, yka057: *, ykd007: *, yka056: *, yka111: *, yka055: string|*, ykc117: *, jyyzm: *, ykh012: *, akc190: *, zfbz: string}}
         */
        ybscParm: function (output) {
            var jyscJosn = JSON.parse(output.astr_jysc_xml);
            // todo 上传医保接口
			let ykc303 = '';
			if(jyscJosn.grzhye && jyscJosn.grzhye.ykc303){
				ykc303 = jyscJosn.grzhye.ykc303
			}
			let ykc194 = '';
			if(jyscJosn.grzhye && jyscJosn.grzhye.ykc194){
				ykc194 = jyscJosn.grzhye.ykc194
			}
            var parm = {
                ybfyhj: rightVue.yjsContentGzyhyb.ybfyhj,//todo
                ghxh: rightVue.mzjbxxContent.ghxh,  //'挂号序号';
                zfbz: '0',  //'作废标志';
                yljgbm: jgbm,    //'医疗机构编码';
                aac003: cd_014.grxxJson.aac003,
                skrgx: cd_014.grxxJson.skrgx,
                akc190: jyscJosn.akc190,    //'就诊编码';
                aka130: jyscJosn.aka130,    //'支付类别';
                ykd007: jyscJosn.ykd007,    //'报销类型';
                aac001: jyscJosn.aac001,    //'个人编码';
                akb020: jyscJosn.akb020,    //'医院编码';
                yka103: jyscJosn.yka103,    //'结算编号';
                yka055: jyscJosn.yka055,    //'费用总额';
                yka056: jyscJosn.yka056,    //'全自费';
                ykc303: this.getGrzhye(jyscJosn, 'ykc303'),    //'个人帐户种类';
                ykc194: this.getGrzhye(jyscJosn, 'ykc194'),    //'个人帐户余额';
                yka065: jyscJosn.yka065,    //'个人帐户支付总额';
                yka107: jyscJosn.yka107,    //'社保基金支付总额';
                ykh012: jyscJosn.ykh012,    //'现金及其他自付';
                yab003: jyscJosn.yab003,    //'医保经办机构编号';
                aae011: jyscJosn.aae011,    //'经办人姓名';
                aae036: jyscJosn.aae036,    //'经办时间';
                yka111: jyscJosn.yka111,    //'符合范围';
                yka057: jyscJosn.yka057,    //'挂钩自付';
                grzhye: jyscJosn.grzhye,    //'个人账户余额';
                ykc117: jyscJosn.ykc117,    //'个人账户余额';
                akc021: cd_014.grxxJson.akc021,    //医疗人员类别
                jylsh: output.astr_jylsh, //'交易流水号';
                jyyzm: output.astr_jyyzm, //'交易验证码';
                jslx: jyscJosn.aka130,//结算类型
                ydbz: (cd_014.grxxJson.ydbz ? cd_014.grxxJson.ydbz : '1'),//异地标志
            };
            var dataset_rows = [];
            if (Array.isArray(jyscJosn.dataset.row)) {
                dataset_rows = jyscJosn.dataset.row;
            } else {
                dataset_rows.push(jyscJosn.dataset.row)
            }
            // if (jyscJosn.dataset.row) {
            //     if (Array.isArray(jyscJosn.dataset.row)) {
            //         jyscJosn.dataset.row.forEach(item => {
            //             item.rylx = '1';
            //             dataset_rows.push(item);
            //         })
            //     } else {
            //         jyscJosn.dataset.row.rylx = '1';
            //         dataset_rows.push(jyscJosn.dataset.row)
            //     }
            // }

            var gjzhxx_rows = [];//有共济账户支付（市医保11 无）
            parm.qslbmx = dataset_rows;
            parm.gjzhxx = gjzhxx_rows;
            return parm;
        },
        getErrorMsg: function (exceptions) {
            let errors = [];
            for (let i = 0; i < exceptions.length; i++) {
                let msg = exceptions[i].msg;
                if (errors.indexOf(msg) < 0) {
                    errors.push(msg);
                }
            }
            let msg = errors.join('\n');
            return rightVue.isNotNullOrEmpty(msg) ? msg : '部分交易处理失败，请重新提交';
        },
        /**
         * 获取个人账户余额
         * @param o
         */
        getGrzhye: function (o, property) {
            try {
                if (Array.isArray(o.grzhye)) {//异地的会返回数组
					if(o.grzhye[0].row){
						return o.grzhye[0].row[property];
					}else{
						return o.grzhye[0][property];
					}
                }
                return o.grzhye.row[property];
            } catch (e) {
                console.log(e)
                return '';
            }
        },
        /**
         * 异常模型
         * @param i
         * @param o
         * @param msg
         * @returns {{xh, msg}}
         */
        exceptionModel: function (i, o, code, msg) {
            return {
                xh: i,
                msg: msg,
                data: o,
                code: code
            }
        },
        printFp: function (list) {
            let reportlets = null;
            let reportlets1 = null;
            let frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            list = list.sort(this.compare('fphm'));
            let hashMap = [];
            for (let ii = 0; ii < list.length; ii++) {
                // fphm = list[ii]["fphm"];
                for (let ix = 0; ix < list[ii].brfyPrintList.length; ix++) {
                    let childObj = list[ii].brfyPrintList[ix];
                    let fphm = childObj.fphm;
                    let jsjlid = childObj.jsjlid;
                    let ghxh = childObj.ghxh;
                    if (hashMap.findIndex(x => x.jsjlid == jsjlid && x.fphm == fphm) > -1) continue;

                    if (reportlets == null) {
                        reportlets = "{reportlet: 'fpdy" + frpath + "mzsf" + frpath + "sffp.cpt',yljgbm:'" + jgbm + "',ghxh:'" + ghxh + "',fphm:'" + fphm + "',jsjlid:'" + jsjlid + "'}";
                        reportlets1 = "{reportlet:'fpdy" + frpath + "mzsf" + frpath + "dzpjghsf.cpt',yljgbm:'"+jgbm+"',jsjlid:'"+jsjlid+"'}";

                    } else {
                        reportlets = reportlets + ",{reportlet: 'fpdy" + frpath + "mzsf" + frpath + "sffp.cpt',yljgbm:'" + jgbm + "',ghxh:'" + ghxh + "',fphm:'" + fphm + "',jsjlid:'" + jsjlid + "'}";
                        reportlets1 = reportlets1 + ",{reportlet:'fpdy" + frpath + "mzsf" + frpath + "dzpjghsf.cpt',yljgbm:'"+jgbm+"',jsjlid:'"+jsjlid+"'}";
                    }
                    hashMap.push({
                        jsjlid: jsjlid,
                        fphm: fphm
                    });
                }
            }
            reportlets = "[" + reportlets + "]";
            reportlets1 = "[" + reportlets1 + "]";
            // 票据打印是否提示0-不提示；1-提示（焦点默认为‘是’）; 2-1-提示（焦点默认为‘否’）
            if (tableInfo.csqxContent.cs00500100112 == '0') {
                if (common.openConfirm("请确认是否打印", function () {
                    //帆软打印
                    //门诊收费打印机参数
                    if (!window.top.J_tabLeft.obj.FRorWindow) {
                        // if (!FrPrint(reportlets, popCenter1.MzsfPrint)) {
                        //
                        // }
                    } else {

                    }
                })) ;
                // if (mconfirm("请确认是否打印")) this.print(data.body.d.list);
            } else if (tableInfo.csqxContent.cs00500100112 == '1') {
                //帆软打印
                if (!window.top.J_tabLeft.obj.FRorWindow) {
                    // if (!FrPrint(reportlets, popCenter1.MzsfPrint)) {
                    //
                    // }
                } else {
                }
            }
			setTimeout(function () {
                if (!FrPrint(reportlets1, 'GP-C80250 Series', null, null, null)) {
                }
			}, 200);
        },
        successSaveAndPrint: function () {
            if (this.jssb) {
                popCenter1.ifClick = true;
                malert("保险结算失败！", 'right', 'defeadted');
                return
            }
            rightVue.mzjbxxContent.fbbm = rightVue.fzContent.ryfbbm;
            rightVue.mzjbxxContent.fbmc = rightVue.fzContent.ryfbmc;
            rightVue.mzjbxxContent.bxlbbm = rightVue.fzContent.rybxlbbm;
            if (rightVue.fzContent.rybxlbbm == '03') {
                rightVue.zxlshSn = '';
                rightVue.$forceUpdate();
            }
            if (this.jsjlContent.fyhj == undefined || this.jsjlContent.fyhj == null) {
                this.jsjlContent.fyhj = 0;
            }
            var ipAddress = window.top.navli.yourIp;
            rightVue.mzjbxxContent.ipAddress = ipAddress;
			for (let i = 0; i < rightVue.gbjsxx.length; i++) {
				for(let j = 0; j < rightVue.brfyjsonList.length; j++){
					if(rightVue.brfyjsonList[j].yzhm == rightVue.gbjsxx[i].yke134){
						rightVue.brfyjsonList[j].bxjsh = rightVue.gbjsxx[i].setl_id

                        if(rightVue.gjjsxx){
                            let ybkzf = cd_014.MathAdd(rightVue.gbjsxx[i].fund_pay_sumamt, rightVue.gbjsxx[i].acct_pay)
                            rightVue.brfyjsonList[j].ybkzf = cd_014.MathAdd(rightVue.gjjsxx.result.acct_pay, ybkzf)

                        }else{
                            rightVue.brfyjsonList[j].ybkzf = cd_014.MathAdd(rightVue.gbjsxx[i].fund_pay_sumamt, rightVue.gbjsxx[i].acct_pay)
                        }

                        if(rightVue.gjjsxx){
                            rightVue.brfyjsonList[j].xjzf = cd_014.floatSub(rightVue.gbjsxx[i].sjfyzh,rightVue.brfyjsonList[j].ybkzf)

                        }else{
                            rightVue.brfyjsonList[j].xjzf = cd_014.floatSub(rightVue.gbjsxx[i].sjfyzh,rightVue.brfyjsonList[j].ybkzf)
                        }

					}
				}

			}
            var json = {
                brfyList: rightVue.brfyjsonList,
                brghModel: rightVue.mzjbxxContent,
                jsjlModel: this.jsjlContent,
                cfkfxmList: this.cfkfxmList,
                bxjsh: rightVue.zxlshSn,
            };
            // @yqq 将结算弹窗的支付类别赋值到mzb_brfy与mzy_jsjl表中
            if (!rightVue.fzContent.bxjk) {
                json.jsjlModel.zflxbm = this.jsjlContent.zflxbm;
                json.jsjlModel.zflxmc = this.listGetName(this.zflxList, this.jsjlContent.zflxbm, 'zflxbm', 'zflxmc');
            }
            var cbData = [];
            this.postAjax('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=save', JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    cbData = data.d.list;
                } else {
                    malert(data.c, 'right', 'defeadted');
                    cbData = [];
                }
                this.jsjlContent.useCoupon = false;
                tableInfo.ylkh = null;
                $("#ykth").focus();
            }, function (error) {
                popCenter1.ifClick = true;
                console.log(error);
            });
            if (cbData.length) {
                malert("支付成功");
                this.printObj = cbData
                console.log(cbData)

                // if (popCenter1.csqxContent.N05001200337 == 1) {
                //     tableInfo.printPage()
                // } else {
                //     if (tableInfo.csqxContent.N05001200346) {//核酸明细费用编码
                //         if (rightVue.brfyjsonList[0].mxfybm != tableInfo.csqxContent.N05001200346) {//只收核酸检测费用,取brfyjsonList[0]
                //             if (tableInfo.csqxContent.N05001200345) {
                //                 tableInfo.$http.post(tableInfo.csqxContent.N05001200345 + "/eb/invoiceEBillOutpatient", JSON.stringify(json2))
                //                     .then(function (data) {
                //                         if (data.body.code == "S0000" && data.body.result.result == "S0000") {
                //                             malert("门诊开票成功");
                //                             popCenter1.printFp(popCenter1.printObj)
                //                         } else {
                //                             malert("门诊开票失败" + data.body.msg, "bottom", "defeadted");
                //                         }
                //                     });
                //             } else {
                //                 popCenter1.printFp(cbData)
                //             }
                //         }
                //     } else {
                //         if (tableInfo.csqxContent.N05001200345) {
                //             tableInfo.$http.post(tableInfo.csqxContent.N05001200345 + "/eb/invoiceEBillOutpatient", JSON.stringify(json2))
                //                 .then(function (data) {
                //                     if (data.body.code == "S0000" && data.body.result.result == "S0000") {
                //                         malert("门诊开票成功");
                //                         popCenter1.printFp(popCenter1.printObj)
                //                     } else {
                //                         malert("门诊开票失败" + data.body.msg, "bottom", "defeadted");
                //                     }
                //                 });
                //         } else {
                //             popCenter1.printFp(cbData)
                //         }
                //     }
                // }


                if (tableInfo.csqxContent.N05001200345) {


                    for (let i = 0; i < cbData.length; i++) {
                        var json2 = {
                            jsjlid: cbData[i].brfyPrintList[0].jsjlid,
                            ipAddress: window.top.navli.yourIp,
                            userName: cbData[i].brfyPrintList[0].czyxm,
                            yljgbm: jgbm
                        };
                        tableInfo.$http.post(tableInfo.csqxContent.N05001200345 + "/eb/invoiceEBillOutpatient", JSON.stringify(json2))
                            .then(function (data) {
                                if (data.body.code == "S0000" && data.body.result.result == "S0000") {
                                    malert("门诊开票成功");

                                } else {
                                    malert("门诊开票失败" + data.body.msg, "bottom", "defeadted");
                                }
                            });
                    }
                    setTimeout(function () {
                        popCenter1.printFp(cbData)
                    }, 1000);


                } else {
                    popCenter1.printFp(cbData)
                }

                this.WebServiceFun()

                // @zh 药监平台
                if (tableInfo.csqxContent.N05001200334) {
                    popCenter1.yyjgpt(cbData);
                }
                this.successNextClear()
            } else {
                popCenter1.isShow = false;
                popCenter1.ifClick = true;
                //his结算失败，调用医保反结算
                popCenter1.cancalYbjs();
            }
        }
        ,
        successNextClear: function () {

            console.log(popCenter1.jsjlContent);
            // 保存成功后加载出结算信息（每保存一条加入一条，不从后台读取）
            rightVue.czyfy();
            // 保存成功处理找补金额赋值
            $("#bjyss").val($("#ssje").val());
            rightVue.bjyss = popCenter1.fDec($("#ssje").val(), 2);
            $("#bjyys").val(popCenter1.jsjlContent.ysje);
            rightVue.bjyys = popCenter1.fDec(popCenter1.jsjlContent.ysje, 2);
            rightVue.bjzbje = popCenter1.fDec(rightVue.bjyss - rightVue.bjyys, 2);
            // 清空弹出框的输入金额
            popCenter1.getJrjs();
            rightVue.zxlshSn = '';
            popCenter1.resObj = {};
            rightVue.clear(); // 调用清空按钮操作
            popCenter1.ifClick = true;
            $("#ssje").val("");
            $("#zbje").val("");
            popCenter1.isShow = false;
            popCenter1.jsjlContent = {};
            popCenter1.cfkfxmList = [];
            rightVue.brfyjsonList = [];
            if (typeof (popCenter1.zflxList[0]) != "undefined") {
                popCenter1.jsjlContent.zflxbm = popCenter1.zflxList[0].zflxbm;
            }
            rightVue.mzjbxxContent = {};
            rightVue.mzjbxxContent = {};
            rightVue.brxxContent = {};
            rightVue.getPjxx(); // 票据信息
            rightVue.czyfy(); // 操作员收费情况
        }
        ,
        WebServiceFun: function () {
            if (tableInfo.csqxContent.cs00500100120 == '1') {
                //写检验申请
                var parm = {
                    beginrq: getTodayDateBegin(),
                    endrq: getTodayDateEnd(),
                    lylx: "mzfy",
                    ksbm: rightVue.fzContent.mzks
                };
                $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqsqByWebService&ksbm=" + tableInfo.ksbm + "&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                    } else {
                        malert("上传检验项目失败！" + json.c, 'right', 'defeadted');
                    }
                });
                var parm = {
                    beginrq: getTodayDateBegin(),
                    endrq: getTodayDateEnd(),
                    ksbm: rightVue.fzContent.mzks
                };

                if (tableInfo.csqxContent.N05001200329 == '1') { // 传数据导翼展pacs表中
                    $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqjcByYzpacs&parm=" + JSON.stringify(parm), function (json) {
                        if (json.a == 0) {

                        } else {
                            malert("上传检查项目失败！" + json.c, 'right', 'defeadted');
                        }
                    });
                }

                $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqjcByWebService&ksbm=" + tableInfo.ksbm + "&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {

                    } else {
                        malert("上传检查项目失败！" + json.c, 'right', 'defeadted');
                    }
                });
            }
        }
        ,
        successPrint: function () {
            if (this.jssb) {
                popCenter1.ifClick = true;
                malert("保险结算失败！", 'right', 'defeadted');
                return
            }
            if (this.jsjlContent.fyhj == undefined || this.jsjlContent.fyhj == null) {
                this.jsjlContent.fyhj = 0;
            }
            var json = {
                brfyList: rightVue.brfyjsonList,
                brghModel: rightVue.mzjbxxContent,
                jsjlModel: this.jsjlContent,
                cfkfxmList: this.cfkfxmList,
                bxjsh: rightVue.zxlshSn
            };
            // 将对象丢到后台进行一系列操作
            $('.printArea').html('')
            tableInfo.printBcdPage();
            setTimeout(function () {
                if (JSON.stringify(gz_001.FpPrintContent) != '{}') {
                    popCenter1.printFp(gz_001.FpPrintContent.list) //发票
                }
            }, 2000)
            if (tableInfo.csqxContent.cs00500100120 == '1') {
                //写检验申请
                var param = {
                    beginrq: getTodayDateBegin(),
                    endrq: getTodayDateEnd(),
                    lylx: "mzfy",
                    ksbm: rightVue.fzContent.mzks
                };
                //webService服务方式
                if (tableInfo.csqxContent.cs05001200325 == '1') {
                    $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqsqByWebService&ksbm=" + tableInfo.ksbm + "&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                        } else {
                            malert("上传检验项目失败！" + json.c, 'right', 'defeadted');
                        }
                    });
                } else {//默认走his
                    $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqsq&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {

                        } else {
                            malert("上传检验项目失败！" + json.c, 'right', 'defeadted');
                        }
                    });
                }

                var param = {
                    beginrq: getTodayDateBegin(),
                    endrq: getTodayDateEnd(),
                    ksbm: rightVue.fzContent.mzks
                };
                //webService服务方式
                if (tableInfo.csqxContent.cs05001200325 == '1') {
                    $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqjcByWebService&ksbm=" + tableInfo.ksbm + "&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {

                        } else {
                            malert("上传检查项目失败！" + json.c, 'right', 'defeadted');
                        }
                    });
                } else {//默认走his
                    $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqjcsq&parm=" + JSON.stringify(param), function (json) {
                        if (json.a == 0) {

                        } else {
                            malert("上传检查项目失败！" + json.c, 'right', 'defeadted');
                        }
                    });
                }

            }
            setTimeout(function () {
                $('.printArea').html('')
                console.log(popCenter1.jsjlContent);
                // 保存成功后加载出结算信息（每保存一条加入一条，不从后台读取）
                rightVue.czyfy();
                // 保存成功处理找补金额赋值
                $("#bjyss").val($("#ssje").val());
                rightVue.bjyss = popCenter1.fDec($("#ssje").val(), 2);
                $("#bjyys").val(popCenter1.jsjlContent.ysje);
                rightVue.bjyys = popCenter1.fDec(popCenter1.jsjlContent.ysje, 2);
                rightVue.bjzbje = popCenter1.fDec(rightVue.bjyss - rightVue.bjyys, 2);
                // 清空弹出框的输入金额
                popCenter1.getJrjs();
                rightVue.clear(); // 调用清空按钮操作
                popCenter1.ifClick = true;
                $("#ssje").val("");
                $("#zbje").val("");
                popCenter1.isShow = false;
                popCenter1.jsjlContent = {};
                popCenter1.cfkfxmList = [];
                rightVue.brfyjsonList = [];
                rightVue.mzjbxxContent = {};
                rightVue.mzjbxxContent = {};
                rightVue.brxxContent = {};
                rightVue.getPjxx(); // 票据信息
                rightVue.czyfy(); // 操作员收费情况
            }, 2000)

            tableInfo.ylkh = null;
            $("#ykth").focus();

        }
        ,
        yyjgpt: function (data) {
            var list = [];
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                var parm = {
                    patient_local_id: rightVue.mzjbxxContent.brid,  //患 者 本 地 唯 一ID 必填 String
                    visit_count: 1,   //就诊次数 必填 Int
                    outpatient_no: rightVue.mzjbxxContent.ghxh, //门(急)诊号 必填 String
                    reg_serial_no: rightVue.mzjbxxContent.ghxh, //挂号序号 必填 String
                    settle_serial_no: data.list[0].brfyPrintList[0].jsjlid,  //结算明细序号 必填 String
                    name: rightVue.mzjbxxContent.brxm,  //患者姓名 必填 String
                    birth_date: rightVue.mzjbxxContent.csrq,    //出生日期 必填 String 患者出生当日的公元纪年日期的完整描述 形如”19850513”
                    gender_code: rightVue.mzjbxxContent.brxb,   //性别代码 必填 String 附录 12 RC001
                    phone_number: rightVue.mzjbxxContent.lxrdh,  //电话号码String 患者本人的电话号码，包括国际、国内区号和分机号
                    idcard: rightVue.mzjbxxContent.sfzh,    //注册证件号码必填 String 患者的注册证件上的唯一法定标识符
                    idcard_code: '01',   //注册证件类型代 码String 附录 7CV02.01.101在【CV02.01.101 身份证件类别代码】范围内
                    settle_no: 1, //第 N 次结算 必填 String每次结算填写一条，N 代表本次就诊中的第几次缴费。当次就诊进行多次缴费的，每次开发票或打印收费单为一次结算未结算标识为“0”
                    trans_type: popCenter1.jsjlContent < 0 ? '2' : '1',    //交易类别 必填 String附录 15 CT07.00.002在【CT07.00.002 交易类别】值域范围内“如果没有交易，此交易类别为“1””
                    medi_exp_pay_code: data.list[0].brfyPrintList[0].fbbm, //医疗费用支付方 式代码必填 String附录 16 CV07.10.003 在【CV07.10.003 医疗费用支 付方式】值域范围内
                    prescribe_no: rightVue.brfyjsonList.yzhm,  //处方序号 String
                    apple_no: '',  //申请单号 String
                    cost_type_code: rightVue.brfyjsonList[i].fylb,    //费用类别代码 必填 String附录 17 EXCV07.10.001EXCV07.10.001 发票收费项目分类代码“如果没有交易，此费用类别代码标识为“99” ”
                    cost_type_name: rightVue.brfyjsonList[i].fylbmc,    //费用类别名称 String 发票收费项目名称
                    drug_id: '',   //药品(项目)代码 String
                    drug_name: '', //药品(项目)名称 String
                    insur_code: '',    //医保编码 String参见附件：贵州省物价标准（4171 及补充文件）的编码
                    insur_name: '',    //医保名称 String
                    amount: rightVue.brfyjsonList[i].fysl,    //数量 Int
                    cost_sum: rightVue.brfyjsonList[i].fydj,  //药品与费用单价 double
                    personal_expenses: data.list[0].brfyPrintList[0].xjzf, //自费金额 double
                    reimb_cost_sum: '',    //报销金额 double
                    total_amount: data.list[0].brfyPrintList[0].fyhj,  //总费用（元） 必填 double
                    indivi_exp_out_insur: '',  //医保范围外个人 自费double
                    total_insur_amount: '',    //医保范围内总额 double
                    medical_insur_pay: '', //医保基金支付 double
                    pay_within_insur: '',  //医保范围内自付 double
                    resund_iden: '1',   //退费标识 必填 String“如果没有交易，此退费标识为“1””
                    receipt_no: data.list[0].brfyPrintList[0].fphm,    //发票号 String
                    settlement_date: this.fDate(new Date(), 'datetime'),   //结算日期 必填 String
                    drug_procurement_code: rightVue.brfyjsonList[i].fyjlid, //药品采购码 String YPID5
                };
                list.push(parm);
            }

            this.postAjax(tableInfo.csqxContent.N05001200334, JSON.stringify(list), function (json) {
                if (json.success == "T") {
                    malert(json.error_msg, 'right', 'success');
                } else {
                    malert(json.error_msg, 'right', 'defeated');
                }
            }, function (e) {
                malert("药监平台接口已断开，请联系管理员！", 'right', 'defeadted');
            });
        }
        ,

        //进行医保反结算，his结算失败时调用
        cancalYbjs: function () {
            if (rightVue.fzContent.rybxlbbm != '03' && rightVue.fzContent.ryfbbm != null) {
                if (rightVue.fzContent.bxjk && rightVue.fzContent.bxjk == '008') {//云南医保结反结算
                    yndryb_008.ybfjs();
                }
            }
        }
        ,

        successQhtf: function () {
            if (this.jssb) {
                popCenter1.ifClick = true;
                malert("保险结算失败！", 'right', 'defeadted');
                return
            }
            $.ajaxSettings.async = false;
            if (!popCenter1.ifClick) return; // 已经点击过就不能再点
            popCenter1.ifClick = false;

            var ssje = $("#ssje").val();
            if (ssje == '0') { // 当实收金额为0
                if (tableInfo.csqxContent.cs00500100109 == '0') { // 参数权限控制实收金额是否允许为0----0不允许
                    malert("收费金额不允许为0", 'right', 'defeadted');
                    popCenter1.ifClick = true;
                    return
                }
            }
            if (ssje == null || ssje == "") {
                malert("收费金额不允许为空", 'right', 'defeadted');
                popCenter1.ifClick = true;
                return
            }
            if (ssje * 1 < popCenter1.jsjlContent.xjzf * 1) {
                malert("收费金额不允许小于现金支付金额", 'right', 'defeadted');
                popCenter1.ifClick = true;
                return
            }
            popCenter1.jsjlContent.zflxmc = popCenter1.listGetName(popCenter1.zflxList, popCenter1.jsjlContent.zflxbm, 'zflxbm', 'zflxmc');

            // 合并成一个对象
            rightVue.mzjbxxContent.fbbm = rightVue.fzContent.ryfbbm;
            rightVue.mzjbxxContent.fbmc = rightVue.fzContent.ryfbmc;
            rightVue.mzjbxxContent.bxlbbm = rightVue.fzContent.rybxlbbm;
            var json = {
                brfyList: rightVue.brfyjsonList,
                brghModel: rightVue.mzjbxxContent,
                jsjlModel: popCenter1.jsjlContent,
                cfkfxmList: popCenter1.cfkfxmList,
                bxjsh: rightVue.zxlshSn
            };

            // 将对象丢到后台进行一系列操作
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=save&',
                JSON.stringify(json)).then(function (data) {
                if (data.body.a == 0) {
                    // 票据打印是否提示0-不提示；1-提示（焦点默认为‘是’）; 2-1-提示（焦点默认为‘否’）
                    if (tableInfo.csqxContent.cs00500100112 == '0') {
                        if (common.openConfirm("请确认是否打印", function () {
                            popCenter1.print(data.body.d.list);
                        })) ;
                        // if (mconfirm("请确认是否打印")) this.print(data.body.d.list);
                    } else if (tableInfo.csqxContent.cs00500100112 == '1') {
                        //调用打印
                        popCenter1.print(data.body.d.list);
                    }
                    if (tableInfo.csqxContent.cs00500100120 == '1') {//是否上传检查检验申请
                        //写检验申请
                        var parm = {
                            beginrq: getTodayDateBegin(),
                            endrq: getTodayDateEnd(),
                            lylx: "mzfy"
                        };
                        $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqsq&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0) {

                            } else {
                                malert("上传检验项目失败！" + json.c, 'right', 'defeadted');
                            }
                        });
                        var parm = {
                            beginrq: getTodayDateBegin(),
                            endrq: getTodayDateEnd()
                        };
                        $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqjcsq&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0) {

                            } else {
                                malert("上传检查项目失败！" + json.c, 'right', 'defeadted');
                            }
                        });
                    }
                    console.log(popCenter1.jsjlContent);
                    // 保存成功后加载出结算信息（每保存一条加入一条，不从后台读取）

                    // 保存成功操作员收费交款合计再次加载
                    rightVue.czyfy();
                    // 保存成功处理找补金额赋值
                    $("#bjyss").val(popCenter1.fDec($("#ssje").val(), 2));
                    rightVue.bjyss = popCenter1.fDec($("#ssje").val(), 2)
                    $("#bjyys").val(popCenter1.jsjlContent.ysje);
                    rightVue.bjyys = popCenter1.fDec(popCenter1.jsjlContent.ysje, 2);
                    rightVue.bjzbje = popCenter1.fDec(rightVue.bjyss - rightVue.bjyys, 2);
                    // 清空弹出框的输入金额
                    popCenter1.getJrjs();
                    rightVue.clear(); // 调用清空按钮操作
                    popCenter1.ifClick = true;
                    $("#ssje").val("");
                    $("#zbje").val("");
                    popCenter1.isShow = false;
                    popCenter1.jsjlContent = {};
                    popCenter1.cfkfxmList = [];
                    rightVue.brfyjsonList = [];
                    popCenter1.jsjlContent = {};
                    rightVue.mzjbxxContent = {};
                    rightVue.mzjbxxContent = {};
                    rightVue.brxxContent = {};
                    rightVue.getPjxx(); // 票据信息
                    rightVue.czyfy(); // 操作员收费情况
                } else {
                    popCenter1.isShow = false;
                    malert("结算失败！", 'right', 'defeadted');
                    // window.gz_001.mznhCh();
                    popCenter1.ifClick = true;
                }
                tableInfo.ylkh = null;
                $("#ykth").focus();
            }, function (error) {
                // gz_001.mznhCh();
                popCenter1.ifClick = true;
                console.log(error);
            });
        }
        ,
        print: function (list) {
            // 查询打印模板
            var json = {
                repname: '补打发票'
            };
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {

                for (var i = 0; i < list.length; i++) {
                    // 清除打印区域
                    popCenter1.clearArea(json.d[0]);
                    // 为打印前生成数据
                    list[i]['brfyPrintList'][0]['fyhj'] = popCenter1.fDec(list[i]['brfyPrintList'][0]['fyhj'], 2);
                    list[i]['brfyPrintList'][0]['brxb'] = popCenter1.brxb_tran[list[i]['brfyPrintList'][0]['brxb']];
                    popCenter1.printContent(list[i]['brfyPrintList'][0]);
                    popCenter1.printTrend(list[i]['brfyPrintList']);
                    // 开始打印
                    var style = document.createElement('style');
                    style.id = "print";
                    style.innerHTML = "@media print {@page {size: landscape;}}";
                    window.document.head.appendChild(style);
                    window.print();
                    $('#print').remove()
                }

            });
        }
        ,
        gbtc: function () {//关闭弹窗事件
            popCenter1.isShow = false;
            popCenter1.jsjlContent.codeContent = "";

            //@HUIHUI 成都银海医保
            if (rightVue.fzContent.rybxlbbm != '03' && rightVue.fzContent.ryfbbm != null) {
                if (rightVue.fzContent.bxjk == 'B07' && rightVue.gzyhybContent != null) {
                    cd_014.cd014_quxiao();
                }
            }
        }
        ,
        compare: function (property) {
            return function (a, b) {
                let value1 = a[property];
                let value2 = b[property];
                return value1 - value2;
            }
        }
    }
});
var popPf = new Vue({
    el: '.popPf',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ishow: false,
        jsonList: [],
    },
});
tableInfo.getCsqx(); // 参数权限设置
popCenter1.getJrjs();
var popTable = new Vue({
    el: '#popCenter',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        isShow: false,
        jsonList: []
    },
});

var popTable1 = new Vue({
    el: '#gjpopCenter',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        isShow: false,
        jsonList: []
    },
    methods: {
        guanbi :function () {
            popTable1.isShow = false;
            rightVue.jejsfs();
        }
    }
});

var toDayList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isFold: false,
        jsonList: [],
        ksrq: '',
        jsrq: '',
        jsValue: '',
        param: {
            page: 1,
            sort: 'ghxh',
            order: 'desc'
        },
        totlePage: 0,
    },
    mounted: function () {
        this.ksrq = this.fDate(new Date(), 'date');
        this.jsrq = this.fDate(new Date(), 'date')
    },
    updated: function () {
    },
    methods: {
        showTime: function (el, code) {
            laydate.render({
                elem: '#' + el
                , show: true //直接显示
                , trigger: 'click'
                , theme: '#1ab394'
                , done: function (value, data) {
                    toDayList[code] = value;
                    toDayList.getData();
                }
            });
        },
        closes: function () {
            this.isFold = false
        },
        getData: function () {
            if (this.ksrq != '' && this.jsrq != '') {
                startDate = this.fDate(this.ksrq, 'date') + ' 00:00:00';
                endtDate = this.fDate(this.jsrq, 'date') + ' 23:59:59';
            }
            var str_param = {
                page: this.param.page,
                rows: this.param.rows,
                parm: this.jsValue,
                ksrq: startDate,
                jsrq: endtDate,
                sort: 'ghxh',
                order: 'desc'
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=queryJzlb&parm=" + JSON.stringify(str_param), function (json) {
                if (json.a == 0) {
                    toDayList.totlePage = Math.ceil(json.d.total / toDayList.param.rows);
                    toDayList.totlePage = toDayList.totlePage == 0 ? 1 : toDayList.totlePage;
                    toDayList.jsonList = json.d.list;
                } else {
                    malert("获取挂号列表失败  " + json.c, 'top', 'defeadted');
                }
            });
        },
        edit: function (index, item) {
            rightVue.searching1(false, 'ghxh', item.ghxh);
            this.isFold = false
        }
    }
});

function loadPage(name) {
    $('#loadPage').load(name + '.html');
}

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
        rightVue.selSearch1 = -1
    }
});

$(document).keydown(function (e) {
    // F1门诊收费保存
    if (e.keyCode == 112) {
        rightVue.mzjs(); // 保存
        rightVue.$nextTick(function () {
            $('#ssje').focus().select()

        })
        return false
    }
    // F12保险快捷键
    if (e.keyCode == 115) {
        tableInfo.loadbx()
        // 这里暂时不知道怎么处理
    }
});
$("#ykth").focus();
