window.sydjWrapper = new Vue({
    el: '.wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        param:{
            'shbz':'3'
        },
        popCont:{},
        jsonList: [],//
        totlePage:0,
        LcList:[],
        kmmc:'',
        rybmList:{
            '3':'全部',
            '0':'未审核',
            '1':'已审核',
        },
        ksList:[],
        zhuangtai:{
            '0':'待审核',
            '1':'已审核',
        }

    },
    mounted:function(){
        this.getKsbm()
        var myDate=new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value:this.param.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    sydjWrapper.param.beginrq = value;
                    sydjWrapper.getData();
                }

            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            rigger: 'click',
            value:this.param.endrq,
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    sydjWrapper.param.endrq = value;
                    sydjWrapper.getData();
                }

            }
        });
    },
    updated:function(){
        changeWin()
    },
    methods: {
        //扫码接收
        smJS:function () {
            pop.popShow=true;
            pop.smShow=true;
            pop.cgShow=false;
            pop.sbShow=false;
            pop.ShowTitle='请使用扫码枪进行扫码'
        },
        //获取当前操作员的拥有科室权限
        getKsbm: function () {
            var str_param = {
                zyks:'1',
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(str_param), function (json) {
                if (json.a == 0 && json.d!=null) {
                    if (json.d.list.length > 0) {
                        sydjWrapper.ksList = json.d.list;
                        sydjWrapper.ksList.unshift({ksbm:'%','ksmc':'全院'});
                        sydjWrapper.popCont.ryks = json.d.list[0].ksbm;
                        sydjWrapper.getData();
                    }
                } else {
                    malert('获取科室失败','top','defeadted')
                }
            });
        },
        //加载获取列表数据
        getData: function () {
            if(this.param.shbz!=3){
                if(this.param.shbz==1){
                    this.param.shbz= 1;
                }else{
                    this.param.shbz=this.param.shbz;
                }
            }
            this.param.jsbz = 1;
            this.jsonList=[];
            this.param.ryks=this.popCont.ryks=='%'?'':this.popCont.ryks;
            this.param.bqcybz = "1";
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=getBaJbxxList&dg='+JSON.stringify(this.param)+'&json='+JSON.stringify(this.param),function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if(json.a==0){
                    sydjWrapper.totlePage = Math.ceil(json.d.total/sydjWrapper.param.rows);
                    sydjWrapper.jsonList = json.d.list;

                }
            });
        },
        //审核
        sh:function (zyh) {
            $.getJSON('/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=getMessage&zyh=' + zyh, function (json) {
                if (json.a == 0) {
                    sydjWrapper.$http.post("/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateSh&",
                        JSON.stringify(json.d)).then(function (data) {
                            if (data.body.a == 0) {
                                malert("审核成功");
                                sydjWrapper.getData();
                            } else {
                                malert("审核失败" + data.body.c, 'top', 'defeadted');
                            }
                        }, function (error) {
                            console.log(error);
                        });
                } else {
                    malert("获取信息失败：" + json.c,'top','defeadted');
                    return;
                }
            });
        },
        qxsh: function(zyh){
            var parm = {
                zyh: zyh
            };
            this.$http.post("/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateQxsh&",
                JSON.stringify(parm)).then(function (data) {
                    if (data.body.a == 0) {
                        malert("取消审核成功");
                        this.getData();
                    } else {
                        malert("取消审核失败" + data.body.c,'top','defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
        },
        //拒绝
        js:function () {
            malert('拒绝','top','defeadted')
        },
        //归还入库
        dgh:function () {
            brzcList.open();
            brzcList.ghShow=true;
            brzcList.jjShow=true;
            brzcList.qxShow=true;
            brzcList.title='归还入库';
            brzcList.saveTitle='归还入库';
        },
        //双击跳转基本信息
        goNew:function (item) {
            sessionStorage.setItem("bagljbxx", item.zyh);
            sessionStorage.setItem("fromBagl", '1');
            this.topNewPage('基本信息','page/bagl/sygl/sydj/jbxx.html');
        },
        resultRydjChange:function (val) {
            // Vue.set(this.rybmList, 'shzt', val[0]);
            var isTwo = false;
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "shbz" :
                    Vue.set(this.param, 'shbz', val[0]);
                    Vue.set(this.param, 'shmc', val[4]);
                    this.getData();
                    break;
                case "ryks" :
                    Vue.set(this.popCont, 'ryks', val[0]);
                    this.getData();
                    break;
                default:
                    break;
            }
            this.$forceUpdate()
        },
    },


});
//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
            popShow:false,
            popContent:{},
            baxx:{},
            cgShow:false,
            smShow:false,
            sbShow:false,
            ShowTitle:''
        },
        methods:{
            //病案接收
            updateBajs:function() {
                if (!this.popContent.parm){
                    malert("请先扫码",'top','defeadted');
                    return
                }

                var parm = {
                    zyh: this.popContent.parm
                };
                this.$http.post("/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=updateJs&",
                    JSON.stringify(parm)).then(function (data) {
                    if (data.body.a == 0) {
                        pop.popContent.parm = '';
                        pop.baxx = data.body.d;
                        pop.cgSm();
                        malert("病案接收成功");
                        sydjWrapper.getData();
                    } else {
                        pop.sbSm();
                        malert(data.body.c,'top','defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });

                console.log("病案接收：" + this.popContent.parm);
            },
            //保存
            saveMb:function () {
                pop.popShow=false;
            },
            //取消
            cancel:function () {
                pop.popShow=false;
            },
            //扫码成功
            cgSm:function () {
                pop.cgShow=true;
                pop.smShow=false;
                pop.sbShow=false;
                pop.ShowTitle='扫码接收成功';
            },
            //扫码失败
            sbSm:function () {
                pop.cgShow=false;
                pop.smShow=false;
                pop.sbShow=true;
                pop.ShowTitle='扫码接收失败';
            }
        },
    });

