var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data() {
        return {
            num: 0,
            jsonList: [],
            param: {},
            totlePage:0,
            page: {
                page: 1,
                rows: 10,
                total: null,
                parm: null
            },
            param_rows: 20,
            bxlbbm: null,
            bxurl: null,
            search: '',
        };
    },
    watch: {
        page(val) {
            this.getData(val);
        },
    },
    created: function () {
    },
    mounted: function () {
        this.getbxlb();
    },

    methods: {
        sschangeDown(event) {
            console.log(event, event.keyCode);
            if (event.keyCode == 13) {
                this.getData(this.page);
            }
        }
        ,
        addData: function () {
        },
        getbxlb: function () {
            var param = {bxjk: "009"};
            common.openloading();
            var _this = this;
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            wrapper.bxlbbm = json.d.list[0].bxlbbm;
                            wrapper.bxurl = json.d.list[0].url;

                        }
                        common.closeLoading();
                        _this.getData();
                    } else {
                        malert("保险类别查询失败!" + json.c);
                        common.closeLoading();
                    }
                });
        },
        tabBg: function (index) {
            this.num = index;
            console.log('---->', this.num, index);
            this.search = '';
            this.getData();
        },
        getData: function (page) {
            var medthod = this.num == 0 ? 'zlQuery' : this.num == 1 ? 'ypQuery' : 'ksQuery';
            // wrapper.totlePage = 0;
            var param = {
                page: page || 1,
                rows: this.param_rows,
                sort: "yljgbm",
                order: "asc",
                parm: this.search,
            };

            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=basic&method=" + medthod + "&parm=" + JSON.stringify(param), function (json) {
                console.log(">>>>>"+json.a);
                if (json.a == "0") {
                    console.log("|||", parseInt(JSON.parse(json.d).total / 20));
                    var total = JSON.parse(json.d).total / wrapper.param_rows;
                    wrapper.totlePage = Math.ceil(total);
                    wrapper.jsonList = JSON.parse(json.d).list;
                }else{
                    malert(json.c)
                }
            });
        },
    }
})