    //统筹类别
    var tableInfo = new Vue({
        el: '#jydj',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
            param: {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                cxrq: 'shrq',
                shbz:'1'
            },
            jsonList: [], //列表集合
            fylbList: [], //费用类别下拉框
            fylb: null, //费用类别值
            balbList: [], //病案类别
            balb: null   //病案类别值
        },
        mounted:function () {
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
            laydate.render({
                elem: '#timeVal',
                    type: 'datetime'
                , eventElem: '.zui-date',
                value:this.param.beginrq
                , trigger: 'click'
                , theme: '#1ab394'
                , done: function (value, data) {
                    if (value != '') {
                        tableInfo.param.beginrq = value;
                        tableInfo.getData();
                    }
                    // wrapper.param.time = value
                }
            });
            laydate.render({
                elem: '#timeVal1',
                    type: 'datetime',
                value:this.param.endrq
                , eventElem: '.zui-date'
                , trigger: 'click'
                , theme: '#1ab394'
                , done: function (value, data) {
                    if (value != '') {
                        tableInfo.param.endrq = value;
                        tableInfo.getData();
                    }
                }
            });

        },
        methods : {
            //初始化页面加载列表
            getData: function () {
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }

                this.param.beginrq=tableInfo.param.beginrq;
                this.param.endrq=tableInfo.param.endrq;
                $.getJSON("/actionDispatcher.do?reqUrl=New1BaglSyglSydj&types=queryZkba&parm="+JSON.stringify(this.param),function (json) {
                    if(json.d!=null){
                        tableInfo.jsonList = json.d.list;
                        tableInfo.totlePage = Math.ceil(json.d.total/tableInfo.param.rows);
                        // tableInfo.isCheckAll = false;
                        // tableInfo.checkAll();//调用全选
                    }
                });
            },
            //借阅登记
            jyDJ:function (num ) {
                brzcList.open();
                brzcList.qxShow=true;
                brzcList.jjShow=false;
                brzcList.ghShow=false;
                brzcList.saveTitle='登记';
                brzcList.title='借阅登记';
                brzcList.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                brzcList.popContent.sqrq = this.fDate(new Date(), 'date');
                brzcList.popContent.jydjrq = this.fDate(new Date(), 'date');
            },

        }
    });


    var brzcList=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
            popContent:{},
            title:'',
            nums:1,
            saveTitle:'',
            rybmList:[],
            allryList: [], //所有人员
            jjShow:false,
            ghShow:false,
            qxShow:false,
            ghbz:{
                '0':'否',
                '1':'是'
            },
            ckbz:{
                '0':'否',
                '1':'是'
            }
        },
        mounted: function () {
            laydate.render({
                elem: '.times1'
                , theme: '#1ab394',
                done: function (value, data) {
                    wap.popContent.sqrq = value

                }
            });
            laydate.render({
                elem: '.times2'
                , theme: '#1ab394',
                done: function (value, data) {
                    wap.popContent.jydjrq = value

                }
            });
            laydate.render({
                elem: '.times3'
                , theme: '#1ab394',
                done: function (value, data) {
                    wap.popContent.ckrq = value
                }
            });
            laydate.render({
                elem: '.times4'
                , theme: '#1ab394',
                done: function (value, data) {
                    wap.popContent.ghrq = value
                }
            });
            laydate.render({
                elem: '.times5'
                , theme: '#1ab394',
                done: function (value, data) {
                    wap.popContent.sprq = value
                }
            });
        },
        methods:{
            closes:function () {
                // brzcList.hzShow=false;
                brzcList.nums=1;
            },
            open: function () {
                brzcList.nums=0;
            },
            jyrdjList:function () {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm", function (json) {
                    if (json.a == 0){
                        brzcList.allryList = json.d.list;
                        console.log(brzcList.allryList )
                    }
                });
            },
            //登记
            saveData:function () {
               if(this.saveTitle='登记'){
                   // var json=JSON.stringify(brzcList.popContent);
                   // var json='{"list":'+JSON.stringify(brzcList.popContent)+'}'
                   var json={
                      list:[{
                          brxm:this.popContent.brxm,
                          brid:this.popContent.brid,
                          zyh:this.popContent.zyh,
                          bah:this.popContent.bah,
                          sqrq:$("#sqrq").val(),
                          jydjrq:$("#jydjrq").val(),
                          ckbz:this.popContent.ckbz,
                          ckrq:$("#ckrq").val(),
                          ghbz:this.popContent.ghbz,
                        ghrq:$("#ghrq").val(),
                         jydjr:this.popContent.bmry,
                         jyr:this.popContent.jyr,
                          jysy:this.popContent.jysy,
                          jydw:this.popContent.jydw,
                        spyj:this.popContent.spyj,
                      }
                 ]
               };
                   this.$http.post('/actionDispatcher.do?reqUrl=New1BaglBasyJydj&types=insert',JSON.stringify(json)).then(function (data) {
                       if(data.body.a == 0){
                           brzcList.closes();
                           tableInfo.getData();
                           malert("保存成功","top","success");
                       } else {
                           malert("上传数据失败","top","defeadted");
                       }
                   },function (error) {
                       console.log(error);
                   });
               }
            }
        }
    });
    //列表
    tableInfo.getData();
    brzcList.jyrdjList(); //所有人员
