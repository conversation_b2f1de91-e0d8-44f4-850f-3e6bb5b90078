<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>EMR模板</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="emrmb.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
    <div class="emrmb-left">
        <div class="zui-form">
            <div class="zui-inline">
                <label class="zui-form-label">检索</label>
                <div class="zui-input-inline">
                    <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                </div>
            </div>
        </div>
        <div class="ksywhd-top"><i class="ksywhd-toggle ksywhd-toggleup" :class="{'ksywhd-toggledown':ejShow}"></i><i class="ksywhd-title" @click="ejMore?ejUp():ejDown()">业务活动记录</i></div>
        <div class="emrmb-list" v-show="ejShow">
            <div class="emrmb-yj" v-for="(item,$index) in leftList" :key="item">
                <span class="emrmb-yj-title" onclick="yjClick(this)"><i class="ksywhd-toggle ksywhd-toggleup" ></i><i class="padd-l-10" v-text="item.flName"></i></span>
                <ul class="emrmb-ej-detail">
                    <li v-for="(a,$index) in item.tree" @click="getData(a.ywhdid)"><i class="icon-bd"></i><i class="padd-l-5" v-text="a.ywhdmc"></i></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="emrmb-right">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="Add">添加</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
                <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
                <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
                <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
            </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalues" @keydown="searchRc()"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view ybglTable" id="utable1" z-height="full" style="margin-top:0; padding:0;">
            <div class="zui-table-header">
                <table class="zui-table table-width50-1">
                    <thead>
                    <tr>
                        <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th z-field="sexs" z-width="100px">
                            <div class="zui-table-cell">活动名称</div>
                        </th>
                        <th z-field="sex" z-width="100px">
                            <div class="zui-table-cell">模板范围</div>
                        </th>
                        <th z-field="city" z-width="100px">
                            <div class="zui-table-cell">科室</div>
                        </th>
                        <th z-field="jm1" z-width="80px">
                            <div class="zui-table-cell">用户</div>
                        </th>
                        <th z-field="jm2" z-width="100px">
                            <div class="zui-table-cell">模板编码</div>
                        </th>
                        <th z-field="jm3" z-width="80px">
                            <div class="zui-table-cell">模板名称</div>
                        </th>
                        <th z-field="jms" z-width="80px">
                            <div class="zui-table-cell">模板简码</div>
                        </th>
                        <th z-field="jm4" z-width="100px">
                            <div class="zui-table-cell">质控名称</div>
                        </th>
                        <th z-field="jm5" z-width="100px">
                            <div class="zui-table-cell">审核状态</div>
                        </th>
                        <th z-field="jm6" z-width="100px">
                            <div class="zui-table-cell">病程记录标志</div>
                        </th>
                        <th z-field="jm7" z-width="100px">
                            <div class="zui-table-cell">插入方式</div>
                        </th>
                        <th z-field="jm7" z-width="6px">
                            <div class="zui-table-cell">是否分页</div>
                        </th>
                        <th z-field="jm7" z-width="100px">
                            <div class="zui-table-cell">最近操作时间</div>
                        </th>
                        <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                            <div class="zui-table-cell">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body body-height" id="zui-table">
                <table class="zui-table table-width50-1">
                    <tbody>
                    <tr :tabindex="$index" v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                        <td width="50px">
                            <div class="zui-table-cell">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.hdmc" ></i>
                            </div>

                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="mbfw[item.mbfw]" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.ksmc" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.yhmc" ></i>
                            </div>
                        </td>

                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.mbbm" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.mbmc" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.mbjm" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.zkmc" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="shzt[item.shzt]" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="bcjlbz[item.bcjlbz]"></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="bcjlcrfs[item.bcjlcrfs]" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="sffy[item.sffy]" ></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="fDate(item.zjczsj,'date')"></i>
                            </div>
                        </td>
                        <td width="100px"><div class="zui-table-cell">
                            <i class="icon-bj" @click="edit($index)"></i>
                            <i class="icon-sc icon-font" @click="remove"></i>
                        </div></td>
                    </tr>
                    </tbody>
                </table>

            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

        </div>


    </div>

</div>
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>业务活动记录ID</i>
                    <input type="text" class="label-input background-h" disabled v-model="popContent.ywhdjlid" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>节点名称</i>
                    <input type="text" class="label-input "  v-model="popContent.kbName" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>可见范围</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="ckkjfw" :index="popContent.ckkjfw" :val="popContent.ckkjfw"
                                  :name="'popContent.ckkjfw'">
                    </select-input>
            </li>
            <li>
                    <i>模板范围</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="mbfw" :index="popContent.mbfw" :val="popContent.mbfw"
                                  :name="'popContent.mbfw'">
                    </select-input>
            </li>
            <li>
                    <i>科室</i>
                    <select-input @change-data="resultChange_type"
                                  :not_empty="false" :child="ksList"
                                  :index="'ksmc'" :index_val="'ksmc'"
                                  :val="ksmc" :search="true" :name="'ksmc'"
                                  id="ksmc" :index_mc="'ksmc'">
                    </select-input>
            </li>
            <li>
                    <i>用户</i>
                    <select-input @change-data="resultChange_types"
                                  :not_empty="false" :child="yhList"
                                  :index="'yhmc'" :index_val="'yhmc'"
                                  :val="yhmc" :search="yhmc" :name="'yhmc'"
                                  id="yhmc" :index_mc="'yhmc'">
                    </select-input>
            </li>
            <li>
                    <i>删除标志</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="scbz" :index="popContent.scbz" :val="popContent.scbz"
                                  :name="'popContent.scbz'">
                    </select-input>
            </li>
            <li>
                    <i>审核状态</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="shzt" :index="popContent.shzt" :val="popContent.shzt"
                                  :name="'popContent.shzt'">
                    </select-input>
            </li>
            <li>
                    <i>模板编码</i>
                    <input type="text" class="label-input background-h" disabled v-model="popContent.mbbm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>病程记录标志</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="bcjlbz" :index="popContent.bcjlbz" :val="popContent.bcjlbz"
                                  :name="'popContent.bcjlbz'">
                    </select-input>
            </li>
            <li>
                    <i>用户</i>
                    <input type="text" class="label-input "  v-model="popContent.yhmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>是否分页</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="sffy" :index="popContent.sffy" :val="popContent.sffy"
                                  :name="'popContent.sffy'">
                    </select-input>
            </li>
            <li>
                    <i>插入方式</i>
                    <select-input @change-data="resultChange" :data-notEmpty="false"
                                  :child="bcjlcrfs" :index="popContent.bcjlcrfs" :val="popContent.bcjlcrfs"
                                  :name="'popContent.bcjlcrfs'">
                    </select-input>
            </li>
            <li>
                    <i>模板简码</i>
                    <input type="text" class="label-input "  v-model="popContent.mbjm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>排列顺序</i>
                    <input type="text" class="label-input "  v-model="popContent.plsx" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>质控名称</i>
                    <input type="text" class="label-input "   v-model="popContent.zkmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>ICD编码</i>
                    <input type="text" class="label-input " :data-notEmpty="true"  v-model="popContent.icdbm" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>ICD名称</i>
                    <input type="text" class="label-input " :data-notEmpty="true"  v-model="popContent.icdmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>模板名称</i>
                    <input type="text" class="label-input " :data-notEmpty="true"  v-model="popContent.mbmc" @keydown="nextFocus($event)"/>
            </li>
            <li class="width100">
                    <i class="width150">模板描述</i>
                    <textarea  class="label-input dz-height"  v-model="popContent.mbms" @keydown="nextFocus($event)"></textarea>
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="emrmb.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>