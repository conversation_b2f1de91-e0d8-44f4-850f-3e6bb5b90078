/**
 * Created by mash on 2017/10/8.
 */
(function() {
	var yp_toolMenu = new Vue({
		el: '.yp_toolMenu',
		mixins: [dic_transform, tableBase, mConfirm, baseFunc],
		data: {
			bxlbbm: null,
			bxurl: null,
			searchtext2: null,
			type:'qb',
		},
		methods: {
			sschangeDown2: function() {
				if(window.event.keyCode == 13) {
					yp_toolMenu.searchtext2 = $('#search2').val();
					yp_toolMenu.getData();
				}
			},
			 changeType:function(xType){
				 yp_toolMenu.type=xType;
				 yp_toolMenu.getData();
	            },
			getbxlb: function() {
				var param = {
					bxjk: "004"
				};
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" +
					JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							if(json.d.list.length > 0) {
								yp_toolMenu.bxlbbm = json.d.list[0].bxlbbm;
								yp_toolMenu.bxurl = json.d.list[0].url;
							}
						} else {
							malert("保险类别查询失败!" + json.c)
						}
					});
			},
			// 请求保险类别
			getData: function() {
				var param = {
					'page': 1,
					'rows': 500,
					'parm': yp_toolMenu.searchtext2,
					'yyff':null,
                    'psyp':null,
				};
				 if(yp_toolMenu.type=='yd'){
	                	param.psyp='1';
	                }
	                if(yp_toolMenu.type=='wd'){
	                	param.yyff='1';
	                }
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=queryYp&parm=" + JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							var res = eval('(' + json.d + ')');
							ypXmMx.jsonList = res.list;
						} else {
							malert(json.c);
						}
					});

			},
			// 保存项目详情
			save: function(bxlbbm, ypbm, bxxmbm, bxxmmc) {
				var param = {
					'page': 1,
					'rows': 100,
					'bxlbbm': bxlbbm,
					'ypbm': ypbm,
					/*'bxxmlb': bxxmlb,*/
					'bxxmbm': bxxmbm,
					'bxxmmc': bxxmmc
				};
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=saveYp&parm=" + JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							malert("保存诊疗项目成功！");
							//                            		zl_toolMenu.getData();
						} else {
							malert(json.c);
						}
					});
			},
			// 删除项目详情
			remove: function() {

			},
			//获取药品项目
			loadXm: function() {
				var param = {
					page: 1,
					rows: 100,
					bxlbbm: yp_toolMenu.bxlbbm
				};
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=getYp&parm=" + JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							malert("获取诊疗项目成功！");
							yp_toolMenu.getData();
						} else {
							malert(json.c);
						}
					});
			},
			//自动对码（项目名称）
			autoDm: function() {
				var param = {
					page: 1,
					rows: 100
				};
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=nhdm&method=autoDmYp&parm=" + JSON.stringify(param),
					function(json) {
						if(json.a == 0) {
							malert("自动对码（项目名称）成功！");
							yp_toolMenu.getData();
						} else {
							malert(json.c);
						}
					});
			},
		}
	});
	yp_toolMenu.getbxlb();
	var ypBxXm = new Vue({
		el: '.ypBxXm',
		mixins: [dic_transform, tableBase, mConfirm, baseFunc],
		data: {
			jsonList: [],
			searchCon: []
		},
		methods: {
			getData: function() {
				var param = {
					bxjk: '004'
				}
				$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" +
					JSON.stringify(param),
					function(json) {
						ypBxXm.totlePage = Math.ceil(json.d.total / ypBxXm.param.rows);
						ypBxXm.jsonList = json.d.list;
					});
			},
			checkOne: function() {
				yp_toolMenu.getData();
			}
		}
	});
	ypBxXm.getData();

	var ypXmMx = new Vue({
		el: '.ypXmMx',
		mixins: [dic_transform, baseFunc, tableBase, mformat],
		components: {
			'search-table': searchTable
		},
		data: {
			qjIndex:null,
			jsonList: [],
			isEdit: null,
			text: null,
			page: {
				page: 1,
				rows: 20,
				total: null
			},
			popContent: {},
			searchCon: {},
			selSearch: -1,
			dg: {
				page: 1,
				rows: 20,
				sort: "",
				order: "asc",
				parm: ""
			},
			them: {'项目编码': 'mediCode', '项目名称': 'mediName', '剂型': 'modelName','自付比例': 'selfScale'}
		},
		methods: {
			edit: function(index) {
				console.log(index);
				this.isEdit = index;
			},
			// 点击进行赋值的操作
			selectOne: function(item) {
				if(item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
					this.page.page++; // 设置当前页号
					this.searching(ypXmMx.qjIndex, true,'bxxmmc',ypXmMx.jsonList[ypXmMx.qjIndex].bxxmmc); // 传参表示请求下一页,不传就表示请求第一页
				} else { // 否则就是选中事件,为json赋值
					ypXmMx.popContent = item;
					Vue.set(ypXmMx.jsonList[ypXmMx.qjIndex], 'bxxmmc', ypXmMx.popContent['mediName']);
					ypXmMx.jsonList[ypXmMx.qjIndex].bxxmbm=this.popContent.mediCode;
					//ypXmMx.jsonList[ypXmMx.qjIndex].bxxmlb=this.popContent.yka001;
					//ypXmMx.jsonList[ypXmMx.qjIndex].zfbl=this.popContent.yka096;
					yp_toolMenu.save(yp_toolMenu.bxlbbm, ypXmMx.jsonList[ypXmMx.qjIndex]['xmbm'], ypXmMx.jsonList[ypXmMx.qjIndex]['bxxmbm'], ypXmMx.jsonList[ypXmMx.qjIndex]['bxxmmc']);
					$(".selectGroup").hide();
				}
			},
			changeDown: function(index, event, type) {
				ypXmMx.qjIndex=index;
				if(this['searchCon'][this.selSearch] == undefined) return;
				this.keyCodeFunction(event, 'popContent', 'searchCon');
				if(event.code == 'Enter' || event.code == 13) {
					if(type="text"){
					Vue.set(ypXmMx.jsonList[index], 'bxxmmc', ypXmMx.popContent['mediName']);
					ypXmMx.jsonList[index].bxxmbm=this.popContent.mediCode;
					//ypXmMx.jsonList[index].bxxmlb=this.popContent.yka001;
					//ypXmMx.jsonList[index].zfbl=this.popContent.yka096;
					yp_toolMenu.save(yp_toolMenu.bxlbbm, ypXmMx.jsonList[index]['xmbm'], ypXmMx.jsonList[index]['bxxmbm'], ypXmMx.jsonList[index]['bxxmmc']);
					this.nextFocus(event);
					}
				}
			},
			// 输入内容进行检索
			searching: function(index, add, type, val) {
				ypXmMx.qjIndex=index;
				this.jsonList[index]['bxxmmc'] = val;
				if(!add) this.page.page = 1;
				var _searchEvent = $(event.target.nextElementSibling).eq(0);
				ypXmMx.popContent = {};
				if(ypXmMx.jsonList[index]['bxxmmc'] == undefined || ypXmMx.jsonList[index]['bxxmmc'] == null) {
					this.page.parm = "";
				} else {
					this.page.parm = ypXmMx.jsonList[index]['bxxmmc'];
				}
				var str_param = {
					parm: this.page.parm,
					page: this.page.page,
					rows: this.page.rows,
				}
				$.getJSON(
					"/actionDispatcher.do?reqUrl=New1BxInterface&url=" + yp_toolMenu.bxurl + "&bxlbbm=" + yp_toolMenu.bxlbbm + "&types=ypxx&method=query&parm=" + JSON.stringify(str_param),
					function(json) {
						if(json.a == 0) {
							var res = eval('(' + json.d + ')');
							if(add) {
								for(var i = 0; i < res.list.length; i++) {
									ypXmMx.searchCon.push(res.list[i]);
								}
							} else {
								ypXmMx.searchCon = res.list;
							}
							ypXmMx.page.total = res.total;
							ypXmMx.selSearch = 0;
							if(res.list.length > 0 && !add) {
								$(".selectGroup").hide();
								_searchEvent.show();
							}
						} else {
							malert(json.c);
						}
					});
			}
		}
	});

	$('body').click(function() {
		$(".selectGroup").hide();
	});

	$(".selectGroup").click(function(e) {
		e.stopPropagation();
	});

	//为table循环添加拖拉的div
	var drawWidthNum = $(".patientTable thead tr").eq(2).find("th").length;
	for(var i = 0; i < drawWidthNum; i++) {
		$(".patientTable thead tr th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
	}

})();
