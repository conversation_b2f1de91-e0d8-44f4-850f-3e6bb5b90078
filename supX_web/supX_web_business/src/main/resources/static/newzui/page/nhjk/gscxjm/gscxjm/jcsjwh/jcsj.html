<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>基础数据</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="jcsj.css" rel="stylesheet">
</head>
<body class="skin-default">
<div class="wrapper background-f" id="wrapper" v-cloak>

    <div class="zui-form flex-start" style="padding-top: 15px;">
        <div class="zui-inline">
            <label class="zui-form-label" style="width: 60px;top: 4px;">检索</label>
            <div class="zui-input-inline " style="margin-left: -40px;">
                <input class="zui-input wh180"
                       v-model="search" placeholder="请输入关键字" @keyDown="sschangeDown" id="search"/>
            </div>
        </div>
    </div>
    <div class="zui-table-view hzList padd-r-10 padd-l-10">
        <tabs :num="num" @tab-active="tabBg" :tab-child="[{text:'药品诊疗项目'},{text:'疾病编码'},{text:'农合科室'}]"></tabs>
        <div class="fyxm-size " key="a" v-if="num==0">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s"><span>唯一编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>拼音码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell  cell-l"><span>药品分类</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>规格</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">单价</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">报销比例</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">保内保外</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">统筹类别</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">基药</div>
                        </th>
                    </tr>
                    <!--@click="checkOne($index)"-->
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                <table class="zui-table table-width50" >
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                        @mouseleave="switchIndex()"
                        @click="switchIndex('activeIndex',true,$index)"
                        :tabindex="$index"
                        v-for="(item, $index) in jsonList">
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.wydm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.pydm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l  " v-text="item.ypfl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s  " v-text="item.ypmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.gg"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.dj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.bxbl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.bnbw"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.tclb"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.jyfjy"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
            </div>
            <!--<div class="zui-table-tool">-->
                <!--<div class="zui-table-page">-->
                    <!--<button  class="page-btn jump-btn" type="button"-->
                             <!--@click="goPage(page, null, null)">跳转</button>-->
                    <!--<input type="number" min="1" value="1" v-model="page" class="page-input jump"/>-->
                    <!--<span class="page-count">共 <i class="color-green">{{totlePage}}</i> 页,每页显示多少条</span>-->
                    <!--<span class="page-limits">-->
                        <!--<select lay-ignore="" v-model="param.rows" @change="getData()">-->
                            <!--<option value="10">10 条</option>-->
                            <!--<option value="20">20 条</option>-->
                            <!--<option value="30">30 条</option>-->
                            <!--<option value="40">40 条</option>-->
                            <!--<option value="50">50 条</option>-->
                            <!--<option value="60">60 条</option>-->
                            <!--<option value="70">70 条</option>-->
                            <!--<option value="80">80 条</option>-->
                            <!--<option value="90">90 条</option>-->
                        <!--</select>-->
                    <!--<em class="dot-bottom"></em>-->
                    <!--</span>-->
                    <!--<div class="page-right">-->
                        <!--<a href="javascript:;" class="page-prev" @click="goPage(1, null, null)"-->
                           <!--:class="page<=1?'disabled':''">-->
                            <!--<i class="page-more"></i>-->
                        <!--</a>-->
                        <!--<a href="javascript:;" class="page-prev"-->
                           <!--:class="page<=1?'disabled':''"-->
                           <!--@click="goPage(page, 'prev','getData')">-->
                            <!--<i class="page-prev"></i>-->
                        <!--</a>-->
                        <!--<a :class="{'page-curr': param.page == 1}"-->
                           <!--@click="goPage(1, null, null)">-->
                            <!--<em>1</em>-->
                        <!--</a>-->
                        <!--<a class="page-spr" v-show="prevMore">···</a>-->
                        <!--<a href="javascript:;" data-page=""-->
                           <!--v-for="(item, $index) in totlePage"-->
                           <!--v-text="item"-->
                           <!--:class="{'page-curr': param.page == item}"-->
                           <!--@click="goPage(item, null, null)"-->
                           <!--v-show="showLittle(item)"></a>-->
                        <!--<a class="page-spr" v-show="nextMore">···</a>-->
                        <!--<a class=""-->
                           <!--:class="{'page-curr': param.page == totlePage}"-->
                           <!--@click="goPage(totlePage, null, null)"-->
                           <!--v-text="totlePage"-->
                           <!--v-show="totlePage > 1"></a>-->
                        <!--<a href="javascript:;" class="page-next"-->
                           <!--:class="param.page >= totlePage?'disabled':''"-->
                           <!--@click="goPage(page, 'next','getData')">-->
                            <!--<i class="page-next"></i>-->
                        <!--</a>-->
                        <!--<a href="javascript:;" class="page-next" @click="goPage(totlePage, null, null)"-->
                           <!--:class="param.page >= totlePage?'disabled':''">-->
                            <!--<i class="page-nextMore"></i>-->
                        <!--</a>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</div>-->
        </div>
        <div class="fyxm-size " key="b" v-else-if="num==1">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s"><span>疾病编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>次疾病编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell  cell-l"><span>诊断名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>创建日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>输入码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">五笔码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">导入日期</div>
                        </th>
                    </tr>
                    <!--@click="checkOne($index)"-->
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                <table class="zui-table table-width50" >
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                        @mouseleave="switchIndex()"
                        @click="switchIndex('activeIndex',true,$index)"
                        :tabindex="$index"
                        v-for="(item, $index) in jsonList">
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jbbm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cjbbm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l  " v-text="item.zdmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s  " v-text="item.cjrq"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.srm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.wbm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.drrq"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
            </div>
            <!--<div class="zui-table-tool">-->
                <!--<div class="zui-table-page">-->
                    <!--<button  class="page-btn jump-btn" type="button"-->
                             <!--@click="goPage(page, null, null)">跳转</button>-->
                    <!--<input type="number" min="1" value="1" v-model="page" class="page-input jump"/>-->
                    <!--<span class="page-count">共 <i class="color-green">{{totlePage}}</i> 页,每页显示多少条</span>-->
                    <!--<span class="page-limits">-->
                        <!--<select lay-ignore="" v-model="param.rows" @change="getData()">-->
                            <!--<option value="10">10 条</option>-->
                            <!--<option value="20">20 条</option>-->
                            <!--<option value="30">30 条</option>-->
                            <!--<option value="40">40 条</option>-->
                            <!--<option value="50">50 条</option>-->
                            <!--<option value="60">60 条</option>-->
                            <!--<option value="70">70 条</option>-->
                            <!--<option value="80">80 条</option>-->
                            <!--<option value="90">90 条</option>-->
                        <!--</select>-->
                    <!--<em class="dot-bottom"></em>-->
                    <!--</span>-->
                    <!--<div class="page-right">-->
                        <!--<a href="javascript:;" class="page-prev" @click="goPage(1, null, null)"-->
                           <!--:class="page<=1?'disabled':''">-->
                            <!--<i class="page-more"></i>-->
                        <!--</a>-->
                        <!--<a href="javascript:;" class="page-prev"-->
                           <!--:class="page<=1?'disabled':''"-->
                           <!--@click="goPage(page, 'prev','getData')">-->
                            <!--<i class="page-prev"></i>-->
                        <!--</a>-->
                        <!--<a :class="{'page-curr': param.page == 1}"-->
                           <!--@click="goPage(1, null, null)">-->
                            <!--<em>1</em>-->
                        <!--</a>-->
                        <!--<a class="page-spr" v-show="prevMore">···</a>-->
                        <!--<a href="javascript:;" data-page=""-->
                           <!--v-for="(item, $index) in totlePage"-->
                           <!--v-text="item"-->
                           <!--:class="{'page-curr': param.page == item}"-->
                           <!--@click="goPage(item, null, null)"-->
                           <!--v-show="showLittle(item)"></a>-->
                        <!--<a class="page-spr" v-show="nextMore">···</a>-->
                        <!--<a class=""-->
                           <!--:class="{'page-curr': param.page == totlePage}"-->
                           <!--@click="goPage(totlePage, null, null)"-->
                           <!--v-text="totlePage"-->
                           <!--v-show="totlePage > 1"></a>-->
                        <!--<a href="javascript:;" class="page-next"-->
                           <!--:class="param.page >= totlePage?'disabled':''"-->
                           <!--@click="goPage(page, 'next','getData')">-->
                            <!--<i class="page-next"></i>-->
                        <!--</a>-->
                        <!--<a href="javascript:;" class="page-next" @click="goPage(totlePage, null, null)"-->
                           <!--:class="param.page >= totlePage?'disabled':''">-->
                            <!--<i class="page-nextMore"></i>-->
                        <!--</a>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</div>-->
        </div>
        <div class="fyxm-size " key="c" v-else-if="num==2">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s"><span>科室编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>科室名称</span></div>
                        </th>
                    </tr>
                    <!--@click="checkOne($index)"-->
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <!--<table class="zui-table table-width50" v-if="jsonList.length!=0">-->
                <table class="zui-table table-width50" >
                    <tbody>
                    <tr :class="[{'table-hovers':$index==activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="switchIndex('hoverIndex',true,$index)"
                        @mouseleave="switchIndex()"
                        @click="switchIndex('activeIndex',true,$index)"
                        :tabindex="$index"
                        v-for="(item, $index) in jsonList">
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ksbm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ksmc"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
            </div>
        </div>
        <div class="zui-table-tool">
            <div class="zui-table-page">
                <button  class="page-btn jump-btn" type="button"
                         @click="goPage(page, null, null)">跳转</button>
                <input type="number" min="1" value="1" v-model="page.page" class="page-input jump"/>
                <span class="page-count">共 <i class="color-green">{{totlePage}}</i> 页,每页显示多少条</span>
                <span class="page-limits">
                        <select lay-ignore="" v-model="param_rows" @change="getData()">
                            <option value="10">10 条</option>
                            <option value="20">20 条</option>
                            <option value="30">30 条</option>
                            <option value="40">40 条</option>
                            <option value="50">50 条</option>
                            <option value="60">60 条</option>
                            <option value="70">70 条</option>
                            <option value="80">80 条</option>
                            <option value="90">90 条</option>
                        </select>
                    <em class="dot-bottom"></em>
                    </span>
                <div class="page-right">
                    <a href="javascript:;" class="page-prev" @click="goPage(1, null, null)"
                       :class="page<=1?'disabled':''">
                        <i class="page-more"></i>
                    </a>
                    <a href="javascript:;" class="page-prev"
                       :class="page<=1?'disabled':''"
                       @click="goPage(page, 'prev','getData')">
                        <i class="page-prev"></i>
                    </a>
                    <a :class="{'page-curr': param.page == 1}"
                       @click="goPage(1, null, null)">
                        <em>1</em>
                    </a>
                    <a class="page-spr" v-show="prevMore">···</a>
                    <a href="javascript:;" data-page=""
                       v-for="(item, $index) in totlePage"
                       v-text="item"
                       :class="{'page-curr': param.page == item}"
                       @click="goPage(item, null, null)"
                       v-show="showLittle(item)"></a>
                    <a class="page-spr" v-show="nextMore">···</a>
                    <a class=""
                       :class="{'page-curr': param.page == totlePage}"
                       @click="goPage(totlePage, null, null)"
                       v-text="totlePage"
                       v-show="totlePage > 1"></a>
                    <a href="javascript:;" class="page-next"
                       :class="param.page >= totlePage?'disabled':''"
                       @click="goPage(page, 'next','getData')">
                        <i class="page-next"></i>
                    </a>
                    <a href="javascript:;" class="page-next" @click="goPage(totlePage, null, null)"
                       :class="param.page >= totlePage?'disabled':''">
                        <i class="page-nextMore"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="jcsj.js"></script>
</body>
</html>