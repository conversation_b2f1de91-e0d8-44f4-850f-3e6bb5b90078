<div class="maxContent yzblsj">
    <div class="fyxm-size ybglTable yzglTable">
        <ul class="cssz-list yzHeader">
            <p class="topData">阶段名称</p>
            <li>医嘱类型</li>
            <li>医嘱种类</li>
            <li>医嘱判断</li>
            <li>组号</li>
            <li class="">医嘱名称</li>
            <li>规格</li>
            <li>用法</li>
            <li>单价</li>
            <li>剂量</li>
            <li>剂量单位</li>
            <li>数量</li>
            <li>频次</li>
            <li>医嘱编码</li>
            <li>序号</li>
            <li>总量</li>
            <li>执行天数</li>
            <li>医生说明</li>
            <li>输液速度</li>
            <li>速度单位</li>
            <li>执行科室</li>
            <li>路径项目</li>
            <li>路径名称</li>
            <li>必选项目</li>
            <li>治疗方案</li>
            <li>医嘱是否自动生成</li>
        </ul>
        <div class="zui-table-body body-height yzDataList">
            <ul class="cssz-list">
                <li class="fixed" v-for="(item,index) in jsonList" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index" >
                    <p class="leftData" :style="[{height:item.yzs.length*45+'px'},{lineHeight:item.yzs.length*45+'px'}]">{{item.jdmc}}</p>
                    <ul>
                        <li class="yzContent"  v-for="(init,index) in item.yzs" v-if="item.yzs">
                            <div class="position"><span :style="{width:arrWidth[0]+'px'}" class="title titleText" :data-title="init.yzlx" data-text="医嘱类型">{{init.yzlx}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[1]+'px'}" class="title titleText" :data-title="init.yzlx" data-text="医嘱种类">{{init.yzlx}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[2]+'px'}" class="title titleText" :data-title="init.yzpd" data-text="医嘱判断">{{init.yzpd}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[3]+'px'}" class="title titleText" :data-title="init.yzfzh" data-text="组号">{{init.yzfzh}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[4]+'px'}" class="title titleText" :data-title="init.yzmc" data-text="医嘱名称">{{init.yzmc}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[5]+'px'}" class="title titleText" :data-title="init.xmgg" data-text="规格">{{init.xmgg}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[6]+'px'}" class="title titleText" :data-title="init.zcyyf" data-text="用法">{{init.zcyyf}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[7]+'px'}" class="title titleText" :data-title="init.yfmc" data-text="单价">{{init.yfmc}}</span>
                            </div>
                            <div class="position"><input :style="{width:arrWidth[8]+'px'}" class="title titleText" :data-title="init.jldwmc" v-model="init.jldwmc"
                                                         data-text="剂量"></div>
                            <div class="position"><span :style="{width:arrWidth[9]+'px'}" class="title titleText" :data-title="init.dcjl" data-text="剂量单位">{{init.dcjl}}</span>
                            </div>
                            <div class="position"><input :style="{width:arrWidth[10]+'px'}" class="title titleText" :data-title="init.jldw" v-model="init.jldw"
                                                         data-text="数量"></div>
                            <div class="position"><span :style="{width:arrWidth[11]+'px'}" class="title titleText" :data-title="init.pcmc" data-text="频次">{{init.pcmc}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[12]+'px'}" class="title titleText" :data-title="init.yzbm" data-text="医嘱编码">{{init.yzbm}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[13]+'px'}" class="title titleText" :data-title="init.xsxh" data-text="序号">{{init.xsxh}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[14]+'px'}" class="title titleText" :data-title="init.yyzl" data-text="总量">{{init.yyzl}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[15]+'px'}" class="title titleText" :data-title="init.jldw" data-text="执行天数">{{init.jldw}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[16]+'px'}" class="title titleText" :data-title="init.yssm" data-text="医生说明">{{init.yssm}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[17]+'px'}" class="title titleText" :data-title="init.sysd" data-text="输液速度">{{init.sysd}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[18]+'px'}" class="title titleText" :data-title="init.sysddw" data-text="速度单位">{{init.sysddw}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[19]+'px'}" class="title titleText" :data-title="init.zxksmc" data-text="执行科室">{{init.zxksmc}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[20]+'px'}" class="title titleText" :data-title="init.yzxmmc" data-text="路径项目">{{init.yzxmmc}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[21]+'px'}" class="title titleText" :data-title="init.ryljmc" data-text="路径名称">{{init.ryljmc}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[22]+'px'}" class="title titleText" :data-title="init.xmxzfs" data-text="必选项目">{{init.xmxzfs}}</span>
                            </div>
                            <div class="position"><span :style="{width:arrWidth[23]+'px'}" class="title titleText" :data-title="init.zlfamc" data-text="治疗方案">{{init.zlfamc}}</span>
                            </div>
                            <div class="position titleText" :style="{width:arrWidth[24]+'px'}">
                                <span class="switch" data-text="医嘱是否自动生成"
                                     style="margin: 0;display: initial;left: 0;top:-16%">
                                    <input type="checkbox" v-model="init.ypyzsfzdsc"/>
                                    <label style="display: inherit;"></label>
                                </span>
                            </div>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>
<script src="yz.js" type="text/javascript"></script>