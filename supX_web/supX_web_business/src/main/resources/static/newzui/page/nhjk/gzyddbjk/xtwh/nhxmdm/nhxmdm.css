#nhxmdm {
    height: calc(100% - 43px);
}

.page_div{
    height: 100%;
}

.page_div > div{
    height: 100%;
}

.InfoMenu{
    position: relative;
}

.patientTable tbody tr:first-child th{
    height: 24px;
}

.patientTable th:nth-child(n+2){
    min-width: 112px;
    max-width: 220px;
}

.lastDiv{
    border-right: 1px solid #CCCCCC !important;
    border-radius: 0 2px 2px 0;
}

.InfoMenu div{
    width: 100px;
}

.toolMenu{
    display: block;
    border-bottom: 3px solid #eee;
    margin-bottom: 0;
}

.InfoMenu{
    padding: 6px 0 0 0;
}

.tableDiv{
    padding-top: 10px;
    height: calc(100% - 100px);
}

.tableDiv input{
    width: 100%;
    height: 18px;
}

.zlBxXm, .ypBxXm, .ksbmXm{
    width: 230px;
}

.zlXmMx, .ypXmMx, .ksbmMx{
    width: calc(100% - 250px);
}

.zlXmMx td, .ypXmMx td, .ksbmMx td{
    position: relative;
}

.tableDiv table{
    height: calc(100% - 3px);
}

.selectGroup{
    top: 32px;
}
.selectInput{
    width: 90%;
    height: 30px;
}

.toolMenu2 > div{
    width: 150px;
    height: 30px;
}

.tableDiv table {
    table-layout: fixed; /*只有定义了table下面的td才起作用*/
   height: 480px;
}

.tableDiv table td {
    max-width: 100px;
    overflow: hidden; /*内容超出宽度是隐藏超出部分的内容*/
    text-overflow: ellipsis; /*这两个配合使用（超出部分用...代替）*/

}

.selectGroup tr td {
	min-width: 120px;
    max-width: 250px;
}
#_page input{
	width: 20px;
}
div#_page.pageDiv{
	position: absolute;
	z-index: 9999;
	bottom: 20px;
}
