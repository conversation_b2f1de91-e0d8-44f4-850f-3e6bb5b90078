.text{
    color: #F57B70;
}
.icon-width:before{
    top: 4px;
}
.paddig-left{
    padding-left: 30px;
}

.tong-search .zui-form .zui-inline.padding-left40{
    padding-left: 40px;
}

.tong-search .zui-form .zui-inline.padding-left60{
    padding-left: 60px;
}

.table-width50 .cell-1-1{ /*由于当前tablist列表没有复选框 故不能让main.css中的cell-1-1覆盖掉默认宽度100px!*/
    width: 100px !important;
}
.rydj-info-box,
.syjj-info-box{
    padding-top: 10px;
    background-color: #fff;
    margin-bottom: 52px;
}

.zui-inline, .zui-input-inline, .zui-select-inline{
    margin-right: 0;
}

.zui-form-label{
    width: 70px;
    padding: 8px 10px 8px 0;
}
.zui-form .zui-inline{
    padding-left: 70px;
}

.action-bar.fixed{
    right: 10px;
    left: 10px;
    width: auto;
}

.danwei-box .zui-input{
    padding-right: 34px;
}
.danwei-box .danwei{
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -9.5px;
    color:#1abc9c;
}
.icon-dysqb:before{
    color: #757c83;
}
.flex{
    display: flex;
}
.noSpace{
    white-space: nowrap;
    padding-right: 16px;
}
.text-ov{
    overflow: hidden;
    width: 100%;
    white-space: nowrap;
    display: flow-root;
    text-overflow: ellipsis;
}
.brfb p{
    text-align: center;
    width: calc(100%/10);
}
.brfb-title{
    background:#edf2f1;
    border:1px solid #333333;
    height:34px;
    align-items: center;
    border-bottom: none;
}
.brfb-item{
    border-bottom:1px solid #e9eee6;
}
.brfb-list{
    background:#ffffff;
    border:1px solid #333333;
    height:30px;
    align-items: center;
    border-bottom: none;
}
#syjjInfo{
    height: 100%;
    overflow: auto;
}
.tong-top{
    height: auto;
    display: flex;
}
.tong-btn{
    padding: 0px 11px;
    line-height: 32px;
}

.bgRed{
    background-color: rgba(255, 0, 0, 0.5) !important;
}
.zui-table-body table .bgRed:hover{
    background: rgba(255, 0, 0, 0.5) !important;
}
@media print {
    #syjjInfo{
        font-size: 10px;
    }
}
.wb-print{
    color: #f3a858;
]}
.top-form{
    margin-right: 0;
    float: none;
}
.tab-card{
    padding-right: 0;
}
.width50{
    width: 50%
}
.gk-width-412{
    width: 412px
}
.gz_001{
    height: 100%;
    overflow: auto;
    padding-bottom: 20px;
}
.height-36{
    height: 36px;
    line-height: 36px
}
.color-f2a654{
    color: #f2a654
}
.color-354052{
    color:#354052;
}
.pad-l-46{
    padding-left: 7px;
}
.pad-l-29{
    padding-left: 29px
}
.pad-r-91{
    padding-right: 91px
}
.pad-l-70{
    padding-left: 70px
}
.mad-top-3{
    margin-top: 3px
}
.mad-b-3{
    margin-bottom: 3px
}
.rightVueAll{
    height: calc(100% - 68px);
}
.mzsf-fyje{
    width: 100%;
    background: #fff;
    height: 30px;
    position: static;
    bottom: 0;
    margin:0 10px;
    border: 1px solid #e9eee6;
}
.icon-icon59:before,.icon-iocn38:before,.icon-iocn56:before{
    color: #1abc9c;
    /*font-size: 14px*/
}
.icon-icon58:before{
    color: #1abc9c;
    /*font-size: 14px*/
}
.iconfont.font-14{
    font-size: 14px;
}
.ckbxqd{
    cursor: pointer;
    font-size:14px;
    color:#f3ab5e;
    text-decoration: underline;
}
.zdjb{
    position: relative;
    width: 100%;
}
.zdjb span{

}
.zdjb:after{
    width: 96%;
    content: '';
    right: 0;
    top: 11px;
    opacity:0.2;
    border-bottom: 1px dashed #354052;
    position: absolute;
}
.file{
    background-image: linear-gradient(-180deg, #ffffff 0%, #e6e8e9 100%);
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    width: 80px;
    height: 34px;
    position: relative;
    display: inline-block;
}
.file:after{
    content: '选择文件';
    color:#1abc9c;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100%;
    text-align: center;
    transform: translate(-50%,-50%);
}
.file input{
    display: none;
}
.hqjtcy{
    background-image:linear-gradient(-180deg, #ffffff 0%, #e6e8e9 100%);
    border:1px solid #dfe3e9;
    border-radius:4px;
    width:140px !important;
    text-align: center;
    line-height: 34px;
    height:34px;
    font-size:14px;
    cursor: pointer;
}
.zflb .zui-select-group{
    width: 80%;
}
.zflb .zui-select-group li{
    height: auto;
}
.popshow{
    position: fixed;
    width: 100%;
    height: 100%;
    text-align: center;
    top: 0;
    left: 0;
    background-color: hsla(170, 100%, 4%, .5);
    -webkit-transition: opacity 0.3s ease;
    transition: opacity 0.3s ease;
    z-index: 111111;
}
.box-border{
    background:#ffffff;
    box-shadow:0 0 9px 0 rgba(1,23,18,0.40);
    border-radius:4px;
    padding-bottom: 8px;
    width:220px;
    margin-bottom: 8px;
}
.box-border .box-header{
    background:#fbfbfb;
    border:1px solid #eeeeee;
    border-radius:4px 4px 0 0;
    height:28px;
    font-size:14px;
    color:#333333;
    font-weight: bolder;
    padding-left: 13px;
    line-height: 28px;
}
.box-border .box-contnet{
    padding: 9px 13px 0 ;
}
.color-e96509{
    color: #e96509;
}
.zui-input{
    font-size: 12px;
    height: 28px;
    text-indent: 5px;
}
.popCenter1 .zui-input{
    height: 45px;
    font-size: 24px;
}
.popCenter1 .font-14 {
    font-size: 24px;
}
.tab-card-header-title{
    font-size: 14px;
}
.zui-table-view .setScroll::-webkit-scrollbar{
    width: 10px;
    height: 10px;
}
.zui-table-view .zui-table-fixed.table-fixed-r {
    right: 10px !important;
}
#ykth{
    text-indent: 0;
    padding: 0;
}
#yndryb_009 .zui-table-view .zui-table-body tr td{
    padding: 0;
}
.zdjbChild{
    width: calc(100% / 4);
}

.bqcydj_model{
    width: 650px;
    padding: 14px 15px 10px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.lineDiv{
    padding: 0 56px;
}
#model{
    z-index: 1000;
    zoom: 1;
}
.printTable td {
    border: 1px solid #444444;
    width: 30px;
    height: 30px;
}
.temTable {
    position: absolute;
    display: inline-block;
}
.yzhmHj{
    position: absolute;
    left: 0px;
}
.zdy-width{
    width: calc(100% - 220px);
}

/*61.139.2.69*/

/*821017*/
