(function () {
    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:'',
            apply:'',
            updateList:[],
        },
        created:function(){
        	var s=new Date().getTime()
        	var l=new Date()
        	var e=l.setDate(l.getDate())
            this.apply=this.formDate(s)+' - '+this.formDate(e);
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods:{
        	selCheck: function(){
        		//批量选择审核
        		if(ztable.isChecked.length == 0){
        			malert('请选择要审核的报告单','top','defeadted');
        			return;
        		}
        		wrapper.updateList=[];
        		for (var i = 0; i < ztable.isChecked.length; i++) {
					if(ztable.isChecked[i]){
						wrapper.updateList.push(ztable.jsonList[i]);
					}
				}
        		
        		if(wrapper.updateList.length == 0){
        			malert('请选择要审核的报告单','top','defeadted');
        			return;
        		}
        		
        		var data = '{"list":' + JSON.stringify(wrapper.updateList) + '}';
	       		this.$http.post('/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=checkBg',data).then(function(json) {
	       			 console.log(json);
	            		if(json.body.a == "0"){
	            			malert('审核成功！','top','success');
	            		}else{
	            			malert('审核失败！','top','defeadted');
	            		}
	           })
	           ztable.getData();
        		
        	},
        	refresh: function(){
        		 ztable.getData();
        	},
        	formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            },
            newAdd:function () {
                pop.isShowpopL=true
                pop.isShow=true;
                // malert('111','top','defeadted')
            },
            AddMdel:function () {
                $('.tab-a').hide();
                $('.tab-box').hide();
                $('.tab-add').show();
                $('.Closeshow').hide();
                $('.hidden').show();
            },
            //更多功能
            moreText:function () {
                $(".cgjy-menu").toggle();
            },

            AddClose:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            //作废
            zuofei:function () {
                $(".cgjy-menu").hide();
                pop.isShowpopL=true
                pop.isShow=true
                pop.pop='作废检验申请';
                pop.centent='确定作废该项吗？';
                //malert('111','top','defeadted')
            } ,
            //打印
            daying:function () {
                window.print();
            },
            //查看设备原始数据
            shebei:function () {
                $(".cgjy-menu").hide();
                isTabel.isTabelShow=true;
                isTabel.isShow=true;
                isTabel.lsShow=false;
                isTabel.pcShow=false;
                isTabel.jsShow=false;
                isTabel.jsShowtime=false;
                isTabel.qsShow=false;
                $('.pop-cg> li').css({'width':'25%'});
                isTabel.titles="查看设备原始数据";
                isTabel.jsm="样本号/项目";
                isTabel.addCs="蓝色为未登记申请的样本结果";
            },
            //批量录入
            piliang:function () {
                $(".cgjy-menu").hide();
                isTabel.isTabelShow=true;
                isTabel.isShow=false;
                isTabel.jsShow=true;
                isTabel.jsShowtime=true;
                isTabel.pcShow=false;
                isTabel.lsShow=false;
                isTabel.qsShow=true;
                isTabel.titles="结果批量录入";
                isTabel.sj="样本时间";
                isTabel.jsm="样本号";
                isTabel.addCs="双击删除";

            },
            //批次调整
            pici:function () {
                $(".cgjy-menu").hide();
                isTabel.isTabelShow=true;
                isTabel.isShow=false;
                isTabel.pcShow=true;
                isTabel.jsShow=false;
                isTabel.jsShowtime=true;
                isTabel.lsShow=false;
                isTabel.qsShow=true;
                isTabel.titles="检验结果批次调整";
                isTabel.sj="样本时间";
                isTabel.jsm="样本号";
                isTabel.addCs="说明:调整值输入R*1.5既原来结果的1.5倍，输入R／0.8即原来结果的0.8次调整只针对已保存结果，未审核的样本，如果未保存结果或是已审核不能调整。格式为：R*1.5 前面R不变后面加运行算+-*／后面加值";
            }
        },
        watch:{
        	'apply':function(){
        		var times = wrapper.apply.split(" - ");
        		console.log(times);
        		ztable.param.beginrq = times[0];
        		ztable.param.endrq = times[1];
        		ztable.getData();
        	}
        }
    });
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            jsShow:false,
            pcShow:false,
            jsShowtime:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:''
        },
        methods:{
            //确定删除

            delOk:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                // event.currentTarget.remove();
            }
        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            pcShow:false,
            jsShowtime:false,
            jsShow:false,
            qsShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            addCs:'',
            centent:'',
            cents:'',
            isFold: false,
            listOne:'',
            zbList:'',
            ybh:'',
            upzblist:[],
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods:{
            // //取消
            close: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            // //确定
            saveOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                //成功回调提示
                // malert('111','top','defeadted');
            },
            AddClose:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            } ,
            // //取消
            closes: function () {
            	wapse.upzblist = [];
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('取消合并','top','success');

            },
            // //确定
            confirms:function () {
            	//确认合并
            	if(wapse.upzblist.length == 0){
            		malert('未有合并的指标！','top','defeadted');
            	}else{
            		 var data = '{"list":' + JSON.stringify(wapse.upzblist) + '}';
	        		 this.$http.post('/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=updateZb',data).then(function(json) {
	        			 console.log(json);
                 		if(json.body.a == "0"){
                 			malert('合并成功！','top','success');
             			    $(".side-form-bg").removeClass('side-form-bg')
             			    $(".side-form").addClass('ng-hide');
             			   wapse.upzblist = [];
                 		}else{
                 			malert('合并失败！','top','defeadted');
                 		}
		             })
            		//合并
            		/*$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=updateZb&json=" + JSON.stringify(wapse.upzblist), function(json) {
                 		console.log(json);
                 		if(json.a == "0"){
                 			malert('合并成功！','top','success');
             			    $(".side-form-bg").removeClass('side-form-bg')
             			    $(".side-form").addClass('ng-hide');
             			   wapse.upzblist = [];
                 		}else{
                 			malert('合并失败！','top','defeadted');
                 		}
                 	});*/
            	}
              
            },
            //双击删除
            dbDel:function (event) {
                pop.isShowpopL=true
                pop.isShow=true
                pop.title='删除指标项目';
                pop.centent='确定删除指标项目： 钾 吗？';

            },
            //指标改变
            zbchange:function(index,$event){
            	//zblist中的index
            	console.log(index);
            	//指标编码
            	var zb = wapse.zbList[index];
            	console.log(zb);
            	//当前的bg
            	//var temp =  wapse.listOne;
            	var temp = JSON.parse(JSON.stringify(wapse.listOne));
            	console.log(temp);
            	temp.zbbm = zb.zbxm;
            	//输入框的值--样本号
            	console.log($($event.currentTarget).val());
            	temp.bbbh = $($event.currentTarget).val();
            	//未输入样本
            	if($($event.currentTarget).val()==''){
            		return;
            	}
            	if($($event.currentTarget).val() == wapse.listOne.bbbh){
            		malert('该样本的值，无变化','right','success');
            		return;
            	}
            	//查询
            	$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryZb&param=" + JSON.stringify(temp), function(json) {
             		console.log(json);
             		if(json.a=="0"){
             			if(json.d == null){
             				malert('样本号未有该项目值','right','success');
             			}else{
             				var va = json.d;
             				wapse.zbList[index].jcjg = va;
             				//判断标准
             				if(!isNaN(va)){
             					if(va < zb.n_min){
             						wapse.zbList[index].zt = '-1';
             					}else if(va > zb.n_max){
             						wapse.zbList[index].zt = '1';
             					}else{
             						wapse.zbList[index].zt = '0';
             					}
             				}
             				//保存
             				if(wapse.upzblist.length == 0){
             					//新增
             					wapse.upzblist.push(wapse.zbList[index]);
             				}else{
             					for(var j = 0;j <wapse.upzblist.length;j++){
             						if(wapse.upzblist[j].zbxm == wapse.zbList[index].zbxm){
             							//更新
             							wapse.upzblist[j] = wapse.zbList[index];
             							break;
             						}
             					}
             					//新增
             					wapse.upzblist.push(wapse.zbList[index]);
             				}
             				
             			}
             		}else{
             			malert('未找到相应结果！','right','defeadted')
             		}
             	});
            	
            }


        },
    });
    var ztable=new Vue({
        el:'.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            pcShow:false,
            isShow:false,
            qsShow:false,
            jsShowtime:false,
            jsShow:false,
            ckShow:false,
            title:'',
            sj:'',
            titles:'',
            jsm:'',
            ybh:'',
            addCs:'',
            centent:'',
            cents:'',
            jsonList:'',
            totlePage:'',
            total:'',
            param:{
            	shbz:'0',//未审核
            	parm:'',
            	zxbz:'1',//已保存
            	beginrq:'',
            	endrq:'',
            	rows:10,
            	page:1
            },
            isChecked:[],
            checkList:[],
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods:{
            dblclick:function (index) {
            	wapse.listOne = ztable.jsonList[index];
            	//获得指标
            	$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryBgmx&param=" + JSON.stringify(wapse.listOne), function(json) {
             		console.log("指标list:");
             		console.log(json);
             		if(json.a == "0"){
             			wapse.zbList = json.d.list;
             			console.log(wapse.zbList);
             		}
             	});
                $('.tab-a').show();
                $('.Closeshow').show();
                $('.tab-box').show();
                $('.tab-add').hide();
                $('.hidden').hide();
                wapse.ckShow=false;
                $('.tab-box-list').scroll(function() {
                    var scrTop=$('.tab-table').offset().top;
                    if(scrTop<90){
                        console.log(scrTop);
                        $('.tab-table').css({
                            'position':'absolute',
                            'top':'90px',
                            'z-index':'999'
                        })
                    }else{
                        $('.tab-table').css({
                            'position':'relative',
                            'top':'0'
                        })
                    }
                })
                // 展示注册记录
                wapse.isFold = true;
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
            },
            getData:function(){
            	wapse.upzblist = [];
            	wapse.checkList=[];
            	$.getJSON("/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=queryBg&param=" + JSON.stringify(ztable.param), function(json) {
            		console.log(json);
            		if(json.a == 0){
            			if(json.d.list){
            				ztable.jsonList = json.d.list;
            				ztable.total = json.d.total;
                    		ztable.totlePage = Math.ceil(json.d.total / ztable.param.rows);
            			}
            		}
            	})
            },
            check:function(index){//审核
            	ztable.checkList.push(ztable.jsonList[index]);
            	var data = '{"list":' + JSON.stringify(ztable.checkList) + '}';
	       		this.$http.post('/actionDispatcher.do?reqUrl=LisJyglJybgBgwh&types=checkBg',data).then(function(json) {
	       			 console.log(json);
	            		if(json.body.a == "0"){
	            			malert('审核成功！','top','success');
	            			ztable.getData();
	            		}else{
	            			malert('审核失败！','top','defeadted');
	            		}
	           })
            }
        },
    });
    $(".tab-a >a").click(function () {
        var index=$(this).index();
        console.log(index);
        $(this).addClass('active');
        $(this).siblings().removeClass('active');
        $('.tab-box >.tab-box-list').eq(index).show().siblings().hide();

    });
    laydate.render({
        elem: '.todate',
        eventElem: '.zui-date i.datenox',
        trigger: 'click',
        theme: '#1ab394',
        range:true,
        done:function (value,data) {
        	wrapper.apply = value;
        }
    });


    //ztable.getData();

})()