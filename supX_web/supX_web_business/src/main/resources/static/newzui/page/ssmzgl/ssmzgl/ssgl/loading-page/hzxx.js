var fyxmTab = new Vue({
    el: '.content',
    data: {
        num: 1,
        ssyz: 0,
        jcShow: false,
        setH:'',
        mzShow: true,
    },
    created: function () {
        this.$nextTick(function () {
            if(this.ssyz!=1){
                this.tabBg('yzgl',userNameBg)
            }else{
                this.tabBg('ssyz',userNameBg)
            }
        })
    },
    methods: {
        tabBg: function (page,obj) {
            obj.num = this.num;
            $(".loadPage").load(page + ".html").fadeIn(300);

        }
    },
});
var userNameBg = new Vue({
    el: '.userNameBg',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        Brxx_List: {},
        csqx: {},
        urlPage: '',
        Num: '',
        num: 0,
        page: '',
        jcShow: false,
        mzShow: true,
        qxks:''
    },
    mounted:function () {
        if(sessionStorage.getItem('ssmzUser')){
            Vue.set(this,'Brxx_List',JSON.parse(sessionStorage.getItem('ssmzUser')).userName);
            Vue.set(this,'csqx',JSON.parse(sessionStorage.getItem('ssmzUser')).csqx);
            Vue.set(fyxmTab,'ssyz',JSON.parse(sessionStorage.getItem('ssmzUser')).ssyz);
        }
        window.addEventListener('storage',function (e) {
            if(e.key=='ssmzUser'){
                Vue.set(userNameBg,'Brxx_List',JSON.parse(sessionStorage.getItem('ssmzUser')).userName);
                Vue.set(userNameBg,'csqx',JSON.parse(sessionStorage.getItem('ssmzUser')).csqx);
                Vue.set(fyxmTab,'ssyz',JSON.parse(sessionStorage.getItem('ssmzUser')).ssyz);
            }

        });
    },
    created: function () {
    },
    methods: {
        getHeight:function () {
            this.$nextTick(function () {
                fyxmTab.setH=$('.height').height()-120
            })
        },
    },
});

var pageList='';
function tabBg(page, index, event,num) {
    if (num) {
        userNameBg.page = page
    }
    if (userNameBg.page == page &&pageList==page) {
        if(num==10){
            dybl()
        }
    } else {
        pageList=page;
        userNameBg.page = '';
        $('.isative').removeClass('active');
        fyxmTab.num = index;
        userNameBg.num = index;
        $(event).addClass('active');
        $(".loadPage").load(page + ".html", '', function () {
            if(num==10){
                dybl()
            }

        }).fadeIn(300);
    }

}
function newPage() {
    poplj.isShow=true
}
laydate.render({
    elem: '#time'
    , trigger: 'click'
    , theme: '#1ab394'
    , done: function (value, data) {
        pop.popContent.time = value;
    }
});
