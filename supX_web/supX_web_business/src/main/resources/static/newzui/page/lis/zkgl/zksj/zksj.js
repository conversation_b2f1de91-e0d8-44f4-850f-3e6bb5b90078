    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShow:false,
            detarr:[],
            centent:'',
            title:'',
        },
        methods:{
            delAll:function () {
                var jsondata='';
                if(this.isChecked.length>0){
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            this.detarr.push(slide.zklist[i])
                            jsondata='{"list":' + JSON.stringify(this.detarr) + '}';
                        }
                    }
                }else{
                    malert('请选择需要删除的数据','top','defeadted');
                }
                this.$http.post('/actionDispatcher.do?reqUrl=LisZkgl&types=deleteYcBatch',jsondata).then(function(data) {
                        if(data.body.a==0){
                            malert('删除成功','top','success');
                            left.getSbbm();
                            pop.detarr=[]
                        }else{
                            malert(data.body.c,'top','defeadted');
                            pop.detarr=[]
                        }
                    },
                    function(error) {
                        malert(error,'top','defeadted');
                    });
                pop.isShow=false;
            }

        }
    });
    var left=new Vue({
        el:'#left',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            zklist:{},
            zklistwx:[],
            detarr:[],
        },
        created:function () {
            this.jysb();
        },
        updated:function(){
            changeWin()
        },
        methods:{
            jysb:function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    console.log(json);
                    if (json.a == 0 && json.d.list.length!=0) {
                        topFixed.jysbList = json.d.list;
                          topFixed.jysbObj.sbbm = json.d.list[0].sbbm;
                          topFixed.jysbObj.text = json.d.list[0].hostname;
                          topFixed.getSbbm();
                        // times.getSbbm()
                    } else {
                        malert("获取申请检验设备失败" + json.c,'top','defeadted');
                        return false;
                    }

                });
            },
            yr:function () {
                var jsondata='';
                if(this.isChecked.length>0){
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            this.detarr.push(this.zklist.wx[i])
                            jsondata='{"list":' + JSON.stringify(this.detarr) + '}';
                        }
                    }
                }else{
                    malert('请选择需要移入的数据','top','defeadted');
                }
                this.$http.post("/actionDispatcher.do?reqUrl=LisZkgl&types=insertYrBatch",jsondata).then(function(data) {
                    console.log(data);
                    if(data.body.a==0){
                        left.detarr=[]
                        left.isChecked=[]
                        topFixed.getSbbm()
                    }else{
                        malert(data.body.c,'top','defeadted');
                    }
                })
            },
            edit:function (list,type) {
                closees.type=type;
                closees.zk=list;
                console.log("~~~~~~~~~~~~~")
                console.log(closees.zk);
                closees.type=type
                closees.title='修改批号';
                closees.msShow=true;
                closees.zk.sb= topFixed.jysbObj.text
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
            },
        },
    })
    var right=new Vue({
        el:'#right',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            zklist:{},
            detarr:[],
            zklistyx:[],
            isCheckeds:[],
            childelist:[],
            activeIndex1:null,
            hoverIndex1:null,
            hoverIndex2:null,
            activeIndex12:null
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        updated:function(){
            changeWin()
        },
        methods:{
            edit:function (list,type) {
                closees.type=type;
                closees.zk=list;
                closees.zk.yxrq=this.$options.filters['formDate'](closees.zk.yxrq);
                closees.zk.qyzt=list.qyzt == '1' ? true : false;
                closees.type=type
                closees.title='修改批号';
                closees.msShow=true;
                closees.zk.sb= topFixed.jysbObj.text
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');
            },
            reCheckBoxs:function(val){
                if(val[0]=='some'){
                    Vue.set(this.isCheckeds, val[1], val[2]);
                }   else{
                    this.isCheckAll = val[2];
                    console.log(this.isCheckAll);
                    if (val[1] == null) val[1] = "jsonList";
                    if (this.isCheckAll) {
                        for (var i = 0; i < this[val[1]].length; i++) {
                            Vue.set(this.isCheckeds, i, true);
                        }
                    } else {
                        this.isCheckeds = [];
                    }
                }
            },
            yc:function () {
                var jsondata='';
                if(this.isCheckeds.length>0){
                    for (var i = 0; i < this.isCheckeds.length; i++) {
                        if (this.isCheckeds[i] == true) {
                            this.detarr.push(this.zklist.yx[i])
                            jsondata='{"list":' + JSON.stringify(this.detarr) + '}';
                        }
                    }
                }else{
                    malert('请选择需要移除的数据','top','defeadted');
                }
                this.$http.post("/actionDispatcher.do?reqUrl=LisZkgl&types=deleteYcBatch",jsondata).then(function(data) {
                    console.log(data);
                    if(data.body.a==0){
                        right.isCheckeds=[]
                        right.detarr=[]
                        topFixed.getSbbm()
                    }else{
                        malert(data.body.c,'top','defeadted');
                    }
                })
            },
            ck:function (list) {
                //if(event.target.nodeName.toLocaleLowerCase()!="i"){
                    this.paramList=list;
                    console.log("-------------")
                    console.log( this.paramList);
                    //list.zbxm=this.jysbObj.zbxm
                    // console.log(JSON.stringify(this.jysbObj));
                    closees.nowZbxm=this.paramList.zbxm;
                    closees.nowJyfz=this.paramList.jyfz;
                    var data='{"obj":' + JSON.stringify(this.paramList) + '}';
                    this.$http.post("/actionDispatcher.do?reqUrl=LisZkgl&types=queryWphByZkzb",data).then(function(json) {
                    	json=json.body;
                    	if(json.a==0){
                    		console.log(json.d.list);
                            right.childelist=json.d.list;
                            // console.log(JSON.stringify(json)+"获取");
                        }
                        else {
                            malert("数据获取失败!" + json.c,'top','defeadted');
                            return false;
                        }
                    })
                   /* $.getJSON("/actionDispatcher.do?reqUrl=LisZkgl&types=queryWphByZkzb&yq="+JSON.stringify(this.paramList), function (json) {
                        console.log(json);
                        if(json.a==0){
                            right.childelist=json.d.list;
                            // console.log(JSON.stringify(json)+"获取");
                        }
                        else {
                            malert("数据获取失败!" + json.c,'top','defeadted');
                            return false;
                        }
                    });*/
               // }
            },
        },
    })
    var topFixed=new Vue({
        el:'#penal',
        data:{
            jysbObj:{
                sbbm:'',
                zbxm:'',
                text:'',
            },
            jysbList:[],
        },
        methods:{
            yc:function () {
              right.yc()
            },
            yr:function () {
                left.yr()
            },
            selectdata:function (value) {
                this.jysbObj.sbbm=value[0]
                this.jysbObj.text=value[4]
                this.getSbbm()
            },
            getSbbm:function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisZkgl&types=queryAllZkzb&yq="+JSON.stringify(topFixed.jysbObj), function (json) {
                    console.log(json);
                    if(json.a==0){
                        left.zklist=json.d;
                        left.zklistwx=json.d.wx;
                        right.zklist=json.d;
                        right.zklistyx=json.d.yx;
                    }
                    else {
                        malert("检验设备获取失败" + json.c,'top','defeadted');
                        return false;
                    }
                });
            },
            //新增批号
            AddNumber:function (type) {
            	if(type='top'){
            		closees.zk={};
            		closees.zk.qyzt= true;
            	}
                closees.type=type
                closees.title='新增批号';
                closees.msShow=true;
                closees.zk.sb= this.jysbObj.text
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');

            },

        },
    })
    var closees=new Vue({
        el:'#brzcList',
        data:{
            msShow:false,
            title:'',
            zk:{},
            type:'',
            nowZbxm:'',
            nowJyfz:''
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods:{
            AddClose:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
            },
            //侧滑确定按钮
            lightOk:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('保存成功','top','success');
            },
            addOk:function () {
                var url=this.type=='top'?'/actionDispatcher.do?reqUrl=LisZkgl&types=insertWph':'/actionDispatcher.do?reqUrl=LisZkgl&types=updateWph'
                closees.zk.qyzt=closees.zk.qyzt==true?1:0;
                closees.zk.sbbm=topFixed.jysbObj.sbbm;
                closees.zk.zbxm=this.nowZbxm;
                closees.zk.jyfz=this.nowJyfz;
                if(this.nowZbxm=='' || this.nowJyfz==''){
                	malert('请先选择质控项目！','top','defeadted');
                	return;
                }

               var  data='{"obj":' + JSON.stringify(closees.zk) + '}';
                console.log(data);
                this.$http.post(url,data).then(function(json) {
                    if(json.body.a == 0){
                        malert('新增成功！！','top','success');
                        $(".side-form-bg").removeClass('side-form-bg')
                        $(".side-form").addClass('ng-hide');
                        right.ck();
                    }else{
                        malert('新增失败！！','top','defeadted');
                    }

                })
            }


        }
    });
    laydate.render({
        elem: '#ltime',
        trigger: 'click',
        theme: '#1ab394',
        format: 'yyyy-MM-dd',
        done:function (value,data) {
            closees.zk.yxrq=value;
        }
    });
