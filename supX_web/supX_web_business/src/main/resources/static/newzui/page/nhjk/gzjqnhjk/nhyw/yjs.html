<div id="yjs">
    <div class="tong-top">
        <button class="tong-btn btn-parmary icon-sx1 paddr-r5">刷新</button>
        <button class="tong-btn btn-parmary-b paddr-r5">医保出院</button>
        <button class="tong-btn btn-parmary-b paddr-r5">取消出院</button>
        <button class="tong-btn btn-parmary-b paddr-r5">预结算</button>
        <button class="tong-btn btn-parmary-b paddr-r5">补偿打印</button>
    </div>
 <vue-scroll :ops="pageScrollOps">
 <div class="hyjls padd-t-30 padd-l-10 padd-r-10">


        <div class="jbxx margin-b-15">
            <div class="jbxx-size">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">入院信息</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <div class="jbxx-box flex-start padd-t-10 padd-b-10 padd-l-10 padd-r-10">
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">住院号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">病人姓名</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">性&ensp;&ensp;&ensp;&ensp;别</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" disabled/>
                            <!--<select-input class="wh182 background-f" @change-data="resultChange" :not_empty="false"-->
                            <!--:child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"-->
                            <!--:name="'popContent.brxb'">-->
                            <!--</select-input>-->
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">床位号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">人员类别</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">出生日期</label>
                        <div class="top-zinle">
                            <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                            <input type="text" class="zui-input background-f times1 text-indent-20" disabled>
                        </div>

                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">病人年龄</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input  fl margin-r-5 wh182"   disabled/>
                        </div>

                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">入院科室</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">住院医师</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f"disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">入院日期</label>
                        <div class="top-zinle flex-start">
                            <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                            <input type="text" class="zui-input background-f times2 text-indent-20 wh182" disabled>
                        </div>

                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">费别</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">联系电话</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" />
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">保险病人</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f"/>
                            <!--<select-input @change-data="resultChange" :data-notEmpty="false"-->
                            <!--:child="istrue_tran" :index="popContent.bxbr" :val="popContent.bxbr"-->
                            <!--:name="'popContent.bxbr'">-->
                            <!--</select-input>-->
                        </div>
                    </div>

                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">保险类别</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">保险卡号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" />
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">出院情况</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" />
                            <!--<select-input @change-data="resultChange"-->
                                          <!--:child="gznhcyqk_tran" :index="popContent.cyqk" :val="popContent.cyqk"-->
                                          <!--:search="true" :name="'popContent.cyqk'":not_empty="true">-->
                            <!--</select-input>-->
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">出院类型</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input  fl margin-r-5 wh182"   disabled/>
                            <!--<select-input @change-data="resultChange" :not_empty="true"-->
                                          <!--:child="gznhcy_tran" :index="popContent.cylx" :val="popContent.cylx"-->
                                          <!--:search="true" :name="'popContent.cylx'":not_empty="true">-->
                            <!--</select-input>-->
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">保险费</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" />
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label">是否转诊</label>
                        <div class="top-zinle  background-f">
                            <input type="text" class="zui-input wh182 background-f"/>
                            <!--<select-input  @change-data="resultChange"-->
                            <!--:child="istrue_tran" :index="'sfzz'" :index_val="'sfzz'" :val="sfzz"-->
                            <!--:name="'popContent.sfzz'" :search="true" :index_mc="'sfzz'">-->
                            <!--</select-input>-->

                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">转诊号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">银行卡号</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f"/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">银行卡号开户人姓</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f" />
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">患者与开户人关系</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 background-f"/>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="jbxx margin-b-15">
            <div class="jbxx-size">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">其他信息</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <div class="jbxx-box flex-start padd-t-10 padd-b-10 padd-l-10 padd-r-10">
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">院内费用金额</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">上传合计金额</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">预结算补偿金额</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">新精准优抚补偿</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">预交金额</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">预结算退补金额</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>

                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">民政救助</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input  fl margin-r-5 wh182"   disabled/>
                        </div>

                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">新精准残联补偿</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">大病商保</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">兜底补偿</label>
                        <div class="top-zinle flex-start">
                            <i class="icon-position iconfont icon-icon61 icon-c4 icon-font20"></i>
                            <input type="text" class="zui-input  times2 text-indent-20 wh182" disabled>
                        </div>

                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">精准目录</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">新精准疾控补偿</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">交通补贴</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182" disabled />
                        </div>
                    </div>

                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">医疗补贴</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">计生救助</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">新精准扶贫补偿</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled />
                        </div>
                    </div>
                    <div class="top-form margin-b-10 flex-start margin-r-20">
                        <label class="top-label ">生活补贴</label>
                        <div class="top-zinle">
                            <input type="text" class="zui-input wh182 " disabled/>
                        </div>
                    </div>
                </div>
            </div>

        </div>
     <div class="zui-table-view">
         <!--入院登记start-->
         <div class="zui-table-header">
             <table class="zui-table">
                 <thead>
                 <tr>
                     <th><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                     <th><div class="zui-table-cell cell-s"><span>个人编号</span></div></th>
                     <th><div class="zui-table-cell cell-s"><span>姓名</span></div></th>
                     <th><div class="zui-table-cell cell-l"><span>上传费用合计</span></div></th>
                     <th><div class="zui-table-cell cell-s"><span>保内费用</span></div></th>
                     <th><div class="zui-table-cell cell-s"><span>自费费用</span></div></th>
                     <th><div class="zui-table-cell cell-s"><span>实际补偿</span></div></th>
                     <th><div class="zui-table-cell cell-s"><span>起付钱</span></div></th>
                 </tr>
                 </thead>
             </table>
         </div>
         <div class="zui-table-body"  @scroll="scrollTable($event)">
             <table class="zui-table">
                 <tbody>
                 <tr v-for="(item, $index) in 10"
                     :tabindex="$index"
                     @dblclick="edit(item)"
                     ref="list"
                     :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                     @mouseenter="hoverMouse(true,$index)"
                     @mouseleave="hoverMouse()">
                     <td><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                     <td><div class="zui-table-cell cell-s">个人编号</div></td>
                     <td><div class="zui-table-cell cell-s">姓名</div></td>
                     <td><div class="zui-table-cell cell-l">上传费用合计</div></td>
                     <td><div class="zui-table-cell cell-s">保内费用</div></td>
                     <td><div class="zui-table-cell cell-s">自费费用</div></td>
                     <td><div class="zui-table-cell cell-s">实际补偿</div></td>
                     <td><div class="zui-table-cell cell-s">起付钱</div></td>
                     <!--绑定数据放开 数据为空的时候占位-->
                     <!--<p v-if="bsdList.length==0" class=" noData  text-center zan-border">暂无数据...</p>-->
                 </tr>
                 </tbody>
             </table>


         </div>


     </div>

 </div>

     </vue-scroll>
</div>
<style type="text/css">
    .hyjls{
        height:77vmin;
    }

</style>
<script type="application/javascript" src="yjs.js"></script>