<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>词库项目</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ckxm.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="jyxm_icon">
    <div class="emrmb-left">
        <div class="zui-form">
            <div class="zui-inline">
                <label class="zui-form-label">检索</label>
                <div class="zui-input-inline">
                    <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                </div>
            </div>
        </div>
        <div class="ksywhd-top" v-for="(item,$index) in leftList">
            <div class="ckxm-list" @click="ejMore?ejUp():ejDown()">
            <i class="ksywhd-toggle ksywhd-toggleup" :class="{'ksywhd-toggledown':ejShow}"></i><i class="ksywhd-title"  :key="item" v-text="item.name"></i>
            </div>
            <div class="emrmb-list" v-show="ejShow">
                <div class="emrmb-yj" v-for="(a,$index) in item.tree">
                    <span class="emrmb-yj-title"><i class="ksywhd-toggle ksywhd-toggleup" onclick="yjClick(this)"></i><i class="padd-l-10" v-text="a.name" @click="getR(a.seq)"></i></span>
                    <ul class="emrmb-ej-detail">
                        <li v-for="(b,$index) in a.tree" @click="getData(b.seq)"><i class="icon-bd"></i><i class="padd-l-5" v-text="b.name"></i></li>
                    </ul>
                </div>
            </div>

        </div>

    </div>
    <div class="emrmb-right">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="Add">添加</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
                <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
                <button class="tong-btn btn-parmary-b icon-width icon-dc padd-l-25">导出</button>
                <button class="tong-btn btn-parmary-b  icon-dysq paddr-r5">打印</button>
            </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalues" @keydown="searchRc()"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view ybglTable" id="utable1" z-height="full" style="margin-top:0; padding:0;">
            <div class="zui-table-header">
                <table class="zui-table table-width50-1">
                    <thead>
                    <tr>
                        <th z-fixed="left" z-style="text-align:center; width:50px" style="width: 50px !important;">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th z-field="sexs" z-width="100px">
                            <div class="zui-table-cell">词库项目id</div>
                        </th>
                        <th z-field="sex" z-width="100px">
                            <div class="zui-table-cell">项目文本值</div>
                        </th>
                        <th z-field="city" z-width="100px">
                            <div class="zui-table-cell">项目数值</div>
                        </th>
                        <th z-field="jm1" z-width="80px">
                            <div class="zui-table-cell">节点序号</div>
                        </th>
                        <th z-field="jm2" z-width="100px">
                            <div class="zui-table-cell">排序号</div>
                        </th>
                        <th z-field="jm3" z-width="80px">
                            <div class="zui-table-cell">医疗机构编码</div>
                        </th>
                        <th z-width="100px" z-fixed="right" z-style="text-align:center;">
                            <div class="zui-table-cell">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body body-height" id="zui-table">
                <table class="zui-table table-width50-1">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :tabindex="$index" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                        <td width="50px">
                            <div class="zui-table-cell">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.ckxmid" :data-title="item.ckxmid"></i>
                            </div>

                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.itemText" :data-title="item.itemText"></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.itemValue" :data-title="item.itemValue"></i>
                            </div>
                        </td>

                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.kbSeq" :data-title="item.kbSeq"></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.listindex" :data-title="item.listindex"></i>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell relative">
                                <i class="title title-width" v-text="item.yljgbm" :data-title="item.yljgbm"></i>
                            </div>
                        </td>
                        <td width="100px"><div class="zui-table-cell">
                            <i class="icon-bj" @click="edit($index)"></i>
                            <i class="icon-sc icon-font" @click="remove"></i>
                        </div></td>
                    </tr>
                    </tbody>
                </table>

            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>


    </div>

</div>
<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>词库项目id</i>
                    <input type="text" class="label-input background-h" disabled v-model="popContent.ckxmid" @keydown="nextFocus($event)"/>
            </li>

            <li>
                    <i>节点序号</i>
                    <input type="text" class="label-input " disabled  v-model="popContent.kbSeq" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>项目数值</i>
                    <input type="text" class="label-input "  v-model="popContent.itemValue" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>排序号</i>
                    <input type="text" class="label-input "  v-model="popContent.listindex" @keydown="nextFocus($event)"/>
            </li>
            <li class="width100">
                    <i class="width150">项目文本值</i>
                    <textarea  class="label-input dz-height"  v-model="popContent.itemText" @keydown="nextFocus($event)"></textarea>
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ckxm.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>