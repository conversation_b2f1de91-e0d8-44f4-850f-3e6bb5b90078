<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>待会诊管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="/newzui/newcss/main.css" rel="stylesheet">
    <link type="text/css" href="dhzgl.css" rel="stylesheet"/>
</head>

<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="background-box">
    <div class="wrapper" id="jyxm_icon" >
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary-b" @click="getData"><i class="iconfont icon-iocn56 icon-c1"></i>刷新</button>
            </div>
            <div class="tong-search">
                <div class="top-form">
                    <label class="top-label">科室</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <select-input :cs="true" @change-data="ksResultChange" :not_empty="false"
                                          :child="qxksList" :index="'ksmc'" :index_val="'ksbm'" :val="param.ksbm"
                                          :name="'param.ksbm'">
                            </select-input>
                        </div>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">时间段</label>
                    <div class="top-zinle">
                        <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                        <input type="text" class="zui-input wh122 times text-indent-20"/>
                    </div>
                    <div class="top-zinle padd-r-5 padd-l-5">
                        至
                    </div>
                    <div class="top-zinle">
                        <i class="icon-position iconfont icon-icon61 icon-c4"></i>
                        <input type="text" class="zui-input wh122 times1 text-indent-20"/>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">检索</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <input class="zui-input todate wh182 text-indent-10" v-model="param.parm" placeholder="请输入关键字" id="timeVal"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view" v-cloak  >
            <div class="zui-table-header">
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>住院号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>姓名</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>性别</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>科室</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>申请人</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>路径名称</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>发生时间</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>发生事件</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body "   @scroll="scrollTable($event)">
                <table class="zui-table zui-collapse">
                    <tbody>
                    <tr v-for="(item,$index) in byshList" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td>
                            <div class="zui-table-cell cell-m">
                                <span v-text="$index+1"></span></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                {{item.zyh}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"> {{item.brxm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m">{{brxb_tran[item.brxb]}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                {{ksContent.ksmc}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.pgrxm}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl">{{item.ryljmc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l">{{fDate(item.pgsj,'datetime')}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.pgjg}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l">
                                <i v-if="item.shzt == '1'" class="text-line color-cff5" @click="refuseFun(item)">拒绝</i>
                                <i v-if="item.shzt == '1'"class="text-line color-dsh" @click="agreeFun(item)"  style="margin-left: 15%">同意</i>
                                <i v-if="item.shzt != '1'"class="text-line color-c00" @click="cancelFun(item)">取消</i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--暂无数据提示,绑数据放开-->
                <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

    </div>
</div>


<script src="dhzgl.js"></script>
</body>

</html>
