<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>交款列表</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div style="padding: 15px;" id="cwjkList">
    <div class="panel">
        <div class="panel-head border-bottom background">
            <div class="zui-row">
                <div class="col-x-10">
                    <div class="zui-input-inline zui-date wh150">
                        <i class="datenox fa-calendar"></i>
                        <input type="text" name="phone" class="zui-input todats" placeholder="开始时间" />
                    </div>
                    <div class="zui-input-inline zui-date wh150">
                        <i class="datenox fa-calendar"></i>
                        <input type="text" name="phone" class="zui-input todate" placeholder="结束时间" />
                    </div>
                    <button class="zui-btn btn-primary">查询</button>
                </div>
                <div class="col-x-2 text-right">
                    <div id="fieldlist" style="display:inline-block;"></div>
                </div>
            </div>
        </div>
        <!--表格区-->
        <div class="zui-table-view" style="border:none;">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th fixed="left" style="text-align:center;">
                            <div class="zui-table-cell"><input type="checkbox" @click="checkAll($event)" id="check0_0" class="zui-checkbox"/>
                                <label for="check0_0"></label>
                            </div>
                        </th>
                        <th field="id" width="100px" fixed="left" >
                            <div class="zui-table-cell"><span>交款凭证号</span></div>
                        </th>
                        <th field="username" width="100px" :sort="true">
                            <div class="zui-table-cell"><span>操作员</span></div>
                        </th>
                        <th field="sex" width="120px">
                            <div class="zui-table-cell"><span>预交金</span></div>
                        </th>
                        <th field="city" width="80px">
                            <div class="zui-table-cell"><span>住院预交</span></div>
                        </th>
                        <th field="sign" width="100px">
                            <div class="zui-table-cell"><span>费用合计</span></div>
                        </th>
                        <th field="experience" width="200px">
                            <div class="zui-table-cell"><span>医保卡支付</span></div>
                        </th>
                        <th field="score" width="130px">
                            <div class="zui-table-cell"><span>医疗卡支付</span></div>
                        </th>
                        <th field="classify" width="140px">
                            <div class="zui-table-cell"><span>现金支付</span></div>
                        </th>
                        <th field="wealth" width="100px">
                            <div class="zui-table-cell"><span>价款日期</span></div>
                        </th>
                        <th field="jkzt" width="100px">
                            <div class="zui-table-cell"><span>交款状态</span></div>
                        </th>
                        <th field="sjbz" width="100px">
                            <div class="zui-table-cell"><span>上交标志</span></div>
                        </th>
                        <th field="sjuser" width="100px">
                            <div class="zui-table-cell"><span>上交人员</span></div>
                        </th>
                        <th field="sjdate" width="100px">
                            <div class="zui-table-cell"><span>上交日期</span></div>
                        </th>
                        <th field="zfuser" width="100px">
                            <div class="zui-table-cell"><span>作废人员</span></div>
                        </th>
                        <th field="zfdate" width="100px">
                            <div class="zui-table-cell"><span>作废日期</span></div>
                        </th>
                        <th field="jkqj" width="100px">
                            <div class="zui-table-cell"><span>交款区间</span></div>
                        </th>
                        <th field="ywck" width="100px">
                            <div class="zui-table-cell"><span>业务窗口</span></div>
                        </th>
                        <th field="bzxm" width="100px">
                            <div class="zui-table-cell"><span>备注说明</span></div>
                        </th>
                        <th field="ztl" width="100px" fixed="right" style="text-align:center;">
                            <div class="zui-table-cell">状态栏</div>
                        </th>
                        <th width="100px"  fixed="right" style="text-align:center;">
                            <div class="zui-table-cell"></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body">
                <table class="zui-table">
                    <tbody>
                    <!--:checked="isChecked[$index+1]"-->
                    <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                        :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                        @dblclick="edit($index)">
                        <th fixed="left" style="text-align:center;">
                            <div class="zui-table-cell"><input type="checkbox" @click="checkOne($event,$index+1)" :checked="isChecked[$index+1]"  :id='"check0_"+$index+1' class="zui-checkbox"/><label :for='"check0_"+$index+1'></label></div>
                        </th>
                        <th class="tableNo" ><div class="zui-table-cell" v-text="$index+1"></div></th>
                        <td ><div class="zui-table-cell" v-text="item.jkpzh"></div></td>
                        <td ><div class="zui-table-cell" v-text="item.czyxm"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDec(item.yjje)"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDec(item.zyyj)"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDec(item.fyhj)"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDec(item.ybkzf)"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDec(item.ylkzf)"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDec(item.xjzf)"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDate(item.jkrq,'date')"></div></td>
                        <td ><div class="zui-table-cell" v-text="item.jkzt==0 ? '正常':'作废'"></div></td>
                        <td ><div class="zui-table-cell" v-text="item.sjbz==0 ? '未上交':'上交'"></div></td>
                        <td ><div class="zui-table-cell" v-text="item.sjryxm"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDate(item.sjrq,'date')"></div></td>
                        <td ><div class="zui-table-cell" v-text="item.zfryxm"></div></td>
                        <td ><div class="zui-table-cell" v-text="fDate(item.zfrq,'date')"></div></td>
                        <td ><div class="zui-table-cell" v-text="item.jkqj"></div></td>
                        <td ><div class="zui-table-cell" v-text="item.ywckmc"></div></td>
                        <td ><div class="zui-table-cell" v-text="item.bzsm"></div></td>
                        <td>
                            <div class="zui-table-cell">
                                <div class="cell_dropdown">
                                    <button class="zui-btn btn-blue btn-xs">操作<i class="ion-android-arrow-dropdown"></i></button>
                                    <ul>
                                        <li><a href="javascript:"><i class="fa-search success"></i>查看明细</a></li>
                                        <li v-if="item.sjbz==0"><a href="javascript:"><i class="fa-minus-square-o blue"></i>取消交款</a></li>
                                        <li v-if="item.jkzt==0"><a href="javascript:"><i class="fa-trash danger"></i>作废交款</a></li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
    <!--<div class="action-bar fixed">-->
        <!--<button class="zui-btn btn-primary">发起交款(F2)</button>-->
    <!--</div>-->
</div>
<script type="application/javascript" src="jklb.js"></script>
</body>
</html>
