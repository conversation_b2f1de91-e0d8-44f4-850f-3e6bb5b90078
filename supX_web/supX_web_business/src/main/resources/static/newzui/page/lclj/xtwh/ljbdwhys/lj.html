<div class="col-x-12">
    <div class="col-x-2 left-tree leftTree">
        <div class="tree_tem1">
            <ul class="item">
                <li class="xtmktreediv">
                    <img src="/newzui/pub/image/toggle03.png" @click="getShow()" class="toggleIMg">
                    <div class="tree_text1 " @click="toggle(0,0,'',$event)">路径病种</div>
                    <ul class="childer tree_tem1">
                        <li class="xtmktreediv " v-for="(item,inSex) in jsonList">
                            <img :src="toggthree" @click="getList(item,inSex,$event)" class="toggleIMg JsonImg">
                            <div class="tree_text1" @click="toggle(1,1,inSex,$event)">{{item.ljmc}}</div>
                            <ul class="item tree_tem1 hideList Json" >
                                <li class="xtmktreediv" v-for="(list,inDex) in cjsonList">
                                    <img :src="toggthree" @click="getChild(list,inDex,$event)" class="toggleIMg CjsonImg">
                                    <div class="tree_text1"  @click="toggle(1,2,inDex,$event)">{{list.jdmc}}</div>
                                    <ul class="item tree_tem1 hideList Cjson">
                                        <li class="xtmktreediv" v-for="(twoList,$index) in twojsonList">
                                            <img :src="toggthree" @click="getChildDatel(twoList,$index,$event)" class="toggleIMg sdsdzzzz twojsonImg">
                                            <div class="tree_text1" @click="toggle(2,3,$index,$event)">{{twoList.ejjdmc}}</div>
                                            <ul class="item tree_tem1 hideList twojson">
                                                <li class="xtmktreediv" v-for="(dList,indEx) in datelitem">
                                                    <img src="/newzui/pub/image/toggle01.png" @click="datel(dList,indEx)" class="toggleIMg sdsd">
                                                    <div class="tree_text1" @click="toggle(3,4,indEx,$event)">{{dList.xmmc}}</div>
                                                </li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    <div class="col-x-10 zui-table-view">
        <div class="ybglTablelist">
            <!--路径病种 -->
            <div class="fyxm-size" v-if="num==0">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th z-field="a0" z-width="60px" z-style="text-align:center;">
                                <div class="zui-table-cell">编码</div>
                            </th>
                            <th z-field="a1" z-width="60px" z-style="text-align:center;">
                                <div class="zui-table-cell">名称</div>
                            </th>
                            <th z-field="a2" z-width="100px">
                                <div class="zui-table-cell">拼音编码</div>
                            </th>
                            <th z-field="a3" z-width="100px">
                                <div class="zui-table-cell">病例分析</div>
                            </th>
                            <th z-field="a4" z-width="100px">
                                <div class="zui-table-cell">适用病情</div>
                            </th>
                            <th z-field="a5" z-width="100px">
                                <div class="zui-table-cell">适用性别</div>
                            </th>
                            <th z-field="a6" z-width="100px">
                                <div class="zui-table-cell">分类</div>
                            </th>
                            <th z-field="a7" z-width="100px">
                                <div class="zui-table-cell">最小年龄</div>
                            </th>
                            <th z-field="a8" z-width="100px">
                                <div class="zui-table-cell">最大年龄</div>
                            </th>
                            <th z-field="a9" z-width="100px">
                                <div class="zui-table-cell">年龄单位</div>
                            </th>
                            <th z-field="a10" z-width="100px">
                                <div class="zui-table-cell">标准天数</div>
                            </th>
                            <th z-field="a11" z-width="100px">
                                <div class="zui-table-cell">适用于所有科室</div>
                            </th>
                            <th z-field="a12" z-width="100px">
                                <div class="zui-table-cell">停用标志</div>
                            </th>
                            <th z-field="a14" z-width="100px">
                                <div class="zui-table-cell">适用对象</div>
                            </th>
                            <th z-field="a15" z-fixed="right" z-width="100px">
                                <div class="zui-table-cell">操作</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body body-height">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :tabindex="$index" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                            <td>
                                <div class="zui-table-cell" v-text="item.ljbm">编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.ljmc">名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.pydm">拼音编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.blfx">病例分析</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.sybq">适用病情</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="lcljsyxb_tran[item.syxb]">适用性别</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.fl">分类</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.synlMin">最小年龄</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.synlMax">最大年龄</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="nldw_tran[item.synlDw]">年龄单位</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.bzts">标准天数</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="istrue_tran[item.syks]">适用于所有科室</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.tybz">停用标志</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.sydx">适用对象</div>
                            </td>
                            <td width="100px">
                                <div class="zui-table-cell">
                                    <i class="icon-bj" @click="edit($index)"></i>
                                    <i class="icon-sc icon-font" @click="removeList(item,$index)"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--一阶段-->
            <div class="fyxm-size" v-if="num==1">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th z-field="a01" z-fixed="left" z-style="text-align:center; width:50px"
                                style="width: 50px !important;">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th z-field="a1" z-width="60px" z-style="text-align:center;">
                                <div class="zui-table-cell">阶段编码</div>
                            </th>
                            <th z-field="a2" z-width="100px">
                                <div class="zui-table-cell">阶段名称</div>
                            </th>
                            <th z-field="a3" z-width="100px">
                                <div class="zui-table-cell">开始天数</div>
                            </th>
                            <th z-field="a4" z-fixed="right" z-width="100px">
                                <div class="zui-table-cell">开始单位</div>
                            </th>
                            <th z-field="a5" z-width="100px">
                                <div class="zui-table-cell">结束天数</div>
                            </th>
                            <th z-field="a6" z-width="100px">
                                <div class="zui-table-cell">结束单位</div>
                            </th>
                            <th z-field="a6" z-width="100px">
                                <div class="zui-table-cell">阶段说明</div>
                            </th>
                            <th z-field="a7" z-fixed="right" z-width="100px">
                                <div class="zui-table-cell">操作</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body body-height">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index">
                            <td width="50px">
                                <div class="zui-table-cell">
                                    <input-checkbox @result="reCheckBox" :list="'cjsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="Datanum==1?item.jdbm:item.ejjdbm">阶段编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="Datanum==1?item.jdmc:item.ejjdmc">阶段名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.jdBegin">开始天数</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.jdBegindw">开始单位</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.jdEnd">结束天数</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.jdEnddw">结束单位</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.jdsm">阶段说明</div>
                            </td>
                            <td width="100px">
                                <div class="zui-table-cell">
                                    <i class="icon-bj" @click="edit($index)"></i>
                                    <i class="icon-sc icon-font" @click="removeList(item)"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!--二阶段-->
            <div class="fyxm-size" v-if="num==2">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th z-field="a0" z-fixed="left" z-style="text-align:center; width:50px"
                                style="width: 50px !important;">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th z-field="a1" z-width="60px" z-style="text-align:center;">
                                <div class="zui-table-cell">编码</div>
                            </th>
                            <th z-field="a2" z-width="100px">
                                <div class="zui-table-cell">项目序号</div>
                            </th>
                            <th z-field="a3" z-width="100px">
                                <div class="zui-table-cell">项目内容</div>
                            </th>
                            <th z-field="a4" z-fixed="right" z-width="100px">
                                <div class="zui-table-cell">项目代码</div>
                            </th>
                            <th z-field="a5" z-width="100px">
                                <div class="zui-table-cell">项目大类</div>
                            </th>
                            <th z-field="a6" z-width="100px">
                                <div class="zui-table-cell">项目类型</div>
                            </th>
                            <th z-field="a7" z-width="100px">
                                <div class="zui-table-cell">执行大类</div>
                            </th>
                            <th z-field="a8" z-width="100px">
                                <div class="zui-table-cell">执行细类</div>
                            </th>
                            <th z-field="a9" z-width="100px">
                                <div class="zui-table-cell">执行方式</div>
                            </th>
                            <th z-field="a10" z-width="100px">
                                <div class="zui-table-cell">执行人</div>
                            </th>
                            <th z-field="a11" z-width="100px">
                                <div class="zui-table-cell">选择方式</div>
                            </th>
                            <th z-field="a12" z-fixed="right" z-width="100px">
                                <div class="zui-table-cell">操作</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body body-height">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" :tabindex="$index" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                            <td width="50px">
                                <div class="zui-table-cell">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xmbm">编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xsxh">项目序号</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xmmc">项目内容</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xmdm">项目代码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xmdl">项目大类</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xmlx">项目类型</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xmzxdl">执行大类</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xmzxxl">执行细类</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="ljbdzxfs_tran[item.xmzxfs]">执行方式</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.cjry">执行人</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="ljbdxzfs_tran[item.xmxzfs]">选择方式</div>
                            </td>
                            <td width="100px">
                                <div class="zui-table-cell">
                                    <i class="icon-bj" @click="edit(item)"></i>
                                    <i class="icon-sc icon-font" @click="removeList(item,$index)"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--详情-->
            <div class="fyxm-size" v-if="num==3">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th z-field="a0" z-fixed="left" z-style="text-align:center; width:50px"
                                style="width: 50px !important;">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th z-field="a1" z-width="60px" z-style="text-align:center;">
                                <div class="zui-table-cell">治疗方案</div>
                            </th>
                            <th z-field="a2" z-width="100px">
                                <div class="zui-table-cell" style="overflow: hidden;">医嘱编码</div>
                            </th>
                            <th z-field="a3" z-width="100px">
                                <div class="zui-table-cell">医嘱类型</div>
                            </th>
                            <th z-field="a4" z-fixed="right" z-width="100px">
                                <div class="zui-table-cell">医嘱种类</div>
                            </th>
                            <th z-field="a5" z-width="100px">
                                <div class="zui-table-cell">医嘱判断</div>
                            </th>
                            <th z-field="a6" z-width="100px">
                                <div class="zui-table-cell">明细编码</div>
                            </th>
                            <th z-field="a7" z-width="100px">
                                <div class="zui-table-cell">显示序号</div>
                            </th>
                            <th z-field="a8" z-width="100px">
                                <div class="zui-table-cell">规格</div>
                            </th>
                            <th z-field="a9" z-width="100px">
                                <div class="zui-table-cell">组号</div>
                            </th>
                            <th z-field="a10" z-width="100px">
                                <div class="zui-table-cell">单次剂量</div>
                            </th>
                            <th z-field="a12" z-width="100px">
                                <div class="zui-table-cell">频次</div>
                            </th>
                            <th z-field="a13" z-width="100px">
                                <div class="zui-table-cell">用药方法</div>
                            </th>
                            <th z-field="a14" z-width="100px">
                                <div class="zui-table-cell">数量</div>
                            </th>
                            <th z-field="a15" z-width="100px">
                                <div class="zui-table-cell">剂量单位</div>
                            </th>
                            <th z-field="a16" z-width="100px">
                                <div class="zui-table-cell">总量</div>
                            </th>
                            <th z-field="a17" z-width="100px">
                                <div class="zui-table-cell">输液速度</div>
                            </th>
                            <th z-field="a18" z-width="100px">
                                <div class="zui-table-cell">速度单位</div>
                            </th>
                            <th z-field="a19" z-width="100px">
                                <div class="zui-table-cell">医生说明</div>
                            </th>
                            <th z-field="a20" z-width="100px">
                                <div class="zui-table-cell">执行科室</div>
                            </th>
                            <th z-field="a21" z-width="100px">
                                <div class="zui-table-cell">必选项目</div>
                            </th>
                            <th z-field="a22" z-width="100px">
                                <div class="zui-table-cell">路径科室</div>
                            </th>
                            <th z-field="a23" z-width="100px">
                                <div class="zui-table-cell">医嘱是否自动生成</div>
                            </th>
                            <th z-field="a24" z-width="100px">
                                <div class="zui-table-cell">草药</div>
                            </th>
                            <th z-field="a25" z-width="100px">
                                <div class="zui-table-cell">组合</div>
                            </th>
                            <th z-field="a26" z-width="100px">
                                <div class="zui-table-cell">组合编码</div>
                            </th>
                            <th z-field="a27" z-width="100px">
                                <div class="zui-table-cell">申请单号</div>
                            </th>
                            <th z-field="a28" z-fixed="right" z-width="100px">
                                <div class="zui-table-cell">操作</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body body-height">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index">
                            <td width="50px">
                                <div class="zui-table-cell">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.zlfa">治疗方案</div>
                            </td>
                            <td class="position">
                                <div class="zui-table-cell title" v-text="item.yzbm">医嘱编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="yzlx_tran[item.yzlx]">医嘱类型</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="yzfl_tran[item.yzzl]">医嘱种类</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="ljbdyzpd_tran[item.yzpd]">医嘱判断</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.zlfa">明细编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xsxh">显示序号</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.xmgg">规格</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.yzfzh">组号</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.dcjl">单次剂量</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.pcbm">频次</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.yyff">用药方法</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.fjsl">数量</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.jldw">剂量单位</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.yyzl">总量</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.sysd">输液速度</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.sysddw">速度单位</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.yssm">医生说明</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.zxks">执行科室</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="ljbdxzfs_tran[item.xmxzfs]">必选项目</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.ljks">路径科室</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.ypyzsfzdsc">医嘱是否自动生成</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.sfcy">草药</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.zhyzbz">组合</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.zhyzbh">组合编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell" v-text="item.zlfa">申请单号</div>
                            </td>
                            <td width="100px">
                                <div class="zui-table-cell">
                                    <i class="icon-bj" @click="edit(item)"></i>
                                    <i class="icon-sc icon-font" @click="removeList(item,$index)"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="side-form  pop-width  pop-548" :class="{'ng-hide':index==1,'pop-805':num==3}" style="padding-top: 0;" id="brzcList001"
     role="form">
    <div class="fyxm-side-top"><span>{{title}}</span> <span class="fr closex ti-close" @click="closes"></span></div>
    <ul class="tab-edit-list tab-edit2-list" v-if="num==0">
        <li>
            <label>
                <i>编码</i>
                <input v-model="popContent.ljbm" type="text" readonly class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>名称</i>
                <input v-model="popContent.ljmc" @blur="setPYDM(popContent.ljmc, 'popContent', 'pydm')" type="text"
                       class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>拼音编码</i>
                <input v-model="popContent.pydm" readonly type="text" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>病例分型</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljblfx_tran" :index="popContent.blfx" :val="popContent.blfx"
                              :name="'popContent.blfx'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>适用病情</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljbzsybq_tran" :index="popContent.sybq" :val="popContent.sybq"
                              :name="'popContent.sybq'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>适用性别</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="lcljsyxb_tran" :index="popContent.syxb" :val="popContent.syxb"
                              :name="'popContent.syxb'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>分类</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljbzfl_tran" :index="popContent.fl" :val="popContent.fl"
                              :name="'popContent.fl'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>最小年龄</i>
                <input v-model="popContent.synlMin" type="text" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>最大年龄</i>
                <input v-model="popContent.synlMax" type="text" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>年龄单位</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="nldw_tran" :index="popContent.synlDw" :val="popContent.synlDw"
                              :name="'popContent.synlDw'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>标准天数</i>
                <input v-model="popContent.bzts" type="text" class="label-input"/>
            </label>
        </li>
        <li>
            <label style="justify-content: center;display: initial;">
                <i style="display: inherit;">适用于所有科室</i>
                <div class="switch">
                    <input type="checkbox" v-model="popContent.syks"/>
                    <label style="display: inherit;"></label>
                </div>
            </label>
            <label style="justify-content: center;display: -webkit-box;margin-top: 3px">
                <i style="display: inherit;margin-right: 61px;">停用标志</i>
                <div class="switch">
                    <input type="checkbox" v-model="popContent.tybz"/>
                    <label style="display: inherit;"></label>
                </div>
            </label>
        </li>
        <li>
            <label>
                <i>适用对象</i>
                <input v-model="popContent.sydx" type="text" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>启用时间</i>
                <input v-model="popContent.qysj" :value="popContent.qysj" type="text" class="label-input qysj"/>
            </label>
        </li>
    </ul>
    <ul class="tab-edit-list tab-edit2-list" v-if="num==1">
        <li>
            <label>
                <i>阶段编码</i>
                <input type="text" readonly v-if="Datanum==1" v-model="popContent.jdbm" class="label-input"/>
                <input type="text" readonly v-if="Datanum==2" v-model="popContent.ejjdbm" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>阶段名称</i>
                <input type="text" v-if="Datanum==1" v-model="popContent.jdmc" class="label-input"/>
                <input type="text" v-if="Datanum==2" v-model="popContent.ejjdmc" class="label-input"/>
            </label>
        </li>
        <li>
            <label class="position">
                <i>开始天数</i>
                <input type="text" v-model="popContent.jdBegin" class="label-input"/>
                <span class="cm" @click="qiehuan(0)" v-text="textDw[popContent.jdBegindw]"></span>
            </label>
        </li>
        <li>
            <label class="position">
                <i>结束天数</i>
                <input type="text" v-model="popContent.jdEnd" class="label-input"/>
                <span class="cm" @click="qiehuan(1)" v-text="textDw[popContent.jdEnddw]"></span>
            </label>
        </li>
        <li>
            <label>
                <i>阶段说明</i>
                <input type="text" v-model="popContent.jdsm" class="label-input"/>
            </label>
        </li>
        <li>
            <label style="justify-content: center;display: initial;">
                <i style="display: inherit;">是否审核</i>
                <div class="switch">
                    <input type="checkbox" v-model="popContent.shbz"/>
                    <label style="display: inherit;"></label>
                </div>
            </label>
        </li>
        <li>
            <label>
                <i>阶段序号</i>
                <input type="text" v-model="popContent.jdxh" class="label-input"/>
            </label>
        </li>
        <div style="clear: both;"></div>
        <div>
            <div style="width: 100%;border: 1px dashed  #1abc9c4f;margin-bottom: 17px"></div>
            <li>
                <label>
                    <i>操作员</i>
                    <input disabled v-model="popContent.cjry" type="text" class="label-input"/>
                </label>
            </li>
            <li>
                <label>
                    <i>创建日期</i>
                    <input disabled :value="popContent.cjrq|formDate" type="text" class="label-input cjrq"/>
                </label>
            </li>
            <li>
                <label>
                    <i>审核</i>
                    <input type="checkbox" v-model="popContent.shbz" class="green" name="a"><label for="list"
                                                                                                   style="margin-right: 8px;"></label>
                    <input disabled style="width: 137px;" v-model="popContent.shry" type="text" class="label-input"/>
                </label>
            </li>
            <li>
                <label>
                    <i>审核日期</i>
                    <input disabled v-model="popContent.shrq" type="text" class="label-input"/>
                </label>
            </li>
        </div>
    </ul>
    <ul class="tab-edit-list tab-edit2-list" v-if="num==2">
        <li>
            <label>
                <i>编码</i>
                <input  v-model="popContent.xmbm" readonly type="text" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>项目序号</i>
                <input type="text" v-model="popContent.xsxh" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>项目内容</i>
                <input type="text" v-model="popContent.xmmc"  class="label-input"/>
            </label>
        </li>
        <!--<li>-->
            <!--<label>-->
                <!--<i>项目代码</i>-->
                <!--<input type="text" v-model="popContent.pydm" class="label-input"/>-->
            <!--</label>-->
        <!--</li>-->
        <li>
            <label>
                <i>项目大类</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljbdxmdl_tran" :index="popContent.xmdl" :val="popContent.xmdl"
                              :name="'popContent.xmdl'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>项目类型</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljbdxmlx_tran" :index="popContent.xmlx" :val="popContent.xmlx"
                              :name="'popContent.xmlx'">
                </select-input>
            </label>
        </li>
        <li class="resultData">
            <label>
                <i>执行大类</i>
                <div class="zui-select-inline">
                <input type="text" class="zui-input" name="input1" check="required"  />
                <div class="zui-select-group" role="listbox">
                    <ul class="inner">
                            <li  @click="getbm(list.bm,'xmzxdl')" v-for="list in XmzxdljsonList" v-text="list.mc"></li>
                    </ul>
                    <p class="tjsj" @click="topNewPage('执行大类','page/lclj/xtwh/ljbdwhys/zxdl.html')"><span>添加执行大类</span></p>
                </div>
                </div>
            </label>
        </li>
        <li class="resultData">
            <label>
                <i>执行细类</i>
                <!--<select-input @change-data="resultChange" :not_empty="false"-->
                              <!--:child="XmzxdljsonList" :index="'mc'" :index_val="'bm'" :val="popContent.xmzxxl"-->
                              <!--:search="true" :name="'popContent.xmzxxl'" >-->
                <!--</select-input>-->
                <div class="zui-select-inline">
                    <input type="text" class="zui-input" name="input1" check="required"  />
                    <div class="zui-select-group" role="listbox">
                        <ul class="inner">
                            <li :data-value="list.bm" @clcik="getbm(list.bm,'xmzxxl')" v-for="list in XmzxxxjsonList" v-text="list.mc"></li>
                        </ul>
                        <p class="tjsj" @click="topNewPage('执行细类','page/lclj/xtwh/ljbdwhys/zxxl.html')">添加执行细类</p>
                    </div>
                </div>
            </label>
        </li>
        <li>
            <label class="position">
                <i>执行方式</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljbdzxfs_tran" :index="popContent.xmzxfs" :val="popContent.xmzxfs"
                              :name="'popContent.xmzxfs'">
                </select-input>
            </label>
        </li>
        <!--<li>-->
            <!--<label class="position">-->
                <!--<i>执行人</i>-->
                <!--<select-input @change-data="resultChange" :not_empty="false"-->
                              <!--:child="ljblfx_tran" :index="popContent.blfx" :val="popContent.blfx"-->
                              <!--:name="'popContent.blfx'">-->
                <!--</select-input>-->
            <!--</label>-->
        <!--</li>-->
        <li>
            <label>
                <i>选择方式</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljbdxzfs_tran" :index="popContent.xmxzfs" :val="popContent.xmxzfs"
                              :name="'popContent.xmxzfs'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>项目评估</i>
                <input type="text" v-model="popContent.xmpg" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>表单格式可选项</i>
                <input type="checkbox" id="list111" v-model="popContent.kxx" class="green" name="a"><label for="list111" style="margin-right: 8px;"></label>
            </label>
        </li>
        <div style="clear: both;"></div>
        <div>
            <div style="width: 100%;border: 1px dashed  #1abc9c4f;margin-bottom: 17px"></div>
            <li>
                <label>
                    <i>操作员</i>
                    <input disabled v-model="popContent.cjry" type="text" class="label-input"/>
                </label>
            </li>
            <li>
                <label>
                    <i>创建日期</i>
                    <input disabled :value="popContent.cjrq|formDate" type="text" class="label-input"/>
                </label>
            </li>
            <li>
                <label>
                    <i>审核</i>
                    <input type="checkbox" v-model="popContent.shbz" id="listsdd" class="green" disabled name="a"><label for="listsdd" style="margin-right: 8px;"></label>
                    <input disabled  style="width: 137px;" type="text" class="label-input"/>
                </label>
            </li>
            <li>
                <label>
                    <i>审核日期</i>
                    <input disabled :value="popContent.shrq|formDate" type="text" class="label-input"/>
                </label>
            </li>
        </div>
    </ul>
    <ul class="tab-edit-list " v-if="num==3">
        <li>
            <label>
                <i>治疗方案</i>
                <!--<input readonly v-model="popContent.zlfa" type="text" class="label-input"/>-->
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="zlFajsonList" :index="'famc'" :index_val="'fabm'" :val="popContent.zlfa"
                              :name="'popContent.zlfa'" :search="true" :disable="Isblfx">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>医嘱编码</i>
                <input type="text" v-model="popContent.yzbm" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>医嘱类型</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljblfx_tran" :index="popContent.yzlx" :val="popContent.yzlx"
                              :name="'popContent.yzlx'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>医嘱种类</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="yzfl_tran" :index="popContent.yzfl" :val="popContent.yzfl"
                              :name="'popContent.yzfl'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>医嘱判断</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljbdyzpd_tran" :index="popContent.yzpd" :val="popContent.yzpd"
                              :name="'popContent.yzpd'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>明细编码</i>
                <input type="text" v-model="popContent.mxbm" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>显示序号</i>
                <input type="text" v-model="popContent.xsxh" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>规格</i>
                <input type="text" v-model="popContent.xmgg" readonly class="label-input"/>
            </label>
        </li>
        <li>
            <label class="position">
                <i>组号</i>
                <input type="text" v-model="popContent.yzfzh" class="label-input"/>
            </label>
        </li>
        <li>
            <label class="position">
                <i>单次剂量</i>
                <input type="text" v-model="popContent.dcjl" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>频次</i>
                <input type="text" v-model="popContent.pcbm" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>用药方法</i>
                <input type="text" v-model="popContent.yyff" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>数量</i>
                <input type="text" v-model="popContent.fjsl" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>剂量单位</i>
                <input type="text" v-model="popContent.jldw" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>总量</i>
                <input type="text" v-model="popContent.yyzl" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>输液速度</i>
                <input  v-model="popContent.sysd" type="text" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>速度单位</i>
                <input type="text" v-model="popContent.sysddw" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>医生说明</i>
                <input type="text" v-model="popContent.yssm" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>执行科室</i>
                <input type="text" v-model="popContent.zxks" class="label-input"/>
            </label>
        </li>
        <li>
            <label>
                <i>必选项目</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="ljbdxzfs_tran" :index="popContent.xmxzfs" :val="popContent.xmxzfs"
                              :name="'popContent.xmxzfs'">
                </select-input>
            </label>
        </li>
        <li>
            <label>
                <i>路径科室</i>
                <input type="text" v-model="popContent.ljks" class="label-input"/>
            </label>
        </li>
        <li>
                <label style="justify-content: center;display: -webkit-box;margin-top: 3px">
                    <i style="display: inherit;margin-right: 61px;">医嘱是否自动生成</i>
                    <div class="switch">
                        <input type="checkbox" v-model="popContent.ypyzsfzdsc"/>
                        <label style="display: inherit;"></label>
                    </div>
                </label>

        </li>
        <li>
            <label>
                <i style="display: inherit;margin-right: 61px;">草药</i>
                <div class="switch">
                    <input type="checkbox" v-model="popContent.sfcy"/>
                    <label style="display: inherit;"></label>
                </div>
            </label>
        </li>
        <li>
            <label>
                <i style="display: inherit;margin-right: 61px;">组合</i>
                <div class="switch">
                    <input type="checkbox" v-model="popContent.zhyzbz"/>
                    <label style="display: inherit;"></label>
                </div>
            </label>
        </li>
        <li>
            <label>
                <i>组合编码</i>
                <input type="text" v-model="popContent.zhyzbh" class="label-input"/>
            </label>
        </li>
        <div style="clear: both;"></div>
        <div>
            <div style="width: 100%;border: 1px dashed  #1abc9c4f;margin-bottom: 17px"></div>
            <li>
                <label>
                    <i>操作员</i>
                    <input disabled v-model="popContent.cjry" type="text" class="label-input"/>
                </label>
            </li>
            <li>
                <label>
                    <i>创建日期</i>
                    <input disabled  :value="popContent.cjrq|formDate" type="text" class="label-input"/>
                </label>
            </li>
            <li>
                <label>
                    <i>审核</i>
                    <input type="checkbox" v-model="popContent.shbz" id="list" readonly class="green" name="a"><label for="list" style="margin-right: 8px;"></label>
                    <input disabled style="width: 137px;" v-model="popContent.shry" type="text" class="label-input"/>
                </label>
            </li>
            <li>
                <label>
                    <i>审核日期</i>
                    <input disabled type="text" :value="popContent.shrq|formDate" class="label-input"/>
                </label>
            </li>
        </div>
    </ul>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<div style="clear: both"></div>
<script src="lj.js" type="text/javascript"></script>
