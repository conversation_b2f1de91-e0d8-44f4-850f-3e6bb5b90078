let mtjs = new Vue({
    el: '#mtjs',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        zyh: '',
        nums: 0,//明细汇总笔数
        totalFee: 0,//明细金额汇总
        isShowYibaoPage: false,
        jsonList: [],
        totalContent: {},
        totalRows: null,
        ifClick: true,
        isChecked: [],
        param: {
            page: 1,
            rows: 50,
            sort: '',
            parm: '',
            order: 'asc'
        }
    },
    mounted: function () {
    },
    updated: function () {

    },
    methods: {
        /**
         * 加载医保页
         */
        loadYibaoPage: function () {
                        window.insuranceUtils.init();
            this.isShowYibaoPage = true;
            $('#loadYibaoPage').load('../../CommonPage/insurancePort/014cdyhyb/014cdyhyb.html');
        },
        getData: function () {


        },
        /**
         * 读卡
         */
        load: function () {
            let grxxJson = window.insuranceUtils.load();
            if (!grxxJson) return;
            cd_014.grxxJson = grxxJson;
        },
        /**
         * 引入
         */
        enter: function () {
            window.insuranceUtils.getData();
        },
        settlement: function () {
                        if (!left_tab1.zyh) {
                malert("请先选择需要上传的患者", "top", "defeadted");
                return false;
            }
            common.openloading();
            let isSuccess = false, data = null;
            let param = {ykc010: left_tab1.zyh};
            let url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=fyxx&method=getMtSettlement&parm=";
            $.fn.getJSONAjax(url, param, function (res) {
                if (res.a != '0') {
                    malert(res.c, "top", "defeadted");
                } else {
                    isSuccess = true;
                    data = JSON.parse(res.d);
                }
            });
            if (!isSuccess) return window.insuranceUtils.failCloseLoading();
            let code20_xml = window.insuranceUtils.toYinHaiRequestModel(data, window.insuranceUtils.models.code20, '20');
            let response = window.insuranceUtils.httpCall(code20_xml);
            if (!response) window.insuranceUtils.failCloseLoading();
            let settle_url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=settle&method=saveRecord&parm=";
            $.fn.getJSONAjax(settle_url, param, function (res) {
                if (res.a != '0') {
                    malert(res.c, "top", "defeadted");
                    window.insuranceUtils.cancel(response.astr_jylsh, response.astr_jyyzm);
                } else {
                    window.insuranceUtils.confirm(response.astr_jylsh, response.astr_jyyzm);
                }
            });
            common.closeLoading();
        },
        upload: function () {
            if (!left_tab1.zyh) {
                malert("请先选择需要上传的患者", "top", "defeadted");
                return false;
            }
            common.openloading();
            window.insuranceUtils.sign2();
            this.fullUpload(left_tab1.zyh, 1, 50);
            common.closeLoading();
        },
        fullUpload: function (zyh, page, size) {
            let param = {
                ykc010: zyh,
                page: page,
                rows: rows
            };
            let isSuccess = false;
            let pageModel = null;
            let url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=fyxx&method=getMtFeeDetail&parm=";
            $.fn.getJSONAjax(url, param, function (res) {
                if (res.a != '0') {
                    malert(res.c, "top", "defeadted");
                } else {
                    pageModel = JSON.parse(res.d);
                    if (pageModel.total == 0) {
                        malert('已上传完成或者暂无可上传的费用明细', "top", "defeadted");
                        isSuccess = false;
                    }
                    isSuccess = true;
                }
            });
            if (!isSuccess && pageModel == null) return;
            let code41_xml = window.insuranceUtils.toYinHaiRequestModel(pageModel.records, window.insuranceUtils.models.code41, '41');
            let response = window.insuranceUtils.httpCall(code41_xml);
            if (response) {
                let item = JSON.parse(response.astr_jysc_xml);
                let newItem = window.insuranceUtils.intersection(pageModel.records, item, 'yka015')
                let url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + left_tab1.bxurl + "&bxlbbm=" + left_tab1.bxlbbm + "&types=fyxx&method=saveUploadRecord&parm=";
                $.fn.getJSONAjax(url, newItem, function (res) {
                    if (res.a != '0') {
                        malert(res.c, "top", "defeadted");
                    }
                });
            }
            let newPage = pageModel.current + 1;
            if (newPage <= pageModel.pages) {
                this.fullUpload(zyh, newPage, size);
            }
        }

    }
});
