var zl_toolMenu = new Vue({
        el:'#jcsjxz .tong-top',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,scrollOps],
        data: {
            bxlbbm: null,
            bxurl: null,
            popContent:{
                yljgbm:jgbm,
            },
        },
        created: function(){
            this.getbxlb();
        },
        methods: {
            getbxlb: function () {
                var param = {bxjk: "005"};
                common.openloading("#zlbxxm");
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                        if (json.a == 0) {
                            if (json.d.list.length > 0) {
                                zl_toolMenu.bxlbbm = json.d.list[0].bxlbbm;
                                zl_toolMenu.bxurl = json.d.list[0].url;
                            }
                            common.closeLoading();
                        } else {
                            malert("保险类别查询失败!" + json.c,"top","defeated")
                            common.closeLoading();
                        }
                    });
            },

            bclbxz:function(){
                common.openloading("#jcsjxz");
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=commonBase&method=redeemTypeDownLoad&parm=" + JSON.stringify(zl_toolMenu.popContent), function (json) {
                        if (json.a == 0) {
                            malert("补偿类别下载成功！","top","success");
                            common.closeLoading();
                        } else {
                            malert(json.c,"top","defeated");
                            common.closeLoading();
                        }
                    });
            },
            zlfsxz:function(){
                common.openloading("#jcsjxz");
                $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=commonBase&method=treatmentModeUpdate&parm=" + JSON.stringify(zl_toolMenu.popContent), function (json) {
                    if (json.a == 0) {
                        malert("治疗方式下载成功！");
                        common.closeLoading();
                    } else {
                        malert(json.c);
                        common.closeLoading();
                    }
                });
            },

            jbbmxz:function(){
                common.openloading("#jcsjxz");
                // $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + zl_toolMenu.bxurl + "&bxlbbm=" + zl_toolMenu.bxlbbm + "&types=commonBase&method=treatmentModeUpdate&parm=" + JSON.stringify(zl_toolMenu.popContent), function (json) {
                //     if (json.a == 0) {
                //         malert("疾病编码下载成功！","top","success");
                //         common.closeLoading();
                //     } else {
                //         malert(json.c,"top","defeated");
                        common.closeLoading();
                //     }
                // });
            },
        }
    });
