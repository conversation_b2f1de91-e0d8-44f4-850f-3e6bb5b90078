<!DOCTYPE html>
<html lang="en">
<head>
    <link href="userPage/user.css" rel="stylesheet">
    <link rel="stylesheet" href="/newzui/pub/css/print.css" media="print"/>
</head>
<body>
<div class="panel">
    <div class="flex  printHide">
        <div class=" flex margin-b-10 margin-top-5 margin-l-20">
            <label class="whiteSpace margin-r-5 ft-14">清单类型</label>
            <select-input @change-data="resultChange_type" :not_empty="false"
                          :child="qdType_tran" :index="popContent.qdType" :val="popContent.qdType"
                          :name="'popContent.qdType'">
            </select-input>
        </div>
        <div class=" flex margin-l-20  margin-b-10 margin-top-5" style="width: 400px;">
            <label class="whiteSpace margin-r-5 ft-14">申请日期</label>
            <div class="zui-input-inline" style="flex:1;">
                <input type="text" name="phone" class="zui-input todate padd-l33" placeholder="请选择申请日期" id="timeVal">
            </div>
        </div>
    </div>
</div>
<div class="fyqd hzList zui-table-view flex-container flex-dir-c zui-table-view">
    <ul v-show="qdmx"  class="flex flex_9">
        <li class="cell-m">序号</li>
        <li class="list">记帐时间</li>
        <li class="list text-left">项目名称</li>
        <li class="list">规格</li>
        <li class="list">数量</li>
        <li class="list">单价</li>
        <li class="list">金额</li>
        <li class="list">医师</li>
        <li class="list">费用科目</li>
    </ul>
    <ul v-show="!qdmx" class="flex flex_10">
        <li class="cell-m">序号</li>
        <li class="">费用编码</li>
        <li class="text-left">费用名称</li>
        <li class="">规格</li>
        <li class="text-left">单位</li>
        <li class="">单价</li>
        <li class="">数量</li>
        <li class="">金额</li>
        <li class="">类型</li>
        <li class="">医保类别</li>
    </ul>
    <vue-scroll :ops="pageScrollOps">
    <ul class="item over-auto flex-one" v-show="qdmx">
        <li v-for="(item,$index) in jsonMxList" class="flex flex_Mx flex_9">
            <p class="list cell-m" v-text="$index+1"></p>
            <p class="list" v-text="fDate(item.djrq,'date')"></p>
            <p class="list text-left title" v-text="item.xmmc"></p>
            <p class="list" v-text="item.fygg"></p>
            <p class="list" v-text="item.fysl"></p>
            <p class="list" v-text="fDec(item.fydj,3)"></p>
            <p class="list" v-text="fDec(item.fyje,2)"></p>
            <p class="list" v-text="item.zyysxm"></p>
            <p class="list" v-text="item.fylbmc"></p>
        </li>
    </ul>
    <ul class="item over-auto flex-one" v-show="!qdmx">
        <li class="scroll"  v-for='(itemlist,index) in jsonHzList'>
            <div class="flex qdmx_title">
                <p class="list notBorder cell-m ysb-green margin-left whiteSpace">{{itemlist.fylbmc}}</p>
                <p class="list notBorder " :data-text="itemlist.fylbmc"></p>
                <p class="list notBorder" :data-text="itemlist.fylbmc"></p>
                <p class="list notBorder" :data-text="itemlist.fylbmc"></p>
                <p class="list notBorder" :data-text="itemlist.fylbmc"></p>
                <p class="list notBorder" :data-text="itemlist.fylbmc"></p>
                <p class="list notBorder" :data-text="itemlist.fylbmc"></p>
                <p class="list notBorder" :data-text="itemlist.fylbmc"></p>
                <p class="list notBorder" :data-text="itemlist.fylbmc"></p>
                <p class="list ysb-yellow">{{fDec(itemlist.fyze,2)}}元</p>
            </div>
        <div class="flex qdmx_item" v-for="(item,$index) in itemlist.fymx">
            <p class="list cell-m">{{$index+1}}</p>
            <p class="list ">{{item.xmbm}}</p>
            <div class="list text-left  position"><span class="title">{{item.xmmc}}</span></div>
            <p class="list ">{{item.fygg}}</p>
            <p class="list text-left">{{item.dw}}</p>
            <p class="list">{{fDec(item.fydj,3)}}</p>
            <p class="list">{{item.fysl}}</p>
            <p class="list">{{fDec(item.fyje,2)}}</p>
            <p class="list">{{item.fylbmc}}</p>
            <p class="list">{{nhtclb_tran[item.fw]}}</p>
        </div>
        </li>
    </ul>
    </vue-scroll>
        <!--<p v-if="jsonHzList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
    <div class="zui-table-tool hzgl-flex printHide">
        <button v-waves class="tong-btn btn-parmary  xmzb-db" @click="print()">打印</button>
    </div>
</div>
<script type="text/javascript" src="userPage/fyqd.js"></script>
</body>
</html>
