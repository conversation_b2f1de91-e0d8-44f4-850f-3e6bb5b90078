/********************************华丽分割线***************************************/
    //列表展示
var tableInfo = new Vue({
        el: '#panel',
        mixins: [tableBase, mformat, printer, mConfirm, baseFunc],
        data: {
            jsonList: [],
            csqxContent: {
                N05001200504:'1',
            },
            beginrq: '',
            endrq: '',
            czyBz:'1',
            czy_tran: {
                '0': "全院",
                '1': "本人",
            },
            jztfList: [],//获取选中的记账退费记录
            bxurl_014:"",
            bxlbbm_014:"",
            bkdj:false,
        },
        mounted: function () {
            //初始化检索日期！为今天0点到今天24点
            var myDate = new Date();
            this.beginrq = this.fDate(myDate.setDate(myDate.getDate()), 'date') + ' 00:00:00';
            this.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
            //入院登记查询列表 时间段选择器
            laydate.render({
                elem: '#timeVal',
                type: 'datetime',
                value: this.beginrq,
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    tableInfo.beginrq = value;
                }
            });
            laydate.render({
                elem: '#timeVal1',
                value: this.endrq,
                type: 'datetime',
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    tableInfo.endrq = value;
                    tableInfo.getData();
                }
            });
            // 页面加载初始化参数权限
            this.getData();
            this.getCsqx();
            this.getbxlb("014","bxurl_014","bxlbbm_014");
        },
        updated: function () {
            changeWin();
        },
        methods: {
            getbxlb: function (bxjk,bxurl,bxlbbm) {
                var param = {bxjk: bxjk};
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            tableInfo[bxlbbm] = json.d.list[0].bxlbbm;
                            tableInfo[bxurl] = json.d.list[0].url;
                        }
                    } else {
                        malert("保险类别查询失败!" + json.c,'right','defeadted')
                    }
                });
            },

            selectCzyBz:function(val){
                this.czyBz=val[0]
                this.getData();
            },
            //补打发票
            somePrint: function () {
                this.jztfList = []
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        // if (tableInfo.jsonList[i].sftf == "1" || tableInfo.jsonList[i].fysl == 0) {
                        //     malert("选中的第记录" + (i + 1) + "条已退费不能进行打印", 'top', 'defeadted');
                        //     return;
                        // }
                        if (tableInfo.csqxContent.cs00500100302 == '0') { //--不允许
                            if (tableInfo.jsonList[i].dycs > 0) {
                                malert("选中的第记录" + (i + 1) + "条不允许重复补打", 'top', 'defeadted');
                                return;
                            }
                        }
                        this.jztfList.push(JSON.parse(JSON.stringify(tableInfo.jsonList[i])));
                    }
                }
                if (this.jztfList.length == 0) {
                    malert("请选中您要打印的数据", 'top', 'defeadted');
                    return;
                }
                popWin.isShow = true;
                popWin.isShowpopL = true;
            },
			bdxp:function() {


                this.jztfList = []
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        this.jztfList.push(JSON.parse(JSON.stringify(tableInfo.jsonList[i])));
                    }
                }
                if (this.jztfList.length == 0) {
                    malert("请选中您要补打的数据", 'top', 'defeadted');
                    return;
                }
                let reportlets = "[{reportlet:'fpdy/mzsf/dzpjghsf.cpt',yljgbm:'" + jgbm + "',jsjlid:'" + tableInfo.jztfList[0].ryjsjlid + "'}]";
                if (!FrPrint(reportlets, 'GP-C80250 Series', null, null, null)) {
                    window.print();
                }
            }
			,
			
            //补开电子发票
            someEBill: function () {
                this.jztfList = []
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        this.jztfList.push(JSON.parse(JSON.stringify(tableInfo.jsonList[i])));
                    }
                }
                if (this.jztfList.length == 0) {
                    malert("请选中您要补开的数据", 'top', 'defeadted');
                    return;
                }
                let that = this;
                if(that.bkdj){
                    return;
                }
                that.bkdj = true;
                        // for (var t = 0; t < data.body.d.length; t++) {
                            var json2={
                                jsjlid:tableInfo.jztfList[0].ryjsjlid,
                                ipAddress:window.top.navli.yourIp,
                                userName:tableInfo.jztfList[0].czyxm,
                                yljgbm:jgbm
                            };
                            if(tableInfo.jztfList[0].bzsm=="挂号收费"){
                                tableInfo.$http.post(tableInfo.csqxContent.N05001200506+"/eb/invEBillRegistration", JSON.stringify(json2))
                                    .then(function (data) {
                                        that.bkdj = false;
                                        if(data.body.code == "S0000" && data.body.result.result=="S0000"){
                                            malert("收费补开成功,请补打发票");
                                        }else{
                                            malert("补开失败"+data.body.msg,"bottom","defeadted");
                                        }
                                    });
                            }else{
                                tableInfo.$http.post(tableInfo.csqxContent.N05001200506+"/eb/invoiceEBillOutpatient", JSON.stringify(json2))
                                    .then(function (data) {
                                        that.bkdj = false;
                                        if(data.body.code == "S0000" && data.body.result.result=="S0000"){
                                            malert("收费补开成功,请补打发票");
                                        }else{
                                            malert("补开失败"+data.body.msg,"bottom","defeadted");
                                        }
                                    });
                            }
                        // }

            },
            printEBill:function(){
                this.jztfList = []
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        this.jztfList.push(JSON.parse(JSON.stringify(tableInfo.jsonList[i])));
                    }
                }
                if (this.jztfList.length == 0) {
                    malert("请选中您要补开的数据", 'top', 'defeadted');
                    return;
                }
                var json2={
                    jsjlid:tableInfo.jztfList[0].ryjsjlid,
                    ipAddress:window.top.navli.yourIp,
                    userName:tableInfo.jztfList[0].czyxm,
                    yljgbm:jgbm
                };


                tableInfo.$http.post(tableInfo.csqxContent.N05001200506+"/eb/printEBillOutpatient", JSON.stringify(json2))
                    .then(function (data) {
                        if(data.body.code == "S0000"){
                            window.open(data.body.result.pictureUrl);


                        }else{
                            malert("获取电子发票失败"+data.body.msg,"bottom","defeadted");
                        }
                    });

            },
            someDbPrintNh:function(){
                var bxjshList =[];
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    var bxjsh =tableInfo.jsonList[i].inpid;

                    if (tableInfo.isChecked[i] == true) {
                        if (tableInfo.jsonList[i].sftf == "1" || tableInfo.jsonList[i].fysl == 0) {
                            malert("选中的第记录" + (i + 1) + "条已退费不能进行打印", 'top', 'defeadted');
                            return;
                        }

                        if (tableInfo.jsonList[i].rybxlbbm !='07'){//不是农合
                            malert("选中的第记录" + (i + 1) + "条已不是农合不能进行打印", 'top', 'defeadted');
                            return;
                        }

                        if (tableInfo.csqxContent.cs00500100302 == '0') { //--不允许
                            if (tableInfo.jsonList[i].dycs > 0) {
                                malert("选中的第记录" + (i + 1) + "条不允许重复补打", 'top', 'defeadted');
                                return;
                            }
                        }
                        if (bxjshList.indexOf(bxjsh)==-1) {
                            bxjshList.push(bxjsh)
                        }
                        // this.jztfList.push(JSON.parse(JSON.stringify(tableInfo.jsonList[i])));
                    }
                }
                for (var i=0;i<bxjshList.length;i++){
                    var jgbm = tableInfo.jsonList[0].yljgbm;
                    var outpid = bxjshList[i];
                    var bdbz=1;//1表示补打
                    var reportlets = "";
                    if (window.top.J_tabLeft.obj.frprintver == "3"){
                        reportlets = "[{reportlet: 'fpdy%2Fgznh%2Fmzbcd.cpt',yljgbm:'"+jgbm+"',bdbz:'"+bdbz+"',outpid:'"+outpid+"'}]";
                    }else{
                        reportlets = "[{reportlet: 'fpdy/gznh/mzbcd.cpt',yljgbm:'"+jgbm+"',outpid:'"+outpid+"'}]";
                    }
                    if (!FrPrint(reportlets,null)){
                        window.print();
                    }
                }
            },
            //农合补偿单，补打
            somePrintNh:function(){
                var bxjshList =[];
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                	var bxjsh =tableInfo.jsonList[i].bxjsh;
                	if(!bxjsh && tableInfo.jsonList[i].inpid){
                		bxjsh = tableInfo.jsonList[i].inpid;
                	}

                    if (tableInfo.isChecked[i] == true) {
                        if (tableInfo.jsonList[i].sftf == "1" || tableInfo.jsonList[i].fysl == 0) {
                            malert("选中的第记录" + (i + 1) + "条已退费不能进行打印", 'top', 'defeadted');
                            return;
                        }

                        if (tableInfo.jsonList[i].rybxlbbm !='07'){//不是农合
                            malert("选中的第记录" + (i + 1) + "条已不是农合不能进行打印", 'top', 'defeadted');
                            return;
                        }

                        if (tableInfo.csqxContent.cs00500100302 == '0') { //--不允许
                            if (tableInfo.jsonList[i].dycs > 0) {
                                malert("选中的第记录" + (i + 1) + "条不允许重复补打", 'top', 'defeadted');
                                return;
                            }
                        }
                        if (bxjshList.indexOf(bxjsh)==-1) {
                            bxjshList.push(bxjsh)
                        }
                        // this.jztfList.push(JSON.parse(JSON.stringify(tableInfo.jsonList[i])));
                    }
                }
                for (var i=0;i<bxjshList.length;i++){
                    var jgbm = tableInfo.jsonList[0].yljgbm;
                    var outpid = bxjshList[i];
                    var bdbz=1;//1表示补打
                    var reportlets = "";
                    if (window.top.J_tabLeft.obj.frprintver == "3"){
                        reportlets = "[{reportlet: 'fpdy%2Fgznh%2Fmzbcd.cpt',yljgbm:'"+jgbm+"',bdbz:'"+bdbz+"',outpid:'"+outpid+"'}]";
                    }else{
                        reportlets = "[{reportlet: 'fpdy/gznh/mzbcd.cpt',yljgbm:'"+jgbm+"',outpid:'"+outpid+"'}]";
                    }
                    if (!FrPrint(reportlets,null)){
                        window.print();
                    }
                }

            },

            print44_014:function(){
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        this.jztfList.push(JSON.parse(JSON.stringify(tableInfo.jsonList[i])));
                    }
                }
                if (!this.jztfList || this.jztfList.length <= 0) {
                    malert("请选中您要补开的数据", 'top', 'defeadted');
                    return;
                }
                var str_param = {
                    ghxh:this.jztfList[0].ryghxh,
                    yka103:this.jztfList[0].bxjsh,
                };
                common.openloading(".wrapper");
                $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + tableInfo.bxurl_014 + "&bxlbbm=" + tableInfo.bxlbbm_014 + "&types=mzjy&method=queryForMztf&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var res = JSON.parse(json.d);
                            var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
                            jykz = jykz + '<control>';
                            jykz = jykz + '<akc190>' + res.akc190 + '</akc190>'; //就诊编码
                            jykz = jykz + '<yka103>' + res.yka103 + '</yka103>'; //结算编号
                            jykz = jykz + '<aac001>' + res.aac001 + '</aac001>'; //个人编号
                            jykz = jykz + '<aka130>' + res.aka130 + '</aka130>'; //支付类别
                            jykz = jykz + '<yab003>' + res.yab003 + '</yab003>'; //经办机构
                            jykz = jykz + '<print>1</print>'; //是否显示打印窗口标志
                            jykz = jykz + '</control>';

                            var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><data><dir>C:\\Users\\<USER>\\Desktop\\</dir></data> ';

                            $.post("http://localhost:10014/init", {}, function (init) {
                                if (init.aint_appcode > 0) {
                                    $.post("http://localhost:10014/call", {
                                        jybh:"44",
                                        jykz_xml:jykz,
                                        jysr_xml:jysr,
                                    }, function (json_44) {
                                        common.closeLoading()
                                    });
                                } else {
                                    common.closeLoading()
                                }
                            });
                        } else {
                            common.closeLoading()
                            malert("门诊单据信息查询失败!",'right','defeadted');
                        }
                    });
            },

            print55_014:function(){
                this.jztfList=[];
                for (var i = 0; i < tableInfo.isChecked.length; i++) {
                    if (tableInfo.isChecked[i] == true) {
                        this.jztfList.push(JSON.parse(JSON.stringify(tableInfo.jsonList[i])));
                    }
                }
                if (!this.jztfList || this.jztfList.length <= 0) {
                    malert("请选中您要补开的数据", 'top', 'defeadted');
                    return;
                }
                var str_param = {
                    ghxh:this.jztfList[0].ryghxh,
                    yka103:this.jztfList[0].bxjsh,
                };
                common.openloading(".wrapper");
                $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + tableInfo.bxurl_014 + "&bxlbbm=" + tableInfo.bxlbbm_014 + "&types=mzjy&method=queryForMztf&parm="
                    + JSON.stringify(str_param),
                    function (json) {
                        if (json.a == 0) {
                            var res = JSON.parse(json.d);
                            var jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>';
                            jykz = jykz + '<control>';
                            jykz = jykz + '<akc190>' + res.akc190 + '</akc190>'; //就诊编码
                            jykz = jykz + '<yka103>' + res.yka103 + '</yka103>'; //结算编号
                            jykz = jykz + '<aac001>' + res.aac001 + '</aac001>'; //个人编号
                            jykz = jykz + '<aka130>' + res.aka130 + '</aka130>'; //支付类别
                            jykz = jykz + '<yab003>' + res.yab003 + '</yab003>'; //经办机构
                            jykz = jykz + '<print>1</print>'; //是否显示打印窗口标志
                            jykz = jykz + '</control>';

                            var jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><data></data> ';


                            $.post("http://localhost:10014/init", {}, function (init) {
                                if (init.aint_appcode > 0) {
                                    $.post("http://localhost:10014/call", {
                                        jybh:"55",
                                        jykz_xml:jykz,
                                        jysr_xml:jysr,
                                    }, function (json_55) {
                                        if (json_55.aint_appcode > 0) {
                                            malert(json_55.astr_appmasg,"right","success");
                                        } else {
                                            malert(json_55.astr_appmasg,"right","defeadted");
                                        }
                                        common.closeLoading()
                                    });
                                } else {
                                    common.closeLoading()
                                    malert(json_55.astr_appmasg,"right","defeadted");
                                }
                            });
                        } else {
                            common.closeLoading()
                            malert("门诊单据信息查询失败",'right','defeadted');
                        }
                    });
            },


            //获取参数权限
            getCsqx: function () {
                //先获取到科室编码
                var ksparm = {"ylbm": 'N050012005'};
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                    if (json.a == 0 && json.d && json.d.length > 0) {
                        ksbm = json.d[0].ksbm;
                        //获取参数权限
                        var parm = {"ylbm": 'N050012005', "ksbm": json.d[0].ksbm};
                        $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0 && json.d && json.d.length > 0) {
                                for (var i = 0; i < json.d.length; i++) {
                                    var csjson = json.d[i];
                                    switch (csjson.csqxbm) {

                                        case "N05001200501" :    //是否允许退非本人收取费用0-不允许退非本人收取费用，1-允许退非本人收取费用
                                            if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                tableInfo.csqxContent.cs00500100301 = csjson.csz;
                                            }
                                            break;
                                        case "N05001200502" :   //是否允许重复补打 0不 1允许
                                            if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                tableInfo.csqxContent.cs00500100302 = csjson.csz;
                                            }
                                            break;
                                        case "N05001200503" :   //是否允许占用新票号补打 0-不允许 1-允许 2-只允许发票号为空的
                                            if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                                tableInfo.csqxContent.N05001200503 = csjson.csz;
                                            }
                                            break;
                                        case "N05001200504": //0-帆软 1-网页
                                            if (csjson.csz) {
                                                tableInfo.csqxContent.N05001200504 = csjson.csz;
                                            }
                                            break;
                                        case "N05001200505": //发票打印格式 0：二版样式，1:一版样式
                                            if (csjson.csz) {
                                                tableInfo.csqxContent.N05001200505 = csjson.csz;
                                            }
                                            break;
                                        case "N05001200506": //博思电子发票门诊调用地址(补开)
                                            if (csjson.csz) {
                                                tableInfo.csqxContent.N05001200506 = csjson.csz;
                                            }
                                            break;
                                    }
                                }
                            } else {
                                malert("参数权限获取失败" + json.c, 'top', 'defeadted');
                            }
                        });
                    } else {
                        malert("权限科室获取失败" + json.c, 'top', 'defeadted');
                    }
                });
            },
            getData: function () {
                var parm = {
                    fphm: this.param.fphm,
                    brxm: this.param.brxm,
                    sfjs: '1',
                    beginrq: this.beginrq,
                    endrq: this.endrq,
                    sort: "sfsj",
                    order: "desc",
                    page: this.param.page,
                    rows: this.param.rows,
                    czybm: this.czyBz == '0' ? '' : userId,
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=query&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                        tableInfo.jsonList = tableInfo.filterSort(json.d.list, 'j', 'sfsj');
                    } else {
                        malert("查询失败：" + json.c, 'top', 'defeadted');
                    }
                });
            },
            //重写行选中
            checkSelectBd: function (val, event) {
                console.log(val);
                if(this.csqxContent.N05001200504=='1'){
                    this.isChecked=[]
                }
                if (val[1] == 'some') {
                    Vue.set(this.isChecked, val[0], !this.isChecked[val[0]]);
                    if (!val[2]) this.isCheckAll = false;
                    if (!this.isChecked[val[0]]) {
                        this.isCheckAll = false;
                        this.isChecked[val[0]] = false;
                        for (var i = 0; i < this.jsonList.length; i++) {
                            Vue.set(this.isChecked, i, false);
                        }
                    } else {//选中
                        this.ischeck(val[0]);
                    }
                    console.log(this.isChecked)
                }
                var that = this;
                this.ckChecked = val[0];
                event.currentTarget.onkeydown = function (e) {
                    if (e.keyCode == 40) {
                        that.isChecked = [];
                        that.ckChecked = that.ckChecked > that[val[2]].length ? 0 : that.ckChecked + 1;
                        that.$set(that.isChecked, that.ckChecked, true)
                    } else if (e.keyCode == 38) {
                        that.isChecked = [];
                        that.ckChecked = that.ckChecked < 0 ? that[val[2]].length : that.ckChecked - 1;
                        that.$set(that.isChecked, that.ckChecked, true)
                    }
                }
            },
            // 公用选中处理业务逻辑
            ischeck: function (index) {
                var err = "0";
                // if (this.jsonList[index].fysl == 0 || this.jsonList[index].sftf == '1') {
                //     malert("已退费不能进行打印", 'top', 'defeadted');
                //     Vue.set(this.isChecked, index, false);
                //     return false;
                // }
                // 需要将发票号相同的一起选中
                if (this.jsonList[index].zhfybh != null && this.jsonList[index].zhfybh != undefined) {
                    for (var i = 0; i < this.jsonList.length; i++) {
                        if (this.jsonList[index].fphm == this.jsonList[i].fphm && this.jsonList[index].ryghxh == this.jsonList[i].ryghxh && this.jsonList[index].ryjsjlid == this.jsonList[i].ryjsjlid && this.jsonList[i].sftf == '0') {
                            if (this.jsonList[index].sftf == "0" && this.jsonList[index].fysl != 0) {
                                Vue.set(this.isChecked, i, true);
                            } else {
                                // Vue.set(this.isChecked, i, false);
                                err = "1";
                            }
                        } else {
                            // Vue.set(this.isChecked, i, false);
                        }
                    }
                } else {
                    for (var i = 0; i < this.jsonList.length; i++) {
                        if (this.jsonList[index].fphm == this.jsonList[i].fphm && this.jsonList[index].ryghxh == this.jsonList[i].ryghxh && this.jsonList[index].ryjsjlid == this.jsonList[i].ryjsjlid && this.jsonList[i].sftf == '0') {
                            if (this.jsonList[index].sftf == "0" && this.jsonList[index].fysl != 0) {
                                Vue.set(this.isChecked, i, true);
                            } else {
                                // Vue.set(this.isChecked, i, false);
                                err = "1";
                            }
                        } else {
                            // Vue.set(this.isChecked, i, false);
                        }
                    }
                }
                if (err == '1') {
                    malert("已退费不能进行打印", 'top', 'defeadted');
                    return false;
                }
            }
        }
    });

var popWin = new Vue({
    el: '#pop',
    mixins: [dic_transform, printer, mformat],
    data: {
        isShow: false,
        isShowpopL: false,
        title: '确认对选中记录进行票据打印？',
        sfzyxh: 0,
        printList: [],
        isPrint: [],
        ArrInarr: [],
        printListData: [],
        reportlets: [],
        popContent: {bdfpnum: '1'},
        cardList: [{bdfp: '否', bdfpnum: 1}, {bdfp: '是', bdfpnum: 2}]
    },
    methods: {
        resultChangeItem: function (event) {
            this.popContent.bdfpnum = event[0]
        },
        printPageFun:function(){
            $('#printPage').load('../mzsf/printPage/lzPrint.html');
        },
        saveData: function () {
            //获取到退费的原因进行赋值操作
            var obj = {};
            var objArr = [];
            this.isPrint = [];
            this.reportlets = [];
            for (var i = 0; i < tableInfo.jztfList.length; i++) {
                if (!obj[tableInfo.jztfList[i].fphm]) {
                    this.isPrint.push(tableInfo.jztfList[i]);
                    obj[tableInfo.jztfList[i].fphm] = true;
                }
                tableInfo.jztfList[i].sfzyxh = popWin.sfzyxh;
            }
            var json = '{"list":' + JSON.stringify(tableInfo.jztfList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=MzsfSfjsPjbdjl&types=queryDyFpxx', json).then(function (data) {
                if (data.body.a == 0) {
                    popWin.printList = data.body.d.list;
                    console.log(this.printList);
                    popWin.print();
                    malert("打印成功！", 'top');
                    popWin.isShow = false;
                    popWin.isShowpopL = false;
                    tableInfo.jztfList = [];
                    tableInfo.getData();
                } else {
                    malert(data.body.c, 'top', 'defeadted');
                }
            }, function (error) {
                console.log(error);
            });

        },
        saveBdfp: function () {
            if(tableInfo.csqxContent.N05001200505 =='1'){
                this.saveData();
                return  false;
            }
            this.isPrint = [];
            this.reportlets = [];
            tableInfo.jztfList.reverse();
            tableInfo.jztfList[0].sfzyxh = 0;
            if (popWin.popContent.bdfpnum == '2'){
                //占用新号 验证参数数据
                if (tableInfo.csqxContent.N05001200503 && tableInfo.csqxContent.N05001200503 != '0'){
                    if (tableInfo.csqxContent.N05001200503 == '2') {
                        for (var i = 0; i < tableInfo.jztfList.length; i++) {
                            if (tableInfo.jztfList[i].fphm){
                                malert("参数不允许占用新发票号补打已有发票号！", 'top', 'defeadted');
                                return;
                            }
                        }
                    }
                    tableInfo.jztfList[0].sfzyxh = 1;
                } else {
                    malert("参数不允许占用新发票号补打！", 'top', 'defeadted');
                    return;
                }
            }
            if (tableInfo.csqxContent.N05001200504 == 1) {
                this.printPageFun();
                return false;
            }
            var postParm = {
                "list" : tableInfo.jztfList
            }
            // 'MzsfSfjsPjbdjl' ||  'New1MzsfSfjsPjbdjl'
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsPjbdjl&types=queryDyFpxx', JSON.stringify(postParm)).then(function (data) {
                    if (data.body.a == 0){
                        //帆软打印
                        //门诊收费打印机参数
                        var frpath = "";
                        if (window.top.J_tabLeft.obj.frprintver == "3") {
                            frpath = "%2F";
                        } else {
                            frpath = "/";
                        }
                        for (var t = 0; t < data.body.d.length; t++) {
                            popWin.reportlets.push({
                                reportlet: 'fpdy' + frpath + 'mzsf' + frpath + 'sffp.cpt',
                                yljgbm: '' + jgbm + '',
                                ghxh: '' + data.body.d[t]["ryghxh"] + '',
                                fphm: '' + data.body.d[t]["fphm"] + '',
                                jsjlid: '' + data.body.d[t]["ryjsjlid"] + '',
                                bdbz:1 //1表示补打
                            });
                        }

                        var MzsfPrint = null;
                        window.top.J_tabLeft.csqxparm.csbm = "N010024002";
                        $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                            if (json.a == 0) {
                                console.log(json.d);
                                if (json.d != null && json.d != undefined && json.d.length > 0) {
                                    MzsfPrint = json.d[0].csz;
                                }
                                if (!FrPrint(popWin.reportlets, MzsfPrint)) {
                                    popWin.print();
                                }
                            } else {
                                popWin.print();
                            }
                        });
                        popWin.isShow = false;
                        popWin.isShowpopL = false;
                        jztfList = [];
                        tableInfo.getData();
                    } else {
                        malert("打印失败:" + data.body.c, 'top', 'defeadted');
                    }
            }, function (error) {
                console.log(error);
            });
        },
        print: function () {
            // 查询打印模板
            var json = {repname: '补打发票'};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 清除打印区域
                popWin.clearArea(json.d[0]);
                // 为打印前生成数据
                popWin.printList[0]['brfyPrintList'][0]['fyhj'] = popWin.fDec(popWin.printList[0]['brfyPrintList'][0]['fyhj'], 2);
                popWin.printList[0]['brfyPrintList'][0]['brxb'] = popWin.brxb_tran[popWin.printList[0]['brfyPrintList'][0]['brxb']];
                popWin.printContent(popWin.printList[0]['brfyPrintList'][0]);
                popWin.printTrend(popWin.printList[0]['brfyPrintList']);
                // 开始打印
                var style = document.createElement('style');
                style.id="print";
                style.innerHTML = "@media print {@page {size: landscape;}}";
                window.document.head.appendChild(style);
                window.print();
                $('#print').remove()
            });
        }
    }
});


