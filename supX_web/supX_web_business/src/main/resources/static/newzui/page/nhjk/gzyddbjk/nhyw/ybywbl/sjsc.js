/**
 * Created by mash on 2017/9/29.
 */
//(function () {
var socket;
var rslmsg;
var ifok=false;
var rs=false;
var wsImpl = window.WebSocket || window.MozWebSocket;
var activeX = document.getElementById("csharpActiveX");
var left_tab1 = new Vue({
    el: '.left_tab1',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        billCode: null,
        bxlbbm: null,
        bxurl: null,
        which: 'hyjl',
        KsId:"",
        ksmc:"",
        IsZyh:"",
        ksList: [],                         // 科室的list
        ksbm: null,                         // 选中的科室
        jsonList: [],// 病人的list
        json:[],
        searchCon:[],
        selSearch:-1,
        text:"",
        page:{
            page:1,
            rows:20,
            total:null
        },
        them_tran:{},
        them:{
            '姓名': 'brxm',
            '性别': 'brxb',
            '年龄': 'nl',
            '住院号': 'zyh',
            '病人费别': 'brfb',
            '入院日期': 'ryrq'
        },
        //cshcg:false,//医保控件初始化
        //查询方式下拉列表
        qhyb_cxfs:'idcard',
        // 业务类型
        qhyb_ywlx:'12',
        qhyb_isjs :'0',
        // 青海医保参数List
        qhybCsArr:[],
        // 青海医保参数
        qhybCs:{},
        qhyb_cxfs_Arr:[{'key':'indi_id','value':'个人电脑号'},
                       {'key':'idcard','value':'公民身份号码'},
                       {'key':'iccardno','value':'IC卡号'},
                       {'key':'insr_code','value':'保险号'},
                       {'key':'name','value':'姓名'}],
        qhyb_ywlx_Arr:[{'key':'12','value':'普通住院'},
                       {'key':'42','value':'工伤住院'},
                       {'key':'52','value':'生育住院'}],
        qhyb_isjs_Arr:[
                       {'key':'0','value' : '未结算'},
                       {'key':'1' ,'value' : '已结算'}
                       ],
        rydjData : {},
        // websocket接收数据延时时间
        socketTime: 1000,
        ifClick4Query : true,
    },
    methods: {
    	uploadSbk: function(){
    		var message= [
      	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
      	              	['start','','BIZC200900'],
      	              	// 固定参数
      	              	['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
      	              	['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
      	              	['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],

      	              	['run',''],
      	              	// 基本信息
      	              	['setresultset','','icinfo'],

      	              	// 获取结果集数据结构
      	              	['getlist',

      	              	 	[

									['getbyname','','card_no',''],
									['getbyname','','center_id',''],
									['getbyname','','indi_id',''],
									['getbyname','','insr_code',''],
									['getbyname','','birthday',''],
									['getbyname','','name',''],
									['getbyname','','pers_type',''],
									['getbyname','','idcard',''],
									['getbyname','','sex',''],
									['getbyname','','indi_sta',''],
									['getbyname','','official_code',''],
									['getbyname','','total_salary',''],
									['getbyname','','corp_id',''],
									['getbyname','','corp_name',''],
									['getbyname','','corp_code',''],
									['getbyname','','corp_sta_code',''],
									['getbyname','','last_balance','']
      	              	 	 ]

      	              	]

      	              ];


          		console.log(message);
          		if (socket.readyState===1) {
                      socket.send(JSON.stringify(message));
                      var cs=0;
          			var interval=setInterval(function(){
          					cs+=1;
          					console.log(cs);
              				if(rs){
                  				if(ifok){
                  					console.log("读取社保卡信息！");
                  					console.log(rslmsg);
                  					console.log(rslmsg.getlist);
                  					clearInterval(interval);
                  					rs=false;
                  					ifok=false;
                  				}else{
                  					clearInterval(interval);
                  					rs=false;
                  					ifok=false;
                  				}
                  			}
              				if(cs>=10){
              					malert("医保超时,请重试！ ");
              					rs=false;
          	    				ifok=false;
              					clearInterval(interval);
              				}
              			},left_tab1.socketTime);
                  }else{
                  	malert("医保通信失败！ ");
                  }
    	},

    	getbxlb: function () {
        	var param = {bxjk:"004"};
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                	console.log(json)
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            left_tab1.bxlbbm = json.d.list[0].bxlbbm;
                            left_tab1.bxurl = json.d.list[0].url;
                            setTimeout(function () {
                            	// 获取医保参数信息
                                left_tab1.getCsData();
                                setTimeout(function () {
                                	// 初始化医保连接socket
                                	left_tab1.socketInit();
                                	setTimeout(function () {
                                		// 登录到医保
                                		left_tab1.login2Qhyb();
                                		/*setTimeout(function () {
                                    		// 设置日志
                                    		left_tab1.setLogs();
                                        }, 400);*/
                                    }, 200);
                                }, 500);
                            }, 100);
                        }
                    } else {
                        malert("保险类别查询失败!" + json.c)
                    }
                });
        },
    	// socket初始化
        socketInit : function(){
        	console.log("初始化");
        	var ip = left_tab1.qhybCs.cxservlet;
    		var host = "ws://"+ip+"/";
    		if(socket == null||socket==undefined){
    			socket = new wsImpl(host);
    		}else{

    			}
    		try{
    			socket.onerror = function(){
    				socket.close();
    				menu.qhyb_conn='onerror';
    				console.log("发生错误！");
    				};
    			socket.onopen = function () {
    				menu.qhyb_conn='onopen';
    				console.log("连接开启！");
    				};
    			socket.onclose = function () {
    				socket.close();
    				menu.qhyb_conn='onclose';
    				console.log("连接关闭！");
    				};
    			socket.onmessage = function (evt) {
    				rs=true;
    				console.log("消息传送正常！");
    				var msg=eval('(' + evt.data + ')');
    				rslmsg=msg;
    				if(rslmsg.code != "" || rslmsg.code != null){
    					ifok=true;
    				}else{
    					ifok=false;
    				}
    				};
    				}
    			catch (ex) {
    					console.log("异常！");
    					socket.close();
    					menu.qhyb_conn='onclose';
    				}
        },
        // 获取科室
        getKsData: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"009001001"}', function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        left_tab1.ksList = json.d;
                        left_tab1.ksbm = json.d[0].ksbm;
                        left_tab1.ksmc = json.d[0].ksmc;
                        setTimeout(function () {
                            left_tab1.getBrData(); //加载完成后自动获取就诊病人列表
                        }, 100);
                    }
                } else {
					malert("获取科室失败", 'top', 'defeadted');
                }
            });
        },
        // 选择科室
        ksChange: function (index) {
            left_tab1.getBrData();
            var obj = event.currentTarget;
            var selected = $(obj).find("option:selected");
            var ks = $(obj).val();
            var mc = selected.text();
            left_tab1.ksmc = selected.text();
        },
        // 获取病人的API
        getBrData: function () {
            var ksbm = $("#ksList").val();
            var ksmc = $('#ksList option:selected').text();
            if (ksbm == null || ksbm == undefined || ksbm == "") {
                malert("请选择就诊科室！");
                return;
            }
            var param = {
            		zyh : this.text,
            		ksbm : ksbm,
            		isjs:this.qhyb_isjs

            	};
				$.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url="+left_tab1.bxurl+"&bxlbbm="+left_tab1.bxlbbm+"&types=inhospital&method=queryAll&parm="+JSON.stringify(param), function (json) {
                	if (json.a == 0){
                		var res=eval('('+json.d+')');
                		left_tab1.jsonList = res.list;
                		console.log(left_tab1.jsonList)
                	}else{
                		malert(json.c);
                	}
                });
        },
        uploadRyxx : function(){
        	this.getBrData();
        },
        // 双击选中病人，获取病人信息
        edit: function (index) {
        		//转换数据格式
                for(var i=0;i<this.jsonList[index].length;i++){
                    if(this.jsonList[index].bxbr=='0'){
                        this.jsonList[index].bxbr==false;
                    }else{
                        this.jsonList[index].bxbr==true;
                    }
                }
                ryInfo = this.jsonList[index];
                ryInfo = Object.assign({}, ryInfo);
                left_tab1.rydjData = ryInfo;
                // 初始化人员信息
                if(left_tab1.qhyb_isjs == '0'){
                	toolMenu.ryxxInit();
                }else{
                	yjs.getData();
                }
                menu.selectedHzxx ="当前选中患者："+ ryInfo.BRXM + "(" + ryInfo.ZYH+ ")";
        },
        ssBrList : function(){
        	if (event.code == 'Enter' || event.code == 13 ||event.code== 'NumpadEnter') {
        		this.getBrData();
            }

        },

        searching: function (add) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var str_param = {
                parm: this.text,
                page: this.page.page,
                rows: this.page.rows,
            };

            $.getJSON("/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=zyhzxx&parm=" +
                JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        var date = null;
                        if (add) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                date = left_tab1.fDate(json.d.list[i]['ryrq'], "date");
                                json.d.list[i]['ryrq'] = date;
                                left_tab1.searchCon.push(json.d.list[i]);
                            }
                        } else {
                            for (var i = 0; i < json.d.list.length; i++) {
                                date = left_tab1.fDate(json.d.list[i]['ryrq'], "date");
                                json.d.list[i]['ryrq'] = date;
                                left_tab1.searchCon.push(json.d.list[i]);
                            }
                            left_tab1.searchCon = json.d.list;
                        }
                        left_tab1.page.total = json.d.total;
                        if (json.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
						malert(json.c, 'top', 'defeadted');
                    }
                })
        },
        selectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.searching(true);
            } else {
                $(".selectGroup").hide();
                this.text = item.zyh;
                left_tab1.getBrData(); //根据科室过滤病人
            }
        },
        //回车
        changeDown: function (event) {
            this.keyCodeFunction(event, 'json', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 ||event.code== 'NumpadEnter') {
                this.text = this.json.zyh;
                left_tab1.getBrData(); //根据科室过滤病人
            }
        },
        // 获取青海医保参数信息
        getCsData: function () {
        	var param = {
            		page:1,
            		rows:30,
            		sort:"yljgbm",
            		order:"asc"
            	};
        	$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url="+left_tab1.bxurl+"&bxlbbm="+left_tab1.bxlbbm+"&types=cssz&method=query&parm="+JSON.stringify(param), function (json) {
               console.log(json);
        		if(json.a == "0"){
                	var res=eval('('+json.d+')');
                	left_tab1.qhybCsArr = res.list;
                	left_tab1.qhybCs = JSON.parse(JSON.stringify(left_tab1.qhybCsArr[0]));
                	console.log("------------")
                	console.log(left_tab1.qhybCs);
                }
            });
        },
        // 登录到青海医保
        login2Qhyb : function(){
            menu.error = '';
        	console.log("登录到青海医保中心...")
        	var message= [
              	              	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
              	              	['start','','0'],
              	              	// 固定参数
              	                ['putcol','','oper_centerid',left_tab1.qhybCs.ybzxbh],
            	              	['putcol','','oper_hospitalid',left_tab1.qhybCs.tqcbh],
            	              	['putcol','','oper_staffid',left_tab1.qhybCs.yyjb],
              	              	// 入参
              	              	['putcol','','login_id',left_tab1.qhybCs.dlid],
              	              	['putcol','','login_password',left_tab1.qhybCs.dkma],
              	              	['run','']
              	                //['setdebug','','2','C:\\qhyblogs'],
              	              ];


        		console.log(message);
        		console.log("socket连接状态："+socket.readyState);
        		if (socket.readyState===1) {
                    socket.send(JSON.stringify(message));
                    var cs=0;
        			var interval=setInterval(function(){
        					cs+=1;
        					console.log(cs);
            				if(rs){
                				if(ifok){
                					console.log(rslmsg);
                					if(rslmsg.run >= 0 ){
                						menu.error = '';
                						menu.qhyb_login_type='success';
                					}else{
                						malert("登录到医保失败！ "+rslmsg.error);
                					}
                					clearInterval(interval);
                					rs=false;
                					ifok=false;
                				}else{
                					clearInterval(interval);
                					rs=false;
                					ifok=false;
                				}
                			}
            				menu.error = '医保连接中...'+cs;
            				if(cs>=10){
            					menu.error = "医保超时,请重试！ ";
            					malert("医保超时,请重试！ ");
            					rs=false;
        	    				ifok=false;
            					clearInterval(interval);
            				}
            			},left_tab1.socketTime);
                }else{
                	malert("医保通信失败！ ");
                }
        },
        //修改密码
        updatePassword:function(){
        	var message= [
             	['newinterfacewithinit',left_tab1.qhybCs.addr,left_tab1.qhybCs.port,left_tab1.qhybCs.servlet],
             		['start','','0'],
	              	// 固定参数
	                ['put','','1','oper_centerid',left_tab1.qhybCs.ybzxbh],
	                ['put','','1','oper_hospitalid',left_tab1.qhybCs.tqcbh],
	                ['put','','1','oper_staffid',left_tab1.qhybCs.yyjb],
	              	// 入参
	              	['put','','1','hospital_id',left_tab1.qhybCs.tqcbh],
	              	['put','','1','old_pwd','6323230032'],
	              	['put','','1','new_pwd','888888'],
	              	['put','','1','confirm_pwd','888888'],
	              	['put','','1','staff_name','宁秀乡'],
	              	['run',''],
	              	['destoryinterface','']
             	];

        	if (socket.readyState===1) {
                socket.send(JSON.stringify(message));
                var cs=0;
    			var interval=setInterval(function(){
    					cs+=1;
    					console.log(cs);
        				if(rs){
            				if(ifok){
            					console.log(rslmsg);
            					clearInterval(interval);
            					rs=false;
            					ifok=false;
            				}else{
            					clearInterval(interval);
            					rs=false;
            					ifok=false;
            				}
            			}
        				if(cs>=10){
        					rs=false;
    	    				ifok=false;
        					clearInterval(interval);
        				}
        			},left_tab1.socketTime);
            }else{
            	malert("医保通信失败！ ");
            }
        }
    },
    watch: {
        'qhyb_isjs': function () {
        	left_tab1.getBrData();
        	menu.yb_isjs = left_tab1.qhyb_isjs;
        	if(menu.yb_isjs == '1'){
        		menu.loadCon('yjs');
        		$("#yjsButton").attr("style","display:none;");
        		$("#ybcyButton").attr("style","display:none;");
        		$("#yjs input").val("");
        		left_tab1.rydjData = {};
        	}else{
        		menu.loadCon('ybdj');
        		$("#yjsButton").attr("style","display:'';");
        		$("#ybcyButton").attr("style","display:'';");
        		$("#yjs input").val("");
        		left_tab1.rydjData = {};
        	}
        }
    }
});
// 初始化科室信息
left_tab1.getKsData();
// 初始化保险类别信息
left_tab1.getbxlb();

var menu = new Vue({
    el: '.nh-menu',
    data: {
        which: '0',
        qhyb_conn:'onclose',
     // 青海医保登录状态 error success
        qhyb_login_type:'error',
        selectedHzxx:'',
        yb_isjs : '0',
        error:'',
    },
    methods: {
        loadCon: function (page) {
            var pageDiv = $("#"+page);
            $(".page_div").hide();
            if(pageDiv.length == 0){
                $("."+page).load(page+".html"+'?random='+Math.random()).fadeIn(300);
            } else {
                $("."+page).fadeIn(300);
            }
        }
    }
});
menu.loadCon('ybdj');
//})();
