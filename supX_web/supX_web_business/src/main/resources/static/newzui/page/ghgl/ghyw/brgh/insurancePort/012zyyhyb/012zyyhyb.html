<link href="/page/ybjk/gzwxhy/ybyw/sjsc/sjsc.css" rel="stylesheet" type="text/css">
<div class="gz_012">
    <div class="ksys-side">
        <ul class="tab-edit-list flex-start">
        <li>
            <i>个人编号</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aac001" disabled="disabled"/>
        </li>
        <li>
            <i>姓名</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aac003" disabled="disabled"/>
        </li>
        <li>
            <i>性别</i>
            <input type="text" class="zui-input  background-h" :value="brxb_tran[grxxJson.aac004]"  disabled="disabled"/>
        </li>
        <li>
            <i>年龄</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.yke112" disabled="disabled"/>
        </li>
        <li>
            <i>公务员级别</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.yac025" disabled="disabled"/>
        </li>
        <li>
            <i>身份证号码</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aac002" disabled="disabled"/>
        </li>
        <li>
            <i>出生日期</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aac006" disabled="disabled"/>
        </li>
        <li>
            <i>医疗人员类&emsp;&emsp;别</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.prm_akc021" disabled="disabled"/>
        </li>
        <li>
            <i>个人参保状&emsp;&emsp;态</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aac031" disabled="disabled"/>
        </li>
        <li>
            <i>参保所属分&ensp;中&ensp;心</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aab034" disabled="disabled"/>
        </li>
        <li>
            <i>执行社会保险办法</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.ykb065" disabled="disabled"/>
        </li>
        <li>
            <i>异地安置标&emsp;&emsp;志</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aae139" disabled="disabled"/>
        </li>
        <li>
            <i>个人账户余&emsp;&emsp;额</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aae240" disabled="disabled"/>
        </li>
        <li>
            <i>单位名称</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.aab003" disabled="disabled"/>
        </li>
        <li>
            <i>分中心编号</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.prm_yab003" disabled="disabled"/>
        </li>
        <li>
            <i>居民医疗人员类别</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.prm_ykc280" disabled="disabled"/>
        </li>
        <li>
            <i>居民医疗人员身份</i>
            <input type="text" class="zui-input  background-h" v-model="grxxJson.prm_ykc281" disabled="disabled"/>
        </li>
        <li>
            <i>住院状态</i>
            <input type="text" class="zui-input  background-h" v-model="brzt_tran[grxxJson.prm_ykc023]" disabled="disabled"/>
        </li>
        <li>
            <i>门诊诊断信&emsp;&emsp;息</i>
            <input class="zui-input" v-model="jbContent.jbmc" @input="searching(false,'jbmc')"
                   @keyDown="changeDown($event,'text')">
            <search-table :message="searchCon" :selected="selSearch"
                          :them="them" :them_tran="them_tran" :page="page"
                          @click-one="checkedOneOut" @click-two="selectOne" :not_empty="true">
            </search-table>
        </li>
        <li class="zflb">
            <i style="color:red;font-weight: 700">支付类别</i>
            <select-input @change-data="resultChange" id="prm_aka130"
                          :child="zyybzflb_tran" :index="zdxxJson.prm_aka130" :val="zdxxJson.prm_aka130"
                          :search="true" :name="'zdxxJson.prm_aka130'" :not_empty="true">
            </select-input>
        </li>
        <li>
            <i>备注</i>
            <input class="zui-input" type="text" v-model="zdxxJson.bzsm"/>
        </li>
        <li>
            <button class="tong-btn btn-parmary paddr-r5" @click="load()">读卡</button>
            <button class="tong-btn btn-parmary paddr-r5" @click="enter()">引入</button>
        </li>
    </ul>

    </div>

</div>
    <!--<div class="zui-row buttonbox">-->
        <!--<button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="load()">读卡</button>-->
        <!--<button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="enter()">引入</button>-->
    <!--</div>-->
<script type="application/javascript" src="insurancePort/012zyyhyb/012zyyhyb.js"></script>