
//医嘱功能菜单
var toolMenu = new Vue({
    el: '#toolMenu',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        tabList:[
            {text:'执行时间'},
            {text:'审核时间'},
            {text:'医生停嘱时间'},
            {text:'护士停嘱时间'},
            {text:'病区出院时间'},
            {text:'修改记录'},
            {text:'首次执行时间'},
            ],
        index: 0,
        zyhs: [],
        yzlx:'2',
        endrqq:'',
        ksrqq:'',
        xgsj1:'',
        xgsj2:'',
        xgsj3:'',
        xgsj4:'',
        xgsj5:'',
        which: 1,
        patientInfo: {},
        toolMenuList: [],//医嘱
        yzzxsjInfoList: [],//医嘱查询
        yzzxsjInfoList2: [],//医嘱查询
        yzshsjInfoList: [],//医嘱审核
        ystzsjInfoList: [],//医生停嘱时间
        hstzsjInfoList: [],//护士停嘱时间
        bqcysjInfoList: [],//取消审核
        xgjlInfoList:[],//修改记录
        zxListBak: [],
        jsonList: [],
        auditingData: [],
        ifClick: true,
        jkbz: true,
        ajaxUrl:{
            '0':'yzcx',
            '1':'xgyzshsj',
            '2':'ystzsj',
            '3':'hstzsj',
            '4':'bqcysj',
            '5':'queryXgjl',
            '6':'yzcx2',
        },
    },
    updated: function () {
        changeWin();
        this.dragTd()
    },
    mounted:function(){
        Mask.newMask(this.MaskOptions('dbegin'));
        Mask.newMask(this.MaskOptions('dEnd'));
        Mask.newMask(this.MaskOptions('xgsj1'));
        Mask.newMask(this.MaskOptions('xgsj2'));
        Mask.newMask(this.MaskOptions('xgsj3'));
        Mask.newMask(this.MaskOptions('xgsj4'));
        Mask.newMask(this.MaskOptions('xgsj5'));
    },
    methods: {
        setTime(event,code){
            this[code]=event.target.value
        },
        showDate: function (index, el,code) {
            this._laydate = laydate.render({
                elem: '#'+el
                , show: true //直接显示
                , type: 'datetime'
                , theme: '#1ab394',
                done: function (value, data) {
                    toolMenu[code]=value
                }
            })
        },
        checkSome: function (index) {
            console.log(111)
            if (!this.isChecked[index]) {
                this.isCheckAll = false;
                Vue.set(this.isChecked, index, false);
            } else {
                Vue.set(this.isChecked, index, true);
            }
            this.$forceUpdate()
        },
        //功能菜单选择
        clickYzcl: function (isClick) {
            this.index = isClick;
            this.queryyz();
        },
        /*统一执行医嘱查询*/
        queryyz: function () {
            //取住院号 查看查询
            this.zyhs = [];
            for (var i = 0; i < left.jsonList.length; i++) {
                if (left.isChecked[i]) {
                    var obj = {};
                    obj.zyh = left.jsonList[i].zyh;
                    this.zyhs.push(obj);

                    if (!left.jsonList[i].zyys) {
                        this.jkbz = false;
                    } else {
                        this.jkbz = true;
                    }
                }
            }
            var zyh = JSON.stringify(this.zyhs);
            //清空选择项
            this.isChecked = []
            //对象清空
            this.jsonList = [];
            console.info(111)
             var param= 'ksbm='+left.ksId+'&yzlx='+this.yzlx+'&zyh='+zyh+'&ksrqq='+(this.index == 3 || this.index == 6 ? '' : this.ksrqq)+'&endrqq='+(this.index == 3 || this.index == 6 ? '' :this.endrqq)+''
            if (this.zyhs.length == 0) {
                malert("请选择病人后再进行此操作！",'top','defeadted');
                return
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types='+this.ajaxUrl[this.index]+'&'+param, function (json) {
                    if (json.a == 0 && json.d && json.d.list.length > 0) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                json.d.list[i].yzxx[int].no = i;
                            }
                        }
                        toolMenu.jsonList = json.d.list;
                        console.log(toolMenu.jsonList)
                    }
                }, function (error) {
                    console.log(error);
                });

        },
        showPop: function () {			// 查看基本信息
            pop.isFold = true;
        },
        //单个医嘱全选
        checkYZAll: function (types, object, numb) {
            if (this.isCheckAll) {
                var obj = object[numb].yzxx;
                for (var i = 0; i < obj.length; i++) {
                    var check = types + "_" + numb + "_" + i;
                    this.isChecked[check] = true;
                }
            } else {
                this.isChecked = [];
            }
        },
        //所有医嘱全选
        checkHzyzAll: function (object) {
            if (this.isCheckAll) {
                for (var j = 0; j < object.length; j++) {
                    for (var i = 0; i < object[j].yzxx.length; i++) {
                        var check = j + "_" + i;
                        this.isChecked[check] = true;
                    }
                }

            } else {
                this.isChecked = [];
            }
        },

        /*时间修改请求*/
        auditing: function () {
            console.log(111)
            if (!toolMenu.ifClick) return //如果为false表示已经点击了不能再点
            toolMenu.ifClick = false;
            if (toolMenu.index == 0) {//医嘱审核
                this.auditingData = [];
                if (left.caqxContent.cs00900100101 == '1') {
                    malert("对不起，您无权修改！",'top','defeadted');
                    return
                }
                for (var i = 0; i < this.jsonList.length; i++) {
                    for (var j = 0; j < this.jsonList[i]['yzxx'].length; j++) {
                        var check = "1_" + i + "_" + j;
                        if (this.isChecked[check]) {
                            var shrq = toolMenu.fDate(this.jsonList[i]['yzxx'][j].shsj,'datetime');
                            var xgsj = this.xgsj1;
                            if (xgsj < shrq){
                                var jj = j + 1;
                                malert("对不起，第["+jj+"]行["+this.jsonList[i]['yzxx'][j].xmmc+"]修改执行时间["+xgsj+"]不能早于医嘱审核时间["+shrq+"]！",'top','defeadted');
                                toolMenu.ifClick = true;
                                return
                            }
                            this.auditingData.push({"xhid": this.jsonList[i]['yzxx'][j].xhid,"sxrq":this.xgsj1});
                            //console.log(this.yzshInfoList[i]['yzxx'][j].xhid);
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱修改！",'top','defeadted');
                    toolMenu.ifClick = true;
                    return;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=HszHlywYzclZx&types=xgzxsj&ksbm=' + left.ksId,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("修改成功",'top','success')
                        toolMenu.ifClick = true;
                        toolMenu.queryyz();//刷新
                    } else {
                        malert("修改失败",'top','defeadted');
                        toolMenu.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
            }
            else if (toolMenu.index == 1) {//修改审核时间
                this.auditingData = [];
                if (left.caqxContent.cs00900100102 == '1') {
                    malert("对不起，您无权修改间！",'top','defeadted');
                    return
                }
                for (var i = 0; i < this.jsonList.length; i++) {
                    for (var j = 0; j < this.jsonList[i]['yzxx'].length; j++) {
                        var check = "2_" + i + "_" + j;
                        if (this.isChecked[check]) {
                            var ksrq = toolMenu.fDate(this.jsonList[i]['yzxx'][j].ksrq,'datetime');
                            var xgsj =  this.xgsj2;
                            if (xgsj < ksrq){
                                var jj = j + 1;
                                malert("对不起，第["+jj+"]行["+this.jsonList[i]['yzxx'][j].xmmc+"]修改审核时间["+xgsj+"]不能早于医嘱开始时间["+ksrq+"]！",'top','defeadted');
                                toolMenu.ifClick = true;
                                return
                            }
                            this.auditingData.push({"xhid": this.jsonList[i]['yzxx'][j].xhid,"sxrq":this.xgsj2});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱修改！",'top','defeadted');
                    toolMenu.ifClick = true;
                    return;
                }
                //console.log("yzsh:"+JSON.stringify({list:this.auditingData}));
                this.$http.post('/actionDispatcher.do?reqUrl=HszHlywYzclZx&types=xgshsj&ksbm=' + left.ksId,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("修改成功",'top','success');
                        toolMenu.ifClick = true;
                        toolMenu.queryyz();//刷新
                    } else {
                        malert("修改失败",'top','defeadted');
                        toolMenu.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
            }
            else if (toolMenu.index == 4) {//病区出院
                this.auditingData = [];
                if (left.caqxContent.cs00900100103 == '1') {
                    malert("对不起，您无权修改！",'top','defeadted');
                    return
                }
                for (var i = 0; i < this.jsonList.length; i++) {
                    for (var j = 0; j < this.jsonList[i]['yzxx'].length; j++) {
                        var check = "3_" + i + "_" + j;
                        if (this.isChecked[check]) {
                            this.auditingData.push({"zyh": this.jsonList[i]['yzxx'][j].zyh,"sxrq":this.xgsj3});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱修改！",'top','defeadted');
                    toolMenu.ifClick = true;
                    return;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=HszHlywYzclZx&types=xgbqcysj&ksbm=' + left.ksId,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("修改成功",'top','success');
                        toolMenu.ifClick = true;
                        toolMenu.queryyz();//刷新
                    } else {
                        malert("修改失败",'top','defeadted');
                        toolMenu.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
            }
            else if (toolMenu.index == 2) {//医生停嘱时间
                this.auditingData = [];
                if (left.caqxContent.cs00900100104 == '1') {
                    malert("对不起，您无权修改！",'top','defeadted');
                    return
                }
                for (var i = 0; i < this.jsonList.length; i++) {
                    for (var j = 0; j < this.jsonList[i]['yzxx'].length; j++) {
                        var check = "4_" + i + "_" + j;
                        if (this.isChecked[check]) {
                            var zxsj = toolMenu.fDate(this.jsonList[i]['yzxx'][j].zxsj,'datetime');
                            var xgsj = this.xgsj4;//$("#xgsj4").val();
                            if (xgsj < zxsj){
                                var jj = j + 1;
                                malert("对不起，第["+jj+"]行["+this.jsonList[i]['yzxx'][j].xmmc+"]修改停嘱时间["+xgsj+"]不能早于医嘱执行时间["+zxsj+"]！",'top','defeadted');
                                toolMenu.ifClick = true;
                                return
                            }
                            this.auditingData.push({"xhid": this.jsonList[i]['yzxx'][j].xhid,"sxrq":this.xgsj4});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱修改！",'top','defeadted');
                    toolMenu.ifClick = true;
                    return;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=HszHlywYzclZx&types=xgystzsj&ksbm=' + left.ksId,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("修改成功",'top','success');
                        toolMenu.ifClick = true;
                        toolMenu.queryyz();//刷新
                    } else {
                        malert("修改失败",'top','defeadted');
                        toolMenu.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
            }
            else if (toolMenu.index == 3) {//护士停嘱时间
                this.auditingData = [];
                if (left.caqxContent.cs00900100105 == '1') {
                    malert("对不起，您无权修改！",'top','defeadted');
                    return
                }
                for (var i = 0; i < this.jsonList.length; i++) {
                    for (var j = 0; j < this.jsonList[i]['yzxx'].length; j++) {
                        var check = "5_" + i + "_" + j;
                        if (this.isChecked[check]) {
                            var ystzsj = toolMenu.fDate(this.jsonList[i]['yzxx'][j].ystzsj,'datetime');
                            var xgsj =this.xgsj5;// $("#xgsj5").val();
                            if (xgsj < ystzsj){
                                var jj = j + 1;
                                malert("对不起，第["+jj+"]行["+this.jsonList[i]['yzxx'][j].xmmc+"]修改护士停嘱时间["+xgsj+"]不能早于医生停嘱时间["+ystzsj+"]！",'top','defeadted');
                                toolMenu.ifClick = true;
                                return
                            }
                            this.auditingData.push({"xhid": this.jsonList[i]['yzxx'][j].xhid,"sxrq":this.xgsj5});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱修改！",'top','defeadted');
                    toolMenu.ifClick = true;
                    return;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=HszHlywYzclZx&types=xghstzsj&ksbm=' + left.ksId,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("修改成功",'top','success');
                        toolMenu.ifClick = true;
                        toolMenu.queryyz();//刷新
                    } else {
                        malert("修改失败",'top','defeadted');
                        toolMenu.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
            }
            else if(toolMenu.index == 5){
                this.auditingData = [];
                for (var i = 0; i < this.jsonList.length; i++) {
                    for (var j = 0; j < this.jsonList[i]['yzxx'].length; j++) {
                        var check = "7_" + i + "_" + j;
                        if (this.isChecked[check]) {
                            this.auditingData.push({"xhid": this.jsonList[i]['yzxx'][j].xhid,"sxrq":$("#xgsj6").val()});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱修改！",'top','defeadted');
                    toolMenu.ifClick = true;
                    return;
                }
                this.$http.post('/actionDispatcher.do?reqUrl=HszHlywYzclZx&types=xgzxsj2&ksbm=' + left.ksId,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("修改成功",'top','success')
                        toolMenu.ifClick = true;
                        toolMenu.queryyz();//刷新
                    } else {
                        malert("修改失败",'top','defeadted');
                        toolMenu.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });
            }
        },
    }
});


// 右边菜单
var pop = new Vue({
    el: '#UserHzxx',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        baseInfo: {},
        isFold: false
    },
    methods: {
        close: function () {
            this.isFold = false;
        }
    }
});
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is("#UserHzxx");
    if (!bol) {
        if (pop.isFold) pop.close();
    }
});

$(".toolMenu div").click(function () {
    $(".toolMenu div").removeClass("toolMenuDiv_selected");
    $(this).addClass("toolMenuDiv_selected");
});
