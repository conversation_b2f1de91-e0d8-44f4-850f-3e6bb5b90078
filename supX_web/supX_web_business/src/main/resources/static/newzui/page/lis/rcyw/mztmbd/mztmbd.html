<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>门诊条码补打</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default">
<div class="wrapper">
    <div class="panel box-fixed">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-gl paddr-r5" @click="guolu">过滤</button>
            <button class="tong-btn btn-parmary-b icon-yl paddr-r5" >预览</button>
            <button class="tong-btn btn-parmary-b icon-dysq paddr-r5" @click="Btnprint">打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">申请日期</label>
                    <div class="zui-input-inline zui-select-inline zui-date">
                        <i class="datenox icon-rl"></i>
                        <input type="text" name="phone" class="zui-input todate padd-l33" v-model="param.time" placeholder="请选择申请日期">
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label padd-l14">检索码</label>
                    <div class="zui-input-inline">
                        <input type="text" class="zui-input" name="input1" placeholder="请输入检索码" v-model="param.bah"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <button class="zui-btn btn-primary  xmzb-db" @click="sx">查询</button>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable" id="utable1 zui-table-body" z-height="full" style="border:none;margin-top: 108px;padding: 0 10px; background: #fff">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th z-fixed="left" z-style="text-align:center; width:50px" z-width="50px">
                        <input-checkbox @result="reCheckBox" :list="'jydjList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th z-field="username"   z-width="80px" z-style="text-align:center;">
                        <div class="zui-table-cell">序号</div>
                    </th>
                    <th z-field="sex" z-width="80px">
                        <div class="zui-table-cell">类型</div>
                    </th>
                    <th z-field="sexs" z-width="80px">
                        <div class="zui-table-cell">来源</div>
                    </th>
                    <th z-field="city" z-width="80px">
                        <div class="zui-table-cell">类别</div>
                    </th>
                    <th z-field="sign" z-width="60px">
                        <div class="zui-table-cell">样本号</div>
                    </th>
                    <th z-field="experience" z-width="60px">
                        <div class="zui-table-cell">性别</div>
                    </th>
                    <th z-field="score" z-width="60px">
                        <div class="zui-table-cell">年龄</div>
                    </th>
                    <th z-field="classify" z-width="100px">
                        <div class="zui-table-cell">住院号/门诊号</div>
                    </th>
                    <th z-field="classifyr" z-width="100px">
                        <div class="zui-table-cell">检验项目</div>
                    </th>
                    <th z-field="classifys" z-width="100px">
                        <div class="zui-table-cell">扣费名称</div>
                    </th>
                    <th z-field="wealth" z-width="100px">
                        <div class="zui-table-cell">扣费金额</div>
                    </th>
                    <th z-field="wealthq" z-width="100px">
                        <div class="zui-table-cell">送检科室</div>
                    </th>
                    <th z-field="wealthqw" z-width="100px">
                        <div class="zui-table-cell">送检医师</div>
                    </th>
                    <th z-field="wealtheq" z-width="100px">
                        <div class="zui-table-cell">执行设备</div>
                    </th>
                    <th z-field="wealthqr" z-width="100px">
                        <div class="zui-table-cell">申请日期</div>
                    </th>
                    <th z-field="wealthqs" z-width="100px">
                        <div class="zui-table-cell">临床诊断</div>
                    </th>
                    <th z-width="100px">
                        <div class="zui-table-cell">检验序号</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table">
            <table class="zui-table" >
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jydjList" @click="checkSelect([$index,'some','jydjList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td width="50px">
                        <input-checkbox @result="reCheckBox" :list="'jydjList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td width="80px"><div class="zui-table-cell" v-text="$index+1"></div></td>
                    <td width="80px"><div class="zui-table-cell" v-text="jydjlx_tran[item.lx]"></div></td>
                    <td width="80px"><div class="zui-table-cell" v-text="jydjyblx_tran[item.yblx]"></div></td>
                    <td width="80px"><div class="zui-table-cell" v-text="jydjly_tran[item.ly]"></div></td>
                    <td width="60px"><div class="zui-table-cell" v-text="brxb_tran[item.xb]"></div></td>
                    <td width="60px"><div class="zui-table-cell" v-text="brxb_tran[item.xb]"></div></td>
                    <td width="60px"><div class="zui-table-cell" v-text="item.nl"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.bah"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.jyxmmc"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.fymc"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.fyje"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.ksmc"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.sqysxm"></div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.zxsbmc"></div></td>
                    <td width="100px"><div class="zui-table-cell">{{item.sqrq|formDate}}</div></td>
                    <td width="100px"><div class="zui-table-cell" v-text="item.lczd"></div>
                    <td width="100px"><div class="zui-table-cell" v-text="item.jyxh"></div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>


</div>
<div id="isTabel">
    <div class="pophide" :class="{'show':isShow}"></div>
    <div class="zui-form podrag  bcsz-layer zui-800 " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
        <div class="layui-layer-title ">过滤查询</div>
        <div class="guolv-xinzeng">
            <span class="layui-txt" @click="append()">新增一项</span>
            <i class="color-btn" @click="isShow=false"
               style="margin-top:-17px;width: 16px;height: 16px;display: inline-block;margin-left: 10px;float: right">×</i>
        </div>
        <div class="layui-layer-content">
            <div class=" layui-mad">
                <ul class="guolv-header guolv-style">
                    <li class="line">项目</li>
                    <li class="line">条件</li>
                    <li class="line">结果</li>
                    <li class="line">连接条件</li>
                    <li class="line">操作</li>
                </ul>
                <ui class="guolv-content" id="guo_append">
                    <div class="guolv-style guolv-bottom" v-for="(item, $index) in cxtjList">
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'xm_' + $index"
                                              @change-data="resultChange_item" :not_empty="true"
                                              :child="xmybgl_tran" :index="'item.xm'" :val="item.xm"
                                              :name="$index + '.xm.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'tj_' + $index"
                                              @change-data="resultChangeTj_item" :not_empty="true"
                                              :child="tjybgl_tran" :index="'item.tj'" :val="item.tj"
                                              :name="$index + '.tj.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                        </li>
                        <li class="line">
                            <!-- 类型 -->
                            <div class="zui-select-inline" v-if="isLxNum.indexOf(''+$index)>=0">
                                <select-input :id="'LX_' + $index"
                                              @change-data="resultChangeTj_item" :not_empty="true"
                                              :child="jydjlx_tran" :index="'item.jg'" :val="item.jg"
                                              :name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                            <!-- 输入框通用 -->
                            <div class="zui-select-inline" v-if="isTyNum.indexOf(''+$index)>=0">
                                <input type="text" class="zui-input" v-model="item.jg"/>
                            </div>
                            <!-- 性别 -->
                            <div class="zui-select-inline" v-if="isXbNum.indexOf(''+$index)>=0">
                                <select-input :id="'XB_' + $index"
                                              @change-data="resultChangeTj_item" :not_empty="true"
                                              :child="xtwhxb_tran" :index="'item.jg'" :val="item.jg"
                                              :name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                            <!-- 科室 -->
                            <div class="zui-select-inline" v-if="isKsNum.indexOf(''+$index)>=0">
                                <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                              :child="util.sjks" :index="'ksmc'" :index_val="'ksbm'"
                                              :val="item.jg" :name="'PfxxJson.'+$index+'.ksbm'" :index_mc="'ksmc'" :search="true">
                                </select-input>
                            </div>
                            <!-- 医生 -->
                            <div class="zui-select-inline" v-if="isYsNum.indexOf(''+$index)>=0">
                                <select-input @change-data="Wf_YsChange" :not_empty="false"
                                              :child="util.sjys" :index="'ysmc'" :index_val="'ysbm'"
                                              :val="item.jg" :name="'PfxxJsonx.'+$index+'.ysbm'" :index_mc="'ysmc'" :search="true">
                                </select-input>
                            </div>
                            <!-- 样本类型 -->
                            <div class="zui-select-inline" v-if="isYblxNum.indexOf(''+$index)>=0">
                                <select-input :id="'XB_' + $index"
                                              @change-data="resultChangeTj_item" :not_empty="true"
                                              :child="jydjyblx_tran" :index="'item.jg'" :val="item.jg"
                                              :name="$index + '.jg.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>

                        </li>
                        <li class="line">
                            <div class="zui-select-inline">
                                <select-input :id="'ljtj_' + $index"
                                              @change-data="resultChangeLjtj_item" :not_empty="true"
                                              :child="ljtjybgl_tran" :index="'item.ljtj'" :val="item.ljtj"
                                              :name="$index + '.ljtj.' + 1" :search="true" @keydown="nextFocus($event)"
                                              data-notEmpty="false">
                                </select-input>
                            </div>
                        </li>
                        <li class="line">
                            <span class="icon-sc" @click="sc($index)"></span>
                        </li>
                    </div>

                </ui>
            </div>
        </div>
        <div class="zui-row buttonbox">
            <button class="zui-btn table_db_esc btn-default" @click="isShow=false">取消</button>
            <button class="zui-btn btn-primary table_db_save" @click="save()">保存</button>
        </div>
    </div>
</div>


<script src="mztmbd.js"></script>
<script type="text/javascript">
    $(".zui-table-view").uitable();
</script>
</body>
</html>