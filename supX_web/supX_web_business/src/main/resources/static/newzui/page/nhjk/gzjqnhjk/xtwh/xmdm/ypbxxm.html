<div id="ypbxxm" class="zlbxxm-height">
    <div class="tong-top">
        <button class="tong-btn btn-parmary" @click="getData"><i class="icon-sx1 padd-r-5"></i>刷新</button>
        <button class="tong-btn btn-parmary-b" @click="remove"><i class="icon-sc-header padd-r-5"></i>删除</button>
        <button class="tong-btn btn-parmary-b" @click="loadXm">获取药品农合项目</button>
        <button class="tong-btn btn-parmary-b" @click="autoDm">自动对码（项目名称）</button>
        <div class="zui-form flex-start">
            <div class="zui-inline">
                <label class="zui-form-label">检索</label>
                <div class="zui-input-inline ">
                    <input class="zui-input wh180" placeholder="请输入关键字" @keyDown="sschangeDown2" id="search2"/>
                </div>
            </div>
            <div class="zui-inline flex-start padd-l-10">
                <label class="padd-r-10"><input type="radio" @click="changeType('qb')" id="yzgl_qb" name="yzgl" checked="checked">全部</label>
                <label class="padd-r-10"><input type="radio" @click="changeType('yd')" id="yzgl_wt" name="yzgl">已对码</label>
                <label class="padd-r-10"><input type="radio" @click="changeType('wd')" id="yzgl_yt" name="yzgl">未对码</label>
            </div>
        </div>
    </div>

    <div class="zlbxxm-box">
        <div class="ypbxxm-left">
            <div class="sjsc-top">
                <div class="sjsc-table-name">
                    <i>保险类别编码</i>
                    <i>保险类别名称</i>
                </div>
                <vue-scroll :ops="pageScrollOps">
                    <ul class="sjsc-table-list">
                        <li v-for="(item,$index) in jsonList" @click="checkOne($index)">
                            <i v-text="item.bxlbbm">保险类别编码</i>
                            <i v-text="item.bxlbmc" data-title="zhy0002002">保险类别名称</i>
                        </li>
                    </ul>
                </vue-scroll>

            </div>
        </div>
        <div class="ypbxxm-right">
            <div class="zui-table-view" v-cloak>
                <!--start-->
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell text-left"><span>序号</span></div></th>
                            <th><div class="zui-table-cell cell-xl"><span>药品编码</span></div></th>
                            <th><div class="zui-table-cell cell-xxl text-left"><span>药品名称</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>药品规格</span></div></th>
                            <th><div class="zui-table-cell"><span>发药单位</span></div></th>
                            <th><div class="zui-table-cell"><span>零价</span></div></th>
                            <th><div class="zui-table-cell cell-xl"><span>保险编码</span></div></th>
                            <th><div class="zui-table-cell cell-xl"><span>保险名称</span></div></th>
                            <th><div class="zui-table-cell"><span>项目类别</span></div></th>
                            <th><div class="zui-table-cell"><span>收费项目类别</span></div></th>
                            <th><div class="zui-table-cell"><span>收费项目等级</span></div></th>
                            <th><div class="zui-table-cell"><span>审批信息</span></div></th>
                            <th><div class="zui-table-cell"><span>药品产地</span></div></th>
                            <th><div class="zui-table-cell"><span>农合统筹</span></div></th>
                            <th><div class="zui-table-cell"><span>字典类别</span></div></th>
                            <th><div class="zui-table-cell"><span>剂量单位</span></div></th>
                            <th><div class="zui-table-cell"><span>拼音代码</span></div></th>
                            <th><div class="zui-table-cell"><span>种类</span></div></th>
                            <th><div class="zui-table-cell"><span>剂型</span></div></th>
                            <th><div class="zui-table-cell"><span>类型</span></div></th>
                            <th><div class="zui-table-cell"><span>保险类别</span></div></th>

                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body"  @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @dblclick="edit($index)"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()">
                            <td class="cell-m"><div class="zui-table-cell" v-text="$index+1"></div></td>
                            <td><div class="zui-table-cell cell-xl" v-text="item.xmbm">药品编码22645</div></td>
                            <td><div class="zui-table-cell cell-xxl text-left" v-text="item.xmmc"></div></td>
                            <td><div class="zui-table-cell cell-l" v-text="item.fygg">药品规格</div></td>
                            <td><div class="zui-table-cell">发药单位</div></td>
                            <td><div class="zui-table-cell" v-text="item.fydj">零价</div></td>
                            <td><div class="zui-table-cell cell-xl" v-text="item.bxxmbm">保险编码</div></td>
                            <td>
                                <div class="zui-table-cell cell-xl" >
                                    <span v-show="isEdit != $index" v-text="item.bxxmmc"></span>
                                    <input :id="'mc_'+$index" v-show="isEdit == $index" v-model="item.bxxmmc" @input="searching($index,false,'bxxmmc',$event.target.value)" @keyDown="changeDown($index,$event,'text')">
                                    <search-table :message="searchCon" :selected="selSearch"
                                                  :them="them" :them_tran="them_tran" :page="page"
                                                  @click-one="checkedOneOut" @click-two="selectOne">
                                    </search-table>
                                </div>
                            </td>
                            <td><div class="zui-table-cell" v-text="item.ybmllb">项目类别</div></td>
                            <td><div class="zui-table-cell" v-text="item.ybsflb">收费项目类别</div></td>
                            <td><div class="zui-table-cell" v-text="item.ybsfdj">收费项目等级</div></td>
                            <td><div class="zui-table-cell" >审批信息</div></td>
                            <td><div class="zui-table-cell" >药品产地</div></td>
                            <td><div class="zui-table-cell" >农合统筹</div></td>
                            <td><div class="zui-table-cell">字典类别</div></td>
                            <td><div class="zui-table-cell">剂量单位</div></td>
                            <td><div class="zui-table-cell">拼音代码</div></td>
                            <td><div class="zui-table-cell">种类</div></td>
                            <td><div class="zui-table-cell">剂型</div></td>
                            <td><div class="zui-table-cell">类型</div></td>
                            <td><div class="zui-table-cell" v-text="item.bxlbbm">保险类别</div></td>
                            <!--绑定数据放开 数据为空的时候占位-->
                            <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
                        </tr>
                        </tbody>
                    </table>


                </div>
                <!--左侧固定start-->
                <div class="zui-table-fixed table-fixed-l">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()">

                                <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--左侧固定end-->
                <!--右侧固定start-->
                <div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-s"><div class="zui-table-cell cell-s"><span>保险类别</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()">
                                <td  class="cell-s">
                                    <div class="zui-table-cell cell-s">保险类别</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--右侧固定end-->
                <!--分页start-->
                <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
                <!--分页end-->
            </div>
        </div>

    </div>
</div>
<style type="text/css">
    .tong-search{
        padding: 13px 20px 5px 20px;
    }
    .padd-l-10{
        padding-left: 10px !important;
    }
    .zui-form .zui-inline{
        margin-bottom: 0px;
    }
    .zlbxxm-box{
        background: #fff;
        width: 100%;
        height: calc(100% - 50px);
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 10px;
    }
    .zlbxxm-height{
        background: #fff;
    }
    .ypbxxm-left{
        width: 280px;
    }
    .ypbxxm-right{
        width: calc(100% - 280px);
    }
    .sjsc-top{
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        box-sizing: border-box;
        flex-wrap: wrap;
        padding: 0 10px 0 0;
    }
    .sjsc-label{
        width: 80px;
        font-size: 14px;

    }
    .sjsc-table-name{
        width: 100%;
        display: flex;
        justify-content: flex-start;
        background: #f9fbfc;
        height: 34px;
        align-items: center;
        border: 1px solid #eee;
        padding: 0 5px;
        box-sizing: border-box;
    }
    .sjsc-table-name i{
        width: calc(100%  / 2);
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .sjsc-table-list{
        width: 100%;
        display: block;
        box-sizing: border-box;
        height:82vmin;
    }
    .sjsc-table-list li{
        width: 100%;
        display: flex;
        justify-content: flex-start;
        line-height: 40px;
        border: 1px solid #eee;
        border-top: none;
        cursor: pointer;
        padding: 0 5px;
    }
    .sjsc-table-list li:hover{
        background: rgba(237, 250, 247,1);
    }
    .sjsc-table-list li i{
        width: calc( 100% / 2);
        text-align: center;
        display: flex;
        justify-content: center;
        flex-wrap: nowrap;
        overflow: hidden;
    }
    .zui-table-view .zui-table-tool{
        left: 295px;
    }
</style>
<script type="application/javascript" src="ypbxxm.js"></script>