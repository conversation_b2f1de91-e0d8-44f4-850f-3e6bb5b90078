window.gz_001=new Vue({
    el:'#gz_001',
    mixins: [dic_transform, tableBase, mformat,nhBx,baseFunc,mConfirm],
    data:{
        json:{
            billCode:'',
            rztg:null,
            chh:'',
            bxlbbm:''
        },
        popContent: {
            jbmc:'',
            rylx:"1",
            sfzz:false,
            sftg:false,
            sfkjtzh:false,
            sycs:0
        },
        bxurl:'',
        InfoList:[],
        brfyList:[],
        outpId:"",
        bcfy:0,

        details:[],
        gh_dhhm:'', // 挂号页面电话号码
    },

    created:function () {
    },
    methods:{
    	change_dhhm:function(){
    		contextInfo.json.lxrdh = gz_001.gh_dhhm;
    	},
        //获取参合号
        getChzh: function () {

            gz_001.chh = contextInfo.json.ybkh;
            gz_001.gh_dhhm =  contextInfo.json.lxrdh;
            gz_001.json.bxlbbm = contextInfo.json.bxlbbm;
        },
        //获取家庭成员编号
        getPerson:function() {
            var head = {
                operCode: "S03",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var yeare = new Date();
            var body = {
                year: yeare.getFullYear(),
                medicalNo: gz_001.chh
            };

            var param = {
                head: head,
                body: body
            }
            var str_param = JSON.stringify(param);
            console.log(str_param);

            gz_001.popContent.ylzh = gz_001.chh;
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.json.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    console.log(json);
                    if (json.a == 0) {
                        var res = eval('(' + json.d + ')')
                        gz_001.InfoList = res.list;
                        if(json.e){ // @yqq
                        	var res1 = eval('(' + json.e + ')')
                            gz_001.details = res1.data[0].member;
                        }

                    } else {
                        malert("农合网络错误，请关闭该窗口从新操作",'top','defeadted');
                    }
                });
        },
        //赋值操作
        edit: function (index) {
            contextInfo.json.brxm = gz_001.InfoList[index].memberName;
            contextInfo.json.sfzjhm = gz_001.InfoList[index].idcard;
            contextInfo.json.brxb = gz_001.InfoList[index].memberSex;
            contextInfo.setAge()
            if (gz_001.rztg) {
                if (conBtu.is_csqx.N05001200251 == '1' && gz_001.gh_dhhm == null) {
                    malert("联系电话不能为空！",'top','defeadted');
                    return
                }
                if (gz_001.popContent.rylx == null) {
                    malert("入院类型不能为空！",'top','defeadted');
                    return
                }
                gz_001.json.jbmc = "一般性医学检查";//Z00.000

                if (conBtu.is_csqx.N05001200251 == '1' && gz_001.gh_dhhm.length > 11) {
                    console.log(gz_001.gh_dhhm.length);
                    malert("电话号已超过最大长度，请核对！",'top','defeadted');
                    return
                }
                // @yqq
                contextInfo.gznhType = true;
                contextInfo.gznhObj = { jzmzName:'无', outpCompensateCost:'0', chroCompensateCost:'0'};
                contextInfo.gznhObj.outpCompensateCost = gz_001.InfoList[index].outpCompensateCost;
                contextInfo.gznhObj.chroCompensateCost = gz_001.InfoList[index].chroCompensateCost;
                if(gz_001.details.length > 0){
                	contextInfo.gznhObj.jzmzName = gz_001.details[index].jzmzName;
                }


                gz_001.nhsjContent1 = gz_001.InfoList[index];
                gz_001.nhsjContent2 = gz_001.popContent;
                gz_001.nhsjContent2.jbmc = $("#jbmc").val();
                console.log(gz_001.nhsjContent1);
                console.log(gz_001.nhsjContent2);
                contextInfo.bxbz = "1";
                malert("农合信息保存成功！",'top','success');
                contextInfo.bxShow = false;
            } else {
                malert("请稍等，正在进行农合身份认证",'top','defeadted');
            }

        },

        //农合登记
        nhjs18:function(){
            var result = "0";//0-成功，1-错误直接退回，2-有登记需调用删除登记
            var head = {
                operCode: "S18",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var sfzz = "0";
            var sftg = '1';
            var sfkjtzh = "0";
            var mzzd = '无';
            var sycs = 0;

            //判断是否是挂号病人
            var jzysxm=contextInfo.json.jzysxm;
            //数据必填判断
            if (gz_001.billCode == null || gz_001.billCode == undefined || gz_001.billCode == ""){
                malert("请先关闭窗口，重新打开窗口农合初始化!",'top','defeadted');
                return  "1";
            }
            if (!jzysxm){
                malert("挂号医生不能为空，请选择医生后重新保存!",'top','defeadted');
                return  "1";
            }
            if (gz_001.nhsjContent2.rylx == null || gz_001.nhsjContent2.rylx == undefined || gz_001.nhsjContent2.rylx == ""){
                malert("入院类型不能为空，请选择后重新保存!",'top','defeadted');
                return  "1";
            }
            /*if (contextInfo.json.fyje == null || contextInfo.json.fyje == undefined || contextInfo.json.fyje == 0){
                malert("费用金额不能为零，请对应挂号关联费重新保存!",'top','defeadted');
                return  "1";
            }*/
            if (gz_001.gh_dhhm == null || gz_001.gh_dhhm == undefined || gz_001.gh_dhhm == ""){
                malert("联系电话不能为空，请录入电话号码重新保存!",'top','defeadted');
                return  "1";
            }
            if (gz_001.gh_dhhm.length > 11){
                malert("联系电话不能大于11位，请录入正确电话号码重新保存!",'top','defeadted');
                return  "1";
            }

            var body = {
                memberId: gz_001.nhsjContent1.memberId,
                inpatientDate: gz_001.fDate(new Date(), 'date'),
                inpatientDepartments: '03',
                treatingPhysician: jzysxm,
                diseaseCode: 'Z00.000',
                initialDiagnosis: mzzd,
                isIncrease: sftg,
                isInfusion: sfzz,
                isAccountPay: sfkjtzh,
                isOutpPay: '1',
                inpatientTypeOflocal: gz_001.nhsjContent2.rylx,
                bxAccount: contextInfo.json.fyje,
                tel: gz_001.gh_dhhm,
                bankCardNo: gz_001.nhsjContent2.card,
                accountHolder: gz_001.nhsjContent2.khrxm,
                holderRelation: gz_001.nhsjContent2.khrgx,
                infusionCount: sycs,
                remark: gz_001.nhsjContent2.bz,
                uploadType: '0'
            };
            var footer = {
                ylzh: gz_001.nhsjContent2.ylzh,
                brnl: contextInfo.json.brnl,
                brxm: contextInfo.json.brxm,
                jtdz: gz_001.nhsjContent1.areaName,
                jbczy: userId,
                tel:contextInfo.json.lxrdh
            };
            //测试
            var param18 = {
                head: head,
                body: body,
                footer: footer
            }
            console.log(param18);
            var str_param = JSON.stringify(param18);
            //this.postAjax("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.json.bxlbbm + "&types=S&parm="+str_param,function (json) {
            gz_001.updatedAjax(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.json.bxlbbm + "&types=S&parm=" + str_param,
                function (json) {
                 var obj = {};
                if(typeof(json)=='string'){
                    obj = JSON.parse(json);
                }else {
                    obj = json;
                };
                    console.log("农合登记:"+obj);
                if(obj.a==0){
                    var str = obj.c;
                    var  bcdpos = str.indexOf("补偿序号：");
                    if (bcdpos > 0){
                        gz_001.outpId = str.substr(bcdpos+5);
                    }
                }else {
                    var str = obj.c;
                    var  bcdpos = str.indexOf("补偿序号为：");
                    if (bcdpos > 0){
                        gz_001.outpId = str.substr(bcdpos+6);
                        result =  "2";
                    }else{
                        malert("农合补偿号登记失败，请重试!",'top','defeadted');
                        result =  "1";
                    }
                }
            },function (error) {
                malert("农合上传失败"+error+"，请重试!",'top','defeadted');
                result =  "1";
                console.log(error);
            });
            return result;
        },
        //删除登记
        nhjs19:function () {
            var result = "0";//0-成功，1-错误直接退回
            var head = {
                operCode: "S19",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var body = {
                outpId:gz_001.outpId,
            };
            var param = {
                head: head,
                body: body,
            };
            if (gz_001.billCode == null || gz_001.billCode == undefined || gz_001.billCode == ""){
                malert("请先关闭窗口，重新打开窗口农合初始化!",'top','defeadted');
                return  "1";
            }
            if (gz_001.outpId == null || gz_001.outpId == undefined || gz_001.outpId == ""){
                malert("请先农合登记，再重新保存!",'top','defeadted');
                return  "1";
            }
            console.log(param);
            var str_param = JSON.stringify(param);
            gz_001.updatedAjax(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.json.bxlbbm + "&types=S&parm=" + str_param,
                function (json) {
                var obj = {};
                if(typeof(json)=='string'){
                    obj = JSON.parse(json);
                }else {
                    obj = json;
                };

                if(obj.a==0){
                    result =  "0";
                    //malert("农合冲红成功，点击确认重新预结算!");
                }else {
                    malert("抱歉，由于网络原因，冲红错误，点击确认重新预结算!",'top','defeadted');
                    result =  "1";
                }
            },function (error) {
                malert("抱歉，由于网络原因，冲红错误"+error+"，点击确认重新预结算!",'top','defeadted');
                result =  "1";
                console.log(error);
            });
            return result;
        },
        //费用上传
        nhjs21:function () {
            var result = "0";//0-成功，1-错误直接退回
            var head = {
                operCode: "S21",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var bodylist = [];
            for (var i = 0; i < gz_001.brfyList.length; i++){
                var detail = {
                    detailName:gz_001.brfyList[i].bxxmmc,
                    detailCode:gz_001.brfyList[i].bxxmbm,
                    detailHosCode:gz_001.brfyList[i].bxxmbm,
                    typeCode:gz_001.brfyList[i].bxxmlb,
                    num:gz_001.brfyList[i].fysl,
                    price:gz_001.brfyList[i].fydj,
                    totalCost:gz_001.brfyList[i].fyje,
                    date:contextInfo.fDate(new Date(),'date'),
                    unit:null,
                    standard:null,
                    formulations:null,
                };
                bodylist.push(detail);
            }
            var list={
                detail:bodylist
            }
            var body={
                outpId:gz_001.outpId,
                list:list
            };
            var param = {
                head:head,
                body:body
            };
            var str_param = JSON.stringify(param);
            gz_001.updatedAjax(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.json.bxlbbm + "&types=S&parm=" + str_param,
                function (json) {
                    var obj = {};
                    if(typeof(json)=='string'){
                        obj = JSON.parse(json);
                    }else {
                        obj = json;
                    };

                    if(obj.a==0){
                        result = "0";
                        //malert("农合预结算成功!");
                    }else {
                        result =  "1";
                        malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!",'top','defeadted');
                    }
                },function (error) {
                    result =  "1";
                    console.log(error);
                });
            return result;
        },
        //上传费用
        nhyjsghf:function () {
            var result = "0";//0-成功，1-错误直接退回
            var param = {
                ghzlbm: contextInfo.type//contextInfo.json.ghzl
            };
            var str_param = JSON.stringify(param);
            gz_001.updatedAjax(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.json.bxlbbm + "&types=mzgh&method=ghfy&parm=" + str_param,
                function (json) {
                    if (json.a == 0) {
                        gz_001.brfyList =JSON.parse(json.d).list;
                        console.log(JSON.parse(json.d));
                        result = "0";
                    } else {
                        malert("费用查询失败", 'top', 'defeadted');
                        result = "1";
                    }
                });
            return result;
        },
        //预结算
        nhjs24:function () {
            var result = "0";//0-成功，1-错误直接退回
            var head = {
                operCode: "S24",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var body = {
                outpId:gz_001.outpId,
            };
            var param = {
                head: head,
                body: body,
            };
            var str_param = JSON.stringify(param);
            gz_001.updatedAjax(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.json.bxlbbm + "&types=S&parm=" + str_param,
                function (json) {
                    var obj = {};
                    if(typeof(json)=='string'){
                        obj = JSON.parse(json);
                    }else {
                        obj = json;
                    };

                    if(obj.a==0){
                        var res = JSON.parse(obj.d).list[0];
                        contextInfo.bcfy = contextInfo.fDec(res.compensatecost + res.salvaclcost + res.salvayfcost + res.salvajscost + res.salvafpcost + res.civilcost, 2)
                        contextInfo.outpId = gz_001.outpId;
                        //malert("农合预结算成功!");bxjsh:"",//贵州农合保险结算号
                        // 		bcfy:0//贵州
                        result = '0';
                    }else {
                        result =  "1";
                        malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!",'top','defeadted');
                    }
                },function (error) {
                    result =  "1";
                    console.log(error);
                });
            return result;
        },
        //结算
        nhjs25:function () {
            var result = "0";//0-成功，1-错误直接退回
            var head = {
                operCode: "S25",
                billCode: gz_001.billCode,
                rsa: ""
            };
            var body = {
                outpId:gz_001.outpId,
            };
            var param = {
                head: head,
                body: body,
            };
            var str_param = JSON.stringify(param);
            gz_001.updatedAjax(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + gz_001.bxurl + "&bxlbbm=" + gz_001.json.bxlbbm + "&types=S&parm=" + str_param,
                function (json) {
                    var obj = {};
                    if(typeof(json)=='string'){
                        obj = JSON.parse(json);
                    }else {
                        obj = json;
                    };

                    if(obj.a==0){
                        malert("农合结算成功!");
                        result = '0';
                    }else {
                        result =  "1";
                        //malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!");
                    }
                },function (error) {
                    result =  "1";
                    console.log(error);
                });
            return result;
        },
        //农合结算
        gzydnhyjs:function () {
            var result = "0";
            result = gz_001.nhyjsghf();//先费用查询
            if (result == "1"){
                malert("抱歉，挂号费用查询失败，不用农合结算!",'top','defeadted');
                return result;
            }
            if (gz_001.brfyList.length <= 0){
                malert("抱歉，挂号费用查询失败，不用农合结算!",'top','defeadted');
                return result;
            }
            //农合登记
            result = gz_001.nhjs18();
            if (result == "1"){//错误直接返回
                //malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!",'top','defeadted');
                return result;
            }else if (result == "2"){
                result = gz_001.nhjs19();//冲红
                if (result == "1"){
                    malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!",'top','defeadted');
                    return result;
                }
                //删除后重新调用登记
                result = gz_001.nhjs18();
                if (result == "1") {
                    //malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!", 'top', 'defeadted');
                    return result;
                }
            }

            //上传费用
            result = gz_001.nhjs21();
            if (result == "1"){
                malert("抱歉，费用上传失败，点击确认重新预结算!",'top','defeadted');
                return result;
            }
            //预结算
            result = gz_001.nhjs24();
            // if(common.openConfirm('本次报销费用'+contextInfo.bcfy,function () {
            //     result='0'
            // },function () {
            //     result='1'
            // }))
            if(!mconfirm('本次报销费用'+contextInfo.bcfy)) {
                result='1'
            };
            if (result == "1"){
                malert("抱歉，预结算失败，点击确认重新预结算!",'top','defeadted');
                return result;
            }
            //结算
            result = gz_001.nhjs25();
            if (result == "1"){
                malert("抱歉，结算失败，点击确认重新预结算!",'top','defeadted');
                return result;
            }
            return  result;
        },
        //查询挂号费
        gzydnhghf:function () {
            var result = "0";
            result = gz_001.nhyjsghf();//先费用查询
            if (result == "1"){
                malert("抱歉，挂号费用查询失败，不用农合结算!",'top','defeadted');
                return result;
            }
            if (gz_001.brfyList.length <= 0){
                malert("抱歉，挂号费用查询失败，不用农合结算!",'top','defeadted');
                return result;
            }
            //农合登记
            result = gz_001.nhjs18();
            if (result == "1"){//错误直接返回
                malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!",'top','defeadted');
                return result;
            }else if (result == "2"){
                result = gz_001.nhjs19();//冲红
                if (result == "1"){
                    malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!",'top','defeadted');
                    return result;
                }
                //删除后重新调用登记
                result = gz_001.nhjs18();
                if (result == "1") {
                    malert("抱歉，由于网络原因，预结算错误，点击确认重新预结算!", 'top', 'defeadted');
                    return result;
                }
            }

            //上传费用
            result = gz_001.nhjs21();
            if (result == "1"){
                malert("抱歉，费用上传失败，点击确认重新预结算!",'top','defeadted');
                return result;
            }
            //预结算
            result = gz_001.nhjs24();
            if (result == "1"){
                malert("抱歉，预结算失败，点击确认重新预结算!",'top','defeadted');
                return result;
            }
            return  result;
        }
    },


});
gz_001.getChzh();
gz_001.getbxlb();

gz_001.gh_dhhm = contextInfo.json.lxrdh;
