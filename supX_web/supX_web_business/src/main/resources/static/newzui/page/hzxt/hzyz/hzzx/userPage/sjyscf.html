<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>三级医师查房</title>
    <link href="userPage/user.css" rel="stylesheet">
    <link href="userPage/sjyscf.css" rel="stylesheet">
</head>
<body>
    <div class="sjys-box" v-cloak>
           <ul class="sjys-time-line">
              <li>
                <span class="sjys-dot"></span>
                <div class="sjys-check">
                <h3>主任医师查房</h3>
                <button v-waves class="sjys-btn" @click="reviewed">审核</button>
                </div>
                <div class="sjys-inquiry">主任医师今日查房：仔细询问病史，患儿1岁，因“咳嗽、仔细询问病史，患儿1岁，因“咳嗽…</div>
                <div class="sjys-charge">主管医生:Henry   查房医生：Henry</div>
                <div class="sjys-dsh"><!--待审核状态，根据--></div>
              </li>
               <li>
                   <span class="sjys-dot1"></span>
                   <div class="sjys-check">
                       <h3>主治医师查房</h3>
                       <button v-waves class="sjys-btn sjys-btn1" @click="EditReport">编辑</button>
                   </div>
                   <div class="sjys-inquiry">***主任医师今日查房：仔细询问病史，患儿1岁，因“咳嗽、发热6天”入院，有明显受凉病史，否认有异物吸入病史，以发热、咳嗽为主要症状，发热为阵发性，具体
                       体温未测，发热时间无明显规不伴呛咳、呼吸困难、潮热、盗汗、腹泻。查体：T37.0C，呼吸平稳，口唇红润，咽部充血，气管居中，双肺呼吸音粗，可闻及相对较
                       固定的中细湿罗音及少许哮鸣音，心律齐有力腹部未见异常，血常规：WBC:9.94×109/L，RBC:4.91×1012/L，HGB:157g/L，PLT:173×109/L，L:16.9%，M:10.1%，
                       N:71.8%。胸片示支气管肺炎。***主任考虑诊断为支气管肺炎。应与大叶性肺炎相鉴别，仅有症状相似，无明显呼吸音低，双肺叩诊未见浊音，不支持。目前治疗患者
                       有好转，***主任考虑诊断为支气管肺炎。应与大叶性肺炎相鉴别，仅有症状相似，无明显呼吸音低，双肺叩诊未见浊音，不支持。目前治疗患者有好转，</div>
                   <div class="sjys-charge">主管医生:Henry   查房医生：Henry</div>
                   <div class="sjys-dsh sjys-wtg"></div>
               </li>
               <li>
                   <span class="sjys-dot sjys-dot2"></span>
                   <div class="sjys-check">
                       <h3>主管医师查房</h3>
                   </div>
                   <div class="sjys-inquiry">主任医师今日查房：仔细询问病史，患儿1岁，因“咳嗽、仔细询问病史，患儿1岁，因“咳嗽…，</div>
                   <div class="sjys-charge">主管医生:Henry   查房医生：Henry</div>
               </li>
           </ul>

        <div class="sjys-fill" @click="bgOpen">
            <i class="iconfont icon-iocn46 icon-cf icon-27"></i>
            <i class="sjys-font12">查房填写</i>
        </div>
    </div>
<transition name="slide-fade">
 <div class="sjys-report" v-cloak v-show="bgShow">
    <div class="report-title">
        <h2>三级医师查房报告填写</h2>
        <span class="report-close"><i class="iconfont icon-iocn55 icon-font24 icon-c1" @click="bgClose"></i></span>
    </div>
    <div class="report-width">
     <vue-scroll :ops="pageScrollOps">
        <div class="report-text" contenteditable="true" placeholder="请输入报告">
            ***主任医师今日查房：仔细询问病史，患儿1岁，因“咳嗽、发热6天”入院，有明显受凉病史，否认有异物吸入病史，以发热、咳嗽为主要症状，发热为阵发性，具体
            体温未测，发热时间无明显规不伴呛咳、呼吸困难、潮热、盗汗、腹泻。查体：T37.0C，呼吸平稳，口唇红润，咽部充血，气管居中，双肺呼吸音粗，可闻及相对较
            固定的中细湿罗音及少许哮鸣音，心律齐有力腹部未见异常，血常规：WBC:9.94×109/L，RBC:4.91×1012/L，HGB:157g/L，PLT:173×109/L，L:16.9%，M:10.1%，
            N:71.8%。胸片示支气管肺炎。***主任考虑诊断为支气管肺炎。应与大叶性肺炎相鉴别，仅有症状相似，无明显呼吸音低，双肺叩诊未见浊音，不支持。目前治疗患者
            有好转，***主任考虑诊断为支气管肺炎。应与大叶性肺炎相鉴别，仅有症状相似，无明显呼吸音低，双肺叩诊未见浊音，不支持。目前治疗患者有好转，
            有好转，***主任考虑诊断为支气管肺炎。应与大叶性肺炎相鉴别，仅有症状相似，无明显呼吸音低，双肺叩诊未见浊音，不支持。目前治疗患者有好转，
            有好转，***主任考虑诊断为支气管肺炎。应与大叶性肺炎相鉴别，仅有症状相似，无明显呼吸音低，双肺叩诊未见浊音，不支持。目前治疗患者有好转，
            有好转，***主任考虑诊断为支气管肺炎。应与大叶性肺炎相鉴别，仅有症状相似，无明显呼吸音低，双肺叩诊未见浊音，不支持。目前治疗患者有好转，
            有好转，***主任考虑诊断为支气管肺炎。应与大叶性肺炎相鉴别，仅有症状相似，无明显呼吸音低，双肺叩诊未见浊音，不支持。目前治疗患者有好转，
        </div>
     </vue-scroll>
    </div>
    <div class="report-standard">
        未按标准填写，请重新填写！
    </div>
    <div class="report-director">
        <div class="director-left">
            <div class="director-job">主任医师</div>
             <div class="director-Imgtext" v-if="delShow">
                <span class="director-img">
                 <img src="/newzui/pub/image/wtg.png">
                 <i class="director-del" @click="del" data-title="移除"></i>
                 </span>
                 <span class="font12">刘医生</span>
             </div>
            <div class="director-Imgtext"@click="addDoctor" data-title="添加主任医生">
             <div class=" director-add">
                 <i class="iconfont icon-iocn42 icon-c4 icon-font25"></i>
             </div>
                <span style="width: 100%;height: 20px;display: block"><!--占位--></span>
            </div>

        </div>
        <div class="director-right">
            <div class="director-job">主治医师</div>
            <div class="director-Imgtext">
                <span class="director-img">
                 <img src="/newzui/pub/image/wtg.png">
                 <i class="director-del" data-title="移除"></i>
                 </span>
                <span class="font12">刘医生</span>
            </div>
            <div class="director-Imgtext"@click="addDrug" data-title="添加主治医生">
                <div class=" director-add">
                    <i class="iconfont icon-iocn42 icon-c4 icon-font25"></i>
                </div>
                <span style="width: 100%;height: 20px;display: block"><!--占位--></span>
            </div>
        </div>
        <button v-waves class="root-btn btn-parmary director-position" @click="reportOk">确定</button>
    </div>

 </div>
    </transition>
<div class="side-form  pop-width" :class="{'ng-hide':numOne==1}"  id="brzcList" role="form" v-cloak>
    <div class="fyxm-side-top flex-between">
        <span v-text="title"></span>
        <span class="fr iconfont icon-iocn55 icon-cf056 icon-font20" @click="close"></span>
    </div>
    <div class="ksys-side">
        <div class="top-form">
            <label class="top-label">科室</label>
                <div class="top-zinle">
                    <select-input class="wh122" @change-data="resultChange"
                                  :child="XsList" :index="'bgxs'" :index_val="'ksbm'" :val="popContent.ksbm"
                                  :name="'popContent.ksbm'" :search="true" :index_mc="'bgxs'" >
                    </select-input>
                </div>
        </div>

        <div class="zui-table-view" style="margin-top: 10px;">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span></span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>医生姓名</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>科室</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>职称</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <vue-scroll :ops="pageScrollOps">
            <div class="zui-table-body body-height">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in 30"
                        :tabindex="$index"
                        ref="list"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','10'],$event)">
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'10'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td><div class="zui-table-cell cell-s">医生姓名</div></td>
                        <td><div class="zui-table-cell cell-s">科室</div></td>
                        <td><div class="zui-table-cell cell-s">职称</div></td>

                    </tr>
                    </tbody>
                </table>
            </div>
            </vue-scroll>
        </div>
    </div>
    <div class="ksys-btn padd-r-10">
        <button v-waves class="root-btn btn-parmary-d9" @click="close">取消</button>
        <button v-waves class="root-btn btn-parmary" @click="sideOk">确定</button>
    </div>
</div>
<div id="pop" class="pophide"  :class="{'show':popShow}" v-cloak>
        <div class="sjys-width">
            <div class="sjys-top">
                <span v-text="topTitle"></span>
                <span class="iconfont icon-iocn55 icon-cf056 icon-font20" @click="Popclose"></span>
            </div>
            <div class="sjys-content" v-show="patientShow">确认审核患者：<i class="color-dsh">李浩然</i> 的三级医师查房报告吗？</div>
            <div class="sjys-textarea" v-show="patientShow==false">
                    <textarea placeholder="请输入拒绝原因"></textarea>
            </div>
            <div class="sjys-pop-btn" v-show="patientShow">
                <button v-waves class="root-btn btn-parmary-f2a" @click="refuse">拒绝</button>
                <button  v-waves class="root-btn btn-parmary" @click="popConfirm">审核</button>

            </div>
            <div class="sjys-pop-btn sjys-pop-btn-t" v-show="patientShow==false">

                <button v-waves class="root-btn btn-parmary-d9" @click="Popclose">取消</button>
                <button  v-waves class="root-btn btn-parmary" @click="popConfirms">确定</button>
            </div>
        </div>
</div>

</body>
<script type="text/javascript" src="userPage/sjyscf.js"></script>
</html>
