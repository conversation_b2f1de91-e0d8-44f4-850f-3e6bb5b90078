//单页面菜单的加载
var InfoMenu = new Vue({
    el: '#InfoMenu',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    components: {
        'search-table': searchTable
    },
    data: {
        qjIndex:null,
        jsonList: [],
        popContent: {},
        searchCon: [],
        selSearch: -1,
        totalPage: 0,
        param:{
            page: 1,
            rows: 10,
            'sfdm': '0',
            'type': 'qb',
            'lbbm': 'ICD_10'
        },
        queryPage: {
            page: 1,
            rows: 10,
            total: null
        },
        lbbm_tran:{
            'ICD_10'    : '诊断编码',
            'ICD_9_CM3' : '手术编码',
            'ICD_ZLBM'  : '肿瘤编码',
            'ICD_ZYBM_B': '中医主病',
            'ICD_ZYBM_Z': '中医主症',
            'RC002': '婚姻状况',
            'RC003': '职业编码',
            'RC013': '麻醉方式',
            'RC023': '科室编码',
            'RC032': '医疗付款方式',
            'RC033': '联系人关系',
            'RC035': '民族编码',
            'RC036': '籍贯编码',
            'RC038': '国籍编码',
            'RC041': '证件类型',
        },
        ip: '/jxkh-api',
        them_tran: {},
        them: {'编码': 'bm', '名称': 'mc', '备注描述':'bzms'}
    },
    mounted:function (){
        this.getData();
    },
    methods: {
        commonResultChange:function(val){
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "type":
                    if(val[0]=='wd'){//未对码
                        Vue.set(this.param, 'sfdm', '1');
                        Vue.set(this.param, 'type', 'wd');
                    }else if(val[0]=='yd'){//已对码
                        Vue.set(this.param, 'sfdm', '2');
                        Vue.set(this.param, 'type', 'yd');
                    }else{//全部
                        Vue.set(this.param, 'sfdm', '0');
                        Vue.set(this.param, 'type', 'qb');
                    }
                    break;
                case "lbbm":
                    Vue.set(this.param, 'lbbm', val[0]);
                    break;
            }
            this.goToPage(1);
        },

        getData:function(){
            this.$http.post(this.ip + "/baseData/queryMapping", JSON.stringify(this.param)).then(function (data) {
                var json = data.body;
                if (json.status == 200) {
                    var res = json.data;
                    InfoMenu.totlePage = res.totalPage;
                    InfoMenu.jsonList = res.list;
                } else {
                    malert("查询项目失败：" + json.body.msg, "top", "defeadted");
                }
            });
        },

        // 点击进行赋值的操作
        selectOne: function (item, index) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(InfoMenu.qjIndex, true, 'bzmc', InfoMenu.jsonList[InfoMenu.qjIndex].bzmc); // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                InfoMenu.popContent = item;
                InfoMenu.jsonList[InfoMenu.qjIndex].bzbm = this.popContent.bm;
                InfoMenu.jsonList[InfoMenu.qjIndex].bzmc = this.popContent.mc;

                this.save(this.jsonList[InfoMenu.qjIndex].ynbm, this.jsonList[InfoMenu.qjIndex].ynmc, this.jsonList[InfoMenu.qjIndex].lbbm, this.popContent.bm, this.popContent.mc);
                this.selSearch = -1;
                $(".selectGroup").hide();
            }
        },

        changeDown: function (index, event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'popContent', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 || event.code =='NumpadEnter') {
                Vue.set(this.jsonList[index], 'bzbm', this.popContent['bm']);
                Vue.set(this.jsonList[index], 'bzmc', this.popContent['mc']);

                this.save(this.jsonList[index].ynbm, this.jsonList[index].ynmc, this.jsonList[index].lbbm, this.popContent['bm'],this.popContent.mc);
                this.nextFocus(event);
                this.selSearch = -1;
                $(".selectGroup").hide();
            }
        },

        // 输入内容进行检索
        searching: function (index, add, type,val) {
            InfoMenu.qjIndex=index;
            this.jsonList[index]['bxxmmc'] = val;
            this.jsonList[index]['bzmc'] = val;
            if (!add) {
                this.queryPage.page = 1;
            }else {
                this.queryPage.page ++ ;
            }
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            InfoMenu.popContent = {};
            var searchParm = {
                'page' : this.queryPage.page,
                'rows' : this.queryPage.rows,
                'parm' : val,
                'lbbm' : this.jsonList[index]['lbbm']
            };

            this.$http.post(InfoMenu.ip + "/baseData/queryBzbm", JSON.stringify(searchParm)).then(function (data) {
                var json = data.body;
                if (json.status == 200) {
                    var res = json.data;
                    if (add) {
                        for (var i = 0; i < res.list.length; i++) {
                            InfoMenu.searchCon.push(res.list[i]);
                        }
                    } else {
                        InfoMenu.searchCon = res.list;
                    }
                    InfoMenu.queryPage.total = res.totalSize;
                    InfoMenu.selSearch = 0;
                    if (res.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                } else {
                    malert(json.msg, "top", "defeadted");
                }
            });
        },

        autoMapping:function(){
            this.$http.post(this.ip + "/baseData/autoMapping", JSON.stringify(this.param)).then(function (data) {
                var json = data.body;
                if (json.status == 200) {
                    malert("自动对码成功" + json.data +"条");
                } else {
                    malert("自动对码失败：" + json.msg, "top", "defeadted");
                }
            });
        },

        save:function(ynbm, ynmc, lbbm, bzbm, bzmc){
            var saveParm = {
                ynbm : ynbm,
                ynmc : ynmc,
                lbbm : lbbm,
                bzbm : bzbm,
                bzmc : bzmc,
            };
            this.$http.post(this.ip + "/baseData/saveMapping", JSON.stringify(saveParm)).then(function (data) {
                var json = data.body;
                if (json.status == 200) {
                    malert("保存成功");
                } else {
                    malert("保存失败：" + json.msg, "top", "defeadted");
                }
            });
        },

        exportData: function () {
            common.openloading('.zui-table-view');
            this.$http.post(this.ip + "/baseData/exportMapping", JSON.stringify(this.param)).then(function (data) {
                common.closeLoading();
                var json = data.body;
                if (json.status == 200) {
                    malert("导出成功");
                    window.location.href = tableInfo.ip + json.data;
                } else {
                    malert("导出失败：" + json.msg, "top", "defeadted");
                }
            },function (error) {
                common.closeLoading();
                console.log(error);
            });
        }
    }
});
$(document).click(function () {
    if (this.className != 'selectGroup') {
        $(".selectGroup").hide();
    }
    $(".popInfo ul").hide();
});

