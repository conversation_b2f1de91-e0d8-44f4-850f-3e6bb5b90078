(function () {
    $(".zui-table-view").uitable();
    var s=new Date().getTime()
    var l=new Date()
    var e=l.setDate(l.getDate()+1)
    var wrapper = new Vue({
        el: '.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            totlePage:'',
            index: 1,
            old: '原始数据',
            pop: {},
            param: {
                page: 1,
                rows: 10,
                total: '',
                time:'',
                bah: '',
                jyxh:''
            },
            jydjList: []
        },

        methods: {
            addclass: function (num) {
                this.index = num
            },
            dayin: function () {
                pop.isShowpopL = true;
                pop.isShow = true;
                pop.pop = '打印申请';
                pop.centent = '确定对选中的记录打印申请单吗？？'
            },
            //点击获取申请
            hqsq: function () {
                // list.jydjList = [{'Id': '001'}, {'Id': '002'}, {'Id': '003'}, {'Id': '004'}, {'Id': '005'}];
                list.jydjList = [];
                //解析时间
                if (this.param.time != null) {
                    var times = this.param.time.split(" - ");
                    this.param.sqrq = times[0];
                    this.param.sqrqEnd = times[1];
                }
                this.param.lx = '0';//门诊
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryMzcydj&yq=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                        list.totlePage =Math.ceil(json.d.total / wrapper.param.rows);
                        list.jydjList = json.d.list;
                        if(list.jydjList.length>0){
                            malert('获取申请成功！','top','success');
                        }else{
                            malert('没有新的申请了','top','defeadted');
                        }

                    } else {
                        malert("获取申请失败" + json.c);
                        return false;
                    }
                });
            },
            getData : function (type){
                //解析时间
                if (this.param.time != null) {
                    var times = this.param.time.split(" - ");
                    this.param.sqrq = times[0];
                    this.param.sqrqEnd = times[1];
                }
                this.param.lx = '0';//门诊
                
                console.log(this.param);
                
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryCydj_mz&yq=" + JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                        list.totlePage =Math.ceil(json.d.total / wrapper.param.rows);
                        list.jydjList = json.d.list;
                        if(list.jydjList.length>0){
                            if(type != 'malert'){
                                malert('查询成功','top','success');
                            }
                        }else{
                            malert('没有查询出申请！','top','defeadted');
                        }

                    } else {
                        malert("获取申请失败" + json.c);
                        return false;
                    }
                });
            },
            cxjydj : function(){
                this.getData();
            },
            cydj: function () {
            	if(this.isChecked.length>0){
            	
	                pop.isShowpopL = true;
	                // addClass()
	                pop.isShow = true;
	                //malert('111','top','defeadted')
	                pop.title = '门诊采样登记';
	                pop.centent = '确定对选中的记录进行采样登记吗？';
            	}else{
            		malert("请选择需要采样登记的申请单",'top','defeadted');
            	}
            },
            jysb: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryJysb&yq=", function (json) {
                    if (json.a == 0) {
                        list.jysbList = json.d.list;
                    } else {
                        malert("获取申请检验设备失败" + json.c,'top','defeadted');
                        return false;
                    }
                });
            }
        },
        watch:{
            'param.time':function(){
                this.getData();
            },
            'param.bah':function(){
                this.getData();
            },
            'param.jyxh':function(){
                var a={
                    page: 1,
                    rows: 20,
                    total: '',
                    sqrq:'',
                    sqrqEnd:'',
                    jyxh:this.param.jyxh
                };
                if(a.jyxh != null && a.jyxh != ''){
                    $.getJSON("/actionDispatcher.do?reqUrl=LisYbgl&types=queryMzcydj&yq=" + JSON.stringify(a), function (json) {
                        if (json.a == 0) {
                            list.jydjList = json.d.list;
                            if(json.d.list.length==1){
                                list.isChecked[0]=true;
                                pop.isShowpopL = true;
                                pop.isShow = true;
                                pop.title = '门诊采样登记';
                                pop.centent = '确定对此条码申请进行采样登记吗？';
                            }
                            if(a.jyxh == null && a.jyxh == ''){
                                malert('未查询该条码的数据','top','defeadted');
                            }

                        } else {
                            malert("查询失败" + json.c,'top','defeadted');
                            return false;
                        }
                    });
                }
            }
        }
    });

    var pop = new Vue({
        el: '#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            isShowpopL: false,
            isShow: false,
            title: '',
            centent: ''
        },methods: {
            cydjOk: function (type) {

                var List = [];
                if(this.isChecked.length>0){
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            var jydj = {};
                            List.push(list.jydjList[i]);
                        }
                    }
                    var json = '{"list":' + JSON.stringify(List) + '}';
                    this.$http.post('/actionDispatcher.do?reqUrl=LisYbgl&types=updateCydj',json).then(function(data) {
                        if(data.body.a == 0) {
                            this.isShowpopL=false;
                            this.isShow=false;
                            malert("采样登记成功");
                            wrapper.getData('malert');
                            
                        } else {
                            malert("采样登记失败",'top','defeadted');
                        }
                    }, function(error) {
                    });
                }else{
                    malert("请选择需要采样登记的申请单",'top','defeadted');
                    this.isShowpopL=false;
                    this.isShow=false;
                }

            }
        }
    });
    var list = new Vue({
        el: '.ybglTable',
        mixins: [dic_transform,  baseFunc, tableBase, mformat],
        data: {
            jydjList: '',
            jysbList:[],
            page:'',
            rows:10
        },
        created:function(){
            wrapper.param.time=this.formDate(s)+' - '+this.formDate(e)
        },
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        },
        methods: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            },

            getData : function(){
            	wrapper.param.rows=this.rows;
            	wrapper.param.page=this.page;
                wrapper.getData();
            }


        }
    });

    laydate.render({
        elem: '.todate'
        , eventElem: '.zui-date i.datenox'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            wrapper.param.time = value
        }
    });

    $("#isTabel input").uicomplete({
        iskeyup: false
    });

    document.onkeydown = function (ev) {
        var ev = window.event || ev;
        var key = ev.keyCode;
        if (key == 83 && ev.ctrlKey) {
            return false
        }
    };

    //初始化调用方法
    wrapper.jysb();//获取检验设备
    window.doCheck = function (event){
        var $event = $(event.srcElement.previousElementSibling);
        $event.prop("checked", !$event.prop("checked"));
    };
})();