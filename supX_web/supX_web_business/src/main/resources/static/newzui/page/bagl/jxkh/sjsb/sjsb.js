    //取开始时间
    var tableInfo = new Vue({
        el: '#wrapper',
        //混合js字典庫
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data: {
            scShow:false,
            totlePage:0,
            jsonList: [],
            scJsonList: [],
            param:{
                page: 1,
                rows: 10,
                'sjgs': '2',
                'zxlx': '1'
            },
            htmlData:[
                {text:'医疗机构名称',className:'cell-l'},
                {text:'组织机构代码',className:''},
                {text:'住院次数',className:''},
                {text:'病案号',className:''},
                {text:'姓名',className:''},
                {text:'性别',className:''},
                {text:'出生日期',className:''},
                {text:'年龄',className:''},
                {text:'身份证号码',className:'cell-xl'},
                {text:'现住址',className:'cell-l'},
                {text:'电话',className:''},
                {text:'户口地址',className:'cell-l'},
                {text:'入院时间',className:''},
                {text:'入院科别',className:''},
                {text:'主要诊断',className:''},
                {text:'主治医生',className:''},
                {text:'出院时间',className:''},
                {text:'总费用',className:''}
            ],
            csqxContent:{},
            sjgs_tran: {
                '2': '二级医院',
                '3': '三级医院'
            },
            yylx_tran: {
                '1': '西医',
                '2': '中医'
            },
            ip: '/jxkh-api',
        },
        //页面渲染完成之后加载数据
        mounted: function () {
            //默认加载当前时间
            //初始化检索日期！为今天0点到今天24点
            var myDate=new Date();
            this.param.beginTime = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endTime = this.fDate(new Date(), 'date')+' 23:59:59';
            laydate.render({
                elem: '#timeVal',
                 type: 'datetime',
                value:this.param.beginTime
                , eventElem: '.zui-date'
                , trigger: 'click'
                , theme: '#1ab394'
                , done: function (value, data) {
                    tableInfo.param.beginTime = value;
                }
            });
            laydate.render({
                elem: '#timeVal1',
                value:this.param.endTime,
                    type: 'datetime'
                , eventElem: '.zui-date'
                , trigger: 'click'
                , theme: '#1ab394'
                , done: function (value, data) {
                    tableInfo.param.endTime =value;
                }
            });
        },

        methods: {
            commonResultChange:function(val){
                var type = val[2][val[2].length - 1];
                switch (type) {
                    case "sjgs":
                        Vue.set(this.param, 'sjgs', val[0]);
                        break;
                    case "zxlx":
                        Vue.set(this.param, 'zxlx', val[0]);
                        break;
                }
            },

            //查询
            getData: function () {
                common.openloading('.zui-table-view');
                this.$http.post(tableInfo.ip + "/sjsb/queryBasj", JSON.stringify(this.param)).then(function (data) {
                    var json = data.body;
                    if (json.status == 200) {
                        tableInfo.jsonList = json.data.list;
                        tableInfo.totlePage= json.data.totalPage;
                    } else {
                        malert("查询失败：" + json.body.msg, "top", "defeadted");
                    }
                });
                common.closeLoading();
            },

            exportCSV: function(){
                common.openloading('.zui-table-view');
                this.$http.post(tableInfo.ip + "/sjsb/exportBasj", JSON.stringify(this.param)).then(function (data) {
                    common.closeLoading();
                    if(data.body.status == 200){
                        malert("导出成功");
                        window.location.href = tableInfo.ip + data.body.data;
                    } else {
                        malert(data.body.msg,'top','defeadted');
                    }
                },function (error) {
                    common.closeLoading();
                    console.log(error);
                });
            },

        }
    });
    //初始化页面需要加载的数据
    tableInfo.getData();
