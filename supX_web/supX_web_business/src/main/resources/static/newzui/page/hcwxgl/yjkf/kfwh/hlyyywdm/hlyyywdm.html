<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>人员维护</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script type="text/javascript">
        common.openloading('html')
    </script>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" v-cloak id="jyxm_icon">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5 icon-font14" @click="getData()">刷新</button>
        </div>
                <div class="flex-container flex-align-c">
                    <label class="whiteSpace ft-14 margin-r-1">检索</label>
                        <input class="zui-input wh180" autocomplete="off" placeholder="请输入关键字" type="text" id="jsvalue" v-model="param.parm" @input="getData()" />
                </div>
    </div>
   <div class="flex-container">
       <div class="zui-table-view padd-l-10 padd-r-10" >
           <div class="zui-table-header">
               <table class="zui-table table-width50">
                   <thead>
                   <tr>
                       <th class="cell-m">
                           <div class="zui-table-cell cell-m">
                               <span>序号</span>
                           </div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>药品名称</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>药物名称</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>药品内码</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>种类</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>功效</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>剂型</span></div>
                       </th>
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>类型</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>分装比例</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>库房单位</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>药房单位</span></div>-->
                       <!--</th>-->
                       <th>
                           <div class="zui-table-cell cell-s"><span>化学名称</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-xxl"><span>化学名代码</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>药品他名</span></div>
                       </th>
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>他名代码</span></div>-->
                       <!--</th>-->
                       <th>
                           <div class="zui-table-cell cell-s"><span>拼音简码</span></div>
                       </th>
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>统筹类别</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>产地</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-l"><span>基本剂量</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>剂量单位</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>建议最小剂量</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>建议最大剂量</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>给药途径</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>条形码</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>手工编码</span></div>-->
                       <!--</th>-->
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>汉语拼音</span></div>-->
                       <!--</th>-->
                   </tr>
                   </thead>
               </table>
           </div>
           <div class="zui-table-body" @scroll="scrollTable($event)">
               <table class="zui-table table-width50" v-if="YkbyPzdList.length!=0">
                   <tbody>
                   <!--style="position: absolute"  :style="{marginTop:heightL[$index]+'px'}"-->
                   <tr v-for="(item, $index) in YkbyPzdList" :tabindex="$index" class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                       @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" @click="checkSelect([$index,'some','YkbyPzdList'],$event)"
                       @dblclick="edit($index)" ref="list">
                       <td class="cell-m">
                           <div class="zui-table-cell cell-m" v-text="$index"></div>
                       </td>
                       <td class="cell-s">
                       <div class="zui-table-cell cell-s" v-text="item.ypmc"></div>
                       </td>

                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.ywid"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.ypbm"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.ypgg"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.zlbm"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.gxbm"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.jxbm"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.hxmc"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.hxmcdm"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.yptm"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.pydm"></div>
                       </td>
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                   </tr>
                   </tbody>
               </table>
               <p v-if="YkbyPzdList.length==0" class="  noData text-center zan-border">暂无数据...</p>
           </div>
           <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
       </div>
       <div class="zui-table-view padd-l-10 padd-r-10" >
           <div class="zui-table-header">
               <table class="zui-table table-width50">
                   <thead>
                   <tr>
                       <th class="cell-m">
                           <div class="zui-table-cell cell-m">
                               <span>序号</span>
                           </div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>药物ID</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>药物编码</span></div>
                       </th>
                       <th>
                           <div class="zui-table-cell cell-s"><span>药物名称</span></div>
                       </th>
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>别名</span></div>-->
                       <!--</th>-->
                       <th>
                           <div class="zui-table-cell cell-s"><span>药物快捷码</span></div>
                       </th>
                       <!--<th>-->
                           <!--<div class="zui-table-cell cell-s"><span>别名快捷码</span></div>-->
                       <!--</th>-->
                   </tr>
                   </thead>
               </table>
           </div>
           <div class="zui-table-body" @scroll="scrollTable($event)">
               <table class="zui-table table-width50" v-if="YwzdList.length!=0">
                   <tbody>
                   <!--style="position: absolute"  :style="{marginTop:heightL[$index]+'px'}"-->
                   <tr v-for="(item, $index) in YwzdList" :tabindex="$index" class="tableTr2" :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                       @mouseenter="switchIndex(true,hoverIndex1,$index)" @mouseleave="switchIndex()" @click="switchIndex(true,activeIndex1,$index)"
                       @dblclick="edit($index)" ref="list">
                       <td class="cell-m">
                           <div class="zui-table-cell cell-s" v-text="$index"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-m" v-text="item.ywid"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.ywbm"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.ywmc"></div>
                       </td>
                       <td class="cell-s">
                           <div class="zui-table-cell cell-s" v-text="item.ywkjm"></div>
                       </td>
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->
                           <!--<div class="zui-table-cell cell-s" v-text="item"></div>-->

                       </td>
                   </tr>
                   </tbody>
               </table>
               <p v-if="YwzdList.length==0" class="  noData text-center zan-border">暂无数据...</p>
           </div>
       </div>
   </div>
</div>

<script type="text/javascript" src="hlyyywdm.js"></script>
</body>

</html>
