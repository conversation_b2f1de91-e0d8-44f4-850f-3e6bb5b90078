var yndryb_008 = new Vue({
    el: '#yndryb_008',
    mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
    components: {
        'search-table': searchTable
    },
    data: {
        ip:'',
        userInfo: {},// 当前用户信息
        bxlbbm: '', // 保险类别编码
        bxurl: '',
        grxxJson: {}, // 个人信息对象
        cardParm: '', //无卡输入
        ybdjxx:{},  //医保登记信息
        yndryb_yllb_tran: {// 门诊医疗类别
            '11': '普通门诊',
            '12': '特殊病门诊',
            '13': '慢性病门诊',
            '14': '急诊抢救',
            '19': '门诊透析',
            '41': '血腹透门诊'
        },
        yndryb_jslb_tran: {// 结算类别
            '1': '正常结算',
            '2': '中途结算',
            '3': '卡挂失结算',
            '4': '自费结算'
        },
        yndryb_spxx_tran: [],// 结算类别
        yndryb_fs_tran: {
            '0': '无',
            '1': '存在医疗封锁'
        },
        yndryb_sex_tran:{
            '1':'男',
            '2':'女',
            '9':'未说明'
        },
        yndryb_rylb_tran: {
            '00':'其他',
            '1': '工人',
            '2': '农民',
            '3': '大学生',
            '4': '干部',
            '5': '国家公务员',
            '6': '参照公务员',
            '7': '现役军人',
            '71': '参战退役人员',
            '8': '无业人员',
            '9': '其他',
            '91': '成年非从业居民',
            '92': '全日制学生(非大学生)',
            '93': '未入校的未成年人'
        },
        jbContent: {}, // 疾病类容
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '疾病编码': 'aka120',
            '疾病名称': 'aka121',
            '拼音代码': 'aka066'
        },
        zdxxJson: {
            "jslx": "1",
        },
    },
    mounted: function () {
        // this.ip = 'http://localhost:9001';
        // this.init();

        // this.getUserIP(function(ip){
            this.ip = 'http://localhost:9001';
            // yndryb_008.init();
            // yndryb_008.getRightVueBrxx();
        // });
    },
    methods: {
        initData: function(){
            this.ip = 'http://localhost:9001';
            yndryb_008.init();
            yndryb_008.getRightVueBrxx();
        },

        init: function () {
            //调用云南东软医保初始化
            $.post(this.ip + "/yndryb/init", {}, function (json) {
                if(json.code == 0){
                    rightVue.yndrybInit = true;
                    malert("医保控件初始化成功!");
                }else{
                    rightVue.yndrybInit = true;
                    malert("医保控件初始化失败！" + json.msg,'top','defeadted');
                }
            });
        },

        getRightVueBrxx:function () {
            yndryb_008.grxxJson.yllb = "11";
            Vue.set(this.grxxJson,"hisywbh",rightVue.mzjbxxContent.ghxh);
            Vue.set(this.grxxJson,"ryzdbm",rightVue.mzjbxxContent.jbbm);
            Vue.set(this.grxxJson,"ryzdmc",rightVue.mzjbxxContent.jbmc);
            this.cardParm = rightVue.mzjbxxContent.sfzh;
            this.$forceUpdate();
        },

        // 获取当前浏览器客户端ip
        getUserIP:function(onNewIP) {
            var MyPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;
            var pc = new MyPeerConnection({
                iceServers: []
            });
            noop = function() {},
                localIPs = {};
            ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g;
            function iterateIP(ip) {
                if (!localIPs[ip]) onNewIP(ip);
                localIPs[ip] = true;
            }
            pc.createDataChannel('');
            pc.createOffer().then((sdp) => {
                sdp.sdp.split('\n').forEach(function (line) {
                    if (line.indexOf('candidate') < 0) return;
                    line.match(ipRegex).forEach(iterateIP);
                });
                pc.setLocalDescription(sdp, noop, noop);
            }).catch((reason) => {
            });
            pc.onicecandidate = (ice) => {
                if (!ice || !ice.candidate || !ice.candidate.candidate || !ice.candidate.candidate.match(ipRegex)) return;
                ice.candidate.candidate.match(ipRegex).forEach(iterateIP);
            };
        },

        // 获取操作员用户信息
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    this.userInfo = json.body.d;
                });
        },

        // 获取保险类别信息
        getbxlb: function () {
            var param = {bxjk: "008"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        yndryb_008.bxlbbm = json.d.list[0].bxlbbm;
                        yndryb_008.bxurl = json.d.list[0].url;
                    }
                } else {
                    malert("保险类别查询失败!" + json.c, 'top', 'defeadted')
                }
            });
        },

        // 读卡
        load: function () {
            if (rightVue.yndrybInit) {
                var inputData = '1||'; //卡类型|个人编号|身份证Or个人编号
                $.post(this.ip + "/yndryb/getEmpInfo", {inputData: inputData}, function (json) {
                    if(json.code == 0){
                        var ybxx = json.data;
                        yndryb_008.grxxJson = Object.assign(yndryb_008.grxxJson, ybxx);
                        malert("读卡成功!");
                        yndryb_008.queryYbdj();
                        yndryb_008.$forceUpdate();
                    }else{
                        malert("读卡失败！" + json.msg,'top','defeadted');
                    }
                });
            } else {
                malert("医保控件未初始化,请重新打开页面！", 'top', 'defeadted');
            }
        },

        resultChange: function (val) {
            console.log(val);
            var type = val[2][1];
            if (type == 'yllb'){
                yndryb_008.grxxJson.yllb = val[0];
                console.log("选择医疗类别:" + val[0]);
                //先清空审批编号
                yndryb_008.grxxJson.spbh = '';
                if (val[0] != 11){
                    console.log("查询审批信息");
                    yndryb_008.grxxJson.yllb = val[0];
                    var parm = {
                        'ybgrbh':yndryb_008.grxxJson.grbh,
                        'yllb':val[0]
                    };
                    $.post(this.ip + "/yndryb/querySpxx", {parm: JSON.stringify(parm)}, function (json) {
                        if(json.code == 0){
                            malert("查询审批信息成功!");
                            yndryb_008.yndryb_spxx_tran = json.data;

                            yndryb_008.$forceUpdate();
                        }else{
                            malert("查询审批信息失败！" + json.msg,'top','defeadted');
                        }
                    });
                }
            }else if(type == 'spbh'){
                yndryb_008.grxxJson.spbh = val[0];
                yndryb_008.grxxJson.ryzdbm = yndryb_008.yndryb_spxx_tran[val[5]].xmbh;
                yndryb_008.grxxJson.ryzdmc = yndryb_008.yndryb_spxx_tran[val[5]].xmmc;
            }else if(type == 'jslx'){
                yndryb_008.zdxxJson.jslx = val[0];
            }
            yndryb_008.$forceUpdate();
            console.log(yndryb_008.grxxJson);
        },

        // 无卡读卡
        loadSFZ: function () {
            if (rightVue.yndrybInit) {
                if (this.cardParm == null || this.cardParm == undefined || this.cardParm.length==0){
                    malert("请输入身份证号或个人编号！",'top','defeadted');
                    return
                }

                var inputData = '2||' + this.cardParm; //卡类型|个人编号|身份证Or个人编号
                $.post(this.ip + "/yndryb/getEmpInfo", {inputData: inputData}, function (json) {
                    if(json.code == 0){
                        yndryb_008.grxxJson = json.data;
                        malert("读卡成功!");
                        yndryb_008.queryYbdj();
                    }else{
                        malert("读卡失败！" + json.msg,'top','defeadted');
                    }
                });
            } else {
                malert("医保控件未初始化,请重新打开页面！", 'top', 'defeadted');
            }
        },

        //查询医保登记信息
        queryYbdj: function(){
            var parm = {
                ybgrbh: yndryb_008.grxxJson.grbh,
                hisywbh: rightVue.brxxContent.ghxh
            };
            $.post(this.ip + "/yndryb/queryYbdj", {parm: JSON.stringify(parm)}, function (json) {
                if(json.code == 0){
                    yndryb_008.ybdjxx = json.data;

                    yndryb_008.$forceUpdate();
                }else{
                    malert("查询登记信息失败！" + json.msg,'top','defeadted');
                }
            });
        },

        //引入
        enter: function () {
            console.log("czyxm->jbr:"+ this.userInfo.czyxm+ " " + rightVue.brxxContent.brxm);
            if (Object.keys(yndryb_008.grxxJson).length === 0) {
                malert("请先读卡", 'top', 'defeadted');
                return;
            }
            if (yndryb_008.grxxJson.grbh == null || yndryb_008.grxxJson.grbh === '' || yndryb_008.grxxJson.grbh === undefined) {
                malert("请先读卡", 'top', 'defeadted');
                return;
            }
            if (yndryb_008.grxxJson.yllb == null || yndryb_008.grxxJson.yllb === '' || yndryb_008.grxxJson.yllb === undefined) {
                malert("请选择医疗类别", 'top', 'defeadted');
                return;
            }
            if (yndryb_008.zdxxJson.jslx == null || yndryb_008.zdxxJson.jslx === '' || yndryb_008.zdxxJson.jslx === undefined) {
                malert("请选择结算类型", 'top', 'defeadted');
                return;
            }
            if (yndryb_008.grxxJson.xm != rightVue.fzContent.brxm){
                malert("病人信息与医保卡不符", 'top', 'defeadted');
                return;
            }

            //此处若未登记则需要先登记
            if (yndryb_008.ybdjxx == null){
                var parm = {
                    'ybgrbh':yndryb_008.grxxJson.grbh,
                    'hisywbh':rightVue.brxxContent.ghxh,
                    'yllb':yndryb_008.grxxJson.yllb,
                    'rylbmc': yndryb_008.grxxJson.rylbmc,
                    'jbr': this.userInfo.czyxm,
                    'ryzdmc':yndryb_008.grxxJson.ryzdmc,
                    'ryzdbm':yndryb_008.grxxJson.ryzdbm,
                    'ryks': rightVue.mzjbxxContent.ghksmc,
                    'xgspbh' : yndryb_008.grxxJson.spbh,
                    'ybkye':yndryb_008.grxxJson.zhye    //账户余额
                };
                //加入医保基本信息
                parm = Object.assign(parm, yndryb_008.grxxJson);
                $.post(this.ip + "/mzyw/ybdj", {parm: JSON.stringify(parm)}, function (json) {
                    if(json.code == 0){
                        malert("医保登记成功!");
                        yndryb_008.ybdjxx = json.data;

                        //个人信息
                        rightVue.yndrybContent = yndryb_008.grxxJson;
                        //门诊诊断信息
                        // rightVue.yndrybContent.jbbm = this.zdxxJson.jbbm;
                        //支付类别
                        // rightVue.yndrybContent.aka130 = this.zdxxJson.aka130;

                        //个人编号,用于结算各种判断
                        rightVue.yndrybContent.grbh = yndryb_008.grxxJson.grbh;
                        malert("引入成功！");
                        popTable.isShow = false;
                        yndryb_008.$forceUpdate();
                    }else{
                        malert("医保登记失败！" + json.msg,'top','defeadted');
                    }
                });
            }else {
                //更新就诊信息
                var jzxxParam = {
                    'ybgrbh':yndryb_008.grxxJson.grbh,
                    'djid':yndryb_008.ybdjxx.djid,
                    'hisywbh':rightVue.brxxContent.ghxh,
                    'yllb': yndryb_008.grxxJson.yllb,
                    'jbr': this.userInfo.czyxm,
                    'ryzdmc':yndryb_008.grxxJson.ryzdmc,
                    'ryzdbm':yndryb_008.grxxJson.ryzdbm,
                    'ryks': rightVue.mzjbxxContent.ghksmc,
                    'xgspbh' : yndryb_008.grxxJson.spbh,
                    'ybkye':yndryb_008.grxxJson.zhye    //账户余额
                };
                jzxxParam = Object.assign(jzxxParam, yndryb_008.grxxJson);
                $.post(this.ip + "/yndryb/updateJzxx", {parm: JSON.stringify(jzxxParam)}, function (json) {
                    if(json.code == 0){
                        console.log("更新就诊信息成功！" );
                    }else{
                        console.log("更新就诊信息失败！" );
                    }
                });

                //个人信息
                rightVue.yndrybContent = yndryb_008.grxxJson;
                //门诊诊断信息
                // rightVue.yndrybContent.jbbm = this.zdxxJson.jbbm;
                //支付类别
                // rightVue.yndrybContent.aka130 = this.zdxxJson.aka130;

                //个人编号,用于结算各种判断
                rightVue.yndrybContent.grbh = yndryb_008.grxxJson.grbh;
                malert("引入成功！");
                popTable.isShow = false;
            }

        },

        //门诊预结算
        mzyjs: function () {
            var result = "0";
            //同步操作
            $.ajaxSettings.async = false;
            //处理费用
            var fylist = [];
            var brfyList = [];
            for (var i = 0; i < rightVue.brfyjsonList.length; i++) {
                var fyparam = {};
                fyparam.mxfyxmbm = rightVue.brfyjsonList[i].mxfyxmbm;
                fyparam.yzlx = rightVue.brfyjsonList[i].yzlx;
                if (fyparam.yzlx == null) {
                    fyparam.yzlx = rightVue.brfyjsonList[i].yzfl;
                }
                fyparam.yzhm = rightVue.brfyjsonList[i].yzhm;
                fyparam.fysl = rightVue.brfyjsonList[i].fysl;
                fyparam.fydj = rightVue.brfyjsonList[i].fydj;
                fyparam.mzys = rightVue.brfyjsonList[i].mzys;
                fyparam.mzysxm = rightVue.brfyjsonList[i].mzysxm;
                fyparam.mzks = rightVue.brfyjsonList[i].mzks;
                fyparam.mzksmc = rightVue.brfyjsonList[i].mzksmc;
                fyparam.yzxh = rightVue.brfyjsonList[i].yzxh;
                fyparam.sqsj = rightVue.brfyjsonList[i].sqsj;
                brfyList.push(fyparam);
            }
            var param = {
                fylist: brfyList,
                userId: userId,
                bxlbbm: yndryb_008.bxlbbm,
                ybgrbh: yndryb_008.ybdjxx.ybgrbh,
                djlsh:  yndryb_008.ybdjxx.djid,
                ddhb : yndryb_008.ybdjxx.ddbh,
                xgspbh : yndryb_008.grxxJson.spbh
            };
            // 查询门诊费用
            $.post(this.ip + "/mzyw/queryMzfy", {parm: JSON.stringify(param)}, function (json) {
                if(json.code == 0){
                    fylist = json.data;
                }else{
                    malert("查询费用信息失败！" + json.msg,'top','defeadted');
                    return false;
                }
            });

            if(fylist==null || fylist==undefined || fylist=="" || fylist.length<=0){
                malert("没有可结算费用！");
                return false;
            }

            var yjsParam = {
                ybgrbh: yndryb_008.ybdjxx.ybgrbh,
                ddbh: yndryb_008.ybdjxx.ddbh,
                djh: yndryb_008.ybdjxx.djid,
                qfxje:yndryb_008.ybdjxx.qfx,
                hisywbh:rightVue.brxxContent.ghxh
            };

            $.post(this.ip + "/mzyw/mzyjs", {parm: JSON.stringify(yjsParam)}, function (json) {
                if(json.code == 0){
                    // fylist = json.data;
                    rightVue.yjsContentYndryb = json.data;
                    var tczf = 0;
                    var zhzf = 0;
                    var dblp = 0;
                    var zgrybz = 0;
                    var gwybz = 0;
                    var jfqbz = 0;
                    var yycd = 0;
                    if (typeof rightVue.yjsContentYndryb.tczf == 'string'){
                        tczf = rightVue.yjsContentYndryb.tczf;
                    }
                    if (typeof rightVue.yjsContentYndryb.zhzf == 'string'){
                        zhzf = rightVue.yjsContentYndryb.zhzf;
                    }
                    if (typeof rightVue.yjsContentYndryb.dblp == 'string'){
                        dblp = rightVue.yjsContentYndryb.dblp;
                    }
                    if (typeof rightVue.yjsContentYndryb.zgrybz == 'string'){
                        zgrybz = rightVue.yjsContentYndryb.zgrybz;
                    }
                    if (typeof rightVue.yjsContentYndryb.gwybz == 'string'){
                        gwybz = rightVue.yjsContentYndryb.gwybz;
                    }
                    if (typeof rightVue.yjsContentYndryb.jfqbz == 'string'){
                        jfqbz = rightVue.yjsContentYndryb.jfqbz;
                    }
                    if (typeof rightVue.yjsContentYndryb.yycd == 'string'){
                        yycd = rightVue.yjsContentYndryb.yycd;
                    }

                    rightVue.yjsContentYndryb.bxje = rightVue.fDec(
                        parseFloat(tczf) //统筹支付
                        + parseFloat(zhzf) //账户支付
                        // + parseFloat(xjzf) //现金支付
                        + parseFloat(dblp) //大病理赔
                        + parseFloat(zgrybz) //照顾人员补助
                        + parseFloat(gwybz) //公务员补助
                        + parseFloat(jfqbz) //解放前补助
                        + parseFloat(yycd), 2); //医院承担

                    rightVue.yjsContentYndryb.ybtczf = rightVue.fDec(
                        parseFloat(tczf) //统筹支付
                        //+ parseFloat(zhzf) //账户支付
                        // + parseFloat(xjzf) //现金支付
                        + parseFloat(dblp) //大病理赔
                        + parseFloat(zgrybz) //照顾人员补助
                        + parseFloat(gwybz) //公务员补助
                        + parseFloat(jfqbz) //解放前补助
                        + parseFloat(yycd), 2); //医院承担

                    rightVue.yndr_jylsh = json.data.jylsh;

                }else{
                    malert("预结算失败！" + json.msg,'right','defeadted');
                    result = "1";
                }
            });


            return result;
        },

        //门诊结算
        mzjs: function () {
            rightVue.zxlshSn = "";
            var param = {
                'ybgrbh': yndryb_008.ybdjxx.ybgrbh,
                'ddbh': yndryb_008.ybdjxx.ddbh,
                'djh': yndryb_008.ybdjxx.djid,
                'jslx': yndryb_008.zdxxJson.jslx,
                'jbr': this.userInfo.czyxm,
                // 'kzfje': '',
                'qfxzfje': yndryb_008.ybdjxx.qfx,
                'hisywbh':rightVue.brxxContent.ghxh
            };
            $.post(this.ip + "/mzyw/mzjs", {parm: JSON.stringify(param)}, function (json) {
                if (json.code == 0) {
                    rightVue.jsContentYndryb = json.data;
                    rightVue.zxlshSn = json.data.jylsh;
                    popCenter1.jssb = false;
                    malert(json.msg);

                    popCenter1.successSaveAndPrint();
                    common.closeLoading();
                } else {
                    popCenter1.jssb = true;
                    malert(json.msg, 'top', 'defeadted');
                }
            });
        },

        // 取消结算
        qxjs: function () {
            var parm = {
                ybgrbh: yndryb_008.ybdjxx.ybgrbh,
                ddbh: yndryb_008.ybdjxx.ddbh,
                djh: yndryb_008.ybdjxx.djid,
                jylsh: rightVue.jsContentYndryb.jylsh,
                fph: rightVue.jsContentYndryb.fph,
                jbr: this.userInfo.czyxm
            };

            $.post(this.ip + "/mzyw/qxjs", {parm:JSON.stringify(parm)}, function (json) {
                if (json.code == 0) {
                    malert(json.msg);
                } else {
                    malert(json.msg, 'top', 'defeadted');
                }
            });
        },

        //his结算异常时调用的反结算
        ybfjs: function(){
            var parm = {
                jylsh: rightVue.zxlshSn,
                jbr:this.userInfo.czyxm
            };
            $.post(this.ip + "/mzyw/ybfjs", {parm: JSON.stringify(parm)}, function (json) {
                if (json.code == 0){
                    console.log("HIS结算失败，已调用医保反结算！");
                } else {
                    malert(json.msg,'top','defeadted');
                }
            });
        },

        // 检索疾病编码
        searching: function (add, type, val) {
            console.log(val)
            this.jbContent['jbmc'] = val;
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.jbContent[type] == undefined || this.jbContent[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.jbContent[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};

            $.post(this.ip + "/baseData/queryYbJbml", {parm: JSON.stringify(str_param)},
                function (json) {
                    if (json.code == 0) {
                        var res = json.data;
                        if (add) {
                            for (var i = 0; i < res.list.length; i++) {
                                yndryb_008.searchCon.push(res.list[i]);
                            }
                        } else {
                            yndryb_008.searchCon = res.list;
                        }
                        yndryb_008.page.total = res.totalSize;
                        yndryb_008.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询疾病项目失败：" +json.msg, "top", "defeadted");
                    }
                });

            // $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + yndryb_008.bxurl + "&bxlbbm=" + yndryb_008.bxlbbm + "&types=baseData&method=queryJbml&parm="
            //     + JSON.stringify(str_param),
            //     function (data) {
            //         if (data.a == '0') {
            //             var res = JSON.parse(data.d);
            //             if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
            //                 for (var i = 0; i < res.list.length; i++) {
            //                     yndryb_008.searchCon.push(res[i]);
            //                 }
            //             } else {
            //                 yndryb_008.searchCon = res.list;
            //             }
            //             yndryb_008.page.total = res.totalSize;
            //             yndryb_008.selSearch = 0;
            //             if (res.list.length > 0 && !add) {
            //                 _searchEvent.show();
            //             }
            //         } else {
            //             malert("查询疾病项目失败：" + data.c, "top", "defeadted");
            //         }
            //     });
        },
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'jbmc');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.jbContent = item;
                Vue.set(this.jbContent, 'jbmc', this.jbContent['aka121']);
                yndryb_008.grxxJson.ryzdbm = this.jbContent.aka120;
                yndryb_008.grxxJson.ryzdmc = this.jbContent.aka121;
                $(".selectGroup").hide();
            }
        },
        changeDown: function (event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'jbContent', 'searchCon');
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(this.jbContent, 'jbmc', this.jbContent['aka121']);
                    yndryb_008.grxxJson.ryzdbm = this.jbContent.aka120;
                    yndryb_008.grxxJson.ryzdmc = this.jbContent.aka121;
                    this.selSearch = 0;
                    this.nextFocus(event);
                }
            }
        },
    },
});
yndryb_008.initData();
yndryb_008.getbxlb();
yndryb_008.getUserInfo();
