(function(){
    //单病种
    var tableInfo = new Vue({
        el: '#jyxm_icon',
        mixins: [dic_transform, baseFunc, tableBase, mformat,checkData],
        data:{
        	popContent: {}, //对象
            jsonList: [], //列表
            ksList: [] //科室下拉框集合
        },
        methods: {
        	//进入页面加载列表信息
            getData: function () {
            	this.param.sort='tjm';
            	if($("#dbzjsvalue").val()!=null&&$("#dbzjsvalue").val()!=''){
			        this.param.parm=$("#dbzjsvalue").val();
			    }else{
			        this.param.parm='';
			    }
        		$.getJSON("/actionDispatcher.do?reqUrl=New1BaglBmwhDbz&types=queryBaglJbbmDbz&parm="+JSON.stringify(this.param),function (json) {
        			tableInfo.totlePage = Math.ceil(json.d.total/tableInfo.param.rows);
        			tableInfo.jsonList = json.d.list;
        		});
            },
            //检索查询回车键
		    searchHc: function() {
		        if(window.event.keyCode == 13) {
		          	this.getData();
		        }
		    },
            //修改
            edit: function (num) {
                wap.dbzwhShow=true;
               wap.open();
               wap.title='编辑'
                //这里要拷贝值到popContent中，不能直接=
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            },

            //删除2018/07/12二次删除弹窗提示
            remove: function (index) {
                var dbzList = [];
                var dbz={};
                var removeUrl="/actionDispatcher.do?reqUrl=New1BaglBmwhDbz&types=delete&";
                if (index!=null) {

                    dbz.tjm = this.jsonList[index].tjm
                    dbzList.push(dbz);
                } else {
                    for(var i=0;i<this.isChecked.length;i++){
                        if(this.isChecked[i] == true){
                            dbz.tjm = this.jsonList[i].tjm
                            dbzList.push(dbz);
                        }
                    }
                }
                // if(dbzList.length == 0){
                //     malert("请选中您要删除的数据");
                //     return false;
                // }

                if (common.openConfirm("确认删除该条信息吗？", function () {
                        var json='{"list":'+JSON.stringify(dbzList)+'}';
                        tableInfo.$http.post(removeUrl,json).then( function (data) {
                            if(data.body.a == 0){
                                malert("删除成功")
                                tableInfo.getData();
                            } else {
                                malert("删除失败")
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                    return false;
                }
                //2018/07/12注释当前添加二次删除弹窗
                // var json='{"list":'+JSON.stringify(dbzList)+'}';
                // this.$http.post(removeUrl,json).then( function (data) {
                //     this.getData();
                //     if(data.body.a == 0){
                //         malert("删除成功")
                //     } else {
                //         malert("删除失败")
                //     }
                // }, function (error) {
                //     console.log(error);
                // });
            },
            //新增清空编辑区
            addData: function(){
                wap.dbzwhShow=true;
                wap.open();
                wap.title='新增'
            	wap.popContent={};
            },
            //说明
            Tbsm:function () {
                wap.dbzwhShow=false;
                wap.open();
                wap.title='特别说明'
            },
            //下拉框加载科室
            GetKsData: function () {
            	this.param.rows=20000;
            	this.param.sort="";
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg="+JSON.stringify(this.param), function (json) {
                    if (json.a == 0) {
                        wap.ksList = json.d.list;
                    } else {
                        malert("科室列表查询失败"+json.c);
                        return;
                    }
                });
            },
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            nums:1,
            title:'',
            dbzwhShow:false,
            ksList:[],
            popContent:{}

        },
        methods:{
            //关闭
            closes:function () {
                // brzcList.hzShow=false;
                wap.nums=1;
            },
            open: function () {
                wap.nums=0;
            },

            //保存值域类别
            saveData: function () {

                // 提交前验证数据（主要是非空）
                if(wap.popContent.tjm=='' || wap.popContent.tjm==null || wap.popContent.tjm==undefined){
                    malert('统计码不能为空','top','defeadted');
                    return false;
                }if(wap.popContent.bzmc=='' || wap.popContent.bzmc==null || wap.popContent.bzmc==undefined){
                    malert('病种名称不能为空','top','defeadted');
                    return false;
                }
                var json=JSON.stringify(wap.popContent);
                this.$http.post("/actionDispatcher.do?reqUrl=New1BaglBmwhDbz&types=save&",json).then(function (data) {
                    if(data.body.a == 0){
                        tableInfo.getData();
                        if(wap.title=='编辑'){
                            wap.closes();
                            malert("保存成功");
                            return;
                        }if(wap.title=='新增'){
                            malert("新增成功");
                        }
                        this.popContent = {};
                    } else {
                        malert("上传数据失败");
                    }
                },function (error) {
                    console.log(error);
                });

            },
        }


    });

    tableInfo.getData();
    tableInfo.GetKsData();


    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function () {
        if ($(this).val() == '' || $(this).val() == null) {
            $(this).addClass("emptyError");
        } else {
            $(this).removeClass("emptyError");
        }
    });

    //为table循环添加拖拉的div
    var drawWidthNum = $(".patientTable tr").eq(0).find("th").length;
    for(var i=0;i<drawWidthNum;i++){
        if(i>=2){
            $(".patientTable th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
        }
    }
})();
