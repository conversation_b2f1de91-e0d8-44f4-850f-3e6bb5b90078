var blsjsb=new Vue({
    el:'.blsjsb',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        checked:false,
        nbtclb:'',
        df:{
            title:'纳入持续改进项目'
        },
    },
    created:function(){

    },
    mounted:function(){

    },
    methods:{
        gjxm:function () {
            common.openConfirm('<p>确认将患者：<span class="color-green">李浩然</span>的不良事件报告纳入<br/>持续改进项目吗？</p>',function () {

            },function () {

            },this.df)
        }
    },
});