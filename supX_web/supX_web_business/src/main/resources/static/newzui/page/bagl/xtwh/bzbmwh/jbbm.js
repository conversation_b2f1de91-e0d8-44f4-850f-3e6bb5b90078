(function(){
    //疾病编码
    var tableInfo = new Vue({
        el: '#jbbm',
        mixins: [dic_transform, baseFunc, tableBase, mformat,checkData],
        data:{
        	popContent: {},
            jsonList: [],
            totlePage:''
        },
        methods: {
        	//进入页面加载列表信息
            getData: function () {
            	this.param.rows=10;
            	this.param.sort='jbmb';
            	if($("#jbbmjsvalue").val()!=null&&$("#jbbmjsvalue").val()!=''){
			        this.param.parm=$("#jbbmjsvalue").val();
			    }else{
			        this.param.parm='';
			    }
                tableInfo.jsonList=[];
        		$.getJSON("/actionDispatcher.do?reqUrl=New1BaglBmwhJbbm&types=queryBaglJbbm&parm="+JSON.stringify(this.param),function (json) {
        			tableInfo.totlePage = Math.ceil(json.d.total/tableInfo.param.rows);
        			tableInfo.jsonList = json.d.list;
        		});
            },
            //检索查询回车键  
		    searchHc: function() {
		        if(window.event.keyCode == 13) {
                    tableInfo.getData();
		        }
		    },

            
            //修改值域类别
            edit: function (num) {
               wap.open();
               wap.title='编辑疾病编码'
                //这里要拷贝值到popContent中，不能直接=
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            },
            
            //删除值域类别
            remove: function () {
                var jbbmList = [];
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                    	var jbbm={};
                    	var removeUrl="/actionDispatcher.do?reqUrl=New1BaglBmwhJbbm&types=delete&";
                    	jbbm.jbmb = this.jsonList[i].jbmb;
                    	jbbmList.push(jbbm);
                    }
                }
                if(jbbmList.length == 0){
                    malert("请选中您要删除的数据");
                    return false;
                }
                var json='{"list":'+JSON.stringify(jbbmList)+'}';
                this.$http.post(removeUrl,json).then( function (data) {
                    this.getData();
                    if(data.body.a == 0){
                        malert("删除成功")
                    } else {
                        malert("删除失败")
                    }
                }, function (error) {
                    console.log(error);
                });
            },
            //新增清空编辑区
            addData: function(){
                wap.open();
                wap.title='新增疾病编码';
            	wap.popContent={};
            }
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            nums:1,
            title:'',
            popContent:{}

        },
        methods:{
            //关闭
            closes:function () {
                // brzcList.hzShow=false;
                wap.nums=1;
            },
            open: function () {
                wap.nums=0;
            },

            //保存值域类别
            saveData: function () {
                // 提交前验证数据（主要是非空）
                if(wap.popContent.jbmb=='' || wap.popContent.jbmb==null || wap.popContent.jbmb==undefined){
                 malert('疾病编码不能为空','top','defeadted');
                 return false;
                }if(wap.popContent.jbmc=='' || wap.popContent.jbmc==null || wap.popContent.jbmc==undefined){
                    malert('疾病名称不能为空','top','defeadted');
                    return false;
                }
                var json=JSON.stringify(wap.popContent);
                this.$http.post("/actionDispatcher.do?reqUrl=New1BaglBmwhJbbm&types=save&",json).then(function (data) {
                    if(data.body.a == 0){
                        tableInfo.getData();
                        if(wap.title=='编辑疾病编码'){
                            wap.closes();
                            malert("保存成功");
                            return;
                        }if(wap.title=='新增疾病编码'){
                            malert("新增成功");
                        }
                        this.popContent = {};
                    } else {
                        malert("上传数据失败");
                    }
                },function (error) {
                    console.log(error);
                });

            },
        }


    });
    tableInfo.getData();
    
    
    //验证是否为空
    $('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function () {
        if ($(this).val() == '' || $(this).val() == null) {
            $(this).addClass("emptyError");
        } else {
            $(this).removeClass("emptyError");
        }
    });

})();

