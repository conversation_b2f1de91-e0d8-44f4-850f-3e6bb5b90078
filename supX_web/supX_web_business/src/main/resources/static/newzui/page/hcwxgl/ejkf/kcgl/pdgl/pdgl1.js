    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShowkd:true,
            isShow:false,
            addShow:false,
            pdWay:'0',
            pdbSelected:{},
            pzList:[],
            pzNum: 0,
            title:'',

            them_tran: {},
            json: {
                'jjzj': 0,
                'ljzj': 0
            },
            fyContent: {},
            ypmcInput: null,
            //是否禁止保存
            //用例编码
            ylbm: 'N040030020022006',
            //权限科室编码
            qxksbm: '',
            //判断方式选择
            popContent: {
                pdWay: 1,
                ypzl: 0,
            },
            //已选二级库房
            yfSelected: 0,

            //盘点方式显示
            pdWayShow: {
                zlName: false,
                ypName: false,
            },
            //二级库房列表
            YFSelect: [],
            //材料种类列表
            YPZLSelect: [],
            //是否存在未审核盘点表
            hasWhpdb: '',
            jsonList: [],
            //库房列表
            //参数属性
            csParm: {},
            param: {},
            //是否禁止提交
            isSubmited: false,
            ypjs: null,
            //凭证列表
            pzhList: [],
            //凭证号默认选项
            pdb: 0,
            //是否是新增盘点材料
            isAddNew: false,
            //盘点单
            //显示材料种类下拉框
            zdrq: getTodayDateTime(), //获取制单日期
            pdbContent:{},
            KFList:[],
            infoList:[],
            num:0,
            //分页信息
            dg: {
                page: 1,
                rows: 2000,
                sort: "",
                order: "asc",
            },
            //显示材料种类下拉框
            ypzlShow: false,
            ypmcShow: false,
            //盘点方式
            selectPdfs: '0',

            them: {
                '材料编号': 'ypbm',
                '材料名称': 'ypmc',
                '规格': 'ypgg',
                '分装比例': 'fzbl',
                '进价': 'ypjj',
                '零价': 'yplj',
                '库房单位': 'kfdwmc',
                '二级库房单位': 'yfdwmc',
                '二级库房种类': 'ypzlmc',
                '材料剂型': 'jxmc'
            },
            //盘点方式下拉框
            options: [{
                text: '全部材料',
                pdWay: '0'
            },
                {
                    text: '种类材料',
                    pdWay: '1'
                },
                {
                    text: '单种材料',
                    pdWay: '2'
                },
            ],

            pddhShow : false,
            pdlrpzh:'',
            pddhList:[],//盘点单号
            search:'',
        },
        mounted: function() {
        	this.pdbContent.pdWay='0';
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
            laydate.render({
                elem: '#timeVal'
                , eventElem: '.zui-date'
                , trigger: 'click',
                type: 'datetime',
                value:this.param.beginrq
                , theme: '#1ab394'
                , done: function (value, data) {
                    wrapper.param.beginrq = value;
                }
            });
            laydate.render({
                elem: '#timeVal1'
                , eventElem: '.zui-date'
                , trigger: 'click',
                    type: 'datetime'
                , theme: '#1ab394',
                value:this.param.endrq
                , done: function (value, data) {
                    wrapper.param.endrq =value;
                }
            });
    	},
    	watch:{
    		'search' :function(){
    			pdList.getData();
    		}
    	},
        methods:{

        	resultChangeYf:function(val){
        		   if (val[2].length > 1) {
                       if (Array.isArray(this[val[2][0]])) {
                           Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                           pdList.getData();
                       } else {
                           Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                           if (val[3] != null) {
                               Vue.set(this[val[2][0]], val[3], val[4]);
                           }
                           pdList.getData();
                       }
                   } else {
                       this[val[2][0]] = val[0];
                       pdList.getData();
                   }
                   if (val[1] != null) {
                       this.nextFocus(val[1]);
                   }
        	},

        	resultChangeYpzl:function(val){
        		   if (val[2].length > 1) {
                       if (Array.isArray(this[val[2][0]])) {
                           Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                       } else {
                           Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                           if (val[3] != null) {
                               Vue.set(this[val[2][0]], val[3], val[4]);
                           }
                       }
                   } else {
                       this[val[2][0]] = val[0];
                   }
                   if (val[1] != null) {
                       this.nextFocus(val[1]);
                   }
        	},
            kd:function (index) {
                this.num=index;
                pdList.loadNum();
                switch (wrapper.num){
                    case 0:
                    	pdList.pdsclist=[];
                        this.isShowkd=false;
                        this.isShowpopL=true;
                        pdList.isShowpopL=true;
                        this.isShow=true;
                        pdList.isShow=true;
                        pdList.isShowkd=false;
                        break;
                    case 1:
                        wrapper.add();
                        break;
                }

            },

            del: function () {
                //删除已选择的记录
                pdList.jsonList.splice(pdList.rowNum, 1);
                malert('已删除','top','defeadted')
            },
            //清空盘点表生成页面
            clearAll: function () {
                pdList.jsonList = [];
                wrapper.selectPdfs = '0';
                wrapper.ypzlShow = false;
                wrapper.ypmcShow = false;
            },
            AddMel:function () {
                wap.open();
                wap.title='添加材料';
            },
            sx:function () {
                pdList.getData();
                pdList.pdsclist=[];
            },
            //获取库房信息
            getKFData: function () {
                var json = {
                    'qrzfbz': 0,
                };
                //加载二级库房列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
                    function (data) {
                        wrapper.YFSelect = data.d.list;
                    });
            //加载材料种类列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl',
                    function(data) {
                        wrapper.YPZLSelect = data.d.list;
                    });
            },

            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
            	console.log(val);
                var isTwo = false;
                //准备参数
                var bean = {
                    "yfbm": wrapper.pdbContent.yfbm
                };
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "pdWay":
                        if(val[0]==0){
                          this.pdWayShow.zlName = false;
                          this.pdWayShow.ypName = false;
                        }else if(val[0]==1){
                                    this.pdWayShow.zlName =true;
                                    this.pdWayShow.ypName =  false;
                        }else if(val[0]==2){
                                    this.pdWayShow.zlName = false;
                                    this.pdWayShow.ypName =true ;
                        }
                        Vue.set(this.pdbContent, 'pdWay', val[0]);
                        Vue.set(this.pdbContent, 'text', val[4]);
                        break;
                    case "yfbm":
                        Vue.set(this.pdbContent, 'yfbm', val[0]);
                        Vue.set(this.pdbContent, 'yfmc', val[4]);
                        break;
                    case "ypzlbm":
                        Vue.set(this.pdbContent, 'ypzlbm', val[0]);
                        Vue.set(this.pdbContent, 'ypzlmc', val[4]);
                        break;
                    case "pzNum":
                        Vue.set(this.pdbContent, 'pzNum', val[0]);
                        Vue.set(this.pdbContent, 'pdpzh', val[4]);
                        break;
                    case "ksbm":
                        var parm = {
                            'kfbm': wrapper.pdbContent.kfbm,
                            'qrzfbz': 0
                        };
                        //查询盘点表
                        $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=pdbList&parm=' + JSON.stringify(parm), function (json) {
                            if (json != null && json.a == 0) {
                                var str = '';
                                if (json.d.length > 0) {
                                    for (var i = 0; i < json.d.length; i++) {
                                        str += ('【' + json.d[i].pdpzh + '】 ');
                                        wrapper.hasWhpdb = '盘点表' + str + '未审核';
                                    }
                                } else {
                                    wrapper.hasWhpdb = '';
                                }
                            } else {
                                malert('查询未审核盘点表失败！','top','defeadted')
                            }
                        });
                        break;
                    case "pdlrpzh":
                    	Vue.set(wrapper,'pdlrpzh',val[0]);
                    	var json = {
                    			pdpzh : wrapper.pdbContent.pdpzh,
                    			pdlrpzh : val[0],
                    			qrzfbz : 0
                    	};
                    	 $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdlrmx&json=' + JSON.stringify(json), function (json) {
                             if (json != null && json.a == 0) {
                            	 pdList.pdsclist = json.d;
                             }
                         });
                    	break;
                    default:
                        break;
                }
            },


            //盘点类型改变
            selChange: function (val) {
                //库房非空判断
                if (this.popContent.pdWay === 0 || this.popContent.pdWay === 1) {
                    malert("请先选择二级库房！",'top','defeadted');
                    //清空盘点方式
                    val.currentTarget.value = 0;
                    return;
                }

                //根据盘点方式显示子菜单
                if (val.currentTarget.value == 1) {
                    //盘点方式赋值
                    wrapper.pdbContent.pdfs = 1;
                    wrapper.ypzlShow = true;
                    wrapper.ypmcShow = false;
                } else if (val.currentTarget.value == 2) {
                    //盘点方式赋值
                    wrapper.pdbContent.pdfs = 2;
                    wrapper.ypzlShow = false;
                    wrapper.ypmcShow = true;
                } else {
                    //盘点方式赋值
                    wrapper.pdbContent.pdfs = 0;
                    wrapper.ypzlShow = false;
                    wrapper.ypmcShow = false;
                }
                //准备参数
                var bean = {
                    "yfbm": wrapper.yfbm
                };
                //根据盘点方式不同，生成相应的字菜单
                if (wrapper.selectPdfs == 1) {
                    //获取材料种类。参数：分页信息/查询类型/查询参数/错误信息
                    wrapper.getInfo(pdList.dg, 'ypzl', bean, "材料种类获取失败！");

                } else if (wrapper.selectPdfs == 2) {
                    //查询单个材料库存。参数：分页信息/查询类型/查询参数/错误信息
                    wrapper.getInfo(pdList.dg, 'ypzd', bean, "材料信息获取失败！");
                }
            },


            //获取科室权限
            getCsqx: function () {
                var parm = {
                    "ksbm": wrapper.qxksbm,
                    "ylbm":  this.ylbm
                };
                //获取科室权限
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                    if(json.a == 0) {
                        if(json.d.length > 0) {
                            for(var i = 0; i < json.d.length; i++) {
                                var csjson = json.d[i];
                            }
                        }

                    } else {
                        malert('参数权限获取失败'+json.c,'top','defeadted')
                    }
                });

            },


            //库房改变后重新获取权限科室编码，保存盘点表后刷新库房列表也可触发此方法
            opChange: function (event, type) {
                if (type == 'ksbm') {
                    //设置库房编码
                    document.getElementById('_kfbm').value = wrapper.pdbContent.kfbm;
                    var obj = event.currentTarget;
                    var selected = $(obj).find("option:selected");
                    qxksbm = selected.attr(type) == undefined ? qxksbm : selected.attr(type); //获取科室编码
                    wrapper.getCsqx(); //选中库房之后再次请求获取参数权限

                    //判断是否存在未审核的盘点表
                    var parm = {
                        'kfbm': wrapper.pdbContent.kfbm,
                        'qrzfbz': 0
                    };
                    //查询盘点表
                    $.getJSON('/actionDispatcher.do?reqUrl=YkglKfywPdb&types=pdbList&parm=' + JSON.stringify(parm), function (json) {
                        if (json != null && json.a == 0) {
                            var str = '';
                            if (json.d.length > 0) {
                                for (var i = 0; i < json.d.length; i++) {
                                    str += ('【' + json.d[i].pdpzh + '】 ');
                                    wrapper.hasWhpdb = '盘点表' + str + '未审核';
                                }
                            } else {
                                wrapper.hasWhpdb = '';
                            }
                        } else {
                            malert('查询未审核盘点表失败！','top','defeadted')
                        }
                    });
                    //判断是否存在未审核的盘点表end

                } else if (type == 'zlbm') {
                    //获取材料种类编码
                    wrapper.pdbContent.zlbm = event.currentTarget.value;
                } else if (type == 'ypbm') {
                    //获取材料编码
                    wrapper.pdbContent.ypbm = event.currentTarget.value;
                }
            },
            //判断是否有操作权限
            hasCx: function (cx) {
                if (!cx) {
                    malert("用户没有操作权限！",'top','defeadted');
                    return true;
                }
            },

            //生成盘点表
            add: function () {
                if(wrapper.pdbContent.yfbm == null||wrapper.pdbContent.yfbm == undefined) {
                    malert('请选择二级库房！','top','defeadted');
                    return;
                }
                //清空库存明细列表
                pdList.jsonList = [];
                //库房非空判断
                var dg = {
                    'page': 1,
                    'rows': 65536,
                    'sort': "",
                    'order': "asc",
                    'parm': ''
                };
                //准备参数
                //设置参数
                var json = {
                    'yfbm': wrapper.pdbContent.yfbm,
                };
                if(wrapper.popContent.pdWay === 2) {
                    json.ypzlbm = wrapper.popContent.ypzl.ypzlbm;
                } else if(wrapper.popContent.pdWay === 3) {
                    json.ypbm = wrapper.popContent.ypbm;
                }
                //获取库存明细列表
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=makePdb' + '&dg=' + JSON.stringify(this.dg) +
                    "&json=" + JSON.stringify(json),
                    function (json) {
                	if(json.a == 0 ){
                		 if (json.d.length > 0) {
                             for (var i = 0; i < json.d.length; i++) {
                                 //计算零价金额
                                 json.d[i].ljje = Math.round(json.d[i].yplj * json.d[i].kcsl * 100) / 100;
                                 //设置实存数量
                                 json.d[i].scsl = 0;
                             }
                             pdList.pdsclist = json.d;
                         } else {
                             malert("无相关材料信息！",'top','defeadted');

                         }
                	}else{
                		malert(json.c,'top','defeadted');
                	}

                    });
            },

            //自动生成
            autoGen: function () {
                //非空判断
                if(this.yfSelected == 0) {
                    malert('请选择二级库房！','top','defeadted');
                    return;
                }
                var dg = {
                    'page': 1,
                    'rows': 65536,
                    'sort': "",
                    'order': "asc",
                    'parm': ''
                };
                //获取凭证号对应的库存明细列表
                //准备参数
                // this.param.pdpzh = wrapper.pdbContent.pdpzh;
                //设置参数
                var json = {
                    'yfbm': wrapper.pdbContent.yfbm,
                };
                if(wrapper.popContent.pdWay === 2) {
                    json.ypzlbm = wrapper.popContent.ypzl.ypzlbm;
                } else if(wrapper.popContent.pdWay === 3) {
                    json.ypbm = wrapper.popContent.ypbm;
                }
                //查询盘点表明细
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=makePdb&dg=' + JSON.stringify(dg) + '&json=' +
                    JSON.stringify(json),function (data) {
                    if(data.a == 0) {
                        if(data.d.length == 0) {
                            malert('尚无此药','top','defeadted');
                        }
                        //计算总计
                        var jjzj = 0.0;
                        var ljzj = 0.0;
                        for(var i = 0; i < data.d.length; i++) {
                            jjzj += data.d[i]['ypjjje'];
                            ljzj += data.d[i]['ypljje'];
                        }

                        pdList.json.jjzj = jjzj.toFixed(2);
                        pdList.json.ljzj = ljzj.toFixed(2);
                        pdList.jsonList = data.d;
                    } else {
                        alert(data.c);
                    }

                });
            },

            //获取二级库房权限
            getYFData: function() {
                //获取二级库房列表
                var parm = {
                    "ylbm": this.ylbm,
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(parm),
                    function(data) {
                        if(data.a == 0) {
                            wrapper.yfList = data.d.list;
                            if(data.d.list.length > 0) {
                                pdList.qxksbm = data.d.list[0].ksbm;
                                pdList.getCsqx(); //加载完库房再次加载参数权限
                            }
                        } else {
                            malert("二级库房获取失败",'top','defeadted');
                        }
                    });

            },
            //盘点类型改变时获取响应的子菜单信息,参数：分页信息/查询类型/查询参数/错误信息
            getInfo: function (dg, type, bean, errInfo) {

                this.dg=wrapper.dg;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=' + type + '&dg=' + JSON.stringify(wrapper.dg) +
                    "&json=" + JSON.stringify(bean),
                    function (json) {
                        if (json != null || json.d.list.length > 0) {
                            wrapper.infoList = json.d.list;
                        } else {
                            malert(errInfo,'top','defeadted');

                        }
                    });

            },

        }
    });
    var pdList = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, baseFunc, printer, mformat],
        data: {
        	pdlrList:[],
        	pdsclist:[],
            //盘点表列表
            jsonList: [],
            pdbContent:{},
            pdbSelected:{},
            isShowpopL:false,
            isTabelShow:false,
            isShowkd:true,
            addShow:false,
            isShow:false,
            shShow:false,//审核
            zhuangtai: {
                "0":"待审核",
                "1": "已审核",
                "2": "作废",
            },
            isSubmited: false,
            pdfinish: false,


            pdb:{},
            totlePage : 0,
            param :{
            	page : 1,
            	rows : 10,
            }
        },
        mounted: function() {
        	this.pdbContent.pdWay='0';

    	},
        updated:function () {
            changeWin()
        },
        methods: {
        	//作废盘点表
        	zfpd:function(){
                common.openloading('.zui-table-view');
        		//准备参数
        		//wrapper.pdbSelected.qrzfbz = '2';
        		var pdb = JSON.parse(JSON.stringify(pdList.pdb));
        		Vue.set(pdb,'qrzfbz','2');
    			var json = {
    				'list': {
    					'pdb': pdb,
    				}
    			};

    			//发送请求保存数据
    			this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
    					JSON.stringify(json))
    				.then(function(data) {
    					if(data.body.a == 0) {
    						this.pdlrList = [];
    						malert("作废成功",'top','success');
    						pdList.cancel();
    					} else {
    						malert(data.c,'top','defeadted');
    					}
    				}, function(error) {
    					console.log(error);
    				});
                common.closeLoading()
        	},
        	//作废
        	zf:function(index){
        		Vue.set(pdList.jsonList[index],'qrzfbz','2');
        		var json = {
        				list :{
        					pdb : pdList.jsonList[index]
        				}
        		};
        		//发送请求保存数据
    			this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
    					JSON.stringify(json))
    				.then(function(data) {
    					if(data.body.a == 0) {
    						this.pdlrList = [];
    						malert("作废成功",'top','success');
    						pdList.cancel();
    					} else {
    						malert(data.c,'top','defeadted');
    					}
    				}, function(error) {
    					console.log(error);
    				});
        	},
        	//盘点完成
        	finish:function(index,pzh){
        		//
        		$("#pdsh").prev().prev().hide();
                wrapper.addShow=true;
                wrapper.isShowkd=false;
                wrapper.isShow=false;
                wrapper.pddhShow = false;
                pdList.isShowkd=false;
                pdList.isShow=false;
                pdList.addShow=true;
                pdList.shShow = false;
                pdList.pdfinish = true;
                wrapper.pzNum=pzh;
                //
                // wrapper.addShow=true;
                // wrapper.isShowkd=false;
                // wrapper.isShow=false;
                // wrapper.pddhShow = false;
                // pdList.isShowkd=false;
                // pdList.isShow=false;
                // pdList.addShow=true;
                // pdList.shShow = false;
                // wrapper.shShow = false;
        		pdList.pdb = pdList.jsonList[index];
        		var json = {
        				pdpzh : pzh,
        				qrzfbz : 1
        		};
        		pdList.pdlrList = [];
        		$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdlrmx&json=' + JSON.stringify(json), function (json) {
        			if(json.a ==0){
        				console.log(json.d);
        				pdList.pdlrList = json.d;
        			}
        		});
        	},
        	//盘点完成审核
        	pdwc: function(){
        		var json = {
        				list:{
        					pdb : pdList.pdb
        				}
        		};
        		this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
    					JSON.stringify(json))
    				.then(function(data) {
    					if(data.body.a == 0) {
    						pdList.pdb = {};
    						malert("盘点完成",'top','success');
    						pdList.cancel();
    					} else {
    						malert(data.body.c,'top','defeadted');
    					}
    				})

        	},
        	//删除
        	scmx:function(index,type){
        		type.splice(index,1);
        	},
        	//审核录入盘点
        	shlrpd: function(){
                common.openloading('.zui-table-view');
        		Vue.set(pdList.pdb,'pdlrpzh',wrapper.pdlrpzh);
        		Vue.set(pdList.pdb,'qrzfbz','1');
        		var json = {
        				list:{
        					pdblr : pdList.pdb,
        					pdblrmx : null
        				}
        		};

        		//发送请求保存数据
    			this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePdlr',
    					JSON.stringify(json))
    				.then(function(data) {
    					if(data.body.a == 0) {
    						Vue.set(json.list,'pdblrmx',pdList.pdlrList);
    		        		//发送请求保存数据
    		    			this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=updateScsl',
    		    					JSON.stringify(json))
    		    				.then(function(data) {
    		    					if(data.body.a == 0) {
    		    						this.pdlrList = [];
    		    						malert("保存成功",'top','success');
    		    						pdList.cancel();
    		    					} else {
    		    						malert(data.body.c,'top','defeadted');
    		    					}
    		    					//是否禁止提交
    		    					this.isSubmited = false;
    		    				}, function(error) {
    		    					console.log(error);
    		    					//是否禁止提交
    		    					this.isSubmited = false;
    		    				});
    					} else {
    						malert(data.body.c,'top','defeadted');
    					}
    					//是否禁止提交
    					this.isSubmited = false;
    				}, function(error) {
    					console.log(error);
    					//是否禁止提交
    					this.isSubmited = false;
    				});
                common.closeLoading()
        		},
        	//提交录入
        	savelr:function(){
        		//是否禁止提交
    			if(this.isSubmited) {
    				malert('数据提交中，请稍候！','top','defeadted');
    				return;
    			}
    			//非空判断
    			if(pdList.pdlrList.length == 0) {
    				malert('没有数据可以提交','top','defeadted');
    				return;
    			}
    			//准备参数
    			for(var i = 0; i < pdList.pdlrList.length; i++) {
    				pdList.pdlrList[i].pdbmxid = pdList.pdlrList[i].pdbid;
    				pdList.pdlrList[i].lrsl =pdList.pdlrList[i].scsl;
    			}
    			//是否禁止提交
    			this.isSubmited = true;
    			var json = {
    				'list': {
    					'pdblr': {
    						pdpzh : wrapper.pzNum,
    						qrzfbz : '0',
    						yfbm : wrapper.pdbContent.yfbm
    						},
    					'pdblrmx': pdList.pdlrList,
    				}
    			};
    			//发送请求保存数据
    			this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePdlr',
    					JSON.stringify(json))
    				.then(function(data) {
    					if(data.body.a == 0) {
    						this.pdlrList = [];
    						malert("保存成功",'top','success');
    						pdList.cancel();
    					} else {
    						malert(data.body.c,'top','defeadted');
    					}
    					//是否禁止提交
    					this.isSubmited = false;
    				}, function(error) {
    					console.log(error);
    					//是否禁止提交
    					this.isSubmited = false;
    				});
        		},
        	//保存生成盘点表
        	savePdsc:function(){
        		//是否禁止保存
    			if(this.isSubmited) {
    				malert('数据提交中，请稍候！','top','defeadted');
    				return;
    			}
    			if(this.pdsclist.length == 0) {
    				malert('没有可以提交的数据！','top','defeadted');
    				return;
    			}
    			//准备参数
    			var pdb = {
    				'yfbm': wrapper.pdbContent.yfbm,
    				'qrzfbz': '0',
    			};

    			var json = {
    				'list': {
    					'pdb': pdb,
    					'pdbmx': this.pdsclist,
    				},
    			};
    			//是否禁止保存
    			this.isSubmited = true;

    			//发送请求保存数据
    			this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
    					JSON.stringify(json))
    				.then(function(data) {
    					if(data.body.a == 0) {
    						this.pdsclist = [];
    						pdList.cancel();
    						malert("提交成功",'top','success');
    					} else {
    						malert(data.body.c,'top','defeadted');
    					}
    					//是否禁止保存
    					this.isSubmited = false;
    				}, function(error) {
    					console.log(error);
    					//是否禁止保存
    					this.isSubmited = false;
    				});
        	},




            cancel:function () {
                wrapper.isShowkd=true;
                wrapper.isShowpopL=false;
                wrapper.addShow=false;
                pdList.isShowpopL=false;
                wrapper.isShow=false;
                pdList.isShow=false;
                pdList.pdfinish=false;
                pdList.shShow=false;
                wrapper.shShow=false;
                pdList.isShowkd=true;
                pdList.addShow=false;
                pdList.getData();
            },



            loadNum: function () {
                this.num = wrapper.num;
            },
            //获取盘点表列表
            getData: function () {
                common.openloading('.zui-table-view');
                this.dateBegin = wrapper.param.beginrq;
                this.dateEnd = wrapper.param.endrq;
                if(this.dateBegin == undefined || this.dateBegin == null || this.dateBegin == "") {
                    this.dateBegin = getTodayDateBegin();
                }
                if(this.dateEnd == undefined || this.dateEnd == null || this.dateEnd == "") {
                    this.dateEnd = getTodayDateEnd();
                }
                var parm = {
                    beginrq: this.dateBegin,
                    endrq: this.dateEnd,
                    yfbm: wrapper.pdbContent.yfbm,
                    page : pdList.param.page,
                    rows : pdList.param.rows,
                    parm : wrapper.search
                    //'kfbm': '02',
                    //'qrzfbz': 0
                };
                //查询盘点表
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPd&json=' + JSON.stringify(parm), function (json) {
                    if (json != null && json.a == 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            json.d[i]['pdrq'] = formatTime(json.d[i]['pdrq'], 'date');
                        }
                        if (json.d.length == 0) {
                            malert('未查询到盘点明细！','top','defeadted');
                        }
                        pdList.jsonList = json.d.list;
                        pdList.totlePage =  Math.ceil(json.d.total / pdList.param.rows);
                        wrapper.pzList = json.d.list;
                        console.log(wrapper.pzList+'凭证')

                    } else {
                        malert('数据获取失败！','top','defeadted')
                    }

                });
                common.closeLoading()
            },
            //盘点录入
            lr:function (index, item) {
                common.openloading('.zui-table-view');
            	pdList.pdlrList = [];
                wrapper.addShow=true;
                wrapper.isShowkd=false;
                wrapper.isShow=false;
                wrapper.pddhShow = false;
                pdList.isShowkd=false;
                pdList.isShow=false;
                pdList.addShow=true;
                pdList.shShow = false;
                wrapper.shShow = false;
                //保存已选盘点表
                wrapper.pdbSelected = item;
                wrapper.pzNum=item.pdpzh;
                //准备参数
                var json = item;
                //发送请求，查询盘点表明细
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdmx&json=' + JSON.stringify(json),
                    function(data) {
                	 for(var i = 0; i < data.d.list.length; i++) {
							data.d.list[i].scsl = data.d.list[i].kcsl;
						}
                        pdList.pdlrList = data.d.list;
                    });
                common.closeLoading()
            },
            //审核盘点
            shpd: function(index){
            	 pdList.pdlrList = [];
            	 wrapper.addShow=true;
                 wrapper.isShowkd=false;
                 wrapper.isShow=false;
                 wrapper.pddhShow = true;
                 pdList.isShowkd=false;
                 pdList.isShow=true;
                 pdList.addShow=false;
                 pdList.shShow=true;
                 wrapper.pddhList = [];
                 pdList.pdb = pdList.jsonList[index];
                 $.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdlr&json={qrzfbz:0}',
                         function(data) {
                	 if(data.a == 0){
                	     console.log(data);
                		 if(data.d.length == 0){
                			 malert("没有需要审核的盘点录入单！");
                			 return
                		 }
                		 wrapper.pddhList = data.d;

                     }
                 });
            }




        }
    });

    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
            title:'',
            query: '', //查询类型，新增或添加已有
            page: [],
            ghdwList: [], //供货单位
            pdWay: 0,
            fyContent: {},
            ypmcInput: null,
            popContent: {},
            addContent: {},
            dg: {
                page: 1,
                rows: 5,
                sort: "",
                order: "asc",
                parm: ''
            },

            them_tran: {},
            them: {
                '生产批号': 'scph',
                '材料编号': 'ypbm',
                '材料名称': 'ypmc',
                '库存数量': 'kcsl',
                '有效期至': 'yxqz',
                '规格': 'ypgg',
                '分装比例': 'fzbl',
                '进价': 'ypjj',
                '零价': 'yplj',
                '库房单位': 'kfdwmc',
                '二级库房单位': 'yfdwmc',
                '效期': 'yxqz',
                '材料剂型': 'jxmc'
            }

        },
        watch: {
            ypmcInput: function(newVal, oldVal) {
                if(newVal == this.fyContent.ypmc) {
                    return;
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                //分页参数
                this.dg.page = 1;
                this.dg.rows = 15;
                this.dg.parm = this.ypmcInput;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd' +
                    '&dg=' + JSON.stringify(this.dg),
                    function(data) {
                        wap.searchCon = data.d.list;
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                        if(data.d.list.length != 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show();

                        } else {
                            $(".selectGroup").hide();
                        }
                    });
            }
        },
        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg');
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                wap.addData();
                this.closes();
            },

            //添加材料记录
            adds: function () {
                if (this.popContent.ypmc == null) {
                    malert('请输入材料！','top','defeadted');
                    return;
                }
                if (this.popContent.scsl == null || this.popContent.scsl == null) {
                    malert('请输入材料数量！','top','defeadted');
                    return;
                }
                //将数据加入录入区
                pdList.jsonList.push(this.popContent);
                // wap.closes();
                this.popContent = {};
                $("#ypmc").focus();
            },

            addNewYp: function () {
                //判断录入数据是否填全
                var inputArr = document.getElementsByClassName("_addData");
                for (var i = 0; i < inputArr.length; i++) {
                    if (inputArr[i].value == null || inputArr[i].value == '') {
                        inputArr[i].className = 'emptyError';
                        malert('数据未输入!','top','defeadted');
                        return;
                    }
                }
                //添加凭证号
                this.addContent.pdpzh = wrapper.pdb.pdpzh;
                //准备参数
                var json = this.addContent;
                this.addContent.kcsl = 0; //库存默认为0
                this.addContent.scsl = 0; //实存默认为0
                //将数据加入录入区
                wap.jsonList.push(this.addContent);
                this.addContent = {};
                //保存新增材料到盘点明细列表
                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=xzpcyp', JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            malert("新增材料记录已经添加至盘库材料明细列表。",'top','success');
                            wap.jsonList = [];
                        } else {
                            if (data.body.d == 0) {
                                malert(data.body.c,'top','defeadted');
                            } else {
                                malert("数据提交失败",'top','defeadted');
                            }
                        }

                    }, function (  error) {
                        console.log(error);
                    });
                //关闭弹出框
                this.isShow = false;
            },

            clear: function () {
                if (this.addContent != null) {
                    this.addContent = {};
                }
            },

            close: function () {
                wap.isShow = false;
            },
            //当输入值后才触发
            change: function(event, type, val) {
                			this.dg.page = 1;
                			if(wrapper.pzNum == '') {
                				malert("请先选择凭证号!",'top','defeadted');
                				return;
                            }
                //根据type执行不同操作
                			wap.query = '/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdmx&dg=';
                			if('addNew' == type) {
                				var _searchEvent = $(event);
                                wap.query = '/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=';

                			} else {
                				var _searchEvent = $(event.target.nextElementSibling).eq(0);
                			}
                			this.popContent.ypmc = val;
                			var bean = wrapper.pzNum;
                			bean.parm = this.popContent.ypmc;
                			bean.page = 1;
                			bean.rows = 5;
                			bean.sort = 'ypbm';

                			$.getJSON(wap.query + JSON.stringify(wap.dg) + "&json=" + JSON.stringify(bean),
                				function(data) {
                					for(var i = 0; i < data.d.list.length; i++) {
                						data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                						data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                					}
                                    wap.page = data.d;
                                    wap.searchCon = data.d.list;
                                    wap.total = data.d.total;
                                    wap.selSearch = 0;
                					if(data.d.list.length != 0) {
                						$(".selectGroup").hide();
                						_searchEvent.show()
                					} else {
                						$(".selectGroup").hide();
                					}
                				});
            },
            //下拉table检索数据
            changeDown: function(event, type) {
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
                if(window.event.keyCode == 13) {
                    this.ypmcInput = this.popContent.ypmc;
                    if(type == 'ypmc') {
                        document.getElementById('scsl').focus();
                    }
                }
            },
            //单击选中
            selectOne: function(item) {
                //分页查询
                if(item == null) {
                    wap.dg.page++;
                    var bean = wrapper.pzNum;
                    bean.parm = this.popContent.ypmc;
                    bean.sort = 'ypbm';
                    bean.page = wap.dg.page;

                    $.getJSON(wap.query + JSON.stringify(wap.dg) + "&json=" + JSON.stringify(bean),
                        function(data) {
                            for(var i = 0; i < data.d.list.length; i++) {
                                data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                                data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                                wap.searchCon.push(data.d.list[i]);
                            }
                        });
                    return;
                }
                this.popContent = item;
                this.ypmcInput = item.ypmc;
                $(".selectGroup").hide();
            },

        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

    wrapper.getKFData();
// wrapper.getInfo();
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol) {
        $(".selectGroup").hide();
    }

});





