
.fydm-content-left{
  width:49%;
  float: left;
  .content-left-top{
    width: 100%;
    i{
      width: calc(~"(100% / 6)");
      text-align: center;
    }
  }
  .sjks-content-left-list{
    li{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      i{
        width: calc(~"(100% / 6)");
        text-align: center;
      }
      &:hover{
        background:rgba(26,188,156,0.08);
        border:1px solid #1abc9c;
      }
    }
  }
}
.sjks-content-right{
  width:100%;
  float: right;
  .content-right-top{
    width: 100%;
    i{
      width: calc(~"(100% / 6)");
      text-align: center;
      em{
        margin-top: 10px;
      }
    }
  }
  li{
    cursor: pointer;
    width: 100%;
    &:hover{
      background:rgba(26,188,156,0.08) !important;
      border:1px solid #1abc9c;
    }
  }
}
.pop-content{
  width:100%;
  float: right;
  .content-right-top,.content-right-list{
    width: 100%;
    i,span{
      width: calc(~"(100% / 3)");
      text-align: center;
      em{
        margin-top: 10px;
      }
    }
  }
  li{
    cursor: pointer;
    width: 100%;
    &:hover{
      background:rgba(26,188,156,0.08) !important;
      border:1px solid #1abc9c;
    }
  }
}
.ksys-side{
  width: 100%;
  padding: 26px 17px;
  float: left;
  .jiansuo{
    margin-bottom: 20px;
  }
  span{
    display: flex;
    width: 100%;
    justify-content: center;
    position: relative;
    i{
      width: 63px;
      float: left;
      align-items: center;
      align-self: center;
    }
  }
  #jyxm_icon .switch{
    top:0;
    left:17px;
  }

}
.border-r4{
  border-radius: 4px !important;
}
.ksys-btn{
  position: absolute;
  bottom: 20px;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 40px;
  button{
    margin-right: 20px;
  }
}
.content-right-list{
  width: 100%;
  height:80vh;
  overflow: auto;
  border: 1px solid #e9eee6;
  border-top: none;
  border-right: none;
  li{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 54px;
    border-top: 1px solid #e9eee6;
    i{
      width: calc(~"(100% / 6)")!important;
      text-align: center;
    }
    &:nth-child(2n){
      background: #fdfdfd;
    }
    &:first-child{
      border-top: none;
    }
  }
}
.tool-center{
  text-align: center;
}