var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    components: {
        'search-table': searchTable
    },
    data: {
        num: 0,
        qjIndex: null,
        jsonList: [],
        jsonLeftList: [],
        searchCon: [],
        param: {},
        dmStatus: '',
        totlePage: 0,
        dg: {page: 1, rows: 60, sort: "", order: "asc", parm: ""},
        page: {
            page: 1,
            rows: 20,
            total: null
        },
        pageSelect: {
            page: 1,
            rows: 20,
            total: null
        },
        selSearch: 0,
        param_rows: 200,
        bxlbbm: null,
        bxurl: null,
        search: '',
        xmlx_tran: {'0': '公用', '1': '门诊', '2': '住院'},
        ypfy_tran: {'0': '非药品', '1': '药品'},
        tybz_tran: {'0': '启用', '1': '停用'},
        them_tran: {},
        them: {'项目编码': 'wydm', '项目名称': 'ypmc','单位':'dw'},
        ksthem: {'科室编码': 'ksbm', '科室名称': 'ksmc'},
        isEdit: -1
    },
    watch: {
        page(val) {
            this.getData(val);
        },
    },
    created: function () {
        this.getbxlb();
    },
    mounted: function () {

    },
    methods: {
        save() {
            var medthod = this.num == 0 ? 'insertZllx' : this.num == 1 ? 'insertYpxx' : 'bcksdm'
            var list = this.jsonList;
            var param = [];
            for(var i = 0; i< list.length; i++){
                var item = list[i];
                if (item.wydm) {
                    param.push(item);
                }else if(item.nhksbm){
                    param.push(item);
                }
            }
            var list ={
                list:param
            }
            var url="/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=nhdm&method="+medthod+"&time="+new Date().getTime();
            wrapper.$http.post(url, JSON.stringify(list)).then(function (data) {
                console.log(">>>>>" + data.body.a);
                if (data.body.a == 0) {
                    malert('保存成功！')
                } else {
                    malert(data.body.c, 'top', 'defeadted')

                }
            });

        },
        changeDown() {},
        commonResultChange: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "dmStatus":
                    if (val[0] == 'wd') {//未对码
                        this.dmStatus = 'wd';
                    } else if (val[0] == 'yd') {//已对码
                        this.dmStatus = 'yd';
                    } else {//全部
                        this.dmStatus = null;
                    }
                    this.getData();
                    break;
            }
            console.log(val);
            this.dmStatus = val[0];
        },
        sschangeDown(event) {
            console.log("11111",this.dmStatus);
            if (event.keyCode == 13) {

                this.getData(this.page);
            }
        },
        tabBg: function (index) {
            this.num = index;
            console.log('---->', this.num, index);
            this.search = '';
            this.dmStatus = '';
            this.jsonList = [];
            this.getData(index);
        },
        addData: function () {
        },
        getKsXm: function () {
            var param = {
                bxlbbm: this.bxlbbm
            };

            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=nhdm&method=cshks&parm=" + JSON.stringify(param), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    wrapper.getData();
                } else {
                    malert(json.c)
                }
            });
        },
        getZlxm: function () {
            var param = {
                bxlbbm: this.bxlbbm
            };

            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=nhdm&method=getHisZllx&parm=" + JSON.stringify(param), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    wrapper.getData();
                } else {
                    malert(json.c)
                }
            });
        },
        getYpXm: function () {
            var param = {
                bxlbbm: this.bxlbbm
            };

            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=nhdm&method=getHisYpzd&parm=" + JSON.stringify(param), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    wrapper.getData();
                } else {
                    malert(json.c)
                }
            });
        },
        getData: function (page) {
            common.openloading()
            var medthod = this.num == 0 ? 'queryZllx' : this.num == 1 ? 'queryYpxx' : 'kslb'
            var param = {
                bxlbbm: this.bxlbbm,
                page: page || 1,
                rows: this.param_rows,
                parm: this.search,
                dmStatus: this.dmStatus=='wd'?1:this.dmStatus=='yd'?2:null
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=nhdm&method=" + medthod + "&parm=" + JSON.stringify(param), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    var total = JSON.parse(json.d).total / wrapper.param_rows;
                    wrapper.totlePage = Math.ceil(total);
                    wrapper.jsonList = JSON.parse(json.d).list;
                    common.closeLoading()
                } else {
                    common.closeLoading()
                    malert(json.c)
                }
            });
        },
        dim:function(item){
            wrapper.bxlbbm=''
            wrapper.bxurl=''
            wrapper.bxlbbm = item.bxlbbm;
            wrapper.bxurl = item.url;
            wrapper.getData();
        },
        getbxlb: function () {
            console.log(arguments);
            var param = {parm: "新_华东健康档案农合"};
            common.openloading();
            var _this = this;
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            // wrapper.bxlbbm = json.d.list[0].bxlbbm;
                            // wrapper.bxurl = json.d.list[0].url;
                            wrapper.jsonLeftList = json.d.list;
                        }
                        common.closeLoading();
                    } else {
                        malert("保险类别查询失败!" + json.c);
                        common.closeLoading();
                    }
                });
        },

        checkedOneOut(index) {
            console.log(arguments);
            var item = wrapper.searchCon[index];
            if(this.num==0){
                this.jsonList[this.qjIndex]['ypmc'] = item.ypmc;
                this.jsonList[this.qjIndex]['wydm'] = item.wydm;
                this.jsonList[this.qjIndex]['bnbw'] = item.bnbw;
                this.jsonList[this.qjIndex]['bxbl'] = item.bxbl;
            }else if(this.num==1){
                this.jsonList[this.qjIndex]['bxypmc'] = item.ypmc;
                this.jsonList[this.qjIndex]['wydm'] = item.wydm;
                this.jsonList[this.qjIndex]['bnbw'] = item.bnbw;
                this.jsonList[this.qjIndex]['bxbl'] = item.bxbl;
            }else if(this.num==2){
                this.jsonList[this.qjIndex]['nhksbm'] = item.ksbm;
                this.jsonList[this.qjIndex]['nhksmc'] = item.ksmc;
            }
            $(".selectGroup").hide();
        },
        selectOne() {
            console.log(arguments);
        },

        searching: function (index, add, type, val) {
            var medthod = this.num == 2 ? 'mhcxks' :'dimQuery';
            this.qjIndex = index;
            if( this.num==1){
                this.jsonList[index]['bxypmc'] = val;
            }else if (this.num==0){
                this.jsonList[index]['ypmc'] = val;
            }else {
                this.jsonList[index]['nhksmc'] = val;
            }
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.popContent = {};
            this.dg['parm'] = val;
            var dim = [];
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=nhdm&method=" + medthod + "&parm=" + JSON.stringify(this.dg), function (json) {
                console.log(">>>>>" + json.a);
                if (json.a == "0") {
                    this.dim = JSON.parse(json.d).list;
                    if (add) {
                        for (var i = 0; i < dim.length; i++) {
                            wrapper.searchCon.push(res.list[i]);
                        }
                    } else {
                        wrapper.searchCon = this.dim;
                    }
                    wrapper.page.total = JSON.parse(json.d).total;
                    wrapper.selSearch = 0;
                    $('#mc_' + index).next().show();
                    if (dim.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                } else {
                    malert(json.c)
                }
            });
        },
    }
});
$('body').click(function () {
    $(".selectGroup").hide();
});
