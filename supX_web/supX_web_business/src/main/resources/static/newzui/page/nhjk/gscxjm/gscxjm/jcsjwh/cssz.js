var cspz = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        popContent: {},
        page: {
            page: 1,
            rows: 20,
            total: null,
            parm: ""
        },
        bxlbbm: null,
        bxurl: null
    },
    mounted: function () {
        this.getbxlb();
        this.getData();
    },
    methods: {
        //获取存在的医保参数信息
        getData: function () {
            var param = {
                page: 1,
                rows: 30,
                sort: "yljgbm",
                order: "asc"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + cspz.bxurl + "&bxlbbm=" + cspz.bxlbbm + "&types=cspz&method=getCsszInfo&parm=" + JSON.stringify(param), function (json) {
                if (json.a == "0") {
                    cspz.popContent = JSON.parse(json.d);
                }
            });
        },


        getbxlb: function () {
            var param = {bxjk: "009"};
            common.openloading();
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            cspz.bxlbbm = json.d.list[0].bxlbbm;
                            cspz.bxurl = json.d.list[0].url;
                            cspz.getData();
                        }
                        common.closeLoading();
                    } else {
                        malert("保险类别查询失败!" + json.c);
                        common.closeLoading();
                    }
                });
        },
        // commonResultChange:function(val){
        //     var type = val[2][val[2].length - 1];
        //     switch (type) {
        //         case "dkqlx":
        //             if(val[0]=='1'){//未对码
        //                 Vue.set(this.popContent, 'dkqlx', '测试');
        //
        //                 // Vue.set(this.param, 'type', 'wd');
        //             }else if(val[0]=='2'){//已对码
        //                 Vue.set(this.param, 'dkqlx', 'sss');
        //
        //                 Vue.set(this.param, 'type', 'yd');
        //             }else{//全部
        //                 Vue.set(this.param, 'sfdm', '0');
        //
        //                 Vue.set(this.param, 'type', 'qb');
        //             }
        //             this.getmx();
        //             break;
        //     }
        // },
        save: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=cspz&method=insertCssz&parm=" + JSON.stringify(this.popContent), function (json) {
                if (json.a == "0") {
                    malert('操作成功！', 'top', 'success');
                    cspz.getData();
                } else {
                    malert(json.c, 'top', 'defeadted');
                }
            });
        }
    }
})
