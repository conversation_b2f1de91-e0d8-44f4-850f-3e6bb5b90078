<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>患者注册</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <style>
        .panel {
            height: calc(100% - 66px);
            overflow: auto;
        }

        .hzgl-flex {
            justify-content: flex-end;
            background: #fff !important;
            display: flex;
            align-items: center;
            border-top: 1px solid #eee;
            height: 66px;
        }
        .zui-table-tool{
            /*width: 100%;*/
            /*left: 0;*/
        }
        .icon-width:before{
            margin-top: 2px;
        }
    </style>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper">
    <div class="panel">
        <div id="toolbar" v-cloak class="panel-head border-bottom background " style="padding: 10px">
            <div class="zui-row">
                <div class="col-x-10" style="display: flex;justify-content: flex-start;align-items: center">
                    <div class="tool-left position">
                            <input  type="text" :value="brxxContent.text" name="phone"
                                   @input="searching(false,'text',$event.target.value)"
                                   @keyDown="changeDown($event,'text')" class="zui-input inp_search wh180"
                                   placeholder="请填写姓名/证件号码/手机号"/>
                            <search-table :message="searchCon" :selected="selSearch" :them="them" :them_tran="them_tran"
                                          :page="page" @click-one="checkedOneOut" @click-two="selectOne"></search-table>
                    </div>
                    <button class="tong-btn btn-parmary ">检索</button>
                    <button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" @click="addData">新增</button>
                    <button class="tong-btn btn-parmary-b"><i class="  icon-width icon-dsfz"></i>读取身份证</button>
                    <button class="tong-btn btn-parmary-b " @click="loadYkt()" ><i class="  icon-width icon-dylk"></i>读取就诊卡</button>
                    <button class="tong-btn btn-parmary-b "><i class=" icon-width icon-dybk"></i>读取社保卡</button>
                    <div class="tool-left position">
                        <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent type="number" title="点击后刷卡" class="zui-input" placeholder="请点击后刷卡" v-model="ykth" @keydown.enter="loadYkt()"/>
                    </div>
                </div>
                <div class="col-x-2 text-right">
                    <button class="zui-btn btn-warning" id="but_regyl" @click="show()">注册记录(F9)</button>
                </div>
            </div>
        </div>
        <div class="panel-body contextInfo" v-cloak id="brzcedit">
            <!--基本信息-->
            <div class="tab-card">
                <div class="tab-card-header"><div class="tab-card-header-title">基本信息</div></div>
                <div class="zui-form tab-card-body">
                    <div class="zui-inline  col-fm-2">
                        <label class="zui-form-label">医疗卡类型</label>
                        <select-input @change-data="resultChange" :not_empty="false"
                                      :child="cardList" :index="'zymc'" :index_val="'zybm'" :val="popContent.ylklx"
                                      :name="'popContent.ylklx'" :search="true" :disable="ylkxx">
                        </select-input>
                    </div>
                    <div class="zui-inline  col-fm-2">
                        <label class="zui-form-label">医疗卡号</label>
                        <div class="zui-input-inline">
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent type="number" :title="popContent.ylkh" class="zui-input" v-model="popContent.ylkh"
                                   :disabled="ylkxx" @keydown="nextFocus($event)"
                                   data-notEmpty="false" id="ylkh">
                            <!--<input type="text" class="zui-input" name="input1" placeholder="请输入医疗卡号"  check="required" />-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">病人ID</label>
                        <div class="zui-input-inline">
                            <input type="text" name="phone" v-model="popContent.brid" :title="popContent.brid"
                                   disabled="disabled" class="zui-input" disabled/>
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">病人姓名</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.brxm" v-model="popContent.brxm" title="popContent.brxm"
                                   data-notEmpty="true" class="zui-input" @keydown="nextFocus($event)" @keydown.enter="readCard()"
                                   @blur="setPYDM(popContent.brxm,'popContent','pydm')">
                            <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">病人性别</label>
                        <select-input @change-data="resultChange" :not_empty="true"
                                      :child="brxb_tran" :index="popContent.brxb" :val="popContent.brxb"
                                      :name="'popContent.brxb'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">手机号码</label>
                        <div class="zui-input-inline">
                            <input  @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model.number="popContent.sjhm" class="zui-input mask"
                                   type="number" maxlength="11" data-notEmpty="true"
                                   @keydown="nextFocus($event,'',true)" onkeyup="value=value.replace(/[^\d]/g,'')">
                            <!--<input type="text" name="phone" oninput="this.value.replace(/\D/g,'')" class="zui-input" mask="+86 999 9999 9999" check="required"/>-->
                        </div>
                    </div>

                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">病人国籍</label>
                        <select-input @change-data="resultChange" @newVal-data="这是回调函数" :not_empty="true"
                                      :child="gjList" :index="'gjmc'" :index_val="'gjbm'" :val="popContent.brgj"
                                      :search="true" :name="'popContent.brgj'" :index_mc="'brgjmc'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">病人民族</label>
                        <select-input @change-data="resultChange" :not_empty="true"
                                      :child="mzList" :index="'mzmc'" :index_val="'mzbm'" :val="popContent.brmz"
                                      :search="true" :name="'popContent.brmz'" :index_mc="'brmzmc'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">婚姻状况</label>
                        <select-input @change-data="resultChange" :not_empty="true"
                                      :child="hyzkList" :index="'hyzkmc'" :index_val="'hyzkbm'" :val="popContent.hyzk"
                                      :search="true" :name="'popContent.hyzk'" :index_mc="'hyzkmc'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">职 业</label>
                        <select-input @change-data="resultChange" :not_empty="true"
                                      :child="zyList" :index="'zymc'" :index_val="'zybm'" :val="popContent.zybm"
                                      :search="true" :name="'popContent.zybm'" :index_mc="'zybmmc'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">患者类型</label>
                        <select-input @change-data="resultChange" :not_empty="true"
                                      :child="ryhzlx_tran" :index="popContent.hzlx" :val="popContent.hzlx"
                                      :name="'popContent.hzlx'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">证件类型</label>
                        <!--<select-input @change-data="resultChange" :not_empty="true"
                                      :child="sfzjlx_tran" :index="popContent.sfzjlx" :val="popContent.sfzjlx"
                                      :name="'popContent.sfzjlx'">
                        </select-input>-->
                        <select-input @change-data="resultChange" :not_empty="false"
                                      :child="zjlxList" :index="'zymc'" :index_val="'zybm'" :val="popContent.sfzjlx"
                                      :search="true" :name="'popContent.sfzjlx'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">证件号码</label>
                        <div class="zui-input-inline">
                            <input  v-model="popContent.sfzjhm" class="title zui-input"
                                   data-notEmpty="true" @blur="setAge($event,'popContent','hjdshengList')"
                                   @keydown.enter="setAge($event,'popContent','hjdshengList')">
                            <!--<input type="text" name="phone" class="zui-input" check="required card"/>-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">出生日期</label>
                        <div class="zui-input-inline zui-date">
                            <i class="datenox fa-calendar"></i>
                            <input  v-model="popContent.csrq" class="zui-input title zui-dateList"
                                   id="csrq"  @keydown="nextFocus($event)"
                                   data-notEmpty="true">
                            <!--<input type="text" name="phone" class="zui-input" focus="sdg" mask="9999-99-99"  check="required"/>-->
                        </div>
                    </div>
                </div>

            </div>
            <!--联系人信息-->
            <div class="tab-card">
                <div class="tab-card-header"><div class="tab-card-header-title">联系人信息</div></div>
                <div class="zui-form tab-card-body">
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">工作单位</label>
                        <div class="zui-input-inline">
                            <!--<input type="text" name="phone" class="zui-input"   check="required"/>-->
                            <input :title="popContent.gzdw" v-model="popContent.gzdw" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)" id="gzdwinput">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">单位地址</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.dwdz" v-model="popContent.dwdz" data-notEmpty="false"
                                   class="zui-input" @keydown="nextFocus($event)">
                            <!--<input type="text" name="phone" class="zui-input">-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">单位邮编</label>
                        <div class="zui-input-inline">
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="popContent.dwyb" class="zui-input" type="number" data-notEmpty="false"
                                   @keydown="nextFocus($event)">
                            <!--<input type="text" name="phone" class="zui-input">-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">联系人姓名</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.lxrxm" v-model="popContent.lxrxm" data-notEmpty="true"
                                   class="zui-input" @keydown="nextFocus($event)">
                            <!--<input type="text" name="phone" class="zui-input">-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">联系人关系</label>
                        <select-input @change-data="resultChange" :not_empty="true"
                                      :child="lxrgxList" :index="'lxrgxmc'" :index_val="'lxrgxbm'"
                                      :val="popContent.lxrgx"
                                      :search="true" :name="'popContent.lxrgx'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">联系人电话</label>
                        <div class="zui-input-inline">
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent :title="popContent.lxrdh" v-model="popContent.lxrdh" type="number" class="zui-input"
                                   data-notEmpty="true" @keydown="nextFocus($event)">
                            <!--<input type="text" name="phone" class="zui-input">-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">联系人地址</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.lxrdz" v-model="popContent.lxrdz" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                            <!--<input type="text" name="phone" class="zui-input">-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">联系人单位</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.lxrdw" v-model="popContent.lxrdw" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                            <!--<input type="text" name="phone" class="zui-input">-->
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">联系人邮编</label>
                        <div class="zui-input-inline">
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent :title="popContent.lxryb" v-model="popContent.lxryb" class="zui-input" type="number"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                            <!--<input type="text" name="phone" class="zui-input">-->
                        </div>
                    </div>
                </div>

            </div>
            <!--地址信息-->
            <div class="tab-card">
                <div class="tab-card-header"><div class="tab-card-header-title">地址信息</div></div>
                <div class="zui-form tab-card-body">
                    <div class="zui-inline  col-fm-4">
                        <label class="zui-form-label">省/市/(区/县)</label>
                        <select-input style="width: calc(100% / 3) !important;float: left;"
                                      @change-data="resultzcChange" :not_empty="true"
                                      :child="jzdshengList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                      :val="popContent.jzdsheng"
                                      :search="true"
                                      :name="'popContent.jzdsheng'" :index_mc="'jzdshengmc'" :phd="'省'">
                        </select-input>
                        <select-input style="width: calc(100% / 3) !important;float: left;"
                                      @change-data="resultzcChange" :not_empty="true"
                                      :child="jzdshiList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                      :val="popContent.jzdshi"
                                      :search="true"
                                      :name="'popContent.jzdshi'" :index_mc="'jzdshimc'" :phd="'市'">
                        </select-input>
                        <select-input style="width: calc(100% / 3) !important;float: left;"
                                      @change-data="resultzcChange" :not_empty="true"
                                      :child="jzdxianList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                      :val="popContent.jzdxian"
                                      :search="true"
                                      :name="'popContent.jzdxian'" :index_mc="'jzdxianmc'" :phd="'区/县'">
                        </select-input>
                    </div>
                    <div class="col-fm-2 zui-inline">
                        <label class="zui-form-label">居住地名称</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdmc" v-model="popContent.jzdmc" class="zui-input"
                                   data-notEmpty="false" id="jzdmc" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">出生</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.csd" v-model="popContent.csd" class="zui-input"
                                   @keydown="nextFocus($event)" data-notEmpty="false">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">居住地乡</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdxiang" v-model="popContent.jzdxiang" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">居住地居委会</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdjwh" v-model="popContent.jzdjwh" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">居住地-村/路</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdnong" v-model="popContent.jzdnong" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">居住地-弄</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdnong" v-model="popContent.jzdnong" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">居住地-楼号</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdlh" v-model="popContent.jzdlh" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">居住地-门牌号</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdmph" v-model="popContent.jzdmph" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                </div>
                <div class="zui-form tab-card-body">
                    <div class="zui-inline  col-fm-4">
                        <label class="zui-form-label">户籍地</label>
                        <select-input style="width: calc(100% / 3) !important;float: left;"
                                      @change-data="resultzcChange" :not_empty="true"
                                      :child="hjdshengList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                      :val="popContent.hjdsheng"
                                      :search="true"
                                      :name="'popContent.hjdsheng'" :index_mc="'hjdshengmc'" :phd="'省'">
                        </select-input>
                        <select-input style="width: calc(100% / 3) !important;float: left;"
                                      @change-data="resultzcChange" :not_empty="true"
                                      :child="hjdshiList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                      :val="popContent.hjdshi"
                                      :search="true"
                                      :name="'popContent.hjdshi'" :index_mc="'hjdshimc'" :phd="'市'">
                        </select-input>
                        <select-input style="width: calc(100% / 3) !important;float: left;"
                                      @change-data="resultzcChange" :not_empty="true"
                                      :child="hjdxianList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                      :val="popContent.hjdxian"
                                      :search="true"
                                      :name="'popContent.hjdxian'" :index_mc="'hjdxianmc'" :phd="'区/县'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户籍地名称</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.hkdz" v-model="popContent.hkdz" class="zui-input"
                                   data-notEmpty="false" id="hkdz" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户籍地乡</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.hjdxiang" v-model="popContent.hjdxiang" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户籍地居委会</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.hjdjwh" v-model="popContent.hjdjwh" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户籍地-村/路</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.hjdnong" v-model="popContent.hjdnong" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户籍地-弄</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdnong" v-model="popContent.jzdnong" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户籍地-楼号</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdlh" v-model="popContent.jzdlh" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户籍地-门牌号</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jzdmph" v-model="popContent.jzdmph" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户籍地行政区划</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.hjdxzqh" v-model="popContent.hjdxzqh" class="zui-input"
                                   data-notEmpty="true" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">户口地邮编</label>
                        <div class="zui-input-inline">
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent :title="popContent.hkdyb" v-model="popContent.hkdyb" type="number" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                </div>
            </div>
            <!--其它信息-->
            <div class="tab-card">
                <div class="tab-card-header"><div class="tab-card-header-title">其它信息</div></div>
                <div class="zui-form tab-card-body">
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">联系电话类别</label>
                        <select-input @change-data="resultChange" :not_empty="false"
                                      :child="lxrdhlx_tran" :index="popContent.lxdhlbdm" :val="popContent.lxdhlbdm"
                                      :name="'popContent.lxdhlbdm'">
                        </select-input>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">健康档案编号</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.jkdabh" v-model="popContent.jkdabh" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">身高</label>
                        <div class="zui-input-inline">
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent :title="popContent.sg" v-model="popContent.sg" type="number" class="zui-input"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">体重</label>
                        <div class="zui-input-inline">
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent :title="popContent.tz" v-model="popContent.tz" class="zui-input" type="number"
                                   data-notEmpty="false" @keydown="nextFocus($event)">
                        </div>
                    </div>
                    <div class="zui-inline col-fm-2">
                        <label class="zui-form-label">拼音代码</label>
                        <div class="zui-input-inline">
                            <input :title="popContent.pydm" v-model="popContent.pydm" class="zui-input phone"
                                   data-notEmpty="false" @keydown="saveDateHc($event)">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-tool hzgl-flex" style="bottom:11px;">
        <button class="zui-btn btn-primary  onsubmit xmzb-db">保存(F2)</button>
        <button class="zui-btn btn-default  onreset xmzb-db">重置</button>
    </div>
</div>
<!--侧边窗口-->
<div class="side-form" :class="{'ng-hide':type}" v-cloak id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span>注册记录</span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="grid-box">
        <div class="col-xxl-6 flex margin-l-10">
            <div class="flex margin-b-15 margin-top20 margin-l-10" style="display: flex;justify-content: flex-start;align-items: center;">
                <div class="zui-input-inline margin-l13 margin-r-5">
                    <input type="text" name="phone" class="zui-input wh180" v-model="param.parm" @keyup.enter="searchingList()" placeholder="请填写关键字"/>
                </div>
                <button class="tong-btn btn-parmary" @click="searchingList()">检索</button>
            </div>
        </div>
    </div>
        <div class="zui-table-view" id="teble-jhth">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">病人ID</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l">病人姓名</div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s">性别</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">出生日期</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">身份证号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">手机号码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">出生地</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">国籍</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">民族</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">证件类型</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">婚姻状况</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">职业编码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">患者类型</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">工作单位</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">单位地址</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">单位邮编</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地行政区划</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地省份</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地市</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地县</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地乡</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地居委会</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地-村</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地-弄</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地-楼号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地-门牌号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left">居住地名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地行政区划</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地-省</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地-市</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地-县</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地-乡</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地居委</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地-村</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地-弄</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地-楼号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地门牌号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户口地址</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户口地邮编</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">联系人姓名</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">联系人关系</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">联系人地址</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">联系人单位</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">联系人邮编</div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-xl">联系人电话</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">登记日期</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">登记人员</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">拼音代码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">身份证件类型名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">婚姻状况名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left">职业编码名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">联系电话类别代码</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">健康档案编号</div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-xl">病人国籍名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">病人民族名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地行政区划名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地省分名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地市名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地县名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地乡名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">居住地居委会名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地行政区划名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地省份名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地市名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地县名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地乡名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl">户籍地居委会名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">身高</div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s">体重</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"
                        @dblclick="edit($index)" @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.brid"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="fDate(item.csrq,'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.sfzjhm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.sjhm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.csd"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brgj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brmz"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.sfzjlx"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.hyzk"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.zybm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hzlx"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.gzdw"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.dwdz"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.dwyb"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdxzqh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdsheng"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdshi"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdxian"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdxiang"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdjwh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdcun"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdnong"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdlh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdmph"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left" v-text="item.jzdmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdxzqh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdsheng"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdshi"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdxian"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdxiang"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdjwh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdcun"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdnong"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdlp"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdmph"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hkdz"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hkdyb"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.lxrxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.lxrgx"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.lxrdz"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.lxrdw"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.lxryb"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.lxrdh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.djrq"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.djry"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.pydm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.sfjzlxmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hyzkmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left" v-text="item.zybmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.lxdhlbdm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jkdabh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.brgjmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.brmzmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdxzqhmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdshengmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdshimc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdxianmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdxiangmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.jzdjwhmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdxzqhmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdshengmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdshimc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdxianmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdxiangmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.hjdjwhmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.sg"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.tz"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div  class="zui-table-fixed table-fixed-l"> <!-- 有浮动就加 table-fixed-r -->
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m text-center">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class="cell-xl">
                                <div  class="zui-table-cell cell-xl"><span>病人ID</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!--@click="hover($index,$event)"-->
                <div class="zui-table-body "  @scroll="scrollTableFixed($event)"
                     style="border-right: 1px solid #eee;">
                    <table class="zui-table">
                        <tbody>
                        <tr @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()" v-for="(item, $index) in jsonList"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            class="tableTr2 table-hovers-filexd-l">
                            <td class="cell-m text-center">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-xl">
                                <div cell="cell-2-0"  class="zui-table-cell cell-xl">{{item.brid}}</div>
                            </td>
                        </tr>
                        </tbody>

                    </table>
                </div>
            </div>
            <page @go-page="goPage"  style="bottom:0" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
</div>

<script src="brzc.js"></script>
</body>
</html>
