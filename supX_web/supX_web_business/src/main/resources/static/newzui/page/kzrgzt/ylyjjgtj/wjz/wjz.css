.count-box{
    width: 100%;
}
.count-left{
    width:calc(100% - 18% - 8px);
    float: left;
    height: 441px;
    background: #fff;
    border:1px solid #eeeeee;
    border-radius:4px 4px 0 0;
}
.count-right{
    width:18%;
    float: right;
}
.count-title{
    width: 100%;
    font-size:16px;
    color:#333333;
    font-weight: bold;
    border-bottom:1px solid #eeeeee;
    background:#fbfbfb;
    padding: 12px 13px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.count-time{
padding: 10px 0 10px 21px;
width:100%;
box-sizing: border-box;
}
.icon-position{
    top: 10px;
}
.text-indent-20{
 text-indent: 20px !important;
}
.count-bottom{
display: flex;
justify-content: center;
align-items: center;
margin-top: 14px;
}
.count-bottom span{
padding: 0 10px 0;
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 14px;
}
.count-bottom span i{
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
}
.font30{
font-size: 30px;
}
.bzt-box{
     height: 230px;
     width: 100%;
     padding-top:10px;
     box-sizing: border-box;
     display: flex;
     justify-content: center;
     align-items: center;

 }
.right-top{
background: #fff;
width: 100%;
height: 215px;
margin-bottom:11px;
position: relative;
}

.bottom-box{
width: 100%;
float: left;
}
.bottom-left{
width: calc(100% - 30% - 10px);
float: left;
background: #fff;
height: 350px;
}
.bottom-right{
width: 30%;
background: #fff;
float: right;
height: 350px;
}
.font12{
font-size: 12px !important;
}
.bottom-table{
width: 100%;
padding: 10px;
box-sizing: border-box;
}

.zui-collapse{
    border-collapse: collapse;
}
.zui-collapse tr.table-active{
    background:rgba(255,69,50,0.08);
}
.crisis-danger {
    background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);
    border: 1px solid #f46161;
    border-radius: 38px;
    width: 16px;
    height: 16px;
    font-size: 12px;
    color: #fff;
    display: block;
    float: left;
    margin: 6px 4px 0 0;
}
small {
    transform: scale(0.75);
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 11px;
}
.wjz-width {
    width: 534px;
    height: 300px;
    position: absolute;
    top: calc((100vh - 300px) / 2);
    left: calc((100vw - 534px) / 2);
    z-index: 9999;
    background: #fff;
    box-shadow: 0 0 18px 0 rgba(0,0,0,0.50);
}
.wjz-top {
    width: 100%;
    height: 46px;
    background: #1abc9c;
    color: #fff;
    font-size: 16px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.wjz-content {
    padding: 19px 15px 15px 30px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.wjz-jsr {
    width: auto;
    padding-right: 5px;
    min-width: 47px;
}
.wjz-radio {
    padding: 0 15px 15px 80px;
}
.c_radio {
    float: left;
    color: #354052;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-right: 20px;
}
.c_radio .lb_text {
    padding-left: 5px;
}
.c_radio > input {
    display: none;
}
.c_radio label {
    vertical-align: middle;
    line-height: 16px;
    display: inline-block;
    height: 16px;
    font-size: 14px;
}
.c_radio > input:checked + label::before,
.c_radio > input:checked + label::after {
    display: block;
}
.c_radio > input + label {
    position: relative;
    cursor: pointer;
    vertical-align: middle;
}
.c_radio > input + label::before {
    position: relative;
    top: 0;
    left: 0;
    display: inline-block;
    width: 16px;
    height: 16px;
    content: '';
    background: url("/newzui/css/images/dx.png") center no-repeat;
    background-size: 16px 16px;
}
.c_radio > input + label::after {
    content: "";
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 16px;
    height: 16px;
    background: url("/newzui/css/images/dx_h.png") center no-repeat;
    background-size: 16px 16px;
}
.wjz-bz {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 15px 15px 0px 33px;
    box-sizing: border-box;
}
.wjz-textarea {
    width: 100%;
    border: 1px solid #d7dbe1;
    padding: 10px 12px;
    box-sizing: border-box;
    height: 68px;
    border-radius: 5px;
    color: #354052;
}
.wjz-btn {
    width: 100%;
    padding: 30px 5px 0 0;
    display: flex;
    justify-content: flex-end;
}
.count-name{
    width: 100%;
    font-size:16px;
    color:#393f45;
    box-sizing: border-box;
    padding: 16px 0 0 15px;
}
.count-process{
/*width:calc(100% - 40px);*/
    margin: 0 auto;
    padding: 39px 0 0 20px;
    position: relative;
width: 372px;
height: 260px;
}

.count-process span{
    display: block;
    font-size: 12px;
    text-align: center;
    float: left;
    width: 100%;
}
.count-start1{
width: 154px;
margin-left: 35px;
}

.count-start2{
    width: 166px;
    height: 57px;
    margin-left: -9px;
    position: relative;
}
.count-start3{
    width: 167px;
    height: 48px;
    position: absolute;
    left:199px;
    top: 86px;

}
.count-start4{
    position: absolute;
    width: 188px;
    height: 57px;
    top: 111px;
    left: 22px;

}
.count-start5{
position: absolute;
    top: 158px;
    left: 22px;
    width: 165px;
    height: 47px;
}
.count-start6{
    position: absolute;
    top: 182px;
    left: 178px;
    width: 155px;
    height: 22px;
    line-height: 22px;
}
.count-start7{
width: 69px;
    position: absolute;
    top: 182px;
    left:322px;
    height: 22px;
    line-height: 22px;
}
.start-text-position{
position: absolute;
top: 24px;
left: -10px;
}
.start-text-position1{
    position: absolute;
    top: 50px;
    left:0;
}
.start-text-position2{
position: absolute;
    top: 25px;
}
.start-text-position3{
    position: absolute;
    top: 50px;
}
.start-text-position4{
position: absolute;
    top: 25px;
}
.text-indent-f40{
text-indent: -40px !important;
}
.text-indent-30{
 text-indent:30px !important;
}
.text-indent-0{
 text-indent: 0px !important;
}
.start-ys1{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size: 154px 22px;
    width: 154px;
    height: 22px;
    line-height: 22px;
}
.start-ys2{
    background: url("/newzui/pub/image/<EMAIL>") center top no-repeat;
    background-size: 166px 57px;
    width: 166px;
    height: 57px;
    line-height: 22px;
}
.start-ys3{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:167px 48px;
    width: 167px;
    height: 48px;
    line-height:70px;
}
.start-ys4{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:188px  57px;
    width: 188px;
    height: 57px;
    line-height: 20px;
}
.start-ys5{
    background: url("/newzui/pub/image/<EMAIL>") center top no-repeat;
    background-size: 165px 47px;
    width: 165px;
    height: 47px;
    line-height: 70px;
}
.start-ys6{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:155px 22px;
    width: 155px;
    height: 22px;
}
.start-ys7{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:69px 22px;
    width: 69px;
    height: 22px;
}

.start-ws1{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size: 154px 22px;
    width: 154px;
    height: 22px;
    line-height: 22px;
}
.start-ws2{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size: 116px 57px;
    width: 166px;
    height: 57px;
    line-height: 22px;
}
.start-ws3{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:167px 48px;
    width: 167px;
    height: 48px;
    line-height:70px;
}
.start-ws4{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:188px  57px;
    width: 188px;
    height: 57px;
    line-height: 20px;
}
.start-ws5{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size: 165px 47px;
    width: 165px;
    height: 47px;
    line-height: 70px;
}
.start-ws6{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:155px 22px;
    width: 155px;
    height: 22px;
}
.start-ws7{
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size:69px 22px;
    width: 69px;
    height: 22px;
}
.color-c42{
    color:#424545;
}
.start-overtime{
    position: absolute;
    top: -5px;
    background: #ff5c63;
    border-radius: 4px;
    width: 51px !important;
    height: 24px;
    left: 64px;
    line-height: 24px;
    color: #fff;
}
.start-overtime:after{
    content: '';
    position: absolute;
    width: 8px;
    background: #ff5c63;
    height: 8px;
    top: 19px;
    left: 22px;
    transform: rotate(45deg);
}
.percent{
width: 65px;
height: 28px;
line-height: 28px;
position: absolute;
text-align: center;
    color:#ff5c63;
    background: #eff2f3;
    bottom: 10px;
    left: calc((100% - 65px)/2);
    font-weight: 600;font-size: 16px;
}
.count-content{
height: 260px;
}