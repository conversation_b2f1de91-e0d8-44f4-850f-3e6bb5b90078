html{
    height: 100%;
    overflow: hidden;
}
/* 新的小(单页面内的)菜单样式 */
.pageMenu {
    position: fixed;
    float: left;
    width: 180px;
    height: 100%;
    background-color: #E2EBEA;
    font-size: 14px;
    color: #96A4AF;
}

.context {
    float: left;
    width: 100%;
    height: 100%;
}

.pageHead {
    font-size: 14px;
    color: #C1C1C1;
    margin: 0 20px;
    padding: 13px 0;
    border-bottom: 1px solid #bbbbbb;
}

.pageHead span:first-child{
    border-left: 2px solid #CCCCCC;
    padding-left: 12px;
}

.pageHead span{
    padding: 0 10px;
}

.info{
    margin-top: 20px;
    display: inline-block;
}

.infoTitle{
    color: #666668;
    margin: 0 30px;
    padding: 0 20px;
    overflow: hidden;
    font-size: 14px;
}

.infoTitle > div{
    float: left;
    padding: 14px 0 12px 0;
    margin-right: 2%;
}

.infoTitle > div > div{
    float: left;
}

.conInfo{
    width: 100%;
    background-color: #FFFFFF;
    margin-top: 20px;
}

.conTit{
    position: relative;
    font-size: 16px;
    margin: 0 20px;
    height: 24px;
    border-bottom: 1px solid #eee;
    color: #999;
}

.conTit div{
    position: absolute;
    top: 10px;
    left: 22px;
    background-color: #FFFFFF;
    padding: 0 10px;
}

.foldIcon{
    width: 24px;
    height: 14px;
    font-size: 20px;
    text-align: center;
    margin: 0 auto;
    color: #435465;
    padding: 10px 0;
    cursor: pointer;
}

.foldIcon_green{
    color: #048069;
    float: right;
    margin-right: 20px;
}

.pageTitle{
    width: 100%;
    text-align: center;
    font-size: 18px;
    margin-top: 26px;
}

.pageDiv{
    width: calc(100% - 60px);
    position: absolute;
    bottom: 20px;
    text-align: center;
}

.page{
    display: inline-block;
}

.page > div{
    float: left;
    border-radius: 2px;
    margin: 0 2px;
}

.page .num{
    padding: 4px 10px;
    cursor: pointer;
}

.page input, .page select{
    width: 30px;
    border-radius: 2px;
    border: 1px solid #aaa;
    height: 25px;
    padding: 0 2px;
    margin: 0 6px;
}

.page select{
    width: 42px;
    height: 27px;
}

.page span{
    margin: 0 4px;
}

.page .divBtu{
    float: right;
    padding: 4px;
    margin: 0 10px 0 14px;
}

.page .num:first-child, .page .next{
    padding: 6px 10px 7px 10px;
}

.page .num:hover{
    color: #FFFFFF;
    background-color: #1AB394;
}

.currentPage {
    color: #FFFFFF;
    background-color: #1AB394;
}

.divBtu {
    background-color: #1AB394;
    color: #fff;
    border: 0;
    border-radius: 2px;
    padding: 8px 0;
    cursor: pointer;
}

.refresh{
    /* position: fixed; */
    /* bottom: 100px; */
    /* right: 30px; */
    cursor: pointer;
    width: 44px;
    position: absolute;
    right: 50px;
}

/*单页面的通用样式*/
.info {
    margin-left: 20px;
    width: calc(100% - 20px);
}

.infoIpt {
    position: relative;
    float: left;
    width: 24%;
    height: 32px;
    margin: 5px 0;
}

.infoIpt p {
    float: left;
    text-align: right;
    width: 90px;
    padding: 7px 8px 7px 0;
    font-size: 14px;
    margin: 0;
    color: #333333;
}

.selectInput {
    height: 34px;
    width: 90%;
}

.infoIpt .selectInput{
    width: calc(100% - 100px);
}

.infoIpt input {
    float: left;
    width: 100%;
    height: 34px;
    font-size: 14px;
    padding: 0 18px 0 6px;
    border: 1px solid #dddddd;
    outline: none;
    box-sizing: border-box;
}

.infoIpt input:focus {
    border: 1px solid #1AB394;
}

.infoIpt select {
    float: left;
    width: 66%;
    height: 34px;
    font-size: 14px;
    padding: 0 6px;
    border: 1px solid #aaaaaa;
    border-radius: 0;
    color: #aaaaaa;
}

.btu {
    display: inline-block;
    background-color: #1AB394;
    color: #FFFFFF;
    margin: 7px 0;
    padding: 8px 20px;
    border-radius: 2px;
    cursor: pointer;
}

/*.infoIpt > span {*/
    /*position: absolute;*/
    /*left: 330px;*/
    /*width: 268px;*/
    /*padding: 5px 0;*/
    /*font-size: 14px;*/
    /*color: #C2C2C2;*/
/*}*/

.selectInput ul {
    top: 37px;
    min-width: 100px;
    max-height: 240px;
    overflow-x: hidden;
}

.selectInput ul > li {
    font-size: 14px;
    padding: 7px 14px;
}

.calendar {
    position: relative;
    float: left;
    height: 32px;
    width: calc(100% - 98px);
}

.exInfo {
    margin: 15px 30px;
    border-left: 4px solid #1AB394;
    background-color: #DFE0E0;
}

.exInfo > div {
    padding: 18px 15px;
}

.exInfo p {
    float: left;
    margin: 0;
    color: #999999;
}

.exInfo span {
    margin-left: 14px;
}

.search {
    text-align: center;
    width: 60%;
    height: 36px;
    margin: 0 auto;
    min-width: 800px;
    padding: 22px 0 4px 0;
}

.searchText {
    width: 58%;
    border: 1px solid #1AB394;
    border-radius: 2px;
    text-align: left;
    background-color: #FFFFFF;
}

.searchText .selectInput {
    float: left;
    width: 15%;
    height: 34px;
    border: 0;
    border-right: 1px solid #E9E9E9;
    border-radius: 2px 0 0 2px;
    min-width: 50px;
}

.searchText input {
    float: left;
    width: 73%;
    height: 34px;
    border: 0;
    border-radius: 2px 0 0 2px;
    outline: none;
    padding: 0 4px;
}

.goSearch {
    float: right;
    width: 11%;
    padding: 7px 0 8px 0;
    background-color: #1AB394;
    color: #FFFFFF;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}

.search > div {
    float: left;
}

.searchBtu {
    width: 12%;
    background-color: #1AB394;
    color: #fff;
    border: 0;
    border-radius: 2px;
    padding: 8px 0 9px 0;
    cursor: pointer;
    margin-right: 1%;
}

.contextStyle {
    width: calc(100% - 60px);
    margin-left: 30px;
    overflow-y: scroll;
    float: left;
    box-shadow: 0 2px 5px #bbbbbb;
    background-color: #fff;
}

.contextSize{
    height: calc(100% - 180px);
    overflow-y: scroll;
}

.btu_cancel {
    background-color: #FFFFFF;
    color: #808080;
    border: 1px solid #CCCCCC;
    margin-left: 14px;
    padding: 7px 20px;
}

.money{
    float: right;
    margin: 10px 0;
}

.money p{
    margin: 5px 0;
    float: left;
}

.money span{
    float: left;
    font-size: 20px;
    color: red;
    font-weight: 900;
    margin: 0 4px;
}

.bottom_btu{
    position: fixed;
    bottom: 0;
    height: 50px;
    width: 100%;
    box-shadow: 0 2px 5px #bbb;
    background-color: #fff;
}

.place{
    width: 48% !important;
}

.place > .selectInput{
    width: calc((100% - 100px)/3);
}

.place input{
    border-right: 0;
}

.place .selectInput:last-child input{
    border-right: 1px solid #dddddd;
}

.toDayList{
    position: relative;
    width: 30px;
    float: right;
    background-color: #FFFFFF;
    height: calc(100% - 180px);
    box-shadow: 0 2px 5px #bbb;
}

.popup-right{
    position: absolute;
    right: 0;
    float: right;
    background-color: #FFFFFF;
    height: 100%;
    overflow: hidden;
}

.street{
    width: 48%;
}

.listFold{
    position: absolute;
    left: -10px;
    top: calc(50% - 25px);
    cursor: pointer;
}

.conBtu{
    margin: 0 60px 0 30px;
}

.toDayTable{
    border-collapse: collapse;
    height: auto;
}

.toDayTable th{
    background-color: #F4F4F4;
    border: 1px solid #E2E2E2;
    padding: 8px 15px;
    font-weight: 600;
}

.toDayTable td{
    border: 1px solid #E2E2E2;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 15px;
}

.listDiv{
    position: relative;
    overflow: scroll;
    margin-top: 20px;
    height: calc(100% - 60px);
    margin-left: 20px;
}

.hideDiv{
    position: absolute;
    display: table;
    top: 0;
    left: 0;
    background-color: #FFFFFF;
    width: 100%;
    height: 100%;
    z-index: 100;
}

.hideDiv div{
    padding: 10px;
    display: table-cell;
    text-align: center;
    vertical-align: middle;
}

.age .selectInput{
    position: absolute;
    width: 50px;
    top: 0;
    right: 0;
    border-left: 0;
    min-width: 50px;
}

.age .selectInput input{
    border-left: 0;
}

.age .selectInput b{
    right: 10px;
}

.age ul{
    min-width: 48px;
    width: 48px;
}

.prvInfo > div{
    margin-right: 14px;
}

.prvInfo > div > div{
    float: left;
}

.infoTitle-btu{
    padding: 3px 10px !important;
    border: 1px solid #ccc;
    border-radius: 2px;
    margin-top: 4px;
    float: right !important;
    background-color: #fff;
    cursor: pointer;
    margin-right: 0 !important;
}

.pop-info{
    width: 600px;
    height: 500px;
    margin: 0 auto;
    background-color: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .33);
    transition: all 0.3s ease;
}

.pop-title{
    background-color: #1AB394;
    color: #FFFFFF;
    padding: 8px 26px;
    font-size: 18px;
    text-align: left;
    border-radius: 4px 4px 0 0;
}

.pop-title img{
    float: right;
    margin-top: 4px;
}

.input-tem1{
    float: left;
    width: 100%;
    height: 34px;
    font-size: 14px;
    padding: 0 18px 0 6px;
    border: 1px solid #CCCCCC;
    outline: none;
    box-sizing: border-box;
}

.input-tem1:focus{
    border: 1px solid #1AB394;
}

.printArea{
    display: none;
}

.printBtu{
    float: right;
}