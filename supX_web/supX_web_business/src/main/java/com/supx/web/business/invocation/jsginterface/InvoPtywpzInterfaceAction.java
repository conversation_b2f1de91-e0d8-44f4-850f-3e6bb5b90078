package com.supx.web.business.invocation.jsginterface;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.jsginterface.pojo.Pt_YwpzModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.jsginterface.iface.IPtywpzInterfaceService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1PtywpzInterface")
public class InvoPtywpzInterfaceAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoPtywpzInterfaceAction.class);

	@Autowired
	private IPtywpzInterfaceService ptywpzInterfaceService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoPtywpzInterfaceAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		getSession(invoContext,result);
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型 调用医保交易为S，否则自已定义
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");    //参数
		Pt_YwpzModel ywpz = null ;
		if  (parm == null || parm.equals("")){
			//参数为空时，为post参数调用，
			ywpz = (Pt_YwpzModel)RequestUtil.getObjParamter(invoContext.getRequest(),Pt_YwpzModel.class);//invoContext.getRequest().getAttribute(State.REQ_PARAMS);
		}else {
			ywpz = (Pt_YwpzModel)JSONObject.parseObject(parm, Pt_YwpzModel.class);
		}
		//获取用户信息
		msg.getUserinfo().put("userinfo", userinfo);
		msg.getParam().put("bean", ywpz);
		switch (optType) {
		case "save"://保存
			if (ywpz == null) {
				result.setLogicCode(ERROR);
				result.setC("保存时对象不能为空");
				return;
			}
			if (ywpz.getMsgcode() == null || ywpz.getMsgkey().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时Msgcode不能为空");
				return;
			}
			if (ywpz.getMsgkey() == null || ywpz.getMsgkey().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时Msgkey不能为空");
				return;
			}
			if (ywpz.getMsgname() == null || ywpz.getMsgname().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时Msgname不能为空");
				return;
			}
			ptywpzInterfaceService.insert(msg, result);
			break;
		case "queryOne"://查询一条记录
			if (ywpz.getMsgcode() == null || ywpz.getMsgcode().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("查询时msgcode不能为空");
				return;
			}
			ptywpzInterfaceService.queryone(msg, result);
			break;
		case "query"://查询
			ptywpzInterfaceService.query(msg, result);
			break;
		case "delete"://删除
			if (ywpz.getMsgcode() == null || ywpz.getMsgcode().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("删除时msgcode不能为空");
				return;
			}
			ptywpzInterfaceService.delete(msg, result);
			break;
		case "update"://修改
			if (ywpz.getMsgcode() == null || ywpz.getMsgcode().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("修改时msgcode不能为空");
				return;
			}
			ptywpzInterfaceService.update(msg, result);
			break;
		default:
			break;
		}

	}
}
