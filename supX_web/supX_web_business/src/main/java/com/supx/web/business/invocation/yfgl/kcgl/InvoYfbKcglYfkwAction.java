package com.supx.web.business.invocation.yfgl.kcgl;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_yfkwModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglYfkwlService;

import javax.annotation.Resource;

/**
 *
 * @ClassName: InvoYfbKcglYfkwAction
 * @Description: (药房库位业务控制)
 * <AUTHOR> YK
 * @date 2020年9月8日 下午7:34:53
 *
 */
@Controller("INVO_New1YfbKcglYfkw")
public class InvoYfbKcglYfkwAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志工具
	private static final Logger logger = LoggerFactory.getLogger(InvoYfbKcglYfkwAction.class);

	@Resource
	private IYfbKcglYfkwlService new1yfbKcglYfkwlService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");

		// 操作类型
		String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");

		// 操作参数
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		Yfb_yfkwModel yfkw = null;

		// 根据操作类型执行不同操作
		switch (optType) {
		// 保存库位信息
		case "save":
			yfkw = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_yfkwModel.class);
			if (yfkw == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("yfkw", yfkw);
				new1yfbKcglYfkwlService.addNew(msg, result);
			}
			break;
		case "update":
			yfkw = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_yfkwModel.class);
			if (yfkw == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("yfkw", yfkw);
				new1yfbKcglYfkwlService.modify(msg, result);
			}
			break;
		case "delete":
			List<Yfb_yfkwModel> list = (List<Yfb_yfkwModel>) RequestUtil.getListParamter(invoContext.getRequest(),
					Yfb_yfkwModel.class);
			if (list == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("list", list);
				new1yfbKcglYfkwlService.delete(msg, result);
			}
			break;
		case "query":
			if (json == null || "".equals(json)) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				yfkw = JSONObject.parseObject(json, Yfb_yfkwModel.class);
				msg.getParam().put("yfkw", yfkw);
				new1yfbKcglYfkwlService.query(msg, result);
			}
			break;
		}

	}

}
