package com.supx.web.business.invocation.yfgl.kcgl;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ypkwModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglYpkwlService;

import javax.annotation.Resource;

/**
 *
 * @ClassName: InvoYfbKcglypkwAction
 * @Description: (药品库位业务控制)
 * <AUTHOR> YK
 * @date 2020年9月8日 下午7:34:53
 *
 */
@Controller("INVO_New1YfbKcglYpkw")
public class InvoYfbKcglYpkwAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志工具
	private static final Logger logger = LoggerFactory.getLogger(InvoYfbKcglYpkwAction.class);

	@Resource
	private IYfbKcglYpkwlService new1yfbKcglYpkwlService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");

		// 操作类型
		String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");

		// 操作参数
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		Yfb_ypkwModel ypkw = null;

		// 根据操作类型执行不同操作
		switch (optType) {
		// 保存库位信息
		case "save":
			ypkw = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_ypkwModel.class);
			if (ypkw == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("ypkw", ypkw);
				new1yfbKcglYpkwlService.addNew(msg, result);
			}
			break;
		case "update":
			ypkw = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_ypkwModel.class);
			if (ypkw == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("ypkw", ypkw);
				new1yfbKcglYpkwlService.modify(msg, result);
			}
			break;
		case "delete":
			List<Yfb_ypkwModel> list = (List<Yfb_ypkwModel>) RequestUtil.getListParamter(invoContext.getRequest(),
					Yfb_ypkwModel.class);
			if (list == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("list", list);
				new1yfbKcglYpkwlService.delete(msg, result);
			}
			break;
		case "query":
			if (json == null || "".equals(json)) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				ypkw = JSONObject.parseObject(json, Yfb_ypkwModel.class);
				msg.getParam().put("ypkw", ypkw);
				new1yfbKcglYpkwlService.query(msg, result);
			}
			break;
		}

	}

}
