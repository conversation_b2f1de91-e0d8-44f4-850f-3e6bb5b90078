package com.supx.web.business.invocation.ykgl.kfyw;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.ykgl.kfyw.pojo.Ykb_ckdModel;
import com.supx.csp.api.ykgl.kfyw.pojo.Ykb_ckdmxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.comm.pojo.DataGrid;
import com.supx.web.business.service.ykgl.kfyw.iface.IYkgKfywCkdService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.supx.comm.util.RequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoYkglKfywCkdAction
* @Description: TODO(库房维护)
* <AUTHOR>
* @date 2020年5月10日 下午11:29:40
 */
@Controller("INVO_New1YkglKfywCkd")
public class InvoYkglKfywCkdAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoYkglKfywCkdAction.class);

	@Autowired
	IYkgKfywCkdService new1ykgKfywCkdService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types",""); //操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types",""); //操作类型
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");         //排序方式
		String ksbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"qxksbm","");         //科室
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getParam().put("ksbm",ksbm);//科室编码

		DataGrid dGrid;
		if(dg.isEmpty()){
			dGrid = new DataGrid();
		} else {
			dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
		}

		switch (optType) {
		case "save"://提交所有药品
			Object jsonObject = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", jsonObject);
			new1ykgKfywCkdService.savebatch(msg, result);
			break;
		case "modify"://提交所有药品（或者修改）
			Object object = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", object);
			new1ykgKfywCkdService.modify(msg,result);
			break;
		case "ckcx"://出库冲销
			Object ckobject = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", ckobject);
			new1ykgKfywCkdService.ckcx(msg,result);
			break;
		case "queryCkd"://加载出库单列表信息
			if(parm.equals("") || parm==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
	            return;
			}else{
				Ykb_ckdModel ckd=JSONObject.parseObject(parm, Ykb_ckdModel.class);
				msg.getParam().put("ckd", ckd);
				new1ykgKfywCkdService.queryCkd(msg, result);
			}
			break;
		case "queryCkdMxByRkdh"://选中单据信息加载出相对应的单据内容明细
			if(parm.equals("") || parm==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
	            return;
			}else{
				Ykb_ckdmxModel ckdmx=JSONObject.parseObject(parm, Ykb_ckdmxModel.class);
				msg.getParam().put("ckdmx", ckdmx);
				new1ykgKfywCkdService.queryCkdMxByRkdh(msg, result);
			}
			break;
		case "zfrkd"://作废
			Ykb_ckdModel zfmodel = RequestUtil.getObjParamter(invoContext.getRequest(),Ykb_ckdModel.class);
			if (zfmodel == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
	            return;
			}else{
				msg.getParam().put("bean", zfmodel);
				new1ykgKfywCkdService.zfrkd(msg, result);
			}
			break;
		case "confuseCkd"://拒绝
			Ykb_ckdModel model = RequestUtil.getObjParamter(invoContext.getRequest(),Ykb_ckdModel.class);
			if (model == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
	            return;
			}else{
				msg.getParam().put("bean", model);
				new1ykgKfywCkdService.confuseCkd(msg, result);
			}
			break;
		case "shrkd"://入库审核
			Ykb_ckdModel shmodel = RequestUtil.getObjParamter(invoContext.getRequest(),Ykb_ckdModel.class);
			if (shmodel == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
	            return;
			}else{
				msg.getParam().put("bean", shmodel);
				new1ykgKfywCkdService.shrkd(msg, result);
			}
			break;
		case "yflysh"://入库审核
			Ykb_ckdModel yfshmodel = RequestUtil.getObjParamter(invoContext.getRequest(),Ykb_ckdModel.class);
			if (yfshmodel == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
	            return;
			}else{
				msg.getParam().put("bean", yfshmodel);
				new1ykgKfywCkdService.yflysh(msg, result);
			}
			break;
		case "updateMx"://修改明细
			Ykb_ckdmxModel ckdmxModel = RequestUtil.getObjParamter(invoContext.getRequest(),Ykb_ckdmxModel.class);
			if (ckdmxModel == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
	            return;
			}else {
				msg.getParam().put("bean", ckdmxModel);
				new1ykgKfywCkdService.updateMx(msg, result);
			}
			break;
		case "print"://出库打印
			if(parm.equals("") || parm==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
	            return;
			}else{
				Ykb_ckdModel ckd=JSONObject.parseObject(parm, Ykb_ckdModel.class);
				msg.getParam().put("ckd", ckd);
				new1ykgKfywCkdService.ckPrint(msg, result);
			}
			break;
		case "updateDycs": //修改打印次数
			Ykb_ckdModel ckd=JSONObject.parseObject(parm, Ykb_ckdModel.class);
			msg.getParam().put("ckd", ckd);
			new1ykgKfywCkdService.updateDycs(msg, result);
			break;
		}
	}

}
