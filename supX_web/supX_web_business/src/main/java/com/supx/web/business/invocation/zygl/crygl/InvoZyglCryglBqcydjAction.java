package com.supx.web.business.invocation.zygl.crygl;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_BqcydjModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.crygl.iface.IZyglCryglBqcydjService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1ZyglCryglBqcydj")
public class InvoZyglCryglBqcydjAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyglCryglBqcydjAction.class);

    @Autowired
    private IZyglCryglBqcydjService zyglCryglBqcydjService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyglCryglBqcydjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        msg.getUserinfo().put("userinfo", userinfo);//用户信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息

        Object obj = null;

        try {
            switch (optType) {
                case "query":
                    //接收对象
                    Zyb_BqcydjModel bean = JSONObject.parseObject(parm, Zyb_BqcydjModel.class);
                    msg.getParam().put("bean", bean);
                    zyglCryglBqcydjService.query(msg, result);
                    break;
                case "insert":

                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可停的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可作废的医嘱信息");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);

                    zyglCryglBqcydjService.insert(msg, result);
                    break;
                case "update":
                    Zyb_BqcydjModel updatebean = JSONObject.parseObject(parm, Zyb_BqcydjModel.class);
                    msg.getParam().put("bean", updatebean);
                    zyglCryglBqcydjService.update(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
        }
    }

}
