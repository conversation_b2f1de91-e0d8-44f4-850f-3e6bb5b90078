package com.supx.web.business.invocation.jsginterfaceGzgw;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.jsginterfaceGzgw.pojo.Gzgw_daxxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.jsginterfaceGzgw.iface.IGzgwdaxxInterfaceService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1GzgwdaxxInterface")
public class InvoGzgwdaxxInterfaceAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoGzgwdaxxInterfaceAction.class);

	@Autowired
	private IGzgwdaxxInterfaceService gzgwdaxxInterfaceService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoGzgwcsInterfaceAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		getSession(invoContext,result);
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型 调用医保交易为S，否则自已定义
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");    //参数
		Gzgw_daxxModel gzgwda = null ;
		if  (parm == null || parm.equals("")){
			//参数为空时，为post参数调用，
			gzgwda = (Gzgw_daxxModel)RequestUtil.getObjParamter(invoContext.getRequest(),Gzgw_daxxModel.class);
		}else {
			gzgwda = (Gzgw_daxxModel)JSONObject.parseObject(parm, Gzgw_daxxModel.class);
		}
		//获取用户信息
		msg.getUserinfo().put("userinfo", userinfo);
		msg.getParam().put("bean", gzgwda);
		switch (optType) {
		case "insert"://保存
			if (gzgwda == null) {
				result.setLogicCode(ERROR);
				result.setC("保存时对象不能为空");
				return;
			}
			if (gzgwda.getDah() == null || gzgwda.getDah().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时档案号不能为空");
				return;
			}
			if (gzgwda.getSfzh() == null || gzgwda.getSfzh().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时身份证号不能为空");
				return;
			}
			gzgwdaxxInterfaceService.insert(msg, result);
			break;
		case "query"://查询
			if (gzgwda == null) {
				result.setLogicCode(ERROR);
				result.setC("查询时对象不能为空");
				return;
			}
			gzgwdaxxInterfaceService.query(msg, result);
			break;
		case "delete"://删除
			if (gzgwda == null) {
				result.setLogicCode(ERROR);
				result.setC("删除时对象不能为空");
				return;
			}
			gzgwdaxxInterfaceService.delete(msg, result);
			break;
		case "update"://修改
			if (gzgwda == null) {
				result.setLogicCode(ERROR);
				result.setC("修改时对象不能为空");
				return;
			}
			gzgwda.setYljgbm(msg.getYljgbm());
			gzgwdaxxInterfaceService.update(msg, result);
			break;
		case "save"://保存
			if (gzgwda == null) {
				result.setLogicCode(ERROR);
				result.setC("保存时对象不能为空");
				return;
			}
			if (gzgwda.getDah() == null || gzgwda.getDah().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时档案号不能为空");
				return;
			}
			if (gzgwda.getSfzh() == null || gzgwda.getSfzh().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时身份证号不能为空");
				return;
			}
			gzgwdaxxInterfaceService.save(msg, result);
			break;
		default:
			break;
		}

	}

}
