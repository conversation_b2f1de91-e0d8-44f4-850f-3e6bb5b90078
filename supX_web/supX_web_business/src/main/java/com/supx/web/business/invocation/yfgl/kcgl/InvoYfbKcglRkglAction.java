package com.supx.web.business.invocation.yfgl.kcgl;

import com.alibaba.fastjson.JSONObject;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_rkdModel;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_rkdmxModel;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ypkcModel;
import com.supx.csp.api.yfgl.yfyw.pojo.Gyb_yfpdb;
import com.supx.csp.api.yfgl.yfyw.pojo.Gyb_yfpdbmx;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.UtilRequest;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.invocation.yfgl.yfyw.InvoYfbYfywBqbyAction;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglRkglService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

@Controller("INVO_New1YfbKcglRkgl")
public class InvoYfbKcglRkglAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYfbYfywBqbyAction.class);

	@Resource
	private IYfbKcglRkglService new1yfbKcglRkglService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 获取Session对象 userinfo用户信息
		getSession(invoContext, result);

		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
		String yfbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "yfbm", "");// 药房编码
		msg.getParam().put("userinfo", userinfo);
		msg.getParam().put("csqxinfo", csqxinfo);
		Object obj = null;

		switch (optType) {

		case "insertPddmx": //新增盘点单明细-建筑-药房
			List<Gyb_yfpdbmx> mxlist= (List<Gyb_yfpdbmx>) RequestUtil.getListParamter(invoContext.getRequest(), Gyb_yfpdbmx.class);
			msg.getParam().put("bean", mxlist);
			new1yfbKcglRkglService.insertPddmx(msg, result);
			break;
		case "insertPdd"://新增盘点单-建筑-药房
			Object pdobj = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", pdobj);
			new1yfbKcglRkglService.insertPddh(msg, result);
			break;
		case "queryYfKc"://查询药房库存-建筑-药房
			Yfb_ypkcModel yf = JSONObject.parseObject(parm, Yfb_ypkcModel.class);
			msg.getParam().put("obj", yf);
			new1yfbKcglRkglService.queryYfkc(msg, result);
			break;
		case "pdshrkd"://新增盘点单-建筑-药房-审核
			Object shobj = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", shobj);
			new1yfbKcglRkglService.shPddh(msg, result);
			break;
		case "queryYkypd":// 查询药库盘点单药房
			Gyb_yfpdb pd = JSONObject.parseObject(parm, Gyb_yfpdb.class);
			msg.getParam().put("pdb", pd);
			new1yfbKcglRkglService.queryYkypd(msg, result);
			break;
		case "queryPddMx":// 选中单据信息加载出相对应的单据内容明细药房
			Gyb_yfpdb pdb = JSONObject.parseObject(parm, Gyb_yfpdb.class);
			msg.getParam().put("pdb", pdb);
			new1yfbKcglRkglService.queryPddMx(msg, result);
			break;
		case "rkdcx":// 入库单查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_rkdModel rkd = JSONObject.parseObject(parm, Yfb_rkdModel.class);
				msg.getParam().put("bean", rkd);
				new1yfbKcglRkglService.queryRkd(msg, result);
			}
			break;
		case "allrkd"://所有入库单
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_rkdModel rkd = JSONObject.parseObject(parm, Yfb_rkdModel.class);
				msg.getParam().put("bean", rkd);
				new1yfbKcglRkglService.queryAllRkd(msg, result);
			}
			break;
		case "rkdmxcx":// 入库明细查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_rkdmxModel rkdmx = JSONObject.parseObject(parm, Yfb_rkdmxModel.class);
				msg.getParam().put("bean", rkdmx);
				new1yfbKcglRkglService.queryRkdmx(msg, result);
			}
			break;
		case "rkdmxtj":// 入库明细查询统计
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_rkdmxModel rkdmx = JSONObject.parseObject(parm, Yfb_rkdmxModel.class);
				msg.getParam().put("bean", rkdmx);
				new1yfbKcglRkglService.rkdmxCx(msg, result);
			}
			break;
		case "saverkd":// 保存入库单
			obj = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (obj == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", obj);
				msg.getParam().put("yfbm", yfbm);
				new1yfbKcglRkglService.SaveRkd(msg, result);
			}
			break;
		case "modify":
			obj = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (obj == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", obj);
				msg.getParam().put("yfbm", yfbm);
				new1yfbKcglRkglService.MoRkd(msg, result);
			}
			break;
		case "rkcx":
			obj = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (obj == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", obj);
				msg.getParam().put("yfbm", yfbm);
				new1yfbKcglRkglService.rkcx(msg, result);
			}
			break;
			case "shrkd":// 审核入库单
			obj = RequestUtil.getObjParamter(invoContext.getRequest());
			if (obj == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", obj);
				msg.getParam().put("yfbm", yfbm);
				new1yfbKcglRkglService.pzsh(msg, result);
			}
			break;
		case "zfrkd":// 作废入库单
			obj = RequestUtil.getObjParamter(invoContext.getRequest());
			if (obj == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", obj);
				msg.getParam().put("yfbm", yfbm);
				new1yfbKcglRkglService.pzzf(msg, result);
			}
			break;
		case "print":// 入库明细查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_rkdModel rkd = JSONObject.parseObject(parm, Yfb_rkdModel.class);
				msg.getParam().put("bean", rkd);
				new1yfbKcglRkglService.rkPrint(msg, result);
			}
			break;
		case "updateDycs"://更新打印次数
			Yfb_rkdModel rkd = JSONObject.parseObject(parm, Yfb_rkdModel.class);
			msg.getParam().put("bean", rkd);
			new1yfbKcglRkglService.updateDycs(msg, result);
			break;
		default:
			break;
		}

	}

}
