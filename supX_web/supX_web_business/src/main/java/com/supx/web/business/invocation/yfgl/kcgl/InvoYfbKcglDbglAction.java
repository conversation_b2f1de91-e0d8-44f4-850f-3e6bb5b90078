package com.supx.web.business.invocation.yfgl.kcgl;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ckdModel;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ckdmxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglDbglService;

import javax.annotation.Resource;

/**
 *
 * @ClassName: InvoYfbKcglDbglAction
 * @Description: TODO(调拨管理Action)
 * <AUTHOR> YK
 * @date 2020年8月31日 下午4:22:43
 *
 */
@Controller("INVO_New1YfbKcglDbgl")
public class InvoYfbKcglDbglAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志输出
	private final Logger logger = LoggerFactory.getLogger(InvoYfbKcglDbglAction.class);
	@Resource
	IYfbKcglDbglService new1yfbKcglDbglService;

	// 检验参数正确性
	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		// 判断操作类型是否为空
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbKcglDbglAction Interface| Requerst Parameter Validation Failed");
		}
	}

	// 根据参数执行不同操作
	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		String bean = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "bean", "");
		// 创建相应工具对象
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

		// 获取Session对象 userinfo用户信息
		getSession(invoContext, result);
		msg.getUserinfo().put("userinfo", userinfo);
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);

		// 根据操作类型执行
		switch (optType) {
		// 保存调拨单和明细
		case "saveDbdAndMx":
			Object objList = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("objList", objList);
			new1yfbKcglDbglService.saveDbd(result, msg);
			break;
		case "modify"://保存或者修改
			Object obj = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("objList", obj);
			new1yfbKcglDbglService.modify(result, msg);
			break;
		// 修改调拨单明细
		case "updateDbd":
			Yfb_ckdModel UDbd = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_ckdModel.class);
			msg.getParam().put("dbd", UDbd);
			new1yfbKcglDbglService.updateDbd(result, msg);
			break;
		// 修改调拨单明细
		case "updateMx":
			List<Yfb_ckdmxModel> list = (List<Yfb_ckdmxModel>) RequestUtil.getListParamter(invoContext.getRequest(),
					Yfb_ckdmxModel.class);
			msg.getParam().put("list", list);
			new1yfbKcglDbglService.updateDbdmx(result, msg);
			break;
		case "query":
			Yfb_ckdModel QCkd = JSONObject.parseObject(bean, Yfb_ckdModel.class);
			msg.getParam().put("dbd", QCkd);
			new1yfbKcglDbglService.queryDbd(result, msg);
			break;
		case "queryMx":
			Yfb_ckdModel ckdModelForMx = JSONObject.parseObject(bean, Yfb_ckdModel.class);
			msg.getParam().put("dbdForMx", ckdModelForMx);
			new1yfbKcglDbglService.queryDbdMx(result, msg);
			break;
		case  "zf":
			Yfb_ckdModel zfckd = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_ckdModel.class);
			if (zfckd == null || StringUtils.isBlank(zfckd.getCkdh())) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setResMsg("作废单号不能为空！");
				logger.info("InvoYfbKcglDbglAction Interface| Requerst Parameter Validation Failed");
				return;
			}
			msg.getParam().put("bean", zfckd);
			new1yfbKcglDbglService.zfDbd(result,msg);
			break;
		}

	}

}
