package com.supx.web.business.invocation.xtwh.ylfwxm;
import com.supx.comm.pojo.ResultParam;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.pojo.DataGrid;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.xtwh.ylfwxm.pojo.Gyb_zlxmglfyModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.xtwh.ylfwxm.iface.IXtwhYlfwxmZlxmglfyService;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoXtwhYlfwxmZlxmglfyAction
* @Description: TODO(诊疗项目关联费用)
* <AUTHOR>
* @date 2020年6月9日 下午6:22:15
*
 */
@Controller("INVO_New1XtwhYlfwxmZlxmglfy")
public class InvoXtwhYlfwxmZlxmglfyAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoXtwhYlfwxmZlxmglfyAction.class);

	@Autowired
	IXtwhYlfwxmZlxmglfyService new1xtwhYlfwxmZlxmglfyService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		Integer ref ;
		List<Gyb_zlxmglfyModel> beans;
		Gyb_zlxmglfyModel bean;
		ResultParam list;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String zlxmbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"zlxmbm","");
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"json","");
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		if (zlxmbm.equals("") || zlxmbm == null){
			zlxmbm = "%";
        }
		switch (optType) {
		case "save"://新增修改
			bean = RequestUtil.getObjParamter(invoContext.getRequest(),Gyb_zlxmglfyModel.class);
			if (bean == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				return;
			}
			msg.getParam().put("bean", bean);
			ref = new1xtwhYlfwxmZlxmglfyService.savebatch(msg, result);
			break;
		case "delete"://删除
			if (sjson == null || sjson.equals("")){
				beans = (List<Gyb_zlxmglfyModel>)RequestUtil.getListParamter(invoContext.getRequest(),Gyb_zlxmglfyModel.class);

			}else {
				beans = (List<Gyb_zlxmglfyModel>)JSONObject.parseArray(sjson, Gyb_zlxmglfyModel.class);
			}
			msg.getParam().put("bean", beans);
			ref = new1xtwhYlfwxmZlxmglfyService.deletebatch(msg, result);
			break;
		case "query"://查询全部
			String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
			DataGrid dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
			Gyb_zlxmglfyModel dbfabean;
			if(sjson.equals("")||sjson==null){
				dbfabean=new Gyb_zlxmglfyModel();
			}else{
				dbfabean=JSONObject.parseObject(sjson, Gyb_zlxmglfyModel.class);
			}
			UtilFun.DataGridInit(dGrid, "");
			dbfabean.setZlxmbm(zlxmbm);
			dbfabean.setRows(dGrid.getRows());
			dbfabean.setPage(dGrid.getPage());
			dbfabean.setSort(dGrid.getSort());
			dbfabean.setOrder(dGrid.getOrder());
			dbfabean.setParm(dGrid.getParm());
			msg.getParam().put("bean", dbfabean);
			list = new1xtwhYlfwxmZlxmglfyService.queryGyb_zlxmglfyList(msg, result);
			break;

		default:
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			break;
		}

	}

}
