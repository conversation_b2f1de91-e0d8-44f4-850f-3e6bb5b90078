package com.supx.web.business.invocation.jsginterfaceGzgw;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.jsginterfaceGzgw.iface.IGzgwServerInterfaceService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1GzgwServerInterface")
public class InvoGzgwServerInterfaceAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoGzgwServerInterfaceAction.class);

	@Autowired
	private IGzgwServerInterfaceService gzgwServerInterfaceService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        String operCode = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"operCode","");//operCode类型
        if (StringUtils.isBlank(operCode))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoGzgwServerInterfaceAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		getSession(invoContext,result);
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String operCode = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"operCode","");//操作类型 调用医保交易为S，否则自已定义
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");    //参数
		JSONObject obj = null;
		if (parm != null && !parm.equals("")) {
			obj = JSONObject.parseObject(parm);
		}else {
			obj = (JSONObject)RequestUtil.getObjParamter(invoContext.getRequest());
		}
		msg.getParam().put("operCode", operCode);
		msg.getParam().put("obj", obj);
		gzgwServerInterfaceService.doServer(msg, result);
	}

}
