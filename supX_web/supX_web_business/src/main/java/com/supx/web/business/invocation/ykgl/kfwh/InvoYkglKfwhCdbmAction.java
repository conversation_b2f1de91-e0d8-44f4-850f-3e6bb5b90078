package com.supx.web.business.invocation.ykgl.kfwh;
import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_cdbmModel;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_ksszlbmModel;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_sccdbmModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfwh.iface.IYkgKfwhCdbmService;
import com.supx.comm.util.RequestUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: InvoYkglKfwhCdbmAction
 * @Description: TODO(库房维护)
 * @date 2020年5月10日 下午11:29:40
 */
@Controller("INVO_New1YkglKfwhCdbm")
public class InvoYkglKfwhCdbmAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoYkglKfwhCdbmAction.class);

    @Autowired
    IYkgKfwhCdbmService new1ykgKfwhCdbmService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        // TODO Auto-generated method stub
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        Integer ref;
        List<Ykb_cdbmModel> beans;
        Ykb_cdbmModel bean;
        ResultParam list;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        String cdbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "cdbm", "");
        String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        if (cdbm == null) {
            cdbm = "";
        }

        switch (optType) {
            case "save":
                bean = RequestUtil.getObjParamter(invoContext.getRequest(), Ykb_cdbmModel.class);
                if (bean == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                msg.getParam().put("bean", bean);
                ref = new1ykgKfwhCdbmService.savebatch(msg, result);
                break;
            case "delete":
                if (sjson == null || sjson.equals("")) {
                    beans = (List<Ykb_cdbmModel>) RequestUtil.getListParamter(invoContext.getRequest(), Ykb_cdbmModel.class);
                } else {
                    beans = (List<Ykb_cdbmModel>) JSONObject.parseArray(sjson, Ykb_cdbmModel.class);
                }
                msg.getParam().put("bean", beans);
                ref = new1ykgKfwhCdbmService.deletebatch(msg, result);
                break;
            case "query":
                sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");
                Ykb_cdbmModel cdbmModel = (Ykb_cdbmModel) JSONObject.parseObject(sjson, Ykb_cdbmModel.class);
                msg.getParam().put("cdbm", cdbmModel);
                list = new1ykgKfwhCdbmService.queryYkbCdbmList(msg, result);
                break;
            case "querySccd":
                sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");
                Ykb_sccdbmModel sccdbmModel =  JSONObject.parseObject(sjson, Ykb_sccdbmModel.class);
                msg.getParam().put("cdbm", sccdbmModel);
                list = new1ykgKfwhCdbmService.queryYkbSccdbmList(msg, result);
                break;
            case "querykjy":
                sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");
                Ykb_ksszlbmModel ksszl = (Ykb_ksszlbmModel) JSONObject.parseObject(sjson, Ykb_ksszlbmModel.class);
                msg.getParam().put("bean", ksszl);
                list = new1ykgKfwhCdbmService.queryKszzlList(msg, result);
                break;
            default:
        }

    }

}
