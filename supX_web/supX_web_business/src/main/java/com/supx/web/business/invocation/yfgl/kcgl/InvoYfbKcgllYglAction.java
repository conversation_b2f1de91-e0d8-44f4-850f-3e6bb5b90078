package com.supx.web.business.invocation.yfgl.kcgl;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_sldModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglLyglService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 *
 * @ClassName: InvoYfbYfywBsglAction
 * @Description: 领药管理
 * <AUTHOR> YK
 * @date 2020年7月15日 上午1:10:47
 *
 */
@Controller("INVO_New1YfbKcglLygl")
public class InvoYfbKcgllYglAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYfbKcgllYglAction.class);

	@Resource
	private IYfbKcglLyglService new1yfbKcglLyglService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 获取Session对象 userinfo用户信息
		getSession(invoContext, result);
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
		msg.getParam().put("userinfo", userinfo);
		msg.getParam().put("csqxinfo", csqxinfo);
		Object object = null;
		switch (optType) {
		case "Sldcx":// 领药单查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_sldModel sld = JSONObject.parseObject(parm, Yfb_sldModel.class);
				msg.getParam().put("bean", sld);
				new1yfbKcglLyglService.querySld(msg, result);
			}
			break;
		case "sldmxcx":// 领药单明细查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_sldModel sld = JSONObject.parseObject(parm, Yfb_sldModel.class);
				msg.getParam().put("bean", sld);
				new1yfbKcglLyglService.querySldmx(msg, result);
			}
			break;
		case "queryYkSlmx":// 领药单明细查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_sldModel sld = JSONObject.parseObject(parm, Yfb_sldModel.class);
				msg.getParam().put("bean", sld);
				new1yfbKcglLyglService.queryYkSlmx(msg, result);
			}
			break;
		case "saveSld":// 保存领药单
			object = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (object == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", object);
				new1yfbKcglLyglService.SaveSld(msg, result);
			}
			break;
		case "ZfShSld":// 作废/审核领药单
			Yfb_sldModel objSh = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_sldModel.class);
			msg.getParam().put("obj", objSh);
			new1yfbKcglLyglService.pzzfsh(msg, result);
			break;
		//更新出库标志
		case "updateCkbz":
			Yfb_sldModel ckbz = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_sldModel.class);
			msg.getParam().put("obj", ckbz);
			new1yfbKcglLyglService.ckbzSh(msg, result);
			break;
		case "updateDycs":
			Yfb_sldModel sld = JSONObject.parseObject(parm, Yfb_sldModel.class);
			msg.getParam().put("bean", sld);
			new1yfbKcglLyglService.updateDycs(msg, result);
			break;
		case "zdscLydsj":
			String zdscparam = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "sld", "");
			Yfb_sldModel zdsc  = JSONObject.parseObject(zdscparam, Yfb_sldModel.class);
			msg.getParam().put("bean",zdsc);
			new1yfbKcglLyglService.getZdscLyd(msg, result);
			break;
		case "sdscLydsj":
			String sdscparam = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "sld", "");
			Yfb_sldModel sdsc  = JSONObject.parseObject(sdscparam, Yfb_sldModel.class);
			msg.getParam().put("bean",sdsc);
			new1yfbKcglLyglService.getSdscLyd(msg, result);
			break;
		case "getZdhcscLyd":
			String zdhcscparam = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "sld", "");
			Yfb_sldModel hcsc  = JSONObject.parseObject(zdhcscparam, Yfb_sldModel.class);
			msg.getParam().put("bean",hcsc);
			new1yfbKcglLyglService.getZdhcscLyd(msg, result);
			break;
		}
	}
}
