package com.supx.web.business.invocation.yfgl.kcgl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.BaseInvocation;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;
import com.supx.csp.api.pubfun.pojo.DropDown.Yfb_YfpckcDddwModel;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_pdbModel;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_pdblrModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglPdglService;
import com.supx.web.business.util.excel.PoiExcelExportUtil;

/**
 *
 * @ClassName: InvoYfbKcglPdglAction
 * @Description: (盘点管理)
 * <AUTHOR> YK
 * @date 2020年9月4日 下午5:52:06
 *
 */
@Controller("INVO_New1YfbKcglPdgl")
public class InvoYfbKcglPdglAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志工具
	private static final Logger logger = LoggerFactory.getLogger(InvoYfbKcglPdglAction.class);

	@Resource
	private IYfbKcglPdglService new1yfbKcglPdglService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void validateLogin(InvocationContext invoContext, InvocationResult result) {

	}

	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
		Yfb_pdbModel pdb;
		// 创建请求工具对象
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

		// 获取保存用户和权限信息
		getSession(invoContext, result);
		msg.getUserinfo().put("userinfo", userinfo);
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);

		// 根据操作类型执行不同操作
		switch (optType) {
		// 生成盘点表
		case "makePdb":
			Yfb_YfpckcDddwModel kcObj = JSONObject.parseObject(json, Yfb_YfpckcDddwModel.class);
			if (kcObj == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("pdb", kcObj);
				new1yfbKcglPdglService.makePdList(msg, result);
			}
			break;
		// 保存盘点表
		case "savePd":
			Object objList = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (objList == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", objList);
				new1yfbKcglPdglService.savePD(msg, result);
			}
			break;
		// 保存盘点录入表
		case "savePdlr":
			Object objLrList = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (objLrList == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", objLrList);
				new1yfbKcglPdglService.savePDLR(msg, result);
			}
			break;
		// 查询盘点表
		case "queryPd":
			pdb = JSONObject.parseObject(json, Yfb_pdbModel.class);
			if (pdb == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("pdb", pdb);
				new1yfbKcglPdglService.qureryPd(msg, result);
			}
			break;
		// 查询盘点录入表
		case "queryPdlr":
			Yfb_pdblrModel pdblr = JSONObject.parseObject(json, Yfb_pdblrModel.class);
			if (pdblr == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("pdblr", pdblr);
				new1yfbKcglPdglService.qureryLr(msg, result);
			}
			break;
		// 查询盘点明细
		case "queryPdmx":
			Yfb_pdbModel pdbmx = JSONObject.parseObject(json, Yfb_pdbModel.class);
			if (pdbmx == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("pdbmx", pdbmx);
				new1yfbKcglPdglService.qureryMx(msg, result);
			}
			break;
		// 查询盘点录入明细
		case "queryPdlrmx":
			Yfb_pdblrModel pdblrmx = JSONObject.parseObject(json, Yfb_pdblrModel.class);
			if (pdblrmx == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("pdblrmx", pdblrmx);
				new1yfbKcglPdglService.qureryLrmx(msg, result);
			}
			break;
		// 保存盘点表
		case "updateScsl":
			Object obj = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (obj == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", obj);
				new1yfbKcglPdglService.upatePdbmx(msg, result);
			}
			break;
		// 导出盘点表
		case "export":
			Yfb_YfpckcDddwModel bbObj = JSONObject.parseObject(json, Yfb_YfpckcDddwModel.class);
			if (bbObj == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglPdglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("pdb", bbObj);
				new1yfbKcglPdglService.makePdList(msg, result);
				if (result.getA().equals("1")) {
					break;
				}
				// 报表导出
				List<Yfb_YfpckcDddwModel> pdscList = (List<Yfb_YfpckcDddwModel>) result.getD();
				// 创建一个workbook 对应一个excel应用文件
				// 表头数组
				String[] titles = { "序号", "药品编码", "药品名称", "规格", "药品批号", "库存数量", "实存数量", "药房单位", "进价", "进价金额", "零价",
						"零价金额", "生产批号", "生产日期", "有效期至", "库房单位", "分装比例", "库位", "产地", "供货单位" };// 表头数组
				String sheetname = "盘点表";// 表名
				XSSFWorkbook workBook = new XSSFWorkbook();
				// 在workbook中添加一个sheet,对应Excel文件中的sheet
				XSSFSheet sheet = workBook.createSheet(sheetname);
				PoiExcelExportUtil exportUtil = new PoiExcelExportUtil(workBook, sheet);
				XSSFCellStyle headStyle = exportUtil.getHeadStyle();
				XSSFCellStyle bodyStyle = exportUtil.getBodyStyle();
				// 构建表头
				XSSFRow headRow = sheet.createRow(0);
				XSSFCell cell = null;
				for (int i = 0; i < titles.length; i++) {
					cell = headRow.createCell(i);
					cell.setCellStyle(headStyle);
					cell.setCellValue(titles[i]);
				}
				// 构建表体数据
				if (pdscList != null && pdscList.size() > 0) {
					// 循环给对应列赋值
					for (int j = 0; j < pdscList.size(); j++) {
						XSSFRow bodyRow = sheet.createRow(j + 1);
						Yfb_YfpckcDddwModel pdscObj = pdscList.get(j);

						// 序号
						cell = bodyRow.createCell(0);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(j + 1);
						// 药品编码
						cell = bodyRow.createCell(1);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getYpbm());
						// 药品名称
						cell = bodyRow.createCell(2);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getYpmc());
						// 规格
						cell = bodyRow.createCell(3);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getYpgg());
						// 药品批号
						cell = bodyRow.createCell(4);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getXtph());
						// 库存数量
						cell = bodyRow.createCell(5);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getKcsl());
						// 实存数量
						cell = bodyRow.createCell(6);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue("");
						// 药房单位
						cell = bodyRow.createCell(7);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getYfdwmc());
						// 进价
						Double ypjj = pdscObj.getYpjj() == null ? 0.0 : pdscObj.getYpjj();
						cell = bodyRow.createCell(8);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ypjj);
						// 进价金额
						Double jjje = pdscObj.getYpjjje() == null ? 0.0 : pdscObj.getYpljje();
						cell = bodyRow.createCell(9);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(jjje);
						// 零价
						Double yplj = pdscObj.getYplj() == null ? 0.0 : pdscObj.getYplj();
						cell = bodyRow.createCell(10);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(yplj);
						// 零价金额
						Double ljje = pdscObj.getYpljje() == null ? 0.0 : pdscObj.getYpljje();
						cell = bodyRow.createCell(11);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ljje);
						// 生产批号
						cell = bodyRow.createCell(12);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getScph());
						// 生产日期
						cell = bodyRow.createCell(13);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getScrq());
						// 有效期至
						cell = bodyRow.createCell(14);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getYxqz());
						// 库房单位
						cell = bodyRow.createCell(15);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getKfdw());
						// 分装比例
						cell = bodyRow.createCell(16);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getFzbl());
						// 库位
						cell = bodyRow.createCell(17);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue("");
						// 产地
						cell = bodyRow.createCell(18);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getCdmc());
						// 供货单位
						cell = bodyRow.createCell(19);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(pdscObj.getGhdwmc());
					}
				}

				String filename = null;
				try {
					filename = URLEncoder.encode("盘点生成表.xls", "utf-8");
				} catch (UnsupportedEncodingException e1) {
					e1.printStackTrace();
				} // 解决中文文件名下载后乱码的问题
				HttpServletResponse resp = invoContext.getResponse();
				resp.setCharacterEncoding("utf-8");
				resp.setHeader("Content-Disposition", "attachment; filename=" + filename + "");

				try {
					workBook.write(resp.getOutputStream());
				} catch (IOException e) {
					e.printStackTrace();
				}

				result.setD(workBook);
				result.setLogicCode(SUCCESS);

			}
			break;
		}

	}

}
