
package com.supx.web.business.invocation.yfgl.cxtj;
import com.supx.comm.pojo.ResultParam;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.List;

import com.supx.comm.pojo.DataGrid;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_kccxModel;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_yppfModel;
import com.supx.csp.api.ykgl.crcx.pojo.YkglCrcxMxtzResModel;
import com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.cxtj.iface.IYfgCxtjAllService;
import com.supx.web.business.util.excel.PoiExcelExportUtil;

/**
 *
 * @ClassName: InvoYfbKcglKccxAction
 * @Description: (药库统计报表控制操作)
 * <AUTHOR>
 * @date 2020年9月29日 下午10:02:54
 *
 */
@Controller("INVO_New1YfbCxtjAll")
public class InvoYfbCxtjAllAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志工具
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	private IYfgCxtjAllService new1yfgKcglKccxService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", ""); // 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void validateLogin(InvocationContext invoContext, InvocationResult result) {

	}

	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		getSession(invoContext, result);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", ""); // 操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", ""); // 操作参数
		String wyid = RequestUtil.getStrParamter(invoContext.getRequest(), "id");
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		Yfb_kccxModel ypkc = null;
		YkglCrcxMxtzResModel mxtz;
		Ykb_ykybbModel ybb;
		String dg;
		DataGrid dGrid;
		// 按操作类型不同执行不同操作
		switch (optType) {
		// 库存查询
		case "kc":
			ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
			// 非空判断
			if (ypkc == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", ypkc);
				new1yfgKcglKccxService.queryKccx(msg, result);
			}
			break;
			// 库存财富查询
			case "getTotalAmount":
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.getTotalAmount(msg, result);
				}
				break;
			case "updatePd": //开启盘点
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.updatePd(msg, result);
				}
				break;
			case "selectPdlb": //查询盘点列表
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.selectPdlb(msg, result);
				}
				break;
			case "updatePdYpKc": //操作员盘点录入
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.updatePdYpKc(msg, result);
				}
				break;
            case "updatePdwc": //审核盘点完成
                ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
                // 非空判断
                if (ypkc == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                } else {
                    msg.getParam().put("bean", ypkc);
                    new1yfgKcglKccxService.updatePdwc(msg, result);
                }
                break;
			case "insertPdpy": //盘点新增药品
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.insertPdpy(msg, result);
				}
				break;
			case "selectPdMxlb": //盘点列表（新）
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.selectPdMxlb(msg, result);
				}
				break;
			case "updatePdcsh": //盘点初始化
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.updatePdcsh(msg, result);
				}
				break;
			case "updatePdmxYpKc": //盘点录入（新）
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.updatePdmxYpKc(msg, result);
				}
				break;
			case "insertPdmx": //盘点新增药品(新)
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.insertPdmx(msg, result);
				}
				break;
			case "deletePdmx": //盘点删除药品(新)
				if(StringUtils.isBlank(wyid)){
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				}else{
					msg.getParam().put("wyid", wyid);
					new1yfgKcglKccxService.deletePdmx(msg, result);
				}
				break;
			case "updateMxPdwc": //审核盘点完成
				ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
				// 非空判断
				if (ypkc == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
				} else {
					msg.getParam().put("bean", ypkc);
					new1yfgKcglKccxService.updateMxPdwc(msg, result);
				}
				break;
			// 销售查询
		case "xs":
			ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
			// 非空判断
			if (ypkc == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", ypkc);
				new1yfgKcglKccxService.queryXscx(msg, result);
			}
			break;

		// 历史库存
		case "lskc":
			ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
			// 非空判断
			if (ypkc == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", ypkc);
				new1yfgKcglKccxService.queryLskc(msg, result);
			}
			break;

		// 明细台帐
		case "mxtz":
			mxtz = JSONObject.parseObject(parm, YkglCrcxMxtzResModel.class);
			// 非空判断
			if (mxtz == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", mxtz);
				new1yfgKcglKccxService.queryMxtz(msg, result);
			}
			break;

		// 领药查询
		case "ly":
			ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
			// 非空判断
			if (ypkc == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", ypkc);
				new1yfgKcglKccxService.queryLycx(msg, result);
			}
			break;
		// 效期、批次停用修改
		case "update":
			mxtz = JSONObject.parseObject(parm, YkglCrcxMxtzResModel.class);
			// 非空判断
			if (mxtz == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", mxtz);
				new1yfgKcglKccxService.updateXqAndPcty(msg, result);
			}
			break;
		// 领药查询
		case "ybb":
			ybb = JSONObject.parseObject(parm, Ykb_ykybbModel.class);
			// 非空判断
			if (ybb == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", ybb);
				new1yfgKcglKccxService.queryYbb(msg, result);
			}
			break;
		// 库存报表导出
		case "exportKc":
			ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
			// 非空判断
			if (ypkc == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", ypkc);
				msg.getParam().put("path", ypkc.getParm());
				ypkc.setParm(null);
				new1yfgKcglKccxService.exportKc(msg, result);

				// 库存导出
				String yfmc = ypkc.getYfmc();// 药房名称
				ResultParam resTem = (ResultParam) result.getD();
				List<Yfb_kccxModel> list = resTem.getList();
				String[] titles = { "序号", "药品编码", "药品名称", "药品规格", "药品种类编码", "药品种类名称", "库存数量", "药品进价", "药品零价", "药品批号",
						"有效期至", "药品产地编码", "药品产地名称", "药房单位编码", "药房单位名称", "分装比例" };
				String sheetname = "药房药品库存表";
				XSSFWorkbook workBook = new XSSFWorkbook();
				// 在workbook中添加一个sheet,对应Excel文件中的sheet
				XSSFSheet sheet = workBook.createSheet(sheetname);
				PoiExcelExportUtil exportUtil = new PoiExcelExportUtil(workBook, sheet);
				XSSFCellStyle headStyle = exportUtil.getHeadStyle();
				XSSFCellStyle bodyStyle = exportUtil.getBodyStyle();
				// 构建表头
				XSSFRow headRow = sheet.createRow(0);
				XSSFCell cell = null;
				for (int i = 0; i < titles.length; i++) {
					cell = headRow.createCell(i);
					cell.setCellStyle(headStyle);
					cell.setCellValue(titles[i]);
				}

				// 构建表体数据
				if (list != null && list.size() > 0) {
					for (int j = 0; j < list.size(); j++) {
						XSSFRow bodyRow = sheet.createRow(j + 1);
						Yfb_kccxModel ks = list.get(j);

						cell = bodyRow.createCell(0);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(j + 1);

						cell = bodyRow.createCell(1);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYpbm());

						cell = bodyRow.createCell(2);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYpmc());

						cell = bodyRow.createCell(3);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYpgg());

						cell = bodyRow.createCell(4);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYpzlbm());

						cell = bodyRow.createCell(5);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYpzlmc());

						cell = bodyRow.createCell(6);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getKcsl());

						cell = bodyRow.createCell(7);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYpjj().toString());

						cell = bodyRow.createCell(8);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYplj().toString());

						cell = bodyRow.createCell(9);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getScph());

						cell = bodyRow.createCell(10);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYxqz());

						cell = bodyRow.createCell(11);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getCdbm());

						cell = bodyRow.createCell(12);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getCdmc());

						cell = bodyRow.createCell(13);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYfdw());

						cell = bodyRow.createCell(14);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getYfdwmc());

						cell = bodyRow.createCell(15);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(ks.getFzbl());
					}
				}

				// 写文件
				String filename = null;
				try {
					filename = URLEncoder.encode(yfmc + "库存报表.xls", "utf-8");
				} catch (UnsupportedEncodingException e1) {
					e1.printStackTrace();
				} // 解决中文文件名下载后乱码的问题
				HttpServletResponse resp = invoContext.getResponse();
				resp.setCharacterEncoding("utf-8");
				resp.setHeader("Content-Disposition", "attachment; filename=" + filename + "");

				try {
					workBook.write(resp.getOutputStream());
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			break;
		// 库存查询
		case "sxyp":
			ypkc = JSONObject.parseObject(parm, Yfb_kccxModel.class);
			// 非空判断
			if (ypkc == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", ypkc);
				new1yfgKcglKccxService.queryYpsxcx(msg, result);
			}
			break;
		// 销售汇总导出
		case "exportXs":
			dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");// 排序方式
			dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
			ypkc = new Yfb_kccxModel();
			if (parm.equals("") || parm == null) {
				ypkc = new Yfb_kccxModel();
			} else {
				ypkc = (Yfb_kccxModel) JSONObject.parseObject(parm, Yfb_kccxModel.class);
			}
			UtilFun.DataGridInit(dGrid, "ypbm");
			ypkc.setRows(dGrid.getRows());
			ypkc.setPage(dGrid.getPage());
			ypkc.setSort(dGrid.getSort());
			ypkc.setOrder(dGrid.getOrder());
			ypkc.setYljgbm(dGrid.getParm());
			msg.setYljgbm(dGrid.getParm());
			msg.getParam().put("path", ypkc.getParm());
			ypkc.setParm(null);
			msg.getParam().put("bean", ypkc);
			new1yfgKcglKccxService.exportXs(msg, result);
			List<Yfb_yppfModel> ypList = (List<Yfb_yppfModel>) result.getD();
			String[] titles = { "发药日期", "药品编码", "药品名称", "药品规格", "药品种类", "处方用量", "退药数量" };// 表头数组
			String sheetname = "药品销售统计（按药品汇总）";// 表名
			// String path = (String) msg.getParam().get("path");// 文件存放绝对路径
			XSSFWorkbook workBook = new XSSFWorkbook();
			// 在workbook中添加一个sheet,对应Excel文件中的sheet
			XSSFSheet sheet = workBook.createSheet(sheetname);
			PoiExcelExportUtil exportUtil = new PoiExcelExportUtil(workBook, sheet);
			XSSFCellStyle headStyle = exportUtil.getHeadStyle();
			XSSFCellStyle bodyStyle = exportUtil.getBodyStyle();
			// 构建表头
			XSSFRow headRow = sheet.createRow(0);
			XSSFCell cell = null;
			for (int i = 0; i < titles.length; i++) {
				cell = headRow.createCell(i);
				cell.setCellStyle(headStyle);
				cell.setCellValue(titles[i]);
			}
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			// 构建表体数据
			if (ypList != null && ypList.size() > 0) {
				// 循环给对应列赋值
				for (int j = 0; j < ypList.size(); j++) {
					XSSFRow bodyRow = sheet.createRow(j + 1);
					Yfb_yppfModel obj = ypList.get(j);

					// 发药日期
					cell = bodyRow.createCell(0);
					cell.setCellStyle(bodyStyle);
					cell.setCellValue(df.format(obj.getFyrq()));
					// 药品编码
					cell = bodyRow.createCell(1);
					cell.setCellStyle(bodyStyle);
					cell.setCellValue(obj.getYpbm());
					// 药品名称
					cell = bodyRow.createCell(2);
					cell.setCellStyle(bodyStyle);
					cell.setCellValue(obj.getYpmc());
					// 药品规格
					cell = bodyRow.createCell(3);
					cell.setCellStyle(bodyStyle);
					cell.setCellValue(obj.getYpgg());
					// 药品种类
					cell = bodyRow.createCell(4);
					cell.setCellStyle(bodyStyle);
					cell.setCellValue(obj.getYpzlmc());
					// 处方用量
					cell = bodyRow.createCell(5);
					cell.setCellStyle(bodyStyle);
					cell.setCellValue(obj.getCfyl());
					// 退药数量
					cell = bodyRow.createCell(6);
					cell.setCellStyle(bodyStyle);
					if (obj.getTysl() != null) {
						cell.setCellValue(obj.getTysl());
					} else {
						cell.setCellValue(0);
					}

				}
			}

			String filename = null;
			try {
				filename = URLEncoder.encode("药品销售统计（按药品汇总）.xls", "utf-8");
			} catch (UnsupportedEncodingException e1) {
				e1.printStackTrace();
			} // 解决中文文件名下载后乱码的问题
			HttpServletResponse resp = invoContext.getResponse();
			resp.setCharacterEncoding("utf-8");
			resp.setHeader("Content-Disposition", "attachment; filename=" + filename + "");

			try {
				workBook.write(resp.getOutputStream());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			break;

		// 明细台帐
		case "exportMxtz":
			mxtz = JSONObject.parseObject(parm, YkglCrcxMxtzResModel.class);
			// 非空判断
			if (mxtz == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", mxtz);
				new1yfgKcglKccxService.queryMxtz(msg, result);
				ResultParam tempParam = (ResultParam) result.getD();
				List<YkglCrcxMxtzResModel> mxList = tempParam.getList();
				String[] titlesMx = { "序号", "药品编码", "药品名称", "期初数量", "入库数量", "出库数量", "销售数量", "结余数量", "损益数量" };// 表头数组
				String sheetnameMx = "药房明细台账";// 表名
				// String path = (String) msg.getParam().get("path");// 文件存放绝对路径
				XSSFWorkbook workBookMx = new XSSFWorkbook();
				// 在workbook中添加一个sheet,对应Excel文件中的sheet
				XSSFSheet sheetMx = workBookMx.createSheet(sheetnameMx);
				PoiExcelExportUtil exportUtilMx = new PoiExcelExportUtil(workBookMx, sheetMx);
				XSSFCellStyle headStyleMx = exportUtilMx.getHeadStyle();
				XSSFCellStyle bodyStyleMx = exportUtilMx.getBodyStyle();
				// 构建表头
				XSSFRow headRowMx = sheetMx.createRow(0);
				XSSFCell cellMx = null;
				for (int i = 0; i < titlesMx.length; i++) {
					cell = headRowMx.createCell(i);
					cell.setCellStyle(headStyleMx);
					cell.setCellValue(titlesMx[i]);
				}
				SimpleDateFormat dfMx = new SimpleDateFormat("yyyy-MM-dd");
				// 构建表体数据
				if (mxList != null && mxList.size() > 0) {
					// 循环给对应列赋值
					for (int j = 0; j < mxList.size(); j++) {
						XSSFRow bodyRowMx = sheetMx.createRow(j + 1);
						YkglCrcxMxtzResModel obj = mxList.get(j);
						Double qcsl = obj.getQcsl() == null ? 0 : obj.getQcsl();// 期初数量
						Double rksl = obj.getRksl() == null ? 0 : obj.getRksl();// 入库数量
						Double cksl = obj.getCksl() == null ? 0 : obj.getCksl();// 出库数量
						Double xssl = obj.getXssl() == null ? 0 : obj.getXssl();// 销售数量
						Double jysl = obj.getJysl() == null ? 0 : obj.getJysl();// 结余数量

						// 序号
						cell = bodyRowMx.createCell(0);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(j + 1);
						// 药品编码
						cell = bodyRowMx.createCell(1);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(obj.getYpbm());
						// 药品名称
						cell = bodyRowMx.createCell(2);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(obj.getYpmc());
						// 期初数量
						cell = bodyRowMx.createCell(3);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(qcsl);
						// 入库数量
						cell = bodyRowMx.createCell(4);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(rksl);

						// 出库数量
						cell = bodyRowMx.createCell(5);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(cksl);
						// 销售数量
						cell = bodyRowMx.createCell(6);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(xssl);
						// 结余数量
						cell = bodyRowMx.createCell(7);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(jysl);
						// 损益数量
						cell = bodyRowMx.createCell(8);
						cell.setCellStyle(bodyStyleMx);
						cell.setCellValue(qcsl + rksl - cksl - xssl - jysl);
					}
				}

				String filenameMx = null;
				try {
					filenameMx = URLEncoder.encode("药房明细台账.xls", "utf-8");
				} catch (UnsupportedEncodingException e1) {
					e1.printStackTrace();
				} // 解决中文文件名下载后乱码的问题
				HttpServletResponse respMx = invoContext.getResponse();
				respMx.setCharacterEncoding("utf-8");
				respMx.setHeader("Content-Disposition", "attachment; filename=" + filenameMx + "");

				try {
					workBookMx.write(respMx.getOutputStream());
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}

				ResultParam resMx = new ResultParam();
				result.setD(workBookMx);
				result.setLogicCode(SUCCESS);
			}
			break;

		}

	}

}
