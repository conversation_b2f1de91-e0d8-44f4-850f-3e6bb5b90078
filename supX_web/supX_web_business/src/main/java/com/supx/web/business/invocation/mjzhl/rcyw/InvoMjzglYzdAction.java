package com.supx.web.business.invocation.mjzhl.rcyw;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.supx.comm.pojo.DataGrid;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.util.DateTimeUtil;
import com.supx.csp.api.mjzhl.rcyw.pojo.Mjzgl_YzdModel;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.UtilRequest;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.invocation.mzys.zlgl.InvoMzysZlglBrjzAction;
import com.supx.web.business.service.mjzhl.rcyw.iface.IMjzglYzdService;
import com.supx.web.business.service.mjzhl.rcyw.iface.IMjzglYzzxService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Controller;

import java.util.List;

@Controller("INVO_New1MjzglYzd")
public class InvoMjzglYzdAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {
    private final Logger logger = LoggerFactory.getLogger(InvoMzysZlglBrjzAction.class);

    @Autowired
    IMjzglYzdService new1mjzglYzdService;

    @Autowired
    IMjzglYzzxService new1mjzglYzzxService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoMzysZlglBrjzAction 接口有问题：【获取types操作类型失败！】");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);
        Integer ref;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", ""); //操作类型
        String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");         //排序方式
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");     //入参数
        String ghxhParm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "ghxh", ""); //挂号序号
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        String[] ghxh = null;
        if (StringUtils.isNotEmpty(ghxhParm)){
            JSONArray array = JSONObject.parseArray(ghxhParm);
            ghxh = new String[array.size()];
            for (int i = 0; i < array.size(); i++) {
                ghxh[i] = array.getJSONObject(i).getString("ghxh");
            }
        }

        //以Json方式接收入参
        // New1ZyysYsywYzcl & yzsave
        Object obj = null;

        //实列化 排序Model
        DataGrid dGrid = null;
        if (StringUtils.isEmpty(dg)) {
            dGrid = new DataGrid();
        } else {
            dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
        }
        msg.getParam().put("userInfo", userinfo);
        //根据Types不同实现不同的接口操作
        try {
            switch (optType) {
                case "saveyzd"://新增开医嘱
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    msg.getParam().put("obj", obj);
                    ref = new1mjzglYzdService.insertyzd(msg, result);
                    break;
                case "queryYzd"://查询医嘱
                    Mjzgl_YzdModel yzdModel;
                    if (StringUtils.isEmpty(parm)){
                        yzdModel = new Mjzgl_YzdModel();
                    }else {
                        yzdModel = JSONObject.parseObject(parm, Mjzgl_YzdModel.class);
                    }
                    yzdModel.setGhxhs(ghxh);
                    msg.getParam().put("bean", yzdModel);
                    new1mjzglYzdService.selectyzdAll(msg, result);
                    break;
                case "queryZyyzMx": //查询中药医嘱明细
                    Mjzgl_YzdModel zymx;
                    if (StringUtils.isEmpty(parm)){
                        zymx = new Mjzgl_YzdModel();
                    }else {
                        zymx = JSONObject.parseObject(parm, Mjzgl_YzdModel.class);
                    }
                    msg.getParam().put("bean", zymx);
                    new1mjzglYzdService.queryZyyzMx(msg, result);
                    break;
                case "queryDshyz":// 查询待审核医嘱
                    Mjzgl_YzdModel dshyz = new Mjzgl_YzdModel();
                    dshyz.setShbz("0");
                    dshyz.setGhxhs(ghxh);
                    msg.getParam().put("bean", dshyz);
                    new1mjzglYzdService.selectyzd(msg, result);
                    break;
                case "queryYsYzd":// 查询医嘱单
                    yzdModel = JSONObject.parseObject(parm, Mjzgl_YzdModel.class);
                    msg.getParam().put("bean", yzdModel);
                    new1mjzglYzdService.selectyzd(msg, result);
                    break;
                case "queryDzxyz"://查询待执行医嘱
                    Mjzgl_YzdModel dzxyz = new Mjzgl_YzdModel();
                    dzxyz.setYzfl("1");//长期医嘱
                    dzxyz.setShbz("1");
                    dzxyz.setYstzbz("0");
                    dzxyz.setZxsj(DateTimeUtil.getDateBegin(new Date()));
                    dzxyz.setGhxhs(ghxh);
                    msg.getParam().put("bean", dzxyz);
                    new1mjzglYzdService.selectyzd(msg, result);
                    break;
                case "queryTzsh"://查询停嘱待审核
                    Mjzgl_YzdModel tzsh = new Mjzgl_YzdModel();
                    tzsh.setYstzbz("1");
                    tzsh.setHstzbz("0");
                    tzsh.setGhxhs(ghxh);
                    msg.getParam().put("bean", tzsh);
                    new1mjzglYzdService.selectyzd(msg, result);
                    break;
                case "yzsh": //护士审核医嘱
//                    List<Mjzgl_YzdModel> shList = (List<Mjzgl_YzdModel>) RequestUtil.getListParamter(invoContext.getRequest(), Mjzgl_YzdModel.class);
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    msg.getParam().put("obj", obj);
                    ref = new1mjzglYzdService.hsshyzyzd(msg, result);
                    break;
                case "yzzf"://医生作废医嘱单
                    obj =  RequestUtil.getListObjParamter(invoContext.getRequest());
                    msg.getParam().put("obj", obj);
                    ref = new1mjzglYzdService.yszfyzd(msg, result);
                    break;
                case "ystz": //医生停嘱医嘱单
                    obj =  RequestUtil.getListObjParamter(invoContext.getRequest());
                    msg.getParam().put("obj", obj);
                    ref = new1mjzglYzdService.ystzyzd(msg, result);
                    break;

                case "qxystz": //取消医生停嘱医嘱单
                    obj =  RequestUtil.getListObjParamter(invoContext.getRequest());
                    msg.getParam().put("obj", obj);
                    ref = new1mjzglYzdService.qxystzyzd(msg, result);
                    break;
                case "hstz": //护士停嘱医嘱医嘱单
                    List<Mjzgl_YzdModel> hstzList = (List<Mjzgl_YzdModel>) RequestUtil.getListParamter(invoContext.getRequest(), Mjzgl_YzdModel.class);
                    msg.getParam().put("bean", hstzList);
                    ref = new1mjzglYzdService.hstzshyzyzd(msg, result);
                    break;
                case "ysecqmyz": //医生二次签名医嘱医嘱单
                    List<Mjzgl_YzdModel> ysqmList = (List<Mjzgl_YzdModel>) RequestUtil.getListParamter(invoContext.getRequest(), Mjzgl_YzdModel.class);
                    msg.getParam().put("bean", ysqmList);
                    ref = new1mjzglYzdService.ysecqmyzyzd(msg, result);
                    break;
                case "hsecqmyz": //护士二次签名医嘱医嘱单
                    List<Mjzgl_YzdModel> hsqmList = (List<Mjzgl_YzdModel>) RequestUtil.getListParamter(invoContext.getRequest(), Mjzgl_YzdModel.class);
                    msg.getParam().put("bean", hsqmList);
                    ref = new1mjzglYzdService.hsecqmyzyzd(msg, result);
                    break;
                case "yzzx"://医嘱执行
                    // TODO: 2019/3/5
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    msg.getParam().put("obj", obj);
                    ref = new1mjzglYzdService.yzzx(msg, result);
                    break;
                default:
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(ERROR);
            result.setC(e.getMessage());
            logger.info("InvoGhglGhywBrghAction接口异常：" + e.getMessage());
        }
    }

}
