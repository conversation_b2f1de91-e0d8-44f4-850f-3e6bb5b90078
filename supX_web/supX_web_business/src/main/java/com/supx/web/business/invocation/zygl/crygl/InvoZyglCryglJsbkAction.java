package com.supx.web.business.invocation.zygl.crygl;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.pojo.IniConfig;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.Utilpubfun;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_jsbkModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.crygl.iface.IZyglCryglJsbkService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1ZyglCryglJsbk")
public class InvoZyglCryglJsbkAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {
    private final Logger logger = LoggerFactory.getLogger(InvoZyglCryglJsbkAction.class);

    @Autowired
    private IZyglCryglJsbkService zyglCryglJsbkService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        // TODO Auto-generated method stub
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        msg.getUserinfo().put("userinfo", userinfo);//用户信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
        msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息
        //取本地参数例子                                     //本地参数编码
        IniConfig iniconfig = Utilpubfun.getSysconfig(sysiniinfo, "000001");//业务窗口编码
        String ywckbh = iniconfig.getCsz();//业务窗口编码
        try {
            switch (optType) {
                case "query":
                    //接收对象
                    Zyb_jsbkModel bean = JSONObject.parseObject(parm, Zyb_jsbkModel.class);
                    msg.getParam().put("bean", bean);
                    zyglCryglJsbkService.query(msg, result);
                    break;
                case "insert":
                    Zyb_jsbkModel insertbean = JSONObject.parseObject(parm, Zyb_jsbkModel.class);
                    msg.getParam().put("bean", insertbean);
                    zyglCryglJsbkService.insert(msg, result);
                    break;
                case "update":
                    Zyb_jsbkModel updatebean = JSONObject.parseObject(parm, Zyb_jsbkModel.class);
                    msg.getParam().put("bean", updatebean);
                    zyglCryglJsbkService.update(msg, result);
                    break;
                case "delete":
                    Zyb_jsbkModel deletebean = JSONObject.parseObject(parm, Zyb_jsbkModel.class);
                    msg.getParam().put("bean", deletebean);
                    zyglCryglJsbkService.delete(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
        }
    }

}
