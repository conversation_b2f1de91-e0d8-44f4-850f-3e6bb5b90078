package com.supx.web.business.invocation.yfgl.yfyw;
import com.supx.comm.pojo.ResultParam;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_yppfModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.pojo.DataGrid;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.yfyw.iface.IYfbYfywCftyzfService;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @ClassName: InvoYfbYfywCftyzfAction
 * @Description: TODO(处方退药作废)
 * @date 2020年6月11日 上午1:27:47
 */
@Controller("INVO_New1YfbYfywCftyzf")
public class InvoYfbYfywCftyzfAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoYfbYfywCftyzfAction.class);
    @Resource
    private IYfbYfywCftyzfService new1yfbYfywCftyzfService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        // TODO Auto-generated method stub
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCftyzfAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);

        ResultParam list;
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        try {
            DataGrid dGrid;
            //根据类别处理参数
            Yfb_ypcfModel cfbean = null;
            Yfb_yppfModel pfbean = null;
            List<Yfb_yppfModel> pfList = new ArrayList<Yfb_yppfModel>();
            if (optType.substring(optType.length() - 4).equals("cfcx")) {//处方查询参数处理
                if (sjson.equals("") || sjson == null) {
                    cfbean = new Yfb_ypcfModel();
                } else {
                    cfbean = JSONObject.parseObject(sjson, Yfb_ypcfModel.class);
                }
                //查询时间判断
                if (cfbean.getCxrq() == null || cfbean.getCxrq().equals("")) {
                    cfbean.setCxrq("cfrq");
                }
                //处理分页信息
                if (cfbean.getPage() == 0) {
                    cfbean.setPage(1);
                }
                if (cfbean.getRows() == 0) {
                    cfbean.setRows(10);
                }
                if (cfbean.getSort().equals("") || cfbean.getSort() == null) {
                    cfbean.setSort("cfh");
                }
                if (cfbean.getOrder().equals("") || cfbean.getOrder() == null) {
                    cfbean.setOrder("asc");
                }
            } else if (optType.substring(optType.length() - 4).equals("pfcx")) {//配方查询参数处理
                if (sjson.equals("") || sjson == null) {
                    pfbean = new Yfb_yppfModel();
                } else {
                    pfbean = JSONObject.parseObject(sjson, Yfb_yppfModel.class);
                }
            } else if (optType.equals("cfzf") || optType.equals("cfzfsh")) {//作废审核等Post方法获取对象参数
                cfbean = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_ypcfModel.class);
            }
            msg.getParam().put("userinfo", userinfo);
            msg.getParam().put("csqxinfo", csqxinfo);
            switch (optType) {
                case "wfyzfcfcx"://未发药处方作废查询
                    msg.getParam().put("bean", cfbean);
                    list = new1yfbYfywCftyzfService.queryCfzfWfy(msg, result);
                    break;
                case "yfyzfcfcx"://已发药处方作废查询
                    msg.getParam().put("bean", cfbean);
                    list = new1yfbYfywCftyzfService.queryCfzfYfy(msg, result);
                    break;
                case "zfshcfcx"://作废审核处方查询
                    msg.getParam().put("bean", cfbean);
                    list = new1yfbYfywCftyzfService.queryZfsh(msg, result);
                    break;
                case "yppfcx"://配方查询
                    msg.getParam().put("bean", pfbean);
                    list = new1yfbYfywCftyzfService.queryYppf(msg, result);
                    break;
                case "cfzf"://处方作废
                    msg.getParam().put("bean", cfbean);
                    new1yfbYfywCftyzfService.UpdateCfzf(msg, result);
                    break;
                case "cfsh"://处方审核
                    Yfb_ypcfModel cftiaopei = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_ypcfModel.class);
                    msg.getParam().put("bean", cftiaopei);
                    new1yfbYfywCftyzfService.UpdateCfsh(msg, result);
                    break;
                case "cfzfsh"://处方作废审核
                    msg.getParam().put("bean", cfbean);
                    new1yfbYfywCftyzfService.UpdateCfzfsh(msg, result);
                    break;
                case "cftysh"://处方退药审核
                    msg.getParam().put("bean", cfbean);
                    new1yfbYfywCftyzfService.UpdateCftysh(msg, result);
                    break;
                case "tycfcx"://退发药处方作废查询
                    msg.getParam().put("bean", cfbean);
                    list = new1yfbYfywCftyzfService.queryCfty(msg, result);
                    break;
                case "cfty":
                    //接收POST方式list对象
                    pfList = (List<Yfb_yppfModel>) RequestUtil.getListParamter(invoContext.getRequest(), Yfb_yppfModel.class);
                    msg.getParam().put("bean", pfList);
                    new1yfbYfywCftyzfService.SaveCfty(msg, result);
                    break;
                default:
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    logger.info("InvoYfbYfywCftyzfAction Interface| Requerst Parameter Validation Failed");
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(ERROR);
            result.setC("处方退药作废异常:" + e.getMessage());
            logger.info("InvoYfbYfywCftyzfAction Interface| Requerst Parameter Validation Failed");
        }
    }

}
