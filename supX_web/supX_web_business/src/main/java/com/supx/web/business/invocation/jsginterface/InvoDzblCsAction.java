package com.supx.web.business.invocation.jsginterface;
import com.supx.comm.pojo.ResultParam;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import com.supx.comm.util.RequestUtil;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.jsginterface.pojo.Dsemr_setupModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.jsginterface.iface.IDsemr_setupService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1DzblCs")
public class InvoDzblCsAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoDzblCsAction.class);
	@Autowired
	private  IDsemr_setupService new1dsemr_setupService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        // TODO Auto-generated method stub
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InviHszByglCwglAction Interface| Requerst Parameter Validation Failed");
        }
    }
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		Integer ref;
        List<Dsemr_setupModel> beans;
        Dsemr_setupModel bean;
        ResultParam list;
		String optType=RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
		getSession(invoContext,result);
		//获取用户信息
		msg.getUserinfo().put("userinfo", userinfo);
		//获取参数信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);
		switch(optType){

		case "query":
			Dsemr_setupModel bzModel;
			if(sjson.equals("") || sjson==null){
				bzModel=new Dsemr_setupModel();
			}else{
				bzModel = JSONObject.parseObject(sjson, Dsemr_setupModel.class);
			}
             msg.getParam().put("bean", bzModel);
             list = new1dsemr_setupService.query(msg, result);
			break;
		//保存电子病历
		case "update":
			bean = (Dsemr_setupModel) JSONObject.parseObject(sjson, Dsemr_setupModel.class);
			 if (bean == null) {
                 result.setLogicCode(PARAM_VALIDATE_ERROR);
                 return;
             }
			msg.getParam().put("bean", bean);
			new1dsemr_setupService.update(msg, result);
			break;
		//删除电子病历
		case "delete":
			bean = (Dsemr_setupModel) JSONObject.parseObject(sjson, Dsemr_setupModel.class);
			msg.getParam().put("bean", bean);
			new1dsemr_setupService.delete(msg, result);
			break;
	}

	}

}
