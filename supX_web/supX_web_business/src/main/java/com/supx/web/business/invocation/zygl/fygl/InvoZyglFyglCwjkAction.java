package com.supx.web.business.invocation.zygl.fygl;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.RequestUtil;
import com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel;
import com.supx.csp.api.zygl.fygl.pojo.Zyb_cwjkModel;
import com.supx.csp.api.zygl.fygl.pojo.Zyb_cwjkMxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.fygl.iface.IZyglFyglCwjkService;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoMzsfSfjsCwjkAction
* @Description: TODO(住院财务交款)
* <AUTHOR>
* @date 2020年8月19日 下午10:03:33
*
 */
@Controller("INVO_New1ZyglFyglCwjk")
public class InvoZyglFyglCwjkAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoZyglFyglCwjkAction.class);

	@Autowired
	private IZyglFyglCwjkService new1zrglFyglCwjkService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyglFyglCwjkAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

		try{
			switch (optType) {
			case "save"://住院财务交款
				String ksbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"ksbm","");
				Zyb_cwjkModel cwjkModel=RequestUtil.getObjParamter(invoContext.getRequest(),Zyb_cwjkModel.class);
				msg.getParam().put("bean", cwjkModel);//对象
				msg.getParam().put("ksbm", ksbm);
				new1zrglFyglCwjkService.insert(msg, result);
				break;
			case "update"://住院取消财务交款
				Zyb_cwjkModel qxcwjkModel=RequestUtil.getObjParamter(invoContext.getRequest(),Zyb_cwjkModel.class);
				msg.getParam().put("bean", qxcwjkModel);//对象
				new1zrglFyglCwjkService.update(msg, result);
				break;
			case "operateCwjk"://操作门诊财务上交(财务)
				Zyb_cwjkModel operateCwjkModel = RequestUtil.getObjParamter(invoContext.getRequest(),Zyb_cwjkModel.class);
				msg.getParam().put("bean", operateCwjkModel);//对象
				new1zrglFyglCwjkService.operateCwjk(msg, result);
				break;
			case "queryCwjk"://分页查询财务交款
				//接收对象
				Zyb_cwjkModel bean = JSONObject.parseObject(parm,Zyb_cwjkModel.class);
				msg.getParam().put("bean", bean);//对象
				new1zrglFyglCwjkService.queryCwjk(msg, result);
				break;
			case "queryCwjkMsg"://查询财务交款所需要的信息
				//接收对象
				Zyb_cwjkModel msgbean = JSONObject.parseObject(parm,Zyb_cwjkModel.class);
				msg.getParam().put("bean", msgbean);//对象
				new1zrglFyglCwjkService.queryCwjkMsg(msg, result);
				break;
			case "queryBrfyFylb"://查询病人费用中按照费用类别分类查询的（住院交款的结算收入）
				Zyb_brfyModel brfybean = JSONObject.parseObject(parm,Zyb_brfyModel.class);
				msg.getParam().put("bean", brfybean);//对象
				new1zrglFyglCwjkService.queryBrfyFylb(msg, result);
				break;
			case "queryCwjkMsgByJkpzh"://根据交款凭证号查询住院交款需要展示的内容
				//String jkpzh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"jkpzh","");
				Zyb_cwjkModel jkbean=JSONObject.parseObject(parm,Zyb_cwjkModel.class);
				msg.getParam().put("bean", jkbean);//对象
				new1zrglFyglCwjkService.queryCwjkMsgByJkpzh(msg, result);
				break;
			case "queryCwjkMx"://交款之后根据交款凭证号查看财务交款明细（住院交款的结算收入）
				Zyb_cwjkMxModel jkmxBean=JSONObject.parseObject(parm,Zyb_cwjkMxModel.class);
				//String jkpzhMx = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"jkpzh","");
				msg.getParam().put("bean", jkmxBean);//对象
				new1zrglFyglCwjkService.queryCwjkMx(msg, result);
				break;
			default:
				break;
			}
		}catch(Exception e){
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
		}
	}
}
