package com.supx.web.business.invocation.zygl.crygl;

import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;

import com.supx.csp.api.ghgl.ghyw.pojo.Gyb_brjbxxModel;
import com.supx.csp.api.zygl.crygl.pojo.Gs_PkryjkModel;
import com.supx.csp.api.zygl.crygl.pojo.Gznh_qnz_bzff_xmbModel;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel;
import com.supx.comm.pojo.DataGrid;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.util.Utilpubfun;
import com.supx.comm.pojo.IniConfig;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.crygl.iface.IZyglCryglRydjService;
import com.supx.web.business.service.gslxsxzz.zr.SxzzzrService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: InvoZyglCryglRydjAction
 * @Description: TODO(入院登记)
 * @date 2020年6月13日 下午6:21:18
 */
@Controller("INVO_New1ZyglCryglRydj")
public class InvoZyglCryglRydjAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyglCryglRydjAction.class);

    @Autowired
    private IZyglCryglRydjService new1zyglCryglRydjService;
    @Autowired
    private SxzzzrService sxzzzrService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        ResultParam list;
        Zyb_rydjModel rydjbean;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
        String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");//排序方式
        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        //取本地参数例子                                     //本地参数编码
        IniConfig iniconfig = Utilpubfun.getSysconfig(sysiniinfo, "000001");//业务窗口编码
        String ywckbm = iniconfig.getCsz();//业务窗口编码
        String ywckmc = iniconfig.getCszmc();//业务窗口名称
        DataGrid dGrid;
        if (dg == null || dg.equals("")) {
            dGrid = new DataGrid();
        } else {
            dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
        }
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        try {
            switch (optType) {
                case "querrydj"://入院登记分页查询
                    if (sjson.equals("") || sjson == null) {
                        rydjbean = new Zyb_rydjModel();
                    } else {
                        rydjbean = JSONObject.parseObject(sjson, Zyb_rydjModel.class);
                    }

                    UtilFun.DataGridInit(dGrid, "zyh");
                    rydjbean.setRows(dGrid.getRows());
                    rydjbean.setPage(dGrid.getPage());
                    rydjbean.setSort(dGrid.getSort());
                    rydjbean.setOrder(dGrid.getOrder());
                    //检索
                    rydjbean.setParm(dGrid.getParm());
                    msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
                    msg.getParam().put("bean", rydjbean);
                    list = new1zyglCryglRydjService.queryRydj(msg, result);
                    break;
                case "queryMzry"://门诊入院
                    Gyb_brjbxxModel bean = null;
                    if (sjson.equals("") || sjson == null) {
                        bean = new Gyb_brjbxxModel();
                    } else {
                        bean = JSONObject.parseObject(sjson, Gyb_brjbxxModel.class);
                    }

                    msg.getParam().put("bean", bean);
                    list = new1zyglCryglRydjService.queryMzry(msg, result);
                    break;
                case "save"://保存（下面修改方法暂时未用，此操作包括了修改）
                    Zyb_rydjModel beans = RequestUtil.getObjParamter(invoContext.getRequest(), Zyb_rydjModel.class);
                    beans.setYwckbh(ywckbm);
                    beans.setYwckmc(ywckmc);
                    msg.getParam().put("beans", beans);
                    msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
                    msg.getUserinfo().put("user", userinfo);
                    if (null != beans.getIsvirtry() && beans.getIsvirtry().equals("1")) {
                        new1zyglCryglRydjService.insertRydjXn(msg, result);
                    } else {
                        new1zyglCryglRydjService.insertRydj(msg, result);
                    }
                    break;
                case "delete"://删除
                    List<Zyb_rydjModel> rydjList;
                    if (json == null || json.equals("")) {
                        rydjList = (List<Zyb_rydjModel>) RequestUtil.getListParamter(invoContext.getRequest(), Zyb_rydjModel.class);
                    } else {
                        rydjList = (List<Zyb_rydjModel>) JSONObject.parseArray(json, Zyb_rydjModel.class);
                    }
                    msg.getParam().put("beans", rydjList);
                    msg.getUserinfo().put("user", userinfo);
                    new1zyglCryglRydjService.deletebatchRydj(msg, result);
                    break;
                case "querrydjJs"://入院检索查询病人基本信息（编辑页面）
                    if (sjson.equals("") || sjson == null) {
                        rydjbean = new Zyb_rydjModel();
                    } else {
                        rydjbean = JSONObject.parseObject(sjson, Zyb_rydjModel.class);
                    }
                    msg.getParam().put("bean", rydjbean);
                    List<Zyb_rydjModel> jbxxList = new1zyglCryglRydjService.queryRydjJs(msg, result);
                    break;
                case "queryByZyh"://根据住院号查询
                    String zyh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "zyh", "");
                    msg.getParam().put("zyh", zyh);
                    Zyb_rydjModel rydjModel = new1zyglCryglRydjService.queryByZyh(msg, result);
                    break;
                case "selectByCybr": //查询出院病人的信息集合（主要针对于病案首页借阅登记）
                    if (sjson.equals("") || sjson == null) {
                        rydjbean = new Zyb_rydjModel();
                    } else {
                        rydjbean = JSONObject.parseObject(sjson, Zyb_rydjModel.class);
                    }
                    msg.getParam().put("bean", rydjbean);
                    new1zyglCryglRydjService.selectByCybr(msg, result);
                    break;
                case "gsspkrksb": //甘肃省贫困人口识别接口
                    Gs_PkryjkModel pkryjk;
                    if (sjson.equals("") || sjson == null) {
                        pkryjk = new Gs_PkryjkModel();
                    } else {
                        pkryjk = JSONObject.parseObject(sjson, Gs_PkryjkModel.class);
                    }
                    msg.getParam().put("bean", pkryjk);
                    msg.getUserinfo().put("user", userinfo);
                    new1zyglCryglRydjService.selectGspkrksb(msg, result);
                    break;
                case "update":
                    Zyb_rydjModel beanUpdate = RequestUtil.getObjParamter(invoContext.getRequest(), Zyb_rydjModel.class);
                    msg.getParam().put("bean", beanUpdate);
                    msg.getParam().put("czybm", userinfo.getCzybm());
                    new1zyglCryglRydjService.updateRydj(msg, result);
                    break;
                case "queryGspkrk"://查询甘肃贫困人口接口
                    Gs_PkryjkModel gsPkryjkModel = null;
                    if (sjson.equals("") || sjson == null) {
                        gsPkryjkModel = new Gs_PkryjkModel();
                    } else {
                        gsPkryjkModel = JSONObject.parseObject(sjson, Gs_PkryjkModel.class);
                    }
                    gsPkryjkModel = JSONObject.parseObject(sjson, Gs_PkryjkModel.class);
                    msg.getParam().put("bean", gsPkryjkModel);
                    msg.getUserinfo().put("user", userinfo);
                    new1zyglCryglRydjService.queryGspkrk(msg, result);
                    break;
                case "queryBrxx"://双向转诊转入查询brxx(甘肃临夏)
                    if (sjson.equals("") || sjson == null) {
                        result.setLogicCode(ERROR);
                        result.setResMsg("参数不能为空");
                        break;
                    }
                    String sfzjhm = JSONObject.parseObject(sjson).getString("sfzjhm");
                    String hisbm = JSONObject.parseObject(sjson).getString("hisbm");
                    String url = JSONObject.parseObject(sjson).getString("url");
                    if (StringUtils.isEmpty(sfzjhm)) {
                        result.setLogicCode(ERROR);
                        result.setResMsg("身份证号不能为空！");
                        break;
                    }
                    msg.getParam().put("sfzjhm", sfzjhm);
                    msg.getParam().put("hisbm", hisbm);
                    msg.getParam().put("url", url);
                    sxzzzrService.getbrxx(msg, result);
                    break;
                case "queryZcHzxx"://双向转诊转出查询患者信息(甘肃临夏)
                    if (sjson.equals("") || sjson == null) {
                        result.setLogicCode(ERROR);
                        result.setResMsg("参数不能为空");
                        break;
                    }
                    JSONObject object = JSONObject.parseObject(sjson);
                    msg.getParam().put("brid", object.getString("brid"));
                    msg.getParam().put("xh", object.getString("xh"));
                    msg.getParam().put("yljgbm", object.getString("yljgbm"));
                    msg.getParam().put("bz", object.getString("bz"));
                    msg.getParam().put("userinfo", userinfo);
                    sxzzzrService.getScbrxx(msg, result);
                    break;
                case "queryGznhJs"://贵州农合疾病检索基本信息
                    Gznh_qnz_bzff_xmbModel gznh = JSONObject.parseObject(sjson, Gznh_qnz_bzff_xmbModel.class);
                    gznh.setYljgbm(msg.getYljgbm());
                    msg.getParam().put("bean", gznh);
                    new1zyglCryglRydjService.queryGznhJs(msg, result);
                    break;
                case "queryMaxBah"://查询当前最大病案号
                    new1zyglCryglRydjService.queryMaxBah(msg, result);
                    break;
                case "queryIsBan"://查询当前病案号是否存在

                    Zyb_rydjModel zydjbah = JSONObject.parseObject(sjson, Zyb_rydjModel.class);
                    msg.getParam().put("bah", zydjbah);
                    new1zyglCryglRydjService.queryIsBan(msg, result);
                    break;
                case "sfsupdaye":
                    Zyb_rydjModel updatezydj = JSONObject.parseObject(sjson, Zyb_rydjModel.class);
                    msg.getParam().put("updatezydj", updatezydj);
                    new1zyglCryglRydjService.sfsupdaye(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
        }
    }
}
