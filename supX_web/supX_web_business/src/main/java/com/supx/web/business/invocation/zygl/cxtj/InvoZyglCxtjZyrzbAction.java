package com.supx.web.business.invocation.zygl.cxtj;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.xtwh.ksry.pojo.Gyb_yljgModel;
import com.supx.csp.api.zygl.cxtj.pojo.Zyb_zyrzbModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.service.xtwh.ksry.iface.IXtwhKsryYljgService;
import com.supx.web.business.service.zygl.cxtj.iface.IZyglCxtjZyrzbCxService;
import com.supx.web.business.util.excel.PoiExcelExportUtil;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoZyglCrygBrfyAction
 * @Description: TODO(住院日志簿)
 * @date 2020年6月22日 下午4:48:41
 */
@Controller("INVO_New1ZyglCxtjZyrzb")
public class InvoZyglCxtjZyrzbAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IZyglCxtjZyrzbCxService new1zyglCxtjZyrzbCxService;

    @Autowired
    private IXtwhKsryYljgService new1xtwhKsryYljgService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyglCrygBrfyCxAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void validateLogin(InvocationContext invoContext, InvocationResult result) {

    }

    @SuppressWarnings("unchecked")
    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        Zyb_zyrzbModel zyrzbModel = null;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        // 获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        msg.getUserinfo().put("userinfo", userinfo);// 用户信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);// 参数信息
        msg.getIniconfig().put("sysiniinfo", sysiniinfo);// 本地参数信息

        try {
            switch (optType) {
                // 住院日志登记簿查询
                case "queryZyrzb":
                    if (parm.equals("") || parm == null) {
                        result.setLogicCode(PARAM_VALIDATE_ERROR);
                        logger.info("InvoZyglCxtjZyrzbAction Interface| Requerst Parameter Validation Failed");
                    } else {
                        zyrzbModel = JSONObject.parseObject(parm, Zyb_zyrzbModel.class);
                        msg.getParam().put("bean", zyrzbModel);// 对象
                        new1zyglCxtjZyrzbCxService.exportRyrzb(msg, result);
                    }
                    break;
                // 住院日志登记簿导出
                case "exportZyrzb":
                    if (parm.equals("") || parm == null) {
                        result.setLogicCode(PARAM_VALIDATE_ERROR);
                        logger.info("InvoZyglCxtjZyrzbAction Interface| Requerst Parameter Validation Failed");
                    } else {
                        zyrzbModel = JSONObject.parseObject(parm, Zyb_zyrzbModel.class);
                        msg.getParam().put("bean", zyrzbModel);// 对象
                        new1zyglCxtjZyrzbCxService.exportRyrzb(msg, result);

                        // 报表导出

                        List<Zyb_zyrzbModel> rzList = (List<Zyb_zyrzbModel>) result.getD();
                        // 创建一个workbook 对应一个excel应用文件
                        String[] titles = {"序号", "入院号", "患者姓名", "性别", "年龄（岁）", "职业名称", "详细地址", "联系电话", "入院日期", "入院诊断",
                                "出院日期", "出院诊断", "出院状态", "住院天数", "传报时间", "管床医师", "体温"};// 表头数组
                        String sheetname = "门诊登记簿";// 表名
                        // 医疗机构名称

                        Gyb_yljgModel bean = new1xtwhKsryYljgService.queryGyb_yljgOne(zyrzbModel.getYljgbm(), result);
                        String yljgmc = bean.getJgmc();
                        yljgmc += "住院登记簿";

                        XSSFWorkbook workBook = new XSSFWorkbook();
                        XSSFSheet sheet = workBook.createSheet(sheetname);
                        PoiExcelExportUtil exportUtil = new PoiExcelExportUtil(workBook, sheet);
                        XSSFCellStyle headStyle = exportUtil.getHeadStyle();
                        XSSFCellStyle bodyStyle = exportUtil.getBodyStyle();

                        // 构建表头
                        XSSFRow headRow = sheet.createRow(0);// 新行
                        headRow = sheet.createRow(0);
                        headRow.setHeight((short) (30 * 20));// 行高
                        XSSFCell cell = null;
                        XSSFCellStyle cellStyle = workBook.createCellStyle();
                        XSSFFont font = workBook.createFont();// 字体
                        font.setBold(true);
                        font.setFontName("黑体");
                        font.setFontHeightInPoints((short) 18);
                        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 居中
                        cellStyle.setVerticalAlignment((short) 0);// 水平居中
                        cellStyle.setFont(font);//

                        CellRangeAddress craHead = new CellRangeAddress(0, 0, 0, 18);
                        sheet.addMergedRegion(craHead);
                        cell = headRow.createCell(0);
                        cell.setCellStyle(cellStyle);
                        cell.setCellValue(yljgmc);

                        // 创建标题行
                        XSSFRow headRow1 = sheet.createRow(1);
                        for (int i = 0; i < titles.length; i++) {
                            cell = headRow1.createCell(i);
                            cell.setCellStyle(headStyle);
                            cell.setCellValue(titles[i]);
                        }
                        SimpleDateFormat fm = new SimpleDateFormat("yyyy-MM-dd");
                        // 构建表体数据
                        if (rzList != null && rzList.size() > 0) {
                            // 循环给对应列赋值
                            int j = 0;
                            int line = 2;
                            for (; j < rzList.size(); j++) {
                                XSSFRow bodyRow = sheet.createRow(line++);
                                Zyb_zyrzbModel obj = rzList.get(j);
                                // 序号
                                cell = bodyRow.createCell(0);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(j + 1);

                                // 入院号
                                cell = bodyRow.createCell(1);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getZyh());
                                // 患者姓名
                                cell = bodyRow.createCell(2);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getBrxm());
                                // 性别
                                cell = bodyRow.createCell(3);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getBrxb());
                                // 年龄（岁）
                                cell = bodyRow.createCell(4);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getBrnl());
                                // 职业名称
                                cell = bodyRow.createCell(5);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getZymc());
                                // 详细地址
                                cell = bodyRow.createCell(6);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getJzdmc());
                                // 联系电话
                                cell = bodyRow.createCell(7);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getSjhm());
                                // 入院日期
                                cell = bodyRow.createCell(8);
                                cell.setCellStyle(bodyStyle);
                                if (obj.getRyrq() == null) {
                                    cell.setCellValue("");
                                } else {
                                    cell.setCellValue(obj.getRyrq());
                                }
                                // 入院诊断
                                cell = bodyRow.createCell(9);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getRyzdmc());
                                // 出院日期
                                cell = bodyRow.createCell(10);
                                cell.setCellStyle(bodyStyle);
                                if (obj.getCyrq() == null) {
                                    cell.setCellValue("");
                                } else {
                                    cell.setCellValue(obj.getCyrq());
                                }
                                // 出院诊断
                                cell = bodyRow.createCell(11);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getRyzdmc());
                                // 出院状态
                                cell = bodyRow.createCell(12);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getCyzt());
                                // 住院天数
                                cell = bodyRow.createCell(13);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getZyts());
                                // 传报时间
                                cell = bodyRow.createCell(14);
                                cell.setCellStyle(bodyStyle);
                                if (obj.getRyrq() == null) {
                                    cell.setCellValue("");
                                } else {
                                    cell.setCellValue(obj.getRyrq());
                                }
                                // 管床医师
                                cell = bodyRow.createCell(15);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue(obj.getZyysxm());
                                // 体温
                                cell = bodyRow.createCell(16);
                                cell.setCellStyle(bodyStyle);
                                cell.setCellValue("");

                                if (j == rzList.size() - 1) {
                                    // 最后说明
                                    bodyRow = sheet.createRow(line);// 创建新行
                                    bodyRow.setHeightInPoints(30);// 设置行高
                                    CellRangeAddress cra = new CellRangeAddress(line, line, 0, 18);
                                    sheet.addMergedRegion(cra);// 插入合并的单元格
                                    cell = bodyRow.createCell(0);
                                    bodyStyle.setAlignment(HorizontalAlignment.LEFT);
                                    cell.setCellStyle(bodyStyle);
                                    cell.setCellValue("备注：");
                                    // cell.setCellValue("注：1.14岁以下儿童要登记家长姓名，初步诊断应填写诊断病名，不能填写症状或体征，"
                                    // + "如为传染病应作出疫情报告明显标志；2.学生要写学校和班级； 3.职业必须填写："
                                    // +
                                    // "如幼托儿童、散居儿童、农民等；4.如发热病例请填写实测体温；5.处理应填写用药情况、实验室检测情况；"
                                    // + "6.每项必须认真填写，不准漏项
                                    // 、缺项，每项规定填写内容只能写在相应的空格内；7.35岁及以上人群首诊测血压和血糖。");
                                    RegionUtil.setBorderRight(1, cra, sheet, workBook);
                                    RegionUtil.setBorderBottom(1, cra, sheet, workBook);
                                }
                            }

                        }

                        String filename = null;
                        try {
                            filename = URLEncoder.encode("住院登记簿.xls", "utf-8");
                        } catch (UnsupportedEncodingException e1) {
                            e1.printStackTrace();
                        } // 解决中文文件名下载后乱码的问题
                        HttpServletResponse resp = invoContext.getResponse();
                        resp.setCharacterEncoding("utf-8");
                        resp.setHeader("Content-Disposition", "attachment; filename=" + filename + "");

                        try {
                            workBook.write(resp.getOutputStream());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                    }
                    break;
                // 无效参数
                default:
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    logger.info("InvoZyglCxtjZyrzbAction Interface| Requerst Parameter Validation Failed");
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoZyglCrygBrfyCxAction Interface| Requerst Parameter Validation Failed");
        }
    }

}
