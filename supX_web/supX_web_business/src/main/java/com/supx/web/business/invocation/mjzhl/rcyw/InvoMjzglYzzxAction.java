package com.supx.web.business.invocation.mjzhl.rcyw;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.mjzhl.rcyw.pojo.Mjzgl_YzzxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.service.mjzhl.rcyw.iface.IMjzglYzzxService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1MjzglYzzx")
public class InvoMjzglYzzxAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoMjzglYzzxAction.class);

    @Autowired
    private IMjzglYzzxService new1MjzglYzzxService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        if (StringUtils.isEmpty(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoMjzglYzzxAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult invoResult) {
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        String ksbm = JSONUtil.parse(parm).getByPath("ksbm").toString();//实体参数
        String ghxhParm = JSONUtil.parse(parm).getByPath("ghxhs").toString(); //挂号序号
        String[] ghxh = null;
        if (StringUtils.isNotEmpty(ghxhParm)){
            JSONArray array = JSONObject.parseArray(ghxhParm);
            ghxh = new String[array.size()];
            for (int i = 0; i < array.size(); i++) {
                ghxh[i] = array.getJSONObject(i).getString("ghxh");
            }
        }

        Mjzgl_YzzxModel yzzxModel = new Mjzgl_YzzxModel();
         yzzxModel.setKsbm(ksbm);
         yzzxModel.setGhxhs(ghxh);

        switch (optType) {
            case "queryAll":    //全部执行单
                msg.getParam().put("bean", yzzxModel);
                new1MjzglYzzxService.queryYzzxdList(msg, invoResult);
                break;
            case "queryZxdKf":  //口服执行单
                yzzxModel.setZxdlx("1");
                msg.getParam().put("bean", yzzxModel);
                new1MjzglYzzxService.queryYzzxdList(msg, invoResult);
                break;
            case "queryZxdZs":    //注射执行单
                yzzxModel.setZxdlx("2");
                msg.getParam().put("bean", yzzxModel);
                new1MjzglYzzxService.queryYzzxdList(msg, invoResult);
                break;
            case "queryZxdSy":    //输液执行单
                yzzxModel.setZxdlx("3");
                msg.getParam().put("bean", yzzxModel);
                new1MjzglYzzxService.queryYzzxdList(msg, invoResult);
                break;
            case "queryZxdSyt":    //输液贴执行单
                yzzxModel.setZxdlx("3");
                msg.getParam().put("bean", yzzxModel);
                new1MjzglYzzxService.querySytList(msg, invoResult);
                break;
            default:
                invoResult.setLogicCode(PARAM_VALIDATE_ERROR);
                break;
        }
    }
}
