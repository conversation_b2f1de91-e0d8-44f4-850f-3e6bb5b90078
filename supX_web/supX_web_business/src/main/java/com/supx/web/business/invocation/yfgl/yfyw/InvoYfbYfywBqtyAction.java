package com.supx.web.business.invocation.yfgl.yfyw;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.hsz.hlyw.pojo.Hsz_tysqModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.yfyw.iface.IYfbYfywBqtysqServyce;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 *
* @ClassName: InvoYfbYfywBqtyAction
* @Description: 药房病区退药审核
* <AUTHOR>
* @date 2020年7月13日 上午12:11:38
*
 */
@Controller("INVO_New1YfbYfywBqty")
public class InvoYfbYfywBqtyAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoYfbYfywBqtyAction.class);

	@Resource
	private IYfbYfywBqtysqServyce iYfbYfywBqtysqServyce;
	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        // TODO Auto-generated method stub
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywBqtyAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);

		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");
		String yfbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"yfbm","");//药房编码
		Hsz_tysqModel bean = null;
		switch (optType) {
		case "bqtysh"://病区退药审核
			List<Hsz_tysqModel> list = (List<Hsz_tysqModel>)RequestUtil.getListParamter(invoContext.getRequest(), Hsz_tysqModel.class);
			if (list == null || list.size() <= 0){
				result.setLogicCode(ERROR);
				result.setC("病区退药审核参数异常!");
	            logger.info("InvoYfbYfywBqtyAction Interface| Requerst Parameter Validation Failed");
	            return;
			}
			for (Hsz_tysqModel tysq : list) {
				if (tysq.getTysqid() == null || tysq.getTysqid().equals("")){
					result.setLogicCode(ERROR);
					result.setC("病区退药审核参数异常!");
		            logger.info("InvoYfbYfywBqtyAction Interface| Requerst Parameter Validation Failed");
		            return;
				}
			}
			msg.getParam().put("userinfo", userinfo);
			msg.getParam().put("bean", list);
			msg.getParam().put("yfbm", yfbm);
			iYfbYfywBqtysqServyce.bqtysh(msg, result);
			break;
		case "tydcx"://退药单查询
			if(parm.equals("") || parm==null){
				result.setLogicCode(ERROR);
				result.setC("病区退药查询参数异常!");
	            logger.info("InvoYfbYfywBqtyAction Interface| Requerst Parameter Validation Failed");
			}else{
				bean=JSONObject.parseObject(parm, Hsz_tysqModel.class);
				msg.getParam().put("bean", bean);
				iYfbYfywBqtysqServyce.queryTysq(msg, result);
			}
			break;
		}
	}
}
