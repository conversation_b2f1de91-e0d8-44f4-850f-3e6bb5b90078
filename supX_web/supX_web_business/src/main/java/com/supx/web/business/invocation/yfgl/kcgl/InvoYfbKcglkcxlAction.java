package com.supx.web.business.invocation.yfgl.kcgl;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_kcxlModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglKcxlService;

import javax.annotation.Resource;

/**
 *
 * @ClassName: InvoYfbKcglkcxlAction
 * @Description: (药房处理控制操作)
 * <AUTHOR> YK
 * @date 2020年9月12日 上午9:45:17
 *
 */
@Controller("INVO_New1YfbKcglkcxl")
public class InvoYfbKcglkcxlAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志工具
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	private IYfbKcglKcxlService new1yfbKcglKcxlService;

	// 参数有效性判断
	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		// 判断操作类型是否为空
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbKcglDbglAction Interface| Requerst Parameter Validation Failed");
		}
	}

	// 业务处理
	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
		Yfb_kcxlModel kcxl = null;
		switch (optType) {
		// 查询
		case "query":
			kcxl = JSONObject.parseObject(json, Yfb_kcxlModel.class);
			if (kcxl == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglDbglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("kcxl", kcxl);
				new1yfbKcglKcxlService.qureryCl(msg, result);
			}
			break;
		// 更新
		case "update":
			List<Yfb_kcxlModel> list = (List<Yfb_kcxlModel>) RequestUtil.getListParamter(invoContext.getRequest(),
					Yfb_kcxlModel.class);
			if (list == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglDbglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("list", list);
				new1yfbKcglKcxlService.upateCl(msg, result);
			}
			break;
		}

	}

}
