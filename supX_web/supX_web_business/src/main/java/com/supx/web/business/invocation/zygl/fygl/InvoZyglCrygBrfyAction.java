package com.supx.web.business.invocation.zygl.fygl;

import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.zygl.crygl.pojo.JsjlxxModel;
import com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.fygl.iface.IZyglFyglBrfyService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import com.supx.comm.util.RequestUtil;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: InvoZyglCrygBrfyAction
 * @Description: TODO(病人费用)
 * @date 2020年6月22日 下午4:48:41
 */
@Controller("INVO_New1ZyglFyglBrfy")
public class InvoZyglCrygBrfyAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyglCrygBrfyAction.class);

    @Autowired
    private IZyglFyglBrfyService new1zyglFyglBrfyService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        Zyb_brfyModel brfyModel;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        String ksbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "ksbm", "");//科室编码
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        msg.getUserinfo().put("userinfo", userinfo);//用户信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
        msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息
        msg.getParam().put("ksbm", ksbm);//科室编码

        try {
            switch (optType) {
                case "queryByModel"://根据住院号或者刷卡查询病人费用
                    //接收对象
                    Zyb_brfyModel bean = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    msg.getParam().put("bean", bean);//对象
                    List<Zyb_brfyModel> brfyModels = (List<Zyb_brfyModel>) new1zyglFyglBrfyService.queryByModel(msg, result);
                    break;
                case "query"://根据住院号或者刷卡分页查询病人费用
                    if (parm.equals("") || parm == null) {
                        brfyModel = new Zyb_brfyModel();
                    } else {
                        brfyModel = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    }
                    msg.getParam().put("bean", brfyModel);//对象
                    ResultParam list = new1zyglFyglBrfyService.query(msg, result);
                    break;
                case "queryJbxx"://添加预交记录时查询的基本信息
                    JsjlxxModel jsjlxxbean = JSONObject.parseObject(parm, JsjlxxModel.class);
                    msg.getParam().put("bean", jsjlxxbean);//对象
                    JsjlxxModel jsjlxxModel = new1zyglFyglBrfyService.queryJbxx(msg, result);
                    break;

                case "save"://批量添加病人费用
                    List<Zyb_brfyModel> beans = (List<Zyb_brfyModel>) RequestUtil.getListParamter(invoContext.getRequest(), Zyb_brfyModel.class);
                    msg.getParam().put("beans", beans);//对象
                    new1zyglFyglBrfyService.insert(msg, result);
                    break;
                case "jztf"://记账退费
                    List<Zyb_brfyModel> beanList = (List<Zyb_brfyModel>) RequestUtil.getListParamter(invoContext.getRequest(), Zyb_brfyModel.class);
                    msg.getParam().put("beans", beanList);//对象
                    new1zyglFyglBrfyService.jztf(msg, result);
                    break;
                case "queryByZyh"://根据住院号查询病人费用集合
                    String zyh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "zyh", "");
                    Zyb_brfyModel brfy = new Zyb_brfyModel();
                    brfy.setZyh(zyh);
                    msg.getParam().put("bean", brfy);
                    new1zyglFyglBrfyService.queryByZyh(msg, result);
                    break;
                case "queryByYhfx"://根据住院号或者卡号分组查询分项优惠记录
                    Zyb_brfyModel yhfxbean = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    msg.getParam().put("bean", yhfxbean);//对象
                    new1zyglFyglBrfyService.queryByYhfx(msg, result);
                    break;
                case "queryWxfy": //查询无效病人费用
                    Zyb_brfyModel wxbean = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    msg.getParam().put("bean", wxbean);//对象
                    new1zyglFyglBrfyService.queryWxfy(msg, result);
                    break;
                case "queryjzxe"://查询记帐限额
                    String jzxe = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "jzxe", "");
                    System.out.println(jzxe);
                    break;
                case "queryLszl"://根据当前登录人科室查询未处理的诊疗项目
                    Zyb_brfyModel lszlbean = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    msg.getParam().put("bean", lszlbean);//对象
                    new1zyglFyglBrfyService.queryLszl(msg, result);
                    break;
                case "updatezx":// 修改病人费用
                    List<Zyb_brfyModel> jczxList = (List<Zyb_brfyModel>) RequestUtil.getListParamter(invoContext.getRequest(), Zyb_brfyModel.class);
                    msg.getParam().put("beans", jczxList);//对象
                    new1zyglFyglBrfyService.updatezx(msg, result);
                    break;
                case "historyJz":// 获取历史记账数据
                    Zyb_brfyModel history = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    msg.getParam().put("beans", history);//对象
                    new1zyglFyglBrfyService.historyJzxx(msg, result);
                    break;
                case "historyJzMxxx":// 获取历史记账数据
                    Zyb_brfyModel historymx = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    msg.getParam().put("beans", historymx);//对象
                    new1zyglFyglBrfyService.historyJzMxxx(msg, result);
                    break;
                case "queryxmzs":
                    if (parm.equals("") || parm == null) {
                        brfyModel = new Zyb_brfyModel();
                    } else {
                        brfyModel = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    }
                    msg.getParam().put("bean", brfyModel);//对象
                    new1zyglFyglBrfyService.queryxmzs(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
        }
    }
}
