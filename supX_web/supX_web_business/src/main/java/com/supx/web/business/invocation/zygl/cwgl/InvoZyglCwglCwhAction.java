package com.supx.web.business.invocation.zygl.cwgl;
import com.supx.comm.pojo.ResultParam;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.supx.comm.util.RequestUtil;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_cwhModel;
import com.supx.csp.api.zygl.cwgl.pojo.HszCwglShowModel;
import com.supx.comm.pojo.DataGrid;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.cwgl.iface.IZyglCwglCwhService;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoZyglCwglCwhAction
* @Description: TODO(床位号)
* <AUTHOR>
* @date 2020年7月17日 下午10:41:46
*
 */
@Controller("INVO_New1ZyglCwglCwh")
public class InvoZyglCwglCwhAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoZyglCwglCwhAction.class);

	@Autowired
	private IZyglCwglCwhService new1zyglCwglCwhService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

		DataGrid dGrid;
		if (dg == null || dg.equals("")){
			dGrid = new DataGrid();
		}else{
		   dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
		}
		try{
			switch (optType) {

			case "delete":	//删除床位
				Zyb_cwhModel bea =RequestUtil.getObjParamter(invoContext.getRequest(),Zyb_cwhModel.class);
				msg.getParam().put("bean", bea);//对象
				new1zyglCwglCwhService.delete(msg, result);
				break;

			case "save"://保存床位号
				Zyb_cwhModel bean =RequestUtil.getObjParamter(invoContext.getRequest(),Zyb_cwhModel.class);
				msg.getParam().put("bean", bean);//对象
				new1zyglCwglCwhService.save(msg, result);
				break;
			case "query"://传入对象查询床位号集合
				Zyb_cwhModel cwh;
				if(parm.equals("") || parm==null){
					cwh=new Zyb_cwhModel();
				}else{
					cwh=JSONObject.parseObject(parm, Zyb_cwhModel.class);
				}

				UtilFun.DataGridInit(dGrid, "xssx");
				cwh.setRows(dGrid.getRows());
				cwh.setPage(dGrid.getPage());
				cwh.setSort(dGrid.getSort());
				cwh.setOrder(dGrid.getOrder());
				msg.getParam().put("bean", cwh);//对象
				ResultParam list=new1zyglCwglCwhService.query(msg, result);
				break;
			case "queryByHsz"://传入对象查询床位号集合
				HszCwglShowModel showmodel;
				if(parm.equals("") || parm==null){
					showmodel=new HszCwglShowModel();
				}else{
					showmodel=JSONObject.parseObject(parm, HszCwglShowModel.class);
				}

				msg.getParam().put("bean", showmodel);//对象
				new1zyglCwglCwhService.queryByHsz(msg, result);
				break;
			case "queryAllByPage":
				Zyb_cwhModel cwBean;
				if (StringUtils.isBlank(parm)) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setC("请传入病区编码和科室编码！");
					return;
				}
				cwBean = JSONObject.parseObject(parm, Zyb_cwhModel.class);

				UtilFun.DataGridInit(dGrid, "xssx");
				cwBean.setRows(dGrid.getRows());
				cwBean.setPage(dGrid.getPage());
				cwBean.setParm(dGrid.getParm());

				msg.getParam().put("bean", cwBean);
				new1zyglCwglCwhService.queryAllByPage(msg,result);
				break;
			case "queryBqKsTree"://查询床位维护里面所需要展示的树结构
				new1zyglCwglCwhService.queryBqKsTree(msg,result);
				break;
			case "queryByList"://根据传入的科室集合对象查询床位号集合
				List<Zyb_cwhModel> beans=JSONObject.parseArray(parm,Zyb_cwhModel.class);
				msg.getParam().put("beans", beans);//对象
				new1zyglCwglCwhService.queryByList(msg, result);
				break;
			case "queryByCwId"://根据传入的床位id查询除开这个床位id之外的床位号集合
				Zyb_cwhModel cwhModel = JSONObject.parseObject(parm,Zyb_cwhModel.class);
				msg.getParam().put("bean", cwhModel);
				new1zyglCwglCwhService.queryByCwId(msg, result);
				break;
			default:
				break;
			}
		}catch(Exception e){
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
		}
	}
}
