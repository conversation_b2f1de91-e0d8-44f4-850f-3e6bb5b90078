package com.supx.web.business.invocation.yfgl.yfyw;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.hsz.hlyw.pojo.Hsz_yz_fymxModel;
import com.supx.csp.api.hsz.hlyw.pojo.Hsz_yz_ypzxdModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.yfyw.iface.IYfbYfywBqbyService;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

@Controller("INVO_New1YfbYfywBqby")
public class InvoYfbYfywBqbyAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYfbYfywBqbyAction.class);
	@Resource
	private IYfbYfywBqbyService iYfbYfywBqbyService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbYfywCffyAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 获取Session对象 userinfo用户信息
		getSession(invoContext, result);

		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
		String ksbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "ksbm", "");// 申领单号
		msg.getParam().put("userinfo", userinfo);
		msg.getParam().put("csqxinfo", csqxinfo);
		msg.getParam().put("sysiniinfo", sysiniinfo);
		// 接收查询实体
		Hsz_yz_ypzxdModel bean = null;
		Hsz_yz_fymxModel beanfy = null;
		// 发药明细
		if (parm.equals("") || parm == null) {
			beanfy = new Hsz_yz_fymxModel();
		} else {
			beanfy = JSONObject.parseObject(parm, Hsz_yz_fymxModel.class);
		}
		beanfy.setKsbm(ksbm);

		//处理用法过滤
		String fylx = beanfy.getFylx();
		if (fylx == null || fylx.equals("")){fylx = "0";}
		String [] zxdlx = getzxdlx(fylx);

		Object obj = null;
		try {
			switch (optType) {
			case "print":// 发药单打印
				if (parm.equals("") || parm == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYfbYfywBqbyAction Interface| Requerst Parameter Validation Failed");
				} else {
					beanfy = JSONObject.parseObject(parm, Hsz_yz_fymxModel.class);
					if(beanfy.getFylx().equals("0")){
						beanfy.setFylx(null);
					}
				}
				beanfy.setSearchzxdlx(zxdlx);//过滤执行单类型
				msg.getParam().put("bean", beanfy);
				iYfbYfywBqbyService.fyPrint(msg, result);
				break;
			case "sldcx":// 申领单查询
				if (parm.equals("") || parm == null) {
					bean = new Hsz_yz_ypzxdModel();
				} else {
					bean = JSONObject.parseObject(parm, Hsz_yz_ypzxdModel.class);
				}
				msg.getParam().put("bean", bean);
				iYfbYfywBqbyService.queryYpzxdBqbySl(msg, result);
				break;
			case "sldmxcx":// 申领单明细查询
				Object object = RequestUtil.getListObjParamter(invoContext.getRequest());
				bean = new Hsz_yz_ypzxdModel();
				String yfbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "yfbm", "");// 药房编码
				String sldh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "sldh", "");// 申领单号
				bean.setYfbm(yfbm);
				bean.setSldh(sldh);
				msg.getParam().put("bean", bean);
				msg.getParam().put("obj", object);
				iYfbYfywBqbyService.queryYpzxdBqbySlMx(msg, result);
				break;
			case "bqby":// 病区摆药
				obj = RequestUtil.getObjParamter(invoContext.getRequest());
				msg.getParam().put("obj", obj);
				iYfbYfywBqbyService.bqby(msg, result);
				break;
			case "kshzfy":// 科室汇总发药
				obj = RequestUtil.getListObjParamter(invoContext.getRequest());
				msg.getParam().put("obj", obj);
				msg.getParam().put("bean", beanfy);
				iYfbYfywBqbyService.kshzfy(msg, result);
				break;
			case "kshzfyqx":// 科室汇总发药取消
				beanfy = (Hsz_yz_fymxModel) RequestUtil.getObjParamter(invoContext.getRequest(),
						Hsz_yz_fymxModel.class);
				msg.getParam().put("bean", beanfy);
				iYfbYfywBqbyService.KshzfyQx(msg, result);
				break;
			case "yfbayd"://药房摆药单查询
				Hsz_yz_ypzxdModel byd = null;
				// 发药明细
				if (parm.equals("") || parm == null) {
					byd = new Hsz_yz_ypzxdModel();
				} else {
					byd = JSONObject.parseObject(parm, Hsz_yz_ypzxdModel.class);
				}
				msg.getParam().put("bean", byd);
				iYfbYfywBqbyService.queryYpzxlshList(msg, result);
				break;
			default:
				break;
			}

		} catch (Exception ex) {
			result.setLogicCode(ERROR);
			result.setC("病区发药异常:" + ex.getMessage());
			logger.info("InvoYfbYfywBqbyAction Interface| Requerst Parameter Validation Failed");
		}

	}
	//执行单类型 0-无, 1-口服,2-注射3-输液,4-护理,5-治疗
	//处理用法过滤fylx; //用法类型选择  0-全部;1-口服;2-输液;3-肌注;4-其他/口服;5-输液/肌注;6-输液/肌注/其他
	/*
	//发药筛选
        yfxzfy_tran: {
        	'0':'全部',
        	'1':'口服',
        	'2':'注射',
        	'3':'输液',
        	'4':'口服/其他',
        	'5':'注射/输液'
        },
	* */
	public String[] getzxdlx(String fylx){
		String [] zxdlx = null;
		switch (fylx) {
			case "1"://口服
				zxdlx = new String[1];
				zxdlx[0] = "1";
				break;
			case "2"://注射
				zxdlx = new String[1];
				zxdlx[0] = "2";
				break;
			case "3"://输液
				zxdlx = new String[1];
				zxdlx[0] = "3";
				break;
			case "4"://其他/口服
				zxdlx = new String[2];
				zxdlx[0] = "0";
				zxdlx[1] = "1";
				break;
			case "5"://输液/肌注
				zxdlx = new String[2];
				zxdlx[0] = "2";
				zxdlx[1] = "3";
				break;
			case "6"://输液/肌注/其他
				zxdlx = new String[3];
				zxdlx[0] = "0";
				zxdlx[1] = "2";
				zxdlx[2] = "3";
				break;
			default:
				break;
		}
		return zxdlx;
	}
}
