package com.supx.web.business.invocation.ykgl.kfwh;
import com.supx.comm.pojo.ResultParam;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_ghdwModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfwh.iface.IYkgKfwhGhdwService;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoYkglKfwhGhdwAction
* @Description: TODO(库房维护)
* <AUTHOR>
* @date 2020年5月10日 下午11:29:40
 */
@Controller("INVO_New1YkglKfwhGhdw")
public class InvoYkglKfwhGhdwAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoYkglKfwhGhdwAction.class);

	@Autowired
	IYkgKfwhGhdwService new1ykgKfwhGhdwService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        // TODO Auto-generated method stub
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		Integer ref ;
		List<Ykb_ghdwModel> beans;
		Ykb_ghdwModel bean;
		ResultParam list;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");
		String dwbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dwbm","");
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"json","");
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		if (dwbm == null){
			dwbm = "";
		}

		switch (optType) {
		case "save":
			bean = RequestUtil.getObjParamter(invoContext.getRequest(),Ykb_ghdwModel.class);
			if (bean == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				return;
			}
			msg.getParam().put("bean", bean);
			new1ykgKfwhGhdwService.savebatch(msg, result);
			break;
		case "delete":
			if (sjson == null || sjson.equals("")){
				beans = (List<Ykb_ghdwModel>)RequestUtil.getListParamter(invoContext.getRequest(),Ykb_ghdwModel.class);
			}else {
				beans = (List<Ykb_ghdwModel>)JSONObject.parseArray(sjson, Ykb_ghdwModel.class);
			}
			msg.getParam().put("bean", beans);
			ref = new1ykgKfwhGhdwService.deletebatch(msg, result);
			break;
		case "query":
			if (sjson == null || "".equals(sjson)) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
			} else {
				Ykb_ghdwModel wzb = JSONObject.parseObject(sjson, Ykb_ghdwModel.class);
				msg.getParam().put("bean", wzb);
				new1ykgKfwhGhdwService.queryYkbGhdwList(msg, result);
			}
			/*String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");
			DataGrid dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
			UtilFun.DataGridInit(dGrid, "dwbm");
			msg.getDataGrid().put("dg", dGrid);
			list = ykgKfwhGhdwService.queryYkbGhdwList(msg, result);*/
			break;
		}

	}

}
