package com.supx.web.business.invocation.commonUtil;

import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.commonUtil.iface.New1FileUploadUtilService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 * 文件上传工具类
 */
@Controller("INVO_New1FileUpload")
public class New1FileUploadUtilAction extends BaseInvocation implements LogicCodeConstants {

    private final Logger logger = LoggerFactory.getLogger(New1FileUploadUtilAction.class);
    @Autowired
    private New1FileUploadUtilService fileUploadUtilService;
    @Override
    public void doService(InvocationContext context, InvocationResult result) {

        String opType = RequestUtil.getStrParamterAsDef(context.getRequest(),"types","");

        switch (opType)
        {
            case "uploadFile":
                try {
                    fileUploadUtilService.uploadFile(context.getRequest(),result);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            break;

            case "deleteFile":
                try {
                    fileUploadUtilService.deleteFile(context.getRequest(),result);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            break;
        }
    }
}
