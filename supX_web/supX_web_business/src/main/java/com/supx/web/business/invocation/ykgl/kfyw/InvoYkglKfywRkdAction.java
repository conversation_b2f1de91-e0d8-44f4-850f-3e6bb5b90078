package com.supx.web.business.invocation.ykgl.kfyw;
import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.ykgl.kfyw.pojo.Gyb_kfpdb;
import com.supx.csp.api.ykgl.kfyw.pojo.Gyb_kfpdbmx;
import com.supx.csp.api.ykgl.kfyw.pojo.Ykb_rkdModel;
import com.supx.comm.pojo.DataGrid;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfyw.iface.IYkgKfywRkdService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 *
 * @ClassName: InvoYkglKfywRkdAction
 * @Description: TODO(库房维护)
 * <AUTHOR>
 * @date 2020年5月10日 下午11:29:40
 */
@Controller("INVO_New1YkglKfywRkd")
public class InvoYkglKfywRkdAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYkglKfywRkdAction.class);

	@Autowired
	IYkgKfywRkdService new1ykgKfywRkdService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		Integer ref;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", ""); // 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", ""); // 操作类型
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", ""); // 排序方式
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");

		// 获取Session对象 userinfo用户信息
		getSession(invoContext, result);
		msg.getUserinfo().put("userinfo", userinfo);// 用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);// 参数信息

		DataGrid dGrid;
		if (dg.isEmpty()) {
			dGrid = new DataGrid();
		} else {
			dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
		}

		List<Ykb_rkdModel> beans;
		Ykb_rkdModel bean;
		ResultParam list;
		switch (optType) {
		case "save":// 提交所有药品信息
			Object jsonObject = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", jsonObject);
			new1ykgKfywRkdService.savebatch(msg, result);
			break;
		case "savemx":
			Object object = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", object);
			new1ykgKfywRkdService.save(msg, result);
			break;
		case "rkcx":// 入库冲销
			Object rkcxObj = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", rkcxObj);
			new1ykgKfywRkdService.rkcx(msg, result);
			break;
		case "delete":
			if (sjson.isEmpty()) {
				beans = (List<Ykb_rkdModel>) RequestUtil.getListParamter(invoContext.getRequest(), Ykb_rkdModel.class);
			} else {
				beans = (List<Ykb_rkdModel>) JSONObject.parseArray(sjson, Ykb_rkdModel.class);
			}
			msg.getParam().put("bean", beans);
			new1ykgKfywRkdService.deletebatch(msg, result);
			break;
		case "queryBySH":// 加载单据列表信息
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfywRkdAction Interface| Requerst Parameter Validation Failed");
			} else {
				Ykb_rkdModel rkd = JSONObject.parseObject(parm, Ykb_rkdModel.class);
				msg.getParam().put("rkd", rkd);
				new1ykgKfywRkdService.queryYkbRkdBySH(msg, result);
			}
			break;
		case "queryRkd":// 加载单据列表信息
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfywRkdAction Interface| Requerst Parameter Validation Failed");
			} else {
				Ykb_rkdModel rkd = JSONObject.parseObject(parm, Ykb_rkdModel.class);
				msg.getParam().put("rkd", rkd);
				new1ykgKfywRkdService.queryYkbRkd(msg, result);
			}
			break;
		case "queryYkypd":// 查询药库盘点单
			Gyb_kfpdb pd = JSONObject.parseObject(parm, Gyb_kfpdb.class);
			msg.getParam().put("pdb", pd);
			new1ykgKfywRkdService.queryYkypd(msg, result);
			break;
		case "queryPddMx":// 选中单据信息加载出相对应的单据内容明细
			Gyb_kfpdb pdb = JSONObject.parseObject(parm, Gyb_kfpdb.class);
			msg.getParam().put("pdb", pdb);
			list = new1ykgKfywRkdService.queryPddMx(msg, result);
			break;
		case "queryRkdMxByRkd":// 选中单据信息加载出相对应的单据内容明细
			UtilFun.DataGridInit(dGrid, "");
			msg.getDataGrid().put("dg", dGrid);
			JSONObject rkobj = JSONObject.parseObject(parm);
			msg.getParam().put("obj",rkobj);
			list = new1ykgKfywRkdService.queryRkdMxByRkdh(msg, result);
			break;
		case "insertPddmx": //新增盘点单明细-建筑
			List<Gyb_kfpdbmx> mxlist= (List<Gyb_kfpdbmx>) RequestUtil.getListParamter(invoContext.getRequest(), Gyb_kfpdbmx.class);
			msg.getParam().put("bean", mxlist);
			new1ykgKfywRkdService.insertPddmx(msg, result);
			break;
		case "insertPdd"://新增盘点单-建筑
			Object pdobj = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", pdobj);
			new1ykgKfywRkdService.insertPddh(msg, result);
			break;
		case "shpdd"://新增盘审核-建筑-审核or作废
			Object pdObj = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", pdObj);
			new1ykgKfywRkdService.shpdd(msg, result);
			break;
		case "confuseRkd"://拒绝审核
			Ykb_rkdModel model = RequestUtil.getObjParamter(invoContext.getRequest(), Ykb_rkdModel.class);
			if (model == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
				logger.info("InvoYkglKfywRkdAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", model);
				new1ykgKfywRkdService.confuseRkd(msg, result);
			}
			break;
		case "invalidRkd":// 作废
			Ykb_rkdModel zfmodel = RequestUtil.getObjParamter(invoContext.getRequest(), Ykb_rkdModel.class);
			if (zfmodel == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
				logger.info("InvoYkglKfywRkdAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", zfmodel);
				new1ykgKfywRkdService.invalidRkd(msg, result);
			}
			break;
		case "passRkd":// 入库审核
			Ykb_rkdModel shmodel = RequestUtil.getObjParamter(invoContext.getRequest(), Ykb_rkdModel.class);
			if (shmodel == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
				logger.info("InvoYkglKfywRkdAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", shmodel);
				new1ykgKfywRkdService.passRkd(msg, result);
			}
			break;
		case "print":// 入库打印
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYkglKfywRkdAction Interface| Requerst Parameter Validation Failed");
			} else {
				Ykb_rkdModel rkd = JSONObject.parseObject(parm, Ykb_rkdModel.class);
				msg.getParam().put("rkd", rkd);
				new1ykgKfywRkdService.rkPrint(msg, result);
			}
			break;
		case "updateDycs":
			Ykb_rkdModel rkd = JSONObject.parseObject(parm, Ykb_rkdModel.class);
			msg.getParam().put("rkd", rkd);
			new1ykgKfywRkdService.updateDycs(msg, result);
			break;
			default:
		}
	}

}
