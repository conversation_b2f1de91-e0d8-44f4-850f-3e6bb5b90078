package com.supx.web.business.invocation.mjzhl.cwwh;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.mjzhl.cwwh.pojo.Mjzgl_cwhModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.invocation.mzys.zlgl.InvoMzysZlglBrjzAction;
import com.supx.comm.pojo.DataGrid;
import com.supx.web.business.service.mjzhl.cwwh.iface.IMjzhlCwhService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1MjzhlCwwh")
public class InvoMjzhlCwhAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoMzysZlglBrjzAction.class);

	@Autowired
	IMjzhlCwhService new1mjzhlCwhService;
	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoMjzhlCwhAction 接口有问题：【获取types操作类型失败！】");
            return;
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		getSession(invoContext,result);
		Integer ref ;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types",""); //操作类型
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");         //排序方式
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");     //入参数
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);

		//实列化 排序Model
		DataGrid dGrid = new DataGrid();
		if(dg != null && !dg.equals("")){
			dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
		}
		msg.getUserinfo().put("user", userinfo);
		//根据Types不同实现不同的接口操作
		try{
			switch (optType) {
			case "saveCwh"://新增
				Mjzgl_cwhModel saveBean = RequestUtil.getObjParamter(invoContext.getRequest(), Mjzgl_cwhModel.class);
				msg.getParam().put("obj", saveBean);
				ref = new1mjzhlCwhService.insertcwh(msg, result);
				break;
			case "updateCwh"://修改
				Mjzgl_cwhModel updateBean= RequestUtil.getObjParamter(invoContext.getRequest(), Mjzgl_cwhModel.class);
				msg.getParam().put("obj", updateBean);
				ref = new1mjzhlCwhService.updatecwh(msg, result);
				break;
			case "queryCwList"://查询床位号
				Mjzgl_cwhModel cwh;
				if (StringUtils.isEmpty(parm)){
					cwh = new Mjzgl_cwhModel();
				}else {
				 	cwh = JSONObject.parseObject(parm,Mjzgl_cwhModel.class);
				}
				cwh.setRows(dGrid.getRows());
				cwh.setPage(dGrid.getPage());
				cwh.setSort(dGrid.getSort());
				cwh.setOrder(dGrid.getOrder());
				cwh.setParm(dGrid.getParm());
				msg.getParam().put("bean", cwh);
				new1mjzhlCwhService.queryCwList(msg, result);
				break;
			case "queryEmptycwh"://查询空床
				Mjzgl_cwhModel cwh2;
				if (StringUtils.isEmpty(parm)){
					cwh2 = new Mjzgl_cwhModel();
				}else {
					cwh2 = JSONObject.parseObject(parm,Mjzgl_cwhModel.class);
				}
				msg.getParam().put("bean", cwh2);
				new1mjzhlCwhService.queryEmptycwh(msg, result);
				break;
			case "queryKs":
				new1mjzhlCwhService.queryKs(msg, result);
				break;
			default:
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				break;
			}
		}catch(Exception e){
			result.setLogicCode(ERROR);
			result.setC(e.getMessage());
            logger.info("InvoGhglGhywBrghAction接口异常：" + e.getMessage() );
		}
	}
}
