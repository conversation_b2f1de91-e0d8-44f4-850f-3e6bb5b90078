package com.supx.web.business.invocation.ykgl.kfwh;
import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_ypjxModel;
import com.supx.comm.pojo.DataGrid;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfwh.iface.IYkgKfwhYpjxService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 *
 * @ClassName: InvoYkglKfwhYpjxAction
 * @Description: TODO(库房维护)
 * <AUTHOR>
 * @date 2020年5月10日 下午11:29:40
 */
@Controller("INVO_New1YkglKfwhYpjx")
public class InvoYkglKfwhYpjxAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYkglKfwhYpjxAction.class);

	@Autowired
	IYkgKfwhYpjxService new1ykgKfwhYpjxService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		Integer ref;
		List<Ykb_ypjxModel> beans;
		Ykb_ypjxModel bean;
		ResultParam list;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		String jxbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "jxbm", "");
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		if (jxbm == null) {
			jxbm = "";
		}

		switch (optType) {
		case "save":// 新增修改
			bean = (Ykb_ypjxModel) JSONObject.parseObject(sjson, Ykb_ypjxModel.class);
			if (bean == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				return;
			}
			msg.getParam().put("bean", bean);
			ref = new1ykgKfwhYpjxService.savebatch(msg, result);
			break;
		case "delete":// 删除
			beans = (List<Ykb_ypjxModel>) JSONObject.parseArray(sjson, Ykb_ypjxModel.class);
			msg.getParam().put("bean", beans);
			ref = new1ykgKfwhYpjxService.deletebatch(msg, result);
			break;
		case "query":// 查询全部
			String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");// 排序方式
			DataGrid dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
			UtilFun.DataGridInit(dGrid, "jxbm");
			msg.getDataGrid().put("dg", dGrid);
			list = new1ykgKfwhYpjxService.queryYkbYpjxList(msg, result);
			break;
		}

	}

}
