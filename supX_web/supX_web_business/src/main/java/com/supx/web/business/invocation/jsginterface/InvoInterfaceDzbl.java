package com.supx.web.business.invocation.jsginterface;

import java.util.List;

import com.supx.comm.pojo.DataGrid;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.jsginterface.pojo.Gyb_ksbmModelList;
import com.supx.csp.api.jsginterface.pojo.Gyb_rybmModellist;
import com.supx.csp.api.xtwh.ksry.pojo.Gyb_RybmModel;
import com.supx.csp.api.xtwh.ksry.pojo.Gyb_ksbmModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.jsginterface.iface.IDzblInterfaceService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1InterfaceDzbl")
public class InvoInterfaceDzbl extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoInterfaceDzbl.class);

    @Autowired
    private IDzblInterfaceService new1dzblInterfaceservice;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", ""); //操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoBaglSyglSydjAction Interface| Requerst Parameter Validation Failed");
        }
        String method = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "method", ""); //方法
        if (StringUtils.isBlank(method)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoBaglSyglSydjAction Interface| Requerst Parameter Validation Failed");
        }
        //

    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        String method = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "method", "");
        String id = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "id", "");

        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        msg.getParam().put("method", method);
        msg.getParam().put("optType", optType);
        msg.getParam().put("id", id);
        msg.getUserinfo().put("userinfo", userinfo);
        if (optType.equals("YPZD")) {
            String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");//排序方式
            String kfbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "kfbm", "");
            DataGrid dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
            UtilFun.DataGridInit(dGrid, "ypbm");
            msg.getDataGrid().put("dg", dGrid);
            msg.getParam().put("kfbm", kfbm);
        }
        if (optType.equals("KS") && method.equals("DSEMR_KSXX_ADD")) {
            List<Gyb_ksbmModelList> ks = (List<Gyb_ksbmModelList>) RequestUtil.getListParamter(invoContext.getRequest(), Gyb_ksbmModelList.class);
            List<Gyb_ksbmModel> kslist = ks.get(0).getKs();
            msg.getParam().put("beans", kslist);
        }
        if (optType.equals("YH") && method.equals("DSEMR_YHXX_ADD")) {
            List<Gyb_rybmModellist> ry = (List<Gyb_rybmModellist>) RequestUtil.getListParamter(invoContext.getRequest(), Gyb_rybmModellist.class);
            List<Gyb_RybmModel> rylist = ry.get(0).getRy();
            msg.getParam().put("beans", rylist);
        }
        new1dzblInterfaceservice.GetDzblInterface(msg);
    }

}
