package com.supx.web.business.invocation.zygl.crygl;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.RequestUtil;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_jsjl_qtzfModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.crygl.iface.IZyglCryglQtzfService;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoZyglCrygQtzfAction
* @Description: TODO(其他支付)
* <AUTHOR>
* @date 2020年6月22日 下午8:34:13
*
 */
@Controller("INVO_New1ZyglCryglQtzf")
public class InvoZyglCrygQtzfAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoZyglCrygQtzfAction.class);

	@Autowired
	private IZyglCryglQtzfService new1zyglCryglQtzfService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		//接收对象
		Zyb_jsjl_qtzfModel bean = JSONObject.parseObject(parm,Zyb_jsjl_qtzfModel.class);

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

		try{
			switch (optType) {
			case "queryByModel"://根据住院号或者刷卡查询结算信息
				msg.getParam().put("bean", bean);//对象
				List<Zyb_jsjl_qtzfModel> qtzfModel= (List<Zyb_jsjl_qtzfModel>) new1zyglCryglQtzfService.queryByModel(msg, result);
				break;
			default:
				break;
			}
		}catch(Exception e){
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
		}
	}
}
