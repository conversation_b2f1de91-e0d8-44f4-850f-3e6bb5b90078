package com.supx.web.business.invocation.zygl.cwgl;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.RequestUtil;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_cwfyModel;
import com.supx.csp.api.zygl.cwgl.pojo.CwfyDataModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.cwgl.iface.IZyglCwglCwfyService;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoZyglCwglCwfyAction
* @Description: TODO(床位费用)
* <AUTHOR>
* @date 2020年7月17日 下午10:41:46
*
 */
@Controller("INVO_New1ZyglCwglCwfy")
public class InvoZyglCwglCwfyAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoZyglCwglCwfyAction.class);

	@Autowired
	private IZyglCwglCwfyService new1zyglCwglCwfyService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

		try{
			switch (optType) {
			case "save"://批量保存床位费用(暂时没有用这个)
				List<Zyb_cwfyModel> beans =(List<Zyb_cwfyModel>) RequestUtil.getListParamter(invoContext.getRequest(),Zyb_cwfyModel.class);
				msg.getParam().put("beans", beans);//对象
				new1zyglCwglCwfyService.save(msg, result);
				break;
			case "delete"://批量删除床位费用
				List<Zyb_cwfyModel> beanList =(List<Zyb_cwfyModel>) RequestUtil.getListParamter(invoContext.getRequest(),Zyb_cwfyModel.class);
				msg.getParam().put("beans", beanList);//对象
				new1zyglCwglCwfyService.delete(msg, result);
				break;
			case "queryyw"://传入对象查询床位费用集合
				if (StringUtils.isBlank(parm)) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setResMsg("请传入床位id");
		            logger.info("InvoZyglCwglCwfyAction Interface| Requerst Parameter Validation Failed");
		            return;
				}
				Zyb_cwfyModel bean = JSONObject.parseObject(parm,Zyb_cwfyModel.class);
				msg.getParam().put("bean",bean );//对象
				new1zyglCwglCwfyService.queryyw(msg, result);
				break;
			case "saveOrUpdate"://批量保存或者修改床位费用
				Object saveobj = RequestUtil.getListObjParamter(invoContext.getRequest());
				if (saveobj == null){
					result.setLogicCode(PARAM_VALIDATE_ERROR);
		            logger.info("InvoZyglCwglCwfyAction Interface| Requerst Parameter Validation Failed");
				}else{
					msg.getParam().put("obj", saveobj);
					new1zyglCwglCwfyService.saveOrUpdate(msg, result);
				}
				break;
			case "modify"://保存或者修改床位费用 1张床的费用
				Object object = RequestUtil.getListObjParamter(invoContext.getRequest());
				if (object == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
		            logger.info("InvoZyglCwglCwfyAction Interface| Requerst Parameter Validation Failed");
				}else{
					CwfyDataModel cw=new CwfyDataModel();
					JSONObject jsobj=(JSONObject)object;
					JSONObject cwh = jsobj.getJSONObject("cwh");
					JSONArray cwfys = jsobj.getJSONArray("cwfy");
					List<Zyb_cwfyModel> fylist=JSON.parseArray(cwfys.toString(),Zyb_cwfyModel.class);
					Zyb_cwfyModel cwhs=JSON.parseObject(cwh.toString(),Zyb_cwfyModel.class);
					cw.setCwfy(fylist);
					cw.setCwh(cwhs);
					msg.getParam().put("obj", cw);
					new1zyglCwglCwfyService.modify(msg, result);
				}
				break;
			//复制费用
			case "fzfy":
				break;
			//批量新增
			case "plxz":
				break;
			//批量删除
			case "plsc":
				break;
			default:
				break;
			}
		}catch(Exception e){
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
		}
	}
}
