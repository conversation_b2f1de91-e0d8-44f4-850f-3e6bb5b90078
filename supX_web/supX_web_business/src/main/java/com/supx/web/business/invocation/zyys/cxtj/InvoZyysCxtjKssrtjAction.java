package com.supx.web.business.invocation.zyys.cxtj;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.pojo.DataGrid;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.zyys.cxtj.pojo.ZyysCxtjKssrtjResModel;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zyys.cxtj.iface.IZyysCxtjKssrtjService;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoZyysCxtjKssrtjAction
 * @Description: TODO(这里用一句话描述这个类的作用)
 * @date 2020年9月28日 上午12:25:42
 */
@Controller("INVO_New1ZyysCxtjKssrtj")
public class InvoZyysCxtjKssrtjAction extends BaseInvocation implements LogicCodeConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyysCxtjKssrtjAction.class);

    @Autowired
    IZyysCxtjKssrtjService new1zyysCxtjKssrtjService;

    /*
     * 验证参数
     */
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyysCxtjKssrtjAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");//排序方式
        DataGrid dGrid;
        if (dg == null || dg.equals("")) {
            dGrid = new DataGrid();
        } else {
            dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
        }
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        System.out.println(msg);

        //获取用户信息
        msg.getUserinfo().put("userinfo", userinfo);
        //获取参数信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);

        switch (optType) {
            case "queryDl":
                ZyysCxtjKssrtjResModel kssrModel = null;
                if (parm.equals("") || parm == null) {
                    kssrModel = new ZyysCxtjKssrtjResModel();
                } else {
                    kssrModel = JSONObject.parseObject(parm, ZyysCxtjKssrtjResModel.class);
                }
                UtilFun.DataGridInit(dGrid, "zyks");
                kssrModel.setRows(dGrid.getRows());
                kssrModel.setPage(dGrid.getPage());
                kssrModel.setSort(dGrid.getSort());
                kssrModel.setOrder(dGrid.getOrder());
                kssrModel.setParm(dGrid.getParm());
                msg.getParam().put("bean", kssrModel);
                new1zyysCxtjKssrtjService.queryKssrDl(msg, result);
                break;
            case "queryMx":
                ZyysCxtjKssrtjResModel kssrModel2 = null;
                if (parm.equals("") || parm == null) {
                    kssrModel2 = new ZyysCxtjKssrtjResModel();
                } else {
                    kssrModel2 = JSONObject.parseObject(parm, ZyysCxtjKssrtjResModel.class);
                }
                UtilFun.DataGridInit(dGrid, "zyks");
                kssrModel2.setRows(dGrid.getRows());
                kssrModel2.setPage(dGrid.getPage());
                kssrModel2.setSort(dGrid.getSort());
                kssrModel2.setOrder(dGrid.getOrder());
                kssrModel2.setParm(dGrid.getParm());
                msg.getParam().put("bean", kssrModel2);
                new1zyysCxtjKssrtjService.queryKssrMx(msg, result);
                break;
        }
    }
}
