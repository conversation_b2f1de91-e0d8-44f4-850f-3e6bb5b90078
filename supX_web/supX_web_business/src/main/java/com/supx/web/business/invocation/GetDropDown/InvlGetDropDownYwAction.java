package com.supx.web.business.invocation.GetDropDown;

import com.supx.comm.util.RequestUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.constants.UtilRequest;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.GetDropDown.iface.IGetDropDownYwService;
import org.springframework.stereotype.Controller;

@Controller("INVO_GetDropDownYw")
public class InvlGetDropDownYwAction  extends BaseInvocation implements LogicCodeConstants,IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvlGetDropDownYwAction.class);
	private String optType;

	@Autowired
	IGetDropDownYwService getDropDownYwService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        // TODO Auto-generated method stub
    	optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//下拉框架类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("GetDropDownActionyw Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		/*
		 * 调用下拉框接口
		 */
		getDropDownYwService.queryDropDown(invoContext,msg,csqxinfo, result);
	}
}
