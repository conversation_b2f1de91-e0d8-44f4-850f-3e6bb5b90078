package com.supx.web.business.invocation.jsginterface;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.jsginterface.iface.IBxInterfaceService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import com.supx.comm.util.RequestUtil;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.Map;

/**
 *
* @ClassName: InvoBxInterfacesAction
* @Description: 保险接口统计一入口
* <AUTHOR>
* @date 2020年10月9日 下午11:31:37
*
 */
@Controller("INVO_New1BxInterface")
public class InvoBxInterfacesAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoBxInterfacesAction.class);

	@Autowired
	private IBxInterfaceService new1bxjkInterfaceService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        // TODO Auto-generated method stub
    	String bxlbbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"bxlbbm","");//保险类别编码
        if (StringUtils.isBlank(bxlbbm))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoBxInterfacesAction Interface| Requerst Parameter Validation Failed");
        }
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InviHszByglCwglAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		getSession(invoContext,result);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型 调用医保交易为S，否则自已定义
		String bxlbbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"bxlbbm","");//保险类别编码
		String method = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"method","");//方法 用于纯内部交易方法
		String url = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "url", "");    //险接口地址
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");    //保险参数
		String bxjk = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"bxjk","");//保险接口编码
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		if  (parm == null || parm.equals("")){
			//参数为空时，为post参数调用，
			Map<String, Object> map = (Map<String, Object>)RequestUtil.getObjParamter(invoContext.getRequest());//invoContext.getRequest().getAttribute(State.REQ_PARAMS);
			Object obj = map.get("list");
			parm = JSONObject.toJSONString(obj);
		}
		msg.getParam().put("bxlbbm", bxlbbm);
		msg.getParam().put("optType", optType);
		msg.getParam().put("method", method);
		msg.getParam().put("url", url);
		msg.getParam().put("obj", parm);
		msg.getParam().put("bxjk",bxjk);
		if(parm.indexOf("billCode")>-1) {
			msg.getParam().put("bxparm", JSONObject.parseObject(parm));
		}
		msg.getParam().put("userinfo", userinfo);
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息
		new1bxjkInterfaceService.BxInterface(msg, result);
	}

}
