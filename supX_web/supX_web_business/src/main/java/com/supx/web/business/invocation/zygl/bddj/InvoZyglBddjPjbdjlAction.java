package com.supx.web.business.invocation.zygl.bddj;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_jsjlModel;
import com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel;
import com.supx.csp.api.zygl.fygl.pojo.Zyb_yjjlModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.bddj.iface.IZyglBddjPjbdjlService;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoZyglBddjPjbdjlAction
* @Description: TODO(住院预交结算的补打单据)
* <AUTHOR>
* @date 2020年8月15日 下午11:47:29
*
 */
@Controller("INVO_New1ZyglBddjPjbdjl")
public class InvoZyglBddjPjbdjlAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoZyglBddjPjbdjlAction.class);

	@Autowired
	private IZyglBddjPjbdjlService new1zyglBddjPjbdjlService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyglBddjPjbdjlAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

		try{
			switch (optType) {
			case "queryJsjlDyFpxx"://补打结算记录
				Zyb_jsjlModel beanJsjl =JSONObject.parseObject(parm,Zyb_jsjlModel.class);
				msg.getParam().put("bean", beanJsjl);//对象
				new1zyglBddjPjbdjlService.queryJsjlDyFpxx(msg, result);
				break;
			case "queryYjjlDyFpxx"://补打预交记录
				Zyb_yjjlModel beanYjjl =JSONObject.parseObject(parm,Zyb_yjjlModel.class);
				msg.getParam().put("bean", beanYjjl);//对象
				new1zyglBddjPjbdjlService.queryYjjlDyFpxx(msg, result);
				break;
			case "querPjdy": //住院费用记账票据打印
				Zyb_brfyModel beanBrfy =JSONObject.parseObject(parm,Zyb_brfyModel.class);
				msg.getParam().put("bean", beanBrfy);//对象
				new1zyglBddjPjbdjlService.querPjdy(msg, result);
				break;
			default:
				break;
			}
		}catch(Exception e){
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
		}
	}
}
