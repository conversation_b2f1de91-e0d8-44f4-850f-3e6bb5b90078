package com.supx.web.business.invocation.yfgl.kcgl;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ckdModel;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ckdmxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglCkglService;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 *
 * @ClassName: InvoYfbKcglCkglAction
 * @Description: 出库管理
 * <AUTHOR>
 * @date 2020年7月15日 上午1:10:47
 *
 */
@Controller("INVO_New1YfbKcglCkgl")
public class InvoYfbKcglCkglAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYfbKcglCkglAction.class);

	@Resource
	private IYfbKcglCkglService new1yfbKcglCkglService;

	//参数校验
	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbKcglCkglAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 获取Session对象 userinfo用户信息
		getSession(invoContext, result);

		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
		msg.getParam().put("userinfo", userinfo);
		msg.getParam().put("csqxinfo", csqxinfo);
		Object object = null;

		switch (optType) {
		case "ckdcx":// 出库单查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglCkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_ckdModel ckd = JSONObject.parseObject(parm, Yfb_ckdModel.class);
				msg.getParam().put("bean", ckd);
				new1yfbKcglCkglService.queryCkd(msg, result);
			}
			break;
		case "ckdmxcx":// 出库单明细查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglCkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_ckdmxModel ckdmx = JSONObject.parseObject(parm, Yfb_ckdmxModel.class);
				msg.getParam().put("bean", ckdmx);
				new1yfbKcglCkglService.queryCkdmx(msg, result);
			}
			break;
		case "saveckd":// 保存出库单
			object = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (object == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglCkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", object);
				new1yfbKcglCkglService.SaveCkd(msg, result);
			}
			break;
		case "modify"://修改出库单或者保存
			object = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (object == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglCkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", object);
				new1yfbKcglCkglService.ModifyCkd(msg, result);
			}
			break;
		case "shckd":// 审核出库单
			object = RequestUtil.getObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", object);
			msg.getParam().put("crlx", "12");
			new1yfbKcglCkglService.pzsh(msg, result);
			break;
		case "zfckd":// 作废出库单
			object = RequestUtil.getObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", object);
			new1yfbKcglCkglService.pzzf(msg, result);
			break;
		case "print":// 出库打印
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglCkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_ckdModel ckp = JSONObject.parseObject(parm, Yfb_ckdModel.class);
				msg.getParam().put("bean", ckp);
				new1yfbKcglCkglService.ckPrint(msg, result);
			}
			break;
		case "ckcx"://出库冲销
			Object ckobject = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", ckobject);
			new1yfbKcglCkglService.ckcx(msg,result);
			break;
		}
	}
}
