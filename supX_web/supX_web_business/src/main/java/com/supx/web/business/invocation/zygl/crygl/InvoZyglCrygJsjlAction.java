package com.supx.web.business.invocation.zygl.crygl;
import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.pojo.IniConfig;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.Utilpubfun;
import com.supx.csp.api.xtwh.ksry.pojo.Gyb_yljgModel;
import com.supx.csp.api.zygl.crygl.pojo.JsjlxxModel;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_jsjlModel;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_jsjl_zfjlModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.xtwh.ksry.iface.IXtwhKsryYljgService;
import com.supx.web.business.service.zygl.crygl.iface.IZyglCryglJsjlService;
import com.supx.web.business.util.excel.PoiExcelExportUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Controller;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 *
* @ClassName: InvoZyglCrygJsjlAction
* @Description: TODO(结算记录)
* <AUTHOR>
* @date 2020年6月21日 下午4:30:47
*
 */
@Controller("INVO_New1ZyglCryglJsjl")
public class InvoZyglCrygJsjlAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoZyglCrygJsjlAction.class);

	@Autowired
	private IZyglCryglJsjlService new1zyglCryglJsjlService;
	@Autowired
	private IXtwhKsryYljgService xtwhKsryYljgService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息
		//取本地参数例子                                     //本地参数编码
		IniConfig iniconfig = Utilpubfun.getSysconfig(sysiniinfo, "000001");//业务窗口编码
		String ywckbh = iniconfig.getCsz();//业务窗口编码
		try{
			switch (optType) {
			case "queryByModel"://根据住院号或者刷卡查询结算信息
				//接收对象
				JsjlxxModel bean = JSONObject.parseObject(parm,JsjlxxModel.class);
				msg.getParam().put("bean", bean);//对象
				JsjlxxModel jsjlxxModel= new1zyglCryglJsjlService.queryBymodel(msg, result);
				break;
			case "save"://结算
				JsjlxxModel jsjlbean = RequestUtil.getObjParamter(invoContext.getRequest(),JsjlxxModel.class);
				jsjlbean.setYwckbh(ywckbh);
				msg.getParam().put("bean", jsjlbean);
				new1zyglCryglJsjlService.insert(msg, result);
				break;
			case "queryJsjl"://根据住院号或者刷卡查询取消结算所需要的基本信息
				Zyb_jsjlModel qxjsbean = JSONObject.parseObject(parm,Zyb_jsjlModel.class);
				msg.getParam().put("bean", qxjsbean);
				JsjlxxModel qxjsjsjlxx= new1zyglCryglJsjlService.queryJsjl(msg, result);
				break;
			case "remove"://取消结算
				JsjlxxModel qxjsjlbean = RequestUtil.getObjParamter(invoContext.getRequest(),JsjlxxModel.class);
				qxjsjlbean.setYwckbh(ywckbh);
				if(qxjsjlbean.getYljgbm()==null){
					qxjsjlbean.setYljgbm(msg.getYljgbm());
				}
				msg.getParam().put("bean", qxjsjlbean);
				new1zyglCryglJsjlService.remove(msg, result);
				break;
			case "queryJsjlByBd"://根据时间筛选结算记录（主要用于补打结算单的）分页
				Zyb_jsjlModel jsjlModel = JSONObject.parseObject(parm,Zyb_jsjlModel.class);
				msg.getParam().put("bean", jsjlModel);
				new1zyglCryglJsjlService.queryJsjlByBd(msg, result);
				break;
			case "export":// 导出报表
				Zyb_jsjlModel jsjlModelbb = JSONObject.parseObject(parm,Zyb_jsjlModel.class);
				msg.getParam().put("bean", jsjlModelbb);
				msg.setYljgbm(jsjlModelbb.getYljgbm());
				ResultParam resultParam = new1zyglCryglJsjlService.queryJsjlByBd(msg, result);
				List<Zyb_jsjlModel> listJsjl=resultParam.getList();
				// 创建一个workbook 对应一个excel应用文件
				String[] titles = { "序号", "住院号", "病人姓名", "发票号码", "科室名称", "结算操作员", "保险类别", "费用合计", "医疗卡支付", "现金支付", "其他支付",
						"优惠金额", "结算欠费", "结算日期"};// 表头数组
				String sheetname = "住院结算记录";// 表名

				// 医疗机构名称
				Gyb_yljgModel yljgbean = xtwhKsryYljgService.queryGyb_yljgOne(jsjlModelbb.getYljgbm(), result);
				String yljgmc = yljgbean.getJgmc();
				yljgmc += "住院结算记录";

				XSSFWorkbook workBook = new XSSFWorkbook();
				XSSFSheet sheet = workBook.createSheet(sheetname);
				PoiExcelExportUtil exportUtil = new PoiExcelExportUtil(workBook, sheet);
				XSSFCellStyle headStyle = exportUtil.getHeadStyle();
				XSSFCellStyle bodyStyle = exportUtil.getBodyStyle();

				// 构建表头
				XSSFRow headRow = sheet.createRow(0);// 新行
				headRow = sheet.createRow(0);
				headRow.setHeight((short) (30 * 20));// 行高
				XSSFCell cell = null;
				XSSFCellStyle cellStyle = workBook.createCellStyle();
				XSSFFont font = workBook.createFont();// 字体
				font.setBold(true);
				font.setFontName("黑体");
				font.setFontHeightInPoints((short) 18);
				cellStyle.setAlignment(HorizontalAlignment.CENTER);// 居中
				cellStyle.setVerticalAlignment((short) 0);// 水平居中
				cellStyle.setFont(font);//

				CellRangeAddress craHead = new CellRangeAddress(0, 0, 0, 13);
				sheet.addMergedRegion(craHead);
				cell = headRow.createCell(0);
				cell.setCellStyle(cellStyle);
				cell.setCellValue(yljgmc);

				// 创建标题行
				XSSFRow headRow1 = sheet.createRow(1);
				for (int i = 0; i < titles.length; i++) {
					cell = headRow1.createCell(i);
					cell.setCellStyle(headStyle);
					cell.setCellValue(titles[i]);
				}
				SimpleDateFormat fm=new SimpleDateFormat("yyyy-MM-dd");
				// 构建表体数据
				if (listJsjl != null && listJsjl.size() > 0) {
					// 循环给对应列赋值
					int j = 0;
					int line = 2;
					for (; j < listJsjl.size(); j++) {
						XSSFRow bodyRow = sheet.createRow(line++);
						Zyb_jsjlModel obj = listJsjl.get(j);
						// 序号
						cell = bodyRow.createCell(0);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(j + 1);
						// 住院号
						cell = bodyRow.createCell(1);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getZyh());
						// 姓名
						cell = bodyRow.createCell(2);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getBrxm());
						// 发票号码
						cell = bodyRow.createCell(3);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getFphm());
						// 科室名称
						cell = bodyRow.createCell(4);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getKsmc());
						// 结算操作员
						cell = bodyRow.createCell(5);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getJsczyxm());
						// 保险类别
						cell = bodyRow.createCell(6);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getBxlbmc());
						// 费用合计
						cell = bodyRow.createCell(7);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getFyhj());
						// 医疗卡支付
						cell = bodyRow.createCell(8);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getYlkzf());
						// 现金支付
						cell = bodyRow.createCell(9);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getXjzf());
						// 其他支付
						cell = bodyRow.createCell(10);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getQtzf());
						// 优惠金额
						cell = bodyRow.createCell(11);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getYhje());
						// 结算欠费
						cell = bodyRow.createCell(12);
						cell.setCellStyle(bodyStyle);
						cell.setCellValue(obj.getJsqf());
						// 结算日期
						cell = bodyRow.createCell(13);
						cell.setCellStyle(bodyStyle);
						if (obj.getJsrq() == null) {
							cell.setCellValue("");
						} else {
							cell.setCellValue(fm.format(obj.getJsrq()));
						}
					}

				}

				String filename = null;
				try {
					filename = URLEncoder.encode("住院结算记录.xls", "utf-8");
				} catch (UnsupportedEncodingException e1) {
					e1.printStackTrace();
				} // 解决中文文件名下载后乱码的问题
				HttpServletResponse resp = invoContext.getResponse();
				resp.setCharacterEncoding("utf-8");
				resp.setHeader("Content-Disposition", "attachment; filename=" + filename + "");

				try {
					workBook.write(resp.getOutputStream());
				} catch (IOException e) {
					e.printStackTrace();
				}

				result.setD(workBook);
				result.setLogicCode(SUCCESS);
				break;
			case "queryJehz":	//计算记录费用金额合计
				Zyb_jsjlModel fyjeBean = JSONObject.parseObject(parm,Zyb_jsjlModel.class);
				msg.getParam().put("bean", fyjeBean);
				new1zyglCryglJsjlService.queryJehz(msg, result);
				break;
			case "queryJsZyh":
				Zyb_jsjl_zfjlModel jszyhBean = JSONObject.parseObject(parm,Zyb_jsjl_zfjlModel.class);
				msg.getParam().put("bean", jszyhBean);
				new1zyglCryglJsjlService.queryZyh(msg, result);
				break;
			case "insertZf":
				Zyb_jsjl_zfjlModel insertzf = JSONObject.parseObject(parm,Zyb_jsjl_zfjlModel.class);
				msg.getParam().put("bean", insertzf);
				new1zyglCryglJsjlService.insertZf(msg, result);
				break;
			case "updateZf":
				Zyb_jsjl_zfjlModel updatezf = JSONObject.parseObject(parm,Zyb_jsjl_zfjlModel.class);
				msg.getParam().put("bean", updatezf);
				new1zyglCryglJsjlService.updateZf(msg, result);
				break;
			default:
				break;
			}
		}catch(Exception e){
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
		}
	}
}
