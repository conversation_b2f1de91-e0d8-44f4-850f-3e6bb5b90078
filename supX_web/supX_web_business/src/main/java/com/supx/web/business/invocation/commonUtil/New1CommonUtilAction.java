package com.supx.web.business.invocation.commonUtil;

import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.commonUtil.pojo.CommonUtil_BrxxModel;
import com.supx.csp.api.xtwh.ksry.pojo.Gyb_RybmModel;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.commonUtil.iface.ICommonUtilService;
import com.supx.web.business.service.xtwh.ksry.iface.IXtwhKsryRybmService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 * 公共工具类（现有接口数据不满足，所以这里定义一个工具类）
 */
@Controller("INVO_New1CommonUtil")
public class New1CommonUtilAction extends BaseInvocation implements LogicCodeConstants {

    private final Logger logger = LoggerFactory.getLogger(New1CommonUtilAction.class);

    @Autowired
    IXtwhKsryRybmService new1xtwhKsryRybmService;
    @Autowired
    ICommonUtilService commonUtilService;
    @Override
    public void doService(InvocationContext context, InvocationResult result) {

        String opType = RequestUtil.getStrParamterAsDef(context.getRequest(),"types","");
        Gyb_RybmModel rybean;
        CommonUtil_BrxxModel brxxModel;
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, context);
        String parm = RequestUtil.getStrParamterAsDef(context.getRequest(),"parm","");
        switch (opType)
        {
            //获取当前登录人信息
            case "queryCurrentUser":
                try {
                    rybean = new Gyb_RybmModel();
                    getSession(context,result);
                    rybean.setRybm(userinfo.getCzybm());
                    rybean.setYljgbm(userinfo.getYljgbm());
                    msg.getParam().put("bean", rybean);
                    new1xtwhKsryRybmService.queryOne(msg, result);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            break;
            case "queryBrxxList":
                brxxModel = new CommonUtil_BrxxModel();
                brxxModel.setYljgbm(msg.getYljgbm());
                msg.getParam().put("bean",brxxModel);
                commonUtilService.queryBrxxList(msg,result);
                break;

            case "getGztyzfCs"://贵州统一支付参数获取
                commonUtilService.getGztyzfCs(msg,result);
                break;
            default:
                result.setLogicCode(PARAM_VALIDATE_ERROR);
            break;
        }
    }
}
