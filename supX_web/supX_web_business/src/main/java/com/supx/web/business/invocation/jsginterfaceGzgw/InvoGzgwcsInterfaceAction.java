package com.supx.web.business.invocation.jsginterfaceGzgw;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.jsginterfaceGzgw.pojo.Gzgw_csModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.jsginterfaceGzgw.iface.IGzgwcsInterfaceService;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1GzgwcsInterface")
public class InvoGzgwcsInterfaceAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoGzgwcsInterfaceAction.class);

	@Autowired
	private IGzgwcsInterfaceService gzgwcsInterfaceService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoGzgwcsInterfaceAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		getSession(invoContext,result);
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型 调用医保交易为S，否则自已定义
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");    //参数
		Gzgw_csModel gzgwcs = null ;
		if  (parm == null || parm.equals("")){
			//参数为空时，为post参数调用，
			gzgwcs = (Gzgw_csModel)RequestUtil.getObjParamter(invoContext.getRequest(),Gzgw_csModel.class);//invoContext.getRequest().getAttribute(State.REQ_PARAMS);
		}else {
			gzgwcs = (Gzgw_csModel)JSONObject.parseObject(parm, Gzgw_csModel.class);
		}
		//获取用户信息
		msg.getUserinfo().put("userinfo", userinfo);
		msg.getParam().put("bean", gzgwcs);
		switch (optType) {
		case "insert"://保存
			if (gzgwcs == null) {
				result.setLogicCode(ERROR);
				result.setC("保存时对象不能为空");
				return;
			}
			if (gzgwcs.getXjbm() == null || gzgwcs.getXjbm().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时县级编码不能为空");
				return;
			}
			if (gzgwcs.getAccount() == null || gzgwcs.getAccount().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时帐号不能为空");
				return;
			}
			gzgwcsInterfaceService.insert(msg, result);
			break;
		case "query"://查询
			gzgwcsInterfaceService.query(msg, result);
			break;
		case "delete"://删除
			if (gzgwcs == null) {
				result.setLogicCode(ERROR);
				result.setC("删除时对象不能为空");
				return;
			}
			gzgwcs.setYljgbm(msg.getYljgbm());
			gzgwcsInterfaceService.delete(msg, result);
			break;
		case "update"://修改
			if (gzgwcs == null) {
				result.setLogicCode(ERROR);
				result.setC("修改时对象不能为空");
				return;
			}
			gzgwcs.setYljgbm(msg.getYljgbm());
			gzgwcsInterfaceService.update(msg, result);
			break;
		case "save"://保存
			if (gzgwcs == null) {
				result.setLogicCode(ERROR);
				result.setC("保存时对象不能为空");
				return;
			}
			if (gzgwcs.getXjbm() == null || gzgwcs.getXjbm().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时县级编码不能为空");
				return;
			}
			if (gzgwcs.getAccount() == null || gzgwcs.getAccount().equals("")) {
				result.setLogicCode(ERROR);
				result.setC("保存时帐号不能为空");
				return;
			}
			gzgwcsInterfaceService.save(msg, result);
			break;
		default:
			break;
		}

	}

}
