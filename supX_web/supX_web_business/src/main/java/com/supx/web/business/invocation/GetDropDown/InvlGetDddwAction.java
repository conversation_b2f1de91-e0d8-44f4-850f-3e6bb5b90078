package com.supx.web.business.invocation.GetDropDown;

import com.supx.comm.pojo.ResultParam;

import java.util.List;

import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.BaseInvocation;
import com.supx.web.api.constants.IRequestConstants;
import org.apache.commons.lang.StringUtils;

import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.csp.api.pubfun.pojo.Gyb_DddwModel;
import com.supx.comm.util.RequestUtil;
import com.supx.csp.api.pubfun.service.IPubFunCspService;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.GetDropDown.iface.IGetDddwService;
import org.springframework.stereotype.Controller;

/*外部开放访问接口*/
@Controller("INVO_GetDddw")
public class InvlGetDddwAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvlGetDddwAction.class);
    @DubboReference
    IGetDddwService getDddwService;
    @DubboReference
    IPubFunCspService service;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        ResultParam list;
        Gyb_DddwModel bean;
        List<Gyb_DddwModel> beans;

        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型

        String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");

        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        switch (optType) {

            case "queryKs": //查询科室

                list = getDddwService.queryGyb_KsDddwList(msg, result);
                break;
            case "queryGhKs": //查询挂号科室
                list = getDddwService.queryGyb_GhKsDddwList(msg, result);
                break;
            case "queryZyKs": //查询住院科室
                list = getDddwService.queryGyb_ZyKsDddwList(msg, result);
                break;
            case "queryRybm": //查询人员编码
                list = getDddwService.queryGyb_RybmDddwList(msg, result);
                break;
            case "queryRybmYs": //查询所有医生
                list = getDddwService.queryGyb_RybmYsDddwList(msg, result);
                break;
            case "queryKsRybm": //根据科室查询人员编码
                if (sjson.equals("") || sjson == null) {
                    bean = new Gyb_DddwModel();
                } else {
                    bean = JSONObject.parseObject(sjson, Gyb_DddwModel.class);
                }
                msg.getParam().put("bean", bean);
                list = getDddwService.queryGyb_KsRybmDddwList(msg, result);
                break;
            case "queryKsRybmYs": //根据科室查询医生
                if (sjson.equals("") || sjson == null) {
                    bean = new Gyb_DddwModel();
                } else {
                    bean = JSONObject.parseObject(sjson, Gyb_DddwModel.class);
                }
                msg.getParam().put("bean", bean);
                list = getDddwService.queryGyb_KsRybmYsDddwList(msg, result);
                break;
            case "queryRybmKs": //查询人员所属科室
                if (sjson.equals("") || sjson == null) {
                    bean = new Gyb_DddwModel();
                } else {
                    bean = JSONObject.parseObject(sjson, Gyb_DddwModel.class);
                }
                msg.getParam().put("bean", bean);
                list = getDddwService.queryGyb_RybmKsDddwList(msg, result);
                break;
            case "queryMz": //查询民族
                list = getDddwService.queryGyb_MzDddwList(msg, result);
                break;
            case "queryGj": //查询国籍
                list = getDddwService.queryGyb_GjDddwList(msg, result);
                break;
            case "queryHyzk": //查询婚姻状况
                list = getDddwService.queryGyb_HyzkDddwList(msg, result);
                break;
            case "queryZybm": //查询职业编码
                list = getDddwService.queryGyb_ZybmDddwList(msg, result);
                break;
            case "queryXzqh": //查询行政区化代码
                list = getDddwService.queryGyb_XzqhList(msg, result);
                break;
            case "queryBrfb": //查询病人费别
                list = getDddwService.queryGyb_BrfbList(msg, result);
                break;
            case "queryBxlb": //查询保险类别
                list = getDddwService.queryGyb_BxlbList(msg, result);
                break;
            case "queryGhzl": //查询挂号种类
                list = getDddwService.queryGyb_GhzlList(msg, result);
                break;
            case "queryYwck": //查询业务窗口
                list = getDddwService.queryGyb_YwckList(msg, result);
                break;
            case "queryCwh": //查询床位号
                list = getDddwService.queryZyb_CwhList(msg, result);
                break;
            case "queryKsByCwh": //根据科室查询床位号
                if (sjson.equals("") || sjson == null) {
                    bean = new Gyb_DddwModel();
                } else {
                    bean = JSONObject.parseObject(sjson, Gyb_DddwModel.class);
                }
                msg.getParam().put("bean", bean);
                list = getDddwService.queryGyb_KsByCwList(msg, result);
                break;
            case "queryCwhByKs": //查询床位号所属科室所属科室
                if (sjson.equals("") || sjson == null) {
                    bean = new Gyb_DddwModel();
                } else {
                    bean = JSONObject.parseObject(sjson, Gyb_DddwModel.class);
                }
                msg.getParam().put("bean", bean);
                list = getDddwService.queryGyb_CwByKsList(msg, result);
                break;
            case "queryZflx": //查询支付类型
                list = getDddwService.queryGyb_ZflxList(msg, result);
                break;
            case "queryYyff": //查询用药方法
                list = getDddwService.queryGyb_YyffList(msg, result);
                break;
            case "queryPc": //查询频次
                list = getDddwService.queryGyb_PcList(msg, result);
                break;
            default:
                result.setLogicCode(PARAM_VALIDATE_ERROR);
                break;
        }

    }
}
