package com.supx.web.business.invocation.zyys.ysyw;

import com.alibaba.fastjson.JSONObject;

import com.supx.csp.api.mzys.zlgl.pojo.Gyb_zhyzModel;
import com.supx.csp.api.ssmzxt.rcyw.pojo.SsSsapModel;
import com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel;
import com.supx.comm.pojo.DataGrid;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.zyys.ysyw.pojo.*;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.util.Utilpubfun;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.cdyb.iface.ICdybService;
import com.supx.web.business.service.zyys.ysyw.iface.IZyysYsywYzclService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: InvoZyysYsywYzclAction
 * @Description: TODO(住院医生站医嘱处理)
 * @date 2020年6月13日 上午9:52:30
 */
@Controller("INVO_New1ZyysYsywYzcl")
public class InvoZyysYsywYzclAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {
    private final Logger logger = LoggerFactory.getLogger(InvoZyysYsywYzclAction.class);
    @Autowired
    private IZyysYsywYzclService new1zyysYsywYzclService;
    @Autowired
    ICdybService new1CdybService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");//实体参数
        String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");//实体参数
        String szyh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "zyh", "");
        String ksbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "ksbm", "");//科室
        String hsbz = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "hsbz", "");//护士标志
        String zrhs = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "zrhs", "");//护士标志
        String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");//排序方式
        DataGrid dGrid;
        if (dg == null || dg.equals("")) {
            dGrid = new DataGrid();
        } else {
            dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
        }
        Object obj = null;
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        msg.getParam().put("ksbm", ksbm);
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
        try {
            switch (optType) {
                case "zyhzxx"://在院患者查询
                    HzxxListResModel beanzy = null;
                    if (parm.equals("") || parm == null) {
                        beanzy = new HzxxListResModel();  //
                    } else {
                        beanzy = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
				/*if(dg!=null&&!dg.equals("")){
				UtilFun.DataGridInit(dGrid, "rycwbh");
				beanzy.setRows(dGrid.getRows());
				beanzy.setPage(dGrid.getPage());
				beanzy.setSort(dGrid.getSort());
				beanzy.setOrder(dGrid.getOrder());
				}*/
                    if (beanzy.getZyzt() == null) {
                        beanzy.setZyzt("0");//范围默认为在院,不含病区出院
                    }
                    //判断传过来的是床位号还是病人姓名
                    if (beanzy.getRycwbh() != null) {
                        if (StringUtils.isNumeric(beanzy.getRycwbh())) {
                            beanzy.setRycwbh(beanzy.getRycwbh());
                        } else {
                            beanzy.setBrxm(beanzy.getRycwbh());
                            beanzy.setRycwbh(null);
                        }
                    }
                    //判断是否点击主管病人
                    if (beanzy.getZyys() != null) {
                        beanzy.setZyysxm(userinfo.getCzyxm());
                        beanzy.setZyys(null);
                    }
                    beanzy.setZrhs(zrhs);
                    msg.getParam().put("bean", beanzy);
                    new1zyysYsywYzclService.queryZyHzxxList(msg, result);
                    break;
                case "zkhzxx"://转科患者查询
                    HzxxListResModel beanzk = null;
                    if (parm.equals("") || parm == null) {
                        beanzk = new HzxxListResModel();  //
                    } else {
                        beanzk = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
                    //判断传过来的是床位号还是病人姓名
                    if (beanzk.getRycwbh() != null) {
                        if (StringUtils.isNumeric(beanzk.getRycwbh())) {
                            beanzk.setRycwbh(beanzk.getRycwbh());
                        } else {
                            beanzk.setBrxm(beanzk.getRycwbh());
                            beanzk.setRycwbh(null);
                        }
                    }
                    msg.getParam().put("bean", beanzk);
                    new1zyysYsywYzclService.queryZkhzList(msg, result);
                    break;
                case "zkhzxxCy"://转科患者查询
                    HzxxListResModel beanzkCy = null;
                    if (parm.equals("") || parm == null) {
                        beanzkCy = new HzxxListResModel();  //
                    } else {
                        beanzkCy = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
                    //判断传过来的是床位号还是病人姓名
                    if (beanzkCy.getRycwbh() != null) {
                        if (StringUtils.isNumeric(beanzkCy.getRycwbh())) {
                            beanzkCy.setRycwbh(beanzkCy.getRycwbh());
                        } else {
                            beanzkCy.setBrxm(beanzkCy.getRycwbh());
                            beanzkCy.setRycwbh(null);
                        }
                    }
                    msg.getParam().put("bean", beanzkCy);
                    new1zyysYsywYzclService.queryZkhzCyList(msg, result);
                    break;
                case "qdhzxx"://清单患者查询
                    HzxxListResModel beanqd = null;
                    if (parm.equals("") || parm == null) {
                        beanqd = new HzxxListResModel();  //
                    } else {
                        beanqd = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
//				UtilFun.DataGridInit(dGrid, "rycwbh");
//				beanqd.setRows(dGrid.getRows());
//				beanqd.setPage(dGrid.getPage());
//				beanqd.setSort(dGrid.getSort());
//				beanqd.setOrder(dGrid.getOrder());
                    if (beanqd.getZyzt() == null) {
                        beanqd.setZyzt("0");//范围默认为在院,不含病区出院
                    }
                    //判断传过来的是床位号还是病人姓名
                    if (beanqd.getRycwbh() != null) {
                        if (StringUtils.isNumeric(beanqd.getRycwbh())) {
                            beanqd.setRycwbh(beanqd.getRycwbh());
                        } else {
                            beanqd.setBrxm(beanqd.getRycwbh());
                            beanqd.setRycwbh(null);
                        }
                    }
                    //判断是否点击主管病人
                    if (beanqd.getZyys() != null) {
                        beanqd.setZyysxm(userinfo.getCzyxm());
                        beanqd.setZyys(null);
                    }
                    msg.getParam().put("bean", beanqd);
                    new1zyysYsywYzclService.queryQdHzxxList(msg, result);
                    break;
                case "qbhzxx"://全部患者查询
                    HzxxListResModel beanzy4 = null;
                    if (parm.equals("") || parm == null) {
                        beanzy4 = new HzxxListResModel();  //
                    } else {
                        beanzy4 = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "rycwbh");
                    beanzy4.setRows(dGrid.getRows());
                    beanzy4.setPage(dGrid.getPage());
                    beanzy4.setSort(dGrid.getSort());
                    beanzy4.setOrder(dGrid.getOrder());

                    msg.getParam().put("bean", beanzy4);
                    new1zyysYsywYzclService.queryqbHzxxList(msg, result);
                    break;
                case "getQueryJrCy"://病区出院患者查询
                    HzxxListResModel beanzy13 = null;
                    if (parm.equals("") || parm == null) {
                        beanzy13 = new HzxxListResModel();  //
                    } else {
                        beanzy13 = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
//                    UtilFun.DataGridInit(dGrid, "rycwbh");
//                    beanzy3.setRows(dGrid.getRows());
//                    beanzy3.setPage(dGrid.getPage());
//                    beanzy3.setSort(dGrid.getSort());
//                    beanzy3.setOrder(dGrid.getOrder());
                    if (beanzy13.getZyzt() == null) {
                        beanzy13.setZyzt("0");//范围默认为在院,不含病区出院
                    }
                    if (beanzy13.getZyys() != null) {
                        beanzy13.setZyysxm(userinfo.getCzyxm());
                        beanzy13.setZyys(null);
                    }
                    beanzy13.setBqcybz("1");
                    beanzy13.setZrhs(zrhs);
                    msg.getParam().put("bean", beanzy13);
                    new1zyysYsywYzclService.queryZyDrcy(msg, result);
                    break;

                case "bqcyhzxx"://病区出院患者查询
                    HzxxListResModel beanzy3 = null;
                    if (parm.equals("") || parm == null) {
                        beanzy3 = new HzxxListResModel();  //
                    } else {
                        beanzy3 = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
//                    UtilFun.DataGridInit(dGrid, "rycwbh");
//                    beanzy3.setRows(dGrid.getRows());
//                    beanzy3.setPage(dGrid.getPage());
//                    beanzy3.setSort(dGrid.getSort());
//                    beanzy3.setOrder(dGrid.getOrder());
                    if (beanzy3.getZyzt() == null) {
                        beanzy3.setZyzt("0");//范围默认为在院,不含病区出院
                    }
                    if (beanzy3.getZyys() != null) {
                        beanzy3.setZyysxm(userinfo.getCzyxm());
                        beanzy3.setZyys(null);
                    }
                    beanzy3.setBqcybz("1");
                    beanzy3.setZrhs(zrhs);
                    msg.getParam().put("bean", beanzy3);
                    new1zyysYsywYzclService.queryZyHzxxList(msg, result);
                    break;
                case "Rcyhzxx"://病区出院患者查询
                    HzxxListResModel Rbeanzy = null;
                    if (parm.equals("") || parm == null) {
                        Rbeanzy = new HzxxListResModel();  //
                    } else {
                        Rbeanzy = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
//                    UtilFun.DataGridInit(dGrid, "rycwbh");
//                    Rbeanzy.setRows(dGrid.getRows());
//                    Rbeanzy.setPage(dGrid.getPage());
//                    Rbeanzy.setSort(dGrid.getSort());
//                    Rbeanzy.setOrder(dGrid.getOrder());
                    Rbeanzy.setZyzt("1");
                    Rbeanzy.setBqcybz("1");
                    Rbeanzy.setZrhs(zrhs);
                    msg.getParam().put("bean", Rbeanzy);
                    new1zyysYsywYzclService.queryRcyhzxxList(msg, result);
                    break;
                case "gznhhzxx"://贵州农合患者查询
                    HzxxListResModel beanzy2 = null;
                    if (parm.equals("") || parm == null) {
                        beanzy2 = new HzxxListResModel();  //
                    } else {
                        beanzy2 = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "rycwbh");
                    beanzy2.setRows(dGrid.getRows());
                    beanzy2.setPage(dGrid.getPage());
                    beanzy2.setSort(dGrid.getSort());
                    beanzy2.setOrder(dGrid.getOrder());
                    if (beanzy2.getZyzt() == null) {
                        beanzy2.setZyzt("0");//范围默认为在院,不含病区出院
                    }
                    //判断传过来的是床位号还是病人姓名
                    if (beanzy2.getRycwbh() != null) {
                        if (StringUtils.isNumeric(beanzy2.getRycwbh())) {
                            beanzy2.setRycwbh(beanzy2.getRycwbh());
                        } else {
                            beanzy2.setBrxm(beanzy2.getRycwbh());
                            beanzy2.setRycwbh(null);
                        }
                    }
                    //判断是否点击主管病人
                    if (beanzy2.getZyys() != null) {
                        beanzy2.setZyysxm(userinfo.getCzyxm());
                        beanzy2.setZyys(null);
                    }
                    msg.getParam().put("bean", beanzy2);
                    new1zyysYsywYzclService.queryGznhHzxxList(msg, result);
                    break;
                case "gzyhybhzxx"://贵州银海医保患者查询
                    HzxxListResModel beanyb = null;
                    if (parm.equals("") || parm == null) {
                        beanyb = new HzxxListResModel();  //
                    } else {
                        beanyb = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "rycwbh");
                    beanyb.setRows(dGrid.getRows());
                    beanyb.setPage(dGrid.getPage());
                    beanyb.setSort(dGrid.getSort());
                    beanyb.setOrder(dGrid.getOrder());
                    if (beanyb.getZyzt() == null) {
                        beanyb.setZyzt("0");//范围默认为在院,不含病区出院
                    }
                    msg.getParam().put("bean", beanyb);
                    new1zyysYsywYzclService.queryGzyhybHzxxList(msg, result);
                    break;
                case "snqhtfhzxx"://遂宁清华同方患者查询
                    HzxxListResModel beansnyb = null;
                    if (parm.equals("") || parm == null) {
                        beansnyb = new HzxxListResModel();  //
                    } else {
                        beansnyb = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
                    if (beansnyb.getZyzt() == null) {
                        beansnyb.setZyzt("0");//范围默认为在院,不含病区出院
                    }
                    msg.getParam().put("bean", beansnyb);
                    new1zyysYsywYzclService.querySnqhtfybHzxxList(msg, result);
                    break;
                case "cyhzxx"://出院患者查询
                    HzxxListResModel beancy = null;
                    if (parm.equals("") || parm == null) {
                        beancy = new HzxxListResModel();  //
                    } else {
                        beancy = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }
                    if (dg != null && !dg.equals("")) {
//				UtilFun.DataGridInit(dGrid, "rycwbh");
//				beancy.setRows(dGrid.getRows());
//				beancy.setPage(dGrid.getPage());
//				beancy.setSort(dGrid.getSort());
//				beancy.setOrder(dGrid.getOrder());
                    }
                    if (beancy.getRycwbh() != null) {
                        if (StringUtils.isNumeric(beancy.getRycwbh())) {
                            beancy.setRycwbh(beancy.getRycwbh());
                        } else {
                            beancy.setBrxm(beancy.getRycwbh());
                            beancy.setRycwbh(null);
                        }
                    }
                    //判断是否点击主管病人
                    if (beancy.getZyys() != null) {
                        beancy.setZyysxm(userinfo.getCzyxm());
                        beancy.setZyys(null);
                    }
                    msg.getParam().put("bean", beancy);
                    new1zyysYsywYzclService.queryCyHzxxList(msg, result);
                    break;
                case "ypyzxx"://患者医嘱信息
                    YzxxListResModel bean = null;
                    if (parm.equals("") || parm == null) {
                        bean = new YzxxListResModel();  //
                    } else {
                        bean = JSONObject.parseObject(parm, YzxxListResModel.class);
                    }
                    msg.getParam().put("bean", bean);
                    new1zyysYsywYzclService.selectYzxx(msg, result);
                    break;
                case "hzyzxx"://患者医嘱信息
                    YzxxListResModel beanyz = null;
                    if (parm.equals("") || parm == null) {
                        beanyz = new YzxxListResModel();  //
                    } else {
                        beanyz = (YzxxListResModel) JSONObject.parseObject(parm, YzxxListResModel.class);
                    }
                    //查询多病人医嘱
                    List<HzxxListResModel> beanzyh = null;
                    if (szyh.equals("") || szyh == null) {
                        beanzyh = new ArrayList<HzxxListResModel>();
                    } else {
                        beanzyh = (List<HzxxListResModel>) JSONObject.parseArray(szyh, HzxxListResModel.class);
                    }
                    String[] zyh = new String[beanzyh.size()];
                    for (int i = 0; i < beanzyh.size(); i++) {
                        HzxxListResModel b = beanzyh.get(i);
                        zyh[i] = b.getZyh();
                    }
                    if (zyh.length > 0) {
                        beanyz.setSearchzyh(zyh);
                    }
                    UtilFun.DataGridInit(dGrid, "ksrq");
                    beanyz.setRows(dGrid.getRows());
                    beanyz.setPage(dGrid.getPage());
                    beanyz.setSort(dGrid.getSort());
                    beanyz.setOrder(dGrid.getOrder());
                    beanyz.setParm(dGrid.getParm());
                    msg.getParam().put("bean", beanyz);
                    new1zyysYsywYzclService.queryHzyzList(msg, result);
                    break;
                case "hzyzxxcx"://患者医嘱信息  用于复制
                    YzxxListResModel beanyzcx = null;
                    if (parm.equals("") || parm == null) {
                        beanyzcx = new YzxxListResModel();  //
                    } else {
                        beanyzcx = (YzxxListResModel) JSONObject.parseObject(parm, YzxxListResModel.class);
                    }
                    //查询多病人医嘱
                    List<HzxxListResModel> beanzyhcx = null;
                    if (szyh.equals("") || szyh == null) {
                        beanzyhcx = new ArrayList<HzxxListResModel>();
                    } else {
                        beanzyhcx = (List<HzxxListResModel>) JSONObject.parseArray(szyh, HzxxListResModel.class);
                    }
                    String[] zyhcx = new String[beanzyhcx.size()];
                    for (int i = 0; i < beanzyhcx.size(); i++) {
                        HzxxListResModel b = beanzyhcx.get(i);
                        zyhcx[i] = b.getZyh();
                    }
                    if (zyhcx.length > 0) {
                        beanyzcx.setSearchzyh(zyhcx);
                    }
                    UtilFun.DataGridInit(dGrid, "ksrq");
                    beanyzcx.setRows(dGrid.getRows());
                    beanyzcx.setPage(dGrid.getPage());
                    beanyzcx.setSort(dGrid.getSort());
                    beanyzcx.setOrder(dGrid.getOrder());
                    beanyzcx.setParm(dGrid.getParm());
                    msg.getParam().put("bean", beanyzcx);
                    new1zyysYsywYzclService.queryHzyzListcx(msg, result);
                    break;
                case "hzyzxxcxfj"://患者医嘱信息
                    YzxxListResModel beanyzcxfj = null;
                    if (parm.equals("") || parm == null) {
                        beanyzcxfj = new YzxxListResModel();  //
                    } else {
                        beanyzcxfj = (YzxxListResModel) JSONObject.parseObject(parm, YzxxListResModel.class);
                    }
                    //查询多病人医嘱
                    List<HzxxListResModel> beanzyhcxfj = null;
                    if (szyh.equals("") || szyh == null) {
                        beanzyhcxfj = new ArrayList<HzxxListResModel>();
                    } else {
                        beanzyhcxfj = (List<HzxxListResModel>) JSONObject.parseArray(szyh, HzxxListResModel.class);
                    }
                    String[] zyhcxfj = new String[beanzyhcxfj.size()];
                    for (int i = 0; i < beanzyhcxfj.size(); i++) {
                        HzxxListResModel b = beanzyhcxfj.get(i);
                        zyhcxfj[i] = b.getZyh();
                    }
                    if (zyhcxfj.length > 0) {
                        beanyzcxfj.setSearchzyh(zyhcxfj);
                    }
                    UtilFun.DataGridInit(dGrid, "ksrq");
                    beanyzcxfj.setRows(dGrid.getRows());
                    beanyzcxfj.setPage(dGrid.getPage());
                    beanyzcxfj.setSort(dGrid.getSort());
                    beanyzcxfj.setOrder(dGrid.getOrder());
                    beanyzcxfj.setParm(dGrid.getParm());
                    msg.getParam().put("bean", beanyzcxfj);
                    new1zyysYsywYzclService.queryHzyzListCxFj(msg, result);
                    break;
                case "queryZyyz"://患者医嘱信息
                    YzxxListResModel beanzyyz = null;
                    if (parm.equals("") || parm == null) {
                        beanzyyz = new YzxxListResModel();  //
                    } else {
                        beanzyyz = (YzxxListResModel) JSONObject.parseObject(parm, YzxxListResModel.class);
                    }
                    msg.getParam().put("bean", beanzyyz);
                    new1zyysYsywYzclService.queryZyyzList(msg, result);
                    break;
                case "hzyzd"://患者医嘱单信息
                    YzxxListResModel beanyzd = null;
                    if (parm.equals("") || parm == null) {
                        beanyzd = new YzxxListResModel();  //
                    } else {
                        beanyzd = (YzxxListResModel) JSONObject.parseObject(parm, YzxxListResModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "ksrq");
//				if(beanyzd.getRows()==0){
//				beanyzd.setRows(dGrid.getRows());
//				beanyzd.setPage(dGrid.getPage());
//				beanyzd.setSort(dGrid.getSort());
//				beanyzd.setOrder(dGrid.getOrder());
//				if(beanyzd.getParm()==null||beanyzd.getParm().equals("")){
//				beanyzd.setParm(dGrid.getParm());
//				}
                    msg.getParam().put("csqxinfo", csqxinfo);//参数信息
                    msg.getParam().put("bean", beanyzd);
                    msg.getParam().put("hsbz", hsbz);
                    new1zyysYsywYzclService.queryHzyzdList(msg, result);
                    break;
                case "zhyzmx"://患者医嘱单信息
                    Gyb_zhyzModel beanzhyzmx = null;
                    String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");//排序方式
                    if (sjson.equals("") || sjson == null) {
                        beanzhyzmx = new Gyb_zhyzModel();  //
                    } else {
                        beanzhyzmx = (Gyb_zhyzModel) JSONObject.parseObject(sjson, Gyb_zhyzModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "fzh");
                    beanzhyzmx.setRows(dGrid.getRows());
                    beanzhyzmx.setPage(dGrid.getPage());
                    beanzhyzmx.setSort(dGrid.getSort());
                    beanzhyzmx.setOrder(dGrid.getOrder());
                    msg.getParam().put("bean", beanzhyzmx);
                    new1zyysYsywYzclService.queryZhyzmxList(msg, result);
                    break;
                case "yzxm"://医嘱项目
                    YzxmListResModel beanxm = null;
                    if (parm.equals("") || parm == null) {
                        beanxm = new YzxmListResModel();  //
                    } else {
                        beanxm = JSONObject.parseObject(parm, YzxmListResModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "xmbm");
                    beanxm.setRows(dGrid.getRows());
                    beanxm.setPage(dGrid.getPage());
                    beanxm.setSort(dGrid.getSort());
                    beanxm.setOrder(dGrid.getOrder());
                    beanxm.setParm(dGrid.getParm());
                    msg.getParam().put("bean", beanxm);
                    new1zyysYsywYzclService.queryYzxmList(msg, result);
                    break;
                case "lczd"://医嘱临床诊断查询
                    Zyys_ylyz_lczdModel beanzd = null;
                    if (parm.equals("") || parm == null) {
                        beanzd = new Zyys_ylyz_lczdModel();  //
                    } else {
                        beanzd = (Zyys_ylyz_lczdModel) JSONObject.parseObject(parm, Zyys_ylyz_lczdModel.class);
                    }
                    if (beanzd.getYlyzxh() == null || beanzd.equals("")) {
                        result.setLogicCode(ERROR);
                        result.setC("医嘱序号不能为空!");
                        return;
                    }
                    if (beanzd.getMxxh() <= 0) {
                        result.setLogicCode(ERROR);
                        result.setC("医嘱明细序号不能小于等于0!");
                        return;
                    }
                    UtilFun.DataGridInit(dGrid, "mxxh");
                    beanzd.setRows(dGrid.getRows());
                    beanzd.setPage(dGrid.getPage());
                    beanzd.setSort(dGrid.getSort());
                    beanzd.setOrder(dGrid.getOrder());
                    msg.getParam().put("bean", beanzd);
                    new1zyysYsywYzclService.queryYzlczdist(msg, result);
                    break;
                case "yzsave"://医嘱保存
                    //新加的获取list对象方法
                    //List<ZyysYzSaveMsg> list = (List<ZyysYzSaveMsg>)RequestUtil.getListParamter(invoContext.getRequest(),ZyysYzSaveMsg.class);
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可保存的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    msg.getParam().put("csqxinfo", csqxinfo);//参数信息
                    new1zyysYsywYzclService.SaveHzyz(msg, result);
                    break;
                case "3101save"://上传医保信息 3101 type 5
                    if (!StringUtils.isEmpty(szyh)) {
                        msg.setBmString(szyh); //获取住院号
                        msg.setDataResource("5");  //操作类型
                        new1CdybService.cdyb3101(msg, result);
                        break;
                    }
                case "3103save"://上传医保信息 3103 type 5
                    if (!StringUtils.isEmpty(parm)) {
                        msg.setBmString(parm);
                        new1CdybService.cdyb3103(msg, result);
                        break;
                    }
                case "yzdelete":  //作废医嘱
                    //新加的获取list对象方法
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可作废的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可作废的医嘱信息");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    msg.getParam().put("csqxinfo", csqxinfo);//参数信息
                    new1zyysYsywYzclService.DeleteHzyz(msg, result);
                    break;
                case "cxyz":  //撤销医嘱
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可撤销的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可撤销的医嘱信息");
                        return;
                    }
                    String cxyzsfsh = Utilpubfun.getCsqx(csqxinfo, "N03003200180", ksbm);//撤消医嘱是否需要审核 0-否 1-是
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    msg.getParam().put("csqxinfo", csqxinfo);
                    msg.getParam().put("ksbm", ksbm);
                    if (cxyzsfsh == null || cxyzsfsh.equals("")) {
                        cxyzsfsh = "0";
                    }
                    if (cxyzsfsh.equals("0"))
                        new1zyysYsywYzclService.cxyz(msg, result);
                    break;
                case "yzstop":  //停嘱
                    //新加的获取list对象方法
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可停的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可作废的医嘱信息");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.StopHzyz(msg, result);
                    break;
                case "yzYz":  //停嘱验证
                    //新加的获取list对象方法
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可停的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可作废的医嘱信息");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.queryYzZt(msg, result);
                    break;
                case "yzstopqx":  //停嘱
                    //新加的获取list对象方法
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可停的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可作废的医嘱信息");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.StopHzyzqx(msg, result);
                    break;
                case "savePs":  //皮试结果保存
                    Zyys_ypyzModel ypyz = null;
                    if (parm.equals("") || parm == null) {
                        ypyz = new Zyys_ypyzModel();  //
                    } else {
                        ypyz = (Zyys_ypyzModel) JSONObject.parseObject(parm, Zyys_ypyzModel.class);
                    }
                    msg.getParam().put("bean", ypyz);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.savePs(msg, result);
                    break;
                case "queryYlyzList":    //查询医疗医嘱
                    Zyys_ylyzModel ylyz = null;
                    if (parm.equals("") || parm == null) {
                        ylyz = new Zyys_ylyzModel();  //
                    } else {
                        ylyz = (Zyys_ylyzModel) JSONObject.parseObject(parm, Zyys_ylyzModel.class);
                    }
                    msg.getParam().put("bean", ylyz);
                    new1zyysYsywYzclService.queryYlyzList(msg, result);
                    break;
//新增加手术申请相关接口 whq 2018-12-18 add
                case "SaveSssq":  //手术申请保存
//				obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    Zyys_SssqModel sssqModel = (Zyys_SssqModel) RequestUtil.getObjParamter(invoContext.getRequest(), Zyys_SssqModel.class);
                    if (sssqModel == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可保存的手术申请信息");
                        logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }

                    msg.getParam().put("bean", sssqModel);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.SaveSssq(msg, result);
                    break;
                case "ModifySssq":  //手术申请保存
                    Zyys_SssqModel beanssxg = (Zyys_SssqModel) RequestUtil.getObjParamter(invoContext.getRequest(), Zyys_SssqModel.class);
                    if (parm == null || parm.equals("")) {
                        beanssxg = new Zyys_SssqModel();  //
                    } else {
                        beanssxg = (Zyys_SssqModel) JSONObject.parseObject(parm, Zyys_SssqModel.class);
                    }
                    msg.getParam().put("bean", beanssxg);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.ModifySssq(msg, result);
                    break;
                case "PtssZf"://排台手术作废
                    SsSsapModel sssqModel1 = (SsSsapModel) RequestUtil.getObjParamter(invoContext.getRequest(), SsSsapModel.class);
                    if (parm == null || "".equals(parm)) {
                        sssqModel1 = new SsSsapModel();
                    } else {
                        sssqModel1 = (SsSsapModel) JSONObject.parseObject(parm, SsSsapModel.class);
                    }
                    msg.getParam().put("bean", sssqModel1);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.PtssZf(msg, result);
                    break;
                case "DeleteSssq":  //手术申请作废
                    Zyys_SssqModel beansszf = null;
                    if (parm.equals("") || parm == null) {
                        beansszf = new Zyys_SssqModel();  //
                    } else {
                        beansszf = (Zyys_SssqModel) JSONObject.parseObject(parm, Zyys_SssqModel.class);
                    }
                    msg.getParam().put("bean", beansszf);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.SaveSssq(msg, result);
                    break;
                case "QuerySssq":    //手术申请查询
                    Zyys_SssqModel beanssquery = null;
                    if (parm.equals("") || parm == null) {
                        ylyz = new Zyys_ylyzModel();  //
                    } else {
                        beanssquery = (Zyys_SssqModel) JSONObject.parseObject(parm, Zyys_SssqModel.class);
                    }
                    msg.getParam().put("bean", beanssquery);
                    new1zyysYsywYzclService.QuerySssq(msg, result);
                    break;
                case "QueryPtMsg":    //查询排台统计
                    new1zyysYsywYzclService.selectSsPtMsg(msg, result);
                    break;
                case "initHzsq"://会诊申请初始化，查询出病历里面的信息
                    Zyys_HzglModel zyys_hzglModel1;
                    if (parm == null || "".equals(parm)) {
                        result.setLogicCode(ERROR);
                        result.setC("初始化会诊申请相关信息失败！失败原因：【参数为空】");
                        logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }
                    zyys_hzglModel1 = JSONObject.parseObject(parm, Zyys_HzglModel.class);
                    msg.getParam().put("zyys_hzglModel", zyys_hzglModel1);
                    new1zyysYsywYzclService.initHzsq(msg, result);
                    break;
                case "saveHzgl":    //会诊申请
                    Zyys_HzglModel zyys_hzglModel2 = RequestUtil.getObjParamter(invoContext.getRequest(), Zyys_HzglModel.class);
                    if (zyys_hzglModel2 == null) {
                        result.setLogicCode(ERROR);
                        result.setC("参数为空，没有可以保存的会诊信息！");
                        logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }
                    msg.getParam().put("userinfo", userinfo);
                    msg.getParam().put("zyys_hzglModel", zyys_hzglModel2);
                    new1zyysYsywYzclService.saveHzgl(msg, result);
                    break;
                case "queryHzsqList"://会诊申请
                    Zyys_HzglModel zyys_hzglModel3;
                    if (parm == null || "".equals(parm)) {
                        result.setLogicCode(ERROR);
                        result.setC("查询会诊申请信息失败！失败原因：【参数为空】");
                        logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }
                    zyys_hzglModel3 = JSONObject.parseObject(parm, Zyys_HzglModel.class);
                    zyys_hzglModel3.setYljgbm(msg.getYljgbm());
                    msg.getParam().put("zyys_hzglModel", zyys_hzglModel3);
                    msg.getParam().put("userinfo", userinfo);
                    new1zyysYsywYzclService.queryHzsqList(msg, result);
                    break;
                case "deleteHzsq"://删除会诊报告
                    Zyys_HzglModel deleteHzsq;
                    if (parm == null || "".equals(parm)) {
                        result.setLogicCode(ERROR);
                        result.setC("删除会诊报告信息失败！失败原因：【参数为空】");
                        logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }
                    deleteHzsq = JSONObject.parseObject(parm, Zyys_HzglModel.class);
                    deleteHzsq.setYljgbm(msg.getYljgbm());
                    msg.getParam().put("zyys_hzglModel", deleteHzsq);
                    new1zyysYsywYzclService.deleteHzsq(msg, result);
                    break;
                case "queryHzbgList"://会诊报告
                    Zyys_HzglModel zyys_hzglModel4;
                    if (parm == null || "".equals(parm)) {
                        result.setLogicCode(ERROR);
                        result.setC("查询会诊报告信息失败！失败原因：【参数为空】");
                        logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }
                    zyys_hzglModel4 = JSONObject.parseObject(parm, Zyys_HzglModel.class);
                    zyys_hzglModel4.setYljgbm(msg.getYljgbm());
                    msg.getParam().put("zyys_hzglModel", zyys_hzglModel4);
                    new1zyysYsywYzclService.queryHzbgList(msg, result);
                    break;
                case "updateStartTime":  // @yqq 修改医嘱开始时间
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可修改的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可修改的医嘱信息");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.updateStartTime(msg, result);
                    break;
                case "updateZxks":  // @yqq 修改医嘱执行科室
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可修改的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可修改的医嘱信息");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywYzclService.updateZxks(msg, result);
                    break;
                case "updateDycs": //增加检查检验打印次数
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可修改的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可修改的医嘱信息");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    new1zyysYsywYzclService.updateDycs(msg, result);
                    break;
                case "saveZyysHzyz"://会诊建议医嘱保存
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可保存的医嘱信息");
                        logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }
                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    msg.getParam().put("csqxinfo", csqxinfo);//参数信息
                    new1zyysYsywYzclService.saveZyysHzyz(msg, result);
                    break;
                case "deleteZyysHzyz":  //作废医嘱
                    //新加的获取list对象方法
                    obj = RequestUtil.getListObjParamter(invoContext.getRequest());
                    if (obj == null) {
                        result.setLogicCode(ERROR);
                        result.setC("没有可删除的医嘱信息！");
                        logger.info("InvoZyysYsywYzclAction Interface| 没有可删除的医嘱信息！");
                        return;
                    }

                    msg.getParam().put("obj", obj);
                    msg.getParam().put("userInfo", userinfo);
                    msg.getParam().put("csqxinfo", csqxinfo);//参数信息
                    new1zyysYsywYzclService.deleteZyysHzyz(msg, result);
                    break;
                case "queryZyysHzyzCyyz"://会诊医嘱草药信息
                    YzxxListResModel beanzyyz1 = null;
                    if (parm.equals("") || parm == null) {
                        beanzyyz1 = new YzxxListResModel();  //
                    } else {
                        beanzyyz1 = JSONObject.parseObject(parm, YzxxListResModel.class);
                    }
                    msg.getParam().put("bean", beanzyyz1);
                    new1zyysYsywYzclService.queryZyysHzyzCyyz(msg, result);
                    break;
                case "queryZyysHzyz"://会诊医嘱复制明细查询
                    YzxxListResModel beanzyyz2 = null;
                    if (parm.equals("") || parm == null) {
                        beanzyyz2 = new YzxxListResModel();  //
                    } else {
                        beanzyyz2 = JSONObject.parseObject(parm, YzxxListResModel.class);
                    }
                    msg.getParam().put("bean", beanzyyz2);
                    new1zyysYsywYzclService.queryZyysHzyz(msg, result);
                    break;
                case "queryWzxfy":
                    msg.getParam().put("bean", szyh);
                    new1zyysYsywYzclService.selectWzxFy(msg, result);
                    break;
                case "updateWzxbatch":

                    List<Zyb_brfyModel> gxwzx = null;
                    if (parm.equals("") || parm == null) {
                        gxwzx = new ArrayList<Zyb_brfyModel>();
                    } else {
                        gxwzx = (List<Zyb_brfyModel>) JSONObject.parseArray(parm, Zyb_brfyModel.class);
                    }

                    msg.getParam().put("bean", gxwzx);
                    new1zyysYsywYzclService.updateWzxbatch(msg, result);

                    break;
                case "ddzcycUpdate":
                    HzxxListResModel beandj = null;
                    if (parm.equals("") || parm == null) {
                        beandj = new HzxxListResModel();  //
                    } else {
                        beandj = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }

                    msg.getParam().put("bean", beandj);
                    new1zyysYsywYzclService.ddzcycUpdate(msg, result);
                    break;
                case "getHzZyxxZd":
                    HzxxListResModel HzZyxxZd = null;
                    if (parm.equals("") || parm == null) {
                        HzZyxxZd = new HzxxListResModel();  //
                    } else {
                        HzZyxxZd = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }

                    msg.getParam().put("bean", HzZyxxZd);
                    new1zyysYsywYzclService.getHzZyxxZd(msg, result);
                    break;
                case "brqxrk":
                    HzxxListResModel beanrk = null;
                    if (parm.equals("") || parm == null) {
                        beanrk = new HzxxListResModel();  //
                    } else {
                        beanrk = (HzxxListResModel) JSONObject.parseObject(parm, HzxxListResModel.class);
                    }

                    msg.getParam().put("bean", beanrk);
                    new1zyysYsywYzclService.brqxrk(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(ERROR);
            result.setC(e.getMessage());
            logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
        }
    }
}
