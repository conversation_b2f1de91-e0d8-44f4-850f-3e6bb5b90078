package com.supx.web.business.invocation.zyys.cxtj;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.util.RequestUtil;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.zyys.cxtj.pojo.ZyysCxtjYssrtjResModel;
import com.supx.comm.pojo.DataGrid;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zyys.cxtj.iface.IZyysCxtjYssrtjService;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoZyysCxtjYssrtjAction
 * @Description: TODO(这里用一句话描述这个类的作用)
 * @date 2020年9月28日 上午12:25:33
 */
@Controller("INVO_New1ZyysCxtjYssrtj")
public class InvoZyysCxtjYssrtjAction extends BaseInvocation implements LogicCodeConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyysCxtjYssrtjAction.class);

    @Autowired
    IZyysCxtjYssrtjService new1zyysCxtjYssrtjService;

    /*
     * 验证参数
     */
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyysCxtjYssrtjAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");//排序方式
        DataGrid dGrid;
        if (dg == null || dg.equals("")) {
            dGrid = new DataGrid();
        } else {
            dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
        }
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        System.out.println(msg);

        //获取用户信息
        msg.getUserinfo().put("userinfo", userinfo);
        //获取参数信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);

        switch (optType) {
            case "queryDl":
                ZyysCxtjYssrtjResModel YssrModel = null;
                if (parm.equals("") || parm == null) {
                    YssrModel = new ZyysCxtjYssrtjResModel();
                } else {
                    YssrModel = JSONObject.parseObject(parm, ZyysCxtjYssrtjResModel.class);
                }
                UtilFun.DataGridInit(dGrid, "zyh");
                YssrModel.setRows(dGrid.getRows());
                YssrModel.setPage(dGrid.getPage());
                YssrModel.setSort(dGrid.getSort());
                YssrModel.setOrder(dGrid.getOrder());
                YssrModel.setParm(dGrid.getParm());
                msg.getParam().put("bean", YssrModel);
                new1zyysCxtjYssrtjService.queryYssrDl(msg, result);
                break;
            case "queryMx":
                ZyysCxtjYssrtjResModel YssrModel2 = null;
                if (parm.equals("") || parm == null) {
                    YssrModel2 = new ZyysCxtjYssrtjResModel();
                } else {
                    YssrModel2 = JSONObject.parseObject(parm, ZyysCxtjYssrtjResModel.class);
                }
                UtilFun.DataGridInit(dGrid, "zyh");
                YssrModel2.setRows(dGrid.getRows());
                YssrModel2.setPage(dGrid.getPage());
                YssrModel2.setSort(dGrid.getSort());
                YssrModel2.setOrder(dGrid.getOrder());
                YssrModel2.setParm(dGrid.getParm());
                msg.getParam().put("bean", YssrModel2);
                new1zyysCxtjYssrtjService.queryYssrMx(msg, result);
                break;
        }
    }
}
