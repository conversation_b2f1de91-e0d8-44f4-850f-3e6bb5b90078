package com.supx.web.business.invocation.xtwh.ylfwxm;
import com.supx.comm.pojo.ResultParam;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.pojo.DataGrid;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.xtwh.ylfwxm.pojo.Yfb_yfyycflxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.xtwh.ylfwxm.iface.IXtwhYlfwxmYfyycflxService;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoXtwhYlfwxmYfAction
* @Description: TODO(药房拥有处方类型)
* <AUTHOR>
* @date 2020年6月8日 下午12:01:38
*
 */
@Controller("INVO_New1XtwhYlfwxmYfyycflx")
public class InvoXtwhYlfwxmYfyycflxAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoXtwhYlfwxmYfyycflxAction.class);

	@Autowired
	IXtwhYlfwxmYfyycflxService new1xtwhYlfwxmYfyycflxService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		Integer ref ;
		List<Yfb_yfyycflxModel> beans;
		Yfb_yfyycflxModel bean;
		ResultParam list;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"json","");
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);

		switch (optType) {
		case "save"://新增修改
			bean = RequestUtil.getObjParamter(invoContext.getRequest(),Yfb_yfyycflxModel.class);
			if (bean == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				return;
			}
			msg.getParam().put("bean", bean);
			ref = new1xtwhYlfwxmYfyycflxService.savebatch(msg, result);
			break;
		case "delete"://删除
			if (sjson == null || sjson.equals("")){
				beans = (List<Yfb_yfyycflxModel>)RequestUtil.getListParamter(invoContext.getRequest(),Yfb_yfyycflxModel.class);

			}else {
				beans = (List<Yfb_yfyycflxModel>)JSONObject.parseArray(sjson, Yfb_yfyycflxModel.class);
			}
			msg.getParam().put("bean", beans);
			ref = new1xtwhYlfwxmYfyycflxService.deletebatch(msg, result);
			break;
		case "query"://查询全部
			String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
			DataGrid dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
			Yfb_yfyycflxModel dbfabean;
			if(sjson.equals("")||sjson==null){
				dbfabean=new Yfb_yfyycflxModel();
			}else{
				dbfabean=JSONObject.parseObject(sjson, Yfb_yfyycflxModel.class);
			}
			UtilFun.DataGridInit(dGrid, "");
			dbfabean.setRows(dGrid.getRows());
			dbfabean.setPage(dGrid.getPage());
			dbfabean.setSort(dGrid.getSort());
			dbfabean.setOrder(dGrid.getOrder());
			dbfabean.setParm(dGrid.getParm());
			msg.getParam().put("bean", dbfabean);
			list = new1xtwhYlfwxmYfyycflxService.queryYfb_yfyycflxList(msg, result);
			break;
		default:
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			break;
		}

	}

}
