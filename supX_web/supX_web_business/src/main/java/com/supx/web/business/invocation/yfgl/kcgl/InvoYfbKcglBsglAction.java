package com.supx.web.business.invocation.yfgl.kcgl;

import org.apache.commons.lang.StringUtils;
import com.supx.comm.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ckdModel;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ckdmxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.kcgl.iface.IYfbKcglBsglService;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 *
 * @ClassName: InvoYfbYfywBsglAction
 * @Description: 报损管理
 * <AUTHOR>
 * @date 2020年7月15日 上午1:10:47
 *
 */
@Controller("INVO_New1YfbKcglBsgl")
public class InvoYfbKcglBsglAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYfbKcglBsglAction.class);

	@Resource
	private IYfbKcglBsglService new1yfbKcglBsglService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 获取Session对象 userinfo用户信息
		getSession(invoContext, result);

		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
		msg.getParam().put("userinfo", userinfo);
		msg.getParam().put("csqxinfo", csqxinfo);
		Object object = null;
		switch (optType) {
		case "bsdcx":// 报损库单查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_ckdModel bsd = JSONObject.parseObject(parm, Yfb_ckdModel.class);
				msg.getParam().put("bean", bsd);
				new1yfbKcglBsglService.queryBsd(msg, result);
			}
			break;
		case "bsdmxcx":// 报损单明细查询
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_ckdmxModel bsdmx = JSONObject.parseObject(parm, Yfb_ckdmxModel.class);
				msg.getParam().put("bean", bsdmx);
				new1yfbKcglBsglService.queryBsdmx(msg, result);
			}
			break;
		case "ckdmxtj":// 报损单明细查询统计
			Yfb_ckdmxModel bsdmx = JSONObject.parseObject(parm, Yfb_ckdmxModel.class);
			msg.getParam().put("bean", bsdmx);
			new1yfbKcglBsglService.ckdmxCx(msg, result);
			break;
		case "savebsd":// 保存报损单
			object = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (object == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", object);
				new1yfbKcglBsglService.SaveBsd(msg, result);
			}
			break;
		case "modify"://保存修改报损单
			object = RequestUtil.getListObjParamter(invoContext.getRequest());
			if (object == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("obj", object);
				new1yfbKcglBsglService.modifyBsd(msg, result);
			}
			break;
		case "shbsd":// 审核报损单
			object = RequestUtil.getObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", object);
			msg.getParam().put("crlx", "05");// 05-报损
			new1yfbKcglBsglService.pzsh(msg, result);
			break;
		case "zfbsd":// 作废报损单
			object = RequestUtil.getObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", object);
			new1yfbKcglBsglService.pzzf(msg, result);
			break;
		case "print":// 出库打印
			if (parm.equals("") || parm == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglBsglAction Interface| Requerst Parameter Validation Failed");
			} else {
				Yfb_ckdModel ckp = JSONObject.parseObject(parm, Yfb_ckdModel.class);
				msg.getParam().put("bean", ckp);
				new1yfbKcglBsglService.ckPrint(msg, result);
			}
			break;
		default:
			break;
		}
	}
}
