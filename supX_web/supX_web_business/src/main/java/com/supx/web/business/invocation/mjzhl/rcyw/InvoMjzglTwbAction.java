package com.supx.web.business.invocation.mjzhl.rcyw;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.mjzhl.rcyw.pojo.Mjzgl_TwbListAndQtjlModel;
import com.supx.csp.api.mjzhl.rcyw.pojo.Mjzgl_TwbModel;
import com.supx.csp.api.mjzhl.rcyw.pojo.Mjzgl_TwbqtjlModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.service.mjzhl.rcyw.iface.IMjzglTwdService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 * @ClassName: InvoMjzglTwbAction
 * @Description: TODO(门急诊体温单信息)
 * <AUTHOR>
 * @createTime 2019/3/13
 */
@Controller("INVO_New1MjzglTwd")
public class InvoMjzglTwbAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoMjzglTwbAction.class);

    @Autowired
    private IMjzglTwdService new1MjzglTwdService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        //获取操作类型
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoMjzglTwbAction Interface| Requerst Parameter Validation Failed");
            return;
        }
        super.validateParam(invoContext, result);
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);

        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");//参数对象
        try {
            UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
            msg.getUserinfo().put("userinfo", userinfo);//用户信息
            msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
            msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

            switch (optType) {
                case "save"://保存病人体温单和体温单其他记录
                    //接收对象集合
                    Mjzgl_TwbListAndQtjlModel beans = RequestUtil.getObjParamter(invoContext.getRequest(), Mjzgl_TwbListAndQtjlModel.class);
                    msg.getParam().put("beans", beans);
                    new1MjzglTwdService.saveBatch(msg, result);
                    break;
                case "queryByOneBr"://查询单个病人的体温单信息
//                    String par = parm;
//                    if (parm != null && !parm.equals("")) {
//                        String[] parmSj = parm.split(",");
//                        String sj = null;
//                        if (parmSj.length == 1) {
//                            sj = parmSj[1];
//                        } else {
//                            sj = parmSj[parmSj.length - 1];
//                        }
//                        String[] sjsj = sj.split(":");
//                        String datesj = sjsj[1];
//                        String[] datesjsj = datesj.split("}");
//                        String date2 = datesjsj[0];
//                        String date3 = date2.substring(1, date2.length() - 1);
//                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                        Date date = new Date();
//                        try {
//                            date = format.parse(date3);
//                        } catch (ParseException e1) {
//                            // TODO Auto-generated catch block
//                            e1.printStackTrace();
//                        }
//                        long datet = date.getTime();
//                        if (parmSj.length == 1) {
//                            par = parmSj[0] + ",'clrq':'" + datet + "'}";
//                        } else {
//                            par = parmSj[0] + "," + parmSj[1] + ",'clrq':'" + datet + "'}";
//                        }
//                    }
                    Mjzgl_TwbModel bean = JSONObject.parseObject(parm, Mjzgl_TwbModel.class);
                    msg.getParam().put("bean", bean);
                    new1MjzglTwdService.queryByOneBr(msg, result);
                    break;
                case "queryTwByOneBr"://查询单个病人的体温信息
//                    String twpar = parm;
//                    if (parm != null && !parm.equals("")) {
//                        String[] parmSj = parm.split(",");
//                        String sj = null;
//                        if (parmSj.length == 1) {
//                            sj = parmSj[1];
//                        } else {
//                            sj = parmSj[parmSj.length - 1];
//                        }
//                        String[] sjsj = sj.split(":");
//                        String datesj = sjsj[1];
//                        String[] datesjsj = datesj.split("}");
//                        String date2 = datesjsj[0];
//                        String date3 = date2.substring(1, date2.length() - 1);
//                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                        Date date = new Date();
//                        try {
//                            date = format.parse(date3);
//                        } catch (ParseException e1) {
//                            // TODO Auto-generated catch block
//                            e1.printStackTrace();
//                        }
//                        long datet = date.getTime();
//                        twpar = parmSj[0] + ",'sxrq':'" + datet + "'}";

                        Mjzgl_TwbModel bean2 = JSONObject.parseObject(parm, Mjzgl_TwbModel.class);
                        msg.getParam().put("bean", bean2);
                        new1MjzglTwdService.queryTwByOneBr(msg, result);
                    //}
                    break;
                case "queryMbByOneBr"://查询单个病人的脉搏信息
//                    String mbpar = parm;
//                    if (parm != null && !parm.equals("")) {
//                        String[] parmSj = parm.split(",");
//                        String sj = null;
//                        if (parmSj.length == 1) {
//                            sj = parmSj[1];
//                        } else {
//                            sj = parmSj[parmSj.length - 1];
//                        }
//                        String[] sjsj = sj.split(":");
//                        String datesj = sjsj[1];
//                        String[] datesjsj = datesj.split("}");
//                        String date2 = datesjsj[0];
//                        String date3 = date2.substring(1, date2.length() - 1);
//                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                        Date date = new Date();
//                        try {
//                            date = format.parse(date3);
//                        } catch (ParseException e1) {
//                            // TODO Auto-generated catch block
//                            e1.printStackTrace();
//                        }
//                        long datet = date.getTime();
//
//                        mbpar = parmSj[0] + ",'sxrq':'" + datet + "'}";
//
//
//                    }
                    Mjzgl_TwbModel bean3 = JSONObject.parseObject(parm, Mjzgl_TwbModel.class);
                    msg.getParam().put("bean", bean3);
                    new1MjzglTwdService.queryMbByOneBr(msg, result);
                    break;
                case "queryXtByOneBr"://查询单个病人的心跳信息
//                    String xtpar = parm;
//                    if (parm != null && !parm.equals("")) {
//                        String[] parmSj = parm.split(",");
//                        String sj = null;
//                        if (parmSj.length == 1) {
//                            sj = parmSj[1];
//                        } else {
//                            sj = parmSj[parmSj.length - 1];
//                        }
//                        String[] sjsj = sj.split(":");
//                        String datesj = sjsj[1];
//                        String[] datesjsj = datesj.split("}");
//                        String date2 = datesjsj[0];
//                        String date3 = date2.substring(1, date2.length() - 1);
//                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                        Date date = new Date();
//                        try {
//                            date = format.parse(date3);
//                        } catch (ParseException e1) {
//                            // TODO Auto-generated catch block
//                            e1.printStackTrace();
//                        }
//                        long datet = date.getTime();
//                        xtpar = parmSj[0] + ",'sxrq':'" + datet + "'}";
//
//                    }
                    Mjzgl_TwbModel bean4 = JSONObject.parseObject(parm, Mjzgl_TwbModel.class);
                    msg.getParam().put("bean", bean4);
                    new1MjzglTwdService.queryXtByOneBr(msg, result);
                    break;
                case "queryHxByOneBr"://查询单个病人的呼吸信息
//                    String hxpar = parm;
//                    if (!parm.equals("")) {
//                        String[] parmSj = parm.split(",");
//                        String sj = null;
//                        if (parmSj.length == 1) {
//                            sj = parmSj[1];
//                        } else {
//                            sj = parmSj[parmSj.length - 1];
//                        }
//                        String[] sjsj = sj.split(":");
//                        String datesj = sjsj[1];
//                        String[] datesjsj = datesj.split("}");
//                        String date2 = datesjsj[0];
//                        String date3 = date2.substring(1, date2.length() - 1);
//                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                        Date date = new Date();
//                        try {
//                            date = format.parse(date3);
//                        } catch (ParseException e1) {
//                            // TODO Auto-generated catch block
//                            e1.printStackTrace();
//                        }
//                        long datet = date.getTime();
//
//                        hxpar = parmSj[0] + ",'sxrq':'" + datet + "'}";
//
//                    }
                    Mjzgl_TwbModel bean5 = JSONObject.parseObject(parm, Mjzgl_TwbModel.class);
                    msg.getParam().put("bean", bean5);
                    new1MjzglTwdService.queryHxByOneBr(msg, result);
                    break;
                case "select"://查询单个病人的体温单其他记录信息
//                    String qtpar = parm;
//                    if (parm != null && !parm.equals("")) {
//                        String[] parmSj = parm.split(",");
//                        String sj = null;
//                        if (parmSj.length == 1) {
//                            sj = parmSj[1];
//                        } else {
//                            sj = parmSj[parmSj.length - 1];
//                        }
//                        String[] sjsj = sj.split(":");
//                        String datesj = sjsj[1];
//                        String[] datesjsj = datesj.split("}");
//                        String date2 = datesjsj[0];
//                        String date3 = date2.substring(1, date2.length() - 1);
//                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                        Date date = new Date();
//                        try {
//                            date = format.parse(date3);
//                        } catch (ParseException e1) {
//                            // TODO Auto-generated catch block
//                            e1.printStackTrace();
//                        }
//                        long datet = date.getTime();
//                        qtpar = parmSj[0] + ",'clrq':'" + datet + "'}";
//
//                    }
                    Mjzgl_TwbqtjlModel jsbean = JSONObject.parseObject(parm, Mjzgl_TwbqtjlModel.class);
                    msg.getParam().put("bean", jsbean);
                    new1MjzglTwdService.select(msg, result);
                    break;
                case "queryQtjl":
//                    String otherPar = parm;
//                    if (parm != null && !parm.equals("")) {
//                        String[] parmSj = parm.split(",");
//                        String sj = null;
//                        if (parmSj.length == 1) {
//                            sj = parmSj[1];
//                        } else {
//                            sj = parmSj[parmSj.length - 1];
//                        }
//                        String[] sjsj = sj.split(":");
//                        String datesj = sjsj[1];
//                        String[] datesjsj = datesj.split("}");
//                        String date2 = datesjsj[0];
//                        String date3 = date2.substring(1, date2.length() - 1);
//                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                        Date date = new Date();
//                        try {
//                            date = format.parse(date3);
//                        } catch (ParseException e1) {
//                            // TODO Auto-generated catch block
//                            e1.printStackTrace();
//                        }
//                        long datet = date.getTime();
//                        otherPar = parmSj[0] + ",'sxrq':'" + datet + "'}";
//
//                    }
                    Mjzgl_TwbqtjlModel jsbean2 = JSONObject.parseObject(parm, Mjzgl_TwbqtjlModel.class);
                    msg.getParam().put("bean", jsbean2);
                    new1MjzglTwdService.querytoshow(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(ERROR);
            result.setC(e.getMessage());
            logger.info("New1MjzglTwd Interface| Requerst Parameter Validation Failed");
        }
    }
}
