package com.supx.web.business.invocation.zygl.crygl;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.supx.comm.util.RequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.zygl.crygl.pojo.Pkh_ryxxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.crygl.iface.IZyglCryglPkhryxxService;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoZyglCrygPkhryxxction
 * @Description: TODO(贫困户人员信息)
 * @date 2020年6月21日 上午8:13:27
 */
@Controller("INVO_New1ZyglCryglPkhryxx")
public class InvoZyglCrygPkhryxxction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyglCrygPkhryxxction.class);

    @Autowired
    private IZyglCryglPkhryxxService new1zyglCryglPkhryxxService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyglCrygPkhryxxction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        msg.getUserinfo().put("userinfo", userinfo);//用户信息

        try {
            switch (optType) {
                case "queryByZjhm"://根据住院号或者刷卡查询结算信息
                    //接收对象
                    Pkh_ryxxModel bean = JSONObject.parseObject(parm, Pkh_ryxxModel.class);
                    msg.getParam().put("bean", bean);//对象
                    new1zyglCryglPkhryxxService.queryByZjhm(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoZyglCrygPkhryxxction Interface| Requerst Parameter Validation Failed");
        }
    }
}
