package com.supx.web.business.invocation.yfgl.yfyw;
import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.Utilpubfun;
import com.supx.comm.util.UtilFun;
import com.supx.comm.util.RequestUtil;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_yppfModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yfgl.yfyw.iface.IYfbYfywCffyService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.supx.comm.pojo.DataGrid;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

@Controller("INVO_New1YfbYfywCffy")
public class InvoYfbYfywCffyAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYfbYfywCffyAction.class);

	@Resource
	private IYfbYfywCffyService new1yfbYfywCffyService;

	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// TODO Auto-generated method stub
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoYfbYfywCffyAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 获取Session对象 userinfo用户信息
		getSession(invoContext, result);

		ResultParam list;
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");// 排序方式
		String ksbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "ksbm", "");// 科室编码
		DataGrid dGrid;
		Yfb_ypcfModel cfbean;
		if (dg == null || dg.equals("")) {
			dGrid = new DataGrid();
		} else {
			dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
		}
		try {
			switch (optType) {
			case "cxYpcf":// 查询统计-处方查询
				if (parm.equals("") || parm == null) {
					cfbean = new Yfb_ypcfModel();
				} else {
					cfbean = JSONObject.parseObject(parm, Yfb_ypcfModel.class);
				}
				if (cfbean.getYfbm() == null || cfbean.getYfbm().equals("")) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setC("药房编码不能为空!");
					logger.info("InvoYfbYfywCffyAction Interface| Requerst Parameter Validation Failed");
					return;
				}
				if(dGrid.getSort().equals("")||dGrid.getSort()==null){
					UtilFun.DataGridInit(dGrid, "cfh");
				}
				cfbean.setRows(dGrid.getRows());
				cfbean.setPage(dGrid.getPage());
				cfbean.setSort(dGrid.getSort());
				cfbean.setOrder("desc");
				msg.getParam().put("bean", cfbean);
				list = new1yfbYfywCffyService.queryYpcf(msg, result);
				break;
			case "queryypcf":// 药品处方查询
				if (parm.equals("") || parm == null) {
					cfbean = new Yfb_ypcfModel();
				} else {
					cfbean = JSONObject.parseObject(parm, Yfb_ypcfModel.class);
				}
				if (cfbean.getYfbm() == null || cfbean.getYfbm().equals("")) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setC("药房编码不能为空!");
					logger.info("InvoYfbYfywCffyAction Interface| Requerst Parameter Validation Failed");
					return;
				}
				UtilFun.DataGridInit(dGrid, "cfh");
				cfbean.setRows(dGrid.getRows());
				cfbean.setPage(dGrid.getPage());
				cfbean.setSort(dGrid.getSort());
				if(dGrid.getOrder() != null && !dGrid.getOrder().equals("")){
					cfbean.setOrder(dGrid.getOrder());
				}else{
					cfbean.setOrder("desc");
				}

				//cfbean.setKfbz("1");// 扣费标志
				//cfbean.setZfbz("0");// 作废标志
				// cfbean.setFybz("0");//发药标志
				msg.getParam().put("bean", cfbean);
				list = new1yfbYfywCffyService.queryYpcf(msg, result);
				break;
			case "queryyppf":// 药品配方查询
				Yfb_yppfModel pfbean;
				if (parm.equals("") || parm == null) {
					pfbean = new Yfb_yppfModel();
				} else {
					pfbean = JSONObject.parseObject(parm, Yfb_yppfModel.class);
				}
				UtilFun.DataGridInit(dGrid, "mxxh");
				pfbean.setRows(dGrid.getRows());
				pfbean.setPage(dGrid.getPage());
				pfbean.setSort(dGrid.getSort());
				pfbean.setOrder(dGrid.getOrder());
				msg.getParam().put("bean", pfbean);
				list = new1yfbYfywCffyService.queryYppf(msg, result);
				break;
			case "queryyppfhb":// 药品配方合并查询
				Yfb_yppfModel pfhbbean;
				if (parm.equals("") || parm == null) {
					pfhbbean = new Yfb_yppfModel();
				} else {
					pfhbbean = JSONObject.parseObject(parm, Yfb_yppfModel.class);
				}
				UtilFun.DataGridInit(dGrid, "mxxh");
				pfhbbean.setRows(dGrid.getRows());
				pfhbbean.setPage(dGrid.getPage());
				pfhbbean.setSort(dGrid.getSort());
				pfhbbean.setOrder(dGrid.getOrder());
				msg.getParam().put("bean", pfhbbean);
				list = new1yfbYfywCffyService.queryYppfhb(msg, result);
				break;
			// 处方发药
			case "cffy":
				Yfb_ypcfModel bean = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_ypcfModel.class);
				String fypdjg = Utilpubfun.getCsqx(csqxinfo, "N04003002200202", ksbm);// 发药判断价格
				msg.getParam().put("bean", bean);
				msg.getParam().put("fypdjg", fypdjg);
				msg.getUserinfo().put("userinfo", userinfo);
				new1yfbYfywCffyService.UpdateCffy(msg, result);
				break;
			// 保存追溯码
			case "saveZsm":
				if (!parm.equals("") || parm != null) {
					msg.setBmString(parm);
				}
				new1yfbYfywCffyService.UpdateZsm(msg, result);
				break;
			case "queryBxjkByCfh":
				Yfb_ypcfModel cfBean = JSONObject.parseObject(parm, Yfb_ypcfModel.class);
				msg.getParam().put("bean", cfBean);
				msg.getUserinfo().put("userinfo", userinfo);
				new1yfbYfywCffyService.queryBxjkByCfh(msg, result);
				break;
			case "tiaopei"://调配
				Yfb_ypcfModel cftiaopei = RequestUtil.getObjParamter(invoContext.getRequest(), Yfb_ypcfModel.class);
				msg.getParam().put("bean", cftiaopei);
				msg.getUserinfo().put("userinfo", userinfo);
				new1yfbYfywCffyService.tiaopei(msg, result);
				break;

			case "querylzcf":
				Yfb_ypcfModel lzcfBean = JSONObject.parseObject(parm, Yfb_ypcfModel.class);
				msg.getParam().put("bean", lzcfBean);
				msg.getUserinfo().put("userinfo", userinfo);
				new1yfbYfywCffyService.querylzcf(msg, result);
				break;
			case "lzcfsh":
				Yfb_ypcfModel lzcfshBean = JSONObject.parseObject(parm, Yfb_ypcfModel.class);
				msg.getParam().put("bean", lzcfshBean);
				msg.getUserinfo().put("userinfo", userinfo);
				new1yfbYfywCffyService.lzcfsh(msg, result);
				break;
			case "lzcfscbc":
				break;
			default:
				break;
			}
		} catch (Exception e) {
			result.setLogicCode(ERROR);
			result.setC("处方发药异常:" + e.getMessage());
			logger.info("InvoYfbYfywCffyAction Interface| Requerst Parameter Validation Failed");
		}

	}

}
