package com.supx.web.business.invocation.xmzysz.mzys.ynbtl;

import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.xmzysz.pojo.BqglYnbtlCyryModel;
import com.supx.csp.api.xmzysz.pojo.BqglYnbtlModel;
import com.supx.csp.api.xmzysz.pojo.BqglYnbtlTljlModel;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.service.xmzysz.mzys.ynbtl.iface.IBqglYnbtlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 * @Description: TODO(病区管理疑难病讨论-参与人员)
 */

@Controller("INVO_New1BqglYnbtlCyry")
public class InvoBqglYnbtlCyryAction extends BaseInvocation implements LogicCodeConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoBqglYnbtlCyryAction.class);

    @Autowired
    IBqglYnbtlService new1iBqglYnbtlService;

    @Override
    public void doService(InvocationContext invocationContext, InvocationResult invocationResult) {

        String optType = RequestUtil.getStrParamterAsDef(invocationContext.getRequest(),"types","");
        UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invocationContext);
        BqglYnbtlCyryModel bean;
        BqglYnbtlTljlModel model;
        BqglYnbtlModel ynbtlModel;
        switch (optType)
        {
            //操作：接收，拒绝
            case "operate" :
                bean = RequestUtil.getObjParamter(invocationContext.getRequest(), BqglYnbtlCyryModel.class);
                if (bean == null){
                    invocationResult.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                getSession(invocationContext,invocationResult);
                bean.setYljgbm(userinfo.getYljgbm());
                msg.getParam().put("bean", bean);
                new1iBqglYnbtlService.updateRelYnbtlCyry(msg, invocationResult);
                break;

            case "audit":
                ynbtlModel = RequestUtil.getObjParamter(invocationContext.getRequest(), BqglYnbtlModel.class);
                if (ynbtlModel == null){
                    invocationResult.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                getSession(invocationContext,invocationResult);
                ynbtlModel.setYljgbm(userinfo.getYljgbm());
                msg.getParam().put("bean", ynbtlModel);
                model = RequestUtil.getObjParamter(invocationContext.getRequest(), BqglYnbtlTljlModel.class);
                model.setYljgbm(msg.getYljgbm());
                model.setShrId(userinfo.getCzybm());
                msg.getParam().put("model",model);
                //更新状态
                new1iBqglYnbtlService.updateAllRelByYnbtlId(msg, invocationResult);
                //更新审核人
                new1iBqglYnbtlService.updateYnbtlTljl(msg, invocationResult);
                if("6".equals(model.getStatus()))//审核通过
                {
                    new1iBqglYnbtlService.updateYnbtl(msg, invocationResult);
                }
                break;

            default:
                invocationResult.setLogicCode(PARAM_VALIDATE_ERROR);
                break;

        }
    }
}
