package com.supx.web.business.invocation.zygl.fygl;

import com.supx.comm.constants.UtilRequest;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.fygl.iface.IZyglFyglCwjkjlService;

import org.apache.commons.lang.StringUtils;
import com.supx.comm.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoMzsfSfjsCwjkAction
 * @Description: TODO(住院财务上交)
 * @date 2020年8月18日 下午10:03:33
 */
@Controller("INVO_New1ZyglFyglCwjkjl")
public class InvoZyglFyglCwjkjlAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyglFyglCwjkjlAction.class);

    @Autowired
    private IZyglFyglCwjkjlService new1zyglFyglCwjkjlService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyglFyglCwjkjlAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        msg.getUserinfo().put("userinfo", userinfo);//用户信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
        msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

        try {
            switch (optType) {
                case "save"://住院财务上交
                    String jkpzh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "jkpzh", "");
                    msg.getParam().put("jkpzh", jkpzh);//对象
                    new1zyglFyglCwjkjlService.insert(msg, result);
                    break;
                case "update"://住院取消财务上交
                    String qxjkpzh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "jkpzh", "");
                    msg.getParam().put("jkpzh", qxjkpzh);//对象
                    new1zyglFyglCwjkjlService.update(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoZyglFyglCwjkjlAction Interface| Requerst Parameter Validation Failed");
        }
    }
}
