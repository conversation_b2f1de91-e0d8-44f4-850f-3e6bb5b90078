package com.supx.web.business.invocation.ykgl.kfwh;
import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.xtwh.ylfwxm.pojo.Gyb_mxfyxmModel;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_ypzlModel;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_ypzl_jcModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfwh.iface.IYkgKfwhYpzlService;
import org.apache.commons.lang.StringUtils;
import com.supx.comm.pojo.DataGrid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: InvoYkglKfwhYpsxAction
 * @Description: TODO(库房维护)
 * @date 2020年5月10日 下午11:29:40
 */
@Controller("INVO_New1YkglKfwhYpsx")
public class InvoYkglKfwhYpsxAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoYkglKfwhYpsxAction.class);

    @Autowired
    IYkgKfwhYpzlService new1ykgKfwhYpzlService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        // TODO Auto-generated method stub
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        Integer ref;
        List<Ykb_ypzlModel> beans;
        Ykb_ypzlModel bean;
        ResultParam list;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String ypzlbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "ypzlbm", "");//科室编码
        String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        if (ypzlbm == null) {
            ypzlbm = "";
        }

        switch (optType) {
            case "save":
                bean = (Ykb_ypzlModel) JSONObject.parseObject(sjson, Ykb_ypzlModel.class);
                if (bean == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                msg.getParam().put("bean", bean);
                ref = new1ykgKfwhYpzlService.savebatch(msg, result);
                break;
            case "delete":
                beans = (List<Ykb_ypzlModel>) JSONObject.parseArray(sjson, Ykb_ypzlModel.class);
                msg.getParam().put("bean", beans);
                ref = new1ykgKfwhYpzlService.deletebatch(msg, result);
                break;
            case "query":
                String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");//排序方式
                DataGrid dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
                Ykb_ypzlModel ypzlbean;
                if (sjson.equals("") || sjson == null) {
                    ypzlbean = new Ykb_ypzlModel();
                } else {
                    ypzlbean = JSONObject.parseObject(sjson, Ykb_ypzlModel.class);
                }
                UtilFun.DataGridInit(dGrid, "ypzlbm");
                ypzlbean.setRows(dGrid.getRows());
                ypzlbean.setPage(dGrid.getPage());
                ypzlbean.setSort(dGrid.getSort());
                ypzlbean.setOrder(dGrid.getOrder());
                ypzlbean.setParm(dGrid.getParm());
                msg.getParam().put("bean", ypzlbean);
                list = new1ykgKfwhYpzlService.queryYkbYpzlList(msg, result);
                break;
            case "queryDyFyXm":
            	Gyb_mxfyxmModel fyxm = (Gyb_mxfyxmModel) JSONObject.parseObject(sjson, Gyb_mxfyxmModel.class);
                msg.getParam().put("bean", fyxm);
                list = new1ykgKfwhYpzlService.queryFyXm(msg, result);
                break;
            case "saveYpzlJc": //保存药品种类加成
                Ykb_ypzl_jcModel jcBean = JSONObject.parseObject(sjson, Ykb_ypzl_jcModel.class);
                if (jcBean == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    result.setC("参数为空");
                    return;
                }
                msg.getParam().put("bean", jcBean);
                new1ykgKfwhYpzlService.saveYpzlJc(msg, result);
                break;
            case "delYpzlJc": //删除加成区间
                Ykb_ypzl_jcModel delBean = JSONObject.parseObject(sjson, Ykb_ypzl_jcModel.class);
                if (delBean == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    result.setC("参数为空");
                    return;
                }
                msg.getParam().put("bean", delBean);
                new1ykgKfwhYpzlService.delYpzlJc(msg, result);
                break;
            case "queryYpzlJc": //查询药品种类加成
                Ykb_ypzl_jcModel queryJc;
                if (!StringUtils.isEmpty(sjson)){
                    queryJc = JSONObject.parseObject(sjson, Ykb_ypzl_jcModel.class);
                    if (StringUtils.isEmpty(queryJc.getYpzlbm())){
                        result.setLogicCode(PARAM_VALIDATE_ERROR);
                        result.setC("种类编码参数为空");
                        return;
                    }
                    msg.getParam().put("bean", queryJc);
                    new1ykgKfwhYpzlService.queryYpzlJc(msg, result);
                } else {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    result.setC("参数为空");
                    return;
                }
                break;
        }
    }
}
