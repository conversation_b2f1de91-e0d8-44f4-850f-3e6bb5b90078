package com.supx.web.business.invocation.ykgl.kfwh;
import com.supx.comm.pojo.ResultParam;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.comm.pojo.DataGrid;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_kcxlModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfwh.iface.IYkgKfwhKcxlService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: InvoYkglKfwhKcxlAction
 * @Description: TODO(科室维护)
 * @date 2020年4月26日 下午11:29:40
 */
@Controller("INVO_New1YkglKfwhKcxl")
public class InvoYkglKfwhKcxlAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoYkglKfwhKcxlAction.class);

    @Autowired
    IYkgKfwhKcxlService new1ykgKfwhKcxlService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        // TODO Auto-generated method stub
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        Integer ref;
        Ykb_kcxlModel bean;
        List<Ykb_kcxlModel> beans = null;
        ResultParam list;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        String ypbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "ypbm", "");
        String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
        String kfbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "kfbm", "");
        if (kfbm.equals("null")) {
            kfbm = null;
        }

        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        if (ypbm == null)
            ypbm = "";

        switch (optType) {
            case "save":
                Object jsonObject = RequestUtil.getListObjParamter(invoContext.getRequest());
                if (jsonObject == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                msg.getParam().put("bean", jsonObject);
                ref = new1ykgKfwhKcxlService.savebatch(msg, result);
                break;
            case "update":
                Object updateJson = RequestUtil.getListObjParamter(invoContext.getRequest());
                if (updateJson == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                msg.getParam().put("bean", updateJson);
                ref = new1ykgKfwhKcxlService.update(msg, result);
                break;
            case "query":
                String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");
                DataGrid dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
                UtilFun.DataGridInit(dGrid, "ypbm");
                msg.getDataGrid().put("dg", dGrid);
                list = new1ykgKfwhKcxlService.queryYkbKcxlList(msg, result);
                break;
            case "queryYpzd":
                dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");
                DataGrid g = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
                UtilFun.DataGridInit(g, "ypbm");
                msg.getDataGrid().put("dg", g);
                msg.getParam().put("kfbm", kfbm);
                list = new1ykgKfwhKcxlService.queryYkbYpzd_ypkcList(msg, result);
                break;
            default:
                result.setLogicCode(PARAM_VALIDATE_ERROR);
                break;
        }

    }

}
