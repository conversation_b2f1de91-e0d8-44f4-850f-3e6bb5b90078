package com.supx.web.business.invocation.zygl.cxtj;

import com.supx.comm.pojo.ResultParam;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.zygl.cxtj.pojo.Zygl_Cxtj_Qtzftj_KsjeResModel;
import com.supx.csp.api.zygl.cxtj.pojo.Zygl_Cxtj_Qtzftj_Ksje_flmxModel;
import com.supx.csp.api.zygl.cxtj.pojo.Zygl_Cxtj_Qtzftj_ZflxResModel;
import com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.service.zygl.cxtj.iface.IZyglFyglBrfyCxService;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoZyglCrygBrfyAction
 * @Description: TODO(住院病人费用报表)
 * @date 2020年6月22日 下午4:48:41
 */
@Controller("INVO_New1ZyglFyglBrfyCx")
public class InvoZyglCrygBrfyCxAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyglCrygBrfyCxAction.class);

    @Autowired
    private IZyglFyglBrfyCxService new1zyglFyglBrfyCxService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyglCrygBrfyCxAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        Zyb_brfyModel brfyModel;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        msg.getUserinfo().put("userinfo", userinfo);//用户信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
        msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

        try {
            switch (optType) {
                case "queryBb"://根据住院号或者刷卡分页查询病人费用
                    if (parm.equals("") || parm == null) {
                        brfyModel = new Zyb_brfyModel();
                    } else {
                        brfyModel = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    }
                    msg.getParam().put("bean", brfyModel);//对象
                    ResultParam list = new1zyglFyglBrfyCxService.queryBb(msg, result);
                    break;
                case "queryJehz"://根据住院号或者卡号分组查询分项优惠记录
                    Zyb_brfyModel jebean = JSONObject.parseObject(parm, Zyb_brfyModel.class);
                    msg.getParam().put("bean", jebean);//对象
                    new1zyglFyglBrfyCxService.queryJehz(msg, result);
                    break;
                case "qtzftjZflxhj":
                    Zygl_Cxtj_Qtzftj_ZflxResModel fyModel = null;
                    if (parm.equals("") || parm == null) {
                        fyModel = new Zygl_Cxtj_Qtzftj_ZflxResModel();
                    } else {
                        fyModel = JSONObject.parseObject(parm, Zygl_Cxtj_Qtzftj_ZflxResModel.class);
                    }
                    msg.getParam().put("bean", fyModel);
                    new1zyglFyglBrfyCxService.qtzftjZflxhj(msg, result);
                    break;
                case "qtzftjKshj":
                    Zygl_Cxtj_Qtzftj_KsjeResModel ryModel = null;
                    if (parm.equals("") || parm == null) {
                        ryModel = new Zygl_Cxtj_Qtzftj_KsjeResModel();
                    } else {
                        ryModel = JSONObject.parseObject(parm, Zygl_Cxtj_Qtzftj_KsjeResModel.class);
                    }
                    msg.getParam().put("bean", ryModel);
                    new1zyglFyglBrfyCxService.qtzftjKshj(msg, result);
                    break;
                case "qtzftjKsmx":
                    Zygl_Cxtj_Qtzftj_Ksje_flmxModel rymxModel = null;
                    if (parm.equals("") || parm == null) {
                        rymxModel = new Zygl_Cxtj_Qtzftj_Ksje_flmxModel();
                    } else {
                        rymxModel = JSONObject.parseObject(parm, Zygl_Cxtj_Qtzftj_Ksje_flmxModel.class);
                    }
                    msg.getParam().put("bean", rymxModel);
                    new1zyglFyglBrfyCxService.qtzftjKsmx(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoZyglCrygBrfyCxAction Interface| Requerst Parameter Validation Failed");
        }
    }
}
