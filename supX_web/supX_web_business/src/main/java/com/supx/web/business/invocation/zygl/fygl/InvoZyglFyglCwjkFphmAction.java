package com.supx.web.business.invocation.zygl.fygl;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.mzsf.sfjs.pojo.Mzb_cwjkFphmModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.fygl.iface.IZyglFyglCwjkFphmService;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoMzsfSfjsCwjkFphmAction
 * @Description: TODO(发票号码)
 * @date 2020年8月18日 下午11:10:29
 */
@Controller("INVO_New1ZyglFyglCwjkFphm")
public class InvoZyglFyglCwjkFphmAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyglFyglCwjkFphmAction.class);

    @Autowired
    private IZyglFyglCwjkFphmService new1zyglFyglCwjkFphmService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyglFyglCwjkFphmAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        msg.getUserinfo().put("userinfo", userinfo);//用户信息
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
        msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

        try {
            switch (optType) {
                case "queryFphm"://查询住院交款需要展示的发票号码
                    Mzb_cwjkFphmModel bean = JSONObject.parseObject(parm, Mzb_cwjkFphmModel.class);
                    msg.getParam().put("bean", bean);//对象
                    new1zyglFyglCwjkFphmService.queryFphm(msg, result);
                    break;
                case "queryFphmByjkpzh"://根据交款凭证号查询需要展示的发票号码
                    String jkpzh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "jkpzh", "");
                    msg.getParam().put("jkpzh", jkpzh);//对象
                    new1zyglFyglCwjkFphmService.queryFphmByjkpzh(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoZyglFyglCwjkFphmAction Interface| Requerst Parameter Validation Failed");
        }
    }
}
