package com.supx.web.business.invocation.zygl.crygl;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_jsjl_yhjeModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zygl.crygl.iface.IZyglCryglYhjeService;

import org.apache.commons.lang.StringUtils;
import com.supx.comm.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 *
* @ClassName: InvoZyglCrygYhjeAction
* @Description: TODO(优惠金额)
* <AUTHOR>
* @date 2020年6月22日 下午8:42:08
*
 */
@Controller("INVO_New1ZyglCryglYhje")
public class InvoZyglCrygYhjeAction extends BaseInvocation implements LogicCodeConstants,IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoZyglCrygYhjeAction.class);

	@Autowired
	private IZyglCryglYhjeService new1zyglCryglYhjeService;

	@Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		//接收对象
		Zyb_jsjl_yhjeModel bean = JSONObject.parseObject(parm,Zyb_jsjl_yhjeModel.class);

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
		msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息

		try{
			switch (optType) {
			case "queryByModel"://根据住院号或者刷卡查询结算信息
				msg.getParam().put("bean", bean);//对象
				List<Zyb_jsjl_yhjeModel> yhjeModel= new1zyglCryglYhjeService.queryByModel(msg, result);
				break;
			default:
				break;
			}
		}catch(Exception e){
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
		}
	}
}
