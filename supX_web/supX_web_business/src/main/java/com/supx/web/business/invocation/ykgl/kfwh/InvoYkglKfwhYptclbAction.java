package com.supx.web.business.invocation.ykgl.kfwh;
import com.supx.comm.pojo.ResultParam;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import com.supx.comm.pojo.DataGrid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_yptclbModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfwh.iface.IYkgKfwhYptclbService;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoYkglKfwhYptclbAction
* @Description: TODO(库房维护)
* <AUTHOR>
* @date 2020年5月10日 下午11:29:40
 */
@Controller("INVO_New1YkglKfwhYptclb")
public class InvoYkglKfwhYptclbAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoYkglKfwhYptclbAction.class);

	@Autowired
	IYkgKfwhYptclbService new1ykgKfwhYptclbService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        // TODO Auto-generated method stub
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		Integer ref ;
		List<Ykb_yptclbModel> beans;
		Ykb_yptclbModel bean;
		ResultParam list;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");
		String tclbbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"tclbbm","");
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"json","");
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		if (tclbbm == null){
			tclbbm = "";
		}

		switch (optType) {
		case "save"://新增修改
			bean = (Ykb_yptclbModel)JSONObject.parseObject(sjson, Ykb_yptclbModel.class);
			if (bean == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				return;
			}
			msg.getParam().put("obj", bean);
			ref = new1ykgKfwhYptclbService.savebatch(msg, result);
			break;
		case "delete"://删除
			beans = (List<Ykb_yptclbModel>)JSONObject.parseArray(sjson, Ykb_yptclbModel.class);
			msg.getParam().put("bean", beans);
			ref = new1ykgKfwhYptclbService.deletebatch(msg, result);
			break;
		case "query"://查询全部
			String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
			DataGrid dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
			if(sjson.equals("") || sjson==null){
				bean=new Ykb_yptclbModel();
			}else{
				bean=JSONObject.parseObject(sjson, Ykb_yptclbModel.class);
			}
			UtilFun.DataGridInit(dGrid, "tclbbm");
			bean.setRows(dGrid.getRows());
			bean.setPage(dGrid.getPage());
			bean.setSort(dGrid.getSort());
			bean.setOrder(dGrid.getOrder());
			msg.getParam().put("bean", bean);
			list = new1ykgKfwhYptclbService.queryYkbYptclbList(msg, result);
			break;
		}

	}

}
