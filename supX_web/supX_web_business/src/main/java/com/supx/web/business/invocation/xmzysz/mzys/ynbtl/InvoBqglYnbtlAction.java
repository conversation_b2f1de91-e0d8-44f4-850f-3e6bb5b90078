package com.supx.web.business.invocation.xmzysz.mzys.ynbtl;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.pojo.ResultParam;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.xmzysz.pojo.BqglYnbtlCyryModel;
import com.supx.csp.api.xmzysz.pojo.BqglYnbtlModel;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.pojo.DataGrid;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.xmzysz.mzys.ynbtl.iface.IBqglYnbtlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Random;

import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Controller;

/**
 * @Description: TODO(病区管理疑难病讨论)
 */

@Controller("INVO_New1BqglYnbtl")
public class InvoBqglYnbtlAction extends BaseInvocation implements LogicCodeConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoBqglYnbtlAction.class);

    @Autowired
    IBqglYnbtlService new1iBqglYnbtlService;

    @Override
    public void doService(InvocationContext invocationContext, InvocationResult invocationResult) {

        String optType = RequestUtil.getStrParamterAsDef(invocationContext.getRequest(), "types", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invocationContext);
        String sjson = RequestUtil.getStrParamterAsDef(invocationContext.getRequest(), "json", "");
        BqglYnbtlModel bean;
        BqglYnbtlCyryModel model;
        switch (optType) {
            case "query":
                String dg = RequestUtil.getStrParamterAsDef(invocationContext.getRequest(), "dg", "");
                DataGrid dGrid = JSONObject.parseObject(dg, DataGrid.class);
                if (sjson == null || "".equals(sjson)) {
                    bean = new BqglYnbtlModel();
                } else {
                    bean = JSONObject.parseObject(sjson, BqglYnbtlModel.class);
                }
                UtilFun.DataGridInit(dGrid, "tlsj");
                bean.setRows(dGrid.getRows());
                bean.setPage(dGrid.getPage());
                bean.setSort(dGrid.getSort());
                bean.setOrder(dGrid.getOrder());
                bean.setParm(dGrid.getParm());
                getSession(invocationContext, invocationResult);
                //(当前操作人)
                bean.setCurrentUserId(userinfo.getCzybm());
                msg.getParam().put("bean", bean);
                ResultParam s = new1iBqglYnbtlService.queryBqglYnbtl(msg, invocationResult);
                break;

            case "save":
                //获取参与人
                List<BqglYnbtlCyryModel> cyrList = (List<BqglYnbtlCyryModel>) RequestUtil.getListParamter(invocationContext.getRequest(), BqglYnbtlCyryModel.class);
                //发起讨论的基本信息
                bean = RequestUtil.getObjParamter(invocationContext.getRequest(), BqglYnbtlModel.class);
                if (bean == null) {
                    invocationResult.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                getSession(invocationContext, invocationResult);
                //设置发起人(当前操作人)
                bean.setFqzId(userinfo.getCzybm());
                bean.setYljgbm(userinfo.getYljgbm());
                bean.setKsbm(userinfo.getKsbm());
                //获取ID
                String id = getPrimaryKey();
                bean.setYnbtlId(id);
                msg.getParam().put("bean", bean);
                //保存疑难病讨论
                new1iBqglYnbtlService.saveYnbtl(msg, invocationResult);
                //保存参与人关系
                if (cyrList != null && cyrList.size() > 0) {
                    model = new BqglYnbtlCyryModel();
                    //给主持人也插入一条参与记录,并且不需要接收
                    model.setCyrId(bean.getZcrId());
                    model.setKsbm(bean.getKsbm());
                    model.setStatus("2");
                    cyrList.add(model);
                    for (int i = 0; i < cyrList.size(); i++) {
                        if (i == 0) {
                            model.setYnbtlId(bean.getRelId());
                        }
                        model = cyrList.get(i);
                        model.setRelId(getPrimaryKey());
                        model.setYnbtlId(bean.getYnbtlId());
                        model.setYljgbm(bean.getYljgbm());
                    }
                }
                msg.getParam().put("cyrList", cyrList);
                new1iBqglYnbtlService.saveRelYnbtlCyry(msg, invocationResult);
                break;

            case "update":
                bean = RequestUtil.getObjParamter(invocationContext.getRequest(), BqglYnbtlModel.class);
                if (bean == null) {
                    invocationResult.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                msg.getParam().put("bean", bean);
                //更新疑难病讨论
                new1iBqglYnbtlService.saveYnbtl(msg, invocationResult);
                //删除参与人关系
                new1iBqglYnbtlService.deleteRelYnbtlCyry(msg, invocationResult);
                //重新保存参与人关系
                new1iBqglYnbtlService.saveRelYnbtlCyry(msg, invocationResult);
                break;

            //计算病人是否有讨论正在进行
            case "count":
                if (sjson == null || "".equals(sjson)) {
                    invocationResult.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                } else {
                    bean = JSONObject.parseObject(sjson, BqglYnbtlModel.class);
                }
                msg.getParam().put("bean", bean);
                new1iBqglYnbtlService.count(msg, invocationResult);
                break;
            default:
                invocationResult.setLogicCode(PARAM_VALIDATE_ERROR);
                break;

        }
    }

    /**
     * 生成主键 by zh
     *
     * @return 定长随机字符串(7位) + 时间戳(13位)
     */
    public String getPrimaryKey() {
        String key = "";
        long obj = System.currentTimeMillis();
        String str = "QWERTYUIOPASDFGHJKLZXCVBNM1234567890";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < 7; ++i) {
            int number = random.nextInt(36);
            sb.append(str.charAt(number));
        }
        key = sb.toString() + String.valueOf(obj);
        return key;
    }
}
