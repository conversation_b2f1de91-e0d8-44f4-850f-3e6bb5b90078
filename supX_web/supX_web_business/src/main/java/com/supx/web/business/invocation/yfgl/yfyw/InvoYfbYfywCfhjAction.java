package com.supx.web.business.invocation.yfgl.yfyw;

import com.supx.comm.pojo.ResultParam;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import com.supx.comm.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_CfhjSaveModel;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_yppfModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.comm.pojo.DataGrid;
import com.supx.web.business.service.yfgl.yfyw.iface.IYfbYfywCfhjService;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @ClassName: InvoYfbYfywCfhjAction
 * @Description: TODO(处方划价)
 * @date 2020年5月27日 上午9:35:14
 */
@Component("INVO_New1YfbYfywCfhj")
public class InvoYfbYfywCfhjAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoYfbYfywCfhjAction.class);

    @Resource
    private IYfbYfywCfhjService new1yfbYfywCfhjService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        // TODO Auto-generated method stub
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);
        ResultParam list;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");//排序方式
        DataGrid dGrid;
        if (dg == null || dg.equals("")) {
            dGrid = new DataGrid();
        } else {
            dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
        }
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        try {
            switch (optType) {
                case "queryypcf"://药品处方查询
                    Yfb_ypcfModel cfbean;
                    if (sjson.equals("") || sjson == null) {
                        cfbean = new Yfb_ypcfModel();
                    } else {
                        cfbean = JSONObject.parseObject(sjson, Yfb_ypcfModel.class);
                    }
                    if (cfbean.getYfbm() == null || cfbean.getYfbm().equals("")) {
                        result.setLogicCode(PARAM_VALIDATE_ERROR);
                        result.setC("药房编码不能为空!");
                        logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
                        return;
                    }
                    UtilFun.DataGridInit(dGrid, "cfh");
                    cfbean.setRows(dGrid.getRows());
                    cfbean.setPage(dGrid.getPage());
                    cfbean.setSort(dGrid.getSort());
                    cfbean.setOrder(dGrid.getOrder());
                    cfbean.setBeginrq(dGrid.getBeginrq());
                    cfbean.setEndrq(dGrid.getEndrq());
                    cfbean.setZfbz("0");
                    //模糊查询条件
                    cfbean.setParm(dGrid.getParm());
                    //cfbean.setSfdzcf("0");//是否电子处方
                    msg.getParam().put("bean", cfbean);
                    list = new1yfbYfywCfhjService.queryYpcf(msg, result);
                    break;
                case "queryyppf"://药品配方查询
                    Yfb_yppfModel pfbean;
                    if (sjson.equals("") || sjson == null) {
                        pfbean = new Yfb_yppfModel();
                    } else {
                        pfbean = JSONObject.parseObject(sjson, Yfb_yppfModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "mxxh");
                    pfbean.setRows(dGrid.getRows());
                    pfbean.setPage(dGrid.getPage());
                    pfbean.setSort(dGrid.getSort());
                    pfbean.setOrder(dGrid.getOrder());
                    msg.getParam().put("bean", pfbean);
                    list = new1yfbYfywCfhjService.queryYppf(msg, result);
                    break;
                case "save"://保存
                    List<Yfb_CfhjSaveModel> beans = (List<Yfb_CfhjSaveModel>) RequestUtil.getListParamter(invoContext.getRequest(), Yfb_CfhjSaveModel.class);
                    msg.getParam().put("beans", beans);
                    msg.getUserinfo().put("user", userinfo);
                    new1yfbYfywCfhjService.saveCfhj(msg, result);
                    break;
                case "modifySave"://修改处方保存
                    List<Yfb_CfhjSaveModel> moBeans = (List<Yfb_CfhjSaveModel>) RequestUtil.getListParamter(invoContext.getRequest(), Yfb_CfhjSaveModel.class);
                    msg.getParam().put("beans", moBeans);
                    msg.getUserinfo().put("user", userinfo);
                    new1yfbYfywCfhjService.modifySave(msg, result);
                    break;
                case "zyzdxfy"://住院划价自动写费用
                    List<Yfb_CfhjSaveModel> bean2s = (List<Yfb_CfhjSaveModel>) RequestUtil.getListParamter(invoContext.getRequest(), Yfb_CfhjSaveModel.class);
                    msg.getParam().put("beans", bean2s);
                    msg.getUserinfo().put("user", userinfo);
                    new1yfbYfywCfhjService.zyzdxfy(msg, result);
                    break;
                case "queryByCfh"://根据处方号查询处方信息
                    Yfb_ypcfModel bean = JSONObject.parseObject(sjson, Yfb_ypcfModel.class);
                    msg.getParam().put("bean", bean);//对象
                    new1yfbYfywCfhjService.queryByCfh(msg, result);
                    break;
                case "queryCfToJz"://住院管理处方查询
                    Yfb_ypcfModel cfTobean;
                    if (sjson.equals("") || sjson == null) {
                        cfTobean = new Yfb_ypcfModel();
                    } else {
                        cfTobean = JSONObject.parseObject(sjson, Yfb_ypcfModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "cfh");
                    cfTobean.setRows(dGrid.getRows());
                    cfTobean.setPage(dGrid.getPage());
                    cfTobean.setSort(dGrid.getSort());
                    cfTobean.setOrder(dGrid.getOrder());
                    cfTobean.setZfbz("0");
                    cfTobean.setParm(dGrid.getParm());
                    //cfbean.setSfdzcf("0");//是否电子处方
                    msg.getParam().put("bean", cfTobean);
                    list = new1yfbYfywCfhjService.queryYpcf(msg, result);
                    break;
                case "queryBrcf"://查询病人未扣费的处方
                    Yfb_ypcfModel brcfBean;
                    if (sjson.equals("") || sjson == null) {
                        result.setLogicCode(PARAM_VALIDATE_ERROR);
                        result.setC("病人姓名不能为空!");
                        logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
                        return;
                    } else {
                        brcfBean = JSONObject.parseObject(sjson, Yfb_ypcfModel.class);
                    }
                    UtilFun.DataGridInit(dGrid, "cfh");
                    brcfBean.setRows(dGrid.getRows());
                    brcfBean.setPage(dGrid.getPage());
                    brcfBean.setSort(dGrid.getSort());
                    brcfBean.setOrder(dGrid.getOrder());
                    msg.getParam().put("bean", brcfBean);
                    list = new1yfbYfywCfhjService.queryBrcf(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            result.setC(e.getMessage());
            logger.info("InvoYfbYfywCfhjAction Interface| Requerst Parameter Validation Failed");
        }
    }
}
