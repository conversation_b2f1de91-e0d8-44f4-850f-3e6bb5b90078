package com.supx.web.business.invocation.ykgl.kfwh;

import com.alibaba.fastjson.JSON;
import com.supx.comm.pojo.ResultParam;
import com.supx.comm.util.RequestUtil;
import com.alibaba.fastjson.JSONObject;
import com.supx.comm.pojo.DataGrid;
import com.supx.csp.api.ykgl.kccx.pojo.Ykb_ypkcModel;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_ypzdModel;
import com.supx.comm.util.UtilFun;
import com.supx.comm.constants.UtilRequest;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfwh.iface.IYkgKfwhYpzdService;
import com.supx.web.business.util.excel.PoiExcelExportUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: InvoYkglKfwhYpzdAction
 * @Description: TODO(科室维护)
 * @date 2020年4月26日 下午11:29:40
 */
@Controller("INVO_New1YkglKfwhYpzd")
public class InvoYkglKfwhYpzdAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoYkglKfwhYpzdAction.class);

    @Autowired
    IYkgKfwhYpzdService new1ykgKfwhYpzdService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        // TODO Auto-generated method stub
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void validateLogin(InvocationContext invoContext, InvocationResult result) {

    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);

        Integer ref;
        Ykb_ypzdModel bean;
        List<Ykb_ypzdModel> beans = null;
        ResultParam list;
        String dg;
        DataGrid dGrid;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
        String ypbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "ypbm", "");
        String kfbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "kfbm", "");
        String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", ""); // 操作参数
        String tybz = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "tybz", ""); // 操作参数

        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        if (ypbm == null)
            ypbm = "";

        switch (optType) {
            case "save":// 新增修改
                // 接收对象
                bean = RequestUtil.getObjParamter(invoContext.getRequest(), Ykb_ypzdModel.class);
                if (bean == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                msg.getParam().put("bean", bean);
                msg.getParam().put("userInfo", userinfo);
                ref = new1ykgKfwhYpzdService.savebatch(msg, result);
                break;
            case "delete":// 删除
                if (sjson == null || sjson.equals("")) {
                    beans = (List<Ykb_ypzdModel>) RequestUtil.getListParamter(invoContext.getRequest(),
                            Ykb_ypzdModel.class);
                } else {
                    beans = (List<Ykb_ypzdModel>) JSONObject.parseArray(sjson, Ykb_ypzdModel.class);
                }
                msg.getParam().put("bean", beans);
                ref = new1ykgKfwhYpzdService.deletebatch(msg, result);
                break;

            case "updateBetch": // 针对病案码表批量更新药品字典
                if (sjson == null || sjson.equals("")) {
                    beans = (List<Ykb_ypzdModel>) RequestUtil.getListParamter(invoContext.getRequest(),
                            Ykb_ypzdModel.class);
                } else {
                    beans = (List<Ykb_ypzdModel>) JSONObject.parseArray(sjson, Ykb_ypzdModel.class);
                }
                msg.getParam().put("beans", beans);
                ref = new1ykgKfwhYpzdService.updateBetch(msg, result);
                break;
            case "query":// 查询全部
                // 排序方式
                Map<String, Object> map = JSON.parseObject(sjson);
                dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");
                dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
                UtilFun.DataGridInit(dGrid, "ypbm");
                msg.getDataGrid().put("dg", dGrid);
                msg.getParam().put("tybz", tybz);
                msg.getParam().put("kfbm", kfbm);
                if (sjson.equals("") || sjson == null) {
                    bean = new Ykb_ypzdModel();
                    bean.setTybz((String) map.get("tybz"));
                } else {
                    bean = JSONObject.parseObject(sjson, Ykb_ypzdModel.class);
                }
                msg.getCsqxinfo().put("csqxinfo", csqxinfo);
                msg.getParam().put("bean", bean);
                list = new1ykgKfwhYpzdService.queryYkbYpzdList(msg, result);
                break;
            case "allKc": // 查询药房和药库的库存
                Ykb_ypkcModel kc = new Ykb_ypkcModel();
                kc.setYpbm(ypbm);
                kc.setKfbm(kfbm);
                msg.getParam().put("bean", kc);
                list = new1ykgKfwhYpzdService.queryYkbYpzdKc(msg, result);
                break;
            case "updateByYpzl": // 针对病案码表对应根据药品种类更新药品
                bean = RequestUtil.getObjParamter(invoContext.getRequest(), Ykb_ypzdModel.class);
                if (bean == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                msg.getParam().put("bean", bean);
                ref = new1ykgKfwhYpzdService.updateByYpzl(msg, result);
                break;
            case "export": // 文件导出
                dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");// 排序方式
                dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
                msg.getDataGrid().put("dg", dGrid);
                bean = new Ykb_ypzdModel();
                if (parm.equals("") || parm == null) {
                    bean = new Ykb_ypzdModel();
                } else {
                    bean = (Ykb_ypzdModel) JSONObject.parseObject(parm, Ykb_ypzdModel.class);
                }
                UtilFun.DataGridInit(dGrid, "ypbm");
                bean.setRows(dGrid.getRows());
                bean.setPage(dGrid.getPage());
                bean.setSort(dGrid.getSort());
                bean.setOrder(dGrid.getOrder());
                bean.setYljgbm(dGrid.getParm());
                msg.setYljgbm(dGrid.getParm());
                msg.getParam().put("path", bean.getParm());
                bean.setParm(null);
                msg.getParam().put("bean", bean);
                dGrid.setParm(null);
                msg.getParam().put("kfbm", kfbm);
                new1ykgKfwhYpzdService.exportFile(msg, result);
                List<Ykb_ypzdModel> ypList = (List<Ykb_ypzdModel>) result.getD();
                String[] titles = {"药品字典编码", "药品名称", "药品规格", "化学名称", "化学名称代码", "药品剂型", "药品功效", "药品种类", "药品产地", "标准编码",
                        "药品进价", "药品零价", "库房单位", "分装比例", "药房单位", "医保统筹类型", "农保统筹类型", "基本剂量", "剂量单位", "可拆分标志", "用药方法", "皮试药品",
                        "皮试效期", "抗生素级别", "国家基本药物", "省补基本药物", "DDD数", "绝对含量", "产品标准号", "批准文号", "停用标志", "最大剂量", "服药剂量",
                        "服药单位", "条形码", "自编码", "登记时间"};// 表头数组
                String sheetname = "药品销售统计（按药品汇总）";// 表名
                // String path = (String) msg.getParam().get("path");// 文件存放绝对路径
                XSSFWorkbook workBook = new XSSFWorkbook();
                // 在workbook中添加一个sheet,对应Excel文件中的sheet
                XSSFSheet sheet = workBook.createSheet(sheetname);
                PoiExcelExportUtil exportUtil = new PoiExcelExportUtil(workBook, sheet);
                XSSFCellStyle headStyle = exportUtil.getHeadStyle();
                XSSFCellStyle bodyStyle = exportUtil.getBodyStyle();
                // 构建表头
                XSSFRow headRow = sheet.createRow(0);
                XSSFCell cell = null;
                for (int i = 0; i < titles.length; i++) {
                    cell = headRow.createCell(i);
                    cell.setCellStyle(headStyle);
                    cell.setCellValue(titles[i]);
                }
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                // 构建表体数据
                if (ypList != null && ypList.size() > 0) {
                    // 循环给对应列赋值
                    for (int j = 0; j < ypList.size(); j++) {
                        XSSFRow bodyRow = sheet.createRow(j + 1);
                        Ykb_ypzdModel obj = ypList.get(j);
                        // 药品编码
                        cell = bodyRow.createCell(0);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getYpbm());
                        // 药品名称
                        cell = bodyRow.createCell(1);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getYpmc());
                        // 药品规格
                        cell = bodyRow.createCell(2);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getYpgg());
                        // 化学名称
                        cell = bodyRow.createCell(3);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getHxmc());
                        // 化学名称编码
                        cell = bodyRow.createCell(4);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getHxmcdm());
                        // 剂型名称
                        cell = bodyRow.createCell(5);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getJxmc());
                        // 药品功效
                        cell = bodyRow.createCell(6);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getGxmc());
                        // 种类编码
                        cell = bodyRow.createCell(7);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getZlmc());
                        // 产地编码
                        cell = bodyRow.createCell(8);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getCdmc());
                        // 标准编码
                        cell = bodyRow.createCell(9);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getBzbm());
                        // 药品进价
                        cell = bodyRow.createCell(10);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getYpjj() != null) {
                            cell.setCellValue(obj.getYpjj().toString());
                        } else {
                            cell.setCellValue(0);
                        }
                        // 药品零价
                        cell = bodyRow.createCell(11);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getYplj() != null) {
                            cell.setCellValue(obj.getYplj().toString());
                        } else {
                            cell.setCellValue(0);
                        }
                        // 库房名称
                        cell = bodyRow.createCell(12);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getKfdwmc());
                        // 分装比例
                        cell = bodyRow.createCell(13);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getFzbl());
                        // 药房名称
                        cell = bodyRow.createCell(14);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getYfdwmc());
                        // 医保统筹类别
                        cell = bodyRow.createCell(15);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getYbtclb() == "1" ? "是" : "否");
                        // 农保统筹类别
                        cell = bodyRow.createCell(16);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getNbtclb() == "1" ? "是" : "否");
                        // 基本剂量
                        cell = bodyRow.createCell(17);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getJbjl() != null) {
                            cell.setCellValue(obj.getJbjl().toString());
                        } else {
                            cell.setCellValue(0);
                        }
                        // 剂量名称
                        cell = bodyRow.createCell(18);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getJldwmc());
                        // 可拆分标志
                        cell = bodyRow.createCell(19);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getKcfbz() == "1" ? "是" : "否");
                        // 用药方法
                        cell = bodyRow.createCell(20);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getYyff());
                        // 皮试药品
                        cell = bodyRow.createCell(21);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getPsyp());
                        // 皮试效期
                        cell = bodyRow.createCell(22);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getPsxq() != null) {
                            cell.setCellValue(df.format(obj.getPsxq()));
                        } else {
                            cell.setCellValue(0);
                        }
                        // 抗生素级别
                        cell = bodyRow.createCell(23);
                        cell.setCellStyle(bodyStyle);
                        String kssjb = null;
                        switch (obj.getKssjb() == null ? "5" : obj.getKssjb()) {
                            case "1":
                                kssjb = "非抗生素";
                                break;
                            case "2":
                                kssjb = "非限制使用抗生素";
                                break;
                            case "3":
                                kssjb = "限制使用抗生素";
                                break;
                            case "4":
                                kssjb = "特殊使用抗生素";
                                break;
                            default:
                                kssjb = "未填";
                                break;
                        }
                        cell.setCellValue(kssjb);
                        // 国家基本药物
                        cell = bodyRow.createCell(24);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getGjjbyw() == "1" ? "是" : "否");
                        // 省补基本药物
                        cell = bodyRow.createCell(25);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getSbjbyw() == "1" ? "是" : "否");
                        // DDD
                        cell = bodyRow.createCell(26);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getDdds() != null) {
                            cell.setCellValue(obj.getDdds().toString());
                        } else {
                            cell.setCellValue(0);
                        }
                        // dddhl
                        cell = bodyRow.createCell(27);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getDddHl() != null) {
                            cell.setCellValue(obj.getDddHl().toString());
                        } else {
                            cell.setCellValue(0);
                        }
                        // 产品标准号
                        cell = bodyRow.createCell(28);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getCpbzh());
                        // 批准文号
                        cell = bodyRow.createCell(29);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getPzwh());
                        // 停用标志
                        cell = bodyRow.createCell(30);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getTybz() == "1" ? "是" : "否");
                        // 最大剂量
                        cell = bodyRow.createCell(31);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getZdjl() != null) {
                            cell.setCellValue(obj.getZdjl().toString());
                        } else {
                            cell.setCellValue(0);
                        }
                        // 服药剂量
                        cell = bodyRow.createCell(32);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getFyjl() != null) {
                            cell.setCellValue(obj.getFyjl().toString());
                        } else {
                            cell.setCellValue(0);
                        }
                        // 服药单位
                        cell = bodyRow.createCell(33);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getFydw());
                        // 条形码
                        cell = bodyRow.createCell(34);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getTxm());
                        // 自编码
                        cell = bodyRow.createCell(35);
                        cell.setCellStyle(bodyStyle);
                        cell.setCellValue(obj.getZbm());
                        // 登记日期
                        cell = bodyRow.createCell(36);
                        cell.setCellStyle(bodyStyle);
                        if (obj.getDjrq() != null) {
                            cell.setCellValue(df.format(obj.getDjrq()));
                        } else {
                            cell.setCellValue(0);
                        }
                    }
                }
                String filename = null;
                try {
                    filename = URLEncoder.encode("药品字典.xls", "utf-8");
                } catch (UnsupportedEncodingException e1) {
                    e1.printStackTrace();
                } // 解决中文文件名下载后乱码的问题
                HttpServletResponse resp = invoContext.getResponse();
                resp.setCharacterEncoding("utf-8");
                resp.setHeader("Content-Disposition", "attachment; filename=" + filename + "");
                try {
                    workBook.write(resp.getOutputStream());
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
                ResultParam res = new ResultParam();
                result.setD(workBook);
                result.setLogicCode(SUCCESS);
                break;
            default:
                result.setLogicCode(PARAM_VALIDATE_ERROR);
                break;
        }

    }

}
