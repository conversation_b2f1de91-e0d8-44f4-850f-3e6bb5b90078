package com.supx.web.business.invocation.mjzhl.rcyw;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import com.supx.comm.util.RequestUtil;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
//import com.drew.metadata.exif.makernotes.CanonMakernoteDirectory.CameraSettings;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.mjzhl.rcyw.pojo.Mjzgl_LgdjModel;
import com.supx.csp.api.mjzhl.rcyw.pojo.Mjzgl_PsjgModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.invocation.mzys.zlgl.InvoMzysZlglBrjzAction;
import com.supx.web.business.service.mjzhl.rcyw.iface.IMjzglLgdjService;
import org.springframework.stereotype.Controller;

import java.util.List;

@Controller("INVO_New1MjzhlLgdj")
public class InvoMjzhlLgdjAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoMzysZlglBrjzAction.class);

    @Autowired
    IMjzglLgdjService new1mjzglLgdjService;
    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoMzysZlglBrjzAction 接口有问题：【获取types操作类型失败！】");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);
        Integer ref;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", ""); //操作类型
        String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");         //排序方式
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");     //入参数
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);

        //实列化 排序Model
//		DataGrid dGrid = new DataGrid();
//		if(dg != null && !dg.equals("")){
//			dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
//		}
        msg.getUserinfo().put("user", userinfo);
        //根据Types不同实现不同的接口操作
        try {
            switch (optType) {
                case "savelgdj"://新增\修改
                    Mjzgl_LgdjModel obj = RequestUtil.getObjParamter(invoContext.getRequest(), Mjzgl_LgdjModel.class);
                    msg.getParam().put("obj", obj);
                    ref = new1mjzglLgdjService.savelgdj(msg, result);
                    break;
                case "queryLgdjList":
                    Mjzgl_LgdjModel queryObj;
                    if (StringUtils.isEmpty(parm)){
                        queryObj = new Mjzgl_LgdjModel();
                    }else {
                        queryObj = JSONObject.parseObject(parm, Mjzgl_LgdjModel.class);
                    }
                    msg.getParam().put("bean", queryObj);
                    new1mjzglLgdjService.queryLgdjList(msg, result);
                    break;
                case "savepsjg": //保存皮试结果
                    List<Mjzgl_PsjgModel> list = (List<Mjzgl_PsjgModel>) RequestUtil.getListParamter(invoContext.getRequest(), Mjzgl_PsjgModel.class);
                    msg.getParam().put("obj", list);
                    ref = new1mjzglLgdjService.updatepsjg(msg, result);
                    break;
                case "selectpsjg": //查询皮试结果
                    Mjzgl_PsjgModel psjg;
                    if (StringUtils.isEmpty(dg)) {
                        psjg = new Mjzgl_PsjgModel();
                    } else {
                        psjg = JSONObject.parseObject(dg, Mjzgl_PsjgModel.class);
                    }
                    msg.getParam().put("bean", psjg);
                    new1mjzglLgdjService.selectpsjg(msg, result);
                    break;
                case "cydj"://出院登记
                    Mjzgl_LgdjModel cydjBean = RequestUtil.getObjParamter(invoContext.getRequest(), Mjzgl_LgdjModel.class);
                    msg.getParam().put("obj", cydjBean);
                    ref = new1mjzglLgdjService.saveCydj(msg, result);
                    break;
                case "qxcydj"://取消出院登记
                    Mjzgl_LgdjModel qxdjBean = RequestUtil.getObjParamter(invoContext.getRequest(), Mjzgl_LgdjModel.class);
                    msg.getParam().put("obj", qxdjBean);
                    ref = new1mjzglLgdjService.qxCydj(msg, result);
                    break;
                default:
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(ERROR);
            result.setC(e.getMessage());
            logger.info("New1MjzhlLgdj接口异常：" + e.getMessage());
        }
    }

}
