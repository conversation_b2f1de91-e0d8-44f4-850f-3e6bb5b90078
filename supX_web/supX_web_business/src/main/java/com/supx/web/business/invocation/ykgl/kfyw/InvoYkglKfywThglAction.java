package com.supx.web.business.invocation.ykgl.kfyw;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.ykgl.kfyw.pojo.Ykb_ckdModel;
import com.supx.csp.api.ykgl.kfyw.pojo.Ykb_ckdmxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfyw.iface.IYkgKfywThdService;
import com.supx.comm.pojo.DataGrid;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoYkglKfywThglAction
* @Description: TODO(库房业务-退货管理)
* <AUTHOR> HY
* @date 2020年7月29日 下午3:09:28
*
 */

@Controller("INVO_New1YkglKfywThgl")
public class InvoYkglKfywThglAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{
	private final Logger logger = LoggerFactory.getLogger(InvoYkglKfywThglAction.class);

	@Autowired
	IYkgKfywThdService new1ykgKfywThglService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types",""); //操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types",""); //操作类型
		String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");         //排序方式
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息

		@SuppressWarnings("unused")
		DataGrid dGrid;
		if(dg.isEmpty()){
			dGrid = new DataGrid();
		} else {
			dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
		}

		switch (optType) {
		case "save"://提交所有药品
			Object jsonObject = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", jsonObject);
			new1ykgKfywThglService.savebatch(msg, result);
			break;
		case "modify"://保存或者修改
			Object obj = RequestUtil.getListObjParamter(invoContext.getRequest());
			msg.getParam().put("obj", obj);
			new1ykgKfywThglService.modify(msg, result);
			break;
		case "queryThd"://加载退货单列表信息
			if(parm.equals("") || parm==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
			}else{
				Ykb_ckdModel thd=JSONObject.parseObject(parm, Ykb_ckdModel.class);
				thd.setCkfs("02");
				msg.getParam().put("ckd", thd);
				new1ykgKfywThglService.queryThd(msg, result);
			}
			break;
		case "queryThglMxByThdh"://选中单据信息加载出相对应的单据内容明细
			if(parm.equals("") || parm==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
			}else{
				Ykb_ckdmxModel thdmx=JSONObject.parseObject(parm, Ykb_ckdmxModel.class);
				msg.getParam().put("ckdmx", thdmx);
				new1ykgKfywThglService.queryThdMxByThdh(msg, result);
			}
			break;
		case "zfThd"://作废
			Ykb_ckdModel zfmodel = RequestUtil.getObjParamter(invoContext.getRequest(),Ykb_ckdModel.class);
			if (zfmodel == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
			}else{
				msg.getParam().put("bean", zfmodel);
				new1ykgKfywThglService.zfThd(msg, result);
			}
			break;
		case "shThd"://退货审核
			Ykb_ckdModel shmodel = RequestUtil.getObjParamter(invoContext.getRequest(),Ykb_ckdModel.class);
			if (shmodel == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("对象参无效!");
	            logger.info("InvoYkglKfywCkdAction Interface| Requerst Parameter Validation Failed");
			}else{
				msg.getParam().put("bean", shmodel);
				new1ykgKfywThglService.shThd(msg, result);
			}
			break;
		}

	}

}
