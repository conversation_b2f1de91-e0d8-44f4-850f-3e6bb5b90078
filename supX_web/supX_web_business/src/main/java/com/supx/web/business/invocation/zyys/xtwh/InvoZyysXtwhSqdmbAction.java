package com.supx.web.business.invocation.zyys.xtwh;

import com.supx.comm.pojo.ResultParam;

import java.util.List;

import com.supx.comm.util.RequestUtil;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.pojo.DataGrid;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.zyys.xtwh.pojo.Zyys_sqdmbModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zyys.xtwh.iface.IZyysXtwhSqdmbService;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoZyysXtwhSqdmbAction
 * @Description: TODO(申请单模板)
 * @date 2020年6月12日 上午9:06:04
 */
@Controller("INVO_New1ZyysXtwhSqdmb")
public class InvoZyysXtwhSqdmbAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyysXtwhSqdmbAction.class);
    @Autowired
    private IZyysXtwhSqdmbService new1zyysXtwhSqdmbService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyysXtwhSqdmbAction Interface| Requerst Parameter Validation Failed");
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        Integer ref;
        List<Zyys_sqdmbModel> beans;
        Zyys_sqdmbModel bean;
        ResultParam list;
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        switch (optType) {
            case "save"://新增修改
                bean = RequestUtil.getObjParamter(invoContext.getRequest(), Zyys_sqdmbModel.class);
                if (bean == null) {
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                msg.getParam().put("bean", bean);
                ref = new1zyysXtwhSqdmbService.savebatch(msg, result);
                break;
            case "delete"://删除
                if (parm == null || parm.equals("")) {
                    beans = (List<Zyys_sqdmbModel>) RequestUtil.getListParamter(invoContext.getRequest(), Zyys_sqdmbModel.class);

                } else {
                    beans = (List<Zyys_sqdmbModel>) JSONObject.parseArray(parm, Zyys_sqdmbModel.class);
                }
                msg.getParam().put("bean", beans);
                ref = new1zyysXtwhSqdmbService.deletebatch(msg, result);
                break;
            case "query"://查询全部
                String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "dg", "");//排序方式
                DataGrid dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
                if (parm.equals("") || parm == null) {
                    bean = new Zyys_sqdmbModel();
                } else {
                    bean = JSONObject.parseObject(parm, Zyys_sqdmbModel.class);
                }
                UtilFun.DataGridInit(dGrid, "mbbm");
                bean.setRows(dGrid.getRows());
                bean.setPage(dGrid.getPage());
                bean.setSort(dGrid.getSort());
                bean.setOrder(dGrid.getOrder());
                msg.getParam().put("bean", bean);
                list = new1zyysXtwhSqdmbService.queryList(msg, result);
                break;
            case "queryOne"://查询单个

                if (parm.equals("") || parm == null) {
                    bean = new Zyys_sqdmbModel();
                } else {
                    bean = JSONObject.parseObject(parm, Zyys_sqdmbModel.class);
                }
                msg.getParam().put("baen", bean);
                bean = new1zyysXtwhSqdmbService.queryOne(msg, result);
                break;
            default:
                result.setLogicCode(PARAM_VALIDATE_ERROR);
                break;
        }
    }

}
