package com.supx.web.business.invocation.blsjbg;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.pubfun.pojo.getxh.GetMaxBmModel;
import com.supx.csp.api.pubfun.service.IPubFunCspService;
import com.supx.csp.api.blsjbg.pojo.BlsjbgModel;
import com.supx.comm.constants.UtilResponse;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.blsjbg.iface.IBlsjbgService;
import com.supx.comm.util.RequestUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

@Controller("INVO_New1Blsjbg")
public class InvoBlsjbgAction extends BaseInvocation implements LogicCodeConstants {

    @DubboReference
    private IBlsjbgService blsjbgService;
    @DubboReference
    IPubFunCspService pubFunCspService;

    @Override

    public void doService(InvocationContext invocationContext, InvocationResult invocationResult) {

        String types = RequestUtil.getStrParamterAsDef(invocationContext.getRequest(),"types","");

        UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class,invocationContext);
        BlsjbgModel bean;
        switch (types){

            case "queryList":

                String dg = RequestUtil.getStrParamterAsDef(invocationContext.getRequest(),"df","");
                bean = (BlsjbgModel)JSONObject.parseObject(dg,BlsjbgModel.class);
                getSession(invocationContext,invocationResult);
                bean.setYljgbm(userinfo.getYljgbm());
                bean.setKsbm(userinfo.getKsbm());
                msg.getParam().put("bean",bean);
                blsjbgService.queryList(msg,invocationResult);
                break;

            case "queryOne":
                break;
            case "save":

                bean = RequestUtil.getObjParamter(invocationContext.getRequest(),BlsjbgModel.class);
                if (bean == null){
                    invocationResult.setLogicCode(PARAM_VALIDATE_ERROR);
                    return;
                }
                getSession(invocationContext,invocationResult);
                //设置发起人(当前操作人)
                bean.setBgrbm(userinfo.getCzybm());
                bean.setYljgbm(userinfo.getYljgbm());
                GetMaxBmModel b = new GetMaxBmModel();
                b.setTablename("YWK_HLB_BLSJBG");
                b.setColumnname("id");
                b.setLeninteger(8);
                msg.getParam().put("bean", b);
                UtilResponse res =  pubFunCspService.GetMaxBm(msg);
                String maxbm =  (String)res.getResResult().get("maxbm");
                bean.setId(maxbm);
                bean.setBgzt("1");//已上报状态
                msg.getParam().put("bean",bean);
                blsjbgService.save(msg,invocationResult);
                break;

        }
    }

}
