package com.supx.web.business.invocation.GetDropDown;

import com.supx.comm.pojo.UserInfoModel;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationResult;
import com.supx.comm.util.SessionUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.supx.comm.constants.UtilRequest;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.GetDropDown.iface.IGetDropDownService;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvlGetDropDownAction
 * @Description: TODO(下拉框列表)
 * @date 2020年5月6日 下午11:41:45
 */
@Controller("INVO_GetDropDown")
public class InvlGetDropDownAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvlGetDropDownAction.class);
    private String optType;
    @Autowired
    IGetDropDownService getDropDownService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        // TODO Auto-generated method stub
        optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//下拉框架类型
        logger.info("GetDropDownAction validateParam| optType: " + optType);
        logger.info("GetDropDownAction validateParam| result.isSuccess() before: " + result.isSuccess());
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("GetDropDownAction Interface| Requerst Parameter Validation Failed");
        }
        logger.info("GetDropDownAction validateParam| result.isSuccess() after: " + result.isSuccess());
    }

    // 移除重写的validateLogin方法，使用父类的正常登录验证逻辑

    /**
     * 重写getSession方法，处理可能的空指针异常
     */
    @Override
    public void getSession(InvocationContext invoContext, InvocationResult result) {
        try {
            String userId = invoContext.getRequest().getHeader("userId");
            String jgbm = invoContext.getRequest().getHeader("jgbm");
            String yqbm = invoContext.getRequest().getHeader(IRequestConstants.YQBM);
            if (jgbm == null) {
                jgbm = "";
            }
            if (yqbm == null) {
                yqbm = "";
            }

            // 获取用户信息
            this.userinfo = (UserInfoModel) SessionUtil.getObjectAttribute(
                    invoContext.getRequest(),
                    IRequestConstants.LOGIN_USER_IFNO + userId + "_" + jgbm + "_" + yqbm
            );

            // 获取权限信息
            this.csqxinfo = (java.util.List) SessionUtil.getObjectAttribute(
                    invoContext.getRequest(),
                    IRequestConstants.PARAM_INFO + userId + "_" + jgbm + "_" + yqbm
            );

            // 获取系统配置信息
            this.sysiniinfo = (java.util.List) SessionUtil.getObjectAttribute(
                    invoContext.getRequest(),
                    IRequestConstants.SYSCONFIG
            );

            // 安全地处理IP地址信息
            java.util.Map<String, Object> map = (java.util.Map) SessionUtil.getObjectAttribute(
                    invoContext.getRequest(),
                    IRequestConstants.IPADDR
            );

            if (map != null && !map.isEmpty() && this.userinfo != null) {
                if (map.containsKey("ComputerMac")) {
                    this.userinfo.setMac((String) map.get("ComputerMac"));
                }
                if (map.containsKey("ComputerName")) {
                    this.userinfo.setCname((String) map.get("ComputerName"));
                }
                if (map.containsKey("ComputerIp")) {
                    this.userinfo.setIpaddr((String) map.get("ComputerIp"));
                }
            }

            // 确保csqxinfo不为null
            if (this.csqxinfo == null) {
                this.csqxinfo = new java.util.ArrayList<>();
            }

        } catch (Exception e) {
            logger.error("GetDropDownAction getSession| 获取Session信息失败: " + e.getMessage(), e);
            // 设置默认值
            if (this.csqxinfo == null) {
                this.csqxinfo = new java.util.ArrayList<>();
            }
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        logger.info("GetDropDownAction doService| 开始执行 optType: " + optType);
        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        //获取Session对象 userinfo用户信息
        getSession(invoContext, result);
        /*
         * 调用下拉框接口
         */
        String sjson = (String) msg.getParam().get("json");
        msg.getParam().put("json", sjson);
        msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
        logger.info("GetDropDownAction doService| 调用queryDropDown方法");
        getDropDownService.queryDropDown(invoContext, msg, result);
        logger.info("GetDropDownAction doService| queryDropDown方法执行完成");

    }

}
