package com.supx.web.business.invocation.zyys.ysyw;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.supx.comm.constants.UtilRequest;
import com.supx.csp.api.zyys.xtwh.pojo.Zyys_ksssybaModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.util.RequestUtil;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.zyys.ysyw.iface.IZyysYsywKsssybaService;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @ClassName: InvoZyysYsywKsssybaAction
 * @Description: TODO(抗生素使用备案)
 * @date 2020年6月13日 上午9:52:30
 */
@Controller("INVO_New1ZyysYsywKsssyba")
public class InvoZyysYsywKsssybaAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

    private final Logger logger = LoggerFactory.getLogger(InvoZyysYsywKsssybaAction.class);
    @Autowired
    private IZyysYsywKsssybaService new1zyysYsywKsssybaService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result) {
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        if (StringUtils.isBlank(optType)) {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
            return;
        }
    }

    @Override
    public void doService(InvocationContext invoContext, InvocationResult result) {
        getSession(invoContext, result);
        String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");//操作类型
        String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "parm", "");//实体参数

        UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
        try {
            switch (optType) {
                case "query"://分页查询所有抗生素使用备案
                    Zyys_ksssybaModel bean = null;
                    if (parm.equals("") || parm == null) {
                        bean = new Zyys_ksssybaModel();  //
                    } else {
                        bean = (Zyys_ksssybaModel) JSONObject.parseObject(parm, Zyys_ksssybaModel.class);
                    }
                    msg.getParam().put("bean", bean);
                    new1zyysYsywKsssybaService.query(msg, result);
                    break;
                case "insert":  //添加抗生素使用备案
                    List<Zyys_ksssybaModel> beans = (List<Zyys_ksssybaModel>) RequestUtil.getListParamter(invoContext.getRequest(), Zyys_ksssybaModel.class);
                    //判断有没有操作的对象
                    if (beans.size() == 0) {
                        result.setC("没有可以操作的对象");
                        result.setLogicCode(PARAM_VALIDATE_ERROR);
                        return;
                    }
                    msg.getParam().put("beans", beans);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywKsssybaService.insert(msg, result);
                    break;
                case "update":  //更新抗生素使用备案
                    List<Zyys_ksssybaModel> updatebeans = (List<Zyys_ksssybaModel>) RequestUtil.getListParamter(invoContext.getRequest(), Zyys_ksssybaModel.class);
                    //判断有没有操作的对象
                    if (updatebeans.size() == 0) {
                        result.setC("没有可以操作的对象");
                        result.setLogicCode(PARAM_VALIDATE_ERROR);
                        return;
                    }
                    msg.getParam().put("beans", updatebeans);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywKsssybaService.update(msg, result);
                    break;
                case "zfsq":  //作废申请
                    Zyys_ksssybaModel zfsq = RequestUtil.getObjParamter(invoContext.getRequest(), Zyys_ksssybaModel.class);
                    if (zfsq == null) {
                        result.setLogicCode(PARAM_VALIDATE_ERROR);
                        return;
                    }
                    msg.getParam().put("bean", zfsq);
                    msg.getParam().put("userInfo", userinfo);
                    new1zyysYsywKsssybaService.zfsq(msg, result);
                    break;
                case "queryOrderBy"://分组查询抗生素申请记录
                    Zyys_ksssybaModel fzbean = null;
                    if (parm.equals("") || parm == null) {
                        fzbean = new Zyys_ksssybaModel();  //
                    } else {
                        fzbean = (Zyys_ksssybaModel) JSONObject.parseObject(parm, Zyys_ksssybaModel.class);
                    }
                    msg.getParam().put("bean", fzbean);
                    new1zyysYsywKsssybaService.queryOrderBy(msg, result);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setLogicCode(ERROR);
            result.setC(e.getMessage());
            logger.info("InvoZyysYsywYzclAction Interface| Requerst Parameter Validation Failed");
        }
    }
}
