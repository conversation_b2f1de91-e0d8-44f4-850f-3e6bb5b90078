package com.supx.web.business.invocation.yzpacs;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.supx.csp.api.yzpacs.pojo.OldLisWjzShowModel;
import com.supx.csp.api.jcbg.pojo.JcbgModel;
import com.supx.csp.api.xtwh.ksry.pojo.Gyb_ksbmModel;
import com.supx.csp.api.yzpacs.pojo.OldLis_CydjShowModel;
import com.supx.csp.api.yzpacs.pojo.OldLis_HisSqModel;
import com.supx.csp.api.yzpacs.pojo.OldLis_JydjModel;
import com.supx.csp.api.yzpacs.pojo.Pacs_Yz_StudyReprotModel;
import com.supx.csp.api.zyys.ysyw.pojo.ZyhsModel;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.HttpClientUtil;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.yzpacs.iface.IHis2OldLisService;
import com.supx.web.business.service.yzpacs.iface.IOldLisWjzService;
import com.supx.web.business.service.yzpacs.iface.IYzPacsService;
import com.supx.comm.util.RequestUtil;
import com.supx.web.business.service.zyys.ysyw.iface.IZyysYsywYzclService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("INVO_YzPacsNew")
public class InvoYzPacsAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	private final Logger logger = LoggerFactory.getLogger(InvoYzPacsAction.class);

	@Autowired
	@Qualifier("YzPacsServiceNew")
	IYzPacsService yzPacsService;

	@Autowired
	IHis2OldLisService His2OldLisService;

	@Autowired
	private IZyysYsywYzclService zyysYsywYzclService;

    @Autowired
    private IOldLisWjzService oldLisWjzService;

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		getSession(invoContext,result);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");//参数对象
		try {
			UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
			msg.getUserinfo().put("userinfo", userinfo);//用户信息
			msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息
			msg.getIniconfig().put("sysiniinfo", sysiniinfo);//本地参数信息
			switch (optType) {
				case "queryYzPacs":
					JSONObject parmObj = JSONObject.parseObject(parm);
					String serverUrl = parmObj.getString("serverUrl");
					String zyh = parmObj.getString("zyh");
					String pacsRes = requestYzPacsData(serverUrl, zyh);
					if (StringUtils.isEmpty(pacsRes)){
						result.setLogicCode(PARAM_VALIDATE_ERROR);
						result.setC("检查信息获取失败");
					}else {
						result.setD(pacsRes);
					}
					break;
			case "yzpacs_queryReport"://保存病人体温单和体温单其他记录
				Pacs_Yz_StudyReprotModel bean = JSONObject.parseObject(parm, Pacs_Yz_StudyReprotModel.class);
				// 非空判断
				if (bean == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", bean);
					yzPacsService.yzpacs_queryBgd(msg, result);
				}
				break;
			case "yzpacs_queryReportDetails"://查询检查报告单明细内容
				Pacs_Yz_StudyReprotModel jcbean = JSONObject.parseObject(parm, Pacs_Yz_StudyReprotModel.class);
				// 非空判断
				if (jcbean == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", jcbean);
					yzPacsService.yzpacs_queryReportDetails(msg, result);
				}
				break;
			case "yzpacs_hqsq"://获取申请
				OldLis_HisSqModel sqbean = JSONObject.parseObject(parm, OldLis_HisSqModel.class);
				// 非空判断
				msg.getParam().put("bean", sqbean);
				His2OldLisService.yzpacs_saveJydj(msg, result);
				break;
			case "yzpacs_hqsqByWebService"://获取检验申请
				OldLis_HisSqModel sqbean1 = JSONObject.parseObject(parm, OldLis_HisSqModel.class);
				// 非空判断
				if (sqbean1 == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setC("获取检验申请失败！参数为空！");
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", sqbean1);
					His2OldLisService.yzpacs_hqsqByWebService(msg, result);
				}
				break;
			case "yzpacs_hqjcByWebService"://获取检查申请
				OldLis_HisSqModel sqbean2 = JSONObject.parseObject(parm, OldLis_HisSqModel.class);
				// 非空判断
				if (sqbean2 == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setC("检查项目上传失败！参数为空！");
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", sqbean2);
					His2OldLisService.yzpacs_hqjcByWebService(msg, result);
				}
				break;

			case "yzpacs_hqjcByYzpacs"://@yqq 向翼展pasc指定表中插入数据
				OldLis_HisSqModel sqModel = JSONObject.parseObject(parm, OldLis_HisSqModel.class);
				// 非空判断
				if (sqModel == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setC("检查项目上传失败！参数为空！");
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", sqModel);
					His2OldLisService.yzpacs_hqjcByYzpacs(msg, result);
				}
				break;

			case "yzpacs_jyxmhbByWebService"://项目合并
				String jyxhs = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"jyxhs","");//检验序号
				// 非空判断
				if (jyxhs == null || jyxhs.equals("")) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setC("合并项目失败！参数为空！");
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("jyxhs", jyxhs);
					His2OldLisService.yzpacs_jyxmhbByWebService(msg, result);
				}
				break;
			/*case "yzpacs_hqjcsq"://获取检查申请
				OldLis_HisSqModel jcsqbean = JSONObject.parseObject(parm, OldLis_HisSqModel.class);
				// 非空判断
				msg.getParam().put("bean", jcsqbean);
				His2OldLisService.yzpacs_savePacs(msg, result);
				break;*/
			case "yzpacs_cydj"://采样登记
				List<OldLis_JydjModel> jczxList =(List<OldLis_JydjModel>) RequestUtil.getListParamter(invoContext.getRequest(),OldLis_JydjModel.class);
				msg.getParam().put("beans", jczxList);//对象
				His2OldLisService.yzpacs_updateJydj(msg, result);
				break;
			case "yzpacs_sqcx"://申请查询
				OldLis_CydjShowModel djbean = JSONObject.parseObject(parm, OldLis_CydjShowModel.class);
				// 非空判断
				if (djbean == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", djbean);
					His2OldLisService.yzpacs_queryJyxmToShow(msg, result);
				}
				break;
			case "yzpacs_bgcx"://申请查询
				OldLis_JydjModel cxbean = JSONObject.parseObject(parm, OldLis_JydjModel.class);
				// 非空判断
				if (cxbean == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", cxbean);
					His2OldLisService.yzpacs_bgcx(msg, result);
				}
				break;
			case "yzpacs_bgmxcx"://申请查询
				OldLis_JydjModel cxmxbean = JSONObject.parseObject(parm, OldLis_JydjModel.class);
				// 非空判断
				if (cxmxbean == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", cxmxbean);
					His2OldLisService.yzpacs_bgmxcx(msg, result);
				}
				break;
			case "queryZyhs"://查询相应的病人信息
				ZyhsModel zyhean = JSONObject.parseObject(parm, ZyhsModel.class);
				// 非空判断
				if (zyhean == null) {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				} else {
					msg.getParam().put("bean", zyhean);
					zyysYsywYzclService.queryZyhs(msg,result);
				}
				break;

			case "oldjcbg_queryJcbg"://检查报告
//                JcbgModel bean = new JcbgModel();
//                bean.setItemClass("颅脑、氨区、胸部、心脏");
//                bean.setItemSubClass("m5648");
//                bean.setDiagnoseDoc("雷强");
//                bean.setReportTime("2020年7月8号 15:42:00");
//                bean.setExamView("左、右冠状动脉开口正常。右冠状动脉近段支架的自管内膜过度增生，伴官腔变窄；支架内官腔尚通畅。余段管壁未见明确斑块形成。");
//                bean.setExamResult("1.冠状动脉呈右侧优势型；\n2.左前降支近端软斑形成，近中段肌桥形成考虑");
//
//                JcbgModel bean2 = new JcbgModel();
//                bean2.setItemClass("颈椎、腰椎");
//                bean2.setItemSubClass("m5648");
//                bean2.setDiagnoseDoc("雷强");
//                bean2.setReportTime("2020年7月9号 11:25:06");
//                bean2.setExamView("颈椎曲度轻度后弓改变，各椎体骨质未见异常；C4-C6椎间隙稍显狭窄，C5_6椎间盘突向后方，导致硬膜囊前间隙受压变窄。");
//                bean2.setExamResult("1.脱髓鞘迟发性脊髓病变");
//
//                List<JcbgModel> list = Arrays.asList(bean, bean2);
//                result.setD(list);
				JcbgModel jcbgModel = JSONObject.parseObject(parm, JcbgModel.class);
				if(jcbgModel==null)
				{
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				}
				else
				{
					msg.getParam().put("bean", jcbgModel);
					His2OldLisService.oldjcbg_queryJcbg(msg,result);
				}
				break;

			case "oldjcbg_queryJcbgDetail"://检查报告详情
				JcbgModel model = JSONObject.parseObject(parm, JcbgModel.class);
				if(model==null)
				{
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				}
				else
				{
					msg.getParam().put("bean", model);
					His2OldLisService.oldjcbg_queryJcbgDetail(msg,result);
				}
				break;
			case "testRTF2Html":
				/*DzblModel dzblModel = JSONObject.parseObject(parm, DzblModel.class);
				if(dzblModel==null)
				{
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				}
				//下载文件到本地
				String remoteFilePath = dzblModel.getMc();
				URL remoteFileUrl = null;
				HttpURLConnection httpUrl = null;
				BufferedInputStream bis = null;
				BufferedOutputStream bos = null;
				//取文件名
				String fileName = remoteFilePath.substring(remoteFilePath.lastIndexOf("/") + 1);
				HttpServletRequest request = invoContext.getRequest();
				ServletContext sc = request.getSession().getServletContext();
				//下载到项目中
//				String prePath = sc.getRealPath("/newzui/hisfile/");
				String prePath = sc.getRealPath("/newzui") + "/hisfile/";
				String relativePath = prePath + fileName;

				File f = new File(relativePath);

				remoteFileUrl = new URL(remoteFilePath);
				httpUrl = (HttpURLConnection) remoteFileUrl.openConnection();
				httpUrl.connect();
				bis = new BufferedInputStream(httpUrl.getInputStream());
				bos = new BufferedOutputStream(new FileOutputStream(f));
				int len = 2048;
				byte[] b = new byte[len];
				while ((len = bis.read(b)) != -1) {
					bos.write(b, 0, len);
				}
				bos.flush();
				bis.close();
				httpUrl.disconnect();

				File file = new File(relativePath);
				byte[] bytes = Files.toByteArray(file);
		        AutoDetectParser tikaParser = new AutoDetectParser();
		        ByteArrayOutputStream out = new ByteArrayOutputStream();
		        SAXTransformerFactory factory = (SAXTransformerFactory) SAXTransformerFactory.newInstance();
		        TransformerHandler handler;
		        try {
		            handler = factory.newTransformerHandler();
		        } catch (TransformerConfigurationException ex) {
		            throw new IOException(ex);
		        }
		        handler.getTransformer().setOutputProperty(OutputKeys.METHOD, "html");
		        handler.getTransformer().setOutputProperty(OutputKeys.INDENT, "no");
		        handler.getTransformer().setOutputProperty(OutputKeys.ENCODING, "UTF-8");
		        handler.setResult(new StreamResult(out));
		        ExpandedTitleContentHandler handler1 = new ExpandedTitleContentHandler(handler);
		        try {
		            tikaParser.parse(new ByteArrayInputStream(bytes), handler1, new Metadata());
		        } catch (SAXException | TikaException ex) {
		            throw new IOException(ex);
		        }finally {
                    if(bis!=null)
                    {
                        bis.close();
                    }
                    if(bos!=null)
                    {
                        bos.close();
                    }
                    if(out!=null)
                    {
                        out.close();
                    }
                    if(file.isFile())
                    {
                        file.delete();
                    }
                }
		        String rs = new String(out.toByteArray(), "UTF-8");
		        result.setD(rs);
		        System.out.println(rs);
				*/
//				String ip=getIpAddr(invoContext.getRequest());
//		        System.out.println(ip);
//		        result.setD(ip);

				break;
			case "saveZyywWjz"://处理危急值，并同步到his数据库

String param = RequestUtil.getListObjParamter(invoContext.getRequest()).toString();
				OldLisWjzShowModel oldLisWjzShowModel = JSON.parseObject(param,OldLisWjzShowModel.class);
				if(oldLisWjzShowModel == null){
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					result.setC("处理危急值失败，失败原因【参数为空】！");
					logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
				}else{
					msg.getParam().put("bean", oldLisWjzShowModel);
					msg.getParam().put("userinfo", userinfo);
					His2OldLisService.saveZyywWjz(msg,result);
				}
				break;
            case "queryWjz":
                OldLisWjzShowModel oldLisWjzShowModel1 = JSON.parseObject(parm,OldLisWjzShowModel.class);
                if(oldLisWjzShowModel1 == null){
                    result.setLogicCode(PARAM_VALIDATE_ERROR);
                    result.setC("危急值查询失败，失败原因【参数为空】！");
                    logger.info("InvoYkglKfcxCrcxAction Interface| Requerst Parameter Validation Failed");
                }else{
                    List<Gyb_ksbmModel> list = oldLisWjzShowModel1.getSearchksbmList();
                    if(list == null || list.size()<=0){
                        String[] searchKsbm = new String[1];
                        searchKsbm[0] = userinfo.getKsbm();
                    }else{
                        String[] searchKsbm = new String[list.size()];
                        for (int i = 0; i < list.size(); i++) {
                            searchKsbm[i] = list.get(i).getKsbm();
                        }
                        oldLisWjzShowModel1.setSearchksbm(searchKsbm);
                    }
                    msg.getParam().put("bean", oldLisWjzShowModel1);
                    oldLisWjzService.queryWjz(msg,result);
                }
                break;
            case "updateDybz":// 更新打印标志
				List<OldLis_CydjShowModel> updateDybzArr =(List<OldLis_CydjShowModel>) RequestUtil.getListParamter(invoContext.getRequest(),OldLis_CydjShowModel.class);
				msg.getParam().put("updateDybzArr", updateDybzArr);//对象
				His2OldLisService.updateDybz(msg, result);
				break;
            case "yzpacs_dysjtb"://打印数据同步
            	String xhs = JSONObject.parseObject(parm).getString("jyxhs");
				msg.getParam().put("xhs", xhs);//对象
				His2OldLisService.yzpacs_dysjtb(msg, result);
				break;
            case "yzpacs_checkBgjg":// 查询检验是否已出报告
            	String xhArr = JSONObject.parseObject(parm).getString("jyxhs");
            	msg.getParam().put("xhs", xhArr);//对象
            	His2OldLisService.yzpacs_checkBgjg(msg, result);
            	break;
            case "updateHisbgdybz":// 更新his报告打印标志
				List<OldLis_CydjShowModel> arr =(List<OldLis_CydjShowModel>) RequestUtil.getListParamter(invoContext.getRequest(),OldLis_CydjShowModel.class);
				msg.getParam().put("updateHisbgdybzArr", arr);//对象
				His2OldLisService.updateHisbgdybz(msg, result);
				break;
			case "updateJyzx":// 查询检验是否已出报告
				String xhArr1 = JSONObject.parseObject(parm).getString("jyxhs");
				msg.getParam().put("xhs", xhArr1);//对象
				His2OldLisService.updateJyzx(msg, result);
				break;
				default:
				break;
			}
		} catch (Exception e) {
			result.setLogicCode(ERROR);
			result.setC(e.getMessage());
            logger.info("InvoYzPacsAction Interface| Requerst Parameter Validation Failed");
		}
	}

	private String getIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
        System.out.println("x-forwarded-for ip: " + ip);
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            if( ip.indexOf(",")!=-1 ){
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
            System.out.println("Proxy-Client-IP ip: " + ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
            System.out.println("WL-Proxy-Client-IP ip: " + ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
            System.out.println("HTTP_CLIENT_IP ip: " + ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            System.out.println("HTTP_X_FORWARDED_FOR ip: " + ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
            System.out.println("X-Real-IP ip: " + ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            System.out.println("getRemoteAddr ip: " + ip);
        }
        System.out.println("获取客户端ip: " + ip);
        return ip;
	   }

	private String requestYzPacsData(String serverUrl, String zyh) throws Exception{
//		String url = "http://*************:8010/PACSService.asmx/GetPACSExamRequestList";
		Map<String,String> parmMap = new HashMap<>();
		parmMap.put("strGatewayData", "<Object><Field Name=\"HISID\" Type=\"System.String\">"+zyh+"</Field></Object>");
		String result = HttpClientUtil.getInstance().doPost(serverUrl, parmMap);
		return result;
	}
}
