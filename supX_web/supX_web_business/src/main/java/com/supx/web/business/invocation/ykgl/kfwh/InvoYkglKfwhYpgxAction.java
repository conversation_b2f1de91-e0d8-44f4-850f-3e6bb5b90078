package com.supx.web.business.invocation.ykgl.kfwh;
import com.supx.comm.pojo.ResultParam;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import com.supx.comm.pojo.DataGrid;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.supx.comm.util.RequestUtil;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.util.UtilFun;
import com.supx.csp.api.ykgl.kfwh.pojo.Ykb_ypgxModel;
import com.supx.web.api.constants.IRequestConstants;
import com.supx.comm.constants.BaseInvocation;
import com.supx.comm.constants.InvocationContext;
import com.supx.comm.constants.InvocationResult;
import com.supx.web.business.constants.LogicCodeConstants;
import com.supx.web.business.service.ykgl.kfwh.iface.IYkgKfwhYpgxService;
import org.springframework.stereotype.Controller;

/**
 *
* @ClassName: InvoYkglKfwhYpgxAction
* @Description: TODO(库房维护)
* <AUTHOR>
* @date 2020年5月10日 下午11:29:40
 */
@Controller("INVO_New1YkglKfwhYpgx")
public class InvoYkglKfwhYpgxAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoYkglKfwhYpgxAction.class);

	@Autowired
	IYkgKfwhYpgxService new1ykgKfwhYpgxService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
        // TODO Auto-generated method stub
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		Integer ref ;
		List<Ykb_ypgxModel> beans;
		Ykb_ypgxModel bean;
		ResultParam list;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");
		String gxbm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"gxbm","");
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"json","");
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		if (gxbm == null){
			gxbm = "";
		}

		switch (optType) {
		case "save"://新增修改
			bean = (Ykb_ypgxModel)JSONObject.parseObject(sjson, Ykb_ypgxModel.class);
			if (bean == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				return;
			}
			msg.getParam().put("bean", bean);
			ref = new1ykgKfwhYpgxService.savebatch(msg, result);
			break;
		case "delete"://删除
			beans = (List<Ykb_ypgxModel>)JSONObject.parseArray(sjson, Ykb_ypgxModel.class);
			msg.getParam().put("bean", beans);
			ref = new1ykgKfwhYpgxService.deletebatch(msg, result);
			break;
		case "query"://查询全部
			String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
			DataGrid dGrid = (DataGrid) JSONObject.parseObject(dg, DataGrid.class);
			UtilFun.DataGridInit(dGrid, "gxbm");
			msg.getDataGrid().put("dg", dGrid);
			list = new1ykgKfwhYpgxService.queryYkbYpgxList(msg, result);
			break;
		}

	}

}
