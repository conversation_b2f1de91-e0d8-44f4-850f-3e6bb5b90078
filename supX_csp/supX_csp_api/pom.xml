<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.supx.csp</groupId>
		<artifactId>supX_csp</artifactId>
		<version>0.0.1</version>
	</parent>
	<artifactId>supX_csp_api</artifactId>
	<name>supX_csp_api</name>

	<dependencies>
		<!-- 依赖公共API模块 -->
		<dependency>
			<groupId>com.supx.comm</groupId>
			<artifactId>supX_comm_api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.supx.comm</groupId>
			<artifactId>supX_comm_util</artifactId>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
	</dependencies>
</project>
