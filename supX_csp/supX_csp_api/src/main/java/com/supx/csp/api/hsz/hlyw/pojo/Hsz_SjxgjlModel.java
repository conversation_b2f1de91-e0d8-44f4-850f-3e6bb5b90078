package com.supx.csp.api.hsz.hlyw.pojo;
import com.supx.comm.pojo.DataGrid;

import java.io.Serializable;
import java.util.Date;



public class Hsz_SjxgjlModel extends DataGrid implements Serializable{
    private String xgjlid;

    private String yljgbm;

    private String zyh;

    private String xglx;

    private String czybm;

    private String czyxm;

    private Date czsj;

    private String xgnr;

    public String getXgjlid() {
        return xgjlid;
    }

    public void setXgjlid(String xgjlid) {
        this.xgjlid = xgjlid == null ? null : xgjlid.trim();
    }

    public String getYljgbm() {
        return yljgbm;
    }

    public void setYljgbm(String yljgbm) {
        this.yljgbm = yljgbm == null ? null : yljgbm.trim();
    }

    public String getZyh() {
        return zyh;
    }

    public void setZyh(String zyh) {
        this.zyh = zyh == null ? null : zyh.trim();
    }

    public String getXglx() {
        return xglx;
    }

    public void setXglx(String xglx) {
        this.xglx = xglx == null ? null : xglx.trim();
    }

    public String getCzybm() {
        return czybm;
    }

    public void setCzybm(String czybm) {
        this.czybm = czybm == null ? null : czybm.trim();
    }

    public String getCzyxm() {
        return czyxm;
    }

    public void setCzyxm(String czyxm) {
        this.czyxm = czyxm == null ? null : czyxm.trim();
    }

    public Date getCzsj() {
        return czsj;
    }

    public void setCzsj(Date czsj) {
        this.czsj = czsj;
    }

    public String getXgnr() {
        return xgnr;
    }

    public void setXgnr(String xgnr) {
        this.xgnr = xgnr == null ? null : xgnr.trim();
    }
}
