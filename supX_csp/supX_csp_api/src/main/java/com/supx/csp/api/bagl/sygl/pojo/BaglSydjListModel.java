package com.supx.csp.api.bagl.sygl.pojo;
import com.supx.comm.pojo.DataGrid;

import java.io.Serializable;
import java.util.Date;



/**
 *
* @ClassName: BaglSydjList
* @Description: TODO(主要用于归档和接收列表展示数据用)
* <AUTHOR>
* @date 2020年9月20日 下午5:41:46
*
 */
public class BaglSydjListModel extends DataGrid implements Serializable{

	private String brid; //病人id

	private String zyh; //住院号

	private String bah;//病案号

	private String brxm;//病人姓名

	private String brxb;//病人性别

	private String sfzjhm;//身份证件号码

	private String dwdz;//单位地址

	private String gzdw ; //工作单位

	private String sjhm; //手机号码

	private String ryksmc;//入院科室名称

	private String cyksmc;//出院科室名称

	private Date csrq;//出生日期

	private int nl; //年龄

	private String nldw; //年龄单位

	private String ryrq; //入院日期

	private String ryzdmcBz; //入院主要诊断名称

	private String cyzyzdmcBz;//出院主要诊断名称

	private String zyysxm;  //住院医生姓名

	private String jsbz; //接收标志

	private Date jsrq;  //接收日期

	private String jsryxm; //接收人员姓名

	private String gdbz; //归档标志

	private Date gdrq; //归档日期

    private String gdryxm; //归档人员姓名



	public String getBrid() {
		return brid;
	}

	public void setBrid(String brid) {
		this.brid = brid;
	}

	public String getZyh() {
		return zyh;
	}

	public void setZyh(String zyh) {
		this.zyh = zyh;
	}

	public String getBah() {
		return bah;
	}

	public void setBah(String bah) {
		this.bah = bah;
	}

	public String getBrxm() {
		return brxm;
	}

	public void setBrxm(String brxm) {
		this.brxm = brxm;
	}

	public String getBrxb() {
		return brxb;
	}

	public void setBrxb(String brxb) {
		this.brxb = brxb;
	}

	public String getSfzjhm() {
		return sfzjhm;
	}

	public void setSfzjhm(String sfzjhm) {
		this.sfzjhm = sfzjhm;
	}

	public String getDwdz() {
		return dwdz;
	}

	public void setDwdz(String dwdz) {
		this.dwdz = dwdz;
	}

	public String getCyksmc() {
		return cyksmc;
	}

	public void setCyksmc(String cyksmc) {
		this.cyksmc = cyksmc;
	}

	public Date getCsrq() {
		return csrq;
	}

	public void setCsrq(Date cyrq) {
		this.csrq = cyrq;
	}

	public String getGzdw() {
		return gzdw;
	}

	public void setGzdw(String gzdw) {
		this.gzdw = gzdw;
	}

	public String getSjhm() {
		return sjhm;
	}

	public void setSjhm(String sjhm) {
		this.sjhm = sjhm;
	}

	public String getRyksmc() {
		return ryksmc;
	}

	public void setRyksmc(String ryksmc) {
		this.ryksmc = ryksmc;
	}

	public int getNl() {
		return nl;
	}

	public void setNl(int nl) {
		this.nl = nl;
	}

	public String getNldw() {
		return nldw;
	}

	public void setNldw(String nldw) {
		this.nldw = nldw;
	}

	public String getRyrq() {
		return ryrq;
	}

	public void setRyrq(String ryrq) {
		this.ryrq = ryrq;
	}

	public String getRyzdmcBz() {
		return ryzdmcBz;
	}

	public void setRyzdmcBz(String ryzdmcBz) {
		this.ryzdmcBz = ryzdmcBz;
	}

	public String getCyzyzdmcBz() {
		return cyzyzdmcBz;
	}

	public void setCyzyzdmcBz(String cyzyzdmcBz) {
		this.cyzyzdmcBz = cyzyzdmcBz;
	}

	public String getZyysxm() {
		return zyysxm;
	}

	public void setZyysxm(String zyysxm) {
		this.zyysxm = zyysxm;
	}

	public Date getJsrq() {
		return jsrq;
	}

	public void setJsrq(Date jsrq) {
		this.jsrq = jsrq;
	}

	public String getJsryxm() {
		return jsryxm;
	}

	public void setJsryxm(String jsryxm) {
		this.jsryxm = jsryxm;
	}

	public Date getGdrq() {
		return gdrq;
	}

	public void setGdrq(Date gdrq) {
		this.gdrq = gdrq;
	}

	public String getGdryxm() {
		return gdryxm;
	}

	public void setGdryxm(String gdryxm) {
		this.gdryxm = gdryxm;
	}

	public String getJsbz() {
		return jsbz;
	}

	public void setJsbz(String jsbz) {
		this.jsbz = jsbz;
	}

	public String getGdbz() {
		return gdbz;
	}

	public void setGdbz(String gdbz) {
		this.gdbz = gdbz;
	}

}
