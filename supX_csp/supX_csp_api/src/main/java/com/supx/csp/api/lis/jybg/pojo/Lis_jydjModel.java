package com.supx.csp.api.lis.jybg.pojo;

import com.supx.comm.pojo.DataGrid;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 检验登记
 * <AUTHOR>
 *
 */
public class Lis_jydjModel extends DataGrid implements Serializable {
	private String yljgbm;

    private String jyxh;

    private String lx;

    private String jzbz;

    private String bah;

    private String brxm;

    private String xb;

    private Date csrq;

    private String ksbm;

    private String cwh;

    private String sqys;

    private Date sqrq;

    private String ybbm;

    private Date cyrq;

    private String lczd;

    private String jyxm;

    private String bbbh;

    private String czy;

    private Date djrq;

    private String zfbz;

    private String zfry;

    private Date zfrq;

    private String jyff;

    private String zxbz;

    private String zxys;

    private String zxsb;

    private String zxks;

    private Date zxrq;

    private String shbz;

    private String shry;

    private Date shrq;

    private String sqdh;

    private BigDecimal sqdxh;

    private BigDecimal fyje;

    private String bz;

    private Short nl;

    private String nldw;

    private String isprint;

    private String yblx;

    private String zklx;

    private String ly;

    private String kfbz;

    private String fyjlid;

    private String tjdw;

    private String jyxhHb;

    private String brdm;

    private String codePrint;

    private String fymc;

    private Date ybhsrq;

    private String ybhsr;

    private String ybhssm;

    private String ffbz;

    private String wswlx;

    private String ybhsbz;

    private String sqdPrint;

    private String cybz;

    private String ysckbz;

    private String ybhHb;

    private String zzdybz;

    private String jscl;

    private String ybjsbz;

    private String ybjsr;

    private Date ybjsrq;

    private String psbz;

    private String psry;

    private Date pssj;

    private Date sjsj;

    private String cyczy;

    private String zlbm;

    private String ybjssm;

    private String bzsm;

    private String clbm;

    private String ksmc;

    private String sqysxm;

    private String jyxmmc;

    private String czyxm;

    private String zfryxm;

    private String zxysxm;

    private String zxksmc;

    private String shryxm;

    private String ybhsrxm;

    private String cyczyxm;

    private String ybmc;

    private String jyffmc;

    private List<Lis_jydj_mxModel> jydjmxList; //检验登记明细

    public String getYljgbm() {
        return yljgbm;
    }

    public void setYljgbm(String yljgbm) {
        this.yljgbm = yljgbm == null ? null : yljgbm.trim();
    }

    public String getJyxh() {
        return jyxh;
    }

    public void setJyxh(String jyxh) {
        this.jyxh = jyxh == null ? null : jyxh.trim();
    }

    public String getLx() {
        return lx;
    }

    public void setLx(String lx) {
        this.lx = lx == null ? null : lx.trim();
    }

    public String getJzbz() {
        return jzbz;
    }

    public void setJzbz(String jzbz) {
        this.jzbz = jzbz == null ? null : jzbz.trim();
    }

    public String getBah() {
        return bah;
    }

    public void setBah(String bah) {
        this.bah = bah == null ? null : bah.trim();
    }

    public String getBrxm() {
        return brxm;
    }

    public void setBrxm(String brxm) {
        this.brxm = brxm == null ? null : brxm.trim();
    }

    public String getXb() {
        return xb;
    }

    public void setXb(String xb) {
        this.xb = xb == null ? null : xb.trim();
    }

    public Date getCsrq() {
        return csrq;
    }

    public void setCsrq(Date csrq) {
        this.csrq = csrq;
    }

    public String getKsbm() {
        return ksbm;
    }

    public void setKsbm(String ksbm) {
        this.ksbm = ksbm == null ? null : ksbm.trim();
    }

    public String getCwh() {
        return cwh;
    }

    public void setCwh(String cwh) {
        this.cwh = cwh == null ? null : cwh.trim();
    }

    public String getSqys() {
        return sqys;
    }

    public void setSqys(String sqys) {
        this.sqys = sqys == null ? null : sqys.trim();
    }

    public Date getSqrq() {
        return sqrq;
    }

    public void setSqrq(Date sqrq) {
        this.sqrq = sqrq;
    }

    public String getYbbm() {
        return ybbm;
    }

    public void setYbbm(String ybbm) {
        this.ybbm = ybbm == null ? null : ybbm.trim();
    }

    public Date getCyrq() {
        return cyrq;
    }

    public void setCyrq(Date cyrq) {
        this.cyrq = cyrq;
    }

    public String getLczd() {
        return lczd;
    }

    public void setLczd(String lczd) {
        this.lczd = lczd == null ? null : lczd.trim();
    }

    public String getJyxm() {
        return jyxm;
    }

    public void setJyxm(String jyxm) {
        this.jyxm = jyxm == null ? null : jyxm.trim();
    }

    public String getBbbh() {
        return bbbh;
    }

    public void setBbbh(String bbbh) {
        this.bbbh = bbbh == null ? null : bbbh.trim();
    }

    public String getCzy() {
        return czy;
    }

    public void setCzy(String czy) {
        this.czy = czy == null ? null : czy.trim();
    }

    public Date getDjrq() {
        return djrq;
    }

    public void setDjrq(Date djrq) {
        this.djrq = djrq;
    }

    public String getZfbz() {
        return zfbz;
    }

    public void setZfbz(String zfbz) {
        this.zfbz = zfbz == null ? null : zfbz.trim();
    }

    public String getZfry() {
        return zfry;
    }

    public void setZfry(String zfry) {
        this.zfry = zfry == null ? null : zfry.trim();
    }

    public Date getZfrq() {
        return zfrq;
    }

    public void setZfrq(Date zfrq) {
        this.zfrq = zfrq;
    }

    public String getJyff() {
        return jyff;
    }

    public void setJyff(String jyff) {
        this.jyff = jyff == null ? null : jyff.trim();
    }

    public String getZxbz() {
        return zxbz;
    }

    public void setZxbz(String zxbz) {
        this.zxbz = zxbz == null ? null : zxbz.trim();
    }

    public String getZxys() {
        return zxys;
    }

    public void setZxys(String zxys) {
        this.zxys = zxys == null ? null : zxys.trim();
    }

    public String getZxsb() {
        return zxsb;
    }

    public void setZxsb(String zxsb) {
        this.zxsb = zxsb == null ? null : zxsb.trim();
    }

    public String getZxks() {
        return zxks;
    }

    public void setZxks(String zxks) {
        this.zxks = zxks == null ? null : zxks.trim();
    }

    public Date getZxrq() {
        return zxrq;
    }

    public void setZxrq(Date zxrq) {
        this.zxrq = zxrq;
    }

    public String getShbz() {
        return shbz;
    }

    public void setShbz(String shbz) {
        this.shbz = shbz == null ? null : shbz.trim();
    }

    public String getShry() {
        return shry;
    }

    public void setShry(String shry) {
        this.shry = shry == null ? null : shry.trim();
    }

    public Date getShrq() {
        return shrq;
    }

    public void setShrq(Date shrq) {
        this.shrq = shrq;
    }

    public String getSqdh() {
        return sqdh;
    }

    public void setSqdh(String sqdh) {
        this.sqdh = sqdh == null ? null : sqdh.trim();
    }

    public BigDecimal getSqdxh() {
        return sqdxh;
    }

    public void setSqdxh(BigDecimal sqdxh) {
        this.sqdxh = sqdxh;
    }

    public BigDecimal getFyje() {
        return fyje;
    }

    public void setFyje(BigDecimal fyje) {
        this.fyje = fyje;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz == null ? null : bz.trim();
    }

    public Short getNl() {
        return nl;
    }

    public void setNl(Short nl) {
        this.nl = nl;
    }

    public String getNldw() {
        return nldw;
    }

    public void setNldw(String nldw) {
        this.nldw = nldw == null ? null : nldw.trim();
    }

    public String getIsprint() {
        return isprint;
    }

    public void setIsprint(String isprint) {
        this.isprint = isprint == null ? null : isprint.trim();
    }

    public String getYblx() {
        return yblx;
    }

    public void setYblx(String yblx) {
        this.yblx = yblx == null ? null : yblx.trim();
    }

    public String getZklx() {
        return zklx;
    }

    public void setZklx(String zklx) {
        this.zklx = zklx == null ? null : zklx.trim();
    }

    public String getLy() {
        return ly;
    }

    public void setLy(String ly) {
        this.ly = ly == null ? null : ly.trim();
    }

    public String getKfbz() {
        return kfbz;
    }

    public void setKfbz(String kfbz) {
        this.kfbz = kfbz == null ? null : kfbz.trim();
    }

    public String getFyjlid() {
        return fyjlid;
    }

    public void setFyjlid(String fyjlid) {
        this.fyjlid = fyjlid == null ? null : fyjlid.trim();
    }

    public String getTjdw() {
        return tjdw;
    }

    public void setTjdw(String tjdw) {
        this.tjdw = tjdw == null ? null : tjdw.trim();
    }

    public String getJyxhHb() {
        return jyxhHb;
    }

    public void setJyxhHb(String jyxhHb) {
        this.jyxhHb = jyxhHb == null ? null : jyxhHb.trim();
    }

    public String getBrdm() {
        return brdm;
    }

    public void setBrdm(String brdm) {
        this.brdm = brdm == null ? null : brdm.trim();
    }

    public String getCodePrint() {
        return codePrint;
    }

    public void setCodePrint(String codePrint) {
        this.codePrint = codePrint == null ? null : codePrint.trim();
    }

    public String getFymc() {
        return fymc;
    }

    public void setFymc(String fymc) {
        this.fymc = fymc == null ? null : fymc.trim();
    }

    public Date getYbhsrq() {
        return ybhsrq;
    }

    public void setYbhsrq(Date ybhsrq) {
        this.ybhsrq = ybhsrq;
    }

    public String getYbhsr() {
        return ybhsr;
    }

    public void setYbhsr(String ybhsr) {
        this.ybhsr = ybhsr == null ? null : ybhsr.trim();
    }

    public String getYbhssm() {
        return ybhssm;
    }

    public void setYbhssm(String ybhssm) {
        this.ybhssm = ybhssm == null ? null : ybhssm.trim();
    }

    public String getFfbz() {
        return ffbz;
    }

    public void setFfbz(String ffbz) {
        this.ffbz = ffbz == null ? null : ffbz.trim();
    }

    public String getWswlx() {
        return wswlx;
    }

    public void setWswlx(String wswlx) {
        this.wswlx = wswlx == null ? null : wswlx.trim();
    }

    public String getYbhsbz() {
        return ybhsbz;
    }

    public void setYbhsbz(String ybhsbz) {
        this.ybhsbz = ybhsbz == null ? null : ybhsbz.trim();
    }

    public String getSqdPrint() {
        return sqdPrint;
    }

    public void setSqdPrint(String sqdPrint) {
        this.sqdPrint = sqdPrint == null ? null : sqdPrint.trim();
    }

    public String getCybz() {
        return cybz;
    }

    public void setCybz(String cybz) {
        this.cybz = cybz == null ? null : cybz.trim();
    }

    public String getYsckbz() {
        return ysckbz;
    }

    public void setYsckbz(String ysckbz) {
        this.ysckbz = ysckbz == null ? null : ysckbz.trim();
    }

    public String getYbhHb() {
        return ybhHb;
    }

    public void setYbhHb(String ybhHb) {
        this.ybhHb = ybhHb == null ? null : ybhHb.trim();
    }

    public String getZzdybz() {
        return zzdybz;
    }

    public void setZzdybz(String zzdybz) {
        this.zzdybz = zzdybz == null ? null : zzdybz.trim();
    }

    public String getJscl() {
        return jscl;
    }

    public void setJscl(String jscl) {
        this.jscl = jscl == null ? null : jscl.trim();
    }

    public String getYbjsbz() {
        return ybjsbz;
    }

    public void setYbjsbz(String ybjsbz) {
        this.ybjsbz = ybjsbz == null ? null : ybjsbz.trim();
    }

    public String getYbjsr() {
        return ybjsr;
    }

    public void setYbjsr(String ybjsr) {
        this.ybjsr = ybjsr == null ? null : ybjsr.trim();
    }

    public Date getYbjsrq() {
        return ybjsrq;
    }

    public void setYbjsrq(Date ybjsrq) {
        this.ybjsrq = ybjsrq;
    }

    public String getPsbz() {
        return psbz;
    }

    public void setPsbz(String psbz) {
        this.psbz = psbz == null ? null : psbz.trim();
    }

    public String getPsry() {
        return psry;
    }

    public void setPsry(String psry) {
        this.psry = psry == null ? null : psry.trim();
    }

    public Date getPssj() {
        return pssj;
    }

    public void setPssj(Date pssj) {
        this.pssj = pssj;
    }

    public Date getSjsj() {
        return sjsj;
    }

    public void setSjsj(Date sjsj) {
        this.sjsj = sjsj;
    }

    public String getCyczy() {
        return cyczy;
    }

    public void setCyczy(String cyczy) {
        this.cyczy = cyczy == null ? null : cyczy.trim();
    }

    public String getZlbm() {
        return zlbm;
    }

    public void setZlbm(String zlbm) {
        this.zlbm = zlbm == null ? null : zlbm.trim();
    }

    public String getYbjssm() {
        return ybjssm;
    }

    public void setYbjssm(String ybjssm) {
        this.ybjssm = ybjssm == null ? null : ybjssm.trim();
    }

    public String getBzsm() {
        return bzsm;
    }

    public void setBzsm(String bzsm) {
        this.bzsm = bzsm == null ? null : bzsm.trim();
    }

    public String getClbm() {
        return clbm;
    }

    public void setClbm(String clbm) {
        this.clbm = clbm == null ? null : clbm.trim();
    }

	public List<Lis_jydj_mxModel> getJydjmxList() {
		return jydjmxList;
	}

	public void setJydjmxList(List<Lis_jydj_mxModel> jydjmxList) {
		this.jydjmxList = jydjmxList;
	}

	public String getKsmc() {
		return ksmc;
	}

	public void setKsmc(String ksmc) {
		this.ksmc = ksmc;
	}

	public String getJyxmmc() {
		return jyxmmc;
	}

	public void setJyxmmc(String jyxmmc) {
		this.jyxmmc = jyxmmc;
	}

	public String getCzyxm() {
		return czyxm;
	}

	public void setCzyxm(String czyxm) {
		this.czyxm = czyxm;
	}

	public String getZfryxm() {
		return zfryxm;
	}

	public void setZfryxm(String zfryxm) {
		this.zfryxm = zfryxm;
	}

	public String getZxysxm() {
		return zxysxm;
	}

	public void setZxysxm(String zxysxm) {
		this.zxysxm = zxysxm;
	}

	public String getZxksmc() {
		return zxksmc;
	}

	public void setZxksmc(String zxksmc) {
		this.zxksmc = zxksmc;
	}

	public String getShryxm() {
		return shryxm;
	}

	public void setShryxm(String shryxm) {
		this.shryxm = shryxm;
	}

	public String getYbhsrxm() {
		return ybhsrxm;
	}

	public void setYbhsrxm(String ybhsrxm) {
		this.ybhsrxm = ybhsrxm;
	}

	public String getCyczyxm() {
		return cyczyxm;
	}

	public void setCyczyxm(String cyczyxm) {
		this.cyczyxm = cyczyxm;
	}

	public String getYbmc() {
		return ybmc;
	}

	public void setYbmc(String ybmc) {
		this.ybmc = ybmc;
	}

	public String getJyffmc() {
		return jyffmc;
	}

	public void setJyffmc(String jyffmc) {
		this.jyffmc = jyffmc;
	}

	public String getSqysxm() {
		return sqysxm;
	}

	public void setSqysxm(String sqysxm) {
		this.sqysxm = sqysxm;
	}

}
