package com.supx.csp.sbgl.kcgl.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.comm.pojo.CsqxInfoModel;
import com.supx.comm.pojo.UserInfoModel;
import com.supx.comm.util.CommonUtil;
import com.supx.comm.util.JsonUtil;
import com.supx.csp.api.pubfun.pojo.getxh.GetXhidMsgModel;
import com.supx.csp.api.pubfun.pojo.getxh.GetXhidResModel;
import com.supx.csp.api.pubfun.pojo.ypkc.YfkcclMsgModel;
import com.supx.csp.api.pubfun.service.IPubFunCspService;
import com.supx.csp.api.pubfun.service.IYpFunCspService;
import com.supx.csp.api.sbgl.kcgl.pojo.Sbfb_ckPrintModel;
import com.supx.csp.api.sbgl.kcgl.pojo.Sbfb_ckdModel;
import com.supx.csp.api.sbgl.kcgl.pojo.Sbfb_ckdmxModel;
import com.supx.csp.api.sbgl.kcgl.service.ISbfKcglCkglCspService;
import com.supx.csp.sbgl.kcgl.dao.Sbfb_ckdModelMapper;
import com.supx.csp.sbgl.kcgl.dao.Sbfb_ckdmxModelMapper;
import com.supx.csp.user.service.ServiceInvocationHelper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@DubboService
public class SbglKcglCkglCspServiceImpl extends ServiceInvocationHelper implements ISbfKcglCkglCspService {

	private final static Logger logger = LoggerFactory.getLogger(SbglKcglCkglCspServiceImpl.class);
	@Autowired
	private Sbfb_ckdModelMapper yfb_ckdModelMapper;
	@Autowired
	private Sbfb_ckdmxModelMapper yfb_ckdmxModelMapper;
	@Autowired
	private IPubFunCspService pubFunCspService;
	@Autowired
	private IYpFunCspService ypFunCspService;

	// 查询
	@Override
	public UtilResponse queryCkd(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Sbfb_ckdModel bean = (Sbfb_ckdModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());
			List<Sbfb_ckdModel> list = yfb_ckdModelMapper.queryCkd(bean);
			PageInfo<Sbfb_ckdModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("单据查询失败:" + e.getCause().getMessage());
			logger.error("单据查询接口【YfbKcglCkglCspServiceImpl queryCkd】发生异常", e);
		}
		return result;
	}

	// 保存
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse SaveCkd(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Date ldDate = new Date();
			JSONObject JsonObj = (JSONObject) msg.getParam().get("obj");
			String cklx = (String) msg.getParam().get("cklx");
			String mess = (String) msg.getParam().get("mess");// 单据提示信息
			String xhlx = (String) msg.getParam().get("xhlx");
			if (JsonObj == null) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "保存对象参数无效");
				logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveCkd】" + mess + "对象参数无效");
				return result;
			}
			JSONObject ckdobj = JsonObj.getJSONObject("ckd");
			JSONArray ckmxobj = (JSONArray) JsonObj.getJSONArray("ckdmx");
			if (ckdobj == null || ckmxobj == null) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "信息无效");
				logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveCkd】" + mess + "信息无效");
				return result;
			}
			Sbfb_ckdModel ckd = (Sbfb_ckdModel) JsonUtil.JsonToClass(ckdobj, Sbfb_ckdModel.class);
			List<Sbfb_ckdmxModel> ckdmx = (List<Sbfb_ckdmxModel>) JsonUtil.JsonToArray(ckmxobj, Sbfb_ckdmxModel.class);
			if (ckd == null || ckdmx == null) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "信息无效");
				logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveCkd】" + mess + "信息无效");
				return result;
			}
			ckd.setCklx(cklx);
			String yfbm = ckd.getSbfbm();
			if (yfbm == null || yfbm.equals("")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单药房编码无效");
				logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveRkd】" + mess + "信息无效");
				return result;
			}
			if (ckd.getCklx() == null || ckd.getCklx().equals("")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单药房编码无效");
				logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveCkd】" + mess + "信息无效");
				return result;
			}
			if (ckdmx == null || ckdmx.size() <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("没有可保存的" + mess + "单明细");
				logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveCkd】没有可保存的" + mess + "单明细");
				return result;
			}
			String strErr = "";
			for (int i = 0; i < ckdmx.size(); i++) {
				if (ckdmx.get(i).getScph() == null || ckdmx.get(i).getScph().equals("")) {
					strErr += "第 " + i + "行药品批号不能为空";
				}
				if (ckdmx.get(i).getYpbm() == null || ckdmx.get(i).getYpbm().equals("")) {
					strErr += "第 " + i + "行药品编码不能为空";
				}
				if (ckdmx.get(i).getScrq() == null) {
					strErr += "第 " + i + "行生产日期不能为空";
				}
				if (ckdmx.get(i).getYxqz() == null) {
					strErr += "第 " + i + "行有效期至不能为空";
				}
				if (ckdmx.get(i).getKfdw() == null || ckdmx.get(i).getKfdw().equals("")) {
					strErr += "第 " + i + "行库房单位不能为空";
				}
				if (ckdmx.get(i).getYfdw() == null || ckdmx.get(i).getYfdw().equals("")) {
					strErr += "第 " + i + "行药房单位不能为空";
				}
				if (ckdmx.get(i).getYpbm() == null || ckdmx.get(i).getYpbm().equals("")) {
					strErr += "第 " + i + "行药品编码不能为空";
				}
				if (ckdmx.get(i).getCksl() == null || ckdmx.get(i).getCksl() <= 0) {
					strErr += "第 " + i + "行出库数量不能小于零";
				}
			}
			if (!strErr.equals("")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "明细保存失败");
				logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveCkd】" + mess + "明细保存失败");
				return result;
			}
			int ref = 0;
			// 出库单为空新增
			if (ckd.getCkdh() == null || ckd.getCkdh().equals("")) {
				// 取出库单号
				UtilResponse resxh = null;
				GetXhidMsgModel beanrk = new GetXhidMsgModel();
				beanrk.setScfs("2");
				beanrk.setCslx(xhlx);
				beanrk.setYfbm(yfbm);
				msg.getParam().put("bean", beanrk);
				resxh = pubFunCspService.GetYfPzh(msg);
				if (!resxh.isSuccess()) {
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg(resxh.getResultMsg());
					logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveCkd】" + resxh.getResultMsg());
					return result;
				}
				GetXhidResModel cfhbean = (GetXhidResModel) resxh.getResResult().get("bean");
				String ckdh = cfhbean.getDqxh();
				ckd.setCkdh(ckdh);
				ckd.setZdr(ckd.getJbr());
				ckd.setZdrq(ldDate);
				ckd.setShzfbz("0");
				ckd.setYljgbm(msg.getYljgbm());
				String bzms=null;
				for (int i = 0; i < ckdmx.size(); i++) {
					ckdmx.get(i).setCkdh(ckdh);
					ckdmx.get(i).setMxxh(i + 1);
					ckdmx.get(i).setYljgbm(msg.getYljgbm());
					if(ckdmx.get(i).getBzms()!=null) bzms=ckdmx.get(i).getBzms();
				}
				if(bzms!=null) ckd.setBzms(bzms);
				ref = yfb_ckdModelMapper.insert(ckd);
				if (ref <= 0) {
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg(mess + "单保存失败");
					logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveRkd】" + mess + "单保存失败");
					throw new Exception();
				}
				ref = yfb_ckdmxModelMapper.insertCkmx(ckdmx);
				if (ref != -1) {
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg(mess + "单明细保存失败");
					logger.error("保存" + mess + "单接口【YfbYfywRkglCspServpceImpl SaveRkd】" + mess + "单保明细存失败");
					throw new Exception();
				}

			} else {// 有单号修改
				ckd.setYljgbm(msg.getYljgbm());
				ref = yfb_ckdModelMapper.update(ckd);
				if (ref <= 0) {
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg(mess + "单保存失败");
					logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveRkd】" + mess + "单保存失败");
					throw new Exception();
				}
				for (int i = 0; i < ckdmx.size(); i++) {
					ckdmx.get(i).setYljgbm(msg.getYljgbm());
				}
				ref = yfb_ckdmxModelMapper.updateCkmx(ckdmx);
				if (ref != -1) {
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg(mess + "单保存明细失败");
					logger.error("保存" + mess + "单接口【YfbKcglCkglCspServiceImpl SaveRkd】" + mess + "单明细保存失败");
					throw new Exception();
				}
			}
			result.getResResult().put("ref", "1");
			result.setResultMsg(mess + "保存成功");

		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("保存入库单失败:" + e.getCause().getMessage());
			logger.error("保存入库单接口【YfbKcglCkglCspServiceImpl SaveRkd】发生异常", e);
			throw new Exception();
		}
		return result;
	}

	// 审核
	@SuppressWarnings("unchecked")
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse pzsh(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "";
		try {
			Date ldDate = new Date();
			UserInfoModel userinfo = (UserInfoModel) msg.getParam().get("userinfo");
			Map<String, Object> map = (Map<String, Object>) msg.getParam().get("obj");
			String ckdh = (String) map.get("ckdh");
			String crlx = (String) msg.getParam().get("crlx");
			mess = (String) msg.getParam().get("mess");// 单据提示信息
			Sbfb_ckdModel bean = new Sbfb_ckdModel();
			bean.setCkdh(ckdh);
			bean.setYljgbm(msg.getYljgbm());
			Sbfb_ckdModel ckd = yfb_ckdModelMapper.queryCkdOne(bean);
			if (ckd == null) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单审核失败:单据号无效");
				logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzsh】单据号无效");
				return result;
			}
			String shbz = ckd.getShzfbz();
			if (shbz.equals("1")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单审核失败:此单据已核审!");
				logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzsh】单据已核审");
				return result;
			}
			if (shbz.equals("2")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单审核失败:此单据已作废!");
				logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzsh】单据已作废");
				return result;
			}
			ckd.setShzfbz("1");
			ckd.setShzfrq(ldDate);
			ckd.setShzfry(userinfo.getCzybm());
			ckd.setShzfryxm(userinfo.getCzyxm());

			// 库存处理入参
			YfkcclMsgModel kcmsg = new YfkcclMsgModel();
			kcmsg.setCzybm(userinfo.getCzybm());
			kcmsg.setDjh(ckdh);
			kcmsg.setCrlx(crlx);// 药房出科室
			kcmsg.setYfbm(ckd.getSbfbm());
			msg.getParam().put("bean", kcmsg);
			msg.getParam().put("ksbm", ckd.getLyks());
			UtilResponse res = ypFunCspService.YfkcclLock(msg);
			if (res.getResultCode().equals("2")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "审核失败:" + res.getResultMsg());
				logger.error("调用" + mess + "审核接口【YfbKcglCkglCspServiceImpl pzsh】发生异常", res.getResultMsg());
				return result;
			}
			if (res.getResultCode().equals("1")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "审核失败:" + res.getResultMsg());
				logger.error("调用" + mess + "审核接口【YfbKcglCkglCspServiceImpl pzsh】发生异常", res.getResultMsg());
				throw new Exception();
			}
			ckd.setYljgbm(msg.getYljgbm());
			int ref = yfb_ckdModelMapper.ckdshzf(ckd);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单审核失败.");
				logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzsh】单据审核异常");
				throw new Exception();
			}
			result.getResResult().put("ref", 1);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + "单审核失败:" + e.getCause().getMessage());
			logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzsh】发生异常", e);
			throw new Exception();
		}
		return result;
	}

	// 作废
	public UtilResponse pzzf(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "";
		try {
			Date ldDate = new Date();
			UserInfoModel userinfo = (UserInfoModel) msg.getParam().get("userinfo");
			Map<String, Object> map = (Map<String, Object>) msg.getParam().get("obj");
			String ckdh = (String) map.get("ckdh");
			String cklx = (String) msg.getParam().get("cklx");
			mess = (String) msg.getParam().get("mess");// 单据提示信息
			Sbfb_ckdModel bean = new Sbfb_ckdModel();
			bean.setCkdh(ckdh);
			bean.setYljgbm(msg.getYljgbm());
			Sbfb_ckdModel ckd = yfb_ckdModelMapper.queryCkdOne(bean);
			if (ckd == null) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单审核失败:单据号无效");
				logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzzf】单据号无效");
				return result;
			}
			String shbz = ckd.getShzfbz();
			if (shbz.equals("1")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单作废失败:此单据已核审!");
				logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzzf】单据已作废");
				return result;
			}
			if (shbz.equals("2")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单作废失败:此单据已作废!");
				logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzzf】单据已作废");
				return result;
			}
			ckd.setShzfbz("2");
			ckd.setShzfrq(ldDate);
			ckd.setShzfry(userinfo.getCzybm());
			ckd.setShzfryxm(userinfo.getCzyxm());
			ckd.setYljgbm(msg.getYljgbm());
			int ref = yfb_ckdModelMapper.ckdshzf(ckd);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "单作废失败.");
				logger.error(mess + "单审核接口【YfbKcglCkglCspServiceImpl pzzf】单据作废异常");
				throw new Exception();
			}
			result.getResResult().put("ref", 1);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + "单作废失败:" + e.getCause().getMessage());
			logger.error(mess + "单作废接口【YfbKcglCkglCspServiceImpl pzzf】发生异常", e);
			throw new Exception();
		}
		return result;
	}

	@Override
	public UtilResponse queryCkdmx(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Sbfb_ckdmxModel bean = (Sbfb_ckdmxModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<Sbfb_ckdmxModel> list = (List<Sbfb_ckdmxModel>) yfb_ckdmxModelMapper.queryCkmx(bean);
			if(list!=null&&list.size()>0){
				for(Sbfb_ckdmxModel ckmx:list){
					if(StringUtils.isEmpty(ckmx.getCxid())){
						String id = ckmx.getId();
						String ckdh = ckmx.getCkdh();
						Double ycxsl = yfb_ckdmxModelMapper.queryCxsl(ckdh,msg.getYljgbm(),id);
						ckmx.setYcxsl(-ycxsl);
					}
				}
			}
			result.getResResult().put("list", list);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("单据明细查询失败:" + e.getCause().getMessage());
			logger.error("单据明细查询接口【YfbKcglCkglCspServiceImpl queryCkdmx】发生异常", e);
		}
		return result;
	}

	// 出库打印
	@Override
	public UtilResponse ckPrint(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Sbfb_ckdModel bean = (Sbfb_ckdModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			// 获取权限参数
			int lines = 0;
			List<CsqxInfoModel> csqxinfo = (List<CsqxInfoModel>) msg.getParam().get("csqxinfo");
			for (CsqxInfoModel csqx : csqxinfo) {
				if ("***********".equals(csqx.getCsqxbm())) {
					lines = Integer.valueOf(csqx.getCsz());
				}
			}

			// 组装实体
			Sbfb_ckPrintModel ckp = new Sbfb_ckPrintModel();

			// 获取单据
			Sbfb_ckdModel dj = yfb_ckdModelMapper.queryCkdOne(bean);

			//设置行数
			dj.setRows(lines);

			// 单据明细
			Sbfb_ckdmxModel mxTem = new Sbfb_ckdmxModel();
			mxTem.setYljgbm(msg.getYljgbm());
			mxTem.setCkdh(bean.getCkdh());
			List<Sbfb_ckdmxModel> djmx = yfb_ckdmxModelMapper.queryCkmx(mxTem);

			// 计算金额
			Double jjjehz = 0.0;
			Double ljjehz = 0.0;
			for (int i = 0; i < djmx.size(); i++) {
				Double jjjeTem = CommonUtil.doubleRound(djmx.get(i).getCksl() , djmx.get(i).getSbjj());

				djmx.get(i).setYpjjje(jjjeTem);

				jjjehz += jjjeTem;
			}

			// 设置汇总
			dj.setJjjehz(jjjehz);
			dj.setLjjehz(ljjehz);

			ckp.setDj(dj);
			ckp.setDjmx(djmx);

			result.getResResult().put("ckp", ckp);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("单据打印失败:" + e.getCause().getMessage());
			logger.error("单据打印接口【YfbKcglCkglCspServiceImpl ckPrint】发生异常", e);
		}
		return result;
	}

	// 出库单明细统计
	@Override
	public UtilResponse ckdmxCx(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Sbfb_ckdmxModel bean = (Sbfb_ckdmxModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());
			List<Sbfb_ckdmxModel> list;
			if ("sum".equals(bean.getOrder())) {
				list = (List<Sbfb_ckdmxModel>) yfb_ckdmxModelMapper.ckdmxCxByyp(bean);
			} else {
				list = (List<Sbfb_ckdmxModel>) yfb_ckdmxModelMapper.ckdmxCx(bean);
			}
			// 统计总计金额
			List<Sbfb_ckdmxModel> tempLisl = yfb_ckdmxModelMapper.ckdmxCxByyp(bean);
			Double temJjzj = 0.0;
			Double temLjzj = 0.0;

			for (int i = 0; i < tempLisl.size(); i++) {
				temJjzj += tempLisl.get(i).getSbjj() * tempLisl.get(i).getCksl();
			}
			// 将总计金额保存在集合的第一个对象内
			if (list.size() > 0) {
				list.get(0).setYpjjje(temJjzj);
				list.get(0).setYpljje(temLjzj);
			}

			PageInfo<Sbfb_ckdmxModel> pageInfo = new PageInfo<Sbfb_ckdmxModel>(list);
			result.getResResult().put("list", pageInfo);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("单据明细查询失败:" + e.getCause().getMessage());
			logger.error("单据明细查询接口【YfbKcglCkglCspServiceImpl queryCkdmx】发生异常", e);
		}
		return result;
	}

}
