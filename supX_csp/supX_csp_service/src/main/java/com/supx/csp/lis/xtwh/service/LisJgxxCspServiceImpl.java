package com.supx.csp.lis.xtwh.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.csp.api.lis.xtwh.pojo.LisJgdyModel;
import com.supx.csp.api.lis.xtwh.pojo.LisTjdwModel;
import com.supx.csp.api.lis.xtwh.pojo.LisXzjgModel;
import com.supx.csp.api.lis.xtwh.service.ILisJgxxCspService;
import com.supx.csp.lis.xtwh.dao.LisJgdyModelMapper;
import com.supx.csp.lis.xtwh.dao.LisJgxzModelMapper;
import com.supx.csp.lis.xtwh.dao.LisTjdwModelMapper;
import com.supx.csp.user.service.ServiceInvocationHelper;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;

@Service
@DubboService
public class LisJgxxCspServiceImpl extends ServiceInvocationHelper implements ILisJgxxCspService {
	// 日志相关
	private final Logger logger = LoggerFactory.getLogger(LisJgxxCspServiceImpl.class);

	@Autowired
	private LisJgxzModelMapper jgxzMapper;
	@Autowired
	private LisJgdyModelMapper jgdyMapper;
	@Autowired
	private LisTjdwModelMapper jgtjMapper;

	@Override
	public UtilResponse selectJg(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			LisXzjgModel bean = (LisXzjgModel) msg.getParam().get("bean");
			// 插件分页
			PageHelper.startPage(bean.getPage(), bean.getRows());
			// 查询
			List<LisXzjgModel> list = jgxzMapper.selectXzjg(bean);
			// 封装
			PageInfo<LisXzjgModel> pageInfo = new PageInfo<LisXzjgModel>(list);
			// 将结果集放入返回结果中
			result.getResResult().put("pageInfo", pageInfo);
		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【 LisJgxxCspServiceImpl selectJg】发生异常", e);

		}
		return result;
	}

	@Override
	public UtilResponse saveJg(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			LisXzjgModel bean = (LisXzjgModel) msg.getParam().get("bean");
			// 对对象进行判断存在与否
			LisXzjgModel lisXzjgModel = jgxzMapper.selectXzjgByOne(bean);
			if (lisXzjgModel == null) {
				// 保存
				Integer ref = jgxzMapper.insertXzjg(bean);
				// 将结果集放入返回结果中
				result.getResResult().put("ref", ref.toString());
			} else {
				// 修改
				Integer ref = jgxzMapper.updateXzjg(bean);
				// 将结果集放入返回结果中
				result.getResResult().put("ref", ref.toString());
			}

		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【LisJgxxCspServiceImpl saveJg】发生异常", e);

		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public UtilResponse deleteJG(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			List<LisXzjgModel> bean = (List<LisXzjgModel>) msg.getParam().get("bean");
			// 创建map
			Map<String, Object> map = new HashMap<String, Object>();
			// 传入参数
			map.put("list", bean);
			map.put("yljgbm", bean.get(0).getYljgbm());
			// 删除
			Integer ref = jgxzMapper.deleteXzjg(map);
			// 将结果集放入返回结果中
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【LisJgxxCspServiceImpl deleteJG】发生异常", e);

		}
		return result;
	}

	@Override
	public UtilResponse selectJgdy(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			LisJgdyModel bean = (LisJgdyModel) msg.getParam().get("bean");
			// 查询
			List<LisJgdyModel> list = jgdyMapper.selectXzjgdy(bean);
			// 插件分页
			PageHelper.startPage(bean.getPage(), bean.getRows());
			// 封装
			PageInfo<LisJgdyModel> pageInfo = new PageInfo<LisJgdyModel>(list);
			// 将结果集放入返回结果中
			result.getResResult().put("pageInfo", pageInfo);
		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【 LisJgxxCspServiceImpl selectJgdy】发生异常", e);

		}
		return result;
	}

	@Override
	public UtilResponse saveJgdy(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			LisJgdyModel bean = (LisJgdyModel) msg.getParam().get("bean");
			// 对对象进行判断存在与否
			LisJgdyModel lisXzjgModel = jgdyMapper.selectXzjgdyByOne(bean);
			if (lisXzjgModel == null) {
				// 保存
				Integer ref = jgdyMapper.insertXzjgdy(bean);
				// 将结果集放入返回结果中
				result.getResResult().put("ref", ref.toString());
			} else {
				// 修改
				Integer ref = jgdyMapper.insertXzjgdy(bean);
				// 将结果集放入返回结果中
				result.getResResult().put("ref", ref.toString());
			}

		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【LisJgxxCspServiceImpl saveJgdy】发生异常", e);

		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public UtilResponse deleteJGdy(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			List<LisJgdyModel> bean = (List<LisJgdyModel>) msg.getParam().get("bean");
			// 创建map
			Map<String, Object> map = new HashMap<String, Object>();
			// 传入参数
			map.put("list", bean);
			map.put("yljgbm", bean.get(0).getYljgbm());
			// 删除
			Integer ref = jgdyMapper.deleteXzjgdy(map);
			// 将结果集放入返回结果中
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【LisJgxxCspServiceImpl deleteJGdy】发生异常", e);

		}
		return result;
	}

	@Override
	public UtilResponse selectTj(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			LisTjdwModel bean = (LisTjdwModel) msg.getParam().get("bean");
			// 查询
			List<LisTjdwModel> list = jgtjMapper.selectTj(bean);
			// 插件分页
			PageHelper.startPage(bean.getPage(), bean.getRows());
			// 封装
			PageInfo<LisTjdwModel> pageInfo = new PageInfo<LisTjdwModel>(list);
			// 将结果集放入返回结果中
			result.getResResult().put("pageInfo", pageInfo);
		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【 LisJgxxCspServiceImpl selectTj】发生异常", e);

		}
		return result;
	}

	@Override
	public UtilResponse saveTj(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			LisTjdwModel bean = (LisTjdwModel) msg.getParam().get("bean");
			// 对对象进行判断存在与否
			LisTjdwModel lisXzjgModel = jgtjMapper.selectTjByOne(bean);
			if (lisXzjgModel == null) {
				// 保存
				Integer ref = jgtjMapper.insertTj(bean);
				// 将结果集放入返回结果中
				result.getResResult().put("ref", ref.toString());
			} else {
				// 修改
				Integer ref = jgtjMapper.updateTj(bean);
				// 将结果集放入返回结果中
				result.getResResult().put("ref", ref.toString());
			}

		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【LisJgxxCspServiceImpl saveJgdy】发生异常", e);

		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public UtilResponse deleteTj(UtilRequest msg) {
		// 创建返回结果对象
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数从 msg中 固定写法
			List<LisTjdwModel> bean = (List<LisTjdwModel>) msg.getParam().get("bean");
			// 创建map
			Map<String, Object> map = new HashMap<String, Object>();
			// 传入参数
			map.put("list", bean);
			map.put("yljgbm", bean.get(0).getYljgbm());
			// 删除
			Integer ref = jgtjMapper.deleteTj(map);
			// 将结果集放入返回结果中
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			// 固定写法
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			// 固定写法
			result.setResultMsg("lis系统检验项目维护接口:" + e.getMessage());
			// 固定写法
			logger.error("lis系统检验项目维护保存接口【LisJgxxCspServiceImpl deleteTj】发生异常", e);

		}
		return result;
	}

	/**
	 * 批量更新
	 */
	@SuppressWarnings("unchecked")
	public UtilResponse updateBatch(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			List<LisXzjgModel> list = (List<LisXzjgModel>) msg.getParam().get("list");
			for(LisXzjgModel xzjg : list){
				xzjg.setYljgbm(msg.getYljgbm());
			}
			Integer ref = jgxzMapper.updateBatch(list);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("结果选项批量更新接口:" + e.getMessage());
			logger.error("结果选项批量更新接口【LisJgxxCspServiceImpl updateBatch】发生异常", e);

		}
		return result;
	}



}
