package com.supx.csp.mzys.zlgl.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.comm.pojo.CsqxInfoModel;
import com.supx.comm.util.ConfigurationRead;
import com.supx.csp.TimedTask.dao.GhinfoDataMapper;
import com.supx.csp.api.TimedTask.pojo.MedicalJchrModel;
import com.supx.csp.api.pubfun.pojo.getxh.GetXhidMsgModel;
import com.supx.csp.api.pubfun.pojo.getxh.GetXhidResModel;
import com.supx.csp.api.pubfun.service.IPubFunCspService;
import com.supx.csp.api.ghgl.ghyw.pojo.Ghb_BrghSaveModel;
import com.supx.csp.api.ghgl.ghyw.pojo.Ghb_brghModel;
import com.supx.csp.api.ghgl.ghyw.pojo.Gyb_brjbxxModel;
import com.supx.csp.api.ghgl.ghyw.service.IGhb_brghCspService;
import com.supx.csp.api.ghgl.ghyw.service.IGyb_brjbxxCspService;
import com.supx.csp.api.mjzhl.rcyw.pojo.Mjzgl_YzdModel;
import com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel;
import com.supx.csp.api.mzys.cxtj.pojo.CfPrintModel;
import com.supx.csp.api.mzys.zlgl.pojo.MzysSytPrintModel;
import com.supx.csp.api.mzys.zlgl.pojo.MzysZlglBrjzYpzlfyModel;
import com.supx.csp.api.mzys.zlgl.pojo.jcjyPrintModel;
import com.supx.csp.api.mzys.zlgl.service.IMzysZlglBrjzCspService;
import com.supx.csp.api.xtwh.ksry.pojo.Gyb_RybmModel;
import com.supx.csp.api.xtwh.ksry.pojo.Gyb_ksbmModel;
import com.supx.csp.api.xtwh.ylfwxm.pojo.Gyb_hbsfxmModel;
import com.supx.csp.api.xtwh.ylfwxm.pojo.Yfb_yfModel;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel;
import com.supx.csp.api.yfgl.yfyw.pojo.Yfb_yppfModel;
import com.supx.csp.api.zygl.crygl.pojo.Jzk_fzdjModel;
import com.supx.csp.user.service.ServiceInvocationHelper;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.comm.pojo.UserInfoModel;
import com.supx.csp.ghgl.ghyw.dao.Ghb_brghModelMapper;
import com.supx.csp.ghgl.ghyw.dao.Gyb_brjbxxModelMapper;
import com.supx.csp.ghgl.ghyw.service.Ghb_brghCspServiceImpl;
import com.supx.csp.mzsf.sfjs.dao.New1Mzb_brfyModelMapper;
import com.supx.csp.mzys.zlgl.dao.New1MzysZlglBrjzModelMapper;
import com.supx.csp.xtwh.ksry.dao.Gyb_RybmModelMapper;
import com.supx.csp.xtwh.ylfwxm.dao.Gyb_hbsfxmModelMapper;
import com.supx.csp.yfgl.yfyw.dao.New1Yfb_ypcfModelMapper;
import com.supx.csp.yfgl.yfyw.dao.New1Yfb_yppfModelMapper;
import com.supx.comm.util.JsonUtil;
import com.wondersgroup.common.decrypt.utils.Sm4HexDe;
import com.wondersgroup.common.endecrypt.utils.Sm4HexEn;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.*;


//门诊医生，诊疗管理，病人接诊实现类
@Service
@DubboService
public class MzysZlglBrjzCspServiceImpl extends ServiceInvocationHelper implements IMzysZlglBrjzCspService{
	ConfigurationRead read;
	//日志
	private final Logger logger = LoggerFactory.getLogger(Ghb_brghCspServiceImpl.class);

	//门诊处方业务表
	@Autowired
	private New1Yfb_ypcfModelMapper yfb_ypcfModelMapper ;
	@Autowired
	private New1Yfb_yppfModelMapper yfb_yppfModelMapper;

	//private YfbYfywCftyzfModelMapper yfbYfywCftyzfModelMapper;

	//门诊医生业务处理
	@Autowired
	private New1MzysZlglBrjzModelMapper mzysZlglBrjzModelMapper;

	//病人费用
	@Autowired
	private New1Mzb_brfyModelMapper mzb_brfyModelMapper;
	//病人基本信息
	@Autowired
	private IGyb_brjbxxCspService gyb_brjbxxCspService;
	//病人挂号
	@Autowired
	private IGhb_brghCspService ghb_brghCspService;
	@Autowired
	private Ghb_brghModelMapper ghb_brghModelMapper;

	//生成编码
	@Autowired
	private IPubFunCspService pubFunCspService;

	@Autowired
    Gyb_brjbxxModelMapper gyb_brjbxxModelMapper;
	@Autowired
	private Gyb_RybmModelMapper gyb_RybmModelMapper;
	@Autowired
	private Gyb_hbsfxmModelMapper gyb_hbsfxmModelMapper;
	@Autowired
	private GhinfoDataMapper ghinfoDataMapper;
	/**
	 * 电子处方保存
	 */
	@Transactional(rollbackFor=Exception.class)
	@Override
	public UtilResponse Save_Mzcf( UtilRequest msg) throws Exception {

		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			List<Yfb_yppfModel> yppf_List = (List<Yfb_yppfModel>)msg.getParam().get("yppf");
			Yfb_ypcfModel ypcf = (Yfb_ypcfModel)msg.getParam1().get("ypcf");
			JSONArray obj = JSON.parseArray(msg.getParam().get("obj").toString());
			List<Mzb_brfyModel> mzfy_List = (List<Mzb_brfyModel>)msg.getParam2().get("mzfy");
			String ghxh=(String) msg.getParam().get("ghxh");
			UserInfoModel userInfo = (UserInfoModel)msg.getUserinfo().get("user");

			//已扣费不能进行修改
			Yfb_ypcfModel cfBean = new Yfb_ypcfModel();
			cfBean.setCfh(ypcf.getCfh());
			cfBean.setYljgbm(msg.getYljgbm());
			Yfb_ypcfModel cfObj = yfb_ypcfModelMapper.querycf(cfBean);

			if(cfObj != null && "1".equals(cfObj.getKfbz())){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("处方已扣费，不能修改！");
				logger.error("处方已扣费，不能修改！！【MzysZlglBrjzCspServiceImpl Save_Mzcf】") ;
				return result;
			}

			//根据挂号序号查询挂号信息
			Ghb_brghModel gh=new Ghb_brghModel();
			gh.setYljgbm(msg.getYljgbm());
			gh.setGhxh(ghxh);
			Ghb_brghModel ghbean=ghb_brghModelMapper.queryGhb_brghOne(gh);
			//获取处方号
			String cfh=null;
			if(ypcf.getCfh()==null||ypcf.getCfh().equals("")){
				GetXhidMsgModel bean = new GetXhidMsgModel();
				bean.setScfs("3");
				bean.setCslx("CFH");
				String cflxmc = ypcf.getCflxmc();
				if(obj!=null && obj.size()>0){
					if ("1".equals(obj.getJSONObject(0).get("jmcf")==null?null:obj.getJSONObject(0).get("jmcf").toString()) &&
							(ypcf.getCflxmc()==null?false:ypcf.getCflxmc().contains("精") || ypcf.getCflxmc()==null?false:ypcf.getCflxmc().contains("毒")|| ypcf.getCflxmc()==null?false:ypcf.getCflxmc().contains("麻"))){//等于1表示
						bean.setJmcf("1");
					}
				}
				msg.getParam().put("bean", bean);
				UtilResponse res_cfh = pubFunCspService.GetIdLock(msg);
				GetXhidResModel brbean = (GetXhidResModel)res_cfh.getResResult().get("bean");
				cfh = brbean.getDqxh();
				ypcf.setCfh(cfh);
			}

			//********************  处理更新  **********************
			Integer yppfRef = 0 ,ypcfRef = 0 , mzfyRef = 0 ;

			if(yppf_List == null || yppf_List.size() <= 0){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：配方信息为空！");
				logger.error("数据提交失败：配方信息为空！【MzysZlglBrjzCspServiceImpl Save_Mzcf】") ;
				throw new Exception();
			}


			if(mzfy_List == null || mzfy_List.size() <= 0){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：费用明细信息为空！");
				logger.error("数据提交失败：费用明细信息为空！【MzysZlglBrjzCspServiceImpl Save_Mzcf】") ;
				throw new Exception();
			}

			//处理医疗机构编码
			for (Mzb_brfyModel mzfy : mzfy_List) {
				mzfy.setYljgbm(msg.getYljgbm());
				mzfy.setYzhm(ypcf.getCfh());
				Mzb_brfyModel brfy=new Mzb_brfyModel();
				brfy.setYzhm(ypcf.getCfh());
				brfy.setYljgbm(msg.getYljgbm());
				List<Mzb_brfyModel> models=mzb_brfyModelMapper.queryYzfy(brfy);
				if(models.size()>0)
					brfy.setFyjlid(models.get(0).getFyjlid());
			}
			ypcf.setYljgbm(msg.getYljgbm());
			for (Yfb_yppfModel yppf : yppf_List) {
				yppf.setYljgbm(msg.getYljgbm());
				yppf.setCfh(ypcf.getCfh());
			}
			//说明这是执行更新操作（处方号已经存在）
			if(cfh==null){
				//1、更新费用表
				mzfyRef = mzb_brfyModelMapper.update(mzfy_List) ;
				//处理不通费用类别需要删除费用记录的操作（类似西药，中成药一起开，删中成药）
				/*List<Mzb_brfyModel> oldfy=mzb_brfyModelMapper.queryByWsf(mzfy_List.get(0));
				if(oldfy.size()>mzfy_List.size()){
					List<Mzb_brfyModel> deletelist=new ArrayList<>();
					for(Mzb_brfyModel old:oldfy){
						boolean czfy=true;
						for(Mzb_brfyModel now:mzfy_List){
							if(now.getYzhm().equals(old.getYzhm())){
								if(now.getFylb().equals(old.getFylb())){
									continue;
								}else{
									czfy=false;
									break;
								}
							}
						}
						if(!czfy){
							deletelist.add(old);
						}
					}
					if(deletelist!=null&&deletelist.size()>0){
						mzb_brfyModelMapper.delete(deletelist);
					}
				}*/
				//2、更新处方主表
				ypcfRef = yfb_ypcfModelMapper.updatecf(ypcf) ;
				//3、更新配方明细表
				yppfRef = yfb_yppfModelMapper.updatepf(yppf_List) ;
			}else{
				//1、添加费用表
				mzfyRef = mzb_brfyModelMapper.insert(mzfy_List) ;
				//2、添加处方主表
				ypcfRef = yfb_ypcfModelMapper.insertcf(ypcf) ;
				//3、添加配方明细表
				yppfRef = yfb_yppfModelMapper.insertpf(yppf_List) ;

			}
			//不管是新增还是修改，都需要调用金额校准的存储过程，对保存的金额进行核对
			String p_msg = ""; //出参  存储过程修改完数据，返回的信息
			int p_rtn = 0; //出参 修改返回的结果，如果为0 则没有修改数据，如果为1 则表示修改颗数据
//			yfb_ypcfModelMapper.callUspYfbYpcfGxfy(ypcf.getCfh(),p_rtn,p_msg);


			List<Map<String,Object>> list = yfb_ypcfModelMapper.getCfFyqk(ypcf.getCfh());

			if(list != null && list.size()>0){

				Double zzfy = 0.0;

				for (int i = 0; i < list.size(); i++) {
					yfb_ypcfModelMapper.zzupdateBrfy(ypcf.getCfh(),ypcf.getBah(),Double.parseDouble(list.get(i).get("FYDJ").toString()),list.get(i).get("LBBM").toString());
					zzfy = JsonUtil.CommonUtil.doubleNotAdd(zzfy,Double.parseDouble(list.get(i).get("FYDJ").toString()));
				}
				yfb_ypcfModelMapper.zzupdateBrcf(ypcf.getCfh(),ypcf.getBah(),zzfy);

			}




			if (mzfyRef != -1){  //判断更新行数
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：费用明细保存失败！提交行数：" + mzfyRef );
				logger.error("数据提交失败：费用明细保存失败【MzysZlglBrjzCspServiceImpl Save_Mzcf】 提交行数：" + mzfyRef ) ;
				throw new Exception();  //异常自动返回  并且SQL事务回滚
			}
			if (ypcfRef <= 0){  //判断更新行数
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：处方主表保存失败！提交行数：" + ypcfRef );
				logger.error("数据提交失败：处方主表保存失败【MzysZlglBrjzCspServiceImpl Save_Mzcf】 提交行数：" + ypcfRef ) ;
				throw new Exception();  //异常自动返回  并且SQL事务回滚
			}
			if (yppfRef != -1){  //判断更新行数
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：配方明细表保存失败！提交行数：" + yppfRef );
				logger.error("数据提交失败：配方明细表保存失败【MzysZlglBrjzCspServiceImpl Save_Mzcf】 提交行数：" + yppfRef ) ;
				throw new Exception();  //异常自动返回  并且SQL事务回滚
			}

			//当挂号表中疾病信息没有时保存药品处方诊断
			if(ghbean.getJbmc()==null){
				ghbean.setJbmc(ypcf.getLczd());
				int i=ghb_brghModelMapper.update(ghbean);
				if (i <= 0){  //判断更新行数
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("数据提交失败：挂号接诊信息修改失败！提交行数：" + i );
					logger.error("数据提交失败：挂号接诊信息修改失败【MzysZlglBrjzCspServiceImpl Save_Mzcf】 提交行数：" + i ) ;
					throw new Exception();  //异常自动返回  并且SQL事务回滚
				}
			}

			//毒麻处方时，更新病人基本信息(实名制信息)
			String ckz = JsonUtil.Utilpubfun.getCsqx((List<CsqxInfoModel>) msg.getCsqxinfo().get("csqxinfo"),"N03001200129",ypcf.getBrks());
			if("1".equals(ckz)){
				Gyb_brjbxxModel brjbxxModel = (Gyb_brjbxxModel)msg.getParam().get("brjbxx");
				if(("03".equals(ypcf.getCflx()) || "1".equals(ypcf.getDmcfbz())) && brjbxxModel != null){
					brjbxxModel.setYljgbm(msg.getYljgbm());
					int i = gyb_brjbxxModelMapper.update(brjbxxModel);
					if (i <= 0){  //判断更新行数
						result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
						result.setResultMsg("数据提交失败：基本信息修改失败！提交行数：" + i );
						logger.error("数据提交失败：基本信息修改失败【MzysZlglBrjzCspServiceImpl Save_Mzcf】 提交行数：" + i ) ;
						throw new Exception();  //异常自动返回  并且SQL事务回滚
					}
				}
			}

			//改变病人接诊状态
			if(ghbean.getJzbz().equals("0")){
				ghbean.setJzbz("1");
				ghbean.setJzsj(new Date());
				ghbean.setJzys(userInfo.getCzybm());
				ghbean.setJzysxm(userInfo.getCzyxm());
				ghbean.setGhks(ypcf.getBrks());
				ghbean.setGhksmc(ypcf.getBrksmc());
				if(ghbean.getJbmc()==null){
					ghbean.setJbmc(ypcf.getLczd());
				}
				int i=ghb_brghModelMapper.update(ghbean);
				if (i <= 0){  //判断更新行数
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("数据提交失败：挂号接诊信息修改失败！提交行数：" + i );
					logger.error("数据提交失败：挂号接诊信息修改失败【MzysZlglBrjzCspServiceImpl Save_Mzcf】 提交行数：" + i ) ;
					throw new Exception();  //异常自动返回  并且SQL事务回滚
				}
			}

			//返回成功标志
			result.setResultMsg("处方信息保存成功！");
			result.getResResult().put("ref", 1 );
			result.getResResult().put("cfh", cfh );
			//上传病历用
			result.getResResult().put("emr_yppf", yppf_List );
			result.getResResult().put("emr_ypcf",  ypcf);
			//生成微信支付码
			result.getResResult().put("brfyList", mzfy_List);

		}catch (Exception ex){
			ex.printStackTrace();
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("门诊处方保存失败:");
			logger.error("调用门诊处方保存接口【MzysZlglBrjzCspServiceImpl Seva_Mzcf】发生异常") ;
			throw new Exception();
		}
		return result;
	}

	@Override
	public UtilResponse selectHrxx(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			JSONArray obj = JSON.parseArray(msg.getParam().get("obj").toString());
			JSONObject objGhxx = obj.getJSONObject(0).getJSONObject("Ghxx");
			JSONArray objMzfy = obj.getJSONObject(0).getJSONArray("Mzfy");
			UserInfoModel userInfo = (UserInfoModel)msg.getUserinfo().get("user");
			MedicalJchrModel jchr = new MedicalJchrModel();
			jchr.setZjhm(objGhxx.getString("sfzjhm"));
			//jchr.setZjhm("519004195006030021");
			MedicalJchrModel jchrModel = ghinfoDataMapper.findMedicalJchr(jchr);
			//调用提醒接口
			if(null != jchrModel && !jchrModel.getUrl().isEmpty()){
				int hzlxdm = 2;
				int ghzl = objGhxx.getIntValue("ghzl");
				if(ghzl != 2){
					hzlxdm = 1;
				}
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Long jzsj = objGhxx.getLongValue("jzsj");
				Date date = new Date(jzsj);
				String jzsjFormat = sdf.format(date);
				// 定义分类的 Set，提高判断效率
				Set<String> category1 = new HashSet<>(Arrays.asList("210", "242"));
				Set<String> category2 = new HashSet<>(Arrays.asList("204", "205", "206", "207", "208", "209","224", "225", "236", "237", "238"));
				JSONArray jsRry = new JSONArray();
				for (int i = 0; i < objMzfy.size(); i++) {
					JSONObject objFy = objMzfy.getJSONObject(i);
					String fylb = objFy.getString("fylb"); // 只获取一次 fylb
					Integer xmlx = null;
					if (category1.contains(fylb)) {
						xmlx = 1;
					} else if (category2.contains(fylb)) {
						xmlx = 2;
					}
					// 仅在符合条件时创建 JSONObject
					if (xmlx != null) {
						JSONObject jsres = new JSONObject();
						jsres.put("xmlx", xmlx);
						String xmdm = null;
						String zhfybm = objFy.getString("zhfybm");
						String fyxmbm = objFy.getString("mxfyxmbm");
						if(1 == xmlx && null != fyxmbm){//检验
							xmdm = ghinfoDataMapper.findCfkdHrdmJy(fyxmbm);
							jsres.put("xmdm", xmdm);
						}else if(2 == xmlx && null != zhfybm){
							xmdm = ghinfoDataMapper.findCfkdHrdmJc(zhfybm);
							jsres.put("xmdm", xmdm);
						}
						if(null == xmdm){
							jsres.put("xmdm", objFy.getString("mxfyxmbm"));
						}
						jsRry.add(jsres);
					}
				}
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("yljgdm","450717359");
				jsonObject.put("yljgmc",userInfo.getYljgmc());
				jsonObject.put("jzksdm",objGhxx.getString("ghks"));
				jsonObject.put("jzksmc",objGhxx.getString("ghksmc"));
				jsonObject.put("jzlsh",objGhxx.getString("ghxh"));
				jsonObject.put("jzysdm",objGhxx.getString("jzys"));
				jsonObject.put("jzysmc",objGhxx.getString("jzysxm"));
				jsonObject.put("hzlxdm",hzlxdm);//ghzl 02-->2   other-->1
				jsonObject.put("kh",objGhxx.getString("brid"));
				jsonObject.put("klx","99");
				jsonObject.put("zjhm",objGhxx.getString("sfzjhm"));
				jsonObject.put("zjlx","01");
				jsonObject.put("brxm",objGhxx.getString("brxm"));
				jsonObject.put("brxb",objGhxx.getString("brxb"));
				jsonObject.put("lxdh",objGhxx.getString("sjhm"));
				jsonObject.put("jzrq",jzsjFormat); //需要格式化1741317897000 yyyy-mm-dd
				jsonObject.put("kdlx","0");
				jsonObject.put("kdxm",jsRry);// mzfy2.get(‘fylb’) == 210,242是检验 1，  204，205，206，207，208，209，224，225 ，236，237，238是检查2
				if(jsRry.size() > 0){
					JSONObject jsbResult = sendRequest(jsonObject);
					result.getResResult().put("json", jsbResult);
				}
			}
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("查询检查检验互认提醒信息错误:"+ex.getCause().getMessage());
			logger.error("调用【Mzys_lczd】查询检查检验互认提醒信息错误【MzysZlglBrjzCspServiceImpl query】发生异常", ex);
		}
		return result;
	}


	/**
	 * 电子处方作废
	 */
	@Transactional(rollbackFor=Exception.class)
	@Override
	public UtilResponse Delete_Mzcf( UtilRequest msg) throws Exception {

		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Yfb_ypcfModel ypcf = (Yfb_ypcfModel)msg.getParam().get("ypcf");
			List<Mzb_brfyModel> mzfy_List = (List<Mzb_brfyModel>)msg.getParam1().get("mzfy");

			//********************  处理更新  **********************
			Integer ypcfRef = 0 , mzfyRef = 0 ;

			if(ypcf == null ){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：处方号为空！");
				logger.error("数据提交失败：处方号为空！【MzysZlglBrjzCspServiceImpl Delete_Mzcf】") ;
				throw new Exception();
			}

			if(mzfy_List == null || mzfy_List.size() <= 0){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：费用明细信息为空！");
				logger.error("数据提交失败：费用明细信息为空！【MzysZlglBrjzCspServiceImpl Delete_Mzcf】") ;
				throw new Exception();
			}
			//医疗机构编码处理
			for (Mzb_brfyModel mzfy : mzfy_List) {
				mzfy.setYljgbm(msg.getYljgbm());
			}
			ypcf.setYljgbm(msg.getYljgbm());
			//1、更新费用表
			mzfyRef = mzb_brfyModelMapper.update(mzfy_List) ;
			if (mzfyRef != -1){  //判断更新行数
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：费用明细保存失败！提交行数：" + mzfyRef );
				logger.error("数据提交失败：费用明细保存失败【MzysZlglBrjzCspServiceImpl Delete_Mzcf】 提交行数：" + mzfyRef ) ;
				throw new Exception();  //异常自动返回  并且SQL事务回滚
			}

			//2、作废处方主表
			ypcfRef = mzysZlglBrjzModelMapper.Ypcf_ZF(ypcf) ;
			if (ypcfRef <= 0){  //判断更新行数
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：处方主表保存失败！提交行数：" + ypcfRef );
				logger.error("数据提交失败：处方主表保存失败【MzysZlglBrjzCspServiceImpl Delete_Mzcf】 提交行数：" + ypcfRef ) ;
				throw new Exception();  //异常自动返回  并且SQL事务回滚
			}
			//上传病历用
			Yfb_yppfModel bean = new Yfb_yppfModel();
			bean.setCfh(ypcf.getCfh());
			bean.setYljgbm(msg.getYljgbm());
			List<Yfb_yppfModel> yppf_List = (List<Yfb_yppfModel>) yfb_yppfModelMapper.queryYppf(bean);
			result.getResResult().put("emr_yppf", yppf_List );
			result.getResResult().put("emr_ypcf",  ypcf);
			//返回成功标志
			result.setResultMsg("处方作废保存成功！");
			result.getResResult().put("ref", 1 );
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("门诊处方作废失败:"+ex.getCause().getMessage());
			logger.error("调用门诊处方作废接口【MzysZlglBrjzCspServiceImpl Delete_Mzcf】发生异常",ex) ;
			throw new Exception();
		}
		return result;
	}


	//根据药品编码查询药品种类及收费项目
	@Override
	public UtilResponse getMxfybm(UtilRequest msg){
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			MzysZlglBrjzYpzlfyModel cxbean = (MzysZlglBrjzYpzlfyModel)msg.getParam().get("bean");
			cxbean.setYljgbm(msg.getYljgbm());
			MzysZlglBrjzYpzlfyModel bean = mzysZlglBrjzModelMapper.getMxfybm(cxbean) ;
			result.getResResult().put("bean", bean);
			//返回成功标志
			result.setResultMsg("查询存成功！");
			result.getResResult().put("ref", 1 );
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据药品编码查询药品种类及收费项目失败:"+ex.getCause().getMessage());
			logger.error("调用根据药品编码查询药品种类及收费项目【MzysZlglBrjzCspServiceImpl getMxfybm】发生异常",ex) ;
		}
		return result;
	}


	//根据挂号序号查询处方主表信息(yfb_ypcf)
	@Override
	public UtilResponse selectCF(UtilRequest msg){
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Yfb_ypcfModel cxbean = (Yfb_ypcfModel)msg.getParam().get("bean");
			cxbean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(cxbean.getPage(), cxbean.getRows());
			List<Yfb_ypcfModel> list = mzysZlglBrjzModelMapper.selectCF(cxbean) ;
			PageInfo<Yfb_ypcfModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据挂号序号查询处方主表信息失败:"+ex.getCause().getMessage());
			logger.error("调用根据挂号序号查询处方主表信息【MzysZlglBrjzCspServiceImpl selectCF】发生异常",ex) ;
		}
		return result;
	}

	//根据处方号查询处方主表信息(yfb_ypcf)
	@Override
	public UtilResponse selectYpcf(UtilRequest msg){
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Yfb_ypcfModel cxbean = (Yfb_ypcfModel)msg.getParam().get("bean");
			cxbean.setYljgbm(msg.getYljgbm());
			Yfb_ypcfModel bean = mzysZlglBrjzModelMapper.selectYpcf(cxbean) ;
			result.getResResult().put("bean", bean);
			//返回成功标志
			result.setResultMsg("查询成功！");
			result.getResResult().put("ref", 1 );
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据处方号查询处方主表信息失败:"+ex.getCause().getMessage());
			logger.error("调用根据处方号查询处方主表信息【MzysZlglBrjzCspServiceImpl selectYpcf】发生异常",ex) ;
		}
		return result;
	}

	//根据处方号和医嘱类型查询处方明细
	@Override
	public UtilResponse selectCFMX(UtilRequest msg){
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Yfb_yppfModel cxbean = (Yfb_yppfModel)msg.getParam().get("bean");
			cxbean.setYljgbm(msg.getYljgbm());
			List<Yfb_yppfModel> bean = mzysZlglBrjzModelMapper.selectCFMX(cxbean) ;
			result.getResResult().put("bean", bean);
			//返回成功标志
			result.setResultMsg("查询存成功！");
			result.getResResult().put("ref", 1 );
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据处方号查询处方明细信息失败:"+ex.getCause().getMessage());
			logger.error("调用根据处方号查询处方明细表信息【MzysZlglBrjzCspServiceImpl selectCFMX】发生异常",ex) ;
		}
		return result;
	}

	@Override
	public UtilResponse savePsjg(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		Integer yppfRef = 0;
		try {
			List<Yfb_yppfModel> yppfList = (List<Yfb_yppfModel>)msg.getParam().get("yppfList");
			yppfRef = yfb_yppfModelMapper.updatepf(yppfList) ;
			result.getResResult().put("ref", yppfRef.toString());
		}catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("保存皮试结果失败:"+e.getCause().getMessage());
			logger.error("调用保存皮试结果接口【MzysZlglBrjzCspServiceImpl deleteOnePf】发生异常", e);
		}
		return result;
	}

	@Override
	public UtilResponse queryBrxx(UtilRequest msg){
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Yfb_ypcfModel ypcf = (Yfb_ypcfModel)msg.getParam().get("bean");
			ypcf.setYljgbm(msg.getYljgbm());
			Gyb_brjbxxModel brxx = mzysZlglBrjzModelMapper.queryBrxx(ypcf) ;
			result.getResResult().put("brxx", brxx);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据挂号序号查询处方主表信息失败:"+ex.getCause().getMessage());
			logger.error("调用根据挂号序号查询处方主表信息【MzysZlglBrjzCspServiceImpl selectCF】发生异常",ex) ;
		}
		return result;
	}


	//根据处方号和医嘱类型查询处方附加费
	@Override
	public UtilResponse selectFJF(UtilRequest msg){
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Mzb_brfyModel bean = (Mzb_brfyModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());

			List<Mzb_brfyModel> bean_fjf = mzysZlglBrjzModelMapper.selectFJF(bean) ;
			result.getResResult().put("bean", bean_fjf);
			//返回成功标志
			result.setResultMsg("查询存成功！");
			result.getResResult().put("ref", 1 );
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据处方号查询处方附加费信息失败:"+ex.getCause().getMessage());
			logger.error("调用根据处方号查询处方附加费表信息【MzysZlglBrjzCspServiceImpl selectFJF】发生异常",ex) ;
		}
		return result;
	}

	/**
	 * 更新病人基本信息和挂号信息
	 */
	@Transactional(rollbackFor=Exception.class)
	@Override
	public UtilResponse updateBrjbxxAndGhxx(UtilRequest msg) throws Exception{
		UtilResponse result = UtilResponse.newInstance();
		try{
			Ghb_BrghSaveModel bean =(Ghb_BrghSaveModel) msg.getParam().get("bean");
			UserInfoModel user=(UserInfoModel) msg.getUserinfo().get("user");
			List<CsqxInfoModel> csqxinfo = (List<CsqxInfoModel>) msg.getCsqxinfo().get("csqxinfo");
			String nltozl = JsonUtil.Utilpubfun.getCsqxAll(csqxinfo, "N05001200266");  // 挂号种类对应年龄判断-兴安妇保 0：不判断，[对应关系json数组]
			Integer sfpdnl = StringUtils.isEmpty(JsonUtil.Utilpubfun.getCsqxAll(csqxinfo, "N05001200265"))?0: Integer.valueOf(JsonUtil.Utilpubfun.getCsqxAll(csqxinfo, "N05001200265")); // 挂号是否判断年龄收费-兴安妇保0：不判断，1:判断
			//获取病人基本信息
			Gyb_brjbxxModel brjbxxModel=bean.getZcxx();
			//获取病人挂号信息
			Ghb_brghModel brghModel=bean.getGhxx();

			//执行
			if(brjbxxModel!=null){
				msg.getParam().put("bean", brjbxxModel);
				UtilResponse brjbxxRef=gyb_brjbxxCspService.updatebatch(msg);
				int ref = (int)brjbxxRef.getResResult().get("ref");
				if (ref !=1) {
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("【病人基本信息信息保存】 失败:    提交行数：" + ref );
					logger.error("调用病人基本信息保存接口【MzysZlglBrjzCspServiceImpl updateBrjbxxAndGhxx】失败，提交行数：" + ref ) ;
					throw new Exception();  //异常自动返回  并且SQL事务回滚
				}
			}
			if(brghModel!=null){
				//改变病人接诊状态
				if(brghModel.getJzbz().equals("0")){
					brghModel.setJzbz("1");
					brghModel.setJzsj(new Date());
					brghModel.setJzys(user.getCzybm());
					brghModel.setJzysxm(user.getCzyxm());
					brghModel.setYljgbm(msg.getYljgbm());
					int i=ghb_brghModelMapper.update(brghModel);
					if (i <= 0){  //判断更新行数
						result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
						result.setResultMsg("数据提交失败：挂号接诊信息修改失败！提交行数：" + i );
						logger.error("数据提交失败：挂号接诊信息修改失败【MzysZlglBrjzCspServiceImpl Save_Mzcf】 提交行数：" + i ) ;
						throw new Exception();  //异常自动返回  并且SQL事务回滚
					}
					/********************************************兴安妇保接诊更新诊察费start**********************************************/
					if(StringUtils.isNotEmpty(nltozl)){
						List<String> lst= new ArrayList<>();
						String ghxh = brghModel.getGhxh();
						String jzys =user.getCzybm();
						lst.add(jzys);
						Gyb_RybmModel rybm = new Gyb_RybmModel();
						rybm.setYljgbm(msg.getYljgbm());
						rybm.setSort("rybm");
						rybm.setSearchry(lst);
						List<Gyb_RybmModel> list = gyb_RybmModelMapper.queryList(rybm);
						List<Gyb_hbsfxmModel> ghfyList= new ArrayList<>();
						if(list!=null&&list.size()>0){
							Gyb_RybmModel gyb_rybmModel = list.get(0);
							String ghzl = gyb_rybmModel.getGhzlbm();
							Short brnl = brghModel.getBrnl();
							String nldw = brghModel.getNldw();
							if(null!=sfpdnl&&sfpdnl!=0){
								if(("1".equals(nldw)&&Integer.valueOf(brnl)<sfpdnl)
										||("2".equals(nldw)&&Integer.valueOf(brnl)<sfpdnl*12)
										||("3".equals(nldw)&&Integer.valueOf(brnl)<sfpdnl*12*30)
										||("4".equals(nldw)&&Integer.valueOf(brnl)<sfpdnl*12*30*24)
										||("5".equals(nldw)&&Integer.valueOf(brnl)<sfpdnl*12*30*24*60)
								){
									JSONObject nlfyjson = JSONObject.parseObject(nltozl);
									String mxfyxmbm  = nlfyjson.getString(ghzl);
									Gyb_hbsfxmModel ghfx=new Gyb_hbsfxmModel();
									ghfx.setYljgbm(msg.getYljgbm());
									ghfx.setMxfybm(mxfyxmbm);
									ghfyList =gyb_hbsfxmModelMapper.queryFyxxByGhzl(ghfx);
								}else{
									Gyb_hbsfxmModel hbfy=new Gyb_hbsfxmModel();
									hbfy.setYljgbm(msg.getYljgbm());
									hbfy.setGhzlbm(ghzl);
									ghfyList = gyb_hbsfxmModelMapper.queryGhfy(hbfy);
								}
							}else{
								Gyb_hbsfxmModel hbfy=new Gyb_hbsfxmModel();
								hbfy.setYljgbm(msg.getYljgbm());
								hbfy.setGhzlbm(ghzl);
								ghfyList = gyb_hbsfxmModelMapper.queryGhfy(hbfy);
							}

							if(null!=ghfyList&&ghfyList.size()>0){
								for(Gyb_hbsfxmModel ghfb:ghfyList){
									String fylb = ghfb.getFylb();
									String mxfybm = ghfb.getMxfybm();
									String mxfymc = ghfb.getMxfymc();
									Double fydj = ghfb.getFydj();
									Double fyje=ghfb.getFyje();
									Double fysl = ghfb.getFysl();
									if(StringUtils.isEmpty(fylb)){
										result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
										result.setResultMsg("兴安修改接诊费用失败fylb为空");
										logger.error("-------------->兴安修改接诊费用失败fylb为空") ;
										throw new Exception();  //异常自动返回  并且SQL事务回滚
									}
									Mzb_brfyModel brfyModel = new Mzb_brfyModel();
									brfyModel.setRyghxh(ghxh);
									brfyModel.setFylb(fylb);
									brfyModel.setMxfyxmbm(mxfybm);
									brfyModel.setMxfyxmmc(mxfymc);
									brfyModel.setFyje(fyje);
									brfyModel.setFysl(fysl);
									brfyModel.setFydj(fydj);
									brfyModel.setYljgbm(msg.getYljgbm());
									mzb_brfyModelMapper.updateBrghfy(brfyModel);
								}
							}


						}
					}
					/********************************************兴安妇保接诊更新诊察费end**********************************************/
					//更新挂号费用mzys
					mzb_brfyModelMapper.updateBrfyMzys(brghModel);
				}
				msg.getParam().put("bean", brghModel);
				UtilResponse brghRef=ghb_brghCspService.update(msg);
				String ghref= (String) brghRef.getResResult().get("ref");
				if(Integer.parseInt(ghref)<=0){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("【病人挂号信息信息保存】 失败:    提交行数：" + Integer.parseInt(ghref) );
					logger.error("调用病人挂号信息保存接口【MzysZlglBrjzCspServiceImpl updateBrjbxxAndGhxx】失败，提交行数：" + Integer.parseInt(ghref) ) ;
					throw new Exception();  //异常自动返回  并且SQL事务回滚
				}
			}
			result.getResResult().put("ref", 1);

		}catch (Exception ex){
			ex.printStackTrace();
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("病人挂号信息保存失败:"+ex.getCause().getMessage());
//			logger.error("调用病人挂号信息保存接口【MzysZlglBrjzCspServiceImpl updateBrjbxxAndGhxx】发生异常",ex);
			throw new Exception();  //异常自动返回  并且SQL事务回滚
		}
		return result;
	}

	/**
	 * 分组查询检查项目处方
	 */
	@Override
	public UtilResponse selectJcxmCf(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Mzb_brfyModel bean = (Mzb_brfyModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());
			List<Mzb_brfyModel> list = mzysZlglBrjzModelMapper.selectJcxmCf(bean) ;
			result.getResResult().put("list", list);
			//返回成功标志
			result.setResultMsg("查询存成功！");
			result.getResResult().put("ref", 1 );
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据医嘱号分组查询检查项目处方失败:"+ex.getCause().getMessage());
			logger.error("调用根据医嘱号分组查询检查项目处方信息【MzysZlglBrjzCspServiceImpl selectJcxmCf】发生异常",ex) ;
		}
		return result;
	}


	/**
	 * 根据医嘱号码查询检查项目明细
	 */
	@Override
	public UtilResponse selectJcxmMx(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Mzb_brfyModel bean = (Mzb_brfyModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<Mzb_brfyModel> list = mzysZlglBrjzModelMapper.selectJcxmMx(bean) ;
			result.getResResult().put("list", list);
			//返回成功标志
			result.setResultMsg("查询存成功！");
			result.getResResult().put("ref", 1 );
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据医嘱号码查询检查项目明细失败:"+ex.getCause().getMessage());
			logger.error("调用根据医嘱号码查询检查项目明细信息【MzysZlglBrjzCspServiceImpl selectJcxmMx】发生异常",ex) ;
		}
		return result;
	}

	/**
	 * 根据医嘱号码查询检查项目明细
	 */
	@Override
	public UtilResponse selectLsJcxmMx(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Mzb_brfyModel bean = (Mzb_brfyModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<Mzb_brfyModel> list = mzysZlglBrjzModelMapper.selectLsJcxmMx(bean) ;
			result.getResResult().put("list", list);
			//返回成功标志
			result.setResultMsg("查询存成功！");
			result.getResResult().put("ref", 1 );
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据医嘱号码查询检查项目明细失败:"+ex.getCause().getMessage());
			logger.error("调用根据医嘱号码查询检查项目明细信息【MzysZlglBrjzCspServiceImpl selectJcxmMx】发生异常",ex) ;
		}
		return result;
	}


	/**
	 * 门诊检查处方保存
	 */
	@Transactional(rollbackFor=Exception.class)
	@Override
	public UtilResponse Save_MzJccf(UtilRequest msg) throws Exception {

		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			List<Mzb_brfyModel> mzfy_List = (List<Mzb_brfyModel>)msg.getParam().get("mzfy");
			//获取处方号
			String cfh="";
			String jgbm ="";
			if(mzfy_List.get(0).getYzhm()==null||mzfy_List.get(0).getYzhm().equals("")){
				GetXhidMsgModel bean = new GetXhidMsgModel();
				bean.setScfs("3");
				bean.setCslx("Yzh");
				msg.getParam().put("bean", bean);
				UtilResponse res_cfh = pubFunCspService.GetIdNameLock(msg);
				GetXhidResModel brbean = (GetXhidResModel)res_cfh.getResResult().get("bean");
				cfh = brbean.getDqxh();
			}

			//********************  处理更新  **********************
			Integer mzfyRef = 0 ;
			if(mzfy_List == null || mzfy_List.size() <= 0){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：费用明细信息为空！");
				logger.error("数据提交失败：费用明细信息为空！【MzysZlglBrjzCspServiceImpl Save_Mzcf】") ;
				throw new Exception();
			}

			//处理医疗机构编码
			for (Mzb_brfyModel mzfy : mzfy_List) {
				mzfy.setYljgbm(msg.getYljgbm());
				if(mzfy.getYzhm()==null||mzfy.getYzhm().equals("")){
					mzfy.setYzhm(cfh);
				}
			}

			//说明这是执行更新操作（处方号已经存在）
			if(cfh.equals("")){
				//不允许更新，防止已收费项目收费标志被修改
				result.setResultMsg("检查处方信息已经保存，请勿重复保存！");
				result.setResultCode("1");
				return result;
				//1、更新费用表
//				mzfyRef = mzb_brfyModelMapper.update(mzfy_List) ;
			}else{
				//1、添加费用表
				mzfyRef = mzb_brfyModelMapper.insert(mzfy_List) ;
			}
			if (mzfyRef != -1){  //判断更新行数
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("数据提交失败：费用明细保存失败！提交行数：" + mzfyRef );
				logger.error("数据提交失败：费用明细保存失败【MzysZlglBrjzCspServiceImpl Save_MzJccf】 提交行数：" + mzfyRef ) ;
				throw new Exception();  //异常自动返回  并且SQL事务回滚
			}
			//取传病历信息
			jgbm = msg.getYljgbm();
			Mzb_brfyModel cxbean = new Mzb_brfyModel();
			cxbean.setYzhm(cfh);
			cxbean.setYljgbm(jgbm);
			mzfy_List = mzb_brfyModelMapper.queryBlYzfy(cxbean);
			//返回成功标志
			result.setResultMsg("检查处方信息保存成功！");
			result.getResResult().put("ref", 1 );
			result.getResResult().put("emr_mzfy", mzfy_List );

		}catch (Exception ex){
			ex.printStackTrace();
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("门诊检查处方保存失败:"+ex.getCause().getMessage());
			logger.error("调用门诊检查处方保存接口【MzysZlglBrjzCspServiceImpl Save_MzJccf】发生异常",ex) ;
			throw new Exception();
		}
		return result;
	}

	/**
	 * 打印处方信息
	 */
	@Override
	public UtilResponse printCf(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Yfb_ypcfModel cfbean = (Yfb_ypcfModel)msg.getParam().get("bean");
			cfbean.setYljgbm(msg.getYljgbm());
			List<CfPrintModel> list = mzysZlglBrjzModelMapper.printCf(cfbean) ;
			//changed by Kiter on 2021.01.27
			if (list != null && list.size() > 0){
				list.forEach(item -> {
					if (StringUtils.isNotEmpty(item.getQtzdmx())){
						if (item.getQtzdmx().contains(item.getLczd())){
							item.setLczd(item.getQtzdmx());
						} else {
							item.setLczd(item.getLczd() + "," + item.getQtzdmx());
						}
					}
				});
			}

			result.getResResult().put("list", list);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("打印处方信息失败:"+ex.getCause().getMessage());
			logger.error("调用打印处方信息【MzysZlglBrjzCspServiceImpl printCf】发生异常",ex) ;
		}
		return result;
	}

	/**
	 * 打印输液贴
	 */
	@Override
	public UtilResponse printSyt(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			MzysSytPrintModel bean = (MzysSytPrintModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			MzysSytPrintModel print = mzysZlglBrjzModelMapper.printSyt(bean) ;
			result.getResResult().put("bean", print);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("打印输液贴信息失败:"+ex.getCause().getMessage());
			logger.error("调用打印输液贴信息【MzysZlglBrjzCspServiceImpl printSyt】发生异常",ex) ;
		}
		return result;
	}

	/**
	 * 检查检验申请单
	 */
	@Override
	public UtilResponse printJcjySqd(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			jcjyPrintModel bean = (jcjyPrintModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<jcjyPrintModel> list = mzysZlglBrjzModelMapper.printJcjySqd(bean) ;
			result.getResResult().put("list", list);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("打印检查检验申请单信息失败:"+ex.getCause().getMessage());
			logger.error("调用打印检查检验申请单信息【MzysZlglBrjzCspServiceImpl printJcjySqd】发生异常",ex) ;
		}
		return result;
	}

	/*
	 * 删除单个配方信息
	 */
	@Override
	public UtilResponse deleteOnePf(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Yfb_yppfModel bean = (Yfb_yppfModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = yfb_yppfModelMapper.deleteOnePf(bean);
			result.getResResult().put("ref", ref.toString());
		}catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("删除单个配方失败:"+e.getCause().getMessage());
			logger.error("调用删除单个配方接口【MzysZlglBrjzCspServiceImpl deleteOnePf】发生异常", e);
		}
		return result;
	}


	@Override
	public UtilResponse sffzpd(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Ghb_brghModel bean = (Ghb_brghModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<Ghb_brghModel> list = ghb_brghModelMapper.sffzpd(bean) ;
			result.getResResult().put("list", list);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("病人ID查询挂号信息失败:"+ex.getCause().getMessage());
			logger.error("调用病人ID查询挂号信息【MzysZlglBrjzCspServiceImpl sffzpd】发生异常",ex) ;
		}
		return result;
	}

	@Override
	public UtilResponse selectLgyz(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Mjzgl_YzdModel bean = (Mjzgl_YzdModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<Mjzgl_YzdModel> list = mzysZlglBrjzModelMapper.selectLgyz(bean) ;
			result.getResResult().put("list", list);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("留观病人皮试医嘱信息查询:"+ex.getCause().getMessage());
			logger.error("留观病人皮试医嘱信息查询发生异常",ex) ;
		}
		return result;
	}

	@Override
	public UtilResponse updateWxPayUrl(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Mzb_brfyModel bean = (Mzb_brfyModel)msg.getParam().get("bean");
			logger.info("更新微信预支付链接：" );
			int ref = mzb_brfyModelMapper.updateWxPayUrl(bean) ;
			result.getResResult().put("ref", ref);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("更新微信预支付链接失败:"+ex.getCause().getMessage());
			logger.error("更新微信预支付链接失败",ex) ;
		}
		return result;
	}

	@Override
	public UtilResponse ryxxlr(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Ghb_BrghSaveModel ghb_brghSaveModel = (Ghb_BrghSaveModel) msg.getParam().get("bean");
			Ghb_brghModel bean = ghb_brghSaveModel.getGhxx();
			UserInfoModel userInfo = (UserInfoModel) msg.getUserinfo().get("user");
			bean.setYljgbm(msg.getYljgbm());
			bean.setCzyxm(userInfo.getCzyxm());
			ghb_brghModelMapper.inserFzxx(bean);
			//返回成功标志
			result.setResultMsg("新增人员信息成功！");
			result.getResResult().put("ref", 1);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("新增人员信息失败:" + e.getCause().getMessage());
			logger.error("新增人员信息失败发生异常", e);
		}
		return result;
	}

	@Override
	public UtilResponse fzxxjc(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Ghb_BrghSaveModel ghb_brghSaveModel = (Ghb_BrghSaveModel) msg.getParam().get("bean");
			Ghb_brghModel bean = ghb_brghSaveModel.getGhxx();
			UserInfoModel userInfo = (UserInfoModel) msg.getUserinfo().get("user");

			bean.setYljgbm(msg.getYljgbm());
			bean.setCzyxm(userInfo.getCzyxm());

			Integer count = ghb_brghModelMapper.selectCount(bean.getSfzjhm());
			if (count > 0) {
				ghb_brghModelMapper.updateDtcs(bean.getSfzjhm());
				result.getResResult().put("status", "2");
			} else {
				result.getResResult().put("status", "1");
			}
			//返回成功标志
			result.getResResult().put("ref", 1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public UtilResponse selectZyCF(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			//处理数据业务
			Yfb_ypcfModel cxbean = (Yfb_ypcfModel)msg.getParam().get("bean");
			cxbean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(cxbean.getPage(), cxbean.getRows());
			List<Yfb_ypcfModel> list = mzysZlglBrjzModelMapper.selectZyCF(cxbean);
			PageInfo<Yfb_ypcfModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据挂号序号查询处方主表信息失败:"+ex.getCause().getMessage());
			logger.error("调用根据挂号序号查询处方主表信息【MzysZlglBrjzCspServiceImpl selectCF】发生异常",ex) ;
		}
		return result;
	}

	/*
	 * 急诊分诊管理
	 */
	@Override
	public UtilResponse JzFzgl(UtilRequest msg) {
		UtilResponse result = new UtilResponse().newInstance();
		Integer ref = 0;
		String strErr = "";
		try {
			Map<String, Object> map = (Map<String, Object>) msg.getParam().get("obj");
			String brid = (String) map.get("brid");//住院号
			String lyfsbm = (String) map.get("lyfsbm");//住院医生编码
			String fzjbbm = (String) map.get("fzjbbm");//住院号
			String ghxh = (String) map.get("ghxh");//挂号序号
			String fzkbbm = (String) map.get("fzkbbm");//住院医生编码
			String tw = (String) map.get("tw");//体温
			String mb = (String) map.get("mb");//脉搏
			String hx = (String) map.get("hx");//呼吸
			String xy = (String) map.get("xy");//血压
			String xl = (String) map.get("xl");//心率
			String xtms = (String) map.get("xtms");//血糖描述
			String xt = (String) map.get("xt");//血糖
			String xtong = (String) map.get("xtong");//血酮
			String zglxbm = (String) map.get("zglxbm");
			String rybm = (String) map.get("rybm");
			String bz = (String) map.get("bz");
			String bqms = (String) map.get("bqms");
			Jzk_fzdjModel fzdj = new Jzk_fzdjModel();
			fzdj.setBrid(brid);
			fzdj.setFzjbbm(fzjbbm);
			fzdj.setLyfsbm(lyfsbm);
			fzdj.setGhxh(ghxh);
			fzdj.setFzkbbm(fzkbbm);
			fzdj.setTw(tw);
			fzdj.setMb(mb);
			fzdj.setHx(hx);
			fzdj.setXy(xy);
			fzdj.setXl(xl);
			fzdj.setXtms(xtms);
			fzdj.setXt(xt);
			fzdj.setXtong(xtong);
			fzdj.setZglxbm(zglxbm);
			fzdj.setRybm(rybm);
			fzdj.setBz(bz);
			fzdj.setBqms(bqms);
			fzdj.setYljgbm("000001");
			ref = mzysZlglBrjzModelMapper.updateJzkfz(fzdj);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			// TODO: handle exception
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("换主管医生申请失败:" + e.getCause().getMessage());
			logger.error("调用转科换床接口【HszByglCwglCspServiceImpl hzkys】发生异常", e);
		}
		return result;
	}

	@Override
	public Gyb_ksbmModel queryYfKsmc(Yfb_yfModel yfbean){

		Gyb_ksbmModel bean = mzysZlglBrjzModelMapper.queryYfKsmc(yfbean) ;
		return bean;
	}

	private JSONObject sendRequest(JSONObject jsonRecord) {
		String JCJY_URL = read.getValue("jcjy_url");
		String APP_CODE = read.getValue("app_code");
		String ALERT_METHOD = read.getValue("alert_method");
		String ALERT_SIGN = read.getValue("alert_sign");
		if(JCJY_URL.isEmpty() && APP_CODE.isEmpty() && ALERT_SIGN.isEmpty()){
			logger.error("获取服务器地址错误!") ;
			return null;
		}
		try (CloseableHttpClient httpClient = createHttpClient()) {
			HttpPost request = new HttpPost(JCJY_URL+ALERT_METHOD);
			request.addHeader("Content-Type", "application/json; charset=utf-8");
			request.addHeader("appCode", APP_CODE);
			request.addHeader("sign", ALERT_SIGN);
			// **1️⃣  请求数据 -> 转 JSON -> SM4 加密**
			String jsonBody = jsonRecord.toJSONString();  // 转换为 JSON
			String encryptedRequest = Sm4HexEn.encode(jsonBody); // **SM4 加密**
			request.setEntity(new StringEntity(encryptedRequest));
 			try (CloseableHttpResponse response = httpClient.execute(request)) {
				String responseString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
				JSONObject responseJson = JSONObject.parseObject(responseString);
				String code = responseJson.getString("code");
				String encryptedData = responseJson.getString("data"); // 获取密文数据
				// **2️⃣ 解析 API 响应**
				if ("0".equals(code) || "2".equals(code)) {
					// **解密 `data` 字段**
					String decryptedData = Sm4HexDe.decode(encryptedData);
					JSONObject dataNode = JSONObject.parseObject(decryptedData);
					if(!dataNode.getString("url").isEmpty()){
						dataNode.put("url", JCJY_URL+dataNode.getString("url"));
					}
					dataNode.put("code", code);
					dataNode.put("msg","当前病人有外院可互认项目,请点击链接查看!");
					return dataNode;
				}
			}
		} catch (Exception e) {
			logger.error("调用 API 失败: {}", e.getMessage(), e);
		}
		return null;
	}

	private CloseableHttpClient createHttpClient() throws Exception {
		// 创建一个信任所有证书的 TrustManager
		TrustManager[] trustAllCerts = new TrustManager[]{
				new X509TrustManager() {
					@Override
					public void checkClientTrusted(X509Certificate[] chain, String authType) {}
					@Override
					public void checkServerTrusted(X509Certificate[] chain, String authType) {}
					@Override
					public X509Certificate[] getAcceptedIssuers() {
						return new X509Certificate[0];
					}
				}
		};
		// 初始化 SSL 上下文，忽略证书验证
		SSLContext sslContext = SSLContexts.custom()
				.loadTrustMaterial(null, (chain, authType) -> true)
				.build();
		SSLConnectionSocketFactory sslFactory = new SSLConnectionSocketFactory(
				sslContext,
				NoopHostnameVerifier.INSTANCE // 忽略 Hostname 校验
		);
		return HttpClients.custom()
				.setSSLSocketFactory(sslFactory)
				.build();
	}



}
