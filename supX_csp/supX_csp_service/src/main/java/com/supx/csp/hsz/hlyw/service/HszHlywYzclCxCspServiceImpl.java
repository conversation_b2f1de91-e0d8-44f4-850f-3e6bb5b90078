package com.supx.csp.hsz.hlyw.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.comm.util.DateTimeUtil;
import com.supx.csp.api.hsz.bygl.pojo.Hsz_WrzbrModel;
import com.supx.csp.api.hsz.hlyw.pojo.*;
import com.supx.csp.api.hsz.hlyw.pojo.*;
import com.supx.csp.api.hsz.hlyw.service.IHszHlywYzclCxCspService;
import com.supx.csp.api.zygl.crygl.pojo.Zyb_cwhModel;
import com.supx.csp.api.zyys.ysyw.pojo.HzxxListResModel;
import com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel;
import com.supx.csp.api.zyys.ysyw.pojo.Zyys_ylyzModel;
import com.supx.csp.api.zyys.ysyw.pojo.Zyys_ypyzModel;
import com.supx.csp.user.service.ServiceInvocationHelper;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.csp.hsz.hlyw.dao.New1Hsz_YzZxdCxModelMapper;
import com.supx.csp.hsz.hlyw.dao.New1Hsz_YzZxdZxModelMapper;
import com.supx.csp.hsz.hlyw.dao.New1Hsz_YzclCxModelMapper;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: HszHlywYzclCxCspServiceImpl
 * @Description: TODO(护士医嘱处理查询实现类)
 * @date 2020年6月14日 下午4:21:50
 */
@Service("new1hszHlywYzclCxService")
@DubboService
public class HszHlywYzclCxCspServiceImpl extends ServiceInvocationHelper implements IHszHlywYzclCxCspService {

    private final Logger logger = LoggerFactory.getLogger(HszHlywYzclCxCspServiceImpl.class);
    @Autowired
    private New1Hsz_YzclCxModelMapper hsz_YzclCxModelMapper;
    @Autowired
    private New1Hsz_YzZxdCxModelMapper hsz_YzZxdModelMapper;
    @Autowired
    private New1Hsz_YzZxdCxModelMapper hsz_YzZxdCxModelMapper;
    @Autowired
    private New1Hsz_YzZxdZxModelMapper hsz_YzZxdZxModelMapper;


    /**
     * 查询最新医疗医疗执行记录
     *
     * @param msg
     * @return
     */
    @Override
    public UtilResponse queryYlzxlshListNew(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ylzxdModel bean = (Hsz_yz_ylzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_yz_ylzxdModel> list = hsz_YzZxdCxModelMapper.queryYlzxlshListNew(bean);
            result.getResResult().put("bean", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医疗医嘱最新执行记录查询失败:" + e.getCause().getMessage());
            logger.error("医疗医嘱最新执行记录接口【HszHlywYzclCxCspServiceImpl queryYlzxlshListNew】发生异常", e);
        }
        return result;
    }

    /**
     * 查询最新医品医疗执行记录
     *
     * @param msg
     * @return
     */
    @Override
    public UtilResponse querypzxlshListNew(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ypzxdModel bean = (Hsz_yz_ypzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_yz_ypzxdModel> list = hsz_YzZxdCxModelMapper.queryYpzxlshListNew(bean);
            result.getResResult().put("bean", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("药品医嘱最新执行记录查询失败:" + e.getCause().getMessage());
            logger.error("药品医嘱最新执行记录接口【HszHlywYzclCxCspServiceImpl queryYpzxlshListNew】发生异常", e);
        }
        return result;
    }

    /**
     * 未入住病人
     */
    @Override
    public UtilResponse queryWrzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            HzxxListResModel bean = (HzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            PageHelper.startPage(bean.getPage(), bean.getRows());
            List<Hsz_WrzbrModel> list = (List<Hsz_WrzbrModel>) hsz_YzclCxModelMapper.queryWrzList(bean);
            for (Hsz_WrzbrModel hz : list) {
                hz.setHszzt("待接入");
            }
            PageInfo<Hsz_WrzbrModel> pageInfo = new PageInfo<>(list);
            result.getResResult().put("pageInfo", pageInfo);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("未入科患者列表查询失败:" + e.getCause().getMessage());
            logger.error("调用未入科患者信息列表信息接口【HszHlywYzclCxCspServiceImpl queryWrzList】发生异常", e);
        }
        return result;
    }

    /**
     * 转出病人
     */
    @Override
    public UtilResponse queryZchzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            String ksbm = (String) msg.getParam().get("ksbm");
            String parm = (String) msg.getParam().get("parm");
            Map<String, Object> map = new HashMap<String, Object>();
            Hsz_WrzbrModel hsz_WrzbrModel = new Hsz_WrzbrModel();
            if (StrUtil.isNotBlank(parm)) {
                hsz_WrzbrModel = JSONObject.parseObject(parm, Hsz_WrzbrModel.class);
            }
            hsz_WrzbrModel.setZcks(ksbm);
            List<Hsz_WrzbrModel> list = (List<Hsz_WrzbrModel>) hsz_YzclCxModelMapper.queryZchzList(hsz_WrzbrModel);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("转出患者列表查询失败:" + e.getCause().getMessage());
            logger.error("调用转出患者信息列表信息接口【HszHlywYzclCxCspServiceImpl queryZchzList】发生异常", e);
        }
        return result;
    }

    /**
     * 转出病人
     */
    @Override
    public UtilResponse queryZchzCyList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            String ksbm = (String) msg.getParam().get("ksbm");
            String parm = (String) msg.getParam().get("parm");
            Map<String, Object> map = new HashMap<String, Object>();
            Hsz_WrzbrModel hsz_WrzbrModel = new Hsz_WrzbrModel();
            if (StrUtil.isNotBlank(parm)) {
                hsz_WrzbrModel = JSONObject.parseObject(parm, Hsz_WrzbrModel.class);
            }
            hsz_WrzbrModel.setZcks(ksbm);
            List<Hsz_WrzbrModel> list = (List<Hsz_WrzbrModel>) hsz_YzclCxModelMapper.queryZchzCyList(hsz_WrzbrModel);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("转出患者列表查询失败:" + e.getCause().getMessage());
            logger.error("调用转出患者信息列表信息接口【HszHlywYzclCxCspServiceImpl queryZchzList】发生异常", e);
        }
        return result;
    }

    /**
     * 执行医嘱
     */
    @Override
    public UtilResponse queryDzxyzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            YzxxListResModel bean = (YzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            String dqyz = (String) msg.getParam().get("dqyz");
            String czyks = (String) msg.getParam().get("czyks");
            logger.info("09执行医嘱>>>>>当前医嘱数据库获取值: " + dqyz + "  ...> 操作员科室:   " + czyks);
            if ("1".equals(dqyz)) {
                bean.setYsqmks(czyks);//取操作员科室(对应医生科室)
            }
            List<YzxxHzxxResResModel> list = (List<YzxxHzxxResResModel>) hsz_YzclCxModelMapper.queryDzxyzList(bean);
            if (!CollectionUtils.isEmpty(list)) {
                for (YzxxHzxxResResModel yz : list) {
                    yz.setZyts(DateTimeUtil.dateToDays(new Date(), yz.getRyrq()));
                    if (yz.getSfzh() == null) {
                        yz.setSfzh("");
                    }
                    if (yz.getLxdh() == null) {
                        yz.setLxdh("");
                    }
                    if (yz.getFyhj() == null) {
                        yz.setFyhj("0.00");
                    }
                    if (yz.getYjhj() == null) {
                        yz.setYjhj(0.00);
                    }
                    // num == 1
                    for (YzxxListResModel yzxx : yz.getYzxx()) {
                        //判断执行值是否是空
                        if (yzxx.getNumb() == 0 && yzxx.getZzxsz() != null && !"".equals(yzxx.getZzxsz())) {
                            String zxweek = DateTimeUtil.getWeekNumber(DateTimeUtil.getDayChar21ByDays(new Date(), bean.getZxyzts())) + "";

                            String[] zzxsztp = yzxx.getZzxsz().split(";");
                            boolean tpczz = true;
                            for (int i = 0; i < zzxsztp.length; i++) {
                                if (zxweek.equals(zzxsztp[i])) {
                                    tpczz = false;
                                    break;
                                }
                            }
                            if (tpczz) {
                                yzxx.setNumb(1);
                            }

                        }
                    }


                }
            }
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("执行医嘱列表查询失败:" + e.getCause().getMessage());
            logger.error("调用执行医嘱列表信息接口【HszHlywYzclCxCspServiceImpl queryDzxyzList】发生异常", e);
        }
        return result;
    }

    /**
     * 执行医嘱
     */
    @Override
    public UtilResponse queryYszDzxyzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            YzxxListResModel bean = (YzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<YzxxHzxxResResModel> list = (List<YzxxHzxxResResModel>) hsz_YzclCxModelMapper.queryYszDzxyzList(bean);
            if (!CollectionUtils.isEmpty(list)) {
                for (YzxxHzxxResResModel yz : list) {
                    yz.setZyts(DateTimeUtil.dateToDays(new Date(), yz.getRyrq()));
                    if (yz.getSfzh() == null) {
                        yz.setSfzh("");
                    }
                    if (yz.getLxdh() == null) {
                        yz.setLxdh("");
                    }
                    if (yz.getFyhj() == null) {
                        yz.setFyhj("0.00");
                    }
                    if (yz.getYjhj() == null) {
                        yz.setYjhj(0.00);
                    }
                    // num == 1
                    for (YzxxListResModel yzxx : yz.getYzxx()) {
                        //判断执行值是否是空
                        if (yzxx.getNumb() == 0 && yzxx.getZzxsz() != null && !"".equals(yzxx.getZzxsz())) {
                            String zxweek = DateTimeUtil.getWeekNumber(DateTimeUtil.getDayChar21ByDays(new Date(), bean.getZxyzts())) + "";

                            String[] zzxsztp = yzxx.getZzxsz().split(";");
                            boolean tpczz = true;
                            for (int i = 0; i < zzxsztp.length; i++) {
                                if (zxweek.equals(zzxsztp[i])) {
                                    tpczz = false;
                                    break;
                                }
                            }
                            if (tpczz) {
                                yzxx.setNumb(1);
                            }

                        }
                    }
                }
            }
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("执行医嘱列表查询失败:" + e.getCause().getMessage());
            logger.error("调用执行医嘱列表信息接口【HszHlywYzclCxCspServiceImpl queryDzxyzList】发生异常", e);
        }
        return result;
    }

    /**
     * 药品待执行医嘱查询
     */
    @Override
    public UtilResponse queryYpdzxyzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Zyys_ypyzModel bean = (Zyys_ypyzModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Zyys_ypyzModel> list = (List<Zyys_ypyzModel>) hsz_YzclCxModelMapper.queryYpdzxyzList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("执行药品医嘱列表查询失败:" + e.getCause().getMessage());
            logger.error("调用执行药品医嘱列表信息接口【HszHlywYzclCxCspServiceImpl queryYpdzxyzList】发生异常", e);
        }
        return result;
    }

    /**
     * 药品待执行非辅药医嘱查询
     */
    @Override
    public UtilResponse queryYpffydzxyzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Zyys_ypyzModel bean = (Zyys_ypyzModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Zyys_ypyzModel> list = (List<Zyys_ypyzModel>) hsz_YzclCxModelMapper.queryYpffydzxyzList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("执行药品医嘱列表查询失败:" + e.getCause().getMessage());
            logger.error("调用执行药品医嘱列表信息接口【HszHlywYzclCxCspServiceImpl queryYpdzxyzList】发生异常", e);
        }
        return result;
    }

    /**
     * 医疗待执行医嘱查询
     */
    @Override
    public UtilResponse queryYldzxyzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Zyys_ylyzModel bean = (Zyys_ylyzModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Zyys_ylyzModel> list = (List<Zyys_ylyzModel>) hsz_YzclCxModelMapper.queryYldzxyzList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("执行医疗医嘱列表查询失败:" + e.getCause().getMessage());
            logger.error("调用执行医疗医嘱列表信息接口【HszHlywYzclCxCspServiceImpl queryYldzxyzList】发生异常", e);
        }
        return result;
    }

    /**
     * 医嘱信息查询 含患者信息
     */
    @Override
    public UtilResponse queryHzxxYzxxList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            YzxxListResModel bean = (YzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            String dqyz = (String) msg.getParam().get("dqyz");
            String czyks = (String) msg.getParam().get("czyks");
            if ("1".equals(dqyz)) {
                bean.setYsqmks(czyks);//取操作员科室(对应医生科室)
            }
            List<YzxxHzxxResResModel> list = (List<YzxxHzxxResResModel>) hsz_YzclCxModelMapper.queryHzxxYzxxList(bean);
            for (YzxxHzxxResResModel yz : list) {
                // @yqq 当cyrq不为空时，住院时间诶出院日期减去入院日期
                if (yz.getCyrq() != null) {
                    yz.setZyts(DateTimeUtil.dateToDays(yz.getCyrq(), yz.getRyrq()));
                } else {
                    yz.setZyts(DateTimeUtil.dateToDays(new Date(), yz.getRyrq()));
                }

                if (yz.getSfzh() == null) {
                    yz.setSfzh("");
                }
                if (yz.getLxdh() == null) {
                    yz.setLxdh("");
                }
                if (yz.getFyhj() == null) {
                    yz.setFyhj("0.00");
                }
                if (yz.getYjhj() == null) {
                    yz.setYjhj(0.00);
                }
            }
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医嘱执行记录查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱执行记录信息接口【HszHlywYzclCxCspServiceImpl queryHzxxYzxxList】发生异常", e);
        }
        return result;
    }

    /**
     * 执行记录
     */
    @Override
    public UtilResponse queryZxjlList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_zxdModel bean = (Hsz_yz_zxdModel) msg.getParam().get("bean");
//			String newZxd = (String)msg.getParam().get("newZxd");
            bean.setYljgbm(msg.getYljgbm());
            String dqyz = (String) msg.getParam().get("dqyz");
            String czyks = (String) msg.getParam().get("czyks");
            String newZxd = bean.getNewZxd();
            logger.info("02执行记录>>>>>当前医嘱数据库获取值: " + dqyz + "  ...> 操作员科室:   " + czyks);
            if ("1".equals(dqyz)) {
                bean.setYsks(czyks);//取操作员科室(对应医生科室)
            }
            if (bean.getZfbz() == null || "".equals(bean.getZfbz())) {
                bean.setZfbz("0");
            }

            List<YzzxdHzxxResModel> list;
            if ("1".equals(newZxd)) {
                list = hsz_YzZxdModelMapper.queryNewZxd(bean);
            } else {
                if ("1".equals(bean.getSczxldety())) {//首次领药领第二天药
                    list = hsz_YzZxdModelMapper.queryZxjlList2(bean);
                } else {
                    list = hsz_YzZxdModelMapper.queryZxjlList(bean);
                }
            }
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医嘱执行记录查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱执行记录信息接口【HszHlywYzclCxCspServiceImpl queryZxjlList】发生异常", e);
        }
        return result;
    }

    /**
     * 药品执行记录(申领/取消申领)
     */
    @Override
    public UtilResponse queryYpzxjlList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ypzxdModel bean = (Hsz_yz_ypzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            String dqyz = (String) msg.getParam().get("dqyz");
            String czyks = (String) msg.getParam().get("czyks");
            logger.info("03药品申领>>>>>当前医嘱数据库获取值: " + dqyz + "  ...> 操作员科室:   " + czyks);
            if ("1".equals(dqyz)) {
                bean.setYsks(czyks);//取操作员科室(对应医生科室)
            }
            List<YpzxdHzxxResModel> list = (List<YpzxdHzxxResModel>) hsz_YzZxdModelMapper.queryYpzxjlList(bean);
            for (YpzxdHzxxResModel yz : list) {
                yz.setZyts(DateTimeUtil.dateToDays(new Date(), yz.getRyrq()));
                if (yz.getSfzh() == null) {
                    yz.setSfzh("");
                }
                if (yz.getLxdh() == null) {
                    yz.setLxdh("");
                }
                if (yz.getFyhj() == null) {
                    yz.setFyhj("0.00");
                }
                if (yz.getYjhj() == null) {
                    yz.setYjhj(0.00);
                }
            }
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("药品执行申领查询失败:" + e.getCause().getMessage());
            logger.error("调用药品执行申领信息接口【HszHlywYzclCxCspServiceImpl queryYpzxjlList】发生异常", e);
        }
        return result;
    }

    /**
     * 药品执行记录(根据执行记录ID查询  申领/取消申领)
     */
    @Override
    public UtilResponse queryYpzxlshList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ypzxdModel bean = (Hsz_yz_ypzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_yz_ypzxdModel> list = (List<Hsz_yz_ypzxdModel>) hsz_YzZxdModelMapper.queryYpzxlshList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("药品执行申领查询失败:" + e.getCause().getMessage());
            logger.error("调用药品执行申领信息接口【HszHlywYzclCxCspServiceImpl queryYpzxjlList】发生异常", e);
        }
        return result;
    }

    /**
     * 医疗执行记录
     */
    @Override
    public UtilResponse queryYlzxlshList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ylzxdModel bean = (Hsz_yz_ylzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_yz_ylzxdModel> list = (List<Hsz_yz_ylzxdModel>) hsz_YzZxdModelMapper.queryYlzxlshList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医疗执行查询失败:" + e.getCause().getMessage());
            logger.error("调用医疗执行信息接口【HszHlywYzclCxCspServiceImpl queryYlzxlshList】发生异常", e);
        }
        return result;
    }

    /**
     * 根据序号ID查询医疗医嘱
     */
    @Override
    public UtilResponse queryYlyzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Zyys_ylyzModel bean = (Zyys_ylyzModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Zyys_ylyzModel> list = (List<Zyys_ylyzModel>) hsz_YzclCxModelMapper.queryYlyzList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医疗医嘱查询失败:" + e.getCause().getMessage());
            logger.error("调用医疗医嘱接口【HszHlywYzclCxCspServiceImpl queryYlyzList】发生异常", e);
        }
        return result;
    }

    /**
     * 根据序号ID查询药品医嘱
     */
    @Override
    public UtilResponse queryYpyzList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Zyys_ypyzModel bean = (Zyys_ypyzModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Zyys_ypyzModel> list = (List<Zyys_ypyzModel>) hsz_YzclCxModelMapper.queryYpyzList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("药品医嘱查询失败:" + e.getCause().getMessage());
            logger.error("调用药品医嘱接口【HszHlywYzclCxCspServiceImpl queryYpyzList】发生异常", e);
        }
        return result;
    }

    /**
     * 根据序号ID查询医嘱
     */
    @Override
    public UtilResponse queryXhidYzxxList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            YzxxListResModel bean = (YzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<YzxxListResModel> list = (List<YzxxListResModel>) hsz_YzclCxModelMapper.queryXhidYzxxList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医嘱查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱接口【HszHlywYzclCxCspServiceImpl queryXhidYzxxList】发生异常", e);
        }
        return result;
    }

    /**
     * 根据序号ID医嘱，用于判断是否允许取消审核
     */
    @Override
    public UtilResponse queryQxyzsh(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_Yz_QxyzshCxModel bean = (Hsz_Yz_QxyzshCxModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_Yz_QxyzshCxModel> list = (List<Hsz_Yz_QxyzshCxModel>) hsz_YzclCxModelMapper.queryQxyzsh(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询取消审核医嘱失败:" + e.getCause().getMessage());
            logger.error("查询取消审核医嘱接口【HszHlywYzclCxCspServiceImpl queryQxyzsh】发生异常", e);
        }
        return result;
    }

    /**
     * 以下主页面查询用
     * 新医嘱
     */
    @Override
    public UtilResponse queryXyzList(UtilRequest msg) {
        // TODO Auto-generated method stub
        return null;
    }

    /**
     * 护理等级
     */
    @Override
    public UtilResponse queryHldjList(UtilRequest msg) {
        // TODO Auto-generated method stub
        return null;
    }

    /**
     * 病区情况
     */
    @Override
    public UtilResponse queryBrqkList(UtilRequest msg) {
        // TODO Auto-generated method stub
        return null;
    }

    /**
     * 新手术
     */
    @Override
    public UtilResponse queryXssList(UtilRequest msg) {
        // TODO Auto-generated method stub
        return null;
    }

    /**
     * 转出医嘱
     */
    @Override
    public UtilResponse queryZcyzList(UtilRequest msg) {
        // TODO Auto-generated method stub
        return null;
    }

    /**
     * 出院医嘱
     */
    @Override
    public UtilResponse queryCyyzList(UtilRequest msg) {
        // TODO Auto-generated method stub
        return null;
    }

    /**
     * 输液贴
     */
    @Override
    public UtilResponse querySytList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_zxdModel bean = (Hsz_yz_zxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            String sczxldety = (String) msg.getParam().get("sczxldety");
            List<YzzxdHzxxResModel> list = null;
            if (sczxldety.equals("1")) { //领第二天药，输液贴查询明细记录
                list = (List<YzzxdHzxxResModel>) hsz_YzZxdModelMapper.querySytSyjhList(bean);
            } else {
                list = (List<YzzxdHzxxResModel>) hsz_YzZxdModelMapper.querySytList(bean);
            }

            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("输液贴查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱执行记录信息接口【HszHlywYzclCxCspServiceImpl querySytList】发生异常", e);
        }
        return result;
    }

    /*
     * 查询皮试执行记录
     */
    @Override
    public UtilResponse queryPsZxjlList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_zxdModel bean = (Hsz_yz_zxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_yz_zxdModel> list = (List<Hsz_yz_zxdModel>) hsz_YzZxdModelMapper.queryPsZxjlList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医嘱执行记录查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱执行记录信息接口【HszHlywYzclCxCspServiceImpl queryZxjlList】发生异常", e);
        }
        return result;
    }

    @Override
    public UtilResponse queryBqcyList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            YzxxListResModel bean = (YzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<YzxxHzxxResResModel> list = (List<YzxxHzxxResResModel>) hsz_YzclCxModelMapper.queryBqcyList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医嘱执行记录查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱执行记录信息接口【HszHlywYzclCxCspServiceImpl queryHzxxYzxxList】发生异常", e);
        }
        return result;
    }

    @Override
    public UtilResponse queryXgjlList(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            YzxxListResModel bean = (YzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<YzxxHzxxResResModel> list = (List<YzxxHzxxResResModel>) hsz_YzclCxModelMapper.queryXgjlList(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医嘱执行记录查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱执行记录信息接口【HszHlywYzclCxCspServiceImpl queryHzxxYzxxList】发生异常", e);
        }
        return result;
    }

    @Override
    public UtilResponse queryqueryKsbmByYf(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Yf2KsModel bean = (Yf2KsModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Yf2KsModel> list = (List<Yf2KsModel>) hsz_YzclCxModelMapper.queryqueryKsbmByYf(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医嘱执行记录查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱执行记录信息接口【HszHlywYzclCxCspServiceImpl queryqueryKsbmByYf】发生异常", e);
        }
        return result;
    }

    @Override
    public UtilResponse querykc(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Zyb_cwhModel bean = (Zyb_cwhModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Zyb_cwhModel> list = (List<Zyb_cwhModel>) hsz_YzclCxModelMapper.querykc(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询失败:" + e.getCause().getMessage());
            logger.error("调用接口【HszHlywYzclCxCspServiceImpl querykc】发生异常", e);
        }
        return result;
    }

    /**
     * 查询医嘱执行项目表
     */
    @Override
    public UtilResponse quetyYzzxxmb(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            YzxxListResModel bean = (YzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<YzxxHzxxResResModel> list = (List<YzxxHzxxResResModel>) hsz_YzclCxModelMapper.quetyYzzxxmb(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询医嘱执行项目表失败:" + e.getCause().getMessage());
            logger.error("调用查询医嘱执行项目表接口【HszHlywYzclCxCspServiceImpl quetyYzzxxmb】发生异常", e);
        }
        return result;
    }

    /**
     * 查询医嘱变更单
     */
    @Override
    public UtilResponse quetyYzbgd(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            YzxxListResModel bean = (YzxxListResModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<YzxxHzxxResResModel> list = (List<YzxxHzxxResResModel>) hsz_YzclCxModelMapper.quetyYzbgd(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询医嘱变更单失败:" + e.getCause().getMessage());
            logger.error("调用查询医嘱变更单接口【HszHlywYzclCxCspServiceImpl quetyYzbgd】发生异常", e);
        }
        return result;
    }

    /**
     * 更新打印标志
     *
     * @param msg
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public UtilResponse updZxjhDybz(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        int ref = 0;
        try {
            List<YzxxListResModel> arr = (List<YzxxListResModel>) msg.getParam().get("bean");
            List<Hsz_yz_ypzxdModel> ypArr = new ArrayList<Hsz_yz_ypzxdModel>();
            List<Hsz_yz_ylzxdModel> ylArr = new ArrayList<Hsz_yz_ylzxdModel>();
            for (int i = 0; i < arr.size(); i++) {
                YzxxListResModel obj = arr.get(i);
                if ("1".equals(obj.getYpbz())) { // 药品
                    Hsz_yz_ypzxdModel yp = new Hsz_yz_ypzxdModel();
                    yp.setYljgbm(msg.getYljgbm());
                    yp.setYpzxlsh(obj.getZxlsh());
                    yp.setZxdybz(obj.getZxdybz());
                    yp.setDysj(obj.getDysj());
                    ypArr.add(yp);
                }
                if ("0".equals(arr.get(i).getYpbz())) { // 诊疗
                    Hsz_yz_ylzxdModel yl = new Hsz_yz_ylzxdModel();
                    yl.setYljgbm(msg.getYljgbm());
                    yl.setYlzxlsh(obj.getZxlsh());
                    yl.setZxdybz(obj.getZxdybz());
                    yl.setDysj(obj.getDysj());
                    ylArr.add(yl);
                }
            }
            if (ypArr.size() > 0) {
                ref = hsz_YzclCxModelMapper.updYpzxdybz(ypArr);
            }
            if (ylArr.size() > 0) {
                ref = hsz_YzclCxModelMapper.updYlzxdybz(ylArr);
            }

            result.getResResult().put("ref", 1);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("更新打印标志失败:" + e.getCause().getMessage());
            logger.error("更新打印标志接口【HszHlywYzclCxCspServiceImpl updZxjhDybz】发生异常", e);
        }
        return result;
    }

    /**
     * 查询申领单列表
     */
    @Override
    public UtilResponse cxsldlb(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ypzxdModel bean = (Hsz_yz_ypzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_yz_ypzxdModel> list = (List<Hsz_yz_ypzxdModel>) hsz_YzZxdModelMapper.cxsldlb(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询申领单列表失败:" + e.getCause().getMessage());
            logger.error("调用查询申领单列表接口【HszHlywYzclCxCspServiceImpl cxsldlb】发生异常", e);
        }
        return result;
    }

    /**
     * 查询发药明细
     */
    @Override
    public UtilResponse queryFymx(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ypzxdModel bean = (Hsz_yz_ypzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_yz_ypzxdModel> list = (List<Hsz_yz_ypzxdModel>) hsz_YzZxdModelMapper.queryFymx(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询发药明细失败:" + e.getCause().getMessage());
            logger.error("调用查询发药明细接口【HszHlywYzclCxCspServiceImpl queryFymx】发生异常", e);
        }
        return result;
    }

    /**
     * 查询发药汇总
     */
    @Override
    public UtilResponse queryFyhz(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ypzxdModel bean = (Hsz_yz_ypzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Hsz_yz_ypzxdModel> list = (List<Hsz_yz_ypzxdModel>) hsz_YzZxdModelMapper.queryFyhz(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询发药汇总失败:" + e.getCause().getMessage());
            logger.error("调用查询发药汇总接口【HszHlywYzclCxCspServiceImpl queryFyhz】发生异常", e);
        }
        return result;
    }

    /**
     * 更新医嘱变更打印标志
     *
     * @param msg
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public UtilResponse updYzbgDybz(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        int ref = 0;
        try {
            List<YzxxListResModel> arr = (List<YzxxListResModel>) msg.getParam().get("bean");
            List<Zyys_ypyzModel> ypArr = new ArrayList<Zyys_ypyzModel>();
            List<Zyys_ylyzModel> ylArr = new ArrayList<Zyys_ylyzModel>();

            for (int i = 0; i < arr.size(); i++) {
                YzxxListResModel obj = arr.get(i);
                if ("1".equals(obj.getYpbz()) && StringUtils.isNotEmpty(obj.getYwlx())) { // 药品
                    Zyys_ypyzModel yp = new Zyys_ypyzModel();
                    yp.setYljgbm(msg.getYljgbm());
                    yp.setYpyzxh(obj.getYzxh());
                    yp.setMxxh(obj.getMxxh());
                    if ("新开".equals(obj.getYwlx())) {
                        yp.setXkdybz("1");
                    }
                    if ("停止".equals(obj.getYwlx())) {
                        yp.setTzdybz("1");
                    }
                    if ("撤销".equals(obj.getYwlx())) {
                        yp.setCxdybz("1");
                    }

                    yp.setDysj(obj.getDysj());

                    ypArr.add(yp);
                }
                if ("0".equals(arr.get(i).getYpbz()) && StringUtils.isNotEmpty(obj.getYwlx())) { // 诊疗
                    Zyys_ylyzModel yl = new Zyys_ylyzModel();
                    yl.setYljgbm(msg.getYljgbm());
                    yl.setYlyzxh(obj.getYzxh());
                    yl.setMxxh(obj.getMxxh());
                    if ("新开".equals(obj.getYwlx())) {
                        yl.setXkdybz("1");
                    }
                    if ("停止".equals(obj.getYwlx())) {
                        yl.setTzdybz("1");
                    }
                    if ("撤销".equals(obj.getYwlx())) {
                        yl.setCxdybz("1");
                    }

                    yl.setDysj(obj.getDysj());
                    ylArr.add(yl);
                }
            }
            if (ypArr.size() > 0) {
                ref = hsz_YzclCxModelMapper.updYzbgYpdybz(ypArr);
            }
            if (ylArr.size() > 0) {
                ref = hsz_YzclCxModelMapper.updYzbgYldybz(ylArr);
            }

            result.getResResult().put("ref", 1);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("更新打印标志失败:" + e.getCause().getMessage());
            logger.error("更新打印标志接口【HszHlywYzclCxCspServiceImpl updZxjhDybz】发生异常", e);
        }
        return result;
    }

    @Override
    public UtilResponse queryYlyzZxd(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_zxdModel bean = (Hsz_yz_zxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            String newZxd = bean.getNewZxd();
            List<YzzxdHzxxResModel> list = null;
            if ("1".equals(newZxd)) {
                list = hsz_YzZxdModelMapper.queryNewZxd(bean);
            } else {
                list = hsz_YzZxdModelMapper.queryYlyzZxd(bean);
            }
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("医嘱执行记录查询失败:" + e.getCause().getMessage());
            logger.error("调用医嘱执行记录信息接口【HszHlywYzclCxCspServiceImpl queryYlyzZxd】发生异常", e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UtilResponse updateYpyzZxd(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ypzxdModel bean = (Hsz_yz_ypzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());

            String[] zxdids = bean.getYpzxlsh().split(",");
            List<Hsz_yz_ypzxdModel> list = new ArrayList<>();
            for (int i = 0; i < zxdids.length; i++) {
                if (zxdids[i] != null && !"".equals(zxdids[i])) {
                    Hsz_yz_ypzxdModel tpbean = new Hsz_yz_ypzxdModel();
                    tpbean.setYpzxlsh(zxdids[i]);
                    tpbean.setSjzxhs(bean.getSjzxhs());
                    tpbean.setSjzxhsxm(bean.getSjzxhsxm());
                    tpbean.setSjzxsj(bean.getSjzxsj());
                    tpbean.setYljgbm(msg.getYljgbm());
                    list.add(tpbean);


                    Hsz_yz_ypzxdModel fyypzxd = hsz_YzZxdZxModelMapper.selectYpzxd(tpbean);

                    if (fyypzxd != null && fyypzxd.getZyh() != null && !"".equals(fyypzxd.getZyh()) && fyypzxd.getXhid() != null && !"".equals(fyypzxd.getXhid())) {

                        String xhid = fyypzxd.getXhid();

                        fyypzxd.setXhid(clxhid(fyypzxd.getXhid()));

                        fyypzxd.setSjzxhs(tpbean.getSjzxhs());
                        fyypzxd.setSjzxhsxm(tpbean.getSjzxhsxm());
                        fyypzxd.setSjzxsj(tpbean.getSjzxsj());
                        fyypzxd.setYljgbm(tpbean.getYljgbm());
                        hsz_YzZxdZxModelMapper.updateYzHszYpyzZxhs(fyypzxd);
                    }

                }

            }
            if (list != null && list.size() > 0) {
                int i = hsz_YzZxdZxModelMapper.updateHszyzZxhs(list);
                if (i != 0) {
                    result.setResultMsg("更新成功");
                } else {
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg("更新失败:");
                }
                result.getResResult().put("ref", i);
            } else {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("更新失败:");
                result.getResResult().put("ref", 0);
            }
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("更新失败:" + e.getCause().getMessage());
            logger.error("更新失败【HszHlywYzclCxCspServiceImpl queryYlyzZxd】发生异常", e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UtilResponse updateYlyzZxd(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Hsz_yz_ylzxdModel bean = (Hsz_yz_ylzxdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            int count = 0;
            String[] zxdids = bean.getYlzxlsh().split(",");
            for (int i = 0; i < zxdids.length; i++) {
                if (zxdids[i] != null && !"".equals(zxdids[i])) {
                    Hsz_yz_ylzxdModel tpbean = new Hsz_yz_ylzxdModel();
                    tpbean.setYlzxlsh(zxdids[i]);
                    tpbean.setSjzxhs(bean.getSjzxhs());
                    tpbean.setSjzxhsxm(bean.getSjzxhsxm());
                    tpbean.setSjzxsj(bean.getSjzxsj());
                    tpbean.setYljgbm(msg.getYljgbm());
                    count += hsz_YzZxdZxModelMapper.updateHszYlyzZxhs(tpbean);


                    Hsz_yz_ylzxdModel fyylzxd = hsz_YzZxdZxModelMapper.selectYlzxd(tpbean);

                    if (fyylzxd != null && fyylzxd.getZyh() != null && !"".equals(fyylzxd.getZyh()) && fyylzxd.getXhid() != null && !"".equals(fyylzxd.getXhid())) {

                        fyylzxd.setKsbm(bean.getKsbm());


                        hsz_YzZxdZxModelMapper.updateYlfy(fyylzxd);


                        String xhid = fyylzxd.getXhid();

                        fyylzxd.setXhid(clxhid(fyylzxd.getXhid()));

                        fyylzxd.setSjzxhs(tpbean.getSjzxhs());
                        fyylzxd.setSjzxhsxm(tpbean.getSjzxhsxm());
                        fyylzxd.setSjzxsj(tpbean.getSjzxsj());
                        fyylzxd.setYljgbm(tpbean.getYljgbm());

                        hsz_YzZxdZxModelMapper.updateYzHszYlyzZxhs(fyylzxd);


                    }


                }

            }


            if (count != 0) {
                result.setResultMsg("更新成功");
            } else {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("更新失败:");
            }
            result.getResResult().put("ref", count);
        } catch (Exception e) {
            e.printStackTrace();
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("更新失败:" + e.getCause().getMessage());
            logger.error("更新失败【HszHlywYzclCxCspServiceImpl queryYlyzZxd】发生异常", e);
        }
        return result;
    }

    public static String clxhid(String xhid) {


        if (xhid.indexOf("_") != -1) {
            xhid = xhid.substring(0, xhid.indexOf("_"));
            return xhid;
        } else {
            return xhid;
        }
    }


    public static void main(String[] args) {
        System.out.println(clxhid("yp202112290000011_1"));
    }
}
