package com.supx.csp.lis.cgjy.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.csp.api.lis.cgjy.service.ILisCgjyCspService;
import com.supx.csp.api.lis.xtwh.pojo.LisJyxmModel;
import com.supx.csp.api.lis.ybgl.pojo.*;
import com.supx.csp.api.lis.ybgl.pojo.*;
import com.supx.csp.api.pubfun.pojo.getxh.GetXhidMsgModel;
import com.supx.csp.api.pubfun.pojo.getxh.GetXhidResModel;
import com.supx.csp.api.pubfun.service.IPubFunCspService;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.comm.pojo.UserInfoModel;
import com.supx.csp.lis.ybgl.dao.Lis_jydj_mx_ybglModelMapper;
import com.supx.csp.lis.ybgl.dao.Lis_jydj_ybglModelMapper;
import com.supx.csp.lis.cgjy.dao.LisCgjyModelMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 常规检验实现类
 * <AUTHOR>
 *
 */
@Service
@DubboService
public class LisCgjyCspServiceImpl implements ILisCgjyCspService {

	private final Logger logger = LoggerFactory.getLogger(LisCgjyCspServiceImpl.class);

	@Autowired
	private LisCgjyModelMapper lisCgjyModelMapper;

	@Autowired
	private Lis_jydj_mx_ybglModelMapper lis_jydj_mx_ybglModelMapper;

	@Autowired
	private IPubFunCspService iPubFunCspService;

	@Autowired
	private Lis_jydj_ybglModelMapper lis_jydjModelMapper;

	/**
	 * 查询所有检验登记信息
	 */
	@Override
	public UtilResponse queryAllJydj(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jydjModel bean = (Lis_jydjModel) msg.getParam().get("cgjyBean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());
			List<Lis_jydjModel> list = lisCgjyModelMapper.queryAllJydj(bean);
			PageInfo<Lis_jydjModel> pageInfo=new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("常规检验查询列表失败:" + ex.getCause().getMessage());
			logger.error("调用常规检验查询列表接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}
	/**
	 *查询检验登记详情和明细详情
	 */
	@Override
	public UtilResponse queryJydjDetails(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jydjModel bean = (Lis_jydjModel) msg.getParam().get("cgjyBean");
			bean.setYljgbm(msg.getYljgbm());
			List<Lis_jydjModel> list = (List<Lis_jydjModel>) lisCgjyModelMapper.queryJydjDetails(bean);
			result.getResResult().put("list", list);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("查询检验登记详情和明细详情列表失败:" + ex.getCause().getMessage());
			logger.error("调用查询检验登记详情和明细详情接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}
	/**
	 *查询需要打印的数据
	 */
	@SuppressWarnings("unchecked")
	@Override
	public UtilResponse queryJydjPrintData(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			List<Lis_jydjModel> pList= (List<Lis_jydjModel>) msg.getParam().get("list");
			for (int i = 0; i < pList.size(); i++) {
				pList.get(i).setYljgbm(msg.getYljgbm());
			}
			List<Lis_jydjModel> resD = (List<Lis_jydjModel>) lisCgjyModelMapper.queryJydjPrintData(pList);
			result.getResResult().put("list", resD);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("查询需要打印的数据失败:" + ex.getCause().getMessage());
			logger.error("调用查询需要打印的数据接口【LisCgjyCspServiceImpl queryJydjPrintData】发生异常", ex);
		}
		return result;
	}

	/**
	 *输入样本标号时 查询出检验结果
	 */
	@Override
	public UtilResponse queryJydjDetailsByInterfaceData(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jydjModel bean = (Lis_jydjModel) msg.getParam().get("cgjyBean");
			bean.setYljgbm(msg.getYljgbm());
			List<Lis_jydjModel> list = (List<Lis_jydjModel>) lisCgjyModelMapper.queryJydjDetailsByInterfaceData(bean);
			result.getResResult().put("list", list);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("输入样本标号时 查询出检验结果失败:" + ex.getCause().getMessage());
			logger.error("调用输入样本标号时 查询出检验结果接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}

	/**
	 * 根据病案号查询出上次检验结果
	 */
	public UtilResponse queryScjg(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jydjModel bean = (Lis_jydjModel) msg.getParam().get("cgjyBean");
			bean.setYljgbm(msg.getYljgbm());
			List<Lis_jydjModel> list=new ArrayList<>();
			if (StringUtils.isNotEmpty(bean.getBah())) {
				list = (List<Lis_jydjModel>) lisCgjyModelMapper.queryScjg(bean);
			}
			result.getResResult().put("list", list);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("根据病案号查询出上次检验结果失败:" + ex.getCause().getMessage());
			logger.error("调用根据病案号查询出上次检验结果接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}
	/**
	 * 查询最大样本号
	 */
	@SuppressWarnings("null")
	public UtilResponse queryMaxYbh(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jydjModel bean = (Lis_jydjModel) msg.getParam().get("cgjyBean");

			//查询出当前设备
			Lis_JysbModel sbp=new Lis_JysbModel();
			sbp.setYljgbm(msg.getYljgbm());
			sbp.setSbbm(bean.getZxsb());
			Lis_JysbModel sb=lis_jydjModelMapper.queryJysb(sbp).get(0);
			String ybhscfs=sb.getYbhscfs();//0:每天变化  1：连续编号
			String ybhws=sb.getYbhws();
			if ("0".equals(ybhscfs)) {
				bean.setYljgbm(msg.getYljgbm());
				bean.setDjrq(new Date());
				Lis_jydjModel res=lisCgjyModelMapper.queryMaxYbh(bean);
				Integer i=1;
				if (null != res && null != res.getBbbh()) {
					i=Integer.parseInt(res.getBbbh())+1;
				}
				result.getResResult().put("maxYbh", i);
			}
			if ("1".equals(ybhscfs)) {
				bean.setYljgbm(msg.getYljgbm());

				Lis_jydjModel res=lisCgjyModelMapper.queryMaxYbh(bean);
				Integer i=1;
				if (null != res && null != res.getBbbh()) {
					i=Integer.parseInt(res.getBbbh())+1;
				}
				result.getResResult().put("maxYbh", i);
			}



		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("查询最大样本号失败:" + ex.getCause().getMessage());
			logger.error("调用查询最大样本号接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}

	/**
	 * 组装实体类
	 * @param msg
	 * @param jydj
	 * @return
	 */
	public Lis_jydjModel zz(UtilRequest msg,Lis_jydjModel jydj){
		UserInfoModel user = JSONObject.parseObject(JSONObject.toJSONString(msg.getUserinfo().get("userinfo")),
				UserInfoModel.class);
		jydj.setCodePrint("0");
		jydj.setCybz("0");
		jydj.setCzy(user.getCzybm());
		jydj.setCzyxm(user.getCzyxm());
		jydj.setDjrq(new Date());
		jydj.setYljgbm(msg.getYljgbm());
		if (StringUtils.isEmpty(jydj.getLx())) {
			jydj.setLx("0");
		}
		if (jydj.getFyje()==null) {
			jydj.setFyje(BigDecimal.ZERO);
		}
		if( StringUtils.isNotEmpty(jydj.getYblx()) &&"Q".equals(jydj.getYblx())){
			jydj.setJzbz("1");
		}else{
			jydj.setJzbz("0");
		}
		jydj.setZxbz("0");
		jydj.setSqdh(jydj.getJyxh());
		if (StringUtils.isEmpty(jydj.getZklx())) {
			jydj.setZklx("0");
		}
		if("0".equals(jydj.getLx())){
			jydj.setLy("1");
		}
		if("1".equals(jydj.getLx())){
			jydj.setLy("3");
		}
		jydj.setKfbz("0");
		jydj.setFfbz("0");
		jydj.setWswlx("0");
		jydj.setYsckbz("0");
		jydj.setZzdybz("0");
		jydj.setYbjsbz("0");

		//置为核收状态
		jydj.setYbhsbz("1");
		jydj.setYbhsr(user.getCzybm());
		jydj.setYbhsrxm(user.getCzyxm());
		jydj.setYbhsrq(new Date());
		//查询生成方式scfs
		LisJyxmModel req=new LisJyxmModel();
		req.setBm(jydj.getJyxm());
		req.setYljgbm(user.getYljgbm());
		LisJyxmModel rep=lisCgjyModelMapper.selectJyxmByXmbm(req);
		jydj.setScfs(rep.getScfs());
		return jydj;
	}

	/**
	 * 新增登记并保存
	 * @throws Exception
	 */
	@Transactional(rollbackFor = Exception.class)
	public UtilResponse xzdj(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jydjModel jydj = (Lis_jydjModel) msg.getParam().get("pd");
			List<Lis_jydj_mxModel> jydjMxs=jydj.getJydjmxList();
			jydj.setYljgbm(msg.getYljgbm());
			if (StringUtils.isNotEmpty(jydj.getJyxh()) && StringUtils.isEmpty(jydj.getXzbz())) {//新增标志为空时说明是更新
				//检验序号不为空时，说明是已存在记录，此时执行更新
				jydj.setZxrq(new Date());
				lisCgjyModelMapper.update(jydj);
			}else{
				//否则新增
				// 设置检验序号  待确定检验序号生成方式
				GetXhidMsgModel hqxh = new GetXhidMsgModel();
				hqxh.setScfs("3");
				hqxh.setCslx("JYXH");
				msg.getParam().put("bean", hqxh);
				UtilResponse res_cfh = iPubFunCspService.GetIdLock(msg);
				GetXhidResModel brbean = (GetXhidResModel) res_cfh.getResResult().get("bean");
				jydj.setJyxh(brbean.getDqxh());
				jydj=zz(msg,jydj);
				lisCgjyModelMapper.insert(jydj);
			}
			//需要更新的检验登记明细List
			List<Lis_jydj_mxModel> updateList = new ArrayList<>();
			//需要新增的检验登记明细List
			List<Lis_jydj_mxModel> insertList = new ArrayList<>();
			int updateref = 0;
			if (jydjMxs.size()>0) {
				for (int i = 0; i < jydjMxs.size(); i++) {
					if (StringUtils.isNotEmpty(jydjMxs.get(i).getJyxh()) && StringUtils.isNotEmpty(jydjMxs.get(i).getZbxm())) {
						//检验序号不为空则更新
						updateList.add(jydjMxs.get(i));
					}else{
						//否则新增
						if (StringUtils.isNotEmpty(jydjMxs.get(i).getZbxm())) {
							jydjMxs.get(i).setYljgbm(msg.getYljgbm());
							jydjMxs.get(i).setJyxh(jydj.getJyxh());
							insertList.add(jydjMxs.get(i));
						}

					}
				}
				if (updateList.size()>0) {
					updateref+=lis_jydj_mx_ybglModelMapper.updateBatch(updateList);
				}
				if (insertList.size()>0) {
					updateref+=lis_jydj_mx_ybglModelMapper.insertBatch(insertList);
				}
			}
			result.getResResult().put("ref", updateref);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("新增登记并保存失败:" + ex.getCause().getMessage());
			logger.error("调用新增登记并保存接口【LisCgjyCspServiceImpl insert】发生异常", ex);
			throw new Exception();
		}
		return result;
	}


	/**
	 * 新增登记
	 * @throws Exception
	 */
	@Transactional(rollbackFor = Exception.class)
	public UtilResponse xz(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try {
			int updateref = 0;
			Lis_jydjModel jydj = (Lis_jydjModel) msg.getParam().get("pd");
			jydj.setYljgbm(msg.getYljgbm());
			if (StringUtils.isNotEmpty(jydj.getJyxh())) {
				//检验序号不为空时，说明是已存在记录，此时执行更新
				lisCgjyModelMapper.update(jydj);
			}else{
				//否则新增
				// 设置检验序号  待确定检验序号生成方式
				GetXhidMsgModel hqxh = new GetXhidMsgModel();
				hqxh.setScfs("3");
				hqxh.setCslx("JYXH");
				msg.getParam().put("bean", hqxh);
				UtilResponse res_cfh = iPubFunCspService.GetIdLock(msg);
				GetXhidResModel brbean = (GetXhidResModel) res_cfh.getResResult().get("bean");
				jydj.setJyxh(brbean.getDqxh());
				jydj=zz(msg,jydj);
				lisCgjyModelMapper.insert(jydj);

				//根据jyxm添加lis_jydj_mx表内容
				if (StringUtils.isNotEmpty(jydj.getJyxm())) {
					if (StringUtils.isEmpty(jydj.getScfs())|| "0".equals(jydj.getScfs())) {
						// 查询出检验项目对应的所有指标编码 Lis_Jyxm bm 为
						// jydj.get(i).getJyxm()
						Lis_xmzbModel xmzbParm = new Lis_xmzbModel();
						xmzbParm.setYljgbm(msg.getYljgbm());
						xmzbParm.setXmbm(jydj.getJyxm());
						List<Lis_xmzbModel> xmzbs = lis_jydjModelMapper.queryXmzb(xmzbParm);
						// 循环获取指标参考值
						for (int j = 0; j < xmzbs.size(); j++) {
							Lis_zbbmModel zbbmckz = lis_jydjModelMapper.queryZbbmCkz(xmzbs.get(j));
							Lis_jydj_mxModel jydjmx = new Lis_jydj_mxModel();
							jydjmx.setYljgbm(msg.getYljgbm());
							jydjmx.setJyxh(jydj.getJyxh());
							jydjmx.setZbxm(xmzbs.get(j).getZbbm());// 指标编码
							jydjmx.setZbxmmc(zbbmckz.getZwmc());
							jydjmx.setYwmc(zbbmckz.getYwmc());
							jydjmx.setDw(zbbmckz.getDw());
							jydjmx.setXh(zbbmckz.getXh());
							jydjmx.setZkbz("0");
							// 经过处理获取LIS_JYDJ_MX最大值，最小值，参考值后拼装检验登记明细实体类
							jydjmx = f_get_ckz(zbbmckz, jydj, jydjmx);
							// 存储检验登记明细表
							lis_jydj_mx_ybglModelMapper.insertSelective(jydjmx);
						}

					}
				}
			}
			result.getResResult().put("ref", updateref);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("新增登记失败:" + ex.getCause().getMessage());
			logger.error("调用新增登记接口【LisCgjyCspServiceImpl insert】发生异常", ex);
			throw new Exception();
		}
		return result;
	}
	/**
	 * 增加项目时先查询项目指标
	 */
	public UtilResponse queryXmzb(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jyxmModel bean = (Lis_jyxmModel) msg.getParam().get("jyxmBean");
			bean.setYljgbm(msg.getYljgbm());
			List<Lis_jyxmModel> list=lisCgjyModelMapper.queryXmzb(bean);
			result.getResResult().put("list", list);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("查询项目指标列表失败:" + ex.getCause().getMessage());
			logger.error("调用项目指标列表接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}

	/**
	 * 查询所有指标列表
	 */
	@Override
	public UtilResponse queryJyzb(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_zbbmModel bean = (Lis_zbbmModel) msg.getParam().get("jyzbBean");
			bean.setYljgbm(msg.getYljgbm());
			List<Lis_zbbmModel> list=lisCgjyModelMapper.queryJyzb(bean);
			result.getResResult().put("list", list);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("查询所有指标列表失败:" + ex.getCause().getMessage());
			logger.error("调用查询所有指标列表接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}


	/**
	 * 批量调整时 根据条件查询出明细列表
	 */
	@Override
	public UtilResponse queryPltz_mx(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jydj_mxModel bean = (Lis_jydj_mxModel) msg.getParam().get("jydjmxBean");
			bean.setYljgbm(msg.getYljgbm());
			List<Lis_jydj_mxModel> list=lisCgjyModelMapper.queryPltz_mx(bean);
			result.getResResult().put("list", list);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("批量调整时 根据条件查询出明细列表失败:" + ex.getCause().getMessage());
			logger.error("调用批量调整时 根据条件查询出明细列表接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}
	/**
	 * 批量调整 确定时保存
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@Transactional(rollbackFor = Exception.class)
	public UtilResponse updatePltzMx(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try {
			int updateref = 0;
			List<Lis_jydj_mxModel> mx = (List<Lis_jydj_mxModel>) msg.getParam().get("pd");
			if(mx.size()>0){
				for (Lis_jydj_mxModel m :mx) {
					m.setYljgbm(msg.getYljgbm());
				}

				updateref=lis_jydj_mx_ybglModelMapper.updateBatch(mx);
			}
			result.getResResult().put("ref", updateref);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("批量调整 确定时保存失败:" + ex.getCause().getMessage());
			logger.error("调用批量调整 确定时保存接口【LisCgjyCspServiceImpl insert】发生异常", ex);
			throw new Exception();
		}
		return result;
	}
	/**
	 * 删除指标项目明细
	 */
	@SuppressWarnings("unchecked")
	@Transactional(rollbackFor = Exception.class)
	public UtilResponse deleteMx(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try {
			int deleteref = 0;
			List<Lis_jydj_mxModel> mx = (List<Lis_jydj_mxModel>) msg.getParam().get("list");
			if(mx.size()>0){
				for (Lis_jydj_mxModel m :mx) {
					m.setYljgbm(msg.getYljgbm());
				}

				deleteref=lis_jydj_mx_ybglModelMapper.deleteBatch(mx);
			}
			result.getResResult().put("ref", deleteref);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("常规检验 删除指标项目明细失败:" + ex.getCause().getMessage());
			logger.error("调用常规检验 删除指标项目明细接口【LisCgjyCspServiceImpl deleteMx】发生异常", ex);
			throw new Exception();
		}
		return result;
	}



	/**
	 * 根据指标编码获取该指标参考值
	 */
	public Lis_jydj_mxModel f_get_ckz(Lis_zbbmModel zbbmckz, Lis_jydjModel jydj, Lis_jydj_mxModel jydjmx) {
		String cklx = zbbmckz.getCklx();// 参考类型 0-未明确,1-通用,2-性别,3-年龄，4-年龄+性别
		String sjlx = zbbmckz.getSjlx();// 数据类型 1-数值,2-文本,3-选择,4-计算

		String xb = jydj.getXb();// 性别
		if (jydj.getNl()==null ||StringUtils.isEmpty(jydj.getNldw())) {
			cklx="1";
		}
		BigDecimal nl = new BigDecimal(jydj.getNl());// 年龄
		Integer nldw = Integer.parseInt(jydj.getNldw());// 年龄单位

		BigDecimal N_MIN = null;
		BigDecimal N_MAX = null;
		String CKZ_T = null;

		switch (cklx) {
		case "0":// 未明确

			break;
		case "1":// 通用
			if ("1".equals(sjlx) || "4".equals(sjlx)) {
				N_MIN = zbbmckz.getLis_ckzModel().getAllN();
				N_MAX = zbbmckz.getLis_ckzModel().getAllNH();
				CKZ_T = N_MIN + "-" + N_MAX;
			} else {
				N_MIN = BigDecimal.ZERO;
				N_MAX = BigDecimal.ZERO;
				CKZ_T = zbbmckz.getLis_ckzModel().getAllT();
			}
			break;
		case "2":// 性别
			if ("1".equals(xb)) {// 男性
				if ("1".equals(sjlx) || "4".equals(sjlx)) {
					N_MIN = zbbmckz.getLis_ckzModel().getManN();
					N_MAX = zbbmckz.getLis_ckzModel().getManNH();
					CKZ_T = N_MIN + "-" + N_MAX;
				} else {
					N_MIN = BigDecimal.ZERO;
					N_MAX = BigDecimal.ZERO;
					CKZ_T = zbbmckz.getLis_ckzModel().getManT();
				}
			} else {// 女性
				if ("1".equals(sjlx) || "4".equals(sjlx)) {
					N_MIN = zbbmckz.getLis_ckzModel().getWomanN();
					N_MAX = zbbmckz.getLis_ckzModel().getWomanNH();
					CKZ_T = N_MIN + "-" + N_MAX;
				} else {
					N_MIN = BigDecimal.ZERO;
					N_MAX = BigDecimal.ZERO;
					CKZ_T = zbbmckz.getLis_ckzModel().getWomanT();
				}
			}
			break;
		case "3":// 年龄
			Lis_ckz_nlModel ckznl = new Lis_ckz_nlModel();
			ckznl.setYljgbm(zbbmckz.getYljgbm());
			ckznl.setZbbm(zbbmckz.getZbbm());
			List<Lis_ckz_nlModel> pd = lis_jydjModelMapper.queryCkznl(ckznl);
			for (int i = 0; i < pd.size(); i++) {
				BigDecimal min = pd.get(i).getMin();// 最小值
				BigDecimal max = pd.get(i).getMax();// 最大值
				Integer nldw1 = Integer.parseInt(pd.get(i).getNldw1());
				Integer nldw2 = Integer.parseInt(pd.get(i).getNldw2());
				BigDecimal nl1 = pd.get(i).getNl1();
				BigDecimal nl2 = pd.get(i).getNl2();
				if (nldw1 == nldw2 && nldw == nldw1) {
					if (nl.compareTo(nl1) >= 0 && nl.compareTo(nl2) <= 0) {
						N_MIN = min;
						N_MAX = max;
						CKZ_T = N_MIN + "-" + N_MAX;
					}
				} else if (nldw1 < nldw2) {
					if (nldw > nldw1) {
						if (nldw < nldw2) {
							N_MIN = min;
							N_MAX = max;
							CKZ_T = N_MIN + "-" + N_MAX;
						}
					}

				}

			}
			break;
		case "4":// 年龄+性别
			Lis_ckz_nlModel nlxb = new Lis_ckz_nlModel();
			nlxb.setYljgbm(zbbmckz.getYljgbm());
			nlxb.setZbbm(zbbmckz.getZbbm());
			List<Lis_ckz_nlModel> ckz = lis_jydjModelMapper.queryCkznl(nlxb);
			for (int i = 0; i < ckz.size(); i++) {
				BigDecimal min = ckz.get(i).getMin();// 最小值
				BigDecimal max = ckz.get(i).getMax();// 最大值
				Integer nldw1 = Integer.parseInt(ckz.get(i).getNldw1());
				Integer nldw2 = Integer.parseInt(ckz.get(i).getNldw2());
				BigDecimal nl1 = ckz.get(i).getNl1();
				BigDecimal nl2 = ckz.get(i).getNl2();
				String this_xb=ckz.get(i).getXb();//0：通用 1：男性  2：女性

				if (nldw1 == nldw2 && nldw == nldw1) {
					if (nl.compareTo(nl1) >= 0 && nl.compareTo(nl2) <= 0) {
						if (xb.equals(this_xb)) {
							N_MIN = min;
							N_MAX = max;
							CKZ_T = N_MIN + "-" + N_MAX;
						}

					}
				} else if (nldw1 < nldw2) {
					if (nldw > nldw1) {
						if (nldw < nldw2) {
							if (xb.equals(this_xb)) {
								N_MIN = min;
								N_MAX = max;
								CKZ_T = N_MIN + "-" + N_MAX;
							}
						}
					}

				}

			}
			break;
		}
		jydjmx.setZxz(N_MIN);
		jydjmx.setZdz(N_MAX);
		jydjmx.setCkzT(CKZ_T);
		return jydjmx;

	}
	/**
	 * 根据时间，检验设备，样本编号 查询出当前检查的结果确定指标明细
	 * @param msg
	 * @return
	 */
	public UtilResponse queryZbList4Tdh(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Lis_jydjModel jydj = (Lis_jydjModel) msg.getParam().get("cgjyBean");
			jydj.setYljgbm(msg.getYljgbm());
			//先删除结果选项指标
			lisCgjyModelMapper.deleteJydjmx(jydj);

			List<Lis_jydj_mxModel> list = (List<Lis_jydj_mxModel>) lisCgjyModelMapper.queryZbList4Tdh(jydj);

			for (int j = 0; j < list.size(); j++) {
				Lis_xmzbModel reqXmzb=new Lis_xmzbModel();
				reqXmzb.setZbbm(list.get(j).getZbxm());
				reqXmzb.setYljgbm(msg.getYljgbm());
				Lis_zbbmModel zbbmckz = lis_jydjModelMapper.queryZbbmCkz(reqXmzb);
				list.get(j).setYljgbm(msg.getYljgbm());
				list.get(j).setJyxh(jydj.getJyxh());

				if(! "F".equals(jydj.getYblx())){
					list.get(j).setZkbz("1");
				}else{//质控
					list.get(j).setZkbz("0");
				}
				if("3".equals(zbbmckz.getSjlx())){
					if(list.get(j).getValueN() != null){
						list.get(j).setValueL(list.get(j).getValueN().toString());
					}else{
						list.get(j).setValueL(list.get(j).getValueT());
					}
				}

				// 经过处理获取LIS_JYDJ_MX最大值，最小值，参考值后拼装检验登记明细实体类
				Lis_jydj_mxModel jydjmx = f_get_ckz(zbbmckz, jydj, list.get(j));
				// 存储检验登记明细表
				lis_jydj_mx_ybglModelMapper.insertSelective(jydjmx);
			}
			lisCgjyModelMapper.update(jydj);
			jydj.setJydjmxList(list);
			List<Lis_jydjModel> replist=new ArrayList<>();
			replist.add(jydj);
			result.getResResult().put("list", replist);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("查询结果确定指标列表失败:" + ex.getCause().getMessage());
			logger.error("调用查询结果确定指标接口【LisCgjyCspServiceImpl query】发生异常", ex);
		}
		return result;
	}









}
