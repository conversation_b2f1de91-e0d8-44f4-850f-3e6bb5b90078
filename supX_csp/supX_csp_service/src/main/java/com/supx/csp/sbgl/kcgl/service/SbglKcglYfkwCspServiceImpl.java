package com.supx.csp.sbgl.kcgl.service;

import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.csp.api.pubfun.pojo.getxh.GetMaxBmModel;
import com.supx.csp.api.pubfun.service.IPubFunCspService;
import com.supx.csp.api.sbgl.kcgl.pojo.Sbfb_yfkwModel;
import com.supx.csp.api.sbgl.kcgl.service.ISbfKcglYfkwCspService;
import com.supx.csp.sbgl.kcgl.dao.Sbfb_sbkwModelMapper;
import com.supx.csp.user.service.ServiceInvocationHelper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * @ClassName: YfbKcglYfkwCspServiceImpl
 * @Description: (药房库位数据操作)
 * <AUTHOR> YK
 * @date 2020年9月8日 下午7:39:05
 *
 */
@Service
@DubboService
public class SbglKcglYfkwCspServiceImpl extends ServiceInvocationHelper implements ISbfKcglYfkwCspService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private Sbfb_sbkwModelMapper yfb_yfkwModelMapper;

	@Autowired
	private IPubFunCspService pubFunCspService;

	// 保存库位信息
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse addKw(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		int ref = -1;

		try {
			// 获取参数
			Sbfb_yfkwModel yfkwModel = (Sbfb_yfkwModel) msg.getParam().get("sbkw");

			// 设置医疗机构编码
			yfkwModel.setYljgbm(msg.getYljgbm());
			// 设置库位编码
			if (yfkwModel.getKwbm() == null || "".equals(yfkwModel.getKwbm())) {
				GetMaxBmModel b = new GetMaxBmModel();
				b.setTablename("YFB_YFKW");
				b.setColumnname("KWBM");
				b.setLeninteger(4);
				msg.getParam().put("bean", b);
				UtilResponse res = pubFunCspService.GetMaxBm(msg);
				String maxbm = (String) res.getResResult().get("maxbm");
				yfkwModel.setKwbm(maxbm);

			}

			// 保存库位信息
			ref = yfb_yfkwModelMapper.insertKw(yfkwModel);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("库位信息保存失败,影响的行数：" + ref);
				logger.error("调用库位信息接口【YfbKcglYfkwCspServiceImpl addKw】发生异常");
				throw new Exception(); // 异常自动返回 并且SQL事务回滚
			}
			result.getResResult().put("ref", ref);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息保存失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfkwCspServiceImpl addKw】,影响的行数：" + ref);
			throw ex;
		}
		return result;
	}

	// 修改库位信息
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse modiKw(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		int ref = -1;

		try {
			// 获取参数
			Sbfb_yfkwModel yfkwModel = (Sbfb_yfkwModel) msg.getParam().get("sbkw");

			// 设置医疗机构编码
			yfkwModel.setYljgbm(msg.getYljgbm());

			// 保存库位信息
			ref = yfb_yfkwModelMapper.updateKw(yfkwModel);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("库位信息更新失败,影响的行数：" + ref);
				logger.error("调用库位信息接口【YfbKcglYfkwCspServiceImpl modiKw】发生异常");
				throw new Exception(); // 异常自动返回 并且SQL事务回滚
			}
			result.getResResult().put("ref", ref);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息更新失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfkwCspServiceImpl modiKw】,影响的行数：" + ref);
			throw ex;
		}
		return result;
	}

	// 删除库位信息
	@SuppressWarnings("unchecked")
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse delKw(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		int ref = -1;

		try {
			// 获取参数
			List<Sbfb_yfkwModel> list = (List<Sbfb_yfkwModel>) msg.getParam().get("list");
			// 设置医疗机构编码
			for (int i = 0; i < list.size(); i++) {
				list.get(i).setYljgbm(msg.getYljgbm());
			}

			// 保存库位信息
			ref = yfb_yfkwModelMapper.deleteKw(list);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("库位信息删除失败,影响的行数：" + ref);
				logger.error("调用库位信息接口【YfbKcglYfkwCspServiceImpl modiKw】发生异常");
				throw new Exception(); // 异常自动返回 并且SQL事务回滚
			}
			result.getResResult().put("ref", ref);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息删除失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfkwCspServiceImpl modiKw】,影响的行数：" + ref);
			throw ex;
		}
		return result;
	}

	// 查询库位信息
	@Override
	public UtilResponse selKw(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数
			Sbfb_yfkwModel yfkwModel = (Sbfb_yfkwModel) msg.getParam().get("yfkw");

			// 设置医疗机构编码
			yfkwModel.setYljgbm(msg.getYljgbm());

			// 保存库位信息
			List<Sbfb_yfkwModel> list = yfb_yfkwModelMapper.selectKw(yfkwModel);
			result.getResResult().put("list", list);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息查询失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfkwCspServiceImpl addKw】发生异常");
			throw ex;
		}
		return result;
	}

}
