package com.supx.csp.lis.bggl.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.csp.api.lis.bggl.pojo.Lis_bgwhModel;
import com.supx.csp.api.lis.bggl.pojo.Lis_bgzbModel;
import com.supx.csp.api.lis.bggl.pojo.Lis_brfyModel;
import com.supx.csp.api.lis.bggl.service.ILis_bgwhCspService;
import com.supx.csp.lis.bggl.dao.Lis_bgwhModelMapper;
import com.supx.csp.user.service.ServiceInvocationHelper;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.comm.pojo.UserInfoModel;
import org.springframework.stereotype.Service;

@Service
@DubboService
public class Lis_bgwhCspserviceImpl extends ServiceInvocationHelper implements ILis_bgwhCspService {

    private final Logger logger = LoggerFactory.getLogger(Lis_bgwhCspserviceImpl.class);

    @Autowired
    private Lis_bgwhModelMapper bg_jybgModelMapper;

    /**
     * 查询报告单
     */
    @Override
    public UtilResponse queryBg(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Lis_bgwhModel bean = (Lis_bgwhModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            PageHelper.startPage(bean.getPage(), bean.getRows());
            List<Lis_bgwhModel> list = bg_jybgModelMapper.queryBg(bean);
            PageInfo<Lis_bgwhModel> pageInfo = new PageInfo<>(list);
            result.getResResult().put("pageInfo", pageInfo);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("Lis查询检验报告失败:" + e.getCause().getMessage());
            logger.error("调用查询检验报告信息接口【Bg_jybgCspserviceImpl queryBg】发生异常", e);
        }
        return result;
    }

    /**
     * 查询病人报告-指标明细
     */
    @Override
    public UtilResponse queryBgmx(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Lis_bgwhModel bean = (Lis_bgwhModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Lis_bgzbModel> list = bg_jybgModelMapper.queryBgmx(bean);
            //设置指标值的结果值
            if (list != null && list.size() != 0) {
                setBzValue(list);
            }
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("Lis查询病人报告明细失败:" + e.getCause().getMessage());
            logger.error("调用查询病人报告明细接口【Bg_jybgCspserviceImpl queryBgmx】发生异常", e);
        }
        return result;
    }


    /**
     * 指标变化趋势
     */
    @Override
    public UtilResponse queryHistory(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            //病案号
            Lis_bgwhModel bean = (Lis_bgwhModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Lis_bgwhModel> jyxhList = bg_jybgModelMapper.queryJyxhByBah(bean);
            //时间，指标map
            Map<String, List<Lis_bgzbModel>> map = null;
            List<Lis_bgzbModel> zbList = null;
            if (jyxhList != null && jyxhList.size() != 0) {
                map = new HashMap<>();
                for (Lis_bgwhModel bg : jyxhList) {
                    zbList = new ArrayList<>();
                    bean.setJyxh(bg.getJyxh());
                    zbList = bg_jybgModelMapper.queryBgmx(bean);
                    setBzValue(zbList);
                    map.put(DateFormatUtils.format(bg.getShrq(), "yyyy/MM/dd"), zbList);
                }
            }
            //包装成前台格式
            Map<String, Object> maps = createHistory(map);
            result.getResResult().put("list", maps);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告查询指标变化趋势失败:" + e.getCause().getMessage());
            logger.error("调用检验报告查询指标变化趋势接口【Bg_jybgCspserviceImpl queryHistory】发生异常", e);
        }
        return result;
    }

    /**
     * 查询检验项目
     */
    @Override
    public UtilResponse queryJyxm(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Lis_bgwhModel bean = (Lis_bgwhModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Lis_bgwhModel> list = bg_jybgModelMapper.queryJyxm(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告查询检验项目失败:" + e.getCause().getMessage());
            logger.error("调用检验报告查询检验项目接口【Bg_jybgCspserviceImpl queryJyxm】发生异常", e);
        }
        return result;
    }

    /**
     * 查询检验设备
     */
    @Override
    public UtilResponse queryJysb(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Lis_bgwhModel bean = (Lis_bgwhModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Lis_bgwhModel> list = bg_jybgModelMapper.queryJysb(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告查询设备名称失败:" + e.getCause().getMessage());
            logger.error("调用检验报告查询设备接口【Bg_jybgCspserviceImpl queryJysb】发生异常", e);
        }
        return result;
    }

    /**
     * 查询病人费用
     */
    @Override
    public UtilResponse queryFyDetail(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Lis_bgwhModel bean = (Lis_bgwhModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Lis_brfyModel> list = bg_jybgModelMapper.queryBgFy(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告查询病人费用失败:" + e.getCause().getMessage());
            logger.error("调用检验报告查询病人费用接口【Bg_jybgCspserviceImpl queryFyDetail】发生异常", e);
        }
        return result;
    }

    /**
     * 合并指标-查询指标值
     */
    @Override
    public UtilResponse queryZb(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Lis_bgwhModel bean = (Lis_bgwhModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            Lis_bgzbModel bg_zbModel = bg_jybgModelMapper.queryZb(bean);
            if (bg_zbModel != null) {
                setBzValue(bg_zbModel);
                result.getResResult().put("zb", bg_zbModel.getJcjg());
            }
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告查询样本待合并指标值失败:" + e.getCause().getMessage());
            logger.error("调用检验报告查询样本待合并指标值接口【Bg_jybgCspserviceImpl queryZb】发生异常", e);
        }
        return result;
    }

    /**
     * 合并指标-更新指标值
     */
    @SuppressWarnings("unchecked")
    @Override
    public UtilResponse updateZb(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            //设置更新的列
            List<Lis_bgzbModel> beans = (List<Lis_bgzbModel>) msg.getParam().get("beans");
            for (Lis_bgzbModel bg_zbModel : beans) {
                bg_zbModel.setYljgbm(msg.getYljgbm());
            }
            //setBzValue(beans);
            //批量更新
            int ref = bg_jybgModelMapper.updateZb(beans);
            if (ref != -1) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("检验报告样本合并指标值失败");
                logger.error("调用检验报告样本待合并指标值接口【Bg_jybgCspserviceImpl updateZb】失败");
            }
            result.getResResult().put("ref", ref);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告样本合并指标值失败:" + e.getCause().getMessage());
            logger.error("调用检验报告样本待合并指标值接口【Bg_jybgCspserviceImpl updateZb】发生异常", e);
        }
        return result;
    }

    /**
     * 修改检验报告日期 发放
     */
    @SuppressWarnings("unchecked")
    @Override
    public UtilResponse updateBgRq(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            //设置更新的列
            List<Lis_bgwhModel> beans = (List<Lis_bgwhModel>) msg.getParam().get("beans");
            for (Lis_bgwhModel bean : beans) {
                bean.setYljgbm(msg.getYljgbm());
            }
            //批量更新
            int ref = bg_jybgModelMapper.updateJydj(beans);
            if (ref != -1) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("检验报告修改失败");
                logger.error("调用检验报告修改接口【Bg_jybgCspserviceImpl updateBgRq】失败");
            }
            result.getResResult().put("ref", ref);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告修改失败:" + e.getCause().getMessage());
            logger.error("调用检验报告修改接口【Bg_jybgCspserviceImpl updateBgRq】发生异常", e);
        }
        return result;
    }


    @SuppressWarnings("unchecked")
    @Override
    public UtilResponse checkBg(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            //设置更新的列
            List<Lis_bgwhModel> beans = (List<Lis_bgwhModel>) msg.getParam().get("beans");
            UserInfoModel userInfo = (UserInfoModel) msg.getUserinfo().get("userInfo");
            for (Lis_bgwhModel bean : beans) {
                bean.setYljgbm(msg.getYljgbm());
                //审核日期
                bean.setShrq(new Date());
                //审核人员
                bean.setShry(userInfo.getCzybm());
                bean.setShryxm(userInfo.getCzyxm());
            }
            //批量更新
            int ref = bg_jybgModelMapper.checkBg(beans);
            if (ref != -1) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("检验报告审核失败");
                logger.error("调用检验报告审核接口【Bg_jybgCspserviceImpl checkBg】失败");
            }
            result.getResResult().put("ref", ref);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告修改失败:" + e.getCause().getMessage());
            logger.error("调用检验报告修改接口【Bg_jybgCspserviceImpl checkBg】发生异常", e);
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    @Override
    public UtilResponse cancelCheck(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            //设置更新的列
            List<Lis_bgwhModel> beans = (List<Lis_bgwhModel>) msg.getParam().get("beans");
            for (Lis_bgwhModel bean : beans) {
                bean.setYljgbm(msg.getYljgbm());
            }
            //批量更新
            int ref = bg_jybgModelMapper.cancelCheck(beans);
            if (ref != -1) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("检验报告取消审核失败");
                logger.error("调用检验报告取消审核接口【Bg_jybgCspserviceImpl cancelCheck】失败");
            }
            result.getResResult().put("ref", ref);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("检验报告取消审核失败:" + e.getCause().getMessage());
            logger.error("调用检验报告取消审核接口【Bg_jybgCspserviceImpl cancelCheck】发生异常", e);
        }
        return result;
    }

    /*
     * 查询指标类型 1-数值,2-文本,3-选择,4-计算
     */
    private void setBzValue(List<Lis_bgzbModel> list) {
        String type = "";
        for (Lis_bgzbModel bg_zbModel : list) {
            type = bg_jybgModelMapper.queryZblxByZb(bg_zbModel);
            if (type == null || type == "") {
                //没有结果
                bg_zbModel.setJcjg(" ");
                continue;
            }
            switch (type) {
                case "1":
                case "4":
                    if (bg_zbModel.getDw() != null) {
                        bg_zbModel.setJcjg(bg_zbModel.getValue_n() + " " + bg_zbModel.getDw());
                    } else {
                        bg_zbModel.setJcjg(bg_zbModel.getValue_n());
                    }
                    if (bg_zbModel.getValue_n() != null) {
                        bg_zbModel.setCol("VALUE_N");
                        BigDecimal value = new BigDecimal(bg_zbModel.getValue_n());
                        //比较偏高 偏低 -1 偏低 0 1偏高
                        if (bg_zbModel.getN_max() != null) {
                            if (value.compareTo(bg_zbModel.getN_max()) > 0) {
                                //偏高
                                bg_zbModel.setZt("1");
                                break;
                            }
                        }
                        if (bg_zbModel.getN_min() != null) {
                            if (value.compareTo(bg_zbModel.getN_min()) < 0) {
                                //偏低
                                bg_zbModel.setZt("-1");
                                break;
                            }
                        }
                        bg_zbModel.setZt("0");
                    }
                    break;
                case "2":
                    bg_zbModel.setJcjg(bg_zbModel.getValue_t());
                    bg_zbModel.setCol("VALUE_T");
                    break;
                case "3":
                    bg_zbModel.setJcjg(bg_zbModel.getValue_l());
                    bg_zbModel.setCol("VALUE_L");
                    break;
                default:
                    break;
            }
        }
    }

    private void setBzValue(Lis_bgzbModel bean) {
        String type = bg_jybgModelMapper.queryZblxByZb(bean);
        switch (type) {
            case "1":
            case "4":
                bean.setJcjg(bean.getValue_n());
                break;
            case "2":
                bean.setJcjg(bean.getValue_t());
                break;
            case "3":
                bean.setJcjg(bean.getValue_l());
                break;
            default:
                break;
        }
    }

    private Map<String, Object> createHistory(Map<String, List<Lis_bgzbModel>> map) {
        Map<String, Object> maps = null;
        if (map == null) {
            return null;
        }
        maps = new HashMap<>();
        //指标目录结果
        List<String> root = new ArrayList<>();
        for (String date : map.keySet()) {
            List<Lis_bgzbModel> zbList = map.get(date);
            for (Lis_bgzbModel lis_bgzbModel : zbList) {
                String zbxmmc = lis_bgzbModel.getZbxmmc();
                if (!root.contains(zbxmmc)) {
                    root.add(zbxmmc);
                }
            }
        }

        //检验日期list
        List<String> dateList = new ArrayList<>();
        //每行指标list
        List<String> lineList = null;


        List<Object> list = new ArrayList<>();

        for (String date : map.keySet()) {
            //日期
            dateList.add(date);
        }
        //行
        for (int i = 0; i < root.size(); i++) {
            //列
            for (int j = 0; j < map.size(); j++) {
                lineList = new ArrayList<>();
                //每行值
                if (j == 0) {
                    //每行第一个为指标名称
                    lineList.add(root.get(i));
                }
                String rootName = root.get(i);
                //zb中的值找对应的值
                for (int k = 0; k < map.get(dateList.get(j)).size(); k++) {
                    //匹配每行对应指标的值
                    if (rootName.equals(map.get(dateList.get(j)).get(k).getZbxmmc())) {
                        lineList.add(map.get(dateList.get(j)).get(k).getJcjg());
                        break;
                    }
                    //未找到指标list中的值
                    if (k == map.get(dateList.get(j)).size() - 1) {
                        lineList.add(" ");
                        break;
                    }
                }
                //每行结束
                if (j == map.size() - 1) {
                    list.add(lineList);
                }
            }

        }
        maps.put("date", dateList);
        maps.put("line", list);
        return maps;
    }

}
