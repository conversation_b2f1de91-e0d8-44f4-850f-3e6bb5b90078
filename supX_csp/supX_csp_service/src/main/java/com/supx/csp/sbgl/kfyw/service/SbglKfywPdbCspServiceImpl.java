package com.supx.csp.sbgl.kfyw.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.csp.api.pubfun.pojo.ypkc.YkkcclMsgModel;
import com.supx.csp.api.pubfun.service.IWzFunCspService;
import com.supx.csp.api.sbgl.kfyw.pojo.Sbgl_pdbModel;
import com.supx.csp.api.sbgl.kfyw.pojo.Sbgl_pdbmxModel;
import com.supx.csp.api.sbgl.kfyw.service.ISbgl_pdbCspService;
import com.supx.csp.user.service.ServiceInvocationHelper;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.comm.pojo.UserInfoModel;
import com.supx.csp.sbgl.kfyw.dao.New1Sbgl_pdbModelMapper;
import com.supx.csp.sbgl.kfyw.dao.New1Sbgl_pdbmxModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * @ClassName: SbglKfywPdbCspServiceImpl
 * @Description: TODO(物资盘点数据操作)
 * <AUTHOR>
 * @date 2020年10月17日 下午2:41:52
 *
 */
@Service
@DubboService
public class SbglKfywPdbCspServiceImpl extends ServiceInvocationHelper implements ISbgl_pdbCspService {
	// 日志工具
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private New1Sbgl_pdbModelMapper sbgl_pdbModelMapper;

	@Autowired
	private New1Sbgl_pdbmxModelMapper sbgl_pdbmxModelMapper;

	@Autowired
	private IWzFunCspService wzFunCspService;

	// 保存单据和明细
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(rollbackFor = Exception.class)
	public UtilResponse saveBatch(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "保存物资盘点表和明细";
		try {
			// 准备参数
			Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("dj");
			List<Sbgl_pdbmxModel> pdbList = (List<Sbgl_pdbmxModel>) msg.getParam().get("djmx");
//			//判断是自动盘点还是手动盘点
//			if(pdb.getPdfs() !=null ){//自动盘点
//                	pdbList = wzb_pdbModelMapper.queryWzmxByWzdl(pdb);
//			}else {//手动盘点
//				    pdbList = (List<Wzb_pdbmxModel>) msg.getParam().get("djmx");
//			}
            //判断盘点的物资是否已经有未审核的盘点
			List<Sbgl_pdbModel> temList = checkPdd(pdbList,pdb);





			// 设置医疗机构编码
//			pdb.setYljgbm(msg.getYljgbm());
//			for (int i = 0; i < pdbList.size(); i++) {
//				pdbList.get(i).setYljgbm(msg.getYljgbm());
//			}
//
//			// 判断是否存在为审核盘点表
//			Wzb_pdbModel temp = new Wzb_pdbModel();
//			temp.setYljgbm(msg.getYljgbm());
//			temp.setWzkf(pdb.getWzkf());
//			temp.setQrzfbz("0");
//			List<Wzb_pdbModel> temList = wzb_pdbModelMapper.selectByPrimaryKey(temp);
			if (temList.size() > 0) {
				String kfbm = temList.get(0).getWzkf();
				String pdpzh = temList.get(0).getPdpzh();
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("物资库房：【" + kfbm + "】 存在未审核盘点但【" + pdpzh + "】，请先审核！");
				logger.error(result.getResultMsg());
				return result;
			}

			// 获取结果
			int ref = sbgl_pdbModelMapper.insertSelective(pdb);
			// 校验结果
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("新增盘点表和明细失败");
				logger.error(mess + "接口【SbglKfywPdbCspServiceImpl saveBatch】失败");
				throw new Exception();// 引发回滚
			}

			int refMx = sbgl_pdbmxModelMapper.insertSelective(pdbList);
			// 校验结果
			if (refMx != -1) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("新增盘点表和明细失败");
				logger.error(mess + "接口【SbglKfywPdbCspServiceImpl saveBatch】失败");
				throw new Exception();// 引发回滚
			}
			// 设置结果
			result.setResultMsg(ISystemConstants.CSP_SUCCESS);
			result.setResultMsg("新增盘点表和明细成功");
			result.getResResult().put("ref", ref);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + e.getCause().getMessage());
			logger.error(mess + "接口【SbglKfywPdbCspServiceImpl saveBatch】发生异常", e);
			throw new Exception(mess + "接口【SbglKfywPdbCspServiceImpl saveBatch】发生异常");// 引发回滚
		}
		return result;
	}

	/**
	 * 核查是否有未审核的盘点单
	 * @param pdbmxList
	 * @param pdb
	 * @return
	 */
	private List<Sbgl_pdbModel> checkPdd(List<Sbgl_pdbmxModel> pdbmxList, Sbgl_pdbModel pdb) {
		for(Sbgl_pdbmxModel model:pdbmxList){
			model.setYljgbm(pdb.getYljgbm());
		}

		List<Sbgl_pdbModel> temList = new ArrayList<Sbgl_pdbModel>();
		try {
			temList = sbgl_pdbModelMapper.selectByPrimaryKeyS(pdbmxList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return temList;
	}

	// 新增明细
	@SuppressWarnings("unchecked")
	@Override
	public UtilResponse addNewMx(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "新增物资明细";
		try {
			// 准备参数
			Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("dj");
			List<Sbgl_pdbmxModel> pdbList = (List<Sbgl_pdbmxModel>) msg.getParam().get("djmx");

			// 设置医疗机构编码
			for (int i = 0; i < pdbList.size(); i++) {
				pdbList.get(i).setYljgbm(msg.getYljgbm());
			}

			// 获取mxxh
			List<Sbgl_pdbmxModel> TemList = sbgl_pdbmxModelMapper.selectByPrimaryKey(pdb);
			Integer mxxh = TemList.size();
			// 设置mxxh和pdbid
			for (int i = 0; i < pdbList.size(); i++) {
				mxxh++;
				pdbList.get(i).setMxxh(mxxh);
				pdbList.get(i).setPdbid(pdb.getPdpzh() + String.valueOf(mxxh));
			}

			// 保存结果
			int refMx = sbgl_pdbmxModelMapper.insertSelective(pdbList);
			// 校验结果
			if (refMx != -1) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultCode("1");
				logger.error(mess + "接口【SbglKfywPdbCspServiceImpl saveBatch】失败");
				return result;
			}
			// 设置结果
			result.setResultMsg(ISystemConstants.CSP_SUCCESS);
			result.getResResult().put("ref", refMx);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + e.getCause().getMessage());
			logger.error(mess + "接口【SbglKfywPdbCspServiceImpl saveBatch】发生异常", e);
			throw e;// 引发回滚
		}
		return result;
	}

	// 查询列表
	@Override
	public UtilResponse queryDj(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "查询盘点列表";
		try {
			// 准备参数
			Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("bean");
			// 设置医疗机构编码
			pdb.setYljgbm(msg.getYljgbm());
			// 获取列表
			PageHelper.startPage(pdb.getPage(), pdb.getRows());
//			List<Wzb_pdbModel> list = wzb_pdbModelMapper.selectByPrimaryKey(pdb);
			List<Sbgl_pdbModel> list = sbgl_pdbModelMapper.queryPdb(pdb);
			PageInfo<Sbgl_pdbModel> pageInfo = new PageInfo<Sbgl_pdbModel>(list);
			result.setResultMsg(ISystemConstants.CSP_SUCCESS);
			result.getResResult().put("list", pageInfo);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + e.getCause().getMessage());
			logger.error(mess + "接口【WzbKfywCkdCspServiceImpl queryDj】发生异常", e);
			return null;
		}
		return result;
	}

	// 查询明细
	@Override
	public UtilResponse queryMx(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "查询盘点明细";
		try {
			// 准备参数
			Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("bean");
			// 设置医疗机构编码
			pdb.setYljgbm(msg.getYljgbm());
			// 获取列表
			List<Sbgl_pdbmxModel> list = sbgl_pdbmxModelMapper.selectByPrimaryKey(pdb);
			result.setResultMsg(ISystemConstants.CSP_SUCCESS);
			result.getResResult().put("list", list);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + e.getCause().getMessage());
			logger.error(mess + "接口【WzbKfywCkdCspServiceImpl queryMx】发生异常", e);
			return null;
		}
		return result;
	}

	// 分页查询明细
	@Override
	public UtilResponse queryMxByPage(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "查询盘点明细";
		try {
			// 准备参数
			Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("bean");
			// 设置医疗机构编码
			pdb.setYljgbm(msg.getYljgbm());
			// 按参数获取列表
			PageHelper.startPage(pdb.getPage(), pdb.getRows());
			List<Sbgl_pdbmxModel> list = sbgl_pdbmxModelMapper.selectByPrimaryKey(pdb);
			PageInfo<Sbgl_pdbmxModel> pageInfo = new PageInfo<>(list);
			result.setResultMsg(ISystemConstants.CSP_SUCCESS);
			result.getResResult().put("list", pageInfo);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + e.getCause().getMessage());
			logger.error(mess + "接口【WzbKfywCkdCspServiceImpl queryMx】发生异常", e);
			return null;
		}
		return result;
	}

	// 作废单据
	@Override
	public UtilResponse invalidDj(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "作废盘点单据";
		try {
			// 准备参数
			Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("bean");
			// 设置医疗机构编码
			pdb.setYljgbm(msg.getYljgbm());
			// 设置作废日期
			pdb.setQrzfrq(new Date());
			pdb.setZdr(null);
			pdb.setQrzfbz("2");
			int ref = sbgl_pdbModelMapper.updateByPrimaryKeySelective(pdb);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultCode("1");
				logger.error(mess + "接口【WzbKfywCkdCspServiceImpl updateDj】执行失败，影响行数：" + ref);
			} else {
				result.setResultMsg(ISystemConstants.CSP_SUCCESS);
			}
			result.getResResult().put("ref", ref);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + e.getCause().getMessage());
			logger.error(mess + "接口【WzbKfywCkdCspServiceImpl queryDj】发生异常", e);
			return null;
		}
		return result;
	}

	// 审核单据
	@Override
	@Transactional(rollbackFor = Exception.class)
	public UtilResponse passDj(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "审核盘点单据";
		try {
			// 准备参数
			Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("bean");
			// 设置医疗机构编码
			pdb.setYljgbm(msg.getYljgbm());
			// 设置审核日期
			pdb.setQrzfrq(new Date());

			// 物资库存处理
			YkkcclMsgModel kcmsg = new YkkcclMsgModel();
			UserInfoModel userInfo = (UserInfoModel) msg.getUserinfo().get("userinfo");
			kcmsg.setCzybm(userInfo.getCzybm());
			kcmsg.setDjh(pdb.getPdpzh());
			// 01－库房入库,02-库房退库,03－盘点
			// 04-库房出库,05-库房退货,06-报损出库
			kcmsg.setCrlx("03");
			kcmsg.setYfszfs("0");// 0-库房出库药房同步上帐,1-药房单独上帐
			msg.getParam().put("bean", kcmsg);
			msg.getParam().put("userinfo", userInfo);
			msg.getParam().put("wzkf", pdb.getWzkf());
			UtilResponse res = wzFunCspService.WzkcclLock(msg); // 校验结果
			if (res.getResultCode().equals("2")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("出库审核失败:" + res.getResultMsg());
				logger.error("调用出库审核接口【WzbKfywCkdCspServiceImpl passCkd】发生异常", res.getResultMsg());
				return result;
			}
			if (res.getResultCode().equals("1")) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("出库审核失败:" + res.getResultMsg());
				logger.error("调用出库审核接口【WzbKfywCkdCspServiceImpl passCkd】发生异常", res.getResultMsg());
				throw new Exception(result.getResultMsg());
			}
            pdb.setQrzfbz("1");//审核
			int ref = sbgl_pdbModelMapper.updateByPrimaryKeySelective(pdb);
			// int ref = -1;

			// 校验结果
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultCode("1");
				logger.error(mess + "接口【WzbKfywCkdCspServiceImpl updateDj】执行失败，影响行数：" + ref);
				throw new Exception("物资盘点单据审核标志修改失败");
			} else {
				result.setResultMsg(ISystemConstants.CSP_SUCCESS);
				result.setResultMsg(res.getResultMsg());
			}
			result.getResResult().put("ref", ref);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			logger.error(mess + "接口【WzbKfywCkdCspServiceImpl queryDj】发生异常", e);
			throw new Exception(e.getMessage());
		}
		return result;
	}

	// 修改明细
	@Override
	public UtilResponse updateMx(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "修改盘点明细";
		try {
			// 准备参数
			Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("bean");
			// 设置医疗机构编码
			pdb.setYljgbm(msg.getYljgbm());
			// 获取列表
			List<Sbgl_pdbmxModel> list = sbgl_pdbmxModelMapper.selectByPrimaryKey(pdb);
			// 更新明细
			int ref = sbgl_pdbmxModelMapper.updateByPrimaryKeySelective(list);
			result.getResResult().put("ref", ref);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + e.getCause().getMessage());
			logger.error(mess + "接口【WzbKfywCkdCspServiceImpl updateMx】发生异常", e);
			return null;
		}
		return result;
	}

	@Override
	public UtilResponse zdpd(UtilRequest msg) {
		Sbgl_pdbModel pdb = (Sbgl_pdbModel) msg.getParam().get("dj");

		UtilResponse result = UtilResponse.newInstance();
		String mess = "自动生成盘点";
		try {
			List<Sbgl_pdbmxModel> pdbList = sbgl_pdbModelMapper.queryWzmxByWzdl(pdb);
			result.setResultMsg(ISystemConstants.CSP_SUCCESS);
			result.getResResult().put("list", pdbList);

		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + e.getCause().getMessage());
			logger.error(mess + "接口【WzbKfywCkdCspServiceImpl zdpd】发生异常", e);
			return null;
		}
		return result;
	}
}
