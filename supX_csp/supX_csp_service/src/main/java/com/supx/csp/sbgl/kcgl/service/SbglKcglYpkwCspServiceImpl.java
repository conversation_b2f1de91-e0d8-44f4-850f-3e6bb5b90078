package com.supx.csp.sbgl.kcgl.service;

import com.github.pagehelper.PageInfo;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.csp.api.sbgl.kcgl.service.ISbfKcglYpkwCspService;
import com.supx.csp.api.yfgl.kcgl.pojo.Yfb_ypkwModel;
import com.supx.csp.sbgl.kcgl.dao.Sbfb_ypkwModelMapper;
import com.supx.csp.user.service.ServiceInvocationHelper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * @ClassName: YfbKcglYpkwCspServiceImpl
 * @Description: (药品库位数据操作)
 * <AUTHOR> YK
 * @date 2020年9月8日 下午7:39:05
 *
 */
@Service
@DubboService
public class SbglKcglYpkwCspServiceImpl extends ServiceInvocationHelper implements ISbfKcglYpkwCspService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private Sbfb_ypkwModelMapper yfb_ypkwModelMapper;

	// 保存库位信息
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse addKw(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		int ref = -1;

		try {
			// 获取参数
			Yfb_ypkwModel ypkwModel = (Yfb_ypkwModel) msg.getParam().get("ypkw");

			// 设置医疗机构编码
			ypkwModel.setYljgbm(msg.getYljgbm());

			// 保存库位信息
			ref = yfb_ypkwModelMapper.insertKw(ypkwModel);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("库位信息保存失败,影响的行数：" + ref);
				logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl addKw】发生异常");
				throw new Exception(); // 异常自动返回 并且SQL事务回滚
			}
			result.getResResult().put("ref", ref);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息保存失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl addKw】,影响的行数：" + ref);
			throw ex;
		}
		return result;
	}

	// 修改库位信息
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse modiKw(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		int ref = -1;

		try {
			// 获取参数
			Yfb_ypkwModel ypkwModel = (Yfb_ypkwModel) msg.getParam().get("ypkw");

			// 设置医疗机构编码
			ypkwModel.setYljgbm(msg.getYljgbm());

			// 保存库位信息
			ref = yfb_ypkwModelMapper.updateKw(ypkwModel);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("库位信息更新失败,影响的行数：" + ref);
				logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl modiKw】发生异常");
				throw new Exception(); // 异常自动返回 并且SQL事务回滚
			}
			result.getResResult().put("ref", ref);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息更新失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl modiKw】,影响的行数：" + ref);
			throw ex;
		}
		return result;
	}

	// 删除库位信息
	@SuppressWarnings("unchecked")
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse delKw(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		int ref = -1;

		try {
			// 获取参数
			List<Yfb_ypkwModel> list = (List<Yfb_ypkwModel>) msg.getParam().get("list");
			// 设置医疗机构编码
			for (int i = 0; i < list.size(); i++) {
				list.get(i).setYljgbm(msg.getYljgbm());
			}

			// 保存库位信息
			ref = yfb_ypkwModelMapper.deleteKw(list);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("库位信息删除失败,影响的行数：" + ref);
				logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl modiKw】发生异常");
				throw new Exception(); // 异常自动返回 并且SQL事务回滚
			}
			result.getResResult().put("ref", ref);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息删除失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl modiKw】,影响的行数：" + ref);
			throw ex;
		}
		return result;
	}

	// 查询库位信息
	@Override
	public UtilResponse selKw(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		try {
			// 获取参数
			Yfb_ypkwModel ypkwModel = (Yfb_ypkwModel) msg.getParam().get("ypkw");

			// 设置医疗机构编码
			ypkwModel.setYljgbm(msg.getYljgbm());

			// 保存库位信息
			List<Yfb_ypkwModel> list = yfb_ypkwModelMapper.selectKw(ypkwModel);
			PageInfo<Yfb_ypkwModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息查询失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl addKw】发生异常");
			throw ex;
		}
		return result;
	}
	// 修改库位信息
	@Transactional(rollbackFor = Exception.class)
	public UtilResponse delete(UtilRequest msg) throws Exception {
		// 准备接受结果
		UtilResponse result = UtilResponse.newInstance();
		int ref = -1;

		try {
			// 获取参数
			Yfb_ypkwModel ypkwModel = (Yfb_ypkwModel) msg.getParam().get("ypkw");

			// 设置医疗机构编码
			ypkwModel.setYljgbm(msg.getYljgbm());

			// 保存库位信息
			ref = yfb_ypkwModelMapper.delete(ypkwModel);
			if (ref <= 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("库位信息删除失败,影响的行数：" + ref);
				logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl delete】发生异常");
				throw new Exception(); // 异常自动返回 并且SQL事务回滚
			}
			result.getResResult().put("ref", ref);

		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("库位信息更新失败:" + ex.getCause().getMessage());
			logger.error("调用库位信息接口【YfbKcglYfpwCspServiceImpl delete】,影响的行数：" + ref);
			throw ex;
		}
		return result;
	}
}
