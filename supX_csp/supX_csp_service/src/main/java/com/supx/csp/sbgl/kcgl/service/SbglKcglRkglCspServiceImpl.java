package com.supx.csp.sbgl.kcgl.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.comm.pojo.CsqxInfoModel;
import com.supx.comm.pojo.UserInfoModel;
import com.supx.comm.util.CommonUtil;
import com.supx.comm.util.JsonUtil;
import com.supx.csp.api.pubfun.pojo.getxh.GetXhidMsgModel;
import com.supx.csp.api.pubfun.pojo.getxh.GetXhidResModel;
import com.supx.csp.api.pubfun.pojo.ypkc.YfkcclMsgModel;
import com.supx.csp.api.pubfun.service.IPubFunCspService;
import com.supx.csp.api.pubfun.service.IYpFunCspService;
import com.supx.csp.api.sbgl.kcgl.pojo.Sbfb_rkPrintModel;
import com.supx.csp.api.sbgl.kcgl.pojo.Sbfb_rkdModel;
import com.supx.csp.api.sbgl.kcgl.pojo.Sbfb_rkdmxModel;
import com.supx.csp.api.sbgl.kcgl.service.ISbfKcglRkglCspService;
import com.supx.csp.sbgl.kcgl.dao.Sbfb_rkdModelMapper;
import com.supx.csp.sbgl.kcgl.dao.Sbfb_rkdmxModelMapper;
import com.supx.csp.sbgl.xtwh.dao.New1Sbdl_sbzdModelMapper;
import com.supx.csp.user.service.ServiceInvocationHelper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: YfbYfywRkglCspServpceImpl
 * @Description: 药房库存增加类接口
 * @date 2020年7月14日 上午11:36:23
 */
@Service
@DubboService
public class SbglKcglRkglCspServiceImpl extends ServiceInvocationHelper implements ISbfKcglRkglCspService {

    private final static Logger logger = LoggerFactory.getLogger(SbglKcglRkglCspServiceImpl.class);

    @Autowired
    private Sbfb_rkdModelMapper yfb_rkdModelMapper;
    @Autowired
    private Sbfb_rkdmxModelMapper yfb_rkdmxModelMapper;
    @Autowired
    private IPubFunCspService pubFunCspService;
    @Autowired
    private IYpFunCspService ypFunCspService;
    @Autowired
    private New1Sbdl_sbzdModelMapper sbgl_ypzdModelMapper;

    /*
     * 入库单查询
     */
    @Override
    public UtilResponse queryRkd(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Sbfb_rkdModel bean = (Sbfb_rkdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            PageHelper.startPage(bean.getPage(), bean.getRows());
            List<Sbfb_rkdModel> list = yfb_rkdModelMapper.queryRkd(bean);
            PageInfo<Sbfb_rkdModel> pageInfo = new PageInfo<>(list);
            result.getResResult().put("pageInfo", pageInfo);
            //result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("单据查询失败:" + e.getCause().getMessage());
            logger.error("单据查询接口【YfbKcglRkglCspServiceImpl queryRkd】发生异常", e);
        }
        return result;
    }

    /**
     * 保存入库单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public UtilResponse SaveRkd(UtilRequest msg) throws Exception {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Date ldDate = new Date();
            JSONObject JsonObj = (JSONObject) msg.getParam().get("obj");
            UserInfoModel userinfo = (UserInfoModel) msg.getParam().get("userinfo");
            String rklx = (String) msg.getParam().get("rklx");
            String mess = (String) msg.getParam().get("mess");// 单据提示信息
            String xhlx = (String) msg.getParam().get("xhlx");
            if (JsonObj == null) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "保存对象参数无效");
                logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + mess + "对象参数无效");
                return result;
            }
            JSONObject rkdobj = JsonObj.getJSONObject("rkd");
            JSONArray rkmxobj = JsonObj.getJSONArray("rkdmx");
            Sbfb_rkdModel rkd = JsonUtil.JsonToClass(rkdobj, Sbfb_rkdModel.class);
            List<Sbfb_rkdmxModel> rkdmx = (List<Sbfb_rkdmxModel>) JsonUtil.JsonToArray(rkmxobj, Sbfb_rkdmxModel.class);
            rkd.setRklx(rklx);
            if (rkd == null) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "信息无效");
                logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + mess + "信息无效");
                return result;
            }
            String yfbm = rkd.getYfbm();
            if (yfbm == null || yfbm.equals("")) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单药房编码无效");
                logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + mess + "信息无效");
                return result;
            }
            if (rkd.getRklx() == null || rkd.getRklx().equals("")) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单药房编码无效");
                logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + mess + "信息无效");
                return result;
            }
            if (rkdmx == null || rkdmx.size() <= 0) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("没有可保存的" + mess + "单明细");
                logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】没有可保存的" + mess + "单明细");
                return result;
            }
            String strErr = "";
            for (int i = 0; i < rkdmx.size(); i++) {
                if (rkdmx.get(i).getScph() == null || rkdmx.get(i).getScph().equals("")) {
                    strErr += "第 " + i + "行药品批号不能为空";
                    break;
                }
                if (rkdmx.get(i).getSbbm() == null || rkdmx.get(i).getSbbm().equals("")) {
                    strErr += "第 " + i + "行药品编码不能为空";
                    break;
                }
                if (rkdmx.get(i).getScrq() == null) {
                    strErr += "第 " + i + "行生产日期不能为空";
                    break;
                }
                if (rkdmx.get(i).getYxqz() == null) {
                    strErr += "第 " + i + "行有效期至不能为空";
                    break;
                }
                if (rkdmx.get(i).getKfdw() == null || rkdmx.get(i).getKfdw().equals("")) {
                    strErr += "第 " + i + "行【" + rkdmx.get(i).getYpmc() + "】库房单位不能为空";
                }
                if (rkdmx.get(i).getYfdw() == null || rkdmx.get(i).getYfdw().equals("")) {
                    strErr += "第 " + i + "行【" + rkdmx.get(i).getYpmc() + "】药房单位不能为空";
                    break;
                }
                if (rkdmx.get(i).getSbbm() == null || rkdmx.get(i).getSbbm().equals("")) {
                    strErr += "第 " + i + "行【" + rkdmx.get(i).getYpmc() + "】药品编码不能为空";
                    break;
                }
                if (rkdmx.get(i).getRksl() == null || rkdmx.get(i).getRksl() <= 0) {
                    strErr += "第 " + i + "行【" + rkdmx.get(i).getYpmc() + "】入库数量不能小于零";
                    break;
                }
            }
            if (!strErr.equals("")) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "明细保存失败," + strErr);
                logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + mess + "明细保存失败");
                return result;
            }
            int ref = 0;
            // 入库单为空新增
            if (rkd.getRkdh() == null || rkd.getRkdh().equals("")) {
                // 取入库单号
                UtilResponse resxh = null;
                GetXhidMsgModel beanrk = new GetXhidMsgModel();
                beanrk.setScfs("2");
                beanrk.setCslx(xhlx);
                beanrk.setYfbm(yfbm);
                msg.getParam().put("bean", beanrk);
                resxh = pubFunCspService.GetYfPzh(msg);
                if (!resxh.isSuccess()) {
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg(resxh.getResultMsg());
                    logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + resxh.getResultMsg());
                    return result;
                }
                GetXhidResModel cfhbean = (GetXhidResModel) resxh.getResResult().get("bean");
                String rkdh = cfhbean.getDqxh();
                rkd.setRkdh(rkdh);
                rkd.setZdr(userinfo.getCzybm());
                rkd.setZdrq(ldDate);
                rkd.setYljgbm(msg.getYljgbm());
                String bzms = null;
                for (int i = 0; i < rkdmx.size(); i++) {
                    rkdmx.get(i).setRkdh(rkdh);
                    rkdmx.get(i).setMxxh(i + 1);
                    rkdmx.get(i).setYljgbm(msg.getYljgbm());
                    if (rkdmx.get(i).getBzms() != null) bzms = rkdmx.get(i).getBzms();
                }
                if (bzms != null) rkd.setBzms(bzms);
                ref = yfb_rkdModelMapper.insert(rkd);
                if (ref <= 0) {
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg(mess + "单保存失败");
                    logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + mess + "单保存失败");
                    throw new Exception();
                }
                ref = yfb_rkdmxModelMapper.insertRkmx(rkdmx);
                if (ref != -1) {
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg(mess + "单明细保存失败");
                    logger.error("保存" + mess + "单接口【YfbYfywRkglCspServpceImpl SaveRkd】" + mess + "单保明细存失败");
                    throw new Exception();
                }
            } else {// 有单号修改
                rkd.setYljgbm(msg.getYljgbm());
                ref = yfb_rkdModelMapper.update(rkd);
                if (ref <= 0) {
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg(mess + "单保存失败");
                    logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + mess + "单保存失败");
                    throw new Exception();
                }
                for (int i = 0; i < rkdmx.size(); i++) {
                    rkdmx.get(i).setYljgbm(msg.getYljgbm());
                }
                ref = yfb_rkdmxModelMapper.updateRkmx(rkdmx);
                if (ref != -1) {
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg(mess + "单保存明细失败");
                    logger.error("保存" + mess + "单接口【YfbKcglRkglCspServiceImpl SaveRkd】" + mess + "单明细保存失败");
                    throw new Exception();
                }
            }
            result.getResResult().put("ref", "1");
            result.setResultMsg(mess + "保存成功");

        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("保存入库单失败:" + e.getCause().getMessage());
            logger.error("保存入库单接口【YfbKcglRkglCspServiceImpl SaveRkd】发生异常", e);
            throw new Exception();
        }
        return result;
    }

    /**
     * 单据审核
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public UtilResponse pzsh(UtilRequest msg) throws Exception {
        UtilResponse result = new UtilResponse().newInstance();
        String mess = "";
        try {
            Date ldDate = new Date();
            UserInfoModel userinfo = (UserInfoModel) msg.getParam().get("userinfo");
            Map<String, Object> map = (Map<String, Object>) msg.getParam().get("obj");
            String rkdh = (String) map.get("rkdh");
            mess = (String) msg.getParam().get("mess");// 单据提示信息
            Sbfb_rkdModel bean = new Sbfb_rkdModel();
            bean.setYljgbm(msg.getYljgbm());
            bean.setRkdh(rkdh);
            Sbfb_rkdModel rkd = yfb_rkdModelMapper.queryRkdOne(bean);
            if (rkd == null) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单审核失败:单据号无效");
                logger.error(mess + "单审核接口【YfbKcglRkglCspServiceImpl pzsh】单据号无效");
                return result;
            }
            String shbz = rkd.getShzfbz();
            if (shbz.equals("1")) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单审核失败:此单据已核审!");
                logger.error(mess + "单审核接口【YfbKcglRkglCspServiceImpl pzsh】单据已核审");
                return result;
            }
            if (shbz.equals("2")) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单审核失败:此单据已作废!");
                logger.error(mess + "单审核接口【YfbKcglRkglCspServiceImpl pzsh】单据已作废");
                return result;
            }
            rkd.setShzfbz("1");
            rkd.setShzfrq(ldDate);
            rkd.setShzfry(userinfo.getCzybm());
            rkd.setShzfryxm(userinfo.getCzyxm());
            rkd.setYljgbm(msg.getYljgbm());

            // 库存处理入参
            YfkcclMsgModel kcmsg = new YfkcclMsgModel();
            kcmsg.setCzybm(userinfo.getCzybm());
            kcmsg.setDjh(rkdh);
            kcmsg.setCrlx("01");// 入库上帐
            msg.getParam().put("bean", kcmsg);
            UtilResponse res = ypFunCspService.YfkcclLock(msg);
            if (res.getResultCode().equals("2")) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "审核失败:" + res.getResultMsg());
                logger.error("调用" + mess + "审核接口【YfbKcglRkglCspServiceImpl pzsh】发生异常", res.getResultMsg());
                throw new Exception();
            }
            int ref = yfb_rkdModelMapper.rkdshzf(rkd);
            if (ref <= 0) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单审核失败.");
                logger.error(mess + "单审核接口【YfbKcglRkglCspServiceImpl pzsh】单据审核异常");
                throw new Exception();
            }
            // 更新药品字典价格
            // 根据入库单号关联修改药品字典价格
            if ("1".equals(rkd.getShzfbz())) {
                Sbfb_rkdmxModel rkdmx = new Sbfb_rkdmxModel();
                rkdmx.setRkdh(rkdh);
                rkdmx.setYljgbm(msg.getYljgbm());
                List<Sbfb_rkdmxModel> mxList = yfb_rkdmxModelMapper.queryRkdmx(rkdmx);
                if (mxList.size() == 0) {
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg("更新药品字典价格失败，入库明细不能为空。");
                    logger.error("更新药品字典价格接口【ykb_RkdCspServiceImpl updateByRkd】异常");
                    throw new Exception();
                }
//				int ypzdRef = sbgl_ypzdModelMapper.updateByYfRkd(mxList);
//				if (ypzdRef != -1) {
//					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
//					result.setResultMsg("更新药品字典价格失败.");
//					logger.error("更新药品字典价格接口【ykb_RkdCspServiceImpl updateByRkd】异常");
//					throw new Exception();
//				}
            }

            result.getResResult().put("ref", 1);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg(mess + "单审核失败:" + e.getCause().getMessage());
            logger.error(mess + "单审核接口【YfbKcglRkglCspServiceImpl pzsh】发生异常", e);
            throw new Exception();
        }
        return result;
    }

    /**
     * 单据明细查询
     */
    @Override
    public UtilResponse queryRkdmx(UtilRequest msg) {
        UtilResponse result = new UtilResponse().newInstance();
        try {
            Sbfb_rkdmxModel bean = (Sbfb_rkdmxModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Sbfb_rkdmxModel> list = yfb_rkdmxModelMapper.queryRkdmx(bean);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("单据明细查询失败:" + e.getCause().getMessage());
            logger.error("单据明细查询接口【YfbKcglRkglCspServiceImpl queryRkdmx】发生异常", e);
        }
        return result;
    }

    /**
     * 单据作废
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public UtilResponse pzzf(UtilRequest msg) throws Exception {
        UtilResponse result = new UtilResponse().newInstance();
        String mess = "";
        try {
            Date ldDate = new Date();
            UserInfoModel userinfo = (UserInfoModel) msg.getParam().get("userinfo");
            Map<String, Object> map = (Map<String, Object>) msg.getParam().get("obj");
            String rkdh = (String) map.get("rkdh");
            mess = (String) msg.getParam().get("mess");// 单据提示信息
            Sbfb_rkdModel bean = new Sbfb_rkdModel();
            bean.setYljgbm(msg.getYljgbm());
            bean.setRkdh(rkdh);
            Sbfb_rkdModel rkd = yfb_rkdModelMapper.queryRkdOne(bean);
            if (rkd == null) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单审核失败:单据号无效");
                logger.error(mess + "单作废接口【YfbKcglRkglCspServiceImpl pzzf】单据号无效");
                return result;
            }
            String shbz = rkd.getShzfbz();
            if (shbz.equals("1")) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单作废失败:此单据已核审!");
                logger.error(mess + "单作废接口【YfbKcglRkglCspServiceImpl pzzf】单据已核审");
                return result;
            }
            if (shbz.equals("2")) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单作废失败:此单据已作废!");
                logger.error(mess + "单作废接口【YfbKcglRkglCspServiceImpl pzzf】单据已作废");
                return result;
            }
            rkd.setShzfbz("2");
            rkd.setShzfrq(ldDate);
            rkd.setShzfry(userinfo.getCzybm());
            rkd.setShzfryxm(userinfo.getCzyxm());
            rkd.setYljgbm(msg.getYljgbm());
            int ref = yfb_rkdModelMapper.rkdshzf(rkd);
            if (ref <= 0) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg(mess + "单作废失败.");
                logger.error(mess + "单作废接口【YfbKcglRkglCspServiceImpl pzzf】单据作废异常");
                throw new Exception();
            }
            result.getResResult().put("ref", 1);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg(mess + "单作废失败:" + e.getCause().getMessage());
            logger.error(mess + "单作废接口【YfbKcglRkglCspServiceImpl pzzf】发生异常", e);
            throw new Exception();
        }
        return result;
    }

    // 入库打印
    @SuppressWarnings("unchecked")
    @Override
    public UtilResponse rkPrint(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Sbfb_rkdModel bean = (Sbfb_rkdModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            // 获取每页行数
            int lines = 0;
            List<CsqxInfoModel> csTem = (List<CsqxInfoModel>) msg.getParam().get("csqxinfo");
            for (CsqxInfoModel csqxInfoModel : csTem) {
                if ("***********".equals(csqxInfoModel.getCsqxbm())) {
                    lines = Integer.valueOf(csqxInfoModel.getCsz());
                }
            }
            // 组装打印实体
            Sbfb_rkPrintModel rkp = new Sbfb_rkPrintModel();

            // 获取单据
            Sbfb_rkdModel dj = yfb_rkdModelMapper.queryRkdOne(bean);
            // 设置页码
            dj.setRows(lines);

            // 获取单据明细
            Sbfb_rkdmxModel mxTem = new Sbfb_rkdmxModel();
            mxTem.setRkdh(bean.getRkdh());
            mxTem.setYljgbm(msg.getYljgbm());
            List<Sbfb_rkdmxModel> djmx = yfb_rkdmxModelMapper.queryRkdmx(mxTem);

            // 计算汇总
            Double jjjehz = 0.0;
            Double ljjehz = 0.0;
            for (int i = 0; i < djmx.size(); i++) {
                // 计算金额
                Double jjjeTem = CommonUtil.doubleRound(djmx.get(i).getRksl(), djmx.get(i).getSbjj());
                djmx.get(i).setSbjjje(jjjeTem);

                // 汇总
                jjjehz += jjjeTem;
            }
            dj.setJjjehz(jjjehz);
            dj.setLjjehz(ljjehz);

            rkp.setDj(dj);
            rkp.setDjmx(djmx);

            result.getResResult().put("rkp", rkp);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("单据打印失败:" + e.getCause().getMessage());
            logger.error("单据打印接口【YfbKcglRkglCspServiceImpl rkPrint】发生异常", e);
        }
        return result;
    }

    // 入库明细查询统计
    @Override
    public UtilResponse rkdmxCx(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Sbfb_rkdmxModel bean = (Sbfb_rkdmxModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            // 设置分页页码和行数
            PageHelper.startPage(bean.getPage(), bean.getRows());
            // 查询结果
            List<Sbfb_rkdmxModel> list = null;
            if ("sum".equals(bean.getOrder())) {
                list = yfb_rkdmxModelMapper.rkdmxCxByyp(bean);
            } else {
                list = yfb_rkdmxModelMapper.rkdmxCx(bean);

            }
            // 统计总计金额
            List<Sbfb_rkdmxModel> tempLisl = yfb_rkdmxModelMapper.rkdmxCxByyp(bean);
            Double temJjzj = 0.0;
            Double temLjzj = 0.0;

            for (int i = 0; i < tempLisl.size(); i++) {
                temJjzj += tempLisl.get(i).getSbjj() * tempLisl.get(i).getRksl();
            }
            // 将总计金额保存在集合的第一个对象内
            if (list.size() > 0) {
                list.get(0).setSbjjje(temJjzj);
            }

            PageInfo<Sbfb_rkdmxModel> pageInfo = new PageInfo<>(list);
            result.getResResult().put("list", pageInfo);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("单据明细查询失败:" + e.getCause().getMessage());
            logger.error("单据明细查询接口【YfbKcglRkglCspServiceImpl queryRkdmx】发生异常", e);
        }
        return result;
    }

}
