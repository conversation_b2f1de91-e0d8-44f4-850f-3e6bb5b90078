package com.supx.csp.sbgl.kcgl.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.supx.comm.constants.ISystemConstants;
import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.csp.api.sbgl.kcgl.pojo.Sbfb_kcxlModel;
import com.supx.csp.api.sbgl.kcgl.service.ISbfKcglKcxlCspService;
import com.supx.csp.sbgl.kcgl.dao.Sbfb_kcxlModelMapper;
import com.supx.csp.user.service.ServiceInvocationHelper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * @ClassName: YfbKcglKcxlCspServiceImpl
 * @Description: (药房存量数据操作)
 * <AUTHOR> YK
 * @date 2020年9月12日 上午9:35:10
 *
 */
@Service
@DubboService
public class SbglKcglKcxlCspServiceImpl extends ServiceInvocationHelper implements ISbfKcglKcxlCspService {

	// 日志输出
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private Sbfb_kcxlModelMapper yfb_kcxlModelMapper;

	// 查询存量列表
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse QueryKcxl(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "查询药房存量";
		try {
			Sbfb_kcxlModel kcxl = (Sbfb_kcxlModel) msg.getParam().get("kcxl");
			// 设置医疗机构编码
			kcxl.setYljgbm(msg.getYljgbm());
			// 插入新纪录
			int ref = yfb_kcxlModelMapper.insertBatch(kcxl);
			// 判断插入结果
			if (ref < 0) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "失败");
				logger.error(mess + "接口【YfbKcglKcxlCspServiceImpl QueryKcxl-insertBatch】" + mess + "单保存失败");
				// 抛出异常，回滚操作
				throw new Exception();
			}

			// 设置分页信息
			PageHelper.startPage(kcxl.getPage(), kcxl.getRows());
			List<Sbfb_kcxlModel> list = yfb_kcxlModelMapper.selecetAll(kcxl);
			PageInfo<Sbfb_kcxlModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("list", pageInfo);
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + "失败:" + e.getCause().getMessage());
			logger.error(mess + "接口【YfbKcglKcxlCspServiceImpl QueryKcxl】发生异常", e);
			throw e;
		}
		return result;
	}

	// 修改存量
	@SuppressWarnings("unchecked")
	@Transactional(rollbackFor = Exception.class)
	@Override
	public UtilResponse updateKcxl(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		String mess = "更新药房存量";
		try {
			List<Sbfb_kcxlModel> list = (List<Sbfb_kcxlModel>) msg.getParam().get("list");
			int ref = yfb_kcxlModelMapper.updateXl(list);
			// 判断插入结果
			if (ref != -1) {
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(mess + "失败");
				logger.error(mess + "接口【YfbKcglKcxlCspServiceImpl QueryKcxl-insertBatch】" + mess + "单保存失败");
				// 抛出异常，回滚操作
				throw new Exception();
			}

			result.getResResult().put("ref", ref);

		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg(mess + "失败:" + e.getCause().getMessage());
			logger.error(mess + "接口【YfbKcglKcxlCspServiceImpl updateKcxl】发生异常", e);
			throw e;
		}
		return result;
	}

}
