package com.supx.csp.user.service;

import com.supx.comm.constants.UtilRequest;
import com.supx.comm.constants.UtilResponse;
import com.supx.comm.pojo.MemcachedKeyInfo;
import com.supx.csp.user.dao.MemcachedCacheMapper;
import com.supx.service.IMemcachedCspService;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Memcached缓存CSP服务实现
 * 迁移自jsg_frame项目，现在在jsg_csp中实现数据库操作
 */
@Service("memcachedCspServiceImpl")
@DubboService(interfaceClass = IMemcachedCspService.class)
public class MemcachedCspServiceImpl extends ServiceInvocationHelper implements IMemcachedCspService {
    private final Logger logger = LoggerFactory.getLogger(MemcachedCspServiceImpl.class);

    @Autowired
    private MemcachedCacheMapper memcachedMapper;

    @Override
    public UtilResponse queryMemKeyBySysNum(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            String sysNum = (String) msg.getParam().get("sysNum");
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("sysNum", sysNum);

            List<MemcachedKeyInfo> list = this.memcachedMapper.queryMemKeyList(paramMap);
            result.getResResult().put("list", list);
        } catch (Exception e) {
            result.setErrorCode("-999999");
            this.logger.error("调用查询Memcached缓存键接口【MemcachedCspServiceImpl queryMemKeyBySysNum】发生异常", e);
        }
        return result;
    }
}
