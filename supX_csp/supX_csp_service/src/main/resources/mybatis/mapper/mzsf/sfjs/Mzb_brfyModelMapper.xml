<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supx.csp.mzsf.sfjs.dao.New1Mzb_brfyModelMapper">
    <resultMap id="BaseResultMap" type="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        <id column="FYJLID" jdbcType="VARCHAR" property="fyjlid"/>
        <result column="RYGHXH" jdbcType="VARCHAR" property="ryghxh"/>
        <result column="RYBRID" jdbcType="VARCHAR" property="rybrid"/>
        <result column="BRXM" jdbcType="VARCHAR" property="brxm"/>
        <result column="RYFBBM" jdbcType="VARCHAR" property="ryfbbm"/>
        <result column="RYBXLBBM" jdbcType="VARCHAR" property="rybxlbbm"/>
        <result column="FYLB" jdbcType="VARCHAR" property="fylb"/>
        <result column="MXFYXMBM" jdbcType="VARCHAR" property="mxfyxmbm"/>
        <result column="CZYBM" jdbcType="VARCHAR" property="czybm"/>
        <result column="SFSJ" jdbcType="TIMESTAMP" property="sfsj"/>
        <result column="FYSL" jdbcType="DECIMAL" property="fysl"/>
        <result column="FYDJ" jdbcType="DECIMAL" property="fydj"/>
        <result column="FYJE" jdbcType="DECIMAL" property="fyje"/>
        <result column="YZLX" jdbcType="VARCHAR" property="yzlx"/>
        <result column="YZHM" jdbcType="VARCHAR" property="yzhm"/>
        <result column="YZXH" jdbcType="DECIMAL" property="yzxh"/>
        <result column="FPHM" jdbcType="VARCHAR" property="fphm"/>
        <result column="MZYS" jdbcType="VARCHAR" property="mzys"/>
        <result column="MZKS" jdbcType="VARCHAR" property="mzks"/>
        <result column="ZXKS" jdbcType="VARCHAR" property="zxks"/>
        <result column="YFBM" jdbcType="VARCHAR" property="yfbm"/>
        <result column="SFJS" jdbcType="VARCHAR" property="sfjs"/>
        <result column="SFTF" jdbcType="VARCHAR" property="sftf"/>
        <result column="TFID" jdbcType="VARCHAR" property="tfid"/>
        <result column="RYMZZD" jdbcType="VARCHAR" property="rymzzd"/>
        <result column="RYJKPZH" jdbcType="VARCHAR" property="ryjkpzh"/>
        <result column="RYJSJLID" jdbcType="VARCHAR" property="ryjsjlid"/>
        <result column="YWCKBH" jdbcType="VARCHAR" property="ywckbh"/>
        <result column="ZHFYBH" jdbcType="VARCHAR" property="zhfybh"/>
        <result column="KSKFBZ" jdbcType="VARCHAR" property="kskfbz"/>
        <result column="KSKFRY" jdbcType="VARCHAR" property="kskfry"/>
        <result column="KSKFRQ" jdbcType="TIMESTAMP" property="kskfrq"/>
        <result column="TFCZY" jdbcType="VARCHAR" property="tfczy"/>
        <result column="IFBANK" jdbcType="VARCHAR" property="ifbank"/>
        <result column="BANKZFLS" jdbcType="VARCHAR" property="bankzfls"/>
        <result column="JYXH" jdbcType="VARCHAR" property="jyxh"/>
        <result column="BZSM" jdbcType="VARCHAR" property="bzsm"/>
        <result column="ZFBZ" jdbcType="VARCHAR" property="zfbz"/>
        <result column="ZFRY" jdbcType="VARCHAR" property="zfry"/>
        <result column="ZFRQ" jdbcType="TIMESTAMP" property="zfrq"/>
        <result column="RYFBMC" jdbcType="VARCHAR" property="ryfbmc"/>
        <result column="FYLBMC" jdbcType="VARCHAR" property="fylbmc"/>
        <result column="MXFYXMMC" jdbcType="VARCHAR" property="mxfyxmmc"/>
        <result column="CZYXM" jdbcType="VARCHAR" property="czyxm"/>
        <result column="MZYSXM" jdbcType="VARCHAR" property="mzysxm"/>
        <result column="MZKSMC" jdbcType="VARCHAR" property="mzksmc"/>
        <result column="ZXKSMC" jdbcType="VARCHAR" property="zxksmc"/>
        <result column="YFMC" jdbcType="VARCHAR" property="yfmc"/>
        <result column="ZHFYMC" jdbcType="VARCHAR" property="zhfymc"/>
        <result column="TFCZYXM" jdbcType="VARCHAR" property="tfczyxm"/>
        <result column="ZFRYXM" jdbcType="VARCHAR" property="zfryxm"/>
        <result column="DZCFBZ" jdbcType="VARCHAR" property="dzcfbz"/>
        <result column="YHBL" jdbcType="DECIMAL" property="yhbl"/>
        <result column="YHJE" jdbcType="DECIMAL" property="yhje"/>
        <result column="ZHFYBZ" jdbcType="VARCHAR" property="zhfybz"/>
        <result column="SQSJ" jdbcType="TIMESTAMP" property="sqsj"/>
        <result column="RYBXLBMC" jdbcType="VARCHAR" property="rybxlbmc"/>
        <result column="YWCKMC" jdbcType="VARCHAR" property="ywckmc"/>
        <result column="YZFL" jdbcType="VARCHAR" property="yzfl"/>
        <result column="DYCS" jdbcType="VARCHAR" property="dycs"/>
        <result column="SQYS" jdbcType="VARCHAR" property="sqys"/>
        <result column="SQYSXM" jdbcType="VARCHAR" property="sqysxm"/>
        <result column="YLKH" jdbcType="VARCHAR" property="ylkh"/>
        <result column="YLKLX" jdbcType="VARCHAR" property="ylklx"/>
        <result column="LCZD" jdbcType="VARCHAR" property="lczd"/>
        <result column="JCMS" jdbcType="VARCHAR" property="jcms"/>
        <result column="JCBW" jdbcType="VARCHAR" property="jcbw"/>
        <result column="LCZZ" jdbcType="VARCHAR" property="lczz"/>
        <result column="BBSM" jdbcType="VARCHAR" property="bbsm"/>
        <result column="JYMD" jdbcType="VARCHAR" property="jymd"/>
        <result column="JYBB" jdbcType="VARCHAR" property="jybb"/>
        <result column="FFYLB" jdbcType="VARCHAR" property="ffylb"/>
    </resultMap>

    <!-- 打印发票 -->
    <resultMap id="BaseResultDyMap" type="com.supx.csp.api.mzsf.sfjs.pojo.MzsfFpdyModel">
        <result column="fphm" property="fphm" jdbcType="VARCHAR"/>
        <result column="fyhj" property="fyhj" jdbcType="DECIMAL"/>
        <result column="qtzf" property="qtzf" jdbcType="DECIMAL"/>
        <result column="xjzf" property="xjzf" jdbcType="DECIMAL"/>
        <collection property="brfyPrintList" column="fphm" ofType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfy_print">
            <result column="ghxh" property="ghxh" jdbcType="VARCHAR"/>
            <result column="jsjlid" property="jsjlid" jdbcType="VARCHAR"/>
            <result column="brxm" property="brxm" jdbcType="VARCHAR"/>
            <result column="fbbm" property="fbbm" jdbcType="VARCHAR"/>
            <result column="fbmc" property="fbmc" jdbcType="VARCHAR"/>
            <result column="bxlb" property="bxlb" jdbcType="VARCHAR"/>
            <result column="bxlbmc" property="bxlbmc" jdbcType="VARCHAR"/>
            <result column="xmmc" property="xmmc" jdbcType="VARCHAR"/>
            <result column="xmbm" property="xmbm" jdbcType="VARCHAR"/>
            <result column="czyxm" property="czyxm" jdbcType="VARCHAR"/>
            <result column="czybm" property="czybm" jdbcType="VARCHAR"/>
            <result column="sfsj" property="sfsj" jdbcType="TIMESTAMP"/>
            <result column="fysl" property="fysl" jdbcType="DECIMAL"/>
            <result column="fydj" property="fydj" jdbcType="DECIMAL"/>
            <result column="fyje" property="fyje" jdbcType="DECIMAL"/>
            <result column="fyhj" property="fyhj" jdbcType="DECIMAL"/>
            <result column="tczf" property="tczf" jdbcType="DECIMAL"/>
            <result column="grzf" property="grzf" jdbcType="DECIMAL"/>
            <result column="qtzf" property="qtzf" jdbcType="DECIMAL"/>
            <result column="xjzf" property="xjzf" jdbcType="DECIMAL"/>
            <result column="mzys" property="mzys" jdbcType="VARCHAR"/>
            <result column="mzysxm" property="mzysxm" jdbcType="VARCHAR"/>
            <result column="mzks" property="mzks" jdbcType="VARCHAR"/>
            <result column="mzksmc" property="mzksmc" jdbcType="VARCHAR"/>
            <result column="zxks" property="zxks" jdbcType="VARCHAR"/>
            <result column="zxksmc" property="zxksmc" jdbcType="VARCHAR"/>
            <result column="fphm" property="fphm" jdbcType="VARCHAR"/>
            <result column="cfh" property="cfh" jdbcType="VARCHAR"/>
            <result column="brxbmc" property="brxbmc" jdbcType="VARCHAR"/>
            <result column="ksdz" property="ksdz" jdbcType="VARCHAR"/>
            <result column="fyck" property="fyck" jdbcType="VARCHAR"/>
            <result column="yfbm" property="yfbm" jdbcType="VARCHAR"/>
            <result column="yfmc" property="yfmc" jdbcType="VARCHAR"/>
            <result column="zhfybh" property="zhfybh" jdbcType="VARCHAR"/>
            <result column="zhfymc" property="zhfymc" jdbcType="VARCHAR"/>
            <result column="ybkh" property="ybkh" jdbcType="VARCHAR"/>
            <result column="brxb" property="brxb" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
    FYJLID, RYGHXH, RYBRID, BRXM, RYFBBM, RYBXLBBM, FYLB, MXFYXMBM, CZYBM, SFSJ, FYSL,
    FYDJ, FYJE, YZLX, YZHM, YZXH, FPHM, MZYS, MZKS, ZXKS, YFBM, SFJS, SFTF, TFID, RYMZZD,
    RYJKPZH, RYJSJLID, YWCKBH, ZHFYBH, KSKFBZ, KSKFRY, KSKFRQ, TFCZY, IFBANK, BANKZFLS,
    JYXH, BZSM, ZFBZ, ZFRY, ZFRQ, RYFBMC, FYLBMC, MXFYXMMC, CZYXM, MZYSXM, MZKSMC, ZXKSMC,
    YFMC, ZHFYMC, TFCZYXM, ZFRYXM, DZCFBZ, YHBL, YHJE, ZHFYBZ, SQSJ,RYBXLBMC,YWCKMC,YZFL,DYCS,SQYS,SQYSXM
  </sql>

    <!-- 首页功能 -收费-->
    <select id="sfshow" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzsf_Sy_sfShowModel"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select sum(jrsf) jrsf,sum(jrtf) jrtf,sum(dsf) dsf,sum(ysf) ysf from (
        select count(distinct sf.ryghxh)jrsf,0 jrtf,0 dsf,0 ysf from mzb_jsjl sf where sf.fyhj&gt;0 and
        sf.yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and sf.jsrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and sf.jsrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="czybm != null and czybm != '' ">
            and sf.czybm =#{czybm ,jdbcType=VARCHAR}
        </if>
        union all
        select 0 jrsf,count(distinct sf.ryghxh) jrtf,0 dsf,0 ysf from mzb_jsjl sf where sf.fyhj&lt;0 and
        sf.yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and sf.jsrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and sf.jsrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="czybm != null and czybm != '' ">
            and sf.czybm =#{czybm ,jdbcType=VARCHAR}
        </if>
        union all
        select 0 jrsf,0 jrtf,count(distinct gh.ghxh) dsf,0 ysf from ghb_brgh gh where thbz='0'
        and not exists (select 1 from mzb_jsjl js where js.ryghxh=gh.ghxh and js.yljgbm=gh.yljgbm)
        and (exists (select 1 from mzb_brfy fy where fy.zfbz='0' and fy.sfjs='0' and fy.ryghxh=gh.ghxh and
        fy.yljgbm=gh.yljgbm ) or
        exists (select 1 from yfb_ypcf cf where cf.kfbz='0' and cf.bah=gh.ghxh and cf.yljgbm=gh.yljgbm) ) and
        gh.yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and gh.ghrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and gh.ghrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        union all
        select 0 jrsf,0 jrtf, 0 dsf,count(distinct gh.ghxh) ysf from ghb_brgh gh where thbz='0'
        and exists (select 1 from mzb_jsjl js where js.ryghxh=gh.ghxh and js.yljgbm=gh.yljgbm) and
        gh.yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and gh.ghrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and gh.ghrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        )
    </select>

    <!-- 首页功能 -收费统计-->
    <select id="sftjshow" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzsf_Sy_sftjShowModel"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select sum(jrsfzje) jrsfzje,sum(jrtfzj) jrtfzj from (
        select sum(fyhj) jrsfzje ,0 jrtfzj from mzb_jsjl where fyhj&gt;0 and yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and jsrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and jsrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="czybm != null and czybm != '' ">
            and czybm =#{czybm ,jdbcType=VARCHAR}
        </if>
        union all
        select 0 jrsfzje,sum(fyhj) jrtfzj from mzb_jsjl where fyhj&lt;0 and yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and jsrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and jsrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="czybm != null and czybm != '' ">
            and czybm =#{czybm ,jdbcType=VARCHAR}
        </if>
        )
    </select>

    <!-- 首页功能 -门诊上缴-->
    <select id="mzsjshow" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzsf_Sy_mzsjShowModel"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select sum(dsjzje) dsjzje,sum(dsjxj) dsjxj from (
        select sum(fyhj) dsjzje ,0 dsjxj from mzb_jsjl where ryjkpzh is null and yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and jsrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and jsrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="czybm != null and czybm != '' ">
            and czybm =#{czybm ,jdbcType=VARCHAR}
        </if>
        union all
        select 0 dsjzje,sum(xjzf) dsjxj from mzb_jsjl where ryjkpzh is null and yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and jsrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and jsrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="czybm != null and czybm != '' ">
            and czybm =#{czybm ,jdbcType=VARCHAR}
        </if>
        )
    </select>

    <!-- 首页功能 -划价-->
    <select id="hjshow" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzsf_Sy_hjShowModel"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select count(distinct bah) from yfb_ypcf where zfbz='0' and kfbz='1' and bzsm='处方划价生成' and
        yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="endrq != null ">
            and kfrq &lt; #{endrq,jdbcType=DATE}
        </if>
        <if test="beginrq != null ">
            and kfrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="czybm != null and czybm != '' ">
            and kfry =#{czybm ,jdbcType=VARCHAR}
        </if>
    </select>


    <select id="queryMzbrfymx" resultType="com.supx.csp.api.mzsf.cxtj.MzbrfyMxModel" parameterType="java.util.List">
        SELECT zhfymc,mxfyxmmc,fydj,fysl,fyje,zxksmc,yzfl,fylbmc,dw from (
        select
        '' as zhfymc,
        pf.ypmc as mxfyxmmc,
        pf.yplj as fydj,
        pf.cfyl as fysl,
        pf.fysl*pf.yplj as fyje,
        (select distinct zxksmc from mzb_brfy brfy where brfy.yzhm=pf.cfh) as zxksmc,
        (select distinct yzfl from mzb_brfy brfy where brfy.yzhm=pf.cfh) as yzfl,
        pf.ypzlmc as fylbmc,
        pf.yfdwmc as dw
        from yfb_yppf pf
        where pf.yljgbm = #{yljgbm,jdbcType=VARCHAR} and pf.cfh in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        union all
        select zhfymc,'' as mxfyxmmc,sum(fydj) as fydj,1 as fysl, sum(fyje) as fyje,max(zxksmc) as zxksmc,max(yzfl) as
        yzfl,max(fylbmc) as fylbmc,'次' as dw from (
        SELECT
        brfy.zhfymc,
        brfy.mxfyxmmc,
        brfy.fydj,
        brfy.fysl,
        brfy.fyje,
        brfy.zxksmc,
        brfy.yzfl,
        brfy.fylbmc,
        xm.fygg as dw
        FROM mzb_brfy brfy left join gyb_mxfyxm xm on brfy.mxfyxmbm = xm.mxfybm
        WHERE brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR} and brfy.yzfl ='4' and
        brfy.yzhm in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) a group by zhfymc,dw
        union all
        SELECT
        '' as zhfymc,
        brfy.mxfyxmmc,
        brfy.fydj,
        brfy.fysl,
        brfy.fyje,
        brfy.zxksmc,
        brfy.yzfl,
        brfy.fylbmc,
        xm.fygg as dw
        FROM mzb_brfy brfy left join gyb_mxfyxm xm on brfy.mxfyxmbm = xm.mxfybm
        WHERE brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
        and ((brfy.yzfl !='4' and brfy.yzfl !='2') or brfy.yzfl is null)
        and brfy.yzhm in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) a order by zxksmc
    </select>

    <!--	<select id="queryMzbrfyLz" resultType="cxtj.mzsf.api.com.supx.csp.MzbrfyLzMxModel" parameterType="java.util.HashMap">-->
    <!--		select * from (-->
    <!--		SELECT pf.ypmc    AS mxfyxmmc,-->
    <!--		  pf.ypbm         AS mxfyxmbm,-->
    <!--		  pf.yplj         AS fydj,-->
    <!--		  pf.cfyl         AS fysl,-->
    <!--		  pf.fysl*pf.yplj AS fyje,-->
    <!--		  fy.yljgbm,-->
    <!--		  fy.ryghxh,-->
    <!--		  fy.fylb-->
    <!--		FROM yfb_yppf pf-->
    <!--		INNER JOIN mzb_brfy fy-->
    <!--		ON pf.yljgbm =fy.yljgbm-->
    <!--		AND pf.cfh   = fy.yzhm-->
    <!--		AND fy.yljgbm =#{yljgbm}-->
    <!--		AND fy.fylb =#{fylb}-->
    <!--		AND fy.ryghxh =#{ryghxh}-->
    <!--		UNION-->
    <!--		SELECT zhfymc AS mxfyxmmc ,-->
    <!--		  zhfybh      AS mxfyxmbm ,-->
    <!--		  SUM(fydj)   AS fydj,-->
    <!--		  SUM(fysl),-->
    <!--		  SUM(fyje),-->
    <!--		  yljgbm,-->
    <!--		  ryghxh,-->
    <!--		  fylb-->
    <!--		FROM mzb_brfy-->
    <!--		WHERE zhfybh IS NOT NULL-->
    <!--		AND yljgbm =#{yljgbm}-->
    <!--		AND fylb =#{fylb}-->
    <!--		AND ryghxh =#{ryghxh}-->
    <!--		GROUP BY zhfymc,-->
    <!--		  zhfybh,-->
    <!--		  yljgbm,-->
    <!--		  ryghxh,-->
    <!--		  fylb-->
    <!--		UNION-->
    <!--		SELECT mxfyxmmc ,-->
    <!--		  mxfyxmbm ,-->
    <!--		  fydj,-->
    <!--		  fysl,-->
    <!--		  fyje,-->
    <!--		  yljgbm,-->
    <!--		  ryghxh,-->
    <!--		  fylb-->
    <!--		FROM mzb_brfy-->
    <!--		WHERE zhfybh IS NULL-->
    <!--		AND yljgbm =#{yljgbm}-->
    <!--		AND fylb =#{fylb}-->
    <!--		AND ryghxh =#{ryghxh}-->
    <!--		) e where fylb not in ('001','002');-->
    <!--	</select>-->


    <!-- 针对报表科室费用汇总 -->
    <select id="queryKshz" resultMap="BaseResultMap" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select ${zxks},${zxksmc},czybm,czyxm,fylb,fylbmc,sum(fyje) fyje from mzb_brfy
        <where>
            sfjs='1' and yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="endrq != null ">
                and (sfsj &lt; #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (sfsj &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="czybm != null and czybm != '' ">
                and czybm=#{czybm ,jdbcType=VARCHAR}
            </if>
        </where>
        group by ${zxks},${zxksmc},czybm,czyxm,fylb,fylbmc
    </select>

    <!-- 针对门诊交款根据费类别和入院交款凭证号查询门诊费用明细 -->
    <select id="queryByFylbAndRyjkpzh" resultMap="BaseResultMap"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
  	select * from mzb_brfy
  	where yljgbm = #{yljgbm,jdbcType=VARCHAR}
  	and fylb=#{fylb,jdbcType=VARCHAR}
  	and ryjkpzh=#{ryjkpzh,jdbcType=VARCHAR}
  </select>

    <!-- 针对门诊交款根据费类别查询门诊费用明细 -->
    <select id="queryByFylb" resultMap="BaseResultMap" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
  	select * from mzb_brfy
  	where yljgbm = #{yljgbm,jdbcType=VARCHAR}
  	 and fylb=#{fylb,jdbcType=VARCHAR}
  	 and ryjkpzh is null
  	 and czybm = #{czybm,jdbcType=VARCHAR}
  	 and sfsj &lt;= #{sfsj,jdbcType=TIMESTAMP}
  </select>

    <!-- 针对门诊财务交款更新数据 -->
    <update id="updateByCwjk" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        update mzb_brfy
        <set>
            ryjkpzh = #{ryjkpzh,jdbcType=VARCHAR}
        </set>
        where yljgbm = #{yljgbm,jdbcType=VARCHAR}
        and ryjkpzh is null
        and sfjs = '1'
        and czybm = #{czybm,jdbcType=VARCHAR}
        and sfsj &lt;= #{sfsj,jdbcType=TIMESTAMP}
    </update>


    <!-- 针对取消门诊财务交款更新数据 -->
    <update id="updateByQxCwjk" parameterType="java.lang.String">
        update mzb_brfy
        <set>
            ryjkpzh = null
        </set>
        where yljgbm = #{yljgbm,jdbcType=VARCHAR} and ryjkpzh = #{ryjkpzh,jdbcType=VARCHAR}
    </update>


    <!-- 打印门诊发票信息 -->
    <select id="queryDyFpxx" resultMap="BaseResultDyMap"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select ghxh,brxm,fbbm,fbmc,bxlb,bxlbmc,xmbm,xmmc,czybm,czyxm,sfsj,fysl,fyje,round(fydj,2) fydj,
        cfh,fphm,mzys,mzysxm,mzks,mzksmc,zxks,zxksmc,yfbm,ksdz,yfmc,ybkzf qtzf,xjzf,ybkh,brxb,jsjlid from ( select
        a.ghxh,a.brxm,a.fbbm,
        a.fbmc,a.bxlb,a.bxlbmc,a.xmbm,a.xmmc,a.czybm,a.czyxm, a.sfsj,sum(a.fysl) fysl,sum(a.fyje) fyje,
        decode(sum(a.fysl),0,0,sum(a.fyje) / sum(a.fysl)) fydj,a.cfh,a.fphm,a.mzys,a.mzysxm,a.mzks,a.mzksmc,
        a.zxks,a.zxksmc,a.yfbm,b.ksdz,c.yfmc,d.ybkzf,d.xjzf,a.ybkh,a.brxb,d.jsjlid from ( select
        jbxx.brxb,jbxx.ybkh,brfy.ryghxh ghxh,case when jbxx.brxm is null then brfy.brxm else jbxx.brxm end
        brxm,brfy.ryfbbm fbbm,brfy.ryfbmc fbmc,brfy.rybxlbbm bxlb,
        brfy.rybxlbmc bxlbmc, case when brfy.zhfybh is null or brfy.zhfybh = '' then brfy.mxfyxmbm else brfy.zhfybh end
        xmbm,
        case when brfy.zhfybh is null or brfy.zhfybh = '' then brfy.mxfyxmmc else brfy.zhfymc end xmmc,
        brfy.czybm,brfy.czyxm,
        to_date(to_char(brfy.sfsj,'yyyy-mm-dd hh24:mi'),'yyyy-mm-dd hh24:mi') sfsj,brfy.fysl,brfy.fyje,brfy.yzhm
        cfh,brfy.yljgbm,
        brfy.fphm,brfy.mzys,brfy.mzysxm,brfy.mzks,brfy.mzksmc,brfy.zxks,brfy.zxksmc,brfy.yfbm,brfy.ryjsjlid
        from mzb_brfy brfy
        left join gyb_brjbxx jbxx on jbxx.brid=brfy.rybrid and jbxx.yljgbm=brfy.yljgbm
        where brfy.yljgbm =#{yljgbm,jdbcType=VARCHAR} and brfy.sfjs = '1'
        and brfy.zfbz='0' and brfy.sftf='0'
        <if test="ryjsjlid != null">
            and brfy.ryjsjlid = #{ryjsjlid,jdbcType=VARCHAR}
        </if>
        <if test="fphm != null">
            and brfy.fphm = #{fphm,jdbcType=VARCHAR}
        </if>
        <if test="ryghxh != null">
            and brfy.ryghxh = #{ryghxh,jdbcType=VARCHAR}
        </if>
        ) a left join gyb_ksbm b on a.zxks = b.ksbm and a.yljgbm=b.yljgbm
        left join yfb_yf c on a.yfbm = c.yfbm and a.yljgbm=c.yljgbm
        left join mzb_jsjl d on d.jsjlid=a.ryjsjlid and a.yljgbm=d.yljgbm
        group by a.ghxh,a.brxm,a.fbbm,a.fbmc,a.bxlb,a.bxlbmc,a.xmbm,a.xmmc,a.brxb,
        a.czybm,a.czyxm,
        a.sfsj,a.cfh,a.fphm,a.mzys,a.mzysxm,a.mzks,a.mzksmc,a.zxks,a.zxksmc,a.yfbm,b.ksdz,c.yfmc,d.ybkzf,d.xjzf,a.ybkh,d.jsjlid
        order by fphm ) fy
    </select>

    <!-- 打印门诊发票信息(多笔结算记录打印) -->
    <select id="queryDyFpxxList" resultMap="BaseResultDyMap"
            parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator="union">
            select ghxh,brxm,fbbm,fbmc,bxlb,bxlbmc,xmbm,xmmc,czybm,czyxm,sfsj,fysl,fyje,round(fydj,2) fydj,
            cfh,fphm,mzys,mzysxm,mzks,mzksmc,zxks,zxksmc,yfbm,ksdz,yfmc,ybkzf qtzf,xjzf,ybkh,brxb,jsjlid from ( select
            a.ghxh,a.brxm,a.fbbm,
            a.fbmc,a.bxlb,a.bxlbmc,a.xmbm,a.xmmc,a.czybm,a.czyxm, a.sfsj,sum(a.fysl) fysl,sum(a.fyje) fyje,
            decode(sum(a.fysl),0,0,sum(a.fyje) / sum(a.fysl)) fydj,a.cfh,a.fphm,a.mzys,a.mzysxm,a.mzks,a.mzksmc,
            a.zxks,a.zxksmc,a.yfbm,b.ksdz,c.yfmc,d.ybkzf,d.xjzf,a.ybkh,a.brxb,d.jsjlid from ( select
            jbxx.brxb,jbxx.ybkh,brfy.ryghxh ghxh,case when jbxx.brxm is null then brfy.brxm else jbxx.brxm end
            brxm,brfy.ryfbbm fbbm,brfy.ryfbmc fbmc,brfy.rybxlbbm bxlb,
            brfy.rybxlbmc bxlbmc, case when brfy.zhfybh is null or brfy.zhfybh = '' then brfy.mxfyxmbm else brfy.zhfybh end
            xmbm,
            case when brfy.zhfybh is null or brfy.zhfybh = '' then brfy.mxfyxmmc else brfy.zhfymc end xmmc,
            brfy.czybm,brfy.czyxm,
            to_date(to_char(brfy.sfsj,'yyyy-mm-dd hh24:mi'),'yyyy-mm-dd hh24:mi') sfsj,brfy.fysl,brfy.fyje,brfy.yzhm
            cfh,brfy.yljgbm,
            brfy.fphm,brfy.mzys,brfy.mzysxm,brfy.mzks,brfy.mzksmc,brfy.zxks,brfy.zxksmc,brfy.yfbm,brfy.ryjsjlid
            from mzb_brfy brfy
            left join gyb_brjbxx jbxx on jbxx.brid=brfy.rybrid and jbxx.yljgbm=brfy.yljgbm
            where brfy.yljgbm =#{item.yljgbm,jdbcType=VARCHAR} and brfy.sfjs = '1'
            and brfy.zfbz='0' and brfy.sftf='0'
            <if test="item.ryjsjlid != null">
                and brfy.ryjsjlid = #{item.ryjsjlid,jdbcType=VARCHAR}
            </if>
            <if test="item.fphm != null">
                and brfy.fphm = #{item.fphm,jdbcType=VARCHAR}
            </if>
            <if test="item.ryghxh != null">
                and brfy.ryghxh = #{item.ryghxh,jdbcType=VARCHAR}
            </if>
            ) a left join gyb_ksbm b on a.zxks = b.ksbm and a.yljgbm=b.yljgbm
            left join yfb_yf c on a.yfbm = c.yfbm and a.yljgbm=c.yljgbm
            left join mzb_jsjl d on d.jsjlid=a.ryjsjlid and a.yljgbm=d.yljgbm
            group by a.ghxh,a.brxm,a.fbbm,a.fbmc,a.bxlb,a.bxlbmc,a.xmbm,a.xmmc,a.brxb,
            a.czybm,a.czyxm,
            a.sfsj,a.cfh,a.fphm,a.mzys,a.mzysxm,a.mzks,a.mzksmc,a.zxks,a.zxksmc,a.yfbm,b.ksdz,c.yfmc,d.ybkzf,d.xjzf,a.ybkh,d.jsjlid
            order by fphm ) fy
        </foreach>
    </select>

    <select id="queryDyFpxx111" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfy_print"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select a.*,decode(a.fysl,0,0,a.fyje / a.fysl) fydj,b.ksdz,c.yfmc from (
        select ryghxh ghxh,brxm,ryfbbm fbbm,ryfbmc fbmc,rybxlbbm bxlb,rybxlbmc bxlbmc,mxfyxmbm xmbm,mxfyxmmc
        xmmc,czybm,czyxm,
        to_date(to_char(sfsj,'yyyy-mm-dd hh24:mi'),'yyyy-mm-dd hh24:mi') sfsj,sum(fysl) fysl,sum(fyje) fyje,yzhm cfh,
        fphm,mzys,mzysxm,mzks,mzksmc,zxks,zxksmc,yfbm,zhfybh,zhfymc from mzb_brfy
        where yljgbm = #{yljgbm,jdbcType=VARCHAR} and sfjs = '1' and fysl &gt; 0 and sftf='0'
        <if test="ryjsjlid != null">
            and ryjsjlid = #{ryjsjlid,jdbcType=VARCHAR}
        </if>
        <if test="fphm != null">
            and fphm = #{fphm,jdbcType=VARCHAR}
        </if>
        group by ryghxh,brxm,ryfbbm,ryfbmc,rybxlbbm,rybxlbmc,mxfyxmbm,mxfyxmmc,czybm,czyxm,
        to_char(sfsj,'yyyy-mm-dd hh24:mi'),yzhm,fphm,mzys,mzysxm,mzks,mzksmc,zxks,zxksmc,yfbm,zhfybh,zhfymc
        ) a left join gyb_ksbm b on a.zxks = b.ksbm left join yfb_yf c on a.yfbm = c.yfbm
        order by fphm
    </select>


    <!-- 根据操作员编码查询操作员应总共收费金额,和应交金额（根据参数参数类型判断） -->
    <select id="queryByCzybm" resultType="Double" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select nvl(sum(fy.fyje),0) from mzb_brfy fy
        left join ghb_brgh gh on gh.ghxh = fy.ryghxh and gh.yljgbm = fy.yljgbm
        left join gyb_brjbxx jb on jb.yljgbm = gh.yljgbm and jb.brid = gh.brid
        <where>
            fy.sfjs='1' and fy.zfbz='0' and fy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="sfjk == 1">
                and fy.ryjkpzh is not null
            </if>
            <if test="sftf == 1">
                and (fy.fyje &lt; 0)
            </if>
            <if test="endrq != null ">
                and (fy.sfsj &lt; #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (fy.sfsj &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="czybm != null and czybm != '' ">
                and fy.czybm=#{czybm ,jdbcType=VARCHAR}
            </if>
            <if test="sfsj!=null and sfsj!=''">
                and to_char(fy.sfsj,'yyyy-mm') = to_char(#{sfsj,jdbcType=TIMESTAMP},'yyyy-mm')
            </if>
            <if test="zxks!=null and zxks!=''">
                and fy.zxks = #{zxks}
            </if>
            <if test="yfbm!=null and yfbm!=''">
                and fy.yfbm is not null
            </if>
            <if test="ryfbbm!=null and ryfbbm!=''">
                and fy.ryfbbm = #{ryfbbm}
            </if>
            <if test="parm!=null and parm!=''.toString()">
                and (fy.brxm like '%${parm}%' or jb.pydm like '%'|| upper(#{parm,jdbcType=VARCHAR}) ||'%' or fy.RYGHXH
                like '%${parm}%' or fy.FPHM like '%${parm}%')
            </if>
        </where>
    </select>

    <!-- 门诊收费统计查询 -->
    <resultMap id="MzsfTjcxMap" type="java.util.HashMap">
        <result property="fyhj" column="FYHJ"/>
        <result property="ybkzf" column="YBKZF"/>
        <result property="xjzf" column="XJZF"/>
    </resultMap>
    <select id="queryMzsfTjcxMap" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel"
            resultMap="MzsfTjcxMap">
        select nvl(sum(FYHJ),0) fyhj,nvl(sum(YBKZF),0) YBKZF,nvl(sum(XJZF),0) XJZF from mzb_jsjl
        <where>
            yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="endrq != null ">
                and (JSRQ &lt; #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (JSRQ &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="czybm != null and czybm != '' ">
                and CZYBM=#{czybm ,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <!-- 根据挂号或者卡号查询患者的未收费信息集合 -->
    <select id="queryByWsf" resultMap="BaseResultMap" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select brfy.*,mx.ffylb from MZB_BRFY brfy inner join gyb_mxfyxm mx on mx.mxfybm=brfy.mxfyxmbm
        and mx.yljgbm=brfy.yljgbm
        <where>
            brfy.sfjs='0' and brfy.zfbz='0' and brfy.YLJGBM = #{yljgbm,jdbcType=VARCHAR}
            and not exists(
                select 1 from yfb_ypcf cf where brfy.yzhm = cf.cfh and brfy.ryghxh = cf.bah
                    and cf.sflzcf='1'
            )
            and (tjtjbz='0' or (tjtjbz='1' and tjdjbz='1'))
            <if test="ryghxh != null and ryghxh != '' ">
                and brfy.ryghxh = #{ryghxh ,jdbcType=VARCHAR}
            </if>
            <if test="ylkh != null and ylkh != ''">
                and exists (select 1 from gyb_brylkxx ylk where ylk.brid=brfy.rybrid and ylk.zfbz='0'
                and (ylk.ylkh = #{ylkh,jdbcType=VARCHAR}))
            </if>
        </where>
        order by brfy.yzhm,brfy.sfsj
    </select>


    <!-- 非挂号费用 -->
    <select id="queryNoGhf" resultMap="BaseResultMap" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select
        <include refid="Base_Column_List"/>
        from MZB_BRFY
        where YZLX != #{yzlx,jdbcType=VARCHAR} and
        SFJS = '1' and zfbz='0' and
        RYGHXH = #{ryghxh,jdbcType=VARCHAR} and
        YLJGBM = #{yljgbm,jdbcType=VARCHAR}
    </select>

    <!-- 查询医嘱费用 -->
    <select id="queryYzfy" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select
        <include refid="Base_Column_List"/>
        from MZB_BRFY
        where zfbz='0' and yzhm = #{yzhm ,jdbcType=VARCHAR} and YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        <if test="yzlx != null and yzlx != '' ">
            and yzlx = #{yzlx ,jdbcType=VARCHAR}
        </if>
        <if test="ryghxh != null and ryghxh != '' ">
            and ryghxh = #{ryghxh,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 查询病历医嘱费用 -->
    <select id="queryBlYzfy" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select distinct fy.yzhm,
        decode(fy.zhfybh, null, fy.mxfyxmbm, fy.zhfybh) mxfyxmbm,
        decode(fy.zhfymc, null, fy.mxfyxmmc, fy.zhfymc) mxfyxmmc,
        gh.ghks,
        gh.ghksmc,
        fy.czybm,
        fy.czyxm,
        fy.fysl,
        fy.bzsm,
        fy.zhfybh                                       zhfybm
        from MZB_BRFY fy
        inner join GHB_BRGH gh on fy.YLJGBM = gh.YLJGBM and fy.RYGHXH = gh.GHXH
        where fy.zfbz='0' and fy.yzhm = #{yzhm ,jdbcType=VARCHAR} and fy.YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        <if test="ryghxh != null and ryghxh != '' ">
            and ryghxh = #{ryghxh,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 挂号费用 -->
    <select id="queryYesGhf" resultMap="BaseResultMap" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        select
        <include refid="Base_Column_List"/>
        from MZB_BRFY
        where YZLX = '1' and
        SFJS = '1' and
        SFTF = '0' and
        ZFBZ='0' and
        FYSL > 0 and
        RYGHXH = #{ryghxh,jdbcType=VARCHAR} AND YLJGBM = #{yljgbm,jdbcType=VARCHAR}
    </select>

    <!-- 查一条 -->
    <select id="selectKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from MZB_BRFY
        where zfbz='0' AND FYJLID = #{fyjlid,jdbcType=VARCHAR} and YLJGBM = #{yljgbm,jdbcType=VARCHAR}
    </select>

    <!-- 分页查询 -->
    <select id="query" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel"
            parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        <!-- SELECT fy.*,jl.bxjsh FROM MZB_BRFY fy
        left join mzb_jsjl jl on jl.jsjlid=fy.ryjsjlid and jl.yljgbm=fy.yljgbm  -->
        SELECT distinct nh.inpid,fy.FYJLID ,fy.RYGHXH ,fy.RYBRID ,case when jbxx.brxm is null then fy.brxm else jbxx.brxm end
        brxm,fy.RYFBBM ,fy.RYBXLBBM ,jbxx.sfzjhm,
        fy.FYLB ,fy.MXFYXMBM ,fy.CZYBM ,fy.SFSJ ,fy.FYSL ,fy.FYDJ ,fy.FYJE ,fy.YZLX ,
        fy.YZHM ,fy.YZXH ,fy.FPHM ,fy.MZYS ,fy.MZKS ,fy.ZXKS ,fy.YFBM ,fy.SFJS ,
        fy.SFTF ,fy.TFID ,fy.RYMZZD ,fy.RYJKPZH ,fy.RYJSJLID ,fy.YWCKBH ,fy.ZHFYBH ,
        fy.KSKFBZ ,fy.KSKFRY ,fy.KSKFRQ ,fy.TFCZY ,fy.IFBANK ,fy.BANKZFLS ,fy.JYXH ,
        fy.BZSM ,fy.ZFBZ ,fy.ZFRY ,fy.ZFRQ ,fy.RYFBMC ,fy.FYLBMC ,fy.MXFYXMMC ,
        fy.CZYXM ,fy.MZYSXM ,fy.MZKSMC ,fy.ZXKSMC ,fy.YFMC ,fy.ZHFYMC ,fy.TFCZYXM ,
        fy.ZFRYXM ,fy.DZCFBZ ,fy.YHBL ,fy.YHJE ,fy.ZHFYBZ ,fy.SQSJ ,fy.RYBXLBMC ,
        fy.YWCKMC ,fy.SQYS ,fy.SQYSXM ,fy.DYCS ,fy.YLJGBM ,fy.YZFL ,fy.FYCK ,
        fy.JKSCBZ ,fy.LCZD ,fy.JCMS ,fy.JCBW ,fy.LCZZ ,fy.BBSM ,fy.JYMD ,fy.JYBB,fy.zxbz,fy.zxry,fy.zxsj,jl.xjzf,
        jl.bxjsh,decode(jl.qtzf,null,0,jl.qtzf) qtzf,decode(jl.ybkzf,null,0,jl.ybkzf)
        ybkzf,jl.pay_no payno,jl.pay_channel paychannel,jl.orderNo,jl.jsrq,jl.zflxbm,zflx.zflxjk,jl.yjyrq,zflx.zflxmc,fy.tjtfsqbz,fy.COSTID, fy.TJTJBZ, fy.TJDJBZ,  fy.TJTMH
        FROM MZB_BRFY fy
        left join mzb_jsjl jl on jl.jsjlid=fy.ryjsjlid and jl.yljgbm=fy.yljgbm
        left join gyb_brjbxx jbxx on jbxx.brid = fy.rybrid and jl.rybrid = jbxx.brid and jbxx.yljgbm = fy.yljgbm
        left join gyb_brylkxx  ylkxx on ylkxx.brid = fy.rybrid and ylkxx.yljgbm = fy.yljgbm
        left join gyb_zflx zflx on jl.zflxbm = zflx.zflxbm and jl.yljgbm = zflx.yljgbm
        left join GZNH_INHOSPITAL nh on fy.ryghxh = nh.zyh and fy.yljgbm = nh.yljgbm AND nh.zfbz = '0'
        <where>
            (fy.zfbz='0' AND fy.YLJGBM = #{yljgbm,jdbcType=VARCHAR})
            <if test="ryjsjlid != null and ryjsjlid != '' ">
                and fy.ryjsjlid = #{ryjsjlid,jdbcType=VARCHAR}
            </if>
            <if test="yzlx != null and yzlx != '' ">
                and fy.yzlx = #{yzlx ,jdbcType=VARCHAR}
            </if>
            <if test="yzhm != null and yzhm != '' ">
                and fy.yzhm = #{yzhm ,jdbcType=VARCHAR}
            </if>
            <if test="sfjs != null and sfjs != ''">
                and fy.sfjs = #{sfjs,jdbcType=VARCHAR}
            </if>
            <if test="ryghxh != null and ryghxh != '' ">
                and fy.ryghxh = #{ryghxh,jdbcType=VARCHAR}
            </if>
            <if test="sftf == 1">
                and (fy.fyje &lt; 0)
            </if>
            <if test="sftf == 0">
                and fy.sftf = #{sftf,jdbcType=VARCHAR} and fy.fyje &gt;= 0
            </if>
            <if test="brxm != null and brxm != '' ">
                <choose>
                    <when test="wbjm == '1'.toString()">
                        and (fy.brxm like '%'||#{brxm,jdbcType=VARCHAR}||'%' or upper(jbxx.wbjm) like
                        '%'||upper(#{brxm,jdbcType=VARCHAR})||'%'
                        or LTRIM(substr(fy.ryghxh,9),0) like '%'||#{brxm,jdbcType=VARCHAR}||'%' or ylkxx.ylkh like
                        '%${brxm}' )
                    </when>
                    <otherwise>
                        and (fy.brxm like '%'||#{brxm,jdbcType=VARCHAR}||'%' or jbxx.pydm like
                        '%'||upper(#{brxm,jdbcType=VARCHAR})||'%'
                        or LTRIM(substr(fy.ryghxh,9),0) like '%'||#{brxm,jdbcType=VARCHAR}||'%' or ylkxx.ylkh like
                        '%${brxm}' )
                    </otherwise>
                </choose>
            </if>
            <if test="fphm != null and fphm != '' ">
                <!-- and (fy.fphm = #{fphm,jdbcType=VARCHAR}) -->
                and (fy.fphm like '%'||#{fphm,jdbcType=VARCHAR}||'%')
            </if>
            <if test="endrq != null ">
                and (fy.sfsj &lt; #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (fy.sfsj &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="czybm != null and czybm != '' ">
                and fy.czybm=#{czybm ,jdbcType=VARCHAR}
            </if>
            <if test="parm!=null and parm!=''.toString()">
                <choose>
                    <when test="wbjm == '1'.toString()">
                        and (fy.brxm like '%${parm}%' or upper(jbxx.wbjm) like '%'|| upper(#{parm,jdbcType=VARCHAR})
                        ||'%' or fy.RYGHXH like '%${parm}%' or fy.FPHM like '%${parm}%')
                    </when>
                    <otherwise>
                        and (fy.brxm like '%${parm}%' or jbxx.pydm like '%'|| upper(#{parm,jdbcType=VARCHAR}) ||'%' or
                        fy.RYGHXH like '%${parm}%' or fy.FPHM like '%${parm}%')
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY fy.SFTF asc,LTRIM(substr(fy.ryghxh,9),0) asc,fy.sfsj desc,fy.fphm desc,fy.ZHFYBH asc
    </select>


    <!-- 删除 -->
    <delete id="delete" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            delete from MZB_BRFY
            where FYJLID = #{item.fyjlid,jdbcType=VARCHAR} AND YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
        </foreach>
    </delete>


    <!-- 插入 -->
    <insert id="insert" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            insert into MZB_BRFY
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.fyjlid != null">
                    FYJLID,
                </if>
                <if test="item.ryghxh != null">
                    RYGHXH,
                </if>
                <if test="item.rybrid != null">
                    RYBRID,
                </if>
                <if test="item.brxm != null">
                    BRXM,
                </if>
                <if test="item.ryfbbm != null">
                    RYFBBM,
                </if>
                <if test="item.rybxlbbm != null">
                    RYBXLBBM,
                </if>
                <if test="item.fylb != null">
                    FYLB,
                </if>
                <if test="item.mxfyxmbm != null">
                    MXFYXMBM,
                </if>
                <if test="item.czybm != null">
                    CZYBM,
                </if>
                <if test="item.sfsj != null">
                    SFSJ,
                </if>
                <if test="item.fysl != null">
                    FYSL,
                </if>
                <if test="item.fydj != null">
                    FYDJ,
                </if>
                <if test="item.fyje != null">
                    FYJE,
                </if>
                <if test="item.yzlx != null">
                    YZLX,
                </if>
                <if test="item.yzhm != null">
                    YZHM,
                </if>
                <if test="item.yzxh != null">
                    YZXH,
                </if>
                <if test="item.fphm != null">
                    FPHM,
                </if>
                <if test="item.mzys != null">
                    MZYS,
                </if>
                <if test="item.mzks != null">
                    MZKS,
                </if>
                <if test="item.zxks != null">
                    ZXKS,
                </if>
                <if test="item.yfbm != null">
                    YFBM,
                </if>
                <if test="item.sfjs != null">
                    SFJS,
                </if>
                <if test="item.sftf != null">
                    SFTF,
                </if>
                <if test="item.tfid != null">
                    TFID,
                </if>
                <if test="item.rymzzd != null">
                    RYMZZD,
                </if>
                <if test="item.ryjkpzh != null">
                    RYJKPZH,
                </if>
                <if test="item.ryjsjlid != null">
                    RYJSJLID,
                </if>
                <if test="item.ywckbh != null">
                    YWCKBH,
                </if>
                <if test="item.zhfybh != null">
                    ZHFYBH,
                </if>
                <if test="item.kskfbz != null">
                    KSKFBZ,
                </if>
                <if test="item.kskfry != null">
                    KSKFRY,
                </if>
                <if test="item.kskfrq != null">
                    KSKFRQ,
                </if>
                <if test="item.tfczy != null">
                    TFCZY,
                </if>
                <if test="item.ifbank != null">
                    IFBANK,
                </if>
                <if test="item.bankzfls != null">
                    BANKZFLS,
                </if>
                <if test="item.jyxh != null">
                    JYXH,
                </if>
                <if test="item.bzsm != null">
                    BZSM,
                </if>
                <if test="item.zfbz != null">
                    ZFBZ,
                </if>
                <if test="item.zfry != null">
                    ZFRY,
                </if>
                <if test="item.zfrq != null">
                    ZFRQ,
                </if>
                <if test="item.ryfbmc != null">
                    RYFBMC,
                </if>
                <if test="item.fylbmc != null">
                    FYLBMC,
                </if>
                <if test="item.mxfyxmmc != null">
                    MXFYXMMC,
                </if>
                <if test="item.czyxm != null">
                    CZYXM,
                </if>
                <if test="item.mzysxm != null">
                    MZYSXM,
                </if>
                <if test="item.mzksmc != null">
                    MZKSMC,
                </if>
                <if test="item.zxksmc != null">
                    ZXKSMC,
                </if>
                <if test="item.yfmc != null">
                    YFMC,
                </if>
                <if test="item.zhfymc != null">
                    ZHFYMC,
                </if>
                <if test="item.tfczyxm != null">
                    TFCZYXM,
                </if>
                <if test="item.zfryxm != null">
                    ZFRYXM,
                </if>
                <if test="item.dzcfbz != null">
                    DZCFBZ,
                </if>
                <if test="item.yhbl != null">
                    YHBL,
                </if>
                <if test="item.yhje != null">
                    YHJE,
                </if>
                <if test="item.zhfybz != null">
                    ZHFYBZ,
                </if>
                <if test="item.sqsj != null">
                    SQSJ,
                </if>
                <if test="item.rybxlbmc != null">
                    RYBXLBMC,
                </if>
                <if test="item.ywckmc != null">
                    YWCKMC,
                </if>
                <if test="item.dycs != null">
                    DYCS,
                </if>
                <if test="item.yljgbm != null">
                    YLJGBM,
                </if>
                <if test="item.sqys != null">
                    SQYS,
                </if>
                <if test="item.sqysxm != null">
                    SQYSXM,
                </if>
                <if test="item.yzfl != null">
                    YZFL,
                </if>
                <if test="item.lczd != null">
                    LCZD,
                </if>
                <if test="item.jcms != null">
                    JCMS,
                </if>
                <if test="item.jcbw != null">
                    JCBW,
                </if>
                <if test="item.lczz != null">
                    LCZZ,
                </if>
                <if test="item.bbsm != null">
                    BBSM,
                </if>
                <if test="item.jymd != null">
                    JYMD,
                </if>
                <if test="item.jybb != null">
                    JYBB,
                </if>
                <if test="item.zxbz != null">
                    ZXBZ,
                </if>
                <if test="item.zxry != null">
                    ZXRY,
                </if>
                <if test="item.zxsj != null">
                    ZXSJ,
                </if>
                <if test="item.sfjz != null">
                    SFJZ,
                </if>
                <if test="item.mtbzmc != null">
                    mtbzmc,
                </if>
                <if test="item.mtbzbm != null">
                    mtbzbm,
                </if>
                <if test="item.jzyyrq != null">
                    jzyyrq,
                </if>
                <if test="item.tjtfsqbz != null">
                    tjtfsqbz,
                </if>
                <if test="item.costid != null">
                    costid,
                </if>
                <if test="item.tjtjbz != null">
                    tjtjbz,
                </if>
                <if test="item.tjdjbz != null">
                    tjdjbz,
                </if>
                <if test="item.tjtmh != null">
                    tjtmh,
                </if>
                <if test="item.bftfysjbz != null">
                    bftfysjbz,
                </if>
                <if test="item.ishff != null">
                    ishff,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.fyjlid != null">
                    #{item.fyjlid,jdbcType=VARCHAR},
                </if>
                <if test="item.ryghxh != null">
                    #{item.ryghxh,jdbcType=VARCHAR},
                </if>
                <if test="item.rybrid != null">
                    #{item.rybrid,jdbcType=VARCHAR},
                </if>
                <if test="item.brxm != null">
                    #{item.brxm,jdbcType=VARCHAR},
                </if>
                <if test="item.ryfbbm != null">
                    #{item.ryfbbm,jdbcType=VARCHAR},
                </if>
                <if test="item.rybxlbbm != null">
                    #{item.rybxlbbm,jdbcType=VARCHAR},
                </if>
                <if test="item.fylb != null">
                    #{item.fylb,jdbcType=VARCHAR},
                </if>
                <if test="item.mxfyxmbm != null">
                    #{item.mxfyxmbm,jdbcType=VARCHAR},
                </if>
                <if test="item.czybm != null">
                    #{item.czybm,jdbcType=VARCHAR},
                </if>
                <if test="item.sfsj != null">
                    #{item.sfsj,jdbcType=TIMESTAMP},
                </if>
                <if test="item.fysl != null">
                    #{item.fysl,jdbcType=DECIMAL},
                </if>
                <if test="item.fydj != null">
                    #{item.fydj,jdbcType=DECIMAL},
                </if>
                <if test="item.fyje != null">
                    #{item.fyje,jdbcType=DECIMAL},
                </if>
                <if test="item.yzlx != null">
                    #{item.yzlx,jdbcType=VARCHAR},
                </if>
                <if test="item.yzhm != null">
                    #{item.yzhm,jdbcType=VARCHAR},
                </if>
                <if test="item.yzxh != null">
                    #{item.yzxh,jdbcType=DECIMAL},
                </if>
                <if test="item.fphm != null">
                    #{item.fphm,jdbcType=VARCHAR},
                </if>
                <if test="item.mzys != null">
                    #{item.mzys,jdbcType=VARCHAR},
                </if>
                <if test="item.mzks != null">
                    #{item.mzks,jdbcType=VARCHAR},
                </if>
                <if test="item.zxks != null">
                    #{item.zxks,jdbcType=VARCHAR},
                </if>
                <if test="item.yfbm != null">
                    #{item.yfbm,jdbcType=VARCHAR},
                </if>
                <if test="item.sfjs != null">
                    #{item.sfjs,jdbcType=VARCHAR},
                </if>
                <if test="item.sftf != null">
                    #{item.sftf,jdbcType=VARCHAR},
                </if>
                <if test="item.tfid != null">
                    #{item.tfid,jdbcType=VARCHAR},
                </if>
                <if test="item.rymzzd != null">
                    #{item.rymzzd,jdbcType=VARCHAR},
                </if>
                <if test="item.ryjkpzh != null">
                    #{item.ryjkpzh,jdbcType=VARCHAR},
                </if>
                <if test="item.ryjsjlid != null">
                    #{item.ryjsjlid,jdbcType=VARCHAR},
                </if>
                <if test="item.ywckbh != null">
                    #{item.ywckbh,jdbcType=VARCHAR},
                </if>
                <if test="item.zhfybh != null">
                    #{item.zhfybh,jdbcType=VARCHAR},
                </if>
                <if test="item.kskfbz != null">
                    #{item.kskfbz,jdbcType=VARCHAR},
                </if>
                <if test="item.kskfry != null">
                    #{item.kskfry,jdbcType=VARCHAR},
                </if>
                <if test="item.kskfrq != null">
                    #{item.kskfrq,jdbcType=TIMESTAMP},
                </if>
                <if test="item.tfczy != null">
                    #{item.tfczy,jdbcType=VARCHAR},
                </if>
                <if test="item.ifbank != null">
                    #{item.ifbank,jdbcType=VARCHAR},
                </if>
                <if test="item.bankzfls != null">
                    #{item.bankzfls,jdbcType=VARCHAR},
                </if>
                <if test="item.jyxh != null">
                    #{item.jyxh,jdbcType=VARCHAR},
                </if>
                <if test="item.bzsm != null">
                    #{item.bzsm,jdbcType=VARCHAR},
                </if>
                <if test="item.zfbz != null">
                    #{item.zfbz,jdbcType=VARCHAR},
                </if>
                <if test="item.zfry != null">
                    #{item.zfry,jdbcType=VARCHAR},
                </if>
                <if test="item.zfrq != null">
                    #{item.zfrq,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ryfbmc != null">
                    #{item.ryfbmc,jdbcType=VARCHAR},
                </if>
                <if test="item.fylbmc != null">
                    #{item.fylbmc,jdbcType=VARCHAR},
                </if>
                <if test="item.mxfyxmmc != null">
                    #{item.mxfyxmmc,jdbcType=VARCHAR},
                </if>
                <if test="item.czyxm != null">
                    #{item.czyxm,jdbcType=VARCHAR},
                </if>
                <if test="item.mzysxm != null">
                    #{item.mzysxm,jdbcType=VARCHAR},
                </if>
                <if test="item.mzksmc != null">
                    #{item.mzksmc,jdbcType=VARCHAR},
                </if>
                <if test="item.zxksmc != null">
                    #{item.zxksmc,jdbcType=VARCHAR},
                </if>
                <if test="item.yfmc != null">
                    #{item.yfmc,jdbcType=VARCHAR},
                </if>
                <if test="item.zhfymc != null">
                    #{item.zhfymc,jdbcType=VARCHAR},
                </if>
                <if test="item.tfczyxm != null">
                    #{item.tfczyxm,jdbcType=VARCHAR},
                </if>
                <if test="item.zfryxm != null">
                    #{item.zfryxm,jdbcType=VARCHAR},
                </if>
                <if test="item.dzcfbz != null">
                    #{item.dzcfbz,jdbcType=VARCHAR},
                </if>
                <if test="item.yhbl != null">
                    #{item.yhbl,jdbcType=DECIMAL},
                </if>
                <if test="item.yhje != null">
                    #{item.yhje,jdbcType=DECIMAL},
                </if>
                <if test="item.zhfybz != null">
                    #{item.zhfybz,jdbcType=VARCHAR},
                </if>
                <if test="item.sqsj != null">
                    #{item.sqsj,jdbcType=TIMESTAMP},
                </if>
                <if test="item.rybxlbmc != null">
                    #{item.rybxlbmc,jdbcType=VARCHAR},
                </if>
                <if test="item.ywckmc != null">
                    #{item.ywckmc,jdbcType=VARCHAR},
                </if>
                <if test="item.dycs != null">
                    #{item.dycs,jdbcType=DECIMAL},
                </if>
                <if test="item.yljgbm != null">
                    #{item.yljgbm,jdbcType=VARCHAR},
                </if>
                <if test="item.sqys != null">
                    #{item.sqys,jdbcType=VARCHAR},
                </if>
                <if test="item.sqysxm != null">
                    #{item.sqysxm,jdbcType=VARCHAR},
                </if>
                <if test="item.yzfl != null">
                    #{item.yzfl,jdbcType=VARCHAR},
                </if>
                <if test="item.lczd != null">
                    #{item.lczd,jdbcType=VARCHAR},
                </if>
                <if test="item.jcms != null">
                    #{item.jcms,jdbcType=VARCHAR},
                </if>
                <if test="item.jcbw != null">
                    #{item.jcbw,jdbcType=VARCHAR},
                </if>
                <if test="item.lczz != null">
                    #{item.lczz,jdbcType=VARCHAR},
                </if>
                <if test="item.bbsm != null">
                    #{item.bbsm,jdbcType=VARCHAR},
                </if>
                <if test="item.jymd != null">
                    #{item.jymd,jdbcType=VARCHAR},
                </if>
                <if test="item.jybb != null">
                    #{item.jybb,jdbcType=VARCHAR},
                </if>
                <if test="item.zxbz != null">
                    #{item.zxbz,jdbcType=VARCHAR},
                </if>
                <if test="item.zxry != null">
                    #{item.zxry,jdbcType=VARCHAR},
                </if>
                <if test="item.zxsj != null">
                    #{item.zxsj,jdbcType=DATE},
                </if>
                <if test="item.sfjz != null">
                    #{item.sfjz,jdbcType=VARCHAR},
                </if>
                <if test="item.mtbzmc != null">
                    #{item.mtbzmc,jdbcType=VARCHAR},
                </if>
                <if test="item.mtbzbm != null">
                    #{item.mtbzbm,jdbcType=VARCHAR},
                </if>
                <if test="item.jzyyrq != null">
                    #{item.jzyyrq,jdbcType=TIMESTAMP},
                </if>
                <if test="item.tjtfsqbz != null">
                    #{item.tjtfsqbz,jdbcType=VARCHAR},
                </if>
                <if test="item.costid != null">
                    #{item.costid,jdbcType=VARCHAR},
                </if>
                <if test="item.tjtjbz != null">
                    #{item.tjtjbz,jdbcType=VARCHAR},
                </if>
                <if test="item.tjdjbz != null">
                    #{item.tjdjbz,jdbcType=VARCHAR},
                </if>
                <if test="item.tjtmh != null">
                    #{item.tjtmh,jdbcType=VARCHAR},
                </if>
                <if test="item.bftfysjbz != null">
                    #{item.bftfysjbz,jdbcType=VARCHAR},
                </if>
                <if test="item.ishff != null">
                    #{item.ishff,jdbcType=VARCHAR},
                </if>

            </trim>
        </foreach>
    </insert>


    <update id="updateBrghfy" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        update MZB_BRFY
        <set>
            <if test="mxfyxmbm != null">
                MXFYXMBM = #{mxfyxmbm,jdbcType=VARCHAR},
            </if>

            <if test="fysl != null">
                FYSL = #{fysl,jdbcType=DECIMAL},
            </if>
            <if test="fydj != null">
                FYDJ = #{fydj,jdbcType=DECIMAL},
            </if>
            <if test="fyje != null">
                FYJE = #{fyje,jdbcType=DECIMAL},
            </if>
            <if test="mxfyxmmc != null">
                MXFYXMMC = #{mxfyxmmc,jdbcType=VARCHAR},
            </if>
            <if test="ishff != null">
                ISHFF = #{ishff,jdbcType=VARCHAR},
            </if>

        </set>
        where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        <if test="ryghxh != null and ryghxh != '' ">
            and ryghxh = #{ryghxh,jdbcType=VARCHAR}
        </if>
        <if test="fylb != null and fylb != '' ">
            and fylb = #{fylb,jdbcType=VARCHAR}
        </if>
    </update>



    <!-- 更改 -->
    <update id="update" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            update MZB_BRFY
            <set>
                <if test="item.ryghxh != null">
                    RYGHXH = #{item.ryghxh,jdbcType=VARCHAR},
                </if>
                <if test="item.rybrid != null">
                    RYBRID = #{item.rybrid,jdbcType=VARCHAR},
                </if>
                <if test="item.brxm != null">
                    BRXM = #{item.brxm,jdbcType=VARCHAR},
                </if>
                <if test="item.ryfbbm != null">
                    RYFBBM = #{item.ryfbbm,jdbcType=VARCHAR},
                </if>
                <if test="item.rybxlbbm != null">
                    RYBXLBBM = #{item.rybxlbbm,jdbcType=VARCHAR},
                </if>
                <if test="item.fylb != null">
                    FYLB = #{item.fylb,jdbcType=VARCHAR},
                </if>
                <if test="item.mxfyxmbm != null">
                    MXFYXMBM = #{item.mxfyxmbm,jdbcType=VARCHAR},
                </if>
                <if test="item.czybm != null">
                    CZYBM = #{item.czybm,jdbcType=VARCHAR},
                </if>
                <if test="item.sfsj != null">
                    SFSJ = #{item.sfsj,jdbcType=TIMESTAMP},
                </if>
                <if test="item.fysl != null">
                    FYSL = #{item.fysl,jdbcType=DECIMAL},
                </if>
                <if test="item.fydj != null">
                    FYDJ = #{item.fydj,jdbcType=DECIMAL},
                </if>
                <if test="item.fyje != null">
                    FYJE = #{item.fyje,jdbcType=DECIMAL},
                </if>
                <if test="item.yzlx != null">
                    YZLX = #{item.yzlx,jdbcType=VARCHAR},
                </if>
                <if test="item.yzhm != null">
                    YZHM = #{item.yzhm,jdbcType=VARCHAR},
                </if>
                <if test="item.yzxh != null">
                    YZXH = #{item.yzxh,jdbcType=DECIMAL},
                </if>
                <if test="item.fphm != null">
                    FPHM = #{item.fphm,jdbcType=VARCHAR},
                </if>
                <if test="item.mzys != null">
                    MZYS = #{item.mzys,jdbcType=VARCHAR},
                </if>
                <if test="item.mzks != null">
                    MZKS = #{item.mzks,jdbcType=VARCHAR},
                </if>
                <if test="item.zxks != null">
                    ZXKS = #{item.zxks,jdbcType=VARCHAR},
                </if>
                <if test="item.yfbm != null">
                    YFBM = #{item.yfbm,jdbcType=VARCHAR},
                </if>
                <if test="item.sfjs != null">
                    SFJS = #{item.sfjs,jdbcType=VARCHAR},
                </if>
                <if test="item.sftf != null">
                    SFTF = #{item.sftf,jdbcType=VARCHAR},
                </if>
                <if test="item.tfid != null">
                    TFID = #{item.tfid,jdbcType=VARCHAR},
                </if>
                <if test="item.rymzzd != null">
                    RYMZZD = #{item.rymzzd,jdbcType=VARCHAR},
                </if>
                <if test="item.ryjkpzh != null">
                    RYJKPZH = #{item.ryjkpzh,jdbcType=VARCHAR},
                </if>
                <if test="item.ryjsjlid != null">
                    RYJSJLID = #{item.ryjsjlid,jdbcType=VARCHAR},
                </if>
                <if test="item.ywckbh != null">
                    YWCKBH = #{item.ywckbh,jdbcType=VARCHAR},
                </if>
                <if test="item.zhfybh != null">
                    ZHFYBH = #{item.zhfybh,jdbcType=VARCHAR},
                </if>
                <if test="item.kskfbz != null">
                    KSKFBZ = #{item.kskfbz,jdbcType=VARCHAR},
                </if>
                <if test="item.kskfry != null">
                    KSKFRY = #{item.kskfry,jdbcType=VARCHAR},
                </if>
                <if test="item.kskfrq != null">
                    KSKFRQ = #{item.kskfrq,jdbcType=TIMESTAMP},
                </if>
                <if test="item.tfczy != null">
                    TFCZY = #{item.tfczy,jdbcType=VARCHAR},
                </if>
                <if test="item.ifbank != null">
                    IFBANK = #{item.ifbank,jdbcType=VARCHAR},
                </if>
                <if test="item.bankzfls != null">
                    BANKZFLS = #{item.bankzfls,jdbcType=VARCHAR},
                </if>
                <if test="item.jyxh != null">
                    JYXH = #{item.jyxh,jdbcType=VARCHAR},
                </if>
                <if test="item.bzsm != null">
                    BZSM = #{item.bzsm,jdbcType=VARCHAR},
                </if>
                <if test="item.zfbz != null">
                    ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
                </if>
                <if test="item.zfry != null">
                    ZFRY = #{item.zfry,jdbcType=VARCHAR},
                </if>
                <if test="item.zfrq != null">
                    ZFRQ = #{item.zfrq,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ryfbmc != null">
                    RYFBMC = #{item.ryfbmc,jdbcType=VARCHAR},
                </if>
                <if test="item.fylbmc != null">
                    FYLBMC = #{item.fylbmc,jdbcType=VARCHAR},
                </if>
                <if test="item.mxfyxmmc != null">
                    MXFYXMMC = #{item.mxfyxmmc,jdbcType=VARCHAR},
                </if>
                <if test="item.czyxm != null">
                    CZYXM = #{item.czyxm,jdbcType=VARCHAR},
                </if>
                <if test="item.mzysxm != null">
                    MZYSXM = #{item.mzysxm,jdbcType=VARCHAR},
                </if>
                <if test="item.mzksmc != null">
                    MZKSMC = #{item.mzksmc,jdbcType=VARCHAR},
                </if>
                <if test="item.zxksmc != null">
                    ZXKSMC = #{item.zxksmc,jdbcType=VARCHAR},
                </if>
                <if test="item.yfmc != null">
                    YFMC = #{item.yfmc,jdbcType=VARCHAR},
                </if>
                <if test="item.zhfymc != null">
                    ZHFYMC = #{item.zhfymc,jdbcType=VARCHAR},
                </if>
                <if test="item.tfczyxm != null">
                    TFCZYXM = #{item.tfczyxm,jdbcType=VARCHAR},
                </if>
                <if test="item.zfryxm != null">
                    ZFRYXM = #{item.zfryxm,jdbcType=VARCHAR},
                </if>
                <if test="item.dzcfbz != null">
                    DZCFBZ = #{item.dzcfbz,jdbcType=VARCHAR},
                </if>
                <if test="item.yhbl != null">
                    YHBL = #{item.yhbl,jdbcType=DECIMAL},
                </if>
                <if test="item.yhje != null">
                    YHJE = #{item.yhje,jdbcType=DECIMAL},
                </if>
                <if test="item.zhfybz != null">
                    ZHFYBZ = #{item.zhfybz,jdbcType=VARCHAR},
                </if>
                <if test="item.sqsj != null">
                    SQSJ = #{item.sqsj,jdbcType=TIMESTAMP},
                </if>
                <if test="item.rybxlbmc != null">
                    RYBXLBMC = #{item.rybxlbmc,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ywckmc != null">
                    YWCKMC = #{item.ywckmc,jdbcType=TIMESTAMP},
                </if>
                <if test="item.dycs != null">
                    DYCS = #{item.dycs,jdbcType=DECIMAL},
                </if>
                <if test="item.sqys != null">
                    SQYS = #{item.sqys,jdbcType=VARCHAR},
                </if>
                <if test="item.sqysxm != null">
                    SQYSXM = #{item.sqysxm,jdbcType=VARCHAR},
                </if>
                <if test="item.yzfl != null">
                    YZFL = #{item.yzfl,jdbcType=VARCHAR},
                </if>
                <if test="item.lczd != null">
                    LCZD=#{item.lczd,jdbcType=VARCHAR},
                </if>
                <if test="item.jcms != null">
                    JCMS=#{item.jcms,jdbcType=VARCHAR},
                </if>
                <if test="item.jcbw != null">
                    JCBW=#{item.jcbw,jdbcType=VARCHAR},
                </if>
                <if test="item.lczz != null">
                    LCZZ=#{item.lczz,jdbcType=VARCHAR},
                </if>
                <if test="item.bbsm != null">
                    BBSM=#{item.bbsm,jdbcType=VARCHAR},
                </if>
                <if test="item.jymd != null">
                    JYMD=#{item.jymd,jdbcType=VARCHAR},
                </if>
                <if test="item.jybb != null">
                    JYBB=#{item.jybb,jdbcType=VARCHAR},
                </if>
                <if test="item.zxbz != null">
                    ZXBZ=#{item.zxbz,jdbcType=VARCHAR},
                </if>
                <if test="item.zxry != null">
                    ZXRY=#{item.zxry,jdbcType=VARCHAR},
                </if>
                <if test="item.zxsj != null">
                    ZXSJ=#{item.zxsj,jdbcType=DATE},
                </if>
                <if test="item.bftfysjbz != null">
                    bftfysjbz=#{item.bftfysjbz,jdbcType=VARCHAR},
                </if>
                <if test="item.ishff != null">
                    ishff=#{item.ishff,jdbcType=VARCHAR},
                </if>

            </set>
            where YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
            <if test="item.fyjlid != null and item.fyjlid != '' ">
                and FYJLID = #{item.fyjlid,jdbcType=VARCHAR}
            </if>
            <if test="item.yzhm != null and item.yzhm != '' ">
                and YZHM = #{item.yzhm,jdbcType=VARCHAR}
            </if>
            <if test="item.mxfyxmbm != null and item.mxfyxmbm != '' ">
                and MXFYXMBM = #{item.mxfyxmbm,jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>

    <!-- 查询检查 -->
    <select id="queryJc" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel"
            resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        SELECT fy.FYJLID ,fy.RYGHXH ,fy.RYBRID ,case when jbxx.brxm is null then fy.brxm else jbxx.brxm end
        brxm,fy.RYFBBM ,fy.RYBXLBBM ,
        fy.FYLB ,fy.MXFYXMBM ,fy.CZYBM ,fy.SFSJ ,fy.FYSL ,fy.FYDJ ,fy.FYJE ,fy.YZLX ,
        fy.YZHM ,fy.YZXH ,fy.FPHM ,fy.MZYS ,fy.MZKS ,fy.ZXKS ,fy.YFBM ,fy.SFJS ,
        fy.SFTF ,fy.TFID ,fy.RYMZZD ,fy.RYJKPZH ,fy.RYJSJLID ,fy.YWCKBH ,fy.ZHFYBH ,
        fy.KSKFBZ ,fy.KSKFRY ,fy.KSKFRQ ,fy.TFCZY ,fy.IFBANK ,fy.BANKZFLS ,fy.JYXH ,
        fy.BZSM ,fy.ZFBZ ,fy.ZFRY ,fy.ZFRQ ,fy.RYFBMC ,fy.FYLBMC ,fy.MXFYXMMC ,
        fy.CZYXM ,fy.MZYSXM ,fy.MZKSMC ,fy.ZXKSMC ,fy.YFMC ,fy.ZHFYMC ,fy.TFCZYXM ,
        fy.ZFRYXM ,fy.DZCFBZ ,fy.YHBL ,fy.YHJE ,fy.ZHFYBZ ,fy.SQSJ ,fy.RYBXLBMC ,
        fy.YWCKMC ,fy.SQYS ,fy.SQYSXM ,fy.DYCS ,fy.YLJGBM ,fy.YZFL ,fy.FYCK ,
        fy.JKSCBZ ,fy.LCZD ,fy.JCMS ,fy.JCBW ,fy.LCZZ ,fy.BBSM ,fy.JYMD ,fy.JYBB,fy.zxbz,fy.zxry,fy.zxsj,
        jl.bxjsh FROM MZB_BRFY fy
        inner join gyb_mxfyxm xm on fy.MXFYXMBM=xm.mxfybm and fy.yljgbm=xm.yljgbm
        left join mzb_jsjl jl on jl.jsjlid=fy.ryjsjlid and jl.yljgbm=fy.yljgbm
        left join gyb_brjbxx jbxx on jbxx.brid = fy.rybrid and jl.rybrid = jbxx.brid and jbxx.yljgbm = fy.yljgbm
        <where>
            (zfbz='0' AND fy.YLJGBM = #{yljgbm,jdbcType=VARCHAR}) and fy.sftf ='0' and fy.fyje &gt; 0 and fy.sfjs='1'
            and length(xm.ffylb)=3
            <if test="zxbz != null and zxbz != '' ">
                <choose>
                    <when test="zxbz == '1'.toString()">
                        and fy.zxbz = #{zxbz,jdbcType=VARCHAR}
                    </when>
                    <otherwise>
                        and (fy.zxbz = '0' or  fy.zxbz = '6')
                    </otherwise>
                </choose>


            </if>
            <if test="ryghxh != null and ryghxh != '' ">
                and fy.ryghxh = #{ryghxh,jdbcType=VARCHAR}
            </if>
            <if test="brxm != null and brxm != '' ">
                and (fy.brxm like '%'||#{brxm,jdbcType=VARCHAR}||'%' or lower(jbxx.pydm) like
                '%'||lower(#{brxm,jdbcType=VARCHAR})||'%')
            </if>
            <if test="endrq != null ">
                and (fy.sfsj &lt; #{endrq,jdbcType=DATE})
            </if>
            <if test="beginrq != null ">
                and (fy.sfsj &gt;= #{beginrq,jdbcType=DATE})
            </if>
        </where>
        ORDER BY fy.sfsj desc
    </select>

    <!-- 查询检验 -->
    <select id="queryJy" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel"
            resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        SELECT fy.FYJLID ,fy.RYGHXH ,fy.RYBRID ,case when jbxx.brxm is null then fy.brxm else jbxx.brxm end
        brxm,fy.RYFBBM ,fy.RYBXLBBM ,
        fy.FYLB ,fy.MXFYXMBM ,fy.CZYBM ,fy.SFSJ ,fy.FYSL ,fy.FYDJ ,fy.FYJE ,fy.YZLX ,
        fy.YZHM ,fy.YZXH ,fy.FPHM ,fy.MZYS ,fy.MZKS ,fy.ZXKS ,fy.YFBM ,fy.SFJS ,
        fy.SFTF ,fy.TFID ,fy.RYMZZD ,fy.RYJKPZH ,fy.RYJSJLID ,fy.YWCKBH ,fy.ZHFYBH ,
        fy.KSKFBZ ,fy.KSKFRY ,fy.KSKFRQ ,fy.TFCZY ,fy.IFBANK ,fy.BANKZFLS ,fy.JYXH ,
        fy.BZSM ,fy.ZFBZ ,fy.ZFRY ,fy.ZFRQ ,fy.RYFBMC ,fy.FYLBMC ,fy.MXFYXMMC ,
        fy.CZYXM ,fy.MZYSXM ,fy.MZKSMC ,fy.ZXKSMC ,fy.YFMC ,fy.ZHFYMC ,fy.TFCZYXM ,
        fy.ZFRYXM ,fy.DZCFBZ ,fy.YHBL ,fy.YHJE ,fy.ZHFYBZ ,fy.SQSJ ,fy.RYBXLBMC ,
        fy.YWCKMC ,fy.SQYS ,fy.SQYSXM ,fy.DYCS ,fy.YLJGBM ,fy.YZFL ,fy.FYCK ,
        fy.JKSCBZ ,fy.LCZD ,fy.JCMS ,fy.JCBW ,fy.LCZZ ,fy.BBSM ,fy.JYMD ,fy.JYBB,fy.zxbz,fy.zxry,fy.zxsj,
        jl.bxjsh FROM MZB_BRFY fy
        inner join gyb_mxfyxm xm on fy.MXFYXMBM=xm.mxfybm and fy.yljgbm=xm.yljgbm
        left join mzb_jsjl jl on jl.jsjlid=fy.ryjsjlid and jl.yljgbm=fy.yljgbm
        left join gyb_brjbxx jbxx on jbxx.brid = fy.rybrid and jl.rybrid = jbxx.brid and jbxx.yljgbm = fy.yljgbm
        <where>
            (zfbz='0' AND fy.YLJGBM = #{yljgbm,jdbcType=VARCHAR}) and fy.sftf ='0' and fy.fyje &gt; 0 and fy.sfjs='1'

            <if test="zxbz != null and zxbz != '' ">
                and fy.zxbz = #{zxbz,jdbcType=VARCHAR}
            </if>
            <if test="ryghxh != null and ryghxh != '' ">
                and fy.ryghxh = #{ryghxh,jdbcType=VARCHAR}
            </if>
            <if test="brxm != null and brxm != '' ">
                and (fy.brxm like '%'||#{brxm,jdbcType=VARCHAR}||'%' or lower(jbxx.pydm) like
                '%'||lower(#{brxm,jdbcType=VARCHAR})||'%')
            </if>
            <if test="endrq != null ">
                and (fy.sfsj &lt; #{endrq,jdbcType=DATE})
            </if>
            <if test="beginrq != null ">
                and (fy.sfsj &gt;= #{beginrq,jdbcType=DATE})
            </if>
            <choose>
                <when test="zxks != null and zxks != '' ">
                    and fy.zxks = #{zxks,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and xm.ffylb='4'
                </otherwise>
            </choose>

        </where>
        ORDER BY fy.sfsj desc
    </select>
    <!-- 保存发票打印记录 -->
    <insert id="insertPjdyjl" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            insert into MZB_FPDYJL
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.czybm != null">
                    czybm,
                </if>
                <if test="item.czyxm != null">
                    czyxm,
                </if>
                <if test="item.ghxh != null">
                    ghxh,
                </if>
                <if test="item.brxm != null">
                    brxm,
                </if>
                <if test="item.djrq != null">
                    djrq,
                </if>
                <if test="item.fphm != null">
                    fphm,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.czybm != null">
                    #{item.czybm,jdbcType=VARCHAR},
                </if>
                <if test="item.czyxm != null">
                    #{item.czyxm,jdbcType=VARCHAR},
                </if>
                <if test="item.ghxh != null">
                    #{item.ghxh,jdbcType=VARCHAR},
                </if>
                <if test="item.brxm != null">
                    #{item.brxm,jdbcType=VARCHAR},
                </if>
                <if test="item.djrq != null">
                    #{item.djrq,jdbcType=DATE},
                </if>
                <if test="item.fphm != null">
                    #{item.fphm,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 根据费用ID查询是否有已经扣费的费用 -->
    <select id="querySfyjMzsfByFyid" parameterType="java.util.List"
            resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
        <foreach collection="list" item="item" index="index" separator="union">
            select * from mzb_brfy
            where yljgbm = #{item.yljgbm}
            and fyjlid = #{item.fyjlid}
        </foreach>
    </select>

    <!-- 根据处方号查询是否都已经收费 -->
    <select id="querySfyjMzsfByCfh" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel">
        select * from YFB_YPCF
        where yljgbm = #{yljgbm}
        <if test="searchcfh != null">
            and CFH in
            <foreach item="item" index="index" collection="searchcfh" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateBrfyMzys" parameterType="com.supx.csp.api.ghgl.ghyw.pojo.Ghb_brghModel">
		update mzb_brfy set mzys = #{jzys,jdbcType=VARCHAR}, mzysxm = #{jzysxm,jdbcType=VARCHAR}
		where fyjlid in(select fyjlid from mzb_brfy where ryghxh = #{ghxh,jdbcType=VARCHAR} and yljgbm =#{yljgbm,jdbcType=VARCHAR} and yzlx = '1' and mzys is null)
	</update>

    <update id="deleteFjf" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
		update mzb_brfy set zfbz='1', zfry = #{zfry,jdbcType=VARCHAR}, zfryxm = #{zfryxm,jdbcType=VARCHAR}, zfrq = #{zfrq,jdbcType=DATE}
		where fyjlid = #{fyjlid,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
	</update>

    <update id="updateWxPayUrl" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel">
		update mzb_brfy set wxPrePayUrl = #{wxPrePayUrl, jdbcType=VARCHAR}
		where yljgbm = #{yljgbm,jdbcType=VARCHAR} and ryghxh = #{ryghxh,jdbcType=VARCHAR} and yzhm = #{yzhm,jdbcType=VARCHAR}
	</update>

    <select id="queryGzydnhD1" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_GzydnhD1">
		SELECT BRXM   --病人姓名
        ,BRNL   --病人年龄
        ,BOOKNO as YLZH   --医疗证号
        --,JTDZ   --
        ,YLJGBM   --医疗机构编码
        ,MEMBERID   --个人编码
        ,ADMISSIONDATE as INPATIENTDATE   --就诊时间
        --,INPATIENTDEPARTMENTS   --就诊科室gznh_yd_ksbm
        ,TREATINGPHYSICIAN   --经治医生
        ,DISEASECODE   --疾病代码gznh_yd_jbbm
        ,INITIALDIAGNOSIS   --初步诊断
        ,ISINCREASE   --是否提高（1提高0不提高）
        --,ISINFUSION   --是否转诊（1转诊默认0）
        --,ISACCOUNTPAY   --是否扣家庭账户
        --,ISOUTPPAY   --是否统筹
        ,INPATIENTTYPEOFLOCAL   --病人类型gznh_yd_code类别04
        ,BXACCOUNT   --保险金额
        ,TEL   --联系电话
        ,BANKCARDNO   --银行卡号
        ,HOLDERRELATION   --患者与开户人关系
        --,INFUSIONCOUNT   --输液次数
        ,REMARK   --备注
        --,LXFS   --联系方式
        --,HISCLINICNO   --HIS门诊号（跨省）需保证本医疗机构内HIS门诊号唯一
        ,FAMILYNO   --家庭编号（跨省）
        ,to_number(STATURE)   --身高（跨省）
        ,to_number(WEIGHT)   --体重（跨省）
        ,SECONDICDNO   --第二疾病诊断（跨省）
        ,THREEICDNO   --第三疾病诊断（跨省）
        ,TREATCODE   --治疗方式编码（跨省
        --,CURECODE   --就诊类型(跨省)参见附表
        --,INHOSID   --来就诊状态（跨省）参见附表
        ,REGISTERID   --经办医疗机构登记流水号ID（跨省）
        --,CLINICNO   --农合门诊登记流水号（跨省）门诊登记修改时传入
        ,AREACODE   --行政编码(跨省)
        ,ISTRANSPROVINCIAL   --是否跨省就医患者1:是0:否(跨省)
        ,inpid as OUTPID   --门诊补偿序号
        ,TOTALCOST   --总费用（四位小数精度）
        ,INSURANCECOST   --保内费用（四位小数精度）
        ,UNDULATINGLINE   --起伏线（四位小数精度）
        ,GENERALREDEEM   --统筹补偿（四位小数精度）
        --,ACCREDEEM   --家庭账户补偿（四位小数精度）
        ,COMPENSATECOST   --农合补偿费用（四位小数精度）
        ,CIVILCOST   --民政救助费用（四位小数精度）
        ,INSURECOST   --大病商保补偿（四位小数精度）
        ,SALVAJSCOST   --计生救助费用（四位小数精度）
        ,BOTTOMREDEEM   --兜底补偿费用（四位小数精度）
        ,MEDICINECOST   --精准目录补偿（四位小数精度）
        ,SALVAYFCOST   --新精准优抚补偿（四位小数精度）
        ,SALVACLCOST   --新精准残联补偿（四位小数精度）
        ,SALVAFPCOST   --新精准扶贫补偿（四位小数精度）
        ,SALVAJKCOST   --新精准疾控补偿（四位小数精度）
        ,REDEEMNO   --补偿类型编码(跨省)
        --,LEAVEDATE   --出院日期(yyyy-mm-dd hh:mm:ss )(跨省,可传当前时间)
        ,JSBZ   --结算标志
        ,JSRY   --结算人员
        ,JSRQ   --结算日期
        ,ZFBZ   --作废标志
        ,ZFRY   --作废人员
        ,ZFRQ   --作废日期
        --,ZFYY   --作废原因
        ,CZY   --操作员
        ,CZRQ   --操作日期
        ,CONTINENTINSURANCECOST   --大地保险补偿金额
        ,SPECIALPOVERTYCOST   --特困供养补偿
        ,MEDICALASSISTANCECOST   --医疗扶助补偿
        --,MEDICALSECONDCOST   --合医二次补偿
        FROM gznh_inhospital
         where yljgbm =#{yljgbm} and inpid =#{outpid}
         union all
         SELECT BRXM   --病人姓名
        ,to_number (BRNL)   --病人年龄
        ,YLZH   --医疗证号
        --,JTDZ   --
        ,YLJGBM   --医疗机构编码
        ,MEMBERID   --个人编码
        ,INPATIENTDATE   --就诊时间
        --,INPATIENTDEPARTMENTS   --就诊科室gznh_yd_ksbm
        ,TREATINGPHYSICIAN   --经治医生
        ,DISEASECODE   --疾病代码gznh_yd_jbbm
        ,INITIALDIAGNOSIS   --初步诊断
        ,ISINCREASE   --是否提高（1提高0不提高）
        --,ISINFUSION   --是否转诊（1转诊默认0）
        --,ISACCOUNTPAY   --是否扣家庭账户
        --,ISOUTPPAY   --是否统筹
        ,INPATIENTTYPEOFLOCAL   --病人类型gznh_yd_code类别04
        ,BXACCOUNT   --保险金额
        ,TEL   --联系电话
        ,BANKCARDNO   --银行卡号
        ,HOLDERRELATION   --患者与开户人关系
        ,REMARK   --备注
        ,FAMILYNO   --家庭编号（跨省）
        ,STATURE   --身高（跨省）
        ,WEIGHT   --体重（跨省）
        ,SECONDICDNO   --第二疾病诊断（跨省）
        ,THREEICDNO   --第三疾病诊断（跨省）
        ,TREATCODE   --治疗方式编码（跨省
        ,REGISTERID   --经办医疗机构登记流水号ID（跨省）
        ,AREACODE   --行政编码(跨省)
        ,ISTRANSPROVINCIAL   --是否跨省就医患者1:是0:否(跨省)
        ,OUTPID   --门诊补偿序号
        ,TOTALCOST   --总费用（四位小数精度）
        ,INSURANCECOST   --保内费用（四位小数精度）
        ,UNDULATINGLINE   --起伏线（四位小数精度）
        ,GENERALREDEEM   --统筹补偿（四位小数精度）
        ,COMPENSATECOST   --农合补偿费用（四位小数精度）
        ,CIVILCOST   --民政救助费用（四位小数精度）
        ,INSURECOST   --大病商保补偿（四位小数精度）
        ,SALVAJSCOST   --计生救助费用（四位小数精度）
        ,BOTTOMREDEEM   --兜底补偿费用（四位小数精度）
        ,MEDICINECOST   --精准目录补偿（四位小数精度）
        ,SALVAYFCOST   --新精准优抚补偿（四位小数精度）
        ,SALVACLCOST
        ,SALVAFPCOST
        ,SALVAJKCOST
        ,REDEEMNO
        ,JSBZ ,JSRY,JSRQ,ZFBZ,ZFRY,ZFRQ,CZY ,CZRQ
        ,CONTINENTINSURANCECOST
        ,SPECIALPOVERTYCOST ,MEDICALASSISTANCECOST
          FROM gznh_mz_jsxx
         where yljgbm =#{yljgbm} and outpid =#{outpid}
	</select>

    <select id="queryGzydnhD2" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_GzydnhD2">
                      select BCRQ,MZLX
        ,RYLX
        ,LXDH
        ,JBMC  --疾病名称
        ,JTDZ
        ,MEMBERID --个人编码
        ,FYJE
        ,(zfJE-(select (BOTTOMREDEEM+CIVILCOST+INSURECOST) zfje from (
        SELECT
        BRXM   --病人姓名
        ,BRNL   --病人年龄
        ,BOOKNO as YLZH   --医疗证号
        --,JTDZ   --
        ,YLJGBM   --医疗机构编码
        ,MEMBERID   --个人编码
        ,ADMISSIONDATE as INPATIENTDATE   --就诊时间
        --,INPATIENTDEPARTMENTS   --就诊科室gznh_yd_ksbm
        ,TREATINGPHYSICIAN   --经治医生
        ,DISEASECODE   --疾病代码gznh_yd_jbbm
        ,INITIALDIAGNOSIS   --初步诊断
        ,ISINCREASE   --是否提高（1提高0不提高）
        --,ISINFUSION   --是否转诊（1转诊默认0）
        --,ISACCOUNTPAY   --是否扣家庭账户
        --,ISOUTPPAY   --是否统筹
        ,INPATIENTTYPEOFLOCAL   --病人类型gznh_yd_code类别04
        ,BXACCOUNT   --保险金额
        ,TEL   --联系电话
        ,BANKCARDNO   --银行卡号
        ,HOLDERRELATION   --患者与开户人关系
        --,INFUSIONCOUNT   --输液次数
        ,REMARK   --备注
        --,LXFS   --联系方式
        --,HISCLINICNO   --HIS门诊号（跨省）需保证本医疗机构内HIS门诊号唯一
        ,FAMILYNO   --家庭编号（跨省）
        ,to_number(STATURE)   --身高（跨省）
        ,to_number(WEIGHT)   --体重（跨省）
        ,SECONDICDNO   --第二疾病诊断（跨省）
        ,THREEICDNO   --第三疾病诊断（跨省）
        ,TREATCODE   --治疗方式编码（跨省
        --,CURECODE   --就诊类型(跨省)参见附表
        --,INHOSID   --来就诊状态（跨省）参见附表
        ,REGISTERID   --经办医疗机构登记流水号ID（跨省）
        --,CLINICNO   --农合门诊登记流水号（跨省）门诊登记修改时传入
        ,AREACODE   --行政编码(跨省)
        ,ISTRANSPROVINCIAL   --是否跨省就医患者1:是0:否(跨省)
        ,inpid as OUTPID   --门诊补偿序号
        ,TOTALCOST   --总费用（四位小数精度）
        ,INSURANCECOST   --保内费用（四位小数精度）
        ,UNDULATINGLINE   --起伏线（四位小数精度）
        ,GENERALREDEEM   --统筹补偿（四位小数精度）
        --,ACCREDEEM   --家庭账户补偿（四位小数精度）
        ,COMPENSATECOST   --农合补偿费用（四位小数精度）
        ,CIVILCOST   --民政救助费用（四位小数精度）
        ,INSURECOST   --大病商保补偿（四位小数精度）
        ,SALVAJSCOST   --计生救助费用（四位小数精度）
        ,BOTTOMREDEEM   --兜底补偿费用（四位小数精度）
        ,MEDICINECOST   --精准目录补偿（四位小数精度）
        ,SALVAYFCOST   --新精准优抚补偿（四位小数精度）
        ,SALVACLCOST   --新精准残联补偿（四位小数精度）
        ,SALVAFPCOST   --新精准扶贫补偿（四位小数精度）
        ,SALVAJKCOST   --新精准疾控补偿（四位小数精度）
        ,REDEEMNO   --补偿类型编码(跨省)
        --,LEAVEDATE   --出院日期(yyyy-mm-dd hh:mm:ss )(跨省,可传当前时间)
        ,JSBZ   --结算标志
        ,JSRY   --结算人员
        ,JSRQ   --结算日期
        ,ZFBZ   --作废标志
        ,ZFRY   --作废人员
        ,ZFRQ   --作废日期
        --,ZFYY   --作废原因
        ,CZY   --操作员
        ,CZRQ   --操作日期
        ,CONTINENTINSURANCECOST   --大地保险补偿金额
        ,SPECIALPOVERTYCOST   --特困供养补偿
        ,MEDICALASSISTANCECOST   --医疗扶助补偿
        --,MEDICALSECONDCOST   --合医二次补偿
        FROM gznh_inhospital
        where yljgbm =#{yljgbm} and inpid =#{outpid}
        union all
        SELECT BRXM   --病人姓名
        ,to_number (BRNL)   --病人年龄
        ,YLZH   --医疗证号
        --,JTDZ   --
        ,YLJGBM   --医疗机构编码
        ,MEMBERID   --个人编码
        ,INPATIENTDATE   --就诊时间
        --,INPATIENTDEPARTMENTS   --就诊科室gznh_yd_ksbm
        ,TREATINGPHYSICIAN   --经治医生
        ,DISEASECODE   --疾病代码gznh_yd_jbbm
        ,INITIALDIAGNOSIS   --初步诊断
        ,ISINCREASE   --是否提高（1提高0不提高）
        --,ISINFUSION   --是否转诊（1转诊默认0）
        --,ISACCOUNTPAY   --是否扣家庭账户
        --,ISOUTPPAY   --是否统筹
        ,INPATIENTTYPEOFLOCAL   --病人类型gznh_yd_code类别04
        ,BXACCOUNT   --保险金额
        ,TEL   --联系电话
        ,BANKCARDNO   --银行卡号
        ,HOLDERRELATION   --患者与开户人关系
        --,INFUSIONCOUNT   --输液次数
        ,REMARK   --备注
        --,LXFS   --联系方式
        --,HISCLINICNO   --HIS门诊号（跨省）需保证本医疗机构内HIS门诊号唯一
        ,FAMILYNO   --家庭编号（跨省）
        ,STATURE   --身高（跨省）
        ,WEIGHT   --体重（跨省）
        ,SECONDICDNO   --第二疾病诊断（跨省）
        ,THREEICDNO   --第三疾病诊断（跨省）
        ,TREATCODE   --治疗方式编码（跨省
        --,CURECODE   --就诊类型(跨省)参见附表
        --,INHOSID   --来就诊状态（跨省）参见附表
        ,REGISTERID   --经办医疗机构登记流水号ID（跨省）
        --,CLINICNO   --农合门诊登记流水号（跨省）门诊登记修改时传入
        ,AREACODE   --行政编码(跨省)
        ,ISTRANSPROVINCIAL   --是否跨省就医患者1:是0:否(跨省)
        ,OUTPID   --门诊补偿序号
        ,TOTALCOST   --总费用（四位小数精度）
        ,INSURANCECOST   --保内费用（四位小数精度）
        ,UNDULATINGLINE   --起伏线（四位小数精度）
        ,GENERALREDEEM   --统筹补偿（四位小数精度）
        --,ACCREDEEM   --家庭账户补偿（四位小数精度）
        ,COMPENSATECOST   --农合补偿费用（四位小数精度）
        ,CIVILCOST   --民政救助费用（四位小数精度）
        ,INSURECOST   --大病商保补偿（四位小数精度）
        ,SALVAJSCOST   --计生救助费用（四位小数精度）
        ,BOTTOMREDEEM   --兜底补偿费用（四位小数精度）
        ,MEDICINECOST   --精准目录补偿（四位小数精度）
        ,SALVAYFCOST   --新精准优抚补偿（四位小数精度）
        ,SALVACLCOST   --新精准残联补偿（四位小数精度）
        ,SALVAFPCOST   --新精准扶贫补偿（四位小数精度）
        ,SALVAJKCOST   --新精准疾控补偿（四位小数精度）
        ,REDEEMNO   --补偿类型编码(跨省)
        --,LEAVEDATE   --出院日期(yyyy-mm-dd hh:mm:ss )(跨省,可传当前时间)
        ,JSBZ   --结算标志
        ,JSRY   --结算人员
        ,JSRQ   --结算日期
        ,ZFBZ   --作废标志
        ,ZFRY   --作废人员
        ,ZFRQ   --作废日期
        --,ZFYY   --作废原因
        ,CZY   --操作员
        ,CZRQ   --操作日期
        ,CONTINENTINSURANCECOST   --大地保险补偿金额
        ,SPECIALPOVERTYCOST   --特困供养补偿
        ,MEDICALASSISTANCECOST   --医疗扶助补偿
        --,MEDICALSECONDCOST   --合医二次补偿
        FROM gznh_mz_jsxx
        where yljgbm =#{yljgbm} and outpid =#{outpid}))) zfje
        ,BCFY
        ,CZYXM --操作员姓名
        from GZNH_MZ_JSXX_BCD
        where yljgbm =#{yljgbm} and outpid =#{outpid}
        union all
        select bcd.BCRQ
          ,'重大疾病' as MZLX
          ,bcd.RYLX
          ,bcd.LXDH
          ,jbbm.mc as JBMC
          ,bcd.JTDZ
          ,bcd.inpid as MEMBERID
          ,inh.TOTALCOST
          ,(inh.TOTALCOST-bcd.sjbc)ZFJE
          ,bcd.SJBC as BCFY
          ,rybm.ryxm as CZYXM
        from GZNH_INHOSPITAL_BCD bcd inner join gznh_inhospital inh  on bcd.inpid=inh.inpid
          left join gyb_rybm rybm on inh.jsry = rybm.rybm
          left join gznh_yd_jbbm jbbm on inh.majordiseaseicd = jbbm.bm
        where bcd.yljgbm =#{yljgbm} and bcd.inpid =#{outpid}
    </select>
</mapper>
