<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.mzsf.sfjs.dao.New1Mzb_jsjlModelMapper" >
  <resultMap id="BaseResultMap" type="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" >
    <id column="JSJLID" property="jsjlid" jdbcType="VARCHAR" />
    <result column="RYGHXH" property="ryghxh" jdbcType="VARCHAR" />
    <result column="RYBRID" property="rybrid" jdbcType="VARCHAR" />
    <result column="RYBXLBBM" property="rybxlbbm" jdbcType="VARCHAR" />
    <result column="RYJKPZH" property="ryjkpzh" jdbcType="VARCHAR" />
    <result column="FYHJ" property="fyhj" jdbcType="DECIMAL" />
    <result column="YBKZF" property="ybkzf" jdbcType="DECIMAL" />
    <result column="YLKZF" property="ylkzf" jdbcType="DECIMAL" />
    <result column="XJZF" property="xjzf" jdbcType="DECIMAL" />
    <result column="ZFLXBM" property="zflxbm" jdbcType="VARCHAR" />
    <result column="CZYBM" property="czybm" jdbcType="VARCHAR" />
    <result column="JSZT" property="jszt" jdbcType="CHAR" />
    <result column="JSRQ" property="jsrq" jdbcType="TIMESTAMP" />
    <result column="BZSM" property="bzsm" jdbcType="VARCHAR" />
    <result column="CZYXM" property="czyxm" jdbcType="VARCHAR" />
    <result column="BXJSH" property="bxjsh" jdbcType="VARCHAR" />
     <result column="CZIP" property="czip" jdbcType="VARCHAR" />
  </resultMap>



	<resultMap id="Qtzfzflx" type="com.supx.csp.api.mzsf.cxtj.Mzsf_Cxtj_Qtzftj_ZflxResModel">
    	<id column="zflxbm" property="zflxbm" jdbcType="VARCHAR" />
	    <result column="zflxmc" property="zflxmc" jdbcType="VARCHAR" />
	    <result column="zfhj" property="zfhj" jdbcType="DECIMAL" />
   </resultMap>

   <resultMap id="Qtzfksje" type="com.supx.csp.api.mzsf.cxtj.Mzsf_Cxtj_Qtzftj_KsjeResModel">
    	<id column="ksbm" property="ksbm" jdbcType="VARCHAR" />
	    <result column="ksmc" property="ksmc" jdbcType="VARCHAR" />
	    <result column="zfje" property="zfje" jdbcType="DECIMAL" />
   </resultMap>

    <resultMap id="Qtzfksmx" type="com.supx.csp.api.mzsf.cxtj.Mzsf_Cxtj_Qtzftj_Ksje_flmxModel">
    <id column="ksbm" property="ksbm" jdbcType="VARCHAR" />
   	<result column="ksmc" property="ksmc" jdbcType="VARCHAR"/>
   	<result column="zfje" property="zfje" jdbcType="DECIMAL" />
    <collection property="flmx" column="ksbm" ofType="com.supx.csp.api.mzsf.cxtj.Mzsf_Cxtj_Qtzfjl_Ksje_flmx_mxModel">
    	<id column="zflxbm" property="zflxbm" jdbcType="VARCHAR" />
	    <result column="zflxmc" property="zflxmc" jdbcType="VARCHAR" />
	    <result column="mxje" property="mxje" jdbcType="DECIMAL" />
    </collection>
   </resultMap>


  <sql id="Base_Column_List" >
    JSJLID, RYGHXH, RYBRID, RYBXLBBM, RYJKPZH, FYHJ, YBKZF,
    YLKZF, XJZF, ZFLXBM, CZYBM, JSZT, JSRQ, BZSM, CZYXM,BXJSH,QTZF,YHHJ,PAY_NO payno,ybfyhj
  </sql>

  <!-- 结算记录的各种金额合计 -->
    <select id="selectFyhj" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" >
    	select sum(jsjl.fyhj) fyze, sum(jsjl.ybkzf) ybkze, sum(jsjl.xjzf) xjze,sum(jsjl.qtzf) ybze  from mzb_jsjl jsjl
    	where jsjl.yljgbm=#{yljgbm,jdbcType=VARCHAR}
    	<if test="endrq != null ">
			and (jsjl.jsrq &lt; #{endrq,jdbcType=TIMESTAMP})
		</if>
		<if test="beginrq != null ">
				and (jsjl.jsrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
		</if>
		<if test="czybm != null and czybm != '' ">
			and jsjl.czybm=#{czybm ,jdbcType=VARCHAR}
		</if>
		<if test="jsjlid != null and jsjlid != '' ">
			and jsjl.jsjlid=#{jsjlid ,jdbcType=VARCHAR}
		</if>
		<if test="sfcxqtzf != null and sfcxqtzf != ''">
			and jsjl.qtzf &gt;0
		</if>
    </select>

  <!-- 查询 -->
  <select id="query" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel"
    parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" >
    select
    <include refid="Base_Column_List" />
    from MZB_JSJL
    <where>
    	yljgbm = #{yljgbm,jdbcType=VARCHAR}
    	<if test="jsjlid != null and jsjlid != ''">
	    	and JSJLID = #{jsjlid,jdbcType=VARCHAR}
	    </if>
	    <if test="ryghxh != null and ryghxh != ''">
	    	and RYGHXH = #{ryghxh,jdbcType=VARCHAR}
	    </if>
	    <if test="jszt != null and jszt != ''">
	    	and JSZT = #{jszt,jdbcType=VARCHAR}
	    </if>
	    <if test="beginrq != null  and endrq != null  ">
	    	and jsrq &gt;= #{beginrq,jdbcType=VARCHAR} and jsrq &lt; #{endrq,jdbcType=VARCHAR}
	    </if>
    </where>

  </select>


  <!-- 查询结算记录明细汇总 -->
  <select id="selectJsjlMx" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" >
 	select jsjl.*,jsxx.ylzh bxkh,bxlb.bxlbmc,case when brgh.fbmc is null then brfy.ryfbmc else brgh.fbmc end fbmc,case when jbxx.brxm is null then brfy.brxm else jbxx.brxm end brxm from mzb_jsjl jsjl
	left join ghb_brgh brgh on brgh.ghxh=jsjl.ryghxh and brgh.yljgbm=jsjl.yljgbm
	left join gyb_brjbxx jbxx on jbxx.brid=brgh.brid and jbxx.yljgbm=brgh.yljgbm
	left join  gyb_bxlb bxlb on bxlb.bxlbbm=jsjl.rybxlbbm and bxlb.yljgbm=jsjl.yljgbm
	left join gznh_mz_jsxx jsxx on jsxx.outpid=jsjl.bxjsh and jsxx.yljgbm=jsjl.yljgbm and jsxx.jsbz='1' and jsxx.zfbz='0'
    inner join (
       select yljgbm,brxm,ryjsjlid,ryfbmc,ryghxh from mzb_brfy where zfbz='0' group by yljgbm,brxm,ryjsjlid,ryfbmc,ryghxh
    ) brfy on brfy.yljgbm=jsjl.yljgbm and brfy.ryjsjlid=jsjl.jsjlid and brfy.ryghxh=jsjl.ryghxh
	where jsjl.yljgbm=#{yljgbm,jdbcType=VARCHAR}
	<if test="endrq != null ">
			and (jsjl.jsrq &lt; #{endrq,jdbcType=TIMESTAMP})
	</if>
	<if test="beginrq != null ">
			and (jsjl.jsrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
	</if>
	<if test="czybm != null and czybm != '' ">
		and jsjl.czybm=#{czybm ,jdbcType=VARCHAR}
	</if>
	<if test="sfcxqtzf != null and sfcxqtzf != ''">
		and jsjl.qtzf &gt;0
	</if>
	ORDER BY jsjl.ryghxh desc
  </select>

  <!-- 针对门诊财务交款更新数据 -->
  <update id="updateByCwjk" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" >
  update MZB_JSJL
  <set >
  ryjkpzh = #{ryjkpzh,jdbcType=VARCHAR}
  </set>
   where yljgbm = #{yljgbm,jdbcType=VARCHAR}
   and ryjkpzh is null and czybm= #{czybm,jdbcType=VARCHAR} and jsrq &lt;= #{jsrq,jdbcType=TIMESTAMP}
  </update>


  <!-- 针对取消门诊财务交款更新数据 -->
  <update id="updateByQxCwjk" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" >
  	update MZB_JSJL
  	<set >
  		ryjkpzh= null
  	</set>
  	 where yljgbm = #{yljgbm,jdbcType=VARCHAR} and ryjkpzh = #{ryjkpzh,jdbcType=VARCHAR}
  </update>

  <!-- 查询 -->
  <select id="select" resultMap="BaseResultMap" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" >
    select
    <include refid="Base_Column_List" />
    from MZB_JSJL
    <where>
    	yljgbm = #{yljgbm,jdbcType=VARCHAR}
    	<if test="jsjlid != null and jsjlid != ''">
	    	and JSJLID = #{jsjlid,jdbcType=VARCHAR}
	    </if>
	    <if test="ryghxh != null and ryghxh != ''">
	    	and RYGHXH = #{ryghxh,jdbcType=VARCHAR}
	    </if>
	    <if test="jszt != null and jszt != ''">
	    	and JSZT = #{jszt,jdbcType=VARCHAR}
	    </if>
	    <if test="beginrq != null  and endrq != null  ">
	    	and jsrq &gt;= #{beginrq,jdbcType=VARCHAR} and jsrq &lt; #{endrq,jdbcType=VARCHAR}
	    </if>
    </where>
  </select>

    <!-- 查询 -->
  <select id="queryJrjs" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" parameterType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel" >
    select
   	js.*,jb.brxm
    from MZB_JSJL js inner join gyb_brjbxx jb on jb.brid=js.rybrid and jb.yljgbm=js.yljgbm
    <where>
    		js.yljgbm = #{yljgbm,jdbcType=VARCHAR} and js.czybm= #{czybm,jdbcType=VARCHAR}
	    	and js.jsrq &gt;= #{beginrq,jdbcType=VARCHAR} and js.jsrq &lt; #{endrq,jdbcType=VARCHAR}
    </where>
    order by jsrq desc
  </select>

  <!-- 删除 -->
  <delete id="delete" parameterType="java.util.List" >
  	<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
	    delete from MZB_JSJL
	    where yljgbm = #{item.yljgbm,jdbcType=VARCHAR} and JSJLID = #{item.jsjlid,jdbcType=VARCHAR}
	</foreach>
  </delete>

  <!-- 插入 -->
  <insert id="insert" parameterType="java.util.List" >
  	<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
	    insert into MZB_JSJL
	    <trim prefix="(" suffix=")" suffixOverrides="," >
	      <if test="item.jsjlid != null" >
	        JSJLID,
	      </if>
	      <if test="item.ryghxh != null" >
	        RYGHXH,
	      </if>
	      <if test="item.rybrid != null" >
	        RYBRID,
	      </if>
	      <if test="item.rybxlbbm != null" >
	        RYBXLBBM,
	      </if>
	      <if test="item.ryjkpzh != null" >
	        RYJKPZH,
	      </if>
	      <if test="item.fyhj != null" >
	        FYHJ,
	      </if>
	      <if test="item.ybkzf != null" >
	        YBKZF,
	      </if>
	      <if test="item.ylkzf != null" >
	        YLKZF,
	      </if>
	      <if test="item.xjzf != null" >
	        XJZF,
	      </if>
	      <if test="item.zflxbm != null" >
	        ZFLXBM,
	      </if>
	      <if test="item.czybm != null" >
	        CZYBM,
	      </if>
	      <if test="item.jszt != null" >
	        JSZT,
	      </if>
	      <if test="item.jsrq != null" >
	        JSRQ,
	      </if>
	      <if test="item.bzsm != null" >
	        BZSM,
	      </if>
	      <if test="item.czyxm != null" >
	        CZYXM,
	      </if>
	      <if test="item.yljgbm != null" >
	        YLJGBM,
	      </if>
	      <if test="item.qtzf != null" >
	        QTZF,
	      </if>
	      <if test="item.bxjsh != null" >
	        BXJSH,
	      </if>
	      <if test="item.czip != null" >
	        CZIP,
	      </if>
		  <if test="item.ifbank != null and item.ifbank != ''.toString()" >
			IFBANK,
		  </if>
		  <if test="item.payno != null and item.payno != ''.toString()" >
		    PAY_NO,
		  </if>
		  <if test="item.paychannel != null and item.paychannel != ''.toString()" >
		    PAY_CHANNEL,
		  </if>
			<if test="item.orderNo != null and item.orderNo != ''.toString()" >
				ORDERNO,
			</if>
			<if test="item.yhhj != null" >
				YHHJ,
			</if>
			<if test="item.yjyrq != null" >
				YJYRQ,
			</if>
			<if test="item.ybfyhj != null" >
				YBFYHJ,
			</if>
			<if test="item.couponAmount != null" >
				COUPONAMOUNT,
			</if>
			<if test="item.couponPrice != null" >
				COUPONPRICE
			</if>
	    </trim>
	    <trim prefix="values (" suffix=")" suffixOverrides="," >
	      <if test="item.jsjlid != null" >
	        #{item.jsjlid,jdbcType=VARCHAR},
	      </if>
	      <if test="item.ryghxh != null" >
	        #{item.ryghxh,jdbcType=VARCHAR},
	      </if>
	      <if test="item.rybrid != null" >
	        #{item.rybrid,jdbcType=VARCHAR},
	      </if>
	      <if test="item.rybxlbbm != null" >
	        #{item.rybxlbbm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.ryjkpzh != null" >
	        #{item.ryjkpzh,jdbcType=VARCHAR},
	      </if>
	      <if test="item.fyhj != null" >
	        #{item.fyhj,jdbcType=DECIMAL},
	      </if>
	      <if test="item.ybkzf != null" >
	        #{item.ybkzf,jdbcType=DECIMAL},
	      </if>
	      <if test="item.ylkzf != null" >
	        #{item.ylkzf,jdbcType=DECIMAL},
	      </if>
	      <if test="item.xjzf != null" >
	        #{item.xjzf,jdbcType=DECIMAL},
	      </if>
	      <if test="item.zflxbm != null" >
	        #{item.zflxbm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.czybm != null" >
	        #{item.czybm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.jszt != null" >
	        #{item.jszt,jdbcType=CHAR},
	      </if>
	      <if test="item.jsrq != null" >
	        #{item.jsrq,jdbcType=TIMESTAMP},
	      </if>
	      <if test="item.bzsm != null" >
	        #{item.bzsm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.czyxm != null" >
	        #{item.czyxm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.yljgbm != null" >
	        #{item.yljgbm,jdbcType=VARCHAR},
	      </if>
	       <if test="item.qtzf != null" >
	        #{item.qtzf,jdbcType=DECIMAL},
	      </if>
	       <if test="item.bxjsh != null" >
	        #{item.bxjsh,jdbcType=DECIMAL},
	      </if>
	         <if test="item.czip != null" >
	        #{item.czip,jdbcType=VARCHAR},
	      </if>
		  <if test="item.ifbank != null and item.ifbank != ''.toString()" >
			  #{item.ifbank,jdbcType=VARCHAR},
		  </if>
		  <if test="item.payno != null and item.payno != ''.toString()" >
			  #{item.payno,jdbcType=VARCHAR},
		  </if>
		  <if test="item.paychannel != null and item.paychannel != ''.toString()" >
			  #{item.paychannel,jdbcType=VARCHAR},
		  </if>
			<if test="item.orderNo != null and item.orderNo != ''.toString()" >
				#{item.orderNo,jdbcType=VARCHAR},
			</if>
			<if test="item.yhhj != null" >
				#{item.yhhj,jdbcType=DECIMAL},
			</if>
			<if test="item.yjyrq != null" >
				#{item.yjyrq,jdbcType=VARCHAR},
			</if>
			<if test="item.ybfyhj != null" >
				#{item.ybfyhj,jdbcType=DECIMAL},
			</if>
			<if test="item.couponAmount != null" >
				#{item.couponAmount,jdbcType=VARCHAR},
			</if>
			<if test="item.couponPrice != null" >
				#{item.couponPrice,jdbcType=VARCHAR}
			</if>
	    </trim>
	  </foreach>
  </insert>

  <!-- 更新 -->
  <update id="update" parameterType="java.util.List" >
  	<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
	    update MZB_JSJL
	    <set >
	      <if test="item.ryghxh != null" >
	        RYGHXH = #{item.ryghxh,jdbcType=VARCHAR},
	      </if>
	      <if test="item.rybrid != null" >
	        RYBRID = #{item.rybrid,jdbcType=VARCHAR},
	      </if>
	      <if test="item.rybxlbbm != null" >
	        RYBXLBBM = #{item.rybxlbbm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.ryjkpzh != null" >
	        RYJKPZH = #{item.ryjkpzh,jdbcType=VARCHAR},
	      </if>
	      <if test="item.fyhj != null" >
	        FYHJ = #{item.fyhj,jdbcType=DECIMAL},
	      </if>
	      <if test="item.ybkzf != null" >
	        YBKZF = #{item.ybkzf,jdbcType=DECIMAL},
	      </if>
	      <if test="item.ylkzf != null" >
	        YLKZF = #{item.ylkzf,jdbcType=DECIMAL},
	      </if>
	      <if test="item.xjzf != null" >
	        XJZF = #{item.xjzf,jdbcType=DECIMAL},
	      </if>
	      <if test="item.zflxbm != null" >
	        ZFLXBM = #{item.zflxbm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.czybm != null" >
	        CZYBM = #{item.czybm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.jszt != null" >
	        JSZT = #{item.jszt,jdbcType=CHAR},
	      </if>
	      <if test="item.jsrq != null" >
	        JSRQ = #{item.jsrq,jdbcType=TIMESTAMP},
	      </if>
	      <if test="item.bzsm != null" >
	        BZSM = #{item.bzsm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.czyxm != null" >
	        CZYXM = #{item.czyxm,jdbcType=VARCHAR},
	      </if>
	      <if test="item.qtzf != null" >
	        QTZF = #{item.qtzf,jdbcType=DECIMAL},
	      </if>
	      <if test="item.bxjsh != null" >
	        BXJSH = #{item.bxjsh,jdbcType=DECIMAL},
	      </if>
	       <if test="item.czip != null" >
	        CZIP = #{item.czip,jdbcType=VARCHAR},
	      </if>
	    </set>
	    where JSJLID = #{item.jsjlid,jdbcType=VARCHAR} and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
	  </foreach>
	</update>


  		<select id="qtzftjZflxhj" resultMap="Qtzfzflx" parameterType="com.supx.csp.api.mzsf.cxtj.Mzsf_Cxtj_Qtzftj_ZflxResModel">
		select qt.zflxmc,qt.zflxbm,sum(qt.zfje) zfhj
		 from (select sum(zfje) zfje,jsjlid,yljgbm,zflxbm,zflxmc from  MZB_JSJL_QTZF
		 where yljgbm= #{yljgbm,jdbcType=VARCHAR}
		  group by
		 zflxbm,zflxmc,jsjlid,yljgbm)
		qt inner join mzb_jsjl js on js.jsjlid=qt.jsjlid and js.yljgbm=qt.yljgbm
		<if test="ksrq !=null and ksrq !=''">
	 		and js.jsrq &gt;= #{ksrq,jdbcType=DATE}
	 	</if>
	 	<if test="jsrqq !=null and jsrqq !=''">
	 		and js.jsrq &lt;= #{jsrqq,jdbcType=DATE}
	 	</if>
		 group by qt.zflxbm,qt.zflxmc order by qt.zflxbm
		</select>


		<select id="qtzftjKshj" resultMap="Qtzfksje" parameterType="com.supx.csp.api.mzsf.cxtj.Mzsf_Cxtj_Qtzftj_KsjeResModel">
			select gh.ghks ksbm,gh.ghksmc ksmc,sum(qt.zfje) zfje
			 from (select sum(zfje) zfje,yljgbm,ryghxh,jsjlid from mzb_jsjl_qtzf
			 where yljgbm= #{yljgbm,jdbcType=VARCHAR}
			 group by ryghxh,jsjlid,yljgbm
			 ) qt inner join mzb_jsjl js on js.jsjlid=qt.jsjlid and
			 js.yljgbm=qt.yljgbm
			<if test="ksrq !=null and ksrq !=''">
	 		and js.jsrq &gt;= #{ksrq,jdbcType=DATE}
	 		</if>
	 		<if test="jsrqq !=null and jsrqq !=''">
	 		and js.jsrq &lt;= #{jsrqq,jdbcType=DATE}
	 		</if>
			  inner join ghb_brgh gh on gh.ghxh=qt.ryghxh
			 and gh.yljgbm=qt.yljgbm
			 group by gh.ghks,gh.ghksmc order by gh.ghks asc
		</select>


		<select id="qtzftjKsmx" resultMap="Qtzfksmx" parameterType="com.supx.csp.api.mzsf.cxtj.Mzsf_Cxtj_Qtzftj_Ksje_flmxModel">
		select flmx.ksbm,flmx.ksmc,flmx.zflxmc,flmx.zflxbm,sum(flmx.mxje) zfje,
		flmx.mxje
		 from
		(select gh.ghks ksbm,gh.ghksmc ksmc,qt.zflxmc,qt.zflxbm,sum(qt.zfje) mxje
		from mzb_jsjl_qtzf qt
  	 	inner join ghb_brgh gh on gh.ghxh=qt.ryghxh
  	 	and gh.yljgbm=qt.yljgbm inner join mzb_jsjl js
   		on js.jsjlid=qt.jsjlid and js.yljgbm=qt.yljgbm
	 	<where>
			qt.yljgbm= #{yljgbm,jdbcType=VARCHAR}
		<if test="ksrq !=null and ksrq !=''">
	 		and js.jsrq &gt;= #{ksrq,jdbcType=DATE}
	 	</if>
	 	<if test="jsrqq !=null and jsrqq !=''">
	 		and js.jsrq &lt;= #{jsrqq,jdbcType=DATE}
	 	</if>
		</where>
		 group by qt.zflxbm,qt.zflxmc,gh.ghks,gh.ghksmc
		)flmx group by flmx.zflxbm,flmx.zflxmc,flmx.ksbm,flmx.ksmc,flmx.mxje
		order by flmx.ksbm asc
		</select>
		</mapper>
