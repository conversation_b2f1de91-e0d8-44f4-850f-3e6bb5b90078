<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.ykgl.kccx.dao.New1Ykb_kccxModelMapper">
	<select id="queryKccxByYpOld" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		SELECT  YPBM, YPMC, HXMC, HXMCDM, YPGG, JXBM, GXBM, ZLBM, CDBM, FZBL, KFDW, YFDW, YBTCLB, NBTCLB,
		JBJL, JLDW, KCFBZ, YYFF, PSYP, PSXQ, KSSJB, GJJBYW, SBJBYW, DDDS, DDD_HL, TYBZ, ZDJL, KFBM, FYJL,
		FYDW, TXM, sum(KCSL) kcsl, JXMC, GXMC, YPZLMC, CDMC, KFDWMC, YFDWMC, TCLBMC, JLDWMC FROM
		(
		select distinct ZD.YPBM, ZD.YPMC, ZD.HXMC, ZD.HXMCDM,	ZD.YPGG, ZD.JXBM, ZD.GXBM,
		ZD.ZLBM, ZD.CDBM, ZD.FZBL, ZD.KFDW, ZD.YFDW, ZD.YBTCLB,
		ZD.NBTCLB, ZD.JBJL, ZD.JLDW,	ZD.KCFBZ, ZD.YYFF, ZD.PSYP,	ZD.PSXQ,ZD.KSSJB,
		ZD.GJJBYW, ZD.SBJBYW,ZD.DDDS,ZD.DDD_HL, KC.CPBZH, KC.PZWH, ZD.TYBZ, ZD.ZDJL, ZD.KFBM,
		ZD.FYJL,ZD.FYDW,ZD.TXM, KC.KCSL, KC.GHDW,
		jx.jxmc,gx.gxmc,zl.ypzlmc,cd.cdmc,kfdw.jldwmc	kfdwmc,yfdw.jldwmc yfdwmc,ybtc.tclbmc,
		jldw.jldwmc	FROM YKB_YPZD ZD
		INNER JOIN YKB_YPKC KC ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM 	and zd.yljgbm = kc.yljgbm
		inner join ykb_ypjx jx on zd.jxbm = jx.jxbm and	zd.yljgbm = jx.yljgbm
		inner join ykb_ypgx gx on zd.gxbm = gx.gxbm	and	zd.yljgbm = gx.yljgbm
		inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and	zd.yljgbm = zl.yljgbm
		left join ykb_cdbm cd on zd.cdbm = cd.cdbm and	zd.yljgbm = cd.yljgbm
		inner join ykb_jldwbm kfdw on zd.kfdw =	kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
		inner join ykb_jldwbm yfdw on	zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
		inner join ykb_yptclb	ybtc on zd.ybtclb =	ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
		inner join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm

		where zd.yljgbm = #{yljgbm,jdbcType=VARCHAR} and to_char(kc.UPTIMESTAMP,'yyyy-MM-dd')=to_char(#{tzrq},'yyyy-MM-dd')
		<if test="zeroKc != null and zeroKc == '1'.toString()">
			and kc.kcsl > 0
		</if>
		<if test="kfbm != null">
			and zd.kfbm = #{kfbm,jdbcType=VARCHAR}
		</if>
		<if test="endrq != null ">
			and kc.yxqz &lt;= #{endrq,jdbcType=TIMESTAMP}
		</if>
		<if test="beginrq != null ">
			and kc.yxqz &gt;= #{beginrq,jdbcType=TIMESTAMP}
		</if>
		<if test="parm != null and parm != '' ">
			and (zd.YPBM like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or zd.YPMC like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or lower(zd.PYDM) like	'%'||lower(#{parm,jdbcType=VARCHAR})||'%')
		</if>
		)

		group by  YPBM, YPMC, HXMC, HXMCDM, YPGG, JXBM, GXBM, ZLBM, CDBM, FZBL, KFDW, YFDW, YBTCLB, NBTCLB,
		JBJL, JLDW, KCFBZ, YYFF, PSYP, PSXQ, KSSJB, GJJBYW, SBJBYW, DDDS, DDD_HL,TYBZ, ZDJL, KFBM, FYJL,
		FYDW, TXM, JXMC, GXMC, YPZLMC, CDMC, KFDWMC, YFDWMC, TCLBMC, JLDWMC

		order by ypbm


	</select>
	<select id="queryKccxOld" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		select distinct ZD.YPBM, ZD.YPMC, ZD.HXMC, ZD.HXMCDM,	ZD.YPGG, ZD.JXBM, ZD.GXBM,
		ZD.ZLBM, ZD.CDBM,ZD.BZBM, ZD.FZBL, kc.YPJJ,	kc.YPLJ, ZD.KFDW, ZD.YFDW, ZD.YBTCLB,
		ZD.NBTCLB, ZD.JBJL, ZD.JLDW,ZD.KCFBZ, ZD.YYFF, ZD.PSYP,	ZD.PSXQ,ZD.KSSJB,
		ZD.GJJBYW, ZD.SBJBYW,ZD.DDDS,ZD.DDD_HL, KC.CPBZH, KC.PZWH, ZD.TYBZ, ZD.ZDJL, ZD.KFBM,
		ZD.FYJL,ZD.FYDW,ZD.TXM, KC.KCSL, KC.YXQZ, KC.SCPH, KC.GHDW, KC.XTPH,KC.SCRQ,
		jx.jxmc,gx.gxmc,zl.ypzlmc,cd.cdmc,kfdw.jldwmc	kfdwmc,yfdw.jldwmc yfdwmc,ybtc.tclbmc,
		jldw.jldwmc,dw.dwmc ghdwmc,zd.ypspm,ypbx.BXXMBM_GJBM ybgjm	FROM YKB_YPZD ZD
		INNER JOIN ykb_ypkc_bak KC ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM 	and zd.yljgbm = kc.yljgbm
		left join ykb_ypjx jx on zd.jxbm = jx.jxbm and	zd.yljgbm = jx.yljgbm
		left join ykb_ypgx gx on zd.gxbm = gx.gxbm	and	zd.yljgbm = gx.yljgbm
		left join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and	zd.yljgbm = zl.yljgbm
		left join ykb_cdbm cd on zd.cdbm = cd.cdbm and	zd.yljgbm = cd.yljgbm
		left join ykb_jldwbm kfdw on zd.kfdw =	kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
		left join ykb_jldwbm yfdw on	zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
		left join ykb_yptclb	ybtc on zd.ybtclb =	ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
		left join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm
		left join ykb_ghdw dw on dw.DWBM = ZD.GHDW and zd.yljgbm = dw.yljgbm
		left join gyb_ypbxxm ypbx on ypbx.ypbm = zd.ypbm and ypbx.bxlbbm='02'
		where zd.yljgbm = #{yljgbm,jdbcType=VARCHAR} and KC.kcsl != 0 and to_char(kc.UPTIMESTAMP,'yyyy-MM-dd')=to_char(#{tzrq},'yyyy-MM-dd')
		<if test="kfbm != null">
			and zd.kfbm = #{kfbm,jdbcType=VARCHAR}
		</if>
		<if test="endrq != null ">
			and kc.yxqz &lt;= #{endrq,jdbcType=TIMESTAMP}
		</if>
		<if test="beginrq != null ">
			and kc.yxqz &gt;= #{beginrq,jdbcType=TIMESTAMP}
		</if>
		<choose>
			<when test="jxbm!=null and jxbm!=''.toString()">
				and zd.jxbm in(${jxbm})
			</when>
			<!--<otherwise>
                and zd.jxbm is null
            </otherwise>-->
		</choose>
		<choose>
			<when test="zlbm!=null and zlbm!=''.toString()">
				and zd.zlbm in (${zlbm})
			</when>
			<!--<otherwise>
                and zd.zlbm is null
            </otherwise>-->
		</choose>
		<if test="parm != null and parm != '' ">
			and (zd.YPBM like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or zd.YPMC like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or lower(zd.PYDM) like	'%'||lower(#{parm,jdbcType=VARCHAR})||'%')
		</if>

		order by ypbm
	</select>
	<!-- // 库存查询 -->
	<select id="queryKccx" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
	resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		select distinct ZD.YPBM, ZD.YPMC, ZD.HXMC, ZD.HXMCDM,	ZD.YPGG, ZD.JXBM, ZD.GXBM,
		ZD.ZLBM, ZD.CDBM,ZD.BZBM, ZD.FZBL, kc.YPJJ,	kc.YPLJ, ZD.KFDW, ZD.YFDW, ZD.YBTCLB,
		ZD.NBTCLB, ZD.JBJL, ZD.JLDW,	ZD.KCFBZ, ZD.YYFF, ZD.PSYP,	ZD.PSXQ,ZD.KSSJB,
		ZD.GJJBYW, ZD.SBJBYW,ZD.DDDS,ZD.DDD_HL, KC.CPBZH, KC.PZWH, ZD.TYBZ, ZD.ZDJL, ZD.KFBM,
		ZD.FYJL,ZD.FYDW,ZD.TXM, KC.KCSL, KC.YXQZ, KC.SCPH, KC.GHDW, KC.XTPH,KC.SCRQ,
		jx.jxmc,gx.gxmc,zl.ypzlmc,cd.cdmc,kfdw.jldwmc	kfdwmc,yfdw.jldwmc yfdwmc,ybtc.tclbmc,
		jldw.jldwmc,ypbx.BXXMBM_GJBM ybgjm	FROM ykb_ypzd ZD
		INNER JOIN ykb_ypkc KC ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM 	and zd.yljgbm = kc.yljgbm
		left join ykb_ypjx jx on zd.jxbm = jx.jxbm and	zd.yljgbm = jx.yljgbm
		left join ykb_ypgx gx on zd.gxbm = gx.gxbm	and	zd.yljgbm = gx.yljgbm
		left join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and	zd.yljgbm = zl.yljgbm
		left join ykb_cdbm cd on zd.cdbm = cd.cdbm and	zd.yljgbm = cd.yljgbm
		left join ykb_jldwbm kfdw on zd.kfdw =	kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
		left join ykb_jldwbm yfdw on	zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
		left join ykb_yptclb	ybtc on zd.ybtclb =	ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
		left join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm
		left join gyb_ypbxxm ypbx on ypbx.ypbm = zd.ypbm and ypbx.bxlbbm='02'
		where zd.yljgbm = #{yljgbm,jdbcType=VARCHAR} and kc.kcsl != 0
		<if test="kfbm != null">
			and zd.kfbm = #{kfbm,jdbcType=VARCHAR}
		</if>
		 <if test="endrq != null ">
			and kc.yxqz &lt;= #{endrq,jdbcType=TIMESTAMP}
		</if>
		<if test="beginrq != null ">
			and kc.yxqz &gt;= #{beginrq,jdbcType=TIMESTAMP}
		</if>
		<if test="parm != null and parm != '' ">
			and (zd.YPBM like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or zd.YPMC like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or lower(zd.PYDM) like	'%'||lower(#{parm,jdbcType=VARCHAR})||'%')
		</if>

		order by zd.ypbm
	</select>
	<resultMap id="KczeResult" type="java.util.HashMap">
		<result property="totalAmount" column="totalAmount"/>
	</resultMap>
	<!-- // 库存总额查询 -->
	<select id="queryKcze" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultMap="KczeResult">
		select round(sum(kcsl * YPLJ) ,4) totalAmount from(
		select distinct ZD.YPBM, ZD.YPMC, ZD.HXMC, ZD.HXMCDM,	ZD.YPGG, ZD.JXBM, ZD.GXBM,
		ZD.ZLBM, ZD.CDBM,ZD.BZBM, ZD.FZBL, kc.YPJJ,	kc.YPLJ, ZD.KFDW, ZD.YFDW, ZD.YBTCLB,
		ZD.NBTCLB, ZD.JBJL, ZD.JLDW,	ZD.KCFBZ, ZD.YYFF, ZD.PSYP,	ZD.PSXQ,ZD.KSSJB,
		ZD.GJJBYW, ZD.SBJBYW,ZD.DDDS,ZD.DDD_HL, KC.CPBZH, KC.PZWH, ZD.TYBZ, ZD.ZDJL, ZD.KFBM,
		ZD.FYJL,ZD.FYDW,ZD.TXM, KC.KCSL, KC.YXQZ, KC.SCPH, KC.GHDW, KC.XTPH,KC.SCRQ,
		jx.jxmc,gx.gxmc,zl.ypzlmc,cd.cdmc,kfdw.jldwmc	kfdwmc,yfdw.jldwmc yfdwmc,ybtc.tclbmc,
		jldw.jldwmc	FROM ykb_ypzd ZD
		INNER JOIN ykb_ypkc KC ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM 	and zd.yljgbm = kc.yljgbm
		left join ykb_ypjx jx on zd.jxbm = jx.jxbm and	zd.yljgbm = jx.yljgbm
		left join ykb_ypgx gx on zd.gxbm = gx.gxbm	and	zd.yljgbm = gx.yljgbm
		left join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and	zd.yljgbm = zl.yljgbm
		left join ykb_cdbm cd on zd.cdbm = cd.cdbm and	zd.yljgbm = cd.yljgbm
		left join ykb_jldwbm kfdw on zd.kfdw =	kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
		left join ykb_jldwbm yfdw on	zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
		left join ykb_yptclb	ybtc on zd.ybtclb =	ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
		left join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm

		where zd.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		<if test="kfbm != null">
			and zd.kfbm = #{kfbm,jdbcType=VARCHAR}
		</if>
		<if test="endrq != null ">
			and kc.yxqz &lt;= #{endrq,jdbcType=TIMESTAMP}
		</if>
		<if test="beginrq != null ">
			and kc.yxqz &gt;= #{beginrq,jdbcType=TIMESTAMP}
		</if>
		<if test="parm != null and parm != '' ">
			and (zd.YPBM like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or zd.YPMC like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or lower(zd.PYDM) like	'%'||lower(#{parm,jdbcType=VARCHAR})||'%')
		</if>

		order by ypbm
		)
	</select>
	<!-- // 库存查询按药品汇总 -->
	<select id="queryKccxByYp" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
	resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		SELECT  YPBM, YPMC, HXMC, HXMCDM, YPGG, JXBM, GXBM, ZLBM, CDBM, FZBL, KFDW, YFDW, YBTCLB, NBTCLB,
 		JBJL, JLDW, KCFBZ, YYFF, PSYP, PSXQ, KSSJB, GJJBYW, SBJBYW, DDDS, DDD_HL, TYBZ, ZDJL, KFBM, FYJL,
 		FYDW, TXM, sum(KCSL) kcsl, JXMC, GXMC, YPZLMC, CDMC, KFDWMC, YFDWMC, TCLBMC, JLDWMC FROM
 			(
			select distinct ZD.YPBM, ZD.YPMC, ZD.HXMC, ZD.HXMCDM,	ZD.YPGG, ZD.JXBM, ZD.GXBM,
			ZD.ZLBM, ZD.CDBM, ZD.FZBL, ZD.KFDW, ZD.YFDW, ZD.YBTCLB,
			ZD.NBTCLB, ZD.JBJL, ZD.JLDW,	ZD.KCFBZ, ZD.YYFF, ZD.PSYP,	ZD.PSXQ,ZD.KSSJB,
			ZD.GJJBYW, ZD.SBJBYW,ZD.DDDS,ZD.DDD_HL, KC.CPBZH, KC.PZWH, ZD.TYBZ, ZD.ZDJL, ZD.KFBM,
			ZD.FYJL,ZD.FYDW,ZD.TXM, KC.KCSL, KC.GHDW,
			jx.jxmc,gx.gxmc,zl.ypzlmc,cd.cdmc,kfdw.jldwmc	kfdwmc,yfdw.jldwmc yfdwmc,ybtc.tclbmc,
			jldw.jldwmc	FROM YKB_YPZD ZD
			INNER JOIN YKB_YPKC KC ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM 	and zd.yljgbm = kc.yljgbm
			inner join ykb_ypjx jx on zd.jxbm = jx.jxbm and	zd.yljgbm = jx.yljgbm
			inner join ykb_ypgx gx on zd.gxbm = gx.gxbm	and	zd.yljgbm = gx.yljgbm
			inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and	zd.yljgbm = zl.yljgbm
			left join ykb_cdbm cd on zd.cdbm = cd.cdbm and	zd.yljgbm = cd.yljgbm
			inner join ykb_jldwbm kfdw on zd.kfdw =	kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
			inner join ykb_jldwbm yfdw on	zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
			inner join ykb_yptclb	ybtc on zd.ybtclb =	ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
			inner join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm

			where zd.yljgbm = #{yljgbm,jdbcType=VARCHAR}
			<if test="kfbm != null">
				and zd.kfbm = #{kfbm,jdbcType=VARCHAR}
			</if>
			 <if test="endrq != null ">
				and kc.yxqz &lt;= #{endrq,jdbcType=TIMESTAMP}
			</if>
			<if test="beginrq != null ">
				and kc.yxqz &gt;= #{beginrq,jdbcType=TIMESTAMP}
			</if>

			<if test="parm != null and parm != '' ">
			and (zd.YPBM like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or zd.YPMC like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or lower(zd.PYDM) like	'%'||lower(#{parm,jdbcType=VARCHAR})||'%')
			</if>
			)

	    group by  YPBM, YPMC, HXMC, HXMCDM, YPGG, JXBM, GXBM, ZLBM, CDBM, FZBL, KFDW, YFDW, YBTCLB, NBTCLB,
	    JBJL, JLDW, KCFBZ, YYFF, PSYP, PSXQ, KSSJB, GJJBYW, SBJBYW, DDDS, DDD_HL,TYBZ, ZDJL, KFBM, FYJL,
	    FYDW, TXM, JXMC, GXMC, YPZLMC, CDMC, KFDWMC, YFDWMC, TCLBMC, JLDWMC

		order by ypbm


	</select>

	<!-- // 历史库存 -->
	<select id="queryLskc" parameterType="com.supx.csp.api.ykgl.kfyw.pojo.Ykb_ckdModel"
	resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		select distinct ZD.YPBM, ZD.YPMC, ZD.HXMC, ZD.HXMCDM,	ZD.YPGG, ZD.JXBM, ZD.GXBM,
		ZD.ZLBM, ZD.CDBM,ZD.BZBM, ZD.FZBL, kc.YPJJ,	kc.YPLJ, ZD.KFDW, ZD.YFDW, ZD.YBTCLB,
		ZD.NBTCLB, ZD.JBJL, ZD.JLDW,	ZD.KCFBZ, ZD.YYFF, ZD.PSYP,	ZD.PSXQ,ZD.KSSJB,
		ZD.GJJBYW, ZD.SBJBYW,ZD.DDDS,ZD.DDD_HL, KC.CPBZH, KC.PZWH, ZD.TYBZ, ZD.ZDJL, ZD.KFBM,
		ZD.FYJL,ZD.FYDW,ZD.TXM, KC.KCSL, KC.YXQZ, KC.SCPH, KC.GHDW, KC.XTPH,KC.SCRQ,
		jx.jxmc,gx.gxmc,zl.ypzlmc,cd.cdmc,kfdw.jldwmc	kfdwmc,yfdw.jldwmc yfdwmc,ybtc.tclbmc,
		jldw.jldwmc	FROM YKB_YPZD ZD
		INNER JOIN YKB_YPKC_BAK KC ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM 	and zd.yljgbm = kc.yljgbm
		inner join ykb_ypjx jx on zd.jxbm = jx.jxbm and	zd.yljgbm = jx.yljgbm
		inner join ykb_ypgx gx on zd.gxbm = gx.gxbm	and	zd.yljgbm = gx.yljgbm
		inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and	zd.yljgbm = zl.yljgbm
		left join ykb_cdbm cd on zd.cdbm = cd.cdbm and	zd.yljgbm = cd.yljgbm
		inner join ykb_jldwbm kfdw on zd.kfdw =	kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
		inner join ykb_jldwbm yfdw on	zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
		inner join ykb_yptclb	ybtc on zd.ybtclb =	ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
		inner join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm
		left join (select ckdmx.yljgbm,sum(ckdmx.cksl)	cksl,ckdmx.xtph,ckdmx.ypbm,ckd.kfbm,ckd.ckdh from ykb_ckdmx ckdmx
		inner join ykb_ckd	ckd on ckd.ckdh=ckdmx.ckdh 	and	ckd.yljgbm = ckdmx.yljgbm
		group by
		ckdmx.xtph,ckdmx.ypbm,ckd.kfbm,ckd.ckdh,ckdmx.yljgbm) b on kc.ypbm = b.ypbm
		and kc.xtph = b.xtph and kc.kfbm = b.kfbm and zd.yljgbm = b.yljgbm
		where zd.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		<if test="kfbm != null">
			and zd.kfbm = #{kfbm,jdbcType=VARCHAR}
		</if>
		 <if test="endrq != null ">
			and kc.yxqz &lt;= #{endrq,jdbcType=TIMESTAMP}
		</if>
		<if test="beginrq != null ">
			and kc.yxqz &gt;= #{beginrq,jdbcType=TIMESTAMP}
		</if>

		order by ypbm
	</select>

	<!-- 药品月报表(期初金额) -->
	<select id="queryQcje" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel"
	resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel">
		SELECT rq,YPZLMC, YPZLBM, sum(kcsl) kcsl ,sum(je) qcje FROM (
		SELECT kc.rq,zl.ypzlmc,zl.ypzlbm,kc.kcsl, round(kc.ypjj*kc.kcsl,2) je
		FROM ykb_ypkc_bak kc
		inner join ykb_ypzd zd on kc.ypbm = zd.ypbm and kc.yljgbm = zd.yljgbm
		inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
		where
		trunc(kc.rq) &gt;= (#{beginrq,jdbcType=DATE} - 1)
		<if test="kfbm != null">
			and kc.kfbm = #{kfbm,jdbcType=VARCHAR}
			and kc.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		</if>
		 )
		group by rq,YPZLMC, YPZLBM
		order by ypzlbm
	</select>

	<!-- 药品月报表-入库金额 -->
	<select id="queryRkje" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel"
	resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel">
		select rkfs,ypzlbm,ypzlmc,sum(rksl) rksl, sum(rkje) crje from(
		select zl.ypzlbm,zl.ypzlmc,rk.shzfrq,rk.rkfs,rkmx.ypbm,rkmx.rksl,round(rkmx.ypjj*rkmx.rksl,2) rkje from ykb_rkdmx rkmx
		inner join ykb_rkd rk on rkmx.rkdh = rk.rkdh and rk.yljgbm = rkmx.yljgbm
   		inner join ykb_ypzd zd on rkmx.ypbm = zd.ypbm and rkmx.yljgbm = zd.yljgbm
  		inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
		where rk.shzfbz = 1
		<if test="kfbm != null">
			 and  rk.kfbm = #{kfbm,jdbcType=VARCHAR}
			 and  rk.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		</if>
		<if test="endrq != null ">
			and trunc(rk.shzfrq) &lt;= #{endrq,jdbcType=DATE}
		</if>
		<if test="beginrq != null ">
			and trunc(rk.shzfrq) &gt;= #{beginrq,jdbcType=DATE}
		</if>

    )
    	group by rkfs,ypzlbm,ypzlmc
    	order by ypzlbm

	</select>

	<!-- 药品月报表-出库金额 -->
	<select id="queryCkje" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel"
	resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel">
		SELECT ckfs,ypzlbm,ypzlmc,sum(cksl) cksl,sum(ckje) crje FROM (
		select zl.ypzlbm,zl.ypzlmc,ck.ckfs,ckmx.ypbm,ckmx.cksl,round(ckmx.ypjj*ckmx.cksl,2) ckje
		from ykb_ckdmx ckmx
		inner join ykb_ckd ck on ckmx.ckdh = ck.ckdh and ck.yljgbm = ckmx.yljgbm
	    inner join ykb_ypzd zd on ckmx.ypbm = zd.ypbm and ckmx.yljgbm = zd.yljgbm
	    inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
		where ck.shzfbz = 1
		<if test="kfbm != null">
			 and  ck.kfbm = #{kfbm,jdbcType=VARCHAR}
			 and  ck.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		</if>
		<if test="endrq != null ">
			and trunc(ck.shzfrq) &lt;= #{endrq,jdbcType=DATE}
		</if>
		<if test="beginrq != null ">
			and trunc(ck.shzfrq) &gt;= #{beginrq,jdbcType=DATE}
		</if>
		)
	    group by ckfs,ypzlbm,ypzlmc
	    order by ypzlbm
	</select>

	<!-- 药品月报表(结余金额,截止日期为当天) -->
	<select id="queryJyje" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel"
	resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_ykybbModel">
		SELECT kfbm,YPZLMC, YPZLBM, sum(kcsl) kcsl ,sum(je) qcje FROM (
		SELECT kc.kfbm,zl.ypzlmc,zl.ypzlbm,kc.kcsl, round(kc.ypjj*kc.kcsl,2) je
		FROM ykb_ypkc kc
		inner join ykb_ypzd zd on kc.ypbm = zd.ypbm and kc.yljgbm = zd.yljgbm
		inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
		<where>
			<if test="kfbm != null">
			kc.kfbm = #{kfbm,jdbcType=VARCHAR}
			and kc.yljgbm = #{yljgbm,jdbcType=VARCHAR}
			</if>
		</where>
		 )
		group by kfbm,YPZLMC, YPZLBM
		order by ypzlbm

	</select>
	<update id="updatePdYpKc" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		update  ykb_ypkc set pdrq=sysdate,pdsjkc=#{pdsjkc},pdyk=#{pdyk},pdczy=#{pdczy},pdczymc=#{pdczymc} where pcty=0
																											and xtph = #{xtph,jdbcType=VARCHAR}
																											and kfbm = #{kfbm,jdbcType=VARCHAR}
																											and scph=#{scph}
																											and ypbm=#{ypbm} and yljgbm=#{yljgbm}
	</update>

	<select id="selectPdzt" resultType="java.lang.Integer">
		select count(1) from  ykb_ypkc where kfbm = #{kfbm,jdbcType=VARCHAR} and pcty=0 and pdrq is not null and yljgbm=#{yljgbm}
	</select>

	<update id="updatePd">
		update ykb_ypkc set pdrq=sysdate,
							pdzcsl = kcsl,kcsl=0
		where kfbm = #{kfbm,jdbcType=VARCHAR} and pcty=0 and yljgbm=#{yljgbm}
	</update>
	<delete id="deleteKc">
		delete from ykb_ypkc where kcsl=0 and kfbm = #{kfbm,jdbcType=VARCHAR} and yljgbm=#{yljgbm}
	</delete>
	<update id="updatePdwc" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		update  ykb_ypkc set pdshrq=sysdate,kcsl = pdsjkc,pdshrybm=#{pdshrybm} where pcty=0
																				 and kfbm=#{kfbm} and yljgbm=#{yljgbm}
	</update>
	<select id="selectPdlb" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		select distinct ZD.YPBM, ZD.YPMC, ZD.HXMC, ZD.HXMCDM, ZD.YPGG, ZD.JXBM,ZD.GXBM,
		ZD.ZLBM, ZD.CDBM, ZD.FZBL, ZD.KFDW, ZD.YFDW, ZD.YBTCLB,KC.scph,kc.scrq,KC.xtph,KC.yplj,KC.ypjj,KC.Yxqz,
		ZD.NBTCLB, ZD.JBJL, ZD.JLDW,ZD.KCFBZ, ZD.YYFF, ZD.PSYP, ZD.PSXQ,ZD.KSSJB,gh.dwmc ghdwmc,
		ZD.GJJBYW, ZD.SBJBYW,ZD.DDDS,ZD.DDD_HL, KC.CPBZH, KC.PZWH, ZD.TYBZ, ZD.ZDJL, ZD.KFBM,
		ZD.FYJL,ZD.FYDW,ZD.TXM, KC.KCSL, KC.GHDW,KC.pdrq,KC.pdzcsl,
		jx.jxmc,gx.gxmc,zl.ypzlmc,cd.cdmc,kfdw.jldwmc kfdwmc,yfdw.jldwmc yfdwmc,ybtc.tclbmc,
		jldw.jldwmc FROM  YKB_YPKC KC
		INNER JOIN YKB_YPZD ZD ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM and zd.yljgbm = kc.yljgbm
		inner join ykb_ypjx jx on zd.jxbm = jx.jxbm and zd.yljgbm = jx.yljgbm
		inner join ykb_ypgx gx on zd.gxbm = gx.gxbm and zd.yljgbm = gx.yljgbm
		inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
		left join ykb_cdbm cd on KC.cdbm = cd.cdbm and KC.yljgbm = cd.yljgbm
		inner join ykb_jldwbm kfdw on zd.kfdw = kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
		inner join ykb_jldwbm yfdw on zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
		inner join ykb_yptclb ybtc on zd.ybtclb = ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
		inner join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm
		left join Ykb_Ghdw gh on KC.Ghdw= gh.dwbm and KC.yljgbm=gh.yljgbm
		where zd.yljgbm = #{yljgbm,jdbcType=VARCHAR} and KC.pcty=0 and kc.pdrq is not null
		<if test="kfbm != null">
			and zd.kfbm = #{kfbm,jdbcType=VARCHAR}
		</if>
		<if test="parm != null and parm != '' ">
			and (zd.YPBM like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or zd.YPMC like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or lower(zd.PYDM) like '%'||lower(#{parm,jdbcType=VARCHAR})||'%')
		</if>
		<if test="pdlc!=null and pdlc==1">
			and kc.pdczy=#{pdczy}
		</if>
		<if test="pdlc!=null and pdlc==2">
			and kc.pdczy is not null
		</if>
		<if test="lrbz!=null">
			and kc.lrbz=0
		</if>
	</select>

	<select id="selectYyk" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultType="com.supx.csp.api.ykgl.kccx.pojo.Pd_YykModel">
		select sum(yplj*pdyk) ljyyk,sum(ypjj*pdyk) jjyyk from ykb_ypkc where kfbm=#{kfbm} and pcty=0 and pdczy is not null and yljgbm=#{yljgbm}
	</select>

	<insert id="insertPdpy" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		insert into ykb_ypkc(ypbm,kfbm,xtph,scph,scrq,yxqz,yplj,ypjj,cdbm,ghdw,kfdw,yfdw,fzbl,uptimestamp,
							 cpbzh,pzwh,yljgbm,pdsjkc,pdrq,pdczy,pdczymc,pdyk,ifpdlr) values
			(#{ypbm},#{kfbm},#{xtph},#{scph},#{scrq},#{yxqz},#{yplj},#{ypjj},#{cdbm},#{ghdw},#{kfdw},#{yfdw},#{fzbl},sysdate,
			 #{cpbzh},#{pzwh},#{yljgbm},#{pdsjkc},sysdate,#{pdczy},#{pdczymc},#{pdsjkc},1)
	</insert>
	<insert id="insertPdmx" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		insert into pdb_ykpdmx
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="ypbm != null">
				ypbm,
			</if>
			<if test="kfbm != null">
				kfbm,
			</if>
			<if test="xtph != null">
				xtph,
			</if>
			<if test="scph != null">
				scph,
			</if>
			<if test="scrq != null">
				scrq,
			</if>
			<if test="yxqz != null">
				yxqz,
			</if>
			<if test="yplj != null">
				yplj,
			</if>
			<if test="ypjj != null">
				ypjj,
			</if>
			<if test="cdbm != null">
				cdbm,
			</if>
			<if test="kfdw != null">
				kfdw,
			</if>
			<if test="yfdw != null">
				yfdw,
			</if>
			<if test="fzbl != null">
				fzbl,
			</if>
			<if test="pzwh != null">
				pzwh,
			</if>
			<if test="yljgbm != null">
				yljgbm,
			</if>
			<if test="cpbzh != null">
				cpbzh,
			</if>
			<if test="pdsjkc != null">
				pdsjkc,
			</if>
			pdrq,
			<if test="pdczy != null">
				pdczy,
			</if>
			<if test="pdzcsl != null">
				pdzcsl,
			</if>
			<if test="pdczymc != null">
				pdczymc,
			</if>
			<if test="pdyk != null">
				pdyk,
			</if>
			<if test="ifpdlr != null">
				ifpdlr,
			</if>
			<if test="ghdw != null">
				ghdw
			</if>
		</trim>

		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="ypbm != null">
				#{ypbm},
			</if>
			<if test="kfbm != null">
				#{kfbm},
			</if>
			<if test="xtph != null">
				#{xtph},
			</if>
			<if test="scph != null">
				#{scph},
			</if>
			<if test="scrq != null">
				#{scrq},
			</if>
			<if test="yxqz != null">
				#{yxqz},
			</if>
			<if test="yplj != null">
				#{yplj},
			</if>
			<if test="ypjj != null">
				#{ypjj},
			</if>
			<if test="cdbm != null">
				#{cdbm},
			</if>
			<if test="kfdw != null">
				#{kfdw},
			</if>
			<if test="yfdw != null">
				#{yfdw},
			</if>
			<if test="fzbl != null">
				#{fzbl},
			</if>
			<if test="pzwh != null">
				#{pzwh},
			</if>
			<if test="yljgbm != null">
				#{yljgbm},
			</if>
			<if test="cpbzh != null">
				#{cpbzh},
			</if>
			<if test="pdsjkc != null">
				#{pdsjkc},
			</if>
			sysdate,
			<if test="pdczy != null">
				#{pdczy},
			</if>
			<if test="pdzcsl != null">
				#{pdzcsl},
			</if>
			<if test="pdczymc != null">
				#{pdczymc},
			</if>
			<if test="pdyk != null">
				#{pdyk},
			</if>
			<if test="ifpdlr != null">
				#{ifpdlr},
			</if>
			<if test="ghdw!= null">
				#{ghdw}
			</if>
		</trim>
	</insert>
	<select id="selectPdxmSum" resultType="java.lang.Integer">
		select count(1) from pdb_ykpdmx where kfbm=#{kfbm} and pdrq is not null
	</select>
	<select id="selectPdmxYyk" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultType="com.supx.csp.api.ykgl.kccx.pojo.Pd_YykModel">
		select round(sum(pdyk*ypjj),4) jjyyk,round(sum(pdyk*yplj),4) ljyyk from(select (sum(mx.pdsjkc)-mx.pdzcsl) pdyk,mx.pdzcsl,mx.yplj/zl.jcbl ypjj,mx.yplj from pdb_ykpdmx mx inner join ykb_ypzd zd on  zd.ypbm = mx.ypbm and zd.yljgbm = mx.yljgbm
																																												 left join ykb_ypzl zl on zl.ypzlbm = zd.zlbm and zl.yljgbm = zd.yljgbm
																				where mx.kfbm=#{kfbm} and mx.yljgbm=#{yljgbm} and to_char(mx.pdrq,'yyyy-mm')=to_char(sysdate,'yyyy-mm') group by mx.xtph,mx.kfbm,mx.ypbm,mx.scph,mx.ypjj,mx.yplj,mx.pdzcsl,zl.jcbl)
	</select>
	<select id="selectPdWlrYk" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultType="com.supx.csp.api.ykgl.kccx.pojo.Pd_YykModel">
		select round(sum(-1*mx.pdzcsl*mx.yplj),4) wpljyyk,round(sum(-1*(mx.yplj/zl.jcbl)*pdzcsl), 4) wpjjyyk from ykb_ypkc mx inner join ykb_ypzd zd on  zd.ypbm = mx.ypbm and zd.yljgbm = mx.yljgbm
																															  left join ykb_ypzl zl on zl.ypzlbm = zd.zlbm and zl.yljgbm = zd.yljgbm
		where mx.kfbm=#{kfbm}  and mx.yljgbm=#{yljgbm} and lrbz=0
	</select>
	<update id="updateLrbz" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		update  ykb_ypkc set pdrq=sysdate,lrbz=1 where
			xtph = #{xtph,jdbcType=VARCHAR}
												   and kfbm = #{kfbm,jdbcType=VARCHAR}
												   and scph=#{scph,jdbcType=VARCHAR}
												   and ypbm=#{ypbm,jdbcType=VARCHAR} and yljgbm=#{yljgbm,jdbcType=VARCHAR}
	</update>
	<select id="selectYpTotal" resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		select sum(pdsjkc) kcsl,ypbm,kfbm,scph,xtph,yxqz,yplj,ypjj,cdbm,ghdw,kfdw,yfdw,fzbl,scrq,
			   yljgbm from pdb_ykpdmx where kfbm=#{kfbm} and ifpdlr=#{ifpdlr} and yljgbm=#{yljgbm} and to_char(pdrq,'yyyy-mm')=to_char(sysdate,'yyyy-mm')
		group by  ypbm,kfbm,xtph,scph,scrq,yxqz,yplj,ypjj,cdbm,ghdw,kfdw,yfdw,fzbl,
				  yljgbm
	</select>

	<update id="updateMxPdSh">
		update pdb_ykpdmx set pdshrq=sysdate,pdshrybm=#{pdshrybm} where
			kfbm = #{kfbm,jdbcType=VARCHAR}
																	and yljgbm=#{yljgbm,jdbcType=VARCHAR} and pdczy is not null and pdshrybm is null
	</update>
	<select id="getTj" resultType="com.supx.csp.api.ykgl.kfyw.pojo.Ykb_tjdmxModel" parameterType="com.supx.csp.api.ykgl.kfyw.pojo.Ykb_tjdmxModel">
		select  ypbm,xtph,scph,tjsl,tjsl_yf,yjj,xjj from ykb_yptj tj
		inner join ykb_yptjmx tjmx on tj.tjdjh = tjmx.tjdjh
		where to_char(tj.tzsjval,'yyyy-mm-dd')&gt;=to_char(#{beginrq,jdbcType=TIMESTAMP},'yyyy-mm-dd')
		and to_char(tj.tzsjval,'yyyy-mm-dd')&lt;=to_char(#{endrq,jdbcType=TIMESTAMP},'yyyy-mm-dd') and tj.qrzfbz='1'
		and tj.kfbm=#{kfbm,jdbcType=VARCHAR}
		<choose>
			<when test="yfbm!=null">
				and yfbm =#{yfbm,jdbcType=VARCHAR}
			</when>
			<otherwise>
				and yfbm is null
			</otherwise>
		</choose>
	</select>
	<insert id="insertPdCsh" useGeneratedKeys="false">
		INSERT ALL
		<foreach collection="list" item="item" index="index">
			INTO pdb_ykpdmx(
			ypbm,kfbm,xtph,scph,scrq,yxqz,yplj,ypjj,cdbm,ghdw,kfdw,yfdw,fzbl,
			cpbzh,pzwh,yljgbm,pdshrybm,pdyk,pdzcsl,pdshrq,pdsjkc
			)
			VALUES
			(#{item.ypbm,jdbcType=VARCHAR},#{item.kfbm,jdbcType=VARCHAR},#{item.xtph,jdbcType=VARCHAR},
			#{item.scph,jdbcType=VARCHAR},#{item.scrq,jdbcType=DATE},#{item.yxqz,jdbcType=DATE},#{item.yplj,jdbcType=DECIMAL},#{item.ypjj,jdbcType=DECIMAL},#{item.cdbm,jdbcType=VARCHAR},#{item.ghdw,jdbcType=VARCHAR},
			#{item.kfdw,jdbcType=VARCHAR},#{item.yfdw,jdbcType=VARCHAR},#{item.fzbl,jdbcType=DECIMAL},
			#{item.cpbzh,jdbcType=VARCHAR},#{item.pzwh,jdbcType=VARCHAR},#{item.yljgbm,jdbcType=VARCHAR},#{item.pdshrybm,jdbcType=VARCHAR},#{item.pdyk,jdbcType=DECIMAL},#{item.pdzcsl,jdbcType=DECIMAL},sysdate,#{item.pdsjkc,jdbcType=DECIMAL}
			)
		</foreach>
		SELECT 1 FROM DUAL
	</insert>
	<!-- // 盘点库存查询按药品汇总查询-->
	<select id="queryPdLr" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		select kc.xtph,kc.kfbm,kc.yxqz,kc.YPBM ,zd.ypmc ,zd.ypgg,zl.ypzlbm,zl.ypzlmc,kc.yfdw,jldw.jldwmc
		YFDWmc,gh.dwmc ghdwmc,kc.scrq,kc.pdshrybm,zl.jcbl,kc.ghdw,kc.id,kc.ifpdlr,KC.cpbzh,kc.pzwh,
		kc.YPJJ,kc.YPLJ,kc.scph,kc.cdbm,cd.cdmc,kc.FZBL,kc.pdsjkc pdsjkc,kc.pdzcsl
		FROM pdb_ykpdmx KC
		INNER JOIN YKB_YPZD ZD ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM and zd.yljgbm = kc.yljgbm
		inner join ykb_ypjx jx on zd.jxbm = jx.jxbm and zd.yljgbm = jx.yljgbm
		inner join ykb_ypgx gx on zd.gxbm = gx.gxbm and zd.yljgbm = gx.yljgbm
		inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
		left join ykb_cdbm cd on KC.cdbm = cd.cdbm and KC.yljgbm = cd.yljgbm
		inner join ykb_jldwbm kfdw on zd.kfdw = kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
		inner join ykb_jldwbm yfdw on zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
		inner join ykb_yptclb ybtc on zd.ybtclb = ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
		inner join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm
		left join Ykb_Ghdw gh on KC.Ghdw= gh.dwbm and KC.yljgbm=gh.yljgbm
		where kc.pdrq is not null and KC.yljgbm=#{yljgbm} and to_char(kc.pdrq,'yyyy-mm')=to_char(sysdate,'yyyy-mm')
		<if test="kfbm != null">
			and kc.kfbm = #{kfbm,jdbcType=VARCHAR}
		</if>
		<if test="parm != null and parm != '' ">
			and (zd.YPBM like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or zd.YPMC like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or lower(zd.PYDM) like '%'||lower(#{parm,jdbcType=VARCHAR})||'%')
		</if>
		<if test="pdlc!=null and pdlc==1">
			and kc.pdczy=#{pdczy} and kc.pdshrybm is null
		</if>
		<if test="pdlc!=null and pdlc==2">
			and kc.pdczy is not null and kc.pdshrybm is null
		</if>
		<if test="pdlc!=null and pdlc==3">
			and kc.pdczy is not null and kc.pdshrybm is null
		</if>
		order by KC.pdrq
	</select>

	<insert id="insertBatchPdwc" parameterType="java.util.List" useGeneratedKeys="false">
		INSERT ALL
		<foreach collection="list" item="item" index="index">
			INTO ykb_ypkc(
			ypbm,kfbm,xtph,scph,scrq,yxqz,yplj,ypjj,cdbm,ghdw,kfdw,yfdw,fzbl,uptimestamp,
			yljgbm,kcsl
			)
			VALUES
			(#{item.ypbm,jdbcType=VARCHAR},#{item.kfbm,jdbcType=VARCHAR},#{item.xtph,jdbcType=VARCHAR},
			#{item.scph,jdbcType=VARCHAR},#{item.scrq,jdbcType=DATE},#{item.yxqz,jdbcType=DATE},
			#{item.yplj,jdbcType=DECIMAL},#{item.ypjj,jdbcType=DECIMAL},#{item.cdbm,jdbcType=VARCHAR},#{item.ghdw,jdbcType=VARCHAR},
			#{item.kfdw,jdbcType=VARCHAR},#{item.yfdw,jdbcType=VARCHAR},#{item.fzbl,jdbcType=DECIMAL},sysdate,
			#{item.yljgbm,jdbcType=VARCHAR},#{item.kcsl,jdbcType=DECIMAL}
			)
		</foreach>
		SELECT 1 FROM DUAL
	</insert>
	<update id="updateBatch">
		<foreach collection="list" item="item" index="index" separator=";" open="begin" close=";end;">
			update ykb_ypkc set kcsl = #{item.kcsl}
			where
			ypbm = #{item.ypbm,jdbcType=VARCHAR} and kfbm = #{item.kfbm,jdbcType=VARCHAR} and scph = #{item.scph,jdbcType=VARCHAR} and xtph=#{item.xtph,jdbcType=VARCHAR}
		</foreach>
	</update>
	<update id="updatePdmxYpKc" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		update pdb_ykpdmx set pdrq=sysdate
		<if test="pdsjkc!=null">
			,pdsjkc=#{pdsjkc}
		</if>
		<if test="pdyk!=null">
			,pdyk=#{pdyk}
		</if>
		<if test="pdczy!=null">
			,pdczy=#{pdczy}
		</if>
		<if test="pdczymc!=null">
			,pdczymc=#{pdczymc}
		</if>
		<if test="scph != null">
			,scph=#{scph}
		</if>
		<if test="scrq != null">
			,scrq=#{scrq}
		</if>
		<if test="yxqz != null">
			,yxqz=#{yxqz}
		</if>
		<if test="yplj != null">
			,yplj=#{yplj}
		</if>
		<if test="ypjj != null">
			,ypjj=#{ypjj}
		</if>
		<if test="cdbm != null">
			,cdbm=#{cdbm}
		</if>
		<if test="ghdw != null">
			,ghdw=#{ghdw}
		</if>
		<if test="pzwh != null">
			,pzwh=#{pzwh}
		</if>
		<if test="cpbzh != null">
			,cpbzh=#{cpbzh}
		</if>
		where
		xtph = #{xtph,jdbcType=VARCHAR} and id=#{id}
		and kfbm = #{kfbm,jdbcType=VARCHAR}
		and ypbm=#{ypbm,jdbcType=VARCHAR} and yljgbm=#{yljgbm,jdbcType=VARCHAR} and to_char(pdrq,'yyyy-mm')=to_char(sysdate,'yyyy-mm')
	</update>
	<!-- // 盘点库存查询按药品汇总查询-->
	<select id="queryPdlbYwc" parameterType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel"
			resultType="com.supx.csp.api.ykgl.kccx.pojo.Ykb_kccxModel">
		select kc.xtph,kc.kfbm,kc.yxqz,kc.YPBM ,zd.ypmc ,zd.ypgg,zl.ypzlbm,zl.ypzlmc,kc.yfdw,jldw.jldwmc
		YFDWmc,gh.dwmc ghdwmc,kc.scrq,kc.pdshrybm,zl.jcbl,kc.ghdw,
		kc.YPJJ,kc.YPLJ,kc.scph,kc.cdbm,cd.cdmc,kc.FZBL,sum(kc.pdsjkc) pdsjkc,(sum(kc.pdsjkc)-kc.pdzcsl) pdyk,kc.pdzcsl
		FROM pdb_ykpdmx KC
		INNER JOIN YKB_YPZD ZD ON ZD.YPBM = KC.YPBM AND ZD.KFBM = KC.KFBM and zd.yljgbm = kc.yljgbm
		inner join ykb_ypjx jx on zd.jxbm = jx.jxbm and zd.yljgbm = jx.yljgbm
		inner join ykb_ypgx gx on zd.gxbm = gx.gxbm and zd.yljgbm = gx.yljgbm
		inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
		left join ykb_cdbm cd on KC.cdbm = cd.cdbm and KC.yljgbm = cd.yljgbm
		inner join ykb_jldwbm kfdw on zd.kfdw = kfdw.jldwbm and zd.yljgbm = kfdw.yljgbm
		inner join ykb_jldwbm yfdw on zd.yfdw = yfdw.jldwbm and zd.yljgbm = yfdw.yljgbm
		inner join ykb_yptclb ybtc on zd.ybtclb = ybtc.tclbbm and zd.yljgbm = ybtc.yljgbm
		inner join ykb_jldwbm jldw on zd.jldw = jldw.jldwbm and zd.yljgbm = jldw.yljgbm
		left join Ykb_Ghdw gh on KC.Ghdw= gh.dwbm and KC.yljgbm=gh.yljgbm
		where kc.pdrq is not null and to_char(kc.pdrq,'yyyy-mm')=to_char(sysdate,'yyyy-mm')
		<if test="kfbm != null">
			and kc.kfbm = #{kfbm,jdbcType=VARCHAR}
		</if>
		<if test="parm != null and parm != '' ">
			and (zd.YPBM like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or zd.YPMC like '%'||#{parm,jdbcType=VARCHAR}||'%'
			or lower(zd.PYDM) like '%'||lower(#{parm,jdbcType=VARCHAR})||'%')
		</if>
		<if test="pdlc!=null and pdlc==1">
			and kc.pdczy=#{pdczy}
		</if>
		<if test="pdlc!=null and pdlc==2">
			and kc.pdczy is not null
		</if>
		<if test="pdlc!=null and pdlc==3">
			and kc.pdczy is not null and kc.pdshrybm is null
		</if>
		group by kc.xtph,kc.kfbm,kc.yxqz,kc.YPBM ,zd.ypmc ,zd.ypgg,zl.ypzlbm,zl.ypzlmc,kc.yfdw,jldw.jldwmc
		,gh.dwmc,kc.scrq,kc.pdshrybm,zl.jcbl,kc.ghdw,
		kc.YPJJ,kc.YPLJ,kc.scph,kc.cdbm,cd.cdmc,kc.FZBL,kc.pdzcsl
	</select>

</mapper>
