<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.lis.wjzcl.dao.Lis_wjzclModelMapper" >
	<resultMap id="BaseResultMap" type="com.supx.csp.api.lis.wjzcl.pojo.Lis_wjzclModel" >
	    <result column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
	    <result column="YSBM" property="bbbh" jdbcType="VARCHAR" />
	    <result column="YSMC" property="brxm" jdbcType="VARCHAR" />
	    <result column="PYDM" property="nl" jdbcType="VARCHAR" />
	    <result column="STOP" property="nldw" jdbcType="VARCHAR" />
	    <result column="XKBZ" property="xb" jdbcType="VARCHAR" />
	    <result column="YSMC" property="jyxm" jdbcType="VARCHAR" />
	    <result column="PYDM" property="jyxmmc" jdbcType="VARCHAR" />
	    <result column="STOP" property="jg" jdbcType="VARCHAR" />
	    <result column="XKBZ" property="pdbz" jdbcType="VARCHAR" />
	    <result column="YSMC" property="jlrq" jdbcType="TIMESTAMP" />
	    <result column="PYDM" property="lx" jdbcType="VARCHAR" />
	    <result column="STOP" property="bah" jdbcType="VARCHAR" />
	    <result column="XKBZ" property="ksmc" jdbcType="VARCHAR" />
	    <result column="YSMC" property="clfs" jdbcType="VARCHAR" />
	    <result column="PYDM" property="clr" jdbcType="VARCHAR" />
	    <result column="STOP" property="clsj" jdbcType="TIMESTAMP" />
	    <result column="XKBZ" property="sqys" jdbcType="VARCHAR" />
	    <result column="XKBZ" property="sqysxm" jdbcType="VARCHAR" />
	    <result column="XKBZ" property="cwh" jdbcType="VARCHAR" />
	    <result column="XKBZ" property="bz" jdbcType="VARCHAR" />
	    <result column="XKBZ" property="clbz" jdbcType="VARCHAR" />
	    <result column="XKBZ" property="jyxh" jdbcType="VARCHAR" />
  	</resultMap>

	<select id="queryWjz" parameterType="com.supx.csp.api.lis.wjzcl.pojo.Lis_wjzclModel" resultMap="BaseResultMap">
		select
		  jy.bbbh,jy.brxm,jy.nl,jy.nldw,jy.xb,jy.jyxm,jy.jyxmmc,jy.lx,jy.bah,jy.ksmc,jy.sqys,jy.sqysxm,jy.cwh,
		  wjz.jg,wjz.pdbz,wjz.jlrq,wjz.clfs,wjz.clr,wjz.clsj,wjz.bz,wjz.clbz,wjz.jyxh
		from lis_jydj jy
		  inner join lis_wjz_list wjz
		on (jy.jyxh = wjz.jyxh and jy.yljgbm = wjz.yljgbm)
		where jy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		<if test="brxm != null and brxm != '' " >
		and BRXM like #{brxm,jdbcType=VARCHAR}'%'
		</if>
		<if test="jyxmmc != null and jyxmmc != '' " >
		and JYXMMC like #{jyxmmc,jdbcType=VARCHAR}'%'
		</if>
		<if test="jyxh != null and jyxh != '' " >
		and JYXH like #{jyxh,jdbcType=VARCHAR}'%'
		</if>
		<if test="endrq != null  " >
		and JLRQ &lt; #{endrq,jdbcType=TIMESTAMP}'%'
		</if>
		<if test="clbz != null and clbz != '' " >
		and CLBZ = #{clbz,jdbcType=VARCHAR}
		</if>
		<if test="beginrq != null  " >
		and JLRQ &gt; #{beginrq,jdbcType=TIMESTAMP}
		</if>
	</select>

	<update id="updateWjz" parameterType="com.supx.csp.api.lis.wjzcl.pojo.Lis_wjzclModel">
		update lis_wjz_list
		set
			<if test="bz != null and bz !=''" >
	       	 	BZ = #{bz,jdbcType=VARCHAR},
	      	</if>
	      	<if test="clfs != null and clfs !=''" >
	       	 	CLFS = #{clfs,jdbcType=VARCHAR},
	      	</if>
			CLBZ = '1',CLR = #{clr,jdbcType=VARCHAR},
			CLSJ = #{clsj,jdbcType=VARCHAR}
		where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
		and JYXH = #{jyxm,jdbcType=VARCHAR}
	</update>

	<delete id="delete" parameterType="com.supx.csp.api.lis.wjzcl.pojo.Lis_wjzclModel">
		delete from lis_sjys
	    where YLJGBM = #{yljgbm,jdbcType=VARCHAR} and JYXH = #{jyxm,jdbcType=VARCHAR}
	</delete>

	<insert id="save" parameterType="com.supx.csp.api.yzpacs.pojo.OldLisWjzShowModel">
		MERGE INTO LIS_WJZ_LIST T1
		USING
		(
			SELECT
			<if test="jyxh != null and jyxh != ''.toString()">
				#{jyxh} JYXH,
			</if>
			<if test="zbxm != null and zbxm != ''.toString()">
				#{zbxm} ZBXM,
			</if>
			<if test="jg != null and jg != ''.toString()">
				#{jg} JG,
			</if>
			<if test="pdbz != null and pdbz != ''.toString()">
				#{pdbz} PDBZ,
			</if>
			<if test="jlrq != null">
				#{jlrq} JLRQ,
			</if>
			<if test="bz != null and bz != ''.toString()">
				#{bz} BZ,
			</if>
			<if test="xsbz != null and xsbz != ''.toString()">
				#{xsbz} XSBZ,
			</if>
			<if test="clfs != null and clfs != ''.toString()">
				#{clfs} CLFS,
			</if>
			<if test="clr != null and clr != ''.toString()">
				#{clr} CLR,
			</if>
			<if test="clsj != null">
				#{clsj} CLSJ,
			</if>
			<if test="qrbz != null and qrbz != ''.toString()">
				#{qrbz} QRBZ,
			</if>
			<if test="qrr != null and qrr != ''.toString()">
				#{qrr} QRR,
			</if>
			<if test="qrsj != null">
				#{qrsj} QRSJ,
			</if>
			<if test="qrlr != null and qrlr != ''.toString()">
				#{qrlr} QRLR,
			</if>
			<if test="clbz != null and clbz != ''.toString()">
				#{clbz} CLBZ,
			</if>
			<if test="ydbz != null and ydbz != ''.toString()">
				#{ydbz} YDBZ,
			</if>
			<if test="ysqr != null and ysqr != ''.toString()">
				#{ysqr} YSQR,
			</if>
			<if test="ysqrr != null and ysqrr != ''.toString()">
				#{ysqrr} YSQRR,
			</if>
			<if test="ysqrrq != null">
				#{ysqrrq} YSQRRQ,
			</if>
			<if test="ysqrnr != null and ysqrnr != ''.toString()">
				#{ysqrnr} YSQRNR,
			</if>
			<if test="jbgys != null and jbgys != ''.toString()">
				#{jbgys} JBGYS,
			</if>
			<if test="jbgrq != null">
				#{jbgrq} JBGRQ,
			</if>
				#{yljgbm} YLJGBM
		FROM DUAL
		) T2
		ON (T1.JYXH = T2.JYXH AND T1.ZBXM = T2.ZBXM AND T1.YLJGBM = T2.YLJGBM)
		WHEN MATCHED THEN
		UPDATE
		<set>
			<if test="jg != null and jg != ''.toString()">
				T1.JG = T2.JG,
			</if>
			<if test="pdbz != null and pdbz != ''.toString()">
				T1.PDBZ = T2.PDBZ,
			</if>
			<if test="jlrq != null">
				T1.JLRQ = T2.JLRQ,
			</if>
			<if test="bz != null and bz != ''.toString()">
				T1.BZ = T2.BZ,
			</if>
			<if test="xsbz != null and xsbz != ''.toString()">
				T1.XSBZ = T2.XSBZ,
			</if>
			<if test="clfs != null and clfs != ''.toString()">
				T1.CLFS = T2.CLFS,
			</if>
			<if test="clr != null and clr != ''.toString()">
				T1.CLR = T2.CLR,
			</if>
			<if test="clsj != null">
				T1.CLSJ = T2.CLSJ,
			</if>
			<if test="qrbz != null and qrbz != ''.toString()">
				T1.QRBZ = T2.QRBZ,
			</if>
			<if test="qrr != null and qrr != ''.toString()">
				T1.QRR = T2.QRR,
			</if>
			<if test="qrsj != null">
				T1.QRSJ = T2.QRSJ,
			</if>
			<if test="qrlr != null and qrlr != ''.toString()">
				T1.QRLR = T2.QRLR,
			</if>
			<if test="clbz != null and clbz != ''.toString()">
				T1.CLBZ = T2.CLBZ,
			</if>
			<if test="ydbz != null and ydbz != ''.toString()">
				T1.YDBZ = T2.YDBZ,
			</if>
			<if test="ysqr != null and ysqr != ''.toString()">
				T1.YSQR = T2.YSQR,
			</if>
			<if test="ysqrr != null and ysqrr != ''.toString()">
				T1.YSQRR = T2.YSQRR,
			</if>
			<if test="ysqrrq != null">
				T1.YSQRRQ = T2.YSQRRQ,
			</if>
			<if test="ysqrnr != null and ysqrnr != ''.toString()">
				T1.YSQRNR = T2.YSQRNR,
			</if>
			<if test="jbgys != null and jbgys != ''.toString()">
				T1.JBGYS = T2.JBGYS,
			</if>
			<if test="jbgrq != null">
				T1.JBGRQ = T2.JBGRQ,
			</if>
		</set>
		WHEN NOT MATCHED THEN
		insert (
		<if test="jyxh != null and jyxh != ''.toString()">
			JYXH,
		</if>
		<if test="zbxm != null and zbxm != ''.toString()">
			ZBXM,
		</if>
		<if test="jg != null and jg != ''.toString()">
			JG,
		</if>
		<if test="pdbz != null and pdbz != ''.toString()">
			PDBZ,
		</if>
		<if test="jlrq != null">
			JLRQ,
		</if>
		<if test="bz != null and bz != ''.toString()">
			BZ,
		</if>
		<if test="xsbz != null and xsbz != ''.toString()">
			XSBZ,
		</if>
		<if test="clfs != null and clfs != ''.toString()">
			CLFS,
		</if>
		<if test="clr != null and clr != ''.toString()">
			CLR,
		</if>
		<if test="clsj != null">
			CLSJ,
		</if>
		<if test="qrbz != null and qrbz != ''.toString()">
			QRBZ,
		</if>
		<if test="qrr != null and qrr != ''.toString()">
			QRR,
		</if>
		<if test="qrsj != null">
			QRSJ,
		</if>
		<if test="qrlr != null and qrlr != ''.toString()">
			QRLR,
		</if>
		<if test="clbz != null and clbz != ''.toString()">
			CLBZ,
		</if>
		<if test="ydbz != null and ydbz != ''.toString()">
			YDBZ,
		</if>
		<if test="ysqr != null and ysqr != ''.toString()">
			YSQR,
		</if>
		<if test="ysqrr != null and ysqrr != ''.toString()">
			YSQRR,
		</if>
		<if test="ysqrrq != null">
			YSQRRQ,
		</if>
		<if test="ysqrnr != null and ysqrnr != ''.toString()">
			YSQRNR,
		</if>
		<if test="jbgys != null and jbgys != ''.toString()">
			JBGYS,
		</if>
		<if test="jbgrq != null">
			JBGRQ,
		</if>
		YLJGBM
		)
		values (
		<if test="jyxh != null and jyxh != ''.toString()">
			T2.JYXH,
		</if>
		<if test="zbxm != null and zbxm != ''.toString()">
			T2.ZBXM,
		</if>
		<if test="jg != null and jg != ''.toString()">
			T2.JG,
		</if>
		<if test="pdbz != null and pdbz != ''.toString()">
			T2.PDBZ,
		</if>
		<if test="jlrq != null">
			T2.JLRQ,
		</if>
		<if test="bz != null and bz != ''.toString()">
			T2.BZ,
		</if>
		<if test="xsbz != null and xsbz != ''.toString()">
			T2.XSBZ,
		</if>
		<if test="clfs != null and clfs != ''.toString()">
			T2.CLFS,
		</if>
		<if test="clr != null and clr != ''.toString()">
			T2.CLR,
		</if>
		<if test="clsj != null">
			T2.CLSJ,
		</if>
		<if test="qrbz != null and qrbz != ''.toString()">
			T2.QRBZ,
		</if>
		<if test="qrr != null and qrr != ''.toString()">
			T2.QRR,
		</if>
		<if test="qrsj != null">
			T2.QRSJ,
		</if>
		<if test="qrlr != null and qrlr != ''.toString()">
			T2.QRLR,
		</if>
		<if test="clbz != null and clbz != ''.toString()">
			T2.CLBZ,
		</if>
		<if test="ydbz != null and ydbz != ''.toString()">
			T2.YDBZ,
		</if>
		<if test="ysqr != null and ysqr != ''.toString()">
			T2.YSQR,
		</if>
		<if test="ysqrr != null and ysqrr != ''.toString()">
			T2.YSQRR,
		</if>
		<if test="ysqrrq != null">
			T2.YSQRRQ,
		</if>
		<if test="ysqrnr != null and ysqrnr != ''.toString()">
			T2.YSQRNR,
		</if>
		<if test="jbgys != null and jbgys != ''.toString()">
			T2.JBGYS,
		</if>
		<if test="jbgrq != null">
			T2.JBGRQ,
		</if>
			T2.YLJGBM
		)
	</insert>
</mapper>
