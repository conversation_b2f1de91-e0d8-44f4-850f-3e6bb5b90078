<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.lis.xtwh.dao.LisTjdwModelMapper">
	<resultMap id="BaseResultMap" type="com.supx.csp.api.lis.xtwh.pojo.LisTjdwModel">
		<id column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
		<id column="DWBM" property="dwbm" jdbcType="VARCHAR" />
		<result column="DWMC" property="dwmc" jdbcType="VARCHAR" />
		<result column="DWDM" property="dwdm" jdbcType="VARCHAR" />
		<result column="TYBZ" property="tybz" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List">
		YLJGBM, DWBM, DWMC, DWDM, TYBZ
	</sql>
	<!--查询全部 -->
	<select id="selectTj" resultMap="BaseResultMap"
		parameterType="com.supx.csp.api.lis.xtwh.pojo.LisTjdwModel">
		select
		<include refid="Base_Column_List" />
		from LIS_TJDW
		where YLJGBM = ${yljgbm,jdbcType=VARCHAR}

	</select>
	<!--查询单个 -->
	<select id="selectTjByOne" resultMap="BaseResultMap"
		parameterType="com.supx.csp.api.lis.xtwh.pojo.LisTjdwModel">
		select
		<include refid="Base_Column_List" />
		from LIS_TJDW
		where YLJGBM = ${yljgbm,jdbcType=VARCHAR}
		<if test="dwbm!=null">
			and dwbm=#{dwbm}
		</if>

	</select>


	<insert id="insertTj" parameterType="com.supx.csp.api.lis.xtwh.pojo.LisTjdwModel">
		insert into LIS_TJDW
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="yljgbm != null">
				YLJGBM,
			</if>
			<if test="dwbm != null">
				DWBM,
			</if>
			<if test="dwmc != null">
				DWMC,
			</if>
			<if test="dwdm != null">
				DWDM,
			</if>
			<if test="tybz != null">
				TYBZ,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="yljgbm != null">
				#{yljgbm,jdbcType=VARCHAR},
			</if>
			<if test="dwbm != null">
				#{dwbm,jdbcType=VARCHAR},
			</if>
			<if test="dwmc != null">
				#{dwmc,jdbcType=VARCHAR},
			</if>
			<if test="dwdm != null">
				#{dwdm,jdbcType=VARCHAR},
			</if>
			<if test="tybz != null">
				#{tybz,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateTj" parameterType="com.supx.csp.api.lis.xtwh.pojo.LisTjdwModel">
		update LIS_TJDW
		<set>
			<if test="dwmc != null">
				DWMC = #{dwmc,jdbcType=VARCHAR},
			</if>
			<if test="dwdm != null">
				DWDM = #{dwdm,jdbcType=VARCHAR},
			</if>
			<if test="tybz != null">
				TYBZ = #{tybz,jdbcType=VARCHAR},
			</if>
		</set>
		where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
		and DWBM =
		#{dwbm,jdbcType=VARCHAR}
	</update>
	<update id="deleteTj" parameterType="java.util.Map">
		update LIS_JYFZ
		set tybz ='1' where yljgbm=${yljgbm} and
		dwbm in
		<foreach item="Model" index="index" collection="list" open="("
			separator="," close=")">
			#{Model.dwbm}
		</foreach>
	</update>

</mapper>
