<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- 医嘱处理 -->
<mapper namespace="com.supx.csp.zyys.ysyw.dao.New1Zyys_YzclModelMapper">


<!-- 药品医嘱新增 -->
<insert id="InsertYpyz" parameterType="java.util.List">
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
  insert into ZYYS_YPYZ
    <trim prefix="(" suffix=")" suffixOverrides=",">
      pybarcode,
      <if test="item.ypyzxh != null">
        YPYZXH,
      </if>
      <if test="item.mxxh != null">
        MXXH,
      </if>
      <if test="item.xhid != null">
        XHID,
      </if>
      <if test="item.zyh != null">
        ZYH,
      </if>
      <if test="item.ksbm != null">
        KSBM,
      </if>
      <if test="item.yzlx != null">
        YZLX,
      </if>
      <if test="item.ksmc != null">
        KSMC,
      </if>
      <if test="item.yzfl != null">
        YZFL,
      </if>
      <if test="item.yebh != null">
        YEBH,
      </if>
       <if test="item.xseyzbz != null">
        XSEYZBZ,
      </if>
      <if test="item.ksrq != null">
        KSRQ,
      </if>
      <if test="item.xdys != null">
        XDYS,
      </if>
      <if test="item.xdysxm != null">
        XDYSXM,
      </if>
      <if test="item.ryypbm != null">
        RYYPBM,
      </if>
      <if test="item.ryypmc != null">
        RYYPMC,
      </if>
      <if test="item.gllb != null and item.gllb != ''">
        GLLB,
      </if>
      <if test="item.tsyz != null">
        TSYZ,
      </if>
      <if test="item.xssx != null">
        XSSX,
      </if>
      <if test="item.fzh != null">
        FZH,
      </if>
      <if test="item.dcjl != null">
        DCJL,
      </if>
      <if test="item.jldw != null">
        JLDW,
      </if>
      <if test="item.jldwmc != null">
        JLDWMC,
      </if>
      <if test="item.yyts != null">
        YYTS,
      </if>
      <if test="item.dj != null">
        DJ,
      </if>
      <if test="item.yyzl != null">
        YYZL,
      </if>
      <if test="item.sysd != null">
        SYSD,
      </if>
      <if test="item.sysddw != null">
        SYSDDW,
      </if>
      <if test="item.yysm != null">
        YYSM,
      </if>
      <if test="item.sfjj != null">
        SFJJ,
      </if>
      <if test="item.ysqm != null">
        YSQM,
      </if>
      <if test="item.ysqmxm != null">
        YSQMXM,
      </if>
      <if test="item.ysqmks != null">
        YSQMKS,
      </if>
      <if test="item.ysqmksmc != null">
        YSQMKSMC,
      </if>
      <if test="item.ysqmsj != null">
        YSQMSJ,
      </if>
      <if test="item.ysecqm != null">
        YSECQM,
      </if>
      <if test="item.ysecqmxm != null">
        YSECQMXM,
      </if>
      <if test="item.ysecqmks != null">
        YSECQMKS,
      </if>
      <if test="item.ysecqmksmc != null">
        YSECQMKSMC,
      </if>
      <if test="item.ysecqmsj != null">
        YSECQMSJ,
      </if>
      <if test="item.hsecqm != null">
        HSECQM,
      </if>
      <if test="item.hsecqmxm != null">
        HSECQMXM,
      </if>
      <if test="item.hsecqmsj != null">
        HSECQMSJ,
      </if>
      <if test="item.shbz != null">
        SHBZ,
      </if>
      <if test="item.shhs != null">
        SHHS,
      </if>
      <if test="item.shhsxm != null">
        SHHSXM,
      </if>
      <if test="item.shsj != null">
        SHSJ,
      </if>
      <if test="item.zxbz != null">
        ZXBZ,
      </if>
      <if test="item.zxhs != null">
        ZXHS,
      </if>
      <if test="item.zxhsxm != null">
        ZXHSXM,
      </if>
      <if test="item.zxks != null">
        ZXKS,
      </if>
      <if test="item.zxksmc != null">
        ZXKSMC,
      </if>
      <if test="item.zxsj != null">
        ZXSJ,
      </if>
      <if test="item.ystzbz != null">
        YSTZBZ,
      </if>
      <if test="item.tzys != null">
        TZYS,
      </if>
      <if test="item.tzysxm != null">
        TZYSXM,
      </if>
      <if test="item.ystzsj != null">
        YSTZSJ,
      </if>
      <if test="item.ystzsm != null">
        YSTZSM,
      </if>
      <if test="item.hstzbz != null">
        HSTZBZ,
      </if>
      <if test="item.tzhs != null">
        TZHS,
      </if>
      <if test="item.tzhsxm != null">
        TZHSXM,
      </if>
      <if test="item.hstzsj != null">
        HSTZSJ,
      </if>
      <if test="item.yzzcbz != null">
        YZZCBZ,
      </if>
      <if test="item.yzzcys != null">
        YZZCYS,
      </if>
      <if test="item.yzzcysxm != null">
        YZZCYSXM,
      </if>
      <if test="item.yzzcsj != null">
        YZZCSJ,
      </if>
      <if test="item.kjywsymd != null">
        KJYWSYMD,
      </if>
      <if test="item.bzsm != null">
        BZSM,
      </if>
      <if test="item.ypzl != null">
        YPZL,
      </if>
      <if test="item.sqsykss != null">
        SQSYKSS,
      </if>
      <if test="item.sqsysj != null">
        SQSYSJ,
      </if>
      <if test="item.sqsyqz != null">
        SQSYQZ,
      </if>
      <if test="item.sqysxm != null">
        SQYSXM,
      </if>
      <if test="item.yssm != null">
        YSSM,
      </if>
      <if test="item.zfbz != null">
        ZFBZ,
      </if>
      <if test="item.zfys != null">
        ZFYS,
      </if>
      <if test="item.zfysxm != null">
        ZFYSXM,
      </if>
      <if test="item.zfsj != null">
        ZFSJ,
      </if>
      <if test="item.zfyy != null">
        ZFYY,
      </if>
      <if test="item.ybtclb != null">
        YBTCLB,
      </if>
      <if test="item.ybtclbmc != null">
        YBTCLBMC,
      </if>
      <if test="item.nbtclb != null">
        NBTCLB,
      </if>
      <if test="item.ypgg != null">
        YPGG,
      </if>
      <if test="item.yyffbm != null">
        YYFFBM,
      </if>
      <if test="item.yyffmc != null">
        YYFFMC,
      </if>
      <if test="item.pcbm != null">
        PCBM,
      </if>
      <if test="item.pcmc != null">
        PCMC,
      </if>
      <if test="item.pccs != null">
        PCCS,
      </if>
      <if test="item.yfdw != null">
        YFDW,
      </if>
      <if test="item.yfdwmc != null">
        YFDWMC,
      </if>
      <if test="item.yfbm != null">
        YFBM,
      </if>
      <if test="item.yfmc != null">
        YFMC,
      </if>
      <if test="item.jxbm != null">
        JXBM,
      </if>
      <if test="item.yljgbm != null">
        YLJGBM,
      </if>
       <if test="item.sfcy != null">
        SFCY,
      </if>
        <if test="item.zyjs != null">
        zyjs,
      </if>
       <if test="item.zxkssj != null">
        zxkssj,
      </if>
      <if test="item.lcljJdbm != null">
        lclj_Jdbm,
      </if>
      <if test="item.lcljJdmc != null">
        lclj_Jdmc,
      </if>
      <if test="item.lcljYzxmbm != null">
        lclj_Yzxmbm,
      </if>
      <if test="item.lcljYzxmmc != null">
        lclj_Yzxmmc,
      </if>
       <if test="item.zyzd != null and item.zyzd!=''">
        zyzd,
      </if>
      <if test="item.zyzh != null and item.zyzh!=''">
        zyzh,
      </if>
      <if test="item.zyzf != null and item.zyzf!=''">
        zyzf,
      </if>
      <if test="item.ylyzxh != null and item.ylyzxh!=''">
        ylyzxh,
      </if>
      <if test="item.ylmxxh != null">
        ylmxxh,
      </if>
      <if test="item.zytt != null">
        zytt,
      </if>
      <if test="item.jysm != null">
        jysm,
      </if>
      <if test="item.srcs != null">
        srcs,
      </if>
      <if test="item.dbrxm != null">
        dbrxm,
      </if>
      <if test="item.dbrzjhm != null">
        dbrzjhm,
      </if>
      <if test="item.dbrlxdh != null">
        dbrlxdh,
      </if>
      <if test="item.tszdmc != null">
        tszdmc,
      </if>
      <if test="item.symd != null">
        SYMD,
      </if>
      <if test="item.ssmc != null">
        SSMC,
      </if>
      <if test="item.syqk != null">
        SYQK,
      </if>
      <if test="item.lhyyls != null">
        LHYYLS,
      </if>
      <if test="item.syyy != null">
        SYYY,
      </if>
      <if test="item.ssbm != null">
        SSBM,
      </if>
      <if test="item.yke186 != null">
        yke186,
      </if>
      <if test="item.yyzq != null">
        yyzq,
      </if>
      <if test="item.zysrcs != null">
        zysrcs,
      </if>
      <if test="item.yzuuid != null">
        yzuuid,
      </if>
      <if test="item.mtksrq != null">
        mtksrq,
      </if>
      <if test="item.mtjsrq != null">
        mtjsrq,
      </if>
      <if test="item.zts != null">
        zts,
      </if>
      <if test="item.kssjb != null">
        kssjb,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
       #{item.ypyzxh}||#{item.fzh},
      <if test="item.ypyzxh != null">
        #{item.ypyzxh,jdbcType=VARCHAR},
      </if>
      <if test="item.mxxh != null">
        #{item.mxxh,jdbcType=DECIMAL},
      </if>
       <if test="item.xhid != null">
        #{item.xhid,jdbcType=VARCHAR},
      </if>
      <if test="item.zyh != null">
        #{item.zyh,jdbcType=VARCHAR},
      </if>
      <if test="item.ksbm != null">
        #{item.ksbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzlx != null">
        #{item.yzlx,jdbcType=VARCHAR},
      </if>
      <if test="item.ksmc != null">
        #{item.ksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.yzfl != null">
        #{item.yzfl,jdbcType=VARCHAR},
      </if>
      <if test="item.yebh != null">
        #{item.yebh,jdbcType=VARCHAR},
      </if>
       <if test="item.xseyzbz != null">
        #{item.xseyzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.ksrq != null">
        #{item.ksrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.xdys != null">
        #{item.xdys,jdbcType=VARCHAR},
      </if>
      <if test="item.xdysxm != null">
        #{item.xdysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ryypbm != null">
        #{item.ryypbm,jdbcType=VARCHAR},
      </if>
      <if test="item.ryypmc != null">
        #{item.ryypmc,jdbcType=VARCHAR},
      </if>
      <if test="item.gllb != null and item.gllb != ''">
        #{item.gllb,jdbcType=VARCHAR},
      </if>
      <if test="item.tsyz != null">
        #{item.tsyz,jdbcType=VARCHAR},
      </if>
      <if test="item.xssx != null">
        #{item.xssx,jdbcType=DECIMAL},
      </if>
      <if test="item.fzh != null">
        #{item.fzh,jdbcType=DECIMAL},
      </if>
      <if test="item.dcjl != null">
        #{item.dcjl,jdbcType=DECIMAL},
      </if>
      <if test="item.jldw != null">
        #{item.jldw,jdbcType=VARCHAR},
      </if>
      <if test="item.jldwmc != null">
        #{item.jldwmc,jdbcType=VARCHAR},
      </if>
      <if test="item.yyts != null">
        #{item.yyts,jdbcType=DECIMAL},
      </if>
      <if test="item.dj != null">
        #{item.dj,jdbcType=DECIMAL},
      </if>
      <if test="item.yyzl != null">
        #{item.yyzl,jdbcType=DECIMAL},
      </if>
      <if test="item.sysd != null">
        #{item.sysd,jdbcType=DECIMAL},
      </if>
      <if test="item.sysddw != null">
        #{item.sysddw,jdbcType=VARCHAR},
      </if>
      <if test="item.yysm != null">
        #{item.yysm,jdbcType=VARCHAR},
      </if>
      <if test="item.sfjj != null">
        #{item.sfjj,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqm != null">
        #{item.ysqm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmxm != null">
        #{item.ysqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmks != null">
        #{item.ysqmks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmksmc != null">
        #{item.ysqmksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmsj != null">
        #{item.ysqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ysecqm != null">
        #{item.ysecqm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmxm != null">
        #{item.ysecqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmks != null">
        #{item.ysecqmks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmksmc != null">
        #{item.ysecqmksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmsj != null">
        #{item.ysecqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.hsecqm != null">
        #{item.hsecqm,jdbcType=VARCHAR},
      </if>
      <if test="item.hsecqmxm != null">
        #{item.hsecqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.hsecqmsj != null">
        #{item.hsecqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.shbz != null">
        #{item.shbz,jdbcType=VARCHAR},
      </if>
      <if test="item.shhs != null">
        #{item.shhs,jdbcType=VARCHAR},
      </if>
      <if test="item.shhsxm != null">
        #{item.shhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.shsj != null">
        #{item.shsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zxbz != null">
        #{item.zxbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zxhs != null">
        #{item.zxhs,jdbcType=VARCHAR},
      </if>
      <if test="item.zxhsxm != null">
        #{item.zxhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zxks != null">
        #{item.zxks,jdbcType=VARCHAR},
      </if>
      <if test="item.zxksmc != null">
        #{item.zxksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zxsj != null">
        #{item.zxsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ystzbz != null">
        #{item.ystzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.tzys != null">
        #{item.tzys,jdbcType=VARCHAR},
      </if>
      <if test="item.tzysxm != null">
        #{item.tzysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ystzsj != null">
        #{item.ystzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ystzsm != null">
        #{item.ystzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.hstzbz != null">
        #{item.hstzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.tzhs != null">
        #{item.tzhs,jdbcType=VARCHAR},
      </if>
      <if test="item.tzhsxm != null">
        #{item.tzhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.hstzsj != null">
        #{item.hstzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.yzzcbz != null">
        #{item.yzzcbz,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcys != null">
        #{item.yzzcys,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcysxm != null">
        #{item.yzzcysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcsj != null">
        #{item.yzzcsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.kjywsymd != null">
        #{item.kjywsymd,jdbcType=VARCHAR},
      </if>
      <if test="item.bzsm != null">
        #{item.bzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.ypzl != null">
        #{item.ypzl,jdbcType=VARCHAR},
      </if>
      <if test="item.sqsykss != null">
        #{item.sqsykss,jdbcType=VARCHAR},
      </if>
      <if test="item.sqsysj != null">
        #{item.sqsysj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.sqsyqz != null">
        #{item.sqsyqz,jdbcType=VARCHAR},
      </if>
      <if test="item.sqysxm != null">
        #{item.sqysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.yssm != null">
        #{item.yssm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfbz != null">
        #{item.zfbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfys != null">
        #{item.zfys,jdbcType=VARCHAR},
      </if>
      <if test="item.zfysxm != null">
        #{item.zfysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfsj != null">
        #{item.zfsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zfyy != null">
        #{item.zfyy,jdbcType=VARCHAR},
      </if>
      <if test="item.ybtclb != null">
        #{item.ybtclb,jdbcType=VARCHAR},
      </if>
      <if test="item.ybtclbmc != null">
        #{item.ybtclbmc,jdbcType=VARCHAR},
      </if>
      <if test="item.nbtclb != null">
        #{item.nbtclb,jdbcType=VARCHAR},
      </if>
      <if test="item.ypgg != null">
        #{item.ypgg,jdbcType=VARCHAR},
      </if>
      <if test="item.yyffbm != null">
        #{item.yyffbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yyffmc != null">
        #{item.yyffmc,jdbcType=VARCHAR},
      </if>
      <if test="item.pcbm != null">
        #{item.pcbm,jdbcType=VARCHAR},
      </if>
      <if test="item.pcmc != null">
        #{item.pcmc,jdbcType=VARCHAR},
      </if>
      <if test="item.pccs != null">
        #{item.pccs,jdbcType=VARCHAR},
      </if>
      <if test="item.yfdw != null">
        #{item.yfdw,jdbcType=VARCHAR},
      </if>
      <if test="item.yfdwmc != null">
        #{item.yfdwmc,jdbcType=VARCHAR},
      </if>
      <if test="item.yfbm != null">
        #{item.yfbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yfmc != null">
        #{item.yfmc,jdbcType=VARCHAR},
      </if>
      <if test="item.jxbm != null">
        #{item.jxbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yljgbm != null">
        #{item.yljgbm,jdbcType=VARCHAR},
      </if>
       <if test="item.sfcy != null">
        #{item.sfcy,jdbcType=VARCHAR},
      </if>
        <if test="item.zyjs != null">
        #{item.zyjs,jdbcType=DECIMAL},
      </if>
       <if test="item.zxkssj != null">
        #{item.zxkssj,jdbcType=DATE},
      </if>
      <if test="item.lcljJdbm != null">
        #{item.lcljJdbm,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljJdmc != null">
        #{item.lcljJdmc,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljYzxmbm != null">
        #{item.lcljYzxmbm,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljYzxmmc != null">
        #{item.lcljYzxmmc,jdbcType=VARCHAR},
      </if>
       <if test="item.zyzd != null and item.zyzd!=''">
        #{item.zyzd,jdbcType=VARCHAR},
      </if>
      <if test="item.zyzh != null and item.zyzh!=''">
        #{item.zyzh,jdbcType=VARCHAR},
      </if>
      <if test="item.zyzf != null and item.zyzf!=''">
        #{item.zyzf,jdbcType=VARCHAR},
      </if>
      <if test="item.ylyzxh != null and item.ylyzxh!=''">
        #{item.ylyzxh,jdbcType=VARCHAR},
      </if>
      <if test="item.ylmxxh != null">
        #{item.ylmxxh,jdbcType=DECIMAL},
      </if>
      <if test="item.zytt != null">
        #{item.zytt},
      </if>
      <if test="item.jysm != null">
        #{item.jysm},
      </if>
      <if test="item.srcs != null">
        #{item.srcs},
      </if>
      <if test="item.dbrxm != null">
        #{item.dbrxm},
      </if>
      <if test="item.dbrzjhm != null">
        #{item.dbrzjhm},
      </if>
      <if test="item.dbrlxdh != null">
        #{item.dbrlxdh},
      </if>
      <if test="item.tszdmc != null">
        #{item.tszdmc},
      </if>
      <if test="item.symd != null">
        #{item.symd,jdbcType=VARCHAR},
      </if>
      <if test="item.ssmc != null">
        #{item.ssmc,jdbcType=VARCHAR},
      </if>
      <if test="item.syqk != null">
        #{item.syqk,jdbcType=VARCHAR},
      </if>
      <if test="item.lhyyls != null">
        #{item.lhyyls,jdbcType=VARCHAR},
      </if>
      <if test="item.syyy != null">
        #{item.syyy,jdbcType=VARCHAR},
      </if>
      <if test="item.ssbm != null">
        #{item.ssbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yke186 != null">
        #{item.yke186,jdbcType=VARCHAR},
      </if>
      <if test="item.yyzq != null">
        #{item.yyzq},
      </if>
      <if test="item.zysrcs != null">
        #{item.zysrcs,jdbcType=VARCHAR},
      </if>
      <if test="item.yzuuid != null">
        #{item.yzuuid,jdbcType=VARCHAR},
      </if>
      <if test="item.mtksrq != null">
        #{item.mtksrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.mtjsrq != null">
        #{item.mtjsrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zts != null">
        #{item.zts,jdbcType=DECIMAL},
      </if>
      <if test="item.kssjb != null">
        #{item.kssjb,jdbcType=VARCHAR},
      </if>
    </trim>
  </foreach>
</insert>

<!-- 药品医嘱修改 -->
<update id="UpdateYpyz" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
  update ZYYS_YPYZ
    <set>
      <if test="item.yzlx != null">
        YZLX = #{item.yzlx,jdbcType=VARCHAR},
      </if>
      <if test="item.yzfl != null">
        YZFL = #{item.yzfl,jdbcType=VARCHAR},
      </if>
      <if test="item.yebh != null">
        YEBH = #{item.yebh,jdbcType=VARCHAR},
      </if>
       <if test="item.xseyzbz != null">
        XSEYZBZ = #{item.xseyzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.ksrq != null">
        KSRQ = #{item.ksrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.xdys != null">
        XDYS = #{item.xdys,jdbcType=VARCHAR},
      </if>
      <if test="item.xdysxm != null">
        XDYSXM = #{item.xdysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ryypbm != null">
        RYYPBM = #{item.ryypbm,jdbcType=VARCHAR},
      </if>
      <if test="item.ryypmc != null">
        RYYPMC = #{item.ryypmc,jdbcType=VARCHAR},
      </if>
      <if test="item.gllb != null">
        GLLB = #{item.gllb,jdbcType=VARCHAR},
      </if>
      <if test="item.tsyz != null">
        TSYZ = #{item.tsyz,jdbcType=VARCHAR},
      </if>
      <if test="item.xssx != null">
        XSSX = #{item.xssx,jdbcType=DECIMAL},
      </if>
      <if test="item.fzh != null">
        FZH = #{item.fzh,jdbcType=DECIMAL},
      </if>
      <if test="item.dcjl != null">
        DCJL = #{item.dcjl,jdbcType=DECIMAL},
      </if>
      <if test="item.jldw != null">
        JLDW = #{item.jldw,jdbcType=VARCHAR},
      </if>
      <if test="item.jldwmc != null">
        JLDWMC = #{item.jldwmc,jdbcType=VARCHAR},
      </if>
      <if test="item.yyts != null">
        YYTS = #{item.yyts,jdbcType=DECIMAL},
      </if>
      <if test="item.dj != null">
        DJ = #{item.dj,jdbcType=DECIMAL},
      </if>
      <if test="item.yyzl != null">
        YYZL = #{item.yyzl,jdbcType=DECIMAL},
      </if>
      <if test="item.sysd != null">
        SYSD = #{item.sysd,jdbcType=DECIMAL},
      </if>
      <if test="item.sysddw != null">
        SYSDDW = #{item.sysddw,jdbcType=VARCHAR},
      </if>
      <if test="item.yysm != null">
        YYSM = #{item.yysm,jdbcType=VARCHAR},
      </if>
      <if test="item.sfjj != null">
        SFJJ = #{item.sfjj,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqm != null">
        YSQM = #{item.ysqm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmxm != null">
        YSQMXM = #{item.ysqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmks != null">
        YSQMKS = #{item.ysqmks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmksmc != null">
        YSQMKSMC = #{item.ysqmksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmsj != null">
        YSQMSJ = #{item.ysqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ysecqm != null">
        YSECQM = #{item.ysecqm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmxm != null">
        YSECQMXM = #{item.ysecqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmks != null">
        YSECQMKS = #{item.ysecqmks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmksmc != null">
        YSECQMKSMC = #{item.ysecqmksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmsj != null">
        YSECQMSJ = #{item.ysecqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.hsecqm != null">
        HSECQM = #{item.hsecqm,jdbcType=VARCHAR},
      </if>
      <if test="item.hsecqmxm != null">
        HSECQMXM = #{item.hsecqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.hsecqmsj != null">
        HSECQMSJ = #{item.hsecqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.shbz != null">
        SHBZ = #{item.shbz,jdbcType=VARCHAR},
      </if>
      <if test="item.shhs != null">
        SHHS = #{item.shhs,jdbcType=VARCHAR},
      </if>
      <if test="item.shhsxm != null">
        SHHSXM = #{item.shhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.shsj != null">
        SHSJ = #{item.shsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zxbz != null">
        ZXBZ = #{item.zxbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zxhs != null">
        ZXHS = #{item.zxhs,jdbcType=VARCHAR},
      </if>
      <if test="item.zxhsxm != null">
        ZXHSXM = #{item.zxhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zxks != null">
        ZXKS = #{item.zxks,jdbcType=VARCHAR},
      </if>
      <if test="item.zxksmc != null">
        ZXKSMC = #{item.zxksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zxsj != null">
        ZXSJ = #{item.zxsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ystzbz != null">
        YSTZBZ = #{item.ystzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.tzys != null">
        TZYS = #{item.tzys,jdbcType=VARCHAR},
      </if>
      <if test="item.tzysxm != null">
        TZYSXM = #{item.tzysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ystzsj != null">
        YSTZSJ = #{item.ystzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ystzsm != null">
        YSTZSM = #{item.ystzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.hstzbz != null">
        HSTZBZ = #{item.hstzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.tzhs != null">
        TZHS = #{item.tzhs,jdbcType=VARCHAR},
      </if>
      <if test="item.tzhsxm != null">
        TZHSXM = #{item.tzhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.hstzsj != null">
        HSTZSJ = #{item.hstzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.yzzcbz != null">
        YZZCBZ = #{item.yzzcbz,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcys != null">
        YZZCYS = #{item.yzzcys,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcysxm != null">
        YZZCYSXM = #{item.yzzcysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcsj != null">
        YZZCSJ = #{item.yzzcsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.kjywsymd != null">
        KJYWSYMD = #{item.kjywsymd,jdbcType=VARCHAR},
      </if>
      <if test="item.bzsm != null">
        BZSM = #{item.bzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.ypzl != null">
        YPZL = #{item.ypzl,jdbcType=VARCHAR},
      </if>
      <if test="item.sqsykss != null">
        SQSYKSS = #{item.sqsykss,jdbcType=VARCHAR},
      </if>
      <if test="item.sqsysj != null">
        SQSYSJ = #{item.sqsysj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.sqsyqz != null">
        SQSYQZ = #{item.sqsyqz,jdbcType=VARCHAR},
      </if>
      <if test="item.sqysxm != null">
        SQYSXM = #{item.sqysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.yssm != null">
        YSSM = #{item.yssm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfbz != null">
        ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfys != null">
        ZFYS = #{item.zfys,jdbcType=VARCHAR},
      </if>
      <if test="item.zfysxm != null">
        ZFYSXM = #{item.zfysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfsj != null">
        ZFSJ = #{item.zfsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zfyy != null">
        ZFYY = #{item.zfyy,jdbcType=VARCHAR},
      </if>
      <if test="item.ybtclb != null">
        YBTCLB = #{item.ybtclb,jdbcType=VARCHAR},
      </if>
      <if test="item.ybtclbmc != null">
        YBTCLBMC = #{item.ybtclbmc,jdbcType=VARCHAR},
      </if>
      <if test="item.nbtclb != null">
        NBTCLB = #{item.nbtclb,jdbcType=VARCHAR},
      </if>
      <if test="item.ypgg != null">
        YPGG = #{item.ypgg,jdbcType=VARCHAR},
      </if>
      <if test="item.yyffbm != null">
        YYFFBM = #{item.yyffbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yyffmc != null">
        YYFFMC = #{item.yyffmc,jdbcType=VARCHAR},
      </if>
      <if test="item.pcbm != null">
        PCBM = #{item.pcbm,jdbcType=VARCHAR},
      </if>
      <if test="item.pcmc != null">
        PCMC = #{item.pcmc,jdbcType=VARCHAR},
      </if>
      <if test="item.pccs != null">
        PCCS = #{item.pccs,jdbcType=VARCHAR},
      </if>
      <if test="item.yfdw != null">
        YFDW = #{item.yfdw,jdbcType=VARCHAR},
      </if>
      <if test="item.yfdwmc != null">
        YFDWMC = #{item.yfdwmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zx_end != null">
        ZX_END = #{item.zx_end,jdbcType=TIMESTAMP},
      </if>
      <if test="item.yfbm != null">
        YFBM = #{item.yfbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yfmc != null">
        YFMC = #{item.yfmc,jdbcType=VARCHAR},
      </if>
      <if test="item.jxbm != null">
        JXBM = #{item.jxbm,jdbcType=VARCHAR},
      </if>
        <if test="item.sfcy != null">
        SFCY = #{item.sfcy,jdbcType=VARCHAR},
      </if>
        <if test="item.zyjs != null">
        ZYJS = #{item.zyjs,jdbcType=DECIMAL},
      </if>
       <if test="item.zxkssj != null">
        zxkssj = #{item.zxkssj,jdbcType=DATE},
      </if>

       <if test="item.zyzd != null">
        ZYZD = #{item.zyzd,jdbcType=VARCHAR},
      </if>
        <if test="item.zyzh != null">
        ZYZH = #{item.zyzh,jdbcType=VARCHAR},
      </if>
       <if test="item.zyzf != null">
        ZYZF = #{item.zyzf,jdbcType=VARCHAR},
      </if>
      <if test="item.zytt != null">
        ZYTT = #{item.zytt,jdbcType=VARCHAR},
      </if>
      <if test="item.srcs != null">
        srcs = #{item.srcs},
      </if>
      <if test="item.dbrxm != null">
        dbrxm = #{item.dbrxm},
      </if>
      <if test="item.dbrzjhm != null">
        dbrzjhm = #{item.dbrzjhm},
      </if>
      <if test="item.dbrlxdh != null">
        dbrlxdh = #{item.dbrlxdh},
      </if>
      <if test="item.symd != null">
        SYMD = #{item.symd,jdbcType=VARCHAR},
      </if>
      <if test="item.ssmc != null">
        SSMC = #{item.ssmc,jdbcType=VARCHAR},
      </if>
      <if test="item.syqk != null">
        SYQK = #{item.syqk,jdbcType=VARCHAR},
      </if>
      <if test="item.lhyyls != null">
        LHYYLS = #{item.lhyyls,jdbcType=VARCHAR},
      </if>
      <if test="item.syyy != null">
        SYYY = #{item.syyy,jdbcType=VARCHAR},
      </if>
      <if test="item.ssbm != null">
        SSBM = #{item.ssbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yke186 != null">
        yke186 = #{item.yke186,jdbcType=VARCHAR},
      </if>
      <if test="item.zysrcs != null">
        zysrcs = #{item.zysrcs,jdbcType=VARCHAR},
      </if>
      <if test="item.yzuuid != null">
        yzuuid = #{item.yzuuid,jdbcType=VARCHAR},
      </if>
      <if test="item.mtksrq != null">
        mtksrq = #{item.mtksrq,jdbcType=VARCHAR},
      </if>
      <if test="item.mtjsrq != null">
        mtjsrq = #{item.mtjsrq,jdbcType=VARCHAR},
      </if>
    </set>
    where YPYZXH = #{item.ypyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL} and shbz = '0' and ystzbz = '0'
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
    </foreach>
</update>

<!-- 药品医嘱作废 -->
<update id="DeleteYpyz" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
  update ZYYS_YPYZ
    <set>
       <if test="item.ystzbz != null">
        YSTZBZ = #{item.ystzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfbz != null">
        ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfys != null">
        ZFYS = #{item.zfys,jdbcType=VARCHAR},
      </if>
      <if test="item.zfysxm != null">
        ZFYSXM = #{item.zfysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfsj != null">
        ZFSJ = #{item.zfsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zfyy != null">
        ZFYY = #{item.zfyy,jdbcType=VARCHAR},
      </if>
    </set>
    where YPYZXH = #{item.ypyzxh,jdbcType=VARCHAR}
    	<if test="item.mxxh != null and item.mxxh != 0">
      	  and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      	</if>
      	  and zfbz = '0' and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
         and zyh = #{item.zyh,jdbcType=VARCHAR}
    </foreach>
</update>

<!-- 药品医嘱停嘱 -->
<update id="StopYpyz" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
  update ZYYS_YPYZ
    <set>
       <if test="item.ystzbz != null">
        YSTZBZ = #{item.ystzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfbz != null">
        ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfys != null">
        ZFYS = #{item.zfys,jdbcType=VARCHAR},
      </if>
      <if test="item.zfysxm != null">
        ZFYSXM = #{item.zfysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfsj != null">
        ZFSJ = #{item.zfsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zfyy != null">
        ZFYY = #{item.zfyy,jdbcType=VARCHAR},
      </if>
        TZYS = #{item.tzys,jdbcType=VARCHAR},
        TZYSXM = #{item.tzysxm,jdbcType=VARCHAR},
        YSTZSJ = #{item.ystzsj,jdbcType=DATE},
      <if test="item.lcljJdbm != null">
        LCLJ_JDBM=#{item.lcljJdbm,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljJdmc != null">
        LCLJ_JDMC=#{item.lcljJdmc,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljYzxmbm != null">
        LCLJ_YZXMBM=#{item.lcljYzxmbm,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljYzxmmc != null">
        LCLJ_YZXMMC=#{item.lcljYzxmmc,jdbcType=VARCHAR},
      </if>
        YSTZSM = #{item.ystzsm,jdbcType=TIMESTAMP},
    </set>
    where YPYZXH = #{item.ypyzxh,jdbcType=VARCHAR}
      	  and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      	  and zfbz = '0' and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
    </foreach>
</update>





<!-- 医疗医嘱新增 -->
<insert id="InsertYlyz" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
	insert into ZYYS_YLYZ
    <trim prefix="(" suffix=")" suffixOverrides=",">
    <if test="item.xhid != null">
        XHID,
      </if>
    <if test="item.ybtclb != null">
        YBTCLB,
      </if>
      <if test="item.ybtclbmc != null">
        YBTCLBMC,
      </if>
      <if test="item.nbtclb != null">
        NBTCLB,
      </if>
      <if test="item.ylyzxh != null">
        YLYZXH,
      </if>
      <if test="item.mxxh != null">
        MXXH,
      </if>
      <if test="item.zyh != null">
        ZYH,
      </if>
      <if test="item.ksbm != null">
        KSBM,
      </if>
      <if test="item.yzlx != null">
        YZLX,
      </if>
      <if test="item.yzfl != null">
        YZFL,
      </if>
      <if test="item.jcfl != null">
        JCFL,
      </if>
      <if test="item.yebh != null">
        YEBH,
      </if>
       <if test="item.xseyzbz != null">
        XSEYZBZ,
      </if>
      <if test="item.ksrq != null">
        KSRQ,
      </if>
      <if test="item.xdys != null">
        XDYS,
      </if>
      <if test="item.xdysxm != null">
        XDYSXM,
      </if>
      <if test="item.rymxzlbm != null">
        RYMXZLBM,
      </if>
      <if test="item.rymxzlmc != null">
        RYMXZLMC,
      </if>
      <if test="item.ryypyzxh != null">
        RYYPYZXH,
      </if>
      <if test="item.ryypmxxh != null">
        RYYPMXXH,
      </if>
      <if test="item.gllb != null">
        GLLB,
      </if>
      <if test="item.hldj != null">
        HLDJ,
      </if>
      <if test="item.bqdj != null">
        BQDJ,
      </if>
      <if test="item.tsyz != null">
        TSYZ,
      </if>
      <if test="item.ryjyxh != null">
        RYJYXH,
      </if>
      <if test="item.sqdh != null">
        SQDH,
      </if>
      <if test="item.xssx != null">
        XSSX,
      </if>
      <if test="item.yssm != null">
        YSSM,
      </if>
      <if test="item.sl != null">
        SL,
      </if>
      <if test="item.dj != null">
        DJ,
      </if>
      <if test="item.sfjj != null">
        SFJJ,
      </if>
      <if test="item.ysqm != null">
        YSQM,
      </if>
      <if test="item.ysqmxm != null">
        YSQMXM,
      </if>
      <if test="item.ysqmks != null">
        YSQMKS,
      </if>
      <if test="item.ysqmksmc != null">
        YSQMKSMC,
      </if>
      <if test="item.ysqmsj != null">
        YSQMSJ,
      </if>
      <if test="item.ysecqm != null">
        YSECQM,
      </if>
      <if test="item.ysecqmxm != null">
        YSECQMXM,
      </if>
      <if test="item.ysecqmks != null">
        YSECQMKS,
      </if>
      <if test="item.ysecqmksmc != null">
        YSECQMKSMC,
      </if>
      <if test="item.ysecqmsj != null">
        YSECQMSJ,
      </if>
      <if test="item.zxbz != null">
        ZXBZ,
      </if>
      <if test="item.zxhs != null">
        ZXHS,
      </if>
      <if test="item.zxhsxm != null">
        ZXHSXM,
      </if>
      <if test="item.zxks != null">
        ZXKS,
      </if>
      <if test="item.zxksmc != null">
        ZXKSMC,
      </if>
      <if test="item.zxsj != null">
        ZXSJ,
      </if>
      <if test="item.hsecqm != null">
        HSECQM,
      </if>
      <if test="item.hsecqmxm != null">
        HSECQMXM,
      </if>
      <if test="item.hsecqmsj != null">
        HSECQMSJ,
      </if>
      <if test="item.shbz != null">
        SHBZ,
      </if>
      <if test="item.shhs != null">
        SHHS,
      </if>
      <if test="item.shhsxm != null">
        SHHSXM,
      </if>
      <if test="item.shsj != null">
        SHSJ,
      </if>
      <if test="item.ystzbz != null">
        YSTZBZ,
      </if>
      <if test="item.tzys != null">
        TZYS,
      </if>
      <if test="item.tzysxm != null">
        TZYSXM,
      </if>
      <if test="item.ystzsj != null">
        YSTZSJ,
      </if>
      <if test="item.ystzsm != null">
        YSTZSM,
      </if>
      <if test="item.hstzbz != null">
        HSTZBZ,
      </if>
      <if test="item.tzhs != null">
        TZHS,
      </if>
      <if test="item.tzhsxm != null">
        TZHSXM,
      </if>
      <if test="item.hstzsj != null">
        HSTZSJ,
      </if>
      <if test="item.yzzcbz != null">
        YZZCBZ,
      </if>
      <if test="item.yzzcys != null">
        YZZCYS,
      </if>
      <if test="item.yzzcysxm != null">
        YZZCYSXM,
      </if>
      <if test="item.yzzcsj != null">
        YZZCSJ,
      </if>
      <if test="item.sfjz != null">
        SFJZ,
      </if>
      <if test="item.sqlbbm != null">
        SQLBBM,
      </if>
      <if test="item.zfbz != null">
        ZFBZ,
      </if>
      <if test="item.zfys != null">
        ZFYS,
      </if>
      <if test="item.zfysxm != null">
        ZFYSXM,
      </if>
      <if test="item.zfsj != null">
        ZFSJ,
      </if>
      <if test="item.zfyy != null">
        ZFYY,
      </if>
      <if test="item.bzsm != null">
        BZSM,
      </if>
      <if test="item.ksmc != null">
        KSMC,
      </if>
      <if test="item.lczd != null">
        LCZD,
      </if>
      <if test="item.jcms != null">
        JCMS,
      </if>
      <if test="item.jcbw != null">
        JCBW,
      </if>
      <if test="item.lczz != null">
        LCZZ,
      </if>
      <if test="item.bbsm != null">
        BBSM,
      </if>
      <if test="item.jymd != null">
        JYMD,
      </if>
      <if test="item.yljgbm != null">
        YLJGBM,
      </if>
      <if test="item.zxkssj != null">
       zxkssj,
      </if>
      <if test="item.lcljJdbm != null">
       lclj_Jdbm,
      </if>
      <if test="item.lcljJdmc != null">
        lclj_Jdmc,
      </if>
      <if test="item.lcljYzxmbm != null">
        lclj_Yzxmbm,
      </if>
      <if test="item.lcljYzxmmc != null">
        lclj_Yzxmmc,
      </if>
      <if test="item.mbbm != null">
        MBBM,
      </if>
      <if test="item.jybb != null">
        JYBB,
      </if>
      <if test="item.mbxmbm != null">
        MBXMBM,
      </if>
      <if test="item.pcbm != null">
        PCBM,
      </if>
      <if test="item.pcmc != null">
        PCMC,
      </if>
      <if test="item.pccs != null">
        PCCS,
      </if>
      <if test="item.yyffmc != null">
        yyffmc,
      </if>
      <if test="item.cyzd != null">
        cyzd,
      </if>
      <if test="item.yke186 != null">
        yke186,
      </if>
      <if test="item.fzh != null">
        fzh,
      </if>
      <if test="item.ypyzxh != null">
        ypyzxh,
      </if>
      <if test="item.yyzq != null">
        yyzq,
      </if>
      <if test="item.zysrcs != null">
        zysrcs,
      </if>
      <if test="item.yzuuid != null">
        yzuuid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="item.xhid != null">
        #{item.xhid,jdbcType=VARCHAR},
      </if>
      <if test="item.ybtclb != null">
        #{item.ybtclb,jdbcType=VARCHAR},
      </if>
      <if test="item.ybtclbmc != null">
        #{item.ybtclbmc,jdbcType=VARCHAR},
      </if>
      <if test="item.nbtclb != null">
        #{item.nbtclb,jdbcType=VARCHAR},
      </if>
      <if test="item.ylyzxh != null">
        #{item.ylyzxh,jdbcType=VARCHAR},
      </if>
      <if test="item.mxxh != null">
        #{item.mxxh,jdbcType=DECIMAL},
      </if>
      <if test="item.zyh != null">
        #{item.zyh,jdbcType=VARCHAR},
      </if>
      <if test="item.ksbm != null">
        #{item.ksbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzlx != null">
        #{item.yzlx,jdbcType=VARCHAR},
      </if>
      <if test="item.yzfl != null">
        #{item.yzfl,jdbcType=VARCHAR},
      </if>
      <if test="item.jcfl != null">
        #{item.jcfl,jdbcType=VARCHAR},
      </if>
      <if test="item.yebh != null">
        #{item.yebh,jdbcType=VARCHAR},
      </if>
       <if test="item.xseyzbz != null">
        #{item.xseyzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.ksrq != null">
        #{item.ksrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.xdys != null">
        #{item.xdys,jdbcType=VARCHAR},
      </if>
      <if test="item.xdysxm != null">
        #{item.xdysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.rymxzlbm != null">
        #{item.rymxzlbm,jdbcType=VARCHAR},
      </if>
      <if test="item.rymxzlmc != null">
        #{item.rymxzlmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ryypyzxh != null">
        #{item.ryypyzxh,jdbcType=VARCHAR},
      </if>
      <if test="item.ryypmxxh != null">
        #{item.ryypmxxh,jdbcType=DECIMAL},
      </if>
      <if test="item.gllb != null">
        #{item.gllb,jdbcType=VARCHAR},
      </if>
      <if test="item.hldj != null">
        #{item.hldj,jdbcType=VARCHAR},
      </if>
      <if test="item.bqdj != null">
        #{item.bqdj,jdbcType=VARCHAR},
      </if>
      <if test="item.tsyz != null">
        #{item.tsyz,jdbcType=VARCHAR},
      </if>
      <if test="item.ryjyxh != null">
        #{item.ryjyxh,jdbcType=VARCHAR},
      </if>
      <if test="item.sqdh != null">
        #{item.sqdh,jdbcType=VARCHAR},
      </if>
      <if test="item.xssx != null">
        #{item.xssx,jdbcType=DECIMAL},
      </if>
      <if test="item.yssm != null">
        #{item.yssm,jdbcType=VARCHAR},
      </if>
      <if test="item.sl != null">
        #{item.sl,jdbcType=DECIMAL},
      </if>
      <if test="item.dj != null">
        #{item.dj,jdbcType=DECIMAL},
      </if>
      <if test="item.sfjj != null">
        #{item.sfjj,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqm != null">
        #{item.ysqm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmxm != null">
        #{item.ysqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmks != null">
        #{item.ysqmks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmksmc != null">
        #{item.ysqmksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmsj != null">
        #{item.ysqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ysecqm != null">
        #{item.ysecqm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmxm != null">
        #{item.ysecqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmks != null">
        #{item.ysecqmks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmksmc != null">
        #{item.ysecqmksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmsj != null">
        #{item.ysecqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zxbz != null">
        #{item.zxbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zxhs != null">
        #{item.zxhs,jdbcType=VARCHAR},
      </if>
      <if test="item.zxhsxm != null">
        #{item.zxhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zxks != null">
        #{item.zxks,jdbcType=VARCHAR},
      </if>
      <if test="item.zxksmc != null">
        #{item.zxksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zxsj != null">
        #{item.zxsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.hsecqm != null">
        #{item.hsecqm,jdbcType=VARCHAR},
      </if>
      <if test="item.hsecqmxm != null">
        #{item.hsecqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.hsecqmsj != null">
        #{item.hsecqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.shbz != null">
        #{item.shbz,jdbcType=VARCHAR},
      </if>
      <if test="item.shhs != null">
        #{item.shhs,jdbcType=VARCHAR},
      </if>
      <if test="item.shhsxm != null">
        #{item.shhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.shsj != null">
        #{item.shsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ystzbz != null">
        #{item.ystzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.tzys != null">
        #{item.tzys,jdbcType=VARCHAR},
      </if>
      <if test="item.tzysxm != null">
        #{item.tzysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ystzsj != null">
        #{item.ystzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ystzsm != null">
        #{item.ystzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.hstzbz != null">
        #{item.hstzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.tzhs != null">
        #{item.tzhs,jdbcType=VARCHAR},
      </if>
      <if test="item.tzhsxm != null">
        #{item.tzhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.hstzsj != null">
        #{item.hstzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.yzzcbz != null">
        #{item.yzzcbz,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcys != null">
        #{item.yzzcys,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcysxm != null">
        #{item.yzzcysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcsj != null">
        #{item.yzzcsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.sfjz != null">
        #{item.sfjz,jdbcType=VARCHAR},
      </if>
      <if test="item.sqlbbm != null">
        #{item.sqlbbm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfbz != null">
        #{item.zfbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfys != null">
        #{item.zfys,jdbcType=VARCHAR},
      </if>
      <if test="item.zfysxm != null">
        #{item.zfysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfsj != null">
        #{item.zfsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zfyy != null">
        #{item.zfyy,jdbcType=VARCHAR},
      </if>
      <if test="item.bzsm != null">
        #{item.bzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.ksmc != null">
        #{item.ksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.lczd != null">
        #{item.lczd,jdbcType=VARCHAR},
      </if>
      <if test="item.jcms != null">
        #{item.jcms,jdbcType=VARCHAR},
      </if>
      <if test="item.jcbw != null">
        #{item.jcbw,jdbcType=VARCHAR},
      </if>
      <if test="item.lczz != null">
        #{item.lczz,jdbcType=VARCHAR},
      </if>
      <if test="item.bbsm != null">
        #{item.bbsm,jdbcType=VARCHAR},
      </if>
      <if test="item.jymd != null">
        #{item.jymd,jdbcType=VARCHAR},
      </if>
      <if test="item.yljgbm != null">
        #{item.yljgbm,jdbcType=VARCHAR},
      </if>
       <if test="item.zxkssj != null">
        #{item.zxkssj,jdbcType=DATE},
      </if>
      <if test="item.lcljJdbm != null">
        #{item.lcljJdbm,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljJdmc != null">
        #{item.lcljJdmc,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljYzxmbm != null">
        #{item.lcljYzxmbm,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljYzxmmc != null">
        #{item.lcljYzxmmc,jdbcType=VARCHAR},
      </if>
      <if test="item.mbbm != null">
        #{item.mbbm,jdbcType=VARCHAR},
      </if>
      <if test="item.jybb != null">
        #{item.jybb,jdbcType=VARCHAR},
      </if>
      <if test="item.mbxmbm != null">
        #{item.mbxmbm,jdbcType=VARCHAR},
      </if>
      <if test="item.pcbm != null">
        #{item.pcbm},
      </if>
      <if test="item.pcmc != null">
        #{item.pcmc},
      </if>
      <if test="item.pccs != null">
        #{item.pccs},
      </if>
      <if test="item.yyffmc != null">
        #{item.yyffmc},
      </if>
      <if test="item.cyzd != null">
        #{item.cyzd},
      </if>
      <if test="item.yke186 != null">
        #{item.yke186},
      </if>
      <if test="item.fzh != null">
        #{item.fzh},
      </if>
      <if test="item.ypyzxh != null">
        #{item.ypyzxh},
      </if>
      <if test="item.yyzq != null">
        #{item.yyzq},
      </if>
      <if test="item.zysrcs != null">
        #{item.zysrcs,jdbcType=VARCHAR},
      </if>
      <if test="item.yzuuid != null">
        #{item.yzuuid,jdbcType=VARCHAR},
      </if>
    </trim>
  </foreach>
</insert>

<!-- 医疗医嘱修改 -->
<update id="UpdateYlyz" parameterType="java.util.List">
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
   update ZYYS_YLYZ
    <set>
      <if test="item.ybtclb != null">
        YBTCLB = #{item.ybtclb,jdbcType=VARCHAR},
      </if>
      <if test="item.ybtclbmc != null">
        YBTCLBMC = #{item.ybtclbmc,jdbcType=VARCHAR},
      </if>
      <if test="item.nbtclb != null">
        NBTCLB = #{item.nbtclb,jdbcType=VARCHAR},
      </if>
      <if test="item.yzlx != null">
        YZLX = #{item.yzlx,jdbcType=VARCHAR},
      </if>
      <if test="item.yzfl != null">
        YZFL = #{item.yzfl,jdbcType=VARCHAR},
      </if>
      <if test="item.jcfl != null">
        JCFL = #{item.jcfl,jdbcType=VARCHAR},
      </if>
      <if test="item.yebh != null">
        YEBH = #{item.yebh,jdbcType=VARCHAR},
      </if>
       <if test="item.xseyzbz != null">
        XSEYZBZ = #{item.xseyzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.ksrq != null">
        KSRQ = #{item.ksrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.xdys != null">
        XDYS = #{item.xdys,jdbcType=VARCHAR},
      </if>
      <if test="item.xdysxm != null">
        XDYSXM = #{item.xdysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.rymxzlbm != null">
        RYMXZLBM = #{item.rymxzlbm,jdbcType=VARCHAR},
      </if>
      <if test="item.rymxzlmc != null">
        RYMXZLMC = #{item.rymxzlmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ryypyzxh != null">
        RYYPYZXH = #{item.ryypyzxh,jdbcType=VARCHAR},
      </if>
      <if test="item.ryypmxxh != null">
        RYYPMXXH = #{item.ryypmxxh,jdbcType=DECIMAL},
      </if>
      <if test="item.gllb != null">
        GLLB = #{item.gllb,jdbcType=VARCHAR},
      </if>
      <if test="item.hldj != null">
        HLDJ = #{item.hldj,jdbcType=VARCHAR},
      </if>
      <if test="item.bqdj != null">
        BQDJ = #{item.bqdj,jdbcType=VARCHAR},
      </if>
      <if test="item.tsyz != null">
        TSYZ = #{item.tsyz,jdbcType=VARCHAR},
      </if>
      <if test="item.ryjyxh != null">
        RYJYXH = #{item.ryjyxh,jdbcType=VARCHAR},
      </if>
      <if test="item.sqdh != null">
        SQDH = #{item.sqdh,jdbcType=VARCHAR},
      </if>
      <if test="item.xssx != null">
        XSSX = #{item.xssx,jdbcType=DECIMAL},
      </if>
      <if test="item.yssm != null">
        YSSM = #{item.yssm,jdbcType=VARCHAR},
      </if>
      <if test="item.sl != null">
        SL = #{item.sl,jdbcType=DECIMAL},
      </if>
      <if test="item.dj != null">
        DJ = #{item.dj,jdbcType=DECIMAL},
      </if>
      <if test="item.sfjj != null">
        SFJJ = #{item.sfjj,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqm != null">
        YSQM = #{item.ysqm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmxm != null">
        YSQMXM = #{item.ysqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmks != null">
        YSQMKS = #{item.ysqmks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmksmc != null">
        YSQMKSMC = #{item.ysqmksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ysqmsj != null">
        YSQMSJ = #{item.ysqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ysecqm != null">
        YSECQM = #{item.ysecqm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmxm != null">
        YSECQMXM = #{item.ysecqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmks != null">
        YSECQMKS = #{item.ysecqmks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmksmc != null">
        YSECQMKSMC = #{item.ysecqmksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.ysecqmsj != null">
        YSECQMSJ = #{item.ysecqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zxbz != null">
        ZXBZ = #{item.zxbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zxhs != null">
        ZXHS = #{item.zxhs,jdbcType=VARCHAR},
      </if>
      <if test="item.zxhsxm != null">
        ZXHSXM = #{item.zxhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zxks != null">
        ZXKS = #{item.zxks,jdbcType=VARCHAR},
      </if>
      <if test="item.zxksmc != null">
        ZXKSMC = #{item.zxksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zxsj != null">
        ZXSJ = #{item.zxsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.hsecqm != null">
        HSECQM = #{item.hsecqm,jdbcType=VARCHAR},
      </if>
      <if test="item.hsecqmxm != null">
        HSECQMXM = #{item.hsecqmxm,jdbcType=VARCHAR},
      </if>
      <if test="item.hsecqmsj != null">
        HSECQMSJ = #{item.hsecqmsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.shbz != null">
        SHBZ = #{item.shbz,jdbcType=VARCHAR},
      </if>
      <if test="item.shhs != null">
        SHHS = #{item.shhs,jdbcType=VARCHAR},
      </if>
      <if test="item.shhsxm != null">
        SHHSXM = #{item.shhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.shsj != null">
        SHSJ = #{item.shsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ystzbz != null">
        YSTZBZ = #{item.ystzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.tzys != null">
        TZYS = #{item.tzys,jdbcType=VARCHAR},
      </if>
      <if test="item.tzysxm != null">
        TZYSXM = #{item.tzysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ystzsj != null">
        YSTZSJ = #{item.ystzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ystzsm != null">
        YSTZSM = #{item.ystzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.hstzbz != null">
        HSTZBZ = #{item.hstzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.tzhs != null">
        TZHS = #{item.tzhs,jdbcType=VARCHAR},
      </if>
      <if test="item.tzhsxm != null">
        TZHSXM = #{item.tzhsxm,jdbcType=VARCHAR},
      </if>
      <if test="item.hstzsj != null">
        HSTZSJ = #{item.hstzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.yzzcbz != null">
        YZZCBZ = #{item.yzzcbz,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcys != null">
        YZZCYS = #{item.yzzcys,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcysxm != null">
        YZZCYSXM = #{yzzcysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzzcsj != null">
        YZZCSJ = #{item.yzzcsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.sfjz != null">
        SFJZ = #{item.sfjz,jdbcType=VARCHAR},
      </if>
      <if test="item.sqlbbm != null">
        SQLBBM = #{item.sqlbbm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfbz != null">
        ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfys != null">
        ZFYS = #{item.zfys,jdbcType=VARCHAR},
      </if>
      <if test="item.zfysxm != null">
        ZFYSXM = #{item.zfysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfsj != null">
        ZFSJ = #{item.zfsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zfyy != null">
        ZFYY = #{item.zfyy,jdbcType=VARCHAR},
      </if>
      <if test="item.bzsm != null">
        BZSM = #{item.bzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.ksmc != null">
        KSMC = #{item.ksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.lczd != null">
        LCZD = #{item.lczd,jdbcType=VARCHAR},
      </if>
      <if test="item.jcms != null">
        JCMS = #{item.jcms,jdbcType=VARCHAR},
      </if>
      <if test="item.jcbw != null">
        JCBW = #{item.jcbw,jdbcType=VARCHAR},
      </if>
      <if test="item.lczz != null">
        LCZZ = #{item.lczz,jdbcType=VARCHAR},
      </if>
      <if test="item.bbsm != null">
        BBSM = #{item.bbsm,jdbcType=VARCHAR},
      </if>
      <if test="item.jymd != null">
        JYMD = #{item.jymd,jdbcType=VARCHAR},
      </if>
      <if test="item.zx_end != null">
        ZX_END = #{item.zx_end,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zxkssj != null">
        zxkssj = #{item.zxkssj,jdbcType=DATE},
      </if>
      <if test="item.lcljJdbm != null">
        LCLJJDBM=#{item.lcljJdbm,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljJdmc != null">
        LCLJJDMC=#{item.lcljJdmc,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljYzxmbm != null">
        LCLJYZXMBM=#{item.lcljYzxmbm,jdbcType=VARCHAR},
      </if>
      <if test="item.lcljYzxmmc != null">
        LCLJYZXMMC=#{item.lcljYzxmmc,jdbcType=VARCHAR},
      </if>
      <if test="item.mbbm != null">
        MBBM = #{item.mbbm,jdbcType=VARCHAR},
      </if>
      <if test="item.jybb != null">
        JYBB = #{item.jybb,jdbcType=VARCHAR},
      </if>
      <if test="item.mbxmbm != null">
        MBXMBM = #{item.mbxmbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yke186 != null">
        yke186 = #{item.yke186,jdbcType=VARCHAR},
      </if>
      <if test="item.fzh != null">
        fzh = #{item.fzh,jdbcType=VARCHAR},
      </if>
      <if test="item.ypyzxh != null">
        ypyzxh = #{item.ypyzxh,jdbcType=VARCHAR},
      </if>
    </set>
    where YLYZXH = #{item.ylyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL} and shbz = '0' and ystzbz = '0'
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
  </foreach>
</update>

<!-- 医疗医嘱作废 -->
<update id="DeleteYlyz" parameterType="java.util.List">
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
   update ZYYS_YLYZ
    <set>
      <if test="item.ystzbz != null">
        YSTZBZ = #{item.ystzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfbz != null">
        ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfys != null">
        ZFYS = #{item.zfys,jdbcType=VARCHAR},
      </if>
      <if test="item.zfysxm != null">
        ZFYSXM = #{item.zfysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfsj != null">
        ZFSJ = #{item.zfsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zfyy != null">
        ZFYY = #{item.zfyy,jdbcType=VARCHAR},
      </if>
    </set>
    where YLYZXH = #{item.ylyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      and zfbz = '0' and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
      and zyh = #{item.zyh,jdbcType=VARCHAR}
  </foreach>
  </update>

  <!-- 医疗医嘱停嘱 -->
  <update id="StopYlyz" parameterType="java.util.List">
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
   update ZYYS_YLYZ
    <set>
      <if test="item.ystzbz != null">
        YSTZBZ = #{item.ystzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfbz != null">
        ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
      </if>
      <if test="item.zfys != null">
        ZFYS = #{item.zfys,jdbcType=VARCHAR},
      </if>
      <if test="item.zfysxm != null">
        ZFYSXM = #{item.zfysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.zfsj != null">
        ZFSJ = #{item.zfsj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.zfyy != null">
        ZFYY = #{item.zfyy,jdbcType=VARCHAR},
      </if>
        TZYS = #{item.tzys,jdbcType=VARCHAR},
        TZYSXM = #{item.tzysxm,jdbcType=VARCHAR},
        YSTZSJ = #{item.ystzsj,jdbcType=DATE},
        YSTZSM = #{item.ystzsm,jdbcType=TIMESTAMP},
    </set>
    where YLYZXH = #{item.ylyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      and zfbz = '0' and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
  </foreach>
</update>


<!-- 临床诊断新增 -->
<insert id="InsertLczd" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
 insert into ZYYS_YLYZ_LCZD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="item.ylyzxh != null" >
        YLYZXH,
      </if>
      <if test="item.mxxh != null" >
        MXXH,
      </if>
      <if test="item.sqlb != null" >
        SQLB,
      </if>
      <if test="item.lczd != null" >
        LCZD,
      </if>
      <if test="item.jcms != null" >
        JCMS,
      </if>
      <if test="item.jcbw != null" >
        JCBW,
      </if>
      <if test="item.lczz != null" >
        LCZZ,
      </if>
      <if test="item.jybb != null" >
        JYBB,
      </if>
      <if test="item.bbsm != null" >
        BBSM,
      </if>
      <if test="item.jymd != null" >
        JYMD,
      </if>
      <if test="item.yljgbm != null" >
        YLJGBM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="item.ylyzxh != null" >
        #{item.ylyzxh,jdbcType=VARCHAR},
      </if>
      <if test="item.mxxh != null" >
        #{item.mxxh,jdbcType=DECIMAL},
      </if>
      <if test="item.sqlb != null" >
        #{item.sqlb,jdbcType=VARCHAR},
      </if>
      <if test="item.lczd != null" >
        #{item.lczd,jdbcType=CLOB},
      </if>
      <if test="item.jcms != null" >
        #{item.jcms,jdbcType=CLOB},
      </if>
      <if test="item.jcbw != null" >
        #{item.jcbw,jdbcType=CLOB},
      </if>
      <if test="item.lczz != null" >
        #{item.lczz,jdbcType=CLOB},
      </if>
      <if test="item.jybb != null" >
        #{item.jybb,jdbcType=CLOB},
      </if>
      <if test="item.bbsm != null" >
        #{item.bbsm,jdbcType=CLOB},
      </if>
      <if test="item.jymd != null" >
        #{item.jymd,jdbcType=CLOB},
      </if>
      <if test="item.yljgbm != null" >
        #{item.yljgbm,jdbcType=CLOB},
      </if>
    </trim>
  </foreach>
</insert>

<!-- 临床诊断修改 -->
<update id="UpdateLczd" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
  update ZYYS_YLYZ_LCZD
    <set>
      <if test="item.sqlb != null">
        SQLB = #{item.sqlb,jdbcType=VARCHAR},
      </if>
      <if test="item.lczd != null">
        LCZD = #{item.lczd,jdbcType=CLOB},
      </if>
      <if test="item.jcms != null">
        JCMS = #{item.jcms,jdbcType=CLOB},
      </if>
      <if test="item.jcbw != null">
        JCBW = #{item.jcbw,jdbcType=CLOB},
      </if>
      <if test="item.lczz != null">
        LCZZ = #{item.lczz,jdbcType=CLOB},
      </if>
      <if test="item.jybb != null">
        JYBB = #{item.jybb,jdbcType=CLOB},
      </if>
      <if test="item.bbsm != null">
        BBSM = #{item.bbsm,jdbcType=CLOB},
      </if>
      <if test="item.jymd != null">
        JYMD = #{item.jymd,jdbcType=CLOB},
      </if>
    </set>
    where YLYZXH = #{item.ylyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
  </foreach>
</update>

<!-- 临床诊断作废 -->
<update id="DeleteLczd" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
  update ZYYS_YLYZ_LCZD
    <set>
      <if test="item.sqlb != null">
        SQLB = #{item.sqlb,jdbcType=VARCHAR},
      </if>
      <if test="item.lczd != null">
        LCZD = #{item.lczd,jdbcType=CLOB},
      </if>
      <if test="item.jcms != null">
        JCMS = #{item.jcms,jdbcType=CLOB},
      </if>
      <if test="item.jcbw != null">
        JCBW = #{item.jcbw,jdbcType=CLOB},
      </if>
      <if test="item.lczz != null">
        LCZZ = #{item.lczz,jdbcType=CLOB},
      </if>
      <if test="item.jybb != null">
        JYBB = #{item.jybb,jdbcType=CLOB},
      </if>
      <if test="item.bbsm != null">
        BBSM = #{item.bbsm,jdbcType=CLOB},
      </if>
      <if test="item.jymd != null">
        JYMD = #{item.jymd,jdbcType=CLOB},
      </if>
    </set>
    where YLYZXH = #{item.ylyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
  </foreach>
</update>



<!-- 药品医嘱开始日期修改 -->
<update id="UpdateYpyzKsrq" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
  update ZYYS_YPYZ
    <set>
      <if test="item.ksrq != null">
        KSRQ = #{item.ksrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.yke186 != null">
        Yke186 = #{item.yke186,jdbcType=VARCHAR},
      </if>
    </set>
    where YPYZXH = #{item.ypyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      and xhid = #{item.xhid,jdbcType=VARCHAR}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
    </foreach>
</update>

<!-- 医疗医嘱开始日期修改-->
  <update id="UpdateYlyzKsrq" parameterType="java.util.List">
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
   update ZYYS_YLYZ
    <set>
      <if test="item.ksrq != null">
        KSRQ = #{item.ksrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.yke186 != null">
        Yke186 = #{item.yke186,jdbcType=VARCHAR},
      </if>
    </set>
    where YLYZXH = #{item.ylyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      and xhid = #{item.xhid,jdbcType=VARCHAR}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
  </foreach>
</update>



<!-- 药品医嘱执行科室修改 -->
<update id="UpdateYpyzZxks" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
  update ZYYS_YPYZ
    <set>
      <if test="item.zxks != null">
        zxks = #{item.zxks},
      </if>
      <if test="item.zxksmc != null">
        zxksmc = #{item.zxksmc},
      </if>
    </set>
    where YPYZXH = #{item.ypyzxh,jdbcType=VARCHAR}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
      <if test="item.mxxh != 0">
        and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      </if>
      <if test="item.xhid != null">
        and xhid = #{item.xhid,jdbcType=VARCHAR}
      </if>
    </foreach>
</update>

<!-- 医疗医嘱执行科室修改-->
  <update id="UpdateYlyzZxks" parameterType="java.util.List">
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
   update ZYYS_YLYZ
    <set>
      <if test="item.zxks != null">
        zxks = #{item.zxks},
      </if>
      <if test="item.zxksmc != null">
        zxksmc = #{item.zxksmc},
      </if>
    </set>
    where YLYZXH = #{item.ylyzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      and xhid = #{item.xhid,jdbcType=VARCHAR}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
  </foreach>
</update>

<!-- 医嘱单药品打印标志修改-->
<update id="UpdateYpyzDybz" parameterType="java.util.List">
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    update zyys_ypyz
    <set>
    <if test="item.tzrqdybz != null">
      tzrqdybz = #{item.tzrqdybz},
    </if>
      <if test="item.tzsjdybz != null">
        tzsjdybz = #{item.tzsjdybz},
      </if>
      <if test="item.tzzxsjdybz != null">
        tzzxsjdybz = #{item.tzzxsjdybz},
      </if>
      <if test="item.tzysdybz != null">
        tzysdybz = #{item.tzysdybz},
      </if>
      <if test="item.tzhsdybz != null">
        tzhsdybz = #{item.tzhsdybz},
      </if>
      <if test="item.zxsjdybz != null">
        zxsjdybz = #{item.zxsjdybz},
      </if>
      <if test="item.zxzdybz != null">
        zxzdybz = #{item.zxzdybz},
      </if>
    </set>
    where  YPYZXH = #{item.ypyzxh,jdbcType=VARCHAR}
        and MXXH = #{item.mxxh,jdbcType=DECIMAL}
        and YZLX = #{item.yzlx,jdbcType=DECIMAL}
        and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
  </foreach>
</update>
  <!-- 医嘱单医疗打印标志修改-->
<update id="UpdateYlyzDybz" parameterType="java.util.List">
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    update zyys_ylyz
    <set>
      <if test="item.tzrqdybz != null">
        tzrqdybz = #{item.tzrqdybz},
      </if>
      <if test="item.tzsjdybz != null">
        tzsjdybz = #{item.tzsjdybz},
      </if>
      <if test="item.tzzxsjdybz != null">
        tzzxsjdybz = #{item.tzzxsjdybz},
      </if>
      <if test="item.tzysdybz != null">
        tzysdybz = #{item.tzysdybz},
      </if>
      <if test="item.tzhsdybz != null">
        tzhsdybz = #{item.tzhsdybz},
      </if>
      <if test="item.zxsjdybz != null">
        zxsjdybz = #{item.zxsjdybz},
      </if>
      <if test="item.zxzdybz != null">
        zxzdybz = #{item.zxzdybz},
      </if>
    </set>
    where  YPYZXH = #{item.ypyzxh,jdbcType=VARCHAR}
    and MXXH = #{item.mxxh,jdbcType=DECIMAL}
    and YZLX = #{item.yzlx,jdbcType=DECIMAL}
    and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
  </foreach>
</update>

<!--  修改检查检验打印次数-->
  <update id="updateYlyzDycs" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
        update zyys_ylyz set dycs=(select dycs+1 from zyys_ylyz where xhid = #{item.xhid,jdbcType=VARCHAR} and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR})
        <if test="item.lczd != null and item.lczd!=''.toString()">
          ,lczd=#{item.lczd}
      </if>
      <if test="item.jcms != null and item.jcms!=''.toString()">
        ,jcms=#{item.jcms}
      </if>
      <if test="item.lczz != null and item.lczz!=''.toString()">
        ,lczz=#{item.lczz}
      </if>
        where  xhid = #{item.xhid,jdbcType=VARCHAR} and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
    </foreach>
  </update>

  <!-- 会诊医嘱新增 -->
  <insert id="InsertZyysHzyz" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
      insert into ZYYS_YSYW_HZYZ
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.yzxh != null">   <!-- 医嘱序号 -->
          YZXH,
        </if>
        <if test="item.mxxh != null"><!-- 明细序号 -->
          MXXH,
        </if>
        <if test="item.xhid != null"><!-- 序号ID -->
          XHID,
        </if>
        <if test="item.yzlx != null"><!-- 医嘱类型 -->
          YZLX,
        </if>
        <if test="item.yzfl != null"><!-- 医嘱分类 -->
          YZFL,
        </if>
        <if test="item.sfjz != null"><!-- 是否急诊 -->
          SFJZ,
        </if>
        <if test="item.kssj != null"><!-- 开始时间 -->
          KSSJ,
        </if>
        <if test="item.ysqm != null"><!-- 医生签名 -->
          YSQM,
        </if>
        <if test="item.ysqmxm != null"><!-- 医生签名姓名 -->
          YSQMXM,
        </if>
        <if test="item.kdys != null"><!-- 开单医生 -->
          KDYS,
        </if>
        <if test="item.kdysxm != null"><!-- 开单医生姓名 -->
          KDYSXM,
        </if>
        <if test="item.yfbm != null"> <!-- 药房编码 -->
          YFBM,
        </if>
        <if test="item.yfmc != null"> <!-- 药房名称 -->
          YFMC,
        </if>
        <if test="item.yebh != null"> <!-- 婴儿编号 -->
          YEBH,
        </if>
        <if test="item.zyh != null">    <!-- 住院号 -->
          ZYH,
        </if>
        <if test="item.ksbm != null">    <!-- 科室编码 -->
          KSBM,
        </if>
        <if test="item.ksmc != null">    <!-- 科室名称 -->
          KSMC,
        </if>
        <if test="item.yljgbm != null"> <!-- 医疗机构编码 -->
          YLJGBM,
        </if>
        <if test="item.xmbm != null">   <!-- 药品编码 -->
          XMBM,
        </if>
        <if test="item.xmmc != null">    <!-- 项目名称 -->
          XMMC,
        </if>
        <if test="item.pcbm != null">   <!-- 频次编码 -->
          PCBM,
        </if>
        <if test="item.pcmc != null">   <!-- 频次编码 -->
          PCMC,
        </if>
        <if test="item.yyffbm != null">   <!-- 用药方法 -->
          YYFFBM,
        </if>
        <if test="item.yyffmc != null">   <!-- 用药方法 -->
          YYFFMC,
        </if>
        <if test="item.dcjl != null">   <!-- 单次剂量 -->
          DCJL,
        </if>
        <if test="item.jldw != null">   <!-- 剂量单位 -->
          JLDW,
        </if>
        <if test="item.jldwmc != null">   <!-- 剂量单位 -->
          JLDWMC,
        </if>
        <if test="item.yfdw != null and item.yfdw != ''.toString()">
          YFDW,<!-- 药房单位 -->
        </if>
        <if test="item.yfdwmc != null and item.yfdwmc != ''.toString()">
          YFDWMC,<!-- 药房单位名称 -->
        </if>
        <if test="item.sl != null"> <!-- 数量 -->
          SL,
        </if>
        <if test="item.dj != null"> <!-- 单价 -->
          DJ,
        </if>
        <if test="item.sysd != null">   <!-- 输液速度 -->
          SYSD,
        </if>
        <if test="item.sysddw != null"> <!-- 速度单位 -->
          SYSDDW,
        </if>
        <if test="item.bzsm != null">   <!-- 备注说明 -->
          BZSM,
        </if>
        <if test="item.yysm != null">   <!-- 用药说明 -->
          YYSM,
        </if>
        <if test="item.yyts != null">   <!-- 用药天数 -->
          YYTS,
        </if>
        <if test="item.fzh != null">    <!-- 分组号 -->
          FZH,
        </if>
        <if test="item.zxks != null">   <!-- 执行科室编码 -->
          ZXKS,
        </if>
        <if test="item.zxksmc != null">   <!-- 执行科室编码 -->
          ZXKSMC,
        </if>
        <if test="item.zhfybm != null"> <!-- 组合费用编码 -->
          ZHFYBM,
        </if>
        <if test="item.zhfymc != null"> <!-- 组合费用编码 -->
          ZHFYMC,
        </if>
        <if test="item.ypbz != null">   <!-- 药品标志 0 诊疗 1 药品 -->
          YPBZ,
        </if>
        <if test="item.sfcy != null">   <!-- 是否草药 0 非草药 1 草药 -->
          SFCY,
        </if>
        <if test="item.zytt != null">   <!-- 中药汤头 -->
          ZYTT,
        </if>
        <if test="item.zyfs != null">   <!-- 中药副数 -->
          ZYFS,
        </if>
        <if test="item.zyzb != null">   <!-- 中医主病 -->
          ZYZB,
        </if>
        <if test="item.zyzz != null">   <!-- 中医主症 -->
          ZYZZ,
        </if>
        <if test="item.zfbz != null">   <!-- 作废标志 -->
          ZFBZ,
        </if>
        <if test="item.zfry != null">   <!-- 作废人员 -->
          ZFRY,
        </if>
        <if test="item.zfryxm != null">   <!-- 作废人员 -->
          ZFRYXM,
        </if>
        <if test="item.zfrq != null">   <!-- 作废日期 -->
          ZFRQ,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.yzxh != null">   <!-- 医嘱序号 -->
          #{item.yzxh,jdbcType=VARCHAR},
        </if>
        <if test="item.mxxh != null"><!-- 明细序号 -->
          #{item.mxxh,jdbcType=DECIMAL},
        </if>
        <if test="item.xhid != null"><!-- 序号ID -->
          #{item.xhid,jdbcType=VARCHAR},
        </if>
        <if test="item.yzlx != null"><!-- 医嘱类型 -->
          #{item.yzlx,jdbcType=VARCHAR},
        </if>
        <if test="item.yzfl != null"><!-- 医嘱分类 -->
          #{item.yzfl,jdbcType=VARCHAR},
        </if>
        <if test="item.sfjz != null"><!-- 是否急诊 -->
          #{item.sfjz,jdbcType=VARCHAR},
        </if>
        <if test="item.kssj != null"><!-- 开始时间 -->
          #{item.kssj,jdbcType=DATE},
        </if>
        <if test="item.ysqm != null"><!-- 医生签名 -->
          #{item.ysqm,jdbcType=VARCHAR},
        </if>
        <if test="item.ysqmxm != null"><!-- 医生签名姓名 -->
          #{item.ysqmxm,jdbcType=VARCHAR},
        </if>
        <if test="item.kdys != null"><!-- 开单医生 -->
          #{item.kdys,jdbcType=VARCHAR},
        </if>
        <if test="item.kdysxm != null"><!-- 开单医生姓名 -->
          #{item.kdysxm,jdbcType=VARCHAR},
        </if>
        <if test="item.yfbm != null"> <!-- 药房编码 -->
          #{item.yfbm,jdbcType=VARCHAR},
        </if>
        <if test="item.yfmc != null"> <!-- 药房名称 -->
          #{item.yfmc,jdbcType=VARCHAR},
        </if>
        <if test="item.yebh != null"> <!-- 婴儿编号 -->
          #{item.yebh,jdbcType=VARCHAR},
        </if>
        <if test="item.zyh != null">    <!-- 住院号 -->
          #{item.zyh,jdbcType=VARCHAR},
        </if>
        <if test="item.ksbm != null">    <!-- 科室编码 -->
          #{item.ksbm,jdbcType=VARCHAR},
        </if>
        <if test="item.ksmc != null">    <!-- 科室名称 -->
          #{item.ksmc,jdbcType=VARCHAR},
        </if>
        <if test="item.yljgbm != null"> <!-- 医疗机构编码 -->
          #{item.yljgbm,jdbcType=VARCHAR},
        </if>
        <if test="item.xmbm != null">   <!-- 药品编码 -->
          #{item.xmbm,jdbcType=VARCHAR},
        </if>
        <if test="item.xmmc != null">    <!-- 项目名称 -->
          #{item.xmmc,jdbcType=VARCHAR},
        </if>
        <if test="item.pcbm != null">   <!-- 频次编码 -->
          #{item.pcbm,jdbcType=VARCHAR},
        </if>
        <if test="item.pcmc != null">   <!-- 频次编码 -->
          #{item.pcmc,jdbcType=VARCHAR},
        </if>
        <if test="item.yyffbm != null">   <!-- 用药方法 -->
          #{item.yyffbm,jdbcType=VARCHAR},
        </if>
        <if test="item.yyffmc != null">   <!-- 用药方法 -->
          #{item.yyffmc,jdbcType=VARCHAR},
        </if>
        <if test="item.dcjl != null">   <!-- 单次剂量 -->
          #{item.dcjl,jdbcType=DECIMAL},
        </if>
        <if test="item.jldw != null">   <!-- 剂量单位 -->
          #{item.jldw,jdbcType=VARCHAR},
        </if>
        <if test="item.jldwmc != null">   <!-- 剂量单位 -->
          #{item.jldwmc,jdbcType=VARCHAR},
        </if>
        <if test="item.yfdw != null and item.yfdw != ''.toString()">
          #{item.yfdw},<!-- 药房单位 -->
        </if>
        <if test="item.yfdwmc != null and item.yfdwmc != ''.toString()">
          #{item.yfdwmc},<!-- 药房单位名称 -->
        </if>
        <if test="item.sl != null"> <!-- 数量 -->
          #{item.sl,jdbcType=DECIMAL},
        </if>
        <if test="item.dj != null"> <!-- 单价 -->
          #{item.dj,jdbcType=DECIMAL},
        </if>
        <if test="item.sysd != null">   <!-- 输液速度 -->
          #{item.sysd,jdbcType=DECIMAL},
        </if>
        <if test="item.sysddw != null"> <!-- 速度单位 -->
          #{item.sysddw,jdbcType=VARCHAR},
        </if>
        <if test="item.bzsm != null">   <!-- 备注说明 -->
          #{item.bzsm,jdbcType=VARCHAR},
        </if>
        <if test="item.yysm != null">   <!-- 用药说明 -->
          #{item.yysm,jdbcType=VARCHAR},
        </if>
        <if test="item.yyts != null">   <!-- 用药天数 -->
          #{item.yyts,jdbcType=DECIMAL},
        </if>
        <if test="item.fzh != null">    <!-- 分组号 -->
          #{item.fzh,jdbcType=DECIMAL},
        </if>
        <if test="item.zxks != null">   <!-- 执行科室编码 -->
          #{item.zxks,jdbcType=VARCHAR},
        </if>
        <if test="item.zxksmc != null">   <!-- 执行科室编码 -->
          #{item.zxksmc,jdbcType=VARCHAR},
        </if>
        <if test="item.zhfybm != null"> <!-- 组合费用编码 -->
          #{item.zhfybm,jdbcType=VARCHAR},
        </if>
        <if test="item.zhfymc != null"> <!-- 组合费用编码 -->
          #{item.zhfymc,jdbcType=VARCHAR},
        </if>
        <if test="item.ypbz != null">   <!-- 药品标志 0 诊疗 1 药品 -->
          #{item.ypbz,jdbcType=VARCHAR},
        </if>
        <if test="item.sfcy != null">   <!-- 是否草药 0 非草药 1 草药 -->
          #{item.sfcy,jdbcType=VARCHAR},
        </if>
        <if test="item.zytt != null">   <!-- 中药汤头 -->
          #{item.zytt,jdbcType=VARCHAR},
        </if>
        <if test="item.zyfs != null">   <!-- 中药副数 -->
          #{item.zyfs,jdbcType=DECIMAL},
        </if>
        <if test="item.zyzb != null">   <!-- 中医主病 -->
          #{item.zyzb,jdbcType=VARCHAR},
        </if>
        <if test="item.zyzz != null">   <!-- 中医主症 -->
          #{item.zyzz,jdbcType=VARCHAR},
        </if>
        <if test="item.zfbz != null">   <!-- 作废标志 -->
          #{item.zfbz,jdbcType=VARCHAR},
        </if>
        <if test="item.zfry != null">   <!-- 作废人员 -->
          #{item.zfry,jdbcType=VARCHAR},
        </if>
        <if test="item.zfryxm != null">   <!-- 作废人员 -->
          #{item.zfryxm,jdbcType=VARCHAR},
        </if>
        <if test="item.zfrq != null">   <!-- 作废日期 -->
          #{item.zfrq,jdbcType=DATE},
        </if>
      </trim>
    </foreach>
  </insert>

  <!-- 会诊医嘱修改 -->
  <update id="UpdateZyysHzyz" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
      update ZYYS_YSYW_HZYZ
      <set>
        <if test="item.yzlx != null"><!-- 医嘱类型 -->
          YZLX = #{item.yzlx},
        </if>
        <if test="item.yzfl != null"><!-- 医嘱分类 -->
          YZFL = #{item.yzfl},
        </if>
        <if test="item.sfjz != null"><!-- 是否急诊 -->
          SFJZ = #{item.sfjz},
        </if>
        <if test="item.kssj != null"><!-- 开始时间 -->
          KSSJ = #{item.kssj},
        </if>
        <if test="item.ysqm != null"><!-- 医生签名 -->
          YSQM = #{item.ysqm},
        </if>
        <if test="item.ysqmxm != null"><!-- 医生签名姓名 -->
          YSQMXM = #{item.ysqmxm},
        </if>
        <if test="item.kdys != null"><!-- 开单医生 -->
          KDYS = #{item.kdys},
        </if>
        <if test="item.kdysxm != null"><!-- 开单医生姓名 -->
          KDYSXM = #{item.kdysxm},
        </if>
        <if test="item.yfbm != null"> <!-- 药房编码 -->
          YFBM = #{item.yfbm},
        </if>
        <if test="item.yfmc != null"> <!-- 药房名称 -->
          YFMC = #{item.yfmc},
        </if>
        <if test="item.yebh != null"> <!-- 婴儿编号 -->
          YEBH = #{item.yebh},
        </if>
        <if test="item.zyh != null">    <!-- 住院号 -->
          ZYH = #{item.zyh},
        </if>
        <if test="item.ksbm != null">    <!-- 科室编码 -->
          KSBM = #{item.ksbm},
        </if>
        <if test="item.ksmc != null">    <!-- 科室名称 -->
          KSMC = #{item.ksmc},
        </if>
        <if test="item.yljgbm != null"> <!-- 医疗机构编码 -->
          YLJGBM = #{item.yljgbm},
        </if>
        <if test="item.xmbm != null">   <!-- 药品编码 -->
          XMBM = #{item.xmbm},
        </if>
        <if test="item.xmmc != null">    <!-- 项目名称 -->
          XMMC = #{item.xmmc},
        </if>
        <if test="item.pcbm != null">   <!-- 频次编码 -->
          PCBM = #{item.pcbm},
        </if>
        <if test="item.pcmc != null">   <!-- 频次编码 -->
          PCMC = #{item.pcmc},
        </if>
        <if test="item.yyffbm != null">   <!-- 用药方法 -->
          YYFFBM = #{item.yyffbm},
        </if>
        <if test="item.yyffmc != null">   <!-- 用药方法 -->
          YYFFMC = #{item.yyffmc},
        </if>
        <if test="item.dcjl != null">   <!-- 单次剂量 -->
          DCJL = #{item.dcjl},
        </if>
        <if test="item.jldw != null">   <!-- 剂量单位 -->
          JLDW = #{item.jldw},
        </if>
        <if test="item.jldwmc != null">   <!-- 剂量单位 -->
          JLDWMC = #{item.jldwmc},
        </if>
        <if test="item.yfdw != null and item.yfdw != ''.toString()">
          YFDW = #{item.yfdw},<!-- 药房单位 -->
        </if>
        <if test="item.yfdwmc != null and item.yfdwmc != ''.toString()">
          YFDWMC = #{item.yfdwmc},<!-- 药房单位名称 -->
        </if>
        <if test="item.sl != null"> <!-- 数量 -->
          SL = #{item.sl},
        </if>
        <if test="item.dj != null"> <!-- 单价 -->
          DJ = #{item.dj},
        </if>
        <if test="item.sysd != null">   <!-- 输液速度 -->
          SYSD = #{item.sysd},
        </if>
        <if test="item.sysddw != null"> <!-- 速度单位 -->
          SYSDDW = #{item.sysddw},
        </if>
        <if test="item.bzsm != null">   <!-- 备注说明 -->
          BZSM = #{item.bzsm},
        </if>
        <if test="item.yysm != null">   <!-- 用药说明 -->
          YYSM = #{item.yysm},
        </if>
        <if test="item.yyts != null">   <!-- 用药天数 -->
          YYTS = #{item.yyts},
        </if>
        <if test="item.fzh != null">    <!-- 分组号 -->
          FZH = #{item.fzh},
        </if>
        <if test="item.zxks != null">   <!-- 执行科室编码 -->
          ZXKS = #{item.zxks},
        </if>
        <if test="item.zxksmc != null">   <!-- 执行科室编码 -->
          ZXKSMC = #{item.zxksmc},
        </if>
        <if test="item.zhfybm != null"> <!-- 组合费用编码 -->
          ZHFYBM = #{item.zhfybm},
        </if>
        <if test="item.zhfymc != null"> <!-- 组合费用编码 -->
          ZHFYMC = #{item.zhfymc},
        </if>
        <if test="item.ypbz != null">   <!-- 药品标志 0 诊疗 1 药品 -->
          YPBZ = #{item.ypbz},
        </if>
        <if test="item.sfcy != null">   <!-- 是否草药 0 非草药 1 草药 -->
          SFCY = #{item.sfcy},
        </if>
        <if test="item.zytt != null">   <!-- 中药汤头 -->
          ZYTT = #{item.zytt},
        </if>
        <if test="item.zyfs != null">   <!-- 中药副数 -->
          ZYFS = #{item.zyfs},
        </if>
        <if test="item.zyzb != null">   <!-- 中医主病 -->
          ZYZB = #{item.zyzb},
        </if>
        <if test="item.zyzz != null">   <!-- 中医主症 -->
          ZYZZ = #{item.zyzz},
        </if>
        <if test="item.zfbz != null">   <!-- 作废标志 -->
          ZFBZ = #{item.zfbz},
        </if>
        <if test="item.zfry != null">   <!-- 作废人员 -->
          ZFRY = #{item.zfry},
        </if>
        <if test="item.zfryxm != null">   <!-- 作废人员 -->
          ZFRYXM = #{item.zfryxm},
        </if>
        <if test="item.zfrq != null">   <!-- 作废日期 -->
          ZFRQ = #{item.zfrq},
        </if>
      </set>
      where ZYH = #{item.zyh,jdbcType=VARCHAR}
      AND YZXH = #{item.yzxh,jdbcType=VARCHAR}
      and MXXH = #{item.mxxh,jdbcType=DECIMAL}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
    </foreach>
  </update>


  <update id="cxyz" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
      update zyb_brfy
      <set>
          zfbz = '1',
        zfry = #{item.cyrybm,jdbcType=VARCHAR},
        zfryxm = #{item.cyryxm,jdbcType=VARCHAR},
        zfsj = sysdate,
          bzsm = bzsm || '撤销未执行检查检验医嘱记录'
      </set>
      where ZYH = #{item.zyh,jdbcType=VARCHAR}
      AND YZXH = #{item.yzxhid,jdbcType=VARCHAR}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
      and yxbz = '0'
    </foreach>
  </update>

  <update id="cx_ylyzbz" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
      update zyys_ylyz
      <set>
          yzcxyy = #{item.parm,jdbcType=VARCHAR},
          cyrybm = #{item.cyrybm,jdbcType=VARCHAR},
          yzcxysxm = #{item.cyryxm,jdbcType=VARCHAR},
          yzcxsj = sysdate,
          yscxbzzc = '1'
      </set>
      where
      EXISTS (select 1 from zyb_brfy fy where zyys_ylyz.zyh = fy.zyh and zyys_ylyz.xhid = fy.yzxh
    	and zyys_ylyz.xhid = #{item.yzxhid,jdbcType=VARCHAR}
    	and zyys_ylyz.zyh = #{item.zyh,jdbcType=VARCHAR} and zyys_ylyz.YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
    	and fy.yxbz = '0' and fy.zfbz = '0')
    </foreach>
  </update>

  <update id="cxylyzbz" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
      update zyys_ylyz
      <set>
        yzcxyy = #{item.parm,jdbcType=VARCHAR},
        cyrybm = #{item.cyrybm,jdbcType=VARCHAR},
        yzcxysxm = #{item.cyryxm,jdbcType=VARCHAR},
        yzcxsj = sysdate,
        yscxbzzc = '1'
      </set>
      where
      EXISTS (select 1 from zyb_brfy fy where zyys_ylyz.zyh = fy.zyh and zyys_ylyz.xhid = fy.yzxh
      and zyys_ylyz.xhid = #{item.yzxhid,jdbcType=VARCHAR}
      and zyys_ylyz.zyh = #{item.zyh,jdbcType=VARCHAR} and zyys_ylyz.YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
      and fy.yxbz = '0' and fy.zfbz = '0')
    </foreach>
  </update>

  <update id="shcx_ylyzbz" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
      update zyys_ylyz ylyz
      <set>
        yzcxshhs = #{item.cyrybm,jdbcType=VARCHAR},
        yzcxshhsxm = #{item.cyryxm,jdbcType=VARCHAR},
        yzcxshyj = #{item.bzsm,jdbcType=VARCHAR},
        yzcxshbz = #{item.yxbz,jdbcType=VARCHAR},
        yzcxshsj = sysdate
        <if test='item.yxbz == "1"'>
          ,yzcxbz = '1'
          ,bzsm = decode(bzsm,null,'',bzsm) || decode(yzcxyy,null,'',yzcxyy)
        </if>
        <if test='item.yxbz == "2"'>
          ,bzsm = decode(bzsm,null,'',bzsm) || decode(#{item.bzsm,jdbcType=VARCHAR},null,'',#{item.bzsm,jdbcType=VARCHAR})
        </if>
      </set>
      where
      EXISTS (select 1 from zyb_brfy fy where ylyz.zyh = fy.zyh and ylyz.xhid = fy.yzxh
      and ylyz.xhid = #{item.yzxhid,jdbcType=VARCHAR}
      and ylyz.zyh = #{item.zyh,jdbcType=VARCHAR} and ylyz.YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
      and fy.yxbz = '0' and fy.zfbz = '0')
    </foreach>
  </update>


  <select id="queryCheckFy" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel"
          resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel">
    select DISTINCT '项目['||yzxmmc||']申请时间['||to_char(sfrq,'yyyy-mm-dd hh24:mi')||']' mxfyxmmc from zyb_brfy where yljgbm = #{yljgbm}
    and zyh = #{zyh} and yzxh in
    <foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
      #{item}
    </foreach>
    and yxbz = '1'
  </select>

  <select id="queryCheckYlyz" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel"
          resultType="com.supx.csp.api.zyys.ysyw.pojo.Zyys_ylyzModel">
    select DISTINCT '项目['||rymxzlmc||']' rymxzlmc from zyys_ylyz where yljgbm = #{yljgbm}
    and zyh = #{zyh} and xhid in
    <foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
      #{item}
    </foreach>
    and pacs_bz = '1'
  </select>

  <select id="checkYzxx" resultType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel"
   parameterType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel">
  		select * from (
			select  ypyz.srcs,null zlbm ,'药品' lx,'1' YPBZ,ypyz.YPYZXH YZXH, ypyz.MXXH,XHID, ypyz.ZYH, ypyz.KSBM, ypyz.KSMC, ypyz.YZLX, ypyz.YZFL, ypyz.YEBH, ypyz.KSRQ, ypyz.XDYS,ypyz.XDYSXM, ypyz.RYYPBM XMBM, ypyz.RYYPMC XMMC, ypyz.GLLB,
			    ypyz.TSYZ, ypyz.XSSX, ypyz.FZH, ypyz.DCJL, ypyz.JLDW,ypyz.JLDWMC,ypyz.YYTS, ypyz.DJ, ypyz.YYZL SL, ypyz.SYSD, ypyz.SYSDDW, ypyz.YYSM,'0' SFJJ, ypyz.YSQM,  ypyz.YSSM,
			    ypyz.YSQMXM, ypyz.YSQMKS, ypyz.YSQMKSMC, ypyz.YSQMSJ, ypyz.YSECQM, ypyz.YSECQMXM, ypyz.YSECQMKS, ypyz.YSECQMKSMC, ypyz.YSECQMSJ,
			    ypyz.ZXBZ, ypyz.ZXHS, ypyz.ZXHSXM, ypyz.ZXKS, ypyz.ZXKSMC, ypyz.ZXSJ, ypyz.HSECQM, ypyz.HSECQMXM, ypyz.HSECQMSJ, ypyz.SHBZ, ypyz.SHHS,ypyz.SHHSXM, ypyz.SHSJ,
			    ypyz.YSTZBZ, ypyz.TZYS, ypyz.TZYSXM, ypyz.YSTZSJ, ypyz.YSTZSM, ypyz.HSTZBZ, ypyz.TZHS, ypyz.TZHSXM, ypyz.HSTZSJ, ypyz.YZZCBZ,
			    ypyz.YZZCYS, ypyz.YZZCYSXM, ypyz.YZZCSJ, ypyz.KJYWSYMD, '0' SFJZ,'' SQLBBM,ypyz.ZFBZ, ypyz.ZFYS, ypyz.ZFYSXM, ypyz.ZFSJ,ypyz.ZFYY, ypyz.YPZL, ypyz.SQSYKSS, ypyz.SQSYSJ, ypyz.SQSYQZ, ypyz.SQYSXM,ypyz.BZSM,
			    null JCFL, null RYYPYZXH, null RYYPMXXH, null HLDJ,null RYJYXH, null SQDH,ypyz.YBTCLB, ypyz.YBTCLBMC, ypyz.NBTCLB, ypyz.YPGG,
			    ypyz.YYFFBM,ypyz.YYFFMC,ypyz.PCBM,ypyz.PCMC,ypyz.PCCS, null BQDJ,ypyz.YFDW,ypyz.YFDWMC,ypyz.YFBM,ypyz.YFMC,
			    null LCZD,null JCMS,null JCBW,null LCZZ,null BBSM,null JYMD,ypyz.SFCY,ypzd.jbjl,null zyzd,null zyzh,null zyzf,null zytt,
			    null mbbm,null mbxmbm, 0 dycs,null sfhzyz
			from zyys_ypyz ypyz
			inner join ykb_ypzd ypzd on ypzd.yljgbm=ypyz.yljgbm and ypzd.ypbm=ypyz.ryypbm
			 WHERE ( ypyz.yljgbm = #{yljgbm,jdbcType=VARCHAR} ) and (ypyz.sfcy is null or sfcy='0')
				   and  ypyz.zyh = #{zyh,jdbcType=VARCHAR}
                   <if test='sfcy == "0"'>
                     and  ypyz.xhid in
					<foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
					    #{item,jdbcType=VARCHAR}
					 </foreach>
                   </if>
                  <if test='sfcy == "1"'>
                    and ypyz.ypyzxh = #{yzxh,jdbcType=VARCHAR}
                  </if>
			union all
			select null srcs,zlmxxm.ZLLB as zlbm,'诊疗' lx,'0' YPBZ,yz.YLYZXH YZXH, yz.MXXH,yz.XHID, yz.ZYH, yz.KSBM,yz.KSMC, yz.YZLX, yz.YZFL, yz.YEBH, yz.KSRQ, yz.XDYS,yz.XDYSXM, yz.RYMXZLBM XMBM, yz.RYMXZLMC XMMC, yz.GLLB,
			     yz.TSYZ, yz.XSSX,null FZH, null DCJL, null JLDW,null JLDWMC,null YYTS, fy.DJ, yz.SL,null SYSD,null SYSDDW,yz.BZSM YYSM,'0' SFJJ, yz.YSQM,  yz.YSSM,
			    yz.YSQMXM, yz.YSQMKS, yz.YSQMKSMC, yz.YSQMSJ, yz.YSECQM, yz.YSECQMXM, yz.YSECQMKS, yz.YSECQMKSMC, yz.YSECQMSJ,
			    yz.ZXBZ, yz.ZXHS, yz.ZXHSXM, yz.ZXKS, yz.ZXKSMC,yz.ZXSJ, yz.HSECQM, yz.HSECQMXM, yz.HSECQMSJ, yz.SHBZ, yz.SHHS, yz.SHHSXM, yz.SHSJ,
			    yz.YSTZBZ, yz.TZYS, yz.TZYSXM, yz.YSTZSJ, yz.YSTZSM, yz.HSTZBZ, yz.TZHS, yz.TZHSXM, yz.HSTZSJ, yz.YZZCBZ,
			    yz.YZZCYS, yz.YZZCYSXM, yz.YZZCSJ,'' KJYWSYMD,  yz.SFJZ, yz.SQLBBM, yz.ZFBZ, yz.ZFYS, yz.ZFYSXM, yz.ZFSJ, yz.ZFYY,null YPZL,null SQSYKSS,null SQSYSJ,null SQSYQZ,null SQYSXM,yz.BZSM BZSM,
			    yz.JCFL, yz.RYYPYZXH, yz.RYYPMXXH, yz.HLDJ,yz.RYJYXH, yz.SQDH,yz.YBTCLB,yz.YBTCLBMC,yz.NBTCLB,null YPGG,
			    null YYFFBM,null YYFFMC,yz.PCBM,yz.PCMC,yz.PCCS,yz.BQDJ,null YFDW,null YFDWMC,null YFBM,null YFMC,
			    yz.LCZD,yz.JCMS,yz.JCBW,yz.LCZZ,yz.BBSM,yz.JYMD,null SFCY,null jbjl,null zyzd,null zyzh,null zyzf,null zytt,
			    yz.mbbm mbbm,yz.mbxmbm mbxmbm,yz.dycs,null sfhzyz
			from zyys_ylyz yz
			left join (select gl.zlxmbm,sum(mxfy.fydj*gl.sl) DJ,gl.yljgbm from gyb_zlxmglfy gl left join
			gyb_mxfyxm mxfy on mxfy.mxfybm=gl.fyxmbm and mxfy.yljgbm=gl.yljgbm group by gl.zlxmbm,gl.yljgbm )fy
			on fy.zlxmbm=yz.RYMXZLBM and fy.yljgbm=yz.yljgbm
			 left join GYB_ZLMXXM zlmxxm on zlmxxm.ZLXMBM = fy.zlxmbm
			 WHERE ( yz.yljgbm = #{yljgbm,jdbcType=VARCHAR} )
				   and  yz.zyh = #{zyh,jdbcType=VARCHAR}
                    <if test='sfcy == "0"'>
				   and  yz.xhid in
					<foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
					    #{item,jdbcType=VARCHAR}
					 </foreach>
                    </if>
                    <if test='sfcy == "1"'>
                      and yz.ylyzxh = #{yzxh,jdbcType=VARCHAR}
                    </if>
				union all
				select    srcs,null zlbm ,'药品' lx,'1' YPBZ,YZXH,null MXXH,null XHID,ZYH, KSBM, KSMC, YZLX, YZFL, null YEBH,to_date(KSRQ,'yyyy-MM-dd  hh24:mi')KSRQ, XDYS,XDYSXM, '中药医嘱' XMBM, '中药一剂'  XMMC,null GLLB,
			    null TSYZ,1 XSSX,null FZH,zyjs DCJL,null JLDW,'剂' JLDWMC,null YYTS,null DJ, sum(yyzl) sl,null SYSD,null SYSDDW,yysm YYSM,'0' SFJJ, YSQM,null  YSSM,
			    YSQMXM, YSQMKS, YSQMKSMC,to_date(YSQMSJ,'yyyy-MM-dd  hh24:mi')YSQMSJ, YSECQM, YSECQMXM, YSECQMKS, YSECQMKSMC,to_date(YSECQMSJ,'yyyy-MM-dd  hh24:mi')YSECQMSJ,
			    ZXBZ, ZXHS, ZXHSXM, ZXKS, ZXKSMC,to_date(ZXSJ,'yyyy-MM-dd  hh24:mi')ZXSJ , HSECQM, HSECQMXM,to_date(HSECQMSJ,'yyyy-MM-dd  hh24:mi')HSECQMSJ , SHBZ, SHHS, SHHSXM,
			    to_date(SHSJ,'yyyy-MM-dd  hh24:mi')SHSJ,
			    YSTZBZ, TZYS, TZYSXM,to_date(YSTZSJ,'yyyy-MM-dd  hh24:mi')YSTZSJ , YSTZSM, HSTZBZ, TZHS, TZHSXM,to_date(HSTZSJ,'yyyy-MM-dd  hh24:mi')HSTZSJ, YZZCBZ,
			    YZZCYS, YZZCYSXM,to_date(YZZCSJ,'yyyy-MM-dd  hh24:mi') YZZCSJ,  KJYWSYMD, '0' SFJZ,'' SQLBBM,ZFBZ, ZFYS, ZFYSXM,
			    to_date(ZFSJ,'yyyy-MM-dd  hh24:mi') ZFSJ,ZFYY,null YPZL, SQSYKSS,to_date(SQSYSJ,'yyyy-MM-dd  hh24:mi') SQSYSJ, SQSYQZ, SQYSXM,BZSM,
			    null JCFL, null RYYPYZXH, null RYYPMXXH, null HLDJ,null RYJYXH, null SQDH,null YBTCLB,null YBTCLBMC,null NBTCLB,null YPGG,
			    null YYFFBM,null YYFFMC,null PCBM,null PCMC,null PCCS, null BQDJ,null YFDW,null YFDWMC,null YFBM,null YFMC,
			    null LCZD,null JCMS,null JCBW,null LCZZ,null BBSM,null JYMD,SFCY,null jbjl,zyzd,zyzh,zyzf,zytt,null mbbm,null mbxmbm,0 dycs,null sfhzyz from (
			    select srcs,YPYZXH YZXH,ZYH, KSBM, KSMC, YZLX, YZFL,to_char(KSRQ,'yyyy-MM-dd  hh24:mi')KSRQ, XDYS,XDYSXM,
			    yyzl, YSQM,
			    YSQMXM, YSQMKS, YSQMKSMC, to_char(YSQMSJ,'yyyy-MM-dd  hh24:mi')YSQMSJ, YSECQM, YSECQMXM, YSECQMKS, YSECQMKSMC,to_char(YSECQMSJ,'yyyy-MM-dd  hh24:mi')YSECQMSJ,
			    ZXBZ, ZXHS, ZXHSXM, ZXKS, ZXKSMC,to_char(ZXSJ,'yyyy-MM-dd  hh24:mi')ZXSJ , HSECQM, HSECQMXM,to_char(HSECQMSJ,'yyyy-MM-dd  hh24:mi')HSECQMSJ , SHBZ, SHHS,
			    SHHSXM,to_char(SHSJ,'yyyy-MM-dd  HH:mm')SHSJ,zyjs,yysm,
			    YSTZBZ, TZYS, TZYSXM,to_char(YSTZSJ,'yyyy-MM-dd  hh24:mi')YSTZSJ , YSTZSM, HSTZBZ, TZHS, TZHSXM,to_char(HSTZSJ,'yyyy-MM-dd  hh24:mi')HSTZSJ,YZZCBZ,
			    YZZCYS, YZZCYSXM,to_char(YZZCSJ,'yyyy-MM-dd  hh24:mi') YZZCSJ, KJYWSYMD, ZFBZ, ZFYS, ZFYSXM,to_char(ZFSJ,'yyyy-MM-dd  hh24:mi') ZFSJ,ZFYY,SQSYKSS,
			    to_char(SQSYSJ,'yyyy-MM-dd  hh24:mi') SQSYSJ,SQSYQZ, SQYSXM,BZSM,
			    SFCY,null jbjl,zyzd,zyzh,zyzf,zytt
				from zyys_ypyz
			 WHERE ( yljgbm = #{yljgbm,jdbcType=VARCHAR} ) and sfcy='1'
				   and  zyh = #{zyh,jdbcType=VARCHAR}
                    <if test='sfcy == "0"'>
		            and  xhid in
					<foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
					    #{item,jdbcType=VARCHAR}
					 </foreach>
                    </if>
                <if test='sfcy == "1"'>
                  and ypyzxh = #{yzxh,jdbcType=VARCHAR}
                </if>
			)group by srcs,YZXH,zyh,KSBM, KSMC, YZLX, YZFL, KSRQ, XDYS,XDYSXM, YSQM,
			    YSQMXM, YSQMKS, YSQMKSMC, YSQMSJ, YSECQM, YSECQMXM, YSECQMKS, YSECQMKSMC, YSECQMSJ,
			    ZXBZ, ZXHS, ZXHSXM, ZXKS, ZXKSMC, ZXSJ, HSECQM, HSECQMXM, HSECQMSJ, SHBZ, SHHS, SHHSXM, SHSJ,
			    YSTZBZ, TZYS, TZYSXM, YSTZSJ, YSTZSM, HSTZBZ, TZHS, TZHSXM, HSTZSJ, YZZCBZ,
			    YZZCYS, YZZCYSXM, YZZCSJ, KJYWSYMD,ZFBZ, ZFYS, ZFYSXM, ZFSJ,ZFYY, SQSYKSS, SQSYSJ, SQSYQZ, SQYSXM,BZSM,SFCY,zyjs,yysm,jbjl,zyzd,zyzh,zyzf,zytt
			) a order by ksrq,XSSX asc
  </select>


  <select id="checkYpzx" parameterType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel"
   resultType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel">
  	select DISTINCT ryypmc as xmmc from hsz_yz_ypzxd where yljgbm = #{yljgbm}
		and zyh = #{zyh}
        <if test='sfcy == "0"'>
          and yzxhid in
          <foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
              #{item}
          </foreach>
        </if>
      <if test='sfcy == "1"'>
         and yzxh = #{yzxh,jdbcType=VARCHAR}
      </if>
		and (slbz = '1' or fybz = '1') and zfbz = '0'

  </select>

  <select id="getYzXssx" resultType="java.lang.Integer">
    select max(xssx) from (
        select yl.ylyzxh, yl.xssx from zyys_ylyz yl where yl.zyh = #{zyh} and yljgbm=#{yljgbm}
        union all
        select yp.ypyzxh, yp.xssx from zyys_ypyz yp where yp.zyh = #{zyh} and yljgbm=#{yljgbm}
    )
  </select>

  <select id="checkYlzx" parameterType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel"
   resultType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel">
  	select DISTINCT zlxmmc as xmmc from hsz_yz_ylzxd where yljgbm = #{yljgbm}
		and zyh = #{zyh} and yzxhid in
		<foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
			#{item}
		</foreach>
		and zfbz = '0'
    </select>


  <!-- 药品医嘱驳回 -->
  <update id="bhYpyz" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
      update ZYYS_YPYZ
      <set>
        <if test="item.ystzbz != null">
          YSTZBZ = #{item.ystzbz,jdbcType=VARCHAR},
        </if>
        <if test="item.zfbz != null">
          ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
        </if>
      </set>
      where zyh=#{item.zyh,jdbcType=VARCHAR} and xhid=#{item.xhid,jdbcType=VARCHAR}
    </foreach>
  </update>

  <!-- 诊疗医嘱驳回 -->
  <update id="bhYlyz" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
      update ZYYS_YLYZ
      <set>
        <if test="item.ystzbz != null">
          YSTZBZ = #{item.ystzbz,jdbcType=VARCHAR},
        </if>
        <if test="item.zfbz != null">
          ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
        </if>
      </set>
      where zyh=#{item.zyh,jdbcType=VARCHAR} and xhid=#{item.xhid,jdbcType=VARCHAR}
    </foreach>
  </update>


  <select id="checkYpzxKf" parameterType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel"
          resultType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel">
    select DISTINCT a.ryypmc as xmmc from zyys_ypyz a
      inner join hsz_yz_ypzxd b on a.yljgbm = b.yljgbm and a.ypyzxh = b.yzxh and a.mxxh = b.yzmxxh and a.ryypbm = b.ryypbm and a.zyh = b.zyh
       inner join hsz_yz_fymx c on b.zyh = c.zyh and c.yljgbm = b.yljgbm and b.fydh = c.bydh and b.ryypbm = c.ryypbm and c.ryyzzxlsh = b.ypzxlsh
      where a.zyh=#{zyh} and c.fysl-c.tysl>0
            and a.ypyzxh = #{yzxh} and a.mxxh = #{mxxh} and a.ryypbm = #{xmbm}

  </select>

  <select id="checkYlzxKf" parameterType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel"
          resultType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel">
    select DISTINCT a.rymxzlmc as xmmc from zyys_ylyz a
inner join hsz_yz_ylzxd b on a.zyh = b.zyh and a.ylyzxh = b.yzxh and a.mxxh = b.yzmxxh and a.rymxzlbm = b.zlxmbm
inner join zyb_brfy c on b.xhid = c.yzxhid and c.zyh = b.zyh
      where a.zyh=#{zyh} and c.yxbz='1' and c.zfbz='0' and c.sftf='0'
            and a.ylyzxh = #{yzxh} and a.mxxh = #{mxxh} and a.rymxzlbm = #{xmbm}

  </select>



  <select id="checkCzYpzxKf" parameterType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel"
          resultType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel">
      select DISTINCT a.ryypmc as xmmc from zyys_ypyz a
      where a.zyh=#{zyh}
      and a.ypyzxh = #{yzxh} and a.mxxh = #{mxxh} and a.ryypbm = #{xmbm} and a.shbz=1 and a.yzlx=1
  </select>
  <select id="checkCzYlzxKf" parameterType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel"
          resultType="com.supx.csp.api.zyys.ysyw.pojo.YzxxListResModel">
  select DISTINCT a.rymxzlmc as xmmc from zyys_ylyz a
  where a.zyh=#{zyh}  and a.shbz=1
  and a.ylyzxh = #{yzxh} and a.mxxh = #{mxxh} and a.rymxzlbm = #{xmbm} and a.yzlx=1
</select>

</mapper>
