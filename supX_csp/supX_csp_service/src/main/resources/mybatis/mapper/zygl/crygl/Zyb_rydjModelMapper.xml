<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.zygl.crygl.dao.New1Zyb_rydjModelMapper">

    <!-- 入院基本信息 -->
    <resultMap id="BaseResultMap" type="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        <id column="ZYH" property="zyh" jdbcType="VARCHAR"/>
        <result column="BAH" property="bah" jdbcType="VARCHAR"/>
        <result column="BRID" property="brid" jdbcType="VARCHAR"/>
        <result column="BXLBBM" property="bxlbbm" jdbcType="VARCHAR"/>
        <result column="BXLBMC" property="bxlbmc" jdbcType="VARCHAR"/>
        <result column="BRFB" property="brfb" jdbcType="VARCHAR"/>
        <result column="BRFBMC" property="brfbmc" jdbcType="VARCHAR"/>
        <result column="YWCKBH" property="ywckbh" jdbcType="VARCHAR"/>
        <result column="YWCKMC" property="ywckmc" jdbcType="VARCHAR"/>
        <result column="CZYBM" property="czybm" jdbcType="VARCHAR"/>
        <result column="CZYXM" property="czyxm" jdbcType="VARCHAR"/>
        <result column="YBKH" property="ybkh" jdbcType="VARCHAR"/>
        <result column="ZYZT" property="zyzt" jdbcType="VARCHAR"/>
        <result column="RYKS" property="ryks" jdbcType="VARCHAR"/>
        <result column="RYKSMC" property="ryksmc" jdbcType="VARCHAR"/>
        <result column="RYRQ" property="ryrq" jdbcType="TIMESTAMP"/>
        <result column="RYDJRQ" property="rydjrq" jdbcType="TIMESTAMP"/>
        <result column="CYRQ" property="cyrq" jdbcType="TIMESTAMP"/>
        <result column="BQCYBZ" property="bqcybz" jdbcType="VARCHAR"/>
        <result column="BQCYRQ" property="bqcyrq" jdbcType="TIMESTAMP"/>
        <result column="MZYS" property="mzys" jdbcType="VARCHAR"/>
        <result column="MZYSXM" property="mzysxm" jdbcType="VARCHAR"/>
        <result column="MZKS" property="mzks" jdbcType="VARCHAR"/>
        <result column="MZKSMC" property="mzksmc" jdbcType="VARCHAR"/>
        <result column="ZYYS" property="zyys" jdbcType="VARCHAR"/>
        <result column="ZYYSXM" property="zyysxm" jdbcType="VARCHAR"/>
        <result column="RYQK" property="ryqk" jdbcType="VARCHAR"/>
        <result column="RYTJ" property="rytj" jdbcType="VARCHAR"/>
        <result column="DBR" property="dbr" jdbcType="VARCHAR"/>
        <result column="DBJE" property="dbje" jdbcType="DECIMAL"/>
        <result column="BXBR" property="bxbr" jdbcType="VARCHAR"/>
        <result column="RYCWID" property="rycwid" jdbcType="VARCHAR"/>
        <result column="RYCWBH" property="rycwbh" jdbcType="VARCHAR"/>
        <result column="NL" property="nl" jdbcType="DECIMAL"/>
        <result column="NLDW" property="nldw" jdbcType="VARCHAR"/>
        <result column="YL" property="yl" jdbcType="DECIMAL"/>
        <result column="YLDW" property="yldw" jdbcType="VARCHAR"/>
        <result column="RYZDBM" property="ryzdbm" jdbcType="VARCHAR"/>
        <result column="RYZDMC" property="ryzdmc" jdbcType="VARCHAR"/>
        <result column="IFZF" property="ifzf" jdbcType="VARCHAR"/>
        <result column="ZFRY" property="zfry" jdbcType="VARCHAR"/>
        <result column="ZFRYXM" property="zfryxm" jdbcType="VARCHAR"/>
        <result column="ZFRQ" property="zfrq" jdbcType="TIMESTAMP"/>
        <result column="CYKS" property="cyks" jdbcType="VARCHAR"/>
        <result column="CYKSMC" property="cyksmc" jdbcType="VARCHAR"/>
        <result column="ZYCS" property="zycs" jdbcType="DECIMAL"/>
        <result column="PKHBZ" property="pkhbz" jdbcType="VARCHAR"/>
        <result column="BQBM" property="bqbm" jdbcType="VARCHAR"/>
        <result column="BQMC" property="bqmc" jdbcType="VARCHAR"/>
        <result column="JJRJYY" property="jjrjyy" jdbcType="VARCHAR"/>
        <result column="ICDBS" property="icdbs" jdbcType="VARCHAR"/>
        <result column="ZZSXQQ" property="zzsxqq" jdbcType="VARCHAR"/>
        <result column="BYXSFPQK" property="byxsfpqk" jdbcType="VARCHAR"/>
        <result column="ZLLB" property="zllb" jdbcType="VARCHAR"/>
        <result column="FZBZ" property="fzbz" jdbcType="VARCHAR"/>
        <result column="FBDD" property="fbdd" jdbcType="VARCHAR"/>
        <result column="FBSJ" property="fbsj" jdbcType="TIMESTAMP"/>
        <result column="RYZYZDBM" property="ryzyzdbm" jdbcType="VARCHAR"/>
        <result column="RYZYZDMC" property="ryzyzdmc" jdbcType="VARCHAR"/>
        <result column="MZZDBM" property="mzzdbm" jdbcType="VARCHAR"/>
        <result column="MZZDMC" property="mzzdmc" jdbcType="VARCHAR"/>
        <result column="MZZYZDBM" property="mzzyzdbm" jdbcType="VARCHAR"/>
        <result column="MZZYZDMC" property="mzzyzdmc" jdbcType="VARCHAR"/>
        <result column="BZSM" property="bzsm" jdbcType="VARCHAR"/>
    </resultMap>


    <!--入院信息，病人基本信息，预交记录，病人医疗信息 -->
    <resultMap id="RyxxResultMap" type="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        <id column="ZYH" property="zyh" jdbcType="VARCHAR"/>
        <result column="BAH" property="bah" jdbcType="VARCHAR"/>
        <result column="BRID" property="brid" jdbcType="VARCHAR"/>
        <result column="BXLBBM" property="bxlbbm" jdbcType="VARCHAR"/>
        <result column="BXLBMC" property="bxlbmc" jdbcType="VARCHAR"/>

        <result column="BRFB" property="brfb" jdbcType="VARCHAR"/>
        <result column="BRFBMC" property="brfbmc" jdbcType="VARCHAR"/>
        <result column="YWCKBH" property="ywckbh" jdbcType="VARCHAR"/>
        <result column="YWCKMC" property="ywckmc" jdbcType="VARCHAR"/>
        <result column="CZYBM" property="czybm" jdbcType="VARCHAR"/>
        <result column="CZYXM" property="czyxm" jdbcType="VARCHAR"/>
        <result column="YBKH" property="ybkh" jdbcType="VARCHAR"/>
        <result column="ZYZT" property="zyzt" jdbcType="VARCHAR"/>
        <result column="RYKS" property="ryks" jdbcType="VARCHAR"/>
        <result column="RYKSMC" property="ryksmc" jdbcType="VARCHAR"/>
        <result column="RYRQ" property="ryrq" jdbcType="TIMESTAMP"/>
        <result column="RYDJRQ" property="rydjrq" jdbcType="TIMESTAMP"/>
        <result column="CYRQ" property="cyrq" jdbcType="TIMESTAMP"/>
        <result column="BQCYBZ" property="bqcybz" jdbcType="VARCHAR"/>
        <result column="BQCYRQ" property="bqcyrq" jdbcType="TIMESTAMP"/>
        <result column="MZYS" property="mzys" jdbcType="VARCHAR"/>
        <result column="MZYSXM" property="mzysxm" jdbcType="VARCHAR"/>
        <result column="MZKS" property="mzks" jdbcType="VARCHAR"/>
        <result column="MZKSMC" property="mzksmc" jdbcType="VARCHAR"/>
        <result column="ZYYS" property="zyys" jdbcType="VARCHAR"/>
        <result column="ZYYSXM" property="zyysxm" jdbcType="VARCHAR"/>
        <result column="RYQK" property="ryqk" jdbcType="VARCHAR"/>
        <result column="RYTJ" property="rytj" jdbcType="VARCHAR"/>
        <result column="DBR" property="dbr" jdbcType="VARCHAR"/>
        <result column="DBJE" property="dbje" jdbcType="DECIMAL"/>
        <result column="BXBR" property="bxbr" jdbcType="VARCHAR"/>
        <result column="RYCWID" property="rycwid" jdbcType="VARCHAR"/>
        <result column="RYCWBH" property="rycwbh" jdbcType="VARCHAR"/>
        <result column="NL" property="nl" jdbcType="DECIMAL"/>
        <result column="NLDW" property="nldw" jdbcType="VARCHAR"/>
        <result column="NL2" property="nl2" jdbcType="DECIMAL"/>
        <result column="NLDW2" property="nldw2" jdbcType="VARCHAR"/>
        <result column="YL" property="yl" jdbcType="DECIMAL"/>
        <result column="YLDW" property="yldw" jdbcType="VARCHAR"/>
        <result column="RYZDBM" property="ryzdbm" jdbcType="VARCHAR"/>
        <result column="RYZDMC" property="ryzdmc" jdbcType="VARCHAR"/>
        <result column="IFZF" property="ifzf" jdbcType="VARCHAR"/>
        <result column="ZFRY" property="zfry" jdbcType="VARCHAR"/>
        <result column="ZFRYXM" property="zfryxm" jdbcType="VARCHAR"/>
        <result column="ZFRQ" property="zfrq" jdbcType="TIMESTAMP"/>
        <result column="CYKS" property="cyks" jdbcType="VARCHAR"/>
        <result column="CYKSMC" property="cyksmc" jdbcType="VARCHAR"/>
        <result column="ZYCS" property="zycs" jdbcType="DECIMAL"/>
        <result column="JBBM" property="jbbm" jdbcType="VARCHAR"/>
        <result column="JBMC" property="jbmc" jdbcType="VARCHAR"/>
        <result column="ZZSXQQ" property="zzsxqq" jdbcType="VARCHAR"/>
        <result column="BYXSFPQK" property="byxsfpqk" jdbcType="VARCHAR"/>
        <result column="bqcyrq" property="bqcyrq" jdbcType="TIMESTAMP"/>
        <result column="bqcybz" property="bqcybz" jdbcType="VARCHAR"/>
        <!-- 病人医疗信息 -->
        <result column="YLKLX" property="ylklx" jdbcType="VARCHAR"/>
        <result column="YLKH" property="ylkh" jdbcType="VARCHAR"/>
        <result column="ZHLJJE" property="zhljje" jdbcType="DECIMAL"/>
        <!-- 预交记录信息 -->
        <result column="ZFLXBM" property="zflxbm" jdbcType="VARCHAR"/>
        <result column="ZFLXMC" property="zflxmc" jdbcType="VARCHAR"/>
        <result column="YJJE" property="yjje" jdbcType="DECIMAL"/>
        <result column="YJRQ" property="yjrq" jdbcType="TIMESTAMP"/>
        <result column="FPHM" property="fphm" jdbcType="VARCHAR"/>
        <result column="ZHYE" property="zhye" jdbcType="DECIMAL"/>
        <result column="SFCR" property="sfcr" jdbcType="VARCHAR"/>
        <result column="BZSM" property="bzsm" jdbcType="VARCHAR"/>
        <result column="YBCYBZ" property="ybcybz" jdbcType="VARCHAR"/>
        <result column="aac001" property="aac001" jdbcType="VARCHAR"/>
        <!-- 病人基本信息 -->
        <collection property="brjbxxModel" column="brid" ofType="com.supx.csp.api.ghgl.ghyw.pojo.Gyb_brjbxxModel">
            <id column="BRJBXXID" property="brid" jdbcType="VARCHAR"/>
            <result column="BRXM" property="brxm" jdbcType="VARCHAR"/>
            <result column="BRXB" property="brxb" jdbcType="VARCHAR"/>
            <result column="CSRQ" property="csrq" jdbcType="TIMESTAMP"/>
            <result column="BRGJ" property="brgj" jdbcType="VARCHAR"/>
            <result column="BRMZ" property="brmz" jdbcType="VARCHAR"/>
            <result column="SFZJLX" property="sfzjlx" jdbcType="VARCHAR"/>
            <result column="SFZJHM" property="sfzjhm" jdbcType="VARCHAR"/>
            <result column="HYZK" property="hyzk" jdbcType="VARCHAR"/>
            <result column="ZYBM" property="zybm" jdbcType="VARCHAR"/>
            <result column="HZLX" property="hzlx" jdbcType="VARCHAR"/>
            <result column="CSD" property="csd" jdbcType="VARCHAR"/>
            <result column="SJHM" property="sjhm" jdbcType="VARCHAR"/>
            <result column="JZDSHENG" property="jzdsheng" jdbcType="VARCHAR"/>
            <result column="JZDSHI" property="jzdshi" jdbcType="VARCHAR"/>
            <result column="JZDXIAN" property="jzdxian" jdbcType="VARCHAR"/>
            <!-- <result column="GZDW" property="gzdw" jdbcType="VARCHAR" />
            <result column="DWDZ" property="dwdz" jdbcType="VARCHAR" />
            <result column="DWYB" property="dwyb" jdbcType="VARCHAR" />
            <result column="jzd_XZQH" property="jzd_xzqh" jdbcType="VARCHAR" />
            <result column="jzd_XIANG" property="jzd_xiang" jdbcType="VARCHAR" />
            <result column="jzd_JWH" property="jzd_jwh" jdbcType="VARCHAR" />
            <result column="jzd_CUN" property="jzd_cun" jdbcType="VARCHAR" />
            <result column="jzd_NONG" property="jzd_nong" jdbcType="VARCHAR" />
            <result column="jzd_LH" property="jzd_lh" jdbcType="VARCHAR" />
            <result column="jzd_MPH" property="jzd_mph" jdbcType="VARCHAR" /> -->
            <result column="jzdMC" property="jzdmc" jdbcType="VARCHAR"/>
            <result column="LXRXM" property="lxrxm" jdbcType="VARCHAR"/>
            <result column="LXRGX" property="lxrgx" jdbcType="VARCHAR"/>
            <result column="LXRDH" property="lxrdh" jdbcType="VARCHAR"/>
            <!--  <result column="hjd_XZQH" property="hjd_xzqh" jdbcType="VARCHAR" />
             <result column="hjd_SHENG" property="hjd_sheng" jdbcType="VARCHAR" />
             <result column="hjd_SHI" property="hjd_shi" jdbcType="VARCHAR" />
             <result column="hjd_XIAN" property="hjd_xian" jdbcType="VARCHAR" />
             <result column="hjd_XIANG" property="hjd_xiang" jdbcType="VARCHAR" />
             <result column="hjd_JWH" property="hjd_jwh" jdbcType="VARCHAR" />
             <result column="hjd_CUN" property="hjd_cun" jdbcType="VARCHAR" />
             <result column="hjd_NONG" property="hjd_nong" jdbcType="VARCHAR" />
             <result column="hjd_LH" property="hjd_lh" jdbcType="VARCHAR" />
             <result column="hjd_MPH" property="hjd_mph" jdbcType="VARCHAR" />
             <result column="HKDZ" property="hkdz" jdbcType="VARCHAR" />
             <result column="HKDYB" property="hkdyb" jdbcType="VARCHAR" />
             <result column="LXRDZ" property="lxrdz" jdbcType="VARCHAR" />
             <result column="LXRDW" property="lxrdw" jdbcType="VARCHAR" />
             <result column="LXRYB" property="lxryb" jdbcType="VARCHAR" />-->
            <result column="DJRQ" property="djrq" jdbcType="TIMESTAMP"/>
            <result column="DJRY" property="djry" jdbcType="VARCHAR"/>
            <result column="PYDM" property="pydm" jdbcType="VARCHAR"/>
            <result column="SFZJLXMC" property="sfzjlxmc" jdbcType="VARCHAR"/>
            <result column="HYZKMC" property="hyzkmc" jdbcType="VARCHAR"/>
            <result column="ZYBMMC" property="zybmmc" jdbcType="VARCHAR"/>
            <!-- <result column="LXDH_LBDM" property="lxdh_lbdm" jdbcType="VARCHAR" />
            <result column="JKDABH" property="jkdabh" jdbcType="VARCHAR" /> -->
            <result column="BRGJMC" property="brgjmc" jdbcType="VARCHAR"/>
            <result column="BRMZMC" property="brmzmc" jdbcType="VARCHAR"/>
            <!-- <result column="jzd_XZQHMC" property="jzd_xzqhmc" jdbcType="VARCHAR" />
            <result column="jzd_SHENGMC" property="jzd_shengmc" jdbcType="VARCHAR" />
            <result column="jzd_SHIMC" property="jzd_shimc" jdbcType="VARCHAR" />
            <result column="jzd_XIANMC" property="jzd_xianmc" jdbcType="VARCHAR" />
            <result column="jzd_XIANGMC" property="jzd_xiangmc" jdbcType="VARCHAR" />
            <result column="jzd_JWHMC" property="jzd_jwhmc" jdbcType="VARCHAR" />
            <result column="hjd_XZQHMC" property="hjd_xzqhmc" jdbcType="VARCHAR" />
            <result column="hjd_SHENGMC" property="hjd_shengmc" jdbcType="VARCHAR" />
            <result column="hjd_SHIMC" property="hjd_shimc" jdbcType="VARCHAR" />
            <result column="hjd_XIANMC" property="hjd_xianmc" jdbcType="VARCHAR" />
            <result column="hjd_XIANGMC" property="hjd_xiangmc" jdbcType="VARCHAR" />
            <result column="hjd_JWHMC" property="hjd_jwhmc" jdbcType="VARCHAR" />
            <result column="SG" property="sg" jdbcType="DECIMAL" />
            <result column="TZ" property="tz" jdbcType="DECIMAL" />-->
            <result column="YLKLX" property="ylklx" jdbcType="VARCHAR"/>
            <result column="YLKH" property="ylkh" jdbcType="VARCHAR"/>
            <result column="brbah" property="bah" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <resultMap id="GznhResultMap" type="com.supx.csp.api.zygl.crygl.pojo.Gznh_qnz_bzff_xmbModel">
        <id column="XH" property="xh" jdbcType="VARCHAR"/>
        <result column="ICD" property="icd" jdbcType="VARCHAR"/>
        <result column="ICDMC" property="icdmc" jdbcType="VARCHAR"/>
        <result column="SSMC" property="ssmc" jdbcType="VARCHAR"/>
        <result column="SSBM" property="ssbm" jdbcType="VARCHAR"/>
        <result column="ZLSSMC" property="zlssmc" jdbcType="VARCHAR"/>
        <result column="XJXJ" property="xjxj" jdbcType="VARCHAR"/>
        <result column="STOP" property="stop" jdbcType="VARCHAR"/>
        <result column="SSMCDM" property="ssmcdm" jdbcType="VARCHAR"/>
        <result column="ZLSSMCDM" property="zlssmcdm" jdbcType="VARCHAR"/>
        <result column="YLJGBM" property="yljgbm" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ZYH, BAH, BRID, BXLBBM,BXLBMC, BRFB, BRFBMC, YWCKBH, YWCKMC, CZYBM, CZYXM, YBKH, ZYZT, RYKS,
    RYKSMC, RYRQ,RYDJRQ, CYRQ, BQCYBZ, BQCYRQ, MZYS, MZYSXM, MZKS, MZKSMC, ZYYS, ZYYSXM, RYQK,
    RYTJ, DBR, DBJE, BXBR, RYCWID, RYCWBH, NL, NLDW, YL, YLDW, RYZDBM, RYZDMC, IFZF,
    ZFRY, ZFRYXM, ZFRQ,PKHBZ,BQBM,BQMC,ICDBS,JJRJYY,ZZSXQQ, BYXSFPQK, ZLLB, FZBZ, FBDD, FBSJ,
    RYZYZDBM, RYZYZDMC, MZZDBM, MZZDMC, MZZYZDBM, MZZYZDMC,SFCR
    </sql>

    <!-- 查询出院病人信息（针对于病案借阅登记信息） -->
    <select id="selectByCybr" resultType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel"
            parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        select brjbxx.brxm,brjbxx.brxb,rydj.bah,rydj.nl,rydj.nldw,rydj.cyrq,rydj.cyksmc from zyb_rydj rydj
        inner join gyb_brjbxx brjbxx on rydj.brid=brjbxx.brid and rydj.yljgbm=brjbxx.yljgbm
        <where>
            rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            and rydj.ifzf='0' and rydj.zyzt='1'
            <if test="parm != null and parm != '' ">
                and (rydj.bah like '%'||#{parm,jdbcType=VARCHAR} or brjbxx.brxm like '%'||#{parm,jdbcType=VARCHAR}
                or lower(brjbxx.pydm) like '%'||lower(#{parm,jdbcType=VARCHAR})||'%' )
            </if>
            <if test="endrq != null ">
                and (rydj.cyrq &lt; #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (rydj.cyrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
        </where>
    </select>

    <!-- 根据住院号查询单条入院登记信息 -->
    <select id="selectByZyh" resultMap="BaseResultMap"
            parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        select decode(yzcysj, null, bqcyrq, yzcysj) bqcyrq,
               yljgbm,
               zyh,
               bah,
               brid,
               bxlbbm,
               bxlbmc,
               brfb,
               brfbmc,
               ywckbh,
               ywckmc,
               czybm,
               czyxm,
               ybkh,
               zyzt,
               ryks,
               ryksmc,
               ryrq,
               rydjrq,
               cyrq,
               bqcybz,
               mzys,
               mzysxm,
               mzks,
               mzksmc,
               zyys,
               zyysxm,
               ryqk,
               rytj,
               dbr,
               dbje,
               bxbr,
               rycwid,
               rycwbh,
               nl,
               nldw,
               nl2,
               nldw2,
               yl,
               yldw,
               ryzdbm,
               ryzdmc,
               ifzf,
               zfry,
               zfryxm,
               zfrq,
               cyks,
               cyksmc,
               zycs,
               pkhbz,
               bqbm,
               bqmc,
               sfcr,
               bzsm,
               ylklx,
               ylkh,
               icdbs,
               jjrjyy,
               zzsxqq,
               byxsfpqk,
               zllb,
               fzbz,
               fbdd,
               fbsj,
               ryzyzdbm,
               ryzyzdmc,
               mzzdbm,
               mzzdmc,
               mzzyzdbm,
               mzzyzdmc,
               zrhs,
               zrhsxm,
               yzcybz,
               yzcysj,
               ybcybz
        from zyb_rydj
        where ifzf = '0'
          and yljgbm = #{yljgbm,jdbcType=VARCHAR}
          and zyh = #{zyh,jdbcType=VARCHAR}
    </select>


    <!-- 根据住院号查询 -->
    <select id="queryByZyh" resultMap="RyxxResultMap" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        select rydj.zyh         zyh,
               rydj.bah         bah,
               rydj.brid        brid,
               rydj.bxlbbm      bxlbbm,
               rydj.bxlbmc      bxlbmc,
               rydj.brfb        brfb,
               rydj.brfbmc      brfbmc,
               rydj.ywckbh      ywckbh,
               rydj.ywckmc      ywckmc,
               rydj.czybm       czybm,
               rydj.czyxm       czyxm,
               rydj.ybkh        ybkh,
               rydj.zyzt        zyzt,
               rydj.ryks        ryks,
               rydj.ryksmc      ryksmc,
               rydj.ryrq        ryrq,
               rydj.rydjrq      rydjrq,
               rydj.cyrq        cyrq,
               rydj.bqcybz      bqcybz,
               rydj.bqcyrq      bqcyrq,
               rydj.mzys        mzys,
               rydj.mzysxm      mzysxm,
               rydj.mzks        mzks,
               rydj.mzksmc      mzksmc,
               rydj.zyys        zyys,
               rydj.zyysxm      zyysxm,
               rydj.ryqk        ryqk,
               rydj.rytj        rytj,
               rydj.dbr         dbr,
               rydj.dbje        dbje,
               rydj.bxbr        bxbr,
               rydj.rycwid      rycwid,
               rydj.rycwbh      rycwbh,
               rydj.nl          nl,
               rydj.nldw        nldw,
               rydj.yl          yl,
               rydj.yldw        yldw,
               rydj.ryzdbm      ryzdbm,
               rydj.ryzdmc      ryzdmc,
               rydj.ifzf        ifzf,
               rydj.zfry        zfry,
               rydj.zfryxm      zfryxm,
               rydj.zfrq        zfrq,
               rydj.cyks        cyks,
               rydj.cyksmc      cyksmc,
               rydj.zycs        zycs,
               rydj.ylklx       ylklx,
               rydj.ylkh        ylkh,
               rydj.ZZSXQQ      zzsxqq,
               rydj.BYXSFPQK    byxsfpqk,
               rydj.zllb,
               rydj.fzbz,
               rydj.fbdd,
               rydj.fbsj,
               rydj.ryzyzdbm,
               rydj.ryzyzdmc,
               rydj.mzzdbm,
               rydj.mzzdmc,
               rydj.mzzyzdbm,
               rydj.mzzyzdmc,
               rydj.sfcr,
               rydj.ybcybz,
               brjbxx.brid      brjbxxid,
               brjbxx.brxm      brxm,
               brjbxx.brxb      brxb,
               brjbxx.csrq      csrq,
               brjbxx.brgj      brgj,
               brjbxx.brgjmc    brgjmc,
               brjbxx.brmz      brmz,
               brjbxx.brmzmc    brmzmc,
               brjbxx.sfzjlx    sfzjlx,
               brjbxx.sfzjlxmc  sfzjlxm,
               brjbxx.sfzjhm    sfzjhm,
               brjbxx.hyzk      hyzk,
               brjbxx.hyzkmc    hyzkmc,
               brjbxx.zybm      zybm,
               brjbxx.zybmmc    zybmmc,
               brjbxx.hzlx      hzlx,
               brjbxx.csd       csd,
               brjbxx.sjhm      sjhm,
               brjbxx.jzdmc     jzdmc,
               brjbxx.djrq      djrq,
               brjbxx.djry      djry,
               brjbxx.pydm      pydm,
               brjbxx.lxrxm,
               brjbxx.lxrgx,
               brjbxx.lxrdh,
               brjbxx.jzd_sheng jzdsheng,
               brjbxx.jzd_shi   jzdshi,
               brjbxx.jzd_xian  jzdxian,
               rydj.mtbbz,
               rydj.djbh,
               rydj.bqcyrq,
               rydj.bqcybz

        from gyb_brjbxx brjbxx
                 inner join zyb_rydj rydj on rydj.brid = brjbxx.brid and rydj.yljgbm = brjbxx.yljgbm
        where rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
          and rydj.ifzf = '0'
          and rydj.zyh = #{zyh,jdbcType=VARCHAR}
    </select>
    <select id="queryByZyh2" resultMap="RyxxResultMap"
            parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        select jr.jrrq ryrq, rydj.zyh, rydj.yljgbm
        from zyb_rydj rydj
                 inner join zyb_jzkjl jr
                            on jr.zyh = rydj.zyh and jr.yljgbm = rydj.yljgbm
        where jr.jrbz = '1'
          and jr.zfbz = '0'
          and jr.jzklx = '0'
          and rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
          and rydj.ifzf = '0'
          and rydj.zyh = #{zyh,jdbcType=VARCHAR}
    </select>

    <!-- 根据病人id查询该病人所有入院次数 -->
    <select id="queryByBrid" resultMap="BaseResultMap"
            parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        SELECT
        <include refid="Base_Column_List"/>
        from ZYB_RYDJ
        where yljgbm = #{yljgbm,jdbcType=VARCHAR}
        and ifzf='0'
        <if test="brid != null">
            and BRID = #{brid,jdbcType=VARCHAR}
        </if>
        <if test="zyzt != null">
            and ZYZT = #{zyzt,jdbcType=VARCHAR}
        </if>
        <if test="ifzf != null">
            and IFZF = #{ifzf,jdbcType=VARCHAR}
        </if>
        <if test="bqcybz != null">
            and bqcybz = #{bqcybz,jdbcType=VARCHAR}
        </if>
        <if test="sfzjhm != null">
            and brid in (select brid from gyb_brjbxx where sfzjhm=#{sfzjhm})
        </if>
        <if test="gbxtbz != null">
            and gbxtbz = #{gbxtbz,jdbcType=VARCHAR}
        </if>
        order by ryrq desc
    </select>

    <!-- 添加页面检索 -->
    <select id="queryRydjJs" resultMap="RyxxResultMap"
            parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        select rydj.zyh zyh,rydj.bah bah,rydj.brid brid,rydj.bxlbbm bxlbbm,rydj.bxlbmc bxlbmc,rydj.brfb brfb,rydj.brfbmc
        brfbmc,
        rydj.ywckbh ywckbh,rydj.ywckmc ywckmc,rydj.czybm czybm,rydj.czyxm czyxm,case when rydj.ybkh is null then
        brjbxx.ybkh else rydj.ybkh end ybkh,rydj.zyzt zyzt,rydj.ryks ryks,
        rydj.ryksmc ryksmc,rydj.ryrq ryrq,rydj.rydjrq rydjrq,rydj.cyrq cyrq,rydj.bqcybz bqcybz,rydj.bqcyrq
        bqcyrq,rydj.mzys
        mzys,rydj.mzysxm mzysxm,
        rydj.mzks mzks,rydj.mzksmc mzksmc,rydj.zyys zyys,rydj.zyysxm zyysxm,rydj.ryqk ryqk,rydj.rytj rytj,rydj.dbr
        dbr,rydj.dbje dbje,
        rydj.bxbr bxbr,rydj.rycwid rycwid,rydj.rycwbh rycwbh,rydj.nl nl,rydj.nldw nldw,rydj.yl yl,
        rydj.yldw yldw,rydj.ryzdbm ryzdbm,rydj.ryzdmc ryzdmc,rydj.ifzf ifzf,rydj.zfry zfry,rydj.zfryxm zfryxm,rydj.zfrq
        zfrq,
        rydj.cyks cyks,rydj.cyksmc cyksmc,rydj.zycs zycs,rydj.ZZSXQQ zzsxqq,rydj.BYXSFPQK byxsfpqk,

        ylkxx.ylkh ylkh,ylkxx.ylklx ylklx,
        <!-- null ylkh,null ylklx, -->

        brjbxx.brid brjbxxid,brjbxx.brxm brxm,brjbxx.brxb brxb,brjbxx.csrq csrq,brjbxx.brgj brgj,
        brjbxx.brgjmc brgjmc,brjbxx.brmz brmz,brjbxx.brmzmc brmzmc,brjbxx.sfzjlx sfzjlx,brjbxx.sfzjlxmc
        sfzjlxm,brjbxx.sfzjhm sfzjhm,
        brjbxx.hyzk hyzk,brjbxx.hyzkmc hyzkmc,brjbxx.zybm zybm,brjbxx.zybmmc zybmmc,brjbxx.hzlx hzlx,brjbxx.csd
        csd,brjbxx.sjhm sjhm,
        brjbxx.jzdmc jzdmc,brjbxx.djrq djrq,brjbxx.djry djry,brjbxx.pydm
        pydm,brjbxx.lxrxm,brjbxx.lxrgx,brjbxx.lxrdh,brjbxx.jzd_sheng jzdsheng,
        brjbxx.jzd_shi jzdshi,brjbxx.jzd_xian jzdxian,ghxx.jbbm,ghxx.jbmc,ghxx.ghks mzks,ghxx.ghksmc mzksmc,ghxx.jzys
        mzys,ghxx.jzysxm mzysxm
        from gyb_brjbxx brjbxx
        inner join gyb_brylkxx ylkxx on ylkxx.brid=brjbxx.brid and ylkxx.lockzt='0' and ylkxx.yljgbm=brjbxx.yljgbm
        left join zyb_rydj rydj on rydj.brid=brjbxx.brid and rydj.zyzt='0' and rydj.ifzf='0'and
        rydj.yljgbm=brjbxx.yljgbm
        <if test="bqcybz != null and bqcybz != ''">
            and (rydj.bqcybz = #{bqcybz,jdbcType=VARCHAR})
        </if>
        <if test="gbxtbz != null and gbxtbz != ''">
            and (rydj.gbxtbz = #{gbxtbz,jdbcType=VARCHAR})
        </if>
        left join ghb_brgh ghxx on ghxx.brid=brjbxx.brid and ghxx.yljgbm=brjbxx.yljgbm
        <where>
            brjbxx.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="brid != null and brid != ''">
                and (brjbxx.brid = #{brid,jdbcType=VARCHAR})
            </if>
            <if test="sfzjlx != null and sfzjlx != ''">
                and (brjbxx.sfzjlx = #{sfzjlx,jdbcType=VARCHAR})
            </if>
            <if test="sfzjhm != null and sfzjhm != ''">
                and (upper(brjbxx.sfzjhm) = upper(#{sfzjhm,jdbcType=VARCHAR}))
            </if>
            <if test="ylklx != null and ylklx != ''">
                and (ylkxx.ylklx = #{ylklx,jdbcType=VARCHAR})
            </if>
            <if test="ylkh != null and ylkh != ''">
                <!-- and exists (select 1 from gyb_brylkxx ylk where ylk.brid=jbxx.brid and ylk.lockzt='0'
                    and (ylk.ylkh = #{ylkh,jdbcType=VARCHAR}) and (ylk.yljgbm = #{yljgbm,jdbcType=VARCHAR}) )-->
                and (ylkxx.ylkh = #{ylkh,jdbcType=VARCHAR})
            </if>
        </where>
    </select>


    <!-- 分页查询入院以及其关联信息 -->
    <select id="queryRydj" resultMap="RyxxResultMap" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        select rydj.zyh zyh,rydj.bah bah,rydj.brid brid,rydj.bxlbbm bxlbbm,rydj.bxlbmc bxlbmc,rydj.brfb brfb,rydj.brfbmc
        brfbmc,
        rydj.ywckbh ywckbh,rydj.ywckmc ywckmc,rydj.czybm czybm,rydj.czyxm czyxm,rydj.ybkh ybkh,rydj.zyzt zyzt,rydj.ryks
        ryks,
        rydj.ryksmc ryksmc,rydj.ryrq ryrq,rydj.rydjrq rydjrq,rydj.cyrq cyrq,rydj.bqcybz bqcybz,rydj.bqcyrq
        bqcyrq,rydj.mzys
        mzys,rydj.mzysxm mzysxm,
        rydj.mzks mzks,rydj.mzksmc mzksmc,rydj.zyys zyys,rydj.zyysxm zyysxm,rydj.ryqk ryqk,rydj.rytj rytj,rydj.dbr
        dbr,rydj.dbje dbje,
        rydj.bxbr bxbr,rydj.rycwid rycwid,rydj.rycwbh rycwbh,rydj.nl nl,rydj.nldw nldw,rydj.nl2 ,rydj.nldw2,rydj.yl yl,
        rydj.yldw yldw,rydj.ryzdbm ryzdbm,rydj.ryzdmc ryzdmc,rydj.ifzf ifzf,rydj.zfry zfry,rydj.zfryxm zfryxm,rydj.zfrq
        zfrq,
        rydj.cyks cyks,rydj.cyksmc cyksmc,rydj.zycs zycs,rydj.ZZSXQQ zzsxqq,rydj.BYXSFPQK byxsfpqk,rydj.sfcr,rydj.bzsm,
        brjbxx.brid brjbxxid,brjbxx.brxm brxm,brjbxx.brxb brxb,brjbxx.csrq csrq,brjbxx.brgj brgj,
        brjbxx.brgjmc brgjmc,brjbxx.brmz brmz,brjbxx.brmzmc brmzmc,brjbxx.sfzjlx sfzjlx,brjbxx.sfzjlxmc
        sfzjlxm,brjbxx.sfzjhm sfzjhm,
        brjbxx.hyzk hyzk,brjbxx.hyzkmc hyzkmc,brjbxx.zybm zybm,brjbxx.zybmmc zybmmc,brjbxx.hzlx hzlx,brjbxx.csd
        csd,brjbxx.sjhm sjhm,
        brjbxx.jzdmc jzdmc,brjbxx.djrq djrq,brjbxx.djry djry,brjbxx.pydm
        pydm,brjbxx.lxrxm,brjbxx.lxrgx,brjbxx.lxrdh,brjbxx.jzd_sheng jzdsheng,
        brjbxx.jzd_shi jzdshi,brjbxx.jzd_xian jzdxian,(nvl(yjje,0)-nvl(fyje,0))
        zhye,rydj.ylkh,ylk.ylklx,rydj.mtbbz,rydj.djbh,brjbxx.bah brbah,yb.rybh aac001

        from zyb_rydj rydj
        inner join gyb_brjbxx brjbxx on brjbxx.brid=rydj.brid and brjbxx.yljgbm=rydj.yljgbm
        inner join gyb_brylkxx ylk on rydj.ylkh = ylk.ylkh and rydj.yljgbm = ylk.yljgbm
        left join gssldyb_zydjxx yb on yb.zyh = rydj.zyh
        left join
        (select sum(fyje) fyje,zyh,yljgbm from ZYB_BRFY where zfbz='0' and yxbz='1' group by zyh,yljgbm) fy
        on rydj.zyh = fy.zyh and rydj.yljgbm =fy.yljgbm
        left join (select sum(yjje) yjje,zyh,yljgbm from ZYB_YJJL group by zyh,yljgbm) yj
        on rydj.zyh = yj.zyh and rydj.yljgbm = yj.yljgbm
        <where>
            rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            and rydj.ifzf='0'
            <if test="parm != null and parm != '' ">
                <choose>
                    <when test="wbjm == '1'.toString()">
                        and (rydj.zyh like '%'||#{parm,jdbcType=VARCHAR} ||'%' or brjbxx.brxm like
                        '%'||#{parm,jdbcType=VARCHAR} ||'%'
                        or lower(brjbxx.wbjm) like '%'||lower(#{parm,jdbcType=VARCHAR})||'%' or brjbxx.sjhm like '%'||
                        #{parm,jdbcType=VARCHAR}||'%'
                        or brjbxx.sfzjhm like '%'|| #{parm,jdbcType=VARCHAR}||'%')
                    </when>
                    <otherwise>
                        and (rydj.zyh like '%'||#{parm,jdbcType=VARCHAR} ||'%' or brjbxx.brxm like
                        '%'||#{parm,jdbcType=VARCHAR} ||'%'
                        or lower(brjbxx.pydm) like '%'||lower(#{parm,jdbcType=VARCHAR})||'%' or brjbxx.sjhm like '%'||
                        #{parm,jdbcType=VARCHAR}||'%'
                        or brjbxx.sfzjhm like '%'|| #{parm,jdbcType=VARCHAR}||'%')
                    </otherwise>
                </choose>
            </if>
            <if test="jssx == 4 ">
                and (brjbxx.sfzjhm like '%'|| #{jsvalue,jdbcType=VARCHAR}||'%')
            </if>
            <if test="jssx == 3 ">
                and (brjbxx.sjhm like '%'|| #{jsvalue,jdbcType=VARCHAR}||'%')
            </if>
            <if test="jssx == 2 ">
                and (rydj.zyh like '%'|| #{jsvalue,jdbcType=VARCHAR}||'%')
            </if>
            <if test="jssx == 5 ">
                and (brjbxx.brxm like '%'|| #{jsvalue,jdbcType=VARCHAR}||'%')
            </if>
            <if test="zyzt != null">
                and rydj.ZYZT = #{zyzt,jdbcType=VARCHAR}
            </if>
            <if test="zyh != null">
                and rydj.zyh = #{zyh,jdbcType=VARCHAR}
            </if>
            <if test="brid != null">
                and brjbxx.brid = #{brid,jdbcType=VARCHAR}
            </if>
            <if test="endrq != null ">
                and (rydj.${cxrq} &lt; #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (rydj.${cxrq} &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="bqcybz != null">
                and rydj.bqcybz = #{bqcybz,jdbcType=VARCHAR}
            </if>
            <if test="ryks !=null and ryks != '' ">
                and rydj.ryks = #{ryks,jdbcType=VARCHAR}
            </if>
            <if test="gbxtbz !=null and gbxtbz != '' ">
                and rydj.gbxtbz = #{gbxtbz,jdbcType=VARCHAR}
            </if>
        </where>
        <if test="sort1 !=null ">
            order by rydj.ryrq
            <if test="order1 !=null">
                desc
            </if>
            <if test="order2 !=null">
                asc
            </if>
        </if>
        <if test="sort2 !=null ">
            order by rydj.ryks
            <if test="order1 !=null">
                desc
            </if>
            <if test="order2 !=null">
                asc
            </if>
        </if>
    </select>

    <select id="queryMzry" resultType="com.supx.csp.api.ghgl.ghyw.pojo.Gyb_brjbxxModel"
            parameterType="com.supx.csp.api.ghgl.ghyw.pojo.Gyb_brjbxxModel">
        select
        a.BRID, a.BRXM, a.BRXB, a.CSRQ, a.BRGJ, a.BRMZ, a.SFZJLX, a.SFZJHM, a.HYZK, a.ZYBM, a.HZLX, a.CSD, a.SJHM,
        a.GZDW, a.DWDZ, a.DWYB, a.jzd_XZQH, a.jzd_SHENG, a.jzd_SHI, a.jzd_XIAN, a.jzd_XIANG, a.jzd_JWH, a.jzd_CUN,
        a.jzd_NONG, a.jzd_LH, a.jzd_MPH, a.jzdMc, a.hjd_XZQH, a.hjd_SHENG, a.hjd_SHI, a.hjd_XIAN, a.hjd_XIANG,
        a.hjd_JWH, a.hjd_CUN, a.hjd_NONG, a.hjd_LH, a.hjd_MPH, a.HKDZ, a.HKDYB, a.LXRXM, a.LXRGX, a.LXRDZ, a.LXRDW,
        a.LXRYB, a.LXRDH, a.DJRQ, a.DJRY, a.PYDM, a.SFZJLXMC, a.HYZKMC, a.ZYBMMC, a.LXDH_LBDM, a.JKDABH, a.BRGJMC,
        a.BRMZMC, a.jzd_XZQHMC, a.jzd_SHENGMC, a.jzd_SHIMC, a.jzd_XIANMC, a.jzd_XIANGMC, a.jzd_JWHMC, a.hjd_XZQHMC,
        a.hjd_SHENGMC, a.hjd_SHIMC, a.hjd_XIANMC, a.hjd_XIANGMC, a.hjd_JWHMC, a.SG, a.TZ,
        rybm.ryxm mzysxm,ksbm.ksmc mzksmc,case when mzry.ryghxh is null then '0' else '1' end ismzry,
        mzry.cbzd,mzry.mzys,mzry.mzks,jbbm.jbmc cbzdmc,mzry.ryghxh ghxh,mzry.zyks,mzry.yjje
        from GYB_BRJBXX a
        inner join mzys_mzry mzry on mzry.rybrid=a.brid and mzry.yljgbm=a.yljgbm
        left join gyb_rybm rybm on rybm.rybm=mzry.mzys and rybm.yljgbm=mzry.yljgbm
        left join gyb_ksbm ksbm on ksbm.ksbm=mzry.mzks and ksbm.yljgbm=mzry.yljgbm
        left join bagl_jbbm jbbm on jbbm.jbmb=mzry.cbzd
        <where>
            a.YLJGBM = #{yljgbm,jdbcType=VARCHAR}
            <if test="parm != null and parm != '' ">
                and (a.BRXM like '%'||#{parm,jdbcType=VARCHAR}||'%'
                OR UPPER(a.PYDM) LIKE '%'||#{parm,jdbcType=VARCHAR}||'%' or exists (select 1 from gyb_brylkxx ylk
                where ylk.brid=a.brid and ylk.yljgbm=a.yljgbm and ylk.lockzt='0'
                and (ylk.ylkh = #{parm,jdbcType=VARCHAR}))
                )
            </if>
            <if test="hzlx != null and hzlx != '' ">
                and (a.HZLX = #{hzlx,jdbcType=VARCHAR})
            </if>
            <if test="sjhm != null and sjhm != ''">
                and (a.SJHM = #{sjhm,jdbcType=VARCHAR})
            </if>
            <if test="hyzk != null and hyzk != ''">
                and (a.HYZK = #{hyzk,jdbcType=VARCHAR})
            </if>
            <if test="sfzjlx != null and sfzjlx != ''">
                and (a.SFZJLX = #{sfzjlx,jdbcType=VARCHAR})
            </if>
            <if test="sfzjhm != null and sfzjhm != ''">
                and (a.SFZJHM = #{sfzjhm,jdbcType=VARCHAR})
            </if>
            <if test="hyzk != null and hyzk != ''">
                and (a.HYZK = #{hyzk,jdbcType=VARCHAR})
            </if>
            <if test="zybm != null and zybm != ''">
                and (a.ZYBM = #{zybm,jdbcType=VARCHAR})
            </if>
            <if test="gzdw != null and gzdw != ''">
                and (a.GZDW like '%'||#{gzdw,jdbcType=VARCHAR}||'%')
            </if>
            <if test="dwdz != null and dwdz != ''">
                and (a.DWDZ like '%'||#{dwdz,jdbcType=VARCHAR}||'%')
            </if>
            <if test="jzdxzqh != null and jzdxzqh != ''">
                and (a.JZD_XZQH = #{jzdxzqh,jdbcType=VARCHAR})
            </if>
            <if test="jzdsheng != null and jzdsheng != ''">
                and (a.JZD_SHENG = #{jzdsheng,jdbcType=VARCHAR})
            </if>
            <if test="jzdshi != null and jzdshi != ''">
                and (a.JZD_SHI = #{jzdshi,jdbcType=VARCHAR})
            </if>
            <if test="jzdxian != null and jzdxian != ''">
                and (a.JZD_XIAN = #{jzdxian,jdbcType=VARCHAR})
            </if>
            <if test="jzdxiang != null and jzdxiang != ''">
                and (a.JZD_XIANG = #{jzdxiang,jdbcType=VARCHAR})
            </if>
            <if test="jzdmc != null and jzdmc != ''">
                and (a.JZDMC = '%'||#{jzdmc,jdbcType=VARCHAR}||'%')
            </if>
            <if test="hjdxzqh != null and hjdxzqh != ''">
                and (a.HJD_XZQH = #{hjdxzqh,jdbcType=VARCHAR})
            </if>
            <if test="hjdsheng != null and hjdsheng != ''">
                and (a.HJD_SHENG = #{hjdsheng,jdbcType=VARCHAR})
            </if>
            <if test="hjdshi != null and hjdshi != ''">
                and (a.HJD_SHI = #{hjdshi,jdbcType=VARCHAR})
            </if>
            <if test="hjdxian != null and hjdxian != ''">
                and (a.HJD_XIAN = #{hjdxian,jdbcType=VARCHAR})
            </if>
            <if test="hjdxiang != null and hjdxiang != ''">
                and (a.HJD_XIANG = #{hjdxiang,jdbcType=VARCHAR})
            </if>
            <if test="hkdz != null and hkdz != ''">
                and (a.HKDZ = #{hkdz,jdbcType=VARCHAR})
            </if>
            <if test="hkdyb != null and hkdyb != ''">
                and (a.HKDYB = #{hkdyb,jdbcType=VARCHAR})
            </if>
            <if test="lxrxm != null and lxrxm != ''">
                and (a.LXRXM = #{lxrxm,jdbcType=VARCHAR})
            </if>
            <if test="lxrdh != null and lxrdh != ''">
                and (a.LXRDH = #{lxrdh,jdbcType=VARCHAR})
            </if>
            <if test="sjhm != null and sjhm != ''">
                and (a.SJHM = #{sjhm,jdbcType=VARCHAR})
            </if>
            <if test="endrq != null ">
                and (a.DJRQ &lt;= #{endrq,jdbcType=VARCHAR})
            </if>
            <if test="beginrq != null ">
                and (a.DJRQ &gt;= #{beginrq,jdbcType=VARCHAR})
            </if>
            <if test="brid != null and brid != '' ">
                and a.BRID = #{brid,jdbcType=VARCHAR}
            </if>
            <if test="ylkh != null and ylkh != ''">
                and (exists (select 1 from gyb_brylkxx where a.brid = gyb_brylkxx.brid
                and YLKH = #{ylkh,jdbcType=VARCHAR} and YLJGBM = #{yljgbm,jdbcType=VARCHAR} ))
            </if>
        </where>
        ORDER BY ${sort} ${order}
    </select>

    <!-- 批量取消入院 -->
    <update id="deletebatchRydj" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            update ZYB_RYDJ
            <set>
                ifzf='1',zyh='-'||#{item.zyh,jdbcType=VARCHAR},
                <if test="item.zfry != null">
                    zfry = #{item.zfry,jdbcType=VARCHAR},
                </if>
                <if test="item.zfryxm != null">
                    zfryxm = #{item.zfryxm,jdbcType=VARCHAR},
                </if>
                <if test="item.zfrq != null">
                    zfrq = #{item.zfrq,jdbcType=TIMESTAMP}
                </if>
            </set>
            where yljgbm = #{item.yljgbm,jdbcType=VARCHAR}
            and zyh=#{item.zyh,jdbcType=VARCHAR}
        </foreach>
    </update>

    <!--新增入院信息  -->
    <insert id="insertRydj" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        insert into ZYB_RYDJ
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="zyh != null">
                ZYH,
            </if>
            <if test="bah != null">
                BAH,
            </if>
            <if test="brid != null">
                BRID,
            </if>
            <if test="bxlbbm != null">
                BXLBBM,
            </if>
            <if test="bxlbmc != null">
                BXLBMC,
            </if>
            <if test="brfb != null">
                BRFB,
            </if>
            <if test="brfbmc != null">
                BRFBMC,
            </if>
            <if test="ywckbh != null">
                YWCKBH,
            </if>
            <if test="ywckmc != null">
                YWCKMC,
            </if>
            <if test="czybm != null">
                CZYBM,
            </if>
            <if test="czyxm != null">
                CZYXM,
            </if>
            <if test="ybkh != null">
                YBKH,
            </if>
            <if test="zyzt != null">
                ZYZT,
            </if>
            <if test="ryks != null">
                RYKS,
            </if>
            <if test="ryksmc != null">
                RYKSMC,
            </if>
            <if test="ryrq != null">
                RYRQ,
            </if>
            <if test="rydjrq != null">
                RYDJRQ,
            </if>
            <if test="cyrq != null">
                CYRQ,
            </if>
            <if test="bqcybz != null">
                BQCYBZ,
            </if>
            <if test="bqcyrq != null">
                BQCYRQ,
            </if>
            <if test="mzys != null">
                MZYS,
            </if>
            <if test="mzysxm != null">
                MZYSXM,
            </if>
            <if test="mzks != null">
                MZKS,
            </if>
            <if test="mzksmc != null">
                MZKSMC,
            </if>
            <if test="zyys != null">
                ZYYS,
            </if>
            <if test="zyysxm != null">
                ZYYSXM,
            </if>
            <if test="ryqk != null">
                RYQK,
            </if>
            <if test="rytj != null">
                RYTJ,
            </if>
            <if test="dbr != null">
                DBR,
            </if>
            <if test="dbje != null">
                DBJE,
            </if>
            <if test="bxbr != null">
                BXBR,
            </if>
            <if test="rycwid != null">
                RYCWID,
            </if>
            <if test="rycwbh != null">
                RYCWBH,
            </if>
            <if test="nl != null">
                NL,
            </if>
            <if test="nldw != null">
                NLDW,
            </if>
            <if test="nl2 != null">
                NL2,
            </if>
            <if test="nldw2 != null">
                NLDW2,
            </if>
            <if test="yl != null">
                YL,
            </if>
            <if test="yldw != null">
                YLDW,
            </if>
            <if test="ryzdbm != null">
                RYZDBM,
            </if>
            <if test="ryzdmc != null">
                RYZDMC,
            </if>
            <if test="ifzf != null">
                IFZF,
            </if>
            <if test="zfry != null">
                ZFRY,
            </if>
            <if test="zfryxm != null">
                ZFRYXM,
            </if>
            <if test="zfrq != null">
                ZFRQ,
            </if>
            <if test="cyks != null">
                CYKS,
            </if>
            <if test="cyksmc != null">
                CYKSMC,
            </if>
            <if test="zycs != null">
                ZYCS,
            </if>
            <if test="ylklx != null">
                YLKLX,
            </if>
            <if test="ylkh != null">
                YLKH,
            </if>
            <if test="yljgbm != null">
                YLJGBM,
            </if>

            <if test="pkhbz != null">
                PKHBZ,
            </if>
            <if test="bqbm != null">
                BQBM,
            </if>
            <if test="bqmc != null">
                BQMC,
            </if>
            <if test="icdbs != null">
                ICDBS,
            </if>
            <if test="jjrjyy != null">
                JJRJYY,
            </if>
            <if test="zzsxqq != null">
                ZZSXQQ,
            </if>
            <if test="byxsfpqk != null">
                BYXSFPQK,
            </if>
            <if test="zllb != null">
                ZLLB,
            </if>
            <if test="fzbz != null">
                FZBZ,
            </if>
            <if test="fbdd != null">
                FBDD,
            </if>
            <if test="fbsj != null">
                FBSJ,
            </if>
            <if test="ryzyzdbm != null">
                RYZYZDBM,
            </if>
            <if test="ryzyzdmc != null">
                RYZYZDMC,
            </if>
            <if test="mzzdbm != null">
                MZZDBM,
            </if>
            <if test="mzzdmc != null">
                MZZDMC,
            </if>
            <if test="mzzyzdbm != null">
                MZZYZDBM,
            </if>
            <if test="mzzyzdmc != null">
                MZZYZDMC,
            </if>
            <if test="sfcr != null">
                SFCR,
            </if>
            <if test="bzsm != null">
                BZSM,
            </if>
            <if test="isvirtry != null">
                ISVIRTRY,
            </if>
            <if test="mtbbz != null">
                MTBBZ,
            </if>
            <if test="djbh != null">
                DJBH,
            </if>
            <if test="gbxtbz != null">
                gbxtbz,
            </if>
            <if test="mdtrtGrpType != null">
                mdtrt_Grp_Type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="zyh != null">
                #{zyh,jdbcType=VARCHAR},
            </if>
            <if test="bah != null">
                #{bah,jdbcType=VARCHAR},
            </if>
            <if test="brid != null">
                #{brid,jdbcType=VARCHAR},
            </if>
            <if test="bxlbbm != null">
                #{bxlbbm,jdbcType=VARCHAR},
            </if>
            <if test="bxlbmc != null">
                #{bxlbmc,jdbcType=VARCHAR},
            </if>
            <if test="brfb != null">
                #{brfb,jdbcType=VARCHAR},
            </if>
            <if test="brfbmc != null">
                #{brfbmc,jdbcType=VARCHAR},
            </if>
            <if test="ywckbh != null">
                #{ywckbh,jdbcType=VARCHAR},
            </if>
            <if test="ywckmc != null">
                #{ywckmc,jdbcType=VARCHAR},
            </if>
            <if test="czybm != null">
                #{czybm,jdbcType=VARCHAR},
            </if>
            <if test="czyxm != null">
                #{czyxm,jdbcType=VARCHAR},
            </if>
            <if test="ybkh != null">
                #{ybkh,jdbcType=VARCHAR},
            </if>
            <if test="zyzt != null">
                #{zyzt,jdbcType=VARCHAR},
            </if>
            <if test="ryks != null">
                #{ryks,jdbcType=VARCHAR},
            </if>
            <if test="ryksmc != null">
                #{ryksmc,jdbcType=VARCHAR},
            </if>
            <if test="ryrq != null">
                #{ryrq,jdbcType=TIMESTAMP},
            </if>
            <if test="rydjrq != null">
                #{rydjrq,jdbcType=TIMESTAMP},
            </if>
            <if test="cyrq != null">
                #{cyrq,jdbcType=TIMESTAMP},
            </if>
            <if test="bqcybz != null">
                #{bqcybz,jdbcType=VARCHAR},
            </if>
            <if test="bqcyrq != null">
                #{bqcyrq,jdbcType=TIMESTAMP},
            </if>
            <if test="mzys != null">
                #{mzys,jdbcType=VARCHAR},
            </if>
            <if test="mzysxm != null">
                #{mzysxm,jdbcType=VARCHAR},
            </if>
            <if test="mzks != null">
                #{mzks,jdbcType=VARCHAR},
            </if>
            <if test="mzksmc != null">
                #{mzksmc,jdbcType=VARCHAR},
            </if>
            <if test="zyys != null">
                #{zyys,jdbcType=VARCHAR},
            </if>
            <if test="zyysxm != null">
                #{zyysxm,jdbcType=VARCHAR},
            </if>
            <if test="ryqk != null">
                #{ryqk,jdbcType=VARCHAR},
            </if>
            <if test="rytj != null">
                #{rytj,jdbcType=VARCHAR},
            </if>
            <if test="dbr != null">
                #{dbr,jdbcType=VARCHAR},
            </if>
            <if test="dbje != null">
                #{dbje,jdbcType=DECIMAL},
            </if>
            <if test="bxbr != null">
                #{bxbr,jdbcType=VARCHAR},
            </if>
            <if test="rycwid != null">
                #{rycwid,jdbcType=VARCHAR},
            </if>
            <if test="rycwbh != null">
                #{rycwbh,jdbcType=VARCHAR},
            </if>
            <if test="nl != null">
                #{nl,jdbcType=DECIMAL},
            </if>
            <if test="nldw != null">
                #{nldw,jdbcType=VARCHAR},
            </if>
            <if test="nl2 != null">
                #{nl2,jdbcType=DECIMAL},
            </if>
            <if test="nldw2 != null">
                #{nldw2,jdbcType=VARCHAR},
            </if>
            <if test="yl != null">
                #{yl,jdbcType=DECIMAL},
            </if>
            <if test="yldw != null">
                #{yldw,jdbcType=VARCHAR},
            </if>
            <if test="ryzdbm != null">
                #{ryzdbm,jdbcType=VARCHAR},
            </if>
            <if test="ryzdmc != null">
                #{ryzdmc,jdbcType=VARCHAR},
            </if>
            <if test="ifzf != null">
                #{ifzf,jdbcType=VARCHAR},
            </if>
            <if test="zfry != null">
                #{zfry,jdbcType=VARCHAR},
            </if>
            <if test="zfryxm != null">
                #{zfryxm,jdbcType=VARCHAR},
            </if>
            <if test="zfrq != null">
                #{zfrq,jdbcType=TIMESTAMP},
            </if>
            <if test="cyks != null">
                #{cyks,jdbcType=VARCHAR},
            </if>
            <if test="cyksmc != null">
                #{cyksmc,jdbcType=VARCHAR},
            </if>
            <if test="zycs != null">
                #{zycs,jdbcType=DECIMAL},
            </if>
            <if test="ylklx != null">
                #{ylklx,jdbcType=VARCHAR},
            </if>
            <if test="ylkh != null">
                #{ylkh,jdbcType=VARCHAR},
            </if>
            <if test="yljgbm != null">
                #{yljgbm,jdbcType=VARCHAR},
            </if>
            <if test="pkhbz != null">
                #{pkhbz,jdbcType=VARCHAR},
            </if>
            <if test="bqbm != null">
                #{bqbm,jdbcType=VARCHAR},
            </if>
            <if test="bqmc != null">
                #{bqmc,jdbcType=VARCHAR},
            </if>
            <if test="icdbs != null">
                #{icdbs,jdbcType=VARCHAR},
            </if>
            <if test="jjrjyy != null">
                #{jjrjyy,jdbcType=VARCHAR},
            </if>
            <if test="zzsxqq != null">
                #{zzsxqq,jdbcType=VARCHAR},
            </if>
            <if test="byxsfpqk != null">
                #{byxsfpqk,jdbcType=VARCHAR},
            </if>
            <if test="zllb != null">
                #{zllb,jdbcType=VARCHAR},
            </if>
            <if test="fzbz != null">
                #{fzbz,jdbcType=VARCHAR},
            </if>
            <if test="fbdd != null">
                #{fbdd,jdbcType=VARCHAR},
            </if>
            <if test="fbsj != null">
                #{fbsj,jdbcType=TIMESTAMP},
            </if>
            <if test="ryzyzdbm != null">
                #{ryzyzdbm,jdbcType=VARCHAR},
            </if>
            <if test="ryzyzdmc != null">
                #{ryzyzdmc,jdbcType=VARCHAR},
            </if>
            <if test="mzzdbm != null">
                #{mzzdbm,jdbcType=VARCHAR},
            </if>
            <if test="mzzdmc != null">
                #{mzzdmc,jdbcType=VARCHAR},
            </if>
            <if test="mzzyzdbm != null">
                #{mzzyzdbm,jdbcType=VARCHAR},
            </if>
            <if test="mzzyzdmc != null">
                #{mzzyzdmc,jdbcType=VARCHAR},
            </if>
            <if test="sfcr != null">
                #{sfcr,jdbcType=VARCHAR},
            </if>
            <if test="bzsm != null">
                #{bzsm,jdbcType=VARCHAR},
            </if>
            <if test="isvirtry != null">
                #{isvirtry,jdbcType=VARCHAR},
            </if>
            <if test="mtbbz != null">
                #{mtbbz,jdbcType=VARCHAR},
            </if>
            <if test="djbh != null">
                #{djbh,jdbcType=VARCHAR},
            </if>
            <if test="gbxtbz != null">
                #{gbxtbz,jdbcType=VARCHAR},
            </if>
            <if test="mdtrtGrpType != null">
                #{mdtrtGrpType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <!-- 急症科分诊-->
    <!-- 保存MERGE INTO -->
    <insert id="updateJzkfz" parameterType="com.supx.csp.api.zygl.crygl.pojo.Jzk_fzdjModel">
        MERGE INTO jzk_fzgl T1
        USING
            (SELECT #{yljgbm,jdbcType=VARCHAR} YLJGBM,
                    #{brid,jdbcType=VARCHAR}   brid,
                    #{lyfsbm,jdbcType=VARCHAR} lyfsbm,
                    #{fzjbbm,jdbcType=VARCHAR} fzjbbm,
                    #{fzkbbm,jdbcType=VARCHAR} fzkbbm,
                    #{tw,jdbcType=VARCHAR}     tw,
                    #{mb,jdbcType=VARCHAR}     mb,
                    #{hx,jdbcType=VARCHAR}     hx,
                    #{xy,jdbcType=VARCHAR}     xy,
                    #{xl,jdbcType=VARCHAR}     xl,
                    #{xtms,jdbcType=VARCHAR}   xtms,
                    #{xt,jdbcType=VARCHAR}     xt,
                    #{xtong,jdbcType=VARCHAR}  xtong,
                    #{zglxbm,jdbcType=VARCHAR} zglxbm,
                    #{rybm,jdbcType=VARCHAR}   rybm,
                    #{ghxh,jdbcType=VARCHAR}   ghxh,
                    #{bqms,jdbcType=VARCHAR}   bqms,
                    #{bz,jdbcType=VARCHAR}     bz
             FROM DUAL) T2
        ON (T1.YLJGBM = T2.YLJGBM and T1.ghxh = T2.ghxh)
        WHEN MATCHED THEN
            UPDATE
            SET T1.brid   = T2.brid,
                T1.lyfsbm = T2.lyfsbm,
                T1.fzjbbm = T2.fzjbbm,
                T1.fzkbbm = T2.fzkbbm,
                T1.tw     = T2.tw,
                T1.mb     = T2.mb,
                T1.hx     = T2.hx,
                T1.xy     = T2.xy,
                T1.xl     = T2.xl,
                T1.xtms   = T2.xtms,
                T1.xt     = T2.xt,
                T1.xtong  = T2.xtong,
                T1.zglxbm = T2.zglxbm,
                T1.rybm   = T2.rybm,
                T1.bqms   = T2.bqms,
                T1.bz     = T2.bz
        WHEN NOT MATCHED THEN
            insert (YLJGBM, brid, lyfsbm, fzjbbm, fzkbbm, tw, mb, hx, xy, zglxbm, rybm, ghxh, bqms, bz)
            values (T2.YLJGBM, T2.brid, T2.lyfsbm, T2.fzjbbm, T2.fzkbbm, T2.tw, T2.mb, T2.hx, T2.xy, T2.zglxbm, T2.rybm,
                    T2.ghxh, T2.bqms, T2.bz)
    </insert>

    <!-- 修改入院登记信息 （修改成功后需 保存修改记录[gyb_brjbxx_xgjl]）-->
    <update id="updateRydj" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        update ZYB_RYDJ
        <set>
            <if test="bah != null">
                BAH = #{bah,jdbcType=VARCHAR},
            </if>
            <if test="brid != null">
                BRID = #{brid,jdbcType=VARCHAR},
            </if>
            <if test="bxlbbm != null">
                BXLBBM = #{bxlbbm,jdbcType=VARCHAR},
            </if>
            <if test="bxlbmc != null">
                BXLBMC = #{bxlbmc,jdbcType=VARCHAR},
            </if>
            <if test="brfb != null">
                BRFB = #{brfb,jdbcType=VARCHAR},
            </if>
            <if test="brfbmc != null">
                BRFBMC = #{brfbmc,jdbcType=VARCHAR},
            </if>
            <if test="ywckbh != null">
                YWCKBH = #{ywckbh,jdbcType=VARCHAR},
            </if>
            <if test="ywckmc != null">
                YWCKMC = #{ywckmc,jdbcType=VARCHAR},
            </if>
            <if test="czybm != null">
                CZYBM = #{czybm,jdbcType=VARCHAR},
            </if>
            <if test="czyxm != null">
                CZYXM = #{czyxm,jdbcType=VARCHAR},
            </if>
            <if test="ybkh != null">
                YBKH = #{ybkh,jdbcType=VARCHAR},
            </if>
            <if test="zyzt != null">
                ZYZT = #{zyzt,jdbcType=VARCHAR},
            </if>
            <if test="ryks != null">
                RYKS = #{ryks,jdbcType=VARCHAR},
            </if>
            <if test="ryksmc != null">
                RYKSMC = #{ryksmc,jdbcType=VARCHAR},
            </if>
            <if test="ryrq != null">
                RYRQ = #{ryrq,jdbcType=TIMESTAMP},
            </if>
            <if test="rydjrq != null">
                RYDJRQ = #{rydjrq,jdbcType=TIMESTAMP},
            </if>
            <if test="cyrq != null">
                CYRQ = #{cyrq,jdbcType=TIMESTAMP},
            </if>
            <if test="bqcybz != null">
                BQCYBZ = #{bqcybz,jdbcType=VARCHAR},
            </if>
            <if test="bqcyrq != null">
                BQCYRQ = #{bqcyrq,jdbcType=TIMESTAMP},
            </if>
            <if test="bqcybz == '0'.toString() and bqcyrq == null">
                BQCYRQ = null,
            </if>
            <if test="mzys != null">
                MZYS = #{mzys,jdbcType=VARCHAR},
            </if>
            <if test="mzysxm != null">
                MZYSXM = #{mzysxm,jdbcType=VARCHAR},
            </if>
            <if test="mzks != null">
                MZKS = #{mzks,jdbcType=VARCHAR},
            </if>
            <if test="mzksmc != null">
                MZKSMC = #{mzksmc,jdbcType=VARCHAR},
            </if>
            <if test="zyys != null">
                ZYYS = #{zyys,jdbcType=VARCHAR},
            </if>
            <if test="zyysxm != null">
                ZYYSXM = #{zyysxm,jdbcType=VARCHAR},
            </if>
            <if test="ryqk != null">
                RYQK = #{ryqk,jdbcType=VARCHAR},
            </if>
            <if test="rytj != null">
                RYTJ = #{rytj,jdbcType=VARCHAR},
            </if>
            <if test="dbr != null">
                DBR = #{dbr,jdbcType=VARCHAR},
            </if>
            <if test="dbje != null">
                DBJE = #{dbje,jdbcType=DECIMAL},
            </if>
            <if test="bxbr != null">
                BXBR = #{bxbr,jdbcType=VARCHAR},
            </if>
            <if test="rycwid != null">
                RYCWID = #{rycwid,jdbcType=VARCHAR},
            </if>
            <if test="rycwbh != null">
                RYCWBH = #{rycwbh,jdbcType=VARCHAR},
            </if>
            <if test="nl != null">
                NL = #{nl,jdbcType=DECIMAL},
            </if>
            <if test="nldw != null">
                NLDW = #{nldw,jdbcType=VARCHAR},
            </if>
            <if test="nl2 != null">
                NL2 = #{nl2,jdbcType=DECIMAL},
            </if>
            <if test="nldw2 != null">
                NLDW2 = #{nldw2,jdbcType=VARCHAR},
            </if>
            <if test="yl != null">
                YL = #{yl,jdbcType=DECIMAL},
            </if>
            <if test="yldw != null">
                YLDW = #{yldw,jdbcType=VARCHAR},
            </if>
            <if test="ryzdbm != null">
                RYZDBM = #{ryzdbm,jdbcType=VARCHAR},
            </if>
            <if test="ryzdmc != null">
                RYZDMC = #{ryzdmc,jdbcType=VARCHAR},
            </if>
            <if test="ifzf != null">
                IFZF = #{ifzf,jdbcType=VARCHAR},
            </if>
            <if test="zfry != null">
                ZFRY = #{zfry,jdbcType=VARCHAR},
            </if>
            <if test="zfryxm != null">
                ZFRYXM = #{zfryxm,jdbcType=VARCHAR},
            </if>
            <if test="zfrq != null">
                ZFRQ = #{zfrq,jdbcType=TIMESTAMP},
            </if>
            <if test="cyks != null">
                CYKS = #{cyks,jdbcType=VARCHAR},
            </if>
            <if test="cyksmc != null">
                CYKSMC = #{cyksmc,jdbcType=VARCHAR},
            </if>
            <if test="zycs != null and zycs != ''">
                ZYCS = #{zycs,jdbcType=DECIMAL},
            </if>
            <if test="pkhbz != null">
                PKHBZ = #{pkhbz,jdbcType=VARCHAR},
            </if>
            <if test="bqbm != null">
                BQBM = #{bqbm,jdbcType=VARCHAR},
            </if>
            <if test="bqmc != null">
                BQMC = #{bqmc,jdbcType=VARCHAR},
            </if>
            <if test="icdbs != null">
                ICDBS = #{icdbs,jdbcType=VARCHAR},
            </if>
            <if test="jjrjyy != null">
                JJRJYY = #{jjrjyy,jdbcType=VARCHAR},
            </if>
            <if test="zzsxqq != null">
                ZZSXQQ = #{zzsxqq,jdbcType=VARCHAR},
            </if>
            <if test="byxsfpqk != null">
                BYXSFPQK = #{byxsfpqk,jdbcType=VARCHAR},
            </if>
            <if test="zllb != null">
                ZLLB = #{zllb,jdbcType=VARCHAR},
            </if>
            <if test="fzbz != null">
                FZBZ = #{fzbz,jdbcType=VARCHAR},
            </if>
            <if test="fbdd != null">
                FBDD = #{fbdd,jdbcType=VARCHAR},
            </if>
            <if test="fbsj != null">
                FBSJ = #{fbsj,jdbcType=TIMESTAMP},
            </if>
            <if test="ryzyzdbm != null">
                RYZYZDBM = #{ryzyzdbm,jdbcType=VARCHAR},
            </if>
            <if test="ryzyzdmc != null">
                RYZYZDMC = #{ryzyzdmc,jdbcType=VARCHAR},
            </if>
            <if test="mzzdbm != null">
                MZZDBM = #{mzzdbm,jdbcType=VARCHAR},
            </if>
            <if test="mzzdmc != null">
                MZZDMC = #{mzzdmc,jdbcType=VARCHAR},
            </if>
            <if test="mzzyzdbm != null">
                MZZYZDBM = #{mzzyzdbm,jdbcType=VARCHAR},
            </if>
            <if test="mzzyzdmc != null">
                MZZYZDMC = #{mzzyzdmc,jdbcType=VARCHAR},
            </if>
            <if test="zrhs != null">
                ZRHS = #{zrhs,jdbcType=VARCHAR},
            </if>
            <if test="zrhsxm != null">
                ZRHSXM = #{zrhsxm,jdbcType=VARCHAR},
            </if>
            <if test="sfcr != null">
                SFCR = #{sfcr,jdbcType=VARCHAR},
            </if>
            yzcybz = #{yzcybz,jdbcType=VARCHAR},
            <if test="bzsm != null">
                bzsm = #{bzsm,jdbcType=VARCHAR},
            </if>
            <if test="bzzdmc != null">
                bzzdmc = #{bzzdmc,jdbcType=VARCHAR},
            </if>
            <if test="bzzdbm != null">
                bzzdbm = #{bzzdbm,jdbcType=VARCHAR},
            </if>
            <if test="bzssmc != null">
                bzssmc = #{bzssmc,jdbcType=VARCHAR},
            </if>
            <if test="bzssbm != null">
                bzssbm = #{bzssbm,jdbcType=VARCHAR},
            </if>
            <if test="cyzd != null">
                cyzd = #{cyzd,jdbcType=VARCHAR},
            </if>
            <if test="sfbzff != null">
                sfbzff = #{sfbzff,jdbcType=VARCHAR},
            </if>
            <if test="xjxj != null">
                xjxj = #{xjxj,jdbcType=VARCHAR},
            </if>
            <if test="dbczy != null">
                dbczy = #{dbczy,jdbcType=VARCHAR},
            </if>
            <if test="mtbbz != null">
                mtbbz = #{mtbbz,jdbcType=VARCHAR},
            </if>
            <if test="djbh != null">
                djbh = #{djbh,jdbcType=VARCHAR},
            </if>
            yzcysj = #{yzcysj,jdbcType=VARCHAR},

        </set>
        where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        and ZYH = #{zyh,jdbcType=VARCHAR}
    </update>


    <select id="ryShow" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel"
            resultType="com.supx.csp.api.zygl.crygl.pojo.Zyb_ryShowModel">
        select sum(jrry) jrry,sum(mzdry) mzdry,sum(mzyry) mzyry from
        ( select count(zyh) jrry, 0 mzdry , 0 mzyry from zyb_rydj
        where yljgbm = #{yljgbm,jdbcType=VARCHAR} and ifzf = '0'
        <if test="beginrq != null">
            and RYRQ &gt;= #{beginrq,jdbcType=TIMESTAMP}
        </if>
        <if test="endrq != null">
            and RYRQ &lt; #{endrq,jdbcType=TIMESTAMP}
        </if>
        union all
        select 0 jrry,count(ryghxh) mzdry,0 mzyry from mzys_mzry
        where zyh is null
        and yljgbm = #{yljgbm,jdbcType=VARCHAR}
        <if test="beginrq != null">
            and sqsj &gt;= #{beginrq,jdbcType=TIMESTAMP}
        </if>
        <if test="endrq != null">
            and sqsj &lt; #{endrq,jdbcType=TIMESTAMP}
        </if>
        union all
        select 0 jrry, 0 mzdry,count(ryghxh) mzyry from mzys_mzry
        where zyh is not null
        and yljgbm = #{yljgbm,jdbcType=VARCHAR}
        <if test="beginrq != null">
            and sqsj &gt;= #{beginrq,jdbcType=TIMESTAMP}
        </if>
        <if test="endrq != null">
            and sqsj &lt; #{endrq,jdbcType=TIMESTAMP}
        </if>
        )
    </select>


    <select id="cyShow" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel"
            resultType="com.supx.csp.api.zygl.crygl.pojo.Zyb_cyShowModel">
        select sum(jrcy) jrcy, sum(dcy) dcy, sum(ycy) ycy from
        (
        select count(1) jrcy,0 dcy, 0 ycy from zyb_rydj
        where 1=1
        <if test="cyks != null and cyks != ''">
            and cyks = #{cyks,jdbcType=VARCHAR}
        </if>
        and zyzt = '1' and yljgbm = #{yljgbm,jdbcType=VARCHAR}
        and CYRQ &gt;= #{beginrq,jdbcType=TIMESTAMP} and CYRQ &lt; #{endrq,jdbcType=TIMESTAMP}
        union all
        select 0 jrcy,count(1) dcy,0 ycy from zyb_rydj
        where 1=1
        <if test="cyks != null and cyks != ''">
            and cyks = #{cyks,jdbcType=VARCHAR}
        </if>
        and zyzt = '0' and bqcybz = '1'
        and yljgbm = #{yljgbm,jdbcType=VARCHAR}
        union all
        select 0 jrcy,0 dcy,count(1) ycy from zyb_rydj
        where 1=1
        <if test="cyks != null and cyks != ''">
            and cyks = #{cyks,jdbcType=VARCHAR}
        </if>
        and zyzt = '1'
        and yljgbm = #{yljgbm,jdbcType=VARCHAR}
        )
    </select>

    <select id="queryCwhByCwid" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_cwhModel"
            resultType="com.supx.csp.api.zygl.crygl.pojo.Zyb_cwhModel">
        select *
        from zyb_cwh
        where yljgbm = #{yljgbm}
          and cwid = #{cwid}
          and zyh is null
    </select>
    <!-- 添加贵州农合疾病检索基本信息 -->
    <select id="queryGznhJs" resultMap="GznhResultMap"
            parameterType="com.supx.csp.api.zygl.crygl.pojo.Gznh_qnz_bzff_xmbModel">
        select xh,icd,icdmc,ssmc,ssbm,zlssmc,xjxj,stop,ssmcdm,zlssmcdm,yljgbm from gznh_qnz_bzff_xmb
        <where>
            yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="parm != null and parm != ''">
                and (ssmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
                or lower(ssmcdm) like '%'||lower(#{parm,jdbcType=VARCHAR})||'%')
            </if>
        </where>
    </select>

    <select id="queryIsBan" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel"
            resultType="java.lang.Integer">
        select count(0) from gyb_brjbxx where bah = #{bah}
        <if test="brid != null and brid != ''">
            <![CDATA[ and brid<>#{brid} ]]>
        </if>
    </select>

    <select id="queryMaxBah" resultType="java.lang.Integer">
        select nvl(max(bah), 0) + 1
        from gyb_brjbxx
    </select>

    <select id="getYbbr" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel"
            resultType="java.lang.Integer">
        select count(0)
        from gssldyb_zydjxx
        where zyh = #{zyh}
          and zfbz = '0'

    </select>

    <select id="getYbfysc" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel"
            resultType="java.lang.Integer">
        select count(0)
        from zyb_bxscmx
        where zyh = #{zyh}
    </select>

    <select id="getBasyzd" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel"
            resultType="java.lang.Integer">
        select count(0)
        from bagl_basy_zdxx
        where zyh = #{zyh}
          and cyzyzdbm is not null
    </select>


    <update id="updateSfsRydj" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        update ZYB_RYDJ
        <set>
            <if test="bxlbbm != null and bxlbbm !=''">
                BXLBBM = #{bxlbbm,jdbcType=VARCHAR},
            </if>
            <if test="bxlbmc != null and bxlbmc !=''">
                BXLBMC = #{bxlbmc,jdbcType=VARCHAR},
            </if>
            <if test="brfb != null and brfb !=''">
                BRFB = #{brfb,jdbcType=VARCHAR},
            </if>
            <if test="brfbmc != null and brfbmc !=''">
                BRFBMC = #{brfbmc,jdbcType=VARCHAR},
            </if>
            <if test="ryzdbm != null and ryzdbm !=''">
                ryzdbm = #{ryzdbm,jdbcType=VARCHAR},
            </if>
            <if test="ryzdmc != null and ryzdmc !=''">
                ryzdmc = #{ryzdmc,jdbcType=VARCHAR},
            </if>
            <if test="ryks != null and ryks !=''">
                ryks = #{ryks,jdbcType=VARCHAR},
            </if>
            <if test="ryksmc != null and ryksmc !=''">
                ryksmc = #{ryksmc,jdbcType=VARCHAR},
            </if>
            <if test="ryrq != null ">
                ryrq = #{ryrq,jdbcType=TIMESTAMP},
            </if>
            <if test="rydjrq != null ">
                rydjrq = #{rydjrq,jdbcType=TIMESTAMP},
            </if>
        </set>
        where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        and ZYH = #{zyh,jdbcType=VARCHAR}
    </update>
    <update id="updateSfsJkac" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
        update zyb_jzkjl
        <set>

            <if test="ryks != null and ryks !=''">
                zrks = #{ryks,jdbcType=VARCHAR},
            </if>
            <if test="ryksmc != null and ryksmc !=''">
                zrksmc = #{ryksmc,jdbcType=VARCHAR},
            </if>
            <if test="ryrq != null ">
                djrq = #{ryrq,jdbcType=TIMESTAMP},
            </if>
            <if test="rydjrq != null ">
                rydjrq = #{rydjrq,jdbcType=TIMESTAMP},
            </if>
        </set>
        where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        and ZYH = #{zyh,jdbcType=VARCHAR} and jzklx='0' and jrbz='0' and zfbz='0'
    </update>

    <select id="queryByGhxh" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel"
            resultType="java.util.HashMap">
        select to_char(ghrq, 'yyyy-mm-dd hh24:mi:ss')    admission_date,
               brnl                                      age,
               ghksmc                                    dept_name,
               xx.brxm                                   name,
               decode(xx.brxb, '1', '男', '2', '女', '') sex,
               ghxh                                      patient_id
        from ghb_brgh ghb,
             gyb_brjbxx xx
        where ghb.brid = xx.brid
          and ghb.YLJGBM = #{yljgbm,jdbcType=VARCHAR}
          and ghb.ghxh = #{zyh,jdbcType=VARCHAR}
    </select>

    <select id="getBrfyze" resultType="java.lang.Integer"
            parameterType="com.supx.csp.api.zyys.ysyw.pojo.HzxxListResModel">
        select sum(fyje)
        from zyb_brfy
        where zyh = #{zyh,jdbcType=VARCHAR}
          and yxbz = '1'
          and sftf = '0'
          and zfbz = '0'
    </select>

    <update id="xgbrDjxx" parameterType="com.supx.csp.api.zyys.ysyw.pojo.HzxxListResModel">
        update zyb_rydj
        set rycwbh='',
            rycwid=''
        where zyh = #{zyh,jdbcType=VARCHAR}

    </update>

    <update id="xgbrJzkjl" parameterType="com.supx.csp.api.zyys.ysyw.pojo.HzxxListResModel">
        update zyb_jzkjl
        set jrbz='0',
            jrry='',
            jrryxm='',
            jrrq=null
        where
        where jzklx='0' and zfbz='0' and jrbz='1' and zyh = #{zyh,jdbcType=VARCHAR}

    </update>

    <update id="xgbrCwh" parameterType="com.supx.csp.api.zyys.ysyw.pojo.HzxxListResModel">
        update zyb_cwh
        set zyh=''
        where zyh = #{zyh,jdbcType=VARCHAR}

    </update>

    <update id="xgbrGdfyxx" parameterType="com.supx.csp.api.zyys.ysyw.pojo.HzxxListResModel">
        delete
        from zyb_brgdfy
        where zyh = #{zyh,jdbcType=VARCHAR}

    </update>


</mapper>
