<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.zygl.fygl.dao.New1Zyb_brfyModelMapper" >

  <resultMap id="BaseResultMap" type="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
    <id column="FYID" property="fyid" jdbcType="VARCHAR" />
    <result column="FYLB" property="fylb" jdbcType="VARCHAR" />
    <result column="FYLBMC" property="fylbmc" jdbcType="VARCHAR" />
    <result column="MXFYXMBM" property="mxfyxmbm" jdbcType="VARCHAR" />
    <result column="MXFYXMMC" property="mxfyxmmc" jdbcType="VARCHAR" />
    <result column="FFYLB" property="ffylb" jdbcType="VARCHAR" />
    <result column="ZHFYBH" property="zhfybh" jdbcType="VARCHAR" />
    <result column="ZHFYMC" property="zhfymc" jdbcType="VARCHAR" />
    <result column="FYDJ" property="fydj" jdbcType="DECIMAL" />
    <result column="FYSL" property="fysl" jdbcType="DECIMAL" />
    <result column="FYJE" property="fyje" jdbcType="DECIMAL" />
    <result column="SFRQ" property="sfrq" jdbcType="TIMESTAMP" />
    <result column="YTSL" property="ytsl" jdbcType="DECIMAL" />
    <result column="ZYH" property="zyh" jdbcType="VARCHAR" />
    <result column="ZYKS" property="zyks" jdbcType="VARCHAR" />
    <result column="ZYKSMC" property="zyksmc" jdbcType="VARCHAR" />
    <result column="ZYYS" property="zyys" jdbcType="VARCHAR" />
    <result column="ZYYSXM" property="zyysxm" jdbcType="VARCHAR" />
    <result column="YSBM" property="ysbm" jdbcType="VARCHAR" />
    <result column="YSXM" property="ysxm" jdbcType="VARCHAR" />
    <result column="YSKS" property="ysks" jdbcType="VARCHAR" />
    <result column="YSKSMC" property="ysksmc" jdbcType="VARCHAR" />
    <result column="ZXKS" property="zxks" jdbcType="VARCHAR" />
    <result column="ZXKSMC" property="zxksmc" jdbcType="VARCHAR" />
    <result column="KSBM" property="ksbm" jdbcType="VARCHAR" />
    <result column="KSMC" property="ksmc" jdbcType="VARCHAR" />
    <result column="YZHM" property="yzhm" jdbcType="VARCHAR" />
    <result column="YZXH" property="yzxh" jdbcType="VARCHAR" />
    <result column="BYDXH" property="bydxh" jdbcType="VARCHAR" />
    <result column="YFBM" property="yfbm" jdbcType="VARCHAR" />
    <result column="YFMC" property="yfmc" jdbcType="VARCHAR" />
    <result column="SFTF" property="sftf" jdbcType="VARCHAR" />
    <result column="TFID" property="tfid" jdbcType="VARCHAR" />
    <result column="YXBZ" property="yxbz" jdbcType="VARCHAR" />
    <result column="RYJSJLID" property="ryjsjlid" jdbcType="VARCHAR" />
    <result column="RYJKPZH" property="ryjkpzh" jdbcType="VARCHAR" />
    <result column="DJRQ" property="djrq" jdbcType="TIMESTAMP" />
    <result column="BZSM" property="bzsm" jdbcType="VARCHAR" />
    <result column="XJJZBZ" property="xjjzbz" jdbcType="VARCHAR" />
    <result column="YHBL" property="yhbl" jdbcType="VARCHAR" />
    <result column="YHJE" property="yhje" jdbcType="DECIMAL" />
    <result column="CZYBM" property="czybm" jdbcType="DECIMAL" />
    <result column="CZYXM" property="czyxm" jdbcType="VARCHAR" />
    <result column="YZXHID" property="yzxhid" jdbcType="VARCHAR" />
    <result column="YZXMMC" property="yzxmmc" jdbcType="VARCHAR" />
    <result column="ZFBZ" property="zfbz" jdbcType="VARCHAR" />
    <result column="ZFRY" property="zfry" jdbcType="VARCHAR" />
    <result column="ZFRYXM" property="zfryxm" jdbcType="VARCHAR" />
    <result column="ZFSJ" property="zfsj" jdbcType="TIMESTAMP" />
    <result column="FPHM" property="fphm" jdbcType="VARCHAR" />
    <!-- 医疗卡信息部分 -->
    <result column="YLKLX" property="ylklx" jdbcType="VARCHAR" />
    <result column="YLKH" property="ylkh" jdbcType="VARCHAR" />
    <!-- 基本信息部分 -->
    <result column="BRID" property="brid" jdbcType="VARCHAR" />
    <result column="BQCYBZ" property="bqcybz" jdbcType="VARCHAR" />
    <result column="BRXM" property="brxm" jdbcType="VARCHAR" />
    <result column="BRXB" property="brxb" jdbcType="VARCHAR" />
    <result column="NL" property="nl" jdbcType="VARCHAR" />
    <result column="NLDW" property="nldw" jdbcType="VARCHAR" />
    <result column="ZYZT" property="zyzt" jdbcType="VARCHAR" />
  </resultMap>


    <!-- 费用记账专用 -->
   <resultMap id="BaseResultMapJz" type="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
    <id column="FYID" property="fyid" jdbcType="VARCHAR" />
    <result column="FYLB" property="fylb" jdbcType="VARCHAR" />
    <result column="FYLBMC" property="fylbmc" jdbcType="VARCHAR" />
    <result column="MXFYXMBM" property="mxfyxmbm" jdbcType="VARCHAR" />
    <result column="MXFYXMMC" property="mxfyxmmc" jdbcType="VARCHAR" />
    <result column="FFYLB" property="ffylb" jdbcType="VARCHAR" />
    <result column="ZHFYBH" property="zhfybh" jdbcType="VARCHAR" />
    <result column="ZHFYMC" property="zhfymc" jdbcType="VARCHAR" />
    <result column="FYDJ" property="fydj" jdbcType="DECIMAL" />
    <result column="FYSL" property="fysl" jdbcType="DECIMAL" />
    <result column="FYJE" property="fyje" jdbcType="DECIMAL" />
    <result column="SFRQ" property="sfrq" jdbcType="TIMESTAMP" />
    <result column="YTSL" property="ytsl" jdbcType="DECIMAL" />
    <result column="ZYH" property="zyh" jdbcType="VARCHAR" />
    <result column="ZYKS" property="zyks" jdbcType="VARCHAR" />
    <result column="ZYKSMC" property="zyksmc" jdbcType="VARCHAR" />
    <result column="ZYYS" property="zyys" jdbcType="VARCHAR" />
    <result column="ZYYSXM" property="zyysxm" jdbcType="VARCHAR" />
    <result column="YSBM" property="ysbm" jdbcType="VARCHAR" />
    <result column="YSXM" property="ysxm" jdbcType="VARCHAR" />
    <result column="YSKS" property="ysks" jdbcType="VARCHAR" />
    <result column="YSKSMC" property="ysksmc" jdbcType="VARCHAR" />
    <result column="ZXKS" property="zxks" jdbcType="VARCHAR" />
    <result column="ZXKSMC" property="zxksmc" jdbcType="VARCHAR" />
    <result column="YZHM" property="yzhm" jdbcType="VARCHAR" />
    <result column="YZXH" property="yzxh" jdbcType="VARCHAR" />
    <result column="BYDXH" property="bydxh" jdbcType="VARCHAR" />
    <result column="YFBM" property="yfbm" jdbcType="VARCHAR" />
    <result column="YFMC" property="yfmc" jdbcType="VARCHAR" />
    <result column="SFTF" property="sftf" jdbcType="VARCHAR" />
    <result column="TFID" property="tfid" jdbcType="VARCHAR" />
    <result column="YXBZ" property="yxbz" jdbcType="VARCHAR" />
    <result column="RYJSJLID" property="ryjsjlid" jdbcType="VARCHAR" />
    <result column="RYJKPZH" property="ryjkpzh" jdbcType="VARCHAR" />
    <result column="DJRQ" property="djrq" jdbcType="TIMESTAMP" />
    <result column="BZSM" property="bzsm" jdbcType="VARCHAR" />
    <result column="XJJZBZ" property="xjjzbz" jdbcType="VARCHAR" />
    <result column="YHBL" property="yhbl" jdbcType="VARCHAR" />
    <result column="YHJE" property="yhje" jdbcType="DECIMAL" />
    <result column="CZYBM" property="czybm" jdbcType="DECIMAL" />
    <result column="CZYXM" property="czyxm" jdbcType="VARCHAR" />
    <result column="YZXHID" property="yzxhid" jdbcType="VARCHAR" />
    <result column="YZXMMC" property="yzxmmc" jdbcType="VARCHAR" />
    <result column="ZFBZ" property="zfbz" jdbcType="VARCHAR" />
    <result column="ZFRY" property="zfry" jdbcType="VARCHAR" />
    <result column="ZFRYXM" property="zfryxm" jdbcType="VARCHAR" />
    <result column="ZFSJ" property="zfsj" jdbcType="TIMESTAMP" />
    <result column="FPHM" property="fphm" jdbcType="VARCHAR" />
    <!-- 是否药品 -->
    <result column="YPFY" property="ypfy" jdbcType="VARCHAR" />
    <result column="FYBZ" property="fybz" jdbcType="VARCHAR" />
    <!-- 医疗卡信息部分 -->
    <result column="YLKLX" property="ylklx" jdbcType="VARCHAR" />
    <result column="YLKH" property="ylkh" jdbcType="VARCHAR" />
    <!-- 基本信息部分 -->
    <result column="BRID" property="brid" jdbcType="VARCHAR" />
    <result column="BQCYBZ" property="bqcybz" jdbcType="VARCHAR" />
    <result column="BRXM" property="brxm" jdbcType="VARCHAR" />
    <result column="BRXB" property="brxb" jdbcType="VARCHAR" />
    <result column="NL" property="nl" jdbcType="VARCHAR" />
    <result column="NLDW" property="nldw" jdbcType="VARCHAR" />
    <result column="ZYZT" property="zyzt" jdbcType="VARCHAR" />
    <result column="FYGG" property="fygg" jdbcType="VARCHAR" />
    <result column="yke186" property="yke186" jdbcType="VARCHAR" />

    <result column="lczd" property="lczd" jdbcType="VARCHAR" />
       <result column="brks" property="brks" jdbcType="VARCHAR" />
       <result column="brksmc" property="brksmc" jdbcType="VARCHAR" />
       <result column="cflx" property="cflx" jdbcType="VARCHAR" />
       <result column="cflxmc" property="cflxmc" jdbcType="VARCHAR" />
       <result column="cfys" property="cfys" jdbcType="VARCHAR" />
       <result column="cfysxm" property="cfysxm" jdbcType="VARCHAR" />


  </resultMap>

  <!--结算票据打印 -->
  <resultMap id="BasePjResultMap" type="com.supx.csp.api.zygl.fygl.pojo.Zyb_fyjz_printModel" >
    <result column="ZYH" property="zyh" jdbcType="VARCHAR" />
    <result column="BRXM" property="brxm" jdbcType="VARCHAR" />
    <result column="BRXB" property="brxb" jdbcType="VARCHAR" />
    <result column="NL" property="nl" jdbcType="VARCHAR" />
    <result column="NLDW" property="nldw" jdbcType="VARCHAR" />
     <result column="SG" property="sg" jdbcType="DECIMAL" />
    <result column="TZ" property="tz" jdbcType="DECIMAL" />
    <result column="RYKS" property="ryks" jdbcType="VARCHAR" />
    <result column="RYKSMC" property="ryksmc" jdbcType="VARCHAR" />
    <result column="FPHM" property="fphm" jdbcType="VARCHAR" />
    <result column="SFZJHM" property="sfzjhm" jdbcType="VARCHAR" />
    <result column="JZDMC" property="jzdmc" jdbcType="VARCHAR" />
    <result column="RYRQ" property="ryrq" jdbcType="TIMESTAMP" />
    <result column="GZDW" property="gzdw" jdbcType="VARCHAR" />
    <result column="DWDZ" property="dwdz" jdbcType="VARCHAR" />
    <result column="SJHM" property="sjhm" jdbcType="DECIMAL" />
    <result column="RYZDBM" property="ryzdbm" jdbcType="DECIMAL" />
    <result column="RYZDMC" property="ryzdmc" jdbcType="DECIMAL" />
    <result column="ZYYS" property="zyys" jdbcType="VARCHAR" />
    <result column="ZYYSXM" property="zyysxm" jdbcType="TIMESTAMP" />
    <result column="RYCWBH" property="rycwbh" jdbcType="VARCHAR" />
    <result column="DBR" property="dbr" jdbcType="VARCHAR" />
    <result column="DBJE" property="dbje" jdbcType="DECIMAL" />
    <result column="YZBM" property="ysbm" jdbcType="VARCHAR" />
    <result column="YSXM" property="ysxm" jdbcType="VARCHAR" />
    <result column="YSKS" property="ysks" jdbcType="VARCHAR" />
    <result column="YSKSMC" property="ysksmc" jdbcType="VARCHAR" />
    <result column="ZXKS" property="zxks" jdbcType="VARCHAR" />
    <result column="ZXKSMC" property="zxksmc" jdbcType="VARCHAR" />
    <result column="CZYBM" property="czybm" jdbcType="VARCHAR" />
    <result column="CZYXM" property="czyxm" jdbcType="VARCHAR" />
    <collection property="brfyList" column="FPHM" ofType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfy_printModel">
    	<id column="FYID" property="fyid" jdbcType="VARCHAR" />
	    <result column="MXFYXMBM" property="mxfyxmbm" jdbcType="VARCHAR" />
	    <result column="MXFYXMMC" property="mxfyxmmc" jdbcType="VARCHAR" />
	    <result column="FYGG" property="fygg" jdbcType="VARCHAR" />
	    <result column="FYDJ" property="fydj" jdbcType="DECIMAL" />
	    <result column="FYSL" property="fysl" jdbcType="DECIMAL" />
	    <result column="FYJE" property="fyje" jdbcType="DECIMAL" />
	    <result column="SFRQ" property="sfrq" jdbcType="TIMESTAMP" />
	    <result column="YZHM" property="yzhm" jdbcType="VARCHAR" />
	    <result column="FPHM" property="fphm" jdbcType="VARCHAR" />
    </collection>
  </resultMap>

    <resultMap id="JzMxxxResultMap" type="com.supx.csp.api.xtwh.ylfwxm.pojo.Gyb_mxfyxmModel" >
        <id column="MXFYBM" property="mxfybm" jdbcType="VARCHAR" />
        <result column="MXFYMC" property="mxfymc" jdbcType="VARCHAR" />
        <result column="FFYLB" property="ffylb" jdbcType="VARCHAR" />
        <result column="PYDM" property="pydm" jdbcType="VARCHAR" />
        <result column="FYGG" property="fygg" jdbcType="VARCHAR" />
        <result column="LBBM" property="lbbm" jdbcType="VARCHAR" />
        <result column="BZBM" property="bzbm" jdbcType="VARCHAR" />
        <result column="FYLX" property="fylx" jdbcType="VARCHAR" />
        <result column="FYTCLB" property="fytclb" jdbcType="VARCHAR" />
        <result column="NBTCLB" property="nbtclb" jdbcType="VARCHAR" />
        <result column="FYDJ" property="fydj" jdbcType="DECIMAL" />
        <result column="HSKS" property="hsks" jdbcType="VARCHAR" />
        <result column="SFGD" property="sfgd" jdbcType="VARCHAR" />
        <result column="XSSX" property="xssx" jdbcType="DECIMAL" />
        <result column="TYBZ" property="tybz" jdbcType="VARCHAR" />
        <result column="YPFY" property="ypfy" jdbcType="VARCHAR" />
        <result column="FFYLB" property="ffylb" jdbcType="VARCHAR" />
        <result column="YHBL" property="yhbl" jdbcType="DECIMAL" />
        <result column="ZBM" property="zbm" jdbcType="VARCHAR" />
        <result column="SFZY" property="sfzy" jdbcType="VARCHAR" />
        <result column="FPFS" property="fpfs" jdbcType="VARCHAR" />
        <result column="FPKS" property="fpks" jdbcType="VARCHAR" />

        <result column="FYLBMC" property="fylbmc" jdbcType="VARCHAR" />
        <result column="ZXKSMC" property="zxksmc" jdbcType="VARCHAR" />
        <result column="TCLBMC" property="tclbmc" jdbcType="VARCHAR" />
        <result column="ZHFY" property="zhfy" jdbcType="VARCHAR" />
        <result column="ZHFYBM" property="zhfybm" jdbcType="VARCHAR" />
        <result column="SL" property="sl" jdbcType="DECIMAL" />
    </resultMap>


  <sql id="Base_Column_List" >
    FYID, FYLB, FYLBMC, MXFYXMBM, MXFYXMMC, ZHFYBH, ZHFYMC, FYDJ, FYSL, FYJE, SFRQ, YTSL,
    ZYH, ZYKS, ZYKSMC, ZYYS, ZYYSXM, YSBM, YSXM, YSKS, YSKSMC, ZXKS, ZXKSMC, YZHM, YZXH,
    BYDXH, YFBM, YFMC, SFTF, TFID, YXBZ, RYJSJLID, RYJKPZH, DJRQ, BZSM, XJJZBZ,YHBL,YHJE,
    CZYBM,CZYXM,YZXHID,YZXMMC,ZFBZ,ZFRY,ZFRYXM,ZFSJ,BRID,FPHM
  </sql>

   <!-- 针对取消住院财务交款更新数据 -->
  <update id="updateByQxJs" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
  	update zyb_brfy
  	<set >
  		ryjsjlid = null
  	</set>
  	 where yljgbm = #{yljgbm,jdbcType=VARCHAR}
  	 and ryjsjlid = #{ryjsjlid,jdbcType=VARCHAR}
  	 and zyh=#{zyh,jdbcType=VARCHAR}
  </update>

    <!-- 针对住院结算更新数据 -->
    <update id="updateBrJs" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
        update zyb_brfy
        set
  		ryjsjlid = #{ryjsjlid,jdbcType=VARCHAR}
  	    where yljgbm = #{yljgbm,jdbcType=VARCHAR}
  	    and yxbz = '1' and zfbz = '0' and ryjsjlid is null
        and zyh=#{zyh,jdbcType=VARCHAR}
    </update>

  <!-- 针对于住院费用记账的票据打印 -->
  <select id="querPjdy" resultMap="BasePjResultMap" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
  	  select jbxx.brxm,jbxx.brxb,jbxx.sg,jbxx.tz,jbxx.sfzjhm,jbxx.jzdmc,jbxx.gzdw,jbxx.dwdz,jbxx.sjhm,
    rydj.nl,rydj.nldw,rydj.ryks,rydj.ryksmc,rydj.zyh,rydj.ryrq,rydj.ryzdbm,rydj.ryzdmc,rydj.zyys,rydj.zyysxm,rydj.rycwbh,rydj.dbr,rydj.dbje,
    brfy.fphm,brfy.ysbm,brfy.ysxm,brfy.ysks,brfy.ysksmc,brfy.zxks,brfy.zxksmc,brfy.czybm,brfy.czyxm,
    mxfyxm.fygg,brfy.fyid,brfy.mxfyxmbm,brfy.mxfyxmmc,brfy.fydj,sum(brfy.fysl) fysl,sum(brfy.fyje) fyje,brfy.sfrq,brfy.yzhm
    from zyb_brfy brfy
	  inner join gyb_mxfyxm mxfyxm on mxfyxm.mxfybm=brfy.mxfyxmbm and mxfyxm.yljgbm = brfy.yljgbm
    inner join zyb_rydj rydj on rydj.zyh=brfy.zyh and rydj.yljgbm =brfy.yljgbm
    inner join gyb_brjbxx jbxx on jbxx.brid = rydj.brid and jbxx.yljgbm=rydj.yljgbm
	  where brfy.yxbz='1' and brfy.zfbz='0' and rydj.ifzf='0' and brfy.fphm=#{fphm,jdbcType=VARCHAR} and brfy.yljgbm=#{yljgbm,jdbcType=VARCHAR}
	  group by jbxx.brxm,jbxx.brxb,jbxx.sg,jbxx.tz,jbxx.sfzjhm,jbxx.jzdmc,jbxx.gzdw,jbxx.dwdz,jbxx.sjhm,
	rydj.nl,rydj.nldw,rydj.ryks,rydj.ryksmc,rydj.zyh,rydj.ryrq,rydj.ryzdbm,rydj.ryzdmc,rydj.zyys,rydj.zyysxm,rydj.rycwbh,rydj.dbr,rydj.dbje,
	brfy.fphm,brfy.ysbm,brfy.ysxm,brfy.ysks,brfy.ysksmc,brfy.zxks,brfy.zxksmc,brfy.czybm,brfy.czyxm,
    mxfyxm.fygg,brfy.fyid,brfy.mxfyxmbm,brfy.mxfyxmmc,brfy.fydj,brfy.sfrq,brfy.yzhm
  </select>

  <!-- 根据住院号查询病人病人费用 -->
  <select id="queryBaByZyh" resultMap="BaseResultMap" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
    select brfy.*,mxfy.yjhz,mxfy.ynhz from zyb_brfy brfy
    inner join gyb_mxfyxm mxfy on brfy.mxfyxmbm =mxfy.mxfybm and brfy.yljgbm =mxfy.yljgbm
    where brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
    and brfy.zfbz='0' and brfy.yxbz='1' and brfy.sftf='0' and brfy.fysl &gt; 0 and brfy.ZYH= #{zyh,jdbcType=VARCHAR}
  </select>

  <!-- 针对住院财务交款更新数据 -->
  <update id="updateByCwjk" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
  update zyb_brfy
  <set >
  ryjkpzh = #{ryjkpzh,jdbcType=VARCHAR}
  </set>
   where yljgbm = #{yljgbm,jdbcType=VARCHAR}
   and ryjkpzh is null and czybm= #{czybm,jdbcType=VARCHAR} and sfrq &lt;= #{sfrq,jdbcType=TIMESTAMP}
  </update>


  <!-- 针对取消住院财务交款更新数据 -->
  <update id="updateByQxCwjk" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
  	update zyb_brfy
  	<set >
  		ryjkpzh= null
  	</set>
  	 where yljgbm = #{yljgbm,jdbcType=VARCHAR}
  	 and ryjkpzh = #{ryjkpzh,jdbcType=VARCHAR}
  </update>

  <!-- 根据住院号查询病人费用集合 -->
   <select id="queryByZyh" resultMap="BaseResultMap" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
    select
    <include refid="Base_Column_List" />
    from zyb_brfy
    where yljgbm = #{yljgbm,jdbcType=VARCHAR}
    and yxbz='1' and ZYH= #{zyh,jdbcType=VARCHAR}
       <if test="zfbz != null and zfbz != ''">
           AND ZFBZ = #{zfbz}
       </if>
       <if test="sftf != null and sftf != ''">
           AND sftf = #{sftf}
       </if>
       <if test="yzhm != null and yzhm != ''">
           AND YZHM = #{yzhm}
       </if>
       <if test="ifjs == 2">
           AND RYJSJLID IS NULL
       </if>
       <if test="ryjsjlid != null and ryjsjlid != ''">
           AND RYJSJLID = #{ryjsjlid}
       </if>
       order by fylb,mxfyxmmc,sfrq desc
  </select>

  <!-- 查询无效的病人病人费用集合 -->
  <select id="queryWxfy" resultMap="BaseResultMap" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
	  select brfy.*,rydj.bqcybz,mx.ffylb from zyb_brfy brfy
		inner join zyb_rydj rydj on rydj.zyh=brfy.zyh and rydj.yljgbm=brfy.yljgbm
		inner join gyb_mxfyxm mx on mx.mxfybm=brfy.mxfyxmbm and mx.yljgbm=brfy.yljgbm
		<where>
			brfy.zfbz=0 and rydj.ifzf='0' and rydj.zyzt='0' and brfy.ryjsjlid is null and brfy.yxbz='0'
			and brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
			<if test="zyh != null and zyh !='' ">
				and (brfy.zyh = #{zyh,jdbcType=VARCHAR})
			</if>
		</where>
  </select>

  <!-- 刷卡或者根据住院号或者根据床位号来查询病人费用集合 -->
  <select id="query" resultMap="BaseResultMapJz" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
select * from (
      select brfy.fyid,brfy.fylb,brfy.fylbmc,brfy.mxfyxmbm,brfy.mxfyxmmc,brfy.zhfybh,brfy.zhfymc,brfy.fydj,brfy.fysl,brfy.fyje,brfy.sfrq,brfy.ytsl,brfy.zyh,brfy.zyks,
      brfy.zyksmc,brfy.zyys,brfy.zyysxm,brfy.ysbm,brfy.ysxm,brfy.ysks,brfy.ysksmc,brfy.zxks,brfy.zxksmc,brfy.yzhm,brfy.yzxh,brfy.bydxh,brfy.yfbm,brfy.yfmc,brfy.sftf,
      brfy.tfid,brfy.yxbz,brfy.ryjsjlid,brfy.ryjkpzh,brfy.djrq,brfy.bzsm,brfy.xjjzbz,brfy.yhbl,brfy.yhje,brfy.czybm,brfy.czyxm,brfy.yzxhid,brfy.yzxmmc,brfy.zfbz,brfy.zfry,brfy.zfryxm,brfy.zfsj,
      brfy.brid,brfy.yljgbm,brfy.fphm,brfy.jkscbz,brfy.bzbm,brfy.jyxh,brfy.fyid_oldhis,brfy.pacs_bz,brfy.kskfrq,brfy.kskfrybm,brfy.kskfryxm,brfy.clbz,brfy.sfrqbf,brfy.ksbm,
      brfy.ksmc,brfy.jcfl,brfy.ylyzxh,brfy.zxbz,brfy.zxry,brfy.zxsj,brfy.settlementstatus,brfy.settlementdate,brfy.zxyxbz,xm.ypfy,xm.fygg,rydj.bqcybz,rydj.zyzt,null yke186,null lczd,null brks,null brksmc,null cflx,null cflxmc,null cfys,null cfysxm,null brxm,null cfrq
      from zyb_brfy brfy
      inner join zyb_rydj rydj on rydj.zyh=brfy.zyh and rydj.yljgbm=brfy.yljgbm
      inner join gyb_mxfyxm xm on xm.mxfybm=brfy.mxfyxmbm and xm.yljgbm=brfy.yljgbm
	<where>
		brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
	    and brfy.zfbz=0 and rydj.ifzf='0' and rydj.zyzt='0' and brfy.ryjsjlid is null and brfy.yxbz='1' and brfy.sftf='0'
        and brfy.yfbm is null
        <if test="zyh != null and zyh !='' ">
			and (brfy.zyh = #{zyh,jdbcType=VARCHAR})
		</if>
		<if test="ylklx != null and ylklx != '' and ylkh != null and ylkh != ''">
			and exists (select 1 from gyb_brylkxx ylk where ylk.brid=rydj.brid
				and (ylk.ylklx = #{ylklx,jdbcType=VARCHAR})
				and (ylk.ylkh = #{ylkh,jdbcType=VARCHAR}))
		</if>
		<if test="rycwbh != null and rycwbh !='' ">
			and (rydj.rycwbh = #{rycwbh,jdbcType=VARCHAR})
		</if>
        <if test="fylb != null and fylb !='' and fylb != 'all'.toString()">
            and (brfy.fylb = #{fylb,jdbcType=VARCHAR})
        </if>
		<if test="beginrq != null and beginrq!=''">
			and (brfy.sfrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
		</if>
		<if test="endrq != null and endrq!=''">
			and (brfy.sfrq &lt;= #{endrq,jdbcType=TIMESTAMP})
		</if>
		<if test="zxks != null and zxks != ''">
			and (brfy.ZYKS = #{zxks})
		</if>
		<if test="parm != null and parm !=''">
             and (brfy.mxfyxmmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
              or brfy.mxfyxmbm like '%'||#{parm,jdbcType=VARCHAR}||'%'
              or brfy.yzxmmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
              or upper(xm.pydm) like '%'||upper(#{parm,jdbcType=VARCHAR})||'%')
        </if>
	</where>

	union all
      select brfy.fyid,brfy.fylb,brfy.mxfyxmmc fylbmc,zd.ypbm mxfyxmbm,zd.ypmc mxfyxmmc,brfy.zhfybh,brfy.zhfymc,pf.yplj fydj,pf.cfyl fysl,pf.yplj*pf.cfyl fyje,brfy.sfrq,brfy.ytsl,brfy.zyh,brfy.zyks,
      brfy.zyksmc,brfy.zyys,brfy.zyysxm,brfy.ysbm,brfy.ysxm,brfy.ysks,brfy.ysksmc,brfy.zxks,brfy.zxksmc,brfy.yzhm,brfy.yzxh,brfy.bydxh,brfy.yfbm,brfy.yfmc,brfy.sftf,
      brfy.tfid,brfy.yxbz,brfy.ryjsjlid,brfy.ryjkpzh,brfy.djrq,brfy.bzsm,brfy.xjjzbz,brfy.yhbl,brfy.yhje,brfy.czybm,brfy.czyxm,cf.cfh yzxhid,brfy.mxfyxmmc yzxmmc,brfy.zfbz,brfy.zfry,brfy.zfryxm,brfy.zfsj,
      brfy.brid,brfy.yljgbm,brfy.fphm,brfy.jkscbz,brfy.bzbm,brfy.jyxh,brfy.fyid_oldhis,brfy.pacs_bz,brfy.kskfrq,brfy.kskfrybm,brfy.kskfryxm,brfy.clbz,brfy.sfrqbf,brfy.ksbm,
      brfy.ksmc,brfy.jcfl,brfy.ylyzxh,brfy.zxbz,brfy.zxry,brfy.zxsj,brfy.settlementstatus,brfy.settlementdate,brfy.zxyxbz,
      xm.ypfy,zd.ypgg fygg,rydj.bqcybz,rydj.zyzt,pf.yke186,cf.lczd,cf.brks,cf.brksmc,cf.cflx,cf.cflxmc,cf.cfys,cf.cfysxm,cf.brxm,cf.cfrq
      from zyb_brfy brfy
      inner join zyb_rydj rydj on rydj.zyh=brfy.zyh and rydj.yljgbm=brfy.yljgbm
      inner join gyb_mxfyxm xm on xm.mxfybm=brfy.mxfyxmbm and xm.yljgbm=brfy.yljgbm
      left join yfb_ypcf cf on  cf.cfh = brfy.yzhm and cf.yljgbm = brfy.yljgbm
      left join yfb_yppf pf on cf.cfh = pf.cfh and cf.yljgbm = pf.yljgbm
      left join ykb_ypzd zd on zd.ypbm = pf.ypbm and zd.yljgbm = pf.yljgbm
      <where>
          brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
          and brfy.zfbz=0 and rydj.ifzf='0' and rydj.zyzt='0' and brfy.ryjsjlid is null and brfy.yxbz='1' and brfy.sftf='0'
          and brfy.yfbm is not null and  bydxh is null And brfy.fylb not in ('210')
          <if test="zyh != null and zyh !='' ">
              and (brfy.zyh = #{zyh,jdbcType=VARCHAR})
          </if>
          <if test="ylklx != null and ylklx != '' and ylkh != null and ylkh != ''">
              and exists (select 1 from gyb_brylkxx ylk where ylk.brid=rydj.brid
              and (ylk.ylklx = #{ylklx,jdbcType=VARCHAR})
              and (ylk.ylkh = #{ylkh,jdbcType=VARCHAR}))
          </if>
          <if test="rycwbh != null and rycwbh !='' ">
              and (rydj.rycwbh = #{rycwbh,jdbcType=VARCHAR})
          </if>
          <if test="fylb != null and fylb !='' and fylb != 'all'.toString()">
              and (brfy.fylb = #{fylb,jdbcType=VARCHAR})
          </if>
          <if test="beginrq != null and beginrq!=''">
              and (brfy.sfrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
          </if>
          <if test="endrq != null and endrq!=''">
              and (brfy.sfrq &lt;= #{endrq,jdbcType=TIMESTAMP})
          </if>
          <if test="zxks != null and zxks != ''">
              and (brfy.ZYKS = #{zxks})
          </if>
          <if test="parm != null and parm !=''">
              and cf.cfh in (
                select cf.cfh from yfb_ypcf cf
                  inner join yfb_yppf pf on cf.cfh = pf.cfh and cf.yljgbm = pf.yljgbm
                  inner join ykb_ypzd zd on zd.ypbm = pf.ypbm and zd.yljgbm = pf.yljgbm
                  where cf.bah = #{zyh,jdbcType=VARCHAR} and
                  (zd.ypmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
                  or zd.ypbm like '%'||#{parm,jdbcType=VARCHAR}||'%'
                  or brfy.yzxmmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
                  or upper(xm.pydm) like '%'||upper(#{parm,jdbcType=VARCHAR})||'%')
              )
          </if>
      </where>
	)
      ORDER BY sfrq,yzxhid,zhfybh desc
  </select>

  <!-- 根据住院号或者医疗卡号分组查询病人费用 （主要用于结算记录）-->
  <select id="queryByYhfx" resultMap="BaseResultMap" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
  	select brfy.zyh zyh,brfy.fylb fylb,brfy.fylbmc fylbmc,sum(brfy.fyje) fyje from zyb_brfy brfy
	<where>
		brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
	    and brfy.yxbz='1' and brfy.zfbz=0 and trim(brfy.ryjsjlid) is null
    	<if test="zyh != null and zyh !='' ">
			and (brfy.zyh = #{zyh,jdbcType=VARCHAR})
		</if>
		<if test="ylklx != null and ylklx != '' and ylkh != null and ylkh != ''">
			and exists (select 1 from gyb_brylkxx ylk where ylk.yljgbm=brfy.yljgbm
				and ylk.brid=brfy.brid and ylk.zfbz='0'
				and (ylk.ylklx = #{ylklx,jdbcType=VARCHAR})
				and (ylk.ylkh = #{ylkh,jdbcType=VARCHAR}))
		</if>
	</where>
	group by brfy.zyh,brfy.fylb,brfy.fylbmc
  </select>

  <!-- 根据住院号或者医疗卡号查询病人费用集合 -->
  <select id="queryByModel" resultMap="BaseResultMap" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
  	select brfy.* from zyb_brfy brfy
	<where>
		brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
	    and brfy.yxbz='1' and brfy.zfbz=0  and brfy.sftf='0'
	    <if test="ifjs == 0">
			and trim(brfy.ryjsjlid) is null
		</if>
		<if test="ifjs == 1">
			and trim(brfy.ryjsjlid) is not null
		</if>
    	<if test="zyh != null and zyh !='' ">
			and (brfy.zyh = #{zyh,jdbcType=VARCHAR})
		</if>
		<if test="ylklx != null and ylklx != '' and ylkh != null and ylkh != ''">
			and exists (select 1 from gyb_brylkxx ylk where ylk.yljgbm=brfy.yljgbm
				and ylk.brid=brfy.brid and ylk.zfbz='0'
				and (ylk.ylklx = #{ylklx,jdbcType=VARCHAR})
				and (ylk.ylkh = #{ylkh,jdbcType=VARCHAR}))
		</if>
		<if test="fylb !=null">
			and brfy.fylb=#{fylb,jdbcType=VARCHAR}
		</if>
	</where>
  </select>

  <!-- 修改病人费用 -->
  <update id="update" parameterType="java.util.List" >
  	<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    update ZYB_BRFY
    <set >
      <if test="item.fylb != null" >
        FYLB = #{item.fylb,jdbcType=VARCHAR},
      </if>
      <if test="item.fylbmc != null" >
        FYLBMC = #{item.fylbmc,jdbcType=VARCHAR},
      </if>
      <if test="item.mxfyxmbm != null" >
        MXFYXMBM = #{item.mxfyxmbm,jdbcType=VARCHAR},
      </if>
      <if test="item.mxfyxmmc != null" >
        MXFYXMMC = #{item.mxfyxmmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zhfybh != null" >
        ZHFYBH = #{item.zhfybh,jdbcType=VARCHAR},
      </if>
      <if test="item.zhfymc != null" >
        ZHFYMC = #{item.zhfymc,jdbcType=VARCHAR},
      </if>
      <if test="item.fydj != null" >
        FYDJ = #{item.fydj,jdbcType=DECIMAL},
      </if>
      <if test="item.fysl != null" >
        FYSL = #{item.fysl,jdbcType=DECIMAL},
      </if>
      <if test="item.fyje != null" >
        FYJE = #{item.fyje,jdbcType=DECIMAL},
      </if>
      <if test="item.sfrq != null" >
        SFRQ = #{item.sfrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ytsl != null" >
        YTSL = #{item.ytsl,jdbcType=DECIMAL},
      </if>
      <if test="item.zyh != null" >
        ZYH = #{item.zyh,jdbcType=VARCHAR},
      </if>
      <if test="item.zyks != null" >
        ZYKS = #{item.zyks,jdbcType=VARCHAR},
      </if>
      <if test="item.zyksmc != null" >
        ZYKSMC = #{item.zyksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zyys != null" >
        ZYYS = #{item.zyys,jdbcType=VARCHAR},
      </if>
      <if test="item.zyysxm != null" >
        ZYYSXM = #{item.zyysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysbm != null" >
        YSBM = #{item.ysbm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysxm != null" >
        YSXM = #{item.ysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysks != null" >
        YSKS = #{item.ysks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysksmc != null" >
        YSKSMC = #{item.ysksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zxks != null" >
        ZXKS = #{item.zxks,jdbcType=VARCHAR},
      </if>
      <if test="item.zxksmc != null" >
        ZXKSMC = #{item.zxksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.yzhm != null" >
        YZHM = #{item.yzhm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzxh != null" >
        YZXH = #{item.yzxh,jdbcType=VARCHAR},
      </if>
      <if test="item.bydxh != null" >
        BYDXH = #{item.bydxh,jdbcType=VARCHAR},
      </if>
      <if test="item.yfbm != null" >
        YFBM = #{item.yfbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yfmc != null" >
        YFMC = #{item.yfmc,jdbcType=VARCHAR},
      </if>
      <if test="item.sftf != null" >
        SFTF = #{item.sftf,jdbcType=VARCHAR},
      </if>
      <if test="item.tfid != null" >
        TFID = #{item.tfid,jdbcType=VARCHAR},
      </if>
      <if test="item.yxbz != null" >
        YXBZ = #{item.yxbz,jdbcType=VARCHAR},zxyxbz = #{item.yxbz,jdbcType=VARCHAR},
      </if>
      <if test="item.ryjsjlid != null" >
        RYJSJLID = #{item.ryjsjlid,jdbcType=VARCHAR},
      </if>
      <if test="item.ryjkpzh != null" >
        RYJKPZH = #{item.ryjkpzh,jdbcType=VARCHAR},
      </if>
      <if test="item.djrq != null" >
        DJRQ = #{item.djrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.bzsm != null" >
        BZSM = #{item.bzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.xjjzbz != null" >
        XJJZBZ = #{item.xjjzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.czybm != null" >
        CZYBM = #{item.czybm,jdbcType=VARCHAR},
      </if>
      <if test="item.czyxm != null" >
        CZYXM = #{item.czyxm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzxhid != null" >
        YZXHID = #{item.yzxhid,jdbcType=VARCHAR},
      </if>
      <if test="item.yzxmmc != null" >
        YZXMMC = #{item.yzxmmc,jdbcType=VARCHAR},
      </if>
      <if test="item.brid != null" >
        BRID = #{item.brid,jdbcType=VARCHAR},
      </if>
      <if test="item.fphm != null" >
        FPHM = #{item.fphm,jdbcType=VARCHAR},
      </if>
        <if test="item.zfbz != null" >
            ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
        </if>
        <if test="item.zfsj != null" >
            ZFSJ = #{item.zfsj,jdbcType=DATE},
        </if>
        <if test="item.zfry != null" >
            ZFRY = #{item.zfry,jdbcType=VARCHAR},
        </if>
        <if test="item.zfryxm != null" >
            ZFRYXM = #{item.zfryxm,jdbcType=VARCHAR},
        </if>
        <if test="item.ksbm != null" >
            KSBM = #{item.ksbm,jdbcType=VARCHAR},
        </if>
        <if test="item.kskfrq != null" >
            kskfrq = #{item.kskfrq,jdbcType=DATE},
        </if>
        <if test="item.kskfrybm != null" >
            kskfrybm = #{item.kskfrybm,jdbcType=VARCHAR},
        </if>
        <if test="item.kskfryxm != null" >
            kskfryxm = #{item.kskfryxm,jdbcType=VARCHAR},
        </if>
    </set>
    where YLJGBM =  #{item.yljgbm,jdbcType=VARCHAR}
    and FYID = #{item.fyid,jdbcType=VARCHAR}
    </foreach>
  </update>

    <!-- 修改病人费用 -->
    <update id="updateOne" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
            update ZYB_BRFY
            <set >
                <if test="fylb != null" >
                    FYLB = #{fylb,jdbcType=VARCHAR},
                </if>
                <if test="fylbmc != null" >
                    FYLBMC = #{fylbmc,jdbcType=VARCHAR},
                </if>
                <if test="mxfyxmbm != null" >
                    MXFYXMBM = #{mxfyxmbm,jdbcType=VARCHAR},
                </if>
                <if test="mxfyxmmc != null" >
                    MXFYXMMC = #{mxfyxmmc,jdbcType=VARCHAR},
                </if>
                <if test="zhfybh != null" >
                    ZHFYBH = #{zhfybh,jdbcType=VARCHAR},
                </if>
                <if test="zhfymc != null" >
                    ZHFYMC = #{zhfymc,jdbcType=VARCHAR},
                </if>
                <if test="fydj != null" >
                    FYDJ = #{fydj,jdbcType=DECIMAL},
                </if>
                <if test="fysl != null" >
                    FYSL = #{fysl,jdbcType=DECIMAL},
                </if>
                <if test="fyje != null" >
                    FYJE = #{fyje,jdbcType=DECIMAL},
                </if>
                <if test="sfrq != null" >
                    SFRQ = #{sfrq,jdbcType=TIMESTAMP},
                </if>
                <if test="ytsl != null" >
                    YTSL = #{ytsl,jdbcType=DECIMAL},
                </if>
                <if test="zyh != null" >
                    ZYH = #{zyh,jdbcType=VARCHAR},
                </if>
                <if test="zyks != null" >
                    ZYKS = #{zyks,jdbcType=VARCHAR},
                </if>
                <if test="zyksmc != null" >
                    ZYKSMC = #{zyksmc,jdbcType=VARCHAR},
                </if>
                <if test="zyys != null" >
                    ZYYS = #{zyys,jdbcType=VARCHAR},
                </if>
                <if test="zyysxm != null" >
                    ZYYSXM = #{zyysxm,jdbcType=VARCHAR},
                </if>
                <if test="ysbm != null" >
                    YSBM = #{ysbm,jdbcType=VARCHAR},
                </if>
                <if test="ysxm != null" >
                    YSXM = #{ysxm,jdbcType=VARCHAR},
                </if>
                <if test="ysks != null" >
                    YSKS = #{ysks,jdbcType=VARCHAR},
                </if>
                <if test="ysksmc != null" >
                    YSKSMC = #{ysksmc,jdbcType=VARCHAR},
                </if>
                <if test="zxks != null" >
                    ZXKS = #{zxks,jdbcType=VARCHAR},
                </if>
                <if test="zxksmc != null" >
                    ZXKSMC = #{zxksmc,jdbcType=VARCHAR},
                </if>
                <if test="yzhm != null" >
                    YZHM = #{yzhm,jdbcType=VARCHAR},
                </if>
                <if test="yzxh != null" >
                    YZXH = #{yzxh,jdbcType=VARCHAR},
                </if>
                <if test="bydxh != null" >
                    BYDXH = #{bydxh,jdbcType=VARCHAR},
                </if>
                <if test="yfbm != null" >
                    YFBM = #{yfbm,jdbcType=VARCHAR},
                </if>
                <if test="yfmc != null" >
                    YFMC = #{yfmc,jdbcType=VARCHAR},
                </if>
                <if test="sftf != null" >
                    SFTF = #{sftf,jdbcType=VARCHAR},
                </if>
                <if test="tfid != null" >
                    TFID = #{tfid,jdbcType=VARCHAR},
                </if>
                <if test="yxbz != null" >
                    YXBZ = #{yxbz,jdbcType=VARCHAR},
                </if>
                <if test="ryjsjlid != null" >
                    RYJSJLID = #{ryjsjlid,jdbcType=VARCHAR},
                </if>
                <if test="ryjkpzh != null" >
                    RYJKPZH = #{ryjkpzh,jdbcType=VARCHAR},
                </if>
                <if test="djrq != null" >
                    DJRQ = #{djrq,jdbcType=TIMESTAMP},
                </if>
                <if test="bzsm != null" >
                    BZSM = #{bzsm,jdbcType=VARCHAR},
                </if>
                <if test="xjjzbz != null" >
                    XJJZBZ = #{xjjzbz,jdbcType=VARCHAR},
                </if>
                <if test="czybm != null" >
                    CZYBM = #{czybm,jdbcType=VARCHAR},
                </if>
                <if test="czyxm != null" >
                    CZYXM = #{czyxm,jdbcType=VARCHAR},
                </if>
                <if test="yzxhid != null" >
                    YZXHID = #{yzxhid,jdbcType=VARCHAR},
                </if>
                <if test="yzxmmc != null" >
                    YZXMMC = #{yzxmmc,jdbcType=VARCHAR},
                </if>
                <if test="brid != null" >
                    BRID = #{brid,jdbcType=VARCHAR},
                </if>
                <if test="fphm != null" >
                    FPHM = #{fphm,jdbcType=VARCHAR},
                </if>
                <if test="zfbz != null" >
                    ZFBZ = #{zfbz,jdbcType=VARCHAR},
                </if>
                <if test="zfsj != null" >
                    ZFSJ = #{zfsj,jdbcType=DATE},
                </if>
                <if test="zfry != null" >
                    ZFRY = #{zfry,jdbcType=VARCHAR},
                </if>
                <if test="zfryxm != null" >
                    ZFRYXM = #{zfryxm,jdbcType=VARCHAR},
                </if>
                <if test="ksbm != null" >
                    KSBM = #{ksbm,jdbcType=VARCHAR},
                </if>
                <if test="ksmc != null" >
                    KSMC = #{ksmc,jdbcType=VARCHAR},
                </if>
            </set>
            where YLJGBM =  #{yljgbm,jdbcType=VARCHAR}
            and FYID = #{fyid,jdbcType=VARCHAR}
    </update>

    <update id="updateBrfyZxks" parameterType="java.util.List">
     <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
         update zyb_brfy
         <set>
             <if test="item.zxks != null">zxks = #{item.zxks},</if>
             <if test="item.zxksmc != null">zxksmc = #{item.zxksmc},</if>
         </set>
         where yzxh = #{item.yzxh, jdbcType=VARCHAR}
         and yljgbm = #{item.yljgbm,jdbcType=VARCHAR}
         <if test="item.yzxhid != null">
             and yzxhid = #{item.yzxhid,jdbcType=VARCHAR}
         </if>
     </foreach>
  </update>

 <!-- 批量新增病人费用 -->
  <insert id="insert" parameterType="java.util.List" >
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    insert into ZYB_BRFY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="item.fylb != null" >
        FYLB,
      </if>
      <if test="item.fylbmc != null" >
        FYLBMC,
      </if>
      <if test="item.mxfyxmbm != null" >
        MXFYXMBM,
      </if>
      <if test="item.mxfyxmmc != null" >
        MXFYXMMC,
      </if>
      <if test="item.zhfybh != null" >
        ZHFYBH,
      </if>
      <if test="item.zhfymc != null" >
        ZHFYMC,
      </if>
      <if test="item.fydj != null" >
        FYDJ,
      </if>
      <if test="item.fysl != null" >
        FYSL,
      </if>
      <if test="item.fyje != null" >
        FYJE,
      </if>
      <if test="item.sfrq != null" >
        SFRQ,
      </if>
      <if test="item.ytsl != null" >
        YTSL,
      </if>
      <if test="item.zyh != null" >
        ZYH,
      </if>
      <if test="item.zyks != null" >
        ZYKS,
      </if>
      <if test="item.zyksmc != null" >
        ZYKSMC,
      </if>
      <if test="item.zyys != null" >
        ZYYS,
      </if>
      <if test="item.zyysxm != null" >
        ZYYSXM,
      </if>
      <if test="item.ysbm != null" >
        YSBM,
      </if>
      <if test="item.ysxm != null" >
        YSXM,
      </if>
      <if test="item.ysks != null" >
        YSKS,
      </if>
      <if test="item.ysksmc != null" >
        YSKSMC,
      </if>
      <if test="item.zxks != null" >
        ZXKS,
      </if>
      <if test="item.zxksmc != null" >
        ZXKSMC,
      </if>
      <if test="item.yzhm != null" >
        YZHM,
      </if>
      <if test="item.yzxh != null" >
        YZXH,
      </if>
      <if test="item.bydxh != null" >
        BYDXH,
      </if>
      <if test="item.yfbm != null" >
        YFBM,
      </if>
      <if test="item.yfmc != null" >
        YFMC,
      </if>
      <if test="item.sftf != null" >
        SFTF,
      </if>
      <if test="item.tfid != null" >
        TFID,
      </if>
      <if test="item.yxbz != null" >
        YXBZ,
      </if>
      <if test="item.ryjsjlid != null" >
        RYJSJLID,
      </if>
      <if test="item.ryjkpzh != null" >
        RYJKPZH,
      </if>
      <if test="item.djrq != null" >
        DJRQ,
      </if>
      <if test="item.bzsm != null" >
        BZSM,
      </if>
      <if test="item.xjjzbz != null" >
        XJJZBZ,
      </if>
      <if test="item.czybm != null" >
        CZYBM,
      </if>
      <if test="item.czyxm != null" >
        CZYXM,
      </if>
      <if test="item.yzxhid != null" >
        YZXHID,
      </if>
      <if test="item.yzxmmc != null" >
        YZXMMC,
      </if>
       <if test="item.brid != null" >
        BRID,
      </if>
      <if test="item.yljgbm != null" >
        YLJGBM,
      </if>
      <if test="item.fphm != null" >
        FPHM,
      </if>
    <if test="item.ksbm != null" >
        KSBM,
    </if>
    <if test="item.ksmc != null" >
        KSMC,
    </if>
        <if test="item.zfbz != null" >
            ZFBZ,
        </if>
        <if test="item.zfry != null" >
            ZFRY,
        </if>
        <if test="item.zfryxm != null" >
            ZFRYXM,
        </if>
        <if test="item.zfsj != null" >
            ZFSJ,
        </if>
        <if test="item.ylyzxh != null" >
            YLYZXH,
        </if>
        <if test="item.fyjzdh != null" >
            fyjzdh,
        </if>
        <if test="item.ycuuid != null" >
            ycuuid,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="item.fylb != null" >
        #{item.fylb,jdbcType=VARCHAR},
      </if>
      <if test="item.fylbmc != null" >
        #{item.fylbmc,jdbcType=VARCHAR},
      </if>
      <if test="item.mxfyxmbm != null" >
        #{item.mxfyxmbm,jdbcType=VARCHAR},
      </if>
      <if test="item.mxfyxmmc != null" >
        #{item.mxfyxmmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zhfybh != null" >
        #{item.zhfybh,jdbcType=VARCHAR},
      </if>
      <if test="item.zhfymc != null" >
        #{item.zhfymc,jdbcType=VARCHAR},
      </if>
      <if test="item.fydj != null" >
        #{item.fydj,jdbcType=DECIMAL},
      </if>
      <if test="item.fysl != null" >
        #{item.fysl,jdbcType=DECIMAL},
      </if>
      <if test="item.fyje != null" >
        #{item.fyje,jdbcType=DECIMAL},
      </if>
      <if test="item.sfrq != null" >
        #{item.sfrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ytsl != null" >
        #{item.ytsl,jdbcType=DECIMAL},
      </if>
      <if test="item.zyh != null" >
        #{item.zyh,jdbcType=VARCHAR},
      </if>
      <if test="item.zyks != null" >
        #{item.zyks,jdbcType=VARCHAR},
      </if>
      <if test="item.zyksmc != null" >
        #{item.zyksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zyys != null" >
        #{item.zyys,jdbcType=VARCHAR},
      </if>
      <if test="item.zyysxm != null" >
        #{item.zyysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysbm != null" >
        #{item.ysbm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysxm != null" >
        #{item.ysxm,jdbcType=VARCHAR},
      </if>
      <if test="item.ysks != null" >
        #{item.ysks,jdbcType=VARCHAR},
      </if>
      <if test="item.ysksmc != null" >
        #{item.ysksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.zxks != null" >
        #{item.zxks,jdbcType=VARCHAR},
      </if>
      <if test="item.zxksmc != null" >
        #{item.zxksmc,jdbcType=VARCHAR},
      </if>
      <if test="item.yzhm != null" >
        #{item.yzhm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzxh != null" >
        #{item.yzxh,jdbcType=VARCHAR},
      </if>
      <if test="item.bydxh != null" >
        #{item.bydxh,jdbcType=VARCHAR},
      </if>
      <if test="item.yfbm != null" >
        #{item.yfbm,jdbcType=VARCHAR},
      </if>
      <if test="item.yfmc != null" >
        #{item.yfmc,jdbcType=VARCHAR},
      </if>
      <if test="item.sftf != null" >
        #{item.sftf,jdbcType=VARCHAR},
      </if>
      <if test="item.tfid != null" >
        #{item.tfid,jdbcType=VARCHAR},
      </if>
      <if test="item.yxbz != null" >
        #{item.yxbz,jdbcType=VARCHAR},
      </if>
      <if test="item.ryjsjlid != null" >
        #{item.ryjsjlid,jdbcType=VARCHAR},
      </if>
      <if test="item.ryjkpzh != null" >
        #{item.ryjkpzh,jdbcType=VARCHAR},
      </if>
      <if test="item.djrq != null" >
        #{item.djrq,jdbcType=TIMESTAMP},
      </if>
      <if test="item.bzsm != null" >
        #{item.bzsm,jdbcType=VARCHAR},
      </if>
      <if test="item.xjjzbz != null" >
        #{item.xjjzbz,jdbcType=VARCHAR},
      </if>
      <if test="item.czybm != null" >
        #{item.czybm,jdbcType=VARCHAR},
      </if>
      <if test="item.czyxm != null" >
        #{item.czyxm,jdbcType=VARCHAR},
      </if>
      <if test="item.yzxhid != null" >
        #{item.yzxhid,jdbcType=VARCHAR},
      </if>
      <if test="item.yzxmmc != null" >
        #{item.yzxmmc,jdbcType=VARCHAR},
      </if>
       <if test="item.brid != null" >
        #{item.brid,jdbcType=VARCHAR},
      </if>
      <if test="item.yljgbm != null" >
        #{item.yljgbm,jdbcType=VARCHAR},
      </if>
      <if test="item.fphm != null" >
        #{item.fphm,jdbcType=VARCHAR},
      </if>
      <if test="item.ksbm != null" >
          #{item.ksbm,jdbcType=VARCHAR},
      </if>
      <if test="item.ksmc != null" >
          #{item.ksmc,jdbcType=VARCHAR},
      </if>
        <if test="item.zfbz != null" >
            #{item.zfbz,jdbcType=VARCHAR},
        </if>
        <if test="item.zfry != null" >
            #{item.zfry,jdbcType=VARCHAR},
        </if>
        <if test="item.zfryxm != null" >
            #{item.zfryxm,jdbcType=VARCHAR},
        </if>
        <if test="item.zfsj != null" >
            #{item.zfsj,jdbcType=TIMESTAMP},
        </if>
        <if test="item.ylyzxh != null" >
            #{item.ylyzxh,jdbcType=VARCHAR},
        </if>
        <if test="item.fyjzdh != null" >
            #{item.fyjzdh,jdbcType=VARCHAR},
        </if>
        <if test="item.ycuuid != null" >
            #{item.ycuuid,jdbcType=VARCHAR},
        </if>
    </trim>
    </foreach>
  </insert>


   <insert id="brfyInsert" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
    insert into ZYB_BRFY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="fylb != null" >
        FYLB,
      </if>
      <if test="fylbmc != null" >
        FYLBMC,
      </if>
      <if test="mxfyxmbm != null" >
        MXFYXMBM,
      </if>
      <if test="mxfyxmmc != null" >
        MXFYXMMC,
      </if>
      <if test="zhfybh != null" >
        ZHFYBH,
      </if>
      <if test="zhfymc != null" >
        ZHFYMC,
      </if>
      <if test="fydj != null" >
        FYDJ,
      </if>
      <if test="fysl != null" >
        FYSL,
      </if>
      <if test="fyje != null" >
        FYJE,
      </if>
      <if test="sfrq != null" >
        SFRQ,
      </if>
      <if test="ytsl != null" >
        YTSL,
      </if>
      <if test="zyh != null" >
        ZYH,
      </if>
      <if test="zyks != null" >
        ZYKS,
      </if>
      <if test="zyksmc != null" >
        ZYKSMC,
      </if>
      <if test="zyys != null" >
        ZYYS,
      </if>
      <if test="zyysxm != null" >
        ZYYSXM,
      </if>
      <if test="ysbm != null" >
        YSBM,
      </if>
      <if test="ysxm != null" >
        YSXM,
      </if>
      <if test="ysks != null" >
        YSKS,
      </if>
      <if test="ysksmc != null" >
        YSKSMC,
      </if>
      <if test="zxks != null" >
        ZXKS,
      </if>
      <if test="zxksmc != null" >
        ZXKSMC,
      </if>
      <if test="yzhm != null" >
        YZHM,
      </if>
      <if test="yzxh != null" >
        YZXH,
      </if>
      <if test="bydxh != null" >
        BYDXH,
      </if>
      <if test="yfbm != null" >
        YFBM,
      </if>
      <if test="yfmc != null" >
        YFMC,
      </if>
      <if test="sftf != null" >
        SFTF,
      </if>
      <if test="tfid != null" >
        TFID,
      </if>
      <if test="yxbz != null" >
        YXBZ,
      </if>
      <if test="ryjsjlid != null" >
        RYJSJLID,
      </if>
      <if test="ryjkpzh != null" >
        RYJKPZH,
      </if>
      <if test="djrq != null" >
        DJRQ,
      </if>
      <if test="bzsm != null" >
        BZSM,
      </if>
      <if test="xjjzbz != null" >
        XJJZBZ,
      </if>
      <if test="czybm != null" >
        CZYBM,
      </if>
      <if test="czyxm != null" >
        CZYXM,
      </if>
      <if test="yzxhid != null" >
        YZXHID,
      </if>
      <if test="yzxmmc != null" >
        YZXMMC,
      </if>
       <if test="brid != null" >
        BRID,
      </if>
      <if test="yljgbm != null" >
        YLJGBM,
      </if>
      <if test="fphm != null" >
        FPHM,
      </if>
      <if test="ksbm != null" >
          KSBM,
      </if>
      <if test="ksmc != null" >
        KSMC,
      </if>
        <if test="zfbz != null" >
            ZFBZ,
        </if>
        <if test="zfry != null" >
            ZFRY,
        </if>
        <if test="zfryxm != null" >
            ZFRYXM,
        </if>
        <if test="zfsj != null" >
            ZFSJ,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="fylb != null" >
        #{fylb,jdbcType=VARCHAR},
      </if>
      <if test="fylbmc != null" >
        #{fylbmc,jdbcType=VARCHAR},
      </if>
      <if test="mxfyxmbm != null" >
        #{mxfyxmbm,jdbcType=VARCHAR},
      </if>
      <if test="mxfyxmmc != null" >
        #{mxfyxmmc,jdbcType=VARCHAR},
      </if>
      <if test="zhfybh != null" >
        #{zhfybh,jdbcType=VARCHAR},
      </if>
      <if test="zhfymc != null" >
        #{zhfymc,jdbcType=VARCHAR},
      </if>
      <if test="fydj != null" >
        #{fydj,jdbcType=DECIMAL},
      </if>
      <if test="fysl != null" >
        #{fysl,jdbcType=DECIMAL},
      </if>
      <if test="fyje != null" >
        #{fyje,jdbcType=DECIMAL},
      </if>
      <if test="sfrq != null" >
        #{sfrq,jdbcType=TIMESTAMP},
      </if>
      <if test="ytsl != null" >
        #{ytsl,jdbcType=DECIMAL},
      </if>
      <if test="zyh != null" >
        #{zyh,jdbcType=VARCHAR},
      </if>
      <if test="zyks != null" >
        #{zyks,jdbcType=VARCHAR},
      </if>
      <if test="zyksmc != null" >
        #{zyksmc,jdbcType=VARCHAR},
      </if>
      <if test="zyys != null" >
        #{zyys,jdbcType=VARCHAR},
      </if>
      <if test="zyysxm != null" >
        #{zyysxm,jdbcType=VARCHAR},
      </if>
      <if test="ysbm != null" >
        #{ysbm,jdbcType=VARCHAR},
      </if>
      <if test="ysxm != null" >
        #{ysxm,jdbcType=VARCHAR},
      </if>
      <if test="ysks != null" >
        #{ysks,jdbcType=VARCHAR},
      </if>
      <if test="ysksmc != null" >
        #{ysksmc,jdbcType=VARCHAR},
      </if>
      <if test="zxks != null" >
        #{zxks,jdbcType=VARCHAR},
      </if>
      <if test="zxksmc != null" >
        #{zxksmc,jdbcType=VARCHAR},
      </if>
      <if test="yzhm != null" >
        #{yzhm,jdbcType=VARCHAR},
      </if>
      <if test="yzxh != null" >
        #{yzxh,jdbcType=VARCHAR},
      </if>
      <if test="bydxh != null" >
        #{bydxh,jdbcType=VARCHAR},
      </if>
      <if test="yfbm != null" >
        #{yfbm,jdbcType=VARCHAR},
      </if>
      <if test="yfmc != null" >
        #{yfmc,jdbcType=VARCHAR},
      </if>
      <if test="sftf != null" >
        #{sftf,jdbcType=VARCHAR},
      </if>
      <if test="tfid != null" >
        #{tfid,jdbcType=VARCHAR},
      </if>
      <if test="yxbz != null" >
        #{yxbz,jdbcType=VARCHAR},
      </if>
      <if test="ryjsjlid != null" >
        #{ryjsjlid,jdbcType=VARCHAR},
      </if>
      <if test="ryjkpzh != null" >
        #{ryjkpzh,jdbcType=VARCHAR},
      </if>
      <if test="djrq != null" >
        #{djrq,jdbcType=TIMESTAMP},
      </if>
      <if test="bzsm != null" >
        #{bzsm,jdbcType=VARCHAR},
      </if>
      <if test="xjjzbz != null" >
        #{xjjzbz,jdbcType=VARCHAR},
      </if>
      <if test="czybm != null" >
        #{czybm,jdbcType=VARCHAR},
      </if>
      <if test="czyxm != null" >
        #{czyxm,jdbcType=VARCHAR},
      </if>
      <if test="yzxhid != null" >
        #{yzxhid,jdbcType=VARCHAR},
      </if>
      <if test="yzxmmc != null" >
        #{yzxmmc,jdbcType=VARCHAR},
      </if>
       <if test="brid != null" >
        #{brid,jdbcType=VARCHAR},
      </if>
      <if test="yljgbm != null" >
        #{yljgbm,jdbcType=VARCHAR},
      </if>
      <if test="fphm != null" >
        #{fphm,jdbcType=VARCHAR},
      </if>
      <if test="ksbm != null" >
        #{ksbm,jdbcType=VARCHAR},
      </if>
      <if test="ksmc != null" >
        #{ksmc,jdbcType=VARCHAR},
      </if>
        <if test="zfbz != null" >
            #{zfbz,jdbcType=VARCHAR},
        </if>
        <if test="zfry != null" >
            #{zfry,jdbcType=VARCHAR},
        </if>
        <if test="zfryxm != null" >
            #{zfryxm,jdbcType=VARCHAR},
        </if>
        <if test="zfsj != null" >
            #{zfsj,jdbcType=TIMESTAMP},
        </if>
    </trim>
  </insert>

 <!-- 获取患者记帐限额 -->
 <select id="queryJzxe" resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_jzxeModel"
     parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_jzxeModel">
	 select b.*,yjje + dbje + zyjzxe - fyje jzxe from (
	select zyh,sum(zyjzxe) zyjzxe,sum(dbje) dbje,sum(fyje) fyje ,sum(yjje) yjje from (
	select zyh,fb.zyjzxe,dj.dbje,0 fyje,0 yjje  from zyb_rydj dj
	left join gyb_brfb fb on fb.fbbm = dj.brfb and fb.yljgbm = dj.yljgbm
	where dj.ifzf = '0' and dj.zyzt = '0' and dj.bqcybz = '0' and dj.yljgbm =  #{yljgbm,jdbcType=VARCHAR}
	<if test="searchzyh != null">
	    and  dj.zyh in
		<foreach item="item" index="index" collection="searchzyh" open="(" separator="," close=")">
		    #{item}
		 </foreach>
    </if>
	union all
	select zyh,0 zyjzxe,0 dbje,fyje,0 yjje from zyb_brfy  where yxbz = '1' and yljgbm =  #{yljgbm,jdbcType=VARCHAR}
		<if test="searchzyh != null">
		    and  zyh in
			<foreach item="item" index="index" collection="searchzyh" open="(" separator="," close=")">
			    #{item}
			 </foreach>
        </if>
	union all
	select yjjl.zyh,
	       0 zyjzxe,
	       0 dbje,
	       0 fyje,
	       case when brfb.jzbl is null then yjjl.yjje
                when brfb.jzbl &lt;= 1 then yjjl.yjje
                else yjjl.yjje * brfb.jzbl
                end yjje
	      from
	          zyb_yjjl yjjl
	       inner join zyb_rydj rydj on yjjl.zyh = rydj.zyh and yjjl.yljgbm = rydj.yljgbm
	       left  join gyb_brfb brfb on brfb.fbbm = rydj.brfb and brfb.yljgbm = rydj.yljgbm
      where yjjl.yljgbm =  #{yljgbm,jdbcType=VARCHAR}
		<if test="searchzyh != null">
		    and  yjjl.zyh in
			<foreach item="item" index="index" collection="searchzyh" open="(" separator="," close=")">
			    #{item}
			 </foreach>
		</if>
	) a group by zyh
	) b
 </select>

 <select id="fyjzShow" resultType="double" parameterType="com.supx.csp.api.zygl.crygl.pojo.Zyb_rydjModel">
 	select sum(fyje) from zyb_brfy
 	where ryjsjlid is not null and yljgbm=#{yljgbm}
 	<if test="ryks != null and ryks != ''">
 		and zyks = #{ryks,jdbcType=VARCHAR}
 	</if>
 	<if test="czybm != null and czybm != ''">
 		and czybm = #{czybm,jdbcType=VARCHAR}
 	</if>
 	<if test="beginrq != null">
 		and sfrq &gt;= #{beginrq,jdbcType=TIMESTAMP}
 	</if>
 	<if test="endrq != null">
 		and sfrq &lt; #{endrq,jdbcType=TIMESTAMP}
 	</if>
 	and zfbz = '0' and yljgbm = #{yljgbm,jdbcType=VARCHAR}
 </select>

    <!-- 查询是否有已经退费记录 -->
    <select id="queryBrfySftf" resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" parameterType="java.util.List">
        select * from zyb_brfy where sftf = '1' and fyid in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.fyid}
        </foreach>
        and yljgbm in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.yljgbm}
        </foreach>
    </select>

    <update id="updateByModel" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel">
            update ZYB_BRFY
                <set>
                    <if test="zfbz != null">
                        ZFBZ = #{zfbz,jdbcType=VARCHAR},
                    </if>
                    <if test="zfry != null">
                        ZFRY = #{zfry,jdbcType=VARCHAR},
                    </if>
                    <if test="zfryxm != null">
                        ZFRYXM = #{zfryxm,jdbcType=VARCHAR},
                    </if>
                    <if test="zfsj != null">
                        ZFSJ = #{zfsj,jdbcType=DATE},
                    </if>
                    <if test="yzhm != null">
                        YZHM = #{yzhm,jdbcType=VARCHAR},
                    </if>
                    <if test="yxbz != null">
                        YXBZ = #{yxbz,jdbcType=VARCHAR},
                    </if>
                    <if test="sftf != null">
                        SFTF = #{sftf,jdbcType=VARCHAR},
                    </if>
                    <if test="tfid != null">
                        TFID = #{tfid,jdbcType=VARCHAR},
                    </if>
                </set>
            where YLJGBM =  #{yljgbm,jdbcType=VARCHAR}
            and ZYH = #{zyh,jdbcType=VARCHAR} AND ZFBZ = '0' AND YXBZ = '1'
        <if test="fyid != null">
            and FYID = #{fyid,jdbcType=VARCHAR}
        </if>
        <if test="yzhm != null">
            and YZHM = #{yzhm,jdbcType=VARCHAR}
        </if>
    </update>

    <!-- 根据检验医嘱序号ID查询检验序号 -->
    <select id="queryJyxh" resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
    select Distinct jyxh,zyh from zyb_brfy brfy
    where brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR} and jyxh is not null
    <if test="searchxhid != null">
        and  brfy.yzxh in
        <foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
  </select>

    <!-- 根据根据医疗序号ID，检验序号查询检验序号是不否有一次没有全部取消 -->
    <select id="queryJyxhqx" resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
        select Distinct jyxh,zyh,yzxmmc,to_char(sfrq,'yyyy-mm-dd') bzsm from zyb_brfy brfy
        where brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR} and jyxh is not null
        <if test="searchxhid != null">
            and  brfy.yzxh not in
            <foreach item="item" index="index" collection="searchxhid" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchjyxh != null">
            and  brfy.jyxh in
            <foreach item="item" index="index" collection="searchjyxh" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 根据住院号查询费用是否异常 -->
    <select id="queryFyCheck" resultType="String" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
        select decode(sum(fyje)-sum(jsje),0,'0','1') ischeck from (
        Select Sum(Fyje) Fyje,0 Jsje  From Zyb_Brfy Where Zyh = #{zyh,jdbcType=VARCHAR} And Yxbz = '1' AND ZFBZ = '0' and yljgbm = #{yljgbm,jdbcType=VARCHAR}
        union all
        Select 0 Fyje,Sum(Fyje) Jsje From Zyb_Brfy Where Zyh = #{zyh,jdbcType=VARCHAR} And Yxbz = '1'  And Sftf = '0' AND ZFBZ = '0'
          and yljgbm = #{yljgbm,jdbcType=VARCHAR}
        )
    </select>

    <select id="queryLszl" resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel">
        select * from (
        select brfy.zyh,brfy.fyid,brfy.mxfyxmmc,brfy.fysl,brfy.fydj,brfy.fyje,brfy.zyysxm,brfy.zyksmc,brfy.djrq,brfy.zxksmc,brfy.kskfrq,brfy.kskfryxm,brxx.brxm,
        '1' xmlx ,brfy.ylyzxh ylyzxh,substr(brfy.yzxhid,instr(brfy.yzxhid,'_',-1)+1) yzmxxh,zxd.zlxmmc yzxmmc from zyb_brfy brfy
        inner join hsz_yz_ylzxd zxd on zxd.zyh = brfy.zyh and zxd.xhid = brfy.yzxhid
        inner join zyb_rydj rydj on brfy.zyh = rydj.zyh and brfy.yljgbm = rydj.yljgbm
        inner join gyb_brjbxx brxx on rydj.brid = brxx.brid and rydj.yljgbm = brxx.yljgbm
        where brfy.yfbm is null and brfy.zfbz='0' and brfy.sftf='0' and brfy.yxbz=#{yxbz,jdbcType=VARCHAR}
        and brfy.yljgbm =#{yljgbm,jdbcType=VARCHAR} and brfy.zxks = #{zxks,jdbcType=VARCHAR}
        and rydj.zyzt = '0' and rydj.bqcybz='0' and rydj.ifzf='0'
        <if test="tjbm != null and tjbm != '' ">
            <choose>
                <when test="tjbm == '2'.toString()">
                    and brfy.zyks = #{zxks,jdbcType=VARCHAR}
                </when>
                <when test="tjbm == '3'.toString()">
                    <![CDATA[ and brfy.zyks <> #{zxks,jdbcType=VARCHAR} ]]>
                </when>
            </choose>
        </if>
        <if test="parm != null and parm != '' ">
            and (
            rydj.zyh like '%'||#{parm,jdbcType=VARCHAR}||'%'
            or brxx.brxm like '%'||#{parm,jdbcType=VARCHAR}||'%'
            or brfy.mxfyxmmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
            )
        </if>
        <if test="yzlx !=null and yzlx !=''">
            and zxd.yzlx = #{yzlx,jdbcType=VARCHAR}
        </if>
        <if test="beginrq !=null">
            and brfy.djrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="endrq !=null">
            and brfy.djrq &lt;= #{endrq,jdbcType=DATE}
        </if>

        )
        order by zyh,djrq,yzxmmc
    </select>


    <!-- 修改病人费用 -->
    <update id="updateBrfy" parameterType="java.util.List" >
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            update ZYB_BRFY
            <set >
                <if test="item.fylb != null" >
                    FYLB = #{item.fylb,jdbcType=VARCHAR},
                </if>
                <if test="item.fylbmc != null" >
                    FYLBMC = #{item.fylbmc,jdbcType=VARCHAR},
                </if>
                <if test="item.mxfyxmbm != null" >
                    MXFYXMBM = #{item.mxfyxmbm,jdbcType=VARCHAR},
                </if>
                <if test="item.mxfyxmmc != null" >
                    MXFYXMMC = #{item.mxfyxmmc,jdbcType=VARCHAR},
                </if>
                <if test="item.zhfybh != null" >
                    ZHFYBH = #{item.zhfybh,jdbcType=VARCHAR},
                </if>
                <if test="item.zhfymc != null" >
                    ZHFYMC = #{item.zhfymc,jdbcType=VARCHAR},
                </if>
                <if test="item.fydj != null" >
                    FYDJ = #{item.fydj,jdbcType=DECIMAL},
                </if>
                <if test="item.fysl != null" >
                    FYSL = #{item.fysl,jdbcType=DECIMAL},
                </if>
                <if test="item.fyje != null" >
                    FYJE = #{item.fyje,jdbcType=DECIMAL},
                </if>
                <if test="item.sfrq != null" >
                    SFRQ = #{item.sfrq,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ytsl != null" >
                    YTSL = #{item.ytsl,jdbcType=DECIMAL},
                </if>
                <if test="item.zyh != null" >
                    ZYH = #{item.zyh,jdbcType=VARCHAR},
                </if>
                <if test="item.zyks != null" >
                    ZYKS = #{item.zyks,jdbcType=VARCHAR},
                </if>
                <if test="item.zyksmc != null" >
                    ZYKSMC = #{item.zyksmc,jdbcType=VARCHAR},
                </if>
                <if test="item.zyys != null" >
                    ZYYS = #{item.zyys,jdbcType=VARCHAR},
                </if>
                <if test="item.zyysxm != null" >
                    ZYYSXM = #{item.zyysxm,jdbcType=VARCHAR},
                </if>
                <if test="item.ysbm != null" >
                    YSBM = #{item.ysbm,jdbcType=VARCHAR},
                </if>
                <if test="item.ysxm != null" >
                    YSXM = #{item.ysxm,jdbcType=VARCHAR},
                </if>
                <if test="item.ysks != null" >
                    YSKS = #{item.ysks,jdbcType=VARCHAR},
                </if>
                <if test="item.ysksmc != null" >
                    YSKSMC = #{item.ysksmc,jdbcType=VARCHAR},
                </if>
                <if test="item.zxks != null" >
                    ZXKS = #{item.zxks,jdbcType=VARCHAR},
                </if>
                <if test="item.zxksmc != null" >
                    ZXKSMC = #{item.zxksmc,jdbcType=VARCHAR},
                </if>
                <if test="item.yzhm != null" >
                    YZHM = #{item.yzhm,jdbcType=VARCHAR},
                </if>
                <if test="item.yzxh != null" >
                    YZXH = #{item.yzxh,jdbcType=VARCHAR},
                </if>
                <if test="item.bydxh != null" >
                    BYDXH = #{item.bydxh,jdbcType=VARCHAR},
                </if>
                <if test="item.yfbm != null" >
                    YFBM = #{item.yfbm,jdbcType=VARCHAR},
                </if>
                <if test="item.yfmc != null" >
                    YFMC = #{item.yfmc,jdbcType=VARCHAR},
                </if>
                <if test="item.sftf != null" >
                    SFTF = #{item.sftf,jdbcType=VARCHAR},
                </if>
                <if test="item.tfid != null" >
                    TFID = #{item.tfid,jdbcType=VARCHAR},
                </if>
                <if test="item.yxbz != null" >
                    YXBZ = #{item.yxbz,jdbcType=VARCHAR},zxyxbz = #{item.yxbz,jdbcType=VARCHAR},
                </if>
                <if test="item.ryjsjlid != null" >
                    RYJSJLID = #{item.ryjsjlid,jdbcType=VARCHAR},
                </if>
                <if test="item.ryjkpzh != null" >
                    RYJKPZH = #{item.ryjkpzh,jdbcType=VARCHAR},
                </if>
                <if test="item.djrq != null" >
                    DJRQ = #{item.djrq,jdbcType=TIMESTAMP},
                </if>
                <if test="item.bzsm != null" >
                    BZSM = #{item.bzsm,jdbcType=VARCHAR},
                </if>
                <if test="item.xjjzbz != null" >
                    XJJZBZ = #{item.xjjzbz,jdbcType=VARCHAR},
                </if>
                <if test="item.czybm != null" >
                    CZYBM = #{item.czybm,jdbcType=VARCHAR},
                </if>
                <if test="item.czyxm != null" >
                    CZYXM = #{item.czyxm,jdbcType=VARCHAR},
                </if>
                <if test="item.yzxhid != null" >
                    YZXHID = #{item.yzxhid,jdbcType=VARCHAR},
                </if>
                <if test="item.yzxmmc != null" >
                    YZXMMC = #{item.yzxmmc,jdbcType=VARCHAR},
                </if>
                <if test="item.brid != null" >
                    BRID = #{item.brid,jdbcType=VARCHAR},
                </if>
                <if test="item.fphm != null" >
                    FPHM = #{item.fphm,jdbcType=VARCHAR},
                </if>
                <if test="item.zfbz != null" >
                    ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
                </if>
                <if test="item.zfsj != null" >
                    ZFSJ = #{item.zfsj,jdbcType=DATE},
                </if>
                <if test="item.zfry != null" >
                    ZFRY = #{item.zfry,jdbcType=VARCHAR},
                </if>
                <if test="item.zfryxm != null" >
                    ZFRYXM = #{item.zfryxm,jdbcType=VARCHAR},
                </if>
                <if test="item.ksbm != null" >
                    KSBM = #{item.ksbm,jdbcType=VARCHAR},
                </if>
                <if test="item.ksmc != null" >
                    KSMC = #{item.ksmc,jdbcType=VARCHAR},
                </if>
            </set>
            where YLJGBM =  #{item.yljgbm,jdbcType=VARCHAR}
            and yzxh = #{item.yzxh,jdbcType=VARCHAR}
            <if test="item.zyh != null" >
                and ZYH = #{item.zyh,jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>
    <select id="historyJzxx" resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel">
        select fy.zyh,fy.fyjzdh,brxx.brxm from zyb_brfy fy
        inner join gyb_brjbxx brxx on fy.brid = brxx.brid and fy.yljgbm = brxx.yljgbm
        where fy.fyjzdh is not null
        and fy.yfbm is null
        <if test="parm != null and parm != '' ">
            and (
            fy.zyh like '%'||#{parm,jdbcType=VARCHAR}||'%'
            or brxx.brxm like '%'||#{parm,jdbcType=VARCHAR}||'%'
            or fy.fyjzdh like '%'||#{parm,jdbcType=VARCHAR}||'%'
            )
        </if>
        <if test="zyh !=null and zyh !=''">
            and fy.zyh = #{zyh,jdbcType=VARCHAR}
        </if>
        <if test="zyks !=null and zyks !=''">
            and fy.zyks = #{zyks,jdbcType=VARCHAR}
        </if>
        <if test="beginrq !=null">
            and fy.djrq &gt;= #{beginrq,jdbcType=DATE}
        </if>
        <if test="endrq !=null">
            and fy.djrq &lt;= #{endrq,jdbcType=DATE}
        </if>
        group by  fy.zyh,fy.fyjzdh,brxx.brxm
    </select>

    <select id="historyJzMxxx" resultMap="JzMxxxResultMap" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel">
        select a.* from (
      select '0' as zhfy,
      mxfyxm.mxfybm mxfybm,
      mxfyxm.mxfymc mxfymc,
      mxfyxm.pydm pydm,
      mxfyxm.fydj fydj,
      mxfyxm.fygg fygg,
      mxfyxm.lbbm lbbm,
      fylb.lbmc fylbmc,
      mxfyxm.fylx fylx,
      mxfyxm.nbtclb nbtclb,
      tclb.tclbmc tclbmc,
      fy.zxks hsks,
      ksbm.ksmc zxksmc,
      mxfyxm.sfgd sfgd,
      mxfyxm.ypfy ypfy,
      mxfyxm.ffylb ffylb,
      mxfyxm.yhbl yhbl,
      mxfyxm.fpfs fpfs,
      mxfyxm.fpks fpks,
      fy.fysl sl
      from gyb_mxfyxm mxfyxm
      inner join zyb_brfy fy on fy.mxfyxmbm = mxfyxm.mxfybm
      inner join gyb_fylb fylb
      on fylb.lbbm=mxfyxm.lbbm
      and fylb.yljgbm=mxfyxm.yljgbm
      inner join gyb_tclb tclb
      on tclb.tclbbm=mxfyxm.fytclb
      and tclb.yljgbm=mxfyxm.yljgbm
      left join gyb_ksbm ksbm
      on ksbm.ksbm=fy.zxks
      and ksbm.yljgbm=mxfyxm.yljgbm and ksbm.tybz='0'

       WHERE mxfyxm.tybz='0' and fylb.tybz='0' and tclb.tybz='0' and mxfyxm.yljgbm='000001'
        and fy.zyh=#{zyh,jdbcType=VARCHAR} and fy.fyjzdh=#{fyjzdh,jdbcType=VARCHAR}
      )a
       ORDER BY length(a.mxfymc),a.zhfy desc,a.mxfybm asc

    </select>

    <!-- 刷卡或者根据住院号或者根据床位号来查询病人费用集合 -->
    <select id="queryxmzs" resultMap="BaseResultMapJz" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
        select * from (
        select brfy.zyh,brfy.fylb,brfy.fylbmc,brfy.mxfyxmbm,brfy.mxfyxmmc,brfy.fydj,sum(brfy.fysl) fysl ,sum(brfy.fyje) fyje from zyb_brfy brfy
        inner join zyb_rydj rydj on rydj.zyh=brfy.zyh and rydj.yljgbm=brfy.yljgbm
        inner join gyb_mxfyxm xm on xm.mxfybm=brfy.mxfyxmbm and xm.yljgbm=brfy.yljgbm
        <where>
            brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            and brfy.zfbz=0 and rydj.ifzf='0' and rydj.zyzt='0' and brfy.ryjsjlid is null and brfy.yxbz='1' and brfy.sftf='0'
            and brfy.yfbm is null
            <if test="zyh != null and zyh !='' ">
                and (brfy.zyh = #{zyh,jdbcType=VARCHAR})
            </if>
            <if test="ylklx != null and ylklx != '' and ylkh != null and ylkh != ''">
                and exists (select 1 from gyb_brylkxx ylk where ylk.brid=rydj.brid
                and (ylk.ylklx = #{ylklx,jdbcType=VARCHAR})
                and (ylk.ylkh = #{ylkh,jdbcType=VARCHAR}))
            </if>
            <if test="rycwbh != null and rycwbh !='' ">
                and (rydj.rycwbh = #{rycwbh,jdbcType=VARCHAR})
            </if>
            <if test="fylb != null and fylb !='' and fylb != 'all'.toString()">
                and (brfy.fylb = #{fylb,jdbcType=VARCHAR})
            </if>
            <if test="beginrq != null and beginrq!=''">
                and (brfy.sfrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="endrq != null and endrq!=''">
                and (brfy.sfrq &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="zxks != null and zxks != ''">
                and (brfy.ZYKS = #{zxks})
            </if>
            <if test="parm != null and parm !=''">
                and (brfy.mxfyxmmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
                or brfy.mxfyxmbm like '%'||#{parm,jdbcType=VARCHAR}||'%'
                or brfy.yzxmmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
                or upper(xm.pydm) like '%'||upper(#{parm,jdbcType=VARCHAR})||'%')
            </if>
        </where>
    group by brfy.zyh,brfy.fylb,brfy.fylbmc,brfy.mxfyxmbm,brfy.mxfyxmmc,brfy.fydj
        union all
        select brfy.zyh,brfy.fylb,brfy.mxfyxmmc fylbmc,zd.ypbm mxfyxmbm,zd.ypmc mxfyxmmc,pf.yplj fydj,sum(pf.cfyl) fysl ,sum(pf.yplj*pf.cfyl) fyje from zyb_brfy brfy
        inner join zyb_rydj rydj on rydj.zyh=brfy.zyh and rydj.yljgbm=brfy.yljgbm
        inner join gyb_mxfyxm xm on xm.mxfybm=brfy.mxfyxmbm and xm.yljgbm=brfy.yljgbm
        left join yfb_ypcf cf on  cf.cfh = brfy.yzhm and cf.yljgbm = brfy.yljgbm
        left join yfb_yppf pf on cf.cfh = pf.cfh and cf.yljgbm = pf.yljgbm
        left join ykb_ypzd zd on zd.ypbm = pf.ypbm and zd.yljgbm = pf.yljgbm
        <where>
            brfy.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            and brfy.zfbz=0 and rydj.ifzf='0' and rydj.zyzt='0' and brfy.ryjsjlid is null and brfy.yxbz='1' and brfy.sftf='0'
            and brfy.yfbm is not null and  bydxh is null And brfy.fylb not in ('210')
            <if test="zyh != null and zyh !='' ">
                and (brfy.zyh = #{zyh,jdbcType=VARCHAR})
            </if>
            <if test="ylklx != null and ylklx != '' and ylkh != null and ylkh != ''">
                and exists (select 1 from gyb_brylkxx ylk where ylk.brid=rydj.brid
                and (ylk.ylklx = #{ylklx,jdbcType=VARCHAR})
                and (ylk.ylkh = #{ylkh,jdbcType=VARCHAR}))
            </if>
            <if test="rycwbh != null and rycwbh !='' ">
                and (rydj.rycwbh = #{rycwbh,jdbcType=VARCHAR})
            </if>
            <if test="fylb != null and fylb !='' and fylb != 'all'.toString()">
                and (brfy.fylb = #{fylb,jdbcType=VARCHAR})
            </if>
            <if test="beginrq != null and beginrq!=''">
                and (brfy.sfrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="endrq != null and endrq!=''">
                and (brfy.sfrq &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="zxks != null and zxks != ''">
                and (brfy.ZYKS = #{zxks})
            </if>
            <if test="parm != null and parm !=''">
                and (zd.ypmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
                or zd.ypbm like '%'||#{parm,jdbcType=VARCHAR}||'%'
                or brfy.yzxmmc like '%'||#{parm,jdbcType=VARCHAR}||'%'
                or upper(xm.pydm) like '%'||upper(#{parm,jdbcType=VARCHAR})||'%')
            </if>
        </where>
        group by brfy.zyh,brfy.fylb,brfy.mxfyxmmc,zd.ypbm,zd.ypmc,pf.yplj
        )
        ORDER BY fylb
    </select>

    <select id="getFyLis" resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
        select a.* from zyb_brfy a
            inner join zyb_rydj b on a.zyh = b.zyh and b.zyzt = '0' and b.ifzf ='0'
             where a.yxbz='0' and a.zfbz='0' and a.sftf='0'
                    and a.jyxh is not null
    </select>



    <select id="getDqfy" resultType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel">
        select fy.* from zyb_brfy fy

        where fy.fyjzdh is  null
        and fy.yfbm is null

        <if test="zyh !=null and zyh !=''">
            and fy.zyh = #{zyh,jdbcType=VARCHAR}
        </if>
        <if test="yzxh !=null and yzxh !=''">
            and fy.yzxh = #{yzxh,jdbcType=VARCHAR}
        </if>
        <if test="sfrq !=null and sfrq !=''">
            and to_char(fy.sfrq,'yyyy-mm-dd') = to_char(#{sfrq,jdbcType=DATE},'yyyy-mm-dd')
        </if>

    </select>

    <!-- 修改病人费用 -->
    <update id="updateHl" parameterType="com.supx.csp.api.zygl.fygl.pojo.Zyb_brfyModel" >
        update zyb_brfy set  zfbz ='1'
        where
        to_char(zyb_brfy.sfrq,'yyyy-mm-dd') = to_char(#{sfrq,jdbcType=TIMESTAMP},'yyyy-mm-dd')
        and mxfyxmbm in (
        '120100003',
        '120100004',
        '120100005'
        )
            and   ZYH = #{zyh,jdbcType=VARCHAR}
    </update>


</mapper>
