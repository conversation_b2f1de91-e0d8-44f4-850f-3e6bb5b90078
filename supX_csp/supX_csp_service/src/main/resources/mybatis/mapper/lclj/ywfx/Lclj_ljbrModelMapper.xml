<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.lclj.ywfx.dao.New1Lclj_ljbrModelMapper" >


	<select id="queryLcljCylybrxx" parameterType="com.supx.csp.api.lclj.ywfx.pojo.Lclj_ljbrModel" resultType="com.supx.csp.api.lclj.ywfx.pojo.Lclj_ljbrModel">
		select
			  ljdj.ryljbm ljbm,ljdj.ryljmc ljmc,ljdj.rjks ksbm,ljdj.ryksmc ksmc,
			  rydj.rycwbh cwh,ljdj.zyh zyh,brxx.brid brid,brxx.brxm brxm,brxx.brxb brxb,
			  rydj.nl brnl,rydj.nldw brnldw,rydj.zyys zyys,rydj.zyysxm zyysxm,
			  ljdj.djrq rjrq,ljdj.djczy rjczy, ljdj.djczyxm rjczyxm, ljdj.ztbz zt,
			  rydj.ryrq ryrq,rydj.cyrq cyrq,brxx.jzdmc dz,pgjl.pgjg pgnr
			from lclj_djjl ljdj
			inner join zyb_rydj rydj on (ljdj.yljgbm = rydj.yljgbm and ljdj.zyh = rydj.zyh)
			inner join gyb_brjbxx brxx on (rydj.brid = brxx.brid and rydj.yljgbm = brxx.yljgbm)
			left join lclj_pgjl pgjl on (pgjl.yljgbm = ljdj.yljgbm and pgjl.zyh = ljdj.zyh)
		where ljdj.ztbz in ('2','3')
		    and ljdj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		<if test="ljbm != null and ljbm != ''">
			and ljdj.ryljbm = #{ljbm,jdbcType=VARCHAR}
		</if>
		<if test="ljmc != null and ljmc != ''">
			and ljdj.ryljmc = #{ljmc,jdbcType=VARCHAR}
		</if>
		<if test="parm != null and parm != ''">
			and (
					instr(brxx.brid,#{parm,jdbcType=VARCHAR}) &gt; 0
				 or instr(brxx.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
				 or instr(ljdj.zyh,#{parm,jdbcType=VARCHAR}) &gt; 0
				 )
		</if>
		<if test="endrq != null  ">
    		and rydj.cyrq &lt;= #{endrq,jdbcType=TIMESTAMP}
   		</if>
   		<if test="beginrq != null  ">
    		and rydj.ryrq &gt;= #{beginrq,jdbcType=TIMESTAMP}
   		</if>
	</select>

	<select id="queryLcljbzbrxx" parameterType="com.supx.csp.api.lclj.ywfx.pojo.Lclj_ljbrModel" resultType="com.supx.csp.api.lclj.ywfx.pojo.Lclj_ljbrModel">
		select
			  ljdj.ryljbm ljbm,ljdj.ryljmc ljmc,ljdj.rjks ksbm,ljdj.ryksmc ksmc,
			  rydj.rycwbh cwh,ljdj.zyh zyh,brxx.brid brid,brxx.brxm brxm,brxx.brxb brxb,
			  rydj.nl brnl,rydj.nldw brnldw,rydj.zyys zyys,rydj.zyysxm zyysxm,
			  ljdj.djrq rjrq,ljdj.djczy rjczy, ljdj.djczyxm rjczyxm, ljdj.ztbz zt,
			  rydj.ryrq ryrq,rydj.cyrq cyrq,brxx.jzdmc dz,pgjl.pgjg pgnr
			from lclj_djjl ljdj
			inner join zyb_rydj rydj on (ljdj.yljgbm = rydj.yljgbm and ljdj.zyh = rydj.zyh)
			inner join gyb_brjbxx brxx on (rydj.brid = brxx.brid and rydj.yljgbm = brxx.yljgbm)
			left join lclj_pgjl pgjl on (pgjl.yljgbm = ljdj.yljgbm and pgjl.zyh = ljdj.zyh)
		where ljdj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		<if test="ljbm != null and ljbm != ''">
			and ljdj.ryljbm = #{ljbm,jdbcType=VARCHAR}
		</if>
		<if test="ljmc != null and ljmc != ''">
			and ljdj.ryljmc = #{ljmc,jdbcType=VARCHAR}
		</if>
		<if test="zt != null and zt != ''">
			and ljdj.ztbz = #{zt,jdbcType=VARCHAR}
		</if>
		<if test="endrq != null  ">
    		and rydj.cyrq &lt;= #{endrq,jdbcType=TIMESTAMP}
   		</if>
   		<if test="beginrq != null  ">
    		and rydj.ryrq &gt;= #{beginrq,jdbcType=TIMESTAMP}
   		</if>
	</select>
</mapper>
