<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.lclj.rcyw.dao.New1Lclj_rjbrModelMapper" >
  <resultMap id="BaseResultMap" type="com.supx.csp.api.lclj.rcyw.pojo.Lclj_rjbrModel" >
    <result column="ZYH" property="zyh" jdbcType="VARCHAR" />
    <result column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
    <result column="BAH" property="bah" jdbcType="VARCHAR" />
    <result column="BRID" property="brid" jdbcType="VARCHAR" />
    <result column="BXLBBM" property="bxlbbm" jdbcType="VARCHAR" />
    <result column="BRFB" property="brfb" jdbcType="VARCHAR" />
    <result column="BRFBMC" property="brfbmc" jdbcType="VARCHAR" />
    <result column="YWCKBH" property="ywckbh" jdbcType="VARCHAR" />
    <result column="YWCKMC" property="ywckmc" jdbcType="VARCHAR" />
    <result column="CZYBM" property="czybm" jdbcType="VARCHAR" />
    <result column="CZYXM" property="czyxm" jdbcType="VARCHAR" />
    <result column="YBKH" property="ybkh" jdbcType="VARCHAR" />
    <result column="ZYZT" property="zyzt" jdbcType="VARCHAR" />
    <result column="RYKS" property="ryks" jdbcType="VARCHAR" />
    <result column="RYKSMC" property="ryksmc" jdbcType="VARCHAR" />
    <result column="RYRQ" property="ryrq" jdbcType="TIMESTAMP" />
    <result column="CYRQ" property="cyrq" jdbcType="TIMESTAMP" />
    <result column="BQCYBZ" property="bqcybz" jdbcType="VARCHAR" />
    <result column="BQCYRQ" property="bqcyrq" jdbcType="TIMESTAMP" />
    <result column="MZYS" property="mzys" jdbcType="VARCHAR" />
    <result column="MZYSXM" property="mzysxm" jdbcType="VARCHAR" />
    <result column="MZKS" property="mzks" jdbcType="VARCHAR" />
    <result column="MZKSMC" property="mzksmc" jdbcType="VARCHAR" />
    <result column="ZYYS" property="zyys" jdbcType="VARCHAR" />
    <result column="ZYYSXM" property="zyysxm" jdbcType="VARCHAR" />
    <result column="RYQK" property="ryqk" jdbcType="VARCHAR" />
    <result column="RYTJ" property="rytj" jdbcType="VARCHAR" />
    <result column="DBR" property="dbr" jdbcType="VARCHAR" />
    <result column="DBJE" property="dbje" jdbcType="DECIMAL" />
    <result column="BXBR" property="bxbr" jdbcType="VARCHAR" />
    <result column="RYCWBH" property="rycwbh" jdbcType="VARCHAR" />
    <result column="NL" property="nl" jdbcType="DECIMAL" />
    <result column="NLDW" property="nldw" jdbcType="VARCHAR" />
    <result column="YL" property="yl" jdbcType="DECIMAL" />
    <result column="YLDW" property="yldw" jdbcType="VARCHAR" />
    <result column="RYZDBM" property="ryzdbm" jdbcType="VARCHAR" />
    <result column="RYZDMC" property="ryzdmc" jdbcType="VARCHAR" />
    <result column="IFZF" property="ifzf" jdbcType="VARCHAR" />
    <result column="ZFRY" property="zfry" jdbcType="VARCHAR" />
    <result column="ZFRYXM" property="zfryxm" jdbcType="VARCHAR" />
    <result column="ZFRQ" property="zfrq" jdbcType="TIMESTAMP" />
    <result column="CYKS" property="cyks" jdbcType="VARCHAR" />
    <result column="CYKSMC" property="cyksmc" jdbcType="VARCHAR" />
    <result column="ZYCS" property="zycs" jdbcType="DECIMAL" />
    <result column="BXLBMC" property="bxlbmc" jdbcType="VARCHAR" />
    <result column="YLKLX" property="ylklx" jdbcType="VARCHAR" />
    <result column="YLKH" property="ylkh" jdbcType="VARCHAR" />
    <result column="RYCWID" property="rycwid" jdbcType="VARCHAR" />
    <result column="RYCWID" property="rycwid" jdbcType="VARCHAR" />
	<result column="LJDJID" property="ljdjid" jdbcType="VARCHAR" />
	<result column="BRXM" property="brxm" jdbcType="VARCHAR"/>
	<result column="BRDZ" property="brdz" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="Base_Column_List" >
    ZYH, YLJGBM, BAH, BRID, BXLBBM, BRFB, BRFBMC, YWCKBH, YWCKMC, CZYBM, CZYXM, YBKH,
    ZYZT, RYKS, RYKSMC, RYRQ, CYRQ, BQCYBZ, BQCYRQ, MZYS, MZYSXM, MZKS, MZKSMC, ZYYS,
    ZYYSXM, RYQK, RYTJ, DBR, DBJE, BXBR, RYCWBH, NL, NLDW, YL, YLDW, RYZDBM, RYZDMC,
    IFZF, ZFRY, ZFRYXM, ZFRQ, CYKS, CYKSMC, ZYCS, BXLBMC, YLKLX, YLKH, RYCWID ,LJDJID
  </sql>

  <select id="queryBr" parameterType="com.supx.csp.api.lclj.rcyw.pojo.Lclj_rjbrModel" resultMap="BaseResultMap">
		  select
		    zyb_rydj.ZYH, zyb_rydj.YLJGBM, zyb_rydj.BAH, zyb_rydj.BRID, zyb_rydj.BXLBBM, zyb_rydj.BRFB, zyb_rydj.BRFBMC,
		    zyb_rydj.YWCKBH, zyb_rydj.YWCKMC, zyb_rydj.CZYBM, zyb_rydj.CZYXM, zyb_rydj.YBKH,
		    zyb_rydj.ZYZT, zyb_rydj.RYKS, zyb_rydj.RYKSMC, zyb_rydj.RYRQ, zyb_rydj.CYRQ, zyb_rydj.BQCYBZ,
		    zyb_rydj.BQCYRQ, zyb_rydj.MZYS, zyb_rydj.MZYSXM, zyb_rydj.MZKS, zyb_rydj.MZKSMC, zyb_rydj.ZYYS,
		    zyb_rydj.ZYYSXM, zyb_rydj.RYQK, zyb_rydj.RYTJ, zyb_rydj.DBR, zyb_rydj.DBJE, zyb_rydj.BXBR,
		    zyb_rydj.RYCWBH, zyb_rydj.NL, zyb_rydj.NLDW, zyb_rydj.YL, zyb_rydj.YLDW, zyb_rydj.RYZDBM, zyb_rydj.RYZDMC,
		    zyb_rydj.IFZF, zyb_rydj.ZFRY, zyb_rydj.ZFRYXM, zyb_rydj.ZFRQ, zyb_rydj.CYKS,
		    zyb_rydj.CYKSMC, zyb_rydj.ZYCS, zyb_rydj.BXLBMC, zyb_rydj.YLKLX, zyb_rydj.YLKH, zyb_rydj.RYCWID ,lclj_djjl.LJDJID,
		    gyb_brjbxx.brxm BRXM,gyb_brjbxx.HKDZ BRDZ
		from zyb_rydj
		inner join gyb_brjbxx
		on (gyb_brjbxx.brid = zyb_rydj.brid and gyb_brjbxx.yljgbm = zyb_rydj.yljgbm)
		left join lclj_djjl
		on (zyb_rydj.yljgbm = lclj_djjl.yljgbm and lclj_djjl.zyh = zyb_rydj.zyh)
		where
		zyb_rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		<if test="parm != null and parm != ''">
			and (
				   instr(zyb_rydj.ZYH,#{parm,jdbcType=VARCHAR}) &gt; 0
				or instr(zyb_rydj.BAH,#{parm,jdbcType=VARCHAR}) &gt; 0
				or instr(zyb_rydj.BRID,#{parm,jdbcType=VARCHAR}) &gt; 0
				or instr(gyb_brjbxx.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
			)
		</if>
		<if test="zyzt != null and zyzt != ''">
			zyb_rydj.zyzt = #{zyzt,jdbcType=VARHCAR}
		</if>
		<if test="bqcybz != null and bqcybz != ''">
			zyb_rydj.bqcybz = #{bqcybz,jdbcType=VARCHAR}
		</if>
		<if test="zyys != null and zyys != ''">
			zyb_rydj.zyys = #{zyys,jdbcType=VARHCAR}
		</if>
		<if test="inorout == '0'">
			<if test="endrq != null  ">
	    		and zyb_rydj.RYRQ &lt;= #{endrq,jdbcType=TIMESTAMP}
	   		</if>
	   		<if test="beginrq != null  ">
	    		and zyb_rydj.RYRQ &gt;= #{beginrq,jdbcType=TIMESTAMP}
	   		</if>
		</if>
		<if test="inorout == '1'">
			<if test="endrq != null  ">
	    		and zyb_rydj.CYRQ &lt;= #{endrq,jdbcType=TIMESTAMP}
	   		</if>
	   		<if test="beginrq != null  ">
	    		and zyb_rydj.CYRQ &gt;= #{beginrq,jdbcType=TIMESTAMP}
	   		</if>
		</if>
   		order by zyb_rydj.RYCWID ${order}
  </select>

  <select id="queryRjbr" parameterType="com.supx.csp.api.lclj.rcyw.pojo.Lclj_rjbrModel" resultMap="BaseResultMap">
  		  select
		    zyb_rydj.ZYH, zyb_rydj.YLJGBM, zyb_rydj.BAH, zyb_rydj.BRID, zyb_rydj.BXLBBM, zyb_rydj.BRFB, zyb_rydj.BRFBMC,
		    zyb_rydj.YWCKBH, zyb_rydj.YWCKMC, zyb_rydj.CZYBM, zyb_rydj.CZYXM, zyb_rydj.YBKH,
		    zyb_rydj.ZYZT, zyb_rydj.RYKS, zyb_rydj.RYKSMC, zyb_rydj.RYRQ, zyb_rydj.CYRQ, zyb_rydj.BQCYBZ,
		    zyb_rydj.BQCYRQ, zyb_rydj.MZYS, zyb_rydj.MZYSXM, zyb_rydj.MZKS, zyb_rydj.MZKSMC, zyb_rydj.ZYYS,
		    zyb_rydj.ZYYSXM, zyb_rydj.RYQK, zyb_rydj.RYTJ, zyb_rydj.DBR, zyb_rydj.DBJE, zyb_rydj.BXBR,
		    zyb_rydj.RYCWBH, zyb_rydj.NL, zyb_rydj.NLDW, zyb_rydj.YL, zyb_rydj.YLDW, zyb_rydj.RYZDBM, zyb_rydj.RYZDMC,
		    zyb_rydj.IFZF, zyb_rydj.ZFRY, zyb_rydj.ZFRYXM, zyb_rydj.ZFRQ, zyb_rydj.CYKS,
		    zyb_rydj.CYKSMC, zyb_rydj.ZYCS, zyb_rydj.BXLBMC, zyb_rydj.YLKLX, zyb_rydj.YLKH, zyb_rydj.RYCWID ,lclj_djjl.LJDJID,
		    gyb_brjbxx.brxm BRXM,gyb_brjbxx.HKDZ BRDZ
		from zyb_rydj
		inner join gyb_brjbxx
		on (gyb_brjbxx.brid = zyb_rydj.brid and gyb_brjbxx.yljgbm = zyb_rydj.yljgbm)
		inner join lclj_djjl
		on (zyb_rydj.yljgbm = lclj_djjl.yljgbm and lclj_djjl.zyh = zyb_rydj.zyh)
		where
		zyb_rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		<if test="parm != null and parm != ''">
			and (
				   instr(zyb_rydj.ZYH,#{parm,jdbcType=VARCHAR}) &gt; 0
				or instr(zyb_rydj.BAH,#{parm,jdbcType=VARCHAR}) &gt; 0
				or instr(zyb_rydj.BRID,#{parm,jdbcType=VARCHAR}) &gt; 0
				or instr(gyb_brjbxx.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
			)
		</if>
		<if test="zyzt != null and zyzt != ''">
			zyb_rydj.zyzt = #{zyzt,jdbcType=VARHCAR}
		</if>
		<if test="bqcybz != null and bqcybz != ''">
			zyb_rydj.bqcybz = #{bqcybz,jdbcType=VARCHAR}
		</if>
		<if test="zyys != null and zyys != ''">
			zyb_rydj.zyys = #{zyys,jdbcType=VARHCAR}
		</if>
		<if test="inorout == '0'">
			<if test="endrq != null  ">
	    		and zyb_rydj.RYRQ &lt;= #{endrq,jdbcType=TIMESTAMP}
	   		</if>
	   		<if test="beginrq != null  ">
	    		and zyb_rydj.RYRQ &gt;= #{beginrq,jdbcType=TIMESTAMP}
	   		</if>
		</if>
		<if test="inorout == '1'">
			<if test="endrq != null  ">
	    		and zyb_rydj.CYRQ &lt;= #{endrq,jdbcType=TIMESTAMP}
	   		</if>
	   		<if test="beginrq != null  ">
	    		and zyb_rydj.CYRQ &gt;= #{beginrq,jdbcType=TIMESTAMP}
	   		</if>
		</if>
   		order by zyb_rydj.RYCWID ${order}
  </select>




</mapper>
