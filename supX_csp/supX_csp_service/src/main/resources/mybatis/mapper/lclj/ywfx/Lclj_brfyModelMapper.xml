<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.lclj.ywfx.dao.New1Lclj_brfyModelMapper" >


	<select id="queryLcljBrfy" parameterType="com.supx.csp.api.lclj.ywfx.pojo.Lclj_brfyModel" resultType="com.supx.csp.api.lclj.ywfx.pojo.Lclj_brfyModel">
		select djjl.ryljbm ljbm,djjl.ryljmc ljmc,brxx.brxm brxm,brfy.* from lclj_djjl djjl
			inner join zyb_brfy brfy on(djjl.yljgbm = brfy.yljgbm and djjl.zyh = brfy.zyh)
			inner join gyb_brjbxx brxx on (brfy.brid = brxx.brid and brfy.yljgbm = brxx.yljgbm)
			inner join zyb_rydj rydj on(brfy.zyh = rydj.zyh and brfy.yljgbm = rydj.yljgbm)
			where
				ztbz= '3'
				and djjl.yljgbm = #{yljgbm,jdbcType=VARCHAR}
				and brfy.SFTF = '0'
				and brfy.YXBZ = '1'
		<if test="zyh != null and zyh != ''">
			and djjl.zyh = #{zyh,jdbcType=VARCHAR}
		</if>
		<if test="ljbm != null and ljbm != ''">
			and djjl.ryljbm = #{ljbm,jdbcType=VARCHAR}
		</if>
		<if test="ljmc != null and ljmc != ''">
			and djjl.ryljmc =#{ljmc,jdbcType=VARCHAR}
		</if>

		<if test="endrq != null  ">
    		and rydj.cyrq &lt;= #{endrq,jdbcType=TIMESTAMP}
   		</if>
   		<if test="beginrq != null  ">
    		and rydj.ryrq &gt;= #{beginrq,jdbcType=TIMESTAMP}
   		</if>
	</select>


</mapper>
