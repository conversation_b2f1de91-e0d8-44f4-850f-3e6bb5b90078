<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.lclj.xtwh.dao.New1Lclj_Wh_Bdmb_HlModelMapper" >
  <resultMap id="BaseResultMap" type="com.supx.csp.api.lclj.xtwh.pojo.Lclj_Wh_Bdmb_HlModel" >
    <id column="XMBM" property="xmbm" jdbcType="VARCHAR" />
    <id column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
    <result column="XMMC" property="xmmc" jdbcType="VARCHAR" />
    <result column="RYLJBM" property="ryljbm" jdbcType="VARCHAR" />
    <result column="RYJDBM" property="ryjdbm" jdbcType="VARCHAR" />
    <result column="RYEJJDBM" property="ryejjdbm" jdbcType="VARCHAR" />
    <result column="XMDL" property="xmdl" jdbcType="VARCHAR" />
    <result column="XMLX" property="xmlx" jdbcType="VARCHAR" />
    <result column="XSXH" property="xsxh" jdbcType="DECIMAL" />
    <result column="XMZXDL" property="xmzxdl" jdbcType="VARCHAR" />
    <result column="XMZXXL" property="xmzxxl" jdbcType="VARCHAR" />
    <result column="XMZXFS" property="xmzxfs" jdbcType="VARCHAR" />
    <result column="XMXZFS" property="xmxzfs" jdbcType="VARCHAR" />
    <result column="XMPG" property="xmpg" jdbcType="VARCHAR" />
    <result column="KXX" property="kxx" jdbcType="VARCHAR" />
    <result column="CJRY" property="cjry" jdbcType="VARCHAR" />
    <result column="CJRQ" property="cjrq" jdbcType="TIMESTAMP" />
    <result column="SHBZ" property="shbz" jdbcType="VARCHAR" />
    <result column="SHRY" property="shry" jdbcType="VARCHAR" />
    <result column="SHRQ" property="shrq" jdbcType="TIMESTAMP" />
    <result column="RYJLMC" property="ryjlmc" jdbcType="VARCHAR" />
    <result column="RYJDMC" property="ryjdmc" jdbcType="VARCHAR" />
    <result column="RYEJJDMC" property="ryejjdmc" jdbcType="VARCHAR" />
    <result column="XMZXDLMC" property="xmzxdlmc" jdbcType="VARCHAR" />
    <result column="CJRYXM" property="cjryxm" jdbcType="VARCHAR" />
    <result column="SHRYXM" property="shryxm" jdbcType="VARCHAR" />
    <result column="XMZXXLMC" property="xmzxxlmc" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    XMBM, YLJGBM, XMMC, RYLJBM, RYJDBM, RYEJJDBM, XMDL, XMLX, XSXH, XMZXDL, XMZXXL, XMZXFS,
    XMXZFS, XMPG, KXX, CJRY, CJRQ, SHBZ, SHRY, SHRQ, RYJLMC, RYJDMC, RYEJJDMC, XMZXDLMC,
    CJRYXM, SHRYXM, XMZXXLMC
  </sql>

  <!-- 查询信息 -->
   <select id="queryLcljXtwhBdmbHl" resultType="com.supx.csp.api.lclj.xtwh.pojo.Lclj_Wh_Bdmb_HlModel" parameterType="com.supx.csp.api.lclj.xtwh.pojo.Lclj_Wh_Bdmb_HlModel">
   <!--  select * from LCLJ_WH_BDMB_HL
  <where>
 	(yljgbm = #{yljgbm,jdbcType=VARCHAR})
    <if test="ryljbm != null and ryljbm != '' ">
    	and (RYLJBM = #{ryljbm,jdbcType=VARCHAR})
    </if>
    <if test="rydjbm != null and rydjbm != '' ">
    	and (RYJDBM = #{rydjbm,jdbcType=VARCHAR})
    </if>
    <if test="ryejjdbm != null and ryejjdbm != '' ">
    	and (RYEJJDBM = #{ryejjdbm,jdbcType=VARCHAR})
    </if>
    <if test="ryljxmbm != null and ryljxmbm != '' ">
    	and (RYLJXMBM = #{ryljxmbm,jdbcType=VARCHAR})
    </if>
    <if test="xmbm != null and xmbm != '' ">
    	and (XMBM = #{xmbm,jdbcType=VARCHAR})
    </if>
    <if test="shbz != null and shbz != '' ">
    	and (SHBZ = #{shbz,jdbcType=VARCHAR})
    </if>
   </where>
   ORDER BY  yzbm  -->

   select
		    hl.XMBM, hl.YLJGBM, hl.XMMC, hl.RYLJBM, hl.RYJDBM, hl.RYEJJDBM, hl.XMDL, hl.XMLX, hl.XSXH,
		    hl.XMZXDL, hl.XMZXXL, hl.XMZXFS, hl.XMXZFS, hl.XMPG, hl.KXX, hl.CJRY, hl.CJRQ, hl.SHBZ,
		    hl.SHRY, hl.SHRQ,
		    bz.ljmc RYLJMC,jd.jdmc RYJDMC,ejjd.ejjdmc RYEJJDMC,zxdl.mc XMZXDLMC,zxxl.mc XMZXXLMC,
		    ry.ryxm CJRYXM,sh.ryxm SHRYXM
		from lclj_wh_bdmb_hl hl
		  left join lclj_wh_bz bz on (hl.yljgbm = bz.yljgbm and hl.ryjdbm = bz.ljbm)
		  left join lclj_wh_jd jd on (jd.yljgbm =hl.yljgbm and jd.jdbm =hl.ryejjdbm)
		  left join lclj_wh_ejjd ejjd on (hl.yljgbm = ejjd.yljgbm and hl.ryejjdbm = ejjd.ejjdbm)
		  left join lclj_wh_bdmb_zxdl zxdl on (hl.yljgbm = zxdl.yljgbm and hl.xmzxdl = zxdl.bm)
		  left join lclj_wh_bdmb_zxxl zxxl on (hl.yljgbm = zxxl.yljgbm and zxxl.bm = hl.xmzxxl)
		  left join gyb_rybm ry on (ry.yljgbm = hl.yljgbm and ry.rybm = hl.cjry)
		  left join gyb_rybm sh on (sh.yljgbm = hl.yljgbm and sh.rybm = hl.shry)

	<where>
			hl.yljgbm = #{yljgbm,jdbcType=VARCHAR}
		and	hl.ryljbm = #{ryljbm,jdbcType=VARCHAR}
		and hl.ryjdbm = #{ryjdbm,jdbcType=VARCHAR}
		and hl.ryejjdbm = #{ryejjdbm,jdbcType=VARCHAR}
	</where>
	order by hl.xsxh ${order}
 </select>

  <delete id="delete"  parameterType="java.util.List" >
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    delete from LCLJ_WH_BDMB_HL
    where XMBM = #{item.xmbm,jdbcType=VARCHAR}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
    </foreach>
  </delete>

  <insert id="save" parameterType="com.supx.csp.api.lclj.xtwh.pojo.Lclj_Wh_Bdmb_HlModel" >
    insert into LCLJ_WH_BDMB_HL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="xmbm != null" >
        XMBM,
      </if>
      <if test="yljgbm != null" >
        YLJGBM,
      </if>
      <if test="xmmc != null" >
        XMMC,
      </if>
      <if test="ryljbm != null" >
        RYLJBM,
      </if>
      <if test="ryjdbm != null" >
        RYJDBM,
      </if>
      <if test="ryejjdbm != null" >
        RYEJJDBM,
      </if>
      <if test="xmdl != null" >
        XMDL,
      </if>
      <if test="xmlx != null" >
        XMLX,
      </if>
      <if test="xsxh != null" >
        XSXH,
      </if>
      <if test="xmzxdl != null" >
        XMZXDL,
      </if>
      <if test="xmzxxl != null" >
        XMZXXL,
      </if>
      <if test="xmzxfs != null" >
        XMZXFS,
      </if>
      <if test="xmxzfs != null" >
        XMXZFS,
      </if>
      <if test="xmpg != null" >
        XMPG,
      </if>
      <if test="kxx != null" >
        KXX,
      </if>
      <if test="cjry != null" >
        CJRY,
      </if>
      <if test="cjrq != null" >
        CJRQ,
      </if>
      <if test="shbz != null" >
        SHBZ,
      </if>
      <if test="shry != null" >
        SHRY,
      </if>
      <if test="shrq != null" >
        SHRQ,
      </if>
      <if test="ryjlmc != null" >
        RYJLMC,
      </if>
      <if test="ryjdmc != null" >
        RYJDMC,
      </if>
      <if test="ryejjdmc != null" >
        RYEJJDMC,
      </if>
      <if test="xmzxdlmc != null" >
        XMZXDLMC,
      </if>
      <if test="cjryxm != null" >
        CJRYXM,
      </if>
      <if test="shryxm != null" >
        SHRYXM,
      </if>
      <if test="xmzxxlmc != null" >
        XMZXXLMC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="xmbm != null" >
        #{xmbm,jdbcType=VARCHAR},
      </if>
      <if test="yljgbm != null" >
        #{yljgbm,jdbcType=VARCHAR},
      </if>
      <if test="xmmc != null" >
        #{xmmc,jdbcType=VARCHAR},
      </if>
      <if test="ryljbm != null" >
        #{ryljbm,jdbcType=VARCHAR},
      </if>
      <if test="ryjdbm != null" >
        #{ryjdbm,jdbcType=VARCHAR},
      </if>
      <if test="ryejjdbm != null" >
        #{ryejjdbm,jdbcType=VARCHAR},
      </if>
      <if test="xmdl != null" >
        #{xmdl,jdbcType=VARCHAR},
      </if>
      <if test="xmlx != null" >
        #{xmlx,jdbcType=VARCHAR},
      </if>
      <if test="xsxh != null" >
        #{xsxh,jdbcType=DECIMAL},
      </if>
      <if test="xmzxdl != null" >
        #{xmzxdl,jdbcType=VARCHAR},
      </if>
      <if test="xmzxxl != null" >
        #{xmzxxl,jdbcType=VARCHAR},
      </if>
      <if test="xmzxfs != null" >
        #{xmzxfs,jdbcType=VARCHAR},
      </if>
      <if test="xmxzfs != null" >
        #{xmxzfs,jdbcType=VARCHAR},
      </if>
      <if test="xmpg != null" >
        #{xmpg,jdbcType=VARCHAR},
      </if>
      <if test="kxx != null" >
        #{kxx,jdbcType=VARCHAR},
      </if>
      <if test="cjry != null" >
        #{cjry,jdbcType=VARCHAR},
      </if>
      <if test="cjrq != null" >
        #{cjrq,jdbcType=TIMESTAMP},
      </if>
      <if test="shbz != null" >
        #{shbz,jdbcType=VARCHAR},
      </if>
      <if test="shry != null" >
        #{shry,jdbcType=VARCHAR},
      </if>
      <if test="shrq != null" >
        #{shrq,jdbcType=TIMESTAMP},
      </if>
      <if test="ryjlmc != null" >
        #{ryjlmc,jdbcType=VARCHAR},
      </if>
      <if test="ryjdmc != null" >
        #{ryjdmc,jdbcType=VARCHAR},
      </if>
      <if test="ryejjdmc != null" >
        #{ryejjdmc,jdbcType=VARCHAR},
      </if>
      <if test="xmzxdlmc != null" >
        #{xmzxdlmc,jdbcType=VARCHAR},
      </if>
      <if test="cjryxm != null" >
        #{cjryxm,jdbcType=VARCHAR},
      </if>
      <if test="shryxm != null" >
        #{shryxm,jdbcType=VARCHAR},
      </if>
      <if test="xmzxxlmc != null" >
        #{xmzxxlmc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="com.supx.csp.api.lclj.xtwh.pojo.Lclj_Wh_Bdmb_HlModel" >
    update LCLJ_WH_BDMB_HL
    <set >
      <if test="xmmc != null" >
        XMMC = #{xmmc,jdbcType=VARCHAR},
      </if>
      <if test="ryljbm != null" >
        RYLJBM = #{ryljbm,jdbcType=VARCHAR},
      </if>
      <if test="ryjdbm != null" >
        RYJDBM = #{ryjdbm,jdbcType=VARCHAR},
      </if>
      <if test="ryejjdbm != null" >
        RYEJJDBM = #{ryejjdbm,jdbcType=VARCHAR},
      </if>
      <if test="xmdl != null" >
        XMDL = #{xmdl,jdbcType=VARCHAR},
      </if>
      <if test="xmlx != null" >
        XMLX = #{xmlx,jdbcType=VARCHAR},
      </if>
      <if test="xsxh != null" >
        XSXH = #{xsxh,jdbcType=DECIMAL},
      </if>
      <if test="xmzxdl != null" >
        XMZXDL = #{xmzxdl,jdbcType=VARCHAR},
      </if>
      <if test="xmzxxl != null" >
        XMZXXL = #{xmzxxl,jdbcType=VARCHAR},
      </if>
      <if test="xmzxfs != null" >
        XMZXFS = #{xmzxfs,jdbcType=VARCHAR},
      </if>
      <if test="xmxzfs != null" >
        XMXZFS = #{xmxzfs,jdbcType=VARCHAR},
      </if>
      <if test="xmpg != null" >
        XMPG = #{xmpg,jdbcType=VARCHAR},
      </if>
      <if test="kxx != null" >
        KXX = #{kxx,jdbcType=VARCHAR},
      </if>
      <if test="cjry != null" >
        CJRY = #{cjry,jdbcType=VARCHAR},
      </if>
      <if test="cjrq != null" >
        CJRQ = #{cjrq,jdbcType=TIMESTAMP},
      </if>
      <if test="shbz != null" >
        SHBZ = #{shbz,jdbcType=VARCHAR},
      </if>
      <if test="shry != null" >
        SHRY = #{shry,jdbcType=VARCHAR},
      </if>
      <if test="shrq != null" >
        SHRQ = #{shrq,jdbcType=TIMESTAMP},
      </if>
      <if test="ryjlmc != null" >
        RYJLMC = #{ryjlmc,jdbcType=VARCHAR},
      </if>
      <if test="ryjdmc != null" >
        RYJDMC = #{ryjdmc,jdbcType=VARCHAR},
      </if>
      <if test="ryejjdmc != null" >
        RYEJJDMC = #{ryejjdmc,jdbcType=VARCHAR},
      </if>
      <if test="xmzxdlmc != null" >
        XMZXDLMC = #{xmzxdlmc,jdbcType=VARCHAR},
      </if>
      <if test="cjryxm != null" >
        CJRYXM = #{cjryxm,jdbcType=VARCHAR},
      </if>
      <if test="shryxm != null" >
        SHRYXM = #{shryxm,jdbcType=VARCHAR},
      </if>
      <if test="xmzxxlmc != null" >
        XMZXXLMC = #{xmzxxlmc,jdbcType=VARCHAR},
      </if>
    </set>
    where XMBM = #{xmbm,jdbcType=VARCHAR}
      and YLJGBM = #{yljgbm,jdbcType=VARCHAR}
  </update>
</mapper>
