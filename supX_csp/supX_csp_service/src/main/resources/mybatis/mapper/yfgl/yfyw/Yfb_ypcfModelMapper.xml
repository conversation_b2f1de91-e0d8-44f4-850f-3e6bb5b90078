<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.yfgl.yfyw.dao.New1Yfb_ypcfModelMapper">
    <sql id="CfBase_Column_List" >
    CFH, CFRQ, YFBM, CFLX, BRLX, BAH, BRXM, LCZD, CFYS, BRKS, HJRY, ZYFS, CFJE, ZFBZ,
    ZFRY, ZFRQ, KFBZ, KFRY, KFRQ, FYBZ, FYRY, FYRQ, ISPRINT, BZSM, UPTIMESTAMP, SJJE,
    DZF, CFYSXM, BRKSMC, HJRYXM, ZFRYXM, KFRYXM, FYRYXM, PYKSMC, PYBZ, PYRYXM, DZS, CFLXMC,
    PYKS, PYRY, PYSJ, ZFSHBZ, ZFSHRY, ZFSHRYXM, ZFSHRQ, SFDZCF,TYCFH,YYSMCF,YWCKBH,ZYTT,LXRXM,LXRDH,LXRSFZH
  </sql>

    <!-- 分组查询药房业务窗口的排号数  -->
    <select id="queryCkCfnum" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
   		select ywckbh,count(1) cfnum from yfb_ypcf where zfbz='0' and kfbz='1' and fybz='0' and cfrq>=(sysdate-1)
   		where yljgbm = #{yljgbm,jdbcType=VARCHAR}
   		group by ywckbh
   </select>

    <!-- 查询处方是否已退费 -->
    <select id="queryYtfcf" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
   select a.* from (
   	SELECT distinct cf.cfh,cf.bah,fy.sftf,fy.yfbm FROM yfb_ypcf cf
	inner join mzb_brfy fy on cf.bah = fy.ryghxh and cf.cfh = fy.yzhm and cf.yljgbm = fy.yljgbm
	where cf.yljgbm = #{yljgbm,jdbcType=VARCHAR}
	and cf.bah = #{bah,jdbcType=VARCHAR}
	and cf.cfh = #{cfh,jdbcType=VARCHAR}
	  union all
   	SELECT distinct cf.cfh,cf.bah,fy.sftf,fy.yfbm FROM yfb_ypcf cf
	inner join zyb_brfy fy on cf.bah = fy.zyh and cf.cfh = fy.yzhm and cf.yljgbm = fy.yljgbm
	where cf.yljgbm = #{yljgbm,jdbcType=VARCHAR}
	and cf.bah = #{bah,jdbcType=VARCHAR}
	and cf.cfh = #{cfh,jdbcType=VARCHAR}
	)a where a.yfbm is not null order by sftf  asc
   </select>

    <!-- 查询住院病人未扣费处方 -->
    <select id="queryBrcf" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
        select *
        from yfb_ypcf
        where brxm = #{brxm,jdbcType=VARCHAR}
        <if test="parm != null and parm != ''">
            and (
            instr(cfh,#{parm,jdbcType=VARCHAR}) &gt; 0
            or instr(bah,#{parm,jdbcType=VARCHAR}) &gt; 0
            )
        </if>
        and yljgbm = #{yljgbm,jdbcType=VARCHAR}
        and kfbz = '0'
        and zfbz = '0'
        and brlx = '1'
    </select>

    <!-- 查询已退费处方 -->
    <select id="queryYtfcfList" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
        SELECT cf.cfh,jbxx.brxm,cf.fybz,tf.sftf,cf.cfrq FROM yfb_ypcf cf
        inner join yfb_yppf pf on pf.cfh = cf.cfh and pf.yljgbm = cf.yljgbm
        <!-- inner join yfb_ypkc kc on kc.ypbm = pf.ypbm and kc.yljgbm = pf.yljgbm and kc.pcty = '0'
        inner join ykb_ypzd zd on zd.ypbm = kc.ypbm and zd.yljgbm = kc.yljgbm and zd.zlbm = '01' -->
        inner join (select yzhm,sftf,rybrid,yljgbm from mzb_brfy
        where yljgbm = #{yljgbm,jdbcType=VARCHAR}
        group by yzhm,sftf,rybrid,yljgbm ) tf on tf.yzhm = cf.cfh and tf.yljgbm = cf.yljgbm
        inner join gyb_brjbxx jbxx on jbxx.brid = tf.rybrid and jbxx.yljgbm = tf.yljgbm
        where cf.yljgbm = #{yljgbm,jdbcType=VARCHAR}  and cf.kfbz = '1' and cf.zfbz = '0'
        and tf.sftf = '1'
        <if test="endrq != null ">
            and (trunc(cf.cfrq) &lt;= #{endrq,jdbcType=TIMESTAMP})
        </if>
        <if test="beginrq != null ">
            and (trunc(cf.cfrq) &gt;= #{beginrq,jdbcType=TIMESTAMP})
        </if>
        group by cf.cfh,jbxx.brxm,cf.fybz,tf.sftf,cf.cfrq
        <if test="sort != 'zdy' ">
            ORDER BY ${sort} ${order}
        </if>
        <if test="sort == 'zdy' ">
            ORDER BY cf.fybz asc , cf.kfbz desc ,cf.cfh desc
        </if>
    </select>


    <!-- 查询处方 用于处方展示含患者信息化 -->
    <select id="queryYpcf" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
        select distinct  a.*,nvl(sfty.tyzt,0) as tyzt from (
        select ry.ryxm fyryxm,cf.CFH,cf.CFRQ,cf.YFBM,cf.CFLX,cf.CFLXMC,cf.BRLX,cf.BAH,cf.BRXM,cf.LCZD,cf.CFYS,cf.BRKS,cf.HJRY,cf.ZYFS,cf.CFJE,cf.ZFBZ,cf.ZFSHBZ,
        cf.ZFRY,cf.ZFRQ,cf.KFBZ,cf.KFRY,cf.KFRQ,cf.FYBZ,cf.FYRY,cf.FYRQ,cf.ISPRINT,decode(zf.zflxmc,null,'现金',zf.zflxmc) BZSM,cf.UPTIMESTAMP,cf.SJJE,
        cf.DZF,cf.DZS,cf.CFYSXM,cf.BRKSMC,cf.HJRYXM,cf.ZFRYXM,cf.KFRYXM,cf.PYKSMC,cf.PYBZ,cf.yysmcf,cf.PYRYXM,zc.brxb,gh.fbbm,gh.brnl,gh.nldw,fb.fbmc,
        to_char(gh.BRNL)|| case when gh.NLDW = '1' then '岁' when gh.NLDW = '2' then '月'
        when gh.NLDW = '3' then '周' when gh.NLDW = '4' then '天' else '' end NL,cf.PYKS,cf.TYCFH,cflx.cflb,
        bx.bxlbmc,gh.gms,cf.zyzf,cf.zyzh,cf.zyjs,bx.bxjk,gh.lstdbz,gh.lstdxh,cf.tpczy,cf.tpczyxm,cf.tprq,cf.cfshbz,cf.ystysqbz,cf.sqrq,zc.lxrdh
        From YFB_YPCF cf left join ghb_brgh gh on cf.bah = gh.ghxh and cf.yljgbm = gh.yljgbm and gh.thbz = '0'
        left join yfb_cflx cflx on cflx.cflxbm=cf.cflx and cflx.yljgbm=cf.yljgbm
        left join gyb_brjbxx zc on gh.brid = zc.brid and gh.yljgbm = zc.yljgbm
        left join gyb_brfb fb on gh.fbbm = fb.fbbm and gh.yljgbm = fb.yljgbm
        left join gyb_rybm ry on ry.rybm = cf.fyry and ry.yljgbm = cf.yljgbm
        left join gyb_bxlb bx on gh.bxlbbm = bx.bxlbbm and gh.yljgbm = bx.yljgbm
        left join mzb_brfy fy on cf.cfh = fy.yzhm and cf.yljgbm = fy.yljgbm  and fy.sftf = '0'  and fy.yzlx = '2'
        left join mzb_jsjl js on fy.yljgbm = js.yljgbm and fy.ryjsjlid = js.jsjlid
        left join gyb_zflx zf on js.yljgbm = zf.yljgbm and js.zflxbm = zf.zflxbm

        <where>
            (cf.brlx = '0') and ( cf.yljgbm = #{yljgbm,jdbcType=VARCHAR} )
            and cf.sflzcf='0'
            <if test="parm != null and parm != '' ">
                and (cf.cfh like '%'||#{parm,jdbcType=VARCHAR}
                or instr(cf.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
                )
            </if>
            <if test="ylkh !=null and ylkh!=''">
                and gh.brid in (select brid from gyb_brylkxx where ylkh=#{ylkh,jdbcType=VARCHAR} and  yljgbm =#{yljgbm,jdbcType=VARCHAR} )
            </if>
            <if test="brxm != null">
                and cf.brxm = #{brxm,jdbcType=VARCHAR}
            </if>
            <if test="bxjk != null">
                and bx.bxjk is null
            </if>
            <if test="tycx != null">
                and (cf.cfje &gt; 0)
            </if>
            <if test="brlx != null ">
                and (cf.brlx = #{brlx,jdbcType=VARCHAR})
            </if>
            <if test="yfbm != null ">
                and (cf.yfbm = #{yfbm,jdbcType=VARCHAR})
            </if>
            <if test="fybz != null ">
                and (cf.fybz = #{fybz,jdbcType=VARCHAR})
            </if>
            <if test="kfbz != null ">
                and (cf.kfbz = #{kfbz,jdbcType=VARCHAR})
            </if>
            <if test="cfshbz != null ">
                and (cf.cfshbz = #{cfshbz,jdbcType=VARCHAR})
            </if>
            <if test="zfbz != null ">
                and (cf.zfbz = #{zfbz,jdbcType=VARCHAR})
            </if>
            <if test="zfshbz != null ">
                and (cf.zfshbz = #{zfshbz,jdbcType=VARCHAR})
            </if>
            <if test="sfdzcf != null ">
                and (cf.sfdzcf = #{sfdzcf,jdbcType=VARCHAR})
            </if>
            <if test="endrq != null ">
                and (cf.${cxrq} &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (cf.${cxrq} &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="ystysqbz != null ">
                and (cf.YSTYSQBZ =#{ystysqbz})
            </if>
        </where>
        union all
        select ry.ryxm fyryxm,cf.CFH,cf.CFRQ,cf.YFBM,cf.CFLX,cf.CFLXMC,cf.BRLX,cf.BAH,cf.BRXM,cf.LCZD,cf.CFYS,cf.BRKS,cf.HJRY,cf.ZYFS,cf.CFJE,cf.ZFBZ,cf.ZFSHBZ,
        cf.ZFRY,cf.ZFRQ,cf.KFBZ,cf.KFRY,cf.KFRQ,cf.FYBZ,cf.FYRY,cf.FYRQ,cf.ISPRINT,cf.BZSM,cf.UPTIMESTAMP,cf.SJJE,
        cf.DZF,cf.DZS,cf.CFYSXM,cf.BRKSMC,cf.HJRYXM,cf.ZFRYXM,cf.KFRYXM,cf.PYKSMC,cf.PYBZ,cf.yysmcf,cf.PYRYXM,zc.brxb,dj.brfb fbbm,dj.nl brnl,dj.nldw,fb.fbmc,
        to_char(dj.NL)|| case when dj.NLDW = '1' then '岁' when dj.NLDW = '2' then '月'
        when dj.NLDW = '3' then '周' when dj.NLDW = '4' then '天' else '' end NL ,cf.PYKS,cf.TYCFH,cflx.cflb,
        bx.bxlbmc,null gms,cf.zyzf,cf.zyzh,cf.zyjs,bx.bxjk,'0' lstdbz,'0' lstdxh,cf.tpczy,cf.tpczyxm,cf.tprq,cf.cfshbz,cf.ystysqbz,cf.sqrq,zc.lxrdh
        From YFB_YPCF cf inner join zyb_rydj dj on cf.bah = dj.zyh and cf.yljgbm = dj.yljgbm and dj.ifzf = '0'
        left join yfb_cflx cflx on cflx.cflxbm=cf.cflx and cflx.yljgbm=cf.yljgbm
        left join gyb_brjbxx zc on dj.brid = zc.brid and dj.yljgbm = zc.yljgbm
        left join gyb_brylkxx ylk on dj.brid=ylk.brid and dj.yljgbm=ylk.yljgbm
        left join gyb_brfb fb on dj.brfb = fb.fbbm and dj.yljgbm = fb.yljgbm
        left join gyb_rybm ry on ry.rybm = cf.fyry and ry.yljgbm = cf.yljgbm
        left join gyb_bxlb bx on dj.bxlbbm = bx.bxlbbm and dj.yljgbm = bx.yljgbm
        left join zyb_brfy fy on cf.bah = fy.zyh and cf.yljgbm = fy.yljgbm and fy.sftf = '0'
        <where>
            (cf.brlx = '1')  and ( cf.yljgbm = #{yljgbm,jdbcType=VARCHAR} )
            and cf.sflzcf='0'
            <if test="parm != null and parm != '' ">
                and (cf.cfh like '%'||#{parm,jdbcType=VARCHAR}
                or instr(cf.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
                )
            </if>
            <if test="bxjk != null">
                and bx.bxjk is null
            </if>
            <if test="tycx != null">
                and (cf.cfje &gt; 0)
            </if>
            <if test="brlx != null ">
                and (cf.brlx = #{brlx,jdbcType=VARCHAR})
            </if>
            <if test="yfbm != null ">
                and (cf.yfbm = #{yfbm,jdbcType=VARCHAR})
            </if>
            <if test="fybz != null ">
                and (cf.fybz = #{fybz,jdbcType=VARCHAR})
            </if>
            <if test="kfbz != null ">
                and (cf.kfbz = #{kfbz,jdbcType=VARCHAR})
            </if>
            <if test="cfshbz != null ">
                and (cf.cfshbz = #{cfshbz,jdbcType=VARCHAR})
            </if>
            <if test="zfbz != null ">
                and (cf.zfbz = #{zfbz,jdbcType=VARCHAR})
            </if>
            <if test="zfshbz != null ">
                and (cf.zfshbz = #{zfshbz,jdbcType=VARCHAR})
            </if>
            <if test="sfdzcf != null ">
                and (cf.sfdzcf = #{sfdzcf,jdbcType=VARCHAR})
            </if>
            <if test="endrq != null ">
                and (cf.${cxrq} &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (cf.${cxrq} &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="ystysqbz != null ">
                and (cf.YSTYSQBZ =#{ystysqbz})
            </if>
        </where>
        ) a   left join (select (case when sum(cfyl)-sum(ytsl)  > 0  and sum(ytsl) > 0
        then '1' when sum(cfyl)-sum(ytsl) = 0 then '2' else '0' end) as tyzt,
        cfh from yfb_yppf  group by cfh)  sfty on a.cfh = sfty.cfh
        <if test="sort != 'zdy' ">
            ORDER BY ${sort} ${order}
        </if>
        <if test="sort == 'zdy' ">
            ORDER BY a.lstdbz desc,a.fybz asc ,a.kfbz desc ,a.cfh desc
        </if>
    </select>

    <update id="callUspYfbYpcfGxfy" statementType="CALLABLE">
        {call usp_yfb_ypcf_gxfy(#{p_cfh,mode=IN,jdbcType=VARCHAR},#{p_rtn,mode=OUT,jdbcType=NUMERIC},#{p_msg,mode=OUT,jdbcType=VARCHAR})}
    </update>

    <!-- 查询处方 用于处方展示含患者信息化 -->
    <select id="queryYpcfJe" resultType="double"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
        select nvl(SUM(CFJE),0) fy from (
        select distinct  a.* from (
        select sum(cfje) cfje from (select sum(round(pf.fysl*pf.yplj,2)) cfje
        From YFB_YPCF cf left join ghb_brgh gh on cf.bah = gh.ghxh and cf.yljgbm = gh.yljgbm and gh.thbz = '0'
        inner join yfb_yppf pf on cf.cfh = pf.cfh
        left join yfb_cflx cflx on cflx.cflxbm=cf.cflx and cflx.yljgbm=cf.yljgbm
        left join gyb_brjbxx zc on gh.brid = zc.brid and gh.yljgbm = zc.yljgbm
        left join gyb_brfb fb on gh.fbbm = fb.fbbm and gh.yljgbm = fb.yljgbm
        left join gyb_rybm ry on ry.rybm = cf.fyry and ry.yljgbm = cf.yljgbm
        left join gyb_bxlb bx on gh.bxlbbm = bx.bxlbbm and gh.yljgbm = bx.yljgbm

        <where>
            (cf.brlx = '0') and ( cf.yljgbm = #{yljgbm,jdbcType=VARCHAR} )
            <if test="parm != null and parm != '' ">
                and (cf.cfh like '%'||#{parm,jdbcType=VARCHAR}
                or instr(cf.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
                )
            </if>
            <if test="ylkh !=null and ylkh!=''">
                and gh.brid in (select brid from gyb_brylkxx where ylkh=#{ylkh,jdbcType=VARCHAR} and yljgbm
                =#{yljgbm,jdbcType=VARCHAR} )
            </if>
            <if test="brxm != null">
                and cf.brxm = #{brxm,jdbcType=VARCHAR}
            </if>
            <if test="bxjk != null">
                and bx.bxjk is null
            </if>
            <if test="tycx != null">
                and (cf.cfje &gt; 0)
            </if>
            <if test="brlx != null ">
                and (cf.brlx = #{brlx,jdbcType=VARCHAR})
            </if>
            <if test="yfbm != null ">
                and (cf.yfbm = #{yfbm,jdbcType=VARCHAR})
            </if>
            <if test="fybz != null ">
                and (cf.fybz = #{fybz,jdbcType=VARCHAR})
            </if>
            <if test="kfbz != null ">
                and (cf.kfbz = #{kfbz,jdbcType=VARCHAR})
            </if>
            <if test="cfshbz != null ">
                and (cf.cfshbz = #{cfshbz,jdbcType=VARCHAR})
            </if>
            <if test="zfbz != null ">
                and (cf.zfbz = #{zfbz,jdbcType=VARCHAR})
            </if>
            <if test="zfshbz != null ">
                and (cf.zfshbz = #{zfshbz,jdbcType=VARCHAR})
            </if>
            <if test="sfdzcf != null ">
                and (cf.sfdzcf = #{sfdzcf,jdbcType=VARCHAR})
            </if>
            <if test="endrq != null ">
                and (cf.${cxrq} &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (cf.${cxrq} &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="ystysqbz != null ">
                and (cf.YSTYSQBZ =#{ystysqbz})
            </if>
        </where>
        union all
        select cf.CFJE
        From YFB_YPCF cf inner join zyb_rydj dj on cf.bah = dj.zyh and cf.yljgbm = dj.yljgbm and dj.ifzf = '0'
        left join yfb_cflx cflx on cflx.cflxbm=cf.cflx and cflx.yljgbm=cf.yljgbm
        left join gyb_brjbxx zc on dj.brid = zc.brid and dj.yljgbm = zc.yljgbm
        left join gyb_brylkxx ylk on dj.brid=ylk.brid and dj.yljgbm=ylk.yljgbm
        left join gyb_brfb fb on dj.brfb = fb.fbbm and dj.yljgbm = fb.yljgbm
        left join gyb_rybm ry on ry.rybm = cf.fyry and ry.yljgbm = cf.yljgbm
        left join gyb_bxlb bx on dj.bxlbbm = bx.bxlbbm and dj.yljgbm = bx.yljgbm
        <where>
            (cf.brlx = '1') and ( cf.yljgbm = #{yljgbm,jdbcType=VARCHAR} )
            <if test="parm != null and parm != '' ">
                and (cf.cfh like '%'||#{parm,jdbcType=VARCHAR}
                or instr(cf.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
                )
            </if>
            <if test="bxjk != null">
                and bx.bxjk is null
            </if>
            <if test="tycx != null">
                and (cf.cfje &gt; 0)
            </if>
            <if test="brlx != null ">
                and (cf.brlx = #{brlx,jdbcType=VARCHAR})
            </if>
            <if test="yfbm != null ">
                and (cf.yfbm = #{yfbm,jdbcType=VARCHAR})
            </if>
            <if test="fybz != null ">
                and (cf.fybz = #{fybz,jdbcType=VARCHAR})
            </if>
            <if test="kfbz != null ">
                and (cf.kfbz = #{kfbz,jdbcType=VARCHAR})
            </if>
            <if test="cfshbz != null ">
                and (cf.cfshbz = #{cfshbz,jdbcType=VARCHAR})
            </if>
            <if test="zfbz != null ">
                and (cf.zfbz = #{zfbz,jdbcType=VARCHAR})
            </if>
            <if test="zfshbz != null ">
                and (cf.zfshbz = #{zfshbz,jdbcType=VARCHAR})
            </if>
            <if test="sfdzcf != null ">
                and (cf.sfdzcf = #{sfdzcf,jdbcType=VARCHAR})
            </if>
            <if test="endrq != null ">
                and (cf.${cxrq} &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (cf.${cxrq} &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="ystysqbz != null ">
                and (cf.YSTYSQBZ =#{ystysqbz})
            </if>
        </where>
        )
        ) a
        )
    </select>

    <!-- 查询在途处方 -->
    <select id="queryZtcf" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
        select * from yfb_ypcf  where kfbz = '0' and fybz = '0' and zfbz = '0'
        and cfje &gt; 0 and cfrq &lt; sysdate and yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="parm != null and parm != '' ">
            and (cfh like '%'||#{parm,jdbcType=VARCHAR}||'%' or brxm like '%'||#{parm,jdbcType=VARCHAR}||'%'
            or cfh in (select cfh from yfb_yppf where ypbm in
            (select ypbm from ykb_ypzd where ypmc like '%'||#{parm,jdbcType=VARCHAR}||'%' or
            upper(pydm) like upper('%'||#{parm,jdbcType=VARCHAR}||'%')))
            )
        </if>
        order by cfrq desc
    </select>

    <!-- 查询在途处方费用 -->
    <select id="queryDzffy" resultType="com.supx.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
        select * from  mzb_brfy  where  yljgbm=#{yljgbm,jdbcType=VARCHAR} and
        sfjs = '0' and yzlx = '2'
        <if test="cxlist != null">
            and  yzhm in
            <foreach item="item" index="index" collection="cxlist" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <!-- 处方查询 -->
    <select id="querycf" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel">

        <!-- SELECT CFH, CFRQ, YFBM, CFLX, BRLX, BAH, BRXM, LCZD, CFYS, BRKS, HJRY, ZYFS, CFJE, ZFBZ,
         ZFRY, ZFRQ, KFBZ, KFRY, KFRQ, FYBZ, FYRY, FYRQ, ISPRINT, BZSM, UPTIMESTAMP, SJJE,
         DZF, CFYSXM, BRKSMC, HJRYXM, ZFRYXM, KFRYXM, FYRYXM, PYKSMC, PYBZ, PYRYXM, DZS, CFLXMC,
         PYKS, PYRY, PYSJ FROM YFB_YPCF WHERE CFH = #{cfh,jdbcType=VARCHAR} and YLJGBM = #{yljgbm,jdbcType=VARCHAR}  -->
        <!-- 2017-12-17 YK MODIFIED -->
        select a.* from (
        SELECT CF.CFH ,CF.CFRQ ,CF.YFBM ,CF.CFLX ,CF.BRLX ,CF.BAH ,JBXX.BRXM,CF.LCZD ,CF.CFYS ,CF.BRKS ,
        CF.HJRY ,CF.ZYFS ,CF.CFJE ,CF.ZFBZ ,CF.ZFRY ,CF.ZFRQ ,CF.KFBZ ,CF.KFRY ,CF.KFRQ ,CF.FYBZ ,CF.FYRY ,CF.FYRQ ,CF.ISPRINT ,
        CF.BZSM ,CF.UPTIMESTAMP ,CF.SJJE ,CF.DZF ,CF.CFYSXM ,CF.BRKSMC ,CF.HJRYXM ,CF.ZFRYXM ,CF.KFRYXM ,CF.FYRYXM ,CF.PYKSMC ,
        CF.PYBZ ,CF.PYRYXM ,CF.DZS ,CF.CFLXMC ,CF.PYKS ,CF.PYRY ,CF.PYSJ,CF.YYSMCF,gh.lstdbz,gh.lstdxh FROM YFB_YPCF CF
        LEFT JOIN GHB_BRGH GH ON GH.GHXH = CF.BAH AND GH.YLJGBM = CF.YLJGBM
        LEFT JOIN GYB_BRJBXX JBXX ON JBXX.BRID = GH.BRID AND JBXX.YLJGBM = GH.YLJGBM
        WHERE CF.CFH = #{cfh,jdbcType=VARCHAR} and CF.YLJGBM = #{yljgbm,jdbcType=VARCHAR} and cf.brlx='0'
        union all
        SELECT CF.CFH ,CF.CFRQ ,CF.YFBM ,CF.CFLX ,CF.BRLX ,CF.BAH ,JBXX.BRXM,CF.LCZD ,CF.CFYS ,CF.BRKS ,
        CF.HJRY ,CF.ZYFS ,CF.CFJE ,CF.ZFBZ ,CF.ZFRY ,CF.ZFRQ ,CF.KFBZ ,CF.KFRY ,CF.KFRQ ,CF.FYBZ ,CF.FYRY ,CF.FYRQ ,CF.ISPRINT ,
        CF.BZSM ,CF.UPTIMESTAMP ,CF.SJJE ,CF.DZF ,CF.CFYSXM ,CF.BRKSMC ,CF.HJRYXM ,CF.ZFRYXM ,CF.KFRYXM ,CF.FYRYXM ,CF.PYKSMC ,
        CF.PYBZ ,CF.PYRYXM ,CF.DZS ,CF.CFLXMC ,CF.PYKS ,CF.PYRY ,CF.PYSJ,CF.YYSMCF,'0' lstdbz,'0' FROM YFB_YPCF CF
        INNER JOIN zyb_rydj GH ON GH.zyh = CF.BAH AND GH.YLJGBM = CF.YLJGBM
        INNER JOIN GYB_BRJBXX JBXX ON JBXX.BRID = GH.BRID AND JBXX.YLJGBM = GH.YLJGBM
        WHERE CF.CFH = #{cfh,jdbcType=VARCHAR} and CF.YLJGBM = #{yljgbm,jdbcType=VARCHAR} and cf.brlx='1'
        )a
    </select>
    <update id="updateZtcf" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            update yfb_ypcf
            <set>
                <if test="item.zfrq != null">
                    ZFRQ = #{item.zfrq,jdbcType=DATE},
                </if>
                <if test="item.zfry != null">
                    ZFRY = #{item.zfry,jdbcType=VARCHAR},
                </if>
                <if test="item.zfryxm != null">
                    ZFRYXM = #{item.zfryxm,jdbcType=VARCHAR},
                </if>
                <if test="item.zfbz != null">
                    ZFBZ = #{item.zfbz,jdbcType=VARCHAR},
                </if>
                <if test="item.bzsm != null">
                    BZSM = #{item.bzsm,jdbcType=VARCHAR},
                </if>
            </set>
            where YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
            <if test="item.cfh != null and item.cfh != '' ">
                and cfh = #{item.cfh,jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>
    <!-- 新增处方保存 -->
    <insert id="insertcf" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel">
        insert into YFB_YPCF
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="cfh != null" >
                CFH,
            </if>
            <if test="cfrq != null" >
                CFRQ,
            </if>
            <if test="yfbm != null" >
                YFBM,
            </if>
            <if test="cflx != null" >
                CFLX,
            </if>
            <if test="brlx != null" >
                BRLX,
            </if>
            <if test="bah != null" >
                BAH,
            </if>
            <if test="brxm != null" >
                BRXM,
            </if>
            <if test="lczd != null" >
                LCZD,
            </if>
            <if test="cfys != null" >
                CFYS,
            </if>
            <if test="brks != null" >
                BRKS,
            </if>
            <if test="hjry != null" >
                HJRY,
            </if>
            <if test="zyfs != null" >
                ZYFS,
            </if>
            <if test="cfje != null" >
                CFJE,
            </if>
            <if test="zfbz != null" >
                ZFBZ,
            </if>
            <if test="zfry != null" >
                ZFRY,
            </if>
            <if test="zfrq != null" >
                ZFRQ,
            </if>
            <if test="kfbz != null" >
                KFBZ,
            </if>
            <if test="kfry != null" >
                KFRY,
            </if>
            <if test="kfrq != null" >
                KFRQ,
            </if>
            <if test="fybz != null" >
                FYBZ,
            </if>
            <if test="fyry != null" >
                FYRY,
            </if>
            <if test="fyrq != null" >
                FYRQ,
            </if>
            <if test="isprint != null" >
                ISPRINT,
            </if>
            <if test="bzsm != null" >
                BZSM,
            </if>
            <if test="sjje != null" >
                SJJE,
            </if>
            <if test="dzf != null" >
                DZF,
            </if>
            <if test="cfysxm != null" >
                CFYSXM,
            </if>
            <if test="brksmc != null" >
                BRKSMC,
            </if>
            <if test="hjryxm != null" >
                HJRYXM,
            </if>
            <if test="zfryxm != null" >
                ZFRYXM,
            </if>
            <if test="kfryxm != null" >
                KFRYXM,
            </if>
            <if test="fyryxm != null" >
                FYRYXM,
            </if>
            <if test="pyksmc != null" >
                PYKSMC,
            </if>
            <if test="pybz != null" >
                PYBZ,
            </if>
            <if test="pyryxm != null" >
                PYRYXM,
            </if>
            <if test="dzs != null" >
                DZS,
            </if>
            <if test="cflxmc != null" >
                CFLXMC,
            </if>
            <if test="pyks != null" >
                PYKS,
            </if>
            <if test="pyry != null" >
                PYRY,
            </if>
            <if test="pysj != null" >
                PYSJ,
            </if>
            <if test="sfdzcf != null" >
                SFDZCF,
            </if>
            <if test="tycfh != null" >
                TYCFH,
            </if>
            <if test="yljgbm != null" >
                YLJGBM,
            </if>
            <if test="yysmcf != null" >
                YYSMCF,
            </if>
            <if test="ywckbh != null" >
                YWCKBH,
            </if>
            <if test="yysm != null and yysm!=''" >
                YYSM,
            </if>
            <if test="zyzh != null and zyzh!=''" >
                ZYZH,
            </if>
            <if test="zyzf != null and zyzf!=''" >
                ZYZF,
            </if>
            <if test="zytt != null and zytt!=''" >
                zytt,
            </if>
            <if test="lxrxm != null and lxrxm!=''" >
                lxrxm,
            </if>
            <if test="lxrdh != null and lxrdh!=''" >
                lxrdh,
            </if>
            <if test="lxrsfzh != null and lxrsfzh!=''" >
                lxrsfzh,
            </if>
            <if test="mtbzbm != null and mtbzbm!=''" >
                mtbzbm,
            </if>
            <if test="mtbzmc != null and mtbzmc!=''" >
                mtbzmc,
            </if>
            <if test="sflzcf != null and sflzcf!=''" >
                sflzcf,
            </if>
            <if test="ishff != null and ishff!=''" >
                ishff,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="cfh != null" >
                #{cfh,jdbcType=VARCHAR},
            </if>
            <if test="cfrq != null" >
                #{cfrq,jdbcType=TIMESTAMP},
            </if>
            <if test="yfbm != null" >
                #{yfbm,jdbcType=VARCHAR},
            </if>
            <if test="cflx != null" >
                #{cflx,jdbcType=VARCHAR},
            </if>
            <if test="brlx != null" >
                #{brlx,jdbcType=VARCHAR},
            </if>
            <if test="bah != null" >
                #{bah,jdbcType=VARCHAR},
            </if>
            <if test="brxm != null" >
                #{brxm,jdbcType=VARCHAR},
            </if>
            <if test="lczd != null" >
                #{lczd,jdbcType=VARCHAR},
            </if>
            <if test="cfys != null" >
                #{cfys,jdbcType=VARCHAR},
            </if>
            <if test="brks != null" >
                #{brks,jdbcType=VARCHAR},
            </if>
            <if test="hjry != null" >
                #{hjry,jdbcType=VARCHAR},
            </if>
            <if test="zyfs != null" >
                #{zyfs,jdbcType=DECIMAL},
            </if>
            <if test="cfje != null" >
                #{cfje,jdbcType=DECIMAL},
            </if>
            <if test="zfbz != null" >
                #{zfbz,jdbcType=VARCHAR},
            </if>
            <if test="zfry != null" >
                #{zfry,jdbcType=VARCHAR},
            </if>
            <if test="zfrq != null" >
                #{zfrq,jdbcType=TIMESTAMP},
            </if>
            <if test="kfbz != null" >
                #{kfbz,jdbcType=VARCHAR},
            </if>
            <if test="kfry != null" >
                #{kfry,jdbcType=VARCHAR},
            </if>
            <if test="kfrq != null" >
                #{kfrq,jdbcType=TIMESTAMP},
            </if>
            <if test="fybz != null" >
                #{fybz,jdbcType=VARCHAR},
            </if>
            <if test="fyry != null" >
                #{fyry,jdbcType=VARCHAR},
            </if>
            <if test="fyrq != null" >
                #{fyrq,jdbcType=TIMESTAMP},
            </if>
            <if test="isprint != null" >
                #{isprint,jdbcType=VARCHAR},
            </if>
            <if test="bzsm != null" >
                #{bzsm,jdbcType=VARCHAR},
            </if>
            <if test="sjje != null" >
                #{sjje,jdbcType=DECIMAL},
            </if>
            <if test="dzf != null" >
                #{dzf,jdbcType=DECIMAL},
            </if>
            <if test="cfysxm != null" >
                #{cfysxm,jdbcType=VARCHAR},
            </if>
            <if test="brksmc != null" >
                #{brksmc,jdbcType=VARCHAR},
            </if>
            <if test="hjryxm != null" >
                #{hjryxm,jdbcType=VARCHAR},
            </if>
            <if test="zfryxm != null" >
                #{zfryxm,jdbcType=VARCHAR},
            </if>
            <if test="kfryxm != null" >
                #{kfryxm,jdbcType=VARCHAR},
            </if>
            <if test="fyryxm != null" >
                #{fyryxm,jdbcType=VARCHAR},
            </if>
            <if test="pyksmc != null" >
                #{pyksmc,jdbcType=VARCHAR},
            </if>
            <if test="pybz != null" >
                #{pybz,jdbcType=VARCHAR},
            </if>
            <if test="pyryxm != null" >
                #{pyryxm,jdbcType=VARCHAR},
            </if>
            <if test="dzs != null" >
                #{dzs,jdbcType=DECIMAL},
            </if>
            <if test="cflxmc != null" >
                #{cflxmc,jdbcType=VARCHAR},
            </if>
            <if test="pyks != null" >
                #{pyks,jdbcType=VARCHAR},
            </if>
            <if test="pyry != null" >
                #{pyry,jdbcType=VARCHAR},
            </if>
            <if test="pysj != null" >
                #{pysj,jdbcType=TIMESTAMP},
            </if>
            <if test="sfdzcf != null" >
                #{sfdzcf,jdbcType=VARCHAR},
            </if>
            <if test="tycfh != null" >
                #{tycfh,jdbcType=VARCHAR},
            </if>
            <if test="yljgbm != null" >
                #{yljgbm,jdbcType=VARCHAR},
            </if>
            <if test="yysmcf != null" >
                #{yysmcf,jdbcType=VARCHAR},
            </if>
            <if test="ywckbh != null" >
                #{ywckbh,jdbcType=VARCHAR},
            </if>
            <if test="yysm != null and yysm!=''" >
                #{yysm,jdbcType=VARCHAR},
            </if>
            <if test="zyzh != null and zyzh!=''" >
                #{zyzh,jdbcType=VARCHAR},
            </if>
            <if test="zyzf != null and zyzf!=''" >
                #{zyzf,jdbcType=VARCHAR},
            </if>
            <if test="zytt != null and zytt!=''" >
                #{zytt,jdbcType=VARCHAR},
            </if>
            <if test="lxrxm != null and lxrxm!=''" >
                #{lxrxm,jdbcType=VARCHAR},
            </if>
            <if test="lxrdh != null and lxrdh!=''" >
                #{lxrdh,jdbcType=VARCHAR},
            </if>
            <if test="lxrsfzh != null and lxrsfzh!=''" >
                #{lxrsfzh,jdbcType=VARCHAR},
            </if>
            <if test="mtbzbm != null and mtbzbm!=''" >
                #{mtbzbm,jdbcType=VARCHAR},
            </if>
            <if test="mtbzmc != null and mtbzmc!=''" >
                #{mtbzmc,jdbcType=VARCHAR},
            </if>
            <if test="sflzcf != null and sflzcf!=''" >
                #{sflzcf,jdbcType=VARCHAR},
            </if>
            <if test="ishff != null and ishff!=''" >
                #{ishff,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 修改处方 -->
    <update id="updatecf" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel">
        update YFB_YPCF
        <set >
            <if test="cfrq != null" >
                CFRQ = #{cfrq,jdbcType=TIMESTAMP},
            </if>
            <if test="yfbm != null" >
                YFBM = #{yfbm,jdbcType=VARCHAR},
            </if>
            <if test="cflx != null" >
                CFLX = #{cflx,jdbcType=VARCHAR},
            </if>
            <if test="brlx != null" >
                BRLX = #{brlx,jdbcType=VARCHAR},
            </if>
            <if test="bah != null" >
                BAH = #{bah,jdbcType=VARCHAR},
            </if>
            <if test="brxm != null" >
                BRXM = #{brxm,jdbcType=VARCHAR},
            </if>
            <if test="lczd != null" >
                LCZD = #{lczd,jdbcType=VARCHAR},
            </if>
            <if test="cfys != null" >
                CFYS = #{cfys,jdbcType=VARCHAR},
            </if>
            <if test="brks != null" >
                BRKS = #{brks,jdbcType=VARCHAR},
            </if>
            <if test="hjry != null" >
                HJRY = #{hjry,jdbcType=VARCHAR},
            </if>
            <if test="zyfs != null and zyfs!=0" >
                ZYFS = #{zyfs,jdbcType=DECIMAL},
            </if>
            <if test="cfje != null" >
                CFJE = #{cfje,jdbcType=DECIMAL},
            </if>
            <if test="zfbz != null" >
                ZFBZ = #{zfbz,jdbcType=VARCHAR},
            </if>
            <if test="zfry != null" >
                ZFRY = #{zfry,jdbcType=VARCHAR},
            </if>
            <if test="zfrq != null" >
                ZFRQ = #{zfrq,jdbcType=TIMESTAMP},
            </if>
            <if test="kfbz != null" >
                KFBZ = #{kfbz,jdbcType=VARCHAR},
            </if>
            <if test="kfry != null" >
                KFRY = #{kfry,jdbcType=VARCHAR},
            </if>
            <if test="kfrq != null" >
                KFRQ = #{kfrq,jdbcType=TIMESTAMP},
            </if>
            <if test="fybz != null" >
                FYBZ = #{fybz,jdbcType=VARCHAR},
            </if>
            <if test="fyry != null" >
                FYRY = #{fyry,jdbcType=VARCHAR},
            </if>
            <if test="fyrq != null" >
                FYRQ = #{fyrq,jdbcType=TIMESTAMP},
            </if>
            <if test="isprint != null" >
                ISPRINT = #{isprint,jdbcType=VARCHAR},
            </if>
            <if test="bzsm != null" >
                BZSM = #{bzsm,jdbcType=VARCHAR},
            </if>
            <if test="sjje != null" >
                SJJE = #{sjje,jdbcType=DECIMAL},
            </if>
            <if test="dzf != null" >
                DZF = #{dzf,jdbcType=DECIMAL},
            </if>
            <if test="cfysxm != null" >
                CFYSXM = #{cfysxm,jdbcType=VARCHAR},
            </if>
            <if test="brksmc != null" >
                BRKSMC = #{brksmc,jdbcType=VARCHAR},
            </if>
            <if test="hjryxm != null" >
                HJRYXM = #{hjryxm,jdbcType=VARCHAR},
            </if>
            <if test="zfryxm != null" >
                ZFRYXM = #{zfryxm,jdbcType=VARCHAR},
            </if>
            <if test="kfryxm != null" >
                KFRYXM = #{kfryxm,jdbcType=VARCHAR},
            </if>
            <if test="fyryxm != null" >
                FYRYXM = #{fyryxm,jdbcType=VARCHAR},
            </if>
            <if test="pyksmc != null" >
                PYKSMC = #{pyksmc,jdbcType=VARCHAR},
            </if>
            <if test="pybz != null" >
                PYBZ = #{pybz,jdbcType=VARCHAR},
            </if>
            <if test="pyryxm != null" >
                PYRYXM = #{pyryxm,jdbcType=VARCHAR},
            </if>
            <if test="dzs != null" >
                DZS = #{dzs,jdbcType=DECIMAL},
            </if>
            <if test="cflxmc != null" >
                CFLXMC = #{cflxmc,jdbcType=VARCHAR},
            </if>
            <if test="pyks != null" >
                PYKS = #{pyks,jdbcType=VARCHAR},
            </if>
            <if test="pyry != null" >
                PYRY = #{pyry,jdbcType=VARCHAR},
            </if>
            <if test="pysj != null" >
                PYSJ = #{pysj,jdbcType=TIMESTAMP},
            </if>
            <if test="sfdzcf != null" >
                SFDZCF = #{sfdzcf,jdbcType=VARCHAR},
            </if>
            <if test="tycfh != null" >
                TYCFH = #{tycfh,jdbcType=VARCHAR},
            </if>
            <if test="yysmcf != null" >
                YYSM = #{yysmcf,jdbcType=VARCHAR},
            </if>
            <if test="ywckbh != null" >
                YWCKBH = #{ywckbh,jdbcType=VARCHAR},
            </if>
            <if test="yysm != null and yysm!=''" >
                YYSM=#{yysm,jdbcType=VARCHAR},
            </if>
            <if test="zyzh != null and zyzh!=''" >
                ZYZH=#{zyzh,jdbcType=VARCHAR},
            </if>
            <if test="zyzf != null and zyzf!=''" >
                ZYZF=#{zyzf,jdbcType=VARCHAR},
            </if>
            <if test="zytt != null and zytt!=''" >
                zytt = #{zytt,jdbcType=VARCHAR},
            </if>
            <if test="lxrsfzh != null and lxrsfzh!=''" >
                lxrsfzh = #{lxrsfzh,jdbcType=VARCHAR},
            </if>
            <if test="mtbzbm != null and mtbzbm!=''" >
                mtbzbm = #{mtbzbm,jdbcType=VARCHAR},
            </if>
            <if test="mtbzmc != null and mtbzmc!=''" >
                mtbzmc = #{mtbzmc,jdbcType=VARCHAR},
            </if>
            <if test="ishff != null and ishff!=''" >
                ishff = #{ishff,jdbcType=VARCHAR},
            </if>
        </set>
        where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        <if test="cfh != null and cfh != ''">
            and CFH = #{cfh,jdbcType=VARCHAR}
        </if>

        <if test="searchcfh != null" >
            and CFH in
            <foreach item="item" index="index" collection="searchcfh" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <!-- 处方发药 -->
    <update id="UpdateCffy"  parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"  >
	UPDATE YFB_YPCF set FYBZ = '1',FYRQ = #{fyrq,jdbcType=TIMESTAMP} ,
	FYRY = #{fyry,jdbcType=VARCHAR}
	WHERE CFH = #{cfh,jdbcType=VARCHAR} and YLJGBM = #{yljgbm,jdbcType=VARCHAR}
	 AND FYBZ = '0' AND ZFBZ = '0'
</update>

    <!-- 处方作废 -->
    <update id="UpdateCfzf"  parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"  >
        UPDATE YFB_YPCF
        set
        <if test='zfbz == "1"' >
            ZFBZ = '1',ZFRQ = #{zfrq,jdbcType=TIMESTAMP},ZFRY = #{zfry,jdbcType=VARCHAR},ZFRYXM = #{zfryxm,jdbcType=VARCHAR},bzsm=bzsm||'退费作废'
        </if>
        <if test='zfbz == "0"' >
            ZFBZ = '0',ZFRQ = null,ZFRY = null,ZFRYXM=null,bzsm=bzsm||'部分退费还原'
        </if>
        <if test="cfshbz != null" >
            CFSHBZ = #{cfshbz,jdbcType=VARCHAR}
            ,CFSHRQ =sysdate
        </if>
        <if test="fybz != null" >
            ,FYBZ = #{fybz,jdbcType=VARCHAR}
        </if>
        <if test="zfshbz != null" >
            ,ZFSHBZ = #{zfshbz,jdbcType=VARCHAR}
        </if>
        <if test="zfshry != null" >
            ,ZFSHRY = #{zfshry,jdbcType=VARCHAR}
        </if>
        <if test="zfshryxm != null" >
            ,ZFSHRYXM = #{zfshryxm,jdbcType=VARCHAR}
        </if>
        <if test="zfshrq != null" >
            ,ZFSHRQ = #{zfshrq,jdbcType=TIMESTAMP}
        </if>
        <if test="cfshry != null" >
            ,CFSHRY = #{cfshry,jdbcType=VARCHAR}
        </if>
        <if test="cfshryxm != null" >
            ,CFSHRYXM = #{cfshryxm,jdbcType=VARCHAR}
        </if>
        <if test="ystysqbz ==2">
            YSTYSQSHRQ=sysdate,TYSQSHRY=#{tysqshry},TYSQSHRYXM=#{tysqshryxm},YSTYSQBZ=#{ystysqbz}
        </if>
        <if test="ystysqbz ==1">
            SQYSBM=#{sqysbm},SQYSXM=#{sqysxm},SQRQ=sysdate,YSTYSQBZ=#{ystysqbz},
            YSTYSQSHRQ='',TYSQSHRY='',TYSQSHRYXM=''
        </if>
        <if test="ystysqbz ==0">
            SQYSBM='',SQYSXM='',SQRQ='',YSTYSQBZ=#{ystysqbz},
            YSTYSQSHRQ='',TYSQSHRY='',TYSQSHRYXM=''
        </if>
        WHERE CFH = #{cfh,jdbcType=VARCHAR} AND YLJGBM = #{yljgbm,jdbcType=VARCHAR}
    </update>

    <!-- 处方作废审核 -->
    <update id="UpdateCfzfsh"  parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"  >
	UPDATE YFB_YPCF set ZFSHBZ = '1',ZFSHRQ = #{zfshrq,jdbcType=TIMESTAMP} ,
	ZFSHRY = #{zfshry,jdbcType=VARCHAR}
	WHERE CFH = #{cfh,jdbcType=VARCHAR} and YLJGBM = #{yljgbm,jdbcType=VARCHAR} AND ZFBZ = '1'
</update>
    <!-- 处方退药审核 -->
    <update id="UpdateCftysh"  parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"  >
	UPDATE YFB_YPCF set FYBZ = '1',FYRQ = #{fyrq,jdbcType=TIMESTAMP} ,
	FYRY = #{fyry,jdbcType=VARCHAR}
	WHERE CFH = #{cfh,jdbcType=VARCHAR} AND YLJGBM = #{yljgbm,jdbcType=VARCHAR} AND FYBZ = '0' AND ZFBZ = '0' AND KFBZ = '1'
</update>

    <!-- 根据挂号序号或住院号查询未扣费处方 sum(pf.cfyl*pf.yplj) -->
    <select id="queryWsfcf"  parameterType="java.util.HashMap"
            resultType="com.supx.csp.api.yfgl.yfyw.pojo.YfbYfywCfkfxmModel">
select cf.cfh,cf.yfbm,cf.cfys,cf.cfysxm,cf.brks,cf.brksmc, sum(pf.cfyl*pf.yplj) je,xm.mxfybm,xm.mxfymc,lb.lbbm,lb.lbmc,
yf.ksbm hsks,ks.ksmc hsksmc,cf.lczd,xm.ffylb
from yfb_yppf pf inner join yfb_ypcf cf  on cf.cfh = pf.cfh and cf.yljgbm = pf.yljgbm
inner join ykb_ypzd zd on pf.ypbm = zd.ypbm and pf.yljgbm = zd.yljgbm
inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
left join gyb_mxfyxm xm on zl.dyfyxm = xm.mxfybm and zl.yljgbm = xm.yljgbm
inner join gyb_fylb lb on lb.lbbm = xm.lbbm and lb.yljgbm = xm.yljgbm
inner join yfb_yf yf on cf.yfbm = yf.yfbm and cf.yljgbm = yf.yljgbm
inner join gyb_ksbm ks on yf.ksbm = ks.ksbm and yf.yljgbm = ks.yljgbm
where cf.kfbz = '0' and cf.zfbz = '0' and cf.bah = #{bah,jdbcType=VARCHAR}
and cf.brlx = #{brlx,jdbcType=VARCHAR} and cf.yljgbm = #{yljgbm,jdbcType=VARCHAR}
and cf.sflzcf='0'
and not exists (select 1 from mzb_brfy fy where fy.yzlx = '2' and fy.ryghxh = cf.bah
and fy.yzhm = cf.cfh and fy.yljgbm=cf.yljgbm and fy.yljgbm=#{yljgbm})
group by cf.cfh,cf.yfbm,cf.cfys,cf.cfysxm,cf.brks,cf.brksmc,xm.mxfybm,xm.mxfymc,lb.lbbm,lb.lbmc,yf.ksbm,ks.ksmc,cf.lczd,xm.ffylb
order by cfh
</select>

    <!-- 根据挂号序号或住院号查询未扣费处方 -->
    <select id="queryWsfcfOne"  parameterType="java.util.HashMap"
            resultType="com.supx.csp.api.yfgl.yfyw.pojo.YfbYfywCfkfxmModel">
select cf.cfh,cf.yfbm,cf.cfys,cf.cfysxm,cf.brks,cf.brksmc,sum(pf.cfyl*pf.yplj) je,xm.mxfybm,xm.mxfymc,lb.lbbm,lb.lbmc,
yf.ksbm hsks,ks.ksmc hsksmc,cf.lczd,cf.brxm
from yfb_yppf pf inner join yfb_ypcf cf  on cf.cfh = pf.cfh and cf.yljgbm = pf.yljgbm
inner join ykb_ypzd zd on pf.ypbm = zd.ypbm and pf.yljgbm = zd.yljgbm
inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm and zd.yljgbm = zl.yljgbm
left join gyb_mxfyxm xm on zl.dyfyxm = xm.mxfybm and zl.yljgbm = xm.yljgbm
inner join gyb_fylb lb on lb.lbbm = xm.lbbm and lb.yljgbm = xm.yljgbm
inner join yfb_yf yf on cf.yfbm = yf.yfbm and cf.yljgbm = yf.yljgbm
inner join gyb_ksbm ks on yf.ksbm = ks.ksbm and yf.yljgbm = ks.yljgbm
where cf.kfbz = '0' and cf.zfbz = '0' and cf.cfh = #{cfh,jdbcType=VARCHAR} and cf.yljgbm = #{yljgbm,jdbcType=VARCHAR}
and not exists (select 1 from mzb_brfy fy where fy.yzlx = '2' and fy.ryghxh = cf.bah and fy.yzhm = cf.cfh and fy.yljgbm=#{yljgbm})
group by cf.cfh,cf.yfbm,cf.cfys,cf.cfysxm,cf.brks,cf.brksmc,xm.mxfybm,xm.mxfymc,lb.lbbm,lb.lbmc,yf.ksbm,ks.ksmc,cf.lczd,cf.brxm
order by cfh
</select>

    <select id="cfShow" parameterType="com.supx.csp.api.yfgl.kcgl.pojo.Yfb_rkdModel" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_cfShowModel">
        select sum(dfy) dfy,sum(yfy) yfy from
        (
        select count(bah) dfy,0 yfy from yfb_ypcf
        where yljgbm = #{yljgbm,jdbcType=VARCHAR}
        and zfbz = '0' and fybz = '0'
        <if test="zdr != null and zdr != ''">
            and cfys = #{zdr,jdbcType=VARCHAR}
        </if>
        union all
        select 0 dfy,count(bah) yfy from yfb_ypcf
        where yljgbm = #{yljgbm,jdbcType=VARCHAR}
        and zfbz = '0' and fybz = '1'
        <if test="zdr != null and zdr != ''">
            and cfys = #{zdr,jdbcType=VARCHAR}
        </if>
        )
    </select>

    <!--药品处方基药-->
    <select id="getYpcfJy" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" resultType="double">
        SELECT nvl(SUM(CFJE),0) fy
        FROM yfb_ypcf cf
        LEFT JOIN yfb_yppf pf ON cf.yljgbm = pf.yljgbm AND cf.cfh = pf.cfh
        LEFT JOIN ykb_ypzd zd ON zd.yljgbm = pf.yljgbm AND zd.ypbm = pf.YPBM
        WHERE 1=1 AND (zd.GJJBYW = '1' OR zd.SBJBYW ='1') and cf.ZFBZ = '0' and cf.yljgbm = #{yljgbm}
        <if test="kfrq!=null and kfrq!=''">
            and to_char(cf.KFRQ,'yyyy-mm') = to_char(#{kfrq,jdbcType=DATE},'yyyy-mm')
        </if>
        <if test="brks!=null and brks!=''">
            and cf.brks = #{brks}
        </if>
    </select>
    <!--药品处方基药-->
    <select id="getYpcfKsssybl" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" resultType="double">
        SELECT nvl(SUM(CFJE),0) fy
        FROM yfb_ypcf cf
        LEFT JOIN yfb_yppf pf ON cf.yljgbm = pf.yljgbm AND cf.cfh = pf.cfh
        LEFT JOIN ykb_ypzd zd ON zd.yljgbm = pf.yljgbm AND zd.ypbm = pf.YPBM
        WHERE 1=1 AND (zd.GJJBYW = '1' OR zd.SBJBYW ='1') and cf.ZFBZ = '0' and cf.yljgbm = #{yljgbm}
        <if test="kfrq!=null and kfrq!=''">
            and to_char(cf.KFRQ,'yyyy-mm') = to_char(#{kfrq,jdbcType=DATE},'yyyy-mm')
        </if>
        <if test="brks!=null and brks!=''">
            and cf.brks = #{brks}
        </if>
        and zd.KSSJB != '1'
    </select>

    <!--验证药品库存和药品有效期-->
    <select id="checkKcAndDate" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            resultType="java.util.HashMap">
        SELECT (
        CASE
        WHEN kcxl.zdkc IS NULL
        THEN 0
        ELSE zdkc
        END ) AS "zdkc",
        kcxl.ypbm as "ypbm",
        (select sum(ypkc.kcsl) from yfb_ypkc ypkc where kcxl.ypbm=ypkc.ypbm and ypkc.yljgbm = #{yljgbm}) as "kcsl",
        (select min(yppf.yxqz) from yfb_yppf yppf where yppf.ypbm=kcxl.ypbm and yppf.yljgbm = #{yljgbm}) as "yxqz",
        (select min(yppf.ypmc) from yfb_yppf yppf where yppf.ypbm=kcxl.ypbm and yppf.yljgbm = #{yljgbm}) as "ypmc"
        FROM   yfb_kcxl kcxl
        WHERE  kcxl.yljgbm = #{yljgbm}
        <if test="cfh !=null">
            <if test="yljgbm !=null">
                and kcxl.ypbm in (select ypbm from yfb_yppf where cfh =#{cfh} and yljgbm = #{yljgbm})
            </if>
        </if>
    </select>


    <select id="queryBxjkByCfh" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel">

           SELECT * FROM MZB_JSJL JSJL INNER JOIN MZB_BRFY FY ON JSJL.JSJLID = FY.RYJSJLID AND JSJL.YLJGBM = FY.YLJGBM
            LEFT JOIN GYB_BXLB BXLB ON JSJL.RYBXLBBM = BXLB.BXLBBM AND JSJL.YLJGBM = BXLB.YLJGBM
            INNER JOIN GYB_BRJBXX BRJBXX ON BRJBXX.BRID = JSJL.RYBRID AND BRJBXX.YLJGBM = JSJL.YLJGBM
            WHERE FY.YLJGBM = #{yljgbm} AND FY.YZHM = #{cfh} AND FY.YZLX = '2'

    </select>

    <update id="tiaopei" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel">
        UPDATE YFB_YPCF SET
            TPCZY = #{tpczy},
            TPCZYXM = #{tpczyxm},
            TPRQ = #{tprq}
       WHERE CFH = #{cfh} AND YLJGBM = #{yljgbm} AND BAH = #{bah}
    </update>


    <update id="updateLzCfsh"  parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"  >
	UPDATE YFB_YPCF set LZCFSH = #{lzcfsh,jdbcType=VARCHAR},lzcfshsj = #{lzcfshsj,jdbcType=TIMESTAMP} ,
	lzcfshys = #{lzcfshys,jdbcType=VARCHAR}
	WHERE CFH = #{cfh,jdbcType=VARCHAR} and YLJGBM = #{yljgbm,jdbcType=VARCHAR}
	  AND ZFBZ = '0'
</update>


    <!-- 查询处方 用于流转处方展示含患者信息化 -->
    <select id="quertLzcf" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel"
            parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Yfb_ypcfModel" >
        select distinct  a.*,nvl(sfty.tyzt,0) as tyzt from (
        select ry.ryxm fyryxm,cf.CFH,cf.CFRQ,cf.YFBM,cf.CFLX,cf.CFLXMC,cf.BRLX,cf.BAH,cf.BRXM,cf.LCZD,cf.CFYS,cf.BRKS,cf.HJRY,cf.ZYFS,cf.CFJE,cf.ZFBZ,cf.ZFSHBZ,
        cf.ZFRY,cf.ZFRQ,cf.KFBZ,cf.KFRY,cf.KFRQ,cf.FYBZ,cf.FYRY,cf.FYRQ,cf.ISPRINT,decode(zf.zflxmc,null,'现金',zf.zflxmc) BZSM,cf.UPTIMESTAMP,cf.SJJE,
        cf.DZF,cf.DZS,cf.CFYSXM,cf.BRKSMC,cf.HJRYXM,cf.ZFRYXM,cf.KFRYXM,cf.PYKSMC,cf.PYBZ,cf.yysmcf,cf.PYRYXM,zc.brxb,gh.fbbm,gh.brnl,gh.nldw,fb.fbmc,
        to_char(gh.BRNL)|| case when gh.NLDW = '1' then '岁' when gh.NLDW = '2' then '月'
        when gh.NLDW = '3' then '周' when gh.NLDW = '4' then '天' else '' end NL,cf.PYKS,cf.TYCFH,cflx.cflb,
        bx.bxlbmc,gh.gms,cf.zyzf,cf.zyzh,cf.zyjs,bx.bxjk,gh.lstdbz,gh.lstdxh,cf.tpczy,cf.tpczyxm,cf.tprq,cf.cfshbz,cf.ystysqbz,cf.sqrq
        From YFB_YPCF cf left join ghb_brgh gh on cf.bah = gh.ghxh and cf.yljgbm = gh.yljgbm and gh.thbz = '0'
        left join yfb_cflx cflx on cflx.cflxbm=cf.cflx and cflx.yljgbm=cf.yljgbm
        left join gyb_brjbxx zc on gh.brid = zc.brid and gh.yljgbm = zc.yljgbm
        left join gyb_brfb fb on gh.fbbm = fb.fbbm and gh.yljgbm = fb.yljgbm
        left join gyb_rybm ry on ry.rybm = cf.fyry and ry.yljgbm = cf.yljgbm
        left join gyb_bxlb bx on gh.bxlbbm = bx.bxlbbm and gh.yljgbm = bx.yljgbm
        left join mzb_brfy fy on cf.cfh = fy.yzhm and cf.yljgbm = fy.yljgbm  and fy.sftf = '0'  and fy.yzlx = '2'
        left join mzb_jsjl js on fy.yljgbm = js.yljgbm and fy.ryjsjlid = js.jsjlid
        left join gyb_zflx zf on js.yljgbm = zf.yljgbm and js.zflxbm = zf.zflxbm

        <where>
            (cf.brlx = '0') and ( cf.yljgbm = #{yljgbm,jdbcType=VARCHAR} )
            and cf.sflzcf='1'
            <if test="parm != null and parm != '' ">
                and (cf.cfh like '%'||#{parm,jdbcType=VARCHAR}
                or instr(cf.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
                )
            </if>
            <if test="ylkh !=null and ylkh!=''">
                and gh.brid in (select brid from gyb_brylkxx where ylkh=#{ylkh,jdbcType=VARCHAR} and  yljgbm =#{yljgbm,jdbcType=VARCHAR} )
            </if>
            <if test="brxm != null">
                and cf.brxm = #{brxm,jdbcType=VARCHAR}
            </if>
            <if test="bxjk != null">
                and bx.bxjk is null
            </if>
            <if test="tycx != null">
                and (cf.cfje &gt; 0)
            </if>
            <if test="brlx != null ">
                and (cf.brlx = #{brlx,jdbcType=VARCHAR})
            </if>
            <if test="yfbm != null ">
                and (cf.yfbm = #{yfbm,jdbcType=VARCHAR})
            </if>
            <if test="lzcfsh != null ">
                and (cf.lzcfsh = #{lzcfsh,jdbcType=VARCHAR})
            </if>
            <if test="zfbz != null ">
                and (cf.zfbz = #{zfbz,jdbcType=VARCHAR})
            </if>
            <if test="zfshbz != null ">
                and (cf.zfshbz = #{zfshbz,jdbcType=VARCHAR})
            </if>
            <if test="sfdzcf != null ">
                and (cf.sfdzcf = #{sfdzcf,jdbcType=VARCHAR})
            </if>
            <if test="endrq != null ">
                and (cf.${cxrq} &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (cf.${cxrq} &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>

        </where>
        union all
        select ry.ryxm fyryxm,cf.CFH,cf.CFRQ,cf.YFBM,cf.CFLX,cf.CFLXMC,cf.BRLX,cf.BAH,cf.BRXM,cf.LCZD,cf.CFYS,cf.BRKS,cf.HJRY,cf.ZYFS,cf.CFJE,cf.ZFBZ,cf.ZFSHBZ,
        cf.ZFRY,cf.ZFRQ,cf.KFBZ,cf.KFRY,cf.KFRQ,cf.FYBZ,cf.FYRY,cf.FYRQ,cf.ISPRINT,cf.BZSM,cf.UPTIMESTAMP,cf.SJJE,
        cf.DZF,cf.DZS,cf.CFYSXM,cf.BRKSMC,cf.HJRYXM,cf.ZFRYXM,cf.KFRYXM,cf.PYKSMC,cf.PYBZ,cf.yysmcf,cf.PYRYXM,zc.brxb,dj.brfb fbbm,dj.nl brnl,dj.nldw,fb.fbmc,
        to_char(dj.NL)|| case when dj.NLDW = '1' then '岁' when dj.NLDW = '2' then '月'
        when dj.NLDW = '3' then '周' when dj.NLDW = '4' then '天' else '' end NL ,cf.PYKS,cf.TYCFH,cflx.cflb,
        bx.bxlbmc,null gms,cf.zyzf,cf.zyzh,cf.zyjs,bx.bxjk,'0' lstdbz,'0' lstdxh,cf.tpczy,cf.tpczyxm,cf.tprq,cf.cfshbz,cf.ystysqbz,cf.sqrq
        From YFB_YPCF cf inner join zyb_rydj dj on cf.bah = dj.zyh and cf.yljgbm = dj.yljgbm and dj.ifzf = '0'
        left join yfb_cflx cflx on cflx.cflxbm=cf.cflx and cflx.yljgbm=cf.yljgbm
        left join gyb_brjbxx zc on dj.brid = zc.brid and dj.yljgbm = zc.yljgbm
        left join gyb_brylkxx ylk on dj.brid=ylk.brid and dj.yljgbm=ylk.yljgbm
        left join gyb_brfb fb on dj.brfb = fb.fbbm and dj.yljgbm = fb.yljgbm
        left join gyb_rybm ry on ry.rybm = cf.fyry and ry.yljgbm = cf.yljgbm
        left join gyb_bxlb bx on dj.bxlbbm = bx.bxlbbm and dj.yljgbm = bx.yljgbm
        left join zyb_brfy fy on cf.bah = fy.zyh and cf.yljgbm = fy.yljgbm and fy.sftf = '0'
        <where>
            (cf.brlx = '1')  and ( cf.yljgbm = #{yljgbm,jdbcType=VARCHAR} )
            and cf.sflzcf='1'
            <if test="parm != null and parm != '' ">
                and (cf.cfh like '%'||#{parm,jdbcType=VARCHAR}
                or instr(cf.brxm,#{parm,jdbcType=VARCHAR}) &gt; 0
                )
            </if>
            <if test="bxjk != null">
                and bx.bxjk is null
            </if>
            <if test="tycx != null">
                and (cf.cfje &gt; 0)
            </if>
            <if test="brlx != null ">
                and (cf.brlx = #{brlx,jdbcType=VARCHAR})
            </if>
            <if test="yfbm != null ">
                and (cf.yfbm = #{yfbm,jdbcType=VARCHAR})
            </if>
            <if test="lzcfsh != null ">
                and (cf.lzcfsh = #{lzcfsh,jdbcType=VARCHAR})
            </if>
            <if test="zfbz != null ">
                and (cf.zfbz = #{zfbz,jdbcType=VARCHAR})
            </if>
            <if test="zfshbz != null ">
                and (cf.zfshbz = #{zfshbz,jdbcType=VARCHAR})
            </if>
            <if test="sfdzcf != null ">
                and (cf.sfdzcf = #{sfdzcf,jdbcType=VARCHAR})
            </if>
            <if test="endrq != null ">
                and (cf.${cxrq} &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="beginrq != null ">
                and (cf.${cxrq} &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>

        </where>
        ) a   left join (select (case when sum(cfyl)-sum(ytsl)  > 0  and sum(ytsl) > 0
        then '1' when sum(cfyl)-sum(ytsl) = 0 then '2' else '0' end) as tyzt,
        cfh from yfb_yppf  group by cfh)  sfty on a.cfh = sfty.cfh
        ORDER BY ${sort} ${order}

    </select>


    <select id="queryYpXls" resultType="com.supx.csp.api.yfgl.yfyw.pojo.Gyb_xysModel" parameterType="com.supx.csp.api.yfgl.yfyw.pojo.Gyb_xysModel">
        select a.ypbm,a.xls,nvl(sum(b.sys),0) sys from gyb_xys a
            left join (
                 select ypbm,sum(c.cfyl) sys from
                 yfb_ypcf b
                 inner join  yfb_yppf c on  b.cfh = c.cfh and b.brlx='0' and b.zfbz='0'
                 where b.bah=#{bah,jdbcType=VARCHAR}
                 group by ypbm
            ) b on a.ypbm = b.ypbm
            where a.tybz = '0' and a.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            group by a.ypbm,a.xls

    </select>


    <select id="getCfFyqk" resultType="java.util.Map" >
        select  fyxm.lbbm,sum(round(round(c.yplj,4)*c.cfyl,2)) fydj
        from  yfb_ypcf b
        inner join vv_yfb_yppf c on b.cfh=c.cfh and b.yljgbm=c.yljgbm
        inner join ykb_ypzd zd on c.YPBM = zd.ypbm
        inner join ykb_ypzl zl on zd.zlbm = zl.ypzlbm
        inner join gyb_mxfyxm fyxm on zl.dyfyxm = fyxm.mxfybm
        where   b.cfh=#{cfh,jdbcType=VARCHAR}
        group by fyxm.lbbm

    </select>

    <update id="zzupdateBrcf">
        update yfb_ypcf set cfje=#{cfje,jdbcType=DECIMAL} where cfh=#{cfh,jdbcType=VARCHAR} and bah = #{bah,jdbcType=VARCHAR}

    </update>

    <update id="zzupdateBrfy">
        update mzb_brfy set fyje=#{fyje,jdbcType=DECIMAL},fydj=#{fyje,jdbcType=DECIMAL} where ryghxh=#{ryghxh,jdbcType=VARCHAR} and fylb=#{fylb,jdbcType=VARCHAR} and yzhm=#{yzhm,jdbcType=VARCHAR}

    </update>

</mapper>
