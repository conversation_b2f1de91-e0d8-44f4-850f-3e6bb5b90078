<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supx.csp.cdyb.dao.Yb3101ModelMapper">
    <resultMap id="ResultPatientDto" type="com.supx.csp.api.cdyb.pojo.PatientModel">
        <id property="patn_id" column="PATN_ID" jdbcType="VARCHAR"/>
        <result property="patn_name" column="PATN_NAME" jdbcType="VARCHAR"/>
        <result property="gend" column="GEND" jdbcType="VARCHAR"/>
        <result property="brdy" column="BRDY" jdbcType="VARCHAR"/>
        <result property="poolarea" column="POOLAREA" jdbcType="VARCHAR"/>
        <result property="curr_mdtrt_id" column="CURR_MDTRT_ID" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="ResultFsiEncounter" type="com.supx.csp.api.cdyb.pojo.FsiEncounterModel">
        <id property="mdtrt_id" column="MDTRT_ID" jdbcType="VARCHAR"/>
        <result property="medins_id" column="MEDINS_ID" jdbcType="VARCHAR"/>
        <result property="medins_name" column="MEDINS_NAME" jdbcType="VARCHAR"/>
        <result property="medins_admdvs" column="MEDINS_ADMDVS" jdbcType="VARCHAR"/>
        <result property="medins_type" column="MEDINS_TYPE" jdbcType="VARCHAR"/>
        <result property="medins_lv" column="MEDINS_LV" jdbcType="VARCHAR"/>
        <result property="wardarea_codg" column="WARDAREA_CODG" jdbcType="VARCHAR"/>
        <result property="wardno" column="WARDNO" jdbcType="VARCHAR"/>
        <result property="bedno" column="BEDNO" jdbcType="VARCHAR"/>
        <result property="adm_date" column="ADM_DATE" jdbcType="VARCHAR"/>
        <result property="dscg_date" column="DSCG_DATE" jdbcType="VARCHAR"/>
        <result property="dscg_main_dise_codg" column="DSCG_MAIN_DISE_CODG" jdbcType="VARCHAR"/>
        <result property="dscg_main_dise_name" column="DSCG_MAIN_DISE_NAME" jdbcType="VARCHAR"/>
        <result property="dr_codg" column="DR_CODG" jdbcType="VARCHAR"/>
        <result property="adm_dept_codg" column="ADM_DEPT_CODG" jdbcType="VARCHAR"/>
        <result property="adm_dept_name" column="ADM_DEPT_NAME" jdbcType="VARCHAR"/>
        <result property="dscg_dept_codg" column="DSCG_DEPT_CODG" jdbcType="VARCHAR"/>
        <result property="dscg_dept_name" column="DSCG_DEPT_NAME" jdbcType="VARCHAR"/>
        <result property="med_mdtrt_type" column="MED_MDTRT_TYPE" jdbcType="VARCHAR"/>
        <result property="med_type" column="MED_TYPE" jdbcType="VARCHAR"/>
        <result property="matn_stas" column="MATN_STAS" jdbcType="VARCHAR"/>
        <result property="medfee_sumamt" column="MEDFEE_SUMAMT" jdbcType="DECIMAL"/>
        <result property="ownpay_amt" column="OWNPAY_AMT" jdbcType="DECIMAL"/>
        <result property="selfpay_amt" column="SELFPAY_AMT" jdbcType="DECIMAL"/>
        <result property="acct_payamt" column="ACCT_PAYAMT" jdbcType="DECIMAL"/>
        <result property="ma_amt" column="MA_AMT" jdbcType="DECIMAL"/>
        <result property="hifp_payamt" column="HIFP_PAYAMT" jdbcType="DECIMAL"/>
        <result property="setl_totlnum" column="SETL_TOTLNUM" jdbcType="INTEGER"/>
        <result property="insutype" column="INSUTYPE" jdbcType="VARCHAR"/>
        <result property="reim_flag" column="REIM_FLAG" jdbcType="VARCHAR"/>
        <result property="out_setl_flag" column="OUT_SETL_FLAG" jdbcType="VARCHAR"/>
        <result property="dise_no" column="DISE_NO" jdbcType="VARCHAR"/>
        <result property="dise_name" column="DISE_NAME" jdbcType="VARCHAR"/>
        <result property="sinc_heal_lv" column="SINC_HEAL_LV" jdbcType="VARCHAR"/>
        <result property="his_md_cnt" column="HIS_MD_CNT" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="ResultFsiDiagnose" type="com.supx.csp.api.cdyb.pojo.FsiDiagnoseModel">
        <id property="dise_id" column="DISE_ID" jdbcType="VARCHAR"/>
        <result property="inout_dise_type" column="INOUT_DISE_TYPE" jdbcType="VARCHAR"/>
        <result property="maindise_flag" column="MAINDISE_FLAG" jdbcType="VARCHAR"/>
        <result property="dise_codg" column="DISE_CODG" jdbcType="VARCHAR"/>
        <result property="dise_name" column="DISE_NAME" jdbcType="VARCHAR"/>
        <result property="dise_date" column="DISE_DATE" jdbcType="VARCHAR"/>
        <result property="dias_srt_no" column="DIAS_SRT_NO" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="ResultFsiOrder" type="com.supx.csp.api.cdyb.pojo.FsiOrderModel">
        <id property="rx_id" column="RX_ID" jdbcType="VARCHAR"/>
        <result property="rxno" column="RXNO" jdbcType="VARCHAR"/>
        <result property="grpno" column="GRPNO" jdbcType="VARCHAR"/>
        <result property="long_drord_flag" column="LONG_DRORD_FLAG" jdbcType="VARCHAR"/>
        <result property="hilist_type" column="HILIST_TYPE" jdbcType="VARCHAR"/>
        <result property="chrg_type" column="CHRG_TYPE" jdbcType="VARCHAR"/>
        <result property="drord_bhvr" column="DRORD_BHVR" jdbcType="VARCHAR"/>
        <result property="hilist_code" column="HILIST_CODE" jdbcType="VARCHAR"/>
        <result property="hilist_name" column="HILIST_NAME" jdbcType="VARCHAR"/>
        <result property="hilist_dosform" column="HILIST_DOSFORM" jdbcType="VARCHAR"/>
        <result property="hilist_lv" column="HILIST_LV" jdbcType="VARCHAR"/>
        <result property="hilist_pric" column="HILIST_PRIC" jdbcType="DECIMAL"/>
        <result property="lv1_hosp_item_pric" column="LV1_HOSP_ITEM_PRIC" jdbcType="DECIMAL"/>
        <result property="lv2_hosp_item_pric" column="LV2_HOSP_ITEM_PRIC" jdbcType="DECIMAL"/>
        <result property="lv3_hosp_item_pric" column="LV3_HOSP_ITEM_PRIC" jdbcType="DECIMAL"/>
        <result property="hilist_memo" column="HILIST_MEMO" jdbcType="VARCHAR"/>
        <result property="hosplist_code" column="HOSPLIST_CODE" jdbcType="VARCHAR"/>
        <result property="hosplist_name" column="HOSPLIST_NAME" jdbcType="VARCHAR"/>
        <result property="hosplist_dosform" column="HOSPLIST_DOSFORM" jdbcType="VARCHAR"/>
        <result property="cnt" column="CNT" jdbcType="DECIMAL"/>
        <result property="pric" column="PRIC" jdbcType="DECIMAL"/>
        <result property="sumamt" column="SUMAMT" jdbcType="DECIMAL"/>
        <result property="ownpay_amt" column="OWNPAY_AMT" jdbcType="DECIMAL"/>
        <result property="selfpay_amt" column="SELFPAY_AMT" jdbcType="DECIMAL"/>
        <result property="spec" column="SPEC" jdbcType="VARCHAR"/>
        <result property="spec_unt" column="SPEC_UNT" jdbcType="VARCHAR"/>
        <result property="drord_begn_date" column="DRORD_BEGN_DATE" jdbcType="VARCHAR"/>
        <result property="drord_stop_date" column="DRORD_STOP_DATE" jdbcType="VARCHAR"/>
        <result property="drord_dept_codg" column="DRORD_DEPT_CODG" jdbcType="VARCHAR"/>
        <result property="drord_dept_name" column="DRORD_DEPT_NAME" jdbcType="VARCHAR"/>
        <result property="drord_dr_codg" column="DRORD_DR_CODG" jdbcType="VARCHAR"/>
        <result property="drord_dr_name" column="DRORD_DR_NAME" jdbcType="VARCHAR"/>
        <result property="drord_dr_profttl" column="DRORD_DR_PROFTTL" jdbcType="VARCHAR"/>
        <result property="curr_drord_flag" column="CURR_DRORD_FLAG" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="ResultFsiOperation" type="com.supx.csp.api.cdyb.pojo.FsiOperationModel">
        <id property="setl_list_oprn_id" column="SETL_LIST_OPRN_ID" jdbcType="VARCHAR"/>
        <result property="oprn_code" column="OPRN_CODE" jdbcType="VARCHAR"/>
        <result property="oprn_name" column="OPRN_NAME" jdbcType="VARCHAR"/>
        <result property="main_oprn_flag" column="MAIN_OPRN_FLAG" jdbcType="VARCHAR"/>
        <result property="oprn_date" column="OPRN_DATE" jdbcType="VARCHAR"/>
        <result property="anst_way" column="ANST_WAY" jdbcType="VARCHAR"/>
        <result property="oper_dr_name" column="OPER_DR_NAME" jdbcType="VARCHAR"/>
        <result property="oper_dr_code" column="OPER_DR_CODE" jdbcType="VARCHAR"/>
        <result property="anst_dr_name" column="ANST_DR_NAME" jdbcType="VARCHAR"/>
        <result property="anst_dr_code" column="ANST_DR_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryPatient" resultMap="ResultPatientDto" parameterType="string">
        select * from (
          select
              rybh as patn_id,
              ryxm as patn_name,
              xb as gend,
              csrq as brdy,
              cbdqybqh as poolarea,
              jzid as curr_mdtrt_id
          from gssldyb_zydjxx where zfbz = '0' and zyh = #{zyh} )
          where patn_id is not null and patn_name is not null and gend is not null and brdy is not null
          and poolarea is not null and curr_mdtrt_id is not null
    </select>

    <select id="queryFsiEncounter" resultMap="ResultFsiEncounter" parameterType="string">
        select * from (
          select
              djxx.jzid as mdtrt_id,
              'H51010602813' as medins_id,
              '四川省建筑医院' as medins_name,
              '510100' as medins_admdvs,
              'A1' as medins_type,
              '12' as medins_lv,
              bq.bqmc as wardarea_codg,
              cwh.fjh as wardno,
              dj.rycwbh as bedno,
              dj.ryrq as adm_date,
              zdxx.cyrq as dscg_date,
              dj.ryzdbm as dscg_main_dise_codg,
              dj.ryzdmc as dscg_main_dise_name,
              nvl(dj.zyys,'07') as dr_codg,
              dj.ryks as adm_dept_codg,
              dj.ryksmc as adm_dept_name,
              dj.ryks as dscg_dept_codg,
              dj.ryksmc as dscg_dept_name,
              '2' as med_mdtrt_type,
              djxx.yllb as med_type,
              '0' as matn_stas,
              nvl(round(sum(brfy.fyje),6),0) as medfee_sumamt,
              '' as ownpay_amt,
              '' as selfpay_amt,
              '' as acct_payamt,
              '' as ma_amt,
              ''as hifp_payamt,
              1 as setl_totlnum,
              djxx.xzlx as insutype,
              '' as reim_flag,
              '' as out_setl_flag,
              dj.ryzdbm as dise_no,
              dj.ryzdmc as dise_name,
              '' as sinc_heal_lv,
              '' as his_md_cnt
          from zyb_rydj dj
               inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
               left join gyb_ksbm ks on dj.ryks = ks.ksbm
               left join gyb_bq bq on ks.bqbm = bq.bqbm
               left join zyb_cwh cwh on dj.rycwid = cwh.cwid
               left join bagl_basy_zdxx zdxx on dj.zyh = zdxx.zyh
               left join gyb_ksbm cyks on cyks.ksbm = zdxx.cyks
               left join zyb_brfy brfy on brfy.zyh = dj.zyh
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'  and dj.ifzf = '0'
          group by djxx.jzid,bq.bqmc,cwh.fjh,dj.rycwbh,dj.ryrq,zdxx.cyrq,dj.ryzdbm,dj.ryzdmc,dj.zyys,dj.ryks,dj.ryksmc,zdxx.cyks,cyks.ksmc,djxx.yllb,djxx.xzlx,dj.ryzdbm,dj.ryzdmc
      ) where adm_date is not null and adm_dept_codg is not null and adm_dept_name is not null  and dr_codg is not null and dscg_dept_codg is not null
          and dscg_dept_name is not null and dscg_main_dise_codg is not null and dscg_main_dise_name is not null and insutype is not null
          and mdtrt_id is not null and med_type is not null
    </select>

    <select id="queryFsiDiagnose" resultMap="ResultFsiDiagnose" parameterType="string">
        select rownum as dias_srt_no,bb.* from (
           select * from (
                 select
                     dj.ryzdbm as dise_id,
                     '1' as inout_dise_type,
                     '0' as maindise_flag,
                     dj.ryzdbm as dise_codg,
                     dj.ryzdmc as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyzyzdbm as dise_id,
                     '2' as inout_dise_type,
                     '1' as maindise_flag,
                     zdxx.cyzyzdbm as dise_codg,
                     zdxx.cyzyzdmc as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm1 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm1 as dise_codg,
                     zdxx.cyqtzdmc1 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm2 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm2 as dise_codg,
                     zdxx.cyqtzdmc2 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm3 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm3 as dise_codg,
                     zdxx.cyqtzdmc3 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm4 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm4 as dise_codg,
                     zdxx.cyqtzdmc4 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm5 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm5 as dise_codg,
                     zdxx.cyqtzdmc5 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm6 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm6 as dise_codg,
                     zdxx.cyqtzdmc6 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm7 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm7 as dise_codg,
                     zdxx.cyqtzdmc7 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm8 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm8 as dise_codg,
                     zdxx.cyqtzdmc8 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm9 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm9 as dise_codg,
                     zdxx.cyqtzdmc9 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm10 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm10 as dise_codg,
                     zdxx.cyqtzdmc10 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm11 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm11 as dise_codg,
                     zdxx.cyqtzdmc11 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
                 union all
                 select
                     zdxx.cyqtzdbm12 as dise_id,
                     '2' as inout_dise_type,
                     '0' as maindise_flag,
                     zdxx.cyqtzdbm12 as dise_codg,
                     zdxx.cyqtzdmc12 as dise_name,
                     dj.ryrq as dise_date
                 from zyb_rydj dj inner join gssldyb_zydjxx djxx on dj.zyh = djxx.zyh
                                  inner join bagl_basy_zdxx zdxx on zdxx.zyh = dj.zyh
                 where djxx.zyh = #{zyh}  and djxx.zfbz = '0'
             )  aa
           where dise_id is not null and inout_dise_type is not null
             and maindise_flag is not null  and dise_codg is not null and  dise_name is not null and dise_date is not null
           group by dise_id,inout_dise_type,maindise_flag,dise_codg,dise_name,dise_date ) bb
    </select>

    <select id="queryFsiOrder" resultMap="ResultFsiOrder" parameterType="string">
        select * from (
          select
              yz.xhid as rx_id,
              yz.ypyzxh as rxno,
              yz.fzh as grpno,
              decode(yz.yzlx,'1','1','0') as long_drord_flag,
              case when yz.ypzl in ('01','02') then '101' when yz.ypzl = '03' then '102' when yz.ypzl = '09' then '106' else '105' end as hilist_type,
              case when yz.ypzl = '01' then '09'  when yz.ypzl = '02' then '11' when yz.ypzl in('03','09') then '10'  else '14' end as chrg_type,
              decode (yz.yzfl,'10','1','0') as drord_bhvr,
              bxxm.bxxmbm_gjbm as hilist_code,
              bxxm.bxxmmc_gjmc as hilist_name,
              jx.jxmc as hilist_dosform,
              case when zd.ybtclb in('92','97','95') then '01' when zd.ybtclb in('98','93','96') then '02'  else '03' end as hilist_lv,
              to_char(zd.ypjj,'FM999999990.00') as hilist_pric,
              '' as lv1_hosp_item_pric,
              '' as lv2_hosp_item_pric,
              '' as lv3_hosp_item_pric,
              '' as hilist_memo,
              yz.ryypbm as hosplist_code,
              yz.ryypmc as hosplist_name,
              jx.jxmc as hosplist_dosform,
              to_char(yz.yyzl,'FM999999990.0000') as cnt,
              to_char(yz.dj,'FM999999990.00') as pric,
              to_char(sum(yz.yyzl * yz.dj),'FM999999990.00') as sumamt,
              '' as ownpay_amt,
              '' as selfpay_amt,
              yz.ypgg as spec,
              yz.yfdwmc as spec_unt,
              yz.ksrq as drord_begn_date,
              yz.ystzsj as drord_stop_date,
              ksbm.frmz_ptbzbm as drord_dept_codg,
              yz.ksmc as drord_dept_name,
              xdys.ybdm as drord_dr_codg,
              yz.xdysxm as drord_dr_name,
              decode(xdys.jszgdm,'05','1','04','2','03','3','02','4','01','5','9') as drord_dr_profttl,
              '1' as curr_drord_flag
          from gssldyb_zydjxx djxx
               inner join zyys_ypyz yz on djxx.zyh = yz.zyh
               inner join gyb_ypbxxm bxxm on bxxm.ypbm = yz.ryypbm and bxxm.bxlbbm = '02'
               inner join ykb_ypzd zd on zd.ypbm = yz.ryypbm
               left join ykb_ypjx jx on jx.jxbm = zd.jxbm
               inner join gyb_rybm rybm on yz.xdys = rybm.rybm
               inner join gyb_ksbm ksbm on ksbm.ksbm = yz.ksbm
               inner join gyb_rybm xdys on xdys.rybm = yz.xdys
          where djxx.zyh = #{zyh} and djxx.zfbz = '0' and yz.zfbz = '0' and to_char(yz.djrq,'yyyy-mm-dd') = to_char(sysdate,'yyyy-mm-dd')
          group by yz.xhid,yz.ypyzxh,yz.fzh,yz.yzlx,yz.ypzl,yz.yzfl,bxxm.bxxmbm_gjbm,bxxm.bxxmmc_gjmc,jx.jxmc,zd.ypjj,yz.ryypbm,yz.ryypmc,jx.jxmc,yz.yyzl,yz.dj,
                   yz.ypgg,yz.yfdwmc,yz.ksrq,yz.ystzsj,ksbm.frmz_ptbzbm,yz.ksmc,xdys.ybdm,yz.xdysxm,zd.ybtclb,xdys.jszgdm
          union all
          select
              yz.xhid as rx_id,
              yz.ylyzxh as rxno,
              0 as grpno,
              decode(yz.yzlx,'1','1','0') as long_drord_flag,
              case when mxfy.lbbm in('228') then '301' else '201' end  as hilist_type,
              decode(mxfy.lbbm,'202','01','222','02','204','03','210','04','203','05','213','06','221','07','228','08','11','09','13','10','12','11','220','13','14')  as chrg_type,
              '0' as drord_bhvr,
              bxxm.bxxmbm_gjbm as hilist_code,
              bxxm.bxxmmc_gjbm as hilist_name,
              '' as hilist_dosform,
              '01' as hilist_lv,
              to_char(mxfy.fydj,'FM999999990.00') as hilist_pric,
              '' as lv1_hosp_item_pric,
              '' as lv2_hosp_item_pric,
              '' as lv3_hosp_item_pric,
              '' as hilist_memo,
              mxfy.mxfybm as hosplist_code,
              mxfy.mxfymc as hosplist_name,
              '' as hosplist_dosform,
              '1.0000' as cnt,
              to_char(mxfy.fydj,'FM999999990.00') as pric,
              to_char(mxfy.fydj,'FM999999990.00') as sumamt,
              '' as ownpay_amt,
              '' as selfpay_amt,
              '次' as spec,
              '次' as spec_unt,
              yz.ksrq as drord_begn_date,
              yz.ystzsj as drord_stop_date,
              ksbm.frmz_ptbzbm as drord_dept_codg,
              yz.ksmc as drord_dept_name,
              xdys.ybdm as drord_dr_codg,
              yz.xdysxm as drord_dr_name,
              decode(xdys.jszgdm,'05','1','04','2','03','3','02','4','01','5','9') as drord_dr_profttl,
              '1' as curr_drord_flag
          from gssldyb_zydjxx djxx
               inner join zyys_ylyz yz on djxx.zyh = yz.zyh
               left join gyb_zlxmglfy glfy on yz.rymxzlbm = glfy.zlxmbm
               left join gyb_mxfyxm mxfy on mxfy.mxfybm = glfy.fyxmbm
               left join gyb_zlbxxm bxxm on bxxm.fyxmbm = mxfy.mxfybm and bxxm.bxlbbm = '02'
               left join gyb_rybm rybm on yz.xdys = rybm.rybm
               left join gyb_ksbm ksbm on ksbm.ksbm = yz.ksbm
               left join gyb_rybm xdys on xdys.rybm = yz.xdys
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
            and to_char(yz.djrq,'yyyy-mm-dd') = to_char(sysdate,'yyyy-mm-dd')  and yz.zfbz = '0'
      )
        where chrg_type is not null and cnt is not null and curr_drord_flag is not null and drord_begn_date is not null and drord_bhvr is not null
          and drord_dept_codg is not null and drord_dr_codg is not null
          and drord_dr_name is not null and drord_dr_profttl is not null and grpno is not null and hilist_code is not null and hilist_lv is not null
          and hilist_name is not null and hilist_pric is not null and hilist_type is not null and hosplist_code is not null
          and hosplist_name is not null and long_drord_flag is not null and pric is not null and rx_id is not null and rxno is not null
          and spec is not null and spec_unt is not null  and sumamt is not null
    </select>

    <select id="queryFsiOperation" resultMap="ResultFsiOperation" parameterType="string">
        select * from (
          select
              ssxx.ssbm1 as setl_list_oprn_id,
              ssxx.ssbm1 as oprn_code,
              ssxx.ssmc1 as oprn_name,
              '' as main_oprn_flag,
              ssxx.ssrq1 as oprn_date,
              '' as anst_way
              ssys1.ryxm as oper_dr_name,
              ssys1.ybdm as oper_dr_code,
              mzys1.ryxm as anst_dr_name,
              mzys1.ybdm as anst_dr_code
          from gssldyb_zydjxx djxx
               inner join bagl_basy_ssxx ssxx on djxx.zyh = ssxx.zyh
               left join gyb_rybm ssys1 on ssys1.rybm = ssxx.ssys1
               left join gyb_rybm mzys1 on mzys1.rybm = ssxx.mzys1
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
          union all
          select
              ssxx.ssbm2 as setl_list_oprn_id,
              ssxx.ssbm2 as oprn_code,
              ssxx.ssmc2 as oprn_name,
              '' as main_oprn_flag,
              ssxx.ssrq2 as oprn_date,
              '' as anst_way,
              ssys2.ryxm as oper_dr_name,
              ssys2.ybdm as oper_dr_code,
              mzys2.ryxm as anst_dr_name,
              mzys2.ybdm as anst_dr_code
          from gssldyb_zydjxx djxx
               inner join bagl_basy_ssxx ssxx on djxx.zyh = ssxx.zyh
               left join gyb_rybm ssys2 on ssys2.rybm = ssxx.ssys2
               left join gyb_rybm mzys2 on mzys2.rybm = ssxx.mzys2
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
          union all
          select
              ssxx.ssbm3 as setl_list_oprn_id,
              ssxx.ssbm3 as oprn_code,
              ssxx.ssmc3 as oprn_name,
              '' as main_oprn_flag,
              ssxx.ssrq3 as oprn_date,
              '' as anst_way,
              ssys3.ryxm as oper_dr_name,
              ssys3.ybdm as oper_dr_code,
              mzys3.ryxm as anst_dr_name,
              mzys3.ybdm as anst_dr_code
          from gssldyb_zydjxx djxx
               inner join bagl_basy_ssxx ssxx on djxx.zyh = ssxx.zyh
               left join gyb_rybm ssys3 on ssys3.rybm = ssxx.ssys3
               left join gyb_rybm mzys3 on mzys3.rybm = ssxx.mzys3
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
          union all
          select
              ssxx.ssbm4 as setl_list_oprn_id,
              ssxx.ssbm4 as oprn_code,
              ssxx.ssmc4 as oprn_name,
              '' as main_oprn_flag,
              ssxx.ssrq4 as oprn_date,
              '' as anst_way,
              ssys4.ryxm as oper_dr_name,
              ssys4.ybdm as oper_dr_code,
              mzys4.ryxm as anst_dr_name,
              mzys4.ybdm as anst_dr_code
          from gssldyb_zydjxx djxx
               inner join bagl_basy_ssxx ssxx on djxx.zyh = ssxx.zyh
               left join gyb_rybm ssys4 on ssys4.rybm = ssxx.ssys4
               left join gyb_rybm mzys4 on mzys4.rybm = ssxx.mzys4
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
          union all
          select
              ssxx.ssbm5 as setl_list_oprn_id,
              ssxx.ssbm5 as oprn_code,
              ssxx.ssmc5 as oprn_name,
              '' as main_oprn_flag,
              ssxx.ssrq5 as oprn_date,
              '' as anst_way,
              ssys5.ryxm as oper_dr_name,
              ssys5.ybdm as oper_dr_code,
              mzys5.ryxm as anst_dr_name,
              mzys5.ybdm as anst_dr_code
          from gssldyb_zydjxx djxx
               inner join bagl_basy_ssxx ssxx on djxx.zyh = ssxx.zyh
               left join gyb_rybm ssys5 on ssys5.rybm = ssxx.ssys5
               left join gyb_rybm mzys5 on mzys5.rybm = ssxx.mzys5
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
          union all
          select
              ssxx.ssbm6 as setl_list_oprn_id,
              ssxx.ssbm6 as oprn_code,
              ssxx.ssmc6 as oprn_name,
              '' as main_oprn_flag,
              ssxx.ssrq6 as oprn_date,
              '' as anst_way,
              ssys6.ryxm as oper_dr_name,
              ssys6.ybdm as oper_dr_code,
              mzys6.ryxm as anst_dr_name,
              mzys6.ybdm as anst_dr_code
          from gssldyb_zydjxx djxx
               inner join bagl_basy_ssxx ssxx on djxx.zyh = ssxx.zyh
               left join gyb_rybm ssys6 on ssys6.rybm = ssxx.ssys6
               left join gyb_rybm mzys6 on mzys6.rybm = ssxx.mzys6
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
          union all
          select
              ssxx.ssbm7 as setl_list_oprn_id,
              ssxx.ssbm7 as oprn_code,
              ssxx.ssmc7 as oprn_name,
              '' as main_oprn_flag,
              ssxx.ssrq7 as oprn_date,
              '' as anst_way,
              ssys7.ryxm as oper_dr_name,
              ssys7.ybdm as oper_dr_code,
              mzys7.ryxm as anst_dr_name,
              mzys7.ybdm as anst_dr_code
          from gssldyb_zydjxx djxx
               inner join bagl_basy_ssxx ssxx on djxx.zyh = ssxx.zyh
               left join gyb_rybm ssys7 on ssys7.rybm = ssxx.ssys7
               left join gyb_rybm mzys7 on mzys7.rybm = ssxx.mzys7
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
          union all
          select
              ssxx.ssbm8 as setl_list_oprn_id,
              ssxx.ssbm8 as oprn_code,
              ssxx.ssmc8 as oprn_name,
              '' as main_oprn_flag,
              ssxx.ssrq8 as oprn_date,
              '' as anst_way,
              ssys8.ryxm as oper_dr_name,
              ssys8.ybdm as oper_dr_code,
              mzys8.ryxm as anst_dr_name,
              mzys8.ybdm as anst_dr_code
          from gssldyb_zydjxx djxx
               inner join bagl_basy_ssxx ssxx on djxx.zyh = ssxx.zyh
               left join gyb_rybm ssys8 on ssys8.rybm = ssxx.ssys8
               left join gyb_rybm mzys8 on mzys8.rybm = ssxx.mzys8
          where djxx.zyh = #{zyh} and djxx.zfbz = '0'
      ) where setl_list_oprn_id is not null
    </select>
</mapper>
