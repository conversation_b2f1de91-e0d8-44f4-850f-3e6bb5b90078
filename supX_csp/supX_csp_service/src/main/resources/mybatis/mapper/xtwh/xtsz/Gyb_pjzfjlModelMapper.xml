<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.xtwh.xtsz.dao.New1Gyb_pjzfjlModelMapper">

    <!-- 分页查询 -->
    <select id="queryPjzfjl" resultType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjzfjlModel"
            parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjzfjlModel">
        select zfjl.*, rybm.ryxm zfryxm
        from GYB_PJZFJL zfjl
        left join gyb_rybm rybm on zfjl.zfry = rybm.rybm and zfjl.yljgbm = rybm.yljgbm
        <where>
            zfjl.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="zffs != null and zffs != ''">
                and zfjl.zffs = #{zffs,jdbcType=VARCHAR}
            </if>
            <if test="pjckdh != null and pjckdh != ''">
                and zfjl.pjckdh = #{pjckdh,jdbcType=VARCHAR}
            </if>
            <if test="zfry != null and zfry != ''">
                and zfjl.zfry = #{zfry,jdbcType=VARCHAR}
            </if>
            <if test="beginrq != null ">
                and zfjl.zfrq &gt;= #{beginrq,jdbcType=TIMESTAMP}
            </if>
            <if test="endrq != null ">
                and zfjl.zfrq &lt;= #{endrq,jdbcType=TIMESTAMP}
            </if>
            <if test="parm != null and parm != ''">
                and (zfjl.zfqsh like '%'||#{parm,jdbcType=VARCHAR}||'%' or zfjl.zfjsh like
                '%'||#{parm,jdbcType=VARCHAR}||'%')
            </if>
        </where>
        order by zfjl.zfrq desc
    </select>

    <!-- 保存 -->
    <insert id="savePjzf" parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjzfjlModel">
        insert INTO GYB_PJZFJL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="zfjlid != null">zfjlid,</if>
            <if test="pjckdh != null">pjckdh,</if>
            <if test="pjjlid != null">pjjlid,</if>
             <if test="pjlx != null">pjlx,</if>
            <if test="zfqsh != null">zfqsh,</if>
            <if test="zfjsh != null">zfjsh,</if>
            <if test="bzms != null">bzms,</if>
            <if test="zfry != null">zfry,</if>
            <if test="zfrq != null">zfrq,</if>
            <if test="zffs != null">zffs,</if>
            <if test="yljgbm != null">yljgbm,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="zfjlid != null">
                #{zfjlid, jdbcType=VARCHAR},
            </if>
            <if test="pjckdh != null">
                #{pjckdh, jdbcType=VARCHAR},
            </if>
            <if test="pjjlid != null">
                #{pjjlid, jdbcType=VARCHAR},
            </if>
            <if test="pjlx != null">
                #{pjlx, jdbcType=VARCHAR},
            </if>
            <if test="zfqsh != null">
                #{zfqsh, jdbcType=VARCHAR},
            </if>
            <if test="zfjsh != null">
                #{zfjsh, jdbcType=VARCHAR},
            </if>
            <if test="bzms != null">
                #{bzms, jdbcType=VARCHAR},
            </if>
            <if test="zfry != null">
                #{zfry, jdbcType=VARCHAR},
            </if>
            <if test="zfrq != null">
                #{zfrq, jdbcType=TIMESTAMP},
            </if>
            <if test="zffs != null">
                #{zffs, jdbcType=VARCHAR},
            </if>
            <if test="yljgbm != null">
                #{yljgbm, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjzfjlModel">
        update GYB_PJZFJL
        <set>
            <if test="dqsyh != null">
                dqsyh = #{dqsyh, jdbcType = VARCHAR},
            </if>
            <if test="pjzt != null">
                pjzt = #{pjzt, jdbcType = VARCHAR},
            </if>
        </set>
        where rkdh = #{rkdh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
    </update>
</mapper>
