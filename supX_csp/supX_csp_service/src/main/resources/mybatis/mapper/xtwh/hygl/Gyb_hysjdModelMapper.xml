<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.xtwh.hygl.dao.New1Gyb_hysjdModelMapper" >

  <select id="query" resultType="com.supx.csp.api.xtwh.hygl.pojo.Gyb_hysjdModel" parameterType="com.supx.csp.api.xtwh.hygl.pojo.Gyb_hysjdModel" >
    select * from GYB_HYSJD
    where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
      <if test="ysbm != null and ysbm != '' ">
    	and (ysbm = #{ysbm,jdbcType=VARCHAR})
    </if>
  	<if test="bcfabm != null and bcfabm != '' ">
    	and (bcfabm = #{bcfabm,jdbcType=VARCHAR})
    </if>
    <if test="ksbm != null and ksbm != '' ">
    	and (ksbm = #{ksbm,jdbcType=VARCHAR})
    </if>
    <if test="pbbid != null and pbbid != '' ">
      and (pbbid = #{pbbid,jdbcType=VARCHAR})
    </if>
    <if test="beginrq != null  ">
      and kssj &gt;= #{beginrq,jdbcType=TIMESTAMP}
    </if>
    <if test="endrq != null  ">
      and jssj &lt;= #{endrq,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="queryByPbbid" resultType="com.supx.csp.api.xtwh.hygl.pojo.Gyb_hysjdModel" parameterType="com.supx.csp.api.xtwh.hygl.pojo.Gyb_hysjdModel">
    select sjd.*, (select count(hyc.hyid) from ghb_yshyc hyc where hyc.sjdid=sjd.sjdid and hyc.ryghxh is not null) as yghsl from gyb_hysjd sjd
    where pbbid = #{pbbid,jdbcType=VARCHAR} and yljgbm=#{yljgbm,jdbcType=VARCHAR}
  </select>

  <delete id="delete" parameterType="com.supx.csp.api.xtwh.hygl.pojo.Gyb_hysjdModel" >
    delete from GYB_HYSJD
    where SJDID = #{sjdid,jdbcType=VARCHAR}
      and YLJGBM = #{yljgbm,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteList" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
        delete from GYB_HYSJD
        where SJDID = #{item.sjdid,jdbcType=VARCHAR} and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
    </foreach>
  </delete>

  <insert id="insert" parameterType="java.util.List" >
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    insert into GYB_HYSJD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="item.sjdid != null" >
        SJDID,
      </if>
      <if test="item.yljgbm != null" >
        YLJGBM,
      </if>
      <if test="item.ghlbbm != null" >
        GHLBBM,
      </if>
      <if test="item.bcfabm != null" >
        BCFABM,
      </if>
      <if test="item.kssj != null" >
        KSSJ,
      </if>
      <if test="item.jssj != null" >
        JSSJ,
      </if>
      <if test="item.ghfpgs != null" >
        GHFPGS,
      </if>
      <if test="item.ysbm != null" >
        YSBM,
      </if>
      <if test="item.ksbm != null" >
        KSBM,
      </if>
      <if test="item.pbbid != null" >
        PBBID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="item.sjdid != null" >
        #{item.sjdid,jdbcType=VARCHAR},
      </if>
      <if test="item.yljgbm != null" >
        #{item.yljgbm,jdbcType=VARCHAR},
      </if>
      <if test="item.ghlbbm != null" >
        #{item.ghlbbm,jdbcType=VARCHAR},
      </if>
      <if test="item.bcfabm != null" >
        #{item.bcfabm,jdbcType=VARCHAR},
      </if>
      <if test="item.kssj != null" >
        #{item.kssj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.jssj != null" >
        #{item.jssj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ghfpgs != null" >
        #{item.ghfpgs,jdbcType=DECIMAL},
      </if>
      <if test="item.ysbm != null" >
        #{item.ysbm,jdbcType=VARCHAR},
      </if>
      <if test="item.ksbm != null" >
        #{item.ksbm,jdbcType=VARCHAR},
      </if>
      <if test="item.pbbid != null" >
        #{item.pbbid,jdbcType=VARCHAR},
      </if>
    </trim>
    </foreach>
  </insert>

  <update id="update" parameterType="java.util.List" >
  <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    update GYB_HYSJD
    <set >
      <if test="item.ghlbbm != null" >
        GHLBBM = #{item.ghlbbm,jdbcType=VARCHAR},
      </if>
      <if test="item.bcfabm != null" >
        BCFABM = #{item.bcfabm,jdbcType=VARCHAR},
      </if>
      <if test="item.kssj != null" >
        KSSJ = #{item.kssj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.jssj != null" >
        JSSJ = #{item.jssj,jdbcType=TIMESTAMP},
      </if>
      <if test="item.ghfpgs != null" >
        GHFPGS = #{item.ghfpgs,jdbcType=DECIMAL},
      </if>
      <if test="item.ysbm != null" >
        YSBM = #{item.ysbm,jdbcType=VARCHAR},
      </if>
      <if test="item.ksbm != null" >
        KSBM = #{item.ksbm,jdbcType=VARCHAR},
      </if>
    </set>
    where SJDID = #{item.sjdid,jdbcType=VARCHAR}
      and YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
     </foreach>
  </update>

</mapper>
