<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.xtwh.xtsz.dao.New1Gyb_pjckdModelMapper">

    <!-- 分页查询 -->
    <select id="query" resultType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjckdModel"
            parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjckdModel">
        select ckd.*, rybm.ryxm czyxm, lyry.ryxm lyryxm, rkd.pch, rkd.czzch, pjsy.dqsyh, lyks.ksmc lyksmc, ywck.ywckmc, pjsy.pjzt
        from gyb_pjckd ckd
        left join gyb_rybm rybm on ckd.czybm = rybm.rybm and ckd.yljgbm = rybm.yljgbm
        left join gyb_rybm lyry on ckd.lyry = lyry.rybm and ckd.yljgbm = lyry.yljgbm
        left join gyb_ksbm lyks on ckd.lyks = lyks.ksbm and ckd.yljgbm = lyks.yljgbm
        left join gyb_pjsyjl pjsy on ckd.pjjlid = pjsy.pjjlid and ckd.yljgbm = pjsy.yljgbm
        left join gyb_pjrkd rkd on ckd.rkdh = rkd.rkdh and ckd.yljgbm = rkd.yljgbm
        left join gyb_ywck ywck on ywck.ywckbm=ckd.ywck and ywck.yljgbm=ckd.yljgbm
        <where>
            ckd.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="ckfs != null and ckfs != ''">
                and ckd.ckfs = #{ckfs,jdbcType=VARCHAR}
            </if>
            <if test="lyry != null and lyry != ''">
                and ckd.lyry = #{lyry,jdbcType=VARCHAR}
            </if>
            <if test="beginrq != null ">
                and ckd.cksj &gt;= #{beginrq,jdbcType=TIMESTAMP}
            </if>
            <if test="endrq != null ">
                and ckd.cksj &lt;= #{endrq,jdbcType=TIMESTAMP}
            </if>
            <if test="cxfs != null and cxfs == '1'.toString()">
                and (pjsy.dqsyh is null or pjsy.dqsyh='0')
            </if>
            <if test="cxfs != null and cxfs == '2'.toString()">
                and pjsy.dqsyh != '0' and pjsy.dqsyh &lt; ckd.jsh
            </if>
            <if test="cxfs != null and cxfs == '3'.toString()">
                and pjsy.dqsyh = ckd.jsh
            </if>
            <if test="cxfs != null and cxfs == '9'.toString()">
                and pjsy.pjzt = '3'
            </if>
            <if test="parm != null and parm != ''">
                and (ckd.qsh like '%'||#{parm,jdbcType=VARCHAR}||'%' or ckd.jsh like
                '%'||#{parm,jdbcType=VARCHAR}||'%')
            </if>
        </where>
        order by ckd.cksj desc
    </select>

    <select id="queryOne" resultType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjckdModel"
            parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjckdModel">
        select ckd.*, rkd.pch, rkd.czzch, pjsy.dqsyh
        from gyb_pjckd ckd
        left join gyb_pjsyjl pjsy on ckd.pjjlid = pjsy.pjjlid and ckd.yljgbm = pjsy.yljgbm
        left join gyb_pjrkd rkd on ckd.rkdh = rkd.rkdh and ckd.yljgbm = rkd.yljgbm
        where ckd.ckdh = #{ckdh,jdbcType=VARCHAR} and ckd.yljgbm = #{yljgbm,jdbcType=VARCHAR}
    </select>

    <!-- 分页查询 -->
    <select id="query2" resultType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjsyjlModel"
            parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjsyjlModel">
        SELECT pjsyh.*,ksbm.ksmc,rybm.ryxm czyxm,rybmTwo.ryxm ffrxm,ywck.ywckmc,rybmxh.ryxm xhryxm FROM GYB_PJSYJL pjsyh
        inner join gyb_ksbm ksbm on ksbm.ksbm=pjsyh.ksbm and ksbm.yljgbm=pjsyh.yljgbm
        inner join gyb_rybm rybm on rybm.rybm=pjsyh.czy and rybm.yljgbm=pjsyh.yljgbm
        inner join gyb_rybm rybmTwo on rybmTwo.rybm=pjsyh.ffr and rybmTwo.yljgbm=pjsyh.yljgbm
        left join gyb_rybm rybmxh on rybmxh.rybm=pjsyh.xhry and rybmxh.yljgbm=pjsyh.yljgbm
        left join gyb_ywck ywck on ywck.ywckbm=pjsyh.ywckbm and ywck.yljgbm=pjsyh.yljgbm
        <where>
            pjsyh.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="parm != null and parm != '' ">
                and (pjsyh.QSH like '%'||#{parm,jdbcType=VARCHAR}||'%' or pjsyh.JSH like
                '%'||#{parm,jdbcType=VARCHAR}||'%')
            </if>
            <if test='pjzt != null and pjzt == "1"'>
                and pjzt in ('0','1')
            </if>
            <if test='pjzt != null and pjzt == "2"'>
                and pjzt in ('2')
            </if>
            <if test='pjzt != null and pjzt == "3"'>
                and pjzt in ('0','1','2')
            </if>
            <if test='pjzt != null and pjzt == "4"'>
                and pjzt in ('3','4')
            </if>
        </where>
        ORDER BY ${sort} ${order}
    </select>

    <!-- 保存 -->
    <insert id="save" parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjckdModel">
        insert INTO Gyb_pjckd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ckdh != null">ckdh,</if>
            <if test="rkdh != null">rkdh,</if>
            <if test="pjjlid != null">pjjlid,</if>
            <if test="qsh != null">qsh,</if>
            <if test="jsh != null">jsh,</if>
            <if test="lyks != null">lyks,</if>
            <if test="lyry != null">lyry,</if>
            <if test="ywck != null">ywck,</if>
            <if test="pjlx != null">pjlx,</if>
            <if test="lpfs != null">lpfs,</if>
            <if test="ckfs != null">ckfs,</if>
            <if test="cksj != null">cksj,</if>
            <if test="czybm != null">czybm,</if>
            <if test="remark != null">remark,</if>
            <if test="yljgbm != null">yljgbm,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ckdh != null">
                #{ckdh, jdbcType=VARCHAR},
            </if>
            <if test="rkdh != null">
                #{rkdh, jdbcType=VARCHAR},
            </if>
            <if test="pjjlid != null">
                #{pjjlid, jdbcType=VARCHAR},
            </if>
            <if test="qsh != null">
                #{qsh, jdbcType=VARCHAR},
            </if>
            <if test="jsh != null">
                #{jsh, jdbcType=VARCHAR},
            </if>
            <if test="lyks != null">
                #{lyks, jdbcType=VARCHAR},
            </if>
            <if test="lyry != null">
                #{lyry, jdbcType=VARCHAR},
            </if>
            <if test="ywck != null">
                #{ywck, jdbcType=VARCHAR},
            </if>
            <if test="pjlx != null">
                #{pjlx, jdbcType=VARCHAR},
            </if>
            <if test="lpfs != null">
                #{lpfs, jdbcType=VARCHAR},
            </if>
            <if test="ckfs != null">
                #{ckfs, jdbcType=VARCHAR},
            </if>
            <if test="cksj != null">
                #{cksj, jdbcType=TIMESTAMP},
            </if>
            <if test="czybm != null">
                #{czybm, jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark, jdbcType=VARCHAR},
            </if>
            <if test="yljgbm != null">
                #{yljgbm, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjckdModel">
        update GYB_PJCKD
        <set>
            <if test="dqsyh != null">
                dqsyh = #{dqsyh, jdbcType = VARCHAR},
            </if>
            <if test="pjzt != null">
                pjzt = #{pjzt, jdbcType = VARCHAR},
            </if>
        </set>
        where rkdh = #{rkdh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
    </update>
</mapper>
