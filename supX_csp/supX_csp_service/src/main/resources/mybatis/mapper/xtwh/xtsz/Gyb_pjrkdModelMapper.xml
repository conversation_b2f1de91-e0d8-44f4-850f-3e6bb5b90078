<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.xtwh.xtsz.dao.New1Gyb_pjrkdModelMapper">

    <!-- 分页查询 -->
    <select id="query" resultType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjrkdModel"
            parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjrkdModel">
        select rkd.*, rybm.ryxm czyxm from gyb_pjrkd rkd
        left join gyb_rybm rybm on rkd.czybm = rybm.rybm and rkd.yljgbm = rybm.yljgbm
        <where>
            rkd.yljgbm = #{yljgbm, jdbcType=VARCHAR}
            <if test="pjzt != null and pjzt != ''">
                and rkd.pjzt = #{pjzt, jdbcType=VARCHAR}
            </if>
            <if test="fkydh != null and fkydh != ''">
                and rkd.fkydh = #{fkydh, jdbcType=VARCHAR}
            </if>
            <if test="rkfs != null and rkfs != ''">
                and rkd.rkfs = #{rkfs,jdbcType=VARCHAR}
            </if>
            <if test="beginrq != null ">
                and rkd.rksj &gt;= #{beginrq,jdbcType=TIMESTAMP}
            </if>
            <if test="endrq != null ">
                and rkd.rksj &lt;= #{endrq,jdbcType=TIMESTAMP}
            </if>
            <if test="cxfs != null and cxfs == '1'.toString()">
                and rkd.dqsyh is null and rkd.pjzt != '9'
            </if>
            <if test="cxfs != null and cxfs == '2'.toString()">
                and rkd.dqsyh is not null and rkd.dqsyh &lt; rkd.jsh
            </if>
            <if test="cxfs != null and cxfs == '3'.toString()">
                and rkd.dqsyh = rkd.jsh
            </if>
            <if test="cxfs != null and cxfs == '9'.toString()">
                and rkd.pjzt = '9'
            </if>
            <if test="parm != null and parm != ''">
                and (rkd.qsh like '%'||#{parm,jdbcType=VARCHAR}||'%' or rkd.jsh like
                '%'||#{parm,jdbcType=VARCHAR}||'%')
            </if>
        </where>
        order by rkd.rkdh desc
    </select>

    <select id="queryOne" resultType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjrkdModel"
            parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjrkdModel">
        select rkd.* from gyb_pjrkd rkd where rkd.rkdh = #{rkdh,jdbcType=VARCHAR} and rkd.yljgbm = #{yljgbm,jdbcType=VARCHAR}
    </select>

    <!-- 保存 -->
    <insert id="save" parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjrkdModel">
        INSERT INTO GYB_PJRKD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rkdh != null">rkdh,</if>
            <if test="pch != null">pch,</if>
            <if test="czzch != null">czzch,</if>
            <if test="qsh != null">qsh,</if>
            <if test="jsh != null">jsh,</if>
            <if test="dqsyh != null">dqsyh,</if>
            <if test="pjlx != null">pjlx,</if>
            <if test="czybm != null">czybm,</if>
            <if test="rksj != null">rksj,</if>
            <if test="rkfs != null">rkfs,</if>
            <if test="pjzt != null">pjzt,</if>
            <if test="fkydh != null">fkydh,</if>
            <if test="yljgbm != null">yljgbm,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rkdh != null">
                #{rkdh, jdbcType=VARCHAR},
            </if>
            <if test="pch != null">
                #{pch, jdbcType=VARCHAR},
            </if>
            <if test="czzch != null">
                #{czzch, jdbcType=VARCHAR},
            </if>
            <if test="qsh != null">
                #{qsh, jdbcType=VARCHAR},
            </if>
            <if test="jsh != null">
                #{jsh, jdbcType=VARCHAR},
            </if>
            <if test="dqsyh != null">
                #{dqsyh, jdbcType=VARCHAR},
            </if>
            <if test="pjlx != null">
                #{pjlx, jdbcType=VARCHAR},
            </if>
            <if test="czybm != null">
                #{czybm, jdbcType=VARCHAR},
            </if>
            <if test="rksj != null">
                #{rksj, jdbcType=TIMESTAMP},
            </if>
            <if test="rkfs != null">
                #{rkfs, jdbcType=VARCHAR},
            </if>
            <if test="pjzt != null">
                #{pjzt, jdbcType=VARCHAR},
            </if>
            <if test="fkydh != null">
                #{fkydh, jdbcType=VARCHAR},
            </if>
            <if test="yljgbm != null">
                #{yljgbm, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="com.supx.csp.api.xtwh.xtsz.pojo.Gyb_pjrkdModel">
        update GYB_PJRKD
        <set>
            <if test="dqsyh != null">
                dqsyh = #{dqsyh, jdbcType = VARCHAR},
            </if>
            <if test="pjzt != null">
                pjzt = #{pjzt, jdbcType = VARCHAR},
            </if>
            <if test="zfry != null">
                zfry = #{zfry, jdbcType = VARCHAR},
            </if>
            <if test="zfrq != null">
                zfrq = #{zfrq, jdbcType = TIMESTAMP},
            </if>
        </set>
        where rkdh = #{rkdh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
    </update>
</mapper>
