<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.xtwh.ksry.dao.Gyb_yljgModelMapper" >
  <resultMap id="BaseResultMap" type="com.supx.csp.api.xtwh.ksry.pojo.Gyb_yljgModel" >
    <id column="JGBM" property="jgbm" jdbcType="VARCHAR" />
    <result column="JGMC" property="jgmc" jdbcType="VARCHAR" />
    <result column="PYDM" property="pydm" jdbcType="VARCHAR" />
    <result column="XZQH" property="xzqh" jdbcType="VARCHAR" />
    <result column="XZJD" property="xzjd" jdbcType="VARCHAR" />
    <result column="CJWH" property="cjwh" jdbcType="VARCHAR" />
    <result column="DJZCLX" property="djzclx" jdbcType="VARCHAR" />
    <result column="JGLB" property="jglb" jdbcType="VARCHAR" />
    <result column="FLGLLX" property="flgllx" jdbcType="VARCHAR" />
    <result column="SZZBDW" property="szzbdw" jdbcType="VARCHAR" />
    <result column="ZFBLSGX" property="zfblsgx" jdbcType="VARCHAR" />
    <result column="JGJB" property="jgjb" jdbcType="VARCHAR" />
    <result column="JGDC" property="jgdc" jdbcType="VARCHAR" />
    <result column="XYFS" property="xyfs" jdbcType="VARCHAR" />
    <result column="SFFZJG" property="sffzjg" jdbcType="VARCHAR" />
    <result column="DZ" property="dz" jdbcType="VARCHAR" />
    <result column="LXDH" property="lxdh" jdbcType="VARCHAR" />
    <result column="YZBM" property="yzbm" jdbcType="VARCHAR" />
    <result column="DZYX" property="dzyx" jdbcType="VARCHAR" />
    <result column="WZYM" property="wzym" jdbcType="VARCHAR" />
    <result column="CLSJ" property="clsj" jdbcType="TIMESTAMP" />
    <result column="DWFZR" property="dwfzr" jdbcType="VARCHAR" />
    <result column="ZCZJ" property="zczj" jdbcType="DECIMAL" />
    <result column="JGMS" property="jgms" jdbcType="VARCHAR" />
    <result column="BZCWS" property="bzcws" jdbcType="DECIMAL" />
    <result column="SYCWS" property="sycws" jdbcType="DECIMAL" />
    <result column="BZRS" property="bzrs" jdbcType="DECIMAL" />
    <result column="FWJZMJ" property="fwjzmj" jdbcType="DECIMAL" />
    <result column="CZRKS" property="czrks" jdbcType="DECIMAL" />
    <result column="WYXLH" property="wyxlh" jdbcType="VARCHAR" />
    <result column="GLLB" property="gllb" jdbcType="VARCHAR" />
    <result column="SFGLYY" property="sfglyy" jdbcType="VARCHAR" />
    <result column="ZZJGDM" property="zzjgdm" jdbcType="VARCHAR" />
    <result column="TYBZ" property="tybz" jdbcType="VARCHAR" />
    <result column="DBUSER" property="dbuser" jdbcType="VARCHAR" />
    <result column="DBPASS" property="dbpass" jdbcType="VARCHAR" />
    <result column="ZCM" property="zcm" jdbcType="VARCHAR" />
    <result column="XZQHMC" property="xzqhmc" jdbcType="VARCHAR" />
    <result column="XZJDMC" property="xzjdmc" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 机构登陆查询 -->
  <resultMap id="BaseLogResultMap" type="com.supx.csp.api.xtwh.ksry.pojo.Gyb_yljgLogModel" >
  	<id column="sort" property="sort" jdbcType="VARCHAR" />
  	<collection property="yljgModels" column="sort" ofType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_yljgModel">
	  	<id column="JGBM" property="jgbm" jdbcType="VARCHAR" />
	    <result column="JGMC" property="jgmc" jdbcType="VARCHAR" />
	    <result column="PYDM" property="pydm" jdbcType="VARCHAR" />
	    <result column="sort" property="sort" jdbcType="VARCHAR" />
  	</collection>
  </resultMap>

  <sql id="Base_Column_List" >
    JGBM, JGMC, XZQH, XZJD, CJWH, DJZCLX, JGLB, FLGLLX, SZZBDW, ZFBLSGX, JGJB, JGDC,
    XYFS, SFFZJG, DZ, LXDH, YZBM, DZYX, WZYM, CLSJ, DWFZR, ZCZJ, JGMS, BZCWS, SYCWS,
    BZRS, FWJZMJ, CZRKS, WYXLH, GLLB, SFGLYY, ZZJGDM, TYBZ, DBUSER, DBPASS, ZCM,PYDM
  </sql>
  <!-- 查询单条记录 -->
  <select id="queryGyb_yljgOne4Mem" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from GYB_YLJG
    where JGBM = #{jgbm,jdbcType=VARCHAR}
  </select>

  <!-- 分页查询 -->
  <select id="querygyb_yljg4Mem" resultMap="BaseResultMap" parameterType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_yljgModel">
  	select yljg.*,xzqh.xzqhmc xzqhmc, xzjd.xzqhmc xzjdmc
	from gyb_yljg yljg
	left join gyb_xzqh xzqh on xzqh.xzqhbm=yljg.xzqh
	left join gyb_xzqh xzjd on xzjd.xzqhbm=yljg.xzjd
    <where>
    	(1=1)
    <if test="parm != null and parm != '' ">
    	and (yljg.JGMC like '%'||#{parm,jdbcType=VARCHAR}||'%' or yljg.JGBM like #{parm,jdbcType=VARCHAR}||'%')
    </if>
    <if test="tybz != null and tybz != '' ">
    	and (yljg.TYBZ = #{tybz,jdbcType=VARCHAR})
    </if>
    </where>
    ORDER BY  ${sort} ${order}
  </select>

  <!-- 批量删除 -->
  <delete id="deletebatch3Mem" parameterType="java.util.List" >
    delete from GYB_YLJG WHERE JGBM IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
        #{item.jgbm,jdbcType=VARCHAR}
    </foreach>
  </delete>

  <!-- 保存 -->
  <insert id="save3Mem" parameterType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_yljgModel">
  	MERGE INTO GYB_YLJG T1
  	USING
  	(
  	  SELECT #{jgbm,jdbcType=VARCHAR} JGBM, #{jgmc,jdbcType=VARCHAR} JGMC, #{xzqh,jdbcType=VARCHAR} XZQH,
      #{xzjd,jdbcType=VARCHAR} XZJD, #{cjwh,jdbcType=VARCHAR} CJWH, #{djzclx,jdbcType=VARCHAR} DJZCLX,
      #{jglb,jdbcType=VARCHAR} JGLB, #{flgllx,jdbcType=VARCHAR} FLGLLX, #{szzbdw,jdbcType=VARCHAR} SZZBDW,
      #{zfblsgx,jdbcType=VARCHAR} ZFBLSGX, #{jgjb,jdbcType=VARCHAR} JGJB, #{jgdc,jdbcType=VARCHAR} JGDC,
      #{xyfs,jdbcType=VARCHAR} XYFS, #{sffzjg,jdbcType=VARCHAR} SFFZJG, #{dz,jdbcType=VARCHAR} DZ, #{lxdh,jdbcType=VARCHAR} LXDH,
      #{yzbm,jdbcType=VARCHAR} YZBM, #{dzyx,jdbcType=VARCHAR} DZYX, #{wzym,jdbcType=VARCHAR} WZYM, #{clsj,jdbcType=TIMESTAMP} CLSJ,
      #{dwfzr,jdbcType=VARCHAR} DWFZR, #{zczj,jdbcType=DECIMAL} ZCZJ, #{jgms,jdbcType=VARCHAR} JGMS,
      #{bzcws,jdbcType=DECIMAL} BZCWS, #{sycws,jdbcType=DECIMAL} SYCWS, #{bzrs,jdbcType=DECIMAL} BZRS,
      #{fwjzmj,jdbcType=DECIMAL} FWJZMJ, #{czrks,jdbcType=DECIMAL} CZRKS, #{wyxlh,jdbcType=VARCHAR} WYXLH,
      #{gllb,jdbcType=VARCHAR} GLLB, #{sfglyy,jdbcType=VARCHAR} SFGLYY, #{zzjgdm,jdbcType=VARCHAR} ZZJGDM,
      #{tybz,jdbcType=VARCHAR} TYBZ, #{dbuser,jdbcType=VARCHAR} DBUSER, #{dbpass,jdbcType=VARCHAR} DBPASS,
      #{zcm,jdbcType=VARCHAR} ZCM
      FROM DUAL
  	) T2
  	ON (T1.JGBM = T2.JGBM)
  	WHEN MATCHED THEN
  	  	UPDATE
	    SET T1.JGMC = T2.JGMC,
	      T1.XZQH = T2.XZQH,
	      T1.XZJD = T2.XZJD,
	      T1.CJWH = T2.CJWH,
	      T1.DJZCLX = T2.DJZCLX,
	      T1.JGLB = T2.JGLB,
	      T1.FLGLLX = T2.FLGLLX,
	      T1.SZZBDW = T2.SZZBDW,
	      T1.ZFBLSGX = T2.ZFBLSGX,
	      T1.JGJB = T2.JGJB,
	      T1.JGDC = T2.JGDC,
    	  T1.XYFS = T2.XYFS,
    	  T1.SFFZJG = T2.SFFZJG,
    	  T1.DZ = T2.DZ,
    	  T1.LXDH = T2.LXDH,
    	  T1.YZBM = T2.YZBM,
    	  T1.DZYX = T2.DZYX,
    	  T1.WZYM = T2.WZYM,
    	  T1.CLSJ = T2.CLSJ,
    	  T1.DWFZR = T2.DWFZR,
    	  T1.ZCZJ = T2.ZCZJ,
    	  T1.JGMS = T2.JGMS,
    	  T1.BZCWS = T2.BZCWS,
    	  T1.SYCWS = T2.SYCWS,
    	  T1.BZRS = T2.BZRS,
    	  T1.FWJZMJ = T2.FWJZMJ,
    	  T1.CZRKS = T2.CZRKS,
    	  T1.WYXLH = T2.WYXLH,
    	  T1.GLLB = T2.GLLB,
    	  T1.SFGLYY = T2.SFGLYY,
    	  T1.ZZJGDM = T2.ZZJGDM,
    	  T1.TYBZ = T2.TYBZ,
    	  T1.DBUSER = T2.DBUSER,
    	  T1.DBPASS = T2.DBPASS,
    	  T1.ZCM = T2.ZCM

  	WHEN NOT MATCHED THEN
  		insert (JGBM, JGMC, XZQH, XZJD, CJWH, DJZCLX, JGLB, FLGLLX, SZZBDW, ZFBLSGX, JGJB, JGDC,
		    XYFS, SFFZJG, DZ, LXDH, YZBM, DZYX, WZYM, CLSJ, DWFZR, ZCZJ, JGMS, BZCWS, SYCWS,
		    BZRS, FWJZMJ, CZRKS, WYXLH, GLLB, SFGLYY, ZZJGDM, TYBZ, DBUSER, DBPASS, ZCM)
	    values (T2.JGBM, T2.JGMC, T2.XZQH, T2.XZJD, T2.CJWH, T2.DJZCLX, T2.JGLB, T2.FLGLLX, T2.SZZBDW, T2.ZFBLSGX, T2.JGJB, T2.JGDC,
		    T2.XYFS, T2.SFFZJG, T2.DZ, T2.LXDH, T2.YZBM, T2.DZYX, T2.WZYM, T2.CLSJ, T2.DWFZR, T2.ZCZJ, T2.JGMS, T2.BZCWS, T2.SYCWS,
		    T2.BZRS, T2.FWJZMJ, T2.CZRKS, T2.WYXLH, T2.GLLB, T2.SFGLYY, T2.ZZJGDM, T2.TYBZ, T2.DBUSER, T2.DBPASS, T2.ZCM)
  </insert>


  <!-- 登陆查询 -->
  <select id="querygyb_yljgLog" resultMap="BaseLogResultMap"  parameterType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_yljgModel">
      select JGBM, JGMC, PYDM, JGMS, substr(JGMC,1,2) as sort
      from gyb_yljg yljg
      where tybz = '0'
      <if test="parm != null and parm != '' ">
          and (
          yljg.JGMC like '%'||#{parm}||'%'
          or yljg.JGBM like #{parm}||'%'
          or upper(yljg.PYDM) like '%'||upper(#{parm})||'%'
          )
      </if>
      order by sort asc
  </select>
</mapper>
