<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.xtwh.ksry.dao.Gyb_dbfaModelMapper" >
  <resultMap id="BaseResultMap" type="com.supx.csp.api.xtwh.ksry.pojo.Gyb_dbfaModel" >
    <id column="DBFABM" property="dbfabm" jdbcType="VARCHAR" />
    <result column="DBFAMC" property="dbfamc" jdbcType="VARCHAR" />
    <result column="PYDM" property="pydm" jdbcType="VARCHAR" />
    <result column="TYBZ" property="tybz" jdbcType="VARCHAR" />
    <result column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
    <result column="SYFW" property="syfw" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    DBFABM, DBFAMC, PYDM, TYBZ,YLJGBM,SYFW
  </sql>

  <!-- 查询单条信息 -->
  <select id="queryGyb_dbfaOne4Mem" resultMap="BaseResultMap" parameterType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_dbfaModel" >
    select
    <include refid="Base_Column_List" />
    from GYB_DBFA
    where DBFABM = #{dbfabm,jdbcType=VARCHAR} and YLJGBM = #{yljgbm,jdbcType=VARCHAR}
  </select>

  <!-- 分页查询 -->
  <select id="querygyb_dbfa4Mem" resultMap="BaseResultMap" parameterType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_dbfaModel">
  	SELECT
    <include refid="Base_Column_List" />
    FROM GYB_DBFA
    <where>
    	yljgbm = #{yljgbm,jdbcType=VARCHAR}
    <if test="parm != null and parm != '' ">
    	and (DBFAMC like '%'||#{parm,jdbcType=VARCHAR}||'%' or  lower(PYDM) like
    	'%'||lower(#{parm,jdbcType=VARCHAR})||'%' or DBFABM like #{parm,jdbcType=VARCHAR}||'%')
    </if>
     <if test="tybz != null and tybz != '' ">
    	and (TYBZ = #{tybz,jdbcType=VARCHAR})
    </if>
    <if test="dbfabm != null and dbfabm != '' ">
    	and (DBFABM = #{dbfabm,jdbcType=VARCHAR})
    </if>
    <if test="syfw !=null and syfw !='' and syfw !='null'">
    	and (SYFW = #{syfw,jdbcType=VARCHAR})
    </if>
    </where>
    ORDER BY  ${sort} ${order}
  </select>

  <!-- 批量删除 -->
  <delete id="deletebatch3Mem" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    delete from GYB_DBFA WHERE YLJGBM = #{item.yljgbm,jdbcType=VARCHAR} and DBFABM=
        #{item.dbfabm,jdbcType=VARCHAR}
    </foreach>
  </delete>

  <!-- 保存 -->
  <insert id="save3Mem" parameterType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_dbfaModel">
  	MERGE INTO GYB_DBFA T1
  	USING
  	(
  	  SELECT #{dbfabm,jdbcType=VARCHAR} DBFABM, #{dbfamc,jdbcType=VARCHAR} DBFAMC,
  	  #{pydm,jdbcType=VARCHAR} PYDM,#{tybz,jdbcType=VARCHAR} TYBZ,
  	  #{yljgbm,jdbcType=VARCHAR} YLJGBM, #{syfw,jdbcType=VARCHAR} SYFW
      FROM DUAL
  	) T2
  	ON (T1.DBFABM = T2.DBFABM AND T1.YLJGBM = T2.YLJGBM)
  	WHEN MATCHED THEN
  	  	UPDATE
	    SET T1.DBFAMC = T2.DBFAMC,
	      T1.PYDM = T2.PYDM,
	      T1.TYBZ = T2.TYBZ,
	      T1.SYFW=T2.SYFW

  	WHEN NOT MATCHED THEN
  		insert (DBFABM, DBFAMC, PYDM,
	      TYBZ,YLJGBM,SYFW)
	    values (T2.DBFABM, T2.DBFAMC, T2.PYDM,
	      T2.TYBZ, T2.YLJGBM,T2.SYFW)
  </insert>



</mapper>
