<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supx.csp.bagl.sygl.dao.New1Bagl_basy_jbxxModelMapper">
    <!-- 查询所有在库状态的病案信息 -->
    <select id="queryZkba" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel"
            resultType="com.supx.csp.api.bagl.sygl.pojo.BaglSydjListModel">
        select bajbxx.brid,bajbxx.zyh,rydj.bah,jbxx.brxm,jbxx.brxb,jbxx.sfzjhm,jbxx.dwdz,jbxx.sjhm,
        jbxx.csrq,ksbm.ksmc ryksmc,cyks.ksmc cyksmc,rydj.nl,rydj.nldw,rydj.ryrq,
        bazdxx.ryzdmc_bz ryzdmcBz,bazdxx.cyzyzdmc_bz cyzyzdmcBz,bajbxx.jsbz,bajbxx.jsrq,
        bajbxx.gdbz,bajbxx.gdrq from bagl_basy_jbxx bajbxx
        inner join bagl_basy_zdxx bazdxx on bazdxx.zyh=bajbxx.zyh and bazdxx.yljgbm=bajbxx.yljgbm
        inner join zyb_rydj rydj on rydj.zyh=bajbxx.zyh and rydj.yljgbm=bajbxx.yljgbm
        inner join gyb_brjbxx jbxx on jbxx.brid=bajbxx.brid and jbxx.yljgbm=bajbxx.yljgbm
        left join gyb_ksbm ksbm on ksbm.ksbm=bazdxx.ryks and ksbm.yljgbm=bazdxx.yljgbm
        left join gyb_ksbm cyks on cyks.ksbm=bazdxx.cyks and cyks.yljgbm=bazdxx.yljgbm
        left join bagl_basy_jydj jydj on jydj.zyh=bajbxx.zyh and jydj.yljgbm=bajbxx.yljgbm
        where bajbxx.shbz ='1' and bajbxx.yljgbm =#{yljgbm,jdbcType=VARCHAR} and bajbxx.zyh not in (select zyh from
        bagl_basy_jydj where yljgbm=#{yljgbm,jdbcType=VARCHAR} and ghbz='0')
        <if test="gdbz != null and gdbz != ''">
            and bajbxx.gdbz = #{gdbz,jdbcType=VARCHAR}
        </if>
        <if test="beginrq != null">
            and (bajbxx.shrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
        </if>
        <if test="endrq != null">
            and (bajbxx.shrq &lt;= #{endrq,jdbcType=TIMESTAMP})
        </if>
    </select>


    <!-- 针对审核与未审核列表展示的 （现在接收归档的列表都要在这里查询）-->
    <select id="queryIsSh" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel"
            resultType="com.supx.csp.api.bagl.sygl.pojo.BaglSydjListModel">
        select bajbxx.zyh,rydj.bah,jbxx.brxm,jbxx.brxb,jbxx.sfzjhm,jbxx.dwdz,jbxx.sjhm,
        jbxx.csrq,ksbm.ksmc ryksmc,cyks.ksmc cyksmc,rydj.nl,rydj.nldw,rydj.ryrq,
        bazdxx.ryzdmc_bz ryzdmcBz,bazdxx.cyzyzdmc_bz cyzyzdmcBz,rybm.ryxm zyysxm,bajbxx.jsbz,bajbxx.jsrq,
        jsry.ryxm jsryxm,bajbxx.gdbz,bajbxx.gdrq,gdry.ryxm gdryxm
        from bagl_basy_jbxx bajbxx
        inner join bagl_basy_zdxx bazdxx on bazdxx.zyh=bajbxx.zyh and bazdxx.yljgbm=bajbxx.yljgbm
        inner join zyb_rydj rydj on rydj.zyh=bajbxx.zyh and rydj.yljgbm=bajbxx.yljgbm
        inner join gyb_brjbxx jbxx on jbxx.brid=bajbxx.brid and jbxx.yljgbm=bajbxx.yljgbm
        left join gyb_ksbm ksbm on ksbm.ksbm=bazdxx.ryks and ksbm.yljgbm=bazdxx.yljgbm
        left join gyb_ksbm cyks on cyks.ksbm=bazdxx.cyks and cyks.yljgbm=bazdxx.yljgbm
        left join gyb_rybm rybm on rybm.rybm=bazdxx.zyys and rybm.yljgbm=bazdxx.yljgbm
        left join gyb_rybm jsry on jsry.rybm =bajbxx.jsry and jsry.yljgbm =bajbxx.yljgbm
        left join gyb_rybm gdry on gdry.rybm =bajbxx.gdry and gdry.yljgbm =bajbxx.yljgbm
        <where>
            rydj.ifzf='0' and bajbxx.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="shbz != null and shbz !='' ">
                and (bajbxx.shbz = #{shbz,jdbcType=VARCHAR})
            </if>
            <if test="gdbz != null and gdbz !='' ">
                and (bajbxx.gdbz = #{gdbz,jdbcType=VARCHAR})
            </if>
            <if test="jsbz != null and jsbz !='' ">
                and (bajbxx.jsbz = #{jsbz,jdbcType=VARCHAR})
            </if>
            <if test="zyh != null and zyh !='' ">
                and (bajbxx.zyh = #{zyh,jdbcType=VARCHAR})
            </if>
            <if test="beginrq != null">
                and (bajbxx.jsrq &gt;= #{beginrq,jdbcType=TIMESTAMP} or bajbxx.gdrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="endrq != null">
                and (bajbxx.jsrq &lt;= #{endrq,jdbcType=TIMESTAMP} or bajbxx.gdrq &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
        </where>
    </select>

    <!-- 添加处理病案首页各表 -->
    <insert id="insertDydj" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
        begin
            insert into bagl_basy_jbxx(zyh, yljgbm, brid, xsecstz, xserytz)
            select rydj.zyh, rydj.yljgbm, rydj.brid, yexx.tz, yexx.tz
            from zyb_rydj rydj
                     left join hsz_yexx yexx on rydj.zyh = yexx.zyh and rydj.yljgbm = yexx.yljgbm
            where rownum = 1
              and rydj.zyh = #{zyh,jdbcType=VARCHAR}
              and rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
              and not exists
                (select 1 from bagl_basy_jbxx bajbxx where rydj.zyh = bajbxx.zyh and rydj.yljgbm = bajbxx.yljgbm);

            insert into bagl_basy_jbxx_yexx(zyh, yljgbm, yebh, xsecstz, xserytz)
            select yexx.zyh, yexx.yljgbm, yexx.yebh, yexx.tz, yexx.tz
            from hsz_yexx yexx
            where yexx.zyh = #{zyh,jdbcType=VARCHAR}
              and yexx.yljgbm = #{yljgbm,jdbcType=VARCHAR}
              and not exists
                (select 1 from bagl_basy_jbxx_yexx bayexx where yexx.zyh = bayexx.zyh and yexx.yljgbm = bayexx.yljgbm);

            insert into bagl_basy_zdxx(zyh, yljgbm, ryks, ryrq, ryqk, rytj, zyys, ryzdbm, ryzdmc)
            select zyh,
                   yljgbm,
                   ryks,
                   ryrq,
                   ryqk,
                   rytj,
                   zyys,
                   ryzdbm,
                   ryzdmc
            from zyb_rydj
            where zyb_rydj.zyh = #{zyh,jdbcType=VARCHAR}
              and zyb_rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
              and not exists
                (select 1
                 from bagl_basy_zdxx
                 where zyb_rydj.zyh = bagl_basy_zdxx.zyh
                   and zyb_rydj.yljgbm = bagl_basy_zdxx.yljgbm);

            insert into bagl_basy_ssxx(zyh, yljgbm)
            select zyh, yljgbm
            from zyb_rydj
            where zyb_rydj.zyh = #{zyh,jdbcType=VARCHAR}
              and zyb_rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
              and not exists
                (select 1
                 from bagl_basy_ssxx
                 where zyb_rydj.zyh = bagl_basy_ssxx.zyh
                   and zyb_rydj.yljgbm = bagl_basy_ssxx.yljgbm);

            insert into bagl_basy_fyxx(zyh, yljgbm)
            select zyh, yljgbm
            from zyb_rydj
            where zyb_rydj.zyh = #{zyh,jdbcType=VARCHAR}
              and zyb_rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
              and not exists
                (select 1
                 from bagl_basy_fyxx
                 where zyb_rydj.zyh = bagl_basy_fyxx.zyh
                   and zyb_rydj.yljgbm = bagl_basy_fyxx.yljgbm);
        end;
    </insert>

    <!-- 根据住院号或者病案号查询基本信息 -->
    <select id="selectByZyhOrBah" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel"
            resultType="com.supx.csp.api.bagl.sygl.pojo.Bagl_jbxx_MsgModel">
        select rydj.brfb,bajbxx.yl,bajbxx.tl,bajbxx.zyh,zybm.zymc ylfkfsmc,bajbxx.bryl,bajbxx.brylday,bajbxx.xsecstz,
        bajbxx.xserytz,bajbxx.jsbz,bajbxx.jsrq,bajbxx.jsry,bajbxx.shbz,bajbxx.shrq,
        bajbxx.shry,bajbxx.gdbz,bajbxx.gdrq,bajbxx.gdry,bajbxx.nls,
        brjbxx.jzd_sheng jzdsheng,brjbxx.jzd_shi jzdshi,brjbxx.jzd_xian jzdxian,brjbxx.jzd_xiang jzdxiang,
        brjbxx.jzd_jwh jzdjwh,brjbxx.jzd_cun jzdcun,brjbxx.jzd_nong jzdnong,brjbxx.jzd_lh jzdlh,brjbxx.jzd_mph jzdmph,
        brjbxx.hjd_xzqh hjdxzqh,brjbxx.hjd_sheng hjdsheng,brjbxx.hjd_shi hjdshi,brjbxx.hjd_xian hjdxian,
        brjbxx.hjd_xiang hjdxiang,brjbxx.hjd_jwh hjdjwh,brjbxx.hjd_cun hjdcun,brjbxx.hjd_nong hjdnong,brjbxx.hjd_lh
        hjdlh,
        brjbxx.hjd_mph hjdmph,brjbxx.lxdh_lbdm lxdhlbdm,brjbxx.jzd_xzqhmc jzdxzqhmc,brjbxx.jzd_shengmc jzdshengmc,
        brjbxx.jzd_shimc jzdshimc,brjbxx.jzd_xianmc jzdxianmc,brjbxx.jzd_xiangmc jzdxiangmc,brjbxx.jzd_jwhmc jzdjwhmc,
        brjbxx.hjd_xzqhmc hjdxzqhmc,brjbxx.hjd_shengmc hjdshengmc,brjbxx.hjd_shimc hjdshimc,brjbxx.hjd_xianmc hjdxianmc,
        brjbxx.hjd_xiangmc hjdxiangmc,brjbxx.hjd_jwhmc hjdjwhmc,brjbxx.jzd_email jzdemail,brjbxx.jzd_qq
        jzdqq,brjbxx.csd_sheng csdsheng,
        brjbxx.csd_shi csdshi,brjbxx.csd_xian csdxian,brjbxx.csd_shengmc csdshengmc,brjbxx.csd_shimc
        csdshimc,brjbxx.csd_xianmc csdxianmc,
        brjbxx.lxr_sheng lxrsheng,brjbxx.lxr_shi lxrshi,brjbxx.lxr_xian lxrxian,brjbxx.lxr_shengmc
        lxrshengmc,brjbxx.lxr_shimc lxrshimc,
        brjbxx.lxr_xianmc lxrxianmc,brjbxx.dw_sheng dwsheng,brjbxx.dw_shi dwshi,brjbxx.dw_xian dwxian,brjbxx.dw_shengmc
        dwshengmc,
        brjbxx.dw_shimc dwshimc,brjbxx.dw_xianmc dwxianmc,brjbxx.jg_sheng jgsheng,brjbxx.jg_shi jgshi,brjbxx.jg_xian
        jgxian,brjbxx.jg_shengmc jgshengmc,
        brjbxx.jg_shimc jgshimc,brjbxx.jg_xianmc jgxianmc,brjbxx.BRID,brjbxx.BRXM,brjbxx.BRXB,brjbxx.CSRQ,
        brjbxx.BRGJ,brjbxx.BRMZ,brjbxx.SFZJLX,brjbxx.SFZJHM,brjbxx.HYZK,brjbxx.ZYBM,brjbxx.HZLX,brjbxx.CSD,brjbxx.SJHM,brjbxx.GZDW,brjbxx.DWDZ,
        brjbxx.DWYB,brjbxx.JZDMC,brjbxx.HKDZ,brjbxx.HKDYB,brjbxx.LXRXM,brjbxx.LXRGX,brjbxx.LXRDZ,brjbxx.LXRDW,brjbxx.LXRYB,brjbxx.LXRDH,brjbxx.DJRQ,brjbxx.DJRY,
        brjbxx.PYDM,brjbxx.SFZJLXMC,brjbxx.HYZKMC,brjbxx.ZYBMMC,brjbxx.JKDABH,brjbxx.BRGJMC,brjbxx.BRMZMC,brjbxx.SG,brjbxx.TZ,brjbxx.ZHLJJE,brjbxx.ZHZFJE,
        brjbxx.JGBM,brjbxx.JGMC,brjbxx.YLJGBM,brjbxx.JZDYB,brjbxx.YBKH,brjbxx.DWDH,brjbxx.JZXM,brjbxx.WXCARDNO,brjbxx.SYZK,brjbxx.SYZKMC,brjbxx.WHCD,brjbxx.WHCDMC,
        brjbxx.DAH,brjbxx.LXRZJH,brjbxx.SFZBF,brjbxx.DRBZ,brjbxx.JZDMC1,brjbxx.YYPTFBBM,brjbxx.GZYYPTBDRQ,brjbxx.GZYYPTJK,brjbxx.JZDXZQH,
        brjbxx.WXWXCARDNO,brjbxx.WBJM,brjbxx.SFMBBR,rydj.zycs,rydj.ylklx,rydj.bah,zybm.zymc,lxrgx.lxrgxmc,rydj.nl,rydj.nldw,
        bj.tyshxydm,bj.jgbm as gjgbm,bajbxx.bcbz,bajbxx.dwfzr,bajbxx.tjfzr,bajbxx.tbr,bajbxx.lxdh,bajbxx.sj,
        case when bajbxx.ylfkfs is null and zydjxx.xzlx='310' then '001'
        when bajbxx.ylfkfs is null and zydjxx.xzlx='390' then '002'
        when bajbxx.ylfkfs is null and rydj.brfb='04' then '006'
        when bajbxx.ylfkfs is not null then bajbxx.ylfkfs
        else '007'
        end ylfkfs,zyjsxx.jzid,zyjsxx.rybh,zyjsxx.jsid,to_char(rydj.cyrq,'yyyy-mm-dd hh24:mi:ss')
        jssj,jsjl.jsjlid,jsjl.fphm,zydjxx.cbdqybqh

        from gyb_brjbxx brjbxx
        inner join zyb_rydj rydj on rydj.brid=brjbxx.brid and rydj.yljgbm=brjbxx.yljgbm
        inner join bagl_basy_jbxx bajbxx on bajbxx.zyh=rydj.zyh and bajbxx.yljgbm=rydj.yljgbm
        left join zyb_jsjl jsjl on rydj.zyh = jsjl.zyh and rydj.yljgbm=jsjl.yljgbm and jsjl.jszt='0'
        left join zyz_zybm zybm on zybm.zybm=bajbxx.ylfkfs and zybm.zylb='07'
        left join gssldyb_zyjsxx zyjsxx on rydj.zyh = zyjsxx.zyh and zyjsxx.zfbz='0'
        left join gssldyb_zydjxx zydjxx on rydj.zyh = zydjxx.zyh and zydjxx.zfbz='0'
        left join gyb_lxrgx lxrgx on lxrgx.lxrgxbm=brjbxx.lxrgx,
        ba_jgbm bj
        <where>
            rydj.ifzf='0' and rydj.yljgbm = #{yljgbm,jdbcType=VARCHAR}
            <if test="zyh != null and zyh !='' ">
                and (rydj.zyh = #{zyh,jdbcType=VARCHAR})
            </if>
            <if test="bah != null and bah !='' ">
                and (rydj.bah = #{bah,jdbcType=VARCHAR})
            </if>
            <if test="shbz != null and shbz !='' ">
                and (bajbxx.shbz = #{shbz,jdbcType=VARCHAR})
            </if>
        </where>
    </select>

    <!-- 保存(暂时不用这个) -->
    <insert id="save" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
        MERGE INTO BAGL_BASY_JBXX T1
        USING
            (SELECT #{zyh,jdbcType=VARCHAR}     ZYH,
                    #{yljgbm,jdbcType=VARCHAR}  YLJGBM,
                    #{ylfkfs,jdbcType=VARCHAR}  YLFKFS,
                    #{bryl,jdbcType=DECIMAL}    BRYL,
                    #{brylday,jdbcType=DECIMAL} BRYLDAY,
                    #{xsecstz,jdbcType=DECIMAL} XSECSTZ,
                    #{xserytz,jdbcType=DECIMAL} XSERYTZ,
                    #{brid,jdbcType=VARCHAR}    BRID,
                    #{jsbz,jdbcType=VARCHAR}    JSBZ,
                    #{jsrq,jdbcType=TIMESTAMP}  JSRQ,
                    #{jsry,jdbcType=VARCHAR}    JSRQ,
                    #{shbz,jdbcType=VARCHAR}    SHBZ,
                    #{shrq,jdbcType=TIMESTAMP}  SHRQ,
                    #{shry,jdbcType=VARCHAR}    SHRQ,
                    #{gdbz,jdbcType=VARCHAR}    GDBZ,
                    #{gdrq,jdbcType=TIMESTAMP}  GDRQ,
                    #{gdry,jdbcType=VARCHAR}    GDRY,
                    #{nls,jdbcType=VARCHAR}     NLS
             FROM DUAL) T2
        ON (T1.ZYH = T2.ZYH AND T1.YLJGBM = T2.YLJGBM)
        WHEN MATCHED THEN
            UPDATE
            SET T1.YLFKFS=T2.YLFKFS,
                T1.BRYL=T2.BRYL,
                T1.BRYLDAY=T2.BRYLDAY,
                T1.XSECSTZ=T2.XSECSTZ,
                T1.XSERYTZ=T2.XSERYTZ,
                T1.BRID=T2.BRID,
                T1.JSBZ=T2.JSBZ,
                T1.JSRQ=T2.JSRQ,
                T1.JSRY=T2.JSRY,
                T1.SHBZ=T2.SHBZ,
                T1.SHRQ=T2.SHRQ,
                T1.SHRY=T2.SHRY,
                T1.GDBZ=T2.GDBZ,
                T1.GDRQ=T2.GDRQ,
                T1.GDRY=T2.GDRY,
                T1.NLS=T2.NLS
        WHEN NOT MATCHED THEN
            insert (ZYH, YLJGBM, YLFKFS, BRYL, BRYLDAY, XSECSTZ, XSERYTZ, BRID, JSBZ, JSRQ, JSRY, SHBZ,
                    SHRQ, SHRY, GDBZ, GDRQ, GDRY, NLS)
            values (T2.ZYH, T2.YLJGBM, T2.YLFKFS, T2.BRYL, T2.BRYLDAY, T2.XSECSTZ, T2.XSERYTZ,
                    T2.BRID, T2.JSBZ, T2.JSRQ, T2.JSRY, T2.SHBZ,
                    T2.SHRQ, T2.SHRY, T2.GDBZ, T2.GDRQ, T2.GDRY, T2.NLS)
    </insert>

    <!-- 保存病案首页基本信息 -->
    <insert id="insert" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
        insert into BAGL_BASY_JBXX
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="zyh != null">
                ZYH,
            </if>
            <if test="yljgbm != null">
                YLJGBM,
            </if>
            <if test="ylfkfs != null">
                YLFKFS,
            </if>
            <if test="bryl != null">
                BRYL,
            </if>
            <if test="brylday != null">
                BRYLDAY,
            </if>
            <if test="xsecstz != null">
                XSECSTZ,
            </if>
            <if test="xserytz != null">
                XSERYTZ,
            </if>
            <if test="brid != null">
                BRID,
            </if>
            <if test="jsbz != null">
                JSBZ,
            </if>
            <if test="jsrq != null">
                JSRQ,
            </if>
            <if test="jsry != null">
                JSRY,
            </if>
            <if test="shbz != null">
                SHBZ,
            </if>
            <if test="shrq != null">
                SHRQ,
            </if>
            <if test="shry != null">
                SHRY,
            </if>
            <if test="gdbz != null">
                GDBZ,
            </if>
            <if test="gdrq != null">
                GDRQ,
            </if>
            <if test="gdry != null">
                GDRY,
            </if>
            <if test="nls != null">
                NLS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="zyh != null">
                #{zyh,jdbcType=VARCHAR},
            </if>
            <if test="yljgbm != null">
                #{yljgbm,jdbcType=VARCHAR},
            </if>
            <if test="ylfkfs != null">
                #{ylfkfs,jdbcType=VARCHAR},
            </if>
            <if test="bryl != null">
                #{bryl,jdbcType=DECIMAL},
            </if>
            <if test="brylday != null">
                #{brylday,jdbcType=DECIMAL},
            </if>
            <if test="xsecstz != null">
                #{xsecstz,jdbcType=DECIMAL},
            </if>
            <if test="xserytz != null">
                #{xserytz,jdbcType=DECIMAL},
            </if>
            <if test="brid != null">
                #{brid,jdbcType=VARCHAR},
            </if>
            <if test="jsbz != null">
                #{jsbz,jdbcType=VARCHAR},
            </if>
            <if test="jsrq != null">
                #{jsrq,jdbcType=TIMESTAMP},
            </if>
            <if test="jsry != null">
                #{jsry,jdbcType=VARCHAR},
            </if>
            <if test="shbz != null">
                #{shbz,jdbcType=VARCHAR},
            </if>
            <if test="shrq != null">
                #{shrq,jdbcType=TIMESTAMP},
            </if>
            <if test="shry != null">
                #{shry,jdbcType=VARCHAR},
            </if>
            <if test="gdbz != null">
                #{gdbz,jdbcType=VARCHAR},
            </if>
            <if test="gdrq != null">
                #{gdrq,jdbcType=TIMESTAMP},
            </if>
            <if test="gdry != null">
                #{gdry,jdbcType=VARCHAR},
            </if>
            <if test="nls != null">
                #{nls,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 修改病案首页基本信息 -->
    <update id="update" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
        update BAGL_BASY_JBXX
        <set>
            bcbz=1,
            <if test="ylfkfs != null">
                YLFKFS = #{ylfkfs,jdbcType=VARCHAR},
            </if>
            <if test="bryl != null">
                BRYL = #{bryl,jdbcType=DECIMAL},
            </if>
            <if test="brylday != null">
                BRYLDAY = #{brylday,jdbcType=DECIMAL},
            </if>
            <if test="xsecstz != 999999">
                XSECSTZ = #{xsecstz,jdbcType=DECIMAL},
            </if>
            <if test="xserytz != 999999">
                XSERYTZ = #{xserytz,jdbcType=DECIMAL},
            </if>
            <if test="brid != null">
                BRID = #{brid,jdbcType=VARCHAR},
            </if>
            <if test="jsbz != null">
                JSBZ = #{jsbz,jdbcType=VARCHAR},
            </if>
            <if test="jsrq != null">
                JSRQ = #{jsrq,jdbcType=TIMESTAMP},
            </if>
            <if test="jsry != null">
                JSRY = #{jsry,jdbcType=VARCHAR},
            </if>
            <if test="shbz != null">
                SHBZ = #{shbz,jdbcType=VARCHAR},
            </if>
            <if test="shrq != null">
                SHRQ = #{shrq,jdbcType=TIMESTAMP},
            </if>
            <if test="shry != null">
                SHRY = #{shry,jdbcType=VARCHAR},
            </if>
            <if test="gdbz != null">
                GDBZ = #{gdbz,jdbcType=VARCHAR},
            </if>
            <if test="gdrq != null">
                GDRQ = #{gdrq,jdbcType=TIMESTAMP},
            </if>
            <if test="gdry != null">
                GDRY = #{gdry,jdbcType=VARCHAR},
            </if>
            <if test="nls != null">
                NLS = #{nls,jdbcType=VARCHAR},
            </if>
            <if test="yl != null and yl != 9999">
                yl = #{yl,jdbcType=VARCHAR},
            </if>
            <if test="tl != null">
                tl = #{tl,jdbcType=VARCHAR},
            </if>
            <if test="dwfzr != null">
                DWFZR = #{dwfzr,jdbcType=VARCHAR},
            </if>
            <if test="tjfzr != null">
                TJFZR = #{tjfzr,jdbcType=VARCHAR},
            </if>
            <if test="tbr != null">
                TBR = #{tbr,jdbcType=VARCHAR},
            </if>
            <if test="lxdh != null">
                LXDH = #{lxdh,jdbcType=VARCHAR},
            </if>
            <if test="sj != null">
                SJ = #{sj,jdbcType=VARCHAR},
            </if>

        </set>
        where ZYH = #{zyh,jdbcType=VARCHAR}
        and YLJGBM = #{yljgbm,jdbcType=VARCHAR}
    </update>

    <!-- 针对取消审核的修改 -->
    <update id="updateQxsh" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
        update BAGL_BASY_JBXX
        <set>
            SHBZ = '0',
            SHRQ = null,
            SHRY = null
        </set>
        where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        and ZYH = #{zyh,jdbcType=VARCHAR}
    </update>


    <!-- 针对取消接收的修改 -->
    <update id="updateQxjs" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
        update BAGL_BASY_JBXX
        <set>
            JSBZ = '0',
            JSRQ = null,
            JSRY = null
        </set>
        where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        and ZYH = #{zyh,jdbcType=VARCHAR}
    </update>

    <!-- 针对取消归档的修改 -->
    <update id="updateQxgd" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
        update BAGL_BASY_JBXX
        <set>
            GDBZ = '0',
            GDRQ = null,
            GDRY = null
        </set>
        where YLJGBM = #{yljgbm,jdbcType=VARCHAR}
        and ZYH = #{zyh,jdbcType=VARCHAR}
    </update>

    <select id="queryBaxx" resultType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel"
            parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
        select bajbxx.* from bagl_basy_jbxx bajbxx
        inner join bagl_basy_zdxx zdxx on zdxx.zyh=bajbxx.zyh and zdxx.yljgbm=bajbxx.yljgbm
        where bajbxx.yljgbm=#{yljgbm,jdbcType=VARCHAR}
        <if test="beginrq != null">
            and (zdxx.cyrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
        </if>
        <if test="endrq != null">
            and (zdxx.cyrq &lt;= #{endrq,jdbcType=TIMESTAMP})
        </if>
    </select>

    <select id="getZyhJcList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select dj.brid, yz.rymxzlbm, yz.ylyzxh
        from zyys_ylyz yz
                 inner join zyb_rydj dj on yz.zyh = dj.zyh
        where yz.zyh = #{zyh}
          and jcfl = '1'
          and yz.zxbz = '1'
          and shbz = '1'
    </select>

    <select id="getBridMzJc" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select *
        from (select ryghxh, zhfybh, yzhm
              from mzb_brfy
              where sfjs = '1'
                and zhfybh = #{jcxmdm}
                and sftf = '1'
                and rybrid = #{brid} andWzbKfywRkdCspServiceImpl fysl>0
              order by sfsj desc)
        where rownum = 1
    </select>

    <select id="getHlts" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel"
            resultType="java.util.HashMap">
        select mxfyxmbm, count(*) ts
        from zyb_brfy
        where zyh = #{zyh,jdbcType=VARCHAR}
          and yljgbm = #{yljgbm,jdbcType=VARCHAR}
          and mxfyxmbm in ('120100002', '120100003', '120100004', '120100005')
          and yxbz = '1'
          and zfbz = '0'
          and sftf = '0'
        group by mxfyxmbm

    </select>


    <select id="getJzkjlJl" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel"
            resultType="com.supx.csp.api.zygl.crygl.pojo.Zyb_jzkjlModel">
        select *
        from zyb_jzkjl
        where zyh = #{zyh,jdbcType=VARCHAR}
          and yljgbm = #{yljgbm,jdbcType=VARCHAR}
          and zfbz = '0'
          and jrbz = '1'
        order by djrq

    </select>


    <select id="getTqBrxx" parameterType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel"
            resultType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel">

        select jzk.zrks                                                                             admdeptcode,
               jzk.zrksmc                                                                           admdeptname,
               zydjxx.ryxm                                                                          brxm,
               rydj.zyh,
               zyjsxx.rybh,
               zyjsxx.jzid,
               zyjsxx.jsid,
               rydj.bah                                                                             medcasno,
               brgj.gjmc                                                                            ntly,
               brzy.zybm                                                                            prfs,
               hykxx.f_xzz                                                                          curraddr,
               hykxx.f_gzdw                                                                         empname,
               brjbxx.dwdz                                                                          empaddr,
               brjbxx.dwyb                                                                          poscode,
               brjbxx.dwdh                                                                          emptel,
               hykxx.F_LXR                                                                          conername,
               hzgx.hzgxbm                                                                          patnrlts,
               hykxx.f_lxrdz                                                                        coneraddr,
               hykxx.f_lxrdh                                                                        conertel,
               ybrytj.rytjbm                                                                        admway,
               zllb.zllbbm                                                                          trttype,
               to_char(rydj.ryrq, 'yyyy-mm-dd hh24:mi:ss')                                          admtime,
               to_char(b.ksrq, 'yyyy-mm-dd hh24:mi:ss')                                             dscgtime,
               jbzd1.icdjbmb                                                                        wmdisecode,
               jbzd1.icdjbmc                                                                        otpwmdise,
               zydjxx.lyfs                                                                          dscgway,
               hykxx.f_sf31tnryjh                                                                   daysrinpflag31,
               hykxx.f_31tnrybd                                                                     daysrinppup31,
               jsjl.jsjlid                                                                          bizsn,
               jsjl.fphm                                                                            billcode,
               jsjl.fphm                                                                            billno,
               rybm.ybdm                                                                            chfpdrcode,
               hsrybm.ybdm                                                                          respnurscode,
               zydjxx.cbdqybqh,
               hykxx.f_nsshzyqhmsj                                                                  pwcrybfadmcomadura,
               hykxx.f_nsshzyhhmsj                                                                  pwcryafadmcomadura,
               ybks.ybksbm                                                                          dscgcaty,
               rydj.cyks                                                                            dscgdeptcode,
               rydj.cyksmc                                                                          dscgdeptname,
               jbzd1.icdjbmb                                                                        fIcd10bm1,
               jbzd2.icdjbmb                                                                        fIcd10bm2,
               jbzd3.icdjbmb                                                                        fIcd10bm3,
               jbzd4.icdjbmb                                                                        fIcd10bm4,
               jbzd5.icdjbmb                                                                        fIcd10bm5,
               jbzd6.icdjbmb                                                                        fIcd10bm6,
               jbzd7.icdjbmb                                                                        fIcd10bm7,
               jbzd8.icdjbmb                                                                        fIcd10bm8,
               jbzd9.icdjbmb                                                                        fIcd10bm9,
               jbzd10.icdjbmb                                                                       fIcd10bm10,
               jbzd11.icdjbmb                                                                       fIcd10bm11,
               jbzd12.icdjbmb                                                                       fIcd10bm12,
               jbzd13.icdjbmb                                                                       fIcd10bm13,
               jbzd14.icdjbmb                                                                       fIcd10bm14,
               jbzd15.icdjbmb                                                                       fIcd10bm15,
               jbzd16.icdjbmb                                                                       fIcd10bm16,
               jbzd17.icdjbmb                                                                       fIcd10bm17,
               jbzd18.icdjbmb                                                                       fIcd10bm18,
               jbzd19.icdjbmb                                                                       fIcd10bm19,
               jbzd20.icdjbmb                                                                       fIcd10bm20,
               jbzd21.icdjbmb                                                                       fIcd10bm21,
               jbzd22.icdjbmb                                                                       fIcd10bm22,
               jbzd1.icdjbmc                                                                        fZdmc1,
               jbzd2.icdjbmc                                                                        fZdmc2,
               jbzd3.icdjbmc                                                                        fZdmc3,
               jbzd4.icdjbmc                                                                        fZdmc4,
               jbzd5.icdjbmc                                                                        fZdmc5,
               jbzd6.icdjbmc                                                                        fZdmc6,
               jbzd7.icdjbmc                                                                        fZdmc7,
               jbzd8.icdjbmc                                                                        fZdmc8,
               jbzd9.icdjbmc                                                                        fZdmc9,
               jbzd10.icdjbmc                                                                       fZdmc10,
               jbzd11.icdjbmc                                                                       fZdmc11,
               jbzd12.icdjbmc                                                                       fZdmc12,
               jbzd13.icdjbmc                                                                       fZdmc13,
               jbzd14.icdjbmc                                                                       fZdmc14,
               jbzd15.icdjbmc                                                                       fZdmc15,
               jbzd16.icdjbmc                                                                       fZdmc16,
               jbzd17.icdjbmc                                                                       fZdmc17,
               jbzd18.icdjbmc                                                                       fZdmc18,
               jbzd19.icdjbmc                                                                       fZdmc19,
               jbzd20.icdjbmc                                                                       fZdmc20,
               jbzd21.icdjbmc                                                                       fZdmc21,
               jbzd22.icdjbmc                                                                       fZdmc22,
               hykxx.f_cyqk1                                                                        fCyqk1,
               hykxx.f_cyqk2                                                                        fCyqk2,
               hykxx.f_cyqk3                                                                        fCyqk3,
               hykxx.f_cyqk4                                                                        fCyqk4,
               hykxx.f_cyqk5                                                                        fCyqk5,
               hykxx.f_cyqk6                                                                        fCyqk6,
               hykxx.f_cyqk7                                                                        fCyqk7,
               hykxx.f_cyqk8                                                                        fCyqk8,
               hykxx.f_cyqk9                                                                        fCyqk9,
               hykxx.f_cyqk10                                                                       fCyqk10,
               hykxx.f_cyqk11                                                                       fCyqk11,
               hykxx.f_cyqk12                                                                       fCyqk12,
               hykxx.f_cyqk13                                                                       fCyqk13,
               hykxx.f_cyqk14                                                                       fCyqk14,
               hykxx.f_cyqk15                                                                       fCyqk15,
               hykxx.f_cyqk16                                                                       fCyqk16,
               hykxx.f_cyqk17                                                                       fCyqk17,
               hykxx.f_cyqk18                                                                       fCyqk18,
               hykxx.f_cyqk19                                                                       fCyqk19,
               hykxx.f_cyqk20                                                                       fCyqk20,
               hykxx.f_cyqk21                                                                       fCyqk21,
               hykxx.f_cyqk22                                                                       fCyqk22,
               hykxx.f_rybq1                                                                        fRybq1,
               hykxx.f_rybq2                                                                        fRybq2,
               hykxx.f_rybq3                                                                        fRybq3,
               hykxx.f_rybq4                                                                        fRybq4,
               hykxx.f_rybq5                                                                        fRybq5,
               hykxx.f_rybq6                                                                        fRybq6,
               hykxx.f_rybq7                                                                        fRybq7,
               hykxx.f_rybq8                                                                        fRybq8,
               hykxx.f_rybq9                                                                        fRybq9,
               hykxx.f_rybq10                                                                       fRybq10,
               hykxx.f_rybq11                                                                       fRybq11,
               hykxx.f_rybq12                                                                       fRybq12,
               hykxx.f_rybq13                                                                       fRybq13,
               hykxx.f_rybq14                                                                       fRybq14,
               hykxx.f_rybq15                                                                       fRybq15,
               hykxx.f_rybq16                                                                       fRybq16,
               hykxx.f_rybq17                                                                       fRybq17,
               hykxx.f_rybq18                                                                       fRybq18,
               hykxx.f_rybq19                                                                       fRybq19,
               hykxx.f_rybq20                                                                       fRybq20,
               hykxx.f_rybq21                                                                       fRybq21,
               hykxx.f_rybq22                                                                       fRybq22,
               hykxx.f_mz1                                                                          fMz1,
               hykxx.f_mz2                                                                          fMz2,
               hykxx.f_mz3                                                                          fMz3,
               hykxx.f_mz4                                                                          fMz4,
               hykxx.f_mz5                                                                          fMz5,
               hykxx.f_mz6                                                                          fMz6,
               hykxx.f_mz7                                                                          fMz7,
               hykxx.f_mz8                                                                          fMz8,
               hykxx.f_ys1                                                                          fYs1,
               hykxx.f_ys2                                                                          fYs2,
               hykxx.f_ys3                                                                          fYs3,
               hykxx.f_ys4                                                                          fYs4,
               hykxx.f_ys5                                                                          fYs5,
               hykxx.f_ys6                                                                          fYs6,
               hykxx.f_ys7                                                                          fYs7,
               hykxx.f_ys8                                                                          fYs8,
               hykxx.f_mzys1                                                                        fMzys1,
               hykxx.f_mzys2                                                                        fMzys2,
               hykxx.f_mzys3                                                                        fMzys3,
               hykxx.f_mzys4                                                                        fMzys4,
               hykxx.f_mzys5                                                                        fMzys5,
               hykxx.f_mzys6                                                                        fMzys6,
               hykxx.f_mzys7                                                                        fMzys7,
               hykxx.f_mzys8                                                                        fMzys8,
               hykxx.f_yhqk1                                                                        fYhqk1,
               hykxx.f_yhqk2                                                                        fYhqk2,
               hykxx.f_yhqk3                                                                        fYhqk3,
               hykxx.f_yhqk4                                                                        fYhqk4,
               hykxx.f_yhqk5                                                                        fYhqk5,
               hykxx.f_yhqk6                                                                        fYhqk6,
               hykxx.f_yhqk7                                                                        fYhqk7,
               hykxx.f_yhqk8                                                                        fYhqk8,
               hykxx.f_ssyz                                                                         fSsyz,
               hykxx.f_ssyz2                                                                        fSsyz2,
               hykxx.f_ssyz3                                                                        fSsyz3,
               hykxx.f_ssyz4                                                                        fSsyz4,
               hykxx.f_ssyz5                                                                        fSsyz5,
               hykxx.f_ssyz6                                                                        fSsyz6,
               hykxx.f_ssyz7                                                                        fSsyz7,
               hykxx.f_ssyz8                                                                        fSsyz8,
               hykxx.f_ssez                                                                         fSsez,
               hykxx.f_ssez2                                                                        fSsez2,
               hykxx.f_ssez3                                                                        fSsez3,
               hykxx.f_ssez4                                                                        fSsez4,
               hykxx.f_ssez5                                                                        fSsez5,
               hykxx.f_ssez6                                                                        fSsez6,
               hykxx.f_ssez7                                                                        fSsez7,
               hykxx.f_ssez8                                                                        fSsez8,
               decode(hykxx.f_ssrq1, null, '', substr(hykxx.f_ssrq1, 0, length(hykxx.f_ssrq1) - 2)) fSsrq1,
               decode(hykxx.f_ssrq2, null, '', substr(hykxx.f_ssrq2, 0, length(hykxx.f_ssrq2) - 2)) fSsrq2,
               decode(hykxx.f_ssrq3, null, '', substr(hykxx.f_ssrq3, 0, length(hykxx.f_ssrq3) - 2)) fSsrq3,
               decode(hykxx.f_ssrq4, null, '', substr(hykxx.f_ssrq4, 0, length(hykxx.f_ssrq4) - 2)) fSsrq4,
               decode(hykxx.f_ssrq5, null, '', substr(hykxx.f_ssrq5, 0, length(hykxx.f_ssrq5) - 2)) fSsrq5,
               decode(hykxx.f_ssrq6, null, '', substr(hykxx.f_ssrq6, 0, length(hykxx.f_ssrq6) - 2)) fSsrq6,
               decode(hykxx.f_ssrq7, null, '', substr(hykxx.f_ssrq7, 0, length(hykxx.f_ssrq7) - 2)) fSsrq7,
               decode(hykxx.f_ssrq8, null, '', substr(hykxx.f_ssrq8, 0, length(hykxx.f_ssrq8) - 2)) fSsrq8,
               ss1.icdssmb                                                                          fSsbm1,
               ss1.icdssmc                                                                          fSsmc1,
               ss2.icdssmb                                                                          fSsbm2,
               ss2.icdssmc                                                                          fSsmc2,
               ss3.icdssmb                                                                          fSsbm3,
               ss3.icdssmc                                                                          fSsmc3,
               ss4.icdssmb                                                                          fSsbm4,
               ss4.icdssmc                                                                          fSsmc4,
               ss5.icdssmb                                                                          fSsbm5,
               ss5.icdssmc                                                                          fSsmc5,
               ss6.icdssmb                                                                          fSsbm6,
               ss6.icdssmc                                                                          fSsmc6,
               ss7.icdssmb                                                                          fSsbm7,
               ss7.icdssmc                                                                          fSsmc7,
               ss8.icdssmb                                                                          fSsbm8,
               ss8.icdssmc                                                                          fSsmc8,
               rybm.ryxm                                                                            zyysxm,
               hsrybm.ryxm                                                                          zrhsxm,
               to_char(zyjsxx.jssj, 'yyyy-mm-dd hh24:mi:ss')                                        setlenddate,
               (select to_char(min(fee_ocur_time), 'yyyy-mm-dd hh24:mi:ss')
                from zyb_bxscmx
                where zyh = rydj.zyh)                                                               setlbegndate

        from gyb_brjbxx brjbxx
                 inner join zyb_rydj rydj on rydj.brid = brjbxx.brid and rydj.yljgbm = brjbxx.yljgbm

                 inner join gssldyb_zyjsxx zyjsxx on rydj.zyh = zyjsxx.zyh and zyjsxx.zfbz = '0'
                 inner join gssldyb_zydjxx zydjxx on rydj.zyh = zydjxx.zyh and zydjxx.zfbz = '0'

                 inner join (select zyh, ksrq
                             from (select zyh, a.ksrq, row_number() over (PARTITION BY a.zyh ORDER BY a.ksrq desc) a
                                   from zyys_ylyz a
                                   where (yzfl = '5' or rymxzlmc like '死亡')
                                     and zxbz = '1'
                                     and shbz = '1'
                                     and zfbz = '0')
                             where a = '1') b on rydj.zyh = b.zyh

                 inner join zyb_jzkjl jzk
                            on rydj.zyh = jzk.zyh and jzk.jzklx = '0' and jzk.jrbz = '1' and jzk.zfbz = '0'
                 left join gyb_gj brgj on brjbxx.brgj = brgj.gjbm
                 left join gssldyb_zy brzy on brzy.jbxxzybm = brjbxx.zybm
                 left join gssldyb_hzgx hzgx on brjbxx.lxrgx = hzgx.jbxxhzgxbm
                 left join GSSLDYB_rytj ybrytj on ybrytj.djrytjbm = rydj.rytj
                 left join bagl_basy_zdxx zdxx on zdxx.zyh = rydj.zyh
                 left join gssldyb_zllb zllb on zllb.bdzllbbm = zdxx.zllb
                 inner join hyk_v_ba_firstpage hykxx on hykxx.f_zyh = rydj.zyh
                 left join bagl_jbbm_hyk jbzd1 on jbzd1.jbmb = hykxx.F_MJZZDBM
                 left join zyb_jsjl jsjl on rydj.zyh = jsjl.zyh and rydj.yljgbm = jsjl.yljgbm and jsjl.jszt = '0'
                 left join gyb_rybm rybm on rybm.rybm = rydj.zyys
                 left join gyb_rybm hsrybm on hsrybm.rybm = rydj.zrhs
                 left join gssldyb_ybksdy ybks on rydj.cyks = ybks.bdksbm
                 left join bagl_jbbm_hyk jbzd1 on jbzd1.jbmb = hykxx.F_ICD_10BM1
                 left join bagl_jbbm_hyk jbzd2 on jbzd2.jbmb = hykxx.F_ICD_10BM2
                 left join bagl_jbbm_hyk jbzd3 on jbzd3.jbmb = hykxx.F_ICD_10BM3
                 left join bagl_jbbm_hyk jbzd4 on jbzd4.jbmb = hykxx.F_ICD_10BM4
                 left join bagl_jbbm_hyk jbzd5 on jbzd5.jbmb = hykxx.F_ICD_10BM5
                 left join bagl_jbbm_hyk jbzd6 on jbzd6.jbmb = hykxx.F_ICD_10BM6
                 left join bagl_jbbm_hyk jbzd7 on jbzd7.jbmb = hykxx.F_ICD_10BM7
                 left join bagl_jbbm_hyk jbzd8 on jbzd8.jbmb = hykxx.F_ICD_10BM8
                 left join bagl_jbbm_hyk jbzd9 on jbzd9.jbmb = hykxx.F_ICD_10BM9
                 left join bagl_jbbm_hyk jbzd10 on jbzd10.jbmb = hykxx.F_ICD_10BM10
                 left join bagl_jbbm_hyk jbzd11 on jbzd11.jbmb = hykxx.F_ICD_10BM11
                 left join bagl_jbbm_hyk jbzd12 on jbzd12.jbmb = hykxx.F_ICD_10BM12
                 left join bagl_jbbm_hyk jbzd13 on jbzd13.jbmb = hykxx.F_ICD_10BM13
                 left join bagl_jbbm_hyk jbzd14 on jbzd14.jbmb = hykxx.F_ICD_10BM14
                 left join bagl_jbbm_hyk jbzd15 on jbzd15.jbmb = hykxx.F_ICD_10BM15
                 left join bagl_jbbm_hyk jbzd16 on jbzd16.jbmb = hykxx.F_ICD_10BM16
                 left join bagl_jbbm_hyk jbzd17 on jbzd17.jbmb = hykxx.F_ICD_10BM17
                 left join bagl_jbbm_hyk jbzd18 on jbzd18.jbmb = hykxx.F_ICD_10BM18
                 left join bagl_jbbm_hyk jbzd19 on jbzd19.jbmb = hykxx.F_ICD_10BM19
                 left join bagl_jbbm_hyk jbzd20 on jbzd20.jbmb = hykxx.F_ICD_10BM20
                 left join bagl_jbbm_hyk jbzd21 on jbzd21.jbmb = hykxx.F_ICD_10BM21
                 left join bagl_jbbm_hyk jbzd22 on jbzd22.jbmb = hykxx.F_ICD_10BM22
                 left join BAGL_SSBM_HYK ss1 on ss1.ssmb = hykxx.f_ssbm1
                 left join BAGL_SSBM_HYK ss2 on ss2.ssmb = hykxx.f_ssbm2
                 left join BAGL_SSBM_HYK ss3 on ss3.ssmb = hykxx.f_ssbm3
                 left join BAGL_SSBM_HYK ss4 on ss4.ssmb = hykxx.f_ssbm4
                 left join BAGL_SSBM_HYK ss5 on ss5.ssmb = hykxx.f_ssbm5
                 left join BAGL_SSBM_HYK ss6 on ss6.ssmb = hykxx.f_ssbm6
                 left join BAGL_SSBM_HYK ss7 on ss7.ssmb = hykxx.f_ssbm7
                 left join BAGL_SSBM_HYK ss8 on ss8.ssmb = hykxx.f_ssbm8


        where rydj.ryks not in ('1051', '0993')
          and (zydjxx.jzid like '519900G%' or zydjxx.jzid like '510100CD%')
          and zyjsxx.jssj &gt;= #{beginrq,jdbcType=TIMESTAMP}
          and zyjsxx.jssj &lt;= #{endrq,jdbcType=TIMESTAMP}

          and rydj.zyh not in (select zyh
                               from gssldyb_ybjsdsc)

    </select>

    <insert id="insertYbjsc" parameterType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel">
        insert into gssldyb_ybjsdsc(setlbegndate, setlenddate, admdeptname, admdeptcode, zyysxm, zrhsxm, ZYH, RYBH,
                                    JZID, JSID, MEDCASNO, NTLY, PRFS, CURRADDR, EMPNAME, EMPADDR, POSCODE, EMPTEL,
                                    CONERNAME, PATNRLTS, CONERADDR, CONERTEL, ADMWAY, TRTTYPE, ADMTIME, DSCGTIME,
                                    WMDISECODE, OTPWMDISE, DSCGWAY, DAYSRINPFLAG31, DAYSRINPPUP31, BIZSN, BILLCODE,
                                    BILLNO, CHFPDRCODE, RESPNURSCODE, CBDQYBQH, PWCRYBFADMCOMADURA, PWCRYAFADMCOMADURA,
                                    DSCGCATY, FICD10BM1, FICD10BM2, FICD10BM3, FICD10BM4, FICD10BM5, FICD10BM6,
                                    FICD10BM7, FICD10BM8, FICD10BM9, FICD10BM10, FICD10BM11, FICD10BM12, FICD10BM13,
                                    FICD10BM14, FICD10BM15, FICD10BM16, FICD10BM17, FICD10BM18, FICD10BM19, FICD10BM20,
                                    FICD10BM21, FICD10BM22, FZDMC1, FZDMC2, FZDMC3, FZDMC4, FZDMC5, FZDMC6, FZDMC7,
                                    FZDMC8, FZDMC9, FZDMC10, FZDMC11, FZDMC12, FZDMC13, FZDMC14, FZDMC15, FZDMC16,
                                    FZDMC17, FZDMC18, FZDMC19, FZDMC20, FZDMC21, FZDMC22, FCYQK1, FCYQK2, FCYQK3,
                                    FCYQK4, FCYQK5, FCYQK6, FCYQK7, FCYQK8, FCYQK9, FCYQK10, FCYQK11, FCYQK12, FCYQK13,
                                    FCYQK14, FCYQK15, FCYQK16, FCYQK17, FCYQK18, FCYQK19, FCYQK20, FCYQK21, FCYQK22,
                                    FRYBQ1, FRYBQ2, FRYBQ3, FRYBQ4, FRYBQ5, FRYBQ6, FRYBQ7, FRYBQ8, FRYBQ9, FRYBQ10,
                                    FRYBQ11, FRYBQ12, FRYBQ13, FRYBQ14, FRYBQ15, FRYBQ16, FRYBQ17, FRYBQ18, FRYBQ19,
                                    FRYBQ20, FRYBQ21, FRYBQ22, FMZ1, FMZ2, FMZ3, FMZ4, FMZ5, FMZ6, FMZ7, FMZ8, FYS1,
                                    FYS2, FYS3, FYS4, FYS5, FYS6, FYS7, FYS8, FMZYS1, FMZYS2, FMZYS3, FMZYS4, FMZYS5,
                                    FMZYS6, FMZYS7, FMZYS8, FYHQK1, FYHQK2, FYHQK3, FYHQK4, FYHQK5, FYHQK6, FYHQK7,
                                    FYHQK8, FSSYZ, FSSYZ2, FSSYZ3, FSSYZ4, FSSYZ5, FSSYZ6, FSSYZ7, FSSYZ8, FSSEZ,
                                    FSSEZ2, FSSEZ3, FSSEZ4, FSSEZ5, FSSEZ6, FSSEZ7, FSSEZ8, FSSRQ1, FSSRQ2, FSSRQ3,
                                    FSSRQ4, FSSRQ5, FSSRQ6, FSSRQ7, FSSRQ8, FSSBM1, FSSMC1, FSSBM2, FSSMC2, FSSBM3,
                                    FSSMC3, FSSBM4, FSSMC4, FSSBM5, FSSMC5, FSSBM6, FSSMC6, FSSBM7, FSSMC7, FSSBM8,
                                    FSSMC8, REFLDEPTDEPT, DSCGDEPTCODE, DSCGDEPTNAME, ybzkkbbm, ybzkkbmc, tjhlts,
                                    yjhlts, ejhlts, sjhlts, brxm, fys1code, fys2code, fys3code, fys4code, fys5code,
                                    fys6code, fys7code, fys8code, fmzys1code, fmzys2code, fmzys3code, fmzys4code,
                                    fmzys5code, fmzys6code, fmzys7code, fmzys8code, ventuseddurat, ventuseddurax,
                                    ventusedduraf)
        values (#{setlbegndate,jdbcType=VARCHAR}, #{setlenddate,jdbcType=VARCHAR}, #{admdeptname,jdbcType=VARCHAR},
                #{admdeptcode,jdbcType=VARCHAR}, #{zyysxm,jdbcType=VARCHAR}, #{zrhsxm,jdbcType=VARCHAR},
                #{zyh,jdbcType=VARCHAR}, #{rybh,jdbcType=VARCHAR}, #{jzid,jdbcType=VARCHAR}, #{jsid,jdbcType=VARCHAR},
                #{medcasno,jdbcType=VARCHAR}, #{ntly,jdbcType=VARCHAR}, #{prfs,jdbcType=VARCHAR},
                #{curraddr,jdbcType=VARCHAR}, #{empname,jdbcType=VARCHAR}, #{empaddr,jdbcType=VARCHAR},
                #{poscode,jdbcType=VARCHAR}, #{emptel,jdbcType=VARCHAR}, #{conername,jdbcType=VARCHAR},
                #{patnrlts,jdbcType=VARCHAR}, #{coneraddr,jdbcType=VARCHAR}, #{conertel,jdbcType=VARCHAR},
                #{admway,jdbcType=VARCHAR}, #{trttype,jdbcType=VARCHAR}, #{admtime,jdbcType=VARCHAR},
                #{dscgtime,jdbcType=VARCHAR}, #{wmdisecode,jdbcType=VARCHAR}, #{otpwmdise,jdbcType=VARCHAR},
                #{dscgway,jdbcType=VARCHAR}, #{daysrinpflag31,jdbcType=VARCHAR}, #{daysrinppup31,jdbcType=VARCHAR},
                #{bizsn,jdbcType=VARCHAR}, #{billcode,jdbcType=VARCHAR}, #{billno,jdbcType=VARCHAR},
                #{chfpdrcode,jdbcType=VARCHAR}, #{respnurscode,jdbcType=VARCHAR}, #{cbdqybqh,jdbcType=VARCHAR},
                #{pwcrybfadmcomadura,jdbcType=VARCHAR}, #{pwcryafadmcomadura,jdbcType=VARCHAR},
                #{dscgcaty,jdbcType=VARCHAR}, #{ficd10bm1,jdbcType=VARCHAR}, #{ficd10bm2,jdbcType=VARCHAR},
                #{ficd10bm3,jdbcType=VARCHAR}, #{ficd10bm4,jdbcType=VARCHAR}, #{ficd10bm5,jdbcType=VARCHAR},
                #{ficd10bm6,jdbcType=VARCHAR}, #{ficd10bm7,jdbcType=VARCHAR}, #{ficd10bm8,jdbcType=VARCHAR},
                #{ficd10bm9,jdbcType=VARCHAR}, #{ficd10bm10,jdbcType=VARCHAR}, #{ficd10bm11,jdbcType=VARCHAR},
                #{ficd10bm12,jdbcType=VARCHAR}, #{ficd10bm13,jdbcType=VARCHAR}, #{ficd10bm14,jdbcType=VARCHAR},
                #{ficd10bm15,jdbcType=VARCHAR}, #{ficd10bm16,jdbcType=VARCHAR}, #{ficd10bm17,jdbcType=VARCHAR},
                #{ficd10bm18,jdbcType=VARCHAR}, #{ficd10bm19,jdbcType=VARCHAR}, #{ficd10bm20,jdbcType=VARCHAR},
                #{ficd10bm21,jdbcType=VARCHAR}, #{ficd10bm22,jdbcType=VARCHAR}, #{fzdmc1,jdbcType=VARCHAR},
                #{fzdmc2,jdbcType=VARCHAR}, #{fzdmc3,jdbcType=VARCHAR}, #{fzdmc4,jdbcType=VARCHAR},
                #{fzdmc5,jdbcType=VARCHAR}, #{fzdmc6,jdbcType=VARCHAR}, #{fzdmc7,jdbcType=VARCHAR},
                #{fzdmc8,jdbcType=VARCHAR}, #{fzdmc9,jdbcType=VARCHAR}, #{fzdmc10,jdbcType=VARCHAR},
                #{fzdmc11,jdbcType=VARCHAR}, #{fzdmc12,jdbcType=VARCHAR}, #{fzdmc13,jdbcType=VARCHAR},
                #{fzdmc14,jdbcType=VARCHAR}, #{fzdmc15,jdbcType=VARCHAR}, #{fzdmc16,jdbcType=VARCHAR},
                #{fzdmc17,jdbcType=VARCHAR}, #{fzdmc18,jdbcType=VARCHAR}, #{fzdmc19,jdbcType=VARCHAR},
                #{fzdmc20,jdbcType=VARCHAR}, #{fzdmc21,jdbcType=VARCHAR}, #{fzdmc22,jdbcType=VARCHAR},
                #{fcyqk1,jdbcType=VARCHAR}, #{fcyqk2,jdbcType=VARCHAR}, #{fcyqk3,jdbcType=VARCHAR},
                #{fcyqk4,jdbcType=VARCHAR}, #{fcyqk5,jdbcType=VARCHAR}, #{fcyqk6,jdbcType=VARCHAR},
                #{fcyqk7,jdbcType=VARCHAR}, #{fcyqk8,jdbcType=VARCHAR}, #{fcyqk9,jdbcType=VARCHAR},
                #{fcyqk10,jdbcType=VARCHAR}, #{fcyqk11,jdbcType=VARCHAR}, #{fcyqk12,jdbcType=VARCHAR},
                #{fcyqk13,jdbcType=VARCHAR}, #{fcyqk14,jdbcType=VARCHAR}, #{fcyqk15,jdbcType=VARCHAR},
                #{fcyqk16,jdbcType=VARCHAR}, #{fcyqk17,jdbcType=VARCHAR}, #{fcyqk18,jdbcType=VARCHAR},
                #{fcyqk19,jdbcType=VARCHAR}, #{fcyqk20,jdbcType=VARCHAR}, #{fcyqk21,jdbcType=VARCHAR},
                #{fcyqk22,jdbcType=VARCHAR}, #{frybq1,jdbcType=VARCHAR}, #{frybq2,jdbcType=VARCHAR},
                #{frybq3,jdbcType=VARCHAR}, #{frybq4,jdbcType=VARCHAR}, #{frybq5,jdbcType=VARCHAR},
                #{frybq6,jdbcType=VARCHAR}, #{frybq7,jdbcType=VARCHAR}, #{frybq8,jdbcType=VARCHAR},
                #{frybq9,jdbcType=VARCHAR}, #{frybq10,jdbcType=VARCHAR}, #{frybq11,jdbcType=VARCHAR},
                #{frybq12,jdbcType=VARCHAR}, #{frybq13,jdbcType=VARCHAR}, #{frybq14,jdbcType=VARCHAR},
                #{frybq15,jdbcType=VARCHAR}, #{frybq16,jdbcType=VARCHAR}, #{frybq17,jdbcType=VARCHAR},
                #{frybq18,jdbcType=VARCHAR}, #{frybq19,jdbcType=VARCHAR}, #{frybq20,jdbcType=VARCHAR},
                #{frybq21,jdbcType=VARCHAR}, #{frybq22,jdbcType=VARCHAR}, #{fmz1,jdbcType=VARCHAR},
                #{fmz2,jdbcType=VARCHAR}, #{fmz3,jdbcType=VARCHAR}, #{fmz4,jdbcType=VARCHAR}, #{fmz5,jdbcType=VARCHAR},
                #{fmz6,jdbcType=VARCHAR}, #{fmz7,jdbcType=VARCHAR}, #{fmz8,jdbcType=VARCHAR}, #{fys1,jdbcType=VARCHAR},
                #{fys2,jdbcType=VARCHAR}, #{fys3,jdbcType=VARCHAR}, #{fys4,jdbcType=VARCHAR}, #{fys5,jdbcType=VARCHAR},
                #{fys6,jdbcType=VARCHAR}, #{fys7,jdbcType=VARCHAR}, #{fys8,jdbcType=VARCHAR},
                #{fmzys1,jdbcType=VARCHAR}, #{fmzys2,jdbcType=VARCHAR}, #{fmzys3,jdbcType=VARCHAR},
                #{fmzys4,jdbcType=VARCHAR}, #{fmzys5,jdbcType=VARCHAR}, #{fmzys6,jdbcType=VARCHAR},
                #{fmzys7,jdbcType=VARCHAR}, #{fmzys8,jdbcType=VARCHAR}, #{fyhqk1,jdbcType=VARCHAR},
                #{fyhqk2,jdbcType=VARCHAR}, #{fyhqk3,jdbcType=VARCHAR}, #{fyhqk4,jdbcType=VARCHAR},
                #{fyhqk5,jdbcType=VARCHAR}, #{fyhqk6,jdbcType=VARCHAR}, #{fyhqk7,jdbcType=VARCHAR},
                #{fyhqk8,jdbcType=VARCHAR}, #{fssyz,jdbcType=VARCHAR}, #{fssyz2,jdbcType=VARCHAR},
                #{fssyz3,jdbcType=VARCHAR}, #{fssyz4,jdbcType=VARCHAR}, #{fssyz5,jdbcType=VARCHAR},
                #{fssyz6,jdbcType=VARCHAR}, #{fssyz7,jdbcType=VARCHAR}, #{fssyz8,jdbcType=VARCHAR},
                #{fssez,jdbcType=VARCHAR}, #{fssez2,jdbcType=VARCHAR}, #{fssez3,jdbcType=VARCHAR},
                #{fssez4,jdbcType=VARCHAR}, #{fssez5,jdbcType=VARCHAR}, #{fssez6,jdbcType=VARCHAR},
                #{fssez7,jdbcType=VARCHAR}, #{fssez8,jdbcType=VARCHAR}, #{fssrq1,jdbcType=VARCHAR},
                #{fssrq2,jdbcType=VARCHAR}, #{fssrq3,jdbcType=VARCHAR}, #{fssrq4,jdbcType=VARCHAR},
                #{fssrq5,jdbcType=VARCHAR}, #{fssrq6,jdbcType=VARCHAR}, #{fssrq7,jdbcType=VARCHAR},
                #{fssrq8,jdbcType=VARCHAR}, #{fssbm1,jdbcType=VARCHAR}, #{fssmc1,jdbcType=VARCHAR},
                #{fssbm2,jdbcType=VARCHAR}, #{fssmc2,jdbcType=VARCHAR}, #{fssbm3,jdbcType=VARCHAR},
                #{fssmc3,jdbcType=VARCHAR}, #{fssbm4,jdbcType=VARCHAR}, #{fssmc4,jdbcType=VARCHAR},
                #{fssbm5,jdbcType=VARCHAR}, #{fssmc5,jdbcType=VARCHAR}, #{fssbm6,jdbcType=VARCHAR},
                #{fssmc6,jdbcType=VARCHAR}, #{fssbm7,jdbcType=VARCHAR}, #{fssmc7,jdbcType=VARCHAR},
                #{fssbm8,jdbcType=VARCHAR}, #{fssmc8,jdbcType=VARCHAR}, #{refldeptdept,jdbcType=VARCHAR},
                #{dscgdeptcode,jdbcType=VARCHAR}, #{dscgdeptname,jdbcType=VARCHAR}, #{ybzkkbbm,jdbcType=VARCHAR},
                #{ybzkkbmc,jdbcType=VARCHAR}, #{tjhlts,jdbcType=VARCHAR}, #{yjhlts,jdbcType=VARCHAR},
                #{ejhlts,jdbcType=VARCHAR}, #{sjhlts,jdbcType=VARCHAR}, #{brxm,jdbcType=VARCHAR},
                #{fys1code,jdbcType=VARCHAR}, #{fys2code,jdbcType=VARCHAR}, #{fys3code,jdbcType=VARCHAR},
                #{fys4code,jdbcType=VARCHAR}, #{fys5code,jdbcType=VARCHAR}, #{fys6code,jdbcType=VARCHAR},
                #{fys7code,jdbcType=VARCHAR}, #{fys8code,jdbcType=VARCHAR}, #{fmzys1code,jdbcType=VARCHAR},
                #{fmzys2code,jdbcType=VARCHAR}, #{fmzys3code,jdbcType=VARCHAR}, #{fmzys4code,jdbcType=VARCHAR},
                #{fmzys5code,jdbcType=VARCHAR}, #{fmzys6code,jdbcType=VARCHAR}, #{fmzys7code,jdbcType=VARCHAR},
                #{fmzys8code,jdbcType=VARCHAR}, #{ventuseddurat,jdbcType=VARCHAR}, #{ventuseddurax,jdbcType=VARCHAR},
                #{ventusedduraf,jdbcType=VARCHAR})
    </insert>


    <delete id="deleteYbjsc" parameterType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel">
        delete
        from gssldyb_ybjsdsc
        where zyh = #{zyh,jdbcType=VARCHAR}
          and sczt = '0'
          and shzt = '0'
    </delete>


    <update id="updateYbjsc" parameterType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel">
        update gssldyb_ybjsdsc
        <set>
            <if test="jzid!=null">jzid=#{jzid,jdbcType=VARCHAR},</if>
            <if test="jsid!=null">jsid=#{jsid,jdbcType=VARCHAR},</if>
            <if test="medcasno!=null">medcasno=#{medcasno,jdbcType=VARCHAR},</if>
            <if test="ntly!=null">ntly=#{ntly,jdbcType=VARCHAR},</if>
            <if test="prfs!=null">prfs=#{prfs,jdbcType=VARCHAR},</if>
            <if test="curraddr!=null">curraddr=#{curraddr,jdbcType=VARCHAR},</if>
            <if test="empname!=null">empname=#{empname,jdbcType=VARCHAR},</if>
            <if test="empaddr!=null">empaddr=#{empaddr,jdbcType=VARCHAR},</if>
            <if test="poscode!=null">poscode=#{poscode,jdbcType=VARCHAR},</if>
            <if test="emptel!=null">emptel=#{emptel,jdbcType=VARCHAR},</if>
            <if test="conername!=null">conername=#{conername,jdbcType=VARCHAR},</if>
            <if test="patnrlts!=null">patnrlts=#{patnrlts,jdbcType=VARCHAR},</if>
            <if test="coneraddr!=null">coneraddr=#{coneraddr,jdbcType=VARCHAR},</if>
            <if test="conertel!=null">conertel=#{conertel,jdbcType=VARCHAR},</if>
            <if test="admway!=null">admway=#{admway,jdbcType=VARCHAR},</if>
            <if test="trttype!=null">trttype=#{trttype,jdbcType=VARCHAR},</if>
            <if test="admtime!=null">admtime=#{admtime,jdbcType=VARCHAR},</if>
            <if test="dscgtime!=null">dscgtime=#{dscgtime,jdbcType=VARCHAR},</if>
            <if test="wmdisecode!=null">wmdisecode=#{wmdisecode,jdbcType=VARCHAR},</if>
            <if test="otpwmdise!=null">otpwmdise=#{otpwmdise,jdbcType=VARCHAR},</if>
            <if test="dscgway!=null">dscgway=#{dscgway,jdbcType=VARCHAR},</if>
            <if test="daysrinpflag31!=null">daysrinpflag31=#{daysrinpflag31,jdbcType=VARCHAR},</if>
            <if test="daysrinppup31!=null">daysrinppup31=#{daysrinppup31,jdbcType=VARCHAR},</if>
            <if test="bizsn!=null">bizsn=#{bizsn,jdbcType=VARCHAR},</if>
            <if test="billcode!=null">billcode=#{billcode,jdbcType=VARCHAR},</if>
            <if test="billno!=null">billno=#{billno,jdbcType=VARCHAR},</if>
            <if test="chfpdrcode!=null">chfpdrcode=#{chfpdrcode,jdbcType=VARCHAR},</if>
            <if test="respnurscode!=null">respnurscode=#{respnurscode,jdbcType=VARCHAR},</if>
            <if test="cbdqybqh!=null">cbdqybqh=#{cbdqybqh,jdbcType=VARCHAR},</if>
            <if test="pwcrybfadmcomadura!=null">pwcrybfadmcomadura=#{pwcrybfadmcomadura,jdbcType=VARCHAR},</if>
            <if test="pwcryafadmcomadura!=null">pwcryafadmcomadura=#{pwcryafadmcomadura,jdbcType=VARCHAR},</if>
            <if test="dscgcaty!=null">dscgcaty=#{dscgcaty,jdbcType=VARCHAR},</if>
            <if test="ficd10bm1!=null">ficd10bm1=#{ficd10bm1,jdbcType=VARCHAR},</if>
            <if test="ficd10bm2!=null">ficd10bm2=#{ficd10bm2,jdbcType=VARCHAR},</if>
            <if test="ficd10bm3!=null">ficd10bm3=#{ficd10bm3,jdbcType=VARCHAR},</if>
            <if test="ficd10bm4!=null">ficd10bm4=#{ficd10bm4,jdbcType=VARCHAR},</if>
            <if test="ficd10bm5!=null">ficd10bm5=#{ficd10bm5,jdbcType=VARCHAR},</if>
            <if test="ficd10bm6!=null">ficd10bm6=#{ficd10bm6,jdbcType=VARCHAR},</if>
            <if test="ficd10bm7!=null">ficd10bm7=#{ficd10bm7,jdbcType=VARCHAR},</if>
            <if test="ficd10bm8!=null">ficd10bm8=#{ficd10bm8,jdbcType=VARCHAR},</if>
            <if test="ficd10bm9!=null">ficd10bm9=#{ficd10bm9,jdbcType=VARCHAR},</if>
            <if test="ficd10bm10!=null">ficd10bm10=#{ficd10bm10,jdbcType=VARCHAR},</if>
            <if test="ficd10bm11!=null">ficd10bm11=#{ficd10bm11,jdbcType=VARCHAR},</if>
            <if test="ficd10bm12!=null">ficd10bm12=#{ficd10bm12,jdbcType=VARCHAR},</if>
            <if test="ficd10bm13!=null">ficd10bm13=#{ficd10bm13,jdbcType=VARCHAR},</if>
            <if test="ficd10bm14!=null">ficd10bm14=#{ficd10bm14,jdbcType=VARCHAR},</if>
            <if test="ficd10bm15!=null">ficd10bm15=#{ficd10bm15,jdbcType=VARCHAR},</if>
            <if test="ficd10bm16!=null">ficd10bm16=#{ficd10bm16,jdbcType=VARCHAR},</if>
            <if test="ficd10bm17!=null">ficd10bm17=#{ficd10bm17,jdbcType=VARCHAR},</if>
            <if test="ficd10bm18!=null">ficd10bm18=#{ficd10bm18,jdbcType=VARCHAR},</if>
            <if test="ficd10bm19!=null">ficd10bm19=#{ficd10bm19,jdbcType=VARCHAR},</if>
            <if test="ficd10bm20!=null">ficd10bm20=#{ficd10bm20,jdbcType=VARCHAR},</if>
            <if test="ficd10bm21!=null">ficd10bm21=#{ficd10bm21,jdbcType=VARCHAR},</if>
            <if test="ficd10bm22!=null">ficd10bm22=#{ficd10bm22,jdbcType=VARCHAR},</if>
            <if test="fzdmc1!=null">fzdmc1=#{fzdmc1,jdbcType=VARCHAR},</if>
            <if test="fzdmc2!=null">fzdmc2=#{fzdmc2,jdbcType=VARCHAR},</if>
            <if test="fzdmc3!=null">fzdmc3=#{fzdmc3,jdbcType=VARCHAR},</if>
            <if test="fzdmc4!=null">fzdmc4=#{fzdmc4,jdbcType=VARCHAR},</if>
            <if test="fzdmc5!=null">fzdmc5=#{fzdmc5,jdbcType=VARCHAR},</if>
            <if test="fzdmc6!=null">fzdmc6=#{fzdmc6,jdbcType=VARCHAR},</if>
            <if test="fzdmc7!=null">fzdmc7=#{fzdmc7,jdbcType=VARCHAR},</if>
            <if test="fzdmc8!=null">fzdmc8=#{fzdmc8,jdbcType=VARCHAR},</if>
            <if test="fzdmc9!=null">fzdmc9=#{fzdmc9,jdbcType=VARCHAR},</if>
            <if test="fzdmc10!=null">fzdmc10=#{fzdmc10,jdbcType=VARCHAR},</if>
            <if test="fzdmc11!=null">fzdmc11=#{fzdmc11,jdbcType=VARCHAR},</if>
            <if test="fzdmc12!=null">fzdmc12=#{fzdmc12,jdbcType=VARCHAR},</if>
            <if test="fzdmc13!=null">fzdmc13=#{fzdmc13,jdbcType=VARCHAR},</if>
            <if test="fzdmc14!=null">fzdmc14=#{fzdmc14,jdbcType=VARCHAR},</if>
            <if test="fzdmc15!=null">fzdmc15=#{fzdmc15,jdbcType=VARCHAR},</if>
            <if test="fzdmc16!=null">fzdmc16=#{fzdmc16,jdbcType=VARCHAR},</if>
            <if test="fzdmc17!=null">fzdmc17=#{fzdmc17,jdbcType=VARCHAR},</if>
            <if test="fzdmc18!=null">fzdmc18=#{fzdmc18,jdbcType=VARCHAR},</if>
            <if test="fzdmc19!=null">fzdmc19=#{fzdmc19,jdbcType=VARCHAR},</if>
            <if test="fzdmc20!=null">fzdmc20=#{fzdmc20,jdbcType=VARCHAR},</if>
            <if test="fzdmc21!=null">fzdmc21=#{fzdmc21,jdbcType=VARCHAR},</if>
            <if test="fzdmc22!=null">fzdmc22=#{fzdmc22,jdbcType=VARCHAR},</if>
            <if test="fcyqk1!=null">fcyqk1=#{fcyqk1,jdbcType=VARCHAR},</if>
            <if test="fcyqk2!=null">fcyqk2=#{fcyqk2,jdbcType=VARCHAR},</if>
            <if test="fcyqk3!=null">fcyqk3=#{fcyqk3,jdbcType=VARCHAR},</if>
            <if test="fcyqk4!=null">fcyqk4=#{fcyqk4,jdbcType=VARCHAR},</if>
            <if test="fcyqk5!=null">fcyqk5=#{fcyqk5,jdbcType=VARCHAR},</if>
            <if test="fcyqk6!=null">fcyqk6=#{fcyqk6,jdbcType=VARCHAR},</if>
            <if test="fcyqk7!=null">fcyqk7=#{fcyqk7,jdbcType=VARCHAR},</if>
            <if test="fcyqk8!=null">fcyqk8=#{fcyqk8,jdbcType=VARCHAR},</if>
            <if test="fcyqk9!=null">fcyqk9=#{fcyqk9,jdbcType=VARCHAR},</if>
            <if test="fcyqk10!=null">fcyqk10=#{fcyqk10,jdbcType=VARCHAR},</if>
            <if test="fcyqk11!=null">fcyqk11=#{fcyqk11,jdbcType=VARCHAR},</if>
            <if test="fcyqk12!=null">fcyqk12=#{fcyqk12,jdbcType=VARCHAR},</if>
            <if test="fcyqk13!=null">fcyqk13=#{fcyqk13,jdbcType=VARCHAR},</if>
            <if test="fcyqk14!=null">fcyqk14=#{fcyqk14,jdbcType=VARCHAR},</if>
            <if test="fcyqk15!=null">fcyqk15=#{fcyqk15,jdbcType=VARCHAR},</if>
            <if test="fcyqk16!=null">fcyqk16=#{fcyqk16,jdbcType=VARCHAR},</if>
            <if test="fcyqk17!=null">fcyqk17=#{fcyqk17,jdbcType=VARCHAR},</if>
            <if test="fcyqk18!=null">fcyqk18=#{fcyqk18,jdbcType=VARCHAR},</if>
            <if test="fcyqk19!=null">fcyqk19=#{fcyqk19,jdbcType=VARCHAR},</if>
            <if test="fcyqk20!=null">fcyqk20=#{fcyqk20,jdbcType=VARCHAR},</if>
            <if test="fcyqk21!=null">fcyqk21=#{fcyqk21,jdbcType=VARCHAR},</if>
            <if test="fcyqk22!=null">fcyqk22=#{fcyqk22,jdbcType=VARCHAR},</if>
            <if test="frybq1!=null">frybq1=#{frybq1,jdbcType=VARCHAR},</if>
            <if test="frybq2!=null">frybq2=#{frybq2,jdbcType=VARCHAR},</if>
            <if test="frybq3!=null">frybq3=#{frybq3,jdbcType=VARCHAR},</if>
            <if test="frybq4!=null">frybq4=#{frybq4,jdbcType=VARCHAR},</if>
            <if test="frybq5!=null">frybq5=#{frybq5,jdbcType=VARCHAR},</if>
            <if test="frybq6!=null">frybq6=#{frybq6,jdbcType=VARCHAR},</if>
            <if test="frybq7!=null">frybq7=#{frybq7,jdbcType=VARCHAR},</if>
            <if test="frybq8!=null">frybq8=#{frybq8,jdbcType=VARCHAR},</if>
            <if test="frybq9!=null">frybq9=#{frybq9,jdbcType=VARCHAR},</if>
            <if test="frybq10!=null">frybq10=#{frybq10,jdbcType=VARCHAR},</if>
            <if test="frybq11!=null">frybq11=#{frybq11,jdbcType=VARCHAR},</if>
            <if test="frybq12!=null">frybq12=#{frybq12,jdbcType=VARCHAR},</if>
            <if test="frybq13!=null">frybq13=#{frybq13,jdbcType=VARCHAR},</if>
            <if test="frybq14!=null">frybq14=#{frybq14,jdbcType=VARCHAR},</if>
            <if test="frybq15!=null">frybq15=#{frybq15,jdbcType=VARCHAR},</if>
            <if test="frybq16!=null">frybq16=#{frybq16,jdbcType=VARCHAR},</if>
            <if test="frybq17!=null">frybq17=#{frybq17,jdbcType=VARCHAR},</if>
            <if test="frybq18!=null">frybq18=#{frybq18,jdbcType=VARCHAR},</if>
            <if test="frybq19!=null">frybq19=#{frybq19,jdbcType=VARCHAR},</if>
            <if test="frybq20!=null">frybq20=#{frybq20,jdbcType=VARCHAR},</if>
            <if test="frybq21!=null">frybq21=#{frybq21,jdbcType=VARCHAR},</if>
            <if test="frybq22!=null">frybq22=#{frybq22,jdbcType=VARCHAR},</if>
            <if test="fmz1!=null">fmz1=#{fmz1,jdbcType=VARCHAR},</if>
            <if test="fmz2!=null">fmz2=#{fmz2,jdbcType=VARCHAR},</if>
            <if test="fmz3!=null">fmz3=#{fmz3,jdbcType=VARCHAR},</if>
            <if test="fmz4!=null">fmz4=#{fmz4,jdbcType=VARCHAR},</if>
            <if test="fmz5!=null">fmz5=#{fmz5,jdbcType=VARCHAR},</if>
            <if test="fmz6!=null">fmz6=#{fmz6,jdbcType=VARCHAR},</if>
            <if test="fmz7!=null">fmz7=#{fmz7,jdbcType=VARCHAR},</if>
            <if test="fmz8!=null">fmz8=#{fmz8,jdbcType=VARCHAR},</if>
            <if test="fys1!=null">fys1=#{fys1,jdbcType=VARCHAR},</if>
            <if test="fys2!=null">fys2=#{fys2,jdbcType=VARCHAR},</if>
            <if test="fys3!=null">fys3=#{fys3,jdbcType=VARCHAR},</if>
            <if test="fys4!=null">fys4=#{fys4,jdbcType=VARCHAR},</if>
            <if test="fys5!=null">fys5=#{fys5,jdbcType=VARCHAR},</if>
            <if test="fys6!=null">fys6=#{fys6,jdbcType=VARCHAR},</if>
            <if test="fys7!=null">fys7=#{fys7,jdbcType=VARCHAR},</if>
            <if test="fys8!=null">fys8=#{fys8,jdbcType=VARCHAR},</if>
            <if test="fmzys1!=null">fmzys1=#{fmzys1,jdbcType=VARCHAR},</if>
            <if test="fmzys2!=null">fmzys2=#{fmzys2,jdbcType=VARCHAR},</if>
            <if test="fmzys3!=null">fmzys3=#{fmzys3,jdbcType=VARCHAR},</if>
            <if test="fmzys4!=null">fmzys4=#{fmzys4,jdbcType=VARCHAR},</if>
            <if test="fmzys5!=null">fmzys5=#{fmzys5,jdbcType=VARCHAR},</if>
            <if test="fmzys6!=null">fmzys6=#{fmzys6,jdbcType=VARCHAR},</if>
            <if test="fmzys7!=null">fmzys7=#{fmzys7,jdbcType=VARCHAR},</if>
            <if test="fmzys8!=null">fmzys8=#{fmzys8,jdbcType=VARCHAR},</if>
            <if test="fyhqk1!=null">fyhqk1=#{fyhqk1,jdbcType=VARCHAR},</if>
            <if test="fyhqk2!=null">fyhqk2=#{fyhqk2,jdbcType=VARCHAR},</if>
            <if test="fyhqk3!=null">fyhqk3=#{fyhqk3,jdbcType=VARCHAR},</if>
            <if test="fyhqk4!=null">fyhqk4=#{fyhqk4,jdbcType=VARCHAR},</if>
            <if test="fyhqk5!=null">fyhqk5=#{fyhqk5,jdbcType=VARCHAR},</if>
            <if test="fyhqk6!=null">fyhqk6=#{fyhqk6,jdbcType=VARCHAR},</if>
            <if test="fyhqk7!=null">fyhqk7=#{fyhqk7,jdbcType=VARCHAR},</if>
            <if test="fyhqk8!=null">fyhqk8=#{fyhqk8,jdbcType=VARCHAR},</if>
            <if test="fssyz!=null">fssyz=#{fssyz,jdbcType=VARCHAR},</if>
            <if test="fssyz2!=null">fssyz2=#{fssyz2,jdbcType=VARCHAR},</if>
            <if test="fssyz3!=null">fssyz3=#{fssyz3,jdbcType=VARCHAR},</if>
            <if test="fssyz4!=null">fssyz4=#{fssyz4,jdbcType=VARCHAR},</if>
            <if test="fssyz5!=null">fssyz5=#{fssyz5,jdbcType=VARCHAR},</if>
            <if test="fssyz6!=null">fssyz6=#{fssyz6,jdbcType=VARCHAR},</if>
            <if test="fssyz7!=null">fssyz7=#{fssyz7,jdbcType=VARCHAR},</if>
            <if test="fssyz8!=null">fssyz8=#{fssyz8,jdbcType=VARCHAR},</if>
            <if test="fssez!=null">fssez=#{fssez,jdbcType=VARCHAR},</if>
            <if test="fssez2!=null">fssez2=#{fssez2,jdbcType=VARCHAR},</if>
            <if test="fssez3!=null">fssez3=#{fssez3,jdbcType=VARCHAR},</if>
            <if test="fssez4!=null">fssez4=#{fssez4,jdbcType=VARCHAR},</if>
            <if test="fssez5!=null">fssez5=#{fssez5,jdbcType=VARCHAR},</if>
            <if test="fssez6!=null">fssez6=#{fssez6,jdbcType=VARCHAR},</if>
            <if test="fssez7!=null">fssez7=#{fssez7,jdbcType=VARCHAR},</if>
            <if test="fssez8!=null">fssez8=#{fssez8,jdbcType=VARCHAR},</if>
            <if test="fssrq1!=null">fssrq1=#{fssrq1,jdbcType=VARCHAR},</if>
            <if test="fssrq2!=null">fssrq2=#{fssrq2,jdbcType=VARCHAR},</if>
            <if test="fssrq3!=null">fssrq3=#{fssrq3,jdbcType=VARCHAR},</if>
            <if test="fssrq4!=null">fssrq4=#{fssrq4,jdbcType=VARCHAR},</if>
            <if test="fssrq5!=null">fssrq5=#{fssrq5,jdbcType=VARCHAR},</if>
            <if test="fssrq6!=null">fssrq6=#{fssrq6,jdbcType=VARCHAR},</if>
            <if test="fssrq7!=null">fssrq7=#{fssrq7,jdbcType=VARCHAR},</if>
            <if test="fssrq8!=null">fssrq8=#{fssrq8,jdbcType=VARCHAR},</if>
            <if test="fssbm1!=null">fssbm1=#{fssbm1,jdbcType=VARCHAR},</if>
            <if test="fssmc1!=null">fssmc1=#{fssmc1,jdbcType=VARCHAR},</if>
            <if test="fssbm2!=null">fssbm2=#{fssbm2,jdbcType=VARCHAR},</if>
            <if test="fssmc2!=null">fssmc2=#{fssmc2,jdbcType=VARCHAR},</if>
            <if test="fssbm3!=null">fssbm3=#{fssbm3,jdbcType=VARCHAR},</if>
            <if test="fssmc3!=null">fssmc3=#{fssmc3,jdbcType=VARCHAR},</if>
            <if test="fssbm4!=null">fssbm4=#{fssbm4,jdbcType=VARCHAR},</if>
            <if test="fssmc4!=null">fssmc4=#{fssmc4,jdbcType=VARCHAR},</if>
            <if test="fssbm5!=null">fssbm5=#{fssbm5,jdbcType=VARCHAR},</if>
            <if test="fssmc5!=null">fssmc5=#{fssmc5,jdbcType=VARCHAR},</if>
            <if test="fssbm6!=null">fssbm6=#{fssbm6,jdbcType=VARCHAR},</if>
            <if test="fssmc6!=null">fssmc6=#{fssmc6,jdbcType=VARCHAR},</if>
            <if test="fssbm7!=null">fssbm7=#{fssbm7,jdbcType=VARCHAR},</if>
            <if test="fssmc7!=null">fssmc7=#{fssmc7,jdbcType=VARCHAR},</if>
            <if test="fssbm8!=null">fssbm8=#{fssbm8,jdbcType=VARCHAR},</if>
            <if test="fssmc8!=null">fssmc8=#{fssmc8,jdbcType=VARCHAR},</if>
            <if test="refldeptdept!=null">refldeptdept=#{refldeptdept,jdbcType=VARCHAR},</if>
            <if test="dscgdeptcode!=null">dscgdeptcode=#{dscgdeptcode,jdbcType=VARCHAR},</if>
            <if test="dscgdeptname!=null">dscgdeptname=#{dscgdeptname,jdbcType=VARCHAR},</if>
            <if test="sczt!=null">sczt=#{sczt,jdbcType=VARCHAR},</if>
            <if test="shzt!=null">shzt=#{shzt,jdbcType=VARCHAR},</if>
            <if test="scsj!=null">scsj=#{scsj,jdbcType=TIMESTAMP},</if>
            <if test="shsj!=null">shsj=#{shsj,jdbcType=TIMESTAMP},</if>
            <if test="sclsh!=null">sclsh=#{sclsh,jdbcType=VARCHAR},</if>
            <if test="tjhlts!=null">tjhlts=#{tjhlts,jdbcType=VARCHAR},</if>
            <if test="yjhlts!=null">yjhlts=#{yjhlts,jdbcType=VARCHAR},</if>
            <if test="ejhlts!=null">ejhlts=#{ejhlts,jdbcType=VARCHAR},</if>
            <if test="sjhlts!=null">sjhlts=#{sjhlts,jdbcType=VARCHAR},</if>
            <if test="brxm!=null">brxm=#{brxm,jdbcType=VARCHAR},</if>
            <if test="fys1code!=null">fys1code=#{fys1code,jdbcType=VARCHAR},</if>
            <if test="fys2code!=null">fys2code=#{fys2code,jdbcType=VARCHAR},</if>
            <if test="fys3code!=null">fys3code=#{fys3code,jdbcType=VARCHAR},</if>
            <if test="fys4code!=null">fys4code=#{fys4code,jdbcType=VARCHAR},</if>
            <if test="fys5code!=null">fys5code=#{fys5code,jdbcType=VARCHAR},</if>
            <if test="fys6code!=null">fys6code=#{fys6code,jdbcType=VARCHAR},</if>
            <if test="fys7code!=null">fys7code=#{fys7code,jdbcType=VARCHAR},</if>
            <if test="fys8code!=null">fys8code=#{fys8code,jdbcType=VARCHAR},</if>
            <if test="fmzys1code!=null">fmzys1code=#{fmzys1code,jdbcType=VARCHAR},</if>
            <if test="fmzys2code!=null">fmzys2code=#{fmzys2code,jdbcType=VARCHAR},</if>
            <if test="fmzys3code!=null">fmzys3code=#{fmzys3code,jdbcType=VARCHAR},</if>
            <if test="fmzys4code!=null">fmzys4code=#{fmzys4code,jdbcType=VARCHAR},</if>
            <if test="fmzys5code!=null">fmzys5code=#{fmzys5code,jdbcType=VARCHAR},</if>
            <if test="fmzys6code!=null">fmzys6code=#{fmzys6code,jdbcType=VARCHAR},</if>
            <if test="fmzys7code!=null">fmzys7code=#{fmzys7code,jdbcType=VARCHAR},</if>
            <if test="fmzys8code!=null">fmzys8code=#{fmzys8code,jdbcType=VARCHAR},</if>
            <if test="ventuseddurat!=null">ventuseddurat=#{ventuseddurat,jdbcType=VARCHAR},</if>
            <if test="ventuseddurax!=null">ventuseddurax=#{ventuseddurax,jdbcType=VARCHAR},</if>
            <if test="ventusedduraf!=null">ventusedduraf=#{ventusedduraf,jdbcType=VARCHAR},</if>
            <if test="zyysxm!=null">zyysxm=#{zyysxm,jdbcType=VARCHAR},</if>
            <if test="zrhsxm!=null">zrhsxm=#{zrhsxm,jdbcType=VARCHAR},</if>
            <if test="setlenddate!=null">setlenddate=#{setlenddate,jdbcType=VARCHAR},</if>
            <if test="setlbegndate!=null">setlbegndate=#{setlbegndate,jdbcType=VARCHAR},</if>
        </set>
        where zyh=#{zyh,jdbcType=VARCHAR}
    </update>

    <insert id="insertYbIcuxx" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_jbxx_IcuModel">
        insert into gssldyb_ybjsdsc_icuinfo(zyh, scscutdwardtype, scscutdinpooltime, scscutdexittime, scscutdsumdura)
        values (#{zyh,jdbcType=VARCHAR}, #{iculx,jdbcType=VARCHAR},
                to_char(#{inpooltime,jdbcType=TIMESTAMP}, 'yyyy-mm-dd hh24:mi:ss'),
                to_char(#{exittime,jdbcType=TIMESTAMP}, 'yyyy-mm-dd hh24:mi:ss'), #{scscutdsumdura,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteYbIcuxx" parameterType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel">
        delete
        from gssldyb_ybjsdsc_icuinfo
        where zyh = #{zyh,jdbcType=VARCHAR}
    </delete>

    <select id="queryYbBaSc" parameterType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel"
            resultType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel">
        select zyh,jzid,admtime,dscgtime,sczt,shzt,brxm from gssldyb_ybjsdsc
        <where>
            <if test="parm != null and parm!=''">
                and (zyh like '%'||#{parm,jdbcType=VARCHAR}||'%' or jzid like '%'||#{parm,jdbcType=VARCHAR}||'%' )
            </if>
            <if test="beginrq != null">
                and (to_date(dscgtime,'yyyy-mm-dd hh24:mi:ss') &gt;= #{beginrq,jdbcType=TIMESTAMP})
            </if>
            <if test="endrq != null">
                and (to_date(dscgtime,'yyyy-mm-dd hh24:mi:ss') &lt;= #{endrq,jdbcType=TIMESTAMP})
            </if>
            <if test="shzt != null and shzt!=''">
                and shzt=#{shzt,jdbcType=VARCHAR}
            </if>
            <if test="sczt != null and endrq != null">
                and sczt=#{sczt,jdbcType=VARCHAR}
            </if>
        </where>
        order by dscgtime desc
    </select>


    <select id="queryYbBaScZyhMx" parameterType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel"
            resultType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel">
        select *
        from gssldyb_ybjsdsc
        where zyh = #{zyh,jdbcType=VARCHAR}
    </select>

    <select id="queryYbBaScZyhIcuMx" parameterType="com.supx.csp.api.bagl.sygl.pojo.BaSyScxxModel"
            resultType="com.supx.csp.api.bagl.sygl.pojo.Bagl_jbxx_IcuModel">
        select zyh, scscutdsumdura, SCSCUTDWARDTYPE iculx, SCSCUTDINPOOLTIME inpooltime, SCSCUTDEXITTIME exittime
        from gssldyb_ybjsdsc_icuinfo
        where zyh = #{zyh,jdbcType=VARCHAR}
    </select>

    <select id="getRyYbdm" resultType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_RybmModel"
            parameterType="com.supx.csp.api.xtwh.ksry.pojo.Gyb_RybmModel">
        select *
        from gyb_rybm
        where ryxm = #{ryxm,jdbcType=VARCHAR}
          and tybz = '0'
    </select>

</mapper>
