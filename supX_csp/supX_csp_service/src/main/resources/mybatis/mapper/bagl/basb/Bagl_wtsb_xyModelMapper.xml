<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.supx.csp.bagl.basb.dao.New1Bagl_wtsb_xyModelMapper" >
	 <!-- 病案上报需要展示的数据集合 -->
   <resultMap id="BaseResultMap" type="com.supx.csp.api.bagl.basb.pojo.BaglBasbMsgModel">
	   <id column="ZYH" property="zyh" jdbcType="VARCHAR" />
	   <id column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
	   <collection property="fyxxModel" column="zyh" ofType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_fyxxModel">
	    <id column="ZYH" property="zyh" jdbcType="VARCHAR" />
	    <id column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
	    <result column="ZFY" property="zfy" jdbcType="DECIMAL" />
	    <result column="ZFJE" property="zfje" jdbcType="DECIMAL" />
	    <result column="YBYLFWF" property="ybylfwf" jdbcType="DECIMAL" />
	    <result column="ZYBZLZF" property="zybzlzf" jdbcType="DECIMAL" />
	    <result column="ZYBZLZHZF" property="zybzlzhzf" jdbcType="DECIMAL" />
	    <result column="YBZLCZF" property="ybzlczf" jdbcType="DECIMAL" />
	    <result column="HLF" property="hlf" jdbcType="DECIMAL" />
	    <result column="QTFY" property="qtfy" jdbcType="DECIMAL" />
	    <result column="BLZDF" property="blzdf" jdbcType="DECIMAL" />
	    <result column="SYSZDF" property="syszdf" jdbcType="DECIMAL" />
	    <result column="YXXZDF" property="yxxzdf" jdbcType="DECIMAL" />
	    <result column="LCZDXMF" property="lczdxmf" jdbcType="DECIMAL" />
	    <result column="FSSZLXMF" property="fsszlxmf" jdbcType="DECIMAL" />
	    <result column="LCWLZLF" property="lcwlzlf" jdbcType="DECIMAL" />
	    <result column="SSZLF" property="sszlf" jdbcType="DECIMAL" />
	    <result column="MZF" property="mzf" jdbcType="DECIMAL" />
	    <result column="SSF" property="ssf" jdbcType="DECIMAL" />
	    <result column="KFF" property="kff" jdbcType="DECIMAL" />
	    <result column="ZYZD" property="zyzd" jdbcType="DECIMAL" />
	    <result column="ZYZL" property="zyzl" jdbcType="DECIMAL" />
	    <result column="ZYWZ" property="zywz" jdbcType="DECIMAL" />
	    <result column="ZYGS" property="zygs" jdbcType="DECIMAL" />
	    <result column="ZCYJF" property="zcyjf" jdbcType="DECIMAL" />
	    <result column="ZYTLZL" property="zytlzl" jdbcType="DECIMAL" />
	    <result column="ZYGCZL" property="zygczl" jdbcType="DECIMAL" />
	    <result column="ZYTSZL" property="zytszl" jdbcType="DECIMAL" />
	    <result column="ZYQT" property="zyqt" jdbcType="DECIMAL" />
	    <result column="ZYTSTPJG" property="zytstpjg" jdbcType="DECIMAL" />
	    <result column="BZSS" property="bzss" jdbcType="DECIMAL" />
	    <result column="XYF" property="xyf" jdbcType="DECIMAL" />
	    <result column="KJYWFY" property="kjywfy" jdbcType="DECIMAL" />
	    <result column="CYF" property="cyf" jdbcType="DECIMAL" />
	    <result column="YLJGZYZJF" property="yljgzyzjf" jdbcType="DECIMAL" />
	    <result column="ZYF" property="zyf" jdbcType="DECIMAL" />
	    <result column="XF" property="xf" jdbcType="DECIMAL" />
	    <result column="BDBLZPF" property="bdblzpf" jdbcType="DECIMAL" />
	    <result column="QDBLZPF" property="qdblzpf" jdbcType="DECIMAL" />
	    <result column="NXYZLZPF" property="nxyzlzpf" jdbcType="DECIMAL" />
	    <result column="XBYZLZPF" property="xbyzlzpf" jdbcType="DECIMAL" />
	    <result column="JCCLF" property="jcclf" jdbcType="DECIMAL" />
	    <result column="ZLCLF" property="zlclf" jdbcType="DECIMAL" />
	    <result column="SSCLF" property="ssclf" jdbcType="DECIMAL" />
	    <result column="QTF" property="qtf" jdbcType="DECIMAL" />
	    <result column="QTZF" property="qtzf" jdbcType="DECIMAL" />
	   </collection>
	   <collection property="ssxxModel" column="zyh" ofType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_ssxxModel">
		<id column="ZYH" property="zyh" jdbcType="VARCHAR" />
	    <id column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
	    <result column="YLQKYFKJYW" property="ylqkyfkjyw" jdbcType="VARCHAR" />
	    <result column="YLQKYFKJYW_CXSJ" property="ylqkyfkjywCxsj" jdbcType="VARCHAR" />
	    <result column="YLQKYFKJYW_LHYY" property="ylqkyfkjywLhyy" jdbcType="VARCHAR" />
	   <result column="SFJZSS1" property="sfjzss1" jdbcType="VARCHAR" />
	   <result column="SFJZSS2" property="sfjzss2" jdbcType="VARCHAR" />
	   <result column="SFJZSS3" property="sfjzss3" jdbcType="VARCHAR" />
	   <result column="SFJZSS4" property="sfjzss4" jdbcType="VARCHAR" />
	   <result column="SFJZSS5" property="sfjzss5" jdbcType="VARCHAR" />
	   <result column="SFJZSS6" property="sfjzss6" jdbcType="VARCHAR" />
	   <result column="SFJZSS7" property="sfjzss7" jdbcType="VARCHAR" />
	   <result column="SFJZSS8" property="sfjzss8" jdbcType="VARCHAR" />
	    <result column="SSBM1" property="ssbm1" jdbcType="VARCHAR" />
	    <result column="SSMC1" property="ssmc1" jdbcType="VARCHAR" />
	    <result column="SSRQ1" property="ssrq1" jdbcType="TIMESTAMP" />
	    <result column="SSJB1" property="ssjb1" jdbcType="VARCHAR" />
	    <result column="SSYS1" property="ssys1" jdbcType="VARCHAR" />
	    <result column="SSYZ1" property="ssyz1" jdbcType="VARCHAR" />
	    <result column="SSEZ1" property="ssez1" jdbcType="VARCHAR" />
	    <result column="QKDJ1" property="qkdj1" jdbcType="VARCHAR" />
	    <result column="MZFS1" property="mzfs1" jdbcType="VARCHAR" />
	    <result column="MZYS1" property="mzys1" jdbcType="VARCHAR" />
	    <result column="SSBM2" property="ssbm2" jdbcType="VARCHAR" />
	    <result column="SSMC2" property="ssmc2" jdbcType="VARCHAR" />
	    <result column="SSRQ2" property="ssrq2" jdbcType="TIMESTAMP" />
	    <result column="SSJB2" property="ssjb2" jdbcType="VARCHAR" />
	    <result column="SSYS2" property="ssys2" jdbcType="VARCHAR" />
	    <result column="SSYZ2" property="ssyz2" jdbcType="VARCHAR" />
	    <result column="SSEZ2" property="ssez2" jdbcType="VARCHAR" />
	    <result column="QKDJ2" property="qkdj2" jdbcType="VARCHAR" />
	    <result column="MZFS2" property="mzfs2" jdbcType="VARCHAR" />
	    <result column="MZYS2" property="mzys2" jdbcType="VARCHAR" />
	    <result column="SSBM3" property="ssbm3" jdbcType="VARCHAR" />
	    <result column="SSMC3" property="ssmc3" jdbcType="VARCHAR" />
	    <result column="SSRQ3" property="ssrq3" jdbcType="TIMESTAMP" />
	    <result column="SSJB3" property="ssjb3" jdbcType="VARCHAR" />
	    <result column="SSYS3" property="ssys3" jdbcType="VARCHAR" />
	    <result column="SSYZ3" property="ssyz3" jdbcType="VARCHAR" />
	    <result column="SSEZ3" property="ssez3" jdbcType="VARCHAR" />
	    <result column="QKDJ3" property="qkdj3" jdbcType="VARCHAR" />
	    <result column="MZFS3" property="mzfs3" jdbcType="VARCHAR" />
	    <result column="MZYS3" property="mzys3" jdbcType="VARCHAR" />
	    <result column="SSBM4" property="ssbm4" jdbcType="VARCHAR" />
	    <result column="SSMC4" property="ssmc4" jdbcType="VARCHAR" />
	    <result column="SSRQ4" property="ssrq4" jdbcType="TIMESTAMP" />
	    <result column="SSJB4" property="ssjb4" jdbcType="VARCHAR" />
	    <result column="SSYS4" property="ssys4" jdbcType="VARCHAR" />
	    <result column="SSYZ4" property="ssyz4" jdbcType="VARCHAR" />
	    <result column="SSEZ4" property="ssez4" jdbcType="VARCHAR" />
	    <result column="QKDJ4" property="qkdj4" jdbcType="VARCHAR" />
	    <result column="MZFS4" property="mzfs4" jdbcType="VARCHAR" />
	    <result column="MZYS4" property="mzys4" jdbcType="VARCHAR" />
	    <result column="SSBM5" property="ssbm5" jdbcType="VARCHAR" />
	    <result column="SSMC5" property="ssmc5" jdbcType="VARCHAR" />
	    <result column="SSRQ5" property="ssrq5" jdbcType="TIMESTAMP" />
	    <result column="SSJB5" property="ssjb5" jdbcType="VARCHAR" />
	    <result column="SSYS5" property="ssys5" jdbcType="VARCHAR" />
	    <result column="SSYZ5" property="ssyz5" jdbcType="VARCHAR" />
	    <result column="SSEZ5" property="ssez5" jdbcType="VARCHAR" />
	    <result column="QKDJ5" property="qkdj5" jdbcType="VARCHAR" />
	    <result column="MZFS5" property="mzfs5" jdbcType="VARCHAR" />
	    <result column="MZYS5" property="mzys5" jdbcType="VARCHAR" />
	    <result column="SSBM6" property="ssbm6" jdbcType="VARCHAR" />
	    <result column="SSMC6" property="ssmc6" jdbcType="VARCHAR" />
	    <result column="SSRQ6" property="ssrq6" jdbcType="TIMESTAMP" />
	    <result column="SSJB6" property="ssjb6" jdbcType="VARCHAR" />
	    <result column="SSYS6" property="ssys6" jdbcType="VARCHAR" />
	    <result column="SSYZ6" property="ssyz6" jdbcType="VARCHAR" />
	    <result column="SSEZ6" property="ssez6" jdbcType="VARCHAR" />
	    <result column="QKDJ6" property="qkdj6" jdbcType="VARCHAR" />
	    <result column="MZFS6" property="mzfs6" jdbcType="VARCHAR" />
	    <result column="MZYS6" property="mzys6" jdbcType="VARCHAR" />
	    <result column="SSBM7" property="ssbm7" jdbcType="VARCHAR" />
	    <result column="SSMC7" property="ssmc7" jdbcType="VARCHAR" />
	    <result column="SSRQ7" property="ssrq7" jdbcType="TIMESTAMP" />
	    <result column="SSJB7" property="ssjb7" jdbcType="VARCHAR" />
	    <result column="SSYS7" property="ssys7" jdbcType="VARCHAR" />
	    <result column="SSYZ7" property="ssyz7" jdbcType="VARCHAR" />
	    <result column="SSEZ7" property="ssez7" jdbcType="VARCHAR" />
	    <result column="QKDJ7" property="qkdj7" jdbcType="VARCHAR" />
	    <result column="MZFS7" property="mzfs7" jdbcType="VARCHAR" />
	    <result column="MZYS7" property="mzys7" jdbcType="VARCHAR" />
	    <result column="SSBM8" property="ssbm8" jdbcType="VARCHAR" />
	    <result column="SSMC8" property="ssmc8" jdbcType="VARCHAR" />
	    <result column="SSRQ8" property="ssrq8" jdbcType="TIMESTAMP" />
	    <result column="SSJB8" property="ssjb8" jdbcType="VARCHAR" />
	    <result column="SSYS8" property="ssys8" jdbcType="VARCHAR" />
	    <result column="SSYZ8" property="ssyz8" jdbcType="VARCHAR" />
	    <result column="SSEZ8" property="ssez8" jdbcType="VARCHAR" />
	    <result column="QKDJ8" property="qkdj8" jdbcType="VARCHAR" />
	    <result column="MZFS8" property="mzfs8" jdbcType="VARCHAR" />
	    <result column="MZYS8" property="mzys8" jdbcType="VARCHAR" />
	    <result column="ZYQJYYKJYW" property="zyqjyykjyw" jdbcType="VARCHAR" />
	    <result column="KJYW_CXSJ" property="kjywCxsj" jdbcType="VARCHAR" />
	    <result column="KJYW_LHYY" property="kjywLhyy" jdbcType="VARCHAR" />
	    <result column="KJYW_DDDS" property="kjywDdds" jdbcType="VARCHAR" />
	    <result column="SFSSLCLJ" property="sfsslclj" jdbcType="VARCHAR" />
	    <result column="SFWCLCLJ" property="sfwclclj" jdbcType="VARCHAR" />
	    <result column="TCLCLJYY" property="tclcljyy" jdbcType="VARCHAR" />
	    <result column="LCLJSFBY" property="lcljsfby" jdbcType="VARCHAR" />
	    <result column="LCLJBYYY" property="lcljbyyy" jdbcType="VARCHAR" />
	    <result column="SFHZ" property="sfhz" jdbcType="VARCHAR" />
	    <result column="YNHZCS" property="ynhzcs" jdbcType="VARCHAR" />
	    <result column="WYHZCS" property="wyhzcs" jdbcType="VARCHAR" />
	    <result column="QTHZQK" property="qthzqk" jdbcType="VARCHAR" />
	    <result column="JC_CT" property="jcCt" jdbcType="VARCHAR" />
	    <result column="JC_PETCT" property="jcPetct" jdbcType="VARCHAR" />
	    <result column="JC_SYCT" property="jcSyct" jdbcType="VARCHAR" />
	    <result column="JC_XP" property="jcXp" jdbcType="VARCHAR" />
	    <result column="JC_BC" property="jcBc" jdbcType="VARCHAR" />
	    <result column="JC_CSXDT" property="jcCsxdt" jdbcType="VARCHAR" />
	    <result column="JC_MRI" property="jcMri" jdbcType="VARCHAR" />
	    <result column="JC_TWS" property="jcTws" jdbcType="VARCHAR" />
	    <result column="SX_HXB" property="sxHxb" jdbcType="VARCHAR" />
	    <result column="SX_XXB" property="sxXxb" jdbcType="VARCHAR" />
	    <result column="SX_XJ" property="sxXj" jdbcType="VARCHAR" />
	    <result column="SX_QX" property="sxQx" jdbcType="VARCHAR" />
	    <result column="SX_ZTX" property="sxZtx" jdbcType="VARCHAR" />
	    <result column="SX_BDB" property="sxBdb" jdbcType="VARCHAR" />
	    <result column="SX_QT" property="sxQt" jdbcType="VARCHAR" />
	    <result column="SXFY" property="sxfy" jdbcType="VARCHAR" />
	    <result column="ZZYJH_31" property="zzyjh31" jdbcType="VARCHAR" />
	    <result column="ZZYJH_31_MD" property="zzyjh31Md" jdbcType="VARCHAR" />
	    <result column="LNSS_RYQ_T" property="lnssRyqT" jdbcType="VARCHAR" />
	    <result column="LNSS_RYQ_XS" property="lnssRyqXs" jdbcType="VARCHAR" />
	    <result column="LNSS_RYQ_FZ" property="lnssRyqFz" jdbcType="VARCHAR" />
	    <result column="LNSS_RYH_T" property="lnssRyhT" jdbcType="VARCHAR" />
	    <result column="LNSS_RYH_XS" property="lnssRyhXs" jdbcType="VARCHAR" />
	    <result column="LNSS_RYH_FZ" property="lnssRyhFz" jdbcType="VARCHAR" />
	    <result column="SFYTYBZRY" property="sfytybzry" jdbcType="VARCHAR" />
	    <result column="YSCCYRQJGTS" property="ysccyrqjgts" jdbcType="CHAR" />
	    <result column="KJYWSYQK" property="kjywsyqk" jdbcType="VARCHAR" />
	    <result column="KJYWMC1" property="kjywmc1" jdbcType="VARCHAR" />
	    <result column="KJYWMC2" property="kjywmc2" jdbcType="VARCHAR" />
	    <result column="KJYWMC3" property="kjywmc3" jdbcType="VARCHAR" />
	    <result column="KJYWMC4" property="kjywmc4" jdbcType="VARCHAR" />
	    <result column="KJYWMC5" property="kjywmc5" jdbcType="VARCHAR" />
	    <result column="KJYWMC6" property="kjywmc6" jdbcType="VARCHAR" />
	    <result column="KJYWMD1" property="kjywmd1" jdbcType="VARCHAR" />
	    <result column="KJYWMD2" property="kjywmd2" jdbcType="VARCHAR" />
	    <result column="KJYWMD3" property="kjywmd3" jdbcType="VARCHAR" />
	    <result column="KJYWMD4" property="kjywmd4" jdbcType="VARCHAR" />
	    <result column="KJYWMD5" property="kjywmd5" jdbcType="VARCHAR" />
	    <result column="KJYWMD6" property="kjywmd6" jdbcType="VARCHAR" />
	    <result column="LZNZZY" property="lznzzy" jdbcType="VARCHAR" />
	    <result column="SSHYQZSS" property="sshyqzss" jdbcType="VARCHAR" />
	    <result column="YFKSS_30M_2H" property="yfkss30m2h" jdbcType="VARCHAR" />
	    <result column="YFKSS_24H" property="yfkss24h" jdbcType="VARCHAR" />
	    <result column="QKYHLB1" property="qkyhlb1" jdbcType="VARCHAR" />
	    <result column="QKYHLB2" property="qkyhlb2" jdbcType="VARCHAR" />
	    <result column="QKYHLB3" property="qkyhlb3" jdbcType="VARCHAR" />
	    <result column="QKYHLB4" property="qkyhlb4" jdbcType="VARCHAR" />
	    <result column="QKYHLB5" property="qkyhlb5" jdbcType="VARCHAR" />
	    <result column="QKYHLB6" property="qkyhlb6" jdbcType="VARCHAR" />
	    <result column="QKYHLB7" property="qkyhlb7" jdbcType="VARCHAR" />
	    <result column="QKYHLB8" property="qkyhlb8" jdbcType="VARCHAR" />
	    <result column="ZYYWDDHZCJSHCD" property="zyywddhzcjshcd" jdbcType="VARCHAR" />
	    <result column="DDHZCYY" property="ddhzcyy" jdbcType="VARCHAR" />
	   </collection>
	   <collection property="zdxxModel" column="zyh" ofType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_zdxxModel">
		<id column="ZYH" property="zyh" jdbcType="VARCHAR" />
	    <id column="YLJGBM" property="yljgbm" jdbcType="VARCHAR" />
	    <result column="RYRQ" property="ryrq" jdbcType="TIMESTAMP" />
	    <result column="RYKS" property="ryks" jdbcType="VARCHAR" />
	    <result column="RYBF" property="rybf" jdbcType="VARCHAR" />
	    <result column="ZKKB" property="zkkb" jdbcType="VARCHAR" />
	    <result column="CYRQ" property="cyrq" jdbcType="TIMESTAMP" />
	    <result column="CYKS" property="cyks" jdbcType="VARCHAR" />
	    <result column="CYBF" property="cybf" jdbcType="VARCHAR" />
	    <result column="MZZDMC" property="mzzdmc" jdbcType="VARCHAR" />
	    <result column="MZZDBM" property="mzzdbm" jdbcType="VARCHAR" />
	    <result column="MZZDMC_ZY" property="mzzdmcZy" jdbcType="VARCHAR" />
	    <result column="MZZDBM_ZY" property="mzzdbmZy" jdbcType="VARCHAR" />
	    <result column="ZLLB" property="zllb" jdbcType="VARCHAR" />
	    <result column="SSLCLJ" property="sslclj" jdbcType="VARCHAR" />
	    <result column="SYZYZJ" property="syzyzj" jdbcType="VARCHAR" />
	    <result column="SYZYZLSB" property="syzyzlsb" jdbcType="VARCHAR" />
	    <result column="SYZYZLJS" property="syzyzljs" jdbcType="VARCHAR" />
	    <result column="BZSH" property="bzsh" jdbcType="VARCHAR" />
	    <result column="RYZDMC" property="ryzdmc" jdbcType="VARCHAR" />
	    <result column="RYZDBM" property="ryzdbm" jdbcType="VARCHAR" />
	    <result column="QZRQ" property="qzrq" jdbcType="TIMESTAMP" />
	    <result column="SFGZBWHBZ" property="sfgzbwhbz" jdbcType="VARCHAR" />
	    <result column="SFNSBL" property="sfnsbl" jdbcType="VARCHAR" />
	    <result column="LYFS" property="lyfs" jdbcType="VARCHAR" />
	    <result column="ZYYLJGMC" property="zyyljgmc" jdbcType="VARCHAR" />
	    <result column="ZSQJGMC" property="zsqjgmc" jdbcType="VARCHAR" />
	    <result column="ZY_CYZBMC" property="zyCyzbmc" jdbcType="VARCHAR" />
	    <result column="ZY_CYZBBM" property="zyCyzbbm" jdbcType="VARCHAR" />
	    <result column="ZY_ZB_RYBQ" property="zyZbRybq" jdbcType="VARCHAR" />
	    <result column="ZY_ZB_CYQK" property="zyZbCyqk" jdbcType="VARCHAR" />
	    <result column="ZY_CYZZMC" property="zyCyzzmc" jdbcType="VARCHAR" />
	    <result column="ZY_CYZZBM" property="zyCyzzbm" jdbcType="VARCHAR" />
	    <result column="ZY_ZZ_RYBQ" property="zyZzRybq" jdbcType="VARCHAR" />
	    <result column="ZY_ZZ_CYQK" property="zyZzCyqk" jdbcType="VARCHAR" />
	    <result column="CYZYZDMC" property="cyzyzdmc" jdbcType="VARCHAR" />
	    <result column="CYZYZD_RYBQ" property="cyzyzdRybq" jdbcType="VARCHAR" />
	    <result column="CYZYZDBM" property="cyzyzdbm" jdbcType="VARCHAR" />
	    <result column="CYZYZD_CYQK" property="cyzyzdCyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC1" property="cyqtzdmc1" jdbcType="VARCHAR" />
	    <result column="CYQTZD1_RYBQ" property="cyqtzd1Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM1" property="cyqtzdbm1" jdbcType="VARCHAR" />
	    <result column="CYQTZD1_CYQK" property="cyqtzd1Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC2" property="cyqtzdmc2" jdbcType="VARCHAR" />
	    <result column="CYQTZD2_RYBQ" property="cyqtzd2Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM2" property="cyqtzdbm2" jdbcType="VARCHAR" />
	    <result column="CYQTZD2_CYQK" property="cyqtzd2Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC3" property="cyqtzdmc3" jdbcType="VARCHAR" />
	    <result column="CYQTZD3_RYBQ" property="cyqtzd3Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM3" property="cyqtzdbm3" jdbcType="VARCHAR" />
	    <result column="CYQTZD3_CYQK" property="cyqtzd3Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC4" property="cyqtzdmc4" jdbcType="VARCHAR" />
	    <result column="CYQTZD4_RYBQ" property="cyqtzd4Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM4" property="cyqtzdbm4" jdbcType="VARCHAR" />
	    <result column="CYQTZD4_CYQK" property="cyqtzd4Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC5" property="cyqtzdmc5" jdbcType="VARCHAR" />
	    <result column="CYQTZD5_RYBQ" property="cyqtzd5Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM5" property="cyqtzdbm5" jdbcType="VARCHAR" />
	    <result column="CYQTZD5_CYQK" property="cyqtzd5Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC6" property="cyqtzdmc6" jdbcType="VARCHAR" />
	    <result column="CYQTZD6_RYBQ" property="cyqtzd6Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM6" property="cyqtzdbm6" jdbcType="VARCHAR" />
	    <result column="CYQTZD6_CYQK" property="cyqtzd6Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC7" property="cyqtzdmc7" jdbcType="VARCHAR" />
	    <result column="CYQTZD7_RYBQ" property="cyqtzd7Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM7" property="cyqtzdbm7" jdbcType="VARCHAR" />
	    <result column="CYQTZD7_CYQK" property="cyqtzd7Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC8" property="cyqtzdmc8" jdbcType="VARCHAR" />
	    <result column="CYQTZD8_RYBQ" property="cyqtzd8Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM8" property="cyqtzdbm8" jdbcType="VARCHAR" />
	    <result column="CYQTZD8_CYQK" property="cyqtzd8Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC9" property="cyqtzdmc9" jdbcType="VARCHAR" />
	    <result column="CYQTZD9_RYBQ" property="cyqtzd9Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM9" property="cyqtzdbm9" jdbcType="VARCHAR" />
	    <result column="CYQTZD9_CYQK" property="cyqtzd9Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC10" property="cyqtzdmc10" jdbcType="VARCHAR" />
	    <result column="CYQTZD10_RYBQ" property="cyqtzd10Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM10" property="cyqtzdbm10" jdbcType="VARCHAR" />
	    <result column="CYQTZD10_CYQK" property="cyqtzd10Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC11" property="cyqtzdmc11" jdbcType="VARCHAR" />
	    <result column="CYQTZD11_RYBQ" property="cyqtzd11Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM11" property="cyqtzdbm11" jdbcType="VARCHAR" />
	    <result column="CYQTZD11_CYQK" property="cyqtzd11Cyqk" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC12" property="cyqtzdmc12" jdbcType="VARCHAR" />
	    <result column="CYQTZD12_RYBQ" property="cyqtzd12Rybq" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM12" property="cyqtzdbm12" jdbcType="VARCHAR" />
	    <result column="CYQTZD12_CYQK" property="cyqtzd12Cyqk" jdbcType="VARCHAR" />
	    <result column="SSZDWBYYMC" property="sszdwbyymc" jdbcType="VARCHAR" />
	    <result column="SSZDWBYYBM" property="sszdwbyybm" jdbcType="VARCHAR" />
	    <result column="BLZDMC" property="blzdmc" jdbcType="VARCHAR" />
	    <result column="BLZDBM" property="blzdbm" jdbcType="VARCHAR" />
	    <result column="BLZD_BLH" property="blzdBlh" jdbcType="VARCHAR" />
	    <result column="ZLXTXBM" property="zlxtxbm" jdbcType="VARCHAR" />
	    <result column="ZLXTXMC" property="zlxtxmc" jdbcType="VARCHAR" />
	    <result column="SFYWGM" property="sfywgm" jdbcType="VARCHAR" />
	    <result column="YWGMMC" property="ywgmmc" jdbcType="VARCHAR" />
	    <result column="SWHZSJ" property="swhzsj" jdbcType="VARCHAR" />
	    <result column="XX_ABO" property="xxAbo" jdbcType="VARCHAR" />
	    <result column="XX_RH" property="xxRh" jdbcType="VARCHAR" />
	    <result column="QJCS" property="qjcs" jdbcType="DECIMAL" />
	    <result column="QJCGCS" property="qjcgcs" jdbcType="DECIMAL" />
	    <result column="MZYCYFH" property="mzycyfh" jdbcType="VARCHAR" />
	    <result column="RYYCYFH" property="ryycyfh" jdbcType="VARCHAR" />
	    <result column="SQYSHFH" property="sqyshfh" jdbcType="VARCHAR" />
	    <result column="FSYBLFH" property="fsyblfh" jdbcType="VARCHAR" />
	    <result column="LCYBLFH" property="lcyblfh" jdbcType="VARCHAR" />
	    <result column="SFSZ" property="sfsz" jdbcType="VARCHAR" />
	    <result column="SZQX_Z" property="szqxZ" jdbcType="DECIMAL" />
	    <result column="SZQX_Y" property="szqxY" jdbcType="DECIMAL" />
	    <result column="SZQX_N" property="szqxN" jdbcType="DECIMAL" />
	    <result column="KZR" property="kzr" jdbcType="VARCHAR" />
	    <result column="KZRXM" property="kzrxm" jdbcType="VARCHAR" />
	    <result column="ZRFZRYS" property="zrfzrys" jdbcType="VARCHAR" />
	    <result column="ZRFZRYSXM" property="zrfzrysxm" jdbcType="VARCHAR" />
	    <result column="ZZNYS" property="zznys" jdbcType="VARCHAR" />
	    <result column="ZZNYSXM" property="zznysxm" jdbcType="VARCHAR" />
	    <result column="ZZYS" property="zzys" jdbcType="VARCHAR" />
	    <result column="ZZYSXM" property="zzysxm" jdbcType="VARCHAR" />
	    <result column="ZYYS" property="zyys" jdbcType="VARCHAR" />
	    <result column="ZYYSXM" property="zyysxm" jdbcType="VARCHAR" />
	    <result column="ZRHS" property="zrhs" jdbcType="VARCHAR" />
	    <result column="ZRHSXM" property="zrhsxm" jdbcType="VARCHAR" />
	    <result column="JXYS" property="jxys" jdbcType="VARCHAR" />
	    <result column="SXYS" property="sxys" jdbcType="VARCHAR" />
	    <result column="BMRY" property="bmry" jdbcType="VARCHAR" />
	    <result column="BAZL" property="bazl" jdbcType="VARCHAR" />
	    <result column="ZKYS" property="zkys" jdbcType="VARCHAR" />
	    <result column="ZKYSXM" property="zkysxm" jdbcType="VARCHAR" />
	    <result column="ZKHS" property="zkhs" jdbcType="VARCHAR" />
	    <result column="ZKHSXM" property="zkhsxm" jdbcType="VARCHAR" />
	    <result column="ZKRQ" property="zkrq" jdbcType="TIMESTAMP" />
	    <result column="ZYTS" property="zyts" jdbcType="DECIMAL" />
	    <result column="ZY_CYQTZDBM1" property="zyCyqtzdbm1" jdbcType="VARCHAR" />
	    <result column="ZY_CYQTZDMC1" property="zyCyqtzdmc1" jdbcType="VARCHAR" />
	    <result column="ZY_QT1_RYBQ" property="zyQt1Rybq" jdbcType="VARCHAR" />
	    <result column="ZY_QT1_CYQK" property="zyQt1Cyqk" jdbcType="VARCHAR" />
	    <result column="ZY_CYQTZDBM2" property="zyCyqtzdbm2" jdbcType="VARCHAR" />
	    <result column="ZY_CYQTZDMC2" property="zyCyqtzdmc2" jdbcType="VARCHAR" />
	    <result column="ZY_QT2_RYBQ" property="zyQt2Rybq" jdbcType="VARCHAR" />
	    <result column="ZY_QT2_CYQK" property="zyQt2Cyqk" jdbcType="VARCHAR" />
	    <result column="ZY_CYQTZDBM3" property="zyCyqtzdbm3" jdbcType="VARCHAR" />
	    <result column="ZY_CYQTZDMC3" property="zyCyqtzdmc3" jdbcType="VARCHAR" />
	    <result column="ZY_QT3_RYBQ" property="zyQt3Rybq" jdbcType="VARCHAR" />
	    <result column="ZY_QT3_CYQK" property="zyQt3Cyqk" jdbcType="VARCHAR" />
	    <result column="ZYQJSFXGZQGZ" property="zyqjsfxgzqgz" jdbcType="VARCHAR" />
	    <result column="SFBZSYZCY" property="sfbzsyzcy" jdbcType="VARCHAR" />
	    <result column="RC_MDSC" property="rcMdsc" jdbcType="VARCHAR" />
	    <result column="RC_CX" property="rcCx" jdbcType="VARCHAR" />
	    <result column="RC_CXL" property="rcCxl" jdbcType="VARCHAR" />
	    <result column="XSEJBSC_CH" property="xsejbscCh" jdbcType="VARCHAR" />
	    <result column="XSEJBSC_PKU" property="xsejbscPku" jdbcType="VARCHAR" />
	    <result column="XSEJBSC_CAH" property="xsejbscCah" jdbcType="VARCHAR" />
	    <result column="XSEJBSC_G6PD" property="xsejbscG6pd" jdbcType="VARCHAR" />
	    <result column="XSEJBSC_TL" property="xsejbscTl" jdbcType="VARCHAR" />
	    <result column="TNMFQ" property="tnmfq" jdbcType="VARCHAR" />
	    <result column="BYXJC" property="byxjc" jdbcType="VARCHAR" />
	    <result column="BYXZD" property="byxzd" jdbcType="VARCHAR" />
	    <result column="QTJGZR" property="qtjgzr" jdbcType="VARCHAR" />
	    <result column="CYZYZDMC_BZ" property="cyzyzdmcBz" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ1" property="cyqtzdmcBz1" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ2" property="cyqtzdmcBz2" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ3" property="cyqtzdmcBz3" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ4" property="cyqtzdmcBz4" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ5" property="cyqtzdmcBz5" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ6" property="cyqtzdmcBz6" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ7" property="cyqtzdmcBz7" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ8" property="cyqtzdmcBz8" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ9" property="cyqtzdmcBz9" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ10" property="cyqtzdmcBz10" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ11" property="cyqtzdmcBz11" jdbcType="VARCHAR" />
	    <result column="CYQTZDMC_BZ12" property="cyqtzdmcBz12" jdbcType="VARCHAR" />
	    <result column="MZZDMC_BZ" property="mzzdmcBz" jdbcType="VARCHAR" />
	    <result column="RYZDMC_BZ" property="ryzdmcBz" jdbcType="VARCHAR" />
	    <result column="MZZDBM_BZ" property="mzzdbmBz" jdbcType="VARCHAR" />
	    <result column="RYZDBM_BZ" property="ryzdbmBz" jdbcType="VARCHAR" />
	    <result column="CYZYZDBM_BZ" property="cyzyzdbmBz" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ1" property="cyqtzdbmBz1" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ2" property="cyqtzdbmBz2" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ3" property="cyqtzdbmBz3" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ4" property="cyqtzdbmBz4" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ5" property="cyqtzdbmBz5" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ6" property="cyqtzdbmBz6" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ7" property="cyqtzdbmBz7" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ8" property="cyqtzdbmBz8" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ9" property="cyqtzdbmBz9" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ10" property="cyqtzdbmBz10" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ11" property="cyqtzdbmBz11" jdbcType="VARCHAR" />
	    <result column="CYQTZDBM_BZ12" property="cyqtzdbmBz12" jdbcType="VARCHAR" />
	    <result column="RYTJ" property="rytj" jdbcType="VARCHAR" />
	    <result column="RYQK" property="ryqk" jdbcType="VARCHAR" />
	   </collection>
	   <collection property="jbxxmsgModel" column="zyh" ofType="com.supx.csp.api.bagl.sygl.pojo.Bagl_jbxx_MsgModel">
		<id column="ZYH" jdbcType="VARCHAR" property="zyh" />
	    <id column="YLJGBM" jdbcType="VARCHAR" property="yljgbm" />
		<result column="BRID" property="brid" jdbcType="VARCHAR" />
	    <result column="BRXM" property="brxm" jdbcType="VARCHAR" />
	    <result column="BRXB" property="brxb" jdbcType="VARCHAR" />
	    <result column="CSRQ" property="csrq" jdbcType="TIMESTAMP" />
	    <result column="BRGJ" property="brgj" jdbcType="VARCHAR" />
	    <result column="BRMZ" property="brmz" jdbcType="VARCHAR" />
	    <result column="SFZJLX" property="sfzjlx" jdbcType="VARCHAR" />
	    <result column="SFZJHM" property="sfzjhm" jdbcType="VARCHAR" />
	    <result column="HYZK" property="hyzk" jdbcType="VARCHAR" />
	    <result column="ZYBM" property="zybm" jdbcType="VARCHAR" />
	    <result column="HZLX" property="hzlx" jdbcType="VARCHAR" />
	    <result column="CSD" property="csd" jdbcType="VARCHAR" />
	    <result column="SJHM" property="sjhm" jdbcType="VARCHAR" />
	    <result column="GZDW" property="gzdw" jdbcType="VARCHAR" />
	    <result column="DWDZ" property="dwdz" jdbcType="VARCHAR" />
	    <result column="DWYB" property="dwyb" jdbcType="VARCHAR" />
	    <result column="jzd_XZQH" property="jzdxzqh" jdbcType="VARCHAR" />
	    <result column="jzd_SHENG" property="jzdsheng" jdbcType="VARCHAR" />
	    <result column="jzd_SHI" property="jzdshi" jdbcType="VARCHAR" />
	    <result column="jzd_XIAN" property="jzdxian" jdbcType="VARCHAR" />
	    <result column="jzd_XIANG" property="jzdxiang" jdbcType="VARCHAR" />
	    <result column="jzd_JWH" property="jzdjwh" jdbcType="VARCHAR" />
	    <result column="jzd_CUN" property="jzdcun" jdbcType="VARCHAR" />
	    <result column="jzd_NONG" property="jzdnong" jdbcType="VARCHAR" />
	    <result column="jzd_LH" property="jzdlh" jdbcType="VARCHAR" />
	    <result column="jzd_MPH" property="jzdmph" jdbcType="VARCHAR" />
	    <result column="jzdMC" property="jzdmc" jdbcType="VARCHAR" />
	    <result column="hjd_XZQH" property="hjdxzqh" jdbcType="VARCHAR" />
	    <result column="hjd_SHENG" property="hjdsheng" jdbcType="VARCHAR" />
	    <result column="hjd_SHI" property="hjdshi" jdbcType="VARCHAR" />
	    <result column="hjd_XIAN" property="hjdxian" jdbcType="VARCHAR" />
	    <result column="hjd_XIANG" property="hjdxiang" jdbcType="VARCHAR" />
	    <result column="hjd_JWH" property="hjdjwh" jdbcType="VARCHAR" />
	    <result column="hjd_CUN" property="hjdcun" jdbcType="VARCHAR" />
	    <result column="hjd_NONG" property="hjdnong" jdbcType="VARCHAR" />
	    <result column="hjd_LH" property="hjdlh" jdbcType="VARCHAR" />
	    <result column="hjd_MPH" property="hjdmph" jdbcType="VARCHAR" />
	    <result column="HKDZ" property="hkdz" jdbcType="VARCHAR" />
	    <result column="HKDYB" property="hkdyb" jdbcType="VARCHAR" />
	    <result column="LXRXM" property="lxrxm" jdbcType="VARCHAR" />
	    <result column="LXRGX" property="lxrgx" jdbcType="VARCHAR" />
	    <result column="LXRDZ" property="lxrdz" jdbcType="VARCHAR" />
	    <result column="LXRDW" property="lxrdw" jdbcType="VARCHAR" />
	    <result column="LXRYB" property="lxryb" jdbcType="VARCHAR" />
	    <result column="LXRDH" property="lxrdh" jdbcType="VARCHAR" />
	    <result column="DJRQ" property="djrq" jdbcType="TIMESTAMP" />
	    <result column="DJRY" property="djry" jdbcType="VARCHAR" />
	    <result column="PYDM" property="pydm" jdbcType="VARCHAR" />
	    <result column="SFZJLXMC" property="sfzjlxmc" jdbcType="VARCHAR" />
	    <result column="HYZKMC" property="hyzkmc" jdbcType="VARCHAR" />
	    <result column="ZYBMMC" property="zybmmc" jdbcType="VARCHAR" />
	    <result column="LXDH_LBDM" property="lxdhlbdm" jdbcType="VARCHAR" />
	    <result column="JKDABH" property="jkdabh" jdbcType="VARCHAR" />
	    <result column="BRGJMC" property="brgjmc" jdbcType="VARCHAR" />
	    <result column="BRMZMC" property="brmzmc" jdbcType="VARCHAR" />
	    <result column="jzd_XZQHMC" property="jzdxzqhmc" jdbcType="VARCHAR" />
	    <result column="jzd_SHENGMC" property="jzdshengmc" jdbcType="VARCHAR" />
	    <result column="jzd_SHIMC" property="jzdshimc" jdbcType="VARCHAR" />
	    <result column="jzd_XIANMC" property="jzdxianmc" jdbcType="VARCHAR" />
	    <result column="jzd_XIANGMC" property="jzdxiangmc" jdbcType="VARCHAR" />
	    <result column="jzd_JWHMC" property="jzdjwhmc" jdbcType="VARCHAR" />
	    <result column="hjd_XZQHMC" property="hjdxzqhmc" jdbcType="VARCHAR" />
	    <result column="hjd_SHENGMC" property="hjdshengmc" jdbcType="VARCHAR" />
	    <result column="hjd_SHIMC" property="hjdshimc" jdbcType="VARCHAR" />
	    <result column="hjd_XIANMC" property="hjdxianmc" jdbcType="VARCHAR" />
	    <result column="hjd_XIANGMC" property="hjdxiangmc" jdbcType="VARCHAR" />
	    <result column="hjd_JWHMC" property="hjdjwhmc" jdbcType="VARCHAR" />
	    <result column="SG" property="sg" jdbcType="DECIMAL" />
	    <result column="TZ" property="tz" jdbcType="DECIMAL" />
	    <result column="YLJGMC" property="yljgmc" jdbcType="VARCHAR" />
	    <result column="JZD_EMAIL" property="jzdEmail" jdbcType="VARCHAR" />
	    <result column="JZD_QQ" property="jzdQq" jdbcType="VARCHAR" />
	    <result column="JZDYB" property="jzdyb" jdbcType="VARCHAR" />
	    <result column="CSD_SHENG" property="csdSheng" jdbcType="VARCHAR" />
	    <result column="CSD_SHI" property="csdShi" jdbcType="VARCHAR" />
	    <result column="CSD_XIAN" property="csdXian" jdbcType="VARCHAR" />
	    <result column="CSD_SHENGMC" property="csdShengmc" jdbcType="VARCHAR" />
	    <result column="CSD_SHIMC" property="csdShimc" jdbcType="VARCHAR" />
	    <result column="CSD_XIANMC" property="csdXianmc" jdbcType="VARCHAR" />
	    <result column="LXR_SHENG" property="lxrSheng" jdbcType="VARCHAR" />
	    <result column="LXR_SHI" property="lxrShi" jdbcType="VARCHAR" />
	    <result column="LXR_XIAN" property="lxrXian" jdbcType="VARCHAR" />
	    <result column="LXR_SHENGMC" property="lxrShengmc" jdbcType="VARCHAR" />
	    <result column="LXR_SHIMC" property="lxrShimc" jdbcType="VARCHAR" />
	    <result column="LXR_XIANMC" property="lxrXianmc" jdbcType="VARCHAR" />
	    <result column="DW_SHENG" property="dwSheng" jdbcType="VARCHAR" />
	    <result column="DW_SHI" property="dwShi" jdbcType="VARCHAR" />
	    <result column="DW_XIAN" property="dwXian" jdbcType="VARCHAR" />
	    <result column="DW_SHENGMC" property="dwShengmc" jdbcType="VARCHAR" />
	    <result column="DW_SHIMC" property="dwShimc" jdbcType="VARCHAR" />
	    <result column="DW_XIANMC" property="dwXianmc" jdbcType="VARCHAR" />
	    <result column="JG_SHENG" property="jgSheng" jdbcType="VARCHAR" />
	    <result column="JG_SHI" property="jgShi" jdbcType="VARCHAR" />
	    <result column="JG_XIAN" property="jgXian" jdbcType="VARCHAR" />
	    <result column="JG_SHENGMC" property="jgShengmc" jdbcType="VARCHAR" />
	    <result column="JG_SHIMC" property="jgShimc" jdbcType="VARCHAR" />
	    <result column="JG_XIANMC" property="jgXianmc" jdbcType="VARCHAR" />
	    <result column="BAH" property="bah" jdbcType="VARCHAR" />
	    <result column="ZYCS" property="zycs" jdbcType="DECIMAL" />
	    <result column="NL" property="nl" jdbcType="DECIMAL" />
	    <result column="YL" property="yl" jdbcType="DECIMAL" />
	    <result column="TL" property="tl" jdbcType="DECIMAL" />
	    <result column="NLDW" property="nldw" jdbcType="VARCHAR" />
	    <result column="YLKH" property="ylkh" jdbcType="VARCHAR" />
	    <result column="YLKLX" property="ylklx" jdbcType="VARCHAR" />

	    <result column="YLFKFS" jdbcType="VARCHAR" property="ylfkfs" />
	    <result column="BRYL" jdbcType="DECIMAL" property="bryl" />
	    <result column="BRYLDAY" jdbcType="DECIMAL" property="brylday" />
	    <result column="XSECSTZ" jdbcType="DECIMAL" property="xsecstz" />
	    <result column="XSERYTZ" jdbcType="DECIMAL" property="xserytz" />
	    <result column="BRID" jdbcType="VARCHAR" property="brid" />
	    <result column="JSBZ" jdbcType="VARCHAR" property="jsbz" />
	    <result column="JSRQ" jdbcType="TIMESTAMP" property="jsrq" />
	    <result column="JSRY" jdbcType="VARCHAR" property="jsry" />
	    <result column="SHBZ" jdbcType="VARCHAR" property="shbz" />
	    <result column="SHRQ" jdbcType="TIMESTAMP" property="shrq" />
	    <result column="SHRY" jdbcType="VARCHAR" property="shry" />
	    <result column="GDBZ" jdbcType="VARCHAR" property="gdbz" />
	    <result column="GDRQ" jdbcType="TIMESTAMP" property="gdrq" />
	    <result column="GDRY" jdbcType="VARCHAR" property="gdry" />

	    <result column="DWFZR" jdbcType="VARCHAR" property="dwfzr" />
	    <result column="TJFZR" jdbcType="VARCHAR" property="tjfzr" />
	    <result column="TBR" jdbcType="VARCHAR" property="tbr" />
	    <result column="LXDH" jdbcType="VARCHAR" property="lxdh" />
	    <result column="SJ" jdbcType="VARCHAR" property="sj" />



	   </collection>

   </resultMap>

	<!-- 查询病案上报需要的集合信息 -->
	<select id="queryBasbMsg" resultMap="BaseResultMap" parameterType="com.supx.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel">
		select brjbxx.*,bajbxx.ylfkfs,bajbxx.bryl,bajbxx.xsecstz,bajbxx.xserytz,yljg.dwfzr,yljg.tjfzr,yljg.lxdh,yljg.TJRDH sj,
		yljg.jgmc yljgmc,rydj.zycs,rydj.bah,rydj.nl,rydj.nldw,rydj.ylkh,rydj.ylklx,bajbxx.yl,bajbxx.tl,ssxx.*,zdxx.*,fyxx.* from gyb_brjbxx brjbxx
	  	inner join gyb_yljg yljg on yljg.jgbm=brjbxx.yljgbm
	  	inner join zyb_rydj rydj on rydj.brid=brjbxx.brid and rydj.yljgbm=brjbxx.yljgbm
	  	inner join bagl_basy_jbxx bajbxx on bajbxx.zyh=rydj.zyh and  bajbxx.yljgbm=rydj.yljgbm
	    inner join bagl_basy_zdxx zdxx on zdxx.zyh=bajbxx.zyh and zdxx.yljgbm=bajbxx.yljgbm
	    inner join bagl_basy_ssxx ssxx on ssxx.zyh=zdxx.zyh and ssxx.yljgbm=zdxx.yljgbm
	    inner join bagl_basy_fyxx fyxx on fyxx.zyh=ssxx.zyh and fyxx.yljgbm = ssxx.yljgbm
	    where yljg.tybz='0' and bajbxx.shbz='1' and bajbxx.yljgbm=#{yljgbm,jdbcType=VARCHAR}
	    <if test="beginrq != null">
			and (zdxx.cyrq &gt;= #{beginrq,jdbcType=TIMESTAMP})
		</if>
		<if test="endrq != null">
			and (zdxx.cyrq &lt;= #{endrq,jdbcType=TIMESTAMP})
		</if>
	</select>

  <!-- 查询所有数据 -->
  <select id="query" resultType="com.supx.csp.api.bagl.basb.pojo.Bagl_wtsb_xyModel" parameterType="com.supx.csp.api.bagl.basb.pojo.Bagl_wtsb_xyModel">
  	select * from BAGL_WTSB_XY where yljgbm=#{yljgbm,jdbcType=VARCHAR}
  </select>

  <!-- 批量删除上报西医的所有数据 -->
  <delete id="delete">
	delete from BAGL_WTSB_XY where yljgbm=#{yljgbm,jdbcType=VARCHAR}
  </delete>

  <!-- 批量添加上报西医 -->
  <insert id="insertone" parameterType="com.supx.csp.api.bagl.basb.pojo.Bagl_wtsb_xyModel" >
	  insert into BAGL_WTSB_XY
	  <trim prefix="(" suffix=")" suffixOverrides="," >
		  <if test="username != null" >
			  USERNAME,
		  </if>
		  <if test="ylfkfs != null" >
			  YLFKFS,
		  </if>
		  <if test="jkkh != null" >
			  JKKH,
		  </if>
		  <if test="zycs != null" >
			  ZYCS,
		  </if>
		  <if test="bah != null" >
			  BAH,
		  </if>
		  <if test="xm != null" >
			  XM,
		  </if>
		  <if test="xb != null" >
			  XB,
		  </if>
		  <if test="csrq != null" >
			  CSRQ,
		  </if>
		  <if test="nl != null" >
			  NL,
		  </if>
		  <if test="gj != null" >
			  GJ,
		  </if>
		  <if test="bzyzsnl != null" >
			  BZYZSNL,
		  </if>
		  <if test="xsecstz != null" >
			  XSECSTZ,
		  </if>
		  <if test="xserytz != null" >
			  XSERYTZ,
		  </if>
		  <if test="csd != null" >
			  CSD,
		  </if>
		  <if test="gg != null" >
			  GG,
		  </if>
		  <if test="mz != null" >
			  MZ,
		  </if>
		  <if test="sfzh != null" >
			  SFZH,
		  </if>
		  <if test="zy != null" >
			  ZY,
		  </if>
		  <if test="hy != null" >
			  HY,
		  </if>
		  <if test="xzz != null" >
			  XZZ,
		  </if>
		  <if test="dh != null" >
			  DH,
		  </if>
		  <if test="yb1 != null" >
			  YB1,
		  </if>
		  <if test="hkdz != null" >
			  HKDZ,
		  </if>
		  <if test="yb2 != null" >
			  YB2,
		  </if>
		  <if test="gzdwjdz != null" >
			  GZDWJDZ,
		  </if>
		  <if test="dwdh != null" >
			  DWDH,
		  </if>
		  <if test="yb3 != null" >
			  YB3,
		  </if>
		  <if test="lxrxm != null" >
			  LXRXM,
		  </if>
		  <if test="gx != null" >
			  GX,
		  </if>
		  <if test="dz != null" >
			  DZ,
		  </if>
		  <if test="dh2 != null" >
			  DH2,
		  </if>
		  <if test="rytj != null" >
			  RYTJ,
		  </if>
		  <if test="rysj != null" >
			  RYSJ,
		  </if>
		  <if test="rysjs != null" >
			  RYSJS,
		  </if>
		  <if test="rykb != null" >
			  RYKB,
		  </if>
		  <if test="rybf != null" >
			  RYBF,
		  </if>
		  <if test="zkkb != null" >
			  ZKKB,
		  </if>
		  <if test="cysj != null" >
			  CYSJ,
		  </if>
		  <if test="cysjs != null" >
			  CYSJS,
		  </if>
		  <if test="cykb != null" >
			  CYKB,
		  </if>
		  <if test="cybf != null" >
			  CYBF,
		  </if>
		  <if test="sjzyts != null" >
			  SJZYTS,
		  </if>
		  <if test="mzzd != null" >
			  MZZD,
		  </if>
		  <if test="jbbm != null" >
			  JBBM,
		  </if>
		  <if test="zyzd != null" >
			  ZYZD,
		  </if>
		  <if test="jbdm != null" >
			  JBDM,
		  </if>
		  <if test="rybq != null" >
			  RYBQ,
		  </if>
		  <if test="qtzd8 != null" >
			  QTZD8,
		  </if>
		  <if test="jbdm8 != null" >
			  JBDM8,
		  </if>
		  <if test="rybq8 != null" >
			  RYBQ8,
		  </if>
		  <if test="qtzd1 != null" >
			  QTZD1,
		  </if>
		  <if test="jbdm1 != null" >
			  JBDM1,
		  </if>
		  <if test="rybq1 != null" >
			  RYBQ1,
		  </if>
		  <if test="qtzd9 != null" >
			  QTZD9,
		  </if>
		  <if test="jbdm9 != null" >
			  JBDM9,
		  </if>
		  <if test="rybq9 != null" >
			  RYBQ9,
		  </if>
		  <if test="qtzd2 != null" >
			  QTZD2,
		  </if>
		  <if test="jbdm2 != null" >
			  JBDM2,
		  </if>
		  <if test="rybq2 != null" >
			  RYBQ2,
		  </if>
		  <if test="qtzd10 != null" >
			  QTZD10,
		  </if>
		  <if test="jbdm10 != null" >
			  JBDM10,
		  </if>
		  <if test="rybq10 != null" >
			  RYBQ10,
		  </if>
		  <if test="qtzd3 != null" >
			  QTZD3,
		  </if>
		  <if test="jbdm3 != null" >
			  JBDM3,
		  </if>
		  <if test="rybq3 != null" >
			  RYBQ3,
		  </if>
		  <if test="qtzd11 != null" >
			  QTZD11,
		  </if>
		  <if test="jbdm11 != null" >
			  JBDM11,
		  </if>
		  <if test="rybq11 != null" >
			  RYBQ11,
		  </if>
		  <if test="qtzd4 != null" >
			  QTZD4,
		  </if>
		  <if test="jbdm4 != null" >
			  JBDM4,
		  </if>
		  <if test="rybq4 != null" >
			  RYBQ4,
		  </if>
		  <if test="qtzd12 != null" >
			  QTZD12,
		  </if>
		  <if test="jbdm12 != null" >
			  JBDM12,
		  </if>
		  <if test="rybq12 != null" >
			  RYBQ12,
		  </if>
		  <if test="qtzd5 != null" >
			  QTZD5,
		  </if>
		  <if test="jbdm5 != null" >
			  JBDM5,
		  </if>
		  <if test="rybq5 != null" >
			  RYBQ5,
		  </if>
		  <if test="qtzd13 != null" >
			  QTZD13,
		  </if>
		  <if test="jbdm13 != null" >
			  JBDM13,
		  </if>
		  <if test="rybq13 != null" >
			  RYBQ13,
		  </if>
		  <if test="qtzd6 != null" >
			  QTZD6,
		  </if>
		  <if test="jbdm6 != null" >
			  JBDM6,
		  </if>
		  <if test="rybq6 != null" >
			  RYBQ6,
		  </if>
		  <if test="qtzd14 != null" >
			  QTZD14,
		  </if>
		  <if test="jbdm14 != null" >
			  JBDM14,
		  </if>
		  <if test="rybq14 != null" >
			  RYBQ14,
		  </if>
		  <if test="qtzd7 != null" >
			  QTZD7,
		  </if>
		  <if test="jbdm7 != null" >
			  JBDM7,
		  </if>
		  <if test="rybq7 != null" >
			  RYBQ7,
		  </if>
		  <if test="qtzd15 != null" >
			  QTZD15,
		  </if>
		  <if test="jbdm15 != null" >
			  JBDM15,
		  </if>
		  <if test="rybq15 != null" >
			  RYBQ15,
		  </if>
		  <if test="wbyy != null" >
			  WBYY,
		  </if>
		  <if test="h23 != null" >
			  H23,
		  </if>
		  <if test="blzd != null" >
			  BLZD,
		  </if>
		  <if test="jbmm != null" >
			  JBMM,
		  </if>
		  <if test="blh != null" >
			  BLH,
		  </if>
		  <if test="ywgm != null" >
			  YWGM,
		  </if>
		  <if test="gmyw != null" >
			  GMYW,
		  </if>
		  <if test="swhzsj != null" >
			  SWHZSJ,
		  </if>
		  <if test="xx != null" >
			  XX,
		  </if>
		  <if test="rh != null" >
			  RH,
		  </if>
		  <if test="kzr != null" >
			  KZR,
		  </if>
		  <if test="zrys != null" >
			  ZRYS,
		  </if>
		  <if test="zzys != null" >
			  ZZYS,
		  </if>
		  <if test="zyys != null" >
			  ZYYS,
		  </if>
		  <if test="zrhs != null" >
			  ZRHS,
		  </if>
		  <if test="jxys != null" >
			  JXYS,
		  </if>
		  <if test="sxys != null" >
			  SXYS,
		  </if>
		  <if test="bmy != null" >
			  BMY,
		  </if>
		  <if test="bazl != null" >
			  BAZL,
		  </if>
		  <if test="zkys != null" >
			  ZKYS,
		  </if>
		  <if test="zkhs != null" >
			  ZKHS,
		  </if>
		  <if test="zkrq != null" >
			  ZKRQ,
		  </if>
		  <if test="ssjczbm1 != null" >
			  SSJCZBM1,
		  </if>
		  <if test="ssjczrq1 != null" >
			  SSJCZRQ1,
		  </if>
		  <if test="ssjb1 != null" >
			  SSJB1,
		  </if>
		  <if test="ssjczmc1 != null" >
			  SSJCZMC1,
		  </if>
		  <if test="sz1 != null" >
			  SZ1,
		  </if>
		  <if test="yz1 != null" >
			  YZ1,
		  </if>
		  <if test="ez1 != null" >
			  EZ1,
		  </if>
		  <if test="qkdj1 != null" >
			  QKDJ1,
		  </if>
		  <if test="qkyhlb1 != null" >
			  QKYHLB1,
		  </if>
		  <if test="mzfs1 != null" >
			  MZFS1,
		  </if>
		  <if test="mzys1 != null" >
			  MZYS1,
		  </if>
		  <if test="ssjczbm2 != null" >
			  SSJCZBM2,
		  </if>
		  <if test="ssjczrq2 != null" >
			  SSJCZRQ2,
		  </if>
		  <if test="ssjb2 != null" >
			  SSJB2,
		  </if>
		  <if test="ssjczmc2 != null" >
			  SSJCZMC2,
		  </if>
		  <if test="sz2 != null" >
			  SZ2,
		  </if>
		  <if test="yz2 != null" >
			  YZ2,
		  </if>
		  <if test="ez2 != null" >
			  EZ2,
		  </if>
		  <if test="qkdj2 != null" >
			  QKDJ2,
		  </if>
		  <if test="qkyhlb2 != null" >
			  QKYHLB2,
		  </if>
		  <if test="mzfs2 != null" >
			  MZFS2,
		  </if>
		  <if test="mzys2 != null" >
			  MZYS2,
		  </if>
		  <if test="ssjczbm3 != null" >
			  SSJCZBM3,
		  </if>
		  <if test="ssjczrq3 != null" >
			  SSJCZRQ3,
		  </if>
		  <if test="ssjb3 != null" >
			  SSJB3,
		  </if>
		  <if test="ssjczmc3 != null" >
			  SSJCZMC3,
		  </if>
		  <if test="sz3 != null" >
			  SZ3,
		  </if>
		  <if test="yz3 != null" >
			  YZ3,
		  </if>
		  <if test="ez3 != null" >
			  EZ3,
		  </if>
		  <if test="qkdj3 != null" >
			  QKDJ3,
		  </if>
		  <if test="qkyhlb3 != null" >
			  QKYHLB3,
		  </if>
		  <if test="mzfs3 != null" >
			  MZFS3,
		  </if>
		  <if test="mzys3 != null" >
			  MZYS3,
		  </if>
		  <if test="ssjczbm4 != null" >
			  SSJCZBM4,
		  </if>
		  <if test="ssjczrq4 != null" >
			  SSJCZRQ4,
		  </if>
		  <if test="ssjb4 != null" >
			  SSJB4,
		  </if>
		  <if test="ssjczmc4 != null" >
			  SSJCZMC4,
		  </if>
		  <if test="sz4 != null" >
			  SZ4,
		  </if>
		  <if test="yz4 != null" >
			  YZ4,
		  </if>
		  <if test="ez4 != null" >
			  EZ4,
		  </if>
		  <if test="qkdj4 != null" >
			  QKDJ4,
		  </if>
		  <if test="qkyhlb4 != null" >
			  QKYHLB4,
		  </if>
		  <if test="mzfs4 != null" >
			  MZFS4,
		  </if>
		  <if test="mzys4 != null" >
			  MZYS4,
		  </if>
		  <if test="ssjczbm5 != null" >
			  SSJCZBM5,
		  </if>
		  <if test="ssjczrq5 != null" >
			  SSJCZRQ5,
		  </if>
		  <if test="ssjb5 != null" >
			  SSJB5,
		  </if>
		  <if test="ssjczmc5 != null" >
			  SSJCZMC5,
		  </if>
		  <if test="sz5 != null" >
			  SZ5,
		  </if>
		  <if test="yz5 != null" >
			  YZ5,
		  </if>
		  <if test="ez5 != null" >
			  EZ5,
		  </if>
		  <if test="qkdj5 != null" >
			  QKDJ5,
		  </if>
		  <if test="qkyhlb5 != null" >
			  QKYHLB5,
		  </if>
		  <if test="mzfs5 != null" >
			  MZFS5,
		  </if>
		  <if test="mzys5 != null" >
			  MZYS5,
		  </if>
		  <if test="ssjczbm6 != null" >
			  SSJCZBM6,
		  </if>
		  <if test="ssjczrq6 != null" >
			  SSJCZRQ6,
		  </if>
		  <if test="ssjb6 != null" >
			  SSJB6,
		  </if>
		  <if test="ssjczmc6 != null" >
			  SSJCZMC6,
		  </if>
		  <if test="sz6 != null" >
			  SZ6,
		  </if>
		  <if test="yz6 != null" >
			  YZ6,
		  </if>
		  <if test="ez6 != null" >
			  EZ6,
		  </if>
		  <if test="qkdj6 != null" >
			  QKDJ6,
		  </if>
		  <if test="qkyhlb6 != null" >
			  QKYHLB6,
		  </if>
		  <if test="mzfs6 != null" >
			  MZFS6,
		  </if>
		  <if test="mzys6 != null" >
			  MZYS6,
		  </if>
		  <if test="ssjczbm7 != null" >
			  SSJCZBM7,
		  </if>
		  <if test="ssjczrq7 != null" >
			  SSJCZRQ7,
		  </if>
		  <if test="ssjb7 != null" >
			  SSJB7,
		  </if>
		  <if test="ssjczmc7 != null" >
			  SSJCZMC7,
		  </if>
		  <if test="sz7 != null" >
			  SZ7,
		  </if>
		  <if test="yz7 != null" >
			  YZ7,
		  </if>
		  <if test="ez7 != null" >
			  EZ7,
		  </if>
		  <if test="qkdj7 != null" >
			  QKDJ7,
		  </if>
		  <if test="qkyhlb7 != null" >
			  QKYHLB7,
		  </if>
		  <if test="mzfs7 != null" >
			  MZFS7,
		  </if>
		  <if test="mzys7 != null" >
			  MZYS7,
		  </if>
		  <if test="lyfs != null" >
			  LYFS,
		  </if>
		  <if test="yzzyYljg != null" >
			  YZZY_YLJG,
		  </if>
		  <if test="wsyYljg != null" >
			  WSY_YLJG,
		  </if>
		  <if test="sfzzyjh != null" >
			  SFZZYJH,
		  </if>
		  <if test="md != null" >
			  MD,
		  </if>
		  <if test="ryqT != null" >
			  RYQ_T,
		  </if>
		  <if test="ryqXs != null" >
			  RYQ_XS,
		  </if>
		  <if test="ryqF != null" >
			  RYQ_F,
		  </if>
		  <if test="ryhT != null" >
			  RYH_T,
		  </if>
		  <if test="ryhXs != null" >
			  RYH_XS,
		  </if>
		  <if test="ryhF != null" >
			  RYH_F,
		  </if>
		  <if test="zfy != null" >
			  ZFY,
		  </if>
		  <if test="zfje != null" >
			  ZFJE,
		  </if>
		  <if test="ylfuf != null" >
			  YLFUF,
		  </if>
		  <if test="zlczf != null" >
			  ZLCZF,
		  </if>
		  <if test="hlf != null" >
			  HLF,
		  </if>
		  <if test="qtfy != null" >
			  QTFY,
		  </if>
		  <if test="blzdf != null" >
			  BLZDF,
		  </if>
		  <if test="syszdf != null" >
			  SYSZDF,
		  </if>
		  <if test="yxxzdf != null" >
			  YXXZDF,
		  </if>
		  <if test="lczdxmf != null" >
			  LCZDXMF,
		  </if>
		  <if test="fsszlxmf != null" >
			  FSSZLXMF,
		  </if>
		  <if test="wlzlf != null" >
			  WLZLF,
		  </if>
		  <if test="sszlf != null" >
			  SSZLF,
		  </if>
		  <if test="maf != null" >
			  MAF,
		  </if>
		  <if test="ssf != null" >
			  SSF,
		  </if>
		  <if test="kff != null" >
			  KFF,
		  </if>
		  <if test="zyzlf != null" >
			  ZYZLF,
		  </if>
		  <if test="xyf != null" >
			  XYF,
		  </if>
		  <if test="kjywf != null" >
			  KJYWF,
		  </if>
		  <if test="zcyf != null" >
			  ZCYF,
		  </if>
		  <if test="zcyf1 != null" >
			  ZCYF1,
		  </if>
		  <if test="xf != null" >
			  XF,
		  </if>
		  <if test="bdblzpf != null" >
			  BDBLZPF,
		  </if>
		  <if test="qdblzpf != null" >
			  QDBLZPF,
		  </if>
		  <if test="nxyzlzpf != null" >
			  NXYZLZPF,
		  </if>
		  <if test="xbyzlzpf != null" >
			  XBYZLZPF,
		  </if>
		  <if test="hcyyclf != null" >
			  HCYYCLF,
		  </if>
		  <if test="yyclf != null" >
			  YYCLF,
		  </if>
		  <if test="ycxyyclf != null" >
			  YCXYYCLF,
		  </if>
		  <if test="qtf != null" >
			  QTF,
		  </if>
		  <if test="ryqk != null" >
			  RYQK,
		  </if>
		  <if test="ryzd != null" >
			  RYZD,
		  </if>
		  <if test="zyzdqzrq != null" >
			  ZYZDQZRQ,
		  </if>
		  <if test="jbbm1 != null" >
			  JBBM1,
		  </if>
		  <if test="bwhbz != null" >
			  BWHBZ,
		  </if>
		  <if test="cyqk != null" >
			  CYQK,
		  </if>
		  <if test="cyqk1 != null" >
			  CYQK1,
		  </if>
		  <if test="cyqk2 != null" >
			  CYQK2,
		  </if>
		  <if test="cyqk3 != null" >
			  CYQK3,
		  </if>
		  <if test="cyqk4 != null" >
			  CYQK4,
		  </if>
		  <if test="cyqk5 != null" >
			  CYQK5,
		  </if>
		  <if test="cyqk6 != null" >
			  CYQK6,
		  </if>
		  <if test="cyqk7 != null" >
			  CYQK7,
		  </if>
		  <if test="cyqk8 != null" >
			  CYQK8,
		  </if>
		  <if test="cyqk9 != null" >
			  CYQK9,
		  </if>
		  <if test="cyqk10 != null" >
			  CYQK10,
		  </if>
		  <if test="cyqk11 != null" >
			  CYQK11,
		  </if>
		  <if test="cyqk12 != null" >
			  CYQK12,
		  </if>
		  <if test="cyqk13 != null" >
			  CYQK13,
		  </if>
		  <if test="cyqk14 != null" >
			  CYQK14,
		  </if>
		  <if test="cyqk15 != null" >
			  CYQK15,
		  </if>
		  <if test="sz != null" >
			  SZ,
		  </if>
		  <if test="szqxz != null" >
			  SZQXZ,
		  </if>
		  <if test="szqxy != null" >
			  SZQXY,
		  </if>
		  <if test="szqxn != null" >
			  SZQXN,
		  </if>
		  <if test="yykjyw != null" >
			  YYKJYW,
		  </if>
		  <if test="sycxsj != null" >
			  SYCXSJ,
		  </if>
		  <if test="lhyy != null" >
			  LHYY,
		  </if>
		  <if test="lcljgl != null" >
			  LCLJGL,
		  </if>
		  <if test="wclclj != null" >
			  WCLCLJ,
		  </if>
		  <if test="tcyy != null" >
			  TCYY,
		  </if>
		  <if test="sfby != null" >
			  SFBY,
		  </if>
		  <if test="byyy != null" >
			  BYYY,
		  </if>
		  <if test="tnmfq != null" >
			  TNMFQ,
		  </if>
		  <if test="tzbzry != null" >
			  TZBZRY,
		  </if>
		  <if test="cyrqjgts != null" >
			  CYRQJGTS,
		  </if>
		  <if test="qtzf != null" >
			  QTZF,
		  </if>
		  <if test="dwfzr != null" >
			  DWFZR,
		  </if>
		  <if test="tjfzr != null" >
			  TJFZR,
		  </if>
		  <if test="tbr != null" >
			  TBR,
		  </if>
		  <if test="lxdh != null" >
			  LXDH,
		  </if>
		  <if test="sj != null" >
			  SJ,
		  </if>
		  <if test="bcrq != null" >
			  BCRQ,
		  </if>
		  <if test="xzzXzqh != null" >
			  XZZ_XZQH,
		  </if>
		  <if test="hkdzXzqh != null" >
			  HKDZ_XZQH,
		  </if>
		  <if test="dwdzXzqh != null" >
			  DWDZ_XZQH,
		  </if>
		  <if test="dzXzqh != null" >
			  DZ_XZQH,
		  </if>
		  <if test="tjfzrDh != null" >
			  TJFZR_DH,
		  </if>
		  <if test="zznys != null" >
			  ZZNYS,
		  </if>
		  <if test="qtjgzr != null" >
			  QTJGZR,
		  </if>
		  <if test="bzyzsnlr != null">
			  BZYZSNLR,
		  </if>
		  <if test="rysjf != null">
			  RYSJF,
		  </if>
		  <if test="cysjf != null">
			  CYSJF,
		  </if>
		  <if test="hxb != null">
			  HXB,
		  </if>
		  <if test="xxb != null">
			  XXB,
		  </if>
		  <if test="xj != null">
			  XJ,
		  </if>
		  <if test="qx != null">
			  QX,
		  </if>
		  <if test="ztxhs != null">
			  ZTXHS,
		  </if>
		  <if test="bdb != null">
			  BDB,
		  </if>
		  <if test="lcd != null">
			  LCD,
		  </if>
		  <if test="qt != null">
			  QT,
		  </if>
		  <if test="sxfy != null">
			  SXFY,
		  </if>
		  <if test="sf1 != null">
			  SF1,
		  </if>
		  <if test="sf2 != null">
			  SF2,
		  </if>
		  <if test="sf3 != null">
			  SF3,
		  </if>
		  <if test="sf4 != null">
			  SF4,
		  </if>
		  <if test="sf5 != null">
			  SF5,
		  </if>
		  <if test="sf6 != null">
			  SF6,
		  </if>
		  <if test="sf7 != null">
			  SF7,
		  </if>
		  <if test="ct != null">
			  CT,
		  </if>
		  <if test="petct != null">
			  PETCT,
		  </if>
		  <if test="syct != null">
			  SYCT,
		  </if>
		  <if test="bc != null">
			  BC,
		  </if>
		  <if test="xp != null">
			  XP,
		  </if>
		  <if test="csxdt != null">
			  CSXDT,
		  </if>
		  <if test="mri != null">
			  MRI,
		  </if>
		  <if test="twsjc != null">
			  TWSJC,
		  </if>
		  <if test="yljgbm != null" >
			  YLJGBM,
		  </if>
	  </trim>
	  <trim prefix="values (" suffix=")" suffixOverrides="," >
		  <if test="username != null" >
			  #{username,jdbcType=VARCHAR},
		  </if>
		  <if test="ylfkfs != null" >
			  #{ylfkfs,jdbcType=VARCHAR},
		  </if>
		  <if test="jkkh != null" >
			  #{jkkh,jdbcType=VARCHAR},
		  </if>
		  <if test="zycs != null" >
			  #{zycs,jdbcType=VARCHAR},
		  </if>
		  <if test="bah != null" >
			  #{bah,jdbcType=VARCHAR},
		  </if>
		  <if test="xm != null" >
			  #{xm,jdbcType=VARCHAR},
		  </if>
		  <if test="xb != null" >
			  #{xb,jdbcType=VARCHAR},
		  </if>
		  <if test="csrq != null" >
			  #{csrq,jdbcType=VARCHAR},
		  </if>
		  <if test="nl != null" >
			  #{nl,jdbcType=DECIMAL},
		  </if>
		  <if test="gj != null" >
			  #{gj,jdbcType=VARCHAR},
		  </if>
		  <if test="bzyzsnl != null" >
			  #{bzyzsnl,jdbcType=DECIMAL},
		  </if>
		  <if test="xsecstz != null" >
			  #{xsecstz,jdbcType=DECIMAL},
		  </if>
		  <if test="xserytz != null" >
			  #{xserytz,jdbcType=DECIMAL},
		  </if>
		  <if test="csd != null" >
			  #{csd,jdbcType=VARCHAR},
		  </if>
		  <if test="gg != null" >
			  #{gg,jdbcType=VARCHAR},
		  </if>
		  <if test="mz != null" >
			  #{mz,jdbcType=VARCHAR},
		  </if>
		  <if test="sfzh != null" >
			  #{sfzh,jdbcType=VARCHAR},
		  </if>
		  <if test="zy != null" >
			  #{zy,jdbcType=VARCHAR},
		  </if>
		  <if test="hy != null" >
			  #{hy,jdbcType=VARCHAR},
		  </if>
		  <if test="xzz != null" >
			  #{xzz,jdbcType=VARCHAR},
		  </if>
		  <if test="dh != null" >
			  #{dh,jdbcType=VARCHAR},
		  </if>
		  <if test="yb1 != null" >
			  #{yb1,jdbcType=VARCHAR},
		  </if>
		  <if test="hkdz != null" >
			  #{hkdz,jdbcType=VARCHAR},
		  </if>
		  <if test="yb2 != null" >
			  #{yb2,jdbcType=VARCHAR},
		  </if>
		  <if test="gzdwjdz != null" >
			  #{gzdwjdz,jdbcType=VARCHAR},
		  </if>
		  <if test="dwdh != null" >
			  #{dwdh,jdbcType=VARCHAR},
		  </if>
		  <if test="yb3 != null" >
			  #{yb3,jdbcType=VARCHAR},
		  </if>
		  <if test="lxrxm != null" >
			  #{lxrxm,jdbcType=VARCHAR},
		  </if>
		  <if test="gx != null" >
			  #{gx,jdbcType=VARCHAR},
		  </if>
		  <if test="dz != null" >
			  #{dz,jdbcType=VARCHAR},
		  </if>
		  <if test="dh2 != null" >
			  #{dh2,jdbcType=VARCHAR},
		  </if>
		  <if test="rytj != null" >
			  #{rytj,jdbcType=VARCHAR},
		  </if>
		  <if test="rysj != null" >
			  #{rysj,jdbcType=VARCHAR},
		  </if>
		  <if test="rysjs != null" >
			  #{rysjs,jdbcType=DECIMAL},
		  </if>
		  <if test="rykb != null" >
			  #{rykb,jdbcType=VARCHAR},
		  </if>
		  <if test="rybf != null" >
			  #{rybf,jdbcType=VARCHAR},
		  </if>
		  <if test="zkkb != null" >
			  #{zkkb,jdbcType=VARCHAR},
		  </if>
		  <if test="cysj != null" >
			  #{cysj,jdbcType=VARCHAR},
		  </if>
		  <if test="cysjs != null" >
			  #{cysjs,jdbcType=DECIMAL},
		  </if>
		  <if test="cykb != null" >
			  #{cykb,jdbcType=VARCHAR},
		  </if>
		  <if test="cybf != null" >
			  #{cybf,jdbcType=VARCHAR},
		  </if>
		  <if test="sjzyts != null" >
			  #{sjzyts,jdbcType=VARCHAR},
		  </if>
		  <if test="mzzd != null" >
			  #{mzzd,jdbcType=VARCHAR},
		  </if>
		  <if test="jbbm != null" >
			  #{jbbm,jdbcType=VARCHAR},
		  </if>
		  <if test="zyzd != null" >
			  #{zyzd,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm != null" >
			  #{jbdm,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq != null" >
			  #{rybq,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd8 != null" >
			  #{qtzd8,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm8 != null" >
			  #{jbdm8,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq8 != null" >
			  #{rybq8,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd1 != null" >
			  #{qtzd1,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm1 != null" >
			  #{jbdm1,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq1 != null" >
			  #{rybq1,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd9 != null" >
			  #{qtzd9,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm9 != null" >
			  #{jbdm9,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq9 != null" >
			  #{rybq9,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd2 != null" >
			  #{qtzd2,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm2 != null" >
			  #{jbdm2,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq2 != null" >
			  #{rybq2,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd10 != null" >
			  #{qtzd10,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm10 != null" >
			  #{jbdm10,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq10 != null" >
			  #{rybq10,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd3 != null" >
			  #{qtzd3,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm3 != null" >
			  #{jbdm3,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq3 != null" >
			  #{rybq3,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd11 != null" >
			  #{qtzd11,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm11 != null" >
			  #{jbdm11,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq11 != null" >
			  #{rybq11,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd4 != null" >
			  #{qtzd4,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm4 != null" >
			  #{jbdm4,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq4 != null" >
			  #{rybq4,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd12 != null" >
			  #{qtzd12,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm12 != null" >
			  #{jbdm12,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq12 != null" >
			  #{rybq12,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd5 != null" >
			  #{qtzd5,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm5 != null" >
			  #{jbdm5,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq5 != null" >
			  #{rybq5,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd13 != null" >
			  #{qtzd13,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm13 != null" >
			  #{jbdm13,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq13 != null" >
			  #{rybq13,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd6 != null" >
			  #{qtzd6,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm6 != null" >
			  #{jbdm6,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq6 != null" >
			  #{rybq6,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd14 != null" >
			  #{qtzd14,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm14 != null" >
			  #{jbdm14,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq14 != null" >
			  #{rybq14,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd7 != null" >
			  #{qtzd7,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm7 != null" >
			  #{jbdm7,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq7 != null" >
			  #{rybq7,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzd15 != null" >
			  #{qtzd15,jdbcType=VARCHAR},
		  </if>
		  <if test="jbdm15 != null" >
			  #{jbdm15,jdbcType=VARCHAR},
		  </if>
		  <if test="rybq15 != null" >
			  #{rybq15,jdbcType=VARCHAR},
		  </if>
		  <if test="wbyy != null" >
			  #{wbyy,jdbcType=VARCHAR},
		  </if>
		  <if test="h23 != null" >
			  #{h23,jdbcType=VARCHAR},
		  </if>
		  <if test="blzd != null" >
			  #{blzd,jdbcType=VARCHAR},
		  </if>
		  <if test="jbmm != null" >
			  #{jbmm,jdbcType=VARCHAR},
		  </if>
		  <if test="blh != null" >
			  #{blh,jdbcType=VARCHAR},
		  </if>
		  <if test="ywgm != null" >
			  #{ywgm,jdbcType=VARCHAR},
		  </if>
		  <if test="gmyw != null" >
			  #{gmyw,jdbcType=VARCHAR},
		  </if>
		  <if test="swhzsj != null" >
			  #{swhzsj,jdbcType=VARCHAR},
		  </if>
		  <if test="xx != null" >
			  #{xx,jdbcType=VARCHAR},
		  </if>
		  <if test="rh != null" >
			  #{rh,jdbcType=VARCHAR},
		  </if>
		  <if test="kzr != null" >
			  #{kzr,jdbcType=VARCHAR},
		  </if>
		  <if test="zrys != null" >
			  #{zrys,jdbcType=VARCHAR},
		  </if>
		  <if test="zzys != null" >
			  #{zzys,jdbcType=VARCHAR},
		  </if>
		  <if test="zyys != null" >
			  #{zyys,jdbcType=VARCHAR},
		  </if>
		  <if test="zrhs != null" >
			  #{zrhs,jdbcType=VARCHAR},
		  </if>
		  <if test="jxys != null" >
			  #{jxys,jdbcType=VARCHAR},
		  </if>
		  <if test="sxys != null" >
			  #{sxys,jdbcType=VARCHAR},
		  </if>
		  <if test="bmy != null" >
			  #{bmy,jdbcType=VARCHAR},
		  </if>
		  <if test="bazl != null" >
			  #{bazl,jdbcType=VARCHAR},
		  </if>
		  <if test="zkys != null" >
			  #{zkys,jdbcType=VARCHAR},
		  </if>
		  <if test="zkhs != null" >
			  #{zkhs,jdbcType=VARCHAR},
		  </if>
		  <if test="zkrq != null" >
			  #{zkrq,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczbm1 != null" >
			  #{ssjczbm1,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczrq1 != null" >
			  #{ssjczrq1,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjb1 != null" >
			  #{ssjb1,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczmc1 != null" >
			  #{ssjczmc1,jdbcType=VARCHAR},
		  </if>
		  <if test="sz1 != null" >
			  #{sz1,jdbcType=VARCHAR},
		  </if>
		  <if test="yz1 != null" >
			  #{yz1,jdbcType=VARCHAR},
		  </if>
		  <if test="ez1 != null" >
			  #{ez1,jdbcType=VARCHAR},
		  </if>
		  <if test="qkdj1 != null" >
			  #{qkdj1,jdbcType=VARCHAR},
		  </if>
		  <if test="qkyhlb1 != null" >
			  #{qkyhlb1,jdbcType=VARCHAR},
		  </if>
		  <if test="mzfs1 != null" >
			  #{mzfs1,jdbcType=VARCHAR},
		  </if>
		  <if test="mzys1 != null" >
			  #{mzys1,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczbm2 != null" >
			  #{ssjczbm2,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczrq2 != null" >
			  #{ssjczrq2,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjb2 != null" >
			  #{ssjb2,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczmc2 != null" >
			  #{ssjczmc2,jdbcType=VARCHAR},
		  </if>
		  <if test="sz2 != null" >
			  #{sz2,jdbcType=VARCHAR},
		  </if>
		  <if test="yz2 != null" >
			  #{yz2,jdbcType=VARCHAR},
		  </if>
		  <if test="ez2 != null" >
			  #{ez2,jdbcType=VARCHAR},
		  </if>
		  <if test="qkdj2 != null" >
			  #{qkdj2,jdbcType=VARCHAR},
		  </if>
		  <if test="qkyhlb2 != null" >
			  #{qkyhlb2,jdbcType=VARCHAR},
		  </if>
		  <if test="mzfs2 != null" >
			  #{mzfs2,jdbcType=VARCHAR},
		  </if>
		  <if test="mzys2 != null" >
			  #{mzys2,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczbm3 != null" >
			  #{ssjczbm3,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczrq3 != null" >
			  #{ssjczrq3,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjb3 != null" >
			  #{ssjb3,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczmc3 != null" >
			  #{ssjczmc3,jdbcType=VARCHAR},
		  </if>
		  <if test="sz3 != null" >
			  #{sz3,jdbcType=VARCHAR},
		  </if>
		  <if test="yz3 != null" >
			  #{yz3,jdbcType=VARCHAR},
		  </if>
		  <if test="ez3 != null" >
			  #{ez3,jdbcType=VARCHAR},
		  </if>
		  <if test="qkdj3 != null" >
			  #{qkdj3,jdbcType=VARCHAR},
		  </if>
		  <if test="qkyhlb3 != null" >
			  #{qkyhlb3,jdbcType=VARCHAR},
		  </if>
		  <if test="mzfs3 != null" >
			  #{mzfs3,jdbcType=VARCHAR},
		  </if>
		  <if test="mzys3 != null" >
			  #{mzys3,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczbm4 != null" >
			  #{ssjczbm4,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczrq4 != null" >
			  #{ssjczrq4,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjb4 != null" >
			  #{ssjb4,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczmc4 != null" >
			  #{ssjczmc4,jdbcType=VARCHAR},
		  </if>
		  <if test="sz4 != null" >
			  #{sz4,jdbcType=VARCHAR},
		  </if>
		  <if test="yz4 != null" >
			  #{yz4,jdbcType=VARCHAR},
		  </if>
		  <if test="ez4 != null" >
			  #{ez4,jdbcType=VARCHAR},
		  </if>
		  <if test="qkdj4 != null" >
			  #{qkdj4,jdbcType=VARCHAR},
		  </if>
		  <if test="qkyhlb4 != null" >
			  #{qkyhlb4,jdbcType=VARCHAR},
		  </if>
		  <if test="mzfs4 != null" >
			  #{mzfs4,jdbcType=VARCHAR},
		  </if>
		  <if test="mzys4 != null" >
			  #{mzys4,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczbm5 != null" >
			  #{ssjczbm5,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczrq5 != null" >
			  #{ssjczrq5,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjb5 != null" >
			  #{ssjb5,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczmc5 != null" >
			  #{ssjczmc5,jdbcType=VARCHAR},
		  </if>
		  <if test="sz5 != null" >
			  #{sz5,jdbcType=VARCHAR},
		  </if>
		  <if test="yz5 != null" >
			  #{yz5,jdbcType=VARCHAR},
		  </if>
		  <if test="ez5 != null" >
			  #{ez5,jdbcType=VARCHAR},
		  </if>
		  <if test="qkdj5 != null" >
			  #{qkdj5,jdbcType=VARCHAR},
		  </if>
		  <if test="qkyhlb5 != null" >
			  #{qkyhlb5,jdbcType=VARCHAR},
		  </if>
		  <if test="mzfs5 != null" >
			  #{mzfs5,jdbcType=VARCHAR},
		  </if>
		  <if test="mzys5 != null" >
			  #{mzys5,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczbm6 != null" >
			  #{ssjczbm6,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczrq6 != null" >
			  #{ssjczrq6,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjb6 != null" >
			  #{ssjb6,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczmc6 != null" >
			  #{ssjczmc6,jdbcType=VARCHAR},
		  </if>
		  <if test="sz6 != null" >
			  #{sz6,jdbcType=VARCHAR},
		  </if>
		  <if test="yz6 != null" >
			  #{yz6,jdbcType=VARCHAR},
		  </if>
		  <if test="ez6 != null" >
			  #{ez6,jdbcType=VARCHAR},
		  </if>
		  <if test="qkdj6 != null" >
			  #{qkdj6,jdbcType=VARCHAR},
		  </if>
		  <if test="qkyhlb6 != null" >
			  #{qkyhlb6,jdbcType=VARCHAR},
		  </if>
		  <if test="mzfs6 != null" >
			  #{mzfs6,jdbcType=VARCHAR},
		  </if>
		  <if test="mzys6 != null" >
			  #{mzys6,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczbm7 != null" >
			  #{ssjczbm7,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczrq7 != null" >
			  #{ssjczrq7,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjb7 != null" >
			  #{ssjb7,jdbcType=VARCHAR},
		  </if>
		  <if test="ssjczmc7 != null" >
			  #{ssjczmc7,jdbcType=VARCHAR},
		  </if>
		  <if test="sz7 != null" >
			  #{sz7,jdbcType=VARCHAR},
		  </if>
		  <if test="yz7 != null" >
			  #{yz7,jdbcType=VARCHAR},
		  </if>
		  <if test="ez7 != null" >
			  #{ez7,jdbcType=VARCHAR},
		  </if>
		  <if test="qkdj7 != null" >
			  #{qkdj7,jdbcType=VARCHAR},
		  </if>
		  <if test="qkyhlb7 != null" >
			  #{qkyhlb7,jdbcType=VARCHAR},
		  </if>
		  <if test="mzfs7 != null" >
			  #{mzfs7,jdbcType=VARCHAR},
		  </if>
		  <if test="mzys7 != null" >
			  #{mzys7,jdbcType=VARCHAR},
		  </if>
		  <if test="lyfs != null" >
			  #{lyfs,jdbcType=VARCHAR},
		  </if>
		  <if test="yzzyYljg != null" >
			  #{yzzyYljg,jdbcType=VARCHAR},
		  </if>
		  <if test="wsyYljg != null" >
			  #{wsyYljg,jdbcType=VARCHAR},
		  </if>
		  <if test="sfzzyjh != null" >
			  #{sfzzyjh,jdbcType=VARCHAR},
		  </if>
		  <if test="md != null" >
			  #{md,jdbcType=VARCHAR},
		  </if>
		  <if test="ryqT != null" >
			  #{ryqT,jdbcType=DECIMAL},
		  </if>
		  <if test="ryqXs != null" >
			  #{ryqXs,jdbcType=DECIMAL},
		  </if>
		  <if test="ryqF != null" >
			  #{ryqF,jdbcType=DECIMAL},
		  </if>
		  <if test="ryhT != null" >
			  #{ryhT,jdbcType=DECIMAL},
		  </if>
		  <if test="ryhXs != null" >
			  #{ryhXs,jdbcType=DECIMAL},
		  </if>
		  <if test="ryhF != null" >
			  #{ryhF,jdbcType=DECIMAL},
		  </if>
		  <if test="zfy != null" >
			  #{zfy,jdbcType=DECIMAL},
		  </if>
		  <if test="zfje != null" >
			  #{zfje,jdbcType=DECIMAL},
		  </if>
		  <if test="ylfuf != null" >
			  #{ylfuf,jdbcType=DECIMAL},
		  </if>
		  <if test="zlczf != null" >
			  #{zlczf,jdbcType=DECIMAL},
		  </if>
		  <if test="hlf != null" >
			  #{hlf,jdbcType=DECIMAL},
		  </if>
		  <if test="qtfy != null" >
			  #{qtfy,jdbcType=DECIMAL},
		  </if>
		  <if test="blzdf != null" >
			  #{blzdf,jdbcType=DECIMAL},
		  </if>
		  <if test="syszdf != null" >
			  #{syszdf,jdbcType=DECIMAL},
		  </if>
		  <if test="yxxzdf != null" >
			  #{yxxzdf,jdbcType=DECIMAL},
		  </if>
		  <if test="lczdxmf != null" >
			  #{lczdxmf,jdbcType=DECIMAL},
		  </if>
		  <if test="fsszlxmf != null" >
			  #{fsszlxmf,jdbcType=DECIMAL},
		  </if>
		  <if test="wlzlf != null" >
			  #{wlzlf,jdbcType=DECIMAL},
		  </if>
		  <if test="sszlf != null" >
			  #{sszlf,jdbcType=DECIMAL},
		  </if>
		  <if test="maf != null" >
			  #{maf,jdbcType=DECIMAL},
		  </if>
		  <if test="ssf != null" >
			  #{ssf,jdbcType=DECIMAL},
		  </if>
		  <if test="kff != null" >
			  #{kff,jdbcType=DECIMAL},
		  </if>
		  <if test="zyzlf != null" >
			  #{zyzlf,jdbcType=DECIMAL},
		  </if>
		  <if test="xyf != null" >
			  #{xyf,jdbcType=DECIMAL},
		  </if>
		  <if test="kjywf != null" >
			  #{kjywf,jdbcType=DECIMAL},
		  </if>
		  <if test="zcyf != null" >
			  #{zcyf,jdbcType=DECIMAL},
		  </if>
		  <if test="zcyf1 != null" >
			  #{zcyf1,jdbcType=DECIMAL},
		  </if>
		  <if test="xf != null" >
			  #{xf,jdbcType=DECIMAL},
		  </if>
		  <if test="bdblzpf != null" >
			  #{bdblzpf,jdbcType=DECIMAL},
		  </if>
		  <if test="qdblzpf != null" >
			  #{qdblzpf,jdbcType=DECIMAL},
		  </if>
		  <if test="nxyzlzpf != null" >
			  #{nxyzlzpf,jdbcType=DECIMAL},
		  </if>
		  <if test="xbyzlzpf != null" >
			  #{xbyzlzpf,jdbcType=DECIMAL},
		  </if>
		  <if test="hcyyclf != null" >
			  #{hcyyclf,jdbcType=DECIMAL},
		  </if>
		  <if test="yyclf != null" >
			  #{yyclf,jdbcType=DECIMAL},
		  </if>
		  <if test="ycxyyclf != null" >
			  #{ycxyyclf,jdbcType=DECIMAL},
		  </if>
		  <if test="qtf != null" >
			  #{qtf,jdbcType=DECIMAL},
		  </if>
		  <if test="ryqk != null" >
			  #{ryqk,jdbcType=VARCHAR},
		  </if>
		  <if test="ryzd != null" >
			  #{ryzd,jdbcType=VARCHAR},
		  </if>
		  <if test="zyzdqzrq != null" >
			  #{zyzdqzrq,jdbcType=VARCHAR},
		  </if>
		  <if test="jbbm1 != null" >
			  #{jbbm1,jdbcType=VARCHAR},
		  </if>
		  <if test="bwhbz != null" >
			  #{bwhbz,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk != null" >
			  #{cyqk,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk1 != null" >
			  #{cyqk1,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk2 != null" >
			  #{cyqk2,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk3 != null" >
			  #{cyqk3,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk4 != null" >
			  #{cyqk4,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk5 != null" >
			  #{cyqk5,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk6 != null" >
			  #{cyqk6,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk7 != null" >
			  #{cyqk7,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk8 != null" >
			  #{cyqk8,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk9 != null" >
			  #{cyqk9,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk10 != null" >
			  #{cyqk10,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk11 != null" >
			  #{cyqk11,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk12 != null" >
			  #{cyqk12,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk13 != null" >
			  #{cyqk13,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk14 != null" >
			  #{cyqk14,jdbcType=VARCHAR},
		  </if>
		  <if test="cyqk15 != null" >
			  #{cyqk15,jdbcType=VARCHAR},
		  </if>
		  <if test="sz != null" >
			  #{sz,jdbcType=VARCHAR},
		  </if>
		  <if test="szqxz != null" >
			  #{szqxz,jdbcType=VARCHAR},
		  </if>
		  <if test="szqxy != null" >
			  #{szqxy,jdbcType=VARCHAR},
		  </if>
		  <if test="szqxn != null" >
			  #{szqxn,jdbcType=VARCHAR},
		  </if>
		  <if test="yykjyw != null" >
			  #{yykjyw,jdbcType=VARCHAR},
		  </if>
		  <if test="sycxsj != null" >
			  #{sycxsj,jdbcType=VARCHAR},
		  </if>
		  <if test="lhyy != null" >
			  #{lhyy,jdbcType=VARCHAR},
		  </if>
		  <if test="lcljgl != null" >
			  #{lcljgl,jdbcType=VARCHAR},
		  </if>
		  <if test="wclclj != null" >
			  #{wclclj,jdbcType=VARCHAR},
		  </if>
		  <if test="tcyy != null" >
			  #{tcyy,jdbcType=VARCHAR},
		  </if>
		  <if test="sfby != null" >
			  #{sfby,jdbcType=VARCHAR},
		  </if>
		  <if test="byyy != null" >
			  #{byyy,jdbcType=VARCHAR},
		  </if>
		  <if test="tnmfq != null" >
			  #{tnmfq,jdbcType=VARCHAR},
		  </if>
		  <if test="tzbzry != null" >
			  #{tzbzry,jdbcType=VARCHAR},
		  </if>
		  <if test="cyrqjgts != null" >
			  #{cyrqjgts,jdbcType=VARCHAR},
		  </if>
		  <if test="qtzf != null" >
			  #{qtzf,jdbcType=VARCHAR},
		  </if>
		  <if test="dwfzr != null" >
			  #{dwfzr,jdbcType=VARCHAR},
		  </if>
		  <if test="tjfzr != null" >
			  #{tjfzr,jdbcType=VARCHAR},
		  </if>
		  <if test="tbr != null" >
			  #{tbr,jdbcType=VARCHAR},
		  </if>
		  <if test="lxdh != null" >
			  #{lxdh,jdbcType=VARCHAR},
		  </if>
		  <if test="sj != null" >
			  #{sj,jdbcType=VARCHAR},
		  </if>
		  <if test="bcrq != null" >
			  #{bcrq,jdbcType=VARCHAR},
		  </if>
		  <if test="xzzXzqh != null" >
			  #{xzzXzqh,jdbcType=VARCHAR},
		  </if>
		  <if test="hkdzXzqh != null" >
			  #{hkdzXzqh,jdbcType=VARCHAR},
		  </if>
		  <if test="dwdzXzqh != null" >
			  #{dwdzXzqh,jdbcType=VARCHAR},
		  </if>
		  <if test="dzXzqh != null" >
			  #{dzXzqh,jdbcType=VARCHAR},
		  </if>
		  <if test="tjfzrDh != null" >
			  #{tjfzrDh,jdbcType=VARCHAR},
		  </if>
		  <if test="zznys != null" >
			  #{zznys,jdbcType=VARCHAR},
		  </if>
		  <if test="qtjgzr != null" >
			  #{qtjgzr,jdbcType=VARCHAR},
		  </if>
		  <if test="bzyzsnlr != null">
			  #{bzyzsnlr},
		  </if>
		  <if test="rysjf != null">
			  #{rysjf},
		  </if>
		  <if test="cysjf != null">
			  #{cysjf},
		  </if>
		  <if test="hxb != null">
			  #{hxb},
		  </if>
		  <if test="xxb != null">
			  #{xxb},
		  </if>
		  <if test="xj != null">
			  #{xj},
		  </if>
		  <if test="qx != null">
			  #{qx},
		  </if>
		  <if test="ztxhs != null">
			  #{ztxhs},
		  </if>
		  <if test="bdb != null">
			  #{bdb},
		  </if>
		  <if test="lcd != null">
			  #{lcd},
		  </if>
		  <if test="qt != null">
			  #{qt},
		  </if>
		  <if test="sxfy != null">
			  #{sxfy},
		  </if>
		  <if test="sf1 != null">
			  #{sf1},
		  </if>
		  <if test="sf2 != null">
			  #{sf2},
		  </if>
		  <if test="sf3 != null">
			  #{sf3},
		  </if>
		  <if test="sf4 != null">
			  #{sf4},
		  </if>
		  <if test="sf5 != null">
			  #{sf5},
		  </if>
		  <if test="sf6 != null">
			  #{sf6},
		  </if>
		  <if test="sf7 != null">
			  #{sf7},
		  </if>
		  <if test="ct != null">
			  #{ct},
		  </if>
		  <if test="petct != null">
			  #{petct},
		  </if>
		  <if test="syct != null">
			  #{syct},
		  </if>
		  <if test="bc != null">
			  #{bc},
		  </if>
		  <if test="xp != null">
			  #{xp},
		  </if>
		  <if test="csxdt != null">
			  #{csxdt},
		  </if>
		  <if test="mri != null">
			  #{mri},
		  </if>
		  <if test="twsjc != null">
			  #{twsjc},
		  </if>
		  <if test="yljgbm != null" >
			  #{yljgbm,jdbcType=VARCHAR},
		  </if>
	  </trim>

  </insert>

	<insert id="insert" parameterType="java.util.List" >
		<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
			insert into BAGL_WTSB_XY
			<trim prefix="(" suffix=")" suffixOverrides="," >
				<if test="item.username != null" >
					USERNAME,
				</if>
				<if test="item.ylfkfs != null" >
					YLFKFS,
				</if>
				<if test="item.jkkh != null" >
					JKKH,
				</if>
				<if test="item.zycs != null" >
					ZYCS,
				</if>
				<if test="item.bah != null" >
					BAH,
				</if>
				<if test="item.xm != null" >
					XM,
				</if>
				<if test="item.xb != null" >
					XB,
				</if>
				<if test="item.csrq != null" >
					CSRQ,
				</if>
				<if test="item.nl != null" >
					NL,
				</if>
				<if test="item.gj != null" >
					GJ,
				</if>
				<if test="item.bzyzsnl != null" >
					BZYZSNL,
				</if>
				<if test="item.xsecstz != null" >
					XSECSTZ,
				</if>
				<if test="item.xserytz != null" >
					XSERYTZ,
				</if>
				<if test="item.csd != null" >
					CSD,
				</if>
				<if test="item.gg != null" >
					GG,
				</if>
				<if test="item.mz != null" >
					MZ,
				</if>
				<if test="item.sfzh != null" >
					SFZH,
				</if>
				<if test="item.zy != null" >
					ZY,
				</if>
				<if test="item.hy != null" >
					HY,
				</if>
				<if test="item.xzz != null" >
					XZZ,
				</if>
				<if test="item.dh != null" >
					DH,
				</if>
				<if test="item.yb1 != null" >
					YB1,
				</if>
				<if test="item.hkdz != null" >
					HKDZ,
				</if>
				<if test="item.yb2 != null" >
					YB2,
				</if>
				<if test="item.gzdwjdz != null" >
					GZDWJDZ,
				</if>
				<if test="item.dwdh != null" >
					DWDH,
				</if>
				<if test="item.yb3 != null" >
					YB3,
				</if>
				<if test="item.lxrxm != null" >
					LXRXM,
				</if>
				<if test="item.gx != null" >
					GX,
				</if>
				<if test="item.dz != null" >
					DZ,
				</if>
				<if test="item.dh2 != null" >
					DH2,
				</if>
				<if test="item.rytj != null" >
					RYTJ,
				</if>
				<if test="item.rysj != null" >
					RYSJ,
				</if>
				<if test="item.rysjs != null" >
					RYSJS,
				</if>
				<if test="item.rykb != null" >
					RYKB,
				</if>
				<if test="item.rybf != null" >
					RYBF,
				</if>
				<if test="item.zkkb != null" >
					ZKKB,
				</if>
				<if test="item.cysj != null" >
					CYSJ,
				</if>
				<if test="item.cysjs != null" >
					CYSJS,
				</if>
				<if test="item.cykb != null" >
					CYKB,
				</if>
				<if test="item.cybf != null" >
					CYBF,
				</if>
				<if test="item.sjzyts != null" >
					SJZYTS,
				</if>
				<if test="item.mzzd != null" >
					MZZD,
				</if>
				<if test="item.jbbm != null" >
					JBBM,
				</if>
				<if test="item.zyzd != null" >
					ZYZD,
				</if>
				<if test="item.jbdm != null" >
					JBDM,
				</if>
				<if test="item.rybq != null" >
					RYBQ,
				</if>
				<if test="item.qtzd8 != null" >
					QTZD8,
				</if>
				<if test="item.jbdm8 != null" >
					JBDM8,
				</if>
				<if test="item.rybq8 != null" >
					RYBQ8,
				</if>
				<if test="item.qtzd1 != null" >
					QTZD1,
				</if>
				<if test="item.jbdm1 != null" >
					JBDM1,
				</if>
				<if test="item.rybq1 != null" >
					RYBQ1,
				</if>
				<if test="item.qtzd9 != null" >
					QTZD9,
				</if>
				<if test="item.jbdm9 != null" >
					JBDM9,
				</if>
				<if test="item.rybq9 != null" >
					RYBQ9,
				</if>
				<if test="item.qtzd2 != null" >
					QTZD2,
				</if>
				<if test="item.jbdm2 != null" >
					JBDM2,
				</if>
				<if test="item.rybq2 != null" >
					RYBQ2,
				</if>
				<if test="item.qtzd10 != null" >
					QTZD10,
				</if>
				<if test="item.jbdm10 != null" >
					JBDM10,
				</if>
				<if test="item.rybq10 != null" >
					RYBQ10,
				</if>
				<if test="item.qtzd3 != null" >
					QTZD3,
				</if>
				<if test="item.jbdm3 != null" >
					JBDM3,
				</if>
				<if test="item.rybq3 != null" >
					RYBQ3,
				</if>
				<if test="item.qtzd11 != null" >
					QTZD11,
				</if>
				<if test="item.jbdm11 != null" >
					JBDM11,
				</if>
				<if test="item.rybq11 != null" >
					RYBQ11,
				</if>
				<if test="item.qtzd4 != null" >
					QTZD4,
				</if>
				<if test="item.jbdm4 != null" >
					JBDM4,
				</if>
				<if test="item.rybq4 != null" >
					RYBQ4,
				</if>
				<if test="item.qtzd12 != null" >
					QTZD12,
				</if>
				<if test="item.jbdm12 != null" >
					JBDM12,
				</if>
				<if test="item.rybq12 != null" >
					RYBQ12,
				</if>
				<if test="item.qtzd5 != null" >
					QTZD5,
				</if>
				<if test="item.jbdm5 != null" >
					JBDM5,
				</if>
				<if test="item.rybq5 != null" >
					RYBQ5,
				</if>
				<if test="item.qtzd13 != null" >
					QTZD13,
				</if>
				<if test="item.jbdm13 != null" >
					JBDM13,
				</if>
				<if test="item.rybq13 != null" >
					RYBQ13,
				</if>
				<if test="item.qtzd6 != null" >
					QTZD6,
				</if>
				<if test="item.jbdm6 != null" >
					JBDM6,
				</if>
				<if test="item.rybq6 != null" >
					RYBQ6,
				</if>
				<if test="item.qtzd14 != null" >
					QTZD14,
				</if>
				<if test="item.jbdm14 != null" >
					JBDM14,
				</if>
				<if test="item.rybq14 != null" >
					RYBQ14,
				</if>
				<if test="item.qtzd7 != null" >
					QTZD7,
				</if>
				<if test="item.jbdm7 != null" >
					JBDM7,
				</if>
				<if test="item.rybq7 != null" >
					RYBQ7,
				</if>
				<if test="item.qtzd15 != null" >
					QTZD15,
				</if>
				<if test="item.jbdm15 != null" >
					JBDM15,
				</if>
				<if test="item.rybq15 != null" >
					RYBQ15,
				</if>
				<if test="item.wbyy != null" >
					WBYY,
				</if>
				<if test="item.h23 != null" >
					H23,
				</if>
				<if test="item.blzd != null" >
					BLZD,
				</if>
				<if test="item.jbmm != null" >
					JBMM,
				</if>
				<if test="item.blh != null" >
					BLH,
				</if>
				<if test="item.ywgm != null" >
					YWGM,
				</if>
				<if test="item.gmyw != null" >
					GMYW,
				</if>
				<if test="item.swhzsj != null" >
					SWHZSJ,
				</if>
				<if test="item.xx != null" >
					XX,
				</if>
				<if test="item.rh != null" >
					RH,
				</if>
				<if test="item.kzr != null" >
					KZR,
				</if>
				<if test="item.zrys != null" >
					ZRYS,
				</if>
				<if test="item.zzys != null" >
					ZZYS,
				</if>
				<if test="item.zyys != null" >
					ZYYS,
				</if>
				<if test="item.zrhs != null" >
					ZRHS,
				</if>
				<if test="item.jxys != null" >
					JXYS,
				</if>
				<if test="item.sxys != null" >
					SXYS,
				</if>
				<if test="item.bmy != null" >
					BMY,
				</if>
				<if test="item.bazl != null" >
					BAZL,
				</if>
				<if test="item.zkys != null" >
					ZKYS,
				</if>
				<if test="item.zkhs != null" >
					ZKHS,
				</if>
				<if test="item.zkrq != null" >
					ZKRQ,
				</if>
				<if test="item.ssjczbm1 != null" >
					SSJCZBM1,
				</if>
				<if test="item.ssjczrq1 != null" >
					SSJCZRQ1,
				</if>
				<if test="item.ssjb1 != null" >
					SSJB1,
				</if>
				<if test="item.ssjczmc1 != null" >
					SSJCZMC1,
				</if>
				<if test="item.sz1 != null" >
					SZ1,
				</if>
				<if test="item.yz1 != null" >
					YZ1,
				</if>
				<if test="item.ez1 != null" >
					EZ1,
				</if>
				<if test="item.qkdj1 != null" >
					QKDJ1,
				</if>
				<if test="item.qkyhlb1 != null" >
					QKYHLB1,
				</if>
				<if test="item.mzfs1 != null" >
					MZFS1,
				</if>
				<if test="item.mzys1 != null" >
					MZYS1,
				</if>
				<if test="item.ssjczbm2 != null" >
					SSJCZBM2,
				</if>
				<if test="item.ssjczrq2 != null" >
					SSJCZRQ2,
				</if>
				<if test="item.ssjb2 != null" >
					SSJB2,
				</if>
				<if test="item.ssjczmc2 != null" >
					SSJCZMC2,
				</if>
				<if test="item.sz2 != null" >
					SZ2,
				</if>
				<if test="item.yz2 != null" >
					YZ2,
				</if>
				<if test="item.ez2 != null" >
					EZ2,
				</if>
				<if test="item.qkdj2 != null" >
					QKDJ2,
				</if>
				<if test="item.qkyhlb2 != null" >
					QKYHLB2,
				</if>
				<if test="item.mzfs2 != null" >
					MZFS2,
				</if>
				<if test="item.mzys2 != null" >
					MZYS2,
				</if>
				<if test="item.ssjczbm3 != null" >
					SSJCZBM3,
				</if>
				<if test="item.ssjczrq3 != null" >
					SSJCZRQ3,
				</if>
				<if test="item.ssjb3 != null" >
					SSJB3,
				</if>
				<if test="item.ssjczmc3 != null" >
					SSJCZMC3,
				</if>
				<if test="item.sz3 != null" >
					SZ3,
				</if>
				<if test="item.yz3 != null" >
					YZ3,
				</if>
				<if test="item.ez3 != null" >
					EZ3,
				</if>
				<if test="item.qkdj3 != null" >
					QKDJ3,
				</if>
				<if test="item.qkyhlb3 != null" >
					QKYHLB3,
				</if>
				<if test="item.mzfs3 != null" >
					MZFS3,
				</if>
				<if test="item.mzys3 != null" >
					MZYS3,
				</if>
				<if test="item.ssjczbm4 != null" >
					SSJCZBM4,
				</if>
				<if test="item.ssjczrq4 != null" >
					SSJCZRQ4,
				</if>
				<if test="item.ssjb4 != null" >
					SSJB4,
				</if>
				<if test="item.ssjczmc4 != null" >
					SSJCZMC4,
				</if>
				<if test="item.sz4 != null" >
					SZ4,
				</if>
				<if test="item.yz4 != null" >
					YZ4,
				</if>
				<if test="item.ez4 != null" >
					EZ4,
				</if>
				<if test="item.qkdj4 != null" >
					QKDJ4,
				</if>
				<if test="item.qkyhlb4 != null" >
					QKYHLB4,
				</if>
				<if test="item.mzfs4 != null" >
					MZFS4,
				</if>
				<if test="item.mzys4 != null" >
					MZYS4,
				</if>
				<if test="item.ssjczbm5 != null" >
					SSJCZBM5,
				</if>
				<if test="item.ssjczrq5 != null" >
					SSJCZRQ5,
				</if>
				<if test="item.ssjb5 != null" >
					SSJB5,
				</if>
				<if test="item.ssjczmc5 != null" >
					SSJCZMC5,
				</if>
				<if test="item.sz5 != null" >
					SZ5,
				</if>
				<if test="item.yz5 != null" >
					YZ5,
				</if>
				<if test="item.ez5 != null" >
					EZ5,
				</if>
				<if test="item.qkdj5 != null" >
					QKDJ5,
				</if>
				<if test="item.qkyhlb5 != null" >
					QKYHLB5,
				</if>
				<if test="item.mzfs5 != null" >
					MZFS5,
				</if>
				<if test="item.mzys5 != null" >
					MZYS5,
				</if>
				<if test="item.ssjczbm6 != null" >
					SSJCZBM6,
				</if>
				<if test="item.ssjczrq6 != null" >
					SSJCZRQ6,
				</if>
				<if test="item.ssjb6 != null" >
					SSJB6,
				</if>
				<if test="item.ssjczmc6 != null" >
					SSJCZMC6,
				</if>
				<if test="item.sz6 != null" >
					SZ6,
				</if>
				<if test="item.yz6 != null" >
					YZ6,
				</if>
				<if test="item.ez6 != null" >
					EZ6,
				</if>
				<if test="item.qkdj6 != null" >
					QKDJ6,
				</if>
				<if test="item.qkyhlb6 != null" >
					QKYHLB6,
				</if>
				<if test="item.mzfs6 != null" >
					MZFS6,
				</if>
				<if test="item.mzys6 != null" >
					MZYS6,
				</if>
				<if test="item.ssjczbm7 != null" >
					SSJCZBM7,
				</if>
				<if test="item.ssjczrq7 != null" >
					SSJCZRQ7,
				</if>
				<if test="item.ssjb7 != null" >
					SSJB7,
				</if>
				<if test="item.ssjczmc7 != null" >
					SSJCZMC7,
				</if>
				<if test="item.sz7 != null" >
					SZ7,
				</if>
				<if test="item.yz7 != null" >
					YZ7,
				</if>
				<if test="item.ez7 != null" >
					EZ7,
				</if>
				<if test="item.qkdj7 != null" >
					QKDJ7,
				</if>
				<if test="item.qkyhlb7 != null" >
					QKYHLB7,
				</if>
				<if test="item.mzfs7 != null" >
					MZFS7,
				</if>
				<if test="item.mzys7 != null" >
					MZYS7,
				</if>
				<if test="item.lyfs != null" >
					LYFS,
				</if>
				<if test="item.yzzyYljg != null" >
					YZZY_YLJG,
				</if>
				<if test="item.wsyYljg != null" >
					WSY_YLJG,
				</if>
				<if test="item.sfzzyjh != null" >
					SFZZYJH,
				</if>
				<if test="item.md != null" >
					MD,
				</if>
				<if test="item.ryqT != null" >
					RYQ_T,
				</if>
				<if test="item.ryqXs != null" >
					RYQ_XS,
				</if>
				<if test="item.ryqF != null" >
					RYQ_F,
				</if>
				<if test="item.ryhT != null" >
					RYH_T,
				</if>
				<if test="item.ryhXs != null" >
					RYH_XS,
				</if>
				<if test="item.ryhF != null" >
					RYH_F,
				</if>
				<if test="item.zfy != null" >
					ZFY,
				</if>
				<if test="item.zfje != null" >
					ZFJE,
				</if>
				<if test="item.ylfuf != null" >
					YLFUF,
				</if>
				<if test="item.zlczf != null" >
					ZLCZF,
				</if>
				<if test="item.hlf != null" >
					HLF,
				</if>
				<if test="item.qtfy != null" >
					QTFY,
				</if>
				<if test="item.blzdf != null" >
					BLZDF,
				</if>
				<if test="item.syszdf != null" >
					SYSZDF,
				</if>
				<if test="item.yxxzdf != null" >
					YXXZDF,
				</if>
				<if test="item.lczdxmf != null" >
					LCZDXMF,
				</if>
				<if test="item.fsszlxmf != null" >
					FSSZLXMF,
				</if>
				<if test="item.wlzlf != null" >
					WLZLF,
				</if>
				<if test="item.sszlf != null" >
					SSZLF,
				</if>
				<if test="item.maf != null" >
					MAF,
				</if>
				<if test="item.ssf != null" >
					SSF,
				</if>
				<if test="item.kff != null" >
					KFF,
				</if>
				<if test="item.zyzlf != null" >
					ZYZLF,
				</if>
				<if test="item.xyf != null" >
					XYF,
				</if>
				<if test="item.kjywf != null" >
					KJYWF,
				</if>
				<if test="item.zcyf != null" >
					ZCYF,
				</if>
				<if test="item.zcyf1 != null" >
					ZCYF1,
				</if>
				<if test="item.xf != null" >
					XF,
				</if>
				<if test="item.bdblzpf != null" >
					BDBLZPF,
				</if>
				<if test="item.qdblzpf != null" >
					QDBLZPF,
				</if>
				<if test="item.nxyzlzpf != null" >
					NXYZLZPF,
				</if>
				<if test="item.xbyzlzpf != null" >
					XBYZLZPF,
				</if>
				<if test="item.hcyyclf != null" >
					HCYYCLF,
				</if>
				<if test="item.yyclf != null" >
					YYCLF,
				</if>
				<if test="item.ycxyyclf != null" >
					YCXYYCLF,
				</if>
				<if test="item.qtf != null" >
					QTF,
				</if>
				<if test="item.ryqk != null" >
					RYQK,
				</if>
				<if test="item.ryzd != null" >
					RYZD,
				</if>
				<if test="item.zyzdqzrq != null" >
					ZYZDQZRQ,
				</if>
				<if test="item.jbbm1 != null" >
					JBBM1,
				</if>
				<if test="item.bwhbz != null" >
					BWHBZ,
				</if>
				<if test="item.cyqk != null" >
					CYQK,
				</if>
				<if test="item.cyqk1 != null" >
					CYQK1,
				</if>
				<if test="item.cyqk2 != null" >
					CYQK2,
				</if>
				<if test="item.cyqk3 != null" >
					CYQK3,
				</if>
				<if test="item.cyqk4 != null" >
					CYQK4,
				</if>
				<if test="item.cyqk5 != null" >
					CYQK5,
				</if>
				<if test="item.cyqk6 != null" >
					CYQK6,
				</if>
				<if test="item.cyqk7 != null" >
					CYQK7,
				</if>
				<if test="item.cyqk8 != null" >
					CYQK8,
				</if>
				<if test="item.cyqk9 != null" >
					CYQK9,
				</if>
				<if test="item.cyqk10 != null" >
					CYQK10,
				</if>
				<if test="item.cyqk11 != null" >
					CYQK11,
				</if>
				<if test="item.cyqk12 != null" >
					CYQK12,
				</if>
				<if test="item.cyqk13 != null" >
					CYQK13,
				</if>
				<if test="item.cyqk14 != null" >
					CYQK14,
				</if>
				<if test="item.cyqk15 != null" >
					CYQK15,
				</if>
				<if test="item.sz != null" >
					SZ,
				</if>
				<if test="item.szqxz != null" >
					SZQXZ,
				</if>
				<if test="item.szqxy != null" >
					SZQXY,
				</if>
				<if test="item.szqxn != null" >
					SZQXN,
				</if>
				<if test="item.yykjyw != null" >
					YYKJYW,
				</if>
				<if test="item.sycxsj != null" >
					SYCXSJ,
				</if>
				<if test="item.lhyy != null" >
					LHYY,
				</if>
				<if test="item.lcljgl != null" >
					LCLJGL,
				</if>
				<if test="item.wclclj != null" >
					WCLCLJ,
				</if>
				<if test="item.tcyy != null" >
					TCYY,
				</if>
				<if test="item.sfby != null" >
					SFBY,
				</if>
				<if test="item.byyy != null" >
					BYYY,
				</if>
				<if test="item.tnmfq != null" >
					TNMFQ,
				</if>
				<if test="item.tzbzry != null" >
					TZBZRY,
				</if>
				<if test="item.cyrqjgts != null" >
					CYRQJGTS,
				</if>
				<if test="item.qtzf != null" >
					QTZF,
				</if>
				<if test="item.dwfzr != null" >
					DWFZR,
				</if>
				<if test="item.tjfzr != null" >
					TJFZR,
				</if>
				<if test="item.tbr != null" >
					TBR,
				</if>
				<if test="item.lxdh != null" >
					LXDH,
				</if>
				<if test="item.sj != null" >
					SJ,
				</if>
				<if test="item.bcrq != null" >
					BCRQ,
				</if>
				<if test="item.xzzXzqh != null" >
					XZZ_XZQH,
				</if>
				<if test="item.hkdzXzqh != null" >
					HKDZ_XZQH,
				</if>
				<if test="item.dwdzXzqh != null" >
					DWDZ_XZQH,
				</if>
				<if test="item.dzXzqh != null" >
					DZ_XZQH,
				</if>
				<if test="item.tjfzrDh != null" >
					TJFZR_DH,
				</if>
				<if test="item.zznys != null" >
					ZZNYS,
				</if>
				<if test="item.qtjgzr != null" >
					QTJGZR,
				</if>
				<if test="item.bzyzsnlr != null">
					BZYZSNLR,
				</if>
				<if test="item.rysjf != null">
					RYSJF,
				</if>
				<if test="item.cysjf != null">
					CYSJF,
				</if>
				<if test="item.hxb != null">
					HXB,
				</if>
				<if test="item.xxb != null">
					XXB,
				</if>
				<if test="item.xj != null">
					XJ,
				</if>
				<if test="item.qx != null">
					QX,
				</if>
				<if test="item.ztxhs != null">
					ZTXHS,
				</if>
				<if test="item.bdb != null">
					BDB,
				</if>
				<if test="item.lcd != null">
					LCD,
				</if>
				<if test="item.qt != null">
					QT,
				</if>
				<if test="item.sxfy != null">
					SXFY,
				</if>
				<if test="item.sf1 != null">
					SF1,
				</if>
				<if test="item.sf2 != null">
					SF2,
				</if>
				<if test="item.sf3 != null">
					SF3,
				</if>
				<if test="item.sf4 != null">
					SF4,
				</if>
				<if test="item.sf5 != null">
					SF5,
				</if>
				<if test="item.sf6 != null">
					SF6,
				</if>
				<if test="item.sf7 != null">
					SF7,
				</if>
				<if test="item.ct != null">
					CT,
				</if>
				<if test="item.petct != null">
					PETCT,
				</if>
				<if test="item.syct != null">
					SYCT,
				</if>
				<if test="item.bc != null">
					BC,
				</if>
				<if test="item.xp != null">
					XP,
				</if>
				<if test="item.csxdt != null">
					CSXDT,
				</if>
				<if test="item.mri != null">
					MRI,
				</if>
				<if test="item.twsjc != null">
					TWSJC,
				</if>
				<if test="item.yljgbm != null" >
					YLJGBM,
				</if>
			</trim>
			<trim prefix="values (" suffix=")" suffixOverrides="," >
				<if test="item.username != null" >
					#{item.username,jdbcType=VARCHAR},
				</if>
				<if test="item.ylfkfs != null" >
					#{item.ylfkfs,jdbcType=VARCHAR},
				</if>
				<if test="item.jkkh != null" >
					#{item.jkkh,jdbcType=VARCHAR},
				</if>
				<if test="item.zycs != null" >
					#{item.zycs,jdbcType=VARCHAR},
				</if>
				<if test="item.bah != null" >
					#{item.bah,jdbcType=VARCHAR},
				</if>
				<if test="item.xm != null" >
					#{item.xm,jdbcType=VARCHAR},
				</if>
				<if test="item.xb != null" >
					#{item.xb,jdbcType=VARCHAR},
				</if>
				<if test="item.csrq != null" >
					#{item.csrq,jdbcType=VARCHAR},
				</if>
				<if test="item.nl != null" >
					#{item.nl,jdbcType=DECIMAL},
				</if>
				<if test="item.gj != null" >
					#{item.gj,jdbcType=VARCHAR},
				</if>
				<if test="item.bzyzsnl != null" >
					#{item.bzyzsnl,jdbcType=DECIMAL},
				</if>
				<if test="item.xsecstz != null" >
					#{item.xsecstz,jdbcType=DECIMAL},
				</if>
				<if test="item.xserytz != null" >
					#{item.xserytz,jdbcType=DECIMAL},
				</if>
				<if test="item.csd != null" >
					#{item.csd,jdbcType=VARCHAR},
				</if>
				<if test="item.gg != null" >
					#{item.gg,jdbcType=VARCHAR},
				</if>
				<if test="item.mz != null" >
					#{item.mz,jdbcType=VARCHAR},
				</if>
				<if test="item.sfzh != null" >
					#{item.sfzh,jdbcType=VARCHAR},
				</if>
				<if test="item.zy != null" >
					#{item.zy,jdbcType=VARCHAR},
				</if>
				<if test="item.hy != null" >
					#{item.hy,jdbcType=VARCHAR},
				</if>
				<if test="item.xzz != null" >
					#{item.xzz,jdbcType=VARCHAR},
				</if>
				<if test="item.dh != null" >
					#{item.dh,jdbcType=VARCHAR},
				</if>
				<if test="item.yb1 != null" >
					#{item.yb1,jdbcType=VARCHAR},
				</if>
				<if test="item.hkdz != null" >
					#{item.hkdz,jdbcType=VARCHAR},
				</if>
				<if test="item.yb2 != null" >
					#{item.yb2,jdbcType=VARCHAR},
				</if>
				<if test="item.gzdwjdz != null" >
					#{item.gzdwjdz,jdbcType=VARCHAR},
				</if>
				<if test="item.dwdh != null" >
					#{item.dwdh,jdbcType=VARCHAR},
				</if>
				<if test="item.yb3 != null" >
					#{item.yb3,jdbcType=VARCHAR},
				</if>
				<if test="item.lxrxm != null" >
					#{item.lxrxm,jdbcType=VARCHAR},
				</if>
				<if test="item.gx != null" >
					#{item.gx,jdbcType=VARCHAR},
				</if>
				<if test="item.dz != null" >
					#{item.dz,jdbcType=VARCHAR},
				</if>
				<if test="item.dh2 != null" >
					#{item.dh2,jdbcType=VARCHAR},
				</if>
				<if test="item.rytj != null" >
					#{item.rytj,jdbcType=VARCHAR},
				</if>
				<if test="item.rysj != null" >
					#{item.rysj,jdbcType=VARCHAR},
				</if>
				<if test="item.rysjs != null" >
					#{item.rysjs,jdbcType=DECIMAL},
				</if>
				<if test="item.rykb != null" >
					#{item.rykb,jdbcType=VARCHAR},
				</if>
				<if test="item.rybf != null" >
					#{item.rybf,jdbcType=VARCHAR},
				</if>
				<if test="item.zkkb != null" >
					#{item.zkkb,jdbcType=VARCHAR},
				</if>
				<if test="item.cysj != null" >
					#{item.cysj,jdbcType=VARCHAR},
				</if>
				<if test="item.cysjs != null" >
					#{item.cysjs,jdbcType=DECIMAL},
				</if>
				<if test="item.cykb != null" >
					#{item.cykb,jdbcType=VARCHAR},
				</if>
				<if test="item.cybf != null" >
					#{item.cybf,jdbcType=VARCHAR},
				</if>
				<if test="item.sjzyts != null" >
					#{item.sjzyts,jdbcType=VARCHAR},
				</if>
				<if test="item.mzzd != null" >
					#{item.mzzd,jdbcType=VARCHAR},
				</if>
				<if test="item.jbbm != null" >
					#{item.jbbm,jdbcType=VARCHAR},
				</if>
				<if test="item.zyzd != null" >
					#{item.zyzd,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm != null" >
					#{item.jbdm,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq != null" >
					#{item.rybq,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd8 != null" >
					#{item.qtzd8,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm8 != null" >
					#{item.jbdm8,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq8 != null" >
					#{item.rybq8,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd1 != null" >
					#{item.qtzd1,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm1 != null" >
					#{item.jbdm1,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq1 != null" >
					#{item.rybq1,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd9 != null" >
					#{item.qtzd9,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm9 != null" >
					#{item.jbdm9,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq9 != null" >
					#{item.rybq9,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd2 != null" >
					#{item.qtzd2,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm2 != null" >
					#{item.jbdm2,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq2 != null" >
					#{item.rybq2,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd10 != null" >
					#{item.qtzd10,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm10 != null" >
					#{item.jbdm10,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq10 != null" >
					#{item.rybq10,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd3 != null" >
					#{item.qtzd3,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm3 != null" >
					#{item.jbdm3,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq3 != null" >
					#{item.rybq3,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd11 != null" >
					#{item.qtzd11,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm11 != null" >
					#{item.jbdm11,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq11 != null" >
					#{item.rybq11,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd4 != null" >
					#{item.qtzd4,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm4 != null" >
					#{item.jbdm4,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq4 != null" >
					#{item.rybq4,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd12 != null" >
					#{item.qtzd12,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm12 != null" >
					#{item.jbdm12,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq12 != null" >
					#{item.rybq12,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd5 != null" >
					#{item.qtzd5,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm5 != null" >
					#{item.jbdm5,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq5 != null" >
					#{item.rybq5,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd13 != null" >
					#{item.qtzd13,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm13 != null" >
					#{item.jbdm13,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq13 != null" >
					#{item.rybq13,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd6 != null" >
					#{item.qtzd6,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm6 != null" >
					#{item.jbdm6,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq6 != null" >
					#{item.rybq6,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd14 != null" >
					#{item.qtzd14,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm14 != null" >
					#{item.jbdm14,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq14 != null" >
					#{item.rybq14,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd7 != null" >
					#{item.qtzd7,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm7 != null" >
					#{item.jbdm7,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq7 != null" >
					#{item.rybq7,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzd15 != null" >
					#{item.qtzd15,jdbcType=VARCHAR},
				</if>
				<if test="item.jbdm15 != null" >
					#{item.jbdm15,jdbcType=VARCHAR},
				</if>
				<if test="item.rybq15 != null" >
					#{item.rybq15,jdbcType=VARCHAR},
				</if>
				<if test="item.wbyy != null" >
					#{item.wbyy,jdbcType=VARCHAR},
				</if>
				<if test="item.h23 != null" >
					#{item.h23,jdbcType=VARCHAR},
				</if>
				<if test="item.blzd != null" >
					#{item.blzd,jdbcType=VARCHAR},
				</if>
				<if test="item.jbmm != null" >
					#{item.jbmm,jdbcType=VARCHAR},
				</if>
				<if test="item.blh != null" >
					#{item.blh,jdbcType=VARCHAR},
				</if>
				<if test="item.ywgm != null" >
					#{item.ywgm,jdbcType=VARCHAR},
				</if>
				<if test="item.gmyw != null" >
					#{item.gmyw,jdbcType=VARCHAR},
				</if>
				<if test="item.swhzsj != null" >
					#{item.swhzsj,jdbcType=VARCHAR},
				</if>
				<if test="item.xx != null" >
					#{item.xx,jdbcType=VARCHAR},
				</if>
				<if test="item.rh != null" >
					#{item.rh,jdbcType=VARCHAR},
				</if>
				<if test="item.kzr != null" >
					#{item.kzr,jdbcType=VARCHAR},
				</if>
				<if test="item.zrys != null" >
					#{item.zrys,jdbcType=VARCHAR},
				</if>
				<if test="item.zzys != null" >
					#{item.zzys,jdbcType=VARCHAR},
				</if>
				<if test="item.zyys != null" >
					#{item.zyys,jdbcType=VARCHAR},
				</if>
				<if test="item.zrhs != null" >
					#{item.zrhs,jdbcType=VARCHAR},
				</if>
				<if test="item.jxys != null" >
					#{item.jxys,jdbcType=VARCHAR},
				</if>
				<if test="item.sxys != null" >
					#{item.sxys,jdbcType=VARCHAR},
				</if>
				<if test="item.bmy != null" >
					#{item.bmy,jdbcType=VARCHAR},
				</if>
				<if test="item.bazl != null" >
					#{item.bazl,jdbcType=VARCHAR},
				</if>
				<if test="item.zkys != null" >
					#{item.zkys,jdbcType=VARCHAR},
				</if>
				<if test="item.zkhs != null" >
					#{item.zkhs,jdbcType=VARCHAR},
				</if>
				<if test="item.zkrq != null" >
					#{item.zkrq,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczbm1 != null" >
					#{item.ssjczbm1,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczrq1 != null" >
					#{item.ssjczrq1,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjb1 != null" >
					#{item.ssjb1,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczmc1 != null" >
					#{item.ssjczmc1,jdbcType=VARCHAR},
				</if>
				<if test="item.sz1 != null" >
					#{item.sz1,jdbcType=VARCHAR},
				</if>
				<if test="item.yz1 != null" >
					#{item.yz1,jdbcType=VARCHAR},
				</if>
				<if test="item.ez1 != null" >
					#{item.ez1,jdbcType=VARCHAR},
				</if>
				<if test="item.qkdj1 != null" >
					#{item.qkdj1,jdbcType=VARCHAR},
				</if>
				<if test="item.qkyhlb1 != null" >
					#{item.qkyhlb1,jdbcType=VARCHAR},
				</if>
				<if test="item.mzfs1 != null" >
					#{item.mzfs1,jdbcType=VARCHAR},
				</if>
				<if test="item.mzys1 != null" >
					#{item.mzys1,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczbm2 != null" >
					#{item.ssjczbm2,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczrq2 != null" >
					#{item.ssjczrq2,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjb2 != null" >
					#{item.ssjb2,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczmc2 != null" >
					#{item.ssjczmc2,jdbcType=VARCHAR},
				</if>
				<if test="item.sz2 != null" >
					#{item.sz2,jdbcType=VARCHAR},
				</if>
				<if test="item.yz2 != null" >
					#{item.yz2,jdbcType=VARCHAR},
				</if>
				<if test="item.ez2 != null" >
					#{item.ez2,jdbcType=VARCHAR},
				</if>
				<if test="item.qkdj2 != null" >
					#{item.qkdj2,jdbcType=VARCHAR},
				</if>
				<if test="item.qkyhlb2 != null" >
					#{item.qkyhlb2,jdbcType=VARCHAR},
				</if>
				<if test="item.mzfs2 != null" >
					#{item.mzfs2,jdbcType=VARCHAR},
				</if>
				<if test="item.mzys2 != null" >
					#{item.mzys2,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczbm3 != null" >
					#{item.ssjczbm3,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczrq3 != null" >
					#{item.ssjczrq3,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjb3 != null" >
					#{item.ssjb3,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczmc3 != null" >
					#{item.ssjczmc3,jdbcType=VARCHAR},
				</if>
				<if test="item.sz3 != null" >
					#{item.sz3,jdbcType=VARCHAR},
				</if>
				<if test="item.yz3 != null" >
					#{item.yz3,jdbcType=VARCHAR},
				</if>
				<if test="item.ez3 != null" >
					#{item.ez3,jdbcType=VARCHAR},
				</if>
				<if test="item.qkdj3 != null" >
					#{item.qkdj3,jdbcType=VARCHAR},
				</if>
				<if test="item.qkyhlb3 != null" >
					#{item.qkyhlb3,jdbcType=VARCHAR},
				</if>
				<if test="item.mzfs3 != null" >
					#{item.mzfs3,jdbcType=VARCHAR},
				</if>
				<if test="item.mzys3 != null" >
					#{item.mzys3,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczbm4 != null" >
					#{item.ssjczbm4,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczrq4 != null" >
					#{item.ssjczrq4,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjb4 != null" >
					#{item.ssjb4,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczmc4 != null" >
					#{item.ssjczmc4,jdbcType=VARCHAR},
				</if>
				<if test="item.sz4 != null" >
					#{item.sz4,jdbcType=VARCHAR},
				</if>
				<if test="item.yz4 != null" >
					#{item.yz4,jdbcType=VARCHAR},
				</if>
				<if test="item.ez4 != null" >
					#{item.ez4,jdbcType=VARCHAR},
				</if>
				<if test="item.qkdj4 != null" >
					#{item.qkdj4,jdbcType=VARCHAR},
				</if>
				<if test="item.qkyhlb4 != null" >
					#{item.qkyhlb4,jdbcType=VARCHAR},
				</if>
				<if test="item.mzfs4 != null" >
					#{item.mzfs4,jdbcType=VARCHAR},
				</if>
				<if test="item.mzys4 != null" >
					#{item.mzys4,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczbm5 != null" >
					#{item.ssjczbm5,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczrq5 != null" >
					#{item.ssjczrq5,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjb5 != null" >
					#{item.ssjb5,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczmc5 != null" >
					#{item.ssjczmc5,jdbcType=VARCHAR},
				</if>
				<if test="item.sz5 != null" >
					#{item.sz5,jdbcType=VARCHAR},
				</if>
				<if test="item.yz5 != null" >
					#{item.yz5,jdbcType=VARCHAR},
				</if>
				<if test="item.ez5 != null" >
					#{item.ez5,jdbcType=VARCHAR},
				</if>
				<if test="item.qkdj5 != null" >
					#{item.qkdj5,jdbcType=VARCHAR},
				</if>
				<if test="item.qkyhlb5 != null" >
					#{item.qkyhlb5,jdbcType=VARCHAR},
				</if>
				<if test="item.mzfs5 != null" >
					#{item.mzfs5,jdbcType=VARCHAR},
				</if>
				<if test="item.mzys5 != null" >
					#{item.mzys5,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczbm6 != null" >
					#{item.ssjczbm6,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczrq6 != null" >
					#{item.ssjczrq6,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjb6 != null" >
					#{item.ssjb6,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczmc6 != null" >
					#{item.ssjczmc6,jdbcType=VARCHAR},
				</if>
				<if test="item.sz6 != null" >
					#{item.sz6,jdbcType=VARCHAR},
				</if>
				<if test="item.yz6 != null" >
					#{item.yz6,jdbcType=VARCHAR},
				</if>
				<if test="item.ez6 != null" >
					#{item.ez6,jdbcType=VARCHAR},
				</if>
				<if test="item.qkdj6 != null" >
					#{item.qkdj6,jdbcType=VARCHAR},
				</if>
				<if test="item.qkyhlb6 != null" >
					#{item.qkyhlb6,jdbcType=VARCHAR},
				</if>
				<if test="item.mzfs6 != null" >
					#{item.mzfs6,jdbcType=VARCHAR},
				</if>
				<if test="item.mzys6 != null" >
					#{item.mzys6,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczbm7 != null" >
					#{item.ssjczbm7,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczrq7 != null" >
					#{item.ssjczrq7,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjb7 != null" >
					#{item.ssjb7,jdbcType=VARCHAR},
				</if>
				<if test="item.ssjczmc7 != null" >
					#{item.ssjczmc7,jdbcType=VARCHAR},
				</if>
				<if test="item.sz7 != null" >
					#{item.sz7,jdbcType=VARCHAR},
				</if>
				<if test="item.yz7 != null" >
					#{item.yz7,jdbcType=VARCHAR},
				</if>
				<if test="item.ez7 != null" >
					#{item.ez7,jdbcType=VARCHAR},
				</if>
				<if test="item.qkdj7 != null" >
					#{item.qkdj7,jdbcType=VARCHAR},
				</if>
				<if test="item.qkyhlb7 != null" >
					#{item.qkyhlb7,jdbcType=VARCHAR},
				</if>
				<if test="item.mzfs7 != null" >
					#{item.mzfs7,jdbcType=VARCHAR},
				</if>
				<if test="item.mzys7 != null" >
					#{item.mzys7,jdbcType=VARCHAR},
				</if>
				<if test="item.lyfs != null" >
					#{item.lyfs,jdbcType=VARCHAR},
				</if>
				<if test="item.yzzyYljg != null" >
					#{item.yzzyYljg,jdbcType=VARCHAR},
				</if>
				<if test="item.wsyYljg != null" >
					#{item.wsyYljg,jdbcType=VARCHAR},
				</if>
				<if test="item.sfzzyjh != null" >
					#{item.sfzzyjh,jdbcType=VARCHAR},
				</if>
				<if test="item.md != null" >
					#{item.md,jdbcType=VARCHAR},
				</if>
				<if test="item.ryqT != null" >
					#{item.ryqT,jdbcType=DECIMAL},
				</if>
				<if test="item.ryqXs != null" >
					#{item.ryqXs,jdbcType=DECIMAL},
				</if>
				<if test="item.ryqF != null" >
					#{item.ryqF,jdbcType=DECIMAL},
				</if>
				<if test="item.ryhT != null" >
					#{item.ryhT,jdbcType=DECIMAL},
				</if>
				<if test="item.ryhXs != null" >
					#{item.ryhXs,jdbcType=DECIMAL},
				</if>
				<if test="item.ryhF != null" >
					#{item.ryhF,jdbcType=DECIMAL},
				</if>
				<if test="item.zfy != null" >
					#{item.zfy,jdbcType=DECIMAL},
				</if>
				<if test="item.zfje != null" >
					#{item.zfje,jdbcType=DECIMAL},
				</if>
				<if test="item.ylfuf != null" >
					#{item.ylfuf,jdbcType=DECIMAL},
				</if>
				<if test="item.zlczf != null" >
					#{item.zlczf,jdbcType=DECIMAL},
				</if>
				<if test="item.hlf != null" >
					#{item.hlf,jdbcType=DECIMAL},
				</if>
				<if test="item.qtfy != null" >
					#{item.qtfy,jdbcType=DECIMAL},
				</if>
				<if test="item.blzdf != null" >
					#{item.blzdf,jdbcType=DECIMAL},
				</if>
				<if test="item.syszdf != null" >
					#{item.syszdf,jdbcType=DECIMAL},
				</if>
				<if test="item.yxxzdf != null" >
					#{item.yxxzdf,jdbcType=DECIMAL},
				</if>
				<if test="item.lczdxmf != null" >
					#{item.lczdxmf,jdbcType=DECIMAL},
				</if>
				<if test="item.fsszlxmf != null" >
					#{item.fsszlxmf,jdbcType=DECIMAL},
				</if>
				<if test="item.wlzlf != null" >
					#{item.wlzlf,jdbcType=DECIMAL},
				</if>
				<if test="item.sszlf != null" >
					#{item.sszlf,jdbcType=DECIMAL},
				</if>
				<if test="item.maf != null" >
					#{item.maf,jdbcType=DECIMAL},
				</if>
				<if test="item.ssf != null" >
					#{item.ssf,jdbcType=DECIMAL},
				</if>
				<if test="item.kff != null" >
					#{item.kff,jdbcType=DECIMAL},
				</if>
				<if test="item.zyzlf != null" >
					#{item.zyzlf,jdbcType=DECIMAL},
				</if>
				<if test="item.xyf != null" >
					#{item.xyf,jdbcType=DECIMAL},
				</if>
				<if test="item.kjywf != null" >
					#{item.kjywf,jdbcType=DECIMAL},
				</if>
				<if test="item.zcyf != null" >
					#{item.zcyf,jdbcType=DECIMAL},
				</if>
				<if test="item.zcyf1 != null" >
					#{item.zcyf1,jdbcType=DECIMAL},
				</if>
				<if test="item.xf != null" >
					#{item.xf,jdbcType=DECIMAL},
				</if>
				<if test="item.bdblzpf != null" >
					#{item.bdblzpf,jdbcType=DECIMAL},
				</if>
				<if test="item.qdblzpf != null" >
					#{item.qdblzpf,jdbcType=DECIMAL},
				</if>
				<if test="item.nxyzlzpf != null" >
					#{item.nxyzlzpf,jdbcType=DECIMAL},
				</if>
				<if test="item.xbyzlzpf != null" >
					#{item.xbyzlzpf,jdbcType=DECIMAL},
				</if>
				<if test="item.hcyyclf != null" >
					#{item.hcyyclf,jdbcType=DECIMAL},
				</if>
				<if test="item.yyclf != null" >
					#{item.yyclf,jdbcType=DECIMAL},
				</if>
				<if test="item.ycxyyclf != null" >
					#{item.ycxyyclf,jdbcType=DECIMAL},
				</if>
				<if test="item.qtf != null" >
					#{item.qtf,jdbcType=DECIMAL},
				</if>
				<if test="item.ryqk != null" >
					#{item.ryqk,jdbcType=VARCHAR},
				</if>
				<if test="item.ryzd != null" >
					#{item.ryzd,jdbcType=VARCHAR},
				</if>
				<if test="item.zyzdqzrq != null" >
					#{item.zyzdqzrq,jdbcType=VARCHAR},
				</if>
				<if test="item.jbbm1 != null" >
					#{item.jbbm1,jdbcType=VARCHAR},
				</if>
				<if test="item.bwhbz != null" >
					#{item.bwhbz,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk != null" >
					#{item.cyqk,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk1 != null" >
					#{item.cyqk1,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk2 != null" >
					#{item.cyqk2,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk3 != null" >
					#{item.cyqk3,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk4 != null" >
					#{item.cyqk4,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk5 != null" >
					#{item.cyqk5,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk6 != null" >
					#{item.cyqk6,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk7 != null" >
					#{item.cyqk7,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk8 != null" >
					#{item.cyqk8,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk9 != null" >
					#{item.cyqk9,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk10 != null" >
					#{item.cyqk10,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk11 != null" >
					#{item.cyqk11,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk12 != null" >
					#{item.cyqk12,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk13 != null" >
					#{item.cyqk13,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk14 != null" >
					#{item.cyqk14,jdbcType=VARCHAR},
				</if>
				<if test="item.cyqk15 != null" >
					#{item.cyqk15,jdbcType=VARCHAR},
				</if>
				<if test="item.sz != null" >
					#{item.sz,jdbcType=VARCHAR},
				</if>
				<if test="item.szqxz != null" >
					#{item.szqxz,jdbcType=VARCHAR},
				</if>
				<if test="item.szqxy != null" >
					#{item.szqxy,jdbcType=VARCHAR},
				</if>
				<if test="item.szqxn != null" >
					#{item.szqxn,jdbcType=VARCHAR},
				</if>
				<if test="item.yykjyw != null" >
					#{item.yykjyw,jdbcType=VARCHAR},
				</if>
				<if test="item.sycxsj != null" >
					#{item.sycxsj,jdbcType=VARCHAR},
				</if>
				<if test="item.lhyy != null" >
					#{item.lhyy,jdbcType=VARCHAR},
				</if>
				<if test="item.lcljgl != null" >
					#{item.lcljgl,jdbcType=VARCHAR},
				</if>
				<if test="item.wclclj != null" >
					#{item.wclclj,jdbcType=VARCHAR},
				</if>
				<if test="item.tcyy != null" >
					#{item.tcyy,jdbcType=VARCHAR},
				</if>
				<if test="item.sfby != null" >
					#{item.sfby,jdbcType=VARCHAR},
				</if>
				<if test="item.byyy != null" >
					#{item.byyy,jdbcType=VARCHAR},
				</if>
				<if test="item.tnmfq != null" >
					#{item.tnmfq,jdbcType=VARCHAR},
				</if>
				<if test="item.tzbzry != null" >
					#{item.tzbzry,jdbcType=VARCHAR},
				</if>
				<if test="item.cyrqjgts != null" >
					#{item.cyrqjgts,jdbcType=VARCHAR},
				</if>
				<if test="item.qtzf != null" >
					#{item.qtzf,jdbcType=VARCHAR},
				</if>
				<if test="item.dwfzr != null" >
					#{item.dwfzr,jdbcType=VARCHAR},
				</if>
				<if test="item.tjfzr != null" >
					#{item.tjfzr,jdbcType=VARCHAR},
				</if>
				<if test="item.tbr != null" >
					#{item.tbr,jdbcType=VARCHAR},
				</if>
				<if test="item.lxdh != null" >
					#{item.lxdh,jdbcType=VARCHAR},
				</if>
				<if test="item.sj != null" >
					#{item.sj,jdbcType=VARCHAR},
				</if>
				<if test="item.bcrq != null" >
					#{item.bcrq,jdbcType=VARCHAR},
				</if>
				<if test="item.xzzXzqh != null" >
					#{item.xzzXzqh,jdbcType=VARCHAR},
				</if>
				<if test="item.hkdzXzqh != null" >
					#{item.hkdzXzqh,jdbcType=VARCHAR},
				</if>
				<if test="item.dwdzXzqh != null" >
					#{item.dwdzXzqh,jdbcType=VARCHAR},
				</if>
				<if test="item.dzXzqh != null" >
					#{item.dzXzqh,jdbcType=VARCHAR},
				</if>
				<if test="item.tjfzrDh != null" >
					#{item.tjfzrDh,jdbcType=VARCHAR},
				</if>
				<if test="item.zznys != null" >
					#{item.zznys,jdbcType=VARCHAR},
				</if>
				<if test="item.qtjgzr != null" >
					#{item.qtjgzr,jdbcType=VARCHAR},
				</if>
				<if test="item.bzyzsnlr != null">
					#{item.bzyzsnlr},
				</if>
				<if test="item.rysjf != null">
					#{item.rysjf},
				</if>
				<if test="item.cysjf != null">
					#{item.cysjf},
				</if>
				<if test="item.hxb != null">
					#{item.hxb},
				</if>
				<if test="item.xxb != null">
					#{item.xxb},
				</if>
				<if test="item.xj != null">
					#{item.xj},
				</if>
				<if test="item.qx != null">
					#{item.qx},
				</if>
				<if test="item.ztxhs != null">
					#{item.ztxhs},
				</if>
				<if test="item.bdb != null">
					#{item.bdb},
				</if>
				<if test="item.lcd != null">
					#{item.lcd},
				</if>
				<if test="item.qt != null">
					#{item.qt},
				</if>
				<if test="item.sxfy != null">
					#{item.sxfy},
				</if>
				<if test="item.sf1 != null">
					#{item.sf1},
				</if>
				<if test="item.sf2 != null">
					#{item.sf2},
				</if>
				<if test="item.sf3 != null">
					#{item.sf3},
				</if>
				<if test="item.sf4 != null">
					#{item.sf4},
				</if>
				<if test="item.sf5 != null">
					#{item.sf5},
				</if>
				<if test="item.sf6 != null">
					#{item.sf6},
				</if>
				<if test="item.sf7 != null">
					#{item.sf7},
				</if>
				<if test="item.ct != null">
					#{item.ct},
				</if>
				<if test="item.petct != null">
					#{item.petct},
				</if>
				<if test="item.syct != null">
					#{item.syct},
				</if>
				<if test="item.bc != null">
					#{item.bc},
				</if>
				<if test="item.xp != null">
					#{item.xp},
				</if>
				<if test="item.csxdt != null">
					#{item.csxdt},
				</if>
				<if test="item.mri != null">
					#{item.mri},
				</if>
				<if test="item.twsjc != null">
					#{item.twsjc},
				</if>
				<if test="item.yljgbm != null" >
					#{item.yljgbm,jdbcType=VARCHAR},
				</if>
			</trim>

		</foreach>
	</insert>

	<update id="updateDysj">
		update Bagl_Basy_Jbxx set dysj=sysdate
		where zyh=#{zyh} and yljgbm=#{yljgbm}
	</update>
	<select id="selectDysj" resultType="java.util.Date">
		select dysj from Bagl_Basy_Jbxx
		where zyh=#{zyh} and yljgbm=#{yljgbm}
	</select>
</mapper>
